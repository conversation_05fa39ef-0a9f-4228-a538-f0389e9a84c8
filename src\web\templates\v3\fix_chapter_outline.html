{% extends "v3/base.html" %}

{% block title %}修复章纲分析推理过程 - 九猫小说分析写作系统v3.0{% endblock %}

{% block extra_css %}
<style>
    .fix-container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
    .fix-card { margin-bottom: 2rem; }
    .fix-header { background-color: #f8f9fa; padding: 1rem; border-bottom: 1px solid #dee2e6; }
    .fix-content { padding: 1.5rem; }
    .fix-footer { background-color: #f8f9fa; padding: 1rem; border-top: 1px solid #dee2e6; }
    .fix-title { font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; }
    .fix-subtitle { font-size: 1.2rem; font-weight: 500; margin-bottom: 0.5rem; }
    .fix-description { margin-bottom: 1rem; }
    .fix-log { background-color: #1e1e1e; color: #f0f0f0; font-family: 'Consolas', 'Courier New', monospace; padding: 1rem; border-radius: 0.25rem; height: 300px; overflow-y: auto; }
    .fix-log-line { margin-bottom: 0.25rem; line-height: 1.5; }
    .fix-log-timestamp { color: #569cd6; margin-right: 0.5rem; }
    .fix-log-info { color: #f0f0f0; }
    .fix-log-warn { color: #dcdcaa; }
    .fix-log-error { color: #f14c4c; }
    .fix-log-success { color: #6a9955; }
    .novel-select { margin-bottom: 1rem; }
    .chapter-select { margin-bottom: 1rem; }
</style>
{% endblock %}

{% block content %}
<div class="fix-container">
    <div class="card fix-card">
        <div class="card-header fix-header">
            <h1 class="fix-title">修复章纲分析推理过程</h1>
            <p class="fix-description">此工具用于修复章节分析中章纲分析维度的推理过程内容。</p>
        </div>
        <div class="card-body fix-content">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group novel-select">
                        <label for="novelSelect">选择小说</label>
                        <select class="form-select" id="novelSelect">
                            <option value="">-- 请选择小说 --</option>
                            {% for novel in novels %}
                                <option value="{{ novel.id }}">{{ novel.title }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group chapter-select">
                        <label for="chapterSelect">选择章节</label>
                        <select class="form-select" id="chapterSelect" disabled>
                            <option value="">-- 请先选择小说 --</option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button id="checkStatusBtn" class="btn btn-primary" disabled>
                            <i class="fas fa-search me-2"></i>检查状态
                        </button>
                        <button id="fixReasoningBtn" class="btn btn-warning" disabled>
                            <i class="fas fa-tools me-2"></i>修复推理过程
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">修复日志</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="fix-log" id="fixLog">
                                <div class="fix-log-line fix-log-info">
                                    <span class="fix-log-timestamp">[{{ now.strftime('%H:%M:%S') }}]</span> 欢迎使用章纲分析推理过程修复工具。
                                </div>
                                <div class="fix-log-line fix-log-info">
                                    <span class="fix-log-timestamp">[{{ now.strftime('%H:%M:%S') }}]</span> 请选择小说和章节，然后点击"检查状态"按钮。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">修复结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="fixResult">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    请先选择小说和章节，然后点击"检查状态"按钮查看当前状态。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer fix-footer">
            <div class="d-flex justify-content-between align-items-center">
                <a href="{{ url_for('v3.console_page') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回控制台
                </a>
                <button id="refreshBtn" class="btn btn-outline-primary">
                    <i class="fas fa-sync-alt me-2"></i>刷新页面
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 添加日志
        function addLog(message, level = 'info') {
            const timestamp = new Date().toTimeString().split(' ')[0];
            const logLine = $('<div class="fix-log-line"></div>').addClass(`fix-log-${level}`);
            logLine.html(`<span class="fix-log-timestamp">[${timestamp}]</span> ${message}`);
            $('#fixLog').append(logLine);
            $('#fixLog').scrollTop($('#fixLog')[0].scrollHeight);
        }
        
        // 小说选择变化事件
        $('#novelSelect').change(function() {
            const novelId = $(this).val();
            
            // 重置章节选择
            $('#chapterSelect').empty().append('<option value="">-- 请选择章节 --</option>').prop('disabled', true);
            $('#checkStatusBtn').prop('disabled', true);
            $('#fixReasoningBtn').prop('disabled', true);
            
            if (novelId) {
                addLog(`已选择小说ID: ${novelId}，正在加载章节列表...`);
                
                // 加载章节列表
                $.ajax({
                    url: `/api/novel/${novelId}/chapters`,
                    type: 'GET',
                    success: function(response) {
                        if (response.success && response.chapters) {
                            // 填充章节选择器
                            response.chapters.forEach(function(chapter) {
                                $('#chapterSelect').append(`<option value="${chapter.id}">${chapter.title || '第' + chapter.chapter_number + '章'}</option>`);
                            });
                            
                            $('#chapterSelect').prop('disabled', false);
                            addLog(`成功加载${response.chapters.length}个章节`, 'success');
                        } else {
                            addLog(`加载章节列表失败: ${response.error || '未知错误'}`, 'error');
                        }
                    },
                    error: function(xhr) {
                        addLog(`加载章节列表请求失败: ${xhr.status} ${xhr.statusText}`, 'error');
                        
                        // 尝试备用API路径
                        $.ajax({
                            url: `/api/novels/${novelId}/chapters`,
                            type: 'GET',
                            success: function(backupResponse) {
                                if (backupResponse.success && backupResponse.chapters) {
                                    // 填充章节选择器
                                    backupResponse.chapters.forEach(function(chapter) {
                                        $('#chapterSelect').append(`<option value="${chapter.id}">${chapter.title || '第' + chapter.chapter_number + '章'}</option>`);
                                    });
                                    
                                    $('#chapterSelect').prop('disabled', false);
                                    addLog(`通过备用API路径成功加载${backupResponse.chapters.length}个章节`, 'success');
                                } else {
                                    addLog(`通过备用API路径加载章节列表也失败: ${backupResponse.error || '未知错误'}`, 'error');
                                }
                            },
                            error: function(backupXhr) {
                                addLog(`通过备用API路径加载章节列表请求也失败: ${backupXhr.status} ${backupXhr.statusText}`, 'error');
                            }
                        });
                    }
                });
            } else {
                addLog('请选择一个小说', 'warn');
            }
        });
        
        // 章节选择变化事件
        $('#chapterSelect').change(function() {
            const chapterId = $(this).val();
            
            if (chapterId) {
                addLog(`已选择章节ID: ${chapterId}`);
                $('#checkStatusBtn').prop('disabled', false);
            } else {
                $('#checkStatusBtn').prop('disabled', true);
                $('#fixReasoningBtn').prop('disabled', true);
            }
        });
        
        // 检查状态按钮点击事件
        $('#checkStatusBtn').click(function() {
            const novelId = $('#novelSelect').val();
            const chapterId = $('#chapterSelect').val();
            
            if (!novelId || !chapterId) {
                addLog('请先选择小说和章节', 'warn');
                return;
            }
            
            addLog(`正在检查章节ID ${chapterId} 的章纲分析推理过程状态...`);
            
            // 显示加载中
            $('#fixResult').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">正在检查状态...</p></div>');
            
            // 检查章纲分析状态
            $.ajax({
                url: `/api/novel/${novelId}/chapter/${chapterId}/analysis/chapter_outline/status`,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        addLog(`成功获取章纲分析状态: ${response.status}`, 'success');
                        
                        if (response.status === 'completed') {
                            // 检查推理过程内容
                            $.ajax({
                                url: `/api/novel/${novelId}/chapter/${chapterId}/analysis/chapter_outline/reasoning_content`,
                                type: 'GET',
                                success: function(reasoningResponse) {
                                    if (reasoningResponse.success) {
                                        if (reasoningResponse.reasoning_content) {
                                            addLog('章纲分析推理过程内容存在', 'success');
                                            $('#fixResult').html(`
                                                <div class="alert alert-success">
                                                    <i class="fas fa-check-circle me-2"></i>
                                                    <strong>状态正常:</strong> 章节ID ${chapterId} 的章纲分析推理过程内容已存在，无需修复。
                                                </div>
                                                <div class="card mt-3">
                                                    <div class="card-header">推理过程内容预览</div>
                                                    <div class="card-body">
                                                        <pre class="border p-3 bg-light" style="max-height: 300px; overflow-y: auto;">${reasoningResponse.reasoning_content}</pre>
                                                    </div>
                                                </div>
                                            `);
                                        } else {
                                            addLog('章纲分析推理过程内容为空，需要修复', 'warn');
                                            $('#fixResult').html(`
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    <strong>需要修复:</strong> 章节ID ${chapterId} 的章纲分析已完成，但推理过程内容为空。
                                                </div>
                                            `);
                                            $('#fixReasoningBtn').prop('disabled', false);
                                        }
                                    } else {
                                        addLog(`获取推理过程内容失败: ${reasoningResponse.error || '未知错误'}`, 'error');
                                        $('#fixResult').html(`
                                            <div class="alert alert-danger">
                                                <i class="fas fa-times-circle me-2"></i>
                                                <strong>检查失败:</strong> 无法获取章节ID ${chapterId} 的章纲分析推理过程内容。
                                                <p class="mb-0 mt-2">错误信息: ${reasoningResponse.error || '未知错误'}</p>
                                            </div>
                                        `);
                                        $('#fixReasoningBtn').prop('disabled', false);
                                    }
                                },
                                error: function(xhr) {
                                    addLog(`获取推理过程内容请求失败: ${xhr.status} ${xhr.statusText}`, 'error');
                                    $('#fixResult').html(`
                                        <div class="alert alert-danger">
                                            <i class="fas fa-times-circle me-2"></i>
                                            <strong>检查失败:</strong> 请求章节ID ${chapterId} 的章纲分析推理过程内容时出错。
                                            <p class="mb-0 mt-2">错误信息: ${xhr.status} ${xhr.statusText}</p>
                                        </div>
                                    `);
                                    $('#fixReasoningBtn').prop('disabled', false);
                                }
                            });
                        } else if (response.status === 'not_analyzed') {
                            addLog('章节尚未进行章纲分析', 'warn');
                            $('#fixResult').html(`
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>未分析:</strong> 章节ID ${chapterId} 尚未进行章纲分析，请先在控制台进行分析。
                                </div>
                            `);
                            $('#fixReasoningBtn').prop('disabled', true);
                        } else {
                            addLog(`章纲分析状态: ${response.status}`, 'info');
                            $('#fixResult').html(`
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>当前状态:</strong> 章节ID ${chapterId} 的章纲分析状态为 ${response.status}。
                                </div>
                            `);
                            $('#fixReasoningBtn').prop('disabled', false);
                        }
                    } else {
                        addLog(`获取章纲分析状态失败: ${response.error || '未知错误'}`, 'error');
                        $('#fixResult').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>检查失败:</strong> 无法获取章节ID ${chapterId} 的章纲分析状态。
                                <p class="mb-0 mt-2">错误信息: ${response.error || '未知错误'}</p>
                            </div>
                        `);
                    }
                },
                error: function(xhr) {
                    addLog(`获取章纲分析状态请求失败: ${xhr.status} ${xhr.statusText}`, 'error');
                    $('#fixResult').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>检查失败:</strong> 请求章节ID ${chapterId} 的章纲分析状态时出错。
                            <p class="mb-0 mt-2">错误信息: ${xhr.status} ${xhr.statusText}</p>
                        </div>
                    `);
                    $('#fixReasoningBtn').prop('disabled', false);
                }
            });
        });
        
        // 修复推理过程按钮点击事件
        $('#fixReasoningBtn').click(function() {
            const novelId = $('#novelSelect').val();
            const chapterId = $('#chapterSelect').val();
            
            if (!novelId || !chapterId) {
                addLog('请先选择小说和章节', 'warn');
                return;
            }
            
            if (!confirm('确定要修复章节ID ' + chapterId + ' 的章纲分析推理过程吗？')) {
                return;
            }
            
            addLog(`开始修复章节ID ${chapterId} 的章纲分析推理过程...`);
            
            // 显示修复中
            $('#fixResult').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">正在修复推理过程...</p></div>');
            $('#fixReasoningBtn').prop('disabled', true);
            
            // 发送修复请求
            $.ajax({
                url: `/api/fix_chapter_outline_reasoning`,
                type: 'POST',
                data: JSON.stringify({
                    novel_id: novelId,
                    chapter_id: chapterId
                }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.success) {
                        addLog(`修复成功: ${response.message}`, 'success');
                        $('#fixResult').html(`
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>修复成功:</strong> ${response.message}
                            </div>
                            <div class="card mt-3">
                                <div class="card-header">修复后的推理过程内容</div>
                                <div class="card-body">
                                    <pre class="border p-3 bg-light" style="max-height: 300px; overflow-y: auto;">${response.reasoning_content || '无内容'}</pre>
                                </div>
                            </div>
                        `);
                    } else {
                        addLog(`修复失败: ${response.error || '未知错误'}`, 'error');
                        $('#fixResult').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>修复失败:</strong> ${response.error || '未知错误'}
                            </div>
                        `);
                        $('#fixReasoningBtn').prop('disabled', false);
                    }
                },
                error: function(xhr) {
                    addLog(`修复请求失败: ${xhr.status} ${xhr.statusText}`, 'error');
                    $('#fixResult').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>修复请求失败:</strong> ${xhr.status} ${xhr.statusText}
                        </div>
                    `);
                    $('#fixReasoningBtn').prop('disabled', false);
                }
            });
        });
        
        // 刷新按钮点击事件
        $('#refreshBtn').click(function() {
            location.reload();
        });
    });
</script>
{% endblock %}
