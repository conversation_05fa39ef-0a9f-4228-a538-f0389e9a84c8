/**
 * 九猫 - 小说详情页面链接修复脚本
 * 解决小说详情页面链接跳转问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._novelLinkFixLoaded) {
        console.log('[链接修复] 小说详情页面链接修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._novelLinkFixLoaded = true;
    
    console.log('[链接修复] 小说详情页面链接修复脚本已加载 - 版本1.0.0');
    
    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[链接修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    function safeError(message) {
        try {
            originalConsoleError.call(console, '[链接修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 初始化函数
    function initialize() {
        safeLog('初始化小说详情页面链接修复');
        
        // 检查是否在小说详情页面
        if (!isNovelDetailPage()) {
            safeLog('当前不是小说详情页面，不执行修复');
            return;
        }
        
        safeLog('检测到小说详情页面，开始执行修复');
        
        // 获取小说ID
        const novelId = getNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法修复链接');
            return;
        }
        
        safeLog('当前小说ID: ' + novelId);
        
        // 修复所有链接
        fixAllLinks(novelId);
        
        // 监听DOM变化，修复新添加的链接
        observeDOMChanges(novelId);
        
        // 添加页面可见性变化事件处理
        handleVisibilityChange(novelId);
        
        safeLog('小说详情页面链接修复完成');
    }
    
    // 检查是否在小说详情页面
    function isNovelDetailPage() {
        const path = window.location.pathname;
        // 匹配 /novel/{id} 格式的URL
        return /^\/novel\/\d+\/?$/.test(path);
    }
    
    // 获取小说ID
    function getNovelId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
            
            // 从页面元素中获取
            const novelContainer = document.getElementById('novel-container');
            if (novelContainer && novelContainer.dataset.novelId) {
                return novelContainer.dataset.novelId;
            }
            
            // 从全局变量中获取
            if (window.novelId) {
                return window.novelId;
            }
            
            if (window.novelIdFromTemplate) {
                return window.novelIdFromTemplate;
            }
            
            return null;
        } catch (e) {
            safeError('获取小说ID时出错: ' + e.message);
            return null;
        }
    }
    
    // 修复所有链接
    function fixAllLinks(novelId) {
        safeLog('修复所有链接');
        
        // 修复所有a标签
        const links = document.querySelectorAll('a');
        safeLog('找到 ' + links.length + ' 个链接');
        
        links.forEach(link => {
            fixLink(link, novelId);
        });
        
        // 修复所有按钮
        const buttons = document.querySelectorAll('button');
        safeLog('找到 ' + buttons.length + ' 个按钮');
        
        buttons.forEach(button => {
            fixButton(button, novelId);
        });
        
        // 修复所有表单
        const forms = document.querySelectorAll('form');
        safeLog('找到 ' + forms.length + ' 个表单');
        
        forms.forEach(form => {
            fixForm(form, novelId);
        });
    }
    
    // 修复链接
    function fixLink(link, novelId) {
        try {
            // 检查是否有href属性
            if (!link.hasAttribute('href')) {
                return;
            }
            
            const href = link.getAttribute('href');
            
            // 检查是否是相对路径
            if (href.startsWith('/')) {
                // 检查是否是章节分析链接
                if (href.includes('/chapters') || 
                    href.includes('/chapter/') || 
                    (link.textContent && link.textContent.toLowerCase().includes('章节') && 
                     link.textContent.toLowerCase().includes('分析'))) {
                    
                    // 确保链接包含小说ID
                    if (!href.includes('/novel/' + novelId)) {
                        const newHref = '/novel/' + novelId + '/chapters';
                        safeLog('修复章节分析链接: ' + href + ' -> ' + newHref);
                        link.setAttribute('href', newHref);
                        
                        // 添加点击事件，确保正确跳转
                        link.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            safeLog('点击章节分析链接，跳转到: ' + newHref);
                            window.location.href = newHref;
                            return false;
                        }, true);
                    }
                }
                
                // 检查是否是维度分析链接
                else if (href.includes('/analysis/')) {
                    // 确保链接包含小说ID
                    if (!href.includes('/novel/' + novelId)) {
                        const dimension = href.split('/analysis/')[1];
                        const newHref = '/novel/' + novelId + '/analysis/' + dimension;
                        safeLog('修复维度分析链接: ' + href + ' -> ' + newHref);
                        link.setAttribute('href', newHref);
                        
                        // 添加点击事件，确保正确跳转
                        link.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            safeLog('点击维度分析链接，跳转到: ' + newHref);
                            window.location.href = newHref;
                            return false;
                        }, true);
                    }
                }
            }
            
            // 添加点击事件，记录所有链接点击
            link.addEventListener('click', function(e) {
                safeLog('点击链接: ' + this.getAttribute('href'));
            });
        } catch (e) {
            safeError('修复链接时出错: ' + e.message);
        }
    }
    
    // 修复按钮
    function fixButton(button, novelId) {
        try {
            // 检查是否是章节分析按钮
            if (button.textContent && 
                button.textContent.toLowerCase().includes('章节') && 
                button.textContent.toLowerCase().includes('分析')) {
                
                safeLog('找到章节分析按钮: ' + button.textContent);
                
                // 添加点击事件，确保正确跳转
                button.addEventListener('click', function(e) {
                    // 如果是模态框按钮，不处理
                    if (button.hasAttribute('data-bs-toggle') && 
                        button.getAttribute('data-bs-toggle') === 'modal') {
                        return;
                    }
                    
                    e.preventDefault();
                    e.stopPropagation();
                    const url = '/novel/' + novelId + '/chapters';
                    safeLog('点击章节分析按钮，跳转到: ' + url);
                    window.location.href = url;
                    return false;
                }, true);
            }
            
            // 检查是否是维度分析按钮
            else if (button.hasAttribute('data-dimension')) {
                const dimension = button.getAttribute('data-dimension');
                
                // 检查是否是分析按钮
                if (button.classList.contains('analyze-btn') || 
                    button.classList.contains('analyze-single-dimension') || 
                    button.classList.contains('reanalyze-btn')) {
                    
                    safeLog('找到维度分析按钮: ' + dimension);
                    
                    // 确保按钮有正确的小说ID
                    if (button.hasAttribute('data-novel-id')) {
                        button.setAttribute('data-novel-id', novelId);
                    }
                }
                
                // 检查是否是查看按钮
                else if (button.classList.contains('view-btn')) {
                    safeLog('找到维度查看按钮: ' + dimension);
                    
                    // 添加点击事件，确保正确跳转
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        const url = '/novel/' + novelId + '/analysis/' + dimension;
                        safeLog('点击维度查看按钮，跳转到: ' + url);
                        window.location.href = url;
                        return false;
                    }, true);
                }
            }
            
            // 添加点击事件，记录所有按钮点击
            button.addEventListener('click', function(e) {
                safeLog('点击按钮: ' + this.textContent);
            });
        } catch (e) {
            safeError('修复按钮时出错: ' + e.message);
        }
    }
    
    // 修复表单
    function fixForm(form, novelId) {
        try {
            // 检查是否有action属性
            if (!form.hasAttribute('action')) {
                return;
            }
            
            const action = form.getAttribute('action');
            
            // 检查是否是分析表单
            if (action.includes('/analyze/') || action.includes('/analyze_novel/')) {
                // 确保表单action包含小说ID
                if (!action.includes('/novel/' + novelId)) {
                    const newAction = '/novel/' + novelId + '/analyze';
                    safeLog('修复分析表单action: ' + action + ' -> ' + newAction);
                    form.setAttribute('action', newAction);
                }
                
                // 添加提交事件，确保正确提交
                form.addEventListener('submit', function(e) {
                    safeLog('提交分析表单: ' + this.getAttribute('action'));
                });
            }
        } catch (e) {
            safeError('修复表单时出错: ' + e.message);
        }
    }
    
    // 监听DOM变化，修复新添加的链接
    function observeDOMChanges(novelId) {
        try {
            // 创建MutationObserver
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // 检查是否有新添加的节点
                    if (mutation.addedNodes.length > 0) {
                        mutation.addedNodes.forEach(function(node) {
                            // 检查是否是元素节点
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                // 修复新添加的链接
                                const links = node.querySelectorAll('a');
                                links.forEach(link => fixLink(link, novelId));
                                
                                // 修复新添加的按钮
                                const buttons = node.querySelectorAll('button');
                                buttons.forEach(button => fixButton(button, novelId));
                                
                                // 修复新添加的表单
                                const forms = node.querySelectorAll('form');
                                forms.forEach(form => fixForm(form, novelId));
                                
                                // 如果节点本身是链接、按钮或表单，也修复它
                                if (node.tagName === 'A') {
                                    fixLink(node, novelId);
                                } else if (node.tagName === 'BUTTON') {
                                    fixButton(node, novelId);
                                } else if (node.tagName === 'FORM') {
                                    fixForm(node, novelId);
                                }
                            }
                        });
                    }
                });
            });
            
            // 配置观察选项
            const config = { childList: true, subtree: true };
            
            // 开始观察
            observer.observe(document.body, config);
            
            safeLog('已开始监听DOM变化');
        } catch (e) {
            safeError('监听DOM变化时出错: ' + e.message);
        }
    }
    
    // 处理页面可见性变化
    function handleVisibilityChange(novelId) {
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，再次修复所有链接
                safeLog('页面变为可见，再次修复所有链接');
                setTimeout(() => fixAllLinks(novelId), 100);
            }
        });
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }
    
    // 导出API，方便调试和手动调用
    window.novelLinkFix = {
        initialize: initialize,
        fixAllLinks: fixAllLinks
    };
})();
