/**
 * 九猫系统 - 增强版推理内容加载器
 * 版本: 1.1.0 - 修复推理过程显示错误
 *
 * 该脚本模仿分析结果的加载方式，确保所有13个维度都能显示推理过程
 * 修复了推理过程与分析结果混淆的问题，确保正确显示AI的思考过程
 */

(function() {
    console.log('[九猫修复] 增强版推理内容加载器已启动');

    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {}, settings: {} };

    // 如果已经运行过，不再重复执行
    if (window.__nineCatsFixes.loaded['reasoning-content-loader-enhanced']) {
        console.log('[九猫修复] 增强版推理内容加载器已经运行过，跳过');
        return;
    }

    // 配置
    const CONFIG = {
        // 重试次数
        maxRetries: 3,
        // 重试延迟（毫秒）
        retryDelay: 2000,
        // 是否启用调试日志
        enableDebug: false,
        // 是否启用自动重试
        enableAutoRetry: true,
        // 自动重试延迟（毫秒）
        autoRetryDelay: 30000,
        // 是否启用备用API
        enableFallbackApi: true,
        // 是否启用内容提取
        enableContentExtraction: true,
        // 是否启用本地缓存
        enableLocalCache: true,
        // 本地缓存过期时间（毫秒）
        localCacheExpiry: 3600000 // 1小时
    };

    // 缓存对象
    const reasoningCache = {};

    // 加载状态跟踪
    const loadingStatus = {
        retries: {},
        pendingRequests: {},
        loadedContainers: new Set()
    };

    // 辅助函数：HTML转义
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    // 辅助函数：从本地存储获取缓存
    function getFromCache(cacheKey) {
        if (!CONFIG.enableLocalCache) return null;

        try {
            const cachedData = localStorage.getItem(`reasoning_${cacheKey}`);
            if (!cachedData) return null;

            const data = JSON.parse(cachedData);

            // 检查缓存是否过期
            if (data.timestamp && (Date.now() - data.timestamp < CONFIG.localCacheExpiry)) {
                console.info(`[九猫修复] 从本地缓存加载推理内容: ${cacheKey}`);
                return data.content;
            }

            // 缓存已过期，删除
            localStorage.removeItem(`reasoning_${cacheKey}`);
            return null;
        } catch (e) {
            console.warn(`[九猫修复] 读取本地缓存失败: ${e.message}`);
            return null;
        }
    }

    // 辅助函数：保存到本地存储
    function saveToCache(cacheKey, content) {
        if (!CONFIG.enableLocalCache) return;

        try {
            const data = {
                content: content,
                timestamp: Date.now()
            };

            localStorage.setItem(`reasoning_${cacheKey}`, JSON.stringify(data));
            console.info(`[九猫修复] 推理内容已保存到本地缓存: ${cacheKey}`);
        } catch (e) {
            console.warn(`[九猫修复] 保存到本地缓存失败: ${e.message}`);
        }
    }

    // 主函数：加载推理内容
    function loadReasoningContent(novelId, dimension, containerId) {
        // 如果已经加载过，不再重复加载
        if (loadingStatus.loadedContainers.has(containerId)) {
            if (CONFIG.enableDebug) {
                console.log(`[九猫修复] 容器 ${containerId} 已加载过推理内容，跳过`);
            }
            return;
        }

        // 获取容器元素
        const container = document.getElementById(containerId);
        if (!container) {
            console.warn(`[九猫修复] 找不到容器元素: ${containerId}`);
            return;
        }

        // 显示加载中状态
        container.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载推理过程，请稍候...</p>
            </div>
        `;

        // 检查缓存
        const cacheKey = `${novelId}_${dimension}`;
        const cachedContent = getFromCache(cacheKey);

        if (cachedContent) {
            // 使用缓存内容
            renderContent(container, cachedContent);
            loadingStatus.loadedContainers.add(containerId);
            return;
        }

        // 构建API URL
        const apiUrl = `/api/novel/${novelId}/analysis/${dimension}/reasoning_content`;

        // 标记为正在加载
        loadingStatus.pendingRequests[containerId] = true;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                // 特殊处理404错误，将其视为"暂无数据"而非错误
                if (response.status === 404) {
                    return {
                        success: false,
                        error: '暂无推理过程数据',
                        status: 404
                    };
                }

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                // 删除正在加载标记
                delete loadingStatus.pendingRequests[containerId];

                if (data.success && data.reasoning_content) {
                    // 显示推理过程
                    renderContent(container, data.reasoning_content);

                    // 保存到缓存
                    saveToCache(cacheKey, data.reasoning_content);

                    // 标记为已加载
                    loadingStatus.loadedContainers.add(containerId);

                    console.info(`[九猫修复] 推理过程加载成功: ${dimension}`);
                } else if (data.status === 404) {
                    // 显示友好的提示信息（不是错误）
                    renderNotFoundMessage(container);
                    console.info(`[九猫修复] 暂无推理过程数据: ${dimension}`);

                    // 如果启用了备用API，尝试使用备用API
                    if (CONFIG.enableFallbackApi) {
                        tryFallbackApi(novelId, dimension, container, containerId);
                    }
                } else {
                    // 显示错误信息
                    renderErrorMessage(container, data.error || '未知错误');
                    console.warn(`[九猫修复] 加载推理过程失败: ${data.error || '未知错误'}`);

                    // 如果启用了备用API，尝试使用备用API
                    if (CONFIG.enableFallbackApi) {
                        tryFallbackApi(novelId, dimension, container, containerId);
                    }
                }
            })
            .catch(error => {
                // 删除正在加载标记
                delete loadingStatus.pendingRequests[containerId];

                // 显示错误信息，但使用更友好的样式
                renderErrorMessage(container, error.message);

                // 记录错误但不在控制台显示为错误
                console.warn(`[九猫修复] 加载推理过程时遇到问题: ${error.message}`);

                // 如果启用了备用API，尝试使用备用API
                if (CONFIG.enableFallbackApi) {
                    tryFallbackApi(novelId, dimension, container, containerId);
                }
                // 如果启用了自动重试，30秒后自动重试
                else if (CONFIG.enableAutoRetry) {
                    // 获取重试次数
                    loadingStatus.retries[containerId] = (loadingStatus.retries[containerId] || 0) + 1;

                    // 如果未超过最大重试次数，自动重试
                    if (loadingStatus.retries[containerId] <= CONFIG.maxRetries) {
                        console.info(`[九猫修复] ${CONFIG.autoRetryDelay/1000}秒后自动重试加载推理过程...`);

                        setTimeout(() => {
                            loadReasoningContent(novelId, dimension, containerId);
                        }, CONFIG.autoRetryDelay);
                    }
                }
            });
    }

    // 尝试使用备用API
    function tryFallbackApi(novelId, dimension, container, containerId) {
        console.info(`[九猫修复] 尝试使用备用API加载推理过程: ${dimension}`);

        // 构建备用API URL
        const fallbackApiUrl = `/api/novel/${novelId}/analysis/${dimension}/process`;

        // 发送请求
        fetch(fallbackApiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.process) {
                    // 显示推理过程
                    let processContent = data.process;

                    // 如果是对象或数组，转换为字符串
                    if (typeof processContent === 'object') {
                        processContent = JSON.stringify(processContent, null, 2);
                    }

                    renderContent(container, processContent);

                    // 保存到缓存
                    const cacheKey = `${novelId}_${dimension}`;
                    saveToCache(cacheKey, processContent);

                    // 标记为已加载
                    loadingStatus.loadedContainers.add(containerId);

                    console.info(`[九猫修复] 通过备用API加载推理过程成功: ${dimension}`);
                } else {
                    // 如果启用了内容提取，尝试从分析结果中提取推理过程
                    if (CONFIG.enableContentExtraction) {
                        tryExtractFromContent(novelId, dimension, container, containerId);
                    } else {
                        // 显示友好的提示信息
                        renderNotFoundMessage(container);
                    }
                }
            })
            .catch(error => {
                console.warn(`[九猫修复] 备用API加载失败: ${error.message}`);

                // 如果启用了内容提取，尝试从分析结果中提取推理过程
                if (CONFIG.enableContentExtraction) {
                    tryExtractFromContent(novelId, dimension, container, containerId);
                } else {
                    // 显示友好的提示信息
                    renderNotFoundMessage(container);
                }
            });
    }

    // 尝试从分析结果中提取推理过程
    function tryExtractFromContent(novelId, dimension, container, containerId) {
        console.info(`[九猫修复] 尝试从分析结果中提取推理过程: ${dimension}`);

        // 构建API URL - 直接使用分析结果API
        const apiUrl = `/api/novel/${novelId}/analysis/${dimension}`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 首先检查是否直接包含reasoning_content字段
                if (data.success && data.reasoning_content) {
                    // 直接使用reasoning_content字段，但先验证内容
                    console.info(`[九猫修复] 成功获取到推理过程字段: ${dimension}`);

                    // 检查是否真的是推理过程而不是分析结果
                    const resultMarkers = [
                        '### **一、',
                        '### 一、',
                        '## 一、',
                        '# 一、',
                        '---\n### **',
                        '## **',
                        '# **',
                        '### **总结',
                        '## **总结',
                        '# **总结',
                        '句式变化分析',
                        '重点探讨',
                        '以下是对该小说文本的',
                        '这部小说文本展现出了',
                        '风格特征：',
                        '主题架构：',
                        '写作技法：',
                        '人物塑造：',
                        '文化符码：',
                        '结构创新：',
                        '节奏控制分析',
                        '### 二、',
                        '### 三、',
                        '### 四、',
                        '### 五、',
                        '世界构建分析',
                        '角色塑造分析',
                        '情节发展分析',
                        '语言风格分析',
                        '主题探索分析',
                        '叙事视角分析',
                        '情感表达分析',
                        '冲突设置分析',
                        '节奏控制分析',
                        '象征意象分析',
                        '文化背景分析',
                        '结构布局分析',
                        '对话艺术分析'
                    ];

                    let isAnalysisResult = false;
                    for (const marker of resultMarkers) {
                        if (data.reasoning_content.includes(marker)) {
                            isAnalysisResult = true;
                            console.warn(`[九猫修复] 警告：reasoning_content字段包含分析结果标记: "${marker}"`);
                            break;
                        }
                    }

                    if (isAnalysisResult) {
                        console.warn(`[九猫修复] 警告：reasoning_content字段可能包含分析结果而非推理过程`);

                        // 尝试从API响应中查找真正的推理过程
                        if (data.metadata && data.metadata.reasoning_content) {
                            console.info(`[九猫修复] 尝试从元数据中获取推理过程`);
                            renderContent(container, data.metadata.reasoning_content);

                            // 保存到缓存
                            const cacheKey = `${novelId}_${dimension}`;
                            saveToCache(cacheKey, data.metadata.reasoning_content);

                            // 标记为已加载
                            loadingStatus.loadedContainers.add(containerId);
                            return;
                        }
                    } else {
                        // 显示推理过程
                        renderContent(container, data.reasoning_content);

                        // 保存到缓存
                        const cacheKey = `${novelId}_${dimension}`;
                        saveToCache(cacheKey, data.reasoning_content);

                        // 标记为已加载
                        loadingStatus.loadedContainers.add(containerId);
                        return;
                    }
                }

                // 如果没有直接的reasoning_content字段，尝试从元数据中获取
                if (data.success && data.metadata && data.metadata.reasoning_content) {
                    // 使用元数据中的reasoning_content，但先验证内容
                    console.info(`[九猫修复] 从元数据中获取到推理过程字段: ${dimension}`);

                    // 检查是否真的是推理过程而不是分析结果
                    const resultMarkers = [
                        '### **一、',
                        '### 一、',
                        '## 一、',
                        '# 一、',
                        '---\n### **',
                        '## **',
                        '# **',
                        '### **总结',
                        '## **总结',
                        '# **总结',
                        '句式变化分析',
                        '重点探讨',
                        '以下是对该小说文本的',
                        '这部小说文本展现出了',
                        '风格特征：',
                        '主题架构：',
                        '写作技法：',
                        '人物塑造：',
                        '文化符码：',
                        '结构创新：',
                        '节奏控制分析',
                        '### 二、',
                        '### 三、',
                        '### 四、',
                        '### 五、',
                        '世界构建分析',
                        '角色塑造分析',
                        '情节发展分析',
                        '语言风格分析',
                        '主题探索分析',
                        '叙事视角分析',
                        '情感表达分析',
                        '冲突设置分析',
                        '节奏控制分析',
                        '象征意象分析',
                        '文化背景分析',
                        '结构布局分析',
                        '对话艺术分析'
                    ];

                    let isAnalysisResult = false;
                    for (const marker of resultMarkers) {
                        if (data.metadata.reasoning_content.includes(marker)) {
                            isAnalysisResult = true;
                            console.warn(`[九猫修复] 警告：元数据中的reasoning_content字段包含分析结果标记: "${marker}"`);
                            break;
                        }
                    }

                    if (isAnalysisResult) {
                        console.warn(`[九猫修复] 警告：元数据中的reasoning_content字段可能包含分析结果而非推理过程`);
                    }

                    // 即使可能是分析结果，也显示内容，但会在renderContent中显示警告
                    renderContent(container, data.metadata.reasoning_content);

                    // 保存到缓存
                    const cacheKey = `${novelId}_${dimension}`;
                    saveToCache(cacheKey, data.metadata.reasoning_content);

                    // 标记为已加载
                    loadingStatus.loadedContainers.add(containerId);
                    return;
                }

                // 如果仍然没有找到，尝试从content中提取
                if (data.success) {
                    // 首先检查是否有reasoning_content字段
                    if (data.reasoning_content) {
                        console.log("[九猫修复] 从API响应的reasoning_content字段获取推理过程");
                        renderContent(container, data.reasoning_content);
                        return;
                    }

                    // 检查content是否包含"好的，我现在要"等推理过程的特征
                    if (data.content) {
                        const content = data.content;
                        const processMarkers = [
                            '好的，我现在要',
                            '首先，我需要',
                            '我将分析',
                            '我需要分析',
                            '嗯，用户让我',
                            '下面我来分析',
                            '让我来分析',
                            '我会按照以下步骤',
                            '我将按照以下步骤',
                            '我将从以下几个方面',
                            '我需要从以下几个方面',
                            '我将逐步分析',
                            '好的，我现在需要',
                            '我需要通读一遍',
                            '我需要先了解',
                            '我会先阅读',
                            '我先阅读一遍',
                            '我需要分析用户提供的',
                            '开始分析这段小说',
                            '我需要全面考虑',
                            '我得仔细阅读',
                            '作为文学分析专家',
                            '我应该观察',
                            '我需要注意',
                            '我将关注',
                            '我要分析',
                            '我要考虑',
                            '我要思考',
                            '我要探讨',
                            '我要研究',
                            '我要理解',
                            '我要解析',
                            '我要评估',
                            '我要判断',
                            '我要确定',
                            '我要找出',
                            '我要识别',
                            '我要辨别',
                            '我要区分',
                            '我要比较',
                            '我要对比',
                            '我要总结',
                            '我要归纳',
                            '我要提炼',
                            '我要整理',
                            '我要组织',
                            '我要构建',
                            '我要形成',
                            '我要得出',
                            '我要推导',
                            '我要推理',
                            '我要推断',
                            '我要推测',
                            '我要猜测',
                            '我要假设',
                            '我要设想',
                            '我要想象',
                            '我要预测'
                        ];

                        // 检查内容的第一段是否包含推理过程的特征
                        const firstParagraph = content.split('\n\n')[0] || '';
                        let firstParagraphIsReasoning = false;
                        for (const marker of processMarkers) {
                            if (firstParagraph.includes(marker)) {
                                firstParagraphIsReasoning = true;
                                break;
                            }
                        }

                        // 检查内容是否以"好的"、"首先"等推理过程常见开头词开始
                        const startWords = ['好的', '首先', '我将', '我需要', '让我', '我会', '我要', '我应该', '我得'];
                        let startsWithProcessWord = false;
                        for (const word of startWords) {
                            if (content.trim().startsWith(word)) {
                                startsWithProcessWord = true;
                                break;
                            }
                        }

                        // 检查是否包含推理过程的特征
                        let isReasoningProcess = false;
                        let processMarkerFound = null;
                        for (const marker of processMarkers) {
                            if (content.includes(marker)) {
                                isReasoningProcess = true;
                                processMarkerFound = marker;
                                break;
                            }
                        }

                        if (isReasoningProcess || firstParagraphIsReasoning || startsWithProcessWord) {
                            // 这可能是一个完整的推理过程被错误地放在了content字段
                            console.info(`[九猫修复] 在content中发现推理过程特征${processMarkerFound ? ': ' + processMarkerFound : ''}`);

                            // 显示整个content作为推理过程
                            renderContent(container, content);

                            // 保存到缓存
                            const cacheKey = `${novelId}_${dimension}`;
                            saveToCache(cacheKey, content);

                            // 标记为已加载
                            loadingStatus.loadedContainers.add(containerId);
                            return;
                        }

                    // 检查是否有process字段
                    if (data.process) {
                        console.info(`[九猫修复] 从API响应的process字段获取推理过程`);
                        renderContent(container, data.process);

                        // 保存到缓存
                        const cacheKey = `${novelId}_${dimension}`;
                        saveToCache(cacheKey, data.process);

                        // 标记为已加载
                        loadingStatus.loadedContainers.add(containerId);
                        return;
                    }

                    // 如果没有找到任何推理过程，显示友好的提示信息
                    renderNotFoundMessage(container);
                } else {
                    // 显示友好的提示信息
                    renderNotFoundMessage(container);
                }
            })
            .catch(error => {
                console.warn(`[九猫修复] 获取推理过程失败: ${error.message}`);

                // 显示友好的提示信息
                renderNotFoundMessage(container);
            });
    }

    // 渲染内容
    function renderContent(container, content) {
        // 检查内容是否是分析结果而不是推理过程
        const resultMarkers = [
            '### **一、',
            '### 一、',
            '## 一、',
            '# 一、',
            '---\n### **',
            '## **',
            '# **',
            '### **总结',
            '## **总结',
            '# **总结',
            '句式变化分析',
            '重点探讨',
            '以下是对该小说文本的',
            '这部小说文本展现出了',
            '风格特征：',
            '主题架构：',
            '写作技法：',
            '人物塑造：',
            '文化符码：',
            '结构创新：',
            '节奏控制分析',
            '### 二、',
            '### 三、',
            '### 四、',
            '### 五、',
            '世界构建分析',
            '角色塑造分析',
            '情节发展分析',
            '语言风格分析',
            '主题探索分析',
            '叙事视角分析',
            '情感表达分析',
            '冲突设置分析',
            '节奏控制分析',
            '象征意象分析',
            '文化背景分析',
            '结构布局分析',
            '对话艺术分析'
        ];

        // 检查内容是否是推理过程
        const processMarkers = [
            '好的，我现在要',
            '首先，我需要',
            '我将分析',
            '我需要分析',
            '嗯，用户让我',
            '下面我来分析',
            '让我来分析',
            '我会按照以下步骤',
            '我将按照以下步骤',
            '我将从以下几个方面',
            '我需要从以下几个方面',
            '我将逐步分析',
            '好的，我现在需要',
            '我需要通读一遍',
            '我需要先了解',
            '我会先阅读',
            '我先阅读一遍',
            '我需要分析用户提供的',
            '开始分析这段小说',
            '我需要全面考虑',
            '我得仔细阅读',
            '作为文学分析专家',
            '现在需要把这些思考整理成',
            '可能还需要考虑',
            '另外，需要关注',
            '我应该观察',
            '我需要注意',
            '我将关注',
            '我要分析',
            '我要考虑',
            '我要思考',
            '我要探讨',
            '我要研究',
            '我要理解',
            '我要解析',
            '我要评估',
            '我要判断',
            '我要确定',
            '我要找出',
            '我要识别',
            '我要辨别',
            '我要区分',
            '我要比较',
            '我要对比',
            '我要总结',
            '我要归纳',
            '我要提炼',
            '我要整理',
            '我要组织',
            '我要构建',
            '我要形成',
            '我要得出',
            '我要推导',
            '我要推理',
            '我要推断',
            '我要推测',
            '我要猜测',
            '我要假设',
            '我要设想',
            '我要想象',
            '我要预测',
            '我要预估',
            '我要预期',
            '我要预见',
            '我要预判',
            '我要预测',
            '我要预想',
            '我要预料',
            '我要预计',
            '我要预知',
            '我要预感',
            '我要预示',
            '我要预兆',
            '我要预言',
            '我要预告',
            '我要预报',
            '我要预测',
            '我要预想',
            '我要预料',
            '我要预计',
            '我要预知',
            '我要预感',
            '我要预示',
            '我要预兆',
            '我要预言',
            '我要预告',
            '我要预报'
        ];

        // 检查是否包含分析结果的特征
        let isAnalysisResult = false;
        let resultMarkerFound = null;
        for (const marker of resultMarkers) {
            if (content.includes(marker)) {
                isAnalysisResult = true;
                resultMarkerFound = marker;
                break;
            }
        }

        // 检查是否包含推理过程的特征
        let isReasoningProcess = false;
        let processMarkerFound = null;
        for (const marker of processMarkers) {
            if (content.includes(marker)) {
                isReasoningProcess = true;
                processMarkerFound = marker;
                break;
            }
        }

        // 检查内容的第一段是否包含推理过程的特征
        const firstParagraph = content.split('\n\n')[0] || '';
        let firstParagraphIsReasoning = false;
        for (const marker of processMarkers) {
            if (firstParagraph.includes(marker)) {
                firstParagraphIsReasoning = true;
                break;
            }
        }

        // 检查内容是否以"好的"、"首先"等推理过程常见开头词开始
        const startWords = ['好的', '首先', '我将', '我需要', '让我', '我会', '我要', '我应该', '我得'];
        let startsWithProcessWord = false;
        for (const word of startWords) {
            if (content.trim().startsWith(word)) {
                startsWithProcessWord = true;
                break;
            }
        }

        // 根据内容特征判断类型
        if ((isReasoningProcess && !isAnalysisResult) || (firstParagraphIsReasoning && !isAnalysisResult) || startsWithProcessWord) {
            // 确认是推理过程，正常显示
            container.innerHTML = `
                <div class="alert alert-success mb-3">
                    <p><i class="fas fa-check-circle"></i> <strong>成功：</strong> 成功加载AI推理过程。</p>
                </div>
                <pre class="reasoning-text">${escapeHtml(content)}</pre>
            `;
            console.info('[九猫修复] 成功识别并显示推理过程');
            if (processMarkerFound) {
                console.debug(`[九猫修复] 推理过程标记: "${processMarkerFound}"`);
            }
        } else if (isAnalysisResult) {
            // 这可能是分析结果而不是推理过程，显示警告
            container.innerHTML = `
                <div class="alert alert-danger mb-3">
                    <p><i class="fas fa-exclamation-triangle"></i> <strong>错误：</strong> 系统错误地将分析结果显示为推理过程。</p>
                    <p class="small">这是因为系统无法正确区分推理过程和分析结果。推理过程应该包含AI的思考过程，而不是格式化的分析报告。</p>
                    <p class="small">请联系管理员修复此问题。</p>
                </div>
                <pre class="reasoning-text">${escapeHtml(content)}</pre>
            `;
            console.warn('[九猫修复] 警告：API返回的是分析结果而不是推理过程');
            if (resultMarkerFound) {
                console.debug(`[九猫修复] 分析结果标记: "${resultMarkerFound}"`);
            }
        } else {
            // 无法确定，但仍然显示内容
            container.innerHTML = `
                <div class="alert alert-info mb-3">
                    <p><i class="fas fa-info-circle"></i> <strong>提示：</strong> 无法确定内容类型，但仍然显示获取到的内容。</p>
                </div>
                <pre class="reasoning-text">${escapeHtml(content)}</pre>
            `;
            console.info('[九猫修复] 无法确定内容类型，但仍然显示获取到的内容');
        }
    }

    // 渲染未找到消息
    function renderNotFoundMessage(container) {
        container.innerHTML = `
            <div class="alert alert-info">
                <p><i class="fas fa-info-circle"></i> 暂无推理过程数据</p>
                <p class="small text-muted mt-2">可能的原因：</p>
                <ul class="small text-muted">
                    <li>该分析是在启用推理过程记录功能之前进行的</li>
                    <li>分析过程中未生成推理过程</li>
                    <li>推理过程数据已被清理</li>
                    <li>分析尚未完成，请稍后再查看</li>
                </ul>
            </div>
        `;
    }

    // 渲染错误消息
    function renderErrorMessage(container, errorMessage) {
        container.innerHTML = `
            <div class="alert alert-warning">
                <p><i class="fas fa-exclamation-circle"></i> 无法加载推理过程</p>
                <p class="small text-muted mt-2">系统将在稍后自动重试，您也可以刷新页面重试。</p>
            </div>
        `;
    }

    // 初始化所有推理内容容器
    function initAllReasoningContainers() {
        // 查找所有推理内容容器
        const containers = document.querySelectorAll('[data-reasoning-container]');

        if (containers.length > 0) {
            console.info(`[九猫修复] 找到 ${containers.length} 个推理内容容器`);

            // 初始化每个容器
            containers.forEach(container => {
                const novelId = container.getAttribute('data-novel-id');
                const dimension = container.getAttribute('data-dimension');
                const containerId = container.id;

                if (novelId && dimension && containerId) {
                    // 加载推理内容
                    loadReasoningContent(novelId, dimension, containerId);
                } else {
                    console.warn(`[九猫修复] 容器缺少必要属性: novel-id=${novelId}, dimension=${dimension}, id=${containerId}`);
                }
            });
        } else {
            console.info('[九猫修复] 未找到推理内容容器，将在页面加载完成后再次检查');

            // 页面加载完成后再次检查
            window.addEventListener('load', () => {
                const containers = document.querySelectorAll('[data-reasoning-container]');

                if (containers.length > 0) {
                    console.info(`[九猫修复] 页面加载后找到 ${containers.length} 个推理内容容器`);

                    // 初始化每个容器
                    containers.forEach(container => {
                        const novelId = container.getAttribute('data-novel-id');
                        const dimension = container.getAttribute('data-dimension');
                        const containerId = container.id;

                        if (novelId && dimension && containerId) {
                            // 加载推理内容
                            loadReasoningContent(novelId, dimension, containerId);
                        }
                    });
                }
            });
        }
    }

    // 导出全局函数
    window.loadReasoningContent = loadReasoningContent;

    // 初始化
    initAllReasoningContainers();

    // 标记为已加载
    window.__nineCatsFixes.loaded['reasoning-content-loader-enhanced'] = true;

    console.log('[九猫修复] 增强版推理内容加载器初始化完成');
})();
