@echo off
echo 九猫 - Chart.js 修复脚本
echo ===========================
echo.

REM 检查 Chart.js 源文件是否存在
if not exist "src\web\static\js\lib\chart.min.js" (
    echo 错误: 源文件 src\web\static\js\lib\chart.min.js 不存在!
    echo 请确保源文件存在后再运行此脚本。
    goto :end
)

echo 1. 复制 Chart.js 到 static/js 目录
copy "src\web\static\js\lib\chart.min.js" "src\web\static\js\chart.min.js" /Y
if %errorlevel% neq 0 (
    echo 复制到 static/js 目录失败!
) else (
    echo 成功复制到 static/js 目录。
)

echo.
echo 2. 确保 direct-static/js/lib 目录存在
if not exist "src\web\direct-static\js\lib" (
    mkdir "src\web\direct-static\js\lib"
    echo 创建了 direct-static/js/lib 目录。
) else (
    echo direct-static/js/lib 目录已存在。
)

echo.
echo 3. 复制 Chart.js 到 direct-static/js 目录
copy "src\web\static\js\lib\chart.min.js" "src\web\direct-static\js\chart.min.js" /Y
if %errorlevel% neq 0 (
    echo 复制到 direct-static/js 目录失败!
) else (
    echo 成功复制到 direct-static/js 目录。
)

echo.
echo 4. 复制 Chart.js 到 direct-static/js/lib 目录
copy "src\web\static\js\lib\chart.min.js" "src\web\direct-static\js\lib\chart.min.js" /Y
if %errorlevel% neq 0 (
    echo 复制到 direct-static/js/lib 目录失败!
) else (
    echo 成功复制到 direct-static/js/lib 目录。
)

echo.
echo 5. 复制综合修复脚本到 static/js 目录
copy "src\web\static\js\chart-comprehensive-fix.js" "src\web\direct-static\js\chart-comprehensive-fix.js" /Y
if %errorlevel% neq 0 (
    echo 复制综合修复脚本失败!
) else (
    echo 成功复制综合修复脚本。
)

echo.
echo 修复完成!
echo 请重启九猫系统以应用更改。

:end
pause
