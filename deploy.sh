
#!/bin/bash

# 安装必要的依赖
sudo apt-get update
sudo apt-get install -y python3 python3-pip nginx

# 创建项目目录
sudo mkdir -p /var/www/jiuma
sudo chown -R $USER:$USER /var/www/jiuma

# 配置 Python 虚拟环境
python3 -m venv /var/www/jiuma/venv
source /var/www/jiuma/venv/bin/activate

# 安装项目依赖
pip install -r requirements.txt

# 配置 Nginx
sudo bash -c 'cat > /etc/nginx/sites-available/jiuma << EOL
server {
    listen 80;
    server_name **************;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOL'

# 启用站点配置
sudo ln -s /etc/nginx/sites-available/jiuma /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

# 创建系统服务
sudo bash -c 'cat > /etc/systemd/system/jiuma.service << EOL
[Unit]
Description=Jiuma Web Application
After=network.target

[Service]
User=$USER
Group=$USER
WorkingDirectory=/var/www/jiuma
Environment="PATH=/var/www/jiuma/venv/bin"
ExecStart=/var/www/jiuma/venv/bin/python app.py

[Install]
WantedBy=multi-user.target
EOL'

# 启动服务
sudo systemctl daemon-reload
sudo systemctl start jiuma
sudo systemctl enable jiuma

echo "部署完成！"
