"""
九猫 - 查找数据库文件脚本
使用Python的os模块直接查找数据库文件
"""

import os
import sys

def find_database_files():
    """查找数据库文件"""
    db_files = []
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                db_path = os.path.join(root, file)
                db_files.append(db_path)
                print(f"找到数据库文件: {db_path}")
    
    return db_files

def main():
    """主函数"""
    print("开始查找数据库文件")
    
    # 查找数据库文件
    db_files = find_database_files()
    
    if db_files:
        print(f"找到 {len(db_files)} 个数据库文件")
    else:
        print("未找到数据库文件")

if __name__ == "__main__":
    main()
