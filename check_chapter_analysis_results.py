"""
检查章节分析结果和推理过程

此脚本用于检查章节分析结果和推理过程，帮助诊断问题。
"""

import os
import sys
import logging
import json
from sqlalchemy import or_

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的模块
try:
    from src.db.connection import Session
    from src.models.chapter_analysis_result import ChapterAnalysisResult
    from src.models.chapter_analysis_process import ChapterAnalysisProcess
    from src.models.chapter import Chapter
    from src.models.novel import Novel
except ImportError as e:
    logger.error(f"导入模块时出错: {str(e)}")
    sys.exit(1)

def check_chapter_analysis_results(chapter_id=None, dimension=None):
    """
    检查章节分析结果和推理过程
    
    Args:
        chapter_id: 章节ID，如果为None则检查所有章节
        dimension: 分析维度，如果为None则检查所有维度
    """
    session = Session()
    try:
        # 构建查询
        query = session.query(ChapterAnalysisResult)
        
        # 如果指定了章节ID，只检查该章节
        if chapter_id:
            query = query.filter(ChapterAnalysisResult.chapter_id == chapter_id)
        
        # 如果指定了维度，只检查该维度
        if dimension:
            query = query.filter(ChapterAnalysisResult.dimension == dimension)
        
        # 获取所有分析结果
        results = query.all()
        logger.info(f"找到 {len(results)} 个章节分析结果")
        
        # 检查每个分析结果
        for result in results:
            try:
                # 获取章节和小说信息
                chapter = session.query(Chapter).filter(Chapter.id == result.chapter_id).first()
                novel = session.query(Novel).filter(Novel.id == result.novel_id).first()
                
                if not chapter or not novel:
                    logger.warning(f"找不到章节或小说: chapter_id={result.chapter_id}, novel_id={result.novel_id}")
                    continue
                
                logger.info(f"检查章节分析结果: id={result.id}, chapter_id={result.chapter_id}, dimension={result.dimension}")
                
                # 检查内容
                content = result.content or ""
                logger.info(f"内容长度: {len(content)}")
                
                # 检查推理过程
                reasoning_content = result.reasoning_content or ""
                logger.info(f"推理过程长度: {len(reasoning_content)}")
                
                # 检查是否有分析过程记录
                processes = session.query(ChapterAnalysisProcess).filter(
                    ChapterAnalysisProcess.result_id == result.id,
                    ChapterAnalysisProcess.processing_stage == "reasoning"
                ).all()
                
                logger.info(f"找到 {len(processes)} 个分析过程记录")
                
                # 检查每个分析过程记录
                for process in processes:
                    logger.info(f"分析过程记录: id={process.id}, stage={process.processing_stage}")
                    logger.info(f"输出文本长度: {len(process.output_text or '')}")
                
                # 如果是章纲分析，检查内容重现部分
                if result.dimension == "chapter_outline" and "## 主要内容" in content:
                    # 提取主要内容部分
                    import re
                    main_content_match = re.search(r'## 主要内容\s*\n(.*?)(?:\n##|\Z)', content, re.DOTALL)
                    if main_content_match:
                        main_content = main_content_match.group(1).strip()
                        logger.info(f"主要内容长度: {len(main_content)}")
                        logger.info(f"主要内容前100个字符: {main_content[:100]}")
                    else:
                        logger.warning(f"未找到主要内容部分")
                
                # 如果没有推理过程，但有分析过程记录，可以从分析过程记录中恢复
                if not reasoning_content and processes:
                    logger.info(f"可以从分析过程记录中恢复推理过程")
                    
                    # 获取最新的分析过程记录
                    latest_process = max(processes, key=lambda p: p.id)
                    
                    if latest_process.output_text:
                        logger.info(f"最新分析过程记录的输出文本长度: {len(latest_process.output_text)}")
                        logger.info(f"输出文本前100个字符: {latest_process.output_text[:100]}")
            except Exception as e:
                logger.error(f"检查章节分析结果时出错: {str(e)}")
    except Exception as e:
        logger.error(f"检查章节分析结果时出错: {str(e)}")
    finally:
        session.close()

if __name__ == "__main__":
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="检查章节分析结果和推理过程")
    parser.add_argument("--chapter_id", type=int, help="章节ID，如果不指定则检查所有章节")
    parser.add_argument("--dimension", type=str, help="分析维度，如果不指定则检查所有维度")
    args = parser.parse_args()
    
    # 执行检查
    check_chapter_analysis_results(args.chapter_id, args.dimension)
