.TH "NPM\-EXPLAIN" "1" "June 2022" "" ""
.SH "NAME"
\fBnpm-explain\fR \- Explain installed packages
.SS Synopsis
.P
.RS 2
.nf
npm explain <folder | specifier>

alias: why
.fi
.RE
.SS Description
.P
This command will print the chain of dependencies causing a given package
to be installed in the current project\.
.P
Positional arguments can be either folders within \fBnode_modules\fP, or
\fBname@version\-range\fP specifiers, which will select the dependency
relationships to explain\.
.P
For example, running \fBnpm explain glob\fP within npm's source tree will show:
.P
.RS 2
.nf
glob@7\.1\.6
node_modules/glob
  glob@"^7\.1\.4" from the root project

glob@7\.1\.1 dev
node_modules/tacks/node_modules/glob
  glob@"^7\.0\.5" from rimraf@2\.6\.2
  node_modules/tacks/node_modules/rimraf
    rimraf@"^2\.6\.2" from tacks@1\.3\.0
    node_modules/tacks
      dev tacks@"^1\.3\.0" from the root project
.fi
.RE
.P
To explain just the package residing at a specific folder, pass that as the
argument to the command\.  This can be useful when trying to figure out
exactly why a given dependency is being duplicated to satisfy conflicting
version requirements within the project\.
.P
.RS 2
.nf
$ npm explain node_modules/nyc/node_modules/find\-up
find\-up@3\.0\.0 dev
node_modules/nyc/node_modules/find\-up
  find\-up@"^3\.0\.0" from nyc@14\.1\.1
  node_modules/nyc
    nyc@"^14\.1\.1" from tap@14\.10\.8
    node_modules/tap
      dev tap@"^14\.10\.8" from the root project
.fi
.RE
.SS Configuration
.SS \fBjson\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Whether or not to output JSON data, rather than the normal output\.
.RS 0
.IP \(bu 2
In \fBnpm pkg set\fP it enables parsing set values with JSON\.parse() before
saving them to your \fBpackage\.json\fP\|\.

.RE
.P
Not supported by all npm commands\.
.SS \fBworkspace\fP
.RS 0
.IP \(bu 2
Default:
.IP \(bu 2
Type: String (can be set multiple times)

.RE
.P
Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option\.
.P
Valid values for the \fBworkspace\fP config are either:
.RS 0
.IP \(bu 2
Workspace names
.IP \(bu 2
Path to a workspace directory
.IP \(bu 2
Path to a parent workspace directory (will result in selecting all
workspaces within that folder)

.RE
.P
When set for the \fBnpm init\fP command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project\.
.P
This value is not exported to the environment for child processes\.
.SS See Also
.RS 0
.IP \(bu 2
npm help config
.IP \(bu 2
npm help npmrc
.IP \(bu 2
npm help folders
.IP \(bu 2
npm help ls
.IP \(bu 2
npm help install
.IP \(bu 2
npm help link
.IP \(bu 2
npm help prune
.IP \(bu 2
npm help outdated
.IP \(bu 2
npm help update

.RE
