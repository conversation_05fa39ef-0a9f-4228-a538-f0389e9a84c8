/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 高潮节奏和人物关系分析专用修复脚本
 * 版本: 1.2.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('高潮节奏和人物关系分析专用修复脚本已加载');
    
    // 安全DOM操作对象
    const safeDom = {
        // 安全地操作DOM元素
        appendChild: function(parent, child) {
            try {
                if (!parent || !child) {
                    console.error('appendChild操作失败: 父节点或子节点为null');
                    return null;
                }
                
                // 检查子节点是否已经是父节点的子节点
                if (parent.contains && parent.contains(child)) {
                    console.warn('appendChild: 尝试添加已经是子节点的元素');
                    return child;
                }
                
                return parent.appendChild(child);
            } catch (e) {
                console.error('appendChild操作失败:', e.message);
                return null;
            }
        },
        
        // 安全地替换子节点
        replaceChild: function(parent, newChild, oldChild) {
            try {
                if (!parent || !newChild || !oldChild) {
                    console.error('replaceChild操作失败: 参数不完整');
                    return null;
                }
                
                // 检查oldChild是否真的是父节点的子节点
                let isChild = false;
                for (let i = 0; i < parent.childNodes.length; i++) {
                    if (parent.childNodes[i] === oldChild) {
                        isChild = true;
                        break;
                    }
                }
                
                if (!isChild) {
                    console.error('replaceChild: 旧节点不是父节点的子节点');
                    return null;
                }
                
                return parent.replaceChild(newChild, oldChild);
            } catch (e) {
                console.error('replaceChild操作失败:', e.message);
                return null;
            }
        },
        
        // 安全地创建元素
        createElement: function(tagName) {
            try {
                return document.createElement(tagName);
            } catch (e) {
                console.error('createElement操作失败:', e.message);
                return null;
            }
        },
        
        // 安全地设置元素内容
        setTextContent: function(element, text) {
            try {
                if (!element) {
                    console.error('setTextContent操作失败: 元素为null');
                    return;
                }
                
                if (typeof element.textContent !== 'undefined') {
                    element.textContent = text;
                } else if (typeof element.innerText !== 'undefined') {
                    element.innerText = text;
                } else {
                    console.warn('setTextContent: 无法设置文本内容，尝试使用innerHTML');
                    element.innerHTML = text;
                }
            } catch (e) {
                console.error('setTextContent操作失败:', e.message);
            }
        },
        
        // 安全地查询选择器
        querySelector: function(selector, parent) {
            try {
                const context = parent || document;
                return context.querySelector(selector);
            } catch (e) {
                console.error(`querySelector操作失败 (${selector}):`, e.message);
                return null;
            }
        },
        
        // 安全地查询所有选择器
        querySelectorAll: function(selector, parent) {
            try {
                const context = parent || document;
                return context.querySelectorAll(selector);
            } catch (e) {
                console.error(`querySelectorAll操作失败 (${selector}):`, e.message);
                return [];
            }
        }
    };
    
    // 修复appendChild方法
    function fixAppendChild() {
        console.log('修复appendChild方法');
        
        // 对有问题的DOM操作进行修复
        const graphContainers = safeDom.querySelectorAll('.character-relationship-graph, .climax-chart-container');
        
        graphContainers.forEach(function(container) {
            if (!container) return;
            
            // 添加错误处理包装层
            const errorWrapper = safeDom.createElement('div');
            if (!errorWrapper) return;
            
            errorWrapper.className = 'error-wrapper';
            safeDom.setTextContent(errorWrapper, '图表加载中...');
            
            // 清空容器并添加包装层
            try {
                while (container.firstChild) {
                    container.removeChild(container.firstChild);
                }
                safeDom.appendChild(container, errorWrapper);
            } catch (e) {
                console.error('修复appendChild过程中出错:', e.message);
            }
        });
    }
    
    // 修复replaceChild方法
    function fixReplaceChild() {
        console.log('修复replaceChild方法');
        
        // 找到所有可能使用replaceChild的元素
        const tableContainers = safeDom.querySelectorAll('.character-table-container, .climax-data-container');
        
        tableContainers.forEach(function(container) {
            if (!container) return;
            
            // 为容器添加更新方法，通过清空和添加的方式替代replaceChild
            container.safeUpdate = function(newContent) {
                try {
                    // 清空容器
                    while (this.firstChild) {
                        this.removeChild(this.firstChild);
                    }
                    
                    // 如果newContent是字符串，创建文本节点
                    if (typeof newContent === 'string') {
                        const textNode = document.createTextNode(newContent);
                        safeDom.appendChild(this, textNode);
                    } 
                    // 如果newContent是DOM节点，直接添加
                    else if (newContent instanceof Node) {
                        safeDom.appendChild(this, newContent);
                    }
                    // 如果是其他类型，转换为字符串
                    else {
                        const textNode = document.createTextNode(String(newContent));
                        safeDom.appendChild(this, textNode);
                    }
                } catch (e) {
                    console.error('safeUpdate操作失败:', e.message);
                }
            };
        });
    }
    
    // 修复特定维度的分析页面
    function fixSpecificDimensions() {
        console.log('修复特定维度的分析页面');
        
        // 获取当前URL
        const currentUrl = window.location.pathname;
        
        // 检查是否是特定维度的分析页面
        const isCharacterRelationships = currentUrl.includes('/analysis/character_relationships');
        const isClimaxPacing = currentUrl.includes('/analysis/climax_pacing');
        
        // 找到折叠按钮
        const collapseButtons = safeDom.querySelectorAll('[data-bs-toggle="collapse"], [data-toggle="collapse"]');
        console.log(`找到 ${collapseButtons.length} 个折叠按钮`);
        
        // 修复高潮节奏图表
        if (isClimaxPacing) {
            const climaxChartContainer = safeDom.querySelector('#climaxChart');
            if (climaxChartContainer) {
                console.log('找到高潮节奏图表容器，尝试修复');
                
                // 修复图表容器
                try {
                    // 确保Canvas有正确的宽度和高度
                    climaxChartContainer.width = climaxChartContainer.parentElement ? 
                        climaxChartContainer.parentElement.clientWidth : 800;
                    climaxChartContainer.height = 400;
                    
                    // 确保图表容器可见
                    if (climaxChartContainer.parentElement) {
                        climaxChartContainer.parentElement.style.display = 'block';
                        climaxChartContainer.parentElement.style.height = 'auto';
                    }
                } catch (e) {
                    console.error('修复高潮节奏图表容器时出错:', e.message);
                }
            } else {
                console.log('未找到高潮节奏图表容器，跳过修复');
            }
        }
        
        // 修复人物关系图
        if (isCharacterRelationships) {
            const relationshipGraphContainer = safeDom.querySelector('#characterRelationshipGraph');
            if (relationshipGraphContainer) {
                console.log('找到人物关系图容器，尝试修复');
                
                // 修复图容器
                try {
                    // 确保容器可见
                    relationshipGraphContainer.style.display = 'block';
                    relationshipGraphContainer.style.height = '500px';
                    relationshipGraphContainer.style.width = '100%';
                    
                    // 确保内部SVG元素可见
                    const svgElement = safeDom.querySelector('svg', relationshipGraphContainer);
                    if (svgElement) {
                        svgElement.style.display = 'block';
                        svgElement.style.width = '100%';
                        svgElement.style.height = '100%';
                    }
                } catch (e) {
                    console.error('修复人物关系图容器时出错:', e.message);
                }
            } else {
                console.log('未找到人物关系图容器，跳过修复');
            }
        }
    }
    
    // 主函数
    function main() {
        // 修复appendChild方法
        fixAppendChild();
        
        // 修复replaceChild方法
        fixReplaceChild();
        
        // 修复特定维度的分析页面
        fixSpecificDimensions();
        
        // 处理图表初始化问题
        if (typeof Chart !== 'undefined' && window.chartLoader) {
            // 确保chart.js正确加载
            window.chartLoader.ensureLoaded(function() {
                console.log('Chart.js加载完成，运行高潮节奏图表初始化');
                // 稍微延迟执行以确保DOM已完全加载
                setTimeout(function() {
                    fixSpecificDimensions();
                }, 500);
            });
        }
    }
    
    // 检查DOM是否已加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', main);
    } else {
        main();
    }
    
    // 在页面完全加载后再次尝试运行
    window.addEventListener('load', function() {
        console.log('开始执行高潮节奏和人物关系分析专用修复');
        main();
    });
})();
