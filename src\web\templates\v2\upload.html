{% extends "v2/base.html" %}

{% block title %}上传小说 - 九猫小说分析系统v2.0{% endblock %}

{% block extra_css %}
<style>
    .upload-header {
        background-color: var(--primary-light);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    .upload-option {
        cursor: pointer;
        padding: 1.5rem;
        border-radius: 0.5rem;
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
    }
    .upload-option:hover {
        border-color: var(--primary-color);
        transform: translateY(-5px);
    }
    .upload-option.active {
        border-color: var(--primary-color);
        background-color: rgba(230, 180, 34, 0.05);
    }
    .upload-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }
    .dimension-section {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-color);
    }
    .dimension-card {
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .dimension-card:hover {
        transform: translateY(-5px);
    }
    .dimension-card.selected {
        border-color: var(--primary-color);
        background-color: rgba(230, 180, 34, 0.05);
    }
    .dimension-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }
</style>
{% endblock %}

{% block content %}
<!-- 上传标题 -->
<div class="upload-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">上传小说</h1>
            <p class="mb-0">上传您的小说文本，选择分析维度，获取专业分析结果</p>
        </div>
        <div class="col-md-4 text-md-end">
            <i class="fas fa-upload fa-3x text-primary"></i>
        </div>
    </div>
</div>

<!-- 错误提示 -->
{% if error %}
    <div class="alert alert-danger mb-4">
        <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    </div>
{% endif %}

<!-- 上传表单 -->
<form method="post" enctype="multipart/form-data">
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title mb-0"><i class="fas fa-book me-2"></i>小说信息</h3>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="title" class="form-label">小说标题 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="title" name="title" required>
                </div>
                <div class="col-md-6">
                    <label for="author" class="form-label">作者</label>
                    <input type="text" class="form-control" id="author" name="author">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="upload-option active" id="fileUploadOption">
                        <div class="text-center">
                            <i class="fas fa-file-upload upload-icon"></i>
                            <h4>上传文件</h4>
                            <p class="text-muted">支持TXT文本文件</p>
                            <input type="file" class="form-control" id="file" name="file" accept=".txt">
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="upload-option" id="textUploadOption">
                        <div class="text-center">
                            <i class="fas fa-keyboard upload-icon"></i>
                            <h4>粘贴文本</h4>
                            <p class="text-muted">直接粘贴小说内容</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-3" id="textAreaContainer" style="display: none;">
                <label for="content" class="form-label">小说内容</label>
                <textarea class="form-control" id="content" name="content" rows="10" placeholder="在此粘贴小说内容..."></textarea>
            </div>
            
            <input type="hidden" name="uploadType" id="uploadType" value="file">
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title mb-0"><i class="fas fa-cubes me-2"></i>分析设置</h3>
        </div>
        <div class="card-body">
            <div class="form-check form-switch mb-4">
                <input class="form-check-input" type="checkbox" id="autoAnalyze" name="autoAnalyze" checked>
                <label class="form-check-label" for="autoAnalyze">上传后自动开始分析</label>
            </div>
            
            <div class="dimension-section" id="dimensionSection">
                <h4 class="mb-3">选择分析维度</h4>
                <div class="mb-3">
                    <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllBtn">全选</button>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="deselectAllBtn">取消全选</button>
                </div>
                <div class="row">
                    {% for dimension in dimensions %}
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card dimension-card h-100" data-dimension="{{ dimension.key }}">
                                <div class="card-body text-center">
                                    <i class="{{ dimension.icon }} dimension-icon"></i>
                                    <h5 class="card-title">{{ dimension.name }}</h5>
                                    <div class="form-check">
                                        <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="{{ dimension.key }}" id="dimension{{ loop.index }}">
                                        <label class="form-check-label" for="dimension{{ loop.index }}">
                                            选择此维度
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
        <a href="{{ url_for('v2.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-times me-2"></i>取消
        </a>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-upload me-2"></i>上传并分析
        </button>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 上传选项切换
        const fileUploadOption = document.getElementById('fileUploadOption');
        const textUploadOption = document.getElementById('textUploadOption');
        const fileInput = document.getElementById('file');
        const textAreaContainer = document.getElementById('textAreaContainer');
        const uploadTypeInput = document.getElementById('uploadType');
        
        fileUploadOption.addEventListener('click', function() {
            fileUploadOption.classList.add('active');
            textUploadOption.classList.remove('active');
            textAreaContainer.style.display = 'none';
            uploadTypeInput.value = 'file';
        });
        
        textUploadOption.addEventListener('click', function() {
            textUploadOption.classList.add('active');
            fileUploadOption.classList.remove('active');
            textAreaContainer.style.display = 'block';
            uploadTypeInput.value = 'text';
        });
        
        // 维度卡片选择
        const dimensionCards = document.querySelectorAll('.dimension-card');
        const dimensionCheckboxes = document.querySelectorAll('.dimension-checkbox');
        
        dimensionCards.forEach(card => {
            card.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');
                const checkbox = document.querySelector(`.dimension-checkbox[value="${dimension}"]`);
                
                checkbox.checked = !checkbox.checked;
                
                if (checkbox.checked) {
                    this.classList.add('selected');
                } else {
                    this.classList.remove('selected');
                }
            });
        });
        
        dimensionCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const dimension = this.value;
                const card = document.querySelector(`.dimension-card[data-dimension="${dimension}"]`);
                
                if (this.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
        });
        
        // 全选/取消全选按钮
        document.getElementById('selectAllBtn').addEventListener('click', function() {
            dimensionCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                const dimension = checkbox.value;
                const card = document.querySelector(`.dimension-card[data-dimension="${dimension}"]`);
                card.classList.add('selected');
            });
        });
        
        document.getElementById('deselectAllBtn').addEventListener('click', function() {
            dimensionCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                const dimension = checkbox.value;
                const card = document.querySelector(`.dimension-card[data-dimension="${dimension}"]`);
                card.classList.remove('selected');
            });
        });
        
        // 自动分析切换
        const autoAnalyzeCheckbox = document.getElementById('autoAnalyze');
        const dimensionSection = document.getElementById('dimensionSection');
        
        autoAnalyzeCheckbox.addEventListener('change', function() {
            if (this.checked) {
                dimensionSection.style.display = 'block';
            } else {
                dimensionSection.style.display = 'none';
            }
        });
    });
</script>
{% endblock %}
