{% extends "v3/base.html" %}

{% block title %}{{ novel.title }} - 内容仓库 - 九猫小说分析写作系统v3.0{% endblock %}

{% block extra_css %}
<style>
    .content-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px var(--shadow-color);
        position: relative;
        overflow: hidden;
    }

    .content-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .content-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    }

    .content-meta {
        position: relative;
        z-index: 1;
        opacity: 0.9;
    }

    .content-stats {
        display: flex;
        gap: 2rem;
        margin-top: 1rem;
    }

    .content-stat {
        display: flex;
        align-items: center;
    }

    .content-stat-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        margin-right: 0.75rem;
    }

    .tab-content {
        padding: 1.5rem;
    }

    .chapter-list {
        max-height: 600px;
        overflow-y: auto;
    }

    .chapter-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .chapter-item:hover {
        background-color: var(--shadow-color);
    }

    .chapter-number {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: var(--primary-light);
        color: var(--dark-color);
        font-weight: 600;
        margin-right: 1rem;
    }

    .chapter-title {
        flex: 1;
    }

    .chapter-word-count {
        margin-right: 1rem;
        color: var(--primary-color);
        font-weight: 600;
    }

    .content-text {
        white-space: pre-wrap;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        line-height: 1.8;
        padding: 1rem;
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
    }

    .original-info {
        background-color: rgba(var(--primary-rgb), 0.1);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .word-count-comparison {
        background-color: rgba(var(--primary-rgb), 0.05);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .comparison-table th, .comparison-table td {
        vertical-align: middle;
    }
</style>
{% endblock %}

{% block content %}
<!-- 内容头部信息 -->
<div class="content-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">{{ novel.title }}</h1>
            <div class="content-meta">
                <p class="lead mb-2">
                    <i class="fas fa-robot me-2"></i>AI生成内容
                    {% if novel.author %}
                    <span class="ms-3"><i class="fas fa-user me-2"></i>{{ novel.author }}</span>
                    {% endif %}
                </p>
                <div class="content-stats">
                    <div class="content-stat">
                        <div class="content-stat-icon">
                            <i class="fas fa-font"></i>
                        </div>
                        <div>
                            <div>{{ novel.word_count|format_number }}</div>
                            <small>总字数</small>
                        </div>
                    </div>
                    <div class="content-stat">
                        <div class="content-stat-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <div>
                            <div>{{ chapters|length }}</div>
                            <small>章节数</small>
                        </div>
                    </div>
                    <div class="content-stat">
                        <div class="content-stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div>
                            <div>{{ novel.created_at.strftime('%Y-%m-%d') }}</div>
                            <small>生成日期</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3.content_repository_page') }}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-1"></i>返回内容仓库
                </a>
                <button class="btn btn-light" id="downloadContentBtn">
                    <i class="fas fa-download me-1"></i>下载内容
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-12">
        <ul class="nav nav-tabs" id="contentTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                    <i class="fas fa-info-circle me-1"></i><strong>概览</strong>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">
                    <i class="fas fa-book-open me-1"></i><strong>章节列表</strong>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="false">
                    <i class="fas fa-file-alt me-1"></i><strong>完整内容</strong>
                </button>
            </li>
        </ul>
        <div class="tab-content" id="contentTabContent">
            <!-- 概览标签页 -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card shadow-sm mb-4">
                            <div class="card-header">
                                <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>内容信息</h3>
                            </div>
                            <div class="card-body">
                                {% if novel.novel_metadata and novel.novel_metadata.original_title %}
                                <div class="original-info mb-3">
                                    <h5><i class="fas fa-book me-2"></i>基于原文</h5>
                                    <p><strong>原文标题：</strong> {{ novel.novel_metadata.original_title }}</p>
                                    {% if novel.novel_metadata.original_author %}
                                    <p><strong>原文作者：</strong> {{ novel.novel_metadata.original_author }}</p>
                                    {% endif %}
                                    {% if novel.novel_metadata.original_id %}
                                    <p><strong>原文ID：</strong> {{ novel.novel_metadata.original_id }}</p>
                                    {% endif %}
                                </div>
                                {% endif %}
                                <div class="mb-3">
                                    <h5>内容摘要</h5>
                                    <p>{{ novel.content[:500] + '...' if novel.content|length > 500 else novel.content }}</p>
                                </div>
                                <div class="mb-3">
                                    <h5>生成信息</h5>
                                    <p><strong>生成时间：</strong> {{ novel.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                                    <p><strong>总字数：</strong> {{ novel.word_count }} 字</p>
                                    <p><strong>章节数：</strong> {{ chapters|length }} 章</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        {% if chapter_word_count_comparison %}
                        <div class="card shadow-sm mb-4">
                            <div class="card-header">
                                <h3 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>章节字数对比</h3>
                            </div>
                            <div class="card-body">
                                <div class="word-count-comparison">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover comparison-table">
                                            <thead>
                                                <tr>
                                                    <th>章节</th>
                                                    <th>原文字数</th>
                                                    <th>生成字数</th>
                                                    <th>差异</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for chapter in chapter_word_count_comparison %}
                                                <tr>
                                                    <td>{{ chapter.chapter_title }}</td>
                                                    <td>{{ chapter.original_word_count }} 字</td>
                                                    <td>{{ chapter.generated_word_count }} 字</td>
                                                    <td>{{ chapter.difference_percent }}%</td>
                                                    <td>
                                                        {% if chapter.difference_percent > 30 %}
                                                        <span class="badge bg-danger"><i class="fas fa-exclamation-triangle me-1"></i>差异过大</span>
                                                        {% elif chapter.difference_percent > 15 %}
                                                        <span class="badge bg-warning"><i class="fas fa-exclamation-circle me-1"></i>差异较大</span>
                                                        {% else %}
                                                        <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>良好</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 章节列表标签页 -->
            <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0"><i class="fas fa-book-open me-2"></i>章节列表</h3>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="chapter-list">
                            {% for chapter in chapters %}
                            <div class="chapter-item">
                                <div class="chapter-number">{{ chapter.chapter_number }}</div>
                                <div class="chapter-title">{{ chapter.title }}</div>
                                <div class="chapter-word-count">
                                    {{ chapter.word_count }} 字
                                </div>
                                <button class="btn btn-sm btn-primary view-chapter-btn" data-chapter-id="{{ chapter.id }}" data-chapter-title="{{ chapter.title }}" data-chapter-content="{{ chapter.content }}">
                                    <i class="fas fa-eye me-1"></i>查看
                                </button>
                                <button class="btn btn-sm btn-outline-success copy-chapter-btn ms-1" data-chapter-content="{{ chapter.content }}" title="复制章节内容">
                                    <i class="fas fa-copy me-1"></i>复制
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 完整内容标签页 -->
            <div class="tab-pane fade" id="content" role="tabpanel" aria-labelledby="content-tab">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h3 class="card-title mb-0"><i class="fas fa-file-alt me-2"></i>完整内容</h3>
                    </div>
                    <div class="card-body">
                        <div class="content-text">{{ novel.content }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 章节查看模态框 -->
<div class="modal fade" id="chapterViewModal" tabindex="-1" aria-labelledby="chapterViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chapterViewModalLabel">章节内容</h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-success" id="copyModalChapterBtn" title="复制章节内容">
                        <i class="fas fa-copy me-1"></i>复制
                    </button>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body">
                <h4 id="modalChapterTitle" class="mb-3"></h4>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>纯净写作内容</strong> - 已自动过滤AI回复前缀和分析说明，只保留写作结果
                </div>
                <div id="modalChapterContent" class="border p-3 rounded bg-light" style="max-height: 500px; overflow-y: auto; white-space: pre-wrap; font-family: 'Microsoft YaHei', sans-serif; line-height: 1.8;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-outline-success" id="copyModalChapterBtn2">
                    <i class="fas fa-copy me-1"></i>复制内容
                </button>
                <button type="button" class="btn btn-primary" id="downloadChapterBtn">
                    <i class="fas fa-download me-1"></i>下载章节
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 内容过滤函数 - 移除AI回复前缀和分析说明
        function cleanWritingContent(content) {
            if (!content) return '';

            // 移除AI回复的常见前缀
            const aiPrefixPatterns = [
                /^好的，我将.*?以下是.*?：\s*/,
                /^我将严格按照您的要求.*?：\s*/,
                /^按照您的要求.*?：\s*/,
                /^以下是.*?内容.*?：\s*/,
                /^现在我将.*?：\s*/,
                /^\（经分析.*?\）\s*/,
                /^\（.*?经分析.*?\）\s*/,
                /^\（.*?原文已达到.*?\）\s*/,
                /^\（.*?强行扩展.*?\）\s*/,
                /^\（.*?以下优化.*?\）\s*/,
                /^\（[^）]*分析[^）]*\）\s*/,
                /^\（[^）]*优化[^）]*\）\s*/
            ];

            let cleanedContent = content;

            // 应用所有过滤规则
            aiPrefixPatterns.forEach(pattern => {
                cleanedContent = cleanedContent.replace(pattern, '');
            });

            // 将【】转换为""
            cleanedContent = cleanedContent.replace(/【/g, '"').replace(/】/g, '"');

            // 移除多余的空行
            cleanedContent = cleanedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

            return cleanedContent.trim();
        }

        // 复制到剪贴板函数
        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                // 使用现代API
                navigator.clipboard.writeText(text).then(() => {
                    showToast('内容已复制到剪贴板', 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                // 降级方案
                fallbackCopyTextToClipboard(text);
            }
        }

        // 降级复制方案
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showToast('内容已复制到剪贴板', 'success');
                } else {
                    showToast('复制失败，请手动复制', 'error');
                }
            } catch (err) {
                console.error('降级复制失败:', err);
                showToast('复制失败，请手动复制', 'error');
            }

            document.body.removeChild(textArea);
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 创建提示元素
            const toast = $(`
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `);

            $('body').append(toast);
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();

            // 3秒后自动移除
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // 查看章节按钮点击事件
        $('.view-chapter-btn').click(function() {
            const chapterTitle = $(this).data('chapter-title');
            const chapterContent = $(this).data('chapter-content');

            // 过滤内容
            const cleanedContent = cleanWritingContent(chapterContent);

            $('#modalChapterTitle').text(chapterTitle);
            $('#modalChapterContent').text(cleanedContent);

            const chapterViewModal = new bootstrap.Modal(document.getElementById('chapterViewModal'));
            chapterViewModal.show();
        });

        // 章节列表复制按钮点击事件
        $('.copy-chapter-btn').click(function() {
            const chapterContent = $(this).data('chapter-content');
            const cleanedContent = cleanWritingContent(chapterContent);
            copyToClipboard(cleanedContent);
        });

        // 模态框复制按钮点击事件
        $('#copyModalChapterBtn, #copyModalChapterBtn2').click(function() {
            const content = $('#modalChapterContent').text();
            copyToClipboard(content);
        });

        // 下载内容按钮点击事件
        $('#downloadContentBtn').click(function() {
            const title = '{{ novel.title }}';
            const content = '{{ novel.content|replace("\n", "\\n")|replace("'", "\\'") }}';

            // 创建Blob对象
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });

            // 创建下载链接
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${title}.txt`;

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });

        // 下载章节按钮点击事件
        $('#downloadChapterBtn').click(function() {
            const title = $('#modalChapterTitle').text();
            const content = $('#modalChapterContent').text();

            // 创建Blob对象
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });

            // 创建下载链接
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${title}.txt`;

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
    });
</script>
{% endblock %}