/**
 * 修复参考蓝本页面刷新问题的JavaScript代码
 * 
 * 问题描述:
 * 1. 设置小说为参考蓝本后，前端不刷新显示最新状态
 * 2. 参考蓝本标记（橙色底色标识）不显示
 * 
 * 这个脚本应该手动插入到浏览器控制台运行，或添加到前端代码中
 */

// 立即执行函数
(function() {
    // 输出调试信息
    console.log('正在执行参考蓝本刷新修复脚本...');
    
    // 获取当前URL
    const currentUrl = window.location.href;
    console.log('当前页面URL:', currentUrl);
    
    // 判断是否在参考蓝本页面
    const isReferenceTemplatePage = currentUrl.includes('/reference-templates') || 
                                  currentUrl.includes('/novels');
    
    if (isReferenceTemplatePage) {
        console.log('检测到参考蓝本相关页面，开始修复刷新问题');
        
        // 强制清除页面缓存
        function forceRefresh() {
            console.log('强制刷新页面，清除缓存...');
            
            // 添加时间戳参数，防止浏览器缓存
            const timestamp = new Date().getTime();
            const url = new URL(window.location.href);
            
            // 设置时间戳参数
            url.searchParams.set('_', timestamp);
            
            // 替换当前URL，不创建新的历史记录
            window.history.replaceState({}, document.title, url.toString());
            
            // 强制刷新页面
            location.reload(true);
        }
        
        // 修复小说卡片上的参考蓝本标记
        function fixTemplateMarkers() {
            console.log('修复参考蓝本标记显示...');
            
            // 检查是否有元数据标记为参考蓝本的小说
            const novelCards = document.querySelectorAll('.novel-card, .template-card');
            console.log(`找到 ${novelCards.length} 个小说/模板卡片`);
            
            novelCards.forEach(card => {
                // 获取小说ID
                const novelId = card.getAttribute('data-novel-id') || 
                               card.querySelector('a[href*="/novel/"]')?.href.split('/novel/')[1]?.split('/')[0] ||
                               card.querySelector('a[href*="/v3/novel/"]')?.href.split('/v3/novel/')[1]?.split('/')[0];
                
                if (novelId) {
                    console.log(`检查小说ID: ${novelId} 是否为参考蓝本`);
                    
                    // 调用API检查是否为参考蓝本
                    fetch(`/api/novel/${novelId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.novel.is_template) {
                                console.log(`小说ID: ${novelId} 是参考蓝本，添加标记`);
                                
                                // 检查是否已有标记
                                if (!card.querySelector('.template-badge')) {
                                    // 创建标记
                                    const badge = document.createElement('span');
                                    badge.className = 'badge bg-warning template-badge';
                                    badge.textContent = '参考蓝本';
                                    badge.style.position = 'absolute';
                                    badge.style.top = '10px';
                                    badge.style.right = '10px';
                                    badge.style.zIndex = '10';
                                    
                                    // 添加到卡片
                                    card.style.position = 'relative';
                                    card.prepend(badge);
                                    console.log('已添加参考蓝本标记');
                                }
                            }
                        })
                        .catch(error => {
                            console.error(`检查小说ID: ${novelId} 时出错:`, error);
                        });
                }
            });
        }
        
        // 修复参考蓝本页面的模板列表
        function fixTemplatesList() {
            console.log('修复参考蓝本列表显示...');
            
            // 刷新参考蓝本列表
            fetch('/api/reference_templates')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.templates && data.templates.length > 0) {
                        console.log(`API返回 ${data.templates.length} 个参考蓝本`);
                        
                        // 检查页面是否有"暂无参考蓝本"的提示
                        const emptyNotice = document.querySelector('.card-body.text-center.py-5');
                        if (emptyNotice && emptyNotice.textContent.includes('暂无参考蓝本')) {
                            console.log('检测到"暂无参考蓝本"提示，但API返回有参考蓝本，需要刷新页面');
                            forceRefresh();
                        }
                    }
                })
                .catch(error => {
                    console.error('获取参考蓝本列表时出错:', error);
                });
        }
        
        // 执行修复
        fixTemplateMarkers();
        fixTemplatesList();
        
        // 设置一个按钮，允许用户手动刷新
        const refreshButton = document.createElement('button');
        refreshButton.className = 'btn btn-warning position-fixed';
        refreshButton.style.bottom = '20px';
        refreshButton.style.right = '20px';
        refreshButton.style.zIndex = '9999';
        refreshButton.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新参考蓝本';
        refreshButton.onclick = forceRefresh;
        
        document.body.appendChild(refreshButton);
        console.log('已添加手动刷新按钮');
    } else {
        console.log('当前不是参考蓝本相关页面，无需修复');
    }
    
    console.log('参考蓝本刷新修复脚本执行完毕');
})(); 