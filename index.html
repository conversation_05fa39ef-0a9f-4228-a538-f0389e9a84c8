<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫系统修复测试</title>

    <!-- 按钮文字最高优先级修复内联样式 -->
    <style id="button-text-supreme-fix-inline">
        /* 全局按钮修复 - 使用更高优先级选择器 */
        .btn, button, input[type="button"], input[type="submit"], input[type="reset"], a.btn,
        .btn[class*="btn-"], button[class*="btn-"], a[class*="btn-"] {
            color: #333333 !important;
            text-shadow: none !important;
            -webkit-text-fill-color: initial !important;
            opacity: 1 !important;
            visibility: visible !important;
            text-indent: 0 !important;
            font-size: 14px !important;
            font-weight: 400 !important;
            letter-spacing: normal !important;
            background-image: none !important;
            background-clip: padding-box !important;
            -webkit-background-clip: padding-box !important;
        }

        /* 特定按钮类型修复 - 使用更高优先级选择器 */
        .btn-primary, button.btn-primary, a.btn-primary,
        .btn.btn-primary, button.btn.btn-primary, a.btn.btn-primary {
            color: #ffffff !important;
            background-color: #007bff !important;
            border-color: #007bff !important;
        }

        .btn-secondary, button.btn-secondary, a.btn-secondary,
        .btn.btn-secondary, button.btn.btn-secondary, a.btn.btn-secondary {
            color: #ffffff !important;
            background-color: #6c757d !important;
            border-color: #6c757d !important;
        }

        .btn-success, button.btn-success, a.btn-success,
        .btn.btn-success, button.btn.btn-success, a.btn.btn-success {
            color: #ffffff !important;
            background-color: #28a745 !important;
            border-color: #28a745 !important;
        }

        .btn-danger, button.btn-danger, a.btn-danger,
        .btn.btn-danger, button.btn.btn-danger, a.btn.btn-danger {
            color: #ffffff !important;
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
        }

        .btn-warning, button.btn-warning, a.btn-warning,
        .btn.btn-warning, button.btn.btn-warning, a.btn.btn-warning {
            color: #212529 !important;
            background-color: #ffc107 !important;
            border-color: #ffc107 !important;
        }

        .btn-info, button.btn-info, a.btn-info,
        .btn.btn-info, button.btn.btn-info, a.btn.btn-info {
            color: #ffffff !important;
            background-color: #17a2b8 !important;
            border-color: #17a2b8 !important;
        }

        .btn-light, button.btn-light, a.btn-light,
        .btn.btn-light, button.btn.btn-light, a.btn.btn-light {
            color: #212529 !important;
            background-color: #f8f9fa !important;
            border-color: #f8f9fa !important;
        }

        .btn-dark, button.btn-dark, a.btn-dark,
        .btn.btn-dark, button.btn.btn-dark, a.btn.btn-dark {
            color: #ffffff !important;
            background-color: #343a40 !important;
            border-color: #343a40 !important;
        }

        /* 确保按钮内的所有元素可见 */
        .btn *, button *, a.btn *,
        .btn[class*="btn-"] *, button[class*="btn-"] *, a[class*="btn-"] * {
            color: inherit !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
    </style>

    <!-- 修复脚本加载器 - 必须最先加载 -->
    <script>
        /**
         * 九猫 - 内联修复引导加载器
         * 在所有其他脚本之前执行，用于引导加载其他修复脚本
         */
        (function() {
            // 预定义基本的修复，防止早期错误
            window.result = window.result || {
                status: 'ok',
                message: '预防性定义的result对象',
                data: {},
                success: true
            };

            // 安全加载脚本的方法
            function safeLoadScript(url, async) {
                try {
                    const script = document.createElement('script');
                    script.src = url;
                    script.async = !!async;
                    document.head.appendChild(script);
                    console.log('加载脚本:', url);
                    return script;
                } catch(e) {
                    console.error('加载脚本时出错:', e);
                    return null;
                }
            }

            // 预先修复document.createElement，避免textContent设置错误
            const originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
                const element = originalCreateElement.call(document, tagName);

                // 特别处理脚本元素
                if (typeof tagName === 'string' && tagName.toLowerCase() === 'script') {
                    try {
                        // 预先安全设置属性，防止错误
                        element._safeSetAttribute = function(name, value) {
                            try {
                                element.setAttribute(name, value);
                                return true;
                            } catch(e) {
                                console.warn('设置属性时出错:', e);
                                return false;
                            }
                        };
                    } catch(e) {
                        console.warn('修复脚本元素时出错:', e);
                    }
                }

                return element;
            };

            // 预防全局错误
            window.onerror = function(message, source, lineno, colno, error) {
                console.error(`引导加载器错误: ${message} at ${source}:${lineno}:${colno}`);
                return true; // 阻止错误传播
            };

            // 在页面加载完成后加载修复脚本
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM加载完成，开始加载修复脚本');

                // 首先加载紧急资源加载器和createElement修复
                safeLoadScript('/src/web/static/js/create-element-fix.js', false);
                safeLoadScript('/src/web/static/js/emergency-resource-loader.js', false);

                // 延迟加载其他修复脚本，确保基础修复已加载
                setTimeout(function() {
                    safeLoadScript('/src/web/static/js/result-not-defined-fix.js', false);
                    safeLoadScript('/src/web/static/js/jquery-loader.js', false);
                    safeLoadScript('/src/web/static/js/dom-operation-fix.js', false);
                    safeLoadScript('/src/web/static/js/chart-loader.js', false);
                    safeLoadScript('/src/web/static/js/main-fix.js', false);
                    safeLoadScript('/src/web/static/js/supreme-fixer.js', false);
                }, 100);
            });

            console.log('修复引导加载器已初始化');
        })();
    </script>

    <!-- 内联应急CSS，确保基本样式即使在CSS加载失败时也能显示 -->
    <style>
        /* 基础应急样式 */
        body { font-family: system-ui, sans-serif; line-height: 1.5; margin: 0; padding: 0; }
        .container { width: 100%; max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        .navbar { display: flex; align-items: center; padding: 8px 16px; background-color: #343a40; color: white; }
        .navbar-brand { font-size: 1.25rem; color: white; text-decoration: none; }
        .card { border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin-bottom: 15px; }
        .btn { display: inline-block; padding: 8px 16px; background: #f8f9fa; border: 1px solid #dee2e6; cursor: pointer; border-radius: 4px; color: #333333 !important; opacity: 1 !important; visibility: visible !important; }
        .btn:hover { background: #e9ecef; }
        .btn-primary { color: #fff !important; background-color: #007bff !important; border-color: #007bff !important; }
        .row { display: flex; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
        .col, .col-12, .col-md-6 { position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
        .col-12 { flex: 0 0 100%; max-width: 100%; }
        @media (min-width: 768px) { .col-md-6 { flex: 0 0 50%; max-width: 50%; } }
        .card-body { flex: 1 1 auto; min-height: 1px; padding: 1.25rem; }
        .card-title { margin-bottom: 0.75rem; font-size: 1.25rem; }
        .collapse { display: none; }
        .collapse.show { display: block; }
    </style>

    <!-- 预加载关键脚本 -->
    <link rel="preload" href="/src/web/static/js/create-element-fix.js" as="script">
    <link rel="preload" href="/src/web/static/js/emergency-resource-loader.js" as="script">
    <link rel="preload" href="/src/web/static/js/button-text-supreme-fix.js" as="script">

    <!-- 引用Bootstrap CSS (可能会404，但会被我们的修复脚本处理) -->
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">

    <!-- 按钮最高优先级修复脚本 -->
    <script src="/src/web/static/js/button-text-supreme-fix.js"></script>

    <!-- 错误处理修复脚本 -->
    <script src="unified-error-handler.js"></script>
    <script src="console-logger-fix.js"></script>
    <script src="global-error-handler-fix.js"></script>
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <a class="navbar-brand" href="#">九猫系统修复测试</a>
        </div>
    </nav>

    <div class="container" style="margin-top: 20px;">
        <div class="row">
            <div class="col-12">
                <h1>修复脚本测试页面</h1>
                <p>这个页面用于测试各种修复脚本的功能。请打开控制台查看详细信息。</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">未定义变量测试</h5>
                        <button class="btn btn-primary" id="test-undefined">测试未定义变量</button>
                        <div class="result mt-2"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">404资源测试</h5>
                        <button class="btn btn-primary" id="test-404">加载不存在的资源</button>
                        <div class="result mt-2"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">DOM操作测试</h5>
                        <button class="btn btn-primary" id="test-dom">测试DOM操作</button>
                        <div id="dom-container" class="mt-2"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">图表测试</h5>
                        <button class="btn btn-primary" id="test-chart">测试图表</button>
                        <div class="mt-2">
                            <canvas id="myChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">折叠组件测试</h5>
                        <button class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#collapseExample">
                            切换折叠内容
                        </button>
                        <div class="collapse mt-2" id="collapseExample">
                            <div class="card card-body">
                                这是一些折叠内容，用于测试Bootstrap折叠组件。
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">API请求测试</h5>
                        <button class="btn btn-primary" id="test-api">测试API请求</button>
                        <div class="result mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 内联脚本，故意使用未声明的result变量 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 测试未定义变量
            document.getElementById('test-undefined').addEventListener('click', function() {
                try {
                    // 尝试使用未定义的变量
                    const data = result.data;
                    document.querySelector('#test-undefined + .result').textContent =
                        '成功访问result变量: ' + JSON.stringify(data);
                } catch(e) {
                    document.querySelector('#test-undefined + .result').textContent =
                        '错误: ' + e.message;
                }
            });

            // 测试404资源
            document.getElementById('test-404').addEventListener('click', function() {
                const script = document.createElement('script');
                script.src = '/non-existent-resource.js';
                script.onload = function() {
                    document.querySelector('#test-404 + .result').textContent =
                        '资源加载成功（应该不会发生）';
                };
                script.onerror = function() {
                    document.querySelector('#test-404 + .result').textContent =
                        '资源加载失败，但页面继续运行';
                };
                document.head.appendChild(script);
            });

            // 测试DOM操作
            document.getElementById('test-dom').addEventListener('click', function() {
                const container = document.getElementById('dom-container');

                // 创建元素
                const div = document.createElement('div');
                div.textContent = '这是一个测试元素';
                div.style.padding = '10px';
                div.style.backgroundColor = '#f0f0f0';
                div.style.margin = '5px';

                // 重复添加同一个元素（正常会出错）
                container.appendChild(div);
                container.appendChild(div); // 这里应该会被我们的修复脚本处理

                // 再添加一个新元素
                const div2 = document.createElement('div');
                div2.textContent = '另一个测试元素';
                div2.style.padding = '10px';
                div2.style.backgroundColor = '#e0e0e0';
                div2.style.margin = '5px';

                container.appendChild(div2);
            });

            // 测试图表
            document.getElementById('test-chart').addEventListener('click', function() {
                // 尝试使用Chart.js绘制图表
                if (typeof Chart !== 'undefined') {
                    const ctx = document.getElementById('myChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['红色', '蓝色', '黄色', '绿色', '紫色', '橙色'],
                            datasets: [{
                                label: '颜色数量',
                                data: [12, 19, 3, 5, 2, 3],
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.2)',
                                    'rgba(54, 162, 235, 0.2)',
                                    'rgba(255, 206, 86, 0.2)',
                                    'rgba(75, 192, 192, 0.2)',
                                    'rgba(153, 102, 255, 0.2)',
                                    'rgba(255, 159, 64, 0.2)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)',
                                    'rgba(255, 159, 64, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                } else {
                    const ctx = document.getElementById('myChart').getContext('2d');
                    ctx.font = '20px Arial';
                    ctx.fillText('Chart.js未加载', 10, 50);
                }
            });

            // 测试API请求
            document.getElementById('test-api').addEventListener('click', function() {
                const resultDiv = document.querySelector('#test-api + .result');
                resultDiv.textContent = '请求中...';

                // 尝试请求一个不存在的API
                fetch('/api/non-existent-endpoint')
                    .then(response => response.json())
                    .then(data => {
                        resultDiv.textContent = '请求成功: ' + JSON.stringify(data);
                    })
                    .catch(error => {
                        resultDiv.textContent = '请求失败: ' + error.message;
                    });
            });
        });
    </script>
</body>
</html>