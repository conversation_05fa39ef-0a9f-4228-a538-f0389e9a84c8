{% extends "base.html" %}

{% block title %}{{ novel.title }} - 章节分析汇总{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('novel.novel_list') }}">小说列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('novel.view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('chapter.list_chapters', novel_id=novel.id) }}">章节列表</a></li>
            <li class="breadcrumb-item active" aria-current="page">章节分析汇总</li>
        </ol>
    </nav>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2>{{ novel.title }} - 章节分析汇总</h2>
            <div>
                <button type="button" class="btn btn-primary" id="expandAllBtn">展开全部</button>
                <button type="button" class="btn btn-secondary" id="collapseAllBtn">折叠全部</button>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <p><strong>作者：</strong>{{ novel.author or '未知' }}</p>
                <p><strong>总字数：</strong>{{ novel.word_count }}</p>
                <p><strong>章节数：</strong>{{ chapters|length }}</p>
            </div>

            <!-- 维度选择器 -->
            <div class="mb-4">
                <label for="dimensionFilter" class="form-label">选择分析维度</label>
                <select class="form-select" id="dimensionFilter">
                    <option value="all">所有维度</option>
                    {% for dimension in dimensions %}
                    <option value="{{ dimension.key }}">{{ dimension.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- 章节分组 -->
            <div class="accordion" id="chaptersAccordion">
                {% for group in chapter_groups %}
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading{{ group.start }}">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ group.start }}" aria-expanded="false" aria-controls="collapse{{ group.start }}">
                            第{{ group.start }}章 - 第{{ group.end }}章
                        </button>
                    </h2>
                    <div id="collapse{{ group.start }}" class="accordion-collapse collapse" aria-labelledby="heading{{ group.start }}" data-bs-parent="#chaptersAccordion">
                        <div class="accordion-body">
                            <!-- 章节列表 -->
                            <div class="list-group mb-4">
                                {% for chapter in group.chapters %}
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5>{{ chapter.title or '第' + chapter.chapter_number|string + '章' }}</h5>
                                        <a href="{{ url_for('chapter.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-sm btn-outline-primary">查看详情</a>
                                    </div>

                                    <!-- 章节分析结果 -->
                                    <div class="chapter-analysis-results mt-3">
                                        <div class="accordion" id="chapter{{ chapter.id }}Accordion">
                                            {% for dimension in dimensions %}
                                            {% if dimension.key in chapter.analysis_dimensions %}
                                            <div class="accordion-item dimension-item" data-dimension="{{ dimension.key }}">
                                                <h2 class="accordion-header" id="chapter{{ chapter.id }}{{ dimension.key }}Heading">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter{{ chapter.id }}{{ dimension.key }}Collapse" aria-expanded="false" aria-controls="chapter{{ chapter.id }}{{ dimension.key }}Collapse">
                                                        {{ dimension.name }}
                                                        <span class="badge bg-success ms-2">已分析</span>
                                                    </button>
                                                </h2>
                                                <div id="chapter{{ chapter.id }}{{ dimension.key }}Collapse" class="accordion-collapse collapse" aria-labelledby="chapter{{ chapter.id }}{{ dimension.key }}Heading">
                                                    <div class="accordion-body">
                                                        {% if lazy_load %}
                                                        <div class="analysis-content markdown-content"
                                                             data-lazy-load="true"
                                                             data-chapter-id="{{ chapter.id }}"
                                                             data-dimension="{{ dimension.key }}">
                                                            <div class="text-center">
                                                                <div class="spinner-border text-primary" role="status">
                                                                    <span class="visually-hidden">加载中...</span>
                                                                </div>
                                                                <p class="mt-2">点击展开后加载分析结果...</p>
                                                            </div>
                                                        </div>
                                                        {% else %}
                                                        <div class="analysis-content markdown-content">
                                                            {{ chapter.analysis_results[dimension.key].content|safe }}
                                                        </div>
                                                        {% endif %}
                                                        <div class="mt-2">
                                                            <a href="{{ url_for('chapter.view_chapter_analysis', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" class="btn btn-sm btn-link">查看完整分析</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% else %}
                                            <div class="accordion-item dimension-item" data-dimension="{{ dimension.key }}">
                                                <h2 class="accordion-header" id="chapter{{ chapter.id }}{{ dimension.key }}Heading">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter{{ chapter.id }}{{ dimension.key }}Collapse" aria-expanded="false" aria-controls="chapter{{ chapter.id }}{{ dimension.key }}Collapse">
                                                        {{ dimension.name }}
                                                        <span class="badge bg-secondary ms-2">未分析</span>
                                                    </button>
                                                </h2>
                                                <div id="chapter{{ chapter.id }}{{ dimension.key }}Collapse" class="accordion-collapse collapse" aria-labelledby="chapter{{ chapter.id }}{{ dimension.key }}Heading">
                                                    <div class="accordion-body">
                                                        <div class="alert alert-secondary">
                                                            该维度尚未分析
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 懒加载标志
        let lazyLoadedGroups = {};

        // 展开全部按钮
        document.getElementById('expandAllBtn').addEventListener('click', function() {
            document.querySelectorAll('.accordion-button.collapsed').forEach(button => {
                button.click();
            });
        });

        // 折叠全部按钮
        document.getElementById('collapseAllBtn').addEventListener('click', function() {
            document.querySelectorAll('.accordion-button:not(.collapsed)').forEach(button => {
                button.click();
            });
        });

        // 维度过滤器
        document.getElementById('dimensionFilter').addEventListener('change', function() {
            const selectedDimension = this.value;

            if (selectedDimension === 'all') {
                // 显示所有维度
                document.querySelectorAll('.dimension-item').forEach(item => {
                    item.style.display = 'block';
                });
            } else {
                // 只显示选中的维度
                document.querySelectorAll('.dimension-item').forEach(item => {
                    if (item.dataset.dimension === selectedDimension) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            }
        });

        // 章节组懒加载
        document.querySelectorAll('.accordion-button[data-bs-toggle="collapse"]').forEach(button => {
            button.addEventListener('click', function() {
                const target = this.getAttribute('data-bs-target');
                const groupId = target.replace('#collapse', '');

                // 如果已经加载过，不再重复加载
                if (lazyLoadedGroups[groupId]) {
                    return;
                }

                // 标记为已加载
                lazyLoadedGroups[groupId] = true;

                // 获取章节组容器
                const groupContainer = document.querySelector(target);

                // 如果章节组包含懒加载标记，加载内容
                const lazyLoadElements = groupContainer.querySelectorAll('[data-lazy-load="true"]');
                if (lazyLoadElements.length > 0) {
                    // 显示加载指示器
                    const loadingIndicator = document.createElement('div');
                    loadingIndicator.className = 'text-center my-3';
                    loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div><p class="mt-2">正在加载章节内容...</p>';
                    groupContainer.querySelector('.accordion-body').prepend(loadingIndicator);

                    // 获取章节ID列表
                    const chapterIds = Array.from(lazyLoadElements).map(el => el.dataset.chapterId);

                    // 获取当前选中的维度
                    const selectedDimension = document.getElementById('dimensionFilter').value;

                    // 发送请求获取章节内容
                    fetch(`/api/novel/{{ novel.id }}/chapters/batch_content`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            chapter_ids: chapterIds,
                            dimension: selectedDimension === 'all' ? null : selectedDimension
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 移除加载指示器
                            loadingIndicator.remove();

                            // 更新章节内容
                            for (const chapterId in data.chapters) {
                                const chapterData = data.chapters[chapterId];
                                const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"]`);

                                if (chapterElement) {
                                    // 更新章节内容
                                    chapterElement.innerHTML = chapterData.content;
                                    chapterElement.removeAttribute('data-lazy-load');
                                }
                            }
                        } else {
                            // 显示错误信息
                            loadingIndicator.innerHTML = `<div class="alert alert-danger">加载失败: ${data.error}</div>`;
                        }
                    })
                    .catch(error => {
                        console.error('加载章节内容时出错:', error);
                        // 显示错误信息
                        loadingIndicator.innerHTML = '<div class="alert alert-danger">加载章节内容时出错</div>';
                    });
                }
            });
        });

        // 章节分析结果懒加载
        document.querySelectorAll('.chapter-analysis-results .accordion-button').forEach(button => {
            button.addEventListener('click', function() {
                const target = this.getAttribute('data-bs-target');
                const contentContainer = document.querySelector(target + ' .analysis-content');

                // 如果内容容器有懒加载标记，加载内容
                if (contentContainer && contentContainer.dataset.lazyLoad === 'true') {
                    // 获取章节ID和维度
                    const chapterId = contentContainer.dataset.chapterId;
                    const dimension = contentContainer.dataset.dimension;

                    // 显示加载指示器
                    contentContainer.innerHTML = '<div class="text-center my-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div><p class="mt-2">正在加载分析结果...</p></div>';

                    // 发送请求获取分析结果
                    fetch(`/api/novel/{{ novel.id }}/chapter/${chapterId}/analysis/${dimension}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新内容
                            contentContainer.innerHTML = data.result.content;
                            contentContainer.removeAttribute('data-lazy-load');
                        } else {
                            // 显示错误信息
                            contentContainer.innerHTML = `<div class="alert alert-danger">加载失败: ${data.error}</div>`;
                        }
                    })
                    .catch(error => {
                        console.error('加载分析结果时出错:', error);
                        // 显示错误信息
                        contentContainer.innerHTML = '<div class="alert alert-danger">加载分析结果时出错</div>';
                    });
                }
            });
        });
    });
</script>
{% endblock %}

{% block styles %}
<style>
    .analysis-content {
        max-height: 300px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 5px;
        background-color: #f9f9f9;
    }
</style>
{% endblock %}
