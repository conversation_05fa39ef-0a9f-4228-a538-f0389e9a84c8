{% extends "base.html" %}

{% block title %}浏览器控制台日志捕获工具{% endblock %}

{% block head %}
{{ super() }}
<style>
    .console-container {
        background-color: #1e1e1e;
        color: #f8f8f8;
        border-radius: 5px;
        padding: 10px;
        font-family: 'Consolas', 'Monaco', monospace;
        height: 400px;
        overflow-y: auto;
    }
    
    .console-inner {
        padding: 5px;
    }
    
    .console-line {
        margin-bottom: 5px;
        word-wrap: break-word;
    }
    
    .log-timestamp {
        color: #888;
        margin-right: 5px;
    }
    
    .log-info {
        color: #4a9df8;
    }
    
    .log-warn {
        color: #f8c36c;
    }
    
    .log-error {
        color: #f85c5c;
    }
    
    .log-network {
        color: #6cf89f;
    }
    
    .test-buttons {
        margin-bottom: 15px;
    }
    
    .test-buttons .btn {
        margin-right: 10px;
        margin-bottom: 10px;
    }
    
    .stats-container {
        background-color: #f8f8f8;
        border-radius: 5px;
        padding: 10px;
        margin-top: 15px;
    }
    
    .stats-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }
    
    .stats-label {
        font-weight: bold;
    }
    
    .stats-value {
        color: #4a9df8;
    }
</style>

{% if use_fix %}
<!-- 控制台捕获页面专用修复脚本 -->
<script src="{{ url_for('static', filename='js/console-capture-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/console-capture-fix.js';"></script>
{% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>浏览器控制台日志捕获工具</h1>
            <p class="lead">捕获并显示浏览器控制台的日志、警告、错误和网络请求</p>
            
            <div class="alert alert-info">
                <strong>提示：</strong> 此工具会拦截并显示浏览器控制台的所有输出，包括来自其他脚本和扩展的输出。
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">测试功能</h5>
                </div>
                <div class="card-body">
                    <div class="test-buttons">
                        <button class="btn btn-primary" onclick="testConsoleLog()">测试console.log</button>
                        <button class="btn btn-danger" onclick="testConsoleError()">测试console.error</button>
                        <button class="btn btn-warning" onclick="testConsoleWarn()">测试console.warn</button>
                        <button class="btn btn-info" onclick="testConsoleInfo()">测试console.info</button>
                        <button class="btn btn-secondary" onclick="testNetworkRequest()">测试网络请求</button>
                        <button class="btn btn-danger" onclick="testJSError()">测试JS错误</button>
                        <button class="btn btn-primary" onclick="testMultipleLogs()">测试多条日志</button>
                        <button class="btn btn-success" onclick="getSystemMetrics()">获取系统指标</button>
                    </div>
                    
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="autoScrollToggle" checked>
                        <label class="form-check-label" for="autoScrollToggle">自动滚动到最新日志</label>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">控制台输出</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary me-2" onclick="clearConsole()">
                            <i class="fas fa-trash"></i> 清空
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="saveLogs()">
                            <i class="fas fa-save"></i> 保存日志
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="console-container">
                        <div class="console-inner" id="consoleOutput"></div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">统计信息</h5>
                </div>
                <div class="card-body">
                    <div class="stats-container">
                        <div class="stats-item">
                            <span class="stats-label">日志总数:</span>
                            <span class="stats-value" id="totalLogs">0</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">信息日志:</span>
                            <span class="stats-value" id="infoLogs">0</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">警告日志:</span>
                            <span class="stats-value" id="warnLogs">0</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">错误日志:</span>
                            <span class="stats-value" id="errorLogs">0</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">网络请求:</span>
                            <span class="stats-value" id="networkRequests">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='js/browser-console-capture.js') }}"></script>
<script>
    // 存储捕获的日志
    let capturedLogs = [];
    
    // 统计信息
    let stats = {
        total: 0,
        info: 0,
        warn: 0,
        error: 0,
        network: 0
    };
    
    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化控制台输出区域
        const consoleOutput = document.getElementById('consoleOutput');
        
        // 设置日志回调
        if (window.browserConsoleCapture) {
            window.browserConsoleCapture.setLogCallback(function(logEntry) {
                // 添加到日志数组
                capturedLogs.push(logEntry);
                
                // 更新统计信息
                stats.total++;
                switch (logEntry.type) {
                    case 'log':
                    case 'info':
                        stats.info++;
                        break;
                    case 'warn':
                        stats.warn++;
                        break;
                    case 'error':
                        stats.error++;
                        break;
                    case 'network':
                        stats.network++;
                        break;
                }
                
                // 更新统计显示
                updateStats();
                
                // 创建日志元素
                const logElement = document.createElement('div');
                logElement.className = `console-line log-${logEntry.type}`;
                
                // 添加时间戳
                const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
                const timestampSpan = document.createElement('span');
                timestampSpan.className = 'log-timestamp';
                timestampSpan.textContent = `[${timestamp}]`;
                logElement.appendChild(timestampSpan);
                
                // 添加日志内容
                const contentSpan = document.createElement('span');
                contentSpan.textContent = logEntry.content;
                logElement.appendChild(contentSpan);
                
                // 添加到控制台输出
                consoleOutput.appendChild(logElement);
                
                // 自动滚动
                if (document.getElementById('autoScrollToggle').checked) {
                    consoleOutput.scrollTop = consoleOutput.scrollHeight;
                }
            });
        } else {
            console.error('浏览器控制台捕获工具未加载');
            
            // 添加错误消息
            const errorElement = document.createElement('div');
            errorElement.className = 'console-line log-error';
            errorElement.textContent = '[错误] 浏览器控制台捕获工具未加载，请刷新页面重试';
            consoleOutput.appendChild(errorElement);
        }
    });
    
    // 更新统计信息
    function updateStats() {
        document.getElementById('totalLogs').textContent = stats.total;
        document.getElementById('infoLogs').textContent = stats.info;
        document.getElementById('warnLogs').textContent = stats.warn;
        document.getElementById('errorLogs').textContent = stats.error;
        document.getElementById('networkRequests').textContent = stats.network;
    }
    
    // 清空控制台
    function clearConsole() {
        const consoleOutput = document.getElementById('consoleOutput');
        consoleOutput.innerHTML = '';
        
        // 重置统计信息
        stats = {
            total: 0,
            info: 0,
            warn: 0,
            error: 0,
            network: 0
        };
        
        // 更新统计显示
        updateStats();
        
        // 清空日志数组
        capturedLogs = [];
    }
    
    // 保存日志
    function saveLogs() {
        if (capturedLogs.length === 0) {
            alert('没有日志可保存');
            return;
        }
        
        // 发送日志到服务器
        fetch('/tools/api/logs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                logs: capturedLogs
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`成功保存 ${data.message}`);
            } else {
                alert(`保存失败: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('保存日志时出错:', error);
            alert(`保存失败: ${error.message}`);
        });
    }
    
    // 测试函数
    function testConsoleLog() {
        console.log('这是一条普通日志消息');
        console.log('带参数的日志:', { name: '九猫', version: '1.0.0' });
        console.log('多参数日志:', 123, true, [1, 2, 3]);
    }
    
    function testConsoleError() {
        console.error('这是一条错误消息');
        console.error('带参数的错误:', new Error('测试错误'));
    }
    
    function testConsoleWarn() {
        console.warn('这是一条警告消息');
        console.warn('带参数的警告:', { type: 'warning', code: 404 });
    }
    
    function testConsoleInfo() {
        console.info('这是一条信息消息');
        console.info('带参数的信息:', { status: 'running', progress: 75 });
    }
    
    function testNetworkRequest() {
        // 测试有效请求
        fetch('/tools/api/mock')
            .then(response => response.json())
            .then(data => console.log('请求成功:', data))
            .catch(error => console.error('请求失败:', error));
        
        // 测试404请求
        setTimeout(() => {
            fetch('/non-existent-endpoint')
                .then(response => response.json())
                .then(data => console.log('404请求成功:', data))
                .catch(error => console.error('404请求失败:', error));
        }, 1000);
    }
    
    function testJSError() {
        try {
            // 测试TypeError
            const obj = null;
            obj.nonExistentMethod();
        } catch (error) {
            console.error('捕获到错误:', error);
        }
        
        // 测试未捕获的错误
        setTimeout(() => {
            const arr = undefined;
            arr.push(1, 2, 3);
        }, 500);
    }
    
    function testMultipleLogs() {
        // 生成多条不同类型的日志
        for (let i = 1; i <= 20; i++) {
            if (i % 4 === 1) {
                console.log(`测试日志 #${i}`);
            } else if (i % 4 === 2) {
                console.error(`测试错误 #${i}`);
            } else if (i % 4 === 3) {
                console.warn(`测试警告 #${i}`);
            } else {
                console.info(`测试信息 #${i}`);
            }
        }
    }
    
    function getSystemMetrics() {
        fetch('/tools/api/metrics')
            .then(response => response.json())
            .then(data => console.log('获取到系统指标:', data))
            .catch(error => console.error('获取系统指标失败:', error));
    }
</script>
{% endblock %}
