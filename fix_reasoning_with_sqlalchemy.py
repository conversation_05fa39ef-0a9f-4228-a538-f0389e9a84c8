"""
使用SQLAlchemy修复章纲分析推理过程
"""

import os
import sys
import traceback

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的模块
try:
    from src.db.connection import Session
    from src.models.chapter_analysis_result import ChapterAnalysisResult
    from src.models.chapter_analysis_process import ChapterAnalysisProcess
    from src.models.chapter import Chapter
    from src.models.novel import Novel
except ImportError as e:
    print(f"导入模块时出错: {str(e)}")
    sys.exit(1)

def fix_reasoning_with_sqlalchemy():
    """使用SQLAlchemy修复章纲分析推理过程"""
    session = Session()
    try:
        # 查询所有章纲分析结果
        results = session.query(ChapterAnalysisResult).filter(
            ChapterAnalysisResult.dimension == "chapter_outline"
        ).all()
        
        print(f"找到 {len(results)} 个章纲分析结果")
        
        # 处理每个结果
        for result in results:
            try:
                # 获取章节和小说信息
                chapter = session.query(Chapter).filter(Chapter.id == result.chapter_id).first()
                novel = session.query(Novel).filter(Novel.id == result.novel_id).first()
                
                if not chapter or not novel:
                    print(f"找不到章节或小说: chapter_id={result.chapter_id}, novel_id={result.novel_id}")
                    continue
                
                print(f"处理章节: {chapter.title or f'第{chapter.chapter_number}章'}, 小说: {novel.title}")
                
                # 检查推理过程
                if not result.reasoning_content:
                    print(f"推理过程为空，需要修复")
                    
                    # 查询分析过程记录
                    processes = session.query(ChapterAnalysisProcess).filter(
                        ChapterAnalysisProcess.result_id == result.id,
                        ChapterAnalysisProcess.processing_stage == "reasoning"
                    ).all()
                    
                    if processes:
                        # 从分析过程记录中恢复推理过程
                        process = max(processes, key=lambda p: p.id)  # 获取最新的记录
                        if process.output_text:
                            print(f"从分析过程记录中恢复推理过程，长度: {len(process.output_text)}")
                            result.reasoning_content = process.output_text
                            session.commit()
                            print(f"成功更新推理过程")
                        else:
                            print(f"分析过程记录的输出文本为空")
                            # 生成新的推理过程
                            generate_new_reasoning(result, chapter, novel, session)
                    else:
                        print(f"没有分析过程记录，生成新的推理过程")
                        # 生成新的推理过程
                        generate_new_reasoning(result, chapter, novel, session)
                else:
                    print(f"推理过程已存在，长度: {len(result.reasoning_content)}")
            except Exception as e:
                print(f"处理章节分析结果时出错: {str(e)}")
                print(traceback.format_exc())
                session.rollback()
        
        print(f"完成修复章纲分析推理过程数据")
    except Exception as e:
        print(f"修复章纲分析推理过程数据时出错: {str(e)}")
        print(traceback.format_exc())
        session.rollback()
    finally:
        session.close()

def generate_new_reasoning(result, chapter, novel, session):
    """生成新的推理过程数据"""
    try:
        # 构建推理过程内容
        dimension = "chapter_outline"
        
        # 构建推理过程内容
        reasoning_content = f"""## 分析思路说明：
1. **内容重现与叙事梳理**：全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。内容不设字数限制，越详细越好。
2. **结构功能与定位分析**：全面分析章节的内部结构和功能定位，评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性
3. **关键事件与转折点**：详细分析本章节中的关键事件和转折点，识别并详述章节中的转折点、冲突点和高潮点
4. **人物表现与发展轨迹**：详细分析本章节中主要人物的表现、行动和发展，描述每个重要人物在本章中的言行和思想变化
5. **章节关联与叙事连贯性**：深入分析本章节与前后章节的关系和叙事连贯性，评估章节过渡的自然度和技巧多样性
6. **伏笔设置与悬念管理**：分析本章节中的伏笔设置和悬念管理，指出章节中埋下的伏笔和暗示，以及它们可能的未来发展方向
7. **主题情感与氛围营造**：评估章节的主题明确性和情感基调，分析本章节的核心主题和情感色彩
8. **创新特色与读者体验**：分析本章节的特色元素和创新点，识别章节中独特的写作技巧，评估如何吸引和维持读者兴趣

## 详细章纲分析：
{result.content}
"""
        
        # 更新分析结果
        result.reasoning_content = reasoning_content
        session.commit()
        print(f"成功生成并保存新的推理过程")
        
        # 保存分析过程记录
        process = ChapterAnalysisProcess(
            chapter_id=chapter.id,
            novel_id=novel.id,
            result_id=result.id,
            dimension=dimension,
            processing_stage="reasoning",
            output_text=reasoning_content,
            is_successful=True
        )
        session.add(process)
        session.commit()
        print(f"成功保存分析过程记录")
    except Exception as e:
        print(f"生成新的推理过程数据时出错: {str(e)}")
        print(traceback.format_exc())
        session.rollback()

if __name__ == "__main__":
    fix_reasoning_with_sqlalchemy()
