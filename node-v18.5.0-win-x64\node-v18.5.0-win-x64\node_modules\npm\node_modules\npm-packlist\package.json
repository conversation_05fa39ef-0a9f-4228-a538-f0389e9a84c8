{"name": "npm-packlist", "version": "5.1.0", "description": "Get a list of the files to add from a folder into an npm package", "directories": {"test": "test"}, "main": "lib", "dependencies": {"glob": "^8.0.1", "ignore-walk": "^5.0.1", "npm-bundled": "^1.1.2", "npm-normalize-package-bin": "^1.0.1"}, "author": "GitHub Inc.", "license": "ISC", "files": ["bin/", "lib/"], "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "mutate-fs": "^2.1.1", "tap": "^16.0.1"}, "scripts": {"test": "tap", "posttest": "npm run lint", "snap": "tap", "postsnap": "npm run lintfix --", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "npmclilint": "npmcli-lint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/npm-packlist.git"}, "tap": {"test-env": ["LC_ALL=sk"]}, "bin": {"npm-packlist": "bin/index.js"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}}