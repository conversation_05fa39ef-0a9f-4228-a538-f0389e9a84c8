<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫 - 浏览器控制台日志捕获工具演示</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}" onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body {
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
        .btn-group {
            margin-bottom: 20px;
        }
        .btn {
            margin-right: 5px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>九猫 - 浏览器控制台日志捕获工具演示</h1>

        <div class="card">
            <div class="card-header">
                <h5>控制台日志测试</h5>
            </div>
            <div class="card-body">
                <p>点击下面的按钮生成不同类型的控制台日志，然后点击右下角的F12按钮查看捕获的日志。</p>

                <div class="btn-group">
                    <button class="btn btn-primary" onclick="testConsoleLog()">测试 console.log</button>
                    <button class="btn btn-danger" onclick="testConsoleError()">测试 console.error</button>
                    <button class="btn btn-warning" onclick="testConsoleWarn()">测试 console.warn</button>
                    <button class="btn btn-info" onclick="testConsoleInfo()">测试 console.info</button>
                </div>

                <div class="btn-group">
                    <button class="btn btn-secondary" onclick="testJSError()">测试 JavaScript 错误</button>
                    <button class="btn btn-success" onclick="testNetworkRequest()">测试网络请求</button>
                    <button class="btn btn-dark" onclick="testMultipleLogs()">生成多条日志</button>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>使用说明</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li>点击右下角的 <strong>F12</strong> 按钮打开日志捕获界面</li>
                    <li>使用顶部的复选框过滤不同类型的日志</li>
                    <li>使用搜索框搜索特定内容</li>
                    <li>使用顶部按钮清空、复制或保存日志</li>
                    <li>也可以使用键盘快捷键 <code>Ctrl+Shift+F12</code> 切换日志界面</li>
                </ol>

                <h6>代码示例：</h6>
                <pre>
// 初始化（页面加载时自动执行）
window.browserConsoleCapture.init();

// 手动添加日志
window.browserConsoleCapture.captureLog('info', ['自定义日志消息']);

// 切换日志界面显示
window.toggleBrowserConsole();

// 清空日志
window.clearBrowserConsole();

// 修改配置
window.browserConsoleCapture.setConfig({
    maxLogEntries: 500,
    autoScroll: true
});
                </pre>
            </div>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/browser-console-capture.js') }}"></script>

    <script>
        // 测试函数
        function testConsoleLog() {
            console.log('这是一条普通日志消息');
            console.log('带参数的日志:', { name: '九猫', version: '1.0.0' });
            console.log('多参数日志:', 123, true, [1, 2, 3]);
        }

        function testConsoleError() {
            console.error('这是一条错误消息');
            console.error('带参数的错误:', new Error('测试错误'));
        }

        function testConsoleWarn() {
            console.warn('这是一条警告消息');
            console.warn('带参数的警告:', { type: 'warning', code: 404 });
        }

        function testConsoleInfo() {
            console.info('这是一条信息消息');
            console.info('带参数的信息:', { status: 'running', progress: 75 });
        }

        function testJSError() {
            try {
                // 故意制造错误
                const obj = null;
                obj.nonExistentMethod();
            } catch (e) {
                console.error('捕获到错误:', e);
            }

            // 未捕获的错误
            setTimeout(() => {
                const arr = undefined;
                arr.push(1); // 这会产生一个未捕获的错误
            }, 100);
        }

        function testNetworkRequest() {
            // 成功的请求
            fetch('/api/system-metrics')
                .then(response => response.json())
                .then(data => console.log('获取到系统指标:', data))
                .catch(error => console.error('获取系统指标失败:', error));

            // 失败的请求
            setTimeout(() => {
                fetch('/non-existent-endpoint')
                    .then(response => response.json())
                    .catch(error => console.error('请求失败:', error));
            }, 500);
        }

        function testMultipleLogs() {
            for (let i = 0; i < 20; i++) {
                const types = ['log', 'error', 'warn', 'info'];
                const type = types[i % types.length];

                switch (type) {
                    case 'log':
                        console.log(`测试日志 #${i+1}`);
                        break;
                    case 'error':
                        console.error(`测试错误 #${i+1}`);
                        break;
                    case 'warn':
                        console.warn(`测试警告 #${i+1}`);
                        break;
                    case 'info':
                        console.info(`测试信息 #${i+1}`);
                        break;
                }
            }
        }
    </script>
</body>
</html>
