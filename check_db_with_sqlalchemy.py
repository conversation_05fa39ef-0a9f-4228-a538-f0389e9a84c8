"""
使用SQLAlchemy检查数据库结构
"""

import os
import sys

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的模块
try:
    from src.db.connection import Session, engine
    from src.models.base import Base
    from src.models.novel import Novel
    from src.models.chapter import Chapter
    from src.models.analysis_result import AnalysisResult
    from src.models.chapter_analysis_result import ChapterAnalysisResult
    from src.models.analysis_process import AnalysisProcess
    from src.models.chapter_analysis_process import ChapterAnalysisProcess
except ImportError as e:
    print(f"导入模块时出错: {str(e)}")
    sys.exit(1)

def check_db_with_sqlalchemy():
    """使用SQLAlchemy检查数据库结构"""
    session = Session()
    try:
        # 检查Novel表
        novels = session.query(Novel).all()
        print(f"Novel表中有 {len(novels)} 条记录")
        
        # 检查Chapter表
        chapters = session.query(Chapter).all()
        print(f"Chapter表中有 {len(chapters)} 条记录")
        
        # 检查AnalysisResult表
        analysis_results = session.query(AnalysisResult).all()
        print(f"AnalysisResult表中有 {len(analysis_results)} 条记录")
        
        # 检查ChapterAnalysisResult表
        chapter_analysis_results = session.query(ChapterAnalysisResult).all()
        print(f"ChapterAnalysisResult表中有 {len(chapter_analysis_results)} 条记录")
        
        # 检查AnalysisProcess表
        analysis_processes = session.query(AnalysisProcess).all()
        print(f"AnalysisProcess表中有 {len(analysis_processes)} 条记录")
        
        # 检查ChapterAnalysisProcess表
        chapter_analysis_processes = session.query(ChapterAnalysisProcess).all()
        print(f"ChapterAnalysisProcess表中有 {len(chapter_analysis_processes)} 条记录")
        
        # 检查章纲分析结果
        chapter_outlines = session.query(ChapterAnalysisResult).filter(
            ChapterAnalysisResult.dimension == "chapter_outline"
        ).all()
        print(f"章纲分析结果有 {len(chapter_outlines)} 条记录")
        
        # 检查推理过程为空的章纲分析结果
        empty_reasoning = session.query(ChapterAnalysisResult).filter(
            ChapterAnalysisResult.dimension == "chapter_outline",
            ChapterAnalysisResult.reasoning_content == None
        ).all()
        print(f"推理过程为空的章纲分析结果有 {len(empty_reasoning)} 条记录")
    except Exception as e:
        print(f"检查数据库结构时出错: {str(e)}")
    finally:
        session.close()

if __name__ == "__main__":
    check_db_with_sqlalchemy()
