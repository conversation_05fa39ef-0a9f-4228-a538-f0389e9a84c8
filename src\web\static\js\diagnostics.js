/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 诊断工具模块 - 用于检测和修复常见问题
 */

// 全局诊断对象
window.nineCatsDiagnostics = {
    // 诊断结果
    results: {
        resourceLoading: {
            status: 'pending',
            details: []
        },
        apiConnectivity: {
            status: 'pending',
            details: []
        },
        browserCompatibility: {
            status: 'pending',
            details: []
        }
    },
    
    // 运行所有诊断
    runAll: function() {
        console.log('开始运行九猫系统诊断...');
        this.checkResourceLoading();
        this.checkApiConnectivity();
        this.checkBrowserCompatibility();
        
        // 5秒后显示诊断结果
        setTimeout(() => {
            this.showResults();
        }, 5000);
    },
    
    // 检查资源加载
    checkResourceLoading: function() {
        console.log('检查资源加载状态...');
        const results = this.results.resourceLoading;
        results.details = [];
        
        // 检查jQuery
        if (typeof jQuery === 'undefined') {
            results.details.push({
                name: 'jQuery',
                status: 'error',
                message: 'jQuery未加载，这会导致大部分功能无法使用'
            });
        } else {
            results.details.push({
                name: 'jQuery',
                status: 'success',
                message: `jQuery已加载 (版本 ${jQuery.fn.jquery})`
            });
        }
        
        // 检查Bootstrap
        if (typeof bootstrap === 'undefined') {
            results.details.push({
                name: 'Bootstrap',
                status: 'error',
                message: 'Bootstrap未加载，这会导致界面显示异常'
            });
        } else {
            results.details.push({
                name: 'Bootstrap',
                status: 'success',
                message: 'Bootstrap已加载'
            });
        }
        
        // 检查Chart.js
        if (typeof Chart === 'undefined') {
            results.details.push({
                name: 'Chart.js',
                status: 'error',
                message: 'Chart.js未加载，这会导致可视化图表无法显示'
            });
        } else {
            results.details.push({
                name: 'Chart.js',
                status: 'success',
                message: 'Chart.js已加载'
            });
        }
        
        // 检查CSS样式
        const bootstrapCssLoaded = Array.from(document.styleSheets).some(sheet => 
            sheet.href && sheet.href.includes('bootstrap')
        );
        
        if (!bootstrapCssLoaded) {
            results.details.push({
                name: 'Bootstrap CSS',
                status: 'error',
                message: 'Bootstrap CSS未加载，这会导致界面显示异常'
            });
        } else {
            results.details.push({
                name: 'Bootstrap CSS',
                status: 'success',
                message: 'Bootstrap CSS已加载'
            });
        }
        
        // 设置总体状态
        const hasErrors = results.details.some(item => item.status === 'error');
        results.status = hasErrors ? 'error' : 'success';
    },
    
    // 检查API连接
    checkApiConnectivity: function() {
        console.log('检查API连接状态...');
        const results = this.results.apiConnectivity;
        results.details = [];
        
        // 获取当前小说ID
        const novelId = this.getCurrentNovelId();
        if (!novelId) {
            results.details.push({
                name: '小说ID',
                status: 'error',
                message: '无法获取当前小说ID'
            });
            results.status = 'error';
            return;
        }
        
        // 测试API连接
        fetch(`/api/ping?_=${new Date().getTime()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                results.details.push({
                    name: 'API基础连接',
                    status: 'success',
                    message: '成功连接到API服务器'
                });
                
                // 测试小说API
                return fetch(`/api/novels/${novelId}?_=${new Date().getTime()}`);
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`小说API错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                results.details.push({
                    name: '小说API',
                    status: 'success',
                    message: `成功获取小说信息: ${data.title || '未知标题'}`
                });
                
                // 测试日志API
                return fetch(`/api/analysis/logs?novel_id=${novelId}&limit=1&_=${new Date().getTime()}`);
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`日志API错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                results.details.push({
                    name: '日志API',
                    status: 'success',
                    message: '成功连接到日志API'
                });
                
                // 设置总体状态
                results.status = 'success';
            })
            .catch(error => {
                console.error('API连接测试失败:', error);
                results.details.push({
                    name: 'API连接',
                    status: 'error',
                    message: `API连接测试失败: ${error.message}`
                });
                results.status = 'error';
            });
    },
    
    // 检查浏览器兼容性
    checkBrowserCompatibility: function() {
        console.log('检查浏览器兼容性...');
        const results = this.results.browserCompatibility;
        results.details = [];
        
        // 检查Fetch API
        if (!window.fetch) {
            results.details.push({
                name: 'Fetch API',
                status: 'error',
                message: '您的浏览器不支持Fetch API，这会导致无法与服务器通信'
            });
        } else {
            results.details.push({
                name: 'Fetch API',
                status: 'success',
                message: '浏览器支持Fetch API'
            });
        }
        
        // 检查Promise
        if (!window.Promise) {
            results.details.push({
                name: 'Promise',
                status: 'error',
                message: '您的浏览器不支持Promise，这会导致异步操作失败'
            });
        } else {
            results.details.push({
                name: 'Promise',
                status: 'success',
                message: '浏览器支持Promise'
            });
        }
        
        // 检查localStorage
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            results.details.push({
                name: 'localStorage',
                status: 'success',
                message: '浏览器支持localStorage'
            });
        } catch (e) {
            results.details.push({
                name: 'localStorage',
                status: 'warning',
                message: '浏览器不支持或禁用了localStorage，这可能影响某些功能'
            });
        }
        
        // 检查Canvas
        const canvas = document.createElement('canvas');
        if (!canvas.getContext) {
            results.details.push({
                name: 'Canvas',
                status: 'error',
                message: '您的浏览器不支持Canvas，这会导致图表无法显示'
            });
        } else {
            results.details.push({
                name: 'Canvas',
                status: 'success',
                message: '浏览器支持Canvas'
            });
        }
        
        // 设置总体状态
        const hasErrors = results.details.some(item => item.status === 'error');
        const hasWarnings = results.details.some(item => item.status === 'warning');
        results.status = hasErrors ? 'error' : (hasWarnings ? 'warning' : 'success');
    },
    
    // 获取当前小说ID
    getCurrentNovelId: function() {
        // 从URL中获取
        const match = window.location.pathname.match(/\/novels\/(\d+)/);
        if (match && match[1]) {
            return match[1];
        }
        
        // 从页面元素中获取
        const container = document.getElementById('novel-container');
        if (container && container.getAttribute('data-novel-id')) {
            return container.getAttribute('data-novel-id');
        }
        
        // 从全局变量中获取
        if (window.consoleLogger && window.consoleLogger.currentNovelId) {
            return window.consoleLogger.currentNovelId;
        }
        
        return null;
    },
    
    // 显示诊断结果
    showResults: function() {
        console.log('诊断完成，显示结果...');
        
        // 创建结果容器
        const resultsContainer = document.createElement('div');
        resultsContainer.className = 'diagnostics-results';
        resultsContainer.style.position = 'fixed';
        resultsContainer.style.bottom = '20px';
        resultsContainer.style.right = '20px';
        resultsContainer.style.width = '400px';
        resultsContainer.style.maxHeight = '80vh';
        resultsContainer.style.overflowY = 'auto';
        resultsContainer.style.backgroundColor = '#fff';
        resultsContainer.style.border = '1px solid #ddd';
        resultsContainer.style.borderRadius = '5px';
        resultsContainer.style.boxShadow = '0 0 10px rgba(0,0,0,0.1)';
        resultsContainer.style.zIndex = '9999';
        resultsContainer.style.padding = '15px';
        
        // 添加标题
        const title = document.createElement('h4');
        title.textContent = '九猫系统诊断结果';
        title.style.borderBottom = '1px solid #eee';
        title.style.paddingBottom = '10px';
        title.style.marginBottom = '15px';
        resultsContainer.appendChild(title);
        
        // 添加关闭按钮
        const closeButton = document.createElement('button');
        closeButton.textContent = '×';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '10px';
        closeButton.style.right = '10px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'none';
        closeButton.style.fontSize = '20px';
        closeButton.style.cursor = 'pointer';
        closeButton.onclick = function() {
            document.body.removeChild(resultsContainer);
        };
        resultsContainer.appendChild(closeButton);
        
        // 添加各项诊断结果
        this.addResultSection(resultsContainer, '资源加载', this.results.resourceLoading);
        this.addResultSection(resultsContainer, 'API连接', this.results.apiConnectivity);
        this.addResultSection(resultsContainer, '浏览器兼容性', this.results.browserCompatibility);
        
        // 添加修复按钮
        const fixButton = document.createElement('button');
        fixButton.textContent = '尝试修复问题';
        fixButton.style.marginTop = '15px';
        fixButton.style.padding = '8px 15px';
        fixButton.style.backgroundColor = '#007bff';
        fixButton.style.color = '#fff';
        fixButton.style.border = 'none';
        fixButton.style.borderRadius = '4px';
        fixButton.style.cursor = 'pointer';
        fixButton.onclick = () => {
            this.fixIssues();
            // 2秒后重新运行诊断
            setTimeout(() => {
                this.runAll();
            }, 2000);
        };
        resultsContainer.appendChild(fixButton);
        
        // 添加到页面
        document.body.appendChild(resultsContainer);
    },
    
    // 添加结果部分
    addResultSection: function(container, title, results) {
        const section = document.createElement('div');
        section.style.marginBottom = '15px';
        
        // 添加标题
        const sectionTitle = document.createElement('h5');
        sectionTitle.textContent = title;
        
        // 添加状态图标
        let statusIcon = '⏳';
        let statusColor = '#6c757d';
        
        if (results.status === 'success') {
            statusIcon = '✅';
            statusColor = '#28a745';
        } else if (results.status === 'error') {
            statusIcon = '❌';
            statusColor = '#dc3545';
        } else if (results.status === 'warning') {
            statusIcon = '⚠️';
            statusColor = '#ffc107';
        }
        
        sectionTitle.innerHTML = `${statusIcon} ${title}`;
        sectionTitle.style.color = statusColor;
        section.appendChild(sectionTitle);
        
        // 添加详情
        if (results.details && results.details.length > 0) {
            const detailsList = document.createElement('ul');
            detailsList.style.listStyleType = 'none';
            detailsList.style.paddingLeft = '10px';
            detailsList.style.marginTop = '5px';
            
            results.details.forEach(detail => {
                const item = document.createElement('li');
                let itemIcon = '⏳';
                let itemColor = '#6c757d';
                
                if (detail.status === 'success') {
                    itemIcon = '✅';
                    itemColor = '#28a745';
                } else if (detail.status === 'error') {
                    itemIcon = '❌';
                    itemColor = '#dc3545';
                } else if (detail.status === 'warning') {
                    itemIcon = '⚠️';
                    itemColor = '#ffc107';
                }
                
                item.innerHTML = `${itemIcon} <strong>${detail.name}:</strong> ${detail.message}`;
                item.style.marginBottom = '5px';
                item.style.color = itemColor;
                detailsList.appendChild(item);
            });
            
            section.appendChild(detailsList);
        }
        
        container.appendChild(section);
    },
    
    // 修复问题
    fixIssues: function() {
        console.log('尝试修复检测到的问题...');
        
        // 修复资源加载问题
        if (this.results.resourceLoading.status === 'error') {
            console.log('修复资源加载问题...');
            
            // 修复jQuery
            if (typeof jQuery === 'undefined') {
                console.log('尝试加载jQuery...');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js';
                document.head.appendChild(script);
            }
            
            // 修复Bootstrap
            if (typeof bootstrap === 'undefined') {
                console.log('尝试加载Bootstrap...');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js';
                document.head.appendChild(script);
            }
            
            // 修复Chart.js
            if (typeof Chart === 'undefined') {
                console.log('尝试加载Chart.js...');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js';
                document.head.appendChild(script);
            }
            
            // 修复Bootstrap CSS
            const bootstrapCssLoaded = Array.from(document.styleSheets).some(sheet => 
                sheet.href && sheet.href.includes('bootstrap')
            );
            
            if (!bootstrapCssLoaded) {
                console.log('尝试加载Bootstrap CSS...');
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css';
                document.head.appendChild(link);
            }
        }
        
        // 修复API连接问题
        if (this.results.apiConnectivity.status === 'error') {
            console.log('修复API连接问题...');
            
            // 清除可能的缓存
            const cacheNames = ['api-cache', 'novel-cache', 'analysis-cache'];
            if ('caches' in window) {
                cacheNames.forEach(cacheName => {
                    caches.delete(cacheName).then(success => {
                        if (success) {
                            console.log(`成功清除缓存: ${cacheName}`);
                        }
                    });
                });
            }
            
            // 刷新页面上的API状态
            if (window.consoleLogger && typeof window.consoleLogger.addLog === 'function') {
                window.consoleLogger.addLog('系统诊断: 检测到API连接问题，已尝试修复', 'warning');
            }
        }
        
        // 浏览器兼容性问题通常无法自动修复，只能提示用户
        if (this.results.browserCompatibility.status === 'error') {
            console.log('检测到浏览器兼容性问题，建议用户更换浏览器');
            alert('检测到您的浏览器可能不兼容九猫系统的某些功能。建议使用最新版本的Chrome、Firefox或Edge浏览器以获得最佳体验。');
        }
        
        console.log('修复尝试完成');
    }
};

// 添加诊断按钮
document.addEventListener('DOMContentLoaded', function() {
    // 创建诊断按钮
    const diagnosticButton = document.createElement('button');
    diagnosticButton.textContent = '系统诊断';
    diagnosticButton.style.position = 'fixed';
    diagnosticButton.style.bottom = '20px';
    diagnosticButton.style.left = '20px';
    diagnosticButton.style.zIndex = '9999';
    diagnosticButton.style.padding = '8px 15px';
    diagnosticButton.style.backgroundColor = '#6c757d';
    diagnosticButton.style.color = '#fff';
    diagnosticButton.style.border = 'none';
    diagnosticButton.style.borderRadius = '4px';
    diagnosticButton.style.cursor = 'pointer';
    diagnosticButton.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    
    // 添加点击事件
    diagnosticButton.onclick = function() {
        window.nineCatsDiagnostics.runAll();
    };
    
    // 添加到页面
    document.body.appendChild(diagnosticButton);
});

// 导出诊断对象
window.runDiagnostics = function() {
    window.nineCatsDiagnostics.runAll();
};
