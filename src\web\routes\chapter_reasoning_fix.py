"""
章纲分析推理过程修复路由
"""

import logging
import traceback
import json
from flask import Blueprint, jsonify, request, abort
from src.db.connection import Session
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.chapter_analysis_process import ChapterAnalysisProcess
from src.models.chapter import Chapter
from src.models.novel import Novel

logger = logging.getLogger(__name__)

# 创建蓝图
chapter_reasoning_fix_bp = Blueprint('chapter_reasoning_fix', __name__)

@chapter_reasoning_fix_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content')
def get_chapter_reasoning_content(novel_id, chapter_id, dimension):
    """
    获取章节分析的推理过程内容。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
    """
    try:
        # 获取请求参数
        full = request.args.get('full', 'false').lower() == 'true'

        # 记录详细的请求信息
        logger.info(f"收到章节推理过程请求: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, full={full}")

        # 获取会话
        session = Session()
        try:
            # 查询章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            # 如果没有找到分析结果
            if not result:
                logger.warning(f"找不到章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
                return jsonify({
                    "success": False,
                    "error": f"找不到章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}",
                    "message": "请确保已完成该维度的分析"
                }), 404

            # 获取推理过程内容
            reasoning_content = None

            # 首先尝试从reasoning_content字段获取
            if result.reasoning_content:
                reasoning_content = result.reasoning_content
                logger.info(f"从reasoning_content字段获取到推理过程内容，长度: {len(reasoning_content)}")

            # 如果没有找到，尝试从metadata中获取
            if not reasoning_content and result.metadata:
                try:
                    # 尝试解析metadata
                    if isinstance(result.metadata, str):
                        metadata = json.loads(result.metadata)
                    else:
                        metadata = result.metadata

                    # 从metadata中获取reasoning_content
                    if metadata and 'reasoning_content' in metadata:
                        reasoning_content = metadata['reasoning_content']
                        logger.info(f"从metadata中获取到推理过程内容，长度: {len(reasoning_content)}")
                except Exception as e:
                    logger.error(f"解析metadata时出错: {str(e)}")

            # 如果仍然没有找到，尝试从分析过程记录中获取
            if not reasoning_content:
                # 查询分析过程记录
                processes = session.query(ChapterAnalysisProcess).filter(
                    ChapterAnalysisProcess.chapter_id == chapter_id,
                    ChapterAnalysisProcess.dimension == dimension,
                    ChapterAnalysisProcess.processing_stage == "reasoning"
                ).all()

                if processes:
                    # 从分析过程记录中恢复推理过程
                    process = max(processes, key=lambda p: p.id)  # 获取最新的记录
                    if process.output_text:
                        reasoning_content = process.output_text
                        logger.info(f"从分析过程记录中获取到推理过程内容，长度: {len(reasoning_content)}")

                        # 更新分析结果
                        result.reasoning_content = reasoning_content
                        session.commit()
                        logger.info(f"成功更新推理过程")

            # 如果仍然没有找到，尝试生成推理过程
            if not reasoning_content:
                # 重定向到生成推理过程的API
                logger.info(f"重定向到生成推理过程的API")
                return generate_chapter_reasoning(novel_id, chapter_id, dimension)

            # 返回推理过程内容
            return jsonify({
                "success": True,
                "reasoning_content": reasoning_content,
                "source": "chapter_reasoning_fix",
                "full": full
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节推理过程内容时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取推理过程内容时发生系统错误，请联系管理员"
        }), 500

@chapter_reasoning_fix_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/generate_reasoning')
def generate_chapter_reasoning(novel_id, chapter_id, dimension):
    """
    生成章节分析的推理过程。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
    """
    try:
        session = Session()

        # 获取章节和小说信息
        chapter = session.query(Chapter).filter(Chapter.id == chapter_id).first()
        novel = session.query(Novel).filter(Novel.id == novel_id).first()

        if not chapter or not novel:
            logger.error(f"找不到章节或小说: chapter_id={chapter_id}, novel_id={novel_id}")
            return jsonify({"success": False, "error": "找不到章节或小说"}), 404

        # 获取分析结果
        result = session.query(ChapterAnalysisResult).filter(
            ChapterAnalysisResult.chapter_id == chapter_id,
            ChapterAnalysisResult.dimension == dimension
        ).first()

        if not result:
            logger.error(f"找不到章节分析结果: chapter_id={chapter_id}, dimension={dimension}")
            return jsonify({"success": False, "error": "找不到章节分析结果"}), 404

        # 检查推理过程
        if result.reasoning_content:
            logger.info(f"推理过程已存在，长度: {len(result.reasoning_content)}")
            return jsonify({
                "success": True,
                "reasoning_content": result.reasoning_content,
                "source": "existing"
            })

        # 查询分析过程记录
        processes = session.query(ChapterAnalysisProcess).filter(
            ChapterAnalysisProcess.result_id == result.id,
            ChapterAnalysisProcess.processing_stage == "reasoning"
        ).all()

        if processes:
            # 从分析过程记录中恢复推理过程
            process = max(processes, key=lambda p: p.id)  # 获取最新的记录
            if process.output_text:
                logger.info(f"从分析过程记录中恢复推理过程，长度: {len(process.output_text)}")
                result.reasoning_content = process.output_text
                session.commit()
                logger.info(f"成功更新推理过程")
                return jsonify({
                    "success": True,
                    "reasoning_content": result.reasoning_content,
                    "source": "process"
                })

        # 如果没有分析过程记录，生成新的推理过程
        logger.info(f"没有分析过程记录，生成新的推理过程")

        # 构建推理过程内容
        reasoning_content = f"""## 分析思路说明：
1. **内容重现与叙事梳理**：全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。内容不设字数限制，越详细越好。
2. **结构功能与定位分析**：全面分析章节的内部结构和功能定位，评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性
3. **关键事件与转折点**：详细分析本章节中的关键事件和转折点，识别并详述章节中的转折点、冲突点和高潮点
4. **人物表现与发展轨迹**：详细分析本章节中主要人物的表现、行动和发展，描述每个重要人物在本章中的言行和思想变化
5. **章节关联与叙事连贯性**：深入分析本章节与前后章节的关系和叙事连贯性，评估章节过渡的自然度和技巧多样性
6. **伏笔设置与悬念管理**：分析本章节中的伏笔设置和悬念管理，指出章节中埋下的伏笔和暗示，以及它们可能的未来发展方向
7. **主题情感与氛围营造**：评估章节的主题明确性和情感基调，分析本章节的核心主题和情感色彩
8. **创新特色与读者体验**：分析本章节的特色元素和创新点，识别章节中独特的写作技巧，评估如何吸引和维持读者兴趣

## 详细章纲分析：
{result.content}
"""

        # 更新分析结果
        result.reasoning_content = reasoning_content
        session.commit()
        logger.info(f"成功生成并保存新的推理过程")

        # 保存分析过程记录
        process = ChapterAnalysisProcess(
            chapter_id=chapter.id,
            novel_id=novel.id,
            result_id=result.id,
            dimension=dimension,
            processing_stage="reasoning",
            output_text=reasoning_content,
            is_successful=True
        )
        session.add(process)
        session.commit()
        logger.info(f"成功保存分析过程记录")

        return jsonify({
            "success": True,
            "reasoning_content": reasoning_content,
            "source": "generated"
        })
    except Exception as e:
        logger.error(f"生成章节推理过程时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        session.close()
