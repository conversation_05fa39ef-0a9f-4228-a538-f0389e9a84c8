/**
 * 九猫 - 内存优化器
 * 优化内存使用，防止内存不足错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('内存优化器已加载 v1.0.0');

    // 配置
    const CONFIG = {
        enableDebug: true,           // 启用调试模式
        maxMemoryUsage: 0.8,         // 最大内存使用率（0.8 = 80%）
        checkInterval: 10000,        // 内存检查间隔（毫秒）
        cleanupThreshold: 0.7,       // 清理阈值（0.7 = 70%）
        safeMode: true               // 安全模式
    };

    // 全局变量
    let memoryUsage = 0;
    let isHighMemoryUsage = false;
    let checkIntervalId = null;

    // 监听DOM加载完成事件
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟执行以确保所有资源已加载
        setTimeout(initializeMemoryOptimizer, 1000);
    });

    // 初始化内存优化器
    function initializeMemoryOptimizer() {
        console.log('初始化内存优化器');

        // 添加内存使用监控
        addMemoryMonitoring();

        // 优化DOM操作
        optimizeDOMOperations();

        // 优化图表
        optimizeCharts();

        // 优化分析过程查看器
        optimizeProcessViewer();
    }

    // 添加内存使用监控
    function addMemoryMonitoring() {
        console.log('添加内存使用监控');

        // 检查是否支持performance.memory
        if (window.performance && window.performance.memory) {
            // 开始定期检查内存使用情况
            checkIntervalId = setInterval(checkMemoryUsage, CONFIG.checkInterval);

            // 立即检查一次
            checkMemoryUsage();
        } else {
            console.log('浏览器不支持内存监控API');
        }
    }

    // 检查内存使用情况
    function checkMemoryUsage() {
        try {
            if (window.performance && window.performance.memory) {
                const memory = window.performance.memory;

                // 计算内存使用率
                memoryUsage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;

                console.log(`内存使用率: ${(memoryUsage * 100).toFixed(2)}%`);

                // 检查是否超过阈值
                if (memoryUsage > CONFIG.maxMemoryUsage && !isHighMemoryUsage) {
                    console.warn(`内存使用率过高: ${(memoryUsage * 100).toFixed(2)}%，开始清理`);
                    isHighMemoryUsage = true;

                    // 执行内存清理
                    cleanupMemory();
                } else if (memoryUsage < CONFIG.cleanupThreshold && isHighMemoryUsage) {
                    console.log(`内存使用率已降低: ${(memoryUsage * 100).toFixed(2)}%`);
                    isHighMemoryUsage = false;
                }
            }
        } catch (e) {
            console.error('检查内存使用情况时出错:', e);
        }
    }

    // 清理内存
    function cleanupMemory() {
        console.log('执行内存清理');

        // 清理不必要的DOM元素
        cleanupDOM();

        // 清理不必要的数据
        cleanupData();

        // 强制垃圾回收（虽然不能直接调用，但可以尝试通过一些操作触发）
        try {
            const largeArray = new Array(1000).fill(new Array(1000));
            largeArray.length = 0;
        } catch (e) {
            console.error('尝试触发垃圾回收时出错:', e);
        }
    }

    // 清理DOM
    function cleanupDOM() {
        console.log('清理DOM');

        // 移除不必要的元素
        const elementsToRemove = [
            '.memory-optimization-notice',
            '.process-explanation'
        ];

        elementsToRemove.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                element.remove();
            });
        });

        // 清理隐藏的内容
        const hiddenElements = document.querySelectorAll('.collapse:not(.show)');
        hiddenElements.forEach(element => {
            // 保留元素但清空内容
            element.innerHTML = '';
        });
    }

    // 清理数据
    function cleanupData() {
        console.log('清理数据');

        // 清理分析过程查看器数据
        if (window.processViewerConfig) {
            // 只保留当前页的数据
            if (window.processViewerConfig.processes && window.processViewerConfig.processes.length > 20) {
                const currentPage = window.processViewerConfig.currentPage || 1;
                const pageSize = window.processViewerConfig.pageSize || 10;
                const startIndex = (currentPage - 1) * pageSize;
                const endIndex = startIndex + pageSize;

                // 只保留当前页的数据
                window.processViewerConfig.processes = window.processViewerConfig.processes.slice(startIndex, endIndex);
            }

            // 清理内容块
            if (window.processViewerConfig.contentChunks && window.processViewerConfig.contentChunks.length > 3) {
                const currentContentPage = window.processViewerConfig.currentContentPage || 0;

                // 只保留当前页和相邻页的内容
                const startIndex = Math.max(0, currentContentPage - 1);
                const endIndex = Math.min(window.processViewerConfig.contentChunks.length, currentContentPage + 2);

                window.processViewerConfig.contentChunks = window.processViewerConfig.contentChunks.slice(startIndex, endIndex);
            }
        }

        // 清理图表实例
        if (window.chartInstances) {
            // 销毁不可见的图表
            for (const id in window.chartInstances) {
                const chart = window.chartInstances[id];
                const canvas = chart.canvas;

                // 检查图表是否可见
                if (canvas && !isElementVisible(canvas)) {
                    // 销毁不可见的图表
                    chart.destroy();
                    delete window.chartInstances[id];
                }
            }
        }
    }

    // 检查元素是否可见
    function isElementVisible(element) {
        if (!element) return false;

        const rect = element.getBoundingClientRect();

        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // 优化DOM操作
    function optimizeDOMOperations() {
        console.log('优化DOM操作');

        // 使用事件委托减少事件监听器数量
        document.addEventListener('click', function(event) {
            // 处理折叠/展开按钮
            if (event.target.classList.contains('process-content-toggle') ||
                event.target.closest('.process-content-toggle')) {

                const button = event.target.classList.contains('process-content-toggle')
                    ? event.target
                    : event.target.closest('.process-content-toggle');

                // 切换图标
                const icon = button.querySelector('i');
                if (icon) {
                    if (icon.classList.contains('fa-caret-right')) {
                        icon.classList.remove('fa-caret-right');
                        icon.classList.add('fa-caret-down');
                    } else {
                        icon.classList.remove('fa-caret-down');
                        icon.classList.add('fa-caret-right');
                    }
                }
            }

            // 处理加载完整内容按钮
            if (event.target.classList.contains('load-full-content') ||
                event.target.closest('.load-full-content')) {

                const button = event.target.classList.contains('load-full-content')
                    ? event.target
                    : event.target.closest('.load-full-content');

                const processId = button.getAttribute('data-process-id');
                const contentType = button.getAttribute('data-content-type');

                if (processId && contentType && window.loadFullContent) {
                    window.loadFullContent(processId, contentType);
                }
            }
        });
    }

    // 优化图表
    function optimizeCharts() {
        console.log('优化图表');

        // 如果存在Chart对象，优化其配置
        if (window.Chart && Chart.defaults) {
            try {
                // 设置全局默认值以减少内存使用
                Chart.defaults.animation = false;
                Chart.defaults.responsive = true;
                Chart.defaults.maintainAspectRatio = false;

                // 安全地设置字体大小
                if (Chart.defaults.font) {
                    Chart.defaults.font.size = 10;
                }

                // 安全地设置点的大小
                if (Chart.defaults.elements && Chart.defaults.elements.point) {
                    Chart.defaults.elements.point.radius = 2;
                    Chart.defaults.elements.point.hoverRadius = 3;
                }

                // 安全地设置线的宽度
                if (Chart.defaults.elements && Chart.defaults.elements.line) {
                    Chart.defaults.elements.line.borderWidth = 1;
                }

                // 安全地设置图例
                if (Chart.defaults.plugins && Chart.defaults.plugins.legend) {
                    Chart.defaults.plugins.legend.display = false;
                }

                // 安全地设置提示框
                if (Chart.defaults.plugins && Chart.defaults.plugins.tooltip) {
                    Chart.defaults.plugins.tooltip.enabled = true;
                    Chart.defaults.plugins.tooltip.mode = 'index';
                    Chart.defaults.plugins.tooltip.intersect = false;
                }

                console.log('图表配置已优化');
            } catch (e) {
                console.error('优化图表配置时出错:', e);
            }
        } else {
            console.log('Chart.js未加载或不可用，跳过图表优化');
        }
    }

    // 优化分析过程查看器
    function optimizeProcessViewer() {
        console.log('优化分析过程查看器');

        // 检查是否在分析过程查看器页面
        if (!window.location.pathname.includes('/process') &&
            !window.location.pathname.includes('/process_viewer')) {
            return;
        }

        // 添加懒加载
        addLazyLoading();

        // 优化内容渲染
        optimizeContentRendering();
    }

    // 添加懒加载
    function addLazyLoading() {
        console.log('添加懒加载');

        // 使用IntersectionObserver实现懒加载
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const node = entry.target;

                        // 检查是否需要加载内容
                        const contentBodies = node.querySelectorAll('.process-content-body');
                        contentBodies.forEach(body => {
                            // 如果内容为空，添加占位符
                            if (!body.textContent.trim()) {
                                body.innerHTML = '<div class="text-center"><em>内容将在展开时加载...</em></div>';
                            }
                        });

                        // 停止观察已处理的节点
                        observer.unobserve(node);
                    }
                });
            }, {
                root: null,
                rootMargin: '0px',
                threshold: 0.1
            });

            // 观察所有时间线节点
            document.querySelectorAll('.timeline-node').forEach(node => {
                observer.observe(node);
            });
        }
    }

    // 优化内容渲染
    function optimizeContentRendering() {
        console.log('优化内容渲染');

        // 重写renderProcessContent函数，优化渲染性能
        if (window.renderProcessContent) {
            const originalRenderProcessContent = window.renderProcessContent;

            window.renderProcessContent = function() {
                try {
                    // 检查内存使用情况
                    if (memoryUsage > CONFIG.cleanupThreshold) {
                        console.warn('内存使用率高，使用优化的渲染方法');

                        const contentElement = document.getElementById('processContent');
                        if (!contentElement) return;

                        // 如果没有内容
                        if (!window.processViewerConfig || !window.processViewerConfig.processContent) {
                            contentElement.innerHTML = `
                                <div class="alert alert-info">
                                    没有找到分析过程内容。这可能是因为该分析是在启用详细过程记录功能之前进行的。
                                </div>
                            `;
                            return;
                        }

                        // 使用更高效的方法渲染内容
                        const content = window.processViewerConfig.processContent;

                        // 分割内容为更小的块
                        const chunks = [];
                        const maxChunkSize = 5000;

                        for (let i = 0; i < content.length; i += maxChunkSize) {
                            chunks.push(content.substring(i, i + maxChunkSize));
                        }

                        // 只渲染第一个块
                        contentElement.innerHTML = chunks[0];

                        // 如果有多个块，添加"加载更多"按钮
                        if (chunks.length > 1) {
                            const loadMoreButton = document.createElement('button');
                            loadMoreButton.className = 'btn btn-outline-primary mt-3';
                            loadMoreButton.textContent = '加载更多内容';
                            loadMoreButton.addEventListener('click', function() {
                                // 加载下一个块
                                const nextChunkIndex = parseInt(this.getAttribute('data-next-chunk') || '1');
                                if (nextChunkIndex < chunks.length) {
                                    // 添加下一个块
                                    contentElement.innerHTML += chunks[nextChunkIndex];

                                    // 更新按钮
                                    this.setAttribute('data-next-chunk', (nextChunkIndex + 1).toString());

                                    // 如果已加载所有块，隐藏按钮
                                    if (nextChunkIndex + 1 >= chunks.length) {
                                        this.style.display = 'none';
                                    }
                                }
                            });
                            loadMoreButton.setAttribute('data-next-chunk', '1');
                            contentElement.appendChild(loadMoreButton);
                        }
                    } else {
                        // 使用原始渲染方法
                        originalRenderProcessContent();
                    }
                } catch (e) {
                    console.error('优化渲染内容时出错:', e);

                    // 使用原始渲染方法
                    originalRenderProcessContent();
                }
            };
        }
    }
})();
