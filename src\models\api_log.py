"""
API调用日志模型
用于记录API调用情况、令牌使用量和费用
"""
from datetime import datetime, timezone
from sqlalchemy import Column, Integer, Float, String, DateTime, Text, JSON
from typing import Dict, Any, Optional

from src.models.base import Base

# 创建一个函数来替代已弃用的datetime.utcnow
def utc_now():
    """返回当前的UTC时间，替代已弃用的datetime.utcnow"""
    return datetime.now(timezone.utc)

class ApiLog(Base):
    """API调用日志模型，用于记录API调用情况、令牌使用量和费用"""

    __tablename__ = 'api_logs'

    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, default=utc_now, index=True)

    # 请求信息
    endpoint = Column(String(100), nullable=False)  # API端点
    method = Column(String(10), nullable=False)  # 请求方法：GET, POST等
    parameters = Column(JSON, nullable=True)  # 请求参数

    # 响应信息
    status_code = Column(Integer, nullable=True)  # HTTP状态码
    response_time = Column(Float, nullable=True)  # 响应时间（毫秒）
    response_size = Column(Integer, nullable=True)  # 响应大小（字节）

    # 令牌使用量
    input_tokens = Column(Integer, nullable=True)  # 输入令牌数
    output_tokens = Column(Integer, nullable=True)  # 输出令牌数
    total_tokens = Column(Integer, nullable=True)  # 总令牌数

    # 费用信息
    input_cost = Column(Float, nullable=True)  # 输入令牌费用（元）
    output_cost = Column(Float, nullable=True)  # 输出令牌费用（元）
    total_cost = Column(Float, nullable=True)  # 总费用（元）

    # 小说ID，用于关联分析
    novel_id = Column(Integer, nullable=True)  # 关联的小说ID
    analysis_type = Column(String(50), nullable=True)  # 分析类型

    # 错误信息
    error_type = Column(String(50), nullable=True)  # 错误类型：timeout, connection_error等
    error_message = Column(Text, nullable=True)  # 错误详细信息

    def __repr__(self):
        return f"<ApiLog(id={self.id}, timestamp={self.timestamp}, endpoint={self.endpoint}, status_code={self.status_code}, tokens={self.total_tokens}, cost={self.total_cost})>"
