{% extends "v3/base.html" %}

{% block title %}{{ dimension_info.name }} 分析 - {{ chapter.title }} - {{ novel.title }} - 九猫小说分析写作系统v3.0{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/analysis-display-v3.css') }}">
<style>
    .analysis-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px var(--shadow-color);
        position: relative;
        overflow: hidden;
    }

    .analysis-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .analysis-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    }

    .analysis-meta {
        position: relative;
        z-index: 1;
        opacity: 0.9;
    }

    .tab-content {
        padding: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 分析头部信息 -->
<div class="analysis-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">{{ dimension_info.name }}</h1>
            <div class="analysis-meta">
                <p class="lead mb-2">
                    <i class="fas fa-book-open me-2"></i>{{ chapter.title }}
                </p>
                <p class="mb-0">
                    <i class="fas fa-book me-2"></i>{{ novel.title }}
                </p>
            </div>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <button id="reanalyzeBtn" class="btn btn-primary me-2">
                    <i class="fas fa-sync-alt me-1"></i>重新分析
                </button>
                <button id="deleteAnalysisBtn" class="btn btn-danger me-2">
                    <i class="fas fa-trash me-1"></i>删除分析
                </button>
                <a href="{{ url_for('v3.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-1"></i>返回章节
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-12">
        <ul class="nav nav-tabs" id="analysisTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="result-tab" data-bs-toggle="tab" data-bs-target="#result" type="button" role="tab" aria-controls="result" aria-selected="true">
                    <i class="fas fa-chart-bar me-1"></i>分析结果
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="reasoning-tab" data-bs-toggle="tab" data-bs-target="#reasoning" type="button" role="tab" aria-controls="reasoning" aria-selected="false">
                    <i class="fas fa-brain me-1"></i>推理过程
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab" aria-controls="info" aria-selected="false">
                    <i class="fas fa-info-circle me-1"></i>维度说明
                </button>
            </li>
        </ul>
        <div class="tab-content" id="analysisTabContent">
            <!-- 分析结果标签页 -->
            <div class="tab-pane fade show active" id="result" role="tabpanel" aria-labelledby="result-tab">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h3 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>{{ dimension_info.name }} 分析结果</h3>
                    </div>
                    <div class="card-body">
                        <div class="analysis-content markdown-content">
{{ chapter_analysis_result.content|safe }}
</div>
                    </div>
                </div>
            </div>

            <!-- 推理过程标签页 -->
            <div class="tab-pane fade" id="reasoning" role="tabpanel" aria-labelledby="reasoning-tab">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h3 class="card-title mb-0"><i class="fas fa-brain me-2"></i>推理过程</h3>
                    </div>
                    <div class="card-body">
                        <div id="reasoningContent" class="reasoning-content">
                            <div class="text-center py-4">
                                <p class="text-muted">点击"推理过程"标签页加载推理内容</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 维度说明标签页 -->
            <div class="tab-pane fade" id="info" role="tabpanel" aria-labelledby="info-tab">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>维度说明</h3>
                    </div>
                    <div class="card-body">
                        <h4>{{ dimension_info.name }}</h4>
                        <p>{{ dimension_info.description }}</p>

                        <div class="alert alert-info mt-4">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>提示：</strong> 分析结果是基于AI对章节内容的理解和分析，仅供参考。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/reasoning-content-formatter.js') }}"></script>
<script src="{{ url_for('static', filename='js/markdown-renderer.js') }}"></script>
<script>
    $(document).ready(function() {
        // 激活当前标签页
        const hash = window.location.hash;
        if (hash) {
            $('#analysisTabs a[href="' + hash + '"]').tab('show');
        }

        // 点击标签页时更新URL
        $('#analysisTabs a').on('click', function(e) {
            window.location.hash = $(this).attr('href');
        });

        // 删除分析按钮点击事件
        $('#deleteAnalysisBtn').click(function() {
            if (confirm('确定要删除"{{ dimension_info.name }}"维度的分析结果吗？\n\n删除后将无法恢复，需要重新分析才能获取结果。')) {
                // 显示加载状态
                const originalText = $(this).html();
                $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>删除中...');
                $(this).prop('disabled', true);

                // 构建API URL
                const apiUrl = `/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analysis/{{ dimension }}/delete`;

                // 发送删除请求
                fetch(apiUrl, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示成功消息
                        alert('分析结果已成功删除，即将返回章节页面');

                        // 返回章节页面
                        window.location.href = "{{ url_for('v3.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}";
                    } else {
                        // 显示错误消息
                        alert(`删除分析结果失败: ${data.message || '未知错误'}`);

                        // 恢复按钮状态
                        $(this).html(originalText);
                        $(this).prop('disabled', false);

                        // 尝试使用备用API路径
                        const backupApiUrl = `/api/novels/{{ novel.id }}/chapter/{{ chapter.id }}/analysis/{{ dimension }}/delete`;
                        fetch(backupApiUrl, {
                            method: 'POST'
                        })
                        .then(response => response.json())
                        .then(backupData => {
                            if (backupData.success) {
                                // 显示成功消息
                                alert('分析结果已成功删除，即将返回章节页面');

                                // 返回章节页面
                                window.location.href = "{{ url_for('v3.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}";
                            }
                        });
                    }
                })
                .catch(error => {
                    // 显示错误消息
                    alert(`请求出错: ${error.message}`);

                    // 恢复按钮状态
                    $(this).html(originalText);
                    $(this).prop('disabled', false);
                });
            }
        });

        // 重新分析按钮点击事件
        $('#reanalyzeBtn').click(function() {
            if (confirm('确定要重新分析"{{ dimension_info.name }}"维度吗？\n\n重新分析将覆盖现有结果，分析过程可能需要几分钟时间，请耐心等待。')) {
                // 显示加载状态
                const originalText = $(this).html();
                $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...');
                $(this).prop('disabled', true);

                // 构建API URL
                const apiUrl = `/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analyze_dimension`;

                // 发送重新分析请求
                fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dimension: '{{ dimension }}',
                        force: true
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示成功消息
                        alert('重新分析已开始，页面将在5秒后刷新');

                        // 5秒后刷新页面
                        setTimeout(() => {
                            window.location.reload();
                        }, 5000);
                    } else {
                        // 显示错误消息
                        alert(`重新分析失败: ${data.error || '未知错误'}`);

                        // 恢复按钮状态
                        $(this).html(originalText);
                        $(this).prop('disabled', false);
                    }
                })
                .catch(error => {
                    // 显示错误消息
                    alert(`请求出错: ${error.message}`);

                    // 恢复按钮状态
                    $(this).html(originalText);
                    $(this).prop('disabled', false);
                });
            }
        });

        // 应用Markdown增强到分析结果
        enhanceMarkdownContent(document.querySelector('.analysis-content'));

        // 加载推理过程
        const reasoningTab = document.getElementById('reasoning-tab');
        reasoningTab.addEventListener('click', function() {
            // 只在第一次点击时加载
            if (!this.dataset.loaded) {
                loadReasoningContent('/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analysis/{{ dimension }}/reasoning_content', 'reasoningContent');
                this.dataset.loaded = 'true';
            }
        });

        // 加载推理内容的函数
        function loadReasoningContent(url, containerId) {
            const container = document.getElementById(containerId);

            // 显示加载中状态
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3">正在加载推理过程，请稍候...</p>
                </div>
            `;

            // 发送请求
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.reasoning_content) {
                        // 渲染推理过程
                        container.innerHTML = `<div class="reasoning-structured markdown-content">${data.reasoning_content}</div>`;

                        // 应用格式化
                        const reasoningContainers = document.querySelectorAll('.reasoning-structured:not(.formatted)');
                        if (reasoningContainers.length > 0 && typeof formatReasoningContent === 'function') {
                            reasoningContainers.forEach(formatReasoningContent);
                        }

                        // 应用Markdown增强
                        const markdownContainers = document.querySelectorAll('.markdown-content:not(.enhanced)');
                        if (markdownContainers.length > 0 && typeof enhanceMarkdownContent === 'function') {
                            markdownContainers.forEach(enhanceMarkdownContent);
                        }
                    } else {
                        container.innerHTML = `
                            <div class="alert alert-info">
                                <p><i class="fas fa-info-circle me-2"></i>暂无推理过程数据</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载推理过程时出错:', error);
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <p><i class="fas fa-exclamation-circle me-2"></i>加载推理过程时出错</p>
                            <p class="small">${error.message}</p>
                        </div>
                    `;
                });
        }
    });
</script>
{% endblock %}
