/**
 * 九猫 - 分析维度选择紧急修复脚本
 * 这个脚本用于紧急修复分析维度选择对话框中维度列表完全消失的问题
 * 版本: 1.0.0
 * 日期: 2025-05-05
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析维度选择紧急修复脚本已加载');
    
    // 配置
    const CONFIG = {
        debug: true,                 // 是否启用调试模式
        fixTimeout: 300,             // 修复超时时间（毫秒）
        retryInterval: 200,          // 重试间隔（毫秒）
        maxRetries: 10,              // 最大重试次数
        forceRebuild: true,          // 是否强制重建维度列表
        checkInterval: 1000,         // 检查间隔（毫秒）
        maxChecks: 10                // 最大检查次数
    };
    
    // 维度名称映射 - 硬编码所有维度
    const DIMENSION_NAMES = {
        'language_style': '语言风格',
        'rhythm_pacing': '节奏与节奏',
        'structure': '结构分析',
        'sentence_variation': '句式变化',
        'paragraph_length': '段落长度',
        'perspective_shifts': '视角转换',
        'paragraph_flow': '段落流畅度',
        'novel_characteristics': '小说特点',
        'world_building': '世界构建',
        'chapter_outline': '章节大纲',
        'character_relationships': '人物关系',
        'opening_effectiveness': '开篇效果',
        'climax_pacing': '高潮节奏'
    };
    
    // 所有维度列表
    const ALL_DIMENSIONS = Object.keys(DIMENSION_NAMES);
    
    // 安全的日志函数
    function safeLog(message, level = 'log') {
        if (CONFIG.debug || level === 'error' || level === 'warn') {
            try {
                console[level](`[维度选择紧急修复] ${message}`);
            } catch (e) {
                // 忽略日志错误
            }
        }
    }
    
    // 查找分析模态框
    function findAnalyzeModal() {
        try {
            const modal = document.getElementById('analyzeModal');
            if (!modal) {
                safeLog('找不到分析模态框', 'warn');
                return null;
            }
            
            safeLog('找到分析模态框');
            return modal;
        } catch (e) {
            safeLog(`查找分析模态框时出错: ${e.message}`, 'error');
            return null;
        }
    }
    
    // 查找维度选择容器
    function findDimensionContainer() {
        try {
            // 首先尝试查找分析模态框
            const modal = findAnalyzeModal();
            if (!modal) {
                return null;
            }
            
            // 查找表单
            const form = modal.querySelector('form');
            if (!form) {
                safeLog('找不到分析表单', 'warn');
                return null;
            }
            
            // 查找表单体
            const modalBody = form.querySelector('.modal-body');
            if (!modalBody) {
                safeLog('找不到模态框体', 'warn');
                return null;
            }
            
            safeLog('找到维度选择容器');
            return modalBody;
        } catch (e) {
            safeLog(`查找维度选择容器时出错: ${e.message}`, 'error');
            return null;
        }
    }
    
    // 查找全选复选框
    function findSelectAllCheckbox() {
        try {
            const selectAll = document.getElementById('select-all-dimensions');
            if (!selectAll) {
                safeLog('找不到全选复选框', 'warn');
                return null;
            }
            
            safeLog('找到全选复选框');
            return selectAll;
        } catch (e) {
            safeLog(`查找全选复选框时出错: ${e.message}`, 'error');
            return null;
        }
    }
    
    // 查找分隔线
    function findHorizontalRule() {
        try {
            const container = findDimensionContainer();
            if (!container) {
                return null;
            }
            
            const hr = container.querySelector('hr');
            if (!hr) {
                safeLog('找不到分隔线', 'warn');
                return null;
            }
            
            safeLog('找到分隔线');
            return hr;
        } catch (e) {
            safeLog(`查找分隔线时出错: ${e.message}`, 'error');
            return null;
        }
    }
    
    // 查找所有维度复选框
    function findDimensionCheckboxes() {
        try {
            const checkboxes = document.querySelectorAll('.dimension-checkbox');
            safeLog(`找到 ${checkboxes.length} 个维度复选框`);
            return checkboxes;
        } catch (e) {
            safeLog(`查找维度复选框时出错: ${e.message}`, 'error');
            return [];
        }
    }
    
    // 检查维度列表是否为空
    function isDimensionListEmpty() {
        const checkboxes = findDimensionCheckboxes();
        return checkboxes.length === 0;
    }
    
    // 创建维度复选框
    function createDimensionCheckbox(dimension, index) {
        try {
            // 创建容器
            const container = document.createElement('div');
            container.className = 'form-check';
            
            // 创建复选框
            const checkbox = document.createElement('input');
            checkbox.className = 'form-check-input dimension-checkbox';
            checkbox.type = 'checkbox';
            checkbox.name = 'dimensions';
            checkbox.value = dimension;
            checkbox.id = `dimension-${index + 1}`;
            
            // 创建标签
            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.setAttribute('for', `dimension-${index + 1}`);
            label.textContent = DIMENSION_NAMES[dimension] || dimension;
            
            // 组装
            container.appendChild(checkbox);
            container.appendChild(label);
            
            return container;
        } catch (e) {
            safeLog(`创建维度复选框时出错: ${e.message}`, 'error');
            return null;
        }
    }
    
    // 重建维度列表
    function rebuildDimensionList() {
        try {
            safeLog('开始重建维度列表');
            
            // 查找必要的元素
            const container = findDimensionContainer();
            const hr = findHorizontalRule();
            
            if (!container || !hr) {
                safeLog('找不到必要的元素，无法重建维度列表', 'error');
                return false;
            }
            
            // 查找警告提示
            const warningAlert = container.querySelector('.alert-warning');
            
            // 删除现有的维度复选框
            const existingCheckboxes = container.querySelectorAll('.form-check:not(:first-child)');
            existingCheckboxes.forEach(checkbox => {
                checkbox.remove();
            });
            
            safeLog(`删除了 ${existingCheckboxes.length} 个现有维度复选框`);
            
            // 创建新的维度复选框
            let addedCount = 0;
            let insertPoint = hr;
            
            ALL_DIMENSIONS.forEach((dimension, index) => {
                const checkboxContainer = createDimensionCheckbox(dimension, index);
                if (checkboxContainer) {
                    // 插入到分隔线后面
                    insertPoint.parentNode.insertBefore(checkboxContainer, insertPoint.nextSibling);
                    // 更新插入点
                    insertPoint = checkboxContainer;
                    addedCount++;
                }
            });
            
            // 如果有警告提示，确保它在维度列表之后
            if (warningAlert) {
                container.insertBefore(warningAlert, insertPoint.nextSibling);
            }
            
            safeLog(`添加了 ${addedCount} 个维度复选框`);
            
            // 绑定全选事件
            bindSelectAllEvent();
            
            return addedCount > 0;
        } catch (e) {
            safeLog(`重建维度列表时出错: ${e.message}`, 'error');
            return false;
        }
    }
    
    // 绑定全选事件
    function bindSelectAllEvent() {
        try {
            const selectAll = findSelectAllCheckbox();
            if (!selectAll) {
                return false;
            }
            
            // 移除现有事件监听器
            const newSelectAll = selectAll.cloneNode(true);
            selectAll.parentNode.replaceChild(newSelectAll, selectAll);
            
            // 添加新的事件监听器
            newSelectAll.addEventListener('change', function() {
                const isChecked = this.checked;
                const checkboxes = findDimensionCheckboxes();
                checkboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
            });
            
            safeLog('绑定全选事件成功');
            return true;
        } catch (e) {
            safeLog(`绑定全选事件时出错: ${e.message}`, 'error');
            return false;
        }
    }
    
    // 监听模态框打开事件
    function listenForModalOpen() {
        try {
            const modal = findAnalyzeModal();
            if (!modal) {
                return false;
            }
            
            // 使用MutationObserver监听模态框的显示状态变化
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.attributeName === 'class' && modal.classList.contains('show')) {
                        safeLog('检测到模态框打开');
                        setTimeout(function() {
                            if (isDimensionListEmpty() || CONFIG.forceRebuild) {
                                rebuildDimensionList();
                            }
                        }, 100);
                    }
                });
            });
            
            observer.observe(modal, { attributes: true });
            safeLog('开始监听模态框打开事件');
            return true;
        } catch (e) {
            safeLog(`监听模态框打开事件时出错: ${e.message}`, 'error');
            return false;
        }
    }
    
    // 定期检查维度列表
    function startPeriodicCheck() {
        let checkCount = 0;
        
        function check() {
            if (checkCount >= CONFIG.maxChecks) {
                safeLog('达到最大检查次数，停止检查');
                return;
            }
            
            checkCount++;
            
            // 检查模态框是否打开
            const modal = findAnalyzeModal();
            if (modal && modal.classList.contains('show')) {
                safeLog(`第 ${checkCount} 次检查: 模态框已打开`);
                
                // 检查维度列表是否为空
                if (isDimensionListEmpty() || CONFIG.forceRebuild) {
                    safeLog('维度列表为空或强制重建，开始重建');
                    rebuildDimensionList();
                } else {
                    safeLog('维度列表不为空，无需重建');
                }
            } else {
                safeLog(`第 ${checkCount} 次检查: 模态框未打开`);
            }
            
            // 继续检查
            setTimeout(check, CONFIG.checkInterval);
        }
        
        // 开始检查
        check();
    }
    
    // 初始化修复
    function initFix() {
        safeLog('开始初始化维度选择紧急修复');
        
        // 监听模态框打开事件
        listenForModalOpen();
        
        // 开始定期检查
        startPeriodicCheck();
        
        // 如果模态框已经打开，立即修复
        const modal = findAnalyzeModal();
        if (modal && modal.classList.contains('show')) {
            safeLog('模态框已经打开，立即修复');
            setTimeout(function() {
                rebuildDimensionList();
            }, CONFIG.fixTimeout);
        }
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initFix);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initFix();
    }
    
    // 导出函数供其他模块使用
    window.dimensionSelectionEmergencyFix = {
        rebuildDimensionList: rebuildDimensionList,
        isDimensionListEmpty: isDimensionListEmpty
    };
})();
