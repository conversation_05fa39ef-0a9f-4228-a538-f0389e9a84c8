/**
 * 九猫 - 资源加载器
 * 强制确保所有资源加载时都添加crossorigin属性
 * 版本: 1.0.0
 */

(function() {
    // 检查是否已加载
    if (window._resourceLoaderLoaded) {
        console.log('[资源加载器] 已加载，不重复执行');
        return;
    }

    // 标记已加载
    window._resourceLoaderLoaded = true;

    console.log('[资源加载器] 初始化版本1.0.0');

    // 保存原始方法
    const originalCreateElement = document.createElement;
    const originalHeadAppendChild = document.head ? document.head.appendChild : null;
    // 安全地获取body的appendChild方法，如果body不存在则为null
    const originalBodyAppendChild = document.body ? document.body.appendChild : null;
    const originalSetAttribute = Element.prototype.setAttribute;

    // 修改createElement方法，强制所有脚本和样式表添加crossorigin属性
    document.createElement = function(tagName) {
        // 调用原始方法创建元素
        const element = originalCreateElement.call(document, tagName);

        // 检查是否是脚本或样式表
        const tag = tagName.toLowerCase();
        if (tag === 'script' || tag === 'link') {
            // 覆盖setAttribute方法
            element.setAttribute = function(name, value) {
                // 调用原始方法设置属性
                const result = originalSetAttribute.call(this, name, value);

                // 如果设置了src或href属性，检查并添加crossorigin
                if ((name === 'src' || name === 'href') && !this.hasAttribute('crossorigin')) {
                    originalSetAttribute.call(this, 'crossorigin', 'anonymous');
                    console.log(`[资源加载器] 为新创建的${tag}元素添加了crossorigin属性`);
                }

                return result;
            };
        }

        return element;
    };

    // 修改appendChild方法，拦截没有crossorigin属性的资源
    function patchAppendChildMethod(object) {
        const originalMethod = object.appendChild;

        object.appendChild = function(element) {
            // 处理脚本和样式表
            if (element.tagName === 'SCRIPT' ||
                (element.tagName === 'LINK' && element.rel === 'stylesheet')) {
                // 如果有src/href属性但没有crossorigin属性，添加它
                if ((element.src || element.href) && !element.hasAttribute('crossorigin')) {
                    element.setAttribute('crossorigin', 'anonymous');
                    console.log(`[资源加载器] 为添加到DOM的${element.tagName.toLowerCase()}元素添加了crossorigin属性`);
                }
            }

            // 调用原始方法
            return originalMethod.call(this, element);
        };
    }

    // 安全地应用补丁到document.head和document.body
    if (document.head) {
        patchAppendChildMethod(document.head);
    }

    // 如果body不存在，等待DOMContentLoaded事件后再应用补丁
    if (document.body) {
        patchAppendChildMethod(document.body);
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            if (document.body) {
                patchAppendChildMethod(document.body);
                console.log('[资源加载器] DOM加载完成后应用了body补丁');
            }
        });
    }

    // 修改为现有脚本和样式表添加crossorigin属性
    function addCrossOriginToExistingResources() {
        // 处理脚本
        const scripts = document.querySelectorAll('script[src]');
        let scriptCount = 0;

        scripts.forEach(script => {
            if (!script.hasAttribute('crossorigin')) {
                script.setAttribute('crossorigin', 'anonymous');
                scriptCount++;
            }
        });

        if (scriptCount > 0) {
            console.log(`[资源加载器] 为${scriptCount}个现有脚本添加了crossorigin属性`);
        }

        // 处理样式表
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        let stylesheetCount = 0;

        stylesheets.forEach(stylesheet => {
            if (!stylesheet.hasAttribute('crossorigin')) {
                stylesheet.setAttribute('crossorigin', 'anonymous');
                stylesheetCount++;
            }
        });

        if (stylesheetCount > 0) {
            console.log(`[资源加载器] 为${stylesheetCount}个现有样式表添加了crossorigin属性`);
        }
    }

    // 为现有资源添加crossorigin属性
    addCrossOriginToExistingResources();

    // 监听DOM变化，为新添加的资源添加crossorigin属性
    function observeDOMChanges() {
        // 创建MutationObserver
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        // 检查是否是元素节点
                        if (node.nodeType === 1) {
                            // 直接处理添加的节点
                            if ((node.tagName === 'SCRIPT' && node.src) ||
                                (node.tagName === 'LINK' && node.rel === 'stylesheet')) {
                                if (!node.hasAttribute('crossorigin')) {
                                    node.setAttribute('crossorigin', 'anonymous');
                                    console.log(`[资源加载器] 为动态添加的${node.tagName.toLowerCase()}元素添加了crossorigin属性`);
                                }
                            }

                            // 处理子元素
                            const resources = node.querySelectorAll('script[src], link[rel="stylesheet"]');
                            resources.forEach(resource => {
                                if (!resource.hasAttribute('crossorigin')) {
                                    resource.setAttribute('crossorigin', 'anonymous');
                                    console.log(`[资源加载器] 为动态添加的子元素${resource.tagName.toLowerCase()}添加了crossorigin属性`);
                                }
                            });
                        }
                    });
                }
            });
        });

        // 配置观察选项
        const config = { childList: true, subtree: true };

        // 开始观察
        observer.observe(document, config);

        console.log('[资源加载器] DOM变化观察器已启动');
    }

    // 启动DOM观察器
    observeDOMChanges();

    // DOM完成加载后再次检查
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[资源加载器] DOM已加载完成，再次检查资源');
        addCrossOriginToExistingResources();
    });

    // 页面完全加载后最后检查一次
    window.addEventListener('load', function() {
        console.log('[资源加载器] 页面已加载完成，最后检查资源');
        addCrossOriginToExistingResources();
    });

    // 导出公共方法
    window.resourceLoader = {
        addCrossOriginToAll: addCrossOriginToExistingResources
    };

    console.log('[资源加载器] 初始化完成');
})();