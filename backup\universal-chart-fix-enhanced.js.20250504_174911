/**
 * 九猫 - 增强版通用图表修复脚本
 * 解决所有维度详情页面的图表加载问题
 * 版本: 2.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('增强版通用图表修复脚本已加载 v2.0.0');
    
    // 存储图表实例的全局对象
    if (!window.chartInstances) {
        window.chartInstances = {};
    }
    
    // 配置
    const CONFIG = {
        enableFallbackCharts: true,   // 启用后备图表
        maxRetries: 3,                // 最大重试次数
        delayBetweenRetries: 500,     // 重试间隔(毫秒)
        reduceAnimations: true,       // 减少动画
        useSimpleCharts: false,       // 使用简化图表(紧急情况)
        avoidChartRedraws: true,      // 避免过多重绘
        safeMode: true,               // 安全模式
        debugMode: false              // 调试模式
    };
    
    // 监听DOM加载完成事件
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟执行以确保所有资源已加载
        setTimeout(initializeChartFixes, 500);
    });
    
    // 初始化图表修复
    function initializeChartFixes() {
        console.log('初始化图表修复');
        
        // 修复替换节点错误
        fixReplaceChildError();
        
        // 修复所有图表
        fixAllCharts();
        
        // 监听页面变化
        observeDOMChanges();
        
        // 添加安全重绘功能
        addSafeRedraw();
    }
    
    // 安全地销毁图表
    function safeDestroyChart(canvas) {
        try {
            if (!canvas) return false;
            
            // 1. 尝试使用Chart.getChart方法(新版Chart.js)
            if (typeof Chart !== 'undefined' && typeof Chart.getChart === 'function') {
                const existingChart = Chart.getChart(canvas);
                if (existingChart) {
                    if (CONFIG.debugMode) console.log('使用Chart.getChart销毁图表');
                    existingChart.destroy();
                    return true;
                }
            }
            
            // 2. 检查全局存储的实例
            if (canvas.id && window.chartInstances[canvas.id]) {
                if (CONFIG.debugMode) console.log(`销毁ID为 ${canvas.id} 的存储图表实例`);
                window.chartInstances[canvas.id].destroy();
                delete window.chartInstances[canvas.id];
                return true;
            }
            
            // 3. 检查canvas._chart属性(旧版Chart.js)
            if (canvas._chart) {
                if (CONFIG.debugMode) console.log('使用canvas._chart销毁图表');
                canvas._chart.destroy();
                canvas._chart = null;
                return true;
            }
            
            return false;
        } catch (e) {
            console.error('销毁图表时出错:', e);
            return false;
        }
    }
    
    // 安全地创建图表
    function safeCreateChart(canvas, config) {
        if (!canvas || !config) {
            console.error('创建图表失败：缺少canvas或配置');
            return null;
        }
        
        try {
            // 确保Chart.js已加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载，无法创建图表');
                loadChartJS(); // 尝试动态加载Chart.js
                return null;
            }
            
            // 先销毁现有图表
            safeDestroyChart(canvas);
            
            // 优化图表配置
            const optimizedConfig = optimizeChartConfig(config);
            
            // 获取上下文
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('无法获取canvas上下文');
                return null;
            }
            
            // 创建新图表
            const chartInstance = new Chart(ctx, optimizedConfig);
            
            // 存储图表实例
            if (canvas.id) {
                window.chartInstances[canvas.id] = chartInstance;
            }
            
            return chartInstance;
        } catch (e) {
            console.error('创建图表时出错:', e);
            
            if (CONFIG.enableFallbackCharts) {
                console.log('尝试使用后备图表');
                return createFallbackChart(canvas, config);
            }
            
            return null;
        }
    }
    
    // 创建后备图表
    function createFallbackChart(canvas, config) {
        try {
            // 创建简化版配置
            const fallbackConfig = {
                type: config.type,
                data: config.data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: false,
                    plugins: {
                        legend: {
                            display: true
                        },
                        tooltip: {
                            enabled: false
                        }
                    }
                }
            };
            
            // 获取上下文
            const ctx = canvas.getContext('2d');
            
            // 创建后备图表
            return new Chart(ctx, fallbackConfig);
        } catch (e) {
            console.error('创建后备图表失败:', e);
            return null;
        }
    }
    
    // 优化图表配置
    function optimizeChartConfig(config) {
        // 创建配置副本，避免修改原始配置
        const optimizedConfig = JSON.parse(JSON.stringify(config));
        
        // 减少动画
        if (CONFIG.reduceAnimations) {
            if (!optimizedConfig.options) optimizedConfig.options = {};
            optimizedConfig.options.animation = false;
        }
        
        // 使用简化图表
        if (CONFIG.useSimpleCharts) {
            if (!optimizedConfig.options) optimizedConfig.options = {};
            if (!optimizedConfig.options.plugins) optimizedConfig.options.plugins = {};
            
            // 禁用tooltips
            optimizedConfig.options.plugins.tooltip = { enabled: false };
            
            // 简化图例
            optimizedConfig.options.plugins.legend = { 
                display: true,
                position: 'top'
            };
        }
        
        return optimizedConfig;
    }
    
    // 修复所有图表
    function fixAllCharts() {
        console.log('开始修复所有图表');
        
        // 查找所有canvas元素
        const canvases = document.querySelectorAll('canvas');
        console.log(`找到 ${canvases.length} 个canvas元素`);
        
        canvases.forEach(function(canvas) {
            try {
                // 销毁现有图表
                safeDestroyChart(canvas);
                
                // 为canvas添加特殊标记，防止重复处理
                canvas.setAttribute('data-chart-fixed', 'true');
                
                // 检查是否有维度属性
                const dimension = canvas.getAttribute('data-dimension');
                if (dimension) {
                    console.log(`处理维度 ${dimension} 的canvas`);
                    
                    // 根据维度创建适当的图表
                    createDimensionChart(canvas, dimension);
                } else if (canvas.id === 'radarChart' || canvas.id === 'barChart') {
                    console.log(`处理ID为 ${canvas.id} 的canvas`);
                    
                    // 处理详情页的图表
                    createDetailPageChart(canvas);
                }
            } catch (e) {
                console.error(`处理canvas时出错:`, e);
            }
        });
    }
    
    // 动态加载Chart.js
    function loadChartJS() {
        if (typeof Chart !== 'undefined') return; // 已加载
        
        console.log('尝试动态加载Chart.js');
        const script = document.createElement('script');
        script.src = '/static/js/lib/chart.min.js';
        script.onload = function() {
            console.log('Chart.js加载成功，重新修复图表');
            setTimeout(fixAllCharts, 500);
        };
        script.onerror = function() {
            console.error('Chart.js加载失败');
        };
        document.head.appendChild(script);
    }
    
    // 监听DOM变化
    function observeDOMChanges() {
        if (!window.MutationObserver) return;
        
        const observer = new MutationObserver(function(mutations) {
            let needsFix = false;
            
            mutations.forEach(function(mutation) {
                // 检查是否添加了新的canvas元素
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeName === 'CANVAS') {
                            needsFix = true;
                        } else if (node.querySelectorAll) {
                            const canvases = node.querySelectorAll('canvas');
                            if (canvases.length > 0) needsFix = true;
                        }
                    });
                }
            });
            
            if (needsFix && !isFixingCharts) {
                // 防止多次重复修复
                isFixingCharts = true;
                setTimeout(function() {
                    fixAllCharts();
                    isFixingCharts = false;
                }, 200);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // 标记是否正在修复图表
    let isFixingCharts = false;
    
    // 添加安全重绘功能
    function addSafeRedraw() {
        window.safeRedrawCharts = function() {
            if (isFixingCharts) return;
            
            isFixingCharts = true;
            setTimeout(function() {
                fixAllCharts();
                isFixingCharts = false;
            }, 200);
        };
    }
    
    // 修复替换节点错误
    function fixReplaceChildError() {
        // 保存原始的replaceChild方法
        const originalReplaceChild = Node.prototype.replaceChild;
        
        // 重写replaceChild方法
        Node.prototype.replaceChild = function(newChild, oldChild) {
            try {
                // 尝试销毁oldChild上的图表
                try {
                    if (oldChild.nodeName === 'CANVAS') {
                        safeDestroyChart(oldChild);
                    } else if (oldChild.querySelectorAll) {
                        const canvases = oldChild.querySelectorAll('canvas');
                        canvases.forEach(safeDestroyChart);
                    }
                } catch (e) {
                    console.error('销毁图表时出错:', e);
                }
                
                return originalReplaceChild.call(this, newChild, oldChild);
            } catch (e) {
                console.error(`replaceChild错误: ${e.message}`);
                
                // 尝试修复：如果失败，尝试先移除旧节点，再添加新节点
                try {
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }
                    this.appendChild(newChild);
                    return newChild;
                } catch (e2) {
                    console.error(`替代方法也失败: ${e2.message}`);
                    throw e; // 重新抛出原始错误
                }
            }
        };
    }
    
    // 暴露工具方法到全局
    window.chartFix = {
        fixAllCharts: fixAllCharts,
        safeDestroyChart: safeDestroyChart,
        safeCreateChart: safeCreateChart
    };
})();
