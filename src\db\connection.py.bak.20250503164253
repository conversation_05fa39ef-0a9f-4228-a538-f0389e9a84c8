"""
Database connection management for the 九猫 (Nine Cats) novel analysis system.
"""
import logging
import time
import threading
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError, OperationalError

import config
from src.models.base import Base

# 导入所有模型，确保在创建数据库表时注册它们
import src.models.api_log
import src.models.system_alert
import src.models.system_metric
import src.models.novel
import src.models.analysis_result
import src.models.analysis_checkpoint
import src.models.intermediate_result

logger = logging.getLogger(__name__)

# 数据库连接池监控
_pool_stats = {
    "total_connections": 0,
    "active_connections": 0,
    "connection_errors": 0,
    "last_error_time": 0,
    "last_error_message": "",
    "last_reset_time": 0
}

# 连接池监控锁
_pool_stats_lock = threading.Lock()

# 检查是否处于启动阶段
import os
_startup_mode = os.environ.get('STARTUP_MODE', 'True').lower() == 'true'

# 根据启动模式选择连接池大小
if _startup_mode:
    # 启动阶段使用较小的连接池，避免资源耗尽
    _pool_size = 10
    _max_overflow = 10
    logger.info("系统启动阶段，使用较小的数据库连接池配置")
else:
    # 正常运行阶段使用较大的连接池，支持并行分析
    _pool_size = 50
    _max_overflow = 50

# 创建数据库引擎，使用优化的连接池配置
engine = create_engine(
    config.DATABASE_URI,
    poolclass=QueuePool,
    pool_size=_pool_size,  # 连接池大小
    max_overflow=_max_overflow,  # 最大溢出连接数
    pool_timeout=60,  # 连接超时时间
    pool_recycle=1800,  # 每30分钟回收连接，避免连接过期
    pool_pre_ping=True,  # 在使用连接前先ping一下，确保连接有效
    echo=config.DEBUG  # 在DEBUG模式下记录所有SQL语句，帮助调试
)

# 记录连接池配置
logger.info(f"数据库连接池配置: pool_size={engine.pool.size()}, max_overflow={engine.pool._max_overflow}, pool_timeout={engine.pool._timeout}秒")

# 创建数据库表
Base.metadata.create_all(engine)

# 创建会话工厂
session_factory = sessionmaker(bind=engine)

# 创建线程安全的会话
Session = scoped_session(session_factory)

def get_session():
    """
    获取数据库会话。
    自动检查连接健康状态，如果发现问题则尝试修复。

    Returns:
        SQLAlchemy会话对象。
    """
    # 检查连接健康状态
    try:
        # 每100次请求检查一次连接健康状态
        if _pool_stats["total_connections"] % 100 == 0:
            check_connection_health()

        # 如果最近有错误，强制检查连接健康状态
        if _pool_stats["connection_errors"] > 0 and time.time() - _pool_stats["last_error_time"] < 300:
            logger.info("检测到最近有连接错误，强制检查连接健康状态")
            if not check_connection_health():
                logger.warning("连接健康检查失败，尝试重置连接池")
                dispose_engine()

        # 获取会话
        session = Session()

        # 更新统计信息
        with _pool_stats_lock:
            _pool_stats["active_connections"] = engine.pool.checkedout()
            _pool_stats["total_connections"] += 1

        return session
    except Exception as e:
        logger.error(f"获取数据库会话时出错: {str(e)}")
        # 出错时尝试重置连接池
        dispose_engine()
        # 重试一次
        return Session()

def close_session():
    """
    关闭当前线程的数据库会话。
    """
    try:
        Session.remove()
        logger.debug("数据库会话已关闭")
    except Exception as e:
        logger.error(f"关闭数据库会话时出错: {str(e)}")

def dispose_engine():
    """
    释放数据库引擎的所有连接。
    在应用重启或遇到连接池问题时调用。
    """
    try:
        engine.dispose()
        logger.info("数据库引擎连接池已释放")

        # 更新连接池统计信息
        with _pool_stats_lock:
            _pool_stats["total_connections"] = 0
            _pool_stats["active_connections"] = 0
            _pool_stats["last_reset_time"] = time.time()

    except Exception as e:
        logger.error(f"释放数据库引擎连接池时出错: {str(e)}")

def get_pool_stats():
    """
    获取连接池统计信息。

    Returns:
        连接池统计信息字典。
    """
    with _pool_stats_lock:
        # 尝试获取实时连接池信息
        try:
            stats = dict(_pool_stats)
            stats["current_pool_size"] = engine.pool.size()
            stats["current_overflow"] = engine.pool.overflow()
            stats["current_checkedin"] = engine.pool.checkedin()
            stats["current_checkout"] = engine.pool.checkedout()
            stats["pool_status"] = "正常" if stats["connection_errors"] == 0 else "警告"
            return stats
        except Exception as e:
            logger.error(f"获取连接池统计信息时出错: {str(e)}")
            return _pool_stats

def check_connection_health():
    """
    检查数据库连接健康状态，如果发现问题则尝试修复。

    Returns:
        布尔值，表示连接是否健康。
    """
    try:
        # 尝试执行简单查询
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))

        # 更新统计信息
        with _pool_stats_lock:
            _pool_stats["active_connections"] = engine.pool.checkedout()
            _pool_stats["total_connections"] = engine.pool.size() + engine.pool.overflow()

        return True
    except OperationalError as e:
        # 连接错误，记录并尝试修复
        with _pool_stats_lock:
            _pool_stats["connection_errors"] += 1
            _pool_stats["last_error_time"] = time.time()
            _pool_stats["last_error_message"] = str(e)

        logger.error(f"数据库连接错误: {str(e)}")

        # 如果错误次数超过阈值，尝试重置连接池
        if _pool_stats["connection_errors"] > 5:
            logger.warning("数据库连接错误次数过多，尝试重置连接池")
            dispose_engine()

        return False
    except SQLAlchemyError as e:
        logger.error(f"数据库查询错误: {str(e)}")
        return False
