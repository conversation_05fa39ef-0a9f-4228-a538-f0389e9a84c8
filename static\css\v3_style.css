/**
 * 九猫小说分析写作系统v3.0 - 全局样式
 */

:root {
    /* 主题颜色 */
    --primary-color: #3498db;
    --primary-light: #5dade2;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    
    /* 背景颜色 */
    --bg-color: #f8f9fa;
    --card-bg: #ffffff;
    --dark-bg: #343a40;
    
    /* 文本颜色 */
    --text-color: #333333;
    --text-light: #6c757d;
    --text-white: #ffffff;
    
    /* 边框和阴影 */
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

/* 全局样式 */
body {
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    box-shadow: 0 2px 10px var(--shadow-color);
}

.navbar-brand {
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.25rem;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 0.25rem;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px var(--shadow-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 6px 12px var(--shadow-color);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

.card-title {
    color: var(--primary-color);
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-white) !important;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary-color) !important;
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--text-white) !important;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--text-white) !important;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--text-white) !important;
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--text-white) !important;
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: var(--text-white) !important;
}

.btn-light {
    background-color: var(--text-white);
    border-color: var(--border-color);
    color: var(--text-color) !important;
}

/* 确保按钮文本可见 */
.btn, .btn-primary, .btn-secondary, .btn-success, .btn-danger, .btn-warning, .btn-info, .btn-light, .btn-dark {
    color: var(--text-white) !important;
}

.btn-outline-primary, .btn-outline-secondary, .btn-outline-success, .btn-outline-danger, .btn-outline-warning, .btn-outline-info, .btn-outline-light, .btn-outline-dark {
    color: inherit !important;
}

.btn-outline-primary:hover, .btn-outline-secondary:hover, .btn-outline-success:hover, .btn-outline-danger:hover, .btn-outline-warning:hover, .btn-outline-info:hover, .btn-outline-dark:hover {
    color: var(--text-white) !important;
}

.btn-outline-light:hover {
    color: var(--text-color) !important;
}

/* 表单样式 */
.form-control {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
}

.form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.5rem;
}

/* 页脚样式 */
.footer {
    background-color: var(--secondary-color);
    color: var(--text-white);
    padding: 1rem 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-title {
        font-size: 1.25rem;
    }
    
    .btn {
        padding: 0.375rem 0.75rem;
    }
}

/* 确保文本在各种背景下可见 */
.text-on-dark {
    color: var(--text-white) !important;
}

.text-on-light {
    color: var(--text-color) !important;
}

/* 修复按钮文本颜色 */
.btn-fix-text {
    color: var(--text-white) !important;
}

.btn-outline-fix-text {
    color: inherit !important;
}

.btn-outline-fix-text:hover {
    color: var(--text-white) !important;
}

/* 确保所有按钮文本可见 */
button, .btn, a.btn {
    color: var(--text-white) !important;
    font-weight: 500 !important;
}
