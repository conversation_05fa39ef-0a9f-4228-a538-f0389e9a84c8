"""
直接使用SQL修复章纲分析推理过程数据
"""

import sqlite3
import os
import re
import sys
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('direct_fix_reasoning.log')
    ]
)
logger = logging.getLogger(__name__)

# 数据库路径
db_path = os.path.join(os.path.dirname(__file__), 'novel_analysis.db')

def fix_chapter_outline_reasoning():
    """修复章纲分析推理过程数据"""
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 查询所有章纲分析结果
        cursor.execute("""
            SELECT car.id, car.chapter_id, car.novel_id, car.content, car.reasoning_content,
                   c.title, c.chapter_number, n.title as novel_title
            FROM chapter_analysis_results car
            JOIN chapters c ON car.chapter_id = c.id
            JOIN novels n ON car.novel_id = n.id
            WHERE car.dimension = 'chapter_outline'
        """)
        
        results = cursor.fetchall()
        logger.info(f"找到 {len(results)} 个章纲分析结果")
        
        # 处理每个结果
        for result in results:
            result_id, chapter_id, novel_id, content, reasoning_content, chapter_title, chapter_number, novel_title = result
            
            chapter_title = chapter_title or f'第{chapter_number}章'
            logger.info(f"处理章节: {chapter_title}, 小说: {novel_title}")
            
            # 检查推理过程
            if not reasoning_content:
                logger.info(f"推理过程为空，需要修复")
                
                # 查询分析过程记录
                cursor.execute("""
                    SELECT output_text
                    FROM chapter_analysis_processes
                    WHERE result_id = ? AND processing_stage = 'reasoning'
                    ORDER BY id DESC
                    LIMIT 1
                """, (result_id,))
                
                process = cursor.fetchone()
                
                if process and process[0]:
                    # 从分析过程记录中恢复推理过程
                    reasoning_content = process[0]
                    logger.info(f"从分析过程记录中恢复推理过程，长度: {len(reasoning_content)}")
                    
                    # 更新数据库
                    cursor.execute("""
                        UPDATE chapter_analysis_results
                        SET reasoning_content = ?
                        WHERE id = ?
                    """, (reasoning_content, result_id))
                    conn.commit()
                    logger.info(f"成功更新推理过程")
                else:
                    # 如果没有分析过程记录，生成新的推理过程
                    logger.info(f"没有分析过程记录，生成新的推理过程")
                    
                    # 构建推理过程内容
                    reasoning_content = f"""## 分析思路说明：
1. **内容重现与叙事梳理**：全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。内容不设字数限制，越详细越好。
2. **结构功能与定位分析**：全面分析章节的内部结构和功能定位，评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性
3. **关键事件与转折点**：详细分析本章节中的关键事件和转折点，识别并详述章节中的转折点、冲突点和高潮点
4. **人物表现与发展轨迹**：详细分析本章节中主要人物的表现、行动和发展，描述每个重要人物在本章中的言行和思想变化
5. **章节关联与叙事连贯性**：深入分析本章节与前后章节的关系和叙事连贯性，评估章节过渡的自然度和技巧多样性
6. **伏笔设置与悬念管理**：分析本章节中的伏笔设置和悬念管理，指出章节中埋下的伏笔和暗示，以及它们可能的未来发展方向
7. **主题情感与氛围营造**：评估章节的主题明确性和情感基调，分析本章节的核心主题和情感色彩
8. **创新特色与读者体验**：分析本章节的特色元素和创新点，识别章节中独特的写作技巧，评估如何吸引和维持读者兴趣

## 详细章纲分析：
{content}
"""
                    
                    # 更新数据库
                    cursor.execute("""
                        UPDATE chapter_analysis_results
                        SET reasoning_content = ?
                        WHERE id = ?
                    """, (reasoning_content, result_id))
                    conn.commit()
                    logger.info(f"成功生成并保存新的推理过程")
                    
                    # 保存分析过程记录
                    cursor.execute("""
                        INSERT INTO chapter_analysis_processes
                        (chapter_id, novel_id, result_id, dimension, processing_stage, output_text, is_successful)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (chapter_id, novel_id, result_id, "chapter_outline", "reasoning", reasoning_content, 1))
                    conn.commit()
                    logger.info(f"成功保存分析过程记录")
            else:
                logger.info(f"推理过程已存在，长度: {len(reasoning_content)}")
        
        logger.info(f"完成修复章纲分析推理过程数据")
    except Exception as e:
        logger.error(f"修复章纲分析推理过程数据时出错: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_chapter_outline_reasoning()
