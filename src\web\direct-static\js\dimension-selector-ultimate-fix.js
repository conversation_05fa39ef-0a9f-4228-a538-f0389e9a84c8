/**
 * 九猫 - 维度选择终极修复
 * 用于解决维度选择下拉菜单无法打开的问题
 * 版本: 1.0.0
 */

(function() {
    // 安全日志函数
    function safeLog(message, level = 'info') {
        try {
            const prefix = '[维度选择终极修复]';
            if (level === 'error') {
                console.error(`${prefix} ${message}`);
            } else if (level === 'warn') {
                console.warn(`${prefix} ${message}`);
            } else {
                console.log(`${prefix} ${message}`);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }

    // 维度列表
    const DIMENSIONS = [
        { key: 'character', name: '人物角色' },
        { key: 'plot', name: '情节发展' },
        { key: 'scene', name: '场景描写' },
        { key: 'emotion', name: '情感表达' },
        { key: 'theme', name: '主题思想' },
        { key: 'conflict', name: '冲突设置' },
        { key: 'narrative', name: '叙事视角' },
        { key: 'language', name: '语言风格' },
        { key: 'symbolism', name: '象征意义' },
        { key: 'cultural', name: '文化背景' },
        { key: 'pacing', name: '节奏控制' },
        { key: 'suspense', name: '悬念设计' },
        { key: 'structure', name: '结构安排' }
    ];

    // 强制显示模态框
    function forceShowModal() {
        try {
            safeLog('尝试强制显示模态框');
            
            // 查找模态框
            const modal = document.getElementById('analyzeModal');
            if (!modal) {
                safeLog('找不到分析模态框', 'error');
                return false;
            }
            
            // 强制显示模态框
            modal.style.display = 'block';
            modal.classList.add('show');
            document.body.classList.add('modal-open');
            
            // 创建背景遮罩
            let backdrop = document.querySelector('.modal-backdrop');
            if (!backdrop) {
                backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                document.body.appendChild(backdrop);
            }
            
            safeLog('模态框已强制显示');
            
            // 修复维度选择
            setTimeout(fixDimensionSelection, 100);
            
            return true;
        } catch (e) {
            safeLog(`强制显示模态框时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 使用Bootstrap API显示模态框
    function showModalWithBootstrap() {
        try {
            safeLog('尝试使用Bootstrap API显示模态框');
            
            // 查找模态框
            const modal = document.getElementById('analyzeModal');
            if (!modal) {
                safeLog('找不到分析模态框', 'error');
                return false;
            }
            
            // 检查Bootstrap是否可用
            if (typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined') {
                // 使用Bootstrap API显示模态框
                const modalInstance = new bootstrap.Modal(modal);
                modalInstance.show();
                
                safeLog('使用Bootstrap API显示模态框成功');
                
                // 修复维度选择
                setTimeout(fixDimensionSelection, 100);
                
                return true;
            } else {
                safeLog('Bootstrap API不可用', 'warn');
                return false;
            }
        } catch (e) {
            safeLog(`使用Bootstrap API显示模态框时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 使用jQuery显示模态框
    function showModalWithJQuery() {
        try {
            safeLog('尝试使用jQuery显示模态框');
            
            // 检查jQuery是否可用
            if (typeof $ !== 'undefined' && typeof $.fn.modal !== 'undefined') {
                // 使用jQuery显示模态框
                $('#analyzeModal').modal('show');
                
                safeLog('使用jQuery显示模态框成功');
                
                // 修复维度选择
                setTimeout(fixDimensionSelection, 100);
                
                return true;
            } else {
                safeLog('jQuery不可用或jQuery modal方法不可用', 'warn');
                return false;
            }
        } catch (e) {
            safeLog(`使用jQuery显示模态框时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 修复维度选择
    function fixDimensionSelection() {
        try {
            safeLog('开始修复维度选择');
            
            // 查找模态框
            const modal = document.getElementById('analyzeModal');
            if (!modal) {
                safeLog('找不到分析模态框', 'error');
                return;
            }
            
            // 查找模态框内容区域
            const modalBody = modal.querySelector('.modal-body');
            if (!modalBody) {
                safeLog('找不到模态框内容区域', 'error');
                return;
            }
            
            // 检查是否已有维度选项
            const existingDimensions = modalBody.querySelectorAll('.dimension-checkbox');
            if (existingDimensions.length > 0) {
                safeLog(`已存在 ${existingDimensions.length} 个维度选项，无需修复`);
                return;
            }
            
            // 查找全选选项
            let selectAllCheckbox = modalBody.querySelector('#select-all-dimensions');
            if (!selectAllCheckbox) {
                safeLog('未找到全选选项，尝试创建');
                
                // 创建全选选项
                const selectAllContainer = document.createElement('div');
                selectAllContainer.className = 'form-check';
                
                selectAllCheckbox = document.createElement('input');
                selectAllCheckbox.type = 'checkbox';
                selectAllCheckbox.className = 'form-check-input';
                selectAllCheckbox.id = 'select-all-dimensions';
                selectAllCheckbox.checked = true;
                
                const selectAllLabel = document.createElement('label');
                selectAllLabel.className = 'form-check-label';
                selectAllLabel.htmlFor = 'select-all-dimensions';
                selectAllLabel.innerHTML = '<strong>全选</strong>';
                
                selectAllContainer.appendChild(selectAllCheckbox);
                selectAllContainer.appendChild(selectAllLabel);
                
                // 查找表单
                const form = modal.querySelector('form');
                if (form) {
                    // 添加到表单
                    form.appendChild(selectAllContainer);
                } else {
                    // 添加到模态框内容区域
                    modalBody.appendChild(selectAllContainer);
                }
            }
            
            // 查找分隔线
            let hr = modalBody.querySelector('hr');
            if (!hr) {
                safeLog('未找到分隔线，尝试创建');
                
                // 创建分隔线
                hr = document.createElement('hr');
                
                // 添加到全选选项后面
                selectAllCheckbox.closest('.form-check').after(hr);
            }
            
            // 创建维度选项容器
            const dimensionsContainer = document.createElement('div');
            dimensionsContainer.id = 'dimensions-container';
            dimensionsContainer.className = 'mb-3';
            
            // 添加维度选项
            DIMENSIONS.forEach((dimension, index) => {
                const dimensionItem = document.createElement('div');
                dimensionItem.className = 'form-check';
                dimensionItem.style.marginBottom = '8px';
                
                const dimensionCheckbox = document.createElement('input');
                dimensionCheckbox.type = 'checkbox';
                dimensionCheckbox.className = 'form-check-input dimension-checkbox';
                dimensionCheckbox.id = `dimension-${index}`;
                dimensionCheckbox.value = dimension.key;
                dimensionCheckbox.dataset.dimension = dimension.key;
                dimensionCheckbox.dataset.dimensionName = dimension.name;
                dimensionCheckbox.checked = true;
                dimensionCheckbox.name = 'dimensions';
                
                const dimensionLabel = document.createElement('label');
                dimensionLabel.className = 'form-check-label';
                dimensionLabel.htmlFor = `dimension-${index}`;
                dimensionLabel.textContent = dimension.name;
                
                dimensionItem.appendChild(dimensionCheckbox);
                dimensionItem.appendChild(dimensionLabel);
                dimensionsContainer.appendChild(dimensionItem);
            });
            
            // 将维度选项容器插入到分隔线后面
            hr.after(dimensionsContainer);
            
            safeLog(`成功添加了 ${DIMENSIONS.length} 个维度选项`);
            
            // 设置全选选项的事件处理
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = dimensionsContainer.querySelectorAll('.dimension-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
            
            safeLog('维度选择修复完成');
        } catch (e) {
            safeLog(`修复维度选择时出错: ${e.message}`, 'error');
        }
    }

    // 监听分析按钮点击
    function setupButtonListeners() {
        try {
            safeLog('设置按钮监听器');
            
            document.addEventListener('click', function(event) {
                // 查找可能是分析按钮的元素
                const target = event.target;
                
                // 检查是否点击了分析按钮
                if ((target.classList && 
                     (target.classList.contains('analyze-btn') || 
                      target.classList.contains('btn-analyze'))) ||
                    (target.getAttribute && target.getAttribute('data-bs-target') === '#analyzeModal') ||
                    (target.textContent && target.textContent.includes('分析')) ||
                    (target.id && target.id.includes('analyze'))) {
                    
                    safeLog('检测到分析按钮点击');
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // 尝试多种方式显示模态框
                    if (!showModalWithBootstrap() && !showModalWithJQuery()) {
                        forceShowModal();
                    }
                }
                
                // 也检查父元素
                const parent = target.parentElement;
                if (parent && 
                    ((parent.classList && 
                      (parent.classList.contains('analyze-btn') || 
                       parent.classList.contains('btn-analyze'))) ||
                     (parent.getAttribute && parent.getAttribute('data-bs-target') === '#analyzeModal') ||
                     (parent.textContent && parent.textContent.includes('分析')) ||
                     (parent.id && parent.id.includes('analyze')))) {
                    
                    safeLog('检测到分析按钮容器点击');
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // 尝试多种方式显示模态框
                    if (!showModalWithBootstrap() && !showModalWithJQuery()) {
                        forceShowModal();
                    }
                }
            });
            
            safeLog('按钮监听器已设置');
        } catch (e) {
            safeLog(`设置按钮监听器时出错: ${e.message}`, 'error');
        }
    }

    // 初始化
    function init() {
        safeLog('初始化维度选择终极修复');
        
        // 设置按钮监听器
        setupButtonListeners();
        
        // 导出全局函数
        window.dimensionSelectorUltimateFix = {
            fixDimensionSelection: fixDimensionSelection,
            forceShowModal: forceShowModal
        };
        
        safeLog('维度选择终极修复初始化完成');
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    safeLog('维度选择终极修复脚本已加载');
})();
