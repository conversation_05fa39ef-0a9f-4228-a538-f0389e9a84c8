{% extends "new_base.html" %}

{% block title %}上传小说 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10 col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title mb-0"><i class="fas fa-upload me-2"></i>上传小说</h2>
            </div>
            <div class="card-body">
                {% if error %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endif %}
                
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="mb-4">
                        <label for="title" class="form-label">小说标题 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required>
                        <div class="form-text">请输入小说的标题，这将用于在系统中识别您的作品。</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="author" class="form-label">作者</label>
                        <input type="text" class="form-control" id="author" name="author">
                        <div class="form-text">可选，请输入小说的作者名称。</div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">上传方式 <span class="text-danger">*</span></label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="uploadType" id="uploadTypeFile" value="file" checked>
                            <label class="form-check-label" for="uploadTypeFile">
                                上传文件
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="uploadType" id="uploadTypeText" value="text">
                            <label class="form-check-label" for="uploadTypeText">
                                粘贴文本
                            </label>
                        </div>
                    </div>
                    
                    <div id="fileUploadSection" class="mb-4">
                        <label for="file" class="form-label">选择文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="file" name="file" accept=".txt,.doc,.docx,.pdf">
                        <div class="form-text">支持的文件格式：TXT, DOC, DOCX, PDF。最大文件大小：10MB。</div>
                        
                        <div class="mt-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-info-circle me-2"></i>文件上传提示</h5>
                                    <ul class="mb-0">
                                        <li>请确保文件编码为UTF-8，以避免中文乱码问题。</li>
                                        <li>建议使用纯文本格式（TXT）以获得最佳分析效果。</li>
                                        <li>如果您的文件较大，上传可能需要一些时间，请耐心等待。</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="textUploadSection" class="mb-4" style="display: none;">
                        <label for="content" class="form-label">粘贴文本内容 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="content" name="content" rows="10"></textarea>
                        <div class="form-text">请直接粘贴小说文本内容。最大支持100万字符。</div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">分析选项</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoAnalyze" name="autoAnalyze" checked>
                            <label class="form-check-label" for="autoAnalyze">上传后自动开始分析</label>
                        </div>
                        
                        <div id="analysisOptionsSection" class="mt-3">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">选择分析维度</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="language_style" name="dimensions" value="language_style" checked>
                                                <label class="form-check-label" for="language_style">语言风格</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="rhythm_pacing" name="dimensions" value="rhythm_pacing" checked>
                                                <label class="form-check-label" for="rhythm_pacing">节奏与节奏</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="structure" name="dimensions" value="structure" checked>
                                                <label class="form-check-label" for="structure">结构分析</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="sentence_variation" name="dimensions" value="sentence_variation">
                                                <label class="form-check-label" for="sentence_variation">句式变化</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="paragraph_length" name="dimensions" value="paragraph_length">
                                                <label class="form-check-label" for="paragraph_length">段落长度</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="perspective_shifts" name="dimensions" value="perspective_shifts">
                                                <label class="form-check-label" for="perspective_shifts">视角转换</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="paragraph_flow" name="dimensions" value="paragraph_flow">
                                                <label class="form-check-label" for="paragraph_flow">段落流畅度</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="novel_characteristics" name="dimensions" value="novel_characteristics">
                                                <label class="form-check-label" for="novel_characteristics">小说特点</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="world_building" name="dimensions" value="world_building">
                                                <label class="form-check-label" for="world_building">世界构建</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="chapter_outline" name="dimensions" value="chapter_outline">
                                                <label class="form-check-label" for="chapter_outline">章节大纲</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="character_relationships" name="dimensions" value="character_relationships">
                                                <label class="form-check-label" for="character_relationships">人物关系</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="opening_effectiveness" name="dimensions" value="opening_effectiveness">
                                                <label class="form-check-label" for="opening_effectiveness">开篇效果</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input dimension-checkbox" type="checkbox" id="climax_pacing" name="dimensions" value="climax_pacing">
                                                <label class="form-check-label" for="climax_pacing">高潮节奏</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllDimensions">全选</button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllDimensions">取消全选</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-upload me-2"></i>上传并分析
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 上传方式切换
        const uploadTypeFile = document.getElementById('uploadTypeFile');
        const uploadTypeText = document.getElementById('uploadTypeText');
        const fileUploadSection = document.getElementById('fileUploadSection');
        const textUploadSection = document.getElementById('textUploadSection');
        
        uploadTypeFile.addEventListener('change', function() {
            if (this.checked) {
                fileUploadSection.style.display = '';
                textUploadSection.style.display = 'none';
                document.getElementById('file').required = true;
                document.getElementById('content').required = false;
            }
        });
        
        uploadTypeText.addEventListener('change', function() {
            if (this.checked) {
                fileUploadSection.style.display = 'none';
                textUploadSection.style.display = '';
                document.getElementById('file').required = false;
                document.getElementById('content').required = true;
            }
        });
        
        // 自动分析选项切换
        const autoAnalyze = document.getElementById('autoAnalyze');
        const analysisOptionsSection = document.getElementById('analysisOptionsSection');
        
        autoAnalyze.addEventListener('change', function() {
            analysisOptionsSection.style.display = this.checked ? '' : 'none';
        });
        
        // 分析维度全选/取消全选
        const selectAllDimensions = document.getElementById('selectAllDimensions');
        const deselectAllDimensions = document.getElementById('deselectAllDimensions');
        const dimensionCheckboxes = document.querySelectorAll('.dimension-checkbox');
        
        selectAllDimensions.addEventListener('click', function() {
            dimensionCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        });
        
        deselectAllDimensions.addEventListener('click', function() {
            dimensionCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        });
        
        // 表单提交验证
        const uploadForm = document.getElementById('uploadForm');
        
        uploadForm.addEventListener('submit', function(event) {
            // 检查是否选择了文件或输入了文本
            const uploadTypeFile = document.getElementById('uploadTypeFile').checked;
            const file = document.getElementById('file').files[0];
            const content = document.getElementById('content').value;
            
            if (uploadTypeFile && !file) {
                event.preventDefault();
                alert('请选择要上传的文件');
                return;
            }
            
            if (!uploadTypeFile && !content) {
                event.preventDefault();
                alert('请输入小说文本内容');
                return;
            }
            
            // 如果启用了自动分析，检查是否选择了至少一个分析维度
            const autoAnalyze = document.getElementById('autoAnalyze').checked;
            if (autoAnalyze) {
                const selectedDimensions = document.querySelectorAll('.dimension-checkbox:checked');
                if (selectedDimensions.length === 0) {
                    event.preventDefault();
                    alert('请至少选择一个分析维度');
                    return;
                }
            }
        });
    });
</script>
{% endblock %}
