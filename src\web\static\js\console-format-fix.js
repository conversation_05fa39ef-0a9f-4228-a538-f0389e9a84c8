/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 控制台格式修复工具
 * 
 * 这个脚本用于修复控制台错误显示中的格式问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('控制台格式修复工具已加载');
    
    // 存储原始控制台方法
    const originalConsole = {
        log: console.log,
        error: console.error,
        warn: console.warn,
        info: console.info,
        debug: console.debug
    };
    
    // 错误计数器
    let errorCounter = 0;
    
    // 修复控制台错误格式
    function fixConsoleErrorFormat() {
        // 重写console.error方法
        console.error = function(...args) {
            // 检查是否是我们要修复的格式
            if (args.length === 1 && typeof args[0] === 'string') {
                const errorMsg = args[0];
                
                // 修复格式问题
                if (errorMsg.includes('人类易读模式:') && errorMsg.includes('].')) {
                    // 修复缺少左引号的问题
                    const fixedMsg = errorMsg.replace('人类易读模式:', '人类易读模式: "').replace('].', '"] ');
                    return originalConsole.error(fixedMsg);
                }
                
                // 修复使用点号而不是空格的问题
                if (errorMsg.includes('位置:') && errorMsg.includes('.行:')) {
                    const fixedMsg = errorMsg.replace('.行:', ' 行:').replace('.列:', ' 列:');
                    return originalConsole.error(fixedMsg);
                }
            }
            
            // 默认行为
            return originalConsole.error(...args);
        };
    }
    
    // 修复window.onerror
    function fixWindowOnError() {
        // 保存原始错误处理函数
        const originalOnError = window.onerror;
        
        // 设置新的错误处理函数
        window.onerror = function(message, source, lineno, colno, error) {
            // 增加错误计数
            errorCounter++;
            
            // 在控制台中显示格式化的错误信息
            originalConsole.error(`[原始错误 #${errorCounter}] ${message}`);
            originalConsole.error(`位置: ${source} 行: ${lineno} 列: ${colno}`);
            if (error && error.stack) {
                originalConsole.error(`堆栈: ${error.stack}`);
            }
            
            // 如果有原始错误处理函数，调用它
            if (typeof originalOnError === 'function') {
                return originalOnError(message, source, lineno, colno, error);
            }
            
            // 不阻止默认错误处理
            return false;
        };
    }
    
    // 主函数
    function main() {
        fixConsoleErrorFormat();
        fixWindowOnError();
        
        console.log('控制台格式修复已应用');
    }
    
    // 立即执行
    main();
})();
