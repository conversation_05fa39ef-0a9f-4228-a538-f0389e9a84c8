{% extends "base.html" %}

{% block title %}{{ novel.title }} - 章节分割预览{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">小说列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">章节分割预览</li>
        </ol>
    </nav>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2>{{ novel.title }} - 章节分割预览</h2>
            <div>
                <button type="button" class="btn btn-primary" id="confirmSplitBtn">
                    确认分割
                </button>
                <button type="button" class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#splitOptionsModal">
                    分割选项
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <p><strong>作者：</strong>{{ novel.author or '未知' }}</p>
                <p><strong>总字数：</strong>{{ novel.word_count }}</p>
                <p><strong>预计章节数：</strong>{{ chapters|length }}</p>
            </div>

            <!-- 分割进度条 -->
            <div id="splitProgressContainer" class="mb-4" style="display: none;">
                <h4>分割进度</h4>
                <div class="progress mb-2">
                    <div id="splitProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div id="splitStatus" class="text-muted">准备中...</div>
            </div>

            <!-- 章节预览列表 -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>章节</th>
                            <th>标题</th>
                            <th>内容预览</th>
                            <th>字数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for chapter in chapters %}
                        <tr>
                            <td>{{ chapter.chapter_number }}</td>
                            <td>
                                <input type="text" class="form-control chapter-title"
                                       data-chapter-index="{{ loop.index0 }}"
                                       value="{{ chapter.title }}">
                            </td>
                            <td>
                                <div class="chapter-preview">{{ chapter.preview }}</div>
                            </td>
                            <td>{{ chapter.word_count }}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary view-chapter-btn"
                                        data-chapter-index="{{ loop.index0 }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#chapterPreviewModal">
                                    查看
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-chapter-btn"
                                        data-chapter-index="{{ loop.index0 }}">
                                    删除
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 章节预览模态框 -->
<div class="modal fade" id="chapterPreviewModal" tabindex="-1" aria-labelledby="chapterPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chapterPreviewModalLabel">章节预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="previewChapterTitle" class="form-label">章节标题</label>
                    <input type="text" class="form-control" id="previewChapterTitle">
                </div>
                <div class="mb-3">
                    <label for="previewChapterContent" class="form-label">章节内容</label>
                    <textarea class="form-control" id="previewChapterContent" rows="15"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="saveChapterBtn">保存修改</button>
            </div>
        </div>
    </div>
</div>

<!-- 分割选项模态框 -->
<div class="modal fade" id="splitOptionsModal" tabindex="-1" aria-labelledby="splitOptionsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="splitOptionsModalLabel">章节分割选项</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="splitOptionsForm">
                    <div class="mb-3">
                        <label for="minChapterLength" class="form-label">最小章节长度（字符数）</label>
                        <input type="number" class="form-control" id="minChapterLength" value="100" min="10" max="1000">
                        <div class="form-text">小于此长度的章节将被忽略</div>
                    </div>
                    <div class="mb-3">
                        <label for="maxChapterLength" class="form-label">最大章节长度（字符数）</label>
                        <input type="number" class="form-control" id="maxChapterLength" value="50000" min="1000" max="100000">
                        <div class="form-text">超过此长度的章节将被尝试再次分割</div>
                    </div>
                    <div class="mb-3">
                        <label for="splitMethod" class="form-label">分割方法</label>
                        <select class="form-select" id="splitMethod">
                            <option value="auto" selected>自动选择最佳方法</option>
                            <option value="pattern">使用章节标记模式匹配</option>
                            <option value="marker">使用章节标记分割</option>
                            <option value="paragraph">使用段落分割</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="applySplitOptionsBtn">应用并重新分割</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const novelId = {{ novel.id }};
        let chapters = {{ chapters_json|safe }};
        let currentChapterIndex = -1;

        // 查看章节按钮
        document.querySelectorAll('.view-chapter-btn').forEach(button => {
            button.addEventListener('click', function() {
                const chapterIndex = parseInt(this.dataset.chapterIndex);
                currentChapterIndex = chapterIndex;
                const chapter = chapters[chapterIndex];

                document.getElementById('previewChapterTitle').value = chapter.title;
                document.getElementById('previewChapterContent').value = chapter.content || "加载中...";

                // 如果没有完整内容，则加载
                if (!chapter.content) {
                    fetch(`/api/novel/${novelId}/chapter_preview/${chapterIndex}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                chapter.content = data.content;
                                document.getElementById('previewChapterContent').value = data.content;
                            } else {
                                document.getElementById('previewChapterContent').value = "加载失败: " + data.error;
                            }
                        })
                        .catch(error => {
                            console.error('加载章节内容时出错:', error);
                            document.getElementById('previewChapterContent').value = "加载出错";
                        });
                }
            });
        });

        // 保存章节修改按钮
        document.getElementById('saveChapterBtn').addEventListener('click', function() {
            if (currentChapterIndex >= 0) {
                const title = document.getElementById('previewChapterTitle').value;
                const content = document.getElementById('previewChapterContent').value;

                // 更新内存中的章节
                chapters[currentChapterIndex].title = title;
                chapters[currentChapterIndex].content = content;

                // 更新UI
                const titleInput = document.querySelector(`.chapter-title[data-chapter-index="${currentChapterIndex}"]`);
                if (titleInput) {
                    titleInput.value = title;
                }

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('chapterPreviewModal'));
                modal.hide();
            }
        });

        // 删除章节按钮
        document.querySelectorAll('.delete-chapter-btn').forEach(button => {
            button.addEventListener('click', function() {
                if (confirm('确定要删除这个章节吗？')) {
                    const chapterIndex = parseInt(this.dataset.chapterIndex);

                    // 从内存中删除章节
                    chapters.splice(chapterIndex, 1);

                    // 重新加载页面以更新UI
                    window.location.reload();
                }
            });
        });

        // 章节标题输入框
        document.querySelectorAll('.chapter-title').forEach(input => {
            input.addEventListener('change', function() {
                const chapterIndex = parseInt(this.dataset.chapterIndex);
                chapters[chapterIndex].title = this.value;
            });
        });

        // 确认分割按钮
        document.getElementById('confirmSplitBtn').addEventListener('click', function() {
            if (confirm('确定要使用当前分割结果创建章节吗？')) {
                // 显示进度条
                document.getElementById('splitProgressContainer').style.display = 'block';
                document.getElementById('splitProgressBar').style.width = '0%';
                document.getElementById('splitProgressBar').setAttribute('aria-valuenow', 0);
                document.getElementById('splitProgressBar').textContent = '0%';
                document.getElementById('splitStatus').textContent = '正在创建章节...';

                // 发送请求
                fetch(`/api/novel/${novelId}/confirm_split`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        chapters: chapters
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新进度条
                        document.getElementById('splitProgressBar').style.width = '100%';
                        document.getElementById('splitProgressBar').setAttribute('aria-valuenow', 100);
                        document.getElementById('splitProgressBar').textContent = '100%';
                        document.getElementById('splitStatus').textContent = '章节创建完成';
                        document.getElementById('splitProgressBar').classList.remove('progress-bar-animated');
                        document.getElementById('splitProgressBar').classList.remove('progress-bar-striped');
                        document.getElementById('splitProgressBar').classList.add('bg-success');

                        // 2秒后跳转到章节列表页面
                        setTimeout(function() {
                            window.location.href = `/novel/${novelId}/chapters`;
                        }, 2000);
                    } else {
                        // 更新进度条
                        document.getElementById('splitProgressBar').style.width = '100%';
                        document.getElementById('splitProgressBar').setAttribute('aria-valuenow', 100);
                        document.getElementById('splitProgressBar').textContent = '失败';
                        document.getElementById('splitStatus').textContent = '章节创建失败: ' + data.error;
                        document.getElementById('splitProgressBar').classList.remove('progress-bar-animated');
                        document.getElementById('splitProgressBar').classList.remove('progress-bar-striped');
                        document.getElementById('splitProgressBar').classList.add('bg-danger');
                    }
                })
                .catch(error => {
                    console.error('创建章节时出错:', error);

                    // 更新进度条
                    document.getElementById('splitProgressBar').style.width = '100%';
                    document.getElementById('splitProgressBar').setAttribute('aria-valuenow', 100);
                    document.getElementById('splitProgressBar').textContent = '错误';
                    document.getElementById('splitStatus').textContent = '创建章节出错';
                    document.getElementById('splitProgressBar').classList.remove('progress-bar-animated');
                    document.getElementById('splitProgressBar').classList.remove('progress-bar-striped');
                    document.getElementById('splitProgressBar').classList.add('bg-danger');
                });
            }
        });

        // 应用分割选项按钮
        document.getElementById('applySplitOptionsBtn').addEventListener('click', function() {
            const minChapterLength = parseInt(document.getElementById('minChapterLength').value);
            const maxChapterLength = parseInt(document.getElementById('maxChapterLength').value);
            const splitMethod = document.getElementById('splitMethod').value;

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('splitOptionsModal'));
            modal.hide();

            // 显示进度条
            document.getElementById('splitProgressContainer').style.display = 'block';
            document.getElementById('splitProgressBar').style.width = '0%';
            document.getElementById('splitProgressBar').setAttribute('aria-valuenow', 0);
            document.getElementById('splitProgressBar').textContent = '0%';
            document.getElementById('splitStatus').textContent = '正在重新分割章节...';

            // 发送请求
            fetch(`/api/novel/${novelId}/preview_split`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    min_chapter_length: minChapterLength,
                    max_chapter_length: maxChapterLength,
                    split_method: splitMethod
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新进度条
                    document.getElementById('splitProgressBar').style.width = '100%';
                    document.getElementById('splitProgressBar').setAttribute('aria-valuenow', 100);
                    document.getElementById('splitProgressBar').textContent = '100%';
                    document.getElementById('splitStatus').textContent = '章节分割完成';
                    document.getElementById('splitProgressBar').classList.remove('progress-bar-animated');
                    document.getElementById('splitProgressBar').classList.remove('progress-bar-striped');
                    document.getElementById('splitProgressBar').classList.add('bg-success');

                    // 刷新页面
                    window.location.reload();
                } else {
                    // 更新进度条
                    document.getElementById('splitProgressBar').style.width = '100%';
                    document.getElementById('splitProgressBar').setAttribute('aria-valuenow', 100);
                    document.getElementById('splitProgressBar').textContent = '失败';
                    document.getElementById('splitStatus').textContent = '章节分割失败: ' + data.error;
                    document.getElementById('splitProgressBar').classList.remove('progress-bar-animated');
                    document.getElementById('splitProgressBar').classList.remove('progress-bar-striped');
                    document.getElementById('splitProgressBar').classList.add('bg-danger');
                }
            })
            .catch(error => {
                console.error('分割章节时出错:', error);

                // 更新进度条
                document.getElementById('splitProgressBar').style.width = '100%';
                document.getElementById('splitProgressBar').setAttribute('aria-valuenow', 100);
                document.getElementById('splitProgressBar').textContent = '错误';
                document.getElementById('splitStatus').textContent = '分割章节出错';
                document.getElementById('splitProgressBar').classList.remove('progress-bar-animated');
                document.getElementById('splitProgressBar').classList.remove('progress-bar-striped');
                document.getElementById('splitProgressBar').classList.add('bg-danger');
            });
        });
    });
</script>
{% endblock %}

{% block styles %}
<style>
    .chapter-preview {
        max-height: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    #previewChapterContent {
        font-family: monospace;
        white-space: pre-wrap;
    }
</style>
{% endblock %}
