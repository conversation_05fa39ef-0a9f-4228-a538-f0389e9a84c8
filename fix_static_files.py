"""
九猫小说分析系统 - 静态文件修复工具

此脚本用于修复静态文件路径问题，将lib子目录中的库文件复制到主静态目录中，
解决"Failed to load resource: the server responded with a status of 404 (NOT FOUND)"错误。
"""
import os
import shutil
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('static_files_fix.log')
    ]
)
logger = logging.getLogger(__name__)

def fix_static_files():
    """修复静态文件路径问题"""
    logger.info("开始修复静态文件路径问题...")
    
    # 获取当前工作目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义静态文件目录
    static_dir = os.path.join(base_dir, 'src', 'web', 'static')
    css_dir = os.path.join(static_dir, 'css')
    js_dir = os.path.join(static_dir, 'js')
    css_lib_dir = os.path.join(css_dir, 'lib')
    js_lib_dir = os.path.join(js_dir, 'lib')
    
    # 确保目录存在
    os.makedirs(css_dir, exist_ok=True)
    os.makedirs(js_dir, exist_ok=True)
    
    # 需要复制的文件映射 {源文件: 目标文件}
    files_to_copy = {
        os.path.join(css_lib_dir, 'bootstrap.min.css'): os.path.join(css_dir, 'bootstrap.min.css'),
        os.path.join(js_lib_dir, 'jquery.min.js'): os.path.join(js_dir, 'jquery.min.js'),
        os.path.join(js_lib_dir, 'bootstrap.bundle.min.js'): os.path.join(js_dir, 'bootstrap.bundle.min.js'),
        os.path.join(js_lib_dir, 'chart.min.js'): os.path.join(js_dir, 'chart.min.js')
    }
    
    # 复制文件
    for src, dst in files_to_copy.items():
        try:
            if os.path.exists(src):
                shutil.copy2(src, dst)
                logger.info(f"已复制 {os.path.basename(src)} 到 {os.path.dirname(dst)}")
            else:
                logger.warning(f"源文件不存在: {src}")
                
                # 尝试从备用位置复制
                alt_src = None
                if 'jquery.min.js' in src:
                    alt_src = os.path.join(js_lib_dir, 'jquery-3.6.0.min.js')
                
                if alt_src and os.path.exists(alt_src):
                    shutil.copy2(alt_src, dst)
                    logger.info(f"已从备用位置复制 {os.path.basename(alt_src)} 到 {dst}")
                else:
                    logger.error(f"无法找到 {os.path.basename(src)} 的有效源文件")
        except Exception as e:
            logger.error(f"复制 {src} 到 {dst} 时出错: {str(e)}")
    
    # 检查修复结果
    missing_files = []
    for _, dst in files_to_copy.items():
        if not os.path.exists(dst):
            missing_files.append(dst)
    
    if missing_files:
        logger.warning(f"以下文件仍然缺失: {', '.join([os.path.basename(f) for f in missing_files])}")
        return False
    else:
        logger.info("所有静态文件已成功修复")
        return True

if __name__ == "__main__":
    success = fix_static_files()
    if success:
        print("静态文件修复成功！请重新启动九猫系统。")
    else:
        print("静态文件修复部分成功，可能仍有一些文件缺失。请检查日志获取详细信息。")
