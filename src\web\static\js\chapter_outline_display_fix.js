/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 章节大纲分析内容完整展示补丁 - 增强版 v2.1
 *
 * 此脚本完全移除前端对章纲分析内容的任何长度限制，
 * 确保"主要内容"部分完整展示，无字数限制
 * 适用于整本书分析维度和单章节分析维度
 */

// 防止脚本重复执行
if (window.chapterOutlineFixApplied !== true) {
    window.chapterOutlineFixApplied = true;

    (function() {
        console.log('章纲分析内容完整展示增强补丁已加载');

        // 在页面加载完成后执行修复
        document.addEventListener('DOMContentLoaded', function() {
            // 应用章纲分析内容展示修复
            console.log('应用章纲分析内容展示增强修复');
            fixChapterOutlineDisplay();

            // 清除可能存在的旧观察者
            if (window.chapterOutlineFixObserver) {
                window.chapterOutlineFixObserver.disconnect();
            }

            // 监听页面动态加载的内容，使用节流函数减少调用频率
            let throttleTimer;
            const throttledFixChapterOutlineDisplay = () => {
                if (!throttleTimer) {
                    throttleTimer = setTimeout(() => {
                        fixChapterOutlineDisplay();
                        throttleTimer = null;
                    }, 1000); // 1秒内最多执行一次
                }
            };

            window.chapterOutlineFixObserver = new MutationObserver(function(mutations) {
                throttledFixChapterOutlineDisplay();
            });

            // 开始监听文档变化
            window.chapterOutlineFixObserver.observe(document.body, { childList: true, subtree: true });

            // 清除可能存在的旧定时器
            if (window.chapterOutlineFixInterval) {
                clearInterval(window.chapterOutlineFixInterval);
            }

            // 定期检查和修复 - 确保在动态加载后也能正确应用，但降低频率
            window.chapterOutlineFixInterval = setInterval(fixChapterOutlineDisplay, 5000);
        });

        // 修复章纲分析内容展示
        function fixChapterOutlineDisplay() {
            // 查找所有可能包含章纲分析的元素 - 扩大选择器范围
            const outlineElements = document.querySelectorAll(
                '.chapter-outline-content, ' +
                '[data-dimension="chapter_outline"] .content, ' +
                '[data-dimension="chapter_outline"] .analysis-content, ' +
                '.analysis-content, ' +
                '.markdown-content, ' +
                '.analysis-result .content, ' +
                '.result-content'
            );

            // 减少日志输出，只在调试模式下输出
            if (window.debugMode) {
                console.log(`找到 ${outlineElements.length} 个可能包含章纲分析的元素`);
            }

            outlineElements.forEach(function(element) {
                // 移除所有可能的限制样式
                element.style.maxHeight = 'none';
                element.style.overflow = 'visible';
                element.style.textOverflow = 'clip';
                element.style.whiteSpace = 'normal';

                // 查找包含主要内容的部分
                const contentText = element.innerHTML;
                if (contentText.includes('主要内容') ||
                    contentText.includes('章节大纲') ||
                    contentText.includes('章纲分析')) {

                    // 减少日志输出，只在调试模式下输出
                    if (window.debugMode) {
                        console.log('发现包含章纲内容的元素，确保完整展示');
                    }

                    // 移除任何可能的截断类
                    element.classList.remove('truncated', 'collapsed', 'limited-height', 'excerpt');

                    // 处理可能嵌套的内容容器
                    const nestedContainers = element.querySelectorAll('div, p, section, article');
                    nestedContainers.forEach(function(container) {
                        container.style.maxHeight = 'none';
                        container.style.overflow = 'visible';
                        container.style.display = 'block';
                        container.classList.remove('truncated', 'collapsed', 'limited-height', 'excerpt');
                    });

                    // 如果有"显示更多"按钮，模拟点击或移除
                    const showMoreButtons = document.querySelectorAll(
                        '.show-more-btn, .expand-btn, .read-more, .more-btn, ' +
                        '[data-action="expand"], [data-action="show-more"], ' +
                        '.toggle-content-btn, .toggle-expand-btn'
                    );

                    showMoreButtons.forEach(function(btn) {
                        // 减少日志输出，只在调试模式下输出
                        if (window.debugMode) {
                            console.log('发现"显示更多"按钮，模拟点击');
                        }
                        try {
                            btn.click();
                        } catch(e) {
                            if (window.debugMode) {
                                console.log('点击按钮失败，尝试移除按钮', e);
                            }
                            btn.style.display = 'none';
                        }
                    });

                    // 强制展开包含"主要内容"的父容器
                    let parentElement = element.parentElement;
                    for (let i = 0; i < 5 && parentElement; i++) {  // 最多向上查找5层
                        parentElement.style.maxHeight = 'none';
                        parentElement.style.overflow = 'visible';
                        parentElement = parentElement.parentElement;
                    }
                }
            });

            // 特殊处理：查找并展开所有章节分析中的详情元素
            const detailsElements = document.querySelectorAll('details');
            detailsElements.forEach(function(details) {
                if (details.innerHTML.includes('主要内容')) {
                    details.setAttribute('open', 'true');
                    // 减少日志输出，只在调试模式下输出
                    if (window.debugMode) {
                        console.log('自动展开包含章纲分析的details元素');
                    }
                }
            });

            // 减少日志输出，只在调试模式下输出
            if (window.debugMode) {
                console.log('章纲分析内容展示增强修复完成');
            }
        }

        // 添加CSS样式以确保内容完整显示
        function addStyles() {
            // 检查是否已添加样式
            if (document.getElementById('chapter-outline-display-fix-styles')) {
                return;
            }

            const styleElement = document.createElement('style');
            styleElement.id = 'chapter-outline-display-fix-styles';
            styleElement.textContent = `
                /* 确保章纲分析内容完整显示 */
                .chapter-outline-content,
                [data-dimension="chapter_outline"] .content,
                [data-dimension="chapter_outline"] .analysis-content,
                .analysis-content,
                .markdown-content,
                .result-content {
                    max-height: none !important;
                    overflow: visible !important;
                    text-overflow: clip !important;
                    white-space: normal !important;
                    display: block !important;
                }

                /* 隐藏所有可能的"显示更多"按钮 */
                .show-more-btn, .expand-btn, .read-more, .more-btn,
                [data-action="expand"], [data-action="show-more"],
                .toggle-content-btn, .toggle-expand-btn {
                    display: none !important;
                }
            `;
            document.head.appendChild(styleElement);
            console.log('添加章纲分析内容展示样式');
        }

        // 添加样式
        addStyles();

        console.log('章纲分析内容完整展示增强补丁初始化完成');
    })();
} else {
    console.log('章纲分析内容完整展示增强补丁已经加载，跳过重复执行');
}
