"""
数据库迁移脚本
用于更新数据库结构，添加缺少的列
"""
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from sqlalchemy import Column, Integer, Float, String, DateTime, Text, JSON, MetaData, Table
from sqlalchemy.sql import text

import config
from src.db.connection import engine, Session

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("db_migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_column_exists(table_name, column_name):
    """检查列是否存在"""
    try:
        session = Session()
        # 使用PRAGMA table_info查询表结构（SQLite）
        if 'sqlite' in config.DATABASE_URI:
            result = session.execute(text(f"PRAGMA table_info({table_name})")).fetchall()
            columns = [row[1] for row in result]
            return column_name in columns
        # 使用information_schema查询表结构（MySQL/PostgreSQL）
        else:
            result = session.execute(
                text(f"SELECT column_name FROM information_schema.columns WHERE table_name = '{table_name}' AND column_name = '{column_name}'")
            ).fetchone()
            return result is not None
    except Exception as e:
        logger.error(f"检查列是否存在时出错: {str(e)}")
        return False
    finally:
        session.close()

def add_column(table_name, column_name, column_type):
    """添加列"""
    try:
        session = Session()
        # 根据数据库类型构建不同的ALTER TABLE语句
        if 'sqlite' in config.DATABASE_URI:
            session.execute(text(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"))
        else:
            session.execute(text(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"))
        session.commit()
        logger.info(f"成功添加列: {table_name}.{column_name} ({column_type})")
        return True
    except Exception as e:
        logger.error(f"添加列时出错: {str(e)}")
        session.rollback()
        return False
    finally:
        session.close()

def migrate_api_logs_table():
    """迁移api_logs表，添加缺少的列"""
    table_name = "api_logs"
    
    # 检查并添加input_tokens列
    if not check_column_exists(table_name, "input_tokens"):
        add_column(table_name, "input_tokens", "INTEGER")
    
    # 检查并添加output_tokens列
    if not check_column_exists(table_name, "output_tokens"):
        add_column(table_name, "output_tokens", "INTEGER")
    
    # 检查并添加total_tokens列
    if not check_column_exists(table_name, "total_tokens"):
        add_column(table_name, "total_tokens", "INTEGER")
    
    # 检查并添加input_cost列
    if not check_column_exists(table_name, "input_cost"):
        add_column(table_name, "input_cost", "FLOAT")
    
    # 检查并添加output_cost列
    if not check_column_exists(table_name, "output_cost"):
        add_column(table_name, "output_cost", "FLOAT")
    
    # 检查并添加total_cost列
    if not check_column_exists(table_name, "total_cost"):
        add_column(table_name, "total_cost", "FLOAT")
    
    # 检查并添加novel_id列
    if not check_column_exists(table_name, "novel_id"):
        add_column(table_name, "novel_id", "INTEGER")
    
    # 检查并添加analysis_type列
    if not check_column_exists(table_name, "analysis_type"):
        add_column(table_name, "analysis_type", "VARCHAR(50)")
    
    # 检查并添加error_type列
    if not check_column_exists(table_name, "error_type"):
        add_column(table_name, "error_type", "VARCHAR(50)")
    
    # 检查并添加error_message列
    if not check_column_exists(table_name, "error_message"):
        add_column(table_name, "error_message", "TEXT")
    
    # 检查并添加parameters列
    if not check_column_exists(table_name, "parameters"):
        add_column(table_name, "parameters", "JSON")

def main():
    """主函数"""
    logger.info("开始数据库迁移...")
    
    # 迁移api_logs表
    logger.info("迁移api_logs表...")
    migrate_api_logs_table()
    
    logger.info("数据库迁移完成")

if __name__ == "__main__":
    main()
