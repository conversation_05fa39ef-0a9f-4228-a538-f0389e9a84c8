/**
 * 九猫 - 分析过程页面优化脚本
 * 专门解决分析过程页面加载慢和崩溃问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析过程页面优化脚本已加载');
    
    // 配置参数
    const CONFIG = {
        lazyLoadNodes: true,       // 懒加载时间线节点
        initialNodeCount: 10,      // 初始加载的节点数量
        batchSize: 5,              // 每批加载的节点数量
        lazyLoadDistance: 500,     // 触发懒加载的距离（像素）
        optimizeImages: true,      // 优化图片加载
        compressLargeText: true,   // 压缩大文本
        maxTextLength: 5000,       // 最大文本长度（超过则压缩）
        monitorMemory: true        // 监控内存使用
    };
    
    // 存储原始节点
    let originalNodes = [];
    let visibleNodes = 0;
    let isLoading = false;
    let activeFilter = 'all';
    
    // 在页面加载完成后执行优化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始优化分析过程页面');
        
        // 检测页面类型
        if (!isAnalysisProcessPage()) {
            console.log('不是分析过程页面，跳过优化');
            return;
        }
        
        // 1. 初始化懒加载
        if (CONFIG.lazyLoadNodes) {
            initLazyLoading();
        }
        
        // 2. 优化大文本显示
        if (CONFIG.compressLargeText) {
            compressLargeTexts();
        }
        
        // 3. 添加内存监控
        if (CONFIG.monitorMemory) {
            setupMemoryMonitoring();
        }
        
        // 4. 修复过滤功能
        fixFilterFunctionality();
    });
    
    // 检测是否是分析过程页面
    function isAnalysisProcessPage() {
        const path = window.location.pathname;
        return path.includes('/novel/') && path.includes('/analysis_process/');
    }
    
    // 初始化懒加载
    function initLazyLoading() {
        try {
            // 获取所有时间线节点
            const timelineContainer = document.querySelector('.process-timeline');
            const timelineTrack = document.querySelector('.timeline-track');
            const allNodes = document.querySelectorAll('.timeline-node');
            
            if (!timelineContainer || !timelineTrack || allNodes.length === 0) {
                console.log('未找到时间线节点，跳过懒加载初始化');
                return;
            }
            
            console.log(`找到 ${allNodes.length} 个时间线节点，初始化懒加载`);
            
            // 保存原始节点引用
            originalNodes = Array.from(allNodes);
            
            // 隐藏除了初始节点外的所有节点
            originalNodes.forEach((node, index) => {
                if (index >= CONFIG.initialNodeCount) {
                    node.style.display = 'none';
                    node.setAttribute('data-lazy-load', 'true');
                } else {
                    visibleNodes++;
                }
            });
            
            // 添加加载更多按钮
            const loadMoreButton = document.createElement('div');
            loadMoreButton.className = 'text-center my-4';
            loadMoreButton.innerHTML = `
                <button id="loadMoreNodes" class="btn btn-outline-primary">
                    加载更多 (${visibleNodes}/${originalNodes.length})
                </button>
            `;
            timelineContainer.appendChild(loadMoreButton);
            
            // 添加加载更多按钮点击事件
            document.getElementById('loadMoreNodes').addEventListener('click', function() {
                loadMoreNodes();
            });
            
            // 添加滚动监听，自动加载更多
            window.addEventListener('scroll', debounce(function() {
                // 如果正在加载或已加载全部，跳过
                if (isLoading || visibleNodes >= originalNodes.length) return;
                
                // 获取加载更多按钮的位置
                const loadMoreButton = document.getElementById('loadMoreNodes');
                if (!loadMoreButton) return;
                
                const buttonRect = loadMoreButton.getBoundingClientRect();
                const windowHeight = window.innerHeight;
                
                // 如果按钮接近可视区域底部，加载更多
                if (buttonRect.top - windowHeight < CONFIG.lazyLoadDistance) {
                    loadMoreNodes();
                }
            }, 200));
            
            console.log(`懒加载初始化完成，已显示 ${visibleNodes}/${originalNodes.length} 个节点`);
        } catch (e) {
            console.error('初始化懒加载时出错:', e);
        }
    }
    
    // 加载更多节点
    function loadMoreNodes() {
        try {
            // 如果正在加载或已加载全部，跳过
            if (isLoading || visibleNodes >= originalNodes.length) return;
            
            isLoading = true;
            
            // 更新加载按钮状态
            const loadMoreButton = document.getElementById('loadMoreNodes');
            if (loadMoreButton) {
                loadMoreButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 加载中...';
                loadMoreButton.disabled = true;
            }
            
            // 计算本次要加载的节点范围
            const startIndex = visibleNodes;
            const endIndex = Math.min(startIndex + CONFIG.batchSize, originalNodes.length);
            
            // 延迟执行，避免UI阻塞
            setTimeout(function() {
                // 显示下一批节点
                for (let i = startIndex; i < endIndex; i++) {
                    const node = originalNodes[i];
                    
                    // 如果当前过滤器不是"全部"，检查节点是否应该显示
                    if (activeFilter === 'all' || node.dataset.stage === activeFilter) {
                        node.style.display = '';
                    } else {
                        node.style.display = 'none';
                    }
                    
                    node.removeAttribute('data-lazy-load');
                }
                
                // 更新已显示节点计数
                visibleNodes = endIndex;
                
                // 更新加载按钮状态
                if (loadMoreButton) {
                    loadMoreButton.innerHTML = `加载更多 (${visibleNodes}/${originalNodes.length})`;
                    loadMoreButton.disabled = false;
                    
                    // 如果已加载全部，隐藏按钮
                    if (visibleNodes >= originalNodes.length) {
                        loadMoreButton.innerHTML = '已加载全部';
                        loadMoreButton.disabled = true;
                        
                        // 延迟后移除按钮
                        setTimeout(function() {
                            loadMoreButton.parentNode.removeChild(loadMoreButton);
                        }, 2000);
                    }
                }
                
                isLoading = false;
                
                console.log(`已加载 ${visibleNodes}/${originalNodes.length} 个节点`);
            }, 100);
        } catch (e) {
            console.error('加载更多节点时出错:', e);
            isLoading = false;
            
            // 恢复加载按钮状态
            const loadMoreButton = document.getElementById('loadMoreNodes');
            if (loadMoreButton) {
                loadMoreButton.innerHTML = `加载更多 (${visibleNodes}/${originalNodes.length})`;
                loadMoreButton.disabled = false;
            }
        }
    }
    
    // 压缩大文本
    function compressLargeTexts() {
        try {
            // 查找所有文本预览和完整文本
            const textPreviews = document.querySelectorAll('.text-preview');
            const fullTexts = document.querySelectorAll('.full-text');
            
            console.log(`找到 ${textPreviews.length} 个文本预览和 ${fullTexts.length} 个完整文本`);
            
            // 处理所有完整文本
            fullTexts.forEach(fullText => {
                const codeElement = fullText.querySelector('code');
                if (!codeElement) return;
                
                const text = codeElement.textContent;
                
                // 如果文本超过最大长度，进行压缩
                if (text.length > CONFIG.maxTextLength) {
                    console.log(`压缩长度为 ${text.length} 的文本`);
                    
                    // 创建压缩版本的文本
                    const compressedText = compressText(text);
                    
                    // 替换原始文本
                    codeElement.textContent = compressedText;
                    
                    // 添加提示信息
                    const infoMessage = document.createElement('div');
                    infoMessage.className = 'alert alert-info mt-2 mb-2';
                    infoMessage.innerHTML = `
                        <small>文本已压缩以提高性能。原始长度: ${text.length} 字符，压缩后: ${compressedText.length} 字符。</small>
                    `;
                    
                    fullText.insertBefore(infoMessage, codeElement);
                }
            });
        } catch (e) {
            console.error('压缩大文本时出错:', e);
        }
    }
    
    // 压缩文本
    function compressText(text) {
        // 如果文本不够长，直接返回
        if (text.length <= CONFIG.maxTextLength) {
            return text;
        }
        
        // 计算要保留的前后文本长度
        const headLength = Math.floor(CONFIG.maxTextLength * 0.6);
        const tailLength = CONFIG.maxTextLength - headLength - 50; // 50字符用于中间的省略信息
        
        // 提取前后文本
        const headText = text.substring(0, headLength);
        const tailText = text.substring(text.length - tailLength);
        
        // 创建省略信息
        const omittedInfo = `\n\n... [省略了 ${text.length - headLength - tailLength} 个字符] ...\n\n`;
        
        // 组合压缩后的文本
        return headText + omittedInfo + tailText;
    }
    
    // 修复过滤功能
    function fixFilterFunctionality() {
        try {
            // 获取过滤按钮和时间线节点
            const stageFilters = document.querySelectorAll('.stage-filter');
            
            if (stageFilters.length === 0) {
                console.log('未找到过滤按钮，跳过修复');
                return;
            }
            
            console.log(`找到 ${stageFilters.length} 个过滤按钮，修复过滤功能`);
            
            // 移除现有的点击事件
            stageFilters.forEach(filter => {
                const newFilter = filter.cloneNode(true);
                filter.parentNode.replaceChild(newFilter, filter);
            });
            
            // 重新添加点击事件
            document.querySelectorAll('.stage-filter').forEach(filter => {
                filter.addEventListener('click', function() {
                    const stage = this.dataset.stage;
                    activeFilter = stage;
                    
                    // 更新过滤按钮样式
                    document.querySelectorAll('.stage-filter').forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.remove('btn-secondary');
                        
                        // 恢复原始样式
                        if (btn.dataset.stage === 'all') {
                            btn.classList.add('btn-outline-secondary');
                        } else if (btn.dataset.stage === 'init') {
                            btn.classList.add('btn-outline-secondary');
                        } else if (btn.dataset.stage === 'chunk_analysis') {
                            btn.classList.add('btn-outline-primary');
                        } else if (btn.dataset.stage === 'combine') {
                            btn.classList.add('btn-outline-success');
                        } else if (btn.dataset.stage === 'finalize') {
                            btn.classList.add('btn-outline-danger');
                        }
                    });
                    
                    // 更新当前按钮样式
                    this.classList.add('active');
                    
                    // 移除outline样式
                    if (this.classList.contains('btn-outline-secondary')) {
                        this.classList.remove('btn-outline-secondary');
                        this.classList.add('btn-secondary');
                    } else if (this.classList.contains('btn-outline-primary')) {
                        this.classList.remove('btn-outline-primary');
                        this.classList.add('btn-primary');
                    } else if (this.classList.contains('btn-outline-success')) {
                        this.classList.remove('btn-outline-success');
                        this.classList.add('btn-success');
                    } else if (this.classList.contains('btn-outline-danger')) {
                        this.classList.remove('btn-outline-danger');
                        this.classList.add('btn-danger');
                    }
                    
                    // 过滤时间线节点
                    originalNodes.forEach((node, index) => {
                        // 只处理已加载的节点
                        if (index < visibleNodes) {
                            if (stage === 'all' || node.dataset.stage === stage) {
                                node.style.display = '';
                            } else {
                                node.style.display = 'none';
                            }
                        }
                    });
                    
                    // 更新加载更多按钮
                    updateLoadMoreButton();
                });
            });
            
            // 默认选中"全部"按钮
            document.querySelector('.stage-filter[data-stage="all"]').click();
        } catch (e) {
            console.error('修复过滤功能时出错:', e);
        }
    }
    
    // 更新加载更多按钮
    function updateLoadMoreButton() {
        const loadMoreButton = document.getElementById('loadMoreNodes');
        if (!loadMoreButton) return;
        
        // 计算当前过滤器下的节点总数
        let totalFilteredNodes = 0;
        let visibleFilteredNodes = 0;
        
        originalNodes.forEach((node, index) => {
            if (activeFilter === 'all' || node.dataset.stage === activeFilter) {
                totalFilteredNodes++;
                
                if (index < visibleNodes) {
                    visibleFilteredNodes++;
                }
            }
        });
        
        // 更新按钮文本
        loadMoreButton.innerHTML = `加载更多 (${visibleFilteredNodes}/${totalFilteredNodes})`;
        
        // 如果已加载全部，禁用按钮
        if (visibleFilteredNodes >= totalFilteredNodes) {
            loadMoreButton.innerHTML = '已加载全部';
            loadMoreButton.disabled = true;
        } else {
            loadMoreButton.disabled = false;
        }
    }
    
    // 设置内存监控
    function setupMemoryMonitoring() {
        try {
            // 检查是否支持性能监控API
            if (!window.performance || !window.performance.memory) {
                console.log('浏览器不支持内存监控API');
                return;
            }
            
            // 定期检查内存使用情况
            const memoryCheckInterval = setInterval(function() {
                const memoryInfo = window.performance.memory;
                const usedHeapSize = memoryInfo.usedJSHeapSize / 1024 / 1024;
                const totalHeapSize = memoryInfo.totalJSHeapSize / 1024 / 1024;
                const heapLimit = memoryInfo.jsHeapSizeLimit / 1024 / 1024;
                
                console.log(`内存使用: ${usedHeapSize.toFixed(2)}MB / ${totalHeapSize.toFixed(2)}MB (限制: ${heapLimit.toFixed(2)}MB)`);
                
                // 如果内存使用接近限制，尝试释放内存
                if (usedHeapSize > heapLimit * 0.8) {
                    console.warn('内存使用接近限制，尝试释放内存');
                    releaseMemory();
                }
            }, 10000);  // 每10秒检查一次
            
            // 页面卸载时清除定时器
            window.addEventListener('beforeunload', function() {
                clearInterval(memoryCheckInterval);
            });
        } catch (e) {
            console.error('设置内存监控时出错:', e);
        }
    }
    
    // 释放内存
    function releaseMemory() {
        try {
            console.log('尝试释放内存');
            
            // 清理不必要的DOM元素
            document.querySelectorAll('.full-text').forEach(element => {
                if (element.style.display === 'none') {
                    // 保存引用以便恢复
                    const parent = element.parentNode;
                    const nextSibling = element.nextSibling;
                    const placeholder = document.createElement('div');
                    placeholder.className = 'text-placeholder';
                    placeholder.dataset.targetId = element.id;
                    
                    // 移除元素
                    parent.removeChild(element);
                    
                    // 添加占位符
                    parent.insertBefore(placeholder, nextSibling);
                }
            });
            
            // 提示垃圾回收
            if (window.gc) {
                window.gc();
            } else {
                // 间接提示垃圾回收
                const largeArray = [];
                for (let i = 0; i < 1000; i++) {
                    largeArray.push(new Array(10000).join('x'));
                }
                largeArray.length = 0;
            }
            
            console.log('内存释放完成');
        } catch (e) {
            console.error('释放内存时出错:', e);
        }
    }
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
})();
