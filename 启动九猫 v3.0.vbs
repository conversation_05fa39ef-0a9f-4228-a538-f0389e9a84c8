Option Explicit

' 九猫小说分析写作系统 v3.0 启动脚本
' 自动启动九猫系统，无需手动操作
' 新增：智能结果缓存与推理复用优化功能
' 新增：精简版专用优化配置，提高效率降低成本

' 设置工作目录为脚本所在目录
Dim fso, shell, pythonCmd, workingDir, runCmd
Set fso = CreateObject("Scripting.FileSystemObject")
Set shell = CreateObject("WScript.Shell")

' 获取当前脚本所在目录
workingDir = fso.GetParentFolderName(WScript.ScriptFullName)
shell.CurrentDirectory = workingDir

' 检查Python环境
pythonCmd = "python"
If Not CheckPythonInstalled(pythonCmd) Then
    pythonCmd = "py"
    If Not CheckPythonInstalled(pythonCmd) Then
        MsgBox "未检测到Python环境，请安装Python 3.8或更高版本。", vbExclamation, "九猫小说分析写作系统"
        WScript.Quit
    End If
End If

' 直接使用完整版启动
Dim useSimplifiedVersion
useSimplifiedVersion = False  ' 默认使用完整版

' 显示启动消息
Dim startupMessage
startupMessage = "九猫小说分析写作系统 v3.0 正在启动..." & vbCrLf & vbCrLf & _
   "系统将在后台运行，浏览器将在几秒钟后自动打开。" & vbCrLf & _
   "此版本添加了章节分析维度删除功能和API修复。" & vbCrLf & _
   "已启用本地资源加载，解决CDN资源连接超时问题。" & vbCrLf & _
   "已增强jQuery修复，彻底解决$(...).on is not a function错误。" & vbCrLf & _
   "新增智能结果缓存与推理复用优化，降低API调用成本30-40%!" & vbCrLf & _
   "请不要关闭浏览器窗口，否则需要手动访问: http://localhost:5001/v3/"

MsgBox startupMessage, 64, "九猫小说分析写作系统 v3.0"

' 创建临时批处理文件来设置环境变量并运行Python
Dim tempFile
Set tempFile = fso.CreateTextFile("temp_run_v3.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & workingDir & """")
tempFile.WriteLine("echo 当前工作目录: %CD%")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set USE_MOCK_API=False")
tempFile.WriteLine("set ENABLE_MOCK_ANALYSIS=False")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("set MEMORY_OPTIMIZED=True")

' 根据版本类型设置不同的配置
If useSimplifiedVersion Then
    ' 精简版配置 - 更低资源占用，更高效率优化
    tempFile.WriteLine("set LOW_MEMORY_MODE=True")
    tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=65")
    tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=75")
    tempFile.WriteLine("set MAX_DB_CONNECTIONS=15")
    tempFile.WriteLine("set DB_POOL_SIZE=15")
    tempFile.WriteLine("set DB_MAX_OVERFLOW=10")
    tempFile.WriteLine("set DISABLE_CHARTS=True")
    tempFile.WriteLine("set VERSION=3.0-simplified")
    tempFile.WriteLine("set USE_SIMPLIFIED_MODE=True")
Else
    ' 完整版配置
    tempFile.WriteLine("set LOW_MEMORY_MODE=True")
    tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=75")
    tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=85")
    tempFile.WriteLine("set MAX_DB_CONNECTIONS=30")
    tempFile.WriteLine("set DB_POOL_SIZE=30")
    tempFile.WriteLine("set DB_MAX_OVERFLOW=20")
    tempFile.WriteLine("set DISABLE_CHARTS=True")
    tempFile.WriteLine("set VERSION=3.0")
    tempFile.WriteLine("set USE_SIMPLIFIED_MODE=False")
End If

' 通用配置
tempFile.WriteLine("set MEMORY_STATS_DIR=" & workingDir & "\memory_stats")
tempFile.WriteLine("set DISABLE_AUTO_REFRESH=True")
tempFile.WriteLine("set MEMORY_CHECK_INTERVAL=3")
tempFile.WriteLine("set OPTIMIZE_DIMENSION_DETAIL=True")
tempFile.WriteLine("set ENABLE_LOG_FILTER=True")
tempFile.WriteLine("set SEPARATE_ANALYSIS_PROCESS=True")
tempFile.WriteLine("set ENABLE_BUTTON_TEXT_SUPREME_FIX=True")
tempFile.WriteLine("set FORCE_BUTTON_TEXT_VISIBILITY=True")
tempFile.WriteLine("set DEEPSEEK_API_KEY=***********************************")
tempFile.WriteLine("set QWEN_API_KEY=sk-6f3b4c6ad9f64f78b22bed422c5d278d")
tempFile.WriteLine("set DEFAULT_MODEL=deepseek-r1")
tempFile.WriteLine("set FORCE_REANALYSIS=True")
tempFile.WriteLine("set FORCE_REAL_API=True")
tempFile.WriteLine("set RELOAD_CONFIG=True")
tempFile.WriteLine("set DEFAULT_VERSION=v3.0")
tempFile.WriteLine("set TOTAL_DIMENSIONS=15")
tempFile.WriteLine("set ENABLE_ANALYSIS_STATUS_FIX=True")
tempFile.WriteLine("set USE_V3_CONSOLE=True")
tempFile.WriteLine("set ENABLE_CHAPTER_DELETE=True")
tempFile.WriteLine("set API_ROUTE_FIX=True")
tempFile.WriteLine("set ENABLE_NOVEL_ANALYSIS_API_FIX=True")
tempFile.WriteLine("set USE_LOCAL_RESOURCES=True")
tempFile.WriteLine("set DISABLE_CDN=True")
tempFile.WriteLine("set ENABLE_RESOURCE_FIX=True")
tempFile.WriteLine("set CDN_TIMEOUT=1000")
tempFile.WriteLine("set LOAD_BOOTSTRAP_ICONS_LOCALLY=True")

' 新增智能结果缓存与推理复用优化配置
tempFile.WriteLine("set ENABLE_RESULT_CACHE=True")
tempFile.WriteLine("set ENABLE_CROSS_CHAPTER_REASONING=True")
tempFile.WriteLine("set ENABLE_PARAGRAPH_REASONING=True")

' 根据版本类型设置不同的优化参数
If useSimplifiedVersion Then
    ' 精简版优化参数 - 更激进的缓存与优化策略
    tempFile.WriteLine("set CACHE_EXPIRATION_HOURS=48")
    tempFile.WriteLine("set SIMILARITY_THRESHOLD=0.85")  ' 降低相似度阈值，提高缓存命中率
    tempFile.WriteLine("set MAX_CACHE_ENTRIES=500")
    tempFile.WriteLine("set MIN_PARAGRAPH_LENGTH=80")    ' 降低段落长度要求
    tempFile.WriteLine("set MAX_PREVIOUS_CHAPTERS=2")    ' 减少前序章节数量
    tempFile.WriteLine("set CROSS_CHAPTER_SIMILARITY=0.6") ' 降低跨章节相似度要求
    tempFile.WriteLine("set MAX_WORKERS=6")              ' 降低最大线程数
    tempFile.WriteLine("set BATCH_SIZE=5")               ' 减小批处理大小
    tempFile.WriteLine("set API_ADAPTIVE_DELAY=True")    ' 启用自适应API延迟
    tempFile.WriteLine("set MIN_API_DELAY=0.1")          ' 最小API延迟
    tempFile.WriteLine("set REUSE_DB_CONNECTIONS=True")  ' 启用数据库连接复用
    tempFile.WriteLine("set ENABLE_SERIALIZATION_BOTTLENECK_OPTIMIZER=True")
    tempFile.WriteLine("set AGGRESSIVE_OPTIMIZATION=True") ' 启用激进优化策略
    tempFile.WriteLine("set OPTIMIZE_FOR_COST=True")       ' 为成本优化
    tempFile.WriteLine("set MIN_REASONING_SIMILARITY=0.6") ' 降低推理相似度要求
Else
    ' 完整版优化参数 - 更平衡的策略
    tempFile.WriteLine("set CACHE_EXPIRATION_HOURS=72")
    tempFile.WriteLine("set SIMILARITY_THRESHOLD=0.92")
    tempFile.WriteLine("set MAX_CACHE_ENTRIES=2000")
    tempFile.WriteLine("set MIN_PARAGRAPH_LENGTH=100")
    tempFile.WriteLine("set MAX_PREVIOUS_CHAPTERS=3")
    tempFile.WriteLine("set CROSS_CHAPTER_SIMILARITY=0.7")
    tempFile.WriteLine("set MAX_WORKERS=8")
    tempFile.WriteLine("set BATCH_SIZE=10")
    tempFile.WriteLine("set API_ADAPTIVE_DELAY=True")
    tempFile.WriteLine("set MIN_API_DELAY=0.2")
    tempFile.WriteLine("set REUSE_DB_CONNECTIONS=True")
    tempFile.WriteLine("set ENABLE_SERIALIZATION_BOTTLENECK_OPTIMIZER=True")
    tempFile.WriteLine("set AGGRESSIVE_OPTIMIZATION=False")
    tempFile.WriteLine("set OPTIMIZE_FOR_COST=False")
    tempFile.WriteLine("set MIN_REASONING_SIMILARITY=0.7")
End If

' 创建缓存目录
tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("if not exist src\cache mkdir src\cache")
tempFile.WriteLine("if not exist src\cache\result_cache mkdir src\cache\result_cache")
tempFile.WriteLine("if not exist src\cache\paragraph_reasoning mkdir src\cache\paragraph_reasoning")
tempFile.WriteLine("echo 检查Python是否可用...")
tempFile.WriteLine("python --version")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo Python未找到，请确保Python已安装并添加到PATH环境变量中")
tempFile.WriteLine("    pause")
tempFile.WriteLine("    exit /b 1")
tempFile.WriteLine(")")
tempFile.WriteLine("echo 启动九猫v3.0系统...")
tempFile.WriteLine(pythonCmd & " -u -m src.web.v3_0_app")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo 启动失败，错误代码: %ERRORLEVEL%")
tempFile.WriteLine("    pause")
tempFile.WriteLine(")")
tempFile.Close

' 在后台运行批处理文件
shell.Run "cmd /c """ & workingDir & "\temp_run_v3.bat""", 1, False

' 等待5秒钟确保服务启动
WScript.Sleep 5000

' 打开浏览器访问系统
shell.Run "http://localhost:5001/v3/", 1, False

' 显示启动成功消息
Dim successMessage
successMessage = "九猫小说分析写作系统 v3.0 已启动！" & vbCrLf & _
   "系统已在浏览器中打开，地址：http://localhost:5001/v3/" & vbCrLf & _
   "如果浏览器未自动打开，请手动访问上述地址。" & vbCrLf & vbCrLf & _
   "此版本添加了章节分析维度删除功能和API修复，解决了分析结果读取404错误问题。" & vbCrLf & _
   "已启用本地资源加载，解决CDN资源连接超时问题。" & vbCrLf & _
   "已增强jQuery修复，彻底解决$(...).on is not a function错误。" & vbCrLf & vbCrLf & _
   "新增三项性能优化：" & vbCrLf & _
   "1. 智能结果缓存：避免重复分析相同内容" & vbCrLf & _
   "2. 跨章节推理复用：提高分析连贯性" & vbCrLf & _
   "3. 段落级推理复用：加速分析处理" & vbCrLf & _
   "预期可降低API调用成本30-40%，显著提升分析效率" & vbCrLf & vbCrLf & _
   "您可以在系统内部的'一键分析写作'功能中选择精简版模式。"

MsgBox successMessage, vbInformation, "九猫小说分析写作系统 v3.0"

' 检查Python是否已安装
Function CheckPythonInstalled(cmd)
    On Error Resume Next
    shell.Run cmd & " --version", 0, True
    CheckPythonInstalled = (Err.Number = 0)
    On Error GoTo 0
End Function
