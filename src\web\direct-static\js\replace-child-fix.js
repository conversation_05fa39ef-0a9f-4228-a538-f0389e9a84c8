/**
 * 九猫 - replaceChild 修复脚本
 * 解决 "Failed to execute 'replaceChild' on 'Node': Invalid or unexpected token" 错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('replaceChild 修复脚本已加载 v1.0.0');
    
    // 保存原始的 replaceChild 方法
    if (!window.__originalReplaceChild) {
        window.__originalReplaceChild = Node.prototype.replaceChild;
    }
    
    // 安全的 HTML 编码函数
    function safeEncodeHTML(html) {
        if (typeof html !== 'string') {
            return html;
        }
        
        try {
            // 创建一个临时元素来测试 HTML 是否有效
            const temp = document.createElement('div');
            temp.innerHTML = html;
            return temp.innerHTML;
        } catch (e) {
            console.error('HTML 编码失败:', e);
            // 如果失败，返回原始 HTML
            return html;
        }
    }
    
    // 安全的节点替换函数
    function safeReplaceChild(newChild, oldChild) {
        try {
            // 尝试使用原始方法
            return window.__originalReplaceChild.call(this, newChild, oldChild);
        } catch (e) {
            console.error('replaceChild 错误:', e.message);
            
            // 如果是语法错误，尝试安全替换
            if (e instanceof SyntaxError || e.message.includes('Invalid or unexpected token')) {
                console.log('检测到语法错误，尝试安全替换');
                
                try {
                    // 创建一个新的节点
                    let safeNewChild;
                    
                    if (newChild.nodeType === Node.TEXT_NODE) {
                        // 如果是文本节点，创建一个新的文本节点
                        safeNewChild = document.createTextNode(newChild.textContent || '');
                    } else if (newChild.nodeType === Node.ELEMENT_NODE) {
                        // 如果是元素节点，创建一个新的元素
                        safeNewChild = document.createElement(newChild.tagName);
                        
                        // 复制属性
                        for (let i = 0; i < newChild.attributes.length; i++) {
                            const attr = newChild.attributes[i];
                            safeNewChild.setAttribute(attr.name, attr.value);
                        }
                        
                        // 安全地复制内容
                        safeNewChild.innerHTML = safeEncodeHTML(newChild.innerHTML);
                    } else {
                        // 对于其他类型的节点，尝试克隆
                        safeNewChild = newChild.cloneNode(true);
                    }
                    
                    // 尝试替换
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }
                    this.appendChild(safeNewChild);
                    
                    return safeNewChild;
                } catch (e2) {
                    console.error('安全替换也失败:', e2.message);
                    
                    // 最后的尝试：完全移除旧节点
                    try {
                        if (this.contains(oldChild)) {
                            this.removeChild(oldChild);
                        }
                        return null;
                    } catch (e3) {
                        console.error('移除旧节点也失败:', e3.message);
                        return null;
                    }
                }
            } else {
                // 如果不是语法错误，重新抛出
                throw e;
            }
        }
    }
    
    // 替换原始的 replaceChild 方法
    Node.prototype.replaceChild = safeReplaceChild;
    
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && 
            (event.error.message.includes('replaceChild') || 
             event.error.message.includes('Invalid or unexpected token'))) {
            
            console.error('捕获到 replaceChild 相关错误:', event.error.message);
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
    
    console.log('replaceChild 方法已安全替换');
})();
