/**
 * 九猫系统布局修复脚本
 * 用于修复页面布局问题
 */

(function() {
    console.log('[布局修复] 初始化...');

    // 等待DOM加载完成
    function onDOMReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    // 修复布局问题
    function fixLayout() {
        console.log('[布局修复] 开始修复布局问题...');

        // 修复popular_tropes布局
        fixPopularTropesLayout();

        // 修复章节列表布局
        fixChapterListLayout();

        // 修复分析维度详情页面布局
        fixAnalysisDimensionLayout();

        // 添加CSS修复
        addCSSFixes();

        console.log('[布局修复] 布局修复完成');
    }

    // 修复popular_tropes布局
    function fixPopularTropesLayout() {
        // 查找popular_tropes相关元素
        const tropesContainers = document.querySelectorAll('.popular_tropes-container, .popular_tropes-settings');
        
        if (tropesContainers.length > 0) {
            console.log('[布局修复] 修复popular_tropes布局');
            
            tropesContainers.forEach(container => {
                // 确保内容正确对齐
                container.style.display = 'flex';
                container.style.flexDirection = 'column';
                container.style.gap = '10px';
                
                // 修复文本溢出
                const textElements = container.querySelectorAll('p, li, h3, h4');
                textElements.forEach(el => {
                    el.style.maxWidth = '100%';
                    el.style.wordBreak = 'break-word';
                });
            });
        }
    }

    // 修复章节列表布局
    function fixChapterListLayout() {
        const chapterList = document.querySelector('.chapter-list');
        if (chapterList) {
            console.log('[布局修复] 修复章节列表布局');
            
            // 确保章节列表项正确对齐
            const chapterItems = chapterList.querySelectorAll('.chapter-item');
            chapterItems.forEach(item => {
                item.style.display = 'flex';
                item.style.alignItems = 'center';
                item.style.justifyContent = 'space-between';
                item.style.padding = '8px';
                item.style.margin = '4px 0';
                item.style.borderRadius = '4px';
                item.style.backgroundColor = '#f8f9fa';
            });
        }
    }

    // 修复分析维度详情页面布局
    function fixAnalysisDimensionLayout() {
        const dimensionDetails = document.querySelector('.dimension-details');
        if (dimensionDetails) {
            console.log('[布局修复] 修复分析维度详情页面布局');
            
            // 确保分析结果和推理过程正确显示
            const resultSection = dimensionDetails.querySelector('.analysis-result');
            const reasoningSection = dimensionDetails.querySelector('.reasoning-content');
            
            if (resultSection) {
                resultSection.style.padding = '15px';
                resultSection.style.backgroundColor = '#fff';
                resultSection.style.borderRadius = '8px';
                resultSection.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                resultSection.style.marginBottom = '20px';
            }
            
            if (reasoningSection) {
                reasoningSection.style.padding = '15px';
                reasoningSection.style.backgroundColor = '#f8f9fa';
                reasoningSection.style.borderRadius = '8px';
                reasoningSection.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                reasoningSection.style.maxHeight = '500px';
                reasoningSection.style.overflowY = 'auto';
            }
        }
    }

    // 添加CSS修复
    function addCSSFixes() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            /* 全局布局修复 */
            body {
                background-color: #fffdf5; /* 浅米黄色背景 */
            }
            
            .container, .container-fluid {
                padding: 20px;
                max-width: 1400px;
                margin: 0 auto;
            }
            
            /* 修复红色边框 */
            * {
                outline: none !important;
                box-shadow: none !important;
            }
            
            /* 修复popular_tropes布局 */
            .popular_tropes-container {
                display: flex;
                flex-direction: column;
                gap: 10px;
                padding: 15px;
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
            
            .popular_tropes-settings {
                display: flex;
                flex-direction: column;
                gap: 10px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
            }
            
            /* 修复章节列表布局 */
            .chapter-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-top: 20px;
            }
            
            .chapter-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;
                background-color: #fff;
                border-radius: 4px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            }
            
            /* 修复分析维度详情页面布局 */
            .dimension-details {
                padding: 20px;
                background-color: #fffdf5;
                border-radius: 8px;
            }
            
            .analysis-result {
                padding: 15px;
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                margin-bottom: 20px;
            }
            
            .reasoning-content {
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                max-height: 500px;
                overflow-y: auto;
            }
            
            /* 修复按钮样式 */
            .btn-delete {
                padding: 2px 8px;
                font-size: 0.8rem;
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
            }
            
            .btn-delete i {
                margin-right: 0;
            }
            
            /* 修复下拉菜单 */
            .dropdown-menu {
                max-height: 300px;
                overflow-y: auto;
                width: 100%;
            }
        `;
        
        document.head.appendChild(styleElement);
        console.log('[布局修复] 添加CSS修复');
    }

    // 监听DOM变化，动态修复布局
    function observeDOM() {
        const observer = new MutationObserver(mutations => {
            let shouldFixLayout = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldFixLayout = true;
                }
            });
            
            if (shouldFixLayout) {
                fixLayout();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('[布局修复] DOM观察器已启动');
    }

    // 初始化
    onDOMReady(() => {
        fixLayout();
        observeDOM();
    });
})();
