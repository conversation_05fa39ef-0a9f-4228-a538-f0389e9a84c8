/**
 * 九猫 - jQuery .on() 方法直接修复脚本
 * 专门解决 $(...).on is not a function 错误
 * 版本: 1.0.0
 * 
 * 这个脚本会在页面加载的最早阶段运行，直接修复jQuery.fn.on方法
 */

// 立即执行函数
(function() {
    console.log('[jQuery .on() 直接修复] 脚本已加载');

    // 直接修复jQuery.fn.on方法
    function fixJQueryOnMethod() {
        // 确保jQuery存在
        if (typeof jQuery !== 'undefined') {
            console.log('[jQuery .on() 直接修复] jQuery已加载，版本:', jQuery.fn.jquery);
            
            // 确保$ 变量可用
            if (typeof $ === 'undefined') {
                console.log('[jQuery .on() 直接修复] $ 变量未定义，设置 $ = jQuery');
                window.$ = jQuery;
            }
            
            // 直接替换jQuery.fn.on方法，无论它是否存在
            console.log('[jQuery .on() 直接修复] 直接替换jQuery.fn.on方法');
            
            jQuery.fn.on = function(events, selector, data, handler) {
                // 处理不同的参数形式
                if (typeof selector === 'function') {
                    handler = selector;
                    data = null;
                    selector = null;
                } else if (typeof data === 'function') {
                    handler = data;
                    data = null;
                }

                if (!handler) {
                    return this;
                }

                // 分割多个事件
                const eventList = events.split(' ');

                return this.each(function() {
                    const element = this;

                    eventList.forEach(function(eventName) {
                        // 处理命名空间
                        const parts = eventName.split('.');
                        const baseEvent = parts[0]; // 例如从'click.bs.button'提取'click'

                        if (selector) {
                            // 事件委托
                            element.addEventListener(baseEvent, function(event) {
                                const matches = element.querySelectorAll(selector);
                                let target = event.target;

                                while (target && target !== element) {
                                    for (let i = 0; i < matches.length; i++) {
                                        if (matches[i] === target) {
                                            // 创建一个jQuery事件对象
                                            const jQueryEvent = event;
                                            jQueryEvent.delegateTarget = element;

                                            // 调用处理函数
                                            handler.call(target, jQueryEvent);
                                            return;
                                        }
                                    }
                                    target = target.parentNode;
                                }
                            });
                        } else {
                            // 直接绑定
                            element.addEventListener(baseEvent, function(event) {
                                // 创建一个jQuery事件对象
                                const jQueryEvent = event;
                                handler.call(element, jQueryEvent);
                            });
                        }
                    });
                });
            };

            // 添加其他常用方法
            if (typeof jQuery.fn.off !== 'function') {
                jQuery.fn.off = function() { return this; };
            }
            
            if (typeof jQuery.fn.one !== 'function') {
                jQuery.fn.one = function(events, selector, data, handler) {
                    // 简化实现
                    if (typeof selector === 'function') {
                        handler = selector;
                        selector = null;
                    }
                    
                    return this.on(events, selector, data, handler);
                };
            }
            
            if (typeof jQuery.fn.trigger !== 'function') {
                jQuery.fn.trigger = function(eventType) {
                    return this.each(function() {
                        try {
                            const event = new Event(eventType);
                            this.dispatchEvent(event);
                        } catch (e) {}
                    });
                };
            }
            
            if (typeof jQuery.fn.tab !== 'function') {
                jQuery.fn.tab = function(action) {
                    if (action === 'show') {
                        return this.each(function() {
                            this.click();
                        });
                    }
                    return this;
                };
            }
            
            console.log('[jQuery .on() 直接修复] jQuery方法修复完成');
            return true;
        } else {
            console.log('[jQuery .on() 直接修复] jQuery未加载，无法修复');
            return false;
        }
    }

    // 立即尝试修复
    fixJQueryOnMethod();

    // 在DOMContentLoaded事件时再次尝试修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[jQuery .on() 直接修复] DOMContentLoaded事件触发，再次尝试修复');
        fixJQueryOnMethod();
    });

    // 在页面完全加载后再次尝试修复
    window.addEventListener('load', function() {
        console.log('[jQuery .on() 直接修复] load事件触发，再次尝试修复');
        fixJQueryOnMethod();
    });

    // 监听jQuery加载完成事件
    document.addEventListener('jQueryLoaded', function() {
        console.log('[jQuery .on() 直接修复] jQueryLoaded事件触发，再次尝试修复');
        fixJQueryOnMethod();
    });

    // 导出全局函数，以便手动调用
    window.fixJQueryOnMethodDirectly = fixJQueryOnMethod;

    // 每100毫秒检查一次jQuery是否加载，并尝试修复
    var checkInterval = setInterval(function() {
        if (typeof jQuery !== 'undefined') {
            console.log('[jQuery .on() 直接修复] 检测到jQuery已加载，尝试修复');
            if (fixJQueryOnMethod()) {
                clearInterval(checkInterval);
            }
        }
    }, 100);

    // 10秒后清除检查间隔，避免无限循环
    setTimeout(function() {
        clearInterval(checkInterval);
    }, 10000);
})();
