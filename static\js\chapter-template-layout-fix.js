/**
 * 九猫系统 - 章节列表设定模板布局修复脚本
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于修复小说列表中章节列表的设定模板显示问题，
 * 将设定模板从左侧移动到右侧显示，使布局更加合理。
 */

(function() {
    console.log('[章节模板布局修复] 初始化...');
    
    // 全局变量
    let isFixed = false;
    let observer = null;
    let templateCache = {};
    
    // CSS样式
    const styles = `
        .chapter-template-container {
            display: flex;
            flex-wrap: wrap;
        }
        
        .chapter-list-column {
            flex: 0 0 35%;
            max-width: 35%;
            padding-right: 15px;
        }
        
        .template-detail-column {
            flex: 0 0 65%;
            max-width: 65%;
            padding-left: 15px;
        }
        
        .template-detail-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            height: 100%;
            background-color: #fff;
        }
        
        .template-detail-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            padding: 12px 15px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        
        .template-detail-content {
            padding: 15px;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .template-content {
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
        }
        
        .template-actions {
            margin-top: 15px;
            text-align: right;
            padding: 10px 15px;
            border-top: 1px solid #e0e0e0;
        }
        
        .template-actions .btn {
            margin-left: 5px;
        }
        
        /* 响应式布局 */
        @media (max-width: 768px) {
            .chapter-template-container {
                flex-direction: column;
            }
            
            .chapter-list-column,
            .template-detail-column {
                flex: 0 0 100%;
                max-width: 100%;
                padding: 0;
            }
            
            .template-detail-column {
                margin-top: 20px;
            }
        }
    `;
    
    // 添加样式
    function addStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = styles;
        document.head.appendChild(styleElement);
        console.log('[章节模板布局修复] 添加样式完成');
    }
    
    // 修复章节模板布局
    function fixChapterTemplateLayout() {
        console.log('[章节模板布局修复] 开始修复章节模板布局...');
        
        // 检查是否已经修复
        if (isFixed) {
            console.log('[章节模板布局修复] 章节模板布局已修复，无需再次修复');
            return;
        }
        
        try {
            // 查找章节列表容器
            const chapterListContainer = document.querySelector('.chapter-list, .table-responsive');
            if (!chapterListContainer) {
                console.log('[章节模板布局修复] 未找到章节列表容器，可能不在章节列表页面');
                return;
            }
            
            // 查找设定模板容器
            const templateContainer = document.querySelector('.template-container, .setting-template');
            
            // 获取父容器
            const parentContainer = chapterListContainer.closest('.card-body, .container');
            if (!parentContainer) {
                console.log('[章节模板布局修复] 未找到父容器，无法修复布局');
                return;
            }
            
            // 创建新的布局容器
            const templateLayoutContainer = document.createElement('div');
            templateLayoutContainer.className = 'chapter-template-container';
            
            // 创建左侧列
            const leftColumn = document.createElement('div');
            leftColumn.className = 'chapter-list-column';
            
            // 创建右侧列
            const rightColumn = document.createElement('div');
            rightColumn.className = 'template-detail-column';
            
            // 创建模板详情卡片
            const templateDetailCard = document.createElement('div');
            templateDetailCard.className = 'template-detail-card';
            
            // 创建模板详情头部
            const templateDetailHeader = document.createElement('div');
            templateDetailHeader.className = 'template-detail-header';
            templateDetailHeader.innerHTML = '<h5 class="mb-0">设定模板详情</h5>';
            
            // 创建模板详情内容
            const templateDetailContent = document.createElement('div');
            templateDetailContent.className = 'template-detail-content';
            templateDetailContent.id = 'template-detail-content';
            
            if (templateContainer) {
                // 如果已有模板容器，移动到右侧
                templateDetailContent.appendChild(templateContainer);
            } else {
                // 否则显示提示信息
                templateDetailContent.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        请从左侧选择要查看的维度或章节，右侧将显示对应的设定模板内容。
                    </div>
                `;
            }
            
            // 创建模板操作区域
            const templateActions = document.createElement('div');
            templateActions.className = 'template-actions';
            templateActions.innerHTML = `
                <button class="btn btn-sm btn-outline-secondary" id="copy-template-btn">
                    <i class="fas fa-copy me-1"></i>复制内容
                </button>
            `;
            
            // 组装模板详情卡片
            templateDetailCard.appendChild(templateDetailHeader);
            templateDetailCard.appendChild(templateDetailContent);
            templateDetailCard.appendChild(templateActions);
            
            // 将模板详情卡片添加到右侧列
            rightColumn.appendChild(templateDetailCard);
            
            // 将章节列表容器移动到左侧列
            if (chapterListContainer.parentElement) {
                // 保存原始位置
                const originalParent = chapterListContainer.parentElement;
                const nextSibling = chapterListContainer.nextSibling;
                
                // 移动到左侧列
                leftColumn.appendChild(chapterListContainer);
                
                // 将左右列添加到布局容器
                templateLayoutContainer.appendChild(leftColumn);
                templateLayoutContainer.appendChild(rightColumn);
                
                // 将布局容器添加到原始位置
                if (nextSibling) {
                    originalParent.insertBefore(templateLayoutContainer, nextSibling);
                } else {
                    originalParent.appendChild(templateLayoutContainer);
                }
            }
            
            // 添加复制按钮事件
            const copyButton = document.getElementById('copy-template-btn');
            if (copyButton) {
                copyButton.addEventListener('click', function() {
                    const templateContent = document.querySelector('.template-content');
                    if (templateContent) {
                        // 创建临时文本区域
                        const textarea = document.createElement('textarea');
                        textarea.value = templateContent.textContent;
                        document.body.appendChild(textarea);
                        
                        // 选择文本
                        textarea.select();
                        
                        // 复制文本
                        document.execCommand('copy');
                        
                        // 移除临时文本区域
                        document.body.removeChild(textarea);
                        
                        // 显示提示
                        alert('模板内容已复制到剪贴板');
                    } else {
                        alert('没有可复制的模板内容');
                    }
                });
            }
            
            // 添加章节和维度点击事件
            addClickHandlers();
            
            // 标记为已修复
            isFixed = true;
            
            console.log('[章节模板布局修复] 章节模板布局修复完成');
        } catch (error) {
            console.error('[章节模板布局修复] 修复章节模板布局时出错:', error);
        }
    }
    
    // 添加点击事件处理
    function addClickHandlers() {
        console.log('[章节模板布局修复] 添加点击事件处理...');
        
        // 查找所有章节和维度链接
        const chapterLinks = document.querySelectorAll('.chapter-item, [data-chapter-id]');
        chapterLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                // 获取章节ID
                const chapterId = this.getAttribute('data-chapter-id');
                if (!chapterId) return;
                
                // 高亮选中项
                chapterLinks.forEach(item => item.classList.remove('active'));
                this.classList.add('active');
                
                // 加载章节模板
                loadChapterTemplate(chapterId);
                
                // 阻止默认行为
                e.preventDefault();
            });
        });
        
        // 查找所有维度链接
        const dimensionLinks = document.querySelectorAll('.dimension-item, [data-dimension]');
        dimensionLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                // 获取维度ID
                const dimensionId = this.getAttribute('data-dimension');
                if (!dimensionId) return;
                
                // 高亮选中项
                dimensionLinks.forEach(item => item.classList.remove('active'));
                this.classList.add('active');
                
                // 加载维度模板
                loadDimensionTemplate(dimensionId);
                
                // 阻止默认行为
                e.preventDefault();
            });
        });
        
        console.log('[章节模板布局修复] 点击事件处理添加完成');
    }
    
    // 加载章节模板
    function loadChapterTemplate(chapterId) {
        console.log('[章节模板布局修复] 加载章节模板:', chapterId);
        
        // 查找模板详情内容
        const templateDetailContent = document.getElementById('template-detail-content');
        if (!templateDetailContent) {
            console.log('[章节模板布局修复] 未找到模板详情内容，无法加载模板');
            return;
        }
        
        // 检查缓存
        if (templateCache['chapter_' + chapterId]) {
            console.log('[章节模板布局修复] 使用缓存的章节模板');
            templateDetailContent.innerHTML = templateCache['chapter_' + chapterId];
            return;
        }
        
        // 显示加载中
        templateDetailContent.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">加载章节模板中...</p>
            </div>
        `;
        
        // 获取小说ID
        const novelId = getNovelId();
        if (!novelId) {
            templateDetailContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    无法获取小说ID，请刷新页面重试
                </div>
            `;
            return;
        }
        
        // 发送请求获取章节模板
        fetch(`/api/novel/${novelId}/chapter/${chapterId}/template`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 显示章节模板内容
                if (data.success) {
                    const templateHtml = `
                        <h4>第${data.chapter_number}章: ${data.chapter_title}</h4>
                        <div class="template-content">
                            ${data.template_content || '暂无模板内容'}
                        </div>
                    `;
                    
                    templateDetailContent.innerHTML = templateHtml;
                    
                    // 缓存模板
                    templateCache['chapter_' + chapterId] = templateHtml;
                } else {
                    templateDetailContent.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载章节模板失败: ${data.message || '未知错误'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('[章节模板布局修复] 加载章节模板时出错:', error);
                templateDetailContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        加载章节模板失败: ${error.message}
                    </div>
                `;
            });
    }
    
    // 加载维度模板
    function loadDimensionTemplate(dimensionId) {
        console.log('[章节模板布局修复] 加载维度模板:', dimensionId);
        
        // 查找模板详情内容
        const templateDetailContent = document.getElementById('template-detail-content');
        if (!templateDetailContent) {
            console.log('[章节模板布局修复] 未找到模板详情内容，无法加载模板');
            return;
        }
        
        // 检查缓存
        if (templateCache['dimension_' + dimensionId]) {
            console.log('[章节模板布局修复] 使用缓存的维度模板');
            templateDetailContent.innerHTML = templateCache['dimension_' + dimensionId];
            return;
        }
        
        // 显示加载中
        templateDetailContent.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">加载维度模板中...</p>
            </div>
        `;
        
        // 获取小说ID
        const novelId = getNovelId();
        if (!novelId) {
            templateDetailContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    无法获取小说ID，请刷新页面重试
                </div>
            `;
            return;
        }
        
        // 发送请求获取维度模板
        fetch(`/api/novel/${novelId}/dimension/${dimensionId}/template`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 显示维度模板内容
                if (data.success) {
                    const templateHtml = `
                        <h4>${data.dimension_name}</h4>
                        <div class="template-content">
                            ${data.template_content || '暂无模板内容'}
                        </div>
                    `;
                    
                    templateDetailContent.innerHTML = templateHtml;
                    
                    // 缓存模板
                    templateCache['dimension_' + dimensionId] = templateHtml;
                } else {
                    templateDetailContent.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载维度模板失败: ${data.message || '未知错误'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('[章节模板布局修复] 加载维度模板时出错:', error);
                templateDetailContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        加载维度模板失败: ${error.message}
                    </div>
                `;
            });
    }
    
    // 获取小说ID
    function getNovelId() {
        // 从URL中获取
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        if (match && match[1]) {
            return match[1];
        }
        
        // 从页面元素中获取
        const novelIdElement = document.querySelector('[data-novel-id]');
        if (novelIdElement) {
            return novelIdElement.getAttribute('data-novel-id');
        }
        
        // 从隐藏字段中获取
        const hiddenInput = document.querySelector('input[name="novel_id"]');
        if (hiddenInput) {
            return hiddenInput.value;
        }
        
        return null;
    }
    
    // 观察DOM变化
    function observeDOMChanges() {
        console.log('[章节模板布局修复] 开始观察DOM变化...');
        
        // 创建观察器
        observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新的节点添加
                if (mutation.addedNodes.length > 0) {
                    // 检查是否需要修复章节模板布局
                    setTimeout(fixChapterTemplateLayout, 500);
                }
            });
        });
        
        // 开始观察
        observer.observe(document.body, { childList: true, subtree: true });
        
        console.log('[章节模板布局修复] DOM变化观察器已启动');
    }
    
    // 初始化
    function initialize() {
        console.log('[章节模板布局修复] 初始化...');
        
        // 添加样式
        addStyles();
        
        // 检查页面是否已加载
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('[章节模板布局修复] 页面已加载，开始修复');
            setTimeout(function() {
                fixChapterTemplateLayout();
                observeDOMChanges();
            }, 1000);
        } else {
            console.log('[章节模板布局修复] 页面尚未加载，等待DOMContentLoaded事件');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('[章节模板布局修复] DOMContentLoaded事件触发，开始修复');
                setTimeout(function() {
                    fixChapterTemplateLayout();
                    observeDOMChanges();
                }, 1000);
            });
        }
    }
    
    // 导出全局函数
    window.fixChapterTemplateLayout = fixChapterTemplateLayout;
    
    // 执行初始化
    initialize();
    
    console.log('[章节模板布局修复] 初始化完成');
})();
