/**
 * 九猫 - 统一错误处理脚本
 * 捕获和处理各种JavaScript错误，防止页面崩溃
 * 版本: 1.0.1
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('统一错误处理脚本已加载');
    
    // 错误计数器
    let errorCount = 0;
    const maxErrors = 50; // 最大错误数，超过后将停止输出日志
    const errorHistory = {}; // 用于存储已发生的错误，避免重复报告
    
    // 配置
    const config = {
        suppressConsoleErrors: false, // 是否抑制控制台错误
        attemptAutoFix: true,        // 尝试自动修复
        reportErrors: true,          // 是否报告错误
        logToServer: false,          // 是否将错误日志发送到服务器
        preventPageCrash: true       // 防止页面崩溃
    };
    
    // 设置全局错误处理
    window.addEventListener('error', function(event) {
        // 检查是否是跨域脚本错误
        if (event.message === "Script error." && !event.filename) {
            console.error('捕获到跨域脚本错误，无法获取详细信息');
            // 不增加错误计数，因为这是一个特殊情况
            return true;
        }
        
        errorCount++;
        
        // 检查是否超过最大错误数
        if (errorCount > maxErrors) {
            if (errorCount === maxErrors + 1) {
                console.error(`错误太多 (${maxErrors}+)，停止输出详细日志以避免性能问题`);
            }
            return true;
        }
        
        // 获取错误信息
        const errorMessage = event.message || '未知错误';
        const errorSource = event.filename || '未知来源';
        const lineNumber = event.lineno || '未知行号';
        const colNumber = event.colno || '未知列号';
        const errorStack = event.error && event.error.stack ? event.error.stack : '无堆栈信息';
        
        // 创建唯一错误ID
        const errorId = `${errorSource}:${lineNumber}:${errorMessage.substring(0, 50)}`;
        
        // 检查是否是重复错误
        if (errorHistory[errorId]) {
            errorHistory[errorId].count++;
            if (errorHistory[errorId].count <= 3) {
                console.warn(`重复错误 (${errorHistory[errorId].count}): ${errorMessage}`);
            }
            return true;
        }
        
        // 记录新错误
        errorHistory[errorId] = { count: 1, timestamp: Date.now() };
        
        // 输出错误信息
        console.error(`错误已被统一处理器捕获并忽略: ${errorMessage}`);
        
        // 防止页面崩溃
        if (config.preventPageCrash) {
            // 阻止事件冒泡
            if (event.stopPropagation) {
                event.stopPropagation();
            }
            
            // 阻止默认行为
            if (event.preventDefault) {
                event.preventDefault();
            }
        }
        
        return true; // 防止默认处理
    }, true);
    
    // 处理未捕获的Promise错误
    window.addEventListener('unhandledrejection', function(event) {
        errorCount++;
        
        // 检查是否超过最大错误数
        if (errorCount > maxErrors) {
            if (errorCount === maxErrors + 1) {
                console.error(`错误太多 (${maxErrors}+)，停止输出详细日志以避免性能问题`);
            }
            return true;
        }
        
        // 获取Promise错误信息
        const reason = event.reason || '未知原因';
        const message = reason.message || String(reason);
        const stack = reason.stack || '无堆栈信息';
        
        // 创建唯一错误ID
        const errorId = `promise:${message.substring(0, 50)}`;
        
        // 检查是否是重复错误
        if (errorHistory[errorId]) {
            errorHistory[errorId].count++;
            if (errorHistory[errorId].count <= 3) {
                console.warn(`重复Promise错误 (${errorHistory[errorId].count}): ${message}`);
            }
            return true;
        }
        
        // 记录新错误
        errorHistory[errorId] = { count: 1, timestamp: Date.now() };
        
        // 输出错误信息
        console.error(`错误已被统一处理器捕获并忽略: 捕获到JS错误: ${message}`);
        
        // 防止页面崩溃
        if (config.preventPageCrash) {
            // 阻止事件冒泡
            if (event.stopPropagation) {
                event.stopPropagation();
            }
            
            // 阻止默认行为
            if (event.preventDefault) {
                event.preventDefault();
            }
        }
        
        return true; // 防止默认处理
    });
    
    // 重写console.error，可选择性地抑制某些错误
    if (config.suppressConsoleErrors) {
        const originalConsoleError = console.error;
        console.error = function(...args) {
            // 检查是否是想要抑制的错误
            const errorMessage = String(args[0] || '');
            
            // 这里可以添加需要抑制的错误消息模式
            const suppressPatterns = [
                'ResizeObserver loop', // 常见的非关键错误
                'Script error.'        // 跨域脚本错误
            ];
            
            // 如果匹配任何抑制模式，则不输出
            if (suppressPatterns.some(pattern => errorMessage.includes(pattern))) {
                return;
            }
            
            // 对于其他错误，正常输出
            originalConsoleError.apply(console, args);
        };
    }
    
    console.log('统一错误处理脚本加载完成');
})();
