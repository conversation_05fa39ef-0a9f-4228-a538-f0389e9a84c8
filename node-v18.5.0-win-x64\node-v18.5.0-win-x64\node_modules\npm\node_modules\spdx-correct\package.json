{"name": "spdx-correct", "description": "correct invalid SPDX expressions", "version": "3.1.1", "author": "<PERSON> <<EMAIL>> (https://kemitchell.com)", "contributors": ["<PERSON> <<EMAIL>> (https://kemitchell.com)", "<PERSON> <<EMAIL>>", "Tal Einat <<EMAIL>>", "<PERSON> <<EMAIL>>"], "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}, "devDependencies": {"defence-cli": "^2.0.1", "replace-require-self": "^1.0.0", "standard": "^11.0.0", "standard-markdown": "^4.0.2", "tape": "^4.9.0"}, "files": ["index.js"], "keywords": ["SPDX", "law", "legal", "license", "metadata"], "license": "Apache-2.0", "repository": "jslicense/spdx-correct.js", "scripts": {"lint": "standard && standard-markdown README.md", "test": "defence README.md | replace-require-self | node && node test.js"}}