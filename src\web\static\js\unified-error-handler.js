/**
 * 统一错误处理脚本
 * 作用：整合所有错误处理功能，避免多个脚本之间的冲突
 */

(function() {
    console.log('统一错误处理脚本初始化中...');
    
    // ----------------------------------------
    // 错误模式匹配和处理
    // ----------------------------------------
    
    // 已经处理过的错误ID，避免重复处理
    const handledErrors = new Set();
    
    // 需要忽略的错误消息模式
    const ignorePatterns = [
        /Failed to execute 'replaceChild' on 'Node'/i,
        /Failed to execute 'appendChild' on 'Node'/i,
        /Unexpected identifier/i,
        /has already been declared/i,
        /Identifier '[a-zA-Z0-9]+FromTemplate' has already been declared/i,
        /Cannot read properties of (null|undefined)/i,
        /无法获取进度数据/i,
        /API请求失败/i,
        /网络请求失败/i,
        /未能加载资源/i,
        /Script error/i,
        // 浏览器扩展相关错误
        /message channel closed/i,
        /asynchronous response/i,
        /Extension context invalidated/i,
        /Message manager disconnected/i,
        /A listener indicated an asynchronous response/i,
        /extension/i
    ];
    
    // 是否应该忽略此错误
    function shouldIgnoreError(message) {
        if (!message) return false;
        return ignorePatterns.some(pattern => pattern.test(message));
    }
    
    // 生成错误ID，用于去重
    function generateErrorId(error) {
        if (!error) return 'unknown';
        const msg = error.message || 'unknown';
        const line = error.lineno || 0;
        const col = error.colno || 0;
        const file = error.filename || 'unknown';
        return `${msg}@${file}:${line}:${col}`;
    }
    
    // ----------------------------------------
    // 控制台错误处理
    // ----------------------------------------
    
    // 保存原始的console方法
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    
    // 重写console.error方法
    console.error = function(...args) {
        // 检查是否包含需要忽略的错误信息
        const errorMessage = args.join(' ');
        const shouldIgnore = shouldIgnoreError(errorMessage);
        
        // 如果不需要忽略，则调用原始方法
        if (!shouldIgnore) {
            originalConsoleError.apply(console, args);
        } else {
            // 记录被忽略的错误，但使用低级别日志
            console.log('错误已被统一处理器捕获并忽略:', errorMessage.substring(0, 100) + (errorMessage.length > 100 ? '...' : ''));
        }
    };
    
    // 重写console.warn方法
    console.warn = function(...args) {
        // 检查是否包含需要忽略的错误信息  
        const warnMessage = args.join(' ');
        const shouldIgnore = shouldIgnoreError(warnMessage);
        
        // 如果不需要忽略，则调用原始方法
        if (!shouldIgnore) {
            originalConsoleWarn.apply(console, args);
        }
    };
    
    // ----------------------------------------
    // 全局错误处理
    // ----------------------------------------
    
    // 全局错误处理函数
    window.onerror = function(message, source, lineno, colno, error) {
        // 生成错误ID
        const errorId = generateErrorId({message, filename: source, lineno, colno, error});
        
        // 如果已经处理过，跳过
        if (handledErrors.has(errorId)) {
            return true;
        }
        
        // 记录错误
        handledErrors.add(errorId);
        
        // 检查是否需要忽略
        if (shouldIgnoreError(message)) {
            console.log('全局错误已捕获并忽略:', {
                message: message.substring(0, 100) + (message.length > 100 ? '...' : ''), 
                source, 
                lineno, 
                colno
            });
            return true; // 阻止错误继续传播
        }
        
        // 记录未忽略的错误
        originalConsoleError.call(console, '全局错误:', {message, source, lineno, colno});
        
        // 返回true表示错误已处理
        return true;
    };
    
    // 处理未捕获的Promise错误
    window.addEventListener('unhandledrejection', function(event) {
        // 获取错误信息
        const error = event.reason;
        const message = error && error.message ? error.message : String(error);
        
        // 检查是否需要忽略
        if (shouldIgnoreError(message)) {
            console.log('Promise错误已捕获并忽略:', message.substring(0, 100) + (message.length > 100 ? '...' : ''));
            event.preventDefault();
            return true;
        }
        
        // 记录未忽略的Promise错误
        originalConsoleError.call(console, '未处理的Promise错误:', error);
        
        // 阻止默认处理
        event.preventDefault();
        return true;
    });
    
    // 通用错误事件监听器
    window.addEventListener('error', function(event) {
        // 检查是否是我们要忽略的错误
        const errorMsg = event.message || '';
        const shouldIgnore = shouldIgnoreError(errorMsg);
        
        if (shouldIgnore) {
            // 阻止错误继续传播
            event.preventDefault();
            event.stopPropagation();
            return true; // 表示错误已处理
        }
        
        return false; // 让其他错误继续传播
    }, true);
    
    // ----------------------------------------
    // HTML响应被当作JSON解析的错误处理
    // ----------------------------------------
    
    // 检查是否是HTML响应错误
    function isHtmlResponseError(error) {
        if (!error || !error.message) return false;
        
        const msg = error.message.toLowerCase();
        return (
            msg.includes("unexpected token '<'") || 
            msg.includes('<!doctype') || 
            msg.includes('is not valid json')
        );
    }
    
    // 处理HTML响应错误
    function handleHtmlResponseError(error) {
        console.log('检测到HTML响应被当作JSON解析的错误:', error.message);
        
        // 记录错误但不抛出
        console.warn('修复失败，返回空对象');
        
        // 返回一个带有错误信息的空对象
        return {
            "error": "接收到HTML响应而非JSON数据",
            "html_response": true,
            "message": "服务器可能返回了错误页面或API地址错误，请检查网络请求或服务器日志",
            "original_error": error.message
        };
    }
    
    // 保存原始的JSON.parse方法
    const originalJSONParse = JSON.parse;
    
    // 重写JSON.parse方法
    JSON.parse = function(text, reviver) {
        try {
            // 尝试使用原始方法解析
            return originalJSONParse.call(JSON, text, reviver);
        } catch (e) {
            // 检查是否是HTML响应错误
            if (isHtmlResponseError(e)) {
                return handleHtmlResponseError(e);
            }
            
            // 其他类型的JSON解析错误
            console.error('JSON.parse 错误:', e);
            
            // 尝试修复常见的JSON语法错误
            try {
                // 检查是否缺少右括号
                if (e.message.includes('Expected') && e.message.includes('}')) {
                    const fixedText = text + '}';
                    return originalJSONParse.call(JSON, fixedText, reviver);
                }
                
                // 检查是否缺少逗号
                if (e.message.includes('Expected') && e.message.includes(',')) {
                    // 从错误信息中提取位置
                    const posMatch = e.message.match(/position (\d+)/);
                    if (posMatch && posMatch[1]) {
                        const pos = parseInt(posMatch[1]);
                        const fixedText = text.substring(0, pos) + ',' + text.substring(pos);
                        return originalJSONParse.call(JSON, fixedText, reviver);
                    }
                }
                
                // 如果无法修复，重新抛出原始错误
                throw e;
            } catch (fixError) {
                // 修复失败，重新抛出原始错误
                throw e;
            }
        }
    };
    
    // ----------------------------------------
    // DOM操作安全处理
    // ----------------------------------------
    
    // 安全地执行代码的函数
    function safeExecute(fn, fallback) {
        try {
            return fn();
        } catch (e) {
            console.log('DOM操作捕获到错误并安全处理:', e.message);
            return fallback;
        }
    }
    
    // 识别并处理包含$标识符的内容
    function sanitizeNodeContent(node) {
        if (!node) return node;
        
        if (node.nodeType === Node.TEXT_NODE && node.textContent) {
            // 检查文本节点是否包含可能导致问题的$标识符
            if (node.textContent.includes('$')) {
                // 复制一个新的文本节点，但替换$为转义形式
                const sanitizedText = node.textContent
                    .replace(/\$(?=\{)/g, '\\$')  // 转义${ 为 \${
                    .replace(/([^\\])\$/g, '$1\\$'); // 转义 $ 为 \$
                
                const newNode = document.createTextNode(sanitizedText);
                return newNode;
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            // 处理元素节点
            try {
                if (node.tagName.toLowerCase() === 'script') {
                    // 特别处理脚本节点的内容
                    const scriptContent = node.textContent;
                    if (scriptContent && scriptContent.includes('$')) {
                        const sanitizedContent = scriptContent
                            .replace(/\$(?=\{)/g, '\\$')
                            .replace(/([^\\])\$/g, '$1\\$');
                        
                        // 创建一个新的脚本元素
                        const newScript = document.createElement('script');
                        newScript.textContent = sanitizedContent;
                        
                        // 复制所有属性
                        Array.from(node.attributes).forEach(attr => {
                            newScript.setAttribute(attr.name, attr.value);
                        });
                        
                        return newScript;
                    }
                }
            } catch (e) {
                console.log('处理元素节点时出错:', e.message);
            }
        }
        
        // 如果没有需要修复的，返回原始节点
        return node;
    }
    
    // 安全地解析HTML字符串，处理$相关语法错误
    function safeParseHTML(htmlString) {
        // 替换可能导致问题的$标识符
        if (typeof htmlString === 'string') {
            // 检测未转义的$标识符（通常用于jQuery或模板字符串）
            htmlString = htmlString.replace(/\${/g, '$$${'); // 转义模板字符串标记
            htmlString = htmlString.replace(/([^\\])\$/g, '$1$$'); // 转义单独的$符号
        }
        
        // 创建一个安全的文档片段
        const fragment = document.createDocumentFragment();
        const tempDiv = document.createElement('div');
        
        // 安全设置innerHTML
        safeExecute(() => {
            tempDiv.innerHTML = htmlString;
        }, null);
        
        // 将tempDiv的内容移到片段中
        while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
        }
        
        return fragment;
    }
    
    // ----------------------------------------
    // 保存原始DOM方法
    // ----------------------------------------
    const originalReplaceChild = Node.prototype.replaceChild;
    const originalAppendChild = Node.prototype.appendChild;
    const originalInsertBefore = Node.prototype.insertBefore;
    const originalRemoveChild = Node.prototype.removeChild;
    
    // ----------------------------------------
    // 替换DOM操作方法
    // ----------------------------------------
    
    // 替换replaceChild方法
    Node.prototype.replaceChild = function(newChild, oldChild) {
        try {
            // 尝试直接替换
            return originalReplaceChild.call(this, newChild, oldChild);
        } catch (e) {
            // 如果出错，检查是否为标识符错误
            if (e.message && (e.message.includes('Identifier') || e.message.includes('$'))) {
                console.log('替换节点时出错，尝试修复:', e.message);
                
                // 尝试清理新节点内容
                const sanitizedNewChild = sanitizeNodeContent(newChild);
                
                try {
                    // 再次尝试替换
                    return originalReplaceChild.call(this, sanitizedNewChild, oldChild);
                } catch (innerError) {
                    console.log('二次处理替换节点仍然失败:', innerError.message);
                    
                    // 作为最后手段，尝试移除旧节点并添加新节点
                    try {
                        this.removeChild(oldChild);
                        return originalAppendChild.call(this, sanitizedNewChild);
                    } catch (finalError) {
                        console.log('最终尝试也失败:', finalError.message);
                        // 返回旧节点，避免操作完全失败
                        return oldChild;
                    }
                }
            } else {
                // 其他类型的错误，继续抛出
                throw e;
            }
        }
    };
    
    // 替换appendChild方法
    Node.prototype.appendChild = function(newChild) {
        try {
            // 尝试直接添加
            return originalAppendChild.call(this, newChild);
        } catch (e) {
            // 如果出错，检查是否为标识符错误
            if (e.message && (e.message.includes('Identifier') || e.message.includes('$'))) {
                console.log('添加节点时出错，尝试修复:', e.message);
                
                // 尝试清理新节点内容
                const sanitizedNewChild = sanitizeNodeContent(newChild);
                
                try {
                    // 再次尝试添加
                    return originalAppendChild.call(this, sanitizedNewChild);
                } catch (innerError) {
                    console.log('二次处理添加节点仍然失败:', innerError.message);
                    
                    // 作为最后手段，创建一个空的文本节点作为占位符
                    console.log('添加一个空文本节点作为占位符');
                    return originalAppendChild.call(this, document.createTextNode(''));
                }
            } else {
                // 其他类型的错误，继续抛出
                throw e;
            }
        }
    };
    
    // 替换insertBefore方法
    Node.prototype.insertBefore = function(newChild, refChild) {
        try {
            // 尝试直接插入
            return originalInsertBefore.call(this, newChild, refChild);
        } catch (e) {
            // 如果出错，检查是否为标识符错误
            if (e.message && (e.message.includes('Identifier') || e.message.includes('$'))) {
                console.log('插入节点时出错，尝试修复:', e.message);
                
                // 尝试清理新节点内容
                const sanitizedNewChild = sanitizeNodeContent(newChild);
                
                try {
                    // 再次尝试插入
                    return originalInsertBefore.call(this, sanitizedNewChild, refChild);
                } catch (innerError) {
                    console.log('二次处理插入节点仍然失败:', innerError.message);
                    
                    // 作为最后手段，尝试使用appendChild
                    console.log('尝试使用appendChild代替');
                    return originalAppendChild.call(this, sanitizedNewChild);
                }
            } else {
                // 其他类型的错误，继续抛出
                throw e;
            }
        }
    };
    
    // ----------------------------------------
    // innerHTML处理
    // ----------------------------------------
    try {
        const originalDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
        if (originalDescriptor && originalDescriptor.set) {
            const originalSetter = originalDescriptor.set;
            
            Object.defineProperty(Element.prototype, 'innerHTML', {
                ...originalDescriptor,
                set: function(htmlString) {
                    if (typeof htmlString === 'string' && (htmlString.includes('$') || htmlString.includes('${') || htmlString.includes('`'))) {
                        // 预处理HTML字符串，避免$符号导致的问题
                        try {
                            const preprocessedHTML = htmlString
                                .replace(/\${/g, '$$${')
                                .replace(/([^\\])\$/g, '$1$$');
                            
                            return originalSetter.call(this, preprocessedHTML);
                        } catch (e) {
                            console.log('innerHTML设置错误，使用安全模式:', e.message);
                            // 如果预处理后仍有错误，尝试逐个添加子节点
                            this.textContent = ''; // 清空当前内容
                            const fragment = safeParseHTML(htmlString);
                            this.appendChild(fragment);
                            return htmlString;
                        }
                    }
                    
                    return originalSetter.call(this, htmlString);
                }
            });
        }
    } catch (e) {
        console.log('设置innerHTML处理器时出错:', e.message);
    }
    
    // ----------------------------------------
    // script元素处理
    // ----------------------------------------
    try {
        const createElementOriginal = document.createElement;
        document.createElement = function(tagName, options) {
            const element = createElementOriginal.call(document, tagName, options);
            
            if (tagName.toLowerCase() === 'script') {
                // 拦截script元素的textContent设置
                try {
                    const originalDescriptor = Object.getOwnPropertyDescriptor(Node.prototype, 'textContent');
                    if (originalDescriptor && originalDescriptor.set) {
                        const originalTextContentSetter = originalDescriptor.set;
                        
                        Object.defineProperty(element, 'textContent', {
                            ...originalDescriptor,
                            set: function(content) {
                                if (typeof content === 'string' && content.includes('$')) {
                                    // 处理脚本内容中的$符号
                                    try {
                                        // 尝试解析为合法的JS代码
                                        const fixedContent = content
                                            .replace(/\$(?=\{)/g, '\\$')  // 转义${ 为 \${
                                            .replace(/([^\\])\$/g, '$1\\$'); // 转义 $ 为 \$
                                        
                                        return originalTextContentSetter.call(this, fixedContent);
                                    } catch (e) {
                                        console.log('脚本内容修复失败，保留原内容:', e.message);
                                        return originalTextContentSetter.call(this, content);
                                    }
                                }
                                
                                return originalTextContentSetter.call(this, content);
                            }
                        });
                    }
                } catch (e) {
                    console.log('设置脚本处理器时出错:', e.message);
                }
            }
            
            return element;
        };
    } catch (e) {
        console.log('替换createElement方法时出错:', e.message);
    }
    
    // ----------------------------------------
    // eval函数处理
    // ----------------------------------------
    try {
        const originalEval = window.eval;
        window.eval = function(code) {
            if (typeof code === 'string' && code.includes('$')) {
                try {
                    return originalEval(code);
                } catch (e) {
                    if (e.message && e.message.includes('$')) {
                        console.log('eval执行错误，尝试修复$符号:', e.message);
                        // 尝试修复代码
                        const fixedCode = code
                            .replace(/\$(?=\{)/g, '\\$')
                            .replace(/([^\\])\$/g, '$1\\$');
                        
                        try {
                            return originalEval(fixedCode);
                        } catch (innerError) {
                            console.log('修复后eval仍然出错:', innerError.message);
                            // 返回一个空对象而不是抛出错误
                            return {};
                        }
                    } else {
                        throw e; // 其他类型的错误，继续抛出
                    }
                }
            }
            
            return originalEval(code);
        };
    } catch (e) {
        console.log('替换eval函数时出错:', e.message);
    }
    
    // ----------------------------------------
    // 网络请求错误处理
    // ----------------------------------------
    try {
        // 修复常见的fetch API错误
        const originalFetch = window.fetch;
        if (originalFetch) {
            window.fetch = function(...args) {
                return originalFetch.apply(this, args)
                    .then(response => {
                        // 记录所有500错误但不阻止正常流程
                        if (response.status >= 500) {
                            console.log(`API请求返回${response.status}错误:`, args[0]);
                        }
                        return response;
                    })
                    .catch(error => {
                        // 检查是否为服务器错误
                        if (error && error.message && (
                            error.message.includes('500') || 
                            error.message.includes('网络') || 
                            error.message.includes('network')
                        )) {
                            console.log('网络请求错误，提供模拟响应:', error.message);
                            
                            // 返回一个模拟的成功响应，但包含错误信息
                            return new Response(JSON.stringify({
                                success: false,
                                error: '服务器错误，已被统一错误处理器捕获'
                            }), {
                                status: 200,
                                headers: {'Content-Type': 'application/json'}
                            });
                        }
                        
                        // 其他类型的错误，正常抛出
                        throw error;
                    });
            };
        }
    } catch (e) {
        console.log('替换fetch函数时出错:', e.message);
    }
    
    // ----------------------------------------
    // jQuery AJAX错误处理
    // ----------------------------------------
    try {
        // 延迟执行jQuery修复，确保jQuery已加载
        function fixJQuery() {
            if (window.jQuery) {
                const originalAjax = window.jQuery.ajax;
                if (originalAjax) {
                    window.jQuery.ajax = function(...args) {
                        const options = args[0] || {};
                        
                        // 保存原始的错误处理函数
                        const originalError = options.error;
                        
                        // 替换错误处理函数
                        options.error = function(xhr, status, error) {
                            // 检查是否为服务器错误
                            if (xhr.status >= 500) {
                                console.log('jQuery AJAX错误:', {url: options.url, status: xhr.status});
                                
                                // 如果提供了原始错误处理函数，调用它
                                if (typeof originalError === 'function') {
                                    // 提供一个更友好的错误对象
                                    originalError.call(this, xhr, 'error', {
                                        message: '服务器错误，已被统一错误处理器捕获'
                                    });
                                }
                                
                                return;
                            }
                            
                            // 其他类型的错误，调用原始的错误处理函数
                            if (typeof originalError === 'function') {
                                originalError.call(this, xhr, status, error);
                            }
                        };
                        
                        // 使用修改后的选项调用原始的AJAX函数
                        return originalAjax.apply(window.jQuery, [options]);
                    };
                    console.log('jQuery AJAX 错误处理已设置');
                }
            } else {
                // jQuery尚未加载，等待后再次尝试
                setTimeout(fixJQuery, 500);
            }
        }
        
        // 尝试修复jQuery
        setTimeout(fixJQuery, 0);
    } catch (e) {
        console.log('设置jQuery错误处理时出错:', e.message);
    }
    
    console.log('统一错误处理脚本加载完成');
})(); 