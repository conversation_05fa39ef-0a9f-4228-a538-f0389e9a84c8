/**
 * 九猫 - 修复"Failed to execute 'replaceChild' on 'Node': Unexpected identifier '$'"错误
 * 版本: 1.1.0
 *
 * 这个脚本专门解决Chart.js相关的replaceChild错误，特别是包含"Unexpected identifier '$'"的错误
 * 此脚本在页面加载前就执行，确保在其他脚本之前修复replaceChild方法
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('加载Unexpected identifier修复脚本 - 优先级最高');

    // 立即执行修复，不等待DOMContentLoaded事件

    // 保存原始的replaceChild方法
    const originalReplaceChild = Node.prototype.replaceChild;

    // 重写replaceChild方法
    Node.prototype.replaceChild = function(newChild, oldChild) {
        try {
            // 尝试使用原始方法
            return originalReplaceChild.call(this, newChild, oldChild);
        } catch (e) {
            console.error('捕获到replaceChild错误:', e.message);

            // 检查是否包含"Unexpected identifier '$'"错误
            if (e.message && e.message.includes('Unexpected identifier')) {
                console.log('检测到特定的replaceChild错误，尝试安全替换');

                try {
                    // 安全替换：先移除旧节点，再添加新节点
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }

                    // 创建一个新的元素，而不是直接使用newChild
                    let safeNode;

                    if (newChild.nodeType === Node.ELEMENT_NODE) {
                        // 对于元素节点，创建相同类型的新元素
                        safeNode = document.createElement(newChild.tagName);

                        // 复制所有属性
                        for (let i = 0; i < newChild.attributes.length; i++) {
                            const attr = newChild.attributes[i];
                            safeNode.setAttribute(attr.name, attr.value);
                        }

                        // 复制内容
                        safeNode.innerHTML = newChild.innerHTML;
                    } else if (newChild.nodeType === Node.TEXT_NODE) {
                        // 对于文本节点，创建新的文本节点
                        safeNode = document.createTextNode(newChild.textContent);
                    } else {
                        // 对于其他类型的节点，创建一个空的div作为替代
                        safeNode = document.createElement('div');
                    }

                    // 添加新节点
                    this.appendChild(safeNode);

                    return safeNode;
                } catch (e2) {
                    console.error('安全替换也失败:', e2.message);

                    // 最后的尝试：直接添加原始节点的克隆
                    try {
                        const clonedNode = newChild.cloneNode(true);
                        this.appendChild(clonedNode);
                        return clonedNode;
                    } catch (e3) {
                        console.error('所有替代方法都失败:', e3.message);
                        throw e; // 重新抛出原始错误
                    }
                }
            } else {
                // 如果不是特定错误，尝试通用修复
                try {
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }
                    this.appendChild(newChild);
                    return newChild;
                } catch (e2) {
                    console.error('通用修复也失败:', e2.message);
                    throw e; // 重新抛出原始错误
                }
            }
        }
    };

    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message &&
            event.error.message.includes('Unexpected identifier')) {
            console.error('捕获到Unexpected identifier错误:', event.error.message);
            console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);

            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);

    // 修复Chart.js相关的问题
    function fixChartJsIssues() {
        // 检查是否有Chart.js
        if (typeof Chart !== 'undefined') {
            console.log('检测到Chart.js，应用特定修复');

            // 保存原始的Chart构造函数
            const originalChart = Chart;

            // 重写Chart构造函数
            window.Chart = function(ctx, config) {
                try {
                    // 尝试使用原始构造函数
                    return new originalChart(ctx, config);
                } catch (e) {
                    console.error('创建Chart时出错:', e.message);

                    // 如果是Canvas已在使用的错误，尝试创建新的canvas
                    if (e.message && e.message.includes('Canvas is already in use')) {
                        console.log('检测到Canvas已在使用错误，尝试创建新的canvas');

                        try {
                            // 获取原始canvas
                            const originalCanvas = ctx.canvas;

                            // 创建新的canvas
                            const newCanvas = document.createElement('canvas');
                            newCanvas.width = originalCanvas.width || 400;
                            newCanvas.height = originalCanvas.height || 300;
                            newCanvas.id = originalCanvas.id || '';
                            newCanvas.className = originalCanvas.className || '';

                            // 替换canvas
                            if (originalCanvas.parentNode) {
                                try {
                                    originalCanvas.parentNode.replaceChild(newCanvas, originalCanvas);
                                } catch (replaceError) {
                                    console.error('替换canvas时出错:', replaceError);

                                    // 如果替换失败，尝试在原始canvas后面添加新canvas
                                    originalCanvas.style.display = 'none';
                                    if (originalCanvas.nextSibling) {
                                        originalCanvas.parentNode.insertBefore(newCanvas, originalCanvas.nextSibling);
                                    } else {
                                        originalCanvas.parentNode.appendChild(newCanvas);
                                    }
                                }
                            }

                            // 在新canvas上创建图表
                            const newCtx = newCanvas.getContext('2d');
                            return new originalChart(newCtx, config);
                        } catch (e2) {
                            console.error('创建新canvas也失败:', e2.message);
                        }
                    }

                    // 如果所有尝试都失败，返回null
                    return null;
                }
            };

            // 复制原始Chart的属性和方法
            for (const prop in originalChart) {
                if (originalChart.hasOwnProperty(prop)) {
                    window.Chart[prop] = originalChart[prop];
                }
            }

            // 复制原型
            window.Chart.prototype = originalChart.prototype;
        }
    }

    // 立即执行Chart.js修复
    fixChartJsIssues();

    // 在页面加载完成后再次执行修复，确保覆盖所有情况
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixChartJsIssues);
    }

    // 添加全局变量标记，表示已应用修复
    window.__unexpectedIdentifierFixed = true;
})();
