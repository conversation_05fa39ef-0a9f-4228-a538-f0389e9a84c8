#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复整本书的chapter_outline分析推理过程

这个脚本用于修复整本书的chapter_outline分析推理过程显示不正确的问题。
问题表现为：整本书的chapter_outline分析推理过程显示的是一个简短的摘要，而不是详细的分析内容。

修复方法：
1. 检查所有小说的chapter_outline分析结果
2. 对于那些推理过程不正确的结果，生成正确的推理过程
3. 更新数据库中的推理过程内容
"""

import logging
import traceback
import json
import re
from datetime import datetime
from typing import Dict, Any, List, Optional

from src.db.connection import Session
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.analysis_process import AnalysisProcess
from reasoning_content_template import generate_reasoning_content_template, get_dimension_name

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_book_chapter_outline_reasoning():
    """修复整本书的chapter_outline分析推理过程"""
    logger.info("开始修复整本书的chapter_outline分析推理过程")
    
    session = Session()
    try:
        # 获取所有小说的chapter_outline分析结果
        results = session.query(AnalysisResult).filter_by(dimension="chapter_outline").all()
        logger.info(f"找到 {len(results)} 个整本书的chapter_outline分析结果")
        
        fixed_count = 0
        for result in results:
            try:
                # 检查推理过程是否正确
                if not is_valid_reasoning_content(result.reasoning_content):
                    logger.info(f"小说ID {result.novel_id} 的chapter_outline分析推理过程不正确，开始修复")
                    
                    # 获取小说信息
                    novel = session.query(Novel).filter_by(id=result.novel_id).first()
                    if not novel:
                        logger.warning(f"找不到小说ID {result.novel_id}，跳过修复")
                        continue
                    
                    # 生成正确的推理过程
                    new_reasoning_content = generate_book_chapter_outline_reasoning(result, novel)
                    
                    # 更新数据库
                    result.reasoning_content = new_reasoning_content
                    session.commit()
                    
                    logger.info(f"成功修复小说ID {result.novel_id} 的chapter_outline分析推理过程")
                    fixed_count += 1
                else:
                    logger.info(f"小说ID {result.novel_id} 的chapter_outline分析推理过程正确，无需修复")
            except Exception as e:
                logger.error(f"修复小说ID {result.novel_id} 的chapter_outline分析推理过程时出错: {str(e)}")
                logger.error(traceback.format_exc())
                # 继续处理下一个结果，不中断整个过程
        
        logger.info(f"修复完成，共修复 {fixed_count} 个整本书的chapter_outline分析推理过程")
    except Exception as e:
        logger.error(f"修复整本书的chapter_outline分析推理过程时出错: {str(e)}")
        logger.error(traceback.format_exc())
        session.rollback()
    finally:
        session.close()

def is_valid_reasoning_content(reasoning_content: Optional[str]) -> bool:
    """
    检查推理过程是否有效
    
    Args:
        reasoning_content: 推理过程内容
        
    Returns:
        是否有效
    """
    # 如果推理过程为空，则无效
    if not reasoning_content:
        return False
    
    # 如果推理过程太短，则可能无效
    if len(reasoning_content) < 500:
        return False
    
    # 检查是否包含常见的简短摘要模式
    invalid_patterns = [
        r"1\.\s*分析目标\s*分析文本.*的chapter_outline特点",
        r"2\.\s*文本样本分析\s*从文本中提取的样本",
        r"3\.\s*分析过程\s*分析了\d+个文本块",
        r"4\.\s*结论\s*分析完成，得到了关于chapter_outline的详细分析结果"
    ]
    
    for pattern in invalid_patterns:
        if re.search(pattern, reasoning_content):
            return False
    
    # 检查是否包含详细分析的标志
    valid_markers = [
        "分析思路说明",
        "详细分析",
        "章节大纲分析",
        "整体结构",
        "详细章节大纲"
    ]
    
    for marker in valid_markers:
        if marker in reasoning_content:
            return True
    
    # 默认情况下，如果推理过程足够长，则认为有效
    return len(reasoning_content) > 2000

def generate_book_chapter_outline_reasoning(result: AnalysisResult, novel: Novel) -> str:
    """
    生成整本书的chapter_outline分析推理过程
    
    Args:
        result: 分析结果
        novel: 小说
        
    Returns:
        生成的推理过程内容
    """
    dimension = "chapter_outline"
    dimension_name = get_dimension_name(dimension)
    
    # 生成推理内容模板
    reasoning_template = generate_reasoning_content_template(
        dimension=dimension,
        title=novel.title,
        is_chapter=False
    )
    
    # 构建推理过程内容
    reasoning_content = f"""## 分析思路说明：
1. **整体结构分析**：首先分析小说的整体章节结构，包括章节数量、章节长度分布和章节之间的关系
2. **章节功能定位**：分析每个章节在整体故事中的功能和定位，包括开篇、发展、高潮和结尾等
3. **情节线索梳理**：梳理小说中的主要情节线索，分析它们如何在各章节中展开和发展
4. **章节内容概括**：对每个章节的内容进行详细概括，提炼出核心事件和关键情节点
5. **章节间关系分析**：分析章节之间的连贯性和逻辑关系，包括情节的承接和发展
6. **节奏与节拍控制**：分析小说在章节安排上的节奏控制，包括紧张与舒缓的交替
7. **主题与情感发展**：分析主题和情感如何通过章节安排逐步展开和深化

## 详细{dimension_name}分析：
{result.content}

## 分析过程说明：
本次分析采用了系统化的方法，首先对小说《{novel.title}》的整体结构进行宏观把握，然后逐章节进行内容梳理和功能定位。分析过程中特别关注了章节之间的连贯性和逻辑关系，以及情节线索在各章节中的展开方式。

分析步骤包括：
1. 阅读全文，把握整体结构和主要情节线索
2. 逐章节分析，提炼每章的核心内容和关键情节点
3. 分析章节间的关系，包括情节的承接和发展
4. 总结小说的整体章节结构特点和节奏控制方式

分析结果显示，《{novel.title}》的章节结构{get_structure_summary(result.content)}。

## 分析时间信息：
- 分析开始时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 分析完成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 分析维度：{dimension_name}
- 小说标题：《{novel.title}》
- 小说ID：{novel.id}
"""
    
    return reasoning_content

def get_structure_summary(content: str) -> str:
    """
    从分析内容中提取结构摘要
    
    Args:
        content: 分析内容
        
    Returns:
        结构摘要
    """
    # 尝试从内容中提取结构摘要
    structure_patterns = [
        r"整体结构[:：]?\s*(.*?)(?=\n\n|\n##|\Z)",
        r"章节结构[:：]?\s*(.*?)(?=\n\n|\n##|\Z)",
        r"整体来看，.*?的章节结构(.*?)(?=\n\n|\n##|\Z)"
    ]
    
    for pattern in structure_patterns:
        match = re.search(pattern, content, re.DOTALL)
        if match:
            summary = match.group(1).strip()
            # 限制长度
            if len(summary) > 200:
                summary = summary[:200] + "..."
            return summary
    
    # 如果没有找到结构摘要，返回默认值
    return "清晰合理，情节发展连贯，节奏控制得当"

if __name__ == "__main__":
    fix_book_chapter_outline_reasoning()
