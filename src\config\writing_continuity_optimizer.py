"""
九猫系统写作连贯性优化器
专门为写作功能提供前序章节信息，确保写作质量不受成本优化影响
"""

import logging
from typing import Dict, Any, List
import re

logger = logging.getLogger(__name__)

class WritingContinuityOptimizer:
    """写作连贯性优化器"""
    
    @staticmethod
    def extract_writing_context(previous_analyses: List[Dict], 
                              previous_chapters_content: List[Dict],
                              current_chapter_number: int,
                              prompt_template: str = "default") -> Dict[str, str]:
        """
        为写作功能提取前序章节的关键信息
        
        Args:
            previous_analyses: 前序章节分析结果
            previous_chapters_content: 前序章节内容
            current_chapter_number: 当前章节编号
            prompt_template: 提示词模板
            
        Returns:
            写作所需的前序章节信息
        """
        try:
            # 写作功能需要更丰富的上下文信息，不能过度压缩
            if prompt_template == "simplified":
                return WritingContinuityOptimizer._extract_essential_context(
                    previous_analyses, previous_chapters_content, current_chapter_number
                )
            else:
                return WritingContinuityOptimizer._extract_comprehensive_context(
                    previous_analyses, previous_chapters_content, current_chapter_number
                )
                
        except Exception as e:
            logger.error(f"提取写作上下文时出错: {str(e)}")
            return {
                "plot_continuity": "前序章节信息提取失败",
                "character_development": "",
                "style_consistency": "",
                "emotional_flow": ""
            }
    
    @staticmethod
    def _extract_essential_context(previous_analyses: List[Dict], 
                                 previous_chapters_content: List[Dict],
                                 current_chapter_number: int) -> Dict[str, str]:
        """
        提取写作必需的核心上下文（精简版）
        
        策略：
        1. 保留最近2章的关键剧情信息
        2. 提取人物关系变化
        3. 保持情感基调连贯性
        4. 控制总长度在1000字以内
        """
        context = {
            "plot_continuity": "",
            "character_development": "",
            "style_consistency": "",
            "emotional_flow": ""
        }
        
        try:
            # 限制处理的章节数量
            recent_analyses = previous_analyses[-2:] if len(previous_analyses) > 2 else previous_analyses
            recent_content = previous_chapters_content[-2:] if len(previous_chapters_content) > 2 else previous_chapters_content
            
            # 提取剧情连贯性信息
            plot_points = []
            for analysis in recent_analyses:
                content = analysis.get("content", "")
                # 提取剧情关键点
                plot_info = WritingContinuityOptimizer._extract_plot_points(content, max_length=200)
                if plot_info:
                    chapter_title = analysis.get("chapter_title", f"第{analysis.get('chapter_number')}章")
                    plot_points.append(f"{chapter_title}: {plot_info}")
            
            context["plot_continuity"] = "\n".join(plot_points)
            
            # 提取人物发展信息
            character_info = []
            for analysis in recent_analyses:
                content = analysis.get("content", "")
                char_development = WritingContinuityOptimizer._extract_character_info(content, max_length=150)
                if char_development:
                    character_info.append(char_development)
            
            context["character_development"] = "；".join(character_info)
            
            # 提取风格一致性信息
            style_info = WritingContinuityOptimizer._extract_style_info(recent_analyses, max_length=200)
            context["style_consistency"] = style_info
            
            # 提取情感流向信息
            emotional_info = WritingContinuityOptimizer._extract_emotional_flow(recent_content, max_length=200)
            context["emotional_flow"] = emotional_info
            
            logger.info(f"[写作上下文-精简版] 提取完成，剧情: {len(context['plot_continuity'])}字符，人物: {len(context['character_development'])}字符")
            
            return context
            
        except Exception as e:
            logger.error(f"提取精简版写作上下文时出错: {str(e)}")
            return context
    
    @staticmethod
    def _extract_comprehensive_context(previous_analyses: List[Dict], 
                                     previous_chapters_content: List[Dict],
                                     current_chapter_number: int) -> Dict[str, str]:
        """
        提取全面的写作上下文（默认版）
        
        策略：
        1. 保留最近3-4章的详细信息
        2. 提供丰富的人物关系和情感变化
        3. 保持风格和语调的连贯性
        4. 总长度控制在2000字以内
        """
        context = {
            "plot_continuity": "",
            "character_development": "",
            "style_consistency": "",
            "emotional_flow": ""
        }
        
        try:
            # 保留更多章节信息
            recent_analyses = previous_analyses[-4:] if len(previous_analyses) > 4 else previous_analyses
            recent_content = previous_chapters_content[-3:] if len(previous_chapters_content) > 3 else previous_chapters_content
            
            # 提取详细的剧情连贯性信息
            plot_sections = []
            for analysis in recent_analyses:
                content = analysis.get("content", "")
                plot_info = WritingContinuityOptimizer._extract_plot_points(content, max_length=400)
                if plot_info:
                    chapter_title = analysis.get("chapter_title", f"第{analysis.get('chapter_number')}章")
                    plot_sections.append(f"### {chapter_title}\n{plot_info}")
            
            context["plot_continuity"] = "\n\n".join(plot_sections)
            
            # 提取详细的人物发展信息
            character_sections = []
            for analysis in recent_analyses:
                content = analysis.get("content", "")
                char_development = WritingContinuityOptimizer._extract_character_info(content, max_length=300)
                if char_development:
                    character_sections.append(char_development)
            
            context["character_development"] = "\n".join(character_sections)
            
            # 提取风格一致性信息
            style_info = WritingContinuityOptimizer._extract_style_info(recent_analyses, max_length=400)
            context["style_consistency"] = style_info
            
            # 提取情感流向信息
            emotional_info = WritingContinuityOptimizer._extract_emotional_flow(recent_content, max_length=400)
            context["emotional_flow"] = emotional_info
            
            logger.info(f"[写作上下文-默认版] 提取完成，剧情: {len(context['plot_continuity'])}字符，人物: {len(context['character_development'])}字符")
            
            return context
            
        except Exception as e:
            logger.error(f"提取默认版写作上下文时出错: {str(e)}")
            return context
    
    @staticmethod
    def _extract_plot_points(content: str, max_length: int = 300) -> str:
        """提取剧情关键点"""
        if not content:
            return ""
        
        # 查找剧情相关的关键句子
        plot_keywords = ['情节', '剧情', '故事', '发展', '转折', '冲突', '高潮', '结局', '事件']
        
        sentences = re.split(r'[。！？]', content)
        plot_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if any(keyword in sentence for keyword in plot_keywords):
                plot_sentences.append(sentence)
        
        # 合并并限制长度
        result = '。'.join(plot_sentences[:3])  # 最多3句
        return result[:max_length] + "..." if len(result) > max_length else result
    
    @staticmethod
    def _extract_character_info(content: str, max_length: int = 250) -> str:
        """提取人物信息"""
        if not content:
            return ""
        
        # 查找人物相关的关键句子
        character_keywords = ['人物', '角色', '主角', '配角', '性格', '关系', '对话', '心理', '情感']
        
        sentences = re.split(r'[。！？]', content)
        character_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if any(keyword in sentence for keyword in character_keywords):
                character_sentences.append(sentence)
        
        # 合并并限制长度
        result = '。'.join(character_sentences[:2])  # 最多2句
        return result[:max_length] + "..." if len(result) > max_length else result
    
    @staticmethod
    def _extract_style_info(analyses: List[Dict], max_length: int = 300) -> str:
        """提取风格信息"""
        style_points = []
        
        for analysis in analyses:
            content = analysis.get("content", "")
            dimension = analysis.get("dimension", "")
            
            # 如果是语言风格相关的分析
            if dimension in ["language_style", "rhythm_pacing", "sentence_variation"]:
                # 提取风格特点
                style_keywords = ['风格', '语言', '句式', '节奏', '语调', '表达']
                sentences = re.split(r'[。！？]', content)
                
                for sentence in sentences:
                    sentence = sentence.strip()
                    if any(keyword in sentence for keyword in style_keywords):
                        style_points.append(sentence)
                        break  # 每个分析只取一句
        
        result = '；'.join(style_points[:3])  # 最多3个要点
        return result[:max_length] + "..." if len(result) > max_length else result
    
    @staticmethod
    def _extract_emotional_flow(chapters_content: List[Dict], max_length: int = 300) -> str:
        """提取情感流向"""
        if not chapters_content:
            return ""
        
        emotional_keywords = ['情感', '心情', '感情', '氛围', '情绪', '基调']
        emotional_points = []
        
        for chapter in chapters_content[-2:]:  # 只看最近2章
            content = chapter.get("content", "")
            if not content:
                continue
            
            # 简单提取情感相关内容
            sentences = content.split('。')[:5]  # 只看前5句
            for sentence in sentences:
                if any(keyword in sentence for keyword in emotional_keywords):
                    emotional_points.append(sentence.strip())
                    break
        
        result = '；'.join(emotional_points)
        return result[:max_length] + "..." if len(result) > max_length else result
    
    @staticmethod
    def format_writing_prompt_context(context: Dict[str, str], 
                                    current_chapter_number: int) -> str:
        """
        格式化写作提示词的上下文部分
        
        Args:
            context: 提取的上下文信息
            current_chapter_number: 当前章节编号
            
        Returns:
            格式化的上下文字符串
        """
        if current_chapter_number <= 1:
            return ""
        
        context_parts = []
        
        if context.get("plot_continuity"):
            context_parts.append(f"## 前序剧情发展：\n{context['plot_continuity']}")
        
        if context.get("character_development"):
            context_parts.append(f"## 人物关系变化：\n{context['character_development']}")
        
        if context.get("style_consistency"):
            context_parts.append(f"## 写作风格要求：\n{context['style_consistency']}")
        
        if context.get("emotional_flow"):
            context_parts.append(f"## 情感基调延续：\n{context['emotional_flow']}")
        
        if context_parts:
            return "\n\n".join(context_parts) + "\n\n"
        else:
            return ""
