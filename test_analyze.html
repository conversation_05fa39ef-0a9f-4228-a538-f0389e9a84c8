<!DOCTYPE html>
<html>
<head>
    <title>测试章节分析</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        button {
            padding: 10px;
            margin: 10px 0;
            cursor: pointer;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ccc;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>测试章节分析</h1>
    <div>
        <label for="novel_id">小说ID:</label>
        <input type="number" id="novel_id" value="42">
    </div>
    <div>
        <label for="chapter_id">章节ID:</label>
        <input type="number" id="chapter_id" value="13">
    </div>
    <div>
        <label for="dimension">分析维度:</label>
        <select id="dimension">
            <option value="language_style">语言风格</option>
            <option value="rhythm_pacing">节奏节拍</option>
            <option value="structure">结构分析</option>
            <option value="sentence_variation">句式变化</option>
            <option value="paragraph_length">段落长度</option>
            <option value="perspective_shifts">视角转换</option>
            <option value="paragraph_flow">段落流畅度</option>
            <option value="novel_characteristics">小说特点</option>
            <option value="world_building">世界构建</option>
            <option value="character_relationships">角色关系</option>
            <option value="opening_effectiveness">开头效果</option>
            <option value="climax_pacing">高潮节奏</option>
        </select>
    </div>
    <button id="analyze">分析章节</button>
    <div id="result">结果将显示在这里...</div>

    <script>
        $(document).ready(function() {
            $('#analyze').click(function() {
                const novel_id = $('#novel_id').val();
                const chapter_id = $('#chapter_id').val();
                const dimension = $('#dimension').val();

                $('#result').html('正在分析...');

                $.ajax({
                    url: `http://localhost:5001/api/novel/${novel_id}/chapter/${chapter_id}/analyze`,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        dimension: dimension
                    }),
                    headers: {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    success: function(response) {
                        $('#result').html(JSON.stringify(response, null, 2));

                        // 如果分析成功，尝试获取推理内容
                        if (response.success) {
                            $.ajax({
                                url: `http://localhost:5001/api/novel/${novel_id}/chapter/${chapter_id}/analysis/${dimension}/reasoning_content`,
                                type: 'GET',
                                success: function(reasoning) {
                                    $('#result').append('<h3>推理内容:</h3><pre>' + JSON.stringify(reasoning, null, 2) + '</pre>');
                                },
                                error: function(xhr) {
                                    $('#result').append('<h3>获取推理内容失败:</h3><pre>' + xhr.responseText + '</pre>');
                                }
                            });
                        }
                    },
                    error: function(xhr) {
                        $('#result').html('分析失败: ' + xhr.responseText);
                    }
                });
            });
        });
    </script>
</body>
</html>
