"""
直接使用SQL查询章节ID
"""

import sqlite3
import os

# 数据库路径
db_path = os.path.join(os.path.dirname(__file__), 'novel_analysis.db')

def query_chapters():
    """查询所有章节"""
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 查询所有章节
        cursor.execute("""
            SELECT c.id, c.novel_id, c.chapter_number, c.title, n.title as novel_title
            FROM chapters c
            JOIN novels n ON c.novel_id = n.id
            ORDER BY c.novel_id, c.chapter_number
        """)
        
        chapters = cursor.fetchall()
        print(f"找到 {len(chapters)} 个章节")
        
        # 显示章节信息
        for chapter in chapters:
            chapter_id, novel_id, chapter_number, chapter_title, novel_title = chapter
            chapter_title = chapter_title or f'第{chapter_number}章'
            print(f"章节ID: {chapter_id}, 小说: {novel_title}, 章节: {chapter_title}")
    except Exception as e:
        print(f"查询章节时出错: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    query_chapters()
