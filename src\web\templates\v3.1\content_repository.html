{% extends "v3.1/base.html" %}

{% block title %}内容仓库 - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .content-card {
        transition: all 0.3s ease;
    }
    .content-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .content-preview {
        max-height: 150px;
        overflow: hidden;
        position: relative;
    }
    .content-preview::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,1));
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h1 class="mb-3"><i class="fas fa-archive text-danger me-2"></i>内容仓库</h1>
                <p class="lead">内容仓库存放所有通过自动写作生成的内容，方便查看、编辑和管理。</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong> 您可以在控制台中使用参考蓝本进行自动写作，生成的内容将自动保存到内容仓库。
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body filter-section">
                <div class="row">
                    <div class="col-md-4 mb-3 mb-md-0">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="搜索内容..." id="searchInput">
                            <button class="btn btn-primary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3 mb-md-0">
                        <select class="form-select" id="sortSelect">
                            <option value="newest">最新创建</option>
                            <option value="oldest">最早创建</option>
                            <option value="longest">字数最多</option>
                            <option value="shortest">字数最少</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3 mb-md-0">
                        <select class="form-select" id="templateSelect">
                            <option value="all">所有参考蓝本</option>
                            <option value="1">参考蓝本1</option>
                            <option value="2">参考蓝本2</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" id="resetFilterBtn">
                            <i class="fas fa-redo me-1"></i>重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 内容列表 -->
<div class="row" id="contentList">
    {% if contents %}
        {% for content in contents %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 content-card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ content.title }}</h5>
                        <span class="badge bg-primary">{{ content.created_at.split('T')[0] if content.created_at is string else content.created_at.strftime('%Y-%m-%d') }}</span>
                    </div>
                    <div class="card-body">
                        <div class="content-preview mb-3">
                            <p>{{ content.content }}</p>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">{{ content.word_count }} 字</span>
                            <div>
                                <a href="#" class="btn btn-sm btn-outline-primary me-1" data-content-id="{{ content.id }}">
                                    <i class="fas fa-eye me-1"></i>查看
                                </a>
                                <a href="#" class="btn btn-sm btn-outline-danger" data-content-id="{{ content.id }}">
                                    <i class="fas fa-trash me-1"></i>删除
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-archive fa-4x text-muted mb-3"></i>
                    <h3>内容仓库为空</h3>
                    <p class="text-muted">您还没有生成任何内容。请前往控制台使用参考蓝本进行自动写作。</p>
                    <div class="mt-4">
                        <a href="{{ url_for('v3_1.console') }}" class="btn btn-primary">
                            <i class="fas fa-terminal me-1"></i>前往控制台
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- 分页 -->
{% if contents and contents|length > 12 %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="内容分页">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
{% endif %}

<!-- 内容查看模态框 -->
<div class="modal fade" id="contentViewModal" tabindex="-1" aria-labelledby="contentViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentViewModalLabel">内容详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h4 id="modalContentTitle"></h4>
                <div class="d-flex justify-content-between mb-3">
                    <span class="text-muted" id="modalContentInfo"></span>
                    <span class="badge bg-primary" id="modalContentDate"></span>
                </div>
                <div id="modalContentBody" class="border p-3 rounded" style="max-height: 400px; overflow-y: auto;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="downloadContentBtn">
                    <i class="fas fa-download me-1"></i>下载
                </button>
                <button type="button" class="btn btn-success" id="editContentBtn">
                    <i class="fas fa-edit me-1"></i>编辑
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个内容吗？此操作无法撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 查看内容
        $(document).on('click', '.btn-outline-primary[data-content-id]', function(e) {
            e.preventDefault();
            const contentId = $(this).data('content-id');

            // 模拟数据，实际应该从API获取
            const content = {
                id: contentId,
                title: '自动生成的内容',
                content: '这是自动生成的内容示例。这里应该显示完整的内容文本。在实际应用中，这部分内容会从API获取。',
                word_count: 1000,
                created_at: '2025-05-10'
            };

            $('#modalContentTitle').text(content.title);
            $('#modalContentInfo').text(`${content.word_count} 字`);
            $('#modalContentDate').text(content.created_at);
            $('#modalContentBody').text(content.content);

            const contentViewModal = new bootstrap.Modal(document.getElementById('contentViewModal'));
            contentViewModal.show();
        });

        // 删除内容
        $(document).on('click', '.btn-outline-danger[data-content-id]', function(e) {
            e.preventDefault();
            const contentId = $(this).data('content-id');

            // 保存要删除的内容ID
            $('#confirmDeleteBtn').data('content-id', contentId);

            const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
            deleteConfirmModal.show();
        });

        // 确认删除
        $('#confirmDeleteBtn').click(function() {
            const contentId = $(this).data('content-id');

            // 调用删除API
            // $.ajax({
            //     url: `/api/generated_content/${contentId}`,
            //     type: 'DELETE',
            //     success: function(response) {
            //         if (response.success) {
            //             // 从页面移除内容卡片
            //             $(`.content-card[data-content-id="${contentId}"]`).parent().remove();
            //
            //             // 关闭模态框
            //             const deleteConfirmModal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            //             deleteConfirmModal.hide();
            //         } else {
            //             alert('删除失败: ' + response.error);
            //         }
            //     },
            //     error: function() {
            //         alert('删除请求失败，请重试');
            //     }
            // });

            // 模拟删除成功
            alert('删除成功（模拟）');
            const deleteConfirmModal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            deleteConfirmModal.hide();
        });

        // 下载内容
        $('#downloadContentBtn').click(function() {
            const title = $('#modalContentTitle').text();
            const content = $('#modalContentBody').text();

            // 创建Blob对象
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });

            // 创建下载链接
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${title}.txt`;

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });

        // 搜索功能
        $('#searchBtn').click(function() {
            const searchTerm = $('#searchInput').val().trim();
            if (searchTerm) {
                alert(`搜索: ${searchTerm}（功能模拟）`);
            }
        });

        // 排序功能
        $('#sortSelect').change(function() {
            const sortValue = $(this).val();
            alert(`排序方式: ${sortValue}（功能模拟）`);
        });

        // 参考蓝本筛选
        $('#templateSelect').change(function() {
            const templateId = $(this).val();
            alert(`筛选参考蓝本: ${templateId}（功能模拟）`);
        });

        // 重置筛选
        $('#resetFilterBtn').click(function() {
            $('#searchInput').val('');
            $('#sortSelect').val('newest');
            $('#templateSelect').val('all');
            alert('已重置筛选条件（功能模拟）');
        });
    });
</script>
{% endblock %}