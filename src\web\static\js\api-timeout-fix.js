/**
 * 九猫小说分析写作系统 - API超时处理改进脚本
 * 
 * 此脚本用于改进API超时处理，提高系统稳定性
 * 版本: 1.0.0
 */

(function() {
    console.log('[API超时处理] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        timeoutRetryCount: 3,        // 超时重试次数
        initialTimeout: 120000,      // 初始超时时间（毫秒）
        timeoutIncrement: 60000,     // 每次重试增加的超时时间（毫秒）
        maxTimeout: 300000,          // 最大超时时间（毫秒）
        retryDelay: 5000,            // 重试延迟时间（毫秒）
        apiPaths: [                  // 需要处理超时的API路径
            '/api/novel/',
            '/api/novels/',
            '/api/analysis/',
            '/api/template/'
        ]
    };

    // 状态
    const STATE = {
        initialized: false,
        originalFetch: window.fetch,
        originalXhrOpen: XMLHttpRequest.prototype.open,
        originalXhrSend: XMLHttpRequest.prototype.send,
        retryCount: {},              // 记录每个请求的重试次数
        timeoutTimers: {}            // 记录每个请求的超时计时器
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[API超时处理] ${message}`);
        }
    }

    // 初始化
    function init() {
        debugLog('初始化API超时处理改进脚本');

        // 重写fetch函数
        overrideFetch();

        // 重写XMLHttpRequest
        overrideXhr();

        // 添加全局AJAX设置
        configureAjax();

        STATE.initialized = true;
        debugLog('API超时处理改进脚本初始化完成');
    }

    // 重写fetch函数
    function overrideFetch() {
        window.fetch = async function(resource, options = {}) {
            // 检查是否是API请求
            if (typeof resource === 'string' && isApiRequest(resource)) {
                debugLog(`处理API fetch请求: ${resource}`);
                
                // 生成请求ID
                const requestId = generateRequestId(resource);
                
                // 初始化重试计数
                if (!STATE.retryCount[requestId]) {
                    STATE.retryCount[requestId] = 0;
                }
                
                // 设置超时选项
                const timeout = calculateTimeout(STATE.retryCount[requestId]);
                
                try {
                    // 创建AbortController
                    const controller = new AbortController();
                    const signal = controller.signal;
                    
                    // 合并选项
                    const fetchOptions = {
                        ...options,
                        signal
                    };
                    
                    // 设置超时计时器
                    const timeoutId = setTimeout(() => {
                        debugLog(`请求超时: ${resource} (${timeout}ms)`, 'warn');
                        controller.abort();
                    }, timeout);
                    
                    // 保存超时计时器
                    STATE.timeoutTimers[requestId] = timeoutId;
                    
                    // 发送请求
                    const response = await STATE.originalFetch.call(this, resource, fetchOptions);
                    
                    // 清除超时计时器
                    clearTimeout(timeoutId);
                    delete STATE.timeoutTimers[requestId];
                    
                    // 重置重试计数
                    delete STATE.retryCount[requestId];
                    
                    return response;
                } catch (error) {
                    // 清除超时计时器
                    if (STATE.timeoutTimers[requestId]) {
                        clearTimeout(STATE.timeoutTimers[requestId]);
                        delete STATE.timeoutTimers[requestId];
                    }
                    
                    // 检查是否是超时错误
                    if (error.name === 'AbortError') {
                        // 增加重试计数
                        STATE.retryCount[requestId]++;
                        
                        // 检查是否超过最大重试次数
                        if (STATE.retryCount[requestId] <= CONFIG.timeoutRetryCount) {
                            debugLog(`请求超时，准备重试 (${STATE.retryCount[requestId]}/${CONFIG.timeoutRetryCount}): ${resource}`, 'warn');
                            
                            // 延迟后重试
                            await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay));
                            
                            // 重试请求
                            return window.fetch(resource, options);
                        } else {
                            debugLog(`请求超时，已达到最大重试次数: ${resource}`, 'error');
                            throw new Error(`请求超时，已重试${CONFIG.timeoutRetryCount}次`);
                        }
                    } else {
                        // 其他错误，直接抛出
                        throw error;
                    }
                }
            } else {
                // 非API请求，使用原始fetch
                return STATE.originalFetch.call(this, resource, options);
            }
        };
        
        debugLog('fetch函数已重写');
    }

    // 重写XMLHttpRequest
    function overrideXhr() {
        // 重写open方法
        XMLHttpRequest.prototype.open = function(method, url, async = true, user, password) {
            // 保存URL
            this._url = url;
            
            // 检查是否是API请求
            if (typeof url === 'string' && isApiRequest(url)) {
                debugLog(`处理API XHR请求: ${url}`);
                
                // 生成请求ID
                const requestId = generateRequestId(url);
                
                // 初始化重试计数
                if (!STATE.retryCount[requestId]) {
                    STATE.retryCount[requestId] = 0;
                }
                
                // 保存请求ID
                this._requestId = requestId;
                
                // 保存方法和参数
                this._method = method;
                this._async = async;
                this._user = user;
                this._password = password;
            }
            
            // 调用原始open方法
            return STATE.originalXhrOpen.call(this, method, url, async, user, password);
        };
        
        // 重写send方法
        XMLHttpRequest.prototype.send = function(body) {
            // 检查是否是API请求
            if (this._url && isApiRequest(this._url)) {
                const requestId = this._requestId;
                
                // 设置超时
                const timeout = calculateTimeout(STATE.retryCount[requestId]);
                this.timeout = timeout;
                
                // 保存请求体
                this._body = body;
                
                // 添加超时处理
                this.ontimeout = () => {
                    debugLog(`XHR请求超时: ${this._url} (${timeout}ms)`, 'warn');
                    
                    // 增加重试计数
                    STATE.retryCount[requestId]++;
                    
                    // 检查是否超过最大重试次数
                    if (STATE.retryCount[requestId] <= CONFIG.timeoutRetryCount) {
                        debugLog(`XHR请求超时，准备重试 (${STATE.retryCount[requestId]}/${CONFIG.timeoutRetryCount}): ${this._url}`, 'warn');
                        
                        // 延迟后重试
                        setTimeout(() => {
                            // 创建新的XHR对象
                            const xhr = new XMLHttpRequest();
                            
                            // 复制所有事件处理程序
                            for (const key in this) {
                                if (key.startsWith('on') && typeof this[key] === 'function') {
                                    xhr[key] = this[key];
                                }
                            }
                            
                            // 打开连接
                            xhr.open(this._method, this._url, this._async, this._user, this._password);
                            
                            // 复制所有请求头
                            for (const header of this.getAllResponseHeaders().split('\r\n')) {
                                if (header) {
                                    const parts = header.split(': ');
                                    xhr.setRequestHeader(parts[0], parts[1]);
                                }
                            }
                            
                            // 发送请求
                            xhr.send(this._body);
                        }, CONFIG.retryDelay);
                    } else {
                        debugLog(`XHR请求超时，已达到最大重试次数: ${this._url}`, 'error');
                        
                        // 重置重试计数
                        delete STATE.retryCount[requestId];
                        
                        // 触发错误事件
                        if (typeof this.onerror === 'function') {
                            this.onerror(new Error(`请求超时，已重试${CONFIG.timeoutRetryCount}次`));
                        }
                    }
                };
            }
            
            // 调用原始send方法
            return STATE.originalXhrSend.call(this, body);
        };
        
        debugLog('XMLHttpRequest已重写');
    }

    // 配置jQuery AJAX（如果存在）
    function configureAjax() {
        if (typeof $ !== 'undefined' && $.ajax) {
            // 设置全局AJAX默认值
            $.ajaxSetup({
                timeout: CONFIG.initialTimeout,
                retryCount: 0,
                retryLimit: CONFIG.timeoutRetryCount,
                retryDelay: CONFIG.retryDelay,
                retryableStatus: [408, 429, 500, 502, 503, 504],
                beforeSend: function(xhr, settings) {
                    // 检查是否是API请求
                    if (isApiRequest(settings.url)) {
                        debugLog(`处理API AJAX请求: ${settings.url}`);
                        
                        // 设置超时
                        settings.timeout = calculateTimeout(settings.retryCount);
                    }
                    return true;
                }
            });
            
            // 添加全局AJAX错误处理
            $(document).ajaxError(function(event, jqXHR, settings, thrownError) {
                // 检查是否是API请求
                if (isApiRequest(settings.url)) {
                    // 检查是否是超时错误或可重试状态码
                    const isTimeout = thrownError === 'timeout';
                    const isRetryableStatus = settings.retryableStatus.includes(jqXHR.status);
                    
                    if (isTimeout || isRetryableStatus) {
                        // 增加重试计数
                        settings.retryCount++;
                        
                        // 检查是否超过最大重试次数
                        if (settings.retryCount <= settings.retryLimit) {
                            debugLog(`AJAX请求${isTimeout ? '超时' : '失败'}，准备重试 (${settings.retryCount}/${settings.retryLimit}): ${settings.url}`, 'warn');
                            
                            // 延迟后重试
                            setTimeout(function() {
                                $.ajax(settings);
                            }, settings.retryDelay);
                            
                            // 阻止错误传播
                            event.stopPropagation();
                        } else {
                            debugLog(`AJAX请求${isTimeout ? '超时' : '失败'}，已达到最大重试次数: ${settings.url}`, 'error');
                        }
                    }
                }
            });
            
            debugLog('jQuery AJAX已配置');
        }
    }

    // 检查是否是API请求
    function isApiRequest(url) {
        if (typeof url !== 'string') {
            return false;
        }
        
        return CONFIG.apiPaths.some(path => url.includes(path));
    }

    // 生成请求ID
    function generateRequestId(url) {
        return url.replace(/[^a-zA-Z0-9]/g, '_');
    }

    // 计算超时时间
    function calculateTimeout(retryCount) {
        const timeout = CONFIG.initialTimeout + (retryCount * CONFIG.timeoutIncrement);
        return Math.min(timeout, CONFIG.maxTimeout);
    }

    // 初始化脚本
    $(document).ready(function() {
        init();
    });
})();
