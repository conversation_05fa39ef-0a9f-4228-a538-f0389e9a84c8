@echo off
echo 九猫小说分析系统 - C盘空间监控工具
echo.

:: 设置阈值（单位：MB）
set CRITICAL_THRESHOLD=5000
set WARNING_THRESHOLD=10000

:: 设置颜色
color 07

:: 获取C盘总空间和可用空间
for /f "tokens=2 delims==" %%a in ('wmic logicaldisk where "DeviceID='C:'" get FreeSpace /value') do set FREE_BYTES=%%a
for /f "tokens=2 delims==" %%a in ('wmic logicaldisk where "DeviceID='C:'" get Size /value') do set TOTAL_BYTES=%%a

:: 计算MB
set /a FREE_MB=%FREE_BYTES:~0,-6%
set /a TOTAL_MB=%TOTAL_BYTES:~0,-6%
set /a USED_MB=%TOTAL_MB%-%FREE_MB%
set /a PERCENT_FREE=(%FREE_MB%*100)/%TOTAL_MB%
set /a PERCENT_USED=100-%PERCENT_FREE%

echo C盘空间状态:
echo ------------------------------------
echo 总空间:   %TOTAL_MB% MB (%TOTAL_MB:~0,-3%.%TOTAL_MB:~-3% GB)
echo 已使用:   %USED_MB% MB (%USED_MB:~0,-3%.%USED_MB:~-3% GB) - %PERCENT_USED%%%
echo 可用空间: %FREE_MB% MB (%FREE_MB:~0,-3%.%FREE_MB:~-3% GB) - %PERCENT_FREE%%%
echo ------------------------------------
echo.

:: 判断空间状态并给出建议
if %FREE_MB% LSS %CRITICAL_THRESHOLD% (
    color 4F
    echo [警告] C盘空间严重不足！
    echo 建议立即清理C盘空间或迁移临时文件！
    echo.
    echo 紧急措施:
    echo 1. 运行 修改系统环境变量.bat (以管理员身份)
    echo 2. 删除不必要的安装程序和临时文件
    echo 3. 将下载和文档移动到其他磁盘
    echo.
) else if %FREE_MB% LSS %WARNING_THRESHOLD% (
    color 6F
    echo [注意] C盘空间偏低
    echo 建议适当清理空间并考虑永久修改临时文件位置
    echo.
    echo 推荐措施:
    echo 1. 运行 修改系统环境变量.bat (以管理员身份)
    echo 2. 使用 启动九猫_优化版.bat 启动九猫系统
    echo.
) else (
    color 2F
    echo [正常] C盘空间充足
    echo.
    echo 建议:
    echo - 继续使用 启动九猫_优化版.bat 以避免过多占用C盘空间
    echo - 定期运行此脚本监控C盘空间
    echo.
)

echo 九猫系统临时文件当前位置:
echo.

:: 检查环境变量
echo TEMP=%TEMP%
echo TMP=%TMP%
echo.

:: 检查是否符号链接
if "%TEMP:~0,2%"=="C:" (
    echo [提示] 当前临时文件位置在C盘
    echo 建议修改临时文件位置到其他磁盘
) else (
    echo [良好] 当前临时文件位置不在C盘
)

echo.
echo 是否清理临时文件? (Y/N)
set /p CLEAN_TEMP="选择: "

if /i "%CLEAN_TEMP%"=="Y" (
    echo.
    echo 清理临时文件...
    echo.
    
    echo 1. 清理系统临时文件夹...
    del /q /f /s "%TEMP%\*.*" 2>nul
    
    echo 2. 清理Windows临时文件夹...
    del /q /f /s "C:\Windows\Temp\*.*" 2>nul
    
    echo 3. 清理用户临时文件夹...
    del /q /f /s "C:\Users\<USER>\AppData\Local\Temp\*.*" 2>nul
    
    echo 临时文件清理完成!
    echo.
)

echo.
echo 请按任意键退出...
pause > nul
color 07 