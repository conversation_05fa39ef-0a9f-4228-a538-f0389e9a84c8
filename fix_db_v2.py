"""
数据库修复脚本 - 用于九猫v2.0系统
"""
import os
import sys
import sqlite3
import glob

def main():
    """主函数"""
    print("九猫v2.0数据库修复工具")
    print("=" * 50)
    
    # 查找数据库文件
    print("查找数据库文件...")
    db_files = []
    for pattern in ['instance/*.db', '*.db', 'data/*.db']:
        db_files.extend(glob.glob(pattern))
    
    if not db_files:
        print("未找到数据库文件")
        return 1
    
    print(f"找到 {len(db_files)} 个数据库文件:")
    for i, db_file in enumerate(db_files):
        print(f"{i+1}. {db_file}")
    
    # 修复每个数据库
    success = False
    for db_path in db_files:
        try:
            print(f"\n检查数据库: {db_path}")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [t[0] for t in cursor.fetchall()]
            
            if not tables:
                print(f"  数据库 {db_path} 中没有表")
                continue
            
            print(f"  数据库 {db_path} 中的表: {', '.join(tables)}")
            
            # 检查并修复表
            for table in ['chapter_analysis_results', 'analysis_results']:
                if table in tables:
                    print(f"  检查表: {table}")
                    
                    # 获取表结构
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [c[1] for c in cursor.fetchall()]
                    
                    if 'reasoning_content' not in columns:
                        print(f"  添加列: reasoning_content 到 {table}")
                        try:
                            cursor.execute(f"ALTER TABLE {table} ADD COLUMN reasoning_content TEXT")
                            conn.commit()
                            print(f"  成功添加列: reasoning_content 到 {table}")
                            success = True
                        except Exception as e:
                            print(f"  添加列时出错: {str(e)}")
                    else:
                        print(f"  表 {table} 已有 reasoning_content 列")
            
            # 关闭连接
            conn.close()
        except Exception as e:
            print(f"处理数据库 {db_path} 时出错: {str(e)}")
    
    if success:
        print("\n数据库修复完成")
        return 0
    else:
        print("\n数据库修复未执行任何操作")
        return 0

if __name__ == "__main__":
    sys.exit(main())
