"""
数据库优化器
用于优化数据库连接池和释放内存
"""
import logging
import sqlite3
import threading
import time
from contextlib import contextmanager

logger = logging.getLogger(__name__)

# 连接池管理
class ConnectionPoolManager:
    # 单例模式
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ConnectionPoolManager, cls).__new__(cls)
                cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        # 初始化
        self.pool_size = 20
        self.max_connections = 50
        self.connection_timeout = 60
        self.connection_recycle = 3600
        self._initialized = True
        
        # 记录日志
        logger.info(f"已限制数据库最大连接数为 {self.max_connections}")
    
    def optimize_pool(self, engine):
        """优化连接池设置"""
        try:
            # 应用连接池限制
            engine.pool._pool.maxsize = self.pool_size
            engine.pool._max_overflow = self.max_connections - self.pool_size
            engine.pool._timeout = self.connection_timeout
            engine.pool._recycle = self.connection_recycle
            
            logger.info(f"已设置连接池大小: {self.pool_size}, 最大溢出: {self.max_connections - self.pool_size}")
            logger.info(f"连接超时: {self.connection_timeout}秒, 回收时间: {self.connection_recycle}秒")
            
            return True
        except Exception as e:
            logger.error(f"优化数据库连接池时出错: {str(e)}")
            return False

# SQLite优化器
class SQLiteOptimizer:
    @staticmethod
    def optimize_connection(conn):
        """优化SQLite连接的性能"""
        try:
            # 启用WAL模式，提高写入性能
            conn.execute("PRAGMA journal_mode = WAL")
            
            # 设置较大的缓存，减少磁盘I/O
            conn.execute("PRAGMA cache_size = -8000")  # -8000表示8MB
            
            # 设置同步模式，提高性能（但稍微降低安全性）
            conn.execute("PRAGMA synchronous = NORMAL")
            
            # 其他性能优化
            conn.execute("PRAGMA temp_store = MEMORY")
            conn.execute("PRAGMA mmap_size = 16777216")  # 16MB内存映射
            
            return True
        except Exception as e:
            logger.error(f"优化SQLite连接时出错: {str(e)}")
            return False
    
    @staticmethod
    def vacuum_database(db_path):
        """整理数据库，释放空间"""
        try:
            # 连接到数据库
            conn = sqlite3.connect(db_path)
            
            # 执行VACUUM操作
            conn.execute("VACUUM")
            
            # 提交事务并关闭连接
            conn.commit()
            conn.close()
            
            logger.info(f"数据库已整理: {db_path}")
            return True
        except Exception as e:
            logger.error(f"整理数据库时出错: {str(e)}")
            return False
    
    @staticmethod
    def purge_old_logs(db_path, days=30):
        """清理旧日志记录"""
        try:
            # 连接到数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 计算截止日期
            cutoff_date = time.time() - (days * 86400)  # days天前的时间戳
            
            # 删除旧日志
            cursor.execute("DELETE FROM api_logs WHERE timestamp < ?", (cutoff_date,))
            cursor.execute("DELETE FROM analysis_logs WHERE timestamp < ?", (cutoff_date,))
            cursor.execute("DELETE FROM system_alerts WHERE timestamp < ?", (cutoff_date,))
            
            # 记录删除的行数
            deleted_rows = cursor.rowcount
            
            # 提交事务并关闭连接
            conn.commit()
            conn.close()
            
            if deleted_rows > 0:
                logger.info(f"已清理 {deleted_rows} 条旧日志记录")
            
            return deleted_rows
        except Exception as e:
            logger.error(f"清理旧日志时出错: {str(e)}")
            return -1

# 内存优化器
class MemoryOptimizer:
    @staticmethod
    def cleanup_session_cache():
        """清理会话缓存，释放内存"""
        try:
            # 这里为空实现，在实际调用时由单独的模块处理
            return True
        except Exception as e:
            logger.error(f"清理会话缓存时出错: {str(e)}")
            return False
    
    @staticmethod
    def suggest_gc():
        """建议垃圾回收"""
        try:
            import gc
            collected = gc.collect()
            logger.debug(f"强制垃圾回收，回收了 {collected} 个对象")
            return collected
        except Exception as e:
            logger.error(f"执行垃圾回收时出错: {str(e)}")
            return -1

# 创建连接池管理器实例
pool_manager = ConnectionPoolManager()

# 上下文管理器，用于数据库优化
@contextmanager
def optimized_connection(engine, db_path=None):
    """
    创建一个优化的数据库连接上下文
    
    Args:
        engine: SQLAlchemy引擎
        db_path: 数据库文件路径（仅适用于SQLite）
    """
    try:
        # 获取连接
        with engine.connect() as conn:
            # 如果是SQLite，应用SQLite优化
            if engine.dialect.name == 'sqlite':
                SQLiteOptimizer.optimize_connection(conn)
            
            # 提供连接给调用者
            yield conn
            
    except Exception as e:
        logger.error(f"使用优化连接时出错: {str(e)}")
        raise

# 定期维护
def schedule_maintenance(db_path, engine=None):
    """
    安排定期维护任务
    
    Args:
        db_path: 数据库文件路径
        engine: SQLAlchemy引擎（可选）
    """
    if engine and engine.dialect.name == 'sqlite':
        # 优化引擎
        pool_manager.optimize_pool(engine)
        
        # 创建维护线程
        maintenance_thread = threading.Thread(
            target=_maintenance_worker,
            args=(db_path,),
            daemon=True
        )
        maintenance_thread.start()
        logger.info("数据库维护线程已启动")

# 维护工作线程
def _maintenance_worker(db_path):
    """
    执行数据库维护任务的工作线程
    
    Args:
        db_path: 数据库文件路径
    """
    try:
        while True:
            # 每24小时执行一次维护
            time.sleep(86400)  # 24小时
            
            # 清理旧日志
            SQLiteOptimizer.purge_old_logs(db_path)
            
            # 整理数据库
            SQLiteOptimizer.vacuum_database(db_path)
            
            # 建议垃圾回收
            MemoryOptimizer.suggest_gc()
            
            logger.info("数据库维护任务已执行")
    except Exception as e:
        logger.error(f"数据库维护线程出错: {str(e)}")
