/**
 * 九猫系统按钮文字最高优先级修复脚本
 * 解决按钮文字不可见的问题（最高优先级版本）
 */

// 立即执行函数
(function() {
    console.log('按钮文字最高优先级修复脚本已加载');

    // 立即执行修复
    injectSupremeStyles();
    fixAllButtons();
    
    // 在DOM加载完成后再次执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM加载完成，执行按钮文字最高优先级修复');
        fixAllButtons();
        observeButtonChanges();
    });

    // 注入最高优先级样式
    function injectSupremeStyles() {
        // 创建样式元素
        const style = document.createElement('style');
        style.id = 'button-text-supreme-fix';
        style.innerHTML = `
            /* 全局按钮修复 - 使用更高优先级选择器 */
            .btn, button, input[type="button"], input[type="submit"], input[type="reset"], a.btn, 
            .btn[class*="btn-"], button[class*="btn-"], a[class*="btn-"] {
                color: #333333 !important;
                text-shadow: none !important;
                -webkit-text-fill-color: initial !important;
                opacity: 1 !important;
                visibility: visible !important;
                text-indent: 0 !important;
                font-size: 14px !important;
                font-weight: 400 !important;
                letter-spacing: normal !important;
                background-image: none !important;
                background-clip: padding-box !important;
                -webkit-background-clip: padding-box !important;
            }

            /* 特定按钮类型修复 - 使用更高优先级选择器 */
            .btn-primary, button.btn-primary, a.btn-primary, 
            .btn.btn-primary, button.btn.btn-primary, a.btn.btn-primary {
                color: #ffffff !important;
                background-color: #007bff !important;
                border-color: #007bff !important;
            }

            .btn-secondary, button.btn-secondary, a.btn-secondary,
            .btn.btn-secondary, button.btn.btn-secondary, a.btn.btn-secondary {
                color: #ffffff !important;
                background-color: #6c757d !important;
                border-color: #6c757d !important;
            }

            .btn-success, button.btn-success, a.btn-success,
            .btn.btn-success, button.btn.btn-success, a.btn.btn-success {
                color: #ffffff !important;
                background-color: #28a745 !important;
                border-color: #28a745 !important;
            }

            .btn-danger, button.btn-danger, a.btn-danger,
            .btn.btn-danger, button.btn.btn-danger, a.btn.btn-danger {
                color: #ffffff !important;
                background-color: #dc3545 !important;
                border-color: #dc3545 !important;
            }

            .btn-warning, button.btn-warning, a.btn-warning,
            .btn.btn-warning, button.btn.btn-warning, a.btn.btn-warning {
                color: #212529 !important;
                background-color: #ffc107 !important;
                border-color: #ffc107 !important;
            }

            .btn-info, button.btn-info, a.btn-info,
            .btn.btn-info, button.btn.btn-info, a.btn.btn-info {
                color: #ffffff !important;
                background-color: #17a2b8 !important;
                border-color: #17a2b8 !important;
            }

            .btn-light, button.btn-light, a.btn-light,
            .btn.btn-light, button.btn.btn-light, a.btn.btn-light {
                color: #212529 !important;
                background-color: #f8f9fa !important;
                border-color: #f8f9fa !important;
            }

            .btn-dark, button.btn-dark, a.btn-dark,
            .btn.btn-dark, button.btn.btn-dark, a.btn.btn-dark {
                color: #ffffff !important;
                background-color: #343a40 !important;
                border-color: #343a40 !important;
            }

            /* 链接按钮修复 */
            .btn-link, button.btn-link, a.btn-link,
            .btn.btn-link, button.btn.btn-link, a.btn.btn-link {
                color: #007bff !important;
                background-color: transparent !important;
                border-color: transparent !important;
                text-decoration: none !important;
            }

            .btn-link:hover, button.btn-link:hover, a.btn-link:hover,
            .btn.btn-link:hover, button.btn.btn-link:hover, a.btn.btn-link:hover {
                color: #0056b3 !important;
                text-decoration: underline !important;
            }

            /* 确保按钮内的所有元素可见 */
            .btn *, button *, a.btn *,
            .btn[class*="btn-"] *, button[class*="btn-"] *, a[class*="btn-"] * {
                color: inherit !important;
                opacity: 1 !important;
                visibility: visible !important;
            }
        `;
        
        // 将样式添加到文档头部的最前面
        if (document.head.firstChild) {
            document.head.insertBefore(style, document.head.firstChild);
        } else {
            document.head.appendChild(style);
        }
        console.log('最高优先级样式已注入');
    }

    // 修复所有按钮
    function fixAllButtons() {
        // 查找所有按钮元素
        const buttons = document.querySelectorAll('.btn, button, input[type="button"], input[type="submit"], input[type="reset"], a.btn');
        console.log('找到 ' + buttons.length + ' 个按钮元素');
        
        // 遍历并修复每个按钮
        buttons.forEach(function(button, index) {
            fixButtonStyles(button, index);
        });
    }

    // 修复单个按钮的样式
    function fixButtonStyles(button, index) {
        // 获取按钮文本
        const buttonText = button.textContent.trim();
        
        // 记录按钮信息
        console.log('最高优先级修复按钮 #' + index + (buttonText ? ' [' + buttonText + ']' : ''));
        
        // 直接设置内联样式
        button.style.setProperty('color', getButtonColor(button), 'important');
        button.style.setProperty('opacity', '1', 'important');
        button.style.setProperty('visibility', 'visible', 'important');
        button.style.setProperty('text-indent', '0', 'important');
        button.style.setProperty('-webkit-text-fill-color', 'initial', 'important');
        button.style.setProperty('text-shadow', 'none', 'important');
        button.style.setProperty('background-clip', 'padding-box', 'important');
        button.style.setProperty('-webkit-background-clip', 'padding-box', 'important');
        
        // 修复按钮内的所有元素
        const children = button.querySelectorAll('*');
        children.forEach(function(child) {
            child.style.setProperty('color', 'inherit', 'important');
            child.style.setProperty('opacity', '1', 'important');
            child.style.setProperty('visibility', 'visible', 'important');
        });
    }

    // 观察DOM变化，修复新添加的按钮
    function observeButtonChanges() {
        // 创建MutationObserver实例
        const observer = new MutationObserver(function(mutations) {
            let newButtonsFound = false;
            
            // 检查是否有新的按钮被添加
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        // 检查添加的节点是否是按钮
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.classList && (node.classList.contains('btn') || node.tagName === 'BUTTON')) {
                                fixButtonStyles(node, 'new');
                                newButtonsFound = true;
                            }
                            
                            // 检查添加的节点内是否包含按钮
                            const childButtons = node.querySelectorAll('.btn, button, input[type="button"], input[type="submit"], input[type="reset"], a.btn');
                            if (childButtons.length > 0) {
                                childButtons.forEach(function(button, index) {
                                    fixButtonStyles(button, 'new-child-' + index);
                                });
                                newButtonsFound = true;
                            }
                        }
                    });
                }
            });
            
            // 如果找到新按钮，记录日志
            if (newButtonsFound) {
                console.log('检测到新的按钮元素，已应用最高优先级修复');
            }
        });
        
        // 配置观察选项
        const config = { 
            childList: true, 
            subtree: true 
        };
        
        // 开始观察整个文档
        observer.observe(document.body, config);
        console.log('已开始观察DOM变化');
    }

    // 根据按钮类型获取适当的文字颜色
    function getButtonColor(button) {
        if (button.classList.contains('btn-primary') || 
            button.classList.contains('btn-secondary') || 
            button.classList.contains('btn-success') || 
            button.classList.contains('btn-danger') || 
            button.classList.contains('btn-info') || 
            button.classList.contains('btn-dark')) {
            return '#ffffff';
        } else if (button.classList.contains('btn-warning') || 
                  button.classList.contains('btn-light')) {
            return '#212529';
        } else if (button.classList.contains('btn-outline-primary')) {
            return '#007bff';
        } else if (button.classList.contains('btn-outline-secondary')) {
            return '#6c757d';
        } else if (button.classList.contains('btn-outline-success')) {
            return '#28a745';
        } else if (button.classList.contains('btn-outline-danger')) {
            return '#dc3545';
        } else if (button.classList.contains('btn-outline-warning')) {
            return '#ffc107';
        } else if (button.classList.contains('btn-outline-info')) {
            return '#17a2b8';
        } else if (button.classList.contains('btn-outline-light')) {
            return '#f8f9fa';
        } else if (button.classList.contains('btn-outline-dark')) {
            return '#343a40';
        } else if (button.classList.contains('btn-link')) {
            return '#007bff';
        } else {
            return '#333333';
        }
    }
})();
