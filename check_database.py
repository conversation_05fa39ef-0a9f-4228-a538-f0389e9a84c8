"""
九猫 - 数据库检查脚本
检查数据库中的character_relationships分析结果
"""

import os
import sys
import sqlite3
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_database():
    """检查数据库中的character_relationships分析结果"""
    logger.info("开始检查数据库")
    
    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'novels.db')
    
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return False
    
    logger.info(f"找到数据库文件: {db_path}")
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看数据库表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        logger.info(f"数据库表: {tables}")
        
        # 查看analysis_results表结构
        cursor.execute("PRAGMA table_info(analysis_results);")
        columns = cursor.fetchall()
        logger.info(f"analysis_results表结构: {columns}")
        
        # 查找novel/4页面的character_relationships分析结果
        cursor.execute("""
            SELECT id, content, analysis_metadata FROM analysis_results 
            WHERE novel_id = 4 AND dimension = 'character_relationships'
        """)
        
        result = cursor.fetchone()
        
        if not result:
            logger.warning("未找到novel/4页面的character_relationships分析结果")
            
            # 查找所有novel_id=4的分析结果
            cursor.execute("""
                SELECT id, dimension, content, analysis_metadata FROM analysis_results 
                WHERE novel_id = 4
            """)
            
            all_results = cursor.fetchall()
            logger.info(f"novel_id=4的所有分析结果: {all_results}")
            
            # 查找所有character_relationships分析结果
            cursor.execute("""
                SELECT id, novel_id, content, analysis_metadata FROM analysis_results 
                WHERE dimension = 'character_relationships'
            """)
            
            all_character_results = cursor.fetchall()
            logger.info(f"所有character_relationships分析结果: {all_character_results}")
            
            conn.close()
            return False
        
        result_id, content, metadata = result
        logger.info(f"找到分析结果ID: {result_id}")
        logger.info(f"内容长度: {len(content) if content else 0}")
        
        # 尝试解析元数据
        try:
            if metadata:
                metadata_dict = json.loads(metadata)
                logger.info(f"元数据: {metadata_dict}")
            else:
                logger.warning("元数据为空")
        except Exception as e:
            logger.error(f"解析元数据时出错: {str(e)}")
            logger.info(f"原始元数据: {metadata}")
        
        # 关闭连接
        conn.close()
        
        return True
    except Exception as e:
        logger.error(f"检查数据库时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始运行数据库检查脚本")
    
    # 检查数据库
    success = check_database()
    
    if success:
        logger.info("数据库检查成功")
    else:
        logger.error("数据库检查失败")

if __name__ == "__main__":
    main()
