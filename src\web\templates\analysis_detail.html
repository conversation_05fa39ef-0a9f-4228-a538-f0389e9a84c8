{% extends "base.html" %}

{% block title %}{{ dimension_name }} 分析详情 - {{ novel.title }}{% endblock %}

{% block head %}
{{ super() }}
<!-- 引入分析详情页面专用样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/analysis-detail.css') }}" onerror="this.onerror=null;this.href='/direct-static/css/analysis-detail.css';">
{% endblock %}

{% block content %}
<div class="container">
  <!-- 返回链接 -->
  <div class="mt-4">
    <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="back-btn">
      <i class="fas fa-arrow-left"></i> 返回小说页面
    </a>
  </div>

  <!-- 主要内容区域 -->
  <div class="analysis-container">
    <!-- 标题区域 -->
    <div class="analysis-header">
      <h1 class="analysis-title">{{ dimension_name }} 分析详情</h1>
      <div class="status-badge badge-success">
        <i class="fas fa-check-circle"></i> 分析完成
      </div>
    </div>

    <div class="row">
      <!-- 左侧主要内容区域 -->
      <div class="col-md-8">
        <!-- 推理过程区域 -->
        <div class="analysis-card">
          <div class="analysis-card-header">
            <h5 class="mb-0">推理过程</h5>
          </div>
          <div class="analysis-card-body">
            <div class="reasoning-content" id="reasoningContent" data-reasoning-container="true" data-novel-id="{{ novel.id }}" data-dimension="{{ dimension }}">
              <div class="text-center my-3">
                <div class="spinner-border text-primary" role="status">
                  <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2">正在加载推理过程，请稍候...</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 分析结果卡片 -->
        <div class="analysis-card">
          <div class="analysis-card-header">
            <h5 class="mb-0">分析结果</h5>
            <div class="header-actions">
              <button class="btn btn-sm btn-outline-secondary" id="toggleContentView">
                <i class="fas fa-expand-alt"></i> 切换视图
              </button>
            </div>
          </div>
          <div class="analysis-card-body">
            <div class="markdown-content" id="analysisContent">
              {{ analysis_result.content|safe if analysis_result and analysis_result.content else '暂无分析结果' }}
            </div>
            <!-- 分页控制区域，由JS动态添加 -->
            <div id="contentPagination" class="d-none mt-3"></div>
          </div>
        </div>

        <!-- 主要数据表格 -->
        {% if analysis_result and analysis_result.metadata and analysis_result.metadata.data_points %}
        <div class="analysis-card">
          <div class="analysis-card-header">
            <h5 class="mb-0">数据详情</h5>
          </div>
          <div class="analysis-card-body">
            <div class="table-responsive">
              <table class="data-table">
                <thead>
                  <tr>
                    {% for header in analysis_result.metadata.data_points.headers %}
                    <th>{{ header }}</th>
                    {% endfor %}
                  </tr>
                </thead>
                <tbody>
                  {% for row in analysis_result.metadata.data_points.rows %}
                  <tr>
                    {% for cell in row %}
                    <td>{{ cell }}</td>
                    {% endfor %}
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        {% endif %}
      </div>

      <!-- 右侧侧边栏 -->
      <div class="col-md-4">
        <!-- 小说信息卡片 -->
        <div class="sidebar-card">
          <div class="sidebar-card-header">
            <h5 class="mb-0">小说信息</h5>
          </div>
          <div class="sidebar-card-body">
            <div class="sidebar-item">
              <span class="item-label">标题</span>
              <span class="item-value">{{ novel.title }}</span>
            </div>
            <div class="sidebar-item">
              <span class="item-label">作者</span>
              <span class="item-value">{{ novel.author or '未知' }}</span>
            </div>
            <div class="sidebar-item">
              <span class="item-label">字数</span>
              <span class="item-value">{{ novel.word_count }}</span>
            </div>
          </div>
        </div>

        <!-- 分析元数据卡片 -->
        <div class="sidebar-card">
          <div class="sidebar-card-header">
            <h5 class="mb-0">分析元数据</h5>
          </div>
          <div class="sidebar-card-body">
            {% if analysis_result and analysis_result.metadata %}
            {% set metadata = analysis_result.metadata %}
            <div class="sidebar-item">
              <span class="item-label">分析时间</span>
              <span class="item-value">{{ analysis_result.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
            </div>
            <div class="sidebar-item">
              <span class="item-label">处理时间</span>
              <span class="item-value">{{ metadata.processing_time|round(2) if metadata.processing_time else 0 }} 秒</span>
            </div>
            <div class="sidebar-item">
              <span class="item-label">分块数量</span>
              <span class="item-value">{{ metadata.chunk_count if metadata.chunk_count else 0 }}</span>
            </div>
            <div class="sidebar-item">
              <span class="item-label">API调用次数</span>
              <span class="item-value">{{ metadata.api_calls if metadata.api_calls else 0 }}</span>
            </div>
            {% else %}
            <p class="text-muted">无元数据信息</p>
            {% endif %}
          </div>
        </div>

        <!-- 操作按钮卡片 -->
        <div class="sidebar-card">
          <div class="sidebar-card-header">
            <h5 class="mb-0">操作</h5>
          </div>
          <div class="sidebar-card-body">
            <button class="primary-btn w-100 mb-3 reanalyze-btn"
                    data-dimension="{{ dimension }}"
                    data-novel-id="{{ novel.id }}">
              <i class="fas fa-sync-alt"></i> 重新分析
            </button>
            <a href="{{ url_for('analysis_process.view_analysis_process_viewer', novel_id=novel.id, dimension=dimension) if 'analysis_process' in url_for else '#' }}"
               class="outline-btn w-100 d-block text-center">
              查看完整分析过程
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 分析进度模态框 -->
<div class="modal fade" id="analysisProgressModal" tabindex="-1" aria-labelledby="analysisProgressModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="analysisProgressModalLabel">分析进行中</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="progress" style="height: 10px;">
          <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
        </div>
        <div class="mt-3" id="progressMessage">
          正在准备分析...
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="cancelAnalysisBtn">取消分析</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 图表禁用脚本 -->
<script src="{{ url_for('static', filename='js/disable-charts.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/disable-charts.js';"></script>

<!-- 图表错误抑制器 - 抑制与图表相关的错误消息 -->
<script src="{{ url_for('static', filename='js/chart-error-suppressor.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chart-error-suppressor.js';"></script>
<!-- 引入维度详情页面修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-detail-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-detail-fix.js';"></script>
<!-- 引入推理过程加载器 -->
<script src="{{ url_for('static', filename='js/reasoning-content-loader.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-loader.js';"></script>
<!-- 引入推理过程修复脚本 -->
<script src="{{ url_for('static', filename='js/reasoning-content-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-fix.js';"></script>
<!-- 引入推理过程紧急修复脚本 -->
<script src="{{ url_for('static', filename='js/reasoning-content-emergency-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-emergency-fix.js';"></script>
<!-- 引入推理过程修复脚本V2 -->
<script src="{{ url_for('static', filename='js/reasoning-content-fix-v2.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-fix-v2.js';"></script>
<script>
// 使用延迟加载策略
document.addEventListener('DOMContentLoaded', function() {
  console.log('分析详情页面已加载，准备初始化组件');

  // 延迟初始化，让页面先渲染
  setTimeout(function() {
    // 绑定重新分析按钮事件
    document.querySelectorAll('.reanalyze-btn').forEach(button => {
      button.addEventListener('click', function() {
        const dimension = this.getAttribute('data-dimension');
        const novelId = this.getAttribute('data-novel-id');

        if (confirm(`确定要重新分析${dimension}维度吗？这将覆盖现有的分析结果。`)) {
          startAnalysis(novelId, dimension);
        }
      });
    });
  }, 500);
});

// 图表功能已完全禁用，节省系统资源
console.log('图表功能已完全禁用，节省系统资源');

// 图表功能已完全禁用，不再创建图表

// 初始化内容分页功能
document.addEventListener('DOMContentLoaded', function() {
  // 获取分析内容元素
  const contentElement = document.getElementById('analysisContent');
  const paginationElement = document.getElementById('contentPagination');
  const toggleButton = document.getElementById('toggleContentView');

  if (!contentElement || !paginationElement || !toggleButton) return;

  // 保存原始内容
  const originalContent = contentElement.innerHTML;
  let isPaginated = false;
  let currentPage = 0;
  let contentChunks = [];

  // 切换视图按钮点击事件
  toggleButton.addEventListener('click', function() {
    if (isPaginated) {
      // 切换回完整视图
      contentElement.innerHTML = originalContent;
      paginationElement.classList.add('d-none');
      toggleButton.innerHTML = '<i class="fas fa-expand-alt"></i> 切换视图';
      isPaginated = false;
    } else {
      // 切换到分页视图
      if (contentChunks.length === 0) {
        // 首次切换，需要分割内容
        contentChunks = splitContent(originalContent);
      }

      // 显示第一页
      currentPage = 0;
      contentElement.innerHTML = contentChunks[currentPage];

      // 显示分页控制
      updatePaginationControls();
      paginationElement.classList.remove('d-none');
      toggleButton.innerHTML = '<i class="fas fa-compress-alt"></i> 完整视图';
      isPaginated = true;
    }
  });

  // 更新分页控制
  function updatePaginationControls() {
    if (contentChunks.length <= 1) {
      paginationElement.classList.add('d-none');
      return;
    }

    paginationElement.innerHTML = `
      <div class="d-flex justify-content-between align-items-center">
        <button class="btn btn-sm btn-outline-secondary" id="prevPage" ${currentPage === 0 ? 'disabled' : ''}>
          <i class="fas fa-chevron-left"></i> 上一页
        </button>
        <span class="page-info">第 ${currentPage + 1} 页，共 ${contentChunks.length} 页</span>
        <button class="btn btn-sm btn-outline-secondary" id="nextPage" ${currentPage === contentChunks.length - 1 ? 'disabled' : ''}>
          下一页 <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    `;

    // 添加分页按钮事件
    document.getElementById('prevPage').addEventListener('click', function() {
      if (currentPage > 0) {
        currentPage--;
        contentElement.innerHTML = contentChunks[currentPage];
        updatePaginationControls();
        // 滚动到顶部
        contentElement.scrollIntoView({ behavior: 'smooth' });
      }
    });

    document.getElementById('nextPage').addEventListener('click', function() {
      if (currentPage < contentChunks.length - 1) {
        currentPage++;
        contentElement.innerHTML = contentChunks[currentPage];
        updatePaginationControls();
        // 滚动到顶部
        contentElement.scrollIntoView({ behavior: 'smooth' });
      }
    });
  }

  // 分割内容为多个块
  function splitContent(content) {
    // 检查内容长度，如果不够长，不需要分页
    if (content.length < 5000) {
      return [content];
    }

    // 尝试按标题分割
    const headingRegex = /<h[1-3][^>]*>.*?<\/h[1-3]>/gi;
    const headingMatches = content.match(headingRegex);

    if (headingMatches && headingMatches.length > 1) {
      // 有多个标题，按标题分割
      const chunks = [];
      let lastIndex = 0;
      let currentChunk = '';
      let headingsInChunk = 0;

      // 遍历内容，按标题分割
      for (let i = 0; i < headingMatches.length; i++) {
        const heading = headingMatches[i];
        const headingIndex = content.indexOf(heading, lastIndex);

        if (headingIndex > lastIndex) {
          // 添加标题前的内容
          currentChunk += content.substring(lastIndex, headingIndex);
        }

        // 添加当前标题
        currentChunk += heading;
        lastIndex = headingIndex + heading.length;
        headingsInChunk++;

        // 每2-3个标题创建一个新块
        if (headingsInChunk >= 2 || currentChunk.length > 3000) {
          // 添加标题后的内容，直到下一个标题
          if (i < headingMatches.length - 1) {
            const nextHeading = headingMatches[i + 1];
            const nextHeadingIndex = content.indexOf(nextHeading, lastIndex);
            if (nextHeadingIndex > lastIndex) {
              currentChunk += content.substring(lastIndex, nextHeadingIndex);
              lastIndex = nextHeadingIndex;
            }
          }

          chunks.push(currentChunk);
          currentChunk = '';
          headingsInChunk = 0;
        }
      }

      // 添加剩余内容
      if (lastIndex < content.length) {
        currentChunk += content.substring(lastIndex);
        if (currentChunk.trim()) {
          chunks.push(currentChunk);
        }
      }

      return chunks.length > 0 ? chunks : [content];
    } else {
      // 没有足够的标题，按长度分割
      const chunks = [];
      const maxChunkLength = 3000;

      for (let i = 0; i < content.length; i += maxChunkLength) {
        chunks.push(content.substring(i, i + maxChunkLength));
      }

      return chunks;
    }
  }

  // 检测内容长度，如果超过阈值，自动启用分页
  if (originalContent.length > 8000) {
    // 延迟执行，避免页面加载时的性能问题
    setTimeout(function() {
      toggleButton.click();
    }, 1000);
  }
});

// 开始分析
function startAnalysis(novelId, dimension) {
  console.log(`开始分析小说 ${novelId} 的 ${dimension} 维度`);

  // 使用与base.html兼容的方式获取模态框元素
  const progressModalEl = document.getElementById('analysisProgressModal');
  let progressModal;

  try {
    // 检查是否使用的是Bootstrap 5
    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined') {
      progressModal = new bootstrap.Modal(progressModalEl);

      // 显示模态框
      progressModal.show();
    } else if (typeof $ !== 'undefined' && typeof $.fn.modal !== 'undefined') {
      // 兼容Bootstrap 4的写法
      progressModal = $(progressModalEl);
      progressModal.modal('show');
    } else {
      // 如果Bootstrap不可用，使用传统方法显示
      progressModalEl.style.display = 'block';
      progressModalEl.classList.add('show');

      // 添加背景遮罩
      let backdrop = document.createElement('div');
      backdrop.className = 'modal-backdrop fade show';
      document.body.appendChild(backdrop);

      // 保存引用以便后续关闭
      progressModalEl._backdrop = backdrop;
      progressModal = {
        hide: function() {
          progressModalEl.style.display = 'none';
          progressModalEl.classList.remove('show');
          if (progressModalEl._backdrop) {
            progressModalEl._backdrop.remove();
          }
        }
      };
    }
  } catch (error) {
    console.error('初始化模态框时出错:', error);
    // 使用传统方法显示
    progressModalEl.style.display = 'block';
    // 不添加背景等样式，简单显示
    progressModal = {
      hide: function() {
        progressModalEl.style.display = 'none';
      }
    };
  }

  // 重置进度条
  const progressBar = document.querySelector('#analysisProgressModal .progress-bar');
  progressBar.style.width = '0%';
  document.getElementById('progressMessage').textContent = '正在准备分析...';

  // 发送分析请求
  fetch(`/api/analysis/start`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      novel_id: novelId,
      dimension: dimension
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('分析请求已发送:', data);

    if (data.success) {
      // 开始轮询分析进度
      const taskId = data.task_id;
      pollAnalysisProgress(taskId, novelId, dimension, progressModal);

      // 绑定取消分析按钮
      document.getElementById('cancelAnalysisBtn').onclick = function() {
        cancelAnalysis(taskId);
        progressModal.hide();
      };
    } else {
      alert(`分析请求失败: ${data.message}`);
      progressModal.hide();
    }
  })
  .catch(error => {
    console.error('分析请求错误:', error);
    alert(`请求错误: ${error.message}`);
    progressModal.hide();
  });
}

// 轮询分析进度
function pollAnalysisProgress(taskId, novelId, dimension, progressModal) {
  console.log(`轮询分析任务 ${taskId} 的进度`);

  const progressBar = document.querySelector('#analysisProgressModal .progress-bar');
  const progressMessage = document.getElementById('progressMessage');

  // 设置轮询间隔
  const pollInterval = setInterval(() => {
    fetch(`/api/analysis/progress/${taskId}`)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      console.log('分析进度:', data);

      if (data.success) {
        // 更新进度条
        const progress = data.progress || 0;
        progressBar.style.width = `${progress}%`;
        progressMessage.textContent = data.message || '分析中...';

        // 检查是否完成
        if (data.status === 'completed') {
          clearInterval(pollInterval);
          progressMessage.textContent = '分析完成，正在刷新页面...';

          // 分析完成后刷新页面
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        }
      } else {
        clearInterval(pollInterval);
        progressMessage.textContent = `分析失败: ${data.message || '未知错误'}`;
      }
    })
    .catch(error => {
      console.error('获取分析进度出错:', error);
      clearInterval(pollInterval);
      progressMessage.textContent = `获取进度出错: ${error.message}`;
    });
  }, 2000); // 每2秒轮询一次
}

// 取消分析
function cancelAnalysis(taskId) {
  console.log(`取消分析任务 ${taskId}`);

  fetch(`/api/analysis/cancel/${taskId}`, {
    method: 'POST'
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('取消分析请求结果:', data);

    if (data.success) {
      console.log('分析已成功取消');
    } else {
      console.error(`取消分析失败: ${data.message}`);
    }
  })
  .catch(error => {
    console.error('取消分析请求错误:', error);
  });
}
</script>
{% endblock %}