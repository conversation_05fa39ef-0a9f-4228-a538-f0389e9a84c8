"""
测试九猫系统的缓存功能
"""
import os
import sys
import time
import json
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

import config
from src.models.novel import Novel
from src.api.analysis import NovelAnalyzer
from src.models.analysis_result import AnalysisResult
from src.db.connection import Session

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("cache_test")

def test_cache_functionality():
    """测试缓存功能"""
    logger.info("=== 开始测试缓存功能 ===")
    
    # 创建测试小说内容
    test_content = "这是一个测试小说，用于测试九猫系统的缓存功能。\n" * 1000
    
    # 创建测试小说对象
    test_novel = Novel(
        title="缓存测试小说",
        content=test_content,
        author="测试作者"
    )
    
    # 保存小说到数据库
    session = Session()
    session.add(test_novel)
    session.commit()
    novel_id = test_novel.id
    logger.info(f"创建测试小说，ID: {novel_id}")
    
    # 创建分析器
    analyzer = NovelAnalyzer()
    
    # 测试维度
    test_dimension = "language_style"
    
    # 启用调试模式，避免实际调用API
    config.DEBUG = True
    
    # 第一次分析：不使用缓存
    logger.info("第一次分析：不使用缓存")
    config.CACHE_ENABLED = False
    start_time = time.time()
    result1 = analyzer._analyze_dimension_optimized(
        {"novel": test_novel, "chunks": ["测试块1", "测试块2"], "total_chunks": 2},
        test_dimension
    )
    time1 = time.time() - start_time
    logger.info(f"第一次分析完成，耗时: {time1:.2f}秒")
    
    # 第二次分析：使用缓存
    logger.info("第二次分析：启用缓存")
    config.CACHE_ENABLED = True
    config.FORCE_REFRESH_CACHE = False
    start_time = time.time()
    result2 = analyzer._analyze_dimension_optimized(
        {"novel": test_novel, "chunks": ["测试块1", "测试块2"], "total_chunks": 2},
        test_dimension
    )
    time2 = time.time() - start_time
    logger.info(f"第二次分析完成，耗时: {time2:.2f}秒")
    
    # 第三次分析：强制刷新缓存
    logger.info("第三次分析：强制刷新缓存")
    config.CACHE_ENABLED = True
    config.FORCE_REFRESH_CACHE = True
    start_time = time.time()
    result3 = analyzer._analyze_dimension_optimized(
        {"novel": test_novel, "chunks": ["测试块1", "测试块2"], "total_chunks": 2},
        test_dimension
    )
    time3 = time.time() - start_time
    logger.info(f"第三次分析完成，耗时: {time3:.2f}秒")
    
    # 检查缓存状态
    logger.info("检查缓存状态")
    cached_result = session.query(AnalysisResult).filter_by(
        novel_id=novel_id, dimension=test_dimension
    ).first()
    
    cache_exists = cached_result is not None
    logger.info(f"缓存存在: {cache_exists}")
    
    if cache_exists:
        logger.info(f"缓存创建时间: {cached_result.created_at}")
        logger.info(f"缓存更新时间: {cached_result.updated_at}")
        logger.info(f"缓存内容长度: {len(cached_result.content)}")
    
    # 清理测试数据
    logger.info("清理测试数据")
    session.query(AnalysisResult).filter_by(novel_id=novel_id).delete()
    session.delete(test_novel)
    session.commit()
    session.close()
    
    # 输出结果
    logger.info("=== 缓存测试结果 ===")
    logger.info(f"第一次分析（无缓存）耗时: {time1:.2f}秒")
    logger.info(f"第二次分析（使用缓存）耗时: {time2:.2f}秒")
    logger.info(f"第三次分析（强制刷新）耗时: {time3:.2f}秒")
    logger.info(f"缓存加速比: {time1/time2:.2f}x")
    
    return {
        "no_cache_time": time1,
        "with_cache_time": time2,
        "force_refresh_time": time3,
        "cache_exists": cache_exists
    }

if __name__ == "__main__":
    # 运行测试
    results = test_cache_functionality()
    
    # 输出结果摘要
    print("\n测试结果摘要:")
    print(f"无缓存分析耗时: {results['no_cache_time']:.2f}秒")
    print(f"使用缓存分析耗时: {results['with_cache_time']:.2f}秒")
    print(f"强制刷新缓存分析耗时: {results['force_refresh_time']:.2f}秒")
    print(f"缓存加速比: {results['no_cache_time']/results['with_cache_time']:.2f}x")
    print(f"缓存是否存在: {'是' if results['cache_exists'] else '否'}")
