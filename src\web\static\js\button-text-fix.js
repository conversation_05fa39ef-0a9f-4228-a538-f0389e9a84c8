/**
 * 九猫系统按钮文字修复脚本
 * 解决按钮文字只在悬停时显示的问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('按钮文字修复脚本已加载');

    // 在DOM加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复按钮文字显示问题');
        fixButtonText();
    });

    // 修复按钮文字显示问题
    function fixButtonText() {
        // 查找所有按钮
        const buttons = document.querySelectorAll('.btn');
        console.log('找到 ' + buttons.length + ' 个按钮');

        // 修复每个按钮
        buttons.forEach(function(button, index) {
            // 确保按钮文字可见
            if (button.style.color === 'transparent' || 
                button.style.color === 'rgba(0, 0, 0, 0)' || 
                button.style.opacity === '0') {
                console.log('修复按钮 #' + index + ' 的文字显示');
                button.style.color = '';
                button.style.opacity = '1';
            }

            // 检查是否有内联样式导致文字隐藏
            if (button.hasAttribute('style')) {
                const style = button.getAttribute('style');
                if (style.includes('color: transparent') || 
                    style.includes('color:transparent') || 
                    style.includes('opacity: 0') || 
                    style.includes('opacity:0') ||
                    style.includes('text-indent:') ||
                    style.includes('text-indent: ')) {
                    console.log('移除按钮 #' + index + ' 的内联样式');
                    button.removeAttribute('style');
                }
            }

            // 确保按钮内的所有文本节点可见
            Array.from(button.childNodes).forEach(function(node) {
                if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                    // 文本节点不为空，确保其父元素可见
                    button.style.color = '';
                    button.style.opacity = '1';
                }
            });

            // 检查按钮是否有文本内容
            const buttonText = button.textContent.trim();
            if (buttonText) {
                console.log('按钮 #' + index + ' 文本内容: ' + buttonText);
                
                // 如果按钮有文本但不可见，强制设置样式
                if (getComputedStyle(button).color === 'rgba(0, 0, 0, 0)' || 
                    getComputedStyle(button).opacity === '0') {
                    console.log('强制修复按钮 #' + index + ' 的文字显示');
                    button.style.setProperty('color', 'inherit', 'important');
                    button.style.setProperty('opacity', '1', 'important');
                }
            }
        });

        // 添加全局CSS规则
        addGlobalStyle();
    }

    // 添加全局CSS规则
    function addGlobalStyle() {
        const style = document.createElement('style');
        style.textContent = `
            /* 确保所有按钮文字正常显示 */
            .btn {
                color: inherit !important;
                opacity: 1 !important;
                visibility: visible !important;
                overflow: visible !important;
                text-indent: 0 !important;
                white-space: normal !important;
            }
            
            /* 确保按钮内的图标和文字都显示 */
            .btn i, .btn span, .btn svg {
                color: inherit !important;
                opacity: 1 !important;
                visibility: visible !important;
            }
            
            /* 主要按钮样式修复 */
            .btn-primary {
                color: #fff !important;
            }
            
            /* 次要按钮样式修复 */
            .btn-secondary {
                color: #fff !important;
            }
            
            /* 成功按钮样式修复 */
            .btn-success {
                color: #fff !important;
            }
            
            /* 危险按钮样式修复 */
            .btn-danger {
                color: #fff !important;
            }
            
            /* 警告按钮样式修复 */
            .btn-warning {
                color: #212529 !important;
            }
            
            /* 信息按钮样式修复 */
            .btn-info {
                color: #fff !important;
            }
            
            /* 轮廓按钮样式修复 */
            .btn-outline-primary {
                color: #007bff !important;
            }
            
            .btn-outline-primary:hover {
                color: #fff !important;
            }
            
            .btn-outline-secondary {
                color: #6c757d !important;
            }
            
            .btn-outline-secondary:hover {
                color: #fff !important;
            }
        `;
        document.head.appendChild(style);
        console.log('添加了全局CSS规则');
    }

    // 监听DOM变化，处理动态添加的按钮
    function observeDOMChanges() {
        // 创建一个观察器实例
        const observer = new MutationObserver(function(mutations) {
            let shouldFixButtons = false;
            
            mutations.forEach(function(mutation) {
                // 检查是否有新增的节点
                if (mutation.addedNodes.length) {
                    // 检查新增的节点中是否有按钮
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.classList && node.classList.contains('btn')) {
                                shouldFixButtons = true;
                            } else if (node.querySelectorAll) {
                                const buttons = node.querySelectorAll('.btn');
                                if (buttons.length > 0) {
                                    shouldFixButtons = true;
                                }
                            }
                        }
                    });
                }
            });
            
            // 如果有新增的按钮，执行修复
            if (shouldFixButtons) {
                console.log('检测到DOM变化，重新修复按钮文字');
                fixButtonText();
            }
        });
        
        // 配置观察选项
        const config = { 
            childList: true, 
            subtree: true 
        };
        
        // 开始观察文档主体
        observer.observe(document.body, config);
        console.log('已开始监听DOM变化');
    }

    // 在页面加载完成后开始监听DOM变化
    window.addEventListener('load', function() {
        // 再次修复按钮文字，确保所有动态加载的内容都被处理
        fixButtonText();
        
        // 开始监听DOM变化
        observeDOMChanges();
        
        console.log('页面加载完成，已设置DOM变化监听');
    });
})();
