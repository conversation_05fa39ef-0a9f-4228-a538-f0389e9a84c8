/**
 * 九猫系统 - 分析页面并发限制脚本
 * 限制同时分析的维度数量，防止服务器过载和500错误
 */

(function() {
    // 配置参数
    const CONFIG = {
        maxConcurrentDimensions: 3,      // 最大并发分析维度数
        dimensionQueueDelay: 5000,       // 排队延迟(ms)
        checkInterval: 2000,             // 检查间隔(ms)
        autoRetryFailed: true,           // 自动重试失败的维度
        maxRetries: 3,                   // 最大重试次数
        retryDelay: 10000,               // 重试延迟(ms)
        logToConsole: true               // 记录到控制台
    };
    
    // 维度状态跟踪
    const dimensionStatus = {};
    
    // 维度队列
    let dimensionQueue = [];
    
    // 记录当前正在分析的维度
    let activeDimensions = [];
    
    // 初始化
    function init() {
        console.log('[分析限制] 初始化分析页面限制...');
        
        // 页面加载时检查当前分析状态
        checkCurrentAnalysisStatus();
        
        // 监听页面上的分析请求
        setupAnalysisRequestLimits();
        
        // 定期检查维度状态
        setInterval(checkDimensionsStatus, CONFIG.checkInterval);
        
        console.log('[分析限制] 分析页面限制已初始化');
    }
    
    // 检查当前分析状态
    function checkCurrentAnalysisStatus() {
        // 获取当前页面的小说ID
        const novelId = getNovelId();
        
        if (!novelId) {
            return;
        }
        
        // 获取所有维度的分析状态
        fetch(`/api/analysis/status?novel_id=${novelId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.dimensions) {
                    // 更新维度状态
                    Object.keys(data.dimensions).forEach(dimension => {
                        const status = data.dimensions[dimension];
                        
                        dimensionStatus[dimension] = {
                            status: status.status || 'unknown',
                            progress: status.progress || 0,
                            lastUpdated: new Date()
                        };
                        
                        // 将正在分析的维度添加到活动列表
                        if (status.status === 'running') {
                            if (!activeDimensions.includes(dimension)) {
                                activeDimensions.push(dimension);
                            }
                        }
                    });
                    
                    // 记录活动维度
                    logActiveDimensions();
                    
                    // 如果有太多活动维度，显示警告
                    if (activeDimensions.length > CONFIG.maxConcurrentDimensions) {
                        showTooManyDimensionsWarning();
                    }
                }
            })
            .catch(error => {
                console.error('[分析限制] 获取分析状态失败:', error);
            });
    }
    
    // 设置分析请求限制
    function setupAnalysisRequestLimits() {
        // 拦截fetch请求
        const originalFetch = window.fetch;
        
        window.fetch = function(resource, init) {
            const url = (resource instanceof Request) ? resource.url : resource;
            
            // 检查是否是开始分析的请求
            if (typeof url === 'string' && url.includes('/api/analysis/start')) {
                // 解析维度参数
                const urlObj = new URL(url, window.location.origin);
                const dimension = urlObj.searchParams.get('dimension');
                
                if (dimension) {
                    // 检查是否已达到最大并发数
                    if (activeDimensions.length >= CONFIG.maxConcurrentDimensions) {
                        console.warn(`[分析限制] 已达到最大并发分析维度数(${CONFIG.maxConcurrentDimensions})，将排队等待`);
                        
                        // 将请求添加到队列
                        return new Promise((resolve, reject) => {
                            dimensionQueue.push({
                                dimension: dimension,
                                url: url,
                                init: init,
                                resolve: resolve,
                                reject: reject,
                                addedTime: new Date()
                            });
                            
                            // 显示排队消息
                            showDimensionQueuedMessage(dimension);
                        });
                    } else {
                        // 添加到活动维度
                        if (!activeDimensions.includes(dimension)) {
                            activeDimensions.push(dimension);
                            
                            // 记录活动维度
                            logActiveDimensions();
                        }
                    }
                }
            }
            
            // 正常执行请求
            return originalFetch.apply(this, arguments);
        };
    }
    
    // 检查维度状态
    function checkDimensionsStatus() {
        const novelId = getNovelId();
        
        if (!novelId || activeDimensions.length === 0) {
            return;
        }
        
        // 获取所有活动维度的状态
        fetch(`/api/analysis/status?novel_id=${novelId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.dimensions) {
                    // 更新维度状态
                    activeDimensions.forEach(dimension => {
                        if (data.dimensions[dimension]) {
                            const status = data.dimensions[dimension];
                            
                            // 更新状态
                            dimensionStatus[dimension] = {
                                status: status.status || 'unknown',
                                progress: status.progress || 0,
                                lastUpdated: new Date()
                            };
                            
                            // 检查是否已完成或失败
                            if (status.status === 'completed' || status.status === 'failed') {
                                // 从活动维度中移除
                                const index = activeDimensions.indexOf(dimension);
                                if (index !== -1) {
                                    activeDimensions.splice(index, 1);
                                }
                                
                                // 如果失败，尝试重试
                                if (status.status === 'failed' && CONFIG.autoRetryFailed) {
                                    handleFailedDimension(dimension, novelId);
                                }
                                
                                // 处理队列中的下一个维度
                                processNextQueuedDimension();
                            }
                        }
                    });
                    
                    // 记录活动维度
                    logActiveDimensions();
                }
            })
            .catch(error => {
                console.error('[分析限制] 获取分析状态失败:', error);
            });
    }
    
    // 处理失败的维度
    function handleFailedDimension(dimension, novelId) {
        // 检查重试次数
        const retryKey = `retry_${dimension}_${novelId}`;
        const retryCount = parseInt(sessionStorage.getItem(retryKey) || '0');
        
        if (retryCount >= CONFIG.maxRetries) {
            console.warn(`[分析限制] 维度 ${dimension} 已达到最大重试次数(${CONFIG.maxRetries})，不再重试`);
            return;
        }
        
        // 增加重试计数
        sessionStorage.setItem(retryKey, (retryCount + 1).toString());
        
        console.log(`[分析限制] 维度 ${dimension} 失败，将在 ${CONFIG.retryDelay/1000} 秒后重试 (${retryCount + 1}/${CONFIG.maxRetries})`);
        
        // 延迟重试
        setTimeout(() => {
            // 重新开始分析
            fetch(`/api/analysis/start?novel_id=${novelId}&dimension=${dimension}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log(`[分析限制] 维度 ${dimension} 重试成功`);
                        
                        // 添加到活动维度
                        if (!activeDimensions.includes(dimension)) {
                            activeDimensions.push(dimension);
                        }
                    } else {
                        console.error(`[分析限制] 维度 ${dimension} 重试失败:`, data.error || '未知错误');
                    }
                })
                .catch(error => {
                    console.error(`[分析限制] 维度 ${dimension} 重试请求失败:`, error);
                });
        }, CONFIG.retryDelay);
    }
    
    // 处理队列中的下一个维度
    function processNextQueuedDimension() {
        // 检查队列是否为空
        if (dimensionQueue.length === 0) {
            return;
        }
        
        // 检查是否已达到最大并发数
        if (activeDimensions.length >= CONFIG.maxConcurrentDimensions) {
            return;
        }
        
        // 获取队列中的下一个维度
        const nextItem = dimensionQueue.shift();
        
        console.log(`[分析限制] 处理队列中的维度 ${nextItem.dimension}`);
        
        // 添加到活动维度
        if (!activeDimensions.includes(nextItem.dimension)) {
            activeDimensions.push(nextItem.dimension);
        }
        
        // 执行请求
        const originalFetch = window.fetch;
        originalFetch(nextItem.url, nextItem.init)
            .then(response => nextItem.resolve(response))
            .catch(error => nextItem.reject(error));
        
        // 记录活动维度
        logActiveDimensions();
    }
    
    // 获取小说ID
    function getNovelId() {
        // 从URL获取小说ID
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        if (match && match[1]) {
            return match[1];
        }
        
        // 尝试从页面元素获取
        const novelIdElement = document.querySelector('[data-novel-id]');
        if (novelIdElement) {
            return novelIdElement.getAttribute('data-novel-id');
        }
        
        return null;
    }
    
    // 记录活动维度
    function logActiveDimensions() {
        if (!CONFIG.logToConsole) return;
        
        console.log(`[分析限制] 当前活动维度: ${activeDimensions.length}/${CONFIG.maxConcurrentDimensions}`, activeDimensions);
        
        if (dimensionQueue.length > 0) {
            console.log(`[分析限制] 维度队列: ${dimensionQueue.length}`, dimensionQueue.map(item => item.dimension));
        }
    }
    
    // 显示维度排队消息
    function showDimensionQueuedMessage(dimension) {
        // 创建消息元素
        let messageContainer = document.getElementById('dimension-queue-message');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'dimension-queue-message';
            messageContainer.className = 'alert alert-info alert-dismissible fade show';
            messageContainer.style.position = 'fixed';
            messageContainer.style.top = '10px';
            messageContainer.style.right = '10px';
            messageContainer.style.zIndex = '1050';
            messageContainer.style.maxWidth = '400px';
            
            // 添加关闭按钮
            messageContainer.innerHTML = `
                <button type="button" class="btn-close" aria-label="关闭"></button>
                <h5>分析队列</h5>
                <div id="dimension-queue-list"></div>
            `;
            
            // 添加关闭按钮事件
            messageContainer.querySelector('.btn-close').addEventListener('click', function() {
                messageContainer.remove();
            });
            
            document.body.appendChild(messageContainer);
        }
        
        // 获取队列列表容器
        const queueList = document.getElementById('dimension-queue-list');
        
        // 更新队列列表
        queueList.innerHTML = '';
        dimensionQueue.forEach((item, index) => {
            const queueItem = document.createElement('div');
            queueItem.className = 'mb-2';
            queueItem.innerHTML = `
                <strong>${index + 1}. ${item.dimension}</strong>
                <div class="small text-muted">添加时间: ${new Date(item.addedTime).toLocaleTimeString()}</div>
                <div class="progress" style="height: 4px;">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            `;
            queueList.appendChild(queueItem);
        });
        
        // 添加说明
        if (dimensionQueue.length > 0) {
            const explanation = document.createElement('p');
            explanation.className = 'small text-muted mt-2';
            explanation.textContent = `为了避免服务器过载，一次最多只能同时分析 ${CONFIG.maxConcurrentDimensions} 个维度。其他维度将排队等待。`;
            queueList.appendChild(explanation);
        }
    }
    
    // 显示过多维度警告
    function showTooManyDimensionsWarning() {
        // 创建警告元素
        let warningElement = document.getElementById('too-many-dimensions-warning');
        if (!warningElement) {
            warningElement = document.createElement('div');
            warningElement.id = 'too-many-dimensions-warning';
            warningElement.className = 'alert alert-warning alert-dismissible fade show';
            warningElement.innerHTML = `
                <strong>警告: </strong> 当前有 ${activeDimensions.length} 个维度正在同时分析，超过推荐的 ${CONFIG.maxConcurrentDimensions} 个。
                这可能导致服务器过载和分析失败。建议减少同时分析的维度数量。
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
            `;
            
            // 添加到页面
            const container = document.querySelector('.container') || document.body;
            container.prepend(warningElement);
        }
    }
    
    // 启动初始化
    init();
})(); 