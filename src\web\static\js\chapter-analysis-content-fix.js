/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 章节分析内容完整展示补丁
 * 
 * 此脚本专门用于处理章节分析页面的内容展示
 * 确保章纲分析在每个章节的分析维度中完整显示，无字数限制
 */

(function() {
    console.log('章节分析内容完整展示补丁已加载');
    
    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        // 立即修复
        enhanceChapterAnalysisDisplay();
        
        // 监听页面动态加载的内容
        const observer = new MutationObserver(function(mutations) {
            let shouldApplyFix = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // 检查是否有章节分析内容加载
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // 元素节点
                            shouldApplyFix = true;
                            break;
                        }
                    }
                }
            });
            
            if (shouldApplyFix) {
                console.log('检测到动态内容变化，应用章节分析内容展示修复');
                enhanceChapterAnalysisDisplay();
                // 延迟再次应用，确保异步加载的内容也能得到处理
                setTimeout(enhanceChapterAnalysisDisplay, 500);
            }
        });
        
        // 开始监听文档变化
        observer.observe(document.body, { childList: true, subtree: true });
        
        // 定期检查和修复
        setInterval(enhanceChapterAnalysisDisplay, 2000);
    });
    
    // 增强章节分析内容展示
    function enhanceChapterAnalysisDisplay() {
        // 1. 移除所有可能的内容限制
        removeContentLimitations();
        
        // 2. 处理章纲分析内容
        processChapterOutlineContent();
        
        // 3. 展开所有折叠内容
        expandCollapsedContent();
        
        console.log('章节分析内容展示增强修复完成');
    }
    
    // 移除所有可能的内容限制
    function removeContentLimitations() {
        // 查找所有可能的内容容器
        const contentContainers = document.querySelectorAll(
            '.analysis-content, ' +
            '.markdown-content, ' +
            '.analysis-result, ' +
            '.analysis-section, ' +
            '.dimension-content, ' +
            '.result-content, ' +
            '.chapter-analysis-content'
        );
        
        contentContainers.forEach(function(container) {
            // 移除高度和显示限制
            container.style.maxHeight = 'none';
            container.style.overflow = 'visible';
            container.style.textOverflow = 'clip';
            container.style.whiteSpace = 'normal';
            container.style.display = 'block';
            
            // 移除可能的截断类
            container.classList.remove('truncated', 'collapsed', 'limited-height', 'excerpt');
        });
    }
    
    // 处理章纲分析内容
    function processChapterOutlineContent() {
        // 查找可能包含章纲分析的元素
        const outlineElements = document.querySelectorAll('*');
        
        outlineElements.forEach(function(element) {
            // 检查元素内容是否包含章纲分析相关文本
            const elementText = element.innerText || '';
            const elementHtml = element.innerHTML || '';
            
            if (
                (elementText.includes('主要内容') || 
                 elementText.includes('章节大纲') || 
                 elementText.includes('章纲分析')) ||
                (elementHtml.includes('主要内容') || 
                 elementHtml.includes('章节大纲') || 
                 elementHtml.includes('章纲分析'))
            ) {
                console.log('发现包含章纲分析内容的元素，确保完整显示');
                
                // 递归处理该元素及其所有子元素
                ensureElementFullDisplay(element);
                
                // 处理父元素，确保容器也能完整显示
                let parent = element.parentElement;
                for (let i = 0; i < 5 && parent; i++) {
                    ensureElementFullDisplay(parent);
                    parent = parent.parentElement;
                }
            }
        });
    }
    
    // 确保元素完整显示
    function ensureElementFullDisplay(element) {
        if (!element || element.nodeType !== 1) return;
        
        // 移除显示限制
        element.style.maxHeight = 'none';
        element.style.overflow = 'visible';
        element.style.textOverflow = 'clip';
        element.style.whiteSpace = 'normal';
        element.style.display = 'block';
        
        // 移除可能的截断类
        element.classList.remove('truncated', 'collapsed', 'limited-height', 'excerpt');
        
        // 处理子元素
        Array.from(element.children).forEach(ensureElementFullDisplay);
    }
    
    // 展开所有折叠内容
    function expandCollapsedContent() {
        // 展开所有 details 元素
        const detailsElements = document.querySelectorAll('details');
        detailsElements.forEach(function(details) {
            details.setAttribute('open', 'true');
        });
        
        // 点击所有"显示更多"按钮
        const showMoreButtons = document.querySelectorAll(
            '.show-more-btn, .expand-btn, .read-more, .more-btn, ' +
            '[data-action="expand"], [data-action="show-more"], ' +
            '.toggle-content-btn, .toggle-expand-btn'
        );
        
        showMoreButtons.forEach(function(btn) {
            try {
                btn.click();
            } catch(e) {
                btn.style.display = 'none';
            }
        });
    }
    
    // 添加CSS样式
    function addStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            /* 确保所有分析内容完整显示 */
            .analysis-content, 
            .markdown-content,
            .analysis-result,
            .analysis-section,
            .dimension-content,
            .result-content,
            .chapter-analysis-content,
            [data-dimension] .content,
            .chapter-outline-content {
                max-height: none !important;
                overflow: visible !important;
                text-overflow: clip !important;
                white-space: normal !important;
                display: block !important;
            }
            
            /* 隐藏所有展开/折叠按钮 */
            .show-more-btn, .expand-btn, .read-more, .more-btn,
            [data-action="expand"], [data-action="show-more"],
            .toggle-content-btn, .toggle-expand-btn {
                display: none !important;
            }
            
            /* 确保主要内容部分突出显示 */
            *:contains("主要内容") {
                display: block !important;
            }
        `;
        document.head.appendChild(styleElement);
        console.log('添加章节分析内容展示样式');
    }
    
    // 添加样式
    addStyles();
})(); 