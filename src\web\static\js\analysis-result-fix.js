/**
 * 九猫小说分析写作系统 - 分析结果修复脚本
 *
 * 此脚本用于修复分析结果不可见的问题，确保分析完成后结果正确显示
 * 版本: 3.0.0
 */

(function() {
    console.log('[分析结果修复v3] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        pollInterval: 1000,  // 轮询间隔（毫秒）
        maxRetries: 5,       // 最大重试次数
        selectors: {
            analysisContent: '#analysisContent, .analysis-content',
            reasoningContent: '#reasoningContent, #reasoningContentExpanded, .reasoning-content',
            progressBar: '.progress-bar',
            statusBadge: '.analysis-status-badge',
            loadingIndicator: '.loading-indicator',
            analysisResult: '.analysis-result',
            noResultMessage: '.text-center.py-5'
        }
    };

    // 状态
    const STATE = {
        novelId: null,
        chapterId: null,
        dimension: null,
        isChapterAnalysis: false,
        isLoading: false,
        retryCount: 0,
        pollTimer: null
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[分析结果修复v2] ${message}`);
        }
    }

    // 获取页面信息
    function getPageInfo() {
        // 尝试从URL获取信息
        const pathParts = window.location.pathname.split('/');

        // 检查是否是章节分析页面
        if (pathParts.includes('chapter') && pathParts.includes('analysis')) {
            const novelIdIndex = pathParts.indexOf('novel') + 1;
            const chapterIdIndex = pathParts.indexOf('chapter') + 1;
            const dimensionIndex = pathParts.indexOf('analysis') + 1;

            if (novelIdIndex > 0 && chapterIdIndex > 0 && dimensionIndex > 0 &&
                novelIdIndex < pathParts.length &&
                chapterIdIndex < pathParts.length &&
                dimensionIndex < pathParts.length) {

                STATE.novelId = pathParts[novelIdIndex];
                STATE.chapterId = pathParts[chapterIdIndex];
                STATE.dimension = pathParts[dimensionIndex];
                STATE.isChapterAnalysis = true;

                debugLog(`检测到章节分析页面: 小说ID=${STATE.novelId}, 章节ID=${STATE.chapterId}, 维度=${STATE.dimension}`);
                return true;
            }
        }

        // 检查是否是整本书分析页面
        if (pathParts.includes('novel') && pathParts.includes('analysis')) {
            const novelIdIndex = pathParts.indexOf('novel') + 1;
            const dimensionIndex = pathParts.indexOf('analysis') + 1;

            if (novelIdIndex > 0 && dimensionIndex > 0 &&
                novelIdIndex < pathParts.length &&
                dimensionIndex < pathParts.length) {

                STATE.novelId = pathParts[novelIdIndex];
                STATE.dimension = pathParts[dimensionIndex];
                STATE.isChapterAnalysis = false;

                debugLog(`检测到整本书分析页面: 小说ID=${STATE.novelId}, 维度=${STATE.dimension}`);
                return true;
            }
        }

        // 尝试从页面元素获取信息
        const novelElement = document.querySelector('[data-novel-id]');
        const chapterElement = document.querySelector('[data-chapter-id]');
        const dimensionElement = document.querySelector('[data-dimension]');

        if (novelElement) {
            STATE.novelId = novelElement.getAttribute('data-novel-id');

            if (chapterElement) {
                STATE.chapterId = chapterElement.getAttribute('data-chapter-id');
                STATE.isChapterAnalysis = true;
            }

            if (dimensionElement) {
                STATE.dimension = dimensionElement.getAttribute('data-dimension');
            }

            if (STATE.novelId && STATE.dimension) {
                debugLog(`从页面元素获取信息: 小说ID=${STATE.novelId}, ${STATE.isChapterAnalysis ? '章节ID=' + STATE.chapterId + ', ' : ''}维度=${STATE.dimension}`);
                return true;
            }
        }

        debugLog('无法获取页面信息', 'warn');
        return false;
    }

    // 加载分析结果
    function loadAnalysisResult() {
        if (STATE.isLoading) {
            debugLog('已有加载请求正在进行，跳过');
            return;
        }

        if (!STATE.novelId || !STATE.dimension) {
            debugLog('缺少必要信息，无法加载分析结果', 'warn');
            return;
        }

        STATE.isLoading = true;
        debugLog('开始加载分析结果');

        // 构建API URL
        let apiUrl;
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            // 尝试多种可能的API路径
            const possiblePaths = [
                `/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}`,
                `/v3.1/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}`,
                `/api/novels/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}`
            ];
            apiUrl = possiblePaths[0]; // 默认使用第一个路径
            debugLog(`使用章节分析API: ${apiUrl} (将尝试多个路径)`);
        } else {
            // 尝试多种可能的API路径
            const possiblePaths = [
                `/api/novel/${STATE.novelId}/analysis/${STATE.dimension}`,
                `/v3.1/api/novel/${STATE.novelId}/analysis/${STATE.dimension}`,
                `/api/novels/${STATE.novelId}/analysis/${STATE.dimension}`
            ];
            apiUrl = possiblePaths[0]; // 默认使用第一个路径
            debugLog(`使用整本书分析API: ${apiUrl} (将尝试多个路径)`);
        }

        // 添加时间戳防止缓存
        apiUrl += `?_=${Date.now()}`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到分析结果数据');

                if (data.success === false) {
                    throw new Error(data.error || '获取分析结果失败');
                }

                // 提取结果内容
                let content = '';
                if (data.result && data.result.content) {
                    content = data.result.content;
                } else if (data.content) {
                    content = data.content;
                } else if (data.analysis && data.analysis.content) {
                    content = data.analysis.content;
                }

                if (content) {
                    // 更新分析结果显示
                    updateAnalysisContent(content);
                    debugLog('成功更新分析结果内容');

                    // 加载推理过程
                    loadReasoningContent();
                } else {
                    debugLog('分析结果内容为空，尝试备用API路径', 'warn');

                    // 尝试备用API路径
                    tryBackupApiPaths();
                }
            })
            .catch(error => {
                debugLog(`加载分析结果出错: ${error.message}，尝试备用API路径`, 'error');

                // 尝试备用API路径
                tryBackupApiPaths();
            })
            .finally(() => {
                STATE.isLoading = false;
            });
    }

    // 尝试备用API路径
    function tryBackupApiPaths() {
        // 构建备用API URL列表
        let backupUrls = [];
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            backupUrls = [
                `/v3.1/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}`,
                `/api/novels/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}`,
                `/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}`
            ];
        } else {
            backupUrls = [
                `/v3.1/api/novel/${STATE.novelId}/analysis/${STATE.dimension}`,
                `/api/novels/${STATE.novelId}/analysis/${STATE.dimension}`,
                `/api/novel/${STATE.novelId}/analysis/${STATE.dimension}`
            ];
        }

        // 添加时间戳防止缓存
        backupUrls = backupUrls.map(url => `${url}?_=${Date.now()}`);

        // 尝试每个备用URL
        tryNextBackupUrl(backupUrls, 0);
    }

    // 递归尝试备用URL
    function tryNextBackupUrl(urls, index) {
        if (index >= urls.length) {
            debugLog('所有备用API路径都失败，放弃加载', 'error');

            // 尝试重试
            retryLoadAnalysisResult();
            return;
        }

        const url = urls[index];
        debugLog(`尝试备用API路径 ${index + 1}/${urls.length}: ${url}`);

        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog(`备用API路径 ${url} 成功获取数据`);

                // 提取结果内容
                let content = '';
                if (data.result && data.result.content) {
                    content = data.result.content;
                } else if (data.content) {
                    content = data.content;
                } else if (data.analysis && data.analysis.content) {
                    content = data.analysis.content;
                }

                if (content) {
                    // 更新分析结果显示
                    updateAnalysisContent(content);
                    debugLog('成功更新分析结果内容');

                    // 加载推理过程
                    loadReasoningContent();
                } else {
                    // 尝试下一个备用URL
                    tryNextBackupUrl(urls, index + 1);
                }
            })
            .catch(error => {
                debugLog(`备用API路径 ${url} 失败: ${error.message}`, 'error');

                // 尝试下一个备用URL
                tryNextBackupUrl(urls, index + 1);
            });
    }

    // 重试加载分析结果
    function retryLoadAnalysisResult() {
        if (STATE.retryCount < CONFIG.maxRetries) {
            STATE.retryCount++;
            debugLog(`将在2秒后进行第${STATE.retryCount}次重试`);

            setTimeout(() => {
                loadAnalysisResult();
            }, 2000);
        } else {
            debugLog('达到最大重试次数，放弃加载', 'warn');
            STATE.retryCount = 0;
        }
    }

    // 更新分析结果内容
    function updateAnalysisContent(content) {
        const contentElements = document.querySelectorAll(CONFIG.selectors.analysisContent);
        if (contentElements.length > 0) {
            contentElements.forEach(element => {
                // 检查是否有"暂无分析结果"的提示
                const noResultMessage = element.querySelector(CONFIG.selectors.noResultMessage);
                if (noResultMessage) {
                    // 移除"暂无分析结果"的提示
                    noResultMessage.remove();

                    // 创建分析结果元素
                    const resultElement = document.createElement('div');
                    resultElement.className = 'analysis-result';
                    resultElement.innerHTML = content;

                    // 添加到内容元素中
                    element.appendChild(resultElement);
                    debugLog('创建并添加了新的分析结果元素');
                } else {
                    // 查找现有的分析结果元素
                    const resultElement = element.querySelector(CONFIG.selectors.analysisResult);
                    if (resultElement) {
                        // 更新现有的分析结果元素
                        resultElement.innerHTML = content;
                        debugLog('更新了现有的分析结果元素');
                    } else {
                        // 直接更新内容元素
                        element.innerHTML = content;
                        debugLog('直接更新了内容元素');
                    }
                }
            });
            debugLog('分析结果内容已更新');

            // 更新进度条为100%
            updateProgressBar(100);

            // 更新状态徽章
            updateStatusBadge();
        } else {
            debugLog('找不到分析结果内容元素', 'warn');
        }
    }

    // 加载推理过程
    function loadReasoningContent() {
        if (!STATE.novelId || !STATE.dimension) {
            debugLog('缺少必要信息，无法加载推理过程', 'warn');
            return;
        }

        debugLog('开始加载推理过程');

        // 构建API URL
        let apiUrl;
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            apiUrl = `/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}/reasoning_content`;
        } else {
            apiUrl = `/api/novel/${STATE.novelId}/analysis/${STATE.dimension}/reasoning_content`;
        }

        // 添加时间戳防止缓存
        apiUrl += `?_=${Date.now()}`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到推理过程数据');

                // 提取推理过程内容
                let reasoningContent = '';
                if (data.reasoning_content) {
                    reasoningContent = data.reasoning_content;
                } else if (data.content) {
                    reasoningContent = data.content;
                }

                if (reasoningContent) {
                    // 更新推理过程显示
                    updateReasoningContent(reasoningContent);
                    debugLog('成功更新推理过程内容');
                } else {
                    debugLog('推理过程内容为空，尝试备用API路径', 'warn');

                    // 尝试备用API路径
                    tryBackupReasoningApiPaths();
                }
            })
            .catch(error => {
                debugLog(`加载推理过程出错: ${error.message}，尝试备用API路径`, 'error');

                // 尝试备用API路径
                tryBackupReasoningApiPaths();
            });
    }

    // 尝试备用推理过程API路径
    function tryBackupReasoningApiPaths() {
        // 构建备用API URL列表
        let backupUrls = [];
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            backupUrls = [
                `/v3.1/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}/reasoning_content`,
                `/api/novels/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}/reasoning_content`,
                `/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}/reasoning_content`
            ];
        } else {
            backupUrls = [
                `/v3.1/api/novel/${STATE.novelId}/analysis/${STATE.dimension}/reasoning_content`,
                `/api/novels/${STATE.novelId}/analysis/${STATE.dimension}/reasoning_content`,
                `/api/novel/${STATE.novelId}/analysis/${STATE.dimension}/reasoning_content`
            ];
        }

        // 添加时间戳防止缓存
        backupUrls = backupUrls.map(url => `${url}?_=${Date.now()}`);

        // 尝试每个备用URL
        tryNextBackupReasoningUrl(backupUrls, 0);
    }

    // 递归尝试备用推理过程URL
    function tryNextBackupReasoningUrl(urls, index) {
        if (index >= urls.length) {
            debugLog('所有备用推理过程API路径都失败', 'error');
            return;
        }

        const url = urls[index];
        debugLog(`尝试备用推理过程API路径 ${index + 1}/${urls.length}: ${url}`);

        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog(`备用推理过程API路径 ${url} 成功获取数据`);

                // 提取推理过程内容
                let reasoningContent = '';
                if (data.reasoning_content) {
                    reasoningContent = data.reasoning_content;
                } else if (data.content) {
                    reasoningContent = data.content;
                }

                if (reasoningContent) {
                    // 更新推理过程显示
                    updateReasoningContent(reasoningContent);
                    debugLog('成功更新推理过程内容');
                } else {
                    // 尝试下一个备用URL
                    tryNextBackupReasoningUrl(urls, index + 1);
                }
            })
            .catch(error => {
                debugLog(`备用推理过程API路径 ${url} 失败: ${error.message}`, 'error');

                // 尝试下一个备用URL
                tryNextBackupReasoningUrl(urls, index + 1);
            });
    }

    // 更新推理过程内容
    function updateReasoningContent(content) {
        const contentElements = document.querySelectorAll(CONFIG.selectors.reasoningContent);
        if (contentElements.length > 0) {
            contentElements.forEach(element => {
                element.innerHTML = content;
            });
            debugLog('推理过程内容已更新');
        } else {
            debugLog('找不到推理过程内容元素', 'warn');
        }
    }

    // 更新进度条
    function updateProgressBar(progress) {
        const progressBar = document.querySelector(CONFIG.selectors.progressBar);
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
            progressBar.textContent = `${progress}%`;
            debugLog(`进度条已更新为${progress}%`);
        }
    }

    // 更新状态徽章
    function updateStatusBadge() {
        const statusBadge = document.querySelector(CONFIG.selectors.statusBadge);
        if (statusBadge) {
            statusBadge.className = 'badge bg-success analysis-status-badge';
            statusBadge.textContent = '分析已完成';
            debugLog('状态徽章已更新为"分析已完成"');
        }
    }

    // 检查页面是否有分析结果
    function checkForExistingResults() {
        debugLog('检查页面是否已有分析结果');

        // 检查是否有分析结果元素
        const contentElements = document.querySelectorAll(CONFIG.selectors.analysisContent);
        if (contentElements.length > 0) {
            let hasResults = false;

            contentElements.forEach(element => {
                // 检查是否有"暂无分析结果"的提示
                const noResultMessage = element.querySelector(CONFIG.selectors.noResultMessage);
                if (noResultMessage) {
                    debugLog('发现"暂无分析结果"提示，需要加载分析结果');
                } else {
                    // 检查是否有分析结果内容
                    const resultElement = element.querySelector(CONFIG.selectors.analysisResult);
                    if (resultElement && resultElement.textContent.trim()) {
                        debugLog('页面已有分析结果内容，无需重新加载');
                        hasResults = true;
                    }
                }
            });

            return hasResults;
        }

        return false;
    }

    // 初始化
    function init() {
        debugLog('初始化分析结果修复脚本');

        // 获取页面信息
        if (getPageInfo()) {
            // 检查页面是否已有分析结果
            const hasExistingResults = checkForExistingResults();

            if (!hasExistingResults) {
                // 加载分析结果
                loadAnalysisResult();

                // 设置轮询定时器，每隔一段时间检查一次分析结果
                STATE.pollTimer = setInterval(() => {
                    if (!STATE.isLoading) {
                        loadAnalysisResult();
                    }
                }, CONFIG.pollInterval);
            } else {
                debugLog('页面已有分析结果，跳过加载');
            }
        }
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 添加强制重新加载按钮点击事件
    document.addEventListener('DOMContentLoaded', function() {
        // 查找重新分析按钮
        const reanalyzeButtons = document.querySelectorAll('a[href*="force=1"]');
        reanalyzeButtons.forEach(button => {
            button.addEventListener('click', function(event) {
                // 不阻止默认行为，让页面正常跳转
                debugLog('点击了重新分析按钮，将在页面加载后自动加载分析结果');
            });
        });

        // 查找手动加载分析结果按钮
        const loadButtons = document.querySelectorAll('.load-analysis-btn');
        loadButtons.forEach(button => {
            button.addEventListener('click', function(event) {
                debugLog('点击了手动加载分析结果按钮');

                // 获取小说ID和维度
                const container = button.closest('[data-novel-id]');
                if (!container) {
                    debugLog('找不到包含data-novel-id属性的父元素', 'error');
                    return;
                }

                const novelId = container.getAttribute('data-novel-id');
                const dimension = container.getAttribute('data-dimension');

                if (!novelId || !dimension) {
                    debugLog('缺少必要的小说ID或维度信息', 'error');
                    return;
                }

                // 更新状态
                STATE.novelId = novelId;
                STATE.dimension = dimension;
                STATE.isChapterAnalysis = false;

                // 显示加载中状态
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>加载中...';
                button.disabled = true;

                // 加载分析结果
                loadAnalysisResult();
            });
        });
    });

    console.log('[分析结果修复v3] 脚本加载完成');
})();
