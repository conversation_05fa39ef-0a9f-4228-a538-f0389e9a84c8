' 九猫小说分析系统启动脚本 (VBS版本)
' 此脚本会在后台启动九猫系统，并自动打开浏览器，无需显示命令行窗口

' 设置工作目录为脚本所在目录
Set fso = CreateObject("Scripting.FileSystemObject")
currentPath = fso.GetParentFolderName(WScript.ScriptFullName)
Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = currentPath

' 显示启动消息
MsgBox "九猫小说分析系统正在启动..." & vbCrLf & vbCrLf & _
       "系统将在后台运行，浏览器将在10秒钟后自动打开。" & vbCrLf & _
       "请不要关闭浏览器窗口，否则需要手动访问: http://localhost:5001", _
       64, "九猫小说分析系统"

' 创建临时批处理文件来设置环境变量并运行Python
Set tempFile = fso.CreateTextFile("temp_run.bat", True, True) ' 使用UTF-8编码
tempFile.WriteLine("@echo off")
tempFile.WriteLine("chcp 65001 > nul")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("python run.py")
tempFile.Close

' 在后台运行批处理文件，但显示窗口以便查看输出和错误信息
WshShell.Run "temp_run.bat", 1, False

' 等待10秒钟确保服务启动
WScript.Sleep 10000

' 打开浏览器 - 使用cmd命令强制使用默认浏览器
WshShell.Run "cmd /c start http://localhost:5001", 0, False
