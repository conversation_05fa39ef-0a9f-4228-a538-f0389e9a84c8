{"name": "npm-normalize-package-bin", "version": "1.0.1", "description": "Turn any flavor of allowable package.json bin into a normalized object", "repository": "git+https://github.com/npm/npm-normalize-package-bin", "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"tap": "^14.10.2"}}