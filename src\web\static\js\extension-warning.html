<!-- 浏览器扩展引起的错误警告 -->
<div id="extension-warning" class="extension-warning-container">
    <div class="extension-warning-content">
        <div class="extension-warning-header">
            <h3>浏览器扩展可能导致问题</h3>
            <button id="extension-warning-close" class="extension-warning-close-btn">&times;</button>
        </div>
        <div class="extension-warning-body">
            <p>我们检测到一些跨域脚本错误，这通常是由浏览器扩展引起的。</p>
            <p>如果页面功能异常，请尝试:</p>
            <ul>
                <li>暂时禁用浏览器扩展</li>
                <li>使用无痕/隐私窗口访问</li>
                <li>尝试使用其他浏览器</li>
            </ul>
        </div>
        <div class="extension-warning-footer">
            <button id="extension-warning-dismiss" class="extension-warning-dismiss-btn">知道了，不再提示</button>
        </div>
    </div>
</div>

<style>
.extension-warning-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background-color: #fff8e1;
    border: 1px solid #ffe082;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    display: none;
}

.extension-warning-content {
    padding: 15px;
}

.extension-warning-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 1px solid #ffe082;
    padding-bottom: 8px;
}

.extension-warning-header h3 {
    margin: 0;
    font-size: 16px;
    color: #e65100;
}

.extension-warning-close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #e65100;
    padding: 0;
    line-height: 1;
}

.extension-warning-body {
    margin-bottom: 15px;
    font-size: 14px;
    color: #333;
}

.extension-warning-body p {
    margin: 0 0 8px 0;
}

.extension-warning-body ul {
    margin: 8px 0;
    padding-left: 20px;
}

.extension-warning-footer {
    text-align: right;
}

.extension-warning-dismiss-btn {
    background-color: #ff9800;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.extension-warning-dismiss-btn:hover {
    background-color: #f57c00;
}
</style>

<script>
(function() {
    // 在页面完全加载后执行
    window.addEventListener('load', function() {
        // 检查是否有跨域错误
        if (window.__capturedCrossOriginErrors) {
            // 检查用户是否已经关闭过此提示
            try {
                if (localStorage.getItem('extension_warning_dismissed') === 'true') {
                    console.log('[扩展警告] 用户已关闭过警告，不再显示');
                    return;
                }
            } catch (e) {
                // 忽略localStorage错误
            }
            
            // 显示警告
            const warning = document.getElementById('extension-warning');
            if (warning) {
                warning.style.display = 'block';
                
                // 设置关闭按钮事件
                document.getElementById('extension-warning-close').addEventListener('click', function() {
                    warning.style.display = 'none';
                });
                
                // 设置不再提示按钮事件
                document.getElementById('extension-warning-dismiss').addEventListener('click', function() {
                    warning.style.display = 'none';
                    try {
                        localStorage.setItem('extension_warning_dismissed', 'true');
                    } catch (e) {
                        // 忽略localStorage错误
                    }
                });
            }
        }
    });
})();
</script> 