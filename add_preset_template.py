#!/usr/bin/env python
"""
将预设模板添加到数据库
"""
import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的类和函数
from src.models.preset import Preset
from src.db.connection import Session
from novel_preset_template import get_novel_preset_template, get_chapter_preset_template

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'add_preset_template_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

def add_preset_templates():
    """将生成的预设模板添加到数据库"""
    session = Session()
    try:
        # 生成整本书的预设模板
        novel_preset_content = get_novel_preset_template()
        novel_preset = Preset(
            title="15维度小说分析预设模板",
            content=novel_preset_content,
            category="preset_template",
            meta_info={
                "type": "novel",
                "dimensions": 15,
                "version": "1.0"
            }
        )
        session.add(novel_preset)
        logger.info("添加整本书预设模板成功")
        
        # 生成章节的预设模板
        chapter_preset_content = get_chapter_preset_template()
        chapter_preset = Preset(
            title="15维度章节分析预设模板",
            content=chapter_preset_content,
            category="preset_template",
            meta_info={
                "type": "chapter",
                "dimensions": 15,
                "version": "1.0"
            }
        )
        session.add(chapter_preset)
        logger.info("添加章节预设模板成功")
        
        # 提交更改
        session.commit()
        logger.info("所有预设模板添加成功")
        
        # 输出新添加的预设ID
        logger.info(f"整本书预设模板ID: {novel_preset.id}")
        logger.info(f"章节预设模板ID: {chapter_preset.id}")
        
    except Exception as e:
        logger.error(f"添加预设模板时出错: {str(e)}", exc_info=True)
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    logger.info("开始添加预设模板到数据库")
    add_preset_templates()
    logger.info("添加预设模板完成") 