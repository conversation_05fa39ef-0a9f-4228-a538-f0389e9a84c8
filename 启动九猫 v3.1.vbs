Option Explicit

' 九猫小说分析写作系统v3.1 启动脚本
' 此脚本用于无命令行窗口启动九猫v3.1系统

' 声明变量
Dim objShell, objFSO, strCurrentDir, strCommand, strPythonCmd
Dim blnPythonInstalled, objWshShell, intResult
Dim tempFile

' 创建对象
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objWshShell = CreateObject("WScript.Shell")

' 获取当前目录
strCurrentDir = objFSO.GetAbsolutePathName(".")

' 检查Python是否安装
On Error Resume Next
intResult = objWshShell.Run("python --version", 0, True)
blnPythonInstalled = (Err.Number = 0) And (intResult = 0)
On Error GoTo 0

' 如果Python未安装，显示错误消息
If Not blnPythonInstalled Then
    MsgBox "未检测到Python环境，请确保已安装Python 3.7或更高版本。" & vbCrLf & _
           "您可以从 https://www.python.org/downloads/ 下载并安装Python。", _
           vbExclamation, "九猫小说分析写作系统v3.1"
    WScript.Quit
End If

' 显示欢迎消息
MsgBox "欢迎使用九猫小说分析写作系统v3.1!" & vbCrLf & vbCrLf & _
       "预设模板已全面升级，现支持15个详细维度的分析：" & vbCrLf & _
       "语言风格、节奏节拍、结构分析、句式变化、段落长度、" & vbCrLf & _
       "视角变化、段落流畅度、小说特点、世界构建、人物关系、" & vbCrLf & _
       "开篇效果、高潮节奏、章纲分析、大纲分析、热梗统计", _
       vbInformation, "九猫小说分析写作系统v3.1"

' 创建临时批处理文件来设置环境变量并运行Python
Set tempFile = objFSO.CreateTextFile("temp_run_v3_1.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & strCurrentDir & """")
tempFile.WriteLine("echo 当前工作目录: %CD%")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set USE_MOCK_API=False")
tempFile.WriteLine("set ENABLE_MOCK_ANALYSIS=False")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("set MEMORY_OPTIMIZED=True")
tempFile.WriteLine("set LOW_MEMORY_MODE=True")
tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=75")
tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=85")
tempFile.WriteLine("set MAX_DB_CONNECTIONS=30")
tempFile.WriteLine("set DB_POOL_SIZE=30")
tempFile.WriteLine("set DB_MAX_OVERFLOW=20")
tempFile.WriteLine("set MEMORY_STATS_DIR=" & strCurrentDir & "\memory_stats")
tempFile.WriteLine("set DISABLE_AUTO_REFRESH=True")
tempFile.WriteLine("set MEMORY_CHECK_INTERVAL=3")
tempFile.WriteLine("set DISABLE_CHARTS=True")
tempFile.WriteLine("set OPTIMIZE_DIMENSION_DETAIL=True")
tempFile.WriteLine("set ENABLE_LOG_FILTER=True")
tempFile.WriteLine("set SEPARATE_ANALYSIS_PROCESS=True")
tempFile.WriteLine("set ENABLE_BUTTON_TEXT_SUPREME_FIX=True")
tempFile.WriteLine("set FORCE_BUTTON_TEXT_VISIBILITY=True")
tempFile.WriteLine("set DEEPSEEK_API_KEY=***********************************")
tempFile.WriteLine("set QWEN_API_KEY=sk-6f3b4c6ad9f64f78b22bed422c5d278d")
tempFile.WriteLine("set DEFAULT_MODEL=deepseek-r1")
tempFile.WriteLine("set FORCE_REANALYSIS=True")
tempFile.WriteLine("set FORCE_REAL_API=True")
tempFile.WriteLine("set RELOAD_CONFIG=True")
tempFile.WriteLine("set VERSION=3.1")
tempFile.WriteLine("set TOTAL_DIMENSIONS=15")
tempFile.WriteLine("set ENABLE_ANALYSIS_STATUS_FIX=True")

tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("echo 检查Python是否可用...")
tempFile.WriteLine("python --version")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo Python未找到，请确保Python已安装并添加到PATH环境变量中")
tempFile.WriteLine("    pause")
tempFile.WriteLine("    exit /b 1")
tempFile.WriteLine(")")
tempFile.WriteLine("echo 启动九猫v3.1系统...")
tempFile.WriteLine("python -u -m src.web.v3_1_app")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo 启动失败，错误代码: %ERRORLEVEL%")
tempFile.WriteLine("    pause")
tempFile.WriteLine(")")
tempFile.Close

' 在后台运行批处理文件
objShell.Run "cmd /c """ & strCurrentDir & "\temp_run_v3_1.bat""", 1, False

' 等待5秒钟确保服务启动
WScript.Sleep 5000

' 打开浏览器访问系统
objShell.Run "http://localhost:5001/v3.1/console", 1, False

' 显示启动成功消息
MsgBox "九猫小说分析写作系统v3.1已启动！" & vbCrLf & vbCrLf & _
       "系统已在浏览器中打开，地址：http://localhost:5001/v3.1/console" & vbCrLf & _
       "如果浏览器未自动打开，请手动访问上述地址。", vbInformation, "九猫小说分析写作系统v3.1"

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
Set objWshShell = Nothing
