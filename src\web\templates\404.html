{% extends "base.html" %}

{% block title %}页面未找到 - 九猫小说分析系统{% endblock %}

{% block head %}
{{ super() }}
<style>
  .error-container {
    padding: 40px 20px;
    max-width: 800px;
    margin: 40px auto;
    text-align: center;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .error-icon {
    font-size: 64px;
    color: #6c757d;
    margin-bottom: 20px;
  }

  .error-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #343a40;
  }

  .error-message {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 30px;
  }

  .back-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #4361ee;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .back-button:hover {
    background-color: #3a56d4;
    color: white;
    text-decoration: none;
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
  }
</style>
{% endblock %}

{% block content %}
<div class="container">
  <div class="error-container">
    <div class="error-icon">
      <i class="fas fa-search"></i>
    </div>
    <h1 class="error-title">页面未找到</h1>
    <p class="error-message">抱歉，您请求的页面不存在或已被移动</p>

    <div class="mt-4">
      <a href="{{ url_for('index') }}" class="back-button">
        <i class="fas fa-home mr-2"></i> 返回首页
      </a>

      <button onclick="window.history.back()" class="back-button ml-2" style="background-color: #6c757d; border: none; cursor: pointer;">
        <i class="fas fa-arrow-left mr-2"></i> 返回上一页
      </button>
    </div>

    <div class="mt-4">
      <p class="small text-muted">
        如果您认为这是一个错误，请联系管理员
      </p>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 错误处理脚本 - 最高优先级 -->
<script src="{{ url_for('static', filename='js/error-handler.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';"></script>

<!-- 错误页面修复脚本 -->
<script src="{{ url_for('static', filename='js/error-page-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/error-page-fix.js';"></script>

<script>
  // 记录404错误
  console.log('页面未找到: ' + window.location.pathname);
  
  // 尝试提供相关页面建议
  document.addEventListener('DOMContentLoaded', function() {
    // 获取当前路径
    const path = window.location.pathname;
    
    // 简单的相关页面建议逻辑
    let suggestion = '';
    
    if (path.includes('novel')) {
      suggestion = '<p>您可能想要访问 <a href="/novels">小说列表</a> 页面</p>';
    } else if (path.includes('analysis')) {
      suggestion = '<p>您可能想要访问 <a href="/dashboard">数据中心</a> 页面</p>';
    } else if (path.includes('system')) {
      suggestion = '<p>您可能想要访问 <a href="/system_monitor">系统监控</a> 页面</p>';
    }
    
    // 如果有建议，添加到页面
    if (suggestion) {
      const messageElem = document.querySelector('.error-message');
      if (messageElem) {
        messageElem.insertAdjacentHTML('afterend', '<div class="mt-3">' + suggestion + '</div>');
      }
    }
  });
</script>
{% endblock %}
