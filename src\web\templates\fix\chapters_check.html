{% extends "base.html" %}

{% block title %}{{ novel.title }} - 章节状态检查{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('novels') }}">小说列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">章节状态检查</li>
        </ol>
    </nav>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2>{{ novel.title }} - 章节状态检查</h2>
            <div>
                <a href="{{ url_for('chapter_fix.create_chapters', novel_id=novel.id) }}" class="btn btn-primary me-2">
                    重新生成章节
                </a>
                <a href="{{ url_for('chapter.list_chapters', novel_id=novel.id) }}" class="btn btn-success me-2">
                    章节列表
                </a>
                <a href="{{ url_for('chapter.chapters_summary', novel_id=novel.id) }}" class="btn btn-info">
                    章节分析汇总
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h4>章节状态</h4>
                <p>小说《{{ novel.title }}》目前有 <strong>{{ chapter_count }}</strong> 个章节。</p>
                
                {% if chapter_count == 0 %}
                <div class="alert alert-warning">
                    <p>该小说还没有章节！需要先创建章节，才能进行章节分析。</p>
                    <a href="{{ url_for('chapter_fix.create_chapters', novel_id=novel.id) }}" class="btn btn-primary">
                        立即创建章节
                    </a>
                </div>
                {% else %}
                <p>章节已创建，可以进行章节分析。</p>
                {% endif %}
            </div>

            <div class="mt-4">
                <h4>修复指南</h4>
                <ol>
                    <li>如果没有章节，点击"重新生成章节"按钮创建章节</li>
                    <li>创建章节后，点击"章节列表"可以查看所有章节</li>
                    <li>在章节列表页面可以分析特定章节</li>
                    <li>分析完成后，可以在"章节分析汇总"页面查看所有章节的分析结果</li>
                </ol>
            </div>

            {% if chapters %}
            <div class="mt-4">
                <h4>章节列表</h4>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>章节</th>
                                <th>标题</th>
                                <th>字数</th>
                                <th>分析状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for chapter in chapters %}
                            <tr>
                                <td>{{ chapter.chapter_number }}</td>
                                <td>{{ chapter.title or '无标题' }}</td>
                                <td>{{ chapter.word_count }}</td>
                                <td>
                                    {% if analysis_counts[chapter.id] > 0 %}
                                    <span class="badge bg-success">已分析 ({{ analysis_counts[chapter.id] }})</span>
                                    {% else %}
                                    <span class="badge bg-secondary">未分析</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 添加任何需要的JavaScript代码
</script>
{% endblock %} 