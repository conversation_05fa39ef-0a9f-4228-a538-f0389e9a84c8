"""
九猫 - 重新分析脚本
通过调用API接口重新分析novel/4的character_relationships维度
"""

import os
import sys
import json
import logging
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def reanalyze():
    """通过调用API接口重新分析novel/4的character_relationships维度"""
    logger.info("开始重新分析")
    
    # 尝试不同的API端点
    api_endpoints = [
        "http://localhost:5001/api/analysis/start",
        "http://localhost:5001/api/analysis/analyze",
        "http://localhost:5001/api/analyze"
    ]
    
    # 准备请求数据
    data = {
        "novel_id": 4,
        "dimension": "character_relationships"
    }
    
    for endpoint in api_endpoints:
        try:
            logger.info(f"尝试调用API: {endpoint}")
            response = requests.post(endpoint, json=data)
            
            if response.status_code == 200:
                logger.info(f"成功调用API: {endpoint}")
                return True
            else:
                logger.warning(f"调用API失败: {endpoint} {response.status_code} {response.text}")
        except Exception as e:
            logger.error(f"调用API时出错: {endpoint} {str(e)}")
    
    return False

def main():
    """主函数"""
    logger.info("开始运行重新分析脚本")
    
    # 重新分析
    success = reanalyze()
    
    if success:
        logger.info("重新分析成功")
        logger.info("请刷新页面以查看更改")
    else:
        logger.error("重新分析失败")

if __name__ == "__main__":
    main()
