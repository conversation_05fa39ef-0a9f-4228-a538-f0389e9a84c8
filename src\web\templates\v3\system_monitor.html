{% extends "v3/base.html" %}

{% block title %}系统监控 - 九猫小说分析写作系统v3.0{% endblock %}

{% block extra_css %}
<style>
    .monitor-card {
        transition: all 0.3s ease;
    }
    .monitor-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .progress {
        height: 1rem;
    }
    .chart-container {
        height: 300px;
    }
    .log-container {
        height: 400px;
        overflow-y: auto;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 1rem;
        font-family: 'Courier New', Courier, monospace;
    }
    .log-entry {
        margin-bottom: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e9ecef;
    }
    .log-info {
        color: #0d6efd;
    }
    .log-warning {
        color: #ffc107;
    }
    .log-error {
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h1 class="mb-3"><i class="fas fa-tachometer-alt text-primary me-2"></i>系统监控</h1>
                <p class="lead">系统监控页面提供实时的系统状态和性能数据，帮助您了解系统的运行情况。</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong> 系统监控数据每60秒自动刷新一次。您也可以点击右上角的刷新按钮手动刷新。
                </div>
                <div class="text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class="me-3 text-muted">
                            <small>最后更新: {{ monitor_data.last_updated }}</small>
                        </div>
                        <button id="refreshBtn" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i>刷新数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统状态卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-4">
        <div class="card h-100 monitor-card shadow-sm">
            <div class="card-body">
                <h5 class="card-title text-primary">
                    <i class="fas fa-clock me-2"></i>系统运行时间
                </h5>
                <h2 class="display-5 mb-0">{{ monitor_data.uptime }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100 monitor-card shadow-sm">
            <div class="card-body">
                <h5 class="card-title text-success">
                    <i class="fas fa-server me-2"></i>API调用次数
                </h5>
                <h2 class="display-5 mb-0">{{ monitor_data.api_calls }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100 monitor-card shadow-sm">
            <div class="card-body">
                <h5 class="card-title text-info">
                    <i class="fas fa-book me-2"></i>小说总数
                </h5>
                <h2 class="display-5 mb-0">{{ monitor_data.total_novels }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100 monitor-card shadow-sm">
            <div class="card-body">
                <h5 class="card-title text-warning">
                    <i class="fas fa-search me-2"></i>分析总数
                </h5>
                <h2 class="display-5 mb-0">{{ monitor_data.total_analyses }}</h2>
            </div>
        </div>
    </div>
</div>

<!-- 资源使用情况 -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0"><i class="fas fa-microchip me-2"></i>资源使用情况</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>CPU使用率</span>
                        <span>{{ monitor_data.cpu_usage }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: {{ monitor_data.cpu_usage }}%" aria-valuenow="{{ monitor_data.cpu_usage }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>内存使用率</span>
                        <span>{{ monitor_data.memory_usage }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ monitor_data.memory_usage }}%" aria-valuenow="{{ monitor_data.memory_usage }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>磁盘使用率</span>
                        <span>{{ monitor_data.disk_usage }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-info" role="progressbar" style="width: {{ monitor_data.disk_usage }}%" aria-valuenow="{{ monitor_data.disk_usage }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                <div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>API使用率</span>
                        <span>{{ monitor_data.api_usage }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: {{ monitor_data.api_usage }}%" aria-valuenow="{{ monitor_data.api_usage }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h3 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i>资源使用趋势</h3>
            </div>
            <div class="card-body">
                <div class="chart-container" id="resourceChart"></div>
            </div>
        </div>
    </div>
</div>

<!-- API使用情况 -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h3 class="card-title mb-0"><i class="fas fa-exchange-alt me-2"></i>API使用情况</h3>
            </div>
            <div class="card-body">
                <div class="chart-container" id="apiUsageChart"></div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h3 class="card-title mb-0"><i class="fas fa-dollar-sign me-2"></i>API成本统计</h3>
            </div>
            <div class="card-body">
                <div class="chart-container" id="apiCostChart"></div>
            </div>
        </div>
    </div>
</div>

<!-- 系统日志 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-danger text-white">
                <h3 class="card-title mb-0"><i class="fas fa-list-alt me-2"></i>系统日志</h3>
            </div>
            <div class="card-body">
                <div class="log-container" id="logContainer">
                    <div class="log-entry log-info">
                        <span class="log-time">[2025-05-15 10:00:00]</span>
                        <span class="log-level">[INFO]</span>
                        <span class="log-message">系统启动成功</span>
                    </div>
                    <div class="log-entry log-info">
                        <span class="log-time">[2025-05-15 10:01:00]</span>
                        <span class="log-level">[INFO]</span>
                        <span class="log-message">数据库连接成功</span>
                    </div>
                    <div class="log-entry log-warning">
                        <span class="log-time">[2025-05-15 10:05:00]</span>
                        <span class="log-level">[WARNING]</span>
                        <span class="log-message">API调用次数接近限制</span>
                    </div>
                    <div class="log-entry log-error">
                        <span class="log-time">[2025-05-15 10:10:00]</span>
                        <span class="log-level">[ERROR]</span>
                        <span class="log-message">API调用失败，正在重试</span>
                    </div>
                    <div class="log-entry log-info">
                        <span class="log-time">[2025-05-15 10:11:00]</span>
                        <span class="log-level">[INFO]</span>
                        <span class="log-message">API调用重试成功</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // 资源使用趋势图表
        const resourceCtx = document.getElementById('resourceChart').getContext('2d');
        const resourceChart = new Chart(resourceCtx, {
            type: 'line',
            data: {
                labels: ['10:00', '10:10', '10:20', '10:30', '10:40', '10:50', '11:00'],
                datasets: [{
                    label: 'CPU使用率',
                    data: [20, 25, 30, 35, 30, 25, 25],
                    borderColor: 'rgba(13, 110, 253, 1)',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '内存使用率',
                    data: [30, 35, 40, 45, 40, 35, 40],
                    borderColor: 'rgba(25, 135, 84, 1)',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // API使用情况图表
        const apiUsageCtx = document.getElementById('apiUsageChart').getContext('2d');
        const apiUsageChart = new Chart(apiUsageCtx, {
            type: 'bar',
            data: {
                labels: ['DeepSeek R1', '通义千问'],
                datasets: [{
                    label: 'API调用次数',
                    data: [100, 50],
                    backgroundColor: [
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(25, 135, 84, 0.7)'
                    ],
                    borderColor: [
                        'rgba(13, 110, 253, 1)',
                        'rgba(25, 135, 84, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // API成本统计图表
        const apiCostCtx = document.getElementById('apiCostChart').getContext('2d');
        const apiCostChart = new Chart(apiCostCtx, {
            type: 'pie',
            data: {
                labels: ['DeepSeek R1', '通义千问'],
                datasets: [{
                    data: [70, 30],
                    backgroundColor: [
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(25, 135, 84, 0.7)'
                    ],
                    borderColor: [
                        'rgba(13, 110, 253, 1)',
                        'rgba(25, 135, 84, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // 刷新按钮点击事件
        $('#refreshBtn').click(function() {
            // 显示加载中提示
            $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>加载中...');
            $(this).prop('disabled', true);

            // 刷新页面
            location.reload();
        });

        // 自动刷新
        setInterval(function() {
            // 自动刷新页面
            location.reload();
        }, 60000);
    });
</script>
{% endblock %}
