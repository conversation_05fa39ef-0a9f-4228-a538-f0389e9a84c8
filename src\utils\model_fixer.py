"""
九猫系统数据库模型修复工具
用于修复数据库模型初始化问题
"""
import logging
import importlib
import sys
import inspect
import time

logger = logging.getLogger(__name__)

def fix_model_relationships():
    """
    修复数据库模型之间的关系
    
    Returns:
        bool: 是否成功修复
    """
    logger.info("开始修复数据库模型关系...")
    
    # 尝试导入所有模型
    try:
        # 导入预加载模块
        from src.models.preload import preload_all_models
        
        # 预加载所有模型
        preload_all_models()
        
        # 导入所有模型
        from src.models import (
            Base, Novel, AnalysisResult, AnalysisCheckpoint,
            IntermediateResult, AnalysisProcess, ApiLog,
            SystemAlert, SystemMetric
        )
        
        # 检查Novel模型是否有AnalysisProcess关系
        if hasattr(Novel, 'analysis_processes'):
            logger.info("Novel模型已有AnalysisProcess关系")
        else:
            logger.warning("Novel模型缺少AnalysisProcess关系，尝试添加")
            
            # 导入relationship
            from sqlalchemy.orm import relationship
            
            # 添加关系
            Novel.analysis_processes = relationship(
                "AnalysisProcess", 
                back_populates="novel", 
                cascade="all, delete-orphan"
            )
            
            logger.info("已添加Novel.analysis_processes关系")
        
        # 检查AnalysisResult模型是否有processes关系
        if hasattr(AnalysisResult, 'processes'):
            logger.info("AnalysisResult模型已有processes关系")
        else:
            logger.warning("AnalysisResult模型缺少processes关系，尝试添加")
            
            # 导入relationship
            from sqlalchemy.orm import relationship
            
            # 添加关系
            AnalysisResult.processes = relationship(
                "AnalysisProcess", 
                back_populates="result", 
                cascade="all, delete-orphan"
            )
            
            logger.info("已添加AnalysisResult.processes关系")
        
        # 检查AnalysisProcess模型是否有novel和result关系
        if hasattr(AnalysisProcess, 'novel') and hasattr(AnalysisProcess, 'result'):
            logger.info("AnalysisProcess模型已有novel和result关系")
        else:
            logger.warning("AnalysisProcess模型缺少关系，尝试添加")
            
            # 导入relationship
            from sqlalchemy.orm import relationship
            
            # 添加关系
            if not hasattr(AnalysisProcess, 'novel'):
                AnalysisProcess.novel = relationship("Novel", back_populates="analysis_processes")
                logger.info("已添加AnalysisProcess.novel关系")
            
            if not hasattr(AnalysisProcess, 'result'):
                AnalysisProcess.result = relationship("AnalysisResult", back_populates="processes")
                logger.info("已添加AnalysisProcess.result关系")
        
        logger.info("数据库模型关系修复完成")
        return True
    except Exception as e:
        logger.error(f"修复数据库模型关系时出错: {str(e)}")
        return False

def check_model_registry():
    """
    检查模型注册表，确保所有模型都已正确注册
    
    Returns:
        dict: 模型注册状态
    """
    try:
        # 导入Base
        from src.models import Base
        
        # 获取所有已注册的映射器
        mappers = list(Base.registry.mappers)
        
        # 获取所有映射器对应的类
        model_classes = {}
        for mapper in mappers:
            model_class = mapper.class_
            class_name = model_class.__name__
            model_classes[class_name] = model_class
        
        # 检查是否包含所有必要的模型
        required_models = [
            "Novel", "AnalysisResult", "AnalysisCheckpoint",
            "IntermediateResult", "AnalysisProcess", "ApiLog",
            "SystemAlert", "SystemMetric"
        ]
        
        missing_models = [model for model in required_models if model not in model_classes]
        
        result = {
            "registered_models": list(model_classes.keys()),
            "missing_models": missing_models,
            "total_registered": len(model_classes),
            "total_required": len(required_models),
            "all_registered": len(missing_models) == 0
        }
        
        if result["all_registered"]:
            logger.info(f"所有必要的模型 ({result['total_registered']}/{result['total_required']}) 已注册")
        else:
            logger.warning(f"缺少以下模型: {', '.join(missing_models)}")
        
        return result
    except Exception as e:
        logger.error(f"检查模型注册表时出错: {str(e)}")
        return {
            "error": str(e),
            "registered_models": [],
            "missing_models": [],
            "total_registered": 0,
            "total_required": 0,
            "all_registered": False
        }

def fix_database_models():
    """
    全面修复数据库模型问题
    
    Returns:
        bool: 是否成功修复
    """
    logger.info("开始全面修复数据库模型...")
    
    # 步骤1: 预加载所有模型
    try:
        from src.models.preload import preload_all_models
        preload_success = preload_all_models()
        if not preload_success:
            logger.warning("预加载模型部分失败，继续修复")
    except ImportError:
        logger.warning("预加载模块不可用，跳过预加载步骤")
    
    # 步骤2: 修复模型关系
    relationship_fixed = fix_model_relationships()
    
    # 步骤3: 检查模型注册状态
    registry_status = check_model_registry()
    
    # 步骤4: 如果仍有问题，尝试重新加载模块
    if not registry_status["all_registered"]:
        logger.warning("仍有模型未注册，尝试重新加载模块")
        
        # 清除相关模块的缓存
        modules_to_reload = [
            "src.models.base",
            "src.models.novel",
            "src.models.analysis_result",
            "src.models.analysis_process",
            "src.models"
        ]
        
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                logger.info(f"重新加载模块: {module_name}")
                try:
                    # 重新加载模块
                    module = importlib.reload(sys.modules[module_name])
                    logger.info(f"模块 {module_name} 重新加载成功")
                except Exception as e:
                    logger.error(f"重新加载模块 {module_name} 时出错: {str(e)}")
        
        # 再次检查模型注册状态
        registry_status = check_model_registry()
    
    # 返回修复结果
    success = relationship_fixed and registry_status["all_registered"]
    
    if success:
        logger.info("数据库模型全面修复成功")
    else:
        logger.warning("数据库模型修复部分成功")
    
    return success

# 在模块导入时自动修复数据库模型
if __name__ != "__main__":
    # 延迟执行，确保其他模块已加载
    time.sleep(1)
    fix_database_models()
