from flask import Blueprint, render_template, request, jsonify, abort
import logging
import json
import os
import time
import random

tools_bp = Blueprint('tools', __name__, url_prefix='/tools')
logger = logging.getLogger(__name__)

@tools_bp.route('/')
def tools_index():
    """工具首页"""
    return render_template('tools/index.html')

@tools_bp.route('/console-capture')
def console_capture():
    """浏览器控制台日志捕获工具"""
    # 检查是否使用修复版
    use_fix = 'fix' in request.args
    return render_template('tools/console_capture.html', use_fix=use_fix)

@tools_bp.route('/api/mock', methods=['GET', 'POST'])
def mock_api():
    """模拟API响应"""
    # 随机延迟
    time.sleep(random.uniform(0.1, 0.5))
    
    # 返回模拟数据
    return jsonify({
        "success": True,
        "message": "这是一个模拟API响应",
        "timestamp": time.time(),
        "data": {
            "id": random.randint(1, 1000),
            "name": "模拟数据",
            "value": random.random()
        }
    })

@tools_bp.route('/api/metrics', methods=['GET'])
def system_metrics():
    """返回系统指标"""
    # 随机延迟
    time.sleep(random.uniform(0.1, 0.3))
    
    # 返回模拟系统指标
    return jsonify({
        "success": True,
        "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S.%f"),
        "metrics": {
            "cpu": random.uniform(10, 90),
            "memory": random.uniform(20, 80),
            "disk": random.uniform(30, 70),
            "network": {
                "in": random.uniform(1, 100),
                "out": random.uniform(1, 100)
            },
            "processes": random.randint(50, 200)
        }
    })

@tools_bp.route('/api/logs', methods=['POST'])
def save_logs():
    """保存捕获的日志"""
    try:
        # 获取请求数据
        data = request.json
        if not data or 'logs' not in data:
            return jsonify({"success": False, "message": "缺少日志数据"}), 400
        
        # 获取日志数据
        logs = data['logs']
        
        # 创建日志目录
        logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
        os.makedirs(logs_dir, exist_ok=True)
        
        # 生成日志文件名
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        log_file = os.path.join(logs_dir, f"browser-logs-{timestamp}.json")
        
        # 保存日志
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(logs, f, ensure_ascii=False, indent=2)
        
        logger.info(f"保存了 {len(logs)} 条浏览器日志到 {log_file}")
        
        return jsonify({
            "success": True,
            "message": f"成功保存 {len(logs)} 条日志",
            "file": os.path.basename(log_file)
        })
    
    except Exception as e:
        logger.error(f"保存日志时出错: {str(e)}")
        return jsonify({"success": False, "message": f"保存日志失败: {str(e)}"}), 500
