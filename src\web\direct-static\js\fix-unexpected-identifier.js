/**
 * 九猫 - 专门修复"Failed to execute 'insertBefore' on 'Node': Unexpected identifier '$'"错误
 * 版本: 1.0.0
 * 
 * 这个脚本专门解决在character_relationships页面出现的insertBefore错误
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('加载Unexpected identifier "$"修复脚本');

    // 保存原始的insertBefore方法
    const originalInsertBefore = Node.prototype.insertBefore;

    // 重写insertBefore方法
    Node.prototype.insertBefore = function(newNode, referenceNode) {
        try {
            // 尝试使用原始方法
            return originalInsertBefore.call(this, newNode, referenceNode);
        } catch (e) {
            console.error('捕获到insertBefore错误:', e.message);

            // 检查是否是特定的Unexpected identifier '$'错误
            if (e.message && e.message.includes('Unexpected identifier')) {
                console.log('检测到特定的insertBefore错误，尝试安全插入');

                try {
                    // 检查是否是脚本元素
                    if (newNode.nodeType === Node.ELEMENT_NODE && newNode.tagName.toLowerCase() === 'script') {
                        console.log('检测到脚本元素，使用特殊处理');
                        
                        // 创建新的脚本元素
                        const safeScript = document.createElement('script');
                        
                        // 复制属性
                        for (let i = 0; i < newNode.attributes.length; i++) {
                            const attr = newNode.attributes[i];
                            safeScript.setAttribute(attr.name, attr.value);
                        }
                        
                        // 安全设置脚本内容 - 避免使用模板字符串中的${}
                        if (newNode.textContent) {
                            // 替换所有的${为一个安全标记，然后在执行后再替换回来
                            const safeContent = newNode.textContent.replace(/\$\{/g, '___DOLLAR_BRACE___');
                            safeScript.textContent = safeContent;
                            
                            // 添加一个数据属性，表示这是一个安全脚本
                            safeScript.setAttribute('data-safe-script', 'true');
                        }
                        
                        // 安全插入
                        if (referenceNode) {
                            return originalInsertBefore.call(this, safeScript, referenceNode);
                        } else {
                            return this.appendChild(safeScript);
                        }
                    } else {
                        // 对于非脚本元素，创建一个克隆
                        const safeNode = newNode.cloneNode(true);
                        
                        // 安全插入
                        if (referenceNode) {
                            return originalInsertBefore.call(this, safeNode, referenceNode);
                        } else {
                            return this.appendChild(safeNode);
                        }
                    }
                } catch (e2) {
                    console.error('安全插入失败:', e2.message);
                    
                    // 最后尝试：直接添加到文档末尾
                    try {
                        return this.appendChild(newNode);
                    } catch (e3) {
                        console.error('所有方法都失败:', e3.message);
                        throw e; // 重新抛出原始错误
                    }
                }
            } else {
                // 如果不是特定的错误，重新抛出
                throw e;
            }
        }
    };
    
    // 添加MutationObserver监视DOM变化，恢复安全脚本中的${}
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE && 
                        node.tagName.toLowerCase() === 'script' && 
                        node.getAttribute('data-safe-script') === 'true') {
                        
                        // 恢复${}
                        if (node.textContent && node.textContent.includes('___DOLLAR_BRACE___')) {
                            // 获取原始内容
                            const originalContent = node.textContent;
                            
                            // 替换回${}
                            const restoredContent = originalContent.replace(/___DOLLAR_BRACE___/g, '${');
                            
                            // 创建新脚本
                            const restoredScript = document.createElement('script');
                            
                            // 复制属性
                            for (let i = 0; i < node.attributes.length; i++) {
                                const attr = node.attributes[i];
                                if (attr.name !== 'data-safe-script') {
                                    restoredScript.setAttribute(attr.name, attr.value);
                                }
                            }
                            
                            // 设置恢复后的内容
                            restoredScript.textContent = restoredContent;
                            
                            // 替换节点
                            if (node.parentNode) {
                                node.parentNode.replaceChild(restoredScript, node);
                            }
                        }
                    }
                });
            }
        });
    });
    
    // 配置观察选项
    const observerConfig = { 
        childList: true, 
        subtree: true 
    };
    
    // 开始观察
    observer.observe(document.documentElement, observerConfig);

    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && 
            (event.error.message.includes('insertBefore') || 
             event.error.message.includes('Unexpected identifier'))) {
            console.error('捕获到insertBefore相关错误:', event.error.message);
            console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);

    // 初始化完成标记
    window.__fixUnexpectedIdentifierLoaded = true;
    console.log('Unexpected identifier "$"修复脚本加载完成');
})(); 