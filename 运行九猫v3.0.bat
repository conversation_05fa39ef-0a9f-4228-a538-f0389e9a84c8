@echo off
echo 九猫小说分析写作系统 v3.0 - 终端运行脚本
echo ===================================
echo.

rem 设置环境变量
set VERSION=3.0
set DEFAULT_MODEL=deepseek-r1
set FORCE_REANALYSIS=True
set FORCE_REAL_API=True
set RELOAD_CONFIG=True
set TOTAL_DIMENSIONS=15
set ENABLE_ANALYSIS_STATUS_FIX=True
set USE_V3_CONSOLE=True

if not exist logs mkdir logs
echo 检查Python是否可用...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo Python未找到，请确保Python已安装并添加到PATH环境变量中
    pause
    exit /b 1
)

echo 启动九猫v3.0系统...
python -u -m src.web.v3_app

echo 如果系统已停止运行，请查看上面的错误信息
pause
