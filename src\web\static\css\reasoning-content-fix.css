/* 九猫系统 - 推理内容显示修复CSS */

/* 确保推理内容容器没有高度限制，完整显示内容 */
.reasoning-content {
    max-height: none !important; 
    overflow: visible !important;
}

/* 确保推理内容文本没有高度限制，完整显示内容 */
.reasoning-text,
.reasoning-text pre {
    max-height: none !important;
    overflow: visible !important;
    white-space: pre-wrap !important;
    line-height: 1.6 !important;
    font-family: monospace !important;
    width: 100% !important;
}

/* 确保pre标签不被截断 */
pre {
    max-height: none !important;
    overflow: visible !important;
    white-space: pre-wrap !important;
}

/* 移除可能存在的分页或折叠样式 */
.reasoning-content-preview {
    max-height: none !important;
    overflow: visible !important;
}

/* 移除预览状态的渐变遮罩 */
.reasoning-content-preview::after {
    display: none !important;
}

/* 确保父容器也没有限制 */
.card-body {
    max-height: none !important;
    overflow: visible !important;
}

/* 增加内容可读性 */
.reasoning-text {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .reasoning-text {
        background-color: #212529;
        color: #e9ecef;
        border-left-color: #0d6efd;
    }
} 