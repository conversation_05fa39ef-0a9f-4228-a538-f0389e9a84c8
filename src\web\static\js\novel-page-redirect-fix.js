/**
 * 九猫 - 小说详情页面自动跳转修复脚本
 * 解决小说详情页面自动跳转到首页的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._novelPageRedirectFixLoaded) {
        console.log('[跳转修复] 小说详情页面自动跳转修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._novelPageRedirectFixLoaded = true;
    
    console.log('[跳转修复] 小说详情页面自动跳转修复脚本已加载 - 版本1.0.0');
    
    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;
    const originalAssign = window.location.assign;
    const originalReplace = window.location.replace;
    const originalHref = Object.getOwnPropertyDescriptor(window.location, 'href');
    
    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[跳转修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 初始化函数
    function initialize() {
        safeLog('初始化小说详情页面自动跳转修复');
        
        // 检查是否在小说详情页面
        if (!isNovelDetailPage()) {
            safeLog('当前不是小说详情页面，不执行修复');
            return;
        }
        
        safeLog('检测到小说详情页面，开始执行修复');
        
        // 拦截页面跳转
        interceptPageRedirects();
        
        // 拦截定时器中的跳转
        interceptTimerRedirects();
        
        // 修复可能导致跳转的脚本
        fixRedirectScripts();
        
        // 添加页面可见性变化事件处理
        handleVisibilityChange();
        
        safeLog('小说详情页面自动跳转修复完成');
    }
    
    // 检查是否在小说详情页面
    function isNovelDetailPage() {
        const path = window.location.pathname;
        // 匹配 /novel/{id} 格式的URL
        return /^\/novel\/\d+\/?$/.test(path);
    }
    
    // 拦截页面跳转
    function interceptPageRedirects() {
        safeLog('拦截页面跳转');
        
        // 拦截location.assign
        window.location.assign = function(url) {
            // 检查是否是跳转到首页
            if (url === '/' || url === '/index' || url === '/home' || url.startsWith('/?') || url.startsWith('/index?') || url.startsWith('/home?')) {
                safeLog('拦截到跳转到首页的尝试: ' + url);
                showRedirectBlockedNotification(url);
                return false;
            }
            
            // 如果不是跳转到首页，允许正常导航
            return originalAssign.apply(this, arguments);
        };
        
        // 拦截location.replace
        window.location.replace = function(url) {
            // 检查是否是跳转到首页
            if (url === '/' || url === '/index' || url === '/home' || url.startsWith('/?') || url.startsWith('/index?') || url.startsWith('/home?')) {
                safeLog('拦截到通过location.replace跳转到首页的尝试: ' + url);
                showRedirectBlockedNotification(url);
                return false;
            }
            
            // 如果不是跳转到首页，允许正常导航
            return originalReplace.apply(this, arguments);
        };
        
        // 拦截location.href赋值
        try {
            Object.defineProperty(window.location, 'href', {
                set: function(url) {
                    // 检查是否是跳转到首页
                    if (url === '/' || url === '/index' || url === '/home' || url.startsWith('/?') || url.startsWith('/index?') || url.startsWith('/home?')) {
                        safeLog('拦截到通过location.href跳转到首页的尝试: ' + url);
                        showRedirectBlockedNotification(url);
                        return false;
                    }
                    
                    // 如果不是跳转到首页，允许正常导航
                    return originalHref.set.call(this, url);
                },
                get: originalHref.get,
                configurable: true
            });
        } catch (e) {
            safeLog('拦截location.href赋值失败: ' + e.message);
        }
    }
    
    // 拦截定时器中的跳转
    function interceptTimerRedirects() {
        safeLog('拦截定时器中的跳转');
        
        // 重写setTimeout
        window.setTimeout = function(callback, delay, ...args) {
            // 检查回调函数是否可能导致跳转
            const callbackString = callback.toString();
            const isRedirectAttempt = 
                (callbackString.includes('location.href') && (
                    callbackString.includes('="/"') || 
                    callbackString.includes('="/index"') || 
                    callbackString.includes('="/home"')
                )) || 
                (callbackString.includes('location.assign') && (
                    callbackString.includes('("/"') || 
                    callbackString.includes('("/index"') || 
                    callbackString.includes('("/home"')
                )) || 
                (callbackString.includes('location.replace') && (
                    callbackString.includes('("/"') || 
                    callbackString.includes('("/index"') || 
                    callbackString.includes('("/home"')
                ));
            
            if (isRedirectAttempt) {
                safeLog('拦截到setTimeout中的跳转尝试');
                showRedirectBlockedNotification('setTimeout中的跳转');
                
                // 返回一个假的定时器ID
                return -1;
            }
            
            // 如果不是跳转尝试，正常创建定时器
            return originalSetTimeout.apply(this, arguments);
        };
        
        // 重写setInterval
        window.setInterval = function(callback, delay, ...args) {
            // 检查回调函数是否可能导致跳转
            const callbackString = callback.toString();
            const isRedirectAttempt = 
                (callbackString.includes('location.href') && (
                    callbackString.includes('="/"') || 
                    callbackString.includes('="/index"') || 
                    callbackString.includes('="/home"')
                )) || 
                (callbackString.includes('location.assign') && (
                    callbackString.includes('("/"') || 
                    callbackString.includes('("/index"') || 
                    callbackString.includes('("/home"')
                )) || 
                (callbackString.includes('location.replace') && (
                    callbackString.includes('("/"') || 
                    callbackString.includes('("/index"') || 
                    callbackString.includes('("/home"')
                ));
            
            if (isRedirectAttempt) {
                safeLog('拦截到setInterval中的跳转尝试');
                showRedirectBlockedNotification('setInterval中的跳转');
                
                // 返回一个假的定时器ID
                return -1;
            }
            
            // 如果不是跳转尝试，正常创建定时器
            return originalSetInterval.apply(this, arguments);
        };
    }
    
    // 修复可能导致跳转的脚本
    function fixRedirectScripts() {
        safeLog('修复可能导致跳转的脚本');
        
        // 查找并禁用包含自动跳转代码的脚本
        const scripts = document.querySelectorAll('script:not([src])');
        for (const script of scripts) {
            const content = script.textContent || '';
            
            // 检查是否包含自动跳转代码
            if ((content.includes('location.href') && (
                    content.includes('="/"') || 
                    content.includes('="/index"') || 
                    content.includes('="/home"')
                )) || 
                (content.includes('location.assign') && (
                    content.includes('("/"') || 
                    content.includes('("/index"') || 
                    content.includes('("/home"')
                )) || 
                (content.includes('location.replace') && (
                    content.includes('("/"') || 
                    content.includes('("/index"') || 
                    content.includes('("/home"')
                ))) {
                
                safeLog('发现可能导致自动跳转的脚本');
                
                // 尝试替换脚本内容
                try {
                    const newScript = document.createElement('script');
                    newScript.textContent = content
                        .replace(/location\.href\s*=\s*['"]\/['"]|location\.href\s*=\s*['"]\/index['"]|location\.href\s*=\s*['"]\/home['"]/g, 
                                'console.log("[跳转修复] 已阻止跳转到首页")')
                        .replace(/location\.assign\s*\(\s*['"]\/['"]\s*\)|location\.assign\s*\(\s*['"]\/index['"]\s*\)|location\.assign\s*\(\s*['"]\/home['"]\s*\)/g, 
                                'console.log("[跳转修复] 已阻止跳转到首页")')
                        .replace(/location\.replace\s*\(\s*['"]\/['"]\s*\)|location\.replace\s*\(\s*['"]\/index['"]\s*\)|location\.replace\s*\(\s*['"]\/home['"]\s*\)/g, 
                                'console.log("[跳转修复] 已阻止跳转到首页")');
                    
                    // 替换原始脚本
                    if (script.parentNode) {
                        script.parentNode.replaceChild(newScript, script);
                        safeLog('已禁用自动跳转脚本');
                    }
                } catch (e) {
                    safeLog('禁用自动跳转脚本失败: ' + e.message);
                }
            }
        }
    }
    
    // 处理页面可见性变化
    function handleVisibilityChange() {
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，再次执行修复
                safeLog('页面变为可见，再次执行修复');
                setTimeout(initialize, 100);
            }
        });
    }
    
    // 显示跳转被阻止的通知
    function showRedirectBlockedNotification(url) {
        const notificationId = 'redirect-blocked-notification';
        
        // 如果已有通知，不再显示
        if (document.getElementById(notificationId)) {
            return;
        }
        
        // 创建通知元素
        const notification = document.createElement('div');
        notification.id = notificationId;
        notification.style.cssText = 'position: fixed; bottom: 20px; right: 20px; background-color: #fff; border: 1px solid #ccc; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 15px; border-radius: 5px; z-index: 10000; max-width: 350px;';
        
        notification.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px; color: #d9534f;">⚠️ 检测到页面自动跳转</div>
            <p style="margin-bottom: 10px;">已自动阻止页面跳转到首页，以防止意外离开当前页面。</p>
            <div>
                <button id="redirect-blocked-close" style="padding: 5px 10px; background: #eee; border: none; margin-right: 10px; cursor: pointer;">关闭</button>
                <button id="redirect-blocked-allow" style="padding: 5px 10px; background: #5bc0de; color: white; border: none; cursor: pointer;">允许跳转</button>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 添加关闭按钮事件
        document.getElementById('redirect-blocked-close').addEventListener('click', function() {
            notification.remove();
        });
        
        // 添加允许跳转按钮事件
        document.getElementById('redirect-blocked-allow').addEventListener('click', function() {
            notification.remove();
            safeLog('用户允许跳转到首页');
            window.location.href = '/';
        });
        
        // 5秒后自动关闭
        setTimeout(() => {
            if (document.getElementById(notificationId)) {
                notification.remove();
            }
        }, 5000);
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }
    
    // 导出API，方便调试和手动调用
    window.novelPageRedirectFix = {
        initialize: initialize,
        showRedirectBlockedNotification: showRedirectBlockedNotification
    };
})();
