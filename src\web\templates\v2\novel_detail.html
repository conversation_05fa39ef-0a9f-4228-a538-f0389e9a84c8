{% extends "v2/base.html" %}

{% block title %}{{ novel.title }} - 九猫小说分析系统v2.0{% endblock %}

{% block extra_css %}
<style>
    .dimension-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .dimension-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .dimension-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    .nav-tabs .nav-link {
        border: none;
        color: var(--text-color);
        font-weight: 500;
        padding: 0.75rem 1rem;
    }
    .nav-tabs .nav-link.active {
        color: var(--primary-color);
        border-bottom: 3px solid var(--primary-color);
        background-color: transparent;
    }
    .tab-content {
        padding: 1.5rem 0;
    }
    .chapter-list {
        max-height: 500px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<!-- 小说信息 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h1 class="mb-2">{{ novel.title }}</h1>
                <p class="text-muted mb-2">
                    {% if novel.author %}作者: {{ novel.author }}{% endif %}
                    <span class="ms-3">字数: {{ novel.word_count }}</span>
                    <span class="ms-3">上传时间: {{ novel.created_at.strftime('%Y-%m-%d %H:%M') if novel.created_at is not string else novel.created_at }}</span>
                </p>
                <div class="mt-3">
                    <button class="btn btn-primary me-2" id="startAnalysisBtn">
                        <i class="fas fa-play-circle me-1"></i>开始分析
                    </button>
                    <a href="{{ url_for('v2.chapters_summary', novel_id=novel.id) }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-list-alt me-1"></i>章节分析汇总
                    </a>
                    <button class="btn btn-outline-secondary" id="showContentBtn">
                        <i class="fas fa-book-open me-1"></i>查看原文
                    </button>
                </div>
            </div>
            <div class="text-end">
                <span class="badge bg-{{ 'success' if novel.is_analyzed else 'warning' }} mb-2">
                    {{ '已分析' if novel.is_analyzed else '未分析' }}
                </span>
            </div>
        </div>
    </div>
</div>

<!-- 内容导航 -->
<ul class="nav nav-tabs mb-4" id="novelTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="dimensions-tab" data-bs-toggle="tab" data-bs-target="#dimensions" type="button" role="tab" aria-controls="dimensions" aria-selected="true">
            <i class="fas fa-cubes me-1"></i>整本书的分析维度
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">
            <i class="fas fa-list-ol me-1"></i>章节列表
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="console-tab" data-bs-toggle="tab" data-bs-target="#console" type="button" role="tab" aria-controls="console" aria-selected="false">
            <i class="fas fa-terminal me-1"></i>分析控制台
        </button>
    </li>
</ul>

<!-- 选项卡内容 -->
<div class="tab-content" id="novelTabsContent">
    <!-- 整本书的分析维度选项卡 -->
    <div class="tab-pane fade show active" id="dimensions" role="tabpanel" aria-labelledby="dimensions-tab">
        <div class="card mb-3">
            <div class="card-header bg-light">
                <h3 class="card-title mb-0">整本书的分析维度</h3>
            </div>
        </div>
        <div class="row">
            {% for dimension in dimensions %}
                <div class="col-md-4 col-sm-6 mb-4">
                    <div class="card dimension-card h-100 {{ 'border-primary' if dimension.key in available_dimensions else '' }}"
                         onclick="location.href='{{ url_for('v2.analysis', novel_id=novel.id, dimension=dimension.key) if dimension.key in available_dimensions else '#' }}'">
                        <div class="card-body text-center">
                            <i class="{{ dimension.icon }} dimension-icon {{ 'text-primary' if dimension.key in available_dimensions else 'text-muted' }}"></i>
                            <h4 class="card-title">{{ dimension.name }}</h4>
                            <p class="card-text small">
                                {% if dimension.key in available_dimensions %}
                                    已完成分析
                                {% else %}
                                    未分析
                                {% endif %}
                            </p>
                            {% if dimension.key in available_dimensions %}
                                <a href="{{ url_for('v2.analysis', novel_id=novel.id, dimension=dimension.key) }}" class="btn btn-sm btn-primary">
                                    查看结果
                                </a>
                            {% else %}
                                <button class="btn btn-sm btn-outline-secondary analyze-dimension-btn" data-dimension="{{ dimension.key }}">
                                    开始分析
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

    <!-- 章节列表选项卡 -->
    <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
        {% if chapters %}
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">章节列表</h3>
                        <span class="badge bg-primary">共 {{ chapters|length }} 章</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush chapter-list">
                        {% for chapter in chapters %}
                            <li class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <a href="{{ url_for('v2.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="text-decoration-none">
                                            <h5 class="mb-1">{{ chapter.title or '第' + chapter.chapter_number|string + '章' }}</h5>
                                        </a>
                                        <p class="small text-muted mb-0">{{ chapter.word_count }} 字</p>
                                    </div>
                                    <div>
                                        {% if chapter.id in chapter_analysis_results %}
                                            <span class="badge bg-success me-2">已分析 {{ chapter_analysis_results[chapter.id]|length }} 个维度</span>
                                        {% else %}
                                            <span class="badge bg-warning me-2">未分析</span>
                                        {% endif %}
                                        <a href="{{ url_for('v2.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-sm btn-outline-primary">
                                            查看详情
                                        </a>
                                    </div>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>该小说暂无章节，系统将自动尝试分割章节。
            </div>
            <div class="text-center">
                <button id="splitChaptersBtn" class="btn btn-primary">
                    <i class="fas fa-cut me-1"></i>分割章节
                </button>
            </div>
        {% endif %}
    </div>

    <!-- 分析控制台选项卡 -->
    <div class="tab-pane fade" id="console" role="tabpanel" aria-labelledby="console-tab">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">分析控制台</h3>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h4>分析设置</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="enableReasoningSwitch" checked>
                                <label class="form-check-label" for="enableReasoningSwitch">显示推理过程</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="forceReanalysisSwitch">
                                <label class="form-check-label" for="forceReanalysisSwitch">强制重新分析</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h4>批量分析</h4>
                    <div class="mb-3">
                        <label class="form-label">选择整本书的分析维度</label>
                        <div class="row">
                            {% for dimension in dimensions %}
                                <div class="col-md-4 col-sm-6">
                                    <div class="form-check">
                                        <input class="form-check-input dimension-checkbox" type="checkbox" value="{{ dimension.key }}" id="dimension{{ loop.index }}">
                                        <label class="form-check-label" for="dimension{{ loop.index }}">
                                            {{ dimension.name }}
                                        </label>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="d-flex">
                        <button id="selectAllDimensionsBtn" class="btn btn-sm btn-outline-secondary me-2">全选</button>
                        <button id="deselectAllDimensionsBtn" class="btn btn-sm btn-outline-secondary me-2">取消全选</button>
                        <button id="batchAnalysisBtn" class="btn btn-primary">开始批量分析</button>
                    </div>
                </div>

                <div>
                    <h4>分析日志</h4>
                    <div class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto;" id="analysisLog">
                        <div class="text-muted">等待分析操作...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 查看原文模态框 -->
<div class="modal fade" id="contentModal" tabindex="-1" aria-labelledby="contentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentModalLabel">{{ novel.title }} - 原文</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre style="white-space: pre-wrap; font-family: 'Microsoft YaHei', sans-serif;">{{ novel.content }}</pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 查看原文按钮
        document.getElementById('showContentBtn').addEventListener('click', function() {
            const contentModal = new bootstrap.Modal(document.getElementById('contentModal'));
            contentModal.show();
        });

        // 全选/取消全选维度
        document.getElementById('selectAllDimensionsBtn').addEventListener('click', function() {
            document.querySelectorAll('.dimension-checkbox').forEach(checkbox => {
                checkbox.checked = true;
            });
        });

        document.getElementById('deselectAllDimensionsBtn').addEventListener('click', function() {
            document.querySelectorAll('.dimension-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
        });

        // 开始分析按钮
        document.getElementById('startAnalysisBtn').addEventListener('click', function() {
            // 切换到分析控制台选项卡
            const consoleTab = document.getElementById('console-tab');
            try {
                // 使用Bootstrap 5的方式激活选项卡
                const tab = new bootstrap.Tab(consoleTab);
                tab.show();
            } catch (e) {
                // 如果出错，使用点击方式激活选项卡
                consoleTab.click();
            }

            // 全选所有维度
            document.getElementById('selectAllDimensionsBtn').click();
        });

        // 批量分析按钮
        document.getElementById('batchAnalysisBtn').addEventListener('click', function() {
            const selectedDimensions = [];
            document.querySelectorAll('.dimension-checkbox:checked').forEach(checkbox => {
                selectedDimensions.push(checkbox.value);
            });

            if (selectedDimensions.length === 0) {
                alert('请至少选择一个分析维度');
                return;
            }

            const forceReanalysis = document.getElementById('forceReanalysisSwitch').checked;
            const enableReasoning = document.getElementById('enableReasoningSwitch').checked;

            // 添加日志
            const logElement = document.getElementById('analysisLog');
            logElement.innerHTML = `<div class="text-success">开始分析，已选择 ${selectedDimensions.length} 个维度...</div>`;

            // 发送API请求
            fetch(`/api/novel/{{ novel.id }}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dimensions: selectedDimensions,
                    force_reanalysis: forceReanalysis,
                    enable_reasoning: enableReasoning
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    logElement.innerHTML += `<div class="text-success">分析请求已提交，正在处理...</div>`;

                    // 显示每个维度的分析结果
                    data.results.forEach(result => {
                        const statusClass = result.success ? 'text-success' : 'text-danger';
                        const statusText = result.success ? '成功' : '失败';
                        logElement.innerHTML += `<div class="${statusClass}">维度 ${result.dimension} 分析${statusText}</div>`;
                    });

                    logElement.innerHTML += `<div class="text-success">分析完成，请刷新页面查看结果</div>`;

                    // 添加刷新按钮
                    logElement.innerHTML += `<div class="mt-3"><button class="btn btn-sm btn-primary" onclick="location.reload()">刷新页面</button></div>`;
                } else {
                    logElement.innerHTML += `<div class="text-danger">分析请求失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                logElement.innerHTML += `<div class="text-danger">发送请求时出错: ${error.message}</div>`;
            });
        });

        // 单个维度分析按钮
        document.querySelectorAll('.analyze-dimension-btn').forEach(button => {
            button.addEventListener('click', function(event) {
                event.stopPropagation();
                const dimension = this.getAttribute('data-dimension');

                // 切换到分析控制台选项卡
                const consoleTab = document.getElementById('console-tab');
                try {
                    // 使用Bootstrap 5的方式激活选项卡
                    const tab = new bootstrap.Tab(consoleTab);
                    tab.show();
                } catch (e) {
                    // 如果出错，使用点击方式激活选项卡
                    consoleTab.click();
                }

                // 取消全选
                document.getElementById('deselectAllDimensionsBtn').click();

                // 选中对应维度
                document.querySelectorAll('.dimension-checkbox').forEach(checkbox => {
                    if (checkbox.value === dimension) {
                        checkbox.checked = true;
                    }
                });

                // 触发批量分析
                document.getElementById('batchAnalysisBtn').click();
            });
        });

        // 分割章节按钮
        const splitChaptersBtn = document.getElementById('splitChaptersBtn');
        if (splitChaptersBtn) {
            splitChaptersBtn.addEventListener('click', function() {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>正在分割章节...';

                // 发送API请求
                fetch(`/api/novel/{{ novel.id }}/split_chapters`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(`分割章节失败: ${data.error}`);
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-cut me-1"></i>分割章节';
                    }
                })
                .catch(error => {
                    alert(`发送请求时出错: ${error.message}`);
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-cut me-1"></i>分割章节';
                });
            });
        }
    });
</script>
{% endblock %}
