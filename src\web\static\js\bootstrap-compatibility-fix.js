/**
 * 九猫 - Bootstrap兼容性修复脚本
 * 解决Bootstrap 4和Bootstrap 5之间的兼容性问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('Bootstrap兼容性修复脚本已加载');

    // 检测Bootstrap版本
    let bootstrapVersion = 0;
    if (typeof bootstrap !== 'undefined') {
        bootstrapVersion = 5; // Bootstrap 5
        console.log('检测到Bootstrap 5');
    } else if (typeof $ !== 'undefined' && typeof $.fn.collapse !== 'undefined') {
        bootstrapVersion = 4; // Bootstrap 4
        console.log('检测到Bootstrap 4');
    } else {
        console.log('未检测到Bootstrap，将尝试加载');
    }

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复Bootstrap兼容性问题');
        
        // 确保Bootstrap已加载
        ensureBootstrapLoaded();
        
        // 修复数据属性
        fixDataAttributes();
        
        // 修复组件初始化
        fixComponentInitialization();
    });
    
    // 确保Bootstrap已加载
    function ensureBootstrapLoaded() {
        try {
            // 检查是否已加载Bootstrap
            if (bootstrapVersion > 0) {
                console.log('Bootstrap已加载，版本: ' + bootstrapVersion);
                return;
            }
            
            console.log('尝试加载Bootstrap');
            
            // 检查jQuery是否已加载
            if (typeof jQuery === 'undefined') {
                console.log('加载jQuery');
                const jqueryScript = document.createElement('script');
                jqueryScript.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js';
                document.head.appendChild(jqueryScript);
                
                // 等待jQuery加载完成
                jqueryScript.onload = function() {
                    console.log('jQuery加载完成');
                    
                    // 加载Bootstrap
                    loadBootstrap();
                };
            } else {
                // 直接加载Bootstrap
                loadBootstrap();
            }
        } catch (e) {
            console.error('确保Bootstrap已加载时出错:', e);
        }
    }
    
    // 加载Bootstrap
    function loadBootstrap() {
        try {
            // 检查是否已加载Bootstrap JS
            if (document.querySelector('script[src*="bootstrap.bundle.min.js"]') || typeof bootstrap !== 'undefined') {
                console.log('Bootstrap已加载');
                bootstrapVersion = 5;
                fixDataAttributes();
                fixComponentInitialization();
                return;
            }
            
            // 检查是否已加载Bootstrap CSS
            const bootstrapCssLoaded = Array.from(document.styleSheets).some(sheet => 
                sheet.href && sheet.href.includes('bootstrap')
            );
            
            if (!bootstrapCssLoaded) {
                console.log('加载Bootstrap CSS');
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = '/static/css/lib/bootstrap.min.css';
                
                link.onerror = function() {
                    console.log('本地Bootstrap CSS加载失败，尝试备用路径');
                    const backupLink = document.createElement('link');
                    backupLink.rel = 'stylesheet';
                    backupLink.href = '/direct-static/css/lib/bootstrap.min.css';
                    document.head.appendChild(backupLink);
                };
                
                document.head.appendChild(link);
            }
            
            // 加载Bootstrap JS
            console.log('加载Bootstrap JS');
            const bootstrapScript = document.createElement('script');
            bootstrapScript.src = '/static/js/lib/bootstrap.bundle.min.js';
            
            // 等待Bootstrap加载完成
            bootstrapScript.onload = function() {
                console.log('Bootstrap加载成功');
                bootstrapVersion = 5;
                
                // 修复数据属性
                fixDataAttributes();
                
                // 修复组件初始化
                fixComponentInitialization();
            };
            
            bootstrapScript.onerror = function() {
                console.log('本地Bootstrap JS加载失败，尝试备用路径');
                
                const backupScript = document.createElement('script');
                backupScript.src = '/direct-static/js/lib/bootstrap.bundle.min.js';
                
                backupScript.onload = function() {
                    console.log('从备用路径加载Bootstrap成功');
                    bootstrapVersion = 5;
                    fixDataAttributes();
                    fixComponentInitialization();
                };
                
                backupScript.onerror = function() {
                    console.log('备用路径加载失败，尝试独立文件');
                    
                    // 尝试加载独立的Bootstrap JS文件
                    const popper = document.createElement('script');
                    popper.src = '/static/js/lib/popper.min.js';
                    
                    popper.onload = function() {
                        const bootstrapCore = document.createElement('script');
                        bootstrapCore.src = '/static/js/lib/bootstrap.min.js';
                        
                        bootstrapCore.onload = function() {
                            console.log('通过独立文件加载Bootstrap成功');
                            bootstrapVersion = 5;
                            fixDataAttributes();
                            fixComponentInitialization();
                        };
                        
                        document.head.appendChild(bootstrapCore);
                    };
                    
                    document.head.appendChild(popper);
                };
                
                document.head.appendChild(backupScript);
            };
            
            document.head.appendChild(bootstrapScript);
        } catch (e) {
            console.error('加载Bootstrap时出错:', e);
        }
    }
    
    // 修复数据属性
    function fixDataAttributes() {
        try {
            if (bootstrapVersion === 5) {
                // 从Bootstrap 4升级到Bootstrap 5
                
                // 修复折叠组件
                document.querySelectorAll('[data-toggle="collapse"]').forEach(function(element) {
                    if (!element.hasAttribute('data-bs-toggle')) {
                        element.setAttribute('data-bs-toggle', 'collapse');
                        console.log('将data-toggle="collapse"转换为data-bs-toggle="collapse"');
                        
                        // 更新目标属性
                        if (element.hasAttribute('data-target')) {
                            const target = element.getAttribute('data-target');
                            element.setAttribute('data-bs-target', target);
                        }
                    }
                });
                
                // 修复工具提示
                document.querySelectorAll('[data-toggle="tooltip"]').forEach(function(element) {
                    if (!element.hasAttribute('data-bs-toggle')) {
                        element.setAttribute('data-bs-toggle', 'tooltip');
                        console.log('将data-toggle="tooltip"转换为data-bs-toggle="tooltip"');
                    }
                });
                
                // 修复弹出框
                document.querySelectorAll('[data-toggle="popover"]').forEach(function(element) {
                    if (!element.hasAttribute('data-bs-toggle')) {
                        element.setAttribute('data-bs-toggle', 'popover');
                        console.log('将data-toggle="popover"转换为data-bs-toggle="popover"');
                    }
                });
                
                // 修复下拉菜单
                document.querySelectorAll('[data-toggle="dropdown"]').forEach(function(element) {
                    if (!element.hasAttribute('data-bs-toggle')) {
                        element.setAttribute('data-bs-toggle', 'dropdown');
                        console.log('将data-toggle="dropdown"转换为data-bs-toggle="dropdown"');
                    }
                });
                
                // 修复标签页
                document.querySelectorAll('[data-toggle="tab"]').forEach(function(element) {
                    if (!element.hasAttribute('data-bs-toggle')) {
                        element.setAttribute('data-bs-toggle', 'tab');
                        console.log('将data-toggle="tab"转换为data-bs-toggle="tab"');
                    }
                });
            } else if (bootstrapVersion === 4) {
                // 从Bootstrap 5降级到Bootstrap 4
                
                // 修复折叠组件
                document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(function(element) {
                    if (!element.hasAttribute('data-toggle')) {
                        element.setAttribute('data-toggle', 'collapse');
                        console.log('将data-bs-toggle="collapse"转换为data-toggle="collapse"');
                        
                        // 更新目标属性
                        if (element.hasAttribute('data-bs-target')) {
                            const target = element.getAttribute('data-bs-target');
                            element.setAttribute('data-target', target);
                        }
                    }
                });
                
                // 修复工具提示
                document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(function(element) {
                    if (!element.hasAttribute('data-toggle')) {
                        element.setAttribute('data-toggle', 'tooltip');
                        console.log('将data-bs-toggle="tooltip"转换为data-toggle="tooltip"');
                    }
                });
                
                // 修复弹出框
                document.querySelectorAll('[data-bs-toggle="popover"]').forEach(function(element) {
                    if (!element.hasAttribute('data-toggle')) {
                        element.setAttribute('data-toggle', 'popover');
                        console.log('将data-bs-toggle="popover"转换为data-toggle="popover"');
                    }
                });
                
                // 修复下拉菜单
                document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(function(element) {
                    if (!element.hasAttribute('data-toggle')) {
                        element.setAttribute('data-toggle', 'dropdown');
                        console.log('将data-bs-toggle="dropdown"转换为data-toggle="dropdown"');
                    }
                });
                
                // 修复标签页
                document.querySelectorAll('[data-bs-toggle="tab"]').forEach(function(element) {
                    if (!element.hasAttribute('data-toggle')) {
                        element.setAttribute('data-toggle', 'tab');
                        console.log('将data-bs-toggle="tab"转换为data-toggle="tab"');
                    }
                });
            }
        } catch (e) {
            console.error('修复数据属性时出错:', e);
        }
    }
    
    // 修复组件初始化
    function fixComponentInitialization() {
        try {
            if (bootstrapVersion === 5) {
                // Bootstrap 5 组件初始化
                
                // 初始化工具提示
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                    tooltipTriggerList.map(function(tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                    console.log('初始化Bootstrap 5工具提示');
                }
                
                // 初始化弹出框
                const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
                    popoverTriggerList.map(function(popoverTriggerEl) {
                        return new bootstrap.Popover(popoverTriggerEl);
                    });
                    console.log('初始化Bootstrap 5弹出框');
                }
                
                // 为折叠组件添加事件处理程序
                document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(function(element) {
                    if (!element._hasCollapseHandler) {
                        element.addEventListener('click', function(event) {
                            event.preventDefault();
                            
                            // 获取目标元素
                            const targetSelector = element.getAttribute('data-bs-target');
                            if (!targetSelector) {
                                return;
                            }
                            
                            const targetElement = document.querySelector(targetSelector);
                            if (!targetElement) {
                                return;
                            }
                            
                            // 使用Bootstrap 5 API
                            if (typeof bootstrap !== 'undefined' && bootstrap.Collapse) {
                                const collapse = bootstrap.Collapse.getInstance(targetElement);
                                if (collapse) {
                                    collapse.toggle();
                                } else {
                                    new bootstrap.Collapse(targetElement, {
                                        toggle: true
                                    });
                                }
                            } else {
                                // 手动切换
                                targetElement.classList.toggle('show');
                            }
                        });
                        
                        element._hasCollapseHandler = true;
                    }
                });
            } else if (bootstrapVersion === 4) {
                // Bootstrap 4 组件初始化
                
                // 初始化工具提示
                if (typeof $ !== 'undefined') {
                    $('[data-toggle="tooltip"]').tooltip();
                    console.log('初始化Bootstrap 4工具提示');
                    
                    // 初始化弹出框
                    $('[data-toggle="popover"]').popover();
                    console.log('初始化Bootstrap 4弹出框');
                    
                    // 初始化折叠组件
                    $('.collapse').collapse({
                        toggle: false
                    });
                    console.log('初始化Bootstrap 4折叠组件');
                }
            }
        } catch (e) {
            console.error('修复组件初始化时出错:', e);
        }
    }
    
    // 添加全局辅助函数
    window.bootstrapToggleCollapse = function(targetId) {
        try {
            const targetElement = document.getElementById(targetId);
            if (!targetElement) {
                console.warn('找不到折叠目标元素: ' + targetId);
                return;
            }
            
            console.log('切换折叠状态: ' + targetId);
            
            if (bootstrapVersion === 5) {
                // Bootstrap 5
                if (typeof bootstrap !== 'undefined' && bootstrap.Collapse) {
                    const collapse = bootstrap.Collapse.getInstance(targetElement);
                    if (collapse) {
                        collapse.toggle();
                    } else {
                        new bootstrap.Collapse(targetElement, {
                            toggle: true
                        });
                    }
                } else {
                    // 手动切换
                    targetElement.classList.toggle('show');
                }
            } else if (bootstrapVersion === 4) {
                // Bootstrap 4
                if (typeof $ !== 'undefined') {
                    $('#' + targetId).collapse('toggle');
                } else {
                    // 手动切换
                    targetElement.classList.toggle('show');
                }
            } else {
                // 手动切换
                targetElement.classList.toggle('show');
            }
        } catch (e) {
            console.error('切换折叠状态时出错:', e);
        }
    };
})();
