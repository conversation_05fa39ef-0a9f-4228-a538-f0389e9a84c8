/**
 * 九猫小说分析写作系统 - jQuery本地加载器
 * 
 * 此脚本用于确保jQuery从本地加载，不依赖CDN
 * 版本: 1.0.0
 */

(function() {
    console.log('[jQuery本地加载器] 初始化...');
    
    // 检查jQuery是否已加载
    function checkJQuery() {
        if (typeof jQuery !== 'undefined') {
            console.log('[jQuery本地加载器] jQuery已加载，版本:', jQuery.fn.jquery);
            
            // 确保$ 变量可用
            if (typeof $ === 'undefined') {
                console.log('[jQuery本地加载器] $ 变量未定义，设置 $ = jQuery');
                window.$ = jQuery;
            }
            
            // 触发jQuery加载完成事件
            const event = new Event('jQueryLoaded');
            document.dispatchEvent(event);
            
            return true;
        }
        
        return false;
    }
    
    // 从本地加载jQuery
    function loadLocalJQuery() {
        console.log('[jQuery本地加载器] 尝试从本地加载jQuery...');
        
        // 创建script元素
        const script = document.createElement('script');
        script.src = '/static/js/lib/jquery.min.js';
        
        script.onload = function() {
            console.log('[jQuery本地加载器] jQuery从本地加载成功!');
            
            // 确保$ 变量可用
            window.$ = jQuery;
            
            // 触发jQuery加载完成事件
            const event = new Event('jQueryLoaded');
            document.dispatchEvent(event);
        };
        
        script.onerror = function() {
            console.error('[jQuery本地加载器] 从本地加载jQuery失败，尝试从CDN加载');
            loadFromCDN();
        };
        
        document.head.appendChild(script);
    }
    
    // 从CDN加载jQuery
    function loadFromCDN() {
        console.log('[jQuery本地加载器] 尝试从CDN加载jQuery...');
        
        // 创建script元素
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';
        
        script.onload = function() {
            console.log('[jQuery本地加载器] jQuery从CDN加载成功!');
            
            // 确保$ 变量可用
            window.$ = jQuery;
            
            // 触发jQuery加载完成事件
            const event = new Event('jQueryLoaded');
            document.dispatchEvent(event);
        };
        
        script.onerror = function() {
            console.error('[jQuery本地加载器] 从CDN加载jQuery失败，尝试备用CDN');
            
            // 尝试备用CDN
            const backupScript = document.createElement('script');
            backupScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js';
            
            backupScript.onload = function() {
                console.log('[jQuery本地加载器] jQuery从备用CDN加载成功!');
                
                // 确保$ 变量可用
                window.$ = jQuery;
                
                // 触发jQuery加载完成事件
                const event = new Event('jQueryLoaded');
                document.dispatchEvent(event);
            };
            
            backupScript.onerror = function() {
                console.error('[jQuery本地加载器] 所有jQuery加载尝试均失败');
                
                // 创建内联jQuery
                createInlineJQuery();
            };
            
            document.head.appendChild(backupScript);
        };
        
        document.head.appendChild(script);
    }
    
    // 创建内联jQuery (最小版本)
    function createInlineJQuery() {
        console.log('[jQuery本地加载器] 尝试创建内联jQuery...');
        
        // 创建script元素
        const script = document.createElement('script');
        script.textContent = `
            // 极简jQuery替代品
            window.jQuery = function(selector) {
                if (typeof selector === 'function') {
                    if (document.readyState !== 'loading') {
                        selector();
                    } else {
                        document.addEventListener('DOMContentLoaded', selector);
                    }
                    return;
                }
                
                const elements = typeof selector === 'string' 
                    ? document.querySelectorAll(selector)
                    : [selector];
                
                return {
                    elements: elements,
                    length: elements.length,
                    each: function(callback) {
                        for (let i = 0; i < this.elements.length; i++) {
                            callback.call(this.elements[i], i, this.elements[i]);
                        }
                        return this;
                    },
                    html: function(content) {
                        if (content === undefined) {
                            return this.elements[0] ? this.elements[0].innerHTML : '';
                        }
                        this.each(function() {
                            this.innerHTML = content;
                        });
                        return this;
                    }
                };
            };
            
            // 设置版本和fn
            jQuery.fn = {};
            jQuery.fn.jquery = '1.0.0-minimal';
            
            // 设置$别名
            window.$ = jQuery;
            
            console.log('[jQuery本地加载器] 已创建内联jQuery替代品');
        `;
        
        document.head.appendChild(script);
        
        // 触发jQuery加载完成事件
        const event = new Event('jQueryLoaded');
        document.dispatchEvent(event);
    }
    
    // 导出全局函数，以便其他脚本可以使用
    window.ensureJQuery = function(callback) {
        if (checkJQuery()) {
            if (callback && typeof callback === 'function') {
                callback(jQuery);
            }
            return true;
        } else {
            // 添加事件监听器，等待jQuery加载完成
            document.addEventListener('jQueryLoaded', function() {
                if (callback && typeof callback === 'function') {
                    callback(jQuery);
                }
            });
            
            // 加载jQuery
            loadLocalJQuery();
            return false;
        }
    };
    
    // 立即检查jQuery
    if (!checkJQuery()) {
        // 如果jQuery未加载，尝试加载
        loadLocalJQuery();
    }
    
    console.log('[jQuery本地加载器] 初始化完成');
})();
