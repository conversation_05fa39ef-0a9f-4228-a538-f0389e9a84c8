/**
 * 九猫 - 语言风格分析过程修复脚本
 * 专门修复language_style维度的分析过程提取问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('语言风格分析过程修复脚本已加载 v1.0.0');
    
    // 监听DOM加载完成事件
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否在语言风格分析过程页面
        if (isLanguageStyleProcessPage()) {
            console.log('检测到语言风格分析过程页面，应用特殊修复');
            
            // 延迟执行以确保其他脚本已加载
            setTimeout(applyLanguageStyleProcessFix, 1000);
        }
    });
    
    // 检查是否在语言风格分析过程页面
    function isLanguageStyleProcessPage() {
        const pathname = window.location.pathname;
        return pathname.includes('/process') && pathname.includes('language_style');
    }
    
    // 应用语言风格分析过程修复
    function applyLanguageStyleProcessFix() {
        console.log('应用语言风格分析过程修复');
        
        // 重写loadProcessContent函数，添加特殊处理
        if (window.loadProcessContent) {
            const originalLoadProcessContent = window.loadProcessContent;
            
            window.loadProcessContent = function() {
                console.log('使用增强版loadProcessContent函数');
                
                // 获取小说ID和维度
                const novelId = getNovelId();
                const dimension = getDimension();
                
                if (!novelId || !dimension) {
                    console.error('无法获取小说ID或维度');
                    // 调用原始函数
                    originalLoadProcessContent();
                    return;
                }
                
                // 显示加载指示器
                const contentElement = document.getElementById('processContent');
                if (!contentElement) {
                    console.error('找不到processContent元素');
                    return;
                }
                
                contentElement.innerHTML = `
                    <div class="loading-indicator">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载分析过程内容，请稍候...</p>
                    </div>
                `;
                
                // 直接获取分析结果内容
                fetch(`/api/novel/${novelId}/analysis/${dimension}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.result && data.result.content) {
                            console.log('成功获取到分析结果内容');
                            
                            // 尝试从内容中提取分析过程
                            const processContent = extractProcessFromContent(data.result.content);
                            
                            if (processContent) {
                                console.log('成功从内容中提取到分析过程');
                                
                                // 构建Markdown内容
                                const novel = window.processViewerConfig.novel || { title: '未知小说' };
                                
                                let content = `# ${novel.title} - ${dimension} 分析过程\n\n`;
                                content += "## 分析摘要\n\n";
                                if (novel.title) content += `- **小说标题**: ${novel.title}\n`;
                                if (novel.author) content += `- **作者**: ${novel.author}\n`;
                                content += `- **分析维度**: ${dimension}\n`;
                                if (novel.word_count) content += `- **总字数**: ${novel.word_count}\n\n`;
                                
                                // 添加提示
                                content += `<div class="alert alert-info">
                                    <strong>注意：</strong> 由于原始分析过程无法提取，显示的是从分析结果中重建的分析过程。
                                </div>\n\n`;
                                
                                content += "## 分析过程\n\n";
                                content += processContent;
                                
                                // 保存内容
                                window.processViewerConfig.processContent = content;
                                
                                // 渲染内容
                                if (window.renderProcessContent) {
                                    window.renderProcessContent();
                                } else {
                                    contentElement.innerHTML = content;
                                }
                            } else {
                                console.log('无法从内容中提取分析过程，使用原始函数');
                                // 如果无法提取，使用原始函数
                                originalLoadProcessContent();
                            }
                        } else {
                            console.log('获取分析结果失败，使用原始函数');
                            // 如果获取失败，使用原始函数
                            originalLoadProcessContent();
                        }
                    })
                    .catch(error => {
                        console.error('获取分析结果时出错:', error);
                        // 出错时使用原始函数
                        originalLoadProcessContent();
                    });
            };
        }
        
        // 如果已经在内容视图，立即加载内容
        const contentView = document.getElementById('contentView');
        if (contentView && contentView.style.display === 'block') {
            console.log('已在内容视图，立即加载内容');
            if (window.loadProcessContent) {
                window.loadProcessContent();
            }
        }
    }
    
    // 从内容中提取分析过程
    function extractProcessFromContent(content) {
        if (!content) return null;
        
        console.log('尝试从内容中提取分析过程');
        
        // 检查是否包含分析过程的特征
        const processMarkers = [
            '分析过程',
            '首先，我需要',
            '我将分析',
            '我需要分析',
            '嗯，用户让我',
            '好的，我现在要',
            '下面我来分析'
        ];
        
        // 检查是否包含任何分析过程标记
        let hasProcessMarker = false;
        let markerFound = '';
        
        for (const marker of processMarkers) {
            if (content.includes(marker)) {
                hasProcessMarker = true;
                markerFound = marker;
                break;
            }
        }
        
        if (hasProcessMarker) {
            console.log(`找到分析过程标记: ${markerFound}`);
            
            // 如果包含"分析过程"标记，尝试提取该部分
            if (content.includes('分析过程')) {
                const processStart = content.indexOf('分析过程');
                // 查找下一个部分
                const nextSections = ['分析结果', '## 结论', '# 结果', '# 分析结果', '## 分析结果'];
                let processEnd = -1;
                
                for (const section of nextSections) {
                    const pos = content.indexOf(section, processStart);
                    if (pos !== -1 && (processEnd === -1 || pos < processEnd)) {
                        processEnd = pos;
                    }
                }
                
                // 提取分析过程部分
                if (processEnd !== -1) {
                    return content.substring(processStart, processEnd);
                } else {
                    return content.substring(processStart);
                }
            }
            
            // 如果包含其他分析过程标记，尝试提取从标记到结果部分的内容
            const startIdx = content.indexOf(markerFound);
            const endMarkers = ['分析结果', '## 结论', '# 结果', '# 分析结果', '## 分析结果', '\n\n1. ', '\n\n总结'];
            let endIdx = -1;
            
            for (const marker of endMarkers) {
                const pos = content.indexOf(marker, startIdx);
                if (pos !== -1 && (endIdx === -1 || pos < endIdx)) {
                    endIdx = pos;
                }
            }
            
            // 提取分析过程部分
            if (endIdx !== -1) {
                return content.substring(startIdx, endIdx);
            } else {
                return content.substring(startIdx);
            }
        }
        
        // 如果没有找到分析过程标记，但内容不是以列表或标题开始，可能整个内容就是分析过程
        if (!content.startsWith('1.') && !content.startsWith('#') && !content.startsWith('-')) {
            console.log('内容可能整个就是分析过程');
            return content;
        }
        
        // 如果无法提取分析过程，生成一个模拟的分析过程
        console.log('无法提取分析过程，生成模拟分析过程');
        
        return `
## 语言风格分析过程

在分析这篇小说的语言风格时，我需要关注以下几个方面：

1. **词汇选择和用词特点**：作者使用的词汇类型、专业术语、方言或特殊表达等
2. **句式结构**：句子长短、复杂程度、句式变化等
3. **修辞手法**：比喻、拟人、夸张等修辞手法的使用
4. **语气和语调**：叙述的语气、对话的语调等
5. **节奏感**：文本的节奏快慢、抑扬顿挫等

通过分析文本中的这些元素，我可以归纳出作者的语言风格特点，并了解这些风格如何服务于故事主题和情感表达。

以下是我对文本进行的详细分析：

${content.substring(0, 200)}...

[注意：这是系统生成的模拟分析过程，因为无法从原始结果中提取真实的分析过程。]
`;
    }
    
    // 获取小说ID
    function getNovelId() {
        // 从URL中获取
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        if (match && match[1]) {
            return match[1];
        }
        
        // 从processViewerConfig中获取
        if (window.processViewerConfig && window.processViewerConfig.novelId) {
            return window.processViewerConfig.novelId;
        }
        
        return null;
    }
    
    // 获取维度
    function getDimension() {
        // 从URL中获取
        const match = window.location.pathname.match(/\/analysis\/([^\/]+)/);
        if (match && match[1]) {
            return match[1];
        }
        
        // 从processViewerConfig中获取
        if (window.processViewerConfig && window.processViewerConfig.dimension) {
            return window.processViewerConfig.dimension;
        }
        
        return null;
    }
})();
