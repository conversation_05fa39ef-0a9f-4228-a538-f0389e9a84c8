/**
 * 九猫 - HTML响应JSON解析修复脚本
 * 专门处理HTML响应被错误地当作JSON解析的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._htmlResponseFixLoaded) {
        console.log('HTML响应修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._htmlResponseFixLoaded = true;
    
    console.log('HTML响应修复脚本已加载 - 版本1.0.0');
    
    // 保存原始的JSON.parse方法
    var originalJSONParse = JSON.parse;
    
    // 防止递归调用的标志
    var isFixing = false;
    
    // 重写JSON.parse方法
    JSON.parse = function(text, reviver) {
        // 如果已经在修复过程中，直接使用原始方法避免递归
        if (isFixing) {
            return originalJSONParse.call(JSO<PERSON>, text, reviver);
        }
        
        try {
            // 尝试使用原始方法解析
            return originalJSONParse.call(JSO<PERSON>, text, reviver);
        } catch (e) {
            // 设置修复标志，防止递归
            isFixing = true;
            
            try {
                // 检查是否是HTML响应错误
                // 例如: "Unexpected token '<', "<!DOCTYPE "... is not valid JSON"
                if (e.message && (e.message.includes("Unexpected token '<'") || 
                                 e.message.includes('<!DOCTYPE') || 
                                 e.message.includes('is not valid JSON'))) {
                    
                    console.log('检测到HTML响应被当作JSON解析的错误:', e.message);
                    
                    // 如果是HTML响应，说明后端可能返回了错误页面而不是JSON数据
                    // 这里我们返回一个带有错误信息的空对象，避免前端代码崩溃
                    console.warn('修复失败，返回空对象');
                    isFixing = false;
                    return {
                        "error": "接收到HTML响应而非JSON数据",
                        "html_response": true,
                        "message": "服务器可能返回了错误页面，请检查网络请求或服务器日志",
                        "original_error": e.message
                    };
                }
                
                // 如果是其他JSON解析错误，重新抛出
                throw e;
            } finally {
                // 无论成功与否，重置修复标志
                isFixing = false;
            }
        }
    };
    
    // 监听全局AJAX请求错误
    document.addEventListener('ajaxError', function(event) {
        try {
            var response = event.detail && event.detail.response;
            
            // 检查响应是否是HTML而非JSON
            if (response && typeof response === 'string' && 
                (response.trim().startsWith('<!DOCTYPE') || response.trim().startsWith('<html'))) {
                
                console.warn('AJAX请求收到HTML响应而非JSON数据');
                
                // 创建一个带错误信息的响应对象
                event.detail.parsedResponse = {
                    "error": "接收到HTML响应而非JSON数据",
                    "html_response": true,
                    "message": "服务器可能返回了错误页面，请检查网络请求或服务器日志"
                };
                
                // 阻止错误继续传播
                event.preventDefault();
            }
        } catch (e) {
            console.error('处理AJAX错误事件时出错:', e);
        }
    }, true);
    
    // 修复fetch API
    if (window.fetch) {
        var originalFetch = window.fetch;
        
        window.fetch = function() {
            return originalFetch.apply(this, arguments)
                .then(function(response) {
                    // 保存原始的json方法
                    var originalJson = response.json;
                    
                    // 重写json方法
                    response.json = function() {
                        return originalJson.call(this)
                            .catch(function(e) {
                                // 检查是否是HTML响应错误
                                if (e.message && (e.message.includes("Unexpected token '<'") || 
                                                 e.message.includes('<!DOCTYPE') || 
                                                 e.message.includes('is not valid JSON'))) {
                                    
                                    console.warn('Fetch API收到HTML响应而非JSON数据');
                                    
                                    // 返回带有错误信息的对象
                                    return {
                                        "error": "接收到HTML响应而非JSON数据",
                                        "html_response": true,
                                        "message": "服务器可能返回了错误页面，请检查网络请求或服务器日志",
                                        "original_error": e.message
                                    };
                                }
                                
                                // 如果是其他错误，继续抛出
                                throw e;
                            });
                    };
                    
                    return response;
                });
        };
    }
    
    // 修复jQuery的ajax方法(如果存在)
    if (window.jQuery) {
        var originalAjax = jQuery.ajax;
        
        jQuery.ajax = function(url, options) {
            // 规范化参数
            if (typeof url === 'object') {
                options = url;
                url = undefined;
            }
            
            options = options || {};
            
            // 保存原始的success回调
            var originalSuccess = options.success;
            
            // 添加数据类型验证
            if (options.dataType === 'json') {
                options.success = function(data, textStatus, jqXHR) {
                    // 如果返回的是HTML而非JSON
                    if (typeof data === 'string' && 
                        (data.trim().startsWith('<!DOCTYPE') || data.trim().startsWith('<html'))) {
                        
                        console.warn('jQuery AJAX请求收到HTML响应而非JSON数据');
                        
                        // 替换为带错误信息的对象
                        data = {
                            "error": "接收到HTML响应而非JSON数据",
                            "html_response": true,
                            "message": "服务器可能返回了错误页面，请检查网络请求或服务器日志"
                        };
                    }
                    
                    // 调用原始的success回调
                    if (originalSuccess) {
                        originalSuccess.call(this, data, textStatus, jqXHR);
                    }
                };
            }
            
            // 调用原始的ajax方法
            return originalAjax.call(jQuery, url, options);
        };
    }
    
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && 
            (event.error.message.includes("Unexpected token '<'") || 
             event.error.message.includes('<!DOCTYPE') || 
             event.error.message.includes('is not valid JSON'))) {
            
            console.log('捕获到HTML响应JSON解析错误，已被处理');
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
    
    console.log('HTML响应修复脚本初始化完成');
})(); 