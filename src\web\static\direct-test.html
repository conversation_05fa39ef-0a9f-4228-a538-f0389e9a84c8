<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫 - 直接测试页面</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007bff;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0069d9;
        }
        .status-box {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>九猫直接测试页面</h1>
        <p>这是一个不依赖任何外部资源的测试页面，用于检查基本的HTML和CSS渲染是否正常工作。</p>
        
        <div class="status-box">
            <h2>页面状态</h2>
            <p id="load-status" class="success">页面HTML已成功加载！</p>
            <p id="css-status" class="success">内联CSS样式已应用！</p>
            <p id="js-status">JavaScript状态检查中...</p>
        </div>
        
        <div>
            <h2>操作</h2>
            <a href="/" class="btn">返回首页</a>
            <button onclick="testJavaScript()" class="btn">测试JavaScript</button>
            <button onclick="checkResources()" class="btn">检查资源</button>
            <button onclick="location.reload()" class="btn">刷新页面</button>
        </div>

        <div class="status-box" id="resource-status">
            <h2>资源检查</h2>
            <p>点击"检查资源"按钮查看资源加载状态</p>
        </div>
    </div>

    <script>
        // 简单的JavaScript测试函数
        function testJavaScript() {
            document.getElementById('js-status').textContent = 'JavaScript正常工作！';
            document.getElementById('js-status').className = 'success';
            alert('JavaScript正常工作！');
        }
        
        // 检查资源加载状态
        function checkResources() {
            const resourceStatus = document.getElementById('resource-status');
            resourceStatus.innerHTML = '<h2>资源检查</h2>';
            
            // 检查CSS
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            resourceStatus.innerHTML += `<p>CSS文件: ${cssLinks.length}个</p>`;
            cssLinks.forEach((link, index) => {
                resourceStatus.innerHTML += `<p>${index + 1}. ${link.href}</p>`;
            });
            
            // 检查JS
            const scripts = document.querySelectorAll('script[src]');
            resourceStatus.innerHTML += `<p>JS文件: ${scripts.length}个</p>`;
            scripts.forEach((script, index) => {
                resourceStatus.innerHTML += `<p>${index + 1}. ${script.src}</p>`;
            });
            
            // 检查图片
            const images = document.querySelectorAll('img');
            resourceStatus.innerHTML += `<p>图片: ${images.length}个</p>`;
            
            // 检查浏览器信息
            resourceStatus.innerHTML += `<p>浏览器: ${navigator.userAgent}</p>`;
            
            // 添加返回首页的链接
            resourceStatus.innerHTML += `
                <div style="margin-top: 20px;">
                    <a href="/" class="btn">返回首页</a>
                    <a href="/simple-test" class="btn">简单测试页面</a>
                </div>
            `;
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM内容已加载完成');
            document.getElementById('js-status').textContent = 'JavaScript已正常加载并执行！';
            document.getElementById('js-status').className = 'success';
        });
    </script>
</body>
</html>
