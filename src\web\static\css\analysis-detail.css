/**
 * 九猫 - 分析详情页面专用样式
 * 优化分析详情页面的显示和性能
 */

/* 分页内容样式 */
.paginated-content {
    position: relative;
}

.paginated-display {
    min-height: 300px;
}

.pagination-controls {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-info {
    font-size: 0.9rem;
    color: #6c757d;
}

/* 懒加载内容样式 */
.content-placeholder {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 10px;
}

/* 优化折叠区域样式 */
.collapse:not(.show) {
    display: none;
}

.collapsing {
    height: 0;
    overflow: hidden;
    transition: height 0.35s ease;
}

/* 优化图表容器样式 */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
    will-change: transform;
}

/* 优化分析卡片样式 */
.analysis-card {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    background-color: #fff;
}

.analysis-card-header {
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.analysis-card-body {
    padding: 20px;
}

/* 优化分析过程和日志样式 */
.analysis-process-card {
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    overflow: hidden;
}

.analysis-process-card .card-header {
    padding: 10px 15px;
    background-color: #f8f9fa;
    font-size: 0.9rem;
}

.analysis-process-card .card-body {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.analysis-process-card pre {
    margin: 0;
    white-space: pre-wrap;
    font-size: 0.85rem;
    color: #212529;
}

/* 推理过程样式 */
.reasoning-content {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.reasoning-text {
    max-height: 500px;
    overflow-y: auto;
    padding: 10px;
}

.reasoning-text pre {
    margin: 0;
    white-space: pre-wrap;
    font-size: 0.9rem;
    color: #333;
    font-family: 'Courier New', Courier, monospace;
    line-height: 1.5;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #4361ee;
}

/* 日志条目样式 */
.log-entry {
    padding: 5px 10px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.85rem;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    color: #6c757d;
    margin-right: 10px;
}

/* 性能优化相关样式 */
.will-change-transform {
    will-change: transform;
}

.hardware-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* 减少重绘的元素 */
.reduce-repaint {
    contain: content;
}

/* 优化大文本显示 */
.large-text-container {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 10px;
}

/* 优化图表错误显示 */
.chart-error {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #6c757d;
    text-align: center;
    padding: 20px;
    border-radius: 5px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }

    .analysis-card-body {
        padding: 15px;
    }

    .analysis-process-card .card-body {
        max-height: 200px;
    }
}

/* 打印优化 */
@media print {
    .chart-container {
        break-inside: avoid;
    }

    .pagination-controls,
    .content-placeholder,
    button {
        display: none !important;
    }

    .collapse {
        display: block !important;
    }

    .analysis-process-card .card-body {
        max-height: none;
        overflow: visible;
    }
}
