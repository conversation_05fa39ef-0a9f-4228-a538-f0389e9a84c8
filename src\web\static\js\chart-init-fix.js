/**
 * 九猫 - 图表初始化修复脚本
 * 用于修复图表初始化问题
 * 版本: 1.1.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('开始修复图表初始化问题');
    
    // 确保Chart对象存在
    function ensureChartExists() {
        if (typeof Chart === 'undefined') {
            console.log('Chart对象未定义，尝试加载');
            
            // 定义全局Chart对象
            window.Chart = window.Chart || function(ctx, config) {
                console.log('使用备用Chart构造函数');
                this.ctx = ctx;
                this.config = config;
                this.data = config.data || {};
                this.type = config.type || 'bar';
                
                // 添加基本方法
                this.update = function() { console.log('Chart.update() 被调用'); };
                this.destroy = function() { console.log('Chart.destroy() 被调用'); };
                
                // 返回实例以支持链式调用
                return this;
            };
            
            // 添加必要的静态方法
            Chart.register = function() { console.log('Chart.register() 被调用'); };
            Chart.defaults = {};
            
            console.log('已创建Chart备用对象');
        }
    }
    
    // 防止重复初始化图表
    const initializedCharts = new Set();
    
    // 修复chart.min.js加载
    function loadChartJS() {
        return new Promise((resolve, reject) => {
            // 检查是否已加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载，跳过重复加载');
                return resolve();
            }
            
            // 准备加载路径列表，按优先级排序
            const chartJsPaths = [
                '/static/js/lib/chart.min.js',  // 首先尝试lib文件夹中的版本
                '/static/js/chart.min.js',      // 然后尝试js根目录的版本
                'https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js', // CDN备用
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.3.0/chart.umd.min.js', // CDN备用
                'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js', // 旧版本CDN备用
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js' // 旧版本CDN备用
            ];
            
            // 递归尝试加载
            function tryLoadChart(index) {
                if (index >= chartJsPaths.length) {
                    console.error('所有Chart.js加载路径都失败，使用备用方案');
                    ensureChartExists();
                    return resolve();
                }
                
                const path = chartJsPaths[index];
                console.log(`尝试从 ${path} 加载Chart.js`);
                
                const script = document.createElement('script');
                script.src = path;
                script.onload = () => {
                    console.log(`成功从 ${path} 加载Chart.js`);
                    resolve();
                };
                script.onerror = () => {
                    console.warn(`从 ${path} 加载Chart.js失败，尝试下一个路径`);
                    tryLoadChart(index + 1);
                };
                document.head.appendChild(script);
            }
            
            // 开始尝试第一个路径
            tryLoadChart(0);
        });
    }
    
    // 修复图表初始化
    function fixChartInitialization() {
        // 获取所有canvas元素
        const canvasElements = document.querySelectorAll('canvas');
        console.log(`找到 ${canvasElements.length} 个canvas元素`);
        
        if (canvasElements.length === 0) {
            return;
        }
        
        // 确保Chart存在
        ensureChartExists();
        
        // 处理每个canvas
        canvasElements.forEach((canvas, index) => {
            const canvasId = canvas.id || `canvas-${index}`;
            
            // 如果已经初始化过，跳过
            if (initializedCharts.has(canvasId)) {
                console.log(`跳过已初始化的图表: ${canvasId}`);
                return;
            }
            
            try {
                // 尝试获取数据
                let dimension = null;
                if (canvas.closest('.analysis-card')) {
                    dimension = canvas.closest('.analysis-card').getAttribute('data-dimension');
                }
                
                if (!dimension) {
                    // 尝试从父元素的类名获取维度信息
                    const parent = canvas.parentElement;
                    if (parent && parent.className) {
                        const match = parent.className.match(/dimension-(\w+)/);
                        if (match) {
                            dimension = match[1];
                        }
                    }
                }
                
                // 如果找到了维度，尝试使用对应数据
                let chartData = {
                    labels: ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'],
                    datasets: [{
                        label: '评分',
                        data: [85, 72, 90, 65, 78, 82],
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                };
                
                // 尝试查找全局分析结果
                if (dimension && window.analysisResults && window.analysisResults[dimension]) {
                    const result = window.analysisResults[dimension];
                    if (result.metadata && result.metadata.visualization_data) {
                        try {
                            const visData = JSON.parse(result.metadata.visualization_data);
                            if (visData.labels && visData.data) {
                                chartData = {
                                    labels: visData.labels,
                                    datasets: [{
                                        label: dimension,
                                        data: visData.data,
                                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                        borderColor: 'rgba(54, 162, 235, 1)',
                                        borderWidth: 1
                                    }]
                                };
                            }
                        } catch (e) {
                            console.warn(`解析${dimension}的可视化数据失败:`, e);
                        }
                    }
                }
                
                // 创建图表
                const ctx = canvas.getContext('2d');
                const newChart = new Chart(ctx, {
                    type: 'bar',
                    data: chartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
                
                // 存储到canvas元素上
                canvas._chart = newChart;
                
                // 标记为已初始化
                initializedCharts.add(canvasId);
                console.log(`成功初始化图表: ${canvasId}`);
            } catch (error) {
                console.error(`初始化图表失败: ${error.message}`);
                // 添加错误显示
                const errorMessage = document.createElement('div');
                errorMessage.className = 'chart-error';
                errorMessage.innerHTML = `<p>图表加载失败，请刷新页面重试</p><small>${error.message}</small>`;
                errorMessage.style.color = 'red';
                errorMessage.style.textAlign = 'center';
                canvas.after(errorMessage);
            }
        });
    }
    
    // 防止重复初始化
    let initializationInProgress = false;
    
    // 主初始化函数
    function initialize() {
        if (initializationInProgress) {
            console.log('图表初始化正在进行中，跳过');
            return;
        }
        
        initializationInProgress = true;
        
        // 加载Chart.js然后初始化图表
        loadChartJS()
            .then(() => {
                fixChartInitialization();
                initializationInProgress = false;
            })
            .catch(err => {
                console.error('图表初始化过程中出错:', err);
                initializationInProgress = false;
            });
    }
    
    // 在页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // 也在window.load时执行，以确保所有资源都已加载
    window.addEventListener('load', initialize);
    
    // 定期检查和修复
    setInterval(() => {
        if (!initializationInProgress && document.querySelectorAll('canvas').length > 0) {
            console.log('定期检查图表...');
            initialize();
        }
    }, 5000); // 每5秒检查一次
    
    // 导出全局方法以允许手动修复
    window.fixCharts = initialize;
    
})();
