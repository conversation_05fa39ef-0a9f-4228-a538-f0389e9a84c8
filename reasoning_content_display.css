/**
 * 九猫系统 - 增强版推理内容显示组件样式
 * 用于美化结构化推理内容的显示
 * 版本: 2.0.0 - 支持小节和更丰富的结构
 */

/* 主容器 */
.reasoning-content-enhanced {
    background-color: #fffdf7; /* 浅米黄色背景 */
    border: 1px solid #e8e0c5;
    border-radius: 8px;
    margin: 15px 0;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
}

/* 头部 */
.reasoning-content-enhanced .reasoning-header {
    padding: 12px 15px;
    border-bottom: 1px solid #e8e0c5;
    background-color: #f9f5e8; /* 更深的米黄色 */
    border-radius: 8px 8px 0 0;
}

.reasoning-content-enhanced .reasoning-header h3 {
    margin: 0;
    font-size: 18px;
    color: #5a4a2f;
    font-weight: 600;
}

/* 主体 */
.reasoning-content-enhanced .reasoning-body {
    padding: 0;
}

/* 部分容器 */
.reasoning-content-enhanced .reasoning-section {
    border-bottom: 1px solid #e8e0c5;
    transition: all 0.3s ease;
}

.reasoning-content-enhanced .reasoning-section:last-child {
    border-bottom: none;
    border-radius: 0 0 8px 8px;
}

/* 分析思路部分特殊样式 */
.reasoning-content-enhanced .approach-section .reasoning-section-header {
    background-color: #f0e8d0; /* 更深的背景色 */
}

.reasoning-content-enhanced .approach-section .reasoning-section-header h4 {
    color: #4a3a1f; /* 更深的文字颜色 */
}

/* 详细分析部分特殊样式 */
.reasoning-content-enhanced .analysis-section .reasoning-section-header {
    background-color: #e8f0d0; /* 淡绿色背景 */
}

.reasoning-content-enhanced .analysis-section .reasoning-section-header h4 {
    color: #3a4a1f; /* 深绿色文字 */
}

/* 部分标题 */
.reasoning-content-enhanced .reasoning-section-header {
    padding: 12px 15px;
    background-color: #f9f5e8;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.reasoning-content-enhanced .reasoning-section-header h4 {
    margin: 0;
    font-size: 16px;
    color: #5a4a2f;
    font-weight: 600;
}

.reasoning-content-enhanced .reasoning-section-header h5 {
    margin: 0;
    font-size: 15px;
    color: #5a4a2f;
    font-weight: 500;
}

.reasoning-content-enhanced .reasoning-section-header .toggle-btn {
    background-color: transparent;
    border: 1px solid #d9c89e;
    color: #8a7a5f;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reasoning-content-enhanced .reasoning-section-header .toggle-btn:hover {
    background-color: #f0e8d0;
    color: #5a4a2f;
}

.reasoning-content-enhanced .reasoning-section-header .toggle-btn i {
    margin-right: 5px;
}

/* 部分内容 */
.reasoning-content-enhanced .reasoning-section-body {
    padding: 15px;
    background-color: #fffdf7;
    overflow-x: auto;
}

/* 子部分容器 */
.reasoning-content-enhanced .sub-sections-container {
    margin-top: 10px;
}

/* 分析介绍部分 */
.reasoning-content-enhanced .analysis-intro {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #e8e0c5;
}

/* 子部分 */
.reasoning-content-enhanced .sub-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #fafaf5;
    border-radius: 6px;
    border-left: 3px solid #d9c89e;
}

.reasoning-content-enhanced .sub-section:last-child {
    margin-bottom: 0;
}

/* 子部分标题 */
.reasoning-content-enhanced .sub-section-header {
    margin: 0 0 10px 0;
    padding-bottom: 8px;
    font-size: 16px;
    color: #4a3a1f;
    border-bottom: 1px solid #e8e0c5;
}

/* 子部分内容 */
.reasoning-content-enhanced .sub-section-content {
    font-size: 14px;
    line-height: 1.6;
}

/* 展开/收起状态 */
.reasoning-content-enhanced .reasoning-section.collapsed .reasoning-section-header {
    border-bottom: none;
}

.reasoning-content-enhanced .reasoning-section.expanded .reasoning-section-header {
    border-bottom: 1px solid #e8e0c5;
}

/* 加载状态 */
.reasoning-content-enhanced .loading {
    padding: 30px;
    text-align: center;
    color: #8a7a5f;
}

.reasoning-content-enhanced .loading .spinner-border {
    color: #d9c89e !important;
    width: 2rem;
    height: 2rem;
}

/* 错误状态 */
.reasoning-content-enhanced .error {
    padding: 15px;
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

/* Markdown内容样式 */
.reasoning-content-enhanced .markdown-content pre,
.reasoning-content-enhanced .reasoning-section-body pre {
    background-color: #f9f5e8;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e8e0c5;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 14px;
    color: #333;
    margin: 10px 0;
}

.reasoning-content-enhanced .markdown-content p,
.reasoning-content-enhanced .reasoning-section-body p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.reasoning-content-enhanced .markdown-content ul,
.reasoning-content-enhanced .markdown-content ol,
.reasoning-content-enhanced .reasoning-section-body ul,
.reasoning-content-enhanced .reasoning-section-body ol {
    padding-left: 20px;
    margin-bottom: 10px;
}

.reasoning-content-enhanced .markdown-content li,
.reasoning-content-enhanced .reasoning-section-body li {
    margin-bottom: 5px;
}

.reasoning-content-enhanced .markdown-content h1,
.reasoning-content-enhanced .markdown-content h2,
.reasoning-content-enhanced .markdown-content h3,
.reasoning-content-enhanced .markdown-content h4,
.reasoning-content-enhanced .markdown-content h5,
.reasoning-content-enhanced .markdown-content h6,
.reasoning-content-enhanced .reasoning-section-body h1,
.reasoning-content-enhanced .reasoning-section-body h2,
.reasoning-content-enhanced .reasoning-section-body h3,
.reasoning-content-enhanced .reasoning-section-body h4,
.reasoning-content-enhanced .reasoning-section-body h5,
.reasoning-content-enhanced .reasoning-section-body h6 {
    color: #5a4a2f;
    margin-top: 15px;
    margin-bottom: 10px;
}

.reasoning-content-enhanced .markdown-content blockquote,
.reasoning-content-enhanced .reasoning-section-body blockquote {
    border-left: 4px solid #d9c89e;
    padding-left: 15px;
    margin-left: 0;
    color: #6c757d;
}

.reasoning-content-enhanced .markdown-content code,
.reasoning-content-enhanced .reasoning-section-body code {
    background-color: #f0e8d0;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 90%;
    color: #333;
}

/* 加粗文本特殊样式 */
.reasoning-content-enhanced strong {
    color: #4a3a1f;
    font-weight: 600;
}

/* 编号列表特殊样式 */
.reasoning-content-enhanced ol {
    counter-reset: item;
    list-style-type: none;
    padding-left: 0;
}

.reasoning-content-enhanced ol li {
    counter-increment: item;
    margin-bottom: 8px;
    position: relative;
    padding-left: 28px;
}

.reasoning-content-enhanced ol li:before {
    content: counter(item) ".";
    position: absolute;
    left: 0;
    top: 0;
    font-weight: bold;
    color: #8a7a5f;
    width: 22px;
    text-align: right;
}

/* 分析思路部分的编号列表特殊样式 */
.reasoning-content-enhanced .approach-section ol li:before {
    color: #4a3a1f;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .reasoning-content-enhanced .reasoning-section-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .reasoning-content-enhanced .reasoning-section-header .toggle-btn {
        margin-top: 8px;
        align-self: flex-end;
    }

    .reasoning-content-enhanced .sub-section {
        padding: 10px;
    }
}
