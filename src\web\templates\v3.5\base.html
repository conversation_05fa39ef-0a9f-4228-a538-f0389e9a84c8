<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{% block title %}九猫小说分析写作系统v3.5{% endblock %}</title>

    <!-- 图标 -->
    <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/v3_5_style.css') }}">

    <!-- Markdown样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/github-markdown.css') }}">

    <!-- 代码高亮 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/highlight.min.css') }}">

    <!-- 额外CSS -->
    {% block extra_css %}{% endblock %}

    <!-- 预加载脚本 -->
    <script>
        // 检测系统主题偏好
        function detectThemePreference() {
            if (localStorage.getItem('theme') === 'dark' ||
                (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches &&
                !localStorage.getItem('theme'))) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
            }
        }

        // 立即执行主题检测
        detectThemePreference();
    </script>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('v3_5.index') }}">
                <i class="fas fa-cat"></i> 九猫小说分析写作系统v3.5
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'v3_5.index' %}active{% endif %}" href="{{ url_for('v3_5.index') }}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'v3.novels' %}active{% endif %}" href="{{ url_for('v3.novels') }}">
                            <i class="fas fa-book"></i> 小说列表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'v3.upload_novel' %}active{% endif %}" href="{{ url_for('v3.upload_novel') }}">
                            <i class="fas fa-upload"></i> 上传小说
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'v3_5.reference_templates' %}active{% endif %}" href="{{ url_for('v3_5.reference_templates') }}">
                            <i class="fas fa-bookmark"></i> 参考蓝本
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'v3_5.console' %}active{% endif %}" href="{{ url_for('v3_5.console') }}">
                            <i class="fas fa-terminal"></i> 控制台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'v3_5.text_comparison' %}active{% endif %}" href="{{ url_for('v3_5.text_comparison') }}">
                            <i class="fas fa-exchange-alt"></i> 原文对比
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'v3_5.system_monitor' %}active{% endif %}" href="{{ url_for('v3_5.system_monitor') }}">
                            <i class="fas fa-chart-line"></i> 系统监控
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="themeToggle">
                            <i class="fas fa-moon" id="darkModeIcon"></i>
                            <i class="fas fa-sun" id="lightModeIcon" style="display: none;"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'v3_5.help_page' %}active{% endif %}" href="{{ url_for('v3_5.help_page') }}">
                            <i class="fas fa-question-circle"></i> 帮助
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container py-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="footer mt-auto py-3">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>九猫小说分析写作系统v3.5</h5>
                    <p class="text-muted">基于先进AI技术的小说分析与写作辅助工具</p>
                </div>
                <div class="col-md-3">
                    <h5>功能</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('v3.novels') }}" class="text-decoration-none">小说分析</a></li>
                        <li><a href="{{ url_for('v3_5.console') }}" class="text-decoration-none">自动写作</a></li>
                        <li><a href="{{ url_for('v3_5.text_comparison') }}" class="text-decoration-none">原文对比</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>资源</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('v3_5.help_page') }}" class="text-decoration-none">帮助中心</a></li>
                        <li><a href="{{ url_for('v3_5.system_monitor') }}" class="text-decoration-none">系统监控</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p class="mb-0">© 2023-2024 九猫小说分析写作系统 | 版本 3.5</p>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>

    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>

    <!-- Marked.js (Markdown解析) -->
    <script src="{{ url_for('static', filename='js/marked.min.js') }}"></script>

    <!-- Highlight.js (代码高亮) -->
    <script src="{{ url_for('static', filename='js/highlight.min.js') }}"></script>

    <!-- 主题切换 -->
    <script>
        $(document).ready(function() {
            // 更新主题图标
            function updateThemeIcons() {
                const theme = localStorage.getItem('theme') || 'light';
                if (theme === 'dark') {
                    $('#darkModeIcon').hide();
                    $('#lightModeIcon').show();
                } else {
                    $('#darkModeIcon').show();
                    $('#lightModeIcon').hide();
                }
            }

            // 切换主题
            $('#themeToggle').click(function(e) {
                e.preventDefault();
                const currentTheme = localStorage.getItem('theme') || 'light';
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcons();
            });

            // 初始化主题图标
            updateThemeIcons();
        });
    </script>

    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/v3.5/main.js') }}"></script>

    <!-- 额外JS -->
    {% block extra_js %}{% endblock %}
</body>
</html>
