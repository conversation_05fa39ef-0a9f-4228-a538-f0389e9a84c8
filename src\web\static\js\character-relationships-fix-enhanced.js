/**
 * 九猫 - 专门修复character_relationships分析中位置4476处的JSON解析错误
 * 这个脚本会在页面加载时立即执行，修复特定的JSON解析错误
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('character_relationships增强修复脚本已加载');
    
    // 保存原始的JSON.parse方法
    var originalJSONParse = JSON.parse;
    
    // 重写JSON.parse方法
    JSON.parse = function(text, reviver) {
        try {
            // 特殊处理：检查是否包含character_relationships错误
            if (text && typeof text === 'string' && text.includes('character_relationships** 时遇到了问题')) {
                console.log('检测到character_relationships特定错误，尝试精确修复');
                
                // 检查是否是位置4476附近的错误
                if (text.length > 4400) {
                    // 查找错误位置前后的内容
                    var errorPos = 4476; // 根据错误消息确定的位置
                    var beforeError = text.substring(Math.max(0, errorPos - 50), errorPos);
                    var afterError = text.substring(errorPos, Math.min(text.length, errorPos + 50));
                    
                    console.log('精确错误位置前内容:', beforeError);
                    console.log('精确错误位置后内容:', afterError);
                    
                    // 修复特定错误 - 在错误位置添加引号
                    var fixedText = text.substring(0, errorPos) + '"' + text.substring(errorPos);
                    console.log('尝试在位置4476添加引号');
                    
                    try {
                        return originalJSONParse(fixedText, reviver);
                    } catch (specialError) {
                        console.error('精确修复失败，尝试添加逗号:', specialError.message);
                        
                        // 尝试添加逗号
                        try {
                            fixedText = text.substring(0, errorPos) + ',' + text.substring(errorPos);
                            console.log('尝试在位置4476添加逗号');
                            return originalJSONParse(fixedText, reviver);
                        } catch (commaError) {
                            console.error('添加逗号修复失败，尝试添加右花括号:', commaError.message);
                            
                            // 尝试添加右花括号
                            try {
                                fixedText = text.substring(0, errorPos) + '}' + text.substring(errorPos);
                                console.log('尝试在位置4476添加右花括号');
                                return originalJSONParse(fixedText, reviver);
                            } catch (braceError) {
                                console.error('添加右花括号修复失败:', braceError.message);
                                // 继续执行标准解析流程
                            }
                        }
                    }
                }
            }
            
            // 尝试使用原始方法解析
            return originalJSONParse(text, reviver);
        } catch (e) {
            console.error('JSON.parse错误:', e.message);
            
            // 检查是否是位置4476附近的错误
            if (e.message.includes('position 447') || e.message.includes('Unterminated string')) {
                console.log('检测到位置447x附近的错误，尝试修复');
                
                try {
                    // 尝试修复未终止的字符串
                    var errorPos = -1;
                    var match = e.message.match(/position (\d+)/);
                    if (match) {
                        errorPos = parseInt(match[1]);
                    } else {
                        // 如果没有明确的位置，使用已知的错误位置
                        errorPos = 4475;
                    }
                    
                    console.log('错误位置:', errorPos);
                    
                    // 检查错误位置前后的内容
                    var beforeError = text.substring(Math.max(0, errorPos - 50), errorPos);
                    var afterError = text.substring(errorPos, Math.min(text.length, errorPos + 50));
                    console.log('错误位置前内容:', beforeError);
                    console.log('错误位置后内容:', afterError);
                    
                    // 尝试添加引号
                    var fixedText = text.substring(0, errorPos) + '"' + text.substring(errorPos);
                    console.log('尝试在错误位置添加引号');
                    
                    try {
                        return originalJSONParse(fixedText, reviver);
                    } catch (quoteError) {
                        console.error('添加引号修复失败:', quoteError.message);
                        
                        // 尝试添加逗号
                        try {
                            fixedText = text.substring(0, errorPos) + ',' + text.substring(errorPos);
                            console.log('尝试在错误位置添加逗号');
                            return originalJSONParse(fixedText, reviver);
                        } catch (commaError) {
                            console.error('添加逗号修复失败:', commaError.message);
                            
                            // 尝试添加右花括号
                            try {
                                fixedText = text.substring(0, errorPos) + '}' + text.substring(errorPos);
                                console.log('尝试在错误位置添加右花括号');
                                return originalJSONParse(fixedText, reviver);
                            } catch (braceError) {
                                console.error('添加右花括号修复失败:', braceError.message);
                                
                                // 如果所有尝试都失败，返回一个空对象而不是抛出错误
                                console.warn('所有修复尝试都失败，返回空对象');
                                return {};
                            }
                        }
                    }
                } catch (fixError) {
                    console.error('修复过程中出错:', fixError.message);
                    
                    // 返回空对象而不是抛出错误
                    console.warn('修复过程出错，返回空对象');
                    return {};
                }
            }
            
            // 如果不是位置4476附近的错误，重新抛出原始错误
            throw e;
        }
    };
    
    // 在页面加载完成后扫描并修复所有脚本
    document.addEventListener('DOMContentLoaded', function() {
        console.log('character_relationships增强修复脚本开始扫描页面');
        
        // 特殊处理：检查是否在character_relationships页面
        var isCharacterRelationshipsPage = window.location.pathname.includes('character_relationships');
        if (isCharacterRelationshipsPage) {
            console.log('检测到character_relationships页面，应用特殊修复');
            
            // 查找分析内容元素
            var analysisContentElements = document.querySelectorAll('.analysis-content, .markdown-content');
            analysisContentElements.forEach(function(element) {
                try {
                    var content = element.textContent || '';
                    if (content && content.includes('character_relationships** 时遇到了问题')) {
                        console.log('找到包含character_relationships错误的内容元素');
                        
                        // 检查是否是JSON格式
                        if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
                            try {
                                // 尝试解析JSON
                                var jsonData = JSON.parse(content);
                                console.log('内容已经是有效的JSON');
                            } catch (e) {
                                console.error('解析内容为JSON失败:', e.message);
                                
                                // 特殊修复character_relationships错误
                                if ((e.message.includes('position 447') || e.message.includes('Unterminated string')) && content.length > 4400) {
                                    var errorPos = -1;
                                    var match = e.message.match(/position (\d+)/);
                                    if (match) {
                                        errorPos = parseInt(match[1]);
                                    } else {
                                        // 如果没有明确的位置，使用已知的错误位置
                                        errorPos = 4475;
                                    }
                                    
                                    console.log('尝试修复character_relationships特定错误，位置:', errorPos);
                                    var fixedContent = content.substring(0, errorPos) + '"' + content.substring(errorPos);
                                    
                                    try {
                                        // 尝试解析修复后的内容
                                        JSON.parse(fixedContent);
                                        console.log('成功修复character_relationships特定错误');
                                        
                                        // 更新元素内容
                                        element.textContent = fixedContent;
                                    } catch (e2) {
                                        console.error('修复character_relationships特定错误失败:', e2.message);
                                        
                                        // 尝试添加逗号
                                        try {
                                            fixedContent = content.substring(0, errorPos) + ',' + content.substring(errorPos);
                                            JSON.parse(fixedContent);
                                            console.log('成功通过添加逗号修复character_relationships特定错误');
                                            element.textContent = fixedContent;
                                        } catch (e3) {
                                            console.error('添加逗号修复失败:', e3.message);
                                            
                                            // 尝试添加右花括号
                                            try {
                                                fixedContent = content.substring(0, errorPos) + '}' + content.substring(errorPos);
                                                JSON.parse(fixedContent);
                                                console.log('成功通过添加右花括号修复character_relationships特定错误');
                                                element.textContent = fixedContent;
                                            } catch (e4) {
                                                console.error('添加右花括号修复失败:', e4.message);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.error('处理character_relationships页面元素时出错:', e.message);
                }
            });
        }
        
        // 查找所有内联脚本
        var scripts = document.querySelectorAll('script:not([src])');
        
        scripts.forEach(function(script) {
            var content = script.textContent || '';
            
            // 检查是否包含JSON.parse调用和character_relationships错误
            if (content.includes('JSON.parse(') && content.includes('character_relationships** 时遇到了问题')) {
                console.log('找到包含character_relationships错误的JSON.parse调用');
                
                // 修复JSON.parse调用
                var fixedContent = content.replace(
                    /(JSON\.parse\s*\(\s*['"])(.*?character_relationships\*\* 时遇到了问题.*?)(['"])/g,
                    function(match, prefix, jsonStr, suffix) {
                        // 检查是否是位置4476附近的错误
                        if (jsonStr.length > 4400) {
                            console.log('修复位置4476附近的错误');
                            
                            // 在错误位置添加引号
                            var errorPos = 4475;
                            var fixedJsonStr = jsonStr.substring(0, errorPos - jsonStr.indexOf('character_relationships')) + '"' + 
                                              jsonStr.substring(errorPos - jsonStr.indexOf('character_relationships'));
                            
                            return prefix + fixedJsonStr + suffix;
                        }
                        return match;
                    }
                );
                
                // 如果内容被修改，替换脚本
                if (fixedContent !== content) {
                    console.log('替换修复后的脚本');
                    var newScript = document.createElement('script');
                    newScript.textContent = fixedContent;
                    script.parentNode.replaceChild(newScript, script);
                }
            }
        });
    });
    
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && 
            (event.error.message.includes('position 447') || 
             event.error.message.includes('Unterminated string') ||
             event.error.message.includes('Expected') && event.error.message.includes('after property value'))) {
            console.error('捕获到character_relationships相关错误:', event.error.message);
            console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
})();
