/**
 * 九猫 - 维度选择下拉菜单修复脚本
 * 完全重构维度选择下拉菜单功能，确保可以正常选择维度
 * 版本: 1.0.0
 */

(function() {
    console.log('[维度下拉菜单修复] 脚本加载中...');

    // 维度列表 - 与config.py中的ANALYSIS_DIMENSIONS保持一致
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "chapter_outline", name: "章节大纲" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" },
        { key: "outline_analysis", name: "大纲分析" },
        { key: "popular_tropes", name: "热梗统计" }
    ];

    // 配置
    const CONFIG = {
        logPrefix: '[维度下拉菜单修复]',
        fixTimeout: 300,
        checkInterval: 2000,
        dropdownSelector: '.dimension-dropdown, [data-bs-toggle="dropdown"]',
        menuSelector: '.dropdown-menu',
        itemSelector: '.dropdown-item, .dimension-item',
        activeClass: 'show',
        debug: true
    };

    // 状态
    const STATE = {
        initialized: false,
        dropdowns: [],
        activeDropdown: null
    };

    // 安全日志函数
    function safeLog(message, level = 'info') {
        try {
            const prefix = CONFIG.logPrefix;
            switch (level) {
                case 'error':
                    console.error(`${prefix} ${message}`);
                    break;
                case 'warn':
                    console.warn(`${prefix} ${message}`);
                    break;
                case 'debug':
                    if (CONFIG.debug) {
                        console.debug(`${prefix} ${message}`);
                    }
                    break;
                default:
                    console.log(`${prefix} ${message}`);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }

    // 查找所有维度下拉菜单
    function findAllDropdowns() {
        try {
            const dropdowns = document.querySelectorAll(CONFIG.dropdownSelector);
            safeLog(`找到 ${dropdowns.length} 个维度下拉菜单`);
            return Array.from(dropdowns);
        } catch (e) {
            safeLog(`查找维度下拉菜单时出错: ${e.message}`, 'error');
            return [];
        }
    }

    // 获取下拉菜单的菜单元素
    function getDropdownMenu(dropdown) {
        try {
            // 方法1：查找子元素
            let menu = dropdown.querySelector(CONFIG.menuSelector);
            if (menu) {
                return menu;
            }

            // 方法2：查找下一个兄弟元素
            menu = dropdown.nextElementSibling;
            if (menu && menu.matches(CONFIG.menuSelector)) {
                return menu;
            }

            // 方法3：查找data-bs-target属性指向的元素
            const targetId = dropdown.getAttribute('data-bs-target');
            if (targetId) {
                menu = document.querySelector(targetId);
                if (menu) {
                    return menu;
                }
            }

            // 方法4：查找aria-controls属性指向的元素
            const controlsId = dropdown.getAttribute('aria-controls');
            if (controlsId) {
                menu = document.getElementById(controlsId);
                if (menu) {
                    return menu;
                }
            }

            safeLog(`无法找到下拉菜单的菜单元素: ${dropdown.outerHTML.substring(0, 100)}`, 'warn');
            return null;
        } catch (e) {
            safeLog(`获取下拉菜单的菜单元素时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 切换下拉菜单的显示状态
    function toggleDropdown(dropdown, force = null) {
        try {
            const menu = getDropdownMenu(dropdown);
            if (!menu) {
                safeLog('无法切换下拉菜单，找不到菜单元素', 'warn');
                return false;
            }

            // 确定是否应该显示
            const shouldShow = force !== null ? force : !menu.classList.contains(CONFIG.activeClass);

            // 如果要显示，先关闭所有其他下拉菜单
            if (shouldShow) {
                closeAllDropdowns();
                STATE.activeDropdown = dropdown;
            } else if (STATE.activeDropdown === dropdown) {
                STATE.activeDropdown = null;
            }

            // 切换显示状态
            if (shouldShow) {
                menu.classList.add(CONFIG.activeClass);
                dropdown.setAttribute('aria-expanded', 'true');
                safeLog(`打开下拉菜单: ${dropdown.textContent.trim().substring(0, 20)}`);
            } else {
                menu.classList.remove(CONFIG.activeClass);
                dropdown.setAttribute('aria-expanded', 'false');
                safeLog(`关闭下拉菜单: ${dropdown.textContent.trim().substring(0, 20)}`);
            }

            return shouldShow;
        } catch (e) {
            safeLog(`切换下拉菜单时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 关闭所有下拉菜单
    function closeAllDropdowns() {
        try {
            document.querySelectorAll(`${CONFIG.menuSelector}.${CONFIG.activeClass}`).forEach(menu => {
                menu.classList.remove(CONFIG.activeClass);
                const dropdown = menu.previousElementSibling;
                if (dropdown) {
                    dropdown.setAttribute('aria-expanded', 'false');
                }
            });
            STATE.activeDropdown = null;
            safeLog('关闭所有下拉菜单');
        } catch (e) {
            safeLog(`关闭所有下拉菜单时出错: ${e.message}`, 'error');
        }
    }

    // 设置下拉菜单的事件处理
    function setupDropdownEvents(dropdown) {
        try {
            // 移除现有的事件监听器，避免重复绑定
            dropdown.removeEventListener('click', handleDropdownClick);

            // 添加新的事件监听器
            dropdown.addEventListener('click', handleDropdownClick);

            // 设置菜单项的事件处理
            const menu = getDropdownMenu(dropdown);
            if (menu) {
                const items = menu.querySelectorAll(CONFIG.itemSelector);
                items.forEach(item => {
                    item.removeEventListener('click', handleMenuItemClick);
                    item.addEventListener('click', handleMenuItemClick);
                });
                safeLog(`为下拉菜单设置了 ${items.length} 个菜单项的事件处理`);
            }

            safeLog(`为下拉菜单设置了事件处理: ${dropdown.textContent.trim().substring(0, 20)}`);
            return true;
        } catch (e) {
            safeLog(`设置下拉菜单事件处理时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 下拉菜单点击事件处理函数
    function handleDropdownClick(event) {
        try {
            event.preventDefault();
            event.stopPropagation();

            const dropdown = this;
            safeLog(`下拉菜单被点击: ${dropdown.textContent.trim().substring(0, 20)}`);

            toggleDropdown(dropdown);
        } catch (e) {
            safeLog(`处理下拉菜单点击事件时出错: ${e.message}`, 'error');
        }
    }

    // 菜单项点击事件处理函数
    function handleMenuItemClick(event) {
        try {
            event.preventDefault();
            event.stopPropagation();

            const item = this;
            const menu = item.closest(CONFIG.menuSelector);
            const dropdown = menu ? menu.previousElementSibling : null;

            safeLog(`菜单项被点击: ${item.textContent.trim()}`);

            // 关闭下拉菜单
            if (menu) {
                menu.classList.remove(CONFIG.activeClass);
            }
            if (dropdown) {
                dropdown.setAttribute('aria-expanded', 'false');
            }

            // 处理维度选择
            const dimension = item.getAttribute('data-dimension');
            const chapterId = item.closest('[data-chapter-id]')?.getAttribute('data-chapter-id') ||
                             dropdown?.closest('[data-chapter-id]')?.getAttribute('data-chapter-id');

            if (dimension && chapterId) {
                safeLog(`选择维度: ${dimension}, 章节ID: ${chapterId}`);
                handleDimensionSelection(dimension, chapterId);
            } else {
                safeLog(`无法处理维度选择，缺少必要的属性: dimension=${dimension}, chapterId=${chapterId}`, 'warn');
            }
        } catch (e) {
            safeLog(`处理菜单项点击事件时出错: ${e.message}`, 'error');
        }
    }

    // 处理维度选择
    function handleDimensionSelection(dimension, chapterId) {
        try {
            safeLog(`处理维度选择: dimension=${dimension}, chapterId=${chapterId}`);

            // 获取小说ID
            const novelId = document.querySelector('[data-novel-id]')?.getAttribute('data-novel-id');
            if (!novelId) {
                safeLog('无法获取小说ID', 'error');
                return;
            }

            // 使用页面中定义的辅助函数（如果存在）
            if (window.chapterAnalysisHelpers) {
                safeLog('使用页面中定义的辅助函数');

                // 显示分析进度模态框
                window.chapterAnalysisHelpers.showProgressModal(chapterId, dimension);

                // 构建API URL
                const apiUrl = `/api/novel/${novelId}/chapter/${chapterId}/analyze`;

                // 发送分析请求
                safeLog(`发送分析请求: ${apiUrl}`);
                fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dimensions: dimension === 'all' ? [] : [dimension],
                        model: 'deepseek-r1'
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    safeLog(`分析请求成功: ${JSON.stringify(data).substring(0, 100)}...`);

                    // 使用页面中定义的轮询函数
                    if (data.success && window.chapterAnalysisHelpers.startProgressPolling) {
                        window.chapterAnalysisHelpers.startProgressPolling(novelId, chapterId);
                    } else {
                        // 如果没有轮询函数或请求失败，隐藏进度模态框
                        const progressModal = document.getElementById('analysisProgressModal');
                        if (progressModal) {
                            const bsModal = bootstrap.Modal.getInstance(progressModal);
                            if (bsModal) {
                                bsModal.hide();
                            }
                        }

                        if (!data.success) {
                            alert(`分析请求失败: ${data.error || '未知错误'}`);
                        }
                    }
                })
                .catch(error => {
                    safeLog(`分析请求失败: ${error.message}`, 'error');

                    // 处理错误
                    const progressModal = document.getElementById('analysisProgressModal');
                    if (progressModal) {
                        const bsModal = bootstrap.Modal.getInstance(progressModal);
                        if (bsModal) {
                            bsModal.hide();
                        }
                    }

                    // 显示错误消息
                    alert(`分析请求失败: ${error.message}`);
                });
            } else {
                safeLog('页面中未定义辅助函数，使用默认实现');

                // 显示分析进度模态框
                const progressModal = document.getElementById('analysisProgressModal');
                if (progressModal) {
                    const bsModal = new bootstrap.Modal(progressModal);
                    bsModal.show();
                    safeLog('显示分析进度模态框');
                }

                // 构建API URL
                const apiUrl = `/api/novel/${novelId}/chapter/${chapterId}/analyze`;

                // 发送分析请求
                safeLog(`发送分析请求: ${apiUrl}`);
                fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dimensions: dimension === 'all' ? [] : [dimension],
                        model: 'deepseek-r1'
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    safeLog(`分析请求成功: ${JSON.stringify(data).substring(0, 100)}...`);

                    // 处理成功响应
                    if (progressModal) {
                        // 隐藏进度模态框
                        const bsModal = bootstrap.Modal.getInstance(progressModal);
                        if (bsModal) {
                            bsModal.hide();
                        }
                    }

                    // 刷新页面或更新UI
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                })
                .catch(error => {
                    safeLog(`分析请求失败: ${error.message}`, 'error');

                    // 处理错误
                    if (progressModal) {
                        // 隐藏进度模态框
                        const bsModal = bootstrap.Modal.getInstance(progressModal);
                        if (bsModal) {
                            bsModal.hide();
                        }
                    }

                    // 显示错误消息
                    alert(`分析请求失败: ${error.message}`);
                });
            }
        } catch (e) {
            safeLog(`处理维度选择时出错: ${e.message}`, 'error');
        }
    }

    // 初始化所有下拉菜单
    function initializeAllDropdowns() {
        try {
            // 查找所有下拉菜单
            const dropdowns = findAllDropdowns();
            STATE.dropdowns = dropdowns;

            // 设置事件处理
            let setupCount = 0;
            dropdowns.forEach(dropdown => {
                if (setupDropdownEvents(dropdown)) {
                    setupCount++;
                }
            });

            safeLog(`成功初始化 ${setupCount}/${dropdowns.length} 个下拉菜单`);
            return setupCount > 0;
        } catch (e) {
            safeLog(`初始化所有下拉菜单时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 设置全局点击事件，关闭所有下拉菜单
    function setupGlobalClickEvent() {
        try {
            // 移除现有的事件监听器，避免重复绑定
            document.removeEventListener('click', handleGlobalClick);

            // 添加新的事件监听器
            document.addEventListener('click', handleGlobalClick);

            safeLog('设置了全局点击事件');
            return true;
        } catch (e) {
            safeLog(`设置全局点击事件时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 全局点击事件处理函数
    function handleGlobalClick(event) {
        try {
            // 检查点击是否在下拉菜单内
            const isInDropdown = event.target.closest(CONFIG.dropdownSelector) ||
                               event.target.closest(CONFIG.menuSelector);

            // 如果不在下拉菜单内，关闭所有下拉菜单
            if (!isInDropdown) {
                closeAllDropdowns();
            }
        } catch (e) {
            safeLog(`处理全局点击事件时出错: ${e.message}`, 'error');
        }
    }

    // 定期检查并修复下拉菜单
    function startPeriodicCheck() {
        try {
            // 设置定期检查
            setInterval(() => {
                // 查找所有下拉菜单
                const dropdowns = findAllDropdowns();

                // 检查是否有新的下拉菜单
                const newDropdowns = dropdowns.filter(dropdown => !STATE.dropdowns.includes(dropdown));
                if (newDropdowns.length > 0) {
                    safeLog(`发现 ${newDropdowns.length} 个新的下拉菜单`);

                    // 设置新下拉菜单的事件处理
                    newDropdowns.forEach(dropdown => {
                        setupDropdownEvents(dropdown);
                    });

                    // 更新状态
                    STATE.dropdowns = dropdowns;
                }
            }, CONFIG.checkInterval);

            safeLog(`启动了定期检查，间隔: ${CONFIG.checkInterval}ms`);
            return true;
        } catch (e) {
            safeLog(`启动定期检查时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 初始化
    function init() {
        try {
            if (STATE.initialized) {
                safeLog('已经初始化，跳过');
                return;
            }

            safeLog('开始初始化');

            // 初始化所有下拉菜单
            initializeAllDropdowns();

            // 设置全局点击事件
            setupGlobalClickEvent();

            // 启动定期检查
            startPeriodicCheck();

            // 标记为已初始化
            STATE.initialized = true;

            safeLog('初始化完成');
        } catch (e) {
            safeLog(`初始化时出错: ${e.message}`, 'error');
        }
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 导出到全局命名空间，以便调试
    window.dimensionDropdownFix = {
        init: init,
        findAllDropdowns: findAllDropdowns,
        initializeAllDropdowns: initializeAllDropdowns,
        closeAllDropdowns: closeAllDropdowns,
        toggleDropdown: toggleDropdown,
        state: STATE
    };

    safeLog('脚本加载完成');
})();
