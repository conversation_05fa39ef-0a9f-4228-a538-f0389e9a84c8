{% extends "base.html" %}

{% block title %}小说列表 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">小说列表</h4>
                <a href="{{ url_for('upload_novel') }}" class="btn btn-light btn-sm">
                    <i class="fas fa-upload me-1"></i> 上传新小说
                </a>
            </div>
            <div class="card-body">
                {% if novels %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>标题</th>
                                    <th>作者</th>
                                    <th>上传时间</th>
                                    <th>字数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for novel in novels %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="text-decoration-none">
                                            {{ novel.title }}
                                        </a>
                                    </td>
                                    <td>{{ novel.author or '未知' }}</td>
                                    <td>{{ novel.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ novel.content|length }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-novel-btn"
                                                    data-novel-id="{{ novel.id }}" 
                                                    data-novel-title="{{ novel.title }}">
                                                <i class="fas fa-trash"></i> 删除
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        暂无小说，请点击右上角的"上传新小说"按钮开始使用。
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除小说"<span id="novelTitle"></span>"吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST" action="">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(novelId, novelTitle) {
        // 设置模态框内容
        document.getElementById('novelTitle').textContent = novelTitle;
        document.getElementById('deleteForm').action = `/novel/${novelId}/delete`;

        // 显示模态框
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    // 为删除按钮添加事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.delete-novel-btn').forEach(function(button) {
            button.addEventListener('click', function() {
                const novelId = this.getAttribute('data-novel-id');
                const novelTitle = this.getAttribute('data-novel-title');
                confirmDelete(novelId, novelTitle);
            });
        });
    });
</script>
{% endblock %}
