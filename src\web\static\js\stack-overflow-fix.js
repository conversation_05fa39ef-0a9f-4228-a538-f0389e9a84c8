/**
 * 九猫 - 堆栈溢出修复脚本
 * 版本: 2.0.0
 * 
 * 这个脚本专门用于修复"Maximum call stack size exceeded"错误
 * 通过防止DOM操作函数的递归调用和解决Chart.js相关问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 设置全局标记，表示已应用堆栈溢出修复
    if (window.__stackOverflowFixApplied) {
        console.log('堆栈溢出修复已经应用，跳过');
        return;
    }
    
    window.__stackOverflowFixApplied = true;
    console.log('堆栈溢出修复脚本已加载 - 最高优先级');
    
    // 存储原始DOM方法
    const originalMethods = {
        appendChild: Node.prototype.appendChild,
        replaceChild: Node.prototype.replaceChild,
        removeChild: Node.prototype.removeChild,
        insertBefore: Node.prototype.insertBefore
    };
    
    // 重置所有DOM方法到原始状态
    function resetAllDomMethods() {
        console.log('重置所有DOM方法到原始状态');
        Node.prototype.appendChild = originalMethods.appendChild;
        Node.prototype.replaceChild = originalMethods.replaceChild;
        Node.prototype.removeChild = originalMethods.removeChild;
        Node.prototype.insertBefore = originalMethods.insertBefore;
    }
    
    // 防止递归调用的计数器
    const recursionCounter = {
        appendChild: 0,
        replaceChild: 0,
        removeChild: 0,
        insertBefore: 0,
        maxRecursion: 1 // 限制为只允许1层递归，防止堆栈溢出
    };
    
    // 重置递归计数器
    function resetRecursionCounters() {
        recursionCounter.appendChild = 0;
        recursionCounter.replaceChild = 0;
        recursionCounter.removeChild = 0;
        recursionCounter.insertBefore = 0;
    }
    
    // 安全地执行DOM操作，防止递归
    function safelyExecuteDom(operation, args, methodName) {
        // 增加递归计数
        recursionCounter[methodName]++;
        
        // 如果递归太深，阻止操作
        if (recursionCounter[methodName] > recursionCounter.maxRecursion) {
            console.error(`检测到${methodName}可能的递归调用，阻止执行`);
            recursionCounter[methodName] = 0; // 重置计数器
            return null;
        }
        
        // 尝试执行操作
        try {
            const result = operation();
            // 重置递归计数
            recursionCounter[methodName] = 0;
            return result;
        } catch (e) {
            console.error(`${methodName}操作失败:`, e.message);
            // 重置递归计数
            recursionCounter[methodName] = 0;
            
            // 对于特定错误，执行额外处理
            if (e.message && e.message.includes('Maximum call stack size exceeded')) {
                console.error(`${methodName}操作导致堆栈溢出，尝试重置DOM方法`);
                
                // 临时重置DOM方法再尝试一次
                resetAllDomMethods();
                resetRecursionCounters();
                
                try {
                    // 使用原始方法
                    const originalResult = args[0];
                    
                    // 重新应用我们的安全方法
                    setTimeout(applyDomMethodFixes, 0);
                    
                    return originalResult;
                } catch (e2) {
                    console.error(`使用原始${methodName}方法也失败:`, e2.message);
                    
                    // 重新应用我们的安全方法
                    setTimeout(applyDomMethodFixes, 0);
                    
                    return null;
                }
            }
            
            return null;
        }
    }
    
    // 应用DOM方法修复
    function applyDomMethodFixes() {
        // 修复appendChild
        Node.prototype.appendChild = function(child) {
            if (!child) {
                console.error('appendChild: 子节点为null或undefined');
                return null;
            }
            
            // 防止相同节点循环添加
            if (this === child) {
                console.error('appendChild: 尝试将节点添加到自身，阻止操作');
                return null;
            }
            
            // 检查child是否已经在this的子节点中
            if (this.contains && this.contains(child)) {
                console.warn('appendChild: 子节点已经是父节点的子节点，避免重复添加');
                return child;
            }
            
            return safelyExecuteDom.call(this, () => {
                return originalMethods.appendChild.call(this, child);
            }, [child], 'appendChild');
        };
        
        // 修复replaceChild
        Node.prototype.replaceChild = function(newChild, oldChild) {
            if (!newChild) {
                console.error('replaceChild: 新子节点为null或undefined');
                return null;
            }
            
            if (!oldChild) {
                console.error('replaceChild: 旧子节点为null或undefined');
                return null;
            }
            
            // 防止相同节点循环替换
            if (newChild === oldChild) {
                console.warn('replaceChild: 新节点与旧节点相同，跳过替换');
                return oldChild;
            }
            
            return safelyExecuteDom.call(this, () => {
                return originalMethods.replaceChild.call(this, newChild, oldChild);
            }, [newChild, oldChild], 'replaceChild');
        };
        
        // 修复removeChild
        Node.prototype.removeChild = function(child) {
            if (!child) {
                console.error('removeChild: 子节点为null或undefined');
                return null;
            }
            
            return safelyExecuteDom.call(this, () => {
                return originalMethods.removeChild.call(this, child);
            }, [child], 'removeChild');
        };
        
        // 修复insertBefore
        Node.prototype.insertBefore = function(newNode, referenceNode) {
            if (!newNode) {
                console.error('insertBefore: 新节点为null或undefined');
                return null;
            }
            
            return safelyExecuteDom.call(this, () => {
                return originalMethods.insertBefore.call(this, newNode, referenceNode);
            }, [newNode, referenceNode], 'insertBefore');
        };
    }
    
    // 检查核心静态文件
    function checkCoreStaticFiles() {
        console.log('开始检查核心静态文件');
        
        // 核心静态文件列表
        const coreFiles = [
            { type: 'css', name: 'bootstrap.min.css', path: '/static/css/lib/bootstrap.min.css', fallbackPath: '/direct-static/css/lib/bootstrap.min.css' },
            { type: 'js', name: 'jquery.min.js', path: '/static/js/lib/jquery.min.js', fallbackPath: '/direct-static/js/lib/jquery.min.js' },
            { type: 'js', name: 'bootstrap.bundle.min.js', path: '/static/js/lib/bootstrap.bundle.min.js', fallbackPath: '/direct-static/js/lib/bootstrap.bundle.min.js' },
            { type: 'js', name: 'chart.min.js', path: '/static/js/lib/chart.min.js', fallbackPath: '/direct-static/js/lib/chart.min.js' }
        ];
        
        // 创建加载队列，按顺序加载
        const loadQueue = [];
        
        // 检查文件是否已加载
        coreFiles.forEach(file => {
            let isLoaded = false;
            
            if (file.type === 'css') {
                // 检查CSS是否已加载
                const styleSheets = document.styleSheets;
                for (let i = 0; i < styleSheets.length; i++) {
                    const sheet = styleSheets[i];
                    if (sheet.href && (sheet.href.includes(file.name))) {
                        isLoaded = true;
                        break;
                    }
                }
            } else if (file.type === 'js') {
                // 检查JS是否已加载
                const scripts = document.getElementsByTagName('script');
                for (let i = 0; i < scripts.length; i++) {
                    if (scripts[i].src && scripts[i].src.includes(file.name)) {
                        isLoaded = true;
                        break;
                    }
                }
                
                // jQuery特殊检查
                if (file.name === 'jquery.min.js' && typeof jQuery !== 'undefined') {
                    isLoaded = true;
                }
                
                // Chart.js特殊检查
                if (file.name === 'chart.min.js' && typeof Chart !== 'undefined') {
                    isLoaded = true;
                }
                
                // Bootstrap特殊检查
                if (file.name === 'bootstrap.bundle.min.js' && typeof bootstrap !== 'undefined') {
                    isLoaded = true;
                }
            }
            
            // 如果未加载，添加到加载队列
            if (!isLoaded) {
                console.log(`${file.name} 未加载，尝试加载`);
                loadQueue.push(file);
            }
        });
        
        // 按顺序加载文件
        function loadNextFile() {
            if (loadQueue.length === 0) {
                return;
            }
            
            const file = loadQueue.shift();
            let element;
            
            if (file.type === 'css') {
                element = document.createElement('link');
                element.rel = 'stylesheet';
                element.href = file.path;
            } else if (file.type === 'js') {
                element = document.createElement('script');
                element.src = file.path;
                element.async = false;
            }
            
            if (element) {
                // 添加加载完成回调
                element.onload = function() {
                    console.log(`${file.name} 加载成功`);
                    loadNextFile(); // 加载下一个文件
                };
                
                // 添加加载失败回调
                element.onerror = function() {
                    console.error(`${file.name} 加载失败，尝试备用路径`);
                    
                    // 创建备用元素
                    let fallbackElement;
                    
                    if (file.type === 'css') {
                        fallbackElement = document.createElement('link');
                        fallbackElement.rel = 'stylesheet';
                        fallbackElement.href = file.fallbackPath;
                    } else if (file.type === 'js') {
                        fallbackElement = document.createElement('script');
                        fallbackElement.src = file.fallbackPath;
                        fallbackElement.async = false;
                    }
                    
                    // 添加备用元素加载完成回调
                    if (fallbackElement) {
                        fallbackElement.onload = function() {
                            console.log(`${file.name} 通过备用路径加载成功`);
                            loadNextFile(); // 加载下一个文件
                        };
                        
                        fallbackElement.onerror = function() {
                            console.error(`${file.name} 备用路径也加载失败`);
                            loadNextFile(); // 继续加载下一个文件
                        };
                        
                        // 安全地添加到文档
                        try {
                            document.head.appendChild(fallbackElement);
                        } catch (e) {
                            console.error(`添加备用${file.name}时出错:`, e.message);
                            loadNextFile(); // 继续加载下一个文件
                        }
                    } else {
                        loadNextFile(); // 继续加载下一个文件
                    }
                };
                
                // 安全地添加到文档
                try {
                    document.head.appendChild(element);
                } catch (e) {
                    console.error(`添加${file.name}时出错:`, e.message);
                    loadNextFile(); // 继续加载下一个文件
                }
            } else {
                loadNextFile(); // 继续加载下一个文件
            }
        }
        
        // 开始加载队列
        if (loadQueue.length > 0) {
            loadNextFile();
        }
    }
    
    // 添加全局错误处理
    function addGlobalErrorHandler() {
        // 保存原始的window.onerror
        const originalOnError = window.onerror;
        
        // 设置新的错误处理函数
        window.onerror = function(message, source, lineno, colno, error) {
            // 处理堆栈溢出错误
            if (message && message.includes('Maximum call stack size exceeded')) {
                console.error('捕获到堆栈溢出错误，重置DOM方法');
                
                // 重置DOM方法
                resetAllDomMethods();
                
                // 延迟重新应用修复
                setTimeout(function() {
                    applyDomMethodFixes();
                }, 10);
                
                // 阻止错误继续传播
                return true;
            }
            
            // 处理appendChild相关错误
            if (message && message.includes('appendChild') && message.includes('null')) {
                console.error('捕获到appendChild空对象错误，已阻止');
                return true;
            }
            
            // 调用原始错误处理函数
            if (typeof originalOnError === 'function') {
                return originalOnError(message, source, lineno, colno, error);
            }
            
            // 默认不阻止
            return false;
        };
        
        // 添加unhandledrejection处理
        window.addEventListener('unhandledrejection', function(event) {
            if (event.reason && event.reason.message && 
                event.reason.message.includes('Maximum call stack size exceeded')) {
                console.error('捕获到Promise中的堆栈溢出错误，重置DOM方法');
                
                // 重置DOM方法
                resetAllDomMethods();
                
                // 延迟重新应用修复
                setTimeout(function() {
                    applyDomMethodFixes();
                }, 10);
                
                // 阻止错误传播
                event.preventDefault();
            }
        });
    }
    
    // 修复创建的脚本中的堆栈溢出问题
    function fixScriptsTags() {
        const scripts = document.querySelectorAll('script:not([src])');
        
        scripts.forEach(function(script) {
            const content = script.textContent;
            
            // 检查脚本内容是否包含可能导致堆栈溢出的模式
            if (content && (
                content.includes('function(') && content.match(/function\s*\(\s*\)\s*{\s*return\s*[^;]*\(\s*\)/) ||
                content.match(/\(\s*function\s*\(\s*\)\s*{\s*return\s*[^;]*\(\s*\)/)
            )) {
                console.warn('检测到可能导致堆栈溢出的脚本模式，尝试修复');
                
                // 创建一个新的脚本元素
                const newScript = document.createElement('script');
                
                // 修改内容，添加递归检测
                let fixedContent = content.replace(
                    /function\s*\(\s*\)\s*{\s*return\s*([^;]*)\(\s*\)/g,
                    'function() { window.__recursionCount = window.__recursionCount || 0; window.__recursionCount++; if (window.__recursionCount > 5) { window.__recursionCount = 0; console.error("检测到递归调用，已阻止"); return null; } var result = $1(); window.__recursionCount--; return result;'
                );
                
                fixedContent = fixedContent.replace(
                    /\(\s*function\s*\(\s*\)\s*{\s*return\s*([^;]*)\(\s*\)/g,
                    '(function() { window.__recursionCount = window.__recursionCount || 0; window.__recursionCount++; if (window.__recursionCount > 5) { window.__recursionCount = 0; console.error("检测到递归调用，已阻止"); return null; } var result = $1(); window.__recursionCount--; return result;'
                );
                
                newScript.textContent = fixedContent;
                
                // 替换原始脚本
                if (script.parentNode) {
                    try {
                        script.parentNode.replaceChild(newScript, script);
                    } catch (e) {
                        console.error('替换脚本时出错:', e.message);
                    }
                }
            }
        });
    }
    
    // 主函数
    function main() {
        // 定义全局result变量，防止"result is not defined"错误
        if (typeof window.result === 'undefined') {
            window.result = { status: 'error', message: '未知错误' };
            console.log('预防性定义了全局result变量');
        }
        
        // 先添加全局错误处理
        addGlobalErrorHandler();
        
        // 修复DOM方法
        applyDomMethodFixes();
        
        // 检查核心静态文件
        checkCoreStaticFiles();
        
        // 在DOM加载完成后执行其他修复
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM加载完成，检查内联脚本');
                fixScriptsTags();
            });
        } else {
            console.log('DOM已加载，立即检查内联脚本');
            fixScriptsTags();
        }
        
        // 页面完全加载后再次检查
        window.addEventListener('load', function() {
            console.log('页面完全加载，再次应用修复');
            applyDomMethodFixes();
        });
    }
    
    // 执行主函数
    main();
})();
