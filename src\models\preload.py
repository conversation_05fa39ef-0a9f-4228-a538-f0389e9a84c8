"""
九猫系统模型预加载模块
确保所有模型在应用启动前正确加载和注册
"""
import logging
import importlib
import sys

logger = logging.getLogger(__name__)

def preload_all_models():
    """
    预加载所有模型，确保它们在应用启动前正确注册

    Returns:
        bool: 是否成功加载所有模型
    """
    logger.info("开始预加载所有数据库模型...")

    # 需要按顺序加载的模型列表
    model_modules = [
        "src.models.base",
        "src.models.novel",
        "src.models.analysis_result",
        "src.models.analysis_checkpoint",
        "src.models.intermediate_result",
        "src.models.analysis_process",
        "src.models.api_log",
        "src.models.system_alert",
        "src.models.system_metric",
        "src.models.reference_template",
        "src.models.generated_content",
        "src.models.preset"
    ]

    # 记录成功加载的模型
    loaded_models = []

    # 尝试加载所有模型
    for module_name in model_modules:
        try:
            # 检查模块是否已加载
            if module_name in sys.modules:
                logger.debug(f"模型模块 {module_name} 已加载")
                loaded_models.append(module_name)
                continue

            # 导入模块
            module = importlib.import_module(module_name)

            # 获取模块中的所有类
            for attr_name in dir(module):
                if attr_name.startswith('_'):
                    continue

                attr = getattr(module, attr_name)

                # 检查是否是类
                if isinstance(attr, type):
                    logger.debug(f"已加载模型类: {module_name}.{attr_name}")

            loaded_models.append(module_name)
            logger.info(f"成功加载模型模块: {module_name}")
        except ImportError as e:
            logger.error(f"导入模型模块 {module_name} 失败: {str(e)}")
        except Exception as e:
            logger.error(f"加载模型模块 {module_name} 时出错: {str(e)}")

    # 检查是否所有模型都已加载
    success = len(loaded_models) == len(model_modules)

    if success:
        logger.info(f"所有模型模块 ({len(loaded_models)}/{len(model_modules)}) 已成功预加载")
    else:
        logger.warning(f"只有部分模型模块 ({len(loaded_models)}/{len(model_modules)}) 成功预加载")

    return success

def get_model_classes():
    """
    获取所有已加载的模型类

    Returns:
        dict: 模型名称到模型类的映射
    """
    from src.models import Base

    # 获取所有继承自Base的模型类
    model_classes = {}

    # 遍历所有已注册的映射器
    for mapper in Base.registry.mappers:
        # 获取映射器对应的类
        model_class = mapper.class_
        # 获取类名
        class_name = model_class.__name__
        # 添加到字典
        model_classes[class_name] = model_class

    return model_classes

# 在模块导入时自动预加载所有模型
if __name__ != "__main__":
    preload_all_models()
