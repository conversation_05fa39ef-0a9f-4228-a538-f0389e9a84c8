{"name": "@npmcli/run-script", "version": "3.0.2", "description": "Run a lifecycle script for a package (descendant of npm-lifecycle)", "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "snap": "tap", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "minipass": "^3.1.6", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "dependencies": {"@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "node-gyp": "^9.0.0", "read-package-json-fast": "^2.0.3"}, "files": ["bin/", "lib/"], "main": "lib/run-script.js", "repository": {"type": "git", "url": "https://github.com/npm/run-script.git"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}}