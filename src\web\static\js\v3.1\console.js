/**
 * 九猫小说分析写作系统v3.1 - 控制台JavaScript
 */

// 全局变量
let selectedTemplateId = null;  // 当前选中的参考蓝本ID
let selectedDimension = null;   // 当前选中的分析维度
let selectedChapterId = null;   // 当前选中的章节ID
let analysisData = {};          // 分析数据缓存
let knowledgeBaseData = {};     // 知识库数据
let presetTemplateData = {};    // 预设模板数据
let apiCallCount = 0;           // API调用计数
let apiCostEstimate = 0;        // API成本估计
let systemStartTime = new Date(); // 系统启动时间

// 在DOM加载完成后执行
$(document).ready(function() {
    console.log('九猫小说分析写作系统v3.1 - 控制台已加载');

    // 初始化运行日志
    initLogs();

    // 初始化系统监控
    initSystemMonitor();

    // 绑定参考蓝本选择事件
    bindTemplateSelection();

    // 绑定标签页切换事件
    bindTabSwitching();

    // 绑定知识库按钮事件
    bindKnowledgeBaseButtons();

    // 绑定自动写作按钮事件
    bindAutoWritingButtons();

    // 添加日志
    addLogEntry('info', '控制台v3.1初始化完成');
});

/**
 * 初始化运行日志
 */
function initLogs() {
    // 添加初始日志
    addLogEntry('info', '控制台v3.1页面已加载');

    // 拦截控制台日志
    const originalConsole = {
        log: console.log,
        warn: console.warn,
        error: console.error,
        info: console.info
    };

    console.log = function(...args) {
        originalConsole.log.apply(console, args);
        addLogEntry('info', args.join(' '));
    };

    console.warn = function(...args) {
        originalConsole.warn.apply(console, args);
        addLogEntry('warn', args.join(' '));
    };

    console.error = function(...args) {
        originalConsole.error.apply(console, args);
        addLogEntry('error', args.join(' '));
    };

    console.info = function(...args) {
        originalConsole.info.apply(console, args);
        addLogEntry('info', args.join(' '));
    };

    // 绑定日志按钮事件
    $('#clearLogsBtn').click(function() {
        $('#logContent').empty();
        addLogEntry('info', '日志已清除');
    });

    $('#refreshLogsBtn').click(function() {
        addLogEntry('info', '日志已刷新');
    });

    // 绑定日志搜索事件
    $('#logSearch').on('input', function() {
        applyLogFilters();
    });

    $('#clearSearchBtn').click(function() {
        $('#logSearch').val('');
        applyLogFilters();
    });
}

/**
 * 添加日志条目
 * @param {string} type 日志类型 (info, warn, error, welcome)
 * @param {string} message 日志消息
 */
function addLogEntry(type, message) {
    const now = new Date();
    const timestamp = now.toTimeString().split(' ')[0];
    const logLine = $('<div></div>')
        .addClass('log-line')
        .addClass('log-' + type);

    const logTimestamp = $('<span></span>')
        .addClass('log-timestamp')
        .text('[' + timestamp + ']');

    logLine.append(logTimestamp);
    logLine.append(' ' + message);

    $('#logContent').append(logLine);

    // 滚动到底部
    const logContainer = document.getElementById('logContent');
    logContainer.scrollTop = logContainer.scrollHeight;

    // 更新API调用计数（如果消息包含API调用信息）
    if (message.includes('API调用') || message.includes('api call')) {
        apiCallCount++;
        updateApiCallCount();
    }
}

/**
 * 应用日志过滤器
 */
function applyLogFilters() {
    const searchText = $('#logSearch').val().toLowerCase();

    $('.log-line').each(function() {
        const lineText = $(this).text().toLowerCase();
        if (searchText === '' || lineText.includes(searchText)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

/**
 * 初始化系统监控
 */
function initSystemMonitor() {
    // 更新系统运行时间
    setInterval(updateSystemUptime, 1000);

    // 更新API调用计数
    updateApiCallCount();
}

/**
 * 更新系统运行时间
 */
function updateSystemUptime() {
    const now = new Date();
    const diff = Math.floor((now - systemStartTime) / 1000);

    const hours = Math.floor(diff / 3600);
    const minutes = Math.floor((diff % 3600) / 60);
    const seconds = diff % 60;

    const uptimeStr =
        (hours < 10 ? '0' + hours : hours) + ':' +
        (minutes < 10 ? '0' + minutes : minutes) + ':' +
        (seconds < 10 ? '0' + seconds : seconds);

    $('#systemUptime').text(uptimeStr);
}

/**
 * 更新API调用计数
 */
function updateApiCallCount() {
    $('#apiCallCount').text(apiCallCount);

    // 估算成本（假设每次API调用成本为0.01元）
    apiCostEstimate = apiCallCount * 0.01;
    $('#apiCostEstimate').text('¥' + apiCostEstimate.toFixed(2));
}

/**
 * 绑定参考蓝本选择事件
 */
function bindTemplateSelection() {
    $('.select-template-btn').click(function() {
        const templateId = $(this).data('template-id');
        selectTemplate(templateId);
    });

    $('.template-card').click(function() {
        const templateId = $(this).data('template-id');
        selectTemplate(templateId);
    });
}

/**
 * 选择参考蓝本
 * @param {number} templateId 参考蓝本ID
 */
function selectTemplate(templateId) {
    selectedTemplateId = templateId;

    // 高亮选中的参考蓝本卡片
    $('.template-card').removeClass('border-primary');
    $(`.template-card[data-template-id="${templateId}"]`).addClass('border-primary');

    // 显示分析内容展示卡片
    $('#analysisDisplayCard').show();

    // 加载章节列表和分析维度
    loadChapters(templateId);

    // 添加日志
    addLogEntry('info', `已选择参考蓝本ID: ${templateId}`);

    // 更新当前任务
    $('#currentTask').text(`分析参考蓝本ID: ${templateId}`);
}

/**
 * 加载章节列表和分析维度
 * @param {number} templateId 参考蓝本ID
 */
function loadChapters(templateId) {
    $('#chapterList').html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><p class="small mt-2">加载中...</p></div>');
    $('#dimensionList').html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><p class="small mt-2">加载中...</p></div>');

    // 调用API获取小说信息、章节列表和分析维度
    $.ajax({
        url: `/v3.1/api/novel/${templateId}`,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                // 缓存分析数据
                analysisData = response;

                // 渲染章节列表
                renderChapterList(response.chapters);

                // 渲染分析维度列表
                renderDimensionList(response.dimensions);

                addLogEntry('info', `成功加载参考蓝本信息: ${response.novel.title}`);
            } else {
                $('#chapterList').html(`<div class="alert alert-danger">${response.error || '加载章节列表失败'}</div>`);
                $('#dimensionList').html(`<div class="alert alert-danger">${response.error || '加载分析维度失败'}</div>`);
                addLogEntry('error', `加载参考蓝本信息失败: ${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#chapterList').html('<div class="alert alert-danger">加载章节列表时出错</div>');
            $('#dimensionList').html('<div class="alert alert-danger">加载分析维度时出错</div>');
            addLogEntry('error', `加载参考蓝本信息失败: ${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 渲染章节列表
 * @param {Array} chapters 章节列表
 */
function renderChapterList(chapters) {
    if (!chapters || chapters.length === 0) {
        $('#chapterList').html('<div class="alert alert-warning">未找到章节</div>');
        return;
    }

    let html = '';
    chapters.forEach(chapter => {
        html += `
            <a href="javascript:void(0)" class="list-group-item list-group-item-action chapter-item" data-chapter-id="${chapter.id}">
                第${chapter.chapter_number}章: ${chapter.title}
                <span class="badge bg-info float-end">${chapter.analysis_count}/15</span>
            </a>
        `;
    });

    $('#chapterList').html(html);

    // 绑定章节点击事件
    $('.chapter-item').click(function() {
        const chapterId = $(this).data('chapter-id');
        selectChapter(chapterId);
    });
}

/**
 * 渲染分析维度列表
 * @param {Array} dimensions 分析维度列表
 */
function renderDimensionList(dimensions) {
    if (!dimensions || dimensions.length === 0) {
        $('#dimensionList').html('<div class="alert alert-warning">未找到分析维度</div>');
        return;
    }

    let html = '';
    dimensions.forEach(dimension => {
        const badgeClass = dimension.is_analyzed ? 'bg-success' : 'bg-secondary';
        html += `
            <a href="javascript:void(0)" class="list-group-item list-group-item-action dimension-item" data-dimension="${dimension.key}">
                <i class="fas fa-${dimension.icon} me-2"></i>${dimension.name}
                <span class="badge ${badgeClass} float-end">${dimension.is_analyzed ? '已分析' : '未分析'}</span>
            </a>
        `;
    });

    $('#dimensionList').html(html);

    // 绑定维度点击事件
    $('.dimension-item').click(function() {
        const dimension = $(this).data('dimension');
        selectDimension(dimension);
    });
}

/**
 * 选择分析维度
 * @param {string} dimension 分析维度
 */
function selectDimension(dimension) {
    selectedDimension = dimension;

    // 高亮选中的维度
    $('.dimension-item').removeClass('active');
    $(`.dimension-item[data-dimension="${dimension}"]`).addClass('active');

    // 加载分析结果
    loadAnalysisResult(selectedTemplateId, dimension);

    // 添加日志
    addLogEntry('info', `已选择分析维度: ${dimension}`);
}

/**
 * 选择章节
 * @param {number} chapterId 章节ID
 */
function selectChapter(chapterId) {
    selectedChapterId = chapterId;

    // 高亮选中的章节
    $('.chapter-item').removeClass('active');
    $(`.chapter-item[data-chapter-id="${chapterId}"]`).addClass('active');

    // 加载章节分析维度
    loadChapterAnalysis(selectedTemplateId, chapterId);

    // 添加日志
    addLogEntry('info', `已选择章节ID: ${chapterId}`);
}

/**
 * 加载章节分析
 * @param {number} templateId 参考蓝本ID
 * @param {number} chapterId 章节ID
 */
function loadChapterAnalysis(templateId, chapterId) {
    // 恢复原始的章节分析内容结构
    const originalContainer = `
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">章节分析维度</h5>
            </div>
            <div class="card-body">
                <div id="chapterAnalysisContent">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status"></div>
                        <p class="mt-3">加载中，请稍候...</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 更新页面内容
    $('#chapterAnalysisContent').closest('.card').replaceWith(originalContainer);

    // 调用API获取章节分析维度
    $.ajax({
        url: `/v3.1/api/novel/${templateId}/chapter/${chapterId}/dimensions`,
        type: 'GET',
        success: function(response) {
            if (response.success && response.dimensions) {
                // 渲染章节分析维度
                renderChapterDimensions(response.dimensions, chapterId);
                addLogEntry('info', `成功加载章节分析维度: 章节ID ${chapterId}`);
            } else {
                $('#chapterAnalysisContent').html(`<div class="alert alert-warning">${response.error || '无法加载章节分析维度'}</div>`);
                addLogEntry('warn', `无法加载章节分析维度: ${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#chapterAnalysisContent').html('<div class="alert alert-danger">加载章节分析维度时出错</div>');
            addLogEntry('error', `加载章节分析维度失败: ${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 渲染章节分析维度
 * @param {Array} dimensions 章节分析维度
 * @param {number} chapterId 章节ID
 */
function renderChapterDimensions(dimensions, chapterId) {
    if (!dimensions || dimensions.length === 0) {
        $('#chapterAnalysisContent').html('<div class="alert alert-warning">未找到章节分析维度</div>');
        return;
    }

    let html = '<div class="row">';

    // 添加维度列表
    dimensions.forEach(dimension => {
        const badgeClass = dimension.is_analyzed ? 'bg-success' : 'bg-secondary';
        html += `
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">${dimension.name}</h5>
                        <p class="card-text">
                            <span class="badge ${badgeClass}">${dimension.is_analyzed ? '已分析' : '未分析'}</span>
                        </p>
                        <button class="btn btn-sm btn-outline-primary view-chapter-analysis-btn"
                                data-chapter-id="${chapterId}"
                                data-dimension="${dimension.key}"
                                ${!dimension.is_analyzed ? 'disabled' : ''}>
                            查看分析
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';

    $('#chapterAnalysisContent').html(html);

    // 绑定查看章节分析按钮事件
    $('.view-chapter-analysis-btn').click(function() {
        const chapterId = $(this).data('chapter-id');
        const dimension = $(this).data('dimension');
        loadChapterAnalysisResult(selectedTemplateId, chapterId, dimension);
    });
}

/**
 * 加载章节分析结果
 * @param {number} templateId 模板ID
 * @param {number} chapterId 章节ID
 * @param {string} dimension 分析维度
 */
function loadChapterAnalysisResult(templateId, chapterId, dimension) {
    $('#chapterAnalysisContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中...</p></div>');
    $('#chapterReasoningContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中...</p></div>');

    // 添加加载日志
    addLogEntry('info', `加载章节分析结果：模板ID=${templateId}, 章节ID=${chapterId}, 维度=${dimension}`);

    // 调用API获取章节分析结果
    $.ajax({
        url: `/v3.1/api/template/${templateId}/chapter/${chapterId}/analysis/${dimension}`,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                // 渲染分析结果
                $('#chapterAnalysisContent').html(marked.parse(response.content));
                addLogEntry('info', `成功加载章节分析结果：${dimension}`);
            } else {
                $('#chapterAnalysisContent').html(`<div class="alert alert-warning">${response.error || '加载分析结果失败'}</div>`);
                addLogEntry('warn', `加载章节分析结果失败：${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#chapterAnalysisContent').html('<div class="alert alert-danger">加载分析结果时出错</div>');
            addLogEntry('error', `加载章节分析结果失败：${xhr.status} ${xhr.statusText}`);
        }
    });

    // 调用API获取章节推理过程
    $.ajax({
        url: `/v3.1/api/template/${templateId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                // 渲染推理过程
                $('#chapterReasoningContent').html(marked.parse(response.content));
                addLogEntry('info', `成功加载章节推理过程：${dimension}`);
            } else {
                $('#chapterReasoningContent').html(`<div class="alert alert-warning">${response.error || '加载推理过程失败'}</div>`);
                addLogEntry('warn', `加载章节推理过程失败：${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#chapterReasoningContent').html('<div class="alert alert-danger">加载推理过程时出错</div>');
            addLogEntry('error', `加载章节推理过程失败：${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 加载分析结果
 * @param {number} templateId 模板ID
 * @param {string} dimension 分析维度
 */
function loadAnalysisResult(templateId, dimension) {
    $('#analysisContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中...</p></div>');
    $('#reasoningContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中...</p></div>');

    // 添加加载日志
    addLogEntry('info', `加载分析结果：模板ID=${templateId}, 维度=${dimension}`);

    // 调用API获取分析结果
    $.ajax({
        url: `/v3.1/api/template/${templateId}/analysis/${dimension}`,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                // 渲染分析结果
                $('#analysisContent').html(marked.parse(response.content));
                addLogEntry('info', `成功加载分析结果：${dimension}`);
            } else {
                $('#analysisContent').html(`<div class="alert alert-warning">${response.error || '加载分析结果失败'}</div>`);
                addLogEntry('warn', `加载分析结果失败：${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#analysisContent').html('<div class="alert alert-danger">加载分析结果时出错</div>');
            addLogEntry('error', `加载分析结果失败：${xhr.status} ${xhr.statusText}`);
        }
    });

    // 调用API获取推理过程
    $.ajax({
        url: `/v3.1/api/template/${templateId}/analysis/${dimension}/reasoning_content`,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                // 渲染推理过程
                $('#reasoningContent').html(marked.parse(response.content));
                addLogEntry('info', `成功加载推理过程：${dimension}`);
            } else {
                $('#reasoningContent').html(`<div class="alert alert-warning">${response.error || '加载推理过程失败'}</div>`);
                addLogEntry('warn', `加载推理过程失败：${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#reasoningContent').html('<div class="alert alert-danger">加载推理过程时出错</div>');
            addLogEntry('error', `加载推理过程失败：${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 绑定标签页切换事件
 */
function bindTabSwitching() {
    $('#consoleTab button').on('shown.bs.tab', function (e) {
        const targetId = $(e.target).attr('id');
        if (targetId === 'logs-tab') {
            addLogEntry('info', '切换到运行日志标签页');
        } else if (targetId === 'analysis-tab') {
            addLogEntry('info', '切换到分析展示标签页');
        } else if (targetId === 'knowledge-tab') {
            addLogEntry('info', '切换到知识库标签页');
        } else if (targetId === 'writing-tab') {
            addLogEntry('info', '切换到自动写作标签页');
        }
    });
}

/**
 * 绑定知识库按钮事件
 */
function bindKnowledgeBaseButtons() {
    // 读取分析结果按钮
    $('#readAnalysisBtn').click(function() {
        if (!selectedTemplateId) {
            addLogEntry('warn', '请先在分析展示中选择参考蓝本');
            $('#knowledgeBaseContent').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>请先在分析展示中选择参考蓝本</div>');
            return;
        }

        readAnalysisResults(selectedTemplateId);
    });

    // 转化为预设模板按钮
    $('#convertToTemplateBtn').click(function() {
        if (Object.keys(knowledgeBaseData).length === 0) {
            addLogEntry('warn', '请先读取分析结果');
            $('#presetTemplateContent').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>请先读取分析结果</div>');
            return;
        }

        convertToPresetTemplate();
    });
}

/**
 * 读取分析结果
 * @param {number} templateId 参考蓝本ID
 */
function readAnalysisResults(templateId) {
    $('#knowledgeBaseContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">读取中，请稍候...</p></div>');

    // 添加详细日志
    addLogEntry('info', `开始读取参考蓝本(ID: ${templateId})的分析结果...`);
    addLogEntry('info', `正在调用API: /v3.1/api/template/${templateId}/analysis`);

    // 调用API获取参考蓝本分析结果
    $.ajax({
        url: `/v3.1/api/template/${templateId}/analysis`,
        type: 'GET',
        beforeSend: function() {
            addLogEntry('info', '正在发送API请求...');
        },
        success: function(response) {
            if (response.success) {
                // 缓存知识库数据
                knowledgeBaseData = response;

                // 添加详细日志
                addLogEntry('info', `成功读取参考蓝本《${response.template.title}》的分析结果`);
                addLogEntry('info', `整本书分析维度: ${response.book_analyses.length}个`);
                addLogEntry('info', `章节数量: ${response.chapters.length}个`);

                // 记录每个维度
                if (response.book_analyses && response.book_analyses.length > 0) {
                    const dimensions = response.book_analyses.map(a => a.dimension).join(', ');
                    addLogEntry('info', `整本书分析维度列表: ${dimensions}`);
                }

                // 记录章节分析情况
                if (response.chapters && response.chapters.length > 0) {
                    response.chapters.forEach(chapter => {
                        const analysisCount = chapter.analysis_results ? chapter.analysis_results.length : 0;
                        addLogEntry('info', `章节${chapter.chapter_number}: ${chapter.title} - 已分析${analysisCount}/15个维度`);
                    });
                }

                // 渲染知识库内容
                renderKnowledgeBaseContent(response);

                addLogEntry('success', `参考蓝本《${response.template.title}》的分析结果读取完成`);
            } else {
                $('#knowledgeBaseContent').html(`<div class="alert alert-danger">${response.error || '无法读取分析结果'}</div>`);
                addLogEntry('error', `读取参考蓝本分析结果失败: ${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#knowledgeBaseContent').html('<div class="alert alert-danger">读取分析结果时出错</div>');
            addLogEntry('error', `读取参考蓝本分析结果失败: ${xhr.status} ${xhr.statusText}`);

            // 添加详细错误日志
            if (xhr.responseText) {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.error) {
                        addLogEntry('error', `错误详情: ${errorResponse.error}`);
                    }
                } catch (e) {
                    addLogEntry('error', `错误响应: ${xhr.responseText.substring(0, 200)}...`);
                }
            }
        }
    });
}

/**
 * 渲染知识库内容
 * @param {Object} data 知识库数据
 */
function renderKnowledgeBaseContent(data) {
    let html = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            成功读取参考蓝本《${data.template.title}》的分析结果
        </div>

        <div class="card mb-3">
            <div class="card-header bg-light">
                <h5 class="mb-0">整本书分析结果</h5>
            </div>
            <div class="card-body">
                <p>已读取 ${data.book_analyses.length} 个维度的分析结果</p>
                <div class="progress mb-3">
                    <div class="progress-bar bg-success" role="progressbar" style="width: ${(data.book_analyses.length / 15) * 100}%" aria-valuenow="${data.book_analyses.length}" aria-valuemin="0" aria-valuemax="15"></div>
                </div>
                <div class="accordion" id="bookAnalysesAccordion">
    `;

    // 添加维度列表和详情
    data.book_analyses.forEach((analysis, index) => {
        html += `
            <div class="accordion-item">
                <h2 class="accordion-header" id="book-dimension-heading-${index}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#book-dimension-collapse-${index}" aria-expanded="false" aria-controls="book-dimension-collapse-${index}">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <span>${analysis.dimension}</span>
                    </button>
                </h2>
                <div id="book-dimension-collapse-${index}" class="accordion-collapse collapse" aria-labelledby="book-dimension-heading-${index}" data-bs-parent="#bookAnalysesAccordion">
                    <div class="accordion-body">
                        <div class="card mb-3">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">分析结果</h6>
                                <button class="btn btn-sm btn-outline-secondary copy-btn" data-content="${encodeURIComponent(analysis.result)}">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="analysis-result">${marked.parse(analysis.result || '暂无分析结果')}</div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">推理过程</h6>
                                <button class="btn btn-sm btn-outline-secondary copy-btn" data-content="${encodeURIComponent(analysis.reasoning_content)}">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="reasoning-content">${marked.parse(analysis.reasoning_content || '暂无推理过程')}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    // 添加思考过程部分（如果有）
    if (data.thinking_process) {
        html += `
            <div class="card mb-3">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">整本书思考过程</h5>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" data-content="${encodeURIComponent(data.thinking_process)}">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                </div>
                <div class="card-body">
                    <button class="btn btn-sm btn-outline-secondary w-100 mb-2 thinking-process-toggle">
                        <i class="fas fa-chevron-down me-1"></i>展开思考过程
                    </button>
                    <div class="thinking-process-container" style="display: none;">
                        <div class="thinking-process">${marked.parse(data.thinking_process)}</div>
                    </div>
                </div>
            </div>
        `;
    }

    html += `
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">章节分析结果</h5>
            </div>
            <div class="card-body">
                <p>已读取 ${data.chapters.length} 个章节的分析结果</p>
                <div class="accordion" id="chaptersAccordion">
    `;

    // 添加章节列表
    data.chapters.forEach((chapter, index) => {
        const analysisCount = chapter.analysis_results ? chapter.analysis_results.length : 0;
        const progressPercent = (analysisCount / 15) * 100;

        html += `
            <div class="accordion-item">
                <h2 class="accordion-header" id="chapter-heading-${index}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter-collapse-${index}" aria-expanded="false" aria-controls="chapter-collapse-${index}">
                        第${chapter.chapter_number}章: ${chapter.title}
                        <span class="badge bg-info ms-2">${analysisCount}/15</span>
                    </button>
                </h2>
                <div id="chapter-collapse-${index}" class="accordion-collapse collapse" aria-labelledby="chapter-heading-${index}" data-bs-parent="#chaptersAccordion">
                    <div class="accordion-body">
                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" role="progressbar" style="width: ${progressPercent}%" aria-valuenow="${analysisCount}" aria-valuemin="0" aria-valuemax="15"></div>
                        </div>

                        <!-- 章节维度分析结果 -->
                        <div class="accordion" id="chapter-${chapter.id}-dimensions-accordion">
        `;

        // 添加章节维度列表和详情
        if (chapter.analysis_results && chapter.analysis_results.length > 0) {
            chapter.analysis_results.forEach((result, dimIndex) => {
                html += `
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="chapter-${chapter.id}-dimension-heading-${dimIndex}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#chapter-${chapter.id}-dimension-collapse-${dimIndex}"
                                    aria-expanded="false"
                                    aria-controls="chapter-${chapter.id}-dimension-collapse-${dimIndex}">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>${result.dimension}</span>
                            </button>
                        </h2>
                        <div id="chapter-${chapter.id}-dimension-collapse-${dimIndex}"
                             class="accordion-collapse collapse"
                             aria-labelledby="chapter-${chapter.id}-dimension-heading-${dimIndex}"
                             data-bs-parent="#chapter-${chapter.id}-dimensions-accordion">
                            <div class="accordion-body">
                                <div class="card mb-3">
                                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">分析结果</h6>
                                        <button class="btn btn-sm btn-outline-secondary copy-btn" data-content="${encodeURIComponent(result.result)}">
                                            <i class="fas fa-copy"></i> 复制
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="analysis-result">${marked.parse(result.result || '暂无分析结果')}</div>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">推理过程</h6>
                                        <button class="btn btn-sm btn-outline-secondary copy-btn" data-content="${encodeURIComponent(result.reasoning_content)}">
                                            <i class="fas fa-copy"></i> 复制
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="reasoning-content">${marked.parse(result.reasoning_content || '暂无推理过程')}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
        } else {
            html += `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    未找到章节分析结果
                </div>
            `;
        }

        html += `
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    $('#knowledgeBaseContent').html(html);

    // 绑定复制按钮事件
    $('.copy-btn').click(function() {
        const content = decodeURIComponent($(this).data('content'));
        navigator.clipboard.writeText(content).then(() => {
            addLogEntry('info', '内容已复制到剪贴板');
            // 显示复制成功提示
            $(this).html('<i class="fas fa-check"></i> 已复制');
            setTimeout(() => {
                $(this).html('<i class="fas fa-copy"></i> 复制');
            }, 2000);
        }).catch(err => {
            addLogEntry('error', `复制失败: ${err}`);
        });
    });

    // 绑定思考过程折叠/展开按钮事件
    $('.thinking-process-toggle').click(function() {
        const container = $(this).next('.thinking-process-container');
        container.toggle();

        if (container.is(':visible')) {
            $(this).html('<i class="fas fa-chevron-up me-1"></i>收起思考过程');
        } else {
            $(this).html('<i class="fas fa-chevron-down me-1"></i>展开思考过程');
        }
    });

    // 应用知识库紧凑显示
    setTimeout(function() {
        if (typeof initKnowledgeBaseCompact === 'function') {
            addLogEntry('info', '应用知识库紧凑显示样式');
            initKnowledgeBaseCompact();
        } else {
            addLogEntry('warn', '知识库紧凑显示脚本未加载');
        }
    }, 500);
}

/**
 * 转化为预设模板
 */
function convertToPresetTemplate() {
    $('#presetTemplateContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">转化中，请稍候...</p></div>');

    // 添加日志
    addLogEntry('info', '开始转化为预设模板');
    addLogEntry('info', `参考蓝本ID: ${selectedTemplateId}`);

    // 检查知识库数据是否有效
    if (!knowledgeBaseData || Object.keys(knowledgeBaseData).length === 0) {
        addLogEntry('warn', '知识库数据无效，尝试使用默认数据');

        // 创建默认知识库数据
        knowledgeBaseData = {
            template: {
                id: selectedTemplateId,
                title: '未知模板'
            },
            book_analyses: [],
            chapters: []
        };
    }

    try {
        // 调用API转化为预设模板
        $.ajax({
            url: '/v3.1/api/convert_to_template',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                template_id: selectedTemplateId,
                knowledge_base_data: knowledgeBaseData
            }),
            success: function(response) {
                if (response.success) {
                    // 缓存预设模板数据
                    presetTemplateData = response;

                    // 渲染预设模板内容
                    renderPresetTemplateContent(response);

                    addLogEntry('info', '成功转化为预设模板');
                } else {
                    addLogEntry('warn', `API返回失败: ${response.error || '未知错误'}`);
                    addLogEntry('info', '尝试使用本地数据生成预设模板');

                    // 创建本地预设模板数据
                    const localTemplateData = {
                        success: true,
                        template: {
                            id: selectedTemplateId,
                            title: knowledgeBaseData.template ? knowledgeBaseData.template.title : '未知模板'
                        },
                        dimensions: knowledgeBaseData.book_analyses ? knowledgeBaseData.book_analyses.map(a => a.dimension) : [],
                        chapters: knowledgeBaseData.chapters || []
                    };

                    // 缓存预设模板数据
                    presetTemplateData = localTemplateData;

                    // 渲染预设模板内容
                    renderPresetTemplateContent(localTemplateData);

                    addLogEntry('info', '使用本地数据成功生成预设模板');
                }
            },
            error: function(xhr) {
                addLogEntry('error', `API调用失败: ${xhr.status} ${xhr.statusText}`);
                addLogEntry('info', '尝试使用本地数据生成预设模板');

                // 创建本地预设模板数据
                const localTemplateData = {
                    success: true,
                    template: {
                        id: selectedTemplateId,
                        title: knowledgeBaseData.template ? knowledgeBaseData.template.title : '未知模板'
                    },
                    dimensions: knowledgeBaseData.book_analyses ? knowledgeBaseData.book_analyses.map(a => a.dimension) : [],
                    chapters: knowledgeBaseData.chapters || []
                };

                // 缓存预设模板数据
                presetTemplateData = localTemplateData;

                // 渲染预设模板内容
                renderPresetTemplateContent(localTemplateData);

                addLogEntry('info', '使用本地数据成功生成预设模板');
            }
        });
    } catch (e) {
        addLogEntry('error', `转化为预设模板时出错: ${e.message}`);

        // 创建本地预设模板数据
        const localTemplateData = {
            success: true,
            template: {
                id: selectedTemplateId,
                title: knowledgeBaseData.template ? knowledgeBaseData.template.title : '未知模板'
            },
            dimensions: knowledgeBaseData.book_analyses ? knowledgeBaseData.book_analyses.map(a => a.dimension) : [],
            chapters: knowledgeBaseData.chapters || []
        };

        // 缓存预设模板数据
        presetTemplateData = localTemplateData;

        // 渲染预设模板内容
        renderPresetTemplateContent(localTemplateData);

        addLogEntry('info', '使用本地数据成功生成预设模板');
    }
}

/**
 * 绑定自动写作按钮事件
 */
function bindAutoWritingButtons() {
    // 开始写作按钮
    $('#startWritingBtn').click(function() {
        const templateId = $('#templateSelect').val();
        const prompt = $('#writingPrompt').val();

        if (!templateId) {
            addLogEntry('warn', '请选择预设模板');
            return;
        }

        startAutoWriting(templateId, prompt);
    });
}

/**
 * 渲染预设模板内容
 * @param {Object} data 预设模板数据
 */
function renderPresetTemplateContent(data) {
    if (!data || !data.template) {
        console.warn('预设模板数据无效，尝试修复');

        // 尝试从全局变量中获取数据
        if (selectedTemplateId && knowledgeBaseData) {
            console.log('从全局变量中获取数据');

            // 构建模板数据
            data = {
                success: true,
                template: {
                    id: selectedTemplateId,
                    title: knowledgeBaseData.template ? knowledgeBaseData.template.title : '未知模板'
                },
                dimensions: knowledgeBaseData.book_analyses ? knowledgeBaseData.book_analyses.map(a => a.dimension) : [],
                chapters: knowledgeBaseData.chapters || []
            };

            console.log('已构建模板数据:', data);
        } else {
            $('#presetTemplateContent').html('<div class="alert alert-warning">预设模板数据无效</div>');
            return;
        }
    }

    // 获取预设ID
    const presetId = data.preset_id || (data.template ? data.template.id : null);
    if (!presetId) {
        $('#presetTemplateContent').html('<div class="alert alert-warning">无法获取预设ID</div>');
        return;
    }

    let html = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            成功转化为预设模板：${data.template.title}
        </div>

        <div class="card mb-3">
            <div class="card-header bg-light">
                <h5 class="mb-0">预设模板信息</h5>
            </div>
            <div class="card-body">
                <p><strong>标题：</strong>${data.template.title}</p>
                <p><strong>创建时间：</strong>${new Date().toLocaleString()}</p>
                <p><strong>维度数量：</strong>${data.book_templates ? Object.keys(data.book_templates).length : (data.dimensions ? data.dimensions.length : 0)}</p>
                <p><strong>章节数量：</strong>${data.chapter_templates ? Object.keys(data.chapter_templates).length : (data.chapters ? data.chapters.length : 0)}</p>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">使用说明</h5>
            </div>
            <div class="card-body">
                <p>预设模板已成功创建，您可以通过以下方式使用：</p>
                <ol>
                    <li>在自动写作标签页中选择此预设模板</li>
                    <li>在知识库页面查看详细的预设内容</li>
                    <li>将预设模板导出为Markdown文件，方便离线使用</li>
                </ol>
                <div class="d-grid gap-2 mt-3">
                    <a href="/v3.1/preset/${presetId}/templates" class="btn btn-primary">
                        <i class="fas fa-eye me-1"></i>查看预设模板详情
                    </a>
                    <button class="btn btn-outline-secondary export-preset-btn" data-preset-id="${presetId}">
                        <i class="fas fa-download me-1"></i>导出预设模板
                    </button>
                </div>
            </div>
        </div>
    `;

    $('#presetTemplateContent').html(html);

    // 绑定导出按钮事件
    $('.export-preset-btn').click(function() {
        const presetId = $(this).data('preset-id');
        exportPresetTemplate(presetId);
    });
}

/**
 * 导出预设模板
 * @param {number} presetId 预设模板ID
 */
function exportPresetTemplate(presetId) {
    addLogEntry('info', `导出预设模板：ID ${presetId}`);

    // 调用API获取预设模板详情
    $.ajax({
        url: `/v3.1/preset/${presetId}`,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                // 生成Markdown内容
                let markdown = `# 预设模板：${response.preset.title}\n\n`;
                markdown += `## 基本信息\n`;
                markdown += `- 创建时间：${new Date(response.preset.created_at).toLocaleString()}\n`;
                markdown += `- 更新时间：${new Date(response.preset.updated_at).toLocaleString()}\n\n`;

                // 添加正文内容
                markdown += response.preset.content || '';

                // 创建下载链接
                const blob = new Blob([markdown], { type: 'text/markdown' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `preset_${presetId}_${response.preset.title}.md`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                addLogEntry('success', `预设模板导出成功：${response.preset.title}`);
            } else {
                addLogEntry('error', `预设模板导出失败：${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            addLogEntry('error', `预设模板导出失败：${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 开始自动写作
 * @param {number} templateId 预设模板ID
 * @param {string} prompt 写作提示
 */
function startAutoWriting(templateId, prompt) {
    $('#writingResult').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">生成中，请稍候...</p></div>');

    // 调用API开始自动写作
    $.ajax({
        url: '/v3.1/auto_write',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            template_id: templateId,
            prompt: prompt
        }),
        success: function(response) {
            if (response.success && response.content) {
                // 渲染写作结果
                const content = marked.parse(response.content.content);
                $('#writingResult').html(content);
                addLogEntry('info', '成功生成自动写作内容');
            } else {
                $('#writingResult').html(`<div class="alert alert-danger">${response.error || '无法生成自动写作内容'}</div>`);
                addLogEntry('error', `生成自动写作内容失败: ${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#writingResult').html('<div class="alert alert-danger">生成自动写作内容时出错</div>');
            addLogEntry('error', `生成自动写作内容失败: ${xhr.status} ${xhr.statusText}`);
        }
    });
}
