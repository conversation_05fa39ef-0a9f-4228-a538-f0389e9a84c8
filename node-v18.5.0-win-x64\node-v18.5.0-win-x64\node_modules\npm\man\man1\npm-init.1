.TH "NPM\-INIT" "1" "June 2022" "" ""
.SH "NAME"
\fBnpm-init\fR \- Create a package\.json file
.SS Synopsis
.P
.RS 2
.nf
npm init [\-\-force|\-f|\-\-yes|\-y|\-\-scope]
npm init <@scope> (same as `npx <@scope>/create`)
npm init [<@scope>/]<name> (same as `npx [<@scope>/]create\-<name>`)

aliases: create, innit
.fi
.RE
.SS Description
.P
\fBnpm init <initializer>\fP can be used to set up a new or existing npm
package\.
.P
\fBinitializer\fP in this case is an npm package named \fBcreate\-<initializer>\fP,
which will be installed by npm help \fBnpm\-exec\fP, and then have its
main bin executed \-\- presumably creating or updating \fBpackage\.json\fP and
running any other initialization\-related operations\.
.P
The init command is transformed to a corresponding \fBnpm exec\fP operation as
follows:
.RS 0
.IP \(bu 2
\fBnpm init foo\fP \-> \fBnpm exec create\-foo\fP
.IP \(bu 2
\fBnpm init @usr/foo\fP \-> \fBnpm exec @usr/create\-foo\fP
.IP \(bu 2
\fBnpm init @usr\fP \-> \fBnpm exec @usr/create\fP

.RE
.P
If the initializer is omitted (by just calling \fBnpm init\fP), init will fall
back to legacy init behavior\. It will ask you a bunch of questions, and
then write a package\.json for you\. It will attempt to make reasonable
guesses based on existing fields, dependencies, and options selected\. It is
strictly additive, so it will keep any fields and values that were already
set\. You can also use \fB\-y\fP/\fB\-\-yes\fP to skip the questionnaire altogether\. If
you pass \fB\-\-scope\fP, it will create a scoped package\.
.P
\fINote:\fR if a user already has the \fBcreate\-<initializer>\fP package
globally installed, that will be what \fBnpm init\fP uses\.  If you want npm
to use the latest version, or another specific version you must specify
it:
.RS 0
.IP \(bu 2
\fBnpm init foo@latest\fP # fetches and runs the latest \fBcreate\-foo\fP from
  the registry
.IP \(bu 2
\fBnpm init foo@1\.2\.3\fP #  runs \fBcreate\-foo@1\.2\.3\fP specifically

.RE
.SS Forwarding additional options
.P
Any additional options will be passed directly to the command, so \fBnpm init
foo \-\- \-\-hello\fP will map to \fBnpm exec \-\- create\-foo \-\-hello\fP\|\.
.P
To better illustrate how options are forwarded, here's a more evolved
example showing options passed to both the \fBnpm cli\fR and a create package,
both following commands are equivalent:
.RS 0
.IP \(bu 2
\fBnpm init foo \-y \-\-registry=<url> \-\- \-\-hello \-a\fP
.IP \(bu 2
\fBnpm exec \-y \-\-registry=<url> \-\- create\-foo \-\-hello \-a\fP

.RE
.SS Examples
.P
Create a new React\-based project using
\fBcreate\-react\-app\fP \fIhttps://npm\.im/create\-react\-app\fR:
.P
.RS 2
.nf
$ npm init react\-app \./my\-react\-app
.fi
.RE
.P
Create a new \fBesm\fP\-compatible package using
\fBcreate\-esm\fP \fIhttps://npm\.im/create\-esm\fR:
.P
.RS 2
.nf
$ mkdir my\-esm\-lib && cd my\-esm\-lib
$ npm init esm \-\-yes
.fi
.RE
.P
Generate a plain old package\.json using legacy init:
.P
.RS 2
.nf
$ mkdir my\-npm\-pkg && cd my\-npm\-pkg
$ git init
$ npm init
.fi
.RE
.P
Generate it without having it ask any questions:
.P
.RS 2
.nf
$ npm init \-y
.fi
.RE
.SS Workspaces support
.P
It's possible to create a new workspace within your project by using the
\fBworkspace\fP config option\. When using \fBnpm init \-w <dir>\fP the cli will
create the folders and boilerplate expected while also adding a reference
to your project \fBpackage\.json\fP \fB"workspaces": []\fP property in order to make
sure that new generated \fBworkspace\fR is properly set up as such\.
.P
Given a project with no workspaces, e\.g:
.P
.RS 2
.nf
\|\.
+\-\- package\.json
.fi
.RE
.P
You may generate a new workspace using the legacy init:
.P
.RS 2
.nf
$ npm init \-w packages/a
.fi
.RE
.P
That will generate a new folder and \fBpackage\.json\fP file, while also updating
your top\-level \fBpackage\.json\fP to add the reference to this new workspace:
.P
.RS 2
.nf
\|\.
+\-\- package\.json
`\-\- packages
   `\-\- a
       `\-\- package\.json
.fi
.RE
.P
The workspaces init also supports the \fBnpm init <initializer> \-w <dir>\fP
syntax, following the same set of rules explained earlier in the initial
\fBDescription\fR section of this page\. Similar to the previous example of
creating a new React\-based project using
\fBcreate\-react\-app\fP \fIhttps://npm\.im/create\-react\-app\fR, the following syntax
will make sure to create the new react app as a nested \fBworkspace\fR within your
project and configure your \fBpackage\.json\fP to recognize it as such:
.P
.RS 2
.nf
npm init \-w packages/my\-react\-app react\-app \.
.fi
.RE
.P
This will make sure to generate your react app as expected, one important
consideration to have in mind is that \fBnpm exec\fP is going to be run in the
context of the newly created folder for that workspace, and that's the reason
why in this example the initializer uses the initializer name followed with a
dot to represent the current directory in that context, e\.g: \fBreact\-app \.\fP:
.P
.RS 2
.nf
\|\.
+\-\- package\.json
`\-\- packages
   +\-\- a
   |   `\-\- package\.json
   `\-\- my\-react\-app
       +\-\- README
       +\-\- package\.json
       `\-\- \.\.\.
.fi
.RE
.SS Configuration
.SS \fByes\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or Boolean

.RE
.P
Automatically answer "yes" to any prompts that npm might print on the
command line\.
.SS \fBforce\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Removes various protections against unfortunate side effects, common
mistakes, unnecessary performance degradation, and malicious input\.
.RS 0
.IP \(bu 2
Allow clobbering non\-npm files in global installs\.
.IP \(bu 2
Allow the \fBnpm version\fP command to work on an unclean git repository\.
.IP \(bu 2
Allow deleting the cache folder with \fBnpm cache clean\fP\|\.
.IP \(bu 2
Allow installing packages that have an \fBengines\fP declaration requiring a
different version of npm\.
.IP \(bu 2
Allow installing packages that have an \fBengines\fP declaration requiring a
different version of \fBnode\fP, even if \fB\-\-engine\-strict\fP is enabled\.
.IP \(bu 2
Allow \fBnpm audit fix\fP to install modules outside your stated dependency
range (including SemVer\-major changes)\.
.IP \(bu 2
Allow unpublishing all versions of a published package\.
.IP \(bu 2
Allow conflicting peerDependencies to be installed in the root project\.
.IP \(bu 2
Implicitly set \fB\-\-yes\fP during \fBnpm init\fP\|\.
.IP \(bu 2
Allow clobbering existing values in \fBnpm pkg\fP
.IP \(bu 2
Allow unpublishing of entire packages (not just a single version)\.

.RE
.P
If you don't have a clear idea of what you want to do, it is strongly
recommended that you do not use this option!
.SS \fBworkspace\fP
.RS 0
.IP \(bu 2
Default:
.IP \(bu 2
Type: String (can be set multiple times)

.RE
.P
Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option\.
.P
Valid values for the \fBworkspace\fP config are either:
.RS 0
.IP \(bu 2
Workspace names
.IP \(bu 2
Path to a workspace directory
.IP \(bu 2
Path to a parent workspace directory (will result in selecting all
workspaces within that folder)

.RE
.P
When set for the \fBnpm init\fP command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project\.
.P
This value is not exported to the environment for child processes\.
.SS \fBworkspaces\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or Boolean

.RE
.P
Set to true to run the command in the context of \fBall\fR configured
workspaces\.
.P
Explicitly setting this to false will cause commands like \fBinstall\fP to
ignore workspaces altogether\. When not set explicitly:
.RS 0
.IP \(bu 2
Commands that operate on the \fBnode_modules\fP tree (install, update, etc\.)
will link workspaces into the \fBnode_modules\fP folder\. \- Commands that do
other things (test, exec, publish, etc\.) will operate on the root project,
\fIunless\fR one or more workspaces are specified in the \fBworkspace\fP config\.

.RE
.P
This value is not exported to the environment for child processes\.
.SS \fBworkspaces\-update\fP
.RS 0
.IP \(bu 2
Default: true
.IP \(bu 2
Type: Boolean

.RE
.P
If set to true, the npm cli will run an update after operations that may
possibly change the workspaces installed to the \fBnode_modules\fP folder\.
.SS \fBinclude\-workspace\-root\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Include the workspace root when workspaces are enabled for a command\.
.P
When false, specifying individual workspaces via the \fBworkspace\fP config, or
all workspaces via the \fBworkspaces\fP flag, will cause npm to operate only on
the specified workspaces, and not on the root project\.
.P
This value is not exported to the environment for child processes\.
.SS See Also
.RS 0
.IP \(bu 2
init\-package\-json module \fIhttp://npm\.im/init\-package\-json\fR
.IP \(bu 2
npm help package\.json
.IP \(bu 2
npm help version
.IP \(bu 2
npm help scope
.IP \(bu 2
npm help exec
.IP \(bu 2
npm help workspaces

.RE
