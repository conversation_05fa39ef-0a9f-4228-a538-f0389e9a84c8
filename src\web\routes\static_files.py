"""
静态文件管理路由模块
提供创建静态文件目录和复制静态文件的API
"""
import os
import logging
import shutil
from flask import Blueprint, jsonify, request, current_app

logger = logging.getLogger(__name__)

# 创建蓝图
static_files_bp = Blueprint('static_files', __name__)

@static_files_bp.route('/api/create-static-directories', methods=['POST'])
def create_static_directories():
    """
    创建静态文件目录的API
    """
    try:
        # 获取静态文件目录
        static_folder = current_app.static_folder
        
        # 创建目录结构
        directories = [
            os.path.join(static_folder, 'css'),
            os.path.join(static_folder, 'css', 'lib'),
            os.path.join(static_folder, 'js'),
            os.path.join(static_folder, 'js', 'lib'),
            os.path.join(static_folder, 'img'),
            os.path.join(static_folder, 'fonts')
        ]
        
        # 创建目录
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"已创建目录: {directory}")
        
        # 创建备用静态目录
        direct_static_folder = os.path.join(os.path.dirname(static_folder), 'direct-static')
        
        # 创建备用目录结构
        direct_directories = [
            os.path.join(direct_static_folder, 'css'),
            os.path.join(direct_static_folder, 'css', 'lib'),
            os.path.join(direct_static_folder, 'js'),
            os.path.join(direct_static_folder, 'js', 'lib'),
            os.path.join(direct_static_folder, 'img'),
            os.path.join(direct_static_folder, 'fonts')
        ]
        
        # 创建备用目录
        for directory in direct_directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"已创建备用目录: {directory}")
        
        return jsonify({
            "success": True,
            "message": "静态文件目录创建成功"
        })
    except Exception as e:
        logger.error(f"创建静态文件目录时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@static_files_bp.route('/api/copy-static-files', methods=['POST'])
def copy_static_files():
    """
    复制静态文件的API
    """
    try:
        # 获取静态文件目录
        static_folder = current_app.static_folder
        
        # 获取请求数据
        data = request.get_json()
        
        if not data or 'files' not in data:
            return jsonify({
                "success": False,
                "error": "缺少files参数"
            }), 400
        
        # 复制文件
        copied_files = []
        for file_info in data['files']:
            if 'source' not in file_info or 'destination' not in file_info:
                continue
                
            source = file_info['source']
            destination = os.path.join(static_folder, file_info['destination'])
            
            # 确保目标目录存在
            os.makedirs(os.path.dirname(destination), exist_ok=True)
            
            # 复制文件
            if os.path.exists(source):
                shutil.copy2(source, destination)
                copied_files.append(destination)
                logger.info(f"已复制文件: {source} -> {destination}")
            else:
                logger.warning(f"源文件不存在: {source}")
        
        return jsonify({
            "success": True,
            "message": f"已复制 {len(copied_files)} 个文件",
            "copied_files": copied_files
        })
    except Exception as e:
        logger.error(f"复制静态文件时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@static_files_bp.route('/api/check-static-files', methods=['GET'])
def check_static_files():
    """
    检查静态文件的API
    """
    try:
        # 获取静态文件目录
        static_folder = current_app.static_folder
        
        # 核心静态文件列表
        core_files = [
            os.path.join(static_folder, 'css', 'bootstrap.min.css'),
            os.path.join(static_folder, 'css', 'lib', 'bootstrap.min.css'),
            os.path.join(static_folder, 'js', 'jquery.min.js'),
            os.path.join(static_folder, 'js', 'lib', 'jquery.min.js'),
            os.path.join(static_folder, 'js', 'bootstrap.bundle.min.js'),
            os.path.join(static_folder, 'js', 'lib', 'bootstrap.bundle.min.js'),
            os.path.join(static_folder, 'js', 'chart.min.js'),
            os.path.join(static_folder, 'js', 'lib', 'chart.min.js')
        ]
        
        # 检查文件
        file_status = {}
        for file_path in core_files:
            file_status[os.path.basename(file_path)] = os.path.exists(file_path)
        
        return jsonify({
            "success": True,
            "file_status": file_status
        })
    except Exception as e:
        logger.error(f"检查静态文件时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
