/**
 * 九猫 - 优化静态文件加载脚本
 * 解决静态文件加载404错误问题，减少重复加载
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('优化静态文件加载脚本已加载');
    
    // 核心静态文件列表
    const coreStaticFiles = [
        { type: 'css', name: 'bootstrap.min.css', path: '/static/css/lib/bootstrap.min.css', backup: '/direct-static/css/lib/bootstrap.min.css', cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' },
        { type: 'js', name: 'jquery.min.js', path: '/static/js/lib/jquery-3.6.0.min.js', backup: '/direct-static/js/lib/jquery-3.6.0.min.js', cdn: 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js' },
        { type: 'js', name: 'bootstrap.bundle.min.js', path: '/static/js/lib/bootstrap.bundle.min.js', backup: '/direct-static/js/lib/bootstrap.bundle.min.js', cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js' },
        { type: 'js', name: 'chart.min.js', path: '/static/js/lib/chart.min.js', backup: '/direct-static/js/lib/chart.min.js', cdn: 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js' }
    ];
    
    // 已加载的文件集合
    const loadedFiles = new Set();
    
    // 检查并修复静态文件
    function checkAndFixStaticFiles() {
        console.log('开始检查核心静态文件');
        
        // 检查每个核心文件
        coreStaticFiles.forEach(file => {
            // 如果文件已加载，跳过
            if (loadedFiles.has(file.name)) {
                return;
            }
            
            checkAndLoadFile(file);
        });
    }
    
    // 检查并加载单个文件
    function checkAndLoadFile(file) {
        if (file.type === 'css') {
            checkAndLoadCSS(file);
        } else if (file.type === 'js') {
            checkAndLoadJS(file);
        }
    }
    
    // 检查并加载CSS文件
    function checkAndLoadCSS(file) {
        // 检查是否已加载
        let isLoaded = false;
        
        // 检查所有样式表
        const styles = document.styleSheets;
        for (let i = 0; i < styles.length; i++) {
            if (styles[i].href && (
                styles[i].href.includes(file.name) || 
                styles[i].href === file.path || 
                styles[i].href === file.backup || 
                styles[i].href === file.cdn
            )) {
                isLoaded = true;
                loadedFiles.add(file.name);
                break;
            }
        }
        
        if (!isLoaded) {
            console.log(`${file.name} 未加载，尝试加载`);
            
            // 尝试加载
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = file.path;
            
            link.onload = function() {
                console.log(`${file.name} 已加载`);
                loadedFiles.add(file.name);
            };
            
            link.onerror = function() {
                console.warn(`${file.path} 加载失败，尝试备用路径`);
                link.href = file.backup;
                
                link.onload = function() {
                    console.log(`${file.name} 已从备用路径加载`);
                    loadedFiles.add(file.name);
                };
                
                link.onerror = function() {
                    console.warn(`${file.backup} 加载失败，尝试CDN`);
                    link.href = file.cdn;
                    
                    link.onload = function() {
                        console.log(`${file.name} 已从CDN加载`);
                        loadedFiles.add(file.name);
                    };
                    
                    link.onerror = function() {
                        console.error(`${file.cdn} 加载失败，无法加载 ${file.name}`);
                    };
                };
            };
            
            document.head.appendChild(link);
        } else {
            console.log(`${file.name} 已加载`);
        }
    }
    
    // 检查并加载JS文件
    function checkAndLoadJS(file) {
        // 检查是否已加载
        let isLoaded = false;
        
        // 特殊检查Chart.js
        if (file.name === 'chart.min.js' && typeof Chart !== 'undefined') {
            isLoaded = true;
            loadedFiles.add(file.name);
        }
        
        // 特殊检查jQuery
        if (file.name === 'jquery.min.js' && typeof jQuery !== 'undefined') {
            isLoaded = true;
            loadedFiles.add(file.name);
        }
        
        // 特殊检查Bootstrap
        if (file.name === 'bootstrap.bundle.min.js' && typeof bootstrap !== 'undefined') {
            isLoaded = true;
            loadedFiles.add(file.name);
        }
        
        // 检查所有脚本
        if (!isLoaded) {
            const scripts = document.scripts;
            for (let i = 0; i < scripts.length; i++) {
                if (scripts[i].src && (
                    scripts[i].src.includes(file.name) || 
                    scripts[i].src === file.path || 
                    scripts[i].src === file.backup || 
                    scripts[i].src === file.cdn
                )) {
                    isLoaded = true;
                    loadedFiles.add(file.name);
                    break;
                }
            }
        }
        
        if (!isLoaded) {
            console.log(`${file.name} 未加载，尝试加载`);
            
            // 尝试加载
            const script = document.createElement('script');
            script.src = file.path;
            
            script.onload = function() {
                console.log(`${file.name} 已加载`);
                loadedFiles.add(file.name);
                
                // 如果是Chart.js，触发图表修复
                if (file.name === 'chart.min.js' && typeof window.chartFix !== 'undefined') {
                    setTimeout(function() {
                        window.chartFix.fixAllCharts();
                    }, 500);
                }
            };
            
            script.onerror = function() {
                console.warn(`${file.path} 加载失败，尝试备用路径`);
                script.src = file.backup;
                
                script.onload = function() {
                    console.log(`${file.name} 已从备用路径加载`);
                    loadedFiles.add(file.name);
                    
                    // 如果是Chart.js，触发图表修复
                    if (file.name === 'chart.min.js' && typeof window.chartFix !== 'undefined') {
                        setTimeout(function() {
                            window.chartFix.fixAllCharts();
                        }, 500);
                    }
                };
                
                script.onerror = function() {
                    console.warn(`${file.backup} 加载失败，尝试CDN`);
                    script.src = file.cdn;
                    
                    script.onload = function() {
                        console.log(`${file.name} 已从CDN加载`);
                        loadedFiles.add(file.name);
                        
                        // 如果是Chart.js，触发图表修复
                        if (file.name === 'chart.min.js' && typeof window.chartFix !== 'undefined') {
                            setTimeout(function() {
                                window.chartFix.fixAllCharts();
                            }, 500);
                        }
                    };
                    
                    script.onerror = function() {
                        console.error(`${file.cdn} 加载失败，无法加载 ${file.name}`);
                    };
                };
            };
            
            document.head.appendChild(script);
        } else {
            console.log(`${file.name} 已加载`);
        }
    }
    
    // 修复静态文件404错误
    function fixStaticFile404Errors() {
        // 监听所有资源加载错误
        window.addEventListener('error', function(event) {
            const target = event.target;
            
            // 检查是否是静态资源加载错误
            if (target && target.tagName) {
                const tagName = target.tagName.toLowerCase();
                
                if ((tagName === 'link' || tagName === 'script' || tagName === 'img') && (target.src || target.href)) {
                    const url = target.src || target.href;
                    
                    // 检查是否是核心静态文件
                    for (const file of coreStaticFiles) {
                        if (url.includes(file.name)) {
                            // 如果文件已加载，跳过
                            if (loadedFiles.has(file.name)) {
                                return;
                            }
                            
                            console.log(`尝试修复核心静态文件: ${file.name}`);
                            
                            // 尝试备用路径
                            if (tagName === 'link') {
                                target.href = file.backup;
                                
                                target.onload = function() {
                                    loadedFiles.add(file.name);
                                };
                                
                                target.onerror = function() {
                                    target.href = file.cdn;
                                };
                            } else {
                                target.src = file.backup;
                                
                                target.onload = function() {
                                    loadedFiles.add(file.name);
                                };
                                
                                target.onerror = function() {
                                    target.src = file.cdn;
                                };
                            }
                            
                            // 阻止错误传播
                            event.preventDefault();
                            return false;
                        }
                    }
                }
            }
        }, true);
    }
    
    // 修复preload资源未使用的警告
    function fixPreloadWarnings() {
        // 查找所有preload链接
        const preloads = document.querySelectorAll('link[rel="preload"]');
        
        preloads.forEach(function(preload) {
            const href = preload.getAttribute('href');
            const as = preload.getAttribute('as');
            
            if (href && as) {
                // 检查是否是核心静态文件
                for (const file of coreStaticFiles) {
                    if (href.includes(file.name)) {
                        // 如果文件已加载，跳过
                        if (loadedFiles.has(file.name)) {
                            return;
                        }
                        
                        // 根据类型创建适当的元素
                        if (as === 'style' || file.type === 'css') {
                            const link = document.createElement('link');
                            link.rel = 'stylesheet';
                            link.href = href;
                            document.head.appendChild(link);
                            
                            link.onload = function() {
                                loadedFiles.add(file.name);
                            };
                        } else if (as === 'script' || file.type === 'js') {
                            const script = document.createElement('script');
                            script.src = href;
                            document.head.appendChild(script);
                            
                            script.onload = function() {
                                loadedFiles.add(file.name);
                            };
                        }
                        
                        break;
                    }
                }
            }
        });
    }
    
    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始执行优化静态文件加载');
        
        // 检查并修复静态文件
        checkAndFixStaticFiles();
        
        // 修复静态文件404错误
        fixStaticFile404Errors();
        
        // 修复preload资源未使用的警告
        setTimeout(fixPreloadWarnings, 2000);
    });
})();
