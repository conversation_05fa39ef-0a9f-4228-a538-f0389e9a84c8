/**
 * 九猫 - 图表管理器
 * 集中管理所有图表实例，解决Canvas重用问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    'use strict';
    
    // 图表实例存储
    const chartInstances = {};
    
    // 图表数据缓存
    const chartDataCache = {};
    
    // 延迟渲染队列
    const renderQueue = [];
    
    // 是否正在处理渲染队列
    let isProcessingQueue = false;
    
    // 图表管理器
    const ChartManager = {
        /**
         * 初始化图表管理器
         */
        init: function() {
            console.log('图表管理器初始化');
            
            // 确保Chart.js已加载
            this.ensureChartJsLoaded();
            
            // 监听窗口大小变化，优化图表响应式渲染
            window.addEventListener('resize', this.handleWindowResize.bind(this));
            
            // 监听可见性变化，优化不可见图表的资源占用
            document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
            
            // 定期清理未使用的图表实例
            setInterval(this.cleanupUnusedCharts.bind(this), 60000); // 每分钟清理一次
        },
        
        /**
         * 确保Chart.js已加载
         */
        ensureChartJsLoaded: function() {
            if (typeof Chart === 'undefined') {
                console.log('Chart.js未加载，尝试加载');
                
                // 创建脚本元素
                const script = document.createElement('script');
                script.src = '/static/js/lib/chart.min.js';
                script.async = false;
                
                // 加载成功回调
                script.onload = () => {
                    console.log('Chart.js加载成功');
                    this.patchChartJs();
                    this.processRenderQueue();
                };
                
                // 加载失败回调
                script.onerror = () => {
                    console.error('Chart.js加载失败，尝试备用路径');
                    script.src = '/direct-static/js/lib/chart.min.js';
                };
                
                // 添加到文档
                document.head.appendChild(script);
            } else {
                console.log('Chart.js已加载');
                this.patchChartJs();
            }
        },
        
        /**
         * 修补Chart.js，解决已知问题
         */
        patchChartJs: function() {
            if (typeof Chart === 'undefined' || Chart._patched) {
                return;
            }
            
            console.log('修补Chart.js');
            
            // 备份原始构造函数
            const originalChart = Chart;
            
            // 替换构造函数
            Chart = function(ctx, config) {
                // 获取canvas ID
                const canvasId = ctx.canvas ? (ctx.canvas.id || 'unknown') : 'unknown';
                
                // 检查是否已有图表实例
                if (chartInstances[canvasId]) {
                    console.log(`销毁已有图表: ${canvasId}`);
                    chartInstances[canvasId].destroy();
                    delete chartInstances[canvasId];
                }
                
                // 创建新图表
                const chart = new originalChart(ctx, config);
                
                // 存储图表实例
                chartInstances[canvasId] = chart;
                
                return chart;
            };
            
            // 复制原型和静态属性
            Chart.prototype = originalChart.prototype;
            Object.keys(originalChart).forEach(key => {
                Chart[key] = originalChart[key];
            });
            
            // 标记为已修补
            Chart._patched = true;
        },
        
        /**
         * 创建图表
         * @param {string} canvasId - Canvas元素ID
         * @param {string} type - 图表类型
         * @param {Object} data - 图表数据
         * @param {Object} options - 图表选项
         * @param {boolean} [delayed=false] - 是否延迟渲染
         * @returns {Chart|null} 图表实例或null
         */
        createChart: function(canvasId, type, data, options, delayed = false) {
            console.log(`创建图表: ${canvasId}, 类型: ${type}, 延迟: ${delayed}`);
            
            // 缓存图表数据
            chartDataCache[canvasId] = {
                type: type,
                data: JSON.parse(JSON.stringify(data)), // 深拷贝
                options: JSON.parse(JSON.stringify(options)) // 深拷贝
            };
            
            // 如果请求延迟渲染
            if (delayed) {
                this.addToRenderQueue(canvasId);
                return null;
            }
            
            // 检查Chart.js是否已加载
            if (typeof Chart === 'undefined') {
                console.log('Chart.js未加载，添加到渲染队列');
                this.addToRenderQueue(canvasId);
                this.ensureChartJsLoaded();
                return null;
            }
            
            try {
                // 获取canvas元素
                const canvas = document.getElementById(canvasId);
                if (!canvas) {
                    console.error(`找不到canvas元素: ${canvasId}`);
                    return null;
                }
                
                // 检查是否已有图表实例
                if (chartInstances[canvasId]) {
                    console.log(`销毁已有图表: ${canvasId}`);
                    chartInstances[canvasId].destroy();
                    delete chartInstances[canvasId];
                }
                
                // 创建新图表
                const ctx = canvas.getContext('2d');
                const chart = new Chart(ctx, {
                    type: type,
                    data: data,
                    options: options
                });
                
                // 存储图表实例
                chartInstances[canvasId] = chart;
                
                console.log(`图表创建成功: ${canvasId}`);
                return chart;
            } catch (e) {
                console.error(`创建图表时出错: ${canvasId}`, e);
                return null;
            }
        },
        
        /**
         * 更新图表
         * @param {string} canvasId - Canvas元素ID
         * @param {Object} data - 新的图表数据
         * @param {Object} [options] - 新的图表选项
         * @returns {boolean} 是否更新成功
         */
        updateChart: function(canvasId, data, options) {
            console.log(`更新图表: ${canvasId}`);
            
            // 获取图表实例
            const chart = chartInstances[canvasId];
            if (!chart) {
                console.error(`找不到图表实例: ${canvasId}`);
                return false;
            }
            
            try {
                // 更新数据
                chart.data = data;
                
                // 更新选项
                if (options) {
                    chart.options = options;
                }
                
                // 更新图表
                chart.update();
                
                // 更新缓存
                chartDataCache[canvasId] = {
                    type: chart.config.type,
                    data: JSON.parse(JSON.stringify(data)),
                    options: JSON.parse(JSON.stringify(options || chart.options))
                };
                
                console.log(`图表更新成功: ${canvasId}`);
                return true;
            } catch (e) {
                console.error(`更新图表时出错: ${canvasId}`, e);
                return false;
            }
        },
        
        /**
         * 销毁图表
         * @param {string} canvasId - Canvas元素ID
         * @returns {boolean} 是否销毁成功
         */
        destroyChart: function(canvasId) {
            console.log(`销毁图表: ${canvasId}`);
            
            // 获取图表实例
            const chart = chartInstances[canvasId];
            if (!chart) {
                console.log(`找不到图表实例: ${canvasId}`);
                return false;
            }
            
            try {
                // 销毁图表
                chart.destroy();
                
                // 删除实例
                delete chartInstances[canvasId];
                
                console.log(`图表销毁成功: ${canvasId}`);
                return true;
            } catch (e) {
                console.error(`销毁图表时出错: ${canvasId}`, e);
                return false;
            }
        },
        
        /**
         * 获取图表实例
         * @param {string} canvasId - Canvas元素ID
         * @returns {Chart|null} 图表实例或null
         */
        getChart: function(canvasId) {
            return chartInstances[canvasId] || null;
        },
        
        /**
         * 获取所有图表实例
         * @returns {Object} 所有图表实例
         */
        getAllCharts: function() {
            return chartInstances;
        },
        
        /**
         * 添加到渲染队列
         * @param {string} canvasId - Canvas元素ID
         */
        addToRenderQueue: function(canvasId) {
            console.log(`添加到渲染队列: ${canvasId}`);
            
            // 检查是否已在队列中
            if (renderQueue.includes(canvasId)) {
                return;
            }
            
            // 添加到队列
            renderQueue.push(canvasId);
            
            // 如果没有正在处理队列，开始处理
            if (!isProcessingQueue) {
                this.processRenderQueue();
            }
        },
        
        /**
         * 处理渲染队列
         */
        processRenderQueue: function() {
            if (renderQueue.length === 0 || isProcessingQueue) {
                return;
            }
            
            console.log(`处理渲染队列，剩余: ${renderQueue.length}`);
            isProcessingQueue = true;
            
            // 检查Chart.js是否已加载
            if (typeof Chart === 'undefined') {
                console.log('Chart.js未加载，延迟处理渲染队列');
                isProcessingQueue = false;
                return;
            }
            
            // 获取下一个要渲染的图表
            const canvasId = renderQueue.shift();
            
            // 获取缓存的图表数据
            const chartData = chartDataCache[canvasId];
            if (!chartData) {
                console.error(`找不到图表数据: ${canvasId}`);
                isProcessingQueue = false;
                this.processRenderQueue();
                return;
            }
            
            // 创建图表
            this.createChart(canvasId, chartData.type, chartData.data, chartData.options);
            
            // 延迟处理下一个，避免阻塞UI
            setTimeout(() => {
                isProcessingQueue = false;
                this.processRenderQueue();
            }, 50);
        },
        
        /**
         * 处理窗口大小变化
         */
        handleWindowResize: function() {
            // 延迟执行，避免频繁调用
            clearTimeout(this._resizeTimeout);
            this._resizeTimeout = setTimeout(() => {
                console.log('窗口大小变化，更新图表');
                
                // 更新所有图表
                Object.keys(chartInstances).forEach(canvasId => {
                    const chart = chartInstances[canvasId];
                    if (chart) {
                        chart.resize();
                    }
                });
            }, 200);
        },
        
        /**
         * 处理可见性变化
         */
        handleVisibilityChange: function() {
            if (document.hidden) {
                console.log('页面不可见，暂停图表更新');
                
                // 页面不可见时，暂停所有图表动画
                Object.keys(chartInstances).forEach(canvasId => {
                    const chart = chartInstances[canvasId];
                    if (chart && chart.options && chart.options.animation) {
                        chart._originalAnimationState = chart.options.animation.duration;
                        chart.options.animation.duration = 0;
                    }
                });
            } else {
                console.log('页面可见，恢复图表更新');
                
                // 页面可见时，恢复所有图表动画
                Object.keys(chartInstances).forEach(canvasId => {
                    const chart = chartInstances[canvasId];
                    if (chart && chart.options && chart.options.animation && chart._originalAnimationState !== undefined) {
                        chart.options.animation.duration = chart._originalAnimationState;
                        delete chart._originalAnimationState;
                    }
                });
            }
        },
        
        /**
         * 清理未使用的图表实例
         */
        cleanupUnusedCharts: function() {
            console.log('清理未使用的图表实例');
            
            // 获取所有canvas元素
            const canvases = document.querySelectorAll('canvas');
            const canvasIds = Array.from(canvases).map(canvas => canvas.id).filter(id => id);
            
            // 查找不在DOM中的图表实例
            Object.keys(chartInstances).forEach(canvasId => {
                if (!canvasIds.includes(canvasId)) {
                    console.log(`清理未使用的图表: ${canvasId}`);
                    this.destroyChart(canvasId);
                }
            });
        }
    };
    
    // 初始化图表管理器
    document.addEventListener('DOMContentLoaded', function() {
        ChartManager.init();
    });
    
    // 导出到全局
    window.ChartManager = ChartManager;
})();
