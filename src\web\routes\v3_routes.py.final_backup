"""
九猫小说分析写作系统v3.0路由
"""
import logging
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash

from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.api_log import ApiLog
from src.models.preset import Preset
from src.db.connection import Session
from src.api.deepseek_client import DeepSeekClient
from src.api.analysis import NovelAnalyzer
from src.services.chapter_analysis_service import ChapterAnalysisService
from src.services.analysis_service import aggregate_chapter_analyses
import config

# 创建蓝图
v3_bp = Blueprint('v3', __name__)
logger = logging.getLogger(__name__)

# 从config.py导入分析维度配置
ANALYSIS_DIMENSIONS = config.ANALYSIS_DIMENSIONS

@v3_bp.route('/')
def index():
    """首页"""
    try:
        session = Session()
        try:
            # 获取最近的小说
            novels = session.query(Novel).order_by(Novel.created_at.desc()).limit(5).all()

            # 获取参考蓝本列表
            reference_templates = get_reference_templates(session)

            # 获取生成内容统计
            generated_content_stats = get_generated_content_stats(session)

            return render_template('v3/index.html',
                                  novels=novels,
                                  reference_templates=reference_templates,
                                  generated_content_stats=generated_content_stats)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载首页时出错: {str(e)}", exc_info=True)
        return render_template('v3/index.html', novels=[], reference_templates=[], generated_content_stats={})

@v3_bp.route('/novels')
def novels():
    """小说列表页面"""
    try:
        session = Session()
        try:
            # 获取所有小说
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()

            # 获取所有维度的键
            all_dimensions = [d['key'] for d in ANALYSIS_DIMENSIONS]

            # 为每本小说获取分析结果
            for novel in novels:
                # 获取分析结果
                analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel.id).all()
                novel.analysis_results = analysis_results

                # 获取已分析的维度
                available_dimensions = [result.dimension for result in analysis_results]
                novel.available_dimensions = available_dimensions

                # 获取章节
                chapters = session.query(Chapter).filter_by(novel_id=novel.id).all()
                novel.chapters = chapters

                # 获取章节分析结果
                chapter_analysis_results = {}
                for chapter in chapters:
                    chapter_results = session.query(ChapterAnalysisResult).filter_by(
                        novel_id=novel.id,
                        chapter_id=chapter.id
                    ).all()

                    if chapter_results:
                        chapter_analysis_results[chapter.id] = {result.dimension: result for result in chapter_results}

                novel.chapter_analysis_results = chapter_analysis_results

                # 检查整本书和所有章节是否都已完成所有维度分析
                novel.can_set_as_template = check_can_set_as_template(
                    novel.id,
                    available_dimensions,
                    chapters,
                    chapter_analysis_results
                )

                # 判断是否所有维度都已分析
                novel.is_fully_analyzed = novel.can_set_as_template  # 只有当整本书和所有章节都分析完成时才标记为"全部维度已分析"
                novel.is_partially_analyzed = len(available_dimensions) > 0 and not novel.is_fully_analyzed

            return render_template('v3/novels.html', novels=novels, dimensions=ANALYSIS_DIMENSIONS)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载小说列表页面时出错: {str(e)}", exc_info=True)
        return render_template('v3/novels.html', novels=[], dimensions=ANALYSIS_DIMENSIONS)

@v3_bp.route('/upload', methods=['GET', 'POST'])
def upload_novel():
    """上传小说页面"""
    if request.method == 'GET':
        return render_template('v3/upload.html', dimensions=ANALYSIS_DIMENSIONS)

    try:
        title = request.form.get('title', '未命名小说')
        author = request.form.get('author', '')
        content = ''

        # 处理文件上传
        if 'file' in request.files and request.form.get('uploadType') == 'file':
            file = request.files['file']
            if file.filename:
                content = file.read().decode('utf-8', errors='ignore')

        # 处理文本粘贴
        elif 'content' in request.form and request.form.get('uploadType') == 'text':
            content = request.form['content']

        if not content:
            return render_template('v3/upload.html', error="请上传文件或粘贴文本内容", dimensions=ANALYSIS_DIMENSIONS)

        # 保存到数据库
        session = Session()
        try:
            novel = Novel(
                title=title,
                author=author,
                content=content
            )

            # 字数会在Novel的__init__方法中自动计算
            session.add(novel)
            session.commit()

            # 获取小说ID用于后续操作
            novel_id = novel.id

            # 提交事务并关闭当前会话，确保novel对象被正确保存
            session.commit()
            session.close()

            # 创建新的会话用于章节分割
            new_session = Session()
            try:
                # 自动分割章节
                from src.services.chapter_analysis_service import ChapterAnalysisService
                chapters = ChapterAnalysisService.split_novel_into_chapters(novel_id)
                logger.info(f"小说《{title}》已上传并自动分割为 {len(chapters)} 个章节")
                new_session.commit()
            except Exception as e:
                logger.error(f"分割章节时出错: {str(e)}", exc_info=True)
                new_session.rollback()
                raise
            finally:
                new_session.close()

            # 重新创建会话用于后续操作
            session = Session()

            # 检查是否需要自动分析
            auto_analyze = request.form.get('autoAnalyze') == 'on'
            if auto_analyze:
                # 获取选中的分析维度
                dimensions = request.form.getlist('dimensions')
                if dimensions:
                    # 获取小说对象
                    novel = session.query(Novel).get(novel_id)
                    if novel:
                        # 启动分析
                        # 导入单维度分析模块
                        from src.api.analyze_dimension import analyze_dimension

                        # 逐个维度分析
                        for dimension in dimensions:
                            analyze_dimension(novel, dimension)

                        flash(f"小说《{title}》已上传并开始分析", "success")
                    else:
                        flash(f"小说《{title}》已上传，但无法启动分析", "warning")
                else:
                    flash(f"小说《{title}》已上传，但未选择分析维度", "warning")
            else:
                flash(f"小说《{title}》已上传", "success")

            return redirect(url_for('v3.view_novel', novel_id=novel_id))
        finally:
            session.close()
    except Exception as e:
        logger.error(f"上传小说时出错: {str(e)}", exc_info=True)
        # 确保会话被关闭
        try:
            if 'session' in locals() and session:
                session.close()
        except:
            pass
        return render_template('v3/upload.html', error=f"上传小说失败: {str(e)}", dimensions=ANALYSIS_DIMENSIONS)

@v3_bp.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    """查看小说详情页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v3.novels'))

            # 获取分析结果
            analysis_results = {}
            available_dimensions = []

            results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            for result in results:
                analysis_results[result.dimension] = result
                available_dimensions.append(result.dimension)

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 获取章节分析结果
            chapter_analysis_results = {}
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).all()

                if chapter_results:
                    chapter_analysis_results[chapter.id] = {result.dimension: result for result in chapter_results}

            # 检查是否可以设为参考蓝本
            can_set_as_template = check_can_set_as_template(novel_id, available_dimensions, chapters, chapter_analysis_results)

            # 为每个维度添加图标信息
            dimensions_with_icons = []
            for dim in ANALYSIS_DIMENSIONS:
                dim_copy = dim.copy()
                # 设置默认图标
                if 'icon' not in dim_copy:
                    if dim_copy['key'] == 'language_style':
                        dim_copy['icon'] = 'fas fa-language'
                    elif dim_copy['key'] == 'rhythm_pace':
                        dim_copy['icon'] = 'fas fa-drum'
                    elif dim_copy['key'] == 'character_development':
                        dim_copy['icon'] = 'fas fa-user'
                    elif dim_copy['key'] == 'emotional_expression':
                        dim_copy['icon'] = 'fas fa-heart'
                    elif dim_copy['key'] == 'scene_description':
                        dim_copy['icon'] = 'fas fa-image'
                    elif dim_copy['key'] == 'dialogue_analysis':
                        dim_copy['icon'] = 'fas fa-comments'
                    elif dim_copy['key'] == 'theme_exploration':
                        dim_copy['icon'] = 'fas fa-lightbulb'
                    elif dim_copy['key'] == 'conflict_setup':
                        dim_copy['icon'] = 'fas fa-bolt'
                    elif dim_copy['key'] == 'perspective_shifts':
                        dim_copy['icon'] = 'fas fa-eye'
                    elif dim_copy['key'] == 'symbolism_imagery':
                        dim_copy['icon'] = 'fas fa-feather-alt'
                    elif dim_copy['key'] == 'cultural_context':
                        dim_copy['icon'] = 'fas fa-globe'
                    elif dim_copy['key'] == 'plot_development':
                        dim_copy['icon'] = 'fas fa-route'
                    elif dim_copy['key'] == 'structure_layout':
                        dim_copy['icon'] = 'fas fa-th-large'
                    elif dim_copy['key'] == 'outline_analysis':
                        dim_copy['icon'] = 'fas fa-list'
                    elif dim_copy['key'] == 'chapter_outline':
                        dim_copy['icon'] = 'fas fa-list-ol'
                    else:
                        dim_copy['icon'] = 'fas fa-search'
                dimensions_with_icons.append(dim_copy)

            return render_template(
                'v3/novel_detail.html',
                novel=novel,
                analysis_results=analysis_results,
                available_dimensions=available_dimensions,
                chapters=chapters,
                chapter_analysis_results=chapter_analysis_results,
                dimensions=dimensions_with_icons,
                can_set_as_template=can_set_as_template
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看小说详情页面时出错: {str(e)}", exc_info=True)
        flash(f"加载小说详情失败: {str(e)}", "danger")
        return redirect(url_for('v3.novels'))

@v3_bp.route('/reference-templates')
def reference_templates():
    """参考蓝本页面"""
    try:
        session = Session()
        session.expire_all()  # 清除会话缓存，确保获取最新数据
        
        try:
            # 获取所有参考蓝本
            templates = get_reference_templates(session)

            # 添加强制刷新标志到模板，用于前端判断
            force_refresh = request.args.get('force_refresh') == 'true'

            # 渲染模板
            response = render_template('v3/reference_templates.html', 
                                      templates=templates, 
                                      force_refresh=force_refresh)

            # 添加缓存控制头，防止浏览器缓存
            headers = {
                'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
                'Pragma': 'no-cache',
                'Expires': '0'
            }

            return response, 200, headers
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载参考蓝本页面时出错: {str(e)}", exc_info=True)
        flash(f"加载参考蓝本失败: {str(e)}", "danger")
        return redirect(url_for('v3.index'))

@v3_bp.route('/console')
def console():
    """控制台页面"""
    try:
        session = Session()
        try:
            # 获取所有参考蓝本
            templates = get_reference_templates(session)

            # 获取当前时间，用于日志显示
            now = datetime.now()

            response = render_template('v3/console.html', templates=templates, now=now)

            # 添加缓存控制头，防止浏览器缓存
            headers = {
                'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
                'Pragma': 'no-cache',
                'Expires': '0'
            }

            return response, 200, headers
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载控制台页面时出错: {str(e)}", exc_info=True)
        flash(f"加载控制台失败: {str(e)}", "danger")
        return redirect(url_for('v3.index'))

@v3_bp.route('/showcase')
def showcase():
    """展示台页面"""
    try:
        # 获取统计数据
        stats = get_generated_content_stats()
        return render_template('v3/showcase.html', stats=stats)
    except Exception as e:
        logger.error(f"加载展示台页面时出错: {str(e)}", exc_info=True)
        flash(f"加载展示台失败: {str(e)}", "danger")
        return redirect(url_for('v3.index'))

@v3_bp.route('/content-repository')
def content_repository():
    """生成内容仓库页面"""
    try:
        session = Session()
        try:
            # 获取所有生成的内容
            generated_contents = get_generated_contents(session)
            return render_template('v3/content_repository.html', contents=generated_contents)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载生成内容仓库页面时出错: {str(e)}", exc_info=True)
        flash(f"加载生成内容仓库失败: {str(e)}", "danger")
        return redirect(url_for('v3.index'))

@v3_bp.route('/help')
def help():
    """帮助页面"""
    try:
        return render_template('v3/help.html')
    except Exception as e:
        logger.error(f"加载帮助页面时出错: {str(e)}", exc_info=True)
        flash(f"加载帮助页面失败: {str(e)}", "danger")
        return redirect(url_for('v3.index'))

@v3_bp.route('/preset/<int:preset_id>')
@v3_bp.route('/v3/preset/<int:preset_id>')
def view_preset(preset_id):
    """查看预设内容详情页面"""
    try:
        session = Session()
        try:
            # 获取预设内容
            preset = session.query(Preset).get(preset_id)
            if not preset:
                flash("未找到指定预设内容", "danger")
                return redirect(url_for('v3.console'))

            # 如果是知识库类型，直接重定向到知识库预设模板页面
            # 这样用户无需点击两次"展开查看"按钮
            if preset.category == 'knowledge_base':
                return redirect(url_for('v3.view_preset_templates', preset_id=preset_id))

            # 转换为字典
            preset_dict = {
                'id': preset.id,
                'title': preset.title,
                'content': preset.content,
                'category': preset.category,
                'created_at': preset.created_at.strftime('%Y-%m-%d %H:%M:%S') if preset.created_at else '未知',
                'updated_at': preset.updated_at.strftime('%Y-%m-%d %H:%M:%S') if preset.updated_at else '未知',
                'show_template_button': False  # 非知识库类型不显示"展开查看"按钮
            }

            return render_template('v3/preset_detail.html', preset=preset_dict)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看预设内容详情页面时出错: {str(e)}", exc_info=True)
        flash(f"加载预设内容详情失败: {str(e)}", "danger")
        return redirect(url_for('v3.console'))

@v3_bp.route('/preset/<int:preset_id>/templates')
@v3_bp.route('/v3/preset/<int:preset_id>/templates')
def view_preset_templates(preset_id):
    """查看预设模板详情页面"""
    try:
        session = Session()
        try:
            # 获取预设内容
            preset = session.query(Preset).get(preset_id)
            if not preset:
                flash("未找到指定预设内容", "danger")
                return redirect(url_for('v3.console'))

            # 检查是否是支持的类型
            supported_categories = ['knowledge_base', 'reference_template', 'template_thinking', 'template_dimension', 'template_chapter']
            if preset.category not in supported_categories:
                logger.warning(f"预设内容ID {preset_id} 类别为 {preset.category}，不是支持的模板类别")
                flash("只有知识库或参考蓝本类型的预设内容才能查看预设模板", "warning")
                return redirect(url_for('v3.view_preset', preset_id=preset_id))

            # 获取参考蓝本ID（尝试多种方法）
            novel_id = None
            novel = None
            
            # 方法1: 从meta_info中获取
            if preset.meta_info and isinstance(preset.meta_info, dict) and 'reference_template_id' in preset.meta_info:
                novel_id = preset.meta_info['reference_template_id']
                logger.info(f"从meta_info找到参考蓝本ID: {novel_id}")
            
            # 方法2: 从标题中解析参考蓝本名称
            if not novel_id and preset.title and "参考蓝本-" in preset.title:
                try:
                    template_name = preset.title.replace("参考蓝本-", "").strip()
                    logger.info(f"从标题中提取参考蓝本名称: {template_name}")
                    # 查找匹配名称的小说
                    templates = session.query(Novel).filter(Novel.title == template_name).all()
                    for template in templates:
                        if template.novel_metadata and template.novel_metadata.get('is_template'):
                            novel_id = template.id
                            novel = template
                            logger.info(f"从名称'{template_name}'找到参考蓝本ID: {novel_id}")
                            break
                except Exception as e:
                    logger.error(f"从标题解析参考蓝本失败: {str(e)}")
            
            # 方法3: 从内容中解析参考蓝本ID
            if not novel_id and preset.content:
                try:
                    # 尝试多种可能的格式
                    patterns = [
                        r"参考蓝本ID:\s*(\d+)",
                        r"参考蓝本ID：\s*(\d+)",
                        r"参考蓝本ID\s*[:：]\s*(\d+)",
                        r"参考蓝本\s*ID\s*[:：]\s*(\d+)",
                        r"参考蓝本.*?ID.*?[:：].*?(\d+)"
                    ]
                    
                    import re
                    for pattern in patterns:
                        match = re.search(pattern, preset.content)
                        if match:
                            novel_id = int(match.group(1))
                            logger.info(f"从内容中解析到参考蓝本ID: {novel_id} (使用模式: {pattern})")
                            break
                    
                    # 如果仍未找到ID，尝试从内容中提取小说名称
                    if not novel_id:
                        title_match = re.search(r"参考蓝本[:：]\s*(.*?)[\n\r]", preset.content)
                        if title_match:
                            template_name = title_match.group(1).strip()
                            logger.info(f"从内容中提取参考蓝本名称: {template_name}")
                            templates = session.query(Novel).filter(Novel.title == template_name).all()
                            for template in templates:
                                if template.novel_metadata and template.novel_metadata.get('is_template'):
                                    novel_id = template.id
                                    novel = template
                                    logger.info(f"从名称'{template_name}'找到参考蓝本ID: {novel_id}")
                                    break
            except Exception as e:
                    logger.error(f"从内容中解析参考蓝本ID或名称失败: {str(e)}")
                    
            # 方法4: 直接查找所有被标记为参考蓝本的小说
            if not novel_id:
                try:
                    logger.info("尝试查找系统中设置的参考蓝本...")
                    templates = session.query(Novel).all()
                    for template in templates:
                        if template.novel_metadata and template.novel_metadata.get('is_template'):
                            novel_id = template.id
                            novel = template
                            logger.info(f"找到参考蓝本: {template.title} (ID: {novel_id})")
                            break
                except Exception as e:
                    logger.error(f"查找参考蓝本失败: {str(e)}")

            # 如果找到了novel_id但没有novel对象，获取novel对象
            if novel_id and not novel:
                try:
                    novel = session.query(Novel).get(novel_id)
                except Exception as e:
                    logger.error(f"获取小说对象失败: {str(e)}")

            # 如果仍然没有找到参考蓝本，记录警告
            if not novel_id:
                logger.warning(f"预设内容 ID {preset_id} 没有关联参考蓝本，meta_info: {preset.meta_info}")
                flash("未找到关联的参考蓝本，无法显示预设模板", "warning")
                return redirect(url_for('v3.view_preset', preset_id=preset_id))

            # 获取章节列表
            chapters = []
            if novel_id:
                try:
                    # 确保novel_id是整数
                    novel_id = int(novel_id)
                    chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()
                    logger.info(f"获取到参考蓝本 ID {novel_id} 的章节列表，共 {len(chapters)} 章")

                    # 如果没有章节，记录警告
                    if not chapters:
                        logger.warning(f"参考蓝本 ID {novel_id} 没有章节，请检查参考蓝本设置")
                except Exception as e:
                    logger.error(f"获取章节列表时出错: {str(e)}", exc_info=True)

            # 为每个维度添加图标信息
            dimensions_with_icons = []
            for dim in ANALYSIS_DIMENSIONS:
                dim_copy = dim.copy()
                # 设置默认图标
                if 'icon' not in dim_copy:
                    if dim_copy['key'] == 'language_style':
                        dim_copy['icon'] = 'fas fa-language'
                    elif dim_copy['key'] == 'rhythm_pacing':
                        dim_copy['icon'] = 'fas fa-drum'
                    elif dim_copy['key'] == 'structure':
                        dim_copy['icon'] = 'fas fa-sitemap'
                    elif dim_copy['key'] == 'sentence_variation':
                        dim_copy['icon'] = 'fas fa-text-width'
                    elif dim_copy['key'] == 'paragraph_length':
                        dim_copy['icon'] = 'fas fa-indent'
                    elif dim_copy['key'] == 'perspective_shifts':
                        dim_copy['icon'] = 'fas fa-eye'
                    elif dim_copy['key'] == 'paragraph_flow':
                        dim_copy['icon'] = 'fas fa-stream'
                    elif dim_copy['key'] == 'novel_characteristics':
                        dim_copy['icon'] = 'fas fa-fingerprint'
                    elif dim_copy['key'] == 'world_building':
                        dim_copy['icon'] = 'fas fa-globe'
                    elif dim_copy['key'] == 'character_relationships':
                        dim_copy['icon'] = 'fas fa-users'
                    elif dim_copy['key'] == 'opening_effectiveness':
                        dim_copy['icon'] = 'fas fa-door-open'
                    elif dim_copy['key'] == 'climax_pacing':
                        dim_copy['icon'] = 'fas fa-mountain'
                    elif dim_copy['key'] == 'popular_tropes':
                        dim_copy['icon'] = 'fas fa-fire'
                    elif dim_copy['key'] == 'outline_analysis':
                        dim_copy['icon'] = 'fas fa-list'
                    elif dim_copy['key'] == 'chapter_outline':
                        dim_copy['icon'] = 'fas fa-list-ol'
                    else:
                        dim_copy['icon'] = 'fas fa-search'
                dimensions_with_icons.append(dim_copy)

            # 转换为字典
            preset_dict = {
                'id': preset.id,
                'title': preset.title,
                'content': preset.content,
                'category': preset.category,
                'created_at': preset.created_at.strftime('%Y-%m-%d %H:%M:%S') if preset.created_at else '未知',
                'updated_at': preset.updated_at.strftime('%Y-%m-%d %H:%M:%S') if preset.updated_at else '未知'
            }
            
            # 根据类别选择不同的模板
            template_file = 'v3/preset_template_detail.html'
            if preset.category == 'knowledge_base':
                template_file = 'v3/knowledge_base_template.html'
                
            logger.info(f"渲染模板 {template_file}，使用参考蓝本ID: {novel_id}, 章节数: {len(chapters)}")

            return render_template(
                template_file,
                preset=preset_dict,
                chapters=chapters,
                dimensions=dimensions_with_icons,
                novel_id=novel_id or 0,
                novel=novel
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看预设模板详情页面时出错: {str(e)}", exc_info=True)
        flash(f"加载预设模板详情失败: {str(e)}", "danger")
        return redirect(url_for('v3.view_preset', preset_id=preset_id))

@v3_bp.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    """查看小说分析结果页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v3.novels'))

            # 获取分析结果
            analysis_result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not analysis_result:
                flash(f"未找到维度 '{dimension}' 的分析结果", "warning")
                return redirect(url_for('v3.view_novel', novel_id=novel_id))

            # 获取维度信息
            dimension_info = next((d for d in ANALYSIS_DIMENSIONS if d['key'] == dimension), None)
            if not dimension_info:
                dimension_info = {'name': dimension, 'description': ''}

            return render_template(
                'v3/analysis.html',
                novel=novel,
                dimension=dimension,
                dimension_info=dimension_info,
                analysis_result=analysis_result
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看分析结果页面时出错: {str(e)}", exc_info=True)
        flash(f"加载分析结果失败: {str(e)}", "danger")
        return redirect(url_for('v3.view_novel', novel_id=novel_id))

@v3_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>')
def view_chapter(novel_id, chapter_id):
    """查看章节详情页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v3.novels'))

            # 获取章节
            chapter = session.query(Chapter).filter_by(
                novel_id=novel_id,
                id=chapter_id
            ).first()

            if not chapter:
                flash("未找到指定章节", "danger")
                return redirect(url_for('v3.view_novel', novel_id=novel_id))

            # 获取章节分析结果
            chapter_analysis_results = {}
            available_dimensions = []

            results = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id
            ).all()

            for result in results:
                chapter_analysis_results[result.dimension] = result
                available_dimensions.append(result.dimension)

            # 为每个维度添加图标信息
            dimensions_with_icons = []
            for dim in ANALYSIS_DIMENSIONS:
                dim_copy = dim.copy()
                # 设置默认图标
                if 'icon' not in dim_copy:
                    if dim_copy['key'] == 'language_style':
                        dim_copy['icon'] = 'fas fa-language'
                    elif dim_copy['key'] == 'rhythm_pacing':
                        dim_copy['icon'] = 'fas fa-drum'
                    elif dim_copy['key'] == 'structure':
                        dim_copy['icon'] = 'fas fa-sitemap'
                    elif dim_copy['key'] == 'sentence_variation':
                        dim_copy['icon'] = 'fas fa-text-width'
                    elif dim_copy['key'] == 'paragraph_length':
                        dim_copy['icon'] = 'fas fa-indent'
                    elif dim_copy['key'] == 'perspective_shifts':
                        dim_copy['icon'] = 'fas fa-eye'
                    elif dim_copy['key'] == 'paragraph_flow':
                        dim_copy['icon'] = 'fas fa-stream'
                    elif dim_copy['key'] == 'novel_characteristics':
                        dim_copy['icon'] = 'fas fa-fingerprint'
                    elif dim_copy['key'] == 'world_building':
                        dim_copy['icon'] = 'fas fa-globe'
                    elif dim_copy['key'] == 'character_relationships':
                        dim_copy['icon'] = 'fas fa-users'
                    elif dim_copy['key'] == 'opening_effectiveness':
                        dim_copy['icon'] = 'fas fa-door-open'
                    elif dim_copy['key'] == 'climax_pacing':
                        dim_copy['icon'] = 'fas fa-mountain'
                    elif dim_copy['key'] == 'popular_tropes':
                        dim_copy['icon'] = 'fas fa-fire'
                    elif dim_copy['key'] == 'outline_analysis':
                        dim_copy['icon'] = 'fas fa-list'
                    elif dim_copy['key'] == 'chapter_outline':
                        dim_copy['icon'] = 'fas fa-list-ol'
                    else:
                        dim_copy['icon'] = 'fas fa-search'
                dimensions_with_icons.append(dim_copy)

            return render_template(
                'v3/chapter_detail.html',
                novel=novel,
                chapter=chapter,
                chapter_analysis_results=chapter_analysis_results,
                available_dimensions=available_dimensions,
                dimensions=dimensions_with_icons
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节详情页面时出错: {str(e)}", exc_info=True)
        flash(f"加载章节详情失败: {str(e)}", "danger")
        return redirect(url_for('v3.view_novel', novel_id=novel_id))

@v3_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def chapter_analysis(novel_id, chapter_id, dimension):
    """查看章节分析结果页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v3.novels'))

            # 获取章节
            chapter = session.query(Chapter).filter_by(
                novel_id=novel_id,
                id=chapter_id
            ).first()

            if not chapter:
                flash("未找到指定章节", "danger")
                return redirect(url_for('v3.view_novel', novel_id=novel_id))

            # 获取章节分析结果
            chapter_analysis_result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not chapter_analysis_result:
                flash(f"未找到维度 '{dimension}' 的章节分析结果", "warning")
                return redirect(url_for('v3.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

            # 获取维度信息
            dimension_info = next((d for d in ANALYSIS_DIMENSIONS if d['key'] == dimension), None)
            if not dimension_info:
                dimension_info = {'name': dimension, 'description': ''}

            return render_template(
                'v3/chapter_analysis.html',
                novel=novel,
                chapter=chapter,
                dimension=dimension,
                dimension_info=dimension_info,
                chapter_analysis_result=chapter_analysis_result
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节分析结果页面时出错: {str(e)}", exc_info=True)
        flash(f"加载章节分析结果失败: {str(e)}", "danger")
        return redirect(url_for('v3.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

@v3_bp.route('/novel/<int:novel_id>/chapters')
def chapters_list(novel_id):
    """章节列表页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v3.novels'))

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 获取章节分析结果
            chapter_analysis_results = {}
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).all()

                if chapter_results:
                    chapter_analysis_results[chapter.id] = {result.dimension: result for result in chapter_results}

            return render_template(
                'v3/chapters_list.html',
                novel=novel,
                chapters=chapters,
                chapter_analysis_results=chapter_analysis_results,
                dimensions=ANALYSIS_DIMENSIONS
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节列表页面时出错: {str(e)}", exc_info=True)
        flash(f"加载章节列表失败: {str(e)}", "danger")
        return redirect(url_for('v3.view_novel', novel_id=novel_id))

@v3_bp.route('/novel/<int:novel_id>/templates')
def view_templates(novel_id):
    """查看预设模板详情页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v3.novels'))

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 为每个维度添加图标信息
            dimensions_with_icons = []
            for dim in ANALYSIS_DIMENSIONS:
                dim_copy = dim.copy()
                # 设置默认图标
                if 'icon' not in dim_copy:
                    if dim_copy['key'] == 'language_style':
                        dim_copy['icon'] = 'fas fa-language'
                    elif dim_copy['key'] == 'rhythm_pacing':
                        dim_copy['icon'] = 'fas fa-drum'
                    elif dim_copy['key'] == 'structure':
                        dim_copy['icon'] = 'fas fa-sitemap'
                    elif dim_copy['key'] == 'sentence_variation':
                        dim_copy['icon'] = 'fas fa-text-width'
                    elif dim_copy['key'] == 'paragraph_length':
                        dim_copy['icon'] = 'fas fa-indent'
                    elif dim_copy['key'] == 'perspective_shifts':
                        dim_copy['icon'] = 'fas fa-eye'
                    elif dim_copy['key'] == 'paragraph_flow':
                        dim_copy['icon'] = 'fas fa-stream'
                    elif dim_copy['key'] == 'novel_characteristics':
                        dim_copy['icon'] = 'fas fa-fingerprint'
                    elif dim_copy['key'] == 'world_building':
                        dim_copy['icon'] = 'fas fa-globe'
                    elif dim_copy['key'] == 'character_relationships':
                        dim_copy['icon'] = 'fas fa-users'
                    elif dim_copy['key'] == 'opening_effectiveness':
                        dim_copy['icon'] = 'fas fa-door-open'
                    elif dim_copy['key'] == 'climax_pacing':
                        dim_copy['icon'] = 'fas fa-mountain'
                    elif dim_copy['key'] == 'popular_tropes':
                        dim_copy['icon'] = 'fas fa-fire'
                    elif dim_copy['key'] == 'outline_analysis':
                        dim_copy['icon'] = 'fas fa-list'
                    elif dim_copy['key'] == 'chapter_outline':
                        dim_copy['icon'] = 'fas fa-list-ol'
                    else:
                        dim_copy['icon'] = 'fas fa-search'
                dimensions_with_icons.append(dim_copy)

            # 创建一个虚拟的preset对象
            preset = {
                'id': novel_id,
                'title': f"参考蓝本: {novel.title}",
                'content': f"参考蓝本《{novel.title}》的详细分析和预设模板",
                'category': 'reference_template',
                'created_at': datetime.now()
            }

            return render_template(
                'v3/preset_template_detail.html',
                novel=novel,
                chapters=chapters,
                dimensions=dimensions_with_icons,
                preset=preset
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看预设模板详情页面时出错: {str(e)}", exc_info=True)
        flash(f"加载预设模板详情失败: {str(e)}", "danger")
        return redirect(url_for('v3.view_novel', novel_id=novel_id))

@v3_bp.route('/novel/<int:novel_id>/chapters/summary')
def chapters_summary(novel_id):
    """章节分析汇总页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v3.novels'))

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 获取章节分析结果
            chapter_analysis_results = {}
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).all()

                if chapter_results:
                    chapter_analysis_results[chapter.id] = {result.dimension: result for result in chapter_results}

            # 为每个维度添加图标信息
            dimensions_with_icons = []
            for dim in ANALYSIS_DIMENSIONS:
                dim_copy = dim.copy()
                # 设置默认图标
                if 'icon' not in dim_copy:
                    if dim_copy['key'] == 'language_style':
                        dim_copy['icon'] = 'fas fa-language'
                    elif dim_copy['key'] == 'rhythm_pacing':
                        dim_copy['icon'] = 'fas fa-drum'
                    elif dim_copy['key'] == 'structure':
                        dim_copy['icon'] = 'fas fa-sitemap'
                    elif dim_copy['key'] == 'sentence_variation':
                        dim_copy['icon'] = 'fas fa-text-width'
                    elif dim_copy['key'] == 'paragraph_length':
                        dim_copy['icon'] = 'fas fa-indent'
                    elif dim_copy['key'] == 'perspective_shifts':
                        dim_copy['icon'] = 'fas fa-eye'
                    elif dim_copy['key'] == 'paragraph_flow':
                        dim_copy['icon'] = 'fas fa-stream'
                    elif dim_copy['key'] == 'novel_characteristics':
                        dim_copy['icon'] = 'fas fa-fingerprint'
                    elif dim_copy['key'] == 'world_building':
                        dim_copy['icon'] = 'fas fa-globe'
                    elif dim_copy['key'] == 'character_relationships':
                        dim_copy['icon'] = 'fas fa-users'
                    elif dim_copy['key'] == 'opening_effectiveness':
                        dim_copy['icon'] = 'fas fa-door-open'
                    elif dim_copy['key'] == 'climax_pacing':
                        dim_copy['icon'] = 'fas fa-mountain'
                    elif dim_copy['key'] == 'popular_tropes':
                        dim_copy['icon'] = 'fas fa-fire'
                    elif dim_copy['key'] == 'outline_analysis':
                        dim_copy['icon'] = 'fas fa-list'
                    elif dim_copy['key'] == 'chapter_outline':
                        dim_copy['icon'] = 'fas fa-list-ol'
                    else:
                        dim_copy['icon'] = 'fas fa-search'
                dimensions_with_icons.append(dim_copy)

            return render_template(
                'v3/chapters_summary.html',
                novel=novel,
                chapters=chapters,
                chapter_analysis_results=chapter_analysis_results,
                dimensions=dimensions_with_icons
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节分析汇总页面时出错: {str(e)}", exc_info=True)
        flash(f"加载章节分析汇总失败: {str(e)}", "danger")
        return redirect(url_for('v3.view_novel', novel_id=novel_id))

@v3_bp.route('/system_monitor')
def system_monitor():
    """系统监控页面"""
    try:
        import psutil
        from datetime import datetime, timedelta

        # 获取真实的系统监控数据
        # CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)

        # 内存使用率
        memory = psutil.virtual_memory()
        memory_usage = memory.percent

        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_usage = disk.percent

        # 获取系统启动时间
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        days, seconds = uptime.days, uptime.seconds
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        uptime_str = f"{days}天 {hours}小时 {minutes}分钟"

        # 获取数据库统计信息
        session = Session()
        try:
            # 获取小说总数
            total_novels = session.query(Novel).count()

            # 获取章节总数
            total_chapters = session.query(Chapter).count()

            # 获取分析结果总数
            total_analyses = session.query(AnalysisResult).count()

            # 获取API调用次数
            api_calls = session.query(ApiLog).count()

            # 获取API使用率（最近24小时的API调用占比）
            yesterday = datetime.now() - timedelta(days=1)
            recent_api_calls = session.query(ApiLog).filter(ApiLog.timestamp >= yesterday).count()
            api_usage = (recent_api_calls / api_calls * 100) if api_calls > 0 else 0

            # 构建监控数据
            monitor_data = {
                'cpu_usage': cpu_usage,
                'memory_usage': memory_usage,
                'disk_usage': disk_usage,
                'api_usage': round(api_usage, 2),
                'uptime': uptime_str,
                'api_calls': api_calls,
                'total_novels': total_novels,
                'total_analyses': total_analyses,
                'total_chapters': total_chapters,
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return render_template('v3/system_monitor.html', monitor_data=monitor_data)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载系统监控页面时出错: {str(e)}", exc_info=True)
        flash(f"加载系统监控页面失败: {str(e)}", "danger")
        return redirect(url_for('v3.index'))

# 辅助函数
def get_reference_templates(session=None):
    """获取参考蓝本列表"""
    try:
        close_session = False
        if session is None:
            session = Session()
            close_session = True

        try:
            # 清除会话缓存，确保获取最新数据
            session.expire_all()
            
            # 从数据库中获取参考蓝本
            templates = []

            # 获取所有小说，强制查询最新数据
            novels = session.query(Novel).all()
            # 刷新查询结果，确保每个对象都是最新的
            for novel in novels:
                session.refresh(novel)
                
            logger.info(f"获取参考蓝本列表: 找到 {len(novels)} 本小说")

            # 首先检查哪些小说被标记为参考蓝本
            template_novels = []
            for novel in novels:
                if novel.novel_metadata and novel.novel_metadata.get('is_template'):
                    template_novels.append(novel)
                    logger.info(f"找到标记为参考蓝本的小说: 《{novel.title}》(ID: {novel.id})")

            logger.info(f"共找到 {len(template_novels)} 本标记为参考蓝本的小说")

            # 处理每个标记为参考蓝本的小说
            for novel in template_novels:
                novel_id = novel.id

                # 获取章节
                chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()

                # 获取分析结果
                analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
                available_dimensions = [result.dimension for result in analysis_results]

                # 添加到参考蓝本列表
                templates.append({
                    'id': novel.id,
                    'title': novel.title,
                    'author': novel.author,
                    'word_count': novel.word_count,
                    'created_at': novel.created_at,
                    'chapter_count': len(chapters),
                    'dimension_count': len(available_dimensions),
                    'template_created_at': novel.novel_metadata.get('template_created_at')
                })
                logger.info(f"已添加参考蓝本: 《{novel.title}》(ID: {novel.id})")

            logger.info(f"参考蓝本列表获取完成，共 {len(templates)} 个参考蓝本")
            return templates
        finally:
            if close_session:
                session.close()
                logger.debug("关闭数据库会话")
    except Exception as e:
        logger.error(f"获取参考蓝本列表时出错: {str(e)}", exc_info=True)
        return []

def check_can_set_as_template(novel_id, available_dimensions, chapters, chapter_analysis_results):
    """检查小说是否可以设为参考蓝本"""
    try:
        # 检查整本书是否已完成所有维度分析
        all_dimensions = [d['key'] for d in ANALYSIS_DIMENSIONS]
        book_dimensions_complete = all(dim in available_dimensions for dim in all_dimensions)

        # 检查所有章节是否都已完成所有维度分析
        chapters_dimensions_complete = True
        for chapter in chapters:
            chapter_results = chapter_analysis_results.get(chapter.id, {})
            if not all(dim in chapter_results for dim in all_dimensions):
                chapters_dimensions_complete = False
                break

        return book_dimensions_complete and chapters_dimensions_complete
    except Exception as e:
        logger.error(f"检查小说是否可以设为参考蓝本时出错: {str(e)}", exc_info=True)
        return False

def get_generated_content_stats(session=None):
    """获取生成内容统计"""
    try:
        close_session = False
        if session is None:
            session = Session()
            close_session = True

        try:
            # 从数据库中获取生成内容统计
            # 这里暂时模拟数据，后续需要实现真实的数据库查询
            stats = {
                'total_chapters': 0,  # 重置为0
                'total_words': 0,
                'avg_words_per_chapter': 0,
                'total_hours': 0  # 重置为0
            }

            return stats
        finally:
            if close_session:
                session.close()
    except Exception as e:
        logger.error(f"获取生成内容统计时出错: {str(e)}", exc_info=True)
        return {
            'total_chapters': 0,
            'total_words': 0,
            'avg_words_per_chapter': 0,
            'total_hours': 0
        }

def get_generated_contents(session=None):
    """获取生成的内容列表"""
    try:
        close_session = False
        if session is None:
            session = Session()
            close_session = True

        try:
            # 从数据库中获取生成的内容
            # 这里暂时模拟数据，后续需要实现真实的数据库查询
            contents = []

            return contents
        finally:
            if close_session:
                session.close()
    except Exception as e:
        logger.error(f"获取生成内容列表时出错: {str(e)}", exc_info=True)
        return []
