{"name": "npm-profile", "version": "6.0.3", "description": "Library for updating an npmjs.com profile", "keywords": [], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"npm-registry-fetch": "^13.0.1", "proc-log": "^2.0.0"}, "main": "./lib/index.js", "repository": {"type": "git", "url": "https://github.com/npm/npm-profile.git"}, "files": ["bin/", "lib/"], "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.4.1", "nock": "^13.2.4", "tap": "^16.0.1"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "posttest": "npm run lint", "test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.4.1"}}