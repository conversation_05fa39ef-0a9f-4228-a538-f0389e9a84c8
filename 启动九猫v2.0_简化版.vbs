' 九猫v2.0小说分析系统启动脚本 (简化版)
' 此脚本会启动九猫v2.0系统，并自动打开浏览器，无需显示命令行窗口

' 设置工作目录为脚本所在目录
Set fso = CreateObject("Scripting.FileSystemObject")
currentPath = fso.GetParentFolderName(WScript.ScriptFullName)
Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = currentPath

' 确保工作目录设置正确
On Error Resume Next
Dim testFile
Set testFile = fso.CreateTextFile("test_dir.txt", True)
If Err.Number <> 0 Then
    MsgBox "无法在当前目录创建文件，可能是权限问题。" & vbCrLf & _
           "错误: " & Err.Description & vbCrLf & _
           "当前目录: " & currentPath, _
           vbExclamation, "九猫v2.0小说分析系统 - 错误"
    WScript.Quit
End If
testFile.WriteLine("目录测试文件")
testFile.Close
fso.DeleteFile "test_dir.txt"
On Error Goto 0

' 检查并创建日志目录
If Not fso.FolderExists("logs") Then
    fso.CreateFolder("logs")
End If

' 显示启动消息
MsgBox "九猫v2.0小说分析系统正在启动..." & vbCrLf & vbCrLf & _
       "系统将在后台运行，浏览器将在10秒钟后自动打开。" & vbCrLf & _
       "请不要关闭浏览器窗口，否则需要手动访问: http://localhost:5001", _
       64, "九猫v2.0小说分析系统"

' 创建临时批处理文件来设置环境变量并运行Python
Set tempFile = fso.CreateTextFile("temp_run_v2.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & currentPath & """")
tempFile.WriteLine("echo 当前工作目录: %CD%")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set USE_MOCK_API=False")
tempFile.WriteLine("set ENABLE_MOCK_ANALYSIS=False")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("set MEMORY_OPTIMIZED=True")
tempFile.WriteLine("set LOW_MEMORY_MODE=True")
tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=75")
tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=85")
tempFile.WriteLine("set MAX_DB_CONNECTIONS=30")
tempFile.WriteLine("set DB_POOL_SIZE=30")
tempFile.WriteLine("set DB_MAX_OVERFLOW=20")
tempFile.WriteLine("set THREAD_POOL_SIZE=8")
tempFile.WriteLine("set MAX_WORKERS=8")
tempFile.WriteLine("set DISABLE_PARALLEL_ANALYSIS=False")
tempFile.WriteLine("set REDUCE_LOGGING=True")
tempFile.WriteLine("set OPTIMIZE_STATIC_FILES=True")
tempFile.WriteLine("set OPTIMIZE_CHART_RENDERING=True")
tempFile.WriteLine("set OPTIMIZE_LARGE_TEXT=True")
tempFile.WriteLine("set USE_EXTERNAL_STORAGE=True")
tempFile.WriteLine("set EXTERNAL_STORAGE_PATH=E:\艹，又来一次\九猫\external_storage")
tempFile.WriteLine("set LOG_PATH=E:\艹，又来一次\九猫\logs")
tempFile.WriteLine("set TEMP_DIR=E:\艹，又来一次\九猫\temp")
tempFile.WriteLine("set CACHE_DIR=E:\艹，又来一次\九猫\cache")
tempFile.WriteLine("set DB_BACKUP_DIR=E:\艹，又来一次\九猫\db_backup")
tempFile.WriteLine("set MEMORY_STATS_DIR=E:\艹，又来一次\九猫\memory_stats")
tempFile.WriteLine("set DISABLE_AUTO_REFRESH=True")
tempFile.WriteLine("set MEMORY_CHECK_INTERVAL=3")
tempFile.WriteLine("set DISABLE_CHARTS=True")
tempFile.WriteLine("set OPTIMIZE_DIMENSION_DETAIL=True")
tempFile.WriteLine("set ENABLE_LOG_FILTER=True")
tempFile.WriteLine("set SEPARATE_ANALYSIS_PROCESS=True")
tempFile.WriteLine("set ENABLE_BUTTON_TEXT_SUPREME_FIX=True")
tempFile.WriteLine("set FORCE_BUTTON_TEXT_VISIBILITY=True")
tempFile.WriteLine("set DEEPSEEK_API_KEY=***********************************")
tempFile.WriteLine("set QWEN_API_KEY=sk-6f3b4c6ad9f64f78b22bed422c5d278d")
tempFile.WriteLine("set DEFAULT_MODEL=deepseek-r1")
tempFile.WriteLine("set FORCE_REANALYSIS=True")
tempFile.WriteLine("set FORCE_REAL_API=True")
tempFile.WriteLine("set RELOAD_CONFIG=True")

tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("echo 检查Python是否可用...")
tempFile.WriteLine("python --version")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo Python未找到，请确保Python已安装并添加到PATH环境变量中")
tempFile.WriteLine("    pause")
tempFile.WriteLine("    exit /b 1")
tempFile.WriteLine(")")
tempFile.WriteLine("echo 启动九猫v2.0系统...")
tempFile.WriteLine("python -u -m src.web.v2_app")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo 启动失败，错误代码: %ERRORLEVEL%")
tempFile.WriteLine("    pause")
tempFile.WriteLine(")")
tempFile.Close

' 在后台运行批处理文件，但显示窗口以便查看输出和错误信息
Dim runResult
runResult = WshShell.Run("cmd /c """ & currentPath & "\temp_run_v2.bat""", 1, False)

' 记录启动信息到日志文件
On Error Resume Next
Dim logFile, logFileName
logFileName = "logs\startup_v2_" & Year(Now()) & Month(Now()) & Day(Now()) & "_" & Hour(Now()) & Minute(Now()) & Second(Now()) & ".log"
Set logFile = fso.CreateTextFile(logFileName, True)
If Err.Number = 0 Then
    logFile.WriteLine("九猫v2.0系统启动时间: " & Now())
    logFile.WriteLine("当前目录: " & currentPath)
    logFile.WriteLine("批处理文件: " & currentPath & "\temp_run_v2.bat")
    logFile.WriteLine("运行结果: " & runResult)
    logFile.Close
Else
    MsgBox "无法创建日志文件: " & Err.Description, vbExclamation, "九猫v2.0小说分析系统 - 警告"
    Err.Clear
End If
On Error Goto 0

' 等待15秒钟确保服务启动
WScript.Sleep 15000

' 打开浏览器 - 使用cmd命令强制使用默认浏览器
WshShell.Run "cmd /c start http://localhost:5001/", 0, False

' 显示启动完成消息
MsgBox "九猫v2.0小说分析系统已启动！" & vbCrLf & vbCrLf & _
       "如果浏览器没有自动打开，请手动访问: http://localhost:5001/" & vbCrLf & _
       "如果系统无法启动，请查看logs目录下的日志文件。", _
       64, "九猫v2.0小说分析系统 - 启动完成"
