{% extends "v3/base.html" %}

{% block title %}内容仓库 - 九猫小说分析写作系统v3.0{% endblock %}

{% block extra_css %}
<style>
    .content-card {
        transition: all 0.3s ease;
    }
    .content-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .content-preview {
        max-height: 150px;
        overflow: hidden;
        position: relative;
    }
    .content-preview::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,1));
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    /* 批量操作样式 */
    .batch-toolbar {
        position: sticky;
        top: 0;
        z-index: 100;
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .batch-mode .content-card {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .batch-mode .content-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }

    .batch-mode .content-card.selected {
        border: 2px solid var(--bs-primary);
        background-color: rgba(13, 110, 253, 0.05);
    }

    .batch-checkbox {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 10;
        background: white;
        border-radius: 50%;
        padding: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h1 class="mb-2"><i class="fas fa-archive text-danger me-2"></i>内容仓库</h1>
                        <p class="lead mb-0">内容仓库存放所有通过自动写作生成的内容，方便查看、编辑和管理。</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" id="toggleBatchModeBtn">
                            <i class="fas fa-check-square me-1"></i>批量管理
                        </button>
                        <a href="{{ url_for('v3.console') }}" class="btn btn-primary">
                            <i class="fas fa-terminal me-1"></i>前往控制台
                        </a>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong> 您可以在控制台中使用参考蓝本进行自动写作，生成的内容将自动保存到内容仓库。
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body filter-section">
                <div class="row">
                    <div class="col-md-4 mb-3 mb-md-0">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="搜索内容..." id="searchInput">
                            <button class="btn btn-primary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3 mb-md-0">
                        <select class="form-select" id="sortSelect">
                            <option value="newest">最新创建</option>
                            <option value="oldest">最早创建</option>
                            <option value="longest">字数最多</option>
                            <option value="shortest">字数最少</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3 mb-md-0">
                        <select class="form-select" id="templateSelect">
                            <option value="all">所有参考蓝本</option>
                            <option value="1">参考蓝本1</option>
                            <option value="2">参考蓝本2</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" id="resetFilterBtn">
                            <i class="fas fa-redo me-1"></i>重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 内容列表 -->
<div class="row" id="contentList">
    {% if contents %}
        {% for content in contents %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 content-card shadow-sm" data-content-id="{{ content.id }}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ content.title }}</h5>
                        <span class="badge bg-primary">{{ content.created_at.split('T')[0] if content.created_at is string else content.created_at }}</span>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <span class="badge bg-info">AI生成</span>
                            {% if content.original_title %}
                                <span class="badge bg-secondary">基于: {{ content.original_title }}</span>
                            {% endif %}
                        </div>
                        <div class="content-preview mb-3">
                            <p>{{ content.content[:200] + '...' if content.content else '无内容预览' }}</p>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="text-muted">{{ content.word_count }} 字</span>
                                {% if content.chapter_count %}
                                    <span class="text-muted ms-2">{{ content.chapter_count }} 章</span>
                                {% endif %}
                            </div>
                            <div>
                                <a href="{{ url_for('v3.view_generated_content', novel_id=content.id) }}" class="btn btn-sm btn-outline-primary me-1">
                                    <i class="fas fa-eye me-1"></i>查看
                                </a>
                                <a href="#" class="btn btn-sm btn-outline-danger delete-content-btn" data-content-id="{{ content.id }}">
                                    <i class="fas fa-trash me-1"></i>删除
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-archive fa-4x text-muted mb-3"></i>
                    <h3>内容仓库为空</h3>
                    <p class="text-muted">您还没有生成任何内容。请前往控制台使用参考蓝本进行自动写作。</p>
                    <div class="mt-4">
                        <a href="{{ url_for('v3.console') }}" class="btn btn-primary">
                            <i class="fas fa-terminal me-1"></i>前往控制台
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- 分页 -->
{% if contents and contents|length > 12 %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="内容分页">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
{% endif %}

<!-- 内容查看模态框 -->
<div class="modal fade" id="contentViewModal" tabindex="-1" aria-labelledby="contentViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentViewModalLabel">内容详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h4 id="modalContentTitle"></h4>
                <div class="d-flex justify-content-between mb-3">
                    <span class="text-muted" id="modalContentInfo"></span>
                    <span class="badge bg-primary" id="modalContentDate"></span>
                </div>
                <div id="modalContentBody" class="border p-3 rounded" style="max-height: 400px; overflow-y: auto;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="downloadContentBtn">
                    <i class="fas fa-download me-1"></i>下载
                </button>
                <button type="button" class="btn btn-success" id="editContentBtn">
                    <i class="fas fa-edit me-1"></i>编辑
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个内容吗？此操作无法撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/v3/batch-operations.js') }}"></script>
<script>
    $(document).ready(function() {
        // 批量管理模式切换
        let batchMode = false;

        $('#toggleBatchModeBtn').click(function() {
            batchMode = !batchMode;

            if (batchMode) {
                // 进入批量模式
                $(this).html('<i class="fas fa-times me-1"></i>退出批量管理');
                $(this).removeClass('btn-outline-primary').addClass('btn-warning');
                $('#contentList').addClass('batch-mode');

                // 为每个内容卡片添加选择框
                $('.content-card').each(function() {
                    const contentId = $(this).data('content-id');
                    if (!$(this).find('.batch-checkbox').length) {
                        $(this).prepend(`
                            <div class="batch-checkbox">
                                <input type="checkbox" class="form-check-input item-checkbox" value="${contentId}" id="content_${contentId}">
                            </div>
                        `);
                    }
                });

                // 初始化批量操作
                if (window.batchOperations) {
                    window.batchOperations.addCheckboxesToItems('content');
                }

                // 添加卡片点击事件
                $('.content-card').off('click.batch').on('click.batch', function(e) {
                    if ($(e.target).is('a, button, input') || $(e.target).closest('a, button').length) {
                        return; // 不处理链接和按钮的点击
                    }

                    const checkbox = $(this).find('.item-checkbox');
                    checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
                });

            } else {
                // 退出批量模式
                $(this).html('<i class="fas fa-check-square me-1"></i>批量管理');
                $(this).removeClass('btn-warning').addClass('btn-outline-primary');
                $('#contentList').removeClass('batch-mode');

                // 移除选择框
                $('.batch-checkbox').remove();
                $('.content-card').removeClass('selected');

                // 隐藏批量工具栏
                $('#batchToolbar').hide();

                // 移除卡片点击事件
                $('.content-card').off('click.batch');

                // 清理批量操作状态
                if (window.batchOperations) {
                    window.batchOperations.cancelSelection();
                }
            }
        });

        // 监听选择框变化，更新卡片样式
        $(document).on('change', '.item-checkbox', function() {
            const card = $(this).closest('.content-card');
            if ($(this).is(':checked')) {
                card.addClass('selected');
            } else {
                card.removeClass('selected');
            }
        });

        // 查看内容
        $(document).on('click', '.btn-outline-primary[data-content-id]', function(e) {
            e.preventDefault();
            const contentId = $(this).data('content-id');

            // 模拟数据，实际应该从API获取
            const content = {
                id: contentId,
                title: '自动生成的内容',
                content: '这是自动生成的内容示例。这里应该显示完整的内容文本。在实际应用中，这部分内容会从API获取。',
                word_count: 1000,
                created_at: '2025-05-10'
            };

            $('#modalContentTitle').text(content.title);
            $('#modalContentInfo').text(`${content.word_count} 字`);
            $('#modalContentDate').text(content.created_at);
            $('#modalContentBody').text(content.content);

            const contentViewModal = new bootstrap.Modal(document.getElementById('contentViewModal'));
            contentViewModal.show();
        });

        // 删除内容
        $(document).on('click', '.btn-outline-danger[data-content-id]', function(e) {
            e.preventDefault();
            const contentId = $(this).data('content-id');

            // 保存要删除的内容ID
            $('#confirmDeleteBtn').data('content-id', contentId);

            const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
            deleteConfirmModal.show();
        });

        // 确认删除
        $('#confirmDeleteBtn').click(function() {
            const contentId = $(this).data('content-id');
            const $btn = $(this);
            const originalHtml = $btn.html();

            // 显示加载状态
            $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>删除中...');
            $btn.prop('disabled', true);

            // 调用删除API
            $.ajax({
                url: `/v3/api/generated-content/${contentId}/delete`,
                type: 'POST',
                contentType: 'application/json',
                success: function(response) {
                    if (response.success) {
                        // 从页面移除内容卡片
                        $(`.content-card[data-content-id="${contentId}"]`).parent().remove();

                        // 关闭模态框
                        const deleteConfirmModal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
                        deleteConfirmModal.hide();

                        // 显示成功提示
                        alert('删除成功');

                        // 如果页面没有内容了，显示空状态
                        if ($('.content-card').length === 0) {
                            $('.row').html(`
                                <div class="col-12">
                                    <div class="empty-state text-center py-5">
                                        <div class="empty-icon mb-3">
                                            <i class="fas fa-folder-open fa-3x text-muted"></i>
                                        </div>
                                        <h3>暂无生成内容</h3>
                                        <p class="text-muted">还没有生成任何内容，去控制台生成一些内容吧。</p>
                                        <a href="/v3/console" class="btn btn-primary">
                                            <i class="fas fa-terminal me-1"></i>前往控制台
                                        </a>
                                    </div>
                                </div>
                            `);
                        }
                    } else {
                        alert('删除失败: ' + (response.error || response.message));
                        // 恢复按钮状态
                        $btn.html(originalHtml);
                        $btn.prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('删除请求失败:', error);
                    alert('删除请求失败，请重试');
                    // 恢复按钮状态
                    $btn.html(originalHtml);
                    $btn.prop('disabled', false);
                }
            });
        });

        // 下载内容
        $('#downloadContentBtn').click(function() {
            const title = $('#modalContentTitle').text();
            const content = $('#modalContentBody').text();

            // 创建Blob对象
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });

            // 创建下载链接
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${title}.txt`;

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });

        // 搜索功能
        $('#searchBtn').click(function() {
            const searchTerm = $('#searchInput').val().trim();
            if (searchTerm) {
                alert(`搜索: ${searchTerm}（功能模拟）`);
            }
        });

        // 排序功能
        $('#sortSelect').change(function() {
            const sortValue = $(this).val();
            alert(`排序方式: ${sortValue}（功能模拟）`);
        });

        // 参考蓝本筛选
        $('#templateSelect').change(function() {
            const templateId = $(this).val();
            alert(`筛选参考蓝本: ${templateId}（功能模拟）`);
        });

        // 重置筛选
        $('#resetFilterBtn').click(function() {
            $('#searchInput').val('');
            $('#sortSelect').val('newest');
            $('#templateSelect').val('all');
            alert('已重置筛选条件（功能模拟）');
        });
    });
</script>
{% endblock %}
