/**
 * 九猫 - API路径修复脚本
 * 用于修复前端API路径不匹配的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[API路径修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,                 // 是否启用调试模式
        checkInterval: 1000,         // 检查间隔（毫秒）
        maxRetries: 5,               // 最大重试次数
        apiPaths: {
            // 需要修复的API路径映射
            '/api/novel/{novel_id}/analysis/{dimension}': '/api/analysis/result/{novel_id}/{dimension}',
            '/api/novel/{novel_id}/analysis/{dimension}/reasoning_content': '/api/novel/{novel_id}/analysis/{dimension}/reasoning_content',
            '/api/novel/{novel_id}/analysis/{dimension}/process': '/api/novel/{novel_id}/analysis/{dimension}/process'
        }
    };

    // 状态
    const STATE = {
        initialized: false,          // 是否已初始化
        retryCount: 0,               // 重试计数
        originalFetch: null,         // 原始fetch函数
        fixedPaths: new Set()        // 已修复的路径
    };

    // 安全日志函数
    function safeLog(message, level = 'info') {
        if (CONFIG.debug) {
            const prefix = '[API路径修复]';
            try {
                switch (level) {
                    case 'error':
                        console.error(`${prefix} ${message}`);
                        break;
                    case 'warn':
                        console.warn(`${prefix} ${message}`);
                        break;
                    default:
                        console.log(`${prefix} ${message}`);
                }
            } catch (e) {
                // 忽略日志错误
            }
        }
    }

    // 替换URL中的参数
    function replaceUrlParams(url, params) {
        let result = url;
        for (const key in params) {
            result = result.replace(`{${key}}`, params[key]);
        }
        return result;
    }

    // 从URL中提取参数
    function extractParamsFromUrl(url, pattern) {
        const patternParts = pattern.split('/');
        const urlParts = url.split('/');
        
        const params = {};
        
        for (let i = 0; i < patternParts.length; i++) {
            const part = patternParts[i];
            if (part.startsWith('{') && part.endsWith('}')) {
                const paramName = part.substring(1, part.length - 1);
                if (i < urlParts.length) {
                    params[paramName] = urlParts[i];
                }
            }
        }
        
        return params;
    }

    // 检查URL是否匹配模式
    function matchUrlPattern(url, pattern) {
        const patternParts = pattern.split('/');
        const urlParts = url.split('/');
        
        if (patternParts.length !== urlParts.length) {
            return false;
        }
        
        for (let i = 0; i < patternParts.length; i++) {
            const part = patternParts[i];
            if (!part.startsWith('{') && part !== urlParts[i]) {
                return false;
            }
        }
        
        return true;
    }

    // 修复API路径
    function fixApiPath(url) {
        // 检查URL是否需要修复
        for (const pattern in CONFIG.apiPaths) {
            if (matchUrlPattern(url, pattern)) {
                const params = extractParamsFromUrl(url, pattern);
                const fixedUrl = replaceUrlParams(CONFIG.apiPaths[pattern], params);
                
                if (url !== fixedUrl) {
                    safeLog(`修复API路径: ${url} -> ${fixedUrl}`);
                    STATE.fixedPaths.add(url);
                    return fixedUrl;
                }
            }
        }
        
        return url;
    }

    // 拦截fetch请求
    function interceptFetch() {
        if (!STATE.originalFetch) {
            STATE.originalFetch = window.fetch;
            
            window.fetch = function(url, options) {
                // 如果URL是字符串，尝试修复
                if (typeof url === 'string') {
                    url = fixApiPath(url);
                }
                // 如果URL是Request对象，尝试修复
                else if (url instanceof Request) {
                    const fixedUrl = fixApiPath(url.url);
                    if (fixedUrl !== url.url) {
                        url = new Request(fixedUrl, url);
                    }
                }
                
                return STATE.originalFetch.call(this, url, options);
            };
            
            safeLog('已拦截fetch请求');
        }
    }

    // 初始化
    function initialize() {
        if (STATE.initialized) {
            return;
        }

        safeLog('初始化API路径修复脚本');

        // 拦截fetch请求
        interceptFetch();

        // 标记为已初始化
        STATE.initialized = true;
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出到全局命名空间，以便调试
    window.apiPathFix = {
        config: CONFIG,
        state: STATE,
        fixApiPath: fixApiPath
    };

    safeLog('脚本加载完成');
})();
