.TH "NPM\-CI" "1" "June 2022" "" ""
.SH "NAME"
\fBnpm-ci\fR \- Clean install a project
.SS Synopsis
.P
.RS 2
.nf
npm ci

aliases: clean\-install, ic, install\-clean, isntall\-clean
.fi
.RE
.SS Description
.P
This command is similar to npm help \fBinstall\fP, except
it's meant to be used in automated environments such as test platforms,
continuous integration, and deployment \-\- or any situation where you want
to make sure you're doing a clean install of your dependencies\.
.P
The main differences between using \fBnpm install\fP and \fBnpm ci\fP are:
.RS 0
.IP \(bu 2
The project \fBmust\fR have an existing \fBpackage\-lock\.json\fP or
\fBnpm\-shrinkwrap\.json\fP\|\.
.IP \(bu 2
If dependencies in the package lock do not match those in \fBpackage\.json\fP,
\fBnpm ci\fP will exit with an error, instead of updating the package lock\.
.IP \(bu 2
\fBnpm ci\fP can only install entire projects at a time: individual
dependencies cannot be added with this command\.
.IP \(bu 2
If a \fBnode_modules\fP is already present, it will be automatically removed
before \fBnpm ci\fP begins its install\.
.IP \(bu 2
It will never write to \fBpackage\.json\fP or any of the package\-locks:
installs are essentially frozen\.

.RE
.P
NOTE: If you create your \fBpackage\-lock\.json\fP file by running \fBnpm install\fP
with flags that can affect the shape of your dependency tree, such as
\fB\-\-legacy\-peer\-deps\fP or \fB\-\-install\-links\fP, you \fImust\fR provide the same
flags to \fBnpm ci\fP or you are likely to encounter errors\. An easy way to do
this is to run, for example,
\fBnpm config set legacy\-peer\-deps=true \-\-location=project\fP and commit the
\fB\|\.npmrc\fP file to your repo\.
.SS Example
.P
Make sure you have a package\-lock and an up\-to\-date install:
.P
.RS 2
.nf
$ cd \./my/npm/project
$ npm install
added 154 packages in 10s
$ ls | grep package\-lock
.fi
.RE
.P
Run \fBnpm ci\fP in that project
.P
.RS 2
.nf
$ npm ci
added 154 packages in 5s
.fi
.RE
.P
Configure Travis to build using \fBnpm ci\fP instead of \fBnpm install\fP:
.P
.RS 2
.nf
# \.travis\.yml
install:
\- npm ci
# keep the npm cache around to speed up installs
cache:
  directories:
  \- "$HOME/\.npm"
.fi
.RE
.SS Configuration
.SS \fBaudit\fP
.RS 0
.IP \(bu 2
Default: true
.IP \(bu 2
Type: Boolean

.RE
.P
When "true" submit audit reports alongside the current npm command to the
default registry and all registries configured for scopes\. See the
documentation for npm help \fBaudit\fP for details on what is
submitted\.
.SS \fBforeground\-scripts\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Run all build scripts (ie, \fBpreinstall\fP, \fBinstall\fP, and \fBpostinstall\fP)
scripts for installed packages in the foreground process, sharing standard
input, output, and error with the main npm process\.
.P
Note that this will generally make installs run slower, and be much noisier,
but can be useful for debugging\.
.SS \fBignore\-scripts\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
If true, npm does not run scripts specified in package\.json files\.
.P
Note that commands explicitly intended to run a particular script, such as
\fBnpm start\fP, \fBnpm stop\fP, \fBnpm restart\fP, \fBnpm test\fP, and \fBnpm run\-script\fP
will still run their intended script if \fBignore\-scripts\fP is set, but they
will \fInot\fR run any pre\- or post\-scripts\.
.SS \fBscript\-shell\fP
.RS 0
.IP \(bu 2
Default: '/bin/sh' on POSIX systems, 'cmd\.exe' on Windows
.IP \(bu 2
Type: null or String

.RE
.P
The shell to use for scripts run with the \fBnpm exec\fP, \fBnpm run\fP and \fBnpm
init <pkg>\fP commands\.
.SS See Also
.RS 0
.IP \(bu 2
npm help install
.IP \(bu 2
npm help package\-lock\.json

.RE
