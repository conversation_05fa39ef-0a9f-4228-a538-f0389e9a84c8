/**
 * 九猫 - 强制导航修复脚本
 * 使用最强力的方式解决所有页面跳转问题
 * 版本: 1.0.0
 * 创建日期: 2023-11-15
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._forceNavigationFixLoaded) {
        console.log('[强制导航修复] 脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._forceNavigationFixLoaded = true;
    
    console.log('[强制导航修复] 脚本已加载 - 版本1.0.0');
    
    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalWindowLocation = window.location;
    const originalWindowOpen = window.open;
    const originalHistoryPushState = history.pushState;
    const originalHistoryReplaceState = history.replaceState;
    
    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[强制导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    function safeError(message) {
        try {
            originalConsoleError.call(console, '[强制导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 获取当前URL中的小说ID
    function getNovelIdFromUrl() {
        try {
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
        } catch (e) {
            safeError('从URL获取小说ID时出错: ' + e.message);
        }
        return null;
    }
    
    // 从页面元素中获取小说ID
    function getNovelIdFromElements() {
        try {
            // 从data属性中获取
            const elements = document.querySelectorAll('[data-novel-id]');
            for (let i = 0; i < elements.length; i++) {
                const id = elements[i].getAttribute('data-novel-id');
                if (id) {
                    return id;
                }
            }
            
            // 从表单元素中获取
            const inputs = document.querySelectorAll('input[name="novel_id"], input#novel_id');
            for (let i = 0; i < inputs.length; i++) {
                const id = inputs[i].value;
                if (id) {
                    return id;
                }
            }
            
            // 从URL中的隐藏参数获取
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('novel_id');
            if (id) {
                return id;
            }
            
            // 从页面内容中获取
            const content = document.body.textContent;
            const matches = content.match(/小说ID[：:]\s*(\d+)/i);
            if (matches && matches[1]) {
                return matches[1];
            }
        } catch (e) {
            safeError('从页面元素获取小说ID时出错: ' + e.message);
        }
        return null;
    }
    
    // 从全局变量中获取小说ID
    function getNovelIdFromGlobals() {
        try {
            if (window.novelId) {
                return window.novelId;
            }
            if (window.novelIdFromTemplate) {
                return window.novelIdFromTemplate;
            }
            if (window.novel && window.novel.id) {
                return window.novel.id;
            }
        } catch (e) {
            safeError('从全局变量获取小说ID时出错: ' + e.message);
        }
        return null;
    }
    
    // 获取小说ID - 尝试所有可能的方法
    function getNovelId() {
        // 首先从URL中获取
        let id = getNovelIdFromUrl();
        if (id) {
            safeLog('从URL获取到小说ID: ' + id);
            return id;
        }
        
        // 然后从页面元素中获取
        id = getNovelIdFromElements();
        if (id) {
            safeLog('从页面元素获取到小说ID: ' + id);
            return id;
        }
        
        // 最后从全局变量中获取
        id = getNovelIdFromGlobals();
        if (id) {
            safeLog('从全局变量获取到小说ID: ' + id);
            return id;
        }
        
        // 如果都获取不到，尝试从localStorage中获取
        try {
            id = localStorage.getItem('lastNovelId');
            if (id) {
                safeLog('从localStorage获取到小说ID: ' + id);
                return id;
            }
        } catch (e) {
            safeError('从localStorage获取小说ID时出错: ' + e.message);
        }
        
        // 如果还是获取不到，使用硬编码的默认值
        safeLog('无法获取小说ID，使用默认值: 1');
        return '1';
    }
    
    // 拦截所有导航操作
    function interceptNavigation() {
        safeLog('开始拦截所有导航操作');
        
        // 拦截window.location赋值
        const locationDescriptor = Object.getOwnPropertyDescriptor(window, 'location');
        if (locationDescriptor && locationDescriptor.configurable) {
            Object.defineProperty(window, 'location', {
                get: function() {
                    return originalWindowLocation;
                },
                set: function(url) {
                    handleNavigation(url);
                    return originalWindowLocation;
                },
                configurable: true
            });
        }
        
        // 拦截window.location.href赋值
        const hrefDescriptor = Object.getOwnPropertyDescriptor(Location.prototype, 'href');
        if (hrefDescriptor && hrefDescriptor.configurable) {
            Object.defineProperty(Location.prototype, 'href', {
                get: function() {
                    return originalWindowLocation.href;
                },
                set: function(url) {
                    handleNavigation(url);
                    return originalWindowLocation.href;
                },
                configurable: true
            });
        }
        
        // 拦截history.pushState
        history.pushState = function() {
            const url = arguments[2];
            if (url) {
                handleNavigation(url);
            }
            return originalHistoryPushState.apply(this, arguments);
        };
        
        // 拦截history.replaceState
        history.replaceState = function() {
            const url = arguments[2];
            if (url) {
                handleNavigation(url);
            }
            return originalHistoryReplaceState.apply(this, arguments);
        };
        
        // 拦截window.open
        window.open = function(url) {
            if (url) {
                url = handleNavigation(url, true);
            }
            return originalWindowOpen.call(window, url);
        };
        
        safeLog('所有导航操作已拦截');
    }
    
    // 处理导航操作
    function handleNavigation(url, isWindowOpen = false) {
        try {
            safeLog('处理导航操作: ' + url);
            
            // 如果是相对URL，转换为绝对URL
            if (url && typeof url === 'string' && !url.startsWith('http') && !url.startsWith('javascript:')) {
                // 如果是首页URL，不做处理
                if (url === '/' || url === '/index' || url === '/home') {
                    safeLog('首页URL，不做处理: ' + url);
                    return url;
                }
                
                // 获取小说ID
                const novelId = getNovelId();
                
                // 修复章节分析链接
                if (url.includes('/chapters') || url === '/chapters') {
                    const fixedUrl = '/novel/' + novelId + '/chapters';
                    safeLog('修复章节分析链接: ' + url + ' -> ' + fixedUrl);
                    
                    // 如果不是window.open，直接设置location.href
                    if (!isWindowOpen) {
                        originalWindowLocation.href = fixedUrl;
                    }
                    
                    return fixedUrl;
                }
                
                // 修复维度分析链接
                if (url.includes('/analysis/')) {
                    // 提取维度名称
                    const dimensionMatch = url.match(/\/analysis\/([^\/]+)/);
                    if (dimensionMatch && dimensionMatch[1]) {
                        const dimension = dimensionMatch[1];
                        const fixedUrl = '/novel/' + novelId + '/analysis/' + dimension;
                        safeLog('修复维度分析链接: ' + url + ' -> ' + fixedUrl);
                        
                        // 如果不是window.open，直接设置location.href
                        if (!isWindowOpen) {
                            originalWindowLocation.href = fixedUrl;
                        }
                        
                        return fixedUrl;
                    }
                }
            }
            
            return url;
        } catch (e) {
            safeError('处理导航操作时出错: ' + e.message);
            return url;
        }
    }
    
    // 修复所有按钮点击事件
    function fixAllButtons() {
        safeLog('开始修复所有按钮点击事件');
        
        // 获取小说ID
        const novelId = getNovelId();
        
        // 保存到localStorage，以便后续使用
        try {
            localStorage.setItem('lastNovelId', novelId);
        } catch (e) {
            safeError('保存小说ID到localStorage时出错: ' + e.message);
        }
        
        // 查找所有按钮
        const buttons = document.querySelectorAll('a, button, .btn, [role="button"]');
        safeLog('找到 ' + buttons.length + ' 个可能的按钮');
        
        buttons.forEach(function(button) {
            try {
                // 跳过已经修复过的按钮
                if (button.__forceButtonFixed) {
                    return;
                }
                
                // 标记按钮已修复
                button.__forceButtonFixed = true;
                
                // 获取按钮文本
                const text = button.textContent ? button.textContent.trim().toLowerCase() : '';
                
                // 修复章节分析按钮
                if (text.includes('章节') && (text.includes('分析') || text.includes('列表'))) {
                    safeLog('修复章节分析按钮: ' + text);
                    
                    // 如果是链接，修复href属性
                    if (button.tagName === 'A') {
                        button.href = '/novel/' + novelId + '/chapters';
                    }
                    
                    // 添加点击事件处理
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        safeLog('点击章节分析按钮，跳转到: /novel/' + novelId + '/chapters');
                        originalWindowLocation.href = '/novel/' + novelId + '/chapters';
                        
                        return false;
                    }, true);
                }
                // 修复维度分析按钮
                else if (button.hasAttribute('data-dimension')) {
                    const dimension = button.getAttribute('data-dimension');
                    if (dimension) {
                        safeLog('修复维度分析按钮: ' + dimension);
                        
                        // 如果是链接，修复href属性
                        if (button.tagName === 'A') {
                            button.href = '/novel/' + novelId + '/analysis/' + dimension;
                        }
                        
                        // 添加点击事件处理
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            
                            safeLog('点击维度分析按钮，跳转到: /novel/' + novelId + '/analysis/' + dimension);
                            originalWindowLocation.href = '/novel/' + novelId + '/analysis/' + dimension;
                            
                            return false;
                        }, true);
                    }
                }
            } catch (e) {
                safeError('修复按钮时出错: ' + e.message);
            }
        });
        
        safeLog('所有按钮点击事件已修复');
    }
    
    // 初始化函数
    function initialize() {
        safeLog('初始化强制导航修复');
        
        // 拦截所有导航操作
        interceptNavigation();
        
        // 修复所有按钮点击事件
        fixAllButtons();
        
        // 定期检查，应对动态加载的情况
        setInterval(fixAllButtons, 2000);
        
        safeLog('强制导航修复初始化完成');
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }
    
    // 导出API，方便调试和手动调用
    window.forceNavigationFix = {
        initialize: initialize,
        fixAllButtons: fixAllButtons,
        getNovelId: getNovelId
    };
})();
