"""
Novel data model for the 九猫 (Nine Cats) novel analysis system.
"""
import os
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Boolean
from sqlalchemy.orm import relationship

from src.models.base import Base

# 创建一个函数来替代已弃用的datetime.utcnow
def utc_now():
    """返回当前的UTC时间，替代已弃用的datetime.utcnow"""
    return datetime.now(timezone.utc)

class Novel(Base):
    """Novel data model."""

    __tablename__ = "novels"

    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    author = Column(String(255), nullable=True)
    content = Column(Text, nullable=False)
    file_path = Column(String(255), nullable=True)
    file_name = Column(String(255), nullable=True)
    word_count = Column(Integer, nullable=True)
    is_analyzed = Column(<PERSON>olean, default=False)
    novel_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Relationship with AnalysisResult
    analysis_results = relationship("AnalysisResult", back_populates="novel", cascade="all, delete-orphan")

    # Relationship with AnalysisCheckpoint
    analysis_checkpoints = relationship("AnalysisCheckpoint", back_populates="novel", cascade="all, delete-orphan")

    # 新增：与AnalysisProcess的关系定义
    analysis_processes = relationship("AnalysisProcess", back_populates="novel", cascade="all, delete-orphan")

    # 与Chapter的关系
    chapters = relationship("Chapter", back_populates="novel", cascade="all, delete-orphan")

    # 与ReferenceTemplate的关系
    reference_templates = relationship("ReferenceTemplate", back_populates="novel", cascade="all, delete-orphan")

    def __init__(
        self,
        title: str,
        content: str,
        author: Optional[str] = None,
        file_path: Optional[str] = None,
        file_name: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a novel.

        Args:
            title: Novel title.
            content: Novel content.
            author: Novel author.
            file_path: Path to the novel file.
            file_name: Name of the novel file.
            metadata: Additional metadata.
        """
        self.title = title
        self.author = author
        self.content = content
        self.file_path = file_path
        self.file_name = file_name
        self.novel_metadata = metadata or {}
        self.word_count = self._count_words(content)
        self.is_analyzed = False

    def _count_words(self, text: str) -> int:
        """
        Count the number of words in the text.

        Args:
            text: Text to count words in.

        Returns:
            Word count.
        """
        # For Chinese text, we'll count characters
        # For mixed text, this is an approximation
        return len(text)

    def get_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the novel.

        Returns:
            Dictionary with novel summary information.
        """
        return {
            "id": self.id,
            "title": self.title,
            "author": self.author,
            "word_count": self.word_count,
            "is_analyzed": self.is_analyzed,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "file_name": self.file_name,
            "analysis_count": len(self.analysis_results) if hasattr(self, "analysis_results") else 0,
            "has_detailed_process": any(len(result.processes) > 0 for result in self.analysis_results)
                                    if hasattr(self, "analysis_results") else False,
            "metadata": self.novel_metadata
        }

    def get_excerpt(self, length: int = 500) -> str:
        """
        Get an excerpt from the novel.

        Args:
            length: Maximum length of the excerpt.

        Returns:
            Novel excerpt.
        """
        if not self.content:
            return "小说内容为空"

        if len(self.content) <= length:
            return self.content

        # 尝试在句子结束处截断
        excerpt = self.content[:length]
        last_period = excerpt.rfind('。')
        last_question = excerpt.rfind('？')
        last_exclamation = excerpt.rfind('！')

        # 找到最后一个句子结束符
        last_sentence_end = max(last_period, last_question, last_exclamation)

        if last_sentence_end > length * 0.7:  # 如果最后一个句子结束符在摘要的后70%
            return self.content[:last_sentence_end + 1] + "..."
        else:
            return excerpt + "..."

    def get_file_name(self):
        """Get the file name from the file path."""
        if self.file_name:
            return self.file_name
        elif self.file_path:
            return os.path.basename(self.file_path)
        else:
            return f"{self.title}.txt"

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the novel to a dictionary.

        Returns:
            Dictionary representation of the novel.
        """
        return {
            "id": self.id,
            "title": self.title,
            "author": self.author,
            "content": self.content,
            "file_path": self.file_path,
            "word_count": self.word_count,
            "is_analyzed": self.is_analyzed,
            "metadata": self.novel_metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "file_name": self.file_name
        }

    @property
    def chapter_count(self) -> int:
        """
        返回小说的章节数。
        """
        # 如果实例有动态赋值的chapter_count属性，优先返回
        if hasattr(self, "__dict__") and "chapter_count" in self.__dict__:
            return self.__dict__["chapter_count"]
        # 否则返回章节关系的数量
        if hasattr(self, "chapters") and self.chapters is not None:
            return len(self.chapters)
        return 0

    @chapter_count.setter
    def chapter_count(self, value: int):
        """
        允许外部设置章节数。
        """
        self.__dict__["chapter_count"] = value
