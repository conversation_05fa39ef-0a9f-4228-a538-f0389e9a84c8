"""
九猫小说分析系统 - 完整版应用入口
"""

import os
import logging
from flask import Flask, render_template, request, redirect, url_for, session, jsonify
from sqlalchemy.orm import Session

from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.analysis_process import AnalysisProcess
from src.database import engine

# 配置日志
logging.basicConfig(level=logging.INFO)
app = Flask(__name__)
app.secret_key = os.urandom(24)
app.logger.setLevel(logging.INFO)

@app.route('/')
def index():
    """渲染完整版首页"""
    try:
        # 检查静态资源
        js_path = os.path.join(os.path.dirname(__file__), 'static', 'js')
        css_path = os.path.join(os.path.dirname(__file__), 'static', 'css')

        # 记录静态资源检查
        app.logger.info(f"检查JS目录: {os.path.exists(js_path)}")
        app.logger.info(f"检查CSS目录: {os.path.exists(css_path)}")

        # 检查并创建会话
        session = Session(engine)
        try:
            # 获取小说列表
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()
            app.logger.debug(f"加载首页，找到 {len(novels)} 本小说")

            # 渲染模板，提供novels变量
            return render_template('full_bootstrap_index.html', novels=novels)
        except Exception as db_error:
            # 数据库相关错误
            app.logger.error(f"数据库操作出错: {str(db_error)}", exc_info=True)

            # 尝试提供一个空的小说列表
            return render_template('full_bootstrap_index.html', novels=[])
        finally:
            # 确保会话始终关闭
            session.close()
    except Exception as e:
        # 处理任何其他异常
        app.logger.error(f"加载首页时遇到意外错误: {str(e)}", exc_info=True)

        # 尝试提供一个基本的错误页面
        try:
            return render_template('error.html',
                                 error_message="加载首页时出错",
                                 error_details=str(e),
                                 back_url=url_for('index'))
        except:
            # 如果渲染模板失败，提供一个非常基本的HTML响应
            return """
            <html>
                <head><title>错误</title></head>
                <body>
                    <h1>加载页面时出错</h1>
                    <p>请刷新页面或稍后再试</p>
                    <a href="/">返回首页</a>
                </body>
            </html>
            """

@app.route('/novels')
def novels():
    """显示小说列表页面"""
    try:
        session = Session(engine)
        try:
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()
            return render_template('novels.html', novels=novels)
        finally:
            session.close()
    except Exception as e:
        app.logger.error(f"加载小说列表页面时出错: {str(e)}", exc_info=True)
        return render_template('error.html', 
                             error_message="加载小说列表页面时出错",
                             error_details=str(e),
                             back_url=url_for('index'))

@app.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    """显示小说详情页面"""
    try:
        session = Session(engine)
        try:
            novel = session.query(Novel).filter(Novel.id == novel_id).first()
            if not novel:
                return render_template('error.html', 
                                     error_message="找不到指定的小说",
                                     back_url=url_for('novels'))
            
            # 获取分析结果
            analysis_result = session.query(AnalysisResult).filter(AnalysisResult.novel_id == novel_id).first()
            
            return render_template('novel_detail.html', novel=novel, analysis_result=analysis_result)
        finally:
            session.close()
    except Exception as e:
        app.logger.error(f"加载小说详情页面时出错: {str(e)}", exc_info=True)
        return render_template('error.html', 
                             error_message="加载小说详情页面时出错",
                             error_details=str(e),
                             back_url=url_for('novels'))

@app.route('/upload', methods=['GET', 'POST'])
def upload():
    """上传小说页面"""
    if request.method == 'GET':
        return render_template('upload.html')
    
    # POST请求处理上传
    try:
        title = request.form.get('title', '未命名小说')
        content = ''
        
        # 处理文件上传
        if 'file' in request.files:
            file = request.files['file']
            if file.filename:
                content = file.read().decode('utf-8', errors='ignore')
        
        # 处理文本粘贴
        elif 'content' in request.form:
            content = request.form['content']
        
        if not content:
            return render_template('upload.html', error="请上传文件或粘贴文本内容")
        
        # 保存到数据库
        session = Session(engine)
        try:
            novel = Novel(title=title, content=content)
            session.add(novel)
            session.commit()
            
            # 重定向到小说详情页
            return redirect(url_for('view_novel', novel_id=novel.id))
        finally:
            session.close()
    except Exception as e:
        app.logger.error(f"上传小说时出错: {str(e)}", exc_info=True)
        return render_template('upload.html', error=f"上传失败: {str(e)}")

@app.route('/analysis')
def analysis_records():
    """显示分析记录页面"""
    try:
        session = Session(engine)
        try:
            # 获取所有分析结果
            results = session.query(AnalysisResult).order_by(AnalysisResult.created_at.desc()).all()
            return render_template('analysis_records.html', results=results)
        finally:
            session.close()
    except Exception as e:
        app.logger.error(f"加载分析记录页面时出错: {str(e)}", exc_info=True)
        return render_template('error.html', 
                             error_message="加载分析记录页面时出错",
                             error_details=str(e),
                             back_url=url_for('index'))

@app.route('/dashboard')
def dashboard():
    """显示数据中心页面"""
    try:
        session = Session(engine)
        try:
            # 获取统计数据
            novel_count = session.query(Novel).count()
            analysis_count = session.query(AnalysisResult).count()
            
            # 获取最近的小说
            recent_novels = session.query(Novel).order_by(Novel.created_at.desc()).limit(5).all()
            
            # 获取最近的分析结果
            recent_analyses = session.query(AnalysisResult).order_by(AnalysisResult.created_at.desc()).limit(5).all()
            
            return render_template('dashboard.html', 
                                 novel_count=novel_count,
                                 analysis_count=analysis_count,
                                 recent_novels=recent_novels,
                                 recent_analyses=recent_analyses)
        finally:
            session.close()
    except Exception as e:
        app.logger.error(f"加载数据中心页面时出错: {str(e)}", exc_info=True)
        return render_template('error.html', 
                             error_message="加载数据中心页面时出错",
                             error_details=str(e),
                             back_url=url_for('index'))

@app.route('/settings')
def settings():
    """显示设置页面"""
    return render_template('settings.html')

@app.route('/api/system-status')
def system_status():
    """获取系统状态API"""
    try:
        # 模拟系统状态数据
        import random
        import psutil
        
        memory_usage = psutil.virtual_memory().percent
        
        status = {
            'memory_usage': memory_usage,
            'api_status': 'normal',
            'database_status': 'normal',
            'novel_count': 0,
            'analysis_count': 0
        }
        
        # 尝试获取实际数据
        try:
            session = Session(engine)
            try:
                status['novel_count'] = session.query(Novel).count()
                status['analysis_count'] = session.query(AnalysisResult).count()
            finally:
                session.close()
        except:
            # 如果数据库查询失败，使用默认值
            status['database_status'] = 'error'
        
        return jsonify(status)
    except Exception as e:
        app.logger.error(f"获取系统状态时出错: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=False)
