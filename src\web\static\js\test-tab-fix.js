/**
 * 九猫测试功能 - 标签页切换修复脚本
 * 解决测试功能页面中标签页无法正常切换的问题
 * 版本: 1.0.0
 */

(function() {
    'use strict';

    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[测试标签页修复] 脚本已加载');

        // 延迟执行，确保页面完全加载
        setTimeout(function() {
            initTabFix();
        }, 500);
    });

    /**
     * 初始化标签页修复
     */
    function initTabFix() {
        console.log('[测试标签页修复] 开始修复标签页切换功能');

        // 检测Bootstrap版本
        const isBootstrap5 = typeof bootstrap !== 'undefined';
        console.log('[测试标签页修复] Bootstrap版本: ' + (isBootstrap5 ? '5' : '4或更早'));

        // 检查是否在测试页面
        if (!isTestPage()) {
            console.log('[测试标签页修复] 不在测试页面，跳过修复');
            return;
        }

        // 修复标签页属性
        fixTabAttributes(isBootstrap5);

        // 修复jQuery的tab方法
        fixJQueryTabMethod();

        // 添加标签页点击事件
        addTabClickHandlers(isBootstrap5);

        // 修复Marked.js加载
        fixMarkedJs();

        console.log('[测试标签页修复] 修复完成');
    }

    /**
     * 检查是否在测试页面
     */
    function isTestPage() {
        // 检查URL是否包含test或测试
        if (window.location.pathname.includes('/test') ||
            window.location.pathname.includes('/测试')) {
            return true;
        }

        // 检查页面标题是否包含测试
        const title = document.title || '';
        if (title.includes('测试')) {
            return true;
        }

        // 检查是否存在测试页面的特定元素
        if (document.getElementById('resultTabs') ||
            document.querySelector('.test-tabs')) {
            return true;
        }

        return false;
    }

    /**
     * 修复标签页属性
     */
    function fixTabAttributes(isBootstrap5) {
        // 获取所有标签页链接
        const tabLinks = document.querySelectorAll('#resultTabs .nav-link');

        if (tabLinks.length === 0) {
            console.log('[测试标签页修复] 未找到标签页链接');
            return;
        }

        console.log(`[测试标签页修复] 找到${tabLinks.length}个标签页链接`);

        // 修复每个标签页链接的属性
        tabLinks.forEach(function(link) {
            if (isBootstrap5) {
                // Bootstrap 5使用data-bs-toggle和data-bs-target
                if (!link.hasAttribute('data-bs-toggle')) {
                    link.setAttribute('data-bs-toggle', 'tab');
                    console.log('[测试标签页修复] 添加data-bs-toggle属性');
                }
            } else {
                // Bootstrap 4使用data-toggle
                if (!link.hasAttribute('data-toggle')) {
                    link.setAttribute('data-toggle', 'tab');
                    console.log('[测试标签页修复] 添加data-toggle属性');
                }
            }

            // 确保role属性正确
            if (!link.hasAttribute('role')) {
                link.setAttribute('role', 'tab');
            }

            // 确保aria-controls属性正确
            const target = link.getAttribute('href');
            if (target && !link.hasAttribute('aria-controls')) {
                link.setAttribute('aria-controls', target.substring(1));
            }
        });

        // 修复标签页内容区域
        const tabPanes = document.querySelectorAll('.tab-pane');
        tabPanes.forEach(function(pane) {
            if (!pane.hasAttribute('role')) {
                pane.setAttribute('role', 'tabpanel');
            }
        });
    }

    /**
     * 修复jQuery的tab方法
     */
    function fixJQueryTabMethod() {
        // 确保jQuery已加载
        if (typeof jQuery === 'undefined') {
            console.log('[测试标签页修复] jQuery未加载，尝试加载');
            loadJQuery();
            return;
        }

        // 检查jQuery的tab方法
        if (typeof jQuery.fn.tab !== 'function') {
            console.log('[测试标签页修复] 添加jQuery.fn.tab方法');

            jQuery.fn.tab = function(action) {
                if (action === 'show') {
                    return this.each(function() {
                        // 获取目标面板
                        const target = this.getAttribute('href');
                        if (!target) return;

                        // 获取所有相关的标签页和面板
                        const parent = this.closest('.nav');
                        if (!parent) return;

                        const tabs = parent.querySelectorAll('.nav-link');
                        const panes = document.querySelectorAll('.tab-pane');

                        // 隐藏所有面板
                        panes.forEach(function(pane) {
                            pane.classList.remove('show', 'active');
                        });

                        // 取消激活所有标签
                        tabs.forEach(function(tab) {
                            tab.classList.remove('active');
                            tab.setAttribute('aria-selected', 'false');
                        });

                        // 激活当前标签和面板
                        this.classList.add('active');
                        this.setAttribute('aria-selected', 'true');

                        const targetPane = document.querySelector(target);
                        if (targetPane) {
                            targetPane.classList.add('show', 'active');
                        }

                        // 触发事件
                        const event = new Event('shown.bs.tab');
                        this.dispatchEvent(event);
                    });
                }
                return this;
            };
        }
    }

    /**
     * 添加标签页点击事件处理
     */
    function addTabClickHandlers(isBootstrap5) {
        // 获取所有标签页链接
        const tabLinks = document.querySelectorAll('#resultTabs .nav-link, #dimensionTabs .nav-link');

        if (tabLinks.length === 0) {
            console.log('[测试标签页修复] 未找到标签页链接');

            // 尝试使用更通用的选择器
            const allTabLinks = document.querySelectorAll('.nav-tabs .nav-link');
            if (allTabLinks.length > 0) {
                console.log(`[测试标签页修复] 找到${allTabLinks.length}个通用标签页链接`);
                allTabLinks.forEach(function(link) {
                    // 移除现有的点击事件，避免重复
                    link.removeEventListener('click', handleTabClick);

                    // 添加新的点击事件
                    link.addEventListener('click', handleTabClick);
                });
            }
            return;
        }

        console.log(`[测试标签页修复] 找到${tabLinks.length}个标签页链接`);

        tabLinks.forEach(function(link) {
            // 移除现有的点击事件，避免重复
            link.removeEventListener('click', handleTabClick);

            // 添加新的点击事件
            link.addEventListener('click', handleTabClick);
        });

        console.log('[测试标签页修复] 已添加标签页点击事件处理');

        // 尝试修复维度点击事件
        fixDimensionClickHandlers();
    }

    /**
     * 修复维度点击事件
     */
    function fixDimensionClickHandlers() {
        const dimensionItems = document.querySelectorAll('.dimension-item');
        if (dimensionItems.length === 0) {
            return;
        }

        console.log(`[测试标签页修复] 找到${dimensionItems.length}个维度项`);

        // 确保维度点击事件正常工作
        dimensionItems.forEach(function(item) {
            item.addEventListener('click', function() {
                // 高亮选中的维度
                dimensionItems.forEach(function(di) {
                    di.classList.remove('selected');
                });
                item.classList.add('selected');

                // 获取维度键
                const dimensionKey = item.getAttribute('data-dimension');
                if (!dimensionKey) return;

                console.log(`[测试标签页修复] 点击维度: ${dimensionKey}`);

                // 尝试调用原始的getDimensionDetail函数
                if (typeof window.getDimensionDetail === 'function') {
                    window.getDimensionDetail(dimensionKey);
                }
            });
        });
    }

    /**
     * 处理标签页点击事件
     */
    function handleTabClick(e) {
        e.preventDefault();

        const link = e.currentTarget;
        const target = link.getAttribute('href');

        if (!target) return;

        console.log(`[测试标签页修复] 处理标签页点击: ${target}`);

        // 获取所有相关的标签页和面板
        const parent = link.closest('.nav');
        if (!parent) return;

        const tabsContainer = link.closest('.nav-tabs');
        const tabContentId = tabsContainer.getAttribute('aria-controls') ||
                            tabsContainer.getAttribute('data-bs-target') ||
                            tabsContainer.id.replace('Tabs', 'TabContent');

        let tabContent;
        if (tabContentId) {
            tabContent = document.getElementById(tabContentId);
        }

        if (!tabContent) {
            // 尝试查找相关的标签内容容器
            const possibleIds = [
                'resultTabsContent',
                'dimensionTabContent',
                target.substring(1) + 'Content'
            ];

            for (const id of possibleIds) {
                const element = document.getElementById(id);
                if (element) {
                    tabContent = element;
                    break;
                }
            }

            // 如果仍然找不到，尝试查找父元素的下一个兄弟元素
            if (!tabContent && parent.nextElementSibling &&
                parent.nextElementSibling.classList.contains('tab-content')) {
                tabContent = parent.nextElementSibling;
            }

            // 最后尝试查找页面上的任何.tab-content元素
            if (!tabContent) {
                tabContent = document.querySelector('.tab-content');
            }
        }

        const tabs = parent.querySelectorAll('.nav-link');
        const panes = tabContent ?
                    tabContent.querySelectorAll('.tab-pane') :
                    document.querySelectorAll('.tab-pane');

        // 隐藏所有面板
        panes.forEach(function(pane) {
            pane.classList.remove('show', 'active');
        });

        // 取消激活所有标签
        tabs.forEach(function(tab) {
            tab.classList.remove('active');
            tab.setAttribute('aria-selected', 'false');
        });

        // 激活当前标签和面板
        link.classList.add('active');
        link.setAttribute('aria-selected', 'true');

        const targetPane = document.querySelector(target);
        if (targetPane) {
            targetPane.classList.add('show', 'active');

            // 触发自定义事件，通知其他脚本标签页已切换
            const event = new CustomEvent('tab-changed', {
                detail: {
                    tabId: target.substring(1),
                    element: targetPane
                }
            });
            document.dispatchEvent(event);

            // 尝试触发Bootstrap的shown.bs.tab事件
            try {
                const bsEvent = new CustomEvent('shown.bs.tab', {
                    bubbles: true,
                    cancelable: true
                });
                link.dispatchEvent(bsEvent);
            } catch (error) {
                console.error('[测试标签页修复] 触发Bootstrap事件失败:', error);
            }
        } else {
            console.warn(`[测试标签页修复] 未找到目标面板: ${target}`);
        }

        console.log(`[测试标签页修复] 切换到标签页: ${target}`);
    }

    /**
     * 加载jQuery
     */
    function loadJQuery() {
        const script = document.createElement('script');
        script.src = '/static/js/lib/jquery.min.js';
        script.onload = function() {
            console.log('[测试标签页修复] jQuery加载成功');
            fixJQueryTabMethod();
        };
        script.onerror = function() {
            console.error('[测试标签页修复] jQuery加载失败');
        };
        document.head.appendChild(script);
    }

    /**
     * 修复Marked.js加载
     */
    function fixMarkedJs() {
        if (typeof marked !== 'undefined') {
            console.log('[测试标签页修复] Marked.js已加载');
            return;
        }

        console.log('[测试标签页修复] 尝试加载Marked.js');

        // 尝试从不同路径加载Marked.js
        const paths = [
            '/static/js/marked.min.js',
            '/static/js/lib/marked.min.js',
            '/direct-static/js/marked.min.js',
            'https://cdn.jsdelivr.net/npm/marked/marked.min.js'
        ];

        loadScript(paths, 0);
    }

    /**
     * 尝试从多个路径加载脚本
     */
    function loadScript(paths, index) {
        if (index >= paths.length) {
            console.error('[测试标签页修复] 所有路径都无法加载Marked.js');
            return;
        }

        const script = document.createElement('script');
        script.src = paths[index];

        script.onload = function() {
            console.log(`[测试标签页修复] 从${paths[index]}加载Marked.js成功`);

            // 初始化Markdown渲染器
            if (typeof marked !== 'undefined') {
                marked.setOptions({
                    breaks: true,
                    gfm: true
                });
            }
        };

        script.onerror = function() {
            console.log(`[测试标签页修复] 从${paths[index]}加载Marked.js失败，尝试下一个路径`);
            loadScript(paths, index + 1);
        };

        document.head.appendChild(script);
    }
})();
