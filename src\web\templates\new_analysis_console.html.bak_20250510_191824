{% extends "new_base.html" %}

{% block title %}分析控制台 - {{ novel.title }} - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-0"><i class="fas fa-terminal me-2"></i>分析控制台</h1>
        <p class="text-muted">查看小说《{{ novel.title }}》的分析过程和日志</p>
    </div>
    <div class="col-md-4 text-md-end">
        <a href="{{ url_for('new.view_novel', novel_id=novel.id) }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>返回小说详情
        </a>
    </div>
</div>

<div class="row">
    <!-- 左侧：分析维度列表 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-cubes me-2"></i>分析维度</h3>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    {% for dimension_key, dimension_name in dimensions.items() %}
                        <a href="#dimension-{{ dimension_key }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if dimension_key in available_dimensions %}active{% endif %}" data-dimension="{{ dimension_key }}">
                            {{ dimension_name }}
                            {% if dimension_key in available_dimensions %}
                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                            {% else %}
                                <span class="badge bg-secondary rounded-pill"><i class="fas fa-times"></i></span>
                            {% endif %}
                        </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧：分析日志和进度 -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title"><i class="fas fa-chart-line me-2"></i>分析进度</h3>
                <div>
                    <button class="btn btn-sm btn-outline-primary" id="refreshProgressBtn">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="progressContainer">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">加载分析进度...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title"><i class="fas fa-list-alt me-2"></i>分析日志</h3>
                <div>
                    <select class="form-select form-select-sm" id="logLevelFilter">
                        <option value="all">所有级别</option>
                        <option value="error">错误</option>
                        <option value="warning">警告</option>
                        <option value="info">信息</option>
                        <option value="debug">调试</option>
                    </select>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="log-container" id="logContainer">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">加载分析日志...</p>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-primary" id="loadMoreLogsBtn">
                    <i class="fas fa-plus me-1"></i>加载更多
                </button>
                <button class="btn btn-sm btn-outline-secondary" id="clearLogsBtn">
                    <i class="fas fa-eraser me-1"></i>清空日志
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始加载数据
        loadAnalysisProgress();
        loadAnalysisLogs();

        // 刷新按钮点击事件
        document.getElementById('refreshProgressBtn').addEventListener('click', function() {
            loadAnalysisProgress();
        });

        // 日志级别过滤器
        document.getElementById('logLevelFilter').addEventListener('change', function() {
            loadAnalysisLogs();
        });

        // 加载更多日志按钮
        document.getElementById('loadMoreLogsBtn').addEventListener('click', function() {
            loadMoreLogs();
        });

        // 清空日志按钮
        document.getElementById('clearLogsBtn').addEventListener('click', function() {
            if (confirm('确定要清空日志显示吗？这不会删除实际的日志文件。')) {
                document.getElementById('logContainer').innerHTML = '<div class="text-center py-3">日志已清空</div>';
            }
        });

        // 维度列表点击事件
        document.querySelectorAll('.list-group-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const dimension = this.getAttribute('data-dimension');

                // 高亮选中的维度
                document.querySelectorAll('.list-group-item').forEach(i => {
                    i.classList.remove('active');
                });
                this.classList.add('active');

                // 加载该维度的分析日志
                loadDimensionLogs(dimension);
            });
        });

        // 设置定时刷新
        setInterval(function() {
            loadAnalysisProgress();
        }, 10000); // 每10秒刷新一次分析进度
    });

    // 加载分析进度
    function loadAnalysisProgress() {
        const progressContainer = document.getElementById('progressContainer');
        progressContainer.innerHTML = '<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">加载分析进度...</p></div>';

        // 这里应该调用API获取实际的分析进度
        // 模拟数据
        setTimeout(() => {
            let html = '';

            // 为每个维度生成进度条
            {% for dimension_key, dimension_name in dimensions.items() %}
                {% if dimension_key in available_dimensions %}
                html += `
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>${'{{ dimension_name }}'}</span>
                            <span class="badge bg-success">已完成</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar"
                                 role="progressbar"
                                 style="width: 100%"
                                 aria-valuenow="100"
                                 aria-valuemin="0"
                                 aria-valuemax="100"></div>
                        </div>
                    </div>
                `;
                {% else %}
                html += `
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>${'{{ dimension_name }}'}</span>
                            <span class="badge bg-secondary">未开始</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                 style="width: 0%"
                                 aria-valuenow="0"
                                 aria-valuemin="0"
                                 aria-valuemax="100"></div>
                        </div>
                    </div>
                `;
                {% endif %}
            {% endfor %}

            progressContainer.innerHTML = html;
        }, 1000);
    }

    // 加载分析日志
    function loadAnalysisLogs() {
        const logLevel = document.getElementById('logLevelFilter').value;
        const logContainer = document.getElementById('logContainer');

        // 这里应该调用API获取实际的分析日志
        // 模拟数据
        setTimeout(() => {
            let html = '<div class="log-list">';

            // 模拟日志数据
            const logs = [
                { timestamp: new Date().toISOString(), level: 'INFO', message: '开始分析小说《{{ novel.title }}》' },
                { timestamp: new Date().toISOString(), level: 'INFO', message: '正在分析语言风格维度...' },
                { timestamp: new Date().toISOString(), level: 'INFO', message: '语言风格分析完成' },
                { timestamp: new Date().toISOString(), level: 'WARNING', message: '节奏与节奏分析可能需要较长时间' },
                { timestamp: new Date().toISOString(), level: 'INFO', message: '正在分析节奏与节奏维度...' },
                { timestamp: new Date().toISOString(), level: 'ERROR', message: '分析过程中遇到错误: 内存不足' },
                { timestamp: new Date().toISOString(), level: 'INFO', message: '重试分析节奏与节奏维度...' },
                { timestamp: new Date().toISOString(), level: 'INFO', message: '节奏与节奏分析完成' }
            ];

            // 根据选择的日志级别过滤
            const filteredLogs = logLevel === 'all'
                ? logs
                : logs.filter(log => log.level.toLowerCase() === logLevel.toLowerCase());

            if (filteredLogs.length === 0) {
                html = '<div class="text-center py-3">暂无符合条件的日志</div>';
            } else {
                filteredLogs.forEach(log => {
                    html += `
                        <div class="log-item log-${log.level.toLowerCase()}">
                            <div class="log-time">${formatDateTime(log.timestamp)}</div>
                            <div class="log-level">${log.level}</div>
                            <div class="log-message">${log.message}</div>
                        </div>
                    `;
                });
                html += '</div>';
            }

            logContainer.innerHTML = html;
        }, 1000);
    }

    // 加载特定维度的日志
    function loadDimensionLogs(dimension) {
        const logContainer = document.getElementById('logContainer');
        logContainer.innerHTML = '<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">加载维度日志...</p></div>';

        // 这里应该调用API获取特定维度的分析日志
        // 模拟数据
        setTimeout(() => {
            const dimensionName = getDimensionName(dimension);
            let html = '<div class="log-list">';

            // 模拟日志数据
            const logs = [
                { timestamp: new Date().toISOString(), level: 'INFO', message: `开始分析${dimensionName}维度` },
                { timestamp: new Date().toISOString(), level: 'INFO', message: `正在处理${dimensionName}数据...` },
                { timestamp: new Date().toISOString(), level: 'INFO', message: `${dimensionName}分析完成` }
            ];

            logs.forEach(log => {
                html += `
                    <div class="log-item log-${log.level.toLowerCase()}">
                        <div class="log-time">${formatDateTime(log.timestamp)}</div>
                        <div class="log-level">${log.level}</div>
                        <div class="log-message">${log.message}</div>
                    </div>
                `;
            });
            html += '</div>';

            logContainer.innerHTML = html;
        }, 1000);
    }

    // 加载更多日志
    function loadMoreLogs() {
        alert('加载更多日志功能尚未实现');
    }

    // 格式化日期时间
    function formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString();
    }

    // 获取维度名称
    function getDimensionName(dimension) {
        const dimensionNames = {
            {% for dimension_key, dimension_name in dimensions.items() %}
                '{{ dimension_key }}': '{{ dimension_name }}',
            {% endfor %}
        };

        return dimensionNames[dimension] || dimension;
    }
</script>
{% endblock %}

{% block extra_css %}
<style>
    .log-container {
        max-height: 500px;
        overflow-y: auto;
    }

    .log-list {
        padding: 0;
    }

    .log-item {
        padding: 0.5rem 1rem;
        border-bottom: 1px solid #eee;
        display: flex;
        flex-wrap: wrap;
    }

    .log-item:last-child {
        border-bottom: none;
    }

    .log-time {
        color: #666;
        font-size: 0.85rem;
        margin-right: 1rem;
        white-space: nowrap;
    }

    .log-level {
        font-weight: bold;
        margin-right: 1rem;
        min-width: 60px;
    }

    .log-message {
        flex: 1;
        word-break: break-word;
    }

    .log-error .log-level {
        color: #dc3545;
    }

    .log-warning .log-level {
        color: #ffc107;
    }

    .log-info .log-level {
        color: #0d6efd;
    }

    .log-debug .log-level {
        color: #6c757d;
    }

    .list-group-item.active {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
</style>
{% endblock %}
