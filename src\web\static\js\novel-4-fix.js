/**
 * 九猫 - 专门修复novel/4页面的JSON解析错误
 * 这个脚本会在页面加载时立即执行，直接替换有问题的JSON数据
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('novel/4页面修复脚本已加载');
    
    // 在页面加载前执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复novel/4页面');
        
        // 检查是否在novel/4页面
        if (window.location.pathname === '/novel/4') {
            console.log('检测到novel/4页面，应用特殊修复');
            
            // 查找所有内联脚本
            var scripts = document.querySelectorAll('script:not([src])');
            
            scripts.forEach(function(script) {
                var content = script.textContent || '';
                
                // 检查是否包含character_relationships错误
                if (content.includes('character_relationships** 时遇到了问题')) {
                    console.log('找到包含character_relationships错误的脚本');
                    
                    // 直接替换有问题的JSON字符串
                    var fixedContent = content.replace(
                        /("character_relationships":[^}]*"content":"[^"]*character_relationships\*\* 时遇到了问题[^"]*\\n\\n## 错误信息\\n```\\nname )(.*?)(")/g,
                        '$1"$3'
                    );
                    
                    // 如果内容被修改，替换脚本
                    if (fixedContent !== content) {
                        console.log('替换修复后的脚本');
                        var newScript = document.createElement('script');
                        newScript.textContent = fixedContent;
                        script.parentNode.replaceChild(newScript, script);
                    }
                }
                
                // 检查是否包含serverData变量
                if (content.includes('serverData = ') && content.includes('character_relationships')) {
                    console.log('找到包含serverData的脚本');
                    
                    // 直接替换有问题的JSON字符串
                    var fixedContent = content.replace(
                        /(serverData\s*=\s*)[^;]*(;)/g,
                        function(match, prefix, suffix) {
                            console.log('替换serverData');
                            
                            // 创建一个有效的serverData对象
                            var validServerData = {
                                novel: {
                                    id: 4,
                                    title: "第1章 她爬到了修仙界",
                                    author: "未知"
                                },
                                analysis_results: {
                                    character_relationships: {
                                        dimension: "character_relationships",
                                        content: "# 分析过程中出错\n\n## 错误详情\n分析维度 **character_relationships** 时遇到了问题。\n\n## 错误信息\n```\nname 'stats_start' is not defined\n```\n\n## 建议操作\n请尝试以下解决方法：\n1. 刷新页面并重新尝试分析\n2. 检查小说文本是否过长或包含特殊字符\n3. 确认API连接正常",
                                        metadata: {
                                            processing_time: 0,
                                            chunk_count: 0,
                                            api_calls: 0,
                                            tokens_used: 0,
                                            cost: 0
                                        }
                                    }
                                },
                                analysis_in_progress: {},
                                available_dimensions: [
                                    "language_style", "paragraph_length", "rhythm_pacing", "sentence_variation",
                                    "character_relationships", "plot_structure", "theme_analysis", "opening_effectiveness",
                                    "climax_pacing", "ending_satisfaction", "narrative_perspective", "chapter_outline", "outline"
                                ]
                            };
                            
                            return prefix + JSON.stringify(validServerData) + suffix;
                        }
                    );
                    
                    // 如果内容被修改，替换脚本
                    if (fixedContent !== content) {
                        console.log('替换修复后的serverData脚本');
                        var newScript = document.createElement('script');
                        newScript.textContent = fixedContent;
                        script.parentNode.replaceChild(newScript, script);
                    }
                }
            });
            
            // 查找所有可能包含JSON数据的元素
            var jsonElements = document.querySelectorAll('[data-json], [data-metadata]');
            
            jsonElements.forEach(function(element) {
                try {
                    // 检查data-json属性
                    if (element.hasAttribute('data-json')) {
                        var jsonContent = element.getAttribute('data-json');
                        
                        if (jsonContent && jsonContent.includes('character_relationships** 时遇到了问题')) {
                            console.log('找到包含character_relationships错误的data-json属性');
                            
                            // 直接替换有问题的JSON字符串
                            var fixedJson = jsonContent.replace(
                                /(character_relationships\*\* 时遇到了问题[^"]*\\n\\n## 错误信息\\n```\\nname )(.*?)(")/g,
                                '$1"$3'
                            );
                            
                            // 更新元素属性
                            element.setAttribute('data-json', fixedJson);
                        }
                    }
                    
                    // 检查data-metadata属性
                    if (element.hasAttribute('data-metadata')) {
                        var metadataContent = element.getAttribute('data-metadata');
                        
                        if (metadataContent && metadataContent.includes('character_relationships** 时遇到了问题')) {
                            console.log('找到包含character_relationships错误的data-metadata属性');
                            
                            // 直接替换有问题的JSON字符串
                            var fixedMetadata = metadataContent.replace(
                                /(character_relationships\*\* 时遇到了问题[^"]*\\n\\n## 错误信息\\n```\\nname )(.*?)(")/g,
                                '$1"$3'
                            );
                            
                            // 更新元素属性
                            element.setAttribute('data-metadata', fixedMetadata);
                        }
                    }
                } catch (e) {
                    console.error('处理JSON元素时出错:', e.message);
                }
            });
            
            // 直接修复页面上的分析卡片
            var analysisCards = document.querySelectorAll('.analysis-card[data-dimension="character_relationships"]');
            
            analysisCards.forEach(function(card) {
                try {
                    // 查找内容元素
                    var contentElement = card.querySelector('.analysis-content');
                    
                    if (contentElement && contentElement.textContent.includes('character_relationships** 时遇到了问题')) {
                        console.log('找到包含character_relationships错误的分析卡片');
                        
                        // 创建一个有效的内容
                        var validContent = "# 分析过程中出错\n\n## 错误详情\n分析维度 **character_relationships** 时遇到了问题。\n\n## 错误信息\n```\nname 'stats_start' is not defined\n```\n\n## 建议操作\n请尝试以下解决方法：\n1. 刷新页面并重新尝试分析\n2. 检查小说文本是否过长或包含特殊字符\n3. 确认API连接正常";
                        
                        // 更新内容
                        contentElement.textContent = validContent;
                    }
                } catch (e) {
                    console.error('处理分析卡片时出错:', e.message);
                }
            });
        }
    });
    
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && 
            (event.error.message.includes('JSON.parse') || 
             event.error.message.includes('Unexpected token') ||
             event.error.message.includes('Unterminated string'))) {
            console.error('捕获到JSON相关错误:', event.error.message);
            
            // 如果是novel/4页面，尝试刷新页面
            if (window.location.pathname === '/novel/4') {
                console.log('在novel/4页面捕获到JSON错误，尝试刷新页面');
                
                // 防止无限刷新
                if (!sessionStorage.getItem('novel4_refreshed')) {
                    sessionStorage.setItem('novel4_refreshed', 'true');
                    // 延迟1秒后刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                }
            }
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
})();
