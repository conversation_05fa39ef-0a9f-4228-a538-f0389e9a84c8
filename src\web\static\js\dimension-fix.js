/**
 * 九猫 - 维度选择修复脚本（增强版）
 * 专门处理维度选择对话框中维度列表不显示的问题
 * 版本: 1.0.2
 * 更新内容:
 * - 修复维度列表显示问题，确保显示全部13个维度
 * - 使用与后端一致的维度键值对格式
 * - 改进表单提交逻辑，确保正确传递维度参数
 * - 增强调试日志
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._dimensionFixLoaded) {
        console.log('维度选择修复脚本已加载，避免重复执行');
        return;
    }

    // 标记脚本已加载
    window._dimensionFixLoaded = true;

    console.log('维度选择修复脚本(增强版)已加载 - 版本1.0.2');

    // 保存原始的console.log方法，用于调试
    const originalConsoleLog = console.log;

    // 与后端一致的13个维度定义（键值对形式，方便与后端对应）
    const dimensions = [
        {key: 'language_style', name: '语言风格'},
        {key: 'rhythm_pacing', name: '节奏与节奏'},
        {key: 'structure', name: '结构分析'},
        {key: 'sentence_variation', name: '句式变化'},
        {key: 'paragraph_length', name: '段落长度'},
        {key: 'perspective_shifts', name: '视角转换'},
        {key: 'paragraph_flow', name: '段落流畅度'},
        {key: 'novel_characteristics', name: '小说特点'},
        {key: 'world_building', name: '世界构建'},
        {key: 'chapter_outline', name: '章节大纲'},
        {key: 'character_relationships', name: '人物关系'},
        {key: 'opening_effectiveness', name: '开篇效果'},
        {key: 'climax_pacing', name: '高潮节奏'}
    ];

    // 修复维度选择对话框 - 使用多种方法确保检测
    function fixDimensionSelection() {
        originalConsoleLog('开始修复维度选择对话框 - 增强版');

        // 方法1: 监听DOM变化，当维度选择对话框出现时进行修复
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        checkAndFixNode(node);
                    }
                } else if (mutation.target) {
                    // 也检查修改的节点，可能是已存在的对话框被更新
                    checkAndFixNode(mutation.target);
                }
            });
        });

        // 辅助函数: 检查节点是否是维度选择对话框并修复
        function checkAndFixNode(node) {
            // 跳过非元素节点
            if (!node || node.nodeType !== 1) return;

            // 检查自身是否是modal
            if (node.classList && node.classList.contains('modal')) {
                checkAndFixModal(node);
            }

            // 检查是否包含modal
            const modals = node.querySelectorAll ? node.querySelectorAll('.modal') : [];
            for (let i = 0; i < modals.length; i++) {
                checkAndFixModal(modals[i]);
            }

            // 特殊情况: 直接检查是否包含标题为"选择分析维度"的元素
            const titles = node.querySelectorAll ? node.querySelectorAll('.modal-title') : [];
            for (let i = 0; i < titles.length; i++) {
                if (titles[i].textContent && titles[i].textContent.includes('选择分析维度')) {
                    // 向上查找modal
                    let modal = titles[i].closest('.modal');
                    if (modal) {
                        checkAndFixModal(modal);
                    }
                }
            }
        }

        // 辅助函数: 检查并修复modal
        function checkAndFixModal(modal) {
            const title = modal.querySelector('.modal-title');
            if (title && title.textContent && title.textContent.includes('选择分析维度')) {
                originalConsoleLog('检测到维度选择对话框，开始修复');
                originalConsoleLog('对话框标题: ' + title.textContent);
                originalConsoleLog('对话框ID: ' + (modal.id || '无ID'));

                // 检查是否已有维度选项
                const existingCheckboxes = modal.querySelectorAll('.dimension-checkbox');
                originalConsoleLog('现有维度选项数量: ' + existingCheckboxes.length);

                // 延迟一点点确保DOM已完全渲染
                setTimeout(function() {
                    fixDimensionCheckboxes(modal);
                }, 50);
            }
        }

        // 开始观察文档变化 - 使用更广泛的设置
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            characterData: false,
            attributeFilter: ['class', 'style', 'id']
        });

        // 方法2: 使用定时器定期检查页面上是否有维度选择对话框
        function periodicCheck() {
            originalConsoleLog('定期检查维度选择对话框');
            // 检查所有打开的模态框
            const modals = document.querySelectorAll('.modal.show, .modal[style*="display: block"]');
            modals.forEach(function(modal) {
                checkAndFixModal(modal);
            });

            // 特殊情况：直接查找标题
            const titles = document.querySelectorAll('.modal-title');
            titles.forEach(function(title) {
                if (title.textContent && title.textContent.includes('选择分析维度')) {
                    let modal = title.closest('.modal');
                    if (modal) {
                        checkAndFixModal(modal);
                    }
                }
            });
        }

        // 立即执行第一次检查
        periodicCheck();

        // 设置周期性检查（每2秒检查一次）
        setInterval(periodicCheck, 2000);

        // 方法3: 监听click事件，可能会触发维度选择对话框
        document.addEventListener('click', function(event) {
            // 检查是否点击了分析按钮
            const target = event.target;
            if (target && (
                target.textContent.includes('分析') ||
                target.classList.contains('analyze-btn') ||
                target.closest('button[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]')
            )) {
                originalConsoleLog('检测到分析按钮点击');
            }

            // 延迟检查，因为对话框可能在点击后才显示
            setTimeout(function() {
                periodicCheck();
            }, 300);
        });

        // 方法4: 监听Bootstrap的modal事件
        if (typeof $ !== 'undefined' || typeof jQuery !== 'undefined') {
            const jq = $ || jQuery;
            jq(document).on('shown.bs.modal', function(event) {
                const modal = event.target;
                checkAndFixModal(modal);
            });
        }
    }

    // 修复维度复选框 - 增强版
    function fixDimensionCheckboxes(modal) {
        originalConsoleLog('修复维度复选框 - 增强版');

        try {
            // 查找维度列表容器 - 尝试多种选择器
            let dimensionList = modal.querySelector('.modal-body');

            if (!dimensionList) {
                originalConsoleLog('未找到.modal-body，尝试其他选择器');
                dimensionList = modal.querySelector('.modal-content > div:not(.modal-header):not(.modal-footer)');
            }

            if (!dimensionList) {
                originalConsoleLog('仍未找到维度列表容器，创建一个新的');
                dimensionList = document.createElement('div');
                dimensionList.className = 'modal-body';
                const modalContent = modal.querySelector('.modal-content');
                const modalFooter = modal.querySelector('.modal-footer');
                if (modalContent) {
                    if (modalFooter) {
                        modalContent.insertBefore(dimensionList, modalFooter);
                    } else {
                        modalContent.appendChild(dimensionList);
                    }
                }
            }

            if (!dimensionList) {
                originalConsoleLog('无法找到或创建维度列表容器，修复失败');
                return;
            }

            // 检查是否已经有维度项
            const existingDimensions = dimensionList.querySelectorAll('input[type="checkbox"]:not(.all-select)');
            if (existingDimensions.length > 1) {
                originalConsoleLog(`已存在${existingDimensions.length}个维度项，无需修复`);
                return;
            }

            // 获取或创建全选复选框
            let selectAllCheckbox = dimensionList.querySelector('input[type="checkbox"]');
            let selectAllLabel;

            if (!selectAllCheckbox) {
                originalConsoleLog('未找到全选复选框，创建一个新的');

                const selectAllContainer = document.createElement('div');
                selectAllContainer.className = 'form-check';

                selectAllCheckbox = document.createElement('input');
                selectAllCheckbox.type = 'checkbox';
                selectAllCheckbox.className = 'form-check-input all-select';
                selectAllCheckbox.id = 'dimension-all';
                selectAllCheckbox.checked = true;

                selectAllLabel = document.createElement('label');
                selectAllLabel.className = 'form-check-label';
                selectAllLabel.htmlFor = 'dimension-all';
                selectAllLabel.textContent = '全选';

                selectAllContainer.appendChild(selectAllCheckbox);
                selectAllContainer.appendChild(selectAllLabel);
                dimensionList.appendChild(selectAllContainer);
            } else {
                // 找到现有的全选标签
                selectAllLabel = dimensionList.querySelector('label[for="' + selectAllCheckbox.id + '"]');
                if (!selectAllLabel) {
                    // 尝试找到与复选框相邻的标签
                    let next = selectAllCheckbox.nextElementSibling;
                    if (next && next.tagName === 'LABEL') {
                        selectAllLabel = next;
                    }
                }

                // 确保复选框有正确的class
                selectAllCheckbox.classList.add('all-select');
            }

            // 创建维度列表容器
            const dimensionItemsContainer = document.createElement('div');
            dimensionItemsContainer.className = 'dimension-items';
            dimensionItemsContainer.style.marginTop = '15px';
            dimensionItemsContainer.style.maxHeight = '300px';
            dimensionItemsContainer.style.overflowY = 'auto';
            dimensionItemsContainer.style.border = '1px solid #dee2e6';
            dimensionItemsContainer.style.borderRadius = '4px';
            dimensionItemsContainer.style.padding = '10px';

            // 添加维度复选框
            dimensions.forEach(function(dimension, index) {
                const dimensionItem = document.createElement('div');
                dimensionItem.className = 'form-check';
                dimensionItem.style.marginBottom = '8px';

                const dimensionCheckbox = document.createElement('input');
                dimensionCheckbox.type = 'checkbox';
                dimensionCheckbox.className = 'form-check-input dimension-checkbox';
                dimensionCheckbox.id = `dimension-${index}`;
                dimensionCheckbox.value = dimension.key; // 使用维度的key作为值
                dimensionCheckbox.dataset.dimension = dimension.key; // 添加数据属性
                dimensionCheckbox.dataset.dimensionName = dimension.name; // 添加维度名称数据属性
                dimensionCheckbox.checked = true;

                const dimensionLabel = document.createElement('label');
                dimensionLabel.className = 'form-check-label';
                dimensionLabel.htmlFor = `dimension-${index}`;
                dimensionLabel.textContent = dimension.name; // 显示维度的中文名称

                dimensionItem.appendChild(dimensionCheckbox);
                dimensionItem.appendChild(dimensionLabel);
                dimensionItemsContainer.appendChild(dimensionItem);
            });

            // 将维度列表添加到对话框中
            dimensionList.appendChild(dimensionItemsContainer);

            // 添加全选复选框的功能
            selectAllCheckbox.addEventListener('change', function() {
                const dimensionCheckboxes = dimensionList.querySelectorAll('.dimension-checkbox');
                dimensionCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            });

            // 添加维度复选框的联动功能
            const dimensionCheckboxes = dimensionList.querySelectorAll('.dimension-checkbox');
            dimensionCheckboxes.forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    // 检查是否所有维度都被选中
                    const allChecked = Array.from(dimensionCheckboxes).every(function(cb) {
                        return cb.checked;
                    });

                    // 更新全选复选框状态
                    selectAllCheckbox.checked = allChecked;
                });
            });

            // 确保确认按钮能正确处理维度选择
            const confirmButton = modal.querySelector('.modal-footer .btn-primary');
            if (confirmButton) {
                // 保存原始的点击处理器
                const originalClickHandler = confirmButton.onclick;

                // 添加新的点击处理器
                confirmButton.onclick = function(event) {
                    // 获取所有选中的维度
                    const selectedDimensions = [];
                    dimensionCheckboxes.forEach(function(checkbox) {
                        if (checkbox.checked) {
                            // 使用维度的key作为值
                            selectedDimensions.push(checkbox.dataset.dimension || checkbox.value);
                        }
                    });

                    // 确保至少选择了一个维度
                    if (selectedDimensions.length === 0) {
                        alert('请至少选择一个维度进行分析');
                        event.preventDefault();
                        event.stopPropagation();
                        return false;
                    }

                    // 记录选中的维度到控制台，方便调试
                    originalConsoleLog('选中的维度:', selectedDimensions);

                    // 创建隐藏的表单字段，用于提交选中的维度
                    const form = confirmButton.closest('form');
                    if (form) {
                        // 清除之前可能存在的维度字段
                        const existingFields = form.querySelectorAll('input[name="dimensions"]');
                        existingFields.forEach(field => field.remove());

                        // 为每个选中的维度创建一个隐藏字段
                        selectedDimensions.forEach(dimension => {
                            const hiddenField = document.createElement('input');
                            hiddenField.type = 'hidden';
                            hiddenField.name = 'dimensions';
                            hiddenField.value = dimension;
                            form.appendChild(hiddenField);
                        });

                        originalConsoleLog('已添加维度字段到表单');
                    }

                    // 调用原始处理器
                    if (originalClickHandler) {
                        return originalClickHandler.call(this, event);
                    }
                };
            }

            originalConsoleLog('维度复选框修复完成，已添加13个维度');
        } catch (error) {
            console.error('修复维度复选框时出错:', error);
        }
    }

    // 立即执行修复
    try {
        originalConsoleLog('立即执行维度选择修复脚本');
        fixDimensionSelection();
    } catch (error) {
        console.error('执行维度选择修复脚本时出错:', error);
    }

    // 在页面加载完成后再次执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixDimensionSelection);
    } else {
        // 延迟执行一次，防止某些异步加载的情况
        setTimeout(fixDimensionSelection, 500);
    }

    // 暴露修复函数到全局，方便手动调用
    window.fixDimensionSelection = fixDimensionSelection;

    console.log('维度选择修复脚本(增强版 v1.0.2)初始化完成，请刷新页面或重新打开对话框');
})();