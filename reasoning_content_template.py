"""
九猫系统 - 推理内容模板生成器
用于生成标准化的推理内容格式，确保整本书分析和章节分析使用相同的推理格式

此模块提供了生成推理内容的函数，可以在分析过程中使用。

导出的函数:
- generate_reasoning_content_template: 生成标准化的推理内容模板
- format_reasoning_content: 格式化API返回的原始推理内容
- get_dimension_name: 获取维度的中文名称
- get_dimension_analysis_approach: 根据维度获取分析思路提示
"""

import logging
import re
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

def generate_reasoning_content_template(
    dimension: str,
    title: str,
    is_chapter: bool = False,
    chapter_title: Optional[str] = None
) -> str:
    """
    生成标准化的推理内容模板，用于提示API生成结构化的推理过程

    Args:
        dimension: 分析维度
        title: 小说标题
        is_chapter: 是否为章节分析
        chapter_title: 章节标题（仅在is_chapter=True时使用）

    Returns:
        结构化的推理内容模板
    """
    # 分析对象描述（整本书或章节）
    analysis_object = f"章节《{chapter_title}》" if is_chapter else f"小说《{title}》"

    # 根据维度生成不同的分析思路提示
    analysis_approach = get_dimension_analysis_approach(dimension)

    # 构建完整的模板
    # 为章纲分析添加特殊说明
    special_instruction = ""
    if dimension == "chapter_outline":
        special_instruction = """
特别说明：对于章纲分析，必须以原文的风格和语气进行叙述，而非分析报告形式。内容重现部分必须极其详尽，不设字数限制，越详细越好。必须完全模仿原文的风格和语气，让读者仿佛亲历其境。"""

    template = f"""请分析{analysis_object}的{get_dimension_name(dimension)}，并按照以下结构提供非常详细的推理过程。分析内容不限字数，越详细越好：{special_instruction}

## 分析思路说明：
{analysis_approach}

## 详细{get_dimension_name(dimension)}分析：

### 一、[第一个关键特点]
[非常详细地分析第一个特点，包括多个具体例子和深入分析理由。引用原文中的具体段落或句子作为例证，并进行深入解读。不要担心内容过长，应尽可能详尽地分析。]

### 二、[第二个关键特点]
[非常详细地分析第二个特点，包括多个具体例子和深入分析理由。引用原文中的具体段落或句子作为例证，并进行深入解读。不要担心内容过长，应尽可能详尽地分析。]

### 三、[第三个关键特点]
[非常详细地分析第三个特点，包括多个具体例子和深入分析理由。引用原文中的具体段落或句子作为例证，并进行深入解读。不要担心内容过长，应尽可能详尽地分析。]

### 四、[第四个关键特点]
[非常详细地分析第四个特点，包括多个具体例子和深入分析理由。引用原文中的具体段落或句子作为例证，并进行深入解读。不要担心内容过长，应尽可能详尽地分析。]

### 五、[第五个关键特点]
[非常详细地分析第五个特点，包括多个具体例子和深入分析理由。引用原文中的具体段落或句子作为例证，并进行深入解读。不要担心内容过长，应尽可能详尽地分析。]

### 六、[第六个关键特点]
[非常详细地分析第六个特点，包括多个具体例子和深入分析理由。引用原文中的具体段落或句子作为例证，并进行深入解读。不要担心内容过长，应尽可能详尽地分析。]

### 七、[第七个关键特点]
[非常详细地分析第七个特点，包括多个具体例子和深入分析理由。引用原文中的具体段落或句子作为例证，并进行深入解读。不要担心内容过长，应尽可能详尽地分析。]

### 八、[可根据需要添加更多小节]
[继续添加更多分析点，不要担心内容过长。系统已配置足够的处理能力，可以处理非常详细的分析结果。]

## 总结与评价
[全面总结以上分析发现，并深入评价这些特点如何共同作用，为作品带来什么效果。提供整体评价和具体建议。]

请确保分析思路部分使用加粗和编号列出分析方法，详细分析部分使用三级标题分小节进行具体分析，每个小节都应包含多个具体文本例子和非常深入的分析。分析内容不限字数，越详细越好，可以尽可能展开分析。不要担心分析内容过长，系统已配置足够的处理能力。
"""
    return template

def get_dimension_name(dimension: str) -> str:
    """获取维度的中文名称"""
    dimension_names = {
        "language_style": "语言风格",
        "rhythm_pacing": "节奏节拍",
        "structure": "结构分析",
        "sentence_variation": "句式变化",
        "paragraph_length": "段落长度",
        "perspective_shifts": "视角转换",
        "paragraph_flow": "段落流畅度",
        "novel_characteristics": "小说特点",
        "world_building": "世界构建",
        "character_relationships": "角色关系",
        "opening_effectiveness": "开篇效果",
        "climax_pacing": "高潮节奏",
        "theme_analysis": "主题分析",
        "outline_analysis": "大纲分析",
        "chapter_outline": "章纲分析",
        "popular_tropes": "热梗统计"
    }
    return dimension_names.get(dimension, dimension)

def get_dimension_analysis_approach(dimension: str) -> str:
    """
    根据维度获取分析思路提示

    Args:
        dimension: 分析维度

    Returns:
        分析思路提示文本
    """
    approaches = {
        "chapter_outline": """
1. **内容重现与叙事梳理**：全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。内容不设字数限制，越详细越好。必须包含以下要素：
   - 以原文的风格描述每个场景的环境、氛围和背景，包括光线、声音、气味等感官细节
   - 生动描绘场景中的物品、布置和空间关系，提供具体的视觉画面
   - 完整记录主要人物的对话内容，尽可能使用原文中的重要对话，保留对话的语气和特点
   - 以原文的风格详细描述人物的动作、表情和肢体语言，包括微小的表情变化和身体动作
   - 深入描述人物的心理活动、情感变化和内心冲突，展现人物的思想过程
2. **结构功能与定位分析**：全面分析章节的内部结构和功能定位，评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性
3. **关键事件与转折点**：详细分析本章节中的关键事件和转折点，识别并详述章节中的转折点、冲突点和高潮点
4. **人物表现与发展轨迹**：详细分析本章节中主要人物的表现、行动和发展，描述每个重要人物在本章中的言行和思想变化
5. **章节关联与叙事连贯性**：深入分析本章节与前后章节的关系和叙事连贯性，评估章节过渡的自然度和技巧多样性
6. **伏笔设置与悬念管理**：分析本章节中的伏笔设置和悬念管理，指出章节中埋下的伏笔和暗示，以及它们可能的未来发展方向
7. **主题情感与氛围营造**：评估章节的主题明确性和情感基调，分析本章节的核心主题和情感色彩
8. **创新特色与读者体验**：分析本章节的特色元素和创新点，识别章节中独特的写作技巧，评估如何吸引和维持读者兴趣
""",
        "outline_analysis": """
1. **整体故事梳理与内容概述**：全面详细地重现整部作品的完整内容，以小说叙述的方式进行，必须极其详尽地描述整个故事中发生的一切，包括关键场景、重要事件、人物对话和心理活动
2. **章节结构与内容分布**：详细分析作品的章节结构和内容分布，评估章节之间的连贯性和平衡性，分析章节如何组织成更大的结构单元
3. **整体架构与结构设计**：全面分析作品的整体结构与架构设计，评估三幕结构/五幕结构/英雄旅程等经典结构模式的应用情况
4. **情节线设计与管理**：深入分析作品的情节线设计与管理，识别并详述主线与支线的数量、内容和层级关系，评估情节线交织技巧和节奏控制
5. **叙事节奏与高潮设计**：分析作品的叙事节奏与高潮设计，评估节奏变化的规律性和目的性，量化高潮分布密度和强度梯度
6. **人物系统与角色设计**：全面分析作品的人物系统和角色设计，识别主要人物、次要人物和配角的功能和特点，评估人物形象的丰满度和一致性
7. **人物弧线与成长设计**：评估作品中人物弧线与成长设计，分析主要人物的起点、转折点和终点设置，量化人物变化的幅度和节奏
8. **主题展开与深化策略**：深入分析作品的主题展开与深化策略，识别核心主题和辅助主题的层级关系，量化主题元素的分布密度和强调程度
9. **世界观构建与背景设定**：评估作品的世界观构建与背景设定，分析世界观元素的丰富度、一致性和原创性，量化世界观信息的揭示节奏和密度
10. **创新特色与读者体验**：分析作品的创新特色和读者体验设计，识别作品在题材、结构、人物、世界观等方面的创新点和突破性特征
""",
        "popular_tropes": """
1. **热梗识别统计**：识别并统计文本中出现的各类热梗元素，记录其出现位置和频率
2. **热梗分类整理**：将识别到的热梗按类型进行分类整理，如网络梗、影视梗、游戏梗等
3. **时效性特征记录**：记录各热梗的时效性特征，区分经典长寿梗、阶段性流行梗和新兴热梗
4. **使用场景分析**：统计热梗在不同场景和情节类型中的使用情况，记录与人物的关联
5. **原创性元素标记**：识别文本中可能的原创梗或对已有热梗的创新使用，客观记录不评价
""",
        "language_style": """
1. **词汇选择分析**：考察作者使用的词汇特点，如正式/口语、具体/抽象、简单/复杂等
2. **句式结构解析**：分析句子长短变化、复杂程度和语法特点
3. **修辞手法识别**：识别比喻、拟人、排比等修辞手法的使用及其效果
4. **语气语调评估**：分析文本的整体语气，如幽默、严肃、讽刺、温暖等
5. **风格一致性考察**：评估风格在全文中的一致性或有意的变化
""",
        "sentence_variation": """
1. **长短句交替分析**：通过统计不同长度句式的分布比例，观察叙事节奏的急缓变化
2. **句式功能分类**：将疑问句/感叹句/陈述句/独白进行标注，分析情感表达的层次递进
3. **修辞手法解码**：识别比喻/排比/拟声等修辞在句式中的特殊运用
4. **视角转换追踪**：标记叙述视角在客观描写与主观心理活动之间的切换轨迹
5. **标点符号解析**：研究破折号/省略号/引号等特殊符号对阅读停顿的调控作用
""",
        "rhythm_pacing": """
1. **情节推进速度测量**：分析故事情节的快慢变化和转折点，绘制节奏曲线
2. **句段长度变化统计**：考察句子和段落长度如何影响阅读节奏，识别模式
3. **场景切换频率分析**：量化场景转换的频率和方式，评估其对节奏的影响
4. **紧张与舒缓交替研究**：识别文本中紧张和舒缓部分的交替模式和比例
5. **高潮铺垫技巧解析**：分析作者如何通过节奏变化铺垫情节高潮，提取方法
""",
        "structure": """
1. **整体架构分析**：考察作品的总体结构设计和章节安排
2. **情节线索梳理**：识别主要情节线索和次要情节线索的编织方式
3. **叙事视角考察**：分析叙事视角的选择和转换对结构的影响
4. **时间线构建研究**：评估时间线的处理方式（线性/非线性/多线并行）
5. **结构平衡性评估**：考察各部分篇幅和重要性的分配是否平衡
""",
        "paragraph_length": """
1. **段落长度统计分析**：测量全文段落长度分布，识别规律和变化
2. **段落功能分类**：将段落按描写/对话/动作/心理活动等功能分类
3. **段落转换技巧研究**：分析段落之间的过渡方式和连接技巧
4. **段落内部结构解析**：考察段落的内部组织和句子安排
5. **段落长度与内容关系评估**：分析段落长度如何服务于内容表达
""",
        "perspective_shifts": """
1. **视角类型识别**：区分第一人称/第三人称/全知视角等不同叙事视角
2. **视角转换点标记**：定位文本中视角转换的具体位置和转换方式
3. **多视角交织分析**：研究多个视角如何交织呈现同一事件或情节
4. **视角限制效果评估**：分析视角限制如何影响信息披露和悬念营造
5. **视角与情感关联研究**：考察视角选择如何影响读者情感体验
""",
        "paragraph_flow": """
1. **段落连接方式分析**：研究段落之间的过渡词、呼应和连接技巧
2. **信息流动追踪**：考察信息如何在段落间流动和递进
3. **节奏变化测量**：分析段落长短交替如何影响阅读节奏
4. **逻辑结构评估**：考察段落排列的逻辑性和连贯性
5. **主题发展研究**：分析主题如何通过段落流动逐步展开和深化
""",
        "novel_characteristics": """
1. **题材类型识别**：确定小说所属的文学类型和题材特点
2. **创作风格分析**：研究作者的独特创作风格和表现手法
3. **主题思想提取**：识别小说传达的核心思想和价值观
4. **艺术手法考察**：分析小说使用的主要艺术手法和技巧
5. **创新点评估**：考察小说在内容和形式上的创新之处
""",
        "world_building": """
1. **世界观构建分析**：研究作者如何建立小说的基本世界观和规则
2. **环境描写技巧**：考察环境和场景描写的方法和特点
3. **文化背景塑造**：分析小说中的文化元素和社会背景
4. **历史/科技设定**：评估历史或科技设定的合理性和一致性
5. **世界细节丰富度**：考察世界构建的细节丰富程度和真实感
""",
        "character_relationships": """
1. **关系网络绘制**：构建角色之间的关系网络和互动模式
2. **关系发展追踪**：分析主要角色关系的变化和发展轨迹
3. **互动方式研究**：考察角色之间的互动方式和沟通模式
4. **权力结构分析**：评估角色之间的权力关系和影响力
5. **情感连接考察**：研究角色之间的情感连接和心理互动
""",
        "opening_effectiveness": """
1. **开篇吸引力分析**：评估开篇对读者注意力的吸引程度
2. **信息铺设技巧**：研究开篇如何铺设基本信息和背景
3. **人物引入方式**：分析主要人物的引入顺序和方式
4. **悬念营造手法**：考察开篇如何设置悬念和引发好奇
5. **基调确立效果**：评估开篇如何确立全书的基调和氛围
""",
        "climax_pacing": """
1. **高潮铺垫分析**：研究作者如何通过前文铺垫高潮情节
2. **节奏加速技巧**：考察高潮前的节奏加速手法和效果
3. **情感强度测量**：分析高潮部分的情感强度和表现方式
4. **冲突升级模式**：评估冲突如何逐步升级达到高潮
5. **高潮后处理**：研究高潮后的情节处理和节奏调整
""",
        "theme_analysis": """
1. **核心主题提取**：识别作品传达的核心思想和价值观
2. **主题表现手法**：分析主题通过何种方式在文本中体现
3. **象征意象研究**：考察作品中的象征和意象如何服务于主题
4. **主题深度评估**：评估主题的深度和复杂性
5. **主题一致性考察**：研究主题在全文中的一致性和发展
"""
    }

    # 如果没有特定维度的分析思路，返回通用分析思路
    default_approach = """
1. **整体特点分析**：把握文本在该维度上的总体表现和特点
2. **具体表现研究**：找出文本中该维度的具体表现和例子
3. **变化规律识别**：分析该维度在文本中的变化规律和模式
4. **效果评估测量**：评估该维度的处理对读者体验的影响
5. **创新点提取**：识别作者在该维度上的创新或独特之处
"""

    return approaches.get(dimension, default_approach)

def format_reasoning_content(raw_content: str) -> str:
    """
    格式化API返回的原始推理内容，确保符合标准格式

    Args:
        raw_content: API返回的原始推理内容

    Returns:
        格式化后的推理内容
    """
    # 如果内容为空，返回提示信息
    if not raw_content or raw_content.strip() == "":
        return "## 分析思路说明：\n[未提供分析内容]\n\n## 详细分析：\n[未提供分析内容]"

    # 如果内容已经包含标准格式的标题，可能已经是格式化的内容
    if "## 分析思路说明：" in raw_content or "## 分析思路说明:" in raw_content:
        return raw_content

    # 检查是否包含其他形式的分析思路标题
    if "## 分析思路" in raw_content or "# 分析思路" in raw_content:
        # 替换为标准格式
        raw_content = raw_content.replace("## 分析思路", "## 分析思路说明：")
        raw_content = raw_content.replace("# 分析思路", "## 分析思路说明：")

        # 检查是否包含详细分析部分
        if "## 详细分析" not in raw_content and "# 详细分析" not in raw_content:
            # 尝试找到分析思路后的第一个标题作为详细分析的开始
            parts = raw_content.split("## 分析思路说明：", 1)
            if len(parts) > 1:
                after_approach = parts[1]
                next_heading_match = re.search(r'(?:\n|^)(?:#+\s+)([^\n]+)', after_approach)

                if next_heading_match:
                    heading_pos = after_approach.find(next_heading_match.group(0))
                    approach_content = after_approach[:heading_pos].strip()
                    analysis_content = after_approach[heading_pos:].strip()

                    return f"""## 分析思路说明：
{approach_content}

## 详细分析：
{analysis_content}"""
                else:
                    # 如果找不到下一个标题，尝试按段落分割
                    paragraphs = after_approach.split("\n\n")
                    if len(paragraphs) > 2:  # 至少有几个段落才分割
                        approach_content = paragraphs[0]
                        analysis_content = "\n\n".join(paragraphs[1:])

                        return f"""## 分析思路说明：
{approach_content}

## 详细分析：
{analysis_content}"""

    # 尝试从原始内容中提取结构化信息
    # 首先检查是否包含编号列表形式的分析思路
    numbered_list_match = re.search(r'(?:^|\n)(?:1\.\s+[^\n]+(?:\n\d+\.\s+[^\n]+)+)', raw_content)
    if numbered_list_match:
        approach_content = numbered_list_match.group(0)
        # 在编号列表后查找可能的详细分析内容
        after_list = raw_content[numbered_list_match.end():].strip()

        # 如果编号列表后有足够的内容，将其视为详细分析
        if len(after_list) > 100:  # 假设详细分析至少有一定长度
            return f"""## 分析思路说明：
{approach_content}

## 详细分析：
{after_list}"""

    # 如果无法识别结构，尝试智能分割
    # 假设前30%是分析思路，后70%是详细分析
    content_length = len(raw_content)
    split_point = int(content_length * 0.3)

    # 找到分割点附近的段落边界
    split_paragraph = raw_content[:split_point].rfind("\n\n")
    if split_paragraph > 0:
        approach_content = raw_content[:split_paragraph].strip()
        analysis_content = raw_content[split_paragraph:].strip()

        # 确保分析思路不太长
        if len(approach_content) > 500:
            approach_content = approach_content[:500] + "..."

        return f"""## 分析思路说明：
{approach_content}

## 详细分析：
{analysis_content}"""

    # 如果所有尝试都失败，使用原始内容并添加标准标题
    return f"""## 分析思路说明：
[从原始内容中提取的分析思路]

## 详细分析：
{raw_content}"""

# 示例用法
if __name__ == "__main__":
    # 生成整本书分析的推理内容模板
    book_template = generate_reasoning_content_template(
        dimension="sentence_variation",
        title="测试小说"
    )
    print("整本书分析模板:")
    print(book_template)
    print("\n" + "-"*50 + "\n")

    # 生成章节分析的推理内容模板
    chapter_template = generate_reasoning_content_template(
        dimension="sentence_variation",
        title="测试小说",
        is_chapter=True,
        chapter_title="第一章 开始"
    )
    print("章节分析模板:")
    print(chapter_template)
