/**
 * 九猫系统 - 知识库紧凑显示样式
 * 优化知识库内容展示，使页面更加干净整洁
 */

/* 基本容器样式优化 */
.template-content.markdown-body {
    font-size: 0.95rem;
    line-height: 1.5;
}

/* 标题样式优化 */
.markdown-body h1 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.markdown-body h2 {
    font-size: 1.5rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid var(--border-color);
}

.markdown-body h3 {
    font-size: 1.25rem;
    margin-top: 1.25rem;
    margin-bottom: 0.5rem;
}

.markdown-body h4 {
    font-size: 1.1rem;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

/* 段落和列表样式优化 */
.markdown-body p {
    margin-bottom: 0.75rem;
}

.markdown-body ul,
.markdown-body ol {
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
}

.markdown-body li {
    margin-bottom: 0.25rem;
}

/* 折叠内容样式 */
.collapsible-section {
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    overflow: hidden;
}

.collapsible-header {
    padding: 0.5rem 0.75rem;
    background-color: rgba(0, 0, 0, 0.02);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
}

.collapsible-header:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.collapsible-header h3,
.collapsible-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
}

.collapsible-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
    padding: 0 0.75rem;
}

.collapsible-content.expanded {
    max-height: 5000px; /* 足够大的值以容纳内容 */
    padding: 0.75rem;
    transition: max-height 0.5s ease-in, padding 0.3s ease-in;
}

/* 思考过程容器的特殊处理 */
.thinking-process-container {
    transition: all 0.3s ease;
}

/* 分析结果和推理过程容器 */
.analysis-result-container,
.reasoning-process-container,
.thinking-process-container {
    margin-bottom: 1rem;
}

.analysis-result-container h3,
.reasoning-process-container h3,
.thinking-process-container h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 思考过程容器 */
.thinking-process {
    font-size: 0.9rem;
    line-height: 1.4;
}

.thinking-process h1 {
    font-size: 1.4rem;
}

.thinking-process h2 {
    font-size: 1.2rem;
}

.thinking-process h3 {
    font-size: 1.1rem;
}

.thinking-process h4,
.thinking-process h5,
.thinking-process h6 {
    font-size: 1rem;
}

.thinking-process p {
    margin-bottom: 0.5rem;
}

.thinking-process ul,
.thinking-process ol {
    padding-left: 1.2rem;
    margin-bottom: 0.5rem;
}

.thinking-process-toggle {
    transition: all 0.2s ease;
}

/* 标签页内容区域 */
.tab-content {
    padding-top: 1rem;
}

/* 维度列表样式优化 */
.dimension-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

/* 章节列表样式优化 */
.chapter-list {
    max-height: 500px;
    overflow-y: auto;
}

.chapter-item {
    padding: 0.4rem 0.75rem;
    font-size: 0.85rem;
}

/* 代码块和预格式化文本 */
.markdown-body pre,
.markdown-body code {
    font-size: 0.85rem;
    padding: 0.5rem;
    margin-bottom: 0.75rem;
    border-radius: 0.25rem;
    background-color: rgba(0, 0, 0, 0.03);
}

/* 表格样式优化 */
.markdown-body table {
    font-size: 0.85rem;
    margin-bottom: 1rem;
    width: 100%;
    border-collapse: collapse;
}

.markdown-body table th,
.markdown-body table td {
    padding: 0.4rem 0.5rem;
    border: 1px solid var(--border-color);
}

.markdown-body table th {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 引用块样式 */
.markdown-body blockquote {
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-color);
    background-color: rgba(0, 0, 0, 0.02);
    font-size: 0.9rem;
}

/* 水平分割线 */
.markdown-body hr {
    margin: 1rem 0;
    border: 0;
    border-top: 1px solid var(--border-color);
}

/* 导出按钮样式优化 */
.export-template-btn,
.export-chapter-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
}

/* 内容预览区域 */
.content-preview {
    max-height: 150px;
    overflow: hidden;
    position: relative;
}

.content-preview::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background: linear-gradient(transparent, var(--card-bg));
}

/* 响应式调整 */
@media (max-width: 768px) {
    .markdown-body h1 {
        font-size: 1.5rem;
    }

    .markdown-body h2 {
        font-size: 1.3rem;
    }

    .markdown-body h3 {
        font-size: 1.1rem;
    }

    .markdown-body h4 {
        font-size: 1rem;
    }
}
