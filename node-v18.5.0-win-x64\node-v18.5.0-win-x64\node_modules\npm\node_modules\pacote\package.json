{"name": "pacote", "version": "13.6.0", "description": "JavaScript package downloader", "author": "GitHub Inc.", "bin": {"pacote": "lib/bin.js"}, "license": "ISC", "main": "lib/index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "tap": {"timeout": 300}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "hosted-git-info": "^5.0.0", "mutate-fs": "^2.1.1", "nock": "^13.2.4", "npm-registry-mock": "^1.3.2", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "keywords": ["packages", "npm", "git"], "dependencies": {"@npmcli/git": "^3.0.0", "@npmcli/installed-package-contents": "^1.0.7", "@npmcli/promise-spawn": "^3.0.0", "@npmcli/run-script": "^3.0.1", "cacache": "^16.0.0", "chownr": "^2.0.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass": "^3.1.6", "mkdirp": "^1.0.4", "npm-package-arg": "^9.0.0", "npm-packlist": "^5.1.0", "npm-pick-manifest": "^7.0.0", "npm-registry-fetch": "^13.0.1", "proc-log": "^2.0.0", "promise-retry": "^2.0.1", "read-package-json": "^5.0.0", "read-package-json-fast": "^2.0.3", "rimraf": "^3.0.2", "ssri": "^9.0.0", "tar": "^6.1.11"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/npm/pacote.git"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0", "windowsCI": false}}