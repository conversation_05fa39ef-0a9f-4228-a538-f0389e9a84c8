"""
九猫系统数据库连接监控模块
监控数据库连接池状态，自动处理连接问题
"""
import logging
import time
import threading
import os
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# 全局变量，用于跟踪数据库连接状态
_db_stats = {
    "total_connections": 0,
    "active_connections": 0,
    "connection_errors": 0,
    "last_error_time": 0,
    "last_error_message": "",
    "last_reset_time": 0,
    "consecutive_errors": 0,
    "health_check_count": 0,
    "last_health_check_time": 0,
    "pool_size": 0,
    "max_overflow": 0
}

# 连接监控锁
_db_stats_lock = threading.Lock()

# 连接池健康状态
_db_pool_healthy = True

# 监控线程
_monitor_thread = None
_stop_monitor = False

def start_db_monitor():
    """启动数据库连接监控"""
    global _monitor_thread, _stop_monitor
    
    # 如果已经有监控线程在运行，先停止它
    if _monitor_thread and _monitor_thread.is_alive():
        stop_db_monitor()
    
    # 重置停止标志
    _stop_monitor = False
    
    # 创建并启动监控线程
    _monitor_thread = threading.Thread(target=_db_monitor_thread, daemon=True)
    _monitor_thread.start()
    
    logger.info("数据库连接监控已启动")

def stop_db_monitor():
    """停止数据库连接监控"""
    global _stop_monitor
    
    if _monitor_thread and _monitor_thread.is_alive():
        _stop_monitor = True
        _monitor_thread.join(timeout=5)
        logger.info("数据库连接监控已停止")

def _db_monitor_thread():
    """数据库连接监控线程"""
    logger.info("数据库连接监控线程已启动")
    
    check_interval = 30  # 默认检查间隔（秒）
    
    while not _stop_monitor:
        try:
            # 获取环境变量中的检查间隔
            try:
                check_interval = int(os.environ.get('DB_MONITOR_INTERVAL', '30'))
            except (ValueError, TypeError):
                check_interval = 30
            
            # 检查数据库连接池状态
            check_db_pool_health()
            
            # 等待下一次检查
            for _ in range(check_interval):
                if _stop_monitor:
                    break
                time.sleep(1)
        except Exception as e:
            logger.error(f"数据库连接监控线程出错: {str(e)}")
            # 出错后等待一段时间再继续
            time.sleep(10)

def check_db_pool_health() -> bool:
    """
    检查数据库连接池健康状态
    
    Returns:
        布尔值，表示连接池是否健康
    """
    global _db_pool_healthy
    
    try:
        # 导入数据库连接模块
        from src.db.connection import engine, recreate_engine
        
        # 更新统计信息
        with _db_stats_lock:
            _db_stats["health_check_count"] += 1
            _db_stats["last_health_check_time"] = time.time()
            
            try:
                _db_stats["pool_size"] = engine.pool.size()
                _db_stats["active_connections"] = engine.pool.checkedout()
                _db_stats["max_overflow"] = engine.pool._max_overflow
            except Exception as e:
                logger.error(f"获取连接池状态时出错: {str(e)}")
        
        # 尝试执行简单查询
        try:
            with engine.connect() as conn:
                from sqlalchemy import text
                conn.execute(text("SELECT 1"))
            
            # 查询成功，重置错误计数
            with _db_stats_lock:
                _db_stats["consecutive_errors"] = 0
            
            # 标记连接池为健康
            _db_pool_healthy = True
            
            # 检查连接池使用率
            pool_size = _db_stats["pool_size"]
            active_connections = _db_stats["active_connections"]
            
            if pool_size > 0 and active_connections / pool_size > 0.8:
                logger.warning(f"连接池使用率较高: {active_connections}/{pool_size} ({active_connections/pool_size*100:.1f}%)")
                
                # 如果使用率超过90%，主动重新创建连接池
                if active_connections / pool_size > 0.9:
                    logger.warning("连接池使用率超过90%，主动重新创建连接池")
                    recreate_engine()
                    logger.info("连接池已重新创建")
            
            return True
        except Exception as e:
            # 查询失败，增加错误计数
            with _db_stats_lock:
                _db_stats["connection_errors"] += 1
                _db_stats["last_error_time"] = time.time()
                _db_stats["last_error_message"] = str(e)
                _db_stats["consecutive_errors"] += 1
            
            logger.error(f"数据库连接检查失败: {str(e)}")
            
            # 如果连续错误次数超过阈值，重新创建连接池
            if _db_stats["consecutive_errors"] > 3:
                logger.warning("连续多次连接错误，尝试重新创建连接池")
                recreate_engine()
                logger.info("连接池已重新创建")
                
                # 标记连接池为不健康
                _db_pool_healthy = False
            
            return False
    except Exception as e:
        logger.error(f"检查数据库连接池健康状态时出错: {str(e)}")
        return False

def get_db_stats() -> Dict[str, Any]:
    """
    获取数据库连接统计信息
    
    Returns:
        字典，包含数据库连接统计信息
    """
    with _db_stats_lock:
        stats = dict(_db_stats)
        stats["pool_healthy"] = _db_pool_healthy
        
        # 计算使用率
        if stats["pool_size"] > 0:
            stats["usage_ratio"] = stats["active_connections"] / stats["pool_size"]
            stats["usage_percent"] = f"{stats['usage_ratio']*100:.1f}%"
        else:
            stats["usage_ratio"] = 0
            stats["usage_percent"] = "0%"
        
        return stats

def reset_db_stats():
    """重置数据库连接统计信息"""
    with _db_stats_lock:
        _db_stats["total_connections"] = 0
        _db_stats["active_connections"] = 0
        _db_stats["connection_errors"] = 0
        _db_stats["consecutive_errors"] = 0
        _db_stats["health_check_count"] = 0
        _db_stats["last_reset_time"] = time.time()
    
    logger.info("数据库连接统计信息已重置")

# 在模块加载时自动启动监控
try:
    # 检查是否启用了数据库连接优化
    if os.environ.get('DB_OPTIMIZE_CONNECTIONS', 'False').lower() in ('true', '1', 't'):
        start_db_monitor()
        logger.info("数据库连接监控已自动启动")
except Exception as e:
    logger.error(f"启动数据库连接监控时出错: {str(e)}")
