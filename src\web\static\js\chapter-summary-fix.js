/**
 * 九猫 - 章节汇总分析页面修复脚本
 * 用于修复章节汇总分析页面的维度选择问题
 * 版本: 1.0.0
 */

(function() {
    console.log('章节汇总分析页面修复脚本已加载 - 版本1.0.0');

    // 正确的维度列表（与后端一致）
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "chapter_outline", name: "章节大纲" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" },
        { key: "outline_analysis", name: "大纲分析" },
        { key: "popular_tropes", name: "热梗统计" }
    ];

    // 修复汇总章节分析模态框
    function fixAggregateChaptersModal() {
        console.log('开始修复汇总章节分析模态框');

        // 查找模态框
        const modal = document.getElementById('aggregateChaptersModal');
        if (!modal) {
            console.warn('未找到汇总章节分析模态框，可能不在相关页面');
            return;
        }

        // 查找维度列表容器
        const dimensionsList = modal.querySelector('#aggregate-dimensions-list');
        if (!dimensionsList) {
            console.warn('未找到维度列表容器');
            return;
        }

        // 清空现有内容
        dimensionsList.innerHTML = '';

        // 添加维度选项
        DIMENSIONS.forEach(dimension => {
            const checkboxDiv = document.createElement('div');
            checkboxDiv.className = 'form-check mb-1';
            checkboxDiv.innerHTML = `
                <input class="form-check-input aggregate-dimension-checkbox" type="checkbox" id="aggregate-${dimension.key}"
                       value="${dimension.key}" checked>
                <label class="form-check-label" for="aggregate-${dimension.key}">
                    ${dimension.name}
                </label>
            `;
            dimensionsList.appendChild(checkboxDiv);
        });

        console.log(`已添加 ${DIMENSIONS.length} 个维度选项到汇总章节分析模态框`);

        // 修复全选/取消全选功能
        const selectAllCheckbox = modal.querySelector('#aggregate-all-dimensions');
        if (selectAllCheckbox) {
            // 移除现有事件监听器
            const newSelectAllCheckbox = selectAllCheckbox.cloneNode(true);
            selectAllCheckbox.parentNode.replaceChild(newSelectAllCheckbox, selectAllCheckbox);

            // 添加新的事件监听器
            newSelectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                modal.querySelectorAll('.aggregate-dimension-checkbox').forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
            });

            console.log('已修复全选/取消全选功能');
        }
    }

    // 修复章节汇总分析页面的维度选择器
    function fixChapterSummaryDimensionFilter() {
        console.log('开始修复章节汇总分析页面的维度选择器');

        // 查找维度选择器
        const dimensionFilter = document.getElementById('dimensionFilter');
        if (!dimensionFilter) {
            console.warn('未找到维度选择器，可能不在章节汇总分析页面');
            return;
        }

        // 获取当前选中的值
        const selectedValue = dimensionFilter.value;

        // 清空现有选项
        dimensionFilter.innerHTML = '';

        // 添加"所有维度"选项
        const allOption = document.createElement('option');
        allOption.value = 'all';
        allOption.textContent = '所有维度';
        dimensionFilter.appendChild(allOption);

        // 添加维度选项
        DIMENSIONS.forEach(dimension => {
            const option = document.createElement('option');
            option.value = dimension.key;
            option.textContent = dimension.name;
            dimensionFilter.appendChild(option);
        });

        // 恢复选中的值
        dimensionFilter.value = selectedValue;

        console.log(`已添加 ${DIMENSIONS.length} 个维度选项到维度选择器`);
    }

    // 在页面加载完成后执行修复
    function init() {
        console.log('初始化章节汇总分析页面修复');

        // 检查是否在章节汇总分析页面
        const isChapterSummaryPage = window.location.pathname.includes('/novel/') &&
                                    window.location.pathname.includes('/chapters/summary');

        // 检查是否在小说详情页面
        const isNovelDetailPage = window.location.pathname.match(/\/novel\/\d+$/) !== null;

        if (isChapterSummaryPage) {
            console.log('检测到章节汇总分析页面，应用相关修复');
            // 修复维度选择器
            fixChapterSummaryDimensionFilter();
        }

        if (isNovelDetailPage) {
            console.log('检测到小说详情页面，设置汇总章节分析模态框监听器');
            // 监听汇总章节分析按钮点击事件
            const aggregateBtn = document.getElementById('aggregateChaptersBtn');
            if (aggregateBtn) {
                aggregateBtn.addEventListener('click', function() {
                    // 延迟执行，确保模态框已显示
                    setTimeout(fixAggregateChaptersModal, 100);
                });
                console.log('已设置汇总章节分析按钮点击事件监听器');
            }
        }

        // 监听模态框显示事件
        if (typeof $ !== 'undefined') {
            $(document).on('shown.bs.modal', '#aggregateChaptersModal', function() {
                console.log('检测到汇总章节分析模态框显示，应用修复');
                fixAggregateChaptersModal();
            });
            console.log('已设置模态框显示事件监听器');
        }
    }

    // 在页面加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 导出修复函数到全局，方便手动调用
    window.chapterSummaryFix = {
        fixAggregateChaptersModal: fixAggregateChaptersModal,
        fixChapterSummaryDimensionFilter: fixChapterSummaryDimensionFilter
    };

    console.log('章节汇总分析页面修复脚本初始化完成');
})();
