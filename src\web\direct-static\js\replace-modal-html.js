/**
 * 九猫 - 替换模态框HTML脚本
 * 直接替换模态框的HTML代码
 * 版本: 1.0.0
 */

(function() {
    console.log('[替换模态框HTML] 脚本已加载');

    // 维度列表 - 与config.py中的ANALYSIS_DIMENSIONS保持一致
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "chapter_outline", name: "章节大纲" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" }
    ];

    // 替换模态框HTML
    function replaceModalHTML() {
        console.log('[替换模态框HTML] 开始替换模态框HTML');

        // 获取当前页面的小说ID和章节ID
        let novelId = null;
        let chapterId = null;

        // 尝试从URL中获取小说ID和章节ID
        const urlMatch = window.location.pathname.match(/\/novel\/(\d+)\/chapter\/(\d+)/);
        if (urlMatch) {
            novelId = urlMatch[1];
            chapterId = urlMatch[2];
            console.log(`[替换模态框HTML] 从URL获取到小说ID: ${novelId}, 章节ID: ${chapterId}`);
        } else {
            // 尝试从页面中获取小说ID和章节ID
            const breadcrumbs = document.querySelectorAll('.breadcrumb-item a');
            breadcrumbs.forEach(link => {
                const href = link.getAttribute('href');
                if (href) {
                    const novelMatch = href.match(/\/novel\/(\d+)/);
                    if (novelMatch) {
                        novelId = novelMatch[1];
                    }
                }
            });

            // 尝试从当前页面标题获取章节ID
            const titleElement = document.querySelector('.card-header h2');
            if (titleElement) {
                const titleText = titleElement.textContent;
                const chapterMatch = titleText.match(/第(\d+)章/);
                if (chapterMatch) {
                    // 这里只是获取章节编号，不是章节ID
                    const chapterNumber = chapterMatch[1];
                    console.log(`[替换模态框HTML] 从标题获取到章节编号: ${chapterNumber}`);
                }
            }
        }

        if (!novelId || !chapterId) {
            console.error('[替换模态框HTML] 无法获取小说ID或章节ID');
            return;
        }

        // 创建新的模态框HTML
        const modalHTML = `
        <div class="modal fade" id="analyzeModal" tabindex="-1" aria-labelledby="analyzeModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="analyzeModalLabel">分析章节</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="analyzeForm" action="/api/novel/${novelId}/chapter/${chapterId}/analyze" method="POST">
                            <div class="mb-3">
                                <p>请选择要分析的维度：</p>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select-all-dimensions" checked>
                                    <label class="form-check-label" for="select-all-dimensions">
                                        <strong>全选</strong>
                                    </label>
                                </div>
                                <hr>
                                <div id="dimensions-container">
                                    ${DIMENSIONS.map((dimension, index) => `
                                    <div class="form-check mb-2">
                                        <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="${dimension.key}" id="dimension-${index}" checked>
                                        <label class="form-check-label" for="dimension-${index}">
                                            ${dimension.name}
                                        </label>
                                    </div>
                                    `).join('')}
                                </div>
                                <div class="alert alert-warning mt-3">
                                    <small>注意：分析过程可能需要较长时间，尤其是对于长篇小说。</small>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="modelSelect" class="form-label">选择分析模型</label>
                                <select class="form-select" id="modelSelect">
                                    <option value="deepseek-r1">DeepSeek R1</option>
                                    <option value="qwen-plus-latest">通义千问-Plus-Latest</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="startAnalysisBtn">开始分析</button>
                    </div>
                </div>
            </div>
        </div>
        `;

        // 查找旧的模态框
        const oldModal = document.getElementById('analyzeModal');
        if (oldModal) {
            console.log('[替换模态框HTML] 找到旧的模态框，移除它');
            oldModal.parentNode.removeChild(oldModal);
        }

        // 添加新的模态框
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHTML;
        document.body.appendChild(modalContainer.firstElementChild);
        
        console.log('[替换模态框HTML] 成功添加新的模态框');

        // 设置全选按钮逻辑
        const selectAllCheckbox = document.getElementById('select-all-dimensions');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                document.querySelectorAll('.dimension-checkbox').forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
            });
        }

        // 设置开始分析按钮逻辑
        const startAnalysisBtn = document.getElementById('startAnalysisBtn');
        if (startAnalysisBtn) {
            startAnalysisBtn.addEventListener('click', function() {
                console.log('[替换模态框HTML] 点击开始分析按钮');
                
                // 获取选中的维度
                const dimensions = [];
                document.querySelectorAll('.dimension-checkbox:checked').forEach(checkbox => {
                    dimensions.push(checkbox.value);
                });
                
                const model = document.getElementById('modelSelect').value;
                
                if (dimensions.length === 0) {
                    alert('请至少选择一个分析维度');
                    return;
                }
                
                // 使用第一个维度作为主维度
                const dimension = dimensions[0];
                
                // 禁用按钮并显示加载状态
                startAnalysisBtn.disabled = true;
                startAnalysisBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
                
                // 显示进度条
                const progressContainer = document.getElementById('analysisProgressContainer');
                if (progressContainer) {
                    progressContainer.style.display = 'block';
                    document.getElementById('analysisProgressBar').style.width = '0%';
                    document.getElementById('analysisProgressBar').setAttribute('aria-valuenow', 0);
                    document.getElementById('analysisProgressBar').textContent = '0%';
                    document.getElementById('analysisStatus').textContent = '正在分析...';
                }
                
                // 关闭模态框
                const modal = document.getElementById('analyzeModal');
                modal.style.display = 'none';
                modal.classList.remove('show');
                document.body.classList.remove('modal-open');
                
                // 移除背景遮罩
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.parentNode.removeChild(backdrop);
                }
                
                // 创建表单数据
                const formData = new FormData();
                formData.append('dimension', dimension);
                formData.append('model', model);
                
                // 发送AJAX请求
                fetch(`/api/novel/${novelId}/chapter/${chapterId}/analyze`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    console.log('[替换模态框HTML] 分析请求已发送，响应:', data);
                    
                    // 检查分析进度
                    checkAnalysisProgress(novelId, chapterId);
                })
                .catch(error => {
                    console.error('[替换模态框HTML] 分析请求失败:', error);
                    alert('分析请求失败，请刷新页面重试');
                    
                    // 恢复按钮状态
                    startAnalysisBtn.disabled = false;
                    startAnalysisBtn.textContent = '开始分析';
                });
            });
        }
        
        console.log('[替换模态框HTML] 模态框HTML替换完成');
    }

    // 检查分析进度
    function checkAnalysisProgress(novelId, chapterId) {
        console.log(`[替换模态框HTML] 开始检查分析进度: 小说ID=${novelId}, 章节ID=${chapterId}`);
        
        const progressInterval = setInterval(function() {
            fetch(`/api/novel/${novelId}/chapter/${chapterId}/progress`)
            .then(response => response.json())
            .then(data => {
                console.log('[替换模态框HTML] 分析进度:', data);
                
                const progressBar = document.getElementById('analysisProgressBar');
                const statusText = document.getElementById('analysisStatus');
                
                if (progressBar && statusText) {
                    if (data.status === 'completed') {
                        // 分析完成
                        progressBar.style.width = '100%';
                        progressBar.setAttribute('aria-valuenow', 100);
                        progressBar.textContent = '100%';
                        statusText.textContent = '分析已完成';
                        
                        // 停止检查进度
                        clearInterval(progressInterval);
                        
                        // 刷新页面显示结果
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else if (data.status === 'processing') {
                        // 分析中
                        const progress = data.progress || 0;
                        progressBar.style.width = `${progress}%`;
                        progressBar.setAttribute('aria-valuenow', progress);
                        progressBar.textContent = `${progress}%`;
                        statusText.textContent = data.message || '正在分析...';
                    } else {
                        // 其他状态
                        statusText.textContent = data.message || '等待分析...';
                    }
                }
            })
            .catch(error => {
                console.error('[替换模态框HTML] 检查分析进度失败:', error);
            });
        }, 2000);
    }

    // 初始化
    function init() {
        console.log('[替换模态框HTML] 初始化中...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(replaceModalHTML, 1000);
            });
        } else {
            setTimeout(replaceModalHTML, 1000);
        }
        
        console.log('[替换模态框HTML] 初始化完成');
    }

    // 初始化
    init();

    console.log('[替换模态框HTML] 脚本加载完成');
})();
