/**
 * 九猫小说分析写作系统 - 分析结果显示修复脚本
 * 
 * 此脚本用于修复分析结果和推理过程显示问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[分析显示修复] 初始化...');
    
    // 配置
    const CONFIG = {
        debug: true,
        selectors: {
            analysisContent: '#analysisContent, .analysis-content',
            reasoningContent: '#reasoningContent, .reasoning-content',
            chapterAnalysisContent: '#chapterAnalysisContent',
            chapterReasoningContent: '#chapterReasoningContent',
            dimensionList: '#dimensionList',
            chapterList: '#chapterList'
        },
        apiPaths: {
            novelAnalysis: '/api/novel/{novelId}/analysis/{dimension}',
            novelReasoning: '/api/novel/{novelId}/analysis/{dimension}/reasoning_content',
            chapterAnalysis: '/api/novel/{novelId}/chapter/{chapterId}/analysis/{dimension}',
            chapterReasoning: '/api/novel/{novelId}/chapter/{chapterId}/analysis/{dimension}/reasoning_content',
            allAnalyses: '/api/novel/{novelId}/analysis'
        }
    };
    
    // 状态
    const STATE = {
        novelId: null,
        chapterId: null,
        dimension: null,
        isChapterAnalysis: false,
        isLoading: false,
        loadedDimensions: new Set(),
        loadedChapters: new Set()
    };
    
    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[分析显示修复] ${message}`);
        }
    }
    
    // 初始化
    function init() {
        debugLog('开始初始化...');
        
        // 确保jQuery已加载
        if (typeof window.ensureJQuery !== 'function') {
            debugLog('ensureJQuery函数不存在，等待页面加载完成后再试', 'warn');
            
            // 等待页面加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', init);
                return;
            }
        } else {
            window.ensureJQuery(function($) {
                debugLog('jQuery已加载，继续初始化');
                initWithJQuery($);
            });
        }
    }
    
    // 使用jQuery初始化
    function initWithJQuery($) {
        debugLog('使用jQuery初始化');
        
        // 获取页面信息
        getPageInfo();
        
        // 添加事件监听器
        addEventListeners($);
        
        // 检查是否需要加载分析结果
        checkAndLoadAnalysisResults($);
    }
    
    // 获取页面信息
    function getPageInfo() {
        debugLog('获取页面信息');
        
        // 尝试从URL获取信息
        const pathParts = window.location.pathname.split('/');
        
        // 检查是否是章节分析页面
        if (pathParts.includes('chapter') && pathParts.includes('analysis')) {
            const novelIdIndex = pathParts.indexOf('novel') + 1;
            const chapterIdIndex = pathParts.indexOf('chapter') + 1;
            const dimensionIndex = pathParts.indexOf('analysis') + 1;
            
            if (novelIdIndex > 0 && chapterIdIndex > 0 && dimensionIndex > 0 &&
                novelIdIndex < pathParts.length &&
                chapterIdIndex < pathParts.length &&
                dimensionIndex < pathParts.length) {
                
                STATE.novelId = pathParts[novelIdIndex];
                STATE.chapterId = pathParts[chapterIdIndex];
                STATE.dimension = pathParts[dimensionIndex];
                STATE.isChapterAnalysis = true;
                
                debugLog(`检测到章节分析页面: 小说ID=${STATE.novelId}, 章节ID=${STATE.chapterId}, 维度=${STATE.dimension}`);
            }
        }
        // 检查是否是整本书分析页面
        else if (pathParts.includes('novel') && pathParts.includes('analysis')) {
            const novelIdIndex = pathParts.indexOf('novel') + 1;
            const dimensionIndex = pathParts.indexOf('analysis') + 1;
            
            if (novelIdIndex > 0 && dimensionIndex > 0 &&
                novelIdIndex < pathParts.length &&
                dimensionIndex < pathParts.length) {
                
                STATE.novelId = pathParts[novelIdIndex];
                STATE.dimension = pathParts[dimensionIndex];
                STATE.isChapterAnalysis = false;
                
                debugLog(`检测到整本书分析页面: 小说ID=${STATE.novelId}, 维度=${STATE.dimension}`);
            }
        }
        // 检查是否是控制台页面
        else if (pathParts.includes('console')) {
            debugLog('检测到控制台页面');
            
            // 控制台页面需要从DOM元素获取信息
            setTimeout(function() {
                const $dimensionList = $(CONFIG.selectors.dimensionList);
                if ($dimensionList.length > 0) {
                    debugLog('找到维度列表元素，将监听点击事件');
                }
                
                const $chapterList = $(CONFIG.selectors.chapterList);
                if ($chapterList.length > 0) {
                    debugLog('找到章节列表元素，将监听点击事件');
                }
            }, 1000);
        }
        
        // 如果无法从URL获取，尝试从DOM元素获取
        if (!STATE.novelId) {
            setTimeout(function() {
                const $novelElement = $('[data-novel-id]');
                if ($novelElement.length > 0) {
                    STATE.novelId = $novelElement.data('novel-id');
                    debugLog(`从DOM元素获取小说ID: ${STATE.novelId}`);
                }
                
                const $chapterElement = $('[data-chapter-id]');
                if ($chapterElement.length > 0) {
                    STATE.chapterId = $chapterElement.data('chapter-id');
                    STATE.isChapterAnalysis = true;
                    debugLog(`从DOM元素获取章节ID: ${STATE.chapterId}`);
                }
                
                const $dimensionElement = $('[data-dimension]');
                if ($dimensionElement.length > 0) {
                    STATE.dimension = $dimensionElement.data('dimension');
                    debugLog(`从DOM元素获取维度: ${STATE.dimension}`);
                }
            }, 500);
        }
    }
    
    // 添加事件监听器
    function addEventListeners($) {
        debugLog('添加事件监听器');
        
        // 监听维度列表点击事件
        $(document).on('click', `${CONFIG.selectors.dimensionList} .list-group-item`, function() {
            const dimension = $(this).data('dimension');
            const novelId = $(this).data('novel-id') || STATE.novelId;
            
            if (dimension && novelId) {
                debugLog(`点击了维度: ${dimension}, 小说ID: ${novelId}`);
                loadNovelAnalysisResult(novelId, dimension);
            }
        });
        
        // 监听章节列表点击事件
        $(document).on('click', `${CONFIG.selectors.chapterList} .list-group-item`, function() {
            const chapterId = $(this).data('chapter-id');
            const novelId = $(this).data('novel-id') || STATE.novelId;
            const dimension = $(this).data('dimension') || STATE.dimension;
            
            if (chapterId && novelId && dimension) {
                debugLog(`点击了章节: ${chapterId}, 小说ID: ${novelId}, 维度: ${dimension}`);
                loadChapterAnalysisResult(novelId, chapterId, dimension);
            }
        });
        
        // 监听标签页切换事件
        $(document).on('shown.bs.tab', 'a[data-bs-toggle="tab"]', function(e) {
            const target = $(e.target).attr('href');
            
            // 检查是否切换到了推理过程标签
            if (target === '#reasoning' || target === '#chapter-reasoning') {
                debugLog('切换到了推理过程标签，检查是否需要加载推理过程');
                
                if (target === '#reasoning' && STATE.novelId && STATE.dimension) {
                    loadNovelReasoningContent(STATE.novelId, STATE.dimension);
                } else if (target === '#chapter-reasoning' && STATE.novelId && STATE.chapterId && STATE.dimension) {
                    loadChapterReasoningContent(STATE.novelId, STATE.chapterId, STATE.dimension);
                }
            }
        });
    }
    
    // 检查并加载分析结果
    function checkAndLoadAnalysisResults($) {
        debugLog('检查是否需要加载分析结果');
        
        // 检查是否有必要的信息
        if (!STATE.novelId) {
            debugLog('缺少小说ID，无法加载分析结果', 'warn');
            return;
        }
        
        // 检查是否是章节分析页面
        if (STATE.isChapterAnalysis && STATE.chapterId && STATE.dimension) {
            debugLog('检测到章节分析页面，加载章节分析结果');
            loadChapterAnalysisResult(STATE.novelId, STATE.chapterId, STATE.dimension);
        }
        // 检查是否是整本书分析页面
        else if (STATE.dimension) {
            debugLog('检测到整本书分析页面，加载整本书分析结果');
            loadNovelAnalysisResult(STATE.novelId, STATE.dimension);
        }
        // 检查是否是控制台页面
        else if (window.location.pathname.includes('console')) {
            debugLog('检测到控制台页面，等待用户选择维度或章节');
        }
    }
    
    // 加载整本书分析结果
    function loadNovelAnalysisResult(novelId, dimension) {
        debugLog(`加载整本书分析结果: novelId=${novelId}, dimension=${dimension}`);
        
        // 检查是否已加载
        const key = `${novelId}_${dimension}`;
        if (STATE.loadedDimensions.has(key)) {
            debugLog(`维度 ${dimension} 已加载，跳过`);
            return;
        }
        
        // 构建API URL
        const apiUrl = CONFIG.apiPaths.novelAnalysis
            .replace('{novelId}', novelId)
            .replace('{dimension}', dimension);
        
        // 添加时间戳防止缓存
        const url = `${apiUrl}?_=${Date.now()}`;
        
        // 发送请求
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到分析结果数据');
                
                // 提取结果内容
                let content = '';
                if (data.result && data.result.content) {
                    content = data.result.content;
                } else if (data.content) {
                    content = data.content;
                }
                
                if (content) {
                    // 更新分析结果显示
                    updateAnalysisContent(content);
                    debugLog('成功更新分析结果内容');
                    
                    // 标记为已加载
                    STATE.loadedDimensions.add(key);
                    
                    // 加载推理过程
                    loadNovelReasoningContent(novelId, dimension);
                } else {
                    debugLog('分析结果内容为空', 'warn');
                }
            })
            .catch(error => {
                debugLog(`加载分析结果出错: ${error.message}`, 'error');
            });
    }
    
    // 加载整本书推理过程
    function loadNovelReasoningContent(novelId, dimension) {
        debugLog(`加载整本书推理过程: novelId=${novelId}, dimension=${dimension}`);
        
        // 构建API URL
        const apiUrl = CONFIG.apiPaths.novelReasoning
            .replace('{novelId}', novelId)
            .replace('{dimension}', dimension);
        
        // 添加时间戳防止缓存
        const url = `${apiUrl}?_=${Date.now()}`;
        
        // 发送请求
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到推理过程数据');
                
                // 提取推理过程内容
                let reasoningContent = '';
                if (data.reasoning_content) {
                    reasoningContent = data.reasoning_content;
                } else if (data.content) {
                    reasoningContent = data.content;
                }
                
                if (reasoningContent) {
                    // 更新推理过程显示
                    updateReasoningContent(reasoningContent);
                    debugLog('成功更新推理过程内容');
                } else {
                    debugLog('推理过程内容为空', 'warn');
                }
            })
            .catch(error => {
                debugLog(`加载推理过程出错: ${error.message}`, 'error');
            });
    }
    
    // 更新分析内容
    function updateAnalysisContent(content) {
        const $contentElement = $(CONFIG.selectors.analysisContent);
        if ($contentElement.length > 0) {
            $contentElement.html(content);
            debugLog('分析内容已更新');
        } else {
            debugLog('找不到分析内容元素', 'warn');
        }
    }
    
    // 更新推理过程内容
    function updateReasoningContent(content) {
        const $contentElement = $(CONFIG.selectors.reasoningContent);
        if ($contentElement.length > 0) {
            $contentElement.html(content);
            debugLog('推理过程内容已更新');
        } else {
            debugLog('找不到推理过程内容元素', 'warn');
        }
    }
    
    // 加载章节分析结果
    function loadChapterAnalysisResult(novelId, chapterId, dimension) {
        debugLog(`加载章节分析结果: novelId=${novelId}, chapterId=${chapterId}, dimension=${dimension}`);
        
        // 检查是否已加载
        const key = `${novelId}_${chapterId}_${dimension}`;
        if (STATE.loadedChapters.has(key)) {
            debugLog(`章节 ${chapterId} 的维度 ${dimension} 已加载，跳过`);
            return;
        }
        
        // 构建API URL
        const apiUrl = CONFIG.apiPaths.chapterAnalysis
            .replace('{novelId}', novelId)
            .replace('{chapterId}', chapterId)
            .replace('{dimension}', dimension);
        
        // 添加时间戳防止缓存
        const url = `${apiUrl}?_=${Date.now()}`;
        
        // 发送请求
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到章节分析结果数据');
                
                // 提取结果内容
                let content = '';
                if (data.result && data.result.content) {
                    content = data.result.content;
                } else if (data.content) {
                    content = data.content;
                }
                
                if (content) {
                    // 更新章节分析结果显示
                    updateChapterAnalysisContent(content);
                    debugLog('成功更新章节分析结果内容');
                    
                    // 标记为已加载
                    STATE.loadedChapters.add(key);
                    
                    // 加载章节推理过程
                    loadChapterReasoningContent(novelId, chapterId, dimension);
                } else {
                    debugLog('章节分析结果内容为空', 'warn');
                }
            })
            .catch(error => {
                debugLog(`加载章节分析结果出错: ${error.message}`, 'error');
            });
    }
    
    // 加载章节推理过程
    function loadChapterReasoningContent(novelId, chapterId, dimension) {
        debugLog(`加载章节推理过程: novelId=${novelId}, chapterId=${chapterId}, dimension=${dimension}`);
        
        // 构建API URL
        const apiUrl = CONFIG.apiPaths.chapterReasoning
            .replace('{novelId}', novelId)
            .replace('{chapterId}', chapterId)
            .replace('{dimension}', dimension);
        
        // 添加时间戳防止缓存
        const url = `${apiUrl}?_=${Date.now()}`;
        
        // 发送请求
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到章节推理过程数据');
                
                // 提取推理过程内容
                let reasoningContent = '';
                if (data.reasoning_content) {
                    reasoningContent = data.reasoning_content;
                } else if (data.content) {
                    reasoningContent = data.content;
                }
                
                if (reasoningContent) {
                    // 更新章节推理过程显示
                    updateChapterReasoningContent(reasoningContent);
                    debugLog('成功更新章节推理过程内容');
                } else {
                    debugLog('章节推理过程内容为空', 'warn');
                }
            })
            .catch(error => {
                debugLog(`加载章节推理过程出错: ${error.message}`, 'error');
            });
    }
    
    // 更新章节分析内容
    function updateChapterAnalysisContent(content) {
        const $contentElement = $(CONFIG.selectors.chapterAnalysisContent);
        if ($contentElement.length > 0) {
            $contentElement.html(content);
            debugLog('章节分析内容已更新');
        } else {
            debugLog('找不到章节分析内容元素', 'warn');
        }
    }
    
    // 更新章节推理过程内容
    function updateChapterReasoningContent(content) {
        const $contentElement = $(CONFIG.selectors.chapterReasoningContent);
        if ($contentElement.length > 0) {
            $contentElement.html(content);
            debugLog('章节推理过程内容已更新');
        } else {
            debugLog('找不到章节推理过程内容元素', 'warn');
        }
    }
    
    // 初始化
    init();
    
    console.log('[分析显示修复] 初始化完成');
})();
