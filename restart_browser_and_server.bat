@echo off
echo ===================================
echo   重启浏览器和服务器
echo ===================================
echo.

echo 正在关闭所有Chrome进程...
taskkill /F /IM chrome.exe /T > nul 2>&1
if %errorlevel% equ 0 (
    echo Chrome已关闭
) else (
    echo Chrome未运行或无法关闭
)

echo 正在关闭所有Python进程...
taskkill /F /IM python.exe /T > nul 2>&1
if %errorlevel% equ 0 (
    echo Python进程已关闭
) else (
    echo Python进程未运行或无法关闭
)

echo 正在清除DNS缓存...
ipconfig /flushdns > nul
echo DNS缓存已清除

echo 正在等待5秒...
timeout /t 5 /nobreak > nul

echo 正在启动九猫服务器...
start /B python main.py

echo 正在等待10秒，确保服务器启动...
timeout /t 10 /nobreak > nul

echo 正在打开浏览器...
start "" http://localhost:5001

echo 完成！
echo.
pause
