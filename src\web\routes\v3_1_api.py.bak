"""
九猫小说分析写作系统v3.1 - API路由
"""
import logging
import json
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app

from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.preset import Preset
from src.services.template_conversion_service import TemplateConversionService

logger = logging.getLogger(__name__)

v3_1_api_bp = Blueprint('v3_1_api', __name__, url_prefix='/v3.1/api')

@v3_1_api_bp.route('/convert_to_template', methods=['POST'])
def convert_to_template():
    """
    将参考蓝本的分析结果转换为预设模板

    请求体:
    {
        "template_id": 参考蓝本ID,
        "knowledge_base_data": 知识库数据（可选）
    }

    响应:
    {
        "success": 是否成功,
        "message": 成功消息,
        "preset_id": 预设ID,
        "book_templates": 整本书预设模板,
        "chapter_templates": 章节预设模板
    }
    """
    try:
        data = request.get_json()
        if not data:
            logger.error("转换预设模板请求体为空")
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        template_id = data.get('template_id')
        if not template_id:
            logger.error("转换预设模板缺少参考蓝本ID")
            return jsonify({
                'success': False,
                'error': '参考蓝本ID不能为空'
            }), 400

        knowledge_base_data = data.get('knowledge_base_data')
        logger.info(f"开始转换参考蓝本 ID {template_id} 为预设模板")

        # 创建一个简单的预设模板
        session = Session()
        try:
            # 获取参考蓝本信息
            template = session.query(Novel).get(template_id)
            if not template:
                logger.error(f"未找到参考蓝本 ID {template_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 创建预设模板
            preset = Preset(
                title=f"预设模板-{template.title}",
                content=f"# {template.title} 预设模板\n\n## 基本信息\n- 参考蓝本ID: {template_id}\n- 参考蓝本: {template.title}\n- 作者: {template.author or '未知'}\n- 字数: {template.word_count or 0}\n- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n## 预设模板内容\n这是基于参考蓝本《{template.title}》生成的预设模板。",
                category="preset_template",
                meta_info={
                    'template_id': template_id,
                    'template_title': template.title,
                    'created_at': datetime.now().isoformat()
                }
            )

            session.add(preset)
            session.commit()

            # 构建响应数据
            result = {
                'success': True,
                'message': '成功转换为预设模板',
                'preset_id': preset.id,
                'template': {
                    'id': template.id,
                    'title': template.title
                },
                'book_templates': {},
                'chapter_templates': {}
            }

            logger.info(f"成功创建预设模板 ID {preset.id}")
            return jsonify(result), 200
        except Exception as e:
            session.rollback()
            logger.error(f"创建预设模板时出错: {str(e)}", exc_info=True)
            raise
        finally:
            session.close()
    except Exception as e:
        logger.error(f"转换参考蓝本为预设模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/analysis', methods=['GET'])
def get_template_analysis(template_id):
    """
    获取参考蓝本的分析结果

    响应:
    {
        "success": 是否成功,
        "template": 参考蓝本信息,
        "book_analyses": 整本书分析结果,
        "chapters": 章节列表及分析结果
    }
    """
    try:
        session = Session()
        try:
            # 获取参考蓝本
            template = session.query(Novel).get(template_id)
            if not template:
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 检查是否为参考蓝本
            if not (template.novel_metadata and template.novel_metadata.get('is_template')):
                return jsonify({
                    'success': False,
                    'error': '指定的小说不是参考蓝本'
                }), 400

            # 获取整本书分析结果
            book_analyses = []
            results = session.query(AnalysisResult).filter_by(novel_id=template_id).all()
            for result in results:
                book_analyses.append({
                    'dimension': result.dimension,
                    'result': result.content,
                    'reasoning_content': result.reasoning_content,
                    'created_at': result.created_at.isoformat() if result.created_at else None,
                    'updated_at': result.updated_at.isoformat() if result.updated_at else None
                })

            # 获取章节
            chapters_data = []
            chapters = session.query(Chapter).filter_by(novel_id=template_id).order_by(Chapter.chapter_number).all()

            # 获取章节分析结果
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=template_id,
                    chapter_id=chapter.id
                ).all()

                analysis_results = []
                for result in chapter_results:
                    analysis_results.append({
                        'dimension': result.dimension,
                        'result': result.content,
                        'reasoning_content': result.reasoning_content,
                        'created_at': result.created_at.isoformat() if result.created_at else None,
                        'updated_at': result.updated_at.isoformat() if result.updated_at else None
                    })

                chapters_data.append({
                    'id': chapter.id,
                    'novel_id': chapter.novel_id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'word_count': chapter.word_count,
                    'analysis_results': analysis_results
                })

            # 构建响应数据
            response_data = {
                'success': True,
                'template': {
                    'id': template.id,
                    'title': template.title,
                    'author': template.author,
                    'word_count': template.word_count,
                    'created_at': template.created_at.isoformat() if template.created_at else None,
                    'updated_at': template.updated_at.isoformat() if template.updated_at else None,
                    'template_created_at': template.novel_metadata.get('template_created_at') if template.novel_metadata else None
                },
                'book_analyses': book_analyses,
                'chapters': chapters_data
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取参考蓝本分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/preset/<int:preset_id>', methods=['GET'])
def get_preset(preset_id):
    """
    获取预设模板

    响应:
    {
        "success": 是否成功,
        "preset": 预设模板信息,
        "book_templates": 整本书预设模板,
        "chapter_templates": 章节预设模板
    }
    """
    try:
        session = Session()
        try:
            # 获取预设模板
            preset = session.query(Preset).get(preset_id)
            if not preset:
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设模板'
                }), 404

            # 检查是否为预设模板
            if preset.category != 'preset_template':
                return jsonify({
                    'success': False,
                    'error': '指定的预设不是预设模板'
                }), 400

            # 获取预设模板内容
            meta_info = preset.meta_info or {}
            template_id = meta_info.get('template_id')

            # 获取参考蓝本
            template = None
            if template_id:
                template = session.query(Novel).get(template_id)

            # 构建响应数据
            response_data = {
                'success': True,
                'preset': {
                    'id': preset.id,
                    'title': preset.title,
                    'content': preset.content,
                    'category': preset.category,
                    'created_at': preset.created_at.isoformat() if preset.created_at else None,
                    'updated_at': preset.updated_at.isoformat() if preset.updated_at else None,
                    'meta_info': meta_info
                },
                'template': {
                    'id': template.id,
                    'title': template.title,
                    'author': template.author,
                    'word_count': template.word_count
                } if template else None
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/preset/<int:preset_id>/templates', methods=['GET'])
def get_preset_templates(preset_id):
    """
    获取预设模板详情

    响应:
    {
        "success": 是否成功,
        "preset": 预设模板信息,
        "novel": 参考蓝本信息,
        "book_templates": 整本书预设模板,
        "chapter_templates": 章节预设模板
    }
    """
    try:
        logger.info(f"获取预设模板详情 [预设ID: {preset_id}]")
        session = Session()
        try:
            # 获取预设模板
            preset = session.query(Preset).get(preset_id)
            if not preset:
                logger.error(f"未找到预设模板 ID {preset_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设模板'
                }), 404

            # 获取预设模板内容
            meta_info = preset.meta_info or {}
            template_id = meta_info.get('template_id')

            # 获取参考蓝本
            novel = None
            if template_id:
                novel = session.query(Novel).get(template_id)

            # 获取章节列表
            chapters = []
            if novel:
                chapters = session.query(Chapter).filter_by(novel_id=novel.id).order_by(Chapter.chapter_number).all()
                logger.info(f"获取到 {len(chapters)} 个章节")

            # 构建响应数据
            response_data = {
                'success': True,
                'preset': {
                    'id': preset.id,
                    'title': preset.title,
                    'content': preset.content,
                    'category': preset.category,
                    'created_at': preset.created_at.isoformat() if preset.created_at else None,
                    'updated_at': preset.updated_at.isoformat() if preset.updated_at else None,
                    'meta_info': meta_info
                },
                'novel': {
                    'id': novel.id,
                    'title': novel.title,
                    'author': novel.author,
                    'word_count': novel.word_count,
                    'created_at': novel.created_at.isoformat() if novel.created_at else None,
                    'updated_at': novel.updated_at.isoformat() if novel.updated_at else None
                } if novel else None,
                'chapters': [
                    {
                        'id': chapter.id,
                        'novel_id': chapter.novel_id,
                        'chapter_number': chapter.chapter_number,
                        'title': chapter.title or f'第{chapter.chapter_number}章',
                        'word_count': chapter.word_count
                    } for chapter in chapters
                ]
            }

            logger.info(f"成功获取预设模板详情 [预设ID: {preset_id}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设模板详情时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/template/<dimension>', methods=['GET'])
def get_novel_template(novel_id, dimension):
    """
    获取小说特定维度的设定模板

    响应:
    {
        "success": 是否成功,
        "template": 设定模板内容
    }
    """
    try:
        logger.info(f"获取小说设定模板 [小说ID: {novel_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 ID {novel_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            # 构建模板内容
            template = None
            if result and result.content:
                # 从分析结果生成模板
                template = f"# {novel.title} - {dimension} 设定模板\n\n"
                template += f"## 基本信息\n"
                template += f"- 小说: {novel.title}\n"
                template += f"- 作者: {novel.author}\n"
                template += f"- 维度: {dimension}\n"
                template += f"- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                template += f"## 设定内容\n"
                template += result.content

            # 构建响应数据
            response_data = {
                'success': True,
                'template': template
            }

            logger.info(f"成功获取小说设定模板 [小说ID: {novel_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说设定模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>', methods=['GET'])
def get_chapter_template(novel_id, chapter_id, dimension):
    """
    获取章节特定维度的设定模板

    响应:
    {
        "success": 是否成功,
        "template": 设定模板内容
    }
    """
    try:
        logger.info(f"获取章节设定模板 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 ID {novel_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter:
                logger.error(f"未找到章节 ID {chapter_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 获取章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            # 构建模板内容
            template = None
            if result and result.content:
                # 从分析结果生成模板
                template = f"# {novel.title} - {chapter.title or f'第{chapter.chapter_number}章'} - {dimension} 设定模板\n\n"
                template += f"## 基本信息\n"
                template += f"- 小说: {novel.title}\n"
                template += f"- 章节: {chapter.title or f'第{chapter.chapter_number}章'}\n"
                template += f"- 维度: {dimension}\n"
                template += f"- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                template += f"## 设定内容\n"
                template += result.content

            # 构建响应数据
            response_data = {
                'success': True,
                'template': template
            }

            logger.info(f"成功获取章节设定模板 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节设定模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/start_analysis', methods=['POST'])
def start_analysis():
    """
    开始分析小说

    请求体:
    {
        "novel_id": 小说ID,
        "dimensions": 分析维度列表
    }

    响应:
    {
        "success": 是否成功,
        "message": 成功消息
    }
    """
    try:
        # 获取请求数据
        data = request.json or {}
        novel_id = data.get('novel_id')
        dimensions = data.get('dimensions', [])

        if not novel_id:
            return jsonify({
                'success': False,
                'message': '未指定小说ID'
            }), 400

        if not dimensions:
            return jsonify({
                'success': False,
                'message': '未指定分析维度'
            }), 400

        # 获取小说
        session = Session()
        try:
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'message': '未找到指定小说'
                }), 404

            # 导入单维度分析模块
            from src.api.analyze_dimension import analyze_dimension

            # 逐个维度分析
            for dimension in dimensions:
                # 启动分析
                analyze_dimension(novel, dimension)

            return jsonify({
                'success': True,
                'message': '分析已开始'
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"开始分析时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/analyze_dimension', methods=['POST'])
def analyze_novel_dimension(novel_id):
    """
    创建小说维度分析任务

    请求体:
    {
        "dimension": 维度名称,
        "force": 是否强制重新分析
    }

    响应:
    {
        "success": 是否成功,
        "analysis_id": 分析任务ID,
        "message": 成功消息
    }
    """
    try:
        # 获取请求数据
        data = request.json or {}
        dimension = data.get('dimension')
        if not dimension:
            return jsonify({
                'success': False,
                'error': '维度名称不能为空'
            }), 400

        force = data.get('force', 0)

        # 获取小说
        session = Session()
        try:
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 导入单维度分析模块
            from src.api.analyze_dimension import analyze_dimension

            # 启动分析
            analyze_dimension(novel, dimension, force=bool(force))

            # 生成分析ID
            analysis_id = f"{novel_id}_{dimension}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            return jsonify({
                'success': True,
                'analysis_id': analysis_id,
                'message': f'成功创建分析任务，正在分析维度 {dimension}'
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"创建分析任务时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/analysis/<analysis_id>/status', methods=['GET'])
def get_analysis_status(analysis_id):
    """
    获取分析任务状态

    响应:
    {
        "success": 是否成功,
        "status": 分析状态,
        "progress": 分析进度,
        "log_entries": 日志条目,
        "result_url": 结果URL（如果完成）,
        "error": 错误信息（如果失败）
    }
    """
    try:
        # 解析分析ID
        parts = analysis_id.split('_')
        if len(parts) < 3:
            return jsonify({
                'success': False,
                'error': '无效的分析ID'
            }), 400

        novel_id = parts[0]
        dimension = parts[1]

        # 尝试将novel_id转换为整数
        try:
            novel_id_int = int(novel_id)
        except ValueError:
            novel_id_int = novel_id

        # 获取小说和分析结果
        session = Session()
        try:
            # 检查分析结果是否存在
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id_int,
                dimension=dimension
            ).first()

            # 检查分析是否完成
            if result:
                status = 'completed'
                progress = 100
                result_url = f"/v3.1/analysis/{novel_id}/{dimension}"
                log_entries = [
                    {'level': 'info', 'message': '分析已完成'},
                    {'level': 'success', 'message': '分析结果已保存'}
                ]

                # 记录日志
                logger.info(f"分析已完成: 小说ID={novel_id}, 维度={dimension}")
            else:
                # 检查是否正在分析中
                # 这里可以添加更复杂的逻辑来检查分析是否正在进行
                # 例如检查分析进度表或临时文件
                status = 'analyzing'
                progress = 50
                result_url = None
                log_entries = [
                    {'level': 'info', 'message': '分析正在进行中...'}
                ]

                # 记录日志
                logger.info(f"分析进行中: 小说ID={novel_id}, 维度={dimension}, 进度=50%")

            return jsonify({
                'success': True,
                'status': status,
                'progress': progress,
                'log_entries': log_entries,
                'result_url': result_url
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取分析状态时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/analysis/<analysis_id>/cancel', methods=['POST'])
def cancel_analysis(analysis_id):
    """
    取消分析任务

    响应:
    {
        "success": 是否成功,
        "message": 成功消息
    }
    """
    try:
        # 解析分析ID
        parts = analysis_id.split('_')
        if len(parts) < 3:
            return jsonify({
                'success': False,
                'error': '无效的分析ID'
            }), 400

        # 模拟取消分析
        return jsonify({
            'success': True,
            'message': '分析已取消'
        }), 200
    except Exception as e:
        logger.error(f"取消分析时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/reanalyze/<dimension>', methods=['POST'])
def reanalyze_dimension(novel_id, dimension):
    """
    重新分析小说的特定维度

    响应:
    {
        "success": 是否成功,
        "message": 成功消息
    }
    """
    try:
        # 获取小说
        session = Session()
        try:
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'message': '未找到指定小说'
                }), 404

            # 导入单维度分析模块
            from src.api.analyze_dimension import analyze_dimension

            # 启动分析，强制重新分析
            analyze_dimension(novel, dimension, force=True)

            return jsonify({
                'success': True,
                'message': '重新分析已开始'
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"重新分析维度时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/set_as_template', methods=['POST'])
def set_as_template(novel_id):
    """
    将小说设置为参考蓝本

    响应:
    {
        "success": 是否成功,
        "message": 成功消息
    }
    """
    try:
        # 获取小说
        session = Session()
        try:
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'message': '未找到指定小说'
                }), 404

            # 检查是否已经是参考蓝本
            if novel.novel_metadata and novel.novel_metadata.get('is_template'):
                return jsonify({
                    'success': True,
                    'message': '该小说已经是参考蓝本'
                })

            # 设置为参考蓝本
            if not novel.novel_metadata:
                novel.novel_metadata = {}

            novel.novel_metadata['is_template'] = True
            novel.novel_metadata['template_created_at'] = datetime.now().isoformat()

            session.commit()

            return jsonify({
                'success': True,
                'message': '成功设置为参考蓝本'
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"设置参考蓝本时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>', methods=['GET'])
def get_novel_info(novel_id):
    """
    获取小说信息、章节列表和分析维度

    响应:
    {
        "success": 是否成功,
        "novel": 小说信息,
        "chapters": 章节列表,
        "dimensions": 分析维度列表
    }
    """
    try:
        logger.info(f"获取小说信息 [小说ID: {novel_id}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 ID {novel_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取章节列表
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()
            chapters_data = []

            for chapter in chapters:
                # 获取章节分析结果数量
                analysis_count = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).count()

                chapters_data.append({
                    'id': chapter.id,
                    'novel_id': chapter.novel_id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'word_count': chapter.word_count,
                    'analysis_count': analysis_count
                })

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS
            dimensions_data = []

            # 获取已分析的维度
            analyzed_dimensions = []
            results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            for result in results:
                analyzed_dimensions.append(result.dimension)

            # 构建维度列表
            for dim in ANALYSIS_DIMENSIONS:
                dim_copy = dim.copy()
                dim_copy['is_analyzed'] = dim['key'] in analyzed_dimensions

                # 设置默认图标
                if 'icon' not in dim_copy:
                    if dim_copy['key'] == 'language_style':
                        dim_copy['icon'] = 'language'
                    elif dim_copy['key'] == 'rhythm_pace':
                        dim_copy['icon'] = 'drum'
                    elif dim_copy['key'] == 'character_development':
                        dim_copy['icon'] = 'user'
                    elif dim_copy['key'] == 'emotional_expression':
                        dim_copy['icon'] = 'heart'
                    elif dim_copy['key'] == 'scene_description':
                        dim_copy['icon'] = 'image'
                    elif dim_copy['key'] == 'dialogue_analysis':
                        dim_copy['icon'] = 'comments'
                    elif dim_copy['key'] == 'theme_exploration':
                        dim_copy['icon'] = 'lightbulb'
                    elif dim_copy['key'] == 'conflict_setup':
                        dim_copy['icon'] = 'bolt'
                    elif dim_copy['key'] == 'perspective_shifts':
                        dim_copy['icon'] = 'eye'
                    elif dim_copy['key'] == 'symbolism_imagery':
                        dim_copy['icon'] = 'feather-alt'
                    elif dim_copy['key'] == 'cultural_context':
                        dim_copy['icon'] = 'globe'
                    elif dim_copy['key'] == 'plot_development':
                        dim_copy['icon'] = 'route'
                    elif dim_copy['key'] == 'structure_layout':
                        dim_copy['icon'] = 'th-large'
                    elif dim_copy['key'] == 'outline_analysis':
                        dim_copy['icon'] = 'list'
                    elif dim_copy['key'] == 'chapter_outline':
                        dim_copy['icon'] = 'list-ol'
                    else:
                        dim_copy['icon'] = 'search'

                dimensions_data.append(dim_copy)

            # 构建响应数据
            response_data = {
                'success': True,
                'novel': {
                    'id': novel.id,
                    'title': novel.title,
                    'author': novel.author,
                    'word_count': novel.word_count,
                    'created_at': novel.created_at.isoformat() if novel.created_at else None,
                    'updated_at': novel.updated_at.isoformat() if novel.updated_at else None,
                    'is_template': novel.novel_metadata and novel.novel_metadata.get('is_template', False) if novel.novel_metadata else False
                },
                'chapters': chapters_data,
                'dimensions': dimensions_data
            }

            logger.info(f"成功获取小说信息 [小说ID: {novel_id}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/dimensions', methods=['GET'])
def get_chapter_dimensions(novel_id, chapter_id):
    """
    获取章节分析维度

    响应:
    {
        "success": 是否成功,
        "dimensions": 分析维度列表
    }
    """
    try:
        logger.info(f"获取章节分析维度 [小说ID: {novel_id}, 章节ID: {chapter_id}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 ID {novel_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter:
                logger.error(f"未找到章节 ID {chapter_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS
            dimensions_data = []

            # 获取已分析的维度
            analyzed_dimensions = []
            results = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id
            ).all()

            for result in results:
                analyzed_dimensions.append(result.dimension)

            # 构建维度列表
            for dim in ANALYSIS_DIMENSIONS:
                dim_copy = dim.copy()
                dim_copy['is_analyzed'] = dim['key'] in analyzed_dimensions

                # 设置默认图标
                if 'icon' not in dim_copy:
                    if dim_copy['key'] == 'language_style':
                        dim_copy['icon'] = 'language'
                    elif dim_copy['key'] == 'rhythm_pace':
                        dim_copy['icon'] = 'drum'
                    elif dim_copy['key'] == 'character_development':
                        dim_copy['icon'] = 'user'
                    elif dim_copy['key'] == 'emotional_expression':
                        dim_copy['icon'] = 'heart'
                    elif dim_copy['key'] == 'scene_description':
                        dim_copy['icon'] = 'image'
                    elif dim_copy['key'] == 'dialogue_analysis':
                        dim_copy['icon'] = 'comments'
                    elif dim_copy['key'] == 'theme_exploration':
                        dim_copy['icon'] = 'lightbulb'
                    elif dim_copy['key'] == 'conflict_setup':
                        dim_copy['icon'] = 'bolt'
                    elif dim_copy['key'] == 'perspective_shifts':
                        dim_copy['icon'] = 'eye'
                    elif dim_copy['key'] == 'symbolism_imagery':
                        dim_copy['icon'] = 'feather-alt'
                    elif dim_copy['key'] == 'cultural_context':
                        dim_copy['icon'] = 'globe'
                    elif dim_copy['key'] == 'plot_development':
                        dim_copy['icon'] = 'route'
                    elif dim_copy['key'] == 'structure_layout':
                        dim_copy['icon'] = 'th-large'
                    elif dim_copy['key'] == 'outline_analysis':
                        dim_copy['icon'] = 'list'
                    elif dim_copy['key'] == 'chapter_outline':
                        dim_copy['icon'] = 'list-ol'
                    else:
                        dim_copy['icon'] = 'search'

                dimensions_data.append(dim_copy)

            # 构建响应数据
            response_data = {
                'success': True,
                'dimensions': dimensions_data
            }

            logger.info(f"成功获取章节分析维度 [小说ID: {novel_id}, 章节ID: {chapter_id}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析维度时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
def get_chapter_analysis(template_id, chapter_id, dimension):
    """
    获取章节分析结果

    响应:
    {
        "success": 是否成功,
        "content": 分析结果内容
    }
    """
    try:
        logger.info(f"获取章节分析结果 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到章节分析结果 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'content': result.content
            }

            logger.info(f"成功获取章节分析结果 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_chapter_reasoning(template_id, chapter_id, dimension):
    """
    获取章节推理过程

    响应:
    {
        "success": 是否成功,
        "content": 推理过程内容
    }
    """
    try:
        logger.info(f"获取章节推理过程 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到章节分析结果 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'content': result.reasoning_content or "暂无推理过程"
            }

            logger.info(f"成功获取章节推理过程 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/presets', methods=['GET'])
def get_presets():
    """
    获取预设模板列表

    响应:
    {
        "success": 是否成功,
        "presets": 预设模板列表
    }
    """
    try:
        session = Session()
        try:
            # 获取预设模板列表
            presets = session.query(Preset).filter_by(category='preset_template').all()

            # 构建响应数据
            presets_data = []
            for preset in presets:
                meta_info = preset.meta_info or {}
                template_id = meta_info.get('template_id')

                # 获取参考蓝本
                template = None
                if template_id:
                    template = session.query(Novel).get(template_id)

                presets_data.append({
                    'id': preset.id,
                    'title': preset.title,
                    'created_at': preset.created_at.isoformat() if preset.created_at else None,
                    'updated_at': preset.updated_at.isoformat() if preset.updated_at else None,
                    'meta_info': meta_info,
                    'template': {
                        'id': template.id,
                        'title': template.title,
                        'author': template.author,
                        'word_count': template.word_count
                    } if template else None
                })

            response_data = {
                'success': True,
                'presets': presets_data
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设模板列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/logs', methods=['GET'])
def get_logs():
    """
    获取系统日志

    响应:
    {
        "success": 是否成功,
        "logs": 日志列表
    }
    """
    try:
        # 获取日志文件路径
        log_file = current_app.config.get('LOG_FILE', 'logs/app.log')

        # 读取日志文件
        logs = []
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    logs.append(line.strip())
        except FileNotFoundError:
            logs = ['日志文件不存在']

        # 构建响应数据
        response_data = {
            'success': True,
            'logs': logs[-100:]  # 只返回最后100行日志
        }

        return jsonify(response_data), 200
    except Exception as e:
        logger.error(f"获取系统日志时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/system_info', methods=['GET'])
def get_system_info():
    """
    获取系统信息

    响应:
    {
        "success": 是否成功,
        "system_info": 系统信息
    }
    """
    try:
        # 获取系统信息
        system_info = {
            'version': 'v3.1',
            'name': '九猫小说分析写作系统',
            'start_time': current_app.config.get('START_TIME', datetime.now().isoformat()),
            'api_call_count': current_app.config.get('API_CALL_COUNT', 0),
            'api_cost_estimate': current_app.config.get('API_COST_ESTIMATE', 0.0)
        }

        # 构建响应数据
        response_data = {
            'success': True,
            'system_info': system_info
        }

        return jsonify(response_data), 200
    except Exception as e:
        logger.error(f"获取系统信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/analysis/<dimension>', methods=['GET'])
def get_template_dimension_analysis(template_id, dimension):
    """
    获取参考蓝本特定维度的分析结果

    响应:
    {
        "success": 是否成功,
        "content": 分析内容
    }
    """
    try:
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=template_id,
                dimension=dimension
            ).first()

            if not result:
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'dimension': dimension,
                'content': result.content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取参考蓝本分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_template_dimension_reasoning(template_id, dimension):
    """
    获取参考蓝本特定维度的推理过程

    响应:
    {
        "success": 是否成功,
        "content": 推理内容
    }
    """
    try:
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=template_id,
                dimension=dimension
            ).first()

            if not result:
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'dimension': dimension,
                'content': result.reasoning_content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取参考蓝本推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
def get_chapter_dimension_analysis(template_id, chapter_id, dimension):
    """
    获取章节特定维度的分析结果

    响应:
    {
        "success": 是否成功,
        "content": 分析内容
    }
    """
    try:
        session = Session()
        try:
            # 获取章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节和维度的分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'dimension': dimension,
                'content': result.content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_chapter_dimension_reasoning(template_id, chapter_id, dimension):
    """
    获取章节特定维度的推理过程

    响应:
    {
        "success": 是否成功,
        "content": 推理内容
    }
    """
    try:
        session = Session()
        try:
            # 获取章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节和维度的分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'dimension': dimension,
                'content': result.reasoning_content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/analyze_dimension', methods=['POST'])
def analyze_novel_dimension(novel_id):
    """
    分析小说特定维度

    请求体:
    {
        "dimension": 维度名称,
        "force": 是否强制重新分析（可选，默认为0）
    }

    响应:
    {
        "success": 是否成功,
        "analysis_id": 分析任务ID,
        "message": 成功消息
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        dimension = data.get('dimension')
        if not dimension:
            return jsonify({
                'success': False,
                'error': '维度名称不能为空'
            }), 400

        force = data.get('force', 0)

        # 这里应该创建一个分析任务，但目前我们只返回一个模拟的分析ID
        analysis_id = f"{novel_id}_{dimension}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

        return jsonify({
            'success': True,
            'analysis_id': analysis_id,
            'message': f'成功创建分析任务，正在分析维度 {dimension}'
        }), 200
    except Exception as e:
        logger.error(f"创建分析任务时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
def get_novel_chapter_analysis(novel_id, chapter_id, dimension):
    """
    获取章节分析结果

    响应:
    {
        "success": 是否成功,
        "content": 分析结果内容
    }
    """
    try:
        logger.info(f"获取章节分析结果 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到章节分析结果 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'content': result.content
            }

            logger.info(f"成功获取章节分析结果 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_novel_chapter_reasoning(novel_id, chapter_id, dimension):
    """
    获取章节推理过程

    响应:
    {
        "success": 是否成功,
        "content": 推理过程内容
    }
    """
    try:
        logger.info(f"获取章节推理过程 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到章节分析结果 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'content': result.reasoning_content or "暂无推理过程"
            }

            logger.info(f"成功获取章节推理过程 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze_dimension', methods=['POST'])
def analyze_chapter_dimension(novel_id, chapter_id):
    """
    分析章节特定维度

    请求体:
    {
        "dimension": 维度名称,
        "force": 是否强制重新分析（可选，默认为0）
    }

    响应:
    {
        "success": 是否成功,
        "analysis_id": 分析任务ID,
        "message": 成功消息
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        dimension = data.get('dimension')
        if not dimension:
            return jsonify({
                'success': False,
                'error': '维度名称不能为空'
            }), 400

        force = data.get('force', 0)

        # 获取章节
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter or chapter.novel_id != novel_id:
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 导入章节分析服务
            from src.services.chapter_analysis_service import ChapterAnalysisService

            # 启动章节分析
            ChapterAnalysisService.analyze_chapter(
                chapter_id=chapter_id,
                dimension=dimension,
                use_cache=not bool(force)  # 如果force为真，则不使用缓存
            )

            # 生成分析ID
            analysis_id = f"{novel_id}_{chapter_id}_{dimension}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            return jsonify({
                'success': True,
                'analysis_id': analysis_id,
                'message': f'成功创建分析任务，正在分析章节维度 {dimension}'
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"创建章节分析任务时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze_all_dimensions', methods=['POST'])
def analyze_all_chapter_dimensions(novel_id, chapter_id):
    """
    分析章节所有维度

    请求体:
    {
        "dimensions": 维度列表（可选，默认为所有维度）,
        "force": 是否强制重新分析（可选，默认为0）
    }

    响应:
    {
        "success": 是否成功,
        "analysis_ids": 分析任务ID列表,
        "message": 成功消息
    }
    """
    try:
        data = request.get_json()
        if not data:
            data = {}

        dimensions = data.get('dimensions', [])
        force = data.get('force', 0)

        # 如果没有指定维度，则使用所有维度
        if not dimensions:
            from config import ANALYSIS_DIMENSIONS
            dimensions = [dim['key'] for dim in ANALYSIS_DIMENSIONS]

        # 这里应该创建多个分析任务，但目前我们只返回模拟的分析ID列表
        analysis_ids = []
        for dimension in dimensions:
            analysis_id = f"{novel_id}_{chapter_id}_{dimension}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            analysis_ids.append(analysis_id)

        return jsonify({
            'success': True,
            'analysis_ids': analysis_ids,
            'message': f'成功创建 {len(dimensions)} 个分析任务'
        }), 200
    except Exception as e:
        logger.error(f"创建章节所有维度分析任务时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/cancel_analysis', methods=['POST'])
def cancel_chapter_analysis(novel_id, chapter_id):
    """
    取消章节分析任务

    响应:
    {
        "success": 是否成功,
        "message": 成功消息
    }
    """
    try:
        # 这里应该取消分析任务，但目前我们只返回成功消息
        return jsonify({
            'success': True,
            'message': '成功取消分析任务'
        }), 200
    except Exception as e:
        logger.error(f"取消章节分析任务时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis_status', methods=['GET'])
def get_chapter_analysis_status(novel_id, chapter_id):
    """
    获取章节分析状态

    响应:
    {
        "success": 是否成功,
        "analyzed_dimensions": 已分析维度列表,
        "analyzing_dimensions": 正在分析维度列表
    }
    """
    try:
        session = Session()
        try:
            # 获取已分析的维度
            analyzed_dimensions = []
            results = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id
            ).all()

            for result in results:
                analyzed_dimensions.append(result.dimension)

            # 正在分析的维度（模拟数据）
            analyzing_dimensions = []

            return jsonify({
                'success': True,
                'analyzed_dimensions': analyzed_dimensions,
                'analyzing_dimensions': analyzing_dimensions
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析状态时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/analysis/<analysis_id>/status', methods=['GET'])
def get_analysis_status(analysis_id):
    """
    获取分析任务状态

    响应:
    {
        "success": 是否成功,
        "status": 任务状态,
        "progress": 进度百分比,
        "result_url": 结果URL（仅当状态为completed时）,
        "error": 错误信息（仅当状态为failed时）,
        "log_entries": 日志条目列表
    }
    """
    try:
        # 解析分析ID
        parts = analysis_id.split('_')
        if len(parts) < 3:
            return jsonify({
                'success': False,
                'error': '无效的分析ID'
            }), 400

        # 提取小说ID和维度
        novel_id = parts[0]
        dimension = parts[1]

        # 尝试将novel_id转换为整数
        try:
            novel_id_int = int(novel_id)
        except ValueError:
            novel_id_int = novel_id

        # 检查数据库中是否存在分析结果
        session = Session()
        try:
            # 检查是否为章节分析
            if len(parts) == 4:  # 章节分析格式: novel_id_chapter_id_dimension_timestamp
                chapter_id = parts[2]
                try:
                    chapter_id_int = int(chapter_id)
                except ValueError:
                    chapter_id_int = chapter_id

                # 查询章节分析结果
                result = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id_int,
                    chapter_id=chapter_id_int,
                    dimension=dimension
                ).first()

                if result:
                    # 分析已完成
                    status = 'completed'
                    progress = 100
                    result_url = f"/v3.1/novel/{novel_id}/chapter/{chapter_id}/dimension/{dimension}"
                    log_entries = [
                        {'level': 'info', 'message': '分析已完成'},
                        {'level': 'success', 'message': '分析结果已保存'}
                    ]
                else:
                    # 分析进行中或未开始
                    status = 'analyzing'
                    progress = 50
                    result_url = None
                    log_entries = [
                        {'level': 'info', 'message': '分析正在进行中...'}
                    ]
            else:  # 小说分析格式: novel_id_dimension_timestamp
                # 查询小说分析结果
                result = session.query(AnalysisResult).filter_by(
                    novel_id=novel_id_int,
                    dimension=dimension
                ).first()

                if result:
                    # 分析已完成
                    status = 'completed'
                    progress = 100
                    result_url = f"/v3.1/novel/{novel_id}/dimension/{dimension}"
                    log_entries = [
                        {'level': 'info', 'message': '分析已完成'},
                        {'level': 'success', 'message': '分析结果已保存'}
                    ]
                    logger.info(f"分析已完成: 小说ID={novel_id}, 维度={dimension}")
                else:
                    # 分析进行中或未开始
                    status = 'analyzing'
                    progress = 50
                    result_url = None
                    log_entries = [
                        {'level': 'info', 'message': '分析正在进行中...'}
                    ]
                    logger.info(f"分析进行中: 小说ID={novel_id}, 维度={dimension}")

            # 构建响应数据
            response_data = {
                'success': True,
                'status': status,
                'progress': progress,
                'log_entries': log_entries
            }

            # 如果状态为completed，添加结果URL
            if status == 'completed':
                response_data['result_url'] = result_url

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取分析任务状态时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/analysis/<analysis_id>/cancel', methods=['POST'])
def cancel_analysis(analysis_id):
    """
    取消分析任务

    响应:
    {
        "success": 是否成功,
        "message": 成功消息
    }
    """
    try:
        # 这里应该取消分析任务，但目前我们只返回成功消息
        return jsonify({
            'success': True,
            'message': '成功取消分析任务'
        }), 200
    except Exception as e:
        logger.error(f"取消分析任务时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500