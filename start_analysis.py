"""
启动九猫分析系统的脚本。
"""
import os
import sys
import logging
import time
import subprocess

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("开始启动九猫分析系统")
    
    # 检查环境
    logger.info("检查Python环境")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"当前工作目录: {os.getcwd()}")
    
    # 启动Flask应用
    logger.info("启动Flask应用")
    try:
        # 使用subprocess启动Flask应用
        flask_process = subprocess.Popen(
            ["python", "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        # 等待应用启动
        logger.info("等待应用启动...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if flask_process.poll() is None:
            logger.info("应用已成功启动")
            logger.info("请在浏览器中访问 http://localhost:5001")
            
            # 打印启动信息
            logger.info("九猫分析系统已启动，可以开始使用了")
            logger.info("按Ctrl+C停止应用")
            
            # 等待用户按Ctrl+C
            try:
                while True:
                    # 读取并打印输出
                    output = flask_process.stdout.readline()
                    if output:
                        print(output.strip())
                    
                    # 检查进程是否还在运行
                    if flask_process.poll() is not None:
                        break
                    
                    time.sleep(0.1)
            except KeyboardInterrupt:
                logger.info("收到停止信号，正在关闭应用...")
                flask_process.terminate()
                flask_process.wait()
                logger.info("应用已关闭")
        else:
            # 读取错误输出
            stdout, stderr = flask_process.communicate()
            logger.error(f"应用启动失败，退出码: {flask_process.returncode}")
            logger.error(f"标准输出: {stdout}")
            logger.error(f"错误输出: {stderr}")
    except Exception as e:
        logger.error(f"启动应用时出错: {str(e)}")
    
    logger.info("脚本执行完毕")

if __name__ == "__main__":
    main()
