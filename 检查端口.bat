@echo off
chcp 65001 > nul
echo ===================================
echo   九猫系统端口检查工具
echo ===================================
echo.

echo 正在检查端口5001是否被占用...
netstat -ano | findstr :5001
if %errorlevel% equ 0 (
    echo 端口5001被占用，以下是占用该端口的进程：
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5001') do (
        echo 进程ID: %%a
        tasklist | findstr %%a
    )
) else (
    echo 端口5001未被占用
)

echo.
echo 正在检查所有Python进程...
tasklist | findstr python
if %errorlevel% equ 0 (
    echo 以上是当前运行的Python进程
) else (
    echo 当前没有Python进程运行
)

echo.
pause
