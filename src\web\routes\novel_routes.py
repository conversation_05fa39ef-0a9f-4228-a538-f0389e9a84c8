from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.api_log import APILog
from src.models.analysis_process import AnalysisProcess
from src.db.connection import Session
from src.services.analysis_service import start_analysis_task, get_task_progress, cancel_analysis_task, sync_novel_analysis_to_chapters, aggregate_chapter_analyses
import json
import logging
import os

novel_bp = Blueprint('novel', __name__)
logger = logging.getLogger(__name__)

# 分析维度配置
ANALYSIS_DIMENSIONS = [
    {"key": "language_style", "name": "语言风格"},
    {"key": "rhythm_pacing", "name": "节奏与节奏"},
    {"key": "structure", "name": "结构分析"},
    {"key": "sentence_variation", "name": "句式变化"},
    {"key": "paragraph_length", "name": "段落长度"},
    {"key": "perspective_shifts", "name": "视角转换"},
    {"key": "paragraph_flow", "name": "段落流畅度"},
    {"key": "novel_characteristics", "name": "小说特点"},
    {"key": "world_building", "name": "世界构建"},
    {"key": "chapter_outline", "name": "章节大纲"},
    {"key": "character_relationships", "name": "人物关系"},
    {"key": "opening_effectiveness", "name": "开篇效果"},
    {"key": "climax_pacing", "name": "高潮节奏"}
]

# 临时禁用的维度（保留注释以便将来恢复）
# DISABLED_DIMENSIONS = [
#     {"key": "rhythm_pacing", "name": "节奏与节奏"},
#     {"key": "structure", "name": "结构分析"},
#     {"key": "world_building", "name": "世界构建"},
#     {"key": "novel_characteristics", "name": "小说特点"}
# ]

@novel_bp.route('/novel/list')
def novel_list():
    session = Session()
    novels = session.query(Novel).all()
    session.close()
    return render_template('novel_list.html', novels=novels)

@novel_bp.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    session = Session()
    novel = session.query(Novel).filter_by(id=novel_id).first()

    if not novel:
        session.close()
        abort(404)

    # 获取所有分析结果
    analysis_results = {}
    analysis_results_db = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()

    for result in analysis_results_db:
        # 安全地获取元数据
        try:
            if hasattr(result, 'analysis_metadata'):
                # 如果是字典，直接使用
                if isinstance(result.analysis_metadata, dict):
                    metadata = dict(result.analysis_metadata)
                # 如果是JSON字符串，尝试解析
                elif isinstance(result.analysis_metadata, str):
                    metadata = json.loads(result.analysis_metadata)
                else:
                    metadata = {}
            else:
                metadata = {}
        except Exception as e:
            logger.error(f"处理元数据时出错: {str(e)}")
            metadata = {}

        analysis_results[result.dimension] = {
            "dimension": result.dimension,
            "content": result.content,
            "metadata": metadata
        }

    # 将分析结果转换为JSON字符串，用于前端JavaScript
    analysis_results_json = json.dumps(analysis_results)

    # 获取已完成的维度数量
    completed_dimensions_count = len(analysis_results)

    # 获取总维度数量
    total_dimensions_count = len(ANALYSIS_DIMENSIONS)

    # 更新小说的分析状态
    if completed_dimensions_count == total_dimensions_count and not novel.is_analyzed:
        novel.is_analyzed = True
        session.commit()
    elif completed_dimensions_count < total_dimensions_count and novel.is_analyzed:
        novel.is_analyzed = False
        session.commit()

    session.close()

    # 检查是否使用修复版模板
    if 'fix' in request.args:
        # 加载修复版模板
        template = 'novel_4_fix.html' if novel_id == 4 else 'novel.html'

        # 提取维度键，用于可用维度列表
        available_dimensions = [dimension["key"] for dimension in ANALYSIS_DIMENSIONS]

        # 在模板中添加修复脚本
        return render_template(template,
                              novel=novel,
                              dimensions=ANALYSIS_DIMENSIONS,
                              available_dimensions=available_dimensions,
                              analysis_results=analysis_results,
                              analysis_results_json=analysis_results_json,
                              completed_dimensions_count=completed_dimensions_count,
                              total_dimensions_count=total_dimensions_count,
                              use_fix_scripts=True)  # 标记使用修复脚本

    # 提取维度键，用于可用维度列表
    available_dimensions = [dimension["key"] for dimension in ANALYSIS_DIMENSIONS]

    return render_template('novel.html',
                          novel=novel,
                          dimensions=ANALYSIS_DIMENSIONS,
                          available_dimensions=available_dimensions,
                          analysis_results=analysis_results,
                          analysis_results_json=analysis_results_json,
                          completed_dimensions_count=completed_dimensions_count,
                          total_dimensions_count=total_dimensions_count)

@novel_bp.route('/novel/<int:novel_id>/text')
def view_novel_text(novel_id):
    session = Session()
    novel = session.query(Novel).filter_by(id=novel_id).first()

    if not novel:
        session.close()
        abort(404)

    session.close()
    return render_template('novel_text.html', novel=novel)

@novel_bp.route('/novel/<int:novel_id>/analysis/<dimension>')
def view_analysis(novel_id, dimension):
    session = None
    try:
        # 记录请求信息
        logger.info(f"请求分析详情页面: novel_id={novel_id}, dimension={dimension}")

        # 获取数据库会话
        from src.db.connection import get_session
        session = get_session()

        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            logger.warning(f"找不到小说: novel_id={novel_id}")
            session.close()
            abort(404)

        # 获取分析结果
        analysis_result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()

        # 如果没有分析结果，记录警告
        if not analysis_result:
            logger.warning(f"找不到分析结果: novel_id={novel_id}, dimension={dimension}")

        # 获取维度名称
        dimension_name = next((d["name"] for d in ANALYSIS_DIMENSIONS if d["key"] == dimension), dimension)

        # 关闭会话
        session.close()
        session = None

        # 检查是否使用修复版模板
        use_fix = request.args.get('fix', 'true').lower() in ('true', '1', 'yes')

        # 先尝试使用专用模板
        template_path = f'analysis/{dimension}.html'
        template_folder = os.path.join(os.path.dirname(__file__), '../templates')

        # 准备模板参数
        template_params = {
            'novel': novel,
            'dimension': dimension,
            'dimension_name': dimension_name,
            'result': analysis_result,  # 注意：这里使用result而不是analysis_result，与基础模板保持一致
            'use_fix_scripts': use_fix  # 添加修复脚本标记
        }

        # 如果有专用模板，使用专用模板
        if os.path.exists(os.path.join(template_folder, template_path)):
            logger.info(f"使用专用模板: {template_path}")
            return render_template(template_path, **template_params)
        else:
            # 使用通用分析详情模板
            logger.info(f"使用通用分析详情模板")
            return render_template('analysis/base_analysis.html', **template_params)

    except Exception as e:
        # 记录错误并防止500错误页面
        logger.error(f"加载分析详情页面出错: {str(e)}", exc_info=True)

        # 确保会话被关闭
        if session:
            try:
                session.close()
            except Exception as close_error:
                logger.error(f"关闭会话时出错: {str(close_error)}")

        # 尝试重置数据库连接池
        try:
            from src.db.connection import dispose_engine
            dispose_engine()
            logger.info("已重置数据库连接池")
        except Exception as dispose_error:
            logger.error(f"重置数据库连接池时出错: {str(dispose_error)}")

        # 返回一个友好的错误页面，而不是500错误
        return render_template('error.html',
                              error_message="加载分析详情页面时出错",
                              error_details=str(e),
                              back_url=url_for('view_novel', novel_id=novel_id))

@novel_bp.route('/novel/new', methods=['GET', 'POST'])
def new_novel():
    if request.method == 'POST':
        title = request.form.get('title')
        author = request.form.get('author')
        content = request.form.get('content')

        if not title or not content:
            flash('标题和内容不能为空', 'danger')
            return render_template('novel_form.html')

        # 计算字数
        word_count = len(content)

        # 创建新小说
        session = Session()
        new_novel = Novel(title=title, author=author, content=content, word_count=word_count)
        session.add(new_novel)
        session.commit()
        novel_id = new_novel.id
        session.close()

        flash('小说创建成功', 'success')
        return redirect(url_for('view_novel', novel_id=novel_id))

    return render_template('novel_form.html')

@novel_bp.route('/novel/<int:novel_id>/edit', methods=['GET', 'POST'])
def edit_novel(novel_id):
    session = Session()
    novel = session.query(Novel).filter_by(id=novel_id).first()

    if not novel:
        session.close()
        abort(404)

    if request.method == 'POST':
        title = request.form.get('title')
        author = request.form.get('author')
        content = request.form.get('content')

        if not title or not content:
            flash('标题和内容不能为空', 'danger')
            session.close()
            return render_template('novel_form.html', novel=novel)

        # 更新小说信息
        novel.title = title
        novel.author = author
        novel.content = content
        novel.word_count = len(content)

        session.commit()
        session.close()

        flash('小说更新成功', 'success')
        return redirect(url_for('view_novel', novel_id=novel_id))

    session.close()
    return render_template('novel_form.html', novel=novel)

@novel_bp.route('/novel/<int:novel_id>/delete', methods=['POST'])
def delete_novel(novel_id):
    session = Session()
    novel = session.query(Novel).filter_by(id=novel_id).first()

    if not novel:
        session.close()
        abort(404)

    # 删除小说及其分析结果
    analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
    for result in analysis_results:
        session.delete(result)

    session.delete(novel)
    session.commit()
    session.close()

    flash('小说删除成功', 'success')
    return redirect(url_for('view_novel', novel_id=novel_id))

# API路由
@novel_bp.route('/api/analysis/start', methods=['POST'])
def api_start_analysis():
    data = request.json
    novel_id = data.get('novel_id')
    dimension = data.get('dimension')

    if not novel_id or not dimension:
        return jsonify({"success": False, "message": "缺少必要参数"})

    # 启动分析任务
    try:
        task_id = start_analysis_task(novel_id, dimension)
        return jsonify({"success": True, "task_id": task_id})
    except Exception as e:
        logger.error(f"启动分析任务失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)})

@novel_bp.route('/api/analysis/progress/<task_id>')
def api_analysis_progress(task_id):
    try:
        progress_data = get_task_progress(task_id)
        return jsonify({"success": True, **progress_data})
    except Exception as e:
        logger.error(f"获取分析进度失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)})

@novel_bp.route('/api/analysis/cancel/<task_id>', methods=['POST'])
def api_cancel_analysis(task_id):
    try:
        cancel_analysis_task(task_id)
        return jsonify({"success": True})
    except Exception as e:
        logger.error(f"取消分析任务失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)})

@novel_bp.route('/api/analysis/result/<int:novel_id>/<dimension>')
def api_analysis_result(novel_id, dimension):
    session = Session()

    try:
        # 获取分析结果
        analysis_result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()

        if not analysis_result:
            session.close()
            return jsonify({"success": False, "message": "未找到分析结果"})

        # 安全地获取元数据
        try:
            if hasattr(analysis_result, 'analysis_metadata'):
                # 如果是字典，直接使用
                if isinstance(analysis_result.analysis_metadata, dict):
                    metadata = dict(analysis_result.analysis_metadata)
                # 如果是JSON字符串，尝试解析
                elif isinstance(analysis_result.analysis_metadata, str):
                    metadata = json.loads(analysis_result.analysis_metadata)
                else:
                    metadata = {}
            else:
                metadata = {}
        except Exception as e:
            logger.error(f"处理元数据时出错: {str(e)}")
            metadata = {}

        # 构建结果数据
        result_data = {
            "dimension": analysis_result.dimension,
            "content": analysis_result.content,
            "metadata": metadata
        }

        # 从外部存储加载图表数据
        try:
            from src.utils.chart_storage import load_chart_data
            chart_data = load_chart_data(novel_id, dimension)
            if chart_data:
                result_data["chart_data"] = chart_data
            else:
                # 如果外部存储中没有图表数据，尝试从元数据中提取
                from src.utils.chart_storage import extract_chart_data_from_metadata
                chart_data = extract_chart_data_from_metadata(result_data["metadata"])
                if chart_data:
                    result_data["chart_data"] = chart_data
                    # 保存到外部存储
                    from src.utils.chart_storage import save_chart_data
                    save_chart_data(novel_id, dimension, chart_data)
        except ImportError:
            logger.warning(f"图表数据外部存储模块未找到，无法加载图表数据")
        except Exception as e:
            logger.error(f"加载图表数据时出错: {str(e)}")

        session.close()
        return jsonify({"success": True, "result": result_data})

    except Exception as e:
        session.close()
        logger.error(f"获取分析结果失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)})

@novel_bp.route('/api/analysis/process')
def api_analysis_process():
    """获取分析过程数据的API端点"""
    novel_id = request.args.get('novel_id')
    dimension = request.args.get('dimension')

    if not novel_id or not dimension:
        return jsonify({"success": False, "message": "缺少必要的参数"})

    try:
        session = Session()

        # 先尝试从分析结果中获取过程数据
        analysis_result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()

        if analysis_result:
            # 安全地获取元数据
            try:
                if hasattr(analysis_result, 'analysis_metadata'):
                    # 如果是字典，直接使用
                    if isinstance(analysis_result.analysis_metadata, dict):
                        metadata = dict(analysis_result.analysis_metadata)
                    # 如果是JSON字符串，尝试解析
                    elif isinstance(analysis_result.analysis_metadata, str):
                        metadata = json.loads(analysis_result.analysis_metadata)
                    else:
                        metadata = {}
                else:
                    metadata = {}
            except Exception as e:
                logger.error(f"处理元数据时出错: {str(e)}")
                metadata = {}

            # 检查元数据中是否包含reasoning_content字段
            if 'reasoning_content' in metadata:
                reasoning_content = metadata['reasoning_content']
                return jsonify({"success": True, "process": reasoning_content})

            # 检查元数据中是否包含process字段
            if 'process' in metadata:
                process_data = metadata['process']
                return jsonify({"success": True, "process": process_data})

            # 检查是否包含process_log字段
            if 'process_log' in metadata:
                process_log = metadata['process_log']
                return jsonify({"success": True, "process": process_log})

            # 检查是否包含analysis_steps字段
            if 'analysis_steps' in metadata:
                analysis_steps = metadata['analysis_steps']
                return jsonify({"success": True, "process": analysis_steps})

            # 检查是否包含steps字段
            if 'steps' in metadata:
                steps = metadata['steps']
                return jsonify({"success": True, "process": steps})

            # 检查是否包含debug_info字段
            if 'debug_info' in metadata:
                debug_info = metadata['debug_info']
                return jsonify({"success": True, "process": debug_info})

            # 尝试从logs字段构建过程信息
            if 'logs' in metadata and isinstance(metadata['logs'], list):
                logs = metadata['logs']
                return jsonify({"success": True, "process": logs})

            # 如果没有专门的过程数据，提取所有可能与过程相关的元数据
            process_related_data = {}
            for key, value in metadata.items():
                if any(k in key.lower() for k in ['process', 'step', 'log', 'debug', 'time', 'duration', 'progress']):
                    process_related_data[key] = value

            if process_related_data:
                return jsonify({"success": True, "process": process_related_data})

            # 如果找不到任何过程相关数据，返回原始元数据作为后备
            return jsonify({
                "success": True,
                "process": "未找到分析过程数据，显示全部元数据代替。\n\n" + json.dumps(metadata, indent=2, ensure_ascii=False)
            })

        # 如果没有找到分析结果，返回错误信息
        return jsonify({"success": False, "message": "未找到指定的分析结果"})

    except Exception as e:
        logger.error(f"获取分析过程数据时出错: {str(e)}")
        return jsonify({"success": False, "message": f"获取分析过程数据时出错: {str(e)}"})

    finally:
        session.close()

@novel_bp.route('/api/novels/<int:novel_id>/sync_to_chapters', methods=['POST'])
def api_sync_novel_analysis_to_chapters(novel_id):
    """
    将整本书的分析结果同步到各个章节。

    Args:
        novel_id: 小说ID
    """
    try:
        # 获取请求参数
        data = request.json or {}
        dimension = data.get('dimension')  # 可选参数，如果不提供则同步所有维度

        # 调用同步函数
        result = sync_novel_analysis_to_chapters(novel_id, dimension)

        return jsonify(result)
    except Exception as e:
        logger.error(f"同步分析结果到章节时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"同步分析结果到章节时出错: {str(e)}",
            "novel_id": novel_id
        })

@novel_bp.route('/api/novels/<int:novel_id>/aggregate_chapters', methods=['POST'])
def api_aggregate_chapter_analyses(novel_id):
    """
    将所有章节的分析结果汇总，创建一个整本书的汇总分析结果。

    Args:
        novel_id: 小说ID
    """
    try:
        # 获取请求参数
        data = request.json or {}
        dimension = data.get('dimension')  # 可选参数，如果不提供则汇总所有维度

        # 调用汇总函数
        result = aggregate_chapter_analyses(novel_id, dimension)

        return jsonify(result)
    except Exception as e:
        logger.error(f"汇总章节分析结果时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"汇总章节分析结果时出错: {str(e)}",
            "novel_id": novel_id
        })

@novel_bp.route('/api/novels/<int:novel_id>/analysis/<dimension>')
def api_get_analysis(novel_id, dimension):
    """
    获取小说特定维度的分析结果。

    这个API路由用于获取完整的分析结果，包括内容和元数据。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            return jsonify({
                "success": False,
                "error": f"找不到ID为{novel_id}的小说"
            }), 404

        # 获取分析结果
        analysis_result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()

        if not analysis_result:
            # 返回404，但使用更友好的错误消息
            return jsonify({
                "success": False,
                "error": f"找不到维度为{dimension}的分析结果",
                "message": "该维度的分析结果尚未生成或已被删除，请先进行分析。",
                "dimension": dimension,
                "novel_id": novel_id
            }), 404

        # 安全地获取元数据
        try:
            if hasattr(analysis_result, 'analysis_metadata'):
                # 如果是字典，直接使用
                if isinstance(analysis_result.analysis_metadata, dict):
                    metadata = dict(analysis_result.analysis_metadata)
                # 如果是JSON字符串，尝试解析
                elif isinstance(analysis_result.analysis_metadata, str):
                    metadata = json.loads(analysis_result.analysis_metadata)
                else:
                    metadata = {}
            else:
                metadata = {}
        except Exception as e:
            logger.error(f"处理元数据时出错: {str(e)}")
            metadata = {}

        # 构建结果数据
        result_data = {
            "success": True,
            "dimension": analysis_result.dimension,
            "content": analysis_result.content,
            "metadata": metadata
        }

        # 检查是否有reasoning_content
        if hasattr(analysis_result, 'reasoning_content') and analysis_result.reasoning_content:
            result_data["reasoning_content"] = analysis_result.reasoning_content
        elif metadata and 'reasoning_content' in metadata:
            result_data["reasoning_content"] = metadata['reasoning_content']

        # 从外部存储加载图表数据
        try:
            from src.utils.chart_storage import load_chart_data
            chart_data = load_chart_data(novel_id, dimension)
            if chart_data:
                result_data["chart_data"] = chart_data
            else:
                # 如果外部存储中没有图表数据，尝试从元数据中提取
                from src.utils.chart_storage import extract_chart_data_from_metadata
                chart_data = extract_chart_data_from_metadata(metadata)
                if chart_data:
                    result_data["chart_data"] = chart_data
                    # 保存到外部存储
                    from src.utils.chart_storage import save_chart_data
                    save_chart_data(novel_id, dimension, chart_data)
        except ImportError:
            logger.warning(f"图表数据外部存储模块未找到，无法加载图表数据")
        except Exception as e:
            logger.error(f"加载图表数据时出错: {str(e)}")

        return jsonify(result_data)

    except Exception as e:
        logger.error(f"获取分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@novel_bp.route('/api/novels/<int:novel_id>/analysis/<dimension>/reasoning_content')
def api_get_reasoning_content(novel_id, dimension):
    """
    直接获取分析结果中的reasoning_content字段。

    这是一个简化的API，专门用于获取分析过程内容。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 检查是否需要完整的原始推理过程
        full_process = request.args.get('full', 'false').lower() in ('true', '1', 'yes', 't')
        logger.info(f"请求推理过程API: novel_id={novel_id}, dimension={dimension}, full={full_process}")

        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            return jsonify({
                "success": False,
                "error": f"找不到ID为{novel_id}的小说"
            }), 404

        # 获取分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()

        if not result:
            # 返回404，但使用更友好的错误消息
            return jsonify({
                "success": False,
                "error": f"找不到维度为{dimension}的分析结果",
                "message": "该维度的分析结果尚未生成或已被删除，请先进行分析。",
                "dimension": dimension,
                "novel_id": novel_id
            }), 404

        # 创建一个统一的获取推理过程内容的流程，优先返回完整内容

        # 1. 首先检查AnalysisResult对象的reasoning_content字段
        if hasattr(result, 'reasoning_content') and result.reasoning_content:
            logger.info(f"从AnalysisResult对象的reasoning_content字段获取到推理过程，长度: {len(result.reasoning_content)}")
            return jsonify({
                "success": True,
                "reasoning_content": result.reasoning_content,
                "source": "result_field",
                "dimension": dimension,
                "novel_id": novel_id,
                "is_full": True
            })

        # 2. 检查元数据中的reasoning_content字段
        if hasattr(result, 'analysis_metadata') and result.analysis_metadata:
            try:
                metadata = result.analysis_metadata
                # 如果是字典类型
                if isinstance(metadata, dict) and 'reasoning_content' in metadata:
                    content = metadata['reasoning_content']
                    logger.info(f"从analysis_metadata中获取到推理过程，长度: {len(content)}")
                    return jsonify({
                        "success": True,
                        "reasoning_content": content,
                        "source": "metadata_field",
                        "dimension": dimension,
                        "novel_id": novel_id,
                        "is_full": True
                    })
                # 检查嵌套的response结构
                elif isinstance(metadata, dict) and 'response' in metadata and isinstance(metadata['response'], dict) and 'reasoning_content' in metadata['response']:
                    content = metadata['response']['reasoning_content']
                    logger.info(f"从analysis_metadata.response中获取到推理过程，长度: {len(content)}")
                    return jsonify({
                        "success": True,
                        "reasoning_content": content,
                        "source": "metadata_response_field",
                        "dimension": dimension,
                        "novel_id": novel_id,
                        "is_full": True
                    })
            except Exception as e:
                logger.error(f"从元数据获取推理过程时出错: {str(e)}")
                # 继续尝试其他方法

        # 3. 从分析过程记录中获取推理过程
        process_records = session.query(AnalysisProcess).filter_by(
            novel_id=novel_id, dimension=dimension
        ).order_by(AnalysisProcess.created_at.desc()).limit(5).all()

        if process_records:
            for process in process_records:
                if process.stage == 'init' and process.input_text:
                    logger.info(f"从分析过程记录获取推理过程，长度: {len(process.input_text)}")
                    return jsonify({
                        "success": True,
                        "reasoning_content": process.input_text,
                        "source": "analysis_process_direct",
                        "dimension": dimension,
                        "novel_id": novel_id,
                        "is_full": True
                    })

        # 4. 从API日志中提取推理过程
        api_logs = session.query(APILog).filter_by(
            novel_id=novel_id, dimension=dimension
        ).order_by(APILog.created_at.desc()).limit(5).all()

        for log in api_logs:
            if hasattr(log, 'response_data') and log.response_data:
                try:
                    # 尝试解析响应数据
                    response_data = None
                    if isinstance(log.response_data, str):
                        response_data = json.loads(log.response_data)
                    elif isinstance(log.response_data, dict):
                        response_data = log.response_data

                    if response_data and 'reasoning_content' in response_data:
                        content = response_data['reasoning_content']
                        logger.info(f"从API日志中提取到推理过程，长度: {len(content)}")
                        return jsonify({
                            "success": True,
                            "reasoning_content": content,
                            "source": "api_log",
                            "dimension": dimension,
                            "novel_id": novel_id,
                            "is_full": True
                        })
                except Exception as e:
                    logger.error(f"解析API日志时出错: {str(e)}")
                    continue

        # 5. 尝试从content字段中提取推理过程特征
        if hasattr(result, 'content') and result.content:
            content = result.content

            # 检查是否包含常见的推理过程特征
            common_markers = [
                '分析过程', '我将分析', '我需要分析', '我将从以下几个方面',
                '首先，我需要', '嗯，用户让我', '好的，我现在要', '下面我来分析',
                '让我来分析', '我会按照以下步骤', '我将按照以下步骤', '作为文学分析专家'
            ]

            # 维度特定的标记，使用简化版本
            dimension_markers = {
                'language_style': ['分析语言风格'],
                'rhythm_pacing': ['分析节奏与节奏'],
                'structure': ['分析结构'],
                'sentence_variation': ['分析句式变化'],
                'paragraph_length': ['分析段落长度'],
                'perspective_shifts': ['分析视角转换'],
                'paragraph_flow': ['分析段落流畅度'],
                'novel_characteristics': ['分析小说特点'],
                'world_building': ['分析世界构建'],
                'chapter_outline': ['分析章节大纲'],
                'character_relationships': ['分析人物关系'],
                'opening_effectiveness': ['分析开篇效果'],
                'climax_pacing': ['分析高潮节奏']
            }

            # 合并标记
            all_markers = common_markers
            if dimension in dimension_markers:
                all_markers = dimension_markers[dimension] + all_markers

            for marker in all_markers:
                if marker in content:
                    logger.info(f"在content中找到推理过程标记: {marker}")
                    return jsonify({
                        "success": True,
                        "reasoning_content": content,
                        "source": "content_field_with_marker",
                        "dimension": dimension,
                        "novel_id": novel_id,
                        "is_full": True
                    })

            # 如果强制要求完整内容，且其他方法都失败，直接返回content
            if full_process:
                logger.info("未找到推理过程标记，但由于设置了full=true，直接返回content")
                return jsonify({
                    "success": True,
                    "reasoning_content": content,
                    "source": "content_field_forced",
                    "dimension": dimension,
                    "novel_id": novel_id,
                    "is_full": True
                })

        # 6. 如果所有尝试都失败，但有元数据
        if hasattr(result, 'analysis_metadata') and result.analysis_metadata and full_process:
            try:
                # 构建一个包含所有元数据的字符串，作为后备
                metadata_str = json.dumps(result.analysis_metadata, ensure_ascii=False, indent=2)
                logger.info("未找到推理过程，但由于设置了full=true，返回全部元数据")
                return jsonify({
                    "success": True,
                    "reasoning_content": f"未找到推理过程，显示全部元数据作为参考：\n\n{metadata_str}",
                    "source": "metadata_dump",
                    "dimension": dimension,
                    "novel_id": novel_id,
                    "is_full": True
                })
            except Exception as e:
                logger.error(f"序列化元数据时出错: {str(e)}")

        # 如果所有方法都失败，返回友好的错误信息
        return jsonify({
            "success": False,
            "error": "未找到推理过程",
            "message": "无法找到该维度的推理过程，可能是因为该分析是在启用推理过程记录功能之前进行的。",
            "dimension": dimension,
            "novel_id": novel_id
        }), 404

    except Exception as e:
        logger.error(f"获取reasoning_content时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取推理过程时发生系统错误，请联系管理员。"
        }), 500
    finally:
        session.close()

@novel_bp.route('/api/novels/<int:novel_id>/analysis/all')
def api_get_all_analysis_results(novel_id):
    session = None
    try:
        from src.db.connection import get_session
        session = get_session()
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            logger.warning(f"API请求所有分析结果：找不到小说 novel_id={novel_id}")
            session.close()
            abort(404, description="Novel not found")

        analysis_results_db = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()

        results_list = []
        for result_db_item in analysis_results_db:
            # 与 view_novel 中构建 analysis_results 类似地处理元数据
            metadata = {}
            try:
                if hasattr(result_db_item, 'analysis_metadata'):
                    if isinstance(result_db_item.analysis_metadata, dict):
                        metadata = dict(result_db_item.analysis_metadata)
                    elif isinstance(result_db_item.analysis_metadata, str) and result_db_item.analysis_metadata:
                        metadata = json.loads(result_db_item.analysis_metadata)
            except Exception as e:
                logger.error(f"API获取所有分析结果时处理元数据出错 for novel_id={novel_id}, dimension={result_db_item.dimension}: {str(e)}")
                # 保留空的 metadata 或记录一个错误标记

            results_list.append({
                "dimension": result_db_item.dimension,
                "content": result_db_item.content,
                "status": result_db_item.status, # 假设AnalysisResult有status字段
                "progress": result_db_item.progress if hasattr(result_db_item, 'progress') else 100, # 假设有progress
                "summary": result_db_item.summary if hasattr(result_db_item, 'summary') else None, # 假设有summary
                "error_message": result_db_item.error_message if hasattr(result_db_item, 'error_message') else None,
                "created_at": result_db_item.created_at.isoformat() if result_db_item.created_at else None,
                "updated_at": result_db_item.updated_at.isoformat() if result_db_item.updated_at else None,
                "analysis_metadata": metadata, # 使用处理过的元数据
                 # 可以根据前端实际需要添加更多字段
            })

        session.close()
        return jsonify(results_list)

    except Exception as e:
        logger.error(f"API获取所有分析结果时出错 for novel_id={novel_id}: {str(e)}", exc_info=True)
        if session:
            try:
                session.close()
            except Exception as close_error:
                logger.error(f"API获取所有分析结果时关闭会话出错: {str(close_error)}")
        # 对于API，通常返回JSON格式的错误
        return jsonify({"error": "Failed to retrieve all analysis results", "details": str(e)}), 500
