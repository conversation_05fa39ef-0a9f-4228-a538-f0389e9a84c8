<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js 加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chart-container {
            width: 100%;
            height: 300px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .log-container {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Chart.js 加载测试</h1>
    
    <div id="status" class="status warning">正在检查 Chart.js 加载状态...</div>
    
    <div class="chart-container">
        <canvas id="myChart"></canvas>
    </div>
    
    <h2>加载日志</h2>
    <div id="log" class="log-container"></div>
    
    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('log');
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'warning' ? 'orange' : 'black';
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console[type](message);
        }
        
        // 更新状态
        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = 'status ' + type;
        }
        
        // 原始控制台方法
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        // 重写控制台方法以捕获日志
        console.log = function(message) {
            log(message);
            originalConsole.log.apply(console, arguments);
        };
        
        console.warn = function(message) {
            log(message, 'warning');
            originalConsole.warn.apply(console, arguments);
        };
        
        console.error = function(message) {
            log(message, 'error');
            originalConsole.error.apply(console, arguments);
        };
        
        // Chart.js 内联加载器
        (function() {
            log('Chart.js内联加载器已执行');
            
            // Chart.js路径列表
            const chartJsPaths = [
                '/static/js/chart.min.js',
                '/static/js/lib/chart.min.js',
                '/direct-static/js/chart.min.js',
                '/direct-static/js/lib/chart.min.js',
                'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js'
            ];
            
            // 检查Chart.js是否已加载
            if (typeof Chart !== 'undefined') {
                log('Chart.js已加载，跳过内联加载');
                initializeChart();
                return;
            }
            
            // 尝试加载Chart.js
            function tryLoadChart(index) {
                if (index >= chartJsPaths.length) {
                    log('所有Chart.js路径都加载失败', 'error');
                    updateStatus('Chart.js 加载失败，请检查网络连接或路径配置', 'error');
                    return;
                }
                
                const path = chartJsPaths[index];
                log(`尝试从 ${path} 加载Chart.js`);
                
                const script = document.createElement('script');
                script.src = path;
                
                script.onload = function() {
                    log(`成功从 ${path} 加载Chart.js`);
                    updateStatus('Chart.js 加载成功', 'success');
                    initializeChart();
                };
                
                script.onerror = function() {
                    log(`从 ${path} 加载Chart.js失败，尝试下一个路径`, 'warning');
                    tryLoadChart(index + 1);
                };
                
                document.head.appendChild(script);
            }
            
            // 开始尝试加载
            tryLoadChart(0);
        })();
        
        // 初始化图表
        function initializeChart() {
            if (typeof Chart === 'undefined') {
                log('Chart.js 未加载，无法初始化图表', 'error');
                return;
            }
            
            log('开始初始化图表');
            
            try {
                const ctx = document.getElementById('myChart').getContext('2d');
                const myChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['红色', '蓝色', '黄色', '绿色', '紫色', '橙色'],
                        datasets: [{
                            label: '测试数据',
                            data: [12, 19, 3, 5, 2, 3],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.2)',
                                'rgba(54, 162, 235, 0.2)',
                                'rgba(255, 206, 86, 0.2)',
                                'rgba(75, 192, 192, 0.2)',
                                'rgba(153, 102, 255, 0.2)',
                                'rgba(255, 159, 64, 0.2)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)',
                                'rgba(153, 102, 255, 1)',
                                'rgba(255, 159, 64, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                
                log('图表初始化成功');
                updateStatus('图表初始化成功', 'success');
            } catch (error) {
                log(`图表初始化失败: ${error.message}`, 'error');
                updateStatus('图表初始化失败', 'error');
            }
        }
    </script>
</body>
</html>
