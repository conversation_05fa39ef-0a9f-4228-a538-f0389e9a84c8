{% extends "v2/base.html" %}

{% block title %}帮助中心 - 九猫小说分析系统v2.0{% endblock %}

{% block extra_css %}
<style>
    .help-header {
        background-color: var(--primary-light);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    .help-section {
        margin-bottom: 2rem;
    }
    .help-section h3 {
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
    }
    .faq-item {
        margin-bottom: 1.5rem;
    }
    .faq-question {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--primary-dark);
    }
    .nav-pills .nav-link {
        color: var(--text-color);
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    .nav-pills .nav-link.active {
        background-color: var(--primary-color);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- 帮助中心标题 -->
<div class="help-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">帮助中心</h1>
            <p class="mb-0">九猫小说分析系统v2.0使用指南和常见问题解答</p>
        </div>
        <div class="col-md-4 text-md-end">
            <i class="fas fa-question-circle fa-3x text-primary"></i>
        </div>
    </div>
</div>

<div class="row">
    <!-- 左侧导航 -->
    <div class="col-lg-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">目录</h3>
            </div>
            <div class="card-body p-0">
                <nav class="nav flex-column nav-pills p-3">
                    <a class="nav-link active" href="#getting-started">入门指南</a>
                    <a class="nav-link" href="#upload-guide">上传小说</a>
                    <a class="nav-link" href="#analysis-guide">分析功能</a>
                    <a class="nav-link" href="#chapter-guide">章节分析</a>
                    <a class="nav-link" href="#faq">常见问题</a>
                    <a class="nav-link" href="#troubleshooting">故障排除</a>
                </nav>
            </div>
        </div>
    </div>

    <!-- 右侧内容 -->
    <div class="col-lg-9">
        <!-- 入门指南 -->
        <div class="help-section" id="getting-started">
            <h3><i class="fas fa-rocket me-2"></i>入门指南</h3>
            <p>欢迎使用九猫小说分析系统v2.0！本系统是一款专为作家和编辑设计的智能小说分析工具，基于先进的AI技术，提供全方位的文本分析服务。</p>
            
            <h4 class="mt-4">系统特点</h4>
            <ul>
                <li><strong>多维度分析</strong> - 提供13个专业维度的深度分析，全方位评估小说质量</li>
                <li><strong>AI驱动</strong> - 采用先进的AI模型，提供专业水准的文学分析</li>
                <li><strong>高效处理</strong> - 优化的处理流程，快速分析长篇小说文本</li>
                <li><strong>章节分析</strong> - 支持逐章节分析，深入了解每个章节的特点</li>
                <li><strong>分析汇总</strong> - 提供章节分析汇总功能，全面把握小说整体特点</li>
            </ul>
            
            <h4 class="mt-4">基本流程</h4>
            <ol>
                <li>上传小说文本（TXT文件或直接粘贴）</li>
                <li>选择需要分析的维度</li>
                <li>等待系统完成分析</li>
                <li>查看分析结果和推理过程</li>
                <li>查看章节分析和汇总结果</li>
            </ol>
        </div>
        
        <!-- 上传小说 -->
        <div class="help-section" id="upload-guide">
            <h3><i class="fas fa-upload me-2"></i>上传小说</h3>
            <p>九猫系统支持两种方式上传小说：文件上传和文本粘贴。</p>
            
            <h4 class="mt-4">文件上传</h4>
            <ol>
                <li>点击导航栏的"上传分析"按钮</li>
                <li>填写小说标题和作者信息</li>
                <li>选择"上传文件"选项</li>
                <li>点击"选择文件"按钮，选择TXT格式的小说文件</li>
                <li>选择需要分析的维度</li>
                <li>点击"上传并分析"按钮</li>
            </ol>
            
            <h4 class="mt-4">文本粘贴</h4>
            <ol>
                <li>点击导航栏的"上传分析"按钮</li>
                <li>填写小说标题和作者信息</li>
                <li>选择"粘贴文本"选项</li>
                <li>在文本框中粘贴小说内容</li>
                <li>选择需要分析的维度</li>
                <li>点击"上传并分析"按钮</li>
            </ol>
            
            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>提示：</strong> 系统支持的最大文件大小为50MB，如果您的小说超过此大小，建议分割后上传。
            </div>
        </div>
        
        <!-- 分析功能 -->
        <div class="help-section" id="analysis-guide">
            <h3><i class="fas fa-chart-bar me-2"></i>分析功能</h3>
            <p>九猫系统提供13个专业维度的小说分析功能，每个维度都从不同角度评估小说的质量和特点。</p>
            
            <h4 class="mt-4">分析维度</h4>
            <p>系统提供的13个分析维度包括：</p>
            <div class="row">
                <div class="col-md-6">
                    <ul>
                        <li>语言风格</li>
                        <li>节奏节拍</li>
                        <li>结构分析</li>
                        <li>句式变化</li>
                        <li>段落长度</li>
                        <li>视角转换</li>
                        <li>段落流畅度</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul>
                        <li>小说特点</li>
                        <li>世界构建</li>
                        <li>章节大纲</li>
                        <li>人物关系</li>
                        <li>开篇效果</li>
                        <li>高潮节奏</li>
                    </ul>
                </div>
            </div>
            
            <h4 class="mt-4">查看分析结果</h4>
            <ol>
                <li>在小说详情页面，点击对应维度的卡片</li>
                <li>系统会显示该维度的分析结果</li>
                <li>点击"分析结果"选项卡查看详细分析</li>
                <li>点击"推理过程"选项卡查看AI的分析思路</li>
            </ol>
            
            <h4 class="mt-4">重新分析</h4>
            <ol>
                <li>在分析结果页面，点击"重新分析"按钮</li>
                <li>在确认对话框中点击"确认重新分析"</li>
                <li>系统会重新进行分析，并更新结果</li>
            </ol>
        </div>
        
        <!-- 章节分析 -->
        <div class="help-section" id="chapter-guide">
            <h3><i class="fas fa-book-open me-2"></i>章节分析</h3>
            <p>九猫系统支持对小说的每个章节进行单独分析，并提供章节分析汇总功能。</p>
            
            <h4 class="mt-4">查看章节</h4>
            <ol>
                <li>在小说详情页面，点击"章节列表"选项卡</li>
                <li>系统会显示小说的所有章节</li>
                <li>点击章节标题或"查看详情"按钮进入章节详情页</li>
            </ol>
            
            <h4 class="mt-4">分析章节</h4>
            <ol>
                <li>在章节详情页面，点击需要分析的维度卡片</li>
                <li>或点击"分析所有维度"按钮一次性分析所有维度</li>
                <li>等待系统完成分析</li>
                <li>查看分析结果和推理过程</li>
            </ol>
            
            <h4 class="mt-4">章节分析汇总</h4>
            <ol>
                <li>在小说详情页面，点击"章节分析汇总"按钮</li>
                <li>系统会显示所有章节的分析结果汇总</li>
                <li>点击"汇总分析结果"按钮，选择需要汇总的维度</li>
                <li>系统会将所有章节的分析结果汇总为整本书的分析结果</li>
            </ol>
        </div>
        
        <!-- 常见问题 -->
        <div class="help-section" id="faq">
            <h3><i class="fas fa-question-circle me-2"></i>常见问题</h3>
            
            <div class="faq-item">
                <p class="faq-question">Q: 系统支持哪些格式的小说文件？</p>
                <p>A: 目前系统仅支持TXT格式的文本文件。如果您的小说是其他格式，请先转换为TXT格式再上传。</p>
            </div>
            
            <div class="faq-item">
                <p class="faq-question">Q: 分析需要多长时间？</p>
                <p>A: 分析时间取决于小说的长度和选择的维度数量。一般来说，一本10万字的小说分析所有维度需要5-10分钟。</p>
            </div>
            
            <div class="faq-item">
                <p class="faq-question">Q: 如何查看分析推理过程？</p>
                <p>A: 在分析结果页面，点击"推理过程"选项卡即可查看AI的分析思路和推理过程。</p>
            </div>
            
            <div class="faq-item">
                <p class="faq-question">Q: 系统如何分割章节？</p>
                <p>A: 系统会自动识别常见的章节标记，如"第X章"、"第X回"、"Chapter X"等。如果自动分割不准确，您可以手动调整。</p>
            </div>
            
            <div class="faq-item">
                <p class="faq-question">Q: 分析结果会保存多久？</p>
                <p>A: 分析结果会永久保存在系统中，除非您手动删除。</p>
            </div>
        </div>
        
        <!-- 故障排除 -->
        <div class="help-section" id="troubleshooting">
            <h3><i class="fas fa-wrench me-2"></i>故障排除</h3>
            
            <div class="faq-item">
                <p class="faq-question">问题: 上传文件失败</p>
                <p>解决方案:</p>
                <ul>
                    <li>确保文件格式为TXT</li>
                    <li>确保文件大小不超过50MB</li>
                    <li>检查文件编码，建议使用UTF-8编码</li>
                    <li>尝试将文本分割成多个小文件上传</li>
                </ul>
            </div>
            
            <div class="faq-item">
                <p class="faq-question">问题: 分析过程中断或失败</p>
                <p>解决方案:</p>
                <ul>
                    <li>检查网络连接是否稳定</li>
                    <li>尝试刷新页面后重新开始分析</li>
                    <li>如果是特定维度失败，可以尝试单独分析该维度</li>
                    <li>对于特别长的小说，尝试分章节分析</li>
                </ul>
            </div>
            
            <div class="faq-item">
                <p class="faq-question">问题: 章节分割不准确</p>
                <p>解决方案:</p>
                <ul>
                    <li>检查小说中的章节标记是否规范</li>
                    <li>尝试手动编辑章节内容</li>
                    <li>如果章节标记非常特殊，可以联系管理员添加支持</li>
                </ul>
            </div>
            
            <div class="faq-item">
                <p class="faq-question">问题: 页面加载缓慢</p>
                <p>解决方案:</p>
                <ul>
                    <li>检查网络连接速度</li>
                    <li>清除浏览器缓存</li>
                    <li>关闭其他占用资源的程序</li>
                    <li>尝试使用不同的浏览器</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 平滑滚动到锚点
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 20,
                        behavior: 'smooth'
                    });
                    
                    // 更新活动链接
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('active');
                    });
                    this.classList.add('active');
                }
            });
        });
        
        // 滚动时更新活动链接
        window.addEventListener('scroll', function() {
            const scrollPosition = window.scrollY;
            
            document.querySelectorAll('.help-section').forEach(section => {
                if (section.offsetTop - 100 <= scrollPosition && 
                    section.offsetTop + section.offsetHeight > scrollPosition) {
                    
                    const id = section.getAttribute('id');
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('active');
                        
                        if (link.getAttribute('href') === `#${id}`) {
                            link.classList.add('active');
                        }
                    });
                }
            });
        });
    });
</script>
{% endblock %}
