/**
 * 九猫 - jQuery选择器修复脚本
 * 用于修复jQuery选择器错误
 * 版本: 1.0.0
 */

(function() {
    console.log('[jQuery选择器修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,                 // 是否启用调试模式
        patchSelectors: true,        // 是否修复选择器
        patchQuerySelectorAll: true  // 是否修复querySelectorAll
    };

    // 状态
    const STATE = {
        initialized: false,          // 是否已初始化
        originalQuerySelectorAll: null, // 原始querySelectorAll函数
        fixedSelectors: new Set()    // 已修复的选择器
    };

    // 安全日志函数
    function safeLog(message, level = 'info') {
        if (CONFIG.debug) {
            const prefix = '[jQuery选择器修复]';
            try {
                switch (level) {
                    case 'error':
                        console.error(`${prefix} ${message}`);
                        break;
                    case 'warn':
                        console.warn(`${prefix} ${message}`);
                        break;
                    default:
                        console.log(`${prefix} ${message}`);
                }
            } catch (e) {
                // 忽略日志错误
            }
        }
    }

    // 修复选择器
    function fixSelector(selector) {
        if (!selector || typeof selector !== 'string') {
            return selector;
        }

        // 已知的问题选择器及其修复
        const problematicSelectors = {
            '\\': '*',
            '*,:x': '*',
            '\\\\': '*',
            '\\\\\\': '*',
            '\\\\\\\\': '*',
            '': '*' // 空选择器也修复为*
        };

        if (selector in problematicSelectors) {
            const fixedSelector = problematicSelectors[selector];
            safeLog(`修复选择器: "${selector}" -> "${fixedSelector}"`);
            STATE.fixedSelectors.add(selector);
            return fixedSelector;
        }

        // 检查并修复包含反斜杠的选择器
        if (selector.includes('\\')) {
            // 将反斜杠替换为*而不是空字符串，确保选择器始终有效
            const fixedSelector = selector.replace(/\\/g, '*');
            if (fixedSelector === '') {
                // 如果替换后为空，则使用*选择器
                safeLog(`修复包含反斜杠的选择器: "${selector}" -> "*"`);
                STATE.fixedSelectors.add(selector);
                return '*';
            }
            safeLog(`修复包含反斜杠的选择器: "${selector}" -> "${fixedSelector}"`);
            STATE.fixedSelectors.add(selector);
            return fixedSelector;
        }

        // 检查并修复包含无效字符的选择器
        if (/[:*+~>][:*+~>]/.test(selector)) {
            const fixedSelector = selector.replace(/[:*+~>][:*+~>]+/g, '*');
            safeLog(`修复包含无效组合的选择器: "${selector}" -> "${fixedSelector}"`);
            STATE.fixedSelectors.add(selector);
            return fixedSelector;
        }

        return selector;
    }

    // 修复querySelectorAll
    function patchQuerySelectorAll() {
        if (CONFIG.patchQuerySelectorAll && !STATE.originalQuerySelectorAll) {
            // 保存原始函数
            STATE.originalQuerySelectorAll = Element.prototype.querySelectorAll;

            // 替换为修复版本
            Element.prototype.querySelectorAll = function(selector) {
                try {
                    return STATE.originalQuerySelectorAll.call(this, selector);
                } catch (error) {
                    // 如果选择器无效，尝试修复
                    safeLog(`querySelectorAll执行错误: ${error.message}，尝试修复选择器: "${selector}"`, 'warn');

                    const fixedSelector = fixSelector(selector);
                    if (fixedSelector !== selector) {
                        try {
                            return STATE.originalQuerySelectorAll.call(this, fixedSelector);
                        } catch (fixedError) {
                            safeLog(`修复后的选择器仍然无效: "${fixedSelector}"，错误: ${fixedError.message}`, 'error');
                            // 返回空NodeList
                            return document.createDocumentFragment().querySelectorAll('*');
                        }
                    }

                    // 如果无法修复，返回空NodeList
                    safeLog(`无法修复选择器: "${selector}"，返回空结果`, 'error');
                    return document.createDocumentFragment().querySelectorAll('*');
                }
            };

            safeLog('已修复Element.prototype.querySelectorAll');
        }
    }

    // 修复jQuery选择器
    function patchjQuerySelectors() {
        if (CONFIG.patchSelectors && typeof jQuery !== 'undefined') {
            // 保存原始的jQuery.find函数
            const originalFind = jQuery.find;

            // 替换为修复版本
            jQuery.find = function(selector, context, results, seed) {
                try {
                    return originalFind.call(this, selector, context, results, seed);
                } catch (error) {
                    // 如果选择器无效，尝试修复
                    safeLog(`jQuery.find执行错误: ${error.message}，尝试修复选择器: "${selector}"`, 'warn');

                    const fixedSelector = fixSelector(selector);
                    if (fixedSelector !== selector) {
                        try {
                            return originalFind.call(this, fixedSelector, context, results, seed);
                        } catch (fixedError) {
                            safeLog(`修复后的选择器仍然无效: "${fixedSelector}"，错误: ${fixedError.message}`, 'error');
                            // 返回空结果
                            return [];
                        }
                    }

                    // 如果无法修复，返回空结果
                    safeLog(`无法修复选择器: "${selector}"，返回空结果`, 'error');
                    return [];
                }
            };

            safeLog('已修复jQuery.find');
        }
    }

    // 初始化
    function initialize() {
        if (STATE.initialized) {
            return;
        }

        safeLog('初始化jQuery选择器修复脚本');

        // 修复querySelectorAll
        patchQuerySelectorAll();

        // 修复jQuery选择器
        patchjQuerySelectors();

        // 标记为已初始化
        STATE.initialized = true;
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出到全局命名空间，以便调试
    window.jQuerySelectorFix = {
        config: CONFIG,
        state: STATE,
        fixSelector: fixSelector
    };

    safeLog('脚本加载完成');
})();
