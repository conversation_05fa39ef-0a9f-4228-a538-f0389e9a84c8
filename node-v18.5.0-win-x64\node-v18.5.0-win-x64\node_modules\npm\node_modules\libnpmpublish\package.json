{"name": "libnpmpublish", "version": "6.0.4", "description": "Programmatic API for the bits behind npm publish and unpublish", "author": "GitHub Inc.", "main": "lib/index.js", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "files": ["bin/", "lib/"], "license": "ISC", "scripts": {"eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "test": "tap", "posttest": "npm run lint", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "libnpmpack": "^4.0.0", "lodash.clonedeep": "^4.5.0", "nock": "^13.2.4", "tap": "^16.0.1"}, "repository": {"type": "git", "url": "https://github.com/npm/cli.git", "directory": "workspaces/libnpmpublish"}, "bugs": "https://github.com/npm/cli/issues", "homepage": "https://npmjs.com/package/libnpmpublish", "dependencies": {"normalize-package-data": "^4.0.0", "npm-package-arg": "^9.0.1", "npm-registry-fetch": "^13.0.0", "semver": "^7.3.7", "ssri": "^9.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}}