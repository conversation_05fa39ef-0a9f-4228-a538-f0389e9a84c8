"""
修复章纲分析内容和推理过程 (v2)

此脚本用于修复章纲分析内容中的主要内容部分，确保它足够详细，
以原文的风格和语气进行叙述，并修复缺失的推理过程数据。
"""

import os
import sys
import logging
import json
import re
import traceback
from datetime import datetime
import time

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_chapter_outline_v2.log')
    ]
)
logger = logging.getLogger(__name__)

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的模块
try:
    from src.db.connection import Session
    from src.models.chapter_analysis_result import ChapterAnalysisResult
    from src.models.chapter_analysis_process import ChapterAnalysisProcess
    from src.models.chapter import Chapter
    from src.models.novel import Novel
    from src.api.deepseek_client import DeepSeekClient
    import config
    from dimension_analysis_templates import get_dimension_name
except ImportError as e:
    logger.error(f"导入模块时出错: {str(e)}")
    sys.exit(1)

def fix_chapter_outline_content(chapter_id=None, force=False):
    """
    修复章纲分析内容和推理过程
    
    Args:
        chapter_id: 章节ID，如果为None则处理所有章节
        force: 是否强制重新生成内容，即使已存在
    """
    session = Session()
    try:
        # 构建查询
        query = session.query(ChapterAnalysisResult).filter(
            ChapterAnalysisResult.dimension == "chapter_outline"
        )
        
        # 如果指定了章节ID，只处理该章节
        if chapter_id:
            query = query.filter(ChapterAnalysisResult.chapter_id == chapter_id)
        
        # 获取所有需要处理的分析结果
        results = query.all()
        logger.info(f"找到 {len(results)} 个章纲分析结果")
        
        # 创建API客户端
        client = DeepSeekClient(model=config.DEFAULT_MODEL)
        
        # 处理每个分析结果
        for result in results:
            try:
                # 获取章节和小说信息
                chapter = session.query(Chapter).filter(Chapter.id == result.chapter_id).first()
                novel = session.query(Novel).filter(Novel.id == result.novel_id).first()
                
                if not chapter or not novel:
                    logger.warning(f"找不到章节或小说: chapter_id={result.chapter_id}, novel_id={result.novel_id}")
                    continue
                
                logger.info(f"处理章节分析结果: id={result.id}, chapter_id={result.chapter_id}, chapter_title={chapter.title or f'第{chapter.chapter_number}章'}")
                
                # 1. 修复推理过程
                if not result.reasoning_content or force:
                    logger.info(f"修复推理过程")
                    
                    # 查询分析过程记录
                    processes = session.query(ChapterAnalysisProcess).filter(
                        ChapterAnalysisProcess.result_id == result.id,
                        ChapterAnalysisProcess.processing_stage == "reasoning"
                    ).all()
                    
                    if processes:
                        # 从分析过程记录中恢复推理过程
                        process = max(processes, key=lambda p: p.id)  # 获取最新的记录
                        if process.output_text:
                            logger.info(f"从分析过程记录中恢复推理过程，长度: {len(process.output_text)}")
                            result.reasoning_content = process.output_text
                            session.commit()
                            logger.info(f"成功更新推理过程")
                        else:
                            logger.warning(f"分析过程记录的输出文本为空")
                    else:
                        # 如果没有分析过程记录，使用内容作为推理过程
                        logger.info(f"没有分析过程记录，使用内容作为推理过程")
                        
                        # 构建推理过程内容
                        reasoning_content = f"""## 分析思路说明：
1. **内容重现与叙事梳理**：全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。内容不设字数限制，越详细越好。
2. **结构功能与定位分析**：全面分析章节的内部结构和功能定位，评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性
3. **关键事件与转折点**：详细分析本章节中的关键事件和转折点，识别并详述章节中的转折点、冲突点和高潮点
4. **人物表现与发展轨迹**：详细分析本章节中主要人物的表现、行动和发展，描述每个重要人物在本章中的言行和思想变化
5. **章节关联与叙事连贯性**：深入分析本章节与前后章节的关系和叙事连贯性，评估章节过渡的自然度和技巧多样性
6. **伏笔设置与悬念管理**：分析本章节中的伏笔设置和悬念管理，指出章节中埋下的伏笔和暗示，以及它们可能的未来发展方向
7. **主题情感与氛围营造**：评估章节的主题明确性和情感基调，分析本章节的核心主题和情感色彩
8. **创新特色与读者体验**：分析本章节的特色元素和创新点，识别章节中独特的写作技巧，评估如何吸引和维持读者兴趣

## 详细分析：
{result.content}
"""
                        
                        # 更新分析结果
                        result.reasoning_content = reasoning_content
                        session.commit()
                        logger.info(f"成功使用内容作为推理过程")
                
                # 2. 修复内容中的主要内容部分
                content = result.content or ""
                if "## 主要内容" in content:
                    # 提取主要内容部分
                    main_content_match = re.search(r'## 主要内容\s*\n(.*?)(?:\n##|\Z)', content, re.DOTALL)
                    if main_content_match:
                        main_content = main_content_match.group(1).strip()
                        logger.info(f"主要内容长度: {len(main_content)}")
                        
                        # 检查主要内容是否足够详细
                        if len(main_content) < 3000 or main_content.startswith("[") or main_content.endswith("]") or force:
                            logger.info(f"主要内容不够详细或需要强制更新，开始生成更详细的内容")
                            
                            # 构建增强提示词
                            prompt = f"""你是一位经验丰富的文学分析专家，擅长分析各类小说文本。请对以下小说《{novel.title}》的章节《{chapter.title or f'第{chapter.chapter_number}章'}》进行详细的章纲分析，特别是内容重现部分。

请按照以下要求进行分析：

## 主要内容
请全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。

内容不设字数限制，越详细越好。严格禁止简短概括或总结性描述。必须以生动、细致、具体的小说叙述方式重现章节内容，让读者仿佛亲历其境。必须包含以下所有要素，并对每个要素进行极其详尽的描述：

1. 详细的场景描述：
   - 以原文的风格描述每个场景的环境、氛围和背景，包括光线、声音、气味等感官细节
   - 生动描绘场景中的物品、布置和空间关系，提供具体的视觉画面
   - 细致刻画场景的时间特征（如白天/黑夜、季节、天气等）及其变化
   - 深入表现场景的情绪氛围和给人的整体感受，以及场景如何影响人物情绪

2. 完整的事件过程：
   - 按照发生顺序详细描述每个事件的起因、经过和结果，不遗漏任何重要细节
   - 以原文的风格描述事件中的具体动作和过程，包括动作的速度、力度和精确描述
   - 生动再现事件的节奏和紧张程度，以及情绪变化曲线
   - 深入分析事件对情节发展的推动作用和对人物的影响

3. 人物言行与心理：
   - 完整记录主要人物的对话内容，尽可能使用原文中的重要对话，保留对话的语气和特点
   - 以原文的风格详细描述人物的动作、表情和肢体语言，包括微小的表情变化和身体动作
   - 深入描述人物的心理活动、情感变化和内心冲突，展现人物的思想过程
   - 生动描述人物之间的互动和关系动态，包括潜在的情感变化和关系发展

必须使用生动的语言、丰富的形容词和具体的细节描述，避免抽象概括。必须包含原文中的关键对话和场景描写，保留原文的风格和语气。必须按照时间顺序或情节发展顺序进行叙述，确保叙述的连贯性和完整性。

章节内容：
{chapter.content}

请只输出"## 主要内容"部分，不要包含其他分析内容。
"""
                            
                            # 调用API生成增强内容
                            logger.info(f"调用API生成增强内容: chapter_id={result.chapter_id}")
                            try:
                                enhanced_result = client.analyze_text(
                                    text=prompt,
                                    analysis_type="chapter_outline_enhanced",
                                    novel_id=novel.id,
                                    chapter_id=chapter.id,
                                    max_tokens=20000  # 使用较大的max_tokens确保生成足够详细的内容
                                )
                                
                                if enhanced_result and "content" in enhanced_result:
                                    enhanced_content = enhanced_result["content"]
                                    logger.info(f"成功生成增强内容: length={len(enhanced_content)}")
                                    
                                    # 替换原内容中的主要内容部分
                                    new_content = re.sub(
                                        r'(## 主要内容\s*\n).*?(?=\n##|\Z)',
                                        r'\1' + enhanced_content.replace('## 主要内容', '').strip(),
                                        content,
                                        flags=re.DOTALL
                                    )
                                    
                                    # 更新分析结果
                                    result.content = new_content
                                    session.commit()
                                    logger.info(f"成功更新章纲分析内容")
                                else:
                                    logger.warning(f"生成增强内容失败: {enhanced_result.get('error', '未知错误')}")
                            except Exception as e:
                                logger.error(f"调用API生成增强内容时出错: {str(e)}")
                                logger.error(traceback.format_exc())
                        else:
                            logger.info(f"主要内容已足够详细，无需修复")
                    else:
                        logger.warning(f"未找到主要内容部分")
                else:
                    logger.warning(f"内容中不包含'主要内容'部分")
                
                # 休息一下，避免API调用过于频繁
                time.sleep(1)
            except Exception as e:
                logger.error(f"处理章节分析结果时出错: {str(e)}")
                logger.error(traceback.format_exc())
                session.rollback()
        
        logger.info(f"完成修复章纲分析内容和推理过程")
    except Exception as e:
        logger.error(f"修复章纲分析内容和推理过程时出错: {str(e)}")
        logger.error(traceback.format_exc())
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="修复章纲分析内容和推理过程")
    parser.add_argument("--chapter_id", type=int, help="章节ID，如果不指定则处理所有章节")
    parser.add_argument("--force", action="store_true", help="是否强制重新生成内容，即使已存在")
    args = parser.parse_args()
    
    # 执行修复
    fix_chapter_outline_content(args.chapter_id, args.force)
