<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫系统连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        h1 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        #results {
            margin-top: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
    </style>
<script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';console.error('错误处理脚本加载失败，尝试备用路径')"></script>
    </head>
<body>
    <h1>九猫系统连接测试</h1>

    <div class="container">
        <p class="success">✅ 如果您能看到此页面，说明服务器已成功启动，并且浏览器可以正常连接到服务器！</p>

        <p>当前时间: <span id="current-time"></span></p>

        <p>浏览器信息: <span id="browser-info"></span></p>

        <p>连接信息: <span id="connection-info"></span></p>

        <button onclick="testAPI()">测试系统API连接</button>
        <button onclick="testDeepSeekAPI()" style="background-color: #2196F3;">测试DeepSeek API连接</button>

        <div id="results"></div>

        <p>如果一切正常，请尝试访问以下链接：</p>
        <ul>
            <li><a href="/">首页</a></li>
            <li><a href="/novel/4">小说分析页面</a></li>
        </ul>
    </div>

    <script>
        // 显示当前时间
        document.getElementById('current-time').textContent = new Date().toLocaleString();

        // 显示浏览器信息
        document.getElementById('browser-info').textContent = navigator.userAgent;

        // 显示连接信息
        document.getElementById('connection-info').textContent =
            navigator.onLine ? '在线' : '离线';

        // 测试系统API连接
        function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '正在测试系统API连接...';

            fetch('/api/novels/4/analysis')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('系统API请求失败: ' + response.status);
                })
                .then(data => {
                    resultsDiv.innerHTML = '<p class="success">✅ 系统API连接成功！</p>' +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<p style="color: red;">❌ 系统API连接失败: ' +
                        error.message + '</p>';
                });
        }

        // 测试DeepSeek API连接
        function testDeepSeekAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="alert alert-info">' +
                '<div class="spinner-border spinner-border-sm" role="status"></div> ' +
                '正在测试DeepSeek API连接，请稍候...' +
                '<br><small>这可能需要10-30秒，请耐心等待</small>' +
                '</div>';

            fetch('/api/test-connection')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('DeepSeek API请求失败: ' + response.status);
                })
                .then(data => {
                    if (data.success) {
                        let statsHtml = '';
                        if (data.api_stats) {
                            const duration = data.duration || '未知';
                            const totalCalls = data.api_stats.total_calls || 0;
                            const totalTokens = data.api_stats.total_tokens || 0;
                            const totalCost = data.api_stats.total_cost ? data.api_stats.total_cost.toFixed(4) : '0.0000';

                            statsHtml =
                                '<div class="card mt-3 mb-3">' +
                                '<div class="card-header bg-info text-white">API调用统计</div>' +
                                '<div class="card-body">' +
                                '<p><strong>调用耗时:</strong> ' + duration + '</p>' +
                                '<p><strong>总调用次数:</strong> ' + totalCalls + '</p>' +
                                '<p><strong>总令牌数:</strong> ' + totalTokens + '</p>' +
                                '<p><strong>总费用:</strong> ' + totalCost + ' 元</p>' +
                                '</div>' +
                                '</div>';
                        }

                        const content = data.content || '';

                        resultsDiv.innerHTML =
                            '<div class="alert alert-success">' +
                            '<h4>✅ DeepSeek API连接成功！</h4>' +
                            '<p>API连接测试成功完成。</p>' +
                            '</div>' +
                            statsHtml +
                            '<div class="card">' +
                            '<div class="card-header bg-success text-white">API响应内容</div>' +
                            '<div class="card-body">' +
                            '<pre style="white-space: pre-wrap; max-height: 300px; overflow-y: auto;">' + content + '</pre>' +
                            '</div>' +
                            '</div>';
                    } else {
                        const errorMsg = data.error || '未知错误';
                        const duration = data.duration || '未知';
                        const errorDetails = data.error_details || '无详细信息';

                        resultsDiv.innerHTML =
                            '<div class="alert alert-danger">' +
                            '<h4>❌ DeepSeek API连接失败</h4>' +
                            '<p><strong>错误信息:</strong> ' + errorMsg + '</p>' +
                            '<p><strong>耗时:</strong> ' + duration + '</p>' +
                            '</div>' +
                            '<div class="card mt-3">' +
                            '<div class="card-header bg-warning">错误详情</div>' +
                            '<div class="card-body">' +
                            '<pre style="white-space: pre-wrap; max-height: 200px; overflow-y: auto;">' + errorDetails + '</pre>' +
                            '</div>' +
                            '</div>' +
                            '<div class="alert alert-info mt-3">' +
                            '<h5>可能的解决方案:</h5>' +
                            '<ul>' +
                            '<li>检查API密钥是否正确</li>' +
                            '<li>检查网络连接是否正常</li>' +
                            '<li>检查API端点是否可访问</li>' +
                            '<li>稍后重试</li>' +
                            '</ul>' +
                            '</div>';
                    }
                })
                .catch(error => {
                    const errorMsg = error.message || '未知错误';

                    resultsDiv.innerHTML =
                        '<div class="alert alert-danger">' +
                        '<h4>❌ DeepSeek API连接失败</h4>' +
                        '<p><strong>错误信息:</strong> ' + errorMsg + '</p>' +
                        '</div>' +
                        '<div class="alert alert-info mt-3">' +
                        '<h5>可能的解决方案:</h5>' +
                        '<ul>' +
                        '<li>检查服务器是否正常运行</li>' +
                        '<li>检查网络连接是否正常</li>' +
                        '<li>刷新页面后重试</li>' +
                        '</ul>' +
                        '</div>';
                });
        }
    </script>
</body>
</html>
