/**
 * 九猫 - 小说功能按钮修复脚本
 * 解决小说详情页面功能按钮跳转问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._novelFunctionButtonFixLoaded) {
        console.log('[功能按钮修复] 小说功能按钮修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._novelFunctionButtonFixLoaded = true;
    
    console.log('[功能按钮修复] 小说功能按钮修复脚本已加载 - 版本1.0.0');
    
    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[功能按钮修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    function safeError(message) {
        try {
            originalConsoleError.call(console, '[功能按钮修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 初始化函数
    function initialize() {
        safeLog('初始化小说功能按钮修复');
        
        // 检查是否在小说详情页面
        if (!isNovelPage()) {
            safeLog('当前不是小说页面，不执行修复');
            return;
        }
        
        safeLog('检测到小说页面，开始执行修复');
        
        // 获取小说ID
        const novelId = getNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法修复功能按钮');
            return;
        }
        
        safeLog('当前小说ID: ' + novelId);
        
        // 修复所有功能按钮
        fixAllFunctionButtons(novelId);
        
        // 监听DOM变化，修复新添加的功能按钮
        observeDOMChanges(novelId);
        
        // 添加全局点击事件处理
        addGlobalClickHandler(novelId);
        
        safeLog('小说功能按钮修复完成');
    }
    
    // 检查是否在小说页面
    function isNovelPage() {
        const path = window.location.pathname;
        // 匹配 /novel/{id} 或 /novel/{id}/... 格式的URL
        return /^\/novel\/\d+/.test(path);
    }
    
    // 获取小说ID
    function getNovelId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
            
            // 从页面元素中获取
            const novelContainer = document.querySelector('[data-novel-id]');
            if (novelContainer && novelContainer.dataset.novelId) {
                return novelContainer.dataset.novelId;
            }
            
            // 从全局变量中获取
            if (window.novelId) {
                return window.novelId;
            }
            
            if (window.novelIdFromTemplate) {
                return window.novelIdFromTemplate;
            }
            
            // 从页面内容中获取
            const novelIdElement = document.querySelector('.novel-id, #novel-id, [name="novel_id"]');
            if (novelIdElement) {
                if (novelIdElement.value) {
                    return novelIdElement.value;
                }
                if (novelIdElement.textContent) {
                    const idMatch = novelIdElement.textContent.match(/\d+/);
                    if (idMatch) {
                        return idMatch[0];
                    }
                }
            }
            
            return null;
        } catch (e) {
            safeError('获取小说ID时出错: ' + e.message);
            return null;
        }
    }
    
    // 修复所有功能按钮
    function fixAllFunctionButtons(novelId) {
        safeLog('修复所有功能按钮');
        
        // 修复章节分析按钮
        fixChapterAnalysisButtons(novelId);
        
        // 修复维度分析按钮
        fixDimensionAnalysisButtons(novelId);
        
        // 修复其他功能按钮
        fixOtherFunctionButtons(novelId);
    }
    
    // 修复章节分析按钮
    function fixChapterAnalysisButtons(novelId) {
        safeLog('修复章节分析按钮');
        
        // 查找所有可能的章节分析按钮
        const buttons = document.querySelectorAll('a.btn, button.btn, a[href*="chapter"], [data-target*="chapter"]');
        
        buttons.forEach(btn => {
            if (!btn.textContent) return;
            
            // 检查是否是章节分析按钮
            const text = btn.textContent.trim().toLowerCase();
            if (text.includes('章节') && 
               (text.includes('分析') || text.includes('列表') || text.includes('管理'))) {
                
                safeLog('找到章节分析按钮: ' + btn.textContent);
                
                // 修复链接和点击事件
                if (btn.tagName === 'A') {
                    // 保存原始链接用于调试
                    const originalHref = btn.getAttribute('href');
                    if (originalHref) {
                        btn.setAttribute('data-original-href', originalHref);
                    }
                    
                    // 设置正确的链接
                    btn.href = `/novel/${novelId}/chapters`;
                    
                    safeLog('修复章节分析链接: ' + (originalHref || '无') + ' -> /novel/' + novelId + '/chapters');
                }
                
                // 设置点击事件 - 使用更可靠的方式处理点击
                if (!btn.__chapterButtonFixed) {
                    btn.__chapterButtonFixed = true;
                    
                    // 设置新的onclick处理函数，优先级高于addEventListener
                    btn.onclick = function(e) {
                        // 阻止默认行为和事件冒泡
                        if (e && e.preventDefault) e.preventDefault();
                        if (e && e.stopPropagation) e.stopPropagation();
                        
                        // 记录点击
                        safeLog('点击章节分析按钮，跳转到: /novel/' + novelId + '/chapters');
                        
                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = `/novel/${novelId}/chapters`;
                        }, 0);
                        
                        return false;
                    };
                }
            }
        });
    }
    
    // 修复维度分析按钮
    function fixDimensionAnalysisButtons(novelId) {
        safeLog('修复维度分析按钮');
        
        // 查找所有维度分析按钮
        const dimensionButtons = document.querySelectorAll('[data-dimension], .dimension-btn, .analyze-btn, .view-btn');
        
        dimensionButtons.forEach(btn => {
            // 获取维度名称
            let dimension = btn.getAttribute('data-dimension');
            
            // 如果没有data-dimension属性，尝试从类名或内容中获取
            if (!dimension) {
                const classList = Array.from(btn.classList);
                const dimensionClass = classList.find(cls => cls.startsWith('dimension-') || cls.startsWith('analyze-'));
                
                if (dimensionClass) {
                    dimension = dimensionClass.replace('dimension-', '').replace('analyze-', '');
                } else if (btn.textContent) {
                    // 从按钮文本中猜测维度
                    const text = btn.textContent.trim().toLowerCase();
                    if (text.includes('语言风格')) dimension = 'language_style';
                    else if (text.includes('节奏')) dimension = 'rhythm_pacing';
                    else if (text.includes('结构')) dimension = 'structure';
                    // 添加更多维度匹配...
                }
            }
            
            if (dimension) {
                safeLog('找到维度分析按钮: ' + dimension);
                
                // 修复链接和点击事件
                if (btn.tagName === 'A') {
                    // 保存原始链接用于调试
                    const originalHref = btn.getAttribute('href');
                    if (originalHref) {
                        btn.setAttribute('data-original-href', originalHref);
                    }
                    
                    // 设置正确的链接
                    btn.href = `/novel/${novelId}/analysis/${dimension}`;
                    
                    safeLog('修复维度分析链接: ' + (originalHref || '无') + ' -> /novel/' + novelId + '/analysis/' + dimension);
                }
                
                // 设置点击事件
                if (!btn.__dimensionButtonFixed) {
                    btn.__dimensionButtonFixed = true;
                    
                    // 设置新的onclick处理函数
                    btn.onclick = function(e) {
                        // 阻止默认行为和事件冒泡
                        if (e && e.preventDefault) e.preventDefault();
                        if (e && e.stopPropagation) e.stopPropagation();
                        
                        // 记录点击
                        safeLog('点击维度分析按钮，跳转到: /novel/' + novelId + '/analysis/' + dimension);
                        
                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = `/novel/${novelId}/analysis/${dimension}`;
                        }, 0);
                        
                        return false;
                    };
                }
            }
        });
    }
    
    // 修复其他功能按钮
    function fixOtherFunctionButtons(novelId) {
        safeLog('修复其他功能按钮');
        
        // 查找所有可能的功能按钮
        const buttons = document.querySelectorAll('a.btn, button.btn');
        
        buttons.forEach(btn => {
            if (!btn.textContent) return;
            
            const text = btn.textContent.trim().toLowerCase();
            
            // 检查是否是重新分析按钮
            if (text.includes('重新分析') || text.includes('重新') && text.includes('分析')) {
                safeLog('找到重新分析按钮: ' + btn.textContent);
                
                // 修复链接和点击事件
                if (btn.tagName === 'A') {
                    // 保存原始链接用于调试
                    const originalHref = btn.getAttribute('href');
                    if (originalHref) {
                        btn.setAttribute('data-original-href', originalHref);
                    }
                    
                    // 设置正确的链接
                    btn.href = `/novel/${novelId}/reanalyze`;
                    
                    safeLog('修复重新分析链接: ' + (originalHref || '无') + ' -> /novel/' + novelId + '/reanalyze');
                }
                
                // 设置点击事件
                if (!btn.__reanalyzeButtonFixed) {
                    btn.__reanalyzeButtonFixed = true;
                    
                    // 设置新的onclick处理函数
                    btn.onclick = function(e) {
                        // 阻止默认行为和事件冒泡
                        if (e && e.preventDefault) e.preventDefault();
                        if (e && e.stopPropagation) e.stopPropagation();
                        
                        // 记录点击
                        safeLog('点击重新分析按钮，跳转到: /novel/' + novelId + '/reanalyze');
                        
                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = `/novel/${novelId}/reanalyze`;
                        }, 0);
                        
                        return false;
                    };
                }
            }
        });
    }
    
    // 监听DOM变化，修复新添加的功能按钮
    function observeDOMChanges(novelId) {
        try {
            // 创建MutationObserver
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // 检查是否有新添加的节点
                    if (mutation.addedNodes.length > 0) {
                        // 延迟执行修复，确保DOM完全更新
                        setTimeout(() => fixAllFunctionButtons(novelId), 100);
                    }
                });
            });
            
            // 配置观察选项
            const config = { childList: true, subtree: true };
            
            // 开始观察
            observer.observe(document.body, config);
            
            safeLog('已开始监听DOM变化');
        } catch (e) {
            safeError('监听DOM变化时出错: ' + e.message);
        }
    }
    
    // 添加全局点击事件处理
    function addGlobalClickHandler(novelId) {
        // 如果已经添加过全局点击处理器，不再重复添加
        if (window.__novelFunctionButtonGlobalHandlerAdded) {
            return;
        }
        
        // 添加全局点击事件处理
        document.addEventListener('click', function(e) {
            // 向上查找最近的按钮或链接
            let target = e.target;
            while (target && target !== document) {
                // 检查是否是章节分析按钮
                if ((target.tagName === 'A' || target.tagName === 'BUTTON') && 
                    target.textContent && 
                    target.textContent.toLowerCase().includes('章节') && 
                    target.textContent.toLowerCase().includes('分析')) {
                    
                    // 阻止默认行为和事件冒泡
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 记录点击
                    safeLog('通过全局处理器捕获章节分析按钮点击，跳转到: /novel/' + novelId + '/chapters');
                    
                    // 使用setTimeout确保在所有事件处理完成后跳转
                    setTimeout(function() {
                        window.location.href = `/novel/${novelId}/chapters`;
                    }, 0);
                    
                    return false;
                }
                
                target = target.parentElement;
            }
        }, true);
        
        window.__novelFunctionButtonGlobalHandlerAdded = true;
        safeLog('已添加全局点击事件处理');
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }
    
    // 导出API，方便调试和手动调用
    window.novelFunctionButtonFix = {
        initialize: initialize,
        fixAllFunctionButtons: fixAllFunctionButtons
    };
})();
