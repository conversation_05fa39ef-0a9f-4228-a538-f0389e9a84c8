/**
 * 九猫 - 模态框修复脚本
 * 专门用于修复模态框无法打开的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[模态框修复] 脚本已加载');

    // 检查Bootstrap是否正确加载
    function checkBootstrap() {
        if (typeof bootstrap === 'undefined') {
            console.error('[模态框修复] Bootstrap未加载，尝试加载本地版本');
            loadBootstrap();
            return false;
        }
        
        if (typeof bootstrap.Modal === 'undefined') {
            console.error('[模态框修复] Bootstrap.Modal未定义，尝试加载本地版本');
            loadBootstrap();
            return false;
        }
        
        console.log('[模态框修复] Bootstrap已正确加载');
        return true;
    }

    // 加载Bootstrap
    function loadBootstrap() {
        const script = document.createElement('script');
        script.src = '/static/js/lib/bootstrap.bundle.min.js';
        script.onload = function() {
            console.log('[模态框修复] Bootstrap本地版本加载成功');
            fixModals();
        };
        script.onerror = function() {
            console.error('[模态框修复] Bootstrap本地版本加载失败，尝试CDN');
            const cdnScript = document.createElement('script');
            cdnScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
            cdnScript.onload = function() {
                console.log('[模态框修复] Bootstrap CDN版本加载成功');
                fixModals();
            };
            document.head.appendChild(cdnScript);
        };
        document.head.appendChild(script);
    }

    // 修复所有模态框
    function fixModals() {
        console.log('[模态框修复] 开始修复模态框');
        
        // 查找所有模态框触发按钮
        const modalTriggers = document.querySelectorAll('[data-bs-toggle="modal"]');
        console.log(`[模态框修复] 找到 ${modalTriggers.length} 个模态框触发按钮`);
        
        modalTriggers.forEach(trigger => {
            const targetId = trigger.getAttribute('data-bs-target');
            if (!targetId) {
                console.error('[模态框修复] 模态框触发按钮没有data-bs-target属性');
                return;
            }
            
            const modalElement = document.querySelector(targetId);
            if (!modalElement) {
                console.error(`[模态框修复] 找不到模态框元素: ${targetId}`);
                return;
            }
            
            console.log(`[模态框修复] 修复模态框: ${targetId}`);
            
            // 移除旧的事件监听器
            const newTrigger = trigger.cloneNode(true);
            trigger.parentNode.replaceChild(newTrigger, trigger);
            
            // 添加新的事件监听器
            newTrigger.addEventListener('click', function(e) {
                e.preventDefault();
                console.log(`[模态框修复] 点击模态框触发按钮: ${targetId}`);
                
                try {
                    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined') {
                        const modal = new bootstrap.Modal(modalElement);
                        modal.show();
                        console.log(`[模态框修复] 成功打开模态框: ${targetId}`);
                    } else {
                        console.error('[模态框修复] Bootstrap或Modal未定义，无法打开模态框');
                        // 尝试使用jQuery打开模态框
                        if (typeof $ !== 'undefined') {
                            $(targetId).modal('show');
                            console.log(`[模态框修复] 使用jQuery打开模态框: ${targetId}`);
                        } else {
                            // 最后的备用方案：直接修改样式
                            modalElement.style.display = 'block';
                            modalElement.classList.add('show');
                            document.body.classList.add('modal-open');
                            
                            // 创建背景遮罩
                            const backdrop = document.createElement('div');
                            backdrop.className = 'modal-backdrop fade show';
                            document.body.appendChild(backdrop);
                            
                            console.log(`[模态框修复] 使用原生JS打开模态框: ${targetId}`);
                        }
                    }
                } catch (error) {
                    console.error(`[模态框修复] 打开模态框时出错: ${error.message}`);
                }
            });
        });
        
        // 修复关闭按钮
        const closeButtons = document.querySelectorAll('[data-bs-dismiss="modal"]');
        console.log(`[模态框修复] 找到 ${closeButtons.length} 个关闭按钮`);
        
        closeButtons.forEach(button => {
            // 移除旧的事件监听器
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // 添加新的事件监听器
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 查找最近的模态框
                const modal = this.closest('.modal');
                if (!modal) {
                    console.error('[模态框修复] 找不到关闭按钮对应的模态框');
                    return;
                }
                
                console.log(`[模态框修复] 点击关闭按钮: ${modal.id}`);
                
                try {
                    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined') {
                        const modalInstance = bootstrap.Modal.getInstance(modal);
                        if (modalInstance) {
                            modalInstance.hide();
                            console.log(`[模态框修复] 成功关闭模态框: ${modal.id}`);
                        } else {
                            // 如果没有实例，创建一个新实例并关闭
                            const newModalInstance = new bootstrap.Modal(modal);
                            newModalInstance.hide();
                            console.log(`[模态框修复] 创建新实例并关闭模态框: ${modal.id}`);
                        }
                    } else {
                        // 尝试使用jQuery关闭模态框
                        if (typeof $ !== 'undefined') {
                            $(modal).modal('hide');
                            console.log(`[模态框修复] 使用jQuery关闭模态框: ${modal.id}`);
                        } else {
                            // 最后的备用方案：直接修改样式
                            modal.style.display = 'none';
                            modal.classList.remove('show');
                            document.body.classList.remove('modal-open');
                            
                            // 移除背景遮罩
                            const backdrop = document.querySelector('.modal-backdrop');
                            if (backdrop) {
                                backdrop.parentNode.removeChild(backdrop);
                            }
                            
                            console.log(`[模态框修复] 使用原生JS关闭模态框: ${modal.id}`);
                        }
                    }
                } catch (error) {
                    console.error(`[模态框修复] 关闭模态框时出错: ${error.message}`);
                }
            });
        });
        
        console.log('[模态框修复] 模态框修复完成');
    }

    // 初始化
    function init() {
        console.log('[模态框修复] 初始化中...');
        
        // 检查Bootstrap是否已加载
        if (checkBootstrap()) {
            // 如果已加载，直接修复模态框
            fixModals();
        }
        
        // 导出全局函数
        window.fixModals = fixModals;
        
        console.log('[模态框修复] 初始化完成');
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 在页面完全加载后再次检查
    window.addEventListener('load', function() {
        console.log('[模态框修复] 页面完全加载，再次检查Bootstrap');
        if (checkBootstrap()) {
            fixModals();
        }
    });

    console.log('[模态框修复] 脚本加载完成');
})();
