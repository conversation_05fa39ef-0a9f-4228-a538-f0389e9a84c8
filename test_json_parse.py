"""
测试JSON解析修复
"""
import json
from markupsafe import Markup

def tojson_safe(obj):
    """安全地将对象转换为JSON字符串，用于在JavaScript中使用"""
    try:
        # 使用更安全的JSON序列化方式
        json_str = json.dumps(obj, ensure_ascii=False)
        
        # 确保返回的是安全的标记，避免Flask自动转义
        return Markup(json_str)
    except Exception as e:
        print(f"JSON序列化对象时出错: {str(e)}")
        # 返回一个空对象，避免前端解析错误
        return Markup("{}")

# 测试数据
test_data = {
    "language_style": {
        "content": "这是一个测试内容，包含一些特殊字符: \" ' \\ \n",
        "metadata": {
            "processing_time": 10.5,
            "visualization_data": {
                "radar": {
                    "labels": ["标签1", "标签2", "标签3"],
                    "data": [85, 72, 90]
                }
            }
        }
    }
}

# 测试JSON序列化
json_str = tojson_safe(test_data)
print("序列化结果:")
print(json_str)

# 测试在HTML中使用
html_template = f"""
<!DOCTYPE html>
<html>
<head>
    <title>JSON解析测试</title>
</head>
<body>
    <h1>JSON解析测试</h1>
    
    <!-- 方法1: 直接赋值 -->
    <script>
        window.analysisResultsData = {json_str};
        console.log('方法1:', window.analysisResultsData);
    </script>
    
    <!-- 方法2: 使用data属性 -->
    <div id="analysis-data" style="display:none;" data-analysis='{json_str}'></div>
    <script>
        (function() {{
            try {{
                var dataElement = document.getElementById('analysis-data');
                var analysisDataStr = dataElement ? dataElement.getAttribute('data-analysis') : '{{}}'
                var analysisData = JSON.parse(analysisDataStr);
                console.log('方法2:', analysisData);
            }} catch (e) {{
                console.error('解析数据时出错:', e);
            }}
        }})();
    </script>
</body>
</html>
"""

# 保存HTML文件
with open("test_json_parse.html", "w", encoding="utf-8") as f:
    f.write(html_template)

print("\nHTML文件已生成: test_json_parse.html")
