<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-cache</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="npm-cache">npm-cache</h1>
<span class="description">Manipulates packages cache</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#details">Details</a></li><li><a href="#a-note-about-the-caches-design">A note about the cache's design</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#cache"><code>cache</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<pre lang="bash"><code>npm cache add &lt;tarball file&gt;
npm cache add &lt;folder&gt;
npm cache add &lt;tarball url&gt;
npm cache add &lt;git url&gt;
npm cache add &lt;name&gt;@&lt;version&gt;
npm cache clean [&lt;key&gt;]
npm cache ls [&lt;name&gt;@&lt;version&gt;]
npm cache verify
</code></pre>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<p>Note: This command is unaware of workspaces.</p>
<h3 id="description">Description</h3>
<p>Used to add, list, or clean the npm cache folder.</p>
<ul>
<li>
<p>add:
Add the specified packages to the local cache.  This command is primarily
intended to be used internally by npm, but it can provide a way to
add data to the local installation cache explicitly.</p>
</li>
<li>
<p>clean:
Delete all data out of the cache folder.  Note that this is typically
unnecessary, as npm's cache is self-healing and resistant to data
corruption issues.</p>
</li>
<li>
<p>verify:
Verify the contents of the cache folder, garbage collecting any unneeded
data, and verifying the integrity of the cache index and all cached data.</p>
</li>
</ul>
<h3 id="details">Details</h3>
<p>npm stores cache data in an opaque directory within the configured <code>cache</code>,
named <code>_cacache</code>. This directory is a
<a href="http://npm.im/cacache"><code>cacache</code></a>-based content-addressable cache that
stores all http request data as well as other package-related data. This
directory is primarily accessed through <code>pacote</code>, the library responsible
for all package fetching as of npm@5.</p>
<p>All data that passes through the cache is fully verified for integrity on
both insertion and extraction. Cache corruption will either trigger an
error, or signal to <code>pacote</code> that the data must be refetched, which it will
do automatically. For this reason, it should never be necessary to clear
the cache for any reason other than reclaiming disk space, thus why <code>clean</code>
now requires <code>--force</code> to run.</p>
<p>There is currently no method exposed through npm to inspect or directly
manage the contents of this cache. In order to access it, <code>cacache</code> must be
used directly.</p>
<p>npm will not remove data by itself: the cache will grow as new packages are
installed.</p>
<h3 id="a-note-about-the-caches-design">A note about the cache's design</h3>
<p>The npm cache is strictly a cache: it should not be relied upon as a
persistent and reliable data store for package data. npm makes no guarantee
that a previously-cached piece of data will be available later, and will
automatically delete corrupted contents. The primary guarantee that the
cache makes is that, if it does return data, that data will be exactly the
data that was inserted.</p>
<p>To run an offline verification of existing cache contents, use <code>npm cache verify</code>.</p>
<h3 id="configuration">Configuration</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="cache"><code>cache</code></h4>
<ul>
<li>Default: Windows: <code>%LocalAppData%\npm-cache</code>, Posix: <code>~/.npm</code></li>
<li>Type: Path</li>
</ul>
<p>The location of npm's cache directory. See <a href="../commands/npm-cache.html"><code>npm cache</code></a></p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../configuring-npm/folders.html">npm folders</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../configuring-npm/npmrc.html">npmrc</a></li>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../commands/npm-publish.html">npm publish</a></li>
<li><a href="../commands/npm-pack.html">npm pack</a></li>
<li><a href="https://npm.im/cacache">https://npm.im/cacache</a></li>
<li><a href="https://npm.im/pacote">https://npm.im/pacote</a></li>
<li><a href="https://npm.im/@npmcli/arborist">https://npm.im/@npmcli/arborist</a></li>
<li><a href="https://npm.im/make-fetch-happen">https://npm.im/make-fetch-happen</a></li>
</ul>
</div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-cache.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>