/**
 * 九猫系统 - createElement修复脚本
 * 版本: 1.0.0
 * 
 * 该脚本解决"Cannot read properties of undefined (reading 'set')"错误
 * 此错误通常发生在document.createElement后访问textContent属性时
 */

(function() {
    console.log('[九猫修复] createElement修复脚本已加载');
    
    // 检查是否已应用此修复
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded && window.__nineCatsFixes.loaded['create-element-fix']) {
        console.log('[九猫修复] createElement修复已经应用，跳过');
        return;
    }
    
    // 保存原始方法
    const originalCreateElement = document.createElement;
    
    // 重写createElement方法
    document.createElement = function(tagName) {
        try {
            // 调用原始方法
            const element = originalCreateElement.call(document, tagName);
            
            // 确保textContent属性存在且可访问
            if (element && typeof element.textContent === 'undefined') {
                // 为IE等旧浏览器创建textContent属性
                Object.defineProperty(element, 'textContent', {
                    get: function() {
                        return this.innerText || '';
                    },
                    set: function(value) {
                        this.innerText = value;
                    },
                    configurable: true
                });
                
                console.log('[九猫修复] 为元素添加了textContent属性');
            }
            
            // 添加安全访问器 - 确保针对属性的访问不会导致崩溃
            const originalGetAttribute = element.getAttribute;
            const originalSetAttribute = element.setAttribute;
            
            // 安全的getAttribute
            element.getAttribute = function(name) {
                try {
                    return originalGetAttribute.call(this, name);
                } catch (e) {
                    console.error('[九猫修复] getAttribute错误:', e.message);
                    return null;
                }
            };
            
            // 安全的setAttribute
            element.setAttribute = function(name, value) {
                try {
                    return originalSetAttribute.call(this, name, value);
                } catch (e) {
                    console.error('[九猫修复] setAttribute错误:', e.message);
                    return this;
                }
            };
            
            return element;
        } catch (e) {
            console.error('[九猫修复] createElement出错:', e.message);
            
            // 尝试创建一个备用元素
            try {
                return originalCreateElement.call(document, 'div');
            } catch (e2) {
                console.error('[九猫修复] 创建备用元素也失败:', e2.message);
                
                // 创建一个最基本的空对象作为最后的回退
                const fallbackElement = {
                    tagName: tagName.toUpperCase(),
                    nodeName: tagName.toUpperCase(),
                    nodeType: 1,
                    textContent: '',
                    innerHTML: '',
                    innerText: '',
                    appendChild: function() { return this; },
                    setAttribute: function() { return this; },
                    getAttribute: function() { return null; },
                    style: {}
                };
                
                return fallbackElement;
            }
        }
    };
    
    // 特殊情况：修复脚本元素
    const originalCreateScriptElement = HTMLScriptElement.prototype.constructor;
    try {
        // 尝试重写脚本元素构造函数
        HTMLScriptElement.prototype.constructor = function() {
            const element = originalCreateScriptElement.apply(this, arguments);
            
            // 确保script元素的textContent永远安全可访问
            if (element && (typeof element.textContent === 'undefined' || element.textContent === null)) {
                Object.defineProperty(element, 'textContent', {
                    get: function() { return this.text || this.innerHTML || ''; },
                    set: function(value) { 
                        this.text = value;
                        this.innerHTML = value;
                    },
                    configurable: true
                });
            }
            
            return element;
        };
    } catch (e) {
        console.error('[九猫修复] 修复脚本元素构造函数失败:', e.message);
    }
    
    // 全局错误处理程序 - 捕获所有与createElement相关的错误
    window.addEventListener('error', function(event) {
        if (event.message && (
            event.message.includes('Cannot read properties of undefined') ||
            event.message.includes('reading \'set\'') ||
            event.message.includes('null is not an object') ||
            event.message.includes('textContent')
        )) {
            console.log('[九猫修复] 捕获到可能与createElement相关的错误:', event.message);
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
    
    // 标记修复已加载
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded) {
        window.__nineCatsFixes.loaded['create-element-fix'] = true;
    }
    
    console.log('[九猫修复] createElement修复脚本加载完成');
})(); 