"""
九猫系统全局日志重定向模块
将所有日志输出重定向到外部存储，减轻系统内存压力
"""
import os
import sys
import time
import logging
import logging.handlers
from datetime import datetime

# 外部存储根目录
EXTERNAL_STORAGE_ROOT = os.environ.get('EXTERNAL_STORAGE_ROOT', 'E:\\艹，又来一次\\九猫')

# 日志目录
LOGS_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "logs")
CONSOLE_LOG_DIR = os.path.join(LOGS_DIR, "console")
APP_LOG_DIR = os.path.join(LOGS_DIR, "app")
ERROR_LOG_DIR = os.path.join(LOGS_DIR, "error")
DEBUG_LOG_DIR = os.path.join(LOGS_DIR, "debug")

# 确保目录存在
for directory in [LOGS_DIR, CONSOLE_LOG_DIR, APP_LOG_DIR, ERROR_LOG_DIR, DEBUG_LOG_DIR]:
    os.makedirs(directory, exist_ok=True)

# 创建日志文件名（使用当前日期和时间）
current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
CONSOLE_LOG_FILE = os.path.join(CONSOLE_LOG_DIR, f"console_{current_time}.log")
APP_LOG_FILE = os.path.join(APP_LOG_DIR, f"app_{current_time}.log")
ERROR_LOG_FILE = os.path.join(ERROR_LOG_DIR, f"error_{current_time}.log")
DEBUG_LOG_FILE = os.path.join(DEBUG_LOG_DIR, f"debug_{current_time}.log")

class FileAndConsoleHandler(logging.Handler):
    """同时将日志输出到文件和控制台的处理器"""
    
    def __init__(self, file_path, mode='a', encoding='utf-8'):
        super().__init__()
        self.file_handler = logging.FileHandler(file_path, mode=mode, encoding=encoding)
        self.console_handler = logging.StreamHandler(sys.stdout)
        
        # 设置格式化器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.file_handler.setFormatter(formatter)
        self.console_handler.setFormatter(formatter)
    
    def emit(self, record):
        self.file_handler.emit(record)
        self.console_handler.emit(record)
    
    def close(self):
        self.file_handler.close()
        self.console_handler.close()
        super().close()

class ConsoleToFileRedirector:
    """将控制台输出重定向到文件"""
    
    def __init__(self, file_path, mode='a', encoding='utf-8', buffer_size=1):
        self.file = open(file_path, mode=mode, encoding=encoding, buffering=buffer_size)
        self.stdout = sys.stdout
        self.stderr = sys.stderr
    
    def start(self):
        """开始重定向"""
        sys.stdout = self
        sys.stderr = self
        print(f"控制台输出已重定向到: {self.file.name}")
    
    def stop(self):
        """停止重定向"""
        sys.stdout = self.stdout
        sys.stderr = self.stderr
        self.file.close()
        print("控制台输出重定向已停止")
    
    def write(self, text):
        """写入文本到文件和原始stdout"""
        self.file.write(text)
        self.stdout.write(text)
        self.file.flush()  # 立即刷新，确保实时写入
    
    def flush(self):
        """刷新缓冲区"""
        self.file.flush()
        self.stdout.flush()

def setup_global_logging():
    """设置全局日志配置"""
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 添加文件和控制台处理器
    handler = FileAndConsoleHandler(APP_LOG_FILE)
    root_logger.addHandler(handler)
    
    # 添加错误日志处理器
    error_handler = logging.FileHandler(ERROR_LOG_FILE)
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    root_logger.addHandler(error_handler)
    
    # 添加调试日志处理器（仅在DEBUG模式下）
    if os.environ.get('FLASK_DEBUG', '0') == '1':
        debug_handler = logging.FileHandler(DEBUG_LOG_FILE)
        debug_handler.setLevel(logging.DEBUG)
        debug_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        root_logger.addHandler(debug_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    
    # 记录初始日志
    root_logger.info(f"全局日志已重定向到: {APP_LOG_FILE}")
    root_logger.info(f"错误日志将保存到: {ERROR_LOG_FILE}")
    if os.environ.get('FLASK_DEBUG', '0') == '1':
        root_logger.info(f"调试日志将保存到: {DEBUG_LOG_FILE}")

def redirect_console_output():
    """重定向控制台输出到文件"""
    redirector = ConsoleToFileRedirector(CONSOLE_LOG_FILE)
    redirector.start()
    return redirector

# 自动清理旧日志文件
def cleanup_old_logs(max_days=7):
    """清理超过指定天数的旧日志文件"""
    import glob
    
    # 当前时间
    current_time = time.time()
    
    # 清理各个日志目录
    for log_dir in [CONSOLE_LOG_DIR, APP_LOG_DIR, ERROR_LOG_DIR, DEBUG_LOG_DIR]:
        # 获取所有日志文件
        log_files = glob.glob(os.path.join(log_dir, "*.log"))
        
        # 检查每个文件的修改时间
        for log_file in log_files:
            # 获取文件修改时间
            file_time = os.path.getmtime(log_file)
            
            # 如果文件超过指定天数未修改，删除它
            if current_time - file_time > max_days * 86400:  # 86400秒 = 1天
                try:
                    os.remove(log_file)
                    print(f"已删除旧日志文件: {log_file}")
                except (PermissionError, OSError) as e:
                    print(f"删除日志文件失败: {log_file}, 错误: {str(e)}")

# 初始化
def initialize():
    """初始化日志重定向"""
    # 清理旧日志
    cleanup_old_logs()
    
    # 设置全局日志
    setup_global_logging()
    
    # 重定向控制台输出
    console_redirector = redirect_console_output()
    
    return console_redirector
