# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Uploads
uploads/*
!uploads/.gitkeep

# OS specific
.DS_Store
Thumbs.db

# Application specific
novels.db
app.log
