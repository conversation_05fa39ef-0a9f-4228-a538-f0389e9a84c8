/**
 * 九猫 - 推理过程加载器
 * 用于从API获取推理过程并显示在页面上
 * 版本: 1.2.0 - 增强推理过程显示功能，统一支持所有分析维度页面
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('推理过程加载器已加载');

    // 加载推理过程
    function loadReasoningContent(novelId, dimension, containerId = 'reasoningContent', chapterId = null) {
        console.log(`开始加载推理过程: novel_id=${novelId}, dimension=${dimension}, chapter_id=${chapterId || 'N/A'}, containerId=${containerId}`);

        // 获取容器元素
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`找不到推理过程容器: ${containerId}`);
            return;
        }

        // 显示加载中状态
        container.innerHTML = `
            <div class="text-center my-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2">正在加载推理过程，请稍候...</p>
            </div>
        `;

        // 确保参数有效
        if (!novelId || !dimension) {
            console.error(`无效的参数: novel_id=${novelId}, dimension=${dimension}`);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <p><i class="fas fa-exclamation-triangle"></i> 参数错误</p>
                    <p class="small">无法加载推理过程，缺少必要参数。</p>
                </div>
            `;
            return;
        }

        // 构建API URL - 明确指示需要完整的推理过程
        let apiUrl;
        if (chapterId) {
            // 章节分析的推理过程API
            apiUrl = `/v3.1/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content?full=true`;
            console.log(`使用章节分析推理过程API: ${apiUrl}`);
        } else {
            // 整本书分析的推理过程API
            apiUrl = `/v3.1/api/novel/${novelId}/analysis/${dimension}/reasoning_content?full=true`;
            console.log(`使用整本书分析推理过程API: ${apiUrl}`);
        }

        // 发送请求
        console.log(`开始发送请求: ${apiUrl}`);

        // 添加请求头，确保正确处理JSON响应
        const fetchOptions = {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        };

        fetch(apiUrl, fetchOptions)
            .then(response => {
                console.log(`收到响应: 状态码=${response.status}, 状态文本=${response.statusText}`);

                // 特殊处理404错误，将其视为"暂无数据"而非错误
                if (response.status === 404) {
                    console.log('响应状态为404，返回暂无数据信息');
                    return { success: false, error: '暂无推理过程数据', status: 404 };
                }

                if (!response.ok) {
                    console.error(`HTTP错误: ${response.status} ${response.statusText}`);
                    throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
                }

                // 检查响应类型
                const contentType = response.headers.get('content-type');
                console.log(`响应内容类型: ${contentType}`);

                if (contentType && contentType.includes('application/json')) {
                    return response.json().catch(error => {
                        console.error('解析JSON响应时出错:', error);
                        throw new Error('解析JSON响应时出错');
                    });
                } else {
                    console.warn(`响应不是JSON格式: ${contentType}`);
                    return response.text().then(text => {
                        console.log(`收到非JSON响应，长度: ${text.length}`);
                        try {
                            // 尝试将文本解析为JSON
                            return JSON.parse(text);
                        } catch (e) {
                            console.error('尝试解析响应文本为JSON时出错:', e);
                            // 返回一个包含文本内容的对象
                            return {
                                success: true,
                                reasoning_content: text,
                                source: 'text_response'
                            };
                        }
                    });
                }
            })
            .then(data => {
                console.log(`处理响应数据: success=${data.success}, 有推理内容=${Boolean(data.reasoning_content)}, 来源=${data.source || '未知'}`);

                if (data.success && data.reasoning_content) {
                    // 处理推理过程内容，确保正确显示结构化内容
                    const content = data.reasoning_content;
                    console.log(`推理内容长度: ${content.length}`);

                    // 检查内容是否包含结构化标记
                    const hasStructure = content.includes('## 分析思路说明') || content.includes('## 详细分析');
                    console.log(`内容是否包含结构化标记: ${hasStructure}`);

                    if (hasStructure) {
                        // 结构化内容，使用更好的格式显示
                        console.log('使用结构化格式显示内容');
                        const formattedHtml = formatStructuredReasoningContent(content);
                        container.innerHTML = formattedHtml;
                    } else {
                        // 普通内容，使用简单的pre标签显示
                        console.log('使用普通pre标签显示内容');
                        container.innerHTML = `<pre class="reasoning-text">${escapeHtml(content)}</pre>`;
                    }

                    console.log('推理过程加载成功，来源:', data.source || '未知');
                } else {
                    // 显示友好的提示信息（不是错误）
                    console.log('没有找到推理过程数据，显示提示信息');
                    container.innerHTML = `
                        <div class="alert alert-info">
                            <p><i class="fas fa-info-circle"></i> 暂无推理过程数据</p>
                            <p class="small text-muted mt-2">可能的原因：</p>
                            <ul class="small text-muted">
                                <li>该分析是在启用推理过程记录功能之前进行的</li>
                                <li>分析过程中未生成推理过程</li>
                                <li>推理过程数据已被清理</li>
                                <li>分析尚未完成，请稍后再查看</li>
                            </ul>
                            <p class="small text-muted mt-2">错误信息: ${data.error || '未知错误'}</p>
                        </div>
                    `;
                    // 使用info级别日志而非警告或错误
                    console.info('暂无推理过程数据', data.status === 404 ? '(资源不存在)' : (data.error || ''));
                }
            })
            .catch(error => {
                // 显示错误信息，但使用更友好的样式
                console.error('加载推理过程时出错:', error);
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <p><i class="fas fa-exclamation-circle"></i> 无法加载推理过程</p>
                        <p class="small text-muted mt-2">错误信息: ${error.message}</p>
                        <p class="small text-muted">系统将在稍后自动重试，您也可以刷新页面重试。</p>
                    </div>
                `;
                // 记录错误但不在控制台显示为错误
                console.warn('加载推理过程时遇到问题:', error.message);

                // 30秒后自动重试
                console.log('30秒后自动重试加载推理过程');
                setTimeout(() => {
                    console.info('自动重试加载推理过程...');
                    loadReasoningContent(novelId, dimension, containerId, chapterId);
                }, 30000);
            });
    }

    // 初始化推理过程展开/收起功能
    function initReasoningContent(novelId, dimension, chapterId = null) {
        console.log(`初始化推理过程功能: novel_id=${novelId}, dimension=${dimension}, chapter_id=${chapterId || 'N/A'}`);

        const toggleReasoningBtn = document.getElementById('toggleReasoningBtn');
        const reasoningContentCollapsed = document.getElementById('reasoningContentCollapsed');
        const reasoningContentFull = document.getElementById('reasoningContentFull');

        if (!toggleReasoningBtn || !reasoningContentCollapsed || !reasoningContentFull) {
            console.error('无法找到推理过程UI元素');
            return;
        }

        // 添加展开/收起事件处理
        toggleReasoningBtn.addEventListener('click', function() {
            if (reasoningContentFull.style.display === 'none') {
                reasoningContentCollapsed.style.display = 'none';
                reasoningContentFull.style.display = 'block';
                toggleReasoningBtn.innerHTML = '<i class="fas fa-compress-alt me-1"></i>收起';
            } else {
                reasoningContentCollapsed.style.display = 'block';
                reasoningContentFull.style.display = 'none';
                toggleReasoningBtn.innerHTML = '<i class="fas fa-expand-alt me-1"></i>展开/收起';
            }
        });

        // 获取推理过程容器
        const container = document.getElementById('reasoningContent');
        if (!container) {
            console.error('找不到推理过程容器');
            return;
        }

        // 检查是否有章节ID数据属性
        if (!chapterId) {
            // 尝试从容器的数据属性中获取章节ID
            chapterId = container.getAttribute('data-chapter-id');
            console.log(`从容器数据属性中获取章节ID: ${chapterId || 'N/A'}`);
        }

        // 如果仍然没有章节ID，尝试从URL中获取
        if (!chapterId) {
            // 尝试从URL中提取章节ID
            const urlMatch = window.location.pathname.match(/\/novel\/\d+\/chapter\/(\d+)/);
            if (urlMatch && urlMatch[1]) {
                chapterId = urlMatch[1];
                console.log(`从URL中提取章节ID: ${chapterId}`);
            }
        }

        // 加载推理过程
        console.log(`加载推理过程: novel_id=${novelId}, dimension=${dimension}, chapter_id=${chapterId || 'N/A'}`);
        loadReasoningContent(novelId, dimension, 'reasoningContent', chapterId);
    }

    // HTML转义函数
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 格式化结构化推理内容
    function formatStructuredReasoningContent(content) {
        if (!content) return '';

        // 检查内容是否包含标题和小标题结构
        const hasStructuredHeadings = /###\s+\d+\.\s+.+/i.test(content);

        if (hasStructuredHeadings) {
            // 使用增强的格式化逻辑处理结构化内容
            return formatRichStructuredContent(content);
        }

        // 分割内容为分析思路、详细分析、深入分析、章节间联系和原始分析五部分
        let approachSection = '';
        let analysisSection = '';
        let detailedSection = '';
        let connectionSection = '';
        let originalSection = '';

        // 提取分析思路部分
        const approachMatch = content.match(/##\s*分析思路说明[:：]?\s*\n([\s\S]*?)(?=\n##|$)/i);
        if (approachMatch && approachMatch[1]) {
            approachSection = approachMatch[1].trim();
        }

        // 提取详细分析部分
        const analysisMatch = content.match(/##\s*详细(?:\S+)?分析[:：]?\s*\n([\s\S]*?)(?=\n##|$)/i);
        if (analysisMatch && analysisMatch[1]) {
            analysisSection = analysisMatch[1].trim();
        }

        // 提取深入分析部分
        const detailedMatch = content.match(/##\s*深入分析[:：]?\s*\n([\s\S]*?)(?=\n##|$)/i);
        if (detailedMatch && detailedMatch[1]) {
            detailedSection = detailedMatch[1].trim();
        }

        // 提取章节间联系分析部分
        const connectionMatch = content.match(/##\s*章节间联系分析[:：]?\s*\n([\s\S]*?)(?=\n##|$)/i);
        if (connectionMatch && connectionMatch[1]) {
            connectionSection = connectionMatch[1].trim();
        }

        // 提取原始分析内容部分
        const originalMatch = content.match(/##\s*原始分析内容[:：]?\s*\n([\s\S]*?)(?=\n##|$)/i);
        if (originalMatch && originalMatch[1]) {
            originalSection = originalMatch[1].trim();
        }

        // 如果没有找到结构化内容，直接返回原始内容
        if (!approachSection && !analysisSection && !detailedSection && !connectionSection && !originalSection) {
            return `<pre class="reasoning-text">${escapeHtml(content)}</pre>`;
        }

        // 构建HTML
        let html = '<div class="reasoning-structured">';

        // 分析思路部分
        if (approachSection) {
            html += `
                <div class="reasoning-section">
                    <h4 class="reasoning-section-title">分析思路说明</h4>
                    <div class="reasoning-section-content">
                        <pre class="reasoning-approach-text">${escapeHtml(approachSection)}</pre>
                    </div>
                </div>
            `;
        }

        // 详细分析部分
        if (analysisSection) {
            // 检查详细分析部分是否包含原始推理内容
            // 如果是原始文本格式，使用pre标签保留格式
            // 如果已经是HTML格式，则直接显示
            const isHtmlContent = /<[a-z][\s\S]*>/i.test(analysisSection);

            html += `
                <div class="reasoning-section">
                    <h4 class="reasoning-section-title">详细分析</h4>
                    <div class="reasoning-section-content">
                        ${isHtmlContent ? analysisSection : `<pre class="reasoning-analysis-text">${escapeHtml(analysisSection)}</pre>`}
                    </div>
                </div>
            `;
        }

        // 深入分析部分
        if (detailedSection) {
            // 检查深入分析部分是否包含HTML内容
            const isHtmlContent = /<[a-z][\s\S]*>/i.test(detailedSection);

            html += `
                <div class="reasoning-section reasoning-detailed-section">
                    <h4 class="reasoning-section-title">深入分析</h4>
                    <div class="reasoning-section-content">
                        ${isHtmlContent ? detailedSection : `<pre class="reasoning-detailed-text">${escapeHtml(detailedSection)}</pre>`}
                    </div>
                </div>
            `;
        }

        // 章节间联系分析部分
        if (connectionSection) {
            // 检查章节间联系分析部分是否包含HTML内容
            const isHtmlContent = /<[a-z][\s\S]*>/i.test(connectionSection);

            html += `
                <div class="reasoning-section reasoning-connection-section">
                    <h4 class="reasoning-section-title">章节间联系分析</h4>
                    <div class="reasoning-section-content">
                        ${isHtmlContent ? connectionSection : `<pre class="reasoning-connection-text">${escapeHtml(connectionSection)}</pre>`}
                    </div>
                </div>
            `;
        }

        // 原始分析内容部分
        if (originalSection) {
            html += `
                <div class="reasoning-section reasoning-original-section">
                    <h4 class="reasoning-section-title">原始分析内容</h4>
                    <div class="reasoning-section-content">
                        <pre class="reasoning-original-text">${escapeHtml(originalSection)}</pre>
                    </div>
                </div>
            `;
        }

        html += '</div>';
        return html;
    }

    // 格式化富结构化内容（包含小标题和项目符号的内容）
    function formatRichStructuredContent(content) {
        if (!content) return '';

        // 分割内容为不同部分
        const sections = [];

        // 提取主要部分
        const mainSections = content.split(/(?=##\s+[^#\n]+)/g);

        // 处理每个主要部分
        mainSections.forEach(section => {
            // 提取标题
            const titleMatch = section.match(/##\s+([^#\n]+)/);
            if (titleMatch) {
                const title = titleMatch[1].trim();
                // 提取内容（标题后的所有内容）
                const sectionContent = section.substring(titleMatch[0].length).trim();

                sections.push({
                    title: title,
                    content: sectionContent,
                    level: 2
                });
            } else {
                // 如果没有标题，将整个部分作为内容
                sections.push({
                    title: '',
                    content: section.trim(),
                    level: 0
                });
            }
        });

        // 构建HTML
        let html = '<div class="reasoning-structured-rich">';

        // 处理每个部分
        sections.forEach(section => {
            if (section.title) {
                html += `<h${section.level} class="reasoning-rich-title">${escapeHtml(section.title)}</h${section.level}>`;
            }

            // 处理内容，将小标题和项目符号转换为HTML
            let processedContent = section.content;

            // 处理小标题（### 1. 标题）
            processedContent = processedContent.replace(/###\s+(\d+\.\s+[^#\n]+)/g, '<h3 class="reasoning-rich-subtitle">$1</h3>');

            // 处理引用的原文（使用引号包裹的文本）
            processedContent = processedContent.replace(/"([^"]+)"/g, '<span class="reasoning-text-quote">"$1"</span>');
            processedContent = processedContent.replace(/"([^"]+)"/g, '<span class="reasoning-text-quote">"$1"</span>');

            // 处理项目符号
            processedContent = processedContent.replace(/•\s+([^\n]+)/g, '<li class="reasoning-rich-bullet">$1</li>');
            processedContent = processedContent.replace(/(?:^|\n)[-*]\s+([^\n]+)/gm, '\n<li class="reasoning-rich-bullet">$1</li>');

            // 将连续的项目符号包装在ul中
            processedContent = processedContent.replace(/(<li class="reasoning-rich-bullet">.*?<\/li>)(\s*<li class="reasoning-rich-bullet">.*?<\/li>)*/gs, '<ul class="reasoning-rich-bullet-list">$&</ul>');

            // 处理数字列表
            processedContent = processedContent.replace(/(?:^|\n)(\d+)\.\s+([^\n]+)/gm, '\n<div class="reasoning-rich-numbered-item"><span class="reasoning-rich-number">$1.</span> $2</div>');

            // 处理段落
            processedContent = processedContent.replace(/(?:^|\n)(?!<[uh]|<li|<div class="reasoning)([^\n]+)/g, '\n<p class="reasoning-rich-paragraph">$1</p>');

            html += `<div class="reasoning-rich-content">${processedContent}</div>`;
        });

        html += '</div>';
        return html;
    }

    // 在页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('推理过程加载器初始化');

        // 查找所有需要加载推理过程的容器
        const containers = document.querySelectorAll('[data-reasoning-container]');

        containers.forEach(container => {
            const novelId = container.getAttribute('data-novel-id');
            const dimension = container.getAttribute('data-dimension');
            const chapterId = container.getAttribute('data-chapter-id');

            if (novelId && dimension) {
                // 设置容器ID
                const containerId = container.id || `reasoningContent_${Math.random().toString(36).substring(2, 11)}`;
                if (!container.id) {
                    container.id = containerId;
                }

                // 加载推理过程，确保传递章节ID
                loadReasoningContent(novelId, dimension, containerId, chapterId);
                console.log(`初始化推理过程加载: novel_id=${novelId}, dimension=${dimension}, chapter_id=${chapterId || 'N/A'}`);
            }
        });
    });

    // 导出函数供其他模块使用
    window.reasoningContentLoader = {
        loadReasoningContent: loadReasoningContent,
        initReasoningContent: initReasoningContent
    };

    // 暴露全局初始化函数，与chapter-outline-chart-fix.js兼容
    window.initReasoningContent = initReasoningContent;
})();
