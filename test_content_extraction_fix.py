#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试写作内容提取修复效果

验证每轮写作内容是否能够完整提取和传递
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.test_service import TestService

def test_content_extraction():
    """测试内容提取功能"""
    
    # 模拟生成的内容（包含规划部分和实际内容）
    test_content = """
# 章节框架规划

## 第一步：章节定位与主题
本章节主要描述主角的成长历程...

## 第二步：情节与场景规划
设定在现代都市背景下...

## 第三步：人物规划
主角：李明，25岁...

# 第1章 新的开始

李明站在公司大楼前，看着眼前这座高耸入云的建筑，心中五味杂陈。今天是他入职第一天，也是他人生新篇章的开始。

"你好，请问你是新来的实习生吗？"一个温和的声音从身后传来。

李明转过身，看到一位穿着职业装的女性正微笑着看着他。"是的，我是李明，今天第一天上班。"

"我是人事部的张经理，欢迎加入我们公司。"张经理伸出手，"我带你去办理入职手续。"

跟着张经理走进大楼，李明感受到了现代化办公环境的氛围。电梯里，张经理向他介绍着公司的基本情况。

"我们公司主要从事软件开发，你被分配到技术部门。"张经理说道，"你的直属上司是王总监，他是个很好相处的人。"

电梯停在了十五楼，门开后，李明看到了一个开放式的办公区域。许多年轻人正在电脑前忙碌着，整个环境充满了活力。

"这就是你的工作环境。"张经理指着一个靠窗的位置说，"那里就是你的座位。"

李明走向自己的座位，心中充满了期待。这是他大学毕业后的第一份正式工作，他决心要在这里好好表现，证明自己的能力。

坐下后，他打开电脑，开始熟悉工作环境。旁边的同事主动过来打招呼，介绍自己和工作内容。

"欢迎新同事！"大家纷纷表示欢迎。

李明感到很温暖，这个团队的氛围比他想象的还要好。他相信，在这里他一定能够学到很多东西，也能为公司做出贡献。

下午，王总监找到了李明，给他安排了第一个任务。"这是一个简单的项目，你可以先熟悉一下我们的开发流程。"

李明认真地听着王总监的讲解，不时地做着笔记。他知道，这是他职业生涯的重要起点。

一天的工作结束后，李明走出公司大楼，回头看了看这座即将陪伴他成长的建筑。夕阳西下，大楼在金色的阳光中显得格外壮观。

他深深地吸了一口气，对未来充满了信心。明天，又是新的一天，又是新的挑战。他已经准备好了。
"""
    
    print("🔧 测试写作内容提取修复效果")
    print("=" * 60)
    
    # 测试原始内容长度
    print(f"📊 原始内容总长度: {len(test_content)} 字符")
    print(f"📊 原始内容字数: {TestService._count_words_accurately(test_content)} 字")
    
    # 测试完整提取功能
    extracted_content = TestService._extract_complete_generated_content(
        test_content, 1, "测试阶段"
    )
    
    print(f"📊 提取后内容长度: {len(extracted_content)} 字符")
    print(f"📊 提取后内容字数: {TestService._count_words_accurately(extracted_content)} 字")
    
    # 显示提取的内容（前500字符）
    print("\n📝 提取的内容预览:")
    print("-" * 40)
    print(extracted_content[:500] + "..." if len(extracted_content) > 500 else extracted_content)
    
    # 验证是否移除了规划内容
    planning_keywords = ["章节框架规划", "第一步", "第二步", "第三步"]
    has_planning = any(keyword in extracted_content for keyword in planning_keywords)
    
    print(f"\n✅ 规划内容移除: {'失败' if has_planning else '成功'}")
    print(f"✅ 章节内容保留: {'成功' if '第1章' in extracted_content else '失败'}")
    print(f"✅ 正文内容保留: {'成功' if '李明站在公司大楼前' in extracted_content else '失败'}")
    
    return extracted_content

def test_multi_round_extraction():
    """测试多轮内容提取"""
    
    print("\n🔄 测试多轮内容提取传递")
    print("=" * 60)
    
    # 模拟第1轮基础内容
    round1_content = """
# 第1章 测试章节

这是第1轮生成的基础内容，包含了基本的故事框架和人物设定。内容相对简单，但已经具备了完整的故事结构。

主角张三是一个普通的上班族，今天遇到了一件改变他人生的事情。

"今天真是特别的一天。"张三自言自语道。

他走在熟悉的街道上，却感觉一切都变得不同了。
"""
    
    # 模拟第2轮扩写优化内容
    round2_content = """
# 第1章 测试章节

这是第1轮生成的基础内容，包含了基本的故事框架和人物设定。内容相对简单，但已经具备了完整的故事结构。

主角张三是一个普通的上班族，今天遇到了一件改变他人生的事情。他在一家中型企业工作，每天过着朝九晚五的生活。

"今天真是特别的一天。"张三自言自语道。

他走在熟悉的街道上，却感觉一切都变得不同了。街道两旁的梧桐叶子已经开始泛黄，秋天的气息越来越浓。

突然，他的手机响了。是一个陌生的号码。

"喂，请问是张三先生吗？"电话那头传来一个女性的声音。

"是的，我是张三。请问您是？"

"我是律师事务所的李律师，有一件关于您的重要事情需要面谈。"

张三感到很困惑，他从来没有和律师事务所打过交道。

"什么事情？"他问道。

"这个在电话里不方便说，您能来我们事务所一趟吗？"

张三犹豫了一下，最终还是答应了。他改变了回家的路线，朝着律师事务所的方向走去。

一路上，他的心情忐忑不安，不知道等待他的会是什么。
"""
    
    print("第1轮基础内容:")
    round1_extracted = TestService._extract_complete_generated_content(round1_content, 1, "第1轮基础")
    print(f"  字数: {TestService._count_words_accurately(round1_extracted)} 字")
    
    print("\n第2轮扩写优化:")
    round2_extracted = TestService._extract_complete_generated_content(round2_content, 1, "第2轮扩写")
    print(f"  字数: {TestService._count_words_accurately(round2_extracted)} 字")
    
    # 验证字数增长
    word_increase = TestService._count_words_accurately(round2_extracted) - TestService._count_words_accurately(round1_extracted)
    print(f"\n📈 字数增长: {word_increase} 字")
    print(f"✅ 内容扩展: {'成功' if word_increase > 0 else '失败'}")
    
    return round1_extracted, round2_extracted

if __name__ == "__main__":
    print("🧪 九猫写作内容提取修复测试")
    print("=" * 80)
    
    # 测试基本提取功能
    test_content_extraction()
    
    # 测试多轮提取
    test_multi_round_extraction()
    
    print("\n🎉 测试完成！")
    print("\n💡 修复说明:")
    print("1. 每轮写作都使用 _extract_complete_generated_content 完整提取内容")
    print("2. 避免了内容在轮次传递中的丢失")
    print("3. 确保字数统计的准确性")
    print("4. 保持了写作质量的连续性")
