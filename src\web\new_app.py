"""
新版九猫小说分析系统应用入口
"""
import os
import sys
import logging
import time
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, abort
from sqlalchemy.orm import Session
from sqlalchemy import func

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入配置
import config

# 导入数据库模型
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.api_log import ApiLog
from src.db.connection import Session, engine
from src.models.base import Base

# 导入API客户端
try:
    from src.api.deepseek_client import DeepSeekClient
    from src.api.analysis import NovelAnalyzer
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("无法导入DeepSeekClient或NovelAnalyzer，某些功能可能不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(config.LOG_DIR if hasattr(config, 'LOG_DIR') else 'logs', 'new_app.log'))
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_CONTENT_LENGTH

# 确保所有数据库表已创建
Base.metadata.create_all(engine)
logger.info("确认所有数据库表已创建")

# 导入路由
from src.web.routes.new_routes import new_bp

# 注册蓝图
app.register_blueprint(new_bp, url_prefix='')

# 主页路由
@app.route('/')
def index():
    return redirect(url_for('new.index'))

# 小说列表页面重定向
@app.route('/novels')
def novels():
    return redirect(url_for('new.novels'))

# 上传小说页面重定向
@app.route('/upload')
def upload_novel():
    return redirect(url_for('new.upload_novel'))

# 查看小说详情页面重定向
@app.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    return redirect(url_for('new.view_novel', novel_id=novel_id))

# 查看分析结果页面重定向
@app.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    return redirect(url_for('new.analysis', novel_id=novel_id, dimension=dimension))

# 系统监控页面重定向
@app.route('/system_monitor')
def system_monitor():
    return redirect(url_for('new.system_monitor'))

# API路由 - 开始分析
@app.route('/api/novel/<int:novel_id>/analyze', methods=['POST'])
def api_analyze_novel(novel_id):
    """启动小说分析"""
    try:
        # 获取请求数据
        data = request.json
        dimensions = data.get('dimensions', [])

        if not dimensions:
            return jsonify({
                'success': False,
                'error': '未指定分析维度'
            }), 400

        # 获取小说
        session = Session()
        try:
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 创建分析器
            analyzer = NovelAnalyzer()

            # 在后台线程中启动分析
            import threading
            analysis_thread = threading.Thread(
                target=_analyze_novel_in_background,
                args=(novel_id, dimensions, analyzer)
            )
            analysis_thread.daemon = True
            analysis_thread.start()

            return jsonify({
                'success': True,
                'message': '分析已开始，请通过进度API监控进度',
                'dimensions': dimensions
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"启动分析时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def _analyze_novel_in_background(novel_id, dimensions, analyzer):
    """在后台分析小说"""
    try:
        logger.info(f"开始在后台分析小说 {novel_id}")
        logger.info(f"要分析的维度: {dimensions}")

        # 为每个维度单独处理
        for dimension in dimensions:
            session = None
            try:
                # 每个维度创建新的会话，避免会话冲突
                session = Session()

                # 在新会话中获取小说对象
                novel = session.query(Novel).filter_by(id=novel_id).first()

                if not novel:
                    logger.error(f"无法找到小说 ID: {novel_id}")
                    continue

                # 记录小说信息，避免后续使用已分离的对象
                novel_title = novel.title
                logger.info(f"分析小说: {novel_title} (ID: {novel_id}) 的维度: {dimension}")

                # 检查是否已有此维度的分析结果
                existing_result = session.query(AnalysisResult).filter_by(
                    novel_id=novel_id,
                    dimension=dimension
                ).first()

                if existing_result:
                    logger.info(f"删除小说 {novel_id} 的现有 {dimension} 分析结果，准备重新分析")
                    session.delete(existing_result)
                    session.commit()

                # 关闭当前会话，让analyze_dimension使用新的会话
                session.close()
                session = None

                # 导入单维度分析模块
                from src.api.analyze_dimension import analyze_dimension

                # 分析单个维度 - analyze_dimension内部会创建自己的会话
                # 这里只传递必要信息而不是novel对象，避免使用已分离的对象
                # 创建一个临时Novel对象，使用正确的参数
                temp_novel = Novel(
                    title=novel_title,
                    content="",  # 临时对象，内容可以为空
                    author=None
                )
                # 手动设置ID
                temp_novel.id = novel_id

                # 分析维度
                result = analyze_dimension(temp_novel, dimension)

                # 检查分析结果并更新状态
                if result:
                    # 创建新会话更新小说状态
                    update_session = Session()
                    try:
                        update_novel = update_session.query(Novel).filter_by(id=novel_id).first()
                        if update_novel:
                            # 不在这里设置is_analyzed，而是在所有维度分析完成后设置
                            # 记录日志
                            logger.info(f"小说 {novel_id} 的维度 {dimension} 分析已完成")
                        else:
                            logger.error(f"更新分析状态时无法找到小说 ID: {novel_id}")
                    finally:
                        update_session.close()
                else:
                    logger.error(f"小说 {novel_id} 的维度 {dimension} 分析失败")

            except Exception as e:
                logger.error(f"分析小说 {novel_id} 的维度 {dimension} 时出错: {str(e)}", exc_info=True)
            finally:
                # 确保会话被关闭
                if session is not None:
                    try:
                        session.close()
                    except Exception as close_error:
                        logger.error(f"关闭会话时出错: {str(close_error)}")

        # 最后检查所有维度是否都已分析完成
        final_session = None
        try:
            final_session = Session()

            # 获取已完成的维度数量
            completed_dimensions = final_session.query(AnalysisResult).filter_by(
                novel_id=novel_id
            ).count()

            # 获取所有13个维度
            all_dimensions = [
                'language_style', 'rhythm_pacing', 'structure', 'sentence_variation',
                'paragraph_length', 'perspective_shifts', 'paragraph_flow', 'novel_characteristics',
                'world_building', 'chapter_outline', 'character_relationships', 'opening_effectiveness',
                'climax_pacing'
            ]

            # 获取已完成的维度列表
            completed_dimension_list = [result.dimension for result in
                                       final_session.query(AnalysisResult.dimension).filter_by(
                                           novel_id=novel_id
                                       ).all()]

            # 计算未完成的维度
            remaining_dimensions = set(all_dimensions) - set(completed_dimension_list)

            # 更新小说的分析状态
            novel = final_session.query(Novel).filter_by(id=novel_id).first()
            if novel:
                # 只有当所有13个维度都已分析完成时，才将is_analyzed设置为True
                novel.is_analyzed = (len(completed_dimension_list) == 13)
                final_session.commit()

                logger.info(f"小说 {novel_id} 的分析状态：已完成 {len(completed_dimension_list)}/13 个维度")
                if remaining_dimensions:
                    logger.info(f"未完成的维度: {', '.join(remaining_dimensions)}")
            else:
                logger.error(f"无法找到小说 ID: {novel_id}")
        except Exception as e:
            logger.error(f"更新小说分析状态时出错: {str(e)}", exc_info=True)
        finally:
            # 确保最终会话被关闭
            if final_session is not None:
                try:
                    final_session.close()
                except:
                    pass

    except Exception as e:
        logger.error(f"后台分析小说时出错: {str(e)}", exc_info=True)

# API路由 - 获取分析进度
@app.route('/api/novel/<int:novel_id>/analysis/<dimension>/progress')
def api_get_analysis_progress(novel_id, dimension):
    """获取分析进度"""
    try:
        # 检查分析结果是否已存在
        session = Session()
        try:
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if result:
                # 分析已完成
                return jsonify({
                    'success': True,
                    'status': 'completed',
                    'progress': 100,
                    'message': '分析已完成'
                })

            # 检查小说是否存在
            novel = session.query(Novel).filter_by(id=novel_id).first()
            if not novel:
                return jsonify({
                    'success': False,
                    'error': f'小说 ID {novel_id} 不存在'
                }), 404

            # 检查中间结果表中是否有进度记录
            try:
                from src.models.intermediate_result import IntermediateResult
                intermediate = session.query(IntermediateResult).filter_by(
                    novel_id=novel_id,
                    dimension=dimension
                ).order_by(IntermediateResult.updated_at.desc()).first()

                if intermediate:
                    # 检查是否有进度记录
                    progress = 10  # 默认进度为10%
                    estimated_time = "计算中..."

                    # 检查是否有progress属性
                    if hasattr(intermediate, 'progress') and intermediate.progress is not None:
                        progress = intermediate.progress

                    # 检查是否有meta_data属性和预计剩余时间
                    if hasattr(intermediate, 'meta_data') and intermediate.meta_data:
                        if isinstance(intermediate.meta_data, dict) and 'estimated_time' in intermediate.meta_data:
                            estimated_time = intermediate.meta_data['estimated_time']
                        elif isinstance(intermediate.meta_data, str):
                            # 如果meta_data是字符串，可能是JSON字符串
                            try:
                                import json
                                metadata_dict = json.loads(intermediate.meta_data)
                                if 'estimated_time' in metadata_dict:
                                    estimated_time = metadata_dict['estimated_time']
                            except:
                                pass

                    return jsonify({
                        'success': True,
                        'status': 'in_progress',
                        'progress': progress,
                        'message': f'正在分析 {dimension}',
                        'estimated_time': estimated_time
                    })
            except Exception as e:
                logger.warning(f"检查中间结果时出错: {str(e)}")
                # 继续执行，不要因为中间结果表的问题而中断

            # 检查小说的分析状态
            if novel.is_analyzed:
                # 如果小说已标记为已分析，但找不到该维度的分析结果，可能是数据不一致
                # 返回一个特殊状态，前端可以据此决定是否重新分析
                return jsonify({
                    'success': True,
                    'status': 'inconsistent',
                    'progress': 0,
                    'message': f'小说已标记为已分析，但找不到 {dimension} 的分析结果'
                })

            # 检查是否有分析任务正在进行中
            # 这里可以根据实际情况实现进度查询
            # 如果没有进度记录，返回固定的初始进度
            return jsonify({
                'success': True,
                'status': 'pending',
                'progress': 0,
                'message': '等待分析开始'
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取分析进度时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# API路由 - 取消分析
@app.route('/api/novel/<int:novel_id>/analyze/cancel', methods=['POST'])
def api_cancel_analysis(novel_id):
    """取消分析"""
    # 简化版本：直接返回成功
    return jsonify({
        'success': True,
        'message': '分析已取消'
    })

# API路由 - 获取系统状态
@app.route('/api/system/status')
def api_system_status():
    """获取系统状态"""
    try:
        # 获取内存使用情况
        memory_used_percent = 0
        try:
            import psutil
            memory = psutil.virtual_memory()
            memory_used_percent = memory.percent
        except ImportError:
            pass

        # 获取API调用情况
        api_used = 0
        api_limit = 100
        try:
            session = Session()
            try:
                # 获取今日API调用次数
                from datetime import datetime
                today = datetime.now().date()
                from sqlalchemy import func
                from src.models.api_log import ApiLog

                api_used = session.query(func.count(ApiLog.id)).filter(
                    func.date(ApiLog.timestamp) == today
                ).scalar() or 0
            finally:
                session.close()
        except Exception:
            pass

        return jsonify({
            'success': True,
            'memory': {
                'used_percent': memory_used_percent
            },
            'api': {
                'used': api_used,
                'limit': api_limit
            }
        })
    except Exception as e:
        logger.error(f"获取系统状态时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# API路由 - 获取系统资源
@app.route('/api/system/resources')
def api_system_resources():
    """获取系统资源使用情况"""
    try:
        import psutil

        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.5)

        # 内存使用情况
        memory = psutil.virtual_memory()

        # 磁盘使用情况
        disk = psutil.disk_usage('/')

        # 网络使用情况
        net_io_counters = psutil.net_io_counters()

        # 等待一秒，再次获取网络数据以计算速率
        time.sleep(0.5)
        net_io_counters_new = psutil.net_io_counters()

        bytes_sent_per_second = net_io_counters_new.bytes_sent - net_io_counters.bytes_sent
        bytes_recv_per_second = net_io_counters_new.bytes_recv - net_io_counters.bytes_recv

        return jsonify({
            'success': True,
            'cpu': {
                'percent': cpu_percent
            },
            'memory': {
                'total': memory.total,
                'used': memory.used,
                'free': memory.available,
                'percent': memory.percent
            },
            'disk': {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': disk.percent
            },
            'network': {
                'bytes_sent': net_io_counters_new.bytes_sent,
                'bytes_recv': net_io_counters_new.bytes_recv,
                'bytes_sent_per_second': bytes_sent_per_second,
                'bytes_recv_per_second': bytes_recv_per_second,
                'total_per_second': bytes_sent_per_second + bytes_recv_per_second
            }
        })
    except ImportError:
        return jsonify({
            'success': False,
            'error': 'psutil模块未安装，无法获取系统资源信息'
        })
    except Exception as e:
        logger.error(f"获取系统资源信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API路由 - 获取API调用统计
@app.route('/api/system/api_stats')
def api_system_api_stats():
    """获取API调用统计"""
    try:
        session = Session()
        try:
            # 获取总调用次数
            total_calls = session.query(func.count(ApiLog.id)).scalar() or 0

            # 获取今日调用次数
            today = datetime.now().date()
            today_calls = session.query(func.count(ApiLog.id)).filter(
                func.date(ApiLog.timestamp) == today
            ).scalar() or 0

            # 获取本小时调用次数
            hour_start = datetime.now().replace(minute=0, second=0, microsecond=0)
            hour_calls = session.query(func.count(ApiLog.id)).filter(
                ApiLog.timestamp >= hour_start
            ).scalar() or 0

            # 按维度统计
            dimension_stats = []
            dimensions = session.query(ApiLog.analysis_type).distinct().all()

            for dimension in dimensions:
                if not dimension[0]:
                    continue

                count = session.query(func.count(ApiLog.id)).filter(
                    ApiLog.analysis_type == dimension[0]
                ).scalar() or 0

                # 使用0作为平均处理时间，因为ApiLog模型可能没有processing_time属性
                avg_time = 0

                dimension_stats.append({
                    'dimension': dimension[0],
                    'count': count,
                    'avg_time': avg_time
                })

            # 按调用次数排序
            dimension_stats.sort(key=lambda x: x['count'], reverse=True)

            return jsonify({
                'success': True,
                'total_calls': total_calls,
                'today_calls': today_calls,
                'today_limit': config.API_CALL_LIMIT_PER_DAY if hasattr(config, 'API_CALL_LIMIT_PER_DAY') else 1000,
                'hour_calls': hour_calls,
                'hour_limit': config.API_CALL_LIMIT_PER_HOUR if hasattr(config, 'API_CALL_LIMIT_PER_HOUR') else 100,
                'by_dimension': dimension_stats
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取API调用统计时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API路由 - 获取系统日志
@app.route('/api/system/logs')
def api_system_logs():
    """获取系统日志"""
    try:
        log_level = request.args.get('level', 'all')
        limit = int(request.args.get('limit', 50))
        offset = int(request.args.get('offset', 0))

        # 模拟日志数据
        logs = []
        for i in range(limit):
            log_type = ['info', 'warning', 'error', 'debug'][i % 4]
            if log_level != 'all' and log_type != log_level:
                continue

            logs.append({
                'timestamp': datetime.now().isoformat(),
                'level': log_type.upper(),
                'message': f'这是一条{log_type}日志，用于测试系统监控页面。'
            })

        return jsonify({
            'success': True,
            'logs': logs
        })
    except Exception as e:
        logger.error(f"获取系统日志时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API路由 - 获取数据库统计
@app.route('/api/system/database')
def api_system_database():
    """获取数据库统计"""
    try:
        # 模拟数据库统计数据
        return jsonify({
            'success': True,
            'connections': {
                'active': 5,
                'max': 30
            },
            'size': 1024 * 1024 * 10,  # 10MB
            'tables': [
                {
                    'name': 'novels',
                    'rows': 10,
                    'size': 1024 * 1024 * 2
                },
                {
                    'name': 'analysis_results',
                    'rows': 50,
                    'size': 1024 * 1024 * 5
                },
                {
                    'name': 'chapters',
                    'rows': 100,
                    'size': 1024 * 1024 * 3
                }
            ]
        })
    except Exception as e:
        logger.error(f"获取数据库统计时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API路由 - 获取系统信息
@app.route('/api/system/info')
def api_system_info():
    """获取系统信息"""
    try:
        import platform
        import sys
        import flask
        import sqlalchemy

        return jsonify({
            'success': True,
            'os': platform.platform(),
            'python_version': platform.python_version(),
            'flask_version': flask.__version__,
            'sqlalchemy_version': sqlalchemy.__version__,
            'start_time': datetime.now().isoformat(),
            'uptime': 3600,  # 1小时
            'threads': 10,
            'pid': os.getpid(),
            'env_vars': {
                'DEBUG': config.DEBUG,
                'HOST': config.HOST,
                'PORT': config.PORT,
                'DATABASE_URI': config.DATABASE_URI
            }
        })
    except Exception as e:
        logger.error(f"获取系统信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 错误处理
@app.errorhandler(404)
def page_not_found(e):
    return render_template('new_error.html', error_code=404, error_message='页面未找到'), 404

@app.errorhandler(500)
def internal_server_error(e):
    return render_template('new_error.html', error_code=500, error_message='服务器内部错误'), 500

if __name__ == '__main__':
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=config.DEBUG,
        threaded=True,
        use_reloader=False
    )
