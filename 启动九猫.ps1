# 九猫小说分析系统启动脚本 (PowerShell版本)
# 此脚本会启动九猫系统，并自动打开浏览器

# 设置工作目录为脚本所在目录
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

# 显示启动消息
Write-Host "====================================="
Write-Host "   九猫小说分析系统正在启动..."
Write-Host "====================================="
Write-Host ""
Write-Host "系统将在后台运行，浏览器将在10秒钟后自动打开。"
Write-Host "请不要关闭此窗口，否则系统将停止运行。"
Write-Host ""

# 设置环境变量
$env:DEBUG = "False"
$env:USE_REAL_API = "True"
$env:HOST = "127.0.0.1"

# 启动Python进程
$pythonProcess = Start-Process -FilePath "python" -ArgumentList "run.py" -PassThru -NoNewWindow

# 等待10秒钟确保服务启动
Write-Host "等待系统启动..."
Start-Sleep -Seconds 10

# 打开浏览器
Write-Host "正在打开浏览器..."
Start-Process "http://localhost:5001"

Write-Host "九猫系统已成功启动！"
Write-Host "请在浏览器中使用系统。"
Write-Host "关闭此窗口将停止系统运行。"

# 等待Python进程结束
$pythonProcess.WaitForExit()
