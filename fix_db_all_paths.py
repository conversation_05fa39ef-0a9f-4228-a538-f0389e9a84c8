"""
九猫 - 修复数据库脚本
尝试所有可能的数据库路径
"""

import os
import sys
import sqlite3
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def fix_database():
    """修复数据库"""
    # 可能的数据库文件路径
    possible_paths = [
        'data/novels.db',
        'src/data/novels.db',
        'novels.db',
        'instance/novels.db',
        'data/app.db',
        'src/data/app.db',
        'app.db',
        'instance/app.db'
    ]
    
    for db_path in possible_paths:
        logger.info(f"尝试数据库路径: {db_path}")
        
        if not os.path.exists(db_path):
            logger.info(f"数据库文件不存在: {db_path}")
            continue
        
        logger.info(f"找到数据库文件: {db_path}")
        
        try:
            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查是否有analysis_results表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='analysis_results'")
            if not cursor.fetchone():
                logger.info(f"数据库 {db_path} 中没有analysis_results表")
                conn.close()
                continue
            
            # 开始事务
            cursor.execute("BEGIN TRANSACTION")
            
            # 查询要修复的分析结果
            cursor.execute("""
                SELECT id FROM analysis_results 
                WHERE novel_id = 4 AND dimension = 'character_relationships'
            """)
            
            result = cursor.fetchone()
            
            if result:
                result_id = result[0]
                logger.info(f"找到要修复的分析结果ID: {result_id}")
                
                # 删除现有分析结果
                cursor.execute("""
                    DELETE FROM analysis_results 
                    WHERE id = ?
                """, (result_id,))
                
                logger.info("已删除现有分析结果")
            
            # 创建有效的内容
            valid_content = """# 分析过程中出错

## 错误详情
分析维度 **character_relationships** 时遇到了问题。

## 错误信息
```
name 'stats_start' is not defined
```

## 建议操作
请尝试以下解决方法：
1. 刷新页面并重新尝试分析
2. 检查小说文本是否过长或包含特殊字符
3. 确认API连接正常

如果问题持续存在，请联系系统管理员。
"""
            
            # 创建有效的元数据
            valid_metadata = {
                "processing_time": 0,
                "chunk_count": 0,
                "api_calls": 0,
                "tokens_used": 0,
                "cost": 0,
                "fixed_by_script": True,
                "fix_timestamp": datetime.now().isoformat()
            }
            
            # 插入新的分析结果
            cursor.execute("""
                INSERT INTO analysis_results 
                (novel_id, dimension, content, analysis_metadata, created_at, updated_at) 
                VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))
            """, (
                4, 
                'character_relationships', 
                valid_content, 
                json.dumps(valid_metadata)
            ))
            
            # 提交事务
            cursor.execute("COMMIT")
            
            # 验证修复结果
            cursor.execute("""
                SELECT id FROM analysis_results 
                WHERE novel_id = 4 AND dimension = 'character_relationships'
            """)
            
            new_result = cursor.fetchone()
            
            if not new_result:
                logger.error(f"修复失败：未找到新插入的分析结果，数据库路径: {db_path}")
                conn.close()
                continue
            
            new_result_id = new_result[0]
            logger.info(f"修复成功，新分析结果ID: {new_result_id}，数据库路径: {db_path}")
            
            # 关闭连接
            conn.close()
            
            return True
        except Exception as e:
            logger.error(f"修复数据库时出错: {str(e)}，数据库路径: {db_path}")
            try:
                # 回滚事务
                cursor.execute("ROLLBACK")
            except:
                pass
            continue
    
    logger.error("所有数据库路径都尝试失败")
    return False

def main():
    """主函数"""
    logger.info("开始修复数据库")
    
    # 修复数据库
    success = fix_database()
    
    if success:
        logger.info("数据库修复成功")
        logger.info("请重启服务器以应用更改")
    else:
        logger.error("数据库修复失败")

if __name__ == "__main__":
    main()
