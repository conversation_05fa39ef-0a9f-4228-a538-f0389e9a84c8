<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫 - 章节分析修复工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .log {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-entry.info {
            background-color: #e8f4f8;
        }
        .log-entry.warn {
            background-color: #fff3cd;
        }
        .log-entry.error {
            background-color: #f8d7da;
        }
        .log-timestamp {
            color: #666;
            margin-right: 10px;
        }
        .log-level {
            font-weight: bold;
            margin-right: 10px;
        }
        .log-message {
            word-break: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>九猫 - 章节分析修复工具</h1>
        <p>这个工具可以帮助您修复章节分析页面的结果显示问题。</p>
        <p>使用方法：</p>
        <ol>
            <li>打开章节分析页面</li>
            <li>在新标签页中打开这个工具</li>
            <li>点击"应用修复"按钮</li>
            <li>返回章节分析页面，刷新页面</li>
        </ol>
        <button id="applyFixBtn" class="btn">应用修复</button>
        <div class="log" id="logContainer"></div>
    </div>

    <script>
        // 日志函数
        function log(message, level = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            
            const timestampSpan = document.createElement('span');
            timestampSpan.className = 'log-timestamp';
            timestampSpan.textContent = timestamp;
            
            const levelSpan = document.createElement('span');
            levelSpan.className = 'log-level';
            levelSpan.textContent = level.toUpperCase();
            
            const messageSpan = document.createElement('span');
            messageSpan.className = 'log-message';
            messageSpan.textContent = message;
            
            logEntry.appendChild(timestampSpan);
            logEntry.appendChild(levelSpan);
            logEntry.appendChild(messageSpan);
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 创建修复脚本
        function createFixScript() {
            log('创建修复脚本');
            
            const script = document.createElement('script');
            script.textContent = `
/**
 * 九猫 - 章节分析结果修复脚本
 * 专门用于修复章节分析页面的结果显示问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[章节分析结果修复] 脚本已加载');

    // 配置
    const CONFIG = {
        autoFix: true,           // 是否自动修复
        fixDelay: 500,           // 修复延迟（毫秒）
        checkInterval: 2000,     // 检查间隔（毫秒）
        maxRetries: 5,           // 最大重试次数
        debug: true              // 是否启用调试日志
    };

    // 安全的日志函数
    function safeLog(message, level = 'log') {
        if (!CONFIG.debug && level === 'log') return;
        
        try {
            if (level === 'error') {
                console.error(\`[章节分析结果修复] \${message}\`);
            } else if (level === 'warn') {
                console.warn(\`[章节分析结果修复] \${message}\`);
            } else {
                console.log(\`[章节分析结果修复] \${message}\`);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }

    // 获取当前小说ID和章节ID
    function getCurrentIds() {
        try {
            // 从URL中提取小说ID和章节ID
            const match = window.location.pathname.match(/\\/novel\\/(\\d+)\\/chapter\\/(\\d+)/);
            if (match) {
                return {
                    novelId: match[1],
                    chapterId: match[2]
                };
            }

            // 如果URL不匹配，尝试从页面元素中获取
            const breadcrumb = document.querySelector('.breadcrumb');
            if (breadcrumb) {
                const links = breadcrumb.querySelectorAll('a');
                for (const link of links) {
                    const href = link.getAttribute('href');
                    if (href) {
                        const novelMatch = href.match(/\\/novel\\/(\\d+)/);
                        if (novelMatch) {
                            const chapterMatch = href.match(/\\/chapter\\/(\\d+)/);
                            if (chapterMatch) {
                                return {
                                    novelId: novelMatch[1],
                                    chapterId: chapterMatch[1]
                                };
                            }
                        }
                    }
                }
            }

            safeLog('无法从URL或页面元素中获取小说ID和章节ID', 'warn');
            return null;
        } catch (e) {
            safeLog(\`获取当前ID时出错: \${e.message}\`, 'error');
            return null;
        }
    }

    // 获取分析结果
    function fetchAnalysisResult(novelId, chapterId, dimension) {
        return new Promise((resolve, reject) => {
            safeLog(\`从API获取章节分析结果: novelId=\${novelId}, chapterId=\${chapterId}, dimension=\${dimension}\`);
            
            fetch(\`/api/novel/\${novelId}/chapter/\${chapterId}/analysis/\${dimension}\`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(\`HTTP错误: \${response.status}\`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && data.success && data.result && data.result.content) {
                        safeLog(\`成功获取章节分析结果: dimension=\${dimension}\`);
                        resolve(data.result);
                    } else if (data && data.result) {
                        // 即使没有content字段，也尝试使用结果
                        safeLog(\`获取到章节分析结果，但缺少content字段: dimension=\${dimension}\`, 'warn');
                        resolve(data.result);
                    } else {
                        reject(new Error('分析结果为空或无效'));
                    }
                })
                .catch(error => {
                    safeLog(\`获取章节分析结果时出错: \${error.message}\`, 'error');
                    reject(error);
                });
        });
    }

    // 更新分析结果显示
    function updateAnalysisResult(result) {
        try {
            safeLog('更新分析结果显示');
            
            // 查找分析结果容器
            const resultContainer = document.querySelector('.analysis-result');
            if (!resultContainer) {
                safeLog('找不到分析结果容器', 'error');
                return false;
            }

            // 查找内容容器
            const contentContainer = resultContainer.querySelector('.analysis-content');
            if (!contentContainer) {
                safeLog('找不到内容容器', 'error');
                return false;
            }

            // 更新内容
            if (result.content) {
                contentContainer.innerHTML = result.content;
                safeLog('成功更新分析结果内容');
            }

            // 更新元数据
            const metadataContainer = resultContainer.querySelector('#analysis-metadata');
            if (metadataContainer && result.metadata) {
                metadataContainer.setAttribute('data-metadata', JSON.stringify(result.metadata));
                const preElement = metadataContainer.querySelector('pre.metadata-json');
                if (preElement) {
                    preElement.textContent = JSON.stringify(result.metadata, null, 2);
                }
                safeLog('成功更新分析结果元数据');
            }

            // 更新日志
            const logsContainer = resultContainer.querySelector('.logs-container');
            if (logsContainer && result.analysis_logs) {
                logsContainer.innerHTML = '';
                result.analysis_logs.forEach(log => {
                    const logEntry = document.createElement('div');
                    logEntry.className = \`log-entry \${log.level || 'info'}\`;
                    
                    const timestamp = document.createElement('span');
                    timestamp.className = 'log-timestamp';
                    timestamp.textContent = log.timestamp || new Date().toISOString();
                    
                    const level = document.createElement('span');
                    level.className = 'log-level';
                    level.textContent = log.level || 'info';
                    
                    const message = document.createElement('span');
                    message.className = 'log-message';
                    message.textContent = log.message || '';
                    
                    logEntry.appendChild(timestamp);
                    logEntry.appendChild(level);
                    logEntry.appendChild(message);
                    logsContainer.appendChild(logEntry);
                });
                safeLog('成功更新分析结果日志');
            }

            return true;
        } catch (e) {
            safeLog(\`更新分析结果显示时出错: \${e.message}\`, 'error');
            return false;
        }
    }

    // 检查并修复分析结果
    function checkAndFixAnalysisResult() {
        try {
            safeLog('检查并修复分析结果');
            
            // 获取当前小说ID和章节ID
            const ids = getCurrentIds();
            if (!ids) {
                safeLog('无法获取当前小说ID和章节ID，跳过修复', 'warn');
                return;
            }

            // 获取当前维度
            const dimensionMatch = window.location.pathname.match(/\\/analysis\\/([^\\/]+)$/);
            if (!dimensionMatch) {
                safeLog('无法从URL中获取维度，跳过修复', 'warn');
                return;
            }

            const dimension = dimensionMatch[1];
            safeLog(\`当前维度: \${dimension}\`);

            // 获取分析结果
            fetchAnalysisResult(ids.novelId, ids.chapterId, dimension)
                .then(result => {
                    // 更新分析结果显示
                    if (updateAnalysisResult(result)) {
                        safeLog('成功修复分析结果');
                    }
                })
                .catch(error => {
                    safeLog(\`获取分析结果时出错: \${error.message}\`, 'warn');
                });
        } catch (e) {
            safeLog(\`检查并修复分析结果时出错: \${e.message}\`, 'error');
        }
    }

    // 导出全局函数
    window.chapterAnalysisResultFix = {
        checkAndFixAnalysisResult,
        updateAnalysisResult,
        fetchAnalysisResult
    };

    // 立即执行修复
    checkAndFixAnalysisResult();
    
    // 设置定期检查
    setInterval(checkAndFixAnalysisResult, 5000);

    safeLog('脚本加载完成');
})();`;
            
            return script;
        }

        // 应用修复
        function applyFix() {
            log('开始应用修复');
            
            try {
                // 创建修复脚本
                const script = createFixScript();
                
                // 将脚本保存到localStorage
                localStorage.setItem('chapterAnalysisFixScript', script.textContent);
                
                log('修复脚本已保存到localStorage');
                log('请返回章节分析页面，然后按F5刷新页面');
                
                // 创建自动注入脚本
                const injector = document.createElement('script');
                injector.textContent = `
                // 自动注入脚本
                (function() {
                    // 在页面加载完成后注入修复脚本
                    function injectFixScript() {
                        const fixScript = localStorage.getItem('chapterAnalysisFixScript');
                        if (fixScript) {
                            const script = document.createElement('script');
                            script.textContent = fixScript;
                            document.head.appendChild(script);
                            console.log('已注入章节分析修复脚本');
                        }
                    }
                    
                    // 在页面加载完成后注入
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', injectFixScript);
                    } else {
                        injectFixScript();
                    }
                })();`;
                
                // 将注入器保存到localStorage
                localStorage.setItem('chapterAnalysisFixInjector', injector.textContent);
                
                log('自动注入脚本已保存到localStorage');
                
                // 创建书签链接
                const bookmarkLink = document.createElement('a');
                bookmarkLink.href = `javascript:(function(){${encodeURIComponent(injector.textContent)}})();`;
                bookmarkLink.textContent = '章节分析修复';
                bookmarkLink.style.display = 'block';
                bookmarkLink.style.marginTop = '20px';
                bookmarkLink.style.padding = '10px';
                bookmarkLink.style.backgroundColor = '#f0f0f0';
                bookmarkLink.style.borderRadius = '4px';
                bookmarkLink.style.textAlign = 'center';
                
                // 添加书签链接
                const container = document.querySelector('.container');
                container.appendChild(document.createElement('hr'));
                container.appendChild(document.createElement('p')).textContent = '您也可以将下面的链接拖到书签栏，然后在章节分析页面点击该书签来应用修复：';
                container.appendChild(bookmarkLink);
                
                log('成功创建书签链接');
                log('修复已应用，请返回章节分析页面并刷新');
            } catch (e) {
                log(`应用修复时出错: ${e.message}`, 'error');
            }
        }

        // 绑定按钮事件
        document.getElementById('applyFixBtn').addEventListener('click', applyFix);
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('修复工具已加载');
        });
    </script>
</body>
</html>
