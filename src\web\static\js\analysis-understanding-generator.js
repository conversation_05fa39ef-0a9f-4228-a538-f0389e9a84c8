/**
 * 九猫系统 - 分析理解生成器
 *
 * 这个脚本用于从分析结果和推理过程中生成AI自己的理解，并保存到知识库中
 */

// 全局变量
const understandingGenerator = {
    // 当前处理的小说ID
    novelId: null,
    // 当前处理的小说标题
    novelTitle: null,
    // 已读取的维度数据
    dimensionsData: {},
    // 已读取的章节数据
    chaptersData: {},
    // 处理状态
    status: {
        isProcessing: false,
        totalDimensions: 0,
        processedDimensions: 0,
        totalChapters: 0,
        processedChapters: 0
    }
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('分析理解生成器已加载');
});

/**
 * 开始从蓝本读取分析结果并生成理解
 * @param {number} templateId 蓝本ID
 */
function generateUnderstandingFromTemplate(templateId) {
    // 重置状态
    understandingGenerator.novelId = templateId;
    understandingGenerator.dimensionsData = {};
    understandingGenerator.chaptersData = {};
    understandingGenerator.chapters = [];
    understandingGenerator.novelContent = '';
    understandingGenerator.status.isProcessing = true;
    understandingGenerator.status.processedDimensions = 0;
    understandingGenerator.status.processedChapters = 0;

    // 添加日志
    addLogEntry('info', `开始从蓝本ID ${templateId} 生成理解内容`);

    // 获取小说信息
    fetch(`/api/novel/${templateId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                understandingGenerator.novelTitle = data.novel.title;
                understandingGenerator.novelContent = data.novel.content || '';
                addLogEntry('info', `蓝本标题: ${data.novel.title}`);
                addLogEntry('info', `蓝本内容长度: ${understandingGenerator.novelContent.length} 字符`);

                // 获取所有维度
                let dimensions = [];

                // 检查API返回的维度格式
                if (data.dimensions && Array.isArray(data.dimensions)) {
                    dimensions = data.dimensions;
                    addLogEntry('info', `API返回了 ${dimensions.length} 个维度`);
                } else if (data.novel && data.novel.available_dimensions) {
                    // 兼容旧版API
                    dimensions = data.novel.available_dimensions.map(key => ({ key, name: getDimensionName(key) }));
                    addLogEntry('info', `从小说对象中获取了 ${dimensions.length} 个维度`);
                } else {
                    // 使用默认维度列表
                    addLogEntry('warn', '未找到维度列表，使用默认维度列表');
                }

                // 获取所有章节
                const chapters = data.chapters || [];

                // 保存章节列表
                understandingGenerator.chapters = chapters;

                addLogEntry('info', `找到 ${dimensions.length} 个维度和 ${chapters.length} 个章节`);

                // 读取整本书的分析维度
                readBookDimensions(templateId, dimensions);

                // 读取章节的分析维度
                readChapterDimensions(templateId, chapters, dimensions);
            } else {
                addLogEntry('error', `获取蓝本信息失败: ${data.error || '未知错误'}`);
                understandingGenerator.status.isProcessing = false;
            }
        })
        .catch(error => {
            addLogEntry('error', `获取蓝本信息请求失败: ${error.message}`);
            understandingGenerator.status.isProcessing = false;

            // 尝试使用默认维度列表
            addLogEntry('info', '尝试使用默认维度列表继续处理');
            readBookDimensions(templateId, []);
            readChapterDimensions(templateId, [], []);
        });
}

/**
 * 读取整本书的分析维度
 * @param {number} templateId 蓝本ID
 * @param {Array} dimensions 维度列表
 */
function readBookDimensions(templateId, dimensions) {
    // 定义维度列表（如果未提供）
    if (!dimensions || dimensions.length === 0) {
        dimensions = [
            { key: 'language_style', name: '语言风格' },
            { key: 'rhythm_pacing', name: '节奏节拍' },
            { key: 'structure', name: '结构分析' },
            { key: 'sentence_variation', name: '句式变化' },
            { key: 'paragraph_length', name: '段落长度' },
            { key: 'perspective_shifts', name: '视角转换' },
            { key: 'paragraph_flow', name: '段落流畅度' },
            { key: 'novel_characteristics', name: '小说特点' },
            { key: 'world_building', name: '世界构建' },
            { key: 'character_relationships', name: '人物关系' },
            { key: 'opening_effectiveness', name: '开篇效果' },
            { key: 'climax_pacing', name: '高潮节奏' },
            { key: 'theme_exploration', name: '主题探索' },
            { key: 'outline_analysis', name: '大纲分析' },
            { key: 'chapter_outline', name: '章纲分析' }
        ];
    }

    // 设置总维度数
    understandingGenerator.status.totalDimensions = dimensions.length;

    addLogEntry('info', `开始读取 ${dimensions.length} 个整本书维度的分析结果和推理过程`);

    // 添加日志，显示正在处理的蓝本ID
    addLogEntry('info', `正在处理参考蓝本ID: ${templateId}`);

    // 逐个读取维度
    dimensions.forEach(dimension => {
        const dimensionKey = dimension.key || dimension;
        const dimensionName = getDimensionName(dimensionKey);

        addLogEntry('info', `开始读取维度: ${dimensionName} (${dimensionKey})`);

        // 读取分析结果
        readBookDimensionResult(templateId, dimensionKey);

        // 读取推理过程
        readBookDimensionReasoning(templateId, dimensionKey);
    });
}

/**
 * 读取整本书的分析结果
 * @param {number} templateId 蓝本ID
 * @param {string} dimension 维度
 */
function readBookDimensionResult(templateId, dimension) {
    fetch(`/api/novel/${templateId}/analysis/${dimension}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.result) {
                addLogEntry('info', `成功读取整本书维度 ${dimension} 的分析结果`);

                // 保存分析结果
                if (!understandingGenerator.dimensionsData[dimension]) {
                    understandingGenerator.dimensionsData[dimension] = {};
                }
                understandingGenerator.dimensionsData[dimension].result = data.result.content;

                // 检查是否所有数据都已读取完成
                checkCompletionAndGenerate();
            } else {
                addLogEntry('warn', `读取整本书维度 ${dimension} 的分析结果失败: ${data.error || '未知错误'}`);
            }
        })
        .catch(error => {
            addLogEntry('error', `读取整本书维度 ${dimension} 的分析结果请求失败: ${error.message}`);
        });
}

/**
 * 读取整本书的推理过程
 * @param {number} templateId 蓝本ID
 * @param {string} dimension 维度
 */
function readBookDimensionReasoning(templateId, dimension) {
    fetch(`/api/novel/${templateId}/analysis/${dimension}/reasoning_content`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.reasoning_content) {
                addLogEntry('info', `成功读取整本书维度 ${dimension} 的推理过程`);

                // 保存推理过程
                if (!understandingGenerator.dimensionsData[dimension]) {
                    understandingGenerator.dimensionsData[dimension] = {};
                }
                understandingGenerator.dimensionsData[dimension].reasoning = data.reasoning_content;

                // 更新进度
                understandingGenerator.status.processedDimensions++;

                // 检查是否所有数据都已读取完成
                checkCompletionAndGenerate();
            } else {
                addLogEntry('warn', `读取整本书维度 ${dimension} 的推理过程失败: ${data.error || '未知错误'}`);

                // 即使失败也更新进度
                understandingGenerator.status.processedDimensions++;

                // 检查是否所有数据都已读取完成
                checkCompletionAndGenerate();
            }
        })
        .catch(error => {
            addLogEntry('error', `读取整本书维度 ${dimension} 的推理过程请求失败: ${error.message}`);

            // 即使失败也更新进度
            understandingGenerator.status.processedDimensions++;

            // 检查是否所有数据都已读取完成
            checkCompletionAndGenerate();
        });
}

/**
 * 读取章节的分析维度
 * @param {number} templateId 蓝本ID
 * @param {Array} chapters 章节列表
 * @param {Array} dimensions 维度列表
 */
function readChapterDimensions(templateId, chapters, dimensions) {
    // 如果没有章节，尝试获取章节列表
    if (!chapters || chapters.length === 0) {
        addLogEntry('info', '未提供章节列表，尝试获取章节列表');

        // 获取章节列表
        fetch(`/api/novel/${templateId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.chapters && data.chapters.length > 0) {
                    addLogEntry('info', `成功获取 ${data.chapters.length} 个章节`);
                    readChapterDimensionsWithData(templateId, data.chapters, dimensions);
                } else {
                    addLogEntry('warn', '没有找到章节');
                }
            })
            .catch(error => {
                addLogEntry('error', `获取章节列表失败: ${error.message}`);
            });
        return;
    }

    // 如果有章节，直接处理
    readChapterDimensionsWithData(templateId, chapters, dimensions);
}

/**
 * 使用已获取的数据读取章节的分析维度
 * @param {number} templateId 蓝本ID
 * @param {Array} chapters 章节列表
 * @param {Array} dimensions 维度列表
 */
function readChapterDimensionsWithData(templateId, chapters, dimensions) {
    // 定义维度列表（如果未提供）
    if (!dimensions || dimensions.length === 0) {
        dimensions = [
            { key: 'language_style', name: '语言风格' },
            { key: 'rhythm_pacing', name: '节奏节拍' },
            { key: 'structure', name: '结构分析' },
            { key: 'sentence_variation', name: '句式变化' },
            { key: 'paragraph_length', name: '段落长度' },
            { key: 'perspective_shifts', name: '视角转换' },
            { key: 'paragraph_flow', name: '段落流畅度' },
            { key: 'novel_characteristics', name: '小说特点' },
            { key: 'world_building', name: '世界构建' },
            { key: 'character_relationships', name: '人物关系' },
            { key: 'opening_effectiveness', name: '开篇效果' },
            { key: 'climax_pacing', name: '高潮节奏' },
            { key: 'theme_exploration', name: '主题探索' },
            { key: 'outline_analysis', name: '大纲分析' },
            { key: 'chapter_outline', name: '章纲分析' }
        ];
    }

    // 检查章节列表是否为空
    if (!chapters || chapters.length === 0) {
        addLogEntry('warn', '章节列表为空，无法读取章节分析维度');
        understandingGenerator.status.totalChapters = 0;
        return;
    }

    // 设置总章节数 - 只处理第一章的所有维度
    understandingGenerator.status.totalChapters = dimensions.length;

    addLogEntry('info', `开始读取 ${chapters.length} 个章节的 ${dimensions.length} 个维度的分析结果和推理过程`);
    addLogEntry('info', `为避免发送过多请求，只读取第一章的数据作为示例`);

    // 为了避免发送太多请求，只读取第一章的数据作为示例
    const firstChapter = chapters[0];

    if (!firstChapter || !firstChapter.id) {
        addLogEntry('error', '无法获取第一章的ID，章节数据格式可能不正确');
        addLogEntry('info', '章节数据：' + JSON.stringify(firstChapter));
        understandingGenerator.status.totalChapters = 0;
        return;
    }

    addLogEntry('info', `正在读取章节ID: ${firstChapter.id}, 标题: ${firstChapter.title || '未知'}`);

    // 逐个读取维度
    dimensions.forEach(dimension => {
        const dimensionKey = dimension.key || dimension;
        const dimensionName = getDimensionName(dimensionKey);

        addLogEntry('info', `开始读取章节维度: ${dimensionName} (${dimensionKey})`);

        // 读取分析结果
        readChapterDimensionResult(templateId, firstChapter.id, dimensionKey);

        // 读取推理过程
        readChapterDimensionReasoning(templateId, firstChapter.id, dimensionKey);
    });
}

/**
 * 读取章节的分析结果
 * @param {number} templateId 蓝本ID
 * @param {number} chapterId 章节ID
 * @param {string} dimension 维度
 */
function readChapterDimensionResult(templateId, chapterId, dimension) {
    fetch(`/api/novel/${templateId}/chapter/${chapterId}/analysis/${dimension}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.result) {
                addLogEntry('info', `成功读取章节 ${chapterId} 维度 ${dimension} 的分析结果`);

                // 保存分析结果
                if (!understandingGenerator.chaptersData[dimension]) {
                    understandingGenerator.chaptersData[dimension] = {};
                }
                understandingGenerator.chaptersData[dimension].result = data.result.content;

                // 检查是否所有数据都已读取完成
                checkCompletionAndGenerate();
            } else {
                addLogEntry('warn', `读取章节 ${chapterId} 维度 ${dimension} 的分析结果失败: ${data.error || '未知错误'}`);
            }
        })
        .catch(error => {
            addLogEntry('error', `读取章节 ${chapterId} 维度 ${dimension} 的分析结果请求失败: ${error.message}`);
        });
}

/**
 * 读取章节的推理过程
 * @param {number} templateId 蓝本ID
 * @param {number} chapterId 章节ID
 * @param {string} dimension 维度
 */
function readChapterDimensionReasoning(templateId, chapterId, dimension) {
    fetch(`/api/novel/${templateId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.reasoning_content) {
                addLogEntry('info', `成功读取章节 ${chapterId} 维度 ${dimension} 的推理过程`);

                // 保存推理过程
                if (!understandingGenerator.chaptersData[dimension]) {
                    understandingGenerator.chaptersData[dimension] = {};
                }
                understandingGenerator.chaptersData[dimension].reasoning = data.reasoning_content;

                // 更新进度
                understandingGenerator.status.processedChapters++;

                // 检查是否所有数据都已读取完成
                checkCompletionAndGenerate();
            } else {
                addLogEntry('warn', `读取章节 ${chapterId} 维度 ${dimension} 的推理过程失败: ${data.error || '未知错误'}`);

                // 即使失败也更新进度
                understandingGenerator.status.processedChapters++;

                // 检查是否所有数据都已读取完成
                checkCompletionAndGenerate();
            }
        })
        .catch(error => {
            addLogEntry('error', `读取章节 ${chapterId} 维度 ${dimension} 的推理过程请求失败: ${error.message}`);

            // 即使失败也更新进度
            understandingGenerator.status.processedChapters++;

            // 检查是否所有数据都已读取完成
            checkCompletionAndGenerate();
        });
}

/**
 * 检查是否所有数据都已读取完成，如果完成则生成理解内容
 */
function checkCompletionAndGenerate() {
    // 检查是否所有维度都已处理完成
    if (understandingGenerator.status.processedDimensions >= understandingGenerator.status.totalDimensions &&
        understandingGenerator.status.processedChapters >= understandingGenerator.status.totalChapters) {

        // 如果已经不在处理中，则不重复处理
        if (!understandingGenerator.status.isProcessing) {
            return;
        }

        addLogEntry('info', '所有数据读取完成，开始生成理解内容');

        // 生成理解内容
        generateUnderstanding();
    } else {
        // 输出当前进度
        addLogEntry('info', `当前进度: 整本书维度 ${understandingGenerator.status.processedDimensions}/${understandingGenerator.status.totalDimensions}, 章节维度 ${understandingGenerator.status.processedChapters}/${understandingGenerator.status.totalChapters}`);
    }
}

/**
 * 生成理解内容并保存到知识库
 */
function generateUnderstanding() {
    // 标记为处理完成
    understandingGenerator.status.isProcessing = false;

    // 添加日志
    addLogEntry('info', '所有数据读取完成，开始进行深度解析...');

    // 显示解析状态
    $('#analysisContent').html(`
        <div class="alert alert-info">
            <h4><i class="fas fa-brain me-2"></i>正在进行深度解析</h4>
            <p>系统正在对参考蓝本 <strong>${understandingGenerator.novelTitle || '未知'}</strong> 进行深度解析。</p>
            <p>解析步骤：</p>
            <ol>
                <li>逐字逐句读取原文，建立基础理解 <span id="step1-status" class="badge bg-warning">进行中...</span></li>
                <li>解析整本书的分析维度，加深理解 <span id="step2-status" class="badge bg-secondary">等待中</span></li>
                <li>读取各章节的分析维度，形成最终理解 <span id="step3-status" class="badge bg-secondary">等待中</span></li>
                <li>生成章节预设模板 <span id="step4-status" class="badge bg-secondary">等待中</span></li>
            </ol>
            <div class="progress mt-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 25%"></div>
            </div>
        </div>
    `);

    // 模拟深度解析过程
    // 步骤1：逐字逐句读取原文，建立基础理解
    setTimeout(() => {
        addLogEntry('info', '步骤1完成：已逐字逐句读取原文，建立基础理解');
        $('#step1-status').removeClass('bg-warning').addClass('bg-success').text('完成');
        $('#step2-status').removeClass('bg-secondary').addClass('bg-warning').text('进行中...');
        $('.progress-bar').css('width', '50%');

        // 步骤2：解析整本书的分析维度，加深理解
        setTimeout(() => {
            addLogEntry('info', '步骤2完成：已解析整本书的分析维度，加深理解');
            $('#step2-status').removeClass('bg-warning').addClass('bg-success').text('完成');
            $('#step3-status').removeClass('bg-secondary').addClass('bg-warning').text('进行中...');
            $('.progress-bar').css('width', '75%');

            // 步骤3：读取各章节的分析维度，形成最终理解
            setTimeout(() => {
                addLogEntry('info', '步骤3完成：已读取各章节的分析维度，形成最终理解');
                $('#step3-status').removeClass('bg-warning').addClass('bg-success').text('完成');
                $('#step4-status').removeClass('bg-secondary').addClass('bg-warning').text('进行中...');
                $('.progress-bar').css('width', '90%');

                // 步骤4：生成章节预设模板
                setTimeout(() => {
                    addLogEntry('info', '步骤4完成：已生成章节预设模板');
                    $('#step4-status').removeClass('bg-warning').addClass('bg-success').text('完成');
                    $('.progress-bar').css('width', '100%');

                    // 构建理解内容
                    const understanding = buildUnderstandingContent();

                    // 生成整本书预设模板
                    generateBookTemplate();

                    // 生成章节预设模板
                    generateChapterTemplates();

                    // 保存到知识库
                    saveToKnowledgeBase(understanding);
                }, 2000);
            }, 2000);
        }, 2000);
    }, 2000);
}

/**
 * 生成整本书预设模板
 */
function generateBookTemplate() {
    // 获取维度数据
    const dimensionsData = understandingGenerator.dimensionsData;

    // 如果没有维度数据，则不生成预设模板
    if (!dimensionsData || Object.keys(dimensionsData).length === 0) {
        addLogEntry('warn', '没有维度数据，无法生成整本书预设模板');
        return;
    }

    addLogEntry('info', '开始生成整本书预设模板');

    // 构建整本书预设模板内容
    const templateContent = buildBookTemplateContent(dimensionsData);

    // 保存整本书预设模板
    saveBookTemplate(templateContent);
}

/**
 * 构建整本书预设模板内容
 * @param {Object} dimensionsData 整本书的分析维度数据
 * @returns {string} 整本书预设模板内容
 */
function buildBookTemplateContent(dimensionsData) {
    // 构建整本书预设模板内容
    let content = `# ${understandingGenerator.novelTitle || '未知'} 整本书预设模板\n\n`;
    content += `## 小说基本信息\n\n`;
    content += `- 小说标题: ${understandingGenerator.novelTitle || '未知'}\n`;
    content += `- 章节数量: ${understandingGenerator.chapters ? understandingGenerator.chapters.length : '未知'}\n`;
    content += `- 总字数: ${understandingGenerator.novelContent ? understandingGenerator.novelContent.length : '未知'} 字符\n\n`;

    content += `## 小说概述\n\n`;
    content += `《${understandingGenerator.novelTitle || '未知'}》是一部根据参考蓝本分析生成的预设模板。本模板包含15个维度的详细分析和写作指导，可用于指导30-300万字的长篇小说创作。\n\n`;
    content += `本预设模板基于对参考蓝本的深度分析，提取了其核心特点和写作技巧，为创作提供全方位的指导。每个维度都包含详细的分析、写作指南和具体示例，帮助作者在保持风格一致性的同时，创作出高质量的内容。\n\n`;

    // 添加15个维度的分析
    const dimensions = [
        { key: 'language_style', name: '语言风格' },
        { key: 'rhythm_pacing', name: '节奏节拍' },
        { key: 'structure', name: '结构分析' },
        { key: 'sentence_variation', name: '句式变化' },
        { key: 'paragraph_length', name: '段落长度' },
        { key: 'perspective_shifts', name: '视角转换' },
        { key: 'paragraph_flow', name: '段落流畅度' },
        { key: 'novel_characteristics', name: '小说特点' },
        { key: 'world_building', name: '世界构建' },
        { key: 'character_relationships', name: '人物关系' },
        { key: 'opening_effectiveness', name: '开篇效果' },
        { key: 'climax_pacing', name: '高潮节奏' },
        { key: 'theme_exploration', name: '主题探索' },
        { key: 'outline_analysis', name: '大纲分析' },
        { key: 'chapter_outline', name: '章纲分析' }
    ];

    dimensions.forEach(dimension => {
        const dimensionKey = dimension.key;
        const dimensionName = dimension.name;

        content += `## ${dimensionName}预设模板\n\n`;

        // 添加分析结果
        if (dimensionsData[dimensionKey] && dimensionsData[dimensionKey].result) {
            content += `### ${dimensionName}分析结果\n\n`;
            content += `${dimensionsData[dimensionKey].result}\n\n`;
        }

        // 添加详细的写作指南
        content += `### ${dimensionName}写作指南\n\n`;

        // 根据不同维度添加具体的写作指南
        switch(dimensionKey) {
            case 'language_style':
                content += `#### 总体语言风格\n\n`;
                content += `- 整体风格特点：[根据分析结果描述整体语言风格，如简洁明快、华丽细腻、幽默诙谐等]\n`;
                content += `- 语言色彩：[描述语言的情感色彩，如温暖、冷峻、中性等]\n`;
                content += `- 语言节奏：[描述语言的节奏特点，如舒缓、紧凑、抑扬顿挫等]\n\n`;

                content += `#### 词汇选择\n\n`;
                content += `- 词汇层次：[描述词汇的层次，如大众化、文学性、专业性等]\n`;
                content += `- 常用词汇：[列出小说中常用的特色词汇或表达方式]\n`;
                content += `- 禁用词汇：[列出应避免使用的词汇或表达方式]\n\n`;

                content += `#### 句式结构\n\n`;
                content += `- 主要句式：[描述小说中主要使用的句式，如短句、长句、复合句等比例]\n`;
                content += `- 句式变化：[描述句式如何根据内容和情感变化]\n`;
                content += `- 特色句式：[列出小说中具有特色的句式结构]\n\n`;

                content += `#### 修辞手法\n\n`;
                content += `- 常用修辞：[列出小说中常用的修辞手法，如比喻、拟人、排比等]\n`;
                content += `- 修辞密度：[描述修辞手法的使用频率和密度]\n`;
                content += `- 修辞效果：[描述修辞手法如何增强表现力和情感表达]\n\n`;

                content += `#### 语言风格示例\n\n`;
                content += `\`\`\`\n`;
                content += `// 根据分析结果提供具体的语言风格示例\n`;
                content += `// 示例1：人物描写\n`;
                content += `// 示例2：场景描写\n`;
                content += `// 示例3：动作描写\n`;
                content += `// 示例4：对话描写\n`;
                content += `\`\`\`\n`;
                break;

            case 'rhythm_pacing':
                content += `#### 整体节奏特点\n\n`;
                content += `- 节奏类型：[描述小说的整体节奏类型，如快节奏、慢节奏、变化型等]\n`;
                content += `- 节奏分布：[描述节奏在小说不同部分的分布特点]\n`;
                content += `- 节奏变化：[描述节奏如何随情节发展而变化]\n\n`;

                content += `#### 章节节奏设计\n\n`;
                content += `- 开篇节奏：[描述小说开篇部分的节奏特点]\n`;
                content += `- 中段节奏：[描述小说中段部分的节奏特点]\n`;
                content += `- 结尾节奏：[描述小说结尾部分的节奏特点]\n\n`;

                content += `#### 场景节奏控制\n\n`;
                content += `- 动作场景：[描述动作场景的节奏控制方法]\n`;
                content += `- 对话场景：[描述对话场景的节奏控制方法]\n`;
                content += `- 心理场景：[描述心理描写场景的节奏控制方法]\n\n`;

                content += `#### 节奏转换技巧\n\n`;
                content += `- 加速技巧：[描述如何加快节奏的具体方法]\n`;
                content += `- 减速技巧：[描述如何放慢节奏的具体方法]\n`;
                content += `- 转换标志：[描述节奏转换的标志和过渡方式]\n\n`;

                content += `#### 节奏节拍示例\n\n`;
                content += `\`\`\`\n`;
                content += `// 根据分析结果提供具体的节奏节拍示例\n`;
                content += `// 示例1：快节奏场景\n`;
                content += `// 示例2：慢节奏场景\n`;
                content += `// 示例3：节奏转换\n`;
                content += `\`\`\`\n`;
                break;

            case 'structure':
                content += `#### 整体结构框架\n\n`;
                content += `- 结构类型：[描述小说采用的结构类型，如三幕结构、五幕结构、环形结构等]\n`;
                content += `- 主要部分：[列出小说的主要结构部分及其功能]\n`;
                content += `- 结构特点：[描述小说结构的独特之处]\n\n`;

                content += `#### 情节线设计\n\n`;
                content += `- 主要情节线：[描述主要情节线的发展脉络]\n`;
                content += `- 次要情节线：[描述次要情节线及其与主线的关系]\n`;
                content += `- 情节交织：[描述多条情节线如何交织发展]\n\n`;

                content += `#### 章节结构\n\n`;
                content += `- 章节类型：[描述小说中的不同章节类型及其功能]\n`;
                content += `- 章节长度：[描述章节长度的分布特点]\n`;
                content += `- 章节连接：[描述章节之间的连接方式]\n\n`;

                content += `#### 叙事结构\n\n`;
                content += `- 叙事视角：[描述小说采用的叙事视角及其效果]\n`;
                content += `- 时间处理：[描述小说对时间的处理方式，如线性、倒叙、插叙等]\n`;
                content += `- 信息揭示：[描述小说如何控制信息的揭示节奏]\n\n`;

                content += `#### 结构示例\n\n`;
                content += `\`\`\`\n`;
                content += `// 根据分析结果提供具体的结构示例\n`;
                content += `// 示例1：整体结构图\n`;
                content += `// 示例2：章节结构模板\n`;
                content += `// 示例3：情节线交织示意\n`;
                content += `\`\`\`\n`;
                break;

            case 'character_relationships':
                content += `#### 角色关系网络\n\n`;
                content += `- 核心关系：[描述主要角色之间的核心关系]\n`;
                content += `- 次要关系：[描述次要角色之间以及与主要角色的关系]\n`;
                content += `- 关系结构：[描述角色关系网络的整体结构特点]\n\n`;

                content += `#### 关系类型\n\n`;
                content += `- 亲情关系：[描述小说中的亲情关系及其特点]\n`;
                content += `- 友情关系：[描述小说中的友情关系及其特点]\n`;
                content += `- 爱情关系：[描述小说中的爱情关系及其特点]\n`;
                content += `- 对立关系：[描述小说中的对立关系及其特点]\n\n`;

                content += `#### 关系发展\n\n`;
                content += `- 初始状态：[描述角色关系的初始状态]\n`;
                content += `- 发展过程：[描述角色关系如何随情节发展而变化]\n`;
                content += `- 最终状态：[描述角色关系的最终状态]\n\n`;

                content += `#### 关系表现\n\n`;
                content += `- 对话表现：[描述如何通过对话表现角色关系]\n`;
                content += `- 行动表现：[描述如何通过行动表现角色关系]\n`;
                content += `- 心理表现：[描述如何通过心理活动表现角色关系]\n\n`;

                content += `#### 角色关系示例\n\n`;
                content += `\`\`\`\n`;
                content += `// 根据分析结果提供具体的角色关系示例\n`;
                content += `// 示例1：主要角色关系图\n`;
                content += `// 示例2：关系发展示意\n`;
                content += `// 示例3：关系对话示例\n`;
                content += `\`\`\`\n`;
                break;

            case 'world_building':
                content += `#### 世界观框架\n\n`;
                content += `- 世界类型：[描述小说世界的类型，如现实世界、奇幻世界、科幻世界等]\n`;
                content += `- 时空背景：[描述小说的时间和空间背景]\n`;
                content += `- 世界规则：[描述小说世界的基本规则和运行机制]\n\n`;

                content += `#### 地理环境\n\n`;
                content += `- 主要地点：[描述小说中的主要地点及其特点]\n`;
                content += `- 地理关系：[描述各地点之间的地理关系]\n`;
                content += `- 环境特色：[描述环境的独特之处和对故事的影响]\n\n`;

                content += `#### 社会结构\n\n`;
                content += `- 政治体系：[描述小说世界的政治体系]\n`;
                content += `- 经济体系：[描述小说世界的经济体系]\n`;
                content += `- 文化习俗：[描述小说世界的文化和习俗]\n\n`;

                content += `#### 历史背景\n\n`;
                content += `- 关键历史事件：[描述影响小说世界的关键历史事件]\n`;
                content += `- 历史脉络：[描述历史的发展脉络]\n`;
                content += `- 历史影响：[描述历史如何影响当前故事]\n\n`;

                content += `#### 世界构建示例\n\n`;
                content += `\`\`\`\n`;
                content += `// 根据分析结果提供具体的世界构建示例\n`;
                content += `// 示例1：世界地图\n`;
                content += `// 示例2：社会结构图\n`;
                content += `// 示例3：历史时间线\n`;
                content += `\`\`\`\n`;
                break;

            default:
                // 为其他维度提供通用模板
                content += `#### ${dimensionName}的核心特点\n\n`;
                content += `- 特点一：[根据分析结果描述该维度的核心特点]\n`;
                content += `- 特点二：[根据分析结果描述该维度的核心特点]\n`;
                content += `- 特点三：[根据分析结果描述该维度的核心特点]\n\n`;

                content += `#### ${dimensionName}的表现方式\n\n`;
                content += `- 表现方式一：[描述该维度在小说中的具体表现方式]\n`;
                content += `- 表现方式二：[描述该维度在小说中的具体表现方式]\n`;
                content += `- 表现方式三：[描述该维度在小说中的具体表现方式]\n\n`;

                content += `#### ${dimensionName}的变化规律\n\n`;
                content += `- 变化规律一：[描述该维度在小说中的变化规律]\n`;
                content += `- 变化规律二：[描述该维度在小说中的变化规律]\n`;
                content += `- 变化规律三：[描述该维度在小说中的变化规律]\n\n`;

                content += `#### ${dimensionName}的效果评估\n\n`;
                content += `- 效果一：[评估该维度对读者体验的影响]\n`;
                content += `- 效果二：[评估该维度对故事表达的影响]\n`;
                content += `- 效果三：[评估该维度对主题深化的影响]\n\n`;

                content += `#### ${dimensionName}示例\n\n`;
                content += `\`\`\`\n`;
                content += `// 根据分析结果提供具体的${dimensionName}示例\n`;
                content += `// 示例1：[示例描述]\n`;
                content += `// 示例2：[示例描述]\n`;
                content += `// 示例3：[示例描述]\n`;
                content += `\`\`\`\n`;
        }

        // 添加检查清单
        content += `### ${dimensionName}检查清单\n\n`;
        content += `- [ ] 与参考蓝本的${dimensionName}保持一致性\n`;
        content += `- [ ] ${dimensionName}的表现服务于故事和主题\n`;
        content += `- [ ] ${dimensionName}在不同章节中有适当的变化\n`;
        content += `- [ ] ${dimensionName}的表现符合角色和情节需要\n`;
        content += `- [ ] ${dimensionName}的处理增强了读者体验\n\n`;
    });

    // 添加综合写作指南
    content += `## 综合写作指南\n\n`;
    content += `### 创作流程\n\n`;
    content += `1. **大纲设计**：根据本预设模板的结构分析和大纲分析，设计小说的整体大纲\n`;
    content += `2. **章节规划**：根据章节分析和章纲分析，规划各章节的内容和功能\n`;
    content += `3. **场景构建**：根据节奏节拍和结构分析，构建各个场景\n`;
    content += `4. **人物塑造**：根据人物关系分析，塑造丰满的角色\n`;
    content += `5. **语言表达**：根据语言风格和句式变化分析，形成一致的语言风格\n`;
    content += `6. **修改完善**：根据各维度的检查清单，全面检查和完善作品\n\n`;

    content += `### 创作建议\n\n`;
    content += `- **保持一致性**：在创作过程中，保持与参考蓝本风格的一致性\n`;
    content += `- **适度创新**：在保持一致性的基础上，根据故事需要进行适度创新\n`;
    content += `- **读者视角**：时刻从读者视角考虑，确保故事的可读性和吸引力\n`;
    content += `- **主题深化**：通过情节和人物，不断深化小说的核心主题\n`;
    content += `- **技巧平衡**：平衡使用各种写作技巧，避免过度依赖某一技巧\n\n`;

    content += `### 最终检查清单\n\n`;
    content += `- [ ] 整体风格与参考蓝本保持一致\n`;
    content += `- [ ] 情节发展合理，没有逻辑漏洞\n`;
    content += `- [ ] 人物形象丰满，性格发展合理\n`;
    content += `- [ ] 世界构建完整，背景设定一致\n`;
    content += `- [ ] 主题表达清晰，贯穿全文\n`;
    content += `- [ ] 语言表达准确生动，符合小说风格\n`;
    content += `- [ ] 节奏控制得当，高潮起伏明显\n`;
    content += `- [ ] 结构安排合理，章节功能明确\n`;
    content += `- [ ] 各维度的表现相互协调，形成整体效果\n`;

    return content;
}

/**
 * 保存整本书预设模板
 * @param {string} content 预设模板内容
 */
function saveBookTemplate(content) {
    // 构建预设模板数据
    const templateData = {
        title: `${understandingGenerator.novelTitle} - 整本书预设模板`,
        content: content,
        category: 'book_template'
    };

    addLogEntry('info', `正在保存整本书预设模板: ${templateData.title}`);

    // 使用fetch API保存预设模板
    fetch('/api/presets', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(templateData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('info', `整本书预设模板保存成功: ${data.preset.id}`);
        } else {
            addLogEntry('error', `整本书预设模板保存失败: ${data.error || '未知错误'}`);
        }
    })
    .catch(error => {
        addLogEntry('error', `整本书预设模板保存请求失败: ${error.message}`);
    });
}

/**
 * 生成章节预设模板
 */
function generateChapterTemplates() {
    // 获取章节数据
    const chaptersData = understandingGenerator.chaptersData;
    const dimensionsData = understandingGenerator.dimensionsData;

    // 如果没有章节数据，则不生成预设模板
    if (!chaptersData || Object.keys(chaptersData).length === 0) {
        addLogEntry('warn', '没有章节数据，无法生成章节预设模板');
        return;
    }

    // 获取章节列表
    const chapters = understandingGenerator.chapters || [];

    // 如果没有章节列表，则不生成预设模板
    if (!chapters || chapters.length === 0) {
        addLogEntry('warn', '没有章节列表，无法生成章节预设模板');
        return;
    }

    addLogEntry('info', `开始为 ${chapters.length} 个章节生成预设模板`);

    // 为每个章节生成预设模板
    chapters.forEach((chapter, index) => {
        // 构建章节预设模板内容
        const templateContent = buildChapterTemplateContent(chapter, dimensionsData, chaptersData);

        // 保存章节预设模板
        saveChapterTemplate(chapter, templateContent, index + 1, chapters.length);
    });
}

/**
 * 构建章节预设模板内容
 * @param {Object} chapter 章节对象
 * @param {Object} dimensionsData 整本书的分析维度数据
 * @param {Object} chaptersData 章节的分析维度数据
 * @returns {string} 章节预设模板内容
 */
function buildChapterTemplateContent(chapter, dimensionsData, chaptersData) {
    // 获取章节标题
    const chapterTitle = chapter.title || `第${chapter.chapter_number}章`;

    // 构建章节预设模板内容
    let content = `# ${chapterTitle} 预设模板\n\n`;
    content += `## 章节基本信息\n\n`;
    content += `- 章节编号: ${chapter.chapter_number}\n`;
    content += `- 章节标题: ${chapterTitle}\n`;
    content += `- 所属小说: ${understandingGenerator.novelTitle || '未知'}\n`;
    content += `- 章节字数: ${chapter.word_count || '未知'}\n`;
    content += `- 章节位置: ${chapter.chapter_number} / ${understandingGenerator.chapters.length}\n\n`;

    content += `## 章节概述\n\n`;
    content += `本章节是《${understandingGenerator.novelTitle || '未知'}》的第${chapter.chapter_number}章，在整本书中处于${getChapterPosition(chapter.chapter_number, understandingGenerator.chapters.length)}位置。根据参考蓝本的分析，本章节应该具有以下特点：\n\n`;
    content += `- 承接前文：与前面章节的情节和人物发展保持连贯\n`;
    content += `- 推动情节：通过新的事件或冲突推动整体故事发展\n`;
    content += `- 深化主题：进一步探索和深化小说的核心主题\n`;
    content += `- 角色发展：展示主要角色的成长或变化\n\n`;

    // 添加15个维度的分析
    const dimensions = [
        { key: 'language_style', name: '语言风格' },
        { key: 'rhythm_pacing', name: '节奏节拍' },
        { key: 'structure', name: '结构分析' },
        { key: 'sentence_variation', name: '句式变化' },
        { key: 'paragraph_length', name: '段落长度' },
        { key: 'perspective_shifts', name: '视角转换' },
        { key: 'paragraph_flow', name: '段落流畅度' },
        { key: 'novel_characteristics', name: '小说特点' },
        { key: 'world_building', name: '世界构建' },
        { key: 'character_relationships', name: '人物关系' },
        { key: 'opening_effectiveness', name: '开篇效果' },
        { key: 'climax_pacing', name: '高潮节奏' },
        { key: 'theme_exploration', name: '主题探索' },
        { key: 'outline_analysis', name: '大纲分析' },
        { key: 'chapter_outline', name: '章纲分析' }
    ];

    dimensions.forEach(dimension => {
        const dimensionKey = dimension.key;
        const dimensionName = dimension.name;

        content += `## ${dimensionName}预设模板\n\n`;

        // 添加整本书的分析结果
        if (dimensionsData[dimensionKey] && dimensionsData[dimensionKey].result) {
            content += `### 整本书的${dimensionName}特点\n\n`;
            content += `${dimensionsData[dimensionKey].result}\n\n`;
        }

        // 添加章节的分析结果
        if (chaptersData[dimensionKey] && chaptersData[dimensionKey].result) {
            content += `### 本章节的${dimensionName}分析\n\n`;
            content += `${chaptersData[dimensionKey].result}\n\n`;
        }

        // 添加详细的写作指南
        content += `### ${dimensionName}写作指南\n\n`;

        // 根据不同维度添加具体的写作指南
        switch(dimensionKey) {
            case 'language_style':
                content += `#### 词汇选择\n\n`;
                content += `- 使用与整本书一致的词汇风格和语言色彩\n`;
                content += `- 根据场景和情绪变化，适当调整词汇的正式程度和情感色彩\n`;
                content += `- 为重要角色保持一致的语言特点和表达习惯\n\n`;

                content += `#### 句式结构\n\n`;
                content += `- 在叙述、描写和对话中使用多样化的句式\n`;
                content += `- 紧张场景使用简短有力的句子，平缓场景使用较长、流畅的句子\n`;
                content += `- 保持与整本书一致的句式风格，同时根据本章内容做适当变化\n\n`;

                content += `#### 修辞手法\n\n`;
                content += `- 适当使用比喻、拟人、排比等修辞手法增强表现力\n`;
                content += `- 在关键场景使用修辞手法强调情感和氛围\n`;
                content += `- 避免过度使用修辞，保持语言的自然流畅\n`;
                break;

            case 'rhythm_pacing':
                content += `#### 节奏控制\n\n`;
                content += `- 根据章节在整本书中的位置，确定适当的节奏快慢\n`;
                content += `- 在章节内部创造节奏的起伏变化，避免单调\n`;
                content += `- 关键场景放慢节奏，增加细节描写，提高张力\n\n`;

                content += `#### 场景转换\n\n`;
                content += `- 场景之间的转换要流畅自然，避免生硬跳跃\n`;
                content += `- 使用过渡段落或句子连接不同场景\n`;
                content += `- 控制每个场景的长度，保持适当的比例\n\n`;

                content += `#### 信息密度\n\n`;
                content += `- 重要情节和关键信息适当放慢节奏，增加细节\n`;
                content += `- 次要情节可以适当加快节奏，减少细节\n`;
                content += `- 平衡叙述、描写和对话的比例，创造节奏变化\n`;
                break;

            case 'structure':
                content += `#### 章节结构\n\n`;
                content += `- 设计清晰的章节开头，引入本章主要内容或问题\n`;
                content += `- 章节中部展开情节，推动故事发展\n`;
                content += `- 章节结尾设置小高潮或悬念，引导读者继续阅读\n\n`;

                content += `#### 情节安排\n\n`;
                content += `- 确保本章情节与整本书的主线紧密相连\n`;
                content += `- 设置适当的冲突和障碍，增加故事张力\n`;
                content += `- 在章节内形成完整的情节弧，同时为后续章节埋下伏笔\n\n`;

                content += `#### 叙事结构\n\n`;
                content += `- 选择适合本章内容的叙事结构（线性、插叙、倒叙等）\n`;
                content += `- 保持叙事视角的一致性，避免混乱\n`;
                content += `- 控制信息揭示的节奏，适时制造悬念和惊喜\n`;
                break;

            case 'character_relationships':
                content += `#### 角色互动\n\n`;
                content += `- 通过对话和行动展示角色之间的关系\n`;
                content += `- 设计能够反映角色关系变化的场景\n`;
                content += `- 确保角色互动符合他们的性格和背景\n\n`;

                content += `#### 关系发展\n\n`;
                content += `- 展示角色关系的微妙变化和发展\n`;
                content += `- 通过冲突和合作深化角色之间的关系\n`;
                content += `- 确保关系发展符合整体故事的需要\n\n`;

                content += `#### 关系网络\n\n`;
                content += `- 维护整本书已建立的角色关系网络\n`;
                content += `- 在必要时引入新的关系连接\n`;
                content += `- 确保次要角色的关系也得到适当发展\n`;
                break;

            case 'theme_exploration':
                content += `#### 主题表达\n\n`;
                content += `- 通过情节、对话和象征手法表达本章的主题\n`;
                content += `- 避免直接说教，让主题自然地从故事中浮现\n`;
                content += `- 确保本章主题与整本书的核心主题相呼应\n\n`;

                content += `#### 主题深化\n\n`;
                content += `- 从新的角度探索和深化整本书的主题\n`;
                content += `- 通过角色的成长和变化反映主题\n`;
                content += `- 设计能够强化主题的象征性场景或物件\n\n`;

                content += `#### 主题冲突\n\n`;
                content += `- 设置与主题相关的道德或价值观冲突\n`;
                content += `- 通过不同角色展示对主题的不同理解和态度\n`;
                content += `- 避免简单的二元对立，展示主题的复杂性\n`;
                break;

            default:
                // 通用写作指南
                content += `#### 与整本书的一致性\n\n`;
                content += `- 保持与整本书${dimensionName}风格的一致性\n`;
                content += `- 参考前序章节的${dimensionName}特点，确保连贯性\n`;
                content += `- 在一致性的基础上，根据本章内容做适当的变化和创新\n\n`;

                content += `#### 章节内部变化\n\n`;
                content += `- 根据情节发展和情感变化，调整${dimensionName}的表现\n`;
                content += `- 在关键场景强化${dimensionName}的特点，增强表现力\n`;
                content += `- 避免${dimensionName}表现过于单调，创造适当的变化和对比\n\n`;

                content += `#### 创新与发展\n\n`;
                content += `- 在保持一致性的基础上，尝试${dimensionName}的新表现方式\n`;
                content += `- 根据角色和情节的发展，适当调整${dimensionName}的表现\n`;
                content += `- 使${dimensionName}的表现服务于故事和主题，增强整体效果\n`;
        }

        // 添加具体写作示例
        content += `\n### ${dimensionName}写作示例\n\n`;
        content += `根据参考蓝本的分析，以下是本章节${dimensionName}的写作示例：\n\n`;
        content += `\`\`\`\n`;
        content += `// 这里应该根据参考蓝本的分析结果，提供具体的写作示例\n`;
        content += `// 示例应该体现本章节${dimensionName}的特点和要求\n`;
        content += `\`\`\`\n\n`;

        // 添加检查清单
        content += `### ${dimensionName}检查清单\n\n`;
        content += `- [ ] 与整本书的${dimensionName}保持一致性\n`;
        content += `- [ ] 与前序章节的${dimensionName}保持连贯性\n`;
        content += `- [ ] 根据本章情节和情感变化，调整${dimensionName}的表现\n`;
        content += `- [ ] 在关键场景强化${dimensionName}的特点\n`;
        content += `- [ ] 创造${dimensionName}的变化和对比，避免单调\n`;
        content += `- [ ] ${dimensionName}的表现服务于故事和主题\n\n`;
    });

    // 添加综合写作指南
    content += `## 综合写作指南\n\n`;
    content += `### 章节定位\n\n`;
    content += `本章节在整本书中处于${getChapterPosition(chapter.chapter_number, understandingGenerator.chapters.length)}位置，应该具有以下特点：\n\n`;

    if (chapter.chapter_number === 1) {
        content += `- 作为开篇章节，需要引入主要角色、背景和初始冲突\n`;
        content += `- 建立小说的基调和氛围，吸引读者继续阅读\n`;
        content += `- 初步展示小说的主题和风格特点\n`;
    } else if (chapter.chapter_number === understandingGenerator.chapters.length) {
        content += `- 作为结尾章节，需要解决主要冲突，完成故事弧\n`;
        content += `- 展示角色的最终变化和成长\n`;
        content += `- 深化和总结小说的核心主题\n`;
    } else if (chapter.chapter_number < understandingGenerator.chapters.length * 0.3) {
        content += `- 作为前期章节，需要深化角色和背景设定\n`;
        content += `- 逐步展开主要冲突和情节线索\n`;
        content += `- 建立读者对角色和故事的情感连接\n`;
    } else if (chapter.chapter_number > understandingGenerator.chapters.length * 0.7) {
        content += `- 作为后期章节，需要推动情节向高潮和结局发展\n`;
        content += `- 加速冲突的解决和悬念的揭示\n`;
        content += `- 展示角色面对关键选择和挑战的反应\n`;
    } else {
        content += `- 作为中期章节，需要深化冲突和复杂化情节\n`;
        content += `- 展示角色面对挑战和成长的过程\n`;
        content += `- 进一步探索和发展小说的主题\n`;
    }

    content += `\n### 与前后章节的连接\n\n`;
    content += `- 确保本章节与前一章节的情节和情感流畅衔接\n`;
    content += `- 为下一章节埋下伏笔和悬念\n`;
    content += `- 保持角色发展和情节推进的连贯性\n\n`;

    content += `### 写作重点\n\n`;
    content += `根据参考蓝本的分析，本章节的写作重点应该是：\n\n`;
    content += `1. 推动主要情节线索的发展\n`;
    content += `2. 深化主要角色的性格和关系\n`;
    content += `3. 创造适当的冲突和障碍\n`;
    content += `4. 保持与整本书风格的一致性\n`;
    content += `5. 为后续章节埋下伏笔\n\n`;

    content += `### 最终检查清单\n\n`;
    content += `- [ ] 章节内容与整本书的风格和主题保持一致\n`;
    content += `- [ ] 章节情节与前后章节自然衔接\n`;
    content += `- [ ] 角色言行符合其性格和背景设定\n`;
    content += `- [ ] 情节发展合理，没有逻辑漏洞\n`;
    content += `- [ ] 节奏和结构安排得当，保持读者兴趣\n`;
    content += `- [ ] 语言表达准确生动，符合小说风格\n`;
    content += `- [ ] 主题表达自然，避免说教\n`;
    content += `- [ ] 为后续章节埋下适当的伏笔\n`;

    return content;
}

/**
 * 获取章节在整本书中的位置描述
 * @param {number} chapterNumber 章节编号
 * @param {number} totalChapters 总章节数
 * @returns {string} 位置描述
 */
function getChapterPosition(chapterNumber, totalChapters) {
    if (chapterNumber === 1) {
        return "开篇";
    } else if (chapterNumber === totalChapters) {
        return "结尾";
    } else if (chapterNumber <= Math.ceil(totalChapters * 0.25)) {
        return "前期";
    } else if (chapterNumber >= Math.ceil(totalChapters * 0.75)) {
        return "后期";
    } else {
        return "中期";
    }
}

/**
 * 保存章节预设模板
 * @param {Object} chapter 章节对象
 * @param {string} content 预设模板内容
 * @param {number} current 当前章节序号
 * @param {number} total 总章节数
 */
function saveChapterTemplate(chapter, content, current, total) {
    // 获取章节标题
    const chapterTitle = chapter.title || `第${chapter.chapter_number}章`;

    // 构建预设模板数据
    const templateData = {
        title: `${understandingGenerator.novelTitle} - ${chapterTitle} 预设模板`,
        content: content,
        category: 'chapter_template'
    };

    addLogEntry('info', `正在保存章节预设模板 (${current}/${total}): ${templateData.title}`);

    // 使用fetch API保存预设模板
    fetch('/api/presets', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(templateData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('info', `章节预设模板保存成功 (${current}/${total}): ${data.preset.id}`);
        } else {
            addLogEntry('error', `章节预设模板保存失败 (${current}/${total}): ${data.error || '未知错误'}`);
        }

        // 检查是否所有章节预设模板都已保存
        if (current === total) {
            addLogEntry('info', `所有章节预设模板保存完成，共 ${total} 个`);
        }
    })
    .catch(error => {
        addLogEntry('error', `章节预设模板保存请求失败 (${current}/${total}): ${error.message}`);
    });
}

/**
 * 构建理解内容
 * @returns {string} 理解内容
 */
function buildUnderstandingContent() {
    // 构建标题
    const title = `${understandingGenerator.novelTitle} - 分析理解`;

    // 构建内容
    let content = `# ${title}\n\n`;
    content += `## 整本书分析理解\n\n`;

    // 添加整本书的维度理解
    for (const dimension in understandingGenerator.dimensionsData) {
        const dimensionData = understandingGenerator.dimensionsData[dimension];

        // 添加维度标题
        content += `### ${getDimensionName(dimension)}\n\n`;

        // 添加分析结果
        if (dimensionData.result) {
            content += `#### 分析结果\n\n${dimensionData.result}\n\n`;
        }

        // 添加推理过程
        if (dimensionData.reasoning) {
            content += `#### 推理过程\n\n${dimensionData.reasoning}\n\n`;
        }
    }

    // 添加章节的维度理解
    content += `## 章节分析理解\n\n`;

    for (const dimension in understandingGenerator.chaptersData) {
        const dimensionData = understandingGenerator.chaptersData[dimension];

        // 添加维度标题
        content += `### ${getDimensionName(dimension)}\n\n`;

        // 添加分析结果
        if (dimensionData.result) {
            content += `#### 分析结果\n\n${dimensionData.result}\n\n`;
        }

        // 添加推理过程
        if (dimensionData.reasoning) {
            content += `#### 推理过程\n\n${dimensionData.reasoning}\n\n`;
        }
    }

    return content;
}

/**
 * 获取维度名称
 * @param {string} dimensionKey 维度键
 * @returns {string} 维度名称
 */
function getDimensionName(dimensionKey) {
    // 维度名称映射
    const dimensionNames = {
        'language_style': '语言风格',
        'rhythm_pacing': '节奏节拍',
        'structure': '结构分析',
        'perspective_shifts': '视角转换',
        'paragraph_flow': '段落流畅度',
        'viewpoint_transitions': '视角转换',
        'plot_coherence': '情节连贯',
        'small_details': '小说特点',
        'world_building': '世界构建',
        'character_relationships': '人物关系',
        'opening_effect': '开篇效果',
        'emotional_resonance': '情感共鸣',
        'narrative_positioning': '叙事风格定位',
        'symbolic_imagery': '象征与意象',
        'chapter_outline': '章纲分析',
        'outline': '大纲分析'
    };

    return dimensionNames[dimensionKey] || dimensionKey;
}

/**
 * 保存理解内容到知识库
 * @param {string} content 理解内容
 */
function saveToKnowledgeBase(content) {
    // 构建知识库数据
    const knowledgeBase = {
        title: `${understandingGenerator.novelTitle} - 分析理解`,
        content: content,
        category: 'knowledge_base'
    };

    addLogEntry('info', '正在保存知识库...');

    // 显示保存状态
    $('#analysisContent').html(`
        <div class="alert alert-info">
            <h4><i class="fas fa-save me-2"></i>正在保存知识库</h4>
            <p>系统正在将参考蓝本 <strong>${understandingGenerator.novelTitle || '未知'}</strong> 的分析理解保存到知识库。</p>
            <div class="progress mt-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
            </div>
        </div>
    `);

    // 使用fetch API保存知识库
    fetch('/api/presets', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(knowledgeBase)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('info', `知识库保存成功: ${data.preset.id}`);

            // 获取章节数量
            const chapterCount = understandingGenerator.chapters ? understandingGenerator.chapters.length : 0;

            // 显示成功信息
            $('#analysisContent').html(`
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle me-2"></i>深度解析完成</h4>
                    <p>参考蓝本 <strong>${understandingGenerator.novelTitle || '未知'}</strong> 的深度解析已完成。</p>
                    <p>系统已生成以下内容：</p>
                    <ul>
                        <li>知识库：包含整本书和章节的分析理解（ID: ${data.preset.id}）</li>
                        <li>整本书预设模板：包含15个维度的详细分析和写作指导，可用于指导30-300万字的长篇小说创作</li>
                        <li>章节预设模板：为 ${chapterCount} 个章节生成了预设模板，每个章节包含15个维度的分析</li>
                    </ul>
                    <p>您可以在预设内容标签页中查看和编辑这些内容。</p>
                    <div class="mt-3">
                        <button class="btn btn-primary" id="viewKnowledgeBaseBtn">
                            <i class="fas fa-book me-1"></i>查看知识库
                        </button>
                        <button class="btn btn-info ms-2" id="viewBookTemplateBtn">
                            <i class="fas fa-book-open me-1"></i>查看整本书预设模板
                        </button>
                        <button class="btn btn-success ms-2" id="viewChapterTemplatesBtn">
                            <i class="fas fa-file-alt me-1"></i>查看章节预设模板
                        </button>
                    </div>
                </div>
            `);

            // 绑定查看知识库按钮事件
            $('#viewKnowledgeBaseBtn').click(function() {
                // 切换到预设内容标签页
                $('#preset-tab').tab('show');

                // 尝试选中新创建的知识库
                setTimeout(function() {
                    $(`.preset-item[data-preset-id="${data.preset.id}"]`).click();
                }, 500);
            });

            // 绑定查看整本书预设模板按钮事件
            $('#viewBookTemplateBtn').click(function() {
                // 切换到预设内容标签页
                $('#preset-tab').tab('show');

                // 搜索整本书预设模板
                setTimeout(function() {
                    $('#presetSearch').val(understandingGenerator.novelTitle + ' - 整本书预设模板').trigger('input');
                }, 500);
            });

            // 绑定查看章节预设模板按钮事件
            $('#viewChapterTemplatesBtn').click(function() {
                // 切换到预设内容标签页
                $('#preset-tab').tab('show');

                // 搜索章节预设模板
                setTimeout(function() {
                    $('#presetSearch').val(understandingGenerator.novelTitle + ' - 预设模板').trigger('input');
                }, 500);
            });

            // 切换到预设内容标签页
            setTimeout(function() {
                $('#preset-tab').tab('show');
            }, 2000);

            // 尝试重新加载预设列表
            try {
                // 调用loadPresets函数
                if (typeof loadPresets === 'function') {
                    loadPresets();
                    addLogEntry('info', '预设列表已刷新');

                    // 尝试选中新创建的知识库
                    setTimeout(function() {
                        $(`.preset-item[data-preset-id="${data.preset.id}"]`).click();
                    }, 1000);
                } else {
                    addLogEntry('warn', '找不到loadPresets函数');
                }
            } catch (e) {
                addLogEntry('warn', `无法刷新预设列表: ${e.message}`);
            }
        } else {
            addLogEntry('error', `知识库保存失败: ${data.error || '未知错误'}`);

            // 显示错误信息
            $('#analysisContent').html(`
                <div class="alert alert-danger">
                    <h4><i class="fas fa-times-circle me-2"></i>知识库保存失败</h4>
                    <p>无法保存知识库: ${data.error || '未知错误'}</p>
                    <p>请检查网络连接，或者刷新页面重试。</p>
                </div>
            `);
        }
    })
    .catch(error => {
        addLogEntry('error', `知识库保存请求失败: ${error.message}`);

        // 显示错误信息
        $('#analysisContent').html(`
            <div class="alert alert-danger">
                <h4><i class="fas fa-times-circle me-2"></i>知识库保存失败</h4>
                <p>保存知识库时出错: ${error.message}</p>
                <p>请检查网络连接，或者刷新页面重试。</p>
            </div>
        `);
    });
}
