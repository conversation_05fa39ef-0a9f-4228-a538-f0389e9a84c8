<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}九猫小说分析系统{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">

    <!-- Favicon错误处理脚本 - 必须最先加载 -->
    <script src="{{ url_for('static', filename='js/favicon-error-handler.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/favicon-error-handler.js';" crossorigin="anonymous"></script>

    <!-- 跨域修复和错误处理脚本 - 必须最先加载 -->
    <script src="{{ url_for('static', filename='js/fix-entry.js') }}" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='js/resource-loader.js') }}" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='js/resource-loader-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/resource-loader-fix.js';" crossorigin="anonymous"></script>

    <!-- API路径修复脚本 - 修复所有API请求中的路径错误 -->
    <script src="{{ url_for('static', filename='js/api-path-fixer.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/api-path-fixer.js';" crossorigin="anonymous"></script>

    <!-- 模板API直接修复脚本 - 修复模板API路径问题 -->
    <script src="{{ url_for('static', filename='js/direct-template-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/direct-template-fix.js';" crossorigin="anonymous"></script>

    <!-- 模板详情布局修复脚本 - 修复模板详情页面布局 -->
    <script src="{{ url_for('static', filename='js/template-detail-layout-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/template-detail-layout-fix.js';" crossorigin="anonymous"></script>

    <!-- 修复加载器 - 负责加载所有其他修复脚本 -->
    <script src="{{ url_for('static', filename='js/fix-loader.js') }}" crossorigin="anonymous"></script>

    <!-- 优化版综合修复脚本 - 最高优先级 (替代了多个单独的脚本) -->
    <script src="{{ url_for('static', filename='js/optimized-fixes.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/optimized-fixes.js';" crossorigin="anonymous"></script>

    <!-- 统一导航修复脚本 - 最高优先级，替代所有其他导航修复脚本 -->
    <script src="{{ url_for('static', filename='js/unified-navigation-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/unified-navigation-fix.js';" crossorigin="anonymous"></script>

    <!-- 小说详情页面按钮修复脚本 - 专门用于修复小说详情页面的功能按钮 -->
    <script src="{{ url_for('static', filename='js/novel-detail-button-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-detail-button-fix.js';" crossorigin="anonymous"></script>

    <!-- 导航调试工具 - 用于调试导航问题 -->
    <script src="{{ url_for('static', filename='js/navigation-debug.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/navigation-debug.js';" crossorigin="anonymous"></script>

    <!-- 以下脚本已被统一导航修复脚本替代，仅作为备用 -->
    <!--
    <script src="{{ url_for('static', filename='js/global-navigation-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/global-navigation-fix.js';" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='js/chapter-navigation-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-navigation-fix.js';" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='js/novel-function-button-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-function-button-fix.js';" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='js/emergency-navigation-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/emergency-navigation-fix.js';" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='js/final-navigation-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/final-navigation-fix.js';" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='js/novel-navigation-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-navigation-fix.js';" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='js/navigation-test.js') }}" crossorigin="anonymous"></script>
    -->

    <!-- 错误提示隐藏样式 -->
    <style>
        /* 隐藏错误提示框 - 内联版本 */
        div[style*="border: 1px solid rgb(238, 0, 0)"],
        div[style*="border:1px solid #e00"],
        div[style*="border:1px solid red"],
        div[style*="background-color: rgb(255, 236, 236)"],
        div[style*="color: rgb(136, 0, 0)"],
        div[style*="color:#800"],
        div[style*="background-color:#fee"] {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            overflow: hidden !important;
            opacity: 0 !important;
        }
    </style>

    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style" crossorigin="anonymous">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js" as="script" crossorigin="anonymous">

    <!-- 加载基础CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" crossorigin="anonymous">
    <link rel="stylesheet" href="/static/css/main.css">

    <!-- 推理内容显示修复CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/reasoning-content-fix.css') }}">

    <!-- Font Awesome 图标库 - 使用CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" crossorigin="anonymous" onerror="this.onerror=null;this.href='/static/css/fontawesome.all.min.css';">

    <!-- 基本图标备用样式 -->
    <style>
        .fa, .fas { font-family: Arial, sans-serif; }
        .fa-bars:before { content: "≡"; }
        .fa-home:before { content: "⌂"; }
        .fa-book:before { content: "📚"; }
        .fa-chart-bar:before { content: "📊"; }
        .fa-user:before { content: "👤"; }
        .fa-cog:before { content: "⚙"; }
        .fa-upload:before { content: "⬆"; }
        .fa-download:before { content: "⬇"; }
    </style>

    <!-- 维度选择修复脚本 - 保留关键业务功能 -->
    <script src="{{ url_for('static', filename='js/dimension-selection-fix.js') }}" crossorigin="anonymous"></script>

    <!-- 直接维度选择修复脚本 - 确保维度选择对话框正确显示 -->
    <script src="{{ url_for('static', filename='js/dimension-selection-direct-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-selection-direct-fix.js';" crossorigin="anonymous"></script>

    <!-- 维度选择调试工具 - 用于调试维度选择问题 -->
    <script src="{{ url_for('static', filename='js/dimension-debugger.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-debugger.js';" crossorigin="anonymous"></script>

    <!-- 维度下拉菜单修复脚本 - 完全重构维度选择下拉菜单功能 -->
    <script src="{{ url_for('static', filename='js/dimension-dropdown-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-dropdown-fix.js';" crossorigin="anonymous"></script>

    <!-- 维度选择终极修复脚本 - 解决维度选择下拉菜单无法打开的问题 -->
    <script src="/direct-static/js/dimension-selector-ultimate-fix.js" onerror="this.onerror=null;this.src='/static/js/dimension-selector-ultimate-fix.js';" crossorigin="anonymous"></script>

    <!-- 页面特定CSS -->
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 浏览器扩展警告 -->
    {% include 'extension-warning.html' %}

    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">九猫小说分析系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home me-1"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/novels"><i class="fas fa-book me-1"></i> 小说列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/upload"><i class="fas fa-upload me-1"></i> 上传分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis"><i class="fas fa-chart-bar me-1"></i> 分析记录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard"><i class="fas fa-tachometer-alt me-1"></i> 数据中心</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/settings"><i class="fas fa-cog me-1"></i> 设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>九猫小说分析系统</h5>
                    <p class="small">专为作家和编辑设计的文本分析工具</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="small">版本 1.0.0 | 使用 DeepSeek R1 API</p>
                    <p class="small">© 2025 九猫系统</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 核心第三方库 - 使用现代加载方式 -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous" async></script>

    <!-- 备用脚本加载 -->
    <script>
        // 检查jQuery是否加载成功
        setTimeout(function() {
            if (typeof jQuery === 'undefined') {
                var script = document.createElement('script');
                script.src = '/static/js/lib/jquery.min.js';
                document.head.appendChild(script);
            }

            // 检查Bootstrap是否加载成功
            if (typeof bootstrap === 'undefined') {
                var script = document.createElement('script');
                script.src = '/static/js/lib/bootstrap.bundle.min.js';
                document.head.appendChild(script);
            }
        }, 1000);
    </script>

    <!-- 页面特定JS -->
    {% block extra_js %}{% endblock %}

    <!-- 基本 JS -->
    <script src="{{ url_for('static', filename='js/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <!-- 历史分析修复脚本 -->
    <script src="{{ url_for('static', filename='js/novel-detail-historical-fix.js') }}"></script>

    <!-- 控制台日志修复脚本 -->
    <script src="{{ url_for('static', filename='js/console-logger-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/console-logger-fix.js';" crossorigin="anonymous"></script>

    <!-- 维度内容修复脚本 -->
    <script src="{{ url_for('static', filename='js/dimension-content-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-content-fix.js';" crossorigin="anonymous"></script>

    <!-- 维度显示修复脚本 -->
    <script src="{{ url_for('static', filename='js/dimension-display-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-display-fix.js';" crossorigin="anonymous"></script>

    <!-- 统一错误处理脚本 -->
    <script src="{{ url_for('static', filename='js/unified-error-handler.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/unified-error-handler.js';" crossorigin="anonymous"></script>

    <!-- 推理内容显示修复脚本 -->
    <script src="{{ url_for('static', filename='js/reasoning-content-display-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-display-fix.js';" crossorigin="anonymous"></script>

    <!-- 推理内容加载器脚本 -->
    <script src="{{ url_for('static', filename='js/reasoning-content-loader.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-loader.js';" crossorigin="anonymous"></script>

    <!-- 分析结果显示修复脚本 -->
    <script src="{{ url_for('static', filename='js/analysis-complete-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-complete-fix.js';" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='js/analysis-result-loader.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-result-loader.js';" crossorigin="anonymous"></script>

    {% block scripts %}{% endblock %}
</body>
</html>
