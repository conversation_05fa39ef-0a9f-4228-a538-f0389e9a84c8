{% extends "v2/base.html" %}

{% block title %}小说列表 - 九猫小说分析系统v2.0{% endblock %}

{% block extra_css %}
<style>
    .novels-header {
        background-color: var(--primary-light);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    .novel-card {
        transition: all 0.3s ease;
        height: 100%;
    }
    .novel-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .novel-card .card-footer {
        background-color: transparent;
    }
    .novel-status {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
    }
    .search-box {
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 小说列表标题 -->
<div class="novels-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">小说列表</h1>
            <p class="mb-0">共 {{ novels|length }} 部小说</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('v2.upload_novel') }}" class="btn btn-primary">
                <i class="fas fa-upload me-2"></i>上传新小说
            </a>
        </div>
    </div>
</div>

<!-- 搜索框 -->
<div class="search-box">
    <div class="card">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索小说标题或作者...">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter">
                        <option value="all">所有状态</option>
                        <option value="analyzed">已分析</option>
                        <option value="not-analyzed">未分析</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="sortOption">
                        <option value="newest">最新上传</option>
                        <option value="oldest">最早上传</option>
                        <option value="title">标题排序</option>
                        <option value="word-count-desc">字数从多到少</option>
                        <option value="word-count-asc">字数从少到多</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 小说列表 -->
{% if novels %}
    <div class="row" id="novelsList">
        {% for novel in novels %}
            <div class="col-md-4 col-sm-6 mb-4 novel-item" 
                 data-title="{{ novel.title }}" 
                 data-author="{{ novel.author or '' }}" 
                 data-status="{{ 'analyzed' if novel.is_analyzed else 'not-analyzed' }}"
                 data-date="{{ novel.created_at.timestamp() }}"
                 data-word-count="{{ novel.word_count }}">
                <div class="card novel-card">
                    <div class="card-body">
                        <span class="novel-status badge bg-{{ 'success' if novel.is_analyzed else 'warning' }}">
                            {{ '已分析' if novel.is_analyzed else '未分析' }}
                        </span>
                        <h4 class="card-title mb-2">{{ novel.title }}</h4>
                        <p class="card-text text-muted mb-3">
                            {% if novel.author %}作者: {{ novel.author }}<br>{% endif %}
                            字数: {{ novel.word_count }}<br>
                            上传时间: {{ novel.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </p>
                    </div>
                    <div class="card-footer">
                        <a href="{{ url_for('v2.view_novel', novel_id=novel.id) }}" class="btn btn-primary w-100">
                            <i class="fas fa-book-open me-1"></i>查看详情
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-book fa-4x text-muted mb-3"></i>
        <h3>暂无小说</h3>
        <p class="text-muted mb-4">您还没有上传任何小说，点击下方按钮开始上传</p>
        <a href="{{ url_for('v2.upload_novel') }}" class="btn btn-primary">
            <i class="fas fa-upload me-2"></i>上传小说
        </a>
    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const statusFilter = document.getElementById('statusFilter');
        const sortOption = document.getElementById('sortOption');
        const novelsList = document.getElementById('novelsList');
        const novelItems = document.querySelectorAll('.novel-item');
        
        // 搜索和筛选函数
        function filterAndSortNovels() {
            const searchTerm = searchInput.value.toLowerCase();
            const status = statusFilter.value;
            const sort = sortOption.value;
            
            // 筛选小说
            novelItems.forEach(item => {
                const title = item.getAttribute('data-title').toLowerCase();
                const author = item.getAttribute('data-author').toLowerCase();
                const itemStatus = item.getAttribute('data-status');
                
                const matchesSearch = title.includes(searchTerm) || author.includes(searchTerm);
                const matchesStatus = status === 'all' || itemStatus === status;
                
                if (matchesSearch && matchesStatus) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
            
            // 排序小说
            const items = Array.from(novelItems).filter(item => item.style.display !== 'none');
            
            items.sort((a, b) => {
                switch (sort) {
                    case 'newest':
                        return parseFloat(b.getAttribute('data-date')) - parseFloat(a.getAttribute('data-date'));
                    case 'oldest':
                        return parseFloat(a.getAttribute('data-date')) - parseFloat(b.getAttribute('data-date'));
                    case 'title':
                        return a.getAttribute('data-title').localeCompare(b.getAttribute('data-title'));
                    case 'word-count-desc':
                        return parseInt(b.getAttribute('data-word-count')) - parseInt(a.getAttribute('data-word-count'));
                    case 'word-count-asc':
                        return parseInt(a.getAttribute('data-word-count')) - parseInt(b.getAttribute('data-word-count'));
                    default:
                        return 0;
                }
            });
            
            // 重新排列小说
            items.forEach(item => {
                novelsList.appendChild(item);
            });
        }
        
        // 添加事件监听器
        searchInput.addEventListener('input', filterAndSortNovels);
        statusFilter.addEventListener('change', filterAndSortNovels);
        sortOption.addEventListener('change', filterAndSortNovels);
    });
</script>
{% endblock %}
