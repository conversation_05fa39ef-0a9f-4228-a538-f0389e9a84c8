/**
 * 九猫 - 跨域错误修复入口
 * 必须在其他脚本之前加载，以捕获并处理跨域脚本错误
 * 版本: 1.1.0
 */

(function() {
    console.log('【跨域错误修复入口】已初始化');
    
    // 在控制台显示修复日志
    function log(message, level) {
        try {
            const prefix = '【跨域错误修复】';
            switch(level) {
                case 'error': console.error(prefix + message); break;
                case 'warn': console.warn(prefix + message); break;
                default: console.log(prefix + message);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }
    
    // 状态跟踪
    const state = {
        initialized: false,
        originalOnError: window.onerror,
        originalUnhandledRejection: window.onunhandledrejection,
        fixedScripts: new Set(),
        errorCount: 0,
        promiseErrorCount: 0
    };

    // 为所有现有脚本添加crossorigin属性
    function fixExistingScripts() {
        try {
            const scripts = document.querySelectorAll('script[src]');
            log(`修复 ${scripts.length} 个现有脚本...`);
            
            scripts.forEach(script => {
                if (!script.hasAttribute('crossorigin')) {
                    script.setAttribute('crossorigin', 'anonymous');
                    state.fixedScripts.add(script.src);
                    log(`已为脚本添加crossorigin属性: ${script.src.split('/').pop()}`);
                }
            });
            
            // 修复外部样式表
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"][href]');
            log(`修复 ${stylesheets.length} 个现有样式表...`);
            
            stylesheets.forEach(link => {
                if (!link.hasAttribute('crossorigin')) {
                    link.setAttribute('crossorigin', 'anonymous');
                    state.fixedScripts.add(link.href);
                    log(`已为样式表添加crossorigin属性: ${link.href.split('/').pop()}`);
                }
            });
        } catch (e) {
            log(`修复现有脚本出错: ${e.message}`, 'error');
        }
    }
    
    // 检查是否是浏览器扩展相关错误
    function isExtensionError(error) {
        if (!error) return false;
        
        const errorString = error.toString();
        const errorMessage = error.message || '';
        
        // 检查常见的扩展错误模式
        return (
            // Chrome扩展消息通道错误
            errorMessage.includes('message channel closed') ||
            errorMessage.includes('asynchronous response') ||
            
            // 扩展的内容脚本错误
            errorString.includes('extension') ||
            errorMessage.includes('extension') ||
            
            // 因扩展引起的跨域访问错误
            (errorMessage.includes('cross-origin') && errorMessage.includes('extension')) ||
            
            // 扩展被禁用或卸载导致的错误
            errorMessage.includes('Extension context invalidated') ||
            
            // Firefox扩展错误
            errorMessage.includes('Message manager disconnected')
        );
    }
    
    // 处理未捕获的Promise错误
    function handlePromiseErrors() {
        // 保存原始处理程序
        state.originalUnhandledRejection = window.onunhandledrejection;
        
        // 设置新的处理程序
        window.onunhandledrejection = function(event) {
            state.promiseErrorCount++;
            const error = event.reason;
            
            // 检查是否是浏览器扩展相关错误
            if (isExtensionError(error)) {
                log(`已捕获并阻止扩展相关的Promise错误: ${error.message || error}`, 'warn');
                event.preventDefault();
                return true;
            }
            
            // 检查是否是Script error或未知来源错误
            if (error && (
                error.message === 'Script error.' || 
                error.message === 'Script error' || 
                error.message?.includes('Script error') ||
                !error.filename || 
                error.filename === 'undefined' || 
                error.filename === 'unknown')
            ) {
                log(`已捕获并阻止Script error相关的Promise错误: ${error.message || error}`, 'warn');
                event.preventDefault();
                
                // 如果还未初始化，尝试修复现有脚本
                if (!state.initialized) {
                    log('尝试修复现有脚本...');
                    fixExistingScripts();
                    ensureCoreFixesLoaded();
                    state.initialized = true;
                }
                
                return true;
            }
            
            // 对于其他错误，调用原始处理程序（如果存在）
            if (typeof state.originalUnhandledRejection === 'function') {
                return state.originalUnhandledRejection.call(window, event);
            }
            
            return false;
        };
        
        log('已设置Promise错误处理');
    }
    
    // 设置全局错误处理
    function setupErrorHandling() {
        // 保存原始的onerror处理器
        state.originalOnError = window.onerror;
        
        // 设置新的onerror处理器，提高优先级
        window.onerror = function(message, source, lineno, colno, error) {
            state.errorCount++;
            
            // 特别处理41:30位置的错误
            if (message === 'Script error.' && (!source || source === '41:30')) {
                log('捕获到特定位置(41:30)的脚本错误，可能是扩展导致', 'warn');
                return true;
            }
            
            // 检查是否是浏览器扩展相关错误
            if (error && isExtensionError(error)) {
                log(`已捕获并阻止扩展相关错误: ${error.message || message}`, 'warn');
                return true;
            }
            
            // 如果是Script error或未知来源错误
            if (message === 'Script error.' || 
                message === 'Script error' || 
                message.includes('Script error') ||
                !source || source === 'undefined' || source === 'unknown') {
                
                log(`捕获到脚本错误: ${message}`, 'warn');
                
                // 如果还未初始化，尝试修复现有脚本
                if (!state.initialized) {
                    log('尝试修复现有脚本...');
                    fixExistingScripts();
                    state.initialized = true;
                }
                
                // 确保加载核心修复脚本
                ensureCoreFixesLoaded();
                
                // 阻止错误冒泡
                return true;
            }
            
            // 对于其他错误，调用原始handler（如果存在）
            if (typeof state.originalOnError === 'function') {
                return state.originalOnError.apply(window, arguments);
            }
            
            return false;
        };
        
        log('已设置全局错误处理');
        
        // 处理Promise错误
        handlePromiseErrors();
    }
    
    // 监听动态添加的脚本
    function monitorDynamicScripts() {
        try {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            // 处理新添加的脚本
                            if (node.tagName === 'SCRIPT' && node.src && !node.hasAttribute('crossorigin')) {
                                node.setAttribute('crossorigin', 'anonymous');
                                state.fixedScripts.add(node.src);
                                log(`已为动态添加的脚本添加crossorigin属性: ${node.src.split('/').pop()}`);
                            }
                            
                            // 处理新添加的样式表
                            if (node.tagName === 'LINK' && node.rel === 'stylesheet' && node.href && !node.hasAttribute('crossorigin')) {
                                node.setAttribute('crossorigin', 'anonymous');
                                state.fixedScripts.add(node.href);
                                log(`已为动态添加的样式表添加crossorigin属性: ${node.href.split('/').pop()}`);
                            }
                        });
                    }
                });
            });
            
            observer.observe(document.documentElement, {
                childList: true,
                subtree: true
            });
            
            log('已设置动态脚本监控');
        } catch (e) {
            log(`设置动态脚本监控出错: ${e.message}`, 'error');
        }
    }
    
    // 确保核心修复脚本已加载
    function ensureCoreFixesLoaded() {
        try {
            // 检查resource-loader.js是否已加载
            if (!document.querySelector('script[src*="resource-loader.js"]')) {
                log('加载资源加载器...');
                
                const resourceLoader = document.createElement('script');
                resourceLoader.src = '/static/js/resource-loader.js';
                resourceLoader.setAttribute('crossorigin', 'anonymous');
                resourceLoader.async = false;
                document.head.appendChild(resourceLoader);
            }
            
            // 检查script-error-fix.js是否已加载
            if (!document.querySelector('script[src*="script-error-fix.js"]')) {
                log('加载脚本错误修复...');
                
                const scriptErrorFix = document.createElement('script');
                scriptErrorFix.src = '/static/js/script-error-fix.js';
                scriptErrorFix.setAttribute('crossorigin', 'anonymous');
                scriptErrorFix.async = false;
                document.head.appendChild(scriptErrorFix);
            }
            
            // 检查fix-loader.js是否已加载
            if (!document.querySelector('script[src*="fix-loader.js"]')) {
                log('加载修复加载器...');
                
                const fixLoader = document.createElement('script');
                fixLoader.src = '/static/js/fix-loader.js';
                fixLoader.setAttribute('crossorigin', 'anonymous');
                fixLoader.async = false;
                document.head.appendChild(fixLoader);
            }
            
        } catch (e) {
            log(`加载核心修复脚本出错: ${e.message}`, 'error');
        }
    }

    // 修复浏览器扩展引起的问题
    function fixBrowserExtensionIssues() {
        try {
            log('安装浏览器扩展问题修复...');
            
            // 修复Chrome扩展的消息通道错误
            if (window.chrome && window.chrome.runtime) {
                const originalSendMessage = chrome.runtime.sendMessage;
                if (originalSendMessage) {
                    chrome.runtime.sendMessage = function() {
                        try {
                            return originalSendMessage.apply(this, arguments);
                        } catch (e) {
                            log(`已拦截扩展sendMessage错误: ${e.message}`, 'warn');
                            return Promise.resolve({ error: 'Extension message blocked by page' });
                        }
                    };
                    log('已修复Chrome扩展消息传递');
                }
            }
            
            // 全局拦截某些来自扩展的错误
            const originalFetch = window.fetch;
            if (originalFetch) {
                window.fetch = function() {
                    try {
                        return originalFetch.apply(this, arguments);
                    } catch (e) {
                        if (isExtensionError(e)) {
                            log(`已拦截扩展fetch错误: ${e.message}`, 'warn');
                            return Promise.resolve(new Response('{}', { status: 200 }));
                        }
                        throw e;
                    }
                };
                log('已修复fetch相关扩展问题');
            }
            
            // 监听并屏蔽扩展发起的特定消息事件
            window.addEventListener('message', function(event) {
                try {
                    if (event.source === window && 
                        event.data && 
                        (event.data.type === 'FROM_EXTENSION' || 
                         (typeof event.data === 'object' && 'from_extension' in event.data))) {
                        log('已阻止疑似来自扩展的消息', 'info');
                        event.stopImmediatePropagation();
                        event.preventDefault();
                    }
                } catch (e) {
                    // 忽略消息处理错误
                }
            }, true);
            
            log('浏览器扩展问题修复完成');
        } catch (e) {
            log(`修复浏览器扩展问题出错: ${e.message}`, 'error');
        }
    }
    
    // 初始化修复
    function initialize() {
        if (state.initialized) {
            return;
        }
        
        log('初始化跨域错误修复...');
        
        // 修复现有脚本
        fixExistingScripts();
        
        // 设置全局错误处理
        setupErrorHandling();
        
        // 监听动态添加的脚本
        monitorDynamicScripts();
        
        // 确保核心修复脚本已加载
        ensureCoreFixesLoaded();
        
        // 修复浏览器扩展问题
        fixBrowserExtensionIssues();
        
        state.initialized = true;
        log('跨域错误修复初始化完成');
    }
    
    // 立即初始化
    initialize();
    
    // 在DOM加载完成后再次检查
    document.addEventListener('DOMContentLoaded', function() {
        log('DOM已加载，再次检查脚本...');
        fixExistingScripts();
    });
    
    // 在页面完全加载后最终检查
    window.addEventListener('load', function() {
        log('页面已加载，最终检查脚本...');
        fixExistingScripts();
        // 重新应用扩展修复
        fixBrowserExtensionIssues();
    });
    
    // 导出到全局命名空间以便调试
    window.crossOriginFix = {
        state: state,
        fixExistingScripts: fixExistingScripts,
        ensureCoreFixesLoaded: ensureCoreFixesLoaded,
        fixBrowserExtensionIssues: fixBrowserExtensionIssues
    };
})(); 