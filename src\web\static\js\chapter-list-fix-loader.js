/**
 * 九猫系统 - 章节列表页面修复脚本加载器
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于在章节列表页面加载所有必要的修复脚本
 */

(function() {
    console.log('[章节列表修复加载器] 初始化...');
    
    // 修复脚本列表
    const fixScripts = [
        '/static/js/jquery-on-direct-fix.js',
        '/static/js/jquery-on-method-fix.js',
        '/static/js/api-path-fix.js',
        '/static/js/template-path-fix.js',
        '/static/js/chapter-template-layout-fix.js'
    ];
    
    // 已加载的脚本
    const loadedScripts = new Set();
    
    // 加载脚本
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            if (loadedScripts.has(src)) {
                console.log(`[章节列表修复加载器] 脚本已加载: ${src}`);
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            
            script.onload = () => {
                console.log(`[章节列表修复加载器] 脚本加载成功: ${src}`);
                loadedScripts.add(src);
                resolve();
            };
            
            script.onerror = (error) => {
                console.error(`[章节列表修复加载器] 脚本加载失败: ${src}`, error);
                reject(error);
            };
            
            document.head.appendChild(script);
        });
    }
    
    // 加载所有修复脚本
    async function loadAllFixScripts() {
        console.log('[章节列表修复加载器] 开始加载所有修复脚本...');
        
        for (const scriptSrc of fixScripts) {
            try {
                await loadScript(scriptSrc);
            } catch (error) {
                console.error(`[章节列表修复加载器] 加载脚本失败: ${scriptSrc}`, error);
            }
        }
        
        console.log('[章节列表修复加载器] 所有修复脚本加载完成');
    }
    
    // 等待DOM加载完成
    function onDOMReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }
    
    // 初始化
    onDOMReady(() => {
        loadAllFixScripts();
    });
})();
