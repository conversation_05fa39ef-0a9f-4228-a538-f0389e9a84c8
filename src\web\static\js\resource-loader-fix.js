/**
 * 九猫 - 资源加载修复脚本
 * 解决静态资源加载404错误问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('资源加载修复脚本已加载');

    // jQuery加载保证函数 - 添加在脚本开始
    function ensureJQuery(callback) {
        // 如果jQuery已经存在
        if (typeof jQuery !== 'undefined') {
            if (callback && typeof callback === 'function') {
                callback(jQuery);
            }
            return;
        }
        
        console.log('jQuery未找到，尝试加载...');
        
        // 创建script元素加载jQuery
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';
        script.onload = function() {
            console.log('jQuery加载成功!');
            if (callback && typeof callback === 'function') {
                callback(jQuery);
            }
        };
        script.onerror = function() {
            console.error('加载jQuery失败，尝试使用备用CDN');
            // 尝试备用CDN
            const backupScript = document.createElement('script');
            backupScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js';
            backupScript.onload = function() {
                console.log('jQuery通过备用CDN加载成功!');
                if (callback && typeof callback === 'function') {
                    callback(jQuery);
                }
            };
            backupScript.onerror = function() {
                console.error('所有jQuery加载尝试均失败');
            };
            document.head.appendChild(backupScript);
        };
        document.head.appendChild(script);
    }

    // 在页面加载时自动确保jQuery加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            ensureJQuery();
        });
    } else {
        ensureJQuery();
    }

    // 资源映射表，将404资源映射到正确的路径或CDN
    const resourceMap = {
        // CSS资源
        '/static/css/bootstrap.min.css': 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css',
        '/static/css/bootstrap.css': 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css',

        // JavaScript资源
        '/static/js/jquery.min.js': 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js',
        '/static/js/bootstrap.bundle.min.js': 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js',
        '/static/js/chart.min.js': 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js',

        // 本地资源备用映射
        '/static/css/lib/bootstrap.min.css': '/static/css/bootstrap.min.css',
        '/static/js/lib/jquery-3.6.0.min.js': '/static/js/jquery.min.js',
        '/static/js/lib/bootstrap.bundle.min.js': '/static/js/bootstrap.bundle.min.js',
        '/static/js/lib/chart.min.js': '/static/js/chart.min.js'
    };

    // 拦截资源加载错误
    document.addEventListener('error', function(event) {
        const target = event.target;

        // 只处理链接和脚本元素
        if (target.tagName === 'LINK' || target.tagName === 'SCRIPT') {
            const url = target.href || target.src;
            if (!url) return;

            // 提取路径部分
            const path = new URL(url).pathname;
            console.log(`资源加载失败: ${path}`);

            // 检查是否有映射
            if (resourceMap[path]) {
                console.log(`找到资源映射: ${path} -> ${resourceMap[path]}`);

                // 创建新元素
                let newElement;
                if (target.tagName === 'LINK') {
                    newElement = document.createElement('link');
                    newElement.rel = 'stylesheet';
                    newElement.href = resourceMap[path];
                } else {
                    newElement = document.createElement('script');
                    newElement.src = resourceMap[path];
                }

                // 复制其他属性
                for (let i = 0; i < target.attributes.length; i++) {
                    const attr = target.attributes[i];
                    if (attr.name !== 'href' && attr.name !== 'src') {
                        newElement.setAttribute(attr.name, attr.value);
                    }
                }

                // 替换元素
                if (target.parentNode) {
                    target.parentNode.replaceChild(newElement, target);
                    console.log(`已替换资源: ${path}`);
                }
            } else if (url.includes('localhost') || url.includes('127.0.0.1')) {
                // 尝试从CDN加载常见资源
                if (url.includes('bootstrap')) {
                    console.log('尝试从CDN加载Bootstrap');
                    loadFromCDN('bootstrap');
                } else if (url.includes('jquery')) {
                    console.log('尝试从CDN加载jQuery');
                    loadFromCDN('jquery');
                } else if (url.includes('chart')) {
                    console.log('尝试从CDN加载Chart.js');
                    loadFromCDN('chart');
                }
            }
        }
    }, true);

    // 从CDN加载常见资源
    function loadFromCDN(resourceType) {
        switch (resourceType) {
            case 'bootstrap':
                // 加载Bootstrap CSS
                if (!document.querySelector('link[href*="bootstrap"]')) {
                    const link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css';
                    document.head.appendChild(link);
                }

                // 加载Bootstrap JS
                if (!document.querySelector('script[src*="bootstrap"]')) {
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js';
                    document.head.appendChild(script);
                }
                break;

            case 'jquery':
                if (!document.querySelector('script[src*="jquery"]')) {
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js';
                    document.head.appendChild(script);
                }
                break;

            case 'chart':
                if (!document.querySelector('script[src*="chart"]')) {
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js';
                    document.head.appendChild(script);
                }
                break;
        }
    }

    // 检查页面上是否已加载核心资源
    function checkCoreResources() {
        console.log('检查核心资源是否已加载');

        // 检查Bootstrap CSS
        if (!document.querySelector('link[href*="bootstrap"]')) {
            console.log('未找到Bootstrap CSS，尝试加载');
            loadFromCDN('bootstrap');
        }

        // 检查jQuery
        if (typeof jQuery === 'undefined') {
            console.log('未找到jQuery，尝试加载');
            loadFromCDN('jquery');
        }

        // 检查Chart.js
        if (typeof Chart === 'undefined') {
            console.log('未找到Chart.js，尝试加载');
            loadFromCDN('chart');
        }
    }

    // 在页面加载完成后检查核心资源
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkCoreResources);
    } else {
        checkCoreResources();
    }

    // 在页面完全加载后再次检查
    window.addEventListener('load', function() {
        setTimeout(checkCoreResources, 1000);
    });

    // 修复resource-loader.js中的错误
    function fixResourceLoader() {
        console.log('开始修复资源加载器');

        try {
            // 检查document.body是否存在
            if (!document.body) {
                console.log('document.body不存在，等待DOM加载完成');

                // 等待DOM加载完成
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('DOM加载完成，继续修复');
                    fixResourceLoader();
                });

                return;
            }

            // 修复appendChild方法
            if (typeof document.body.appendChild !== 'function') {
                console.error('document.body.appendChild不是函数，尝试修复');

                // 保存原始的appendChild方法
                const originalAppendChild = Element.prototype.appendChild;

                // 重新定义document.body.appendChild
                document.body.appendChild = function(node) {
                    return originalAppendChild.call(this, node);
                };

                console.log('document.body.appendChild已修复');
            }

            // 修复document.head.appendChild方法
            if (document.head && typeof document.head.appendChild !== 'function') {
                console.error('document.head.appendChild不是函数，尝试修复');

                // 保存原始的appendChild方法
                const originalAppendChild = Element.prototype.appendChild;

                // 重新定义document.head.appendChild
                document.head.appendChild = function(node) {
                    return originalAppendChild.call(this, node);
                };

                console.log('document.head.appendChild已修复');
            }

            // 修复resource-loader.js中的错误
            if (window.resourceLoader) {
                console.log('找到resourceLoader对象，尝试修复');

                // 保存原始的方法
                const originalAddResource = window.resourceLoader.addResource;

                // 重新定义addResource方法
                window.resourceLoader.addResource = function(type, url, options) {
                    try {
                        // 检查document.body是否存在
                        if (!document.body) {
                            console.error('document.body不存在，无法添加资源: ' + url);
                            return null;
                        }

                        // 调用原始方法
                        return originalAddResource.call(this, type, url, options);
                    } catch (e) {
                        console.error('添加资源时出错: ' + e.message);

                        // 使用原生方法添加资源
                        try {
                            if (type === 'script') {
                                const script = document.createElement('script');
                                script.src = url;
                                script.async = true;
                                document.head.appendChild(script);
                                return script;
                            } else if (type === 'style') {
                                const link = document.createElement('link');
                                link.rel = 'stylesheet';
                                link.href = url;
                                document.head.appendChild(link);
                                return link;
                            }
                        } catch (e2) {
                            console.error('使用原生方法添加资源时出错: ' + e2.message);
                        }

                        return null;
                    }
                };

                console.log('resourceLoader.addResource已修复');
            }

            console.log('资源加载器修复完成');
        } catch (e) {
            console.error('修复资源加载器时出错: ' + e.message);
        }
    }

    // 立即执行修复
    fixResourceLoader();

    // 定期检查，确保修复生效
    let attempts = 0;
    const maxAttempts = 5;
    const interval = setInterval(function() {
        attempts++;

        // 再次修复资源加载器
        fixResourceLoader();

        // 达到最大尝试次数后停止
        if (attempts >= maxAttempts) {
            clearInterval(interval);
            console.log('资源加载器修复尝试完成');
        }
    }, 1000);

    /**
     * 九猫小说分析系统资源加载修复脚本
     * 用于修复via.placeholder.com域名解析错误
     */

    // 监听所有图片加载错误事件
    document.addEventListener('error', function(e) {
        const target = e.target;
        
        // 仅处理图片元素的加载错误
        if (target.tagName.toLowerCase() !== 'img') return;
        
        // 检查是否是placeholder.com的URL
        if (target.src && target.src.indexOf('via.placeholder.com') !== -1) {
            console.log('检测到占位图片加载错误，替换为本地图片:', target.src);
            
            // 提取URL中的文本参数
            let textParam = '';
            try {
                const urlObj = new URL(target.src);
                textParam = urlObj.searchParams.get('text') || '';
            } catch (err) {
                // 如果URL解析失败，尝试手动提取text参数
                const textMatch = target.src.match(/text=([^&]+)/);
                if (textMatch && textMatch[1]) {
                    textParam = decodeURIComponent(textMatch[1]);
                }
            }
            
            // 根据text参数选择合适的本地图片
            if (textParam.includes('矛盾环图示')) {
                target.src = '/static/img/placeholder_conflict_cycle.png';
            } else {
                // 对于其他占位图，使用通用占位图
                target.src = '/static/img/placeholder_general.png';
            }
            
            // 防止循环触发error事件
            target.onerror = null;
        }
    }, true);  // 使用捕获阶段以确保能捕获所有图片错误
    
    // 对已存在的图片进行检查和修复
    function fixExistingImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (img.src && img.src.indexOf('via.placeholder.com') !== -1) {
                // 创建一个新的Image对象进行测试
                const testImg = new Image();
                testImg.onload = function() {
                    // 图片可以正常加载，不需要修复
                };
                testImg.onerror = function() {
                    // 图片加载失败，应用修复
                    let textParam = '';
                    try {
                        const urlObj = new URL(img.src);
                        textParam = urlObj.searchParams.get('text') || '';
                    } catch (err) {
                        const textMatch = img.src.match(/text=([^&]+)/);
                        if (textMatch && textMatch[1]) {
                            textParam = decodeURIComponent(textMatch[1]);
                        }
                    }
                    
                    if (textParam.includes('矛盾环图示')) {
                        img.src = '/static/img/placeholder_conflict_cycle.png';
                    } else {
                        img.src = '/static/img/placeholder_general.png';
                    }
                };
                testImg.src = img.src;
            }
        });
    }
    
    // 在DOM加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixExistingImages);
    } else {
        fixExistingImages();
    }
    
    // 创建一个MutationObserver来监听DOM变化，处理动态添加的图片
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    // 检查是否是IMG元素
                    if (node.nodeName === 'IMG') {
                        if (node.src && node.src.indexOf('via.placeholder.com') !== -1) {
                            fixPlaceholderImage(node);
                        }
                    }
                    // 检查添加的节点是否包含图片
                    else if (node.nodeType === 1) {  // Element节点
                        const images = node.querySelectorAll('img');
                        images.forEach(img => {
                            if (img.src && img.src.indexOf('via.placeholder.com') !== -1) {
                                fixPlaceholderImage(img);
                            }
                        });
                    }
                });
            }
        });
    });
    
    // 辅助函数：修复占位图像
    function fixPlaceholderImage(img) {
        let textParam = '';
        try {
            const urlObj = new URL(img.src);
            textParam = urlObj.searchParams.get('text') || '';
        } catch (err) {
            const textMatch = img.src.match(/text=([^&]+)/);
            if (textMatch && textMatch[1]) {
                textParam = decodeURIComponent(textMatch[1]);
            }
        }
        
        if (textParam.includes('矛盾环图示')) {
            img.src = '/static/img/placeholder_conflict_cycle.png';
        } else {
            img.src = '/static/img/placeholder_general.png';
        }
        img.onerror = null;
    }
    
    // 开始观察整个文档
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true
    });
    
    console.log('资源加载修复脚本已加载完成');
})();
