{"name": "socks-proxy-agent", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "version": "6.2.0", "main": "dist/index.js", "author": {"email": "<EMAIL>", "name": "<PERSON>", "url": "http://n8.io/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-socks-proxy-agent.git"}, "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "dependencies": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}, "devDependencies": {"@commitlint/cli": "latest", "@commitlint/config-conventional": "latest", "@types/debug": "latest", "@types/node": "latest", "cacheable-lookup": "^6.0.4", "conventional-github-releaser": "latest", "dns2": "^2.0.1", "finepack": "latest", "git-authors-cli": "latest", "mocha": "latest", "nano-staged": "latest", "npm-check-updates": "latest", "prettier-standard": "latest", "raw-body": "latest", "rimraf": "latest", "simple-git-hooks": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "standard": "latest", "standard-markdown": "latest", "standard-version": "latest", "ts-standard": "latest", "typescript": "latest"}, "engines": {"node": ">= 10"}, "files": ["dist"], "license": "MIT", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "nano-staged": {"*.js": ["prettier-standard"], "*.md": ["standard-markdown"], "package.json": ["finepack"]}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}, "typings": "dist/index.d.ts", "scripts": {"build": "tsc", "clean": "rimraf node_modules", "contributors": "(git-authors-cli && finepack && git add package.json && git commit -m 'build: contributors' --no-verify) || true", "lint": "ts-standard", "postrelease": "npm run release:tags && npm run release:github && (ci-publish || npm publish --access=public)", "prebuild": "<PERSON><PERSON><PERSON> dist", "prerelease": "npm run update:check && npm run contributors", "release": "standard-version -a", "release:github": "conventional-github-releaser -p angular", "release:tags": "git push --follow-tags origin HEAD:master", "test": "mocha --reporter spec", "update": "ncu -u", "update:check": "ncu -- --error-level 2"}, "readme": "socks-proxy-agent\n================\n### A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS\n[![Build Status](https://github.com/TooTallNate/node-socks-proxy-agent/workflows/Node%20CI/badge.svg)](https://github.com/TooTallNate/node-socks-proxy-agent/actions?workflow=Node+CI)\n\nThis module provides an `http.Agent` implementation that connects to a\nspecified SOCKS proxy server, and can be used with the built-in `http`\nand `https` modules.\n\nIt can also be used in conjunction with the `ws` module to establish a WebSocket\nconnection over a SOCKS proxy. See the \"Examples\" section below.\n\nInstallation\n------------\n\nInstall with `npm`:\n\n``` bash\nnpm install socks-proxy-agent\n```\n\n\nExamples\n--------\n\n#### TypeScript example\n\n```ts\nimport https from 'https';\nimport { SocksProxyAgent } from 'socks-proxy-agent';\n\nconst info = {\n\thostname: 'br41.nordvpn.com',\n\tuserId: '<EMAIL>',\n\tpassword: 'abcdef12345124'\n};\nconst agent = new SocksProxyAgent(info);\n\nhttps.get('https://ipinfo.io', { agent }, (res) => {\n\tconsole.log(res.headers);\n\tres.pipe(process.stdout);\n});\n```\n\n#### `http` module example\n\n```js\nvar url = require('url');\nvar http = require('http');\nvar { SocksProxyAgent } = require('socks-proxy-agent');\n\n// SOCKS proxy to connect to\nvar proxy = process.env.socks_proxy || 'socks://127.0.0.1:1080';\nconsole.log('using proxy server %j', proxy);\n\n// HTTP endpoint for the proxy to connect to\nvar endpoint = process.argv[2] || 'http://nodejs.org/api/';\nconsole.log('attempting to GET %j', endpoint);\nvar opts = url.parse(endpoint);\n\n// create an instance of the `SocksProxyAgent` class with the proxy server information\nvar agent = new SocksProxyAgent(proxy);\nopts.agent = agent;\n\nhttp.get(opts, function (res) {\n\tconsole.log('\"response\" event!', res.headers);\n\tres.pipe(process.stdout);\n});\n```\n\n#### `https` module example\n\n```js\nvar url = require('url');\nvar https = require('https');\nvar { SocksProxyAgent } = require('socks-proxy-agent');\n\n// SOCKS proxy to connect to\nvar proxy = process.env.socks_proxy || 'socks://127.0.0.1:1080';\nconsole.log('using proxy server %j', proxy);\n\n// HTTP endpoint for the proxy to connect to\nvar endpoint = process.argv[2] || 'https://encrypted.google.com/';\nconsole.log('attempting to GET %j', endpoint);\nvar opts = url.parse(endpoint);\n\n// create an instance of the `SocksProxyAgent` class with the proxy server information\nvar agent = new SocksProxyAgent(proxy);\nopts.agent = agent;\n\nhttps.get(opts, function (res) {\n\tconsole.log('\"response\" event!', res.headers);\n\tres.pipe(process.stdout);\n});\n```\n\n#### `ws` WebSocket connection example\n\n``` js\nvar WebSocket = require('ws');\nvar { SocksProxyAgent } = require('socks-proxy-agent');\n\n// SOCKS proxy to connect to\nvar proxy = process.env.socks_proxy || 'socks://127.0.0.1:1080';\nconsole.log('using proxy server %j', proxy);\n\n// WebSocket endpoint for the proxy to connect to\nvar endpoint = process.argv[2] || 'ws://echo.websocket.org';\nconsole.log('attempting to connect to WebSocket %j', endpoint);\n\n// create an instance of the `SocksProxyAgent` class with the proxy server information\nvar agent = new SocksProxyAgent(proxy);\n\n// initiate the WebSocket connection\nvar socket = new WebSocket(endpoint, { agent: agent });\n\nsocket.on('open', function () {\n\tconsole.log('\"open\" event!');\n\tsocket.send('hello world');\n});\n\nsocket.on('message', function (data, flags) {\n\tconsole.log('\"message\" event! %j %j', data, flags);\n\tsocket.close();\n});\n```\n\nLicense\n-------\n\n(The MIT License)\n\nCopyright (c) 2013 Nathan Rajlich &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}