{"name": "libnpmorg", "version": "4.0.3", "description": "Programmatic api for `npm org` commands", "author": "GitHub Inc.", "main": "lib/index.js", "keywords": ["libnpm", "npm", "package manager", "api", "orgs", "teams"], "license": "ISC", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "test": "tap", "posttest": "npm run lint", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "files": ["bin/", "lib/"], "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "minipass": "^3.1.1", "nock": "^13.2.4", "tap": "^16.0.1"}, "repository": {"type": "git", "url": "https://github.com/npm/cli.git", "directory": "workspaces/libnpmorg"}, "bugs": "https://github.com/npm/libnpmorg/issues", "homepage": "https://npmjs.com/package/libnpmorg", "dependencies": {"aproba": "^2.0.0", "npm-registry-fetch": "^13.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}}