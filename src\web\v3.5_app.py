"""
九猫小说分析写作系统v3.5 - 主应用
"""
import os
import sys
import logging
import time
import json
import traceback
from datetime import datetime, timezone
from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, send_from_directory, abort

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', f'app_{datetime.now().strftime("%Y%m%d")}.log'), encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# 创建应用
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'dev_key_for_development_only')
app.config['UPLOAD_FOLDER'] = os.path.join(os.getcwd(), 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB
app.config['TEMPLATES_AUTO_RELOAD'] = True
app.config['JSON_AS_ASCII'] = False
app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 导入v3.5版本的路由
try:
    from src.web.routes.v3_5_routes import v3_5_bp
    from src.web.routes.v3_5_api import v3_5_api_bp
    app.register_blueprint(v3_5_bp)
    app.register_blueprint(v3_5_api_bp)
    app.logger.info("成功导入v3.5版本的路由蓝图")
except ImportError as e:
    app.logger.error(f"无法导入v3.5版本的路由蓝图: {str(e)}")
    # 如果v3.5版本的路由不可用，回退到v3版本
    try:
        from src.web.routes.v3_routes import v3_bp
        from src.web.routes.v3_api import v3_api_bp
        app.register_blueprint(v3_bp)
        app.register_blueprint(v3_api_bp)
        app.logger.warning("回退到v3版本的路由蓝图")
    except ImportError as e2:
        app.logger.error(f"无法导入v3版本的路由蓝图: {str(e2)}")

# 导入共享API路由模块
try:
    from src.web.routes.shared_api_routes import shared_api_bp
    app.register_blueprint(shared_api_bp)
    app.logger.info("成功导入共享API路由模块")
except ImportError as e:
    app.logger.warning(f"无法导入共享API路由模块: {str(e)}")

# 处理Chrome DevTools请求
@app.route('/.well-known/appspecific/com.chrome.devtools.json')
def chrome_devtools():
    """处理Chrome DevTools请求，避免404日志"""
    return jsonify({})

# 添加全局缓存控制
@app.after_request
def add_cache_control(response):
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

# 添加修复脚本
@app.after_request
def add_fix_script(response):
    if response.content_type and 'text/html' in response.content_type:
        # 将响应转换为字符串
        response_data = response.get_data(as_text=True)
        
        # 检查是否已经包含修复脚本
        if 'fix_chapter_outline_button.js' not in response_data:
            # 在</body>标签前添加修复脚本
            fix_script = '<script src="/static/js/fix_chapter_outline_button.js"></script>'
            response_data = response_data.replace('</body>', f'{fix_script}</body>')
            
            # 更新响应
            response.set_data(response_data)
    
    return response

# 主页路由
@app.route('/')
def index():
    return redirect(url_for('v3_5.index'))

@app.route('/novels')
def novels():
    return redirect(url_for('v3_5.novels'))

@app.route('/upload')
def upload_novel():
    return redirect(url_for('v3_5.upload_novel'))

@app.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    return redirect(url_for('v3_5.view_novel', novel_id=novel_id))

@app.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    return redirect(url_for('v3_5.analysis', novel_id=novel_id, dimension=dimension))

@app.route('/console')
def console():
    return redirect(url_for('v3_5.console'))

# 启动应用
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
