/**
 * 九猫 - 静态文件加载修复脚本
 * 解决静态文件加载404错误问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('静态文件加载修复脚本已加载');
    
    // 核心静态文件列表
    const coreStaticFiles = [
        { type: 'css', name: 'bootstrap.min.css', path: '/static/css/lib/bootstrap.min.css', backup: '/direct-static/css/lib/bootstrap.min.css', cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' },
        { type: 'js', name: 'jquery.min.js', path: '/static/js/lib/jquery-3.6.0.min.js', backup: '/direct-static/js/lib/jquery-3.6.0.min.js', cdn: 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js' },
        { type: 'js', name: 'bootstrap.bundle.min.js', path: '/static/js/lib/bootstrap.bundle.min.js', backup: '/direct-static/js/lib/bootstrap.bundle.min.js', cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js' },
        { type: 'js', name: 'chart.min.js', path: '/static/js/lib/chart.min.js', backup: '/direct-static/js/lib/chart.min.js', cdn: 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js' }
    ];
    
    // 检查并修复静态文件
    function checkAndFixStaticFiles() {
        console.log('开始检查核心静态文件');
        
        coreStaticFiles.forEach(file => {
            checkAndLoadFile(file);
        });
    }
    
    // 检查并加载单个文件
    function checkAndLoadFile(file) {
        console.log(`检查 ${file.name} 文件`);
        
        if (file.type === 'css') {
            checkAndLoadCSS(file);
        } else if (file.type === 'js') {
            checkAndLoadJS(file);
        }
    }
    
    // 检查并加载CSS文件
    function checkAndLoadCSS(file) {
        // 检查是否已加载
        let isLoaded = false;
        const styles = document.styleSheets;
        for (let i = 0; i < styles.length; i++) {
            if (styles[i].href && (styles[i].href.includes(file.name) || styles[i].href === file.path || styles[i].href === file.backup || styles[i].href === file.cdn)) {
                isLoaded = true;
                break;
            }
        }
        
        if (!isLoaded) {
            console.log(`${file.name} 未加载，尝试加载`);
            
            // 尝试加载
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = file.path;
            
            link.onerror = function() {
                console.warn(`${file.path} 加载失败，尝试备用路径`);
                link.href = file.backup;
                
                link.onerror = function() {
                    console.warn(`${file.backup} 加载失败，尝试CDN`);
                    link.href = file.cdn;
                    
                    link.onerror = function() {
                        console.error(`${file.cdn} 加载失败，无法加载 ${file.name}`);
                    };
                };
            };
            
            document.head.appendChild(link);
        } else {
            console.log(`${file.name} 已加载`);
        }
    }
    
    // 检查并加载JS文件
    function checkAndLoadJS(file) {
        // 检查是否已加载
        let isLoaded = false;
        
        // 特殊检查Chart.js
        if (file.name === 'chart.min.js' && typeof Chart !== 'undefined') {
            isLoaded = true;
        }
        
        // 特殊检查jQuery
        if (file.name === 'jquery.min.js' && typeof jQuery !== 'undefined') {
            isLoaded = true;
        }
        
        // 特殊检查Bootstrap
        if (file.name === 'bootstrap.bundle.min.js' && typeof bootstrap !== 'undefined') {
            isLoaded = true;
        }
        
        // 检查所有脚本
        if (!isLoaded) {
            const scripts = document.scripts;
            for (let i = 0; i < scripts.length; i++) {
                if (scripts[i].src && (scripts[i].src.includes(file.name) || scripts[i].src === file.path || scripts[i].src === file.backup || scripts[i].src === file.cdn)) {
                    isLoaded = true;
                    break;
                }
            }
        }
        
        if (!isLoaded) {
            console.log(`${file.name} 未加载，尝试加载`);
            
            // 尝试加载
            const script = document.createElement('script');
            script.src = file.path;
            
            script.onerror = function() {
                console.warn(`${file.path} 加载失败，尝试备用路径`);
                script.src = file.backup;
                
                script.onerror = function() {
                    console.warn(`${file.backup} 加载失败，尝试CDN`);
                    script.src = file.cdn;
                    
                    script.onerror = function() {
                        console.error(`${file.cdn} 加载失败，无法加载 ${file.name}`);
                    };
                };
            };
            
            document.head.appendChild(script);
        } else {
            console.log(`${file.name} 已加载`);
        }
    }
    
    // 修复静态文件404错误
    function fixStaticFile404Errors() {
        // 监听所有资源加载错误
        window.addEventListener('error', function(event) {
            const target = event.target;
            
            // 检查是否是静态资源加载错误
            if (target && target.tagName) {
                const tagName = target.tagName.toLowerCase();
                
                if ((tagName === 'link' || tagName === 'script' || tagName === 'img') && target.src) {
                    console.warn(`资源加载失败: ${target.src}`);
                    
                    // 检查是否是核心静态文件
                    for (const file of coreStaticFiles) {
                        if (target.src.includes(file.name) || target.src === file.path) {
                            console.log(`尝试修复核心静态文件: ${file.name}`);
                            
                            // 尝试备用路径
                            if (tagName === 'link') {
                                target.href = file.backup;
                            } else {
                                target.src = file.backup;
                            }
                            
                            // 阻止错误传播
                            event.preventDefault();
                            return false;
                        }
                    }
                }
            }
        }, true);
    }
    
    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始执行静态文件加载修复');
        
        // 检查并修复静态文件
        checkAndFixStaticFiles();
        
        // 修复静态文件404错误
        fixStaticFile404Errors();
    });
})();
