{"version": "8.12.1", "name": "npm", "description": "a package manager for JavaScript", "workspaces": ["docs", "smoke-tests", "workspaces/*"], "files": ["index.js", "bin", "docs/content/**/*.md", "docs/output/**/*.html", "lib", "man"], "keywords": ["install", "modules", "package manager", "package.json"], "preferGlobal": true, "config": {"publishtest": false}, "homepage": "https://docs.npmjs.com/", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "repository": {"type": "git", "url": "https://github.com/npm/cli"}, "bugs": {"url": "https://github.com/npm/cli/issues"}, "directories": {"bin": "./bin", "doc": "./doc", "lib": "./lib", "man": "./man"}, "main": "./index.js", "bin": {"npm": "bin/npm-cli.js", "npx": "bin/npx-cli.js"}, "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "dependencies": {"@isaacs/string-locale-compare": "^1.1.0", "@npmcli/arborist": "^5.0.4", "@npmcli/ci-detect": "^2.0.0", "@npmcli/config": "^4.1.0", "@npmcli/fs": "^2.1.0", "@npmcli/map-workspaces": "^2.0.3", "@npmcli/package-json": "^2.0.0", "@npmcli/run-script": "^3.0.1", "abbrev": "~1.1.1", "archy": "~1.0.0", "cacache": "^16.1.0", "chalk": "^4.1.2", "chownr": "^2.0.0", "cli-columns": "^4.0.0", "cli-table3": "^0.6.2", "columnify": "^1.6.0", "fastest-levenshtein": "^1.0.12", "glob": "^8.0.1", "graceful-fs": "^4.2.10", "hosted-git-info": "^5.0.0", "ini": "^3.0.0", "init-package-json": "^3.0.2", "is-cidr": "^4.0.2", "json-parse-even-better-errors": "^2.3.1", "libnpmaccess": "^6.0.2", "libnpmdiff": "^4.0.2", "libnpmexec": "^4.0.2", "libnpmfund": "^3.0.1", "libnpmhook": "^8.0.2", "libnpmorg": "^4.0.2", "libnpmpack": "^4.0.2", "libnpmpublish": "^6.0.2", "libnpmsearch": "^5.0.2", "libnpmteam": "^4.0.2", "libnpmversion": "^3.0.1", "make-fetch-happen": "^10.1.6", "minipass": "^3.1.6", "minipass-pipeline": "^1.2.4", "mkdirp": "^1.0.4", "mkdirp-infer-owner": "^2.0.0", "ms": "^2.1.2", "node-gyp": "^9.0.0", "nopt": "^5.0.0", "npm-audit-report": "^3.0.0", "npm-install-checks": "^5.0.0", "npm-package-arg": "^9.0.2", "npm-pick-manifest": "^7.0.1", "npm-profile": "^6.0.3", "npm-registry-fetch": "^13.1.1", "npm-user-validate": "^1.0.1", "npmlog": "^6.0.2", "opener": "^1.5.2", "pacote": "^13.6.0", "parse-conflict-json": "^2.0.2", "proc-log": "^2.0.1", "qrcode-terminal": "^0.12.0", "read": "~1.0.7", "read-package-json": "^5.0.1", "read-package-json-fast": "^2.0.3", "readdir-scoped-modules": "^1.1.0", "rimraf": "^3.0.2", "semver": "^7.3.7", "ssri": "^9.0.1", "tar": "^6.1.11", "text-table": "~0.2.0", "tiny-relative-date": "^1.3.0", "treeverse": "^2.0.0", "validate-npm-package-name": "^4.0.0", "which": "^2.0.2", "write-file-atomic": "^4.0.1"}, "bundleDependencies": ["@isaacs/string-locale-compare", "@npmcli/arborist", "@npmcli/ci-detect", "@npmcli/config", "@npmcli/fs", "@npmcli/map-workspaces", "@npmcli/package-json", "@npmcli/run-script", "abbrev", "archy", "cacache", "chalk", "chownr", "cli-columns", "cli-table3", "columnify", "fastest-le<PERSON><PERSON><PERSON>", "glob", "graceful-fs", "hosted-git-info", "ini", "init-package-json", "is-cidr", "json-parse-even-better-errors", "libnpmaccess", "libnpmdiff", "libnpmexec", "libnpmfund", "libnpmhook", "libnpmorg", "libnpmpack", "libnpmpublish", "libnpmsearch", "libnpmteam", "libnpmversion", "make-fetch-happen", "minipass", "minipass-pipeline", "mkdirp", "mkdirp-infer-owner", "ms", "node-gyp", "nopt", "npm-audit-report", "npm-install-checks", "npm-package-arg", "npm-pick-manifest", "npm-profile", "npm-registry-fetch", "npm-user-validate", "npmlog", "opener", "pacote", "parse-conflict-json", "proc-log", "qrcode-terminal", "read", "read-package-json", "read-package-json-fast", "readdir-scoped-modules", "<PERSON><PERSON><PERSON>", "semver", "ssri", "tar", "text-table", "tiny-relative-date", "treeverse", "validate-npm-package-name", "which", "write-file-atomic"], "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "licensee": "^8.2.0", "nock": "^13.2.4", "spawk": "^1.7.1", "tap": "^16.0.1"}, "scripts": {"dumpconf": "env | grep npm | sort | uniq", "preversion": "bash scripts/update-authors.sh && git add AUTHORS && git commit -m \"chore: update AUTHORS\" || true", "licenses": "licensee --production --errors-only", "test": "tap", "test-all": "npm run test --if-present --workspaces --include-workspace-root", "snap": "tap", "postsnap": "make -s docs", "test:nocleanup": "NO_TEST_CLEANUP=1 npm run test --", "sudotest": "sudo npm run test --", "sudotest:nocleanup": "sudo NO_TEST_CLEANUP=1 npm run test --", "posttest": "npm run lint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "lint-all": "npm run lint --if-present --workspaces --include-workspace-root", "prelint": "rimraf test/npm_cache*", "resetdeps": "bash scripts/resetdeps.sh"}, "tap": {"test-env": ["LC_ALL=sk"], "color": 1, "files": "test/{lib,bin,index.js}", "timeout": 600, "nyc-arg": ["--exclude", "workspaces/**", "--exclude", "tap-snapshots/**"]}, "templateOSS": {"rootRepo": false, "rootModule": false, "version": "3.5.0"}, "license": "Artistic-2.0", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}