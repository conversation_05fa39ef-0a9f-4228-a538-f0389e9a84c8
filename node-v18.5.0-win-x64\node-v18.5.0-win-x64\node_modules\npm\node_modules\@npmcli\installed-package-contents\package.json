{"name": "@npmcli/installed-package-contents", "version": "1.0.7", "description": "Get the list of files installed in a package in node_modules, including bundled dependencies", "author": "<PERSON> <<EMAIL>> (https://izs.me)", "main": "index.js", "bin": {"installed-package-contents": "index.js"}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true, "color": true}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.11.0"}, "dependencies": {"npm-bundled": "^1.1.1", "npm-normalize-package-bin": "^1.0.1"}, "repository": "git+https://github.com/npm/installed-package-contents", "files": ["index.js"], "engines": {"node": ">= 10"}}