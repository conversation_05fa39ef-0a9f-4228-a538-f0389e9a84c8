# -*- coding: utf-8 -*-
'''
内存优化模块
优化内存使用，将缓存和临时数据存储到外部存储
'''
import os
import gc
import sys
import json
import time
import logging
from functools import wraps

try:
    import config
    CHUNK_SIZE = getattr(config, 'CHUNK_SIZE', 1024 * 1024)  # 默认1MB
    MAX_CACHE_SIZE = getattr(config, 'MAX_CACHE_SIZE', 100 * 1024 * 1024)  # 默认100MB
    ENABLE_OPTIMIZATION = getattr(config, 'ENABLE_MEMORY_OPTIMIZATION', True)
except (ImportError, AttributeError):
    # 默认值
    CHUNK_SIZE = 1024 * 1024  # 1MB
    MAX_CACHE_SIZE = 100 * 1024 * 1024  # 100MB
    ENABLE_OPTIMIZATION = True

def stream_file_processor(func):
    @wraps(func)
    def wrapper(file_path, *args, **kwargs):
        if not ENABLE_OPTIMIZATION:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return func(content, *args, **kwargs)

        # 流式处理
        results = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                while chunk := f.read(CHUNK_SIZE):
                    chunk_result = func(chunk, *args, **kwargs)
                    results.append(chunk_result)
                    # 强制垃圾回收
                    gc.collect()
            return ''.join(results) if isinstance(results[0], str) else results
        except Exception as e:
            print(f'流式处理失败: {str(e)}')
            # 失败时回退到常规方法
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return func(content, *args, **kwargs)
    return wrapper

def optimize_memory_usage():
    '''优化内存使用'''
    # 强制垃圾回收
    gc.collect()

    # 检查是否使用外部存储
    use_external_storage = os.environ.get('USE_EXTERNAL_STORAGE', '0').lower() in ('1', 'true', 'yes')

    # 如果使用外部存储，保存内存使用情况
    if use_external_storage:
        try:
            from src.utils.external_storage import save_memory_stats

            # 获取内存使用情况
            try:
                import psutil
                process = psutil.Process(os.getpid())
                memory_info = process.memory_info()
                memory_percent = psutil.virtual_memory().percent

                # 保存内存统计数据
                stats = {
                    'timestamp': time.time(),
                    'process_rss': memory_info.rss,
                    'process_rss_mb': memory_info.rss / 1024 / 1024,
                    'system_percent': memory_percent
                }
                save_memory_stats(stats)

                print(f'当前内存使用: {memory_info.rss / 1024 / 1024:.2f} MB ({memory_percent:.1f}%)')
                print(f'内存统计数据已保存到外部存储')

                return memory_info.rss
            except ImportError:
                print('psutil未安装，无法获取内存使用情况')
                return 0
        except ImportError:
            # 外部存储模块不可用
            pass

    # 常规内存优化
    try:
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        print(f'当前内存使用: {memory_info.rss / 1024 / 1024:.2f} MB')
        return memory_info.rss
    except ImportError:
        print('psutil未安装，无法获取内存使用情况')
        return 0

def clear_cache():
    '''清理缓存'''
    gc.collect()

    # 检查是否使用外部存储
    use_external_storage = os.environ.get('USE_EXTERNAL_STORAGE', '0').lower() in ('1', 'true', 'yes')

    # 如果使用外部存储，清理外部缓存
    if use_external_storage:
        try:
            from src.utils.external_storage import CACHE_DIR

            # 清理过期缓存文件（超过1天的缓存）
            import glob
            import time

            # 获取所有缓存文件
            cache_files = glob.glob(os.path.join(CACHE_DIR, '*.json'))

            # 当前时间
            current_time = time.time()

            # 清理过期缓存
            cleared_count = 0
            for cache_file in cache_files:
                # 获取文件修改时间
                file_time = os.path.getmtime(cache_file)

                # 如果文件超过1天未修改，删除它
                if current_time - file_time > 86400:  # 86400秒 = 1天
                    try:
                        os.remove(cache_file)
                        cleared_count += 1
                    except (PermissionError, OSError):
                        # 忽略权限错误
                        pass

            if cleared_count > 0:
                print(f'已清理 {cleared_count} 个过期缓存文件')
        except ImportError:
            # 外部存储模块不可用
            pass

    print('内存缓存已清理')
