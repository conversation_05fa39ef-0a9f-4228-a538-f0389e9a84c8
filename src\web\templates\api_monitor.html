<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫 - API调用监控</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="{{ url_for('static', filename='js/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/chart.js') }}"></script>
    <style>
        .card {
            margin-bottom: 20px;
        }
        .stat-card {
            text-align: center;
            padding: 15px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .api-log-table {
            font-size: 14px;
        }
        .refresh-btn {
            margin-bottom: 20px;
        }
    </style>
<script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';console.error('错误处理脚本加载失败，尝试备用路径')"></script>
    </head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">九猫小说分析系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/novels">小说列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/api-monitor">API监控</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/system-monitor">系统监控</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="mb-4">API调用监控</h1>
        
        <button id="refreshBtn" class="btn btn-primary refresh-btn">
            <i class="bi bi-arrow-clockwise"></i> 刷新数据
        </button>
        
        <div class="row">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-value" id="todayCalls">0</div>
                    <div class="stat-label">今日调用次数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-value" id="weekCalls">0</div>
                    <div class="stat-label">本周调用次数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-value" id="monthCalls">0</div>
                    <div class="stat-label">本月调用次数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-value" id="successRate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        最近24小时调用次数
                    </div>
                    <div class="card-body">
                        <canvas id="callsChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        调用状态分布
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        维度分析统计
                    </div>
                    <div class="card-body">
                        <canvas id="dimensionChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        令牌使用统计
                    </div>
                    <div class="card-body">
                        <canvas id="tokenChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                最近API调用日志
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover api-log-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>分析类型</th>
                                <th>状态码</th>
                                <th>输入令牌</th>
                                <th>输出令牌</th>
                                <th>总令牌</th>
                                <th>费用(元)</th>
                                <th>响应时间(ms)</th>
                            </tr>
                        </thead>
                        <tbody id="apiLogTable">
                            <!-- API日志将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                API费用统计
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card stat-card">
                            <div class="stat-value" id="todayCost">¥0.00</div>
                            <div class="stat-label">今日费用</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stat-card">
                            <div class="stat-value" id="weekCost">¥0.00</div>
                            <div class="stat-label">本周费用</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stat-card">
                            <div class="stat-value" id="monthCost">¥0.00</div>
                            <div class="stat-label">本月费用</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                API配置参数
            </div>
            <div class="card-body">
                <form id="apiConfigForm">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>分块大小配置</h5>
                            <div class="mb-3">
                                <label for="defaultChunkSize" class="form-label">默认分块大小</label>
                                <input type="number" class="form-control" id="defaultChunkSize" name="defaultChunkSize" value="{{ config.DIMENSION_CHUNK_SIZES.default }}">
                            </div>
                            {% for dimension, size in config.DIMENSION_CHUNK_SIZES.items() %}
                            {% if dimension != 'default' %}
                            <div class="mb-3">
                                <label for="chunkSize_{{ dimension }}" class="form-label">{{ dimension }} 分块大小</label>
                                <input type="number" class="form-control" id="chunkSize_{{ dimension }}" name="chunkSize_{{ dimension }}" value="{{ size }}">
                            </div>
                            {% endif %}
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <h5>最大输出Token配置</h5>
                            <div class="mb-3">
                                <label for="defaultMaxTokens" class="form-label">默认最大输出Token</label>
                                <input type="number" class="form-control" id="defaultMaxTokens" name="defaultMaxTokens" value="{{ config.DIMENSION_MAX_TOKENS.default }}">
                            </div>
                            {% for dimension, tokens in config.DIMENSION_MAX_TOKENS.items() %}
                            {% if dimension != 'default' %}
                            <div class="mb-3">
                                <label for="maxTokens_{{ dimension }}" class="form-label">{{ dimension }} 最大输出Token</label>
                                <input type="number" class="form-control" id="maxTokens_{{ dimension }}" name="maxTokens_{{ dimension }}" value="{{ tokens }}">
                            </div>
                            {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">保存配置</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 图表对象
        let callsChart, statusChart, dimensionChart, tokenChart;
        
        // 初始化图表
        function initCharts() {
            // 最近24小时调用次数图表
            const callsCtx = document.getElementById('callsChart').getContext('2d');
            callsChart = new Chart(callsCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'API调用次数',
                        data: [],
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // 调用状态分布图表
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            statusChart = new Chart(statusCtx, {
                type: 'pie',
                data: {
                    labels: ['成功', '错误', '超时'],
                    datasets: [{
                        data: [0, 0, 0],
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(255, 205, 86, 0.6)'
                        ]
                    }]
                },
                options: {
                    responsive: true
                }
            });
            
            // 维度分析统计图表
            const dimensionCtx = document.getElementById('dimensionChart').getContext('2d');
            dimensionChart = new Chart(dimensionCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '分析次数',
                        data: [],
                        backgroundColor: 'rgba(54, 162, 235, 0.6)'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // 令牌使用统计图表
            const tokenCtx = document.getElementById('tokenChart').getContext('2d');
            tokenChart = new Chart(tokenCtx, {
                type: 'bar',
                data: {
                    labels: ['输入令牌', '输出令牌'],
                    datasets: [{
                        label: '令牌数量',
                        data: [0, 0],
                        backgroundColor: [
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        // 加载API统计数据
        function loadApiStats() {
            fetch('/api/api-stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        
                        // 更新统计卡片
                        document.getElementById('todayCalls').textContent = stats.today_calls;
                        document.getElementById('weekCalls').textContent = stats.week_calls;
                        document.getElementById('monthCalls').textContent = stats.month_calls;
                        document.getElementById('successRate').textContent = stats.success_rate + '%';
                        
                        // 更新最近24小时调用次数图表
                        callsChart.data.labels = stats.time_labels;
                        callsChart.data.datasets[0].data = stats.call_counts;
                        callsChart.update();
                        
                        // 更新调用状态分布图表
                        statusChart.data.datasets[0].data = [
                            stats.success_count,
                            stats.error_count,
                            stats.timeout_count
                        ];
                        statusChart.update();
                        
                        // 更新令牌使用统计图表
                        if (stats.token_stats) {
                            tokenChart.data.datasets[0].data = [
                                stats.token_stats.input_tokens,
                                stats.token_stats.output_tokens
                            ];
                            tokenChart.update();
                            
                            // 更新费用统计
                            document.getElementById('todayCost').textContent = '¥' + stats.token_stats.today_cost.toFixed(2);
                            document.getElementById('weekCost').textContent = '¥' + stats.token_stats.week_cost.toFixed(2);
                            document.getElementById('monthCost').textContent = '¥' + stats.token_stats.month_cost.toFixed(2);
                        }
                    }
                })
                .catch(error => console.error('加载API统计数据出错:', error));
        }
        
        // 加载维度统计数据
        function loadDimensionStats() {
            fetch('/api/dimension-stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        
                        // 更新维度分析统计图表
                        dimensionChart.data.labels = stats.labels;
                        dimensionChart.data.datasets[0].data = stats.counts;
                        dimensionChart.update();
                    }
                })
                .catch(error => console.error('加载维度统计数据出错:', error));
        }
        
        // 加载API日志
        function loadApiLogs() {
            fetch('/api/api-logs')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const logs = data.logs;
                        const tableBody = document.getElementById('apiLogTable');
                        
                        // 清空表格
                        tableBody.innerHTML = '';
                        
                        // 添加日志行
                        logs.forEach(log => {
                            const row = document.createElement('tr');
                            
                            // 设置状态码样式
                            let statusClass = '';
                            if (log.status_code >= 200 && log.status_code < 300) {
                                statusClass = 'text-success';
                            } else if (log.status_code >= 400) {
                                statusClass = 'text-danger';
                            }
                            
                            row.innerHTML = `
                                <td>${new Date(log.timestamp).toLocaleString()}</td>
                                <td>${log.analysis_type || '-'}</td>
                                <td class="${statusClass}">${log.status_code || '-'}</td>
                                <td>${log.input_tokens || 0}</td>
                                <td>${log.output_tokens || 0}</td>
                                <td>${log.total_tokens || 0}</td>
                                <td>${log.total_cost ? log.total_cost.toFixed(4) : '0.0000'}</td>
                                <td>${log.response_time ? log.response_time.toFixed(0) : '-'}</td>
                            `;
                            
                            tableBody.appendChild(row);
                        });
                    }
                })
                .catch(error => console.error('加载API日志出错:', error));
        }
        
        // 保存API配置
        function saveApiConfig(event) {
            event.preventDefault();
            
            const formData = new FormData(document.getElementById('apiConfigForm'));
            const config = {
                dimension_chunk_sizes: {},
                dimension_max_tokens: {}
            };
            
            // 处理分块大小配置
            config.dimension_chunk_sizes.default = parseInt(formData.get('defaultChunkSize'));
            for (const [key, value] of formData.entries()) {
                if (key.startsWith('chunkSize_')) {
                    const dimension = key.replace('chunkSize_', '');
                    config.dimension_chunk_sizes[dimension] = parseInt(value);
                }
            }
            
            // 处理最大输出Token配置
            config.dimension_max_tokens.default = parseInt(formData.get('defaultMaxTokens'));
            for (const [key, value] of formData.entries()) {
                if (key.startsWith('maxTokens_')) {
                    const dimension = key.replace('maxTokens_', '');
                    config.dimension_max_tokens[dimension] = parseInt(value);
                }
            }
            
            // 发送配置到服务器
            fetch('/api/update-api-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('API配置已保存');
                    } else {
                        alert('保存API配置失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('保存API配置出错:', error);
                    alert('保存API配置出错');
                });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();
            
            // 加载数据
            loadApiStats();
            loadDimensionStats();
            loadApiLogs();
            
            // 刷新按钮点击事件
            document.getElementById('refreshBtn').addEventListener('click', function() {
                loadApiStats();
                loadDimensionStats();
                loadApiLogs();
            });
            
            // API配置表单提交事件
            document.getElementById('apiConfigForm').addEventListener('submit', saveApiConfig);
            
            // 定时刷新数据（每60秒）
            setInterval(function() {
                loadApiStats();
                loadApiLogs();
            }, 60000);
        });
    </script>
</body>
</html>
