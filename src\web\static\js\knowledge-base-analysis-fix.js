/**
 * 九猫小说分析写作系统 - 知识库分析结果显示修复脚本
 *
 * 此脚本用于修复知识库页面中分析结果不显示但推理过程正常显示的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[知识库分析结果显示修复] 初始化...');

    // 配置
    const CONFIG = {
        debug: true,
        selectors: {
            analysisContent: '.analysis-content',
            reasoningContent: '.reasoning-content',
            dimensionAccordion: '#analysisAccordion',
            dimensionItem: '[data-dimension]',
            readAnalysisBtn: '#readAnalysisBtn'
        },
        apiPaths: {
            novelAnalysis: '/api/novel/{novelId}/analysis/{dimension}',
            novelReasoning: '/api/novel/{novelId}/analysis/{dimension}/reasoning_content'
        }
    };

    // 状态
    const STATE = {
        novelId: null,
        isLoading: false,
        fixedDimensions: new Set()
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[知识库分析结果显示修复] ${message}`);
        }
    }

    // 初始化
    function init() {
        debugLog('开始初始化...');

        // 确保jQuery已加载
        if (typeof window.ensureJQuery !== 'function') {
            debugLog('ensureJQuery函数不存在，等待页面加载完成后再试', 'warn');

            // 等待页面加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', init);
                return;
            }

            // 尝试直接使用jQuery
            if (typeof jQuery !== 'undefined') {
                debugLog('jQuery已加载，继续初始化');
                initWithJQuery(jQuery);
            } else {
                debugLog('jQuery未加载，无法初始化', 'error');
                return;
            }
        } else {
            window.ensureJQuery(function($) {
                debugLog('jQuery已加载，继续初始化');
                initWithJQuery($);
            });
        }
    }

    // 使用jQuery初始化
    function initWithJQuery($) {
        debugLog('使用jQuery初始化');

        // 获取页面信息
        getPageInfo($);

        // 添加事件监听器
        addEventListeners($);

        // 修复分析结果显示
        fixAnalysisDisplay($);
    }

    // 获取页面信息
    function getPageInfo($) {
        debugLog('获取页面信息');

        // 尝试从全局变量获取小说ID
        if (window.selectedTemplateId) {
            STATE.novelId = window.selectedTemplateId;
            debugLog(`从全局selectedTemplateId获取小说ID: ${STATE.novelId}`);
        }

        // 尝试从URL获取信息
        if (!STATE.novelId) {
            const urlParams = new URLSearchParams(window.location.search);
            const novelIdFromUrl = urlParams.get('novel_id');

            if (novelIdFromUrl) {
                STATE.novelId = novelIdFromUrl;
                debugLog(`从URL获取小说ID: ${STATE.novelId}`);
            }
        }

        // 如果无法从URL获取，尝试从DOM元素获取
        if (!STATE.novelId) {
            const $novelElement = $('[data-novel-id]');
            if ($novelElement.length > 0) {
                STATE.novelId = $novelElement.data('novel-id');
                debugLog(`从DOM元素获取小说ID: ${STATE.novelId}`);
            }
        }

        // 如果仍然无法获取，尝试从localStorage获取
        if (!STATE.novelId && window.localStorage) {
            const storedNovelId = localStorage.getItem('selectedNovelId');
            if (storedNovelId) {
                STATE.novelId = storedNovelId;
                debugLog(`从localStorage获取小说ID: ${STATE.novelId}`);

                // 如果从localStorage获取到了小说ID，同时更新全局变量
                if (typeof window.selectedTemplateId !== 'undefined') {
                    window.selectedTemplateId = storedNovelId;
                    debugLog(`更新全局selectedTemplateId为: ${storedNovelId}`);
                }
            }
        }

        // 如果已经有分析数据，尝试从中获取小说ID
        if (!STATE.novelId && window.analysisData && window.analysisData.novel) {
            STATE.novelId = window.analysisData.novel.id;
            debugLog(`从全局analysisData获取小说ID: ${STATE.novelId}`);
        }

        // 如果获取到了小说ID，保存到localStorage
        if (STATE.novelId && window.localStorage) {
            localStorage.setItem('selectedNovelId', STATE.novelId);
            debugLog(`将小说ID保存到localStorage: ${STATE.novelId}`);
        }
    }

    // 添加事件监听器
    function addEventListeners($) {
        debugLog('添加事件监听器');

        // 监听读取分析结果按钮点击事件
        $(document).on('click', CONFIG.selectors.readAnalysisBtn, function() {
            debugLog('点击了读取分析结果按钮');

            // 延迟执行，确保分析结果已加载
            setTimeout(function() {
                fixAnalysisDisplay($);
            }, 2000);
        });

        // 监听维度项点击事件
        $(document).on('click', CONFIG.selectors.dimensionItem, function() {
            const dimension = $(this).data('dimension');
            debugLog(`点击了维度: ${dimension}`);

            if (dimension) {
                // 延迟执行，确保分析结果已加载
                setTimeout(function() {
                    fixDimensionAnalysisDisplay($, dimension);
                }, 1000);
            }
        });

        // 监听标签页切换事件
        $(document).on('shown.bs.tab', 'button[data-bs-toggle="tab"]', function(e) {
            const target = $(e.target).attr('data-bs-target');

            // 检查是否切换到了分析结果标签
            if (target && target.includes('result')) {
                const dimensionElement = $(e.target).closest(CONFIG.selectors.dimensionItem);
                if (dimensionElement.length > 0) {
                    const dimension = dimensionElement.data('dimension');
                    if (dimension) {
                        debugLog(`切换到了维度 ${dimension} 的分析结果标签`);
                        fixDimensionAnalysisDisplay($, dimension);
                    }
                }
            }
        });
    }

    // 修复分析结果显示
    function fixAnalysisDisplay($) {
        debugLog('修复分析结果显示');

        if (!STATE.novelId) {
            debugLog('缺少小说ID，无法修复分析结果显示', 'warn');
            return;
        }

        // 查找所有维度项
        const $dimensionItems = $(CONFIG.selectors.dimensionItem);

        if ($dimensionItems.length === 0) {
            debugLog('未找到维度项，可能尚未加载分析结果', 'warn');
            return;
        }

        debugLog(`找到 ${$dimensionItems.length} 个维度项`);

        // 遍历所有维度项
        $dimensionItems.each(function() {
            const $dimensionItem = $(this);
            const dimension = $dimensionItem.data('dimension');

            if (dimension) {
                // 检查分析结果内容
                const $analysisContent = $dimensionItem.find(CONFIG.selectors.analysisContent);

                if ($analysisContent.length > 0) {
                    const content = $analysisContent.html().trim();

                    // 如果分析结果为空或显示"暂无分析结果"
                    if (!content || content === '暂无分析结果' || content.includes('加载中')) {
                        debugLog(`维度 ${dimension} 的分析结果为空或显示"暂无分析结果"，尝试修复`);
                        fixDimensionAnalysisDisplay($, dimension);
                    } else {
                        debugLog(`维度 ${dimension} 的分析结果已有内容，无需修复`);
                    }
                }
            }
        });
    }

    // 修复特定维度的分析结果显示
    function fixDimensionAnalysisDisplay($, dimension) {
        debugLog(`修复维度 ${dimension} 的分析结果显示`);

        if (!STATE.novelId) {
            debugLog('缺少小说ID，无法修复分析结果显示', 'warn');
            return;
        }

        // 检查是否已修复过该维度
        if (STATE.fixedDimensions.has(dimension)) {
            debugLog(`维度 ${dimension} 已修复过，跳过`);
            return;
        }

        // 查找对应的分析内容元素
        const $dimensionItem = $(`[data-dimension="${dimension}"]`);

        if ($dimensionItem.length === 0) {
            debugLog(`未找到维度 ${dimension} 的元素`, 'warn');
            return;
        }

        const $analysisContent = $dimensionItem.find(CONFIG.selectors.analysisContent);

        if ($analysisContent.length === 0) {
            debugLog(`未找到维度 ${dimension} 的分析内容元素`, 'warn');
            return;
        }

        // 检查推理过程内容
        const $reasoningContent = $dimensionItem.find(CONFIG.selectors.reasoningContent);

        if ($reasoningContent.length > 0) {
            const reasoningContent = $reasoningContent.html().trim();

            // 如果推理过程有内容，但分析结果为空
            if (reasoningContent && reasoningContent !== '暂无推理过程' && reasoningContent !== '加载中...') {
                debugLog(`维度 ${dimension} 的推理过程有内容，但分析结果为空，尝试从推理过程中提取分析结果`);

                // 尝试从推理过程中提取分析结果
                const analysisResult = extractAnalysisResultFromReasoning(reasoningContent);

                if (analysisResult) {
                    debugLog(`成功从推理过程中提取维度 ${dimension} 的分析结果`);
                    $analysisContent.html(analysisResult);

                    // 标记为已修复
                    STATE.fixedDimensions.add(dimension);
                    return;
                }
            }
        }

        // 如果无法从推理过程中提取分析结果，尝试从API获取
        loadAnalysisResult($, dimension);
    }

    // 从推理过程中提取分析结果
    function extractAnalysisResultFromReasoning(reasoningContent) {
        debugLog('尝试从推理过程中提取分析结果');

        // 尝试查找"分析结果"部分
        const resultMatch = reasoningContent.match(/<h2>分析结果<\/h2>([\s\S]*?)(<h2>|$)/i);

        if (resultMatch && resultMatch[1]) {
            debugLog('找到分析结果部分');
            return resultMatch[1].trim();
        }

        // 尝试查找"最终分析"部分
        const finalMatch = reasoningContent.match(/<h2>最终分析<\/h2>([\s\S]*?)(<h2>|$)/i);

        if (finalMatch && finalMatch[1]) {
            debugLog('找到最终分析部分');
            return finalMatch[1].trim();
        }

        // 尝试查找"总结"部分
        const summaryMatch = reasoningContent.match(/<h2>总结<\/h2>([\s\S]*?)(<h2>|$)/i);

        if (summaryMatch && summaryMatch[1]) {
            debugLog('找到总结部分');
            return summaryMatch[1].trim();
        }

        // 尝试查找最后一个h2标签后的内容
        const lastH2Match = reasoningContent.match(/<h2>[^<]+<\/h2>([\s\S]*)$/i);

        if (lastH2Match && lastH2Match[1]) {
            debugLog('找到最后一个h2标签后的内容');
            return lastH2Match[1].trim();
        }

        debugLog('未能从推理过程中提取分析结果', 'warn');
        return null;
    }

    // 从API加载分析结果
    function loadAnalysisResult($, dimension) {
        debugLog(`从API加载维度 ${dimension} 的分析结果`);

        if (!STATE.novelId) {
            debugLog('缺少小说ID，无法加载分析结果', 'warn');
            return;
        }

        // 查找对应的分析内容元素
        const $dimensionItem = $(`[data-dimension="${dimension}"]`);
        const $analysisContent = $dimensionItem.find(CONFIG.selectors.analysisContent);

        if ($analysisContent.length === 0) {
            debugLog(`未找到维度 ${dimension} 的分析内容元素`, 'warn');
            return;
        }

        // 显示加载中
        $analysisContent.html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><p class="small mt-2">加载中...</p></div>');

        // 构建API URL
        const apiUrl = CONFIG.apiPaths.novelAnalysis
            .replace('{novelId}', STATE.novelId)
            .replace('{dimension}', dimension);

        // 添加时间戳防止缓存
        const url = `${apiUrl}?_=${Date.now()}`;

        // 发送请求
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                debugLog(`获取到维度 ${dimension} 的分析结果数据`);

                // 提取结果内容
                let content = '';
                if (response.result && response.result.content) {
                    content = response.result.content;
                } else if (response.content) {
                    content = response.content;
                }

                if (content) {
                    // 更新分析结果显示
                    $analysisContent.html(content);
                    debugLog(`成功更新维度 ${dimension} 的分析结果内容`);

                    // 标记为已修复
                    STATE.fixedDimensions.add(dimension);
                } else {
                    $analysisContent.html('暂无分析结果');
                    debugLog(`维度 ${dimension} 的分析结果内容为空`, 'warn');
                }
            },
            error: function(xhr) {
                debugLog(`加载维度 ${dimension} 的分析结果出错: ${xhr.status} ${xhr.statusText}`, 'error');
                $analysisContent.html(`<div class="alert alert-danger">加载失败: ${xhr.status} ${xhr.statusText}</div>`);
            }
        });
    }

    // 初始化
    init();

    console.log('[知识库分析结果显示修复] 初始化完成');
})();
