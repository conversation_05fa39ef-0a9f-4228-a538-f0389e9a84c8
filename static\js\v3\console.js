/**
 * 九猫系统控制台主脚本
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 */

// 确保jQuery已正确加载
(function() {
    // 检查jQuery是否已加载
    if (typeof jQuery === 'undefined') {
        console.error('[控制台] jQuery未加载，尝试使用ensureJQuery加载');
        if (typeof window.ensureJQuery === 'function') {
            window.ensureJQuery(function($) {
                console.log('[控制台] jQuery通过ensureJQuery加载成功');
                initConsoleWithJQuery($);
            });
        } else {
            console.error('[控制台] 无法加载jQuery，控制台功能将不可用');
            alert('系统错误：jQuery未加载，控制台功能将不可用。请刷新页面或联系管理员。');
        }
        return;
    }

    // 检查jQuery.fn.on方法是否存在
    if (typeof jQuery.fn.on !== 'function') {
        console.error('[控制台] jQuery.fn.on方法不存在，尝试修复');

        // 添加基本的on方法实现
        jQuery.fn.on = function(event, selector, data, callback) {
            // 处理不同的参数形式
            if (typeof selector === 'function') {
                callback = selector;
                selector = undefined;
                data = undefined;
            } else if (typeof data === 'function') {
                callback = data;
                data = undefined;
            }

            if (!callback) return this;

            return this.each(function() {
                const element = this;

                // 简单的事件委托实现
                if (selector) {
                    const originalCallback = callback;
                    callback = function(e) {
                        const target = e.target;
                        const matches = element.querySelectorAll(selector);
                        for (let i = 0; i < matches.length; i++) {
                            let current = target;
                            while (current && current !== element) {
                                if (current === matches[i]) {
                                    // 设置this为匹配的元素
                                    const originalThis = this;
                                    this.currentTarget = current;
                                    originalCallback.call(current, e);
                                    this.currentTarget = originalThis;
                                    return;
                                }
                                current = current.parentNode;
                            }
                        }
                    };
                }

                // 添加事件监听器
                element.addEventListener(event.split('.')[0], callback);
            });
        };

        console.log('[控制台] jQuery.fn.on方法已修复');
    }

    // 初始化控制台
    initConsoleWithJQuery(jQuery);
})();

/**
 * 使用jQuery初始化控制台
 * @param {Object} $ - jQuery对象
 */
function initConsoleWithJQuery($) {
    console.log('[控制台] 开始初始化控制台...');

    // 全局变量
    let currentTemplateId = null;
    let currentDimensionId = null;
    let currentChapterId = null;
    let presets = [];
    let currentPresetId = null;

    // DOM就绪后执行
    $(document).ready(function() {
        console.log('[控制台] DOM已就绪，初始化控制台功能');

        // 初始化各个功能模块
        initLogModule();
        initAnalysisModule();
        initKnowledgeBaseModule();
        initWritingModule();

        // 添加全局事件监听器
        setupGlobalEventListeners();

        console.log('[控制台] 控制台初始化完成');
    });

    /**
     * 初始化日志模块
     */
    function initLogModule() {
        console.log('[控制台] 初始化日志模块');

        // 清除日志按钮
        $('#clearLogsBtn').on('click', function() {
            $('#logContent').html('');
            addLogEntry('日志已清除', 'info');
        });

        // 刷新日志按钮
        $('#refreshLogsBtn').on('click', function() {
            refreshLogs();
        });

        // 日志搜索
        $('#logSearch').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            if (!searchTerm) {
                $('.log-line').show();
                return;
            }

            $('.log-line').each(function() {
                const logText = $(this).text().toLowerCase();
                $(this).toggle(logText.includes(searchTerm));
            });
        });

        // 清除搜索按钮
        $('#clearSearchBtn').on('click', function() {
            $('#logSearch').val('');
            $('.log-line').show();
        });

        // 添加欢迎日志
        addLogEntry('控制台日志模块初始化完成', 'info');
    }

    /**
     * 初始化分析模块
     */
    function initAnalysisModule() {
        console.log('[控制台] 初始化分析模块');

        // 选择模板按钮点击事件
        $('.select-template-btn').on('click', function() {
            const templateId = $(this).data('template-id');
            selectTemplate(templateId);
        });

        // 模板卡片点击事件
        $('.template-card').on('click', function() {
            const templateId = $(this).data('template-id');
            selectTemplate(templateId);
        });
    }

    /**
     * 初始化知识库模块
     */
    function initKnowledgeBaseModule() {
        console.log('[控制台] 初始化知识库模块');

        // 读取分析结果按钮
        $('#readAnalysisBtn').on('click', function() {
            if (!currentTemplateId) {
                showAlert('请先选择一个参考蓝本', 'warning');
                return;
            }

            loadKnowledgeBase(currentTemplateId);
        });

        // 转化为预设模板按钮
        $('#convertToTemplateBtn').on('click', function() {
            if (!currentTemplateId) {
                showAlert('请先选择一个参考蓝本', 'warning');
                return;
            }

            convertToPresetTemplate(currentTemplateId);
        });

        // 新建预设按钮
        $('#newPresetBtn').on('click', function() {
            clearPresetEditor();
            $('#presetEditorTitle').text('新建预设');
        });

        // 保存预设按钮
        $('#savePresetBtn').on('click', function() {
            savePreset();
        });

        // 删除预设按钮
        $('#deletePresetBtn').on('click', function() {
            if (!currentPresetId) {
                showAlert('请先选择一个预设', 'warning');
                return;
            }

            if (confirm('确定要删除这个预设吗？此操作不可撤销。')) {
                deletePreset(currentPresetId);
            }
        });

        // 读取预设按钮
        $('#readPresetBtn').on('click', function() {
            if (!currentPresetId) {
                showAlert('请先选择一个预设', 'warning');
                return;
            }

            loadPresetContent(currentPresetId);
        });

        // 展开查看按钮
        $('#expandPresetBtn').on('click', function() {
            const content = $('#presetContent').val();
            if (!content) {
                showAlert('预设内容为空', 'warning');
                return;
            }

            // 创建模态框显示内容
            const modalId = 'presetContentModal';
            const modal = $(`
                <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                    <div class="modal-dialog modal-lg modal-dialog-scrollable">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="${modalId}Label">预设内容</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body markdown-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            $('body').append(modal);

            // 显示模态框
            const modalElement = document.getElementById(modalId);
            const bsModal = new bootstrap.Modal(modalElement);
            bsModal.show();

            // 模态框关闭后移除
            modalElement.addEventListener('hidden.bs.modal', function() {
                $(this).remove();
            });
        });

        // 加载预设列表
        loadPresets();
    }

    /**
     * 初始化写作模块
     */
    function initWritingModule() {
        console.log('[控制台] 初始化写作模块');

        // 加载预设模板到下拉列表
        loadPresetsForWriting();

        // 开始写作按钮
        $('#startWritingBtn').on('click', function() {
            const templateId = $('#templateSelect').val();
            const prompt = $('#writingPrompt').val();

            if (!templateId) {
                showAlert('请选择一个预设模板', 'warning');
                return;
            }

            if (!prompt) {
                showAlert('请输入写作提示', 'warning');
                return;
            }

            startAutoWriting(templateId, prompt);
        });
    }

    /**
     * 设置全局事件监听器
     */
    function setupGlobalEventListeners() {
        // 监听标签页切换事件
        $('#consoleTab button').on('click', function(e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // 监听分析标签页切换事件
        $('#analysisTab button').on('click', function(e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // 监听分析结果标签页切换事件
        $('#analysisResultTab button').on('click', function(e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // 监听章节分析结果标签页切换事件
        $('#chapterAnalysisResultTab button').on('click', function(e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // 捕获浏览器控制台错误
        if (typeof window.addEventListener === 'function') {
            window.addEventListener('error', function(event) {
                const errorMsg = `JavaScript错误: ${event.message} 来源: ${event.filename} 行: ${event.lineno}`;
                addLogEntry(errorMsg, 'error');
                console.error('[控制台错误捕获]', errorMsg);
            });
        }
    }

    /**
     * 选择模板
     * @param {string} templateId - 模板ID
     */
    function selectTemplate(templateId) {
        console.log('[控制台] 选择模板:', templateId);
        currentTemplateId = templateId;

        // 显示加载中状态
        $('#dimensionList').html(`
            <div class="text-center py-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                <p class="small mt-2">加载中...</p>
            </div>
        `);

        // 显示分析内容区域
        $('#analysisDisplayCard').show();

        // 加载维度列表
        $.ajax({
            url: `/v3/api/templates/${templateId}/dimensions`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    renderDimensionList(response.dimensions);
                    addLogEntry(`成功加载模板 ${templateId} 的维度列表`, 'info');
                } else {
                    showAlert(`加载维度列表失败: ${response.message}`, 'danger');
                    addLogEntry(`加载维度列表失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                showAlert('加载维度列表时发生错误', 'danger');
                addLogEntry(`加载维度列表错误: ${error}`, 'error');
            }
        });

        // 加载章节列表
        $.ajax({
            url: `/v3/api/templates/${templateId}/chapters`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    renderChapterList(response.chapters);
                    addLogEntry(`成功加载模板 ${templateId} 的章节列表`, 'info');
                } else {
                    showAlert(`加载章节列表失败: ${response.message}`, 'danger');
                    addLogEntry(`加载章节列表失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                showAlert('加载章节列表时发生错误', 'danger');
                addLogEntry(`加载章节列表错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 渲染维度列表
     * @param {Array} dimensions - 维度列表
     */
    function renderDimensionList(dimensions) {
        if (!dimensions || dimensions.length === 0) {
            $('#dimensionList').html(`
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    没有找到分析维度
                </div>
            `);
            return;
        }

        let html = '';
        dimensions.forEach(function(dimension) {
            html += `
                <a href="#" class="list-group-item list-group-item-action dimension-item" data-dimension-id="${dimension.id}">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${dimension.name}</h6>
                        <small class="text-muted">${dimension.status || '已完成'}</small>
                    </div>
                    <small>${dimension.description || '无描述'}</small>
                </a>
            `;
        });

        $('#dimensionList').html(html);

        // 添加点击事件
        $('.dimension-item').on('click', function(e) {
            e.preventDefault();
            const dimensionId = $(this).data('dimension-id');
            loadDimensionAnalysis(dimensionId);

            // 高亮选中项
            $('.dimension-item').removeClass('active');
            $(this).addClass('active');
        });
    }

    /**
     * 渲染章节列表
     * @param {Array} chapters - 章节列表
     */
    function renderChapterList(chapters) {
        if (!chapters || chapters.length === 0) {
            $('#chapterList').html(`
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    没有找到章节
                </div>
            `);
            return;
        }

        let html = '';
        chapters.forEach(function(chapter) {
            html += `
                <a href="#" class="list-group-item list-group-item-action chapter-item" data-chapter-id="${chapter.id}">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">第${chapter.chapter_number}章: ${chapter.title}</h6>
                        <small class="text-muted">${chapter.status || '已完成'}</small>
                    </div>
                    <small>字数: ${chapter.word_count || '未知'}</small>
                </a>
            `;
        });

        $('#chapterList').html(html);

        // 添加点击事件
        $('.chapter-item').on('click', function(e) {
            e.preventDefault();
            const chapterId = $(this).data('chapter-id');
            loadChapterAnalysis(chapterId);

            // 高亮选中项
            $('.chapter-item').removeClass('active');
            $(this).addClass('active');
        });
    }

    /**
     * 加载维度分析
     * @param {string} dimensionId - 维度ID
     */
    function loadDimensionAnalysis(dimensionId) {
        console.log('[控制台] 加载维度分析:', dimensionId);
        currentDimensionId = dimensionId;

        // 显示加载中状态
        $('#analysisContent').html(`
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">加载分析结果中...</p>
            </div>
        `);

        $('#reasoningContent').html(`
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">加载推理过程中...</p>
            </div>
        `);

        // 加载分析结果
        $.ajax({
            url: `/v3/api/dimensions/${dimensionId}/analysis`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    $('#analysisContent').html(response.content || '暂无分析结果');
                    addLogEntry(`成功加载维度 ${dimensionId} 的分析结果`, 'info');
                } else {
                    $('#analysisContent').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载分析结果失败: ${response.message}
                        </div>
                    `);
                    addLogEntry(`加载分析结果失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#analysisContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        加载分析结果时发生错误
                    </div>
                `);
                addLogEntry(`加载分析结果错误: ${error}`, 'error');
            }
        });

        // 加载推理过程
        $.ajax({
            url: `/v3/api/dimensions/${dimensionId}/reasoning`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    $('#reasoningContent').html(response.content || '暂无推理过程');
                    addLogEntry(`成功加载维度 ${dimensionId} 的推理过程`, 'info');
                } else {
                    $('#reasoningContent').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载推理过程失败: ${response.message}
                        </div>
                    `);
                    addLogEntry(`加载推理过程失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#reasoningContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        加载推理过程时发生错误
                    </div>
                `);
                addLogEntry(`加载推理过程错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 加载章节分析
     * @param {string} chapterId - 章节ID
     */
    function loadChapterAnalysis(chapterId) {
        console.log('[控制台] 加载章节分析:', chapterId);
        currentChapterId = chapterId;

        // 显示加载中状态
        $('#chapterAnalysisContent').html(`
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">加载章节分析结果中...</p>
            </div>
        `);

        $('#chapterReasoningContent').html(`
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">加载章节推理过程中...</p>
            </div>
        `);

        // 加载章节分析结果
        $.ajax({
            url: `/v3/api/chapters/${chapterId}/analysis`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    $('#chapterAnalysisContent').html(response.content || '暂无章节分析结果');
                    addLogEntry(`成功加载章节 ${chapterId} 的分析结果`, 'info');
                } else {
                    $('#chapterAnalysisContent').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载章节分析结果失败: ${response.message}
                        </div>
                    `);
                    addLogEntry(`加载章节分析结果失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#chapterAnalysisContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        加载章节分析结果时发生错误
                    </div>
                `);
                addLogEntry(`加载章节分析结果错误: ${error}`, 'error');
            }
        });

        // 加载章节推理过程
        $.ajax({
            url: `/v3/api/chapters/${chapterId}/reasoning`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    $('#chapterReasoningContent').html(response.content || '暂无章节推理过程');
                    addLogEntry(`成功加载章节 ${chapterId} 的推理过程`, 'info');
                } else {
                    $('#chapterReasoningContent').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载章节推理过程失败: ${response.message}
                        </div>
                    `);
                    addLogEntry(`加载章节推理过程失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#chapterReasoningContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        加载章节推理过程时发生错误
                    </div>
                `);
                addLogEntry(`加载章节推理过程错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 加载知识库
     * @param {string} templateId - 模板ID
     */
    function loadKnowledgeBase(templateId) {
        console.log('[控制台] 加载知识库:', templateId);

        // 显示加载中状态
        $('#knowledgeBaseContent').html(`
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">加载知识库中...</p>
            </div>
        `);

        // 检查是否已加载知识库修复脚本
        if (typeof window.loadKnowledgeBaseWithFix === 'function') {
            console.log('[控制台] 使用知识库修复脚本加载知识库');
            window.loadKnowledgeBaseWithFix(templateId, function(success, content, error) {
                if (success) {
                    $('#knowledgeBaseContent').html(`
                        <div class="markdown-body">
                            ${content || '暂无知识库内容'}
                        </div>
                    `);
                    addLogEntry(`成功加载模板 ${templateId} 的知识库`, 'info');
                } else {
                    $('#knowledgeBaseContent').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载知识库失败: ${error || '未知错误'}
                        </div>
                    `);
                    addLogEntry(`加载知识库失败: ${error || '未知错误'}`, 'error');
                }
            });
            return;
        }

        // 如果没有加载修复脚本，则使用原始方法
        console.log('[控制台] 使用原始方法加载知识库');

        // 加载知识库
        $.ajax({
            url: `/v3/api/templates/${templateId}/knowledge_base`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    $('#knowledgeBaseContent').html(`
                        <div class="markdown-body">
                            ${response.content || '暂无知识库内容'}
                        </div>
                    `);
                    addLogEntry(`成功加载模板 ${templateId} 的知识库`, 'info');
                } else {
                    $('#knowledgeBaseContent').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载知识库失败: ${response.message}
                        </div>
                    `);
                    addLogEntry(`加载知识库失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#knowledgeBaseContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        加载知识库时发生错误
                    </div>
                `);
                addLogEntry(`加载知识库错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 转化为预设模板
     * @param {string} templateId - 模板ID
     */
    function convertToPresetTemplate(templateId) {
        console.log('[控制台] 转化为预设模板:', templateId);

        // 显示加载中状态
        showAlert('正在转化为预设模板，请稍候...', 'info');

        // 检查是否已加载知识库分析修复脚本
        if (typeof window.convertToPresetTemplateWithFix === 'function') {
            console.log('[控制台] 使用知识库分析修复脚本转化为预设模板');
            window.convertToPresetTemplateWithFix(templateId, function(success, preset, error) {
                if (success) {
                    showAlert('成功转化为预设模板', 'success');
                    addLogEntry(`成功将模板 ${templateId} 转化为预设模板`, 'info');

                    // 重新加载预设列表
                    loadPresets();

                    // 填充预设编辑器
                    $('#presetId').val(preset.id);
                    $('#presetTitle').val(preset.title || '');
                    $('#presetContent').val(preset.content || '');
                    $('#presetCategory').val(preset.category || 'knowledge_base');
                    $('#presetEditorTitle').text('编辑预设');
                    currentPresetId = preset.id;
                } else {
                    showAlert(`转化为预设模板失败: ${error || '未知错误'}`, 'danger');
                    addLogEntry(`转化为预设模板失败: ${error || '未知错误'}`, 'error');
                }
            });
            return;
        }

        // 如果没有加载修复脚本，则使用原始方法
        console.log('[控制台] 使用原始方法转化为预设模板');

        // 转化为预设模板
        $.ajax({
            url: `/v3/api/templates/${templateId}/convert_to_preset`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showAlert('成功转化为预设模板', 'success');
                    addLogEntry(`成功将模板 ${templateId} 转化为预设模板`, 'info');

                    // 重新加载预设列表
                    loadPresets();

                    // 填充预设编辑器
                    $('#presetId').val(response.preset_id);
                    $('#presetTitle').val(response.title || '');
                    $('#presetContent').val(response.content || '');
                    $('#presetCategory').val(response.category || 'knowledge_base');
                    $('#presetEditorTitle').text('编辑预设');
                    currentPresetId = response.preset_id;
                } else {
                    showAlert(`转化为预设模板失败: ${response.message}`, 'danger');
                    addLogEntry(`转化为预设模板失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                showAlert('转化为预设模板时发生错误', 'danger');
                addLogEntry(`转化为预设模板错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 加载预设列表
     */
    function loadPresets() {
        console.log('[控制台] 加载预设列表');

        // 显示加载中状态
        $('#presetList').html(`
            <div class="text-center py-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                <p class="small mt-2">加载中...</p>
            </div>
        `);

        // 加载预设列表
        $.ajax({
            url: '/v3/api/presets',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    presets = response.presets || [];
                    renderPresetList(presets);
                    addLogEntry('成功加载预设列表', 'info');
                } else {
                    $('#presetList').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载预设列表失败: ${response.message}
                        </div>
                    `);
                    addLogEntry(`加载预设列表失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#presetList').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        加载预设列表时发生错误
                    </div>
                `);
                addLogEntry(`加载预设列表错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 渲染预设列表
     * @param {Array} presets - 预设列表
     */
    function renderPresetList(presets) {
        if (!presets || presets.length === 0) {
            $('#presetList').html(`
                <div class="text-center py-3">
                    <small class="text-muted">暂无预设内容</small>
                </div>
            `);
            return;
        }

        let html = '';
        presets.forEach(function(preset) {
            html += `
                <a href="#" class="list-group-item list-group-item-action preset-item" data-preset-id="${preset.id}">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${preset.title}</h6>
                        <small class="text-muted">${preset.category || '未分类'}</small>
                    </div>
                    <small>${preset.description || '无描述'}</small>
                </a>
            `;
        });

        $('#presetList').html(html);

        // 添加点击事件
        $('.preset-item').on('click', function(e) {
            e.preventDefault();
            const presetId = $(this).data('preset-id');
            currentPresetId = presetId;

            // 高亮选中项
            $('.preset-item').removeClass('active');
            $(this).addClass('active');

            // 加载预设内容
            loadPresetContent(presetId);
        });
    }

    /**
     * 加载预设内容
     * @param {string} presetId - 预设ID
     */
    function loadPresetContent(presetId) {
        console.log('[控制台] 加载预设内容:', presetId);

        // 显示加载中状态
        $('#presetContent').val('加载中...');

        // 查找预设
        const preset = presets.find(p => p.id === presetId);
        if (preset) {
            $('#presetId').val(preset.id);
            $('#presetTitle').val(preset.title || '');
            $('#presetContent').val(preset.content || '');
            $('#presetCategory').val(preset.category || 'knowledge_base');
            $('#presetEditorTitle').text('编辑预设');
            addLogEntry(`成功加载预设 ${presetId} 的内容`, 'info');
        } else {
            // 如果在本地找不到，则从服务器加载
            $.ajax({
                url: `/v3/api/presets/${presetId}`,
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        $('#presetId').val(response.preset.id);
                        $('#presetTitle').val(response.preset.title || '');
                        $('#presetContent').val(response.preset.content || '');
                        $('#presetCategory').val(response.preset.category || 'knowledge_base');
                        $('#presetEditorTitle').text('编辑预设');
                        addLogEntry(`成功从服务器加载预设 ${presetId} 的内容`, 'info');
                    } else {
                        showAlert(`加载预设内容失败: ${response.message}`, 'danger');
                        addLogEntry(`加载预设内容失败: ${response.message}`, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    showAlert('加载预设内容时发生错误', 'danger');
                    addLogEntry(`加载预设内容错误: ${error}`, 'error');
                }
            });
        }
    }

    /**
     * 保存预设
     */
    function savePreset() {
        console.log('[控制台] 保存预设');

        const presetId = $('#presetId').val();
        const title = $('#presetTitle').val();
        const content = $('#presetContent').val();
        const category = $('#presetCategory').val();

        if (!title) {
            showAlert('请输入预设标题', 'warning');
            return;
        }

        if (!content) {
            showAlert('请输入预设内容', 'warning');
            return;
        }

        // 显示加载中状态
        showAlert('正在保存预设，请稍候...', 'info');

        // 构建请求数据
        const data = {
            title: title,
            content: content,
            category: category
        };

        // 确定请求方法和URL
        let method = 'POST';
        let url = '/v3/api/presets';

        if (presetId) {
            method = 'PUT';
            url = `/v3/api/presets/${presetId}`;
            data.id = presetId;
        }

        // 发送请求
        $.ajax({
            url: url,
            method: method,
            data: JSON.stringify(data),
            contentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    showAlert('预设保存成功', 'success');
                    addLogEntry(`成功保存预设 ${response.preset_id}`, 'info');

                    // 更新预设ID
                    $('#presetId').val(response.preset_id);
                    currentPresetId = response.preset_id;

                    // 重新加载预设列表
                    loadPresets();
                } else {
                    showAlert(`保存预设失败: ${response.message}`, 'danger');
                    addLogEntry(`保存预设失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                showAlert('保存预设时发生错误', 'danger');
                addLogEntry(`保存预设错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 删除预设
     * @param {string} presetId - 预设ID
     */
    function deletePreset(presetId) {
        console.log('[控制台] 删除预设:', presetId);

        // 显示加载中状态
        showAlert('正在删除预设，请稍候...', 'info');

        // 发送请求
        $.ajax({
            url: `/v3/api/presets/${presetId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showAlert('预设删除成功', 'success');
                    addLogEntry(`成功删除预设 ${presetId}`, 'info');

                    // 清空预设编辑器
                    clearPresetEditor();

                    // 重新加载预设列表
                    loadPresets();
                } else {
                    showAlert(`删除预设失败: ${response.message}`, 'danger');
                    addLogEntry(`删除预设失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                showAlert('删除预设时发生错误', 'danger');
                addLogEntry(`删除预设错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 清空预设编辑器
     */
    function clearPresetEditor() {
        $('#presetId').val('');
        $('#presetTitle').val('');
        $('#presetContent').val('');
        $('#presetCategory').val('knowledge_base');
        currentPresetId = null;
    }

    /**
     * 加载预设模板到下拉列表
     */
    function loadPresetsForWriting() {
        console.log('[控制台] 加载预设模板到下拉列表');

        // 显示加载中状态
        $('#templateSelect').html('<option value="">加载中...</option>');

        // 加载预设列表
        $.ajax({
            url: '/v3/api/presets',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const presets = response.presets || [];
                    let options = '<option value="">请选择预设模板</option>';

                    presets.forEach(function(preset) {
                        options += `<option value="${preset.id}">${preset.title}</option>`;
                    });

                    $('#templateSelect').html(options);
                    addLogEntry('成功加载预设模板列表', 'info');
                } else {
                    $('#templateSelect').html('<option value="">加载失败</option>');
                    addLogEntry(`加载预设模板列表失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#templateSelect').html('<option value="">加载错误</option>');
                addLogEntry(`加载预设模板列表错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 开始自动写作
     * @param {string} templateId - 模板ID
     * @param {string} prompt - 写作提示
     */
    function startAutoWriting(templateId, prompt) {
        console.log('[控制台] 开始自动写作:', templateId, prompt);

        // 显示加载中状态
        $('#writingResult').html(`
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">正在生成内容，请稍候...</p>
            </div>
        `);

        // 发送请求
        $.ajax({
            url: '/v3/api/writing/generate',
            method: 'POST',
            data: JSON.stringify({
                template_id: templateId,
                prompt: prompt
            }),
            contentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    $('#writingResult').html(`
                        <div class="markdown-body">
                            ${response.content || '生成内容为空'}
                        </div>
                    `);
                    addLogEntry('成功生成写作内容', 'info');
                } else {
                    $('#writingResult').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            生成写作内容失败: ${response.message}
                        </div>
                    `);
                    addLogEntry(`生成写作内容失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#writingResult').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        生成写作内容时发生错误
                    </div>
                `);
                addLogEntry(`生成写作内容错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 刷新日志
     */
    function refreshLogs() {
        console.log('[控制台] 刷新日志');

        // 显示加载中状态
        $('#logContent').prepend(`
            <div class="log-line log-info">
                <span class="log-time">${formatTime(new Date())}</span>
                <span class="log-message">正在刷新日志...</span>
            </div>
        `);

        // 加载日志
        $.ajax({
            url: '/v3/api/logs',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const logs = response.logs || [];
                    let html = '';

                    logs.forEach(function(log) {
                        html += `
                            <div class="log-line log-${log.level || 'info'}">
                                <span class="log-time">${formatTime(new Date(log.timestamp))}</span>
                                <span class="log-message">${log.message}</span>
                            </div>
                        `;
                    });

                    $('#logContent').html(html);
                    addLogEntry('成功刷新日志', 'info');
                } else {
                    addLogEntry(`刷新日志失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr, status, error) {
                addLogEntry(`刷新日志错误: ${error}`, 'error');
            }
        });
    }

    /**
     * 添加日志条目
     * @param {string} message - 日志消息
     * @param {string} level - 日志级别
     */
    function addLogEntry(message, level = 'info') {
        const logLine = `
            <div class="log-line log-${level}">
                <span class="log-time">${formatTime(new Date())}</span>
                <span class="log-message">${message}</span>
            </div>
        `;

        $('#logContent').prepend(logLine);
        console.log(`[控制台日志] [${level.toUpperCase()}] ${message}`);
    }

    /**
     * 格式化时间
     * @param {Date} date - 日期对象
     * @returns {string} 格式化后的时间字符串
     */
    function formatTime(date) {
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
    }

    /**
     * 显示提示信息
     * @param {string} message - 提示消息
     * @param {string} type - 提示类型
     */
    function showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // 如果页面上已有提示，则替换
        if ($('.alert').length > 0) {
            $('.alert').replaceWith(alertHtml);
        } else {
            // 否则在页面顶部添加
            $('#alertContainer').html(alertHtml);
        }
    }
}