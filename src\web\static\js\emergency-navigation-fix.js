/**
 * 九猫 - 紧急导航修复脚本
 * 解决所有导航问题，确保链接正确跳转
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._emergencyNavigationFixLoaded) {
        console.log('[紧急导航修复] 脚本已加载，避免重复执行');
        return;
    }

    // 标记脚本已加载
    window._emergencyNavigationFixLoaded = true;

    console.log('[紧急导航修复] 脚本已加载 - 版本1.0.0');

    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;

    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[紧急导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }

    function safeError(message) {
        try {
            originalConsoleError.call(console, '[紧急导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }

    // 获取当前小说ID
    function getCurrentNovelId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                safeLog('从URL获取到小说ID: ' + match[1]);
                return match[1];
            }

            // 从页面元素中获取
            const novelContainer = document.querySelector('[data-novel-id]');
            if (novelContainer && novelContainer.dataset.novelId) {
                safeLog('从data属性获取到小说ID: ' + novelContainer.dataset.novelId);
                return novelContainer.dataset.novelId;
            }

            // 从全局变量中获取
            if (window.novelId) {
                safeLog('从全局变量获取到小说ID: ' + window.novelId);
                return window.novelId;
            }

            if (window.novelIdFromTemplate) {
                safeLog('从模板变量获取到小说ID: ' + window.novelIdFromTemplate);
                return window.novelIdFromTemplate;
            }

            // 从页面内容中获取
            const novelIdElement = document.querySelector('.novel-id, #novel-id, [name="novel_id"]');
            if (novelIdElement) {
                if (novelIdElement.value) {
                    safeLog('从表单元素获取到小说ID: ' + novelIdElement.value);
                    return novelIdElement.value;
                }
                if (novelIdElement.textContent) {
                    const idMatch = novelIdElement.textContent.match(/\d+/);
                    if (idMatch) {
                        safeLog('从文本内容获取到小说ID: ' + idMatch[0]);
                        return idMatch[0];
                    }
                }
            }

            // 从面包屑导航中获取
            const breadcrumbLinks = document.querySelectorAll('.breadcrumb a[href*="/novel/"]');
            if (breadcrumbLinks.length > 0) {
                const lastLink = breadcrumbLinks[breadcrumbLinks.length - 1];
                const breadcrumbMatch = lastLink.href.match(/\/novel\/(\d+)/);
                if (breadcrumbMatch && breadcrumbMatch[1]) {
                    safeLog('从面包屑导航获取到小说ID: ' + breadcrumbMatch[1]);
                    return breadcrumbMatch[1];
                }
            }

            safeError('无法获取小说ID');
            return null;
        } catch (e) {
            safeError('获取小说ID时出错: ' + e.message);
            return null;
        }
    }

    // 修复所有链接
    function fixAllLinks() {
        safeLog('开始修复所有链接');

        // 获取当前小说ID
        const novelId = getCurrentNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法修复链接');
            return;
        }

        safeLog('当前小说ID: ' + novelId);

        // 修复所有链接
        const allLinks = document.querySelectorAll('a');
        safeLog('找到 ' + allLinks.length + ' 个链接');

        allLinks.forEach(link => {
            try {
                // 跳过已经修复过的链接
                if (link.__linkFixed) {
                    return;
                }

                // 标记链接已修复
                link.__linkFixed = true;

                // 获取链接的href属性
                const href = link.getAttribute('href');
                if (!href) {
                    return;
                }

                // 跳过外部链接和锚点链接
                if (href.startsWith('http') || href.startsWith('#') || href.startsWith('javascript:')) {
                    return;
                }

                // 保存原始链接
                link.setAttribute('data-original-href', href);

                // 修复章节分析链接
                if (href.includes('/chapter') || link.textContent.toLowerCase().includes('章节')) {
                    const newHref = '/novel/' + novelId + '/chapters';
                    link.href = newHref;
                    safeLog('修复章节分析链接: ' + href + ' -> ' + newHref);

                    // 设置点击事件
                    link.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        safeLog('点击章节分析链接，跳转到: ' + newHref);
                        window.location.href = newHref;
                        return false;
                    };
                }
                // 修复维度分析链接
                else if (href.includes('/analysis/') || href.includes('/dimension/')) {
                    // 提取维度名称
                    let dimension = '';
                    const dimensionMatch = href.match(/\/(analysis|dimension)\/([^\/]+)/);
                    if (dimensionMatch && dimensionMatch[2]) {
                        dimension = dimensionMatch[2];
                    } else {
                        // 从链接文本或类名中猜测维度
                        const text = link.textContent.trim().toLowerCase();
                        if (text.includes('语言风格')) dimension = 'language_style';
                        else if (text.includes('节奏')) dimension = 'rhythm_pacing';
                        else if (text.includes('结构')) dimension = 'structure';
                        // 添加更多维度匹配...
                        else {
                            // 从类名中获取
                            const classList = Array.from(link.classList);
                            const dimensionClass = classList.find(cls => cls.startsWith('dimension-') || cls.startsWith('analyze-'));
                            if (dimensionClass) {
                                dimension = dimensionClass.replace('dimension-', '').replace('analyze-', '');
                            }
                        }
                    }

                    if (dimension) {
                        const newHref = '/novel/' + novelId + '/analysis/' + dimension;
                        link.href = newHref;
                        safeLog('修复维度分析链接: ' + href + ' -> ' + newHref);

                        // 设置点击事件
                        link.onclick = function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            safeLog('点击维度分析链接，跳转到: ' + newHref);
                            window.location.href = newHref;
                            return false;
                        };
                    }
                }
                // 修复小说详情链接
                else if (href.match(/^\/novel\/\d+\/?$/)) {
                    const newHref = '/novel/' + novelId;
                    link.href = newHref;
                    safeLog('修复小说详情链接: ' + href + ' -> ' + newHref);

                    // 设置点击事件
                    link.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        safeLog('点击小说详情链接，跳转到: ' + newHref);
                        window.location.href = newHref;
                        return false;
                    };
                }
            } catch (e) {
                safeError('修复链接时出错: ' + e.message);
            }
        });

        // 修复所有按钮
        const allButtons = document.querySelectorAll('button, .btn');
        safeLog('找到 ' + allButtons.length + ' 个按钮');

        allButtons.forEach(button => {
            try {
                // 跳过已经修复过的按钮
                if (button.__buttonFixed) {
                    return;
                }

                // 标记按钮已修复
                button.__buttonFixed = true;

                // 获取按钮文本
                const text = button.textContent.trim().toLowerCase();

                // 修复章节分析按钮
                if (text.includes('章节') && (text.includes('分析') || text.includes('列表'))) {
                    const newHref = '/novel/' + novelId + '/chapters';
                    safeLog('修复章节分析按钮: ' + text);

                    // 如果是链接，设置href属性
                    if (button.tagName === 'A') {
                        button.href = newHref;
                    }

                    // 设置点击事件
                    button.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        safeLog('点击章节分析按钮，跳转到: ' + newHref);
                        window.location.href = newHref;
                        return false;
                    };
                }
                // 修复维度分析按钮
                else if (text.includes('分析') && !text.includes('章节') && !text.includes('重新')) {
                    // 从按钮文本或类名中猜测维度
                    let dimension = '';
                    if (text.includes('语言风格')) dimension = 'language_style';
                    else if (text.includes('节奏')) dimension = 'rhythm_pacing';
                    else if (text.includes('结构')) dimension = 'structure';
                    // 添加更多维度匹配...
                    else {
                        // 从类名中获取
                        const classList = Array.from(button.classList);
                        const dimensionClass = classList.find(cls => cls.startsWith('dimension-') || cls.startsWith('analyze-'));
                        if (dimensionClass) {
                            dimension = dimensionClass.replace('dimension-', '').replace('analyze-', '');
                        }

                        // 从data属性中获取
                        if (!dimension && button.dataset.dimension) {
                            dimension = button.dataset.dimension;
                        }
                    }

                    if (dimension) {
                        const newHref = '/novel/' + novelId + '/analysis/' + dimension;
                        safeLog('修复维度分析按钮: ' + text + ' -> ' + dimension);

                        // 如果是链接，设置href属性
                        if (button.tagName === 'A') {
                            button.href = newHref;
                        }

                        // 设置点击事件
                        button.onclick = function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            safeLog('点击维度分析按钮，跳转到: ' + newHref);
                            window.location.href = newHref;
                            return false;
                        };
                    }
                }
                // 修复重新分析按钮
                else if (text.includes('重新') && text.includes('分析')) {
                    const newHref = '/novel/' + novelId + '/reanalyze';
                    safeLog('修复重新分析按钮: ' + text);

                    // 如果是链接，设置href属性
                    if (button.tagName === 'A') {
                        button.href = newHref;
                    }

                    // 设置点击事件
                    button.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        safeLog('点击重新分析按钮，跳转到: ' + newHref);
                        window.location.href = newHref;
                        return false;
                    };
                }
            } catch (e) {
                safeError('修复按钮时出错: ' + e.message);
            }
        });

        safeLog('所有链接和按钮修复完成');
    }

    // 添加全局点击事件处理
    function addGlobalClickHandler() {
        safeLog('添加全局点击事件处理');

        // 获取当前小说ID
        const novelId = getCurrentNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法添加全局点击事件处理');
            return;
        }

        // 添加全局点击事件处理
        document.addEventListener('click', function(e) {
            // 向上查找最近的按钮或链接
            let target = e.target;
            while (target && target !== document) {
                // 检查是否是章节分析按钮
                if ((target.tagName === 'A' || target.tagName === 'BUTTON' || target.classList.contains('btn')) &&
                    target.textContent &&
                    target.textContent.toLowerCase().includes('章节') &&
                    (target.textContent.toLowerCase().includes('分析') || target.textContent.toLowerCase().includes('列表'))) {

                    // 阻止默认行为和事件冒泡
                    e.preventDefault();
                    e.stopPropagation();

                    // 记录点击
                    safeLog('通过全局处理器捕获章节分析按钮点击，跳转到: /novel/' + novelId + '/chapters');

                    // 使用setTimeout确保在所有事件处理完成后跳转
                    setTimeout(function() {
                        window.location.href = '/novel/' + novelId + '/chapters';
                    }, 0);

                    return false;
                }

                target = target.parentElement;
            }
        }, true);

        safeLog('全局点击事件处理已添加');
    }

    // 监听DOM变化，修复新添加的链接
    function observeDOMChanges() {
        try {
            // 创建MutationObserver
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // 检查是否有新添加的节点
                    if (mutation.addedNodes.length > 0) {
                        // 延迟执行修复，确保DOM完全更新
                        setTimeout(function() {
                            // 检查是否在首页
                            if (isHomePage()) {
                                fixHomePageLinks();
                            } else {
                                // 检查是否在小说详情页
                                const novelId = getCurrentNovelId();
                                if (novelId) {
                                    fixAllLinks();
                                }
                                // 其他页面不做特殊处理
                            }
                        }, 100);
                    }
                });
            });

            // 配置观察选项
            const config = { childList: true, subtree: true };

            // 开始观察
            observer.observe(document.body, config);

            safeLog('已开始监听DOM变化');
        } catch (e) {
            safeError('监听DOM变化时出错: ' + e.message);
        }
    }

    // 检查是否在首页
    function isHomePage() {
        const path = window.location.pathname;
        return path === '/' || path === '/index' || path === '/index.html' || path === '/home';
    }

    // 修复首页链接
    function fixHomePageLinks() {
        safeLog('开始修复首页链接');

        // 修复所有链接
        const allLinks = document.querySelectorAll('a');
        safeLog('找到 ' + allLinks.length + ' 个链接');

        allLinks.forEach(link => {
            try {
                // 跳过已经修复过的链接
                if (link.__linkFixed) {
                    return;
                }

                // 标记链接已修复
                link.__linkFixed = true;

                // 获取链接的href属性
                const href = link.getAttribute('href');
                if (!href) {
                    return;
                }

                // 跳过外部链接和锚点链接
                if (href.startsWith('http') || href.startsWith('#') || href.startsWith('javascript:')) {
                    return;
                }

                // 保存原始链接
                link.setAttribute('data-original-href', href);

                // 修复小说详情链接 - 这些在首页上是有效的
                if (href.match(/^\/novel\/\d+\/?$/)) {
                    // 提取小说ID
                    const novelIdMatch = href.match(/\/novel\/(\d+)/);
                    if (novelIdMatch && novelIdMatch[1]) {
                        const novelId = novelIdMatch[1];
                        const newHref = '/novel/' + novelId;

                        // 确保链接正确
                        link.href = newHref;

                        // 移除所有现有的点击事件处理函数
                        link.onclick = null;

                        // 设置新的点击事件处理函数
                        link.addEventListener('click', function(e) {
                            // 不阻止默认行为，让链接正常工作
                            safeLog('点击小说详情链接，跳转到: ' + newHref);
                        }, false);
                    }
                }
            } catch (e) {
                safeError('修复首页链接时出错: ' + e.message);
            }
        });

        safeLog('首页链接修复完成');
    }

    // 初始化函数
    function initialize() {
        safeLog('初始化紧急导航修复');

        // 检查是否在首页
        if (isHomePage()) {
            safeLog('检测到首页，只修复首页链接');
            fixHomePageLinks();
        } else {
            // 检查是否在小说详情页
            const novelId = getCurrentNovelId();
            if (novelId) {
                safeLog('检测到小说详情页，小说ID: ' + novelId);

                // 修复所有链接
                fixAllLinks();

                // 添加全局点击事件处理
                addGlobalClickHandler();
            } else {
                safeLog('不是小说详情页，也不是首页，只进行基本修复');
                // 可以在这里添加其他页面的修复逻辑
            }
        }

        // 监听DOM变化 - 对所有页面都有效
        observeDOMChanges();

        safeLog('紧急导航修复初始化完成');
    }

    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }

    // 导出API，方便调试和手动调用
    window.emergencyNavigationFix = {
        initialize: initialize,
        fixAllLinks: fixAllLinks
    };
})();
