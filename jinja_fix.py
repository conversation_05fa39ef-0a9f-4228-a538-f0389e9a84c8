# 修复Jinja2模板语法错误的脚本
import os

# 模板文件路径
template_file = "src/web/templates/new_novel.html"

try:
    # 读取文件内容
    with open(template_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 寻找连续的else标签并修复
    fixed = False
    for i in range(len(lines)):
        if '{% else %}' in lines[i]:
            # 在文件中向上查找最近的if标签
            for j in range(i-1, -1, -1):
                if '{% if completed_dimensions_count == total_dimensions_count %}' in lines[j]:
                    # 找到第一个else之后、第二个else之前的内容
                    for k in range(i+1, len(lines)):
                        if '{% else %}' in lines[k]:
                            # 这是要修复的第二个else
                            lines[k] = lines[k].replace('{% else %}', '{% elif completed_dimensions_count > 0 %}')
                            fixed = True
                            print(f"在第{k+1}行修复了语法错误：将第二个else替换为elif")
                            break
                    if fixed:
                        break
            if fixed:
                break

    # 如果找到并修复了错误，写回文件
    if fixed:
        with open(template_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        print(f"成功修复了模板文件 {template_file}")
    else:
        print(f"未找到语法错误或模板结构与预期不符")

except Exception as e:
    print(f"修复过程中出现错误: {str(e)}") 