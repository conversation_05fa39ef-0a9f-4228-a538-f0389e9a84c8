/**
 * 分析完成修复脚本
 *
 * 解决当13个维度全部分析完成时，分析结果不可见的问题
 */

(function() {
    // 安全日志函数
    function safeLog(message, level = 'info') {
        try {
            if (level === 'error') {
                console.error('[分析完成修复]', message);
            } else if (level === 'warn') {
                console.warn('[分析完成修复]', message);
            } else {
                console.log('[分析完成修复]', message);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }

    // 配置
    const CONFIG = {
        debug: true,
        retryDelay: 1000,
        maxRetries: 5,
        selectors: {
            analysisContent: '.analysis-content',
            previewContent: '.novel-preview',
            statusBadge: '.badge-analysis-status',
            dimensionCards: '.dimension-card',
            loadingIndicator: '.loading-indicator',
            analysisContainer: '.analysis-container'
        },
        dataAttributes: {
            novelId: 'data-novel-id',
            dimension: 'data-dimension',
            chapterId: 'data-chapter-id'
        }
    };

    // 状态
    const STATE = {
        novelId: null,
        isFixed: false,
        retryCount: 0,
        allDimensionsCompleted: false,
        loadingResults: false,
        resultCache: {}
    };

    // 获取小说ID
    function getNovelId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }

            // 从页面元素中获取
            const novelIdElement = document.querySelector(`[${CONFIG.dataAttributes.novelId}]`);
            if (novelIdElement) {
                return novelIdElement.getAttribute(CONFIG.dataAttributes.novelId);
            }

            return null;
        } catch (e) {
            safeLog(`获取小说ID时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 获取章节ID（如果在章节分析页面）
    function getChapterId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/chapter\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }

            // 从页面元素中获取
            const chapterIdElement = document.querySelector(`[${CONFIG.dataAttributes.chapterId}]`);
            if (chapterIdElement) {
                return chapterIdElement.getAttribute(CONFIG.dataAttributes.chapterId);
            }

            return null;
        } catch (e) {
            safeLog(`获取章节ID时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 获取当前维度
    function getCurrentDimension() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/analysis\/([^\/]+)/);
            if (match && match[1]) {
                return match[1];
            }

            // 从页面元素中获取
            const dimensionElement = document.querySelector(`[${CONFIG.dataAttributes.dimension}]`);
            if (dimensionElement) {
                return dimensionElement.getAttribute(CONFIG.dataAttributes.dimension);
            }

            // 从当前活动的标签页获取
            const activeTab = document.querySelector('.nav-link.active');
            if (activeTab && activeTab.getAttribute('href')) {
                const tabMatch = activeTab.getAttribute('href').match(/#([^-]+)/);
                if (tabMatch && tabMatch[1]) {
                    return tabMatch[1];
                }
            }

            return null;
        } catch (e) {
            safeLog(`获取当前维度时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 检查是否在章节分析页面
    function isChapterAnalysisPage() {
        return window.location.pathname.includes('/chapter/') && window.location.pathname.includes('/analysis/');
    }

    // 检查是否所有维度都已完成分析
    function checkAllDimensionsCompleted() {
        try {
            // 查找状态徽章
            const statusBadges = document.querySelectorAll(CONFIG.selectors.statusBadge);
            for (const badge of statusBadges) {
                // 检查是否包含"15/15"或"已完成"字样
                if (badge.textContent.includes('15/15') ||
                    badge.textContent.includes('15个维度已完成') ||
                    (badge.textContent.includes('分析已完成') && badge.classList.contains('bg-success'))) {
                    safeLog('检测到所有维度已完成分析');
                    return true;
                }

                // 尝试从文本中提取完成数量
                const match = badge.textContent.match(/(\d+)\/(\d+)/);
                if (match && match[1] === match[2] && parseInt(match[1]) > 0) {
                    safeLog(`检测到维度分析已完成: ${match[1]}/${match[2]}`);
                    return true;
                }
            }
            return false;
        } catch (e) {
            safeLog(`检查所有维度是否完成时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 检查分析内容是否为空
    function isContentEmpty() {
        try {
            const contentElement = document.querySelector(CONFIG.selectors.analysisContent);
            const previewElement = document.querySelector(CONFIG.selectors.previewContent);

            // 检查分析内容
            if (contentElement && contentElement.innerHTML.trim() === '') {
                return true;
            }

            // 检查预览内容
            if (previewElement && previewElement.innerHTML.trim() === '') {
                return true;
            }

            return false;
        } catch (e) {
            safeLog(`检查内容是否为空时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 修复分析结果显示
    function fixAnalysisResults() {
        try {
            // 防止重复修复
            if (STATE.loadingResults) {
                safeLog('已经在加载结果中，跳过');
                return;
            }

            STATE.loadingResults = true;
            safeLog('开始修复分析结果显示');

            // 显示加载指示器
            showLoadingIndicator();

            // 获取必要的信息
            const chapterId = getChapterId();
            const currentDimension = getCurrentDimension();
            const isChapterPage = isChapterAnalysisPage();

            // 获取预览元素
            const previewElement = document.querySelector(CONFIG.selectors.previewContent);

            safeLog(`修复信息: 小说ID=${STATE.novelId}, 章节ID=${chapterId}, 维度=${currentDimension}, 是否章节页=${isChapterPage}`);

            if (isChapterPage && chapterId && currentDimension) {
                // 章节分析页面 - 只获取当前维度的分析结果
                safeLog(`检测到章节分析页面，尝试获取章节分析结果，小说ID: ${STATE.novelId}, 章节ID: ${chapterId}, 维度: ${currentDimension}`);

                // 使用全局API函数（如果存在）
                if (window.fetchAnalysisResult) {
                    safeLog('使用全局fetchAnalysisResult函数获取分析结果');
                    window.fetchAnalysisResult(STATE.novelId, chapterId, currentDimension)
                        .then(result => {
                            if (result) {
                                safeLog('成功获取章节分析结果');

                                // 使用全局API函数更新分析结果
                                if (window.updateAnalysisResult) {
                                    safeLog('使用全局updateAnalysisResult函数更新分析结果');
                                    window.updateAnalysisResult(result);
                                } else {
                                    // 手动更新分析结果
                                    safeLog('找不到全局updateAnalysisResult函数，手动更新分析结果');
                                    const contentElement = document.querySelector(CONFIG.selectors.analysisContent);
                                    if (contentElement && result.content) {
                                        contentElement.innerHTML = result.content;
                                        safeLog('成功更新分析结果内容');
                                    }
                                }

                                STATE.isFixed = true;
                                STATE.loadingResults = false;
                                hideLoadingIndicator();
                                safeLog('章节分析结果显示修复完成');
                            } else {
                                safeLog('获取到的章节分析结果无效', 'warn');
                                STATE.loadingResults = false;
                                hideLoadingIndicator();
                                retryFix();
                            }
                        })
                        .catch(error => {
                            safeLog(`获取章节分析结果时出错: ${error.message}`, 'error');
                            STATE.loadingResults = false;
                            hideLoadingIndicator();
                            retryFix();
                        });
                } else {
                    // 直接使用fetch获取章节分析结果
                    safeLog('找不到全局fetchAnalysisResult函数，直接使用fetch');

                    fetch(`/api/novel/${STATE.novelId}/chapter/${chapterId}/analysis/${currentDimension}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data && data.success && data.result) {
                                safeLog('成功获取章节分析结果');

                                // 更新分析结果
                                const contentElement = document.querySelector(CONFIG.selectors.analysisContent);
                                if (contentElement && data.result.content) {
                                    contentElement.innerHTML = data.result.content;
                                    safeLog('成功更新分析结果内容');
                                }

                                STATE.isFixed = true;
                                STATE.loadingResults = false;
                                hideLoadingIndicator();
                                safeLog('章节分析结果显示修复完成');
                            } else {
                                safeLog('获取到的章节分析结果无效', 'warn');
                                STATE.loadingResults = false;
                                hideLoadingIndicator();
                                retryFix();
                            }
                        })
                        .catch(error => {
                            safeLog(`获取章节分析结果时出错: ${error.message}`, 'error');
                            STATE.loadingResults = false;
                            hideLoadingIndicator();
                            retryFix();
                        });
                }
            } else {
                // 小说分析页面 - 获取所有维度的分析结果
                safeLog(`尝试重新获取所有维度的分析结果，小说ID: ${STATE.novelId}`);

                // 如果在单个维度分析页面，只获取当前维度
                if (currentDimension && !isChapterPage) {
                    safeLog(`检测到单个维度分析页面，只获取当前维度: ${currentDimension}`);

                    // 强制重新获取分析结果，即使已完成所有维度
                    fetch(`/api/novel/${STATE.novelId}/analysis/${currentDimension}?force_refresh=true`)
                        .then(response => response.json())
                        .then(data => {
                            if (data && data.success && data.result) {
                                safeLog('成功获取单个维度分析结果');

                                // 更新分析结果
                                const contentElement = document.querySelector(CONFIG.selectors.analysisContent);
                                if (contentElement && data.result.content) {
                                    contentElement.innerHTML = data.result.content;
                                    safeLog('成功更新分析结果内容');
                                }

                                STATE.isFixed = true;
                                STATE.loadingResults = false;
                                hideLoadingIndicator();
                                safeLog('单个维度分析结果显示修复完成');
                            } else {
                                safeLog('获取到的单个维度分析结果无效', 'warn');
                                STATE.loadingResults = false;
                                hideLoadingIndicator();
                                retryFix();
                            }
                        })
                        .catch(error => {
                            safeLog(`获取单个维度分析结果时出错: ${error.message}`, 'error');
                            STATE.loadingResults = false;
                            hideLoadingIndicator();
                            retryFix();
                        });
                } else {
                    // 获取所有维度的分析结果
                    // 使用强制刷新参数，确保不使用缓存
                    fetch(`/api/novels/${STATE.novelId}/analysis?force_refresh=true`)
                        .then(response => response.json())
                        .then(results => {
                            if (Array.isArray(results) && results.length > 0) {
                                safeLog(`成功获取 ${results.length} 个维度的分析结果`);

                                // 更新全局变量
                                if (typeof window.analysisResultsData === 'undefined') {
                                    window.analysisResultsData = {};
                                }

                                results.forEach(result => {
                                    if (result && result.dimension) {
                                        window.analysisResultsData[result.dimension] = result;
                                        safeLog(`更新全局变量: 维度 ${result.dimension} 的数据已保存`);
                                    }
                                });

                                // 更新小说预览内容
                                if (previewElement && results.length > 0) {
                                    // 使用第一个结果作为预览
                                    const firstResult = results[0];
                                    if (firstResult && firstResult.content) {
                                        safeLog('更新小说预览内容');
                                        previewElement.innerHTML = firstResult.content;
                                    }
                                }

                                // 更新所有维度卡片
                                if (window.updateDimensionCard) {
                                    safeLog('使用updateDimensionCard函数更新所有维度卡片');
                                    results.forEach(result => {
                                        if (result && result.dimension) {
                                            window.updateDimensionCard(result.dimension, result);
                                        }
                                    });
                                } else {
                                    safeLog('找不到updateDimensionCard函数，尝试手动更新卡片', 'warn');
                                    updateDimensionCardsManually(results);
                                }

                                STATE.isFixed = true;
                                STATE.loadingResults = false;
                                hideLoadingIndicator();
                                safeLog('分析结果显示修复完成');
                            } else {
                                safeLog('未获取到任何分析结果', 'warn');
                                STATE.loadingResults = false;
                                hideLoadingIndicator();
                                retryFix();
                            }
                        })
                        .catch(error => {
                            safeLog(`获取分析结果时出错: ${error.message}`, 'error');
                            STATE.loadingResults = false;
                            hideLoadingIndicator();
                            retryFix();
                        });
                }
            }
        } catch (e) {
            safeLog(`修复分析结果显示时出错: ${e.message}`, 'error');
            STATE.loadingResults = false;
            hideLoadingIndicator();
            retryFix();
        }
    }

    // 手动更新维度卡片
    function updateDimensionCardsManually(results) {
        try {
            safeLog('手动更新维度卡片');

            // 获取所有维度卡片
            const cards = document.querySelectorAll(CONFIG.selectors.dimensionCards);
            safeLog(`找到 ${cards.length} 个维度卡片`);

            cards.forEach(card => {
                const dimension = card.getAttribute(CONFIG.dataAttributes.dimension);
                if (!dimension) return;

                // 查找对应的结果
                const result = results.find(r => r.dimension === dimension);
                if (!result) return;

                // 更新卡片内容
                const cardBody = card.querySelector('.card-body');
                if (!cardBody) return;

                // 提取摘要
                let excerpt = '分析已完成，点击"查看详情"查看完整结果。';
                if (result.content) {
                    // 简单提取前100个字符作为摘要
                    excerpt = result.content.substring(0, 100) + '...';
                }

                // 更新卡片内容
                cardBody.innerHTML = `
                    <div class="d-flex justify-content-between mb-2">
                        <span class="badge bg-success">分析完成</span>
                        <a href="/novel/${STATE.novelId}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                    </div>
                    <div class="analysis-excerpt mt-2">
                        ${excerpt}
                    </div>
                `;

                safeLog(`成功更新维度 ${dimension} 的卡片`);
            });
        } catch (e) {
            safeLog(`手动更新维度卡片时出错: ${e.message}`, 'error');
        }
    }

    // 显示加载指示器
    function showLoadingIndicator() {
        try {
            // 检查是否已存在加载指示器
            let loadingIndicator = document.querySelector(CONFIG.selectors.loadingIndicator);

            if (!loadingIndicator) {
                // 创建加载指示器
                loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'loading-indicator position-fixed';
                loadingIndicator.style.cssText = 'top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; background: rgba(255,255,255,0.8); padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.2);';

                loadingIndicator.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0">正在重新加载分析结果，请稍候...</p>
                    </div>
                `;

                document.body.appendChild(loadingIndicator);
            } else {
                // 显示已存在的加载指示器
                loadingIndicator.style.display = 'block';
            }
        } catch (e) {
            safeLog(`显示加载指示器时出错: ${e.message}`, 'error');
        }
    }

    // 隐藏加载指示器
    function hideLoadingIndicator() {
        try {
            const loadingIndicator = document.querySelector(CONFIG.selectors.loadingIndicator);
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        } catch (e) {
            safeLog(`隐藏加载指示器时出错: ${e.message}`, 'error');
        }
    }

    // 重试修复
    function retryFix() {
        try {
            if (STATE.retryCount >= CONFIG.maxRetries) {
                safeLog(`已达到最大重试次数 ${CONFIG.maxRetries}，停止重试`, 'warn');
                return;
            }

            STATE.retryCount++;
            const delay = CONFIG.retryDelay * STATE.retryCount;

            safeLog(`将在 ${delay}ms 后进行第 ${STATE.retryCount} 次重试`);

            setTimeout(fixAnalysisResults, delay);
        } catch (e) {
            safeLog(`重试修复时出错: ${e.message}`, 'error');
        }
    }

    // 初始化
    function init() {
        try {
            // 获取小说ID
            STATE.novelId = getNovelId();

            if (!STATE.novelId) {
                safeLog('无法获取小说ID，修复脚本将不会运行', 'warn');
                return;
            }

            safeLog(`九猫分析结果显示修复脚本已初始化，小说ID: ${STATE.novelId}`);

            // 检查是否需要修复
            setTimeout(checkAndFix, 1000);

            // 添加事件监听器，监听页面变化
            addEventListeners();
        } catch (e) {
            safeLog(`初始化时出错: ${e.message}`, 'error');
        }
    }

    // 添加事件监听器
    function addEventListeners() {
        try {
            // 监听DOM变化，检测分析状态变化
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' || mutation.type === 'attributes') {
                        // 检查是否有状态变化
                        const statusChanged = Array.from(mutation.target.classList || []).some(cls =>
                            cls.includes('badge') || cls.includes('status'));

                        if (statusChanged) {
                            safeLog('检测到状态变化，重新检查是否需要修复');
                            checkAndFix();
                        }
                    }
                });
            });

            // 观察整个文档
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'style', 'data-status']
            });

            // 监听页面加载完成事件
            window.addEventListener('load', function() {
                safeLog('页面加载完成，检查是否需要修复');
                checkAndFix();
            });

            // 监听自定义事件
            document.addEventListener('analysis-completed', function() {
                safeLog('接收到分析完成事件，检查是否需要修复');
                STATE.allDimensionsCompleted = true;
                checkAndFix();
            });
        } catch (e) {
            safeLog(`添加事件监听器时出错: ${e.message}`, 'error');
        }
    }

    // 检查并修复
    function checkAndFix() {
        try {
            // 检查是否已修复
            if (STATE.isFixed) {
                safeLog('分析结果显示已修复，跳过');
                return;
            }

            // 检查是否所有维度都已完成分析
            STATE.allDimensionsCompleted = checkAllDimensionsCompleted();

            // 检查分析内容是否为空
            const contentEmpty = isContentEmpty();

            safeLog(`检查状态: 所有维度已完成=${STATE.allDimensionsCompleted}, 内容为空=${contentEmpty}`);

            // 如果所有维度已完成但内容为空，或者显式设置了allDimensionsCompleted，则修复
            if ((STATE.allDimensionsCompleted && contentEmpty) || STATE.allDimensionsCompleted) {
                safeLog('检测到需要修复的情况，开始修复');
                fixAnalysisResults();
            } else if (contentEmpty) {
                // 如果内容为空但不是所有维度都完成，可能是单个维度的问题，也尝试修复
                safeLog('内容为空，尝试修复单个维度');
                fixAnalysisResults();
            } else {
                safeLog('不需要修复');
            }
        } catch (e) {
            safeLog(`检查和修复时出错: ${e.message}`, 'error');
        }
    }

    // 暴露全局API
    window.fixAnalysisResultsDisplay = {
        fix: fixAnalysisResults,
        checkAndFix: checkAndFix,
        isAllDimensionsCompleted: checkAllDimensionsCompleted
    };

    // 启动
    init();
})();
