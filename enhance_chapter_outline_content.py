"""
增强章纲分析内容

此脚本用于增强章纲分析内容，特别是内容重现部分，
确保它足够详细，以原文的风格和语气进行叙述。
"""

import os
import sys
import logging
import json
import re
import traceback
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('enhance_chapter_outline_content.log')
    ]
)
logger = logging.getLogger(__name__)

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的模块
try:
    from src.db.connection import Session
    from src.models.chapter_analysis_result import ChapterAnalysisResult
    from src.models.chapter import Chapter
    from src.models.novel import Novel
    from src.api.deepseek_client import DeepSeekClient
    import config
except ImportError as e:
    logger.error(f"导入模块时出错: {str(e)}")
    sys.exit(1)

def enhance_chapter_outline_content(chapter_id=None, force=False):
    """
    增强章纲分析内容

    Args:
        chapter_id: 章节ID，如果为None则处理所有章节
        force: 是否强制重新生成内容，即使已存在
    """
    session = Session()
    try:
        # 构建查询
        query = session.query(ChapterAnalysisResult).filter(
            ChapterAnalysisResult.dimension == "chapter_outline"
        )

        # 如果指定了章节ID，只处理该章节
        if chapter_id:
            query = query.filter(ChapterAnalysisResult.chapter_id == chapter_id)

        # 获取所有需要处理的分析结果
        results = query.all()
        logger.info(f"找到 {len(results)} 个章纲分析结果")

        # 创建API客户端
        client = DeepSeekClient(model=config.DEFAULT_MODEL)

        # 处理每个分析结果
        for result in results:
            try:
                # 获取章节和小说信息
                chapter = session.query(Chapter).filter(Chapter.id == result.chapter_id).first()
                novel = session.query(Novel).filter(Novel.id == result.novel_id).first()

                if not chapter or not novel:
                    logger.warning(f"找不到章节或小说: chapter_id={result.chapter_id}, novel_id={result.novel_id}")
                    continue

                logger.info(f"处理章纲分析结果: id={result.id}, chapter_id={result.chapter_id}")

                # 检查内容是否需要增强
                content = result.content or ""

                # 检查内容重现部分是否足够详细
                if not force and "## 主要内容" in content and len(content) > 5000:
                    # 提取主要内容部分
                    main_content_match = re.search(r'## 主要内容\s*\n(.*?)(?:\n##|\Z)', content, re.DOTALL)
                    if main_content_match:
                        main_content = main_content_match.group(1).strip()
                        # 如果主要内容部分足够详细，跳过
                        if len(main_content) > 3000 and not main_content.startswith("[") and not main_content.endswith("]"):
                            logger.info(f"内容重现部分已足够详细，跳过: length={len(main_content)}")
                            continue

                logger.info(f"增强章纲分析内容: chapter_id={result.chapter_id}")

                # 构建增强提示词
                prompt = f"""你是一位经验丰富的文学分析专家，擅长分析各类小说文本。请对以下小说《{novel.title}》的章节《{chapter.title or f'第{chapter.chapter_number}章'}》进行详细的章纲分析，特别是内容重现部分。

请按照以下要求进行分析：

## 主要内容
请全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。

内容不设字数限制，越详细越好。严格禁止简短概括或总结性描述。必须以生动、细致、具体的小说叙述方式重现章节内容，让读者仿佛亲历其境。必须包含以下所有要素，并对每个要素进行极其详尽的描述：

1. 详细的场景描述：
   - 以原文的风格描述每个场景的环境、氛围和背景，包括光线、声音、气味等感官细节
   - 生动描绘场景中的物品、布置和空间关系，提供具体的视觉画面
   - 细致刻画场景的时间特征（如白天/黑夜、季节、天气等）及其变化
   - 深入表现场景的情绪氛围和给人的整体感受，以及场景如何影响人物情绪

2. 完整的事件过程：
   - 按照发生顺序详细描述每个事件的起因、经过和结果，不遗漏任何重要细节
   - 以原文的风格描述事件中的具体动作和过程，包括动作的速度、力度和精确描述
   - 生动再现事件的节奏和紧张程度，以及情绪变化曲线
   - 深入分析事件对情节发展的推动作用和对人物的影响

3. 人物言行与心理：
   - 完整记录主要人物的对话内容，尽可能使用原文中的重要对话，保留对话的语气和特点
   - 以原文的风格详细描述人物的动作、表情和肢体语言，包括微小的表情变化和身体动作
   - 深入描述人物的心理活动、情感变化和内心冲突，展现人物的思想过程
   - 生动描述人物之间的互动和关系动态，包括潜在的情感变化和关系发展

必须使用生动的语言、丰富的形容词和具体的细节描述，避免抽象概括。必须包含原文中的关键对话和场景描写，保留原文的风格和语气。必须按照时间顺序或情节发展顺序进行叙述，确保叙述的连贯性和完整性。

章节内容：
{chapter.content}

请只输出"## 主要内容"部分，不要包含其他分析内容。
"""

                # 调用API生成增强内容
                logger.info(f"调用API生成增强内容: chapter_id={result.chapter_id}")
                enhanced_result = client.analyze_text(
                    text=prompt,
                    analysis_type="chapter_outline_enhanced",
                    novel_id=novel.id,
                    chapter_id=chapter.id,
                    max_tokens=20000  # 使用较大的max_tokens确保生成足够详细的内容
                )

                if enhanced_result and "content" in enhanced_result:
                    enhanced_content = enhanced_result["content"]
                    logger.info(f"成功生成增强内容: length={len(enhanced_content)}")

                    # 替换原内容中的主要内容部分
                    if "## 主要内容" in content:
                        # 提取原内容中的主要内容部分
                        new_content = re.sub(
                            r'(## 主要内容\s*\n).*?(?=\n##|\Z)',
                            r'\1' + enhanced_content.replace('## 主要内容', '').strip(),
                            content,
                            flags=re.DOTALL
                        )
                    else:
                        # 如果原内容中没有主要内容部分，添加到开头
                        new_content = f"## 主要内容\n{enhanced_content.replace('## 主要内容', '').strip()}\n\n{content}"

                    # 更新分析结果
                    result.content = new_content
                    session.commit()
                    logger.info(f"成功更新章纲分析内容")
                else:
                    logger.warning(f"生成增强内容失败: {enhanced_result.get('error', '未知错误')}")
            except Exception as e:
                logger.error(f"处理章纲分析结果时出错: {str(e)}")
                logger.error(traceback.format_exc())
                session.rollback()

        logger.info(f"完成增强章纲分析内容")
    except Exception as e:
        logger.error(f"增强章纲分析内容时出错: {str(e)}")
        logger.error(traceback.format_exc())
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="增强章纲分析内容")
    parser.add_argument("--chapter_id", type=int, help="章节ID，如果不指定则处理所有章节")
    parser.add_argument("--force", action="store_true", help="是否强制重新生成内容，即使已存在")
    args = parser.parse_args()

    # 执行增强
    enhance_chapter_outline_content(args.chapter_id, args.force)
