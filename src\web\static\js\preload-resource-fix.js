/**
 * 九猫 - 预加载资源修复脚本
 * 用于修复预加载资源相关问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('预加载资源修复脚本已加载 v1.0.0');
    
    // 配置
    const CONFIG = {
        enableDebug: false,           // 启用调试模式
        checkInterval: 1000,          // 检查间隔（毫秒）
        maxRetries: 3                 // 最大重试次数
    };
    
    // 初始化
    function initialize() {
        console.log('初始化预加载资源修复');
        
        // 监听DOM加载完成事件
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', fixPreloadResources);
        } else {
            // DOM已加载完成，直接修复
            fixPreloadResources();
        }
        
        // 定期检查预加载资源
        setInterval(checkPreloadResources, CONFIG.checkInterval);
    }
    
    // 修复预加载资源
    function fixPreloadResources() {
        console.log('修复预加载资源');
        
        // 获取所有预加载链接
        const preloads = document.querySelectorAll('link[rel="preload"]');
        
        // 处理每个预加载链接
        preloads.forEach(function(preload) {
            processPreload(preload);
        });
    }
    
    // 处理预加载链接
    function processPreload(preload) {
        // 获取预加载资源的URL和类型
        const href = preload.getAttribute('href');
        const as = preload.getAttribute('as');
        
        if (!href || !as) {
            console.warn('预加载链接缺少href或as属性:', preload);
            return;
        }
        
        // 根据资源类型创建相应的元素
        switch (as) {
            case 'style':
                createStyleLink(href);
                break;
            case 'script':
                createScriptElement(href);
                break;
            case 'font':
                createFontFaceRule(href, preload.getAttribute('crossorigin'));
                break;
            case 'image':
                preloadImage(href);
                break;
            default:
                console.log(`跳过未知类型的预加载资源: ${as}`);
        }
    }
    
    // 创建样式链接
    function createStyleLink(href) {
        // 检查是否已经有相同href的样式表
        const existingStyles = document.querySelectorAll('link[rel="stylesheet"]');
        let exists = false;
        
        existingStyles.forEach(function(style) {
            if (style.getAttribute('href') === href) {
                exists = true;
            }
        });
        
        // 如果不存在，创建一个
        if (!exists) {
            console.log('创建样式表链接:', href);
            
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            
            // 添加到head
            document.head.appendChild(link);
        }
    }
    
    // 创建脚本元素
    function createScriptElement(href) {
        // 检查是否已经有相同src的脚本
        const existingScripts = document.querySelectorAll('script');
        let exists = false;
        
        existingScripts.forEach(function(script) {
            if (script.getAttribute('src') === href) {
                exists = true;
            }
        });
        
        // 如果不存在，创建一个
        if (!exists) {
            console.log('创建脚本元素:', href);
            
            const script = document.createElement('script');
            script.src = href;
            
            // 添加到body
            document.body.appendChild(script);
        }
    }
    
    // 创建@font-face规则
    function createFontFaceRule(href, crossorigin) {
        console.log('创建字体规则:', href);
        
        // 创建样式元素
        const style = document.createElement('style');
        
        // 提取字体名称（简单方法，可能需要改进）
        const fontName = href.split('/').pop().split('.')[0];
        
        // 创建@font-face规则
        style.textContent = `
            @font-face {
                font-family: '${fontName}';
                src: url('${href}') format('woff2');
                font-weight: normal;
                font-style: normal;
            }
        `;
        
        // 添加到head
        document.head.appendChild(style);
    }
    
    // 预加载图片
    function preloadImage(href) {
        console.log('预加载图片:', href);
        
        const img = new Image();
        img.src = href;
    }
    
    // 检查预加载资源
    function checkPreloadResources() {
        // 获取所有预加载链接
        const preloads = document.querySelectorAll('link[rel="preload"]');
        
        // 检查是否有新的预加载链接
        preloads.forEach(function(preload) {
            // 检查是否已处理
            if (!preload.hasAttribute('data-processed')) {
                // 标记为已处理
                preload.setAttribute('data-processed', 'true');
                
                // 处理预加载链接
                processPreload(preload);
            }
        });
    }
    
    // 初始化
    initialize();
})();
