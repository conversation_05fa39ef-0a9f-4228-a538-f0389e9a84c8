/**
 * 九猫 - 首页修复脚本
 * 用于修复首页空白问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('首页修复脚本已初始化');
    
    // 配置
    const CONFIG = {
        enableDebug: true,           // 启用调试模式
        maxRetries: 3,               // 最大重试次数
        delayBetweenRetries: 500,    // 重试间隔(毫秒)
        safeMode: true,              // 安全模式
        checkInterval: 1000,         // 检查间隔(毫秒)
        maxCheckTime: 10000          // 最大检查时间(毫秒)
    };
    
    // 监听DOM加载完成事件
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM内容已加载，开始修复首页');
        
        // 延迟执行，确保所有脚本都已加载
        setTimeout(checkAndFixIndex, 500);
        
        // 再次检查，以防第一次检查失败
        setTimeout(checkAndFixIndex, 2000);
    });
    
    // 检查并修复首页
    function checkAndFixIndex() {
        console.log('检查首页内容');
        
        // 获取主容器
        const mainContainer = document.querySelector('.container');
        
        // 如果主容器不存在或为空，创建一个基本容器
        if (!mainContainer || mainContainer.children.length === 0) {
            console.warn('主容器不存在或为空，创建基本容器');
            
            // 如果主容器不存在，创建一个
            if (!mainContainer) {
                const container = document.createElement('div');
                container.className = 'container mt-4';
                
                // 添加基本内容
                container.innerHTML = createBasicContent();
                
                // 添加到页面
                document.body.appendChild(container);
            } else {
                // 如果主容器存在但为空，添加基本内容
                mainContainer.innerHTML = createBasicContent();
            }
            
            return;
        }
        
        // 检查主容器是否有可见内容
        const hasVisibleContent = Array.from(mainContainer.children).some(child => {
            // 检查元素是否可见
            const style = window.getComputedStyle(child);
            return style.display !== 'none' && style.visibility !== 'hidden' && child.offsetHeight > 0;
        });
        
        // 如果主容器没有可见内容，添加基本内容
        if (!hasVisibleContent) {
            console.warn('主容器没有可见内容，添加基本内容');
            mainContainer.innerHTML = createBasicContent();
        }
    }
    
    // 创建基本内容
    function createBasicContent() {
        return `
            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">九猫小说文本分析系统</h4>
                        </div>
                        <div class="card-body">
                            <p class="lead">欢迎使用九猫小说文本分析系统，这是一款专门为作家和编辑设计的文本分析工具。</p>
                            
                            <h5 class="mt-4">主要功能：</h5>
                            <ul>
                                <li>文本高潮节奏分析</li>
                                <li>人物关系网络可视化</li>
                                <li>情感曲线追踪</li>
                                <li>文体风格识别</li>
                                <li>情节结构分析</li>
                            </ul>
                            
                            <div class="alert alert-info mt-4">
                                <strong>提示：</strong> 点击上方导航栏中的"开始分析"按钮，上传您的小说文本文件或直接粘贴文本内容开始分析。
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">快速开始</h5>
                        </div>
                        <div class="card-body">
                            <a href="/" class="btn btn-success btn-lg btn-block mb-3">
                                进入数据中心
                            </a>
                            
                            <a href="/upload" class="btn btn-primary btn-lg btn-block mb-3">
                                开始新的分析
                            </a>
                            
                            <h6>最近的小说：</h6>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <a href="/novel/4">
                                        斗破苍穹
                                    </a>
                                    <small class="text-muted d-block">2023-08-10</small>
                                </li>
                                <li class="list-group-item">
                                    <a href="/novel/3">
                                        重生之都市修仙
                                    </a>
                                    <small class="text-muted d-block">2023-07-05</small>
                                </li>
                                <li class="list-group-item">
                                    <a href="/novel/2">
                                        我在末世种田
                                    </a>
                                    <small class="text-muted d-block">2023-06-20</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card shadow-sm mt-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">系统状态</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    系统版本
                                    <span class="badge bg-primary">1.0.0</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    分析引擎
                                    <span class="badge bg-success">已连接</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    数据库
                                    <span class="badge bg-info">已连接</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">系统优化</h5>
                        </div>
                        <div class="card-body">
                            <p>九猫小说分析系统已应用内存优化和堆栈溢出保护，确保稳定运行。</p>
                            <div id="optimization-status" class="alert alert-success">
                                系统正常运行中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 导出到全局命名空间
    window.IndexFix = {
        checkAndFixIndex: checkAndFixIndex,
        createBasicContent: createBasicContent
    };
    
    console.log('首页修复脚本设置完成');
})();
