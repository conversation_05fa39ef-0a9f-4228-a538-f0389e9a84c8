<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫 - 错误处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .test-button {
            padding: 8px 15px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background-color: #0069d9;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            min-height: 20px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
<script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';console.error('错误处理脚本加载失败，尝试备用路径')"></script>
    </head>
<body>
    <div class="container">
        <h1>九猫 - 错误处理测试</h1>

        <div class="test-section">
            <h2>测试1: 基本错误处理</h2>
            <p>测试全局错误处理函数是否正常工作。</p>
            <button class="test-button" onclick="testBasicError()">运行测试</button>
            <div id="basic-error-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>测试2: 控制台错误</h2>
            <p>测试 console.error 函数是否正常工作。</p>
            <button class="test-button" onclick="testConsoleError()">运行测试</button>
            <div id="console-error-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>测试3: 模拟资源加载错误</h2>
            <p>测试资源加载错误处理。</p>
            <button class="test-button" onclick="testResourceError()">运行测试</button>
            <div id="resource-error-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>测试4: 模拟API错误</h2>
            <p>测试API错误处理。</p>
            <button class="test-button" onclick="testApiError()">运行测试</button>
            <div id="api-error-result" class="result"></div>
        </div>
    </div>

    <!-- 安全的错误处理函数 -->
    <script>
        // 保存原始的console.error函数
        var originalConsoleError = console.error;

        // 记录测试结果
        function logResult(id, message, isError) {
            var resultElement = document.getElementById(id);
            if (resultElement) {
                resultElement.innerHTML = message;
                resultElement.className = isError ? 'result error' : 'result success';
            }
        }

        // 测试1: 基本错误处理
        function testBasicError() {
            try {
                // 故意制造一个错误
                var obj = null;
                obj.nonExistentMethod();
                logResult('basic-error-result', '测试失败: 错误应该被捕获', true);
            } catch (e) {
                logResult('basic-error-result', '测试成功: 错误被正确捕获 - ' + e.message, false);
            }
        }

        // 测试2: 控制台错误
        function testConsoleError() {
            try {
                // 替换console.error以便测试
                console.error = function() {
                    // 恢复原始函数
                    console.error = originalConsoleError;
                    logResult('console-error-result', '测试成功: console.error函数正常工作', false);
                };

                // 调用console.error
                console.error('测试错误消息');
            } catch (e) {
                console.error = originalConsoleError;
                logResult('console-error-result', '测试失败: console.error函数出错 - ' + e.message, true);
            }
        }

        // 测试3: 模拟资源加载错误
        function testResourceError() {
            try {
                // 创建一个不存在的脚本元素
                var script = document.createElement('script');
                script.src = 'non-existent-script.js';
                script.onerror = function() {
                    logResult('resource-error-result', '测试成功: 资源加载错误被正确处理', false);
                };

                // 设置超时，如果onerror没有触发
                setTimeout(function() {
                    if (document.getElementById('resource-error-result').innerHTML === '') {
                        logResult('resource-error-result', '测试失败: 资源加载错误未被处理', true);
                    }
                }, 2000);

                document.head.appendChild(script);
            } catch (e) {
                logResult('resource-error-result', '测试失败: 创建脚本元素时出错 - ' + e.message, true);
            }
        }

        // 测试4: 模拟API错误
        function testApiError() {
            try {
                // 模拟fetch API错误
                fetch('non-existent-api-endpoint')
                    .then(function(response) {
                        if (!response.ok) {
                            throw new Error('API响应错误: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(function(data) {
                        logResult('api-error-result', '测试失败: API错误应该被捕获', true);
                    })
                    .catch(function(error) {
                        logResult('api-error-result', '测试成功: API错误被正确捕获 - ' + error.message, false);
                    });
            } catch (e) {
                logResult('api-error-result', '测试失败: 调用fetch API时出错 - ' + e.message, true);
            }
        }

        // 全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
            return true; // 阻止默认错误处理
        };
    </script>
</body>
</html>
