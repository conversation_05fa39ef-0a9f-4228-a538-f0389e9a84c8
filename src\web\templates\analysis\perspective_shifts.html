{% extends "analysis.html" %}

{% block title %}视角转换分析 - {{ novel.title }}{% endblock %}

{% block additional_styles %}
<style>
    /* 为视角转换分析优化的样式 */
    .perspective-section {
        margin-bottom: 30px;
        border-radius: 5px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        background-color: #fff;
    }

    .perspective-section h3 {
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
        margin-bottom: 15px;
        color: #333;
    }

    .perspective-chart-container {
        height: 300px;
        margin: 20px 0;
    }

    /* 懒加载相关样式 */
    [data-section-loaded="false"] {
        min-height: 100px;
    }

    /* 添加性能优化样式 */
    .will-change-transform {
        will-change: transform;
    }

    /* 减少滚动重绘 */
    .scroll-content {
        backface-visibility: hidden;
    }

    /* 优化图片加载 */
    .lazy-image {
        opacity: 0;
        transition: opacity 0.3s;
    }

    .lazy-image.loaded {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="container scroll-content">
    <div class="row">
        <div class="col-12">
            <h1 class="mt-4 mb-4">视角转换分析 - {{ novel.title }}</h1>

            <!-- 摘要部分 -->
            <div class="perspective-section analysis-section">
                <h3>分析摘要</h3>
                <p>{{ result.summary|safe }}</p>
            </div>

            <!-- 主要分析结果 -->
            <div class="perspective-section analysis-section">
                <h3>视角转换模式</h3>
                <div class="perspective-chart-container" data-chart-type="doughnut" id="perspectiveTypeChart">
                    <canvas id="perspectiveTypeCanvas"></canvas>
                </div>
                <div class="mt-3">
                    {{ result.perspective_type_analysis|safe }}
                </div>
            </div>

            <!-- 视角一致性分析 -->
            <div class="perspective-section analysis-section">
                <h3>视角一致性</h3>
                <div class="perspective-chart-container" data-chart-type="line" id="consistencyChart">
                    <canvas id="consistencyCanvas"></canvas>
                </div>
                <div class="mt-3">
                    {{ result.consistency_analysis|safe }}
                </div>
            </div>

            <!-- 视角转换点分析 -->
            <div class="perspective-section analysis-section">
                <h3>视角转换点</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>位置</th>
                                <th>转换类型</th>
                                <th>前视角</th>
                                <th>后视角</th>
                                <th>效果评估</th>
                            </tr>
                        </thead>
                        <tbody id="perspectiveShiftsTable">
                            {% if result.perspective_shifts %}
                                {% for shift in result.perspective_shifts %}
                                <tr>
                                    <td>{{ shift.position }}</td>
                                    <td>{{ shift.type }}</td>
                                    <td>{{ shift.from_perspective }}</td>
                                    <td>{{ shift.to_perspective }}</td>
                                    <td>{{ shift.effectiveness }}</td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr><td colspan="5">未检测到明显的视角转换点</td></tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 视角转换建议 -->
            <div class="perspective-section analysis-section">
                <h3>改进建议</h3>
                <div class="card">
                    <div class="card-body">
                        {{ result.suggestions|safe }}
                    </div>
                </div>
            </div>

            <!-- 视角转换示例 -->
            <div class="perspective-section analysis-section">
                <h3>典型示例</h3>
                <div class="examples-container">
                    {% if result.examples %}
                        {% for example in result.examples %}
                        <div class="card mb-3">
                            <div class="card-header">示例 {{ loop.index }}</div>
                            <div class="card-body">
                                <blockquote class="blockquote">
                                    <p>{{ example.text|safe }}</p>
                                </blockquote>
                                <p class="card-text">{{ example.explanation|safe }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">未提供示例</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_scripts %}
<!-- 加载我们的优化脚本，让它优先于其他脚本 -->
<script src="{{ url_for('static', filename='js/analysis-page-optimizer.js') }}" data-critical="true"></script>

<!-- 加载进度信息修复脚本 -->
<script src="{{ url_for('static', filename='js/analysis-progress-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-progress-fix-enhanced.js';"></script>

<!-- 加载维度详情页面修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-detail-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-detail-fix-enhanced.js';"></script>

<!-- 加载视角转换页面专用修复脚本 -->
<script src="{{ url_for('static', filename='js/perspective-shifts-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/perspective-shifts-fix.js';"></script>

<!-- 然后加载其他必要的脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 注册图表初始化事件处理
    document.querySelectorAll('[data-chart-type]').forEach(function(container) {
        container.addEventListener('initialize-chart', function(event) {
            const chartType = this.getAttribute('data-chart-type');
            const canvasId = this.querySelector('canvas').id;

            // 初始化图表，使用懒加载
            initChart(canvasId, chartType);
        });
    });

    // 图表初始化函数
    function initChart(canvasId, chartType) {
        const ctx = document.getElementById(canvasId).getContext('2d');

        // 根据图表类型初始化不同的图表
        if (chartType === 'doughnut' && canvasId === 'perspectiveTypeCanvas') {
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: [
                        '第一人称',
                        '第三人称限制性',
                        '第三人称全知性',
                        '混合视角'
                    ],
                    datasets: [{
                        data: [
                            {{ result.first_person_percentage|default(25) }},
                            {{ result.third_person_limited_percentage|default(40) }},
                            {{ result.third_person_omniscient_percentage|default(20) }},
                            {{ result.mixed_perspective_percentage|default(15) }}
                        ],
                        backgroundColor: [
                            '#4e73df',
                            '#1cc88a',
                            '#36b9cc',
                            '#f6c23e'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: 60,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        } else if (chartType === 'line' && canvasId === 'consistencyCanvas') {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [
                        '开始', '1/4处', '1/2处', '3/4处', '结尾'
                    ],
                    datasets: [{
                        label: '视角一致性',
                        data: [
                            {{ result.consistency_start|default(90) }},
                            {{ result.consistency_quarter|default(85) }},
                            {{ result.consistency_half|default(75) }},
                            {{ result.consistency_three_quarters|default(80) }},
                            {{ result.consistency_end|default(85) }}
                        ],
                        borderColor: '#4e73df',
                        backgroundColor: 'rgba(78, 115, 223, 0.1)',
                        borderWidth: 2,
                        pointBackgroundColor: '#4e73df',
                        pointRadius: 3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            min: 50,
                            max: 100,
                            ticks: {
                                beginAtZero: false
                            },
                            grid: {
                                drawBorder: false
                            },
                            title: {
                                display: true,
                                text: '一致性 (%)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
    }
});
</script>
{% endblock %}