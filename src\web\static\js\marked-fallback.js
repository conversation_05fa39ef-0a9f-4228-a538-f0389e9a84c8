/**
 * Marked.js 备用实现
 * 当原始的 marked.min.js 加载失败时使用
 */

(function() {
    console.log('[Marked备用] 初始化备用Markdown渲染器');
    
    // 检查是否已经存在marked对象
    if (typeof window.marked !== 'undefined') {
        console.log('[Marked备用] Marked.js已存在，不需要备用实现');
        return;
    }
    
    // 创建一个简单的备用实现
    window.marked = {
        parse: function(text) {
            console.log('[Marked备用] 使用备用渲染器处理Markdown');
            
            if (!text) return '';
            
            // 非常简单的Markdown处理
            // 处理标题
            text = text.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
            text = text.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
            text = text.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
            text = text.replace(/^#### (.*?)$/gm, '<h4>$1</h4>');
            text = text.replace(/^##### (.*?)$/gm, '<h5>$1</h5>');
            
            // 处理粗体和斜体
            text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
            
            // 处理链接
            text = text.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');
            
            // 处理列表
            text = text.replace(/^- (.*?)$/gm, '<li>$1</li>');
            
            // 处理段落
            text = text.replace(/^(?!<[a-z])(.*?)$/gm, '<p>$1</p>');
            
            return text;
        },
        
        setOptions: function(options) {
            console.log('[Marked备用] 设置选项:', options);
            // 备用实现不需要设置选项
        }
    };
    
    console.log('[Marked备用] 备用Markdown渲染器已初始化');
})();
