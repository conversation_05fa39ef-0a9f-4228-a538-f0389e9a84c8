"""
九猫小说分析系统v2.0应用入口
"""
import os
import sys
import logging
import time
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_cors import CORS
from sqlalchemy.orm import Session
from sqlalchemy import func

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入配置
import config

# 导入数据库模型
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.api_log import ApiLog
from src.db.connection import Session, engine
from src.models.base import Base

# 导入API客户端
try:
    from src.api.deepseek_client import DeepSeekClient
    from src.api.analysis import NovelAnalyzer
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("无法导入DeepSeekClient或NovelAnalyzer，某些功能可能不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(config.LOG_DIR if hasattr(config, 'LOG_DIR') else 'logs', 'v2_app.log'))
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_CONTENT_LENGTH

# 启用CORS
CORS(app)

# 添加自定义模板过滤器
@app.template_filter('tojson_safe')
def tojson_safe(obj):
    """安全地将对象转换为JSON字符串，用于在JavaScript中使用"""
    import json
    from markupsafe import Markup

    # 处理SQLAlchemy的MetaData对象
    if hasattr(obj, '__class__') and obj.__class__.__name__ == 'MetaData':
        try:
            # 尝试将MetaData对象转换为字典
            if hasattr(obj, 'items') and callable(obj.items):
                obj = {k: v for k, v in obj.items()}
            else:
                obj = {}
        except Exception as e:
            logger.error(f"转换MetaData对象为字典时出错: {str(e)}")
            obj = {}

    # 处理其他不可序列化的对象
    def json_default(o):
        if hasattr(o, '__dict__'):
            return o.__dict__
        elif hasattr(o, 'items') and callable(o.items):
            return {k: v for k, v in o.items()}
        else:
            return str(o)

    try:
        # 使用更安全的JSON序列化方式
        json_str = json.dumps(obj, default=json_default, ensure_ascii=False)

        # 确保返回的是安全的标记，避免Flask自动转义
        return Markup(json_str)
    except Exception as e:
        logger.error(f"JSON序列化对象时出错: {str(e)}")
        # 返回一个空对象，避免前端解析错误
        return Markup("{}")

# 确保所有数据库表已创建
Base.metadata.create_all(engine)
logger.info("确认所有数据库表已创建")

# 运行数据库迁移
try:
    from src.db.migrations import run_migrations
    logger.info("开始执行数据库迁移...")
    run_migrations()
    logger.info("数据库迁移执行完成")
except Exception as e:
    logger.error(f"执行数据库迁移时出错: {str(e)}", exc_info=True)

# 导入路由
from src.web.routes.v2_routes import v2_bp
from src.web.routes.v2_api import v2_api_bp

# 导入章节推理过程修复路由蓝图
try:
    from src.web.routes.chapter_reasoning_fix import chapter_reasoning_fix_bp
    logger.info("成功导入章节推理过程修复路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入章节推理过程修复路由蓝图: {str(e)}")
    chapter_reasoning_fix_bp = None

# 注册蓝图
app.register_blueprint(v2_bp, url_prefix='')
app.register_blueprint(v2_api_bp, url_prefix='')

# 注册章节推理过程修复路由蓝图
if chapter_reasoning_fix_bp:
    app.register_blueprint(chapter_reasoning_fix_bp)
    logger.info("已注册章节推理过程修复路由蓝图")

# 主页路由
@app.route('/')
def index():
    return redirect(url_for('v2.index'))

# 小说列表页面重定向
@app.route('/novels')
def novels():
    return redirect(url_for('v2.novels'))

# 上传小说页面重定向
@app.route('/upload')
def upload_novel():
    return redirect(url_for('v2.upload_novel'))

# 查看小说详情页面重定向
@app.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    return redirect(url_for('v2.view_novel', novel_id=novel_id))

# 查看分析结果页面重定向
@app.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    return redirect(url_for('v2.analysis', novel_id=novel_id, dimension=dimension))

# 章节分析页面重定向
@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>')
def view_chapter(novel_id, chapter_id):
    return redirect(url_for('v2.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

# 章节分析结果页面重定向
@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def chapter_analysis(novel_id, chapter_id, dimension):
    return redirect(url_for('v2.chapter_analysis', novel_id=novel_id, chapter_id=chapter_id, dimension=dimension))

# 章节分析汇总页面重定向
@app.route('/novel/<int:novel_id>/chapters/summary')
def chapters_summary(novel_id):
    return redirect(url_for('v2.chapters_summary', novel_id=novel_id))

# 系统监控页面重定向
@app.route('/system_monitor')
def system_monitor():
    return redirect(url_for('v2.system_monitor'))

# API路由 - 获取整本书的分析推理过程
@app.route('/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content')
def api_get_reasoning_content(novel_id, dimension):
    """获取整本书的分析推理过程"""
    try:
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到整本书的分析结果: novel_id={novel_id}, dimension={dimension}")
                return jsonify({
                    'success': False,
                    'error': '未找到整本书的分析结果'
                }), 404

            # 获取推理过程
            reasoning_content = None

            # 首先尝试从reasoning_content字段获取
            if result.reasoning_content:
                reasoning_content = result.reasoning_content
                source = "reasoning_content字段"
                logger.info(f"从reasoning_content字段获取到整本书的推理过程内容，长度: {len(reasoning_content)}")

            # 如果没有，尝试从元数据中获取
            elif result.analysis_metadata and 'reasoning_content' in result.analysis_metadata:
                reasoning_content = result.analysis_metadata['reasoning_content']
                source = "analysis_metadata"
                logger.info(f"从analysis_metadata获取到整本书的推理过程内容，长度: {len(reasoning_content)}")

            if reasoning_content:
                return jsonify({
                    'success': True,
                    'reasoning_content': reasoning_content,
                    'source': source
                })
            else:
                logger.error(f"未找到整本书的推理过程内容: novel_id={novel_id}, dimension={dimension}")
                return jsonify({
                    'success': False,
                    'error': '未找到整本书的推理过程内容'
                }), 404
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取整本书的推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# API路由 - 获取章节分析推理过程 (已移至v2_api.py，此处保留为兼容性)
# @app.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content')
# def api_get_chapter_reasoning_content(novel_id, chapter_id, dimension):
#     """获取章节分析推理过程"""
#     try:
#         session = Session()
#         try:
#             # 获取章节分析结果
#             result = session.query(ChapterAnalysisResult).filter_by(
#                 novel_id=novel_id,
#                 chapter_id=chapter_id,
#                 dimension=dimension
#             ).first()
#
#             if not result:
#                 logger.error(f"未找到章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
#                 return jsonify({
#                     'success': False,
#                     'error': '未找到章节分析结果'
#                 }), 404
#
#             # 获取推理过程
#             reasoning_content = None
#
#             # 首先尝试从reasoning_content字段获取
#             if result.reasoning_content:
#                 reasoning_content = result.reasoning_content
#                 source = "reasoning_content字段"
#                 logger.info(f"从reasoning_content字段获取到推理过程内容，长度: {len(reasoning_content)}")
#
#             # 如果没有，尝试从元数据中获取
#             elif result.analysis_metadata and 'reasoning_content' in result.analysis_metadata:
#                 reasoning_content = result.analysis_metadata['reasoning_content']
#                 source = "analysis_metadata"
#                 logger.info(f"从analysis_metadata获取到推理过程内容，长度: {len(reasoning_content)}")
#
#             if reasoning_content:
#                 return jsonify({
#                     'success': True,
#                     'reasoning_content': reasoning_content,
#                     'source': source
#                 })
#             else:
#                 logger.error(f"未找到推理过程内容: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
#                 return jsonify({
#                     'success': False,
#                     'error': '未找到推理过程内容'
#                 }), 404
#         finally:
#             session.close()
#     except Exception as e:
#         logger.error(f"获取章节推理过程时出错: {str(e)}", exc_info=True)
#         return jsonify({
#             'success': False,
#             'error': str(e)
#         }), 500

# 错误处理
@app.errorhandler(404)
def page_not_found(e):
    return render_template('v2/error.html', error_code=404, error_message='页面未找到'), 404

@app.errorhandler(500)
def internal_server_error(e):
    return render_template('v2/error.html', error_code=500, error_message='服务器内部错误'), 500

if __name__ == '__main__':
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=config.DEBUG,
        threaded=True,
        use_reloader=False
    )
