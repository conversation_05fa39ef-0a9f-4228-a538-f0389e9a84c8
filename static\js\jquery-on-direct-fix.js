/**
 * jQuery .on() 方法直接修复脚本
 * 最高优先级加载，确保jQuery.fn.on方法可用
 */
(function() {
    console.log('[jQuery直接修复] 初始化...');

    // 等待jQuery加载完成
    function waitForjQuery(callback) {
        if (typeof jQuery !== 'undefined') {
            callback(jQuery);
        } else {
            setTimeout(function() {
                waitForjQuery(callback);
            }, 50);
        }
    }

    // 修复jQuery.fn.on方法
    function fixJQueryOn(jQuery) {
        console.log('[jQuery直接修复] 检查jQuery.fn.on方法');

        if (typeof jQuery.fn.on !== 'function') {
            console.log('[jQuery直接修复] jQuery.fn.on方法不存在，添加实现');

            jQuery.fn.on = function(event, selector, data, callback) {
                // 处理不同的参数形式
                if (typeof selector === 'function') {
                    callback = selector;
                    selector = undefined;
                    data = undefined;
                } else if (typeof data === 'function') {
                    callback = data;
                    data = undefined;
                }

                if (!callback) return this;

                return this.each(function() {
                    const element = this;
                    
                    // 处理事件委托
                    if (selector) {
                        const originalCallback = callback;
                        callback = function(e) {
                            const target = e.target;
                            const matches = element.querySelectorAll(selector);
                            for (let i = 0; i < matches.length; i++) {
                                let current = target;
                                while (current && current !== element) {
                                    if (current === matches[i]) {
                                        const event = e;
                                        event.currentTarget = current;
                                        event.delegateTarget = element;
                                        originalCallback.call(current, event);
                                        return;
                                    }
                                    current = current.parentNode;
                                }
                            }
                        };
                    }

                    // 处理Bootstrap特殊事件
                    if (event.includes('.bs.')) {
                        const baseEvent = event.split('.')[0];
                        element.addEventListener(baseEvent, callback);
                    } else {
                        // 处理多个事件
                        event.split(' ').forEach(function(evt) {
                            const baseEvt = evt.split('.')[0];
                            element.addEventListener(baseEvt, callback);
                        });
                    }
                });
            };

            // 添加off方法
            if (typeof jQuery.fn.off !== 'function') {
                jQuery.fn.off = function(event, selector, callback) {
                    if (typeof selector === 'function') {
                        callback = selector;
                        selector = undefined;
                    }

                    return this.each(function() {
                        if (event) {
                            event.split(' ').forEach((evt) => {
                                const baseEvt = evt.split('.')[0];
                                this.removeEventListener(baseEvt, callback);
                            });
                        }
                    });
                };
            }

            console.log('[jQuery直接修复] jQuery.fn.on方法已修复');
        } else {
            console.log('[jQuery直接修复] jQuery.fn.on方法已存在');
        }
    }

    // 导出全局修复函数
    window.fixJQueryOnMethodDirectly = function() {
        waitForjQuery(fixJQueryOn);
    };

    // 立即执行修复
    waitForjQuery(fixJQueryOn);

    console.log('[jQuery直接修复] 初始化完成');
})();