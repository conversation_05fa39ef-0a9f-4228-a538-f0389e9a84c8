/**
 * 九猫 - 错误修复助手
 * 用于修复各种JavaScript错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('错误修复助手已加载 v1.0.0');
    
    // 配置
    const CONFIG = {
        enableDebug: false,           // 启用调试模式
        safeMode: true,               // 安全模式
        fixUndefinedErrors: true,     // 修复未定义错误
        fixTypeErrors: true,          // 修复类型错误
        fixReferenceErrors: true      // 修复引用错误
    };
    
    // 初始化
    function initialize() {
        console.log('初始化错误修复助手');
        
        // 添加全局错误处理
        addGlobalErrorHandling();
        
        // 修复常见错误
        fixCommonErrors();
        
        // 修复DOM操作错误
        fixDOMOperationErrors();
        
        // 修复AJAX错误
        fixAJAXErrors();
    }
    
    // 添加全局错误处理
    function addGlobalErrorHandling() {
        // 已经有全局错误处理，不再重复添加
        if (window.onerror) {
            console.log('全局错误处理已存在，跳过添加');
            return;
        }
        
        // 添加全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('捕获到错误:', message);
            
            // 尝试修复错误
            if (tryFixError(message, source, lineno, colno, error)) {
                return true; // 阻止默认处理
            }
            
            return false; // 允许默认处理
        };
    }
    
    // 尝试修复错误
    function tryFixError(message, source, lineno, colno, error) {
        // 检查是否是未定义错误
        if (CONFIG.fixUndefinedErrors && message && message.includes('is not defined')) {
            return fixUndefinedError(message);
        }
        
        // 检查是否是类型错误
        if (CONFIG.fixTypeErrors && message && message.includes('TypeError')) {
            return fixTypeError(message, error);
        }
        
        // 检查是否是引用错误
        if (CONFIG.fixReferenceErrors && message && message.includes('ReferenceError')) {
            return fixReferenceError(message);
        }
        
        return false; // 无法修复
    }
    
    // 修复未定义错误
    function fixUndefinedError(message) {
        // 提取未定义的变量名
        const match = message.match(/([a-zA-Z0-9_$]+) is not defined/);
        if (!match) return false;
        
        const varName = match[1];
        console.log(`尝试修复未定义错误: ${varName}`);
        
        // 创建空对象或函数
        if (varName.endsWith('Chart') || varName.includes('create') || varName.includes('init')) {
            // 可能是函数
            window[varName] = function() {
                console.warn(`调用了自动修复的空函数: ${varName}`);
                return null;
            };
        } else {
            // 可能是对象
            window[varName] = {};
        }
        
        console.log(`已创建空的 ${varName}`);
        return true;
    }
    
    // 修复类型错误
    function fixTypeError(message, error) {
        console.log(`尝试修复类型错误: ${message}`);
        
        // 检查是否是"Cannot read property"错误
        if (message.includes('Cannot read property') || message.includes('Cannot read properties')) {
            // 提取属性路径
            const match = message.match(/property '([^']+)' of (undefined|null)/);
            if (match) {
                const prop = match[1];
                console.log(`检测到无法读取属性: ${prop}`);
                
                // 尝试修复常见对象
                if (prop === 'defaults' && !window.Chart) {
                    window.Chart = { defaults: {} };
                    return true;
                }
                
                if (prop === 'font' && window.Chart && !window.Chart.defaults.font) {
                    window.Chart.defaults.font = {};
                    return true;
                }
            }
        }
        
        return false;
    }
    
    // 修复引用错误
    function fixReferenceError(message) {
        console.log(`尝试修复引用错误: ${message}`);
        
        // 提取引用的变量名
        const match = message.match(/ReferenceError: ([a-zA-Z0-9_$]+) is not defined/);
        if (!match) return false;
        
        const varName = match[1];
        
        // 创建空对象或函数
        if (varName.endsWith('Chart') || varName.includes('create') || varName.includes('init')) {
            // 可能是函数
            window[varName] = function() {
                console.warn(`调用了自动修复的空函数: ${varName}`);
                return null;
            };
        } else {
            // 可能是对象
            window[varName] = {};
        }
        
        console.log(`已创建空的 ${varName}`);
        return true;
    }
    
    // 修复常见错误
    function fixCommonErrors() {
        // 确保jQuery存在
        if (typeof jQuery === 'undefined') {
            console.log('jQuery未定义，创建模拟对象');
            window.jQuery = window.$ = function() {
                return {
                    ready: function(fn) { 
                        if (document.readyState !== 'loading') {
                            fn();
                        } else {
                            document.addEventListener('DOMContentLoaded', fn);
                        }
                        return this;
                    },
                    on: function() { return this; },
                    off: function() { return this; },
                    click: function() { return this; },
                    each: function() { return this; },
                    ajax: function() { return this; }
                };
            };
        }
        
        // 确保Chart存在
        if (typeof Chart === 'undefined') {
            console.log('Chart未定义，创建模拟对象');
            window.Chart = {
                defaults: {
                    font: { size: 12 },
                    animation: false,
                    responsive: true,
                    maintainAspectRatio: false,
                    elements: {
                        point: { radius: 2, hoverRadius: 3 },
                        line: { borderWidth: 1 }
                    },
                    plugins: {
                        legend: { display: false },
                        tooltip: { enabled: true, mode: 'index', intersect: false }
                    }
                },
                getChart: function() { return null; }
            };
        }
    }
    
    // 修复DOM操作错误
    function fixDOMOperationErrors() {
        // 保存原始的appendChild方法
        const originalAppendChild = Node.prototype.appendChild;
        
        // 重写appendChild方法
        Node.prototype.appendChild = function(child) {
            try {
                return originalAppendChild.call(this, child);
            } catch (e) {
                console.error(`appendChild错误: ${e.message}`);
                return child; // 返回子节点，假装成功
            }
        };
        
        // 保存原始的insertBefore方法
        const originalInsertBefore = Node.prototype.insertBefore;
        
        // 重写insertBefore方法
        Node.prototype.insertBefore = function(newNode, referenceNode) {
            try {
                return originalInsertBefore.call(this, newNode, referenceNode);
            } catch (e) {
                console.error(`insertBefore错误: ${e.message}`);
                return newNode; // 返回新节点，假装成功
            }
        };
    }
    
    // 修复AJAX错误
    function fixAJAXErrors() {
        // 如果jQuery存在，修复AJAX错误
        if (window.jQuery) {
            // 保存原始的ajax方法
            const originalAjax = jQuery.ajax;
            
            // 重写ajax方法
            jQuery.ajax = function(url, options) {
                // 规范化参数
                if (typeof url === 'object') {
                    options = url;
                    url = options.url;
                } else if (typeof options === 'undefined') {
                    options = {};
                }
                
                // 添加错误处理
                const originalError = options.error;
                options.error = function(jqXHR, textStatus, errorThrown) {
                    console.warn(`AJAX错误: ${textStatus} - ${errorThrown}`);
                    
                    // 调用原始错误处理
                    if (originalError) {
                        originalError(jqXHR, textStatus, errorThrown);
                    }
                };
                
                // 调用原始ajax方法
                return originalAjax.call(jQuery, options);
            };
        }
    }
    
    // 初始化
    initialize();
})();
