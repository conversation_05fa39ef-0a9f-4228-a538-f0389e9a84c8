{"name": "@npmcli/metavuln-calculator", "version": "3.1.0", "main": "lib/index.js", "files": ["bin/", "lib/"], "description": "Calculate meta-vulnerabilities from package security advisories", "repository": {"type": "git", "url": "https://github.com/npm/metavuln-calculator.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "posttest": "npm run lint", "snap": "tap", "postsnap": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.0", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "dependencies": {"cacache": "^16.0.0", "json-parse-even-better-errors": "^2.3.1", "pacote": "^13.0.3", "semver": "^7.3.5"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.0"}}