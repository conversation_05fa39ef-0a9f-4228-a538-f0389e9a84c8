<!DOCTYPE html>
<html>
<head>
    <title>Favicon Handler</title>
    <script>
        // 这个脚本会生成一个简单的favicon并将其设置为当前页面的图标
        // 当浏览器请求favicon.ico时，它会被重定向到这个页面，然后这个页面会提供一个动态生成的favicon

        // 创建一个canvas元素
        const canvas = document.createElement('canvas');
        canvas.width = 32;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');

        // 设置背景色为深蓝色
        ctx.fillStyle = '#19376D';
        ctx.fillRect(0, 0, 32, 32);

        // 绘制文字"九"
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 24px Arial, "Microsoft YaHei", SimHei, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('九', 16, 16);

        // 将canvas转换为favicon
        const link = document.createElement('link');
        link.rel = 'icon';
        link.href = canvas.toDataURL('image/png');
        document.head.appendChild(link);

        // 如果这个页面被直接访问，显示一条消息
        window.onload = function() {
            document.body.innerHTML = '<h1>这是一个动态生成的favicon</h1><p>这个页面用于处理favicon.ico请求，通常不应该被直接访问。</p>';

            // 尝试自动创建favicon.ico文件
            try {
                // 创建一个下载链接
                const downloadLink = document.createElement('a');
                downloadLink.href = canvas.toDataURL('image/png');
                downloadLink.download = 'favicon.ico';
                downloadLink.innerHTML = '下载favicon.ico文件';
                downloadLink.style.display = 'block';
                downloadLink.style.marginTop = '20px';
                downloadLink.style.padding = '10px';
                downloadLink.style.backgroundColor = '#19376D';
                downloadLink.style.color = 'white';
                downloadLink.style.textDecoration = 'none';
                downloadLink.style.textAlign = 'center';
                downloadLink.style.borderRadius = '5px';
                downloadLink.style.width = '200px';

                // 添加说明
                const instructions = document.createElement('p');
                instructions.innerHTML = '点击上面的按钮下载favicon.ico文件，然后将其放置在src/web/static目录中。';

                // 添加到页面
                document.body.appendChild(downloadLink);
                document.body.appendChild(instructions);

                // 添加自动下载功能
                const autoDownloadButton = document.createElement('button');
                autoDownloadButton.innerHTML = '自动下载并保存';
                autoDownloadButton.style.display = 'block';
                autoDownloadButton.style.marginTop = '20px';
                autoDownloadButton.style.padding = '10px';
                autoDownloadButton.style.backgroundColor = '#4CAF50';
                autoDownloadButton.style.color = 'white';
                autoDownloadButton.style.border = 'none';
                autoDownloadButton.style.borderRadius = '5px';
                autoDownloadButton.style.width = '200px';
                autoDownloadButton.style.cursor = 'pointer';

                autoDownloadButton.onclick = function() {
                    // 自动点击下载链接
                    downloadLink.click();

                    // 显示成功消息
                    const successMessage = document.createElement('p');
                    successMessage.innerHTML = '下载已开始！请将下载的文件放置在src/web/static目录中。';
                    successMessage.style.color = 'green';
                    document.body.appendChild(successMessage);
                };

                document.body.appendChild(autoDownloadButton);
            } catch (e) {
                console.error('创建下载链接时出错:', e);
                const errorMessage = document.createElement('p');
                errorMessage.innerHTML = '创建下载链接时出错: ' + e.message;
                errorMessage.style.color = 'red';
                document.body.appendChild(errorMessage);
            }
        };
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #19376D;
        }
        p {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <noscript>
        <h1>需要启用JavaScript</h1>
        <p>这个页面需要JavaScript来生成favicon。</p>
    </noscript>
</body>
</html>
