"""
Client for interacting with the Alibaba Cloud AI models API.
Supports multiple models including DeepSeek R1 and 通义千问-Plus-Latest.
"""
import json
import requests
import logging
import time
import hashlib
import re
import traceback
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

# 使用相对导入避免循环导入问题
import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import config
from src.db.connection import Session
from src.models.api_log import ApiLog
from src.models.intermediate_result import IntermediateResult

logger = logging.getLogger(__name__)

# 全局客户端实例，用于缓存
_client_instance = None

class DeepSeekClient:
    """Client for interacting with the Alibaba Cloud AI models API."""

    # 类级别变量，用于跟踪API调用
    _api_calls = []
    _api_call_limit = 10000  # 设置为较大值，实际上不限制
    _api_call_window = 3600  # 时间窗口（秒）

    # 类级别变量，用于跟踪API费用
    _total_tokens_used = 0
    _total_cost = 0.0
    _session_start_time = time.time()  # 初始化为程序启动时间
    _analysis_start_time = time.time()  # 当前分析开始时间

    def __init__(self, model: Optional[str] = None, api_key: Optional[str] = None, api_base_url: Optional[str] = None):
        """
        Initialize the Alibaba Cloud AI models API client.

        Args:
            model: Model to use. If None, uses the default model from config.
            api_key: API key. If None, uses the key from config based on the model.
            api_base_url: API base URL. If None, uses the URL from config.
        """
        # 设置当前使用的模型
        self.model = model or config.DEFAULT_MODEL

        # 根据模型选择API密钥
        if api_key:
            self.api_key = api_key
        else:
            # 强制使用配置文件中的API密钥，忽略环境变量
            if self.model == "deepseek-r1":
                self.api_key = config.DEEPSEEK_API_KEY
            elif self.model == "qwen-plus-latest":
                self.api_key = config.QWEN_API_KEY
            else:
                model_config = config.SUPPORTED_MODELS.get(self.model, {})
                self.api_key = model_config.get("api_key", config.DEEPSEEK_API_KEY)  # 默认使用DeepSeek密钥

        # 确保API密钥是最新的
        if self.model == "deepseek-r1" and self.api_key != config.DEEPSEEK_API_KEY:
            logger.warning(f"API密钥不匹配，使用配置文件中的密钥: {config.DEEPSEEK_API_KEY[:6]}...")
            self.api_key = config.DEEPSEEK_API_KEY

        # 设置API基础URL
        self.api_base_url = api_base_url or config.DASHSCOPE_API_BASE_URL

        # 获取模型配置
        self.model_config = config.SUPPORTED_MODELS.get(self.model, {})
        self.model_name = self.model_config.get("name", self.model)
        self.endpoint = self.model_config.get("endpoint", f"{self.api_base_url}/services/aigc/text-generation/generation")

        logger.info(f"初始化 {self.model_name} API 客户端, API基础URL: {self.api_base_url}")
        logger.info(f"使用模型: {self.model_name}, 端点: {self.endpoint}")

        # 当前正在分析的小说ID
        self.current_novel_id = None
        self.current_analysis_type = None

        # 打印API密钥前几个字符（安全起见不打印全部）
        if self.api_key:
            masked_key = self.api_key[:6] + "..." + self.api_key[-4:]
            logger.info(f"API密钥已配置: {masked_key}")
        else:
            logger.warning("API密钥未配置，请检查环境变量或配置文件")

        # 从配置中获取API调用限制 - 已禁用限制
        self._api_call_limit = getattr(config, 'API_CALL_LIMIT_PER_HOUR', 10000)
        logger.info(f"API调用限制已禁用，改为记录调用次数和费用")

    @classmethod
    def _record_api_call(cls, input_tokens=0, output_tokens=0):
        """
        记录API调用，并清理过期记录，同时计算费用

        Args:
            input_tokens: 输入令牌数
            output_tokens: 输出令牌数
        """
        current_time = time.time()
        # 添加当前调用
        cls._api_calls.append(current_time)
        # 清理超过时间窗口的调用记录
        cls._api_calls = [t for t in cls._api_calls if current_time - t < cls._api_call_window]

        # 计算并累加令牌使用量和费用
        if input_tokens > 0 or output_tokens > 0:
            total_tokens = input_tokens + output_tokens
            cls._total_tokens_used += total_tokens

            # 计算费用（元）
            input_cost = (input_tokens / 1000) * config.API_COST_INPUT_PER_1K_TOKENS
            output_cost = (output_tokens / 1000) * config.API_COST_OUTPUT_PER_1K_TOKENS
            total_cost = input_cost + output_cost

            cls._total_cost += total_cost

            # 记录到日志
            logger.info(f"[API费用] 本次调用: 输入={input_tokens}令牌, 输出={output_tokens}令牌, 总计={total_tokens}令牌")
            logger.info(f"[API费用] 本次费用: 输入={input_cost:.4f}元, 输出={output_cost:.4f}元, 总计={total_cost:.4f}元")
            logger.info(f"[API费用] 累计使用: {cls._total_tokens_used}令牌, 累计费用: {cls._total_cost:.4f}元")

            return {
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": total_tokens,
                "input_cost": input_cost,
                "output_cost": output_cost,
                "total_cost": total_cost
            }

        return None

    @classmethod
    def get_api_call_stats(cls):
        """获取API调用统计信息，包括令牌使用量和费用"""
        current_time = time.time()
        # 清理过期记录
        cls._api_calls = [t for t in cls._api_calls if current_time - t < cls._api_call_window]
        # 计算当前小时内的调用次数
        calls_in_window = len(cls._api_calls)

        # 计算会话时长（分钟）
        session_duration_seconds = current_time - cls._session_start_time
        session_duration_minutes = session_duration_seconds / 60

        # 确保会话时长是合理的值（不超过24小时）
        if session_duration_minutes > 24 * 60:  # 超过24小时
            logger.warning(f"检测到异常会话时长: {session_duration_minutes:.2f}分钟，重置为当前分析时长")
            # 重置会话开始时间为分析开始时间
            cls._session_start_time = cls._analysis_start_time if hasattr(cls, '_analysis_start_time') and cls._analysis_start_time > 0 else current_time - 3600
            session_duration_seconds = current_time - cls._session_start_time
            session_duration_minutes = session_duration_seconds / 60

        # 计算当前分析时长（分钟）
        # 确保分析开始时间存在且有效
        if hasattr(cls, '_analysis_start_time') and cls._analysis_start_time > 0:
            analysis_duration_seconds = current_time - cls._analysis_start_time
            analysis_duration_minutes = analysis_duration_seconds / 60

            # 确保分析时长是合理的值（不超过会话时长）
            if analysis_duration_minutes > session_duration_minutes:
                logger.warning(f"检测到异常分析时长: {analysis_duration_minutes:.2f}分钟 > 会话时长: {session_duration_minutes:.2f}分钟，调整为会话时长")
                analysis_duration_minutes = session_duration_minutes

            # 确保分析时长不会过大（不超过12小时）
            if analysis_duration_minutes > 12 * 60:
                logger.warning(f"检测到异常分析时长: {analysis_duration_minutes:.2f}分钟，重置为合理值")
                # 重置分析开始时间
                cls._analysis_start_time = current_time - 3600  # 默认设为1小时前
                analysis_duration_seconds = current_time - cls._analysis_start_time
                analysis_duration_minutes = analysis_duration_seconds / 60
        else:
            # 如果分析开始时间无效，使用会话时长作为备用，但不超过1小时
            analysis_duration_minutes = min(session_duration_minutes, 60)
            # 设置分析开始时间为当前时间，避免未来计算出错
            cls._analysis_start_time = current_time - (analysis_duration_minutes * 60)

        # 记录时长信息到日志
        logger.info(f"会话时长: {session_duration_minutes:.2f}分钟 ({session_duration_seconds:.2f}秒)")
        logger.info(f"分析时长: {analysis_duration_minutes:.2f}分钟 ({analysis_duration_seconds:.2f}秒)")

        return {
            "calls_in_window": calls_in_window,
            "total_calls": len(cls._api_calls),
            "total_tokens": cls._total_tokens_used,
            "total_cost": cls._total_cost,
            "session_duration_minutes": round(session_duration_minutes, 2),
            "analysis_duration_minutes": round(analysis_duration_minutes, 2),
            "session_duration_seconds": round(session_duration_seconds, 2),
            "analysis_duration_seconds": round(analysis_duration_seconds, 2),
            "average_cost_per_call": round(cls._total_cost / max(1, len(cls._api_calls)), 4),
            "average_tokens_per_call": round(cls._total_tokens_used / max(1, len(cls._api_calls)), 0)
        }

    def _save_api_log(self, api_log):
        """保存API调用日志到数据库"""
        try:
            session = Session()

            # 确保API日志对象有效
            if not hasattr(api_log, 'endpoint') or not api_log.endpoint:
                api_log.endpoint = "unknown"
            if not hasattr(api_log, 'method') or not api_log.method:
                api_log.method = "POST"

            # 添加并保存
            session.add(api_log)
            session.commit()
            logger.debug(f"已保存API调用日志，ID: {api_log.id}")
        except Exception as e:
            logger.error(f"保存API调用日志时出错: {str(e)}")
            try:
                session.rollback()
            except Exception as rollback_error:
                logger.error(f"回滚API日志保存时出错: {str(rollback_error)}")
        finally:
            try:
                session.close()
            except Exception as close_error:
                logger.error(f"关闭数据库会话时出错: {str(close_error)}")

    @classmethod
    def check_rate_limit(cls):
        """
        检查API调用统计信息，但不限制调用

        Returns:
            (True, stats): 始终返回True表示允许调用，同时返回统计信息
        """
        stats = cls.get_api_call_stats()
        # 已禁用API调用限制，始终返回True
        return True, stats

    def analyze_text(self, text: str, analysis_type: str, novel_id: int = None, chapter_id: int = None, progress_callback=None, max_tokens: int = None) -> Dict[str, Any]:
        """
        Analyze text using the DeepSeek API with dynamic max_tokens based on analysis type and text length.

        Args:
            text: Text to analyze.
            analysis_type: Type of analysis to perform.
            novel_id: Novel ID (optional).
            chapter_id: Chapter ID (optional).
            progress_callback: Progress callback function (optional).
            max_tokens: Optional override for max_tokens. If None, will be determined dynamically.

        Returns:
            Analysis result.
        """
        # 记录当前分析的小说ID和章节ID（使用简单的整数值，避免存储对象引用）
        self.current_novel_id = novel_id
        self.current_chapter_id = chapter_id

        # 记录实际使用的小说ID和章节ID，用于调试
        logger.info(f"实际使用的小说ID: {novel_id}, 章节ID: {chapter_id}")
        # 动态确定max_tokens
        if max_tokens is None:
            # 根据分析类型选择合适的max_tokens
            if hasattr(config, 'DIMENSION_MAX_TOKENS'):
                max_tokens = config.DIMENSION_MAX_TOKENS.get(analysis_type, config.DIMENSION_MAX_TOKENS.get("default", 2000))
            else:
                max_tokens = 2000  # 默认值

            # 根据文本长度动态调整max_tokens
            text_length = len(text)
            if text_length < 2000:
                # 对于短文本，适当减少max_tokens
                max_tokens = int(min(max_tokens, max_tokens * 0.8))
            elif text_length > 10000:
                # 对于长文本，适当增加max_tokens，但不超过配置的1.2倍
                max_tokens = int(min(max_tokens * 1.2, max_tokens * 1.2))

            logger.info(f"[API请求] 为 {analysis_type} 分析动态设置max_tokens: {max_tokens}，文本长度: {text_length}")

        # 设置当前分析类型
        self.current_analysis_type = analysis_type

        logger.info(f"[API请求] 开始 {analysis_type} 分析，文本长度: {len(text)} 字符")
        logger.info(f"[API请求] 最大生成令牌数: {max_tokens}")

        # 尝试添加分析日志到控制台
        try:
            # 尝试获取当前正在分析的小说ID
            novel_id = getattr(self, 'current_novel_id', None)
            if novel_id:
                # 使用日志记录而不是直接调用add_analysis_log
                logger.info(f"[API请求] 开始 {analysis_type} 分析，文本长度: {len(text)} 字符")
                logger.debug(f"[API请求] 最大生成令牌数: {max_tokens}")

                # 获取当前API调用统计
                stats = self.get_api_call_stats()
                logger.info(f"[API统计] 当前会话: 已调用{stats['total_calls']}次API, 累计费用: {stats['total_cost']:.4f}元")
        except Exception as log_error:
            logger.error(f"添加API分析日志时出错: {str(log_error)}")

        # 根据分析类型构建提示词
        prompt = self._create_analysis_prompt(text, analysis_type)

        # 使用当前模型的端点
        endpoint = self.endpoint
        logger.info(f"[API请求] 使用 {self.model_name} API端点: {endpoint}")

        # 打印API密钥信息
        logger.info(f"[API请求] 使用的API密钥: {self.api_key[:6]}...")

        start_time = time.time()
        logger.info(f"[API请求] 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 如果是API测试连接，使用示范样本，但强制禁用DEBUG模式
        if analysis_type == "test_connection":
            logger.info(f"[API请求] 使用示范样本响应，分析类型: {analysis_type}")
            return self._get_sample_response(analysis_type, text)

        # 强制禁用DEBUG模式，确保使用真实API调用
        if False and config.DEBUG:  # 永远不会执行的条件
            logger.info(f"[API请求] DEBUG模式已被强制禁用，将使用真实API调用")
            # 不返回示范样本，继续执行真实API调用

        # 检查API调用频率限制
        if config.API_RATE_LIMIT_ENABLED:
            can_call, stats = self.check_rate_limit()
            if not can_call:
                logger.warning(f"API调用频率超出限制! 已调用: {stats['calls_in_window']}/{stats['limit']}，将在 {stats['reset_seconds']} 秒后重置")
                return {
                    "error": f"API调用频率超出限制，请稍后再试。已调用: {stats['calls_in_window']}/{stats['limit']}，将在 {stats['reset_seconds']} 秒后重置"
                }

        # 添加请求状态监控
        request_start_time = time.time()
        request_id = f"req_{int(request_start_time)}_{analysis_type}"

        # 记录请求开始时间
        logger.info(f"[API请求] 开始请求 ID: {request_id}, 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 实际API调用
        try:
            # 创建API日志记录
            try:
                api_log = ApiLog(
                    endpoint="text-generation",
                    method="POST",
                    novel_id=getattr(self, 'current_novel_id', None),
                    analysis_type=analysis_type
                )
                # 记录请求参数
                api_log.input_tokens = len(text) // 4  # 粗略估计输入令牌数
            except Exception as e:
                logger.error(f"创建API日志记录时出错: {str(e)}")
                api_log = ApiLog(
                    endpoint="text-generation",
                    method="POST"
                )

            # 确保API密钥格式正确
            # 阿里云DeepSeek API需要使用Bearer前缀的认证头
            api_key = self.api_key

            # 确保使用最新的API密钥
            if self.model == "deepseek-r1":
                api_key = config.DEEPSEEK_API_KEY
                logger.info(f"[API请求] 使用DeepSeek R1 API密钥: {api_key[:6]}...")
            elif self.model == "qwen-plus-latest":
                api_key = config.QWEN_API_KEY
                logger.info(f"[API请求] 使用通义千问API密钥: {api_key[:6]}...")

            # 移除可能存在的Bearer前缀，稍后会重新添加
            if api_key.startswith("Bearer "):
                api_key = api_key[7:]  # 移除"Bearer "前缀

            # 阿里云DeepSeek API需要使用特定的认证头格式
            headers = {
                "Authorization": f"Bearer {api_key}",  # 阿里云DeepSeek API需要Bearer前缀
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            # 打印API密钥信息用于调试（不打印完整密钥，只打印前后几位）
            logger.info(f"[API请求] 使用API密钥: {api_key[:6]}...{api_key[-4:] if len(api_key) > 10 else ''}")

            # 添加到控制台日志
            try:
                novel_id = getattr(self, 'current_novel_id', None)
                if novel_id:
                    # 使用日志记录而不是直接调用add_analysis_log
                    logger.info(f"[API请求] 准备调用阿里云 {self.model_name} API，使用密钥: {api_key[:6]}...{api_key[-4:]}")
                    logger.info(f"[API请求] 分析维度: {analysis_type}，文本长度: {len(text)} 字符")
                    logger.info(f"[API请求] 使用模型: {self.model}")
            except Exception as log_error:
                logger.error(f"添加API密钥日志时出错: {str(log_error)}")

            # 确保请求体中不包含任何可能导致编码问题的字符
            # 根据阿里云API的要求调整请求体格式
            # 参考阿里云API文档: https://help.aliyun.com/document_detail/2400162.html

            # 根据模型选择不同的请求体格式
            if self.model == "deepseek-r1":
                # DeepSeek R1 模型使用的请求体格式
                payload = {
                    "model": "deepseek-r1",
                    "input": {
                        "prompt": prompt,
                        "parameters": {
                            "max_tokens": max_tokens,
                            "temperature": 0.1,
                            "top_p": 0.8,
                            "top_k": 50,
                            "repetition_penalty": 1.1,
                            "stop": []
                        }
                    }
                }
            else:
                # 通义千问等其他模型使用的请求体格式
                payload = {
                    "model": self.model,
                    "input": {
                        "messages": [
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ]
                    },
                    "parameters": {
                        "max_tokens": max_tokens,
                        "temperature": 0.7,
                        "top_p": 0.8,
                        "result_format": "message"
                    }
                }

            logger.info(f"[API请求] 请求体: {payload}")

            # 使用当前模型的端点URL
            clean_endpoint = self.endpoint

            logger.info(f"[API请求] 发送POST请求到阿里云 {self.model_name} API: {clean_endpoint}")
            logger.info(f"[API请求] 请求头: {headers}")
            logger.info(f"[API请求] 请求大小: {len(json.dumps(payload))} 字节")

            # 打印请求信息用于调试（不包含完整的请求头和请求体，避免泄露敏感信息）
            logger.info(f"[API请求] 请求信息: URL={clean_endpoint}, 请求体大小={len(json.dumps(payload))}字节")

            # 添加到控制台日志
            try:
                novel_id = getattr(self, 'current_novel_id', None)
                if novel_id:
                    # 使用日志记录而不是直接调用add_analysis_log
                    logger.info(f"[API请求] 正在发送请求到阿里云 {self.model_name} API...")
                    logger.debug(f"[API请求] 请求体大小: {len(json.dumps(payload))}字节")
                    logger.debug(f"[API请求] 分析类型: {analysis_type}")
            except Exception as log_error:
                logger.error(f"添加API请求日志时出错: {str(log_error)}")

            # 添加到控制台日志
            try:
                novel_id = getattr(self, 'current_novel_id', None)
                if novel_id:
                    # 使用日志记录而不是直接调用add_analysis_log
                    logger.info(f"[API请求] 发送请求到阿里云 {self.model_name} API")
                    logger.debug(f"[API请求] 使用模型: {self.model}")
            except Exception as log_error:
                logger.error(f"添加API请求日志时出错: {str(log_error)}")

            # 添加增强的重试机制 - 增加重试次数和使用指数退避策略
            max_retries = 5
            base_retry_delay = 5  # 初始重试延迟（秒）
            max_retry_delay = 60  # 最大重试延迟（秒）

            # 添加到控制台日志
            try:
                novel_id = getattr(self, 'current_novel_id', None)
                if novel_id:
                    # 使用日志记录而不是直接调用add_analysis_log
                    logger.info(f"[API请求] 开始发送请求，最大重试次数: {max_retries}")
            except Exception as log_error:
                logger.error(f"添加API重试日志时出错: {str(log_error)}")

            for retry in range(max_retries):
                try:
                    logger.info(f"[API请求] 尝试 {retry+1}/{max_retries}")

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API请求] 尝试 {retry+1}/{max_retries}")
                    except Exception as log_error:
                        logger.error(f"添加API尝试日志时出错: {str(log_error)}")

                    # 发送请求 - 使用更合理的超时时间，并分别设置连接超时和读取超时
                    # 连接超时30秒，读取超时120秒，总超时不超过150秒
                    response = requests.post(clean_endpoint, headers=headers, json=payload, timeout=(30, 120))

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API请求] 请求已发送，状态码: {response.status_code}")
                    except Exception as log_error:
                        logger.error(f"添加API响应状态日志时出错: {str(log_error)}")

                    break
                except Exception as e:
                    # 处理所有可能的异常，包括连接错误、超时、SSL错误等
                    error_type = type(e).__name__
                    error_msg = str(e)

                    if retry < max_retries - 1:
                        # 计算当前重试的延迟时间（指数退避策略）
                        current_retry_delay = min(base_retry_delay * (2 ** retry), max_retry_delay)
                        logger.warning(f"[API请求] 尝试 {retry+1} 失败: {error_type}: {error_msg}，将在 {current_retry_delay} 秒后重试")

                        # 添加到控制台日志
                        try:
                            novel_id = getattr(self, 'current_novel_id', None)
                            if novel_id:
                                # 使用日志记录而不是直接调用add_analysis_log
                                logger.warning(f"[API请求] 尝试 {retry+1} 失败: {error_type}: {error_msg[:100]}，将在 {current_retry_delay} 秒后重试")
                        except Exception as log_error:
                            logger.error(f"添加API失败日志时出错: {str(log_error)}")

                        # 根据错误类型调整重试策略
                        if isinstance(e, requests.exceptions.Timeout):
                            logger.warning(f"[API请求] 超时错误，增加下次超时时间")
                            # 下次请求增加超时时间
                            headers["X-Timeout-Retry"] = "true"
                        elif isinstance(e, requests.exceptions.ConnectionError):
                            logger.warning(f"[API请求] 连接错误，可能是网络问题")
                        elif isinstance(e, requests.exceptions.RequestException):
                            logger.warning(f"[API请求] 请求错误: {error_msg}")

                        # 计算当前重试的延迟时间（指数退避策略）
                        current_retry_delay = min(base_retry_delay * (2 ** retry), max_retry_delay)
                        logger.warning(f"[API请求] 将在 {current_retry_delay} 秒后重试")

                        # 添加到控制台日志
                        try:
                            novel_id = getattr(self, 'current_novel_id', None)
                            if novel_id:
                                # 使用日志记录而不是直接调用add_analysis_log
                                logger.warning(f"[API请求] 将在 {current_retry_delay} 秒后重试 (尝试 {retry+2}/{max_retries})")
                        except Exception as log_error:
                            logger.error(f"添加API重试日志时出错: {str(log_error)}")

                        time.sleep(current_retry_delay)
                    else:
                        # 计算总请求时间
                        total_request_time = time.time() - request_start_time

                        logger.error(f"[API请求] 请求 ID: {request_id} 失败")
                        logger.error(f"[API请求] 所有重试都失败: {error_type}: {error_msg}")
                        logger.error(f"[API请求] 总请求时间: {total_request_time:.2f}秒")

                        # 添加到控制台日志
                        try:
                            novel_id = getattr(self, 'current_novel_id', None)
                            if novel_id:
                                # 使用日志记录而不是直接调用add_analysis_log
                                logger.error(f"[API请求] 所有重试都失败: {error_type}: {error_msg[:100]}")
                                logger.error(f"[API请求] 请尝试检查网络连接或API密钥是否正确")
                        except Exception as log_error:
                            logger.error(f"添加API失败日志时出错: {str(log_error)}")

                        # 记录API调用失败
                        api_log.error_type = error_type
                        api_log.error_message = error_msg
                        self._save_api_log(api_log)

                        # 返回错误信息而不是抛出异常，让系统能够继续运行
                        return {
                            "error": f"API调用失败: {error_type}: {error_msg}",
                            "type": analysis_type
                        }

            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            total_request_time = end_time - request_start_time  # 总请求时间（包括重试）

            logger.info(f"[API响应] 请求 ID: {request_id} 完成")
            logger.info(f"[API响应] 请求耗时: {end_time - start_time:.2f}秒")
            logger.info(f"[API响应] 总请求时间（含重试）: {total_request_time:.2f}秒")
            logger.info(f"[API响应] 状态码: {response.status_code}")

            # 更新API日志
            api_log.status_code = response.status_code
            api_log.response_time = response_time
            api_log.response_size = len(response.content) if response.content else 0

            if response.headers:
                logger.info(f"[API响应] 响应头: {dict(response.headers)}")

            # 尝试获取响应内容
            try:
                response_text = response.text
                logger.info(f"[API响应] 响应内容（前500字符）: {response_text[:500]}...")
            except Exception as e:
                logger.warning(f"[API响应] 无法获取响应文本: {str(e)}")
                api_log.error_type = "response_parse_error"
                api_log.error_message = str(e)

            if response.status_code == 200:
                try:
                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API响应] 收到成功响应 (状态码: 200)，正在解析...")
                    except Exception as log_error:
                        logger.error(f"添加API响应日志时出错: {str(log_error)}")

                    response_json = response.json()

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API响应] 成功解析JSON响应")
                    except Exception as log_error:
                        logger.error(f"添加API响应解析日志时出错: {str(log_error)}")

                    # 提取令牌使用信息 - 根据不同模型处理不同的响应格式
                    if self.model == "deepseek-r1":
                        # DeepSeek R1 模型的响应格式
                        output = response_json.get("output", {})
                        usage = output.get("usage", {})
                        prompt_tokens = usage.get("prompt_tokens", 0)
                        completion_tokens = usage.get("completion_tokens", 0)
                        total_tokens = prompt_tokens + completion_tokens
                    else:
                        # 通义千问等其他模型的响应格式
                        usage = response_json.get("usage", {})
                        prompt_tokens = usage.get("input_tokens", 0)
                        completion_tokens = usage.get("output_tokens", 0)
                        total_tokens = usage.get("total_tokens", 0)

                    logger.info(f"[API响应] 令牌使用信息: 输入={prompt_tokens}, 输出={completion_tokens}, 总计={total_tokens}")

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API响应] 令牌使用: 输入={prompt_tokens}, 输出={completion_tokens}, 总计={total_tokens}")
                    except Exception as log_error:
                        logger.error(f"添加API令牌日志时出错: {str(log_error)}")

                    # 记录响应结构，帮助调试
                    logger.info(f"[API响应] 响应结构: {list(response_json.keys())}")

                    # 提取和处理内容 - 适配阿里云DeepSeek API的响应格式
                    # 尝试多种可能的响应格式
                    content = None

                    # 添加到控制台日志
                    try:
                        novel_id = getattr(self, 'current_novel_id', None)
                        if novel_id:
                            # 使用日志记录而不是直接调用add_analysis_log
                            logger.info(f"[API响应] 正在提取响应内容...")
                    except Exception as log_error:
                        logger.error(f"添加API内容提取日志时出错: {str(log_error)}")

                    # 记录响应JSON，帮助调试
                    logger.info(f"[API响应] 响应JSON摘要: {json.dumps(response_json)[:500]}...")

                    # 根据不同模型处理不同的响应格式
                    if self.model == "deepseek-r1":
                        # DeepSeek R1 模型的响应格式
                        output = response_json.get("output", {})
                        if output:
                            logger.info(f"[API响应] 找到output字段: {output}")

                            # 添加到控制台日志
                            try:
                                novel_id = getattr(self, 'current_novel_id', None)
                                if novel_id:
                                    # 使用日志记录而不是直接调用add_analysis_log
                                    logger.info(f"[API响应] 找到output字段，继续解析...")
                            except Exception as log_error:
                                logger.error(f"添加API输出字段日志时出错: {str(log_error)}")

                            # DeepSeek R1 模型可能在output.text中返回内容
                            content = output.get("text", "")

                            # 如果没有找到text字段，尝试从choices中获取
                            if not content and "choices" in output and len(output["choices"]) > 0:
                                choice = output["choices"][0]
                                logger.info(f"[API响应] 找到choice: {choice}")

                                # 尝试从message.content中获取内容
                                if "message" in choice and "content" in choice["message"]:
                                    content = choice["message"]["content"]
                                    logger.info(f"[API响应] 从choice.message.content中获取到内容")
                            if content:
                                logger.info(f"[API响应] 从DeepSeek R1 output.text字段获取到内容: {content[:200]}...")

                                # 添加到控制台日志
                                try:
                                    novel_id = getattr(self, 'current_novel_id', None)
                                    if novel_id:
                                        # 使用日志记录而不是直接调用add_analysis_log
                                        logger.info(f"[API响应] 成功提取内容，长度: {len(content)} 字符")
                                except Exception as log_error:
                                    logger.error(f"添加API内容提取日志时出错: {str(log_error)}")
                    else:
                        # 通义千问等其他模型的响应格式 - 标准格式 output -> choices -> message -> content
                        output = response_json.get("output", {})
                        if output:
                            logger.info(f"[API响应] 找到output字段: {output}")

                            # 添加到控制台日志
                            try:
                                novel_id = getattr(self, 'current_novel_id', None)
                                if novel_id:
                                    # 使用日志记录而不是直接调用add_analysis_log
                                    logger.info(f"[API响应] 找到output字段，继续解析...")
                            except Exception as log_error:
                                logger.error(f"添加API输出字段日志时出错: {str(log_error)}")

                            choices = output.get("choices", [])
                            if choices and len(choices) > 0:
                                choice = choices[0]
                                logger.info(f"[API响应] 找到choice: {choice}")

                                # 添加到控制台日志
                                try:
                                    from src.web.app import add_analysis_log
                                    novel_id = getattr(self, 'current_novel_id', None)
                                    if novel_id:
                                        add_analysis_log(novel_id, f"[API响应] 找到choices字段，继续解析...", "info", analysis_type)
                                except Exception as log_error:
                                    logger.error(f"添加API选择字段日志时出错: {str(log_error)}")

                                message = choice.get("message", {})
                                logger.info(f"[API响应] 找到message: {message}")

                                # 添加到控制台日志
                                try:
                                    from src.web.app import add_analysis_log
                                    novel_id = getattr(self, 'current_novel_id', None)
                                    if novel_id:
                                        add_analysis_log(novel_id, f"[API响应] 找到message字段，提取内容...", "info", analysis_type)
                                except Exception as log_error:
                                    logger.error(f"添加API消息字段日志时出错: {str(log_error)}")

                                content = message.get("content", "")
                                logger.info(f"[API响应] 从标准路径获取到内容: {content[:200]}...")

                                # 添加到控制台日志
                                try:
                                    from src.web.app import add_analysis_log
                                    novel_id = getattr(self, 'current_novel_id', None)
                                    if novel_id:
                                        add_analysis_log(novel_id, f"[API响应] 成功提取内容，长度: {len(content)} 字符", "info", analysis_type)
                                except Exception as log_error:
                                    logger.error(f"添加API内容提取日志时出错: {str(log_error)}")

                    # 备用方式: 直接在响应中的content字段
                    if not content:
                        content = response_json.get("content", "")
                        if content:
                            logger.info(f"[API响应] 从顶层content字段获取到内容: {content[:200]}...")

                    # 方式3: 在data或result字段中
                    if not content:
                        data = response_json.get("data", {})
                        if data:
                            logger.info(f"[API响应] 找到data字段: {data}")
                            content = data.get("content", "")
                            if content:
                                logger.info(f"[API响应] 从data字段获取到内容: {content[:200]}...")

                    # 方式4: 在result字段中
                    if not content:
                        result = response_json.get("result", {})
                        if result:
                            logger.info(f"[API响应] 找到result字段: {result}")
                            content = result.get("content", "")
                            if content:
                                logger.info(f"[API响应] 从result字段获取到内容: {content[:200]}...")

                    # 方式5: 在response字段中
                    if not content:
                        response_field = response_json.get("response", "")
                        if response_field:
                            logger.info(f"[API响应] 找到response字段: {response_field}")
                            if isinstance(response_field, str):
                                content = response_field
                                logger.info(f"[API响应] 从response字段获取到内容: {content[:200]}...")

                    if content:
                        logger.info(f"[API响应] 内容摘要: {content[:200]}...")
                        logger.info(f"[API处理] 成功解析 {analysis_type} 分析结果")

                        # 添加到控制台日志
                        try:
                            from src.web.app import add_analysis_log
                            novel_id = getattr(self, 'current_novel_id', None)
                            if novel_id:
                                add_analysis_log(novel_id, f"[API响应] 成功获取 {analysis_type} 分析结果", "info", analysis_type)
                                add_analysis_log(novel_id, f"[API响应] 内容长度: {len(content)} 字符", "debug", analysis_type)
                        except Exception as log_error:
                            logger.error(f"添加API响应日志时出错: {str(log_error)}")

                        # 记录令牌使用量和费用
                        cost_info = self._record_api_call(prompt_tokens, completion_tokens)

                        # 更新API日志
                        try:
                            api_log.input_tokens = prompt_tokens
                            api_log.output_tokens = completion_tokens
                            api_log.total_tokens = total_tokens
                            api_log.novel_id = getattr(self, 'current_novel_id', None)
                            api_log.analysis_type = analysis_type

                            if cost_info:
                                api_log.input_cost = cost_info["input_cost"]
                                api_log.output_cost = cost_info["output_cost"]
                                api_log.total_cost = cost_info["total_cost"]

                            # 记录请求和响应数据（截断以避免数据库存储问题）
                            api_log.parameters = {
                                "model": self.model,
                                "analysis_type": analysis_type,
                                "max_tokens": max_tokens,
                                "text_length": len(text)
                            }
                        except Exception as e:
                            logger.error(f"更新API日志时出错: {str(e)}")

                            # 添加到控制台日志
                            try:
                                from src.web.app import add_analysis_log
                                novel_id = getattr(self, 'current_novel_id', None)
                                if novel_id:
                                    add_analysis_log(novel_id, f"[API费用] 本次调用: 输入={prompt_tokens}令牌, 输出={completion_tokens}令牌, 总计={total_tokens}令牌", "info", analysis_type)
                                    add_analysis_log(novel_id, f"[API费用] 本次费用: 输入={cost_info['input_cost']:.4f}元, 输出={cost_info['output_cost']:.4f}元, 总计={cost_info['total_cost']:.4f}元", "info", analysis_type)

                                    # 获取累计统计信息
                                    stats = self.get_api_call_stats()
                                    add_analysis_log(novel_id, f"[API统计] 累计调用: {stats['total_calls']}次, 累计令牌: {stats['total_tokens']}个, 累计费用: {stats['total_cost']:.4f}元", "info", analysis_type)
                            except Exception as log_error:
                                logger.error(f"添加API费用日志时出错: {str(log_error)}")

                        # 保存API日志
                        self._save_api_log(api_log)

                        # 检查内容是否是JSON格式的字符串，如果是，尝试提取实际内容
                        if isinstance(content, str) and (content.startswith('{"output":') or content.startswith('{"choices":')):
                            try:
                                # 尝试解析JSON
                                content_json = json.loads(content)
                                logger.info(f"[API处理] 检测到JSON格式的内容，尝试提取实际内容")

                                # 尝试从不同路径提取内容
                                extracted_content = None

                                # 路径1: output.choices[0].message.content
                                if 'output' in content_json and 'choices' in content_json['output'] and len(content_json['output']['choices']) > 0:
                                    choice = content_json['output']['choices'][0]
                                    if 'message' in choice and 'content' in choice['message']:
                                        extracted_content = choice['message']['content']
                                        logger.info(f"[API处理] 从output.choices[0].message.content提取到内容")

                                        # 尝试提取推理过程内容
                                        if 'reasoning_content' in choice['message']:
                                            reasoning_content = choice['message']['reasoning_content']
                                            logger.info(f"[API处理] 从output.choices[0].message.reasoning_content提取到推理过程内容，长度: {len(reasoning_content)} 字符")

                                # 路径2: output.text
                                if not extracted_content and 'output' in content_json and 'text' in content_json['output']:
                                    extracted_content = content_json['output']['text']
                                    logger.info(f"[API处理] 从output.text提取到内容")

                                # 如果成功提取到内容，更新content
                                if extracted_content:
                                    logger.info(f"[API处理] 成功从JSON中提取内容，长度: {len(extracted_content)} 字符")
                                    content = extracted_content

                                    # 添加到控制台日志
                                    try:
                                        from src.web.app import add_analysis_log
                                        novel_id = getattr(self, 'current_novel_id', None)
                                        if novel_id:
                                            add_analysis_log(novel_id, f"[API处理] 成功从JSON中提取内容，长度: {len(extracted_content)} 字符", "info", analysis_type)
                                    except Exception as log_error:
                                        logger.error(f"添加API内容提取日志时出错: {str(log_error)}")
                            except json.JSONDecodeError as e:
                                logger.error(f"[API处理] 解析JSON内容时出错: {str(e)}")
                                # 保持原始内容不变

                        # 检查是否是推理过程分析
                        if analysis_type.endswith("_reasoning"):
                            logger.info(f"这是推理过程分析，将content同时作为reasoning_content返回")
                            result = {
                                "type": analysis_type,
                                "content": content,
                                "reasoning_content": content
                            }
                        else:
                            # 尝试从API响应中提取reasoning_content
                            reasoning_content = ""
                            try:
                                # 尝试从响应中提取reasoning_content
                                if 'output' in response_json and 'choices' in response_json['output'] and len(response_json['output']['choices']) > 0:
                                    choice = response_json['output']['choices'][0]
                                    if 'message' in choice and 'reasoning_content' in choice['message']:
                                        reasoning_content = choice['message']['reasoning_content']
                                        logger.info(f"从API响应中提取到reasoning_content，长度: {len(reasoning_content)} 字符")
                                    # 如果没有reasoning_content字段，但有content字段，则使用content字段作为推理过程内容
                                    elif 'message' in choice and 'content' in choice['message']:
                                        # 对于某些特定的分析类型，将content作为reasoning_content
                                        if analysis_type in ["language_style", "rhythm_pacing", "structure", "character_relationships"]:
                                            reasoning_content = choice['message']['content']
                                            logger.info(f"使用content字段作为reasoning_content，长度: {len(reasoning_content)} 字符")
                            except Exception as e:
                                logger.error(f"提取reasoning_content时出错: {str(e)}")

                            # 对于普通分析，添加reasoning_content字段
                            result = {
                                "type": analysis_type,
                                "content": content,
                                "reasoning_content": reasoning_content
                            }

                        logger.info(f"返回分析结果: type={result['type']}, content长度={len(result['content'])}, reasoning_content长度={len(result['reasoning_content'])}")
                        return result
                    else:
                        error_msg = f"API响应中没有找到内容，请检查响应格式: {response_json}"
                        logger.error(error_msg)
                        # 记录API错误
                        api_log.error_type = "content_not_found"
                        api_log.error_message = error_msg
                        self._save_api_log(api_log)
                        return {"error": error_msg}
                except json.JSONDecodeError as e:
                    error_msg = f"API响应不是有效的JSON: {str(e)}, 响应内容: {response.text[:500]}..."
                    logger.error(error_msg)
                    # 记录API错误
                    api_log.error_type = "json_decode_error"
                    api_log.error_message = error_msg
                    self._save_api_log(api_log)
                    return {"error": error_msg}
            elif response.status_code == 401:
                error_msg = f"API授权失败：请检查API密钥是否正确。状态码={response.status_code}, 响应={response.text}"
                logger.error(error_msg)
                # 记录API错误
                api_log.error_type = "authentication_error"
                api_log.error_message = error_msg
                self._save_api_log(api_log)
                return {"error": error_msg}
            elif response.status_code == 400:
                error_msg = f"API请求参数错误：请检查请求参数。状态码={response.status_code}, 响应={response.text}"
                logger.error(error_msg)
                # 记录API错误
                api_log.error_type = "bad_request"
                api_log.error_message = error_msg
                self._save_api_log(api_log)
                return {"error": error_msg}
            else:
                error_msg = f"API调用失败: 状态码={response.status_code}, 响应={response.text}"
                logger.error(error_msg)
                # 记录API错误
                api_log.error_type = "api_error"
                api_log.error_message = error_msg
                self._save_api_log(api_log)
                return {"error": error_msg}

        except requests.exceptions.Timeout:
            error_msg = "API请求超时，请稍后重试"
            logger.error(error_msg)
            return {"error": error_msg}
        except requests.exceptions.ConnectionError:
            error_msg = "连接API服务器失败，请检查网络连接"
            logger.error(error_msg)
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"API调用过程中出错: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {"error": error_msg}

    async def analyze_chapter_dimension(self, novel_id, chapter_id, dimension, previous_analyses=None):
        """
        分析小说章节的特定维度，强化章节间的连贯性和递进关系

        Args:
            novel_id: 小说ID
            chapter_id: 章节ID
            dimension: 分析维度
            previous_analyses: 前序章节的分析结果列表

        Returns:
            分析结果
        """
        try:
            # 获取章节内容
            chapter_content = await self.get_chapter_content(novel_id, chapter_id)
            if not chapter_content:
                return {"error": "无法获取章节内容"}

            # 获取章节标题
            chapter_info = await self.get_chapter_info(novel_id, chapter_id)
            chapter_title = chapter_info.get("title", f"第{chapter_id}章")
            chapter_number = chapter_info.get("chapter_number", chapter_id)

            # 获取更多的前序章节分析结果，以增强连贯性
            if not previous_analyses:
                # 如果没有提供前序章节分析，尝试获取更多前序章节的分析结果
                previous_analyses = await self.get_previous_chapter_analyses(
                    novel_id=novel_id,
                    chapter_id=chapter_id,
                    dimension=dimension,
                    limit=3  # 获取前3个章节的分析结果，增加连贯性
                )

            # 获取小说基本信息，为分析提供更多上下文
            novel_info = await self.get_novel_info(novel_id)
            novel_title = novel_info.get("title", "未知小说")

            # 构建增强的提示词，强调章节间的连贯性和递进关系
            prompt = await self.build_chapter_analysis_prompt(
                dimension,
                chapter_content,
                chapter_title,
                previous_analyses,
                novel_title=novel_title,
                chapter_number=chapter_number
            )

            # 在日志中记录分析的上下文信息
            logger.info(f"开始分析 '{novel_title}' 的第 {chapter_number} 章 '{chapter_title}' 的 {dimension} 维度")
            if previous_analyses:
                logger.info(f"分析包含 {len(previous_analyses)} 个前序章节的连贯性和递进关系")

            # 调用API进行分析
            response = await self.analyze_with_api(prompt)

            if not response:
                return {"error": "API分析失败"}

            # 增强分析结果，确保包含章节间的连贯性分析
            if response and "content" in response:
                # 检查分析结果是否包含章节间联系分析
                if "章节间联系" not in response["content"] and previous_analyses:
                    # 如果没有包含章节间联系分析，添加一个提示
                    response["content"] += "\n\n## 章节间联系与发展提示\n本章节应与前序章节形成连贯的分析体系，请参考前序章节分析结果，关注情节、人物、主题和写作手法的延续与发展。"

            return response

        except Exception as e:
            logger.error(f"分析章节维度时出错: {str(e)}")
            return {"error": f"分析章节维度时出错: {str(e)}"}

    async def get_chapter_content(self, novel_id, chapter_id):
        """获取章节内容"""
        try:
            from src.models.chapter import Chapter
            from src.db.connection import Session

            session = Session()
            try:
                chapter = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.id == chapter_id
                ).first()

                if not chapter:
                    logger.error(f"找不到章节: novel_id={novel_id}, chapter_id={chapter_id}")
                    return None

                return chapter.content
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取章节内容时出错: {str(e)}")
            return None

    async def get_chapter_info(self, novel_id, chapter_id):
        """获取章节信息"""
        try:
            from src.models.chapter import Chapter
            from src.db.connection import Session

            session = Session()
            try:
                chapter = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.id == chapter_id
                ).first()

                if not chapter:
                    logger.error(f"找不到章节: novel_id={novel_id}, chapter_id={chapter_id}")
                    return {}

                return {
                    "id": chapter.id,
                    "title": chapter.title,
                    "chapter_number": chapter.chapter_number,
                    "word_count": len(chapter.content) if chapter.content else 0
                }
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取章节信息时出错: {str(e)}")
            return {}

    async def get_novel_info(self, novel_id):
        """获取小说信息"""
        try:
            from src.models.novel import Novel
            from src.db.connection import Session

            session = Session()
            try:
                novel = session.query(Novel).filter(Novel.id == novel_id).first()

                if not novel:
                    logger.error(f"找不到小说: novel_id={novel_id}")
                    return {}

                return {
                    "id": novel.id,
                    "title": novel.title,
                    "author": novel.author,
                    "word_count": novel.word_count
                }
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取小说信息时出错: {str(e)}")
            return {}

    async def get_previous_chapter_analyses(self, novel_id, chapter_id, dimension, limit=3):
        """获取前序章节的分析结果"""
        try:
            from src.models.chapter import Chapter
            from src.models.chapter_analysis_result import ChapterAnalysisResult
            from src.db.connection import Session

            session = Session()
            try:
                # 获取当前章节信息
                current_chapter = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.id == chapter_id
                ).first()

                if not current_chapter:
                    logger.error(f"找不到当前章节: novel_id={novel_id}, chapter_id={chapter_id}")
                    return []

                # 获取前序章节
                previous_chapters = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.chapter_number < current_chapter.chapter_number
                ).order_by(Chapter.chapter_number.desc()).limit(limit).all()

                if not previous_chapters:
                    logger.info(f"当前章节 {chapter_id} 是第一章或前面没有章节")
                    return []

                # 获取前序章节的分析结果
                previous_analyses = []
                for prev_chapter in previous_chapters:
                    analysis_result = session.query(ChapterAnalysisResult).filter(
                        ChapterAnalysisResult.novel_id == novel_id,
                        ChapterAnalysisResult.chapter_id == prev_chapter.id,
                        ChapterAnalysisResult.dimension == dimension
                    ).first()

                    if analysis_result:
                        content = analysis_result.content
                        reasoning_content = analysis_result.reasoning_content

                        previous_analyses.append({
                            "chapter_id": prev_chapter.id,
                            "chapter_number": prev_chapter.chapter_number,
                            "chapter_title": prev_chapter.title or f"第{prev_chapter.chapter_number}章",
                            "content": content,
                            "reasoning_content": reasoning_content
                        })

                logger.info(f"成功获取 {len(previous_analyses)} 个前序章节的分析结果")
                return previous_analyses
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取前序章节分析结果时出错: {str(e)}")
            return []

    async def build_chapter_analysis_prompt(self, dimension, chapter_content, chapter_title, previous_analyses=None, novel_title="", chapter_number=0):
        """构建章节分析提示词"""
        # 构建前序章节分析结果的摘要
        previous_analyses_summary = ""
        if previous_analyses:
            previous_analyses_summary = "## 前序章节分析摘要：\n\n"
            for i, prev_analysis in enumerate(previous_analyses):
                prev_chapter_title = prev_analysis.get("chapter_title", f"第{prev_analysis.get('chapter_number')}章")
                content = prev_analysis.get("content", "")
                reasoning_content = prev_analysis.get("reasoning_content", "")

                # 提取分析内容的关键点
                key_points = []

                # 尝试提取主要分析点
                analysis_points = re.findall(r'[一二三四五六七八九十]、([^一二三四五六七八九十\n]+)', content)
                if analysis_points:
                    key_points.extend([f"• {point.strip()}" for point in analysis_points[:3]])

                # 尝试提取小标题
                subtitles = re.findall(r'(?:###|[0-9]+\.) ([^\n]+)', content)
                if subtitles:
                    key_points.extend([f"• {subtitle.strip()}" for subtitle in subtitles[:3]])

                # 如果没有提取到结构化内容，使用前500字
                if not key_points:
                    content_summary = content[:500] + "..." if len(content) > 500 else content
                    previous_analyses_summary += f"### {prev_chapter_title} 分析摘要：\n{content_summary}\n\n"
                else:
                    # 使用提取的关键点
                    previous_analyses_summary += f"### {prev_chapter_title} 分析要点：\n"
                    for point in key_points:
                        previous_analyses_summary += f"{point}\n"
                    previous_analyses_summary += "\n"

                # 添加推理过程摘要
                if reasoning_content:
                    reasoning_summary = reasoning_content[:300] + "..." if len(reasoning_content) > 300 else reasoning_content
                    previous_analyses_summary += f"### {prev_chapter_title} 推理过程摘要：\n{reasoning_summary}\n\n"

            # 添加明确的连贯性分析指导
            previous_analyses_summary += """### 重要提示：
在分析本章节时，请特别注意与前序章节的连贯性和递进关系。你的分析必须：

1. 明确指出本章节如何延续和发展前序章节的情节、人物和主题
2. 分析本章节相比前序章节的新变化和发展
3. 识别贯穿各章节的核心元素和它们的演变
4. 分析作者在本章节使用的写作手法如何与前序章节形成呼应或对比
5. 评估本章节在整体故事发展中的作用和地位

你的分析应该形成一个连贯、递进的分析体系，而不是将本章节孤立对待。
"""

        # 添加章节间联系分析部分
        chapter_connection_section = ""
        if previous_analyses:
            chapter_connection_section = """
## 章节间联系与发展：
（这里详细分析本章节与前序章节的联系、变化和发展，必须明确指出本章节是如何延续、发展或转变前序章节的元素。分析必须具体且有深度，不能泛泛而谈。）

### 情节连贯性分析：
（分析本章节的情节如何承接前序章节，有哪些情节线索的延续和发展）

### 人物发展分析：
（分析主要人物在本章节相比前序章节有哪些变化和发展）

### 主题深化分析：
（分析本章节如何深化或转变前序章节建立的主题）

### 写作手法递进：
（分析本章节在写作手法上相比前序章节有哪些延续、变化或提升）
"""

        # 增强版提示模板
        enhanced_prompt_template = f"""你是一位经验丰富的文学分析专家，擅长分析各类小说文本。请对以下小说《{novel_title}》的第{chapter_number}章《{chapter_title}》进行详细的{dimension}分析。

请按照以下格式输出分析结果：

## 分析思路说明：
1. [分析方法1]：简要说明你将如何分析这个维度的第一个方面
2. [分析方法2]：简要说明你将如何分析这个维度的第二个方面
3. [分析方法3]：简要说明你将如何分析这个维度的第三个方面
...（根据具体维度可以有更多分析方法）

## 详细{dimension}分析：
（这里是详细的分析内容，包括多个小节和具体分析）

{chapter_connection_section}

## 重要分析指导：
1. 请使用通俗易懂的语言进行分析，避免过多专业术语和学术化表达
2. 每个分析点都应包含1-2个从文本中提取的具体例子（直接引用原文）
3. 对这些例子进行简明的解释说明，说明它们如何体现你的分析观点
4. 评估这些特点对读者体验的影响
5. 分析结论应该具体明确，避免空泛的评价
6. 绝对避免使用晦涩难懂的学术术语和概念，如"时空错位理论"、"系统干预层"等抽象概念
7. 使用日常生活中常见的比喻和例子来解释复杂概念
8. 分析应该像是在与普通读者交流，而不是学术论文
9. 在分析中要体现章节间的联系和发展脉络，不要将本章节孤立对待

请确保分析思路部分简明扼要，列出3-5点关键分析方法。详细分析部分则应该全面、深入、有条理，同时保持语言通俗易懂，多举具体例子。最终的分析结果应该是任何高中学历的读者都能轻松理解的内容。

{previous_analyses_summary if previous_analyses else ''}

文本：
{chapter_content}
"""

        return enhanced_prompt_template

    async def analyze_with_api(self, prompt):
        """调用API进行分析"""
        try:
            # 使用现有的analyze_text方法
            result = self.analyze_text(prompt, "chapter_analysis")
            return result
        except Exception as e:
            logger.error(f"调用API分析时出错: {str(e)}")
            return None

    def _get_previous_chapter_analysis(self, novel_id: int, chapter_id: int, dimension: str, limit: int = 2) -> list:
        """
        获取前面章节的分析结果，用于章节间信息联通

        Args:
            novel_id: 小说ID
            chapter_id: 当前章节ID
            dimension: 分析维度
            limit: 获取的前序章节数量

        Returns:
            前序章节的分析结果列表
        """
        if not novel_id or not chapter_id:
            logger.info(f"无法获取前序章节分析结果：缺少小说ID或章节ID")
            return []

        try:
            from src.models.chapter import Chapter
            from src.models.chapter_analysis_result import ChapterAnalysisResult
            from src.db.connection import Session

            with Session() as session:
                # 获取当前章节的信息
                current_chapter = session.query(Chapter).filter(Chapter.id == chapter_id, Chapter.novel_id == novel_id).first()
                if not current_chapter:
                    logger.error(f"获取前序章节分析结果失败：当前章节不存在 chapter_id={chapter_id}")
                    return []

                # 获取前面的章节
                previous_chapters = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.chapter_number < current_chapter.chapter_number
                ).order_by(Chapter.chapter_number.desc()).limit(limit).all()

                if not previous_chapters:
                    logger.info(f"当前章节 {chapter_id} 是第一章或前面没有章节")
                    return []

                # 获取前面章节的分析结果
                previous_analyses = []
                for prev_chapter in previous_chapters:
                    analysis_result = session.query(ChapterAnalysisResult).filter(
                        ChapterAnalysisResult.novel_id == novel_id,
                        ChapterAnalysisResult.chapter_id == prev_chapter.id,
                        ChapterAnalysisResult.dimension == dimension
                    ).first()

                    if analysis_result and analysis_result.result:
                        try:
                            result_data = json.loads(analysis_result.result) if isinstance(analysis_result.result, str) else analysis_result.result

                            # 提取分析结果内容
                            content = result_data.get("content", "")
                            if not content and hasattr(analysis_result, "content"):
                                content = analysis_result.content

                            previous_analyses.append({
                                "chapter_id": prev_chapter.id,
                                "chapter_number": prev_chapter.chapter_number,
                                "chapter_title": prev_chapter.title or f"第{prev_chapter.chapter_number}章",
                                "content": content
                            })
                        except Exception as e:
                            logger.error(f"解析前序章节分析结果时出错: {str(e)}")

                logger.info(f"成功获取 {len(previous_analyses)} 个前序章节的分析结果")
                return previous_analyses
        except Exception as e:
            logger.error(f"获取前序章节分析结果时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _create_analysis_prompt(self, text: str, analysis_type: str) -> str:
        """
        Create an analysis prompt based on the analysis type.

        Args:
            text: Text to analyze.
            analysis_type: Type of analysis to perform.

        Returns:
            Analysis prompt.
        """
        # 获取当前分析的小说ID和章节ID
        novel_id = getattr(self, 'current_novel_id', None)
        chapter_id = getattr(self, 'current_chapter_id', None)

        # 获取前序章节的分析结果
        previous_analyses = []
        if novel_id and chapter_id:
            previous_analyses = self._get_previous_chapter_analysis(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=analysis_type,
                limit=2  # 获取前2个章节的分析结果
            )

        # 构建前序章节分析结果的摘要
        previous_analyses_summary = ""
        if previous_analyses:
            previous_analyses_summary = "## 前序章节分析摘要：\n\n"
            for i, prev_analysis in enumerate(previous_analyses):
                chapter_title = prev_analysis.get("chapter_title", f"第{prev_analysis.get('chapter_number')}章")
                content = prev_analysis.get("content", "")

                # 提取分析内容的关键点
                # 尝试提取结构化内容
                key_points = []

                # 尝试提取主要分析点
                analysis_points = re.findall(r'[一二三四五六七八九十]、([^一二三四五六七八九十\n]+)', content)
                if analysis_points:
                    key_points.extend([f"• {point.strip()}" for point in analysis_points[:3]])

                # 尝试提取小标题
                subtitles = re.findall(r'(?:###|[0-9]+\.) ([^\n]+)', content)
                if subtitles:
                    key_points.extend([f"• {subtitle.strip()}" for subtitle in subtitles[:3]])

                # 如果没有提取到结构化内容，使用前500字
                if not key_points:
                    content_summary = content[:500] + "..." if len(content) > 500 else content
                    previous_analyses_summary += f"### {chapter_title} 分析摘要：\n{content_summary}\n\n"
                else:
                    # 使用提取的关键点
                    previous_analyses_summary += f"### {chapter_title} 分析要点：\n"
                    for point in key_points:
                        previous_analyses_summary += f"{point}\n"
                    previous_analyses_summary += "\n"

            # 添加明确的连贯性分析指导
            previous_analyses_summary += """### 重要提示：
在分析本章节时，请特别注意与前序章节的连贯性和递进关系。你的分析必须：

1. 明确指出本章节如何延续和发展前序章节的情节、人物和主题
2. 分析本章节相比前序章节的新变化和发展
3. 识别贯穿各章节的核心元素和它们的演变
4. 分析作者在本章节使用的写作手法如何与前序章节形成呼应或对比
5. 评估本章节在整体故事发展中的作用和地位

你的分析应该形成一个连贯、递进的分析体系，而不是将本章节孤立对待。
"""

            logger.info(f"成功构建结构化的前序章节分析摘要，包含 {len(previous_analyses)} 个章节")

        # 基础提示模板
        base_prompt = "你是一位经验丰富的文学分析专家，擅长分析各类小说文本。请对以下小说文本进行详细的{analysis_type}分析。"

        # 添加前序章节分析指导
        chapter_connection_section = ""
        if previous_analyses:
            chapter_connection_section = """
## 章节间联系与发展：
（这里详细分析本章节与前序章节的联系、变化和发展，必须明确指出本章节是如何延续、发展或转变前序章节的元素。分析必须具体且有深度，不能泛泛而谈。）

### 情节连贯性分析：
（分析本章节的情节如何承接前序章节，有哪些情节线索的延续和发展）

### 人物发展分析：
（分析主要人物在本章节相比前序章节有哪些变化和发展）

### 主题深化分析：
（分析本章节如何深化或转变前序章节建立的主题）

### 写作手法递进：
（分析本章节在写作手法上相比前序章节有哪些延续、变化或提升）
"""

        # 增强版提示模板，包含更详细的输出格式要求和通俗易懂的指导
        enhanced_prompt_template = """你是一位经验丰富的文学分析专家，擅长分析各类小说文本。请对以下小说文本进行详细的{analysis_type}分析。

请按照以下格式输出分析结果：

## 分析思路说明：
1. [分析方法1]：简要说明你将如何分析这个维度的第一个方面
2. [分析方法2]：简要说明你将如何分析这个维度的第二个方面
3. [分析方法3]：简要说明你将如何分析这个维度的第三个方面
...（根据具体维度可以有更多分析方法）

## 详细{analysis_type}分析：
（这里是详细的分析内容，包括多个小节和具体分析）

{chapter_connection_section}

## 重要分析指导：
1. 请使用通俗易懂的语言进行分析，避免过多专业术语和学术化表达
2. 每个分析点都应包含1-2个从文本中提取的具体例子（直接引用原文）
3. 对这些例子进行简明的解释说明，说明它们如何体现你的分析观点
4. 评估这些特点对读者体验的影响
5. 分析结论应该具体明确，避免空泛的评价
6. 绝对避免使用晦涩难懂的学术术语和概念，如"时空错位理论"、"系统干预层"等抽象概念
7. 使用日常生活中常见的比喻和例子来解释复杂概念
8. 分析应该像是在与普通读者交流，而不是学术论文
9. 在分析中要体现章节间的联系和发展脉络，不要将本章节孤立对待

请确保分析思路部分简明扼要，列出3-5点关键分析方法。详细分析部分则应该全面、深入、有条理，同时保持语言通俗易懂，多举具体例子。最终的分析结果应该是任何高中学历的读者都能轻松理解的内容。"""

        # 填充模板
        enhanced_prompt = enhanced_prompt_template.format(
            analysis_type=analysis_type,
            chapter_connection_section=chapter_connection_section
        )

        # 不同分析维度的提示词
        prompts = {
            "language_style": (
                f"{enhanced_prompt} 摘要: 请对以下小说文本进行逐字逐句的详细的语言风格分析。\n\n"
                f"请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n"
                f"{previous_analyses_summary if previous_analyses else ''}\n\n"
                f"文本：\n{text}"
            ),
            "rhythm_pacing": f"{enhanced_prompt} 摘要: 重点对以下小说文本进行节奏控制分析。包括段落长度、句式节奏、情节发展速度等方面。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "structure": f"{enhanced_prompt} 摘要: 重点对以下小说文本进行结构设计分析。章节安排、情节发展、高潮设置等。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "outline_analysis": f"{enhanced_prompt} 摘要: 请对以下小说文本进行详细的大纲分析。\n\n特别注意：\n1. 整体故事大纲与内容概述：全面详细地概述整部作品的故事内容，包括主要情节线、重要事件和人物发展。不要简单概括，而是要详细描述整个故事的发展过程，按照时间顺序或情节发展顺序进行叙述。确保覆盖所有关键情节点和转折点，让读者通过这部分内容就能完整了解整个故事讲述了什么。\n2. 章节结构与内容分布研究：详细分析作品的章节结构和内容分布。列出每个章节的主要内容和功能，评估章节之间的连贯性和平衡性。分析章节如何组织成更大的结构单元（如卷、部分或阶段），以及这些结构单元如何服务于整体叙事。\n3. 整体结构与架构设计评估：全面分析作品的整体结构与架构设计。评估三幕结构/五幕结构/英雄旅程等经典结构模式的应用情况。量化结构完整度和平衡性（开端15%/发展70%/结局15%）。分析结构创新点和变异处理。\n4. 情节线设计与管理考察：深入分析作品的情节线设计与管理。识别并详述主线与支线的数量、内容和层级关系。量化情节线的完整度和收束度。评估情节线交织技巧和节奏控制。分析各情节线如何服务于人物发展和主题表达。\n5. 叙事节奏与高潮设计研究：分析作品的叙事节奏与高潮设计。评估节奏变化的规律性和目的性。量化高潮分布密度和强度梯度。分析节奏控制技巧和高潮铺垫手法。评估节奏与高潮设计如何增强读者体验和情感投入。\n6. 人物系统与角色设计分析：全面分析作品的人物系统和角色设计。识别主要人物、次要人物和配角的功能和特点。评估人物形象的丰满度和一致性。分析人物关系网络的复杂性和动态变化。量化不同类型人物的分布比例。\n7. 人物弧线与成长设计评估：评估作品中人物弧线与成长设计。分析主要人物的起点、转折点和终点设置。量化人物变化的幅度和节奏。评估人物成长与情节发展的匹配度。分析人物弧线如何服务于主题表达和读者共鸣。\n8. 主题展开与深化策略研究：深入分析作品的主题展开与深化策略。识别核心主题和辅助主题的层级关系。量化主题元素的分布密度和强调程度。分析主题通过情节/人物/对话等不同载体的表达方式。评估主题展开的完整性和说服力。\n9. 世界观构建与背景设定考察：评估作品的世界观构建与背景设定。分析世界观元素的丰富度、一致性和原创性。量化世界观信息的揭示节奏和密度。评估世界观对故事氛围和读者沉浸感的影响。分析背景设定如何服务于情节发展和主题表达。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n请生成一个详细、全面的章节大纲，帮助读者理解所有章节的内容和发展。内容不限字数，越详细越好。对于每个章节，至少提供800字以上的详细描述，确保覆盖所有重要情节点和人物发展。\n\n文本：\n{text}",
            "sentence_variation": f"{enhanced_prompt} 摘要: 重点对以下小说文本进行句式结构分析来创造节奏感和表达不同的情感。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "paragraph_length": f"{enhanced_prompt} 摘要: 分析段落长度的变化及其对阅读体验和情感表达的影响。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "perspective_shifts": f"{enhanced_prompt} 摘要: 重点对以下小说文本进行视角转化分析，包括叙事视角的变化及其效果。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "paragraph_flow": f"{enhanced_prompt} 摘要: 重点对以下小说文本进行段落衔接术分析，分析段落之间的过渡和连接以及如何影响整体阅读流畅度。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "novel_characteristics": f"{enhanced_prompt} 摘要: 全面分析小说的风格、主题、写作技巧等特点。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "world_building": f"{enhanced_prompt} 摘要: 分析作者如何构建小说世界，包括环境描写、背景设定等。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "chapter_outline": f"{enhanced_prompt} 摘要: 请仔细分析以下小说文本，提取详细的章节结构和内容大纲。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "character_relationships": f"""{enhanced_prompt} 摘要: 分析小说中的人物关系网络，包括主要角色之间的互动和发展。

## 特别重要的人物关系分析指导：
1. 必须使用通俗易懂的日常语言，完全避免学术化、理论化的表达
2. 禁止使用任何晦涩难懂的学术术语，如"身份花拓学"、"系统干预层"、"时空错位理论"等抽象概念
3. 分析应该像是在与普通读者聊天，用简单直白的语言描述人物之间的关系
4. 使用具体的人物互动场景作为例子，而不是抽象的理论框架
5. 将复杂的关系用日常生活中常见的比喻来解释，如"像兄弟一样亲密"、"如同冤家路窄"等
6. 分析结果应该是任何高中学历的读者都能轻松理解的内容
7. 避免使用"红绿金铃构成权力关系的巴别塔系统"之类的晦涩表达
8. 不要使用"认知性意识"、"系统干预"等抽象术语
9. 不要创造不存在的学术理论或分析框架
10. 分析应该直接描述"谁和谁是什么关系，他们之间发生了什么，关系如何变化"

请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。

{previous_analyses_summary if previous_analyses else ''}

文本：
{text}""",
            "opening_effectiveness": f"{enhanced_prompt} 摘要: 分析小说开篇的效果，包括如何吸引读者注意力和设立基调。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "climax_pacing": f"{enhanced_prompt} 摘要: 分析小说中高潮情节的设置和节奏控制。 \n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}",
            "comprehensive_report": f"{enhanced_prompt} 请整合以下各个维度的分析，生成一份全面的小说分析报告。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n{text}",
            "chapter_outline": f"{enhanced_prompt} 摘要: 请仔细分析以下小说文本，提取详细的章节结构和内容大纲。\n\n请首先说明你的分析思路，然后进行详细分析。这部分内容将作为推理过程保存。\n\n{previous_analyses_summary if previous_analyses else ''}\n\n文本：\n{text}"
        }

        # 默认使用通用提示词
        return prompts.get(analysis_type, f"请对以下小说文本进行{analysis_type}分析：\n\n{text}")

    def _get_sample_response(self, analysis_type: str, text: str) -> Dict[str, Any]:
        """
        Generate a sample response for DEBUG mode.

        Args:
            analysis_type: Type of analysis.
            text: Text to analyze.

        Returns:
            Sample response.
        """
        # 获取文本的基本信息
        text_length = len(text)
        word_count = len(text.split())
        paragraph_count = len(text.split('\n\n'))

        # 根据分析类型生成不同的模拟响应
        if analysis_type == "language_style":
            content = f"""# 语言风格分析

## 概述
本文本长度为{text_length}字符，约{word_count}个词，{paragraph_count}个段落。整体语言风格简洁明了，富有表现力。

## 词汇选择
作者使用了丰富多样的词汇，包括大量描述性形容词和生动的动词。专业术语和行业词汇的使用恰到好处，增强了文本的专业性和可信度。

## 句式结构
句式变化丰富，长短句搭配合理。作者善于使用复杂句来表达复杂概念，同时也适时使用简短句子增强节奏感和强调重点。

## 修辞手法
文本中使用了多种修辞手法，包括比喻、拟人、排比等，使表达更加生动形象。

## 语气和基调
整体语气客观专业，同时在适当位置融入了个人观点，使文本既有权威性又有亲和力。

## 总结
该文本的语言风格专业而不失亲和力，技术性与可读性平衡得当，适合目标读者阅读和理解。"""
        elif analysis_type == "rhythm_pacing":
            content = f"""# 节奏与节奏分析

## 概述
本文本共{paragraph_count}个段落，整体节奏流畅，起伏有致。

## 段落长度
段落长度变化适中，短段落用于强调重点或转换话题，长段落用于详细说明或描述场景。

## 句子节奏
句子长度多样，平均每句约20-25个字。作者善于使用标点符号控制阅读节奏，逗号和句号的配置恰到好处。

## 情节发展速度
情节发展速度适中，重要情节展开从容，次要情节简洁明快。作者善于在关键时刻放慢节奏，增强戏剧性和张力。

## 高潮设置
文本中设置了多个小高潮和一个主要高潮，高潮前节奏逐渐加快，高潮后适当放缓，给读者留下思考和消化的空间。

## 总结
整体节奏把握得当，既有变化又保持连贯，能够有效吸引读者并保持阅读兴趣。"""
        else:
            # 通用模拟响应
            content = f"""# {analysis_type.replace('_', ' ').title()} 分析

## 概述
这是一个模拟的{analysis_type.replace('_', ' ')}分析结果，用于DEBUG模式测试。

## 文本基本信息
- 文本长度: {text_length}字符
- 词数: 约{word_count}个词
- 段落数: {paragraph_count}个段落

## 详细分析
这里是详细的{analysis_type.replace('_', ' ')}分析内容，在实际部署时会被真实的API分析结果替代。

## 总结
这是一个DEBUG模式下的模拟分析结果，仅用于测试系统功能和界面显示。"""

        # 返回模拟的API响应格式
        return {
            "type": analysis_type,
            "content": content
        }

    def _generate_combined_content(self, contents: List[str], analysis_type: str) -> str:
        """
        Generate combined content from multiple analysis results.

        Args:
            contents: List of analysis contents.
            analysis_type: Type of analysis.

        Returns:
            Combined content.
        """
        # 简单情况：如果只有一个块，直接返回
        if len(contents) == 1:
            return contents[0]

        # 使用模板创建组合内容
        combined = f"# {analysis_type.replace('_', ' ').title()} 分析报告\n\n"
        combined += "## 概述\n\n"
        combined += "以下是对完整小说的分析，基于多个文本片段的综合结果。\n\n"

        # 根据分析类型添加特定的章节
        if analysis_type == "language_style":
            combined += "## 语言风格特点\n\n"
            # 从各部分提取关键点
            # 这里实际上应该使用NLP技术提取要点，但为简化起见使用模拟方法

        elif analysis_type == "structure":
            combined += "## 整体结构\n\n"
            combined += "## 章节分析\n\n"
            combined += "## 情节发展\n\n"

        else:
            combined += "## 详细分析\n\n"

        # 我们不再使用DEBUG模式，直接进行API调用
        # 简单地包含每个块的部分内容，作为备用方案
        for i, content in enumerate(contents):
            # 提取每个内容的主要部分（跳过标题等）
            parts = content.split('\n\n')
            substantive_parts = parts[2:6] if len(parts) > 5 else parts  # 跳过标题和概述
            combined += f"### 文本片段 {i+1} 分析\n\n"
            combined += '\n\n'.join(substantive_parts) + "\n\n"

        combined += "## 总结\n\n"
        combined += "综合以上分析，本小说在" + analysis_type.replace('_', ' ') + "方面表现出以下特点...\n\n"

        # 对于非DEBUG模式，使用API进行一次整合分析
        try:
            # 创建整合提示
            integration_prompt = f"你是文学分析专家，请将以下关于{analysis_type.replace('_', ' ')}的分析结果整合为一个连贯的整体分析报告：\n\n"

            for i, content in enumerate(contents):
                integration_prompt += f"### 片段 {i+1} 分析:\n{content[:800]}...\n\n"  # 限制长度

            # 调用API进行整合
            integration_result = self.analyze_text(integration_prompt, f"integrate_{analysis_type}")

            if "error" not in integration_result:
                return integration_result.get("content", combined)  # 如果成功，返回整合结果

        except Exception as e:
            logger.error(f"整合分析结果时出错: {str(e)}")

        # 如果整合失败，返回简单组合的内容
        return combined

    def _split_text_into_chunks(self, text, max_chunk_size=5000, overlap=200):
        """
        将文本分割成合适大小的块，以便进行分析
        
        Args:
            text: 要分割的文本
            max_chunk_size: 每个块的最大字符数
            overlap: 块之间的重叠字符数，以保持上下文连贯性
            
        Returns:
            文本块列表
        """
        if not text:
            return []
            
        # 如果文本长度小于最大块大小，直接返回整个文本
        if len(text) <= max_chunk_size:
            return [text]
            
        chunks = []
        start = 0
        
        while start < len(text):
            # 计算当前块的结束位置
            end = min(start + max_chunk_size, len(text))
            
            # 如果不是最后一个块，尝试在适当的位置（句号、问号、感叹号等）切割
            if end < len(text):
                # 尝试在句子边界切割
                sentence_boundaries = ['.', '!', '?', '。', '！', '？', '\n\n']
                for i in range(end - 1, max(start + max_chunk_size // 2, start), -1):
                    if text[i] in sentence_boundaries:
                        end = i + 1  # 包含句子结束符
                        break
            
            # 添加当前块
            chunks.append(text[start:end])
            
            # 计算下一个块的起始位置，考虑重叠
            start = max(0, end - overlap)
        
        return chunks
        
    def _combine_chunk_results(self, chunk_results, dimension):
        """
        组合多个文本块的分析结果
        
        Args:
            chunk_results: 每个文本块的分析结果列表
            dimension: 分析维度
            
        Returns:
            合并后的分析结果
        """
        # 如果只有一个块，直接返回该块的结果
        if len(chunk_results) == 1:
            return chunk_results[0]
            
        # 提取每个块的内容
        contents = []
        for result in chunk_results:
            if result and "content" in result:
                contents.append(result["content"])
            else:
                logger.warning(f"块分析结果格式不正确，跳过: {result}")
                
        if not contents:
            logger.error("没有有效的块分析结果内容，无法合并")
            return {"content": "分析失败：没有有效的块分析结果"}
            
        # 专门处理章节大纲维度
        if dimension == "chapter_outline":
            return self._combine_chapter_outline_results(contents)
            
        # 使用_generate_combined_content方法组合内容
        combined_content = self._generate_combined_content(contents, dimension)
        
        return {
            "type": dimension,
            "content": combined_content
        }
        
    def _combine_chapter_outline_results(self, contents):
        """
        专门用于组合章节大纲分析的结果
        
        Args:
            contents: 章节大纲分析内容列表
            
        Returns:
            合并后的章节大纲分析结果
        """
        # 创建合并章节大纲的提示词
        combined_prompt = """请整合以下多个文本块的章节大纲分析结果，生成一个完整连贯的章节大纲分析：

## 重要指导：
1. 识别并合并相同章节的分析结果，避免重复
2. 对于被分割在不同文本块中的章节，将其内容完整地整合在一起
3. 确保章节的顺序正确，按照章节编号或顺序排列
4. 对于每个章节，提供详细的内容叙述、关键情节点和重要人物
5. 分析章节之间的连贯性和情节发展

以下是多个文本块的章节大纲分析结果：
"""
        
        # 添加每个块的分析结果，提供更多上下文
        for i, content in enumerate(contents):
            # 提取更多内容，确保主要内容部分能够完整保留
            content_preview = content[:8000] if len(content) > 8000 else content
            combined_prompt += f"\n\n## 文本块 {i+1} 分析结果:\n{content_preview}"
            
        # 添加输出格式指导
        combined_prompt += """

请按照以下格式整合上述分析结果：

# 章节大纲分析

## 整体结构
[提供小说的整体结构分析，包括章节数量、结构特点、主要情节线索等]

## 详细章节大纲
[按顺序列出每个章节的详细内容，格式如下]

### 第X章：[章节标题]
- **主要内容**：[详细描述本章的内容，包含场景描述、事件过程、人物对话和心理活动，以及情节转折和冲突]
- **关键情节点**：[列出本章的关键情节发展]
- **重要人物**：[列出本章出现的重要人物及其行动]
- **与整体故事的关系**：[分析本章在整体故事中的作用和地位]

## 章节间关系与发展
[分析章节之间的连贯性、情节发展和主题推进]

## 总体评价
[对小说章节结构的整体评价]
"""
        
        # 使用更大的max_tokens，确保能够生成完整的合并结果
        combined_result = self.analyze_text(combined_prompt, "combine_chapter_outline", max_tokens=10000)
        
        logger.info(f"章节大纲合并完成，结果长度: {len(combined_result.get('content', ''))}")
        
        return {
            "type": "chapter_outline",
            "content": combined_result.get("content", "无法合并章节大纲分析结果")
        }


# 提供一个独立的函数，供外部模块调用
def analyze_text(text: str, dimension: str, novel_title: str = None, max_tokens: int = None) -> Dict[str, Any]:
    """
    分析文本内容，创建DeepSeekClient实例并调用其analyze_text方法。

    Args:
        text: 要分析的文本内容
        dimension: 分析维度
        novel_title: 小说标题（可选）
        max_tokens: 最大生成令牌数（可选）

    Returns:
        分析结果字典
    """
    global _client_instance

    # 如果没有全局实例，创建一个
    if _client_instance is None:
        _client_instance = DeepSeekClient()
        logger.info(f"创建新的DeepSeekClient实例用于文本分析")

    # 记录分析信息
    logger.info(f"开始分析{'《'+novel_title+'》' if novel_title else '文本'} 的维度: {dimension}")
    logger.info(f"文本长度: {len(text)} 字符")

    # 调用客户端的analyze_text方法
    if len(text) > config.MAX_CHUNK_SIZE:
        # 对于长文本，使用分块分析
        logger.info(f"文本长度超过最大块大小 ({config.MAX_CHUNK_SIZE})，使用分块分析")
        result = _client_instance.analyze_novel_in_chunks(text, dimension)
    else:
        # 对于短文本，直接分析
        result = _client_instance.analyze_text(text, dimension, max_tokens)

    return result
