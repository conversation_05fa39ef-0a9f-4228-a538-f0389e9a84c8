"""
验证阿里云API密钥是否有效
"""
import requests
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 要测试的API密钥列表
API_KEYS = [
    "sk-9ff38bc35eaa44db82c4121bbba4f3b1",  # DeepSeek R1
    "***********************************",  # 通义千问
    "sk-68232283d4dc4216af3110885a2c985f"   # 旧的DeepSeek R1
]

def verify_api_key(api_key, model="deepseek-r1"):
    """验证API密钥是否有效"""
    logger.info(f"正在验证API密钥: {api_key[:6]}...")
    
    # 设置API端点
    if model == "deepseek-r1":
        endpoint = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    else:
        endpoint = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # 设置请求体 - 使用最小的请求
    if model == "deepseek-r1":
        payload = {
            "model": "deepseek-r1",
            "input": {
                "prompt": "Hello",
                "parameters": {
                    "max_tokens": 10
                }
            }
        }
    else:
        payload = {
            "model": model,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello"
                    }
                ]
            },
            "parameters": {
                "max_tokens": 10
            }
        }
    
    try:
        # 发送请求
        logger.info(f"发送请求到: {endpoint}")
        response = requests.post(endpoint, headers=headers, json=payload, timeout=30)
        
        # 检查响应状态码
        logger.info(f"响应状态码: {response.status_code}")
        
        # 尝试解析响应内容
        try:
            response_json = response.json()
            logger.info(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)[:500]}...")
            
            # 检查是否有错误信息
            if "error" in response_json:
                error = response_json["error"]
                logger.error(f"API错误: {error}")
                return False
            
            # 检查是否有输出
            if "output" in response_json:
                logger.info("API密钥有效!")
                return True
            else:
                logger.warning("响应中没有输出字段")
                return False
        except json.JSONDecodeError:
            logger.error(f"无法解析JSON响应: {response.text[:200]}...")
            return False
    except Exception as e:
        logger.error(f"验证API密钥时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始验证API密钥")
    
    for api_key in API_KEYS:
        # 测试DeepSeek R1模型
        logger.info(f"测试API密钥 {api_key[:6]}... 与 DeepSeek R1 模型")
        is_valid_deepseek = verify_api_key(api_key, "deepseek-r1")
        
        # 测试通义千问模型
        logger.info(f"测试API密钥 {api_key[:6]}... 与 通义千问 模型")
        is_valid_qwen = verify_api_key(api_key, "qwen-plus-latest")
        
        logger.info(f"API密钥 {api_key[:6]}... 结果: DeepSeek R1 - {'有效' if is_valid_deepseek else '无效'}, 通义千问 - {'有效' if is_valid_qwen else '无效'}")
    
    logger.info("API密钥验证完成")

if __name__ == "__main__":
    main()
