/**
 * 九猫 - 分析维度选择修复脚本
 * 这个脚本专门用于修复分析维度选择部分的问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析维度选择修复脚本已加载');

    // 配置
    const CONFIG = {
        debug: true,                 // 是否启用调试模式
        fixEmptyLabels: true,        // 是否修复空白标签
        fixMissingOptions: true,     // 是否修复缺失选项
        fixTimeout: 500,             // 修复超时时间（毫秒）
        retryInterval: 300,          // 重试间隔（毫秒）
        maxRetries: 5                // 最大重试次数
    };

    // 维度名称映射
    const DIMENSION_NAMES = {
        'language_style': '语言风格',
        'rhythm_pacing': '节奏与节奏',
        'structure': '结构分析',
        'sentence_variation': '句式变化',
        'paragraph_length': '段落长度',
        'perspective_shifts': '视角转换',
        'paragraph_flow': '段落流畅度',
        'novel_characteristics': '小说特点',
        'world_building': '世界构建',
        'chapter_outline': '章节大纲',
        'character_relationships': '人物关系',
        'opening_effectiveness': '开篇效果',
        'climax_pacing': '高潮节奏',
        'outline_analysis': '大纲分析',
        'popular_tropes': '热梗统计'
    };

    // 安全的日志函数
    function safeLog(message, level = 'log') {
        if (CONFIG.debug || level === 'error' || level === 'warn') {
            try {
                console[level](`[维度选择修复] ${message}`);
            } catch (e) {
                // 忽略日志错误
            }
        }
    }

    // 查找所有分析维度选择框
    function findDimensionCheckboxes() {
        try {
            const checkboxes = document.querySelectorAll('.dimension-checkbox');
            safeLog(`找到 ${checkboxes.length} 个分析维度选择框`);
            return checkboxes;
        } catch (e) {
            safeLog(`查找分析维度选择框时出错: ${e.message}`, 'error');
            return [];
        }
    }

    // 检查标签是否为空
    function isLabelEmpty(label) {
        try {
            return !label || !label.textContent || label.textContent.trim() === '';
        } catch (e) {
            safeLog(`检查标签是否为空时出错: ${e.message}`, 'error');
            return true;
        }
    }

    // 修复空白标签
    function fixEmptyLabel(checkbox) {
        try {
            const dimension = checkbox.value;
            if (!dimension) {
                safeLog('选择框没有维度值，无法修复', 'warn');
                return false;
            }

            safeLog(`修复维度 ${dimension} 的空白标签`);

            // 获取维度名称
            const dimensionName = DIMENSION_NAMES[dimension] || dimension;

            // 查找标签
            const label = checkbox.nextElementSibling;
            if (!label || !label.classList.contains('form-check-label')) {
                safeLog(`找不到维度 ${dimension} 的标签`, 'warn');

                // 尝试通过ID查找标签
                const id = checkbox.id;
                if (id) {
                    const labelById = document.querySelector(`label[for="${id}"]`);
                    if (labelById) {
                        labelById.textContent = dimensionName;
                        safeLog(`通过ID修复了维度 ${dimension} 的标签`);
                        return true;
                    }
                }

                // 如果找不到标签，创建一个新标签
                const newLabel = document.createElement('label');
                newLabel.className = 'form-check-label';
                newLabel.setAttribute('for', checkbox.id || `dimension-${dimension}`);
                newLabel.textContent = dimensionName;

                // 将新标签插入到选择框后面
                checkbox.parentNode.insertBefore(newLabel, checkbox.nextSibling);
                safeLog(`为维度 ${dimension} 创建了新标签`);
                return true;
            }

            // 更新标签文本
            label.textContent = dimensionName;
            safeLog(`修复了维度 ${dimension} 的标签`);
            return true;
        } catch (e) {
            safeLog(`修复空白标签时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 修复所有空白标签
    function fixAllEmptyLabels() {
        try {
            const checkboxes = findDimensionCheckboxes();
            let fixedCount = 0;

            checkboxes.forEach(checkbox => {
                const label = checkbox.nextElementSibling;
                if (isLabelEmpty(label)) {
                    if (fixEmptyLabel(checkbox)) {
                        fixedCount++;
                    }
                }
            });

            safeLog(`修复了 ${fixedCount} 个空白标签`);
            return fixedCount;
        } catch (e) {
            safeLog(`修复所有空白标签时出错: ${e.message}`, 'error');
            return 0;
        }
    }

    // 检查是否缺少维度选项
    function checkMissingDimensions() {
        try {
            const checkboxes = findDimensionCheckboxes();
            const existingDimensions = Array.from(checkboxes).map(checkbox => checkbox.value);
            const missingDimensions = [];

            // 检查是否有缺失的维度
            Object.keys(DIMENSION_NAMES).forEach(dimension => {
                if (!existingDimensions.includes(dimension)) {
                    missingDimensions.push(dimension);
                }
            });

            safeLog(`发现 ${missingDimensions.length} 个缺失的维度: ${missingDimensions.join(', ')}`);
            return missingDimensions;
        } catch (e) {
            safeLog(`检查缺失维度时出错: ${e.message}`, 'error');
            return [];
        }
    }

    // 添加缺失的维度选项
    function addMissingDimensions() {
        try {
            const missingDimensions = checkMissingDimensions();

            // 检查是否有任何维度选项
            const existingCheckboxes = document.querySelectorAll('.dimension-checkbox');
            if (existingCheckboxes.length === 0) {
                safeLog('没有找到任何维度选项，将创建所有维度选项');
                return createAllDimensionOptions();
            }

            if (missingDimensions.length === 0) {
                safeLog('没有缺失的维度选项');
                return 0;
            }

            // 查找维度选择容器
            const container = document.querySelector('.dimension-checkbox')?.closest('form') ||
                              document.querySelector('.modal-body') ||
                              document.querySelector('form');
            if (!container) {
                safeLog('找不到维度选择容器，将尝试创建所有维度选项', 'warn');
                return createAllDimensionOptions();
            }

            // 查找最后一个维度选择项
            const lastCheckbox = Array.from(document.querySelectorAll('.dimension-checkbox')).pop();
            if (!lastCheckbox) {
                safeLog('找不到最后一个维度选择项，将尝试创建所有维度选项', 'warn');
                return createAllDimensionOptions();
            }

            const lastCheckboxContainer = lastCheckbox.closest('.form-check');
            if (!lastCheckboxContainer) {
                safeLog('找不到最后一个维度选择项的容器，将尝试创建所有维度选项', 'warn');
                return createAllDimensionOptions();
            }

            // 添加缺失的维度选项
            let addedCount = 0;
            missingDimensions.forEach(dimension => {
                try {
                    // 创建新的选择项
                    const newCheckboxContainer = document.createElement('div');
                    newCheckboxContainer.className = 'form-check';

                    const newCheckbox = document.createElement('input');
                    newCheckbox.className = 'form-check-input dimension-checkbox';
                    newCheckbox.type = 'checkbox';
                    newCheckbox.name = 'dimensions';
                    newCheckbox.value = dimension;
                    newCheckbox.id = `dimension-${dimension}`;
                    newCheckbox.checked = true; // 默认选中

                    const newLabel = document.createElement('label');
                    newLabel.className = 'form-check-label';
                    newLabel.setAttribute('for', `dimension-${dimension}`);
                    newLabel.textContent = DIMENSION_NAMES[dimension] || dimension;

                    // 添加到容器中
                    newCheckboxContainer.appendChild(newCheckbox);
                    newCheckboxContainer.appendChild(newLabel);

                    // 插入到最后一个选择项后面
                    lastCheckboxContainer.parentNode.insertBefore(newCheckboxContainer, lastCheckboxContainer.nextSibling);

                    safeLog(`添加了维度 ${dimension} 的选择项`);
                    addedCount++;
                } catch (e) {
                    safeLog(`添加维度 ${dimension} 的选择项时出错: ${e.message}`, 'error');
                }
            });

            safeLog(`添加了 ${addedCount} 个缺失的维度选择项`);
            return addedCount;
        } catch (e) {
            safeLog(`添加缺失维度选项时出错: ${e.message}`, 'error');
            return 0;
        }
    }

    // 创建所有维度选项
    function createAllDimensionOptions() {
        try {
            safeLog('开始创建所有维度选项');

            // 查找模态框
            const modal = document.querySelector('.modal.show') ||
                          document.querySelector('.modal[aria-labelledby="analyzeModalLabel"]') ||
                          document.querySelector('.modal:has(.modal-title:contains("选择分析维度"))');

            if (!modal) {
                safeLog('找不到分析模态框', 'warn');
                return 0;
            }

            // 查找模态框内容区域
            const modalBody = modal.querySelector('.modal-body');
            if (!modalBody) {
                safeLog('找不到模态框内容区域', 'warn');
                return 0;
            }

            // 查找全选选项
            const selectAllContainer = modalBody.querySelector('.form-check');

            // 创建维度选项容器
            const dimensionsContainer = document.createElement('div');
            dimensionsContainer.className = 'dimensions-container mt-3';
            dimensionsContainer.style.maxHeight = '300px';
            dimensionsContainer.style.overflowY = 'auto';
            dimensionsContainer.style.border = '1px solid #dee2e6';
            dimensionsContainer.style.borderRadius = '0.25rem';
            dimensionsContainer.style.padding = '10px';

            // 添加所有维度选项
            let addedCount = 0;
            Object.keys(DIMENSION_NAMES).forEach(dimension => {
                try {
                    // 创建新的选择项
                    const newCheckboxContainer = document.createElement('div');
                    newCheckboxContainer.className = 'form-check mb-2';

                    const newCheckbox = document.createElement('input');
                    newCheckbox.className = 'form-check-input dimension-checkbox';
                    newCheckbox.type = 'checkbox';
                    newCheckbox.name = 'dimensions';
                    newCheckbox.value = dimension;
                    newCheckbox.id = `dimension-${dimension}`;
                    newCheckbox.checked = true; // 默认选中

                    const newLabel = document.createElement('label');
                    newLabel.className = 'form-check-label';
                    newLabel.setAttribute('for', `dimension-${dimension}`);
                    newLabel.textContent = DIMENSION_NAMES[dimension] || dimension;

                    // 添加到容器中
                    newCheckboxContainer.appendChild(newCheckbox);
                    newCheckboxContainer.appendChild(newLabel);
                    dimensionsContainer.appendChild(newCheckboxContainer);

                    addedCount++;
                } catch (e) {
                    safeLog(`创建维度 ${dimension} 的选择项时出错: ${e.message}`, 'error');
                }
            });

            // 将维度选项容器添加到模态框内容区域
            if (selectAllContainer) {
                // 如果有全选选项，将维度选项容器添加到全选选项后面
                selectAllContainer.parentNode.insertBefore(dimensionsContainer, selectAllContainer.nextSibling);
            } else {
                // 否则直接添加到模态框内容区域
                modalBody.appendChild(dimensionsContainer);
            }

            safeLog(`创建了 ${addedCount} 个维度选项`);
            return addedCount;
        } catch (e) {
            safeLog(`创建所有维度选项时出错: ${e.message}`, 'error');
            return 0;
        }
    }

    // 监听模态框打开
    function setupModalObserver() {
        try {
            // 创建一个新的MutationObserver
            const observer = new MutationObserver((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.type === 'attributes' &&
                        mutation.attributeName === 'class' &&
                        mutation.target.classList.contains('modal') &&
                        mutation.target.classList.contains('show')) {

                        safeLog('检测到模态框打开');

                        // 延迟一下，确保模态框内容已加载
                        setTimeout(() => {
                            if (mutation.target.textContent.includes('选择分析维度')) {
                                safeLog('检测到分析维度模态框打开');

                                // 执行修复
                                if (CONFIG.fixEmptyLabels) {
                                    fixAllEmptyLabels();
                                }

                                if (CONFIG.fixMissingOptions) {
                                    // 检查是否有任何维度选项
                                    const existingCheckboxes = document.querySelectorAll('.dimension-checkbox');
                                    if (existingCheckboxes.length === 0) {
                                        safeLog('没有找到任何维度选项，将创建所有维度选项');
                                        createAllDimensionOptions();
                                    } else {
                                        addMissingDimensions();
                                    }
                                }
                            }
                        }, 100);
                    }
                }
            });

            // 开始观察document.body的子节点变化
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class']
            });

            safeLog('模态框观察器已设置');
            return observer;
        } catch (e) {
            safeLog(`设置模态框观察器时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 监听分析按钮点击
    function setupButtonListeners() {
        try {
            document.addEventListener('click', (event) => {
                const target = event.target;
                if (target.matches('button, .btn') &&
                    (target.textContent.includes('分析') ||
                     target.id === 'analyzeBtn' ||
                     target.classList.contains('analyze-btn'))) {
                    safeLog('检测到分析按钮点击');

                    // 延迟一下，确保模态框已打开
                    setTimeout(() => {
                        // 执行修复
                        if (CONFIG.fixEmptyLabels) {
                            fixAllEmptyLabels();
                        }

                        if (CONFIG.fixMissingOptions) {
                            // 检查是否有任何维度选项
                            const existingCheckboxes = document.querySelectorAll('.dimension-checkbox');
                            if (existingCheckboxes.length === 0) {
                                safeLog('没有找到任何维度选项，将创建所有维度选项');
                                createAllDimensionOptions();
                            } else {
                                addMissingDimensions();
                            }
                        }
                    }, 300);
                }
            });

            safeLog('按钮监听器已设置');
        } catch (e) {
            safeLog(`设置按钮监听器时出错: ${e.message}`, 'error');
        }
    }

    // 在页面加载完成后执行修复
    function initFix() {
        safeLog('开始修复分析维度选择问题');

        // 设置模态框观察器
        setupModalObserver();

        // 设置按钮监听器
        setupButtonListeners();

        // 延迟执行，确保页面元素已加载
        setTimeout(() => {
            // 检查当前是否有打开的模态框
            const openModal = document.querySelector('.modal.show');
            if (openModal && openModal.textContent.includes('选择分析维度')) {
                safeLog('检测到已打开的分析维度模态框');

                // 执行修复
                if (CONFIG.fixEmptyLabels) {
                    fixAllEmptyLabels();
                }

                if (CONFIG.fixMissingOptions) {
                    // 检查是否有任何维度选项
                    const existingCheckboxes = document.querySelectorAll('.dimension-checkbox');
                    if (existingCheckboxes.length === 0) {
                        safeLog('没有找到任何维度选项，将创建所有维度选项');
                        createAllDimensionOptions();
                    } else {
                        addMissingDimensions();
                    }
                }
            }
        }, CONFIG.fixTimeout);
    }

    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initFix);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initFix();
    }

    // 导出函数供其他模块使用
    window.dimensionSelectionFix = {
        fixAllEmptyLabels: fixAllEmptyLabels,
        addMissingDimensions: addMissingDimensions,
        createAllDimensionOptions: createAllDimensionOptions
    };
})();
