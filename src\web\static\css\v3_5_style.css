/* 九猫小说分析写作系统v3.5 样式表 */

/* 全局样式 - 淡色主题 */
:root {
    /* 主题颜色 - 淡雅配色 */
    --primary-color: #6a8caf; /* 淡蓝色主色调 */
    --primary-dark: #4a6b8f; /* 深蓝色 */
    --primary-light: #8ba7c5; /* 浅蓝色 */
    --secondary-color: #a67c52; /* 淡棕色 */
    --secondary-light: #c4a68a; /* 浅棕色 */
    --success-color: #7cb07c; /* 淡绿色 */
    --info-color: #6a8caf; /* 淡蓝色 */
    --warning-color: #d9b778; /* 淡黄色 */
    --danger-color: #c57878; /* 淡红色 */
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    /* 背景颜色 */
    --body-bg: #f7f9fc; /* 淡蓝灰色背景 */
    --card-bg: #ffffff; /* 纯白色卡片 */
    --sidebar-bg: #f0f4f8; /* 侧边栏背景 */
    --footer-bg: #e9eff5; /* 页脚背景 */
    
    /* 文本颜色 */
    --text-color: #4a5568; /* 深灰色文本 */
    --text-light: #718096; /* 浅灰色文本 */
    --text-muted: #a0aec0; /* 更浅的灰色文本 */
    --text-white: #ffffff;
    
    /* 边框和阴影 */
    --border-color: #e2e8f0; /* 淡色边框 */
    --shadow-color: rgba(106, 140, 175, 0.1); /* 淡蓝色阴影 */
    --box-shadow: 0 2px 8px var(--shadow-color);
    
    /* 过渡效果 */
    --transition-speed: 0.3s;
    --transition: all 0.3s ease;
    
    /* 渐变 */
    --primary-gradient: linear-gradient(to right, var(--primary-color), var(--primary-light));
    --secondary-gradient: linear-gradient(to right, var(--secondary-color), var(--secondary-light));
}

/* 深色主题变量 */
body[data-theme="dark"] {
    --primary-color: #6a8caf;
    --primary-dark: #4a6b8f;
    --primary-light: #8ba7c5;
    --secondary-color: #a67c52;
    --secondary-light: #c4a68a;
    --body-bg: #1a202c;
    --card-bg: #2d3748;
    --sidebar-bg: #2d3748;
    --footer-bg: #2d3748;
    --text-color: #e2e8f0;
    --text-light: #a0aec0;
    --text-muted: #718096;
    --border-color: #4a5568;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

body {
    background-color: var(--body-bg);
    color: var(--text-color);
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    transition: background-color var(--transition-speed), color var(--transition-speed);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar {
    background-color: var(--card-bg);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.navbar-brand {
    font-weight: 600;
    color: var(--primary-color) !important;
}

.navbar-brand i {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

.nav-link {
    color: var(--text-color) !important;
    font-weight: 500;
    transition: var(--transition);
    border-radius: 4px;
    padding: 0.5rem 1rem !important;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background-color: rgba(106, 140, 175, 0.1);
}

.nav-link.active {
    color: var(--primary-color) !important;
    background-color: rgba(106, 140, 175, 0.15);
}

/* 卡片样式 */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 4px 12px var(--shadow-color);
    transform: translateY(-2px);
}

.card-header {
    background-color: rgba(106, 140, 175, 0.05);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--primary-color);
}

.card-footer {
    background-color: rgba(106, 140, 175, 0.05);
    border-top: 1px solid var(--border-color);
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 1.25rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-light);
    border-color: var(--secondary-light);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid var(--border-color);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(106, 140, 175, 0.25);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 4px;
}

.badge-primary {
    background-color: var(--primary-color);
}

.badge-secondary {
    background-color: var(--secondary-color);
}

/* 页脚样式 */
footer {
    background-color: var(--footer-bg);
    color: var(--text-color);
    padding: 2rem 0;
    margin-top: auto;
    border-top: 1px solid var(--border-color);
}

/* 控制台样式 */
.console-container {
    height: calc(100vh - 250px);
    min-height: 600px;
}

.console-nav {
    background-color: var(--sidebar-bg);
    border-bottom: 1px solid var(--border-color);
}

.console-nav .nav-link {
    color: var(--text-color);
    padding: 1rem;
    border-radius: 0;
    border-bottom: 3px solid transparent;
}

.console-nav .nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-bottom: 3px solid var(--primary-color);
}

.console-content {
    padding: 1.5rem;
    height: 100%;
    overflow-y: auto;
}

/* 文本对比样式 */
.comparison-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.text-comparison-row {
    display: flex;
    flex: 1;
    min-height: 300px;
}

.text-panel {
    flex: 1;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin: 0.5rem;
    background-color: var(--card-bg);
    overflow-y: auto;
}

.text-panel-header {
    font-weight: 600;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.highlight {
    background-color: rgba(214, 158, 46, 0.2);
    border-radius: 3px;
    padding: 2px 0;
}

.similarity-high {
    background-color: rgba(124, 176, 124, 0.3);
}

.similarity-medium {
    background-color: rgba(217, 183, 120, 0.3);
}

.similarity-low {
    background-color: rgba(197, 120, 120, 0.2);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .text-comparison-row {
        flex-direction: column;
    }
    
    .text-panel {
        margin: 0.5rem 0;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}
