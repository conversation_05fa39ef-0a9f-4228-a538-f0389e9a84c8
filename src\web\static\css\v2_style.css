/* 九猫小说分析系统v2.0 - 样式表 */

/* 全局样式 - 浅米黄色和淡白色主题 */
:root {
    --primary-color: #e6b422; /* 金黄色主色调 */
    --primary-dark: #c99a17; /* 深金黄色 */
    --primary-light: #f7d26e; /* 浅金黄色 */
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #fffdf5; /* 更淡的米黄色背景 */
    --card-bg: #ffffff; /* 纯白色卡片 */
    --text-color: #4a4a4a;
    --border-color: #f7f0d7; /* 更淡的边框颜色 */
    --shadow-color: rgba(230, 180, 34, 0.05);
    --footer-bg: #fffdf5;
    --transition-speed: 0.3s;
}

/* 深色主题变量 */
body[data-theme="dark"] {
    --primary-color: #e6b422; /* 保持金黄色 */
    --primary-dark: #c99a17;
    --primary-light: #f7d26e;
    --secondary-color: #495057;
    --body-bg: #2d2a20; /* 深棕色背景 */
    --card-bg: #3a3629; /* 深棕黄色卡片 */
    --text-color: #f0e0b2; /* 浅金色文字 */
    --border-color: #6b5c29; /* 深金色边框 */
    --shadow-color: rgba(230, 180, 34, 0.2);
    --footer-bg: #2a2620;
}

body {
    background-color: var(--body-bg);
    color: var(--text-color);
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    transition: background-color var(--transition-speed), color var(--transition-speed);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 10px var(--shadow-color);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-link:hover {
    transform: translateY(-2px);
}

/* 卡片样式 */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px var(--shadow-color);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 1.25rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--card-bg);
    border-top: 1px solid var(--border-color);
    padding: 0.75rem 1.25rem;
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-color: var(--primary-dark);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 表格样式 */
.table {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
}

.table thead th {
    background-color: var(--primary-light);
    color: var(--dark-color);
    border-color: var(--border-color);
}

.table-hover tbody tr:hover {
    background-color: var(--shadow-color);
}

/* 表单样式 */
.form-control, .form-select {
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(230, 180, 34, 0.25);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.5rem;
}

.badge-primary {
    background-color: var(--primary-color);
    color: white;
}

/* 警告框样式 */
.alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* 页脚样式 */
.footer {
    background-color: var(--footer-bg) !important;
    color: var(--text-color);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 0;
    margin-top: 2rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* 分析维度标签 */
.dimension-badge {
    display: inline-block;
    padding: 0.5em 0.85em;
    font-size: 0.8em;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
}

/* Markdown内容样式 */
.markdown-content {
    line-height: 1.7;
    font-size: 1.05rem;
}

.markdown-content h1, 
.markdown-content h2, 
.markdown-content h3, 
.markdown-content h4, 
.markdown-content h5, 
.markdown-content h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--primary-dark);
}

.markdown-content h1 {
    font-size: 1.8rem;
    border-bottom: 2px solid var(--primary-light);
    padding-bottom: 0.5rem;
}

.markdown-content h2 {
    font-size: 1.6rem;
    border-bottom: 1px solid var(--primary-light);
    padding-bottom: 0.3rem;
}

.markdown-content h3 {
    font-size: 1.4rem;
}

.markdown-content p {
    margin-bottom: 1rem;
}

.markdown-content ul, 
.markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.markdown-content blockquote {
    border-left: 4px solid var(--primary-light);
    padding-left: 1rem;
    margin-left: 0;
    color: var(--secondary-color);
}

.markdown-content pre {
    background-color: #f8f8f8;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
}

.markdown-content code {
    background-color: #f8f8f8;
    padding: 0.2rem 0.4rem;
    border-radius: 0.3rem;
    font-family: 'Courier New', Courier, monospace;
}

.markdown-content table {
    width: 100%;
    margin-bottom: 1rem;
    border-collapse: collapse;
}

.markdown-content table th,
.markdown-content table td {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
}

.markdown-content table th {
    background-color: var(--primary-light);
    font-weight: 600;
}

/* 分析结果容器 */
.analysis-container {
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px var(--shadow-color);
}

/* 推理过程容器 */
.reasoning-container {
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px var(--shadow-color);
    border-left: 4px solid var(--primary-color);
}

/* 章节列表样式 */
.chapter-list {
    list-style: none;
    padding: 0;
}

.chapter-list-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.chapter-list-item:hover {
    background-color: var(--shadow-color);
}

.chapter-list-item a {
    color: var(--text-color);
    text-decoration: none;
}

.chapter-list-item a:hover {
    color: var(--primary-color);
}

/* 章节分析汇总样式 */
.chapter-group {
    margin-bottom: 2rem;
}

.chapter-group-header {
    background-color: var(--primary-light);
    color: var(--dark-color);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem 0.5rem 0 0;
    font-weight: 600;
    cursor: pointer;
}

.chapter-group-body {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 0.5rem 0.5rem;
    padding: 1rem;
}

/* 加载中动画 */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
