{% extends "v3.1/base.html" %}

{% block title %}{{ chapter.title }} - {{ novel.title }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .chapter-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px var(--shadow-color);
        position: relative;
        overflow: hidden;
    }

    .chapter-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .chapter-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    }

    .chapter-meta {
        position: relative;
        z-index: 1;
        opacity: 0.9;
    }

    .chapter-content {
        background-color: var(--card-bg);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px var(--shadow-color);
        line-height: 1.8;
        white-space: pre-wrap;
        max-height: 500px;
        overflow-y: auto;
    }

    .dimension-card {
        transition: all 0.3s ease;
        height: 100%;
    }

    .dimension-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px var(--shadow-color);
    }

    .dimension-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .dimension-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .tab-content {
        padding: 1.5rem;
    }

    .chapter-navigation {
        display: flex;
        justify-content: space-between;
        margin-top: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 章节头部信息 -->
<div class="chapter-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">{{ chapter.title }}</h1>
            <div class="chapter-meta">
                <p class="lead mb-2">
                    <i class="fas fa-book me-2"></i>{{ novel.title }}
                </p>
                <p class="mb-0">
                    <i class="fas fa-user me-2"></i>{{ novel.author or '未知作者' }}
                </p>
            </div>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#startAnalysisModal">
                    <i class="fas fa-search me-1"></i>开始分析
                </button>
                <a href="{{ url_for('v3_1.view_novel', novel_id=novel.id) }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-1"></i>返回小说
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-12">
        <ul class="nav nav-tabs" id="chapterTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="true">
                    <i class="fas fa-file-alt me-1"></i><strong>章节内容</strong>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="dimensions-tab" data-bs-toggle="tab" data-bs-target="#dimensions" type="button" role="tab" aria-controls="dimensions" aria-selected="false">
                    <i class="fas fa-search me-1"></i><strong>分析维度</strong>
                </button>
            </li>
        </ul>
        <div class="tab-content" id="chapterTabContent">
            <!-- 章节内容标签页 -->
            <div class="tab-pane fade show active" id="content" role="tabpanel" aria-labelledby="content-tab">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h3 class="card-title mb-0"><i class="fas fa-file-alt me-2"></i>章节内容</h3>
                    </div>
                    <div class="card-body">
                        <div class="chapter-content">{{ chapter.content }}</div>
                    </div>
                </div>
            </div>

            <!-- 分析维度标签页 -->
            <div class="tab-pane fade" id="dimensions" role="tabpanel" aria-labelledby="dimensions-tab">
                <div class="row">
                    {% for dimension in dimensions %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card dimension-card shadow-sm">
                            <div class="card-body">
                                <div class="dimension-status">
                                    {% if dimension.key in available_dimensions %}
                                    <span class="badge bg-success">已完成</span>
                                    {% else %}
                                    <span class="badge bg-secondary">未分析</span>
                                    {% endif %}
                                </div>
                                <div class="text-center mb-3">
                                    <div class="dimension-icon text-primary">
                                        <i class="{{ dimension.icon|default('fas fa-search') }}"></i>
                                    </div>
                                    <h4>{{ dimension.name }}</h4>
                                </div>
                                <p class="text-muted">{{ dimension.description|truncate(100) }}</p>
                                <div class="text-center mt-3">
                                    {% if dimension.key in available_dimensions %}
                                    <a href="{{ url_for('v3_1.chapter_analysis', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" class="btn btn-primary">
                                        <i class="fas fa-eye me-1"></i>查看结果
                                    </a>
                                    {% else %}
                                    <button class="btn btn-outline-primary start-analysis-btn" data-dimension="{{ dimension.key }}">
                                        <i class="fas fa-search me-1"></i>开始分析
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 章节导航 -->
<div class="chapter-navigation">
    <div>
        {% set prev_chapter = None %}
        {% set next_chapter = None %}
        {% set found_current = False %}

        {% for ch in novel.chapters|sort(attribute='chapter_number') %}
            {% if found_current %}
                {% if not next_chapter %}
                    {% set next_chapter = ch %}
                {% endif %}
            {% elif ch.id == chapter.id %}
                {% set found_current = True %}
            {% else %}
                {% set prev_chapter = ch %}
            {% endif %}
        {% endfor %}

        {% if prev_chapter %}
        <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=prev_chapter.id) }}" class="btn btn-outline-primary">
            <i class="fas fa-chevron-left me-1"></i>上一章：{{ prev_chapter.title }}
        </a>
        {% else %}
        <button class="btn btn-outline-secondary" disabled>
            <i class="fas fa-chevron-left me-1"></i>已是第一章
        </button>
        {% endif %}
    </div>
    <div>
        {% if next_chapter %}
        <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=next_chapter.id) }}" class="btn btn-outline-primary">
            下一章：{{ next_chapter.title }}<i class="fas fa-chevron-right ms-1"></i>
        </a>
        {% else %}
        <button class="btn btn-outline-secondary" disabled>
            已是最后一章<i class="fas fa-chevron-right ms-1"></i>
        </button>
        {% endif %}
    </div>
</div>

<!-- 开始分析模态框 -->
<div class="modal fade" id="startAnalysisModal" tabindex="-1" aria-labelledby="startAnalysisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="startAnalysisModalLabel">选择分析维度</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    请选择要分析的维度，分析过程可能需要几分钟时间。
                </div>
                <div class="row">
                    {% for dimension in dimensions %}
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input class="form-check-input dimension-checkbox" type="checkbox" id="dimension_{{ dimension.key }}" value="{{ dimension.key }}" {% if dimension.key in available_dimensions %}disabled checked{% endif %}>
                            <label class="form-check-label" for="dimension_{{ dimension.key }}">
                                {{ dimension.name }}
                                {% if dimension.key in available_dimensions %}
                                <span class="badge bg-success ms-2">已完成</span>
                                {% endif %}
                            </label>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startAnalysisBtn">
                    <i class="fas fa-search me-1"></i>开始分析
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 激活当前标签页
        const hash = window.location.hash;
        if (hash) {
            $('#chapterTabs a[href="' + hash + '"]').tab('show');
        }

        // 点击标签页时更新URL
        $('#chapterTabs a').on('click', function(e) {
            window.location.hash = $(this).attr('href');
        });

        // 开始分析按钮点击事件
        $('.start-analysis-btn').click(function() {
            const dimension = $(this).data('dimension');
            // 选中对应的复选框
            $('#dimension_' + dimension).prop('checked', true);
            // 显示模态框
            $('#startAnalysisModal').modal('show');
        });

        // 模态框中的开始分析按钮点击事件
        $('#startAnalysisBtn').click(function() {
            // 获取选中的维度
            const dimensions = [];
            $('.dimension-checkbox:checked:not(:disabled)').each(function() {
                dimensions.push($(this).val());
            });

            if (dimensions.length === 0) {
                alert('请至少选择一个未分析的维度');
                return;
            }

            // 关闭模态框
            $('#startAnalysisModal').modal('hide');

            // 显示加载提示
            const loadingHtml = `
                <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                    <div class="card p-4 text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h5>正在开始分析，请稍候...</h5>
                    </div>
                </div>
            `;
            $('body').append(loadingHtml);

            // 发送AJAX请求
            $.ajax({
                url: '/api/v3.1/start_chapter_analysis',
                type: 'POST',
                data: JSON.stringify({
                    novel_id: {{ novel.id }},
                    chapter_id: {{ chapter.id }},
                    dimensions: dimensions
                }),
                contentType: 'application/json',
                success: function(response) {
                    // 移除加载提示
                    $('#loadingOverlay').remove();

                    if (response.success) {
                        alert('分析已开始，请稍后刷新页面查看结果');
                        // 刷新页面
                        location.reload();
                    } else {
                        alert('开始分析失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    // 移除加载提示
                    $('#loadingOverlay').remove();

                    alert('开始分析失败: ' + error);
                }
            });
        });
    });
</script>
{% endblock %}