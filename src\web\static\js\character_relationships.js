/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 人物关系分析模块
 */

// 全局错误处理函数
window.onerror = function(message, source, lineno, colno, error) {
    // 添加调试日志，验证参数是否正确传递
    console.log('Error Details:', { message, source, lineno, colno, error });
    
    // 确保 console.error 参数列表正确闭合
    console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
    
    return true; // 阻止默认错误处理
};

// 初始化人物关系分析
document.addEventListener('DOMContentLoaded', function() {
    console.log('人物关系分析模块已加载');
    
    // 检查是否在人物关系分析页面
    const isCharacterRelationshipsPage = window.location.pathname.includes('character_relationships');
    if (!isCharacterRelationshipsPage) {
        return;
    }
    
    console.log('当前页面是人物关系分析页面，初始化分析功能');
    
    // 初始化图表
    initializeCharts();
});

// 初始化图表
function initializeCharts() {
    // 确保Chart.js已加载
    if (typeof Chart === 'undefined') {
        console.error('Chart.js未加载，无法初始化图表');
        return;
    }
    
    console.log('初始化人物关系图表');
    
    try {
        // 获取图表容器
        const radarChart = document.getElementById('radarChart');
        const barChart = document.getElementById('barChart');
        
        if (!radarChart || !barChart) {
            console.warn('找不到图表容器，跳过图表初始化');
            return;
        }
        
        // 初始化雷达图
        new Chart(radarChart.getContext('2d'), {
            type: 'radar',
            data: {
                labels: ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'],
                datasets: [{
                    label: '人物关系分析评分',
                    data: [0, 0, 0, 0, 0], // 初始数据为0
                    fill: true,
                    backgroundColor: 'rgba(74, 107, 223, 0.2)',
                    borderColor: 'rgb(74, 107, 223)',
                    pointBackgroundColor: 'rgb(74, 107, 223)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgb(74, 107, 223)'
                }]
            },
            options: {
                elements: {
                    line: {
                        borderWidth: 3
                    }
                },
                scales: {
                    r: {
                        angleLines: {
                            display: true
                        },
                        suggestedMin: 0,
                        suggestedMax: 100
                    }
                }
            }
        });
        
        // 初始化柱状图
        new Chart(barChart.getContext('2d'), {
            type: 'bar',
            data: {
                labels: ['主要人物数', '次要人物数', '关系复杂度', '关系变化', '描写深度', '一致性'],
                datasets: [{
                    label: '人物关系分析指标',
                    data: [0, 0, 0, 0, 0, 0], // 初始数据为0
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(255, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(153, 102, 255, 0.2)',
                        'rgba(255, 159, 64, 0.2)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
        
        console.log('人物关系图表初始化完成');
    } catch (error) {
        console.error('初始化图表时出错:', error);
    }
}

// 更新图表数据
function updateCharts(data) {
    console.log('更新图表数据:', data);
    
    // 实现图表数据更新逻辑
}
