{"name": "@npmcli/fs", "version": "2.1.0", "description": "filesystem utilities for the npm cli", "main": "lib/index.js", "files": ["bin/", "lib/"], "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/fs.git"}, "keywords": ["npm", "oss"], "author": "GitHub Inc.", "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.1.2", "tap": "^15.1.6"}, "dependencies": {"@gar/promisify": "^1.1.3", "semver": "^7.3.5"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.1.2"}}