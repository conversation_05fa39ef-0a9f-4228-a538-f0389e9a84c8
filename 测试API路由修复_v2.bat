@echo off
echo 测试九猫v3.0章节删除API路由修复
echo.
echo 此脚本将测试章节删除API路由是否已修复
echo.

rem 设置环境变量
set API_ROUTE_FIX=True
set ENABLE_CHAPTER_DELETE=True

rem 测试主API路由
echo 测试主API路由...
curl -X POST http://localhost:5001/api/novel/48/chapter/282/analysis/language_style/delete
echo.
echo.

echo 测试别名API路由...
curl -X POST http://localhost:5001/api/novels/48/chapter/282/analysis/language_style/delete
echo.
echo.

echo 测试完成，请检查是否返回成功响应而不是404错误
echo.
echo 如果测试成功，请在浏览器中访问 http://localhost:5001/v3/novel/48/chapter/282 测试删除功能
echo.
pause
