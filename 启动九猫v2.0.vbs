' 九猫v2.0系统分析系统启动脚本
' 此脚本会猫v2.0系统，并自动打开浏览器

' 设置基本对象
S基对 = CaObj("ing.ieSysteObjct"
Set fso = CreateObject("Scripting.FileSystemell")

' 确保日志目录存在
On Error Resume N
End .WriteLine("    pause")
tempFile.WriteLine("    exit /b 1")
tempFi
FileWie保务
WshShell.Run "cmd /c start http://localhost:5001/", 0, False
显示启动消息
v2.0  "@cffemp检查yho是否可用...pyhon--vrsionif%RRORVENEQempchoyh未找到，请确保Pyho已安装并添加到P环境变量中   auexit/b1)
cho执行数据库修复...pyo -c"mr os sqli3, glob; t查找数据库...'); dbf []; fpan n ['n/*.db', '*db', 'daa/*.db']:fsexdglbg(ptrn); t'找到+tldfils))+'个数据库文件:'r(d_f);dpathindfilesrin('检查数据库+ db_pa); cnqli3.conncd_ph);csr= cnncusor);soxu('SEECT amROM q_maerWH y=''b'''; ab [[0] fo  cuo.fchal(]; t'表: ' + (abs)); fo abl ['chap_aysissu','anays_uls']: ifab  b:t'修复表: ' +b); cuo.xcu'PRAG ab_fo'+ ab+ ''); coum [c[1] fo c co.fchal]; f 'asog_cnn'ntncluns: n('添加列: rasong_到'+ tabl); cur.xcu'ALT TAB'+ab+'ADDCOLUMN rasing_ctTEXT'; conn.com; conn.cos; t'完成'"ne(" 00