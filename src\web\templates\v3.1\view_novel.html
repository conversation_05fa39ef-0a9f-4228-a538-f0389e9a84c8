{% extends "v3.1/base.html" %}

{% block title %}{{ novel.title }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .novel-header {
        background: linear-gradient(135deg, #FFF8E1, #FFFCF5);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
        border: 1px solid #E6D7B9;
    }

    .novel-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .novel-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        color: #5D4037;
    }

    .novel-header p {
        position: relative;
        z-index: 1;
        color: #5D4037;
    }

    .novel-meta {
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #E6D7B9;
    }

    .novel-meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .novel-meta-item i {
        width: 24px;
        margin-right: 0.5rem;
        color: #FF8F00;
    }

    .dimension-card {
        transition: all 0.3s ease;
        border: 1px solid #E6D7B9;
        background-color: #FFFFFF;
        height: 100%;
    }

    .dimension-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }

    .dimension-card .card-header {
        background-color: #FFF8E1;
        border-bottom: 1px solid #E6D7B9;
    }

    .dimension-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #FF8F00;
    }

    .dimension-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .btn-primary {
        background-color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-primary:hover {
        background-color: #F57C00;
        border-color: #F57C00;
    }

    .btn-outline-primary {
        color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-outline-primary:hover {
        background-color: #FF8F00;
        border-color: #FF8F00;
        color: white;
    }

    .progress {
        height: 0.5rem;
        background-color: #E6D7B9;
    }

    .progress-bar {
        background-color: #FF8F00;
    }

    .tab-content {
        padding: 1.5rem;
        background-color: #FFFFFF;
        border: 1px solid #E6D7B9;
        border-top: none;
        border-radius: 0 0 0.5rem 0.5rem;
    }

    .nav-tabs .nav-link {
        color: #5D4037;
        border: 1px solid transparent;
    }

    .nav-tabs .nav-link.active {
        color: #FF8F00;
        border-color: #E6D7B9;
        border-bottom-color: #FFFFFF;
        background-color: #FFFFFF;
    }

    .chapter-list {
        max-height: 500px;
        overflow-y: auto;
    }

    .chapter-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #E6D7B9;
        transition: all 0.3s ease;
    }

    .chapter-item:hover {
        background-color: #FFF8E1;
    }

    .chapter-item:last-child {
        border-bottom: none;
    }

    .chapter-number {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: #FFF8E1;
        color: #5D4037;
        font-weight: 600;
        margin-right: 1rem;
    }

    .chapter-title {
        flex: 1;
    }

    .badge.bg-primary {
        background-color: #FF8F00 !important;
    }

    .badge.bg-secondary {
        background-color: #757575 !important;
    }

    .badge.bg-success {
        background-color: #43A047 !important;
    }

    .badge.bg-warning {
        background-color: #FFB300 !important;
    }

    .badge.bg-info {
        background-color: #29B6F6 !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- 小说头部信息 -->
<div class="novel-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">{{ novel.title }}</h1>
            <p class="lead mb-2">
                {% if novel.author %}
                <i class="fas fa-user me-2"></i>{{ novel.author }}
                {% endif %}
            </p>
            <p class="mb-0">
                <span class="badge bg-primary me-2">{{ novel.word_count }} 字</span>
                <span class="badge bg-info me-2">{{ novel.chapter_count }} 章节</span>
                <span class="badge bg-success me-2">{{ novel.analyzed_dimensions|default([])|length }}/15 维度已分析</span>
                {% if novel.is_template %}
                <span class="badge bg-warning">参考蓝本</span>
                {% endif %}
            </p>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3_1.novels') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
                {% if not novel.is_template %}
                <button id="setAsTemplateBtn" class="btn btn-outline-warning">
                    <i class="fas fa-bookmark me-1"></i>设为参考蓝本
                </button>
                {% endif %}
                <a href="{{ url_for('v3_1.console') }}?novel_id={{ novel.id }}" class="btn btn-primary">
                    <i class="fas fa-terminal me-1"></i>控制台
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-md-4 mb-4">
        <!-- 小说元数据 -->
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>小说信息</h3>
            </div>
            <div class="card-body">
                <div class="novel-meta">
                    <div class="novel-meta-item">
                        <i class="fas fa-book"></i>
                        <span>标题: {{ novel.title }}</span>
                    </div>
                    {% if novel.author %}
                    <div class="novel-meta-item">
                        <i class="fas fa-user"></i>
                        <span>作者: {{ novel.author }}</span>
                    </div>
                    {% endif %}
                    <div class="novel-meta-item">
                        <i class="fas fa-font"></i>
                        <span>字数: {{ novel.word_count }}</span>
                    </div>
                    <div class="novel-meta-item">
                        <i class="fas fa-list-ol"></i>
                        <span>章节数: {{ novel.chapter_count }}</span>
                    </div>
                    <div class="novel-meta-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>创建时间: {{ novel.created_at.strftime('%Y-%m-%d %H:%M') if novel.created_at is not string else novel.created_at }}</span>
                    </div>
                    <div class="novel-meta-item">
                        <i class="fas fa-chart-pie"></i>
                        <span>分析维度: {{ novel.analyzed_dimensions|default([])|length }}/15</span>
                    </div>
                    <div class="novel-meta-item">
                        <i class="fas fa-bookmark"></i>
                        <span>参考蓝本: {% if novel.is_template %}是{% else %}否{% endif %}</span>
                    </div>
                </div>

                <div class="d-grid gap-2 mt-3">
                    <a href="{{ url_for('v3_1.chapters_list', novel_id=novel.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-1"></i>查看章节列表
                    </a>
                    <a href="{{ url_for('v3_1.view_templates', novel_id=novel.id) }}" class="btn btn-outline-info">
                        <i class="fas fa-cog me-1"></i>查看设定模板
                    </a>
                    {% if novel.is_template %}
                    <a href="{{ url_for('v3_1.console') }}?template_id={{ novel.id }}" class="btn btn-success">
                        <i class="fas fa-terminal me-1"></i>使用参考蓝本
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 分析进度 -->
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-tasks me-2"></i>分析进度</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5>整本书分析</h5>
                    <div class="progress mb-2">
                        <div class="progress-bar" role="progressbar" style="width: {{ (novel.analyzed_dimensions|default([])|length / 15) * 100 }}%;" aria-valuenow="{{ novel.analyzed_dimensions|default([])|length }}" aria-valuemin="0" aria-valuemax="15"></div>
                    </div>
                    <small class="text-muted">{{ novel.analyzed_dimensions|default([])|length }}/15 维度已分析</small>
                </div>

                <div>
                    <h5>章节分析</h5>
                    <div class="progress mb-2">
                        <div class="progress-bar" role="progressbar" style="width: {{ (novel.analyzed_chapters|default(0) / novel.chapter_count) * 100 if novel.chapter_count > 0 else 0 }}%;" aria-valuenow="{{ novel.analyzed_chapters|default(0) }}" aria-valuemin="0" aria-valuemax="{{ novel.chapter_count }}"></div>
                    </div>
                    <small class="text-muted">{{ novel.analyzed_chapters|default(0) }}/{{ novel.chapter_count }} 章节已分析</small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- 分析维度标签页 -->
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="novelTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="dimensions-tab" data-bs-toggle="tab" data-bs-target="#dimensions" type="button" role="tab" aria-controls="dimensions" aria-selected="true">
                            <i class="fas fa-chart-bar me-1"></i>分析维度
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">
                            <i class="fas fa-list me-1"></i>章节列表
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="false">
                            <i class="fas fa-file-alt me-1"></i>内容预览
                        </button>
                    </li>
                </ul>
            </div>
            <div class="tab-content" id="novelTabContent">
                <!-- 分析维度标签页内容 -->
                <div class="tab-pane fade show active" id="dimensions" role="tabpanel" aria-labelledby="dimensions-tab">
                    <div class="row">
                        {% for dimension in dimensions %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="dimension-card card h-100">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ dimension.name }}</h5>
                                    {% if dimension.key in novel.analyzed_dimensions|default([]) %}
                                    <span class="badge bg-success dimension-status">已分析</span>
                                    {% else %}
                                    <span class="badge bg-secondary dimension-status">未分析</span>
                                    {% endif %}
                                </div>
                                <div class="card-body text-center">
                                    <i class="{{ dimension.icon }} dimension-icon"></i>
                                    <p class="card-text">{{ dimension.description }}</p>
                                </div>
                                <div class="card-footer bg-transparent">
                                    {% if dimension.key in novel.analyzed_dimensions|default([]) %}
                                    <a href="{{ url_for('v3_1.dimension_detail', novel_id=novel.id, dimension=dimension.key) }}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-eye me-1"></i>查看分析
                                    </a>
                                    {% else %}
                                    <a href="{{ url_for('v3_1.analyze_dimension', novel_id=novel.id, dimension=dimension.key) }}" class="btn btn-primary w-100 analyze-btn" data-dimension="{{ dimension.key }}">
                                        <i class="fas fa-cogs me-1"></i>开始分析
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 章节列表标签页内容 -->
                <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                    <div class="chapter-list">
                        {% if chapters %}
                            {% for chapter in chapters %}
                            <div class="chapter-item d-flex align-items-center">
                                <div class="chapter-number">{{ chapter.chapter_number }}</div>
                                <div class="chapter-title">
                                    <h5 class="mb-0">{{ chapter.title }}</h5>
                                    <small class="text-muted">{{ chapter.word_count }} 字</small>
                                </div>
                                <div>
                                    <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>查看
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-list fa-3x text-muted mb-3"></i>
                                <h4>暂无章节</h4>
                                <p class="text-muted">该小说尚未分章节或章节信息不可用。</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- 内容预览标签页内容 -->
                <div class="tab-pane fade" id="content" role="tabpanel" aria-labelledby="content-tab">
                    <div class="content-preview">
                        {% if novel.content %}
                            <div style="max-height: 500px; overflow-y: auto; white-space: pre-wrap; font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; line-height: 1.8;">
                                {{ novel.content[:2000] }}
                                {% if novel.content|length > 2000 %}
                                <div class="text-center mt-3">
                                    <span class="badge bg-secondary">内容过长，仅显示前2000字</span>
                                </div>
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <h4>内容不可用</h4>
                                <p class="text-muted">该小说内容不可用或尚未加载。</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 设为参考蓝本按钮点击事件
        $('#setAsTemplateBtn').click(function() {
            if (confirm('确定要将"{{ novel.title }}"设为参考蓝本吗？\n\n只有完成所有15个维度分析的小说才能设为参考蓝本。')) {
                // 显示加载状态
                const originalText = $(this).html();
                $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>处理中...');
                $(this).prop('disabled', true);

                // 发送AJAX请求
                $.ajax({
                    url: '/v3.1/api/novel/{{ novel.id }}/set_as_template',
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            // 成功设置为参考蓝本
                            alert('成功将"{{ novel.title }}"设为参考蓝本！');
                            // 刷新页面
                            location.reload();
                        } else {
                            // 设置失败
                            alert('设置参考蓝本失败: ' + response.message);
                            // 恢复按钮状态
                            $('#setAsTemplateBtn').html(originalText);
                            $('#setAsTemplateBtn').prop('disabled', false);
                        }
                    },
                    error: function(xhr) {
                        // 请求错误
                        let errorMessage = '设置参考蓝本失败';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage += ': ' + response.message;
                            }
                        } catch (e) {
                            errorMessage += '，请确保所有15个维度都已分析完成。';
                        }
                        alert(errorMessage);
                        // 恢复按钮状态
                        $('#setAsTemplateBtn').html(originalText);
                        $('#setAsTemplateBtn').prop('disabled', false);
                    }
                });
            }
        });

        // 开始分析按钮点击事件
        $('.analyze-btn').click(function(e) {
            e.preventDefault();
            const dimension = $(this).data('dimension');
            const url = $(this).attr('href');

            // 显示确认对话框
            if (confirm(`确定要开始分析"${dimension}"维度吗？\n\n分析过程可能需要几分钟时间，请耐心等待。`)) {
                // 显示加载状态
                const originalText = $(this).html();
                $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...');
                $(this).prop('disabled', true);

                // 跳转到分析页面
                window.location.href = url;
            }
        });

        // 标签页切换事件
        $('#novelTab button').on('shown.bs.tab', function (e) {
            // 如果切换到章节列表标签页，加载章节列表
            if ($(e.target).attr('id') === 'chapters-tab') {
                // 这里可以添加动态加载章节列表的代码
                console.log('章节列表标签页已激活');
            }
        });

        // 检查URL中的tab参数，如果存在则激活对应的标签页
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab');
        if (tab) {
            // 激活对应的标签页
            $(`#novelTab button[data-bs-target="#${tab}"]`).tab('show');
        }
    });
</script>
{% endblock %}