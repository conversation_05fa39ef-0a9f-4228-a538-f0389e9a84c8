/**
 * 九猫 - 综合修复脚本
 * 整合所有修复脚本，确保正确的加载顺序
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('综合修复脚本已加载');

    // 需要加载的脚本列表，按顺序加载
    const scripts = [
        { name: '日志过滤', path: '/static/js/logging-filter.js', backup: '/direct-static/js/logging-filter.js' },
        { name: '优化静态加载', path: '/static/js/optimized-static-loader.js', backup: '/direct-static/js/optimized-static-loader.js' },
        { name: 'Unexpected identifier修复', path: '/static/js/unexpected-identifier-fix.js', backup: '/direct-static/js/unexpected-identifier-fix.js' },
        { name: '统一图表修复', path: '/static/js/consolidated-chart-fix.js', backup: '/direct-static/js/consolidated-chart-fix.js' }
    ];

    // 已加载的脚本计数
    let loadedCount = 0;

    // 加载脚本
    function loadScript(script, callback) {
        const scriptElement = document.createElement('script');
        scriptElement.src = script.path;

        scriptElement.onload = function() {
            console.log(`${script.name}脚本已加载`);
            callback(null, script);
        };

        scriptElement.onerror = function() {
            console.warn(`${script.path} 加载失败，尝试备用路径`);
            scriptElement.src = script.backup;

            scriptElement.onload = function() {
                console.log(`${script.name}脚本已从备用路径加载`);
                callback(null, script);
            };

            scriptElement.onerror = function() {
                console.error(`${script.backup} 加载失败，无法加载 ${script.name}脚本`);
                callback(new Error(`无法加载 ${script.name}脚本`), script);
            };
        };

        document.head.appendChild(scriptElement);
    }

    // 按顺序加载脚本
    function loadScriptsSequentially(index) {
        if (index >= scripts.length) {
            console.log('所有修复脚本已加载完成');
            return;
        }

        loadScript(scripts[index], function(error, script) {
            if (error) {
                console.error(`加载 ${script.name}脚本时出错:`, error);
            }

            // 无论成功还是失败，都继续加载下一个脚本
            loadScriptsSequentially(index + 1);
        });
    }

    // 开始加载脚本
    loadScriptsSequentially(0);

    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        // 检查是否是脚本加载错误
        if (event.target && event.target.tagName === 'SCRIPT') {
            console.warn(`脚本加载错误: ${event.target.src}`);

            // 检查是否是我们的修复脚本
            for (const script of scripts) {
                if (event.target.src === script.path) {
                    console.log(`尝试从备用路径加载 ${script.name}脚本`);

                    // 创建新的脚本元素
                    const newScript = document.createElement('script');
                    newScript.src = script.backup;
                    document.head.appendChild(newScript);

                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }
    }, true);
})();
