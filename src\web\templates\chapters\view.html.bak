{% extends "base.html" %}

{% block title %}{{ novel.title }} - 第{{ chapter.chapter_number }}章{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('novel.list_novels') }}">小说列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('novel.view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('chapter.list_chapters', novel_id=novel.id) }}">章节列表</a></li>
            <li class="breadcrumb-item active" aria-current="page">第{{ chapter.chapter_number }}章</li>
        </ol>
    </nav>

    <div class="row">
        <!-- 左侧：章节内容 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h2>{{ chapter.title or '第' + chapter.chapter_number|string + '章' }}</h2>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p><strong>章节编号：</strong>{{ chapter.chapter_number }}</p>
                        <p><strong>字数：</strong>{{ chapter.word_count }}</p>
                    </div>
                    <div class="chapter-content">
                        {% for paragraph in chapter.content.split('\n') %}
                            {% if paragraph.strip() %}
                                <p>{{ paragraph }}</p>
                            {% else %}
                                <br>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：分析结果 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>分析结果</h2>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                            分析章节
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 分析进度条 -->
                    <div id="analysisProgressContainer" class="mb-4" style="display: none;">
                        <h4>分析进度</h4>
                        <div class="progress mb-2">
                            <div id="analysisProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                        <div id="analysisStatus" class="text-muted">准备中...</div>
                        <div class="mt-2">
                            <button id="cancelAnalysisBtn" class="btn btn-sm btn-danger">取消分析</button>
                        </div>
                    </div>

                    <!-- 分析结果列表 -->
                    <div class="list-group">
                        {% for dimension in dimensions %}
                            {% set result = analysis_results.get(dimension.key) %}
                            <a href="{{ url_for('chapter.view_chapter_analysis', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) if result else '#' }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {{ 'disabled' if not result }}">
                                {{ dimension.name }}
                                {% if result %}
                                    <span class="badge bg-success">已分析</span>
                                {% else %}
                                    <span class="badge bg-secondary">未分析</span>
                                {% endif %}
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析模态框 -->
<div class="modal fade" id="analyzeModal" tabindex="-1" aria-labelledby="analyzeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analyzeModalLabel">分析章节</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="analyzeForm">
                    <div class="mb-3">
                        <label for="dimensionSelect" class="form-label">选择分析维度</label>
                        <select class="form-select" id="dimensionSelect" required>
                            {% for dimension in dimensions %}
                            <option value="{{ dimension.key }}">{{ dimension.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="modelSelect" class="form-label">选择分析模型</label>
                        <select class="form-select" id="modelSelect">
                            <option value="deepseek-r1">DeepSeek R1</option>
                            <option value="qwen-plus-latest">通义千问-Plus-Latest</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startAnalysisBtn">开始分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const novelId = {{ novel.id }};
        const chapterId = {{ chapter.id }};
        let analysisInterval = null;
        
        // 开始分析按钮
        document.getElementById('startAnalysisBtn').addEventListener('click', function() {
            const dimension = document.getElementById('dimensionSelect').value;
            const model = document.getElementById('modelSelect').value;
            
            if (!dimension) {
                alert('请选择分析维度');
                return;
            }
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('analyzeModal'));
            modal.hide();
            
            // 显示进度条
            document.getElementById('analysisProgressContainer').style.display = 'block';
            document.getElementById('analysisProgressBar').style.width = '0%';
            document.getElementById('analysisProgressBar').setAttribute('aria-valuenow', 0);
            document.getElementById('analysisProgressBar').textContent = '0%';
            document.getElementById('analysisStatus').textContent = '正在分析...';
            
            // 发送请求
            fetch(`/api/novel/${novelId}/chapter/${chapterId}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dimension: dimension,
                    model: model
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新进度条
                    document.getElementById('analysisProgressBar').style.width = '100%';
                    document.getElementById('analysisProgressBar').setAttribute('aria-valuenow', 100);
                    document.getElementById('analysisProgressBar').textContent = '100%';
                    document.getElementById('analysisStatus').textContent = '分析完成';
                    document.getElementById('analysisProgressBar').classList.remove('progress-bar-animated');
                    document.getElementById('analysisProgressBar').classList.remove('progress-bar-striped');
                    document.getElementById('analysisProgressBar').classList.add('bg-success');
                    
                    // 2秒后刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    // 更新进度条
                    document.getElementById('analysisProgressBar').style.width = '100%';
                    document.getElementById('analysisProgressBar').setAttribute('aria-valuenow', 100);
                    document.getElementById('analysisProgressBar').textContent = '失败';
                    document.getElementById('analysisStatus').textContent = '分析失败: ' + data.error;
                    document.getElementById('analysisProgressBar').classList.remove('progress-bar-animated');
                    document.getElementById('analysisProgressBar').classList.remove('progress-bar-striped');
                    document.getElementById('analysisProgressBar').classList.add('bg-danger');
                }
            })
            .catch(error => {
                console.error('分析章节时出错:', error);
                
                // 更新进度条
                document.getElementById('analysisProgressBar').style.width = '100%';
                document.getElementById('analysisProgressBar').setAttribute('aria-valuenow', 100);
                document.getElementById('analysisProgressBar').textContent = '错误';
                document.getElementById('analysisStatus').textContent = '分析出错';
                document.getElementById('analysisProgressBar').classList.remove('progress-bar-animated');
                document.getElementById('analysisProgressBar').classList.remove('progress-bar-striped');
                document.getElementById('analysisProgressBar').classList.add('bg-danger');
            });
        });
        
        // 取消分析按钮
        document.getElementById('cancelAnalysisBtn').addEventListener('click', function() {
            document.getElementById('analysisProgressContainer').style.display = 'none';
        });
    });
</script>
{% endblock %}
