"""
直接测试分析过程记录功能，不依赖环境变量
"""
import os
import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

# 强制设置环境变量
os.environ['ENABLE_DETAILED_PROCESS_RECORDING'] = 'True'
os.environ['SAVE_FULL_API_INTERACTIONS'] = 'True'
os.environ['SAVE_PROMPTS'] = 'True'
os.environ['ENHANCED_LOG_CATEGORIES'] = 'True'
os.environ['RECORD_INTERMEDIATE_RESULTS'] = 'True'
os.environ['PROCESS_RECORDING_DEBUG'] = 'True'
os.environ['PROCESS_RECORDING_VERBOSE'] = 'True'
os.environ['FORCE_PROCESS_RECORDING'] = 'True'

# 导入必要的模块
try:
    from src.db.connection import Session
    from src.models.analysis_process import AnalysisProcess
    from src.models.novel import Novel
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    sys.exit(1)

# 定义ProcessRecorder类，不依赖外部模块
class DirectProcessRecorder:
    """直接的分析过程记录器，不依赖环境变量"""

    @staticmethod
    def record_process(novel_id, dimension, block_index, total_blocks, stage,
                      input_text=None, output_text=None, prompt=None,
                      api_request=None, api_response=None,
                      processing_time=None, tokens=None,
                      is_successful=True, error_message=None,
                      result_id=None, metadata=None):
        """记录分析过程"""
        try:
            session = Session()

            # 创建分析过程记录
            process = AnalysisProcess(
                novel_id=novel_id,
                dimension=dimension,
                block_index=block_index,
                total_blocks=total_blocks,
                processing_stage=stage,
                result_id=result_id,
                input_text=input_text,
                output_text=output_text,
                prompt_used=prompt,
                api_request=api_request,
                api_response=api_response,
                processing_time=processing_time,
                tokens_used=tokens,
                is_successful=is_successful,
                error_message=error_message,
                metadata=metadata or {}
            )

            session.add(process)
            session.commit()

            process_id = process.id
            logger.info(f"已记录分析过程: ID={process_id}, 小说ID={novel_id}, 维度={dimension}, 阶段={stage}")

            session.close()
            return process_id

        except Exception as e:
            logger.error(f"记录分析过程时出错: {str(e)}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return None

def check_database_connection():
    """检查数据库连接是否正常"""
    try:
        session = Session()
        # 尝试查询一条记录
        novel = session.query(Novel).first()
        if novel:
            logger.info(f"数据库连接正常，找到小说: ID={novel.id}, 标题={novel.title}")
        else:
            logger.warning("数据库连接正常，但没有找到任何小说")
        session.close()
        return True, novel.id if novel else None
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False, None

def check_existing_processes():
    """检查数据库中是否存在分析过程记录"""
    try:
        session = Session()
        # 查询分析过程记录总数
        total_count = session.query(AnalysisProcess).count()
        logger.info(f"数据库中共有 {total_count} 条分析过程记录")

        # 查询最近的5条记录
        recent_processes = session.query(AnalysisProcess).order_by(AnalysisProcess.id.desc()).limit(5).all()
        logger.info(f"最近的 {len(recent_processes)} 条分析过程记录:")
        for process in recent_processes:
            logger.info(f"  ID={process.id}, 小说ID={process.novel_id}, 维度={process.dimension}, 阶段={process.processing_stage}, 时间={process.created_at}")

        session.close()
        return total_count > 0
    except Exception as e:
        logger.error(f"查询分析过程记录失败: {e}")
        return False

def create_test_process_record(novel_id):
    """创建一条测试分析过程记录"""
    if not novel_id:
        logger.error("没有提供小说ID，无法创建测试记录")
        return False

    try:
        # 创建测试记录
        logger.info(f"尝试为小说ID={novel_id}创建测试分析过程记录")
        process_id = DirectProcessRecorder.record_process(
            novel_id=novel_id,
            dimension="test_dimension",
            block_index=0,
            total_blocks=1,
            stage="test",
            input_text="测试输入文本",
            output_text="测试输出文本",
            prompt="测试提示词",
            processing_time=100,
            tokens=50,
            metadata={"test": True, "timestamp": datetime.now().isoformat()}
        )

        if process_id:
            logger.info(f"成功创建测试记录，ID={process_id}")
            return True
        else:
            logger.error("创建测试记录失败")
            return False
    except Exception as e:
        logger.error(f"创建测试记录时出错: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始直接测试分析过程记录功能")

    # 检查数据库连接
    db_ok, novel_id = check_database_connection()
    if not db_ok:
        logger.error("数据库连接失败，无法继续测试")
        return

    # 检查现有记录
    has_records = check_existing_processes()
    if has_records:
        logger.info("数据库中已存在分析过程记录")
    else:
        logger.warning("数据库中没有找到任何分析过程记录")

    # 创建测试记录
    test_ok = create_test_process_record(novel_id)
    if test_ok:
        logger.info("测试成功，分析过程记录功能正常工作")
    else:
        logger.error("测试失败，分析过程记录功能不正常")

    logger.info("测试完成")

if __name__ == "__main__":
    main()
