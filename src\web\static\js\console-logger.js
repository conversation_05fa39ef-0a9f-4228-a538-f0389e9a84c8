/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 控制台日志显示模块
 */

// 全局变量，用于存储最后一次获取的日志时间戳
// 使用window对象来避免重复声明
window.consoleLogger = window.consoleLogger || {};
window.consoleLogger.lastLogTimestamp = '';
window.consoleLogger.isPollingLogs = false;
window.consoleLogger.logPollingInterval = null;
window.consoleLogger.currentNovelId = null;
window.consoleLogger.currentLogLevel = 'all';
window.consoleLogger.autoScroll = true;
// 添加变量跟踪上次日志更新时间
window.consoleLogger.lastUpdateTime = 0;
window.consoleLogger.hasAddedPlaceholder = false;

// 初始化控制台日志功能
function initConsoleLogger(novelId, novelTitle) {
    console.log('初始化控制台日志功能，小说ID: ' + novelId + ', 标题: ' + novelTitle);
    window.consoleLogger.currentNovelId = novelId;

    // 重置占位消息状态
    window.consoleLogger.hasAddedPlaceholder = false;

    // 清空控制台
    clearConsole();

    // 添加欢迎信息
    addConsoleMessage('// 九猫分析控制台已准备就绪，等待分析开始...', 'welcome');

    // 如果提供了小说标题，则显示标题，否则显示ID
    if (novelTitle) {
        addConsoleMessage('// 当前小说: 《' + novelTitle + '》', 'welcome');
    } else {
        // 尝试获取小说标题
        fetchNovelTitle(novelId).then(title => {
            // 清除之前的ID行
            const consoleInner = document.querySelector('.console-inner');
            const idLine = Array.from(consoleInner.querySelectorAll('.console-line.log-welcome')).find(
                line => line.textContent.includes('当前小说')
            );
            if (idLine) {
                idLine.innerHTML = '// 当前小说: 《' + title + '》';
            } else {
                addConsoleMessage('// 当前小说: 《' + title + '》', 'welcome');
            }
        }).catch(error => {
            console.error('获取小说标题失败:', error);
            addConsoleMessage('// 当前小说ID: ' + novelId, 'welcome');
        });
    }

    addConsoleMessage('// 分析开始后将在此显示实时日志', 'welcome');

    // 绑定控制台相关事件
    bindConsoleEvents();

    // 开始轮询日志
    startLogPolling();
}

// 获取小说标题
async function fetchNovelTitle(novelId) {
    try {
        const response = await fetch('/api/novels/' + novelId);
        if (!response.ok) {
            throw new Error('HTTP error! status: ' + response.status);
        }
        const data = await response.json();
        return data.title || '未知标题 (ID: ' + novelId + ')';
    } catch (error) {
        console.error('获取小说标题时出错:', error);
        return '未知标题 (ID: ' + novelId + ')';
    }
}

// 绑定控制台相关事件
function bindConsoleEvents() {
    // 日志级别选择
    const logLevelSelect = document.getElementById('console-log-level');
    if (logLevelSelect) {
        logLevelSelect.addEventListener('change', function() {
            window.consoleLogger.currentLogLevel = this.value;
            console.log('日志级别已更改为: ' + window.consoleLogger.currentLogLevel);
            // 清空控制台并重新获取日志
            clearConsole();
            addConsoleMessage('// 日志级别已更改为: ' + window.consoleLogger.currentLogLevel, 'welcome');
            fetchLogs(true);
        });
    }

    // 清空控制台按钮
    const clearConsoleBtn = document.getElementById('clear-console');
    if (clearConsoleBtn) {
        clearConsoleBtn.addEventListener('click', function() {
            clearConsole();
            addConsoleMessage('// 控制台已清空', 'welcome');
        });
    }

    // 复制控制台按钮
    const copyConsoleBtn = document.getElementById('copy-console');
    if (copyConsoleBtn) {
        copyConsoleBtn.addEventListener('click', function() {
            copyConsoleContent();
        });
    }
}

// 开始轮询日志
function startLogPolling() {
    if (window.consoleLogger.isPollingLogs) {
        console.log('日志轮询已在进行中，跳过');
        return;
    }

    window.consoleLogger.isPollingLogs = true;
    console.log('开始轮询分析日志');

    // 立即获取一次日志
    fetchLogs(true);

    // 设置轮询间隔
    window.consoleLogger.logPollingInterval = setInterval(function() {
        fetchLogs(false);
    }, 2000); // 每2秒轮询一次，提高日志更新频率
}

// 停止轮询日志
function stopLogPolling() {
    if (!window.consoleLogger.isPollingLogs) {
        return;
    }

    window.consoleLogger.isPollingLogs = false;
    console.log('停止轮询分析日志');

    if (window.consoleLogger.logPollingInterval) {
        clearInterval(window.consoleLogger.logPollingInterval);
        window.consoleLogger.logPollingInterval = null;
    }
}

// 获取分析日志
function fetchLogs(isInitial = false) {
    if (!window.consoleLogger.currentNovelId) {
        console.error('未设置小说ID，无法获取日志');
        return;
    }

    // 构建URL
    const timestamp = new Date().getTime();
    let url = '/api/analysis/logs?novel_id=' + window.consoleLogger.currentNovelId + '&level=' + window.consoleLogger.currentLogLevel + '&_=' + timestamp;

    // 添加is_initial参数
    url += '&is_initial=' + isInitial;

    // 限制返回数量
    const limit = isInitial ? 100 : 50;  // 增加返回数量
    url += '&limit=' + limit;

    // 添加上次获取的时间戳，以便只获取新日志
    if (window.consoleLogger.lastLogTimestamp && !isInitial) {
        url += '&since=' + encodeURIComponent(window.consoleLogger.lastLogTimestamp);
    }

    // 只在开发模式下输出日志URL
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log(`获取日志: ${url}`);
    }

    // 发送请求
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 只有在实际有日志时才输出日志信息
                if (data.logs && data.logs.length > 0) {
                    console.log('获取到 ' + data.logs.length + ' 条日志');
                    // 更新日志时间戳
                    window.consoleLogger.lastUpdateTime = Date.now();
                    window.consoleLogger.hasAddedPlaceholder = false;
                } else {
                    // 没有日志，只打印调试信息
                    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                        console.log('没有新日志');
                    }
                }

                // 如果返回了小说标题，更新控制台欢迎信息
                if (data.novel_title && isInitial) {
                    // 查找并更新小说ID/标题行
                    const consoleInner = document.querySelector('.console-inner');
                    const idLine = Array.from(consoleInner.querySelectorAll('.console-line.log-welcome')).find(
                        line => line.textContent.includes('当前小说')
                    );
                    if (idLine) {
                        idLine.innerHTML = '// 当前小说: 《' + data.novel_title + '》';
                    }
                }

                // 处理日志数据（只有在有日志时才处理）
                if (data.logs && data.logs.length > 0) {
                    processLogs(data.logs, isInitial);
                }

                // 更新时间戳
                window.consoleLogger.lastLogTimestamp = data.timestamp;

                // 仅在有实际日志时才确保轮询继续
                if ((data.logs.length > 0 || data.is_running) && !window.consoleLogger.isPollingLogs) {
                    startLogPolling();
                }
            } else {
                console.error('获取日志失败:', data.error);
            }
        })
        .catch(error => {
            console.error('获取日志时出错:', error);

            // 检查是否已经显示了错误消息
            const consoleInner = document.querySelector('.console-inner');
            if (consoleInner) {
                const errorLines = Array.from(consoleInner.querySelectorAll('.console-line.log-error')).filter(
                    line => line.textContent.includes('获取日志失败')
                );

                // 如果没有显示过错误消息，则显示
                if (errorLines.length === 0) {
                    const timestamp = new Date().toLocaleTimeString();
                    const errorMsg = document.createElement('div');
                    errorMsg.className = 'console-line log-error';
                    errorMsg.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> 获取日志失败: ${error.message}。将在5秒后重试...`;
                    consoleInner.appendChild(errorMsg);

                    // 滚动到底部
                    if (window.consoleLogger.autoScroll) {
                        scrollConsoleToBottom();
                    }
                }
            }

            // 5秒后重试
            setTimeout(() => {
                console.log('尝试重新获取日志...');
                fetchLogs(false);
            }, 5000);
        });
}

// 处理日志数据
function processLogs(logs, isInitial) {
    if (!logs || logs.length === 0) {
        // 不显示任何占位消息，完全静默
        return;
    }

    // 如果是初始加载，清空控制台
    if (isInitial) {
        clearConsole(false);
        addConsoleMessage('// 加载历史日志...', 'welcome');
    }

    // 按时间正序排序
    logs.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // 添加日志到控制台
    logs.forEach(log => {
        const timestamp = new Date(log.timestamp).toLocaleTimeString();
        let message = log.message;
        const level = log.level || 'info';
        const dimension = log.dimension;
        const progress = log.progress;
        const important = log.important || false;

        // 过滤掉不必要的等待信息
        if (!important && level !== 'error' && level !== 'warning') {
            const waitMessages = [
                "等待中", "请稍候", "初始化中", "准备中", "队列中",
                "正在等待", "即将开始", "日志将很快显示"
            ];

            if (waitMessages.some(waitMsg => message.includes(waitMsg))) {
                // 在调试模式下记录这些消息
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                    console.log('过滤掉等待消息:', message);
                }
                return;
            }
        }

        // 检查是否是重要的分析信息
        let isImportant = important;
        if (!isImportant) {
            const importantKeywords = [
                "API调用完成", "分析完成", "处理时间", "令牌使用量", "费用",
                "分析结果", "进度更新", "分析块", "完成度", "%", "耗时"
            ];

            if (importantKeywords.some(keyword => message.includes(keyword))) {
                isImportant = true;
            }
        }

        // 调试信息
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('处理日志消息:', message, '重要:', isImportant);
        }

        // 构建日志消息
        let logMessage = '';

        // 添加时间戳
        logMessage += '<span class="log-timestamp">[' + timestamp + ']</span> ';

        // 添加维度信息
        if (dimension) {
            logMessage += '<span class="log-dimension">[' + dimension + ']</span> ';
        }

        // 添加进度信息
        if (progress !== null && progress !== undefined) {
            logMessage += '<span class="log-progress">[' + progress + '%]</span> ';
        }

        // 如果是重要信息，添加突出显示
        if (isImportant) {
            // 根据不同类型的重要信息添加不同的样式
            let className = 'font-weight-bold';

            if (message.includes('API调用完成') || message.includes('分析完成')) {
                className += ' text-success';
            } else if (message.includes('处理时间') || message.includes('耗时')) {
                className += ' text-info';
            } else if (message.includes('令牌使用量') || message.includes('费用')) {
                className += ' text-primary';
            } else if (message.includes('%') || message.includes('进度更新')) {
                className += ' text-primary';
            }

            message = '<span class="' + className + '">' + message + '</span>';
        }

        // 添加消息内容
        logMessage += message;

        // 添加到控制台
        addConsoleMessage(logMessage, level);

        // 如果是重要信息，更新控制台标题
        if (isImportant) {
            // 提取纯文本内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = message;
            const textContent = tempDiv.textContent || tempDiv.innerText || message;
            updateConsoleTitle(textContent);
        }
    });

    // 滚动到底部
    if (window.consoleLogger.autoScroll) {
        scrollConsoleToBottom();
    }
}

// 添加消息到控制台
function addConsoleMessage(message, level) {
    const consoleInner = document.querySelector('.console-inner');
    if (!consoleInner) {
        console.error('找不到控制台内部元素');
        return;
    }

    // 不再过滤任何消息，确保所有日志都能显示
    // 只记录调试信息
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('处理控制台消息:', message);
    }

    const logLine = document.createElement('div');
    logLine.className = `console-line log-${level}`;
    logLine.innerHTML = message;

    consoleInner.appendChild(logLine);

    // 更新控制台标题，显示最新消息
    updateConsoleTitle(message);
}

// 更新控制台标题，显示最新消息
function updateConsoleTitle(message) {
    // 提取纯文本内容
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = message;
    const textContent = tempDiv.textContent || tempDiv.innerText || message;

    const consoleTitle = document.querySelector('.console-title');
    if (consoleTitle) {
        // 如果消息太长，截断它
        const maxLength = 50;
        let shortMessage = textContent;
        if (textContent.length > maxLength) {
            shortMessage = textContent.substring(0, maxLength) + '...';
        }

        // 更新标题
        consoleTitle.innerHTML = '分析控制台 <small class="text-muted">最新: ' + shortMessage + '</small>';
    }
}

// 清空控制台
function clearConsole(resetWelcome = true) {
    const consoleInner = document.querySelector('.console-inner');
    if (!consoleInner) {
        console.error('找不到控制台内部元素');
        return;
    }

    consoleInner.innerHTML = '';

    // 添加欢迎信息
    if (resetWelcome) {
        addConsoleMessage('// 控制台已清空', 'welcome');
        addConsoleMessage('// 分析日志将实时显示在这里', 'welcome');
        addConsoleMessage('// 如果看不到日志，请检查浏览器控制台(F12)是否有错误信息', 'welcome');
    }
}

// 滚动控制台到底部
function scrollConsoleToBottom() {
    const consoleOutput = document.getElementById('analysis-console');
    if (consoleOutput) {
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }
}

// 复制控制台内容
function copyConsoleContent() {
    const consoleInner = document.querySelector('.console-inner');
    if (!consoleInner) {
        console.error('找不到控制台内部元素');
        return;
    }

    // 获取纯文本内容
    const textContent = Array.from(consoleInner.querySelectorAll('.console-line'))
        .map(line => line.textContent)
        .join('\n');

    // 复制到剪贴板
    navigator.clipboard.writeText(textContent)
        .then(() => {
            console.log('控制台内容已复制到剪贴板');
            // 显示提示
            const copyBtn = document.getElementById('copy-console');
            if (copyBtn) {
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '已复制!';
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                }, 2000);
            }
        })
        .catch(err => {
            console.error('复制失败:', err);
        });
}

// 添加日志函数（供外部调用）
// 确保 window.consoleLogger 对象已初始化
window.consoleLogger = window.consoleLogger || {};
window.consoleLogger.addLog = function(message, level, timestamp, dimension, progress) {
    // 创建日志对象
    const log = {
        message: message,
        level: level || 'info',
        timestamp: timestamp || new Date().toISOString(),
        dimension: dimension,
        progress: progress
    };

    // 处理单条日志
    processLogs([log], false);
};

// 导出函数
window.initConsoleLogger = initConsoleLogger;
window.startLogPolling = startLogPolling;
window.stopLogPolling = stopLogPolling;
window.clearConsole = clearConsole;
window.copyConsoleContent = copyConsoleContent;
