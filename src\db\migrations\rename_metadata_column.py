"""
重命名presets表中的metadata列为meta_info
"""
import logging
import sqlite3

logger = logging.getLogger(__name__)

def run_migration():
    """重命名presets表中的metadata列为meta_info"""
    try:
        # 连接数据库
        conn = sqlite3.connect('novels.db')
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='presets'")
        if not cursor.fetchone():
            logger.info("presets表不存在，无需迁移")
            conn.close()
            return True
        
        # 检查列是否存在
        cursor.execute("PRAGMA table_info(presets)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # 如果metadata列存在，但meta_info列不存在，则重命名列
        if 'metadata' in column_names and 'meta_info' not in column_names:
            logger.info("开始重命名presets表中的metadata列为meta_info")
            
            # SQLite不支持直接重命名列，需要创建新表并复制数据
            cursor.execute('''
            CREATE TABLE presets_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                category TEXT NOT NULL DEFAULT 'other',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                meta_info TEXT
            )
            ''')
            
            # 复制数据
            cursor.execute('''
            INSERT INTO presets_new (id, title, content, category, created_at, updated_at, meta_info)
            SELECT id, title, content, category, created_at, updated_at, metadata
            FROM presets
            ''')
            
            # 删除旧表
            cursor.execute('DROP TABLE presets')
            
            # 重命名新表
            cursor.execute('ALTER TABLE presets_new RENAME TO presets')
            
            # 创建索引
            cursor.execute('CREATE INDEX idx_presets_category ON presets(category)')
            cursor.execute('CREATE INDEX idx_presets_created_at ON presets(created_at)')
            
            # 提交事务
            conn.commit()
            
            logger.info("presets表中的metadata列已重命名为meta_info")
        elif 'meta_info' in column_names:
            logger.info("presets表中已有meta_info列，无需迁移")
        else:
            logger.info("presets表中没有metadata列，无需迁移")
        
        # 关闭连接
        conn.close()
        
        return True
    except Exception as e:
        logger.error(f"重命名presets表中的metadata列时出错: {str(e)}", exc_info=True)
        return False
