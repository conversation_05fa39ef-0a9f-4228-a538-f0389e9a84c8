
/* 基础样式 */
body { font-family: system-ui, sans-serif; line-height: 1.5; margin: 0; padding: 0; }
.container { width: 100%; max-width: 1200px; margin: 0 auto; padding: 0 15px; }
.navbar { display: flex; align-items: center; padding: 8px 16px; background-color: #343a40; color: white; }
.navbar-brand { font-size: 1.25rem; color: white; text-decoration: none; }
.card { border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin-bottom: 15px; }
.btn { display: inline-block; padding: 8px 16px; background: #f8f9fa; border: 1px solid #dee2e6; cursor: pointer; border-radius: 4px; }
.btn-primary { color: #fff; background-color: #007bff; border-color: #007bff; }
h1, h2, h3, h4, h5, h6 { margin-top: 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
