/**
 * API路径修复脚本
 * 用于修复九猫系统中的API路径问题
 * 版本: 1.0.0
 * 最后更新: 2023-05-25
 */

(function() {
    console.log('[API路径修复] 初始化...');

    // API路径映射
    const apiPathMap = {
        // 基本路径修复
        '/api/novels/': '/api/novel/',
        '/v3.1/api/': '/api/',
        '/v3.5/api/': '/api/',

        // 特定端点修复
        '/api/novel/template/': '/api/template/',
        '/api/novel/analysis/': '/api/analysis/',

        // 章节相关路径修复
        '/template/structure': '/structure',
        '/template/paragraph_length': '/paragraph_length',
        '/template/perspective_shifts': '/perspective_shifts',
        '/template/sentence_variation': '/sentence_variation',
        '/template/novel_characteristics': '/novel_characteristics',

        // 分析维度路径修复
        '/analysis/character_analysis': '/character_analysis',
        '/analysis/plot_analysis': '/plot_analysis',
        '/analysis/emotion_analysis': '/emotion_analysis',
        '/analysis/climax_analysis': '/climax_analysis',
        '/analysis/style_analysis': '/style_analysis',
        '/analysis/theme_analysis': '/theme_analysis',
        '/analysis/chapter_outline': '/chapter_outline',
        '/analysis/hot_meme': '/hot_meme',
        '/analysis/popular_tropes': '/popular_tropes'
    };

    // 原始的jQuery.ajax方法
    let originalAjax = null;

    // 修复API路径
    function fixApiPath(url) {
        if (!url || typeof url !== 'string') return url;

        let fixedUrl = url;

        // 特殊处理模板相关端点
        if (fixedUrl.includes('/novel/') &&
            (fixedUrl.includes('/template/perspective_shifts') ||
             fixedUrl.includes('/template/sentence_variation') ||
             fixedUrl.includes('/template/novel_characteristics') ||
             fixedUrl.includes('/template/structure') ||
             fixedUrl.includes('/template/paragraph_length'))) {

            // 提取模板ID
            const match = fixedUrl.match(/\/novel\/(\d+)\/template\//);
            if (match && match[1]) {
                const templateId = match[1];
                const endpoint = fixedUrl.split('/template/')[1];
                fixedUrl = `/api/template/${templateId}/${endpoint}`;
                console.log(`[API路径修复] 特殊修复模板路径: ${url} -> ${fixedUrl}`);
            }
        }
        // 应用路径映射
        else {
            for (const [oldPath, newPath] of Object.entries(apiPathMap)) {
                if (fixedUrl.includes(oldPath)) {
                    console.log(`[API路径修复] 修复路径: ${fixedUrl} -> ${fixedUrl.replace(oldPath, newPath)}`);
                    fixedUrl = fixedUrl.replace(oldPath, newPath);
                }
            }
        }

        // 特殊处理: 移除重复的斜杠
        fixedUrl = fixedUrl.replace(/\/+/g, '/').replace(':/', '://');

        return fixedUrl;
    }

    // 修复所有API路径
    function fixAllApiPaths() {
        if (typeof jQuery === 'undefined' || typeof jQuery.ajax !== 'function') {
            console.error('[API路径修复] jQuery或jQuery.ajax不可用，无法修复API路径');
            return false;
        }

        // 保存原始的ajax方法
        if (!originalAjax) {
            originalAjax = jQuery.ajax;
        }

        // 重写ajax方法
        jQuery.ajax = function(url, options) {
            // 处理不同的参数形式
            if (typeof url === 'object') {
                options = url;
                url = options.url;
            } else if (typeof options === 'object') {
                url = options.url || url;
            }

            // 修复URL
            const fixedUrl = fixApiPath(url);

            // 更新URL
            if (typeof options === 'object') {
                options.url = fixedUrl;
            } else {
                url = fixedUrl;
            }

            // 调用原始方法
            return originalAjax.apply(this, typeof options === 'object' ? [options] : [url, options]);
        };

        console.log('[API路径修复] jQuery.ajax方法已修改，将自动修复API路径');
        return true;
    }

    // 修复特定的API请求
    function fixSpecificApiRequests() {
        // 修复模板结构请求
        if (typeof window.loadTemplateStructure === 'function') {
            const originalLoadTemplateStructure = window.loadTemplateStructure;
            window.loadTemplateStructure = function(templateId) {
                console.log('[API路径修复] 修复loadTemplateStructure请求');
                const url = `/api/template/${templateId}/structure`;
                $.ajax({
                    url: url,
                    type: 'GET',
                    success: function(data) {
                        // 调用原始回调
                        if (typeof originalLoadTemplateStructure.success === 'function') {
                            originalLoadTemplateStructure.success(data);
                        }
                    },
                    error: function(xhr) {
                        console.error('[API路径修复] 加载模板结构失败:', xhr.status, xhr.statusText);
                        // 调用原始错误回调
                        if (typeof originalLoadTemplateStructure.error === 'function') {
                            originalLoadTemplateStructure.error(xhr);
                        }
                    }
                });
            };
        }

        // 修复段落长度请求
        if (typeof window.loadParagraphLength === 'function') {
            const originalLoadParagraphLength = window.loadParagraphLength;
            window.loadParagraphLength = function(templateId) {
                console.log('[API路径修复] 修复loadParagraphLength请求');
                const url = `/api/template/${templateId}/paragraph_length`;
                $.ajax({
                    url: url,
                    type: 'GET',
                    success: function(data) {
                        // 调用原始回调
                        if (typeof originalLoadParagraphLength.success === 'function') {
                            originalLoadParagraphLength.success(data);
                        }
                    },
                    error: function(xhr) {
                        console.error('[API路径修复] 加载段落长度失败:', xhr.status, xhr.statusText);
                        // 调用原始错误回调
                        if (typeof originalLoadParagraphLength.error === 'function') {
                            originalLoadParagraphLength.error(xhr);
                        }
                    }
                });
            };
        }
    }

    // 全局错误处理函数
    function handleApiError(event) {
        if (event.message && event.message.includes('404')) {
            console.error('[API路径修复] 捕获到API 404错误:', event.message);
            fixAllApiPaths();
        }
    }

    // 添加全局错误处理
    window.addEventListener('error', handleApiError);

    // 导出全局修复函数
    window.fixApiPaths = fixAllApiPaths;

    // 立即尝试修复
    if (fixAllApiPaths()) {
        console.log('[API路径修复] API路径已修复');
        fixSpecificApiRequests();
    } else {
        console.log('[API路径修复] 无法修复API路径，jQuery不可用');
    }

    console.log('[API路径修复] 初始化完成');
})();
