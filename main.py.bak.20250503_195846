"""
Main entry point for the 九猫 (Nine Cats) novel analysis system.
"""
import os
import sys
import threading
import signal
import atexit
from datetime import datetime, timezone
import logging

# 导入自定义日志配置和内存监控
from src.utils.logging_config import configure_logging, get_memory_friendly_logger
from src.utils.memory_monitor import start_memory_monitoring, stop_memory_monitoring

from src.web.app import app
import config

# 配置日志系统，启用启动模式以减少初始日志量
configure_logging(
    level=logging.INFO if not config.DEBUG else logging.DEBUG,
    log_file='logs/app.log',
    max_bytes=5 * 1024 * 1024,  # 5MB
    backup_count=10,
    console=True,
    startup_mode=True  # 启用启动模式，减少启动阶段的日志量
)

# 获取内存友好型日志记录器
logger = get_memory_friendly_logger(__name__)

# 全局变量，用于跟踪系统状态
running = True

# 优雅退出函数
def cleanup():
    """清理资源并优雅退出"""
    global running
    running = False
    logger.info("正在关闭九猫系统...")

    # 停止内存监控
    try:
        stop_memory_monitoring()
        logger.info("内存监控已停止")
    except Exception as e:
        logger.error(f"停止内存监控时出错: {str(e)}")

    # 清理数据库连接
    try:
        from src.db.connection import dispose_engine
        dispose_engine()
        logger.info("数据库连接已清理")
    except Exception as e:
        logger.error(f"清理数据库连接时出错: {str(e)}")

    # 显示最终内存状态
    try:
        from src.utils.memory_monitor import MemoryMonitor
        MemoryMonitor.print_memory_status()
    except Exception:
        pass

    logger.info("九猫系统已关闭")

# 注册信号处理函数
def signal_handler(sig, frame):
    """处理系统信号"""
    logger.info(f"收到信号 {sig}，准备关闭系统")
    cleanup()
    sys.exit(0)

# 注册退出处理函数
atexit.register(cleanup)

# 注册信号处理
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# 确保模板和静态文件路径正确
logger.info(f"模板路径: {app.template_folder}")
logger.info(f"静态文件路径: {app.static_folder}")

# 检查静态文件是否存在
js_lib_path = os.path.join(app.static_folder, 'js', 'lib')
css_lib_path = os.path.join(app.static_folder, 'css', 'lib')

if os.path.exists(js_lib_path):
    js_files = os.listdir(js_lib_path)
    logger.info(f"JS库文件: {js_files}")
else:
    logger.warning(f"JS库路径不存在: {js_lib_path}")

if os.path.exists(css_lib_path):
    css_files = os.listdir(css_lib_path)
    logger.info(f"CSS库文件: {css_files}")
else:
    logger.warning(f"CSS库路径不存在: {css_lib_path}")

logger.info("系统启动中，请稍候...")

# Create required directories
os.makedirs(config.UPLOAD_FOLDER, exist_ok=True)

# Add template context processor for current date
@app.context_processor
def inject_now():
    return {'now': datetime.now(timezone.utc)}

def run_app_with_monitoring():
    """运行Flask应用并监控资源使用情况"""
    import time
    import os
    import threading

    # 创建日志目录
    os.makedirs('logs', exist_ok=True)

    # 设置启动模式环境变量
    os.environ['STARTUP_MODE'] = 'True'

    # 定义一个函数，在30秒后关闭启动模式
    def disable_startup_mode():
        time.sleep(30)  # 等待30秒
        os.environ['STARTUP_MODE'] = 'False'
        logger.info("系统启动模式已结束，切换到正常运行模式")

        # 尝试扩展数据库连接池
        try:
            from src.db.connection import engine
            # 获取当前连接池大小
            current_size = engine.pool.size()
            # 如果当前连接池较小，则扩展它
            if current_size < 50:
                logger.info(f"扩展数据库连接池: {current_size} -> 50")
                engine.pool._pool.maxsize = 50
                engine.pool._max_overflow = 50
        except Exception as e:
            logger.error(f"扩展数据库连接池时出错: {str(e)}")

    # 启动一个线程，在30秒后关闭启动模式
    startup_timer = threading.Thread(target=disable_startup_mode, daemon=True)
    startup_timer.start()
    logger.info("已启动定时器，30秒后将退出启动模式")

    # 启动内存监控
    logger.info("启动内存监控...")
    start_memory_monitoring(
        warning_threshold=75.0,  # 内存使用率达到75%时发出警告
        critical_threshold=85.0,  # 内存使用率达到85%时采取措施
        check_interval=10.0  # 每10秒检查一次
    )

    # 启动Flask应用
    try:
        logger.info(f"启动九猫小说分析系统，地址: {config.HOST}:{config.PORT}")

        # 不在日志中显示完整API密钥
        if hasattr(config, 'DEEPSEEK_API_KEY') and config.DEEPSEEK_API_KEY:
            masked_key = config.DEEPSEEK_API_KEY[:6] + "..." + config.DEEPSEEK_API_KEY[-4:]
            logger.info(f"DeepSeek API Key: {masked_key}")

        # 使用threaded=True提高并发性能
        app.run(
            host=config.HOST,
            port=config.PORT,
            debug=config.DEBUG,
            threaded=True,
            use_reloader=False  # 禁用reloader以避免启动两个进程
        )
    except Exception as e:
        logger.error(f"启动应用时出错: {str(e)}")
        cleanup()
    finally:
        # 停止内存监控
        stop_memory_monitoring()

if __name__ == '__main__':
    # 检查是否已安装psutil
    try:
        import psutil
        logger.info("检测到psutil已安装，启用内存监控")

        # 显示初始内存状态
        from src.utils.memory_monitor import MemoryMonitor
        MemoryMonitor.print_memory_status()

        # 运行带监控的应用
        run_app_with_monitoring()
    except ImportError:
        logger.warning("未安装psutil，无法监控系统资源")
        logger.warning("建议安装psutil以启用内存监控: pip install psutil")
        logger.warning("系统将以有限的监控功能运行")

        # 创建日志目录
        import os
        os.makedirs('logs', exist_ok=True)

        # 直接运行应用，不使用内存监控
        try:
            logger.info(f"启动九猫小说分析系统，地址: {config.HOST}:{config.PORT}")
            app.run(
                host=config.HOST,
                port=config.PORT,
                debug=config.DEBUG,
                threaded=True,
                use_reloader=False
            )
        except Exception as e:
            logger.error(f"启动应用时出错: {str(e)}")
            cleanup()
