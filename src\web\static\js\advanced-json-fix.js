/**
 * 九猫 - 高级JSON修复脚本
 * 专门处理复杂的JSON解析错误，包括位置4476和6499的特定错误
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('高级JSON修复脚本已加载');
    
    // 保存原始的JSON.parse方法
    var originalJSONParse = JSON.parse;
    
    // 重写JSON.parse方法
    JSON.parse = function(text, reviver) {
        try {
            // 尝试使用原始方法解析
            return originalJSONParse(text, reviver);
        } catch (e) {
            console.error('JSON.parse错误:', e.message);
            
            // 获取错误位置
            var errorPosition = -1;
            var errorMatch = e.message.match(/position (\d+)/);
            if (errorMatch && errorMatch[1]) {
                errorPosition = parseInt(errorMatch[1]);
                console.log('错误位置:', errorPosition);
                
                // 显示错误位置前后的内容以便调试
                var start = Math.max(0, errorPosition - 50);
                var end = Math.min(text.length, errorPosition + 50);
                console.log('错误位置前内容:', text.substring(start, errorPosition));
                console.log('错误位置后内容:', text.substring(errorPosition, end));
            }
            
            // 特殊处理位置4476附近的错误 (Expected ',' or '}' after property value)
            if (errorPosition >= 4470 && errorPosition <= 4480) {
                console.log('检测到位置4476附近的错误，尝试特殊修复');
                
                try {
                    // 在错误位置插入逗号
                    var fixedText = text.substring(0, errorPosition) + ',' + text.substring(errorPosition);
                    console.log('尝试在位置添加逗号');
                    return originalJSONParse(fixedText, reviver);
                } catch (e1) {
                    console.error('添加逗号修复失败，尝试添加右大括号');
                    
                    try {
                        // 在错误位置插入右大括号
                        var fixedText = text.substring(0, errorPosition) + '}' + text.substring(errorPosition);
                        return originalJSONParse(fixedText, reviver);
                    } catch (e2) {
                        console.error('添加右大括号修复失败');
                    }
                }
            }
            
            // 特殊处理位置6499附近的错误 (Unterminated string)
            if (errorPosition >= 6490 && errorPosition <= 6510 || e.message.includes('Unterminated string')) {
                console.log('检测到未终止的字符串错误，尝试特殊修复');
                
                try {
                    // 在错误位置添加引号
                    var fixedText = text.substring(0, errorPosition) + '"' + text.substring(errorPosition);
                    console.log('尝试在位置添加引号');
                    return originalJSONParse(fixedText, reviver);
                } catch (e1) {
                    console.error('添加引号修复失败');
                    
                    // 尝试更复杂的修复：查找最后一个引号并闭合
                    try {
                        var lastQuotePos = text.lastIndexOf('"', errorPosition);
                        if (lastQuotePos !== -1) {
                            var fixedText = text.substring(0, lastQuotePos + 1) + '"}' + text.substring(lastQuotePos + 1);
                            console.log('尝试闭合最后一个引号并添加右大括号');
                            return originalJSONParse(fixedText, reviver);
                        }
                    } catch (e2) {
                        console.error('闭合引号修复失败');
                    }
                }
            }
            
            // 通用修复：处理character_relationships错误
            if (text.includes('character_relationships** 时遇到了问题')) {
                console.log('检测到character_relationships错误，尝试通用修复');
                
                try {
                    // 修复特定错误模式
                    var fixedText = text.replace(
                        /(character_relationships\*\* 时遇到了问题[^"]*\\n\\n## 错误信息\\n```\\nname )(.*?)(")/g,
                        '$1"$3'
                    );
                    
                    console.log('尝试修复character_relationships错误');
                    return originalJSONParse(fixedText, reviver);
                } catch (e1) {
                    console.error('character_relationships修复失败');
                }
            }
            
            // 高级修复：尝试修复整个JSON结构
            try {
                console.log('尝试高级修复整个JSON结构');
                
                // 修复常见的JSON格式问题
                var fixedText = text
                    // 修复未终止的字符串
                    .replace(/([^\\])"([^"]*$)/g, '$1"$2"')
                    // 修复缺少逗号的属性
                    .replace(/([^,{])\s*"([^"]+)":/g, '$1,"$2":')
                    // 修复缺少右大括号
                    .replace(/([^}])\s*$/g, '$1}')
                    // 修复未转义的引号
                    .replace(/([^\\])"/g, '$1\\"')
                    // 修复未转义的换行符
                    .replace(/\n/g, '\\n')
                    // 修复未转义的制表符
                    .replace(/\t/g, '\\t')
                    // 修复未转义的回车符
                    .replace(/\r/g, '\\r');
                
                console.log('尝试解析修复后的JSON');
                return originalJSONParse(fixedText, reviver);
            } catch (e1) {
                console.error('高级修复失败:', e1.message);
                
                // 最后的尝试：返回一个有效的空对象而不是抛出错误
                console.warn('所有修复尝试都失败，返回空对象');
                return {};
            }
        }
    };
    
    // 在页面加载完成后扫描并修复所有JSON数据
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始扫描页面中的JSON数据');
        
        // 特殊处理：检查是否在novel/4页面
        if (window.location.pathname === '/novel/4') {
            console.log('检测到novel/4页面，应用特殊修复');
            
            // 查找所有内联脚本
            var scripts = document.querySelectorAll('script:not([src])');
            
            scripts.forEach(function(script) {
                var content = script.textContent || '';
                
                // 检查是否包含JSON.parse调用
                if (content.includes('JSON.parse(')) {
                    console.log('找到包含JSON.parse调用的脚本');
                    
                    // 修复JSON.parse调用
                    var fixedContent = content.replace(
                        /(JSON\.parse\s*\(\s*['"])(.*?)(['"])/g,
                        function(match, prefix, jsonStr, suffix) {
                            // 检查是否包含错误信息
                            if (jsonStr.includes('character_relationships** 时遇到了问题')) {
                                console.log('修复JSON.parse中的character_relationships错误');
                                
                                // 修复特定错误模式
                                var fixedJsonStr = jsonStr.replace(
                                    /(character_relationships\*\* 时遇到了问题[^"]*\\n\\n## 错误信息\\n```\\nname )(.*?)(")/g,
                                    '$1"$3'
                                );
                                
                                return prefix + fixedJsonStr + suffix;
                            }
                            return match;
                        }
                    );
                    
                    // 如果内容被修改，替换脚本
                    if (fixedContent !== content) {
                        console.log('替换修复后的脚本');
                        var newScript = document.createElement('script');
                        newScript.textContent = fixedContent;
                        script.parentNode.replaceChild(newScript, script);
                    }
                }
                
                // 检查是否包含serverData变量
                if (content.includes('serverData = ') && content.includes('character_relationships')) {
                    console.log('找到包含serverData的脚本');
                    
                    // 修复serverData赋值
                    var fixedContent = content.replace(
                        /(serverData\s*=\s*)(.*?)(;)/g,
                        function(match, prefix, dataStr, suffix) {
                            if (dataStr.includes('character_relationships** 时遇到了问题')) {
                                console.log('修复serverData中的character_relationships错误');
                                
                                // 修复特定错误模式
                                var fixedDataStr = dataStr.replace(
                                    /(character_relationships\*\* 时遇到了问题[^"]*\\n\\n## 错误信息\\n```\\nname )(.*?)(")/g,
                                    '$1"$3'
                                );
                                
                                return prefix + fixedDataStr + suffix;
                            }
                            return match;
                        }
                    );
                    
                    // 如果内容被修改，替换脚本
                    if (fixedContent !== content) {
                        console.log('替换修复后的serverData脚本');
                        var newScript = document.createElement('script');
                        newScript.textContent = fixedContent;
                        script.parentNode.replaceChild(newScript, script);
                    }
                }
            });
            
            // 修复页面上的JSON数据属性
            var jsonElements = document.querySelectorAll('[data-json], [data-metadata]');
            
            jsonElements.forEach(function(element) {
                try {
                    // 检查data-json属性
                    if (element.hasAttribute('data-json')) {
                        var jsonContent = element.getAttribute('data-json');
                        
                        if (jsonContent && jsonContent.includes('character_relationships** 时遇到了问题')) {
                            console.log('找到包含character_relationships错误的data-json属性');
                            
                            // 修复特定错误模式
                            var fixedJson = jsonContent.replace(
                                /(character_relationships\*\* 时遇到了问题[^"]*\\n\\n## 错误信息\\n```\\nname )(.*?)(")/g,
                                '$1"$3'
                            );
                            
                            // 更新元素属性
                            element.setAttribute('data-json', fixedJson);
                        }
                    }
                    
                    // 检查data-metadata属性
                    if (element.hasAttribute('data-metadata')) {
                        var metadataContent = element.getAttribute('data-metadata');
                        
                        if (metadataContent && metadataContent.includes('character_relationships** 时遇到了问题')) {
                            console.log('找到包含character_relationships错误的data-metadata属性');
                            
                            // 修复特定错误模式
                            var fixedMetadata = metadataContent.replace(
                                /(character_relationships\*\* 时遇到了问题[^"]*\\n\\n## 错误信息\\n```\\nname )(.*?)(")/g,
                                '$1"$3'
                            );
                            
                            // 更新元素属性
                            element.setAttribute('data-metadata', fixedMetadata);
                        }
                    }
                } catch (e) {
                    console.error('处理JSON元素时出错:', e.message);
                }
            });
        }
    });
    
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && 
            (event.error.message.includes('JSON.parse') || 
             event.error.message.includes('Unexpected token') ||
             event.error.message.includes('Unterminated string') ||
             event.error.message.includes('Expected') ||
             event.error.message.includes('missing ) after argument list'))) {
            console.error('捕获到JSON相关错误:', event.error.message);
            console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
})();
