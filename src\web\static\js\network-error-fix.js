/**
 * 网络错误修复脚本
 * 解决 ERR_NETWORK_CHANGED 和 API 状态获取失败问题
 */

(function() {
    'use strict';

    // 网络错误重试配置
    const RETRY_CONFIG = {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
        timeoutMs: 10000
    };

    // 网络状态监控
    let networkStatus = {
        isOnline: navigator.onLine,
        lastCheck: Date.now(),
        failureCount: 0
    };

    /**
     * 检测网络状态变化
     */
    function setupNetworkMonitoring() {
        // 监听网络状态变化
        window.addEventListener('online', function() {
            networkStatus.isOnline = true;
            networkStatus.failureCount = 0;
            console.log('[网络监控] 网络连接已恢复');
            
            // 如果有正在进行的分析，尝试恢复
            if (window.analysisInProgress && window.analysisTimer) {
                console.log('[网络监控] 尝试恢复分析状态检查');
                // 延迟一秒后恢复轮询
                setTimeout(function() {
                    if (window.pollAnalysisStatus && window.currentTaskId) {
                        window.pollAnalysisStatus(window.currentTaskId);
                    }
                }, 1000);
            }
        });

        window.addEventListener('offline', function() {
            networkStatus.isOnline = false;
            console.log('[网络监控] 网络连接已断开');
        });

        // 定期检查网络状态
        setInterval(function() {
            if (!navigator.onLine && networkStatus.isOnline) {
                networkStatus.isOnline = false;
                console.log('[网络监控] 检测到网络断开');
            } else if (navigator.onLine && !networkStatus.isOnline) {
                networkStatus.isOnline = true;
                networkStatus.failureCount = 0;
                console.log('[网络监控] 检测到网络恢复');
            }
        }, 5000);
    }

    /**
     * 增强的 AJAX 请求函数，支持重试和错误处理
     */
    function enhancedAjax(options) {
        const originalOptions = $.extend({}, options);
        let retryCount = 0;

        function makeRequest() {
            const requestOptions = $.extend({}, originalOptions, {
                timeout: RETRY_CONFIG.timeoutMs,
                success: function(data, textStatus, xhr) {
                    // 重置失败计数
                    networkStatus.failureCount = 0;
                    
                    if (originalOptions.success) {
                        originalOptions.success(data, textStatus, xhr);
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    networkStatus.failureCount++;
                    
                    console.log(`[网络错误] 请求失败: ${textStatus}, 错误: ${errorThrown}, 状态码: ${xhr.status}`);
                    
                    // 检查是否是网络相关错误
                    const isNetworkError = (
                        textStatus === 'timeout' ||
                        textStatus === 'error' ||
                        xhr.status === 0 ||
                        errorThrown === 'Network Error' ||
                        errorThrown.includes('ERR_NETWORK_CHANGED')
                    );

                    // 如果是网络错误且还有重试次数
                    if (isNetworkError && retryCount < RETRY_CONFIG.maxRetries) {
                        retryCount++;
                        const delay = RETRY_CONFIG.retryDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, retryCount - 1);
                        
                        console.log(`[网络错误] 第 ${retryCount} 次重试，${delay}ms 后重试...`);
                        
                        setTimeout(function() {
                            // 检查网络状态
                            if (navigator.onLine) {
                                makeRequest();
                            } else {
                                console.log('[网络错误] 网络仍然断开，等待网络恢复...');
                                // 等待网络恢复后重试
                                const checkNetwork = setInterval(function() {
                                    if (navigator.onLine) {
                                        clearInterval(checkNetwork);
                                        makeRequest();
                                    }
                                }, 2000);
                            }
                        }, delay);
                        
                        return;
                    }

                    // 如果重试次数用完或不是网络错误，调用原始错误处理
                    if (originalOptions.error) {
                        // 提供更友好的错误信息
                        let friendlyError = errorThrown;
                        if (isNetworkError) {
                            friendlyError = '网络连接不稳定，请检查网络连接后重试';
                        } else if (xhr.status === 404) {
                            friendlyError = '请求的资源不存在';
                        } else if (xhr.status === 500) {
                            friendlyError = '服务器内部错误';
                        } else if (xhr.status === 503) {
                            friendlyError = '服务暂时不可用';
                        }
                        
                        // 创建增强的错误对象
                        const enhancedXhr = $.extend({}, xhr, {
                            friendlyError: friendlyError,
                            isNetworkError: isNetworkError,
                            retryCount: retryCount
                        });
                        
                        originalOptions.error(enhancedXhr, textStatus, friendlyError);
                    }
                }
            });

            return $.ajax(requestOptions);
        }

        return makeRequest();
    }

    /**
     * 修复 test.js 中的网络错误处理
     */
    function fixTestJsNetworkErrors() {
        // 如果 window.pollAnalysisStatus 存在，增强它
        if (window.pollAnalysisStatus) {
            const originalPollAnalysisStatus = window.pollAnalysisStatus;
            
            window.pollAnalysisStatus = function(taskId) {
                // 保存当前任务ID以便网络恢复时使用
                window.currentTaskId = taskId;
                
                if (window.analysisTimer) {
                    clearTimeout(window.analysisTimer);
                }

                window.analysisTimer = setTimeout(function() {
                    enhancedAjax({
                        url: `/api/test/status/${taskId}`,
                        type: 'GET',
                        success: function(response) {
                            if (response.success) {
                                // 更新进度
                                const progress = response.progress || 0;
                                $('.analysis-progress').css('width', `${progress}%`);

                                // 格式化状态显示
                                let statusText = response.status || '正在分析中...';
                                let formattedStatus = statusText;

                                if (statusText.includes(': ')) {
                                    const parts = statusText.split(': ');
                                    if (parts.length === 2) {
                                        if (statusText.includes('字')) {
                                            const chapterPart = parts[0];
                                            const wordCountPart = parts[1];
                                            formattedStatus = `${chapterPart}: <span class="badge badge-info">${wordCountPart}</span>`;
                                        } else {
                                            const chapterPart = parts[0];
                                            const dimensionPart = parts[1];
                                            formattedStatus = `${chapterPart}: <span class="badge badge-primary">${dimensionPart}</span>`;
                                        }
                                    }
                                }

                                $('#analysisStatus').html(formattedStatus);

                                // 更新进度条颜色
                                if (progress < 30) {
                                    $('.analysis-progress').removeClass('bg-success bg-info').addClass('bg-warning');
                                } else if (progress < 70) {
                                    $('.analysis-progress').removeClass('bg-success bg-warning').addClass('bg-info');
                                } else {
                                    $('.analysis-progress').removeClass('bg-info bg-warning').addClass('bg-success');
                                }

                                if (window.addLogEntry) {
                                    window.addLogEntry('info', `分析进度: ${progress}%, 状态: ${statusText}`);
                                }

                                if (response.completed) {
                                    // 分析完成
                                    window.analysisInProgress = false;
                                    window.currentTaskId = null;
                                    
                                    const duration = Math.round((new Date() - window.analysisStartTime) / 1000);

                                    if (response.error) {
                                        if (window.addLogEntry) {
                                            window.addLogEntry('error', `分析写作失败: ${response.error}，耗时: ${duration}秒`);
                                        }
                                        $('#startAnalysisBtn').html('<i class="fas fa-exclamation-triangle mr-1"></i> 处理失败').prop('disabled', false);
                                        $('#analysisStatus').text('分析写作失败');
                                        $('.analysis-progress').css('width', '100%').removeClass('progress-bar-animated').addClass('bg-danger');

                                        $('#resultSection').show();
                                        $('#analysisContent').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>分析写作失败: ${response.error}</div>`);
                                        $('#writingContent').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>由于分析失败，无法生成内容。</div>`);
                                    } else {
                                        let completionMessage = `分析写作完成，耗时: ${duration}秒`;
                                        if (response.results && response.results.generated_novel_id) {
                                            completionMessage += `，生成的内容已保存到内容仓库，ID: ${response.results.generated_novel_id}`;
                                        }
                                        if (window.addLogEntry) {
                                            window.addLogEntry('info', completionMessage);
                                        }

                                        $('#startAnalysisBtn').html('<i class="fas fa-check mr-1"></i> 处理完成').prop('disabled', false);

                                        if (response.results && response.results.generated_novel_id) {
                                            $('#analysisStatus').html(`分析写作已完成，<a href="/v3/content-repository" class="alert-link">点击查看内容仓库</a>`);
                                        } else {
                                            $('#analysisStatus').text('分析写作已完成');
                                        }

                                        $('.analysis-progress').css('width', '100%').removeClass('progress-bar-animated');
                                        $('#resultSection').show();

                                        if (window.displayResults) {
                                            window.displayResults(response.results);
                                        }
                                    }
                                } else {
                                    // 继续轮询
                                    window.pollAnalysisStatus(taskId);
                                }
                            } else {
                                window.analysisInProgress = false;
                                window.currentTaskId = null;
                                
                                if (window.showError) {
                                    window.showError(response.error || '获取分析状态失败');
                                }
                                $('#startAnalysisBtn').html('<i class="fas fa-magic mr-1"></i> 开始一键分析写作').prop('disabled', false);
                            }
                        },
                        error: function(xhr) {
                            window.analysisInProgress = false;
                            window.currentTaskId = null;
                            
                            let errorMessage = '获取分析状态时出错';
                            if (xhr.friendlyError) {
                                errorMessage = xhr.friendlyError;
                            } else if (xhr.status === 0) {
                                errorMessage = '网络连接失败，请检查网络连接';
                            }
                            
                            if (window.showError) {
                                window.showError(errorMessage);
                            }
                            $('#startAnalysisBtn').html('<i class="fas fa-magic mr-1"></i> 开始一键分析写作').prop('disabled', false);
                        }
                    });
                }, 2000);
            };
        }
    }

    /**
     * 创建网络状态指示器
     */
    function createNetworkIndicator() {
        const indicator = $(`
            <div id="networkIndicator" style="
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 9999;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 12px;
                color: white;
                display: none;
            ">
                <i class="fas fa-wifi"></i> <span id="networkStatus">网络正常</span>
            </div>
        `);
        
        $('body').append(indicator);
        
        // 监听网络状态变化并更新指示器
        function updateIndicator() {
            const $indicator = $('#networkIndicator');
            const $status = $('#networkStatus');
            
            if (!navigator.onLine || networkStatus.failureCount > 2) {
                $indicator.css('background-color', '#dc3545').show();
                $status.text('网络异常');
            } else if (networkStatus.failureCount > 0) {
                $indicator.css('background-color', '#ffc107').show();
                $status.text('网络不稳定');
            } else {
                $indicator.hide();
            }
        }
        
        // 定期更新指示器
        setInterval(updateIndicator, 1000);
        
        // 初始更新
        updateIndicator();
    }

    // 初始化
    $(document).ready(function() {
        console.log('[网络错误修复] 初始化网络错误修复脚本');
        
        setupNetworkMonitoring();
        fixTestJsNetworkErrors();
        createNetworkIndicator();
        
        // 全局暴露增强的 AJAX 函数
        window.enhancedAjax = enhancedAjax;
        
        console.log('[网络错误修复] 网络错误修复脚本初始化完成');
    });

})();
