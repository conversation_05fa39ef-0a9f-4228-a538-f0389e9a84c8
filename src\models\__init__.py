"""
Data models for the 九猫 (Nine Cats) novel analysis system.
"""
# 导入顺序很重要，确保依赖关系正确

# 首先导入基础模型
from .base import Base

# 导入分析过程模型(先导入以解决循环依赖)
from .analysis_process import AnalysisProcess
from .chapter_analysis_process import ChapterAnalysisProcess

# 然后导入核心模型（注意顺序）
from .novel import Novel
from .chapter import Chapter
from .analysis_result import AnalysisResult
from .chapter_analysis_result import ChapterAnalysisResult
from .analysis_checkpoint import AnalysisCheckpoint
from .intermediate_result import IntermediateResult

# 导入系统相关模型
from .api_log import ApiLog
from .system_alert import SystemAlert
from .system_metric import SystemMetric

# 导入v3.0新增模型
from .reference_template import ReferenceTemplate
from .generated_content import GeneratedContent
from .preset import Preset

__all__ = [
    "Base",
    "Novel",
    "Chapter",
    "AnalysisResult",
    "ChapterAnalysisResult",
    "AnalysisCheckpoint",
    "IntermediateResult",
    "AnalysisProcess",
    "ChapterAnalysisProcess",
    "ApiLog",
    "SystemAlert",
    "SystemMetric",
    "ReferenceTemplate",
    "GeneratedContent",
    "Preset",
]
