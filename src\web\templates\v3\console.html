{% extends "v3/base.html" %}

{% block title %}控制台 - 九猫小说分析写作系统v3.0{% endblock %}

{% block head %}
<!-- API路径修复脚本 -->
<script src="{{ url_for('static', filename='js/api-path-fix.js') }}"></script>

<!-- jQuery修复脚本 - 增强版 -->
<script>
// 立即执行的jQuery修复脚本
(function() {
    console.log('[jQuery修复] 初始化增强版修复脚本...');

    // 全局变量
    let checkInterval = null;
    let fixAttempts = 0;
    const MAX_FIX_ATTEMPTS = 10;

    // 检查jQuery是否已加载
    function checkJQuery() {
        if (typeof jQuery !== 'undefined') {
            console.log('[jQuery修复] jQuery已加载，版本:', jQuery.fn.jquery || '未知');

            // 确保$ 变量可用
            if (typeof $ === 'undefined') {
                console.log('[jQuery修复] $ 变量未定义，设置 $ = jQuery');
                window.$ = jQuery;
            }

            return true;
        }
        console.log('[jQuery修复] jQuery未加载');
        return false;
    }

    // 加载jQuery
    function loadJQuery(callback) {
        console.log('[jQuery修复] 尝试加载jQuery...');

        const script = document.createElement('script');
        script.src = '/static/js/lib/jquery.min.js';

        script.onload = function() {
            console.log('[jQuery修复] jQuery加载成功!');
            if (callback) callback(jQuery);
        };

        script.onerror = function() {
            console.error('[jQuery修复] 加载jQuery失败，尝试使用CDN');

            const cdnScript = document.createElement('script');
            cdnScript.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';

            cdnScript.onload = function() {
                console.log('[jQuery修复] 从CDN加载jQuery成功!');
                if (callback) callback(jQuery);
            };

            cdnScript.onerror = function() {
                console.error('[jQuery修复] 从CDN加载jQuery也失败，创建内联jQuery');
                createInlineJQuery(callback);
            };

            document.head.appendChild(cdnScript);
        };

        document.head.appendChild(script);
    }

    // 创建内联jQuery (最小版本)
    function createInlineJQuery(callback) {
        console.log('[jQuery修复] 创建内联jQuery...');

        // 创建内联jQuery
        window.jQuery = function(selector) {
            // 处理$(function(){}) 形式的调用 (document ready)
            if (typeof selector === 'function') {
                if (document.readyState !== 'loading') {
                    selector();
                } else {
                    document.addEventListener('DOMContentLoaded', selector);
                }
                return jQuery;
            }

            // 处理选择器
            let elements = [];

            if (selector === document) {
                elements = [document];
            } else if (selector === window) {
                elements = [window];
            } else if (selector instanceof Element) {
                elements = [selector];
            } else if (typeof selector === 'string') {
                elements = Array.from(document.querySelectorAll(selector));
            } else if (selector instanceof Array) {
                elements = selector;
            }

            // 创建jQuery对象
            const jQueryObject = {
                length: elements.length,

                // 获取元素
                get: function(index) {
                    return elements[index];
                },

                // 遍历元素
                each: function(callback) {
                    for (let i = 0; i < elements.length; i++) {
                        callback.call(elements[i], i, elements[i]);
                    }
                    return this;
                },

                // 事件绑定
                on: function(events, selector, data, handler) {
                    // 处理参数
                    if (typeof selector === 'function') {
                        handler = selector;
                        selector = undefined;
                        data = undefined;
                    } else if (typeof data === 'function') {
                        handler = data;
                        data = undefined;
                    }

                    if (!handler) return this;

                    // 处理多个事件
                    if (events.indexOf(' ') > -1) {
                        const eventArray = events.split(' ');
                        for (let i = 0; i < eventArray.length; i++) {
                            this.on(eventArray[i], selector, data, handler);
                        }
                        return this;
                    }

                    return this.each(function() {
                        const element = this;

                        // 处理事件委托
                        if (selector) {
                            const originalHandler = handler;
                            handler = function(e) {
                                const target = e.target;
                                const matches = element.querySelectorAll(selector);
                                let current = target;

                                while (current && current !== element) {
                                    for (let i = 0; i < matches.length; i++) {
                                        if (current === matches[i]) {
                                            e.delegateTarget = element;
                                            e.currentTarget = current;
                                            originalHandler.call(current, e);
                                            return;
                                        }
                                    }
                                    current = current.parentNode;
                                }
                            };
                        }

                        // 处理Bootstrap事件
                        if (events.includes('.bs.')) {
                            const baseEvent = events.split('.')[0];
                            element.addEventListener(baseEvent, handler);
                        } else {
                            element.addEventListener(events, handler);
                        }
                    });
                },

                // 移除事件
                off: function(events, selector, handler) {
                    if (typeof selector === 'function') {
                        handler = selector;
                        selector = undefined;
                    }

                    return this.each(function() {
                        if (events) {
                            events.split(' ').forEach((event) => {
                                const baseEvent = event.split('.')[0];
                                this.removeEventListener(baseEvent, handler);
                            });
                        }
                    });
                },

                // 触发事件
                trigger: function(eventType) {
                    return this.each(function() {
                        try {
                            const event = new Event(eventType);
                            this.dispatchEvent(event);
                        } catch (e) {
                            try {
                                const event = document.createEvent('Event');
                                event.initEvent(eventType, true, true);
                                this.dispatchEvent(event);
                            } catch (ie_e) {
                                console.error('[jQuery修复] 触发事件失败:', ie_e);
                            }
                        }
                    });
                },

                // 文档就绪
                ready: function(callback) {
                    if (document.readyState !== 'loading') {
                        callback.call(document, jQuery);
                    } else {
                        document.addEventListener('DOMContentLoaded', function() {
                            callback.call(document, jQuery);
                        });
                    }
                    return this;
                },

                // HTML操作
                html: function(content) {
                    if (content === undefined) {
                        return elements[0] ? elements[0].innerHTML : '';
                    }
                    return this.each(function() {
                        this.innerHTML = content;
                    });
                },

                // 文本操作
                text: function(content) {
                    if (content === undefined) {
                        return elements[0] ? elements[0].textContent : '';
                    }
                    return this.each(function() {
                        this.textContent = content;
                    });
                },

                // 值操作
                val: function(value) {
                    if (value === undefined) {
                        return elements[0] ? elements[0].value : '';
                    }
                    return this.each(function() {
                        this.value = value;
                    });
                },

                // 添加内容
                append: function(content) {
                    return this.each(function() {
                        if (typeof content === 'string') {
                            this.insertAdjacentHTML('beforeend', content);
                        } else if (content instanceof Element) {
                            this.appendChild(content);
                        }
                    });
                },

                // 类操作
                addClass: function(className) {
                    return this.each(function() {
                        this.classList.add(className);
                    });
                },

                removeClass: function(className) {
                    return this.each(function() {
                        this.classList.remove(className);
                    });
                },

                toggleClass: function(className) {
                    return this.each(function() {
                        this.classList.toggle(className);
                    });
                },

                hasClass: function(className) {
                    return elements[0] ? elements[0].classList.contains(className) : false;
                },

                // 属性操作
                attr: function(name, value) {
                    if (value === undefined) {
                        return elements[0] ? elements[0].getAttribute(name) : null;
                    }
                    return this.each(function() {
                        this.setAttribute(name, value);
                    });
                },

                removeAttr: function(name) {
                    return this.each(function() {
                        this.removeAttribute(name);
                    });
                },

                // 样式操作
                css: function(prop, value) {
                    if (typeof prop === 'object') {
                        return this.each(function() {
                            for (const p in prop) {
                                this.style[p] = prop[p];
                            }
                        });
                    }
                    if (value === undefined) {
                        return elements[0] ? getComputedStyle(elements[0])[prop] : '';
                    }
                    return this.each(function() {
                        this.style[prop] = value;
                    });
                },

                // 显示/隐藏
                show: function() {
                    return this.each(function() {
                        this.style.display = '';
                    });
                },

                hide: function() {
                    return this.each(function() {
                        this.style.display = 'none';
                    });
                },

                toggle: function() {
                    return this.each(function() {
                        this.style.display = this.style.display === 'none' ? '' : 'none';
                    });
                },

                // 点击事件
                click: function(handler) {
                    if (handler) {
                        return this.on('click', handler);
                    }
                    return this.each(function() {
                        this.click();
                    });
                },

                // Bootstrap标签页
                tab: function(action) {
                    if (action === 'show') {
                        return this.each(function() {
                            this.click();
                        });
                    }
                    return this;
                }
            };

            // 添加数组访问方法
            for (let i = 0; i < elements.length; i++) {
                jQueryObject[i] = elements[i];
            }

            return jQueryObject;
        };

        // 添加静态方法
        jQuery.ajax = function(options) {
            const xhr = new XMLHttpRequest();
            xhr.open(options.type || 'GET', options.url, true);

            if (options.contentType) {
                xhr.setRequestHeader('Content-Type', options.contentType);
            }

            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    if (options.success) {
                        let response;
                        try {
                            response = JSON.parse(xhr.responseText);
                        } catch (e) {
                            response = xhr.responseText;
                        }
                        options.success(response);
                    }
                } else {
                    if (options.error) {
                        options.error(xhr);
                    }
                }
            };

            xhr.onerror = function() {
                if (options.error) {
                    options.error(xhr);
                }
            };

            xhr.send(options.data);
        };

        // 设置版本和fn
        jQuery.fn = jQuery.prototype = {};
        jQuery.fn.jquery = '1.0.0-inline';

        // 设置$别名
        window.$ = jQuery;

        console.log('[jQuery修复] 内联jQuery创建成功');

        if (callback) callback(jQuery);
    }

    // 修复jQuery.fn.on方法
    function fixJQueryOn() {
        if (typeof jQuery === 'undefined') return false;

        if (typeof jQuery.fn.on !== 'function') {
            console.log('[jQuery修复] 添加jQuery.fn.on方法');

            jQuery.fn.on = function(events, selector, data, handler) {
                // 处理参数
                if (typeof selector === 'function') {
                    handler = selector;
                    selector = undefined;
                    data = undefined;
                } else if (typeof data === 'function') {
                    handler = data;
                    data = undefined;
                }

                if (!handler) return this;

                // 处理多个事件
                if (events.indexOf(' ') > -1) {
                    const eventArray = events.split(' ');
                    for (let i = 0; i < eventArray.length; i++) {
                        this.on(eventArray[i], selector, data, handler);
                    }
                    return this;
                }

                return this.each(function() {
                    const element = this;

                    // 处理事件委托
                    if (selector) {
                        const originalHandler = handler;
                        handler = function(e) {
                            const target = e.target;
                            const matches = element.querySelectorAll(selector);
                            let current = target;

                            while (current && current !== element) {
                                for (let i = 0; i < matches.length; i++) {
                                    if (current === matches[i]) {
                                        e.delegateTarget = element;
                                        e.currentTarget = current;
                                        originalHandler.call(current, e);
                                        return;
                                    }
                                }
                                current = current.parentNode;
                            }
                        };
                    }

                    // 处理Bootstrap事件
                    if (events.includes('.bs.')) {
                        const baseEvent = events.split('.')[0];
                        element.addEventListener(baseEvent, handler);
                    } else {
                        element.addEventListener(events, handler);
                    }
                });
            };

            return true;
        }

        return false;
    }

    // 修复jQuery.fn.ready方法
    function fixJQueryReady() {
        if (typeof jQuery === 'undefined') return false;

        if (typeof jQuery.fn.ready !== 'function') {
            console.log('[jQuery修复] 添加jQuery.fn.ready方法');

            jQuery.fn.ready = function(callback) {
                if (document.readyState !== 'loading') {
                    callback.call(document, jQuery);
                } else {
                    document.addEventListener('DOMContentLoaded', function() {
                        callback.call(document, jQuery);
                    });
                }
                return this;
            };

            return true;
        }

        return false;
    }

    // 修复所有jQuery方法
    function fixAllJQueryMethods() {
        fixAttempts++;
        console.log('[jQuery修复] 尝试修复jQuery方法，第', fixAttempts, '次尝试');

        if (!checkJQuery()) {
            console.log('[jQuery修复] jQuery未加载，尝试加载');
            loadJQuery(function() {
                fixJQueryMethods();
            });
            return false;
        }

        return fixJQueryMethods();
    }

    // 修复jQuery方法
    function fixJQueryMethods() {
        let fixed = false;

        // 修复.on()方法
        if (fixJQueryOn()) {
            fixed = true;
        }

        // 修复.ready()方法
        if (fixJQueryReady()) {
            fixed = true;
        }

        // 修复.tab()方法
        if (typeof jQuery.fn.tab !== 'function') {
            console.log('[jQuery修复] 添加jQuery.fn.tab方法');
            jQuery.fn.tab = function(action) {
                if (action === 'show') {
                    return this.each(function() {
                        this.click();
                    });
                }
                return this;
            };
            fixed = true;
        }

        // 修复.trigger()方法
        if (typeof jQuery.fn.trigger !== 'function') {
            console.log('[jQuery修复] 添加jQuery.fn.trigger方法');
            jQuery.fn.trigger = function(eventType) {
                return this.each(function() {
                    try {
                        const event = new Event(eventType);
                        this.dispatchEvent(event);
                    } catch (e) {
                        try {
                            const event = document.createEvent('Event');
                            event.initEvent(eventType, true, true);
                            this.dispatchEvent(event);
                        } catch (ie_e) {
                            console.error('[jQuery修复] 触发事件失败:', ie_e);
                        }
                    }
                });
            };
            fixed = true;
        }

        if (fixed) {
            console.log('[jQuery修复] jQuery方法已修复');
        } else {
            console.log('[jQuery修复] 没有需要修复的jQuery方法');
        }

        return fixed;
    }

    // 定期检查jQuery方法
    function startPeriodicCheck() {
        if (checkInterval) {
            clearInterval(checkInterval);
        }

        checkInterval = setInterval(function() {
            if (fixAttempts >= MAX_FIX_ATTEMPTS) {
                console.log('[jQuery修复] 已达到最大尝试次数，停止定期检查');
                clearInterval(checkInterval);
                return;
            }

            if (typeof jQuery !== 'undefined') {
                // 检查jQuery.fn.on方法是否存在
                if (typeof jQuery.fn.on !== 'function') {
                    console.log('[jQuery修复] 定期检查发现jQuery.fn.on方法不存在，尝试修复');
                    fixAllJQueryMethods();
                }

                // 检查jQuery.fn.ready方法是否存在
                if (typeof jQuery.fn.ready !== 'function') {
                    console.log('[jQuery修复] 定期检查发现jQuery.fn.ready方法不存在，尝试修复');
                    fixAllJQueryMethods();
                }
            } else {
                console.log('[jQuery修复] 定期检查发现jQuery未加载，尝试修复');
                fixAllJQueryMethods();
            }
        }, 1000); // 每秒检查一次
    }

    // 全局错误处理函数
    function handleGlobalError(event) {
        if (event.message && (
            event.message.includes('$ is not defined') ||
            event.message.includes('$(...).on is not a function') ||
            event.message.includes('$(...).ready is not a function') ||
            event.message.includes('$(...).tab is not a function') ||
            event.message.includes('jQuery')
        )) {
            console.error('[jQuery修复] 捕获到jQuery错误:', event.message);

            // 显示应急修复按钮
            const container = document.getElementById('emergencyFixContainer');
            if (container) {
                container.style.display = 'block';
            }

            fixAllJQueryMethods();
        }
    }

    // 添加全局错误处理
    window.addEventListener('error', handleGlobalError);

    // 导出全局修复函数
    window.fixJQuery = function() {
        console.log('[jQuery修复] 手动触发jQuery修复');
        fixAttempts = 0; // 重置尝试次数
        return fixAllJQueryMethods();
    };

    // 立即尝试修复
    fixAllJQueryMethods();

    // 启动定期检查
    startPeriodicCheck();

    // 在DOM加载完成后再次尝试修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[jQuery修复] DOM加载完成，再次尝试修复jQuery');
        fixAllJQueryMethods();
    });

    console.log('[jQuery修复] 增强版修复脚本初始化完成');
})();
</script>

<!-- 内联jQuery加载器 - 确保jQuery在页面加载最早阶段就可用 -->
<script>
// 立即执行的内联jQuery加载器
(function() {
    console.log('[内联jQuery加载器] 初始化...');

    // 全局变量，标记jQuery是否已加载
    window.jQueryLoaded = false;

    // 检查jQuery是否已加载
    function checkJQuery() {
        if (typeof jQuery !== 'undefined') {
            console.log('[内联jQuery加载器] jQuery已加载，版本:', jQuery.fn.jquery);

            // 确保$ 变量可用
            if (typeof $ === 'undefined') {
                console.log('[内联jQuery加载器] $ 变量未定义，设置 $ = jQuery');
                window.$ = jQuery;
            }

            // 标记jQuery已加载
            window.jQueryLoaded = true;

            // 触发jQuery加载完成事件
            try {
                const event = new Event('jQueryLoaded');
                document.dispatchEvent(event);
            } catch (e) {
                console.error('[内联jQuery加载器] 触发事件失败:', e);
                // IE兼容性处理
                try {
                    const ieEvent = document.createEvent('Event');
                    ieEvent.initEvent('jQueryLoaded', true, true);
                    document.dispatchEvent(ieEvent);
                } catch (ie_e) {
                    console.error('[内联jQuery加载器] IE事件创建失败:', ie_e);
                }
            }

            return true;
        }

        return false;
    }

    // 立即加载jQuery
    function loadJQueryNow() {
        console.log('[内联jQuery加载器] 立即加载jQuery...');

        // 创建script元素
        const script = document.createElement('script');
        script.src = '/static/js/lib/jquery.min.js';

        script.onload = function() {
            console.log('[内联jQuery加载器] jQuery加载成功!');

            // 确保$ 变量可用
            window.$ = jQuery;

            // 标记jQuery已加载
            window.jQueryLoaded = true;

            // 触发jQuery加载完成事件
            try {
                const event = new Event('jQueryLoaded');
                document.dispatchEvent(event);
            } catch (e) {
                console.error('[内联jQuery加载器] 触发事件失败:', e);
                // IE兼容性处理
                try {
                    const ieEvent = document.createEvent('Event');
                    ieEvent.initEvent('jQueryLoaded', true, true);
                    document.dispatchEvent(ieEvent);
                } catch (ie_e) {
                    console.error('[内联jQuery加载器] IE事件创建失败:', ie_e);
                }
            }
        };

        script.onerror = function() {
            console.error('[内联jQuery加载器] 加载jQuery失败，创建内联jQuery');
            createInlineJQuery();
        };

        // 添加到文档头部
        document.head.appendChild(script);
    }

    // 创建内联jQuery (最小版本)
    function createInlineJQuery() {
        console.log('[内联jQuery加载器] 创建内联jQuery...');

        // 创建script元素
        const script = document.createElement('script');
        script.textContent = `
            // 极简jQuery替代品
            window.jQuery = function(selector) {
                if (typeof selector === 'function') {
                    if (document.readyState !== 'loading') {
                        selector();
                    } else {
                        document.addEventListener('DOMContentLoaded', selector);
                    }
                    return;
                }

                const elements = typeof selector === 'string'
                    ? document.querySelectorAll(selector)
                    : [selector];

                return {
                    elements: elements,
                    length: elements.length,
                    each: function(callback) {
                        for (let i = 0; i < this.elements.length; i++) {
                            callback.call(this.elements[i], i, this.elements[i]);
                        }
                        return this;
                    },
                    html: function(content) {
                        if (content === undefined) {
                            return this.elements[0] ? this.elements[0].innerHTML : '';
                        }
                        this.each(function() {
                            this.innerHTML = content;
                        });
                        return this;
                    },
                    text: function(content) {
                        if (content === undefined) {
                            return this.elements[0] ? this.elements[0].textContent : '';
                        }
                        this.each(function() {
                            this.textContent = content;
                        });
                        return this;
                    },
                    addClass: function(className) {
                        this.each(function() {
                            this.classList.add(className);
                        });
                        return this;
                    },
                    removeClass: function(className) {
                        this.each(function() {
                            this.classList.remove(className);
                        });
                        return this;
                    },
                    append: function(content) {
                        this.each(function() {
                            if (typeof content === 'string') {
                                this.innerHTML += content;
                            } else if (content.nodeType) {
                                this.appendChild(content);
                            }
                        });
                        return this;
                    },
                    click: function(callback) {
                        this.each(function() {
                            this.addEventListener('click', callback);
                        });
                        return this;
                    },
                    on: function(event, selector, callback) {
                        // 处理不同的参数形式
                        if (typeof selector === 'function') {
                            callback = selector;
                            selector = null;
                        }

                        // 处理Bootstrap特殊事件
                        if (event.includes('.bs.')) {
                            // 对于Bootstrap事件，使用原生事件监听
                            const baseEvent = event.split('.')[0]; // 例如从'shown.bs.tab'提取'shown'

                            this.each(function() {
                                this.addEventListener(baseEvent, function(e) {
                                    // 简单模拟Bootstrap事件
                                    callback.call(this, e);
                                });
                            });
                            return this;
                        }

                        this.each(function() {
                            if (selector) {
                                // 事件委托
                                this.addEventListener(event, function(e) {
                                    const possibleTargets = document.querySelectorAll(selector);
                                    let target = e.target;

                                    while (target !== this) {
                                        for (let i = 0; i < possibleTargets.length; i++) {
                                            if (possibleTargets[i] === target) {
                                                callback.call(target, e);
                                                return;
                                            }
                                        }
                                        target = target.parentNode;
                                        if (!target) break;
                                    }
                                });
                            } else {
                                // 直接绑定
                                this.addEventListener(event, callback);
                            }
                        });
                        return this;
                    }
                };
            };

            // 设置版本和fn
            jQuery.fn = {};
            jQuery.fn.jquery = '1.0.0-minimal';

            // 添加tab方法
            jQuery.fn.tab = function(action) {
                if (action === 'show') {
                    this.elements.forEach(function(element) {
                        if (element) {
                            element.click();
                        }
                    });
                }
                return this;
            };

            // 添加ajax方法
            jQuery.ajax = function(options) {
                const xhr = new XMLHttpRequest();
                xhr.open(options.type || 'GET', options.url, true);

                if (options.contentType) {
                    xhr.setRequestHeader('Content-Type', options.contentType);
                }

                xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        if (options.success) {
                            let response;
                            try {
                                response = JSON.parse(xhr.responseText);
                            } catch (e) {
                                response = xhr.responseText;
                            }
                            options.success(response);
                        }
                    } else {
                        if (options.error) {
                            options.error(xhr);
                        }
                    }
                };

                xhr.onerror = function() {
                    if (options.error) {
                        options.error(xhr);
                    }
                };

                xhr.send(options.data);

                return xhr;
            };

            // 设置$别名
            window.$ = jQuery;

            // 标记jQuery已加载
            window.jQueryLoaded = true;

            console.log('[内联jQuery加载器] 已创建内联jQuery替代品');

            // 触发jQuery加载完成事件
            try {
                const event = new Event('jQueryLoaded');
                document.dispatchEvent(event);
            } catch (e) {
                console.error('[内联jQuery加载器] 触发事件失败:', e);
                // IE兼容性处理
                try {
                    const ieEvent = document.createEvent('Event');
                    ieEvent.initEvent('jQueryLoaded', true, true);
                    document.dispatchEvent(ieEvent);
                } catch (ie_e) {
                    console.error('[内联jQuery加载器] IE事件创建失败:', ie_e);
                }
            }
        `;

        document.head.appendChild(script);
    }

    // 导出全局函数，以便其他脚本可以使用
    window.ensureJQuery = function(callback) {
        if (window.jQueryLoaded || checkJQuery()) {
            if (callback && typeof callback === 'function') {
                callback(jQuery);
            }
            return true;
        } else {
            // 添加事件监听器，等待jQuery加载完成
            document.addEventListener('jQueryLoaded', function() {
                if (callback && typeof callback === 'function') {
                    callback(jQuery);
                }
            });

            // 加载jQuery
            loadJQueryNow();
            return false;
        }
    };

    // 立即检查jQuery
    if (!checkJQuery()) {
        // 如果jQuery未加载，立即加载
        loadJQueryNow();
    }

    console.log('[内联jQuery加载器] 初始化完成');
})();
</script>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/knowledge-base-compact.css') }}">
<style>
    /* 采用v3.1主色调和卡片式UI */
    .console-container { height: calc(100vh - 250px); min-height: 600px; }
    .console-nav { background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; }
    .console-nav .nav-link { color: #495057; padding: 1rem; border-radius: 0; border-bottom: 3px solid transparent; }
    .console-nav .nav-link.active { color: #4a6bff; background-color: transparent; border-bottom: 3px solid #4a6bff; }
    .console-content { padding: 1.5rem; height: 100%; overflow-y: auto; }
    .log-container { background-color: #1e1e1e; color: #f0f0f0; font-family: 'Consolas', 'Courier New', monospace; padding: 1rem; border-radius: 0.25rem; height: 500px; overflow-y: auto; }
    .log-line { margin-bottom: 0.25rem; line-height: 1.5; }
    .log-timestamp { color: #569cd6; margin-right: 0.5rem; }
    .log-info { color: #f0f0f0; }
    .log-warn { color: #dcdcaa; }
    .log-error { color: #f14c4c; }
    .log-welcome { color: #8a8a8a; font-style: italic; }
    .section-title { font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem; padding-bottom: 0.5rem; border-bottom: 1px solid #dee2e6; }
    .template-card { cursor: pointer; transition: all 0.2s ease; }
    .template-card:hover { transform: translateY(-3px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
    .knowledge-base-container, .auto-writing-container { height: 100%; }
    .markdown-body { padding: 1rem; background-color: #fff; border-radius: 0.25rem; border: 1px solid #dee2e6; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 控制台导航栏 -->
    <ul class="nav nav-tabs console-nav mb-4" id="consoleTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="true">
                <i class="fas fa-terminal me-2"></i>运行日志
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab" aria-controls="analysis" aria-selected="false">
                <i class="fas fa-chart-bar me-2"></i>分析展示
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="knowledge-tab" data-bs-toggle="tab" data-bs-target="#knowledge" type="button" role="tab" aria-controls="knowledge" aria-selected="false">
                <i class="fas fa-book me-2"></i>知识库/预设
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="writing-tab" data-bs-toggle="tab" data-bs-target="#writing" type="button" role="tab" aria-controls="writing" aria-selected="false">
                <i class="fas fa-pen-fancy me-2"></i>自动写作
            </button>
        </li>
    </ul>

    <div class="tab-content console-container" id="consoleTabContent">
        <!-- 运行日志 -->
        <div class="tab-pane fade show active" id="logs" role="tabpanel" aria-labelledby="logs-tab">
            <div class="console-content">
                <h2 class="section-title"><i class="fas fa-terminal me-2"></i>运行日志</h2>
                <!-- 修复链接 -->
                {% include 'v3/console_fix_link.html' %}

                <div class="card mb-4">
                    <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">系统日志</h5>
                        <div>
                            <button id="clearLogsBtn" class="btn btn-sm btn-outline-light me-2">
                                <i class="fas fa-trash-alt me-1"></i>清除
                            </button>
                            <button id="refreshLogsBtn" class="btn btn-sm btn-outline-light">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="log-container" id="logContent">
                            <div class="log-line log-welcome">
                                <span class="log-timestamp">[{{ now.strftime('%H:%M:%S') }}]</span> 欢迎使用九猫系统运行日志。此日志显示系统运行过程中的重要信息。
                            </div>
                            <div class="log-line log-info">
                                <span class="log-timestamp">[{{ now.strftime('%H:%M:%S') }}]</span> 系统已启动，准备就绪。
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="input-group">
                            <input type="text" id="logSearch" class="form-control" placeholder="搜索日志...">
                            <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析展示 -->
        <div class="tab-pane fade" id="analysis" role="tabpanel" aria-labelledby="analysis-tab">
            <div class="console-content">
                <h2 class="section-title"><i class="fas fa-chart-bar me-2"></i>分析展示</h2>
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">选择参考蓝本</h5>
                    </div>
                    <div class="card-body">
                        {% if templates %}
                            <div class="row">
                                {% for template in templates %}
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 template-card" data-template-id="{{ template.id }}">
                                            <div class="card-body">
                                                <h5 class="card-title">{{ template.title }}</h5>
                                                <p class="card-text text-muted">
                                                    {% if template.author %}作者: {{ template.author }}<br>{% endif %}
                                                    字数: {{ template.word_count }}<br>
                                                    章节数: {{ template.chapter_count }}
                                                </p>
                                                <div class="text-center mt-3">
                                                    <button class="btn btn-sm btn-outline-primary select-template-btn" data-template-id="{{ template.id }}">
                                                        <i class="fas fa-check me-1"></i>选择
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                未找到参考蓝本，请先在小说列表中设置参考蓝本。
                            </div>
                        {% endif %}
                    </div>
                </div>
                <!-- 分析内容展示区，维度/章节二级tab -->
                <div class="card" id="analysisDisplayCard" style="display: none;">
                    <div class="card-header bg-light">
                        <ul class="nav nav-tabs card-header-tabs" id="analysisTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="book-tab" data-bs-toggle="tab" data-bs-target="#book" type="button" role="tab" aria-controls="book" aria-selected="true">整本书</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">章节列表</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="analysisTabContent">
                            <div class="tab-pane fade show active" id="book" role="tabpanel" aria-labelledby="book-tab">
                                <div class="row">
                                    <div class="col-md-3">
                                        <h5 class="mb-3">整本书的分析维度</h5>
                                        <div class="list-group" id="dimensionList">
                                            <div class="text-center py-3">
                                                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                                <p class="small mt-2">加载中...</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <ul class="nav nav-tabs card-header-tabs" id="analysisResultTab" role="tablist">
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link active" id="result-tab" data-bs-toggle="tab" data-bs-target="#result" type="button" role="tab" aria-controls="result" aria-selected="true">分析结果</button>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link" id="reasoning-tab" data-bs-toggle="tab" data-bs-target="#reasoning" type="button" role="tab" aria-controls="reasoning" aria-selected="false">推理过程</button>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="card-body">
                                                <div class="tab-content" id="analysisResultTabContent">
                                                    <div class="tab-pane fade show active" id="result" role="tabpanel" aria-labelledby="result-tab">
                                                        <div id="analysisContent" class="markdown-body">
                                                            请从左侧选择一个分析维度，查看对应的分析结果。
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade" id="reasoning" role="tabpanel" aria-labelledby="reasoning-tab">
                                                        <div id="reasoningContent" class="markdown-body">
                                                            选择分析维度后，这里将显示详细的推理过程。
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                                <div class="row">
                                    <div class="col-md-3">
                                        <h5 class="mb-3">章节列表</h5>
                                        <div class="list-group" id="chapterList">
                                            <div class="text-center py-3">
                                                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                                <p class="small mt-2">加载中...</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <ul class="nav nav-tabs card-header-tabs" id="chapterAnalysisResultTab" role="tablist">
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link active" id="chapter-result-tab" data-bs-toggle="tab" data-bs-target="#chapter-result" type="button" role="tab" aria-controls="chapter-result" aria-selected="true">分析结果</button>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link" id="chapter-reasoning-tab" data-bs-toggle="tab" data-bs-target="#chapter-reasoning" type="button" role="tab" aria-controls="chapter-reasoning" aria-selected="false">推理过程</button>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="card-body">
                                                <div class="tab-content" id="chapterAnalysisResultTabContent">
                                                    <div class="tab-pane fade show active" id="chapter-result" role="tabpanel" aria-labelledby="chapter-result-tab">
                                                        <div id="chapterAnalysisContent" class="markdown-body">
                                                            请从左侧选择一个章节，查看对应的分析结果。
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade" id="chapter-reasoning" role="tabpanel" aria-labelledby="chapter-reasoning-tab">
                                                        <div id="chapterReasoningContent" class="markdown-body">
                                                            选择章节后，这里将显示详细的推理过程。
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识库/预设模板 -->
        <div class="tab-pane fade" id="knowledge" role="tabpanel" aria-labelledby="knowledge-tab">
            <div class="console-content">
                <h2 class="section-title"><i class="fas fa-book me-2"></i>知识库与预设模板</h2>
                <div class="card mb-4">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">参考蓝本知识库</h5>
                        <div>
                            <button id="readAnalysisBtn" class="btn btn-sm btn-light me-2">
                                <i class="fas fa-sync-alt me-1"></i>读取分析结果
                            </button>
                            <button id="convertToTemplateBtn" class="btn btn-sm btn-light">
                                <i class="fas fa-magic me-1"></i>转化为预设模板
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="knowledgeBaseContent">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>使用步骤：</strong><br>
                                1. 请先在<a href="#analysis" class="alert-link" onclick="document.getElementById('analysis-tab').click()">分析展示</a>标签页选择一个参考蓝本<br>
                                2. 然后点击"读取分析结果"按钮，从分析展示中读取参考蓝本的分析结果
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">预设模板管理</h5>
                        <button id="newPresetBtn" class="btn btn-sm btn-light">
                            <i class="fas fa-plus me-1"></i>新建预设
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="list-group list-group-flush" id="presetList" style="max-height: 400px; overflow-y: auto;">
                                    <div class="text-center py-3">
                                        <small class="text-muted">暂无预设内容</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0" id="presetEditorTitle">编辑预设</h5>
                                            <div>
                                                <button id="savePresetBtn" class="btn btn-sm btn-success">
                                                    <i class="fas fa-save me-1"></i>保存
                                                </button>
                                                <button id="deletePresetBtn" class="btn btn-sm btn-danger ms-1">
                                                    <i class="fas fa-trash me-1"></i>删除
                                                </button>
                                                <button id="readPresetBtn" class="btn btn-sm btn-info ms-1">
                                                    <i class="fas fa-book-reader me-1"></i>读取
                                                </button>
                                                <button id="expandPresetBtn" class="btn btn-sm btn-outline-info ms-1">
                                                    <i class="fas fa-expand-alt me-1"></i>展开查看
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="presetTitle" class="form-label">标题</label>
                                            <input type="text" class="form-control" id="presetTitle" placeholder="输入预设标题">
                                        </div>
                                        <div class="mb-3">
                                            <label for="presetContent" class="form-label">内容</label>
                                            <textarea class="form-control" id="presetContent" rows="10" placeholder="输入预设内容..."></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="presetCategory" class="form-label">分类</label>
                                            <select class="form-select" id="presetCategory">
                                                <option value="writing_prompt">写作提示</option>
                                                <option value="character">人物设定</option>
                                                <option value="plot">情节构思</option>
                                                <option value="scene">场景描写</option>
                                                <option value="knowledge_base">知识库</option>
                                                <option value="chapter_template">章节预设模板</option>
                                                <option value="other">其他</option>
                                            </select>
                                        </div>
                                        <input type="hidden" id="presetId" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自动写作 -->
        <div class="tab-pane fade" id="writing" role="tabpanel" aria-labelledby="writing-tab">
            <div class="console-content">
                <h2 class="section-title"><i class="fas fa-pen-fancy me-2"></i>自动写作</h2>
                <div class="card mb-4">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">写作控制台</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="templateSelect" class="form-label">选择预设模板</label>
                            <select class="form-select" id="templateSelect">
                                <option value="">-- 请选择预设模板 --</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="writingPrompt" class="form-label">写作提示</label>
                            <textarea class="form-control" id="writingPrompt" rows="3" placeholder="请输入写作提示..."></textarea>
                        </div>
                        <div class="d-grid">
                            <button id="startWritingBtn" class="btn btn-primary">
                                <i class="fas fa-play me-1"></i>开始写作
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">写作结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="writingResult" class="markdown-body">
                            写作结果将显示在这里。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    <!-- 应急修复按钮 -->
    <div id="emergencyFixContainer" style="position: fixed; bottom: 10px; right: 10px; z-index: 9999; display: none;">
        <button id="emergencyFixBtn" class="btn btn-danger btn-sm" onclick="window.fixJQuery()">
            <i class="fas fa-wrench"></i> 修复jQuery
        </button>
    </div>
{% endblock %}

{% block scripts %}
<!-- 确保jQuery已加载并可用 -->
<script>
// 确保jQuery已加载并可用
(function() {
    console.log('[控制台页面] 确保jQuery已加载...');

    // 如果内联jQuery加载器已经加载了jQuery，直接使用
    if (window.jQueryLoaded || typeof jQuery !== 'undefined') {
        console.log('[控制台页面] jQuery已加载，继续加载其他脚本');
        loadAllScripts();
    } else {
        console.log('[控制台页面] jQuery未加载，等待加载完成');

        // 等待jQuery加载完成
        document.addEventListener('jQueryLoaded', function() {
            console.log('[控制台页面] jQuery加载完成事件触发，继续加载其他脚本');
            loadAllScripts();
        });

        // 设置超时，如果等待太久，尝试再次加载
        setTimeout(function() {
            if (!window.jQueryLoaded && typeof jQuery === 'undefined') {
                console.error('[控制台页面] jQuery加载超时，尝试再次加载');

                // 尝试使用内联jQuery加载器加载jQuery
                if (typeof window.ensureJQuery === 'function') {
                    window.ensureJQuery(function() {
                        console.log('[控制台页面] jQuery通过ensureJQuery加载成功，继续加载其他脚本');
                        loadAllScripts();
                    });
                } else {
                    console.error('[控制台页面] ensureJQuery函数不存在，无法加载jQuery');
                    // 彻底报错，不再用最小jQuery替代品
                    alert('jQuery 加载失败，页面无法正常使用，请刷新页面或检查网络！');
                    return;
                }
            }
        }, 3000);
    }

    // 加载所有脚本
    function loadAllScripts() {
        console.log('[控制台页面] 开始加载所有脚本');

        // 创建一个加载脚本的函数
        function loadScript(src, callback) {
            console.log('[控制台页面] 加载脚本:', src);

            var script = document.createElement('script');
            script.src = src;

            script.onload = function() {
                console.log('[控制台页面] 脚本加载成功:', src);
                if (callback) callback();
            };

            script.onerror = function() {
                console.error('[控制台页面] 脚本加载失败:', src);
                if (callback) callback();
            };

            document.body.appendChild(script);
        }

        // 按顺序加载脚本
        var scripts = [
            "{{ url_for('static', filename='js/api-version-fix.js') }}",
            "{{ url_for('static', filename='js/api-timeout-fix.js') }}",
            "{{ url_for('static', filename='js/api-analysis-result-fix.js') }}",
            "{{ url_for('static', filename='js/console-fix.js') }}",
            "{{ url_for('static', filename='js/browser-console-capture.js') }}",
            "{{ url_for('static', filename='js/create-knowledge-base.js') }}",
            "{{ url_for('static', filename='js/analysis-understanding-generator.js') }}",
            "{{ url_for('static', filename='js/v3/template_selector.js') }}",
            "{{ url_for('static', filename='js/knowledge-base-compact.js') }}",
            "{{ url_for('static', filename='js/analysis-result-fix.js') }}",
            "{{ url_for('static', filename='js/reanalyze-fix.js') }}",
            "{{ url_for('static', filename='js/chapter-reasoning-fix.js') }}",
            "{{ url_for('static', filename='js/auto-fix-chapter-reasoning.js') }}",
            "{{ url_for('static', filename='js/real-reasoning-fix.js') }}",
            "{{ url_for('static', filename='js/analysis-display-fix.js') }}",
            "{{ url_for('static', filename='js/analysis-content-fix.js') }}",
            "{{ url_for('static', filename='js/knowledge-base-result-fix.js') }}",
            "{{ url_for('static', filename='js/knowledge-base-analysis-fix.js') }}"
        ];

        // 最后加载控制台主脚本
        var mainScript = "{{ url_for('static', filename='js/v3/console.js') }}";

        // 递归加载脚本
        function loadScriptsSequentially(index) {
            if (index >= scripts.length) {
                // 所有脚本加载完成，加载主脚本
                console.log('[控制台页面] 所有辅助脚本加载完成，加载主脚本');
                // 强校验 jQuery 是否可用
                if (typeof jQuery === 'undefined') {
                    alert('jQuery 加载失败，页面无法正常使用，请刷新重试！');
                    return;
                }

                // 确保jQuery.fn.on方法存在
                if (typeof jQuery.fn.on !== 'function') {
                    console.log('[控制台页面] jQuery.fn.on方法不存在，尝试使用专门的修复函数');

                    // 尝试使用专门的jQuery修复函数
                    if (typeof window.fixJQueryOnMethodDirectly === 'function') {
                        console.log('[控制台页面] 调用直接jQuery修复函数');
                        window.fixJQueryOnMethodDirectly();
                    } else if (typeof window.fixJQueryOnMethod === 'function') {
                        console.log('[控制台页面] 调用专门的jQuery修复函数');
                        window.fixJQueryOnMethod();
                    } else {
                        console.log('[控制台页面] 专门的修复函数不可用，添加基本实现');
                        jQuery.fn.on = function(event, selector, callback) {
                            if (typeof selector === 'function') {
                                callback = selector;
                                selector = null;
                            }

                            this.each(function() {
                                this.addEventListener(event, callback);
                            });

                            return this;
                        };
                    }
                }
                loadScript(mainScript, function() {
                    console.log('[控制台页面] 所有脚本加载完成');

                    // 确保控制台初始化
                    if (typeof initConsole === 'function' && typeof jQuery !== 'undefined') {
                        console.log('[控制台页面] 初始化控制台');
                        initConsole(jQuery);
                    } else {
                        console.error('[控制台页面] 无法初始化控制台，initConsole函数或jQuery不存在');
                    }
                });
                return;
            }

            // 加载当前脚本，然后加载下一个
            loadScript(scripts[index], function() {
                loadScriptsSequentially(index + 1);
            });
        }

        // 开始加载脚本
        loadScriptsSequentially(0);
    }
})();
</script>

<!-- 添加全局错误处理 -->
<script>
// 添加全局错误处理
window.addEventListener('error', function(event) {
    console.error('[全局错误处理] 捕获到错误:', event.message, '来源:', event.filename, '行:', event.lineno);

    // 检测jQuery错误并显示应急修复按钮
    if (event.message && (
        event.message.includes('$ is not defined') ||
        event.message.includes('$(...).on is not a function') ||
        event.message.includes('$(...).ready is not a function') ||
        event.message.includes('$(...).tab is not a function') ||
        event.message.includes('$(...).trigger is not a function') ||
        event.message.includes('$(...).off is not a function') ||
        event.message.includes('jQuery')
    )) {
        // 显示应急修复按钮
        var container = document.getElementById('emergencyFixContainer');
        if (container) {
            container.style.display = 'block';
        }

        // 尝试自动修复
        if (typeof window.fixJQuery === 'function') {
            console.log('[全局错误处理] 尝试自动修复jQuery');
            window.fixJQuery();
        }

        console.error('[全局错误处理] 检测到jQuery相关错误，尝试修复');

        // 重新加载出错的脚本
        if (event.filename) {
            console.log('[全局错误处理] 尝试重新加载出错的脚本:', event.filename);
            var script = document.createElement('script');
            script.src = event.filename + '?_=' + new Date().getTime(); // 添加时间戳防止缓存
            document.body.appendChild(script);
        }
    }
});
</script>
{% endblock %}
