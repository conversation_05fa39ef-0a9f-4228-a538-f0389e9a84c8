/**
 * 九猫小说分析写作系统 - API分析结果修复脚本
 * 
 * 此脚本用于修复API分析结果返回格式不一致的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[API分析结果修复] 初始化...');
    
    // 配置
    const CONFIG = {
        debug: true,
        apiPaths: {
            novelAnalysis: '/api/novel/{novelId}/analysis/{dimension}',
            novelReasoning: '/api/novel/{novelId}/analysis/{dimension}/reasoning_content',
            chapterAnalysis: '/api/novel/{novelId}/chapter/{chapterId}/analysis/{dimension}',
            chapterReasoning: '/api/novel/{novelId}/chapter/{chapterId}/analysis/{dimension}/reasoning_content'
        }
    };
    
    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[API分析结果修复] ${message}`);
        }
    }
    
    // 初始化
    function init() {
        debugLog('开始初始化...');
        
        // 确保jQuery已加载
        if (typeof window.ensureJQuery !== 'function') {
            debugLog('ensureJQuery函数不存在，等待页面加载完成后再试', 'warn');
            
            // 等待页面加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', init);
                return;
            }
            
            // 尝试直接使用jQuery
            if (typeof jQuery !== 'undefined') {
                debugLog('jQuery已加载，继续初始化');
                initWithJQuery(jQuery);
            } else {
                debugLog('jQuery未加载，无法初始化', 'error');
                return;
            }
        } else {
            window.ensureJQuery(function($) {
                debugLog('jQuery已加载，继续初始化');
                initWithJQuery($);
            });
        }
    }
    
    // 使用jQuery初始化
    function initWithJQuery($) {
        debugLog('使用jQuery初始化');
        
        // 拦截Ajax请求
        interceptAjaxRequests($);
    }
    
    // 拦截Ajax请求
    function interceptAjaxRequests($) {
        debugLog('拦截Ajax请求');
        
        // 保存原始的$.ajax方法
        const originalAjax = $.ajax;
        
        // 重写$.ajax方法
        $.ajax = function(options) {
            // 检查是否是分析结果相关的请求
            if (options.url && typeof options.url === 'string') {
                const url = options.url;
                
                // 检查是否是小说分析结果请求
                if (url.includes('/novel/') && url.includes('/analysis/')) {
                    debugLog(`拦截到分析结果请求: ${url}`);
                    
                    // 保存原始的success回调
                    const originalSuccess = options.success;
                    
                    // 重写success回调
                    options.success = function(response) {
                        // 修复分析结果格式
                        const fixedResponse = fixAnalysisResultFormat(response, url);
                        
                        // 调用原始的success回调
                        if (originalSuccess) {
                            originalSuccess(fixedResponse);
                        }
                    };
                }
            }
            
            // 调用原始的$.ajax方法
            return originalAjax.apply($, arguments);
        };
    }
    
    // 修复分析结果格式
    function fixAnalysisResultFormat(response, url) {
        debugLog(`修复分析结果格式: ${url}`);
        
        // 如果响应为空或不是对象，直接返回
        if (!response || typeof response !== 'object') {
            debugLog('响应为空或不是对象，无法修复', 'warn');
            return response;
        }
        
        // 检查是否是推理过程请求
        if (url.includes('/reasoning_content')) {
            debugLog('检测到推理过程请求');
            
            // 如果响应中没有reasoning_content字段，但有content字段
            if (!response.reasoning_content && response.content) {
                debugLog('响应中没有reasoning_content字段，但有content字段，进行修复');
                response.reasoning_content = response.content;
            }
            
            return response;
        }
        
        // 检查是否是分析结果请求
        if (url.includes('/analysis/') && !url.includes('/reasoning_content')) {
            debugLog('检测到分析结果请求');
            
            // 如果响应中没有result字段，但有content字段
            if (!response.result && response.content) {
                debugLog('响应中没有result字段，但有content字段，进行修复');
                response.result = {
                    content: response.content,
                    reasoning_content: response.reasoning_content || '',
                    created_at: response.created_at || '',
                    updated_at: response.updated_at || '',
                    metadata: response.metadata || {}
                };
            }
            
            // 如果响应中有result字段，但result中没有content字段
            if (response.result && !response.result.content && response.content) {
                debugLog('响应中有result字段，但result中没有content字段，进行修复');
                response.result.content = response.content;
            }
            
            return response;
        }
        
        return response;
    }
    
    // 初始化
    init();
    
    // 导出修复函数
    window.fixAnalysisResultFormat = fixAnalysisResultFormat;
    
    console.log('[API分析结果修复] 初始化完成');
})();
