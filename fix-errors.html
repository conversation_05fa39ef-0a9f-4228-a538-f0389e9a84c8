<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫系统错误修复</title>
    
    <!-- 修复脚本 -->
    <script src="unified-error-handler.js"></script>
    <script src="console-logger-fix.js"></script>
    <script src="global-error-handler-fix.js"></script>
    
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #343a40;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        h2 {
            color: #495057;
            margin-top: 20px;
        }
        .card {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .card-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #007bff;
        }
        .btn {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            border: none;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #0069d9;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
        }
        .success {
            border-left-color: #28a745;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>九猫系统错误修复</h1>
        <p>此页面用于修复九猫系统中的JavaScript错误。点击下面的按钮测试各种错误情况。</p>
        
        <div class="card">
            <div class="card-title">错误处理测试</div>
            <button class="btn" id="test-error">测试普通错误</button>
            <button class="btn" id="test-promise-error">测试Promise错误</button>
            <button class="btn" id="test-script-error">测试Script Error</button>
            <div class="result" id="error-result"></div>
        </div>
        
        <div class="card">
            <div class="card-title">控制台日志测试</div>
            <button class="btn" id="test-log">测试日志</button>
            <button class="btn" id="test-warn">测试警告</button>
            <button class="btn" id="test-info">测试信息</button>
            <div class="result" id="log-result"></div>
        </div>
        
        <div class="card">
            <div class="card-title">修复说明</div>
            <p>本页面包含以下修复脚本：</p>
            <ul>
                <li><strong>unified-error-handler.js</strong> - 统一错误处理脚本，捕获和处理各种JavaScript错误</li>
                <li><strong>console-logger-fix.js</strong> - 控制台日志修复脚本，确保错误信息正确显示</li>
                <li><strong>global-error-handler-fix.js</strong> - 全局错误处理修复脚本，修复全局错误处理相关问题</li>
            </ul>
            <p>使用方法：</p>
            <pre><code>&lt;!-- 在HTML头部引入这些脚本 --&gt;
&lt;script src="unified-error-handler.js"&gt;&lt;/script&gt;
&lt;script src="console-logger-fix.js"&gt;&lt;/script&gt;
&lt;script src="global-error-handler-fix.js"&gt;&lt;/script&gt;</code></pre>
        </div>
        
        <div class="card">
            <div class="card-title">应用到九猫系统</div>
            <p>要将这些修复应用到九猫系统，请将这些脚本复制到九猫系统的静态资源目录，并在HTML模板中引用它们。</p>
            <button class="btn" id="copy-instructions">复制应用说明</button>
            <div class="result" id="copy-result"></div>
        </div>
    </div>
    
    <script>
        // 测试普通错误
        document.getElementById('test-error').addEventListener('click', function() {
            try {
                // 故意制造一个错误
                const obj = null;
                obj.nonExistentMethod();
            } catch (e) {
                document.getElementById('error-result').textContent = '成功捕获错误: ' + e.message;
                document.getElementById('error-result').className = 'result success';
            }
        });
        
        // 测试Promise错误
        document.getElementById('test-promise-error').addEventListener('click', function() {
            // 创建一个会被拒绝的Promise
            new Promise(function(resolve, reject) {
                reject(new Error('测试Promise错误'));
            }).catch(function(error) {
                document.getElementById('error-result').textContent = '成功捕获Promise错误: ' + error.message;
                document.getElementById('error-result').className = 'result success';
            });
        });
        
        // 测试Script Error
        document.getElementById('test-script-error').addEventListener('click', function() {
            // 模拟一个Script Error
            const scriptError = new Event('error');
            scriptError.message = 'Script error.';
            window.dispatchEvent(scriptError);
            
            document.getElementById('error-result').textContent = '已触发Script Error，请查看控制台';
            document.getElementById('error-result').className = 'result';
        });
        
        // 测试日志
        document.getElementById('test-log').addEventListener('click', function() {
            console.log('这是一条测试日志');
            document.getElementById('log-result').textContent = '已输出测试日志，请查看控制台';
            document.getElementById('log-result').className = 'result success';
        });
        
        // 测试警告
        document.getElementById('test-warn').addEventListener('click', function() {
            console.warn('这是一条测试警告');
            document.getElementById('log-result').textContent = '已输出测试警告，请查看控制台';
            document.getElementById('log-result').className = 'result success';
        });
        
        // 测试信息
        document.getElementById('test-info').addEventListener('click', function() {
            console.info('这是一条测试信息');
            document.getElementById('log-result').textContent = '已输出测试信息，请查看控制台';
            document.getElementById('log-result').className = 'result success';
        });
        
        // 复制应用说明
        document.getElementById('copy-instructions').addEventListener('click', function() {
            const instructions = `
1. 将以下文件复制到九猫系统的静态资源目录（例如 src/web/static/js/）：
   - unified-error-handler.js
   - console-logger-fix.js
   - global-error-handler-fix.js

2. 在HTML模板文件（例如 base.html 或 layout.html）的头部添加以下代码：

   <!-- 错误处理修复脚本 -->
   <script src="/static/js/unified-error-handler.js"></script>
   <script src="/static/js/console-logger-fix.js"></script>
   <script src="/static/js/global-error-handler-fix.js"></script>

3. 重启九猫系统，刷新浏览器页面。
`;
            
            document.getElementById('copy-result').innerHTML = '<pre>' + instructions + '</pre>';
            document.getElementById('copy-result').className = 'result success';
            
            // 尝试复制到剪贴板
            try {
                navigator.clipboard.writeText(instructions).then(function() {
                    alert('应用说明已复制到剪贴板！');
                });
            } catch (e) {
                console.warn('无法复制到剪贴板:', e);
            }
        });
    </script>
</body>
</html>
