"""
九猫系统日志过滤器模块
提供日志过滤和内存优化功能
"""
import logging
import time
import re
from typing import Dict, List, Set, Optional, Any
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class LogFilter:
    """日志过滤器，用于减少重复日志和优化内存使用"""

    def __init__(self):
        """初始化日志过滤器"""
        # 存储最近的日志消息哈希值，用于检测重复
        self.recent_log_hashes: Dict[int, Dict[str, float]] = {}
        # 存储日志消息模式和最后出现时间
        self.log_patterns: Dict[str, float] = {}
        # 重复日志计数
        self.duplicate_count: Dict[int, Dict[str, int]] = {}
        # 上次清理时间
        self.last_cleanup_time = time.time()
        # 清理间隔（秒）
        self.cleanup_interval = 300  # 5分钟
        # 启动时间
        self.startup_time = time.time()
        # 启动模式持续时间（秒）
        self.startup_duration = 30  # 30秒
        # 启动阶段需要过滤的额外模式
        self.startup_filter_patterns = [
            "数据库连接已创建",
            "数据库连接已检出",
            "数据库连接已归还",
            "数据库会话已关闭",
            "请求开始时会话已准备",
            "连接池使用率",
            "加载首页",
            "找到",
            "检查",
            "初始化",
            "尝试加载",
            "已加载",
            "开始扫描",
            "获取进度信息",
            "成功解析",
            "图表初始化"
        ]

    def is_duplicate(self, novel_id: int, message: str, level: str,
                    dimension: Optional[str] = None, threshold: int = 5) -> bool:
        """
        检查日志是否是重复的

        Args:
            novel_id: 小说ID
            message: 日志消息
            level: 日志级别
            dimension: 分析维度
            threshold: 重复阈值（秒）

        Returns:
            如果是重复日志，返回True；否则返回False
        """
        # 生成日志消息的唯一标识
        log_key = f"{level}:{dimension}:{message}"
        log_hash = hash(log_key)

        # 确保novel_id在字典中
        if novel_id not in self.recent_log_hashes:
            self.recent_log_hashes[novel_id] = {}
            self.duplicate_count[novel_id] = {}

        current_time = time.time()

        # 检查是否是重复日志
        if log_hash in self.recent_log_hashes[novel_id]:
            last_time = self.recent_log_hashes[novel_id][log_hash]
            if current_time - last_time < threshold:
                # 更新重复计数
                if log_hash not in self.duplicate_count[novel_id]:
                    self.duplicate_count[novel_id][log_hash] = 1
                else:
                    self.duplicate_count[novel_id][log_hash] += 1
                return True

        # 更新最近日志哈希值
        self.recent_log_hashes[novel_id][log_hash] = current_time

        # 定期清理旧的日志哈希值
        if current_time - self.last_cleanup_time > self.cleanup_interval:
            self._cleanup_old_hashes()

        return False

    def is_polling_log(self, message: str) -> bool:
        """
        检查是否是轮询类日志

        Args:
            message: 日志消息

        Returns:
            如果是轮询日志，返回True；否则返回False
        """
        # 轮询日志的特征
        polling_patterns = [
            r'收到日志请求.*novel_id=\d+',
            r'找到小说标题.*',
            r'没有找到小说ID \d+ 的日志',
            r'小说ID \d+ 的分析状态',
            r'返回日志响应.*',
            r'按级别.*筛选后',
            r'按时间戳.*筛选后',
            r'最终返回.*条日志',
            r'添加了(欢迎|状态)消息'
        ]

        # 检查消息是否匹配任何轮询模式
        for pattern in polling_patterns:
            if re.search(pattern, message):
                return True

        return False

    def is_in_startup_mode(self) -> bool:
        """
        检查是否处于启动模式

        Returns:
            如果处于启动模式，返回True；否则返回False
        """
        # 检查是否超过启动持续时间
        return time.time() - self.startup_time < self.startup_duration

    def is_startup_filtered(self, message: str) -> bool:
        """
        检查日志是否应该在启动阶段被过滤

        Args:
            message: 日志消息

        Returns:
            如果应该被过滤，返回True；否则返回False
        """
        # 如果不在启动模式，不过滤
        if not self.is_in_startup_mode():
            return False

        # 检查是否匹配启动过滤模式
        for pattern in self.startup_filter_patterns:
            if pattern in message:
                return True

        return False

    def is_important_log(self, message: str, level: str, dimension: Optional[str] = None) -> bool:
        """
        检查是否是重要日志

        Args:
            message: 日志消息
            level: 日志级别
            dimension: 分析维度

        Returns:
            如果是重要日志，返回True；否则返回False
        """
        # 在启动模式下，只有错误和警告日志是重要的
        if self.is_in_startup_mode():
            # 错误和警告日志始终是重要的
            if level in ['error', 'warning']:
                return True

            # 启动阶段的重要关键词
            startup_important_keywords = [
                '错误', '警告', '失败', '成功', '完成', '启动', '初始化完成',
                '内存使用', 'CPU使用', '资源使用', '清理', '重置', '连接池'
            ]

            for keyword in startup_important_keywords:
                if keyword in message:
                    return True

            # 启动阶段过滤更多日志
            return False

        # 正常模式下的处理
        # 错误和警告日志始终是重要的
        if level in ['error', 'warning']:
            return True

        # 包含特定关键词的日志是重要的
        important_keywords = [
            '分析完成', '处理时间', '令牌使用量', '费用', '分析结果',
            '进度更新', '分析块', '完成度', '%', '耗时', 'API调用',
            '开始分析', '结束分析', '成功', '失败', '错误', '警告',
            '内存使用', 'CPU使用', '资源使用', '清理', '重置'
        ]

        for keyword in important_keywords:
            if keyword in message:
                return True

        # 特定维度的日志可能是重要的
        if dimension and dimension != 'system':
            return True

        return False

    def _cleanup_old_hashes(self, max_age: int = 3600) -> None:
        """
        清理旧的日志哈希值

        Args:
            max_age: 最大保留时间（秒）
        """
        current_time = time.time()
        self.last_cleanup_time = current_time

        # 清理每个小说的旧哈希值
        for novel_id in list(self.recent_log_hashes.keys()):
            for log_hash in list(self.recent_log_hashes[novel_id].keys()):
                if current_time - self.recent_log_hashes[novel_id][log_hash] > max_age:
                    del self.recent_log_hashes[novel_id][log_hash]
                    if log_hash in self.duplicate_count[novel_id]:
                        del self.duplicate_count[novel_id][log_hash]

            # 如果小说没有日志哈希值，删除该条目
            if not self.recent_log_hashes[novel_id]:
                del self.recent_log_hashes[novel_id]
                if novel_id in self.duplicate_count:
                    del self.duplicate_count[novel_id]

    def filter_logs(self, logs: List[Dict[str, Any]], novel_id: int,
                   max_logs: int = 1000, memory_critical: bool = False) -> List[Dict[str, Any]]:
        """
        过滤日志列表，减少内存使用

        Args:
            logs: 日志列表
            novel_id: 小说ID
            max_logs: 最大保留日志数量
            memory_critical: 是否处于内存紧张状态

        Returns:
            过滤后的日志列表
        """
        if not logs:
            return logs

        # 在启动模式下更激进地过滤日志
        in_startup_mode = self.is_in_startup_mode()

        # 如果在启动模式下，或者内存紧张，或者日志数量超过限制，进行过滤
        if not (len(logs) <= max_logs and not memory_critical and not in_startup_mode):
            # 标记重要日志
            for log in logs:
                # 检查是否是启动阶段应该过滤的日志
                message = log.get('message', '')
                if in_startup_mode and self.is_startup_filtered(message):
                    log['_important'] = False
                else:
                    log['_important'] = self.is_important_log(
                        message,
                        log.get('level', 'info'),
                        log.get('dimension')
                    )

            # 确定过滤策略
            if memory_critical or in_startup_mode:
                # 在内存紧张或启动模式下，只保留重要日志
                filtered_logs = [log for log in logs if log.get('_important', False)]

                if memory_critical:
                    logger.warning(f"内存紧张，过滤日志：原有 {len(logs)} 条，保留 {len(filtered_logs)} 条重要日志")
                elif in_startup_mode:
                    # 在启动模式下，不记录过滤日志的详细信息，避免产生更多日志
                    pass
            else:
                # 正常模式下，保留所有重要日志和最近的非重要日志
                important_logs = [log for log in logs if log.get('_important', False)]
                unimportant_logs = [log for log in logs if not log.get('_important', False)]

                # 计算要保留的非重要日志数量
                keep_unimportant = max(0, max_logs - len(important_logs))

                # 保留最近的非重要日志
                kept_unimportant = unimportant_logs[-keep_unimportant:] if keep_unimportant > 0 else []

                # 合并日志并按时间排序
                filtered_logs = important_logs + kept_unimportant
                filtered_logs.sort(key=lambda x: x.get('timestamp', ''))

                logger.info(f"日志过滤：原有 {len(logs)} 条，保留 {len(filtered_logs)} 条（{len(important_logs)} 条重要日志）")
        else:
            # 不需要过滤
            filtered_logs = logs

        # 移除临时标记
        for log in filtered_logs:
            if '_important' in log:
                del log['_important']

        return filtered_logs

    def get_duplicate_summary(self, novel_id: int) -> Dict[str, int]:
        """
        获取重复日志摘要

        Args:
            novel_id: 小说ID

        Returns:
            重复日志摘要
        """
        if novel_id not in self.duplicate_count:
            return {}

        return self.duplicate_count[novel_id].copy()

    def clear_duplicate_count(self, novel_id: Optional[int] = None) -> None:
        """
        清除重复日志计数

        Args:
            novel_id: 小说ID，如果为None则清除所有
        """
        if novel_id is None:
            self.duplicate_count.clear()
        elif novel_id in self.duplicate_count:
            self.duplicate_count[novel_id].clear()

# 创建全局日志过滤器实例
log_filter = LogFilter()

def is_duplicate_log(novel_id: int, message: str, level: str,
                    dimension: Optional[str] = None, threshold: int = 5) -> bool:
    """
    检查日志是否是重复的

    Args:
        novel_id: 小说ID
        message: 日志消息
        level: 日志级别
        dimension: 分析维度
        threshold: 重复阈值（秒）

    Returns:
        如果是重复日志，返回True；否则返回False
    """
    global log_filter
    return log_filter.is_duplicate(novel_id, message, level, dimension, threshold)

def is_polling_log(message: str) -> bool:
    """
    检查是否是轮询类日志

    Args:
        message: 日志消息

    Returns:
        如果是轮询日志，返回True；否则返回False
    """
    global log_filter
    return log_filter.is_polling_log(message)

def filter_logs(logs: List[Dict[str, Any]], novel_id: int,
               max_logs: int = 1000, memory_critical: bool = False) -> List[Dict[str, Any]]:
    """
    过滤日志列表，减少内存使用

    Args:
        logs: 日志列表
        novel_id: 小说ID
        max_logs: 最大保留日志数量
        memory_critical: 是否处于内存紧张状态

    Returns:
        过滤后的日志列表
    """
    global log_filter
    return log_filter.filter_logs(logs, novel_id, max_logs, memory_critical)

def get_duplicate_summary(novel_id: int) -> Dict[str, int]:
    """
    获取重复日志摘要

    Args:
        novel_id: 小说ID

    Returns:
        重复日志摘要
    """
    global log_filter
    return log_filter.get_duplicate_summary(novel_id)

def clear_duplicate_count(novel_id: Optional[int] = None) -> None:
    """
    清除重复日志计数

    Args:
        novel_id: 小说ID，如果为None则清除所有
    """
    global log_filter
    log_filter.clear_duplicate_count(novel_id)
