"""
九猫小说分析写作系统 - 分析结果修复脚本

此脚本用于修复数据库中的分析结果表，确保它包含必要的字段，
特别是添加缺失的reasoning_content字段。
"""
import os
import sys
import sqlite3
import logging
import traceback
# from datetime import datetime  # 暂时不需要

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('fix_analysis_results')

# 数据库路径
DB_PATH = 'novels.db'  # 主数据库文件在根目录

def check_table_structure(conn, table_name):
    """检查表结构，返回列名列表"""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]
    logger.info(f"表 {table_name} 的列: {', '.join(column_names)}")
    return column_names

def add_missing_column(conn, table_name, column_name, column_type):
    """添加缺失的列"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")
        conn.commit()
        logger.info(f"成功添加列 {column_name} 到表 {table_name}")
        return True
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            logger.warning(f"列 {column_name} 已存在于表 {table_name}")
            return True
        logger.error(f"添加列 {column_name} 到表 {table_name} 时出错: {str(e)}")
        return False

def fix_analysis_results_table(conn):
    """修复分析结果表"""
    # 检查分析结果表结构
    columns = check_table_structure(conn, 'analysis_results')

    # 添加缺失的列
    if 'reasoning_content' not in columns:
        add_missing_column(conn, 'analysis_results', 'reasoning_content', 'TEXT')

    # 检查章节分析结果表结构
    columns = check_table_structure(conn, 'chapter_analysis_results')

    # 添加缺失的列
    if 'reasoning_content' not in columns:
        add_missing_column(conn, 'chapter_analysis_results', 'reasoning_content', 'TEXT')

def update_analysis_results(conn):
    """更新分析结果，从元数据中提取推理过程"""
    cursor = conn.cursor()

    # 获取所有分析结果
    cursor.execute("SELECT id, dimension, analysis_metadata FROM analysis_results WHERE reasoning_content IS NULL OR reasoning_content = ''")
    results = cursor.fetchall()
    logger.info(f"找到 {len(results)} 条缺少推理过程的分析结果")

    updated_count = 0
    for result in results:
        result_id, dimension, metadata = result
        if not metadata:
            continue

        try:
            # 尝试从元数据中提取推理过程
            import json
            metadata_dict = json.loads(metadata) if isinstance(metadata, str) else metadata

            reasoning_content = None
            # 检查元数据中是否有推理过程
            if isinstance(metadata_dict, dict):
                if 'reasoning_content' in metadata_dict:
                    reasoning_content = metadata_dict['reasoning_content']
                elif 'reasoning' in metadata_dict:
                    reasoning_content = metadata_dict['reasoning']
                elif 'process' in metadata_dict:
                    reasoning_content = metadata_dict['process']

            if reasoning_content:
                # 更新分析结果
                cursor.execute(
                    "UPDATE analysis_results SET reasoning_content = ? WHERE id = ?",
                    (reasoning_content, result_id)
                )
                updated_count += 1
                logger.info(f"已更新分析结果 ID={result_id}, 维度={dimension}")
        except Exception as e:
            logger.error(f"处理分析结果 ID={result_id} 时出错: {str(e)}")
            logger.error(traceback.format_exc())

    conn.commit()
    logger.info(f"成功更新 {updated_count}/{len(results)} 条分析结果")

def main():
    """主函数"""
    logger.info("开始修复分析结果表")

    # 检查数据库文件是否存在
    if not os.path.exists(DB_PATH):
        logger.error(f"数据库文件 {DB_PATH} 不存在")
        return

    try:
        # 连接数据库
        conn = sqlite3.connect(DB_PATH)

        # 修复分析结果表
        fix_analysis_results_table(conn)

        # 更新分析结果
        update_analysis_results(conn)

        # 关闭连接
        conn.close()

        logger.info("分析结果表修复完成")
    except Exception as e:
        logger.error(f"修复分析结果表时出错: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
