{"name": "npm-registry-fetch", "version": "13.1.1", "description": "Fetch-based http client for use with npm registry APIs", "main": "lib", "files": ["bin/", "lib/"], "scripts": {"eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "postversion": "npm publish", "test": "tap", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/npm-registry-fetch.git"}, "keywords": ["npm", "registry", "fetch"], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"make-fetch-happen": "^10.0.6", "minipass": "^3.1.6", "minipass-fetch": "^2.0.3", "minipass-json-stream": "^1.0.1", "minizlib": "^2.1.2", "npm-package-arg": "^9.0.1", "proc-log": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.3.2", "cacache": "^16.0.2", "nock": "^13.2.4", "require-inject": "^1.4.4", "ssri": "^9.0.0", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "test-ignore": "test[\\\\/](util|cache)[\\\\/]"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.3.2"}}