"""
测试九猫系统启动
"""
import os
import sys
import time
import logging
import subprocess
import requests
import signal

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_startup():
    """测试九猫系统启动"""
    logger.info("开始测试九猫系统启动")
    
    # 检查是否存在run.py
    if not os.path.exists('run.py'):
        logger.error("未找到run.py，无法启动系统")
        return False
    
    # 启动系统
    logger.info("启动九猫系统...")
    process = subprocess.Popen(
        [sys.executable, 'run.py'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # 等待系统启动
    logger.info("等待系统启动...")
    time.sleep(5)
    
    # 检查系统是否正常运行
    try:
        logger.info("测试连接到系统...")
        response = requests.get('http://localhost:5001/connection-test', timeout=5)
        if response.status_code == 200:
            logger.info("系统已成功启动！")
            success = True
        else:
            logger.error(f"系统可能未正常启动。状态码: {response.status_code}")
            success = False
    except requests.exceptions.RequestException as e:
        logger.error(f"无法连接到系统: {str(e)}")
        success = False
    
    # 终止进程
    logger.info("终止系统进程...")
    process.send_signal(signal.SIGTERM)
    process.wait(timeout=5)
    
    return success

if __name__ == '__main__':
    if test_startup():
        logger.info("测试成功！九猫系统可以正常启动。")
        sys.exit(0)
    else:
        logger.error("测试失败！九猫系统无法正常启动。")
        sys.exit(1)
