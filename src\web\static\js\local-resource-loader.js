/**
 * 九猫系统 - 本地资源加载器
 * 版本: 1.0.0
 * 
 * 该脚本专门用于处理资源加载错误，优先使用本地资源
 * 解决CDN资源连接超时问题
 */

(function() {
    console.log('[九猫修复] 本地资源加载器已加载');
    
    // 检查是否已应用此修复
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded && window.__nineCatsFixes.loaded['local-resource-loader']) {
        console.log('[九猫修复] 本地资源加载器已经应用，跳过');
        return;
    }
    
    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {} };
    
    // 本地资源映射
    const localResources = {
        // jQuery
        jquery: '/static/js/lib/jquery.min.js',
        // Bootstrap JS
        bootstrap: '/static/js/lib/bootstrap.bundle.min.js',
        // Bootstrap CSS
        bootstrapCss: '/static/css/bootstrap.min.css',
        // Font Awesome
        fontawesome: '/static/css/fontawesome.min.css',
        // Chart.js
        chartjs: '/static/js/lib/chart.min.js'
    };
    
    // 资源状态跟踪
    const resourceState = {
        loaded: {},            // 已成功加载的资源
        failed: {},            // 加载失败的资源
        retries: {},           // 重试次数
        maxRetries: 2,         // 最大重试次数
        monitored: new Set(),  // 正在监控的资源URL
        pendingFallbacks: []   // 待处理的备用加载
    };
    
    // 立即检查并加载关键资源
    function checkAndLoadCriticalResources() {
        // 检查jQuery
        if (typeof jQuery === 'undefined') {
            console.log('[九猫修复] jQuery未检测到，加载本地版本');
            loadLocalResource('jquery', 'script');
        }
        
        // 检查Bootstrap
        if (typeof bootstrap === 'undefined') {
            console.log('[九猫修复] Bootstrap未检测到，加载本地版本');
            loadLocalResource('bootstrap', 'script');
        }
        
        // 检查Bootstrap CSS
        let hasBootstrapCSS = false;
        document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            if (link.href && link.href.includes('bootstrap') && link.sheet) {
                hasBootstrapCSS = true;
            }
        });
        
        if (!hasBootstrapCSS) {
            console.log('[九猫修复] Bootstrap CSS未检测到或加载失败，加载本地版本');
            loadLocalResource('bootstrapCss', 'style');
        }
        
        // 检查Font Awesome
        let hasFontAwesome = false;
        document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            if (link.href && (link.href.includes('fontawesome') || link.href.includes('font-awesome')) && link.sheet) {
                hasFontAwesome = true;
            }
        });
        
        if (!hasFontAwesome) {
            console.log('[九猫修复] Font Awesome未检测到或加载失败，加载本地版本');
            loadLocalResource('fontawesome', 'style');
        }
    }
    
    // 加载本地资源
    function loadLocalResource(resourceType, elementType) {
        if (!localResources[resourceType]) {
            console.error('[九猫修复] 未找到本地资源:', resourceType);
            return;
        }
        
        const resourcePath = localResources[resourceType];
        console.log('[九猫修复] 加载本地资源:', resourcePath);
        
        if (elementType === 'script') {
            const script = document.createElement('script');
            script.src = resourcePath;
            script.async = false; // 确保按顺序执行
            
            // 添加加载事件监听
            script.addEventListener('load', function() {
                console.log('[九猫修复] 本地资源加载成功:', resourcePath);
                resourceState.loaded[resourcePath] = true;
            });
            
            script.addEventListener('error', function() {
                console.error('[九猫修复] 本地资源加载失败:', resourcePath);
                resourceState.failed[resourcePath] = true;
            });
            
            document.head.appendChild(script);
        } else if (elementType === 'style') {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = resourcePath;
            
            // 添加加载事件监听
            link.addEventListener('load', function() {
                console.log('[九猫修复] 本地样式表加载成功:', resourcePath);
                resourceState.loaded[resourcePath] = true;
            });
            
            link.addEventListener('error', function() {
                console.error('[九猫修复] 本地样式表加载失败:', resourcePath);
                resourceState.failed[resourcePath] = true;
            });
            
            document.head.appendChild(link);
        }
    }
    
    // 监控资源加载错误
    function monitorResourceLoadErrors() {
        window.addEventListener('error', function(event) {
            const target = event.target;
            
            // 处理脚本加载错误
            if (target.tagName === 'SCRIPT' && target.src) {
                const src = target.src;
                handleResourceError(src, target);
            }
            
            // 处理样式表加载错误
            if (target.tagName === 'LINK' && target.rel === 'stylesheet' && target.href) {
                const href = target.href;
                handleResourceError(href, target);
            }
        }, true);
    }
    
    // 处理资源加载错误
    function handleResourceError(url, element) {
        // 如果已经处理过，跳过
        if (resourceState.failed[url]) {
            return;
        }
        
        console.error('[九猫修复] 资源加载失败:', url);
        resourceState.failed[url] = true;
        
        // 获取资源类型
        const resourceType = getResourceType(url, element);
        if (resourceType) {
            console.log('[九猫修复] 尝试加载本地资源:', resourceType);
            
            // 根据元素类型加载本地资源
            if (element.tagName === 'SCRIPT') {
                loadLocalResource(resourceType, 'script');
            } else if (element.tagName === 'LINK' && element.rel === 'stylesheet') {
                loadLocalResource(resourceType, 'style');
            }
        }
    }
    
    // 获取资源类型
    function getResourceType(url, element) {
        const lowerUrl = url.toLowerCase();
        
        // 根据URL判断
        if (lowerUrl.includes('jquery') && lowerUrl.includes('.js')) {
            return 'jquery';
        } else if (lowerUrl.includes('bootstrap') && lowerUrl.includes('.js')) {
            return 'bootstrap';
        } else if (lowerUrl.includes('bootstrap') && lowerUrl.includes('.css')) {
            return 'bootstrapCss';
        } else if ((lowerUrl.includes('fontawesome') || lowerUrl.includes('font-awesome')) && lowerUrl.includes('.css')) {
            return 'fontawesome';
        } else if ((lowerUrl.includes('chart') || lowerUrl.includes('chart.js')) && lowerUrl.includes('.js')) {
            return 'chartjs';
        }
        
        return null;
    }
    
    // 立即执行
    checkAndLoadCriticalResources();
    monitorResourceLoadErrors();
    
    // 页面加载完成后再次检查
    window.addEventListener('load', checkAndLoadCriticalResources);
    
    // 标记修复已加载
    window.__nineCatsFixes.loaded['local-resource-loader'] = true;
    
    console.log('[九猫修复] 本地资源加载器加载完成');
})();
