/**
 * 九猫小说分析写作系统 - 控制台修复脚本
 *
 * 此脚本用于修复控制台页面中分析结果和推理过程显示问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[控制台修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        selectors: {
            analysisContent: '#analysisContent',
            reasoningContent: '#reasoningContent',
            chapterAnalysisContent: '#chapterAnalysisContent',
            chapterReasoningContent: '#chapterReasoningContent',
            dimensionList: '#dimensionList',
            chapterList: '#chapterList'
        },
        apiPaths: {
            novel: ['/api/novel/', '/api/novels/'],
            analysis: ['/api/novel/{novel_id}/analysis/{dimension}', '/api/novels/{novel_id}/analysis/{dimension}'],
            reasoning: ['/api/novel/{novel_id}/analysis/{dimension}/reasoning_content', '/api/novels/{novel_id}/analysis/{dimension}/reasoning_content'],
            chapterAnalysis: ['/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}', '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}'],
            chapterReasoning: ['/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content', '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content']
        }
    };

    // 状态
    const STATE = {
        novelId: null,
        chapterId: null,
        dimension: null,
        isChapterAnalysis: false,
        isLoading: false,
        retryCount: 0,
        pollTimer: null
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[控制台修复] ${message}`);
        }
    }

    // 初始化
    function init() {
        debugLog('初始化控制台修复脚本');

        // 监听维度点击事件
        $(document).on('click', '.dimension-item', function() {
            const dimension = $(this).data('dimension');
            const templateId = getSelectedTemplateId();

            if (templateId && dimension) {
                debugLog(`检测到维度点击: ${dimension}, 模板ID: ${templateId}`);

                // 延迟执行，确保原始函数先执行
                setTimeout(() => {
                    fixAnalysisResult(templateId, dimension);
                }, 500);
            }
        });

        // 监听章节维度点击事件
        $(document).on('click', '.view-chapter-analysis-btn', function() {
            const chapterId = $(this).data('chapter-id');
            const dimension = $(this).data('dimension');
            const templateId = getSelectedTemplateId();

            if (templateId && chapterId && dimension) {
                debugLog(`检测到章节维度点击: 章节ID ${chapterId}, 维度: ${dimension}, 模板ID: ${templateId}`);

                // 延迟执行，确保原始函数先执行
                setTimeout(() => {
                    fixChapterAnalysisResult(templateId, chapterId, dimension);
                }, 500);
            }
        });

        // 监听标签页切换事件
        $(document).on('shown.bs.tab', '#analysisResultTab button[data-bs-toggle="tab"]', function() {
            const tabId = $(this).attr('id');

            if (tabId === 'reasoning-tab') {
                debugLog('检测到推理过程标签页激活');

                // 获取当前选中的维度和模板ID
                const dimension = getSelectedDimension();
                const templateId = getSelectedTemplateId();

                if (templateId && dimension) {
                    // 延迟执行，确保标签页切换完成
                    setTimeout(() => {
                        fixReasoningContent(templateId, dimension);
                    }, 300);
                }
            }
        });

        // 监听章节标签页切换事件
        $(document).on('shown.bs.tab', '#chapterAnalysisResultTab button[data-bs-toggle="tab"]', function() {
            const tabId = $(this).attr('id');

            if (tabId === 'chapter-reasoning-tab') {
                debugLog('检测到章节推理过程标签页激活');

                // 获取当前选中的章节、维度和模板ID
                const chapterId = getSelectedChapterId();
                const dimension = getSelectedDimension();
                const templateId = getSelectedTemplateId();

                if (templateId && chapterId && dimension) {
                    // 延迟执行，确保标签页切换完成
                    setTimeout(() => {
                        fixChapterReasoningContent(templateId, chapterId, dimension);
                    }, 300);
                }
            }
        });

        debugLog('控制台修复脚本初始化完成');
    }

    // 获取当前选中的模板ID
    function getSelectedTemplateId() {
        // 尝试从全局变量获取
        if (typeof selectedTemplateId !== 'undefined' && selectedTemplateId) {
            return selectedTemplateId;
        }

        // 尝试从DOM元素获取
        const selectedTemplate = $('.template-card.border-primary');
        if (selectedTemplate.length > 0) {
            return selectedTemplate.data('template-id');
        }

        return null;
    }

    // 获取当前选中的维度
    function getSelectedDimension() {
        // 尝试从全局变量获取
        if (typeof window.selectedDimension !== 'undefined' && window.selectedDimension) {
            return window.selectedDimension;
        }

        // 尝试从DOM元素获取
        const activeDimension = $('.dimension-item.active');
        if (activeDimension.length > 0) {
            return activeDimension.data('dimension');
        }

        return null;
    }

    // 获取当前选中的章节ID
    function getSelectedChapterId() {
        // 尝试从全局变量获取
        if (typeof selectedChapterId !== 'undefined' && selectedChapterId) {
            return selectedChapterId;
        }

        // 尝试从DOM元素获取
        const selectedChapter = $('.chapter-item.active');
        if (selectedChapter.length > 0) {
            return selectedChapter.data('chapter-id');
        }

        return null;
    }

    // 修复分析结果
    function fixAnalysisResult(templateId, dimension) {
        debugLog(`修复分析结果: 模板ID=${templateId}, 维度=${dimension}`);

        // 检查分析结果内容
        const analysisContent = $(CONFIG.selectors.analysisContent);

        // 如果内容为空或包含错误信息，尝试重新加载
        if (analysisContent.is(':empty') ||
            analysisContent.text().trim() === '' ||
            analysisContent.find('.alert-danger').length > 0 ||
            analysisContent.find('.alert-warning').length > 0) {

            debugLog('检测到分析结果内容为空或包含错误信息，尝试重新加载');

            // 显示加载中提示
            analysisContent.html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">修复中，正在重新加载分析结果...</p></div>');

            // 尝试所有可能的API路径
            tryAllApiPaths(CONFIG.apiPaths.analysis, templateId, dimension, null, function(data) {
                if (data && data.success) {
                    let content = '';

                    // 提取内容
                    if (data.result && data.result.content) {
                        content = data.result.content;
                    } else if (data.content) {
                        content = data.content;
                    }

                    if (content) {
                        // 更新分析结果显示
                        analysisContent.html(marked.parse(content));
                        debugLog('成功修复分析结果内容');

                        // 同时修复推理过程
                        fixReasoningContent(templateId, dimension);
                    } else {
                        analysisContent.html('<div class="alert alert-warning">无法获取分析结果内容</div>');
                        debugLog('无法获取分析结果内容', 'warn');
                    }
                } else {
                    analysisContent.html('<div class="alert alert-danger">修复分析结果失败</div>');
                    debugLog('修复分析结果失败', 'error');
                }
            });
        }
    }

    // 修复推理过程
    function fixReasoningContent(templateId, dimension) {
        debugLog(`修复推理过程: 模板ID=${templateId}, 维度=${dimension}`);

        // 检查推理过程内容
        const reasoningContent = $(CONFIG.selectors.reasoningContent);

        // 如果内容为空或包含错误信息，尝试重新加载
        if (reasoningContent.is(':empty') ||
            reasoningContent.text().trim() === '' ||
            reasoningContent.find('.alert-danger').length > 0 ||
            reasoningContent.find('.alert-warning').length > 0) {

            debugLog('检测到推理过程内容为空或包含错误信息，尝试重新加载');

            // 显示加载中提示
            reasoningContent.html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">修复中，正在重新加载推理过程...</p></div>');

            // 尝试所有可能的API路径
            tryAllApiPaths(CONFIG.apiPaths.reasoning, templateId, dimension, null, function(data) {
                if (data && data.success) {
                    let content = '';

                    // 提取内容
                    if (data.reasoning_content) {
                        content = data.reasoning_content;
                    } else if (data.content) {
                        content = data.content;
                    }

                    if (content) {
                        // 更新推理过程显示
                        reasoningContent.html(marked.parse(content));
                        debugLog('成功修复推理过程内容');
                    } else {
                        reasoningContent.html('<div class="alert alert-warning">无法获取推理过程内容</div>');
                        debugLog('无法获取推理过程内容', 'warn');
                    }
                } else {
                    reasoningContent.html('<div class="alert alert-danger">修复推理过程失败</div>');
                    debugLog('修复推理过程失败', 'error');
                }
            });
        }
    }

    // 修复章节分析结果
    function fixChapterAnalysisResult(templateId, chapterId, dimension) {
        debugLog(`修复章节分析结果: 模板ID=${templateId}, 章节ID=${chapterId}, 维度=${dimension}`);

        // 检查章节分析结果内容
        const chapterAnalysisContent = $(CONFIG.selectors.chapterAnalysisContent);

        // 如果内容为空或包含错误信息，尝试重新加载
        if (chapterAnalysisContent.is(':empty') ||
            chapterAnalysisContent.text().trim() === '' ||
            chapterAnalysisContent.find('.alert-danger').length > 0 ||
            chapterAnalysisContent.find('.alert-warning').length > 0) {

            debugLog('检测到章节分析结果内容为空或包含错误信息，尝试重新加载');

            // 显示加载中提示
            chapterAnalysisContent.html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">修复中，正在重新加载章节分析结果...</p></div>');

            // 尝试所有可能的API路径
            tryAllApiPaths(CONFIG.apiPaths.chapterAnalysis, templateId, dimension, chapterId, function(data) {
                if (data && data.success) {
                    let content = '';

                    // 提取内容
                    if (data.result && data.result.content) {
                        content = data.result.content;
                    } else if (data.content) {
                        content = data.content;
                    }

                    if (content) {
                        // 更新章节分析结果显示
                        chapterAnalysisContent.html(marked.parse(content));
                        debugLog('成功修复章节分析结果内容');

                        // 同时修复章节推理过程
                        fixChapterReasoningContent(templateId, chapterId, dimension);
                    } else {
                        chapterAnalysisContent.html('<div class="alert alert-warning">无法获取章节分析结果内容</div>');
                        debugLog('无法获取章节分析结果内容', 'warn');
                    }
                } else {
                    chapterAnalysisContent.html('<div class="alert alert-danger">修复章节分析结果失败</div>');
                    debugLog('修复章节分析结果失败', 'error');
                }
            });
        }
    }

    // 修复章节推理过程
    function fixChapterReasoningContent(templateId, chapterId, dimension) {
        debugLog(`修复章节推理过程: 模板ID=${templateId}, 章节ID=${chapterId}, 维度=${dimension}`);

        // 检查章节推理过程内容
        const chapterReasoningContent = $(CONFIG.selectors.chapterReasoningContent);

        // 如果内容为空或包含错误信息，尝试重新加载
        if (chapterReasoningContent.is(':empty') ||
            chapterReasoningContent.text().trim() === '' ||
            chapterReasoningContent.find('.alert-danger').length > 0 ||
            chapterReasoningContent.find('.alert-warning').length > 0) {

            debugLog('检测到章节推理过程内容为空或包含错误信息，尝试重新加载');

            // 显示加载中提示
            chapterReasoningContent.html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">修复中，正在重新加载章节推理过程...</p></div>');

            // 尝试所有可能的API路径
            tryAllApiPaths(CONFIG.apiPaths.chapterReasoning, templateId, dimension, chapterId, function(data) {
                if (data && data.success) {
                    let content = '';

                    // 提取内容
                    if (data.reasoning_content) {
                        content = data.reasoning_content;
                    } else if (data.content) {
                        content = data.content;
                    }

                    if (content) {
                        // 更新章节推理过程显示
                        chapterReasoningContent.html(marked.parse(content));
                        debugLog('成功修复章节推理过程内容');
                    } else {
                        chapterReasoningContent.html('<div class="alert alert-warning">无法获取章节推理过程内容</div>');
                        debugLog('无法获取章节推理过程内容', 'warn');
                    }
                } else {
                    chapterReasoningContent.html('<div class="alert alert-danger">修复章节推理过程失败</div>');
                    debugLog('修复章节推理过程失败', 'error');
                }
            });
        }
    }

    // 尝试所有可能的API路径
    function tryAllApiPaths(apiPaths, templateId, dimension, chapterId, callback) {
        let index = 0;

        function tryNextPath() {
            if (index >= apiPaths.length) {
                debugLog('所有API路径均失败', 'error');
                callback(null);
                return;
            }

            let apiUrl = apiPaths[index];

            // 替换参数
            apiUrl = apiUrl.replace('{novel_id}', templateId)
                          .replace('{dimension}', dimension);

            // 如果有章节ID，替换章节ID参数
            if (chapterId) {
                apiUrl = apiUrl.replace('{chapter_id}', chapterId);
            }

            // 添加时间戳防止缓存
            apiUrl += `?_=${Date.now()}`;

            debugLog(`尝试API路径 ${index + 1}/${apiPaths.length}: ${apiUrl}`);

            // 发送请求
            $.ajax({
                url: apiUrl,
                type: 'GET',
                success: function(data) {
                    debugLog(`API路径 ${apiUrl} 成功获取数据`);
                    callback(data);
                },
                error: function() {
                    debugLog(`API路径 ${apiUrl} 失败，尝试下一个路径`, 'warn');
                    index++;
                    tryNextPath();
                }
            });
        }

        // 开始尝试第一个路径
        tryNextPath();
    }

    // 初始化脚本
    $(document).ready(function() {
        init();
    });
})();
