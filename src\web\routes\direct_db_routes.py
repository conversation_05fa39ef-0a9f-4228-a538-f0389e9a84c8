"""
九猫小说分析写作系统 - 直接数据库查询路由

此模块提供直接从数据库获取分析结果的路由，
绕过API调用，确保分析结果能够正确显示。

包含以下功能：
1. 直接获取小说分析结果
2. 直接获取章节分析结果
3. 获取真实推理过程内容
"""
import logging
import traceback
from flask import Blueprint, jsonify, request
from sqlalchemy import text

from src.db.connection import Session
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult

logger = logging.getLogger(__name__)

direct_db_bp = Blueprint('direct_db', __name__, url_prefix='/direct_db')

@direct_db_bp.route('/analysis')
def direct_get_analysis():
    """
    直接从数据库获取小说分析结果。

    参数:
        novel_id: 小说ID
        dimension: 分析维度
    """
    try:
        novel_id = request.args.get('novel_id')
        dimension = request.args.get('dimension')

        if not novel_id or not dimension:
            return jsonify({
                "success": False,
                "error": "缺少必要参数",
                "message": "请提供novel_id和dimension参数"
            }), 400

        logger.info(f"直接查询分析结果: novel_id={novel_id}, dimension={dimension}")

        session = Session()
        try:
            # 使用ORM查询，避免SQL注入和字段映射问题
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                logger.warning(f"未找到分析结果: novel_id={novel_id}, dimension={dimension}")
                return jsonify({
                    "success": False,
                    "error": "未找到分析结果",
                    "message": f"该小说尚未进行{dimension}分析，请先进行分析。"
                }), 404

            # 将结果转换为字典
            result_dict = {
                'id': result.id,
                'novel_id': result.novel_id,
                'dimension': result.dimension,
                'content': result.content,
                'created_at': result.created_at,
                'updated_at': result.updated_at
            }

            # 添加reasoning_content字段（如果存在）
            if hasattr(result, 'reasoning_content'):
                result_dict['reasoning_content'] = result.reasoning_content
            else:
                logger.warning(f"分析结果缺少reasoning_content字段: novel_id={novel_id}, dimension={dimension}")
                result_dict['reasoning_content'] = ""

            # 添加metadata字段（如果存在）
            if hasattr(result, 'metadata'):
                # 处理metadata，确保它是可JSON序列化的
                try:
                    # 完全跳过metadata字段，避免序列化问题
                    result_dict['metadata'] = "metadata字段已省略，避免序列化问题"
                    logger.info("已省略metadata字段，避免序列化问题")
                except Exception as e:
                    logger.warning(f"处理metadata字段时出错: {str(e)}")
                    result_dict['metadata'] = "metadata字段处理出错"

            logger.info(f"成功获取分析结果: novel_id={novel_id}, dimension={dimension}, 字段: {list(result_dict.keys())}")

            # 确保content字段存在
            if not result_dict['content']:
                logger.warning(f"分析结果content字段为空: novel_id={novel_id}, dimension={dimension}")
                result_dict['content'] = "分析结果内容不可用"

            # 转换日期时间字段为ISO格式字符串
            for key, value in result_dict.items():
                if hasattr(value, 'isoformat'):
                    result_dict[key] = value.isoformat()

            return jsonify({
                "success": True,
                "result": result_dict
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"直接查询分析结果时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@direct_db_bp.route('/chapter_analysis')
def direct_get_chapter_analysis():
    """
    直接从数据库获取章节分析结果。

    参数:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
    """
    try:
        novel_id = request.args.get('novel_id')
        chapter_id = request.args.get('chapter_id')
        dimension = request.args.get('dimension')

        if not novel_id or not chapter_id or not dimension:
            return jsonify({
                "success": False,
                "error": "缺少必要参数",
                "message": "请提供novel_id、chapter_id和dimension参数"
            }), 400

        logger.info(f"直接查询章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")

        session = Session()
        try:
            # 使用ORM查询，避免SQL注入和字段映射问题
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                logger.warning(f"未找到章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
                return jsonify({
                    "success": False,
                    "error": "未找到章节分析结果",
                    "message": f"该章节尚未进行{dimension}分析，请先进行分析。"
                }), 404

            # 将结果转换为字典
            result_dict = {
                'id': result.id,
                'novel_id': result.novel_id,
                'chapter_id': result.chapter_id,
                'dimension': result.dimension,
                'content': result.content,
                'created_at': result.created_at,
                'updated_at': result.updated_at
            }

            # 添加reasoning_content字段（如果存在）
            if hasattr(result, 'reasoning_content'):
                result_dict['reasoning_content'] = result.reasoning_content
            else:
                logger.warning(f"章节分析结果缺少reasoning_content字段: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
                result_dict['reasoning_content'] = ""

            # 添加metadata字段（如果存在）
            if hasattr(result, 'metadata'):
                # 处理metadata，确保它是可JSON序列化的
                try:
                    # 完全跳过metadata字段，避免序列化问题
                    result_dict['metadata'] = "metadata字段已省略，避免序列化问题"
                    logger.info("已省略metadata字段，避免序列化问题")
                except Exception as e:
                    logger.warning(f"处理metadata字段时出错: {str(e)}")
                    result_dict['metadata'] = "metadata字段处理出错"

            logger.info(f"成功获取章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, 字段: {list(result_dict.keys())}")

            # 确保content字段存在
            if not result_dict['content']:
                logger.warning(f"章节分析结果content字段为空: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
                result_dict['content'] = "分析结果内容不可用"

            # 转换日期时间字段为ISO格式字符串
            for key, value in result_dict.items():
                if hasattr(value, 'isoformat'):
                    result_dict[key] = value.isoformat()

            return jsonify({
                "success": True,
                "result": result_dict
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"直接查询章节分析结果时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@direct_db_bp.route('/analysis/get_reasoning_content', methods=['POST'])
def get_reasoning_content():
    """
    直接从数据库获取推理过程内容

    请求体参数:
        novel_id: 小说ID
        chapter_id: 章节ID (可选，章节分析时需要)
        dimension: 分析维度
        is_chapter: 是否为章节分析 (布尔值)
    """
    try:
        data = request.get_json()

        if not data:
            logger.error("请求数据为空")
            return jsonify({
                "success": False,
                "error": "请求数据为空"
            }), 400

        novel_id = data.get('novel_id')
        chapter_id = data.get('chapter_id')
        dimension = data.get('dimension')
        is_chapter = data.get('is_chapter', False)

        if not novel_id or not dimension:
            logger.error(f"缺少必要参数: novel_id={novel_id}, dimension={dimension}")
            return jsonify({
                "success": False,
                "error": "缺少必要参数",
                "message": "请提供novel_id和dimension参数"
            }), 400

        if is_chapter and not chapter_id:
            logger.error("章节分析需要提供chapter_id参数")
            return jsonify({
                "success": False,
                "error": "缺少必要参数",
                "message": "章节分析需要提供chapter_id参数"
            }), 400

        logger.info(f"直接获取推理过程内容: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, is_chapter={is_chapter}")

        session = Session()
        try:
            if is_chapter:
                # 查询章节分析推理过程
                result = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter_id,
                    dimension=dimension
                ).first()
            else:
                # 查询整本书分析推理过程
                result = session.query(AnalysisResult).filter_by(
                    novel_id=novel_id,
                    dimension=dimension
                ).first()

            if not result:
                logger.warning(f"未找到分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, is_chapter={is_chapter}")
                return jsonify({
                    "success": False,
                    "error": "未找到分析结果",
                    "message": "未找到对应的分析结果，请先进行分析。"
                }), 404

            # 获取推理过程内容
            reasoning_content = None
            if hasattr(result, 'reasoning_content'):
                reasoning_content = result.reasoning_content

            if not reasoning_content:
                logger.warning(f"分析结果缺少推理过程内容: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, is_chapter={is_chapter}")

                # 尝试从其他字段获取
                if hasattr(result, 'reasoning_process'):
                    reasoning_content = result.reasoning_process
                elif hasattr(result, 'analysis_process'):
                    reasoning_content = result.analysis_process
                elif hasattr(result, 'analysis_reasoning'):
                    reasoning_content = result.analysis_reasoning
                elif hasattr(result, 'reasoning_details'):
                    reasoning_content = result.reasoning_details
                elif hasattr(result, 'reasoning'):
                    reasoning_content = result.reasoning

            if not reasoning_content:
                logger.error(f"未找到推理过程内容: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, is_chapter={is_chapter}")
                return jsonify({
                    "success": False,
                    "error": "未找到推理过程内容",
                    "message": "该分析结果没有推理过程内容，请尝试重新分析。"
                }), 404

            logger.info(f"成功获取推理过程内容: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, is_chapter={is_chapter}, 内容长度: {len(reasoning_content)}")

            return jsonify({
                "success": True,
                "reasoning_content": reasoning_content
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取推理过程内容时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@direct_db_bp.route('/analysis/check_tables', methods=['GET'])
def check_tables():
    """
    检查数据库表结构
    """
    try:
        session = Session()
        try:
            # 使用SQLAlchemy执行原始SQL查询
            # 获取所有表名
            tables_result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table';"))
            tables = [row[0] for row in tables_result]

            table_info = {}

            # 获取每个表的列信息
            for table_name in tables:
                columns_result = session.execute(text(f"PRAGMA table_info({table_name});"))
                columns = [row[1] for row in columns_result]
                table_info[table_name] = columns

            logger.info(f"成功获取数据库表结构: {len(tables)}个表")

            return jsonify({
                "success": True,
                "tables": table_info
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"检查数据库表结构时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500