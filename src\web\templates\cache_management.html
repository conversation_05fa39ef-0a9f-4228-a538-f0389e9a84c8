{% extends "base.html" %}

{% block title %}缓存管理 - 九猫{% endblock %}

{% block extra_css %}
<style>
    .cache-stats {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .cache-item {
        margin-bottom: 10px;
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }
    
    .cache-item:hover {
        background-color: #f1f1f1;
    }
    
    .cache-actions {
        margin-top: 15px;
    }
    
    .cache-filter {
        margin-bottom: 20px;
    }
    
    .cache-age-indicator {
        width: 15px;
        height: 15px;
        display: inline-block;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .cache-age-fresh {
        background-color: #28a745;
    }
    
    .cache-age-medium {
        background-color: #ffc107;
    }
    
    .cache-age-old {
        background-color: #dc3545;
    }
    
    .prewarming-section {
        margin-top: 30px;
        padding: 15px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item active">缓存管理</li>
            </ol>
        </nav>

        <h1>缓存管理</h1>
        
        <!-- 缓存统计信息 -->
        <div class="cache-stats">
            <h3>缓存统计</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-white bg-primary mb-3">
                        <div class="card-body">
                            <h5 class="card-title">总缓存数量</h5>
                            <p class="card-text display-4">{{ cache_stats.total }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success mb-3">
                        <div class="card-body">
                            <h5 class="card-title">新鲜缓存</h5>
                            <p class="card-text display-4">{{ cache_stats.fresh }}</p>
                            <small>最近{{ cache_stats.fresh_days }}天内更新</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning mb-3">
                        <div class="card-body">
                            <h5 class="card-title">即将过期</h5>
                            <p class="card-text display-4">{{ cache_stats.expiring_soon }}</p>
                            <small>{{ cache_stats.expiring_days }}天内过期</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-danger mb-3">
                        <div class="card-body">
                            <h5 class="card-title">已过期</h5>
                            <p class="card-text display-4">{{ cache_stats.expired }}</p>
                            <small>超过{{ cache_stats.cache_valid_days }}天未更新</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">缓存大小</h5>
                            <p class="card-text">总大小: {{ cache_stats.total_size_formatted }}</p>
                            <p class="card-text">平均大小: {{ cache_stats.avg_size_formatted }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">维度分布</h5>
                            <canvas id="dimensionChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 缓存操作 -->
        <div class="cache-actions">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3>缓存操作</h3>
                <div>
                    <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#clearCacheModal">
                        清除所有缓存
                    </button>
                    <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#clearExpiredModal">
                        清除过期缓存
                    </button>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#prewarmCacheModal">
                        预热缓存
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 缓存过滤器 -->
        <div class="cache-filter">
            <form method="GET" action="{{ url_for('cache_management') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="novel_filter" class="form-label">小说</label>
                    <select class="form-select" id="novel_filter" name="novel_id">
                        <option value="">所有小说</option>
                        {% for novel in novels %}
                            <option value="{{ novel.id }}" {% if request.args.get('novel_id')|int == novel.id %}selected{% endif %}>
                                {{ novel.title }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="dimension_filter" class="form-label">分析维度</label>
                    <select class="form-select" id="dimension_filter" name="dimension">
                        <option value="">所有维度</option>
                        {% for dimension in dimensions %}
                            <option value="{{ dimension }}" {% if request.args.get('dimension') == dimension %}selected{% endif %}>
                                {{ dimension }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="age_filter" class="form-label">缓存年龄</label>
                    <select class="form-select" id="age_filter" name="age">
                        <option value="">所有</option>
                        <option value="fresh" {% if request.args.get('age') == 'fresh' %}selected{% endif %}>新鲜</option>
                        <option value="expiring" {% if request.args.get('age') == 'expiring' %}selected{% endif %}>即将过期</option>
                        <option value="expired" {% if request.args.get('age') == 'expired' %}selected{% endif %}>已过期</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">筛选</button>
                    <a href="{{ url_for('cache_management') }}" class="btn btn-outline-secondary ms-2">重置</a>
                </div>
            </form>
        </div>
        
        <!-- 缓存列表 -->
        <div class="cache-list">
            <h3>缓存列表</h3>
            
            {% if cache_items %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>小说</th>
                                <th>维度</th>
                                <th>创建时间</th>
                                <th>更新时间</th>
                                <th>年龄</th>
                                <th>大小</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in cache_items %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('view_novel', novel_id=item.novel_id) }}">
                                            {{ item.novel_title }}
                                        </a>
                                    </td>
                                    <td>{{ item.dimension }}</td>
                                    <td>{{ item.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ item.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if item.age_days <= cache_stats.fresh_days %}
                                            <span class="cache-age-indicator cache-age-fresh" title="新鲜"></span>
                                        {% elif item.age_days <= cache_stats.cache_valid_days %}
                                            <span class="cache-age-indicator cache-age-medium" title="即将过期"></span>
                                        {% else %}
                                            <span class="cache-age-indicator cache-age-old" title="已过期"></span>
                                        {% endif %}
                                        {{ item.age_days }} 天
                                    </td>
                                    <td>{{ item.size_formatted }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('view_analysis', novel_id=item.novel_id, dimension=item.dimension) }}" class="btn btn-sm btn-outline-primary">
                                                查看
                                            </a>
                                            <a href="{{ url_for('refresh_cache', cache_id=item.id) }}" class="btn btn-sm btn-outline-success">
                                                刷新
                                            </a>
                                            <a href="{{ url_for('delete_cache', cache_id=item.id) }}" class="btn btn-sm btn-outline-danger" 
                                               onclick="return confirm('确定要删除这个缓存吗？');">
                                                删除
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('cache_management', page=pagination.page-1, **request.args) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            
                            {% for p in range(1, pagination.pages + 1) %}
                                <li class="page-item {% if p == pagination.page %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('cache_management', page=p, **request.args) }}">{{ p }}</a>
                                </li>
                            {% endfor %}
                            
                            <li class="page-item {% if pagination.page == pagination.pages %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('cache_management', page=pagination.page+1, **request.args) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    没有找到符合条件的缓存项。
                </div>
            {% endif %}
        </div>
        
        <!-- 缓存预热配置 -->
        <div class="prewarming-section">
            <h3>缓存预热配置</h3>
            <p>配置自动缓存预热，提前分析常用维度，提高用户体验。</p>
            
            <form method="POST" action="{{ url_for('update_prewarming_config') }}" class="mt-3">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enable_prewarming" name="enable_prewarming" 
                                   {% if prewarming_config.enabled %}checked{% endif %}>
                            <label class="form-check-label" for="enable_prewarming">启用自动缓存预热</label>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="prewarming_interval" class="form-label">预热间隔（小时）</label>
                        <input type="number" class="form-control" id="prewarming_interval" name="prewarming_interval" 
                               value="{{ prewarming_config.interval }}" min="1" max="168">
                        <div class="form-text">设置自动预热的时间间隔，建议设置为非高峰时段运行。</div>
                    </div>
                    <div class="col-md-6">
                        <label for="prewarming_max_novels" class="form-label">每次预热的最大小说数量</label>
                        <input type="number" class="form-control" id="prewarming_max_novels" name="prewarming_max_novels" 
                               value="{{ prewarming_config.max_novels }}" min="1" max="100">
                        <div class="form-text">限制每次预热处理的小说数量，避免系统负载过高。</div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="prewarming_days" class="form-label">获取最近多少天内访问的小说</label>
                        <input type="number" class="form-control" id="prewarming_days" name="prewarming_days" 
                               value="{{ prewarming_config.days }}" min="1" max="365">
                    </div>
                    <div class="col-md-6">
                        <label for="prewarming_min_views" class="form-label">最小访问次数</label>
                        <input type="number" class="form-control" id="prewarming_min_views" name="prewarming_min_views" 
                               value="{{ prewarming_config.min_views }}" min="1" max="1000">
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="prewarming_dimensions" class="form-label">要预热的维度（留空则使用最常用维度）</label>
                        <select class="form-select" id="prewarming_dimensions" name="prewarming_dimensions" multiple>
                            {% for dimension in dimensions %}
                                <option value="{{ dimension }}" 
                                        {% if dimension in prewarming_config.dimensions %}selected{% endif %}>
                                    {{ dimension }}
                                </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">按住Ctrl键可以选择多个维度。</div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">保存配置</button>
                        <a href="{{ url_for('run_manual_prewarming') }}" class="btn btn-success">
                            立即运行预热
                        </a>
                    </div>
                </div>
            </form>
            
            <!-- 预热历史记录 -->
            {% if prewarming_history %}
                <h4 class="mt-4">预热历史记录</h4>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>处理小说数</th>
                                <th>成功数</th>
                                <th>耗时</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in prewarming_history %}
                                <tr>
                                    <td>{{ record.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ record.total_novels }}</td>
                                    <td>{{ record.successful_novels }}</td>
                                    <td>{{ record.elapsed_time }}秒</td>
                                    <td>
                                        {% if record.status == 'success' %}
                                            <span class="badge bg-success">成功</span>
                                        {% elif record.status == 'partial' %}
                                            <span class="badge bg-warning">部分成功</span>
                                        {% else %}
                                            <span class="badge bg-danger">失败</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 清除所有缓存确认模态框 -->
<div class="modal fade" id="clearCacheModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认清除所有缓存</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-danger">警告：此操作将删除所有缓存数据，需要重新分析才能恢复。此操作不可撤销！</p>
                <p>确定要清除所有缓存吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <a href="{{ url_for('clear_all_cache') }}" class="btn btn-danger">确认清除</a>
            </div>
        </div>
    </div>
</div>

<!-- 清除过期缓存确认模态框 -->
<div class="modal fade" id="clearExpiredModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认清除过期缓存</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>此操作将删除所有过期的缓存数据（超过{{ cache_stats.cache_valid_days }}天未更新）。</p>
                <p>确定要清除过期缓存吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <a href="{{ url_for('clear_expired_cache') }}" class="btn btn-warning">确认清除</a>
            </div>
        </div>
    </div>
</div>

<!-- 预热缓存模态框 -->
<div class="modal fade" id="prewarmCacheModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">预热缓存</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('run_manual_prewarming') }}">
                <div class="modal-body">
                    <p>预热缓存将提前分析常用维度，提高用户体验。</p>
                    
                    <div class="mb-3">
                        <label for="modal_days" class="form-label">获取最近多少天内访问的小说</label>
                        <input type="number" class="form-control" id="modal_days" name="days" value="30" min="1" max="365">
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_min_views" class="form-label">最小访问次数</label>
                        <input type="number" class="form-control" id="modal_min_views" name="min_views" value="1" min="1" max="1000">
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_max_novels" class="form-label">最多处理多少本小说</label>
                        <input type="number" class="form-control" id="modal_max_novels" name="max_novels" value="10" min="1" max="100">
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_dimensions" class="form-label">要预热的维度（留空则使用最常用维度）</label>
                        <select class="form-select" id="modal_dimensions" name="dimensions" multiple>
                            {% for dimension in dimensions %}
                                <option value="{{ dimension }}">{{ dimension }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">按住Ctrl键可以选择多个维度。</div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="modal_force" name="force">
                        <label class="form-check-label" for="modal_force">
                            强制更新缓存（即使缓存未过期）
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">开始预热</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // 维度分布图表
    const dimensionCtx = document.getElementById('dimensionChart').getContext('2d');
    const dimensionChart = new Chart(dimensionCtx, {
        type: 'pie',
        data: {
            labels: {{ dimension_stats.labels|tojson }},
            datasets: [{
                data: {{ dimension_stats.counts|tojson }},
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                    '#5a5c69', '#858796', '#6f42c1', '#20c9a6', '#fd7e14',
                    '#6610f2', '#17a2b8', '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right',
                }
            }
        }
    });
    
    // 表单验证
    document.addEventListener('DOMContentLoaded', function() {
        const prewarmingForm = document.querySelector('form[action="{{ url_for("update_prewarming_config") }}"]');
        if (prewarmingForm) {
            prewarmingForm.addEventListener('submit', function(event) {
                const interval = document.getElementById('prewarming_interval').value;
                if (interval < 1) {
                    alert('预热间隔必须大于0');
                    event.preventDefault();
                }
            });
        }
    });
</script>
{% endblock %}
