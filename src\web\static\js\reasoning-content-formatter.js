/**
 * 九猫系统 - 推理内容格式化工具
 * 用于增强推理内容的显示效果
 */

document.addEventListener('DOMContentLoaded', function() {
    // 查找所有推理内容容器
    const reasoningContainers = document.querySelectorAll('.reasoning-structured');
    
    // 如果找到容器，进行格式化处理
    if (reasoningContainers.length > 0) {
        reasoningContainers.forEach(formatReasoningContent);
    }
    
    // 监听推理标签页点击事件，处理动态加载的内容
    const reasoningTabs = document.querySelectorAll('#reasoning-tab');
    if (reasoningTabs.length > 0) {
        reasoningTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 延迟执行，等待内容加载
                setTimeout(function() {
                    const dynamicContainers = document.querySelectorAll('.reasoning-structured:not(.formatted)');
                    dynamicContainers.forEach(formatReasoningContent);
                }, 1000);
            });
        });
    }
});

/**
 * 格式化推理内容
 * @param {HTMLElement} container - 推理内容容器
 */
function formatReasoningContent(container) {
    // 标记为已格式化，避免重复处理
    if (container.classList.contains('formatted')) {
        return;
    }
    
    // 获取原始内容
    const originalContent = container.innerHTML;
    
    // 检查是否包含结构化标记
    if (originalContent.includes('## 分析思路说明') || originalContent.includes('## 详细分析')) {
        // 分割内容
        const sections = splitReasoningContent(originalContent);
        
        // 清空容器
        container.innerHTML = '';
        
        // 添加分析思路部分
        if (sections.approach) {
            const approachSection = document.createElement('div');
            approachSection.className = 'reasoning-section';
            
            const approachTitle = document.createElement('h3');
            approachTitle.className = 'reasoning-section-title';
            approachTitle.innerHTML = '分析思路说明';
            
            const approachContent = document.createElement('div');
            approachContent.className = 'reasoning-section-content';
            approachContent.innerHTML = formatApproachContent(sections.approach);
            
            approachSection.appendChild(approachTitle);
            approachSection.appendChild(approachContent);
            container.appendChild(approachSection);
        }
        
        // 添加详细分析部分
        if (sections.analysis) {
            const analysisSection = document.createElement('div');
            analysisSection.className = 'reasoning-section';
            
            const analysisTitle = document.createElement('h3');
            analysisTitle.className = 'reasoning-section-title';
            analysisTitle.innerHTML = '详细分析';
            
            const analysisContent = document.createElement('div');
            analysisContent.className = 'reasoning-section-content';
            analysisContent.innerHTML = sections.analysis;
            
            analysisSection.appendChild(analysisTitle);
            analysisSection.appendChild(analysisContent);
            container.appendChild(analysisSection);
        }
    }
    
    // 标记为已格式化
    container.classList.add('formatted');
}

/**
 * 分割推理内容为分析思路和详细分析两部分
 * @param {string} content - 原始推理内容
 * @returns {Object} 包含approach和analysis两个属性的对象
 */
function splitReasoningContent(content) {
    const result = {
        approach: '',
        analysis: ''
    };
    
    // 查找分析思路部分
    const approachMatch = content.match(/##\s*分析思路说明[：:]([\s\S]*?)(?=##\s*详细分析|$)/i);
    if (approachMatch && approachMatch[1]) {
        result.approach = approachMatch[1].trim();
    }
    
    // 查找详细分析部分
    const analysisMatch = content.match(/##\s*详细分析[：:]([\s\S]*)/i);
    if (analysisMatch && analysisMatch[1]) {
        result.analysis = analysisMatch[1].trim();
    } else {
        // 如果没有找到详细分析标记，但有内容，将剩余部分作为详细分析
        if (approachMatch && content.substring(approachMatch.index + approachMatch[0].length).trim()) {
            result.analysis = content.substring(approachMatch.index + approachMatch[0].length).trim();
        } else if (!approachMatch) {
            // 如果没有找到任何标记，将全部内容作为详细分析
            result.analysis = content;
        }
    }
    
    return result;
}

/**
 * 格式化分析思路内容，增强编号列表的显示
 * @param {string} content - 分析思路内容
 * @returns {string} 格式化后的HTML
 */
function formatApproachContent(content) {
    // 处理编号列表
    let formattedContent = content.replace(/(\d+)\.\s+\*\*([^*]+)\*\*\s*[:：]?\s*([^\n]+)/g, 
        '<div class="reasoning-approach-item"><strong>$1. $2</strong>: <span class="reasoning-approach-text">$3</span></div>');
    
    // 处理未格式化的编号列表
    formattedContent = formattedContent.replace(/(\d+)\.\s+([^\n]+)/g, 
        '<div class="reasoning-approach-item"><strong>$1.</strong> <span class="reasoning-approach-text">$2</span></div>');
    
    return formattedContent;
}
