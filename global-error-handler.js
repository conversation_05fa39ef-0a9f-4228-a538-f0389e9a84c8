/**
 * 九猫 - 全局错误处理脚本
 * 捕获和处理各种JavaScript错误，防止页面崩溃
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('全局错误处理脚本已加载');
    
    // 错误计数器
    let errorCount = 0;
    const maxErrors = 50; // 最大错误数，超过后将停止输出日志
    const errorHistory = {}; // 用于存储已发生的错误，避免重复报告
    
    // 配置
    const config = {
        suppressConsoleErrors: false, // 是否抑制控制台错误
        attemptAutoFix: true,        // 尝试自动修复
        reportErrors: true,          // 是否报告错误
        logToServer: false,          // 是否将错误日志发送到服务器
        preventPageCrash: true       // 防止页面崩溃
    };
    
    // 设置全局错误处理
    window.addEventListener('error', function(event) {
        errorCount++;
        
        // 检查是否超过最大错误数
        if (errorCount > maxErrors) {
            if (errorCount === maxErrors + 1) {
                console.error(`错误太多 (${maxErrors}+)，停止输出详细日志以避免性能问题`);
            }
            return true;
        }
        
        // 获取错误信息
        const errorMessage = event.message || '未知错误';
        const errorSource = event.filename || '未知来源';
        const lineNumber = event.lineno || '未知行号';
        const colNumber = event.colno || '未知列号';
        const errorStack = event.error && event.error.stack ? event.error.stack : '无堆栈信息';
        
        // 创建唯一错误ID
        const errorId = `${errorSource}:${lineNumber}:${errorMessage.substring(0, 50)}`;
        
        // 检查是否是重复错误
        if (errorHistory[errorId]) {
            errorHistory[errorId].count++;
            if (errorHistory[errorId].count <= 3) {
                console.warn(`重复错误 (${errorHistory[errorId].count}): ${errorMessage}`);
            }
            return true;
        }
        
        // 记录新错误
        errorHistory[errorId] = { count: 1, timestamp: Date.now() };
        
        // 输出错误信息
        console.error(`捕获到错误: ${errorMessage}`);
        console.error(`位置: ${errorSource} 行${lineNumber}, 列${colNumber}`);
        console.error(`堆栈: ${errorStack}`);
        
        // 尝试修复特定类型的错误
        if (config.attemptAutoFix) {
            // 图表相关错误
            if (errorMessage.includes('Chart') || 
                errorMessage.includes('canvas') || 
                errorMessage.includes('Canvas')) {
                console.log('尝试修复图表相关错误');
                
                // 尝试使用全局chartFix对象修复
                if (window.chartFix && typeof window.chartFix.fixAllCharts === 'function') {
                    setTimeout(window.chartFix.fixAllCharts, 200);
                }
            }
            
            // DOM操作相关错误
            if (errorMessage.includes('removeChild') || 
                errorMessage.includes('appendChild') || 
                errorMessage.includes('Node') ||
                errorMessage.includes('replaceChild')) {
                console.log('尝试修复DOM操作相关错误');
                
                // 检查是否是某个元素不存在的错误
                if (errorMessage.includes('null') || errorMessage.includes('undefined')) {
                    // 可能是元素不存在的错误，尝试刷新DOM
                    console.log('可能是元素不存在的错误，尝试刷新DOM');
                }
            }
            
            // JSON解析错误
            if (errorMessage.includes('JSON') || errorMessage.includes('parse')) {
                console.log('检测到JSON解析错误');
            }
            
            // Unexpected identifier错误
            if (errorMessage.includes('Unexpected identifier')) {
                console.log('检测到Unexpected identifier错误');
            }
            
            // 未定义或null引用错误
            if (errorMessage.includes('undefined') || errorMessage.includes('null')) {
                console.log('检测到未定义或null引用错误');
            }
        }
        
        // 防止页面崩溃
        if (config.preventPageCrash) {
            // 阻止事件冒泡
            if (event.stopPropagation) {
                event.stopPropagation();
            }
            
            // 阻止默认行为
            if (event.preventDefault) {
                event.preventDefault();
            }
        }
        
        return true; // 防止默认处理
    }, true);
    
    // 处理未捕获的Promise错误
    window.addEventListener('unhandledrejection', function(event) {
        errorCount++;
        
        // 检查是否超过最大错误数
        if (errorCount > maxErrors) {
            if (errorCount === maxErrors + 1) {
                console.error(`错误太多 (${maxErrors}+)，停止输出详细日志以避免性能问题`);
            }
            return true;
        }
        
        // 获取Promise错误信息
        const reason = event.reason || '未知原因';
        const message = reason.message || String(reason);
        const stack = reason.stack || '无堆栈信息';
        
        // 创建唯一错误ID
        const errorId = `promise:${message.substring(0, 50)}`;
        
        // 检查是否是重复错误
        if (errorHistory[errorId]) {
            errorHistory[errorId].count++;
            if (errorHistory[errorId].count <= 3) {
                console.warn(`重复Promise错误 (${errorHistory[errorId].count}): ${message}`);
            }
            return true;
        }
        
        // 记录新错误
        errorHistory[errorId] = { count: 1, timestamp: Date.now() };
        
        // 输出错误信息
        console.error(`未处理的Promise拒绝: ${message}`);
        console.error(`堆栈: ${stack}`);
        
        // 防止页面崩溃
        if (config.preventPageCrash) {
            // 阻止事件冒泡
            if (event.stopPropagation) {
                event.stopPropagation();
            }
            
            // 阻止默认行为
            if (event.preventDefault) {
                event.preventDefault();
            }
        }
        
        return true; // 防止默认处理
    });
    
    // 重写console.error，可选择性地抑制某些错误
    if (config.suppressConsoleErrors) {
        const originalConsoleError = console.error;
        console.error = function(...args) {
            // 检查是否是想要抑制的错误
            const errorMessage = String(args[0] || '');
            
            // 这里可以添加需要抑制的错误消息模式
            const suppressPatterns = [
                'ResizeObserver loop', // 常见的非关键错误
                'Script error.'        // 跨域脚本错误
            ];
            
            // 如果匹配任何抑制模式，则不输出
            if (suppressPatterns.some(pattern => errorMessage.includes(pattern))) {
                return;
            }
            
            // 对于其他错误，正常输出
            originalConsoleError.apply(console, args);
        };
    }
})(); 