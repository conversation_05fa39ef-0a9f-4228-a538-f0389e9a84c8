#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
九猫小说分析写作系统 - 预设模板修复脚本

此脚本用于验证并测试预设模板功能修复是否有效
"""
import os
import sys
import json
import requests
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 服务器地址
SERVER_URL = "http://localhost:5001"

def test_api_endpoints():
    """测试API端点是否正常工作"""
    logger.info("开始测试API端点...")
    
    # 1. 获取参考蓝本分析结果
    endpoint = "/api/v3.1/template/1/analysis"
    logger.info(f"测试端点：{endpoint}")
    try:
        response = requests.get(f"{SERVER_URL}{endpoint}")
        if response.status_code == 200 and response.json().get('success'):
            logger.info(f"端点 {endpoint} 测试成功")
        else:
            logger.error(f"端点 {endpoint} 测试失败: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"测试端点 {endpoint} 时出错: {str(e)}")
    
    # 2. 获取参考蓝本特定维度的分析结果
    endpoint = "/api/v3.1/template/1/analysis/language_style"
    logger.info(f"测试端点：{endpoint}")
    try:
        response = requests.get(f"{SERVER_URL}{endpoint}")
        if response.status_code == 200 and response.json().get('success'):
            logger.info(f"端点 {endpoint} 测试成功")
        else:
            logger.error(f"端点 {endpoint} 测试失败: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"测试端点 {endpoint} 时出错: {str(e)}")
    
    # 3. 获取参考蓝本特定维度的推理过程
    endpoint = "/api/v3.1/template/1/analysis/language_style/reasoning_content"
    logger.info(f"测试端点：{endpoint}")
    try:
        response = requests.get(f"{SERVER_URL}{endpoint}")
        if response.status_code == 200 and response.json().get('success'):
            logger.info(f"端点 {endpoint} 测试成功")
        else:
            logger.error(f"端点 {endpoint} 测试失败: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"测试端点 {endpoint} 时出错: {str(e)}")
    
    # 4. 转化为预设模板
    endpoint = "/api/v3.1/convert_to_template"
    logger.info(f"测试端点：{endpoint}")
    try:
        data = {
            "template_id": 1,
            "knowledge_base_data": None
        }
        response = requests.post(f"{SERVER_URL}{endpoint}", json=data)
        if response.status_code == 200 and response.json().get('success'):
            logger.info(f"端点 {endpoint} 测试成功")
        else:
            logger.error(f"端点 {endpoint} 测试失败: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"测试端点 {endpoint} 时出错: {str(e)}")
    
    logger.info("API端点测试完成")

def check_file_integrity():
    """检查文件完整性"""
    logger.info("检查文件完整性...")
    
    # 1. 检查是否存在template_conversion_service.py文件
    file_path = "src/services/template_conversion_service.py"
    if os.path.exists(file_path):
        logger.info(f"文件 {file_path} 存在")
    else:
        logger.error(f"文件 {file_path} 不存在")
    
    # 2. 检查console.js文件
    file_path = "src/web/static/js/v3.1/console.js"
    if os.path.exists(file_path):
        logger.info(f"文件 {file_path} 存在")
    else:
        logger.error(f"文件 {file_path} 不存在")
    
    # 3. 检查v3_1_api.py文件
    file_path = "src/web/routes/v3_1_api.py"
    if os.path.exists(file_path):
        logger.info(f"文件 {file_path} 存在")
    else:
        logger.error(f"文件 {file_path} 不存在")
    
    logger.info("文件完整性检查完成")

def main():
    """主函数"""
    logger.info("启动预设模板修复脚本...")
    
    # 检查文件完整性
    check_file_integrity()
    
    # 测试API端点
    try:
        test_api_endpoints()
    except Exception as e:
        logger.error(f"测试API端点时出错: {str(e)}")
    
    logger.info("预设模板修复脚本执行完成")

if __name__ == "__main__":
    main() 