/**
 * 九猫分析系统v3.0 - 分析结果和推理过程显示样式
 */

/* 分析结果容器 */
.analysis-content {
    background-color: var(--card-bg);
    border-radius: 0.75rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px var(--shadow-color);
    line-height: 1.8;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 推理过程容器 */
.reasoning-content {
    background-color: var(--card-bg);
    border-radius: 0.75rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px var(--shadow-color);
    line-height: 1.8;
    max-height: none;
    overflow-y: visible;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 推理过程文本 */
.reasoning-text {
    white-space: pre-wrap;
    line-height: 1.8;
    font-family: monospace;
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    max-width: 100%;
    color: var(--text-color);
}

/* Markdown内容样式 */
.markdown-content {
    font-size: 16px;
    line-height: 1.8;
    color: var(--text-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 1.5em;
    margin-bottom: 0.75em;
    color: var(--primary-color);
    font-weight: 600;
    clear: both;
}

.markdown-content h1 {
    font-size: 1.8em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.3em;
}

.markdown-content h2 {
    font-size: 1.6em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.3em;
}

.markdown-content h3 {
    font-size: 1.4em;
}

.markdown-content h4 {
    font-size: 1.2em;
}

.markdown-content p {
    margin-bottom: 1em;
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 1.5em;
    padding-left: 2em;
    list-style-position: outside;
}

.markdown-content ul {
    list-style-type: disc;
}

.markdown-content ol {
    list-style-type: decimal;
}

.markdown-content li {
    margin-bottom: 0.8em;
    padding-left: 0.5em;
}

.markdown-content li > ul,
.markdown-content li > ol {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.markdown-content blockquote {
    border-left: 4px solid var(--primary-light);
    padding: 1em 1.5em;
    margin: 1.5em 0;
    background-color: rgba(230, 180, 34, 0.05);
    color: var(--secondary-color);
    border-radius: 0 0.5rem 0.5rem 0;
}

.markdown-content code {
    background-color: rgba(230, 180, 34, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 0.3rem;
    font-family: 'Courier New', Courier, monospace;
    color: var(--primary-dark);
}

.markdown-content pre {
    background-color: rgba(230, 180, 34, 0.05);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin-bottom: 1em;
    border: 1px solid var(--border-color);
}

.markdown-content pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
}

.markdown-content table {
    width: 100%;
    margin-bottom: 1rem;
    border-collapse: collapse;
}

.markdown-content table th,
.markdown-content table td {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
}

.markdown-content table th {
    background-color: var(--primary-light);
    font-weight: 600;
}

/* 结构化推理内容 */
.reasoning-structured {
    background-color: var(--card-bg);
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
    overflow: hidden;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

/* 增强的Markdown元素 */
.enhanced-heading {
    position: relative;
    padding-left: 0.5rem;
}

.enhanced-heading::before {
    content: "";
    position: absolute;
    left: -0.5rem;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.numbered-heading {
    font-size: 1.3em;
    font-weight: 600;
    margin: 1.5em 0 0.8em;
    color: var(--primary-color);
    padding-bottom: 0.3em;
    border-bottom: 1px solid var(--border-color);
}

.numbered-heading .number {
    font-weight: 700;
    color: var(--primary-dark);
}

.enhanced-list {
    margin-left: 1rem;
    padding-left: 1.5rem;
}

.enhanced-list li {
    position: relative;
    padding-left: 0.5rem;
    margin-bottom: 0.8rem;
}

.enhanced-quote {
    border-left: 4px solid var(--primary-light);
    padding: 1em 1.5em;
    margin: 1.5em 0;
    background-color: rgba(230, 180, 34, 0.05);
    color: var(--secondary-color);
    border-radius: 0 0.5rem 0.5rem 0;
    font-style: italic;
}

.inline-code {
    background-color: rgba(230, 180, 34, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 0.3rem;
    font-family: 'Courier New', Courier, monospace;
    color: var(--primary-dark);
    font-size: 0.9em;
}

.enhanced-code-block {
    background-color: rgba(230, 180, 34, 0.05);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1.5em 0;
    border: 1px solid var(--border-color);
}

.enhanced-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5em 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.enhanced-table th {
    background-color: var(--primary-light);
    color: var(--dark-color);
    font-weight: 600;
    text-align: left;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
}

.enhanced-table td {
    padding: 0.8rem;
    border: 1px solid var(--border-color);
}

.enhanced-table tr:nth-child(even) {
    background-color: rgba(230, 180, 34, 0.03);
}

.enhanced-bold {
    font-weight: 700;
    color: var(--primary-dark);
}

.enhanced-italic {
    font-style: italic;
    color: var(--secondary-color);
}

.enhanced-hr {
    border: none;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--primary-light), transparent);
    margin: 2em 0;
}

.reasoning-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.reasoning-section:first-child {
    background-color: rgba(230, 180, 34, 0.05);
}

.reasoning-section:last-child {
    border-bottom: none;
}

.reasoning-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.reasoning-section-content {
    line-height: 1.8;
    color: var(--text-color);
}

.reasoning-approach-item {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.reasoning-approach-item::before {
    content: "•";
    position: absolute;
    left: 0.5rem;
    color: var(--primary-color);
}

.reasoning-approach-text {
    color: var(--text-color);
}

/* 分析状态指示器 */
.analysis-status {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.analysis-status.completed {
    background-color: var(--success-color);
}

.analysis-status.not-completed {
    background-color: var(--secondary-color);
}

.analysis-status.in-progress {
    background-color: var(--warning-color);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.5;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.5;
    }
}
