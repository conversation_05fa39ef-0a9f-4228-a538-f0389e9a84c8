"""
简单的JSON解析测试
"""
import json

# 测试数据
test_data = {
    "language_style": {
        "content": "这是一个测试内容，包含一些特殊字符: \" ' \\ \n",
        "metadata": {
            "processing_time": 10.5,
            "visualization_data": {
                "radar": {
                    "labels": ["标签1", "标签2", "标签3"],
                    "data": [85, 72, 90]
                }
            }
        }
    }
}

# 测试JSON序列化
json_str = json.dumps(test_data, ensure_ascii=False)
print("序列化结果:")
print(json_str)

# 测试JSON反序列化
try:
    parsed_data = json.loads(json_str)
    print("\n反序列化成功!")
    print(f"内容长度: {len(parsed_data['language_style']['content'])}")
    print(f"元数据: {parsed_data['language_style']['metadata']}")
except Exception as e:
    print(f"\n反序列化失败: {e}")

# 测试在HTML中使用的方法
html_script = f"""
<script>
    window.analysisResultsData = {json_str};
    console.log('数据:', window.analysisResultsData);
</script>
"""

print("\nHTML脚本:")
print(html_script)

# 测试使用data属性
html_data_attr = f"""
<div id="analysis-data" data-analysis='{json_str}'></div>
<script>
    var dataElement = document.getElementById('analysis-data');
    var analysisDataStr = dataElement.getAttribute('data-analysis');
    var analysisData = JSON.parse(analysisDataStr);
    console.log('数据:', analysisData);
</script>
"""

print("\nHTML data属性:")
print(html_data_attr)
