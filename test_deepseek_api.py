"""
测试DeepSeek API连接
"""
import os
import sys
import logging
import json
import requests
import time

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 使用DEBUG级别以获取更多信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('deepseek_api_test.log')
    ]
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入配置
import config

def test_api_key_format():
    """测试API密钥格式"""
    api_key = config.DEEPSEEK_API_KEY
    logger.info(f"API密钥长度: {len(api_key)}")
    logger.info(f"API密钥前缀: {api_key[:6]}")
    
    # 检查是否包含Bearer前缀
    if api_key.startswith("Bearer "):
        logger.warning("API密钥包含Bearer前缀，这可能导致问题")
        api_key = api_key[7:]  # 移除Bearer前缀
        logger.info(f"移除Bearer前缀后的API密钥: {api_key[:6]}...")
    
    # 检查是否是有效的API密钥格式
    if len(api_key) < 20:
        logger.error(f"API密钥长度不足: {len(api_key)}")
        return False
    
    return True

def test_api_endpoint():
    """测试API端点是否可访问"""
    endpoint = config.SUPPORTED_MODELS["deepseek-r1"]["endpoint"]
    logger.info(f"API端点: {endpoint}")
    
    try:
        # 发送简单的HEAD请求检查端点是否可访问
        response = requests.head(endpoint, timeout=10)
        logger.info(f"API端点响应状态码: {response.status_code}")
        
        # 即使返回4xx也表示端点存在
        if response.status_code < 500:
            logger.info("API端点可访问")
            return True
        else:
            logger.error(f"API端点不可访问，状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"访问API端点时出错: {str(e)}")
        return False

def test_api_auth():
    """测试API认证"""
    api_key = config.DEEPSEEK_API_KEY
    endpoint = config.SUPPORTED_MODELS["deepseek-r1"]["endpoint"]
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # 设置最小请求体
    payload = {
        "model": "deepseek-r1",
        "input": {
            "prompt": "Hello",
            "parameters": {
                "max_tokens": 10
            }
        }
    }
    
    try:
        # 发送请求
        logger.info("测试API认证...")
        response = requests.post(endpoint, headers=headers, json=payload, timeout=30)
        logger.info(f"API认证响应状态码: {response.status_code}")
        
        # 检查响应
        if response.status_code == 200:
            logger.info("API认证成功")
            return True
        elif response.status_code == 401:
            logger.error("API认证失败: 未授权")
            logger.error(f"响应内容: {response.text}")
            return False
        else:
            logger.error(f"API认证测试返回非预期状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            return False
    except Exception as e:
        logger.error(f"测试API认证时出错: {str(e)}")
        return False

def test_api_request_format():
    """测试API请求格式"""
    api_key = config.DEEPSEEK_API_KEY
    endpoint = config.SUPPORTED_MODELS["deepseek-r1"]["endpoint"]
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # 设置请求体 - 使用官方文档中的格式
    payload = {
        "model": "deepseek-r1",
        "input": {
            "prompt": "请简要分析以下文本的语言风格：\n\n这是一段测试文本，用于验证API调用是否正常工作。",
            "parameters": {
                "max_tokens": 100,
                "temperature": 0.1,
                "top_p": 0.8,
                "top_k": 50,
                "repetition_penalty": 1.1,
                "stop": []
            }
        }
    }
    
    try:
        # 发送请求
        logger.info("测试API请求格式...")
        logger.debug(f"请求头: {headers}")
        logger.debug(f"请求体: {json.dumps(payload, ensure_ascii=False)}")
        
        response = requests.post(endpoint, headers=headers, json=payload, timeout=30)
        logger.info(f"API请求响应状态码: {response.status_code}")
        
        # 检查响应
        if response.status_code == 200:
            logger.info("API请求格式正确")
            try:
                response_json = response.json()
                logger.info(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)[:500]}...")
                return True
            except Exception as e:
                logger.error(f"解析响应内容时出错: {str(e)}")
                logger.error(f"响应内容: {response.text[:500]}...")
                return False
        else:
            logger.error(f"API请求格式测试返回非预期状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            return False
    except Exception as e:
        logger.error(f"测试API请求格式时出错: {str(e)}")
        return False

def test_full_api_call():
    """完整测试API调用"""
    api_key = config.DEEPSEEK_API_KEY
    endpoint = config.SUPPORTED_MODELS["deepseek-r1"]["endpoint"]
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # 设置请求体 - 使用实际的分析提示词
    prompt = "你是一位经验丰富的文学分析专家，擅长分析各类小说文本。请对以下小说文本进行详细的语言风格分析。 摘要: 请对以下小说文本进行详细的语言风格分析。逐字逐句分析，关注作者的用词、修辞手法、语气和表达方式。 \n\n文本：\n这是一段测试文本，用于验证API调用是否正常工作。作者使用了简洁明了的语言，直接表达了测试的目的。"
    
    payload = {
        "model": "deepseek-r1",
        "input": {
            "prompt": prompt,
            "parameters": {
                "max_tokens": 500,
                "temperature": 0.1,
                "top_p": 0.8,
                "top_k": 50,
                "repetition_penalty": 1.1,
                "stop": []
            }
        }
    }
    
    try:
        # 发送请求
        logger.info("执行完整API调用测试...")
        start_time = time.time()
        
        response = requests.post(endpoint, headers=headers, json=payload, timeout=60)
        end_time = time.time()
        logger.info(f"API调用耗时: {end_time - start_time:.2f}秒")
        logger.info(f"API调用响应状态码: {response.status_code}")
        
        # 检查响应
        if response.status_code == 200:
            logger.info("API调用成功")
            try:
                response_json = response.json()
                logger.info(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)[:500]}...")
                
                # 提取生成的文本
                output = response_json.get("output", {})
                if not output:
                    logger.error("响应中没有output字段")
                    logger.error(f"完整响应: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
                    return False
                
                # 检查是否有text字段
                text = output.get("text", "")
                if text:
                    logger.info(f"生成的文本: {text[:200]}...")
                else:
                    # 检查是否有choices字段
                    choices = output.get("choices", [])
                    if choices and len(choices) > 0:
                        choice = choices[0]
                        message = choice.get("message", {})
                        content = message.get("content", "")
                        if content:
                            logger.info(f"从choices中提取的内容: {content[:200]}...")
                        else:
                            logger.error("无法从choices中提取内容")
                            logger.error(f"choice内容: {json.dumps(choice, indent=2, ensure_ascii=False)}")
                            return False
                    else:
                        logger.error("响应中既没有text字段也没有choices字段")
                        logger.error(f"output内容: {json.dumps(output, indent=2, ensure_ascii=False)}")
                        return False
                
                # 提取令牌使用信息
                usage = output.get("usage", {})
                prompt_tokens = usage.get("prompt_tokens", 0)
                completion_tokens = usage.get("completion_tokens", 0)
                total_tokens = prompt_tokens + completion_tokens
                logger.info(f"令牌使用: 输入={prompt_tokens}, 输出={completion_tokens}, 总计={total_tokens}")
                
                return True
            except Exception as e:
                logger.error(f"解析响应内容时出错: {str(e)}")
                logger.error(f"响应内容: {response.text[:500]}...")
                return False
        else:
            logger.error(f"API调用失败，状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            return False
    except Exception as e:
        logger.error(f"执行API调用时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始测试DeepSeek API连接")
    
    # 测试API密钥格式
    if not test_api_key_format():
        logger.error("API密钥格式测试失败")
        return
    
    # 测试API端点
    if not test_api_endpoint():
        logger.error("API端点测试失败")
        return
    
    # 测试API认证
    if not test_api_auth():
        logger.error("API认证测试失败")
        return
    
    # 测试API请求格式
    if not test_api_request_format():
        logger.error("API请求格式测试失败")
        return
    
    # 完整测试API调用
    if not test_full_api_call():
        logger.error("完整API调用测试失败")
        return
    
    logger.info("所有测试通过，DeepSeek API连接正常")

if __name__ == "__main__":
    main()
