import sqlite3
import os
import json

def check_database():
    try:
        print(f"当前工作目录: {os.getcwd()}")
        print(f"数据库文件是否存在: {os.path.exists('novels.db')}")

        # 连接数据库
        conn = sqlite3.connect('novels.db')
        cursor = conn.cursor()

        # 获取所有表
        print("\n数据库中的表:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"- {table[0]}")

        # 检查analysis_results表
        print("\n检查analysis_results表:")
        try:
            cursor.execute("PRAGMA table_info(analysis_results)")
            columns = cursor.fetchall()

            if columns:
                print("analysis_results表的列:")
                for column in columns:
                    print(f"- {column[1]} ({column[2]})")
            else:
                print("analysis_results表不存在或没有列")
        except Exception as e:
            print(f"检查analysis_results表时出错: {str(e)}")

        # 检查chapter_analysis_results表
        print("\n检查chapter_analysis_results表:")
        try:
            cursor.execute("PRAGMA table_info(chapter_analysis_results)")
            columns = cursor.fetchall()

            if columns:
                print("chapter_analysis_results表的列:")
                for column in columns:
                    print(f"- {column[1]} ({column[2]})")
            else:
                print("chapter_analysis_results表不存在或没有列")
        except Exception as e:
            print(f"检查chapter_analysis_results表时出错: {str(e)}")

        # 查询小说列表
        print("\n查询小说列表:")
        cursor.execute('''
        SELECT id, title, author, word_count, novel_metadata
        FROM novels
        ''')
        novels = cursor.fetchall()

        if novels:
            print(f"找到 {len(novels)} 本小说:")
            for novel in novels:
                novel_id, title, author, word_count, metadata_str = novel

                # 解析元数据JSON
                is_template = False
                if metadata_str:
                    try:
                        metadata = json.loads(metadata_str)
                        is_template = metadata.get('is_template', False)
                    except:
                        pass

                print(f'ID: {novel_id}, 标题: {title}, 作者: {author}, 字数: {word_count}, 是否为参考蓝本: {is_template}')

                # 查询小说分析结果
                cursor.execute('''
                SELECT dimension, content, reasoning_content, created_at
                FROM analysis_results
                WHERE novel_id = ?
                ''', (novel_id,))
                results = cursor.fetchall()

                if results:
                    print(f'  整本书分析结果数量: {len(results)}')
                    for result in results:
                        dimension, content, reasoning_content, created_at = result
                        content_length = len(content) if content else 0
                        reasoning_length = len(reasoning_content) if reasoning_content else 0
                        print(f'  维度: {dimension}, 分析结果长度: {content_length}, 推理过程长度: {reasoning_length}, 创建时间: {created_at}')
                else:
                    print('  没有整本书分析结果')

                # 查询章节
                cursor.execute('''
                SELECT id, title, chapter_number FROM chapters
                WHERE novel_id = ?
                ORDER BY chapter_number
                ''', (novel_id,))
                chapters = cursor.fetchall()
                print(f'  章节数量: {len(chapters)}')

                # 查询章节分析结果
                for chapter in chapters[:3]:  # 只显示前3个章节的信息，避免输出过多
                    chapter_id, chapter_title, chapter_number = chapter
                    cursor.execute('''
                    SELECT dimension, content, reasoning_content, created_at
                    FROM chapter_analysis_results
                    WHERE novel_id = ? AND chapter_id = ?
                    ''', (novel_id, chapter_id))
                    chapter_results = cursor.fetchall()

                    if chapter_results:
                        print(f'  章节ID: {chapter_id}, 标题: {chapter_title or f"第{chapter_number}章"}, 分析结果数量: {len(chapter_results)}')
                        for result in chapter_results:
                            dimension, content, reasoning_content, created_at = result
                            content_length = len(content) if content else 0
                            reasoning_length = len(reasoning_content) if reasoning_content else 0
                            print(f'    维度: {dimension}, 分析结果长度: {content_length}, 推理过程长度: {reasoning_length}, 创建时间: {created_at}')
                    else:
                        print(f'  章节ID: {chapter_id}, 标题: {chapter_title or f"第{chapter_number}章"}, 没有分析结果')
        else:
            print("未找到小说")

        # 关闭连接
        conn.close()

    except Exception as e:
        print(f"检查数据库时出错: {str(e)}")

if __name__ == "__main__":
    check_database()
