{% extends "base.html" %}

{% block title %}{{ novel.title }} - 章节列表{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('novel.list_novels') }}">小说列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('novel.view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">章节列表</li>
        </ol>
    </nav>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2>{{ novel.title }} - 章节列表</h2>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                    分析所有章节
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <p><strong>作者：</strong>{{ novel.author or '未知' }}</p>
                <p><strong>总字数：</strong>{{ novel.word_count }}</p>
                <p><strong>章节数：</strong>{{ chapters|length }}</p>
            </div>

            <!-- 分析进度条 -->
            <div id="analysisProgressContainer" class="mb-4" style="display: none;">
                <h4>分析进度</h4>
                <div class="progress mb-2">
                    <div id="analysisProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div id="analysisStatus" class="text-muted">准备中...</div>
                <div class="mt-2">
                    <button id="cancelAnalysisBtn" class="btn btn-sm btn-danger">取消分析</button>
                </div>
            </div>

            <!-- 章节列表 -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>章节</th>
                            <th>标题</th>
                            <th>字数</th>
                            <th>分析状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for chapter in chapters %}
                        <tr>
                            <td>{{ chapter.chapter_number }}</td>
                            <td>{{ chapter.title or '无标题' }}</td>
                            <td>{{ chapter.word_count }}</td>
                            <td>
                                {% if analysis_counts[chapter.id] > 0 %}
                                <span class="badge bg-success">已分析 ({{ analysis_counts[chapter.id] }})</span>
                                {% else %}
                                <span class="badge bg-secondary">未分析</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('chapter.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-sm btn-primary">查看</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 分析模态框 -->
<div class="modal fade" id="analyzeModal" tabindex="-1" aria-labelledby="analyzeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analyzeModalLabel">分析所有章节</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="analyzeForm">
                    <div class="mb-3">
                        <label for="dimensionSelect" class="form-label">选择分析维度</label>
                        <select class="form-select" id="dimensionSelect" required>
                            {% for dimension in dimensions %}
                            <option value="{{ dimension.key }}">{{ dimension.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="modelSelect" class="form-label">选择分析模型</label>
                        <select class="form-select" id="modelSelect">
                            <option value="deepseek-r1">DeepSeek R1</option>
                            <option value="qwen-plus-latest">通义千问-Plus-Latest</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="parallelSwitch" checked>
                            <label class="form-check-label" for="parallelSwitch">启用并行处理</label>
                        </div>
                        <div class="form-text">并行处理可以加快分析速度，但可能增加系统负载</div>
                    </div>
                    <div class="mb-3" id="maxWorkersContainer">
                        <label for="maxWorkersRange" class="form-label">最大工作线程数: <span id="maxWorkersValue">4</span></label>
                        <input type="range" class="form-range" id="maxWorkersRange" min="1" max="8" value="4">
                        <div class="form-text">增加工作线程数可以加快分析速度，但会增加系统负载</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startAnalysisBtn">开始分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const novelId = {{ novel.id }};
        let analysisInterval = null;

        // 最大工作线程数滑块
        document.getElementById('maxWorkersRange').addEventListener('input', function() {
            document.getElementById('maxWorkersValue').textContent = this.value;
        });

        // 并行处理开关
        document.getElementById('parallelSwitch').addEventListener('change', function() {
            document.getElementById('maxWorkersContainer').style.display = this.checked ? 'block' : 'none';
        });

        // 开始分析按钮
        document.getElementById('startAnalysisBtn').addEventListener('click', function() {
            const dimension = document.getElementById('dimensionSelect').value;
            const model = document.getElementById('modelSelect').value;
            const parallel = document.getElementById('parallelSwitch').checked;
            const maxWorkers = parseInt(document.getElementById('maxWorkersRange').value);

            if (!dimension) {
                alert('请选择分析维度');
                return;
            }

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('analyzeModal'));
            modal.hide();

            // 显示进度条
            document.getElementById('analysisProgressContainer').style.display = 'block';

            // 发送请求
            fetch(`/novel/${novelId}/chapters/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dimension: dimension,
                    model: model,
                    parallel: parallel,
                    max_workers: maxWorkers
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 开始轮询进度
                    startProgressPolling();
                } else {
                    alert('启动分析任务失败: ' + data.error);
                    document.getElementById('analysisProgressContainer').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('启动分析任务时出错:', error);
                alert('启动分析任务时出错');
                document.getElementById('analysisProgressContainer').style.display = 'none';
            });
        });

        // 取消分析按钮
        document.getElementById('cancelAnalysisBtn').addEventListener('click', function() {
            if (confirm('确定要取消分析任务吗？')) {
                fetch(`/novel/${novelId}/chapters/cancel`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('已取消分析任务');
                        stopProgressPolling();
                        document.getElementById('analysisProgressContainer').style.display = 'none';
                    } else {
                        alert('取消分析任务失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('取消分析任务时出错:', error);
                    alert('取消分析任务时出错');
                });
            }
        });

        // 开始轮询进度
        function startProgressPolling() {
            // 清除之前的轮询
            stopProgressPolling();

            // 开始新的轮询
            analysisInterval = setInterval(function() {
                fetch(`/novel/${novelId}/chapters/progress`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateProgressUI(data);

                        // 如果分析完成或失败，停止轮询
                        if (!data.is_running) {
                            stopProgressPolling();

                            // 如果分析完成，刷新页面
                            if (data.status === 'completed') {
                                setTimeout(function() {
                                    window.location.reload();
                                }, 2000);
                            }
                        }
                    } else {
                        console.error('获取进度失败:', data.error);
                    }
                })
                .catch(error => {
                    console.error('获取进度时出错:', error);
                });
            }, 2000);
        }

        // 停止轮询进度
        function stopProgressPolling() {
            if (analysisInterval) {
                clearInterval(analysisInterval);
                analysisInterval = null;
            }
        }

        // 更新进度UI
        function updateProgressUI(data) {
            const progressBar = document.getElementById('analysisProgressBar');
            const statusText = document.getElementById('analysisStatus');

            // 更新进度条
            progressBar.style.width = `${data.progress}%`;
            progressBar.setAttribute('aria-valuenow', data.progress);
            progressBar.textContent = `${data.progress}%`;

            // 更新状态文本
            let statusMessage = '';
            switch (data.status) {
                case 'starting':
                    statusMessage = '正在启动分析任务...';
                    break;
                case 'splitting_chapters':
                    statusMessage = '正在分割章节...';
                    break;
                case 'analyzing':
                    statusMessage = `正在分析章节 (${data.completed_chapters}/${data.total_chapters})`;
                    break;
                case 'completed':
                    statusMessage = '分析完成';
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.remove('progress-bar-striped');
                    progressBar.classList.add('bg-success');
                    break;
                case 'failed':
                    statusMessage = `分析失败: ${data.error || '未知错误'}`;
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.remove('progress-bar-striped');
                    progressBar.classList.add('bg-danger');
                    break;
                case 'cancelled':
                    statusMessage = '分析已取消';
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.remove('progress-bar-striped');
                    progressBar.classList.add('bg-warning');
                    break;
                default:
                    statusMessage = `状态: ${data.status}`;
            }

            statusText.textContent = statusMessage;
        }

        // 检查是否有正在进行的分析任务
        fetch(`/novel/${novelId}/chapters/progress`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.is_running) {
                // 显示进度条
                document.getElementById('analysisProgressContainer').style.display = 'block';

                // 更新进度UI
                updateProgressUI(data);

                // 开始轮询进度
                startProgressPolling();
            }
        })
        .catch(error => {
            console.error('检查分析任务时出错:', error);
        });
    });
</script>
{% endblock %}
