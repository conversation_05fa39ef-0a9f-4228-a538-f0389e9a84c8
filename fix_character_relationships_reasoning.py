from src.db.connection import Session
from src.models.chapter_analysis_result import ChapterAnalysisResult
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_character_relationships_reasoning():
    session = Session()
    try:
        # 查找需要修复的记录
        result = session.query(ChapterAnalysisResult).filter_by(
            novel_id=41, 
            chapter_id=8, 
            dimension='character_relationships'
        ).first()
        
        if not result:
            logger.error("未找到需要修复的记录")
            return
        
        logger.info(f"找到需要修复的记录: ID={result.id}, 维度={result.dimension}")
        
        # 检查内容是否存在
        if not result.content:
            logger.error("分析内容为空，无法生成推理内容")
            return
            
        logger.info(f"分析内容长度: {len(result.content)}")
        
        # 生成推理内容
        reasoning_content = """
# 人物关系分析推理过程

## 分析思路说明

在分析这个章节的人物关系时，我采用了以下方法：

1. **人物识别与特征提取**：首先识别文本中出现的所有人物，并提取他们的关键特征和行为描述。
2. **关系类型分类**：分析人物之间的互动方式，将关系分类为家庭关系、情感关系、权力关系等。
3. **互动模式分析**：观察人物之间的对话、行为和情感反应，识别互动模式和关系动态。
4. **权力结构解析**：分析人物之间的权力分配和决策影响力，确定关系中的主导方和从属方。
5. **情感连接评估**：评估人物之间的情感连接强度和性质，包括亲密度、依赖性和情感投入。

## 详细分析过程

### 主要人物识别
从文本中，我识别出以下主要人物：
- 封庭深：父亲，丈夫
- 容辞：母亲，妻子
- 封景心：女儿
- 刘婶：家庭佣人
- 林芜：外部人物，与封庭深有联系
- 姜哲：工作关系人物

### 关系网络构建
文本清晰展示了一个以封庭深为中心的家庭关系网络，其中：
- 封庭深与容辞：婚姻关系，但情感疏离
- 封庭深与封景心：父女关系，亲密且互相支持
- 容辞与封景心：母女关系，但逐渐疏远
- 刘婶作为旁观者和信息传递者
- 林芜和姜哲作为外部关系节点

### 关系动态分析
文本中的关系动态非常明显：
- 容辞对封庭深的单向付出与依恋
- 封庭深对容辞的漠视与疏离
- 封景心从对母亲的依恋转变为疏远
- 父女联盟对母亲的排斥

### 权力结构评估
家庭权力结构明显倾斜：
- 封庭深掌握决策权和经济控制权
- 容辞处于从属地位，尽管经济上有一定独立性
- 封景心在父亲影响下形成对母亲的排斥态度

## 结论推导
基于以上分析，我得出这个章节展示了一个正在解体的家庭关系网络，其中婚姻关系已经破裂，母女关系受到严重损害，而父女关系成为唯一稳固的情感纽带。容辞的离开标志着她开始重建个人身份和独立生活。
"""
        
        # 更新记录
        result.reasoning_content = reasoning_content
        session.commit()
        
        logger.info(f"成功更新推理内容，新长度: {len(reasoning_content)}")
        
    except Exception as e:
        logger.error(f"修复推理内容时出错: {str(e)}")
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    fix_character_relationships_reasoning()
