/**
 * Bootstrap bundle fix script
 * This script loads the bootstrap.bundle.min.js from a CDN and falls back to a local copy if needed
 */

(function() {
    // Function to load script
    function loadScript(src, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = src;
        script.onload = callback;
        script.onerror = function() {
            console.warn('Failed to load script from ' + src);
            if (callback) callback(new Error('Failed to load script'));
        };
        document.head.appendChild(script);
    }

    // Try to load from CDN first
    loadScript('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js', function(error) {
        if (error) {
            console.log('Falling back to local bootstrap bundle');
            // If CDN fails, load from local path
            loadScript('/static/js/lib/bootstrap.bundle.min.js', function(error) {
                if (error) {
                    console.error('Failed to load bootstrap bundle from local path');
                }
            });
        } else {
            console.log('Bootstrap bundle loaded from CDN');
        }
    });
})();
