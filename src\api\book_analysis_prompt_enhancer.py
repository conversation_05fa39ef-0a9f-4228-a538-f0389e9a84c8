"""
九猫系统 - 整本书分析提示词增强器
用于增强整本书分析提示词，确保分析更加详细和完善
"""

import logging
import re
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

def enhance_book_analysis_prompt(original_prompt: str, dimension: str, prompt_template: str = "default") -> str:
    """
    增强整本书分析提示词，添加用户要求的补充内容

    Args:
        original_prompt: 原始提示词
        dimension: 分析维度
        prompt_template: 提示词模板，默认为 default，可选 simplified

    Returns:
        增强后的提示词
    """
    # 根据提示词模板选择不同的增强内容
    if prompt_template == "simplified":
        # 精简版温和优化增强内容 - 在保证质量基础上进一步降本增效
        general_enhancement = """
重要补充要求（精简版温和优化模式）：
1. 基于原文分析：分析必须基于小说的实际内容，避免泛泛而谈
2. 重点突出：重点描述关键场景、事件和人物关系
3. 精炼详细：分析结果要精炼但保持必要的详细度，重点突出核心内容
4. 核心分析：重点分析原因、效果和关键意义，避免冗余描述
5. 简洁明了：使用清晰、简洁的语言表达，避免重复和冗长

分析内容应包含：
- 关键文本引用：精选原文中最重要的段落或句子作为依据
- 核心分析框架：包括主要特点、具体表现、关键效果（重点突出）
- 重点解读：对关键内容进行深入但简洁的解读
- 主要覆盖：分析覆盖小说的主要部分，避免过度细节
- 典型例证：使用最具代表性的例子支持分析观点

以上要求针对精简版温和优化模式，在保证分析质量的同时进一步提高效率和控制成本。
"""
    else:
        # 默认版增强内容 - 完整模式
        general_enhancement = """
重要补充要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于小说的实际内容，不允许泛泛而谈或猜测
2. 必须详细叙述原文内容：极其详尽地描述小说中的关键场景、事件、对话和人物心理活动
3. 不限字数，越详细越好：分析结果必须极其详细，不设字数限制，至少8000字以上
4. 必须提供深入的分析：不仅描述现象，还要分析原因、效果和意义
5. 语言必须通俗易懂：使用清晰、生动的语言，避免过多专业术语

分析内容必须包含：
- 详细的文本引用：引用原文中的具体段落、句子或词语，作为分析的依据
- 完整的分析框架：使用清晰的结构，包括总体特点、具体表现、变化规律、效果评估和创新点
- 深入的解读：对引用的文本进行深入解读，分析其含义、作用和效果
- 全面的覆盖：分析应覆盖小说的各个部分，不仅限于开头或结尾
- 具体的例证：使用具体的例子支持分析观点，不做空泛的评价

以上要求是首要执行的，必须执行的！在进行分析时，请首先考虑这些要求。
"""

    # 特定维度的增强内容
    if prompt_template == "simplified":
        # 精简版特定维度增强内容
        dimension_specific_enhancements = {
            "chapter_outline": """
章纲分析特别要求（精简版温和优化模式）：
1. 基于原文分析：分析基于小说的实际内容，避免泛泛而谈
2. 重点描述章节内容：重点描述主要章节的关键场景、事件和人物活动
3. 精炼详细：内容重现要精炼但保持必要详细度，重点突出核心内容
4. 分析章节连贯性：分析章节间的主要关系和连接方式
5. 简洁明了：使用清晰、简洁的语言表达，避免冗余

内容重现应包含：
- 主要章节概述：按章节顺序描述核心内容和关键情节发展
- 关键事件过程：描述最重要事件的起因、经过和结果
- 人物发展要点：描述主要人物的关键表现和重要发展变化
- 情节线索梳理：梳理主要情节线索及其核心发展

以上要求针对精简版温和优化模式，在保证分析质量的同时进一步控制成本。
""",
            "outline_analysis": """
大纲分析特别要求（精简版温和优化模式）：
1. 基于原文分析：分析基于小说的实际内容，避免泛泛而谈
2. 重点重现作品内容：重点重现作品的主要内容和关键情节
3. 精炼详细：内容重现要精炼但保持必要详细度，重点突出核心内容
4. 分析整体结构：分析作品的主要结构和架构特点
5. 简洁明了：使用清晰、简洁的语言表达，避免冗余

内容重现应包含：
- 主要场景描述：描述最关键场景的环境和氛围
- 关键事件过程：描述最重要事件的起因、经过和结果
- 人物对话要点：记录主要人物的核心对话内容
- 人物心理活动：描述人物的关键心理活动和重要情感变化
- 情节发展脉络：展示情节的核心发展过程

以上要求针对精简版温和优化模式，在保证分析质量的同时进一步控制成本。
""",
            "language_style": """
语言风格分析特别要求（精简版温和优化模式）：
1. 基于原文分析：分析基于小说的实际内容，避免泛泛而谈
2. 引用关键原文：精选最重要的原文片段作为分析依据
3. 精炼详细：分析结果要精炼但保持必要详细度，重点突出核心特点
4. 分析语言特点：分析作者的主要用词、句式和表达方式
5. 简洁明了：使用清晰、简洁的语言表达，避免冗余

分析内容应包含：
- 词汇选择分析：分析作者使用的核心词汇特点
- 句式结构解析：分析句子的主要特点和关键语法特征
- 修辞手法识别：识别最重要的修辞手法及其效果
- 语气语调评估：分析文本的整体语气特点
- 风格一致性考察：评估风格的一致性或重要变化

以上要求针对精简版温和优化模式，在保证分析质量的同时进一步控制成本。
""",
            "rhythm_pacing": """
节奏节拍分析特别要求（精简版温和优化模式）：
1. 基于原文分析：分析基于小说的实际内容，避免泛泛而谈
2. 引用关键原文：精选最重要的原文片段作为分析依据
3. 精炼详细：分析结果要精炼但保持必要详细度，重点突出核心节奏特点
4. 分析节奏变化：分析文本的主要节奏变化和情节推进特点
5. 简洁明了：使用清晰、简洁的语言表达，避免冗余

分析内容应包含：
- 整体节奏特点：分析作品的核心节奏特点
- 章节节奏分析：分析主要章节的关键节奏特点
- 场景节奏控制：分析最重要场景的节奏控制方式
- 情节推进速度：分析情节推进的主要特点
- 高潮铺垫技巧：分析高潮前的核心铺垫技巧

以上要求针对精简版温和优化模式，在保证分析质量的同时进一步控制成本。
"""
        }
    else:
        # 默认版特定维度增强内容
        dimension_specific_enhancements = {
            "chapter_outline": """
章纲分析特别要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于小说的实际内容，不允许泛泛而谈或猜测
2. 必须极其详细叙述原文内容：极其详尽地描述小说中的所有章节内容，包括场景、事件、对话和人物心理活动
3. 不限字数，必须极其详细：内容重现部分必须极其详细，至少15000字以上
4. 必须分析章节间的连贯性：分析章节之间的关系和连接方式，展示整体叙事的连贯性和发展
5. 语言必须通俗易懂：使用清晰、生动的语言，模仿原文的风格和语气

内容重现部分必须包含：
- 详细的章节概述（至少5000字）：按照章节顺序，详细描述每个章节的主要内容和情节发展
- 完整的事件过程（至少5000字）：按照发生顺序详细描述小说中的主要事件的起因、经过和结果
- 人物发展轨迹（至少3000字）：描述主要人物在不同章节中的表现和发展变化
- 情节线索梳理（至少2000字）：梳理小说中的主要情节线索，分析它们如何在各章节中展开和发展

以上要求是首要执行的，必须执行的！在进行章纲分析时，请首先考虑这些要求。
""",
        "outline_analysis": """
大纲分析特别要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于小说的实际内容，不允许泛泛而谈或猜测
2. 必须极其详细叙述原文内容：极其详尽地重现整部作品的完整内容，以小说叙述的方式进行
3. 不限字数，必须极其详细：内容重现部分必须极其详细，至少20000字以上
4. 必须分析整体结构：分析作品的整体结构与架构设计，评估结构模式的应用情况
5. 语言必须通俗易懂：使用清晰、生动的语言，让读者仿佛亲历其境

内容重现部分必须包含：
- 详细的场景描述（至少5000字）：描述作品中的关键场景的环境、氛围和背景，包括光线、声音、气味等感官细节
- 完整的事件过程（至少5000字）：按照发生顺序详细描述整部作品中的每个重要事件的起因、经过和结果
- 人物对话与互动（至少5000字）：完整记录主要人物的对话内容，尽可能使用原文中的重要对话
- 人物心理活动（至少3000字）：深入描述人物的心理活动、情感变化和内心冲突
- 情节发展脉络（至少2000字）：清晰展示情节如何从开始发展到结束，包括转折点和高潮

以上要求是首要执行的，必须执行的！在进行大纲分析时，请首先考虑这些要求。
""",
        "language_style": """
语言风格分析特别要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于小说的实际内容，不允许泛泛而谈或猜测
2. 必须引用大量原文：分析中必须引用大量原文片段，作为分析的依据
3. 不限字数，必须极其详细：分析结果必须极其详细，至少10000字以上
4. 必须分析语言特点：详细分析作者的用词、句式、修辞手法、语气和表达方式
5. 语言必须通俗易懂：使用清晰、生动的语言，避免过多专业术语

分析内容必须包含：
- 词汇选择分析（至少2000字）：分析作者使用的词汇特点，如正式/口语、具体/抽象、简单/复杂等
- 句式结构解析（至少2000字）：分析句子长短变化、复杂程度和语法特点
- 修辞手法识别（至少2000字）：识别比喻、拟人、排比等修辞手法的使用及其效果
- 语气语调评估（至少2000字）：分析文本的整体语气，如幽默、严肃、讽刺、温暖等
- 风格一致性考察（至少2000字）：评估风格在全文中的一致性或有意的变化

以上要求是首要执行的，必须执行的！在进行语言风格分析时，请首先考虑这些要求。
""",
        "rhythm_pacing": """
节奏节拍分析特别要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于小说的实际内容，不允许泛泛而谈或猜测
2. 必须引用大量原文：分析中必须引用大量原文片段，作为分析的依据
3. 不限字数，必须极其详细：分析结果必须极其详细，至少10000字以上
4. 必须分析节奏变化：详细分析文本的节奏变化、紧张与舒缓的交替、情节推进的速度等
5. 语言必须通俗易懂：使用清晰、生动的语言，避免过多专业术语

分析内容必须包含：
- 整体节奏特点（至少2000字）：分析作品的整体节奏特点，如快速紧凑型、舒缓铺陈型、波动变化型等
- 章节节奏分析（至少2000字）：分析不同章节的节奏特点及其变化
- 场景节奏控制（至少2000字）：分析关键场景的节奏控制方式及其效果
- 情节推进速度（至少2000字）：分析情节推进的速度变化及其原因
- 高潮铺垫技巧（至少2000字）：分析高潮前的铺垫和节奏控制技巧

以上要求是首要执行的，必须执行的！在进行节奏节拍分析时，请首先考虑这些要求。
"""
    }

    # 获取特定维度的增强内容，如果没有则使用通用增强内容
    enhancement = dimension_specific_enhancements.get(dimension, general_enhancement)

    # 检查原始提示词是否已经包含增强内容
    if prompt_template == "simplified":
        # 精简版检查
        if "精简版降本增效模式" in original_prompt:
            logger.info(f"提示词已包含精简版增强内容，无需再次添加")
            return original_prompt
    else:
        # 默认版检查
        if "必须使用原文" in original_prompt and "必须详细叙述原文内容" in original_prompt:
            logger.info(f"提示词已包含默认版增强内容，无需再次添加")
            return original_prompt

    # 在提示词的开头添加增强内容
    # 查找合适的插入位置：在第一个"你是"或"请"之后
    match = re.search(r'(你是.*?专家.*?[。，,.]|请.*?分析.*?[。，,.])', original_prompt)
    if match:
        insert_position = match.end()
        enhanced_prompt = original_prompt[:insert_position] + enhancement + original_prompt[insert_position:]
    else:
        # 如果找不到合适的位置，就在开头添加
        enhanced_prompt = enhancement + original_prompt

    logger.info(f"成功增强{dimension}维度的整本书分析提示词（{prompt_template}版）")
    return enhanced_prompt
