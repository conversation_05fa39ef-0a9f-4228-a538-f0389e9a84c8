{% extends "v3.1/base.html" %}

{% block title %}{{ chapter.title }} - {{ novel.title }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .chapter-header {
        background: linear-gradient(135deg, #FFF8E1, #FFFCF5);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
        border: 1px solid #E6D7B9;
    }

    .chapter-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .chapter-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        color: #5D4037;
    }

    .chapter-header p {
        position: relative;
        z-index: 1;
        color: #5D4037;
    }

    .chapter-meta {
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #E6D7B9;
    }

    .chapter-meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .chapter-meta-item i {
        width: 24px;
        margin-right: 0.5rem;
        color: #FF8F00;
    }

    .dimension-card {
        transition: all 0.3s ease;
        border: 1px solid #E6D7B9;
        background-color: #FFFFFF;
        height: 100%;
    }

    .dimension-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }

    .dimension-card .card-header {
        background-color: #FFF8E1;
        border-bottom: 1px solid #E6D7B9;
    }

    .dimension-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #FF8F00;
    }

    .dimension-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .btn-primary {
        background-color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-primary:hover {
        background-color: #F57C00;
        border-color: #F57C00;
    }

    .btn-outline-primary {
        color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-outline-primary:hover {
        background-color: #FF8F00;
        border-color: #FF8F00;
        color: white;
    }

    .progress {
        height: 0.5rem;
        background-color: #E6D7B9;
    }

    .progress-bar {
        background-color: #FF8F00;
    }

    .tab-content {
        padding: 1.5rem;
        background-color: #FFFFFF;
        border: 1px solid #E6D7B9;
        border-top: none;
        border-radius: 0 0 0.5rem 0.5rem;
    }

    .nav-tabs .nav-link {
        color: #5D4037;
        border: 1px solid transparent;
    }

    .nav-tabs .nav-link.active {
        color: #FF8F00;
        border-color: #E6D7B9;
        border-bottom-color: #FFFFFF;
        background-color: #FFFFFF;
    }

    .chapter-navigation {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
    }

    .badge.bg-primary {
        background-color: #FF8F00 !important;
    }

    .badge.bg-secondary {
        background-color: #757575 !important;
    }

    .badge.bg-success {
        background-color: #43A047 !important;
    }

    .badge.bg-warning {
        background-color: #FFB300 !important;
    }

    .badge.bg-info {
        background-color: #29B6F6 !important;
    }

    .chapter-content {
        white-space: pre-wrap;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        line-height: 1.8;
        padding: 1rem;
        background-color: #FFFFFF;
        border: 1px solid #E6D7B9;
        border-radius: 0.5rem;
        max-height: 500px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<!-- 章节头部信息 -->
<div class="chapter-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">{{ chapter.title }}</h1>
            <p class="lead mb-2">
                <i class="fas fa-book me-2"></i>{{ novel.title }}
                {% if novel.author %}
                <span class="ms-2"><i class="fas fa-user me-1"></i>{{ novel.author }}</span>
                {% endif %}
            </p>
            <p class="mb-0">
                <span class="badge bg-primary me-2">第{{ chapter.chapter_number }}章</span>
                <span class="badge bg-info me-2">{{ chapter.word_count }} 字</span>
                <span class="badge bg-success me-2">{{ chapter.analyzed_dimensions|default([])|length }}/15 维度已分析</span>
            </p>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3_1.chapters_list', novel_id=novel.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-list me-1"></i>章节列表
                </a>
                <a href="{{ url_for('v3_1.view_novel', novel_id=novel.id) }}" class="btn btn-outline-primary">
                    <i class="fas fa-book me-1"></i>返回小说
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 章节导航 -->
<div class="chapter-navigation mb-4">
    {% if prev_chapter %}
    <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=prev_chapter.id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-chevron-left me-1"></i>上一章：{{ prev_chapter.title }}
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        <i class="fas fa-chevron-left me-1"></i>已是第一章
    </button>
    {% endif %}

    {% if next_chapter %}
    <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=next_chapter.id) }}" class="btn btn-outline-secondary">
        下一章：{{ next_chapter.title }}<i class="fas fa-chevron-right ms-1"></i>
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        已是最后一章<i class="fas fa-chevron-right ms-1"></i>
    </button>
    {% endif %}
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-md-4 mb-4">
        <!-- 章节元数据 -->
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>章节信息</h3>
            </div>
            <div class="card-body">
                <div class="chapter-meta">
                    <div class="chapter-meta-item">
                        <i class="fas fa-heading"></i>
                        <span>标题: {{ chapter.title }}</span>
                    </div>
                    <div class="chapter-meta-item">
                        <i class="fas fa-sort-numeric-up"></i>
                        <span>章节编号: {{ chapter.chapter_number }}</span>
                    </div>
                    <div class="chapter-meta-item">
                        <i class="fas fa-font"></i>
                        <span>字数: {{ chapter.word_count }}</span>
                    </div>
                    <div class="chapter-meta-item">
                        <i class="fas fa-chart-pie"></i>
                        <span>分析维度: {{ chapter.analyzed_dimensions|default([])|length }}/15</span>
                    </div>
                </div>

                <div class="d-grid gap-2 mt-3">
                    <a href="{{ url_for('v3_1.analyze_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-primary">
                        <i class="fas fa-cogs me-1"></i>分析所有维度
                    </a>
                </div>
            </div>
        </div>

        <!-- 分析进度 -->
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-tasks me-2"></i>分析进度</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5>章节分析</h5>
                    <div class="progress mb-2">
                        <div class="progress-bar" role="progressbar" style="width: {{ (chapter.analyzed_dimensions|default([])|length / 15) * 100 }}%;" aria-valuenow="{{ chapter.analyzed_dimensions|default([])|length }}" aria-valuemin="0" aria-valuemax="15"></div>
                    </div>
                    <small class="text-muted">{{ chapter.analyzed_dimensions|default([])|length }}/15 维度已分析</small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- 章节内容标签页 -->
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="chapterTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="true">
                            <i class="fas fa-file-alt me-1"></i>章节内容
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="dimensions-tab" data-bs-toggle="tab" data-bs-target="#dimensions" type="button" role="tab" aria-controls="dimensions" aria-selected="false">
                            <i class="fas fa-chart-bar me-1"></i>分析维度
                        </button>
                    </li>
                </ul>
            </div>
            <div class="tab-content" id="chapterTabContent">
                <!-- 章节内容标签页内容 -->
                <div class="tab-pane fade show active" id="content" role="tabpanel" aria-labelledby="content-tab">
                    <div class="chapter-content">
                        {% if chapter.content %}
                            {{ chapter.content }}
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <h4>内容不可用</h4>
                                <p class="text-muted">该章节内容不可用或尚未加载。</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- 分析维度标签页内容 -->
                <div class="tab-pane fade" id="dimensions" role="tabpanel" aria-labelledby="dimensions-tab">
                    <div class="row">
                        {% for dimension in dimensions %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="dimension-card card h-100">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ dimension.name }}</h5>
                                    {% if dimension.key in chapter.analyzed_dimensions|default([]) %}
                                    <span class="badge bg-success dimension-status">已分析</span>
                                    {% else %}
                                    <span class="badge bg-secondary dimension-status">未分析</span>
                                    {% endif %}
                                </div>
                                <div class="card-body text-center">
                                    <i class="{{ dimension.icon }} dimension-icon"></i>
                                    <p class="card-text">{{ dimension.description }}</p>
                                </div>
                                <div class="card-footer bg-transparent">
                                    {% if dimension.key in chapter.analyzed_dimensions|default([]) %}
                                    <a href="{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-eye me-1"></i>查看分析
                                    </a>
                                    {% else %}
                                    <a href="{{ url_for('v3_1.analyze_chapter_dimension', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" class="btn btn-primary w-100 analyze-btn" data-dimension="{{ dimension.key }}">
                                        <i class="fas fa-cogs me-1"></i>开始分析
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 章节导航（底部） -->
<div class="chapter-navigation mt-4">
    {% if prev_chapter %}
    <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=prev_chapter.id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-chevron-left me-1"></i>上一章：{{ prev_chapter.title }}
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        <i class="fas fa-chevron-left me-1"></i>已是第一章
    </button>
    {% endif %}

    {% if next_chapter %}
    <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=next_chapter.id) }}" class="btn btn-outline-secondary">
        下一章：{{ next_chapter.title }}<i class="fas fa-chevron-right ms-1"></i>
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        已是最后一章<i class="fas fa-chevron-right ms-1"></i>
    </button>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 开始分析按钮点击事件
        $('.analyze-btn').click(function(e) {
            e.preventDefault();
            const dimension = $(this).data('dimension');
            const url = $(this).attr('href');

            // 显示确认对话框
            if (confirm(`确定要开始分析"${dimension}"维度吗？\n\n分析过程可能需要几分钟时间，请耐心等待。`)) {
                // 显示加载状态
                const originalText = $(this).html();
                $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...');
                $(this).prop('disabled', true);

                // 跳转到分析页面
                window.location.href = url;
            }
        });

        // 标签页切换事件
        $('#chapterTab button').on('shown.bs.tab', function (e) {
            // 如果切换到分析维度标签页，可以在这里添加额外的逻辑
            if ($(e.target).attr('id') === 'dimensions-tab') {
                console.log('分析维度标签页已激活');
            }
        });

        // 检查URL中的tab参数，如果存在则激活对应的标签页
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab');
        if (tab) {
            // 激活对应的标签页
            $(`#chapterTab button[data-bs-target="#${tab}"]`).tab('show');
        }

        // 键盘导航
        $(document).keydown(function(e) {
            // 左箭头键 - 上一章
            if (e.keyCode === 37) {
                const prevChapterLink = $('a:contains("上一章")').first();
                if (prevChapterLink.length && !prevChapterLink.hasClass('disabled')) {
                    window.location.href = prevChapterLink.attr('href');
                }
            }
            // 右箭头键 - 下一章
            else if (e.keyCode === 39) {
                const nextChapterLink = $('a:contains("下一章")').first();
                if (nextChapterLink.length && !nextChapterLink.hasClass('disabled')) {
                    window.location.href = nextChapterLink.attr('href');
                }
            }
        });
    });
</script>
{% endblock %}