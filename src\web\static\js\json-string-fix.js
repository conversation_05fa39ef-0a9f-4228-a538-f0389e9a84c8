/**
 * 九猫 - 专门修复JSON字符串中未终止字符串的问题
 * 这个脚本会在页面加载时立即执行，修复特定的JSON解析错误
 * 增强版 - 修复character_relationships分析中的特定错误
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('增强版JSON字符串修复脚本已加载');

    // 保存原始的JSON.parse方法
    var originalJSONParse = JSON.parse;

    // 重写JSON.parse方法
    JSON.parse = function(text, reviver) {
        try {
            // 特殊处理：检查是否包含character_relationships错误
            if (text && typeof text === 'string' && text.includes('character_relationships** 时遇到了问题')) {
                console.log('检测到character_relationships特定错误，尝试特殊修复');

                // 检查是否是位置4475附近的错误
                if (text.length > 4400) {
                    // 查找错误位置前后的内容
                    var errorPos = 4475; // 根据错误消息确定的位置
                    var beforeError = text.substring(Math.max(0, errorPos - 50), errorPos);
                    var afterError = text.substring(errorPos, Math.min(text.length, errorPos + 50));

                    console.log('特定错误位置前内容:', beforeError);
                    console.log('特定错误位置后内容:', afterError);

                    // 修复特定错误 - 在错误位置添加引号
                    var fixedText = text.substring(0, errorPos) + '"' + text.substring(errorPos);
                    console.log('尝试特殊修复character_relationships错误');

                    try {
                        return originalJSONParse(fixedText, reviver);
                    } catch (specialError) {
                        console.error('特殊修复失败，尝试标准修复流程:', specialError.message);
                        // 继续执行标准修复流程
                    }
                }
            }

            // 尝试使用原始方法解析
            return originalJSONParse(text, reviver);
        } catch (e) {
            console.error('JSON.parse错误:', e.message);

            // 检查是否是未终止的字符串错误
            if (e.message.includes('Unterminated string')) {
                console.log('检测到未终止的字符串错误，尝试修复');

                try {
                    // 尝试修复未终止的字符串
                    // 1. 找到错误位置
                    var errorPos = -1;
                    var match = e.message.match(/position (\d+)/);
                    if (match) {
                        errorPos = parseInt(match[1]);
                    }

                    if (errorPos >= 0) {
                        console.log('错误位置:', errorPos);

                        // 2. 检查错误位置前后的内容
                        var beforeError = text.substring(Math.max(0, errorPos - 50), errorPos);
                        var afterError = text.substring(errorPos, Math.min(text.length, errorPos + 50));
                        console.log('错误位置前内容:', beforeError);
                        console.log('错误位置后内容:', afterError);

                        // 3. 尝试修复
                        var fixedText = '';

                        // 检查是否包含特定错误模式
                        if (beforeError.includes('character_relationships') && beforeError.includes('时遇到了问题')) {
                            // 特殊处理character_relationships错误
                            fixedText = text.substring(0, errorPos) + '"' + text.substring(errorPos);
                            console.log('尝试修复character_relationships特定错误');

                            try {
                                return originalJSONParse(fixedText, reviver);
                            } catch (e2) {
                                console.error('修复character_relationships特定错误失败:', e2.message);
                            }
                        }

                        // 检查是否是缺少引号的问题
                        var lastQuotePos = beforeError.lastIndexOf('"');
                        if (lastQuotePos >= 0) {
                            // 在错误位置添加引号
                            fixedText = text.substring(0, errorPos) + '"' + text.substring(errorPos);
                            console.log('尝试在错误位置添加引号');

                            try {
                                return originalJSONParse(fixedText, reviver);
                            } catch (e2) {
                                console.error('添加引号修复失败:', e2.message);
                            }
                        }

                        // 尝试转义错误位置前的引号
                        if (beforeError.endsWith('"') && !beforeError.endsWith('\\"')) {
                            fixedText = text.substring(0, errorPos - 1) + '\\"' + text.substring(errorPos);
                            console.log('尝试转义错误位置前的引号');

                            try {
                                return originalJSONParse(fixedText, reviver);
                            } catch (e2) {
                                console.error('转义引号修复失败:', e2.message);
                            }
                        }

                        // 尝试修复整个JSON字符串
                        // 这是一个更激进的修复方法，尝试修复整个JSON结构
                        try {
                            // 尝试修复常见的JSON格式问题
                            var sanitizedText = text
                                // 修复未转义的引号
                                .replace(/([^\\])"/g, '$1\\"')
                                // 修复未转义的换行符
                                .replace(/\n/g, '\\n')
                                // 修复未转义的制表符
                                .replace(/\t/g, '\\t')
                                // 修复未转义的回车符
                                .replace(/\r/g, '\\r')
                                // 确保JSON对象正确闭合
                                .replace(/}([^,}\]])/g, '},$1')
                                // 确保JSON数组正确闭合
                                .replace(/\]([^,}\]])/g, '],$1');

                            // 确保整个JSON字符串是一个有效的对象
                            if (!sanitizedText.startsWith('{') && !sanitizedText.startsWith('[')) {
                                sanitizedText = '{' + sanitizedText;
                            }
                            if (!sanitizedText.endsWith('}') && !sanitizedText.endsWith(']')) {
                                sanitizedText = sanitizedText + '}';
                            }

                            console.log('尝试修复整个JSON结构');
                            return originalJSONParse(sanitizedText, reviver);
                        } catch (e3) {
                            console.error('整体修复失败:', e3.message);
                        }
                    }
                } catch (fixError) {
                    console.error('修复过程中出错:', fixError.message);
                }
            }

            // 如果所有修复尝试都失败，尝试返回一个空对象而不是抛出错误
            console.warn('所有修复尝试都失败，返回空对象');
            return {};
        }
    };

    // 在页面加载完成后扫描并修复所有JSON数据
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始扫描页面中的JSON数据');

        // 特殊处理：检查是否在character_relationships页面
        var isCharacterRelationshipsPage = window.location.pathname.includes('character_relationships');
        if (isCharacterRelationshipsPage) {
            console.log('检测到character_relationships页面，应用特殊修复');

            // 查找分析内容元素
            var analysisContentElements = document.querySelectorAll('.analysis-content, .markdown-content');
            analysisContentElements.forEach(function(element) {
                try {
                    var content = element.textContent || '';
                    if (content && content.includes('character_relationships** 时遇到了问题')) {
                        console.log('找到包含character_relationships错误的内容元素');

                        // 检查是否是JSON格式
                        if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
                            try {
                                // 尝试解析JSON
                                var jsonData = JSON.parse(content);
                                console.log('内容已经是有效的JSON');
                            } catch (e) {
                                console.error('解析内容为JSON失败:', e.message);

                                // 特殊修复character_relationships错误
                                if (e.message.includes('Unterminated string') && content.length > 4400) {
                                    var errorPos = -1;
                                    var match = e.message.match(/position (\d+)/);
                                    if (match) {
                                        errorPos = parseInt(match[1]);
                                    } else {
                                        // 如果没有明确的位置，使用已知的错误位置
                                        errorPos = 4475;
                                    }

                                    console.log('尝试修复character_relationships特定错误，位置:', errorPos);
                                    var fixedContent = content.substring(0, errorPos) + '"' + content.substring(errorPos);

                                    try {
                                        // 尝试解析修复后的内容
                                        JSON.parse(fixedContent);
                                        console.log('成功修复character_relationships特定错误');

                                        // 更新元素内容
                                        element.textContent = fixedContent;
                                    } catch (e2) {
                                        console.error('修复character_relationships特定错误失败:', e2.message);
                                    }
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.error('处理character_relationships页面元素时出错:', e.message);
                }
            });
        }

        // 查找所有可能包含JSON数据的元素
        var jsonElements = document.querySelectorAll('[data-json], [data-metadata], script[type="application/json"]');

        jsonElements.forEach(function(element) {
            try {
                var jsonContent = element.textContent || element.getAttribute('data-json') || element.getAttribute('data-metadata');

                if (jsonContent) {
                    console.log('找到可能包含JSON数据的元素');

                    // 尝试解析JSON
                    try {
                        JSON.parse(jsonContent);
                        console.log('JSON数据有效');
                    } catch (e) {
                        console.error('元素中的JSON数据无效:', e.message);

                        // 检查是否是character_relationships特定错误
                        if (jsonContent.includes('character_relationships** 时遇到了问题') && e.message.includes('Unterminated string')) {
                            console.log('检测到元素中的character_relationships特定错误');

                            var errorPos = -1;
                            var match = e.message.match(/position (\d+)/);
                            if (match) {
                                errorPos = parseInt(match[1]);
                            } else {
                                // 如果没有明确的位置，使用已知的错误位置
                                errorPos = 4475;
                            }

                            var fixedJson = jsonContent.substring(0, errorPos) + '"' + jsonContent.substring(errorPos);

                            try {
                                // 尝试解析修复后的JSON
                                JSON.parse(fixedJson);
                                console.log('成功修复character_relationships特定错误');

                                // 更新元素的内容
                                if (element.tagName.toLowerCase() === 'script') {
                                    element.textContent = fixedJson;
                                } else {
                                    if (element.hasAttribute('data-json')) {
                                        element.setAttribute('data-json', fixedJson);
                                    }
                                    if (element.hasAttribute('data-metadata')) {
                                        element.setAttribute('data-metadata', fixedJson);
                                    }
                                }

                                // 已修复，跳过常规修复
                                return;
                            } catch (e2) {
                                console.error('修复character_relationships特定错误失败:', e2.message);
                                // 继续尝试常规修复
                            }
                        }

                        // 尝试修复JSON数据
                        try {
                            // 修复未终止的字符串
                            var fixedJson = jsonContent
                                // 修复未转义的引号
                                .replace(/([^\\])"/g, '$1\\"')
                                // 修复未转义的换行符
                                .replace(/\n/g, '\\n')
                                // 修复未转义的制表符
                                .replace(/\t/g, '\\t')
                                // 修复未转义的回车符
                                .replace(/\r/g, '\\r');

                            // 尝试解析修复后的JSON
                            JSON.parse(fixedJson);
                            console.log('成功修复JSON数据');

                            // 更新元素的内容
                            if (element.tagName.toLowerCase() === 'script') {
                                element.textContent = fixedJson;
                            } else {
                                if (element.hasAttribute('data-json')) {
                                    element.setAttribute('data-json', fixedJson);
                                }
                                if (element.hasAttribute('data-metadata')) {
                                    element.setAttribute('data-metadata', fixedJson);
                                }
                            }
                        } catch (e2) {
                            console.error('修复JSON数据失败:', e2.message);
                        }
                    }
                }
            } catch (e) {
                console.error('处理元素时出错:', e.message);
            }
        });

        // 特殊处理：扫描所有内联脚本
        var scripts = document.querySelectorAll('script:not([src])');
        scripts.forEach(function(script) {
            try {
                var content = script.textContent || '';

                // 检查是否包含JSON.parse调用
                if (content.includes('JSON.parse(')) {
                    console.log('找到包含JSON.parse调用的脚本');

                    // 检查是否包含character_relationships错误
                    if (content.includes('character_relationships** 时遇到了问题')) {
                        console.log('脚本中包含character_relationships错误，尝试修复');

                        // 修复JSON.parse调用中的字符串
                        var fixedContent = content.replace(
                            /(JSON\.parse\s*\(\s*['"])(.*?character_relationships\*\* 时遇到了问题.*?)(['"])/g,
                            function(match, prefix, jsonStr, suffix) {
                                // 在位置4475附近添加引号
                                if (jsonStr.length > 4400) {
                                    var errorPos = 4475;
                                    var fixedJsonStr = jsonStr.substring(0, errorPos) + '"' + jsonStr.substring(errorPos);
                                    return prefix + fixedJsonStr + suffix;
                                }
                                return match;
                            }
                        );

                        // 如果内容被修改，替换脚本
                        if (fixedContent !== content) {
                            console.log('替换修复后的脚本');
                            var newScript = document.createElement('script');
                            newScript.textContent = fixedContent;
                            script.parentNode.replaceChild(newScript, script);
                        }
                    }
                }
            } catch (e) {
                console.error('处理脚本时出错:', e.message);
            }
        });
    });
})();
