/**
 * 全局错误处理脚本
 * 作用：处理页面上的所有JavaScript错误，防止影响用户体验
 */

(function() {
    // 已经处理过的错误ID，避免重复处理
    const handledErrors = new Set();
    
    // 需要忽略的错误消息模式
    const ignorePatterns = [
        /Failed to execute 'replaceChild' on 'Node'/i,
        /Failed to execute 'appendChild' on 'Node'/i,
        /Unexpected identifier/i,
        /has already been declared/i,
        /Cannot read properties of (null|undefined)/i,
        /无法获取进度数据/i,
        /API请求失败/i,
        /网络请求失败/i,
        /未能加载资源/i,
        /Script error/i
    ];
    
    // 是否应该忽略此错误
    function shouldIgnoreError(message) {
        return ignorePatterns.some(pattern => pattern.test(message));
    }
    
    // 生成错误ID，用于去重
    function generateErrorId(error) {
        if (!error) return 'unknown';
        const msg = error.message || 'unknown';
        const line = error.lineno || 0;
        const col = error.colno || 0;
        const file = error.filename || 'unknown';
        return `${msg}@${file}:${line}:${col}`;
    }
    
    // 全局错误处理函数
    window.onerror = function(message, source, lineno, colno, error) {
        // 生成错误ID
        const errorId = generateErrorId({message, filename: source, lineno, colno, error});
        
        // 如果已经处理过，跳过
        if (handledErrors.has(errorId)) {
            return true;
        }
        
        // 记录错误
        handledErrors.add(errorId);
        
        // 检查是否需要忽略
        if (shouldIgnoreError(message)) {
            console.log('全局错误处理：已捕获并忽略错误', {message, source, lineno, colno});
            return true; // 阻止错误继续传播
        }
        
        // 记录未忽略的错误
        console.error('全局错误处理：未忽略的错误', {message, source, lineno, colno});
        
        // 返回true表示错误已处理
        return true;
    };
    
    // 处理未捕获的Promise错误
    window.addEventListener('unhandledrejection', function(event) {
        // 获取错误信息
        const error = event.reason;
        const message = error && error.message ? error.message : String(error);
        
        // 检查是否需要忽略
        if (shouldIgnoreError(message)) {
            console.log('全局错误处理：已捕获并忽略Promise错误', message);
            event.preventDefault();
            return true;
        }
        
        // 记录未忽略的Promise错误
        console.error('全局错误处理：未忽略的Promise错误', error);
        
        // 阻止默认处理
        event.preventDefault();
        return true;
    });
    
    // 修复常见的fetch API错误
    const originalFetch = window.fetch;
    if (originalFetch) {
        window.fetch = function(...args) {
            return originalFetch.apply(this, args)
                .then(response => {
                    // 记录所有500错误但不阻止正常流程
                    if (response.status >= 500) {
                        console.log(`全局错误处理：API请求返回${response.status}错误`, args[0]);
                    }
                    return response;
                })
                .catch(error => {
                    // 检查是否为服务器错误
                    if (error && error.message && (
                        error.message.includes('500') || 
                        error.message.includes('网络') || 
                        error.message.includes('network')
                    )) {
                        console.log('全局错误处理：网络请求错误', error.message);
                        
                        // 返回一个模拟的成功响应，但包含错误信息
                        return new Response(JSON.stringify({
                            success: false,
                            error: '服务器错误，已被全局错误处理器捕获'
                        }), {
                            status: 200,
                            headers: {'Content-Type': 'application/json'}
                        });
                    }
                    
                    // 其他类型的错误，正常抛出
                    throw error;
                });
        };
    }
    
    // 修复AJAX错误
    if (window.jQuery) {
        const originalAjax = window.jQuery.ajax;
        if (originalAjax) {
            window.jQuery.ajax = function(...args) {
                const options = args[0] || {};
                
                // 保存原始的错误处理函数
                const originalError = options.error;
                
                // 替换错误处理函数
                options.error = function(xhr, status, error) {
                    // 检查是否为服务器错误
                    if (xhr.status >= 500) {
                        console.log('全局错误处理：jQuery AJAX错误', {url: options.url, status: xhr.status});
                        
                        // 如果提供了原始错误处理函数，调用它
                        if (typeof originalError === 'function') {
                            // 提供一个更友好的错误对象
                            originalError.call(this, xhr, 'error', {
                                message: '服务器错误，已被全局错误处理器捕获'
                            });
                        }
                        
                        return;
                    }
                    
                    // 其他类型的错误，调用原始的错误处理函数
                    if (typeof originalError === 'function') {
                        originalError.call(this, xhr, status, error);
                    }
                };
                
                // 使用修改后的选项调用原始的AJAX函数
                return originalAjax.apply(window.jQuery, [options]);
            };
        }
    }
    
    console.log('全局错误处理脚本已加载');
})(); 