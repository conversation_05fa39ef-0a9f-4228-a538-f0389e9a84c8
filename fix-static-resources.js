/**
 * 九猫系统 - 静态资源修复工具
 * 
 * 该脚本用于修复常见的静态资源加载错误，包括：
 * - bootstrap-icons.css
 * - jquery.min.js
 * - bootstrap.bundle.min.js
 * - marked.min.js
 * - favicon.ico
 */

(function() {
    console.log('[九猫修复] 静态资源修复工具已加载');

    // 配置CDN资源URL
    const resourceUrls = {
        'bootstrap-icons.css': [
            '/static/css/bootstrap-icons.css',
            'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css',
            'https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.css',
            'https://fastly.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css'
        ],
        'jquery.min.js': [
            '/static/js/lib/jquery.min.js',
            '/static/js/jquery.min.js',
            'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js',
            'https://cdn.bootcdn.net/ajax/libs/jquery/3.6.4/jquery.min.js',
            'https://fastly.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js'
        ],
        'bootstrap.bundle.min.js': [
            '/static/js/lib/bootstrap.bundle.min.js',
            '/static/js/bootstrap.bundle.min.js',
            'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
            'https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/js/bootstrap.bundle.min.js',
            'https://fastly.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'
        ],
        'marked.min.js': [
            '/static/js/lib/marked.min.js',
            '/static/js/marked.min.js',
            'https://cdn.jsdelivr.net/npm/marked/marked.min.js',
            'https://cdn.bootcdn.net/ajax/libs/marked/4.3.0/marked.min.js',
            'https://fastly.jsdelivr.net/npm/marked/marked.min.js'
        ]
    };

    // 创建favicon
    function createFavicon() {
        // 检查是否已存在favicon
        if (document.querySelector('link[rel="icon"]')) {
            return;
        }

        // 创建一个简单的favicon
        const favicon = document.createElement('link');
        favicon.rel = 'icon';
        favicon.type = 'image/png';
        favicon.href = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABDdJREFUWEfFl21sFEUYx//P7F7be6G0vJRSQEAKKBRBQUCIGIkmYgQTiYmJfCAx8YMfjIkfTDQmJiYmGhM/+AETjSYqiAKCIIgCIi9SQEDAUqC8lZbS3vXudmfMbK+9Xq/Xo5TG/bK5252Z5/+b55nZZ4YwRmvh/v3xZCJ5C0JXE9EcAKUAXAAOgLNg7Jfuzs5PD27demdMt8ky6Ik9e+6JRqPvgGgZGBwQOAiMADAAGAByAIwCZBPYkELqyo0ffvjbWMCMCrBs+/ZZhmG8A6LJABMA9gHoAcABxABMBGBnQIwQaAiMpKbpL+/YtOlkPoi8AE/u3TvBsqzXQXgYjGEQzgHoA2ACmAJgXAaADcYQQIMgfKZr2qvfbtzYlQsiJ8Cze/dOHbKsj0B0OwhnAHQAiAAoATAZgJEBYYNxHYyrIPxo6PrL2z/5pGM0iKwAK/fsmTqcTH4GwkIwWgFcA+AA8ANwZwDYYFwB4wqAPbqu/2fbxx+3ZYPICLBi9+5JyWTyUxAWAGgBcBGABqAUgA+AlgHRBkYzGAcMw3hr60cfXR0JMQpg1a5dE5LJ5EYQ7gbQDKAJgJsBKAOgZkAkwWgC4xcAX+q6/tbmDRuujgAZBfDUrl0FiURiA4iWAjgP4AKAOIAyAMUZAA4YjQBOgLHJ0LW3v9qw4Vp6iBGn4Jldu/zxeHwDES0DcA5AK4A4gHIAxQC0DIgGAMcB/KBp2jtbNmxoTUGkh+CpnTv9sXh8PYgeBHAWQDsAC0AFgCIAegZEPYBjYGzVdf3dLz/9tM0FcQCe3rHDH4vF3gfRMgBnAHQCSAKYAKAQgJEB0QDgKBg/6rr+3uYNG9pdAGfKXbVjR0EsFnsfRA8BOA2gC0AKoAhAAQA9A+IYgCMAtum6/v7mDRs6HQBn0K5Zs6YgGo2+C6JHAJwC0A0gAaAYQCEAPQOiFsAhANt1XX9/07p1nRkAT+/cWRCNRt8D0SMAGgH0AogBKAFQAMDIgKgDcBDADl3XP9i8fn2XA7B69erCaDT6LogeA9AA4AaAKIBSAH4ARgZEPYADAHbquv7h5nXrulMAT+zcWRSNRt8B0WMATgLoAxABUAbAB8DIgGgAsA/ALl3XP9q8fn23A/D0jh1FsVjsbRA9DuAEgH4AIQDlAPwAjAyIRgB7AezWdf3jTevWXXcAnlm9ujgWi70FoicA1AMYABACUAGgAICRAXESwB4Ae3Rd/2TTunU9DsCqVauKY7HYmyB6EkAdgEEAQQCVAAoAGBkQpwDsBrBP1/VPNq5d2+sArF69uiQej78BotUAjgMYAhAEUAWgAICZAVELYBeA/bquf7px7do+B+DZVatK4vH46yB6CsAxAMMAhgFUA/BlQNQB2AngN13XP9u4dm2/A7BmzZrSeDz+GohWATgKIAwgBGAiAG8GxBkAOwAc0HX9841r1w44AM+tXFkaj8dfBdEzAI4AiAAIApgEwJMBcRbAdgAHdV3/YuPatYMOwPMrVpTF4/GXQPQ8gMMAogCCAKoAeDMgzgHYBuCQrusbNqxdO+QA/A8JGPtSvFgvfAAAAABJRU5ErkJggg==';
        document.head.appendChild(favicon);
        console.log('[九猫修复] 已创建内联favicon');
    }

    // 检查并加载资源
    function checkAndLoadResources() {
        // 检查是否已经加载了jQuery
        if (!window.jQuery) {
            loadResource('jquery.min.js');
        }

        // 检查页面上是否有Bootstrap相关元素
        if (document.querySelector('.container') || 
            document.querySelector('.row') || 
            document.querySelector('.btn')) {
            loadResource('bootstrap.bundle.min.js');
        }

        // 检查页面上是否有Bootstrap图标
        if (document.querySelector('.bi') || 
            document.querySelector('[class*="bi-"]')) {
            loadResource('bootstrap-icons.css');
        }

        // 检查页面上是否有Markdown内容
        if (document.querySelector('[data-markdown]') || 
            document.querySelector('.markdown')) {
            loadResource('marked.min.js');
        }

        // 创建favicon
        createFavicon();
    }

    // 加载资源
    function loadResource(resourceName) {
        const urls = resourceUrls[resourceName];
        if (!urls) return;

        let loaded = false;
        let attempts = 0;

        function tryNextUrl() {
            if (attempts >= urls.length || loaded) return;
            
            const url = urls[attempts];
            attempts++;

            if (resourceName.endsWith('.css')) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = url;
                link.onload = function() {
                    console.log(`[九猫修复] CSS资源加载成功: ${url}`);
                    loaded = true;
                };
                link.onerror = function() {
                    console.log(`[九猫修复] CSS资源加载失败: ${url}，尝试下一个URL`);
                    tryNextUrl();
                };
                document.head.appendChild(link);
            } else if (resourceName.endsWith('.js')) {
                const script = document.createElement('script');
                script.src = url;
                script.onload = function() {
                    console.log(`[九猫修复] JS资源加载成功: ${url}`);
                    loaded = true;
                };
                script.onerror = function() {
                    console.log(`[九猫修复] JS资源加载失败: ${url}，尝试下一个URL`);
                    tryNextUrl();
                };
                document.head.appendChild(script);
            }
        }

        tryNextUrl();
    }

    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkAndLoadResources);
    } else {
        checkAndLoadResources();
    }

    // 监听网络错误
    window.addEventListener('error', function(event) {
        const target = event.target;
        // 只处理资源加载错误
        if (target.tagName === 'LINK' || target.tagName === 'SCRIPT') {
            const url = target.src || target.href;
            if (!url) return;

            // 获取资源名称
            const resourceName = url.split('/').pop();
            
            // 检查是否是我们要处理的资源
            for (const key in resourceUrls) {
                if (resourceName.includes(key.replace('.min', ''))) {
                    console.log(`[九猫修复] 检测到资源加载错误: ${url}`);
                    loadResource(key);
                    break;
                }
            }
        }
    }, true);
})(); 