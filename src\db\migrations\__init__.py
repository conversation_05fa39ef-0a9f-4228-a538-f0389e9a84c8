"""
数据库迁移模块
"""
import logging
import importlib
import os
from pathlib import Path

logger = logging.getLogger(__name__)

# 导入迁移脚本
from src.db.migrations.create_presets_table import migrate as create_presets_table
from src.db.migrations.rename_metadata_column import run_migration as rename_metadata_column

def run_migrations():
    """
    运行所有数据库迁移脚本
    """
    logger.info("开始运行数据库迁移...")

    # 获取迁移脚本目录
    migrations_dir = Path(__file__).parent

    # 获取所有迁移脚本
    migration_files = []
    for file in os.listdir(migrations_dir):
        if file.endswith('.py') and file != '__init__.py':
            migration_files.append(file)

    # 按字母顺序排序迁移脚本
    migration_files.sort()

    # 运行所有迁移脚本
    success_count = 0
    for file in migration_files:
        try:
            # 去掉.py后缀
            module_name = file[:-3]
            logger.info(f"导入迁移模块: {module_name}")

            # 导入模块
            module = importlib.import_module(f"src.db.migrations.{module_name}")

            # 运行迁移
            if hasattr(module, 'run_migration'):
                logger.info(f"运行迁移: {module_name}")
                result = module.run_migration()
                if result:
                    logger.info(f"迁移成功: {module_name}")
                    success_count += 1
                else:
                    logger.error(f"迁移失败: {module_name}")
            elif hasattr(module, 'migrate'):
                logger.info(f"运行迁移: {module_name}")
                result = module.migrate()
                if result:
                    logger.info(f"迁移成功: {module_name}")
                    success_count += 1
                else:
                    logger.error(f"迁移失败: {module_name}")
            else:
                logger.warning(f"迁移模块 {module_name} 没有run_migration或migrate函数")
        except Exception as e:
            logger.error(f"运行迁移 {file} 时出错: {str(e)}")

    # 运行直接导入的迁移脚本
    try:
        logger.info("运行预设内容表迁移")
        result = create_presets_table()
        if result:
            logger.info("预设内容表迁移成功")
            success_count += 1
        else:
            logger.error("预设内容表迁移失败")
    except Exception as e:
        logger.error(f"运行预设内容表迁移时出错: {str(e)}")

    try:
        logger.info("运行重命名metadata列迁移")
        result = rename_metadata_column()
        if result:
            logger.info("重命名metadata列迁移成功")
            success_count += 1
        else:
            logger.error("重命名metadata列迁移失败")
    except Exception as e:
        logger.error(f"运行重命名metadata列迁移时出错: {str(e)}")

    logger.info(f"数据库迁移完成，成功: {success_count}/{len(migration_files) + 2}")
    return success_count > 0
