"""
检查九猫系统的配置和环境
"""
import os
import sys
import importlib.util

def check_file_exists(file_path):
    """检查文件是否存在"""
    exists = os.path.isfile(file_path)
    print(f"检查文件 {file_path}: {'存在' if exists else '不存在'}")
    return exists

def check_module_installed(module_name):
    """检查模块是否已安装"""
    spec = importlib.util.find_spec(module_name)
    installed = spec is not None
    print(f"检查模块 {module_name}: {'已安装' if installed else '未安装'}")
    return installed

def main():
    """主函数"""
    print("=== 九猫系统环境检查 ===")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查关键文件
    key_files = [
        "config.py",
        "main.py",
        "src/api/analysis.py",
        "src/api/deepseek_client.py",
        "src/web/app.py",
        "src/models/analysis_result.py",
        "src/models/intermediate_result.py"
    ]
    
    missing_files = []
    for file in key_files:
        if not check_file_exists(file):
            missing_files.append(file)
    
    # 检查关键模块
    key_modules = [
        "flask",
        "requests",
        "sqlalchemy",
        "json",
        "threading",
        "concurrent.futures"
    ]
    
    missing_modules = []
    for module in key_modules:
        if not check_module_installed(module):
            missing_modules.append(module)
    
    # 检查配置文件
    if check_file_exists("config.py"):
        try:
            import config
            print("\n=== 配置信息 ===")
            print(f"DEBUG模式: {config.DEBUG}")
            print(f"API密钥: {config.DEEPSEEK_API_KEY[:6]}...")
            print(f"端口: {config.PORT}")
            print(f"主机: {config.HOST}")
            print(f"数据库URI: {config.DATABASE_URI}")
            print(f"并行分析: {config.PARALLEL_ANALYSIS_ENABLED}")
            print(f"缓存启用: {config.CACHE_ENABLED}")
        except Exception as e:
            print(f"导入配置文件时出错: {str(e)}")
    
    # 总结
    print("\n=== 检查结果 ===")
    if missing_files:
        print(f"缺少 {len(missing_files)} 个关键文件:")
        for file in missing_files:
            print(f"  - {file}")
    else:
        print("所有关键文件都存在")
    
    if missing_modules:
        print(f"缺少 {len(missing_modules)} 个关键模块:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请使用以下命令安装缺少的模块:")
        print(f"pip install {' '.join(missing_modules)}")
    else:
        print("所有关键模块都已安装")
    
    print("\n检查完成")

if __name__ == "__main__":
    main()
    input("按回车键继续...")
