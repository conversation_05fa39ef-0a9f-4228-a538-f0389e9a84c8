"""
数据库迁移脚本：添加reasoning_content列到chapter_analysis_results表
"""
import sqlite3
import logging
import os
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_db_path():
    """获取数据库路径"""
    # 尝试从环境变量获取数据库路径
    db_path = os.environ.get('DB_PATH')
    if db_path:
        return db_path

    # 默认路径
    base_dir = Path(__file__).parent.parent.parent.parent

    # 尝试直接在根目录查找
    root_db_path = os.path.join(base_dir, 'novels.db')
    if os.path.exists(root_db_path):
        return root_db_path

    # 尝试在data目录查找
    data_db_path = os.path.join(base_dir, 'data', 'novels.db')
    if os.path.exists(data_db_path):
        return data_db_path

    # 尝试在instance目录查找
    instance_db_path = os.path.join(base_dir, 'instance', 'novels.db')
    if os.path.exists(instance_db_path):
        return instance_db_path

    # 默认返回根目录的路径
    return root_db_path

def run_migration():
    """运行迁移脚本"""
    db_path = get_db_path()
    logger.info(f"使用数据库路径: {db_path}")

    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return False

    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='chapter_analysis_results'")
        if not cursor.fetchone():
            logger.error("chapter_analysis_results表不存在")
            conn.close()
            return False

        # 检查列是否已存在
        cursor.execute("PRAGMA table_info(chapter_analysis_results)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'reasoning_content' not in columns:
            logger.info("添加reasoning_content列到chapter_analysis_results表")
            cursor.execute("ALTER TABLE chapter_analysis_results ADD COLUMN reasoning_content TEXT")
            conn.commit()
            logger.info("成功添加reasoning_content列")
        else:
            logger.info("reasoning_content列已存在，无需添加")

        # 关闭连接
        conn.close()
        return True
    except Exception as e:
        logger.error(f"迁移过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = run_migration()
    if success:
        logger.info("迁移成功完成")
    else:
        logger.error("迁移失败")
