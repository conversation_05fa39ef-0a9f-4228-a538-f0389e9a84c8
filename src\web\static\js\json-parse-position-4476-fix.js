/**
 * 九猫系统 JSON Parse 位置 4476 修复脚本
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于修复 bootstrap.bundle.min.js 中第6行第20744列处的语法错误
 * 错误信息: 分析器预期在此处找到与"{"标记匹配的"}"
 */

(function() {
    console.log('[JSON Parse 位置 4476 修复] 初始化...');
    
    // 检查是否存在错误
    function checkForError() {
        try {
            // 尝试访问 bootstrap 对象
            if (typeof bootstrap !== 'undefined') {
                return false; // 如果 bootstrap 对象存在，说明没有错误
            }
            
            // 检查是否有语法错误
            const scripts = document.querySelectorAll('script');
            for (let i = 0; i < scripts.length; i++) {
                const src = scripts[i].src || '';
                if (src.includes('bootstrap.bundle.min.js')) {
                    console.log('[JSON Parse 位置 4476 修复] 检测到 bootstrap.bundle.min.js 脚本');
                    return true; // 假设有错误
                }
            }
            
            return false;
        } catch (e) {
            console.error('[JSON Parse 位置 4476 修复] 检查错误时出错:', e);
            return true; // 如果检查过程中出错，假设有错误
        }
    }
    
    // 修复错误
    function fixError() {
        console.log('[JSON Parse 位置 4476 修复] 开始修复错误...');
        
        // 移除所有现有的 bootstrap.bundle.min.js 脚本
        const scripts = document.querySelectorAll('script');
        for (let i = 0; i < scripts.length; i++) {
            const src = scripts[i].src || '';
            if (src.includes('bootstrap.bundle.min.js')) {
                console.log('[JSON Parse 位置 4476 修复] 移除现有的 bootstrap.bundle.min.js 脚本');
                scripts[i].parentNode.removeChild(scripts[i]);
            }
        }
        
        // 加载修复后的 bootstrap.bundle.min.js
        console.log('[JSON Parse 位置 4476 修复] 加载修复后的 bootstrap.bundle.min.js');
        
        // 触发 bootstrap-bundle-fix-loader.js 中的修复函数
        if (typeof window.fixBootstrapBundle === 'function') {
            window.fixBootstrapBundle();
        } else {
            // 如果修复函数不存在，直接加载 CDN 版本
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
            script.integrity = 'sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz';
            script.crossOrigin = 'anonymous';
            document.head.appendChild(script);
        }
    }
    
    // 初始化
    function initialize() {
        // 检查页面是否已加载
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('[JSON Parse 位置 4476 修复] 页面已加载，执行初始检查');
            setTimeout(function() {
                if (checkForError()) {
                    console.log('[JSON Parse 位置 4476 修复] 检测到错误，开始修复');
                    fixError();
                } else {
                    console.log('[JSON Parse 位置 4476 修复] 未检测到错误，无需修复');
                }
            }, 500); // 等待0.5秒，确保页面完全加载
        } else {
            console.log('[JSON Parse 位置 4476 修复] 页面尚未加载，等待 DOMContentLoaded 事件');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('[JSON Parse 位置 4476 修复] DOMContentLoaded 事件触发，执行初始检查');
                setTimeout(function() {
                    if (checkForError()) {
                        console.log('[JSON Parse 位置 4476 修复] 检测到错误，开始修复');
                        fixError();
                    } else {
                        console.log('[JSON Parse 位置 4476 修复] 未检测到错误，无需修复');
                    }
                }, 500); // 等待0.5秒，确保页面完全加载
            });
        }
    }
    
    // 执行初始化
    initialize();
    
    console.log('[JSON Parse 位置 4476 修复] 初始化完成');
})();
