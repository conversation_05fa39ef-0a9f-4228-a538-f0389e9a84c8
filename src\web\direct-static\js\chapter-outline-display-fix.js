
// 九猫系统 - 章纲分析显示完整修复脚本 v2.1
// 用于确保章纲分析的主要内容完整显示，不受字数限制
// 同时移除章节列表中的"大纲分析"维度选项

// 防止脚本重复执行
if (window.chapterOutlineFixApplied !== true) {
    window.chapterOutlineFixApplied = true;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('章纲分析显示完整修复脚本已加载');

        // 1. 移除章节列表中的"大纲分析"维度选项
        function removeOutlineAnalysisDimension() {
            // 移除章节列表中的大纲分析复选框
            const dimensionCheckboxes = document.querySelectorAll('.chapter-dimension-checkbox, .aggregate-dimension-checkbox');
            dimensionCheckboxes.forEach(checkbox => {
                if (checkbox.value === 'outline_analysis') {
                    const parentLabel = checkbox.closest('label');
                    if (parentLabel) {
                        const parentDiv = parentLabel.closest('div');
                        if (parentDiv) {
                            parentDiv.style.display = 'none';
                        }
                    }
                }
            });

            // 移除已分析维度中的大纲分析维度标签
            const dimensionBadges = document.querySelectorAll('.dimension-badge');
            dimensionBadges.forEach(badge => {
                if (badge.getAttribute('data-dimension') === 'outline_analysis') {
                    badge.style.display = 'none';
                }
            });

            // 移除维度卡片中的大纲分析
            const dimensionCards = document.querySelectorAll('.dimension-card');
            dimensionCards.forEach(card => {
                const cardLink = card.getAttribute('onclick');
                if (cardLink && cardLink.includes('outline_analysis')) {
                    card.style.display = 'none';
                }
            });
        }

        // 2. 确保章纲分析内容完整显示
        function enableFullContentDisplay() {
            // 查找所有章纲分析的内容
            const allContentElements = document.querySelectorAll('.markdown-content');

            allContentElements.forEach(element => {
                // 查找可能被截断的内容
                const truncatedElements = element.querySelectorAll('[data-truncated="true"]');

                truncatedElements.forEach(truncated => {
                    // 移除truncated属性，显示完整内容
                    truncated.removeAttribute('data-truncated');
                    truncated.style.maxHeight = 'none';
                    truncated.style.overflow = 'visible';

                    // 如果有"显示更多"按钮，隐藏它
                    const showMoreBtn = truncated.nextElementSibling;
                    if (showMoreBtn && showMoreBtn.classList.contains('show-more-btn')) {
                        showMoreBtn.style.display = 'none';
                    }
                });

                // 查找字数限制提示
                const limitNotices = element.querySelectorAll('.limit-notice');
                limitNotices.forEach(notice => {
                    notice.style.display = 'none';
                });

                // 直接修改内容样式，确保完整显示
                element.style.maxHeight = 'none';
                element.style.overflow = 'visible';
            });
        }

        // 修改渲染函数，确保不再截断内容
        if (window.renderMarkdown && !window.renderMarkdownPatched) {
            window.renderMarkdownPatched = true;
            const originalRenderMarkdown = window.renderMarkdown;
            window.renderMarkdown = function(markdown, container) {
                const result = originalRenderMarkdown(markdown, container);

                // 确保内容完整显示
                setTimeout(() => {
                    enableFullContentDisplay();
                }, 100);

                return result;
            };
        }

        // 定期执行检查，确保完整显示（对动态加载的内容有效）
        function runFixRoutines() {
            removeOutlineAnalysisDimension();
            enableFullContentDisplay();

            // 检查页面URL，决定是否需要特别处理
            if (window.location.href.includes('/chapter/')) {
                // 减少日志输出，只在调试模式下输出
                if (window.debugMode) {
                    console.log('当前在章节详情页，应用修复...');
                }
            } else if (window.location.href.includes('/chapters_summary')) {
                if (window.debugMode) {
                    console.log('当前在章节列表页，应用修复...');
                }
            }
        }

        // 初始执行
        runFixRoutines();

        // 清除可能存在的旧定时器
        if (window.chapterOutlineFixInterval) {
            clearInterval(window.chapterOutlineFixInterval);
        }

        // 定期检查 (每5秒，而不是每2秒)
        window.chapterOutlineFixInterval = setInterval(runFixRoutines, 5000);

        // 清除可能存在的旧观察者
        if (window.chapterOutlineFixObserver) {
            window.chapterOutlineFixObserver.disconnect();
        }

        // 监听页面变化，但使用节流函数减少调用频率
        let throttleTimer;
        const throttledRunFixRoutines = () => {
            if (!throttleTimer) {
                throttleTimer = setTimeout(() => {
                    runFixRoutines();
                    throttleTimer = null;
                }, 1000); // 1秒内最多执行一次
            }
        };

        window.chapterOutlineFixObserver = new MutationObserver(throttledRunFixRoutines);

        // 监听整个文档的变化
        window.chapterOutlineFixObserver.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('章纲分析显示完整修复脚本初始化完成');
    });
} else {
    console.log('章纲分析显示完整修复脚本已经加载，跳过重复执行');
}
