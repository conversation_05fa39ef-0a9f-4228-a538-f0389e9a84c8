"""
九猫系统日志配置模块
提供集中的日志配置，支持日志轮转和内存优化
"""
import os
import logging
import logging.handlers
import sys
import time
from typing import Optional, Dict, Any, List

# 默认日志格式
DEFAULT_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 全局变量，用于跟踪系统启动状态
_startup_mode = True
_startup_time = time.time()
_startup_duration = 30  # 启动模式持续30秒

# 启动阶段需要过滤的日志模式
_startup_filter_patterns = [
    "数据库连接已创建",
    "数据库连接已检出",
    "数据库连接已归还",
    "数据库会话已关闭",
    "请求开始时会话已准备",
    "连接池使用率",
    "加载首页",
    "找到",
    "检查",
    "初始化",
    "尝试加载"
]

class StartupModeFilter(logging.Filter):
    """启动模式日志过滤器，减少启动阶段的日志量"""

    def __init__(self, patterns: List[str]):
        super().__init__()
        self.patterns = patterns

    def filter(self, record):
        # 如果不在启动模式，允许所有日志
        global _startup_mode, _startup_time, _startup_duration
        if not _startup_mode or (time.time() - _startup_time) > _startup_duration:
            if _startup_mode:
                _startup_mode = False
                # 输出一条日志，表示退出启动模式
                if hasattr(record, 'name'):
                    logger = logging.getLogger(record.name)
                    logger.info("系统启动模式结束，恢复正常日志级别")
            return True

        # 在启动模式下，过滤掉匹配模式的日志
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            for pattern in self.patterns:
                if pattern in record.msg:
                    return False

        # 在启动模式下，只允许WARNING及以上级别的日志和少量INFO日志
        if record.levelno < logging.WARNING:
            # 允许10%的INFO日志通过，以保留一些重要信息
            import random
            return random.random() < 0.1

        return True

def configure_logging(
    level: int = logging.INFO,
    log_file: Optional[str] = None,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    console: bool = True,
    format_str: str = DEFAULT_LOG_FORMAT,
    startup_mode: bool = True
) -> None:
    """
    配置日志系统，支持日志轮转和多种输出目标

    Args:
        level: 日志级别，默认INFO
        log_file: 日志文件路径，如果为None则不输出到文件
        max_bytes: 单个日志文件最大字节数，默认10MB
        backup_count: 保留的日志文件数量，默认5个
        console: 是否输出到控制台
        format_str: 日志格式字符串
        startup_mode: 是否启用启动模式（减少启动阶段日志量）
    """
    # 设置全局启动模式状态
    global _startup_mode
    _startup_mode = startup_mode

    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建格式化器
    formatter = logging.Formatter(format_str)

    # 添加控制台处理器
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

    # 添加文件处理器（如果指定了日志文件）
    if log_file:
        # 检查是否使用外部存储
        try:
            from src.utils.external_storage import LOGS_DIR
            # 使用外部存储路径
            external_log_file = os.path.join(LOGS_DIR, os.path.basename(log_file))
            log_file = external_log_file
            root_logger.info(f"日志将保存到外部存储: {log_file}")
        except ImportError:
            # 外部存储模块不可用，使用原始路径
            pass

        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # 设置第三方库的日志级别
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)

    # 在启动模式下添加启动过滤器
    if startup_mode:
        startup_filter = StartupModeFilter(_startup_filter_patterns)
        root_logger.addFilter(startup_filter)
        root_logger.info("系统启动模式已激活，日志量将减少30秒")

def get_memory_friendly_logger(name: str) -> logging.Logger:
    """
    获取内存友好型日志记录器，自动限制日志消息大小

    Args:
        name: 日志记录器名称

    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)

    # 创建一个过滤器，限制日志消息大小
    class MessageSizeFilter(logging.Filter):
        def __init__(self, max_size: int = 1000):
            super().__init__()
            self.max_size = max_size

        def filter(self, record):
            if hasattr(record, 'msg') and isinstance(record.msg, str) and len(record.msg) > self.max_size:
                record.msg = record.msg[:self.max_size] + f"... [截断，完整长度: {len(record.msg)}]"
            return True

    # 添加过滤器
    logger.addFilter(MessageSizeFilter())

    return logger
