@echo off
chcp 65001 > nul
title 九猫小说分析系统 - 静态文件修复工具

echo ================================================
echo        九猫小说分析系统 - 静态文件修复工具
echo ================================================
echo 此工具将修复"Failed to load resource: the server responded with a status of 404 (NOT FOUND)"错误
echo 并确保所有必需的静态文件都正确配置
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python。请确保Python已安装并添加到PATH环境变量中。
    echo.
    echo 按任意键退出...
    pause > nul
    exit /b 1
)

echo [信息] 正在运行静态文件管理工具...
echo.

python static_file_manager.py

if %errorlevel% neq 0 (
    echo.
    echo [警告] 修复过程中出现错误，请查看日志文件了解详情。
) else (
    echo.
    echo [成功] 修复完成！
)

echo.
echo 请重新启动九猫系统以应用更改。
echo.
echo 按任意键退出...
pause > nul
