"""
预设内容模型
用于存储控制台中的预设内容
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from src.models.base import Base

class Preset(Base):
    """预设内容模型"""
    __tablename__ = 'presets'

    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(255), nullable=False, comment='预设标题')
    content = Column(Text, nullable=False, comment='预设内容')
    category = Column(String(50), nullable=False, default='other', comment='预设分类')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    meta_info = Column(JSON, nullable=True, comment='元数据')

    def __repr__(self):
        return f"<Preset(id={self.id}, title='{self.title}', category='{self.category}')>"

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'category': self.category,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'metadata': self.meta_info
        }
