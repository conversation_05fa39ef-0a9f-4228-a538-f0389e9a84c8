{% extends "v3/base.html" %}

{% block title %}帮助中心 - 九猫小说分析写作系统v3.0{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">帮助目录</h3>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush" id="help-tabs" role="tablist">
                    <a class="list-group-item list-group-item-action active" id="intro-tab" data-bs-toggle="list" href="#intro" role="tab" aria-controls="intro">
                        <i class="fas fa-info-circle me-2"></i>系统介绍
                    </a>
                    <a class="list-group-item list-group-item-action" id="upload-tab" data-bs-toggle="list" href="#upload" role="tab" aria-controls="upload">
                        <i class="fas fa-upload me-2"></i>上传小说
                    </a>
                    <a class="list-group-item list-group-item-action" id="analysis-tab" data-bs-toggle="list" href="#analysis" role="tab" aria-controls="analysis">
                        <i class="fas fa-search me-2"></i>小说分析
                    </a>
                    <a class="list-group-item list-group-item-action" id="template-tab" data-bs-toggle="list" href="#template" role="tab" aria-controls="template">
                        <i class="fas fa-bookmark me-2"></i>参考蓝本
                    </a>
                    <a class="list-group-item list-group-item-action" id="console-tab" data-bs-toggle="list" href="#console" role="tab" aria-controls="console">
                        <i class="fas fa-terminal me-2"></i>控制台
                    </a>
                    <a class="list-group-item list-group-item-action" id="showcase-tab" data-bs-toggle="list" href="#showcase" role="tab" aria-controls="showcase">
                        <i class="fas fa-chart-bar me-2"></i>展示台
                    </a>
                    <a class="list-group-item list-group-item-action" id="repository-tab" data-bs-toggle="list" href="#repository" role="tab" aria-controls="repository">
                        <i class="fas fa-archive me-2"></i>内容仓库
                    </a>
                    <a class="list-group-item list-group-item-action" id="faq-tab" data-bs-toggle="list" href="#faq" role="tab" aria-controls="faq">
                        <i class="fas fa-question-circle me-2"></i>常见问题
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="tab-content" id="help-tabContent">
                    <!-- 系统介绍 -->
                    <div class="tab-pane fade show active" id="intro" role="tabpanel" aria-labelledby="intro-tab">
                        <h2 class="mb-4">九猫小说分析写作系统v3.0</h2>
                        <p class="lead">九猫是一款专为作家和编辑设计的智能小说分析写作工具，基于先进的AI技术，提供全方位的文本分析和写作辅助服务。</p>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>版本信息：</strong> 当前版本为v3.0，相比v2.0版本，新增了参考蓝本、控制台、展示台和内容仓库功能。
                        </div>
                        
                        <h4 class="mt-4">系统特点</h4>
                        <ul>
                            <li><strong>全面分析：</strong> 从15个专业维度分析小说，包括语言风格、结构分析、人物关系等。</li>
                            <li><strong>参考蓝本：</strong> 保存已完成全部分析的书籍作为参考，为写作提供灵感和指导。</li>
                            <li><strong>自动写作：</strong> 基于参考蓝本，自动生成符合特定风格和结构的内容。</li>
                            <li><strong>数据可视化：</strong> 直观展示创作统计数据，帮助作家了解自己的创作情况。</li>
                            <li><strong>内容管理：</strong> 集中管理生成的内容，方便查看、编辑和导出。</li>
                        </ul>
                        
                        <h4 class="mt-4">系统架构</h4>
                        <p>九猫系统采用先进的AI技术，基于阿里云DeepSeek R1和通义千问API，提供高质量的文本分析和生成服务。系统架构包括：</p>
                        <ul>
                            <li><strong>前端界面：</strong> 用户友好的Web界面，支持各种操作和可视化展示。</li>
                            <li><strong>后端服务：</strong> 处理用户请求，管理数据库，调用AI接口。</li>
                            <li><strong>AI引擎：</strong> 使用阿里云DeepSeek R1和通义千问API进行文本分析和生成。</li>
                            <li><strong>数据存储：</strong> 安全存储用户上传的小说、分析结果和生成内容。</li>
                        </ul>
                    </div>
                    
                    <!-- 上传小说 -->
                    <div class="tab-pane fade" id="upload" role="tabpanel" aria-labelledby="upload-tab">
                        <h2 class="mb-4">上传小说</h2>
                        <p class="lead">九猫系统支持多种方式上传小说，包括文件上传和文本粘贴。</p>
                        
                        <h4 class="mt-4">上传步骤</h4>
                        <ol>
                            <li>点击导航栏中的"上传小说"按钮。</li>
                            <li>填写小说标题和作者信息（作者信息可选）。</li>
                            <li>选择上传方式：文件上传或文本粘贴。</li>
                            <li>如果选择文件上传，点击"选择文件"按钮，选择TXT格式的小说文件。</li>
                            <li>如果选择文本粘贴，在文本框中粘贴小说内容。</li>
                            <li>选择是否自动分析，如果选择自动分析，还需选择要分析的维度。</li>
                            <li>点击"上传"按钮完成上传。</li>
                        </ol>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>注意：</strong> 上传的小说文件大小不应超过10MB，支持的文件格式为TXT。
                        </div>
                        
                        <h4 class="mt-4">文件格式要求</h4>
                        <p>为了获得最佳分析效果，上传的小说文件应符合以下要求：</p>
                        <ul>
                            <li>文件格式：纯文本（TXT）格式。</li>
                            <li>编码：UTF-8编码，避免乱码问题。</li>
                            <li>章节划分：建议使用明确的章节标记，如"第一章"、"Chapter 1"等。</li>
                            <li>段落划分：段落之间应有空行或缩进。</li>
                        </ul>
                    </div>
                    
                    <!-- 小说分析 -->
                    <div class="tab-pane fade" id="analysis" role="tabpanel" aria-labelledby="analysis-tab">
                        <h2 class="mb-4">小说分析</h2>
                        <p class="lead">九猫系统提供全面的小说分析功能，从15个专业维度分析小说，帮助作家深入了解自己的作品。</p>
                        
                        <h4 class="mt-4">分析维度</h4>
                        <p>系统支持以下15个分析维度：</p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul>
                                    <li><strong>大纲分析：</strong> 分析小说的整体结构和情节发展。</li>
                                    <li><strong>章纲分析：</strong> 分析每个章节的结构和内容。</li>
                                    <li><strong>语言风格：</strong> 分析小说的语言特点和表达方式。</li>
                                    <li><strong>节奏节拍：</strong> 分析小说的叙事节奏和情节发展速度。</li>
                                    <li><strong>人物塑造：</strong> 分析小说中的人物形象和性格特点。</li>
                                    <li><strong>情感表达：</strong> 分析小说中的情感表达和情绪变化。</li>
                                    <li><strong>场景描写：</strong> 分析小说中的场景描写和环境塑造。</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul>
                                    <li><strong>对话分析：</strong> 分析小说中的对话内容和风格。</li>
                                    <li><strong>主题探索：</strong> 分析小说的主题思想和深层含义。</li>
                                    <li><strong>冲突设置：</strong> 分析小说中的冲突设置和矛盾发展。</li>
                                    <li><strong>视角转换：</strong> 分析小说的叙事视角和视角转换。</li>
                                    <li><strong>象征意象：</strong> 分析小说中的象征和意象运用。</li>
                                    <li><strong>文化背景：</strong> 分析小说的文化背景和历史背景。</li>
                                    <li><strong>情节发展：</strong> 分析小说的情节发展和转折点。</li>
                                    <li><strong>结构布局：</strong> 分析小说的结构布局和章节安排。</li>
                                </ul>
                            </div>
                        </div>
                        
                        <h4 class="mt-4">分析过程</h4>
                        <ol>
                            <li>上传小说后，进入小说详情页面。</li>
                            <li>点击"开始分析"按钮，选择要分析的维度。</li>
                            <li>系统会自动分析选定的维度，分析过程可能需要几分钟时间。</li>
                            <li>分析完成后，可以在小说详情页面查看分析结果。</li>
                            <li>点击具体的维度，可以查看详细的分析内容。</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>提示：</strong> 分析过程中，您可以离开页面，系统会在后台继续分析。分析完成后，您可以随时回来查看结果。
                        </div>
                    </div>
                    
                    <!-- 参考蓝本 -->
                    <div class="tab-pane fade" id="template" role="tabpanel" aria-labelledby="template-tab">
                        <h2 class="mb-4">参考蓝本</h2>
                        <p class="lead">参考蓝本是九猫系统v3.0的新功能，可以将已完成全部分析的小说设为参考蓝本，用于自动写作。</p>
                        
                        <h4 class="mt-4">创建参考蓝本</h4>
                        <p>要创建参考蓝本，需要满足以下条件：</p>
                        <ol>
                            <li>小说已完成所有15个维度的分析。</li>
                            <li>小说的所有章节都已完成所有维度的分析。</li>
                        </ol>
                        
                        <p>创建步骤：</p>
                        <ol>
                            <li>进入已完成全部分析的小说详情页面。</li>
                            <li>点击"设为参考蓝本"按钮。</li>
                            <li>系统会检查是否满足条件，如果满足，会将小说设为参考蓝本。</li>
                            <li>设置成功后，可以在"参考蓝本"页面查看所有参考蓝本。</li>
                        </ol>
                        
                        <h4 class="mt-4">使用参考蓝本</h4>
                        <p>参考蓝本可以在控制台中用于自动写作：</p>
                        <ol>
                            <li>进入控制台页面。</li>
                            <li>选择一个参考蓝本。</li>
                            <li>查看参考蓝本的分析结果，了解其风格和特点。</li>
                            <li>切换到"自动写作"标签页。</li>
                            <li>输入写作提示，点击"开始写作"按钮。</li>
                            <li>系统会基于参考蓝本生成符合要求的内容。</li>
                        </ol>
                        
                        <div class="alert alert-success">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>提示：</strong> 参考蓝本越详细，生成的内容质量越高。建议选择内容丰富、风格鲜明的小说作为参考蓝本。
                        </div>
                    </div>
                    
                    <!-- 控制台 -->
                    <div class="tab-pane fade" id="console" role="tabpanel" aria-labelledby="console-tab">
                        <h2 class="mb-4">控制台</h2>
                        <p class="lead">控制台是九猫系统v3.0的核心功能，可以查看参考蓝本的分析结果，进行自动写作。</p>
                        
                        <h4 class="mt-4">控制台功能</h4>
                        <p>控制台主要包括两个功能：</p>
                        <ul>
                            <li><strong>分析结果查看：</strong> 查看参考蓝本的分析结果和推理过程。</li>
                            <li><strong>自动写作：</strong> 基于参考蓝本进行自动写作。</li>
                        </ul>
                        
                        <h4 class="mt-4">使用步骤</h4>
                        <ol>
                            <li>进入控制台页面。</li>
                            <li>选择一个参考蓝本。</li>
                            <li>在左侧选择要查看的分析维度或章节。</li>
                            <li>在右侧查看分析结果或推理过程。</li>
                            <li>切换到"自动写作"标签页。</li>
                            <li>输入写作提示，点击"开始写作"按钮。</li>
                            <li>系统会基于参考蓝本生成符合要求的内容。</li>
                            <li>生成的内容会自动保存到内容仓库。</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>提示：</strong> 写作提示越具体，生成的内容越符合要求。建议在提示中明确指出需要的风格、主题、情节等要素。
                        </div>
                    </div>
                    
                    <!-- 展示台 -->
                    <div class="tab-pane fade" id="showcase" role="tabpanel" aria-labelledby="showcase-tab">
                        <h2 class="mb-4">展示台</h2>
                        <p class="lead">展示台是九猫系统v3.0的新功能，提供系统运行和内容创作的统计数据。</p>
                        
                        <h4 class="mt-4">展示台功能</h4>
                        <p>展示台主要显示以下统计数据：</p>
                        <ul>
                            <li><strong>已创作章节：</strong> 系统生成的章节总数。</li>
                            <li><strong>总字数：</strong> 系统生成的内容总字数。</li>
                            <li><strong>平均字数/章：</strong> 每个章节的平均字数。</li>
                            <li><strong>运行时间：</strong> 系统运行的总时间。</li>
                        </ul>
                        
                        <p>展示台还提供以下图表：</p>
                        <ul>
                            <li><strong>创作趋势：</strong> 显示一段时间内的创作数量变化。</li>
                            <li><strong>内容分布：</strong> 显示不同类型内容的分布情况。</li>
                        </ul>
                        
                        <h4 class="mt-4">使用方法</h4>
                        <p>展示台数据会自动更新，无需手动操作。您可以随时查看最新的统计数据和图表。</p>
                        
                        <div class="alert alert-success">
                            <i class="fas fa-chart-line me-2"></i>
                            <strong>提示：</strong> 展示台数据可以帮助您了解系统的使用情况和创作效率，为优化写作过程提供参考。
                        </div>
                    </div>
                    
                    <!-- 内容仓库 -->
                    <div class="tab-pane fade" id="repository" role="tabpanel" aria-labelledby="repository-tab">
                        <h2 class="mb-4">内容仓库</h2>
                        <p class="lead">内容仓库是九猫系统v3.0的新功能，用于存放自动写作生成的内容。</p>
                        
                        <h4 class="mt-4">内容仓库功能</h4>
                        <p>内容仓库主要提供以下功能：</p>
                        <ul>
                            <li><strong>内容列表：</strong> 显示所有生成的内容，包括标题、字数、创建时间等信息。</li>
                            <li><strong>内容查看：</strong> 查看生成内容的详细内容。</li>
                            <li><strong>内容下载：</strong> 下载生成的内容为TXT文件。</li>
                            <li><strong>内容编辑：</strong> 编辑生成的内容。</li>
                            <li><strong>内容删除：</strong> 删除不需要的内容。</li>
                        </ul>
                        
                        <h4 class="mt-4">使用方法</h4>
                        <ol>
                            <li>进入内容仓库页面。</li>
                            <li>浏览内容列表，查看所有生成的内容。</li>
                            <li>点击"查看"按钮，查看内容详情。</li>
                            <li>点击"下载"按钮，下载内容为TXT文件。</li>
                            <li>点击"编辑"按钮，编辑内容。</li>
                            <li>点击"删除"按钮，删除不需要的内容。</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>提示：</strong> 内容仓库中的内容会永久保存，除非手动删除。建议定期整理内容仓库，删除不需要的内容。
                        </div>
                    </div>
                    
                    <!-- 常见问题 -->
                    <div class="tab-pane fade" id="faq" role="tabpanel" aria-labelledby="faq-tab">
                        <h2 class="mb-4">常见问题</h2>
                        <p class="lead">以下是用户常见的问题和解答。</p>
                        
                        <div class="accordion" id="faqAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq1">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faqAnswer1" aria-expanded="true" aria-controls="faqAnswer1">
                                        系统支持哪些文件格式？
                                    </button>
                                </h2>
                                <div id="faqAnswer1" class="accordion-collapse collapse show" aria-labelledby="faq1" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        目前系统只支持TXT格式的文本文件。建议使用UTF-8编码，避免出现乱码问题。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq2">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqAnswer2" aria-expanded="false" aria-controls="faqAnswer2">
                                        分析过程需要多长时间？
                                    </button>
                                </h2>
                                <div id="faqAnswer2" class="accordion-collapse collapse" aria-labelledby="faq2" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        分析时间取决于小说的长度和选择的分析维度数量。一般来说，一本中等长度的小说（约10万字）分析一个维度需要3-5分钟。如果选择多个维度，时间会相应增加。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq3">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqAnswer3" aria-expanded="false" aria-controls="faqAnswer3">
                                        如何创建参考蓝本？
                                    </button>
                                </h2>
                                <div id="faqAnswer3" class="accordion-collapse collapse" aria-labelledby="faq3" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        要创建参考蓝本，需要先上传小说，然后完成所有15个维度的分析，并确保所有章节都完成了所有维度的分析。完成后，在小说详情页面点击"设为参考蓝本"按钮即可。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq4">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqAnswer4" aria-expanded="false" aria-controls="faqAnswer4">
                                        自动写作的内容质量如何？
                                    </button>
                                </h2>
                                <div id="faqAnswer4" class="accordion-collapse collapse" aria-labelledby="faq4" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        自动写作的内容质量取决于参考蓝本的质量和写作提示的具体程度。一般来说，参考蓝本越详细，写作提示越具体，生成的内容质量越高。建议选择内容丰富、风格鲜明的小说作为参考蓝本，并在写作提示中明确指出需要的风格、主题、情节等要素。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq5">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqAnswer5" aria-expanded="false" aria-controls="faqAnswer5">
                                        系统是否支持多用户？
                                    </button>
                                </h2>
                                <div id="faqAnswer5" class="accordion-collapse collapse" aria-labelledby="faq5" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        目前系统不支持多用户登录和权限管理。所有用户共享同一个系统环境，可以查看和操作所有小说和生成内容。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 激活当前标签页
        const hash = window.location.hash;
        if (hash) {
            $('#help-tabs a[href="' + hash + '"]').tab('show');
        }
        
        // 点击标签页时更新URL
        $('#help-tabs a').on('click', function(e) {
            window.location.hash = $(this).attr('href');
        });
    });
</script>
{% endblock %}
