/**
 * 九猫系统 - Result变量未定义修复脚本
 * 版本: 1.0.0
 * 
 * 该脚本解决result变量未定义的问题，为常见的未定义变量提供默认值
 */

(function() {
    console.log('[九猫修复] Result变量未定义修复脚本已加载');
    
    // 检查是否已应用此修复
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded && window.__nineCatsFixes.loaded['result-not-defined-fix']) {
        console.log('[九猫修复] Result变量未定义修复已经应用，跳过');
        return;
    }
    
    // 创建默认结果对象
    const defaultResult = {
        set: function(key, value) {
            if (!this._data) this._data = {};
            this._data[key] = value;
            console.log('[九猫修复] Result.set被调用:', key, value);
            return this;
        },
        get: function(key) {
            if (!this._data) return null;
            console.log('[九猫修复] Result.get被调用:', key);
            return this._data[key] || null;
        },
        clear: function() {
            this._data = {};
            console.log('[九猫修复] Result.clear被调用');
            return this;
        },
        toJSON: function() {
            return this._data || {};
        },
        _data: {}
    };
    
    // 常见的未定义变量列表
    const commonUndefinedVars = [
        'result', 
        'data', 
        'config', 
        'params', 
        'options', 
        'settings', 
        'response', 
        'chart'
    ];
    
    // 为所有常见未定义变量创建默认值
    commonUndefinedVars.forEach(function(varName) {
        if (typeof window[varName] === 'undefined') {
            if (varName === 'result') {
                window[varName] = defaultResult;
                console.log('[九猫修复] 已创建默认result对象');
            } else if (varName === 'chart' || varName === 'chartData' || varName === 'chartConfig') {
                window[varName] = {
                    update: function() { return this; },
                    render: function() { return this; },
                    destroy: function() { return true; },
                    data: { datasets: [] },
                    options: {}
                };
                console.log('[九猫修复] 已创建默认' + varName + '对象');
            } else {
                window[varName] = {};
                console.log('[九猫修复] 已创建默认' + varName + '对象');
            }
        }
    });
    
    // 保护特定全局对象以防止意外覆盖
    function protectGlobalObject(objName) {
        const original = window[objName];
        if (original) {
            Object.defineProperty(window, objName, {
                get: function() { return original; },
                set: function(value) {
                    console.warn('[九猫修复] 尝试覆盖' + objName + '，已被阻止');
                    return original;
                },
                configurable: false
            });
        }
    }
    
    // 保护重要的全局对象
    ['result', 'jQuery', '$', 'Chart'].forEach(protectGlobalObject);
    
    // 拦截未定义变量的访问
    const originalGet = Object.getOwnPropertyDescriptor(window, 'result') || { get: function() { return defaultResult; } };
    
    // 替换window.result访问器
    try {
        Object.defineProperty(window, 'result', {
            get: function() {
                try {
                    return originalGet.get ? originalGet.get.call(this) : defaultResult;
                } catch (e) {
                    console.error('[九猫修复] 获取result时出错:', e.message);
                    return defaultResult;
                }
            },
            set: function(value) {
                try {
                    // 确保value至少具有基本方法
                    if (value && typeof value === 'object') {
                        if (typeof value.set !== 'function') {
                            value.set = defaultResult.set;
                        }
                        if (typeof value.get !== 'function') {
                            value.get = defaultResult.get;
                        }
                        console.log('[九猫修复] result已被设置为一个新对象，添加了缺失的方法');
                        
                        if (originalGet.set) {
                            return originalGet.set.call(this, value);
                        }
                    } else {
                        console.warn('[九猫修复] 尝试将result设置为非对象值，已被忽略');
                        return defaultResult;
                    }
                } catch (e) {
                    console.error('[九猫修复] 设置result时出错:', e.message);
                    return defaultResult;
                }
            },
            configurable: true
        });
    } catch (e) {
        console.error('[九猫修复] 替换result访问器失败:', e.message);
    }
    
    // 添加全局错误处理程序，捕获所有未定义变量错误
    window.addEventListener('error', function(event) {
        // 检查是否为未定义变量错误
        if (event.message && (
            event.message.includes('is not defined') || 
            event.message.includes('undefined') ||
            event.message.includes('null') ||
            event.message.includes('cannot read property') ||
            event.message.includes('cannot set property')
        )) {
            console.log('[九猫修复] 捕获到可能的未定义变量错误:', event.message);
            
            // 尝试从错误消息中提取变量名
            let varName = null;
            const notDefinedMatch = event.message.match(/'([^']+)' is not defined/);
            const cannotReadMatch = event.message.match(/Cannot read propert(?:y|ies) of (undefined|null)/);
            
            if (notDefinedMatch && notDefinedMatch[1]) {
                varName = notDefinedMatch[1];
                console.log('[九猫修复] 识别到未定义变量:', varName);
                
                // 为此变量创建一个默认值
                if (typeof window[varName] === 'undefined') {
                    window[varName] = varName === 'result' ? defaultResult : {};
                    console.log('[九猫修复] 已为' + varName + '创建默认值');
                }
                
                // 阻止错误传播
                event.preventDefault();
                return false;
            } else if (cannotReadMatch) {
                console.log('[九猫修复] 检测到null/undefined属性访问错误');
                
                // 这种情况比较复杂，通常无法直接修复
                // 但我们可以至少阻止错误显示在控制台中
                event.preventDefault();
                return false;
            }
        }
    }, true);
    
    // 标记修复已加载
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded) {
        window.__nineCatsFixes.loaded['result-not-defined-fix'] = true;
    }
    
    console.log('[九猫修复] Result变量未定义修复脚本加载完成');
})(); 