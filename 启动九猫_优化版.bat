@echo off
echo 九猫小说分析系统 - 优化版

:: 设置临时目录环境变量
set "SCRIPT_DIR=%~dp0"
set "TEMP_DIR=%SCRIPT_DIR%temp"

:: 确保临时目录存在
if not exist "%TEMP_DIR%" (
    echo 创建临时目录: %TEMP_DIR%
    mkdir "%TEMP_DIR%"
)

:: 设置环境变量
set "TEMP=%TEMP_DIR%"
set "TMP=%TEMP_DIR%"

echo 临时文件已重定向到: %TEMP_DIR%
echo.

:: 运行优化脚本
if not exist "optimize_nine_cats.py" (
    echo 优化脚本不存在，跳过优化步骤
) else (
    echo 检查系统优化状态...
    python -c "import os; print('优化模块已安装' if os.path.exists('temp_redirect.py') and os.path.exists('memory_optimizer.py') else '需要安装优化模块')"
    
    set /p RUN_OPTIMIZE="是否运行优化脚本? (Y/N，首次运行请选Y): "
    if /i "%RUN_OPTIMIZE%"=="Y" (
        echo 运行系统优化脚本...
        python optimize_nine_cats.py
    )
)

:: 检查Python是否安装
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python未安装或未添加到PATH，请安装Python
    pause
    exit /b 1
)

:: 检查依赖
python -c "import flask" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 安装依赖...
    pip install -r requirements.txt
)

:: 安装psutil（用于内存监控）
python -c "import psutil" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 安装psutil（用于内存监控）...
    pip install psutil
)

echo.
echo 启动九猫小说分析系统（优化版）...
echo 临时文件将保存在: %TEMP_DIR%
echo 内存优化已启用
echo.

:: 启动服务器
python main.py

if %ERRORLEVEL% NEQ 0 (
    echo 启动失败，请查看日志获取详细信息
    pause
    exit /b 1
) else (
    echo 服务器已关闭
    pause
) 