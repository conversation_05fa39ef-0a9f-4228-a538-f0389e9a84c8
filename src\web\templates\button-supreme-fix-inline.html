<!-- button-supreme-fix-inline.html -->
<!-- 按钮文字最高优先级修复内联脚本 -->
<style>
    /* 按钮文字最高优先级修复样式 */
    .btn, button, input[type="button"], input[type="submit"], input[type="reset"] {
        color: inherit !important;
        opacity: 1 !important;
        visibility: visible !important;
        overflow: visible !important;
        text-indent: 0 !important;
        white-space: normal !important;
        display: inline-block !important;
    }

    /* 按钮颜色最高优先级修复 */
    .btn-primary { color: #fff !important; background-color: #007bff !important; border-color: #007bff !important; }
    .btn-secondary { color: #fff !important; background-color: #6c757d !important; border-color: #6c757d !important; }
    .btn-success { color: #fff !important; background-color: #28a745 !important; border-color: #28a745 !important; }
    .btn-danger { color: #fff !important; background-color: #dc3545 !important; border-color: #dc3545 !important; }
    .btn-warning { color: #212529 !important; background-color: #ffc107 !important; border-color: #ffc107 !important; }
    .btn-info { color: #fff !important; background-color: #17a2b8 !important; border-color: #17a2b8 !important; }
</style>
<script>
    // 按钮文字最高优先级修复脚本
    (function() {
        // 立即执行函数，确保最先运行

        // 定义修复函数
        function fixButtons() {
            const buttons = document.querySelectorAll('.btn, button, input[type="button"], input[type="submit"], input[type="reset"]');

            buttons.forEach(function(button) {
                // 确保按钮文字可见
                button.style.setProperty('color', button.classList.contains('btn-primary') ? '#fff' : 'inherit', 'important');
                button.style.setProperty('opacity', '1', 'important');
                button.style.setProperty('visibility', 'visible', 'important');
                button.style.setProperty('overflow', 'visible', 'important');
                button.style.setProperty('text-indent', '0', 'important');
                button.style.setProperty('white-space', 'normal', 'important');
                button.style.setProperty('display', 'inline-block', 'important');
            });
        }

        // 立即执行一次
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', fixButtons);
        } else {
            fixButtons();
        }

        // 每100毫秒检查一次，确保新添加的按钮也能被修复
        setInterval(fixButtons, 100);

        console.log('按钮文字最高优先级修复已应用');
    })();
</script>
