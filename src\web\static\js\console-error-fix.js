/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 控制台错误修复工具
 * 
 * 这个脚本用于修复控制台错误显示格式问题
 * 版本: 1.1.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('控制台错误修复工具已加载');
    
    // 存储原始控制台方法
    const originalConsole = {
        log: console.log,
        error: console.error,
        warn: console.warn,
        info: console.info,
        debug: console.debug
    };
    
    // 错误计数器
    let errorCounter = 0;
    
    // 已处理的错误集合
    const processedErrors = new Set();
    
    // 检查jQuery是否加载
    function checkJQuery() {
        if (typeof jQuery === 'undefined') {
            console.warn('检测到jQuery未加载，尝试加载jQuery');
            
            // 尝试加载jQuery
            const jqueryScript = document.createElement('script');
            jqueryScript.src = '/static/js/lib/jquery.min.js';
            
            jqueryScript.onload = function() {
                console.log('jQuery加载成功');
            };
            
            jqueryScript.onerror = function() {
                // 尝试备用路径
                console.warn('从默认路径加载jQuery失败，尝试备用路径');
                
                const backupScript = document.createElement('script');
                backupScript.src = '/direct-static/js/lib/jquery.min.js';
                
                backupScript.onload = function() {
                    console.log('从备用路径加载jQuery成功');
                };
                
                backupScript.onerror = function() {
                    // 尝试从CDN加载
                    console.warn('从备用路径加载jQuery失败，尝试CDN');
                    
                    const cdnScript = document.createElement('script');
                    cdnScript.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
                    
                    cdnScript.onload = function() {
                        console.log('从CDN加载jQuery成功');
                    };
                    
                    cdnScript.onerror = function() {
                        console.error('所有jQuery加载尝试均失败');
                    };
                    
                    document.head.appendChild(cdnScript);
                };
                
                document.head.appendChild(backupScript);
            };
            
            document.head.appendChild(jqueryScript);
        }
    }
    
    // 修复控制台错误显示
    function fixConsoleError() {
        // 重写console.error方法
        console.error = function(message, ...args) {
            // 如果是错误对象，使用格式化显示
            if (message instanceof Error) {
                originalConsole.error(`[错误] ${message.message}`);
                if (message.stack) {
                    originalConsole.error(`[堆栈] ${message.stack}`);
                }
                
                // 检查特定类型的错误
                handleSpecificErrors(message);
                
                return;
            }
            
            // 检查字符串中的特定错误模式
            if (typeof message === 'string') {
                if (
                    message.includes('$ is not defined') || 
                    message.includes('jQuery is not defined') ||
                    message.includes('Uncaught ReferenceError: $ is not defined')
                ) {
                    checkJQuery();
                }
                
                if (message.includes('appendChild') && message.includes('null')) {
                    originalConsole.error('[DOM错误] 尝试对null对象执行appendChild操作');
                    
                    // 不阻断正常的错误输出，但添加额外的诊断信息
                    originalConsole.error('[DOM修复] 请检查DOM操作前是否进行了null检查');
                }
                
                if (message.includes('Maximum call stack size exceeded')) {
                    originalConsole.error('[堆栈溢出] 检测到可能的递归调用，请检查互相调用的函数');
                    
                    // 不阻断正常的错误输出，但添加额外的诊断信息
                    originalConsole.error('[堆栈修复] 请确保DOM操作没有形成循环引用或无限递归');
                }
            }
            
            // 普通错误消息，直接传递给原始方法
            originalConsole.error(message, ...args);
        };
    }
    
    // 处理特定类型的错误
    function handleSpecificErrors(error) {
        if (error.message) {
            if (
                error.message.includes('$ is not defined') || 
                error.message.includes('jQuery is not defined')
            ) {
                checkJQuery();
            }
            
            if (error.message.includes('appendChild') && error.message.includes('null')) {
                originalConsole.error('[DOM错误] 尝试对null对象执行appendChild操作');
            }
            
            if (error.message.includes('Maximum call stack size exceeded')) {
                originalConsole.error('[堆栈溢出] 检测到可能的递归调用');
            }

            if (error.message.includes('result is not defined')) {
                originalConsole.error('[引用错误] 变量result未定义');
                originalConsole.error('[修复提示] 请确保result变量在使用前已正确定义，若属于页面模板，请设置默认值');
                
                // 全局定义一个空的result变量防止页面崩溃
                if (typeof window.result === 'undefined') {
                    window.result = { status: 'error', message: '未知错误' };
                    originalConsole.error('[应急修复] 已创建临时result变量防止页面崩溃');
                }
            }
        }
    }
    
    // 修复window.onerror
    function fixWindowOnError() {
        // 保存原始错误处理函数
        const originalOnError = window.onerror;
        
        // 设置新的错误处理函数
        window.onerror = function(message, source, lineno, colno, error) {
            // 生成错误的唯一标识
            const errorId = `${message}:${source}:${lineno}:${colno}`;
            
            // 如果已经处理过这个错误，不再重复处理
            if (processedErrors.has(errorId)) {
                return false;
            }
            
            // 添加到已处理集合
            processedErrors.add(errorId);
            
            // 增加错误计数
            errorCounter++;
            
            // 在控制台中显示格式化的错误信息
            originalConsole.error(`[原始错误 #${errorCounter}] ${message}`);
            originalConsole.error(`位置: ${source} 行: ${lineno} 列: ${colno}`);
            if (error && error.stack) {
                originalConsole.error(`堆栈: ${error.stack}`);
            }
            
            // 处理特定类型的错误
            if (message) {
                if (
                    message.includes('$ is not defined') || 
                    message.includes('jQuery is not defined') ||
                    message.includes('Uncaught ReferenceError: $ is not defined')
                ) {
                    checkJQuery();
                }
                
                if (message.includes('Maximum call stack size exceeded')) {
                    // 尝试重置递归计数器（如果存在的话）
                    if (window.__stackOverflowFixApplied && typeof resetRecursionCounters === 'function') {
                        resetRecursionCounters();
                    }
                }

                if (message.includes('result is not defined')) {
                    originalConsole.error('[引用错误] 变量result未定义');
                    originalConsole.error('[修复提示] 请确保result变量在使用前已正确定义，若属于页面模板，请设置默认值');
                    
                    // 全局定义一个空的result变量防止页面崩溃
                    if (typeof window.result === 'undefined') {
                        window.result = { status: 'error', message: '未知错误' };
                        originalConsole.error('[应急修复] 已创建临时result变量防止页面崩溃');
                    }
                }
            }
            
            // 如果有原始错误处理函数，调用它
            if (typeof originalOnError === 'function') {
                return originalOnError(message, source, lineno, colno, error);
            }
            
            // 不阻止默认错误处理
            return false;
        };
    }
    
    // 主函数
    function main() {
        // 检查jQuery
        if (document.readyState !== 'loading') {
            // 如果页面已加载，立即检查
            checkJQuery();
        } else {
            // 否则，等待页面加载
            document.addEventListener('DOMContentLoaded', checkJQuery);
        }
        
        // 修复控制台错误
        fixConsoleError();
        
        // 修复window.onerror
        fixWindowOnError();
        
        console.log('控制台错误修复已应用');
    }
    
    // 立即执行
    main();
})();
