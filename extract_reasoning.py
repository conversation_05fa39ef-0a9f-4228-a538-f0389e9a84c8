import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

from src.db.connection import Session
from src.models.analysis_result import AnalysisResult
from src.models.analysis_process import AnalysisProcess
from src.models.novel import Novel

def extract_reasoning_content(novel_id, dimension):
    """
    从分析结果中提取推理过程内容。
    
    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            print(f"找不到ID为{novel_id}的小说")
            return
        
        print(f"小说: {novel.title}")
        
        # 获取分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()
        
        if not result:
            print(f"找不到维度为{dimension}的分析结果")
            return
        
        print(f"分析结果ID: {result.id}")
        print(f"创建时间: {result.created_at}")
        print(f"更新时间: {result.updated_at}")
        print(f"内容长度: {len(result.content) if result.content else 0}")
        print(f"元数据长度: {len(result.analysis_metadata) if result.analysis_metadata else 0}")
        
        # 尝试从元数据中提取reasoning_content
        if result.analysis_metadata:
            try:
                metadata = json.loads(result.analysis_metadata) if isinstance(result.analysis_metadata, str) else result.analysis_metadata
                
                print(f"元数据类型: {type(metadata)}")
                
                if isinstance(metadata, dict):
                    print(f"元数据键: {list(metadata.keys())}")
                    
                    # 检查是否有嵌套字典
                    for key, value in metadata.items():
                        if isinstance(value, dict):
                            print(f"嵌套字典 {key} 的键: {list(value.keys())}")
                    
                    # 尝试直接从元数据中提取分析过程内容
                    if 'reasoning_content' in metadata:
                        reasoning_content = metadata['reasoning_content']
                        print(f"在元数据顶层找到reasoning_content，长度: {len(reasoning_content)}")
                        print(f"内容前100个字符: {reasoning_content[:100]}")
                        
                        # 创建分析过程记录
                        create_analysis_process(session, novel_id, dimension, reasoning_content)
                        return
                    
                    # 也可能在result字段内
                    elif 'result' in metadata and isinstance(metadata['result'], dict) and 'reasoning_content' in metadata['result']:
                        reasoning_content = metadata['result']['reasoning_content']
                        print(f"在result字段中找到reasoning_content，长度: {len(reasoning_content)}")
                        print(f"内容前100个字符: {reasoning_content[:100]}")
                        
                        # 创建分析过程记录
                        create_analysis_process(session, novel_id, dimension, reasoning_content)
                        return
                    
                    # 如果没有找到reasoning_content，但API响应包含了COMPLETE_TEXT字段
                    elif 'COMPLETE_TEXT' in metadata:
                        complete_text = metadata['COMPLETE_TEXT']
                        # 从完整文本中提取分析过程部分
                        if isinstance(complete_text, str) and '分析过程' in complete_text:
                            # 查找分析过程部分
                            process_section_start = complete_text.find('分析过程')
                            if process_section_start != -1:
                                # 查找下一个部分或使用文本结尾
                                next_section_markers = ['分析结果', '## 结论', '# 结果']
                                process_section_end = -1
                                for marker in next_section_markers:
                                    pos = complete_text.find(marker, process_section_start)
                                    if pos != -1 and (process_section_end == -1 or pos < process_section_end):
                                        process_section_end = pos
                                
                                if process_section_end == -1:
                                    reasoning_content = complete_text[process_section_start:]
                                else:
                                    reasoning_content = complete_text[process_section_start:process_section_end]
                                
                                print(f"从COMPLETE_TEXT中提取到分析过程内容，长度: {len(reasoning_content)}")
                                print(f"内容前100个字符: {reasoning_content[:100]}")
                                
                                # 创建分析过程记录
                                create_analysis_process(session, novel_id, dimension, reasoning_content)
                                return
                    
                    # 如果在元数据中找不到reasoning_content，检查API响应格式
                    elif 'choices' in metadata:
                        choices = metadata['choices']
                        if isinstance(choices, list) and len(choices) > 0:
                            choice = choices[0]
                            if isinstance(choice, dict) and 'message' in choice:
                                message = choice['message']
                                if isinstance(message, dict) and 'content' in message:
                                    content = message['content']
                                    # 检查内容是否包含分析过程
                                    if '分析过程' in content:
                                        # 提取分析过程部分
                                        reasoning_content = content
                                        print(f"从API choices中提取到分析过程内容，长度: {len(reasoning_content)}")
                                        print(f"内容前100个字符: {reasoning_content[:100]}")
                                        
                                        # 创建分析过程记录
                                        create_analysis_process(session, novel_id, dimension, reasoning_content)
                                        return
                    
                    # 如果还是没找到，但API响应包含reasoning字段
                    elif 'reasoning' in metadata:
                        reasoning = metadata['reasoning']
                        if isinstance(reasoning, str) and len(reasoning) > 50:  # 确保不是空字符串
                            reasoning_content = reasoning
                            print(f"从reasoning字段中提取到分析过程内容，长度: {len(reasoning_content)}")
                            print(f"内容前100个字符: {reasoning_content[:100]}")
                            
                            # 创建分析过程记录
                            create_analysis_process(session, novel_id, dimension, reasoning_content)
                            return
            except Exception as e:
                print(f"解析元数据时出错: {str(e)}")
        
        # 如果从元数据中无法提取推理过程，尝试从内容中提取
        if result.content:
            content = result.content
            # 检查内容是否符合分析过程的特征（而非结果）
            if '首先' in content and '分析' in content and not content.startswith('1.') and not content.startswith('#'):
                reasoning_content = content
                print(f"从content字段中识别到分析过程内容，长度: {len(reasoning_content)}")
                print(f"内容前100个字符: {reasoning_content[:100]}")
                
                # 创建分析过程记录
                create_analysis_process(session, novel_id, dimension, reasoning_content)
                return
            
            # 检查是否包含分析过程的特征
            process_markers = [
                '分析过程',
                '首先，我需要',
                '我将分析',
                '我需要分析',
                '嗯，用户让我',
                '好的，我现在要',
                '下面我来分析'
            ]
            
            for marker in process_markers:
                if marker in content:
                    print(f"在content中找到分析过程标记: {marker}")
                    
                    # 提取从标记到结果部分的内容
                    start_idx = content.find(marker)
                    end_markers = ['分析结果', '## 结论', '# 结果', '# 分析结果', '## 分析结果', '\n\n1. ', '\n\n总结']
                    end_idx = -1
                    
                    for end_marker in end_markers:
                        pos = content.find(end_marker, start_idx)
                        if pos != -1 and (end_idx == -1 or pos < end_idx):
                            end_idx = pos
                    
                    # 提取分析过程部分
                    if end_idx != -1:
                        reasoning_content = content[start_idx:end_idx]
                    else:
                        reasoning_content = content[start_idx:]
                    
                    print(f"从content中提取到分析过程内容，长度: {len(reasoning_content)}")
                    print(f"内容前100个字符: {reasoning_content[:100]}")
                    
                    # 创建分析过程记录
                    create_analysis_process(session, novel_id, dimension, reasoning_content)
                    return
        
        print("无法提取推理过程内容")
    finally:
        session.close()

def create_analysis_process(session, novel_id, dimension, reasoning_content):
    """
    创建分析过程记录。
    
    Args:
        session: 数据库会话
        novel_id: 小说ID
        dimension: 分析维度
        reasoning_content: 推理过程内容
    """
    try:
        # 检查是否已存在分析过程记录
        existing_process = session.query(AnalysisProcess).filter_by(
            novel_id=novel_id, dimension=dimension, processing_stage='init'
        ).first()
        
        if existing_process:
            print(f"已存在分析过程记录，ID: {existing_process.id}")
            return
        
        # 创建分析过程记录
        process = AnalysisProcess(
            novel_id=novel_id,
            dimension=dimension,
            processing_stage='init',
            stage_index=0,
            block_index=0,
            total_blocks=1,
            is_successful=True,
            input_text=reasoning_content,
            output_text=reasoning_content,
            prompt_used="从分析结果中提取的推理过程",
            processing_time=0,
            tokens_used=0
        )
        
        session.add(process)
        session.commit()
        
        print(f"成功创建分析过程记录，ID: {process.id}")
    except Exception as e:
        session.rollback()
        print(f"创建分析过程记录时出错: {str(e)}")

if __name__ == '__main__':
    if len(sys.argv) < 3:
        print("用法: python extract_reasoning.py <novel_id> <dimension>")
        sys.exit(1)
    
    novel_id = int(sys.argv[1])
    dimension = sys.argv[2]
    
    extract_reasoning_content(novel_id, dimension)
