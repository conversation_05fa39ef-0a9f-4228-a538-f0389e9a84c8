{"name": "normalize-package-data", "version": "4.0.0", "author": "GitHub Inc.", "description": "Normalizes data that can be found in package.json files.", "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/npm/normalize-package-data.git"}, "main": "lib/normalize.js", "scripts": {"postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint '**/*.js'", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "npm-template-check", "template-copy": "npm-template-copy --force", "snap": "tap"}, "dependencies": {"hosted-git-info": "^5.0.0", "is-core-module": "^2.8.1", "semver": "^7.3.5", "validate-npm-package-license": "^3.0.4"}, "devDependencies": {"@npmcli/template-oss": "^2.9.2", "tap": "^15.0.9"}, "files": ["bin", "lib"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "templateOSS": {"version": "2.9.2"}, "tap": {"branches": 86, "functions": 92, "lines": 86, "statements": 86}}