<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫 - 静态资源测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0069d9;
        }
    </style>
<script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';console.error('错误处理脚本加载失败，尝试备用路径')"></script>
    </head>
<body>
    <div class="container">
        <h1>九猫静态资源测试页面</h1>
        <p>这个页面用于测试静态资源是否能正确加载。</p>

        <h2>1. 基本HTML和CSS</h2>
        <div class="status success" id="html-css-status">
            ✓ 基本HTML和CSS加载正常
        </div>

        <h2>2. jQuery测试</h2>
        <div class="status" id="jquery-status">
            正在测试jQuery...
        </div>
        <button id="jquery-test-btn">测试jQuery</button>

        <h2>3. Bootstrap测试</h2>
        <div class="status" id="bootstrap-status">
            正在测试Bootstrap...
        </div>
        <button id="bootstrap-test-btn">测试Bootstrap</button>

        <h2>4. 静态文件路径</h2>
        <div class="status" id="path-status">
            正在检查静态文件路径...
        </div>

        <h2>5. API连接测试</h2>
        <div class="status" id="api-status">
            正在测试API连接...
        </div>
        <button id="api-test-btn">测试API连接</button>

        <div style="margin-top: 20px;">
            <a href="/">返回首页</a>
        </div>
    </div>

    <!-- 尝试加载jQuery -->
    <script>
        // 检查静态文件路径
        document.getElementById('path-status').innerHTML = `
            当前页面URL: ${window.location.href}<br>
            静态文件基础路径: ${window.location.origin}/static/
        `;

        // 创建一个函数来加载脚本
        function loadScript(url, callback) {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = url;
            script.onload = function() {
                callback(null, url);
            };
            script.onerror = function() {
                callback(new Error('无法加载脚本: ' + url), url);
            };
            document.head.appendChild(script);
        }

        // 尝试使用多种路径加载jQuery
        function tryLoadJQuery() {
            // 尝试的路径列表
            var paths = [
                '/static/js/lib/jquery.min.js',
                '/direct-static/js/lib/jquery.min.js',
                'https://code.jquery.com/jquery-3.6.0.min.js'
            ];

            var pathIndex = 0;

            function tryNextPath() {
                if (pathIndex >= paths.length) {
                    var jqueryStatus = document.getElementById('jquery-status');
                    jqueryStatus.className = 'status error';
                    jqueryStatus.innerHTML = '✗ jQuery加载失败: 所有路径都尝试失败';
                    return;
                }

                var path = paths[pathIndex++];
                console.log('尝试加载jQuery: ' + path);

                loadScript(path, function(error, url) {
                    if (error || typeof jQuery === 'undefined') {
                        console.log('路径 ' + url + ' 加载失败，尝试下一个路径');
                        tryNextPath();
                    } else {
                        console.log('jQuery成功加载: ' + url);
                        onJQueryLoaded();
                    }
                });
            }

            tryNextPath();
        }

        // jQuery加载成功后的回调
        function onJQueryLoaded() {
            var jqueryStatus = document.getElementById('jquery-status');

            // 检查jQuery是否正确加载
            if (typeof jQuery !== 'undefined') {
                jqueryStatus.className = 'status success';
                jqueryStatus.innerHTML = '✓ jQuery加载成功 (版本: ' + jQuery.fn.jquery + ')';

                // 添加jQuery测试按钮功能
                $('#jquery-test-btn').click(function() {
                    $(this).text('jQuery工作正常!');
                    setTimeout(function() {
                        $('#jquery-test-btn').text('测试jQuery');
                    }, 2000);
                });

                // 尝试加载Bootstrap
                tryLoadBootstrap();
            } else {
                jqueryStatus.className = 'status error';
                jqueryStatus.innerHTML = '✗ jQuery加载失败: 全局变量不可用';
            }
        }

        // 尝试使用多种路径加载Bootstrap
        function tryLoadBootstrap() {
            // 尝试的路径列表
            var paths = [
                '/static/js/lib/bootstrap.bundle.min.js',
                '/direct-static/js/lib/bootstrap.bundle.min.js',
                'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'
            ];

            var pathIndex = 0;

            function tryNextPath() {
                if (pathIndex >= paths.length) {
                    var bootstrapStatus = document.getElementById('bootstrap-status');
                    bootstrapStatus.className = 'status error';
                    bootstrapStatus.innerHTML = '✗ Bootstrap加载失败: 所有路径都尝试失败';
                    return;
                }

                var path = paths[pathIndex++];
                console.log('尝试加载Bootstrap: ' + path);

                loadScript(path, function(error, url) {
                    if (error || typeof bootstrap === 'undefined') {
                        console.log('路径 ' + url + ' 加载失败，尝试下一个路径');
                        tryNextPath();
                    } else {
                        console.log('Bootstrap成功加载: ' + url);
                        onBootstrapLoaded();
                    }
                });
            }

            tryNextPath();
        }

        // Bootstrap加载成功后的回调
        function onBootstrapLoaded() {
            var bootstrapStatus = document.getElementById('bootstrap-status');

            // 检查Bootstrap是否正确加载
            if (typeof bootstrap !== 'undefined') {
                bootstrapStatus.className = 'status success';
                bootstrapStatus.innerHTML = '✓ Bootstrap加载成功';

                // 添加Bootstrap测试按钮功能
                $('#bootstrap-test-btn').click(function() {
                    if (typeof bootstrap.Toast !== 'undefined') {
                        $(this).text('Bootstrap工作正常!');
                        setTimeout(function() {
                            $('#bootstrap-test-btn').text('测试Bootstrap');
                        }, 2000);
                    } else {
                        bootstrapStatus.className = 'status warning';
                        bootstrapStatus.innerHTML = '⚠ Bootstrap已加载但部分功能可能不可用';
                    }
                });

                // 测试API连接
                setupApiTest();
            } else {
                bootstrapStatus.className = 'status error';
                bootstrapStatus.innerHTML = '✗ Bootstrap加载失败: 全局变量不可用';
            }
        }

        // 设置API测试
        function setupApiTest() {
            $('#api-test-btn').click(function() {
                var apiStatus = document.getElementById('api-status');
                apiStatus.className = 'status warning';
                apiStatus.innerHTML = '正在测试API连接...';

                $.ajax({
                    url: '/api/system/status',
                    method: 'GET',
                    success: function(data) {
                        apiStatus.className = 'status success';
                        apiStatus.innerHTML = '✓ API连接成功: ' + JSON.stringify(data);
                    },
                    error: function(xhr, status, error) {
                        apiStatus.className = 'status error';
                        apiStatus.innerHTML = '✗ API连接失败: ' + error;
                    }
                });
            });
        }

        // 开始加载过程
        tryLoadJQuery();
    </script>
</body>
</html>
