"""
九猫小说分析系统 - 分析过程记录修复工具
修复分析过程记录功能，确保分析过程被正确记录到数据库中
"""
import os
import sys
import logging
import sqlite3
from datetime import datetime

# 配置日志
log_filename = f"fix_analysis_process_recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def backup_database():
    """备份数据库"""
    try:
        backup_filename = f"novels_backup_process_recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        import shutil
        shutil.copy2("novels.db", backup_filename)
        logger.info(f"数据库已备份为 {backup_filename}")
        return True
    except Exception as e:
        logger.error(f"备份数据库失败: {str(e)}")
        return False

def check_table_exists(conn, table_name):
    """检查表是否存在"""
    cursor = conn.cursor()
    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
    return cursor.fetchone() is not None

def check_analysis_process_table(conn):
    """检查分析过程表是否存在并且结构正确"""
    if not check_table_exists(conn, 'analysis_processes'):
        logger.error("分析过程表不存在，请先运行 fix_db_for_analysis_process.py")
        return False

    # 检查表结构
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(analysis_processes)")
    columns = [row[1] for row in cursor.fetchall()]

    required_columns = [
        'id', 'novel_id', 'result_id', 'dimension', 'block_index', 'total_blocks',
        'processing_stage', 'stage_index', 'input_text', 'output_text', 'prompt_used',
        'api_request', 'api_response', 'processing_time', 'tokens_used', 'is_successful',
        'error_message', 'process_metadata', 'created_at'
    ]

    missing_columns = [col for col in required_columns if col not in columns]
    if missing_columns:
        logger.error(f"分析过程表缺少以下列: {missing_columns}")
        return False

    logger.info("分析过程表结构正确")
    return True

def check_analysis_results_table(conn):
    """检查分析结果表是否包含必要的列"""
    if not check_table_exists(conn, 'analysis_results'):
        logger.error("分析结果表不存在")
        return False

    # 检查表结构
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(analysis_results)")
    columns = [row[1] for row in cursor.fetchall()]

    required_columns = [
        'id', 'novel_id', 'dimension', 'content', 'analysis_metadata',
        'analysis_logs', 'created_at', 'updated_at'
    ]

    missing_columns = [col for col in required_columns if col not in columns]
    if missing_columns:
        logger.error(f"分析结果表缺少以下列: {missing_columns}")
        return False

    logger.info("分析结果表结构正确")
    return True

def check_environment_variables():
    """检查环境变量是否正确设置"""
    env_vars = {
        'ENABLE_DETAILED_PROCESS_RECORDING': os.environ.get('ENABLE_DETAILED_PROCESS_RECORDING', 'False'),
        'SAVE_FULL_API_INTERACTIONS': os.environ.get('SAVE_FULL_API_INTERACTIONS', 'False'),
        'SAVE_PROMPTS': os.environ.get('SAVE_PROMPTS', 'False'),
        'ENHANCED_LOG_CATEGORIES': os.environ.get('ENHANCED_LOG_CATEGORIES', 'False'),
        'RECORD_INTERMEDIATE_RESULTS': os.environ.get('RECORD_INTERMEDIATE_RESULTS', 'False')
    }

    logger.info("当前环境变量设置:")
    for var, value in env_vars.items():
        logger.info(f"  {var} = {value}")

    # 检查是否有任何变量未设置为True
    missing_vars = [var for var, value in env_vars.items() if value.lower() != 'true']
    if missing_vars:
        logger.warning(f"以下环境变量未设置为True: {missing_vars}")
        logger.warning("这可能导致分析过程记录功能不完整")
        return False

    logger.info("所有必要的环境变量已正确设置")
    return True

def create_test_process_record(conn):
    """创建一个测试分析过程记录，验证功能是否正常"""
    try:
        cursor = conn.cursor()

        # 获取一个有效的小说ID
        cursor.execute("SELECT id FROM novels LIMIT 1")
        novel_id_row = cursor.fetchone()
        if not novel_id_row:
            logger.error("数据库中没有小说记录，无法创建测试记录")
            return False

        novel_id = novel_id_row[0]

        # 获取该小说的一个分析结果ID
        cursor.execute("SELECT id FROM analysis_results WHERE novel_id = ? LIMIT 1", (novel_id,))
        result_id_row = cursor.fetchone()
        result_id = result_id_row[0] if result_id_row else None

        # 创建测试记录
        cursor.execute("""
        INSERT INTO analysis_processes (
            novel_id, result_id, dimension, block_index, total_blocks,
            processing_stage, stage_index, input_text, output_text,
            prompt_used, processing_time, tokens_used, is_successful,
            process_metadata, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        """, (
            novel_id, result_id, 'test_dimension', 0, 1,
            'test', 0, '测试输入文本', '测试输出文本',
            '测试提示词', 100, 50, 1,
            '{"test": true}'
        ))

        conn.commit()
        logger.info(f"已创建测试分析过程记录，小说ID: {novel_id}")

        # 验证记录是否创建成功
        cursor.execute("SELECT id FROM analysis_processes WHERE novel_id = ? AND processing_stage = 'test'", (novel_id,))
        test_record = cursor.fetchone()
        if test_record:
            logger.info(f"测试记录创建成功，ID: {test_record[0]}")

            # 删除测试记录
            cursor.execute("DELETE FROM analysis_processes WHERE id = ?", (test_record[0],))
            conn.commit()
            logger.info("测试记录已删除")
            return True
        else:
            logger.error("测试记录创建失败")
            return False

    except Exception as e:
        logger.error(f"创建测试记录时出错: {str(e)}")
        conn.rollback()
        return False

def create_process_recording_patch():
    """创建一个补丁文件，修复分析过程记录功能"""
    patch_filename = "process_recording_patch.py"

    patch_content = """
# 九猫小说分析系统 - 分析过程记录补丁
# 将此文件放在src/services目录下，并在analysis_service.py中导入

import logging
import json
import time
from datetime import datetime
from src.db.connection import Session
from src.models.analysis_process import AnalysisProcess

logger = logging.getLogger(__name__)

class ProcessRecorder:
    """分析过程记录器,用于记录分析过程的详细信息"""

    @staticmethod
    def record_process(novel_id, dimension, block_index, total_blocks, stage,
                      input_text=None, output_text=None, prompt=None,
                      api_request=None, api_response=None,
                      processing_time=None, tokens=None,
                      is_successful=True, error_message=None,
                      result_id=None, metadata=None):
        """
        记录分析过程

        Args:
            novel_id: 小说ID
            dimension: 分析维度
            block_index: 分块索引
            total_blocks: 总分块数
            stage: 处理阶段
            input_text: 输入文本
            output_text: 输出文本
            prompt: 提示词
            api_request: API请求
            api_response: API响应
            processing_time: 处理时间（毫秒）
            tokens: 使用的令牌数
            is_successful: 是否成功
            error_message: 错误信息
            result_id: 分析结果ID
            metadata: 其他元数据
        """
        # 检查是否启用了详细过程记录
        if not ProcessRecorder.is_process_recording_enabled():
            logger.debug(f"详细过程记录功能未启用，跳过记录")
            return None

        try:
            session = Session()

            # 创建分析过程记录
            process = AnalysisProcess(
                novel_id=novel_id,
                dimension=dimension,
                block_index=block_index,
                total_blocks=total_blocks,
                processing_stage=stage,
                result_id=result_id,
                input_text=input_text,
                output_text=output_text,
                prompt_used=prompt,
                api_request=api_request,
                api_response=api_response,
                processing_time=processing_time,
                tokens_used=tokens,
                is_successful=is_successful,
                error_message=error_message,
                metadata=metadata or {}
            )

            session.add(process)
            session.commit()

            process_id = process.id
            logger.debug(f"已记录分析过程: ID={process_id}, 小说ID={novel_id}, 维度={dimension}, 阶段={stage}")

            session.close()
            return process_id

        except Exception as e:
            logger.error(f"记录分析过程时出错: {str(e)}")
            if session:
                session.rollback()
                session.close()
            return None

    @staticmethod
    def is_process_recording_enabled():
        """检查是否启用了详细过程记录"""
        import os
        return os.environ.get('ENABLE_DETAILED_PROCESS_RECORDING', 'False').lower() == 'true'

    @staticmethod
    def record_init(novel_id, dimension, input_text=None, metadata=None):
        """记录初始化阶段"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            stage='init',
            input_text=input_text,
            metadata=metadata
        )

    @staticmethod
    def record_chunk_analysis(novel_id, dimension, block_index, total_blocks,
                             input_text, output_text=None, prompt=None,
                             api_request=None, api_response=None,
                             processing_time=None, tokens=None,
                             is_successful=True, error_message=None):
        """记录分块分析阶段"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=block_index,
            total_blocks=total_blocks,
            stage='chunk_analysis',
            input_text=input_text,
            output_text=output_text,
            prompt=prompt,
            api_request=api_request,
            api_response=api_response,
            processing_time=processing_time,
            tokens=tokens,
            is_successful=is_successful,
            error_message=error_message
        )

    @staticmethod
    def record_combine(novel_id, dimension, input_chunks, output_text=None,
                      processing_time=None, is_successful=True, error_message=None):
        """记录结果合并阶段"""
        # 将输入块转换为字符串
        if isinstance(input_chunks, list):
            input_text = json.dumps(input_chunks, ensure_ascii=False)
        else:
            input_text = str(input_chunks)

        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            stage='combine',
            input_text=input_text,
            output_text=output_text,
            processing_time=processing_time,
            is_successful=is_successful,
            error_message=error_message
        )

    @staticmethod
    def record_finalize(novel_id, dimension, result_id, input_text=None,
                       output_text=None, processing_time=None,
                       is_successful=True, error_message=None):
        """记录最终处理阶段"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            stage='finalize',
            result_id=result_id,
            input_text=input_text,
            output_text=output_text,
            processing_time=processing_time,
            is_successful=is_successful,
            error_message=error_message
        )

    @staticmethod
    def record_error(novel_id, dimension, stage, error_message,
                    block_index=0, total_blocks=1, input_text=None):
        """记录错误"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=block_index,
            total_blocks=total_blocks,
            stage=stage,
            input_text=input_text,
            is_successful=False,
            error_message=error_message
        )

# 导出ProcessRecorder类
__all__ = ['ProcessRecorder']
"""

    with open(patch_filename, 'w', encoding='utf-8') as f:
        f.write(patch_content)

    logger.info(f"已创建分析过程记录补丁文件: {patch_filename}")
    logger.info(f"请将此文件复制到 src/services 目录下，并在 analysis_service.py 中导入使用")

    return patch_filename

def create_integration_instructions():
    """创建集成说明文件"""
    instructions_filename = "process_recording_integration.md"

    instructions_content = """# 九猫小说分析系统 - 分析过程记录功能集成说明

## 问题描述

当前系统在分析小说时，没有正确记录分析过程的详细信息，导致分析详情页面的"分析过程"部分显示为空白。

## 解决方案

1. 确保数据库表结构正确
2. 添加分析过程记录功能
3. 确保环境变量正确设置

## 集成步骤

### 1. 复制补丁文件

将 `process_recording_patch.py` 文件复制到 `src/services` 目录下。

### 2. 修改 analysis_service.py

在 `src/services/analysis_service.py` 文件中添加以下导入语句：

```python
from src.services.process_recording_patch import ProcessRecorder
```

### 3. 在 run_analysis 函数中添加分析过程记录

在 `run_analysis` 函数中，找到以下位置并添加相应的记录代码：

#### 初始化阶段

在函数开始处，任务状态更新为 'in_progress' 后添加：

```python
# 记录初始化阶段
ProcessRecorder.record_init(novel_id, dimension, input_text=None, metadata={
    'start_time': time.time(),
    'task_id': task_id
})
```

#### 分块分析阶段

在分析文本阶段添加：

```python
# 记录分块分析阶段
ProcessRecorder.record_chunk_analysis(
    novel_id=novel_id,
    dimension=dimension,
    block_index=0,  # 根据实际分块索引修改
    total_blocks=1,  # 根据实际总分块数修改
    input_text="分析文本内容",  # 替换为实际输入文本
    output_text="分析结果",  # 替换为实际输出文本
    prompt="使用的提示词",  # 替换为实际提示词
    processing_time=processing_time_ms,  # 处理时间（毫秒）
    tokens=tokens_used  # 使用的令牌数
)
```

#### 结果合并阶段

在生成分析结果前添加：

```python
# 记录结果合并阶段
ProcessRecorder.record_combine(
    novel_id=novel_id,
    dimension=dimension,
    input_chunks=chunk_results,  # 分块结果列表
    output_text="合并后的结果",  # 替换为实际合并结果
    processing_time=combine_time_ms  # 合并处理时间（毫秒）
)
```

#### 最终处理阶段

在保存分析结果后添加：

```python
# 记录最终处理阶段
ProcessRecorder.record_finalize(
    novel_id=novel_id,
    dimension=dimension,
    result_id=result.id,  # 分析结果ID
    output_text=result.content,  # 最终分析内容
    processing_time=total_time_ms  # 总处理时间（毫秒）
)
```

#### 错误处理

在错误处理部分添加：

```python
# 记录错误
ProcessRecorder.record_error(
    novel_id=novel_id,
    dimension=dimension,
    stage='error_handling',
    error_message=str(e)
)
```

### 4. 确保环境变量正确设置

在启动脚本中，确保以下环境变量设置为 `True`：

- `ENABLE_DETAILED_PROCESS_RECORDING`
- `SAVE_FULL_API_INTERACTIONS`
- `SAVE_PROMPTS`
- `ENHANCED_LOG_CATEGORIES`
- `RECORD_INTERMEDIATE_RESULTS`

### 5. 重新启动系统

使用 `启动九猫_分析过程完整记录版.vbs` 启动系统，并重新进行分析。

## 验证

1. 进行一次新的分析
2. 查看分析详情页面的"查看分析过程"链接
3. 确认分析过程页面显示了详细的分析步骤和中间结果

## 故障排除

如果分析过程仍然为空，请检查：

1. 数据库中 `analysis_processes` 表是否有记录
2. 环境变量是否正确设置
3. 日志中是否有相关错误信息
"""

    with open(instructions_filename, 'w', encoding='utf-8') as f:
        f.write(instructions_content)

    logger.info(f"已创建集成说明文件: {instructions_filename}")

    return instructions_filename

def main():
    """主函数"""
    logger.info("========== 开始修复分析过程记录功能 ==========")

    # 备份数据库
    if not backup_database():
        logger.error("备份数据库失败，终止操作")
        return

    try:
        # 连接数据库
        conn = sqlite3.connect("novels.db")

        # 检查表结构
        if not check_analysis_process_table(conn):
            logger.error("分析过程表结构不正确，请先运行 fix_db_for_analysis_process.py")
            conn.close()
            return

        if not check_analysis_results_table(conn):
            logger.warning("分析结果表结构不完整，可能影响功能")

        # 检查环境变量
        check_environment_variables()

        # 创建测试记录
        if create_test_process_record(conn):
            logger.info("数据库表结构测试成功，可以正常记录分析过程")
        else:
            logger.warning("数据库表结构测试失败，可能无法正常记录分析过程")

        # 关闭连接
        conn.close()

        # 创建补丁文件
        patch_file = create_process_recording_patch()

        # 创建集成说明
        instructions_file = create_integration_instructions()

        print("\n✓ 修复工具执行完成！")
        print(f"✓ 已创建补丁文件: {patch_file}")
        print(f"✓ 已创建集成说明: {instructions_file}")
        print("✓ 请按照集成说明中的步骤进行修复")

    except Exception as e:
        logger.error(f"修复过程中发生错误: {str(e)}")
        print("\n✗ 修复工具执行失败")
        print(f"✗ 请查看日志: {log_filename}")

    logger.info("========== 修复工具执行结束 ==========")

if __name__ == "__main__":
    main()
