/**
 * 九猫 - 导航调试工具 (直接静态版本)
 * 用于调试和追踪导航问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._navigationDebugLoaded) {
        console.log('[导航调试] 脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._navigationDebugLoaded = true;
    
    console.log('[导航调试] 导航调试工具已加载 - 版本1.0.0 (直接静态版本)');
    
    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalWindowLocation = window.location;
    
    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[导航调试] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    function safeError(message) {
        try {
            originalConsoleError.call(console, '[导航调试] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 获取当前页面信息
    function getPageInfo() {
        const path = window.location.pathname;
        const novelId = path.match(/\/novel\/(\d+)/) ? path.match(/\/novel\/(\d+)/)[1] : null;
        const pageType = getPageType(path);
        
        return {
            path: path,
            novelId: novelId,
            pageType: pageType
        };
    }
    
    // 获取页面类型
    function getPageType(path) {
        if (/^\/novel\/\d+\/?$/.test(path)) {
            return 'novel_detail';
        } else if (/^\/novel\/\d+\/chapters\/?$/.test(path)) {
            return 'chapter_list';
        } else if (/^\/novel\/\d+\/chapter\/\d+\/?$/.test(path)) {
            return 'chapter_detail';
        } else if (/^\/novel\/\d+\/analysis\/\w+\/?$/.test(path)) {
            return 'dimension_detail';
        } else if (path === '/' || path === '/index' || path === '/home') {
            return 'home';
        } else {
            return 'other';
        }
    }
    
    // 创建调试面板
    function createDebugPanel() {
        // 检查是否已经创建了调试面板
        if (document.getElementById('nav-debug-panel')) {
            return;
        }
        
        // 创建调试面板
        const panel = document.createElement('div');
        panel.id = 'nav-debug-panel';
        panel.style.position = 'fixed';
        panel.style.bottom = '10px';
        panel.style.right = '10px';
        panel.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        panel.style.color = 'white';
        panel.style.padding = '10px';
        panel.style.borderRadius = '5px';
        panel.style.zIndex = '9999';
        panel.style.fontSize = '12px';
        panel.style.maxWidth = '300px';
        panel.style.maxHeight = '300px';
        panel.style.overflow = 'auto';
        panel.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
        
        // 添加标题
        const title = document.createElement('div');
        title.style.fontWeight = 'bold';
        title.style.marginBottom = '5px';
        title.style.borderBottom = '1px solid #666';
        title.style.paddingBottom = '5px';
        title.textContent = '导航调试面板 (直接静态版本)';
        panel.appendChild(title);
        
        // 添加内容容器
        const content = document.createElement('div');
        content.id = 'nav-debug-content';
        panel.appendChild(content);
        
        // 添加按钮容器
        const buttons = document.createElement('div');
        buttons.style.marginTop = '10px';
        buttons.style.display = 'flex';
        buttons.style.justifyContent = 'space-between';
        panel.appendChild(buttons);
        
        // 添加刷新按钮
        const refreshButton = document.createElement('button');
        refreshButton.textContent = '刷新';
        refreshButton.style.padding = '3px 8px';
        refreshButton.style.backgroundColor = '#007bff';
        refreshButton.style.color = 'white';
        refreshButton.style.border = 'none';
        refreshButton.style.borderRadius = '3px';
        refreshButton.style.cursor = 'pointer';
        refreshButton.onclick = updateDebugPanel;
        buttons.appendChild(refreshButton);
        
        // 添加清除按钮
        const clearButton = document.createElement('button');
        clearButton.textContent = '清除日志';
        clearButton.style.padding = '3px 8px';
        clearButton.style.backgroundColor = '#dc3545';
        clearButton.style.color = 'white';
        clearButton.style.border = 'none';
        clearButton.style.borderRadius = '3px';
        clearButton.style.cursor = 'pointer';
        clearButton.onclick = clearDebugLog;
        buttons.appendChild(clearButton);
        
        // 添加隐藏按钮
        const hideButton = document.createElement('button');
        hideButton.textContent = '隐藏';
        hideButton.style.padding = '3px 8px';
        hideButton.style.backgroundColor = '#6c757d';
        hideButton.style.color = 'white';
        hideButton.style.border = 'none';
        hideButton.style.borderRadius = '3px';
        hideButton.style.cursor = 'pointer';
        hideButton.onclick = function() {
            panel.style.display = 'none';
        };
        buttons.appendChild(hideButton);
        
        // 添加到页面
        document.body.appendChild(panel);
        
        // 更新调试面板
        updateDebugPanel();
    }
    
    // 更新调试面板
    function updateDebugPanel() {
        const content = document.getElementById('nav-debug-content');
        if (!content) return;
        
        // 获取页面信息
        const pageInfo = getPageInfo();
        
        // 获取导航修复状态
        const unifiedFixLoaded = window._unifiedNavigationFixLoaded || false;
        const globalFixLoaded = window._globalNavigationFixLoaded || false;
        const novelFixLoaded = window._novelFunctionButtonFixLoaded || false;
        
        // 更新内容
        content.innerHTML = `
            <div style="margin-bottom: 5px;"><strong>页面信息</strong></div>
            <div>路径: ${pageInfo.path}</div>
            <div>小说ID: ${pageInfo.novelId || 'N/A'}</div>
            <div>页面类型: ${pageInfo.pageType}</div>
            <div style="margin-top: 10px; margin-bottom: 5px;"><strong>修复脚本状态</strong></div>
            <div>统一导航修复: ${unifiedFixLoaded ? '已加载' : '未加载'}</div>
            <div>全局导航修复: ${globalFixLoaded ? '已加载' : '未加载'}</div>
            <div>小说功能按钮修复: ${novelFixLoaded ? '已加载' : '未加载'}</div>
            <div style="margin-top: 10px; margin-bottom: 5px;"><strong>导航日志</strong></div>
            <div id="nav-debug-log" style="max-height: 100px; overflow-y: auto; border-top: 1px solid #666; padding-top: 5px;"></div>
        `;
        
        // 显示导航日志
        showDebugLog();
    }
    
    // 添加导航日志
    function addDebugLog(message) {
        // 获取或创建日志数组
        window._navDebugLogs = window._navDebugLogs || [];
        
        // 添加日志
        window._navDebugLogs.push({
            time: new Date(),
            message: message
        });
        
        // 限制日志数量
        if (window._navDebugLogs.length > 50) {
            window._navDebugLogs.shift();
        }
        
        // 更新日志显示
        showDebugLog();
    }
    
    // 显示导航日志
    function showDebugLog() {
        const logContainer = document.getElementById('nav-debug-log');
        if (!logContainer) return;
        
        // 获取日志数组
        window._navDebugLogs = window._navDebugLogs || [];
        
        // 更新日志显示
        logContainer.innerHTML = '';
        
        // 如果没有日志，显示提示
        if (window._navDebugLogs.length === 0) {
            logContainer.innerHTML = '<div style="color: #999;">暂无导航日志</div>';
            return;
        }
        
        // 显示日志
        window._navDebugLogs.forEach(function(log) {
            const logItem = document.createElement('div');
            logItem.style.marginBottom = '3px';
            logItem.style.borderBottom = '1px dotted #444';
            logItem.style.paddingBottom = '3px';
            
            const time = log.time.toLocaleTimeString();
            logItem.innerHTML = `<span style="color: #999;">[${time}]</span> ${log.message}`;
            
            logContainer.appendChild(logItem);
        });
        
        // 滚动到底部
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    // 清除导航日志
    function clearDebugLog() {
        window._navDebugLogs = [];
        showDebugLog();
    }
    
    // 监听导航事件
    function monitorNavigation() {
        // 监听点击事件
        document.addEventListener('click', function(e) {
            // 向上查找最近的按钮或链接
            let target = e.target;
            while (target && target !== document) {
                if (target.tagName === 'A' || target.tagName === 'BUTTON' || target.classList.contains('btn')) {
                    // 获取文本和链接
                    const text = target.textContent ? target.textContent.trim() : '';
                    const href = target.tagName === 'A' ? target.getAttribute('href') : null;
                    
                    // 记录点击
                    addDebugLog(`点击元素: ${target.tagName} "${text}" ${href ? 'href="' + href + '"' : ''}`);
                    break;
                }
                
                target = target.parentElement;
            }
        }, true);
        
        // 监听页面加载
        window.addEventListener('load', function() {
            addDebugLog('页面加载完成');
            updateDebugPanel();
        });
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                addDebugLog('页面变为可见');
                updateDebugPanel();
            }
        });
    }
    
    // 初始化函数
    function initialize() {
        safeLog('初始化导航调试工具');
        
        // 创建调试面板
        createDebugPanel();
        
        // 监听导航事件
        monitorNavigation();
        
        // 添加初始日志
        addDebugLog('导航调试工具已初始化 (直接静态版本)');
        
        safeLog('导航调试工具初始化完成');
    }
    
    // 在页面加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行初始化
        initialize();
    }
    
    // 导出API，方便调试和手动调用
    window.navDebug = {
        log: addDebugLog,
        show: createDebugPanel,
        update: updateDebugPanel,
        clear: clearDebugLog
    };
})();
