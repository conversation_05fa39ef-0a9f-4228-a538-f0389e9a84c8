Option Explicit

' 九猫小说分析系统完全外部存储启动脚本
' 将所有日志、缓存、临时数据和数据库备份保存到E盘，减轻系统内存压力

' 创建Shell对象
Dim WshShell
Set WshShell = CreateObject("WScript.Shell")

' 设置环境变量
WshShell.Environment("PROCESS")("PYTHONUNBUFFERED") = "1"
WshShell.Environment("PROCESS")("FLASK_ENV") = "production"
WshShell.Environment("PROCESS")("FLASK_DEBUG") = "0"
WshShell.Environment("PROCESS")("PARALLEL_ANALYSIS") = "1"
WshShell.Environment("PROCESS")("THREAD_POOL_SIZE") = "8"
WshShell.Environment("PROCESS")("MAX_WORKERS") = "8"
WshShell.Environment("PROCESS")("DB_POOL_SIZE") = "30"
WshShell.Environment("PROCESS")("DB_MAX_OVERFLOW") = "30"
WshShell.Environment("PROCESS")("USE_EXTERNAL_STORAGE") = "1"
WshShell.Environment("PROCESS")("EXTERNAL_STORAGE_ROOT") = "E:\艹，又来一次\九猫"
WshShell.Environment("PROCESS")("REDIRECT_ALL_LOGS") = "1"
WshShell.Environment("PROCESS")("MEMORY_OPTIMIZATION") = "1"
WshShell.Environment("PROCESS")("USE_CACHE_MANAGER") = "1"
WshShell.Environment("PROCESS")("USE_DB_BACKUP") = "1"
WshShell.Environment("PROCESS")("MEMORY_CHECK_INTERVAL") = "60"
WshShell.Environment("PROCESS")("LOG_CLEANUP_INTERVAL") = "3600"
WshShell.Environment("PROCESS")("DB_BACKUP_INTERVAL") = "3600"
WshShell.Environment("PROCESS")("DB_OPTIMIZE_INTERVAL") = "86400"

' 显示启动信息
WScript.Echo "正在启动九猫小说分析系统（完全外部存储版）..."
WScript.Echo "所有数据将保存到E盘，减轻系统内存压力"
WScript.Echo ""
WScript.Echo "配置信息:"
WScript.Echo "- 线程池大小: 8"
WScript.Echo "- 数据库连接池大小: 30"
WScript.Echo "- 数据库最大溢出连接: 30"
WScript.Echo "- 并行分析: 启用"
WScript.Echo "- 外部存储: 启用"
WScript.Echo "- 外部存储路径: E:\艹，又来一次\九猫"
WScript.Echo "- 所有日志重定向: 启用"
WScript.Echo "- 内存优化: 启用"
WScript.Echo "- 缓存管理器: 启用"
WScript.Echo "- 数据库备份: 启用"
WScript.Echo "- 内存检查间隔: 60秒"
WScript.Echo "- 日志清理间隔: 3600秒"
WScript.Echo "- 数据库备份间隔: 3600秒"
WScript.Echo "- 数据库优化间隔: 86400秒"
WScript.Echo ""
WScript.Echo "系统启动中，请稍候..."

' 确保外部存储目录存在
Dim FSO
Set FSO = CreateObject("Scripting.FileSystemObject")

' 创建主目录
If Not FSO.FolderExists("E:\艹，又来一次\九猫") Then
    FSO.CreateFolder("E:\艹，又来一次\九猫")
End If

' 创建子目录
Dim subDirs
subDirs = Array("logs", "logs\console", "logs\app", "logs\error", "logs\debug", _
                "cache", "cache\json", "cache\pickle", "cache\text", _
                "temp", "db_backup", "memory_stats")

Dim dir
For Each dir In subDirs
    If Not FSO.FolderExists("E:\艹，又来一次\九猫\" & dir) Then
        FSO.CreateFolder("E:\艹，又来一次\九猫\" & dir)
    End If
Next

' 启动应用
Dim Command
Command = "python main.py"
WshShell.Run Command, 1, False

' 等待5秒
WScript.Sleep 5000

' 打开浏览器
WshShell.Run "http://localhost:5001/", 1, False

' 显示完成信息
WScript.Echo "九猫小说分析系统已启动！"
WScript.Echo "请访问: http://localhost:5001/"
WScript.Echo ""
WScript.Echo "所有数据将保存到: E:\艹，又来一次\九猫"
WScript.Echo "- 日志文件: E:\艹，又来一次\九猫\logs"
WScript.Echo "- 缓存数据: E:\艹，又来一次\九猫\cache"
WScript.Echo "- 临时文件: E:\艹，又来一次\九猫\temp"
WScript.Echo "- 数据库备份: E:\艹，又来一次\九猫\db_backup"
WScript.Echo "- 内存统计: E:\艹，又来一次\九猫\memory_stats"
WScript.Echo ""
WScript.Echo "按任意键关闭此窗口..."

' 等待用户按键
WScript.StdIn.ReadLine
