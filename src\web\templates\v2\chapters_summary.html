{% extends "v2/base.html" %}

{% block title %}{{ novel.title }} - 章节分析汇总 - 九猫小说分析系统v2.0{% endblock %}

{% block extra_css %}
<style>
    .summary-header {
        background-color: var(--primary-light);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    .chapter-group {
        margin-bottom: 1.5rem;
    }
    .chapter-group-header {
        background-color: var(--primary-light);
        color: var(--dark-color);
        padding: 0.75rem 1rem;
        border-radius: 0.5rem 0.5rem 0 0;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .chapter-group-header:hover {
        background-color: var(--primary-color);
        color: white;
    }
    .chapter-group-body {
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        border-top: none;
        border-radius: 0 0 0.5rem 0.5rem;
        padding: 1rem;
    }
    .chapter-row {
        padding: 0.75rem;
        border-bottom: 1px solid var(--border-color);
    }
    .chapter-row:last-child {
        border-bottom: none;
    }
    .dimension-badge {
        display: inline-block;
        padding: 0.4em 0.6em;
        font-size: 0.75em;
        font-weight: 600;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 1rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        margin-right: 0.3rem;
        margin-bottom: 0.3rem;
    }
    .dimension-badge.empty {
        background: linear-gradient(135deg, #6c757d, #adb5bd);
    }
    .dimension-filter {
        margin-bottom: 1.5rem;
    }
    .dimension-filter .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 返回按钮和标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="{{ url_for('v2.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回小说详情
        </a>
    </div>
    <div>
        <button class="btn btn-primary" id="aggregateBtn">
            <i class="fas fa-compress-arrows-alt me-2"></i>汇总分析结果
        </button>
    </div>
</div>

<!-- 汇总标题 -->
<div class="summary-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">章节分析汇总</h1>
            <p class="mb-0">
                <span class="badge bg-primary me-2">{{ novel.title }}</span>
                {% if novel.author %}<span class="badge bg-secondary me-2">作者: {{ novel.author }}</span>{% endif %}
                <span class="badge bg-info me-2">{{ novel.word_count }} 字</span>
                <span class="badge bg-success">{{ chapters|length }} 个章节</span>
            </p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="d-flex justify-content-md-end align-items-center">
                <i class="fas fa-list-alt fa-3x text-primary me-3"></i>
                <div>
                    <p class="mb-0 small">汇总时间</p>
                    <p class="mb-0 fw-bold">{{ current_time }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 维度筛选 -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title mb-0"><i class="fas fa-filter me-2"></i>维度筛选</h3>
    </div>
    <div class="card-body">
        <div class="dimension-filter">
            <button class="btn btn-sm btn-primary" id="showAllDimensionsBtn">显示所有维度</button>
            {% for dimension in dimensions %}
                <button class="btn btn-sm btn-outline-primary dimension-filter-btn" data-dimension="{{ dimension.key }}">
                    <i class="{{ dimension.icon }} me-1"></i>{{ dimension.name }}
                </button>
            {% endfor %}
        </div>
    </div>
</div>

<!-- 章节分组 -->
{% for group in chapter_groups %}
    <div class="chapter-group">
        <div class="chapter-group-header" data-bs-toggle="collapse" data-bs-target="#group{{ group.group_id }}">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-folder me-2"></i>第 {{ group.start }} - {{ group.end }} 章
                </div>
                <div>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>
        <div class="chapter-group-body collapse" id="group{{ group.group_id }}">
            {% for chapter in group.chapters %}
                <div class="chapter-row">
                    <div class="row">
                        <div class="col-md-4">
                            <h5 class="mb-1">
                                <a href="{{ url_for('v2.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="text-decoration-none">
                                    {{ chapter.title or '第' + chapter.chapter_number|string + '章' }}
                                </a>
                            </h5>
                            <p class="small text-muted mb-0">{{ chapter.word_count }} 字</p>
                        </div>
                        <div class="col-md-8">
                            <div class="mb-2">
                                {% if chapter_analysis_dimensions and chapter.id in chapter_analysis_dimensions and chapter_analysis_dimensions[chapter.id] %}
                                    {% for dimension_key in chapter_analysis_dimensions[chapter.id] %}
                                        {% set dimension_info_list = dimensions|selectattr('key', 'equalto', dimension_key)|list %}
                                        {% if dimension_info_list %}
                                            {% set dimension_info = dimension_info_list|first %}
                                            <a href="{{ url_for('v2.chapter_analysis', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension_key) }}"
                                               class="dimension-badge dimension-item" data-dimension="{{ dimension_key }}">
                                                <i class="{{ dimension_info.icon }} me-1"></i>{{ dimension_info.name }}
                                            </a>
                                        {% else %}
                                            <span class="dimension-badge dimension-item" data-dimension="{{ dimension_key }}">
                                                {{ dimension_key }}
                                            </span>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <span class="dimension-badge empty">未分析</span>
                                {% endif %}
                            </div>
                            <div>
                                <a href="{{ url_for('v2.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-sm btn-outline-primary">
                                    查看详情
                                </a>
                                {% if not (chapter_analysis_dimensions and chapter.id in chapter_analysis_dimensions and chapter_analysis_dimensions[chapter.id]) %}
                                    <button class="btn btn-sm btn-outline-secondary analyze-chapter-btn" data-chapter-id="{{ chapter.id }}">
                                        开始分析
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
{% endfor %}

<!-- 汇总进度模态框 -->
<div class="modal fade" id="aggregateModal" tabindex="-1" aria-labelledby="aggregateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="aggregateModalLabel">汇总分析结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">选择要汇总的维度</label>
                    <div class="row">
                        {% for dimension in dimensions %}
                            <div class="col-md-4 col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input aggregate-dimension-checkbox" type="checkbox" value="{{ dimension.key }}" id="aggregateDimension{{ loop.index }}">
                                    <label class="form-check-label" for="aggregateDimension{{ loop.index }}">
                                        {{ dimension.name }}
                                    </label>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="d-flex mb-3">
                    <button id="selectAllAggregateBtn" class="btn btn-sm btn-outline-secondary me-2">全选</button>
                    <button id="deselectAllAggregateBtn" class="btn btn-sm btn-outline-secondary me-2">取消全选</button>
                </div>
                <div id="aggregateProgressContainer" style="display: none;">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="aggregateProgressBar"></div>
                    </div>
                    <div id="aggregateStatus" class="mb-3">准备开始汇总...</div>
                    <div id="aggregateLog" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto;">
                        <div class="text-muted">等待汇总开始...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="startAggregateBtn">开始汇总</button>
                <button type="button" class="btn btn-success" id="viewResultsBtn" style="display: none;">查看结果</button>
            </div>
        </div>
    </div>
</div>

<!-- 章节分析模态框 -->
<div class="modal fade" id="chapterAnalysisModal" tabindex="-1" aria-labelledby="chapterAnalysisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chapterAnalysisModalLabel">章节分析</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">选择要分析的维度</label>
                    <div class="row">
                        {% for dimension in dimensions %}
                            <div class="col-md-4 col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input chapter-dimension-checkbox" type="checkbox" value="{{ dimension.key }}" id="chapterDimension{{ loop.index }}">
                                    <label class="form-check-label" for="chapterDimension{{ loop.index }}">
                                        {{ dimension.name }}
                                    </label>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="d-flex mb-3">
                    <button id="selectAllChapterBtn" class="btn btn-sm btn-outline-secondary me-2">全选</button>
                    <button id="deselectAllChapterBtn" class="btn btn-sm btn-outline-secondary me-2">取消全选</button>
                </div>
                <div id="chapterProgressContainer" style="display: none;">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="chapterProgressBar"></div>
                    </div>
                    <div id="chapterStatus" class="mb-3">准备开始分析...</div>
                    <div id="chapterLog" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto;">
                        <div class="text-muted">等待分析开始...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="startChapterAnalysisBtn">开始分析</button>
                <button type="button" class="btn btn-success" id="refreshPageBtn" style="display: none;">刷新页面</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 维度筛选
        const showAllDimensionsBtn = document.getElementById('showAllDimensionsBtn');
        const dimensionFilterBtns = document.querySelectorAll('.dimension-filter-btn');
        const dimensionItems = document.querySelectorAll('.dimension-item');

        showAllDimensionsBtn.addEventListener('click', function() {
            dimensionItems.forEach(item => {
                item.style.display = '';
            });

            dimensionFilterBtns.forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-outline-primary');
            });

            showAllDimensionsBtn.classList.add('btn-primary');
            showAllDimensionsBtn.classList.remove('btn-outline-primary');
        });

        dimensionFilterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');

                dimensionItems.forEach(item => {
                    if (item.getAttribute('data-dimension') === dimension) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });

                dimensionFilterBtns.forEach(btn => {
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-outline-primary');
                });

                showAllDimensionsBtn.classList.remove('btn-primary');
                showAllDimensionsBtn.classList.add('btn-outline-primary');

                this.classList.add('btn-primary');
                this.classList.remove('btn-outline-primary');
            });
        });

        // 汇总按钮
        document.getElementById('aggregateBtn').addEventListener('click', function() {
            const aggregateModal = new bootstrap.Modal(document.getElementById('aggregateModal'));
            aggregateModal.show();
        });

        // 全选/取消全选汇总维度
        document.getElementById('selectAllAggregateBtn').addEventListener('click', function() {
            document.querySelectorAll('.aggregate-dimension-checkbox').forEach(checkbox => {
                checkbox.checked = true;
            });
        });

        document.getElementById('deselectAllAggregateBtn').addEventListener('click', function() {
            document.querySelectorAll('.aggregate-dimension-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
        });

        // 开始汇总按钮
        document.getElementById('startAggregateBtn').addEventListener('click', function() {
            const selectedDimensions = [];
            document.querySelectorAll('.aggregate-dimension-checkbox:checked').forEach(checkbox => {
                selectedDimensions.push(checkbox.value);
            });

            if (selectedDimensions.length === 0) {
                alert('请至少选择一个维度进行汇总');
                return;
            }

            // 显示进度容器
            document.getElementById('aggregateProgressContainer').style.display = 'block';
            this.disabled = true;

            // 重置进度条和日志
            const progressBar = document.getElementById('aggregateProgressBar');
            const statusElement = document.getElementById('aggregateStatus');
            const logElement = document.getElementById('aggregateLog');

            progressBar.style.width = '0%';
            statusElement.textContent = '准备开始汇总...';
            logElement.innerHTML = '<div class="text-muted">等待汇总开始...</div>';

            // 发送API请求
            fetch(`/api/novel/{{ novel.id }}/aggregate_chapters`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dimensions: selectedDimensions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    progressBar.style.width = '100%';
                    statusElement.textContent = '汇总完成';
                    logElement.innerHTML += `<div class="text-success">汇总请求已成功处理</div>`;

                    // 显示查看结果按钮
                    document.getElementById('viewResultsBtn').style.display = 'block';
                } else {
                    progressBar.style.width = '0%';
                    statusElement.textContent = '汇总失败';
                    logElement.innerHTML += `<div class="text-danger">汇总请求失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                progressBar.style.width = '0%';
                statusElement.textContent = '发送请求时出错';
                logElement.innerHTML += `<div class="text-danger">发送请求时出错: ${error.message}</div>`;
            });
        });

        // 查看结果按钮
        document.getElementById('viewResultsBtn').addEventListener('click', function() {
            // 获取选中的维度
            const selectedDimensions = [];
            document.querySelectorAll('.aggregate-dimension-checkbox:checked').forEach(checkbox => {
                selectedDimensions.push(checkbox.value);
            });

            // 如果只选择了一个维度，直接跳转到该维度的分析结果页面
            if (selectedDimensions.length === 1) {
                window.location.href = `/novel/{{ novel.id }}/analysis/${selectedDimensions[0]}`;
            } else {
                // 如果选择了多个维度，跳转到小说详情页面
                window.location.href = "{{ url_for('v2.view_novel', novel_id=novel.id) }}";
            }
        });

        // 章节分析按钮
        document.querySelectorAll('.analyze-chapter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const chapterId = this.getAttribute('data-chapter-id');
                const chapterAnalysisModal = new bootstrap.Modal(document.getElementById('chapterAnalysisModal'));

                // 设置当前章节ID
                document.getElementById('startChapterAnalysisBtn').setAttribute('data-chapter-id', chapterId);

                chapterAnalysisModal.show();
            });
        });

        // 全选/取消全选章节维度
        document.getElementById('selectAllChapterBtn').addEventListener('click', function() {
            document.querySelectorAll('.chapter-dimension-checkbox').forEach(checkbox => {
                checkbox.checked = true;
            });
        });

        document.getElementById('deselectAllChapterBtn').addEventListener('click', function() {
            document.querySelectorAll('.chapter-dimension-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
        });

        // 开始章节分析按钮
        document.getElementById('startChapterAnalysisBtn').addEventListener('click', function() {
            const chapterId = this.getAttribute('data-chapter-id');
            const selectedDimensions = [];
            document.querySelectorAll('.chapter-dimension-checkbox:checked').forEach(checkbox => {
                selectedDimensions.push(checkbox.value);
            });

            if (selectedDimensions.length === 0) {
                alert('请至少选择一个维度进行分析');
                return;
            }

            // 显示进度容器
            document.getElementById('chapterProgressContainer').style.display = 'block';
            this.disabled = true;

            // 重置进度条和日志
            const progressBar = document.getElementById('chapterProgressBar');
            const statusElement = document.getElementById('chapterStatus');
            const logElement = document.getElementById('chapterLog');

            progressBar.style.width = '0%';
            statusElement.textContent = '准备开始分析...';
            logElement.innerHTML = '<div class="text-muted">等待分析开始...</div>';

            // 发送API请求
            fetch(`/api/novel/{{ novel.id }}/chapter/${chapterId}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dimensions: selectedDimensions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    progressBar.style.width = '100%';
                    statusElement.textContent = '分析完成';
                    logElement.innerHTML += `<div class="text-success">分析请求已成功处理</div>`;

                    // 显示刷新页面按钮
                    document.getElementById('refreshPageBtn').style.display = 'block';
                } else {
                    progressBar.style.width = '0%';
                    statusElement.textContent = '分析失败';
                    logElement.innerHTML += `<div class="text-danger">分析请求失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                progressBar.style.width = '0%';
                statusElement.textContent = '发送请求时出错';
                logElement.innerHTML += `<div class="text-danger">发送请求时出错: ${error.message}</div>`;
            });
        });

        // 刷新页面按钮
        document.getElementById('refreshPageBtn').addEventListener('click', function() {
            location.reload();
        });
    });
</script>
{% endblock %}
