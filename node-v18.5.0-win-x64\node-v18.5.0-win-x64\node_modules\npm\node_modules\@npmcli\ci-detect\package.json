{"name": "@npmcli/ci-detect", "version": "2.0.0", "description": "Detect what kind of CI environment the program is in", "author": "GitHub Inc.", "license": "ISC", "main": "./lib", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lint": "eslint '**/*.js'", "postlint": "npm-template-check", "template-copy": "npm-template-copy --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "tap": {"check-coverage": true}, "devDependencies": {"@npmcli/template-oss": "^2.7.1", "tap": "^15.1.6"}, "files": ["bin", "lib"], "repository": {"type": "git", "url": "git+https://github.com/npm/ci-detect.git"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "templateOSS": {"version": "2.7.1"}}