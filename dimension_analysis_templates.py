"""
九猫系统 - 维度分析模板生成器
用于生成基于用户提供的13个维度分析提示词的分析结果

此模块提供了生成分析结果的函数，可以在分析过程中使用。
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

def generate_dimension_analysis_template(
    dimension: str,
    title: str,
    is_chapter: bool = False,
    chapter_title: Optional[str] = None
) -> str:
    """
    生成基于用户提供的13个维度分析提示词的分析结果模板

    Args:
        dimension: 分析维度
        title: 小说标题
        is_chapter: 是否为章节分析
        chapter_title: 章节标题（仅在is_chapter=True时使用）

    Returns:
        结构化的分析结果模板
    """
    # 分析对象描述（整本书或章节）
    analysis_object = f"章节《{chapter_title}》" if is_chapter else f"小说《{title}》"

    # 根据维度获取分析提示词
    analysis_prompts = get_dimension_analysis_prompts(dimension)

    # 构建完整的模板
    template = f"""# {analysis_object}的{get_dimension_name(dimension)}分析

## 分析指导说明
请使用通俗易懂的语言进行分析，避免过多专业术语。每个分析点都应包含：
1. 简明扼要的分析结论
2. 从文本中提取的1-2个具体例子（引用原文）
3. 对这些例子的解释说明
4. 对读者体验的影响评估

## 总体评价
[根据分析结果给出{analysis_object}在{get_dimension_name(dimension)}方面的总体评价，包括优点、特色和可能的不足。使用通俗易懂的语言，避免专业术语。]

{analysis_prompts}

## 总结建议
[基于以上分析，给出对{analysis_object}在{get_dimension_name(dimension)}方面的总结性评价和改进建议。使用具体、可操作的建议，而非抽象概念。]
"""
    return template

def get_dimension_name(dimension: str) -> str:
    """获取维度的中文名称"""
    dimension_names = {
        "language_style": "语言风格",
        "rhythm_pacing": "节奏节拍",
        "structure": "结构分析",
        "sentence_variation": "句式变化",
        "paragraph_length": "段落长度",
        "perspective_shifts": "视角转换",
        "paragraph_flow": "段落流畅度",
        "novel_characteristics": "小说特点",
        "world_building": "世界构建",
        "character_relationships": "角色关系",
        "opening_effectiveness": "开篇效果",
        "climax_pacing": "高潮节奏",
        "theme_analysis": "主题分析",
        "chapter_outline": "章纲分析",

        "popular_tropes": "热梗统计",
        "outline_analysis": "大纲分析"
    }
    return dimension_names.get(dimension, dimension)

def get_dimension_analysis_prompts(dimension: str) -> str:
    """
    根据维度获取分析提示词

    Args:
        dimension: 分析维度

    Returns:
        分析提示词文本
    """
    prompts = {


        "outline_analysis": """## 整体故事大纲与内容概述
[全面详细地重现整部作品的完整内容，以小说叙述的方式进行，而非分析报告形式。必须极其详尽地描述整个故事中发生的一切，包括关键场景、重要事件、人物对话和心理活动，使读者即使不阅读原文也能通过这部分内容完整体验整个故事。

这部分内容不限字数，但必须极其详细，绝对不能少于2000字。严格禁止简短概括或总结性描述。必须以生动、细致、具体的小说叙述方式重现整部作品的内容，让读者仿佛亲历其境。必须包含以下所有要素，并对每个要素进行极其详尽的描述：

1. 详细的场景描述（至少500字）：
   - 描述作品中的关键场景的环境、氛围和背景，包括光线、声音、气味等感官细节
   - 描述场景中的物品、布置和空间关系，提供具体的视觉画面
   - 描述场景的时间特征（如白天/黑夜、季节、天气等）及其变化
   - 描述场景的情绪氛围和给人的整体感受，以及场景如何影响人物情绪
   - 描述场景之间的转换和连接，以及空间设置对故事的影响

2. 完整的事件过程（至少500字）：
   - 按照发生顺序详细描述整部作品中的每个重要事件的起因、经过和结果，不遗漏任何重要细节
   - 描述事件中的具体动作和过程，包括动作的速度、力度和精确描述
   - 描述事件的节奏和紧张程度，以及情绪变化曲线
   - 描述事件对情节发展的推动作用和对人物的影响
   - 描述事件之间的因果关系和连接方式

3. 人物言行与心理（至少500字）：
   - 记录主要人物的关键对话内容，尽可能使用原文中的重要对话，保留对话的语气和特点
   - 详细描述人物的动作、表情和肢体语言，包括微小的表情变化和身体动作
   - 深入描述人物的心理活动、情感变化和内心冲突，展现人物的思想过程
   - 生动描述人物之间的互动和关系动态，包括潜在的情感变化和关系发展
   - 描述人物性格的多面性和复杂性，以及性格如何影响其行为选择

4. 情节转折与冲突（至少300字）：
   - 详细描述作品中的每个主要转折点和冲突，包括冲突的形成、发展和解决过程
   - 描述转折点前后的情节变化和人物反应
   - 分析冲突和转折对人物和故事发展的影响
   - 描述冲突的紧张程度和情感强度
   - 描述多重冲突之间的交织和影响

5. 伏笔与暗示（至少200字）：
   - 指出作品中埋下的伏笔和暗示，以及它们的实现和回收
   - 描述伏笔的埋设方式和隐蔽程度
   - 分析这些伏笔如何与整体故事相连接
   - 描述伏笔回收时的效果和对读者的影响

必须使用生动的语言、丰富的形容词和具体的细节描述，避免抽象概括。必须包含原文中的关键对话和场景描写，保留原文的风格和语气。必须按照时间顺序或情节发展顺序进行叙述，确保叙述的连贯性和完整性。

这部分内容应该尽可能详细，不限字数，越详细越好，使读者能够完全沉浸在故事中，仿佛亲自经历了整个故事的内容。必须确保描述足够详尽，能够让读者不需要阅读原文就能完整理解和体验整个故事。]

## 章节结构与内容分布
[详细分析作品的章节结构和内容分布。列出每个章节的主要内容和功能，评估章节之间的连贯性和平衡性。分析章节如何组织成更大的结构单元（如卷、部分或阶段），以及这些结构单元如何服务于整体叙事。量化不同类型内容（如行动场景、对话、内心描写、环境描写等）在各章节中的分布比例。评估章节结构的节奏感和读者体验]

## 整体结构与架构设计
[全面分析作品的整体结构与架构设计。评估三幕结构/五幕结构/英雄旅程等经典结构模式的应用情况。量化结构完整度和平衡性（开端15%/发展70%/结局15%）。分析结构创新点和变异处理。评估结构如何服务于主题表达和情感传递。提供具体的结构图表，标明主要情节点在整体结构中的位置]

## 情节线设计与管理
[深入分析作品的情节线设计与管理。识别并详述主线与支线的数量、内容和层级关系。量化情节线的完整度和收束度。评估情节线交织技巧和节奏控制。分析各情节线如何服务于人物发展和主题表达。特别关注情节线之间的连接点和交叉点，以及它们如何相互影响和推动整体故事发展]

## 叙事节奏与高潮设计
[分析作品的叙事节奏与高潮设计。评估节奏变化的规律性和目的性。量化高潮分布密度和强度梯度。分析节奏控制技巧和高潮铺垫手法。评估节奏与高潮设计如何增强读者体验和情感投入。详细分析主要高潮点的构建过程，包括铺垫、爆发和余波，以及它们在整体故事中的作用]

## 人物系统与角色设计
[全面分析作品的人物系统和角色设计。识别主要人物、次要人物和配角的功能和特点。评估人物形象的丰满度和一致性。分析人物关系网络的复杂性和动态变化。量化不同类型人物（如正面角色、反面角色、中性角色）的分布比例。评估人物系统如何服务于主题表达和情节发展]

## 人物弧线与成长设计
[评估作品中人物弧线与成长设计。分析主要人物的起点、转折点和终点设置。量化人物变化的幅度和节奏。评估人物成长与情节发展的匹配度。分析人物弧线如何服务于主题表达和读者共鸣。详细追踪主要人物的成长历程，包括关键决策点、挑战和突破，以及这些经历如何塑造人物性格和命运]

## 主题展开与深化策略
[深入分析作品的主题展开与深化策略。识别核心主题和辅助主题的层级关系。量化主题元素的分布密度和强调程度。分析主题通过情节/人物/对话等不同载体的表达方式。评估主题展开的完整性和说服力。特别关注主题如何随着故事发展而逐渐深化和复杂化，以及作品如何通过不同角度和层次探索核心主题]

## 世界观构建与背景设定
[评估作品的世界观构建与背景设定。分析世界观元素的丰富度、一致性和原创性。量化世界观信息的揭示节奏和密度。评估世界观对故事氛围和读者沉浸感的影响。分析背景设定如何服务于情节发展和主题表达。特别关注世界观的独特元素和规则，以及它们如何影响故事中的人物选择和事件发展]""",

        "popular_tropes": """## 热梗识别与统计
[识别并统计文本中出现的热梗元素，包括网络流行梗、文化梗、类型文学常见梗等。列出所有识别到的热梗，并标注出现的章节位置和频率。不对热梗进行评价或分析，仅进行客观记录。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 热梗分类汇总
[将识别到的热梗按类型进行分类汇总，如网络流行梗、影视梗、游戏梗、文学梗、历史梗等。统计各类热梗的数量和占比。列出每类热梗的具体例子，并标注其在文本中的具体位置。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 热梗时效性分析
[记录识别到的热梗的时效性特征，区分经典长寿梗、阶段性流行梗和新兴热梗。标注各热梗的大致流行时间段。统计不同时期热梗的分布情况，展示文本的时代特征。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 热梗使用场景统计
[统计热梗在文本中的使用场景，如对话中、叙述中、人物思想中等。记录热梗与特定人物的关联情况。统计热梗在不同情节类型（如日常、冲突、高潮等）中的分布。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 热梗原创性记录
[记录文本中可能的原创梗或对已有热梗的创新使用。标注这些原创或创新元素的具体位置。不对原创性进行评价，仅客观记录可能成为新热梗的元素。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间热梗统计的继承与发展
[分析本章节与前序章节在热梗使用方面的联系、变化和发展。识别热梗使用的延续性和创新点。评估热梗变化如何服务于情节发展和读者体验。分析作者如何在保持风格一致性的同时实现热梗使用的丰富和变化。必须参考前序章节的热梗统计分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "language_style": """## 语言风格多维度分析
[全面分析文本语言风格的多个维度：正式/口语比例、抒情/叙事比例、简洁/繁复程度。量化分析各类型段落占比，如口语化段落应占40-60%，抒情性段落应控制在20-30%，为读者提供节奏变化。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 词汇层次与专业术语运用
[分析词汇的层次性和专业术语使用情况。统计高频词汇、生僻词和专业术语的分布密度，评估其与题材的匹配度。专业术语密度应与作品类型匹配：硬科幻>3%，玄幻>2%，都市<1%。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 五感描写与意象构建
[深入分析五感描写（视觉、听觉、嗅觉、味觉、触觉）的分布比例和质量。评估意象的原创性、连贯性和象征意义。优质作品应有30%以上的多感官描写，且意象应形成体系，如《夜的命名术》的机械感官系统。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 修辞手法多样性评估
[全面评估修辞手法的使用情况，包括比喻、拟人、排比、对偶、夸张等。分析各类修辞的分布、频率和效果。每万字应有15-20处精彩修辞，且不同类型修辞应均衡分布。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 语言风格与人物/场景匹配度
[分析语言风格与人物身份、场景氛围的匹配程度。评估不同场景（战斗、日常、情感）的语言风格切换自然度。识别语言风格与角色性格的一致性，如《剑来》中陈平安与齐静春的语言差异。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 语言节奏与情绪调控
[分析语言节奏（句长变化、停顿设置）如何服务于情绪调控。评估高潮段落的语言密度和节奏变化。情感高潮段落应有明显的语言节奏变化，短句比例提升30%以上。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 文化元素与时代特征
[识别文本中的文化元素（典故、俗语、方言）和时代特征词汇。评估其真实性、融合度和对世界观的贡献。历史/古代背景作品中的文化元素密度应>2%，且避免明显的现代词汇。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 语言创新与个人风格
[评估作者语言的创新性和个人风格特征。识别标志性表达、独创词汇和句式特点。分析这些特征的一致性和对作品辨识度的贡献。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间语言风格的继承与发展
[分析本章节与前序章节在语言风格方面的联系、变化和发展。识别语言风格的延续性和创新点。评估语言风格变化如何服务于情节发展和人物成长。分析作者如何在保持风格一致性的同时实现语言表达的丰富和变化。必须参考前序章节的语言风格分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "rhythm_pacing": """## 叙事节奏曲线分析
[构建完整的叙事节奏曲线，量化分析紧张-舒缓-高潮的波动频率和幅度。评估节奏变化的合理性和有效性。优质作品应在每3-5万字形成一个完整的节奏周期，且波峰波谷高度差应随情节推进逐渐增大。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 冲突密度与层次分析
[全面分析冲突点的密度、类型和层次。区分外部冲突（人与人、人与环境）和内部冲突（心理、道德）的分布比例。量化每万字的冲突点数量（标准：现代文≥1.2个/千字，玄幻≥1.5个/千字），并评估冲突的层次性和递进关系。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节结构与钩子技巧
[深入分析章节内部结构和章节间的连接技巧。评估章末钩子的类型（悬念型/反转型/情感型/行动型）、强度和有效性。分析钩子与下章开头的呼应关系。参考《十日终焉》的钩子系统，每章应有明确钩子，且70%以上章节钩子应在后续3章内得到回应。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 场景转换与时间流速控制
[分析场景转换的频率、方式和平滑度。评估不同场景的时间流速控制（慢镜头/常速/快进）及其与情节重要性的匹配度。关键场景应采用慢镜头技术，时间流速比例应达到3:1以上，如《三体》危机时刻的时间延展。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 情感节奏与呼吸感构建
[分析情感强度的波动规律和呼吸感段落的设置。评估高潮后的缓冲处理和情感过渡的自然度。高潮后应有300-800字的日常缓冲，形成完整的呼吸感，如《深空彼岸》的茶歇描写。情感波动应形成小-中-大的递进结构。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 信息流密度与悬念管理
[分析信息流密度和悬念管理策略。评估新信息/设定的投放节奏和悬念的埋设-解答周期。量化每300字的新信息点数量，分析悬念的层次结构和解答比例。参考《诡秘之主》的序列晋升节奏，主要悬念应在5-10万字内得到部分解答，同时埋设新悬念。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 阅读体验节奏设计
[评估作品的整体阅读体验节奏设计，包括爽点分布、笑点安排和泪点设置。分析这些情感点的密度、强度和变化规律。优质作品应在每1-2万字设置1个明确的情感共鸣点，且情感类型应有变化。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 多线叙事节奏协调
[针对多线叙事作品，分析各条叙事线的节奏协调性和交织技巧。评估线索切换的时机选择和节奏平衡。多线叙事应在关键节点实现线索交汇，且各线索的发展速度应保持相对平衡，避免读者对某一线索的长期脱节。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间节奏节拍的继承与发展
[分析本章节与前序章节在节奏节拍方面的联系、变化和发展。识别节奏模式的延续性和变化点。评估节奏变化如何服务于情节发展和情感积累。分析作者如何通过节奏控制实现章节间的连贯性和递进性。必须参考前序章节的节奏节拍分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "structure": """## 整体结构框架分析
[全面分析作品的结构框架，包括经典结构模式（三幕式、英雄旅程、五幕式等）的应用情况。识别关键结构节点（如启程、第一转折点、中点危机、第二转折点、高潮、结局）的位置和处理方式。评估结构的完整性、平衡性和创新性。参考《万相之王》的英雄旅程结构，关键节点应在合理字数范围内出现（如第一转折点应在总字数的20-25%处）。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 伏笔系统与回收机制
[深入分析伏笔的埋设、发展和回收系统。评估伏笔的层次性（主线伏笔/支线伏笔/细节伏笔）、密度和回收率。量化首卷伏笔的回收时间分布，50%的伏笔应在30万字内回收，90%应在全书范围内回收。分析伏笔回收的艺术性和满足感，如《庆余年》的箱中狙击枪伏笔。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 叙事结构与时间线管理
[分析作品的叙事结构（线性/非线性）和时间线管理技巧。评估倒叙、插叙、平行叙事等手法的使用比例和效果。量化非线性叙事的占比（悬疑类作品建议15-25%），分析其对情节理解和悬念构建的贡献。参考《天才基本法》的双时空结构，评估时间线切换的清晰度和必要性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节结构与篇章布局
[评估章节内部结构的完整性和章节间的逻辑关系。分析篇章布局的节奏感和平衡性。评估章节长度分布的合理性和变化规律。优质作品应保持章节长度的相对稳定（波动不超过30%），同时在关键情节处适当调整章节长度以强化效果。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 多线叙事结构与交织技巧
[针对多线叙事作品，分析各条叙事线的结构完整性和交织技巧。绘制多线叙事拓扑图，评估主支线交叉频率（建议>3次/卷）和交叉点的情节价值。分析各线索的权重分配和发展平衡。参考《庆余年》的三线交织结构，评估多线叙事的清晰度和整体协调性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 情节单元与副本结构
[分析作品中的情节单元（副本/任务/事件）结构。评估单元内部的完整性和单元间的衔接自然度。量化单元结构与主线的咬合度，分析其对整体情节推进的贡献。参考《全球高考》的考场副本结构，评估单元的独立性和系统性平衡。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 结构节奏与高潮分布
[分析作品的结构节奏和高潮分布规律。评估小高潮、中高潮和大高潮的层次安排和递进关系。量化高潮间的字数间隔和强度变化。优质作品应形成清晰的高潮递进结构，主要高潮应在关键结构点出现，且强度应逐渐提升。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 结构创新与类型突破
[评估作品在结构设计上的创新点和类型突破。分析非传统结构元素的融入方式和效果。评估结构创新对读者体验和作品主题表达的贡献。创新结构应保持足够的可读性，同时为作品增添独特价值。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间结构的继承与发展
[分析本章节与前序章节在结构方面的联系、变化和发展。识别结构元素的延续性和创新点。评估本章节如何承接前序章节的结构设计，又如何为后续章节铺垫。分析作者如何通过结构设计实现章节间的连贯性和递进性。必须参考前序章节的结构分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "sentence_variation": """## 句式类型分布与多样性
[全面分析句式类型（简单句/复合句/并列句/复杂句）的分布比例和变化规律。评估句式多样性指数（不同句式类型数量/总句子数量），优质作品指数应>0.4。分析不同场景的句式分布特点，如战斗场景短句占比应>70%，抒情场景长句占比应>60%。参考《第一序列》的刀光血溅段落与心理描写段落对比。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 句长控制与节奏变化
[深入分析句长分布和变化规律。计算平均句长和句长标准差，评估句长变化的节奏感和目的性。量化不同情绪/场景的句长特征，建立句长-情绪映射图。优质作品应在关键情节处有明显的句长变化模式，如《龙族》的情感爆发段落句长由长到短的渐变。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 修辞句式应用与效果
[分析各类修辞句式（排比/递进/对偶/设问/反问等）的使用频率、分布位置和表达效果。评估修辞句式与内容的匹配度和新颖度。量化重要场景的修辞密度，关键宣言/演讲段落的修辞句式密度应>30%。参考《雪中悍刀行》的宣言式语句，分析其在情节中的点睛作用。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 对话句式特征与人物塑造
[深入分析对话句式的特征及其与人物塑造的关系。评估不同人物的对话句长、句式复杂度和语言特点。量化主要角色的对话句式差异化程度，建议平均对话句长≤15字，且主要角色间的对话特征差异度应>30%。参考《全职高手》中叶修、黄少天等角色的对话差异，分析对话句式如何强化人物性格。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 句式节奏与情感表达
[分析句式节奏与情感表达的关系。评估句式变化如何服务于情感强度的递进和转折。量化情感高潮段落的句式特征（如短句频率、破折号使用率、感叹句比例）。优质作品应在情感转折处有明显的句式变化，如《龙族》告别场景的长短句交替和句式节奏变化。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 句首句尾变化技巧
[分析句首句尾的变化技巧和模式。评估句首变化（如副词/动词/名词开头）和句尾处理（如余韵/悬念/点题）的多样性和效果。量化不同类型句首的分布比例，避免单一句式模式（如某类句首占比不应超过40%）。分析句首句尾变化如何增强文本的节奏感和可读性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 标点符号运用艺术
[深入分析标点符号的使用频率、分布和艺术效果。评估特殊标点（破折号/省略号/感叹号等）的使用目的和效果。量化不同情绪场景的标点特征，如紧张场景的逗号密度应降低20%以上。分析标点符号如何辅助句式变化，增强表达效果。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 句式创新与个人风格
[评估作者在句式运用上的创新点和个人风格特征。分析标志性句式结构和句式组合模式。量化创新句式的使用频率和分布，评估其对作品风格的贡献。分析句式风格与作品主题/类型的匹配度，如何通过句式变化强化作品的整体风格。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间句式变化的继承与发展
[分析本章节与前序章节在句式变化方面的联系、变化和发展。识别句式特征的延续性和创新点。评估句式变化如何服务于情节发展和人物成长。分析作者如何在保持风格一致性的同时实现句式表达的丰富和变化。必须参考前序章节的句式变化分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "paragraph_length": """## 段落长度分布与规律
[全面分析段落长度的分布规律和变化模式。绘制段落长度分布图，评估短段落（<100字）、中段落（100-300字）和长段落（>300字）的比例。量化超长段落（>500字）的占比，建议控制在5%以内。分析段落长度与内容类型（对话/动作/描写/心理）的关系。参考《大王饶命》的段落切割技巧，评估段落长度控制的合理性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 黄金段落区间与阅读舒适度
[深入分析黄金段落区间（150-300字）的分布和效果。评估黄金段落占比（建议>60%）对阅读舒适度的影响。量化不同阅读平台（手机/电脑/纸质书）的最佳段落长度区间。分析段落长度与阅读节奏的关系，评估段落长度如何影响读者的阅读速度和沉浸感。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 段落长度与内容密度关系
[分析段落长度与内容密度的关系。评估不同长度段落的信息承载量和表达效率。识别信息密度断层（>800字无转折点），预警可能导致阅读疲劳的段落。对比分析《道诡异仙》等高密度行文作品的段落控制技巧，评估如何在保持信息密度的同时优化段落长度。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 段落长度与场景节奏控制
[评估段落长度如何服务于场景节奏控制。分析不同场景类型（战斗/对话/描写/心理）的最佳段落长度模式。量化紧张场景的段落长度变化（通常应缩短30%以上）。分析段落长度变化如何强化情节节奏，如《诡秘之主》的战斗场景段落长度递减模式。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 分镜式段落技巧应用
[深入分析分镜式段落技巧的应用。识别单动作聚焦段落及其在关键场景中的分布。评估分镜式段落的效果和适用场景。参考《诡秘之主》的"拔枪→射击→血雾"三连段，分析如何通过段落切割增强视觉冲击力和节奏感。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 段落呼吸曲线与节奏变化
[构建段落呼吸曲线，分析长短段落交替的频率和规律。评估段落长度变化的节奏感和目的性。量化长短段落交替频率（建议2-4段/次），分析其对阅读节奏的影响。参考《剑来》的山水描写段落节奏，评估段落长度变化如何创造文本的呼吸感。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 段落开头结尾处理技巧
[分析段落开头和结尾的处理技巧。评估段首句型（总结句/场景句/动作句/对话句）的分布和效果。分析段尾处理（悬念/转折/余韵/铺垫）的多样性和目的性。量化不同类型段落开头的分布比例，避免单一模式（如某类段首占比不应超过40%）。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 段落长度与叙事视角关系
[分析段落长度与叙事视角的关系。评估不同视角（第一人称/第三人称/全知视角）下的最佳段落长度模式。量化视角切换时的段落长度变化规律。分析如何通过段落长度的变化暗示视角的转换或情感的变化。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间段落长度的继承与发展
[分析本章节与前序章节在段落长度方面的联系、变化和发展。识别段落长度模式的延续性和变化点。评估段落长度变化如何服务于情节发展和情感表达。分析作者如何通过段落长度控制实现章节间的连贯性和递进性。必须参考前序章节的段落长度分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "perspective_shifts": """## 叙事视角类型与分布
[全面分析作品中的叙事视角类型（第一人称/第三人称限制性/第三人称全知/多视角）及其分布。评估主视角的占比（标准：80%-90%）和辅助视角的功能性。量化不同视角的字数分布和切换频率。参考《斗破苍穹》萧炎视角的覆盖率和辅助视角的使用时机，分析视角选择的合理性和效果。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 视角切换技巧与标记系统
[深入分析视角切换的技巧和标记系统。评估视角切换的清晰度和平滑度。识别视角切换标记（如空行/时间戳/场景转换/人物名标题），分析其有效性。参考《诡秘之主》塔罗会的视角切割技巧，评估如何在多视角叙事中保持清晰的阅读体验。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 视角深度与限制性控制
[分析视角深度（表层感知/中层思考/深层潜意识）的变化和控制。评估限制性视角的严格程度和一致性。识别视角越界或视角污染点，如非设定解释段落中出现的上帝视角。参考《三体》黑暗森林法则解说的视角处理，分析如何在保持视角限制的同时传递必要信息。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 心理描写与视角表现
[评估心理描写在视角表现中的作用和技巧。分析动作与心理活动的同步率（建议间隔≤2句）和平衡性。量化不同角色视角下的心理描写深度和频率。参考《大奉打更人》查案推演中的心理描写，分析如何通过心理活动深化视角体验。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 多视角叙事结构与功能
[针对多视角作品，分析视角安排的结构性和功能性。评估不同视角的叙事功能（推动情节/展示世界/深化人物/制造悬念）。量化视角切换与情节发展的关联度。分析多视角如何服务于整体叙事目标，如《冰与火之歌》的多视角结构如何构建宏大世界观。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 视角一致性与污染控制
[深入分析视角的一致性和污染控制。生成视角污染热力图，标记同一段落内视角跳跃位置。评估视角污染的严重程度和影响。参考《剑来》的视角严守案例，分析如何在复杂叙事中维持视角的纯净度。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 视角与情感距离调控
[分析视角选择如何影响读者与角色/事件的情感距离。评估不同视角下的情感投入度和代入感。量化关键情感场景的视角处理特点。分析如何通过视角调整（拉近/推远）来控制读者情感体验，如《白夜行》中对雪穗视角的处理。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 视角创新与叙事实验
[评估作品在视角运用上的创新点和叙事实验。分析非传统视角（如物品视角/集体视角/交替视角）的使用效果。评估视角创新对作品主题表达和阅读体验的贡献。分析视角实验的成功因素和局限性，如《云边有个小卖部》的特殊视角处理。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间视角转换的继承与发展
[分析本章节与前序章节在视角转换方面的联系、变化和发展。识别视角处理的延续性和创新点。评估视角变化如何服务于情节发展和人物塑造。分析作者如何在保持视角一致性的同时实现视角表达的丰富和变化。必须参考前序章节的视角转换分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "paragraph_flow": """## 段落内部逻辑结构与连贯性
[深入分析段落内部的逻辑结构和句子连接方式。评估句与句之间的逻辑关系（因果/递进/转折/并列）的明确性和自然度。量化逻辑关系词（因此/然而/不仅/同时等）的使用频率和分布。分析段落内部逻辑链的完整性和流畅度，识别逻辑断裂或跳跃点。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 段落间过渡技巧与衔接艺术
[全面分析段落之间的过渡技巧和衔接方法。识别不同类型的过渡手段（关键词重复/呼应/逻辑关系词/时空标记/人物连接）及其效果。评估过渡句的使用频率（标准：每800字≥1个）和质量。参考《雪中悍刀行》的地理衔接词运用，分析如何通过精妙的过渡技巧增强文本的连贯性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 段落主题控制与焦点管理
[分析段落主题的控制技巧和焦点管理策略。评估段落内部主题的一致性和焦点的稳定性。识别主题偏离或焦点跳跃点，分析其是否为有意为之及其效果。量化段落主题句的位置分布（首句/中间/尾句）和表达方式，评估其对段落凝聚力的贡献。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 信息流密度与节奏控制
[深入分析段落间信息流密度的分布和变化规律。评估信息密度的均衡性和变化的目的性。识别信息密度断层（连续3段无转折则预警）和信息过载点。参考《道诡异仙》的高密度叙事节奏，分析如何在保持信息丰富度的同时避免读者疲劳。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 段落结构多样性与层次感
[评估段落结构的多样性和层次感。分析不同结构类型（总分/分总/并列/递进/对比/因果）的分布比例和适用场景。量化段落层次的深度和复杂度，评估其与内容复杂性的匹配度。分析段落结构如何服务于内容表达和阅读体验。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 场景流动与分镜技术
[分析场景流动的自然度和分镜技术的应用。识别分镜切割点及其标志性特征（如动作动词引导段落切换）。评估场景转换的清晰度和流畅度。参考《诡秘之主》的"扣扳机→血雾炸开"三连段，分析如何通过精确的分镜技术增强叙事的视觉冲击力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 节奏变化与呼吸感构建
[深入分析文本的节奏变化和呼吸感构建技巧。评估长短段落交替的频率和比例（建议2:1~3:1）。分析段落节奏与情节发展/情感变化的匹配度。参考《龙族》情感爆发段落的节奏处理，评估如何通过段落节奏变化增强文本的表现力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 语言流畅度与阅读障碍识别
[全面评估文本的语言流畅度和潜在阅读障碍。生成流畅度障碍图谱，标注超长复合句/逻辑断裂点/信息过载区域。分析这些障碍点对阅读体验的影响和改进方法。参考《凡人修仙传》飞升描写优化版，对比分析修改前后的流畅度变化。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 衔接手段多样性与效果评估
[分析文本中使用的各类衔接手段（词汇衔接/语法衔接/逻辑衔接/语义衔接）的多样性和效果。评估不同类型衔接手段的分布比例和适用场景。量化关键衔接词（代词/连接词/指示词）的使用频率和准确性。分析衔接手段如何增强文本的连贯性和可读性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间段落流畅度的继承与发展
[分析本章节与前序章节在段落流畅度方面的联系、变化和发展。识别段落流畅度特征的延续性和创新点。评估段落流畅度变化如何服务于情节发展和阅读体验。分析作者如何在保持风格一致性的同时实现段落流畅度的丰富和变化。必须参考前序章节的段落流畅度分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "novel_characteristics": """## 题材类型分析与混合度评估
[全面分析作品的题材类型特征及其混合度。识别主要题材（玄幻/都市/科幻/悬疑/历史等）的核心元素及其在文本中的表现方式。评估多题材混合的比例和融合度。量化不同题材元素的分布密度，分析题材选择与目标读者群体的匹配度。评估题材处理的专业性和深度，如科幻元素的科学基础，历史元素的史实准确性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 风格特征识别与独特性评估
[深入分析作品的风格特征和独特性。识别标志性风格元素（如语言风格/叙事节奏/情感基调/幽默方式）及其一致性。评估风格与内容/主题的匹配度。量化风格独特性指数，对比同类作品找出差异化特征。分析风格形成的原因和对读者体验的影响。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 叙事手法多样性与创新性
[全面评估作品使用的叙事手法及其创新性。分析各类叙事技巧（线性/非线性/多线/意识流/元叙事等）的使用比例和效果。识别创新性叙事手法及其对作品的贡献。量化叙事手法的变化频率和转换自然度。评估叙事手法与内容表达的匹配度。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 主题深度与思想内涵
[深入探索作品的主题深度和思想内涵。识别显性主题和隐性主题，分析主题的层次性和复杂度。评估主题表达的方式（直接/象征/隐喻/人物体现）和有效性。量化主题相关段落的分布密度，分析主题如何贯穿全文。评估作品在哲学思考/社会批判/人性探索等方面的深度。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 情感共鸣点与读者连接
[全面分析作品中的情感共鸣点和读者连接机制。识别可能引起读者共鸣的情感点（如成长/爱情/友情/奋斗/挫折）和认知点（如价值观/世界观）。评估共鸣点的普遍性和深度。量化情感共鸣点的分布密度（建议每2-3万字设置1个强共鸣点）。分析作品如何通过共鸣点建立与读者的情感连接。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 商业元素与市场定位
[分析作品的商业元素和市场定位。识别商业化标签（如系统/签到/爽文/甜宠）的使用密度和方式。评估情绪浓度曲线（如甜宠文每小时甜度值≥8）和爽点分布。量化热点响应指数和时事关联度。参考《我师兄实在太稳健了》的标签词频和《偷偷藏不住》的吻戏分布，分析商业元素的有效性和读者接受度。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 创新点与突破性特征
[深入分析作品的创新点和突破性特征。识别在题材/风格/结构/人物/世界观等方面的创新元素。评估创新的深度和广度，区分表面创新和本质创新。量化创新元素的分布密度和重要性。分析创新如何为作品增添价值和差异化竞争力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 元素融合度与类型突破
[全面评估作品的元素融合度和类型突破。分析不同元素（如悬疑+克苏鲁）的交叉频率和融合自然度。评估元素融合的创造性和合理性。量化跨类型元素的分布密度（如《道诡异仙》每章≥2处跨类型元素）。分析元素融合如何拓展作品的表现力和吸引力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 互动性与读者参与度
[分析作品的互动性设计和读者参与机制。识别互动式结构（如选择支节点）和读者参与点。评估互动设计的合理性和吸引力。参考《今夜通灵》的弹幕投票触发点，分析如何通过互动增强读者参与感和沉浸体验。探讨作品在新媒体环境下的互动潜力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间小说特点的继承与发展
[分析本章节与前序章节在小说特点方面的联系、变化和发展。识别小说特点的延续性和创新点。评估小说特点变化如何服务于情节发展和主题表达。分析作者如何在保持风格一致性的同时实现小说特点的丰富和变化。必须参考前序章节的小说特点分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "world_building": """## 世界观体系完整性与层次性
[全面评估作品世界观的完整性和层次性。分析世界观的核心要素（历史/地理/文化/种族/政治/经济/宗教等）的覆盖度和深度。评估世界观的宏观结构和微观细节的平衡性。量化世界观要素的分布密度，分析世界观构建的系统性和有机性。参考《指环王》的中土世界构建，评估世界观的历史纵深感和文化多样性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 设定一致性与逻辑自洽
[深入分析世界设定的内部一致性和逻辑自洽性。识别潜在的逻辑矛盾、设定冲突或世界观漏洞。评估设定规则的严密性和例外处理的合理性。量化设定冲突点（应<1个/10万字）和设定补充/修正频率。分析设定一致性如何影响读者的沉浸感和信任度。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 世界构建技巧与信息投放
[全面评估世界构建的技巧和信息投放策略。分析直接说明与间接展示的比例和效果。评估设定释放曲线的合理性（建议前10万字仅展开30%设定）。量化世界观信息的投放密度和节奏。参考《诡秘之主》的序列体系披露节奏，分析如何避免信息过载同时保持读者兴趣。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 符号系统与记忆锚点
[分析作品的符号系统和记忆锚点设计。评估标志性符号（如魔法体系/组织标识/特殊物品）的独特性和记忆性。量化符号记忆点密度（建议每5万字新增1个标志物）。参考《斗罗大陆》的魂环颜色体系，分析如何通过视觉化符号增强世界的可识别性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 规则体系设计与平衡性
[深入分析作品中的规则体系（魔法/科技/武功/超能力等）设计。评估规则的原创性、合理性和完整性。分析规则体系的内部平衡和进阶逻辑。量化规则体系的复杂度和学习曲线。生成规则可视化图谱，评估抽象概念到具象道具的映射清晰度。参考《第一序列》的阶级通行证设计，分析规则体系如何服务于情节和主题。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 社会生态系统与权力结构
[全面评估作品中的社会生态系统和权力结构。分析经济/武力/权力的三角制约关系和平衡机制。评估社会阶层、组织架构和权力流动的合理性。量化社会系统的复杂度和真实感。参考《庆余年》的四大宗师平衡模型，分析社会生态闭环的强度和稳定性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 文化深度与现实映射
[分析作品中的文化构建深度和现实映射关系。评估文化元素（语言/习俗/艺术/信仰）的丰富度和独特性。分析文化锚点与现实文化的契合度和改编合理性。量化文化细节的分布密度和真实感。参考《大国重工》的三线建设还原度，评估文化构建如何增强世界的可信度和厚重感。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 世界与情节/人物的融合度
[深入分析世界设定与情节发展、人物塑造的融合度。评估世界设定如何推动情节发展、塑造人物性格和制造冲突。分析世界元素在关键情节点的作用和必要性。量化世界设定对情节的贡献度，避免"设定为设定"的问题。分析世界构建如何服务于作品的核心主题和情感表达。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 世界构建创新性与想象力
[全面评估作品世界构建的创新性和想象力。识别原创世界元素和概念的数量和质量。分析世界构建中的跨界融合和类型突破。评估世界设定的拓展潜力和衍生可能。量化创新世界元素的分布密度和重要性。分析世界构建的创新如何为作品增添独特价值和吸引力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间世界构建的继承与发展
[分析本章节与前序章节在世界构建方面的联系、变化和发展。识别世界观元素的延续性和扩展点。评估世界构建的渐进式展开和深化过程。分析作者如何在保持世界观一致性的同时实现世界设定的丰富和扩展。必须参考前序章节的世界构建分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "character_relationships": """## 关系网络结构与复杂度
[全面分析作品中的人物关系网络结构和复杂度。绘制主要人物之间的关系网络图，包括亲情、友情、爱情、敌对、师徒、主仆等多维关系。评估关系网络的密度、层次性和平衡性。量化关系连接点的数量和强度，分析网络的中心性和边缘性。参考《琅琊榜》的誉王关系网，评估权谋文中节点连接度（应>3）和关系复杂度。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 关系动态演变与发展轨迹
[深入分析人物关系的动态演变和发展轨迹。追踪重要关系对的建立、发展、转变和结局过程。评估关系变化的自然度和必然性。量化关系转折点的分布密度和影响力。分析关系发展如何反映人物成长和主题深化。参考《剑来》中陈平安与各角色关系的演变，评估关系发展的深度和情感厚度。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 关系层次与多维互动
[评估人物关系的层次性和多维互动。分析表层关系（社会身份/职责）与深层关系（情感/信任/价值观）的对比和冲突。识别复杂关系中的矛盾性和张力。量化关系维度的丰富度（单维关系vs多维关系）。分析多维关系如何增加人物互动的复杂性和真实感。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 冲突与和解模式分析
[全面分析人物关系中的冲突与和解模式。识别冲突类型（利益/价值观/误解/性格）及其产生原因。评估冲突解决方式的多样性和合理性。量化冲突-和解循环的频率和强度变化。分析冲突与和解如何推动人物成长和关系深化。参考《长夜余火》的团队冲突处理，评估冲突的建设性作用。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 关系对情节的推动作用
[深入分析人物关系对情节发展的推动作用。评估关键情节点中关系因素的影响力。量化关系变化引发的情节转折比例。分析关系与情节的互动模式（关系推动情节vs情节改变关系）。识别关系驱动型情节和情节驱动型关系的分布。评估关系对情节推动的有效性和必要性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 次要人物关系价值与独立性
[评估次要人物的关系价值和独立性。分析次要人物与主要人物的关联度和功能性。量化次要人物的"工具人"特征（对主线推动率<5%的角色应有其他价值）。评估次要人物关系网的独立完整性。参考《大王饶命》的同学群像处理，分析如何避免次要角色沦为纯工具人。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 关系强度与情感深度
[分析人物关系的强度和情感深度。量化羁绊强度值及其波动曲线。评估重大事件（救赎/背叛/牺牲）对关系强度的影响系数。分析情感投入的真实性和感染力。参考《剑来》中陈平安羁绊值的波动曲线，评估关系强度变化如何增强情感共鸣。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 关系创新与类型突破
[评估作品在人物关系设计上的创新点和类型突破。分析非传统关系模式和关系发展路径。识别关系设定中的颠覆性元素和新颖互动。量化关系熵变（如敌友转换动态），对标《诡秘之主》的阿蒙互动模式。分析关系创新如何为作品增添独特魅力和深度。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 身份与关系的互动机制
[深入分析身份变化与关系发展的互动机制。评估身份反转对关系网络的冲击和重构作用。量化身份反转的频率（标准：每20万字≥1次）和影响范围。分析身份与关系的相互塑造过程。参考《长夜余火》的商见曜队友反转记录，评估身份变化如何增强关系的戏剧性和复杂性。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间人物关系的继承与发展
[分析本章节与前序章节在人物关系方面的联系、变化和发展。识别关系发展的延续性和转折点。评估关系变化如何服务于人物成长和情节推进。分析作者如何在保持关系逻辑性的同时实现关系动态的丰富和深化。必须参考前序章节的人物关系分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "opening_effectiveness": """## 开篇吸引力与注意力捕获
[全面评估开篇的吸引力和注意力捕获能力。分析开篇的核心吸引元素（悬念/冲突/场景/人物/语言）及其效果。评估首段/首页/首章的阅读黏性和转化率。量化开篇关键指标：首章危机浓度（建议前3000字含≥2个生死冲突）、悬念设置密度、场景代入感强度。参考《赤心巡天》的背叛+濒死事件开篇，分析如何在开篇迅速吸引读者注意。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 信息投放策略与节奏控制
[深入分析开篇信息的投放策略和节奏控制。评估背景信息、人物信息、冲突信息、世界观信息的投放比例和顺序。量化信息密度曲线，避免信息过载或信息匮乏。分析信息投放与读者接受能力的匹配度。参考《诡秘之主》的信息递进式开篇，评估如何在保持神秘感的同时提供足够的阅读锚点。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 人物与冲突引入技巧
[全面评估开篇的人物与冲突引入技巧。分析主要人物的引入方式、顺序和初始形象塑造。评估核心冲突的埋设位置和展示方式。量化三章关键元素（人设/冲突/悬念完整度），参考《万相之王》的开篇要素覆盖率。分析人物与冲突如何相互映衬，共同构建开篇张力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 世界观与设定展示艺术
[分析开篇的世界观与设定展示艺术。评估世界元素的引入自然度和吸引力。量化金手指/核心设定的显性度（核心设定应在1万字内完整展示）。分析直接说明与间接展示的比例和效果。参考《我有一座恐怖屋》的黑色手机机制展示，评估如何在开篇高效地建立世界观框架。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 悬念系统与钩子设计
[深入分析开篇的悬念系统和钩子设计。评估不同层次悬念（即时悬念/中期悬念/长期悬念）的设置和平衡。量化悬念钩子强度和密度，评估未解之谜的呼应周期（主要悬念需在5万字内得到部分解答）。参考《十日终焉》的头颅消失谜题，分析如何通过悬念系统维持读者长期阅读动力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 情感基调与共情机制
[评估开篇的情感基调和共情机制设计。分析情感基调的建立方式和与作品整体风格的匹配度。量化共情指数，标记孤独/愤怒/期待等情绪峰值点。评估角色处境与读者情感连接的有效性。参考《深海余烬》的失乡号共鸣曲线，分析如何在开篇建立强有力的情感连接。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 主题暗示与象征铺垫
[分析开篇对全文主题的暗示技巧和象征铺垫。识别主题元素的埋设位置和表现形式（象征/隐喻/对比/伏笔）。评估主题暗示的隐蔽性和有效性。量化主题相关元素的密度和分布。分析开篇主题暗示如何为全文奠定思想基调，引导读者深层阅读。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 开篇与全文的结构呼应
[全面评估开篇与全文的结构呼应关系。分析开篇元素在全文中的延展、发展和变奏。评估开篇与结尾的呼应技巧和完整性。量化开篇伏笔的回收率和分布。分析开篇如何为全文结构奠定基础，形成首尾呼应的完整感。参考《三体》的开篇与全系列的结构关联，评估开篇的长期结构价值。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 读者期待建立与阅读动力
[深入分析开篇如何建立读者期待和阅读动力。评估情感期待、情节期待和认知期待的构建技巧。量化期待点的密度和强度。分析期待与满足的平衡机制。评估开篇对不同类型读者的吸引策略。分析开篇如何通过多层次期待建立，形成持续的阅读推动力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间开篇效果的继承与发展
[分析本章节与前序章节在开篇效果方面的联系、变化和发展。识别开篇技巧的延续性和创新点。评估开篇效果变化如何服务于情节发展和读者体验。分析作者如何在保持风格一致性的同时实现开篇效果的丰富和变化。必须参考前序章节的开篇效果分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "climax_pacing": """## 高潮铺垫系统与节奏控制
[全面分析高潮前的铺垫系统和节奏控制。评估伏笔埋设、情绪积累、冲突升级的层次性和渐进性。量化压抑段与爆发段的比例（建议3:1至5:1）和节奏变化曲线。分析铺垫元素的多样性（情节/情感/象征/对比）和密度分布。识别高潮预警信号（高潮前应埋设3-5个暗示）及其明显度梯度。参考《斗破苍穹》的突破瓶颈节奏和《全职高手》的赛前铺垫技巧，评估铺垫如何增强高潮的爆发力和满足感。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 高潮结构设计与层次安排
[深入分析作品的高潮结构设计和层次安排。识别多重高潮结构（小高潮/中高潮/大高潮）的分布和递进关系。评估高潮间的间隔和强度变化规律。量化不同层次高潮的影响范围和持续时间。分析高潮的内部结构（铺垫-爆发-余韵）的完整性和平衡性。参考《诡秘之主》的多层次高潮设计，评估高潮结构如何维持长篇作品的节奏张力。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 高潮爆发技巧与强度控制
[全面评估高潮爆发的技巧和强度控制。分析高潮爆发的表现手法（节奏加速/句式变化/场景聚焦/多感官描写）和效果。量化高潮段落的语言密度和节奏变化。评估情节转折的力度和情感爆发的强度。分析转折点强度（关键转折应同时影响3个以上角色）和影响范围。参考《诡秘之主》塔罗会变局，评估高潮爆发如何达到最大戏剧效果。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 情感共鸣与读者体验
[分析高潮中的情感共鸣设计和读者体验营造。评估情感峰值的类型多样性和分布密度（每5万字应设置1-2个情感共鸣点）。量化情感强度曲线和变化规律。分析情感共鸣点与人物成长/主题表达的关联度。参考《龙族》路明非觉醒场景，评估如何通过情感设计增强高潮的感染力和记忆点。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 高潮后余韵处理与情绪缓冲
[深入分析高潮后的余韵处理和情绪缓冲技巧。评估余韵段落的长度比例和节奏变化。分析情绪缓冲、情节收束和后续铺垫的平衡处理。量化余波效应的持续时间（高潮影响应持续3章以上）和影响范围。参考《庆余年》范闲北齐行后续，评估余韵处理如何避免情感断崖同时保持读者兴趣。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 高潮与主题/人物的统一性
[评估高潮与作品主题、人物成长的统一性和呼应关系。分析高潮如何强化主题表达和推动人物成长。量化高潮中主题元素的密度和表现方式。评估高潮对人物命运/关系/价值观的影响深度。分析高潮在思想内涵层面的价值，避免"为爽而爽"的空洞高潮。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 多线交汇与整体协调
[针对多线叙事作品，分析高潮中的多线交汇技巧和整体协调。评估不同叙事线在高潮中的汇聚方式和节奏控制。量化线索交汇点的密度和重要性。分析多线高潮的同步性/错峰性安排及其效果。参考《冰与火之歌》的多线高潮编排，评估如何在复杂叙事中创造协调统一的高潮体验。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 类型化高潮与创新突破
[分析作品在高潮设计上的类型特征和创新突破。识别符合类型期待的高潮元素和突破类型框架的创新点。评估创新高潮的风险控制和读者接受度。量化高潮中的创新元素密度和重要性。分析高潮创新如何在满足类型读者基本期待的同时，提供新鲜的阅读体验。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 高潮节奏与整体叙事节奏的协调
[全面评估高潮节奏与作品整体叙事节奏的协调性。分析高潮在整体叙事曲线中的位置和功能。评估高潮前后的节奏过渡自然度和必要性。量化全书节奏曲线中高潮点的分布规律和强度变化。分析高潮节奏如何服务于整体阅读体验，形成完整的节奏美感。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]

## 章节间高潮节奏的继承与发展
[分析本章节与前序章节在高潮节奏方面的联系、变化和发展。识别高潮处理的延续性和创新点。评估高潮节奏变化如何服务于情节发展和情感积累。分析作者如何在保持节奏感的同时实现高潮表现的丰富和变化。必须参考前序章节的高潮节奏分析结果，形成连贯、递进的分析体系，而不是将本章节孤立对待。分析内容不限字数，应尽可能详细，提供充分的文本例证和深入分析]""",

        "theme_analysis": """## 主题层次结构与核心价值
[全面分析作品的主题层次结构和核心价值。识别显性主题和隐性主题，区分表层主题和深层主题。评估主题的层次性和复杂度。量化主题元素的分布密度和重要性权重。分析主题间的逻辑关系和层级结构。评估核心主题的价值取向和思想深度，分析其在作品中的统领作用]

## 主题表达技巧与艺术手法
[深入分析主题的表达技巧和艺术手法。评估不同表达方式（直接阐述/象征隐喻/人物体现/情节体现/环境暗示）的使用比例和效果。量化象征系统的密度和一致性。分析主题表达的隐蔽性和渗透性。评估主题表达的艺术性和感染力。参考《百年孤独》的魔幻现实主义手法，分析如何通过艺术表达增强主题的感染力]

## 主题与叙事结构的融合
[评估主题与叙事结构的融合度和互动关系。分析叙事结构如何服务于主题表达。识别关键情节点与主题的呼应关系。量化主题在不同叙事阶段的表现强度变化。分析主题如何影响情节走向和结构设计。评估主题与结构的有机统一性，避免"贴标签"式的生硬主题]

## 主题与人物系统的互动
[分析主题与人物系统的互动关系。评估主要人物如何体现和发展主题。识别人物成长轨迹与主题发展的对应关系。量化不同角色对主题的贡献度和表现方式。分析人物冲突如何凸显主题矛盾。评估人物系统如何立体呈现主题的多面性和复杂性]

## 主题的思想深度与哲学内涵
[全面评估主题的思想深度和哲学内涵。分析主题在哲学思考、社会批判、人性探索等方面的深度。评估主题的原创性和思想价值。量化哲理性内容的分布密度和表达方式。分析主题如何超越故事表层，触及普遍人性和社会本质。参考《三体》的哲学思考深度，评估作品主题的思想高度]

## 主题的时代性与普遍性平衡
[分析主题的时代性与普遍性平衡。评估主题对当代社会问题/价值观的回应程度。识别主题中的时代特征和永恒价值。量化时代元素与普遍元素的比例。分析主题如何在反映时代特征的同时，触及永恒的人性和社会议题。评估主题的长期生命力和跨时代价值]

## 主题与读者共鸣机制
[深入分析主题与读者的共鸣机制。识别主题中可能引起不同类型读者共鸣的情感点和认知点。评估主题的现实意义和情感价值。量化共鸣点的分布密度和强度。分析主题如何通过共鸣建立与读者的深层连接。评估主题对读者世界观/价值观的潜在影响]

## 主题创新与文学传统
[评估主题在创新性和文学传统中的定位。分析主题与同类作品主题的共性和差异。识别主题处理的创新点和突破性。量化创新元素的比重和重要性。分析主题如何在尊重文学传统的基础上实现创新发展。评估主题创新对作品独特价值的贡献]

## 主题统一性与多元性平衡
[分析作品主题的统一性与多元性平衡。评估多元主题之间的有机联系和内在逻辑。识别主题系统的核心与边缘关系。量化不同主题元素的协调度和冲突度。分析如何在保持主题多元性的同时维持整体统一性。评估主题系统的完整性和平衡性]""",

        "chapter_outline": """## 主要内容
[全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。

内容不设字数限制，越详细越好。严格禁止简短概括或总结性描述。必须以生动、细致、具体的小说叙述方式重现章节内容，让读者仿佛亲历其境。必须包含以下所有要素，并对每个要素进行极其详尽的描述：

1. 详细的场景描述：
   - 以原文的风格描述每个场景的环境、氛围和背景，包括光线、声音、气味等感官细节
   - 生动描绘场景中的物品、布置和空间关系，提供具体的视觉画面
   - 细致刻画场景的时间特征（如白天/黑夜、季节、天气等）及其变化
   - 深入表现场景的情绪氛围和给人的整体感受，以及场景如何影响人物情绪

2. 完整的事件过程：
   - 按照发生顺序详细描述每个事件的起因、经过和结果，不遗漏任何重要细节
   - 以原文的叙事风格描述事件中的具体动作和过程，包括动作的速度、力度和精确描述
   - 生动再现事件的节奏和紧张程度，以及情绪变化曲线
   - 深入分析事件对情节发展的推动作用和对人物的影响

3. 人物言行与心理：
   - 完整记录主要人物的对话内容，尽可能使用原文中的重要对话，保留对话的语气和特点
   - 以原文的风格详细描述人物的动作、表情和肢体语言，包括微小的表情变化和身体动作
   - 深入描述人物的心理活动、情感变化和内心冲突，展现人物的思想过程
   - 生动描述人物之间的互动和关系动态，包括潜在的情感变化和关系发展

4. 情节转折与冲突：
   - 以原文的叙事风格详细描述章节中的每个转折点和冲突，包括冲突的形成、发展和解决过程
   - 生动再现转折点前后的情节变化和人物反应
   - 深入分析冲突和转折对人物和故事发展的影响
   - 细致描绘冲突的紧张程度和情感强度

5. 伏笔与暗示：
   - 以原文的风格指出章节中埋下的伏笔和暗示，以及它们可能的未来发展方向
   - 生动描述伏笔的埋设方式和隐蔽程度
   - 深入分析这些伏笔如何与整体故事相连接

必须使用生动的语言、丰富的形容词和具体的细节描述，避免抽象概括。必须包含原文中的关键对话和场景描写，保留原文的风格和语气。必须按照时间顺序或情节发展顺序进行叙述，确保叙述的连贯性和完整性。

这部分内容应该尽可能详细，不受任何字数限制，使读者能够完全沉浸在章节的故事中，仿佛亲自经历了整个章节的内容。必须完全模仿原文的风格和语气进行叙述，而不是用分析报告的语言。]

## 章节结构与内容分布
[详细分析作品的章节结构和内容分布。列出每个章节的主要内容和功能，评估章节之间的连贯性和平衡性。分析章节如何组织成更大的结构单元（如卷、部分或阶段），以及这些结构单元如何服务于整体叙事。量化不同类型内容（如行动场景、对话、内心描写、环境描写等）在各章节中的分布比例。评估章节结构的节奏感和读者体验]

## 整体结构与架构设计
[全面分析作品的整体结构与架构设计。评估三幕结构/五幕结构/英雄旅程等经典结构模式的应用情况。量化结构完整度和平衡性（开端15%/发展70%/结局15%）。分析结构创新点和变异处理。评估结构如何服务于主题表达和情感传递。提供具体的结构图表，标明主要情节点在整体结构中的位置]

## 情节线设计与管理
[深入分析作品的情节线设计与管理。识别并详述主线与支线的数量、内容和层级关系。量化情节线的完整度和收束度。评估情节线交织技巧和节奏控制。分析各情节线如何服务于人物发展和主题表达。特别关注情节线之间的连接点和交叉点，以及它们如何相互影响和推动整体故事发展]

## 叙事节奏与高潮设计
[分析作品的叙事节奏与高潮设计。评估节奏变化的规律性和目的性。量化高潮分布密度和强度梯度。分析节奏控制技巧和高潮铺垫手法。评估节奏与高潮设计如何增强读者体验和情感投入。详细分析主要高潮点的构建过程，包括铺垫、爆发和余波，以及它们在整体故事中的作用]

## 人物系统与角色设计
[全面分析作品的人物系统和角色设计。识别主要人物、次要人物和配角的功能和特点。评估人物形象的丰满度和一致性。分析人物关系网络的复杂性和动态变化。量化不同类型人物（如正面角色、反面角色、中性角色）的分布比例。评估人物系统如何服务于主题表达和情节发展]

## 人物弧线与成长设计
[评估作品中人物弧线与成长设计。分析主要人物的起点、转折点和终点设置。量化人物变化的幅度和节奏。评估人物成长与情节发展的匹配度。分析人物弧线如何服务于主题表达和读者共鸣。详细追踪主要人物的成长历程，包括关键决策点、挑战和突破，以及这些经历如何塑造人物性格和命运]

## 主题展开与深化策略
[深入分析作品的主题展开与深化策略。识别核心主题和辅助主题的层级关系。量化主题元素的分布密度和强调程度。分析主题通过情节/人物/对话等不同载体的表达方式。评估主题展开的完整性和说服力。特别关注主题如何随着故事发展而逐渐深化和复杂化，以及作品如何通过不同角度和层次探索核心主题]

## 世界观构建与背景设定
[评估作品的世界观构建与背景设定。分析世界观元素的丰富度、一致性和原创性。量化世界观信息的揭示节奏和密度。评估世界观对故事氛围和读者沉浸感的影响。分析背景设定如何服务于情节发展和主题表达。特别关注世界观的独特元素和规则，以及它们如何影响故事中的人物选择和事件发展]

## 章节内部结构与功能定位
[全面分析章节的内部结构和功能定位。评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性。分析章节的功能类型（推进型/铺垫型/转折型/高潮型/过渡型）及其在整体结构中的作用。量化目标-阻碍-行动单元（G-O-A结构）的完整性，每章应包含完整的G-O-A结构。参考《万相之王》的章节模板，评估章节结构如何服务于整体叙事]

## 章节间关系与叙事连贯性
[深入分析章节之间的关系和叙事连贯性。评估章节过渡的自然度和技巧多样性。识别章节间的连接方式（直接延续/时空跳转/视角切换/平行叙事）及其效果。量化章节间的信息衔接完整度和逻辑连贯性。分析章节如何承接前章内容，又如何为后续章节铺垫。评估章节在整体叙事链中的位置和作用]

## 读者体验与期待管理
[分析作品对读者体验和期待管理的设计。评估如何吸引和维持读者兴趣，如何管理读者期待。分析章末钩子的类型和强度。量化悬念的设置密度和解答比例。评估在情感共鸣、知识满足和娱乐价值等方面的表现。分析如何通过这些设计增强读者的阅读体验和持续阅读动力]"""
    }

    # 如果没有特定维度的分析提示词，返回通用分析提示词
    default_prompts = """## 整体特点分析
[分析文本在该维度上的总体表现和特点]

## 具体表现研究
[找出文本中该维度的具体表现和例子]

## 变化规律识别
[分析该维度在文本中的变化规律和模式]

## 效果评估测量
[评估该维度的处理对读者体验的影响]

## 创新点提取
[识别作者在该维度上的创新或独特之处]"""

    return prompts.get(dimension, default_prompts)

# 示例用法
if __name__ == "__main__":
    # 生成整本书分析的分析结果模板
    book_template = generate_dimension_analysis_template(
        dimension="language_style",
        title="测试小说"
    )
    print("整本书分析模板:")
    print(book_template)
    print("\n" + "-"*50 + "\n")

    # 生成章节分析的分析结果模板
    chapter_template = generate_dimension_analysis_template(
        dimension="language_style",
        title="测试小说",
        is_chapter=True,
        chapter_title="第一章 开始"
    )
    print("章节分析模板:")
    print(chapter_template)
