{% extends "base.html" %}

{% block title %}{{ novel.title }} - {{ dimension }} 分析过程查看器{% endblock %}

{% block extra_css %}
<style>
    .process-viewer-container {
        margin-top: 20px;
    }

    .process-navigation {
        position: sticky;
        top: 20px;
    }

    .process-summary-card {
        margin-bottom: 20px;
    }

    .summary-value {
        font-weight: bold;
    }

    .process-filter-container {
        margin-bottom: 20px;
    }

    .process-content {
        margin-bottom: 30px;
    }

    .timeline-container {
        position: relative;
        padding-left: 20px;
    }

    .timeline-node {
        margin-bottom: 20px;
        position: relative;
    }

    .timeline-node:before {
        content: '';
        position: absolute;
        left: -20px;
        top: 15px;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background-color: #6c757d;
    }

    .timeline-node.init:before {
        background-color: #6c757d;
    }

    .timeline-node.chunk_analysis:before {
        background-color: #007bff;
    }

    .timeline-node.combine:before {
        background-color: #28a745;
    }

    .timeline-node.finalize:before {
        background-color: #dc3545;
    }

    .timeline-node.error:before {
        background-color: #dc3545;
        animation: pulse 2s infinite;
    }

    .timeline-card {
        border-left: 4px solid #6c757d;
    }

    .timeline-node.init .timeline-card {
        border-left-color: #6c757d;
    }

    .timeline-node.chunk_analysis .timeline-card {
        border-left-color: #007bff;
    }

    .timeline-node.combine .timeline-card {
        border-left-color: #28a745;
    }

    .timeline-node.finalize .timeline-card {
        border-left-color: #dc3545;
    }

    .timeline-node.error .timeline-card {
        border-left-color: #dc3545;
    }

    .stage-badge {
        padding: 5px 10px;
        border-radius: 4px;
        color: white;
    }

    .process-metadata {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .process-content-toggle {
        cursor: pointer;
    }

    .process-content-body {
        max-height: 300px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
    }

    .pagination-container {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
        }
    }

    /* 优化滚动性能 */
    .timeline-container {
        will-change: transform;
    }

    /* 延迟加载指示器 */
    .loading-indicator {
        text-align: center;
        padding: 20px;
        color: #6c757d;
    }

    /* 内存优化提示 */
    .memory-optimization-notice {
        background-color: #e9f7ef;
        border-left: 4px solid #28a745;
        padding: 10px 15px;
        margin-bottom: 20px;
        font-size: 0.9rem;
    }

    /* 分析过程重要提示 */
    .process-explanation {
        background-color: #f8f9fa;
        border-left: 4px solid #17a2b8;
        padding: 15px;
        margin-bottom: 20px;
        font-size: 0.9rem;
    }

    /* 分析卡片样式 */
    .analysis-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .analysis-card-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .analysis-card-body {
        padding: 20px;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }

    /* Markdown内容样式 */
    .markdown-content {
        font-size: 1rem;
        line-height: 1.6;
        color: #343a40;
    }

    .markdown-content h1,
    .markdown-content h2,
    .markdown-content h3 {
        margin-top: 1.5rem;
        margin-bottom: 1rem;
        font-weight: 600;
        color: #212529;
    }

    .markdown-content h1 {
        font-size: 1.75rem;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 0.5rem;
    }

    .markdown-content h2 {
        font-size: 1.5rem;
    }

    .markdown-content h3 {
        font-size: 1.25rem;
    }

    .markdown-content p {
        margin-bottom: 1rem;
    }

    .markdown-content ul,
    .markdown-content ol {
        margin-bottom: 1rem;
        padding-left: 2rem;
    }

    .markdown-content li {
        margin-bottom: 0.5rem;
    }

    .markdown-content blockquote {
        border-left: 4px solid #e9ecef;
        padding-left: 1rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .markdown-content pre {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 4px;
        overflow-x: auto;
        margin-bottom: 1rem;
    }

    .markdown-content code {
        background-color: #f8f9fa;
        padding: 0.2rem 0.4rem;
        border-radius: 4px;
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        font-size: 0.875em;
    }

    .markdown-content table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }

    .markdown-content table th,
    .markdown-content table td {
        padding: 0.75rem;
        border: 1px solid #dee2e6;
    }

    .markdown-content table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-microscope"></i> 分析过程查看器</h2>
                <div>
                    <a href="/novel/{{ novel.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-book"></i> 返回小说详情
                    </a>
                    <a href="/novel/{{ novel.id }}/analysis/{{ dimension }}" class="btn btn-outline-secondary ml-2">
                        <i class="fas fa-chart-pie"></i> 查看分析结果
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> 分析过程与分析结果的区别</h5>
                <p>您正在查看的是<strong>分析过程</strong>，即系统是如何分析文本得出结论的详细思考过程，而非最终的分析结果。</p>
                <p>分析过程展示了AI如何理解文本、识别特征、进行推理并形成结论的完整思路。如需查看最终结论，请点击上方的"查看分析结果"按钮。</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="card process-navigation">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-map"></i> 导航</h5>
                </div>
                <div class="card-body">
                    <div class="process-summary-card">
                        <h6><i class="fas fa-info-circle"></i> 小说信息</h6>
                        <p><strong>标题:</strong> {{ novel.title }}</p>
                        {% if novel.author %}
                        <p><strong>作者:</strong> {{ novel.author }}</p>
                        {% endif %}
                        <p><strong>分析维度:</strong> {{ dimension }}</p>
                        <p><strong>总字数:</strong> <span id="wordCountValue">加载中...</span></p>
                        <p><strong>创建时间:</strong> <span id="createdAtValue">加载中...</span></p>
                        <p><strong>处理时间:</strong> <span id="processingTimeValue">加载中...</span></p>
                    </div>

                    <div class="process-filter-container">
                        <h6><i class="fas fa-filter"></i> 过滤选项</h6>
                        <div class="form-group">
                            <label for="stage-filter">处理阶段:</label>
                            <select class="form-control" id="stage-filter">
                                <option value="all">所有阶段</option>
                                <option value="init">初始化阶段</option>
                                <option value="chunk_analysis">分块分析阶段</option>
                                <option value="combine">结果合并阶段</option>
                                <option value="finalize">最终处理阶段</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="process-search">搜索分析过程:</label>
                            <input type="text" class="form-control" id="process-search" placeholder="搜索分析过程...">
                        </div>
                    </div>

                    <hr>

                    <div class="view-options">
                        <h6><i class="fas fa-eye"></i> 视图选项</h6>
                        <button id="toggleView" class="btn btn-outline-primary btn-sm btn-block mb-2">
                            切换到完整过程视图
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-9">
            <div class="memory-optimization-notice">
                <strong>内存优化模式：</strong> 此页面使用延迟加载和分页技术，减少内存占用。分析过程数据存储在外部存储中，按需加载。
            </div>

            <div class="process-explanation">
                <h5><i class="fas fa-brain"></i> 什么是分析过程?</h5>
                <p>分析过程记录了AI思考和推理的完整轨迹，包括：</p>
                <ul>
                    <li>如何理解和解析文本</li>
                    <li>关注哪些文本特征并如何分析这些特征</li>
                    <li>推理逻辑和思考链</li>
                    <li>如何从分析得出最终结论</li>
                </ul>
                <p>这些记录让您能够了解AI是如何一步步分析文本并得出结论的。</p>
            </div>

            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-tasks"></i> 分析过程</h5>
                </div>
                <div class="card-body" id="processViewContainer">
                    <!-- 分析流程视图 -->
                    <div id="timelineView" class="timeline-container">
                        <h5 class="mb-3">分析过程时间线</h5>
                        <div id="processList" class="mb-4">
                            <div class="text-center my-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载分析过程数据，请稍候...</p>
                            </div>
                        </div>

                        <nav aria-label="分析过程分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页将在这里动态生成 -->
                            </ul>
                        </nav>
                    </div>

                    <!-- 分析过程内容区域 -->
                    <div id="contentView" class="process-content" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">分析过程内容</h5>
                                <small class="text-muted">显示完整的思考和推理过程</small>
                            </div>
                            <div class="card-body">
                                <div id="processContent" class="markdown-content">
                                    <!-- 分析过程内容将在这里渲染 -->
                                    <div class="text-center my-5">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="sr-only">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载分析过程内容，请稍候...</p>
                                    </div>
                                </div>
                                <div id="contentPagination" class="mt-4" style="display: none;">
                                    <ul class="pagination justify-content-center">
                                        <!-- 内容分页将在这里动态生成 -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入分析过程查看器脚本 -->
<script src="/static/js/analysis-process-viewer.js"></script>
<!-- 引入分析过程查看器修复脚本 -->
<script src="/static/js/process-viewer-fix.js"></script>
<!-- 引入语言风格分析过程修复脚本 -->
<script src="/static/js/language-style-process-fix.js"></script>
<!-- 引入推理过程修复脚本 -->
<script src="/static/js/reasoning-content-fix.js"></script>
<!-- 引入推理过程紧急修复脚本 -->
<script src="/static/js/reasoning-content-emergency-fix.js" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-emergency-fix.js';"></script>
<!-- 引入推理过程修复脚本V2 -->
<script src="/static/js/reasoning-content-fix-v2.js" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-fix-v2.js';"></script>
<script>
    // 页面加载后初始化分析过程查看器
    document.addEventListener('DOMContentLoaded', function() {
        // 配置参数，不要重新声明processViewerConfig变量
        initProcessViewer({
            novelId: {{ novel.id }},
            dimension: "{{ dimension }}",
            novel: {
                title: "{{ novel.title }}",
                author: "{{ novel.author }}",
                word_count: "{{ novel.word_count }}"
            }
        });

        // 默认显示完整过程视图
        setTimeout(function() {
            const toggleButton = document.getElementById('toggleView');
            if (toggleButton) {
                toggleButton.click();
            }
        }, 1000);

        // 添加错误处理
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message &&
                event.error.message.includes('无法提取分析过程内容')) {
                console.error('捕获到分析过程提取错误:', event.error.message);

                // 尝试使用备用方法加载内容
                if (window.loadProcessContent) {
                    setTimeout(window.loadProcessContent, 500);
                }

                // 阻止错误传播
                event.preventDefault();
                return false;
            }
        }, true);
    });
</script>
{% endblock %}
