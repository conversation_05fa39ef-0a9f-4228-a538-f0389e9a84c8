module.exports = {
  justNow: "now",
  secondsAgo: "{{time}}s",
  aMinuteAgo: "1m",
  minutesAgo: "{{time}}m",
  anHourAgo: "1h",
  hoursAgo: "{{time}}h",
  aDayAgo: "1d",
  daysAgo: "{{time}}d",
  aWeekAgo: "1w",
  weeksAgo: "{{time}}w",
  aMonthAgo: '4w',
  monthsAgo: (months) => `${Math.round(months / 12 * 52)}w`,
  aYearAgo: "1y",
  yearsAgo: "{{time}}y",
  overAYearAgo: "1y+",
  secondsFromNow: "+{{time}}s",
  aMinuteFromNow: "+1m",
  minutesFromNow: "+{{time}}m",
  anHourFromNow: "+1h",
  hoursFromNow: "+{{time}}h",
  aDayFromNow: "+1d",
  daysFromNow: "+{{time}}d",
  aWeekFromNow: "+1w",
  weeksFromNow: "+{{time}}w",
  aMonthFromNow: '+4w',
  monthsFromNow: (months) => `+${Math.round(months / 12 * 52)}w`,
  aYearFromNow: "+1y",
  yearsFromNow: "+{{time}}y",
  overAYearFromNow: "+1y+"
}
