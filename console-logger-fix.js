/**
 * 九猫 - 控制台日志修复脚本
 * 修复控制台日志相关问题，确保错误信息正确显示
 * 版本: 1.0.1
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('控制台日志修复脚本已加载');
    
    // 保存原始的console方法
    const originalConsole = {
        log: console.log,
        warn: console.warn,
        error: console.error,
        info: console.info,
        debug: console.debug
    };
    
    // 日志计数器
    let logCounter = {
        log: 0,
        warn: 0,
        error: 0,
        info: 0,
        debug: 0
    };
    
    // 最大日志数量
    const maxLogs = {
        log: 1000,
        warn: 500,
        error: 300,
        info: 500,
        debug: 300
    };
    
    // 日志历史记录
    const logHistory = {
        log: {},
        warn: {},
        error: {},
        info: {},
        debug: {}
    };
    
    // 配置
    const config = {
        enableTimestamps: true,     // 是否在日志前添加时间戳
        groupSimilarLogs: true,     // 是否对相似日志进行分组
        maxRepeatCount: 5,          // 相同日志最多显示次数
        filterSensitiveInfo: true,  // 是否过滤敏感信息
        logLevel: 'info'            // 日志级别: debug, info, warn, error
    };
    
    // 日志级别映射
    const logLevelMap = {
        debug: 0,
        info: 1,
        warn: 2,
        error: 3
    };
    
    // 当前日志级别
    const currentLogLevel = logLevelMap[config.logLevel] || 1;
    
    // 添加时间戳
    function addTimestamp(args) {
        if (config.enableTimestamps) {
            const now = new Date();
            const timestamp = `[${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}]`;
            return [timestamp, ...args];
        }
        return args;
    }
    
    // 检查是否应该记录该日志
    function shouldLog(level) {
        return logLevelMap[level] >= currentLogLevel;
    }
    
    // 检查是否是重复日志
    function isRepeatedLog(type, args) {
        if (!config.groupSimilarLogs) {
            return false;
        }
        
        // 创建日志ID
        const logId = JSON.stringify(args.map(arg => {
            if (typeof arg === 'object' && arg !== null) {
                return '[object]';
            }
            return String(arg).substring(0, 100);
        }));
        
        // 检查是否存在于历史记录中
        if (logHistory[type][logId]) {
            logHistory[type][logId].count++;
            return logHistory[type][logId].count > config.maxRepeatCount;
        }
        
        // 记录新日志
        logHistory[type][logId] = { count: 1, timestamp: Date.now() };
        return false;
    }
    
    // 过滤敏感信息
    function filterSensitiveInfo(args) {
        if (!config.filterSensitiveInfo) {
            return args;
        }
        
        return args.map(arg => {
            if (typeof arg === 'string') {
                // 过滤密码、token等敏感信息
                return arg.replace(/password["']?\s*[:=]\s*["']([^"']+)["']/gi, 'password: "***"')
                          .replace(/token["']?\s*[:=]\s*["']([^"']+)["']/gi, 'token: "***"')
                          .replace(/api[-_]?key["']?\s*[:=]\s*["']([^"']+)["']/gi, 'api-key: "***"');
            }
            return arg;
        });
    }
    
    // 重写console方法
    console.log = function(...args) {
        if (!shouldLog('debug')) return;
        
        logCounter.log++;
        if (logCounter.log > maxLogs.log) {
            if (logCounter.log === maxLogs.log + 1) {
                originalConsole.warn(`日志太多 (${maxLogs.log}+)，停止输出详细日志以避免性能问题`);
            }
            return;
        }
        
        if (isRepeatedLog('log', args)) return;
        
        const filteredArgs = filterSensitiveInfo(args);
        const timestampedArgs = addTimestamp(filteredArgs);
        originalConsole.log.apply(console, timestampedArgs);
    };
    
    console.warn = function(...args) {
        if (!shouldLog('warn')) return;
        
        logCounter.warn++;
        if (logCounter.warn > maxLogs.warn) {
            if (logCounter.warn === maxLogs.warn + 1) {
                originalConsole.warn(`警告太多 (${maxLogs.warn}+)，停止输出详细警告以避免性能问题`);
            }
            return;
        }
        
        if (isRepeatedLog('warn', args)) return;
        
        const filteredArgs = filterSensitiveInfo(args);
        const timestampedArgs = addTimestamp(filteredArgs);
        originalConsole.warn.apply(console, timestampedArgs);
    };
    
    console.error = function(...args) {
        if (!shouldLog('error')) return;
        
        logCounter.error++;
        if (logCounter.error > maxLogs.error) {
            if (logCounter.error === maxLogs.error + 1) {
                originalConsole.error(`错误太多 (${maxLogs.error}+)，停止输出详细错误以避免性能问题`);
            }
            return;
        }
        
        if (isRepeatedLog('error', args)) return;
        
        const filteredArgs = filterSensitiveInfo(args);
        const timestampedArgs = addTimestamp(filteredArgs);
        originalConsole.error.apply(console, timestampedArgs);
    };
    
    console.info = function(...args) {
        if (!shouldLog('info')) return;
        
        logCounter.info++;
        if (logCounter.info > maxLogs.info) {
            if (logCounter.info === maxLogs.info + 1) {
                originalConsole.info(`信息太多 (${maxLogs.info}+)，停止输出详细信息以避免性能问题`);
            }
            return;
        }
        
        if (isRepeatedLog('info', args)) return;
        
        const filteredArgs = filterSensitiveInfo(args);
        const timestampedArgs = addTimestamp(filteredArgs);
        originalConsole.info.apply(console, timestampedArgs);
    };
    
    console.debug = function(...args) {
        if (!shouldLog('debug')) return;
        
        logCounter.debug++;
        if (logCounter.debug > maxLogs.debug) {
            if (logCounter.debug === maxLogs.debug + 1) {
                originalConsole.debug(`调试信息太多 (${maxLogs.debug}+)，停止输出详细调试信息以避免性能问题`);
            }
            return;
        }
        
        if (isRepeatedLog('debug', args)) return;
        
        const filteredArgs = filterSensitiveInfo(args);
        const timestampedArgs = addTimestamp(filteredArgs);
        originalConsole.debug.apply(console, timestampedArgs);
    };
    
    console.log('控制台日志修复脚本加载完成');
})();
