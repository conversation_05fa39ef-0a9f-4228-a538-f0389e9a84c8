"""
九猫系统监控模块
提供系统资源监控、API调用统计、数据库连接池状态监控和告警功能
"""
import os
import time
import psutil
import logging
# import platform
import threading
import smtplib
import json
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
from collections import deque
from sqlalchemy import func, desc
# from sqlalchemy.pool import QueuePool

import config
from src.db.connection import Session, engine
from src.models.analysis_result import AnalysisResult
from src.models.api_log import ApiLog
from src.models.system_alert import SystemAlert
from src.models.system_metric import SystemMetric

# 配置日志
logger = logging.getLogger(__name__)

# 内存中的指标存储，用于实时监控
class MetricsStore:
    """内存中的指标存储，用于实时监控"""

    def __init__(self, max_history=1440):  # 默认存储24小时的数据（每分钟一个点）
        self.max_history = max_history
        self.cpu_history = deque(maxlen=max_history)
        self.memory_history = deque(maxlen=max_history)
        self.disk_history = deque(maxlen=max_history)
        self.api_calls_history = deque(maxlen=max_history)
        self.db_connections_history = deque(maxlen=max_history)
        self.last_updated = datetime.now()

        # 告警配置
        self.alert_thresholds = {
            'cpu_usage': 90,  # CPU使用率超过90%触发告警
            'memory_usage': 85,  # 内存使用率超过85%触发告警
            'disk_usage': 90,  # 磁盘使用率超过90%触发告警
            'db_connections': 80,  # 数据库连接池使用率超过80%触发告警
            'api_error_rate': 10  # API错误率超过10%触发告警
        }

        # 告警状态（避免重复告警）
        self.alert_status = {
            'cpu_usage': False,
            'memory_usage': False,
            'disk_usage': False,
            'db_connections': False,
            'api_error_rate': False
        }

        # 启动监控线程
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

    def _monitor_loop(self):
        """监控循环，定期收集系统指标"""
        while self.monitoring_active:
            try:
                # 收集系统指标
                self.collect_metrics()

                # 检查告警条件
                self.check_alert_conditions()

                # 每60秒收集一次
                time.sleep(60)
            except Exception as e:
                logger.error(f"监控循环出错: {str(e)}")
                time.sleep(60)  # 出错后等待一分钟再重试

    def collect_metrics(self):
        """收集当前系统指标"""
        timestamp = datetime.now()

        # 收集CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)

        # 收集内存使用率
        memory = psutil.virtual_memory()
        memory_usage = memory.percent

        # 收集磁盘使用率
        disk = psutil.disk_usage('/')
        disk_usage = disk.percent

        # 收集API调用统计
        api_calls = self._get_api_call_stats()

        # 收集数据库连接池状态
        db_connections = self._get_db_connection_stats()

        # 添加到历史记录
        self.cpu_history.append((timestamp, cpu_usage))
        self.memory_history.append((timestamp, memory_usage))
        self.disk_history.append((timestamp, disk_usage))
        self.api_calls_history.append((timestamp, api_calls))
        self.db_connections_history.append((timestamp, db_connections))

        # 更新最后更新时间
        self.last_updated = timestamp

        # 保存到数据库
        self._save_metrics_to_db(timestamp, cpu_usage, memory_usage, disk_usage, api_calls, db_connections)

    def _get_api_call_stats(self):
        """获取API调用统计"""
        try:
            session = Session()

            # 获取最近1小时的API调用记录
            one_hour_ago = datetime.now() - timedelta(hours=1)

            # 总调用次数
            total_calls = session.query(func.count(ApiLog.id)).filter(
                ApiLog.timestamp >= one_hour_ago
            ).scalar() or 0

            # 成功调用次数
            success_calls = session.query(func.count(ApiLog.id)).filter(
                ApiLog.timestamp >= one_hour_ago,
                ApiLog.status_code >= 200,
                ApiLog.status_code < 300
            ).scalar() or 0

            # 错误调用次数
            error_calls = session.query(func.count(ApiLog.id)).filter(
                ApiLog.timestamp >= one_hour_ago,
                ApiLog.status_code >= 400
            ).scalar() or 0

            # 超时调用次数
            timeout_calls = session.query(func.count(ApiLog.id)).filter(
                ApiLog.timestamp >= one_hour_ago,
                ApiLog.error_type == 'timeout'
            ).scalar() or 0

            # 平均响应时间
            avg_response_time = session.query(func.avg(ApiLog.response_time)).filter(
                ApiLog.timestamp >= one_hour_ago
            ).scalar() or 0

            return {
                'total': total_calls,
                'success': success_calls,
                'error': error_calls,
                'timeout': timeout_calls,
                'avg_response_time': round(avg_response_time, 2)
            }
        except Exception as e:
            logger.error(f"获取API调用统计时出错: {str(e)}")
            return {'total': 0, 'success': 0, 'error': 0, 'timeout': 0, 'avg_response_time': 0}
        finally:
            session.close()

    def _get_db_connection_stats(self):
        """获取数据库连接池状态"""
        try:
            # 获取SQLAlchemy连接池状态
            if hasattr(engine.pool, '_pool'):
                pool = engine.pool

                # 连接池大小
                pool_size = pool.size()

                # 活跃连接数
                active_connections = pool.checkedout()

                # 溢出连接数
                overflow = pool.overflow()

                # 连接池使用率
                usage_percent = (active_connections / (pool_size + overflow)) * 100 if (pool_size + overflow) > 0 else 0

                return {
                    'pool_size': pool_size,
                    'active': active_connections,
                    'overflow': overflow,
                    'usage_percent': round(usage_percent, 2)
                }
            else:
                # 如果无法获取连接池状态，返回默认值
                return {
                    'pool_size': 5,  # 默认连接池大小
                    'active': 0,
                    'overflow': 0,
                    'usage_percent': 0
                }
        except Exception as e:
            logger.error(f"获取数据库连接池状态时出错: {str(e)}")
            return {'pool_size': 5, 'active': 0, 'overflow': 0, 'usage_percent': 0}

    def _save_metrics_to_db(self, timestamp, cpu_usage, memory_usage, disk_usage, api_calls, db_connections):
        """保存指标到数据库"""
        try:
            session = Session()

            # 创建指标记录
            metric = SystemMetric(
                timestamp=timestamp,
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                api_calls_total=api_calls['total'],
                api_calls_success=api_calls['success'],
                api_calls_error=api_calls['error'],
                api_calls_timeout=api_calls['timeout'],
                api_avg_response_time=api_calls['avg_response_time'],
                db_pool_size=db_connections['pool_size'],
                db_active_connections=db_connections['active'],
                db_overflow=db_connections['overflow'],
                db_usage_percent=db_connections['usage_percent']
            )

            session.add(metric)
            session.commit()
        except Exception as e:
            logger.error(f"保存系统指标到数据库时出错: {str(e)}")
            session.rollback()
        finally:
            session.close()

    def check_alert_conditions(self):
        """检查是否需要触发告警"""
        if not hasattr(config, 'ALERTS_ENABLED') or not config.ALERTS_ENABLED:
            return

        # 获取最新指标
        if not self.cpu_history or not self.memory_history or not self.disk_history:
            return

        _, cpu_usage = self.cpu_history[-1]
        _, memory_usage = self.memory_history[-1]
        _, disk_usage = self.disk_history[-1]

        # 获取最新的数据库连接池状态
        if self.db_connections_history:
            _, db_connections = self.db_connections_history[-1]
            db_usage_percent = db_connections['usage_percent']
        else:
            db_usage_percent = 0

        # 获取最新的API调用统计
        if self.api_calls_history:
            _, api_calls = self.api_calls_history[-1]
            api_error_rate = (api_calls['error'] / api_calls['total'] * 100) if api_calls['total'] > 0 else 0
        else:
            api_error_rate = 0

        # 检查CPU使用率
        self._check_and_alert('cpu_usage', cpu_usage, self.alert_thresholds['cpu_usage'],
                             f"CPU使用率过高: {cpu_usage}%", "critical")

        # 检查内存使用率
        self._check_and_alert('memory_usage', memory_usage, self.alert_thresholds['memory_usage'],
                             f"内存使用率过高: {memory_usage}%", "critical")

        # 检查磁盘使用率
        self._check_and_alert('disk_usage', disk_usage, self.alert_thresholds['disk_usage'],
                             f"磁盘使用率过高: {disk_usage}%", "warning")

        # 检查数据库连接池使用率
        self._check_and_alert('db_connections', db_usage_percent, self.alert_thresholds['db_connections'],
                             f"数据库连接池使用率过高: {db_usage_percent}%", "warning")

        # 检查API错误率
        self._check_and_alert('api_error_rate', api_error_rate, self.alert_thresholds['api_error_rate'],
                             f"API错误率过高: {api_error_rate}%", "warning")

    def _check_and_alert(self, metric_name, current_value, threshold, message, level):
        """检查指标是否超过阈值，并触发告警"""
        # 如果当前值超过阈值，且之前没有触发过告警
        if current_value >= threshold and not self.alert_status[metric_name]:
            # 创建告警
            self._create_alert(message, level)

            # 发送邮件通知（如果启用）
            if hasattr(config, 'EMAIL_ALERTS_ENABLED') and config.EMAIL_ALERTS_ENABLED:
                self._send_alert_email(f"九猫系统告警: {message}", message)

            # 更新告警状态
            self.alert_status[metric_name] = True

        # 如果当前值低于阈值，且之前触发过告警，重置告警状态
        elif current_value < threshold and self.alert_status[metric_name]:
            # 创建恢复告警
            self._create_alert(f"{message.split(':')[0]}已恢复: {current_value}%", "info")

            # 更新告警状态
            self.alert_status[metric_name] = False

    def _create_alert(self, message, level):
        """创建系统告警记录"""
        try:
            session = Session()

            # 创建告警记录
            alert = SystemAlert(
                timestamp=datetime.now(),
                level=level,
                title=message.split(':')[0],
                message=message
            )

            session.add(alert)
            session.commit()

            logger.warning(f"系统告警: {message}")
        except Exception as e:
            logger.error(f"创建系统告警时出错: {str(e)}")
            session.rollback()
        finally:
            session.close()

    def _send_alert_email(self, subject, message):
        """发送告警邮件"""
        if not hasattr(config, 'ALERT_EMAIL_RECIPIENTS') or not config.ALERT_EMAIL_RECIPIENTS:
            return

        try:
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = config.EMAIL_SENDER
            msg['To'] = ', '.join(config.ALERT_EMAIL_RECIPIENTS)
            msg['Subject'] = subject

            # 添加邮件内容
            body = f"""
            <html>
            <body>
                <h2>九猫系统告警</h2>
                <p><strong>时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>告警内容:</strong> {message}</p>
                <p><strong>系统信息:</strong></p>
                <ul>
                    <li>CPU使用率: {self.cpu_history[-1][1]}%</li>
                    <li>内存使用率: {self.memory_history[-1][1]}%</li>
                    <li>磁盘使用率: {self.disk_history[-1][1]}%</li>
                </ul>
                <p>请及时检查系统状态。</p>
            </body>
            </html>
            """

            msg.attach(MIMEText(body, 'html'))

            # 发送邮件
            server = smtplib.SMTP(config.EMAIL_SERVER, config.EMAIL_PORT)
            server.starttls()
            server.login(config.EMAIL_USERNAME, config.EMAIL_PASSWORD)
            server.send_message(msg)
            server.quit()

            logger.info(f"已发送告警邮件: {subject}")
        except Exception as e:
            logger.error(f"发送告警邮件时出错: {str(e)}")

    def get_system_metrics(self):
        """获取当前系统指标"""
        # 获取系统运行时间
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        days, seconds = uptime.days, uptime.seconds
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        uptime_str = f"{days}天 {hours}小时 {minutes}分钟"

        # 获取最新的CPU、内存和磁盘使用率
        cpu_usage = self.cpu_history[-1][1] if self.cpu_history else psutil.cpu_percent(interval=1)
        memory_usage = self.memory_history[-1][1] if self.memory_history else psutil.virtual_memory().percent
        disk_usage = self.disk_history[-1][1] if self.disk_history else psutil.disk_usage('/').percent

        return {
            'cpu_usage': cpu_usage,
            'memory_usage': memory_usage,
            'disk_usage': disk_usage,
            'uptime': uptime_str,
            'last_updated': self.last_updated
        }

    def get_api_stats(self):
        """获取API调用统计"""
        try:
            session = Session()

            # 获取今日调用次数
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_calls = session.query(func.count(ApiLog.id)).filter(
                ApiLog.timestamp >= today
            ).scalar() or 0

            # 获取本周调用次数
            week_start = today - timedelta(days=today.weekday())
            week_calls = session.query(func.count(ApiLog.id)).filter(
                ApiLog.timestamp >= week_start
            ).scalar() or 0

            # 获取本月调用次数
            month_start = today.replace(day=1)
            month_calls = session.query(func.count(ApiLog.id)).filter(
                ApiLog.timestamp >= month_start
            ).scalar() or 0

            # 获取最近24小时的调用次数（按小时分组）
            one_day_ago = datetime.now() - timedelta(days=1)
            hourly_calls = session.query(
                func.date_trunc('hour', ApiLog.timestamp).label('hour'),
                func.count(ApiLog.id).label('count')
            ).filter(
                ApiLog.timestamp >= one_day_ago
            ).group_by('hour').order_by('hour').all()

            # 格式化时间标签和调用次数
            time_labels = []
            call_counts = []

            # 创建24小时的时间点
            for i in range(24):
                hour = (datetime.now() - timedelta(hours=24-i-1)).replace(minute=0, second=0, microsecond=0)
                time_labels.append(hour.strftime('%H:%M'))

                # 查找该小时的调用次数
                count = 0
                for h, c in hourly_calls:
                    if h.hour == hour.hour and h.day == hour.day:
                        count = c
                        break

                call_counts.append(count)

            # 获取成功率和平均响应时间
            recent_calls = session.query(ApiLog).filter(
                ApiLog.timestamp >= one_day_ago
            ).all()

            total_recent = len(recent_calls)
            success_count = sum(1 for log in recent_calls if 200 <= (log.status_code or 0) < 300)
            error_count = sum(1 for log in recent_calls if (log.status_code or 0) >= 400)
            timeout_count = sum(1 for log in recent_calls if log.error_type == 'timeout')

            success_rate = round((success_count / total_recent) * 100, 2) if total_recent > 0 else 100
            avg_response_time = round(sum(log.response_time or 0 for log in recent_calls) / total_recent, 2) if total_recent > 0 else 0

            return {
                'today_calls': today_calls,
                'week_calls': week_calls,
                'month_calls': month_calls,
                'time_labels': time_labels,
                'call_counts': call_counts,
                'success_rate': success_rate,
                'avg_response_time': avg_response_time,
                'success_count': success_count,
                'error_count': error_count,
                'timeout_count': timeout_count
            }
        except Exception as e:
            logger.error(f"获取API统计信息时出错: {str(e)}")
            return {
                'today_calls': 0,
                'week_calls': 0,
                'month_calls': 0,
                'time_labels': [f"{i}:00" for i in range(24)],
                'call_counts': [0] * 24,
                'success_rate': 100,
                'avg_response_time': 0,
                'success_count': 0,
                'error_count': 0,
                'timeout_count': 0
            }
        finally:
            session.close()

    def get_db_stats(self):
        """获取数据库连接池状态"""
        try:
            # 获取最新的数据库连接池状态
            if self.db_connections_history:
                _, db_connections = self.db_connections_history[-1]
            else:
                db_connections = self._get_db_connection_stats()

            # 确定连接池状态
            usage_percent = db_connections['usage_percent']
            if usage_percent >= 90:
                status = 'critical'
            elif usage_percent >= 70:
                status = 'warning'
            else:
                status = 'healthy'

            return {
                'pool_size': db_connections['pool_size'],
                'active_connections': db_connections['active'],
                'overflow': db_connections['overflow'],
                'usage_percent': usage_percent,
                'status': status
            }
        except Exception as e:
            logger.error(f"获取数据库统计信息时出错: {str(e)}")
            return {
                'pool_size': 5,
                'active_connections': 0,
                'overflow': 0,
                'usage_percent': 0,
                'status': 'healthy'
            }

    def get_alerts(self, limit=10):
        """获取最近的系统告警"""
        try:
            session = Session()

            # 获取最近的告警记录
            alerts = session.query(SystemAlert).order_by(
                SystemAlert.timestamp.desc()
            ).limit(limit).all()

            return alerts
        except Exception as e:
            logger.error(f"获取系统告警时出错: {str(e)}")
            return []
        finally:
            session.close()

    def get_alert_settings(self):
        """获取告警设置"""
        return {
            'enabled': hasattr(config, 'ALERTS_ENABLED') and config.ALERTS_ENABLED,
            'email_enabled': hasattr(config, 'EMAIL_ALERTS_ENABLED') and config.EMAIL_ALERTS_ENABLED,
            'thresholds': self.alert_thresholds
        }

    def update_alert_settings(self, settings):
        """更新告警设置"""
        try:
            # 更新告警启用状态
            if 'enabled' in settings:
                config.ALERTS_ENABLED = settings['enabled']

            # 更新邮件告警启用状态
            if 'email_enabled' in settings:
                config.EMAIL_ALERTS_ENABLED = settings['email_enabled']

            # 更新告警阈值
            if 'thresholds' in settings:
                for key, value in settings['thresholds'].items():
                    if key in self.alert_thresholds:
                        self.alert_thresholds[key] = value

            # 保存配置到文件
            self._save_config_to_file()

            return True
        except Exception as e:
            logger.error(f"更新告警设置时出错: {str(e)}")
            return False

    def _save_config_to_file(self):
        """保存配置到文件"""
        try:
            # 创建配置字典
            config_dict = {
                'ALERTS_ENABLED': getattr(config, 'ALERTS_ENABLED', True),
                'EMAIL_ALERTS_ENABLED': getattr(config, 'EMAIL_ALERTS_ENABLED', False),
                'ALERT_THRESHOLDS': self.alert_thresholds
            }

            # 保存到JSON文件
            with open('alert_config.json', 'w') as f:
                json.dump(config_dict, f, indent=4)

            logger.info("已保存告警配置到文件")
        except Exception as e:
            logger.error(f"保存告警配置到文件时出错: {str(e)}")

    def get_logs(self, limit=100, level=None):
        """获取系统日志"""
        logs = []

        try:
            # 读取日志文件
            log_file = 'app.log'
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    lines = f.readlines()

                    # 解析日志行
                    for line in reversed(lines):
                        try:
                            # 解析日志格式: 2023-01-01 12:00:00,000 - name - LEVEL - message
                            parts = line.split(' - ', 3)
                            if len(parts) >= 4:
                                timestamp_str, name, level_str, message = parts

                                # 解析时间戳
                                timestamp = datetime.strptime(timestamp_str.strip(), '%Y-%m-%d %H:%M:%S,%f')

                                # 过滤日志级别
                                if level and level.lower() != level_str.lower():
                                    continue

                                logs.append({
                                    'timestamp': timestamp,
                                    'name': name.strip(),
                                    'level': level_str.lower().strip(),
                                    'message': message.strip()
                                })

                                # 达到限制数量后停止
                                if len(logs) >= limit:
                                    break
                        except Exception as e:
                            # 跳过无法解析的行
                            continue

            return logs
        except Exception as e:
            logger.error(f"获取系统日志时出错: {str(e)}")
            return []

# 创建全局指标存储实例
metrics_store = MetricsStore()

def get_system_metrics():
    """获取系统指标"""
    return metrics_store.get_system_metrics()

def get_api_stats():
    """获取API调用统计"""
    return metrics_store.get_api_stats()

def get_db_stats():
    """获取数据库连接池状态"""
    return metrics_store.get_db_stats()

def get_alerts(limit=10):
    """获取系统告警"""
    return metrics_store.get_alerts(limit)

def get_alert_settings():
    """获取告警设置"""
    return metrics_store.get_alert_settings()

def update_alert_settings(settings):
    """更新告警设置"""
    return metrics_store.update_alert_settings(settings)

def get_logs(limit=100, level=None):
    """获取系统日志"""
    return metrics_store.get_logs(limit, level)

def get_dimension_stats():
    """获取维度分析统计"""
    try:
        session = Session()

        # 获取各维度的分析次数
        dimension_counts = session.query(
            AnalysisResult.dimension,
            func.count(AnalysisResult.id).label('count')
        ).group_by(
            AnalysisResult.dimension
        ).order_by(
            desc('count')
        ).all()

        # 格式化结果
        labels = [d[0] for d in dimension_counts]
        counts = [d[1] for d in dimension_counts]

        return {
            'labels': labels,
            'counts': counts
        }
    except Exception as e:
        logger.error(f"获取维度统计信息时出错: {str(e)}")
        return {
            'labels': [],
            'counts': []
        }
    finally:
        session.close()
