/**
 * 九猫系统popular_tropes布局修复脚本
 * 用于修复popular_tropes相关布局问题
 */

(function() {
    console.log('[popular_tropes修复] 初始化...');

    // 等待DOM加载完成
    function onDOMReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    // 修复popular_tropes布局
    function fixPopularTropesLayout() {
        console.log('[popular_tropes修复] 开始修复popular_tropes布局...');

        // 添加CSS样式
        addPopularTropesStyles();

        // 修复popular_tropes容器
        fixPopularTropesContainers();

        // 修复popular_tropes设置区域
        fixPopularTropesSettings();

        console.log('[popular_tropes修复] popular_tropes布局修复完成');
    }

    // 添加CSS样式
    function addPopularTropesStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            /* popular_tropes容器样式 */
            .popular_tropes-container {
                display: flex;
                flex-direction: column;
                gap: 15px;
                padding: 20px;
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                margin-bottom: 20px;
            }
            
            /* popular_tropes标题样式 */
            .popular_tropes-container h3 {
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                margin-top: 0;
            }
            
            /* popular_tropes设置区域样式 */
            .popular_tropes-settings {
                display: flex;
                flex-direction: column;
                gap: 10px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                margin-bottom: 15px;
            }
            
            /* popular_tropes设置标题样式 */
            .popular_tropes-settings h4 {
                color: #2c3e50;
                margin-top: 0;
                margin-bottom: 10px;
            }
            
            /* popular_tropes列表样式 */
            .popular_tropes-list {
                list-style-type: disc;
                padding-left: 20px;
                margin: 0;
            }
            
            .popular_tropes-list li {
                margin-bottom: 8px;
                line-height: 1.5;
            }
            
            /* popular_tropes内容区域样式 */
            .popular_tropes-content {
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                margin-bottom: 15px;
            }
            
            /* popular_tropes表单样式 */
            .popular_tropes-form {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .popular_tropes-form .form-group {
                margin-bottom: 10px;
            }
            
            .popular_tropes-form label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            
            .popular_tropes-form input,
            .popular_tropes-form textarea,
            .popular_tropes-form select {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            
            .popular_tropes-form button {
                align-self: flex-start;
                padding: 8px 16px;
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            
            .popular_tropes-form button:hover {
                background-color: #2980b9;
            }
        `;
        
        document.head.appendChild(styleElement);
        console.log('[popular_tropes修复] 添加popular_tropes样式');
    }

    // 修复popular_tropes容器
    function fixPopularTropesContainers() {
        // 查找popular_tropes相关容器
        const containers = document.querySelectorAll('.popular_tropes-container, [data-dimension="popular_tropes"]');
        
        if (containers.length > 0) {
            console.log(`[popular_tropes修复] 找到${containers.length}个popular_tropes容器`);
            
            containers.forEach(container => {
                // 添加类名
                container.classList.add('popular_tropes-container');
                
                // 确保内容正确对齐
                container.style.display = 'flex';
                container.style.flexDirection = 'column';
                container.style.gap = '15px';
                
                // 修复文本溢出
                const textElements = container.querySelectorAll('p, li, h3, h4');
                textElements.forEach(el => {
                    el.style.maxWidth = '100%';
                    el.style.wordBreak = 'break-word';
                });
                
                // 修复列表样式
                const lists = container.querySelectorAll('ul');
                lists.forEach(list => {
                    list.classList.add('popular_tropes-list');
                });
                
                // 修复标题样式
                const titles = container.querySelectorAll('h3, h4');
                titles.forEach(title => {
                    if (title.textContent.includes('popular_tropes') || title.textContent.includes('热门桥段')) {
                        title.textContent = '热门桥段分析';
                    }
                });
            });
        } else {
            console.log('[popular_tropes修复] 未找到popular_tropes容器');
        }
    }

    // 修复popular_tropes设置区域
    function fixPopularTropesSettings() {
        // 查找popular_tropes设置区域
        const settingsContainers = document.querySelectorAll('.popular_tropes-settings, [data-settings="popular_tropes"]');
        
        if (settingsContainers.length > 0) {
            console.log(`[popular_tropes修复] 找到${settingsContainers.length}个popular_tropes设置区域`);
            
            settingsContainers.forEach(container => {
                // 添加类名
                container.classList.add('popular_tropes-settings');
                
                // 确保内容正确对齐
                container.style.display = 'flex';
                container.style.flexDirection = 'column';
                container.style.gap = '10px';
                
                // 修复表单样式
                const forms = container.querySelectorAll('form');
                forms.forEach(form => {
                    form.classList.add('popular_tropes-form');
                });
            });
        } else {
            console.log('[popular_tropes修复] 未找到popular_tropes设置区域');
        }
    }

    // 监听DOM变化，动态修复popular_tropes布局
    function observeDOM() {
        const observer = new MutationObserver(mutations => {
            let shouldFixLayout = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        
                        if (node.nodeType === 1) { // 元素节点
                            if (node.classList && (
                                node.classList.contains('popular_tropes-container') || 
                                node.getAttribute('data-dimension') === 'popular_tropes' ||
                                node.classList.contains('popular_tropes-settings') ||
                                node.getAttribute('data-settings') === 'popular_tropes'
                            )) {
                                shouldFixLayout = true;
                                break;
                            } else if (node.querySelector && (
                                node.querySelector('.popular_tropes-container, [data-dimension="popular_tropes"], .popular_tropes-settings, [data-settings="popular_tropes"]')
                            )) {
                                shouldFixLayout = true;
                                break;
                            }
                        }
                    }
                }
            });
            
            if (shouldFixLayout) {
                fixPopularTropesLayout();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('[popular_tropes修复] DOM观察器已启动');
    }

    // 初始化
    onDOMReady(() => {
        fixPopularTropesLayout();
        observeDOM();
    });
})();
