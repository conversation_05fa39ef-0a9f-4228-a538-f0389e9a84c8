<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON.parse错误修复测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h2 {
            margin-top: 0;
            color: #444;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 4px solid #ccc;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-left-color: #4CAF50;
        }
        .error {
            border-left-color: #F44336;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        .log-container {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JSON.parse错误修复测试</h1>
        
        <div class="test-section">
            <h2>测试1: 位置4476错误</h2>
            <p>模拟位置4476处的JSON.parse错误 (Expected ',' or '}' after property value)</p>
            <button id="test1">运行测试</button>
            <div id="result1" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>测试2: character_relationships错误</h2>
            <p>模拟character_relationships相关的JSON.parse错误</p>
            <button id="test2">运行测试</button>
            <div id="result2" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>测试3: 通用JSON修复</h2>
            <p>测试通用JSON修复功能</p>
            <button id="test3">运行测试</button>
            <div id="result3" class="result"></div>
        </div>
        
        <h2>控制台日志</h2>
        <div id="log-container" class="log-container"></div>
    </div>
    
    <!-- 加载修复脚本 -->
    <script src="position-4476-fix.js"></script>
    
    <script>
        // 重写console.log和console.error，将输出显示在页面上
        (function() {
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;
            const logContainer = document.getElementById('log-container');
            
            console.log = function() {
                const args = Array.from(arguments);
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
                ).join(' ');
                
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.textContent = '[LOG] ' + message;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
                
                originalLog.apply(console, arguments);
            };
            
            console.error = function() {
                const args = Array.from(arguments);
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
                ).join(' ');
                
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.style.color = 'red';
                logEntry.textContent = '[ERROR] ' + message;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
                
                originalError.apply(console, arguments);
            };
            
            console.warn = function() {
                const args = Array.from(arguments);
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
                ).join(' ');
                
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.style.color = 'orange';
                logEntry.textContent = '[WARN] ' + message;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
                
                originalWarn.apply(console, arguments);
            };
        })();
        
        // 测试1: 位置4476错误
        document.getElementById('test1').addEventListener('click', function() {
            const resultElement = document.getElementById('result1');
            
            try {
                console.log('开始测试1: 位置4476错误');
                
                // 创建一个在位置4476附近有错误的JSON字符串
                let jsonStr = '{"dimension":"character_relationships","content":"# 人物关系分析\\n\\n## 主要人物\\n\\n1. **林小鱼** - 主角，穿越到修仙界的现代女性\\n2. **赵掌门** - 清风派掌门，收留林小鱼的人\\n3. **张长老** - 清风派长老，对林小鱼有敌意\\n\\n## 人物关系分析\\n\\n### 林小鱼与赵掌门\\n\\n林小鱼与赵掌门之间形成了一种特殊的师徒关系。赵掌门作为清风派掌门，不仅收留了突然出现的林小鱼，还决定亲自收她为徒。这种关系具有以下特点：\\n\\n- **保护与依赖**：赵掌门对林小鱼表现出明显的保护意愿，在张长老质疑时坚定地维护她\\n- **欣赏与期待**：赵掌门似乎在林小鱼身上看到了某种潜力或特质，这促使他破例收她为徒\\n- **权威与服从**：典型的师徒关系，赵掌门拥有绝对权威，林小鱼需要学习服从\\n\\n### 林小鱼与张长老\\n\\n林小鱼与张长老之间存在明显的紧张关系，表现为：\\n\\n- **怀疑与敌意**：张长老对林小鱼的来历表示强烈怀疑，认为她可能是"妖女"或敌对门派的奸细\\n- **权力冲突**：张长老质疑掌门的决定，但最终不得不服从，这种权力冲突以林小鱼为焦点\\n- **潜在威胁**：张长老可能成为林小鱼在门派中的主要阻力或敌人\\n\\n### 赵掌门与张长老\\n\\n赵掌门与张长老之间展现了典型的门派内部权力结构：\\n\\n- **决策与质疑**：作为掌门，赵掌门拥有最终决策权；作为长老，张长老有权提出质疑\\n- **尊重与服从**：尽管有分歧，张长老最终还是尊重并服从了掌门的决定\\n- **信任与怀疑**：两人对待外来者的态度存在差异，反映了性格和处事方式的不同\\n\\n## 关系网络特点\\n\\n1. **三角关系结构**：形成了典型的三角关系，林小鱼处于核心位置\\n2. **权力不平衡**：三人之间存在明显的权力层级，赵掌门>张长老>林小鱼\\n3. **潜在冲突**：张长老的怀疑态度埋下了未来冲突的种子\\n4. **双重勾连**：林小鱼同时与赵掌门和张长老建立了联系，但性质完全不同\\n\\n## 关系发展预测\\n\\n1. **师徒情深**：林小鱼与赵掌门的师徒关系可能进一步深化，发展出父女般的情感\\n2. **敌对转变**：林小鱼可能通过表现赢得张长老的认可，或者两人的敌对关系可能加剧\\n3. **忠诚考验**：林小鱼的身份之谜可能成为考验赵掌门对她信任的关键事件\\n4. **派系形成**：围绕林小鱼的去留可能在门派内部形成支持与反对的两派\\n\\n## 关系张力与趣味性\\n\\n1. **身份之谜**：林小鱼的真实身份成为关系发展的核心悬念\\n2. **权力斗争**：门派内部的微妙权力斗争为故事增添了政治色彩\\n3. **文化冲突**：现代人林小鱼与修仙世界的文化差异可能带来有趣的互动\\n4. **成长故事**：林小鱼需要在这复杂的关系网络中学习生存，展现了成长故事的元素\\n\\n## 总结\\n\\n第一章中的人物关系虽然人物较少，但已经建立了一个结构清晰、张力十足的关系网络。林小鱼作为穿越者处于关系网络的核心，她与赵掌门的师徒关系以及与张长老的对立关系，共同构成了故事发展的基础人际结构。这种结构为后续剧情的展开提供了丰富的可能性，特别是在身份揭示、忠诚考验、权力斗争等方面"metadata":{"processing_time":15.7,"chunk_count":1,"api_calls":1,"tokens_used":1024,"cost":0.01024}}';
                
                // 在位置4476附近（"特别是在身份揭示、忠诚考验、权力斗争等方面"后面）缺少逗号
                
                // 尝试解析
                const result = JSON.parse(jsonStr);
                
                // 显示结果
                resultElement.textContent = '解析成功！结果：\n' + JSON.stringify(result, null, 2).substring(0, 200) + '...';
                resultElement.classList.add('success');
                resultElement.classList.remove('error');
                
                console.log('测试1完成：成功修复位置4476错误');
            } catch (e) {
                resultElement.textContent = '解析失败：' + e.message;
                resultElement.classList.add('error');
                resultElement.classList.remove('success');
                
                console.error('测试1失败：', e.message);
            }
        });
        
        // 测试2: character_relationships错误
        document.getElementById('test2').addEventListener('click', function() {
            const resultElement = document.getElementById('result2');
            
            try {
                console.log('开始测试2: character_relationships错误');
                
                // 创建一个包含character_relationships错误的JSON字符串
                let jsonStr = '{"dimension":"character_relationships","content":"# 分析 **character_relationships** 时遇到了问题\\n\\n## 错误信息\\n```\\nname 张长老 is not defined\\n```\\n\\n请检查您的文本内容，确保所有人物名称都已正确定义。","metadata":{"processing_time":0.5,"error":true}}';
                
                // 尝试解析
                const result = JSON.parse(jsonStr);
                
                // 显示结果
                resultElement.textContent = '解析成功！结果：\n' + JSON.stringify(result, null, 2);
                resultElement.classList.add('success');
                resultElement.classList.remove('error');
                
                console.log('测试2完成：成功修复character_relationships错误');
            } catch (e) {
                resultElement.textContent = '解析失败：' + e.message;
                resultElement.classList.add('error');
                resultElement.classList.remove('success');
                
                console.error('测试2失败：', e.message);
            }
        });
        
        // 测试3: 通用JSON修复
        document.getElementById('test3').addEventListener('click', function() {
            const resultElement = document.getElementById('result3');
            
            try {
                console.log('开始测试3: 通用JSON修复');
                
                // 创建一个包含各种常见JSON错误的字符串
                let jsonStr = '{"name":"测试数据","description":"这是一个包含各种错误的JSON字符串"' + // 缺少逗号
                              '"items":["项目1","项目2","项目3"' + // 缺少右括号
                              '"metadata":{"author":"九猫","created_at":"2023-05-01"' + // 缺少右大括号
                              '"tags":["测试","JSON","修复"]}'; // 缺少右大括号
                
                // 尝试解析
                const result = JSON.parse(jsonStr);
                
                // 显示结果
                resultElement.textContent = '解析成功！结果：\n' + JSON.stringify(result, null, 2);
                resultElement.classList.add('success');
                resultElement.classList.remove('error');
                
                console.log('测试3完成：成功修复通用JSON错误');
            } catch (e) {
                resultElement.textContent = '解析失败：' + e.message;
                resultElement.classList.add('error');
                resultElement.classList.remove('success');
                
                console.error('测试3失败：', e.message);
            }
        });
    </script>
</body>
</html>
