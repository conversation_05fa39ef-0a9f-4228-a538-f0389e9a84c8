.TH "NPM\-FUND" "1" "June 2022" "" ""
.SH "NAME"
\fBnpm-fund\fR \- Retrieve funding information
.SS Synopsis
.P
.RS 2
.nf
npm fund [[<@scope>/]<pkg>]
.fi
.RE
.SS Description
.P
This command retrieves information on how to fund the dependencies of a
given project\. If no package name is provided, it will list all
dependencies that are looking for funding in a tree structure, listing the
type of funding and the url to visit\. If a package name is provided then it
tries to open its funding url using the \fB\-\-browser\fP config param; if there
are multiple funding sources for the package, the user will be instructed
to pass the \fB\-\-which\fP option to disambiguate\.
.P
The list will avoid duplicated entries and will stack all packages that
share the same url as a single entry\. Thus, the list does not have the same
shape of the output from \fBnpm ls\fP\|\.
.SS Example
.SS Workspaces support
.P
It's possible to filter the results to only include a single workspace and its
dependencies using the \fBworkspace\fP config option\.
.SS Example:
.P
Here's an example running \fBnpm fund\fP in a project with a configured
workspace \fBa\fP:
.P
.RS 2
.nf
$ npm fund
test\-workspaces\-fund@1\.0\.0
+\-\- https://example\.com/a
| | `\-\- a@1\.0\.0
| `\-\- https://example\.com/maintainer
|     `\-\- foo@1\.0\.0
+\-\- https://example\.com/npmcli\-funding
|   `\-\- @npmcli/test\-funding
`\-\- https://example\.com/org
    `\-\- bar@2\.0\.0
.fi
.RE
.P
And here is an example of the expected result when filtering only by
a specific workspace \fBa\fP in the same project:
.P
.RS 2
.nf
$ npm fund \-w a
test\-workspaces\-fund@1\.0\.0
`\-\- https://example\.com/a
  | `\-\- a@1\.0\.0
  `\-\- https://example\.com/maintainer
      `\-\- foo@2\.0\.0
.fi
.RE
.SS Configuration
.SS \fBjson\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Whether or not to output JSON data, rather than the normal output\.
.RS 0
.IP \(bu 2
In \fBnpm pkg set\fP it enables parsing set values with JSON\.parse() before
saving them to your \fBpackage\.json\fP\|\.

.RE
.P
Not supported by all npm commands\.
.SS \fBbrowser\fP
.RS 0
.IP \(bu 2
Default: OS X: \fB"open"\fP, Windows: \fB"start"\fP, Others: \fB"xdg\-open"\fP
.IP \(bu 2
Type: null, Boolean, or String

.RE
.P
The browser that is called by npm commands to open websites\.
.P
Set to \fBfalse\fP to suppress browser behavior and instead print urls to
terminal\.
.P
Set to \fBtrue\fP to use default system URL opener\.
.SS \fBunicode\fP
.RS 0
.IP \(bu 2
Default: false on windows, true on mac/unix systems with a unicode locale,
as defined by the \fBLC_ALL\fP, \fBLC_CTYPE\fP, or \fBLANG\fP environment variables\.
.IP \(bu 2
Type: Boolean

.RE
.P
When set to true, npm uses unicode characters in the tree output\. When
false, it uses ascii characters instead of unicode glyphs\.
.SS \fBworkspace\fP
.RS 0
.IP \(bu 2
Default:
.IP \(bu 2
Type: String (can be set multiple times)

.RE
.P
Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option\.
.P
Valid values for the \fBworkspace\fP config are either:
.RS 0
.IP \(bu 2
Workspace names
.IP \(bu 2
Path to a workspace directory
.IP \(bu 2
Path to a parent workspace directory (will result in selecting all
workspaces within that folder)

.RE
.P
When set for the \fBnpm init\fP command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project\.
.P
This value is not exported to the environment for child processes\.
.SS \fBwhich\fP
.RS 0
.IP \(bu 2
Default: null
.IP \(bu 2
Type: null or Number

.RE
.P
If there are multiple funding sources, which 1\-indexed source URL to open\.
.SH See Also
.RS 0
.IP \(bu 2
npm help install
.IP \(bu 2
npm help docs
.IP \(bu 2
npm help ls
.IP \(bu 2
npm help config
.IP \(bu 2
npm help workspaces

.RE
