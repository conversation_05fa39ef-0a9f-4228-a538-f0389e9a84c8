/**
 * 九猫系统 Bootstrap Bundle 替换脚本
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于替换损坏的 bootstrap.bundle.min.js 文件
 */

(function() {
    console.log('[Bootstrap Bundle 替换] 初始化...');
    
    // 检查是否有损坏的 bootstrap.bundle.min.js 文件
    function checkForDamagedBootstrapBundle() {
        const scripts = document.querySelectorAll('script[src*="bootstrap.bundle.min.js"]');
        
        for (const script of scripts) {
            // 检查是否是本地文件
            if (script.src.includes('/static/') || script.src.includes('/lib/')) {
                console.log('[Bootstrap Bundle 替换] 检测到本地 Bootstrap Bundle 脚本:', script.src);
                return script;
            }
        }
        
        return null;
    }
    
    // 替换损坏的 bootstrap.bundle.min.js 文件
    function replaceDamagedBootstrapBundle(damagedScript) {
        console.log('[Bootstrap Bundle 替换] 替换损坏的 Bootstrap Bundle 脚本');
        
        // 创建新的脚本元素
        const newScript = document.createElement('script');
        newScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
        newScript.integrity = 'sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz';
        newScript.crossOrigin = 'anonymous';
        
        // 添加数据属性，标记为替换脚本
        newScript.setAttribute('data-replaced', 'true');
        newScript.setAttribute('data-original-src', damagedScript.src);
        
        // 加载成功回调
        newScript.onload = function() {
            console.log('[Bootstrap Bundle 替换] 新的 Bootstrap Bundle 脚本加载成功');
            
            // 移除损坏的脚本
            if (damagedScript.parentNode) {
                damagedScript.parentNode.removeChild(damagedScript);
            }
            
            // 创建自定义事件通知其他脚本
            try {
                const event = new Event('bootstrapBundleReplaced');
                document.dispatchEvent(event);
            } catch (e) {
                console.error('[Bootstrap Bundle 替换] 创建事件失败:', e);
            }
        };
        
        // 加载失败回调
        newScript.onerror = function() {
            console.error('[Bootstrap Bundle 替换] 新的 Bootstrap Bundle 脚本加载失败，尝试使用备用 CDN');
            
            // 尝试使用备用 CDN
            const backupScript = document.createElement('script');
            backupScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js';
            backupScript.setAttribute('data-replaced', 'true');
            backupScript.setAttribute('data-original-src', damagedScript.src);
            
            backupScript.onload = function() {
                console.log('[Bootstrap Bundle 替换] 从备用 CDN 加载 Bootstrap Bundle 成功');
                
                // 移除损坏的脚本
                if (damagedScript.parentNode) {
                    damagedScript.parentNode.removeChild(damagedScript);
                }
                
                // 创建自定义事件通知其他脚本
                try {
                    const event = new Event('bootstrapBundleReplaced');
                    document.dispatchEvent(event);
                } catch (e) {
                    console.error('[Bootstrap Bundle 替换] 创建事件失败:', e);
                }
            };
            
            backupScript.onerror = function() {
                console.error('[Bootstrap Bundle 替换] 从备用 CDN 加载 Bootstrap Bundle 也失败，尝试使用本地修复');
                
                // 加载本地修复脚本
                loadLocalFixScript();
            };
            
            document.head.appendChild(backupScript);
        };
        
        // 添加到文档
        document.head.appendChild(newScript);
    }
    
    // 加载本地修复脚本
    function loadLocalFixScript() {
        console.log('[Bootstrap Bundle 替换] 加载本地修复脚本');
        
        // 检查是否已加载
        if (document.querySelector('script[src*="bootstrap-bundle-fix.js"]')) {
            console.log('[Bootstrap Bundle 替换] 本地修复脚本已加载');
            return;
        }
        
        // 创建脚本元素
        const script = document.createElement('script');
        script.src = '/static/js/bootstrap-bundle-fix.js';
        
        script.onload = function() {
            console.log('[Bootstrap Bundle 替换] 本地修复脚本加载成功');
        };
        
        script.onerror = function() {
            console.error('[Bootstrap Bundle 替换] 本地修复脚本加载失败，尝试使用内联修复');
            
            // 创建内联修复脚本
            const inlineScript = document.createElement('script');
            inlineScript.textContent = `
                // 内联 Bootstrap 修复
                (function() {
                    console.log('[内联 Bootstrap 修复] 初始化...');
                    
                    // 创建最小的 bootstrap 对象
                    window.bootstrap = window.bootstrap || {};
                    
                    // 创建最小的 Modal 实现
                    bootstrap.Modal = function(element) {
                        this.element = element;
                        
                        this.show = function() {
                            if (this.element) {
                                this.element.style.display = 'block';
                                document.body.classList.add('modal-open');
                            }
                        };
                        
                        this.hide = function() {
                            if (this.element) {
                                this.element.style.display = 'none';
                                document.body.classList.remove('modal-open');
                            }
                        };
                        
                        this.toggle = function() {
                            if (this.element && this.element.style.display === 'block') {
                                this.hide();
                            } else {
                                this.show();
                            }
                        };
                    };
                    
                    // 创建最小的 Tab 实现
                    bootstrap.Tab = function(element) {
                        this.element = element;
                        
                        this.show = function() {
                            if (!this.element) return;
                            
                            // 获取目标面板
                            const target = document.querySelector(this.element.getAttribute('data-bs-target') || this.element.getAttribute('href'));
                            if (!target) return;
                            
                            // 获取所有相关的标签页和面板
                            const parent = this.element.closest('.nav');
                            if (!parent) return;
                            
                            const tabs = parent.querySelectorAll('[data-bs-toggle="tab"], [data-bs-toggle="pill"]');
                            const panes = document.querySelectorAll('.tab-pane');
                            
                            // 隐藏所有面板
                            for (let i = 0; i < panes.length; i++) {
                                panes[i].classList.remove('show', 'active');
                            }
                            
                            // 取消激活所有标签
                            for (let i = 0; i < tabs.length; i++) {
                                tabs[i].classList.remove('active');
                            }
                            
                            // 激活当前标签和面板
                            this.element.classList.add('active');
                            target.classList.add('show', 'active');
                        };
                    };
                    
                    console.log('[内联 Bootstrap 修复] 完成');
                })();
            `;
            
            document.head.appendChild(inlineScript);
        };
        
        document.head.appendChild(script);
    }
    
    // 初始检查
    function initialCheck() {
        // 检查页面是否已加载
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('[Bootstrap Bundle 替换] 页面已加载，执行初始检查');
            setTimeout(function() {
                const damagedScript = checkForDamagedBootstrapBundle();
                if (damagedScript) {
                    console.log('[Bootstrap Bundle 替换] 初始检查发现损坏的脚本，开始替换');
                    replaceDamagedBootstrapBundle(damagedScript);
                } else {
                    console.log('[Bootstrap Bundle 替换] 初始检查未发现损坏的脚本');
                }
            }, 1000); // 等待1秒，确保页面完全加载
        } else {
            console.log('[Bootstrap Bundle 替换] 页面尚未加载，等待 DOMContentLoaded 事件');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('[Bootstrap Bundle 替换] DOMContentLoaded 事件触发，执行初始检查');
                setTimeout(function() {
                    const damagedScript = checkForDamagedBootstrapBundle();
                    if (damagedScript) {
                        console.log('[Bootstrap Bundle 替换] 初始检查发现损坏的脚本，开始替换');
                        replaceDamagedBootstrapBundle(damagedScript);
                    } else {
                        console.log('[Bootstrap Bundle 替换] 初始检查未发现损坏的脚本');
                    }
                }, 1000); // 等待1秒，确保页面完全加载
            });
        }
    }
    
    // 导出全局函数
    window.replaceBootstrapBundle = function() {
        console.log('[Bootstrap Bundle 替换] 手动触发 Bootstrap Bundle 替换');
        const damagedScript = checkForDamagedBootstrapBundle();
        if (damagedScript) {
            replaceDamagedBootstrapBundle(damagedScript);
            return true;
        } else {
            console.log('[Bootstrap Bundle 替换] 未发现损坏的脚本');
            return false;
        }
    };
    
    // 执行初始检查
    initialCheck();
    
    console.log('[Bootstrap Bundle 替换] 初始化完成');
})();
