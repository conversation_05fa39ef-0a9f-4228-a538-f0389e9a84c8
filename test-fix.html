<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫系统 - 修复脚本测试</title>
    
    <!-- 内联引导加载器 - 必须最先加载 -->
    <script>
        (function() {
            console.log('九猫修复引导加载器已初始化');
            
            // 预定义基本变量
            window.result = window.result || { 
                status: 'ok',
                message: '预定义的result对象',
                data: {},
                success: true
            };
            
            // 修复document.createElement
            const originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
                const element = originalCreateElement.call(document, tagName);
                
                // 处理脚本元素
                if (typeof tagName === 'string' && tagName.toLowerCase() === 'script') {
                    try {
                        if (typeof element.textContent === 'undefined') {
                            Object.defineProperty(element, 'textContent', {
                                get: function() { return this.innerText || ''; },
                                set: function(value) { this.innerText = value; },
                                configurable: true
                            });
                        }
                    } catch(e) {
                        console.warn('修复脚本元素时出错:', e);
                    }
                }
                
                return element;
            };
            
            // 安全加载脚本
            window.safeLoadScript = function(url, async) {
                try {
                    const script = document.createElement('script');
                    script.src = url;
                    script.async = !!async;
                    document.head.appendChild(script);
                    console.log('加载脚本:', url);
                    return script;
                } catch(e) {
                    console.error('加载脚本时出错:', e);
                    return null;
                }
            };
            
            // 全局错误处理
            window.onerror = function(message, source, lineno, colno, error) {
                console.error(`全局错误: ${message} at ${source}:${lineno}:${colno}`);
                return true; // 阻止错误传播
            };
        })();
    </script>
    
    <!-- 加载修复脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始加载修复脚本');
            
            // 加载各修复脚本
            safeLoadScript('/src/web/static/js/create-element-fix.js', false);
            safeLoadScript('/src/web/static/js/emergency-resource-loader.js', false);
            safeLoadScript('/src/web/static/js/result-not-defined-fix.js', false);
            safeLoadScript('/src/web/static/js/dom-operation-fix.js', false);
            safeLoadScript('/src/web/static/js/jquery-loader.js', false);
            safeLoadScript('/src/web/static/js/chart-loader.js', false);
            safeLoadScript('/src/web/static/js/supreme-fixer.js', false);
        });
    </script>
    
    <!-- 基本样式 -->
    <style>
        body { font-family: system-ui, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { border: 1px solid #ddd; border-radius: 4px; padding: 16px; margin-bottom: 16px; }
        .btn { display: inline-block; background: #4CAF50; color: white; padding: 8px 16px; 
               border: none; border-radius: 4px; cursor: pointer; margin-right: 8px; }
        .btn:hover { background: #45a049; }
        .result { margin-top: 12px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; min-height: 24px; }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>九猫系统修复脚本测试</h1>
        <p>此页面用于测试九猫系统的修复脚本功能。请打开控制台查看详细信息。</p>
        
        <div class="card">
            <h2>1. result变量未定义测试</h2>
            <button class="btn" id="test-undefined">测试result变量</button>
            <div class="result" id="undefined-result"></div>
            
            <script>
                document.getElementById('test-undefined').addEventListener('click', function() {
                    try {
                        // 尝试使用未定义的result变量
                        const data = result.data;
                        document.getElementById('undefined-result').textContent = 
                            '成功访问result变量: ' + JSON.stringify(data);
                    } catch(e) {
                        document.getElementById('undefined-result').textContent = 
                            '错误: ' + e.message;
                    }
                });
            </script>
        </div>
        
        <div class="card">
            <h2>2. 属性设置错误测试</h2>
            <button class="btn" id="test-property">测试属性设置</button>
            <div class="result" id="property-result"></div>
            
            <script>
                document.getElementById('test-property').addEventListener('click', function() {
                    try {
                        // 创建脚本元素并设置属性
                        const script = document.createElement('script');
                        script.textContent = 'console.log("脚本内容加载成功");';
                        document.head.appendChild(script);
                        
                        document.getElementById('property-result').textContent = 
                            '属性设置成功';
                    } catch(e) {
                        document.getElementById('property-result').textContent = 
                            '错误: ' + e.message;
                    }
                });
            </script>
        </div>
        
        <div class="card">
            <h2>3. DOM操作错误测试</h2>
            <button class="btn" id="test-dom">测试DOM操作</button>
            <div class="result" id="dom-result"></div>
            <div id="dom-container"></div>
            
            <script>
                document.getElementById('test-dom').addEventListener('click', function() {
                    try {
                        const container = document.getElementById('dom-container');
                        
                        // 创建一个元素
                        const div = document.createElement('div');
                        div.textContent = '测试元素';
                        div.style.padding = '8px';
                        div.style.backgroundColor = '#f0f0f0';
                        div.style.margin = '4px';
                        
                        // 重复添加同一个元素（正常会出错）
                        container.appendChild(div);
                        container.appendChild(div); // 这里应该会被修复
                        
                        document.getElementById('dom-result').textContent = 
                            '重复DOM操作成功处理';
                    } catch(e) {
                        document.getElementById('dom-result').textContent = 
                            '错误: ' + e.message;
                    }
                });
            </script>
        </div>
        
        <div class="card">
            <h2>4. 资源加载错误测试</h2>
            <button class="btn" id="test-resource">加载不存在的资源</button>
            <div class="result" id="resource-result"></div>
            
            <script>
                document.getElementById('test-resource').addEventListener('click', function() {
                    try {
                        // 尝试加载不存在的资源
                        const script = document.createElement('script');
                        script.src = '/not-exist-resource.js';
                        script.onload = function() {
                            document.getElementById('resource-result').textContent = 
                                '资源加载成功（这不应该发生）';
                        };
                        script.onerror = function() {
                            document.getElementById('resource-result').textContent = 
                                '资源加载失败，但页面继续运行';
                        };
                        document.head.appendChild(script);
                    } catch(e) {
                        document.getElementById('resource-result').textContent = 
                            '错误: ' + e.message;
                    }
                });
            </script>
        </div>
        
        <div class="card">
            <h2>5. jQuery和Chart.js测试</h2>
            <button class="btn" id="test-jquery">测试jQuery</button>
            <button class="btn" id="test-chart">测试Chart.js</button>
            <div class="result" id="libs-result"></div>
            <canvas id="myChart" width="400" height="200"></canvas>
            
            <script>
                document.getElementById('test-jquery').addEventListener('click', function() {
                    try {
                        if (typeof jQuery !== 'undefined') {
                            // 使用jQuery
                            jQuery('#libs-result').text('jQuery加载成功，版本: ' + jQuery.fn.jquery);
                        } else {
                            document.getElementById('libs-result').textContent = 
                                'jQuery未加载';
                        }
                    } catch(e) {
                        document.getElementById('libs-result').textContent = 
                            '错误: ' + e.message;
                    }
                });
                
                document.getElementById('test-chart').addEventListener('click', function() {
                    try {
                        if (typeof Chart !== 'undefined') {
                            // 使用Chart.js
                            const ctx = document.getElementById('myChart').getContext('2d');
                            new Chart(ctx, {
                                type: 'bar',
                                data: {
                                    labels: ['红', '蓝', '黄', '绿', '紫', '橙'],
                                    datasets: [{
                                        label: '样本数据',
                                        data: [12, 19, 3, 5, 2, 3],
                                        backgroundColor: [
                                            'rgba(255, 99, 132, 0.2)',
                                            'rgba(54, 162, 235, 0.2)',
                                            'rgba(255, 206, 86, 0.2)',
                                            'rgba(75, 192, 192, 0.2)',
                                            'rgba(153, 102, 255, 0.2)',
                                            'rgba(255, 159, 64, 0.2)'
                                        ]
                                    }]
                                }
                            });
                            document.getElementById('libs-result').textContent = 
                                'Chart.js加载成功并绘制图表';
                        } else {
                            document.getElementById('libs-result').textContent = 
                                'Chart.js未加载';
                        }
                    } catch(e) {
                        document.getElementById('libs-result').textContent = 
                            '错误: ' + e.message;
                    }
                });
            </script>
        </div>
    </div>
</body>
</html> 