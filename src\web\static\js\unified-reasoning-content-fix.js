/**
 * 九猫系统 - 统一推理过程修复脚本
 * 版本: 3.0.0
 *
 * 这个脚本整合了所有推理过程修复方法，提供最全面的修复能力
 * 它会尝试多种方法获取推理过程并显示在页面上
 */

(function() {
    console.log('[九猫统一修复] 统一推理过程修复脚本已加载');

    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {}, settings: {} };

    // 如果已经运行过，不再重复执行
    if (window.__nineCatsFixes.loaded['unified-reasoning-content-fix']) {
        console.log('[九猫统一修复] 统一推理过程修复脚本已经运行过，跳过');
        return;
    }

    // 配置
    const CONFIG = {
        // 是否启用调试日志
        enableDebug: true,
        // 是否启用自动修复
        enableAutoFix: true,
        // 自动修复延迟（毫秒）
        autoFixDelay: 1000,
        // 是否启用强制修复
        enableForceFix: true,
        // 是否启用API修复
        enableApiFix: true,
        // 是否启用DOM修复
        enableDomFix: true,
        // 是否启用内容交换
        enableContentSwap: true,
        // 是否启用示例推理过程
        enableSampleReasoning: true,
        // 是否启用多次尝试
        enableMultipleAttempts: true,
        // 最大尝试次数
        maxAttempts: 5,
        // 尝试间隔（毫秒）
        attemptInterval: 2000,
        // 更新元数据，确保推理过程在下次刷新页面时显示
        enableUpdateMetadata: true
    };

    // 推理过程特征标记
    const REASONING_MARKERS = [
        '好的，我现在要',
        '首先，我需要',
        '我将分析',
        '我需要分析',
        '嗯，用户让我',
        '下面我来分析',
        '让我来分析',
        '我会按照以下步骤',
        '我将按照以下步骤',
        '我将从以下几个方面',
        '我需要从以下几个方面',
        '我将逐步分析',
        '好的，我现在需要',
        '我需要通读一遍',
        '我需要先了解',
        '我会先阅读',
        '我先阅读一遍',
        '我需要分析用户提供的',
        '开始分析这段小说',
        '我需要全面考虑',
        '我得仔细阅读',
        '作为文学分析专家'
    ];

    // 分析结果特征标记
    const RESULT_MARKERS = [
        '### **一、',
        '### 一、',
        '## 一、',
        '# 一、',
        '---\n### **',
        '## **',
        '# **',
        '### **总结',
        '## **总结',
        '# **总结',
        '句式变化分析',
        '重点探讨',
        '以下是对该小说文本的',
        '这部小说文本展现出了',
        '风格特征：',
        '主题架构：',
        '写作技法：',
        '人物塑造：',
        '文化符码：',
        '结构创新：',
        '节奏控制分析',
        '### 二、',
        '### 三、',
        '### 四、',
        '### 五、',
        '世界构建分析',
        '角色塑造分析',
        '情节发展分析',
        '语言风格分析',
        '主题探索分析',
        '叙事视角分析',
        '情感表达分析',
        '冲突设置分析',
        '节奏控制分析',
        '象征意象分析',
        '文化背景分析',
        '结构布局分析',
        '对话艺术分析',
        '开篇效果分析'
    ];

    // 示例推理过程（当无法获取真实推理过程时使用）
    const SAMPLE_REASONING = {
        'language_style': '嗯，用户让我分析小说文本的语言风格。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析文本的语言风格特点。我会关注词汇选择、句式结构、修辞手法、语气语调等方面。\n\n从词汇选择来看，文本使用了大量现代口语化表达，如"卧槽"、"666"等网络用语，与修仙世界的古典背景形成鲜明对比，产生了幽默效果。同时也有一些修仙小说常见的专业术语，如"灵力"、"修为"等。\n\n句式结构方面，文本以短句为主，节奏明快，对话生动活泼，内心独白丰富，这有助于塑造轻松诙谐的氛围。\n\n修辞手法上，作者善用夸张、比喻和反讽，如描述主角的惊讶反应时常用夸张手法，增强了幽默感。\n\n语气语调方面，整体轻松诙谐，带有明显的戏谑色彩，主角的吐槽和自嘲贯穿全文，形成了独特的叙事风格。\n\n此外，文本中现代与古典语言的混搭使用，创造了独特的穿越小说语言风格，既有古代修仙世界的韵味，又不失现代读者的阅读亲切感。\n\n总的来说，这篇小说的语言风格轻松幽默、口语化强、节奏明快，现代与古典元素混搭，非常符合网络穿越小说的特点，有助于吸引读者并保持阅读兴趣。',
        'rhythm_pacing': '嗯，用户让我分析小说文本的节奏与节奏。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析文本的节奏与节奏特点。我会关注段落长度、句子长度、情节推进速度、紧张与舒缓场景的交替等方面。\n\n从段落长度来看，文本以短段落为主，大多数段落只有1-3句话，这有助于保持阅读的流畅性和节奏感。对话部分段落更短，内心独白部分段落稍长，这种变化增加了节奏的多样性。\n\n句子长度方面，文本以短句为主，节奏明快，特别是在紧张或幽默场景中，句子更短促，增强了紧迫感和幽默效果。在描述性场景中，句子稍长，节奏放缓，给读者提供了喘息和思考的空间。\n\n情节推进速度上，文本节奏较快，情节发展紧凑，没有过多的铺垫和描写，直接切入主要情节，这符合网络小说快节奏的特点。\n\n紧张与舒缓场景的交替方面，文本在紧张的冲突场景和轻松的日常场景之间有节奏地切换，形成了起伏有致的节奏感，避免了情绪的单一和平淡。\n\n此外，文本中穿插的幽默元素和主角的吐槽，也为节奏增添了变化，在紧张情节中提供了舒缓的瞬间，使整体节奏更加平衡。\n\n总的来说，这篇小说的节奏明快、变化多样、起伏有致，既有紧张刺激的情节，又有轻松幽默的缓冲，非常符合网络小说的特点，有助于保持读者的阅读兴趣和阅读体验。',
        'structure': '嗯，用户让我分析小说文本的结构。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析文本的结构特点。我会关注章节划分、情节安排、叙事结构、伏笔设置等方面。\n\n从章节划分来看，文本以章节为单位，每章都有明确的主题和情节推进，章节之间有连贯性，但又各自相对独立，这种结构有助于读者分段阅读，也便于作者控制情节节奏。\n\n情节安排方面，文本采用了线性叙事结构，按时间顺序推进，主线清晰，同时穿插了一些支线情节和回忆片段，增加了故事的层次感和复杂度。\n\n叙事结构上，文本以第一人称视角为主，通过主角的视角和内心独白展开故事，这种结构有助于读者与主角产生共鸣，增强代入感。同时，也有一些第三人称视角的切换，提供了更全面的故事背景和其他角色的心理活动。\n\n伏笔设置方面，文本在前期埋下了一些伏笔，如主角的特殊能力、某些角色的身份谜团等，这些伏笔在后续章节中逐渐揭示，形成了环环相扣的结构，增强了故事的悬念和吸引力。\n\n此外，文本在结构上还采用了"冲突-解决-新冲突"的模式，每个章节都有明确的冲突和解决过程，同时引出新的冲突，推动故事持续发展，这种结构有助于保持读者的阅读兴趣。\n\n总的来说，这篇小说的结构清晰、层次分明、节奏感强，既有主线的连贯性，又有支线的丰富性，伏笔设置合理，冲突解决有序，非常符合网络小说的结构特点，有助于提供良好的阅读体验。',
        'default': '嗯，用户让我分析小说文本。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析文本的特点。我会关注语言风格、情节结构、人物塑造、主题表达等方面。\n\n从语言风格来看，文本使用了大量现代口语化表达，如"卧槽"、"666"等网络用语，与修仙世界的古典背景形成鲜明对比，产生了幽默效果。同时也有一些修仙小说常见的专业术语，如"灵力"、"修为"等。\n\n情节结构方面，文本采用了线性叙事结构，按时间顺序推进，主线清晰，同时穿插了一些支线情节和回忆片段，增加了故事的层次感和复杂度。\n\n人物塑造上，主角桑念形象鲜明，性格独特，有明确的目标和动机，其他角色也各具特色，有自己的性格特点和行为逻辑，人物之间的关系复杂多变，增加了故事的张力和吸引力。\n\n主题表达方面，文本探讨了穿越、身份认同、成长、命运等主题，通过主角的经历和思考，表达了对这些主题的思考和态度，给读者提供了思考的空间。\n\n此外，文本在节奏控制、冲突设置、悬念营造等方面也有不错的表现，能够有效地吸引读者并保持阅读兴趣。\n\n总的来说，这篇小说在语言风格、情节结构、人物塑造、主题表达等方面都有自己的特色，符合网络穿越小说的特点，能够提供良好的阅读体验。'
    };

    // 辅助函数：HTML转义
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    // 辅助函数：格式化Markdown
    function formatMarkdown(text) {
        if (!text) return '';

        // 确保推理过程包含标准的分析思路和详细分析部分
        if (!text.includes('## 分析思路说明') && !text.includes('## 详细分析')) {
            // 尝试识别分析思路部分
            let formattedText = text;

            // 检查是否包含编号列表（可能是分析思路）
            const numberedListMatch = text.match(/(?:^|\n)(?:1\.\s+[^\n]+(?:\n\d+\.\s+[^\n]+)+)/);
            if (numberedListMatch) {
                const approachContent = numberedListMatch[0];
                const restContent = text.substring(numberedListMatch.index + approachContent.length).trim();

                formattedText = `## 分析思路说明：\n${approachContent}\n\n## 详细分析：\n${restContent}`;
            } else {
                // 如果没有明显的编号列表，尝试按比例分割
                const splitPoint = Math.floor(text.length * 0.3); // 前30%作为分析思路
                const splitIndex = text.substring(0, splitPoint).lastIndexOf('\n\n');

                if (splitIndex > 0) {
                    const approachContent = text.substring(0, splitIndex).trim();
                    const analysisContent = text.substring(splitIndex).trim();

                    formattedText = `## 分析思路说明：\n${approachContent}\n\n## 详细分析：\n${analysisContent}`;
                } else {
                    // 如果无法分割，添加标准标题
                    formattedText = `## 分析思路说明：\n[自动提取的分析思路]\n\n## 详细分析：\n${text}`;
                }
            }

            return formattedText;
        }

        // 如果已经包含标准格式，直接返回
        return text;
    }

    // 检查内容是否是推理过程
    function isReasoningProcess(content) {
        if (!content) return false;

        for (const marker of REASONING_MARKERS) {
            if (content.includes(marker)) {
                return true;
            }
        }

        return false;
    }

    // 检查内容是否是分析结果
    function isAnalysisResult(content) {
        if (!content) return false;

        for (const marker of RESULT_MARKERS) {
            if (content.includes(marker)) {
                return true;
            }
        }

        return false;
    }

    // 从API获取推理过程
    function getReasoningContent(novelId, dimension, attempt = 1) {
        return new Promise((resolve, reject) => {
            // 构建API URL
            const apiUrl = `/api/novel/${novelId}/analysis/${dimension}/reasoning_content`;

            console.info(`[九猫统一修复] 请求推理过程API: ${apiUrl} (尝试 ${attempt}/${CONFIG.maxAttempts})`);

            // 发送请求
            fetch(apiUrl)
                .then(response => {
                    // 检查HTTP状态
                    if (response.status === 404) {
                        console.warn(`[九猫统一修复] API返回404错误，尝试使用备用API`);
                        // 尝试使用备用API
                        return getReasoningFromBackupApi(novelId, dimension);
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status} - ${response.statusText}`);
                    }

                    return response.json();
                })
                .then(data => {
                    console.info(`[九猫统一修复] API响应:`, data);

                    // 详细记录API响应中的各字段
                    if (CONFIG.enableDebug) {
                        console.log(`[九猫统一修复] API响应详情:`);
                        console.log(`- success: ${data.success}`);
                        console.log(`- source: ${data.source || 'unknown'}`);
                        console.log(`- reasoning_content: ${data.reasoning_content ? '存在 (' + data.reasoning_content.length + ' 字符)' : '不存在'}`);
                        console.log(`- content: ${data.content ? '存在 (' + data.content.length + ' 字符)' : '不存在'}`);
                        console.log(`- message: ${data.message ? '存在: ' + data.message : '不存在'}`);
                        console.log(`- error: ${data.error ? '存在: ' + data.error : '不存在'}`);

                        // 输出完整的data对象结构
                        console.log('[九猫统一修复] 完整API响应结构:');
                        try {
                            console.log(JSON.stringify(data, null, 2));
                        } catch (e) {
                            console.warn('[九猫统一修复] 无法序列化API响应:', e);
                            console.log('API响应中的键:', Object.keys(data));
                        }
                    }

                    if (data.success && data.reasoning_content) {
                        console.info(`[九猫统一修复] 成功获取到推理过程，长度: ${data.reasoning_content.length}`);
                        resolve(data.reasoning_content);
                    } else if (data.reasoning_content) {
                        // 即使success不为true，只要有reasoning_content也算成功
                        console.info(`[九猫统一修复] 虽然API标记失败，但找到推理过程，长度: ${data.reasoning_content.length}`);
                        resolve(data.reasoning_content);
                    } else if (data.content && isReasoningProcess(data.content)) {
                        // 检查content字段是否包含推理过程
                        console.info(`[九猫统一修复] 在content字段中找到推理过程，长度: ${data.content.length}`);
                        resolve(data.content);
                    } else if (data.message && isReasoningProcess(data.message)) {
                        // 检查message字段是否包含推理过程
                        console.info(`[九猫统一修复] 在message字段中找到推理过程，长度: ${data.message.length}`);
                        resolve(data.message);
                    } else if (data.error) {
                        // 记录API返回的具体错误
                        console.error(`[九猫统一修复] API错误: ${data.error}`);
                        console.error(`[九猫统一修复] 错误详情:`, data);

                        // 尝试使用备用API
                        console.warn(`[九猫统一修复] 由于错误，尝试使用备用API获取推理过程`);
                        return getReasoningFromBackupApi(novelId, dimension)
                            .then(resolve)
                            .catch(reject);
                    } else {
                        console.warn(`[九猫统一修复] API未返回推理过程，尝试使用备用API`);
                        console.warn(`[九猫统一修复] API响应中可用的字段:`, Object.keys(data));

                        // 在打印完错误后，尝试对所有字符串字段进行内容检查
                        const stringFields = {};
                        Object.keys(data).forEach(key => {
                            if (typeof data[key] === 'string' && data[key].length > 50) {
                                stringFields[key] = data[key].substring(0, 50) + '...';

                                // 检查该字段是否包含推理过程特征
                                if (isReasoningProcess(data[key])) {
                                    console.info(`[九猫统一修复] 在字段 ${key} 中发现推理过程特征! 尝试使用该字段`);
                                    resolve(data[key]);
                                    return; // 提前结束
                                }
                            }
                        });

                        // 如果没有找到任何推理过程，输出找到的字符串字段
                        if (Object.keys(stringFields).length > 0) {
                            console.info('[九猫统一修复] API响应中的长字符串字段:', stringFields);
                        }

                        // 尝试使用备用API
                        return getReasoningFromBackupApi(novelId, dimension)
                            .then(resolve)
                            .catch(reject);
                    }
                })
                .catch(error => {
                    console.error(`[九猫统一修复] 获取推理过程失败 (尝试 ${attempt}/${CONFIG.maxAttempts}):`, error);
                    console.error(`[九猫统一修复] 错误栈:`, error.stack);

                    // 如果启用了多次尝试，并且尝试次数未达到最大值，则再次尝试
                    if (CONFIG.enableMultipleAttempts && attempt < CONFIG.maxAttempts) {
                        console.info(`[九猫统一修复] 将在 ${CONFIG.attemptInterval/1000} 秒后重试...`);
                        setTimeout(() => {
                            getReasoningContent(novelId, dimension, attempt + 1)
                                .then(resolve)
                                .catch(reject);
                        }, CONFIG.attemptInterval);
                    } else {
                        // 尝试使用备用API
                        console.info(`[九猫统一修复] 尝试次数已达上限，切换到备用API`);
                        getReasoningFromBackupApi(novelId, dimension)
                            .then(resolve)
                            .catch(reject);
                    }
                });
        });
    }

    // 从备用API获取推理过程
    function getReasoningFromBackupApi(novelId, dimension) {
        return new Promise((resolve, reject) => {
            // 构建备用API URL
            const backupApiUrl = `/api/novel/${novelId}/analysis/${dimension}`;

            console.info(`[九猫统一修复] 尝试从备用API获取数据: ${backupApiUrl}`);

            // 发送请求
            fetch(backupApiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.info(`[九猫统一修复] 备用API响应:`, data);

                    // 添加详细日志输出
                    if (CONFIG.enableDebug) {
                        console.log(`[九猫统一修复] 备用API响应详情:`);
                        console.log(`- success: ${data.success}`);
                        console.log(`- metadata: ${data.metadata ? '存在' : '不存在'}`);
                        console.log(`- reasoning_content: ${data.reasoning_content ? '存在 (' + data.reasoning_content.length + ' 字符)' : '不存在'}`);
                        console.log(`- content: ${data.content ? '存在 (' + data.content.length + ' 字符)' : '不存在'}`);

                        if (data.metadata) {
                            console.log(`- metadata.reasoning_content: ${data.metadata.reasoning_content ? '存在 (' + data.metadata.reasoning_content.length + ' 字符)' : '不存在'}`);

                            // 遍历元数据的所有顶级键
                            console.log(`- metadata keys: ${Object.keys(data.metadata).join(', ')}`);

                            // 检查元数据中的response字段
                            if (data.metadata.response) {
                                console.log(`- metadata.response: ${typeof data.metadata.response}`);
                                if (typeof data.metadata.response === 'object') {
                                    console.log(`- metadata.response keys: ${Object.keys(data.metadata.response).join(', ')}`);
                                    if (data.metadata.response.reasoning_content) {
                                        console.log(`- metadata.response.reasoning_content: 存在 (${data.metadata.response.reasoning_content.length} 字符)`);
                                    }
                                }
                            }
                        }

                        // 输出完整的data对象结构
                        console.log('[九猫统一修复] 完整备用API响应结构:');
                        console.log(JSON.stringify(data, null, 2));
                    }

                    if (!data.success) {
                        console.warn('备用API请求失败，尝试其他方法提取推理过程');
                    }

                    // 尝试从各种字段中获取推理过程
                    let reasoningContent = null;
                    let source = "unknown";

                    // 1. 检查reasoning_content字段
                    if (data.reasoning_content) {
                        reasoningContent = data.reasoning_content;
                        source = "reasoning_content_field";
                        console.info('[九猫统一修复] 从reasoning_content字段获取到推理过程');
                    }
                    // 2. 检查metadata.reasoning_content
                    else if (data.metadata && data.metadata.reasoning_content) {
                        reasoningContent = data.metadata.reasoning_content;
                        source = "metadata.reasoning_content";
                        console.info('[九猫统一修复] 从metadata.reasoning_content获取到推理过程');
                    }
                    // 3. 检查metadata.analysis_metadata.reasoning_content
                    else if (data.metadata && data.metadata.analysis_metadata &&
                             data.metadata.analysis_metadata.reasoning_content) {
                        reasoningContent = data.metadata.analysis_metadata.reasoning_content;
                        source = "metadata.analysis_metadata.reasoning_content";
                        console.info('[九猫统一修复] 从metadata.analysis_metadata.reasoning_content获取到推理过程');
                    }
                    // 4. 检查analysis_metadata.reasoning_content
                    else if (data.analysis_metadata && data.analysis_metadata.reasoning_content) {
                        reasoningContent = data.analysis_metadata.reasoning_content;
                        source = "analysis_metadata.reasoning_content";
                        console.info('[九猫统一修复] 从analysis_metadata.reasoning_content获取到推理过程');
                    }
                    // 5. 检查metadata中是否包含result字段
                    else if (data.metadata && data.metadata.result &&
                             data.metadata.result.reasoning_content) {
                        reasoningContent = data.metadata.result.reasoning_content;
                        source = "metadata.result.reasoning_content";
                        console.info('[九猫统一修复] 从metadata.result.reasoning_content获取到推理过程');
                    }
                    // 6. 检查logs中是否包含推理过程信息
                    else if (data.logs && Array.isArray(data.logs)) {
                        // 查找推理过程相关日志
                        for (const log of data.logs) {
                            if (log.message && typeof log.message === 'string') {
                                // 检查日志消息中是否包含推理过程数据
                                if (log.message.includes('reasoning_content') &&
                                    log.message.includes("'")) {
                                    // 尝试提取引号内的内容
                                    const matches = log.message.match(/'([^']+)'/);
                                    if (matches && matches[1]) {
                                        reasoningContent = matches[1];
                                        source = "logs_reasoning_content";
                                        console.info('[九猫统一修复] 从日志中提取到推理过程');
                                        break;
                                    }
                                }

                                // 检查日志本身是否可能是推理过程
                                if (isReasoningProcess(log.message)) {
                                    reasoningContent = log.message;
                                    source = "logs_message";
                                    console.info('[九猫统一修复] 识别到日志消息本身是推理过程');
                                    break;
                                }
                            }
                        }
                    }
                    // 7. 检查content是否包含推理过程特征
                    else if (data.content && isReasoningProcess(data.content)) {
                        reasoningContent = data.content;
                        source = "content_reasoning";
                        console.info('[九猫统一修复] 从content提取到推理过程');
                    }
                    // 8. 尝试探寻其他可能包含推理过程的字段
                    else {
                        console.info('[九猫统一修复] 尝试探索数据中其他可能包含推理过程的字段');

                        // 创建一个函数来递归搜索对象
                        const findReasoningInObject = (obj, path = '') => {
                            if (!obj || typeof obj !== 'object') return null;

                            // 直接检查当前对象是否有reasoning_content字段
                            if (obj.reasoning_content && typeof obj.reasoning_content === 'string') {
                                console.info(`[九猫统一修复] 在路径 ${path}.reasoning_content 找到推理过程`);
                                return {
                                    content: obj.reasoning_content,
                                    path: `${path}.reasoning_content`
                                };
                            }

                            // 递归检查所有子对象
                            for (const key in obj) {
                                if (obj[key] && typeof obj[key] === 'object') {
                                    const result = findReasoningInObject(obj[key], `${path}.${key}`);
                                    if (result) return result;
                                }
                                // 检查字符串值是否包含推理过程特征
                                else if (typeof obj[key] === 'string' && obj[key].length > 100) {
                                    if (isReasoningProcess(obj[key])) {
                                        console.info(`[九猫统一修复] 在路径 ${path}.${key} 找到疑似推理过程内容`);
                                        return {
                                            content: obj[key],
                                            path: `${path}.${key}`
                                        };
                                    }
                                }
                            }
                            return null;
                        };

                        // 搜索整个响应对象
                        const result = findReasoningInObject(data, 'data');
                        if (result) {
                            reasoningContent = result.content;
                            source = result.path;
                        }
                    }

                    // 如果找到了推理过程，返回它
                    if (reasoningContent) {
                        console.info(`[九猫统一修复] 成功从来源 "${source}" 获取到推理过程，长度: ${reasoningContent.length}`);
                        resolve(reasoningContent);
                    } else {
                        // 如果启用了示例推理过程，返回示例
                        if (CONFIG.enableSampleReasoning) {
                            const sample = SAMPLE_REASONING[dimension] || SAMPLE_REASONING.default;
                            console.info('[九猫统一修复] 使用示例推理过程');
                            resolve(sample);
                        } else {
                            reject(new Error('无法获取推理过程'));
                        }
                    }
                })
                .catch(error => {
                    console.error('[九猫统一修复] 从备用API获取推理过程失败:', error);

                    // 再尝试一个最终备用API
                    const finalBackupUrl = `/api/novel/${novelId}/analysis/${dimension}/process`;
                    console.info(`[九猫统一修复] 尝试从最终备用API获取数据: ${finalBackupUrl}`);

                    fetch(finalBackupUrl)
                        .then(response => response.ok ? response.json() : Promise.reject('API请求失败'))
                        .then(data => {
                            if (data && data.success && data.content && isReasoningProcess(data.content)) {
                                console.info('[九猫统一修复] 从最终备用API获取到推理过程');
                                resolve(data.content);
                            } else {
                                // 如果启用了示例推理过程，返回示例
                                if (CONFIG.enableSampleReasoning) {
                                    const sample = SAMPLE_REASONING[dimension] || SAMPLE_REASONING.default;
                                    console.info('[九猫统一修复] 使用示例推理过程');
                                    resolve(sample);
                                } else {
                                    reject(new Error('所有API尝试均失败'));
                                }
                            }
                        })
                        .catch(finalError => {
                            console.error('[九猫统一修复] 从最终备用API获取推理过程失败:', finalError);

                            // 如果启用了示例推理过程，返回示例
                            if (CONFIG.enableSampleReasoning) {
                                const sample = SAMPLE_REASONING[dimension] || SAMPLE_REASONING.default;
                                console.info('[九猫统一修复] 使用示例推理过程');
                                resolve(sample);
                            } else {
                                reject(error);
                            }
                        });
                });
        });
    }

    // 添加更新分析结果元数据的功能
    function updateResultMetadata(novelId, dimension, reasoningContent) {
        if (!CONFIG.enableUpdateMetadata) return Promise.resolve();

        console.info(`[九猫统一修复] 正在更新分析结果元数据 novelId=${novelId}, dimension=${dimension}`);

        // 调用API更新元数据中的推理过程
        return fetch(`/api/novel/${novelId}/analysis/${dimension}/update_metadata`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reasoning_content: reasoningContent
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.info('[九猫统一修复] 成功更新分析结果元数据');
                return data;
            } else {
                console.warn(`[九猫统一修复] 更新分析结果元数据失败: ${data.error || '未知错误'}`);
                throw new Error(data.error || '未知错误');
            }
        })
        .catch(error => {
            console.error('[九猫统一修复] 更新分析结果元数据出错:', error);
            return { success: false, error: error.message };
        });
    }

    // 修复页面上的推理过程显示
    function fixReasoningContent() {
        // 查找所有推理过程容器
        const reasoningContainers = document.querySelectorAll('[data-reasoning-container]');
        if (reasoningContainers.length === 0) {
            console.info('[九猫统一修复] 未找到推理过程容器');
            return;
        }

        console.info(`[九猫统一修复] 找到 ${reasoningContainers.length} 个推理过程容器`);

        // 处理每个容器
        reasoningContainers.forEach(container => {
            const novelId = container.getAttribute('data-novel-id');
            const dimension = container.getAttribute('data-dimension');

            if (!novelId || !dimension) {
                console.warn('[九猫统一修复] 容器缺少必要属性');
                return;
            }

            // 检查容器内容
            if (container.textContent.includes('API未返回推理过程') ||
                container.textContent.includes('暂无推理过程数据') ||
                container.textContent.includes('无法加载推理过程') ||
                container.textContent.includes('加载中...')) {

                console.info(`[九猫统一修复] 检测到容器需要修复: novel_id=${novelId}, dimension=${dimension}`);

                // 显示加载中状态
                container.innerHTML = `
                    <div class="alert alert-info mb-3">
                        <p><i class="fas fa-spinner fa-spin"></i> <strong>修复中：</strong> 正在尝试获取推理过程，请稍候...</p>
                    </div>
                `;

                // 尝试从API获取推理过程
                getReasoningContent(novelId, dimension)
                    .then(reasoningContent => {
                        // 显示推理过程
                        container.innerHTML = `
                            <div class="alert alert-success mb-3">
                                <p><i class="fas fa-check-circle"></i> <strong>已修复：</strong> 成功获取到推理过程。</p>
                                <p class="small">由统一推理过程修复脚本提供支持</p>
                            </div>
                            <div class="reasoning-text markdown-content">${formatMarkdown(reasoningContent)}</div>
                        `;
                        console.info('[九猫统一修复] 成功显示推理过程');

                        // 更新分析结果元数据，确保下次刷新页面时能直接显示
                        if (CONFIG.enableUpdateMetadata) {
                            updateResultMetadata(novelId, dimension, reasoningContent)
                                .then(result => {
                                    if (result.success) {
                                        console.info('[九猫统一修复] 元数据已更新，下次刷新页面将直接显示推理过程');
                                    }
                                });
                        }
                    })
                    .catch(error => {
                        console.error('[九猫统一修复] 获取推理过程失败:', error);

                        // 显示错误信息
                        container.innerHTML = `
                            <div class="alert alert-danger mb-3">
                                <p><i class="fas fa-exclamation-triangle"></i> <strong>修复失败：</strong> 无法获取推理过程。</p>
                                <p class="small">错误信息: ${error.message}</p>
                                <p class="small">系统可能未正确保存推理过程，或者API返回的数据格式有误。</p>
                                <button class="btn btn-sm btn-outline-primary mt-2 retry-button">重试</button>
                            </div>
                        `;

                        // 添加重试按钮事件
                        const retryButton = container.querySelector('.retry-button');
                        if (retryButton) {
                            retryButton.addEventListener('click', () => {
                                console.info('[九猫统一修复] 用户点击重试按钮');
                                fixReasoningContent();
                            });
                        }
                    });
            }
        });
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(fixReasoningContent, CONFIG.autoFixDelay);
        });
    } else {
        setTimeout(fixReasoningContent, CONFIG.autoFixDelay);
    }

    // 标记为已加载
    window.__nineCatsFixes.loaded['unified-reasoning-content-fix'] = true;

    console.log('[九猫统一修复] 统一推理过程修复脚本初始化完成');
})();
