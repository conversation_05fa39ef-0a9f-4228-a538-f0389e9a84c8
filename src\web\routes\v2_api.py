"""
九猫小说分析系统v2.0 API路由
"""
import logging
import traceback
from flask import Blueprint, request, jsonify
from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.analysis_result import AnalysisResult
from src.services.chapter_analysis_service import ChapterAnalysisService
import config

v2_api_bp = Blueprint('v2_api', __name__)
logger = logging.getLogger(__name__)

@v2_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content')
@v2_api_bp.route('/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content')  # 兼容旧路径
def api_get_chapter_reasoning_content(novel_id, chapter_id, dimension):
    """
    API: 获取章节分析的推理过程内容。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
    """
    try:
        # 获取请求参数
        full = request.args.get('full', 'false').lower() == 'true'

        # 记录详细的请求信息
        logger.info(f"收到章节推理过程请求: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, full={full}")
        logger.info(f"请求路径: {request.path}, 请求方法: {request.method}, 请求参数: {dict(request.args)}")

        # 直接从数据库查询章节分析结果
        session = Session()
        try:
            # 直接查询数据库
            logger.info(f"直接从数据库查询章节分析结果: chapter_id={chapter_id}, dimension={dimension}")
            result_db = session.query(ChapterAnalysisResult).filter(
                ChapterAnalysisResult.chapter_id == chapter_id,
                ChapterAnalysisResult.dimension == dimension
            ).first()

            if result_db:
                logger.info(f"找到章节分析结果: id={result_db.id}, chapter_id={result_db.chapter_id}, dimension={result_db.dimension}")

                # 检查是否有推理过程内容
                if result_db.reasoning_content:
                    reasoning_content = result_db.reasoning_content
                    logger.info(f"从数据库直接查询到推理过程内容，长度: {len(reasoning_content)}")

                    # 确保推理过程内容格式正确
                    try:
                        # 导入格式化函数
                        from reasoning_content_template import format_reasoning_content

                        # 格式化推理过程内容
                        formatted_reasoning = format_reasoning_content(reasoning_content)
                        logger.info(f"推理过程内容已格式化，原长度: {len(reasoning_content)}，新长度: {len(formatted_reasoning)}")

                        # 使用格式化后的内容
                        reasoning_content = formatted_reasoning
                    except Exception as e:
                        # 如果格式化失败，记录错误但继续使用原始内容
                        logger.error(f"格式化推理过程内容时出错: {str(e)}")
                        logger.error(traceback.format_exc())

                    # 返回推理过程
                    logger.info(f"返回推理过程内容，长度: {len(reasoning_content)}")
                    return jsonify({
                        "success": True,
                        "reasoning_content": reasoning_content,
                        "source": "chapter_analysis_result.reasoning_content",
                        "full": full
                    })
                else:
                    # 如果没有推理过程内容，尝试从元数据中获取
                    logger.info(f"数据库中没有推理过程内容，尝试从元数据中获取")
                    if result_db.analysis_metadata and isinstance(result_db.analysis_metadata, dict) and 'reasoning_content' in result_db.analysis_metadata:
                        reasoning_content = result_db.analysis_metadata['reasoning_content']
                        logger.info(f"从元数据中获取到推理过程内容，长度: {len(reasoning_content)}")

                        # 格式化推理过程内容
                        try:
                            from reasoning_content_template import format_reasoning_content
                            formatted_reasoning = format_reasoning_content(reasoning_content)
                            reasoning_content = formatted_reasoning
                        except Exception as e:
                            logger.error(f"格式化从元数据获取的推理过程内容时出错: {str(e)}")

                        # 返回推理过程
                        logger.info(f"返回从元数据获取的推理过程内容，长度: {len(reasoning_content)}")
                        return jsonify({
                            "success": True,
                            "reasoning_content": reasoning_content,
                            "source": "chapter_analysis_result.metadata.reasoning_content",
                            "full": full
                        })

            # 如果没有找到分析结果或没有推理过程内容，尝试使用ChapterAnalysisService
            logger.info(f"数据库中没有找到推理过程内容，尝试使用ChapterAnalysisService")
            result = ChapterAnalysisService.get_chapter_analysis_result(chapter_id, dimension)

            # 记录日志，帮助调试
            logger.info(f"ChapterAnalysisService返回结果: success={result.get('success', False)}")

            # 如果找到分析结果，尝试提取推理过程
            if result.get('success', False):
                analysis_result = result.get('result', {})
                reasoning_content = analysis_result.get('reasoning_content', '')
                logger.info(f"从ChapterAnalysisService结果中提取推理过程，长度: {len(reasoning_content) if reasoning_content else 0}")

                if reasoning_content:
                    # 格式化推理过程内容
                    try:
                        from reasoning_content_template import format_reasoning_content
                        formatted_reasoning = format_reasoning_content(reasoning_content)
                        reasoning_content = formatted_reasoning
                    except Exception as e:
                        logger.error(f"格式化从ChapterAnalysisService获取的推理过程内容时出错: {str(e)}")

                    # 返回推理过程
                    logger.info(f"返回从ChapterAnalysisService获取的推理过程内容，长度: {len(reasoning_content)}")
                    return jsonify({
                        "success": True,
                        "reasoning_content": reasoning_content,
                        "source": "chapter_analysis_service",
                        "full": full
                    })

            # 如果仍然没有推理过程内容，尝试使用修复API生成
            logger.warning(f"所有方法都未找到推理过程内容，尝试使用修复API生成")
            try:
                # 构建修复API URL
                fix_url = f"/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/generate_reasoning"
                logger.info(f"使用修复API URL: {fix_url}")

                # 使用内部重定向调用修复API
                from flask import current_app
                with current_app.test_client() as client:
                    response = client.get(fix_url)

                    if response.status_code == 200:
                        fix_data = response.get_json()
                        if fix_data and fix_data.get('success') and fix_data.get('reasoning_content'):
                            reasoning_content = fix_data['reasoning_content']
                            logger.info(f"成功使用修复API生成推理过程，长度: {len(reasoning_content)}")

                            # 格式化推理过程内容
                            try:
                                from reasoning_content_template import format_reasoning_content
                                formatted_reasoning = format_reasoning_content(reasoning_content)
                                reasoning_content = formatted_reasoning
                            except Exception as e:
                                logger.error(f"格式化从修复API获取的推理过程内容时出错: {str(e)}")

                            # 返回推理过程
                            logger.info(f"返回从修复API获取的推理过程内容，长度: {len(reasoning_content)}")
                            return jsonify({
                                "success": True,
                                "reasoning_content": reasoning_content,
                                "source": "fix_api",
                                "full": full
                            })
                        else:
                            logger.warning(f"修复API返回成功但没有推理内容: {fix_data}")
                    else:
                        logger.warning(f"修复API调用失败，状态码: {response.status_code}")
            except Exception as fix_error:
                logger.error(f"调用修复API时出错: {str(fix_error)}")
                logger.error(traceback.format_exc())

            # 如果所有方法都失败，返回错误
            logger.warning(f"所有方法都未能获取推理过程内容")
            return jsonify({
                "success": False,
                "error": "未找到推理过程内容",
                "status": 404
            }), 404
        except Exception as e:
            logger.error(f"获取章节推理过程时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({
                "success": False,
                "error": f"获取章节推理过程时出错: {str(e)}",
                "status": 500
            }), 500
        finally:
            session.close()
    except Exception as e:
        logger.error(f"API获取章节推理过程时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e),
            "status": 500
        }), 500

@v2_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
@v2_api_bp.route('/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')  # 兼容旧路径
def api_get_chapter_analysis(novel_id, chapter_id, dimension):
    """
    API: 获取章节分析结果。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
    """
    try:
        # 获取分析结果
        result = ChapterAnalysisService.get_chapter_analysis_result(chapter_id, dimension)

        # 记录日志，帮助调试
        logger.info(f"获取章节 {chapter_id} 的 {dimension} 分析结果: {result['success']}")

        # 如果没有找到分析结果，尝试创建一个空结果
        if not result['success'] and "未找到分析结果" in result.get('error', ''):
            # 获取维度名称
            dimension_name = dimension
            for dim in config.ANALYSIS_DIMENSIONS:
                if dim.get('key') == dimension:
                    dimension_name = dim.get('name', dimension)
                    break

            # 创建一个空的分析结果
            empty_result = {
                "success": True,
                "result": {
                    "id": None,
                    "chapter_id": chapter_id,
                    "dimension": dimension,
                    "content": f"<div class='alert alert-info'>该章节尚未进行{dimension_name}分析，请先进行分析。</div>",
                    "metadata": {}
                }
            }
            return jsonify(empty_result)

        return jsonify(result)
    except Exception as e:
        logger.error(f"API获取章节分析结果时出错: {str(e)}")
        return jsonify({"success": False, "error": str(e)})

@v2_api_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>')
@v2_api_bp.route('/api/novels/<int:novel_id>/analysis/<dimension>')  # 兼容旧路径
def api_get_novel_analysis(novel_id, dimension):
    """
    API: 获取小说分析结果。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    try:
        # 获取分析结果
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                # 获取维度名称
                dimension_name = dimension
                for dim in config.ANALYSIS_DIMENSIONS:
                    if dim.get('key') == dimension:
                        dimension_name = dim.get('name', dimension)
                        break

                # 返回一个空的分析结果
                return jsonify({
                    "success": False,
                    "error": "未找到分析结果",
                    "message": f"该小说尚未进行{dimension_name}分析，请先进行分析。",
                    "status": 404
                }), 404

            # 转换为字典，确保元数据可以序列化为JSON
            # 处理元数据，确保可以序列化为JSON
            metadata = result.metadata
            if metadata and not isinstance(metadata, dict):
                try:
                    # 尝试将元数据转换为字典
                    metadata = dict(metadata)
                except:
                    # 如果无法转换，则使用字符串表示
                    metadata = str(metadata)

            result_dict = {
                "id": result.id,
                "novel_id": result.novel_id,
                "dimension": result.dimension,
                "content": result.content,
                "metadata": metadata,
                "created_at": result.created_at.isoformat() if result.created_at else None,
                "updated_at": result.updated_at.isoformat() if result.updated_at else None
            }

            return jsonify({
                "success": True,
                "result": result_dict
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"API获取小说分析结果时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})

@v2_api_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content')
@v2_api_bp.route('/api/novels/<int:novel_id>/analysis/<dimension>/reasoning_content')  # 兼容旧路径
def api_get_novel_reasoning_content(novel_id, dimension):
    """
    API: 获取小说分析的推理过程内容。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    try:
        # 获取请求参数
        full = request.args.get('full', 'false').lower() == 'true'

        # 获取分析结果
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                return jsonify({
                    "success": False,
                    "error": "未找到分析结果",
                    "status": 404
                }), 404

            # 从分析结果中提取推理过程
            reasoning_content = ''

            # 首先尝试从reasoning_content字段获取
            if hasattr(result, 'reasoning_content') and result.reasoning_content:
                reasoning_content = result.reasoning_content
                logger.info(f"从reasoning_content字段获取到推理过程内容，长度: {len(reasoning_content)}")
            # 然后尝试从metadata中获取reasoning_content
            elif hasattr(result, 'metadata') and result.metadata:
                metadata = result.metadata
                # 处理元数据，确保可以序列化为JSON
                if not isinstance(metadata, dict):
                    try:
                        # 尝试将元数据转换为字典
                        metadata = dict(metadata)
                    except:
                        # 如果无法转换，则使用字符串表示
                        metadata = str(metadata)

                if isinstance(metadata, dict) and 'reasoning_content' in metadata:
                    reasoning_content = metadata['reasoning_content']
                    logger.info(f"从metadata.reasoning_content字段获取到推理过程内容，长度: {len(reasoning_content)}")

            # 如果仍然没有推理过程，返回一个明确的错误信息
            if not reasoning_content:
                # 获取维度名称
                dimension_name = dimension
                for dim in config.ANALYSIS_DIMENSIONS:
                    if dim.get('key') == dimension:
                        dimension_name = dim.get('name', dimension)
                        break

                # 返回错误信息
                logger.warning(f"未找到{dimension_name}的推理过程内容")
                return jsonify({
                    "success": False,
                    "error": f"未找到{dimension_name}的推理过程内容",
                    "message": "请尝试重新分析该维度，以获取完整的推理过程。",
                    "status": 404
                }), 404

            # 确保推理过程内容格式正确
            try:
                # 导入格式化函数
                from reasoning_content_template import format_reasoning_content

                # 格式化推理过程内容
                formatted_reasoning = format_reasoning_content(reasoning_content)
                logger.info(f"格式化推理过程内容，长度: {len(reasoning_content)}")

                # 使用格式化后的内容
                reasoning_content = formatted_reasoning
            except Exception as e:
                # 如果格式化失败，记录错误但继续使用原始内容
                logger.error(f"格式化推理过程内容时出错: {str(e)}")
                logger.error(traceback.format_exc())

            # 返回推理过程
            return jsonify({
                "success": True,
                "reasoning_content": reasoning_content,
                "source": "metadata.reasoning_content字段",
                "full": full
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"API获取小说推理过程时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})

@v2_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content')
@v2_api_bp.route('/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content')  # 兼容旧路径
def api_get_chapter_reasoning_content(novel_id, chapter_id, dimension):
    """
    API: 获取章节分析的推理过程内容。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
    """
    try:
        # 获取请求参数
        full = request.args.get('full', 'false').lower() == 'true'

        # 获取章节分析服务
        chapter_analysis_service = ChapterAnalysisService()

        # 获取章节分析结果
        result, reasoning_content = chapter_analysis_service.get_chapter_analysis_result_with_reasoning(
            novel_id, chapter_id, dimension
        )

        if not result:
            return jsonify({
                "success": False,
                "error": "未找到章节分析结果",
                "status": 404
            }), 404

        if not reasoning_content:
            # 获取维度名称
            dimension_name = dimension
            for dim in config.ANALYSIS_DIMENSIONS:
                if dim.get('key') == dimension:
                    dimension_name = dim.get('name', dimension)
                    break

            # 创建一个默认的推理过程内容
            default_reasoning = f"""# 章节{chapter_id} {dimension_name}分析推理过程

该章节的{dimension_name}分析结果没有记录详细的推理过程。这可能是因为：

1. 该分析是在启用推理过程记录功能之前进行的
2. 分析过程中出现了错误，导致推理过程未能正确保存
3. 该维度的分析使用了简化流程，没有生成详细的推理过程

您可以尝试重新分析该章节的该维度，以获取完整的推理过程。
"""

            return jsonify({
                "success": True,
                "reasoning_content": default_reasoning,
                "source": "default_template",
                "full": full
            })

        # 记录推理过程内容长度
        logger.info(f"章节 {chapter_id} 的 {dimension} 推理过程内容长度: {len(reasoning_content)}字符")

        # 确保推理过程内容格式正确
        try:
            # 导入格式化函数
            from reasoning_content_template import format_reasoning_content

            # 格式化推理过程内容
            formatted_reasoning = format_reasoning_content(reasoning_content)

            # 使用格式化后的内容
            reasoning_content = formatted_reasoning
        except Exception as e:
            # 如果格式化失败，记录错误但继续使用原始内容
            logger.error(f"格式化章节推理过程内容时出错: {str(e)}")
            logger.error(traceback.format_exc())

        # 返回推理过程
        return jsonify({
            "success": True,
            "reasoning_content": reasoning_content,
            "source": "chapter_analysis_result",
            "full": full
        })
    except Exception as e:
        logger.error(f"API获取章节推理过程时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})
