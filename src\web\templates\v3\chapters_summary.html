{% extends "v3/base.html" %}

{% block title %}章节分析汇总 - {{ novel.title }} - 九猫小说分析写作系统v3.0{% endblock %}

{% block extra_css %}
<style>
    .summary-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px var(--shadow-color);
        position: relative;
        overflow: hidden;
    }

    .summary-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .summary-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    }

    .summary-meta {
        position: relative;
        z-index: 1;
        opacity: 0.9;
    }

    .summary-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .summary-table th,
    .summary-table td {
        padding: 0.75rem;
        text-align: center;
        border: 1px solid var(--border-color);
    }

    .summary-table th {
        background-color: var(--primary-light);
        color: var(--dark-color);
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .summary-table tr:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .summary-table tr:hover {
        background-color: var(--shadow-color);
    }

    .summary-table-container {
        max-height: 600px;
        overflow-y: auto;
        border-radius: 0.5rem;
        box-shadow: 0 4px 15px var(--shadow-color);
    }

    .analysis-status {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: inline-block;
        border: 2px solid var(--dark-color);
    }

    .analysis-status.completed {
        background-color: var(--success-color);
        border-color: var(--success-color);
    }

    .analysis-status.not-completed {
        background-color: var(--primary-color);
        border-color: var(--primary-dark);
    }

    .chapter-title {
        font-weight: 600;
        text-align: left;
    }

    .dimension-header {
        writing-mode: vertical-lr;
        white-space: nowrap;
        padding: 1rem 0.5rem;
        min-height: 150px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 汇总头部信息 -->
<div class="summary-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">章节分析汇总</h1>
            <div class="summary-meta">
                <p class="lead mb-2">
                    <i class="fas fa-book me-2"></i>{{ novel.title }}
                </p>
                <p class="mb-0">
                    <i class="fas fa-user me-2"></i>{{ novel.author or '未知作者' }}
                </p>
            </div>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3.view_novel', novel_id=novel.id) }}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-1"></i>返回小说
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 汇总表格 -->
<div class="card shadow-sm">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0"><i class="fas fa-table me-2"></i>章节分析汇总表</h3>
            <div>
                <span class="me-3">
                    <span class="analysis-status completed me-1"></span>
                    已完成
                </span>
                <span>
                    <span class="analysis-status not-completed me-1"></span>
                    未完成
                </span>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="summary-table-container">
            <table class="summary-table">
                <thead>
                    <tr>
                        <th style="min-width: 200px;">章节</th>
                        {% for dimension in dimensions %}
                        <th class="dimension-header" title="{{ dimension.description }}">{{ dimension.name }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for chapter in chapters %}
                    <tr>
                        <td class="chapter-title">
                            <a href="{{ url_for('v3.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}">
                                {{ chapter.title }}
                            </a>
                        </td>
                        {% for dimension in dimensions %}
                        <td>
                            {% set chapter_results = chapter_analysis_results.get(chapter.id, {}) %}
                            {% if dimension.key in chapter_results %}
                            <div class="d-flex justify-content-center gap-2">
                                <a href="{{ url_for('v3.chapter_analysis', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" title="查看分析结果">
                                    <span class="analysis-status completed"></span>
                                </a>
                                <a href="#" class="delete-chapter-analysis-btn"
                                   data-novel-id="{{ novel.id }}"
                                   data-chapter-id="{{ chapter.id }}"
                                   data-dimension="{{ dimension.key }}"
                                   data-dimension-name="{{ dimension.name }}"
                                   title="删除分析结果">
                                    <i class="fas fa-trash text-danger"></i>
                                </a>
                            </div>
                            {% else %}
                            <a href="#" class="start-chapter-analysis-btn" data-chapter-id="{{ chapter.id }}" data-dimension="{{ dimension.key }}" title="开始分析">
                                <span class="analysis-status not-completed"></span>
                            </a>
                            {% endif %}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分析统计 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>分析完成率</h3>
            </div>
            <div class="card-body">
                <canvas id="completionRateChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>维度分析统计</h3>
            </div>
            <div class="card-body">
                <canvas id="dimensionStatsChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // 删除章节分析按钮点击事件
        $('.delete-chapter-analysis-btn').click(function(e) {
            e.preventDefault();
            const novelId = $(this).data('novel-id');
            const chapterId = $(this).data('chapter-id');
            const dimension = $(this).data('dimension');
            const dimensionName = $(this).data('dimension-name');

            if (!novelId || !chapterId || !dimension) {
                console.error('缺少必要的数据属性');
                return;
            }

            if (confirm(`确定要删除章节的"${dimensionName}"分析结果吗？此操作不可恢复。`)) {
                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-danger mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在删除分析结果，请稍候...</h5>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);

                // 调用API删除章节分析结果
                $.ajax({
                    url: `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/delete`,
                    type: 'POST',
                    success: function(response) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        if (response.success) {
                            alert(`成功删除章节的"${dimensionName}"分析结果`);
                            // 刷新页面
                            location.reload();
                        } else {
                            alert(`删除分析结果失败: ${response.message || '未知错误'}`);

                            // 尝试使用备用API路径
                            $.ajax({
                                url: `/api/novels/${novelId}/chapter/${chapterId}/analysis/${dimension}/delete`,
                                type: 'POST',
                                success: function(backupResponse) {
                                    if (backupResponse.success) {
                                        alert(`成功删除章节的"${dimensionName}"分析结果`);
                                        // 刷新页面
                                        location.reload();
                                    } else {
                                        alert(`删除分析结果失败: ${backupResponse.message || '未知错误'}`);
                                    }
                                },
                                error: function(xhr) {
                                    alert(`所有API路径均失败，无法删除分析结果: ${xhr.status} ${xhr.statusText}`);
                                }
                            });
                        }
                    },
                    error: function(xhr) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        alert(`删除分析结果失败: ${xhr.status} ${xhr.statusText}`);

                        // 尝试使用备用API路径
                        $.ajax({
                            url: `/api/novels/${novelId}/chapter/${chapterId}/analysis/${dimension}/delete`,
                            type: 'POST',
                            success: function(backupResponse) {
                                if (backupResponse.success) {
                                    alert(`成功删除章节的"${dimensionName}"分析结果`);
                                    // 刷新页面
                                    location.reload();
                                }
                            }
                        });
                    }
                });
            }
        });
        // 计算分析完成率数据
        const totalAnalyses = {{ chapters|length }} * {{ dimensions|length }};
        let completedAnalyses = 0;

        {% for chapter in chapters %}
            {% set chapter_results = chapter_analysis_results.get(chapter.id, {}) %}
            completedAnalyses += {{ chapter_results|length }};
        {% endfor %}

        const completionRate = (completedAnalyses / totalAnalyses) * 100;
        const incompletionRate = 100 - completionRate;

        // 创建分析完成率图表
        const completionRateCtx = document.getElementById('completionRateChart').getContext('2d');
        const completionRateChart = new Chart(completionRateCtx, {
            type: 'doughnut',
            data: {
                labels: ['已完成', '未完成'],
                datasets: [{
                    data: [completedAnalyses, totalAnalyses - completedAnalyses],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(108, 117, 125, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // 计算维度分析统计数据
        const dimensionStats = {};
        {% for dimension in dimensions %}
            dimensionStats['{{ dimension.name }}'] = 0;
        {% endfor %}

        {% for chapter in chapters %}
            {% set chapter_results = chapter_analysis_results.get(chapter.id, {}) %}
            {% for dimension in dimensions %}
                {% if dimension.key in chapter_results %}
                    dimensionStats['{{ dimension.name }}'] += 1;
                {% endif %}
            {% endfor %}
        {% endfor %}

        // 创建维度分析统计图表
        const dimensionStatsCtx = document.getElementById('dimensionStatsChart').getContext('2d');
        const dimensionStatsChart = new Chart(dimensionStatsCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(dimensionStats),
                datasets: [{
                    label: '已完成章节数',
                    data: Object.values(dimensionStats),
                    backgroundColor: 'rgba(230, 180, 34, 0.7)',
                    borderColor: 'rgba(230, 180, 34, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: {{ chapters|length }},
                        ticks: {
                            stepSize: 1
                        }
                    },
                    x: {
                        ticks: {
                            autoSkip: false,
                            maxRotation: 90,
                            minRotation: 90
                        }
                    }
                }
            }
        });

        // 点击开始分析按钮
        $('.start-chapter-analysis-btn').click(function(e) {
            e.preventDefault();
            const chapterId = $(this).data('chapter-id');
            const dimension = $(this).data('dimension');

            if (confirm(`确定要开始分析该章节的${dimension}维度吗？`)) {
                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在开始分析，请稍候...</h5>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);

                // 发送AJAX请求
                $.ajax({
                    url: '/api/v3/start_chapter_analysis',
                    type: 'POST',
                    data: JSON.stringify({
                        novel_id: {{ novel.id }},
                        chapter_id: chapterId,
                        dimensions: [dimension]
                    }),
                    contentType: 'application/json',
                    success: function(response) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        if (response.success) {
                            alert('分析已开始，请稍后刷新页面查看结果');
                            // 刷新页面
                            location.reload();
                        } else {
                            alert('开始分析失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        alert('开始分析失败: ' + error);
                    }
                });
            }
        });
    });
</script>
{% endblock %}
