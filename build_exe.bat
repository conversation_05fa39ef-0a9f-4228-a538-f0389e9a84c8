@echo off
echo Installing dependencies...
pip install -r requirements.txt
pip install cx_Freeze

echo Creating necessary directories...
if not exist "uploads" mkdir uploads

echo Packaging the Nine Cats Novel Analysis System...
python setup.py build

echo Packaging completed!
echo The executable file is located in the build\exe.win-amd64-3.x\NineCats directory
echo Please run the NineCats.exe file in that directory

pause
