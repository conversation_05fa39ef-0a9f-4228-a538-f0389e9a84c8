"""
九猫系统高级并行分析测试脚本
测试不同小说大小和不同维度组合的性能表现
"""
import os
import sys
import time
import logging
import random
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

import config
from src.models.novel import Novel
from src.api.analysis import NovelAnalyzer
from src.api.deepseek_client import DeepSeekClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("advanced_parallel_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("advanced_parallel_test")

# 测试小说大小配置
NOVEL_SIZES = {
    "small": 10000,    # 约1万字
    "medium": 50000,   # 约5万字
    "large": 100000,   # 约10万字
    "xlarge": 200000   # 约20万字
}

# 测试维度组合 - 临时修改为只包含语言风格
DIMENSION_SETS = {
    "minimal": ["language_style"],
    "basic": ["language_style"],
    "standard": ["language_style"],
    "full": config.ANALYSIS_DIMENSIONS  # 所有维度 (当前只有language_style)
}

def generate_test_novel(size_name):
    """生成指定大小的测试小说"""
    size = NOVEL_SIZES.get(size_name, 10000)
    logger.info(f"生成{size_name}大小的测试小说 ({size}字符)")

    # 生成随机段落
    paragraphs = []
    remaining_chars = size

    # 段落模板，用于生成更真实的文本
    paragraph_templates = [
        "这是一个关于{subject}的故事，主人公{name}在{place}经历了{event}。{detail}",
        "{name}站在{place}，思考着{thought}。{detail}",
        "{place}的景色十分美丽，{detail}。{name}不禁感叹道："{quote}"",
        "当{name}来到{place}时，{event}。这让他想起了{memory}。",
        "{time}，{place}发生了{event}，{name}不得不{action}。"
    ]

    subjects = ["冒险", "爱情", "友谊", "成长", "奋斗", "家庭", "战争", "和平"]
    names = ["小明", "小红", "老王", "张三", "李四", "王五", "赵六", "钱七"]
    places = ["山顶", "海边", "城市", "乡村", "森林", "沙漠", "学校", "办公室", "家中", "公园"]
    events = ["遇见了老朋友", "发现了一个秘密", "经历了一场冒险", "解决了一个难题", "做出了重要决定"]
    details = [
        "阳光照在脸上，温暖而舒适。",
        "微风轻拂，带来一丝凉意。",
        "远处传来鸟叫声，清脆悦耳。",
        "空气中弥漫着花香，令人心旷神怡。",
        "雨水打在窗户上，发出节奏感的声音。"
    ]
    thoughts = ["过去的经历", "未来的计划", "人生的意义", "世界的奥秘", "情感的复杂性"]
    quotes = [
        "生活不仅是眼前的苟且，还有诗和远方。",
        "人生如梦，岁月如歌。",
        "世界上最远的距离不是生与死，而是我站在你面前，你却不知道我爱你。",
        "不经一番寒彻骨，怎得梅花扑鼻香。",
        "天行健，君子以自强不息；地势坤，君子以厚德载物。"
    ]
    times = ["清晨", "正午", "黄昏", "深夜", "凌晨", "春天", "夏天", "秋天", "冬天"]
    memories = ["童年的快乐时光", "青春的懵懂岁月", "初恋的甜蜜回忆", "奋斗的艰辛历程", "成功的喜悦瞬间"]
    actions = ["面对挑战", "寻求帮助", "重新思考", "做出抉择", "踏上旅程"]

    while remaining_chars > 0:
        template = random.choice(paragraph_templates)
        paragraph = template.format(
            subject=random.choice(subjects),
            name=random.choice(names),
            place=random.choice(places),
            event=random.choice(events),
            detail=random.choice(details),
            thought=random.choice(thoughts),
            quote=random.choice(quotes),
            time=random.choice(times),
            memory=random.choice(memories),
            action=random.choice(actions)
        )

        # 确保不超过总字符数
        if len(paragraph) <= remaining_chars:
            paragraphs.append(paragraph)
            remaining_chars -= len(paragraph)
        else:
            # 如果当前段落太长，生成一个更短的
            short_para = "这是一个测试段落。" * (remaining_chars // 10)
            paragraphs.append(short_para[:remaining_chars])
            remaining_chars = 0

    content = "\n\n".join(paragraphs)

    # 创建小说对象
    test_novel = Novel(
        title=f"测试小说-{size_name}",
        content=content,
        author="测试作者",
        file_path=f"test_novel_{size_name}.txt"
    )
    # 手动设置ID
    test_novel.id = random.randint(1000, 9999)

    # 保存到文件以便查看
    with open(f"test_novel_{size_name}.txt", "w", encoding="utf-8") as f:
        f.write(content)

    return test_novel

def run_test_case(novel_size, dimension_set, parallel=True, max_workers=3, max_chunk_workers=2):
    """运行单个测试用例"""
    test_name = f"{novel_size}-{dimension_set}-{'parallel' if parallel else 'serial'}"
    logger.info(f"=== 开始测试: {test_name} ===")

    # 生成测试小说
    novel = generate_test_novel(novel_size)

    # 获取测试维度
    dimensions = DIMENSION_SETS[dimension_set]

    # 创建分析器
    analyzer = NovelAnalyzer()

    # 设置并行分析配置
    config.PARALLEL_ANALYSIS_ENABLED = parallel
    config.MAX_PARALLEL_ANALYSES = max_workers
    config.MAX_CHUNK_WORKERS = max_chunk_workers

    # 启用调试模式，避免实际调用API
    config.DEBUG = True

    # 运行分析
    start_time = time.time()
    if parallel:
        results = analyzer.analyze_novel_parallel(novel, dimensions, max_workers=max_workers)
    else:
        results = analyzer.analyze_novel(novel, dimensions)

    elapsed_time = time.time() - start_time

    # 记录结果
    result_info = {
        "test_name": test_name,
        "novel_size": novel_size,
        "novel_chars": len(novel.content),
        "dimension_set": dimension_set,
        "dimensions": dimensions,
        "dimension_count": len(dimensions),
        "parallel": parallel,
        "max_workers": max_workers,
        "max_chunk_workers": max_chunk_workers,
        "elapsed_time": elapsed_time,
        "results_count": len(results),
        "timestamp": datetime.now().isoformat()
    }

    logger.info(f"测试 {test_name} 完成，耗时: {elapsed_time:.2f}秒")
    return result_info

def run_all_tests():
    """运行所有测试组合"""
    logger.info("=== 开始运行所有测试组合 ===")

    all_results = []

    # 使用线程池并行运行测试
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = []

        # 添加所有测试组合
        for novel_size in NOVEL_SIZES.keys():
            for dimension_set in DIMENSION_SETS.keys():
                # 串行测试
                futures.append(executor.submit(
                    run_test_case, novel_size, dimension_set, False
                ))

                # 并行测试 - 标准配置
                futures.append(executor.submit(
                    run_test_case, novel_size, dimension_set, True, 3, 2
                ))

                # 并行测试 - 高并行配置
                futures.append(executor.submit(
                    run_test_case, novel_size, dimension_set, True, 5, 3
                ))

        # 收集结果
        for future in futures:
            try:
                result = future.result()
                all_results.append(result)
            except Exception as e:
                logger.error(f"测试执行出错: {str(e)}")

    # 保存结果到JSON文件
    with open("parallel_test_results.json", "w", encoding="utf-8") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)

    # 分析结果
    analyze_test_results(all_results)

    logger.info("=== 所有测试完成 ===")
    return all_results

def analyze_test_results(results):
    """分析测试结果，计算加速比等指标"""
    logger.info("=== 分析测试结果 ===")

    # 按小说大小和维度集分组
    grouped_results = {}
    for result in results:
        key = f"{result['novel_size']}-{result['dimension_set']}"
        if key not in grouped_results:
            grouped_results[key] = []
        grouped_results[key].append(result)

    # 计算每组的加速比
    summary = []
    for key, group in grouped_results.items():
        serial_result = next((r for r in group if not r['parallel']), None)
        if not serial_result:
            continue

        serial_time = serial_result['elapsed_time']

        for result in group:
            if result['parallel']:
                speedup = serial_time / result['elapsed_time']
                efficiency = speedup / result['max_workers']

                summary_item = {
                    "test_group": key,
                    "novel_size": result['novel_size'],
                    "dimension_set": result['dimension_set'],
                    "parallel_config": f"{result['max_workers']}-{result['max_chunk_workers']}",
                    "serial_time": serial_time,
                    "parallel_time": result['elapsed_time'],
                    "speedup": speedup,
                    "efficiency": efficiency
                }
                summary.append(summary_item)

                logger.info(f"测试组 {key}, 配置 {result['max_workers']}-{result['max_chunk_workers']}: "
                           f"串行时间={serial_time:.2f}秒, 并行时间={result['elapsed_time']:.2f}秒, "
                           f"加速比={speedup:.2f}x, 效率={efficiency:.2f}")

    # 保存摘要到JSON文件
    with open("parallel_test_summary.json", "w", encoding="utf-8") as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)

    # 生成简单的文本报告
    with open("parallel_test_report.txt", "w", encoding="utf-8") as f:
        f.write("九猫系统并行分析性能测试报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("测试结果摘要:\n")
        f.write("-" * 50 + "\n")

        # 按小说大小分组
        for size in NOVEL_SIZES.keys():
            f.write(f"\n小说大小: {size} ({NOVEL_SIZES[size]}字符)\n")

            size_results = [r for r in summary if r['novel_size'] == size]
            for result in size_results:
                f.write(f"  维度集: {result['dimension_set']} ({len(DIMENSION_SETS[result['dimension_set']])}个维度)\n")
                f.write(f"    并行配置: {result['parallel_config']}\n")
                f.write(f"    串行时间: {result['serial_time']:.2f}秒\n")
                f.write(f"    并行时间: {result['parallel_time']:.2f}秒\n")
                f.write(f"    加速比: {result['speedup']:.2f}x\n")
                f.write(f"    效率: {result['efficiency']:.2f}\n\n")

        # 最佳配置建议
        f.write("\n最佳配置建议:\n")
        f.write("-" * 50 + "\n")

        # 按小说大小找出最佳配置
        for size in NOVEL_SIZES.keys():
            size_results = [r for r in summary if r['novel_size'] == size]
            if size_results:
                best_result = max(size_results, key=lambda x: x['speedup'])
                f.write(f"小说大小 {size}: 最佳配置为 {best_result['parallel_config']}, "
                       f"加速比 {best_result['speedup']:.2f}x\n")

        f.write("\n结论与建议:\n")
        f.write("-" * 50 + "\n")
        f.write("1. 并行分析在所有测试场景中都能提供显著的性能提升\n")
        f.write("2. 对于较大的小说和更多的分析维度，并行效果更为明显\n")
        f.write("3. 建议根据服务器资源情况，选择适当的并行配置\n")
        f.write("4. 对于生产环境，建议定期进行性能测试，以确定最佳配置\n")

if __name__ == "__main__":
    # 运行所有测试
    run_all_tests()

    print("\n测试完成！详细结果请查看以下文件:")
    print("- advanced_parallel_test.log: 详细日志")
    print("- parallel_test_results.json: 原始测试数据")
    print("- parallel_test_summary.json: 测试结果摘要")
    print("- parallel_test_report.txt: 测试报告")
