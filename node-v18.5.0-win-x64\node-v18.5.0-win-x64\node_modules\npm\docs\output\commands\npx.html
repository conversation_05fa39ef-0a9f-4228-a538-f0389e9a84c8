<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npx</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="npx">npx</h1>
<span class="description">Run a command from a local or remote npm package</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#npx-vs-npm-exec"><code>npx</code> vs <code>npm exec</code></a></li><li><a href="#examples">Examples</a></li><li><a href="#compatibility-with-older-npx-versions">Compatibility with Older npx Versions</a></li><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<pre lang="bash"><code>npx -- &lt;pkg&gt;[@&lt;version&gt;] [args...]
npx --package=&lt;pkg&gt;[@&lt;version&gt;] -- &lt;cmd&gt; [args...]
npx -c '&lt;cmd&gt; [args...]'
npx --package=foo -c '&lt;cmd&gt; [args...]'
</code></pre>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h3 id="description">Description</h3>
<p>This command allows you to run an arbitrary command from an npm package
(either one installed locally, or fetched remotely), in a similar context
as running it via <code>npm run</code>.</p>
<p>Whatever packages are specified by the <code>--package</code> option will be
provided in the <code>PATH</code> of the executed command, along with any locally
installed package executables.  The <code>--package</code> option may be
specified multiple times, to execute the supplied command in an environment
where all specified packages are available.</p>
<p>If any requested packages are not present in the local project
dependencies, then they are installed to a folder in the npm cache, which
is added to the <code>PATH</code> environment variable in the executed process.  A
prompt is printed (which can be suppressed by providing either <code>--yes</code> or
<code>--no</code>).</p>
<p>Package names provided without a specifier will be matched with whatever
version exists in the local project.  Package names with a specifier will
only be considered a match if they have the exact same name and version as
the local dependency.</p>
<p>If no <code>-c</code> or <code>--call</code> option is provided, then the positional arguments
are used to generate the command string.  If no <code>--package</code> options
are provided, then npm will attempt to determine the executable name from
the package specifier provided as the first positional argument according
to the following heuristic:</p>
<ul>
<li>If the package has a single entry in its <code>bin</code> field in <code>package.json</code>,
or if all entries are aliases of the same command, then that command
will be used.</li>
<li>If the package has multiple <code>bin</code> entries, and one of them matches the
unscoped portion of the <code>name</code> field, then that command will be used.</li>
<li>If this does not result in exactly one option (either because there are
no bin entries, or none of them match the <code>name</code> of the package), then
<code>npm exec</code> exits with an error.</li>
</ul>
<p>To run a binary <em>other than</em> the named binary, specify one or more
<code>--package</code> options, which will prevent npm from inferring the package from
the first command argument.</p>
<h3 id="npx-vs-npm-exec"><code>npx</code> vs <code>npm exec</code></h3>
<p>When run via the <code>npx</code> binary, all flags and options <em>must</em> be set prior to
any positional arguments.  When run via <code>npm exec</code>, a double-hyphen <code>--</code>
flag can be used to suppress npm's parsing of switches and options that
should be sent to the executed command.</p>
<p>For example:</p>
<pre><code>$ npx foo@latest bar --package=@npmcli/foo
</code></pre>
<p>In this case, npm will resolve the <code>foo</code> package name, and run the
following command:</p>
<pre><code>$ foo bar --package=@npmcli/foo
</code></pre>
<p>Since the <code>--package</code> option comes <em>after</em> the positional arguments, it is
treated as an argument to the executed command.</p>
<p>In contrast, due to npm's argument parsing logic, running this command is
different:</p>
<pre><code>$ npm exec foo@latest bar --package=@npmcli/foo
</code></pre>
<p>In this case, npm will parse the <code>--package</code> option first, resolving the
<code>@npmcli/foo</code> package.  Then, it will execute the following command in that
context:</p>
<pre><code>$ foo@latest bar
</code></pre>
<p>The double-hyphen character is recommended to explicitly tell npm to stop
parsing command line options and switches.  The following command would
thus be equivalent to the <code>npx</code> command above:</p>
<pre><code>$ npm exec -- foo@latest bar --package=@npmcli/foo
</code></pre>
<h3 id="examples">Examples</h3>
<p>Run the version of <code>tap</code> in the local dependencies, with the provided
arguments:</p>
<pre><code>$ npm exec -- tap --bail test/foo.js
$ npx tap --bail test/foo.js
</code></pre>
<p>Run a command <em>other than</em> the command whose name matches the package name
by specifying a <code>--package</code> option:</p>
<pre><code>$ npm exec --package=foo -- bar --bar-argument
# ~ or ~
$ npx --package=foo bar --bar-argument
</code></pre>
<p>Run an arbitrary shell script, in the context of the current project:</p>
<pre><code>$ npm x -c 'eslint &amp;&amp; say "hooray, lint passed"'
$ npx -c 'eslint &amp;&amp; say "hooray, lint passed"'
</code></pre>
<h3 id="compatibility-with-older-npx-versions">Compatibility with Older npx Versions</h3>
<p>The <code>npx</code> binary was rewritten in npm v7.0.0, and the standalone <code>npx</code>
package deprecated at that time.  <code>npx</code> uses the <code>npm exec</code>
command instead of a separate argument parser and install process, with
some affordances to maintain backwards compatibility with the arguments it
accepted in previous versions.</p>
<p>This resulted in some shifts in its functionality:</p>
<ul>
<li>Any <code>npm</code> config value may be provided.</li>
<li>To prevent security and user-experience problems from mistyping package
names, <code>npx</code> prompts before installing anything.  Suppress this
prompt with the <code>-y</code> or <code>--yes</code> option.</li>
<li>The <code>--no-install</code> option is deprecated, and will be converted to <code>--no</code>.</li>
<li>Shell fallback functionality is removed, as it is not advisable.</li>
<li>The <code>-p</code> argument is a shorthand for <code>--parseable</code> in npm, but shorthand
for <code>--package</code> in npx.  This is maintained, but only for the <code>npx</code>
executable.</li>
<li>The <code>--ignore-existing</code> option is removed.  Locally installed bins are
always present in the executed process <code>PATH</code>.</li>
<li>The <code>--npm</code> option is removed.  <code>npx</code> will always use the <code>npm</code> it ships
with.</li>
<li>The <code>--node-arg</code> and <code>-n</code> options are removed.</li>
<li>The <code>--always-spawn</code> option is redundant, and thus removed.</li>
<li>The <code>--shell</code> option is replaced with <code>--script-shell</code>, but maintained
in the <code>npx</code> executable for backwards compatibility.</li>
</ul>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../commands/npm-run-script.html">npm run-script</a></li>
<li><a href="../using-npm/scripts.html">npm scripts</a></li>
<li><a href="../commands/npm-test.html">npm test</a></li>
<li><a href="../commands/npm-start.html">npm start</a></li>
<li><a href="../commands/npm-restart.html">npm restart</a></li>
<li><a href="../commands/npm-stop.html">npm stop</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../commands/npm-exec.html">npm exec</a></li>
</ul>
</div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npx.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>