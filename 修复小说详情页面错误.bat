@echo off
echo 九猫小说分析系统 - 小说详情页面错误修复工具
echo ======================================
echo 此工具将修复访问小说详情页面时出现的500内部服务器错误。
echo.

REM 设置Python执行路径
set PYTHON_PATH=python

REM 检查Python是否可用
%PYTHON_PATH% --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 找不到Python。请确保Python已安装并已添加到系统PATH中。
    echo 如果已安装Python，请尝试修改脚本中的PYTHON_PATH变量。
    goto :end
)

echo 正在执行修复脚本...
echo.

REM 运行修复脚本
%PYTHON_PATH% fix_novel_view_endpoint.py

echo.
echo 修复过程完成。
echo 请重启九猫系统以应用修复。

:end
pause 