/**
 * 九猫系统 - Bootstrap.min.js 重定向文件
 * 
 * 此文件用于解决bootstrap.min.js 404错误
 * 实际上重定向到bootstrap.bundle.min.js，因为系统使用的是bundle版本
 */

// 检查是否已加载bootstrap.bundle.min.js
if (typeof bootstrap === 'undefined') {
    console.log('bootstrap.min.js: 检测到bootstrap未加载，尝试加载bootstrap.bundle.min.js');
    
    // 创建脚本元素
    var script = document.createElement('script');
    
    // 尝试从本地加载
    script.src = '/static/js/lib/bootstrap.bundle.min.js';
    
    // 错误处理 - 尝试从备用位置加载
    script.onerror = function() {
        console.warn('无法从本地加载bootstrap.bundle.min.js，尝试从CDN加载');
        
        var cdnScript = document.createElement('script');
        cdnScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js';
        
        cdnScript.onerror = function() {
            console.error('无法从CDN加载bootstrap.bundle.min.js');
        };
        
        document.head.appendChild(cdnScript);
    };
    
    // 添加到文档
    document.head.appendChild(script);
} else {
    console.log('bootstrap.min.js: bootstrap已加载，无需重复加载');
}
