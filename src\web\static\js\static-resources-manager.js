/**
 * 九猫 - 静态资源管理器
 * 用于安全加载和管理静态资源（CSS、JS、图片等）
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('静态资源管理器已初始化');
    
    // 存储加载状态的资源映射
    const resourceMap = {
        css: {},
        js: {},
        img: {}
    };
    
    // 加载超时时间（毫秒）
    const LOAD_TIMEOUT = 10000;
    
    // CSS 资源列表
    const cssResources = [
        '/static/css/bootstrap.min.css',
        '/static/css/main.css'
    ];
    
    // JS 资源列表（按依赖顺序排列）
    const jsResources = [
        '/static/js/jquery.min.js',
        '/static/js/bootstrap.bundle.min.js',
        '/static/js/chart.min.js',
        '/static/js/main.js'
    ];
    
    // 检查资源是否已加载（在DOM中）
    function isResourceLoaded(type, url) {
        if (type === 'css') {
            return !!document.querySelector(`link[href*="${getResourceName(url)}"]`);
        } else if (type === 'js') {
            return !!document.querySelector(`script[src*="${getResourceName(url)}"]`);
        }
        return false;
    }
    
    // 从URL中提取资源名称
    function getResourceName(url) {
        return url.split('/').pop().split('?')[0];
    }
    
    // 创建加载超时处理
    function createLoadTimeout(type, url) {
        return setTimeout(() => {
            if (resourceMap[type][url] && resourceMap[type][url].status === 'loading') {
                console.warn(`${type.toUpperCase()} 资源加载超时: ${url}`);
                resourceMap[type][url].status = 'timeout';
                
                // 尝试重新加载
                if (!resourceMap[type][url].retryCount || resourceMap[type][url].retryCount < 2) {
                    const retryCount = (resourceMap[type][url].retryCount || 0) + 1;
                    console.log(`尝试重新加载 ${type} 资源 (${retryCount}/2): ${url}`);
                    
                    resourceMap[type][url].retryCount = retryCount;
                    loadResource(type, url);
                } else {
                    console.error(`${type.toUpperCase()} 资源加载失败，已达到最大重试次数: ${url}`);
                    resourceMap[type][url].status = 'failed';
                    
                    // 触发加载失败事件
                    const event = new CustomEvent('resourceLoadFailed', {
                        detail: { type, url }
                    });
                    window.dispatchEvent(event);
                    
                    // 对于关键资源，可以提供备用方案
                    handleCriticalResourceFailure(type, url);
                }
            }
        }, LOAD_TIMEOUT);
    }
    
    // 处理关键资源加载失败
    function handleCriticalResourceFailure(type, url) {
        if (type === 'js') {
            const resourceName = getResourceName(url);
            
            // 针对特定资源的处理
            if (resourceName === 'jquery.min.js') {
                // 尝试从CDN加载jQuery
                console.log('尝试从备用CDN加载jQuery');
                loadResource('js', 'https://code.jquery.com/jquery-3.6.4.min.js');
            } else if (resourceName === 'bootstrap.bundle.min.js') {
                // 尝试从CDN加载Bootstrap
                console.log('尝试从备用CDN加载Bootstrap');
                loadResource('js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js');
            } else if (resourceName === 'chart.min.js') {
                // 尝试从CDN加载Chart.js
                console.log('尝试从备用CDN加载Chart.js');
                loadResource('js', 'https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js');
            }
        } else if (type === 'css') {
            const resourceName = getResourceName(url);
            
            if (resourceName === 'bootstrap.min.css') {
                // 尝试从CDN加载Bootstrap CSS
                console.log('尝试从备用CDN加载Bootstrap CSS');
                loadResource('css', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
            }
        }
    }
    
    // 加载单个资源
    function loadResource(type, url) {
        // 如果资源已加载，则跳过
        if (isResourceLoaded(type, url)) {
            console.log(`${type.toUpperCase()} 资源已存在: ${url}`);
            resourceMap[type][url] = { status: 'loaded' };
            return Promise.resolve();
        }
        
        // 如果资源正在加载中，则返回现有promise
        if (resourceMap[type][url] && resourceMap[type][url].status === 'loading' && resourceMap[type][url].promise) {
            return resourceMap[type][url].promise;
        }
        
        // 创建加载promise
        const promise = new Promise((resolve, reject) => {
            try {
                let element;
                
                if (type === 'css') {
                    element = document.createElement('link');
                    element.rel = 'stylesheet';
                    element.href = url;
                } else if (type === 'js') {
                    element = document.createElement('script');
                    element.src = url;
                    element.async = false;  // 确保按顺序执行
                } else if (type === 'img') {
                    element = new Image();
                    element.src = url;
                } else {
                    throw new Error(`不支持的资源类型: ${type}`);
                }
                
                // 设置加载和错误处理
                element.onload = function() {
                    if (resourceMap[type][url]) {
                        clearTimeout(resourceMap[type][url].timeout);
                        resourceMap[type][url].status = 'loaded';
                    }
                    console.log(`${type.toUpperCase()} 资源加载成功: ${url}`);
                    resolve(element);
                    
                    // 触发资源加载完成事件
                    const event = new CustomEvent('resourceLoaded', {
                        detail: { type, url }
                    });
                    window.dispatchEvent(event);
                };
                
                element.onerror = function(error) {
                    if (resourceMap[type][url]) {
                        clearTimeout(resourceMap[type][url].timeout);
                        resourceMap[type][url].status = 'error';
                    }
                    console.error(`${type.toUpperCase()} 资源加载失败: ${url}`, error);
                    reject(error);
                    
                    // 尝试在错误时恢复
                    handleResourceError(type, url, element);
                };
                
                // 添加到文档
                if (type === 'css' || type === 'js') {
                    document.head.appendChild(element);
                }
                
                console.log(`${type.toUpperCase()} 资源加载请求: ${url}`);
                
                // 设置加载超时
                const timeout = createLoadTimeout(type, url);
                
                // 更新资源映射
                resourceMap[type][url] = {
                    element,
                    status: 'loading',
                    promise,
                    timeout
                };
            } catch (error) {
                console.error(`创建${type}元素出错: ${url}`, error);
                reject(error);
                
                if (resourceMap[type][url]) {
                    resourceMap[type][url].status = 'error';
                }
            }
        });
        
        if (resourceMap[type][url]) {
            resourceMap[type][url].promise = promise;
        } else {
            resourceMap[type][url] = { 
                status: 'loading',
                promise
            };
        }
        
        return promise;
    }
    
    // 处理资源加载错误
    function handleResourceError(type, url, element) {
        // 针对不同类型资源的错误处理
        if (type === 'css') {
            console.log(`尝试内联CSS来替代失败的样式表: ${url}`);
            // 这里可以添加重要的内联CSS
            const style = document.createElement('style');
            style.textContent = `
                /* 基本样式用作备用 */
                body { font-family: sans-serif; margin: 0; padding: 20px; }
                .container { max-width: 1200px; margin: 0 auto; }
                .btn { display: inline-block; padding: 8px 16px; background: #f0f0f0; border: 1px solid #ccc; cursor: pointer; }
                .card { border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; }
                .collapse { display: none; }
                .collapse.show { display: block; }
            `;
            document.head.appendChild(style);
        }
    }
    
    // 加载所有预定义的CSS资源
    function loadAllCSS() {
        return Promise.all(cssResources.map(url => loadResource('css', url)));
    }
    
    // 按顺序加载所有预定义的JS资源
    async function loadAllJS() {
        for (const url of jsResources) {
            try {
                await loadResource('js', url);
            } catch (error) {
                console.error(`JS资源加载序列中的错误: ${url}`, error);
                // 继续加载下一个脚本
            }
        }
    }
    
    // 初始化资源加载
    function init() {
        // 检查DOM是否已准备好
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startLoading);
        } else {
            startLoading();
        }
    }
    
    // 开始加载资源
    function startLoading() {
        console.log('开始加载静态资源');
        
        // 首先加载CSS，然后加载JS
        loadAllCSS()
            .then(() => {
                console.log('所有CSS资源加载完成');
                return loadAllJS();
            })
            .then(() => {
                console.log('所有JS资源加载完成');
                
                // 触发所有资源加载完成事件
                const event = new CustomEvent('allResourcesLoaded');
                window.dispatchEvent(event);
            })
            .catch(error => {
                console.error('资源加载过程中出错:', error);
            });
    }
    
    // 导出到全局
    window.ResourceManager = {
        loadResource,
        loadCSS: (url) => loadResource('css', url),
        loadJS: (url) => loadResource('js', url),
        loadImage: (url) => loadResource('img', url)
    };
    
    // 开始初始化
    init();
    
    console.log('静态资源管理器加载完成');
})(); 