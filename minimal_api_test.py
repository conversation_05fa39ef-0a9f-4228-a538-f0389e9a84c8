"""
最小化的API测试脚本
"""
import sys

print("Python版本:", sys.version)
print("正在导入requests库...")

try:
    import requests
    print("成功导入requests库")
except ImportError:
    print("导入requests库失败，请确保已安装: pip install requests")
    sys.exit(1)

print("正在测试API连接...")

# API密钥
API_KEY = "sk-9ff38bc35eaa44db82c4121bbba4f3b1"

# 设置API端点
endpoint = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

# 设置请求头
headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json",
    "Accept": "application/json"
}

# 设置请求体
payload = {
    "model": "deepseek-r1",
    "input": {
        "prompt": "Hello",
        "parameters": {
            "max_tokens": 10
        }
    }
}

try:
    print(f"发送请求到: {endpoint}")
    for i in range(3):  # 最多重试3次
        try:
            response = requests.post(endpoint, headers=headers, json=payload, timeout=320)
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            if response.status_code == 200:
                print("API连接成功!")
                break
            elif response.status_code == 500:
                print(f"服务器内部错误（500），第{i+1}次重试...")
                if i == 2:
                    print("多次尝试后仍然失败，请稍后重试。")
            else:
                print(f"API连接失败，状态码: {response.status_code}")
                break
        except requests.exceptions.Timeout:
            print(f"请求超时，第{i+1}次重试...")
            if i == 2:
                print("多次超时，请检查网络或稍后重试。")
        except Exception as e:
            print(f"API连接测试出错: {str(e)}")
            break
except Exception as e:
    print(f"API连接测试出错: {str(e)}")

input("按回车键继续...")
