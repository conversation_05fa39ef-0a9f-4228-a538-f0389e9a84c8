{"name": "@npmcli/git", "version": "3.0.1", "main": "lib/index.js", "files": ["bin/", "lib/"], "description": "a util for spawning git from npm CLI contexts", "repository": {"type": "git", "url": "https://github.com/npm/git.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"lint": "eslint \"**/*.js\"", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "snap": "tap", "test": "tap", "posttest": "npm run lint", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "slash": "^3.0.0", "tap": "^16.0.1"}, "dependencies": {"@npmcli/promise-spawn": "^3.0.0", "lru-cache": "^7.4.4", "mkdirp": "^1.0.4", "npm-pick-manifest": "^7.0.0", "proc-log": "^2.0.0", "promise-inflight": "^1.0.1", "promise-retry": "^2.0.1", "semver": "^7.3.5", "which": "^2.0.2"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "3.2.2"}}