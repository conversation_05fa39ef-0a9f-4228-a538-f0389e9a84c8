"""
九猫系统 - 分析维度优先级配置
用于定义分析维度的优先级顺序，特别是在批量分析或汇总时的处理顺序

此模块提供了获取优先级排序的维度列表的函数，可以在分析过程中使用。
"""

import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

# 维度优先级配置 - 数字越小优先级越高
DIMENSION_PRIORITIES = {
    "chapter_outline": 1,     # 章纲分析 - 最高优先级
    "outline_analysis": 2,    # 大纲分析 - 第二优先级
    "language_style": 3,      # 语言风格
    "structure": 4,           # 结构分析
    "character_relationships": 5,  # 人物关系
    "world_building": 6,      # 世界构建
    "novel_characteristics": 7,    # 小说特点
    "rhythm_pacing": 8,       # 节奏节拍
    "paragraph_flow": 9,      # 段落流畅度
    "sentence_variation": 10, # 句式变化
    "paragraph_length": 11,   # 段落长度
    "perspective_shifts": 12, # 视角转换
    "opening_effectiveness": 13,   # 开篇效果
    "climax_pacing": 14,      # 高潮节奏
    "popular_tropes": 15,     # 热梗统计
}

def get_prioritized_dimensions(dimensions: List[str]) -> List[str]:
    """
    根据优先级对维度列表进行排序
    
    Args:
        dimensions: 需要排序的维度列表
        
    Returns:
        按优先级排序后的维度列表
    """
    # 确保所有维度都有优先级，如果没有则赋予默认优先级（最低）
    max_priority = max(DIMENSION_PRIORITIES.values()) + 1
    
    # 使用优先级进行排序
    sorted_dimensions = sorted(
        dimensions, 
        key=lambda dim: DIMENSION_PRIORITIES.get(dim, max_priority)
    )
    
    logger.info(f"维度优先级排序: 原始顺序={dimensions}, 排序后={sorted_dimensions}")
    return sorted_dimensions

def get_batch_dimensions(dimensions: List[str], batch_size: int = 3) -> List[List[str]]:
    """
    将维度列表按优先级分成多个批次
    
    Args:
        dimensions: 需要分批的维度列表
        batch_size: 每批的维度数量
        
    Returns:
        按优先级分批后的维度列表的列表
    """
    # 先按优先级排序
    prioritized_dimensions = get_prioritized_dimensions(dimensions)
    
    # 分批
    batches = []
    for i in range(0, len(prioritized_dimensions), batch_size):
        batches.append(prioritized_dimensions[i:i+batch_size])
    
    logger.info(f"维度分批: 共{len(batches)}批, 每批最多{batch_size}个维度")
    return batches

# 示例用法
if __name__ == "__main__":
    # 测试维度优先级排序
    test_dimensions = [
        "language_style", 
        "rhythm_pacing", 
        "structure", 
        "character_relationships", 
        "chapter_outline", 
        "outline_analysis"
    ]
    
    print("原始维度列表:", test_dimensions)
    print("优先级排序后:", get_prioritized_dimensions(test_dimensions))
    
    # 测试维度分批
    print("分批结果:", get_batch_dimensions(test_dimensions, 2))
