{"name": "@npmcli/promise-spawn", "version": "3.0.0", "files": ["bin/", "lib/"], "main": "./lib/index.js", "description": "spawn processes the way the npm cli likes to do", "repository": {"type": "git", "url": "https://github.com/npm/promise-spawn.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "minipass": "^3.1.1", "tap": "^16.0.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}, "dependencies": {"infer-owner": "^1.0.4"}}