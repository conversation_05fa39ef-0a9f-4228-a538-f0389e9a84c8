@echo off
chcp 65001 >nul

echo ===================================
echo   九猫小说分析系统启动脚本
echo ===================================
echo.
echo 正在启动九猫小说分析系统...
echo.

:: 设置工作目录为脚本所在目录
cd /d %~dp0

:: 检查Python是否安装
echo 检查Python安装...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python。请安装Python并添加到PATH。
    pause
    exit /b 1
)

:: 检查是否存在main.py
echo 验证main.py存在...
if not exist main.py (
    echo [错误] 在当前目录中未找到main.py。请将此脚本移动到项目根目录。
    pause
    exit /b 1
)

:: 检查并下载前端依赖库
echo 检查前端依赖...
if not exist "src\web\static\js\lib\jquery.min.js" (
    echo [信息] 正在下载前端依赖...
    python download_libs.py
)

:: 修改config.py确保禁用DEBUG模式
echo 确保禁用DEBUG模式...
python -c "
import re
with open('config.py', 'r', encoding='utf-8') as f:
    content = f.read()
if 'DEBUG = True' in content:
    content = content.replace('DEBUG = True', 'DEBUG = False')
    with open('config.py', 'w', encoding='utf-8') as f:
        f.write(content)
    print('[信息] 已将DEBUG模式设置为False')
else:
    print('[信息] DEBUG模式已禁用')
"

:: 启动九猫系统
echo 启动九猫小说分析系统...
start "九猫分析系统" cmd /k "python main.py"

:: 等待系统启动
echo 等待系统启动...
timeout /t 5 /nobreak >nul

:: 打开浏览器
echo 在浏览器中打开 http://localhost:5001...
start "" http://localhost:5001

echo [成功] 系统已成功启动。
echo 请在浏览器中使用系统，完成后关闭命令窗口即可停止系统。
echo.
pause
