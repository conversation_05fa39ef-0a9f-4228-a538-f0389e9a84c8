/**
 * 九猫 - 容器修复脚本
 * 修复"找不到主容器"的问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('[九猫修复] 容器修复脚本已加载');
    
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否存在主容器
        const mainContainer = document.querySelector('.container-fluid');
        if (!mainContainer) {
            console.log('[九猫修复] 找不到.container-fluid主容器，尝试创建');
            
            // 查找替代容器
            const alternativeContainer = document.querySelector('.container');
            if (alternativeContainer) {
                console.log('[九猫修复] 找到.container替代容器，添加container-fluid类');
                alternativeContainer.classList.add('container-fluid');
            } else {
                console.log('[九猫修复] 找不到替代容器，创建新的主容器');
                
                // 创建新的主容器
                const newContainer = document.createElement('div');
                newContainer.className = 'container-fluid';
                
                // 查找body
                const body = document.body;
                
                // 将body的所有子元素移动到新容器中
                while (body.firstChild) {
                    newContainer.appendChild(body.firstChild);
                }
                
                // 将新容器添加到body中
                body.appendChild(newContainer);
                
                console.log('[九猫修复] 已创建新的主容器');
            }
        }
        
        // 修复维度状态容器
        fixDimensionsContainer();
    });
    
    // 修复维度状态容器
    function fixDimensionsContainer() {
        // 检查是否存在window.NineCats对象
        if (!window.NineCats) {
            console.log('[九猫修复] 创建NineCats对象');
            window.NineCats = {};
        }
        
        // 检查是否存在containers对象
        if (!window.NineCats.containers) {
            console.log('[九猫修复] 创建containers对象');
            window.NineCats.containers = {};
        }
        
        // 创建维度状态容器
        const dimensionsContainer = 'dimensionsStatusContainer';
        if (!window.NineCats.containers[dimensionsContainer]) {
            console.log('[九猫修复] 创建维度状态容器');
            
            // 创建容器元素
            const container = document.createElement('div');
            container.id = dimensionsContainer;
            container.className = 'card mb-4';
            container.innerHTML = `
                <div class="card-header">
                    <h5 class="mb-0">分析维度状态</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>维度</th>
                                    <th>状态</th>
                                    <th>进度</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="dimensionsStatusBody">
                                <!-- 这里将由JavaScript填充维度状态 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
            
            // 保存容器
            window.NineCats.containers[dimensionsContainer] = container;
            
            // 查找主容器并添加维度状态容器
            const mainContainer = document.querySelector('.container-fluid') || document.querySelector('.container');
            if (mainContainer) {
                // 查找分析部分
                const analysisSection = mainContainer.querySelector('.analysis-section');
                if (analysisSection) {
                    console.log('[九猫修复] 找到分析部分，添加维度状态容器');
                    analysisSection.appendChild(container);
                } else {
                    console.log('[九猫修复] 找不到分析部分，直接添加到主容器');
                    
                    // 查找合适的位置
                    const h2 = mainContainer.querySelector('h2');
                    if (h2 && h2.textContent.includes('分析结果')) {
                        console.log('[九猫修复] 找到分析结果标题，在其后添加维度状态容器');
                        
                        // 查找标题所在的父元素
                        const parent = h2.parentElement;
                        if (parent) {
                            // 查找父元素的下一个兄弟元素
                            const nextSibling = parent.nextElementSibling;
                            if (nextSibling) {
                                // 在下一个兄弟元素之前插入容器
                                nextSibling.parentElement.insertBefore(container, nextSibling);
                            } else {
                                // 在父元素之后插入容器
                                parent.parentElement.appendChild(container);
                            }
                        } else {
                            // 在标题之后插入容器
                            h2.parentElement.insertBefore(container, h2.nextSibling);
                        }
                    } else {
                        // 直接添加到主容器
                        mainContainer.appendChild(container);
                    }
                }
            } else {
                console.error('[九猫修复] 找不到主容器，无法添加维度状态容器');
            }
        }
    }
    
    console.log('[九猫修复] 容器修复脚本加载完成');
})();
