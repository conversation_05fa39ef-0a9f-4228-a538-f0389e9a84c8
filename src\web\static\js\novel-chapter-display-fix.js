/**
 * 九猫 - 小说与章节分析显示修复脚本
 * 用于修复整本书分析页面错误显示章节分析内容的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[九猫显示修复] 脚本已加载');

    // 配置
    const CONFIG = {
        debug: true,              // 是否启用调试日志
        checkInterval: 1000,      // 检查间隔（毫秒）
        maxRetries: 5             // 最大重试次数
    };

    // 安全的日志函数
    function safeLog(message, level = 'log') {
        if (!CONFIG.debug && level === 'log') return;

        try {
            if (level === 'error') {
                console.error(`[九猫显示修复] ${message}`);
            } else if (level === 'warn') {
                console.warn(`[九猫显示修复] ${message}`);
            } else {
                console.log(`[九猫显示修复] ${message}`);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }

    // 检查当前页面类型
    function checkPageType() {
        const path = window.location.pathname;

        // 整本书分析页面 - 例如: /novel/34
        if (path.match(/^\/novel\/\d+\/?$/)) {
            return 'novel';
        }

        // 章节列表页面 - 例如: /novel/34/chapters
        if (path.match(/^\/novel\/\d+\/chapters\/?$/)) {
            return 'chapters';
        }

        // 章节详情页面 - 例如: /novel/34/chapter/1
        if (path.match(/^\/novel\/\d+\/chapter\/\d+\/?$/)) {
            return 'chapter';
        }

        // 章节分析页面 - 例如: /novel/34/chapter/1/analysis/language_style
        if (path.match(/^\/novel\/\d+\/chapter\/\d+\/analysis\/[^\/]+\/?$/)) {
            return 'chapter_analysis';
        }

        // 整本书分析详情页面 - 例如: /novel/34/analysis/language_style
        if (path.match(/^\/novel\/\d+\/analysis\/[^\/]+\/?$/)) {
            return 'novel_analysis';
        }

        return 'unknown';
    }

    // 修复整本书分析页面
    function fixNovelPage() {
        safeLog('修复整本书分析页面');

        // 检查是否有章节分析标题
        const chapterTitles = document.querySelectorAll('h1, h2, h3, h4, h5');
        for (const title of chapterTitles) {
            if (title.textContent.includes('章节分析:') ||
                (title.textContent.includes('章节') && title.textContent.includes('分析'))) {

                safeLog(`发现章节分析标题: "${title.textContent}"，尝试修复`);

                // 获取当前小说ID
                const novelId = getCurrentNovelId();
                if (!novelId) {
                    safeLog('无法获取小说ID，跳过修复', 'warn');
                    return;
                }

                // 查找包含章节分析内容的容器
                const container = findParentContainer(title);
                if (!container) {
                    safeLog('无法找到章节分析内容容器，跳过修复', 'warn');
                    return;
                }

                // 创建提示信息
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-warning';
                alertDiv.innerHTML = `
                    <strong>提示：</strong> 这里应该显示整本书的分析结果，而不是单个章节的分析。
                    <br>
                    如果您想分析特定章节，请<a href="/novel/${novelId}/chapters" class="alert-link">点击这里</a>前往章节列表页面，
                    然后选择特定章节进行分析。
                `;

                // 替换章节分析内容
                container.innerHTML = '';
                container.appendChild(alertDiv);

                safeLog('成功修复章节分析标题和内容');

                // 修复页面标题
                if (document.title.includes('章节分析')) {
                    document.title = document.title.replace('章节分析', '小说分析');
                    safeLog('成功修复页面标题');
                }
            }
        }
    }

    // 查找父容器
    function findParentContainer(element) {
        // 向上查找最近的容器元素
        let current = element;
        while (current && current !== document.body) {
            // 检查是否是可能的容器元素
            if (current.classList.contains('card') ||
                current.classList.contains('container') ||
                current.classList.contains('row') ||
                current.classList.contains('col') ||
                current.tagName === 'SECTION' ||
                current.tagName === 'ARTICLE' ||
                current.tagName === 'DIV') {

                return current;
            }
            current = current.parentElement;
        }

        // 如果找不到合适的容器，返回元素的父元素
        return element.parentElement;
    }

    // 从URL、数据属性或页面内容获取当前小说ID
    function getCurrentNovelId() {
        // 尝试从URL获取
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        if (match && match[1]) {
            safeLog(`从URL获取到小说ID: ${match[1]}`);
            return match[1];
        }

        // 尝试从data属性获取
        const novelContainer = document.querySelector('[data-novel-id]');
        if (novelContainer && novelContainer.dataset.novelId) {
            safeLog(`从data属性获取到小说ID: ${novelContainer.dataset.novelId}`);
            return novelContainer.dataset.novelId;
        }

        // 尝试从breadcrumb获取
        const breadcrumbLinks = document.querySelectorAll('.breadcrumb a[href*="/novel/"]');
        if (breadcrumbLinks.length > 0) {
            const lastLink = breadcrumbLinks[breadcrumbLinks.length - 1];
            const breadcrumbMatch = lastLink.href.match(/\/novel\/(\d+)/);
            if (breadcrumbMatch && breadcrumbMatch[1]) {
                safeLog(`从面包屑导航获取到小说ID: ${breadcrumbMatch[1]}`);
                return breadcrumbMatch[1];
            }
        }

        safeLog('无法获取小说ID', 'warn');
        return null;
    }

    // 初始化函数
    function initialize() {
        safeLog('初始化中...');

        // 检查当前页面类型
        const pageType = checkPageType();
        safeLog(`当前页面类型: ${pageType}`);

        // 根据页面类型应用不同的修复
        switch (pageType) {
            case 'novel':
                // 修复整本书分析页面
                fixNovelPage();

                // 定期检查，应对动态加载的情况
                setInterval(fixNovelPage, CONFIG.checkInterval);
                break;

            default:
                safeLog(`当前页面类型 ${pageType} 不需要修复`);
                break;
        }

        safeLog('初始化完成');
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出到全局命名空间，方便调试
    window.novelChapterDisplayFix = {
        checkPageType,
        fixNovelPage,
        getCurrentNovelId
    };
})();
