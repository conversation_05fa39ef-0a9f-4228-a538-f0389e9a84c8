/**
 * 九猫 - 错误页面修复脚本
 * 用于检测和修复错误页面，提供更好的用户体验
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('错误页面修复脚本已加载 v1.0.0');
    
    // 错误页面特征
    const ERROR_INDICATORS = [
        '发生错误',
        '抱歉',
        '系统处理您的请求时遇到了问题',
        'Error',
        'Sorry',
        'Problem',
        '500',
        '404'
    ];
    
    // 检查是否是错误页面
    function isErrorPage() {
        // 检查页面文本
        const pageText = document.body.innerText;
        
        for (const indicator of ERROR_INDICATORS) {
            if (pageText.includes(indicator)) {
                return true;
            }
        }
        
        // 检查URL
        const url = window.location.href;
        if (url.includes('error') || url.includes('500') || url.includes('404')) {
            return true;
        }
        
        return false;
    }
    
    // 创建错误恢复UI
    function createErrorRecoveryUI() {
        // 创建恢复UI容器
        const recoveryUI = document.createElement('div');
        recoveryUI.className = 'error-recovery-container';
        recoveryUI.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            z-index: 9999;
            max-width: 300px;
        `;
        
        // 创建恢复UI内容
        recoveryUI.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong>页面加载出现问题</strong>
                <span style="cursor: pointer;" onclick="this.parentNode.parentNode.remove()">×</span>
            </div>
            <p style="margin-bottom: 10px;">系统在加载页面时遇到了问题。您可以尝试以下操作：</p>
            <ul style="margin-bottom: 10px; padding-left: 20px;">
                <li>刷新页面</li>
                <li>清除浏览器缓存</li>
                <li>检查系统服务是否正常运行</li>
            </ul>
            <div>
                <button onclick="location.reload()" style="background-color: #856404; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">刷新页面</button>
                <a href="/" style="background-color: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block;">返回首页</a>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(recoveryUI);
    }
    
    // 修复错误页面
    function fixErrorPage() {
        // 检查是否是系统监控页面
        const isSystemMonitor = window.location.pathname.includes('/system-monitor');
        
        if (isSystemMonitor) {
            console.log('检测到系统监控页面错误，尝试修复');
            
            // 创建系统监控页面内容
            const container = document.createElement('div');
            container.className = 'container mt-4';
            
            // 创建标题
            const title = document.createElement('h1');
            title.textContent = '系统监控';
            container.appendChild(title);
            
            // 创建系统状态卡片
            const statusCard = document.createElement('div');
            statusCard.className = 'card mb-4';
            
            const statusCardHeader = document.createElement('div');
            statusCardHeader.className = 'card-header';
            statusCardHeader.innerHTML = '<h5 class="card-title mb-0">系统状态</h5>';
            statusCard.appendChild(statusCardHeader);
            
            const statusCardBody = document.createElement('div');
            statusCardBody.className = 'card-body';
            statusCardBody.innerHTML = `
                <div class="alert alert-warning">
                    <p><strong>系统监控暂时不可用</strong></p>
                    <p>系统监控服务可能正在维护或暂时无法访问。请稍后再试。</p>
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
                    <a href="/" class="btn btn-secondary ml-2">返回首页</a>
                </div>
            `;
            
            statusCard.appendChild(statusCardBody);
            container.appendChild(statusCard);
            
            // 替换页面内容
            const mainContent = document.querySelector('main') || document.querySelector('.container') || document.body;
            mainContent.innerHTML = '';
            mainContent.appendChild(container);
            
            // 尝试获取系统状态数据
            fetch('/api/system/status')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('成功获取系统状态数据:', data);
                    
                    // 更新页面内容
                    statusCardBody.innerHTML = `
                        <p><strong>CPU使用率:</strong> ${data.system.cpu_percent}%</p>
                        <p><strong>内存使用率:</strong> ${data.system.memory_percent}%</p>
                        <p><strong>已用内存:</strong> ${data.system.memory_used} MB</p>
                        <p><strong>总内存:</strong> ${data.system.memory_total} MB</p>
                        <p><strong>系统启动时间:</strong> ${data.system.boot_time}</p>
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
                        </div>
                    `;
                })
                .catch(error => {
                    console.error('获取系统状态数据失败:', error);
                });
        } else {
            // 通用错误页面修复
            console.log('检测到通用错误页面，显示恢复UI');
            createErrorRecoveryUI();
        }
    }
    
    // 在页面加载完成后检查是否是错误页面
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            if (isErrorPage()) {
                fixErrorPage();
            }
        });
    } else {
        if (isErrorPage()) {
            fixErrorPage();
        }
    }
})();
