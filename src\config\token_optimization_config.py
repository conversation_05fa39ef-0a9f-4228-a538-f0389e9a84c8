"""
九猫系统Token输出优化配置
在保证分析质量的同时，智能控制输出token数量，降低API成本
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class TokenOptimizationConfig:
    """Token输出优化配置管理器"""
    
    @staticmethod
    def get_optimized_max_tokens(dimension: str, 
                               prompt_template: str = "default",
                               text_length: int = 0) -> int:
        """
        获取优化后的max_tokens配置
        
        Args:
            dimension: 分析维度
            prompt_template: 提示词模板
            text_length: 输入文本长度
            
        Returns:
            优化后的max_tokens值
        """
        # 基础token配置（相比原配置大幅降低）
        base_tokens = TokenOptimizationConfig._get_base_tokens(dimension, prompt_template)
        
        # 根据文本长度动态调整
        adjusted_tokens = TokenOptimizationConfig._adjust_by_text_length(base_tokens, text_length)
        
        # 应用质量保证下限
        final_tokens = TokenOptimizationConfig._apply_quality_threshold(adjusted_tokens, dimension)
        
        logger.info(f"[Token优化] 维度:{dimension}, 模板:{prompt_template}, 文本长度:{text_length}")
        logger.info(f"[Token优化] 基础:{base_tokens} → 调整:{adjusted_tokens} → 最终:{final_tokens}")
        
        return final_tokens
    
    @staticmethod
    def _get_base_tokens(dimension: str, prompt_template: str) -> int:
        """获取基础token配置"""
        
        if prompt_template == "simplified":
            # 精简版：激进优化，大幅降低token使用
            simplified_tokens = {
                "language_style": 2000,        # 从15000降到2000 (-87%)
                "rhythm_pacing": 2000,         # 从15000降到2000 (-87%)
                "structure": 2500,             # 从18000降到2500 (-86%)
                "sentence_variation": 1800,    # 从15000降到1800 (-88%)
                "paragraph_length": 1800,      # 从15000降到1800 (-88%)
                "perspective_shifts": 2000,    # 从15000降到2000 (-87%)
                "paragraph_flow": 2000,        # 从15000降到2000 (-87%)
                "novel_characteristics": 2500, # 从18000降到2500 (-86%)
                "world_building": 2500,        # 从18000降到2500 (-86%)
                "chapter_outline": 3000,       # 从25000降到3000 (-88%)
                "character_relationships": 2500, # 从18000降到2500 (-86%)
                "opening_effectiveness": 2000,  # 从15000降到2000 (-87%)
                "climax_pacing": 2000,         # 从15000降到2000 (-87%)
                "outline_analysis": 2800,      # 从20000降到2800 (-86%)
                "popular_tropes": 2500,        # 从18000降到2500 (-86%)
                "default": 2000                # 从15000降到2000 (-87%)
            }
            return simplified_tokens.get(dimension, simplified_tokens["default"])
        
        else:
            # 默认版：温和优化，适度降低token使用
            default_tokens = {
                "language_style": 4000,        # 从15000降到4000 (-73%)
                "rhythm_pacing": 4000,         # 从15000降到4000 (-73%)
                "structure": 5000,             # 从18000降到5000 (-72%)
                "sentence_variation": 3500,    # 从15000降到3500 (-77%)
                "paragraph_length": 3500,      # 从15000降到3500 (-77%)
                "perspective_shifts": 4000,    # 从15000降到4000 (-73%)
                "paragraph_flow": 4000,        # 从15000降到4000 (-73%)
                "novel_characteristics": 5000, # 从18000降到5000 (-72%)
                "world_building": 5000,        # 从18000降到5000 (-72%)
                "chapter_outline": 6000,       # 从25000降到6000 (-76%)
                "character_relationships": 5000, # 从18000降到5000 (-72%)
                "opening_effectiveness": 4000,  # 从15000降到4000 (-73%)
                "climax_pacing": 4000,         # 从15000降到4000 (-73%)
                "outline_analysis": 5500,      # 从20000降到5500 (-73%)
                "popular_tropes": 5000,        # 从18000降到5000 (-72%)
                "default": 4000                # 从15000降到4000 (-73%)
            }
            return default_tokens.get(dimension, default_tokens["default"])
    
    @staticmethod
    def _adjust_by_text_length(base_tokens: int, text_length: int) -> int:
        """根据文本长度动态调整token数"""
        if text_length == 0:
            return base_tokens
        
        # 文本长度调整系数
        if text_length < 2000:
            # 短文本：减少20%
            multiplier = 0.8
        elif text_length < 5000:
            # 中等文本：保持不变
            multiplier = 1.0
        elif text_length < 10000:
            # 长文本：增加15%
            multiplier = 1.15
        else:
            # 超长文本：增加25%
            multiplier = 1.25
        
        adjusted = int(base_tokens * multiplier)
        return adjusted
    
    @staticmethod
    def _apply_quality_threshold(tokens: int, dimension: str) -> int:
        """应用质量保证下限"""
        # 不同维度的最低质量要求
        min_tokens = {
            "language_style": 1500,
            "rhythm_pacing": 1500,
            "structure": 2000,
            "sentence_variation": 1200,
            "paragraph_length": 1200,
            "perspective_shifts": 1500,
            "paragraph_flow": 1500,
            "novel_characteristics": 2000,
            "world_building": 2000,
            "chapter_outline": 2500,
            "character_relationships": 2000,
            "opening_effectiveness": 1500,
            "climax_pacing": 1500,
            "outline_analysis": 2200,
            "popular_tropes": 2000,
            "default": 1500
        }
        
        min_required = min_tokens.get(dimension, min_tokens["default"])
        return max(tokens, min_required)
    
    @staticmethod
    def get_quality_optimization_prompt(dimension: str, 
                                      prompt_template: str,
                                      target_tokens: int) -> str:
        """
        获取质量优化提示词片段
        
        Args:
            dimension: 分析维度
            prompt_template: 提示词模板
            target_tokens: 目标token数
            
        Returns:
            质量优化提示词片段
        """
        if prompt_template == "simplified":
            return TokenOptimizationConfig._get_simplified_quality_prompt(dimension, target_tokens)
        else:
            return TokenOptimizationConfig._get_default_quality_prompt(dimension, target_tokens)
    
    @staticmethod
    def _get_simplified_quality_prompt(dimension: str, target_tokens: int) -> str:
        """获取精简版质量优化提示词"""
        return f"""
**精简版智能输出优化（目标约{target_tokens}tokens）：**
1. **核心要点优先**：重点突出最重要的分析结论
2. **精准表达**：使用简洁有力的语言，避免冗余描述
3. **结构清晰**：采用条理分明的组织方式
4. **质量保证**：确保分析深度和准确性不受影响
5. **高效利用**：在有限篇幅内提供最大价值的分析内容

请在保持分析质量的前提下，控制输出长度在合理范围内，重点突出核心观点和关键发现。
"""
    
    @staticmethod
    def _get_default_quality_prompt(dimension: str, target_tokens: int) -> str:
        """获取默认版质量优化提示词"""
        return f"""
**默认版智能输出优化（目标约{target_tokens}tokens）：**
1. **详细分析**：提供充分详细的分析内容，确保分析深度
2. **合理控制**：在保证质量的前提下，避免过度冗长
3. **重点突出**：突出最重要的分析发现和结论
4. **逻辑清晰**：保持清晰的分析逻辑和结构
5. **价值最大化**：确保每个部分都有实质性的分析价值

请提供详细而有价值的分析，同时保持内容的紧凑性和针对性。
"""
    
    @staticmethod
    def calculate_token_savings(original_tokens: int, optimized_tokens: int) -> Dict[str, Any]:
        """
        计算token节省效果
        
        Args:
            original_tokens: 原始token配置
            optimized_tokens: 优化后token配置
            
        Returns:
            节省效果统计
        """
        if original_tokens == 0:
            return {"savings_rate": 0, "saved_tokens": 0, "cost_savings": 0}
        
        saved_tokens = original_tokens - optimized_tokens
        savings_rate = saved_tokens / original_tokens
        
        # 估算成本节省（假设每1000tokens约0.01元）
        cost_savings = (saved_tokens / 1000) * 0.01
        
        return {
            "original_tokens": original_tokens,
            "optimized_tokens": optimized_tokens,
            "saved_tokens": saved_tokens,
            "savings_rate": savings_rate,
            "cost_savings": round(cost_savings, 4)
        }
    
    @staticmethod
    def get_dimension_optimization_strategy(dimension: str) -> Dict[str, Any]:
        """
        获取维度特定的优化策略
        
        Args:
            dimension: 分析维度
            
        Returns:
            优化策略配置
        """
        # 高价值维度（需要保持较高质量）
        high_value_dimensions = ["chapter_outline", "character_relationships", "structure"]
        
        # 轻量级维度（可以更激进优化）
        lightweight_dimensions = ["sentence_variation", "paragraph_length", "perspective_shifts"]
        
        if dimension in high_value_dimensions:
            return {
                "optimization_level": "conservative",
                "min_quality_ratio": 0.8,  # 至少保持80%的原始质量
                "max_compression": 0.7,    # 最多压缩70%
                "priority": "quality_first"
            }
        elif dimension in lightweight_dimensions:
            return {
                "optimization_level": "aggressive", 
                "min_quality_ratio": 0.6,  # 至少保持60%的原始质量
                "max_compression": 0.85,   # 最多压缩85%
                "priority": "cost_first"
            }
        else:
            return {
                "optimization_level": "balanced",
                "min_quality_ratio": 0.7,  # 至少保持70%的原始质量
                "max_compression": 0.75,   # 最多压缩75%
                "priority": "balanced"
            }
    
    @staticmethod
    def get_comprehensive_optimization_report() -> Dict[str, Any]:
        """
        获取综合优化报告
        
        Returns:
            优化效果综合报告
        """
        dimensions = [
            "language_style", "rhythm_pacing", "structure", "sentence_variation",
            "paragraph_length", "perspective_shifts", "paragraph_flow", 
            "novel_characteristics", "world_building", "chapter_outline",
            "character_relationships", "opening_effectiveness", "climax_pacing",
            "outline_analysis", "popular_tropes"
        ]
        
        # 原始配置（当前config.py中的值）
        original_config = {
            "language_style": 15000, "rhythm_pacing": 15000, "structure": 18000,
            "sentence_variation": 15000, "paragraph_length": 15000, "perspective_shifts": 15000,
            "paragraph_flow": 15000, "novel_characteristics": 18000, "world_building": 18000,
            "chapter_outline": 25000, "character_relationships": 18000, "opening_effectiveness": 15000,
            "climax_pacing": 15000, "outline_analysis": 20000, "popular_tropes": 18000
        }
        
        simplified_total_original = 0
        simplified_total_optimized = 0
        default_total_original = 0
        default_total_optimized = 0
        
        for dimension in dimensions:
            original = original_config.get(dimension, 15000)
            
            simplified_optimized = TokenOptimizationConfig._get_base_tokens(dimension, "simplified")
            default_optimized = TokenOptimizationConfig._get_base_tokens(dimension, "default")
            
            simplified_total_original += original
            simplified_total_optimized += simplified_optimized
            default_total_original += original
            default_total_optimized += default_optimized
        
        simplified_savings = TokenOptimizationConfig.calculate_token_savings(
            simplified_total_original, simplified_total_optimized
        )
        
        default_savings = TokenOptimizationConfig.calculate_token_savings(
            default_total_original, default_total_optimized
        )
        
        return {
            "simplified_mode": {
                "total_original_tokens": simplified_total_original,
                "total_optimized_tokens": simplified_total_optimized,
                "savings_rate": simplified_savings["savings_rate"],
                "estimated_cost_savings": simplified_savings["cost_savings"]
            },
            "default_mode": {
                "total_original_tokens": default_total_original,
                "total_optimized_tokens": default_total_optimized,
                "savings_rate": default_savings["savings_rate"],
                "estimated_cost_savings": default_savings["cost_savings"]
            },
            "optimization_summary": {
                "simplified_compression": f"{simplified_savings['savings_rate']:.1%}",
                "default_compression": f"{default_savings['savings_rate']:.1%}",
                "total_dimensions_optimized": len(dimensions)
            }
        }
