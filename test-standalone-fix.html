<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫 - 堆栈溢出修复测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #343a40;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        h2 {
            color: #495057;
            margin-top: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .test-button:hover {
            background-color: #0069d9;
        }
        .error-button {
            background-color: #dc3545;
        }
        .error-button:hover {
            background-color: #c82333;
        }
        .fix-button {
            background-color: #28a745;
        }
        .fix-button:hover {
            background-color: #218838;
        }
        .test-output {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            min-height: 100px;
            max-height: 200px;
            overflow-y: auto;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .log {
            margin: 5px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>九猫 - 堆栈溢出修复测试</h1>
        <p>这个页面用于测试和修复堆栈溢出错误（Maximum call stack size exceeded）。</p>
        
        <div class="test-section">
            <h2>测试一：递归堆栈溢出</h2>
            <button id="testRecursion" class="test-button error-button">触发递归错误</button>
            <button id="fixRecursion" class="test-button fix-button">应用修复</button>
            <div id="recursionOutput" class="test-output"></div>
        </div>
        
        <div class="test-section">
            <h2>测试二：DOM操作堆栈溢出</h2>
            <button id="testDOM" class="test-button error-button">触发DOM错误</button>
            <button id="fixDOM" class="test-button fix-button">应用DOM修复</button>
            <div id="domTestContainer"></div>
            <div id="domOutput" class="test-output"></div>
        </div>
        
        <div class="test-section">
            <h2>测试三：事件循环堆栈溢出</h2>
            <button id="testEvent" class="test-button error-button">触发事件错误</button>
            <button id="fixEvent" class="test-button fix-button">应用事件修复</button>
            <div id="eventOutput" class="test-output"></div>
        </div>
        
        <div class="test-section">
            <h2>测试四：综合测试</h2>
            <button id="testAll" class="test-button error-button">触发所有错误</button>
            <button id="fixAll" class="test-button fix-button">应用全部修复</button>
            <div id="allOutput" class="test-output"></div>
        </div>
    </div>
    
    <script>
        // 自定义控制台输出函数
        function log(message, outputId, isError = false) {
            const outputDiv = document.getElementById(outputId);
            const logDiv = document.createElement('div');
            logDiv.className = 'log ' + (isError ? 'error' : '');
            logDiv.textContent = message;
            outputDiv.appendChild(logDiv);
            outputDiv.scrollTop = outputDiv.scrollHeight;
            
            // 同时在控制台输出
            if (isError) {
                console.error(message);
            } else {
                console.log(message);
            }
        }
        
        // 存储原始DOM方法
        const originalMethods = {
            appendChild: Node.prototype.appendChild,
            replaceChild: Node.prototype.replaceChild,
            removeChild: Node.prototype.removeChild
        };
        
        // 测试一：递归函数导致堆栈溢出
        document.getElementById('testRecursion').addEventListener('click', function() {
            log('开始测试递归导致的堆栈溢出...', 'recursionOutput');
            
            try {
                // 无限递归函数
                function infiniteRecursion(n) {
                    return infiniteRecursion(n + 1);
                }
                
                infiniteRecursion(0);
            } catch (e) {
                log('捕获到错误: ' + e.message, 'recursionOutput', true);
            }
        });
        
        // 递归错误修复
        document.getElementById('fixRecursion').addEventListener('click', function() {
            log('应用递归错误修复...', 'recursionOutput');
            
            // 安全递归函数
            function safeRecursion(n, maxDepth = 10) {
                if (n >= maxDepth) {
                    log('递归达到最大深度 ' + maxDepth + '，安全返回', 'recursionOutput');
                    return n;
                }
                return safeRecursion(n + 1, maxDepth);
            }
            
            try {
                const result = safeRecursion(0);
                log('安全递归执行完成，结果: ' + result, 'recursionOutput', false);
            } catch (e) {
                log('安全递归仍然出错: ' + e.message, 'recursionOutput', true);
            }
        });
        
        // 测试二：DOM操作导致堆栈溢出
        document.getElementById('testDOM').addEventListener('click', function() {
            log('开始测试DOM操作导致的堆栈溢出...', 'domOutput');
            
            try {
                const container = document.getElementById('domTestContainer');
                container.innerHTML = ''; // 清空容器
                
                // 创建一个自引用的DOM结构
                function createSelfReferencingDom() {
                    const div = document.createElement('div');
                    div.id = 'recursiveDiv';
                    
                    // 创建引用自身的内部属性
                    div._parent = div;
                    
                    // 创建一个对象循环引用
                    const obj1 = {};
                    const obj2 = { ref: obj1 };
                    obj1.ref = obj2;
                    
                    div.refObject = obj1;
                    
                    // 尝试触发堆栈溢出
                    function recursiveDomOperation(element, depth) {
                        if (depth > 1000) return; // 防止浏览器崩溃
                        
                        const child = document.createElement('div');
                        child.textContent = 'Depth: ' + depth;
                        
                        // 设置自引用属性
                        child._parent = child;
                        child.refObject = element.refObject;
                        
                        element.appendChild(child);
                        recursiveDomOperation(child, depth + 1);
                    }
                    
                    recursiveDomOperation(div, 0);
                    return div;
                }
                
                container.appendChild(createSelfReferencingDom());
            } catch (e) {
                log('捕获到DOM错误: ' + e.message, 'domOutput', true);
            }
        });
        
        // DOM操作错误修复
        document.getElementById('fixDOM').addEventListener('click', function() {
            log('应用DOM操作错误修复...', 'domOutput');
            
            // 防止递归调用的计数器
            const recursionCounter = {
                appendChild: 0,
                replaceChild: 0,
                removeChild: 0,
                maxRecursion: 3 // 允许的最大递归深度
            };
            
            // 修复appendChild方法
            Node.prototype.appendChild = function(child) {
                if (!child) return null;
                
                recursionCounter.appendChild++;
                
                try {
                    // 如果递归太深，直接返回，避免堆栈溢出
                    if (recursionCounter.appendChild > recursionCounter.maxRecursion) {
                        log('DOM操作检测到递归过深: appendChild', 'domOutput', true);
                        recursionCounter.appendChild = 0;
                        return null;
                    }
                    
                    const result = originalMethods.appendChild.call(this, child);
                    recursionCounter.appendChild = 0;
                    return result;
                } catch (e) {
                    log('appendChild错误: ' + e.message, 'domOutput', true);
                    recursionCounter.appendChild = 0;
                    return null;
                }
            };
            
            try {
                const container = document.getElementById('domTestContainer');
                container.innerHTML = ''; // 清空容器
                
                // 创建安全的DOM结构
                function createSafeDom(maxDepth = 5) {
                    const div = document.createElement('div');
                    div.id = 'safeDiv';
                    
                    function safeDomOperation(element, depth) {
                        if (depth >= maxDepth) {
                            log('达到最大DOM深度 ' + maxDepth + '，停止创建', 'domOutput');
                            return;
                        }
                        
                        const child = document.createElement('div');
                        child.textContent = '安全深度: ' + depth;
                        child.style.marginLeft = (depth * 20) + 'px';
                        child.style.padding = '5px';
                        child.style.border = '1px solid #ccc';
                        child.style.marginTop = '5px';
                        
                        element.appendChild(child);
                        
                        // 递归创建子元素，但限制深度
                        safeDomOperation(child, depth + 1);
                    }
                    
                    safeDomOperation(div, 0);
                    return div;
                }
                
                container.appendChild(createSafeDom());
                log('安全DOM操作成功完成', 'domOutput', false);
            } catch (e) {
                log('安全DOM操作失败: ' + e.message, 'domOutput', true);
            }
        });
        
        // 测试三：事件循环堆栈溢出
        document.getElementById('testEvent').addEventListener('click', function() {
            log('开始测试事件循环导致的堆栈溢出...', 'eventOutput');
            
            try {
                // 创建互相调用的函数
                const funA = function() { funB(); };
                const funB = function() { funA(); };
                
                // 触发堆栈溢出
                funA();
            } catch (e) {
                log('捕获到事件错误: ' + e.message, 'eventOutput', true);
            }
        });
        
        // 事件循环错误修复
        document.getElementById('fixEvent').addEventListener('click', function() {
            log('应用事件循环错误修复...', 'eventOutput');
                
            // 创建带计数器的安全函数
            let callCounter = 0;
            const maxCalls = 10;
            
            const safeFunA = function() {
                callCounter++;
                if (callCounter > maxCalls) {
                    log('达到最大调用次数 ' + maxCalls + '，停止调用', 'eventOutput');
                    callCounter = 0;
                    return;
                }
                safeFunB();
            };
            
            const safeFunB = function() {
                callCounter++;
                if (callCounter > maxCalls) {
                    log('达到最大调用次数 ' + maxCalls + '，停止调用', 'eventOutput');
                    callCounter = 0;
                    return;
                }
                safeFunA();
            };
            
            try {
                safeFunA();
            } catch (e) {
                log('安全事件循环仍然出错: ' + e.message, 'eventOutput', true);
            }
        });
        
        // 测试四：综合测试
        document.getElementById('testAll').addEventListener('click', function() {
            document.getElementById('testRecursion').click();
            document.getElementById('testDOM').click();
            document.getElementById('testEvent').click();
            
            log('所有测试已完成', 'allOutput');
        });
        
        // 应用所有修复
        document.getElementById('fixAll').addEventListener('click', function() {
            document.getElementById('fixRecursion').click();
            document.getElementById('fixDOM').click();
            document.getElementById('fixEvent').click();
            
            log('所有修复已应用', 'allOutput');
            log('九猫堆栈溢出修复测试完成', 'allOutput');
        });
    </script>
</body>
</html>
