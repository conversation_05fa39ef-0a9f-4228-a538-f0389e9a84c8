"""
直接处理同步API请求的Flask应用
"""
import os
import sys
import json
import logging
from flask import Flask, request, jsonify
from datetime import datetime
from direct_sync_api import sync_novel_analysis_to_chapters

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'direct_sync_api_server_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

@app.route('/api/novel/<int:novel_id>/sync_to_chapters', methods=['POST'])
def api_sync_novel_analysis_to_chapters(novel_id):
    """
    将整本书的分析结果同步到各个章节。

    Args:
        novel_id: 小说ID
    """
    try:
        # 获取请求参数
        data = request.json or {}
        dimension = data.get('dimension')  # 可选参数，如果不提供则同步所有维度

        # 调用同步函数
        result = sync_novel_analysis_to_chapters(novel_id, dimension)

        return jsonify(result)
    except Exception as e:
        logger.error(f"同步分析结果到章节时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"同步分析结果到章节时出错: {str(e)}",
            "novel_id": novel_id
        })

if __name__ == "__main__":
    # 获取端口
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 5002
    
    # 启动Flask应用
    logger.info(f"启动同步API服务器，端口: {port}")
    app.run(host='0.0.0.0', port=port, debug=True)
