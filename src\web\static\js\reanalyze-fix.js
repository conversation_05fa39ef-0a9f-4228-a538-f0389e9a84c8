/**
 * 九猫小说分析写作系统 - 重新分析修复脚本
 *
 * 此脚本用于修复重新分析功能和API路径问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[重新分析修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        apiPaths: {
            // 正确的API路径
            novel: ['/api/novel/', '/api/novels/'],
            template: ['/api/novel/', '/api/novels/'],
            analysis: ['/api/novel/{novel_id}/analysis/{dimension}', '/api/novels/{novel_id}/analysis/{dimension}'],
            reasoning: ['/api/novel/{novel_id}/analysis/{dimension}/reasoning_content', '/api/novels/{novel_id}/analysis/{dimension}/reasoning_content'],
            reanalyze: ['/api/novel/{novel_id}/reanalyze/{dimension}', '/api/novels/{novel_id}/reanalyze/{dimension}'],
            chapterAnalysis: ['/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}', '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}'],
            chapterReasoning: ['/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content', '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content'],
            chapterReanalyze: ['/api/novel/{novel_id}/chapter/{chapter_id}/reanalyze/{dimension}', '/api/novels/{novel_id}/chapter/{chapter_id}/reanalyze/{dimension}']
        }
    };

    // 状态
    const STATE = {
        initialized: false,
        reanalyzeButtons: {},
        originalFetch: window.fetch,
        originalXhrOpen: XMLHttpRequest.prototype.open
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[重新分析修复] ${message}`);
        }
    }

    // 初始化
    function init() {
        debugLog('初始化重新分析修复脚本');

        // 修复API路径
        fixApiPaths();

        // 添加重新分析按钮
        addReanalyzeButtons();

        // 监听DOM变化，动态添加重新分析按钮
        observeDomChanges();

        STATE.initialized = true;
        debugLog('重新分析修复脚本初始化完成');
    }

    // 修复API路径
    function fixApiPaths() {
        debugLog('开始修复API路径');

        // 重写fetch函数
        window.fetch = function(resource, options) {
            // 如果resource是字符串（URL），检查并修复
            if (typeof resource === 'string') {
                // 检查是否包含/api/template/
                if (resource.includes('/api/template/')) {
                    const fixedUrl = resource.replace('/api/template/', '/api/novel/');
                    debugLog(`修复API路径: ${resource} -> ${fixedUrl}`);
                    resource = fixedUrl;
                }
            }
            // 调用原始的fetch函数
            return STATE.originalFetch.call(this, resource, options);
        };

        // 重写XMLHttpRequest.open方法
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            // 检查是否包含/api/template/
            if (typeof url === 'string' && url.includes('/api/template/')) {
                const fixedUrl = url.replace('/api/template/', '/api/novel/');
                debugLog(`修复XHR API路径: ${url} -> ${fixedUrl}`);
                url = fixedUrl;
            }
            // 调用原始的open方法
            return STATE.originalXhrOpen.call(this, method, url, async, user, password);
        };

        debugLog('API路径修复完成');
    }

    // 添加重新分析按钮
    function addReanalyzeButtons() {
        debugLog('开始添加重新分析按钮');

        // 添加整本书分析的重新分析按钮
        addBookReanalyzeButton();

        // 添加章节分析的重新分析按钮
        addChapterReanalyzeButton();

        debugLog('重新分析按钮添加完成');
    }

    // 添加整本书分析的重新分析按钮
    function addBookReanalyzeButton() {
        // 检查是否已存在重新分析按钮
        if ($('#reanalyzeBtn').length > 0) {
            debugLog('整本书分析的重新分析按钮已存在');
            return;
        }

        // 查找整本书分析结果标签页
        const resultTab = $('#result-tab');
        if (resultTab.length > 0) {
            // 创建重新分析按钮
            const reanalyzeBtn = $('<button id="reanalyzeBtn" class="btn btn-sm btn-outline-primary ms-2"><i class="fas fa-sync-alt me-1"></i>重新分析</button>');

            // 添加到标签页旁边
            resultTab.after(reanalyzeBtn);

            // 绑定点击事件
            reanalyzeBtn.click(function() {
                if (confirm('确定要重新分析吗？这将删除现有的分析结果并重新生成。')) {
                    reanalyzeBookDimension();
                }
            });

            debugLog('整本书分析的重新分析按钮已添加');
        } else {
            debugLog('未找到整本书分析结果标签页，无法添加重新分析按钮');
        }
    }

    // 添加章节分析的重新分析按钮
    function addChapterReanalyzeButton() {
        // 检查是否已存在重新分析按钮
        if ($('#chapterReanalyzeBtn').length > 0) {
            debugLog('章节分析的重新分析按钮已存在');
            return;
        }

        // 查找章节分析结果标签页
        const chapterResultTab = $('#chapter-result-tab');
        if (chapterResultTab.length > 0) {
            // 创建重新分析按钮
            const reanalyzeBtn = $('<button id="chapterReanalyzeBtn" class="btn btn-sm btn-outline-primary ms-2"><i class="fas fa-sync-alt me-1"></i>重新分析</button>');

            // 添加到标签页旁边
            chapterResultTab.after(reanalyzeBtn);

            // 绑定点击事件
            reanalyzeBtn.click(function() {
                if (confirm('确定要重新分析吗？这将删除现有的分析结果并重新生成。')) {
                    reanalyzeChapterDimension();
                }
            });

            debugLog('章节分析的重新分析按钮已添加');
        } else {
            debugLog('未找到章节分析结果标签页，无法添加重新分析按钮');
        }
    }

    // 重新分析整本书维度
    function reanalyzeBookDimension() {
        const templateId = getSelectedTemplateId();
        const dimension = getSelectedDimension();

        if (!templateId || !dimension) {
            alert('请先选择参考蓝本和分析维度');
            return;
        }

        debugLog(`开始重新分析整本书: 模板ID=${templateId}, 维度=${dimension}`);

        // 显示加载状态
        $('#reanalyzeBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...');
        $('#analysisContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">重新分析中，请稍候...</p></div>');

        // 添加日志
        addLogEntry('info', `开始重新分析整本书: 模板ID=${templateId}, 维度=${dimension}`);

        // 尝试所有可能的API路径
        tryAllApiPaths(CONFIG.apiPaths.reanalyze, templateId, dimension, null, 'POST', function(data) {
            if (data && data.success) {
                addLogEntry('info', `重新分析请求已发送: ${data.message || '分析已开始'}`);

                // 显示成功消息
                alert('分析任务已启动，请稍后刷新页面查看结果');

                // 5秒后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 5000);
            } else {
                // 恢复按钮状态
                $('#reanalyzeBtn').prop('disabled', false).html('<i class="fas fa-sync-alt me-1"></i>重新分析');

                // 显示错误消息
                alert(`启动分析失败: ${data ? data.error || '未知错误' : '请求失败'}`);
                addLogEntry('error', `重新分析请求失败: ${data ? data.error || '未知错误' : '请求失败'}`);
            }
        });
    }

    // 重新分析章节维度
    function reanalyzeChapterDimension() {
        const templateId = getSelectedTemplateId();
        const chapterId = getSelectedChapterId();
        const dimension = getSelectedDimension();

        if (!templateId || !chapterId || !dimension) {
            alert('请先选择参考蓝本、章节和分析维度');
            return;
        }

        debugLog(`开始重新分析章节: 模板ID=${templateId}, 章节ID=${chapterId}, 维度=${dimension}`);

        // 显示加载状态
        $('#chapterReanalyzeBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...');
        $('#chapterAnalysisContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">重新分析中，请稍候...</p></div>');

        // 添加日志
        addLogEntry('info', `开始重新分析章节: 模板ID=${templateId}, 章节ID=${chapterId}, 维度=${dimension}`);

        // 尝试所有可能的API路径
        tryAllApiPaths(CONFIG.apiPaths.chapterReanalyze, templateId, dimension, chapterId, 'POST', function(data) {
            if (data && data.success) {
                addLogEntry('info', `重新分析请求已发送: ${data.message || '分析已开始'}`);

                // 显示成功消息
                alert('分析任务已启动，请稍后刷新页面查看结果');

                // 5秒后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 5000);
            } else {
                // 恢复按钮状态
                $('#chapterReanalyzeBtn').prop('disabled', false).html('<i class="fas fa-sync-alt me-1"></i>重新分析');

                // 显示错误消息
                alert(`启动分析失败: ${data ? data.error || '未知错误' : '请求失败'}`);
                addLogEntry('error', `重新分析请求失败: ${data ? data.error || '未知错误' : '请求失败'}`);
            }
        });
    }

    // 尝试所有可能的API路径
    function tryAllApiPaths(apiPaths, templateId, dimension, chapterId, method, callback) {
        let index = 0;

        function tryNextPath() {
            if (index >= apiPaths.length) {
                debugLog('所有API路径均失败', 'error');
                callback(null);
                return;
            }

            let apiUrl = apiPaths[index];

            // 替换参数
            apiUrl = apiUrl.replace('{novel_id}', templateId)
                          .replace('{dimension}', dimension);

            // 如果有章节ID，替换章节ID参数
            if (chapterId) {
                apiUrl = apiUrl.replace('{chapter_id}', chapterId);
            }

            // 添加时间戳防止缓存
            apiUrl += `?_=${Date.now()}`;

            debugLog(`尝试API路径 ${index + 1}/${apiPaths.length}: ${apiUrl}`);

            // 发送请求
            $.ajax({
                url: apiUrl,
                type: method || 'GET',
                contentType: 'application/json',
                success: function(data) {
                    debugLog(`API路径 ${apiUrl} 成功获取数据`);
                    callback(data);
                },
                error: function() {
                    debugLog(`API路径 ${apiUrl} 失败，尝试下一个路径`, 'warn');
                    index++;
                    tryNextPath();
                }
            });
        }

        // 开始尝试第一个路径
        tryNextPath();
    }

    // 监听DOM变化，动态添加重新分析按钮
    function observeDomChanges() {
        // 创建MutationObserver实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新的节点添加
                if (mutation.addedNodes.length > 0) {
                    // 检查是否需要添加重新分析按钮
                    if ($('#reanalyzeBtn').length === 0 || $('#chapterReanalyzeBtn').length === 0) {
                        addReanalyzeButtons();
                    }
                }
            });
        });

        // 配置观察选项
        const config = { childList: true, subtree: true };

        // 开始观察
        observer.observe(document.body, config);

        debugLog('DOM变化监听已启动');
    }

    // 获取当前选中的模板ID
    function getSelectedTemplateId() {
        // 尝试从全局变量获取
        if (typeof selectedTemplateId !== 'undefined' && selectedTemplateId) {
            return selectedTemplateId;
        }

        // 尝试从DOM元素获取
        const selectedTemplate = $('.template-card.border-primary');
        if (selectedTemplate.length > 0) {
            return selectedTemplate.data('template-id');
        }

        return null;
    }

    // 获取当前选中的维度
    function getSelectedDimension() {
        // 尝试从全局变量获取
        if (typeof window.selectedDimension !== 'undefined' && window.selectedDimension) {
            return window.selectedDimension;
        }

        // 尝试从DOM元素获取
        const activeDimension = $('.dimension-item.active');
        if (activeDimension.length > 0) {
            return activeDimension.data('dimension');
        }

        return null;
    }

    // 获取当前选中的章节ID
    function getSelectedChapterId() {
        // 尝试从全局变量获取
        if (typeof selectedChapterId !== 'undefined' && selectedChapterId) {
            return selectedChapterId;
        }

        // 尝试从DOM元素获取
        const selectedChapter = $('.chapter-item.active');
        if (selectedChapter.length > 0) {
            return selectedChapter.data('chapter-id');
        }

        return null;
    }

    // 添加日志条目（如果存在全局addLogEntry函数）
    function addLogEntry(type, message) {
        if (typeof window.addLogEntry === 'function') {
            window.addLogEntry(type, message);
        } else {
            debugLog(message, type === 'error' ? 'error' : type === 'warn' ? 'warn' : 'log');
        }
    }

    // 初始化脚本
    $(document).ready(function() {
        init();
    });
})();
