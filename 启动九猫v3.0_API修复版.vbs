Option Explicit

' 九猫小说分析写作系统 v3.0 API修复版启动脚本
' 自动启动九猫系统，无需手动操作
' 此版本包含API路由修复，解决reasoning_content 404错误问题

' 设置工作目录为脚本所在目录
Dim fso, shell, pythonCmd, workingDir, runCmd
Set fso = CreateObject("Scripting.FileSystemObject")
Set shell = CreateObject("WScript.Shell")

' 获取当前脚本所在目录
workingDir = fso.GetParentFolderName(WScript.ScriptFullName)
shell.CurrentDirectory = workingDir

' 检查Python环境
pythonCmd = "python"
If Not CheckPythonInstalled(pythonCmd) Then
    pythonCmd = "py"
    If Not CheckPythonInstalled(pythonCmd) Then
        MsgBox "未检测到Python环境，请安装Python 3.8或更高版本。", vbExclamation, "九猫小说分析写作系统"
        WScript.Quit
    End If
End If

' 显示启动消息
MsgBox "九猫小说分析写作系统 v3.0 API修复版正在启动..." & vbCrLf & vbCrLf & _
       "系统将在后台运行，浏览器将在几秒钟后自动打开。" & vbCrLf & _
       "此版本修复了reasoning_content API 404错误问题。" & vbCrLf & _
       "请不要关闭浏览器窗口，否则需要手动访问: http://localhost:5001/v3/", _
       64, "九猫小说分析写作系统 v3.0 API修复版"

' 创建临时批处理文件来设置环境变量并运行Python
Dim tempFile
Set tempFile = fso.CreateTextFile("temp_run_v3_api_fixed.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & workingDir & """")
tempFile.WriteLine("echo 当前工作目录: %CD%")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set USE_MOCK_API=False")
tempFile.WriteLine("set ENABLE_MOCK_ANALYSIS=False")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("set MEMORY_OPTIMIZED=True")
tempFile.WriteLine("set LOW_MEMORY_MODE=True")
tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=75")
tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=85")
tempFile.WriteLine("set MAX_DB_CONNECTIONS=30")
tempFile.WriteLine("set DB_POOL_SIZE=30")
tempFile.WriteLine("set DB_MAX_OVERFLOW=20")
tempFile.WriteLine("set MEMORY_STATS_DIR=" & workingDir & "\memory_stats")
tempFile.WriteLine("set DISABLE_AUTO_REFRESH=True")
tempFile.WriteLine("set MEMORY_CHECK_INTERVAL=3")
tempFile.WriteLine("set DISABLE_CHARTS=True")
tempFile.WriteLine("set OPTIMIZE_DIMENSION_DETAIL=True")
tempFile.WriteLine("set ENABLE_LOG_FILTER=True")
tempFile.WriteLine("set SEPARATE_ANALYSIS_PROCESS=True")
tempFile.WriteLine("set ENABLE_BUTTON_TEXT_SUPREME_FIX=True")
tempFile.WriteLine("set FORCE_BUTTON_TEXT_VISIBILITY=True")
tempFile.WriteLine("set DEEPSEEK_API_KEY=***********************************")
tempFile.WriteLine("set QWEN_API_KEY=sk-6f3b4c6ad9f64f78b22bed422c5d278d")
tempFile.WriteLine("set DEFAULT_MODEL=deepseek-r1")
tempFile.WriteLine("set FORCE_REANALYSIS=True")
tempFile.WriteLine("set FORCE_REAL_API=True")
tempFile.WriteLine("set RELOAD_CONFIG=True")
tempFile.WriteLine("set VERSION=3.0")
tempFile.WriteLine("set TOTAL_DIMENSIONS=15")
tempFile.WriteLine("set ENABLE_ANALYSIS_STATUS_FIX=True")
tempFile.WriteLine("set USE_V3_CONSOLE=True")
tempFile.WriteLine("set API_ROUTE_FIX=True")
tempFile.WriteLine("set ENABLE_NOVEL_ANALYSIS_API_FIX=True")

tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("echo 检查Python是否可用...")
tempFile.WriteLine("python --version")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo Python未找到，请确保Python已安装并添加到PATH环境变量中")
tempFile.WriteLine("    pause")
tempFile.WriteLine("    exit /b 1")
tempFile.WriteLine(")")
tempFile.WriteLine("echo 启动九猫v3.0 API修复版系统...")
tempFile.WriteLine(pythonCmd & " -u -m src.web.v3_0_app")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo 启动失败，错误代码: %ERRORLEVEL%")
tempFile.WriteLine("    pause")
tempFile.WriteLine(")")
tempFile.Close

' 在后台运行批处理文件
shell.Run "cmd /c """ & workingDir & "\temp_run_v3_api_fixed.bat""", 1, False

' 等待5秒钟确保服务启动
WScript.Sleep 5000

' 打开浏览器访问系统
shell.Run "http://localhost:5001/", 1, False

' 显示启动成功消息
MsgBox "九猫小说分析写作系统 v3.0 API修复版已启动！" & vbCrLf & _
       "系统已在浏览器中打开，地址：http://localhost:5001/" & vbCrLf & _
       "如果浏览器未自动打开，请手动访问上述地址。" & vbCrLf & vbCrLf & _
       "此版本修复了reasoning_content API 404错误问题。", vbInformation, "九猫小说分析写作系统 v3.0 API修复版"

' 检查Python是否已安装
Function CheckPythonInstalled(cmd)
    On Error Resume Next
    shell.Run cmd & " --version", 0, True
    CheckPythonInstalled = (Err.Number = 0)
    On Error GoTo 0
End Function
