/**
 * 小说详情页历史分析修复脚本
 * 专门处理小说详情页中的历史分析问题
 * 自动注入版本
 */

// 自动注入脚本
(function() {
    // 检查是否在小说详情页
    if (window.location.pathname.match(/\/novel\/\d+\/?$/) || window.location.pathname.match(/\/novel\/\d+\/analysis/)) {
        console.log('[小说详情页历史分析修复] 检测到小说页面，执行修复');
        injectScript();
    }
    
    // 注入修复脚本
    function injectScript() {
        const scriptContent = function() {
            console.log('[小说详情页历史分析修复] 脚本已加载');
            
            // 安全的日志函数
            function safeLog(message, level = 'log') {
                try {
                    if (level === 'error') {
                        console.error(`[小说详情页修复] ${message}`);
                    } else if (level === 'warn') {
                        console.warn(`[小说详情页修复] ${message}`);
                    } else {
                        console.log(`[小说详情页修复] ${message}`);
                    }
                } catch (e) {
                    // 忽略日志错误
                }
            }
            
            // 获取当前小说ID
            function getNovelId() {
                // 尝试从URL中提取
                const urlMatch = window.location.pathname.match(/\/novel\/(\d+)/);
                if (urlMatch && urlMatch[1]) {
                    return urlMatch[1];
                }
                
                return null;
            }
            
            // 检查并处理完成的维度
            function checkAndHandleCompletedDimensions() {
                try {
                    // 检查是否有13/13完成标记
                    const cardElements = document.querySelectorAll('.card-body');
                    let completionFound = false;
                    
                    for (const card of cardElements) {
                        if (card.textContent.includes('13/13')) {
                            safeLog('找到13/13维度完成标记');
                            completionFound = true;
                            break;
                        }
                    }
                    
                    if (completionFound) {
                        // 查找分析维度链接
                        let dimensionLinks = document.querySelectorAll('a.badge-success');
                        if (dimensionLinks.length === 0) {
                            // 尝试其他选择器
                            dimensionLinks = document.querySelectorAll('a.list-group-item.badge-success');
                        }
                        
                        if (dimensionLinks.length > 0) {
                            safeLog(`找到 ${dimensionLinks.length} 个完成的维度链接`);
                            
                            // 延迟点击第一个链接，确保页面已完全加载
                            setTimeout(() => {
                                safeLog('自动点击第一个维度链接');
                                dimensionLinks[0].click();
                            }, 1000);
                        } else {
                            // 没有找到明确标记为完成的链接，尝试查找所有分析相关链接
                            const allAnalysisLinks = document.querySelectorAll('a[href*="/analysis/"]');
                            if (allAnalysisLinks.length > 0) {
                                safeLog(`找到 ${allAnalysisLinks.length} 个分析相关链接`);
                                
                                // 延迟点击第一个链接
                                setTimeout(() => {
                                    safeLog('自动点击第一个分析链接');
                                    allAnalysisLinks[0].click();
                                }, 1000);
                            } else {
                                // 如果没有找到任何链接，尝试通过API获取维度列表
                                const novelId = getNovelId();
                                if (novelId) {
                                    safeLog(`尝试通过API获取小说 ${novelId} 的维度列表`);
                                    
                                    fetch(`/api/novel/${novelId}/dimensions`)
                                        .then(response => response.json())
                                        .then(data => {
                                            if (data.success && data.dimensions && data.dimensions.length > 0) {
                                                const firstDimension = data.dimensions[0];
                                                safeLog(`获取到维度列表，第一个维度: ${firstDimension}`);
                                                
                                                // 跳转到第一个维度的分析页面
                                                safeLog(`跳转到分析页面: /novel/${novelId}/analysis/${firstDimension}`);
                                                window.location.href = `/novel/${novelId}/analysis/${firstDimension}`;
                                            } else {
                                                safeLog('未获取到有效的维度列表', 'warn');
                                            }
                                        })
                                        .catch(error => safeLog(`获取维度列表失败: ${error.message}`, 'error'));
                                }
                            }
                        }
                    }
                } catch (e) {
                    safeLog(`检查和处理完成维度时出错: ${e.message}`, 'error');
                }
            }
            
            // 处理分析页面
            function handleAnalysisPage() {
                try {
                    // 检查是否在分析页面
                    if (window.location.pathname.match(/\/analysis\//)) {
                        safeLog('检测到分析页面');
                        
                        const novelId = getNovelId();
                        const dimension = getDimension();
                        
                        if (!novelId || !dimension) {
                            safeLog(`无法获取小说ID或维度: novelId=${novelId}, dimension=${dimension}`, 'warn');
                            return;
                        }
                        
                        // 检查分析内容是否为空
                        const contentContainer = document.querySelector('.analysis-content');
                        if (contentContainer && contentContainer.innerHTML.trim() === '') {
                            safeLog('分析内容为空，尝试加载');
                            
                            // 直接使用fetch加载内容
                            fetch(`/api/novel/${novelId}/analysis/${dimension}`)
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success && data.result && data.result.content) {
                                        safeLog('获取到分析结果内容');
                                        contentContainer.innerHTML = data.result.content;
                                        safeLog('已更新分析内容');
                                    } else {
                                        safeLog('分析结果为空或无效', 'warn');
                                    }
                                })
                                .catch(error => safeLog(`直接获取分析结果失败: ${error.message}`, 'error'));
                        }
                        
                        // 检查分析状态是否正确
                        const statusElement = document.getElementById('analysisStatus');
                        if (statusElement && statusElement.textContent.trim() !== '已完成') {
                            safeLog('修正分析状态为已完成');
                            statusElement.className = 'badge badge-success px-3 py-2';
                            statusElement.textContent = '已完成';
                            
                            const progressBar = document.getElementById('progressBar');
                            if (progressBar) {
                                progressBar.className = 'progress-bar bg-success';
                                progressBar.style.width = '100%';
                                progressBar.setAttribute('aria-valuenow', 100);
                                progressBar.textContent = '100%';
                            }
                        }
                    }
                } catch (e) {
                    safeLog(`处理分析页面时出错: ${e.message}`, 'error');
                }
            }
            
            // 获取当前维度
            function getDimension() {
                // 尝试从URL中提取
                const urlMatch = window.location.pathname.match(/\/analysis\/([^\/]+)$/);
                if (urlMatch && urlMatch[1]) {
                    return urlMatch[1];
                }
                
                // 尝试从当前维度元素获取
                const currentDimension = document.getElementById('currentDimension');
                if (currentDimension && currentDimension.textContent.trim()) {
                    return currentDimension.textContent.trim();
                }
                
                return null;
            }
            
            // 主函数
            function main() {
                try {
                    safeLog('开始执行小说详情页历史分析修复');
                    
                    // 检查当前页面类型并处理
                    if (window.location.pathname.match(/\/novel\/\d+\/?$/)) {
                        safeLog('检测到小说详情页');
                        
                        // 延迟执行，确保页面已完全加载
                        setTimeout(checkAndHandleCompletedDimensions, 800);
                    } else if (window.location.pathname.match(/\/analysis\//)) {
                        safeLog('检测到分析页面');
                        
                        // 延迟执行，确保页面已完全加载
                        setTimeout(handleAnalysisPage, 800);
                        
                        // 定期检查分析内容
                        setInterval(handleAnalysisPage, 2000);
                    }
                } catch (e) {
                    safeLog(`主函数执行出错: ${e.message}`, 'error');
                }
            }
            
            // 页面加载完成后执行
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', main);
            } else {
                main();
            }
            
            safeLog('脚本加载完成');
        };
        
        // 创建并插入脚本
        const script = document.createElement('script');
        script.textContent = `(${scriptContent.toString()})();`;
        document.head.appendChild(script);
    }
})(); 