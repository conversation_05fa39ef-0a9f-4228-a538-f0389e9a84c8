/**
 * 九猫 - 专门修复novel_4.html页面的脚本
 */

// 立即执行函数
(function() {
    console.log('novel-page-fix.js 已加载');
    
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('检查novel页面是否需要修复');
        
        // 检查是否在novel页面
        if (window.location.pathname.includes('/novel/')) {
            console.log('当前在novel页面，开始修复');
            
            // 查找所有内联脚本
            var scripts = document.querySelectorAll('script:not([src])');
            
            scripts.forEach(function(script) {
                // 检查是否包含有问题的JSON.parse调用
                if (script.textContent && script.textContent.includes('JSON.parse(')) {
                    console.log('找到包含JSON.parse的脚本');
                    
                    // 修复脚本内容
                    var fixedContent = script.textContent.replace(
                        /JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g, 
                        function(match, quote, content) {
                            if (!match.endsWith(')')) {
                                console.log('修复缺少右括号的JSON.parse调用');
                                return 'JSON.parse(' + quote + content + quote + ')';
                            }
                            return match;
                        }
                    );
                    
                    // 如果内容被修改，替换脚本
                    if (fixedContent !== script.textContent) {
                        console.log('替换修复后的脚本');
                        var newScript = document.createElement('script');
                        newScript.textContent = fixedContent;
                        script.parentNode.replaceChild(newScript, script);
                    }
                }
                
                // 特别检查是否包含serverData = JSON.parse调用
                if (script.textContent && script.textContent.includes('serverData = JSON.parse(')) {
                    console.log('找到serverData = JSON.parse调用');
                    
                    // 修复特定的JSON.parse调用
                    var fixedContent = script.textContent.replace(
                        /serverData\s*=\s*JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g, 
                        function(match, quote, content) {
                            if (!match.endsWith(')')) {
                                console.log('修复serverData = JSON.parse调用');
                                return 'serverData = JSON.parse(' + quote + content + quote + ')';
                            }
                            return match;
                        }
                    );
                    
                    // 如果内容被修改，替换脚本
                    if (fixedContent !== script.textContent) {
                        console.log('替换修复后的serverData脚本');
                        var newScript = document.createElement('script');
                        newScript.textContent = fixedContent;
                        script.parentNode.replaceChild(newScript, script);
                    }
                }
                
                // 检查是否包含try { ... } catch (e) { ... }块中的JSON.parse
                if (script.textContent && script.textContent.includes('try') && 
                    script.textContent.includes('JSON.parse')) {
                    console.log('找到try-catch块中的JSON.parse');
                    
                    // 修复try-catch块中的JSON.parse调用
                    var fixedContent = script.textContent.replace(
                        /try\s*\{\s*([^}]*JSON\.parse\([^)]*)\s*\}\s*catch/g,
                        function(match, tryBlock) {
                            if (tryBlock.includes('JSON.parse(') && !tryBlock.includes('JSON.parse(') && tryBlock.includes(')')) {
                                console.log('修复try块中缺少右括号的JSON.parse调用');
                                var fixedTryBlock = tryBlock.replace(
                                    /(JSON\.parse\([^)]*)/g,
                                    '$1)'
                                );
                                return 'try { ' + fixedTryBlock + ' } catch';
                            }
                            return match;
                        }
                    );
                    
                    // 如果内容被修改，替换脚本
                    if (fixedContent !== script.textContent) {
                        console.log('替换修复后的try-catch脚本');
                        var newScript = document.createElement('script');
                        newScript.textContent = fixedContent;
                        script.parentNode.replaceChild(newScript, script);
                    }
                }
            });
            
            // 添加全局错误处理
            window.addEventListener('error', function(event) {
                if (event.error && event.error.message && 
                    (event.error.message.includes('missing ) after argument list') || 
                     event.error.message.includes('JSON.parse'))) {
                    console.error('捕获到JSON相关错误:', event.error.message);
                    console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }, true);
        }
    });
})();
