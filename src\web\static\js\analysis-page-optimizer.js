/**
 * 分析页面优化器
 * 优化分析页面的加载和显示
 */
(function() {
    console.log('[分析页面优化器] 脚本已加载');

    // 安全的日志函数
    function safeLog(message, level = 'log') {
        try {
            if (level === 'error') {
                console.error(`[分析页面优化器] ${message}`);
            } else if (level === 'warn') {
                console.warn(`[分析页面优化器] ${message}`);
            } else {
                console.log(`[分析页面优化器] ${message}`);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }

    // 获取当前小说ID
    function getNovelId() {
        // 尝试从日志容器获取
        const logContainer = document.getElementById('logContainer');
        if (logContainer && logContainer.dataset.novelId) {
            return logContainer.dataset.novelId;
        }
        
        // 尝试从URL中提取
        const urlMatch = window.location.pathname.match(/\/novel\/(\d+)/);
        if (urlMatch && urlMatch[1]) {
            return urlMatch[1];
        }
        
        // 尝试从面包屑导航中获取
        const breadcrumbLinks = document.querySelectorAll('.breadcrumb-item a');
        for (const link of breadcrumbLinks) {
            const href = link.getAttribute('href');
            if (href) {
                const novelMatch = href.match(/\/novel\/(\d+)/);
                if (novelMatch && novelMatch[1]) {
                    return novelMatch[1];
                }
            }
        }
        
        return null;
    }

    // 获取当前维度
    function getDimension() {
        // 尝试从内容容器获取
        const contentContainer = document.querySelector('.analysis-content');
        if (contentContainer && contentContainer.getAttribute('data-dimension')) {
            return contentContainer.getAttribute('data-dimension');
        }
        
        // 尝试从当前维度元素获取
        const currentDimension = document.getElementById('currentDimension');
        if (currentDimension && currentDimension.textContent.trim()) {
            return currentDimension.textContent.trim();
        }
        
        // 尝试从URL中提取
        const urlMatch = window.location.pathname.match(/\/analysis\/([^\/]+)$/);
        if (urlMatch && urlMatch[1]) {
            return urlMatch[1];
        }
        
        // 如果有多个维度链接，获取选中的那个
        const activeLink = document.querySelector('.list-group-item.active');
        if (activeLink) {
            const dimensionMatch = activeLink.textContent.trim().match(/^(\S+)/);
            if (dimensionMatch && dimensionMatch[1]) {
                return dimensionMatch[1];
            }
        }
        
        return null;
    }

    // 加载分析结果
    function loadAnalysisResult() {
        const novelId = getNovelId();
        const dimension = getDimension();
        
        if (!novelId || !dimension) {
            safeLog(`无法加载分析结果: novelId=${novelId}, dimension=${dimension}`, 'warn');
            return;
        }
        
        safeLog(`尝试加载分析结果: novelId=${novelId}, dimension=${dimension}`);
        
        if (window.fetchAnalysisResult && window.updateAnalysisResult) {
            window.fetchAnalysisResult(novelId, null, dimension)
                .then(result => {
                    const success = window.updateAnalysisResult(result);
                    if (success) {
                        safeLog('成功加载分析结果');
                    } else {
                        safeLog('更新分析结果失败', 'warn');
                    }
                })
                .catch(error => safeLog(`加载分析结果失败: ${error.message}`, 'error'));
        } else {
            safeLog('全局API函数未定义，无法加载分析结果', 'warn');
        }
    }

    // 改进分析状态显示
    function enhanceAnalysisStatusDisplay() {
        try {
            safeLog('改进分析状态显示');
            
            // 获取分析状态元素
            const statusElement = document.getElementById('analysisStatus');
            if (!statusElement) {
                safeLog('找不到分析状态元素', 'warn');
                return;
            }
            
            // 获取进度条元素
            const progressBar = document.getElementById('progressBar');
            if (!progressBar) {
                safeLog('找不到进度条元素', 'warn');
                return;
            }
            
            // 检查分析是否完成
            const isCompleted = statusElement.textContent.trim() === '已完成' || 
                              statusElement.classList.contains('badge-success');
            
            // 检查13个维度全部完成
            const dimensionText = document.querySelector('.card-title')?.textContent || '';
            const is13DimensionsCompleted = dimensionText.includes('13/13') || 
                                          dimensionText.includes('13个维度已完成');
            
            if (isCompleted || is13DimensionsCompleted) {
                safeLog(`检测到分析已完成状态 (isCompleted=${isCompleted}, is13DimensionsCompleted=${is13DimensionsCompleted})`);
                
                // 确保状态显示为完成
                statusElement.className = 'badge badge-success px-3 py-2';
                statusElement.textContent = '已完成';
                progressBar.className = 'progress-bar bg-success';
                progressBar.style.width = '100%';
                progressBar.setAttribute('aria-valuenow', 100);
                progressBar.textContent = '100%';
                
                // 确保分析结果可见
                const analysisResults = document.querySelectorAll('.analysis-content');
                let hasVisibleContent = false;
                
                analysisResults.forEach(result => {
                    if (result.innerHTML.trim() !== '') {
                        hasVisibleContent = true;
                    }
                });
                
                // 如果没有可见内容，尝试重新加载分析结果
                if (!hasVisibleContent) {
                    safeLog('没有发现可见的分析结果，尝试重新加载', 'warn');
                    loadAnalysisResult();
                }
            }
        } catch (e) {
            safeLog(`改进分析状态显示时出错: ${e.message}`, 'error');
        }
    }

    // 页面加载优化
    function optimizePage() {
        try {
            safeLog('页面加载优化');
            
            // 延迟加载非关键资源
            const nonCriticalResources = document.querySelectorAll('img:not([data-critical]), script:not([data-critical])');
            for (const resource of nonCriticalResources) {
                if (resource.tagName === 'IMG' && !resource.hasAttribute('loading')) {
                    resource.setAttribute('loading', 'lazy');
                    safeLog('添加懒加载属性到图片');
                }
            }
            
            // 改进分析状态显示
            enhanceAnalysisStatusDisplay();
            
            safeLog('页面优化完成');
        } catch (e) {
            safeLog(`页面优化时出错: ${e.message}`, 'error');
        }
    }

    // 页面加载完成后优化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', optimizePage);
    } else {
        optimizePage();
    }
    
    // 立即执行一次分析状态检查
    setTimeout(enhanceAnalysisStatusDisplay, 300);
    
    // 定期检查
    setInterval(enhanceAnalysisStatusDisplay, 3000);
    
    safeLog('脚本加载完成');
})();
