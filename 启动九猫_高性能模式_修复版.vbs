' 九猫小说分析系统启动脚本 (VBS版本) - 高性能模式
' 此脚本会在后台启动九猫系统，并自动打开浏览器，无需显示命令行窗口
' 高性能模式：保持并行分析功能，增加线程池和数据库连接池大小

' 设置工作目录为脚本所在目录
Set fso = CreateObject("Scripting.FileSystemObject")
Set WshShell = CreateObject("WScript.Shell")
currentPath = fso.GetParentFolderName(WScript.ScriptFullName)
WshShell.CurrentDirectory = currentPath

' 检查并创建日志目录
If Not fso.FolderExists("logs") Then
    fso.CreateFolder("logs")
End If

' 清理旧日志文件，只保留最新的5个
Sub CleanupOldLogs()
    On Error Resume Next

    Dim logFolder, logFiles, file
    Set logFolder = fso.GetFolder(currentPath & "\logs")
    Set logFiles = logFolder.Files

    ' 创建一个数组来存储日志文件
    Dim fileArray()
    ReDim fileArray(logFiles.Count - 1)

    ' 填充数组
    Dim i, fileCount
    i = 0
    fileCount = 0

    For Each file In logFiles
        If LCase(Right(file.Name, 4)) = ".log" Then
            fileArray(i) = file.Path
            i = i + 1
            fileCount = fileCount + 1
        End If
    Next

    ' 如果文件数量超过5个，删除最旧的文件
    If fileCount > 5 Then
        ' 按修改日期排序（冒泡排序）
        Dim j, temp
        For i = 0 To fileCount - 2
            For j = 0 To fileCount - i - 2
                If fso.GetFile(fileArray(j)).DateLastModified > fso.GetFile(fileArray(j+1)).DateLastModified Then
                    temp = fileArray(j)
                    fileArray(j) = fileArray(j+1)
                    fileArray(j+1) = temp
                End If
            Next
        Next

        ' 删除最旧的文件（保留最新的5个）
        For i = 0 To fileCount - 6
            If fileArray(i) <> "" Then
                fso.DeleteFile(fileArray(i))
            End If
        Next
    End If
End Sub

' 检查psutil是否已安装
Function IsPsutilInstalled()
    Dim result
    result = WshShell.Run("python -c ""import psutil""", 0, True)
    IsPsutilInstalled = (result = 0)
End Function

' 安装psutil
Sub InstallPsutil()
    Dim result
    result = WshShell.Run("pip install psutil", 1, True)
    If result <> 0 Then
        MsgBox "无法安装psutil模块。系统将以有限的内存监控功能运行。" & vbCrLf & _
               "建议手动安装: pip install psutil", _
               48, "九猫小说分析系统 - 警告"
    End If
End Sub

' 检查端口5001是否被占用
Function IsPortInUse()
    Dim result
    result = WshShell.Run("netstat -ano | findstr :5001 | findstr LISTENING", 0, True)
    IsPortInUse = (result = 0)
End Function

' 终止占用端口5001的进程
Sub KillPort5001Process()
    On Error Resume Next

    ' 创建临时批处理文件来终止进程
    Set killPortFile = fso.CreateTextFile("kill_port.bat", True)
    killPortFile.WriteLine("@echo off")
    killPortFile.WriteLine("for /f ""tokens=5"" %%a in ('netstat -ano ^| findstr :5001 ^| findstr LISTENING') do (")
    killPortFile.WriteLine("    taskkill /f /pid %%a >nul 2>&1")
    killPortFile.WriteLine(")")
    killPortFile.Close

    ' 运行批处理文件
    WshShell.Run "kill_port.bat", 0, True

    ' 删除临时文件
    fso.DeleteFile "kill_port.bat"
End Sub

' 检查Python是否可用
Function IsPythonAvailable()
    Dim result
    result = WshShell.Run("python --version", 0, True)
    IsPythonAvailable = (result = 0)
End Function

' 检查run.py文件是否存在
Function IsRunPyExists()
    IsRunPyExists = fso.FileExists(currentPath & "\run.py")
End Function

' 执行日志清理
CleanupOldLogs

' 检查Python是否可用
If Not IsPythonAvailable() Then
    MsgBox "未检测到Python环境，请确保已安装Python并添加到系统PATH中。", _
           vbCritical, "九猫小说分析系统 - 错误"
    WScript.Quit
End If

' 检查run.py文件是否存在
If Not IsRunPyExists() Then
    MsgBox "未找到run.py文件，请确保脚本位于九猫系统根目录中。", _
           vbCritical, "九猫小说分析系统 - 错误"
    WScript.Quit
End If

' 检查并安装psutil
If Not IsPsutilInstalled() Then
    If MsgBox("未检测到psutil模块，该模块用于内存监控和优化。" & vbCrLf & _
              "是否立即安装？", _
              vbYesNo + vbQuestion, "九猫小说分析系统") = vbYes Then
        InstallPsutil
    End If
End If

' 检查并释放端口
If IsPortInUse() Then
    If MsgBox("端口5001已被占用，需要释放才能启动九猫系统。" & vbCrLf & _
              "是否尝试终止占用该端口的进程？", _
              vbYesNo + vbQuestion, "九猫小说分析系统") = vbYes Then
        KillPort5001Process
    End If
End If

' 显示启动消息
MsgBox "九猫小说分析系统正在启动（高性能模式 - 优化版）..." & vbCrLf & vbCrLf & _
       "系统将在后台运行，浏览器将在15秒钟后自动打开。" & vbCrLf & _
       "请不要关闭浏览器窗口，否则需要手动访问: http://localhost:5001" & vbCrLf & vbCrLf & _
       "高性能模式已启用，特别优化了以下配置：" & vbCrLf & _
       "- 增加数据库连接池大小（100/100）" & vbCrLf & _
       "- 增加线程池大小（16线程）" & vbCrLf & _
       "- 优化并行分析参数" & vbCrLf & _
       "- 特别优化了高潮节奏和人物关系分析", _
       64, "九猫小说分析系统 - 高性能优化模式"

' 创建启动日志文件
Dim logFile
Set logFile = fso.CreateTextFile("logs\startup_" & Replace(Replace(Replace(Now(), ":", "-"), "/", "-"), " ", "_") & ".log", True)
logFile.WriteLine("九猫小说分析系统启动日志")
logFile.WriteLine("时间: " & Now())
logFile.WriteLine("工作目录: " & currentPath)
logFile.WriteLine("高性能模式已启用")
logFile.WriteLine("-----------------------------------")
logFile.Close

' 创建临时批处理文件来设置环境变量并运行Python
Set tempFile = fso.CreateTextFile("temp_run.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & currentPath & """")
tempFile.WriteLine("echo 九猫小说分析系统启动中...")
tempFile.WriteLine("echo 工作目录: %CD%")
tempFile.WriteLine("echo -----------------------------------")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("set MEMORY_OPTIMIZED=True")
tempFile.WriteLine("set LOW_MEMORY_MODE=False")
tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=75")
tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=85")
tempFile.WriteLine("set MAX_DB_CONNECTIONS=150")
tempFile.WriteLine("set DB_POOL_SIZE=100")
tempFile.WriteLine("set DB_MAX_OVERFLOW=100")
tempFile.WriteLine("set THREAD_POOL_SIZE=16")
tempFile.WriteLine("set MAX_WORKERS=16")
tempFile.WriteLine("set DISABLE_PARALLEL_ANALYSIS=False")
tempFile.WriteLine("set PARALLEL_ANALYSIS_ENABLED=True")
tempFile.WriteLine("set REDUCE_LOGGING=True")
tempFile.WriteLine("set MAX_CHUNK_SIZE=8000")
tempFile.WriteLine("set OVERLAP_SIZE=100")
tempFile.WriteLine("set MAX_PARALLEL_ANALYSES=8")
tempFile.WriteLine("set MAX_CHUNK_WORKERS=8")
tempFile.WriteLine("rem 特别优化高潮节奏和人物关系分析")
tempFile.WriteLine("set DIMENSION_CHUNK_SIZES_CLIMAX_PACING=10000")
tempFile.WriteLine("set DIMENSION_CHUNK_SIZES_CHARACTER_RELATIONSHIPS=10000")
tempFile.WriteLine("set DIMENSION_MAX_TOKENS_CLIMAX_PACING=3000")
tempFile.WriteLine("set DIMENSION_MAX_TOKENS_CHARACTER_RELATIONSHIPS=3000")
tempFile.WriteLine("set OPTIMIZE_MEMORY_FOR_LARGE_ANALYSES=True")
tempFile.WriteLine("rem 错误处理和恢复配置")
tempFile.WriteLine("set AUTO_RESET_ON_ERROR=True")
tempFile.WriteLine("set MAX_RETRIES_ON_ERROR=3")
tempFile.WriteLine("set ERROR_RECOVERY_DELAY=5")
tempFile.WriteLine("set ENABLE_CONNECTION_POOL_MONITORING=True")
tempFile.WriteLine("rem 内存管理优化")
tempFile.WriteLine("set GC_COLLECT_FREQUENCY=60")
tempFile.WriteLine("set CLEAN_TEMP_FILES=True")
tempFile.WriteLine("set STARTUP_MODE=True")
tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("echo 启动Python服务...")
tempFile.WriteLine("echo -----------------------------------")
tempFile.WriteLine("python run.py")
tempFile.WriteLine("if errorlevel 1 (")
tempFile.WriteLine("    echo 启动失败，请检查错误信息")
tempFile.WriteLine("    echo 按任意键退出...")
tempFile.WriteLine("    pause > nul")
tempFile.WriteLine(")")
tempFile.Close

' 在后台运行批处理文件，显示窗口以便查看输出和错误信息
WshShell.Run "temp_run.bat", 1, False

' 等待15秒钟确保服务启动
WScript.Sleep 15000

' 检查服务是否已启动
Dim isServerRunning
isServerRunning = False

' 尝试3次检查服务是否启动
For i = 1 To 3
    If IsPortInUse() Then
        isServerRunning = True
        Exit For
    End If
    WScript.Sleep 5000 ' 等待5秒再次检查
Next

' 如果服务已启动，打开浏览器
If isServerRunning Then
    ' 打开浏览器 - 使用cmd命令强制使用默认浏览器
    WshShell.Run "cmd /c start http://localhost:5001", 0, False
Else
    ' 服务未启动，显示错误消息
    MsgBox "九猫系统服务未能正常启动，请检查temp_run.bat窗口中的错误信息。" & vbCrLf & _
           "可能的原因：" & vbCrLf & _
           "1. Python环境配置问题" & vbCrLf & _
           "2. 缺少必要的Python依赖" & vbCrLf & _
           "3. 端口5001被占用" & vbCrLf & _
           "4. 工作目录设置不正确", _
           vbCritical, "九猫小说分析系统 - 启动失败"
End If
