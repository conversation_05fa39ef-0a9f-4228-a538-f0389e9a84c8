/**
 * 九猫系统 - 智能资源加载器
 * 版本: 1.0.0
 * 
 * 该脚本根据预加载检测器的结果动态加载资源
 * 在CDN资源失败时自动切换到本地资源
 */

(function() {
    console.log('[九猫修复] 智能资源加载器已启动');
    
    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {}, settings: {} };
    
    // 如果已经运行过，不再重复执行
    if (window.__nineCatsFixes.loaded['smart-resource-loader']) {
        console.log('[九猫修复] 智能资源加载器已经运行过，跳过');
        return;
    }
    
    // 资源配置
    const resourceConfig = {
        // 关键资源列表
        criticalResources: [
            {
                type: 'css',
                cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
                local: '/static/css/bootstrap.min.css',
                id: 'bootstrap-css',
                loaded: false
            },
            {
                type: 'js',
                cdn: 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js',
                local: '/static/js/lib/jquery.min.js',
                id: 'jquery-js',
                loaded: false,
                check: function() { return typeof jQuery !== 'undefined'; }
            },
            {
                type: 'js',
                cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
                local: '/static/js/lib/bootstrap.bundle.min.js',
                id: 'bootstrap-js',
                loaded: false,
                check: function() { return typeof bootstrap !== 'undefined'; }
            },
            {
                type: 'css',
                cdn: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
                local: '/static/css/fontawesome.min.css',
                id: 'fontawesome-css',
                loaded: false
            }
        ],
        
        // 加载超时时间（毫秒）
        timeout: 3000,
        
        // 最大重试次数
        maxRetries: 2
    };
    
    // 资源加载状态
    const loadingStatus = {
        resourcesLoaded: 0,
        resourcesFailed: 0,
        retries: {},
        loadingStarted: false
    };
    
    // 获取资源策略
    function getResourceStrategy() {
        // 如果预加载检测器已经决定了策略，使用它
        if (window.__nineCatsFixes.resourceStrategy) {
            return window.__nineCatsFixes.resourceStrategy.useCDN;
        }
        
        // 如果localStorage中有策略，使用它
        const storedStrategy = localStorage.getItem('nineCats_resourceStrategy');
        if (storedStrategy) {
            try {
                const strategy = JSON.parse(storedStrategy);
                return strategy.useCDN;
            } catch (e) {
                console.error('[九猫修复] 解析存储的资源策略失败:', e);
            }
        }
        
        // 默认使用本地资源（更安全的选择）
        return false;
    }
    
    // 加载单个资源
    function loadResource(resource, useCDN = null) {
        // 如果资源已加载，跳过
        if (resource.loaded) {
            return Promise.resolve(true);
        }
        
        // 如果未指定是否使用CDN，使用全局策略
        if (useCDN === null) {
            useCDN = getResourceStrategy();
        }
        
        // 选择URL
        const url = useCDN ? resource.cdn : resource.local;
        console.log(`[九猫修复] 加载${resource.type === 'js' ? '脚本' : '样式表'}: ${url}`);
        
        return new Promise((resolve) => {
            // 创建元素
            let element;
            if (resource.type === 'js') {
                element = document.createElement('script');
                element.type = 'text/javascript';
                element.src = url;
                element.async = false; // 确保按顺序执行
            } else {
                element = document.createElement('link');
                element.rel = 'stylesheet';
                element.type = 'text/css';
                element.href = url;
            }
            
            // 设置ID
            element.id = 'smart-loader-' + resource.id;
            
            // 设置超时
            const timeout = setTimeout(() => {
                console.error(`[九猫修复] 加载超时: ${url}`);
                
                // 如果是CDN资源，尝试切换到本地资源
                if (useCDN) {
                    console.log(`[九猫修复] 切换到本地资源: ${resource.local}`);
                    loadResource(resource, false).then(resolve);
                } else {
                    resolve(false);
                }
            }, resourceConfig.timeout);
            
            // 加载成功
            element.onload = function() {
                clearTimeout(timeout);
                console.log(`[九猫修复] 加载成功: ${url}`);
                resource.loaded = true;
                loadingStatus.resourcesLoaded++;
                resolve(true);
            };
            
            // 加载失败
            element.onerror = function() {
                clearTimeout(timeout);
                console.error(`[九猫修复] 加载失败: ${url}`);
                
                // 记录重试次数
                loadingStatus.retries[resource.id] = (loadingStatus.retries[resource.id] || 0) + 1;
                
                // 如果是CDN资源，尝试切换到本地资源
                if (useCDN) {
                    console.log(`[九猫修复] 切换到本地资源: ${resource.local}`);
                    loadResource(resource, false).then(resolve);
                } 
                // 如果是本地资源且未超过最大重试次数，重试
                else if (loadingStatus.retries[resource.id] <= resourceConfig.maxRetries) {
                    console.log(`[九猫修复] 重试加载本地资源 (${loadingStatus.retries[resource.id]}/${resourceConfig.maxRetries}): ${resource.local}`);
                    setTimeout(() => {
                        loadResource(resource, false).then(resolve);
                    }, 500);
                }
                // 超过最大重试次数，放弃
                else {
                    console.error(`[九猫修复] 放弃加载资源: ${resource.id}`);
                    loadingStatus.resourcesFailed++;
                    resolve(false);
                }
            };
            
            // 添加到文档
            document.head.appendChild(element);
        });
    }
    
    // 检查资源是否已通过其他方式加载
    function checkResourceAlreadyLoaded(resource) {
        // 如果资源有检查函数，使用它
        if (resource.check && resource.check()) {
            console.log(`[九猫修复] 资源已通过其他方式加载: ${resource.id}`);
            resource.loaded = true;
            return true;
        }
        
        // 检查DOM中是否已存在该资源
        if (resource.type === 'js') {
            const scripts = document.querySelectorAll('script[src]');
            for (let i = 0; i < scripts.length; i++) {
                if (scripts[i].src.includes(resource.cdn.split('/').pop()) || 
                    scripts[i].src.includes(resource.local.split('/').pop())) {
                    console.log(`[九猫修复] 脚本已存在于DOM中: ${resource.id}`);
                    resource.loaded = true;
                    return true;
                }
            }
        } else {
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            for (let i = 0; i < links.length; i++) {
                if (links[i].href.includes(resource.cdn.split('/').pop()) || 
                    links[i].href.includes(resource.local.split('/').pop())) {
                    console.log(`[九猫修复] 样式表已存在于DOM中: ${resource.id}`);
                    resource.loaded = true;
                    return true;
                }
            }
        }
        
        return false;
    }
    
    // 加载所有关键资源
    function loadAllCriticalResources() {
        if (loadingStatus.loadingStarted) {
            return;
        }
        
        loadingStatus.loadingStarted = true;
        console.log('[九猫修复] 开始加载关键资源');
        
        // 首先检查资源是否已加载
        resourceConfig.criticalResources.forEach(checkResourceAlreadyLoaded);
        
        // 按顺序加载资源
        let chain = Promise.resolve();
        resourceConfig.criticalResources.forEach(resource => {
            if (!resource.loaded) {
                chain = chain.then(() => loadResource(resource));
            }
        });
        
        // 加载完成后的处理
        chain.then(() => {
            console.log(`[九猫修复] 关键资源加载完成: ${loadingStatus.resourcesLoaded}成功, ${loadingStatus.resourcesFailed}失败`);
            
            // 触发自定义事件
            const event = new CustomEvent('criticalResourcesLoaded', {
                detail: {
                    success: loadingStatus.resourcesLoaded,
                    failed: loadingStatus.resourcesFailed
                }
            });
            document.dispatchEvent(event);
        });
    }
    
    // 监听资源策略决定事件
    document.addEventListener('resourceStrategyDecided', function(event) {
        console.log('[九猫修复] 收到资源策略决定事件:', event.detail);
        loadAllCriticalResources();
    });
    
    // 如果DOMContentLoaded已触发，立即加载资源
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadAllCriticalResources);
    } else {
        loadAllCriticalResources();
    }
    
    // 标记为已加载
    window.__nineCatsFixes.loaded['smart-resource-loader'] = true;
    
    console.log('[九猫修复] 智能资源加载器初始化完成');
})();
