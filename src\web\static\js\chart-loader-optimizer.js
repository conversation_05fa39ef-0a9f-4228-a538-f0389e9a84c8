/**
 * 九猫 - Chart.js加载优化器
 * 优化Chart.js库的加载性能
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('Chart.js加载优化器已加载');
    
    // 配置参数
    const CONFIG = {
        preloadChartJs: true,       // 预加载Chart.js
        useLocalCopy: true,         // 优先使用本地副本
        useCDNFallback: true,       // 使用CDN作为备选
        cacheInLocalStorage: true,  // 缓存到localStorage
        maxCacheAge: 86400000,      // 缓存最大有效期（毫秒）
        cdnUrls: [
            'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js',
            'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js'
        ]
    };
    
    // Chart.js加载状态
    let chartJsLoaded = false;
    let loadingPromise = null;
    let loadStartTime = 0;
    
    // 检查Chart.js是否已加载
    function isChartJsLoaded() {
        return typeof Chart !== 'undefined' || chartJsLoaded;
    }
    
    // 从localStorage加载Chart.js
    function loadFromCache() {
        if (!CONFIG.cacheInLocalStorage) return null;
        
        try {
            // 获取缓存数据
            const cachedData = localStorage.getItem('chartjs_cached_script');
            const cachedTime = localStorage.getItem('chartjs_cached_time');
            
            // 检查缓存是否存在且未过期
            if (cachedData && cachedTime) {
                const cacheAge = Date.now() - parseInt(cachedTime, 10);
                if (cacheAge < CONFIG.maxCacheAge) {
                    console.log(`从缓存加载Chart.js (缓存年龄: ${Math.round(cacheAge / 1000 / 60)} 分钟)`);
                    return cachedData;
                } else {
                    console.log('Chart.js缓存已过期，将重新加载');
                }
            }
        } catch (e) {
            console.error('从缓存加载Chart.js时出错:', e);
        }
        
        return null;
    }
    
    // 保存Chart.js到localStorage
    function saveToCache(scriptContent) {
        if (!CONFIG.cacheInLocalStorage) return;
        
        try {
            localStorage.setItem('chartjs_cached_script', scriptContent);
            localStorage.setItem('chartjs_cached_time', Date.now().toString());
            console.log('Chart.js已缓存到localStorage');
        } catch (e) {
            console.error('缓存Chart.js时出错:', e);
        }
    }
    
    // 加载Chart.js
    function loadChartJs() {
        // 如果已经加载或正在加载，直接返回
        if (isChartJsLoaded() || loadingPromise) {
            return loadingPromise || Promise.resolve();
        }
        
        // 记录加载开始时间
        loadStartTime = performance.now();
        
        // 创建加载Promise
        loadingPromise = new Promise((resolve, reject) => {
            // 首先尝试从缓存加载
            const cachedScript = loadFromCache();
            if (cachedScript) {
                try {
                    // 创建脚本元素
                    const script = document.createElement('script');
                    script.textContent = cachedScript;
                    document.head.appendChild(script);
                    
                    // 标记为已加载
                    chartJsLoaded = true;
                    
                    // 记录加载时间
                    const loadTime = performance.now() - loadStartTime;
                    console.log(`Chart.js从缓存加载完成 (${loadTime.toFixed(2)}ms)`);
                    
                    // 解析Promise
                    resolve();
                    return;
                } catch (e) {
                    console.error('执行缓存的Chart.js脚本时出错:', e);
                    // 继续尝试其他加载方式
                }
            }
            
            // 尝试加载本地副本
            if (CONFIG.useLocalCopy) {
                const localUrl = '/static/js/lib/chart.min.js';
                
                // 创建脚本元素
                const script = document.createElement('script');
                script.src = localUrl;
                
                // 加载成功处理
                script.onload = function() {
                    // 标记为已加载
                    chartJsLoaded = true;
                    
                    // 记录加载时间
                    const loadTime = performance.now() - loadStartTime;
                    console.log(`Chart.js从本地加载完成 (${loadTime.toFixed(2)}ms)`);
                    
                    // 尝试缓存脚本内容
                    if (CONFIG.cacheInLocalStorage) {
                        fetch(localUrl)
                            .then(response => response.text())
                            .then(scriptContent => {
                                saveToCache(scriptContent);
                            })
                            .catch(e => {
                                console.error('获取Chart.js脚本内容时出错:', e);
                            });
                    }
                    
                    // 解析Promise
                    resolve();
                };
                
                // 加载失败处理
                script.onerror = function() {
                    console.error('从本地加载Chart.js失败，尝试使用CDN');
                    
                    // 如果配置了使用CDN作为备选，尝试从CDN加载
                    if (CONFIG.useCDNFallback && CONFIG.cdnUrls.length > 0) {
                        loadFromCDN(CONFIG.cdnUrls, 0, resolve, reject);
                    } else {
                        reject(new Error('加载Chart.js失败'));
                    }
                };
                
                // 添加到文档
                document.head.appendChild(script);
            } else if (CONFIG.useCDNFallback && CONFIG.cdnUrls.length > 0) {
                // 直接从CDN加载
                loadFromCDN(CONFIG.cdnUrls, 0, resolve, reject);
            } else {
                reject(new Error('没有可用的Chart.js加载方式'));
            }
        });
        
        return loadingPromise;
    }
    
    // 从CDN加载Chart.js
    function loadFromCDN(urls, index, resolve, reject) {
        if (index >= urls.length) {
            reject(new Error('所有CDN加载尝试均失败'));
            return;
        }
        
        // 获取当前URL
        const url = urls[index];
        
        // 创建脚本元素
        const script = document.createElement('script');
        script.src = url;
        
        // 加载成功处理
        script.onload = function() {
            // 标记为已加载
            chartJsLoaded = true;
            
            // 记录加载时间
            const loadTime = performance.now() - loadStartTime;
            console.log(`Chart.js从CDN加载完成 (${loadTime.toFixed(2)}ms): ${url}`);
            
            // 尝试缓存脚本内容
            if (CONFIG.cacheInLocalStorage) {
                fetch(url)
                    .then(response => response.text())
                    .then(scriptContent => {
                        saveToCache(scriptContent);
                    })
                    .catch(e => {
                        console.error('获取Chart.js脚本内容时出错:', e);
                    });
            }
            
            // 解析Promise
            resolve();
        };
        
        // 加载失败处理
        script.onerror = function() {
            console.error(`从CDN加载Chart.js失败: ${url}，尝试下一个CDN`);
            
            // 尝试下一个CDN
            loadFromCDN(urls, index + 1, resolve, reject);
        };
        
        // 添加到文档
        document.head.appendChild(script);
    }
    
    // 预加载Chart.js
    if (CONFIG.preloadChartJs) {
        // 使用requestIdleCallback在浏览器空闲时预加载
        if (window.requestIdleCallback) {
            window.requestIdleCallback(function() {
                loadChartJs().catch(function(error) {
                    console.error('预加载Chart.js时出错:', error);
                });
            });
        } else {
            // 如果不支持requestIdleCallback，使用setTimeout延迟加载
            setTimeout(function() {
                loadChartJs().catch(function(error) {
                    console.error('预加载Chart.js时出错:', error);
                });
            }, 1000);
        }
    }
    
    // 导出到全局对象
    window.chartJsOptimizer = {
        loadChartJs: loadChartJs,
        isLoaded: isChartJsLoaded
    };
})();
