/**
 * 全局jQuery错误处理脚本
 * 用于捕获和修复jQuery相关错误
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 */

(function() {
    console.log('[全局jQuery错误处理] 初始化...');

    // 检查jQuery是否已加载
    function checkJQuery() {
        if (typeof jQuery !== 'undefined') {
            console.log('[全局jQuery错误处理] jQuery已加载，版本:', jQuery.fn.jquery);
            return true;
        }
        console.log('[全局jQuery错误处理] jQuery未加载');
        return false;
    }

    // 加载jQuery
    function loadJQuery(callback) {
        console.log('[全局jQuery错误处理] 加载jQuery...');
        
        const script = document.createElement('script');
        script.src = '/static/js/lib/jquery.min.js';
        
        script.onload = function() {
            console.log('[全局jQuery错误处理] jQuery加载成功');
            if (callback) callback(jQuery);
        };
        
        script.onerror = function() {
            console.error('[全局jQuery错误处理] jQuery加载失败');
            if (callback) callback(null);
        };
        
        document.head.appendChild(script);
    }

    // 修复jQuery.fn.on方法
    function fixJQueryOn($) {
        if (!$ || typeof $.fn.on === 'function') return;
        
        console.log('[全局jQuery错误处理] 修复jQuery.fn.on方法');
        
        $.fn.on = function(events, selector, data, handler) {
            // 处理参数
            if (typeof selector === 'function') {
                handler = selector;
                selector = undefined;
                data = undefined;
            } else if (typeof data === 'function') {
                handler = data;
                data = undefined;
            }

            if (!handler) return this;

            // 处理多个事件
            if (events.indexOf(' ') > -1) {
                const eventArray = events.split(' ');
                for (let i = 0; i < eventArray.length; i++) {
                    this.on(eventArray[i], selector, data, handler);
                }
                return this;
            }

            return this.each(function() {
                const element = this;

                // 处理事件委托
                if (selector) {
                    const originalHandler = handler;
                    handler = function(e) {
                        const target = e.target;
                        const matches = element.querySelectorAll(selector);
                        let current = target;

                        while (current && current !== element) {
                            for (let i = 0; i < matches.length; i++) {
                                if (current === matches[i]) {
                                    e.delegateTarget = element;
                                    e.currentTarget = current;
                                    originalHandler.call(current, e);
                                    return;
                                }
                            }
                            current = current.parentNode;
                        }
                    };
                }

                // 处理Bootstrap事件
                if (events.includes('.bs.')) {
                    const baseEvent = events.split('.')[0];
                    element.addEventListener(baseEvent, handler);
                } else {
                    element.addEventListener(events, handler);
                }
            });
        };
    }

    // 修复jQuery.fn.tab方法
    function fixJQueryTab($) {
        if (!$ || typeof $.fn.tab === 'function') return;
        
        console.log('[全局jQuery错误处理] 修复jQuery.fn.tab方法');
        
        $.fn.tab = function(action) {
            if (action === 'show') {
                return this.each(function() {
                    this.click();
                });
            }
            return this;
        };
    }

    // 修复jQuery.fn.trigger方法
    function fixJQueryTrigger($) {
        if (!$ || typeof $.fn.trigger === 'function') return;
        
        console.log('[全局jQuery错误处理] 修复jQuery.fn.trigger方法');
        
        $.fn.trigger = function(eventType) {
            return this.each(function() {
                try {
                    const event = new Event(eventType);
                    this.dispatchEvent(event);
                } catch (e) {
                    try {
                        const event = document.createEvent('Event');
                        event.initEvent(eventType, true, true);
                        this.dispatchEvent(event);
                    } catch (ie_e) {
                        console.error('[全局jQuery错误处理] 触发事件失败:', ie_e);
                    }
                }
            });
        };
    }

    // 修复所有jQuery方法
    function fixAllJQueryMethods($) {
        if (!$) return;
        
        console.log('[全局jQuery错误处理] 修复所有jQuery方法');
        
        fixJQueryOn($);
        fixJQueryTab($);
        fixJQueryTrigger($);
        
        // 确保$ 变量可用
        if (typeof window.$ === 'undefined') {
            console.log('[全局jQuery错误处理] $ 变量未定义，设置 $ = jQuery');
            window.$ = $;
        }
    }

    // 全局错误处理函数
    function handleGlobalError(event) {
        console.error('[全局jQuery错误处理] 捕获到错误:', event.message, '来源:', event.filename, '行:', event.lineno);

        // 检查是否是jQuery相关错误
        if (event.message && (
            event.message.includes('$ is not defined') ||
            event.message.includes('$(...).on is not a function') ||
            event.message.includes('$(...).tab is not a function') ||
            event.message.includes('$(...).trigger is not a function') ||
            event.message.includes('$(...).off is not a function') ||
            event.message.includes('jQuery') ||
            event.message.includes('jquery')
        )) {
            console.error('[全局jQuery错误处理] 检测到jQuery相关错误，尝试修复');

            // 检查jQuery是否已加载
            if (checkJQuery()) {
                // jQuery已加载，修复方法
                fixAllJQueryMethods(jQuery);
            } else {
                // jQuery未加载，加载jQuery
                loadJQuery(function($) {
                    if ($) {
                        fixAllJQueryMethods($);
                    } else {
                        console.error('[全局jQuery错误处理] 无法加载jQuery，无法修复错误');
                    }
                });
            }

            // 重新加载出错的脚本
            if (event.filename && event.filename.includes('console.js')) {
                console.log('[全局jQuery错误处理] 尝试重新加载console.js脚本');
                setTimeout(function() {
                    const script = document.createElement('script');
                    script.src = '/static/js/v3/console.js?_=' + new Date().getTime(); // 添加时间戳防止缓存
                    document.body.appendChild(script);
                }, 500); // 延迟500ms，确保jQuery已修复
            }
        }
    }

    // 添加全局错误处理
    window.addEventListener('error', handleGlobalError);

    // 检查并修复jQuery
    if (checkJQuery()) {
        fixAllJQueryMethods(jQuery);
    }

    // 导出全局函数
    window.fixJQueryGlobally = function() {
        if (checkJQuery()) {
            fixAllJQueryMethods(jQuery);
        } else {
            loadJQuery(function($) {
                if ($) fixAllJQueryMethods($);
            });
        }
    };

    console.log('[全局jQuery错误处理] 初始化完成');
})();