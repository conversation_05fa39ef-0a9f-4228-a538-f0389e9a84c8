{% extends "v3.1/base.html" %}

{% block title %}分析{{ dimension_info.name }} - {{ chapter.title }} - {{ novel.title }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .analysis-header {
        background: linear-gradient(135deg, #FFF8E1, #FFFCF5);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
        border: 1px solid #E6D7B9;
    }

    .analysis-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .analysis-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        color: #5D4037;
    }

    .analysis-header p {
        position: relative;
        z-index: 1;
        color: #5D4037;
    }

    .btn-primary {
        background-color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-primary:hover {
        background-color: #F57C00;
        border-color: #F57C00;
    }

    .btn-outline-primary {
        color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-outline-primary:hover {
        background-color: #FF8F00;
        border-color: #FF8F00;
        color: white;
    }

    .badge.bg-primary {
        background-color: #FF8F00 !important;
    }

    .badge.bg-secondary {
        background-color: #757575 !important;
    }

    .badge.bg-success {
        background-color: #43A047 !important;
    }

    .badge.bg-warning {
        background-color: #FFB300 !important;
    }

    .badge.bg-info {
        background-color: #29B6F6 !important;
    }

    .analysis-card {
        border: 1px solid #E6D7B9;
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .analysis-card .card-header {
        background-color: #FFF8E1;
        border-bottom: 1px solid #E6D7B9;
        padding: 1rem;
    }

    .analysis-content {
        padding: 1.5rem;
    }

    .dimension-icon-large {
        font-size: 3rem;
        color: #FF8F00;
        margin-bottom: 1rem;
    }

    .progress {
        height: 0.5rem;
        background-color: #E6D7B9;
    }

    .progress-bar {
        background-color: #FF8F00;
    }

    .analysis-meta {
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #E6D7B9;
    }

    .analysis-meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .analysis-meta-item i {
        width: 24px;
        margin-right: 0.5rem;
        color: #FF8F00;
    }

    .analysis-step {
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        background-color: #FFFCF5;
        border: 1px solid #E6D7B9;
    }

    .analysis-step.active {
        background-color: #FFF8E1;
        border-color: #FFB300;
    }

    .analysis-step.completed {
        background-color: #F1F8E9;
        border-color: #43A047;
    }

    .analysis-step-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #FF8F00;
        color: white;
        font-weight: 600;
        margin-right: 0.5rem;
    }

    .analysis-step.completed .analysis-step-number {
        background-color: #43A047;
    }

    .analysis-step-title {
        font-weight: 600;
        color: #5D4037;
    }

    .analysis-step-description {
        margin-top: 0.5rem;
        color: #757575;
    }

    .analysis-step-status {
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }

    .analysis-step-status.waiting {
        color: #757575;
    }

    .analysis-step-status.in-progress {
        color: #FFB300;
    }

    .analysis-step-status.completed {
        color: #43A047;
    }

    .analysis-log {
        max-height: 300px;
        overflow-y: auto;
        padding: 1rem;
        background-color: #F5F5F5;
        border-radius: 0.5rem;
        font-family: monospace;
        font-size: 0.9rem;
        white-space: pre-wrap;
        margin-top: 1rem;
    }

    .log-entry {
        margin-bottom: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #E0E0E0;
    }

    .log-entry:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .log-time {
        color: #757575;
        margin-right: 0.5rem;
    }

    .log-info {
        color: #2196F3;
    }

    .log-success {
        color: #43A047;
    }

    .log-warning {
        color: #FFB300;
    }

    .log-error {
        color: #F44336;
    }

    .chapter-navigation {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 分析头部信息 -->
<div class="analysis-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">分析{{ dimension_info.name }}</h1>
            <p class="lead mb-2">
                <i class="fas fa-book me-2"></i>{{ novel.title }} - {{ chapter.title }}
                {% if novel.author %}
                <span class="ms-2"><i class="fas fa-user me-1"></i>{{ novel.author }}</span>
                {% endif %}
            </p>
            <p class="mb-0">
                <span class="badge bg-primary me-2">第{{ chapter.chapter_number }}章</span>
                <span class="badge bg-info me-2">{{ chapter.word_count }} 字</span>
                <span class="badge bg-success me-2">{{ chapter.analyzed_dimensions|default([])|length }}/15 维度已分析</span>
            </p>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回章节
                </a>
                <button id="cancelAnalysisBtn" class="btn btn-outline-danger">
                    <i class="fas fa-stop-circle me-1"></i>取消分析
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 章节导航 -->
<div class="chapter-navigation mb-4">
    {% if prev_chapter %}
    <a href="{{ url_for('v3_1.analyze_chapter_dimension', novel_id=novel.id, chapter_id=prev_chapter.id, dimension=dimension) }}" class="btn btn-outline-secondary">
        <i class="fas fa-chevron-left me-1"></i>上一章：{{ prev_chapter.title }}
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        <i class="fas fa-chevron-left me-1"></i>已是第一章
    </button>
    {% endif %}

    {% if next_chapter %}
    <a href="{{ url_for('v3_1.analyze_chapter_dimension', novel_id=novel.id, chapter_id=next_chapter.id, dimension=dimension) }}" class="btn btn-outline-secondary">
        下一章：{{ next_chapter.title }}<i class="fas fa-chevron-right ms-1"></i>
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        已是最后一章<i class="fas fa-chevron-right ms-1"></i>
    </button>
    {% endif %}
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-md-4 mb-4">
        <!-- 维度信息 -->
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>维度信息</h3>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <i class="{{ dimension_info.icon }} dimension-icon-large"></i>
                    <h3>{{ dimension_info.name }}</h3>
                </div>

                <div class="analysis-meta">
                    <div class="analysis-meta-item">
                        <i class="fas fa-book"></i>
                        <span>小说: {{ novel.title }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-bookmark"></i>
                        <span>章节: {{ chapter.title }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-chart-pie"></i>
                        <span>维度: {{ dimension_info.name }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-font"></i>
                        <span>字数: {{ chapter.word_count }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-clock"></i>
                        <span>预计时间: <span id="estimatedTime">计算中...</span></span>
                    </div>
                </div>

                <p class="card-text">{{ dimension_info.description }}</p>

                <div class="progress mb-2">
                    <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" id="analysisProgress"></div>
                </div>
                <div class="text-center mb-3">
                    <span id="progressText">准备中...</span>
                </div>

                <div class="d-grid gap-2 mt-3">
                    <a href="{{ url_for('v3_1.view_templates', novel_id=novel.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-cog me-1"></i>查看设定模板
                    </a>
                </div>
            </div>
        </div>

        <!-- 分析日志 -->
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-list-alt me-2"></i>分析日志</h3>
            </div>
            <div class="card-body">
                <div class="analysis-log" id="analysisLog">
                    <div class="log-entry">
                        <span class="log-time">{{ now.strftime('%H:%M:%S') }}</span>
                        <span class="log-info">开始分析 {{ dimension_info.name }}...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- 分析步骤 -->
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-tasks me-2"></i>分析步骤</h3>
            </div>
            <div class="card-body">
                <div class="analysis-step" id="step1">
                    <div class="d-flex align-items-center">
                        <span class="analysis-step-number">1</span>
                        <span class="analysis-step-title">准备分析</span>
                    </div>
                    <div class="analysis-step-description">
                        加载章节内容，准备分析环境，初始化分析模型。
                    </div>
                    <div class="analysis-step-status waiting" id="step1Status">
                        <i class="fas fa-hourglass-start me-1"></i>等待中...
                    </div>
                </div>

                <div class="analysis-step" id="step2">
                    <div class="d-flex align-items-center">
                        <span class="analysis-step-number">2</span>
                        <span class="analysis-step-title">内容分析</span>
                    </div>
                    <div class="analysis-step-description">
                        分析章节内容，提取关键信息，应用{{ dimension_info.name }}维度的分析模型。
                    </div>
                    <div class="analysis-step-status waiting" id="step2Status">
                        <i class="fas fa-hourglass-start me-1"></i>等待中...
                    </div>
                </div>

                <div class="analysis-step" id="step3">
                    <div class="d-flex align-items-center">
                        <span class="analysis-step-number">3</span>
                        <span class="analysis-step-title">生成分析结果</span>
                    </div>
                    <div class="analysis-step-description">
                        整合分析数据，生成详细的{{ dimension_info.name }}分析结果和推理过程。
                    </div>
                    <div class="analysis-step-status waiting" id="step3Status">
                        <i class="fas fa-hourglass-start me-1"></i>等待中...
                    </div>
                </div>

                <div class="analysis-step" id="step4">
                    <div class="d-flex align-items-center">
                        <span class="analysis-step-number">4</span>
                        <span class="analysis-step-title">保存结果</span>
                    </div>
                    <div class="analysis-step-description">
                        将分析结果保存到数据库，更新章节的分析状态。
                    </div>
                    <div class="analysis-step-status waiting" id="step4Status">
                        <i class="fas fa-hourglass-start me-1"></i>等待中...
                    </div>
                </div>
            </div>
        </div>

        <!-- 章节内容预览 -->
        <div class="card shadow-sm mt-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-file-alt me-2"></i>章节内容预览</h3>
            </div>
            <div class="card-body">
                <div style="max-height: 200px; overflow-y: auto; white-space: pre-wrap; font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; line-height: 1.8;">
                    {% if chapter.content %}
                        {{ chapter.content[:500] }}
                        {% if chapter.content|length > 500 %}
                        <div class="text-center mt-3">
                            <span class="badge bg-secondary">内容过长，仅显示前500字</span>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5>内容不可用</h5>
                            <p class="text-muted">该章节内容不可用或尚未加载。</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 分析结果预览 -->
        <div class="card shadow-sm mt-4" id="resultPreviewCard" style="display: none;">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i>分析结果预览</h3>
            </div>
            <div class="card-body">
                <div id="resultPreview">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-3">正在生成分析结果预览，请稍候...</p>
                    </div>
                </div>
                <div class="d-grid gap-2 mt-3">
                    <a href="#" class="btn btn-primary" id="viewResultBtn" style="display: none;">
                        <i class="fas fa-eye me-1"></i>查看完整分析结果
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 章节导航（底部） -->
<div class="chapter-navigation mt-4">
    {% if prev_chapter %}
    <a href="{{ url_for('v3_1.analyze_chapter_dimension', novel_id=novel.id, chapter_id=prev_chapter.id, dimension=dimension) }}" class="btn btn-outline-secondary">
        <i class="fas fa-chevron-left me-1"></i>上一章：{{ prev_chapter.title }}
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        <i class="fas fa-chevron-left me-1"></i>已是第一章
    </button>
    {% endif %}

    {% if next_chapter %}
    <a href="{{ url_for('v3_1.analyze_chapter_dimension', novel_id=novel.id, chapter_id=next_chapter.id, dimension=dimension) }}" class="btn btn-outline-secondary">
        下一章：{{ next_chapter.title }}<i class="fas fa-chevron-right ms-1"></i>
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        已是最后一章<i class="fas fa-chevron-right ms-1"></i>
    </button>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 分析状态变量
        let analysisInProgress = true;
        let currentStep = 0;
        let progress = 0;
        let analysisStartTime = new Date();
        let analysisId = null;

        // 估计分析时间（基于字数）
        const wordCount = {{ chapter.word_count }};
        let estimatedSeconds = Math.max(30, Math.min(300, wordCount / 50)); // 每50字约1秒，最少30秒，最多300秒
        updateEstimatedTime(estimatedSeconds);

        // 开始轮询分析状态
        startAnalysis();

        // 开始分析
        function startAnalysis() {
            // 发送开始分析请求
            $.ajax({
                url: '/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analyze_dimension',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    dimension: '{{ dimension }}',
                    force: {{ force|default(0) }}
                }),
                success: function(response) {
                    if (response.success) {
                        analysisId = response.analysis_id;
                        addLogEntry('分析任务已创建，ID: ' + analysisId, 'info');

                        // 开始轮询分析状态
                        updateAnalysisStatus();
                        // 设置定时器定期更新状态
                        const statusInterval = setInterval(function() {
                            if (!analysisInProgress) {
                                clearInterval(statusInterval);
                                return;
                            }
                            updateAnalysisStatus();
                        }, 5000); // 每5秒更新一次
                    } else {
                        analysisInProgress = false;
                        addLogEntry('创建分析任务失败: ' + response.message, 'error');
                        alert('创建分析任务失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    analysisInProgress = false;
                    addLogEntry('创建分析任务失败: ' + error, 'error');
                    alert('创建分析任务失败，请稍后重试');
                }
            });
        }

        // 更新分析状态
        function updateAnalysisStatus() {
            if (!analysisId) return;

            $.ajax({
                url: '/v3.1/api/analysis/' + analysisId + '/status',
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        const status = response.status;
                        progress = response.progress || 0;

                        // 更新进度条
                        updateProgressBar(progress);

                        // 更新步骤状态
                        updateStepStatus(status);

                        // 添加日志条目
                        if (response.log_entries && response.log_entries.length > 0) {
                            response.log_entries.forEach(entry => {
                                addLogEntry(entry.message, entry.level);
                            });
                        }

                        // 检查是否完成
                        if (status === 'completed') {
                            analysisInProgress = false;
                            analysisCompleted(response.result_url);
                        } else if (status === 'failed') {
                            analysisInProgress = false;
                            analysisFailed(response.error);
                        }
                    } else {
                        addLogEntry('获取分析状态失败: ' + response.message, 'warning');
                    }
                },
                error: function(xhr, status, error) {
                    addLogEntry('获取分析状态失败: ' + error, 'warning');
                }
            });
        }

        // 更新进度条
        function updateProgressBar(percent) {
            $('#analysisProgress').css('width', percent + '%').attr('aria-valuenow', percent);
            $('#progressText').text('进度: ' + percent + '%');
        }

        // 更新步骤状态
        function updateStepStatus(status) {
            let stepNumber = 0;

            if (status === 'initializing') {
                stepNumber = 1;
                updateStep(1, 'active', '<i class="fas fa-spinner fa-spin me-1"></i>初始化中...');
            } else if (status === 'analyzing') {
                stepNumber = 2;
                updateStep(1, 'completed', '<i class="fas fa-check me-1"></i>初始化完成');
                updateStep(2, 'active', '<i class="fas fa-spinner fa-spin me-1"></i>分析中...');
            } else if (status === 'generating_result') {
                stepNumber = 3;
                updateStep(1, 'completed', '<i class="fas fa-check me-1"></i>初始化完成');
                updateStep(2, 'completed', '<i class="fas fa-check me-1"></i>分析完成');
                updateStep(3, 'active', '<i class="fas fa-spinner fa-spin me-1"></i>生成结果中...');
            } else if (status === 'saving') {
                stepNumber = 4;
                updateStep(1, 'completed', '<i class="fas fa-check me-1"></i>初始化完成');
                updateStep(2, 'completed', '<i class="fas fa-check me-1"></i>分析完成');
                updateStep(3, 'completed', '<i class="fas fa-check me-1"></i>结果生成完成');
                updateStep(4, 'active', '<i class="fas fa-spinner fa-spin me-1"></i>保存中...');
            } else if (status === 'completed') {
                stepNumber = 4;
                updateStep(1, 'completed', '<i class="fas fa-check me-1"></i>初始化完成');
                updateStep(2, 'completed', '<i class="fas fa-check me-1"></i>分析完成');
                updateStep(3, 'completed', '<i class="fas fa-check me-1"></i>结果生成完成');
                updateStep(4, 'completed', '<i class="fas fa-check me-1"></i>保存完成');
            }

            if (stepNumber > currentStep) {
                currentStep = stepNumber;
            }
        }

        // 更新单个步骤状态
        function updateStep(step, status, statusText) {
            const $step = $('#step' + step);
            const $status = $('#step' + step + 'Status');

            $step.removeClass('active completed').addClass(status);
            $status.removeClass('waiting in-progress completed').addClass(status === 'active' ? 'in-progress' : status);
            $status.html(statusText);
        }

        // 添加日志条目
        function addLogEntry(message, level) {
            const now = new Date();
            const timeString = now.toTimeString().split(' ')[0];
            const $log = $('#analysisLog');

            const $entry = $('<div class="log-entry"></div>');
            $entry.append('<span class="log-time">' + timeString + '</span>');
            $entry.append('<span class="log-' + level + '">' + message + '</span>');

            $log.append($entry);
            $log.scrollTop($log[0].scrollHeight);
        }

        // 更新估计时间
        function updateEstimatedTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            $('#estimatedTime').text(minutes + '分' + remainingSeconds + '秒');
        }

        // 分析完成
        function analysisCompleted(resultUrl) {
            // 计算实际用时
            const endTime = new Date();
            const elapsedSeconds = (endTime - analysisStartTime) / 1000;
            const minutes = Math.floor(elapsedSeconds / 60);
            const seconds = Math.floor(elapsedSeconds % 60);

            addLogEntry('分析完成！用时: ' + minutes + '分' + seconds + '秒', 'success');

            // 更新进度条为100%
            updateProgressBar(100);
            $('#progressText').text('分析完成！');

            // 显示结果预览
            $('#resultPreviewCard').show();
            $('#resultPreview').html('<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>{{ dimension_info.name }}分析已完成！</div>');

            // 设置查看结果按钮
            $('#viewResultBtn').attr('href', resultUrl).show();

            // 禁用取消按钮
            $('#cancelAnalysisBtn').prop('disabled', true);
        }

        // 分析失败
        function analysisFailed(error) {
            addLogEntry('分析失败: ' + error, 'error');

            // 更新进度条
            $('#analysisProgress').addClass('bg-danger');
            $('#progressText').text('分析失败');

            // 显示错误信息
            $('#resultPreviewCard').show();
            $('#resultPreview').html('<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>分析失败: ' + error + '</div>');

            // 禁用取消按钮
            $('#cancelAnalysisBtn').prop('disabled', true);
        }

        // 取消分析
        $('#cancelAnalysisBtn').click(function() {
            if (!analysisId) return;

            if (confirm('确定要取消分析吗？已完成的部分将丢失。')) {
                $.ajax({
                    url: '/v3.1/api/analysis/' + analysisId + '/cancel',
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            analysisInProgress = false;
                            addLogEntry('分析已取消', 'warning');

                            // 更新UI
                            $('#progressText').text('分析已取消');
                            $('#analysisProgress').addClass('bg-warning');

                            // 禁用取消按钮
                            $('#cancelAnalysisBtn').prop('disabled', true);

                            // 显示返回按钮
                            $('#resultPreviewCard').show();
                            $('#resultPreview').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>分析已取消</div>');
                            $('#viewResultBtn').text('返回章节页面').attr('href', "{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}").show();
                        } else {
                            addLogEntry('取消分析失败: ' + response.message, 'error');
                            alert('取消分析失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        addLogEntry('取消分析失败: ' + error, 'error');
                        alert('取消分析失败，请稍后重试');
                    }
                });
            }
        });

        // 键盘导航
        $(document).keydown(function(e) {
            // 左箭头键 - 上一章
            if (e.keyCode === 37) {
                const prevChapterLink = $('a:contains("上一章")').first();
                if (prevChapterLink.length && !prevChapterLink.hasClass('disabled')) {
                    window.location.href = prevChapterLink.attr('href');
                }
            }
            // 右箭头键 - 下一章
            else if (e.keyCode === 39) {
                const nextChapterLink = $('a:contains("下一章")').first();
                if (nextChapterLink.length && !nextChapterLink.hasClass('disabled')) {
                    window.location.href = nextChapterLink.attr('href');
                }
            }
        });
    });
</script>
{% endblock %}