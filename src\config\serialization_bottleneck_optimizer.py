"""
九猫系统串行化瓶颈优化器
在现有降本增效框架基础上，专门解决串行化瓶颈问题
完全兼容现有的精简版和默认版优化策略
"""

import logging
import time
import threading
import queue
import asyncio
from typing import Dict, List, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil

# 导入新增的优化器
from src.config.result_cache_optimizer import ResultCacheOptimizer
from src.config.cross_chapter_reasoning_optimizer import CrossChapterReasoningOptimizer
from src.config.paragraph_reasoning_reuse import ParagraphReasoningReuse

logger = logging.getLogger(__name__)

class SerializationBottleneckOptimizer:
    """完全兼容现有降本增效框架的串行化瓶颈优化器"""

    def __init__(self, prompt_template: str = "default"):
        """
        初始化优化器

        Args:
            prompt_template: 提示词模板类型，继承现有优化框架
        """
        # 继承您现有的优化配置
        from src.config.parallel_optimization_config import ParallelOptimizationConfig
        self.base_config = ParallelOptimizationConfig.get_optimization_config(prompt_template)
        self.prompt_template = prompt_template
        self._async_pools = {}
        self._batch_processors = {}
        self._connection_managers = {}

        # 新增：结果缓存和推理复用优化器
        self.result_cache_optimizer = None
        self.cross_chapter_optimizer = None
        self.paragraph_reasoning_reuse = None

        # 按需初始化优化器
        if self.base_config.get("enable_result_cache", True):
            self.result_cache_optimizer = ResultCacheOptimizer(prompt_template)

        if self.base_config.get("enable_cross_chapter_reasoning", True):
            self.cross_chapter_optimizer = CrossChapterReasoningOptimizer(prompt_template)

        if self.base_config.get("enable_paragraph_reasoning", True):
            self.paragraph_reasoning_reuse = ParagraphReasoningReuse(prompt_template)

        logger.info(f"[串行化瓶颈优化器] 初始化完成，启用智能结果缓存与推理复用优化")

    def _load_base_config(self) -> Dict[str, Any]:
        """加载基础配置，继承现有优化框架"""
        try:
            base_config = self.base_config

            # 在现有配置基础上添加串行化瓶颈优化配置
            serialization_config = {
                # 数据库操作优化
                "db_batch_size": 10 if self.prompt_template == "simplified" else 20,
                "db_batch_timeout": 5.0,
                "db_async_enabled": True,
                "db_connection_reuse": True,

                # API调用优化
                "api_burst_mode": True,
                "api_adaptive_delay": True,
                "api_pipeline_enabled": True,
                "api_connection_pooling": True,

                # 内存缓存优化
                "memory_cache_enabled": True,
                "memory_cache_size_mb": 256 if self.prompt_template == "simplified" else 512,
                "cache_ttl_seconds": 3600,

                # 异步处理优化
                "async_result_processing": True,
                "async_db_writes": True,
                "async_progress_updates": True,

                # 资源调度优化
                "dynamic_worker_scaling": True,
                "resource_aware_scheduling": True,
                "load_balancing_enabled": True
            }

            # 合并配置
            base_config.update(serialization_config)

            logger.info(f"[串行化优化] 已加载{self.prompt_template}版本的优化配置")
            return base_config

        except Exception as e:
            logger.error(f"[串行化优化] 加载配置失败: {str(e)}")
            return {}

    def optimize_database_operations(self, task_id: str) -> Dict[str, Any]:
        """
        优化数据库操作，解决数据库写入串行化瓶颈

        Args:
            task_id: 任务ID

        Returns:
            数据库优化配置
        """
        try:
            db_config = {
                "batch_processor": None,
                "async_writer": None,
                "connection_manager": None
            }

            # 1. 批量写入处理器
            batch_processor = DatabaseBatchProcessor(
                batch_size=self.base_config.get("db_batch_size", 10),
                batch_timeout=self.base_config.get("db_batch_timeout", 5.0),
                prompt_template=self.prompt_template
            )

            # 2. 异步数据库写入器
            if self.base_config.get("db_async_enabled", True):
                async_writer = AsyncDatabaseWriter(
                    max_workers=6 if self.prompt_template == "simplified" else 10,  # 温和提升，精简版控制成本
                    queue_size=40 if self.prompt_template == "simplified" else 80   # 减小队列大小以控制内存
                )
                db_config["async_writer"] = async_writer

            # 3. 连接管理器
            connection_manager = DatabaseConnectionManager(
                pool_size=self.base_config.get("connection_pool_size", 15),
                reuse_connections=self.base_config.get("db_connection_reuse", True)
            )

            db_config.update({
                "batch_processor": batch_processor,
                "connection_manager": connection_manager
            })

            # 存储到实例中
            self._batch_processors[task_id] = batch_processor
            self._connection_managers[task_id] = connection_manager

            logger.info(f"[数据库优化] 任务{task_id}的数据库操作优化已配置")
            return db_config

        except Exception as e:
            logger.error(f"[数据库优化] 优化数据库操作失败: {str(e)}")
            return {}

    def optimize_api_calls(self, task_id: str) -> Dict[str, Any]:
        """
        优化API调用，解决API调用延迟导致的串行化

        Args:
            task_id: 任务ID

        Returns:
            API优化配置
        """
        try:
            api_config = {
                "pipeline_processor": None,
                "adaptive_delay_manager": None,
                "burst_controller": None
            }

            # 1. API管道处理器 - 减少延迟等待
            if self.base_config.get("api_pipeline_enabled", True):
                pipeline_processor = APIPipelineProcessor(
                    max_concurrent=self.base_config.get("instance_concurrency", 8),
                    adaptive_delay=self.base_config.get("api_adaptive_delay", True)
                )
                api_config["pipeline_processor"] = pipeline_processor

            # 2. 自适应延迟管理器
            if self.base_config.get("api_adaptive_delay", True):
                delay_manager = AdaptiveDelayManager(
                    base_delay=self.base_config.get("api_delay", 0.3),
                    max_delay=2.0,
                    min_delay=0.1
                )
                api_config["adaptive_delay_manager"] = delay_manager

            # 3. 突发控制器 - 在允许范围内提高并发
            if self.base_config.get("api_burst_mode", True):
                burst_controller = APIBurstController(
                    burst_size=3 if self.prompt_template == "simplified" else 5,
                    burst_interval=10.0
                )
                api_config["burst_controller"] = burst_controller

            logger.info(f"[API优化] 任务{task_id}的API调用优化已配置")
            return api_config

        except Exception as e:
            logger.error(f"[API优化] 优化API调用失败: {str(e)}")
            return {}

    def optimize_resource_scheduling(self, task_id: str) -> Dict[str, Any]:
        """
        优化资源调度，动态调整并发度避免资源竞争

        Args:
            task_id: 任务ID

        Returns:
            资源调度优化配置
        """
        try:
            scheduling_config = {}

            # 1. 动态工作线程调度器
            if self.base_config.get("dynamic_worker_scaling", True):
                worker_scheduler = DynamicWorkerScheduler(
                    min_workers=3 if self.prompt_template == "simplified" else 5,
                    max_workers=8 if self.prompt_template == "simplified" else 12,
                    scale_factor=0.8
                )
                scheduling_config["worker_scheduler"] = worker_scheduler

            # 2. 资源感知调度器
            if self.base_config.get("resource_aware_scheduling", True):
                resource_scheduler = ResourceAwareScheduler(
                    cpu_threshold=70 if self.prompt_template == "simplified" else 85,
                    memory_threshold=80,
                    prompt_template=self.prompt_template
                )
                scheduling_config["resource_scheduler"] = resource_scheduler

            # 3. 负载均衡器
            if self.base_config.get("load_balancing_enabled", True):
                load_balancer = LoadBalancer(
                    strategy="round_robin" if self.prompt_template == "simplified" else "least_connections"
                )
                scheduling_config["load_balancer"] = load_balancer

            logger.info(f"[资源调度] 任务{task_id}的资源调度优化已配置")
            return scheduling_config

        except Exception as e:
            logger.error(f"[资源调度] 优化资源调度失败: {str(e)}")
            return {}

    def create_optimized_executor(self, task_id: str, max_workers: int) -> ThreadPoolExecutor:
        """
        创建优化的线程池执行器，解决线程池瓶颈

        Args:
            task_id: 任务ID
            max_workers: 最大工作线程数

        Returns:
            优化的线程池执行器
        """
        try:
            # 根据系统资源动态调整工作线程数
            cpu_count = psutil.cpu_count(logical=True)
            memory_percent = psutil.virtual_memory().percent

            # 资源感知的工作线程数调整
            if memory_percent > 85:
                adjusted_workers = max(3, max_workers // 2)
                logger.warning(f"[线程池优化] 内存使用率过高({memory_percent}%)，降低工作线程数至{adjusted_workers}")
            elif memory_percent < 50 and cpu_count >= 8:
                adjusted_workers = min(max_workers + 2, cpu_count * 2)
                logger.info(f"[线程池优化] 系统资源充足，提升工作线程数至{adjusted_workers}")
            else:
                adjusted_workers = max_workers

            # 创建优化的线程池
            executor = ThreadPoolExecutor(
                max_workers=adjusted_workers,
                thread_name_prefix=f"OptimizedPool-{task_id}"
            )

            logger.info(f"[线程池优化] 为任务{task_id}创建优化线程池，工作线程数: {adjusted_workers}")
            return executor

        except Exception as e:
            logger.error(f"[线程池优化] 创建优化线程池失败: {str(e)}")
            return ThreadPoolExecutor(max_workers=max_workers)

    def apply_all_optimizations(self, task_id: str) -> Dict[str, Any]:
        """
        应用所有串行化瓶颈优化

        Args:
            task_id: 任务ID

        Returns:
            完整的优化配置
        """
        try:
            optimization_results = {
                "task_id": task_id,
                "prompt_template": self.prompt_template,
                "optimizations_applied": [],
                "config": {}
            }

            # 1. 数据库操作优化
            db_config = self.optimize_database_operations(task_id)
            if db_config:
                optimization_results["config"]["database"] = db_config
                optimization_results["optimizations_applied"].append("database_operations")

            # 2. API调用优化
            api_config = self.optimize_api_calls(task_id)
            if api_config:
                optimization_results["config"]["api"] = api_config
                optimization_results["optimizations_applied"].append("api_calls")

            # 3. 资源调度优化
            scheduling_config = self.optimize_resource_scheduling(task_id)
            if scheduling_config:
                optimization_results["config"]["scheduling"] = scheduling_config
                optimization_results["optimizations_applied"].append("resource_scheduling")

            # 4. 记录优化状态
            optimization_results["status"] = "completed"
            optimization_results["applied_at"] = time.time()

            logger.info(f"[串行化优化] 任务{task_id}的所有优化已应用: {optimization_results['optimizations_applied']}")
            return optimization_results

        except Exception as e:
            logger.error(f"[串行化优化] 应用优化失败: {str(e)}")
            return {"status": "failed", "error": str(e)}

    def optimize_analysis_request(self, analysis_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        优化分析请求

        Args:
            analysis_request: 原始分析请求

        Returns:
            优化后的分析请求
        """
        # ... existing code for serialization bottleneck optimization ...

        # 应用结果缓存优化
        if self.result_cache_optimizer:
            analysis_request = self.result_cache_optimizer.optimize(analysis_request)

        # 应用段落推理复用优化
        if self.paragraph_reasoning_reuse and "text" in analysis_request:
            paragraph_results = self.paragraph_reasoning_reuse.process_text(
                analysis_request["text"],
                analysis_request.get("dimension", "")
            )
            if paragraph_results.get("matched_paragraphs", 0) > 0:
                analysis_request["paragraph_reasoning"] = paragraph_results.get("reusable_reasoning", [])

        # 应用跨章节推理优化
        if (self.cross_chapter_optimizer and
            "chapter_id" in analysis_request and
            "previous_analyses" in analysis_request):
            analysis_request = self.cross_chapter_optimizer.optimize(
                analysis_request,
                analysis_request.get("previous_analyses", []),
                analysis_request.get("dimension", "")
            )

        return analysis_request

    def save_analysis_result(self, result: Dict[str, Any], analysis_request: Dict[str, Any]) -> None:
        """
        保存分析结果到缓存

        Args:
            result: 分析结果
            analysis_request: 分析请求
        """
        # 保存到结果缓存
        if self.result_cache_optimizer and "text" in analysis_request:
            self.result_cache_optimizer.save_to_cache(
                analysis_request["text"],
                analysis_request.get("dimension", ""),
                result
            )

        # 保存段落级推理结果
        if self.paragraph_reasoning_reuse and "text" in analysis_request:
            # 简化实现：实际项目中应该有段落拆分和推理映射逻辑
            # 这里仅作示例
            text = analysis_request["text"]
            reasoning = result.get("reasoning", "")
            dimension = analysis_request.get("dimension", "")

            # 简单分段并保存
            paragraphs = text.split("\n\n")
            for paragraph in paragraphs:
                if len(paragraph) >= 100:  # 只保存有意义的段落
                    # 在实际项目中，应该精确提取每个段落对应的推理
                    # 简化实现，将整体推理与每个段落关联
                    self.paragraph_reasoning_reuse.save_to_cache(
                        paragraph, reasoning, dimension, "analysis"
                    )

        # ... existing code ...

    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        stats = {
            "prompt_template": self.prompt_template,
            "serial_bottleneck_optimized": True
        }

        # 添加缓存统计
        if self.result_cache_optimizer:
            stats["result_cache"] = self.result_cache_optimizer.get_stats()

        if self.paragraph_reasoning_reuse:
            stats["paragraph_reasoning"] = self.paragraph_reasoning_reuse.get_stats()

        # ... existing code ...

        return stats


class DatabaseBatchProcessor:
    """数据库批量处理器"""

    def __init__(self, batch_size: int, batch_timeout: float, prompt_template: str):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.prompt_template = prompt_template
        self.pending_operations = []
        self.last_batch_time = time.time()
        self.lock = threading.Lock()

    def add_operation(self, operation: Dict[str, Any]):
        """添加数据库操作到批处理队列"""
        with self.lock:
            self.pending_operations.append(operation)

            # 检查是否需要立即处理
            if (len(self.pending_operations) >= self.batch_size or
                time.time() - self.last_batch_time >= self.batch_timeout):
                self._process_batch()

    def _process_batch(self):
        """处理当前批次"""
        if not self.pending_operations:
            return

        batch = self.pending_operations.copy()
        self.pending_operations.clear()
        self.last_batch_time = time.time()

        logger.info(f"[批量处理] 处理{len(batch)}个数据库操作")
        # 这里会调用实际的批量数据库操作


class AsyncDatabaseWriter:
    """异步数据库写入器"""

    def __init__(self, max_workers: int, queue_size: int):
        self.max_workers = max_workers
        self.queue_size = queue_size
        self.write_queue = queue.Queue(maxsize=queue_size)
        self.workers = []
        self._start_workers()

    def _start_workers(self):
        """启动异步写入工作线程"""
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"AsyncDBWriter-{i+1}"
            )
            worker.daemon = True
            worker.start()
            self.workers.append(worker)

    def _worker_loop(self):
        """工作线程循环"""
        while True:
            try:
                operation = self.write_queue.get(timeout=5)
                if operation is None:
                    break
                # 执行异步写入操作
                self._execute_write(operation)
                self.write_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"[异步写入] 执行写入操作失败: {str(e)}")

    def _execute_write(self, operation: Dict[str, Any]):
        """执行写入操作"""
        # 实际的数据库写入逻辑
        pass


class APIPipelineProcessor:
    """API管道处理器"""

    def __init__(self, max_concurrent: int, adaptive_delay: bool):
        self.max_concurrent = max_concurrent
        self.adaptive_delay = adaptive_delay
        self.active_requests = 0
        self.lock = threading.Lock()

    def process_requests(self, requests: List[Dict[str, Any]]) -> List[Any]:
        """管道化处理API请求"""
        results = []

        with ThreadPoolExecutor(max_workers=self.max_concurrent) as executor:
            future_to_request = {
                executor.submit(self._execute_request, req): req
                for req in requests
            }

            for future in as_completed(future_to_request):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"[API管道] 请求执行失败: {str(e)}")

        return results

    def _execute_request(self, request: Dict[str, Any]) -> Any:
        """执行单个API请求"""
        # 实际的API调用逻辑
        pass


class AdaptiveDelayManager:
    """自适应延迟管理器"""

    def __init__(self, base_delay: float, max_delay: float, min_delay: float):
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.min_delay = min_delay
        self.current_delay = base_delay
        self.success_count = 0
        self.error_count = 0

    def get_delay(self) -> float:
        """获取当前延迟时间"""
        return self.current_delay

    def record_success(self):
        """记录成功请求"""
        self.success_count += 1
        if self.success_count >= 5:  # 连续5次成功，减少延迟
            self.current_delay = max(self.min_delay, self.current_delay * 0.9)
            self.success_count = 0

    def record_error(self):
        """记录错误请求"""
        self.error_count += 1
        if self.error_count >= 2:  # 连续2次错误，增加延迟
            self.current_delay = min(self.max_delay, self.current_delay * 1.5)
            self.error_count = 0


class APIBurstController:
    """API突发控制器"""

    def __init__(self, burst_size: int, burst_interval: float):
        self.burst_size = burst_size
        self.burst_interval = burst_interval
        self.last_burst_time = 0
        self.current_burst_count = 0

    def can_burst(self) -> bool:
        """检查是否可以进行突发请求"""
        current_time = time.time()

        if current_time - self.last_burst_time >= self.burst_interval:
            self.last_burst_time = current_time
            self.current_burst_count = 0
            return True

        return self.current_burst_count < self.burst_size

    def record_burst(self):
        """记录突发请求"""
        self.current_burst_count += 1


class DynamicWorkerScheduler:
    """动态工作线程调度器"""

    def __init__(self, min_workers: int, max_workers: int, scale_factor: float):
        self.min_workers = min_workers
        self.max_workers = max_workers
        self.scale_factor = scale_factor
        self.current_workers = min_workers

    def get_optimal_workers(self, queue_size: int, cpu_usage: float) -> int:
        """获取最优工作线程数"""
        if queue_size > self.current_workers * 2 and cpu_usage < 70:
            # 队列积压且CPU使用率不高，增加工作线程
            self.current_workers = min(self.max_workers,
                                     int(self.current_workers * (1 + self.scale_factor)))
        elif queue_size < self.current_workers // 2 or cpu_usage > 85:
            # 队列较空或CPU使用率过高，减少工作线程
            self.current_workers = max(self.min_workers,
                                     int(self.current_workers * (1 - self.scale_factor)))

        return self.current_workers


class ResourceAwareScheduler:
    """资源感知调度器"""

    def __init__(self, cpu_threshold: float, memory_threshold: float, prompt_template: str):
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        self.prompt_template = prompt_template

    def should_throttle(self) -> bool:
        """检查是否应该限流"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent

            return (cpu_percent > self.cpu_threshold or
                   memory_percent > self.memory_threshold)
        except Exception:
            return False

    def get_throttle_factor(self) -> float:
        """获取限流因子"""
        if self.should_throttle():
            return 0.5 if self.prompt_template == "simplified" else 0.7
        return 1.0


class LoadBalancer:
    """负载均衡器"""

    def __init__(self, strategy: str):
        self.strategy = strategy
        self.worker_loads = {}
        self.current_worker = 0

    def select_worker(self, workers: List[str]) -> str:
        """选择工作线程"""
        if self.strategy == "round_robin":
            worker = workers[self.current_worker % len(workers)]
            self.current_worker += 1
            return worker
        elif self.strategy == "least_connections":
            return min(workers, key=lambda w: self.worker_loads.get(w, 0))
        else:
            return workers[0]

    def update_load(self, worker: str, load: int):
        """更新工作线程负载"""
        self.worker_loads[worker] = load


class DatabaseConnectionManager:
    """数据库连接管理器"""

    def __init__(self, pool_size: int, reuse_connections: bool):
        self.pool_size = pool_size
        self.reuse_connections = reuse_connections
        self.connection_pool = queue.Queue(maxsize=pool_size)
        self.active_connections = {}
        self.lock = threading.Lock()

    def get_connection(self):
        """获取数据库连接"""
        try:
            if self.reuse_connections and not self.connection_pool.empty():
                return self.connection_pool.get_nowait()
            else:
                # 创建新连接的逻辑
                return self._create_connection()
        except queue.Empty:
            return self._create_connection()

    def return_connection(self, connection):
        """归还数据库连接"""
        if self.reuse_connections:
            try:
                self.connection_pool.put_nowait(connection)
            except queue.Full:
                # 连接池已满，关闭连接
                self._close_connection(connection)
        else:
            self._close_connection(connection)

    def _create_connection(self):
        """创建数据库连接"""
        # 实际的连接创建逻辑
        pass

    def _close_connection(self, connection):
        """关闭数据库连接"""
        # 实际的连接关闭逻辑
        pass
