/**
 * 九猫系统 - 应急资源加载器
 * 版本: 1.0.0
 *
 * 该脚本用于处理静态资源404错误，提供内联备用资源
 * 处理bootstrap.min.css、jquery.min.js等资源加载失败的情况
 */

(function() {
    console.log('[九猫修复] 应急资源加载器已加载');

    // 检查是否已应用此修复
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded && window.__nineCatsFixes.loaded['emergency-resource-loader']) {
        console.log('[九猫修复] 应急资源加载器已经应用，跳过');
        return;
    }

    // 资源状态跟踪
    const resourceState = {
        loaded: {},            // 已成功加载的资源
        failed: {},            // 加载失败的资源
        retries: {},           // 重试次数
        maxRetries: 3,         // 最大重试次数
        monitored: new Set(),  // 正在监控的资源URL
        pendingFallbacks: []   // 待处理的备用加载
    };

    // 资源列表，按优先级排序（本地资源优先）
    const cdnUrls = {
        jquery: [
            '/static/js/lib/jquery.min.js',  // 本地资源优先
            'https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js',
            'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js',
            'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js',
            'https://fastly.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js'
        ],
        bootstrap: [
            '/static/js/lib/bootstrap.bundle.min.js',  // 本地资源优先
            'https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.1/js/bootstrap.bundle.min.js',
            'https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js',
            'https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.1/js/bootstrap.bundle.min.js',
            'https://fastly.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js'
        ],
        bootstrapCss: [
            '/static/css/bootstrap.min.css',  // 本地资源优先
            'https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.1/css/bootstrap.min.css',
            'https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css',
            'https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.1/css/bootstrap.min.css',
            'https://fastly.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css'
        ],
        chartjs: [
            '/static/js/lib/chart.min.js',  // 本地资源优先
            'https://cdn.bootcdn.net/ajax/libs/Chart.js/3.7.1/chart.min.js',
            'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js',
            'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js',
            'https://fastly.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js'
        ],
        fontawesome: [
            '/static/css/fontawesome.min.css',  // 本地资源优先
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css'
        ]
    };

    // 内联备用资源 - 最小版本，用于应急
    const inlineResources = {
        jquery: function() {
            console.log('[九猫修复] 加载内联jQuery最小版本');
            // 最小版本的jQuery，只包含基本选择器和DOM操作
            window.jQuery = window.$ = function(selector) {
                if (typeof selector === 'function') {
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', selector);
                    } else {
                        selector();
                    }
                    return;
                }

                const elements = typeof selector === 'string'
                    ? document.querySelectorAll(selector)
                    : [selector];

                const jQueryObject = {
                    elements: Array.from(elements),
                    length: elements.length,

                    // 基本选择器方法
                    find: function(subSelector) {
                        const results = [];
                        this.elements.forEach(el => {
                            const found = el.querySelectorAll(subSelector);
                            results.push(...found);
                        });
                        return jQuery(results);
                    },

                    // 基本DOM操作
                    html: function(content) {
                        if (content === undefined) {
                            return this.elements[0] ? this.elements[0].innerHTML : '';
                        }
                        this.elements.forEach(el => el.innerHTML = content);
                        return this;
                    },
                    text: function(content) {
                        if (content === undefined) {
                            return this.elements[0] ? this.elements[0].textContent : '';
                        }
                        this.elements.forEach(el => el.textContent = content);
                        return this;
                    },
                    val: function(value) {
                        if (value === undefined) {
                            return this.elements[0] ? this.elements[0].value : '';
                        }
                        this.elements.forEach(el => el.value = value);
                        return this;
                    },

                    // 基本事件处理
                    on: function(event, handler) {
                        this.elements.forEach(el => el.addEventListener(event, handler));
                        return this;
                    },
                    off: function(event, handler) {
                        this.elements.forEach(el => el.removeEventListener(event, handler));
                        return this;
                    },

                    // 实用方法
                    each: function(callback) {
                        this.elements.forEach((el, i) => callback.call(el, i, el));
                        return this;
                    },

                    // CSS操作
                    css: function(prop, value) {
                        if (typeof prop === 'object') {
                            this.elements.forEach(el => {
                                for (const key in prop) {
                                    el.style[key] = prop[key];
                                }
                            });
                        } else {
                            if (value === undefined) {
                                return this.elements[0] ? getComputedStyle(this.elements[0])[prop] : '';
                            }
                            this.elements.forEach(el => el.style[prop] = value);
                        }
                        return this;
                    },

                    // 类操作
                    addClass: function(className) {
                        this.elements.forEach(el => el.classList.add(className));
                        return this;
                    },
                    removeClass: function(className) {
                        this.elements.forEach(el => el.classList.remove(className));
                        return this;
                    },
                    toggleClass: function(className) {
                        this.elements.forEach(el => el.classList.toggle(className));
                        return this;
                    },

                    // Ajax简化版
                    ajax: function(options) {
                        const xhr = new XMLHttpRequest();
                        xhr.open(options.type || 'GET', options.url, true);

                        if (options.contentType) {
                            xhr.setRequestHeader('Content-Type', options.contentType);
                        }

                        xhr.onload = function() {
                            if (xhr.status >= 200 && xhr.status < 300) {
                                if (options.success) options.success(xhr.responseText);
                            } else {
                                if (options.error) options.error(xhr);
                            }
                        };

                        xhr.onerror = function() {
                            if (options.error) options.error(xhr);
                        };

                        xhr.send(options.data || null);
                        return this;
                    }
                };

                // 添加索引访问器
                jQueryObject.elements.forEach((el, i) => {
                    jQueryObject[i] = el;
                });

                return jQueryObject;
            };

            // 添加基本工具方法
            jQuery.extend = jQuery.fn = {};
        },

        bootstrapBasicCss: function() {
            console.log('[九猫修复] 加载内联Bootstrap基本CSS');
            const style = document.createElement('style');
            style.textContent = `
                /* 基本Bootstrap样式 - 最小版本 */
                .container { width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto; }
                @media (min-width: 576px) { .container { max-width: 540px; } }
                @media (min-width: 768px) { .container { max-width: 720px; } }
                @media (min-width: 992px) { .container { max-width: 960px; } }
                @media (min-width: 1200px) { .container { max-width: 1140px; } }
                .row { display: flex; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 { position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                .col { flex-basis: 0; flex-grow: 1; max-width: 100%; }
                .col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
                .col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
                .col-3 { flex: 0 0 25%; max-width: 25%; }
                .col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
                .col-6 { flex: 0 0 50%; max-width: 50%; }
                .col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
                .col-12 { flex: 0 0 100%; max-width: 100%; }

                /* 按钮 */
                .btn { display: inline-block; font-weight: 400; text-align: center; vertical-align: middle; user-select: none; border: 1px solid transparent; padding: .375rem .75rem; font-size: 1rem; line-height: 1.5; border-radius: .25rem; transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out; }
                .btn-primary { color: #fff; background-color: #007bff; border-color: #007bff; }
                .btn-secondary { color: #fff; background-color: #6c757d; border-color: #6c757d; }
                .btn-success { color: #fff; background-color: #28a745; border-color: #28a745; }
                .btn-danger { color: #fff; background-color: #dc3545; border-color: #dc3545; }

                /* 卡片 */
                .card { position: relative; display: flex; flex-direction: column; min-width: 0; word-wrap: break-word; background-color: #fff; background-clip: border-box; border: 1px solid rgba(0,0,0,.125); border-radius: .25rem; }
                .card-body { flex: 1 1 auto; min-height: 1px; padding: 1.25rem; }
                .card-title { margin-bottom: .75rem; }
                .card-text:last-child { margin-bottom: 0; }

                /* 其他常用样式 */
                .text-center { text-align: center !important; }
                .mt-2 { margin-top: .5rem !important; }
                .mt-3 { margin-top: 1rem !important; }
                .mt-4 { margin-top: 1.5rem !important; }
                .mt-5 { margin-top: 3rem !important; }
                .mb-2 { margin-bottom: .5rem !important; }
                .mb-3 { margin-bottom: 1rem !important; }
                .mb-4 { margin-bottom: 1.5rem !important; }
                .mb-5 { margin-bottom: 3rem !important; }
                .p-2 { padding: .5rem !important; }
                .p-3 { padding: 1rem !important; }
                .p-4 { padding: 1.5rem !important; }
                .p-5 { padding: 3rem !important; }
            `;
            document.head.appendChild(style);
        },

        chartjsMinimal: function() {
            console.log('[九猫修复] 加载内联Chart.js最小版本');
            // 最小版本的Chart.js，只包含基本图表功能
            window.Chart = function(ctx, config) {
                this.ctx = ctx;
                this.config = config || {};
                this.data = config.data || { datasets: [] };
                this.options = config.options || {};
                this.type = config.type || 'line';

                // 绘制简单的文本消息，表明这是备用版本
                if (ctx && ctx.canvas) {
                    const canvas = ctx.canvas;
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.fillStyle = '#f8f9fa';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);

                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#343a40';
                    ctx.textAlign = 'center';
                    ctx.fillText('图表加载失败，使用应急备用图表', canvas.width / 2, canvas.height / 2 - 15);
                    ctx.fillText('请刷新页面重试', canvas.width / 2, canvas.height / 2 + 15);
                }

                console.log('[九猫修复] 创建了应急Chart实例:', this.type);
                return this;
            };

            // 添加必要的方法
            Chart.prototype.update = function() {
                console.log('[九猫修复] Chart.update被调用');
                return this;
            };

            Chart.prototype.destroy = function() {
                console.log('[九猫修复] Chart.destroy被调用');
                if (this.ctx && this.ctx.canvas) {
                    this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
                }
                return true;
            };

            // 添加基本的静态属性和方法
            Chart.defaults = {
                global: {
                    defaultFontFamily: 'Arial',
                    defaultFontSize: 12
                }
            };

            Chart.helpers = {
                merge: function(target, source) {
                    return Object.assign({}, target, source);
                }
            };

            // 注册插件的方法
            Chart.register = function() {
                console.log('[九猫修复] Chart.register被调用，但未实际注册（应急模式）');
            };

            // 添加版本信息
            Chart.version = '应急 1.0.0';
        }
    };

    // 监控资源加载错误
    function monitorResourceLoadErrors() {
        // 监控现有资源
        document.querySelectorAll('link[rel="stylesheet"], script[src]').forEach(elem => {
            const url = elem.href || elem.src;
            if (url && !resourceState.monitored.has(url)) {
                resourceState.monitored.add(url);

                // 已经加载完成的资源不需要处理
                if (elem.sheet || (elem.readyState && elem.readyState === 'complete')) {
                    resourceState.loaded[url] = true;
                    return;
                }

                // 处理加载错误
                elem.addEventListener('error', function() {
                    handleResourceError(url, elem);
                });
            }
        });

        // 记录加载的资源，并监控未来的资源
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(document, tagName);

            if (tagName.toLowerCase() === 'link' || tagName.toLowerCase() === 'script') {
                // 监听src或href属性的变更
                const originalSetAttribute = element.setAttribute;
                element.setAttribute = function(name, value) {
                    if ((name === 'src' || name === 'href') && value) {
                        // 设置后监控该资源
                        if (!resourceState.monitored.has(value)) {
                            resourceState.monitored.add(value);

                            // 添加错误处理
                            element.addEventListener('error', function() {
                                handleResourceError(value, element);
                            });
                        }
                    }
                    return originalSetAttribute.call(this, name, value);
                };
            }

            return element;
        };
    }

    // 处理资源加载错误
    function handleResourceError(url, element) {
        console.error('[九猫修复] 资源加载失败:', url);

        // 标记为失败
        resourceState.failed[url] = true;

        // 获取重试次数
        resourceState.retries[url] = (resourceState.retries[url] || 0) + 1;

        // 如果超过最大重试次数，尝试备用方案
        if (resourceState.retries[url] > resourceState.maxRetries) {
            // 检查是什么类型的资源
            const resourceType = getResourceType(url, element);
            if (resourceType) {
                console.log('[九猫修复] 尝试加载备用资源:', resourceType);
                loadFallbackResource(url, resourceType, element);
            }
        } else {
            // 重试加载
            console.log('[九猫修复] 重试加载资源:', url, '第', resourceState.retries[url], '次');
            setTimeout(function() {
                retryLoadResource(url, element);
            }, 1000 * resourceState.retries[url]); // 递增延迟
        }
    }

    // 获取资源类型
    function getResourceType(url, element) {
        const lowerUrl = url.toLowerCase();

        // 根据URL判断
        if (lowerUrl.includes('jquery') && lowerUrl.includes('.js')) {
            return 'jquery';
        } else if (lowerUrl.includes('bootstrap') && lowerUrl.includes('.js')) {
            return 'bootstrap';
        } else if (lowerUrl.includes('bootstrap') && lowerUrl.includes('.css')) {
            return 'bootstrapCss';
        } else if ((lowerUrl.includes('chart') || lowerUrl.includes('chart.js')) && lowerUrl.includes('.js')) {
            return 'chartjs';
        }

        // 根据元素类型判断
        if (element) {
            const tagName = element.tagName.toLowerCase();
            if (tagName === 'script') {
                // 可能是JavaScript库
                return detectScriptType(element);
            } else if (tagName === 'link' && element.rel === 'stylesheet') {
                // 可能是CSS
                return detectStyleType(element);
            }
        }

        return null;
    }

    // 检测脚本类型
    function detectScriptType(element) {
        // 从元素属性或ID中获取线索
        const id = element.id || '';
        const className = element.className || '';

        if (id.includes('jquery') || className.includes('jquery')) {
            return 'jquery';
        } else if (id.includes('bootstrap') || className.includes('bootstrap')) {
            return 'bootstrap';
        } else if (id.includes('chart') || className.includes('chart')) {
            return 'chartjs';
        }

        return null;
    }

    // 检测样式类型
    function detectStyleType(element) {
        // 从元素属性或ID中获取线索
        const id = element.id || '';
        const className = element.className || '';

        if (id.includes('bootstrap') || className.includes('bootstrap')) {
            return 'bootstrapCss';
        }

        return null;
    }

    // 重试加载资源
    function retryLoadResource(url, element) {
        if (element) {
            // 克隆并替换元素
            const clone = element.cloneNode(true);

            // 确保监听错误事件
            clone.addEventListener('error', function() {
                handleResourceError(url, clone);
            });

            // 成功回调
            clone.addEventListener('load', function() {
                console.log('[九猫修复] 重试加载成功:', url);
                resourceState.loaded[url] = true;
            });

            if (element.parentNode) {
                element.parentNode.replaceChild(clone, element);
            }
        }
    }

    // 加载备用资源
    function loadFallbackResource(originalUrl, resourceType, originalElement) {
        // 如果有CDN备用列表
        if (cdnUrls[resourceType] && cdnUrls[resourceType].length > 0) {
            console.log('[九猫修复] 尝试从CDN加载备用资源:', resourceType);

            // 创建新元素
            let newElement;
            if (originalElement.tagName.toLowerCase() === 'script') {
                newElement = document.createElement('script');
                newElement.type = 'text/javascript';
                newElement.src = cdnUrls[resourceType][0]; // 使用第一个CDN
            } else {
                newElement = document.createElement('link');
                newElement.rel = 'stylesheet';
                newElement.type = 'text/css';
                newElement.href = cdnUrls[resourceType][0]; // 使用第一个CDN
            }

            // 错误处理 - 尝试其他CDN
            let cdnIndex = 0;
            newElement.addEventListener('error', function() {
                cdnIndex++;
                if (cdnIndex < cdnUrls[resourceType].length) {
                    console.log('[九猫修复] CDN加载失败，尝试下一个CDN:', cdnUrls[resourceType][cdnIndex]);
                    if (newElement.tagName.toLowerCase() === 'script') {
                        newElement.src = cdnUrls[resourceType][cdnIndex];
                    } else {
                        newElement.href = cdnUrls[resourceType][cdnIndex];
                    }
                } else {
                    console.error('[九猫修复] 所有CDN都加载失败，使用内联备用资源');

                    // 移除失败的元素
                    if (newElement.parentNode) {
                        newElement.parentNode.removeChild(newElement);
                    }

                    // 使用内联备用资源
                    if (inlineResources[resourceType]) {
                        inlineResources[resourceType]();
                    }
                }
            });

            // 成功回调
            newElement.addEventListener('load', function() {
                console.log('[九猫修复] CDN加载成功:', cdnUrls[resourceType][cdnIndex]);
                resourceState.loaded[originalUrl] = true;
            });

            // 替换原始元素
            if (originalElement.parentNode) {
                originalElement.parentNode.replaceChild(newElement, originalElement);
            } else {
                // 如果没有父节点，添加到head或body
                (document.head || document.documentElement).appendChild(newElement);
            }
        } else {
            // 没有CDN备用，直接使用内联备用
            console.log('[九猫修复] 没有CDN备用资源，使用内联备用');
            if (inlineResources[resourceType]) {
                inlineResources[resourceType]();
            }

            // 移除原始元素
            if (originalElement.parentNode) {
                originalElement.parentNode.removeChild(originalElement);
            }
        }
    }

    // 处理pending fallbacks
    function processPendingFallbacks() {
        if (resourceState.pendingFallbacks.length > 0) {
            const fallback = resourceState.pendingFallbacks.shift();
            loadFallbackResource(fallback.url, fallback.type, fallback.element);

            // 继续处理队列
            if (resourceState.pendingFallbacks.length > 0) {
                setTimeout(processPendingFallbacks, 100);
            }
        }
    }

    // 启动监控
    monitorResourceLoadErrors();

    // 页面加载完成后检查资源状态
    window.addEventListener('load', function() {
        console.log('[九猫修复] 页面加载完成，检查资源状态');

        // 处理所有的pending fallbacks
        processPendingFallbacks();

        // 检查jQuery、Bootstrap和Chart.js是否可用
        if (!window.jQuery && !resourceState.loaded['jquery']) {
            console.warn('[九猫修复] jQuery未检测到，加载备用版本');
            inlineResources.jquery();
        }

        // 检查常见的资源错误
        const missingResources = Array.from(document.querySelectorAll('link[rel="stylesheet"][href], script[src]'))
            .filter(elem => {
                const url = elem.href || elem.src;
                return !elem.sheet && !resourceState.loaded[url] && url;
            });

        if (missingResources.length > 0) {
            console.warn('[九猫修复] 检测到', missingResources.length, '个资源可能未正确加载');
            missingResources.forEach(elem => {
                const url = elem.href || elem.src;
                const type = getResourceType(url, elem);
                if (type) {
                    resourceState.pendingFallbacks.push({
                        url: url,
                        type: type,
                        element: elem
                    });
                }
            });

            // 开始处理
            processPendingFallbacks();
        }
    });

    // 标记修复已加载
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded) {
        window.__nineCatsFixes.loaded['emergency-resource-loader'] = true;
    }

    console.log('[九猫修复] 应急资源加载器加载完成');
})();