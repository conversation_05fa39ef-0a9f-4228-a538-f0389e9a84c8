from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult

def main():
    session = Session()
    try:
        # 获取所有小说
        novels = session.query(Novel).all()
        print('小说列表:')
        for novel in novels:
            is_template = novel.novel_metadata and novel.novel_metadata.get("is_template", False)
            print(f'ID: {novel.id}, 标题: {novel.title}, 是否为参考蓝本: {is_template}')
            
            # 获取分析结果
            analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel.id).all()
            available_dimensions = [result.dimension for result in analysis_results]
            print(f'  分析维度: {available_dimensions}')
            
            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel.id).all()
            print(f'  章节数量: {len(chapters)}')
            
            # 获取章节分析结果
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel.id,
                    chapter_id=chapter.id
                ).all()
                
                chapter_dimensions = [result.dimension for result in chapter_results]
                print(f'  章节ID: {chapter.id}, 标题: {chapter.title}, 分析维度: {chapter_dimensions}')
    finally:
        session.close()

if __name__ == "__main__":
    main()
