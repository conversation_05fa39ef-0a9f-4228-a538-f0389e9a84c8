/**
 * 九猫 - 导航测试脚本
 * 用于测试导航修复是否有效
 * 版本: 1.0.0
 */

(function() {
    console.log('[导航测试] 脚本已加载');
    
    // 等待页面加载完成
    window.addEventListener('load', function() {
        console.log('[导航测试] 页面加载完成，开始测试');
        
        // 测试资源加载器
        testResourceLoader();
        
        // 测试导航
        testNavigation();
    });
    
    // 测试资源加载器
    function testResourceLoader() {
        console.log('[导航测试] 测试资源加载器');
        
        try {
            // 检查document.body是否存在
            if (document.body) {
                console.log('[导航测试] document.body存在');
            } else {
                console.warn('[导航测试] document.body不存在，这可能导致问题');
            }
            
            // 检查是否有appendChild错误
            console.log('[导航测试] 尝试向body添加元素');
            const testDiv = document.createElement('div');
            testDiv.id = 'navigation-test-div';
            testDiv.style.display = 'none';
            document.body.appendChild(testDiv);
            console.log('[导航测试] 成功向body添加元素');
            
            // 移除测试元素
            document.body.removeChild(testDiv);
        } catch (e) {
            console.error('[导航测试] 资源加载器测试失败:', e);
        }
    }
    
    // 测试导航
    function testNavigation() {
        console.log('[导航测试] 测试导航');
        
        try {
            // 检查当前页面路径
            const path = window.location.pathname;
            console.log('[导航测试] 当前页面路径:', path);
            
            // 检查是否在小说详情页
            const isNovelPage = path.match(/\/novel\/\d+\/?$/);
            if (isNovelPage) {
                console.log('[导航测试] 当前在小说详情页');
                
                // 获取小说ID
                const novelId = path.match(/\/novel\/(\d+)/)[1];
                console.log('[导航测试] 小说ID:', novelId);
                
                // 检查章节分析按钮
                testChapterAnalysisButtons(novelId);
                
                // 检查维度分析按钮
                testDimensionAnalysisButtons(novelId);
                
                // 测试导航终极修复脚本
                testNavigationSupremeFix(novelId);
            } else {
                console.log('[导航测试] 当前不在小说详情页，跳过按钮测试');
            }
        } catch (e) {
            console.error('[导航测试] 导航测试失败:', e);
        }
    }
    
    // 测试导航终极修复脚本
    function testNavigationSupremeFix(novelId) {
        console.log('[导航测试] 测试导航终极修复脚本');
        
        // 检查脚本是否已加载
        if (window._navigationSupremeFixLoaded) {
            console.log('[导航测试] 导航终极修复脚本已加载');
        } else {
            console.warn('[导航测试] 导航终极修复脚本未加载');
        }
        
        // 检查API是否可用
        if (window.navigationSupremeFix) {
            console.log('[导航测试] 导航终极修复API可用');
            
            // 检查全局点击处理器
            if (window.__supremeNavigationGlobalHandlerAdded) {
                console.log('[导航测试] 导航终极修复全局点击处理器已添加');
            } else {
                console.warn('[导航测试] 导航终极修复全局点击处理器未添加');
            }
            
            // 手动调用修复函数
            console.log('[导航测试] 手动调用导航终极修复函数');
            window.navigationSupremeFix.fixAllButtons();
        } else {
            console.warn('[导航测试] 导航终极修复API不可用');
        }
    }
    
    // 测试章节分析按钮
    function testChapterAnalysisButtons(novelId) {
        console.log('[导航测试] 测试章节分析按钮');
        
        // 查找所有可能的章节分析按钮
        const buttons = document.querySelectorAll('a.btn, button.btn, a[href*="chapter"], [data-target*="chapter"]');
        console.log(`[导航测试] 找到${buttons.length}个可能的按钮`);
        
        let chapterButtons = [];
        
        // 检查每个按钮
        buttons.forEach(btn => {
            if (!btn.textContent) return;
            
            // 检查是否是章节分析按钮
            const text = btn.textContent.trim().toLowerCase();
            if (text.includes('章节') && 
               (text.includes('分析') || text.includes('列表') || text.includes('管理'))) {
                
                chapterButtons.push(btn);
                
                // 检查链接
                if (btn.tagName === 'A') {
                    const href = btn.getAttribute('href');
                    const expectedHref = `/novel/${novelId}/chapters`;
                    
                    console.log(`[导航测试] 章节分析按钮链接: ${href}`);
                    
                    if (href === expectedHref) {
                        console.log('[导航测试] 链接正确');
                    } else {
                        console.warn(`[导航测试] 链接不正确，应为: ${expectedHref}`);
                    }
                }
                
                // 检查点击事件
                console.log('[导航测试] 章节分析按钮点击事件:', btn.onclick ? '已设置' : '未设置');
            }
        });
        
        console.log(`[导航测试] 找到${chapterButtons.length}个章节分析按钮`);
        
        // 特别处理小说ID为41的情况
        if (novelId === '41') {
            console.log('[导航测试] 检测到小说ID为41，检查特殊处理');
            
            // 检查全局点击处理器
            if (window.__novel41ClickHandlerSet) {
                console.log('[导航测试] 小说41特殊处理已设置');
            } else {
                console.warn('[导航测试] 小说41特殊处理未设置');
            }
        }
    }
    
    // 测试维度分析按钮
    function testDimensionAnalysisButtons(novelId) {
        console.log('[导航测试] 测试维度分析按钮');
        
        // 查找所有维度分析按钮
        const buttons = document.querySelectorAll('[data-dimension], .dimension-btn, .analyze-btn, .view-btn');
        console.log(`[导航测试] 找到${buttons.length}个可能的维度分析按钮`);
        
        let dimensionButtons = [];
        
        // 检查每个按钮
        buttons.forEach(btn => {
            // 获取维度名称
            let dimension = btn.getAttribute('data-dimension');
            
            // 如果没有data-dimension属性，尝试从类名或内容中获取
            if (!dimension) {
                if (btn.classList) {
                    const classList = Array.from(btn.classList);
                    const dimensionClass = classList.find(cls => cls.startsWith('dimension-') || cls.startsWith('analyze-'));
                    
                    if (dimensionClass) {
                        dimension = dimensionClass.replace('dimension-', '').replace('analyze-', '');
                    }
                }
            }
            
            if (dimension) {
                dimensionButtons.push(btn);
                
                // 检查链接
                if (btn.tagName === 'A') {
                    const href = btn.getAttribute('href');
                    const expectedHref = `/novel/${novelId}/analysis/${dimension}`;
                    
                    console.log(`[导航测试] 维度分析按钮(${dimension})链接: ${href}`);
                    
                    if (href === expectedHref) {
                        console.log('[导航测试] 链接正确');
                    } else {
                        console.warn(`[导航测试] 链接不正确，应为: ${expectedHref}`);
                    }
                }
                
                // 检查点击事件
                console.log('[导航测试] 维度分析按钮点击事件:', btn.onclick ? '已设置' : '未设置');
            }
        });
        
        console.log(`[导航测试] 找到${dimensionButtons.length}个维度分析按钮`);
    }
})();
