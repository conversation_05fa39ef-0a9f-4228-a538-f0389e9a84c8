"""
分析过程路由模块，提供访问和展示详细分析过程的功能。
"""
import logging
import json
from flask import Blueprint, render_template, jsonify, request, abort

from src.db.connection import Session
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.analysis_process import AnalysisProcess

logger = logging.getLogger(__name__)

# 创建蓝图
analysis_process_bp = Blueprint('analysis_process', __name__)

@analysis_process_bp.route('/novel/<int:novel_id>/analysis/<dimension>/process')
def view_analysis_process(novel_id, dimension):
    """
    查看特定分析维度的详细分析过程。

    注意：此路由使用原始模板，加载所有分析过程数据。
    对于大型分析，建议使用优化版本的分析过程查看器。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            abort(404, description=f"找不到ID为{novel_id}的小说")

        # 获取分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()
        if not result:
            abort(404, description=f"找不到维度为{dimension}的分析结果")

        # 获取分析过程记录
        processes = session.query(AnalysisProcess).filter_by(
            novel_id=novel_id, dimension=dimension
        ).all()

        # 手动排序：init -> chunk_analysis -> combine -> finalize
        stage_order = {"init": 1, "chunk_analysis": 2, "combine": 3, "finalize": 4}
        processes.sort(key=lambda p: (
            stage_order.get(p.processing_stage, 5),
            p.block_index,
            p.stage_index
        ))

        if not processes:
            # 如果没有记录详细过程，返回提示信息
            return render_template('error.html',
                                   error_title="未找到分析过程记录",
                                   error_message=f"没有找到小说 '{novel.title}' 维度 '{dimension}' 的详细分析过程记录。\n"
                                               f"这可能是因为该分析是在启用详细过程记录功能之前进行的。\n"
                                               f"请尝试使用 '启动九猫_分析过程完整记录版.vbs' 启动，并重新进行分析。")

        # 根据处理阶段分组
        stage_groups = {
            "init": {"name": "初始化阶段", "processes": []},
            "chunk_analysis": {"name": "分块分析阶段", "processes": []},
            "combine": {"name": "结果合并阶段", "processes": []},
            "finalize": {"name": "最终处理阶段", "processes": []}
        }

        # 分组并添加阶段名称
        for process in processes:
            # 添加阶段名称
            process.stage_name = process.get_stage_name()

            # 分组
            if process.processing_stage in stage_groups:
                stage_groups[process.processing_stage]["processes"].append(process)
            else:
                # 处理其他阶段
                if "other" not in stage_groups:
                    stage_groups["other"] = {"name": "其他阶段", "processes": []}
                stage_groups["other"]["processes"].append(process)

        # 转换为列表
        stages = [stage_groups[key] for key in stage_groups if key in stage_groups and stage_groups[key]["processes"]]

        return render_template('analysis_process.html',
                              novel=novel,
                              result=result,
                              processes=processes,
                              stages=stages)
    except Exception as e:
        logger.error(f"查看分析过程时出错: {str(e)}", exc_info=True)
        return render_template('error.html',
                              error_title="查看分析过程时出错",
                              error_message=str(e))
    finally:
        session.close()

@analysis_process_bp.route('/novel/<int:novel_id>/analysis/<dimension>/process_viewer')
def view_analysis_process_viewer(novel_id, dimension):
    """
    查看特定分析维度的详细分析过程（优化版）。

    使用延迟加载和分页技术，减少内存占用。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            abort(404, description=f"找不到ID为{novel_id}的小说")

        # 获取分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()
        if not result:
            abort(404, description=f"找不到维度为{dimension}的分析结果")

        # 渲染优化版分析过程查看器模板
        return render_template('analysis_process_viewer.html',
                              novel=novel,
                              dimension=dimension)
    except Exception as e:
        logger.error(f"查看分析过程查看器时出错: {str(e)}", exc_info=True)
        return render_template('error.html',
                              error_title="查看分析过程时出错",
                              error_message=str(e))
    finally:
        session.close()

@analysis_process_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>/process')
def api_get_analysis_process(novel_id, dimension):
    """
    获取特定分析维度的详细分析过程API。

    支持分页和过滤，用于优化版分析过程查看器。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        stage = request.args.get('stage', None)
        search = request.args.get('search', None)
        block_index = request.args.get('block_index', None)

        # 构建查询
        query = session.query(AnalysisProcess).filter_by(
            novel_id=novel_id, dimension=dimension
        )

        # 应用阶段过滤
        if stage and stage != 'all':
            query = query.filter(AnalysisProcess.processing_stage == stage)
        elif block_index is not None:
            try:
                block_index = int(block_index)
                query = query.filter_by(block_index=block_index)
            except ValueError:
                pass

        # 应用搜索过滤
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (AnalysisProcess.input_text.ilike(search_term)) |
                (AnalysisProcess.output_text.ilike(search_term)) |
                (AnalysisProcess.prompt_used.ilike(search_term)) |
                (AnalysisProcess.error_message.ilike(search_term))
            )

        # 获取总数
        total_count = query.count()

        # 获取所有记录并手动排序
        processes = query.all()

        # 手动排序：init -> chunk_analysis -> combine -> finalize
        stage_order = {"init": 1, "chunk_analysis": 2, "combine": 3, "finalize": 4}
        processes.sort(key=lambda p: (
            stage_order.get(p.processing_stage, 5),
            p.block_index,
            p.stage_index
        ))

        # 应用分页
        start = (page - 1) * limit
        end = start + limit
        processes = processes[start:end]

        # 获取分析结果元数据
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()

        # 禁用元数据，避免序列化错误
        result_metadata = {
            "created_at": result.created_at.isoformat() if result and result.created_at else None,
            "processing_time": None
        }

        logger.info("元数据已被禁用以避免序列化错误")

        # 转换为字典表示
        process_dicts = [process.to_dict() for process in processes]

        return jsonify({
            "success": True,
            "processes": process_dicts,
            "count": total_count,
            "page": page,
            "limit": limit,
            "pages": (total_count + limit - 1) // limit,
            "metadata": result_metadata
        })
    except Exception as e:
        logger.error(f"获取分析过程API时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@analysis_process_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>/process/<int:process_id>')
def api_get_analysis_process_detail(novel_id, dimension, process_id):
    """
    获取特定分析过程详情的API。

    支持按需加载完整内容，用于优化版分析过程查看器。
    可以通过content_type参数指定要获取的内容类型（input、output、prompt）。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
        process_id: 过程ID
    """
    session = Session()
    try:
        # 获取内容类型参数
        content_type = request.args.get('content_type', None)

        # 获取特定过程记录
        process = session.query(AnalysisProcess).filter_by(
            id=process_id, novel_id=novel_id, dimension=dimension
        ).first()

        if not process:
            return jsonify({
                "success": False,
                "error": "未找到指定的分析过程记录"
            }), 404

        # 转换为字典表示
        process_dict = process.to_dict()

        # 如果指定了内容类型，只返回该类型的完整内容
        if content_type:
            if content_type == 'input' and hasattr(process, 'input_text') and process.input_text:
                process_dict["input_text"] = process.input_text
            elif content_type == 'output' and hasattr(process, 'output_text') and process.output_text:
                process_dict["output_text"] = process.output_text
            elif content_type == 'prompt' and hasattr(process, 'prompt_used') and process.prompt_used:
                process_dict["prompt_used"] = process.prompt_used
            elif content_type == 'api_request' and hasattr(process, 'api_request') and process.api_request:
                process_dict["api_request"] = process.api_request
            elif content_type == 'api_response' and hasattr(process, 'api_response') and process.api_response:
                process_dict["api_response"] = process.api_response
        else:
            # 对于API请求和响应，添加完整内容
            if hasattr(process, 'api_request') and process.api_request:
                process_dict["api_request_full"] = process.api_request

            if hasattr(process, 'api_response') and process.api_response:
                process_dict["api_response_full"] = process.api_response

            # 添加完整的输入和输出文本
            if hasattr(process, 'input_text') and process.input_text:
                process_dict["input_text_full"] = process.input_text

            if hasattr(process, 'output_text') and process.output_text:
                process_dict["output_text_full"] = process.output_text

            if hasattr(process, 'prompt_used') and process.prompt_used:
                process_dict["prompt_used_full"] = process.prompt_used

        return jsonify({
            "success": True,
            "process": process_dict
        })
    except Exception as e:
        logger.error(f"获取分析过程详情API时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@analysis_process_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>/process/content')
def api_get_analysis_process_content(novel_id, dimension):
    """
    获取特定分析维度的分析过程内容API。

    将所有分析过程记录整合为一个完整的Markdown内容，用于在分析过程查看器中显示。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            return jsonify({
                "success": False,
                "error": f"找不到ID为{novel_id}的小说"
            }), 404

        # 获取分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()
        if not result:
            return jsonify({
                "success": False,
                "error": f"找不到维度为{dimension}的分析结果"
            }), 404

        # 获取分析过程记录
        processes = session.query(AnalysisProcess).filter_by(
            novel_id=novel_id, dimension=dimension
        ).all()

        # 手动排序：init -> chunk_analysis -> combine -> finalize
        stage_order = {"init": 1, "chunk_analysis": 2, "combine": 3, "finalize": 4}
        processes.sort(key=lambda p: (
            stage_order.get(p.processing_stage, 5),
            p.block_index,
            p.stage_index
        ))

        if not processes:
            # 如果没有分析过程记录，尝试使用分析结果的内容
            if result and hasattr(result, 'analysis_metadata') and result.analysis_metadata:
                try:
                    # 尝试从分析结果的content字段中提取推理过程
                    has_reasoning_content = False
                    reasoning_content = None

                    # 尝试从content字段中提取推理过程
                    if hasattr(result, 'content') and result.content:
                        content = result.content
                        if isinstance(content, str) and '分析过程' in content:
                            # 尝试提取分析过程部分
                            process_start = content.find('分析过程')
                            if process_start != -1:
                                # 找到分析过程后的内容
                                process_content = content[process_start:]
                                has_reasoning_content = True
                                reasoning_content = process_content
                                logger.info("从content字段中提取到分析过程内容")
                        elif isinstance(content, str) and '推理过程' in content:
                            # 尝试提取推理过程部分
                            process_start = content.find('推理过程')
                            if process_start != -1:
                                # 找到推理过程后的内容
                                process_content = content[process_start:]
                                has_reasoning_content = True
                                reasoning_content = process_content
                                logger.info("从content字段中提取到推理过程内容")
                        elif isinstance(content, str) and '思考过程' in content:
                            # 尝试提取思考过程部分
                            process_start = content.find('思考过程')
                            if process_start != -1:
                                # 找到思考过程后的内容
                                process_content = content[process_start:]
                                has_reasoning_content = True
                                reasoning_content = process_content
                                logger.info("从content字段中提取到思考过程内容")
                        else:
                            # 如果没有找到特定标记，使用整个内容
                            has_reasoning_content = True
                            reasoning_content = content
                            logger.info("使用整个content字段作为推理过程内容")

                    # 如果还是没有找到，尝试从分析结果内容中提取
                    if not has_reasoning_content and hasattr(result, 'content') and result.content:
                        content = result.content
                        if '分析过程' in content:
                            # 尝试提取分析过程部分
                            process_start = content.find('分析过程')
                            if process_start != -1:
                                # 找到分析过程后的内容
                                process_content = content[process_start:]

                                # 检查是否只有"分析过程"几个字
                                if len(process_content.strip()) > 10:  # 不只是"分析过程"几个字
                                    has_reasoning_content = True
                                    reasoning_content = process_content
                                    logger.info("从分析结果内容中提取到分析过程内容")
                                else:
                                    logger.warning("从分析结果内容中只找到'分析过程'几个字，没有实际内容")

                        # 直接检查原始内容中是否包含reasoning_content关键词
                        if not has_reasoning_content and 'reasoning_content' in content:
                            # 尝试提取reasoning_content部分
                            try:
                                # 查找可能的JSON格式
                                import re

                                # 尝试多种正则表达式模式
                                patterns = [
                                    r'"reasoning_content"\s*:\s*"(.*?)"(?:\s*\}|\s*,)',  # JSON格式，双引号
                                    r"'reasoning_content'\s*:\s*'(.*?)'(?:\s*\}|\s*,)",  # JSON格式，单引号
                                    r"'reasoning_content':\s*'([^']*)'",                 # 简单格式，单引号
                                    r'"reasoning_content":\s*"([^"]*)"',                 # 简单格式，双引号
                                    r"reasoning_content':\s*'(.*?)(?:'}}|'})",          # 结尾格式1
                                    r"reasoning_content':\s*'([^']*)'"                   # 任意单引号内容
                                ]

                                for pattern in patterns:
                                    # 使用re.DOTALL使.能匹配换行符
                                    matches = re.findall(pattern, content, re.DOTALL)
                                    if matches and matches[0]:
                                        reasoning_content = matches[0].replace('\\n', '\n').replace('\\\\', '\\')
                                        has_reasoning_content = True
                                        logger.info(f"通过正则表达式模式 '{pattern}' 从内容中提取到reasoning_content")
                                        break

                                # 如果正则表达式没有匹配到，尝试直接查找标记
                                if not has_reasoning_content:
                                    # 尝试多种标记组合
                                    start_markers = [
                                        "'reasoning_content': '",
                                        "'reasoning_content':'",
                                        '"reasoning_content": "',
                                        '"reasoning_content":"'
                                    ]
                                    end_markers = ["'}}", "'}", '"}', '"}}}', "'}}", "',", '",']

                                    for start_marker in start_markers:
                                        if start_marker in content:
                                            start_idx = content.find(start_marker) + len(start_marker)

                                            # 尝试所有可能的结束标记
                                            for end_marker in end_markers:
                                                end_idx = content.find(end_marker, start_idx)
                                                if end_idx > start_idx:
                                                    extracted = content[start_idx:end_idx]
                                                    # 检查提取的内容是否看起来像分析过程
                                                    if len(extracted) > 100 and ('分析' in extracted or '用户' in extracted):
                                                        reasoning_content = extracted.replace('\\n', '\n').replace('\\\\', '\\')
                                                        has_reasoning_content = True
                                                        logger.info(f"通过标记 '{start_marker}...{end_marker}' 从内容中提取到reasoning_content")
                                                        break

                                            if has_reasoning_content:
                                                break

                                # 如果还是没有找到，尝试直接搜索内容中的分析过程特征
                                if not has_reasoning_content:
                                    # 查找常见的分析过程开头
                                    process_starts = [
                                        "嗯，用户让我",
                                        "好的，我现在要",
                                        "我将分析",
                                        "下面我来分析",
                                        "首先，我需要",
                                        "我需要分析"
                                    ]

                                    for start_phrase in process_starts:
                                        if start_phrase in content:
                                            start_idx = content.find(start_phrase)
                                            # 尝试找到分析过程的结束位置
                                            end_markers = ["\n\n1. ", "\n\n## ", "\n\n总结", "\n\n以下是", "'}}", '"}']
                                            end_idx = -1

                                            for end_marker in end_markers:
                                                temp_idx = content.find(end_marker, start_idx)
                                                if temp_idx != -1 and (end_idx == -1 or temp_idx < end_idx):
                                                    end_idx = temp_idx

                                            if end_idx != -1:
                                                reasoning_content = content[start_idx:end_idx]
                                            else:
                                                # 如果没有找到结束标记，取从开始到内容结束
                                                reasoning_content = content[start_idx:]

                                            has_reasoning_content = True
                                            logger.info(f"通过分析过程特征 '{start_phrase}' 从内容中提取到reasoning_content")
                                            break
                            except Exception as e:
                                logger.error(f"尝试提取reasoning_content时出错: {str(e)}", exc_info=True)

                        # 如果内容中包含"嗯，用户让我"或类似的开头，可能是分析过程
                        if not has_reasoning_content and ('嗯，用户让我' in content or '好的，我现在要' in content or '我将分析' in content):
                            # 尝试提取这部分内容
                            start_markers = ['嗯，用户让我', '好的，我现在要', '我将分析', '下面我来分析']
                            for marker in start_markers:
                                if marker in content:
                                    start_idx = content.find(marker)
                                    # 找到下一个可能的结束标记
                                    end_markers = ['\n\n1. ', '\n\n## ', '\n\n总结']
                                    end_idx = -1
                                    for end_marker in end_markers:
                                        temp_idx = content.find(end_marker, start_idx)
                                        if temp_idx != -1 and (end_idx == -1 or temp_idx < end_idx):
                                            end_idx = temp_idx

                                    if end_idx != -1:
                                        reasoning_content = content[start_idx:end_idx]
                                    else:
                                        reasoning_content = content[start_idx:]

                                    has_reasoning_content = True
                                    logger.info(f"通过开头标记'{marker}'提取到可能的分析过程")
                                    break

                    if has_reasoning_content and reasoning_content:
                        # 构建Markdown内容
                        content = f"# {novel.title} - {dimension} 分析过程\n\n"
                        content += "## 分析摘要\n\n"
                        content += f"- **小说标题**: {novel.title}\n"
                        content += f"- **作者**: {novel.author or '未知'}\n"
                        content += f"- **分析维度**: {dimension}\n"
                        content += f"- **总字数**: {novel.word_count}\n\n"

                        if hasattr(result, 'created_at') and result.created_at:
                            try:
                                content += f"- **分析时间**: {result.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                            except:
                                content += f"- **分析时间**: {str(result.created_at)}\n\n"

                        content += "## 分析过程\n\n"
                        content += reasoning_content

                        # 添加分析结果内容
                        if hasattr(result, 'content') and result.content:
                            content += "\n\n## 分析结果\n\n"
                            content += result.content

                        return jsonify({
                            "success": True,
                            "content": content,
                            "source": "reasoning_content"
                        })
                except Exception as e:
                    logger.error(f"从分析结果元数据提取reasoning_content时出错: {str(e)}", exc_info=True)

            # 如果没有找到reasoning_content，尝试使用分析日志
            if result and hasattr(result, 'analysis_logs') and result.analysis_logs:
                try:
                    # 尝试从分析结果的日志中提取内容
                    logs = json.loads(result.analysis_logs) if isinstance(result.analysis_logs, str) else result.analysis_logs

                    # 构建简单的Markdown内容
                    content = f"# {novel.title} - {dimension} 分析过程\n\n"
                    content += "## 分析摘要\n\n"
                    content += f"- **小说标题**: {novel.title}\n"
                    content += f"- **作者**: {novel.author or '未知'}\n"
                    content += f"- **分析维度**: {dimension}\n"
                    content += f"- **总字数**: {novel.word_count}\n"

                    if hasattr(result, 'created_at') and result.created_at:
                        try:
                            content += f"- **分析时间**: {result.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                        except:
                            content += f"- **分析时间**: {str(result.created_at)}\n\n"

                    content += "## 分析过程\n\n"

                    # 添加分析日志内容
                    if isinstance(logs, dict) and 'logs' in logs:
                        for log in logs['logs']:
                            if isinstance(log, dict):
                                if 'message' in log:
                                    content += f"- {log['message']}\n"
                                if 'details' in log and log['details']:
                                    content += f"  {log['details']}\n"
                    elif isinstance(logs, list):
                        for log in logs:
                            if isinstance(log, dict):
                                if 'message' in log:
                                    content += f"- {log['message']}\n"
                                if 'details' in log and log['details']:
                                    content += f"  {log['details']}\n"
                            elif isinstance(log, str):
                                content += f"- {log}\n"
                    elif isinstance(logs, str):
                        content += logs

                    # 添加分析结果内容
                    if hasattr(result, 'content') and result.content:
                        content += "\n\n## 分析结果\n\n"
                        content += result.content

                    return jsonify({
                        "success": True,
                        "content": content,
                        "source": "analysis_logs"
                    })
                except Exception as e:
                    logger.error(f"从分析结果日志提取内容时出错: {str(e)}", exc_info=True)

            # 如果没有分析过程记录，也没有分析结果日志，则直接使用分析结果内容
            if result and hasattr(result, 'content') and result.content:
                # 从分析结果中提取内容
                content = f"# {novel.title} - {dimension} 分析过程\n\n"
                content += "## 分析摘要\n\n"
                content += f"- **小说标题**: {novel.title}\n"
                content += f"- **作者**: {novel.author or '未知'}\n"
                content += f"- **分析维度**: {dimension}\n"
                content += f"- **总字数**: {novel.word_count}\n"

                if hasattr(result, 'created_at') and result.created_at:
                    try:
                        content += f"- **分析时间**: {result.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                    except:
                        content += f"- **分析时间**: {str(result.created_at)}\n\n"

                content += "## 注意\n\n"
                content += "没有找到详细的分析过程记录，以下是分析结果内容。\n\n"
                content += "## 分析结果\n\n"
                content += result.content

                # 尝试从分析结果的元数据中提取其他信息
                if hasattr(result, 'analysis_metadata') and result.analysis_metadata:
                    try:
                        metadata = json.loads(result.analysis_metadata) if isinstance(result.analysis_metadata, str) else result.analysis_metadata

                        # 添加元数据信息
                        if metadata and isinstance(metadata, dict):
                            content += "\n\n## 分析元数据\n\n"

                            # 处理时间
                            if 'processing_time' in metadata:
                                content += f"- **处理时间**: {metadata['processing_time']} 秒\n"

                            # 分块数量
                            if 'chunk_count' in metadata:
                                content += f"- **分块数量**: {metadata['chunk_count']}\n"

                            # API调用次数
                            if 'api_calls' in metadata:
                                content += f"- **API调用次数**: {metadata['api_calls']}\n"

                            # 令牌使用量
                            if 'tokens_used' in metadata:
                                content += f"- **令牌使用量**: {metadata['tokens_used']}\n"

                            # 其他元数据
                            for key, value in metadata.items():
                                if key not in ['processing_time', 'chunk_count', 'api_calls', 'tokens_used', 'reasoning_content']:
                                    if isinstance(value, (str, int, float, bool)):
                                        content += f"- **{key}**: {value}\n"
                    except Exception as e:
                        logger.error(f"解析分析元数据时出错: {str(e)}", exc_info=True)

                return jsonify({
                    "success": True,
                    "content": content,
                    "source": "analysis_result_content"
                })

            # 如果都没有，返回错误
            return jsonify({
                "success": False,
                "error": "没有找到分析过程记录"
            }), 404

        # 生成Markdown内容
        content = generate_process_content(novel, dimension, processes)

        return jsonify({
            "success": True,
            "content": content
        })
    except Exception as e:
        logger.error(f"获取分析过程内容API时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

def generate_process_content(novel, dimension, processes):
    """
    生成分析过程内容。

    将分析过程记录整合为一个完整的Markdown内容。

    Args:
        novel: 小说对象
        dimension: 分析维度
        processes: 分析过程记录列表

    Returns:
        整合后的Markdown内容
    """
    # 标题
    content = f"# {novel.title} - {dimension} 分析过程\n\n"

    # 分析摘要
    content += "## 分析摘要\n\n"
    content += f"- **小说标题**: {novel.title}\n"
    content += f"- **作者**: {novel.author or '未知'}\n"
    content += f"- **分析维度**: {dimension}\n"
    content += f"- **总字数**: {novel.word_count}\n"
    content += f"- **分块数量**: {len(set([p.block_index for p in processes]))}\n"

    # 获取分析时间
    if processes and hasattr(processes[0], 'created_at') and processes[0].created_at:
        try:
            content += f"- **分析时间**: {processes[0].created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
        except:
            # 如果日期格式化失败，使用简单的字符串表示
            content += f"- **分析时间**: {str(processes[0].created_at)}\n"

    content += "\n"

    # 按阶段分组
    stage_groups = {
        "init": {"name": "初始化阶段", "processes": []},
        "chunk_analysis": {"name": "分块分析阶段", "processes": []},
        "combine": {"name": "结果合并阶段", "processes": []},
        "finalize": {"name": "最终处理阶段", "processes": []}
    }

    # 分组
    for process in processes:
        if process.processing_stage in stage_groups:
            stage_groups[process.processing_stage]["processes"].append(process)
        else:
            # 处理其他阶段
            if "other" not in stage_groups:
                stage_groups["other"] = {"name": "其他阶段", "processes": []}
            stage_groups["other"]["processes"].append(process)

    # 按阶段生成内容
    for stage_key in ["init", "chunk_analysis", "combine", "finalize", "other"]:
        if stage_key in stage_groups and stage_groups[stage_key]["processes"]:
            stage = stage_groups[stage_key]
            content += f"## {stage['name']}\n\n"

            # 处理每个过程
            for i, process in enumerate(stage["processes"]):
                # 过程标题
                if stage_key == "chunk_analysis":
                    content += f"### 分块 {process.block_index + 1}/{process.total_blocks}\n\n"
                elif i > 0:
                    content += f"### {stage['name']} - 步骤 {i + 1}\n\n"
                else:
                    content += f"### {stage['name']}\n\n"

                # 处理时间
                if process.processing_time:
                    content += f"**处理时间**: {process.processing_time} 毫秒\n\n"

                # 令牌数
                if process.tokens_used:
                    content += f"**令牌数**: {process.tokens_used}\n\n"

                # 错误信息
                if not process.is_successful and process.error_message:
                    content += f"**错误**: {process.error_message}\n\n"

                # 输入文本
                if process.input_text:
                    content += "#### 输入文本\n\n"
                    content += "```\n"
                    content += process.input_text[:1000] + ("..." if len(process.input_text) > 1000 else "")
                    content += "\n```\n\n"

                # 提示词
                if process.prompt_used:
                    content += "#### 提示词\n\n"
                    content += "```\n"
                    content += process.prompt_used[:1000] + ("..." if len(process.prompt_used) > 1000 else "")
                    content += "\n```\n\n"

                # 输出文本
                if process.output_text:
                    content += "#### 输出文本\n\n"
                    content += process.output_text + "\n\n"

    return content

@analysis_process_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content')
def api_get_reasoning_content(novel_id, dimension):
    """
    直接获取分析结果中的reasoning_content字段。

    这是一个简化的API，专门用于获取分析过程内容。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            return jsonify({
                "success": False,
                "error": f"找不到ID为{novel_id}的小说"
            }), 404

        # 检查是否有分析过程记录
        process_records = session.query(AnalysisProcess).filter_by(
            novel_id=novel_id, dimension=dimension
        ).order_by(AnalysisProcess.created_at.desc()).limit(5).all()

        # 如果有分析过程记录，尝试从中提取推理过程
        if process_records:
            for process in process_records:
                if process.stage == 'init' and process.input_text:
                    # 检查输入文本是否包含推理过程特征
                    process_markers = [
                        '好的，我现在要',
                        '首先，我需要',
                        '我将分析',
                        '我需要分析',
                        '嗯，用户让我',
                        '下面我来分析',
                        '让我来分析',
                        '我会按照以下步骤',
                        '我将按照以下步骤',
                        '我将从以下几个方面',
                        '我需要从以下几个方面',
                        '我将逐步分析',
                        '好的，我现在需要',
                        '我需要通读一遍',
                        '我需要先了解',
                        '我会先阅读',
                        '我先阅读一遍',
                        '我需要分析用户提供的',
                        '开始分析这段小说',
                        '我需要全面考虑',
                        '我得仔细阅读',
                        '作为文学分析专家'
                    ]

                    for marker in process_markers:
                        if marker in process.input_text:
                            logger.info(f"从分析过程记录中找到推理过程，长度: {len(process.input_text)}")
                            return jsonify({
                                "success": True,
                                "reasoning_content": process.input_text,
                                "source": "analysis_process_input",
                                "dimension": dimension,
                                "novel_id": novel_id
                            })

        # 获取分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()

        if not result:
            return jsonify({
                "success": False,
                "error": f"找不到维度为{dimension}的分析结果"
            }), 404

        # 特殊处理language_style维度
        if dimension == 'language_style' and hasattr(result, 'content') and result.content:
            logger.info("检测到language_style维度，应用特殊处理")

            content = result.content

            # 尝试从内容中提取分析过程
            process_content = None

            # 检查是否包含分析过程的特征
            process_markers = [
                '分析过程',
                '首先，我需要',
                '我将分析',
                '我需要分析',
                '嗯，用户让我',
                '好的，我现在要',
                '下面我来分析'
            ]

            # 检查是否包含任何分析过程标记
            for marker in process_markers:
                if marker in content:
                    logger.info(f"在language_style内容中找到分析过程标记: {marker}")

                    # 提取从标记到结果部分的内容
                    start_idx = content.find(marker)
                    end_markers = ['分析结果', '## 结论', '# 结果', '# 分析结果', '## 分析结果', '\n\n1. ', '\n\n总结']
                    end_idx = -1

                    for end_marker in end_markers:
                        pos = content.find(end_marker, start_idx)
                        if pos != -1 and (end_idx == -1 or pos < end_idx):
                            end_idx = pos

                    # 提取分析过程部分
                    if end_idx != -1:
                        process_content = content[start_idx:end_idx]
                    else:
                        process_content = content[start_idx:]

                    break

            # 如果找到了分析过程内容
            if process_content:
                logger.info(f"成功从language_style内容中提取到分析过程，长度: {len(process_content)}")

                # 构建Markdown内容
                markdown_content = f"# {novel.title} - {dimension} 分析过程\n\n"
                markdown_content += "## 分析摘要\n\n"
                markdown_content += f"- **小说标题**: {novel.title}\n"
                markdown_content += f"- **作者**: {novel.author or '未知'}\n"
                markdown_content += f"- **分析维度**: {dimension}\n"
                markdown_content += f"- **总字数**: {novel.word_count}\n\n"

                markdown_content += "## 分析过程\n\n"
                markdown_content += process_content

                return jsonify({
                    "success": True,
                    "reasoning_content": process_content,
                    "content": markdown_content,
                    "source": "language_style_content"
                })

        # 尝试从分析结果的元数据中提取reasoning_content
        if not hasattr(result, 'analysis_metadata') or not result.analysis_metadata:
            return jsonify({
                "success": False,
                "error": "分析结果没有元数据"
            }), 404

        metadata = json.loads(result.analysis_metadata) if isinstance(result.analysis_metadata, str) else result.analysis_metadata

        # 直接打印元数据，帮助调试
        logger.info(f"分析元数据: {str(metadata)[:1000]}")

        # 检查元数据中是否包含reasoning_content
        has_reasoning_content = False
        reasoning_content = None

        # 打印元数据的所有键
        if isinstance(metadata, dict):
            logger.info(f"元数据键: {list(metadata.keys())}")

            # 检查是否有嵌套字典
            for key, value in metadata.items():
                if isinstance(value, dict):
                    logger.info(f"嵌套字典 {key} 的键: {list(value.keys())}")

        # 尝试直接从元数据中提取分析过程内容
        if isinstance(metadata, dict):
            # 在API响应中，reasoning_content可能直接包含在metadata顶层
            if 'reasoning_content' in metadata:
                has_reasoning_content = True
                reasoning_content = metadata['reasoning_content']
                logger.info("在元数据顶层找到reasoning_content")
            # 也可能在result字段内
            elif 'result' in metadata and isinstance(metadata['result'], dict) and 'reasoning_content' in metadata['result']:
                has_reasoning_content = True
                reasoning_content = metadata['result']['reasoning_content']
                logger.info("在result字段中找到reasoning_content")

        # 如果没有找到reasoning_content，但API响应包含了COMPLETE_TEXT字段
        if not has_reasoning_content and isinstance(metadata, dict) and 'COMPLETE_TEXT' in metadata:
            complete_text = metadata['COMPLETE_TEXT']
            # 从完整文本中提取分析过程部分
            if isinstance(complete_text, str) and '分析过程' in complete_text:
                # 查找分析过程部分
                process_section_start = complete_text.find('分析过程')
                if process_section_start != -1:
                    # 查找下一个部分或使用文本结尾
                    next_section_markers = ['分析结果', '## 结论', '# 结果']
                    process_section_end = -1
                    for marker in next_section_markers:
                        pos = complete_text.find(marker, process_section_start)
                        if pos != -1 and (process_section_end == -1 or pos < process_section_end):
                            process_section_end = pos

                    if process_section_end == -1:
                        reasoning_content = complete_text[process_section_start:]
                    else:
                        reasoning_content = complete_text[process_section_start:process_section_end]

                    has_reasoning_content = True
                    logger.info("从COMPLETE_TEXT中提取到分析过程内容")

        # 如果在元数据中找不到reasoning_content，检查API响应格式
        if not has_reasoning_content and 'choices' in metadata:
            choices = metadata['choices']
            if isinstance(choices, list) and len(choices) > 0:
                choice = choices[0]
                if isinstance(choice, dict) and 'message' in choice:
                    message = choice['message']
                    if isinstance(message, dict) and 'content' in message:
                        content = message['content']
                        # 检查内容是否包含分析过程
                        if '分析过程' in content:
                            # 提取分析过程部分
                            reasoning_content = content
                            has_reasoning_content = True
                            logger.info("从API choices中提取到分析过程内容")

        # 如果还是没找到，但API响应包含reasoning字段
        if not has_reasoning_content and 'reasoning' in metadata:
            reasoning = metadata['reasoning']
            if isinstance(reasoning, str) and len(reasoning) > 50:  # 确保不是空字符串
                reasoning_content = reasoning
                has_reasoning_content = True
                logger.info("从reasoning字段中提取到分析过程内容")

        # 如果还是没有找到，检查content字段是否包含分析过程内容而非结果
        if not has_reasoning_content and hasattr(result, 'content') and result.content:
            content = result.content
            # 检查内容是否符合分析过程的特征（而非结果）
            if '首先' in content and '分析' in content and not content.startswith('1.') and not content.startswith('#'):
                reasoning_content = content
                has_reasoning_content = True
                logger.info("从content字段中识别到分析过程内容")

        # 如果找到了reasoning_content，返回它
        if has_reasoning_content and reasoning_content:
            logger.info(f"成功提取到reasoning_content，内容长度: {len(reasoning_content)}")
            return jsonify({
                "success": True,
                "reasoning_content": reasoning_content,
                "source": "reasoning_content"
            })
        else:
            # 如果找不到，返回错误
            logger.warning("无法提取reasoning_content")
            return jsonify({
                "success": False,
                "error": "无法提取分析过程内容",
                "available_keys": list(metadata.keys()) if isinstance(metadata, dict) else "元数据不是字典"
            }), 404

    except Exception as e:
        logger.error(f"获取reasoning_content时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@analysis_process_bp.route('/api/analysis_processes/count')
def api_get_process_count():
    """获取分析过程记录总数的API。"""
    session = Session()
    try:
        count = session.query(AnalysisProcess).count()
        return jsonify({
            "success": True,
            "count": count
        })
    except Exception as e:
        logger.error(f"获取分析过程记录数时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@analysis_process_bp.route('/api/novels/<int:novel_id>/analysis_processes/cleanup', methods=['POST'])
def api_cleanup_analysis_processes(novel_id):
    """
    清理特定小说的分析过程记录API。
    用于减少数据库大小，但保留基本分析结果。

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 验证小说存在
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            return jsonify({
                "success": False,
                "error": f"找不到ID为{novel_id}的小说"
            }), 404

        # 获取要清理的记录数
        count = session.query(AnalysisProcess).filter_by(novel_id=novel_id).count()

        # 执行清理
        session.query(AnalysisProcess).filter_by(novel_id=novel_id).delete()
        session.commit()

        return jsonify({
            "success": True,
            "message": f"已清理小说 '{novel.title}' 的 {count} 条分析过程记录"
        })
    except Exception as e:
        session.rollback()
        logger.error(f"清理分析过程记录时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()