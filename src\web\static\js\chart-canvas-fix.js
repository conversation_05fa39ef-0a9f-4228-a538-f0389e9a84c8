/**
 * 九猫 - Chart.js Canvas修复脚本
 * 解决"Canvas is already in use"和"Failed to execute replaceChild on Node"错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('Chart.js Canvas修复脚本已加载');

    // 存储图表实例的全局对象
    if (!window.chartInstances) {
        window.chartInstances = {};
    }

    // 安全地销毁图表
    function safeDestroyChart(canvas) {
        try {
            // 尝试获取与canvas关联的图表实例
            if (typeof Chart !== 'undefined') {
                const existingChart = Chart.getChart(canvas);
                if (existingChart) {
                    console.log('销毁现有图表实例');
                    existingChart.destroy();
                    return true;
                }
            }

            // 检查全局存储的实例
            const canvasId = canvas.id;
            if (canvasId && window.chartInstances && window.chartInstances[canvasId]) {
                console.log(`销毁ID为 ${canvasId} 的存储图表实例`);
                window.chartInstances[canvasId].destroy();
                delete window.chartInstances[canvasId];
                return true;
            }

            // 检查维度属性
            const dimension = canvas.getAttribute('data-dimension');
            if (dimension && window.chartInstances && window.chartInstances[dimension]) {
                console.log(`销毁维度 ${dimension} 的存储图表实例`);
                window.chartInstances[dimension].destroy();
                delete window.chartInstances[dimension];
                return true;
            }

            return false;
        } catch (e) {
            console.error('销毁图表时出错:', e);
            return false;
        }
    }

    // 安全地创建图表
    function safeCreateChart(canvas, config) {
        try {
            // 先销毁现有图表
            safeDestroyChart(canvas);

            // 创建新图表
            const ctx = canvas.getContext('2d');
            const chart = new Chart(ctx, config);

            // 存储图表实例
            if (canvas.id) {
                window.chartInstances[canvas.id] = chart;
            }

            // 如果有维度属性，也存储
            const dimension = canvas.getAttribute('data-dimension');
            if (dimension) {
                window.chartInstances[dimension] = chart;
            }

            return chart;
        } catch (e) {
            console.error('创建图表时出错:', e);

            // 如果是Canvas已在使用的错误，尝试更激进的修复
            if (e.message && e.message.includes('Canvas is already in use')) {
                console.log('检测到Canvas已在使用错误，尝试更激进的修复');

                try {
                    // 创建新的canvas元素替换旧的
                    const oldCanvas = canvas;
                    const newCanvas = document.createElement('canvas');

                    // 复制所有属性
                    newCanvas.id = oldCanvas.id || '';
                    newCanvas.className = oldCanvas.className || '';
                    newCanvas.style.cssText = oldCanvas.style.cssText || '';
                    newCanvas.width = oldCanvas.width || 400;
                    newCanvas.height = oldCanvas.height || 300;

                    // 复制数据属性
                    Array.from(oldCanvas.attributes).forEach(attr => {
                        if (attr.name.startsWith('data-')) {
                            newCanvas.setAttribute(attr.name, attr.value);
                        }
                    });

                    // 安全替换canvas
                    try {
                        // 先尝试正常替换
                        oldCanvas.parentNode.replaceChild(newCanvas, oldCanvas);
                    } catch (replaceError) {
                        console.error('替换canvas时出错:', replaceError);

                        // 如果替换失败，尝试先移除旧节点，再添加新节点
                        try {
                            if (oldCanvas.parentNode) {
                                oldCanvas.parentNode.removeChild(oldCanvas);
                                oldCanvas.parentNode.appendChild(newCanvas);
                            }
                        } catch (nodeError) {
                            console.error('节点操作失败:', nodeError);

                            // 最后尝试，在旧canvas旁边添加新canvas，然后隐藏旧canvas
                            if (oldCanvas.parentNode) {
                                oldCanvas.style.display = 'none';
                                oldCanvas.parentNode.insertBefore(newCanvas, oldCanvas.nextSibling);
                            }
                        }
                    }

                    // 在新canvas上创建图表
                    const ctx = newCanvas.getContext('2d');
                    const chart = new Chart(ctx, config);

                    // 存储图表实例
                    if (newCanvas.id) {
                        window.chartInstances[newCanvas.id] = chart;
                    }

                    const dimension = newCanvas.getAttribute('data-dimension');
                    if (dimension) {
                        window.chartInstances[dimension] = chart;
                    }

                    return chart;
                } catch (e2) {
                    console.error('激进修复也失败:', e2);
                }
            }

            return null;
        }
    }

    // 修复所有图表
    function fixAllCharts() {
        console.log('尝试修复所有图表');

        // 查找所有canvas元素
        const canvases = document.querySelectorAll('canvas');
        console.log(`找到 ${canvases.length} 个canvas元素`);

        canvases.forEach(canvas => {
            try {
                // 销毁现有图表
                safeDestroyChart(canvas);

                // 获取维度信息
                const dimension = canvas.getAttribute('data-dimension');
                if (dimension) {
                    console.log(`尝试重新初始化维度 ${dimension} 的图表`);

                    // 尝试调用特定维度的初始化函数
                    const initFunctionName = `init${dimension.charAt(0).toUpperCase() + dimension.slice(1)}Chart`;
                    if (typeof window[initFunctionName] === 'function') {
                        window[initFunctionName]();
                    }
                }
            } catch (e) {
                console.error(`处理canvas时出错:`, e);
            }
        });

        // 如果有全局初始化函数，调用它
        if (typeof window.initCharts === 'function') {
            console.log('调用全局initCharts函数');
            window.initCharts();
        } else if (typeof window.initializeCharts === 'function') {
            console.log('调用全局initializeCharts函数');
            window.initializeCharts();
        }
    }

    // 修复replaceChild错误
    function fixReplaceChildError() {
        // 检查是否已经应用了修复
        if (window.__unexpectedIdentifierFixed) {
            console.log('replaceChild已被unexpected-identifier-fix.js修复，跳过');
            return;
        }

        // 保存原始的replaceChild方法
        const originalReplaceChild = Node.prototype.replaceChild;

        // 重写replaceChild方法
        Node.prototype.replaceChild = function(newChild, oldChild) {
            try {
                // 尝试使用原始方法
                return originalReplaceChild.call(this, newChild, oldChild);
            } catch (e) {
                console.error('replaceChild错误:', e.message);

                // 检查是否是Unexpected identifier错误
                if (e.message && e.message.includes('Unexpected identifier')) {
                    console.log('检测到Unexpected identifier错误，使用安全替换');

                    // 创建一个新的元素，而不是直接使用newChild
                    let safeNode;

                    if (newChild.nodeType === Node.ELEMENT_NODE) {
                        // 对于元素节点，创建相同类型的新元素
                        safeNode = document.createElement(newChild.tagName);

                        // 复制所有属性
                        for (let i = 0; i < newChild.attributes.length; i++) {
                            const attr = newChild.attributes[i];
                            safeNode.setAttribute(attr.name, attr.value);
                        }

                        // 复制内容
                        safeNode.innerHTML = newChild.innerHTML;
                    } else if (newChild.nodeType === Node.TEXT_NODE) {
                        // 对于文本节点，创建新的文本节点
                        safeNode = document.createTextNode(newChild.textContent);
                    } else {
                        // 对于其他类型的节点，创建一个空的div作为替代
                        safeNode = document.createElement('div');
                    }

                    // 移除旧节点并添加新节点
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }
                    this.appendChild(safeNode);

                    return safeNode;
                } else {
                    // 对于其他类型的错误，使用一般的安全替换
                    try {
                        if (this.contains(oldChild)) {
                            this.removeChild(oldChild);
                        }
                        this.appendChild(newChild);
                        return newChild;
                    } catch (e2) {
                        console.error('安全替换也失败:', e2.message);
                        throw e; // 重新抛出原始错误
                    }
                }
            }
        };
    }

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始执行Chart.js Canvas修复');

        // 修复replaceChild错误
        fixReplaceChildError();

        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                // 处理Chart.js相关错误
                if (event.error.message.includes('Chart with ID') ||
                    event.error.message.includes('Canvas is already in use')) {
                    console.error('捕获到Chart.js相关错误:', event.error.message);

                    // 尝试修复
                    setTimeout(fixAllCharts, 100);

                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }

                // 处理replaceChild错误
                if (event.error.message.includes('Failed to execute \'replaceChild\' on \'Node\'')) {
                    console.error('捕获到replaceChild错误:', event.error.message);

                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);
    });

    // 导出函数供其他模块使用
    window.safeCreateChart = safeCreateChart;
    window.safeDestroyChart = safeDestroyChart;
    window.fixAllCharts = fixAllCharts;
})();
