{% extends "base.html" %}

{% block title %}{{ novel.title }} - 九猫{% endblock %}

{% block head %}
{{ super() }}
<!-- 小说详情页面不停刷新修复脚本 - 最高优先级 -->
<script>
// 内联脚本，确保最先执行
(function() {
    console.log('[小说页面修复-内联] 初始化临时刷新阻止器');

    // 保存原始的reload方法
    var originalReload = window.location.reload;
    var originalAssign = window.location.assign;
    var originalReplace = window.location.replace;

    // 临时禁用reload方法，直到完整的修复脚本加载
    window.location.reload = function() {
        console.warn('[小说页面修复-内联] 拦截到页面刷新尝试');
        return false;
    };

    // 临时拦截跳转到首页的尝试
    window.location.assign = function(url) {
        if (url === '/' || url === '/index' || url === '/home') {
            console.warn('[小说页面修复-内联] 拦截到跳转到首页的尝试');
            return false;
        }
        return originalAssign.apply(this, arguments);
    };

    window.location.replace = function(url) {
        if (url === '/' || url === '/index' || url === '/home') {
            console.warn('[小说页面修复-内联] 拦截到跳转到首页的尝试');
            return false;
        }
        return originalReplace.apply(this, arguments);
    };

    // 清除所有可能的定时器
    for (var i = 1; i < 1000; i++) {
        try {
            clearTimeout(i);
            clearInterval(i);
        } catch (e) {}
    }

    // 获取当前小说ID并存储为全局变量，方便其他脚本使用
    try {
        var match = window.location.pathname.match(/\/novel\/(\d+)/);
        if (match && match[1]) {
            window.novelIdFromTemplate = match[1];
            console.log('[小说页面修复-内联] 当前小说ID: ' + window.novelIdFromTemplate);
        }
    } catch (e) {}

    console.log('[小说页面修复-内联] 临时刷新阻止器和跳转阻止器已启用');
})();
</script>

<!-- 小说页面专用修复脚本 -->
<script src="{{ url_for('static', filename='js/novel-page-refresh-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/novel-page-refresh-fix.js';"></script>
<script src="{{ url_for('static', filename='js/novel-page-redirect-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/novel-page-redirect-fix.js';"></script>
<script src="{{ url_for('static', filename='js/novel-link-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/novel-link-fix.js';"></script>
<script src="{{ url_for('static', filename='js/chapter-navigation-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/chapter-navigation-fix.js';"></script>
<script src="{{ url_for('static', filename='js/novel-navigation-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/novel-navigation-fix.js';"></script>

<!-- 新的分析结果显示样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/analysis-display.css') }}" onerror="this.onerror=null;this.href='/direct-static/css/analysis-display.css';">
{% endblock %}

{% block extra_css %}
<style>
    .novel-excerpt {
        max-height: 300px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        font-size: 0.9rem;
        white-space: pre-wrap;
    }

    .analysis-card {
        height: 100%;
    }

    .analysis-content {
        max-height: 200px;
        overflow-y: auto;
    }

    .analysis-progress {
        margin-top: 10px;
        margin-bottom: 15px;
    }

    .analysis-status {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        color: #666;
        margin-top: 5px;
    }

    .analysis-visualization {
        margin-top: 15px;
        height: 150px;
        background-color: #f8f9fa;
        border-radius: 5px;
        overflow: hidden;
    }

    .analysis-chart {
        width: 100%;
        height: 100%;
    }

    /* 控制台样式 */
    .console-output {
        background-color: #1e1e1e;
        color: #f0f0f0;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.85rem;
        height: 300px;
        overflow-y: auto;
        padding: 10px;
        border-radius: 0 0 5px 5px;
    }

    .console-inner {
        padding: 5px;
    }

    .console-welcome {
        color: #888;
        margin-bottom: 10px;
    }

    .console-line {
        margin: 2px 0;
        line-height: 1.4;
        white-space: pre-wrap;
        word-break: break-word;
    }

    .log-debug {
        color: #888;
    }

    .log-info {
        color: #4CAF50;
    }

    .log-warning {
        color: #FFC107;
    }

    .log-error {
        color: #F44336;
    }

    .log-timestamp {
        color: #888;
        margin-right: 5px;
    }

    .log-dimension {
        color: #2196F3;
        font-weight: bold;
        margin-right: 5px;
    }

    .log-progress {
        color: #9C27B0;
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 针对小说页面的应急修复 -->
<div id="extension-warning-container"></div>
<script>
(function() {
    // 立即运行，处理章节分析按钮
    function fixChapterAnalysisButtons() {
        try {
            // 获取当前小说ID - 直接从环境变量中获取
            const novelId = "{{ novel.id }}";
            console.log("[小说页面修复] 当前小说ID: " + novelId);

            // 存储到全局变量
            window.novelId = novelId;

            // 找到所有章节分析按钮
            const buttons = document.querySelectorAll('a.btn, button.btn');
            buttons.forEach(function(btn) {
                if (btn.textContent.includes('章节分析') ||
                    (btn.textContent.includes('章节') && btn.textContent.includes('分析'))) {
                    console.log("[小说页面修复] 找到章节分析按钮: " + btn.textContent.trim());

                    // 设置正确的href和onclick
                    if (btn.tagName === 'A') {
                        btn.href = "/novel/" + novelId + "/chapters";
                    }

                    // 保存原始onclick
                    var originalOnclick = btn.onclick;

                    // 覆盖onclick
                    btn.onclick = function(e) {
                        console.log("[小说页面修复] 章节分析按钮被点击");
                        e.preventDefault();
                        e.stopPropagation();

                        // 延迟执行跳转，避免被其他脚本拦截
                        setTimeout(function() {
                            window.location.href = "/novel/" + novelId + "/chapters";
                        }, 10);

                        return false;
                    };

                    console.log("[小说页面修复] 章节分析按钮已修复");
                }
            });
        } catch (e) {
            console.error("[小说页面修复] 修复章节分析按钮时出错:", e);
        }
    }

    // 页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixChapterAnalysisButtons);
    } else {
        fixChapterAnalysisButtons();
    }

    // 加载浏览器扩展警告组件
    function loadExtensionWarning() {
        try {
            fetch("{{ url_for('static', filename='js/extension-warning.html') }}")
                .then(response => response.text())
                .then(html => {
                    document.getElementById('extension-warning-container').innerHTML = html;
                })
                .catch(err => {
                    console.error("[小说页面修复] 加载扩展警告组件失败:", err);
                });
        } catch (e) {
            console.error("[小说页面修复] 加载扩展警告组件时出错:", e);
        }
    }

    // 加载扩展警告组件
    setTimeout(loadExtensionWarning, 1000);
})();
</script>

{% if novel.id == 41 %}
<!-- 针对小说41的特殊修复脚本 -->
<script src="{{ url_for('static', filename='js/novel-41-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-41-fix.js';"></script>
{% endif %}

<!-- replaceChild 修复脚本 - 最高优先级 -->
<script src="{{ url_for('static', filename='js/replace-child-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/replace-child-fix.js';"></script>

<!-- 加载统一错误处理脚本，处理所有错误 -->
<script src="{{ url_for('static', filename='js/unified-error-handler.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/unified-error-handler.js';"></script>

<!-- 维度容器修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-container-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-container-fix.js';"></script>

<!-- 维度状态修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-status-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-status-fix.js';"></script>

<!-- 分析完成修复脚本 - 解决13个维度全部完成时分析结果不可见的问题 -->
<script src="{{ url_for('static', filename='js/analysis-complete-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-complete-fix.js';"></script>

<!-- 小说与章节分析显示修复脚本 -->
<script src="{{ url_for('static', filename='js/novel-chapter-display-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-chapter-display-fix.js';"></script>

<div class="row"
     data-novel-id="{{ novel.id }}"
     data-novel-title="{{ novel.title }}"
     id="novel-container">
    <div class="col-md-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item active">{{ novel.title }}</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ novel.title }}</h1>
            <div>
                <a href="{{ url_for('chapter_fix.direct_chapters', novel_id=novel.id) }}" class="btn btn-success me-2 chapter-analysis-btn" data-novel-id="{{ novel.id }}" onclick="console.log('[小说详情页面] 点击章节分析按钮');">
                    <i class="fas fa-book-open"></i> 章节分析
                </a>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                    分析小说
                </button>
                <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                    删除
                </button>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">小说信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>作者：</strong> {{ novel.author or '未知' }}</p>
                        <p><strong>字数：</strong> {{ novel.word_count }}</p>
                        <p><strong>上传时间：</strong> {{ novel.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">小说摘要</h5>
                    </div>
                    <div class="card-body">
                        <div class="novel-excerpt">
                            {{ novel.get_excerpt(2000) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="mt-5">分析结果</h2>
                    <div>
                        <a href="{{ url_for('chapter_fix.direct_summary', novel_id=novel.id) }}" class="btn btn-success me-2 chapter-summary-btn" data-novel-id="{{ novel.id }}" onclick="console.log('[小说详情页面] 点击章节分析汇总按钮');">
                            <i class="fas fa-list-alt"></i> 章节分析汇总
                        </a>
                        <button type="button" class="btn btn-info me-2" id="syncToChaptersBtn">
                            <i class="fas fa-sync-alt"></i> 同步到章节
                        </button>
                        <button type="button" class="btn btn-warning me-2" id="aggregateChaptersBtn">
                            <i class="fas fa-compress-alt"></i> 汇总章节分析
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                            分析小说
                        </button>
                    </div>
                </div>

                <!-- 添加控制台日志显示区域 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center bg-dark text-white">
                        <h5 class="mb-0">分析控制台</h5>
                        <div>
                            <select class="form-select form-select-sm" id="console-log-level">
                                <option value="all">所有级别</option>
                                <option value="info">信息</option>
                                <option value="warning">警告</option>
                                <option value="error">错误</option>
                                <option value="debug">调试</option>
                            </select>
                            <button class="btn btn-sm btn-outline-light ms-2" id="clear-console">清空</button>
                            <button class="btn btn-sm btn-outline-light ms-2" id="copy-console">复制</button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="analysis-console" class="console-output">
                            <div class="console-inner">
                                <div class="console-welcome">
                                    <span class="text-muted">// 分析控制台已准备就绪，等待分析开始...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载指示器 -->
                <div id="loadingIndicator"></div>

                <!-- 分析状态卡片 -->
                <div class="card analysis-status-card mt-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="mb-0">分析状态</h3>
                            <div>
                                {% if completed_dimensions_count == total_dimensions_count %}
                                    <span class="badge bg-success p-2 analysis-status-badge">{{ completed_dimensions_count }}/{{ total_dimensions_count }} 分析已完成</span>
                                {% else %}
                                    <span class="badge bg-warning p-2 analysis-status-badge">{{ completed_dimensions_count }}/{{ total_dimensions_count }} 分析未完成</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="dimensions-status-table">
                                <thead class="table-light">
                                    <tr>
                                        <th>维度</th>
                                        <th>状态</th>
                                        <th>进度</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 13个维度的行 -->
                                    {% for dimension in [
                                        {'key': 'language_style', 'name': '语言风格'},
                                        {'key': 'rhythm_pacing', 'name': '节奏与节奏'},
                                        {'key': 'structure', 'name': '结构分析'},
                                        {'key': 'sentence_variation', 'name': '句式变化'},
                                        {'key': 'paragraph_length', 'name': '段落长度'},
                                        {'key': 'perspective_shifts', 'name': '视角转换'},
                                        {'key': 'paragraph_flow', 'name': '段落流畅度'},
                                        {'key': 'novel_characteristics', 'name': '小说特点'},
                                        {'key': 'world_building', 'name': '世界构建'},
                                        {'key': 'chapter_outline', 'name': '章纲分析'},
                                        {'key': 'character_relationships', 'name': '人物关系'},
                                        {'key': 'opening_effectiveness', 'name': '开篇效果'},
                                        {'key': 'climax_pacing', 'name': '高潮节奏'}
                                    ] %}
                                    <tr data-dimension="{{ dimension.key }}">
                                        <td>{{ dimension.name }}</td>
                                        <td class="dimension-status"><span class="badge bg-secondary">未知</span></td>
                                        <td class="dimension-progress">
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" style="width: 0%;"
                                                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                            </div>
                                        </td>
                                        <td class="dimension-actions">
                                            <button class="btn btn-sm btn-outline-primary analyze-btn"
                                                    data-dimension="{{ dimension.key }}"
                                                    data-novel-id="{{ novel.id }}">
                                                分析
                                            </button>
                                            <a href="/novel/{{ novel.id }}/analysis/{{ dimension.key }}"
                                               class="btn btn-sm btn-outline-secondary ms-1 view-btn"
                                               data-dimension="{{ dimension.key }}"
                                               data-novel-id="{{ novel.id }}">
                                                查看
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 分析结果部分 -->
                <div class="analysis-section mt-4" id="analysis-section-container">
                    <h3 class="mb-3">分析结果</h3>

                    {% if analysis_results %}
                        <div class="row">
                            {% for dimension, result in analysis_results.items() %}
                                <div class="col-md-4 mb-4">
                                    <div class="card analysis-card" data-dimension="{{ dimension }}">
                                        <div class="card-header">
                                            <h5 class="card-title">
                                                {% if dimension == 'language_style' %}
                                                    语言风格
                                                {% elif dimension == 'rhythm_pacing' %}
                                                    节奏与节奏
                                                {% elif dimension == 'structure' %}
                                                    结构分析
                                                {% elif dimension == 'sentence_variation' %}
                                                    句式变化
                                                {% elif dimension == 'paragraph_length' %}
                                                    段落长度
                                                {% elif dimension == 'perspective_shifts' %}
                                                    视角转换
                                                {% elif dimension == 'paragraph_flow' %}
                                                    段落流畅度
                                                {% elif dimension == 'novel_characteristics' %}
                                                    小说特点
                                                {% elif dimension == 'world_building' %}
                                                    世界构建
                                                {% elif dimension == 'chapter_outline' %}
                                                    章纲分析
                                                {% elif dimension == 'character_relationships' %}
                                                    人物关系
                                                {% elif dimension == 'opening_effectiveness' %}
                                                    开篇效果
                                                {% elif dimension == 'climax_pacing' %}
                                                    高潮节奏
                                                {% else %}
                                                    {{ dimension }}
                                                {% endif %}
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            {% if result %}
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span class="badge bg-success">分析完成</span>
                                                    <a href="/novel/{{ novel.id }}/analysis/{{ dimension }}"
                                                       class="btn btn-sm btn-outline-primary view-analysis-btn"
                                                       data-novel-id="{{ novel.id }}"
                                                       data-dimension="{{ dimension }}">
                                                        查看详情
                                                    </a>
                                                </div>
                                                <div class="analysis-excerpt mt-2">
                                                    {% if result is mapping %}
                                                        {% if result.content|length > 300 %}
                                                            {{ (result.content[:300] + "...")|safe }}
                                                        {% else %}
                                                            {{ result.content|safe }}
                                                        {% endif %}
                                                    {% else %}
                                                        {% if result.get_excerpt is defined %}
                                                            {{ result.get_excerpt(300)|safe }}
                                                        {% else %}
                                                            {% if result.content|length > 300 %}
                                                                {{ (result.content[:300] + "...")|safe }}
                                                            {% else %}
                                                                {{ result.content|safe }}
                                                            {% endif %}
                                                        {% endif %}
                                                    {% endif %}
                                                </div>
                                            {% elif dimension in analysis_in_progress %}
                                                <div class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">加载中...</span>
                                                    </div>
                                                    <p class="mt-2">正在分析中，请稍候...</p>
                                                    <div class="progress mt-3">
                                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                             role="progressbar"
                                                             id="progress-{{ dimension }}"
                                                             aria-valuenow="{{ analysis_in_progress[dimension].get('progress', 0) }}"
                                                             aria-valuemin="0"
                                                             aria-valuemax="100"
                                                             data-progress="{{ max(10, analysis_in_progress[dimension].get('progress', 0)) }}">
                                                            {{ analysis_in_progress[dimension].get('progress', 0) }}%
                                                        </div>
                                                    </div>
                                                </div>
                                            {% else %}
                                                <div class="text-center py-3">
                                                    <span class="badge bg-secondary mb-3">未分析</span>
                                                    <p class="text-muted">尚未对该维度进行分析</p>
                                                    <button class="btn btn-sm btn-primary analyze-single-dimension"
                                                            data-dimension="{{ dimension }}"
                                                            data-novel-id="{{ novel.id }}">
                                                        开始分析
                                                    </button>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            尚未进行任何分析。点击"分析小说"按钮开始分析，或者使用上方的维度表格选择特定维度进行分析。
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析模态框 -->
<div class="modal fade" id="analyzeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择分析维度</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('analyze_novel', novel_id=novel.id) }}" id="analyze-form" data-novel-id="{{ novel.id }}" onsubmit="return validateAnalyzeForm(this);">
                <div class="modal-body">
                    <p>请选择要分析的维度：</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="select-all-dimensions">
                        <label class="form-check-label" for="select-all-dimensions">
                            <strong>全选</strong>
                        </label>
                    </div>
                    <hr>
                    <!-- 维度列表将由JavaScript动态生成 -->
                    <!-- 这里是一个空的占位符，JavaScript会在这里添加维度列表 -->
                    <div class="alert alert-warning mt-3">
                        <small>注意：分析过程可能需要较长时间，尤其是对于长篇小说。</small>
                    </div>

                    <div class="form-check form-switch mt-3">
                        <input class="form-check-input" type="checkbox" id="parallel-analysis" name="parallel_analysis" checked>
                        <label class="form-check-label" for="parallel-analysis">
                            <strong>启用并行分析</strong> (同时分析多个维度，提高效率)
                        </label>
                    </div>

                    <div class="form-check form-switch mt-2">
                        <input class="form-check-input" type="checkbox" id="use-cache" name="use_cache" checked>
                        <label class="form-check-label" for="use-cache">
                            <strong>使用缓存结果</strong> (如果有缓存，直接使用，避免重复分析)
                        </label>
                    </div>

                    <div class="form-check form-switch mt-2">
                        <input class="form-check-input" type="checkbox" id="force-refresh" name="force_refresh">
                        <label class="form-check-label" for="force-refresh">
                            <strong>强制刷新缓存</strong> (忽略现有缓存，重新分析)
                        </label>
                    </div>

                    <div class="mt-4">
                        <label for="model-select" class="form-label"><strong>选择分析模型</strong></label>
                        <select class="form-select" id="model-select" name="model">
                            <option value="deepseek-r1" selected>阿里云 DeepSeek R1 (默认)</option>
                            <option value="qwen-plus-latest">阿里云 通义千问-Plus-Latest</option>
                        </select>
                        <div class="form-text">选择不同的模型可能会产生不同的分析结果</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">开始分析</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除小说"{{ novel.title }}"吗？此操作不可撤销，所有相关的分析结果也将被删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form method="POST" action="{{ url_for('delete_novel', novel_id=novel.id) }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 同步到章节模态框 -->
<div class="modal fade" id="syncToChaptersModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">同步到章节</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>此操作将把整本书的分析结果同步到各个章节，使章节分析页面显示分析结果。</p>
                <p>请选择要同步的维度：</p>

                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="sync-all-dimensions" checked>
                    <label class="form-check-label" for="sync-all-dimensions">
                        <strong>所有维度</strong>
                    </label>
                </div>

                <div id="sync-dimensions-list" class="ms-3">
                    <!-- 维度列表将由JavaScript动态生成 -->
                </div>

                <div class="alert alert-info mt-3">
                    <small>注意：同步操作会为每个章节创建分析结果记录，可能需要一些时间。</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmSyncBtn">开始同步</button>
            </div>
        </div>
    </div>
</div>

<!-- 汇总章节分析模态框 -->
<div class="modal fade" id="aggregateChaptersModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">汇总章节分析</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>此操作将把所有章节的分析结果汇总，创建一个整本书的汇总分析结果。</p>
                <p>请选择要汇总的维度：</p>

                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="aggregate-all-dimensions" checked>
                    <label class="form-check-label" for="aggregate-all-dimensions">
                        <strong>所有维度</strong>
                    </label>
                </div>

                <div id="aggregate-dimensions-list" class="ms-3">
                    <!-- 维度列表将由JavaScript动态生成 -->
                </div>

                <div class="alert alert-info mt-3">
                    <small>注意：汇总操作只会汇总章节特定的分析结果（即对章节单独进行分析的结果），不会汇总从整本书同步到章节的分析结果。</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAggregateBtn">开始汇总</button>
            </div>
        </div>
    </div>
</div>

{% if novel.id == 41 %}
<!-- 小说41特殊处理，确保章节分析按钮能够正常跳转 -->
<script>
(function() {
    // 当页面加载完成时执行
    function fixNovel41Links() {
        console.log('[小说41修复] 修复章节分析按钮');
        const buttons = document.querySelectorAll('a, button');

        buttons.forEach(function(btn) {
            // 检查是否是章节分析按钮
            if (btn.textContent &&
                btn.textContent.toLowerCase().includes('章节') &&
                btn.textContent.toLowerCase().includes('分析')) {

                // 修复链接
                if (btn.tagName === 'A') {
                    btn.href = '/novel/41/chapters';
                }

                // 绑定点击事件
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('[小说41修复] 点击章节分析按钮，跳转到: /novel/41/chapters');
                    window.location.href = '/novel/41/chapters';
                    return false;
                }, true);
            }
        });
    }

    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixNovel41Links);
    } else {
        // 如果页面已经加载完成，立即执行修复
        fixNovel41Links();
    }

    // 使用setTimeout代替setInterval，减少性能消耗
    setTimeout(fixNovel41Links, 1000);
    setTimeout(fixNovel41Links, 3000);
    setTimeout(fixNovel41Links, 5000);
})();
</script>
{% endif %}
{% endblock %}

{% block extra_js %}
<!-- 表单验证函数和按钮点击处理 -->
<script>
// 处理查看详情按钮点击
document.addEventListener('DOMContentLoaded', function() {
    // 查找所有查看详情按钮
    const viewButtons = document.querySelectorAll('.view-analysis-btn, .view-btn');

    // 为每个按钮添加点击事件
    viewButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 获取维度和小说ID
            const dimension = this.getAttribute('data-dimension');
            const novelId = this.getAttribute('data-novel-id');

            if (!dimension || !novelId) {
                console.error('按钮缺少维度或小说ID');
                return;
            }

            // 构建URL
            const url = '/novel/' + novelId + '/analysis/' + dimension;

            console.log('点击查看详情按钮，跳转到: ' + url);

            // 跳转到详情页面
            window.location.href = url;
        });
    });

    // 处理章节分析按钮
    const chapterButtons = document.querySelectorAll('.chapter-analysis-btn');
    chapterButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 获取小说ID
            const novelId = this.getAttribute('data-novel-id');

            if (!novelId) {
                console.error('按钮缺少小说ID');
                return;
            }

            // 构建URL
            const url = '/novel/' + novelId + '/chapters';

            console.log('点击章节分析按钮，跳转到: ' + url);

            // 跳转到章节分析页面
            window.location.href = url;
        });
    });

    // 处理章节分析汇总按钮
    const chapterSummaryButtons = document.querySelectorAll('.chapter-summary-btn');
    chapterSummaryButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 获取小说ID
            const novelId = this.getAttribute('data-novel-id');

            if (!novelId) {
                console.error('按钮缺少小说ID');
                return;
            }

            // 构建URL
            const url = '/novel/' + novelId + '/chapters/summary';

            console.log('点击章节分析汇总按钮，跳转到: ' + url);

            // 跳转到章节分析汇总页面
            window.location.href = url;
        });
    });
});

function validateAnalyzeForm(form) {
    console.log('验证分析表单');

    // 获取小说ID
    const novelId = form.getAttribute('data-novel-id');
    if (!novelId) {
        console.error('表单缺少小说ID');
        alert('表单缺少小说ID，无法提交');
        return false;
    }

    // 确保表单action包含小说ID
    const action = form.getAttribute('action');
    if (!action.includes('/novel/' + novelId)) {
        const newAction = '/novel/' + novelId + '/analyze';
        console.log('修复表单action: ' + action + ' -> ' + newAction);
        form.setAttribute('action', newAction);
    }

    // 检查是否选择了至少一个维度
    const dimensionCheckboxes = form.querySelectorAll('input[type="checkbox"][name="dimensions"]');
    let hasSelectedDimension = false;

    dimensionCheckboxes.forEach(function(checkbox) {
        if (checkbox.checked) {
            hasSelectedDimension = true;
        }
    });

    if (!hasSelectedDimension) {
        alert('请至少选择一个维度进行分析');
        return false;
    }

    console.log('表单验证通过，提交表单');
    return true;
}
</script>

<!-- 预先定义分析结果数据 -->
<div id="analysis-data" style="display:none;" data-analysis='{{ analysis_results_js|tojson_safe }}'></div>
<script>
    // 安全地定义全局分析结果数据
    (function() {
        try {
            var dataElement = document.getElementById('analysis-data');
            var analysisDataStr = dataElement ? dataElement.getAttribute('data-analysis') : '{}';
            window.analysisResultsData = JSON.parse(analysisDataStr) || {};
            console.log('成功加载分析数据');

            // 检查是否所有维度都已完成分析
            function checkAllDimensionsCompleted() {
                try {
                    // 查找状态徽章
                    const statusBadges = document.querySelectorAll('.badge');
                    for (const badge of statusBadges) {
                        if (badge.textContent.includes('13/13 分析已完成')) {
                            console.log('检测到所有维度已完成分析');
                            return true;
                        }
                    }
                    return false;
                } catch (e) {
                    console.error('检查所有维度是否完成时出错:', e);
                    return false;
                }
            }

            // 如果所有维度都已完成分析，尝试重新获取所有维度的数据
            function fetchAllDimensionsData() {
                const novelIdMatch = window.location.pathname.match(/\/novel\/(\d+)/);
                if (novelIdMatch && novelIdMatch[1]) {
                    const novelId = novelIdMatch[1];
                    console.log(`尝试使用新的API /api/novel/${novelId}/analysis/all 重新获取所有维度的数据`);
                    fetch(`/api/novel/${novelId}/analysis/all`) // <--- 修改后的 API 端点
                        .then(response => {
                            if (!response.ok) {
                                console.error(`API请求失败: ${response.status} ${response.statusText}`);
                                // 尝试读取错误响应体
                                return response.json().then(errData => {
                                    throw new Error(`API Error: ${response.status} - ${errData.error || 'Unknown error'}`);
                                }).catch(() => {
                                    // 如果响应体不是JSON或为空，抛出通用错误
                                    throw new Error(`API Error: ${response.status} ${response.statusText}`);
                                });
                            }
                            return response.json();
                        })
                        .then(results => {
                            if (Array.isArray(results)) { // 新API直接返回数组
                                console.log(`成功获取 ${results.length} 个维度的数据`);
                                // 清空旧数据，确保完全替换
                                window.analysisResultsData = {};
                                results.forEach(result => {
                                    if (result && result.dimension) {
                                        window.analysisResultsData[result.dimension] = result;
                                    }
                                });

                                // 更新所有卡片
                                if (window.updateDimensionCard && typeof window.updateDimensionCard === 'function') {
                                    console.log('调用 window.updateDimensionCard 更新所有卡片');
                                    Object.values(window.analysisResultsData).forEach(result => {
                                        if (result && result.dimension) {
                                            window.updateDimensionCard(result.dimension, result);
                                        }
                                    });
                                } else {
                                    console.warn('window.updateDimensionCard 函数未定义或不可用');
                                }

                                // 尝试调用 fixAllEmptyCards 以确保UI一致性
                                if (window.dimensionContentFix && window.dimensionContentFix.fixAllEmptyCards && typeof window.dimensionContentFix.fixAllEmptyCards === 'function') {
                                    console.log('调用 window.dimensionContentFix.fixAllEmptyCards');
                                    window.dimensionContentFix.fixAllEmptyCards();
                                }

                            } else {
                                console.warn('从API获取的维度数据格式不正确，期望得到一个数组:', results);
                            }
                        })
                        .catch(error => {
                            console.error('获取所有维度数据时出错:', error.message || error);
                        });
                } else {
                    console.error('无法从URL中提取 novelId');
                }
            }

            // 在页面加载完成后自动修复所有空白卡片
            document.addEventListener('DOMContentLoaded', function() {
                // 延迟执行，确保所有脚本都已加载
                setTimeout(function() {
                    console.log('页面加载完成，尝试修复所有空白卡片');

                    // 检查是否所有维度都已完成分析
                    const allCompleted = checkAllDimensionsCompleted();

                    if (allCompleted) {
                        console.log('检测到所有维度已完成分析，尝试重新获取所有维度的数据');
                        fetchAllDimensionsData();

                        // 设置定期检查
                        setInterval(function() {
                            console.log('定期检查维度卡片');
                            if (window.dimensionContentFix && window.dimensionContentFix.fixAllEmptyCards) {
                                window.dimensionContentFix.fixAllEmptyCards();
                            }
                        }, 5000);
                    } else {
                        // 如果不是所有维度都已完成，只修复空白卡片
                        if (window.dimensionContentFix && window.dimensionContentFix.fixAllEmptyCards) {
                            window.dimensionContentFix.fixAllEmptyCards();
                        }
                    }
                }, 1000);
            });
        } catch (e) {
            console.error('初始化分析数据时出错:', e);
            window.analysisResultsData = {};
        }
    })();
</script>

<!-- 同步和汇总功能脚本 -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 获取小说ID
        const novelId = document.getElementById('novel-container').dataset.novelId;

        // 同步到章节按钮点击事件
        document.getElementById('syncToChaptersBtn').addEventListener('click', function() {
            // 获取已分析的维度
            const analyzedDimensions = [];
            document.querySelectorAll('.analysis-card').forEach(card => {
                const dimension = card.dataset.dimension;
                // 检查是否已分析（有成功标记）
                if (card.querySelector('.badge.bg-success')) {
                    analyzedDimensions.push({
                        key: dimension,
                        name: card.querySelector('.card-title').textContent.trim()
                    });
                }
            });

            // 如果没有已分析的维度，显示提示
            if (analyzedDimensions.length === 0) {
                alert('没有找到已分析的维度，请先进行小说分析。');
                return;
            }

            // 生成维度选择列表
            const dimensionsList = document.getElementById('sync-dimensions-list');
            dimensionsList.innerHTML = '';

            analyzedDimensions.forEach(dimension => {
                const checkboxDiv = document.createElement('div');
                checkboxDiv.className = 'form-check mb-1';
                checkboxDiv.innerHTML = `
                    <input class="form-check-input dimension-checkbox" type="checkbox" id="sync-${dimension.key}"
                           value="${dimension.key}" checked>
                    <label class="form-check-label" for="sync-${dimension.key}">
                        ${dimension.name}
                    </label>
                `;
                dimensionsList.appendChild(checkboxDiv);
            });

            // 全选/取消全选功能
            document.getElementById('sync-all-dimensions').addEventListener('change', function() {
                const isChecked = this.checked;
                document.querySelectorAll('.dimension-checkbox').forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
            });

            // 显示模态框
            const syncModal = new bootstrap.Modal(document.getElementById('syncToChaptersModal'));
            syncModal.show();

            // 确认同步按钮点击事件
            document.getElementById('confirmSyncBtn').addEventListener('click', function() {
                // 获取选中的维度
                const selectedDimensions = [];
                document.querySelectorAll('.dimension-checkbox:checked').forEach(checkbox => {
                    selectedDimensions.push(checkbox.value);
                });

                // 如果没有选中的维度，显示提示
                if (selectedDimensions.length === 0) {
                    alert('请至少选择一个维度进行同步。');
                    return;
                }

                // 禁用按钮，显示加载状态
                const confirmBtn = document.getElementById('confirmSyncBtn');
                const originalText = confirmBtn.textContent;
                confirmBtn.disabled = true;
                confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 同步中...';

                // 调用API进行同步
                const syncAll = document.getElementById('sync-all-dimensions').checked;
                const dimension = syncAll ? null : selectedDimensions[0]; // 如果选择了多个维度但不是全选，只使用第一个

                fetch(`/api/novel/${novelId}/sync_to_chapters`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dimension: dimension
                    })
                })
                .then(response => {
                    // 检查响应状态
                    if (!response.ok) {
                        throw new Error(`服务器返回错误状态码: ${response.status}`);
                    }

                    // 尝试解析JSON响应
                    return response.json()
                        .catch(error => {
                            // 如果解析JSON失败，可能是因为返回了HTML而不是JSON
                            if (error.message && (error.message.includes("Unexpected token '<'") ||
                                                 error.message.includes('<!DOCTYPE') ||
                                                 error.message.includes('is not valid JSON'))) {
                                console.error('同步出错：服务器返回HTML而不是JSON', error.message);
                                throw new Error('同步出错：服务器返回HTML而不是JSON响应，可能是API地址错误或服务器发生异常');
                            }
                            throw error;
                        });
                })
                .then(data => {
                    // 处理HTML-as-JSON错误（由我们的html-response-fix.js脚本处理的情况）
                    if (data && data.html_response === true) {
                        throw new Error(`同步出错：${data.message || '服务器返回HTML而不是JSON响应'}`);
                    }

                    // 恢复按钮状态
                    confirmBtn.disabled = false;
                    confirmBtn.textContent = originalText;

                    // 关闭模态框
                    syncModal.hide();

                    // 显示结果
                    if (data.success) {
                        // 添加到控制台日志
                        const consoleInner = document.querySelector('#analysis-console .console-inner');
                        const logLine = document.createElement('div');
                        logLine.className = 'console-line log-info';

                        const timestamp = new Date().toLocaleTimeString();
                        const stats = data.stats;

                        logLine.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> <span class="log-dimension">同步完成</span> 成功同步 ${stats.total_dimensions} 个维度到 ${stats.total_chapters} 个章节，创建了 ${stats.created_results} 个新结果，更新了 ${stats.updated_results} 个现有结果。`;

                        consoleInner.appendChild(logLine);
                        consoleInner.scrollTop = consoleInner.scrollHeight;

                        // 显示成功提示
                        alert(`同步成功！\n已将 ${stats.total_dimensions} 个维度的分析结果同步到 ${stats.total_chapters} 个章节。`);
                    } else {
                        // 添加到控制台日志
                        const consoleInner = document.querySelector('#analysis-console .console-inner');
                        const logLine = document.createElement('div');
                        logLine.className = 'console-line log-error';

                        const timestamp = new Date().toLocaleTimeString();
                        logLine.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> <span class="log-dimension">同步失败</span> ${data.error}`;

                        consoleInner.appendChild(logLine);
                        consoleInner.scrollTop = consoleInner.scrollHeight;

                        // 显示错误提示
                        alert(`同步失败：${data.error}`);
                    }
                })
                .catch(error => {
                    // 恢复按钮状态
                    confirmBtn.disabled = false;
                    confirmBtn.textContent = originalText;

                    // 关闭模态框
                    syncModal.hide();

                    // 添加到控制台日志
                    const consoleInner = document.querySelector('#analysis-console .console-inner');
                    const logLine = document.createElement('div');
                    logLine.className = 'console-line log-error';

                    const timestamp = new Date().toLocaleTimeString();
                    logLine.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> <span class="log-dimension">同步错误</span> ${error.message}`;

                    consoleInner.appendChild(logLine);
                    consoleInner.scrollTop = consoleInner.scrollHeight;

                    // 显示错误提示
                    alert(`同步出错：${error.message}`);
                });
            }, { once: true }); // 只绑定一次事件，避免重复点击
        });

        // 汇总章节分析按钮点击事件
        document.getElementById('aggregateChaptersBtn').addEventListener('click', function() {
            // 获取所有维度
            const allDimensions = [
                { key: 'language_style', name: '语言风格' },
                { key: 'rhythm_pacing', name: '节奏与节奏' },
                { key: 'structure', name: '结构分析' },
                { key: 'sentence_variation', name: '句式变化' },
                { key: 'paragraph_length', name: '段落长度' },
                { key: 'perspective_shifts', name: '视角转换' },
                { key: 'paragraph_flow', name: '段落流畅度' },
                { key: 'novel_characteristics', name: '小说特点' },
                { key: 'world_building', name: '世界构建' },
                { key: 'chapter_outline', name: '章纲分析' },
                { key: 'character_relationships', name: '人物关系' },
                { key: 'opening_effectiveness', name: '开篇效果' },
                { key: 'climax_pacing', name: '高潮节奏' }
            ];

            // 生成维度选择列表
            const dimensionsList = document.getElementById('aggregate-dimensions-list');
            dimensionsList.innerHTML = '';

            allDimensions.forEach(dimension => {
                const checkboxDiv = document.createElement('div');
                checkboxDiv.className = 'form-check mb-1';
                checkboxDiv.innerHTML = `
                    <input class="form-check-input aggregate-dimension-checkbox" type="checkbox" id="aggregate-${dimension.key}"
                           value="${dimension.key}" checked>
                    <label class="form-check-label" for="aggregate-${dimension.key}">
                        ${dimension.name}
                    </label>
                `;
                dimensionsList.appendChild(checkboxDiv);
            });

            // 全选/取消全选功能
            document.getElementById('aggregate-all-dimensions').addEventListener('change', function() {
                const isChecked = this.checked;
                document.querySelectorAll('.aggregate-dimension-checkbox').forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
            });

            // 显示模态框
            const aggregateModal = new bootstrap.Modal(document.getElementById('aggregateChaptersModal'));
            aggregateModal.show();

            // 确认汇总按钮点击事件
            document.getElementById('confirmAggregateBtn').addEventListener('click', function() {
                // 获取选中的维度
                const selectedDimensions = [];
                document.querySelectorAll('.aggregate-dimension-checkbox:checked').forEach(checkbox => {
                    selectedDimensions.push(checkbox.value);
                });

                // 如果没有选中的维度，显示提示
                if (selectedDimensions.length === 0) {
                    alert('请至少选择一个维度进行汇总。');
                    return;
                }

                // 禁用按钮，显示加载状态
                const confirmBtn = document.getElementById('confirmAggregateBtn');
                const originalText = confirmBtn.textContent;
                confirmBtn.disabled = true;
                confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 汇总中...';

                // 调用API进行汇总
                const aggregateAll = document.getElementById('aggregate-all-dimensions').checked;
                const dimension = aggregateAll ? null : selectedDimensions[0]; // 如果选择了多个维度但不是全选，只使用第一个

                fetch(`/api/novel/${novelId}/aggregate_chapters`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dimension: dimension
                    })
                })
                .then(response => {
                    // 检查响应状态
                    if (!response.ok) {
                        throw new Error(`服务器返回错误状态码: ${response.status}`);
                    }

                    // 尝试解析JSON响应
                    return response.json()
                        .catch(error => {
                            // 如果解析JSON失败，可能是因为返回了HTML而不是JSON
                            if (error.message && (error.message.includes("Unexpected token '<'") ||
                                                 error.message.includes('<!DOCTYPE') ||
                                                 error.message.includes('is not valid JSON'))) {
                                console.error('汇总出错：服务器返回HTML而不是JSON', error.message);
                                throw new Error('汇总出错：服务器返回HTML而不是JSON响应，可能是API地址错误或服务器发生异常');
                            }
                            throw error;
                        });
                })
                .then(data => {
                    // 处理HTML-as-JSON错误（由我们的html-response-fix.js脚本处理的情况）
                    if (data && data.html_response === true) {
                        throw new Error(`汇总出错：${data.message || '服务器返回HTML而不是JSON响应'}`);
                    }

                    // 恢复按钮状态
                    confirmBtn.disabled = false;
                    confirmBtn.textContent = originalText;

                    // 关闭模态框
                    aggregateModal.hide();

                    // 显示结果
                    if (data.success) {
                        // 添加到控制台日志
                        const consoleInner = document.querySelector('#analysis-console .console-inner');
                        const logLine = document.createElement('div');
                        logLine.className = 'console-line log-info';

                        const timestamp = new Date().toLocaleTimeString();
                        const stats = data.stats;

                        logLine.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> <span class="log-dimension">汇总完成</span> 成功汇总 ${stats.total_dimensions} 个维度的章节分析结果，创建了 ${stats.created_results} 个新结果，更新了 ${stats.updated_results} 个现有结果。`;

                        consoleInner.appendChild(logLine);
                        consoleInner.scrollTop = consoleInner.scrollHeight;

                        // 显示成功提示
                        alert(`汇总成功！\n已将 ${stats.total_dimensions} 个维度的章节分析结果汇总到整本书。\n点击确定后可手动刷新页面查看汇总结果。`);

                        // 创建通知元素，提供手动刷新选项
                        const notification = document.createElement('div');
                        notification.style.cssText = 'position: fixed; bottom: 20px; right: 20px; background-color: #28a745; color: white; padding: 15px; border-radius: 5px; z-index: 10000; box-shadow: 0 2px 10px rgba(0,0,0,0.2);';
                        notification.innerHTML = `
                            <div style="font-weight: bold; margin-bottom: 10px;">✅ 汇总已完成</div>
                            <p style="margin-bottom: 10px;">章节分析结果已成功汇总，点击刷新按钮查看结果。</p>
                            <button id="manual-refresh-btn" style="padding: 5px 10px; background: white; color: #28a745; border: none; border-radius: 3px; cursor: pointer;">刷新页面</button>
                        `;

                        // 添加到页面
                        document.body.appendChild(notification);

                        // 添加刷新按钮事件
                        document.getElementById('manual-refresh-btn').addEventListener('click', function() {
                            // 使用原始的reload方法刷新页面
                            if (window.novelPageRefreshFix && typeof window.novelPageRefreshFix.initialize === 'function') {
                                // 先禁用刷新阻止器
                                window._disableRefreshBlocker = true;
                            }

                            // 然后刷新页面
                            window.location.reload();
                        });

                        // 10秒后自动关闭
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                        }, 10000);
                    } else {
                        // 添加到控制台日志
                        const consoleInner = document.querySelector('#analysis-console .console-inner');
                        const logLine = document.createElement('div');
                        logLine.className = 'console-line log-error';

                        const timestamp = new Date().toLocaleTimeString();
                        logLine.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> <span class="log-dimension">汇总失败</span> ${data.error}`;

                        consoleInner.appendChild(logLine);
                        consoleInner.scrollTop = consoleInner.scrollHeight;

                        // 显示错误提示
                        alert(`汇总失败：${data.error}`);
                    }
                })
                .catch(error => {
                    // 恢复按钮状态
                    confirmBtn.disabled = false;
                    confirmBtn.textContent = originalText;

                    // 关闭模态框
                    aggregateModal.hide();

                    // 添加到控制台日志
                    const consoleInner = document.querySelector('#analysis-console .console-inner');
                    const logLine = document.createElement('div');
                    logLine.className = 'console-line log-error';

                    const timestamp = new Date().toLocaleTimeString();
                    logLine.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> <span class="log-dimension">汇总错误</span> ${error.message}`;

                    consoleInner.appendChild(logLine);
                    consoleInner.scrollTop = consoleInner.scrollHeight;

                    // 显示错误提示
                    alert(`汇总出错：${error.message}`);
                });
            }, { once: true }); // 只绑定一次事件，避免重复点击
        });
    });
</script>

<!-- 图表禁用脚本 - 最高优先级 -->
<script src="{{ url_for('static', filename='js/disable-charts.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/disable-charts.js';"></script>

<!-- 图表错误抑制器 - 抑制与图表相关的错误消息 -->
<script src="{{ url_for('static', filename='js/chart-error-suppressor.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chart-error-suppressor.js';"></script>

<!-- 图表功能已完全禁用，节省系统资源 -->

<!-- 直接修复脚本 - 必须在所有其他脚本之前加载 -->
<script src="{{ url_for('static', filename='js/direct-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/direct-fix.js';"></script>

<!-- 最终修复脚本 - 最高优先级 -->
<script src="{{ url_for('static', filename='js/final-fix-position-4476.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/final-fix-position-4476.js';"></script>

<!-- 位置4476专用修复脚本 - 最高优先级 -->
<script src="{{ url_for('static', filename='js/json-fix-position-4476.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/json-fix-position-4476.js';"></script>

<!-- 高级JSON修复脚本 - 处理位置4476和6499的特定错误 -->
<script src="{{ url_for('static', filename='js/advanced-json-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/advanced-json-fix.js';"></script>

<!-- 高级JSON修复增强脚本 - 专门处理位置4476的错误 -->
<script src="{{ url_for('static', filename='js/advanced-json-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/advanced-json-fix-enhanced.js';"></script>

<!-- 专门修复novel/4页面的脚本 -->
<script src="{{ url_for('static', filename='js/novel-4-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-4-fix.js';"></script>

<!-- 专门修复novel页面的脚本 -->
<script src="{{ url_for('static', filename='js/novel-page-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-page-fix.js';"></script>

<!-- 控制台日志修复脚本 -->
<script src="{{ url_for('static', filename='js/console-logger-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/console-logger-fix.js';"></script>

<!-- 增强版JSON.parse错误修复脚本 -->
<script src="{{ url_for('static', filename='js/json-parse-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/json-parse-fix-enhanced.js';"></script>

<!-- 图表初始化脚本已禁用 -->

<!-- 分析维度显示修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-display-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-display-fix.js';"></script>

<!-- 分析维度选择修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-selection-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-selection-fix.js';"></script>

<!-- 章节汇总分析页面修复脚本 -->
<script src="{{ url_for('static', filename='js/chapter-summary-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-summary-fix.js';"></script>

<!-- 分析对话框修复脚本 -->
<script src="{{ url_for('static', filename='js/analyze-modal-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analyze-modal-fix.js';"></script>

<!-- 内联脚本：维度选择紧急修复 -->
<script>
// 维度选择紧急修复内联脚本
(function() {
    console.log('维度选择紧急修复内联脚本已加载');

    // 维度名称映射
    const DIMENSION_NAMES = {
        'language_style': '语言风格',
        'rhythm_pacing': '节奏与节奏',
        'structure': '结构分析',
        'sentence_variation': '句式变化',
        'paragraph_length': '段落长度',
        'perspective_shifts': '视角转换',
        'paragraph_flow': '段落流畅度',
        'novel_characteristics': '小说特点',
        'world_building': '世界构建',
        'chapter_outline': '章纲分析',
        'character_relationships': '人物关系',
        'opening_effectiveness': '开篇效果',
        'climax_pacing': '高潮节奏'
    };

    // 所有维度列表
    const ALL_DIMENSIONS = Object.keys(DIMENSION_NAMES);

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        // 监听模态框打开事件
        const modal = document.getElementById('analyzeModal');
        if (modal) {
            modal.addEventListener('shown.bs.modal', function() {
                console.log('模态框已打开，检查维度列表');
                setTimeout(function() {
                    const checkboxes = document.querySelectorAll('.dimension-checkbox');
                    if (checkboxes.length === 0) {
                        console.log('维度列表为空，尝试修复');
                        // 查找分隔线
                        const hr = modal.querySelector('hr');
                        if (hr) {
                            // 查找警告提示
                            const warningAlert = modal.querySelector('.alert-warning');

                            // 创建维度列表容器
                            const dimensionsContainer = document.createElement('div');
                            dimensionsContainer.className = 'dimensions-container';

                            // 创建新的维度复选框
                            ALL_DIMENSIONS.forEach((dimension, index) => {
                                const container = document.createElement('div');
                                container.className = 'form-check';

                                const checkbox = document.createElement('input');
                                checkbox.className = 'form-check-input dimension-checkbox';
                                checkbox.type = 'checkbox';
                                checkbox.name = 'dimensions';
                                checkbox.value = dimension;
                                checkbox.id = `dimension-${index + 1}`;

                                const label = document.createElement('label');
                                label.className = 'form-check-label';
                                label.setAttribute('for', `dimension-${index + 1}`);
                                label.textContent = DIMENSION_NAMES[dimension] || dimension;

                                container.appendChild(checkbox);
                                container.appendChild(label);

                                dimensionsContainer.appendChild(container);
                            });

                            // 将维度列表容器插入到分隔线和警告提示之间
                            hr.parentNode.insertBefore(dimensionsContainer, warningAlert);

                            console.log('维度列表修复完成');

                            // 绑定全选事件
                            const selectAll = document.getElementById('select-all-dimensions');
                            if (selectAll) {
                                selectAll.addEventListener('change', function() {
                                    const isChecked = this.checked;
                                    document.querySelectorAll('.dimension-checkbox').forEach(checkbox => {
                                        checkbox.checked = isChecked;
                                    });
                                });
                            }
                        }
                    } else {
                        console.log('维度列表不为空，无需修复');
                    }
                }, 100);
            });
        }
    });
})();
</script>

<script>
    // 从HTML元素获取模板变量
    const novelContainer = document.getElementById('novel-container');
    const novelIdFromTemplate = novelContainer ? novelContainer.getAttribute('data-novel-id') : null;
    const novelTitleFromTemplate = novelContainer ? novelContainer.getAttribute('data-novel-title') : null;

    // 定义全局novelId变量，避免ReferenceError
    let novelId;
    try {
        novelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
    } catch (e) {
        console.error('解析novelId出错:', e);
        novelId = novelIdFromTemplate;
    }

    // 全局变量容器，防止变量命名冲突
    if (!window.NineCats) {
        window.NineCats = {
            containers: {},
            variables: {},
            counters: {}
        };
    }

    // 初始化容器计数器
    if (!window.NineCats.counters.containerCounter) {
        window.NineCats.counters.containerCounter = 0;
    }

    // 创建一个生成唯一容器变量名的函数
    function createUniqueContainerName(prefix) {
        const uniqueId = ++window.NineCats.counters.containerCounter;
        const containerName = `${prefix || 'container'}_${uniqueId}`;
        return containerName;
    }

    // 脚本加载函数
    function loadScript(url, fallbacks, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;

        // 成功加载
        script.onload = function() {
            console.log('成功加载脚本: ' + url);
            if (callback) callback(null, url);
        };

        // 加载失败，尝试备用URL
        script.onerror = function() {
            console.warn('脚本加载失败: ' + url);
            if (fallbacks && fallbacks.length > 0) {
                var nextUrl = fallbacks.shift();
                console.log('尝试备用URL: ' + nextUrl);
                loadScript(nextUrl, fallbacks, callback);
            } else {
                console.error('所有URL都加载失败');
                if (callback) callback(new Error('所有URL都加载失败'), null);
            }
        };

        document.head.appendChild(script);
    }

    // 加载分析页面所需的JS文件
    function loadAnalysisScripts() {

        // 加载Chart.js加载器
        loadScript(
            "{{ url_for('static', filename='js/chart-loader.js') }}",
            ['/direct-static/js/chart-loader.js'],
            function(error, url) {
                if (error) {
                    console.error('Chart.js加载器加载失败: ' + error);
                } else {
                    console.log('Chart.js加载器加载成功');
                }
            }
        );

        // 加载控制台日志JS
        loadScript(
            "{{ url_for('static', filename='js/console-logger.js') }}",
            ['/direct-static/js/console-logger.js'],
            function(error, url) {
                if (error) {
                    console.error('控制台日志JS加载失败: ' + error);
                    return;
                }

                console.log('控制台日志JS加载成功');

                // 如果jQuery已加载，初始化控制台日志
                if (typeof jQuery !== 'undefined' && typeof initConsoleLogger === 'function') {
                    try {
                        initConsoleLogger(novelIdFromTemplate, novelTitleFromTemplate);
                        console.log('控制台日志初始化成功');
                    } catch (e) {
                        console.error('初始化控制台日志时出错: ' + e);
                    }
                } else {
                    console.warn('jQuery或控制台日志函数未加载，无法初始化控制台');
                }
            }
        );

        // 加载小说分析JS
        loadScript(
            "{{ url_for('static', filename='js/novel-analysis.js') }}",
            ['/direct-static/js/novel-analysis.js'],
            function(error, url) {
                if (error) {
                    console.error('小说分析JS加载失败: ' + error);
                    return;
                }

                console.log('小说分析JS加载成功');

                // 图表功能已禁用，不再初始化图表
            }
        );
    }

    // 当DOM加载完成后，加载分析脚本
    document.addEventListener('DOMContentLoaded', function() {
        // 检查jQuery是否已加载
        if (typeof jQuery !== 'undefined') {
            loadAnalysisScripts();
        } else {
            // 如果jQuery未加载，等待一段时间后再次检查
            console.log('jQuery未加载，等待加载...');
            setTimeout(function checkJQuery() {
                if (typeof jQuery !== 'undefined') {
                    console.log('jQuery已加载，开始加载分析脚本');
                    loadAnalysisScripts();
                } else {
                    console.log('jQuery仍未加载，继续等待...');
                    setTimeout(checkJQuery, 500);
                }
            }, 500);
        }
    });

    // 错误处理函数，防止JS错误导致整个页面失效
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('捕获到JS错误:', message, '在行:', lineno, '列:', colno);
        // 防止错误中断其他功能
        return true;
    };

    // 初始化所有进度条宽度
    function initProgressBars() {
        console.log('初始化所有进度条宽度');
        const progressBars = document.querySelectorAll('.progress-bar[data-progress]');
        console.log('找到进度条元素数量:', progressBars.length);
        progressBars.forEach(bar => {
            const progress = parseInt(bar.getAttribute('data-progress') || '0');
            const minDisplay = parseInt(bar.getAttribute('data-min-display') || '10');
            if (progress !== null) {
                // 确保进度至少为最小显示值，以便用户能看到进度条
                const displayProgress = Math.max(progress, minDisplay);
                console.log(`设置进度条宽度: ${progress}% -> ${displayProgress}%`);
                bar.style.width = displayProgress + '%';
                // 确保文本内容也更新
                bar.textContent = `${progress}%`;
            }
        });
    }

    // 检查是否需要自动刷新页面 - 已禁用自动刷新
    function checkAutoRefresh() {
        // 获取所有进度条
        const progressBars = document.querySelectorAll('.progress-bar[role="progressbar"]');
        let allCompleted = true;

        // 检查是否所有进度条都已完成
        progressBars.forEach(bar => {
            const progress = parseInt(bar.getAttribute('aria-valuenow') || '0');
            if (progress < 100) {
                allCompleted = false;
            }
        });

        // 如果所有进度条都已完成，且页面上有进度条，显示通知而不是自动刷新
        if (allCompleted && progressBars.length > 0) {
            console.log('所有分析已完成，显示通知');

            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = 'position: fixed; bottom: 20px; right: 20px; background-color: #28a745; color: white; padding: 15px; border-radius: 5px; z-index: 10000; box-shadow: 0 2px 10px rgba(0,0,0,0.2);';
            notification.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 10px;">✅ 分析已完成</div>
                <p style="margin-bottom: 10px;">所有分析任务已完成，点击刷新按钮查看结果。</p>
                <button id="manual-refresh-btn" style="padding: 5px 10px; background: white; color: #28a745; border: none; border-radius: 3px; cursor: pointer;">刷新页面</button>
            `;

            // 添加到页面
            document.body.appendChild(notification);

            // 添加刷新按钮事件
            document.getElementById('manual-refresh-btn').addEventListener('click', function() {
                // 使用原始的reload方法刷新页面
                if (window.novelPageRefreshFix && typeof window.novelPageRefreshFix.initialize === 'function') {
                    // 先禁用刷新阻止器
                    window._disableRefreshBlocker = true;
                }

                // 然后刷新页面
                window.location.reload();
            });

            // 10秒后自动关闭
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 10000);
        }
    }

    // 全局变量，用于存储分析结果数据
    // 注意：analysisResultsData在下面已经定义，这里不需要重复定义

    // 确保jQuery已加载后再执行
    function initializeJQueryFunctions() {
        if (typeof jQuery === 'undefined') {
            console.log('jQuery未加载，等待加载...');
            setTimeout(initializeJQueryFunctions, 500);
            return;
        }

        console.log('jQuery已加载，初始化jQuery功能');

        // 确保novelId变量正确
        let currentNovelId;
        try {
            currentNovelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
        } catch (e) {
            console.error('解析currentNovelId出错:', e);
            currentNovelId = novelIdFromTemplate;
        }

        // 全选按钮逻辑
        $('#select-all-dimensions').change(function() {
            const isChecked = $(this).prop('checked');
            $('.dimension-checkbox').prop('checked', isChecked);
        });

        // 表单提交事件
        $('#analyze-form').submit(function(e) {
            e.preventDefault();

            const dimensions = [];
            $('.dimension-checkbox:checked').each(function() {
                dimensions.push($(this).val());
            });

            if (dimensions.length === 0) {
                alert('请至少选择一个分析维度');
                return;
            }

            // 关闭模态框
            $('#analyzeModal').modal('hide');

            // 开始分析
            startAnalysis(currentNovelId, dimensions);
        });
    }

    // 当DOM加载完成后，初始化jQuery功能
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeJQueryFunctions);
    } else {
        // 如果DOM已加载完成，直接初始化
        initializeJQueryFunctions();
    }

        // 开始分析函数
        function startAnalysis(novelId, dimensions) {
            console.log(`开始分析小说 ${novelId}，维度:`, dimensions);

            // 显示加载状态
            dimensions.forEach(dimension => {
                // 查找或创建维度卡片
                let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                if (!card) {
                    card = createDimensionCard(dimension);
                }

                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    if (cardBody) {
                        // 显示进度条
                        cardBody.innerHTML = `
                            <div class="analysis-progress">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        id="progress-${dimension}"
                                        aria-valuenow="10"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        data-progress="10"
                                        data-min-display="10"
                                        style="width: 10%">
                                        10%
                                    </div>
                                </div>
                                <div class="analysis-status">
                                    <span>分析中...</span>
                                    <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                                </div>
                                <div class="analysis-visualization mt-3">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">正在分析中，请稍候...</p>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }
            });

            // 确保URL不包含任何非ASCII字符，使用干净的URL
            const cleanUrl = `/api/novels/${novelId}/analyze`;

            console.log(`发送分析请求到: ${cleanUrl}，维度: ${dimensions.join(', ')}`);

            // 发送API请求
            fetch(cleanUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache'
                },
                body: JSON.stringify({
                    dimensions: dimensions,
                    parallel_analysis: true,
                    use_cache: document.getElementById('use-cache').checked,
                    force_refresh: document.getElementById('force-refresh').checked,
                    model: document.getElementById('model-select').value
                }),
            })
            .then(response => {
                if (!response.ok) {
                    console.error(`服务器响应错误: ${response.status} ${response.statusText}`);
                    return response.text().then(text => {
                        console.error(`错误响应内容: ${text}`);
                        throw new Error(`服务器响应错误: ${response.status} ${response.statusText}, 详情: ${text}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('分析请求响应:', data);

                if (data.success) {
                    // 启动进度更新
                    updateAnalysisProgress();

                    // 设置一个定时器，定期检查分析结果是否已保存到数据库
                    dimensions.forEach(dimension => {
                        // 每5秒检查一次分析结果
                        const checkResultInterval = setInterval(() => {
                            console.log(`检查维度 ${dimension} 的分析结果...`);
                            // 添加时间戳防止缓存
                            const timestamp = new Date().getTime();

                            console.log(`获取分析结果: /api/novels/${novelId}/analysis/${dimension}?_=${timestamp}`);

                            fetch(`/api/novels/${novelId}/analysis/${dimension}?_=${timestamp}`, {
                                headers: {
                                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                                    'Pragma': 'no-cache'
                                }
                            })
                                .then(response => {
                                    console.log(`维度 ${dimension} 的分析结果请求状态: ${response.status}`);
                                    if (response.ok) {
                                        // 如果成功获取到结果，清除定时器
                                        console.log(`维度 ${dimension} 的分析结果已保存到数据库`);
                                        clearInterval(checkResultInterval);
                                        return response.json();
                                    } else if (response.status === 404 || response.status === 202) {
                                        // 如果是404错误或202状态码，继续等待
                                        console.log(`维度 ${dimension} 的分析结果尚未保存到数据库，继续等待`);
                                        // 尝试解析响应内容，可能包含进度信息
                                        return response.json().catch(e => {
                                            console.log(`无法解析响应内容为JSON: ${e.message}`);
                                            return null;
                                        });
                                    } else {
                                        console.error(`获取维度 ${dimension} 的分析结果失败: HTTP ${response.status}`);
                                        return response.text().then(text => {
                                            console.error(`错误响应内容: ${text}`);
                                            throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                                        });
                                    }
                                })
                                .then(resultData => {
                                    if (resultData && resultData.success) {
                                        // 更新卡片显示结果
                                        updateDimensionCard(dimension, resultData);
                                    }
                                })
                                .catch(error => {
                                    console.error(`检查维度 ${dimension} 的分析结果时出错:`, error);
                                });
                        }, 5000); // 每5秒检查一次

                        // 60秒后自动清除定时器，避免无限期运行
                        setTimeout(() => {
                            clearInterval(checkResultInterval);
                        }, 60000);
                    });
                } else {
                    alert('分析启动失败: ' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('分析请求错误:', error);
                alert('请求错误: ' + error.message);
            });
        }

        // 开始单个维度分析函数
        function startSingleDimensionAnalysis(novelIdParam, dimension) {
            // 使用传入的novelId参数，如果没有则使用全局novelId
            const idToUse = novelIdParam || novelId;
            console.log(`开始分析小说 ${idToUse} 的维度 ${dimension}`);
            // 直接调用包含完整错误处理的 startAnalysis
            startAnalysis(idToUse, [dimension]);
            /* 删除旧的 fetch 调用，因为它在 startAnalysis 中重复了
            fetch(`/api/novels/${novelId}/analyze`, {
                // ... fetch options ...
            })
            .then(response => response.json())
            .then(data => {
                // ... success handling ...
            })
            .catch(error => {
                console.error('分析请求错误:', error);
                 // 记录更详细的错误信息
                console.error('错误详情:', { message: error.message, stack: error.stack });
                alert('请求错误: ' + error.message);
            });
            */
        }

        // 单个维度分析按钮点击事件
        $('.analyze-single-dimension').click(function() {
            const dimension = $(this).data('dimension');
            console.log(`点击了维度 ${dimension} 的分析按钮`);
            // 使用全局novelId变量，而不是currentNovelId
            startSingleDimensionAnalysis(novelId, dimension);
        });

        // 检查是否有分析进度数据，更新进度条
        // 添加时间戳防止缓存
        const progressTimestamp = new Date().getTime();

        fetch(`/api/analysis/progress?novel_id=${novelId}&_=${progressTimestamp}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('加载页面时检查分析进度:', data);

                // 如果有进度数据且成功获取
                if (data.success && data.progress && Object.keys(data.progress).length > 0) {
                    console.log('有分析进度数据，开始初始化进度条');

                    // 过滤出未完成的分析
                    const inProgressDimensions = {};
                    let hasInProgressAnalysis = false;

                    for (const dimension in data.progress) {
                        if (!data.progress[dimension].completed && data.progress[dimension].progress < 100) {
                            inProgressDimensions[dimension] = data.progress[dimension];
                            hasInProgressAnalysis = true;
                        } else {
                            console.log(`维度 ${dimension} 已完成，不再显示进度条`);
                        }
                    }

                    // 如果有正在进行的分析，只显示这些分析的进度条
                    if (hasInProgressAnalysis) {
                        console.log('有正在进行的分析，只显示这些分析的进度条');
                        data.progress = inProgressDimensions;
                    }

                    // 显示所有进行中的分析
                    for (const dimension in data.progress) {
                        const progress = data.progress[dimension].progress;
                        console.log(`维度 ${dimension} 当前进度: ${progress}%`);

                        // 查找或创建维度卡片
                        let card = document.querySelector('.analysis-card[data-dimension="' + dimension + '"]');
                        if (!card) {
                            card = createDimensionCard(dimension);
                        }

                        if (card) {
                            const cardBody = card.querySelector('.card-body');

                            // 创建进度条
                            if (progress < 100 && progress >= 0) {
                                // 确保进度至少为10%，以便用户能看到进度条
                                const displayProgress = Math.max(progress, 10);
                                console.log(`为维度 ${dimension} 创建进度条，进度: ${progress}%, 显示进度: ${displayProgress}%`);

                                cardBody.innerHTML = `
                                    <div class="analysis-progress">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                role="progressbar"
                                                id="progress-${dimension}"
                                                aria-valuenow="${progress}"
                                                aria-valuemin="0"
                                                aria-valuemax="100"
                                                data-progress="${progress}"
                                                data-min-display="10"
                                                style='width: ${displayProgress}%'>
                                                ${progress}%
                                            </div>
                                        </div>
                                        <div class="analysis-status">
                                            <span>分析中...</span>
                                            <span id="time-${dimension}">预计剩余时间: ${data.progress[dimension].estimated_time}</span>
                                        </div>
                                        <div class="analysis-visualization mt-3">
                                            <div class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <p class="mt-2">正在分析中，请稍候...</p>
                                            </div>
                                        </div>
                                    </div>
                                `;

                                // 确认进度条已创建并设置了正确的宽度
                                setTimeout(() => {
                                    const progressBar = document.getElementById(`progress-${dimension}`);
                                    if (progressBar) {
                                        console.log(`确认维度 ${dimension} 的进度条宽度: ${progressBar.style.width}`);
                                    }
                                }, 100);
                            }
                        }
                    }

                    // 启动进度更新
                    updateAnalysisProgress();
                } else {
                    console.log('没有正在进行的分析');
                }
            })
            .catch(error => {
                console.error('获取分析进度时出错:', error);
                // 记录更详细的错误信息
                console.error('错误详情:', { message: error.message, stack: error.stack });
            });

        // 获取已完成的分析结果并显示
        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();

        fetch(`/api/novels/${novelId}/analysis?_=${timestamp}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(results => {
                console.log('获取分析结果数据:', results);

                // 如果获取到了新的分析结果，更新全局变量
                if (results && Array.isArray(results) && results.length > 0) {
                    console.log('API返回的分析结果数据:', results);

                    // 将结果转换为以维度为键的对象
                    const newResultsData = {};
                    results.forEach(result => {
                        if (result && result.dimension) {
                            newResultsData[result.dimension] = result;
                            console.log(`处理维度 ${result.dimension} 的结果:`, {
                                contentType: typeof result.content,
                                contentLength: result.content ? result.content.length : 0,
                                metadataType: typeof result.metadata,
                                metadataKeys: result.metadata ? Object.keys(result.metadata) : []
                            });
                        }
                    });

                    // 更新全局变量
                    if (Object.keys(newResultsData).length > 0) {
                        console.log('更新全局分析结果数据:', newResultsData);
                        window.analysisResultsData = newResultsData;
                    }
                }

                // 更新每个维度的卡片
                results.forEach(result => {
                    if (result && result.dimension) {
                        const dimension = result.dimension;
                        console.log(`尝试更新维度 ${dimension} 的卡片`);

                        // 如果找不到卡片，可能还没有创建
                        let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                        if (!card) {
                            console.log(`为完成的分析结果创建维度 ${dimension} 的卡片`);
                            card = createDimensionCard(dimension);
                        }

                        // 使用更新卡片函数
                        updateDimensionCard(dimension, result);
                    }
                });

                // 初始化所有图表
                setTimeout(initializeCharts, 500);
            })
            .catch(error => {
                console.error('获取分析结果时出错:', error);
                // 记录更详细的错误信息
                console.error('错误详情:', { message: error.message, stack: error.stack });
            });

        // 初始化进度条
        initProgressBars();

        // 检查是否需要自动刷新页面
        setTimeout(checkAutoRefresh, 5000);

    // 创建分析维度卡片的函数
    function createDimensionCard(dimension) {
        console.log(`为维度 ${dimension} 创建新卡片 - 开始`);

        // 首先检查是否已存在该维度的卡片（使用多种方式查找）
        let existingCard = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);

        // 如果找不到，尝试使用ID查找
        if (!existingCard) {
            existingCard = document.getElementById(`card-${dimension}`);
        }

        // 如果找到了现有卡片，直接返回
        if (existingCard) {
            console.log(`维度 ${dimension} 的卡片已存在，直接返回现有卡片`);
            return existingCard;
        }

        console.log(`维度 ${dimension} 的卡片不存在，创建新卡片`);

        // 创建卡片容器
        const cardCol = document.createElement('div');
        cardCol.className = 'col-md-4 mb-4';
        cardCol.id = `card-container-${dimension}`;

        // 格式化维度名称为更友好的显示格式
        let displayName = formatDimensionName(dimension);

        // 卡片HTML
        cardCol.innerHTML = `
            <div class="card analysis-card" data-dimension="${dimension}" id="card-${dimension}">
                <div class="card-header">
                    <h5 class="card-title">${displayName}</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">准备分析中...</p>
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        console.log(`维度 ${dimension} 的卡片HTML已创建`);

        // 将卡片添加到分析结果容器中
        let resultsContainer = null;

        // 首先尝试查找已有的结果行容器
        resultsContainer = document.getElementById('analysis-results-row');
        if (resultsContainer) {
            console.log(`找到已有的分析结果行容器 #analysis-results-row`);
        } else {
            // 尝试查找任何包含卡片的行容器
            const existingCards = document.querySelectorAll('.analysis-card');
            if (existingCards.length > 0) {
                // 找到第一个卡片的父元素的父元素（行容器）
                const firstCardParent = existingCards[0].closest('.row');
                if (firstCardParent) {
                    console.log(`找到现有卡片的行容器`);
                    resultsContainer = firstCardParent;
                    // 给容器添加ID以便后续查找
                    if (!firstCardParent.id) {
                        firstCardParent.id = 'analysis-results-row';
                        console.log(`为现有行容器添加ID: analysis-results-row`);
                    }
                }
            }
        }

        // 如果仍然找不到容器，创建一个新的分析结果行容器
        if (!resultsContainer) {
            console.log(`未找到现有的结果容器，创建新容器`);

            // 创建一个新的行容器
            const newRow = document.createElement('div');
            newRow.className = 'row mt-4';
            newRow.id = 'analysis-results-row';

            // 查找分析部分
            const analysisSection = document.querySelector('.analysis-section');
            if (analysisSection) {
                console.log(`找到分析部分，在其后添加结果行容器`);
                analysisSection.insertAdjacentElement('afterend', newRow);
                resultsContainer = newRow;
            } else {
                // 如果找不到分析部分，查找分析结果标题
                const analysisHeading = document.querySelector('h2.mt-5');
                if (analysisHeading) {
                    console.log(`找到分析结果标题，在其后添加结果行容器`);
                    analysisHeading.insertAdjacentElement('afterend', newRow);
                    resultsContainer = newRow;
                } else {
                    // 如果还是找不到，尝试找其他可能的父元素
                    const contentSection = document.querySelector('.col-md-12');
                    if (contentSection) {
                        console.log(`找到内容部分，添加结果行容器`);
                        contentSection.appendChild(newRow);
                        resultsContainer = newRow;
                    } else {
                        console.error(`无法找到合适的位置添加分析结果行容器`);
                        // 作为最后手段，添加到body
                        document.body.appendChild(newRow);
                        resultsContainer = newRow;
                    }
                }
            }

            console.log(`创建了新的分析结果行容器: #${newRow.id}`);
        }

        // 添加卡片到容器
        if (resultsContainer) {
            console.log(`将卡片添加到容器中: ${resultsContainer.id || '未命名容器'}`);
            resultsContainer.appendChild(cardCol);

            // 确认卡片已添加到DOM
            setTimeout(() => {
                const addedCard = document.getElementById(`card-${dimension}`);
                if (addedCard) {
                    console.log(`确认维度 ${dimension} 的卡片已成功添加到DOM`);
                } else {
                    console.error(`维度 ${dimension} 的卡片未能成功添加到DOM`);
                }
            }, 50);

            // 获取并返回卡片元素
            const card = cardCol.querySelector('.analysis-card');
            if (card) {
                console.log(`维度 ${dimension} 的卡片创建成功`);
                return card;
            }
        }

        console.error(`维度 ${dimension} 的卡片创建失败`);
        return null;
    }

    // 启动多维度分析
    function startAnalysis(novelIdParam, dimensions) {
        // 使用传入的novelId参数，如果没有则使用全局novelId
        const idToUse = novelIdParam || novelId;
        // 添加时间戳防止缓存
        const analyzeTimestamp = new Date().getTime();

        fetch(`/api/novels/${idToUse}/analyze?_=${analyzeTimestamp}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: JSON.stringify({
                dimensions: dimensions,
                parallel_analysis: document.getElementById('parallel-analysis')
                    ? document.getElementById('parallel-analysis').checked
                    : true,
                use_cache: document.getElementById('use-cache')
                    ? document.getElementById('use-cache').checked
                    : true,
                force_refresh: document.getElementById('force-refresh')
                    ? document.getElementById('force-refresh').checked
                    : false
            }),
        })
        .then(response => {
            if (!response.ok) {
                console.error(`服务器响应错误: ${response.status} ${response.statusText}`);
                return response.text().then(text => {
                    console.error(`错误响应内容: ${text}`);
                    throw new Error(`服务器响应错误: ${response.status} ${response.statusText}, 详情: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('分析成功启动，创建进度条');

                // 为每个维度创建进度条
                dimensions.forEach(dimension => {
                    // 使用data-dimension属性查找对应的卡片
                    let card = document.querySelector('.analysis-card[data-dimension="' + dimension + '"]');
                    console.log(`查找维度 ${dimension} 的卡片元素，使用data-dimension属性`);

                    // 如果卡片不存在，创建它
                    if (!card) {
                        card = createDimensionCard(dimension);
                    }

                    if (card) {
                        const cardBody = card.querySelector('.card-body');
                        console.log(`找到维度 ${dimension} 的卡片元素`);

                        if (cardBody) {
                            console.log(`为维度 ${dimension} 创建进度条`);

                            // 创建进度条HTML
                            cardBody.innerHTML = `
                                <div class="analysis-progress">
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                            role="progressbar"
                                            id="progress-${dimension}"
                                            aria-valuenow="0"
                                            aria-valuemin="0"
                                            aria-valuemax="100"
                                            data-progress="0"
                                            data-min-display="10"
                                            style="width: 10%">
                                            10%
                                        </div>
                                    </div>
                                    <div class="analysis-status">
                                        <span>分析中...</span>
                                        <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                                    </div>
                                    <div class="analysis-visualization mt-3">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">正在分析中，请稍候...</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                    } else {
                        console.error(`无法找到维度 ${dimension} 的卡片元素`);
                    }
                });

                // 启动进度更新
                updateAnalysisProgress();
            } else {
                alert('分析启动失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('分析请求错误:', error);
            alert('请求错误: ' + error.message);
        });
    }

    // 分析结果数据已在novel-analysis.js中定义为全局变量
    // 确保全局变量存在
    if (typeof window.analysisResultsData === 'undefined') {
        window.analysisResultsData = {};
    }

    // 分析数据已经在页面顶部通过data属性加载
    // 这里只需要记录和显示数据
    console.log('使用已加载的分析结果数据');

    // 显示更详细的数据结构信息
    try {
        if (window.analysisResultsData && typeof window.analysisResultsData === 'object') {
            for (const dimension in window.analysisResultsData) {
                if (window.analysisResultsData.hasOwnProperty(dimension)) {
                    const resultData = window.analysisResultsData[dimension];
                    console.log(`维度 ${dimension} 的数据:`, {
                        dimension: dimension,
                        content: resultData.content ? (resultData.content.substring(0, 50) + '...') : '无内容',
                        metadata: resultData.metadata,
                        hasVisualization: resultData.metadata && resultData.metadata.visualization_data ? '有可视化数据' : '无可视化数据'
                    });
                }
            }
        } else {
            console.log('没有找到分析结果数据或数据格式不正确');
        }
    } catch (e) {
        console.error('处理分析结果数据时出错:', e);
    }

    // 初始化所有图表
    function initializeCharts() {
        console.log('初始化所有图表');

        // 确保Chart.js已加载
        if (window.chartLoader) {
            window.chartLoader.ensureLoaded(function() {
                console.log('Chart.js已加载，开始初始化图表');
                initializeChartsWithChartJS();
            });
        } else {
            console.warn('chartLoader不可用，尝试直接初始化图表');
            // 检查Chart.js是否已加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载，开始初始化图表');
                initializeChartsWithChartJS();
            } else {
                console.error('Chart.js未加载，尝试加载Chart.js');
                // 尝试加载Chart.js
                var chartScript = document.createElement('script');
                chartScript.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js';

                chartScript.onload = function() {
                    console.log('成功从CDN加载Chart.js，开始初始化图表');
                    initializeChartsWithChartJS();
                };

                chartScript.onerror = function() {
                    console.error('无法加载Chart.js，图表初始化失败');
                };

                document.head.appendChild(chartScript);
            }
        }
    }

    // 在Chart.js已加载的情况下初始化图表
    function initializeChartsWithChartJS() {
        const chartElements = document.querySelectorAll('.analysis-chart');
        console.log('找到图表元素数量:', chartElements.length);

        // 打印当前的分析结果数据
        console.log('初始化图表时的分析结果数据:', window.analysisResultsData);

        // 检查DOM中的卡片元素
        const cards = document.querySelectorAll('.analysis-card');
        console.log('当前DOM中的分析卡片数量:', cards.length);
        cards.forEach(card => {
            const dimension = card.getAttribute('data-dimension');
            console.log(`卡片维度: ${dimension}, 内容:`, card.innerHTML.substring(0, 100) + '...');
        });

        chartElements.forEach(canvas => {
            const dimension = canvas.getAttribute('data-dimension');
            if (!dimension) {
                console.error('图表元素没有dimension属性:', canvas);
                return;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error(`无法获取维度 ${dimension} 的图表上下文`);
                return;
            }

            // 尝试从分析结果中提取可视化数据
            let labels = ['风格', '节奏', '结构', '人物', '情节', '主题'];
            let data = [85, 72, 90, 65, 78, 82];

            console.log(`尝试为维度 ${dimension} 获取可视化数据`);
            console.log('当前analysisResultsData:', window.analysisResultsData);

            // 检查是否有可视化数据
            try {
                if (window.analysisResultsData &&
                    window.analysisResultsData[dimension] &&
                    window.analysisResultsData[dimension].metadata &&
                    window.analysisResultsData[dimension].metadata.visualization_data) {

                    const visualData = window.analysisResultsData[dimension].metadata.visualization_data.radar;
                    if (visualData && visualData.labels && visualData.data) {
                        labels = visualData.labels;
                        data = visualData.data;
                        console.log(`使用维度 ${dimension} 的自定义可视化数据:`, labels, data);
                    } else {
                        console.log(`维度 ${dimension} 没有雷达图数据，使用默认数据`);
                    }
                } else {
                    // 尝试从API获取最新数据
                    console.log(`analysisResultsData中没有维度 ${dimension} 的数据，尝试从API获取`);
                    fetch(`/api/novels/${novelIdFromTemplate}/analysis/${dimension}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('获取分析结果失败');
                            }
                            return response.json();
                        })
                        .then(resultData => {
                            if (resultData.metadata && resultData.metadata.visualization_data && resultData.metadata.visualization_data.radar) {
                                const visualData = resultData.metadata.visualization_data.radar;
                                if (visualData && visualData.labels && visualData.data) {
                                    labels = visualData.labels;
                                    data = visualData.data;
                                    console.log(`从API获取维度 ${dimension} 的可视化数据:`, labels, data);

                                    // 重新创建图表
                                    createChart(ctx, dimension, labels, data);
                                    return;
                                }
                            }
                            console.log(`从API获取的维度 ${dimension} 数据中没有可视化数据，使用默认数据`);
                            createChart(ctx, dimension, labels, data);
                        })
                        .catch(error => {
                            console.error(`获取维度 ${dimension} 的可视化数据失败:`, error);
                            createChart(ctx, dimension, labels, data);
                        });
                    return; // 异步获取数据，先返回
                }
            } catch (e) {
                console.error(`处理维度 ${dimension} 的可视化数据时出错:`, e);
                console.log(`为维度 ${dimension} 使用默认可视化数据`);
            }

            // 使用通用函数创建图表
            createChart(ctx, dimension, labels, data);
        });
    }

    // 创建雷达图的通用函数
    function createChart(ctx, dimension, labels, data) {
        if (!ctx) {
            console.error(`无法为维度 ${dimension} 创建图表：上下文无效`);
            return;
        }

        // 确保数据有效
        if (!labels || !Array.isArray(labels) || !data || !Array.isArray(data)) {
            console.error(`维度 ${dimension} 的图表数据无效:`, { labels, data });
            labels = ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'];
            data = [85, 72, 90, 65, 78, 82];
        }

        // 确保数据长度匹配
        if (labels.length !== data.length) {
            console.warn(`维度 ${dimension} 的标签和数据长度不匹配，将使用默认数据`);
            labels = ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'];
            data = [85, 72, 90, 65, 78, 82];
        }

        console.log(`为维度 ${dimension} 创建图表，使用数据:`, { labels, data });

        // 使用 chartLoader 确保 Chart.js 已加载
        window.chartLoader.ensureLoaded(function() {
            try {
                // 检查是否已存在图表，如果存在则销毁
                if (window.Chart && ctx.canvas) {
                    const chartInstance = Chart.getChart(ctx.canvas);
                    if (chartInstance) {
                        console.log(`销毁维度 ${dimension} 的现有图表`);
                        chartInstance.destroy();
                    }
                }

                // 创建新图表
                new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: formatDimensionName(dimension) + '分析评分',
                            data: data,
                            fill: true,
                            backgroundColor: 'rgba(74, 107, 223, 0.2)',
                            borderColor: 'rgb(74, 107, 223)',
                            pointBackgroundColor: 'rgb(74, 107, 223)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgb(74, 107, 223)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        elements: {
                            line: {
                                borderWidth: 3
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                suggestedMin: 0,
                                suggestedMax: 100,
                                ticks: {
                                    stepSize: 20
                                }
                            }
                        }
                    }
                });
                console.log(`成功为维度 ${dimension} 创建图表`);
            } catch (e) {
                console.error(`为维度 ${dimension} 创建图表时出错:`, e);
            }
        });
    }

    // 开始单个维度的分析
    function startSingleDimensionAnalysis(novelId, dimension) {
        console.log(`开始分析小说 ${novelId} 的 ${dimension} 维度`);

        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        const url = `/api/novels/${novelId}/analyze?_=${timestamp}`;

        console.log(`发送分析请求到: ${url}`);

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache'
            },
            body: JSON.stringify({
                dimensions: [dimension],
                parallel_analysis: document.getElementById('parallel-analysis') ? document.getElementById('parallel-analysis').checked : true,
                use_cache: document.getElementById('use-cache') ? document.getElementById('use-cache').checked : true,
                force_refresh: document.getElementById('force-refresh') ? document.getElementById('force-refresh').checked : false
            }),
        })
        .then(response => {
            console.log(`分析请求响应状态: ${response.status}`);
            if (!response.ok) {
                return response.text().then(text => {
                    console.error(`错误响应内容: ${text}`);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log(`分析请求响应数据:`, data);
            if (data.success) {
                // 使用data-dimension属性查找对应的卡片
                let card = document.querySelector('.analysis-card[data-dimension="' + dimension + '"]');
                console.log(`查找维度 ${dimension} 的卡片元素，使用data-dimension属性`);

                // 如果卡片不存在，创建它
                if (!card) {
                    card = createDimensionCard(dimension);
                }

                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    console.log(`为维度 ${dimension} 创建进度条`);

                    // 创建进度条HTML
                    cardBody.innerHTML = `
                        <div class="analysis-progress">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar"
                                     id="progress-${dimension}"
                                     aria-valuenow="10"
                                     aria-valuemin="0"
                                     aria-valuemax="100"
                                     data-progress="10"
                                     data-min-display="10"
                                     style="width: 10%">
                                    10%
                                </div>
                            </div>
                            <div class="analysis-status">
                                <span>分析中...</span>
                                <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                            </div>
                            <div class="analysis-visualization mt-3">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">正在分析中，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    `;

                    // 确认进度条已创建并设置了正确的宽度
                    setTimeout(() => {
                        const progressBar = document.getElementById(`progress-${dimension}`);
                        if (progressBar) {
                            console.log(`确认维度 ${dimension} 的进度条宽度: ${progressBar.style.width}`);
                        }
                    }, 100);

                    // 确认进度条已创建
                    const progressBar = document.getElementById(`progress-${dimension}`);
                    if (progressBar) {
                        console.log(`进度条元素已创建: progress-${dimension}`);
                    } else {
                        console.error(`无法找到进度条元素: progress-${dimension}`);
                    }
                } else {
                    console.error(`无法找到维度 ${dimension} 的卡片元素`);
                }

                // 启动进度更新
                console.log('开始更新分析进度');
                updateAnalysisProgress();
            } else {
                alert('分析启动失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('分析请求错误:', error);
            alert('请求错误: ' + error.message);
        });
    }

    // 重置数据库连接池
    function resetDatabaseConnections() {
        console.log('尝试重置数据库连接池');

        fetch('/api/reset_connections', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            }
        })
        .then(response => {
            if (!response.ok) {
                console.error(`重置连接池失败: HTTP ${response.status}`);
                return response.text().then(text => {
                    console.error(`错误响应内容: ${text}`);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('重置连接池响应:', data);
            if (data.success) {
                console.log('数据库连接池已重置');
                // 延迟1秒后重新尝试更新进度
                setTimeout(updateAnalysisProgress, 1000);
            } else {
                console.error('重置连接池失败:', data.error);
            }
        })
        .catch(error => {
            console.error('重置连接池请求错误:', error);
        });
    }

    // 增加防止重复并发轮询的标志
    let isUpdatingProgress = false;

    // 更新分析进度
    function updateAnalysisProgress() {
        if (isUpdatingProgress) {
            console.log('上一次进度请求尚未完成，跳过本次轮询');
            return;
        }
        isUpdatingProgress = true;
        console.log('开始更新分析进度');

        // novelId已在全局定义，不需要重复定义

        // 添加随机参数防止缓存
        const timestamp = new Date().getTime();
        const url = `/api/analysis/progress?novel_id=${novelId}&_=${timestamp}`;

        console.log(`获取分析进度: ${url}`);

        // 检查是否有分析进度数据
        fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache'
            }
        })
            .then(response => {
                if (!response.ok) {
                    console.error(`获取进度信息失败: HTTP ${response.status}`);

                    // 如果是500错误，可能是连接池问题，尝试重置连接池
                    if (response.status === 500) {
                        console.log('检测到500错误，尝试重置数据库连接池');
                        // 重置并发标志，允许新的轮询
                        isUpdatingProgress = false;
                        resetDatabaseConnections();
                        return null;
                    }

                    return response.text().then(text => {
                        console.error(`错误响应内容: ${text}`);
                        throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('获取到进度数据:', data);

                if (!data.success) {
                    console.error('获取进度信息失败:', data.error);
                    return;
                }

                // 如果没有进度数据，退出
                if (!data.progress || Object.keys(data.progress).length === 0) {
                    console.log('没有正在进行的分析任务');
                    return;
                }

                // 处理每个维度的进度数据
                let allCompleted = true;
                let hasAnyProgress = false;

                // 更新控制台日志
                if (data.recent_logs && data.recent_logs.length > 0) {
                    console.log(`收到 ${data.recent_logs.length} 条最新日志`);
                    data.recent_logs.forEach(log => {
                        // 使用控制台记录器添加日志
                        if (window.consoleLogger) {
                            window.consoleLogger.addLog(log.message, log.level, log.timestamp, log.dimension);
                        }
                    });
                }

                // 更新小说标题
                if (data.novel_title) {
                    const titleElement = document.querySelector('h1.mt-4');
                    if (titleElement) {
                        // 检查标题是否包含小说ID
                        if (titleElement.textContent.includes('小说ID:')) {
                            titleElement.textContent = `《${data.novel_title}》`;
                        }
                    }
                }

                for (const dimension in data.progress) {
                    console.log(`处理维度 ${dimension} 的进度数据`);

                    // 跳过已完成的分析
                    if (data.progress[dimension].completed || data.progress[dimension].progress >= 100) {
                        console.log(`维度 ${dimension} 已完成，跳过进度更新`);
                        continue;
                    }

                    hasAnyProgress = true;

                    // 先确保该维度的卡片存在
                    let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);

                    if (!card) {
                        console.log(`未找到维度 ${dimension} 的卡片，创建新卡片`);
                        card = createDimensionCard(dimension);

                        if (!card) {
                            console.error(`无法为维度 ${dimension} 创建卡片，跳过此维度`);
                            continue;
                        }
                    }

                    // 找到卡片的主体部分
                    const cardBody = card.querySelector('.card-body');
                    if (!cardBody) {
                        console.error(`维度 ${dimension} 的卡片没有卡片主体元素`);
                        continue;
                    }

                    // 获取进度数据
                    const progress = data.progress[dimension].progress;
                    const estimatedTime = data.progress[dimension].estimated_time;

                    // 获取更详细的进度信息
                    const remainingTime = data.progress[dimension].remaining_time || "计算中...";
                    const eta = data.progress[dimension].eta || "";
                    const blocksProgress = data.progress[dimension].blocks_progress || "";
                    const blocksPercentage = data.progress[dimension].blocks_percentage || 0;

                    // 获取最新日志
                    const latestLog = data.progress[dimension].latest_log || "";

                    // 显示总体进度信息
                    if (data.overall_progress) {
                        const overallProgressElement = document.getElementById('overall-progress');
                        if (overallProgressElement) {
                            const overallProgress = data.overall_progress.progress;
                            const completedDimensions = data.overall_progress.completed_dimensions;
                            const totalDimensions = data.overall_progress.total_dimensions;
                            const overallRemainingTime = data.overall_progress.remaining_time;
                            const overallEta = data.overall_progress.eta;

                            overallProgressElement.innerHTML = `
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">总体分析进度</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="progress mb-3">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                style="width: ${overallProgress}%"
                                                aria-valuenow="${overallProgress}"
                                                aria-valuemin="0"
                                                aria-valuemax="100">
                                                ${overallProgress}%
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>已完成维度: ${completedDimensions}/${totalDimensions}</span>
                                            <span>预计剩余时间: ${overallRemainingTime}</span>
                                        </div>
                                        <div class="text-end mt-2">
                                            <small class="text-muted">预计完成时间: ${overallEta}</small>
                                        </div>
                                    </div>
                                </div>
                            `;
                        } else {
                            // 如果总体进度元素不存在，创建一个
                            const analysisSection = document.querySelector('.analysis-section');
                            if (analysisSection) {
                                const overallProgressDiv = document.createElement('div');
                                overallProgressDiv.id = 'overall-progress';
                                overallProgressDiv.className = 'mt-4';
                                analysisSection.insertAdjacentElement('afterend', overallProgressDiv);

                                // 递归调用自身，以便更新新创建的元素
                                updateAnalysisProgress();
                                return;
                            }
                        }
                    }

                    // 检查是否出错或完成
                    if (progress < 0) {
                        // 分析失败
                        console.log(`维度 ${dimension} 分析失败，错误信息: ${estimatedTime}`);
                        cardBody.innerHTML = `
                            <div class="alert alert-danger">
                                <strong>分析失败</strong>
                                <p>${estimatedTime || '未知错误'}</p>
                                <button class="btn btn-sm btn-danger analyze-single-dimension mt-2"
                                        data-dimension="${dimension}"
                                        data-novel-id="${novelId}">
                                    重试分析
                                </button>
                            </div>
                        `;
                    } else if (progress >= 100) {
                        // 分析完成，显示结果
                        console.log(`维度 ${dimension} 分析完成，加载结果`);
                        allCompleted = allCompleted && true;

                        // 添加时间戳防止缓存
                        const timestamp = new Date().getTime();

                        // 尝试从API获取结果
                        const resultUrl = `/api/novels/${novelId}/analysis/${dimension}?_=${timestamp}`;
                        console.log(`获取分析结果: ${resultUrl}`);

                        fetch(resultUrl, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'Cache-Control': 'no-cache, no-store, must-revalidate',
                                'Pragma': 'no-cache'
                            }
                        })
                            .then(response => {
                                console.log(`维度 ${dimension} 的分析结果请求状态: ${response.status}`);
                                if (!response.ok) {
                                    if (response.status === 404 || response.status === 202) {
                                        // 如果是404错误或202状态码，说明分析结果尚未保存到数据库
                                        console.log(`维度 ${dimension} 的分析结果尚未保存到数据库，继续等待`);

                                        // 尝试解析响应内容，可能包含进度信息
                                        return response.json().catch(e => {
                                            console.log(`无法解析响应内容为JSON: ${e.message}`);

                                            // 检查分析进度，如果进度已经是100%但结果不存在，可能是保存结果时出错
                                            if (progress >= 100) {
                                                // 尝试重新启动该维度的分析
                                                console.log(`维度 ${dimension} 进度为100%但结果不存在，尝试重新分析`);
                                                setTimeout(() => {
                                                    startSingleDimensionAnalysis(novelId, dimension);
                                                }, 3000); // 延迟3秒后重新启动分析
                                            }

                                            // 不抛出错误，继续等待进度更新
                                            return null;
                                        });
                                    } else {
                                        console.error(`获取维度 ${dimension} 的分析结果失败: HTTP ${response.status}`);
                                        return response.text().then(text => {
                                            console.error(`错误响应内容: ${text}`);
                                            throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                                        });
                                    }
                                }
                                return response.json();
                            })
                            .then(resultData => {
                                if (resultData) {
                                    console.log(`获取到维度 ${dimension} 的分析结果:`, resultData);
                                    if (resultData.success) {
                                        updateDimensionCard(dimension, resultData);
                                    } else {
                                        console.warn(`维度 ${dimension} 的分析结果不成功:`, resultData.error);
                                        // 如果分析进度是100%但结果不成功，可能是保存结果时出错
                                        if (progress >= 100) {
                                            console.log(`维度 ${dimension} 进度为100%但结果不成功，尝试重新分析`);
                                            setTimeout(() => {
                                                startSingleDimensionAnalysis(novelId, dimension);
                                            }, 3000); // 延迟3秒后重新启动分析
                                        }
                                    }
                                }
                            })
                            .catch(error => {
                                console.error(`获取维度 ${dimension} 的结果出错:`, error);
                                // 显示错误信息，但不中断进度更新
                                const card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                                if (card) {
                                    const cardBody = card.querySelector('.card-body');
                                    if (cardBody) {
                                        // 安全处理错误消息
                                        // 安全处理所有输入参数
                                        const errorMsg = (error && error.message) ? String(error.message).replace(/</g, '&lt;') : '未知错误';
                                        const safeDimension = dimension ? String(dimension).replace(/"/g, '&quot;') : '';
                                        const safeNovelId = novelId ? String(novelId).replace(/"/g, '&quot;') : '';

                                        cardBody.innerHTML = `
                                            <div class="alert alert-warning">
                                                <strong>获取结果时出错</strong>
                                                <p>${errorMsg}</p>
                                                <button class="btn btn-sm btn-primary mt-2 analyze-single-dimension"
                                                    data-dimension="${safeDimension}"
                                                    data-novel-id="${safeNovelId}">
                                                    重试分析
                                                </button>
                                            </div>
                                        `;

                                            // 记录详细错误信息到控制台
                                            try {
                                                if (window.console && typeof window.console.error === 'function') {
                                                    window.console.error('获取维度 ' + dimension + ' 的分析结果时出错:', String(errorMsg));
                                                }
                                            } catch (loggingError) {
                                                // 静默失败
                                            }

                                            // 如果控制台日志功能可用，添加错误消息
                                            try {
                                                if (window.consoleLogger && typeof window.consoleLogger.addLog === 'function') {
                                                    window.consoleLogger.addLog('获取维度 ' + dimension + ' 的分析结果时出错: ' + String(errorMsg), 'error');
                                                }
                                            } catch (loggerError) {
                                                // 静默失败
                                            }
                                    }
                                }
                            })
                            .catch(function(fetchError) {
                                try {
                                    if (window.console && typeof window.console.error === 'function') {
                                        window.console.error('获取维度 ' + dimension + ' 的分析结果时网络错误:', fetchError);
                                    }
                                } catch (loggingError) {
                                    // 静默失败
                                }

                                // 显示错误信息
                                try {
                                    const cardBody = card.querySelector('.card-body');
                                    if (cardBody) {
                                        cardBody.innerHTML =
                                            '<div class="alert alert-danger">' +
                                            '<strong>网络错误</strong>' +
                                            '<p>获取分析结果时发生网络错误，请检查网络连接后重试。</p>' +
                                            '<button class="btn btn-sm btn-primary mt-2 analyze-single-dimension" ' +
                                            'data-dimension="' + dimension + '" ' +
                                            'data-novel-id="' + novelId + '">' +
                                            '重试分析' +
                                            '</button>' +
                                            '</div>';
                                    }
                                } catch (htmlError) {
                                    // 静默失败
                                }

                                // 如果控制台日志功能可用，添加错误消息
                                try {
                                    if (window.consoleLogger && typeof window.consoleLogger.addLog === 'function') {
                                        var errorMessage = '';
                                        try {
                                            errorMessage = fetchError.message || '未知错误';
                                        } catch (e) {
                                            errorMessage = '未知错误';
                                        }
                                        window.consoleLogger.addLog('获取维度 ' + dimension + ' 的分析结果时网络错误: ' + errorMessage, 'error');
                                    }
                                } catch (loggerError) {
                                    // 静默失败
                                }
                            });
                    } else {
                        // 分析进行中，更新进度条
                        allCompleted = false;
                        const displayProgress = Math.max(progress, 10);

                        // 检查是否已有进度条
                        let progressBar = document.getElementById('progress-' + dimension);
                        let timeSpan = document.getElementById('time-' + dimension);

                        if (progressBar && timeSpan) {
                            // 更新现有进度条
                            console.log('更新维度 ' + dimension + ' 的进度条: ' + progress + '% -> ' + displayProgress + '%');
                            progressBar.style.width = displayProgress + '%';
                            progressBar.setAttribute('aria-valuenow', progress);
                            progressBar.setAttribute('data-progress', progress);
                            progressBar.textContent = progress + '%';
                            timeSpan.textContent = '预计剩余时间: ' + (remainingTime || estimatedTime);

                            // 更新块级进度信息
                            const blocksElement = document.getElementById('blocks-' + dimension);
                            if (blocksElement && blocksProgress) {
                                blocksElement.textContent = '块进度: ' + blocksProgress;

                                // 更新块级进度条
                                const blocksProgressBar = document.getElementById('blocks-progress-' + dimension);
                                if (blocksProgressBar) {
                                    blocksProgressBar.style.width = blocksPercentage + '%';
                                    blocksProgressBar.setAttribute('aria-valuenow', blocksPercentage);
                                }
                            }

                            // 更新预计完成时间
                            const etaElement = document.getElementById('eta-' + dimension);
                            if (etaElement && eta) {
                                etaElement.textContent = '预计完成时间: ' + eta;
                            }

                            // 更新最新日志
                            const latestLogElement = document.getElementById('latest-log-' + dimension);
                            if (latestLogElement && latestLog) {
                                latestLogElement.textContent = '最新进展: ' + latestLog;
                            } else if (latestLog && !latestLogElement) {
                                // 如果有最新日志但没有显示元素，添加一个
                                const statusDiv = progressBar.closest('.analysis-status');
                                if (statusDiv) {
                                    const logDiv = document.createElement('div');
                                    logDiv.className = 'mt-2 p-2 bg-light rounded';
                                    logDiv.innerHTML = '<small id="latest-log-' + dimension + '" class="text-muted">最新进展: ' + latestLog + '</small>';

                                    // 找到终止分析按钮的容器，在它之前插入日志
                                    const buttonContainer = statusDiv.querySelector('.mt-2');
                                    if (buttonContainer) {
                                        statusDiv.insertBefore(logDiv, buttonContainer);
                                    } else {
                                        statusDiv.appendChild(logDiv);
                                    }
                                }
                            }
                        } else {
                            // 创建新的进度条
                            console.log('为维度 ' + dimension + ' 创建新进度条: ' + progress + '%');
                            // 安全处理变量
                            const safeProgress = progress || 0;
                            const safeDisplayProgress = displayProgress || 10;
                            const safeRemainingTime = remainingTime || estimatedTime || '计算中...';
                            const safeEta = eta || '';
                            const safeLatestLog = latestLog || '';
                            const safeBlocksProgress = blocksProgress || '';
                            const safeBlocksPercentage = blocksPercentage || 0;

                            // 构建块进度HTML
                            let blocksHtml = '';
                            if (safeBlocksProgress) {
                                blocksHtml =
                                    '<div class="mt-2">' +
                                    '<small id="blocks-' + dimension + '" class="text-muted">块进度: ' + safeBlocksProgress + '</small>' +
                                    '<div class="progress mt-1" style="height: 5px;">' +
                                    '<div class="progress-bar bg-info" role="progressbar" ' +
                                    'id="blocks-progress-' + dimension + '" ' +
                                    'aria-valuenow="' + safeBlocksPercentage + '" ' +
                                    'aria-valuemin="0" aria-valuemax="100" ' +
                                    'style="width: ' + safeBlocksPercentage + '%"></div>' +
                                    '</div>' +
                                    '</div>';
                            }

                            // 构建ETA HTML
                            let etaHtml = '';
                            if (safeEta) {
                                etaHtml = '<div class="text-end mt-1"><small id="eta-' + dimension + '" class="text-muted">预计完成时间: ' + safeEta + '</small></div>';
                            }

                            // 构建最新日志HTML
                            let logHtml = '';
                            if (safeLatestLog) {
                                logHtml =
                                    '<div class="mt-2 p-2 bg-light rounded">' +
                                    '<small id="latest-log-' + dimension + '" class="text-muted">最新进展: ' + safeLatestLog + '</small>' +
                                    '</div>';
                            } else {
                                logHtml =
                                    '<div class="mt-2 p-2 bg-light rounded">' +
                                    '<small id="latest-log-' + dimension + '" class="text-muted">最新进展: 分析进度 ' + safeProgress + '%</small>' +
                                    '</div>';
                            }

                            // 构建完整HTML
                            cardBody.innerHTML =
                                '<div class="analysis-progress">' +
                                '<div class="progress mb-2">' +
                                '<div class="progress-bar progress-bar-striped progress-bar-animated" ' +
                                'role="progressbar" ' +
                                'id="progress-' + dimension + '" ' +
                                'aria-valuenow="' + safeProgress + '" ' +
                                'aria-valuemin="0" ' +
                                'aria-valuemax="100" ' +
                                'data-progress="' + safeProgress + '" ' +
                                'data-min-display="10" ' +
                                'style="width: ' + safeDisplayProgress + '%">' +
                                safeProgress + '%' +
                                '</div>' +
                                '</div>' +
                                '<div class="analysis-status mb-2">' +
                                '<div class="d-flex justify-content-between">' +
                                '<span>分析中...</span>' +
                                '<span id="time-' + dimension + '">预计剩余时间: ' + safeRemainingTime + '</span>' +
                                '</div>' +
                                blocksHtml +
                                etaHtml +
                                logHtml +
                                '<div class="mt-2">' +
                                '<button class="btn btn-sm btn-danger stop-analysis-btn" ' +
                                'data-dimension="' + dimension + '" ' +
                                'data-novel-id="' + novelId + '">' +
                                '终止分析' +
                                '</button>' +
                                '</div>' +
                                '</div>' +
                                '<div class="analysis-visualization mt-3">' +
                                '<div class="text-center py-4">' +
                                '<div class="spinner-border text-primary" role="status">' +
                                '<span class="visually-hidden">Loading...</span>' +
                                '</div>' +
                                '<p class="mt-2">正在分析中，请稍候...</p>' +
                                '</div>' +
                                '</div>' +
                                '</div>';
                        }
                    }
                }

                // 如果所有分析都完成了或没有正在进行的分析，不再继续检查进度
                if ((allCompleted && Object.keys(data.progress).length > 0) || !hasAnyProgress) {
                    console.log('所有分析已完成或没有正在进行的分析，不再继续检查进度');
                    // 不再刷新页面，而是依靠前面的API调用来获取结果

                    // 确保获取所有完成的分析结果
                    fetch('/api/novels/' + novelId + '/analysis?_=' + new Date().getTime())
                        .then(response => response.json())
                        .then(results => {
                            console.log('分析完成后获取所有结果:', results);
                            if (results && results.length > 0) {
                                console.log('开始处理分析结果，找到 ' + results.length + ' 个结果');
                                results.forEach(result => {
                                    console.log('处理维度 ' + result.dimension + ' 的结果:', {
                                        contentType: typeof result.content,
                                        contentLength: result.content ? result.content.length : 0,
                                        metadataType: typeof result.metadata,
                                        metadataKeys: result.metadata ? Object.keys(result.metadata) : []
                                    });
                                    updateDimensionCard(result.dimension, result);
                                });
                            } else {
                                console.warn('没有找到任何分析结果');
                            }
                        })
                        .catch(error => console.error('获取完成结果时出错:', error));

                    // 重置标志
                    isUpdatingProgress = false;
                } else {
                    // 重置标志后继续下一轮
                    isUpdatingProgress = false;
                    setTimeout(updateAnalysisProgress, 1500);
                }
            })
            .catch(error => {
                try {
                    if (window.console && typeof window.console.error === 'function') {
                        window.console.error('获取进度信息时出错:', error);

                        // 安全地记录错误详情
                        var errorDetails = { message: '未知错误', stack: '无堆栈信息' };
                        try {
                            if (error) {
                                errorDetails.message = String(error.message || '未知错误');
                                errorDetails.stack = String(error.stack || '无堆栈信息');
                            }
                        } catch (e) {
                            // 静默失败
                        }
                        window.console.error('错误详情:', errorDetails);
                    }
                } catch (loggingError) {
                    // 静默失败
                }
                // 重置标志后继续下一轮
                isUpdatingProgress = false;
                setTimeout(updateAnalysisProgress, 5000);
            });
    }

    // 终止分析函数
    function stopAnalysis(novelId, dimension) {
        console.log('尝试终止小说 ' + novelId + ' 的 ' + dimension + ' 分析');

        fetch('/api/novels/' + novelId + '/stop_analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                dimension: dimension
            }),
        })
        .then(response => {
            if (!response.ok) {
                try {
                    if (window.console && typeof window.console.error === 'function') {
                        window.console.error('终止分析请求失败: HTTP ' + response.status);
                    }
                } catch (loggingError) {
                    // 静默失败
                }

                return response.text().then(text => {
                    try {
                        if (window.console && typeof window.console.error === 'function') {
                            window.console.error('错误响应内容: ' + String(text || ''));
                        }
                    } catch (loggingError) {
                        // 静默失败
                    }

                    throw new Error('HTTP error! status: ' + response.status + ', message: ' + String(text || ''));
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('成功终止 ' + dimension + ' 分析');
                alert('已终止 ' + formatDimensionName(dimension) + ' 分析');
            } else {
                try {
                    if (window.console && typeof window.console.error === 'function') {
                        window.console.error('终止分析失败:', String(data.error || '未知错误'));
                    }
                } catch (loggingError) {
                    // 静默失败
                }

                try {
                    alert('终止分析失败: ' + String(data.error || '未知错误'));
                } catch (alertError) {
                    // 静默失败
                }
            }
        })
        .catch(error => {
            try {
                if (window.console && typeof window.console.error === 'function') {
                    window.console.error('终止分析请求错误:', error);
                }
            } catch (loggingError) {
                // 静默失败
            }

            try {
                var errorMessage = '';
                try {
                    errorMessage = error && error.message ? String(error.message) : '未知错误';
                } catch (e) {
                    errorMessage = '未知错误';
                }
                alert('请求错误: ' + errorMessage);
            } catch (alertError) {
                // 静默失败
            }
        });
    }

    // 更新维度卡片的函数
    function updateDimensionCard(dimension, resultData) {
        console.log('尝试更新维度 ' + dimension + ' 的卡片，数据:', resultData);

        // 检查结果数据是否有效
        if (!resultData) {
            console.error('维度 ' + dimension + ' 的结果数据为空');
            return;
        }

        // 如果结果数据中没有success字段，但有content字段，也认为是有效的
        if (!resultData.success && !resultData.content) {
            console.warn('维度 ' + dimension + ' 的结果数据可能无效，但仍尝试处理:', resultData);
            // 继续处理，不要直接返回
        }

        // 查找卡片
        let card = document.querySelector('.analysis-card[data-dimension="' + dimension + '"]');
        if (!card) {
            console.log('未找到维度 ' + dimension + ' 的卡片，尝试创建');
            card = createDimensionCard(dimension);
            if (!card) {
                console.error('无法为维度 ' + dimension + ' 创建卡片');
                return;
            }
        }

        // 获取卡片主体
        const cardBody = card.querySelector('.card-body');
        if (!cardBody) {
            console.error('维度 ' + dimension + ' 的卡片没有主体元素');
            return;
        }

        // 保存结果数据到全局变量，供图表使用
        if (typeof analysisResultsData === 'undefined') {
            window.analysisResultsData = {};
        }
        analysisResultsData[dimension] = resultData;

        // 创建可视化HTML
        let visualizationHtml = '';
        if (resultData.metadata && resultData.metadata.visualization_data) {
            visualizationHtml =
                '<div class="analysis-visualization mt-3">' +
                '<canvas class="analysis-chart" data-dimension="' + dimension + '" width="400" height="250"></canvas>' +
                '</div>';
        }

        // 提取内容摘要
        let excerpt = '';
        try {
            // 确保内容是字符串
            let content = '';
            if (resultData.content) {
                if (typeof resultData.content === 'string') {
                    content = resultData.content;
                } else {
                    console.warn('维度 ' + dimension + ' 的内容不是字符串:', resultData.content);
                    try {
                        content = JSON.stringify(resultData.content);
                    } catch (e) {
                        console.error('转换维度 ' + dimension + ' 的内容为字符串时出错:', e);
                        content = '内容格式错误';
                    }
                }
            } else {
                content = '无内容';
            }

            // 去除HTML标签，只保留文本内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';
            excerpt = textContent.substring(0, 300) + '...';
            console.log('成功提取维度 ' + dimension + ' 的摘要，长度: ' + excerpt.length);
        } catch (e) {
            console.error('提取维度 ' + dimension + ' 的摘要时出错:', e);
            excerpt = '提取摘要时出错';
        }

        // 更新卡片内容
        try {
            // 构建完整HTML
            cardBody.innerHTML =
                '<div class="d-flex justify-content-between mb-2">' +
                '<span class="badge bg-success">分析完成</span>' +
                '<a href="' + window.location.pathname + '/analysis/' + dimension + '" class="btn btn-sm btn-outline-primary">查看详情</a>' +
                '</div>' +
                visualizationHtml +
                '<div class="analysis-excerpt mt-2">' +
                excerpt +
                '</div>';
            console.log('成功更新维度 ' + dimension + ' 的卡片内容');
        } catch (e) {
            console.error('更新维度 ' + dimension + ' 的卡片内容时出错:', e);
            // 尝试使用更简单的内容
            try {
                cardBody.innerHTML =
                    '<div class="d-flex justify-content-between mb-2">' +
                    '<span class="badge bg-success">分析完成</span>' +
                    '<a href="' + window.location.pathname + '/analysis/' + dimension + '" class="btn btn-sm btn-outline-primary">查看详情</a>' +
                    '</div>' +
                    '<div class="analysis-excerpt mt-2">' +
                    '分析已完成，点击"查看详情"查看完整结果。' +
                    '</div>';
            } catch (innerError) {
                console.error(`尝试使用简化内容更新卡片时也出错:`, innerError);
            }
        }

        // 初始化图表
        setTimeout(() => {
            try {
                const canvas = card.querySelector('.analysis-chart');
                if (canvas) {
                    console.log(`找到维度 ${dimension} 的图表画布元素`);
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                        console.log(`获取到维度 ${dimension} 的图表上下文`);
                        try {
                            let labels = ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'];
                            let data = [85, 72, 90, 65, 78, 82];

                            // 确保元数据是有效的对象
                            let metadata = {};
                            if (resultData.metadata) {
                                if (typeof resultData.metadata === 'object') {
                                    metadata = resultData.metadata;
                                } else {
                                    console.warn(`维度 ${dimension} 的元数据不是对象:`, resultData.metadata);
                                    try {
                                        metadata = JSON.parse(resultData.metadata);
                                    } catch (e) {
                                        console.error(`解析维度 ${dimension} 的元数据时出错:`, e);
                                        metadata = {};
                                    }
                                }
                            }

                            if (metadata &&
                                metadata.visualization_data &&
                                metadata.visualization_data.radar) {

                                const visualData = metadata.visualization_data.radar;
                                if (visualData && visualData.labels && visualData.data) {
                                    labels = visualData.labels;
                                    data = visualData.data;
                                    console.log(`使用维度 ${dimension} 的自定义可视化数据:`, labels, data);
                                }
                            }

                            createChart(ctx, dimension, labels, data);
                            console.log(`成功为维度 ${dimension} 创建图表`);
                        } catch (e) {
                            console.error(`为维度 ${dimension} 创建图表时出错:`, e);
                        }
                    } else {
                        console.error(`无法获取维度 ${dimension} 的图表上下文`);
                    }
                } else {
                    console.error(`找不到维度 ${dimension} 的图表画布元素`);
                }
            } catch (e) {
                console.error(`初始化维度 ${dimension} 的图表时出错:`, e);
            }
        }, 200);

        console.log(`维度 ${dimension} 的卡片已更新`);
    }

    // 创建图表的函数
    function createChart(ctx, dimension, labels, data) {
        console.log(`创建维度 ${dimension} 的图表，标签:`, labels, '数据:', data);

        try {
            // 使用chartLoader确保Chart.js已加载
            if (window.chartLoader) {
                window.chartLoader.ensureLoaded(function() {
                    try {
                        // 检查是否已存在图表实例，如果存在则销毁
                        if (window.chartInstances && window.chartInstances[dimension]) {
                            console.log(`销毁维度 ${dimension} 的现有图表实例`);
                            window.chartInstances[dimension].destroy();
                        }

                        // 确保标签和数据是有效的数组
                        if (!Array.isArray(labels)) {
                            console.warn(`维度 ${dimension} 的标签不是数组，使用默认标签`);
                            labels = ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'];
                        }

                        if (!Array.isArray(data)) {
                            console.warn(`维度 ${dimension} 的数据不是数组，使用默认数据`);
                            data = [85, 72, 90, 65, 78, 82];
                        }

                        // 创建雷达图
                        const chartInstance = new Chart(ctx, {
                            type: 'radar',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: formatDimensionName(dimension),
                                    data: data,
                                    backgroundColor: 'rgba(74, 107, 223, 0.2)',
                                    borderColor: 'rgba(74, 107, 223, 1)',
                                    borderWidth: 2,
                                    pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                                    pointBorderColor: '#fff',
                                    pointHoverBackgroundColor: '#fff',
                                    pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    r: {
                                        angleLines: {
                                            display: true
                                        },
                                        suggestedMin: 0,
                                        suggestedMax: 100
                                    }
                                }
                            }
                        });

                        // 保存图表实例以便后续管理
                        if (!window.chartInstances) {
                            window.chartInstances = {};
                        }
                        window.chartInstances[dimension] = chartInstance;

                        console.log(`维度 ${dimension} 的图表创建成功`);
                    } catch (e) {
                        console.error(`创建维度 ${dimension} 的图表时出错:`, e);
                    }
                });
            } else {
                // 如果chartLoader不可用，尝试直接加载Chart.js
                console.error(`chartLoader不可用，尝试直接加载Chart.js`);

                if (typeof Chart === 'undefined') {
                    var chartScript = document.createElement('script');
                    chartScript.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js';

                    chartScript.onload = function() {
                        console.log('CDN Chart.js加载成功，重新尝试创建图表');
                        setTimeout(function() {
                            createChart(ctx, dimension, labels, data);
                        }, 100);
                    };

                    document.head.appendChild(chartScript);
                } else {
                    // Chart.js已加载，直接创建图表
                    console.log(`Chart.js已加载，直接创建图表`);

                    // 检查是否已存在图表实例，如果存在则销毁
                    if (window.chartInstances && window.chartInstances[dimension]) {
                        console.log(`销毁维度 ${dimension} 的现有图表实例`);
                        window.chartInstances[dimension].destroy();
                    }

                    // 创建雷达图
                    const chartInstance = new Chart(ctx, {
                        type: 'radar',
                        data: {
                            labels: labels || ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'],
                            datasets: [{
                                label: formatDimensionName(dimension),
                                data: data || [85, 72, 90, 65, 78, 82],
                                backgroundColor: 'rgba(74, 107, 223, 0.2)',
                                borderColor: 'rgba(74, 107, 223, 1)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                r: {
                                    angleLines: {
                                        display: true
                                    },
                                    suggestedMin: 0,
                                    suggestedMax: 100
                                }
                            }
                        }
                    });

                    // 保存图表实例以便后续管理
                    if (!window.chartInstances) {
                        window.chartInstances = {};
                    }
                    window.chartInstances[dimension] = chartInstance;
                }
            }
        } catch (e) {
            console.error(`创建维度 ${dimension} 的图表时出错:`, e);
        }
    }

    // 格式化维度名称的函数
    function formatDimensionName(dimension) {
        const dimensionMap = {
            'language_style': '语言风格',
            'rhythm_pacing': '节奏与节奏',
            'structure': '结构分析',
            'sentence_variation': '句式变化',
            'paragraph_length': '段落长度',
            'perspective_shifts': '视角转换',
            'paragraph_flow': '段落流畅度',
            'novel_characteristics': '小说特点',
            'world_building': '世界构建',
            'chapter_outline': '章节大纲',
            'character_relationships': '人物关系',
            'opening_effectiveness': '开篇效果',
            'climax_pacing': '高潮节奏'
        };

        // 如果找不到映射，尝试将下划线替换为空格并首字母大写
        if (!dimensionMap[dimension]) {
            console.log(`未找到维度 ${dimension} 的中文名称，使用默认格式化`);
            return dimension
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
        }

        return dimensionMap[dimension];
    }

    // 创建维度卡片的函数
    function createDimensionCard(dimension) {
        console.log(`尝试创建维度 ${dimension} 的卡片`);

        // 检查是否已存在该维度的卡片
        let existingCard = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
        if (existingCard) {
            console.log(`维度 ${dimension} 的卡片已存在，返回现有卡片`);
            return existingCard;
        }

        // 获取分析结果容器
        const resultsContainer = document.querySelector('.row.mt-4');
        if (!resultsContainer) {
            console.error('找不到分析结果容器');
            return null;
        }

        // 创建卡片列
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-4';

        // 创建卡片
        const card = document.createElement('div');
        card.className = 'card analysis-card';
        card.setAttribute('data-dimension', dimension);

        // 创建卡片头部
        const cardHeader = document.createElement('div');
        cardHeader.className = 'card-header';

        // 创建卡片标题
        const cardTitle = document.createElement('h5');
        cardTitle.className = 'card-title';
        cardTitle.textContent = formatDimensionName(dimension);

        // 创建卡片主体
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';
        cardBody.innerHTML = `
            <p class="text-muted">尚未分析</p>
            <button class="btn btn-sm btn-outline-secondary analyze-single-dimension"
                    data-dimension="${dimension}"
                    data-novel-id="${document.querySelector('#analyze-form').getAttribute('data-novel-id')}">
                开始分析
            </button>
        `;

        // 组装卡片
        cardHeader.appendChild(cardTitle);
        card.appendChild(cardHeader);
        card.appendChild(cardBody);
        col.appendChild(card);

        // 添加到容器
        resultsContainer.appendChild(col);

        // 绑定分析按钮事件
        const analyzeButton = card.querySelector('.analyze-single-dimension');
        if (analyzeButton) {
            analyzeButton.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');
                const novelId = this.getAttribute('data-novel-id');
                console.log(`点击了维度 ${dimension} 的分析按钮`);
                startSingleDimensionAnalysis(novelId, dimension);
            });
        }

        console.log(`成功创建维度 ${dimension} 的卡片`);
        return card;
    }

    // 显示所有维度的当前状态
    window.displayAllDimensions = function() {
        // 确保novelId是有效的
        let novelId;
        try {
            novelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
        } catch (e) {
            console.error('解析novelId出错:', e);
            novelId = novelIdFromTemplate;
        }

        // 获取所有可用维度
        const availableDimensions = [
            'language_style', 'rhythm_pacing', 'structure', 'sentence_variation',
            'paragraph_length', 'perspective_shifts', 'paragraph_flow',
            'novel_characteristics', 'world_building', 'chapter_outline',
            'character_relationships', 'opening_effectiveness', 'climax_pacing'
        ];

        // 查找已存在的维度状态表格
        const existingTable = document.getElementById('dimensions-status-table');
        if (existingTable) {
            console.log('找到已存在的维度状态表格，使用现有表格');

            // 更新维度列表
            availableDimensions.forEach(function(dimension) {
                // 查找对应的行
                const row = existingTable.querySelector(`tr[data-dimension="${dimension}"]`);
                if (row) {
                    // 更新行内容
                    updateDimensionRow(dimension, row);
                }
            });

            return;
        }

        // 如果没有找到现有表格，尝试查找容器
        const container = document.querySelector('.analysis-section');
        if (!container) {
            console.error('找不到维度状态容器(.analysis-section)，无法添加维度状态');
            return;
        }

        console.log('找到维度状态容器，但没有找到表格，这不应该发生，因为表格已经在HTML中预先创建');
    }

    // 更新维度行
    function updateDimensionRow(dimension, row) {
        if (!row) return;

        // 确保novelId是有效的
        let novelId;
        try {
            novelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
        } catch (e) {
            console.error('解析novelId出错:', e);
            novelId = novelIdFromTemplate;
        }

        // 获取状态单元格
        const statusCell = row.querySelector('.dimension-status');
        // 获取进度单元格
        const progressCell = row.querySelector('.dimension-progress');
        // 获取操作单元格
        const actionsCell = row.querySelector('.dimension-actions');

        if (!statusCell || !progressCell || !actionsCell) {
            console.error(`维度 ${dimension} 的行缺少必要的单元格`);
            return;
        }

        // 获取维度状态
        fetch(`/api/novels/${novelId}/analysis/${dimension}`)
            .then(res => {
                if (!res.ok) {
                    if (res.status === 404) {
                        // 维度尚未分析
                        statusCell.innerHTML = '<span class="badge bg-secondary">未分析</span>';
                        // 重置进度条
                        const progressBar = progressCell.querySelector('.progress-bar');
                        if (progressBar) {
                            progressBar.style.width = '0%';
                            progressBar.setAttribute('aria-valuenow', '0');
                            progressBar.textContent = '0%';
                        }
                    } else if (res.status === 202) {
                        // 维度正在分析中
                        return res.json().then(data => {
                            statusCell.innerHTML = '<span class="badge bg-info">分析中</span>';
                            // 更新进度条
                            const progressBar = progressCell.querySelector('.progress-bar');
                            if (progressBar) {
                                const progress = data.progress || 0;
                                progressBar.style.width = `${progress}%`;
                                progressBar.setAttribute('aria-valuenow', progress);
                                progressBar.textContent = `${progress}%`;
                            }
                        });
                    }
                    return Promise.reject(new Error(`获取维度 ${dimension} 状态失败: ${res.status}`));
                }
                return res.json();
            })
            .then(data => {
                if (data && data.success) {
                    // 维度已分析完成
                    statusCell.innerHTML = '<span class="badge bg-success">已完成</span>';
                    // 更新进度条
                    const progressBar = progressCell.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = '100%';
                        progressBar.setAttribute('aria-valuenow', '100');
                        progressBar.textContent = '100%';
                    }
                    // 更新操作按钮
                    actionsCell.innerHTML = `
                        <button class="btn btn-sm btn-outline-secondary reanalyze-btn"
                                data-dimension="${dimension}"
                                data-novel-id="${novelId}">
                            <i class="fas fa-sync"></i> 重新分析
                        </button>
                        <a href="/novel/${novelId}/analysis/${dimension}"
                           class="btn btn-sm btn-outline-primary ms-1">
                            查看详情
                        </a>
                    `;
                }
            })
            .catch(error => {
                console.error(`更新维度 ${dimension} 状态时出错:`, error);
            });
    }

    // 更新所有维度的状态显示
    function updateDimensionStatus() {
        // 确保novelId是有效的
        let novelId;
        try {
            novelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
        } catch (e) {
            console.error('解析novelId出错:', e);
            novelId = novelIdFromTemplate;
        }

        // 查找已存在的维度状态表格
        const existingTable = document.getElementById('dimensions-status-table');
        if (!existingTable) {
            console.error('找不到维度状态表格，无法更新维度状态');
            return;
        }

        // 获取所有维度行
        const rows = existingTable.querySelectorAll('tbody tr[data-dimension]');
        if (rows.length === 0) {
            console.error('维度状态表格中没有找到维度行');
            return;
        }

        // 更新每个维度行
        rows.forEach(row => {
            const dimension = row.getAttribute('data-dimension');
            if (dimension) {
                updateDimensionRow(dimension, row);
            }
        });

        // 获取所有分析结果和进度（用于卡片更新）
        Promise.all([
            fetch(`/api/novels/${novelId}/analysis`).then(res => res.json()),
            fetch(`/api/analysis/progress?novel_id=${novelId}`).then(res => res.json())
        ])
        .then(([results, progress]) => {
            console.log('更新维度状态:', results, progress);

            // 如果获取到了新的分析结果，更新全局变量
            if (results && Array.isArray(results) && results.length > 0) {
                console.log('API返回的分析结果数据:', results);

                // 将结果转换为以维度为键的对象
                const newResultsData = {};
                results.forEach(result => {
                    if (result && result.dimension) {
                        console.log(`处理维度 ${result.dimension} 的结果:`, {
                            contentType: typeof result.content,
                            contentLength: result.content ? result.content.length : 0,
                            metadataType: typeof result.metadata,
                            metadataKeys: result.metadata ? Object.keys(result.metadata) : []
                        });

                        // 确保内容是字符串
                        let content = '';
                        if (result.content) {
                            if (typeof result.content === 'string') {
                                content = result.content;
                            } else {
                                console.warn(`维度 ${result.dimension} 的内容不是字符串:`, result.content);
                                try {
                                    content = JSON.stringify(result.content);
                                } catch (e) {
                                    console.error(`转换维度 ${result.dimension} 的内容为字符串时出错:`, e);
                                    content = '内容格式错误';
                                }
                            }
                        }

                        // 确保元数据是对象
                        let metadata = {};
                        if (result.metadata) {
                            if (typeof result.metadata === 'object') {
                                metadata = result.metadata;
                            } else {
                                console.warn(`维度 ${result.dimension} 的元数据不是对象:`, result.metadata);
                                try {
                                    metadata = JSON.parse(result.metadata);
                                } catch (e) {
                                    console.error(`解析维度 ${result.dimension} 的元数据时出错:`, e);
                                    metadata = {};
                                }
                            }
                        }

                        newResultsData[result.dimension] = {
                            dimension: result.dimension,
                            content: content,
                            metadata: metadata
                        };
                    }
                });

                // 更新全局变量
                if (Object.keys(newResultsData).length > 0) {
                    console.log('更新全局分析结果数据:', newResultsData);
                    analysisResultsData = newResultsData;

                    // 初始化图表
                    setTimeout(initializeCharts, 500);
                }
            }

            // 获取所有维度的状态表格行
            const rows = document.querySelectorAll('tr[data-dimension]');

            rows.forEach(row => {
                const dimension = row.getAttribute('data-dimension');
                const statusCell = row.querySelector('.dimension-status');
                const actionCell = row.querySelector('td:last-child');

                // 检查是否有已完成的结果
                const result = results.find(r => r.dimension === dimension);

                // 检查是否有进行中的分析
                const inProgress = progress.success &&
                                  progress.progress &&
                                  progress.progress[dimension] &&
                                  progress.progress[dimension].progress < 100 &&
                                  progress.progress[dimension].progress >= 0;

                // 检查是否有错误
                const hasError = progress.success &&
                                progress.progress &&
                                progress.progress[dimension] &&
                                progress.progress[dimension].progress < 0;

                if (result) {
                    // 分析已完成
                    statusCell.innerHTML = `<span class="badge bg-success">已完成分析</span>`;
                    actionCell.innerHTML = `
                        <a href="${window.location.pathname}/analysis/${dimension}"
                           class="btn btn-sm btn-outline-primary">
                            查看详情
                        </a>
                        <button class="btn btn-sm btn-outline-secondary analyze-single-dimension ms-2"
                                data-dimension="${dimension}"
                                data-novel-id="${novelId}">
                            重新分析
                        </button>
                    `;

                    // 更新卡片显示
                    let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                    if (card) {
                        const cardBody = card.querySelector('.card-body');
                        if (cardBody) {
                            // 创建可视化和结果内容
                            let visualizationHtml = '';
                            if (result.metadata && result.metadata.visualization_data) {
                                visualizationHtml = `
                                    <div class="analysis-visualization mt-3">
                                        <canvas class="analysis-chart" data-dimension="${dimension}" width="400" height="250"></canvas>
                                    </div>
                                `;
                            }

                            // 提取内容的前300个字符作为摘要
                            let excerpt = '';
                            try {
                                // 去除HTML标签，只保留文本内容
                                const tempDiv = document.createElement('div');
                                tempDiv.innerHTML = result.content || '';
                                const textContent = tempDiv.textContent || tempDiv.innerText || '';
                                excerpt = textContent.substring(0, 300) + '...';
                            } catch (e) {
                                console.error(`提取摘要时出错:`, e);
                                excerpt = (result.content || '').substring(0, 300) + '...';
                            }

                            // 更新卡片内容
                            cardBody.innerHTML = `
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="badge bg-success">分析完成</span>
                                    <div>
                                        <a href="${window.location.pathname}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                                        ${dimension === 'character_relationships' ?
                                            `<a href="/optimized/character_relationships/${novelId}" class="btn btn-sm btn-outline-success ms-1">优化版</a>` :
                                            ''}
                                    </div>
                                </div>
                                ${visualizationHtml}
                                <div class="analysis-excerpt mt-2">
                                    ${excerpt}
                                </div>
                            `;

                            // 初始化图表
                            setTimeout(() => {
                                const canvas = card.querySelector('.analysis-chart');
                                if (canvas) {
                                    initializeCharts();
                                }
                            }, 100);
                        }
                    }
                } else if (inProgress) {
                    // 分析进行中
                    const currentProgress = progress.progress[dimension].progress;
                    const estimatedTime = progress.progress[dimension].estimated_time;

                    statusCell.innerHTML = `
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                 aria-valuenow="${currentProgress}"
                                 aria-valuemin="0"
                                 aria-valuemax="100"
                                 style="width: ${currentProgress}%">
                                ${currentProgress}%
                            </div>
                        </div>
                        <small class="text-muted">预计剩余: ${estimatedTime}</small>
                    `;

                    actionCell.innerHTML = `
                        <span class="text-muted">分析中...</span>
                        <button class="btn btn-sm btn-outline-danger stop-analysis-btn ms-2"
                                data-dimension="${dimension}"
                                data-novel-id="${novelIdFromTemplate}">
                            终止
                        </button>
                    `;

                    // 更新卡片显示
                    let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                    if (!card) {
                        card = createDimensionCard(dimension);
                    }

                    if (card) {
                        const cardBody = card.querySelector('.card-body');
                        if (cardBody) {
                            const displayProgress = Math.max(currentProgress, 10);

                            cardBody.innerHTML = `
                                <div class="analysis-progress">
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                            role="progressbar"
                                            id="progress-${dimension}"
                                            aria-valuenow="${currentProgress}"
                                            aria-valuemin="0"
                                            aria-valuemax="100"
                                            data-progress="${currentProgress}"
                                            data-min-display="10"
                                            style="width: ${displayProgress}%">
                                            ${currentProgress}%
                                        </div>
                                    </div>
                                    <div class="analysis-status">
                                        <span>分析中...</span>
                                        <span id="time-${dimension}">预计剩余时间: ${estimatedTime}</span>
                                        <button class="btn btn-sm btn-danger stop-analysis-btn mt-2"
                                                data-dimension="${dimension}"
                                                data-novel-id="${novelIdFromTemplate}">
                                            终止分析
                                        </button>
                                    </div>
                                    <div class="analysis-visualization mt-3">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">正在分析中，请稍候...</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                    }
                } else if (hasError) {
                    // 分析出错或被终止
                    const errorMsg = progress.progress[dimension].estimated_time;
                    const isTerminated = progress.progress[dimension].progress === -2;

                    if (isTerminated) {
                        // 分析被终止，显示断点继续按钮
                        statusCell.innerHTML = `<span class="badge bg-warning">已终止</span>`;
                        actionCell.innerHTML = `
                            <button class="btn btn-sm btn-outline-primary resume-analysis-btn"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                从断点继续
                            </button>
                            <button class="btn btn-sm btn-outline-danger analyze-single-dimension ms-2"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                重新开始
                            </button>
                        `;

                        // 更新卡片显示
                        let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                        if (card) {
                            const cardBody = card.querySelector('.card-body');
                            if (cardBody) {
                                cardBody.innerHTML = `
                                    <div class="alert alert-warning">
                                        <strong>分析已终止</strong>
                                        <p>您可以从断点继续分析，或重新开始分析。</p>
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-primary resume-analysis-btn"
                                                    data-dimension="${dimension}"
                                                    data-novel-id="${novelIdFromTemplate}">
                                                从断点继续
                                            </button>
                                            <button class="btn btn-sm btn-danger analyze-single-dimension ms-2"
                                                    data-dimension="${dimension}"
                                                    data-novel-id="${novelIdFromTemplate}">
                                                重新开始
                                            </button>
                                        </div>
                                    </div>
                                `;
                            }
                        }
                    } else {
                        // 分析出错
                        statusCell.innerHTML = `<span class="badge bg-danger">分析失败</span>`;
                        actionCell.innerHTML = `
                            <button class="btn btn-sm btn-outline-danger analyze-single-dimension"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                重试
                            </button>
                        `;

                        // 更新卡片显示
                        let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                        if (card) {
                            const cardBody = card.querySelector('.card-body');
                            if (cardBody) {
                                cardBody.innerHTML = `
                                    <div class="alert alert-danger">
                                        <strong>分析失败</strong>
                                        <p>${errorMsg || '未知错误'}</p>
                                        <button class="btn btn-sm btn-danger analyze-single-dimension mt-2"
                                                data-dimension="${dimension}"
                                                data-novel-id="${novelIdFromTemplate}">
                                            重试分析
                                        </button>
                                    </div>
                                `;
                            }
                        }
                    }
                } else {
                    // 未分析
                    statusCell.innerHTML = `<span class="badge bg-secondary">未分析</span>`;
                    actionCell.innerHTML = `
                        <button class="btn btn-sm btn-outline-primary analyze-single-dimension"
                                data-dimension="${dimension}"
                                data-novel-id="${novelIdFromTemplate}">
                            开始分析
                        </button>
                    `;

                    // 更新卡片显示
                    let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                    if (card) {
                        const cardBody = card.querySelector('.card-body');
                        if (cardBody) {
                            cardBody.innerHTML = `
                                <div class="text-center py-4">
                                    <p class="text-muted">尚未分析此维度</p>
                                    <button class="btn btn-primary analyze-single-dimension mt-2"
                                            data-dimension="${dimension}"
                                            data-novel-id="${novelIdFromTemplate}">
                                        开始分析
                                    </button>
                                </div>
                            `;
                        }
                    }
                }
            });

            // 不再需要重新绑定按钮事件，我们将使用事件委托
        })
        .catch(error => {
            console.error('更新维度状态时出错:', error);
            // 记录更详细的错误信息
            console.error('错误详情:', { message: error.message, stack: error.stack });
        });
    }

    // 初始显示所有维度
    displayAllDimensions();

    // 定时更新维度状态
    setInterval(updateDimensionStatus, 3000); // 每3秒更新一次维度状态

    // 单个维度分析函数
    function startSingleDimensionAnalysis(novelId, dimension) {
        console.log(`开始分析小说 ${novelId} 的 ${dimension} 维度`);

        // 更新UI显示分析进行中
        const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
        if (row) {
            const statusCell = row.querySelector('.dimension-status');
            const actionCell = row.querySelector('td:last-child');

            statusCell.innerHTML = `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar"
                         aria-valuenow="0"
                         aria-valuemin="0"
                         aria-valuemax="100"
                         style="width: 0%">
                        0%
                    </div>
                </div>
                <small class="text-muted">预计剩余: 计算中...</small>
            `;

            actionCell.innerHTML = `
                <span class="text-muted">分析中...</span>
                <button class="btn btn-sm btn-outline-danger stop-analysis-btn ms-2"
                        data-dimension="${dimension}"
                        data-novel-id="${novelIdFromTemplate}">
                    终止
                </button>
            `;
        }

        // 更新卡片显示
        let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
        if (!card) {
            card = createDimensionCard(dimension);
        }

        if (card) {
            const cardBody = card.querySelector('.card-body');
            if (cardBody) {
                cardBody.innerHTML = `
                    <div class="analysis-progress">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                role="progressbar"
                                id="progress-${dimension}"
                                aria-valuenow="0"
                                aria-valuemin="0"
                                aria-valuemax="100"
                                data-progress="0"
                                data-min-display="10"
                                style="width: 10%">
                                0%
                            </div>
                        </div>
                        <div class="analysis-status">
                            <span>分析中...</span>
                            <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                            <button class="btn btn-sm btn-danger stop-analysis-btn mt-2"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                终止分析
                            </button>
                        </div>
                        <div class="analysis-visualization mt-3">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">正在分析中，请稍候...</p>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // 发送API请求开始分析
        fetch(`/api/novels/${novelId}/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                dimensions: [dimension]
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`成功启动 ${dimension} 分析:`, data);
                // 启动进度更新
                updateAnalysisProgress();
            } else {
                console.error(`启动 ${dimension} 分析失败:`, data);
                alert('启动分析失败: ' + (data.error || '未知错误'));

                // 恢复UI状态
                if (row) {
                    const statusCell = row.querySelector('.dimension-status');
                    const actionCell = row.querySelector('td:last-child');

                    statusCell.innerHTML = `<span class="badge bg-danger">启动失败</span>`;
                    actionCell.innerHTML = `
                        <button class="btn btn-sm btn-outline-primary analyze-single-dimension"
                                data-dimension="${dimension}"
                                data-novel-id="${novelId}">
                            重试分析
                        </button>
                    `;
                }

                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    if (cardBody) {
                        cardBody.innerHTML = `
                            <div class="alert alert-danger">
                                <strong>启动分析失败</strong>
                                <p>${data.error || '未知错误'}</p>
                                <button class="btn btn-sm btn-primary analyze-single-dimension mt-2"
                                        data-dimension="${dimension}"
                                        data-novel-id="${novelIdFromTemplate}">
                                    重试分析
                                </button>
                            </div>
                        `;
                    }
                }
            }
        })
        .catch(error => {
            console.error('分析请求错误:', error);
            alert('请求错误: ' + error.message);

            // 恢复UI状态
            if (row) {
                const statusCell = row.querySelector('.dimension-status');
                const actionCell = row.querySelector('td:last-child');

                statusCell.innerHTML = `<span class="badge bg-danger">请求错误</span>`;
                actionCell.innerHTML = `
                    <button class="btn btn-sm btn-outline-primary analyze-single-dimension"
                            data-dimension="${dimension}"
                            data-novel-id="${novelIdFromTemplate}">
                        重试分析
                    </button>
                `;
            }

            if (card) {
                const cardBody = card.querySelector('.card-body');
                if (cardBody) {
                    cardBody.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>请求错误</strong>
                            <p>${error.message}</p>
                            <button class="btn btn-sm btn-primary analyze-single-dimension mt-2"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                重试分析
                            </button>
                        </div>
                    `;
                }
            }
        });
    }

    // 使用事件委托处理分析按钮点击
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('analyze-single-dimension')) {
            const dimension = e.target.getAttribute('data-dimension');
            const novelId = e.target.getAttribute('data-novel-id');
            console.log(`点击了维度 ${dimension} 的分析按钮，小说ID: ${novelId}`);

            if (!dimension || !novelId) {
                console.error('缺少必要的数据属性: dimension或novel-id');
                alert('无法启动分析：缺少必要的参数');
                return;
            }

            startSingleDimensionAnalysis(novelId, dimension);
        }

        // 处理重新分析按钮点击
        if (e.target && (e.target.classList.contains('reanalyze-btn') ||
                        (e.target.parentElement && e.target.parentElement.classList.contains('reanalyze-btn')))) {
            // 获取按钮元素（可能是图标的父元素）
            const button = e.target.classList.contains('reanalyze-btn') ? e.target : e.target.parentElement;
            const dimension = button.getAttribute('data-dimension');
            const novelId = button.getAttribute('data-novel-id');
            console.log(`点击了维度 ${dimension} 的重新分析按钮，小说ID: ${novelId}`);

            if (!dimension || !novelId) {
                console.error('缺少必要的数据属性: dimension或novel-id');
                alert('无法启动重新分析：缺少必要的参数');
                return;
            }

            if (confirm(`确定要重新分析"${dimension}"维度吗？现有结果将被覆盖。`)) {
                reanalyzeExistingDimension(novelId, dimension);
            }
        }
    });

    // 终止分析按钮事件
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('stop-analysis-btn')) {
            const dimension = e.target.getAttribute('data-dimension');
            const novelId = e.target.getAttribute('data-novel-id');

            if (confirm('确定要终止分析吗？您可以在稍后从断点继续分析。')) {
                stopAnalysis(novelId, dimension);
            }
        }
    });

    // 从断点继续按钮事件
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('resume-analysis-btn')) {
            const dimension = e.target.getAttribute('data-dimension');
            const novelId = e.target.getAttribute('data-novel-id');

            resumeAnalysis(novelId, dimension);
        }
    });

    // 终止分析函数
    function stopAnalysis(novelId, dimension) {
        fetch(`/api/novels/${novelId}/stop_analysis`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                dimension: dimension
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('分析已终止');
                // 刷新页面或更新UI
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                alert('终止分析失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('终止分析请求错误:', error);
            alert('请求错误: ' + error.message);
        });
    }

    // 重新分析已有维度
    function reanalyzeExistingDimension(novelId, dimension) {
        console.log(`开始重新分析维度 ${dimension}，小说ID: ${novelId}`);

        // 先删除现有结果
        fetch(`/api/novels/${novelId}/analysis/${dimension}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`成功删除维度 ${dimension} 的现有结果，开始新的分析`);

                // 删除成功后，开始新的分析
                return fetch(`/api/novels/${novelId}/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dimensions: [dimension],
                        force_refresh: true
                    })
                });
            } else {
                throw new Error(`删除维度 ${dimension} 的现有结果失败: ${data.error || '未知错误'}`);
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`成功启动维度 ${dimension} 的重新分析`);

                // 更新UI，显示分析进度
                const card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    if (cardBody) {
                        // 显示进度条
                        cardBody.innerHTML = `
                            <div class="analysis-progress">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        id="progress-${dimension}"
                                        aria-valuenow="10"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        data-progress="10"
                                        data-min-display="10"
                                        style="width: 10%">
                                        10%
                                    </div>
                                </div>
                                <div class="analysis-status">
                                    <span>分析中...</span>
                                    <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                                </div>
                                <div class="analysis-visualization mt-3">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">正在分析中，请稍候...</p>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }

                // 启动进度更新
                updateAnalysisProgress();
            } else {
                alert(`启动分析失败: ${data.error || '未知错误'}`);
            }
        })
        .catch(error => {
            console.error('重新分析请求出错:', error);
            alert(`请求错误: ${error.message}`);
        });
    }

    // 从断点继续分析函数
    function resumeAnalysis(novelId, dimension) {
        fetch(`/api/novels/${novelId}/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                dimensions: [dimension],
                resume: true
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('从断点继续分析');
                // 更新UI显示分析进行中
                const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
                if (row) {
                    const statusCell = row.querySelector('.dimension-status');
                    const actionCell = row.querySelector('td:last-child');

                    statusCell.innerHTML = `
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                 aria-valuenow="0"
                                 aria-valuemin="0"
                                 aria-valuemax="100"
                                 style="width: 0%">
                                0%
                            </div>
                        </div>
                        <small class="text-muted">预计剩余: 计算中...</small>
                    `;

                    actionCell.innerHTML = `
                        <span class="text-muted">分析中...</span>
                        <button class="btn btn-sm btn-outline-danger stop-analysis-btn ms-2"
                                data-dimension="${dimension}"
                                data-novel-id="${novelIdFromTemplate}">
                            终止
                        </button>
                    `;
                }

                // 更新卡片显示
                let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                if (!card) {
                    card = createDimensionCard(dimension);
                }

                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    if (cardBody) {
                        cardBody.innerHTML = `
                            <div class="analysis-progress">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        id="progress-${dimension}"
                                        aria-valuenow="0"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        data-progress="0"
                                        data-min-display="10"
                                        style="width: 10%">
                                        0%
                                    </div>
                                </div>
                                <div class="analysis-status">
                                    <span>分析中...</span>
                                    <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                                    <button class="btn btn-sm btn-danger stop-analysis-btn mt-2"
                                            data-dimension="${dimension}"
                                            data-novel-id="${novelIdFromTemplate}">
                                        终止分析
                                    </button>
                                </div>
                                <div class="analysis-visualization mt-3">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">正在分析中，请稍候...</p>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }

                // 启动进度更新
                updateAnalysisProgress();
            } else {
                alert('从断点继续分析失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('从断点继续分析请求错误:', error);
            alert('请求错误: ' + error.message);
        });
    }
</script>

<!-- 容器修复脚本 - 必须在其他脚本之前加载 -->
<script src="{{ url_for('static', filename='js/container-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/container-fix.js';"></script>

<!-- 维度内容修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-content-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-content-fix.js';"></script>

<!-- 同步API修复脚本 -->
<script src="{{ url_for('static', filename='js/lib/sync-api-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/lib/sync-api-fix.js';"></script>

<script src="{{ url_for('static', filename='js/console-logger.js') }}"></script>
<script>
    // 在页面加载完成后初始化控制台日志
    function initializeConsoleLogger() {
        // 确保变量已定义
        if (typeof novelIdFromTemplate !== 'undefined' && typeof initConsoleLogger === 'function') {
            try {
                // 初始化控制台日志，传递小说标题
                initConsoleLogger(novelIdFromTemplate, novelTitleFromTemplate);
                console.log('控制台日志初始化成功');

                // 监听分析按钮点击事件
                document.querySelectorAll('.analyze-btn, .analyze-all-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        // 清空控制台
                        if (typeof clearConsole === 'function') {
                            clearConsole();
                        }
                        // 不添加欢迎消息，直接开始轮询
                        if (typeof startLogPolling === 'function') {
                            startLogPolling();
                        }
                    });
                });
            } catch (e) {
                console.error('初始化控制台日志时出错:', e);
            }
        } else {
            console.warn('控制台日志函数未加载，无法初始化控制台');
            // 延迟重试
            setTimeout(initializeConsoleLogger, 500);
        }
    }

    // 当DOM加载完成后，初始化控制台日志
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeConsoleLogger);
    } else {
        // 如果DOM已加载完成，直接初始化
        initializeConsoleLogger();
    }
</script>

<!-- 添加必要的检查，确保完成的分析结果始终可见 -->
function createAnalysisResultsSection() {
    console.log('创建分析结果部分');

    // 获取分析部分容器
    const container = document.getElementById('analysis-section-container');
    if (!container) {
        console.error('找不到分析部分容器');
        return;
    }

    // 检查已有的分析结果卡片
    const existingCards = document.querySelectorAll('.analysis-card');
    if (existingCards.length > 0) {
        console.log(`已存在 ${existingCards.length} 个分析卡片，不需要重新创建`);
        return;
    }

    // 获取分析结果数据
    if (!analysisResultsData || Object.keys(analysisResultsData).length === 0) {
        console.log('没有分析结果数据，无法创建分析结果卡片');
        return;
    }

    console.log(`从 ${Object.keys(analysisResultsData).length} 个分析结果创建分析卡片`);

    // 结果部分标题已经存在，但可能未显示，先删除旧的
    let resultsSectionElement = document.querySelector('.analysis-results-section');
    if (resultsSectionElement) {
        resultsSectionElement.remove();
    }

    // 创建一个新的结果部分
    resultsSectionElement = document.createElement('div');
    resultsSectionElement.className = 'analysis-results-section mt-4';

    // 创建标题
    const titleElement = document.createElement('h3');
    titleElement.className = 'mb-3';
    titleElement.textContent = '分析结果';
    resultsSectionElement.appendChild(titleElement);

    // 创建行布局
    const rowElement = document.createElement('div');
    rowElement.className = 'row';
    resultsSectionElement.appendChild(rowElement);

    // 遍历分析结果数据，创建卡片
    for (const dimension in analysisResultsData) {
        const result = analysisResultsData[dimension];

        // 创建列
        const colElement = document.createElement('div');
        colElement.className = 'col-md-4 mb-4';
        rowElement.appendChild(colElement);

        // 创建卡片
        const cardElement = document.createElement('div');
        cardElement.className = 'card analysis-card h-100';
        cardElement.setAttribute('data-dimension', dimension);
        colElement.appendChild(cardElement);

        // 卡片标题
        const cardHeader = document.createElement('div');
        cardHeader.className = 'card-header';
        cardElement.appendChild(cardHeader);

        const cardTitle = document.createElement('h5');
        cardTitle.className = 'card-title';
        cardHeader.appendChild(cardTitle);

        // 设置维度名称
        cardTitle.textContent = getDimensionName(dimension);

        // 卡片内容
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body d-flex flex-column';
        cardElement.appendChild(cardBody);

        // 状态栏
        const statusBar = document.createElement('div');
        statusBar.className = 'd-flex justify-content-between mb-2';
        cardBody.appendChild(statusBar);

        const statusBadge = document.createElement('span');
        statusBadge.className = 'badge bg-success';
        statusBadge.textContent = '分析完成';
        statusBar.appendChild(statusBadge);

        // 按钮组
        const buttonGroup = document.createElement('div');
        statusBar.appendChild(buttonGroup);

        // 重新分析按钮
        const reanalyzeButton = document.createElement('button');
        reanalyzeButton.className = 'btn btn-sm btn-outline-secondary me-1 reanalyze-btn';
        reanalyzeButton.setAttribute('data-dimension', dimension);
        reanalyzeButton.setAttribute('data-novel-id', novelId);
        reanalyzeButton.innerHTML = '<i class="fas fa-sync"></i> 重新分析';
        buttonGroup.appendChild(reanalyzeButton);

        // 查看详情按钮
        const viewButton = document.createElement('a');
        viewButton.className = 'btn btn-sm btn-outline-primary view-analysis-btn';
        viewButton.setAttribute('data-dimension', dimension);
        viewButton.setAttribute('data-novel-id', novelId);
        viewButton.href = `/novel/${novelId}/analysis/${dimension}`;
        viewButton.textContent = '查看详情';
        buttonGroup.appendChild(viewButton);

        // 分析内容摘要
        const excerptElement = document.createElement('div');
        excerptElement.className = 'analysis-excerpt mt-2 flex-grow-1';
        cardBody.appendChild(excerptElement);

        // 提取内容摘要
        let excerpt = '';
        try {
            // 去除HTML标签，只保留文本内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = result.content || '';
            const textContent = tempDiv.textContent || tempDiv.innerText || '';
            excerpt = textContent.substring(0, 300) + '...';
        } catch (e) {
            console.error(`提取摘要时出错:`, e);
            excerpt = (result.content || '').substring(0, 300) + '...';
        }

        excerptElement.textContent = excerpt;
    }

    // 添加到容器
    container.appendChild(resultsSectionElement);
    console.log('成功创建分析结果部分');

    // 添加事件监听器
    addAnalysisCardEventListeners();
}

// 添加分析卡片事件监听器
function addAnalysisCardEventListeners() {
    console.log('添加分析卡片事件监听器');

    // 重新分析按钮
    document.querySelectorAll('.reanalyze-btn').forEach(button => {
        button.addEventListener('click', function() {
            const dimension = this.getAttribute('data-dimension');
            const novelId = this.getAttribute('data-novel-id');
            console.log(`重新分析: ${dimension}, novelId: ${novelId}`);
            startSingleDimensionAnalysis(novelId, dimension);
        });
    });

    // 查看分析详情按钮
    document.querySelectorAll('.view-analysis-btn').forEach(button => {
        button.addEventListener('click', function(event) {
            if (event.ctrlKey || event.metaKey) {
                // 如果按下Ctrl键或Command键，使用默认行为（在新标签页中打开）
                return;
            }

            // 阻止默认行为
            event.preventDefault();

            const dimension = this.getAttribute('data-dimension');
            const novelId = this.getAttribute('data-novel-id');
            const url = `/novel/${novelId}/analysis/${dimension}`;

            console.log(`查看分析详情: ${dimension}, novelId: ${novelId}, url: ${url}`);

            // 显示加载指示器
            showLoadingOverlay();

            // 打开URL
            window.location.href = url;
        });
    });
}

// 获取维度名称
function getDimensionName(dimension) {
    const dimensionMap = {
        'language_style': '语言风格',
        'rhythm_pacing': '节奏与节奏',
        'structure': '结构分析',
        'sentence_variation': '句式变化',
        'paragraph_length': '段落长度',
        'perspective_shifts': '视角转换',
        'paragraph_flow': '段落流畅度',
        'novel_characteristics': '小说特点',
        'world_building': '世界构建',
        'chapter_outline': '章节大纲',
        'character_relationships': '人物关系',
        'opening_effectiveness': '开篇效果',
        'climax_pacing': '高潮节奏'
    };

    return dimensionMap[dimension] || dimension;
}

// 整合更新维度状态和创建分析卡片的功能
function updateDimensionAndResults() {
    console.log('更新维度状态和分析结果');

    // 确保novelId是有效的
    let novelId;
    try {
        novelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
    } catch (e) {
        console.error('解析novelId出错:', e);
        novelId = novelIdFromTemplate;
    }

    // 获取分析容器
    const analysisSection = document.getElementById('analysis-section-container');
    if (!analysisSection) {
        console.error('找不到分析部分容器');
        return;
    }

    // 获取所有分析结果和进度
    Promise.all([
        fetch(`/api/novels/${novelId}/analysis`).then(res => res.json()),
        fetch(`/api/analysis/progress?novel_id=${novelId}`).then(res => res.json())
    ])
    .then(([results, progress]) => {
        console.log('获取到分析数据:');
        console.log('- 结果数量:', results ? results.length : 0);
        console.log('- 进度状态:', progress ? '有效' : '无效');

        // 记录分析维度完成情况
        let completedCount = 0;
        let totalCount = 13; // 我们有13个分析维度

        // 将结果转换为以维度为键的对象
        const resultsData = {};
        if (results && Array.isArray(results)) {
            results.forEach(result => {
                if (result && result.dimension) {
                    resultsData[result.dimension] = result;
                    completedCount++;
                }
            });
        }

        // 更新全局分析结果数据
        analysisResultsData = resultsData;

        // 更新维度状态表格
        updateDimensionStatusTable(resultsData, progress);

        // 更新维度完成状态信息
        updateCompletionStatus(completedCount, totalCount);

        // 检查是否需要刷新分析卡片部分
        const existingCards = document.querySelectorAll('.analysis-card');
        const existingResultsSection = document.querySelector('.analysis-results-section');

        // 如果分析完成但没有卡片，或者卡片数量与结果不一致，需要重新创建
        const needRecreateDimensionCards =
            (completedCount > 0 && existingCards.length === 0) ||
            (completedCount > 0 && existingCards.length !== completedCount) ||
            (completedCount > 0 && !existingResultsSection);

        if (needRecreateDimensionCards) {
            console.log('需要重新创建分析卡片:', {
                completedCount,
                existingCardsCount: existingCards.length,
                hasResultsSection: !!existingResultsSection
            });

            // 如果之前的分析结果部分存在，先移除
            if (existingResultsSection) {
                existingResultsSection.remove();
            }

            // 创建分析结果部分
            createAnalysisResultsSection();
        } else {
            console.log('无需重新创建分析卡片:', {
                completedCount,
                existingCardsCount: existingCards.length,
                hasResultsSection: !!existingResultsSection
            });

            // 即使不需要重新创建，也更新现有卡片内容
            updateExistingCards(resultsData);
        }

        // 如果有未完成的分析任务，继续轮询进度
        const isAnalysisRunning = progress && progress.success && progress.is_running;
        if (isAnalysisRunning) {
            console.log('检测到分析任务正在运行，5秒后再次更新');
            setTimeout(updateDimensionAndResults, 5000);
        } else {
            console.log('没有检测到正在运行的分析任务');
        }
    })
    .catch(error => {
        console.error('获取分析数据出错:', error);
    });
}

// 更新维度状态表格
function updateDimensionStatusTable(resultsData, progressData) {
    const table = document.getElementById('dimensions-status-table');
    if (!table) {
        console.error('找不到维度状态表格');
        return;
    }

    // 获取所有维度行
    const rows = table.querySelectorAll('tbody tr[data-dimension]');
    if (rows.length === 0) {
        console.error('维度状态表格中没有找到维度行');
        return;
    }

    // 更新每个维度行
    rows.forEach(row => {
        const dimension = row.getAttribute('data-dimension');
        if (!dimension) return;

        const statusCell = row.querySelector('.dimension-status');
        const progressCell = row.querySelector('.dimension-progress');
        const actionsCell = row.querySelector('.dimension-actions');

        if (!statusCell || !progressCell || !actionsCell) {
            console.error(`无法找到维度 ${dimension} 的单元格元素`);
            return;
        }

        // 检查是否有已完成的结果
        const hasResult = resultsData && resultsData[dimension];

        // 检查是否有进行中的分析
        const isInProgress = progressData &&
                           progressData.success &&
                           progressData.progress &&
                           progressData.progress[dimension] &&
                           progressData.progress[dimension].progress < 100 &&
                           progressData.progress[dimension].progress >= 0;

        // 检查是否有错误
        const hasError = progressData &&
                        progressData.success &&
                        progressData.progress &&
                        progressData.progress[dimension] &&
                        progressData.progress[dimension].progress < 0;

        if (hasResult) {
            // 分析已完成
            statusCell.innerHTML = `<span class="badge bg-success">已完成</span>`;
            progressCell.innerHTML = `<div class="progress" style="height: 20px;">
                <div class="progress-bar bg-success" role="progressbar" style="width: 100%;"
                     aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">100%</div>
            </div>`;
            actionsCell.innerHTML = `
                <a href="/novel/${novelId}/analysis/${dimension}"
                   class="btn btn-sm btn-outline-primary">
                    查看
                </a>
                <button class="btn btn-sm btn-outline-secondary ms-1 analyze-btn"
                        data-dimension="${dimension}"
                        data-novel-id="${novelId}">
                    重新分析
                </button>
            `;
        } else if (isInProgress) {
            // 分析进行中
            const progress = progressData.progress[dimension].progress;
            const estimatedTime = progressData.progress[dimension].estimated_time || '计算中...';

            statusCell.innerHTML = `<span class="badge bg-primary">分析中</span>`;
            progressCell.innerHTML = `<div class="progress" style="height: 20px;">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" 
                     data-progress="${progress}" aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100">${progress}%</div>
            </div>
            <small class="text-muted">预计: ${estimatedTime}</small>`;
            actionsCell.innerHTML = `
                <button class="btn btn-sm btn-outline-danger stop-analysis-btn"
                        data-dimension="${dimension}"
                        data-novel-id="${novelId}">
                    终止
                </button>
            `;
        } else if (hasError) {
            // 分析出错
            statusCell.innerHTML = `<span class="badge bg-danger">失败</span>`;
            progressCell.innerHTML = `<div class="progress" style="height: 20px;">
                <div class="progress-bar bg-danger" role="progressbar" style="width: 100%;"
                     aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">错误</div>
            </div>`;
            actionsCell.innerHTML = `
                <button class="btn btn-sm btn-outline-primary analyze-btn"
                        data-dimension="${dimension}"
                        data-novel-id="${novelId}">
                    重试
                </button>
            `;
        } else {
            // 未分析
            statusCell.innerHTML = `<span class="badge bg-secondary">未分析</span>`;
            progressCell.innerHTML = `<div class="progress" style="height: 20px;">
                <div class="progress-bar" role="progressbar" style="width: 0%;"
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>`;
            actionsCell.innerHTML = `
                <button class="btn btn-sm btn-outline-primary analyze-btn"
                        data-dimension="${dimension}"
                        data-novel-id="${novelId}">
                    分析
                </button>
            `;
        }
    });

    // 重新绑定按钮事件
    bindTableButtons();
}

// 更新已完成分析的卡片
function updateExistingCards(resultsData) {
    // 遍历所有分析卡片
    document.querySelectorAll('.analysis-card').forEach(card => {
        const dimension = card.getAttribute('data-dimension');
        if (!dimension) return;

        const result = resultsData[dimension];
        if (!result) return;

        // 更新卡片内容
        const cardBody = card.querySelector('.card-body');
        if (!cardBody) return;

        // 提取内容摘要
        let excerpt = '';
        try {
            // 去除HTML标签，只保留文本内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = result.content || '';
            const textContent = tempDiv.textContent || tempDiv.innerText || '';
            excerpt = textContent.substring(0, 300) + '...';
        } catch (e) {
            console.error(`提取摘要时出错:`, e);
            excerpt = (result.content || '').substring(0, 300) + '...';
        }

        // 仅更新内容摘要部分
        const excerptElement = cardBody.querySelector('.analysis-excerpt');
        if (excerptElement) {
            excerptElement.textContent = excerpt;
        }
    });
}

// 更新分析完成状态信息
function updateCompletionStatus(completedCount, totalCount) {
    // 更新状态标签
    const statusBadge = document.querySelector('.d-flex.justify-content-between.align-items-center .badge');
    if (statusBadge) {
        if (completedCount === totalCount) {
            statusBadge.className = 'badge bg-success p-2';
            statusBadge.textContent = `${completedCount}/${totalCount} 分析已完成`;
        } else {
            statusBadge.className = 'badge bg-warning p-2';
            statusBadge.textContent = `${completedCount}/${totalCount} 分析未完成`;
        }
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，初始化分析功能');

    // 确保全局变量已设置
    if (typeof novelIdFromTemplate === 'undefined') {
        novelIdFromTemplate = document.querySelector('meta[name="novel-id"]')?.content || '0';
        console.log('从meta标签获取novelId:', novelIdFromTemplate);
    }

    // 绑定按钮事件
    bindTableButtons();

    // 初始化分析状态和结果卡片
    updateDimensionAndResults();

    // 每60秒自动刷新一次状态和结果
    setInterval(function() {
        console.log('定时刷新分析数据');
        updateDimensionAndResults();
    }, 60000);
});

// 绑定表格按钮事件
function bindTableButtons() {
    // 分析按钮
    document.querySelectorAll('.analyze-btn').forEach(button => {
        button.addEventListener('click', function() {
            const dimension = this.getAttribute('data-dimension');
            const novelId = this.getAttribute('data-novel-id');
            console.log(`开始分析: ${dimension}, novelId: ${novelId}`);
            startSingleDimensionAnalysis(novelId, dimension);
        });
    });

    // 终止按钮
    document.querySelectorAll('.stop-analysis-btn').forEach(button => {
        button.addEventListener('click', function() {
            const dimension = this.getAttribute('data-dimension');
            const novelId = this.getAttribute('data-novel-id');
            console.log(`终止分析: ${dimension}, novelId: ${novelId}`);
            stopAnalysis(novelId, dimension);
        });
    });
}

// 加载小说详情页历史分析修复脚本
loadScript("{{ url_for('static', filename='js/novel-detail-historical-fix.js') }}", true);

// 辅助函数：加载脚本
function loadScript(src, critical = false) {
    const script = document.createElement('script');
    script.src = src;
    if (critical) {
        script.setAttribute('data-critical', 'true');
    }
    script.onload = function() {
        console.log(`脚本已加载: ${src}`);
    };
    script.onerror = function() {
        console.error(`脚本加载失败: ${src}`);
    };
    document.head.appendChild(script);
}
{% endblock %}

{% block extra_js %}
<!-- 小说详情页历史分析修复脚本 -->
<script src="{{ url_for('static', filename='js/novel-detail-historical-fix.js') }}" data-critical="true"></script>

<!-- 设置进度条宽度的脚本 -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 设置所有进度条的宽度
        document.querySelectorAll('.progress-bar[data-progress]').forEach(function(progressBar) {
            const progress = progressBar.getAttribute('data-progress');
            if (progress) {
                progressBar.style.width = progress + '%';
            }
        });
        
        // 监听DOM变化，设置新添加的进度条的宽度
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // 元素节点
                        const progressBars = node.querySelectorAll ? node.querySelectorAll('.progress-bar[data-progress]') : [];
                        progressBars.forEach(function(progressBar) {
                            const progress = progressBar.getAttribute('data-progress');
                            if (progress) {
                                progressBar.style.width = progress + '%';
                            }
                        });
                    }
                });
            });
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    });
</script>
{% endblock %}