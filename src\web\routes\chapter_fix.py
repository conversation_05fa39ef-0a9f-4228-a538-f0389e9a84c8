"""
特别路由文件，用于修复章节分析功能
"""
import logging
import traceback
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.db.connection import Session
from src.utils.chapter_creator import ChapterCreator
import config

chapter_fix_bp = Blueprint('chapter_fix', __name__)
logger = logging.getLogger(__name__)

@chapter_fix_bp.route('/fix/novel/<int:novel_id>/create_chapters')
def create_chapters(novel_id):
    """
    为小说创建章节

    Args:
        novel_id: 小说ID
    """
    try:
        # 使用章节创建工具创建章节
        success, chapters, message = ChapterCreator.create_chapters_for_novel(novel_id)

        if success:
            flash(message, 'success')
            return redirect(url_for('chapter.list_chapters', novel_id=novel_id))
        else:
            flash(f'创建章节失败: {message}', 'error')
            return redirect(url_for('view_novel', novel_id=novel_id))
    except Exception as e:
        logger.error(f"创建章节时出错: {str(e)}")
        logger.error(traceback.format_exc())
        flash(f'创建章节时出错: {str(e)}', 'error')
        return redirect(url_for('view_novel', novel_id=novel_id))

@chapter_fix_bp.route('/fix/novel/<int:novel_id>/chapters_check')
def check_chapters(novel_id):
    """
    检查小说的章节状态

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            flash('小说不存在', 'error')
            return redirect(url_for('novels'))

        # 获取章节
        chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()

        # 获取章节分析结果
        chapter_analysis_counts = {}
        for chapter in chapters:
            count = session.query(ChapterAnalysisResult).filter_by(chapter_id=chapter.id).count()
            chapter_analysis_counts[chapter.id] = count

        return render_template(
            'fix/chapters_check.html',
            novel=novel,
            chapters=chapters,
            chapter_count=len(chapters),
            analysis_counts=chapter_analysis_counts
        )
    except Exception as e:
        logger.error(f"检查章节状态时出错: {str(e)}")
        logger.error(traceback.format_exc())
        flash(f'检查章节状态时出错: {str(e)}', 'error')
        return redirect(url_for('view_novel', novel_id=novel_id))
    finally:
        session.close()

@chapter_fix_bp.route('/fix/novel/<int:novel_id>/direct_chapters')
def direct_chapters(novel_id):
    """
    直接导航到章节列表

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 记录请求信息
        logger.info(f"直接导航到章节列表: novel_id={novel_id}")

        # 获取小说
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            logger.warning(f"小说不存在: novel_id={novel_id}")
            flash('小说不存在', 'error')
            return redirect(url_for('novels'))

        # 获取章节
        chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()

        # 如果没有章节，先创建
        if not chapters:
            logger.info(f"小说没有章节，重定向到创建章节: novel_id={novel_id}")
            return redirect(url_for('chapter_fix.create_chapters', novel_id=novel_id))

        # 有章节，直接进入章节列表
        logger.info(f"小说有章节，重定向到章节列表: novel_id={novel_id}, chapter_count={len(chapters)}")

        # 构建URL并记录
        target_url = url_for('chapter.list_chapters', novel_id=novel_id)
        logger.info(f"重定向目标URL: {target_url}")

        return redirect(target_url)
    except Exception as e:
        logger.error(f"直接导航到章节列表时出错: {str(e)}")
        logger.error(traceback.format_exc())
        flash(f'直接导航到章节列表时出错: {str(e)}', 'error')
        return redirect(url_for('view_novel', novel_id=novel_id))
    finally:
        session.close()

@chapter_fix_bp.route('/fix/novel/<int:novel_id>/direct_summary')
def direct_summary(novel_id):
    """
    直接导航到章节分析汇总

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 记录请求信息
        logger.info(f"直接导航到章节分析汇总: novel_id={novel_id}")

        # 获取小说
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            logger.warning(f"小说不存在: novel_id={novel_id}")
            flash('小说不存在', 'error')
            return redirect(url_for('novels'))

        # 获取章节
        chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()

        # 如果没有章节，先创建
        if not chapters:
            logger.info(f"小说没有章节，重定向到创建章节: novel_id={novel_id}")
            return redirect(url_for('chapter_fix.create_chapters', novel_id=novel_id))

        # 有章节，直接进入章节分析汇总页面
        logger.info(f"小说有章节，重定向到章节分析汇总: novel_id={novel_id}, chapter_count={len(chapters)}")

        # 构建URL并记录
        target_url = url_for('chapter.chapters_summary', novel_id=novel_id)
        logger.info(f"重定向目标URL: {target_url}")

        return redirect(target_url)
    except Exception as e:
        logger.error(f"直接导航到章节分析汇总时出错: {str(e)}")
        logger.error(traceback.format_exc())
        flash(f'直接导航到章节分析汇总时出错: {str(e)}', 'error')
        return redirect(url_for('view_novel', novel_id=novel_id))
    finally:
        session.close()