import os

# 文件路径
template_file = 'src/web/templates/analysis.html'

# 确保文件存在
if not os.path.exists(template_file):
    print(f"错误: 找不到文件 {template_file}")
    exit(1)

print(f"修复 {template_file} 中...")

# 读取文件内容
with open(template_file, 'r', encoding='utf-8') as f:
    lines = f.readlines()

# 创建备份
backup_file = template_file + '.bak'
with open(backup_file, 'w', encoding='utf-8') as f:
    f.writelines(lines)
print(f"已创建备份: {backup_file}")

# 记录原始行
print("原始行:")
for i in range(856, 865):
    if i < len(lines):
        print(f"{i+1}: {lines[i].strip()}")

# 修复else和endif标签
fixed = False
for i in range(856, 865):
    if i < len(lines) and '{%' in lines[i] and ' else' in lines[i]:
        lines[i] = "{% else %}\n"
        fixed = True
        print(f"修复了第{i+1}行: {lines[i].strip()}")
    elif i < len(lines) and '{%' in lines[i] and 'endif' in lines[i]:
        lines[i] = "{% endif %}\n"
        fixed = True
        print(f"修复了第{i+1}行: {lines[i].strip()}")

# 写回修复后的内容
if fixed:
    with open(template_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    print("文件已修复并保存")
else:
    print("没有发现需要修复的问题")

print("修复完成，请重新启动九猫系统进行测试") 