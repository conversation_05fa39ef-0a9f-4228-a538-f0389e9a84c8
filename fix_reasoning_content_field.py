#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
九猫系统 - 推理过程字段修复工具
用于修复推理过程显示"API未返回推理过程"的问题

这个脚本会检查数据库中的分析结果，从元数据中提取推理过程内容，并将其保存到reasoning_content字段中。
"""

import os
import sys
import json
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# 设置日志
import datetime
log_filename = f"fix_reasoning_content_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fix_reasoning_content_field')

# 导入数据库模型和连接
from src.db.connection import Session
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.api_log import ApiLog

def extract_reasoning_content(result):
    """
    从分析结果中提取推理过程内容。

    Args:
        result: AnalysisResult对象

    Returns:
        str: 提取到的推理过程内容，如果没有找到则返回None
    """
    # 初始化变量
    reasoning_content = None

    # 方法1: 检查reasoning_content字段
    if hasattr(result, 'reasoning_content') and result.reasoning_content:
        logger.info(f"分析结果ID {result.id} 已有reasoning_content字段，长度: {len(result.reasoning_content)}")
        return result.reasoning_content

    # 方法2: 从元数据中提取
    if hasattr(result, 'analysis_metadata') and result.analysis_metadata:
        metadata = result.analysis_metadata

        # 如果是字符串，尝试解析为JSON
        if isinstance(metadata, str):
            try:
                metadata = json.loads(metadata)
                logger.info(f"将元数据从字符串解析为JSON")
            except Exception as e:
                logger.error(f"解析元数据时出错: {str(e)}")
                metadata = {}

        # 方法2.1: 直接从metadata的顶层字段查找
        if isinstance(metadata, dict) and 'reasoning_content' in metadata:
            reasoning_content = metadata['reasoning_content']
            logger.info(f"从元数据的reasoning_content字段找到推理过程，长度: {len(reasoning_content)}")
            return reasoning_content

        # 方法2.2: 从response字段查找
        if isinstance(metadata, dict) and 'response' in metadata:
            response = metadata['response']

            if isinstance(response, dict) and 'reasoning_content' in response:
                reasoning_content = response['reasoning_content']
                logger.info(f"从元数据的response.reasoning_content找到推理过程，长度: {len(reasoning_content)}")
                return reasoning_content

            # 如果response是字符串，尝试解析为JSON
            if isinstance(response, str) and ('reasoning_content' in response or '我需要分析' in response):
                try:
                    json_obj = json.loads(response)
                    if isinstance(json_obj, dict) and 'reasoning_content' in json_obj:
                        reasoning_content = json_obj['reasoning_content']
                        logger.info(f"从解析的response字符串中找到推理过程，长度: {len(reasoning_content)}")
                        return reasoning_content
                except:
                    # 如果字符串本身就是推理过程，直接使用
                    if '我需要分析' in response or '首先，我需要' in response:
                        reasoning_content = response
                        logger.info(f"发现response字符串本身就是推理过程，长度: {len(reasoning_content)}")
                        return reasoning_content

    # 方法3: 检查分析结果内容是否包含推理过程
    if hasattr(result, 'content') and result.content:
        content = result.content
        markers = ['我需要分析', '首先，我需要', '嗯，用户让我', '好的，我现在要']

        for marker in markers:
            if marker in content:
                logger.info(f"发现content字段可能包含推理过程，标记: {marker}")

                # 提取该部分
                end_markers = ['# 分析结果', '## 分析结果', '### 分析结果', '总结', '因此']
                start_idx = content.find(marker)
                end_idx = len(content)

                for end_marker in end_markers:
                    pos = content.find(end_marker, start_idx)
                    if pos > 0 and pos < end_idx:
                        end_idx = pos

                # 提取推理过程部分
                reasoning_content = content[start_idx:end_idx].strip()

                if len(reasoning_content) > 50:  # 确保不是太短的内容
                    logger.info(f"从content中提取出推理过程，长度: {len(reasoning_content)}")
                    return reasoning_content

    # 如果所有方法都失败，返回None
    logger.warning(f"无法从分析结果ID {result.id} 中提取推理过程")
    return None

def fix_reasoning_content_field():
    """
    修复推理过程字段，将推理过程内容从元数据中提取出来，并保存到reasoning_content字段中。
    """
    session = Session()
    try:
        # 获取所有分析结果
        results = session.query(AnalysisResult).all()
        logger.info(f"找到 {len(results)} 个分析结果")

        # 统计信息
        fixed_count = 0
        already_fixed_count = 0
        failed_count = 0

        # 遍历所有分析结果
        for result in results:
            logger.info(f"处理分析结果: ID={result.id}, 小说ID={result.novel_id}, 维度={result.dimension}")

            # 检查是否已经有推理过程
            if hasattr(result, 'reasoning_content') and result.reasoning_content:
                logger.info(f"分析结果 {result.id} 已经有推理过程，长度: {len(result.reasoning_content)}")
                already_fixed_count += 1
                continue

            # 提取推理过程
            reasoning_content = extract_reasoning_content(result)

            if reasoning_content:
                # 更新reasoning_content字段
                result.reasoning_content = reasoning_content
                session.commit()
                logger.info(f"成功为分析结果 {result.id} 更新reasoning_content字段，长度: {len(reasoning_content)}")
                fixed_count += 1
            else:
                logger.warning(f"无法为分析结果 {result.id} 提取推理过程")
                failed_count += 1

        # 打印统计信息
        logger.info(f"修复完成: 成功修复 {fixed_count} 个，已修复 {already_fixed_count} 个，失败 {failed_count} 个")

    except Exception as e:
        logger.error(f"修复推理过程字段时出错: {str(e)}")
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    logger.info("开始修复推理过程字段")
    fix_reasoning_content_field()
    logger.info("修复完成")
