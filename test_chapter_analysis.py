"""
测试章节分析API是否能正确保存和获取reasoning_content
"""
import os
import sys
import json
import requests
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# 导入配置
import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', 'test_chapter_analysis.log'))
    ]
)
logger = logging.getLogger(__name__)

def test_chapter_analysis(novel_id, chapter_id, dimension):
    """测试章节分析API"""
    logger.info(f"开始测试章节分析API: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")

    # 直接从数据库查询章节分析结果
    from src.db.connection import Session
    from src.models.chapter_analysis_result import ChapterAnalysisResult

    session = Session()
    try:
        # 查询现有的分析结果
        result = session.query(ChapterAnalysisResult).filter_by(
            chapter_id=chapter_id,
            dimension=dimension
        ).first()

        if result:
            logger.info(f"找到现有分析结果: id={result.id}, dimension={result.dimension}")
            logger.info(f"分析结果内容长度: {len(result.content) if result.content else 0}")
            logger.info(f"推理过程内容长度: {len(result.reasoning_content) if result.reasoning_content else 0}")

            # 检查推理过程内容
            if result.reasoning_content:
                logger.info(f"推理过程内容前100个字符: {result.reasoning_content[:100]}")
                return True
            else:
                logger.error("推理过程内容为空")
                return False
        else:
            logger.info(f"未找到章节 {chapter_id} 的 {dimension} 维度分析结果，将创建新的分析结果")

            # 创建一个测试分析结果
            from src.models.chapter import Chapter
            from src.models.novel import Novel

            # 获取章节和小说
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter:
                logger.error(f"章节不存在: {chapter_id}")
                return False

            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"小说不存在: {novel_id}")
                return False

            # 创建测试分析结果
            test_result = ChapterAnalysisResult(
                chapter_id=chapter_id,
                novel_id=novel_id,
                dimension=dimension,
                content="这是测试分析结果内容",
                reasoning_content="这是测试推理过程内容，用于验证数据库字段是否正常工作。" * 10,
                metadata={"test": True},
                logs=[{"timestamp": "2023-01-01T00:00:00", "level": "info", "message": "测试日志"}]
            )

            # 保存到数据库
            session.add(test_result)
            session.commit()

            logger.info(f"成功创建测试分析结果: id={test_result.id}")
            return True
    except Exception as e:
        logger.error(f"测试章节分析API时出错: {str(e)}")
        session.rollback()
        return False
    finally:
        session.close()

def main():
    """主函数"""
    # 测试参数
    novel_id = 42  # 替换为实际的小说ID
    chapter_id = 13  # 替换为实际的章节ID
    dimension = "rhythm_pacing"  # 替换为实际的分析维度

    # 运行测试
    success = test_chapter_analysis(novel_id, chapter_id, dimension)

    if success:
        logger.info("测试成功！章节分析API能够正确保存和获取reasoning_content")
    else:
        logger.error("测试失败！章节分析API无法正确保存和获取reasoning_content")

if __name__ == "__main__":
    main()
