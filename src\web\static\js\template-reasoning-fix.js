/**
 * 九猫小说分析系统 - 模板推理过程修复脚本
 * 
 * 此脚本用于检测和替换模板化的推理过程内容，确保显示真实的分析内容
 * 版本: 1.0.0
 */

(function() {
    console.log('[模板推理过程修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        // 模板特征
        templateFeatures: [
            '分析思路说明',
            '1. 文本特征提取',
            '2. 结构化分析',
            '3. 角色关系图谱',
            '4. 冲突点识别',
            '5. 主题深度探索',
            '6. 读者情感预测',
            '7. 写作技巧评估',
            '8. 市场定位分析',
            '详细分析',
            '本文通过多层次分析'
        ],
        // 模板内容匹配阈值
        templateMatchThreshold: 3,
        // API路径前缀
        apiPathPrefixes: [
            '/api/novel/',
            '/api/novels/',
            '/v3/api/novel/',
            '/v3/api/novels/',
            '/v3.1/api/novel/',
            '/v3.1/api/novels/',
            '/direct_db/analysis/'
        ]
    };

    // 状态
    const STATE = {
        initialized: false,
        cachedReasoningContent: {}  // 缓存已获取的推理过程内容
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[模板推理过程修复] ${message}`);
        }
    }

    // 初始化
    function init() {
        debugLog('初始化模板推理过程修复脚本');

        // 监听DOM变化，检测推理过程内容
        observeReasoningContent();

        // 监听页面加载完成事件
        window.addEventListener('load', function() {
            setTimeout(checkAllReasoningContent, 1000);
        });

        STATE.initialized = true;
        debugLog('模板推理过程修复脚本初始化完成');
    }

    // 观察DOM变化，检测推理过程内容
    function observeReasoningContent() {
        // 创建MutationObserver
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新的推理过程内容元素
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 检查是否是推理过程内容元素
                            const reasoningContainers = node.querySelectorAll('[data-reasoning-container="true"], #reasoningContent, #chapterReasoningContent');
                            if (reasoningContainers.length > 0) {
                                debugLog(`检测到${reasoningContainers.length}个推理过程内容元素`);
                                reasoningContainers.forEach(checkReasoningContent);
                            } else if (node.id === 'reasoningContent' || node.id === 'chapterReasoningContent' || node.hasAttribute('data-reasoning-container')) {
                                debugLog('检测到推理过程内容元素');
                                checkReasoningContent(node);
                            }

                            // 检查内部元素
                            const innerReasoningContainers = node.querySelectorAll('[data-reasoning-container="true"], #reasoningContent, #chapterReasoningContent');
                            if (innerReasoningContainers.length > 0) {
                                debugLog(`检测到${innerReasoningContainers.length}个内部推理过程内容元素`);
                                innerReasoningContainers.forEach(checkReasoningContent);
                            }
                        }
                    });
                }
            });
        });

        // 配置观察选项
        const observerConfig = {
            childList: true,
            subtree: true
        };

        // 开始观察
        observer.observe(document.body, observerConfig);
        debugLog('已开始观察DOM变化');
    }

    // 检查所有推理过程内容
    function checkAllReasoningContent() {
        debugLog('检查所有推理过程内容');

        // 查找所有推理过程内容元素
        const reasoningContainers = document.querySelectorAll('[data-reasoning-container="true"], #reasoningContent, #chapterReasoningContent');
        debugLog(`找到${reasoningContainers.length}个推理过程内容元素`);

        // 检查每个推理过程内容元素
        reasoningContainers.forEach(checkReasoningContent);
    }

    // 检查推理过程内容
    function checkReasoningContent(container) {
        // 获取推理过程内容
        const content = container.textContent || '';
        debugLog(`检查推理过程内容，长度: ${content.length}`);

        // 检查是否是模板内容
        if (isTemplateContent(content)) {
            debugLog('检测到模板内容，尝试获取真实内容');

            // 获取参数
            const novelId = container.getAttribute('data-novel-id') || getNovelIdFromUrl();
            const chapterId = container.getAttribute('data-chapter-id') || getChapterIdFromUrl();
            const dimension = container.getAttribute('data-dimension') || getDimensionFromUrl();

            if (novelId && dimension) {
                debugLog(`获取参数: novelId=${novelId}, chapterId=${chapterId}, dimension=${dimension}`);

                // 获取真实内容
                getRealReasoningContent(novelId, chapterId, dimension, function(realContent) {
                    if (realContent && !isTemplateContent(realContent)) {
                        debugLog('获取到真实内容，更新显示');

                        // 更新显示
                        updateReasoningContent(container, realContent);
                    }
                });
            } else {
                debugLog('无法获取参数，无法获取真实内容', 'warn');
            }
        } else {
            debugLog('内容不是模板内容，无需修复');
        }
    }

    // 判断内容是否是模板内容
    function isTemplateContent(content) {
        if (!content) return true;

        // 检查是否包含模板特征
        let matchCount = 0;
        for (const feature of CONFIG.templateFeatures) {
            if (content.includes(feature)) {
                matchCount++;
            }
        }

        return matchCount >= CONFIG.templateMatchThreshold;
    }

    // 获取真实的推理过程内容
    function getRealReasoningContent(novelId, chapterId, dimension, callback) {
        // 检查缓存
        const cacheKey = `${novelId}_${chapterId || 'book'}_${dimension}`;
        if (STATE.cachedReasoningContent[cacheKey]) {
            debugLog('使用缓存的推理过程内容');
            callback(STATE.cachedReasoningContent[cacheKey]);
            return;
        }

        // 构建API路径
        let apiPaths = [];
        for (const prefix of CONFIG.apiPathPrefixes) {
            if (chapterId) {
                apiPaths.push(`${prefix}${novelId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`);
            } else {
                apiPaths.push(`${prefix}${novelId}/analysis/${dimension}/reasoning_content`);
            }
        }

        // 尝试所有API路径
        tryApiPaths(apiPaths, 0, function(content) {
            if (content) {
                // 缓存内容
                STATE.cachedReasoningContent[cacheKey] = content;
                callback(content);
            } else {
                // 尝试直接从数据库获取
                const dbApiUrl = '/direct_db/analysis/get_reasoning_content';
                $.ajax({
                    url: dbApiUrl,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        novel_id: novelId,
                        chapter_id: chapterId,
                        dimension: dimension,
                        is_chapter: !!chapterId
                    }),
                    success: function(response) {
                        if (response.success && response.reasoning_content) {
                            // 缓存内容
                            STATE.cachedReasoningContent[cacheKey] = response.reasoning_content;
                            callback(response.reasoning_content);
                        } else {
                            callback(null);
                        }
                    },
                    error: function() {
                        callback(null);
                    }
                });
            }
        });
    }

    // 尝试API路径
    function tryApiPaths(paths, index, callback) {
        if (index >= paths.length) {
            callback(null);
            return;
        }

        const apiUrl = paths[index];
        debugLog(`尝试API路径: ${apiUrl}`);

        $.ajax({
            url: apiUrl,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    // 尝试获取推理过程内容
                    const content = response.reasoning_content || response.content || (response.result && response.result.reasoning_content);
                    if (content && !isTemplateContent(content)) {
                        debugLog(`从API获取到真实内容，长度: ${content.length}`);
                        callback(content);
                    } else {
                        debugLog('API返回的内容仍然是模板内容，尝试下一个API路径');
                        tryApiPaths(paths, index + 1, callback);
                    }
                } else {
                    debugLog(`API返回失败: ${response.error || '未知错误'}`);
                    tryApiPaths(paths, index + 1, callback);
                }
            },
            error: function() {
                debugLog(`API请求失败: ${apiUrl}`);
                tryApiPaths(paths, index + 1, callback);
            }
        });
    }

    // 更新推理过程内容
    function updateReasoningContent(container, content) {
        // 查找内容元素
        const contentElement = container.querySelector('.reasoning-text, pre, .markdown-body');
        if (contentElement) {
            // 更新内容
            contentElement.textContent = content;
            debugLog('已更新推理过程内容元素');
        } else {
            // 创建新的内容元素
            const newContentElement = document.createElement('pre');
            newContentElement.className = 'reasoning-text';
            newContentElement.textContent = content;
            container.innerHTML = '';
            container.appendChild(newContentElement);
            debugLog('已创建新的推理过程内容元素');
        }

        // 如果有marked库，使用marked渲染内容
        if (typeof marked !== 'undefined') {
            try {
                const contentElement = container.querySelector('.reasoning-text, pre, .markdown-body');
                if (contentElement) {
                    contentElement.innerHTML = marked.parse(content);
                    debugLog('已使用marked渲染推理过程内容');
                }
            } catch (e) {
                debugLog(`使用marked渲染内容时出错: ${e.message}`, 'error');
            }
        }
    }

    // 从URL获取小说ID
    function getNovelIdFromUrl() {
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        return match ? match[1] : null;
    }

    // 从URL获取章节ID
    function getChapterIdFromUrl() {
        const match = window.location.pathname.match(/\/chapter\/(\d+)/);
        return match ? match[1] : null;
    }

    // 从URL获取维度
    function getDimensionFromUrl() {
        const match = window.location.pathname.match(/\/analysis\/([^\/]+)/);
        return match ? match[1] : null;
    }

    // 初始化脚本
    $(document).ready(function() {
        init();
    });
})();
