.TH "NPMRC" "5" "June 2022" "" ""
.SH "NAME"
\fBnpmrc\fR \- The npm config files
.SS Description
.P
npm gets its config settings from the command line, environment variables,
and \fBnpmrc\fP files\.
.P
The \fBnpm config\fP command can be used to update and edit the contents of the
user and global npmrc files\.
.P
For a list of available configuration options, see
npm help config\.
.SS Files
.P
The four relevant files are:
.RS 0
.IP \(bu 2
per\-project config file (/path/to/my/project/\.npmrc)
.IP \(bu 2
per\-user config file (~/\.npmrc)
.IP \(bu 2
global config file ($PREFIX/etc/npmrc)
.IP \(bu 2
npm builtin config file (/path/to/npm/npmrc)

.RE
.P
All npm config files are an ini\-formatted list of \fBkey = value\fP parameters\.
Environment variables can be replaced using \fB${VARIABLE_NAME}\fP\|\. For
example:
.P
.RS 2
.nf
prefix = ${HOME}/\.npm\-packages
.fi
.RE
.P
Each of these files is loaded, and config options are resolved in priority
order\.  For example, a setting in the userconfig file would override the
setting in the globalconfig file\.
.P
Array values are specified by adding "[]" after the key name\. For example:
.P
.RS 2
.nf
key[] = "first value"
key[] = "second value"
.fi
.RE
.SS Comments
.P
Lines in \fB\|\.npmrc\fP files are interpreted as comments when they begin with a
\fB;\fP or \fB#\fP character\. \fB\|\.npmrc\fP files are parsed by
npm/ini \fIhttps://github\.com/npm/ini\fR, which specifies this comment syntax\.
.P
For example:
.P
.RS 2
.nf
# last modified: 01 Jan 2016
; Set a new registry for a scoped package
@myscope:registry=https://mycustomregistry\.example\.org
.fi
.RE
.SS Per\-project config file
.P
When working locally in a project, a \fB\|\.npmrc\fP file in the root of the
project (ie, a sibling of \fBnode_modules\fP and \fBpackage\.json\fP) will set
config values specific to this project\.
.P
Note that this only applies to the root of the project that you're running
npm in\.  It has no effect when your module is published\.  For example, you
can't publish a module that forces itself to install globally, or in a
different location\.
.P
Additionally, this file is not read in global mode, such as when running
\fBnpm install \-g\fP\|\.
.SS Per\-user config file
.P
\fB$HOME/\.npmrc\fP (or the \fBuserconfig\fP param, if set in the environment or on
the command line)
.SS Global config file
.P
\fB$PREFIX/etc/npmrc\fP (or the \fBglobalconfig\fP param, if set above): This file
is an ini\-file formatted list of \fBkey = value\fP parameters\.  Environment
variables can be replaced as above\.
.SS Built\-in config file
.P
\fBpath/to/npm/itself/npmrc\fP
.P
This is an unchangeable "builtin" configuration file that npm keeps
consistent across updates\.  Set fields in here using the \fB\|\./configure\fP
script that comes with npm\.  This is primarily for distribution maintainers
to override default configs in a standard and consistent manner\.
.SS See also
.RS 0
.IP \(bu 2
npm help folders
.IP \(bu 2
npm help config
.IP \(bu 2
npm help config
.IP \(bu 2
npm help package\.json
.IP \(bu 2
npm help npm

.RE
