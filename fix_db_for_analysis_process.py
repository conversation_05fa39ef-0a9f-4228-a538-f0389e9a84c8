"""
九猫小说分析系统 - 数据库修复工具
为分析过程记录功能添加必要的数据表
"""
import os
import sys
import logging
import sqlite3
from datetime import datetime

# 配置日志
log_filename = f"fix_db_for_analysis_process_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def backup_database():
    """备份数据库"""
    try:
        backup_filename = f"novels_backup_analysis_process_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        import shutil
        shutil.copy2("novels.db", backup_filename)
        logger.info(f"数据库已备份为 {backup_filename}")
        return True
    except Exception as e:
        logger.error(f"备份数据库失败: {str(e)}")
        return False

def check_table_exists(conn, table_name):
    """检查表是否存在"""
    cursor = conn.cursor()
    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
    return cursor.fetchone() is not None

def create_analysis_process_table(conn):
    """创建分析过程记录表"""
    try:
        cursor = conn.cursor()
        
        # 创建分析过程表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS analysis_process (
            id INTEGER PRIMARY KEY,
            novel_id INTEGER NOT NULL,
            analysis_type VARCHAR(50) NOT NULL,
            step_number INTEGER NOT NULL,
            step_name VARCHAR(255) NOT NULL,
            step_description TEXT,
            input_data TEXT,
            output_data TEXT,
            prompt_used TEXT,
            api_response TEXT,
            tokens_used INTEGER,
            processing_time FLOAT,
            importance_level VARCHAR(20) DEFAULT 'normal',
            error_message TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (novel_id) REFERENCES novels(id)
        )
        """)
        
        # 创建索引
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_analysis_process_novel_id
        ON analysis_process (novel_id)
        """)
        
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_analysis_process_analysis_type
        ON analysis_process (analysis_type)
        """)
        
        conn.commit()
        logger.info("分析过程表创建成功")
        return True
    except Exception as e:
        logger.error(f"创建分析过程表失败: {str(e)}")
        return False

def alter_analysis_results_table(conn):
    """修改分析结果表，添加日志分类和重要性相关字段"""
    try:
        cursor = conn.cursor()
        
        # 检查分析结果表是否存在
        if not check_table_exists(conn, 'analysis_results'):
            logger.error("分析结果表不存在，无法修改")
            return False
        
        # 获取现有列
        cursor.execute("PRAGMA table_info(analysis_results)")
        columns = [row[1] for row in cursor.fetchall()]
        
        # 添加缺失的列
        if 'log_category' not in columns:
            cursor.execute("ALTER TABLE analysis_results ADD COLUMN log_category VARCHAR(50) DEFAULT 'general'")
            logger.info("已添加log_category列到analysis_results表")
        
        if 'log_tags' not in columns:
            cursor.execute("ALTER TABLE analysis_results ADD COLUMN log_tags TEXT")
            logger.info("已添加log_tags列到analysis_results表")
        
        if 'importance_level' not in columns:
            cursor.execute("ALTER TABLE analysis_results ADD COLUMN importance_level VARCHAR(20) DEFAULT 'normal'")
            logger.info("已添加importance_level列到analysis_results表")
        
        if 'has_process_logs' not in columns:
            cursor.execute("ALTER TABLE analysis_results ADD COLUMN has_process_logs BOOLEAN DEFAULT 0")
            logger.info("已添加has_process_logs列到analysis_results表")
        
        conn.commit()
        return True
    except Exception as e:
        logger.error(f"修改分析结果表失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("========== 开始修复数据库以支持分析过程记录 ==========")
    
    # 备份数据库
    if not backup_database():
        logger.error("备份数据库失败，终止操作")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect("novels.db")
        
        # 创建分析过程表
        if create_analysis_process_table(conn):
            logger.info("分析过程表创建/更新成功")
        else:
            logger.error("分析过程表创建/更新失败")
            conn.close()
            return
        
        # 修改分析结果表
        if alter_analysis_results_table(conn):
            logger.info("分析结果表修改成功")
        else:
            logger.error("分析结果表修改失败")
            conn.close()
            return
        
        # 关闭连接
        conn.close()
        
        print("\n✓ 数据库修复完成！已添加分析过程记录相关表结构")
        print("✓ 请尝试使用九猫小说分析系统的分析过程记录功能")
        
    except Exception as e:
        logger.error(f"数据库修复过程中发生错误: {str(e)}")
        print("\n✗ 数据库修复失败")
        print(f"✗ 请查看日志: {log_filename}")
    
    logger.info("========== 数据库修复结束 ==========")

if __name__ == "__main__":
    main() 