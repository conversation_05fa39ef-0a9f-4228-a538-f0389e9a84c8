"""
生成内容模型
"""
import json
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from src.models.base import Base

class GeneratedContent(Base):
    """生成内容模型"""
    __tablename__ = 'generated_contents'

    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    prompt = Column(Text, nullable=True)
    word_count = Column(Integer, default=0)
    reference_template_id = Column(Integer, ForeignKey('reference_templates.id'), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    content_metadata = Column(Text, nullable=True)

    # 关联关系
    reference_template = relationship("ReferenceTemplate", back_populates="generated_contents")

    def __init__(self, title, content, prompt=None, reference_template_id=None, metadata=None):
        self.title = title
        self.content = content
        self.prompt = prompt
        self.reference_template_id = reference_template_id
        self.content_metadata = json.dumps(metadata) if metadata else None
        self.word_count = len(content) if content else 0

    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'prompt': self.prompt,
            'word_count': self.word_count,
            'reference_template_id': self.reference_template_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'metadata': json.loads(self.content_metadata) if self.content_metadata else None
        }

    def __repr__(self):
        return f"<GeneratedContent(id={self.id}, title='{self.title}', word_count={self.word_count})>"
