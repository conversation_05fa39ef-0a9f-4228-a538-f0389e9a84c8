/**
 * 九猫 - Bootstrap折叠组件修复脚本
 * 解决分析结果页面中折叠/展开功能无法正常工作的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('Bootstrap折叠组件修复脚本已加载');

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复Bootstrap折叠组件');
        
        // 检查Bootstrap版本并修复折叠组件
        fixCollapseButtons();
        
        // 修复分析过程折叠区域
        fixAnalysisProcessCollapse();
    });
    
    // 检查Bootstrap版本并修复折叠按钮
    function fixCollapseButtons() {
        try {
            // 检查Bootstrap版本
            const isBootstrap5 = typeof bootstrap !== 'undefined';
            const isBootstrap4 = typeof $ !== 'undefined' && typeof $.fn.collapse !== 'undefined';
            
            console.log('Bootstrap版本检测: Bootstrap 5 - ' + isBootstrap5 + ', Bootstrap 4 - ' + isBootstrap4);
            
            // 查找所有折叠按钮
            const collapseButtons = document.querySelectorAll('[data-toggle="collapse"], [data-bs-toggle="collapse"]');
            console.log('找到 ' + collapseButtons.length + ' 个折叠按钮');
            
            // 修复每个折叠按钮
            collapseButtons.forEach(function(button) {
                // 确保按钮有正确的属性
                if (button.hasAttribute('data-toggle') && !button.hasAttribute('data-bs-toggle')) {
                    // 从Bootstrap 4升级到Bootstrap 5
                    const target = button.getAttribute('data-toggle');
                    if (target === 'collapse') {
                        console.log('将data-toggle="collapse"转换为data-bs-toggle="collapse"');
                        button.setAttribute('data-bs-toggle', 'collapse');
                        
                        // 更新目标属性
                        if (button.hasAttribute('data-target')) {
                            const targetSelector = button.getAttribute('data-target');
                            button.setAttribute('data-bs-target', targetSelector);
                        }
                    }
                }
                
                // 确保按钮有点击事件处理程序
                if (!button._hasCollapseHandler) {
                    button.addEventListener('click', function(event) {
                        event.preventDefault();
                        
                        // 获取目标元素
                        const targetSelector = button.getAttribute('data-bs-target') || button.getAttribute('data-target');
                        if (!targetSelector) {
                            console.warn('折叠按钮没有目标选择器');
                            return;
                        }
                        
                        const targetElement = document.querySelector(targetSelector);
                        if (!targetElement) {
                            console.warn('找不到折叠目标元素: ' + targetSelector);
                            return;
                        }
                        
                        console.log('切换折叠状态: ' + targetSelector);
                        
                        // 根据Bootstrap版本切换折叠状态
                        if (isBootstrap5) {
                            // Bootstrap 5
                            if (typeof bootstrap.Collapse !== 'undefined') {
                                const collapse = new bootstrap.Collapse(targetElement, {
                                    toggle: true
                                });
                            } else {
                                // 手动切换
                                targetElement.classList.toggle('show');
                            }
                        } else if (isBootstrap4) {
                            // Bootstrap 4
                            $(targetSelector).collapse('toggle');
                        } else {
                            // 手动切换
                            targetElement.classList.toggle('show');
                        }
                    });
                    
                    button._hasCollapseHandler = true;
                    console.log('为折叠按钮添加了点击事件处理程序');
                }
            });
        } catch (e) {
            console.error('修复折叠按钮时出错:', e);
        }
    }
    
    // 修复分析过程折叠区域
    function fixAnalysisProcessCollapse() {
        try {
            // 查找分析过程折叠区域
            const analysisProcessCollapse = document.getElementById('analysisProcessCollapse');
            if (!analysisProcessCollapse) {
                console.log('未找到分析过程折叠区域，跳过修复');
                return;
            }
            
            console.log('找到分析过程折叠区域，尝试修复');
            
            // 查找对应的按钮
            const analysisProcessButton = document.querySelector('[data-bs-target="#analysisProcessCollapse"], [data-target="#analysisProcessCollapse"]');
            if (!analysisProcessButton) {
                console.log('未找到分析过程折叠按钮，尝试创建');
                
                // 查找可能的父元素
                const header = document.querySelector('.card-header:has(h5:contains("分析过程"))');
                if (header) {
                    // 创建新按钮
                    const newButton = document.createElement('button');
                    newButton.className = 'btn btn-link';
                    newButton.setAttribute('type', 'button');
                    newButton.setAttribute('data-bs-toggle', 'collapse');
                    newButton.setAttribute('data-bs-target', '#analysisProcessCollapse');
                    newButton.setAttribute('aria-expanded', 'false');
                    newButton.setAttribute('aria-controls', 'analysisProcessCollapse');
                    newButton.textContent = '分析过程';
                    
                    // 添加到页面
                    header.innerHTML = '';
                    const h5 = document.createElement('h5');
                    h5.className = 'mb-0';
                    h5.appendChild(newButton);
                    header.appendChild(h5);
                    
                    console.log('创建了新的分析过程折叠按钮');
                }
            }
            
            // 确保折叠区域有正确的类
            if (!analysisProcessCollapse.classList.contains('collapse')) {
                analysisProcessCollapse.classList.add('collapse');
                console.log('添加了collapse类');
            }
            
            // 查找分析日志折叠区域
            const analysisLogsCollapse = document.getElementById('analysisLogsCollapse');
            if (analysisLogsCollapse) {
                console.log('找到分析日志折叠区域，尝试修复');
                
                // 确保折叠区域有正确的类
                if (!analysisLogsCollapse.classList.contains('collapse')) {
                    analysisLogsCollapse.classList.add('collapse');
                    console.log('添加了collapse类');
                }
            }
        } catch (e) {
            console.error('修复分析过程折叠区域时出错:', e);
        }
    }
    
    // 添加全局辅助函数
    window.toggleCollapse = function(targetId) {
        try {
            const targetElement = document.getElementById(targetId);
            if (!targetElement) {
                console.warn('找不到折叠目标元素: ' + targetId);
                return;
            }
            
            console.log('手动切换折叠状态: ' + targetId);
            
            // 检查Bootstrap版本
            if (typeof bootstrap !== 'undefined' && typeof bootstrap.Collapse !== 'undefined') {
                // Bootstrap 5
                const collapse = new bootstrap.Collapse(targetElement, {
                    toggle: true
                });
            } else if (typeof $ !== 'undefined' && typeof $.fn.collapse !== 'undefined') {
                // Bootstrap 4
                $('#' + targetId).collapse('toggle');
            } else {
                // 手动切换
                targetElement.classList.toggle('show');
            }
        } catch (e) {
            console.error('手动切换折叠状态时出错:', e);
        }
    };
})();
