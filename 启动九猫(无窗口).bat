@echo off
chcp 65001 >nul

echo ===================================
echo   九猫小说分析系统 - 无窗口启动
echo ===================================
echo.
echo 正在启动九猫小说分析系统...
echo.

:: 设置工作目录为脚本所在目录
cd /d %~dp0

:: 检查Python是否安装
echo 检查Python安装...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python。请安装Python并添加到PATH。
    pause
    exit /b 1
)

:: 检查是否存在src/web/app.py
echo 验证src/web/app.py是否存在...
if not exist src\web\app.py (
    echo [错误] 在此目录中未找到src\web\app.py。请将此脚本移动到项目根目录。
    pause
    exit /b 1
)

:: 下载前端依赖库
echo 检查前端依赖...
if not exist "src\web\static\js\lib\jquery.min.js" (
    echo [信息] 正在下载前端依赖...
    python download_libs.py
)

:: 启动VBS脚本
echo [信息] 正在启动系统...
start /b wscript.exe start_ninecats.vbs

echo.
echo [成功] 九猫系统正在后台启动！
echo [信息] 浏览器将在10秒钟后自动打开
echo [信息] 系统将在后台继续运行，此窗口将自动关闭
echo.
echo 正在等待系统启动...

:: 等待5秒后自动关闭窗口
timeout /t 5 /nobreak >nul

:: 自动关闭此窗口
exit
