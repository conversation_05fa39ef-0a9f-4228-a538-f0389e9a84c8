"""
Chapter data model for the 九猫 (Nine Cats) novel analysis system.
"""
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Boolean, ForeignKey
from sqlalchemy.orm import relationship

from src.models.base import Base

# 创建一个函数来替代已弃用的datetime.utcnow
def utc_now():
    """返回当前的UTC时间，替代已弃用的datetime.utcnow"""
    return datetime.now(timezone.utc)

class Chapter(Base):
    """Chapter data model."""

    __tablename__ = "chapters"

    id = Column(Integer, primary_key=True)
    novel_id = Column(Integer, ForeignKey("novels.id"), nullable=False)
    chapter_number = Column(Integer, nullable=False)  # 章节编号
    title = Column(String(255), nullable=True)  # 章节标题
    content = Column(Text, nullable=False)  # 章节内容
    word_count = Column(Integer, nullable=True)  # 章节字数
    is_analyzed = Column(Boolean, default=False)  # 是否已分析
    chapter_metadata = Column(JSON, nullable=True)  # 章节元数据
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # 与Novel的关系
    novel = relationship("Novel", back_populates="chapters")
    
    # 与ChapterAnalysisResult的关系
    analysis_results = relationship("ChapterAnalysisResult", back_populates="chapter", cascade="all, delete-orphan")

    def __init__(
        self,
        novel_id: int,
        chapter_number: int,
        content: str,
        title: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        初始化章节。

        Args:
            novel_id: 小说ID
            chapter_number: 章节编号
            content: 章节内容
            title: 章节标题
            metadata: 额外的元数据
        """
        self.novel_id = novel_id
        self.chapter_number = chapter_number
        self.content = content
        self.title = title
        self.chapter_metadata = metadata or {}
        self.word_count = self._count_words(content)
        self.is_analyzed = False

    def _count_words(self, text: str) -> int:
        """
        计算文本中的字数。

        Args:
            text: 要计算字数的文本

        Returns:
            字数
        """
        # 中文按字符计算，英文按空格分隔计算
        if not text:
            return 0
            
        # 移除空白字符
        text = text.strip()
        
        # 计算中文字符数
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        
        # 计算英文单词数
        english_words = len([word for word in text.split() if any(c.isalpha() for c in word)])
        
        # 返回总字数
        return chinese_chars + english_words

    def get_summary(self) -> Dict[str, Any]:
        """
        获取章节摘要。

        Returns:
            包含章节摘要信息的字典
        """
        return {
            "id": self.id,
            "novel_id": self.novel_id,
            "chapter_number": self.chapter_number,
            "title": self.title,
            "word_count": self.word_count,
            "is_analyzed": self.is_analyzed,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "analysis_count": len(self.analysis_results) if hasattr(self, "analysis_results") else 0,
            "metadata": self.chapter_metadata
        }

    def to_dict(self) -> Dict[str, Any]:
        """
        将章节转换为字典。

        Returns:
            章节的字典表示
        """
        return {
            "id": self.id,
            "novel_id": self.novel_id,
            "chapter_number": self.chapter_number,
            "title": self.title,
            "content": self.content,
            "word_count": self.word_count,
            "is_analyzed": self.is_analyzed,
            "metadata": self.chapter_metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
