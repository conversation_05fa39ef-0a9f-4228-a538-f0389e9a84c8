"""
九猫 - 修复数据库脚本
修复analysis_results表中缺少的analysis_logs列
"""

import os
import sys
import sqlite3
import logging
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("fix_database.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def find_database_file():
    """查找数据库文件"""
    # 可能的数据库文件路径
    possible_paths = [
        'data/novels.db',
        'src/data/novels.db',
        'novels.db',
        'instance/novels.db',
        'data/app.db',
        'src/data/app.db',
        'app.db',
        'instance/app.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"找到数据库文件: {path}")
            return path
    
    # 如果没有找到，搜索当前目录及其子目录
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                path = os.path.join(root, file)
                logger.info(f"找到数据库文件: {path}")
                return path
    
    logger.error("未找到数据库文件")
    return None

def fix_database():
    """修复数据库"""
    # 查找数据库文件
    db_path = find_database_file()
    if not db_path:
        logger.error("无法找到数据库文件，修复失败")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查analysis_results表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='analysis_results'")
        if not cursor.fetchone():
            logger.error("analysis_results表不存在，无法修复")
            conn.close()
            return False
        
        # 检查列是否已存在
        cursor.execute("PRAGMA table_info(analysis_results)")
        columns = [row[1] for row in cursor.fetchall()]
        
        # 如果analysis_logs列不存在，添加它
        if 'analysis_logs' not in columns:
            logger.info("analysis_logs列不存在，正在添加...")
            
            try:
                # 开始事务
                cursor.execute("BEGIN TRANSACTION")
                
                # 添加analysis_logs列
                cursor.execute("ALTER TABLE analysis_results ADD COLUMN analysis_logs TEXT")
                
                # 提交事务
                conn.commit()
                logger.info("成功添加analysis_logs列")
            except Exception as e:
                # 回滚事务
                conn.rollback()
                logger.error(f"添加analysis_logs列时出错: {str(e)}")
                conn.close()
                return False
        else:
            logger.info("analysis_logs列已存在，无需添加")
        
        # 检查analysis_metadata列是否存在
        if 'analysis_metadata' not in columns:
            logger.info("analysis_metadata列不存在，正在添加...")
            
            try:
                # 开始事务
                cursor.execute("BEGIN TRANSACTION")
                
                # 添加analysis_metadata列
                cursor.execute("ALTER TABLE analysis_results ADD COLUMN analysis_metadata TEXT")
                
                # 提交事务
                conn.commit()
                logger.info("成功添加analysis_metadata列")
            except Exception as e:
                # 回滚事务
                conn.rollback()
                logger.error(f"添加analysis_metadata列时出错: {str(e)}")
                conn.close()
                return False
        else:
            logger.info("analysis_metadata列已存在，无需添加")
        
        # 检查是否有空的analysis_logs值，并设置为空数组
        cursor.execute("SELECT id FROM analysis_results WHERE analysis_logs IS NULL")
        null_logs_ids = [row[0] for row in cursor.fetchall()]
        
        if null_logs_ids:
            logger.info(f"发现 {len(null_logs_ids)} 条记录的analysis_logs为NULL，正在修复...")
            
            try:
                # 开始事务
                cursor.execute("BEGIN TRANSACTION")
                
                # 更新空的analysis_logs值为空数组
                empty_array_json = json.dumps([])
                for record_id in null_logs_ids:
                    cursor.execute(
                        "UPDATE analysis_results SET analysis_logs = ? WHERE id = ?",
                        (empty_array_json, record_id)
                    )
                
                # 提交事务
                conn.commit()
                logger.info("成功修复空的analysis_logs值")
            except Exception as e:
                # 回滚事务
                conn.rollback()
                logger.error(f"修复空的analysis_logs值时出错: {str(e)}")
                conn.close()
                return False
        
        # 检查是否有空的analysis_metadata值，并设置为空对象
        cursor.execute("SELECT id FROM analysis_results WHERE analysis_metadata IS NULL")
        null_metadata_ids = [row[0] for row in cursor.fetchall()]
        
        if null_metadata_ids:
            logger.info(f"发现 {len(null_metadata_ids)} 条记录的analysis_metadata为NULL，正在修复...")
            
            try:
                # 开始事务
                cursor.execute("BEGIN TRANSACTION")
                
                # 更新空的analysis_metadata值为空对象
                empty_object_json = json.dumps({})
                for record_id in null_metadata_ids:
                    cursor.execute(
                        "UPDATE analysis_results SET analysis_metadata = ? WHERE id = ?",
                        (empty_object_json, record_id)
                    )
                
                # 提交事务
                conn.commit()
                logger.info("成功修复空的analysis_metadata值")
            except Exception as e:
                # 回滚事务
                conn.rollback()
                logger.error(f"修复空的analysis_metadata值时出错: {str(e)}")
                conn.close()
                return False
        
        # 关闭连接
        conn.close()
        
        logger.info("数据库修复完成")
        return True
    except Exception as e:
        logger.error(f"修复数据库时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始修复数据库...")
    
    success = fix_database()
    
    if success:
        logger.info("数据库修复成功")
    else:
        logger.error("数据库修复失败")

if __name__ == "__main__":
    main()
