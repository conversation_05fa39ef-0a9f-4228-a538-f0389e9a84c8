@echo off
cd /d "E:\艹，又来一次\九猫"
echo 当前工作目录: %CD%
set DEBUG=False
set USE_REAL_API=True
set USE_MOCK_API=False
set ENABLE_MOCK_ANALYSIS=False
set HOST=127.0.0.1
set MEMORY_OPTIMIZED=True
set LOW_MEMORY_MODE=True
set MEMORY_WARNING_THRESHOLD=75
set MEMORY_CRITICAL_THRESHOLD=85
set MAX_DB_CONNECTIONS=30
set DB_POOL_SIZE=30
set DB_MAX_OVERFLOW=20
set MEMORY_STATS_DIR=E:\艹，又来一次\九猫\memory_stats
set DISABLE_AUTO_REFRESH=True
set MEMORY_CHECK_INTERVAL=3
set DISABLE_CHARTS=True
set OPTIMIZE_DIMENSION_DETAIL=True
set ENABLE_LOG_FILTER=True
set SEPARATE_ANALYSIS_PROCESS=True
set ENABLE_BUTTON_TEXT_SUPREME_FIX=True
set FORCE_BUTTON_TEXT_VISIBILITY=True
set DEEPSEEK_API_KEY=***********************************
set QWEN_API_KEY=sk-6f3b4c6ad9f64f78b22bed422c5d278d
set DEFAULT_MODEL=deepseek-r1
set FORCE_REANALYSIS=True
set FORCE_REAL_API=True
set RELOAD_CONFIG=True
set VERSION=3.1
set TOTAL_DIMENSIONS=15
set ENABLE_ANALYSIS_STATUS_FIX=True
if not exist logs mkdir logs
echo 检查Python是否可用...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo Python未找到，请确保Python已安装并添加到PATH环境变量中
    pause
    exit /b 1
)
echo 启动九猫v3.1系统...
python -u -m src.web.v3_1_app
if %ERRORLEVEL% NEQ 0 (
    echo 启动失败，错误代码: %ERRORLEVEL%
    pause
)
