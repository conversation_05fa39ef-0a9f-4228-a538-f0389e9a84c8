/**
 * 九猫 - 小说详情页面按钮修复脚本
 * 专门用于修复小说详情页面的功能按钮
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._novelDetailButtonFixLoaded) {
        console.log('[小说详情按钮修复] 脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._novelDetailButtonFixLoaded = true;
    
    console.log('[小说详情按钮修复] 小说详情页面按钮修复脚本已加载 - 版本1.0.0');
    
    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[小说详情按钮修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    function safeError(message) {
        try {
            originalConsoleError.call(console, '[小说详情按钮修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 检查是否在小说详情页面
    function isNovelDetailPage() {
        const path = window.location.pathname;
        return /^\/novel\/\d+\/?$/.test(path);
    }
    
    // 获取小说ID
    function getNovelId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
            
            // 从页面元素中获取
            const novelContainer = document.querySelector('[data-novel-id]');
            if (novelContainer && novelContainer.dataset.novelId) {
                return novelContainer.dataset.novelId;
            }
            
            // 从全局变量中获取
            if (window.novelId) {
                return window.novelId;
            }
            
            if (window.novelIdFromTemplate) {
                return window.novelIdFromTemplate;
            }
            
            return null;
        } catch (e) {
            safeError('获取小说ID时出错: ' + e.message);
            return null;
        }
    }
    
    // 修复章节分析按钮
    function fixChapterAnalysisButtons() {
        safeLog('修复章节分析按钮');
        
        // 获取小说ID
        const novelId = getNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法修复章节分析按钮');
            return;
        }
        
        // 查找所有可能的章节分析按钮
        const buttons = document.querySelectorAll('a.btn, button.btn, a[href*="chapter"], [data-target*="chapter"]');
        
        buttons.forEach(btn => {
            if (!btn.textContent) return;
            
            // 检查是否是章节分析按钮
            const text = btn.textContent.trim().toLowerCase();
            if (text.includes('章节') && 
               (text.includes('分析') || text.includes('列表') || text.includes('管理'))) {
                
                safeLog('找到章节分析按钮: ' + btn.textContent);
                
                // 修复链接和点击事件
                if (btn.tagName === 'A') {
                    // 保存原始链接用于调试
                    const originalHref = btn.getAttribute('href');
                    if (originalHref) {
                        btn.setAttribute('data-original-href', originalHref);
                    }
                    
                    // 设置正确的链接
                    btn.href = `/novel/${novelId}/chapters`;
                    
                    safeLog('修复章节分析链接: ' + (originalHref || '无') + ' -> /novel/' + novelId + '/chapters');
                }
                
                // 设置点击事件 - 使用更可靠的方式处理点击
                if (!btn.__chapterButtonFixed) {
                    btn.__chapterButtonFixed = true;
                    
                    // 移除所有现有的点击事件处理函数
                    btn.onclick = null;
                    
                    // 设置新的onclick处理函数，优先级高于addEventListener
                    btn.onclick = function(e) {
                        // 阻止默认行为和事件冒泡
                        if (e && e.preventDefault) e.preventDefault();
                        if (e && e.stopPropagation) e.stopPropagation();
                        
                        // 记录点击
                        safeLog('点击章节分析按钮，跳转到: /novel/' + novelId + '/chapters');
                        
                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = `/novel/${novelId}/chapters`;
                        }, 0);
                        
                        return false;
                    };
                    
                    // 添加点击事件监听器，确保捕获阶段处理
                    btn.addEventListener('click', function(e) {
                        // 阻止默认行为和事件冒泡
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // 记录点击
                        safeLog('通过addEventListener捕获章节分析按钮点击，跳转到: /novel/' + novelId + '/chapters');
                        
                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = `/novel/${novelId}/chapters`;
                        }, 0);
                        
                        return false;
                    }, true);
                }
            }
        });
    }
    
    // 修复维度分析按钮
    function fixDimensionAnalysisButtons() {
        safeLog('修复维度分析按钮');
        
        // 获取小说ID
        const novelId = getNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法修复维度分析按钮');
            return;
        }
        
        // 查找所有维度分析按钮
        const dimensionButtons = document.querySelectorAll('[data-dimension], .dimension-btn, .analyze-btn, .view-btn');
        
        dimensionButtons.forEach(btn => {
            // 获取维度
            let dimension = btn.getAttribute('data-dimension');
            
            // 如果没有data-dimension属性，尝试从类名或其他属性中获取
            if (!dimension) {
                // 从类名中获取
                const classList = Array.from(btn.classList);
                for (const cls of classList) {
                    if (cls.startsWith('dimension-') || cls.startsWith('analyze-') || cls.startsWith('view-')) {
                        const match = cls.match(/^(?:dimension|analyze|view)-(.+)$/);
                        if (match && match[1]) {
                            dimension = match[1];
                            break;
                        }
                    }
                }
                
                // 从其他属性中获取
                if (!dimension) {
                    dimension = btn.getAttribute('data-target') || 
                               btn.getAttribute('data-type') || 
                               btn.getAttribute('data-analysis');
                }
            }
            
            if (dimension) {
                safeLog('找到维度分析按钮: ' + dimension);
                
                // 修复链接和点击事件
                if (btn.tagName === 'A') {
                    // 保存原始链接用于调试
                    const originalHref = btn.getAttribute('href');
                    if (originalHref) {
                        btn.setAttribute('data-original-href', originalHref);
                    }
                    
                    // 设置正确的链接
                    btn.href = `/novel/${novelId}/analysis/${dimension}`;
                    
                    safeLog('修复维度分析链接: ' + (originalHref || '无') + ' -> /novel/' + novelId + '/analysis/' + dimension);
                }
                
                // 设置点击事件
                if (!btn.__dimensionButtonFixed) {
                    btn.__dimensionButtonFixed = true;
                    
                    // 移除所有现有的点击事件处理函数
                    btn.onclick = null;
                    
                    // 设置新的onclick处理函数
                    btn.onclick = function(e) {
                        // 阻止默认行为和事件冒泡
                        if (e && e.preventDefault) e.preventDefault();
                        if (e && e.stopPropagation) e.stopPropagation();
                        
                        // 记录点击
                        safeLog('点击维度分析按钮，跳转到: /novel/' + novelId + '/analysis/' + dimension);
                        
                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = `/novel/${novelId}/analysis/${dimension}`;
                        }, 0);
                        
                        return false;
                    };
                    
                    // 添加点击事件监听器，确保捕获阶段处理
                    btn.addEventListener('click', function(e) {
                        // 阻止默认行为和事件冒泡
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // 记录点击
                        safeLog('通过addEventListener捕获维度分析按钮点击，跳转到: /novel/' + novelId + '/analysis/' + dimension);
                        
                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = `/novel/${novelId}/analysis/${dimension}`;
                        }, 0);
                        
                        return false;
                    }, true);
                }
            }
        });
    }
    
    // 添加全局点击事件处理
    function addGlobalClickHandler() {
        safeLog('添加全局点击事件处理');
        
        // 如果已经添加过全局点击处理器，不再重复添加
        if (window.__novelDetailButtonGlobalHandlerAdded) {
            return;
        }
        
        // 获取小说ID
        const novelId = getNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法添加全局点击事件处理');
            return;
        }
        
        // 添加全局点击事件处理
        document.addEventListener('click', function(e) {
            // 向上查找最近的按钮或链接
            let target = e.target;
            while (target && target !== document) {
                // 检查是否是章节分析按钮
                if ((target.tagName === 'A' || target.tagName === 'BUTTON' || target.classList.contains('btn')) && 
                    target.textContent && 
                    target.textContent.toLowerCase().includes('章节') && 
                    (target.textContent.toLowerCase().includes('分析') || target.textContent.toLowerCase().includes('列表'))) {
                    
                    // 阻止默认行为和事件冒泡
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 记录点击
                    safeLog('通过全局处理器捕获章节分析按钮点击，跳转到: /novel/' + novelId + '/chapters');
                    
                    // 使用setTimeout确保在所有事件处理完成后跳转
                    setTimeout(function() {
                        window.location.href = `/novel/${novelId}/chapters`;
                    }, 0);
                    
                    return false;
                }
                
                target = target.parentElement;
            }
        }, true);
        
        window.__novelDetailButtonGlobalHandlerAdded = true;
        safeLog('已添加全局点击事件处理');
    }
    
    // 监听DOM变化
    function observeDOMChanges() {
        try {
            // 创建MutationObserver
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // 检查是否有新添加的节点
                    if (mutation.addedNodes.length > 0) {
                        // 延迟执行修复，确保DOM完全更新
                        setTimeout(function() {
                            fixChapterAnalysisButtons();
                            fixDimensionAnalysisButtons();
                        }, 100);
                    }
                });
            });
            
            // 配置观察选项
            const config = { childList: true, subtree: true };
            
            // 开始观察
            observer.observe(document.body, config);
            
            safeLog('已开始监听DOM变化');
        } catch (e) {
            safeError('监听DOM变化时出错: ' + e.message);
        }
    }
    
    // 初始化函数
    function initialize() {
        safeLog('初始化小说详情页面按钮修复');
        
        // 检查是否在小说详情页面
        if (!isNovelDetailPage()) {
            safeLog('当前不是小说详情页面，不执行修复');
            return;
        }
        
        safeLog('检测到小说详情页面，开始执行修复');
        
        // 修复章节分析按钮
        fixChapterAnalysisButtons();
        
        // 修复维度分析按钮
        fixDimensionAnalysisButtons();
        
        // 添加全局点击事件处理
        addGlobalClickHandler();
        
        // 监听DOM变化
        observeDOMChanges();
        
        safeLog('小说详情页面按钮修复完成');
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }
    
    // 导出API，方便调试和手动调用
    window.novelDetailButtonFix = {
        initialize: initialize,
        fixChapterAnalysisButtons: fixChapterAnalysisButtons,
        fixDimensionAnalysisButtons: fixDimensionAnalysisButtons
    };
})();
