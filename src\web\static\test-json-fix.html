<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bootstrap JSON Parse 错误修复测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- 加载修复脚本 -->
    <script src="/static/js/bootstrap-bundle-fix-loader.js"></script>
    <script src="/static/js/json-parse-position-4476-fix.js"></script>
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin-top: 20px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-info {
            color: #0c5460;
        }
        .log-error {
            color: #721c24;
        }
        .log-warning {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">Bootstrap JSON Parse 错误修复测试</h1>
        
        <div id="bootstrapStatus" class="status">
            检查 Bootstrap 状态...
        </div>
        
        <div class="mb-3">
            <button id="testModal" class="btn btn-primary me-2">测试 Modal</button>
            <button id="testCollapse" class="btn btn-success me-2">测试 Collapse</button>
            <button id="testTab" class="btn btn-info me-2">测试 Tab</button>
            <button id="reloadBootstrap" class="btn btn-warning">重新加载 Bootstrap</button>
        </div>
        
        <!-- Modal 测试 -->
        <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">测试 Modal</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        如果你能看到这个对话框，说明 Bootstrap Modal 功能正常工作！
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Collapse 测试 -->
        <div class="mt-4">
            <p>
                <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                    展开/折叠内容
                </button>
            </p>
            <div class="collapse" id="collapseExample">
                <div class="card card-body">
                    如果你能看到这段折叠内容，说明 Bootstrap Collapse 功能正常工作！
                </div>
            </div>
        </div>
        
        <!-- Tab 测试 -->
        <div class="mt-4">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button" role="tab" aria-controls="home" aria-selected="true">首页</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="false">资料</button>
                </li>
            </ul>
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active p-3" id="home" role="tabpanel" aria-labelledby="home-tab">
                    如果你能看到这个标签页内容，说明 Bootstrap Tab 功能正常工作！
                </div>
                <div class="tab-pane fade p-3" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    这是第二个标签页的内容。
                </div>
            </div>
        </div>
        
        <h3 class="mt-4">控制台日志</h3>
        <div id="logContainer" class="log-container"></div>
    </div>
    
    <script>
        // 捕获控制台日志
        (function() {
            const logContainer = document.getElementById('logContainer');
            const originalConsoleLog = console.log;
            const originalConsoleError = console.error;
            const originalConsoleWarn = console.warn;
            
            function addLogEntry(message, type) {
                const entry = document.createElement('div');
                entry.className = `log-entry log-${type}`;
                entry.textContent = message;
                logContainer.appendChild(entry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            
            console.log = function() {
                const message = Array.from(arguments).join(' ');
                addLogEntry(message, 'info');
                originalConsoleLog.apply(console, arguments);
            };
            
            console.error = function() {
                const message = Array.from(arguments).join(' ');
                addLogEntry(message, 'error');
                originalConsoleError.apply(console, arguments);
            };
            
            console.warn = function() {
                const message = Array.from(arguments).join(' ');
                addLogEntry(message, 'warning');
                originalConsoleWarn.apply(console, arguments);
            };
        })();
        
        // 检查 Bootstrap 状态
        function checkBootstrapStatus() {
            const statusElement = document.getElementById('bootstrapStatus');
            
            if (typeof bootstrap !== 'undefined') {
                try {
                    // 尝试创建一个 Modal 实例
                    const modalElement = document.createElement('div');
                    modalElement.className = 'modal';
                    document.body.appendChild(modalElement);
                    
                    const modal = new bootstrap.Modal(modalElement);
                    modal.dispose();
                    document.body.removeChild(modalElement);
                    
                    statusElement.className = 'status status-success';
                    statusElement.textContent = 'Bootstrap 已成功加载并正常工作！';
                    console.log('Bootstrap 状态检查：成功');
                } catch (e) {
                    statusElement.className = 'status status-warning';
                    statusElement.textContent = 'Bootstrap 已加载但功能异常：' + e.message;
                    console.error('Bootstrap 状态检查：功能异常', e);
                }
            } else {
                statusElement.className = 'status status-error';
                statusElement.textContent = 'Bootstrap 未加载或加载失败！';
                console.error('Bootstrap 状态检查：未加载');
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟检查 Bootstrap 状态，给修复脚本一些时间
            setTimeout(checkBootstrapStatus, 1000);
            
            // 测试按钮事件
            document.getElementById('testModal').addEventListener('click', function() {
                try {
                    const modal = new bootstrap.Modal(document.getElementById('exampleModal'));
                    modal.show();
                    console.log('Modal 测试：成功');
                } catch (e) {
                    console.error('Modal 测试：失败', e);
                    alert('Modal 测试失败：' + e.message);
                }
            });
            
            document.getElementById('testCollapse').addEventListener('click', function() {
                try {
                    const collapseElement = document.getElementById('collapseExample');
                    const collapse = new bootstrap.Collapse(collapseElement);
                    collapse.toggle();
                    console.log('Collapse 测试：成功');
                } catch (e) {
                    console.error('Collapse 测试：失败', e);
                    alert('Collapse 测试失败：' + e.message);
                }
            });
            
            document.getElementById('testTab').addEventListener('click', function() {
                try {
                    const tabElement = document.getElementById('profile-tab');
                    const tab = new bootstrap.Tab(tabElement);
                    tab.show();
                    console.log('Tab 测试：成功');
                } catch (e) {
                    console.error('Tab 测试：失败', e);
                    alert('Tab 测试失败：' + e.message);
                }
            });
            
            document.getElementById('reloadBootstrap').addEventListener('click', function() {
                console.log('手动重新加载 Bootstrap...');
                
                // 移除所有现有的 bootstrap.bundle.min.js 脚本
                const scripts = document.querySelectorAll('script');
                for (let i = 0; i < scripts.length; i++) {
                    const src = scripts[i].src || '';
                    if (src.includes('bootstrap.bundle.min.js')) {
                        console.log('移除现有的 bootstrap.bundle.min.js 脚本');
                        scripts[i].parentNode.removeChild(scripts[i]);
                    }
                }
                
                // 触发修复函数
                if (typeof window.fixBootstrapBundle === 'function') {
                    window.fixBootstrapBundle();
                    
                    // 延迟检查状态
                    setTimeout(checkBootstrapStatus, 1000);
                } else {
                    console.error('修复函数不存在');
                    alert('修复函数不存在');
                }
            });
        });
    </script>
</body>
</html>
