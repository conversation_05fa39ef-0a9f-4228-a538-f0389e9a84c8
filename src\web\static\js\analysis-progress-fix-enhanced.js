/**
 * 九猫 - 分析页面进度信息修复脚本（增强版）
 * 修复分析页面进度信息获取和显示问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('[九猫修复] 分析页面进度信息修复脚本（增强版）已加载');

    // 配置
    const CONFIG = {
        // 是否启用调试日志
        debug: true,

        // 进度轮询间隔（毫秒）
        pollInterval: 3000,

        // 最大重试次数
        maxRetries: 5,

        // 是否使用模拟进度（当API失败时）
        useSimulatedProgress: true,

        // 模拟进度的增长速度（每次轮询增加的百分比）
        simulatedProgressIncrement: 5,

        // 模拟进度的最大值（百分比）
        simulatedProgressMax: 95
    };

    // 全局状态
    const STATE = {
        // 当前重试次数
        retryCount: 0,

        // 当前模拟进度
        simulatedProgress: 0,

        // 轮询定时器ID
        pollTimerId: null,

        // 是否正在使用模拟进度
        isUsingSimulatedProgress: false,

        // 最后一次成功获取的进度数据
        lastProgressData: null
    };

    // 调试日志
    function debugLog(...args) {
        if (CONFIG.debug) {
            console.log('[九猫修复-进度]', ...args);
        }
    }

    // 获取当前页面的小说ID和维度
    function getPageInfo() {
        // 从URL中获取
        const match = window.location.pathname.match(/\/novel\/(\d+)\/analysis\/([^\/]+)/);
        if (match && match[1] && match[2]) {
            return {
                novelId: match[1],
                dimension: match[2]
            };
        }

        // 从页面元素中获取
        const novelIdElement = document.querySelector('[data-novel-id]');
        const dimensionElement = document.querySelector('[data-dimension]');

        if (novelIdElement && dimensionElement) {
            return {
                novelId: novelIdElement.dataset.novelId,
                dimension: dimensionElement.dataset.dimension
            };
        }

        // 从全局变量中获取
        if (window.novelId && window.dimension) {
            return {
                novelId: window.novelId,
                dimension: window.dimension
            };
        }

        // 从页面标题中获取
        const titleMatch = document.title.match(/(.+)\s+-\s+(.+)/);
        if (titleMatch) {
            // 尝试从标题中提取维度
            const dimensionMap = {
                '语言风格': 'language_style',
                '节奏与节奏': 'rhythm_pacing',
                '结构分析': 'structure',
                '句式变化': 'sentence_variation',
                '段落长度': 'paragraph_length',
                '视角转换': 'perspective_shifts',
                '段落流畅度': 'paragraph_flow',
                '小说特点': 'novel_characteristics',
                '世界构建': 'world_building',
                '章节大纲': 'chapter_outline',
                '人物关系': 'character_relationships',
                '开篇效果': 'opening_effectiveness',
                '高潮节奏': 'climax_pacing'
            };

            const chineseDimension = titleMatch[1].trim();
            const dimension = dimensionMap[chineseDimension] || chineseDimension;

            // 尝试从URL中提取小说ID
            const idMatch = window.location.pathname.match(/\/novel\/(\d+)/);
            if (idMatch && idMatch[1]) {
                return {
                    novelId: idMatch[1],
                    dimension: dimension
                };
            }
        }

        return null;
    }

    // 获取进度信息
    function fetchProgressInfo(novelId, dimension) {
        return new Promise((resolve, reject) => {
            // 构建API URL
            const apiUrl = `/api/analysis/progress?novel_id=${novelId}&dimension=${dimension}`;
            debugLog(`获取进度信息: ${apiUrl}`);

            // 发送请求
            fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取进度信息成功:', data);

                // 重置重试计数
                STATE.retryCount = 0;

                // 保存最后一次成功获取的进度数据
                STATE.lastProgressData = data;

                // 如果之前在使用模拟进度，现在切换回真实进度
                if (STATE.isUsingSimulatedProgress) {
                    debugLog('从模拟进度切换回真实进度');
                    STATE.isUsingSimulatedProgress = false;
                }

                resolve(data);
            })
            .catch(error => {
                debugLog('获取进度信息失败:', error);

                // 增加重试计数
                STATE.retryCount++;

                if (STATE.retryCount <= CONFIG.maxRetries) {
                    debugLog(`重试 ${STATE.retryCount}/${CONFIG.maxRetries}`);

                    // 如果有最后一次成功获取的进度数据，使用它
                    if (STATE.lastProgressData) {
                        debugLog('使用上次成功获取的进度数据');
                        resolve(STATE.lastProgressData);
                    } else if (CONFIG.useSimulatedProgress) {
                        // 使用模拟进度
                        debugLog('使用模拟进度');
                        STATE.isUsingSimulatedProgress = true;

                        // 增加模拟进度
                        STATE.simulatedProgress += CONFIG.simulatedProgressIncrement;
                        if (STATE.simulatedProgress > CONFIG.simulatedProgressMax) {
                            STATE.simulatedProgress = CONFIG.simulatedProgressMax;
                        }

                        // 创建模拟进度数据
                        const simulatedData = {
                            success: true,
                            is_running: true,
                            progress: STATE.simulatedProgress,
                            estimated_time: '模拟进度',
                            message: '正在分析中...',
                            dimension: dimension,
                            novel_id: novelId
                        };

                        resolve(simulatedData);
                    } else {
                        reject(error);
                    }
                } else {
                    debugLog('超过最大重试次数，放弃');
                    reject(error);
                }
            });
        });
    }

    // 更新进度UI
    function updateProgressUI(data) {
        try {
            // 查找进度条元素
            const progressBar = document.querySelector('.progress-bar');
            if (!progressBar) {
                debugLog('找不到进度条元素');
                return;
            }

            // 获取进度值
            const progress = data.progress || 0;

            // 更新进度条
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
            progressBar.textContent = `${progress}%`;

            // 更新估计时间
            const timeElement = document.querySelector('.estimated-time');
            if (timeElement) {
                timeElement.textContent = `预计剩余时间: ${data.estimated_time || '计算中...'}`;
            }

            // 更新状态消息
            const statusElement = document.querySelector('.status-message');
            if (statusElement) {
                statusElement.textContent = data.message || '正在分析中...';
            }

            // 如果分析已完成，刷新页面
            if (data.progress >= 100 || data.status === 'completed') {
                debugLog('分析已完成，准备刷新页面');

                // 清除轮询定时器
                if (STATE.pollTimerId) {
                    clearInterval(STATE.pollTimerId);
                    STATE.pollTimerId = null;
                }

                // 更新状态显示
                const statusElement = document.querySelector('.status-message, #analysisStatus');
                if (statusElement) {
                    statusElement.textContent = '分析已完成，正在刷新页面...';
                    if (statusElement.classList.contains('badge')) {
                        statusElement.className = statusElement.className.replace(/bg-\w+/g, '') + ' bg-success';
                    }
                }

                // 延迟2秒后刷新页面
                setTimeout(() => {
                    debugLog('刷新页面');
                    window.location.reload();
                }, 2000);
            }
        } catch (error) {
            console.error('[九猫修复-进度] 更新进度UI时出错:', error);
        }
    }

    // 开始轮询进度
    function startPollingProgress() {
        // 获取页面信息
        const pageInfo = getPageInfo();
        if (!pageInfo) {
            console.error('[九猫修复-进度] 无法获取页面信息');
            return;
        }

        debugLog('开始轮询进度:', pageInfo);

        // 立即获取一次进度
        fetchProgressInfo(pageInfo.novelId, pageInfo.dimension)
            .then(data => {
                updateProgressUI(data);
            })
            .catch(error => {
                console.error('[九猫修复-进度] 获取进度信息失败:', error);
            });

        // 设置轮询定时器
        STATE.pollTimerId = setInterval(() => {
            fetchProgressInfo(pageInfo.novelId, pageInfo.dimension)
                .then(data => {
                    updateProgressUI(data);
                })
                .catch(error => {
                    console.error('[九猫修复-进度] 获取进度信息失败:', error);
                });
        }, CONFIG.pollInterval);
    }

    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        debugLog('页面加载完成，初始化进度修复');

        // 检查是否是分析页面
        const isAnalysisPage = window.location.pathname.includes('/analysis/');
        if (!isAnalysisPage) {
            debugLog('不是分析页面，不执行修复');
            return;
        }

        // 开始轮询进度
        startPollingProgress();
    });

    // 如果页面已经加载完成，立即执行
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        debugLog('页面已加载，立即初始化进度修复');

        // 检查是否是分析页面
        const isAnalysisPage = window.location.pathname.includes('/analysis/');
        if (!isAnalysisPage) {
            debugLog('不是分析页面，不执行修复');
            return;
        }

        // 开始轮询进度
        startPollingProgress();
    }

    console.log('[九猫修复] 分析页面进度信息修复脚本（增强版）加载完成');
})();
