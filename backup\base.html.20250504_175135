<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}九猫小说分析系统{% endblock %}</title>

    <!-- 紧急修复脚本，必须最先加载 -->
    {% include 'fix-loader.html' %}
    <script src="/static/js/supreme-fixer.js"></script>
    <script src="/static/js/variable-declaration-fix.js"></script>

    <!-- 预加载关键资源 -->
    <link rel="preload" href="/static/css/bootstrap.min.css" as="style">
    <link rel="preload" href="/static/js/jquery.min.js" as="script">
    <link rel="preload" href="/static/js/bootstrap.bundle.min.js" as="script">

    <!-- 样式表 -->
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/main.css">

    <!-- 加载增强版静态文件管理器 -->
    <script src="/static/js/static-file-manager-enhanced.js"></script>

    <!-- 加载图表禁用脚本 -->
    <script src="/static/js/disable-charts.js"></script>

    <!-- 加载增强版维度详情页面修复脚本 -->
    <script src="/static/js/dimension-detail-fix-enhanced.js"></script>

    <!-- 备用内联基础样式，在CSS加载失败时使用 -->
    <style>
        body { font-family: system-ui, sans-serif; line-height: 1.5; margin: 0; padding: 0; }
        .container { width: 100%; max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        .navbar { display: flex; align-items: center; padding: 8px 16px; background-color: #343a40; color: white; }
        .navbar-brand { font-size: 1.25rem; color: white; text-decoration: none; }
        .card { border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin-bottom: 15px; }
        .btn { display: inline-block; padding: 8px 16px; background: #f8f9fa; border: 1px solid #dee2e6; cursor: pointer; border-radius: 4px; }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">九猫小说分析系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/novels">小说列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">分析记录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p>© 2023 九猫小说分析系统 | 版权所有</p>
        </div>
    </footer>

    <!-- 基础JS库 -->
    <script src="{{ url_for('static', filename='js/lib/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/lib/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/lib/chart.min.js') }}"></script>
    
    <!-- 九猫系统核心修复脚本 -->
    <script src="{{ url_for('static', filename='js/emergency-resource-loader.js') }}"></script>
    <script src="{{ url_for('static', filename='js/universal-chart-fix-enhanced.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dimension-detail-fix-enhanced.js') }}"></script>
    <script src="{{ url_for('static', filename='js/global-error-handler.js') }}"></script>

    <!-- 脚本加载错误处理 -->
    <script>
        // 检测脚本加载失败并尝试从CDN加载
        window.addEventListener('error', function(event) {
            const target = event.target;

            // 只处理脚本加载失败
            if (target.tagName === 'SCRIPT' && event.target.src) {
                const src = event.target.src;
                console.error(`脚本加载失败: ${src}`);

                // 尝试从CDN加载关键脚本
                if (src.includes('jquery.min.js') && typeof jQuery === 'undefined') {
                    console.log('尝试从CDN加载jQuery...');
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';
                    document.head.appendChild(script);
                }

                if (src.includes('bootstrap.bundle.min.js') && typeof bootstrap === 'undefined') {
                    console.log('尝试从CDN加载Bootstrap...');
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
                    document.head.appendChild(script);
                }
            }
        }, true);
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
