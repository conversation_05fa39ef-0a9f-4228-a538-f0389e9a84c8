/**
 * 九猫系统按钮文字紧急修复脚本
 * 解决按钮文字不可见的问题（强制修复版本）
 */

// 立即执行函数
(function() {
    console.log('按钮文字紧急修复脚本已加载');

    // 立即执行修复
    injectEmergencyStyles();
    
    // 在DOM加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM加载完成，执行按钮文字紧急修复');
        fixAllButtons();
        observeButtonChanges();
    });

    // 注入紧急样式
    function injectEmergencyStyles() {
        // 创建样式元素
        const style = document.createElement('style');
        style.id = 'button-text-emergency-fix';
        style.innerHTML = `
            /* 全局按钮修复 */
            .btn, button, input[type="button"], input[type="submit"], input[type="reset"], a.btn {
                color: #333333 !important;
                text-shadow: none !important;
                -webkit-text-fill-color: initial !important;
                opacity: 1 !important;
                visibility: visible !important;
                text-indent: 0 !important;
                font-size: 14px !important;
                font-weight: 400 !important;
                letter-spacing: normal !important;
                background-image: none !important;
            }

            /* 特定按钮类型修复 */
            .btn-primary, button.btn-primary, a.btn-primary {
                color: #ffffff !important;
                background-color: #007bff !important;
                border-color: #007bff !important;
            }

            .btn-secondary, button.btn-secondary, a.btn-secondary {
                color: #ffffff !important;
                background-color: #6c757d !important;
                border-color: #6c757d !important;
            }

            .btn-success, button.btn-success, a.btn-success {
                color: #ffffff !important;
                background-color: #28a745 !important;
                border-color: #28a745 !important;
            }

            .btn-danger, button.btn-danger, a.btn-danger {
                color: #ffffff !important;
                background-color: #dc3545 !important;
                border-color: #dc3545 !important;
            }

            .btn-warning, button.btn-warning, a.btn-warning {
                color: #212529 !important;
                background-color: #ffc107 !important;
                border-color: #ffc107 !important;
            }

            .btn-info, button.btn-info, a.btn-info {
                color: #ffffff !important;
                background-color: #17a2b8 !important;
                border-color: #17a2b8 !important;
            }

            .btn-light, button.btn-light, a.btn-light {
                color: #212529 !important;
                background-color: #f8f9fa !important;
                border-color: #f8f9fa !important;
            }

            .btn-dark, button.btn-dark, a.btn-dark {
                color: #ffffff !important;
                background-color: #343a40 !important;
                border-color: #343a40 !important;
            }

            /* 轮廓按钮修复 */
            .btn-outline-primary, button.btn-outline-primary, a.btn-outline-primary {
                color: #007bff !important;
                background-color: transparent !important;
                border-color: #007bff !important;
            }

            .btn-outline-primary:hover, button.btn-outline-primary:hover, a.btn-outline-primary:hover {
                color: #ffffff !important;
                background-color: #007bff !important;
            }

            .btn-outline-secondary, button.btn-outline-secondary, a.btn-outline-secondary {
                color: #6c757d !important;
                background-color: transparent !important;
                border-color: #6c757d !important;
            }

            .btn-outline-secondary:hover, button.btn-outline-secondary:hover, a.btn-outline-secondary:hover {
                color: #ffffff !important;
                background-color: #6c757d !important;
            }

            /* 确保按钮内的所有元素可见 */
            .btn *, button *, a.btn * {
                color: inherit !important;
                opacity: 1 !important;
                visibility: visible !important;
            }

            /* 修复可能的伪元素问题 */
            .btn:before, .btn:after, button:before, button:after, a.btn:before, a.btn:after {
                display: none !important;
            }

            /* 修复可能的文字颜色渐变问题 */
            .btn, button, a.btn {
                background-clip: initial !important;
                -webkit-background-clip: initial !important;
            }
        `;
        
        // 将样式添加到文档头部
        document.head.appendChild(style);
        console.log('紧急样式已注入');
    }

    // 修复所有按钮
    function fixAllButtons() {
        // 查找所有按钮元素
        const buttons = document.querySelectorAll('.btn, button, input[type="button"], input[type="submit"], input[type="reset"], a.btn');
        console.log('找到 ' + buttons.length + ' 个按钮元素');
        
        // 遍历并修复每个按钮
        buttons.forEach(function(button, index) {
            fixButtonStyles(button, index);
        });
    }

    // 修复单个按钮的样式
    function fixButtonStyles(button, index) {
        // 获取按钮文本
        const buttonText = button.textContent.trim();
        
        // 记录按钮信息
        console.log('修复按钮 #' + index + (buttonText ? ' [' + buttonText + ']' : ''));
        
        // 直接设置内联样式
        button.style.setProperty('color', getButtonColor(button), 'important');
        button.style.setProperty('opacity', '1', 'important');
        button.style.setProperty('visibility', 'visible', 'important');
        button.style.setProperty('text-indent', '0', 'important');
        button.style.setProperty('-webkit-text-fill-color', 'initial', 'important');
        button.style.setProperty('text-shadow', 'none', 'important');
        button.style.setProperty('background-clip', 'initial', 'important');
        button.style.setProperty('-webkit-background-clip', 'initial', 'important');
        
        // 移除可能导致问题的属性
        button.style.removeProperty('background-image');
        
        // 修复按钮内的所有元素
        const children = button.querySelectorAll('*');
        children.forEach(function(child) {
            child.style.setProperty('color', 'inherit', 'important');
            child.style.setProperty('opacity', '1', 'important');
            child.style.setProperty('visibility', 'visible', 'important');
        });
    }

    // 根据按钮类型获取适当的文字颜色
    function getButtonColor(button) {
        if (button.classList.contains('btn-primary') || 
            button.classList.contains('btn-secondary') || 
            button.classList.contains('btn-success') || 
            button.classList.contains('btn-danger') || 
            button.classList.contains('btn-info') || 
            button.classList.contains('btn-dark')) {
            return '#ffffff';
        } else if (button.classList.contains('btn-warning') || 
                  button.classList.contains('btn-light')) {
            return '#212529';
        } else if (button.classList.contains('btn-outline-primary')) {
            return '#007bff';
        } else if (button.classList.contains('btn-outline-secondary')) {
            return '#6c757d';
        } else if (button.classList.contains('btn-outline-success')) {
            return '#28a745';
        } else if (button.classList.contains('btn-outline-danger')) {
            return '#dc3545';
        } else if (button.classList.contains('btn-outline-warning')) {
            return '#ffc107';
        } else if (button.classList.contains('btn-outline-info')) {
            return '#17a2b8';
        } else if (button.classList.contains('btn-outline-light')) {
            return '#f8f9fa';
        } else if (button.classList.contains('btn-outline-dark')) {
            return '#343a40';
        } else {
            return '#333333';
        }
    }

    // 观察DOM变化，处理新添加的按钮
    function observeButtonChanges() {
        // 创建一个观察器实例
        const observer = new MutationObserver(function(mutations) {
            let newButtonsFound = false;
            
            // 检查是否有新的按钮被添加
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        // 检查添加的节点是否是按钮
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.classList && (node.classList.contains('btn') || node.tagName === 'BUTTON')) {
                                fixButtonStyles(node, 'new');
                                newButtonsFound = true;
                            }
                            
                            // 检查添加的节点内是否包含按钮
                            const childButtons = node.querySelectorAll('.btn, button, input[type="button"], input[type="submit"], input[type="reset"], a.btn');
                            if (childButtons.length > 0) {
                                childButtons.forEach(function(button, index) {
                                    fixButtonStyles(button, 'new-child-' + index);
                                });
                                newButtonsFound = true;
                            }
                        }
                    });
                }
            });
            
            // 如果找到新按钮，记录日志
            if (newButtonsFound) {
                console.log('检测到新的按钮元素，已应用修复');
            }
        });
        
        // 配置观察选项
        const config = {
            childList: true,
            subtree: true
        };
        
        // 开始观察整个文档
        observer.observe(document.documentElement, config);
        console.log('已开始观察DOM变化');
    }

    // 在窗口加载完成后再次修复所有按钮
    window.addEventListener('load', function() {
        console.log('窗口加载完成，再次修复所有按钮');
        fixAllButtons();
        
        // 延迟执行一次，确保动态加载的内容也被处理
        setTimeout(fixAllButtons, 1000);
    });
})();
