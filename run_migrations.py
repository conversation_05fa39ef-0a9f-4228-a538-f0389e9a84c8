"""
直接执行数据库迁移的脚本
"""
import os
import sys
import logging
import sqlite3

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', 'migrations.log'))
    ]
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("开始执行数据库迁移...")
    
    # 获取数据库文件路径
    db_path = os.path.join('instance', 'novel_analyzer.db')
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return False
    
    logger.info(f"找到数据库文件: {db_path}")
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查chapter_analysis_results表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='chapter_analysis_results'")
        if not cursor.fetchone():
            logger.error("chapter_analysis_results表不存在")
            return False
        
        # 检查chapter_analysis_results表是否有reasoning_content列
        cursor.execute("PRAGMA table_info(chapter_analysis_results)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if "reasoning_content" not in columns:
            logger.info("chapter_analysis_results表中没有reasoning_content列，正在添加...")
            cursor.execute("ALTER TABLE chapter_analysis_results ADD COLUMN reasoning_content TEXT")
            conn.commit()
            logger.info("成功添加reasoning_content列到chapter_analysis_results表")
        else:
            logger.info("chapter_analysis_results表已有reasoning_content列，无需添加")
        
        # 检查analysis_results表是否有reasoning_content列
        cursor.execute("PRAGMA table_info(analysis_results)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if "reasoning_content" not in columns:
            logger.info("analysis_results表中没有reasoning_content列，正在添加...")
            cursor.execute("ALTER TABLE analysis_results ADD COLUMN reasoning_content TEXT")
            conn.commit()
            logger.info("成功添加reasoning_content列到analysis_results表")
        else:
            logger.info("analysis_results表已有reasoning_content列，无需添加")
        
        # 关闭连接
        conn.close()
        
        logger.info("数据库迁移执行完成")
        return True
    except Exception as e:
        logger.error(f"执行数据库迁移时出错: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    # 确保logs目录存在
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 执行迁移
    success = main()
    
    # 输出结果
    if success:
        print("数据库迁移成功完成")
        sys.exit(0)
    else:
        print("数据库迁移失败，请查看logs/migrations.log获取详细信息")
        sys.exit(1)
