// 全局变量，用于存储分析结果数据
let analysisResultsData = {};

// 更新维度卡片的函数
function updateDimensionCard(dimension, resultData) {
    console.log(`尝试更新维度 ${dimension} 的卡片，数据:`, resultData);

    // 检查结果数据是否有效
    if (!resultData) {
        console.error(`维度 ${dimension} 的结果数据为空`);

        // 尝试使用dimensionDisplayFix修复空白卡片
        if (window.dimensionDisplayFix && window.dimensionDisplayFix.fixEmptyCard) {
            const card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
            if (card) {
                console.log(`尝试使用dimensionDisplayFix修复维度 ${dimension} 的空白卡片`);
                window.dimensionDisplayFix.fixEmptyCard(card);
            }
        }

        return;
    }

    // 查找卡片
    const card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
    if (!card) {
        console.error(`找不到维度 ${dimension} 的卡片`);
        return;
    }

    const cardBody = card.querySelector('.card-body');
    if (!cardBody) {
        console.error(`找不到维度 ${dimension} 的卡片主体`);
        return;
    }

    // 创建可视化HTML
    let visualizationHtml = '';
    try {
        // 确保元数据是有效的对象
        let metadata = {};
        if (resultData.metadata) {
            if (typeof resultData.metadata === 'object') {
                metadata = resultData.metadata;
            } else {
                console.warn(`维度 ${dimension} 的元数据不是对象:`, resultData.metadata);
                try {
                    metadata = JSON.parse(resultData.metadata);
                } catch (e) {
                    console.error(`解析维度 ${dimension} 的元数据时出错:`, e);
                    metadata = {};
                }
            }
        }

        if (metadata && metadata.visualization_data) {
            visualizationHtml = `
                <div class="analysis-visualization mt-3">
                    <canvas class="analysis-chart" data-dimension="${dimension}" width="400" height="250"></canvas>
                </div>
            `;
        }
    } catch (e) {
        console.error(`创建维度 ${dimension} 的可视化HTML时出错:`, e);
        visualizationHtml = '';
    }

    // 提取内容摘要
    let excerpt = '';
    try {
        // 确保内容是字符串
        let content = '';
        if (resultData.content) {
            if (typeof resultData.content === 'string') {
                content = resultData.content;
            } else {
                console.warn(`维度 ${dimension} 的内容不是字符串:`, resultData.content);
                try {
                    content = JSON.stringify(resultData.content);
                } catch (e) {
                    console.error(`转换维度 ${dimension} 的内容为字符串时出错:`, e);
                    content = '内容格式错误';
                }
            }
        } else {
            content = '无内容';
        }

        // 去除HTML标签，只保留文本内容
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;
        const textContent = tempDiv.textContent || tempDiv.innerText || '';
        excerpt = textContent.substring(0, 300) + '...';
        console.log(`成功提取维度 ${dimension} 的摘要，长度: ${excerpt.length}`);
    } catch (e) {
        console.error(`提取维度 ${dimension} 的摘要时出错:`, e);
        excerpt = '提取摘要时出错';
    }

    // 更新卡片内容
    try {
        // 为特定维度添加优化版链接
        let extraButton = '';
        if (dimension === 'character_relationships') {
            extraButton = `<a href="/optimized/character_relationships/${window.location.pathname.split('/').pop()}" class="btn btn-sm btn-outline-success ms-1">优化版</a>`;
        } else if (dimension === 'chapter_outline') {
            extraButton = `<a href="${window.location.pathname}/analysis/${dimension}?fix=1" class="btn btn-sm btn-outline-success ms-1">优化版</a>`;
        }

        cardBody.innerHTML = `
            <div class="d-flex justify-content-between mb-2">
                <span class="badge bg-success">分析完成</span>
                <div>
                    <a href="${window.location.pathname}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                    ${extraButton}
                </div>
            </div>
            ${visualizationHtml}
            <div class="analysis-excerpt mt-2">
                ${excerpt}
            </div>
        `;
        console.log(`成功更新维度 ${dimension} 的卡片内容`);
    } catch (e) {
        console.error(`更新维度 ${dimension} 的卡片内容时出错:`, e);
        // 尝试使用更简单的内容
        try {
            cardBody.innerHTML = `
                <div class="d-flex justify-content-between mb-2">
                    <span class="badge bg-success">分析完成</span>
                    <a href="${window.location.pathname}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                </div>
                <div class="analysis-excerpt mt-2">
                    分析已完成，点击"查看详情"查看完整结果。
                </div>
            `;
        } catch (innerError) {
            console.error(`尝试使用简化内容更新卡片时也出错:`, innerError);
        }
    }

    // 初始化图表
    setTimeout(() => {
        try {
            const canvas = card.querySelector('.analysis-chart');
            if (canvas) {
                console.log(`找到维度 ${dimension} 的图表画布元素`);
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    console.log(`获取到维度 ${dimension} 的图表上下文`);
                    try {
                        let labels = ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'];
                        let data = [85, 72, 90, 65, 78, 82];

                        // 确保元数据是有效的对象
                        let metadata = {};
                        if (resultData.metadata) {
                            if (typeof resultData.metadata === 'object') {
                                metadata = resultData.metadata;
                            } else {
                                console.warn(`维度 ${dimension} 的元数据不是对象:`, resultData.metadata);
                                try {
                                    metadata = JSON.parse(resultData.metadata);
                                } catch (e) {
                                    console.error(`解析维度 ${dimension} 的元数据时出错:`, e);
                                    metadata = {};
                                }
                            }
                        }

                        if (metadata &&
                            metadata.visualization_data &&
                            metadata.visualization_data.radar) {

                            const visualData = metadata.visualization_data.radar;
                            if (visualData && visualData.labels && visualData.data) {
                                labels = visualData.labels;
                                data = visualData.data;
                                console.log(`使用维度 ${dimension} 的自定义可视化数据:`, labels, data);
                            }
                        }

                        createChart(ctx, dimension, labels, data);
                        console.log(`成功为维度 ${dimension} 创建图表`);
                    } catch (e) {
                        console.error(`为维度 ${dimension} 创建图表时出错:`, e);
                    }
                } else {
                    console.error(`无法获取维度 ${dimension} 的图表上下文`);
                }
            } else {
                console.error(`找不到维度 ${dimension} 的图表画布元素`);
            }
        } catch (e) {
            console.error(`初始化维度 ${dimension} 的图表时出错:`, e);
        }
    }, 200);

    console.log(`维度 ${dimension} 的卡片已更新`);
}

// 创建图表的函数 - 已禁用图表创建功能
function createChart(ctx, dimension, labels, data) {
    console.log(`图表创建功能已禁用，不创建维度 ${dimension} 的图表`);

    // 如果画布存在，显示一个提示信息
    if (ctx && ctx.canvas) {
        // 清除画布
        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

        // 绘制提示文本
        ctx.fillStyle = '#6c757d';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('图表功能已禁用以节省系统资源', ctx.canvas.width / 2, ctx.canvas.height / 2);
    }

    // 在画布上方添加一个提示元素
    if (ctx && ctx.canvas) {
        const container = ctx.canvas.parentElement;
        if (container) {
            // 检查是否已经有提示元素
            const existingMsg = container.querySelector('.chart-disabled-message');
            if (!existingMsg) {
                const msgElement = document.createElement('div');
                msgElement.className = 'chart-disabled-message alert alert-info mt-2';
                msgElement.style.fontSize = '12px';
                msgElement.innerHTML = '图表功能已禁用以节省系统资源';
                container.appendChild(msgElement);
            }
        }
    }

    // 返回一个空对象，模拟图表实例
    if (!window.chartInstances) {
        window.chartInstances = {};
    }
    window.chartInstances[dimension] = {
        destroy: function() { console.log('销毁禁用的图表实例'); }
    };

    return window.chartInstances[dimension];
}

// 直接加载Chart.js的函数
function loadChartJsManually(callback) {
    console.log('手动加载Chart.js');

    // 检查是否已加载
    if (typeof Chart !== 'undefined') {
        console.log('Chart.js已加载，直接调用回调函数');
        if (typeof callback === 'function') {
            callback();
        }
        return;
    }

    // 按优先级准备Chart.js路径
    const chartJsPaths = [
        '/static/js/lib/chart.min.js',  // 首选本地lib目录
        '/static/js/chart.min.js',      // 次选js根目录
        'https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.3.0/chart.umd.min.js',
        'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js'
    ];

    // 递归尝试加载
    function tryLoadChart(index) {
        if (index >= chartJsPaths.length) {
            console.error('所有Chart.js加载路径都失败');
            return;
        }

        const path = chartJsPaths[index];
        console.log(`尝试从 ${path} 加载Chart.js`);

        const script = document.createElement('script');
        script.src = path;
        script.async = true;

        script.onload = function() {
            console.log(`成功从 ${path} 加载Chart.js`);
            if (typeof callback === 'function') {
                callback();
            }
        };

        script.onerror = function() {
            console.warn(`从 ${path} 加载Chart.js失败，尝试下一个路径`);
            tryLoadChart(index + 1);
        };

        document.head.appendChild(script);
    }

    // 开始尝试加载
    tryLoadChart(0);
}

// 初始化所有图表
function initializeCharts() {
    console.log('初始化所有图表');
    const chartCanvases = document.querySelectorAll('.analysis-chart');
    console.log(`找到 ${chartCanvases.length} 个图表画布`);

    // 检查是否有空白卡片需要修复
    if (window.dimensionDisplayFix && window.dimensionDisplayFix.fixAllEmptyCards) {
        console.log('尝试使用dimensionDisplayFix修复所有空白卡片');
        const fixedCount = window.dimensionDisplayFix.fixAllEmptyCards();
        console.log(`修复了 ${fixedCount} 个空白卡片`);
    }

    chartCanvases.forEach(canvas => {
        try {
            const dimension = canvas.getAttribute('data-dimension');
            if (dimension) {
                console.log(`初始化维度 ${dimension} 的图表`);
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    // 获取该维度的分析结果数据
                    const resultData = analysisResultsData && analysisResultsData[dimension];

                    if (resultData) {
                        console.log(`找到维度 ${dimension} 的结果数据:`, {
                            hasMetadata: !!resultData.metadata,
                            hasVisualizationData: resultData.metadata && !!resultData.metadata.visualization_data
                        });

                        // 确保元数据是有效的对象
                        let metadata = {};
                        if (resultData.metadata) {
                            if (typeof resultData.metadata === 'object') {
                                metadata = resultData.metadata;
                            } else {
                                try {
                                    metadata = JSON.parse(resultData.metadata);
                                } catch (e) {
                                    console.warn(`解析维度 ${dimension} 的元数据时出错:`, e);
                                    metadata = {};
                                }
                            }
                        }

                        if (metadata && metadata.visualization_data && metadata.visualization_data.radar) {
                            const visualData = metadata.visualization_data.radar;
                            if (visualData && Array.isArray(visualData.labels) && Array.isArray(visualData.data)) {
                                console.log(`使用维度 ${dimension} 的自定义可视化数据`);
                                createChart(ctx, dimension, visualData.labels, visualData.data);
                            } else {
                                console.warn(`维度 ${dimension} 的可视化数据格式无效，使用默认数据`);
                                createChart(ctx, dimension,
                                    ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'],
                                    [85, 72, 90, 65, 78, 82]
                                );
                            }
                        } else {
                            console.log(`维度 ${dimension} 没有可视化数据，使用默认数据`);
                            createChart(ctx, dimension,
                                ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'],
                                [85, 72, 90, 65, 78, 82]
                            );
                        }
                    } else {
                        console.log(`没有找到维度 ${dimension} 的结果数据，使用默认数据`);
                        // 使用默认数据
                        createChart(ctx, dimension,
                            ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'],
                            [85, 72, 90, 65, 78, 82]
                        );

                        // 尝试修复卡片
                        const card = canvas.closest('.analysis-card');
                        if (card && window.dimensionDisplayFix && window.dimensionDisplayFix.fixEmptyCard) {
                            console.log(`尝试使用dimensionDisplayFix修复维度 ${dimension} 的空白卡片`);
                            window.dimensionDisplayFix.fixEmptyCard(card);
                        }
                    }
                } else {
                    console.error(`无法获取维度 ${dimension} 的图表上下文`);
                }
            } else {
                console.warn('找到没有维度属性的图表画布');
            }
        } catch (e) {
            console.error('初始化图表时出错:', e);
        }
    });
}

// 格式化维度名称的函数
function formatDimensionName(dimension) {
    const dimensionMap = {
        'language_style': '语言风格',
        'rhythm_pacing': '节奏与节奏',
        'structure': '结构分析',
        'sentence_variation': '句式变化',
        'paragraph_length': '段落长度',
        'perspective_shifts': '视角转换',
        'paragraph_flow': '段落流畅度',
        'novel_characteristics': '小说特点',
        'world_building': '世界构建',
        'chapter_outline': '章节大纲',
        'character_relationships': '人物关系',
        'opening_effectiveness': '开篇效果',
        'climax_pacing': '高潮节奏'
    };

    // 如果找不到映射，尝试将下划线替换为空格并首字母大写
    if (!dimensionMap[dimension]) {
        console.log(`未找到维度 ${dimension} 的中文名称，使用默认格式化`);
        return dimension
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    return dimensionMap[dimension];
}

// 导出函数供其他模块使用
window.updateDimensionCard = updateDimensionCard;
window.createChart = createChart;
window.initializeCharts = initializeCharts;
window.formatDimensionName = formatDimensionName;
