"""
Analysis module for the 九猫 (Nine Cats) novel analysis system.
"""
import logging
from typing import Dict, Any, List, Optional
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import time
from datetime import datetime, timezone
import json

import config
from src.api.deepseek_client import DeepSeekClient
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.db.connection import Session

logger = logging.getLogger(__name__)

class NovelAnalyzer:
    """Class for analyzing novels using the Alibaba Cloud AI models API."""

    def __init__(self, api_client: Optional[DeepSeekClient] = None, model: Optional[str] = None):
        """
        Initialize the novel analyzer.

        Args:
            api_client: API client. If None, creates a new client.
            model: Model to use. If None, uses the default model from config.
        """
        self.model = model or config.DEFAULT_MODEL
        self.api_client = api_client or DeepSeekClient(model=self.model)

    def analyze_novel(self, novel: Novel, dimensions: Optional[List[str]] = None, prompt_template: str = "default", temperature: float = None) -> Dict[str, AnalysisResult]:
        """
        Analyze a novel across multiple dimensions.

        Args:
            novel: The novel to analyze.
            dimensions: List of analysis dimensions to perform. If None, uses all dimensions from config.
            prompt_template: Prompt template to use (default or simplified).
            temperature: Optional temperature parameter for controlling randomness.

        Returns:
            Dictionary mapping dimension names to analysis results.
        """
        if dimensions is None:
            dimensions = config.ANALYSIS_DIMENSIONS

        total_start_time = time.time()

        # 重置分析开始时间
        DeepSeekClient._analysis_start_time = time.time()

        logger.info(f"======= 开始分析小说 ID:{novel.id}, 标题:'{novel.title}' =======")
        logger.info(f"分析维度: {', '.join(dimensions)}")
        logger.info(f"小说长度: {len(novel.content)} 字符")

        results = {}

        for i, dimension in enumerate(dimensions):
            dimension_start_time = time.time()
            # 记录API统计起始值
            stats_start = DeepSeekClient.get_api_call_stats()
            logger.info(f"[{i+1}/{len(dimensions)}] 开始分析维度: {dimension}")
            logger.info(f"当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

            try:
                # 更新进度状态为开始
                from src.web.app import analysis_progress
                if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                    analysis_progress[novel.id][dimension]["progress"] = 10
                    analysis_progress[novel.id][dimension]["estimated_time"] = "计算中..."
                    logger.info(f"进度更新: {dimension} 10%")

                # 分割文本为多个块
                chunks = self.api_client._split_text_into_chunks(novel.content)
                total_chunks = len(chunks)
                logger.info(f"文本已分割为 {total_chunks} 个块, 每块大约 {config.MAX_CHUNK_SIZE} 字符")

                # 逐块分析
                chunk_results = []

                # 获取断点信息，确定起始块
                start_block = 0
                try:
                    from src.web.app import analysis_progress
                    if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                        if "current_block" in analysis_progress[novel.id][dimension]:
                            start_block = analysis_progress[novel.id][dimension].get("current_block", 0)
                            logger.info(f"从断点恢复，从块 {start_block}/{total_chunks} 开始分析")
                except Exception as e:
                    logger.error(f"获取断点信息时出错: {str(e)}")
                    start_block = 0

                # 如果是从断点恢复，添加之前的结果占位符
                if start_block > 0:
                    for _ in range(start_block):
                        chunk_results.append({
                            "type": dimension,
                            "content": f"[从断点恢复的内容]"
                        })
                    logger.info(f"添加了 {start_block} 个占位结果用于断点恢复")

                # === 并发分析每个分块 ===
                def analyze_chunk(j, chunk):
                    chunk_start_time = time.time()
                    # 检查是否应该终止分析
                    try:
                        from src.web.app import analysis_running
                        if novel.id in analysis_running and not analysis_running.get(novel.id, True):
                            logger.info(f"检测到终止信号，停止分析维度 {dimension}，当前进度: 块 {j}/{total_chunks}")
                            return {
                                "type": dimension,
                                "content": f"[分析已中断] 块 {j+1}/{total_chunks} 未分析"
                            }
                    except Exception as e:
                        logger.error(f"检查终止状态时出错: {str(e)}")
                    # 分析当前块
                    logger.info(f"维度 {dimension}: 并发分析块 {j+1}/{total_chunks} (长度: {len(chunk)} 字符)")
                    if config.DEBUG:
                        logger.info(f"DEBUG模式：模拟分析而不是实际调用API")
                        result = {
                            "type": dimension,
                            "content": f"这是{dimension}分析的模拟结果，用于测试进度条和可视化功能。\n\n"
                                    f"这是第{j+1}个文本块的分析结果，总共有{total_chunks}个块。\n\n"
                                    f"在实际部署时，这里会显示真实的分析结果。"
                        }
                    else:
                        logger.info(f"维度 {dimension}: 调用 {self.api_client.model_name} API 分析文本块 {j+1}/{total_chunks}...")
                        api_start_time = time.time()
                        result = self.api_client.analyze_text(
                            chunk,
                            dimension,
                            prompt_template=prompt_template,
                            temperature=temperature
                        )
                        api_end_time = time.time()
                        logger.info(f"维度 {dimension}: {self.api_client.model_name} API 调用完成, 耗时: {api_end_time - api_start_time:.2f}秒")
                    chunk_end_time = time.time()
                    logger.info(f"维度 {dimension}: 完成块 {j+1}/{total_chunks} 分析, 耗时: {chunk_end_time - chunk_start_time:.2f}秒")
                    return result

                # 使用优化的并行配置，破除串行化瓶颈
                try:
                    from src.config.parallel_optimization_config import ParallelOptimizationConfig
                    optimization_config = ParallelOptimizationConfig.get_optimization_config(prompt_template)

                    # 动态计算最优并行度（精简版成本优先）
                    if prompt_template == "simplified":
                        # 精简版：严格控制并行度以控制成本
                        optimal_workers = min(
                            optimization_config["max_chunk_workers"],
                            max(3, min(6, total_chunks // 3)),  # 精简版最多6个线程，更保守的策略
                            config.MAX_CHUNK_WORKERS
                        )
                        logger.info(f"[精简版成本控制] 维度{dimension}限制并行度以控制API成本: {optimal_workers}")
                    else:
                        # 默认版：性能优先
                        optimal_workers = min(
                            optimization_config["max_chunk_workers"],
                            max(3, total_chunks // 2),  # 至少3个，最多总块数的一半
                            config.MAX_CHUNK_WORKERS
                        )
                        logger.info(f"[默认版性能优先] 维度{dimension}使用优化并行度: {optimal_workers}")

                except Exception as e:
                    logger.warning(f"[串行化优化] 获取优化配置失败，使用默认配置: {str(e)}")
                    optimal_workers = min(8, max(3, total_chunks // 2))  # 提高默认并行度

                with ThreadPoolExecutor(max_workers=optimal_workers, thread_name_prefix=f"chunk_{dimension}") as executor:
                    future_to_index = {executor.submit(analyze_chunk, j, chunks[j]): j for j in range(start_block, total_chunks)}
                    # 保证结果顺序
                    results_map = {}
                    for future in as_completed(future_to_index):
                        j = future_to_index[future]
                        try:
                            result = future.result()
                            results_map[j] = result
                            # 更新进度
                            try:
                                from src.web.app import analysis_progress
                                if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                                    progress = 10 + int(80 * (j - start_block + 1) / (total_chunks - start_block))
                                    analysis_progress[novel.id][dimension]["progress"] = progress
                                    analysis_progress[novel.id][dimension]["current_block"] = j + 1
                            except Exception as e:
                                logger.error(f"更新块进度状态时出错: {str(e)}")
                        except Exception as e:
                            logger.error(f"分块分析时出错: {str(e)}")
                            results_map[j] = {
                                "type": dimension,
                                "content": f"[分析出错] 块 {j+1}/{total_chunks}: {str(e)}"
                            }
                    # 按顺序合并
                    for j in range(start_block, total_chunks):
                        chunk_results.append(results_map[j])

                # 组合所有块的结果
                logger.info(f"开始合并 {len(chunk_results)} 个块的分析结果...")
                combine_start_time = time.time()
                combined_result = self.api_client._combine_chunk_results(chunk_results, dimension)
                combine_end_time = time.time()
                logger.info(f"合并完成, 耗时: {combine_end_time - combine_start_time:.2f}秒")

                # 结果预览
                content_preview = combined_result.get("content", "")[:200] + "..." if combined_result.get("content") else "无内容"
                logger.info(f"合并后的结果摘要: {content_preview}")

                # 创建分析结果对象
                dimension_end_time = time.time()
                processing_time = dimension_end_time - dimension_start_time

                # 获取API统计结束值并计算差值
                stats_end = DeepSeekClient.get_api_call_stats()
                calls_diff = stats_end["total_calls"] - stats_start["total_calls"]
                tokens_diff = stats_end["total_tokens"] - stats_start["total_tokens"]
                cost_diff = stats_end["total_cost"] - stats_start["total_cost"]
                metadata_dict = {
                    "processing_time": processing_time,
                    "chunk_count": total_chunks,
                    "api_calls": calls_diff,
                    "tokens_used": tokens_diff,
                    "cost": cost_diff,
                    "start_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(dimension_start_time)),
                    "end_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(dimension_end_time))
                }
                # 使用实际指标生成可视化数据
                # 基础性能指标
                perf_labels = ["处理时间(秒)", "分块数量", "API调用次数", "令牌使用量", "费用(元)"]
                perf_data = [
                    round(processing_time, 2),
                    total_chunks,
                    calls_diff,
                    tokens_diff,
                    round(cost_diff, 4)
                ]

                # 根据分析维度生成更有意义的可视化数据
                import random
                if dimension == "language_style":
                    # 语言风格分析的可视化数据
                    radar_labels = ["热词敏感度", "流派特征", "情绪感染力", "修辞密度", "口语化程度", "感官描写"]
                    # 从内容中提取或估算这些指标的值（这里使用随机值作为示例，实际应该基于分析结果）
                    radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
                elif dimension == "rhythm_pacing":
                    # 节奏控制分析的可视化数据
                    radar_labels = ["开篇信息密度", "悬念钩子强度", "多线叙事平衡", "章节节拍", "信息投放节奏", "快慢节奏变化"]
                    radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
                elif dimension == "structure":
                    # 结构分析的可视化数据
                    radar_labels = ["整体架构", "情节连贯性", "冲突设置", "高潮安排", "结局处理", "支线整合"]
                    radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
                elif dimension == "world_building":
                    # 世界构建分析的可视化数据
                    radar_labels = ["世界观完整度", "设定一致性", "背景细节", "文化塑造", "环境描写", "历史深度"]
                    radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
                elif dimension == "character_relationships":
                    # 人物关系分析的可视化数据
                    radar_labels = ["主角塑造", "配角丰满度", "关系复杂性", "人物成长", "性格一致性", "互动真实感"]
                    radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
                else:
                    # 其他维度使用通用可视化数据
                    radar_labels = ["分析深度", "洞察力", "实用性", "准确性", "全面性", "创新视角"]
                    radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]

                metadata_dict["visualization_data"] = {
                    "radar": {"labels": radar_labels, "data": radar_data},
                    "bar": {"labels": perf_labels, "data": perf_data}
                }

                # 确保元数据是字典类型
                if not isinstance(metadata_dict, dict):
                    logger.warning(f"元数据不是字典类型: {type(metadata_dict)}")
                    metadata_dict = {"error": f"元数据无法转换为字典: {type(metadata_dict)}"}

                # 额外保护：确保metadata_dict是纯粹的Python字典，避免SQLAlchemy元数据对象
                try:
                    # 先转换为JSON字符串再转回字典，确保是纯Python对象
                    metadata_str = json.dumps(metadata_dict)
                    metadata_dict = json.loads(metadata_str)
                    logger.info(f"元数据已转换为纯Python字典: {type(metadata_dict)}")
                except Exception as e:
                    logger.error(f"转换元数据为纯Python字典时出错: {str(e)}")
                    metadata_dict = {"error": f"元数据转换失败: {str(e)}"}

                # 获取分析过程日志
                try:
                    from src.web.app import analysis_logs
                    dimension_logs = []
                    if novel.id in analysis_logs:
                        # 筛选出当前维度的日志
                        dimension_logs = [log for log in analysis_logs[novel.id]
                                         if log.get('dimension') == dimension or
                                         (log.get('dimension') is None and dimension in log.get('message', ''))]
                        logger.info(f"为维度 {dimension} 收集了 {len(dimension_logs)} 条分析过程日志")
                except Exception as e:
                    logger.error(f"获取分析过程日志时出错: {str(e)}")
                    dimension_logs = []

                # 创建分析结果对象，注意metadata参数会被存储在analysis_metadata属性中
                result = AnalysisResult(
                    novel_id=novel.id,
                    dimension=dimension,
                    content=combined_result.get("content", ""),
                    metadata=metadata_dict,
                    logs=dimension_logs  # 添加分析过程日志
                )

                # 更新进度为100%，明确标记为已完成
                if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                    analysis_progress[novel.id][dimension]["progress"] = 100
                    analysis_progress[novel.id][dimension]["estimated_time"] = "已完成"
                    logger.info(f"进度更新: {dimension} 100%, 已完成")

                results[dimension] = result
                logger.info(f"完成维度 {dimension} 分析, 总耗时: {processing_time:.2f}秒")

            except Exception as e:
                logger.error(f"分析维度 {dimension} 时出错: {str(e)}", exc_info=True)
                results[dimension] = AnalysisResult(
                    novel_id=novel.id,
                    dimension=dimension,
                    content=f"分析过程中出错: {str(e)}",
                    metadata={"error": str(e)}
                )

                # 更新进度为错误状态
                if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                    analysis_progress[novel.id][dimension]["progress"] = -1
                    analysis_progress[novel.id][dimension]["estimated_time"] = f"错误: {str(e)}"
                    logger.info(f"进度更新: {dimension} 错误, {str(e)}")

        total_end_time = time.time()
        total_time = total_end_time - total_start_time

        # 获取API调用统计信息
        api_stats = DeepSeekClient.get_api_call_stats()

        logger.info(f"======= 小说分析完成 =======")
        logger.info(f"总耗时: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        logger.info(f"[API统计] 总调用次数: {api_stats['total_calls']}次")
        logger.info(f"[API统计] 总令牌使用量: {api_stats['total_tokens']}个")
        logger.info(f"[API统计] 总费用: {api_stats['total_cost']:.4f}元")
        logger.info(f"[API统计] 平均每次调用费用: {api_stats['average_cost_per_call']:.4f}元")
        logger.info(f"[API统计] 分析时长: {api_stats['analysis_duration_minutes']:.2f}分钟")
        logger.info(f"成功分析 {len(results)} 个维度")

        # 在返回结果前，确保所有结果对象的元数据都是纯Python字典
        for dimension, result in results.items():
            try:
                if hasattr(result, 'analysis_metadata') and result.analysis_metadata is not None:
                    # 检查metadata类型
                    logger.info(f"维度 {dimension} 的元数据类型: {type(result.analysis_metadata)}")

                    # 强制转换为纯Python字典
                    try:
                        if isinstance(result.analysis_metadata, dict):
                            # 通过JSON序列化和反序列化确保是纯Python字典
                            metadata_str = json.dumps(result.analysis_metadata)
                            result.analysis_metadata = json.loads(metadata_str)
                            logger.info(f"维度 {dimension} 的元数据已转换为纯Python字典")
                        else:
                            logger.warning(f"维度 {dimension} 的元数据不是字典: {type(result.analysis_metadata)}")
                            result.analysis_metadata = {"error": f"元数据无法转换为字典: {type(result.analysis_metadata)}"}
                    except Exception as e:
                        logger.error(f"转换维度 {dimension} 的元数据时出错: {str(e)}")
                        result.analysis_metadata = {"error": f"元数据转换失败: {str(e)}"}
            except Exception as e:
                logger.error(f"处理维度 {dimension} 的结果时出错: {str(e)}")

        return results

    def analyze_novel_parallel(self, novel: Novel, dimensions: Optional[List[str]] = None, max_workers: int = 5, prompt_template: str = "default", temperature: float = None) -> Dict[str, AnalysisResult]:
        """
        Analyze a novel across multiple dimensions in parallel with advanced optimization.

        Args:
            novel: The novel to analyze.
            dimensions: List of analysis dimensions to perform. If None, uses all dimensions from config.
            max_workers: Maximum number of parallel workers for dimension-level parallelism.
            prompt_template: Prompt template to use (default or simplified).
            temperature: Optional temperature parameter for controlling randomness.

        Returns:
            Dictionary mapping dimension names to analysis results.
        """
        if dimensions is None:
            dimensions = config.ANALYSIS_DIMENSIONS

        total_start_time = time.time()

        # 重置分析开始时间
        DeepSeekClient._analysis_start_time = time.time()

        logger.info(f"======= 开始优化并行分析小说 ID:{novel.id}, 标题:'{novel.title}' =======")
        logger.info(f"并行分析维度: {', '.join(dimensions)}, 最大并行数: {max_workers} (默认5)")
        logger.info(f"小说长度: {len(novel.content)} 字符")

        results = {}

        # 预处理：将小说文本分割为块，所有维度共享这些块，避免重复分割
        chunks = self.api_client._split_text_into_chunks(novel.content)
        total_chunks = len(chunks)
        logger.info(f"文本已分割为 {total_chunks} 个块, 每块大约 {config.MAX_CHUNK_SIZE} 字符")

        # 创建共享的分析上下文，减少重复计算
        shared_context = {
            "novel": novel,
            "chunks": chunks,
            "total_chunks": total_chunks,
            "start_time": total_start_time
        }

        # 使用两级并行：维度级并行和块级并行
        # 使用优化配置管理器获取并行策略
        try:
            from src.config.parallel_optimization_config import ParallelOptimizationConfig
            optimization_config = ParallelOptimizationConfig.get_optimization_config(prompt_template)
            effective_max_workers = min(max_workers, optimization_config["max_dimension_workers"])

            logger.info(f"[并行优化] 使用{optimization_config['optimization_level']}级别的并行策略")
            logger.info(f"[并行优化] 最大并发维度数: {effective_max_workers}")
            logger.info(f"[并行优化] 单实例并发度: {optimization_config['instance_concurrency']}")
            logger.info(f"[并行优化] 连接池大小: {optimization_config['connection_pool_size']}")
        except ImportError:
            # 降级到原有逻辑
            if prompt_template == "simplified":
                effective_max_workers = min(max_workers, 8)
                logger.info(f"[精简版并行] 使用效率与成本平衡的并行策略，最大并发维度数: {effective_max_workers}")
            else:
                effective_max_workers = max_workers
                logger.info(f"[默认版并行] 使用性能优先的并行策略，最大并发维度数: {effective_max_workers}")

        with ThreadPoolExecutor(max_workers=effective_max_workers, thread_name_prefix="dimension_parallel") as dimension_executor:
            # 为每个维度创建一个任务
            future_to_dimension = {
                dimension_executor.submit(
                    self._analyze_dimension_optimized,
                    shared_context,
                    dimension,
                    prompt_template,
                    temperature
                ): dimension
                for dimension in dimensions
            }

            for future in as_completed(future_to_dimension):
                dimension = future_to_dimension[future]
                try:
                    result = future.result()
                    results[dimension] = result

                    # 更新进度状态为完成
                    try:
                        from src.web.app import analysis_progress
                        if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                            analysis_progress[novel.id][dimension]["progress"] = 100
                            analysis_progress[novel.id][dimension]["estimated_time"] = "已完成"
                            logger.info(f"进度更新: {dimension} 100%, 已完成")
                    except Exception as e:
                        logger.error(f"更新进度状态时出错: {str(e)}")

                except Exception as e:
                    logger.error(f"分析维度 {dimension} 时出错: {str(e)}")
                    error_message = f"""# 分析过程中出错

## 错误详情
分析维度 **{dimension}** 时遇到了问题。

## 错误信息
```
{str(e)}
```

## 建议操作
请尝试以下解决方法：
1. 刷新页面并重新尝试分析
2. 检查小说文本是否过长或包含特殊字符
3. 确认API连接正常

如果问题持续存在，请联系系统管理员。
"""
                    results[dimension] = AnalysisResult(
                        novel_id=novel.id,
                        dimension=dimension,
                        content=error_message,
                        metadata={"error": str(e), "formatted_error": True}
                    )

                    # 更新进度为错误状态
                    try:
                        from src.web.app import analysis_progress
                        if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                            analysis_progress[novel.id][dimension]["progress"] = -1
                            analysis_progress[novel.id][dimension]["estimated_time"] = f"错误: {str(e)}"
                            logger.info(f"进度更新: {dimension} 错误, {str(e)}")
                    except Exception as err:
                        logger.error(f"更新错误进度状态时出错: {str(err)}")

        total_end_time = time.time()
        total_time = total_end_time - total_start_time

        # 获取API调用统计信息
        api_stats = DeepSeekClient.get_api_call_stats()

        logger.info(f"======= 小说优化并行分析完成 =======")
        logger.info(f"总耗时: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        logger.info(f"[API统计] 总调用次数: {api_stats['total_calls']}次")
        logger.info(f"[API统计] 总令牌使用量: {api_stats['total_tokens']}个")
        logger.info(f"[API统计] 总费用: {api_stats['total_cost']:.4f}元")
        logger.info(f"[API统计] 平均每次调用费用: {api_stats['average_cost_per_call']:.4f}元")
        logger.info(f"[API统计] 分析时长: {api_stats['analysis_duration_minutes']:.2f}分钟")
        logger.info(f"成功分析 {len(results)} 个维度")

        # 在返回结果前，确保所有结果对象的元数据都是纯Python字典
        for dimension, result in results.items():
            try:
                if hasattr(result, 'analysis_metadata') and result.analysis_metadata is not None:
                    # 检查metadata类型
                    logger.info(f"维度 {dimension} 的元数据类型: {type(result.analysis_metadata)}")

                    # 强制转换为纯Python字典
                    try:
                        if isinstance(result.analysis_metadata, dict):
                            # 通过JSON序列化和反序列化确保是纯Python字典
                            metadata_str = json.dumps(result.analysis_metadata)
                            result.analysis_metadata = json.loads(metadata_str)
                            logger.info(f"维度 {dimension} 的元数据已转换为纯Python字典")
                        else:
                            logger.warning(f"维度 {dimension} 的元数据不是字典: {type(result.analysis_metadata)}")
                            result.analysis_metadata = {"error": f"元数据无法转换为字典: {type(result.analysis_metadata)}"}
                    except Exception as e:
                        logger.error(f"转换维度 {dimension} 的元数据时出错: {str(e)}")
                        result.analysis_metadata = {"error": f"元数据转换失败: {str(e)}"}
            except Exception as e:
                logger.error(f"处理维度 {dimension} 的结果时出错: {str(e)}")

        return results

    def _analyze_dimension_optimized(self, shared_context: Dict[str, Any], dimension: str, prompt_template: str = "default", temperature: float = None) -> AnalysisResult:
        """
        Optimized version of dimension analysis that uses shared context.

        Args:
            shared_context: Shared analysis context with novel and chunks.
            dimension: The analysis dimension.
            prompt_template: Prompt template to use (default or simplified).
            temperature: Optional temperature parameter for controlling randomness.

        Returns:
            Analysis result for the dimension.
        """
        novel = shared_context["novel"]
        chunks = shared_context["chunks"]
        total_chunks = shared_context["total_chunks"]

        start_time = time.time()
        # 记录API统计起始值
        stats_start = DeepSeekClient.get_api_call_stats()
        logger.info(f"开始优化分析维度: {dimension}")

        # 检查是否启用缓存
        if config.CACHE_ENABLED and not config.FORCE_REFRESH_CACHE:
            # 检查缓存中是否已有分析结果
            try:
                session = Session()
                cached_result = session.query(AnalysisResult).filter_by(
                    novel_id=novel.id, dimension=dimension
                ).first()

                # 如果找到缓存结果，检查是否需要重新分析
                if cached_result:
                    # 获取缓存时间
                    cache_time = cached_result.updated_at
                    current_time = time.time()

                    # 检查缓存是否过期
                    cache_valid_seconds = config.CACHE_VALID_DAYS * 24 * 60 * 60

                    # 如果缓存未过期且内容不为空，直接返回缓存结果
                    if cache_time and (current_time - cache_time.timestamp() < cache_valid_seconds) and cached_result.content:
                        logger.info(f"维度 {dimension} 找到有效缓存结果，缓存时间: {cache_time}，跳过分析")

                        # 更新进度状态为完成（使用缓存）
                        try:
                            from src.web.app import analysis_progress
                            if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                                analysis_progress[novel.id][dimension]["progress"] = 100
                                analysis_progress[novel.id][dimension]["estimated_time"] = "已完成（使用缓存）"
                                logger.info(f"进度更新: {dimension} 100%, 使用缓存结果")
                        except Exception as e:
                            logger.error(f"更新缓存进度状态时出错: {str(e)}")

                        return cached_result
                    else:
                        logger.info(f"维度 {dimension} 缓存结果已过期或内容为空，将重新分析")
                else:
                    logger.info(f"维度 {dimension} 没有找到缓存结果，将进行分析")
            except Exception as e:
                logger.error(f"检查缓存结果时出错: {str(e)}")
            finally:
                session.close()

        # 检查缓存中是否已有分析结果
        try:
            session = Session()
            cached_result = session.query(AnalysisResult).filter_by(
                novel_id=novel.id, dimension=dimension
            ).first()

            # 如果找到缓存结果，检查是否需要重新分析
            if cached_result:
                # 获取缓存时间
                cache_time = cached_result.updated_at
                current_time = time.time()

                # 检查缓存是否过期（默认7天）
                cache_valid_days = getattr(config, 'CACHE_VALID_DAYS', 7)
                cache_valid_seconds = cache_valid_days * 24 * 60 * 60

                # 如果缓存未过期且内容不为空，直接返回缓存结果
                if cache_time and (current_time - cache_time.timestamp() < cache_valid_seconds) and cached_result.content:
                    logger.info(f"维度 {dimension} 找到有效缓存结果，缓存时间: {cache_time}，跳过分析")

                    # 添加到控制台日志
                    try:
                        from src.web.app import add_analysis_log
                        add_analysis_log(novel.id, f"维度 {dimension} 使用缓存结果，缓存时间: {cache_time}", "info", dimension)
                    except Exception as e:
                        logger.error(f"添加缓存日志时出错: {str(e)}")

                    # 更新进度状态为完成（使用缓存）
                    try:
                        from src.web.app import analysis_progress
                        if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                            analysis_progress[novel.id][dimension]["progress"] = 100
                            analysis_progress[novel.id][dimension]["estimated_time"] = "已完成（使用缓存）"
                            logger.info(f"进度更新: {dimension} 100%, 使用缓存结果")
                    except Exception as e:
                        logger.error(f"更新缓存进度状态时出错: {str(e)}")

                    return cached_result
                else:
                    logger.info(f"维度 {dimension} 缓存结果已过期或内容为空，将重新分析")

                    # 添加到控制台日志
                    try:
                        from src.web.app import add_analysis_log
                        add_analysis_log(novel.id, f"维度 {dimension} 缓存结果已过期或内容为空，将重新分析", "info", dimension)
                    except Exception as e:
                        logger.error(f"添加缓存过期日志时出错: {str(e)}")
            else:
                logger.info(f"维度 {dimension} 没有找到缓存结果，将进行分析")

                # 添加到控制台日志
                try:
                    from src.web.app import add_analysis_log
                    add_analysis_log(novel.id, f"维度 {dimension} 没有找到缓存结果，将进行分析", "info", dimension)
                except Exception as e:
                    logger.error(f"添加无缓存日志时出错: {str(e)}")
        except Exception as e:
            logger.error(f"检查缓存结果时出错: {str(e)}")
        finally:
            session.close()

        # 确保数据库连接正常
        try:
            from src.db.connection import dispose_engine
            # 检查数据库连接是否正常
            from sqlalchemy import text
            from src.db.connection import engine

            # 尝试执行简单查询测试连接
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                logger.info(f"分析维度 {dimension} 前数据库连接正常")
        except Exception as db_error:
            logger.error(f"分析维度 {dimension} 前数据库连接测试失败: {str(db_error)}")
            # 尝试释放并重新初始化连接池
            try:
                dispose_engine()
                logger.info(f"已释放并重新初始化数据库连接池")
            except Exception as dispose_error:
                logger.error(f"释放连接池时出错: {str(dispose_error)}")

        try:
            # 更新进度状态为开始
            from src.web.app import analysis_progress, analysis_running
            if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                analysis_progress[novel.id][dimension]["progress"] = 10
                analysis_progress[novel.id][dimension]["estimated_time"] = "计算中..."
                logger.info(f"进度更新: {dimension} 10%")

                # 记录当前块和总块数，用于断点续传
                analysis_progress[novel.id][dimension]["current_block"] = 0
                analysis_progress[novel.id][dimension]["total_blocks"] = total_chunks
        except Exception as e:
            logger.error(f"更新初始进度状态时出错: {str(e)}")

        try:
            # 获取断点信息，确定起始块
            start_block = 0
            try:
                from src.web.app import analysis_progress
                if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                    if "current_block" in analysis_progress[novel.id][dimension]:
                        start_block = analysis_progress[novel.id][dimension].get("current_block", 0)
                        logger.info(f"从断点恢复，从块 {start_block}/{total_chunks} 开始分析")
            except Exception as e:
                logger.error(f"获取断点信息时出错: {str(e)}")
                start_block = 0

            # 逐块分析
            chunk_results = []

            # 如果是从断点恢复，尝试加载之前的分析结果
            previous_results = []
            if start_block > 0:
                try:
                    # 尝试从数据库加载部分分析结果
                    session = Session()
                    try:
                        # 查询是否有部分分析结果
                        partial_result = session.query(AnalysisResult).filter_by(
                            novel_id=novel.id,
                            dimension=dimension
                        ).first()

                        if partial_result and partial_result.analysis_metadata and "partial_results" in partial_result.analysis_metadata:
                            previous_results = partial_result.analysis_metadata["partial_results"]
                            logger.info(f"加载到 {len(previous_results)} 个之前的分析结果片段")
                    finally:
                        session.close()
                except Exception as e:
                    logger.error(f"加载之前的分析结果时出错: {str(e)}")

            # 如果是从断点恢复，添加之前的结果
            if start_block > 0:
                # 如果有之前的分析结果，使用它们
                if previous_results and len(previous_results) >= start_block:
                    for i in range(start_block):
                        chunk_results.append(previous_results[i])
                    logger.info(f"使用 {start_block} 个之前的分析结果")
                else:
                    # 否则使用占位符
                    for _ in range(start_block):
                        chunk_results.append({
                            "type": dimension,
                            "content": f"[从断点恢复的内容]"
                        })
                    logger.info(f"添加了 {start_block} 个占位结果用于断点恢复")

            # 使用更高效的块级并行处理
            # 使用优化配置管理器获取块级并行策略
            try:
                from src.config.parallel_optimization_config import ParallelOptimizationConfig
                dimension_config = ParallelOptimizationConfig.get_dimension_specific_config(dimension, prompt_template)

                chunk_workers = min(dimension_config["max_chunk_workers"], max(3, total_chunks // 4))
                api_delay = dimension_config["api_delay"]
                stream_threshold = dimension_config["stream_threshold"]
                instance_concurrency = dimension_config["instance_concurrency"]

                logger.info(f"[块级并行优化] 维度 {dimension}: 使用 {chunk_workers} 个并行工作线程")
                logger.info(f"[块级并行优化] API延迟: {api_delay}秒，流式阈值: {stream_threshold}字符")
                logger.info(f"[块级并行优化] 单实例并发度: {instance_concurrency}")

                # 如果有成本倍数，记录日志
                if "cost_multiplier" in dimension_config:
                    logger.info(f"[成本优化] 维度 {dimension} 成本倍数: {dimension_config['cost_multiplier']}")

            except ImportError:
                # 降级到原有逻辑
                if prompt_template == "simplified":
                    chunk_workers = min(8, max(3, total_chunks // 4))
                    api_delay = 0.3
                    stream_threshold = 30000
                    logger.info(f"[精简版块级并行] 维度 {dimension}: 使用 {chunk_workers} 个并行工作线程，API延迟 {api_delay}秒")
                else:
                    chunk_workers = min(12, max(5, total_chunks // 3))
                    api_delay = 0.2
                    stream_threshold = 20000
                    logger.info(f"[默认版块级并行] 维度 {dimension}: 使用 {chunk_workers} 个并行工作线程，API延迟 {api_delay}秒")

            # === 并发分析每个分块 ===
            def analyze_chunk(j, chunk):
                chunk_start_time = time.time()

                # 串行化瓶颈优化：使用自适应延迟替代固定延迟
                if j > 0:
                    try:
                        from src.config.serialization_bottleneck_optimizer import SerializationBottleneckOptimizer
                        optimizer = SerializationBottleneckOptimizer(prompt_template=prompt_template)

                        # 获取API优化配置
                        api_config = optimizer.optimize_api_calls(f"analysis_{novel.id}_{dimension}")

                        if api_config.get("adaptive_delay_manager"):
                            # 使用自适应延迟
                            adaptive_delay = api_config["adaptive_delay_manager"].get_delay()
                            time.sleep(adaptive_delay)
                            logger.debug(f"[自适应延迟] 维度{dimension}块{j+1}使用延迟: {adaptive_delay:.2f}秒")
                        else:
                            # 回退到固定延迟
                            time.sleep(api_delay)
                    except Exception as e:
                        logger.debug(f"[延迟优化] 自适应延迟失败，使用固定延迟: {str(e)}")
                        time.sleep(api_delay)

                # 检查是否应该终止分析
                try:
                    from src.web.app import analysis_running
                    if novel.id in analysis_running and not analysis_running.get(novel.id, True):
                        logger.info(f"检测到终止信号，停止分析维度 {dimension}，当前进度: 块 {j}/{total_chunks}")
                        return {
                            "type": dimension,
                            "content": f"[分析已中断] 块 {j+1}/{total_chunks} 未分析"
                        }
                except Exception as e:
                    logger.error(f"检查终止状态时出错: {str(e)}")

                # 分析当前块
                logger.info(f"维度 {dimension}: 并发分析块 {j+1}/{total_chunks} (长度: {len(chunk)} 字符)")

                if config.DEBUG:
                    logger.info(f"DEBUG模式：模拟分析而不是实际调用API")
                    result = {
                        "type": dimension,
                        "content": f"这是{dimension}分析的模拟结果，用于测试进度条和可视化功能。\n\n"
                                f"这是第{j+1}个文本块的分析结果，总共有{total_chunks}个块。\n\n"
                                f"在实际部署时，这里会显示真实的分析结果。"
                    }
                else:
                    logger.info(f"维度 {dimension}: 调用 DeepSeek API 分析文本块 {j+1}/{total_chunks}...")
                    api_start_time = time.time()

                    # 根据优化配置决定是否使用流式输出
                    try:
                        use_stream = len(chunk) > stream_threshold
                    except NameError:
                        # 如果没有stream_threshold，使用默认逻辑
                        use_stream = (
                            len(chunk) > 30000 or  # 大文本块使用流式输出
                            dimension in ["chapter_outline", "outline_analysis"] or  # 高消耗维度使用流式输出
                            (prompt_template == "default" and len(chunk) > 20000)  # 默认版大文本使用流式输出
                        )

                    result = self.api_client.analyze_text(
                        chunk,
                        dimension,
                        prompt_template=prompt_template,
                        temperature=temperature,
                        stream=use_stream
                    )

                    api_end_time = time.time()
                    stream_info = " (流式)" if use_stream else " (标准)"
                    logger.info(f"维度 {dimension}: DeepSeek API{stream_info} 调用完成, 耗时: {api_end_time - api_start_time:.2f}秒")

                chunk_end_time = time.time()
                logger.info(f"维度 {dimension}: 完成块 {j+1}/{total_chunks} 分析, 耗时: {chunk_end_time - chunk_start_time:.2f}秒")
                return result

            # 使用串行化瓶颈优化的线程池配置
            thread_name_prefix = f"chunk_{dimension}_{prompt_template}"

            # 应用串行化瓶颈优化
            try:
                from src.config.serialization_bottleneck_optimizer import SerializationBottleneckOptimizer
                optimizer = SerializationBottleneckOptimizer(prompt_template=prompt_template)
                optimized_executor = optimizer.create_optimized_executor(
                    task_id=f"analysis_{novel.id}_{dimension}",
                    max_workers=chunk_workers
                )
                logger.info(f"[串行化优化] 为维度{dimension}创建了优化的线程池执行器")
            except Exception as e:
                logger.warning(f"[串行化优化] 创建优化执行器失败，使用默认执行器: {str(e)}")
                optimized_executor = ThreadPoolExecutor(max_workers=chunk_workers, thread_name_prefix=thread_name_prefix)

            with optimized_executor as executor:
                logger.info(f"[块级并行] 启动 {chunk_workers} 个工作线程处理 {total_chunks - start_block} 个文本块")

                # 批量提交任务以提高效率
                future_to_index = {}
                for j in range(start_block, total_chunks):
                    future = executor.submit(analyze_chunk, j, chunks[j])
                    future_to_index[future] = j

                logger.info(f"[块级并行] 已提交 {len(future_to_index)} 个分析任务")

                # 保证结果顺序
                results_map = {}
                completed_count = 0

                for future in as_completed(future_to_index):
                    j = future_to_index[future]
                    completed_count += 1

                    try:
                        result = future.result()
                        results_map[j] = result

                        # 每完成10个任务或完成所有任务时输出进度
                        if completed_count % 10 == 0 or completed_count == len(future_to_index):
                            logger.info(f"[块级并行] 已完成 {completed_count}/{len(future_to_index)} 个分析任务")
                        # 更新进度
                        try:
                            from src.web.app import analysis_progress
                            if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                                progress = 10 + int(80 * (j - start_block + 1) / (total_chunks - start_block))
                                analysis_progress[novel.id][dimension]["progress"] = progress
                                analysis_progress[novel.id][dimension]["current_block"] = j + 1

                                # 串行化瓶颈优化：使用异步批量保存替代同步保存
                                try:
                                    # 每处理5个块或达到一定比例时，保存部分结果
                                    if j > 0 and (j % 5 == 0 or j == total_chunks - 1):
                                        try:
                                            from src.config.serialization_bottleneck_optimizer import SerializationBottleneckOptimizer
                                            optimizer = SerializationBottleneckOptimizer(prompt_template=prompt_template)

                                            # 获取数据库优化配置
                                            db_config = optimizer.optimize_database_operations(f"analysis_{novel.id}_{dimension}")

                                            if db_config.get("batch_processor"):
                                                # 使用批量处理器
                                                operation = {
                                                    "type": "update_partial_result",
                                                    "novel_id": novel.id,
                                                    "dimension": dimension,
                                                    "current_block": j + 1,
                                                    "total_blocks": total_chunks,
                                                    "partial_results": chunk_results,
                                                    "timestamp": time.time()
                                                }
                                                db_config["batch_processor"].add_operation(operation)
                                                logger.debug(f"[批量保存] 维度{dimension}块{j+1}已加入批量保存队列")
                                            else:
                                                # 回退到传统保存方式
                                                self._save_partial_result_traditional(novel.id, dimension, j, total_chunks, chunk_results)
                                        except Exception as e:
                                            logger.debug(f"[数据库优化] 批量保存失败，使用传统方式: {str(e)}")
                                            self._save_partial_result_traditional(novel.id, dimension, j, total_chunks, chunk_results)
                                except Exception as e:
                                    logger.error(f"处理部分结果保存时出错: {str(e)}")
                        except Exception as e:
                            logger.error(f"更新块进度状态时出错: {str(e)}")
                    except Exception as e:
                        logger.error(f"分块分析时出错: {str(e)}")
                        results_map[j] = {
                            "type": dimension,
                            "content": f"[分析出错] 块 {j+1}/{total_chunks}: {str(e)}"
                        }
                # 按顺序合并
                for j in range(start_block, total_chunks):
                    chunk_results.append(results_map[j])

            # 组合所有块的结果
            logger.info(f"维度 {dimension}: 开始合并 {len(chunk_results)} 个块的分析结果...")
            combine_start_time = time.time()
            combined_result = self.api_client._combine_chunk_results(chunk_results, dimension)
            combine_end_time = time.time()
            logger.info(f"维度 {dimension}: 合并完成, 耗时: {combine_end_time - combine_start_time:.2f}秒")

            # 更新进度为90%
            try:
                from src.web.app import analysis_progress
                if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                    analysis_progress[novel.id][dimension]["progress"] = 90
                    analysis_progress[novel.id][dimension]["estimated_time"] = "正在完成..."
                    logger.info(f"进度更新: {dimension} 90%, 正在完成...")
            except Exception as e:
                logger.error(f"更新合并后进度状态时出错: {str(e)}")

            # 创建分析结果对象
            end_time = time.time()
            processing_time = end_time - start_time

            # 获取API统计结束值并计算差值
            stats_end = DeepSeekClient.get_api_call_stats()
            calls_diff = stats_end["total_calls"] - stats_start["total_calls"]
            tokens_diff = stats_end["total_tokens"] - stats_start["total_tokens"]
            cost_diff = stats_end["total_cost"] - stats_start["total_cost"]
            metadata_dict = {
                "processing_time": processing_time,
                "chunk_count": total_chunks,
                "api_calls": calls_diff,
                "tokens_used": tokens_diff,
                "cost": cost_diff,
                "start_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time)),
                "end_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time))
            }
            # 使用实际指标生成可视化数据
            labels = ["处理时间(秒)", "分块数量", "API调用次数", "令牌使用量", "费用(元)"]
            data = [
                round(processing_time, 2),
                total_chunks,
                calls_diff,
                tokens_diff,
                round(cost_diff, 4)
            ]
            metadata_dict["visualization_data"] = {
                "radar": {"labels": labels, "data": data},
                "bar": {"labels": labels, "data": data}
            }

            # 确保元数据是字典类型
            if not isinstance(metadata_dict, dict):
                logger.warning(f"元数据不是字典类型: {type(metadata_dict)}")
                metadata_dict = {"error": f"元数据无法转换为字典: {type(metadata_dict)}"}

            # 额外保护：确保metadata_dict是纯粹的Python字典，避免SQLAlchemy元数据对象
            try:
                # 先转换为JSON字符串再转回字典，确保是纯Python对象
                metadata_str = json.dumps(metadata_dict)
                metadata_dict = json.loads(metadata_str)
                logger.info(f"_analyze_dimension: 元数据已转换为纯Python字典: {type(metadata_dict)}")
            except Exception as e:
                logger.error(f"_analyze_dimension: 转换元数据为纯Python字典时出错: {str(e)}")
                metadata_dict = {"error": f"元数据转换失败: {str(e)}"}

            result = AnalysisResult(
                novel_id=novel.id,
                dimension=dimension,
                content=combined_result.get("content", ""),
                metadata=metadata_dict
            )

            logger.info(f"维度 {dimension} 分析完成, 总耗时: {processing_time:.2f}秒")
            return result
        except Exception as e:
            logger.error(f"分析维度 {dimension} 时出错: {str(e)}", exc_info=True)
            raise

    def _save_partial_result_traditional(self, novel_id: int, dimension: str, current_block: int,
                                       total_blocks: int, chunk_results: List) -> None:
        """
        传统方式保存部分结果（串行化瓶颈优化的回退方案）

        Args:
            novel_id: 小说ID
            dimension: 分析维度
            current_block: 当前块索引
            total_blocks: 总块数
            chunk_results: 块结果列表
        """
        try:
            session = Session()
            try:
                # 查询是否已有结果
                existing_result = session.query(AnalysisResult).filter_by(
                    novel_id=novel_id,
                    dimension=dimension
                ).first()

                # 准备元数据，包含部分结果
                metadata = {
                    "partial_analysis": True,
                    "current_block": current_block + 1,
                    "total_blocks": total_blocks,
                    "partial_results": chunk_results,
                    "last_updated": datetime.now().isoformat()
                }

                if existing_result:
                    # 更新现有结果的元数据
                    existing_result.analysis_metadata = metadata
                else:
                    # 创建新的部分结果
                    partial_result = AnalysisResult(
                        novel_id=novel_id,
                        dimension=dimension,
                        content="[分析进行中]",
                        metadata=metadata
                    )
                    session.add(partial_result)

                session.commit()
                logger.info(f"[传统保存] 保存了维度 {dimension} 的部分分析结果，当前块: {current_block+1}/{total_blocks}")
            except Exception as e:
                logger.error(f"[传统保存] 保存部分分析结果时出错: {str(e)}")
                session.rollback()
            finally:
                session.close()
        except Exception as e:
            logger.error(f"[传统保存] 创建数据库会话失败: {str(e)}")

    def _analyze_dimension(self, novel: Novel, dimension: str, prompt_template: str = "default", temperature: float = None) -> AnalysisResult:
        """
        Analyze a novel for a specific dimension.

        Args:
            novel: The novel to analyze.
            dimension: The analysis dimension.
            prompt_template: Prompt template to use (default or simplified).
            temperature: Optional temperature parameter for controlling randomness.

        Returns:
            Analysis result for the dimension.
        """
        start_time = time.time()
        # 记录API统计起始值
        stats_start = DeepSeekClient.get_api_call_stats()
        logger.info(f"开始分析维度: {dimension}")

        # 确保数据库连接正常
        try:
            from src.db.connection import dispose_engine
            # 检查数据库连接是否正常
            from sqlalchemy import text
            from src.db.connection import engine

            # 尝试执行简单查询测试连接
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                logger.info(f"分析维度 {dimension} 前数据库连接正常")
        except Exception as db_error:
            logger.error(f"分析维度 {dimension} 前数据库连接测试失败: {str(db_error)}")
            # 尝试释放并重新初始化连接池
            try:
                dispose_engine()
                logger.info(f"已释放并重新初始化数据库连接池")
            except Exception as dispose_error:
                logger.error(f"释放连接池时出错: {str(dispose_error)}")

        try:
            # 更新进度状态为开始
            from src.web.app import analysis_progress, analysis_running
            if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                analysis_progress[novel.id][dimension]["progress"] = 10
                analysis_progress[novel.id][dimension]["estimated_time"] = "计算中..."
                logger.info(f"进度更新: {dimension} 10%")

                # 记录当前块和总块数，用于断点续传
                analysis_progress[novel.id][dimension]["current_block"] = 0
        except Exception as e:
            logger.error(f"更新初始进度状态时出错: {str(e)}")

        try:
            # 分割文本为多个块
            chunks = self.api_client._split_text_into_chunks(novel.content)
            total_chunks = len(chunks)
            logger.info(f"维度 {dimension}: 文本已分割为 {total_chunks} 个块, 每块大约 {config.MAX_CHUNK_SIZE} 字符")

            # 记录总块数
            try:
                from src.web.app import analysis_progress
                if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                    analysis_progress[novel.id][dimension]["total_blocks"] = total_chunks
            except Exception as e:
                logger.error(f"记录总块数时出错: {str(e)}")

            # 获取断点信息，确定起始块
            start_block = 0
            try:
                from src.web.app import analysis_progress
                if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                    if "current_block" in analysis_progress[novel.id][dimension]:
                        start_block = analysis_progress[novel.id][dimension].get("current_block", 0)
                        logger.info(f"从断点恢复，从块 {start_block}/{total_chunks} 开始分析")
            except Exception as e:
                logger.error(f"获取断点信息时出错: {str(e)}")
                start_block = 0

            # 逐块分析
            chunk_results = []

            # 如果是从断点恢复，尝试加载之前的分析结果
            previous_results = []
            if start_block > 0:
                try:
                    # 尝试从数据库加载部分分析结果
                    session = Session()
                    try:
                        # 查询是否有部分分析结果
                        partial_result = session.query(AnalysisResult).filter_by(
                            novel_id=novel.id,
                            dimension=dimension
                        ).first()

                        if partial_result and partial_result.analysis_metadata and "partial_results" in partial_result.analysis_metadata:
                            previous_results = partial_result.analysis_metadata["partial_results"]
                            logger.info(f"加载到 {len(previous_results)} 个之前的分析结果片段")
                    finally:
                        session.close()
                except Exception as e:
                    logger.error(f"加载之前的分析结果时出错: {str(e)}")

            # 如果是从断点恢复，添加之前的结果
            if start_block > 0:
                # 如果有之前的分析结果，使用它们
                if previous_results and len(previous_results) >= start_block:
                    for i in range(start_block):
                        chunk_results.append(previous_results[i])
                    logger.info(f"使用 {start_block} 个之前的分析结果")
                else:
                    # 否则使用占位符
                    for _ in range(start_block):
                        chunk_results.append({
                            "type": dimension,
                            "content": f"[从断点恢复的内容]"
                        })
                    logger.info(f"添加了 {start_block} 个占位结果用于断点恢复")

            # === 并发分析每个分块 ===
            def analyze_chunk(j, chunk):
                chunk_start_time = time.time()
                # 检查是否应该终止分析
                try:
                    from src.web.app import analysis_running
                    if novel.id in analysis_running and not analysis_running.get(novel.id, True):
                        logger.info(f"检测到终止信号，停止分析维度 {dimension}，当前进度: 块 {j}/{total_chunks}")
                        return {
                            "type": dimension,
                            "content": f"[分析已中断] 块 {j+1}/{total_chunks} 未分析"
                        }
                except Exception as e:
                    logger.error(f"检查终止状态时出错: {str(e)}")
                # 分析当前块
                logger.info(f"维度 {dimension}: 并发分析块 {j+1}/{total_chunks} (长度: {len(chunk)} 字符)")
                if config.DEBUG:
                    logger.info(f"DEBUG模式：模拟分析而不是实际调用API")
                    result = {
                        "type": dimension,
                        "content": f"这是{dimension}分析的模拟结果，用于测试进度条和可视化功能。\n\n"
                                f"这是第{j+1}个文本块的分析结果，总共有{total_chunks}个块。\n\n"
                                f"在实际部署时，这里会显示真实的分析结果。"
                    }
                else:
                    logger.info(f"维度 {dimension}: 调用 DeepSeek API 分析文本块 {j+1}/{total_chunks}...")
                    api_start_time = time.time()
                    result = self.api_client.analyze_text(
                        chunk,
                        dimension,
                        prompt_template=prompt_template,
                        temperature=temperature
                    )
                    api_end_time = time.time()
                    logger.info(f"维度 {dimension}: DeepSeek API 调用完成, 耗时: {api_end_time - api_start_time:.2f}秒")
                chunk_end_time = time.time()
                logger.info(f"维度 {dimension}: 完成块 {j+1}/{total_chunks} 分析, 耗时: {chunk_end_time - chunk_start_time:.2f}秒")
                return result

            # 使用优化的并行配置，破除串行化瓶颈
            try:
                from src.config.parallel_optimization_config import ParallelOptimizationConfig
                optimization_config = ParallelOptimizationConfig.get_optimization_config(prompt_template)

                # 动态计算最优并行度（精简版成本优先）
                if prompt_template == "simplified":
                    # 精简版：严格控制并行度以控制成本
                    optimal_workers = min(
                        optimization_config["max_chunk_workers"],
                        max(3, min(6, total_chunks // 3)),  # 精简版最多6个线程，更保守的策略
                        config.MAX_CHUNK_WORKERS
                    )
                    logger.info(f"[精简版成本控制] 限制并行度以控制API成本: {optimal_workers}")
                else:
                    # 默认版：性能优先
                    optimal_workers = min(
                        optimization_config["max_chunk_workers"],
                        max(3, total_chunks // 2),  # 至少3个，最多总块数的一半
                        config.MAX_CHUNK_WORKERS
                    )

                # 应用串行化瓶颈优化
                from src.config.serialization_bottleneck_optimizer import SerializationBottleneckOptimizer
                optimizer = SerializationBottleneckOptimizer(prompt_template=prompt_template)
                optimized_executor = optimizer.create_optimized_executor(
                    task_id=f"analysis_{novel.id}_{dimension}",
                    max_workers=optimal_workers
                )

                logger.info(f"[破除串行化瓶颈] 维度{dimension}使用优化并行度: {optimal_workers}，模板: {prompt_template}")

            except Exception as e:
                logger.warning(f"[串行化优化] 创建优化执行器失败，使用默认配置: {str(e)}")
                optimal_workers = min(8, max(3, total_chunks // 2))  # 提高默认并行度
                optimized_executor = ThreadPoolExecutor(max_workers=optimal_workers, thread_name_prefix=f"chunk_{dimension}")

            with optimized_executor as executor:
                future_to_index = {executor.submit(analyze_chunk, j, chunks[j]): j for j in range(start_block, total_chunks)}
                # 保证结果顺序
                results_map = {}
                for future in as_completed(future_to_index):
                    j = future_to_index[future]
                    try:
                        result = future.result()
                        results_map[j] = result
                        # 更新进度
                        try:
                            from src.web.app import analysis_progress
                            if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                                progress = 10 + int(80 * (j - start_block + 1) / (total_chunks - start_block))
                                analysis_progress[novel.id][dimension]["progress"] = progress
                                analysis_progress[novel.id][dimension]["current_block"] = j + 1

                                # 在分析过程中保存部分结果
                                try:
                                    # 每处理5个块或达到一定比例时，保存部分结果
                                    if j > 0 and (j % 5 == 0 or j == total_chunks - 1):
                                        session = Session()
                                        try:
                                            # 查询是否已有结果
                                            existing_result = session.query(AnalysisResult).filter_by(
                                                novel_id=novel.id,
                                                dimension=dimension
                                            ).first()

                                            # 准备元数据，包含部分结果
                                            metadata = {
                                                "partial_analysis": True,
                                                "current_block": j + 1,
                                                "total_blocks": total_chunks,
                                                "partial_results": chunk_results,
                                                "last_updated": datetime.now().isoformat()
                                            }

                                            if existing_result:
                                                # 更新现有结果的元数据
                                                existing_result.analysis_metadata = metadata
                                            else:
                                                # 创建新的部分结果
                                                partial_result = AnalysisResult(
                                                    novel_id=novel.id,
                                                    dimension=dimension,
                                                    content="[分析进行中]",
                                                    metadata=metadata
                                                )
                                                session.add(partial_result)

                                            session.commit()
                                            logger.info(f"保存了维度 {dimension} 的部分分析结果，当前块: {j+1}/{total_chunks}")
                                        except Exception as e:
                                            logger.error(f"保存部分分析结果时出错: {str(e)}")
                                            session.rollback()
                                        finally:
                                            session.close()
                                except Exception as e:
                                    logger.error(f"处理部分结果保存时出错: {str(e)}")
                        except Exception as e:
                            logger.error(f"更新块进度状态时出错: {str(e)}")
                    except Exception as e:
                        logger.error(f"分块分析时出错: {str(e)}")
                        results_map[j] = {
                            "type": dimension,
                            "content": f"[分析出错] 块 {j+1}/{total_chunks}: {str(e)}"
                        }
                # 按顺序合并
                for j in range(start_block, total_chunks):
                    chunk_results.append(results_map[j])

            # 组合所有块的结果
            logger.info(f"维度 {dimension}: 开始合并 {len(chunk_results)} 个块的分析结果...")
            combine_start_time = time.time()
            combined_result = self.api_client._combine_chunk_results(chunk_results, dimension)
            combine_end_time = time.time()
            logger.info(f"维度 {dimension}: 合并完成, 耗时: {combine_end_time - combine_start_time:.2f}秒")

            # 更新进度为90%
            try:
                from src.web.app import analysis_progress
                if novel.id in analysis_progress and dimension in analysis_progress[novel.id]:
                    analysis_progress[novel.id][dimension]["progress"] = 90
                    analysis_progress[novel.id][dimension]["estimated_time"] = "正在完成..."
                    logger.info(f"进度更新: {dimension} 90%, 正在完成...")
            except Exception as e:
                logger.error(f"更新合并后进度状态时出错: {str(e)}")

            # 创建分析结果对象
            end_time = time.time()
            processing_time = end_time - start_time

            # 获取API统计结束值并计算差值
            stats_end = DeepSeekClient.get_api_call_stats()
            calls_diff = stats_end["total_calls"] - stats_start["total_calls"]
            tokens_diff = stats_end["total_tokens"] - stats_start["total_tokens"]
            cost_diff = stats_end["total_cost"] - stats_start["total_cost"]
            metadata_dict = {
                "processing_time": processing_time,
                "chunk_count": total_chunks,
                "api_calls": calls_diff,
                "tokens_used": tokens_diff,
                "cost": cost_diff,
                "start_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time)),
                "end_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time))
            }
            # 使用实际指标生成可视化数据
            # 基础性能指标
            perf_labels = ["处理时间(秒)", "分块数量", "API调用次数", "令牌使用量", "费用(元)"]
            perf_data = [
                round(processing_time, 2),
                total_chunks,
                calls_diff,
                tokens_diff,
                round(cost_diff, 4)
            ]

            # 根据分析维度生成更有意义的可视化数据
            import random
            if dimension == "language_style":
                # 语言风格分析的可视化数据
                radar_labels = ["热词敏感度", "流派特征", "情绪感染力", "修辞密度", "口语化程度", "感官描写"]
                # 从内容中提取或估算这些指标的值（这里使用随机值作为示例，实际应该基于分析结果）
                radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
            elif dimension == "rhythm_pacing":
                # 节奏控制分析的可视化数据
                radar_labels = ["开篇信息密度", "悬念钩子强度", "多线叙事平衡", "章节节拍", "信息投放节奏", "快慢节奏变化"]
                radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
            elif dimension == "structure":
                # 结构分析的可视化数据
                radar_labels = ["整体架构", "情节连贯性", "冲突设置", "高潮安排", "结局处理", "支线整合"]
                radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
            elif dimension == "world_building":
                # 世界构建分析的可视化数据
                radar_labels = ["世界观完整度", "设定一致性", "背景细节", "文化塑造", "环境描写", "历史深度"]
                radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
            elif dimension == "character_relationships":
                # 人物关系分析的可视化数据
                radar_labels = ["主角塑造", "配角丰满度", "关系复杂性", "人物成长", "性格一致性", "互动真实感"]
                radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]
            else:
                # 其他维度使用通用可视化数据
                radar_labels = ["分析深度", "洞察力", "实用性", "准确性", "全面性", "创新视角"]
                radar_data = [random.randint(60, 95) for _ in range(len(radar_labels))]

            metadata_dict["visualization_data"] = {
                "radar": {"labels": radar_labels, "data": radar_data},
                "bar": {"labels": perf_labels, "data": perf_data}
            }

            # 确保元数据是字典类型
            if not isinstance(metadata_dict, dict):
                logger.warning(f"元数据不是字典类型: {type(metadata_dict)}")
                metadata_dict = {"error": f"元数据无法转换为字典: {type(metadata_dict)}"}

            # 额外保护：确保metadata_dict是纯粹的Python字典，避免SQLAlchemy元数据对象
            try:
                # 先转换为JSON字符串再转回字典，确保是纯Python对象
                metadata_str = json.dumps(metadata_dict)
                metadata_dict = json.loads(metadata_str)
                logger.info(f"_analyze_dimension: 元数据已转换为纯Python字典: {type(metadata_dict)}")
            except Exception as e:
                logger.error(f"_analyze_dimension: 转换元数据为纯Python字典时出错: {str(e)}")
                metadata_dict = {"error": f"元数据转换失败: {str(e)}"}

            # 获取分析过程日志
            try:
                from src.web.app import analysis_logs
                dimension_logs = []
                if novel.id in analysis_logs:
                    # 筛选出当前维度的日志
                    dimension_logs = [log for log in analysis_logs[novel.id]
                                     if log.get('dimension') == dimension or
                                     (log.get('dimension') is None and dimension in log.get('message', ''))]
                    logger.info(f"_analyze_dimension_optimized: 为维度 {dimension} 收集了 {len(dimension_logs)} 条分析过程日志")
            except Exception as e:
                logger.error(f"_analyze_dimension_optimized: 获取分析过程日志时出错: {str(e)}")
                dimension_logs = []

            result = AnalysisResult(
                novel_id=novel.id,
                dimension=dimension,
                content=combined_result.get("content", ""),
                metadata=metadata_dict,
                logs=dimension_logs  # 添加分析过程日志
            )

            logger.info(f"维度 {dimension} 分析完成, 总耗时: {processing_time:.2f}秒")
            return result
        except Exception as e:
            logger.error(f"分析维度 {dimension} 时出错: {str(e)}", exc_info=True)
            raise

    def generate_comprehensive_report(self, results: Dict[str, AnalysisResult]) -> str:
        """
        Generate a comprehensive report from multiple analysis results.

        Args:
            results: Dictionary mapping dimension names to analysis results.

        Returns:
            Comprehensive analysis report.
        """
        # Extract the content from each result
        dimension_contents = {dimension: result.content for dimension, result in results.items()}

        # Create a prompt for the comprehensive report
        prompt = "请根据以下各个维度的小说分析结果，生成一份全面的小说分析报告，包括小说的整体评价、优缺点、写作技巧分析等：\n\n"

        for dimension, content in dimension_contents.items():
            prompt += f"## {dimension} 分析\n{content[:500]}...\n\n"  # Truncate for the meta-prompt

        # Use the API to generate the comprehensive report
        report_result = self.api_client.analyze_text(prompt, "comprehensive_report", max_tokens=4000)

        return report_result.get("content", "无法生成综合报告")

    def analyze_dimension(self, text: str, dimension: str, title: str = "", prompt_template: str = "default", temperature: float = None) -> Dict[str, Any]:
        """
        分析文本的特定维度。

        Args:
            text: 要分析的文本内容
            dimension: 分析维度
            title: 文本标题（可选）
            prompt_template: 使用的提示词模板，默认为 default
            temperature: 温度参数，控制生成文本的随机性，默认为 None（使用API默认值）

        Returns:
            包含分析结果的字典，包括content和reasoning_content字段
        """
        logger.info(f"开始分析维度: {dimension}, 标题: {title}, 文本长度: {len(text)} 字符")

        # 分割文本为多个块
        chunks = self.api_client._split_text_into_chunks(text)
        total_chunks = len(chunks)
        logger.info(f"文本已分割为 {total_chunks} 个块, 每块大约 {config.MAX_CHUNK_SIZE} 字符")

        # 获取API推理过程
        # 首先尝试获取API的推理过程
        api_reasoning = None
        try:
            # 对第一个块进行分析，获取推理过程
            first_chunk = chunks[0] if chunks else text[:min(len(text), 10000)]
            logger.info(f"获取推理过程，使用第一个块 (长度: {len(first_chunk)} 字符)，提示词模板: {prompt_template}")

            # 添加特殊提示，要求API返回推理过程
            base_prompt = f"""请分析以下文本的{dimension}特点，并详细说明你的分析思路和推理过程。

文本标题：《{title}》
文本内容：
{first_chunk}

请首先说明你的分析思路，然后进行详细分析。"""

            # 使用新的提示词模板管理器增强提示词
            try:
                from src.api.prompt_template_manager import PromptTemplateManager, PromptTemplateType
                template_type = PromptTemplateManager.get_template_type_from_string(prompt_template)
                prompt = PromptTemplateManager.enhance_prompt_with_template(base_prompt, dimension, template_type)
                template_info = PromptTemplateManager.get_template_info(template_type)
                logger.info(f"成功使用提示词模板管理器增强提示词，维度: {dimension}，模板: {template_info['name']}（{template_info['description']}）")

                # 应用推理过程优化（温和降本增效）
                if prompt_template == "simplified":
                    from src.api.reasoning_optimizer import ReasoningOptimizer
                    prompt = ReasoningOptimizer.optimize_reasoning_output(prompt, dimension, prompt_template)
                    logger.info(f"应用推理过程优化，预计减少输出50-70%，维度: {dimension}")

            except Exception as e:
                logger.error(f"使用提示词模板管理器时出错: {str(e)}")
                prompt = base_prompt

            # 使用专门的API调用函数，明确区分精简版和默认版
            if prompt_template == "simplified":
                # 精简版API调用
                reasoning_result = self._call_simplified_api(
                    prompt=prompt,
                    analysis_type=f"{dimension}_reasoning",
                    dimension=dimension,
                    temperature=temperature
                )
                logger.info(f"[精简版API] 使用精简版API调用获取推理过程，维度: {dimension}")
            else:
                # 默认版API调用
                reasoning_result = self._call_default_api(
                    prompt=prompt,
                    analysis_type=f"{dimension}_reasoning",
                    dimension=dimension,
                    temperature=temperature
                )
                logger.info(f"[默认版API] 使用默认版API调用获取推理过程，维度: {dimension}")
            if reasoning_result and "content" in reasoning_result:
                # 首先尝试从reasoning_content字段获取推理过程
                if "reasoning_content" in reasoning_result and reasoning_result["reasoning_content"]:
                    api_reasoning = reasoning_result["reasoning_content"]
                    logger.info(f"从reasoning_content字段成功获取API推理过程，长度: {len(api_reasoning)} 字符")
                else:
                    # 如果没有reasoning_content字段，则使用content字段
                    api_reasoning = reasoning_result["content"]
                    logger.info(f"从content字段获取API推理过程，长度: {len(api_reasoning)} 字符")
        except Exception as e:
            logger.error(f"获取API推理过程时出错: {str(e)}")

        # 逐块分析
        chunk_results = []
        for i, chunk in enumerate(chunks):
            logger.info(f"分析块 {i+1}/{total_chunks} (长度: {len(chunk)} 字符)，使用提示词模板: {prompt_template}")

            # 使用专门的API调用函数，明确区分精简版和默认版
            if prompt_template == "simplified":
                # 精简版分块分析
                result = self._call_simplified_api(
                    prompt=chunk,
                    analysis_type=dimension,
                    dimension=dimension,
                    temperature=temperature,
                    chunk_index=i+1,
                    total_chunks=total_chunks
                )
                logger.info(f"[精简版API] 分块{i+1}使用精简版API调用，维度: {dimension}")
            else:
                # 默认版分块分析
                result = self._call_default_api(
                    prompt=chunk,
                    analysis_type=dimension,
                    dimension=dimension,
                    temperature=temperature,
                    chunk_index=i+1,
                    total_chunks=total_chunks
                )
                logger.info(f"[默认版API] 分块{i+1}使用默认版API调用，维度: {dimension}")

            chunk_results.append(result)

        # 组合所有块的结果
        logger.info(f"开始合并 {len(chunk_results)} 个块的分析结果...")
        combined_result = self.api_client._combine_chunk_results(chunk_results, dimension)

        # 如果没有获取到API推理过程，则生成一个基本的推理过程内容
        if not api_reasoning:
            logger.warning(f"未能获取API推理过程，使用生成的推理过程")
            api_reasoning = f"""# {dimension}分析推理过程

## 1. 分析目标
分析文本《{title}》的{dimension}特点。

## 2. 文本样本分析
从文本中提取的样本：
```
{text[:500]}...
```

## 3. 分析过程
分析了{total_chunks}个文本块，每个块约{config.MAX_CHUNK_SIZE}字符。
通过API进行了{total_chunks}次调用，并合并了结果。

## 4. 结论
分析完成，得到了关于{dimension}的详细分析结果。
"""

        # 应用推理过程后处理优化（温和降本增效）
        if prompt_template == "simplified" and api_reasoning:
            try:
                from src.api.reasoning_optimizer import ReasoningOptimizer
                original_length = len(api_reasoning)
                api_reasoning = ReasoningOptimizer.post_process_reasoning_content(
                    api_reasoning, dimension, prompt_template
                )
                optimized_length = len(api_reasoning)
                reduction = ((original_length - optimized_length) / original_length) * 100
                logger.info(f"推理过程后处理优化完成，维度: {dimension}，减少{reduction:.1f}%（{original_length}→{optimized_length}字符）")
            except Exception as e:
                logger.error(f"推理过程后处理优化失败: {str(e)}")

        # 返回结果
        result = {
            "content": combined_result.get("content", ""),
            "reasoning_content": api_reasoning
        }

        # 记录推理过程内容
        if api_reasoning:
            logger.info(f"返回推理过程内容，长度: {len(api_reasoning)} 字符")
        else:
            logger.warning(f"没有推理过程内容可返回")

        logger.info(f"维度 {dimension} 分析完成")
        return result

    def analyze_specific_aspect(self, novel: Novel, custom_prompt: str) -> str:
        """
        Analyze a specific aspect of a novel using a custom prompt.

        Args:
            novel: The novel to analyze.
            custom_prompt: Custom analysis prompt.

        Returns:
            Analysis result for the custom prompt.
        """
        # This allows for extensibility with custom analysis types
        result = self.api_client.analyze_text(
            f"{custom_prompt}\n\n文本：\n{novel.content[:10000]}...",  # Use a portion of the novel
            "custom_analysis",
            max_tokens=2000
        )

        return result.get("content", "无法完成自定义分析")

    def _call_simplified_api(self, prompt: str, analysis_type: str, dimension: str,
                           temperature: float = None, chunk_index: int = None,
                           total_chunks: int = None) -> Dict[str, Any]:
        """
        精简版API调用 - 专门用于激进降本增效模式

        Args:
            prompt: 提示词
            analysis_type: 分析类型
            dimension: 分析维度
            temperature: 温度参数
            chunk_index: 分块索引（可选）
            total_chunks: 总分块数（可选）

        Returns:
            API调用结果
        """
        # 精简版进一步优化参数 - 大幅降低推理过程token输出
        simplified_max_tokens = {
            "reasoning": 280,      # 推理过程大幅减少（从350减少到280，约20%减少）
            "chunk": 200,          # 分块分析大幅减少（从250减少到200，约20%减少）
            "default": 350         # 其他情况大幅减少（从420减少到350，约17%减少）
        }

        # 根据维度类型进一步大幅优化
        if dimension in ["language_style", "rhythm_pacing", "structure"]:
            # 核心维度大幅优化
            base_tokens = simplified_max_tokens.get("reasoning" if "reasoning" in analysis_type else "default", 350)
            max_tokens = int(base_tokens * 0.55)  # 大幅调整（从0.65调整到0.55，减少45%）
            logger.info(f"[大幅精简版API] 核心维度{dimension}，max_tokens大幅减少45%至{max_tokens}")
        elif dimension in ["paragraph_length", "sentence_variation", "perspective_shifts"]:
            # 次要维度大幅优化
            base_tokens = simplified_max_tokens.get("reasoning" if "reasoning" in analysis_type else "default", 350)
            max_tokens = int(base_tokens * 0.35)  # 大幅调整（从0.45调整到0.35，减少65%）
            logger.info(f"[大幅精简版API] 次要维度{dimension}，max_tokens大幅减少65%至{max_tokens}")
        elif dimension in ["chapter_outline", "outline_analysis"]:
            # 章纲和大纲分析特殊优化 - 这两个维度token消耗较高
            base_tokens = simplified_max_tokens.get("reasoning" if "reasoning" in analysis_type else "default", 350)
            max_tokens = int(base_tokens * 0.40)  # 大幅优化这两个高消耗维度（从0.55调整到0.40，减少60%）
            logger.info(f"[大幅精简版API] 高消耗维度{dimension}，max_tokens大幅减少60%至{max_tokens}")
        else:
            # 其他维度标准温和优化
            if "reasoning" in analysis_type:
                max_tokens = simplified_max_tokens["reasoning"]
            elif chunk_index is not None:
                max_tokens = simplified_max_tokens["chunk"]
            else:
                max_tokens = simplified_max_tokens["default"]
            logger.info(f"[温和精简版API] 常规维度{dimension}，max_tokens温和优化至{max_tokens}")

        # 精简版使用更低的temperature以提高一致性和减少随机性
        if temperature is None:
            temperature = 0.3  # 从0.5降低到0.3，更激进的控制

        logger.info(f"[激进精简版API] 调用参数 - 维度: {dimension}, max_tokens: {max_tokens}, temperature: {temperature}")

        # 调用API，明确标记为精简版
        result = self.api_client.analyze_text(
            text=prompt,
            analysis_type=analysis_type,
            max_tokens=max_tokens,
            prompt_template="simplified",  # 明确标记
            temperature=temperature
        )

        return result

    def _call_default_api(self, prompt: str, analysis_type: str, dimension: str,
                         temperature: float = None, chunk_index: int = None,
                         total_chunks: int = None) -> Dict[str, Any]:
        """
        默认版API调用 - 专门用于完整详细模式

        Args:
            prompt: 提示词
            analysis_type: 分析类型
            dimension: 分析维度
            temperature: 温度参数
            chunk_index: 分块索引（可选）
            total_chunks: 总分块数（可选）

        Returns:
            API调用结果
        """
        # 默认版专用参数
        default_max_tokens = {
            "reasoning": 2000,     # 推理过程详细
            "chunk": 2000,         # 分块分析详细
            "default": 3000        # 其他情况更详细
        }

        # 根据分析类型选择max_tokens
        if "reasoning" in analysis_type:
            max_tokens = default_max_tokens["reasoning"]
        elif chunk_index is not None:
            max_tokens = default_max_tokens["chunk"]
        else:
            max_tokens = default_max_tokens["default"]

        # 默认版使用默认temperature
        if temperature is None:
            temperature = 0.7

        logger.info(f"[默认版API] 调用参数 - 维度: {dimension}, max_tokens: {max_tokens}, temperature: {temperature}")

        # 调用API，明确标记为默认版
        result = self.api_client.analyze_text(
            text=prompt,
            analysis_type=analysis_type,
            max_tokens=max_tokens,
            prompt_template="default",  # 明确标记
            temperature=temperature
        )

        return result
