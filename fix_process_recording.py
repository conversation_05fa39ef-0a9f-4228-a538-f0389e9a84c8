"""
修复分析过程记录功能
"""
import os
import sys
import logging
import shutil
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def check_environment_variables():
    """检查环境变量是否正确设置"""
    env_vars = {
        'ENABLE_DETAILED_PROCESS_RECORDING': os.environ.get('ENABLE_DETAILED_PROCESS_RECORDING', 'False'),
        'SAVE_FULL_API_INTERACTIONS': os.environ.get('SAVE_FULL_API_INTERACTIONS', 'False'),
        'SAVE_PROMPTS': os.environ.get('SAVE_PROMPTS', 'False'),
        'ENHANCED_LOG_CATEGORIES': os.environ.get('ENHANCED_LOG_CATEGORIES', 'False'),
        'RECORD_INTERMEDIATE_RESULTS': os.environ.get('RECORD_INTERMEDIATE_RESULTS', 'False'),
        'PROCESS_RECORDING_DEBUG': os.environ.get('PROCESS_RECORDING_DEBUG', 'False'),
        'PROCESS_RECORDING_VERBOSE': os.environ.get('PROCESS_RECORDING_VERBOSE', 'False'),
        'FORCE_PROCESS_RECORDING': os.environ.get('FORCE_PROCESS_RECORDING', 'False')
    }
    
    logger.info("当前环境变量设置:")
    for var, value in env_vars.items():
        logger.info(f"  {var} = {value}")
    
    # 检查是否启用了分析过程记录
    is_enabled = os.environ.get('ENABLE_DETAILED_PROCESS_RECORDING', 'False').lower() == 'true'
    logger.info(f"分析过程记录功能是否启用: {is_enabled}")
    
    return is_enabled

def check_process_recording_patch():
    """检查分析过程记录补丁文件是否存在"""
    patch_file = os.path.join('src', 'services', 'process_recording_patch.py')
    if os.path.exists(patch_file):
        logger.info(f"分析过程记录补丁文件存在: {patch_file}")
        return True
    else:
        logger.error(f"分析过程记录补丁文件不存在: {patch_file}")
        return False

def check_analysis_process_model():
    """检查分析过程模型是否存在"""
    model_file = os.path.join('src', 'models', 'analysis_process.py')
    if os.path.exists(model_file):
        logger.info(f"分析过程模型文件存在: {model_file}")
        return True
    else:
        logger.error(f"分析过程模型文件不存在: {model_file}")
        return False

def check_analysis_service():
    """检查分析服务是否导入了ProcessRecorder"""
    service_file = os.path.join('src', 'services', 'analysis_service.py')
    if not os.path.exists(service_file):
        logger.error(f"分析服务文件不存在: {service_file}")
        return False
    
    try:
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'from src.services.process_recording_patch import ProcessRecorder' in content:
            logger.info("分析服务已导入ProcessRecorder")
            has_import = True
        else:
            logger.warning("分析服务未导入ProcessRecorder")
            has_import = False
        
        if 'ProcessRecorder.record_init' in content:
            logger.info("分析服务使用了ProcessRecorder.record_init")
            has_usage = True
        else:
            logger.warning("分析服务未使用ProcessRecorder.record_init")
            has_usage = False
        
        return has_import and has_usage
    except Exception as e:
        logger.error(f"检查分析服务文件时出错: {e}")
        return False

def create_env_file():
    """创建环境变量设置文件"""
    env_file = 'set_process_recording_env.bat'
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write('@echo off\n')
            f.write('echo 设置分析过程记录环境变量...\n')
            f.write('set ENABLE_DETAILED_PROCESS_RECORDING=True\n')
            f.write('set SAVE_FULL_API_INTERACTIONS=True\n')
            f.write('set SAVE_PROMPTS=True\n')
            f.write('set ENHANCED_LOG_CATEGORIES=True\n')
            f.write('set RECORD_INTERMEDIATE_RESULTS=True\n')
            f.write('set PROCESS_RECORDING_DEBUG=True\n')
            f.write('set PROCESS_RECORDING_VERBOSE=True\n')
            f.write('set FORCE_PROCESS_RECORDING=True\n')
            f.write('echo 环境变量已设置，现在可以运行Python脚本\n')
            f.write('echo 例如: python test_process_recording.py\n')
            f.write('cmd /k\n')
        
        logger.info(f"已创建环境变量设置文件: {env_file}")
        return True
    except Exception as e:
        logger.error(f"创建环境变量设置文件时出错: {e}")
        return False

def create_direct_test_script():
    """创建直接测试脚本"""
    test_file = 'direct_test_process_recording.py'
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write('''"""
直接测试分析过程记录功能，不依赖环境变量
"""
import os
import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

# 强制设置环境变量
os.environ['ENABLE_DETAILED_PROCESS_RECORDING'] = 'True'
os.environ['SAVE_FULL_API_INTERACTIONS'] = 'True'
os.environ['SAVE_PROMPTS'] = 'True'
os.environ['ENHANCED_LOG_CATEGORIES'] = 'True'
os.environ['RECORD_INTERMEDIATE_RESULTS'] = 'True'
os.environ['PROCESS_RECORDING_DEBUG'] = 'True'
os.environ['PROCESS_RECORDING_VERBOSE'] = 'True'
os.environ['FORCE_PROCESS_RECORDING'] = 'True'

# 导入必要的模块
try:
    from src.db.connection import Session
    from src.models.analysis_process import AnalysisProcess
    from src.models.novel import Novel
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    sys.exit(1)

# 定义ProcessRecorder类，不依赖外部模块
class DirectProcessRecorder:
    """直接的分析过程记录器，不依赖环境变量"""
    
    @staticmethod
    def record_process(novel_id, dimension, block_index, total_blocks, stage, 
                      input_text=None, output_text=None, prompt=None, 
                      api_request=None, api_response=None, 
                      processing_time=None, tokens=None, 
                      is_successful=True, error_message=None,
                      result_id=None, metadata=None):
        """记录分析过程"""
        try:
            session = Session()
            
            # 创建分析过程记录
            process = AnalysisProcess(
                novel_id=novel_id,
                dimension=dimension,
                block_index=block_index,
                total_blocks=total_blocks,
                processing_stage=stage,
                result_id=result_id,
                input_text=input_text,
                output_text=output_text,
                prompt_used=prompt,
                api_request=api_request,
                api_response=api_response,
                processing_time=processing_time,
                tokens_used=tokens,
                is_successful=is_successful,
                error_message=error_message,
                process_metadata=metadata or {}
            )
            
            session.add(process)
            session.commit()
            
            process_id = process.id
            logger.info(f"已记录分析过程: ID={process_id}, 小说ID={novel_id}, 维度={dimension}, 阶段={stage}")
            
            session.close()
            return process_id
        
        except Exception as e:
            logger.error(f"记录分析过程时出错: {str(e)}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return None

def check_database_connection():
    """检查数据库连接是否正常"""
    try:
        session = Session()
        # 尝试查询一条记录
        novel = session.query(Novel).first()
        if novel:
            logger.info(f"数据库连接正常，找到小说: ID={novel.id}, 标题={novel.title}")
        else:
            logger.warning("数据库连接正常，但没有找到任何小说")
        session.close()
        return True, novel.id if novel else None
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False, None

def check_existing_processes():
    """检查数据库中是否存在分析过程记录"""
    try:
        session = Session()
        # 查询分析过程记录总数
        total_count = session.query(AnalysisProcess).count()
        logger.info(f"数据库中共有 {total_count} 条分析过程记录")
        
        # 查询最近的5条记录
        recent_processes = session.query(AnalysisProcess).order_by(AnalysisProcess.id.desc()).limit(5).all()
        logger.info(f"最近的 {len(recent_processes)} 条分析过程记录:")
        for process in recent_processes:
            logger.info(f"  ID={process.id}, 小说ID={process.novel_id}, 维度={process.dimension}, 阶段={process.processing_stage}, 时间={process.created_at}")
        
        session.close()
        return total_count > 0
    except Exception as e:
        logger.error(f"查询分析过程记录失败: {e}")
        return False

def create_test_process_record(novel_id):
    """创建一条测试分析过程记录"""
    if not novel_id:
        logger.error("没有提供小说ID，无法创建测试记录")
        return False
    
    try:
        # 创建测试记录
        logger.info(f"尝试为小说ID={novel_id}创建测试分析过程记录")
        process_id = DirectProcessRecorder.record_process(
            novel_id=novel_id,
            dimension="test_dimension",
            block_index=0,
            total_blocks=1,
            stage="test",
            input_text="测试输入文本",
            output_text="测试输出文本",
            prompt="测试提示词",
            processing_time=100,
            tokens=50,
            metadata={"test": True, "timestamp": datetime.now().isoformat()}
        )
        
        if process_id:
            logger.info(f"成功创建测试记录，ID={process_id}")
            return True
        else:
            logger.error("创建测试记录失败")
            return False
    except Exception as e:
        logger.error(f"创建测试记录时出错: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始直接测试分析过程记录功能")
    
    # 检查数据库连接
    db_ok, novel_id = check_database_connection()
    if not db_ok:
        logger.error("数据库连接失败，无法继续测试")
        return
    
    # 检查现有记录
    has_records = check_existing_processes()
    if has_records:
        logger.info("数据库中已存在分析过程记录")
    else:
        logger.warning("数据库中没有找到任何分析过程记录")
    
    # 创建测试记录
    test_ok = create_test_process_record(novel_id)
    if test_ok:
        logger.info("测试成功，分析过程记录功能正常工作")
    else:
        logger.error("测试失败，分析过程记录功能不正常")
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
''')
        
        logger.info(f"已创建直接测试脚本: {test_file}")
        return True
    except Exception as e:
        logger.error(f"创建直接测试脚本时出错: {e}")
        return False

def create_force_process_recording_script():
    """创建强制启用分析过程记录的脚本"""
    script_file = 'force_enable_process_recording.py'
    try:
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write('''"""
强制启用分析过程记录功能
"""
import os
import sys
import logging
import importlib
import types

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def patch_process_recorder():
    """修补ProcessRecorder类的is_process_recording_enabled方法"""
    try:
        # 导入ProcessRecorder类
        from src.services.process_recording_patch import ProcessRecorder
        
        # 保存原始方法
        original_method = ProcessRecorder.is_process_recording_enabled
        
        # 定义新方法
        def patched_method():
            """始终返回True的方法"""
            logger.info("使用修补后的is_process_recording_enabled方法，强制返回True")
            return True
        
        # 替换方法
        ProcessRecorder.is_process_recording_enabled = staticmethod(patched_method)
        
        logger.info("成功修补ProcessRecorder.is_process_recording_enabled方法")
        return True
    except ImportError:
        logger.error("无法导入ProcessRecorder类，修补失败")
        return False
    except Exception as e:
        logger.error(f"修补ProcessRecorder类时出错: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始强制启用分析过程记录功能")
    
    # 修补ProcessRecorder类
    patch_ok = patch_process_recorder()
    if patch_ok:
        logger.info("修补成功，分析过程记录功能已强制启用")
    else:
        logger.error("修补失败，无法强制启用分析过程记录功能")
    
    logger.info("修补完成")

if __name__ == "__main__":
    main()
''')
        
        logger.info(f"已创建强制启用分析过程记录的脚本: {script_file}")
        return True
    except Exception as e:
        logger.error(f"创建强制启用分析过程记录的脚本时出错: {e}")
        return False

def create_modified_startup_script():
    """创建修改后的启动脚本"""
    script_file = '启动九猫_强制记录分析过程.vbs'
    try:
        # 读取原始启动脚本
        original_script = '启动九猫_分析过程修复版.vbs'
        if not os.path.exists(original_script):
            logger.error(f"原始启动脚本不存在: {original_script}")
            return False
        
        with open(original_script, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修改脚本内容
        modified_content = content.replace(
            'set ENABLE_DETAILED_PROCESS_RECORDING=True',
            'set ENABLE_DETAILED_PROCESS_RECORDING=True\ntempFile.WriteLine("set PYTHONPATH=%PYTHONPATH%;%CD%")'
        )
        
        # 添加额外的环境变量
        modified_content = modified_content.replace(
            'set FORCE_PROCESS_RECORDING=True',
            'set FORCE_PROCESS_RECORDING=True\ntempFile.WriteLine("set NINECATS_FORCE_PROCESS_RECORDING=True")'
        )
        
        # 写入修改后的脚本
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        logger.info(f"已创建修改后的启动脚本: {script_file}")
        return True
    except Exception as e:
        logger.error(f"创建修改后的启动脚本时出错: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始修复分析过程记录功能")
    
    # 检查环境变量
    env_ok = check_environment_variables()
    if not env_ok:
        logger.warning("环境变量未正确设置，分析过程记录功能未启用")
    
    # 检查分析过程记录补丁文件
    patch_ok = check_process_recording_patch()
    if not patch_ok:
        logger.error("分析过程记录补丁文件不存在，无法继续修复")
        return
    
    # 检查分析过程模型
    model_ok = check_analysis_process_model()
    if not model_ok:
        logger.error("分析过程模型文件不存在，无法继续修复")
        return
    
    # 检查分析服务
    service_ok = check_analysis_service()
    if not service_ok:
        logger.warning("分析服务未正确集成ProcessRecorder")
    
    # 创建环境变量设置文件
    env_file_ok = create_env_file()
    if not env_file_ok:
        logger.warning("创建环境变量设置文件失败")
    
    # 创建直接测试脚本
    test_script_ok = create_direct_test_script()
    if not test_script_ok:
        logger.warning("创建直接测试脚本失败")
    
    # 创建强制启用分析过程记录的脚本
    force_script_ok = create_force_process_recording_script()
    if not force_script_ok:
        logger.warning("创建强制启用分析过程记录的脚本失败")
    
    # 创建修改后的启动脚本
    startup_script_ok = create_modified_startup_script()
    if not startup_script_ok:
        logger.warning("创建修改后的启动脚本失败")
    
    logger.info("修复完成，请按照以下步骤操作：")
    logger.info("1. 关闭当前运行的九猫系统")
    logger.info("2. 双击'启动九猫_强制记录分析过程.vbs'启动系统")
    logger.info("3. 重新进行分析")
    logger.info("4. 查看分析过程")
    logger.info("")
    logger.info("如果仍然无法看到分析过程，请尝试以下步骤：")
    logger.info("1. 运行'set_process_recording_env.bat'设置环境变量")
    logger.info("2. 在打开的命令行窗口中运行'python direct_test_process_recording.py'")
    logger.info("3. 检查是否能成功创建测试记录")
    logger.info("4. 如果测试成功，说明数据库和模型没有问题，可能是环境变量没有正确传递给应用程序")

if __name__ == "__main__":
    main()
