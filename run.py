"""
九猫小说分析系统启动脚本
"""
import os
import sys
import logging
import shutil

# 确保工作目录为脚本所在目录
os.chdir(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log')
    ]
)
logger = logging.getLogger(__name__)

def _copy_fix_script():
    """复制修复脚本到静态目录"""
    try:
        # 确保源文件存在
        if os.path.exists('fix-unexpected-identifier.js'):
            # 确保目标目录存在
            os.makedirs('src/web/static/js', exist_ok=True)

            # 复制到静态目录
            shutil.copy('fix-unexpected-identifier.js', 'src/web/static/js/')
            logger.info("已复制fix-unexpected-identifier.js到静态目录")

            # 也复制到备用路径
            os.makedirs('src/web/direct-static/js', exist_ok=True)
            shutil.copy('fix-unexpected-identifier.js', 'src/web/direct-static/js/')
            logger.info("已复制fix-unexpected-identifier.js到备用静态目录")

            return True
        else:
            logger.warning("未找到fix-unexpected-identifier.js文件，跳过复制")
            return False
    except Exception as e:
        logger.error(f"复制修复脚本到静态目录时出错: {str(e)}")
        return False

def main():
    """主函数，启动九猫小说分析系统"""
    logger.info("正在启动九猫小说分析系统...")

    # 加载启动修复模块
    _load_startup_fix()

    # 复制修复脚本到静态目录
    _copy_fix_script()

    # 应用内存优化设置
    _apply_memory_optimizations()

    # 优化数据库连接池
    _optimize_database()

    # 检查是否存在main.py
    if os.path.exists('main.py'):
        logger.info("找到main.py，使用它启动系统")
        try:
            import main
            # 如果main模块有run_app_with_monitoring函数，则调用它
            if hasattr(main, 'run_app_with_monitoring'):
                logger.info("使用run_app_with_monitoring启动系统")
                main.run_app_with_monitoring()
            # 否则，如果main是作为脚本运行的，则直接导入它
            else:
                logger.info("直接导入main模块")
        except Exception as e:
            logger.error(f"使用main.py启动系统时出错: {str(e)}")
            logger.info("尝试使用src.web.app启动系统")
            _start_with_app()
    else:
        logger.info("未找到main.py，使用src.web.app启动系统")
        _start_with_app()

def _load_startup_fix():
    """加载启动修复模块"""
    try:
        # 检查启动修复模块是否存在
        if os.path.exists('src/startup_fix.py'):
            logger.info("找到启动修复模块，正在加载...")
            try:
                import src.startup_fix
                logger.info("启动修复模块已加载")
                return True
            except Exception as e:
                logger.error(f"加载启动修复模块时出错: {str(e)}")
                return False
        else:
            logger.info("未找到启动修复模块，尝试创建...")

            # 确保目录存在
            os.makedirs('src', exist_ok=True)

            # 创建基本的启动修复模块
            with open('src/startup_fix.py', 'w', encoding='utf-8') as f:
                f.write('''"""
九猫应用启动优化和修复模块
该模块解决了应用启动和运行中的常见问题
"""
import logging
import threading

logger = logging.getLogger(__name__)

def run_fixes():
    """运行修复"""
    logger.info("运行基本修复")

# 在导入时自动运行
if __name__ != "__main__":
    # 创建一个后台线程来运行修复
    fix_thread = threading.Thread(target=run_fixes)
    fix_thread.daemon = True
    fix_thread.start()
''')

            logger.info("已创建基本的启动修复模块")

            # 尝试导入
            try:
                import src.startup_fix
                logger.info("新创建的启动修复模块已加载")
                return True
            except Exception as e:
                logger.error(f"加载新创建的启动修复模块时出错: {str(e)}")
                return False
    except Exception as e:
        logger.error(f"处理启动修复模块时出错: {str(e)}")
        return False

def _apply_memory_optimizations():
    """应用内存优化设置"""
    try:
        # 强制启用内存优化模式
        os.environ['MEMORY_OPTIMIZED'] = 'True'
        memory_optimized = True

        # 检查是否启用了低内存模式
        low_memory_mode = os.environ.get('LOW_MEMORY_MODE', 'False').lower() == 'true'

        # 设置内存监控参数
        warning_threshold = float(os.environ.get('MEMORY_WARNING_THRESHOLD', '75'))
        critical_threshold = float(os.environ.get('MEMORY_CRITICAL_THRESHOLD', '85'))
        check_interval = float(os.environ.get('MEMORY_CHECK_INTERVAL', '3'))

        # 确保阈值合理
        if warning_threshold < 60:
            warning_threshold = 60
            logger.warning(f"内存警告阈值过低，已调整为 {warning_threshold}%")

        if critical_threshold < warning_threshold + 5:
            critical_threshold = warning_threshold + 10
            logger.warning(f"内存危险阈值过低，已调整为 {critical_threshold}%")

        logger.info(f"内存警告阈值: {warning_threshold}%")
        logger.info(f"内存危险阈值: {critical_threshold}%")

        # 设置线程池大小
        thread_pool_size = int(os.environ.get('THREAD_POOL_SIZE', '8'))
        max_workers = int(os.environ.get('MAX_WORKERS', '8'))

        # 确保线程池大小合理
        if thread_pool_size < 4:
            thread_pool_size = 4
            logger.warning(f"线程池大小过小，已调整为 {thread_pool_size}")

        if max_workers < 4:
            max_workers = 4
            logger.warning(f"最大工作线程数过小，已调整为 {max_workers}")

        # 设置并行分析开关
        disable_parallel = os.environ.get('DISABLE_PARALLEL_ANALYSIS', 'False').lower() == 'true'

        # 应用设置到配置模块
        try:
            import config

            # 设置线程池大小
            config.THREAD_POOL_SIZE = thread_pool_size
            config.MAX_WORKERS = max_workers

            # 设置并行分析开关
            if disable_parallel:
                config.PARALLEL_ANALYSIS_ENABLED = False
                logger.info("已禁用并行分析")
            else:
                # 确保并行分析已启用
                config.PARALLEL_ANALYSIS_ENABLED = True
                logger.info("已启用并行分析")

            logger.info(f"已设置线程池大小: {thread_pool_size}")
            logger.info(f"已设置最大工作线程数: {max_workers}")

            # 设置日志级别
            reduce_logging = os.environ.get('REDUCE_LOGGING', 'False').lower() == 'true'
            if reduce_logging and hasattr(config, 'DEBUG') and not config.DEBUG:
                # 在非调试模式下减少日志输出
                logging.getLogger('werkzeug').setLevel(logging.WARNING)
                logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
                logger.info("已减少非关键日志输出")
        except ImportError:
            logger.warning("无法导入config模块，跳过配置设置")

        # 启动内存监控
        try:
            # 导入内存监控模块
            from src.utils.memory_monitor import start_memory_monitoring

            # 启动内存监控
            start_memory_monitoring(
                warning_threshold=warning_threshold,
                critical_threshold=critical_threshold,
                check_interval=check_interval
            )

            logger.info(f"内存监控已启动（警告阈值: {warning_threshold}%，危险阈值: {critical_threshold}%，检查间隔: {check_interval}秒）")
        except ImportError:
            logger.warning("内存监控模块不可用，跳过内存监控")
        except Exception as e:
            logger.error(f"启动内存监控时出错: {str(e)}")

        # 运行自动修复
        try:
            from src.utils.auto_fixer import run_auto_fixes_async

            # 异步运行自动修复
            fix_thread = run_auto_fixes_async()
            logger.info("自动修复已启动（异步模式）")
        except ImportError:
            logger.warning("自动修复模块不可用，跳过自动修复")
        except Exception as e:
            logger.error(f"启动自动修复时出错: {str(e)}")
    except Exception as e:
        logger.error(f"应用内存优化设置时出错: {str(e)}")

def _optimize_database():
    """优化数据库连接池"""
    try:
        # 检查是否启用了内存优化模式
        memory_optimized = os.environ.get('MEMORY_OPTIMIZED', 'False').lower() == 'true'
        low_memory_mode = os.environ.get('LOW_MEMORY_MODE', 'False').lower() == 'true'

        # 获取数据库连接池配置
        pool_size = int(os.environ.get('DB_POOL_SIZE', '10'))
        max_overflow = int(os.environ.get('DB_MAX_OVERFLOW', '10'))

        # 确保连接池大小合理
        if pool_size < 5:
            pool_size = 5
            logger.warning(f"数据库连接池大小过小，已调整为 {pool_size}")

        if max_overflow < 5:
            max_overflow = 5
            logger.warning(f"数据库连接池最大溢出过小，已调整为 {max_overflow}")

        # 运行数据库修复脚本
        try:
            logger.info("运行数据库修复脚本...")
            from src.db.fix_intermediate_results import fix_intermediate_results_table
            fix_intermediate_results_table()
            logger.info("数据库修复脚本已运行完成")
        except Exception as e:
            logger.error(f"运行数据库修复脚本时出错: {str(e)}")

        # 尝试导入数据库优化模块
        try:
            # 首先尝试导入新的优化器模块
            try:
                from src.db.optimizer import pool_manager, schedule_maintenance

                # 获取数据库路径
                db_path = os.path.join(os.getcwd(), 'novels.db')

                # 初始化连接池管理器
                logger.info("使用增强的数据库优化器")

                # 导入引擎
                try:
                    from src.db.connection import engine

                    # 优化引擎
                    if pool_manager.optimize_pool(engine):
                        logger.info("已使用增强优化器配置连接池")

                    # 安排维护任务
                    schedule_maintenance(db_path, engine)
                except ImportError:
                    logger.warning("无法导入数据库引擎，跳过连接池优化")

                    # 安排维护任务
                    schedule_maintenance(db_path)
            except (ImportError, AttributeError):
                # 如果找不到新的优化器，尝试旧的优化函数
                logger.info("使用旧版数据库优化器")
                from src.db.optimizer import optimize_db_pool, limit_max_connections

                # 限制最大连接数
                limited = limit_max_connections()
                if limited:
                    logger.info("已限制数据库最大连接数")

                # 优化连接池
                optimized = optimize_db_pool()
                if optimized:
                    logger.info("已优化数据库连接池配置")
        except ImportError:
            logger.warning("无法导入数据库优化模块，跳过数据库优化")

        # 尝试直接优化SQLAlchemy连接池
        try:
            import sqlalchemy.pool

            # 修改默认值
            sqlalchemy.pool.QueuePool._pool_size = pool_size
            sqlalchemy.pool.QueuePool._max_overflow = max_overflow

            logger.info(f"已设置SQLAlchemy默认连接池大小: {pool_size}, 最大溢出: {max_overflow}")

            # 设置连接超时
            timeout = 60  # 60秒
            sqlalchemy.pool.QueuePool._timeout = timeout
            logger.info(f"已设置SQLAlchemy连接超时: {timeout}秒")

            # 设置回收时间
            recycle = 3600  # 1小时
            sqlalchemy.pool.QueuePool._recycle = recycle
            logger.info(f"已设置SQLAlchemy连接回收时间: {recycle}秒")
        except (ImportError, AttributeError) as e:
            logger.warning(f"直接优化SQLAlchemy连接池时出错: {str(e)}")
    except Exception as e:
        logger.error(f"优化数据库时出错: {str(e)}")

def _start_with_app():
    """使用src.web.app启动系统"""
    try:
        # 导入配置
        import config

        # 导入Flask应用
        from src.web.app import app

        # 初始化图表禁用功能
        try:
            from src.web.chart_disabler import initialize_chart_disabler
            initialize_chart_disabler(app)
            logger.info("图表禁用功能已初始化")
        except ImportError as e:
            logger.warning(f"无法初始化图表禁用功能: {str(e)}")

        # 启动应用
        logger.info(f"启动九猫小说分析系统，监听 {config.HOST}:{config.PORT}")
        app.run(
            host=config.HOST,
            port=config.PORT,
            debug=config.DEBUG,
            threaded=True,
            use_reloader=False  # 禁用reloader以避免启动两个进程
        )
    except Exception as e:
        logger.error(f"启动系统时出错: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
