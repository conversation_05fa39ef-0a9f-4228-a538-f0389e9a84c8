@echo off
echo 九猫小说分析系统 - 强制记录分析过程版
echo.
echo 正在启动系统，请稍候...
echo.

:: 设置环境变量
set DEBUG=False
set USE_REAL_API=True
set HOST=127.0.0.1
set MEMORY_OPTIMIZED=False
set LOW_MEMORY_MODE=False
set MEMORY_WARNING_THRESHOLD=85
set MEMORY_CRITICAL_THRESHOLD=95
set MAX_DB_CONNECTIONS=20
set DB_POOL_SIZE=10
set DB_MAX_OVERFLOW=10
set THREAD_POOL_SIZE=8
set MAX_WORKERS=8
set DISABLE_PARALLEL_ANALYSIS=False
set REDUCE_LOGGING=False

:: 强制启用分析过程记录
set ENABLE_DETAILED_PROCESS_RECORDING=True
set SAVE_FULL_API_INTERACTIONS=True
set SAVE_PROMPTS=True
set ENHANCED_LOG_CATEGORIES=True
set RECORD_INTERMEDIATE_RESULTS=True
set PROCESS_RECORDING_DEBUG=True
set PROCESS_RECORDING_VERBOSE=True
set FORCE_PROCESS_RECORDING=True
set NINECATS_FORCE_PROCESS_RECORDING=True

:: 设置Python路径
set PYTHONPATH=%PYTHONPATH%;%CD%

:: 创建日志目录
if not exist logs mkdir logs

echo 环境变量已设置，启动系统...
echo.
echo 请不要关闭此窗口，系统将在后台运行。
echo 浏览器将在几秒钟后自动打开。
echo.

:: 启动Python进程
start python run.py

:: 等待10秒钟确保服务启动
timeout /t 10 /nobreak > nul

:: 打开浏览器
start http://localhost:5001

echo 系统已启动，请在浏览器中使用。
echo 如需关闭系统，请关闭此窗口。
echo.
