{"name": "socks", "private": false, "version": "2.6.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "main": "build/index.js", "typings": "typings/index.d.ts", "homepage": "https://github.com/JoshGlazebrook/socks/", "repository": {"type": "git", "url": "https://github.com/JoshGlazebrook/socks.git"}, "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "engines": {"node": ">= 10.13.0", "npm": ">= 3.0.0"}, "author": "<PERSON>", "contributors": ["castorw"], "license": "MIT", "readmeFilename": "README.md", "devDependencies": {"@types/ip": "1.1.0", "@types/mocha": "^9.1.0", "@types/node": "^17.0.15", "coveralls": "^3.1.1", "mocha": "^9.2.0", "nyc": "15.1.0", "prettier": "^2.5.1", "socks5-server": "^0.1.1", "ts-node": "^10.4.0", "typescript": "^4.5.5"}, "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.2.0"}, "scripts": {"prepublish": "npm install -g typescript && npm run build", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "lint": "tslint --project tsconfig.json 'src/**/*.ts'", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p ."}, "nyc": {"extension": [".ts", ".tsx"], "include": ["src/*.ts", "src/**/*.ts"], "exclude": ["**.*.d.ts", "node_modules", "typings"], "require": ["ts-node/register"], "reporter": ["json", "html"], "all": true}}