"""
九猫系统自动修复模块
在应用启动时自动修复常见问题
"""
import os
import sys
import logging
import shutil
import time
import threading
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class AutoFixer:
    """自动修复器，用于修复常见问题"""

    def __init__(self):
        """初始化自动修复器"""
        self.fixes_applied = []
        self.fixes_failed = []

    def run_all_fixes(self) -> Tuple[List[str], List[str]]:
        """
        运行所有修复

        Returns:
            已应用的修复列表和失败的修复列表
        """
        logger.info("开始自动修复...")

        # 重置修复记录
        self.fixes_applied = []
        self.fixes_failed = []

        # 运行所有修复方法
        self._fix_static_directories()
        self._fix_external_storage()
        self._fix_database_connections()
        self._fix_memory_settings()
        self._fix_chart_issues()
        self._fix_dimension_detail_pages()

        # 记录修复结果
        if self.fixes_applied:
            logger.info(f"已应用 {len(self.fixes_applied)} 个修复: {', '.join(self.fixes_applied)}")
        else:
            logger.info("没有应用任何修复")

        if self.fixes_failed:
            logger.warning(f"有 {len(self.fixes_failed)} 个修复失败: {', '.join(self.fixes_failed)}")

        return self.fixes_applied, self.fixes_failed

    def _fix_static_directories(self) -> None:
        """修复静态文件目录"""
        try:
            # 获取静态文件目录
            static_folder = self._get_static_folder()
            if not static_folder:
                self.fixes_failed.append("静态文件目录修复")
                return

            # 创建目录结构
            directories = [
                os.path.join(static_folder, 'css'),
                os.path.join(static_folder, 'css', 'lib'),
                os.path.join(static_folder, 'js'),
                os.path.join(static_folder, 'js', 'lib'),
                os.path.join(static_folder, 'img'),
                os.path.join(static_folder, 'fonts')
            ]

            # 创建目录
            for directory in directories:
                os.makedirs(directory, exist_ok=True)
                logger.info(f"已创建目录: {directory}")

            # 创建备用静态目录
            direct_static_folder = os.path.join(os.path.dirname(static_folder), 'direct-static')

            # 创建备用目录结构
            direct_directories = [
                os.path.join(direct_static_folder, 'css'),
                os.path.join(direct_static_folder, 'css', 'lib'),
                os.path.join(direct_static_folder, 'js'),
                os.path.join(direct_static_folder, 'js', 'lib'),
                os.path.join(direct_static_folder, 'img'),
                os.path.join(direct_static_folder, 'fonts')
            ]

            # 创建备用目录
            for directory in direct_directories:
                os.makedirs(directory, exist_ok=True)
                logger.info(f"已创建备用目录: {directory}")

            # 复制修复脚本
            self._copy_fix_scripts(static_folder)

            self.fixes_applied.append("静态文件目录修复")
        except Exception as e:
            logger.error(f"修复静态文件目录时出错: {str(e)}")
            self.fixes_failed.append("静态文件目录修复")

    def _copy_fix_scripts(self, static_folder: str) -> None:
        """
        复制修复脚本到静态文件目录

        Args:
            static_folder: 静态文件目录
        """
        try:
            # 确保目标目录存在
            js_dir = os.path.join(static_folder, 'js')
            os.makedirs(js_dir, exist_ok=True)

            # 修复脚本列表
            fix_scripts = [
                'enhanced-chart-fix.js',
                'static-file-manager-enhanced.js',
                'dimension-detail-fix-enhanced.js'
            ]

            # 检查脚本是否存在
            for script in fix_scripts:
                script_path = os.path.join(js_dir, script)
                if not os.path.exists(script_path):
                    logger.warning(f"修复脚本不存在: {script_path}")

            logger.info("已检查修复脚本")
        except Exception as e:
            logger.error(f"复制修复脚本时出错: {str(e)}")

    def _fix_external_storage(self) -> None:
        """修复外部存储配置"""
        try:
            # 检查环境变量
            external_storage_path = os.environ.get('EXTERNAL_STORAGE_PATH')
            if not external_storage_path:
                logger.warning("未设置EXTERNAL_STORAGE_PATH环境变量")
                # 设置默认值
                external_storage_path = "E:\\艹，又来一次\\九猫\\external_storage"
                os.environ['EXTERNAL_STORAGE_PATH'] = external_storage_path
                logger.info(f"已设置EXTERNAL_STORAGE_PATH环境变量为: {external_storage_path}")

            # 创建外部存储目录
            directories = [
                external_storage_path,
                os.path.join(external_storage_path, 'logs'),
                os.path.join(external_storage_path, 'cache'),
                os.path.join(external_storage_path, 'temp'),
                os.path.join(external_storage_path, 'db_backup'),
                os.path.join(external_storage_path, 'memory_stats')
            ]

            # 创建目录
            for directory in directories:
                os.makedirs(directory, exist_ok=True)
                logger.info(f"已创建外部存储目录: {directory}")

            self.fixes_applied.append("外部存储配置修复")
        except Exception as e:
            logger.error(f"修复外部存储配置时出错: {str(e)}")
            self.fixes_failed.append("外部存储配置修复")

    def _fix_database_connections(self) -> None:
        """修复数据库连接配置"""
        try:
            # 尝试导入数据库优化模块
            try:
                from src.utils.db_optimizer import optimize_db_connection_pool, check_and_cleanup_db_connections
                
                # 优化数据库连接池
                db_optimized = optimize_db_connection_pool()
                if db_optimized:
                    logger.info("已优化数据库连接池配置")
                
                # 检查并清理数据库连接
                db_cleaned = check_and_cleanup_db_connections(force=False)
                if db_cleaned:
                    logger.info("已清理数据库连接")
                
                self.fixes_applied.append("数据库连接配置修复")
            except ImportError:
                logger.warning("无法导入数据库优化模块")
                self.fixes_failed.append("数据库连接配置修复")
        except Exception as e:
            logger.error(f"修复数据库连接配置时出错: {str(e)}")
            self.fixes_failed.append("数据库连接配置修复")

    def _fix_memory_settings(self) -> None:
        """修复内存设置"""
        try:
            # 设置内存监控参数
            os.environ['MEMORY_WARNING_THRESHOLD'] = '75'  # 降低警告阈值
            os.environ['MEMORY_CRITICAL_THRESHOLD'] = '85'  # 降低危险阈值
            os.environ['MEMORY_CHECK_INTERVAL'] = '3'  # 缩短检查间隔
            
            # 启用外部存储
            os.environ['USE_EXTERNAL_STORAGE'] = 'True'
            
            # 启用日志过滤
            os.environ['ENABLE_LOG_FILTER'] = 'True'
            
            # 启用并行分析
            os.environ['DISABLE_PARALLEL_ANALYSIS'] = 'False'
            
            # 设置线程池大小
            os.environ['THREAD_POOL_SIZE'] = '4'  # 适中的线程池大小
            
            # 设置数据库连接池大小
            os.environ['DB_POOL_SIZE'] = '30'  # 适中的连接池大小
            
            logger.info("已优化内存设置")
            self.fixes_applied.append("内存设置修复")
        except Exception as e:
            logger.error(f"修复内存设置时出错: {str(e)}")
            self.fixes_failed.append("内存设置修复")

    def _fix_chart_issues(self) -> None:
        """修复Chart.js相关问题"""
        try:
            # 设置Chart.js优化参数
            os.environ['OPTIMIZE_CHART_RENDERING'] = 'True'
            
            # 检查修复脚本是否存在
            static_folder = self._get_static_folder()
            if not static_folder:
                self.fixes_failed.append("Chart.js问题修复")
                return
                
            chart_fix_script = os.path.join(static_folder, 'js', 'enhanced-chart-fix.js')
            if not os.path.exists(chart_fix_script):
                logger.warning(f"Chart.js修复脚本不存在: {chart_fix_script}")
                self.fixes_failed.append("Chart.js问题修复")
                return
                
            logger.info("已应用Chart.js修复")
            self.fixes_applied.append("Chart.js问题修复")
        except Exception as e:
            logger.error(f"修复Chart.js相关问题时出错: {str(e)}")
            self.fixes_failed.append("Chart.js问题修复")

    def _fix_dimension_detail_pages(self) -> None:
        """修复维度详情页面问题"""
        try:
            # 设置维度详情页面优化参数
            os.environ['OPTIMIZE_DIMENSION_DETAIL'] = 'True'
            
            # 检查修复脚本是否存在
            static_folder = self._get_static_folder()
            if not static_folder:
                self.fixes_failed.append("维度详情页面修复")
                return
                
            dimension_fix_script = os.path.join(static_folder, 'js', 'dimension-detail-fix-enhanced.js')
            if not os.path.exists(dimension_fix_script):
                logger.warning(f"维度详情页面修复脚本不存在: {dimension_fix_script}")
                self.fixes_failed.append("维度详情页面修复")
                return
                
            logger.info("已应用维度详情页面修复")
            self.fixes_applied.append("维度详情页面修复")
        except Exception as e:
            logger.error(f"修复维度详情页面问题时出错: {str(e)}")
            self.fixes_failed.append("维度详情页面修复")

    def _get_static_folder(self) -> Optional[str]:
        """
        获取静态文件目录

        Returns:
            静态文件目录路径，如果无法确定则返回None
        """
        try:
            # 尝试从Flask应用获取
            try:
                from flask import current_app
                if current_app and hasattr(current_app, 'static_folder'):
                    return current_app.static_folder
            except (ImportError, RuntimeError):
                pass
            
            # 尝试从项目结构推断
            base_dir = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            static_folder = os.path.join(base_dir, 'src', 'web', 'static')
            if os.path.isdir(static_folder):
                return static_folder
                
            # 尝试从当前目录推断
            current_dir = os.path.abspath(os.path.curdir)
            static_folder = os.path.join(current_dir, 'src', 'web', 'static')
            if os.path.isdir(static_folder):
                return static_folder
                
            # 尝试从环境变量获取
            if 'STATIC_FOLDER' in os.environ:
                return os.environ['STATIC_FOLDER']
                
            logger.warning("无法确定静态文件目录")
            return None
        except Exception as e:
            logger.error(f"获取静态文件目录时出错: {str(e)}")
            return None

# 创建全局自动修复器实例
auto_fixer = AutoFixer()

def run_auto_fixes() -> Tuple[List[str], List[str]]:
    """
    运行自动修复

    Returns:
        已应用的修复列表和失败的修复列表
    """
    global auto_fixer
    return auto_fixer.run_all_fixes()

def run_auto_fixes_async() -> threading.Thread:
    """
    异步运行自动修复

    Returns:
        修复线程
    """
    thread = threading.Thread(
        target=run_auto_fixes,
        daemon=True,
        name="AutoFixerThread"
    )
    thread.start()
    return thread
