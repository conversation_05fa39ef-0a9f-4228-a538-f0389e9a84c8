"""
九猫系统并行分析优化配置
整合所有降本增效策略，包括单实例并发度、冷启动、异步调用、连接池等优化方案
"""

import os
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class ParallelOptimizationConfig:
    """并行分析优化配置管理器"""

    @staticmethod
    def get_optimization_config(prompt_template: str = "default") -> Dict[str, Any]:
        """
        获取优化配置

        Args:
            prompt_template: 提示词模板类型 (default/simplified)

        Returns:
            优化配置字典
        """
        base_config = {
            # 基础并行配置
            "enable_parallel": True,
            "enable_streaming": True,
            "enable_connection_pooling": True,
            "enable_async_processing": True,

            # 错误处理和重试
            "max_retries": 3,
            "retry_delay": 1.0,
            "timeout_seconds": 300,

            # 监控和日志
            "enable_performance_monitoring": True,
            "log_level": "INFO",
            "progress_update_interval": 10
        }

        if prompt_template == "simplified":
            # 精简版：效率与成本平衡配置（温和优化）
            optimization_config = {
                **base_config,

                # 维度级并行配置（成本优先）
                "max_dimension_workers": 6,  # 温和提升，严格控制成本
                "dimension_batch_size": 2,   # 减小批处理大小以控制成本

                # 块级并行配置（温和提升）
                "max_chunk_workers": 6,      # 温和的块级并行度，避免成本激增
                "chunk_batch_size": 3,       # 减小块批处理大小
                "api_delay": 0.4,           # 稍长的API调用延迟，降低并发压力

                # 单实例并发度优化（降本增效核心）
                "instance_concurrency": 8,   # 单实例并发度8（I/O密集型推荐5-10）
                "connection_pool_size": 15,  # 连接池大小
                "connection_reuse": True,    # 启用连接复用
                "keep_alive_timeout": 30,    # 连接保活时间

                # 冷启动优化
                "warmup_enabled": True,      # 启用预热
                "warmup_requests": 2,        # 预热请求数
                "warmup_timeout": 10,        # 预热超时时间

                # 异步调用优化
                "async_batch_size": 5,       # 异步批处理大小
                "async_queue_size": 20,      # 异步队列大小
                "async_worker_threads": 5,   # 异步工作线程数

                # 流式输出配置
                "stream_threshold": 30000,   # 流式输出阈值（字符数）
                "stream_chunk_size": 1024,   # 流式数据块大小
                "stream_timeout": 60,        # 流式超时时间

                # 资源管理
                "memory_limit_mb": 2048,     # 内存限制
                "cpu_limit_percent": 70,     # CPU使用限制
                "disk_cache_size_mb": 512,   # 磁盘缓存大小

                # 成本控制
                "cost_control_enabled": True,
                "max_cost_per_analysis": 5.0,  # 单次分析最大成本（元）
                "cost_alert_threshold": 3.0,   # 成本告警阈值

                # 质量保证
                "quality_check_enabled": True,
                "min_content_length": 100,      # 最小内容长度
                "max_error_rate": 0.1,         # 最大错误率

                "optimization_level": "balanced"  # 优化级别：平衡
            }

            logger.info("[精简版配置] 使用效率与成本平衡的并行优化配置")

        else:
            # 默认版：性能优先配置
            optimization_config = {
                **base_config,

                # 维度级并行配置
                "max_dimension_workers": 12,  # 更高的维度并发数
                "dimension_batch_size": 5,    # 更大的维度批处理大小

                # 块级并行配置
                "max_chunk_workers": 12,      # 更高的块级并行度
                "chunk_batch_size": 6,        # 更大的块批处理大小
                "api_delay": 0.2,            # 更短的API调用延迟

                # 单实例并发度优化
                "instance_concurrency": 10,  # 单实例并发度10
                "connection_pool_size": 30,  # 更大的连接池
                "connection_reuse": True,
                "keep_alive_timeout": 60,    # 更长的连接保活时间

                # 冷启动优化
                "warmup_enabled": True,
                "warmup_requests": 3,        # 更多预热请求
                "warmup_timeout": 15,        # 更长的预热超时

                # 异步调用优化
                "async_batch_size": 8,       # 更大的异步批处理
                "async_queue_size": 40,      # 更大的异步队列
                "async_worker_threads": 12,  # 更多异步工作线程

                # 流式输出配置
                "stream_threshold": 20000,   # 更低的流式输出阈值
                "stream_chunk_size": 2048,   # 更大的流式数据块
                "stream_timeout": 120,       # 更长的流式超时

                # 资源管理
                "memory_limit_mb": 4096,     # 更大的内存限制
                "cpu_limit_percent": 85,     # 更高的CPU使用限制
                "disk_cache_size_mb": 1024,  # 更大的磁盘缓存

                # 成本控制
                "cost_control_enabled": False,  # 默认版不限制成本
                "max_cost_per_analysis": 20.0,
                "cost_alert_threshold": 15.0,

                # 质量保证
                "quality_check_enabled": True,
                "min_content_length": 50,
                "max_error_rate": 0.05,      # 更低的错误率要求

                "optimization_level": "performance"  # 优化级别：性能优先
            }

            logger.info("[默认版配置] 使用性能优先的并行优化配置")

        return optimization_config

    @staticmethod
    def get_dimension_specific_config(dimension: str, prompt_template: str = "default") -> Dict[str, Any]:
        """
        获取特定维度的优化配置

        Args:
            dimension: 分析维度
            prompt_template: 提示词模板类型

        Returns:
            维度特定的优化配置
        """
        base_config = ParallelOptimizationConfig.get_optimization_config(prompt_template)

        # 高消耗维度特殊配置
        high_cost_dimensions = ["chapter_outline", "outline_analysis", "character_relationships"]

        if dimension in high_cost_dimensions:
            # 高消耗维度需要特殊优化
            if prompt_template == "simplified":
                base_config.update({
                    "max_chunk_workers": 6,      # 降低并行度
                    "api_delay": 0.5,           # 增加延迟
                    "instance_concurrency": 6,   # 降低并发度
                    "stream_threshold": 20000,   # 降低流式阈值
                    "cost_multiplier": 1.5       # 成本倍数
                })
            else:
                base_config.update({
                    "max_chunk_workers": 10,     # 适度降低并行度
                    "api_delay": 0.3,           # 适度增加延迟
                    "instance_concurrency": 8,   # 适度降低并发度
                    "stream_threshold": 15000,   # 降低流式阈值
                    "cost_multiplier": 1.2       # 成本倍数
                })

            logger.info(f"[维度优化] {dimension} 使用高消耗维度特殊配置")

        # 轻量级维度优化配置
        lightweight_dimensions = ["sentence_variation", "paragraph_length", "perspective_shifts"]

        if dimension in lightweight_dimensions:
            base_config.update({
                "max_chunk_workers": min(base_config["max_chunk_workers"] + 2, 15),  # 增加并行度
                "api_delay": max(base_config["api_delay"] - 0.1, 0.1),             # 减少延迟
                "stream_threshold": base_config["stream_threshold"] + 10000,        # 提高流式阈值
                "cost_multiplier": 0.8                                             # 成本倍数
            })

            logger.info(f"[维度优化] {dimension} 使用轻量级维度优化配置")

        return base_config

    @staticmethod
    def apply_runtime_optimization(config: Dict[str, Any], runtime_stats: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据运行时统计信息动态调整配置

        Args:
            config: 当前配置
            runtime_stats: 运行时统计信息

        Returns:
            调整后的配置
        """
        try:
            # 根据错误率调整
            error_rate = runtime_stats.get("error_rate", 0)
            if error_rate > config.get("max_error_rate", 0.1):
                # 错误率过高，降低并行度
                config["max_chunk_workers"] = max(config["max_chunk_workers"] - 2, 2)
                config["api_delay"] += 0.2
                logger.warning(f"[动态优化] 错误率过高({error_rate:.2%})，降低并行度至{config['max_chunk_workers']}")

            # 根据响应时间调整
            avg_response_time = runtime_stats.get("avg_response_time", 0)
            if avg_response_time > 30:  # 响应时间超过30秒
                # 响应时间过长，启用流式输出
                config["stream_threshold"] = min(config["stream_threshold"], 15000)
                logger.warning(f"[动态优化] 响应时间过长({avg_response_time:.1f}s)，降低流式阈值至{config['stream_threshold']}")

            # 根据成本调整
            if config.get("cost_control_enabled", False):
                current_cost = runtime_stats.get("current_cost", 0)
                max_cost = config.get("max_cost_per_analysis", 5.0)
                if current_cost > max_cost * 0.8:  # 成本接近上限
                    # 启用更激进的成本控制
                    config["max_chunk_workers"] = max(config["max_chunk_workers"] - 1, 3)
                    config["instance_concurrency"] = max(config["instance_concurrency"] - 1, 5)
                    logger.warning(f"[动态优化] 成本接近上限({current_cost:.2f}/{max_cost:.2f})，降低并发度")

        except Exception as e:
            logger.error(f"[动态优化] 应用运行时优化时出错: {str(e)}")

        return config

    @staticmethod
    def get_connection_pool_config(prompt_template: str = "default") -> Dict[str, Any]:
        """
        获取连接池配置

        Args:
            prompt_template: 提示词模板类型

        Returns:
            连接池配置
        """
        if prompt_template == "simplified":
            return {
                "pool_size": 15,
                "max_overflow": 10,
                "pool_timeout": 30,
                "pool_recycle": 3600,
                "pool_pre_ping": True,
                "echo": False
            }
        else:
            return {
                "pool_size": 30,
                "max_overflow": 20,
                "pool_timeout": 60,
                "pool_recycle": 7200,
                "pool_pre_ping": True,
                "echo": False
            }
