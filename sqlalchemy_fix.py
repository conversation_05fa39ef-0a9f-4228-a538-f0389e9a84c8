"""
九猫 - 使用SQLAlchemy修复数据库脚本
修复novel/4页面的character_relationships分析结果
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_database():
    """使用SQLAlchemy修复数据库中的character_relationships分析结果"""
    logger.info("开始使用SQLAlchemy修复数据库")
    
    try:
        # 导入数据库模型和会话
        from src.models.analysis_result import AnalysisResult
        from src.db.connection import Session
        
        # 创建会话
        session = Session()
        
        try:
            # 查找novel/4页面的character_relationships分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=4,
                dimension='character_relationships'
            ).first()
            
            if result:
                logger.info(f"找到分析结果ID: {result.id}")
                
                # 删除现有结果
                session.delete(result)
                session.flush()
                logger.info("已删除现有分析结果")
            
            # 创建有效的内容
            valid_content = """# 分析过程中出错

## 错误详情
分析维度 **character_relationships** 时遇到了问题。

## 错误信息
```
name 'stats_start' is not defined
```

## 建议操作
请尝试以下解决方法：
1. 刷新页面并重新尝试分析
2. 检查小说文本是否过长或包含特殊字符
3. 确认API连接正常

如果问题持续存在，请联系系统管理员。
"""
            
            # 创建有效的元数据
            valid_metadata = {
                "processing_time": 0,
                "chunk_count": 0,
                "api_calls": 0,
                "tokens_used": 0,
                "cost": 0,
                "fixed_by_script": True,
                "fix_timestamp": datetime.now().isoformat()
            }
            
            # 创建新的分析结果
            new_result = AnalysisResult(
                novel_id=4,
                dimension='character_relationships',
                content=valid_content,
                metadata=valid_metadata
            )
            
            # 添加到会话
            session.add(new_result)
            
            # 提交更改
            session.commit()
            logger.info(f"成功创建新的分析结果，ID: {new_result.id}")
            
            return True
        except Exception as e:
            logger.error(f"修复分析结果时出错: {str(e)}")
            session.rollback()
            return False
        finally:
            session.close()
    except Exception as e:
        logger.error(f"导入模块时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始运行SQLAlchemy修复脚本")
    
    # 修复数据库
    success = fix_database()
    
    if success:
        logger.info("数据库修复成功")
        logger.info("请重启服务器以应用更改")
    else:
        logger.error("数据库修复失败")

if __name__ == "__main__":
    main()
