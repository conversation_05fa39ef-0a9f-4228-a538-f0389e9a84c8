"""
强制启用分析过程记录功能
"""
import os
import sys
import logging
import importlib
import types

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def patch_process_recorder():
    """修补ProcessRecorder类的is_process_recording_enabled方法"""
    try:
        # 导入ProcessRecorder类
        from src.services.process_recording_patch import ProcessRecorder
        
        # 保存原始方法
        original_method = ProcessRecorder.is_process_recording_enabled
        
        # 定义新方法
        def patched_method():
            """始终返回True的方法"""
            logger.info("使用修补后的is_process_recording_enabled方法，强制返回True")
            return True
        
        # 替换方法
        ProcessRecorder.is_process_recording_enabled = staticmethod(patched_method)
        
        logger.info("成功修补ProcessRecorder.is_process_recording_enabled方法")
        return True
    except ImportError:
        logger.error("无法导入ProcessRecorder类，修补失败")
        return False
    except Exception as e:
        logger.error(f"修补ProcessRecorder类时出错: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始强制启用分析过程记录功能")
    
    # 修补ProcessRecorder类
    patch_ok = patch_process_recorder()
    if patch_ok:
        logger.info("修补成功，分析过程记录功能已强制启用")
    else:
        logger.error("修补失败，无法强制启用分析过程记录功能")
    
    logger.info("修补完成")

if __name__ == "__main__":
    main()
