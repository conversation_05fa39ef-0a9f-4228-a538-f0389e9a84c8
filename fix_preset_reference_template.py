#!/usr/bin/env python
"""
修复预设内容与参考蓝本的关联关系
"""
import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的类和函数
from src.models.preset import Preset
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.db.connection import Session

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'fix_preset_reference_template_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

def fix_preset_template_references():
    """修复预设内容与参考蓝本的关联关系"""
    session = Session()
    try:
        # 获取所有知识库类型的预设内容
        presets = session.query(Preset).filter_by(category='knowledge_base').all()
        
        if not presets:
            logger.warning("未找到知识库类型的预设内容")
            return
            
        logger.info(f"找到 {len(presets)} 个知识库类型的预设内容")
        
        # 检查每个预设内容的参考蓝本关联
        for preset in presets:
            logger.info(f"处理预设内容 ID: {preset.id}, 标题: {preset.title}")
            
            # 检查meta_info是否存在
            if not preset.meta_info:
                preset.meta_info = {}
                logger.warning(f"预设内容 ID: {preset.id} 没有meta_info，已创建空对象")
            
            # 获取参考蓝本ID
            reference_template_id = None
            if isinstance(preset.meta_info, dict) and 'reference_template_id' in preset.meta_info:
                reference_template_id = preset.meta_info['reference_template_id']
                logger.info(f"从meta_info中获取到参考蓝本ID: {reference_template_id}")
            else:
                # 尝试从内容中解析参考蓝本ID
                if "参考蓝本ID:" in preset.content:
                    import re
                    match = re.search(r"参考蓝本ID:\s*(\d+)", preset.content)
                    if match:
                        reference_template_id = int(match.group(1))
                        logger.info(f"从内容中解析到参考蓝本ID: {reference_template_id}")
                        # 更新meta_info
                        preset.meta_info['reference_template_id'] = reference_template_id
                        logger.info(f"已更新预设内容 ID: {preset.id} 的meta_info")
            
            # 如果没有找到参考蓝本ID，列出可用的参考蓝本
            if not reference_template_id:
                # 获取所有小说，优先选择已设为模板的小说
                novels = session.query(Novel).all()
                template_novels = []
                
                for novel in novels:
                    # 检查小说是否已设为模板
                    is_template = False
                    if novel.novel_metadata and isinstance(novel.novel_metadata, dict) and novel.novel_metadata.get('is_template'):
                        is_template = True
                    
                    # 获取章节数
                    chapter_count = session.query(Chapter).filter_by(novel_id=novel.id).count()
                    
                    if is_template and chapter_count > 0:
                        template_novels.append({
                            'id': novel.id,
                            'title': novel.title,
                            'chapter_count': chapter_count,
                            'is_template': True
                        })
                
                if template_novels:
                    # 使用第一个找到的模板小说
                    chosen_novel = template_novels[0]
                    reference_template_id = chosen_novel['id']
                    preset.meta_info['reference_template_id'] = reference_template_id
                    logger.info(f"自动选择参考蓝本 ID: {reference_template_id}, 标题: {chosen_novel['title']}")
                else:
                    # 如果没有模板小说，尝试使用有章节的普通小说
                    normal_novels = []
                    for novel in novels:
                        chapter_count = session.query(Chapter).filter_by(novel_id=novel.id).count()
                        if chapter_count > 0:
                            normal_novels.append({
                                'id': novel.id,
                                'title': novel.title,
                                'chapter_count': chapter_count,
                                'is_template': False
                            })
                    
                    if normal_novels:
                        # 使用第一个找到的有章节的小说
                        chosen_novel = normal_novels[0]
                        reference_template_id = chosen_novel['id']
                        preset.meta_info['reference_template_id'] = reference_template_id
                        logger.info(f"自动选择参考蓝本 ID: {reference_template_id}, 标题: {chosen_novel['title']} (非模板)")
                    else:
                        logger.error(f"未找到合适的参考蓝本，预设内容 ID: {preset.id} 无法关联参考蓝本")
            
            # 如果找到了参考蓝本ID，检查该参考蓝本是否存在以及是否有章节
            if reference_template_id:
                novel = session.query(Novel).get(reference_template_id)
                if not novel:
                    logger.error(f"参考蓝本 ID: {reference_template_id} 不存在")
                    continue
                
                # 检查章节
                chapters = session.query(Chapter).filter_by(novel_id=reference_template_id).all()
                if not chapters:
                    logger.warning(f"参考蓝本 ID: {reference_template_id} 没有章节")
                    
                    # 尝试自动分割章节
                    try:
                        from src.services.chapter_analysis_service import ChapterAnalysisService
                        logger.info(f"尝试为参考蓝本 ID: {reference_template_id} 自动分割章节")
                        
                        # 确保新建会话处理章节分割
                        session.close()
                        new_session = Session()
                        new_chapters = ChapterAnalysisService.split_novel_into_chapters(reference_template_id)
                        new_session.commit()
                        new_session.close()
                        
                        # 重新获取会话和章节
                        session = Session()
                        chapters = session.query(Chapter).filter_by(novel_id=reference_template_id).all()
                        logger.info(f"成功为参考蓝本 ID: {reference_template_id} 自动分割了 {len(chapters)} 个章节")
                    except Exception as e:
                        logger.error(f"自动分割章节失败: {str(e)}")
                else:
                    logger.info(f"参考蓝本 ID: {reference_template_id} 有 {len(chapters)} 个章节")
                
                # 将该小说设为模板
                if novel.novel_metadata and isinstance(novel.novel_metadata, dict):
                    novel.novel_metadata['is_template'] = True
                else:
                    novel.novel_metadata = {'is_template': True}
                logger.info(f"已将参考蓝本 ID: {reference_template_id} 设为模板")
        
        # 提交更改
        session.commit()
        logger.info("已完成预设内容与参考蓝本关联关系的修复")
    except Exception as e:
        logger.error(f"修复过程中出错: {str(e)}", exc_info=True)
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    logger.info("开始修复预设内容与参考蓝本的关联关系")
    fix_preset_template_references()
    logger.info("修复完成") 