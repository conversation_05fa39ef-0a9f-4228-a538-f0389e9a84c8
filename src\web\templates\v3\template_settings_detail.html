{% extends "v3/base.html" %}

{% block title %}设定模板详情 - 九猫{% endblock %}

{% block styles %}
<style>
    /* 主要配色方案：浅米黄色和淡白色 */
    :root {
        --light-yellow: #FFF8E1;
        --pale-white: #FFFCF5;
        --accent-color: #FFB74D;
        --text-color: #5D4037;
        --border-color: #E6D7B9;
        --hover-color: #FFF3CD;
    }

    body {
        background-color: var(--pale-white);
        color: var(--text-color);
    }

    .card {
        background-color: #FFFFFF;
        border-color: var(--border-color);
    }

    .card-header {
        background-color: var(--light-yellow);
        border-bottom: 1px solid var(--border-color);
    }

    .nav-tabs .nav-link {
        color: var(--text-color);
        border: none;
        font-weight: 500;
        padding: 0.75rem 1rem;
    }

    .nav-tabs .nav-link.active {
        color: #FF8F00;
        border-bottom: 3px solid #FF8F00;
        background-color: transparent;
    }

    .dimension-item {
        cursor: pointer;
        padding: 0.75rem 1rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
    }

    .dimension-item:hover {
        background-color: var(--hover-color);
    }

    .dimension-item.active {
        background-color: var(--light-yellow);
        font-weight: bold;
        border-left: 3px solid var(--accent-color);
    }

    .accordion-button {
        padding: 0.75rem 1rem;
        font-weight: 500;
        background-color: var(--pale-white);
    }

    .accordion-button:not(.collapsed) {
        background-color: var(--light-yellow);
        color: #FF8F00;
    }

    .accordion-body {
        padding: 0;
        background-color: #FFFFFF;
    }

    .chapter-dimension-item {
        cursor: pointer;
        padding: 0.5rem 1rem 0.5rem 2rem;
        transition: all 0.2s ease;
        border-left: 0;
        border-right: 0;
        border-radius: 0;
    }

    .chapter-dimension-item:hover {
        background-color: var(--hover-color);
    }

    .chapter-dimension-item.active {
        background-color: var(--light-yellow);
        font-weight: bold;
        color: #FF8F00;
        border-left: 3px solid var(--accent-color);
    }

    .template-content {
        white-space: pre-wrap;
        background-color: #FFFFFF;
        padding: 20px;
        border-radius: 5px;
        border: 1px solid var(--border-color);
        min-height: 400px;
        max-height: 70vh;
        overflow-y: auto;
    }

    .markdown-body h1 {
        font-size: 1.8rem;
        margin-top: 1.5rem;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
        color: #FF8F00;
    }

    .markdown-body h2 {
        font-size: 1.5rem;
        margin-top: 1.2rem;
        margin-bottom: 0.8rem;
        color: #FF8F00;
    }

    .markdown-body h3 {
        font-size: 1.3rem;
        margin-top: 1rem;
        margin-bottom: 0.6rem;
    }

    .btn-primary {
        background-color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-primary:hover {
        background-color: #F57C00;
        border-color: #F57C00;
    }

    .btn-outline-primary {
        color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-outline-primary:hover {
        background-color: #FF8F00;
        border-color: #FF8F00;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="mb-0">{{ novel.title }} - 设定模板详情</h1>
                    <div>
                        <a href="{{ url_for('v3.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回小说详情
                        </a>
                        <a href="{{ url_for('v3.console') }}" class="btn btn-outline-primary ms-2">
                            <i class="fas fa-terminal me-1"></i>控制台
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <p class="lead">设定模板详情页面展示了整本书和各章节的15个维度设定模板，您可以根据需要查看和应用这些模板。</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>提示：</strong> 请从左侧选择要查看的维度或章节，右侧将显示对应的设定模板内容。
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <ul class="nav nav-tabs card-header-tabs" id="templateTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="book-tab" data-bs-toggle="tab" data-bs-target="#book" type="button" role="tab" aria-controls="book" aria-selected="true">
                                <i class="fas fa-book me-1"></i>整本书设定模板
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">
                                <i class="fas fa-list-ol me-1"></i>章节设定模板
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body p-0">
                    <div class="tab-content" id="templateTabContent">
                        <!-- 整本书标签页 -->
                        <div class="tab-pane fade show active" id="book" role="tabpanel" aria-labelledby="book-tab">
                            <div class="row g-0">
                                <div class="col-md-3 p-3 border-end">
                                    <h5 class="mb-3">维度列表</h5>
                                    <div class="list-group">
                                        {% for dimension in dimensions %}
                                        <a href="javascript:void(0)" class="list-group-item list-group-item-action dimension-item" data-dimension="{{ dimension.key }}">
                                            <i class="{{ dimension.icon }} me-2"></i>{{ dimension.name }}
                                        </a>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="col-md-9 p-3">
                                    <div id="bookTemplateContent" class="template-content markdown-body">
                                        <div class="text-center py-5">
                                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                            <h4>请选择维度</h4>
                                            <p class="text-muted">从左侧选择一个维度，查看对应的设定模板内容。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 章节标签页 -->
                        <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                            <div class="row g-0">
                                <div class="col-md-3 p-3 border-end">
                                    <h5 class="mb-3">章节列表</h5>
                                    <div class="accordion" id="chaptersAccordion">
                                        {% if chapters and chapters|length > 0 %}
                                        {% for chapter in chapters %}
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="chapter-heading-{{ chapter.id }}">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter-collapse-{{ chapter.id }}" aria-expanded="false" aria-controls="chapter-collapse-{{ chapter.id }}">
                                                        第{{ chapter.chapter_number }}章: {{ chapter.title }}
                                                    </button>
                                                </h2>
                                                <div id="chapter-collapse-{{ chapter.id }}" class="accordion-collapse collapse" aria-labelledby="chapter-heading-{{ chapter.id }}" data-bs-parent="#chaptersAccordion">
                                                    <div class="accordion-body p-0">
                                                        <div class="list-group list-group-flush">
                                                        {% for dimension in dimensions %}
                                                            <a href="javascript:void(0)" class="list-group-item list-group-item-action chapter-dimension-item"
                                                               data-chapter-id="{{ chapter.id }}" data-dimension="{{ dimension.key }}">
                                                                <i class="{{ dimension.icon }} me-2"></i>{{ dimension.name }}
                                                            </a>
                                                        {% endfor %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                        {% else %}
                                            <div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                <strong>未找到章节列表</strong>，请确保：
                                                <ol>
                                                    <li>小说已正确设置</li>
                                                    <li>小说包含章节</li>
                                                </ol>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-9 p-3">
                                    <div id="chapterTemplateContent" class="template-content markdown-body">
                                        <div class="text-center py-5">
                                            <i class="fas fa-list-ol fa-3x text-muted mb-3"></i>
                                            <h4>请选择章节和维度</h4>
                                            <p class="text-muted">从左侧先选择一个章节，然后选择一个维度，查看对应的设定模板内容。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
    $(document).ready(function() {
        // 定义变量
        var selectedChapterId = null;
        var selectedDimension = null;
        var bookTemplateContent = {};
        var chapterTemplateContent = {};
        var novelId = "{{ novel.id }}";
        var novelTitle = "{{ novel.title }}";

        // 选择整本书维度
        $('.dimension-item').click(function() {
            $('.dimension-item').removeClass('active');
            $(this).addClass('active');

            var dimension = $(this).data('dimension');
            selectedDimension = dimension;

            // 显示加载中
            $('#bookTemplateContent').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中，请稍候...</p></div>');

            // 获取设定模板内容
            // 添加调试信息
            console.log("正在请求设定模板: " + dimension);

            // 尝试使用不同的API路径
            var apiUrl = "/api/novel/" + novelId + "/template/" + dimension;
            console.log("请求URL: " + apiUrl);

            $.ajax({
                url: apiUrl,
                type: "GET",
                success: function(response) {
                    console.log("API响应: ", response);
                    if (response.success) {
                        // 处理成功响应
                        handleBookTemplateResponse(response, dimension);
                    } else {
                        // 显示错误信息
                        $('#bookTemplateContent').html('<div class="alert alert-warning">获取设定模板失败: ' + response.error + '</div>');

                        // 尝试备用API路径
                        tryFallbackApi(dimension);
                    }
                },
                error: function(xhr, status, error) {
                    // 显示错误信息
                    console.log("API请求失败: ", status, error);
                    console.log("状态码: ", xhr.status);
                    console.log("响应文本: ", xhr.responseText);

                    $('#bookTemplateContent').html('<div class="alert alert-warning">获取设定模板失败，请稍后再试</div>');

                    // 尝试备用API路径
                    tryFallbackApi(dimension);
                }
            });
        });

        // 章节维度点击事件
        $('.chapter-dimension-item').click(function() {
            $('.chapter-dimension-item').removeClass('active');
            $(this).addClass('active');

            var chapterId = $(this).data('chapter-id');
            var dimension = $(this).data('dimension');
            selectedChapterId = chapterId;

            // 显示加载中
            $('#chapterTemplateContent').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中，请稍候...</p></div>');

            // 获取章节设定模板内容
            // 添加调试信息
            console.log("正在请求章节设定模板: 章节ID=" + chapterId + ", 维度=" + dimension);

            // 尝试使用不同的API路径
            var apiUrl = "/api/novel/" + novelId + "/chapter/" + chapterId + "/template/" + dimension;
            console.log("请求URL: " + apiUrl);

            $.ajax({
                url: apiUrl,
                type: "GET",
                success: function(response) {
                    console.log("章节API响应: ", response);
                    if (response.success) {
                        // 处理成功响应
                        handleChapterTemplateResponse(response, chapterId, dimension);
                    } else {
                        // 显示错误信息
                        $('#chapterTemplateContent').html('<div class="alert alert-warning">获取章节设定模板失败: ' + response.error + '</div>');

                        // 尝试备用API路径
                        tryFallbackChapterApi(chapterId, dimension);
                    }
                },
                error: function(xhr, status, error) {
                    // 显示错误信息
                    console.log("章节API请求失败: ", status, error);
                    console.log("状态码: ", xhr.status);
                    console.log("响应文本: ", xhr.responseText);

                    $('#chapterTemplateContent').html('<div class="alert alert-warning">获取章节设定模板失败，请稍后再试</div>');

                    // 尝试备用API路径
                    tryFallbackChapterApi(chapterId, dimension);
                }
            });
        });

        // 处理整本书模板响应的函数
        function handleBookTemplateResponse(response, dimension) {
            if (response.template) {
                bookTemplateContent[dimension] = response.template;

                // 使用marked.js渲染Markdown内容
                var renderedContent = marked.parse(response.template);

                // 添加导出按钮和标题
                var dimensionName = $('.dimension-item[data-dimension="' + dimension + '"]').text().trim();
                var headerHtml =
                    '<div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">' +
                        '<h3>' + dimensionName + ' 设定模板</h3>' +
                        '<button class="btn btn-sm btn-outline-primary export-template-btn" data-dimension="' + dimension + '">' +
                            '<i class="fas fa-download me-1"></i>导出模板' +
                        '</button>' +
                    '</div>';

                $('#bookTemplateContent').html(headerHtml + renderedContent);
            } else {
                // 生成默认模板内容
                var dimensionName = $('.dimension-item[data-dimension="' + dimension + '"]').text().trim();
                var defaultTemplate = generateDefaultBookTemplate(dimension, dimensionName);
                bookTemplateContent[dimension] = defaultTemplate;

                // 使用marked.js渲染Markdown内容
                var renderedContent = marked.parse(defaultTemplate);

                // 添加导出按钮和标题
                var headerHtml =
                    '<div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">' +
                        '<h3>' + dimensionName + ' 设定模板</h3>' +
                        '<button class="btn btn-sm btn-outline-primary export-template-btn" data-dimension="' + dimension + '">' +
                            '<i class="fas fa-download me-1"></i>导出模板' +
                        '</button>' +
                    '</div>';

                $('#bookTemplateContent').html(headerHtml + renderedContent);
            }
        }

        // 处理章节模板响应的函数
        function handleChapterTemplateResponse(response, chapterId, dimension) {
            if (!chapterTemplateContent[chapterId]) {
                chapterTemplateContent[chapterId] = {};
            }

            if (response.template) {
                chapterTemplateContent[chapterId][dimension] = response.template;

                // 使用marked.js渲染Markdown内容
                var renderedContent = marked.parse(response.template);

                // 获取章节标题和维度名称
                var chapterTitle = $('#chapter-heading-' + chapterId + ' .accordion-button').text().trim();
                var dimensionName = $('.chapter-dimension-item[data-chapter-id="' + chapterId + '"][data-dimension="' + dimension + '"]').text().trim();

                // 添加导出按钮和标题
                var headerHtml =
                    '<div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">' +
                        '<h3>' + chapterTitle + ' - ' + dimensionName + ' 设定模板</h3>' +
                        '<button class="btn btn-sm btn-outline-primary export-chapter-btn" data-chapter-id="' + chapterId + '" data-dimension="' + dimension + '">' +
                            '<i class="fas fa-download me-1"></i>导出模板' +
                        '</button>' +
                    '</div>';

                $('#chapterTemplateContent').html(headerHtml + renderedContent);
            } else {
                // 生成默认章节模板内容
                var chapterTitle = $('#chapter-heading-' + chapterId + ' .accordion-button').text().trim();
                var dimensionName = $('.chapter-dimension-item[data-chapter-id="' + chapterId + '"][data-dimension="' + dimension + '"]').text().trim();
                var defaultTemplate = generateDefaultChapterTemplate(chapterId, dimension, chapterTitle, dimensionName);
                chapterTemplateContent[chapterId][dimension] = defaultTemplate;

                // 使用marked.js渲染Markdown内容
                var renderedContent = marked.parse(defaultTemplate);

                // 添加导出按钮和标题
                var headerHtml =
                    '<div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">' +
                        '<h3>' + chapterTitle + ' - ' + dimensionName + ' 设定模板</h3>' +
                        '<button class="btn btn-sm btn-outline-primary export-chapter-btn" data-chapter-id="' + chapterId + '" data-dimension="' + dimension + '">' +
                            '<i class="fas fa-download me-1"></i>导出模板' +
                        '</button>' +
                    '</div>';

                $('#chapterTemplateContent').html(headerHtml + renderedContent);
            }
        }

        // 导出整本书模板按钮事件
        $(document).on('click', '.export-template-btn', function() {
            var dimension = $(this).data('dimension');
            exportBookTemplate(dimension);
        });

        // 导出章节模板按钮事件
        $(document).on('click', '.export-chapter-btn', function() {
            var chapterId = $(this).data('chapter-id');
            var dimension = $(this).data('dimension');
            exportChapterTemplate(chapterId, dimension);
        });

        // 导出整本书设定模板
        function exportBookTemplate(dimension) {
            var content = bookTemplateContent[dimension];
            var dimensionName = $('.dimension-item[data-dimension="' + dimension + '"]').text().trim();
            var filename = novelTitle + "_" + dimensionName + "_设定模板.md";

            // 创建下载链接
            var blob = new Blob([content], { type: 'text/markdown' });
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 尝试备用API路径
        function tryFallbackApi(dimension) {
            console.log("尝试备用API路径...");

            // 尝试不同的API路径
            var fallbackUrls = [
                "/api/novels/" + novelId + "/template/" + dimension,  // 复数形式
                "/v3/api/novel/" + novelId + "/template/" + dimension,  // 添加v3前缀
                "/api/novel/" + novelId + "/templates/" + dimension,  // templates复数形式
                "/api/novel/" + novelId + "/analysis/" + dimension + "/template"  // 分析结果转模板
            ];

            // 依次尝试每个备用URL
            tryNextFallbackUrl(fallbackUrls, 0, dimension);
        }

        // 递归尝试备用URL
        function tryNextFallbackUrl(urls, index, dimension) {
            if (index >= urls.length) {
                console.log("所有备用API路径都失败，生成默认模板");
                // 所有备用URL都失败，生成默认模板
                var dimensionName = $('.dimension-item[data-dimension="' + dimension + '"]').text().trim();
                var defaultTemplate = generateDefaultBookTemplate(dimension, dimensionName);
                handleBookTemplateResponse({template: defaultTemplate}, dimension);
                return;
            }

            var currentUrl = urls[index];
            console.log("尝试备用URL: " + currentUrl);

            $.ajax({
                url: currentUrl,
                type: "GET",
                success: function(response) {
                    console.log("备用API响应: ", response);
                    if (response.success) {
                        // 处理成功响应
                        handleBookTemplateResponse(response, dimension);
                    } else {
                        // 尝试下一个备用URL
                        tryNextFallbackUrl(urls, index + 1, dimension);
                    }
                },
                error: function() {
                    // 尝试下一个备用URL
                    tryNextFallbackUrl(urls, index + 1, dimension);
                }
            });
        }

        // 导出章节设定模板
        function exportChapterTemplate(chapterId, dimension) {
            var content = chapterTemplateContent[chapterId][dimension];
            var chapterTitle = $('#chapter-heading-' + chapterId + ' .accordion-button').text().trim();
            var dimensionName = $('.chapter-dimension-item[data-chapter-id="' + chapterId + '"][data-dimension="' + dimension + '"]').text().trim();
            var filename = novelTitle + "_" + chapterTitle + "_" + dimensionName + "_设定模板.md";

            // 创建下载链接
            var blob = new Blob([content], { type: 'text/markdown' });
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 尝试备用章节API路径
        function tryFallbackChapterApi(chapterId, dimension) {
            console.log("尝试备用章节API路径...");

            // 尝试不同的API路径
            var fallbackUrls = [
                "/api/novels/" + novelId + "/chapter/" + chapterId + "/template/" + dimension,  // novel复数形式
                "/v3/api/novel/" + novelId + "/chapter/" + chapterId + "/template/" + dimension,  // 添加v3前缀
                "/api/novel/" + novelId + "/chapters/" + chapterId + "/template/" + dimension,  // chapter复数形式
                "/api/novel/" + novelId + "/chapter/" + chapterId + "/analysis/" + dimension + "/template"  // 分析结果转模板
            ];

            // 依次尝试每个备用URL
            tryNextFallbackChapterUrl(fallbackUrls, 0, chapterId, dimension);
        }

        // 递归尝试备用章节URL
        function tryNextFallbackChapterUrl(urls, index, chapterId, dimension) {
            if (index >= urls.length) {
                console.log("所有备用章节API路径都失败，生成默认模板");
                // 所有备用URL都失败，生成默认模板
                var chapterTitle = $('#chapter-heading-' + chapterId + ' .accordion-button').text().trim();
                var dimensionName = $('.chapter-dimension-item[data-chapter-id="' + chapterId + '"][data-dimension="' + dimension + '"]').text().trim();
                var defaultTemplate = generateDefaultChapterTemplate(chapterId, dimension, chapterTitle, dimensionName);
                handleChapterTemplateResponse({template: defaultTemplate}, chapterId, dimension);
                return;
            }

            var currentUrl = urls[index];
            console.log("尝试备用章节URL: " + currentUrl);

            $.ajax({
                url: currentUrl,
                type: "GET",
                success: function(response) {
                    console.log("备用章节API响应: ", response);
                    if (response.success) {
                        // 处理成功响应
                        handleChapterTemplateResponse(response, chapterId, dimension);
                    } else {
                        // 尝试下一个备用URL
                        tryNextFallbackChapterUrl(urls, index + 1, chapterId, dimension);
                    }
                },
                error: function() {
                    // 尝试下一个备用URL
                    tryNextFallbackChapterUrl(urls, index + 1, chapterId, dimension);
                }
            });
        }

        // 生成默认整本书设定模板
        function generateDefaultBookTemplate(dimension, dimensionName) {
            var template = "# " + novelTitle + " - " + dimensionName + " 设定模板\n\n";

            template += "## 基本信息\n";
            template += "- 小说名称：" + novelTitle + "\n";
            template += "- 维度：" + dimensionName + "\n";
            template += "- 适用字数范围：30-300万字\n";
            template += "- 生成时间：" + new Date().toLocaleString() + "\n\n";

            template += "## 设定内容\n";
            template += "这是" + dimensionName + "的默认设定模板，您可以根据需要修改和完善。\n\n";

            // 根据不同维度添加特定内容
            if (dimension === "language_style") {
                template += "### 语言风格设定\n";
                template += "- 总体风格：[简洁明快/华丽典雅/质朴自然/锋利犀利]\n";
                template += "- 语言氛围：[轻松幽默/庄重肃穆/神秘悬疑/浪漫温馨]\n";
                template += "- 特色词汇：[专业领域词汇/方言俚语/古典词汇/现代流行语]\n";
                template += "- 句式偏好：[长短句搭配比例/复合句使用频率/排比句运用]\n\n";
            } else if (dimension === "rhythm_pacing") {
                template += "### 节奏节拍设定\n";
                template += "- 总体节奏：[快速紧凑型/舒缓铺陈型/波动变化型/情节驱动型]\n";
                template += "- 加速减速模式：[递进式/波浪式/起伏式/阶梯式]\n";
                template += "- 章节内节奏变化：[开头-中段-结尾的节奏变化规律]\n";
                template += "- 情节与节奏的匹配度：[快节奏场景/慢节奏场景的内容特点]\n\n";
            } else {
                template += "### " + dimensionName + "设定\n";
                template += "- 请在此处添加" + dimensionName + "的具体设定内容\n";
                template += "- 可以包括具体的数值和比例\n";
                template += "- 适用于30-300万字的长篇小说创作\n\n";
            }

            template += "## 应用指南\n";
            template += "本设定模板适用于30-300万字的长篇小说创作，请根据您的具体需求和创作风格进行调整。在使用过程中，建议：\n\n";
            template += "1. **灵活应用**：根据您的故事情节和风格需求，有选择地应用模板中的元素\n";
            template += "2. **保持连贯性**：确保各个维度之间的设定保持一致，避免风格冲突\n";
            template += "3. **创新发展**：在模板基础上进行创新，发展自己独特的创作风格\n\n";

            template += "---\n";
            template += "© " + new Date().getFullYear() + " 九猫写作系统 - 设定模板\n";

            return template;
        }

        // 生成默认章节设定模板
        function generateDefaultChapterTemplate(chapterId, dimension, chapterTitle, dimensionName) {
            var template = "# " + novelTitle + " - " + chapterTitle + " - " + dimensionName + " 设定模板\n\n";

            template += "## 章节基本信息\n";
            template += "- 小说: " + novelTitle + "\n";
            template += "- 章节: " + chapterTitle + "\n";
            template += "- 维度: " + dimensionName + "\n";
            template += "- 适用字数范围: 5000-10000字/章\n\n";

            template += "## 设定内容\n";
            template += "这是" + chapterTitle + "的" + dimensionName + "默认设定模板，您可以根据需要修改和完善。\n\n";

            // 根据不同维度添加特定内容
            if (dimension === "language_style") {
                template += "### 本章语言风格设定\n";
                template += "- 主要语气：[正式/轻松/紧张/舒缓/幽默/严肃]\n";
                template += "- 词汇选择：[专业术语比例/口语化程度/华丽词藻使用频率]\n";
                template += "- 句式特点：[长短句比例/复杂句使用/特殊句式安排]\n";
                template += "- 修辞偏好：[本章重点使用的修辞手法及效果]\n\n";
            } else if (dimension === "rhythm_pacing") {
                template += "### 本章节奏节拍设定\n";
                template += "- 开场节奏：[缓慢铺垫/中速展开/快速进入]\n";
                template += "- 中段变化：[渐进加速/平稳过渡/起伏波动]\n";
                template += "- 高潮设计：[位置/持续长度/强度]\n";
                template += "- 结尾节奏：[戛然而止/缓慢收束/余韵悠长]\n\n";
            } else {
                template += "### 本章" + dimensionName + "设定\n";
                template += "- 请在此处添加本章" + dimensionName + "的具体设定内容\n";
                template += "- 可以包括具体的数值和比例\n";
                template += "- 适用于本章节的创作\n\n";
            }

            template += "## 章节内容结构建议\n";
            template += "### 开篇部分\n";
            template += "- 开场方式：[直接开场/环境描写开场/对话开场/悬念开场]\n";
            template += "- 情境设置：[时间地点人物活动的明确交代]\n\n";

            template += "### 中间发展部分\n";
            template += "- 情节推进：[新情节的引入节奏/线索展开方式]\n";
            template += "- 冲突设置：[冲突类型/展现手法/强度控制]\n\n";

            template += "### 结尾部分\n";
            template += "- 章节收束：[圆满式/悬念式/暗示式/开放式]\n";
            template += "- 后续铺垫：[对下一章的自然引导]\n\n";

            template += "---\n";
            template += "© " + new Date().getFullYear() + " 九猫写作系统 - 章节设定模板\n";

            return template;
        }
    });
</script>
{% endblock %}