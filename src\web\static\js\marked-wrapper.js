/**
 * Marked.js 兼容性包装器
 * 确保 marked.parse() 方法可用
 */

(function() {
    // 等待 marked 加载完成
    function ensureMarked() {
        if (typeof marked !== 'undefined') {
            console.log('[Marked包装器] Marked.js 已加载');
            
            // 检查 parse 方法是否存在
            if (!marked.parse && typeof marked === 'function') {
                console.log('[Marked包装器] 添加 marked.parse 方法');
                
                // 如果 marked 是函数但没有 parse 方法，添加 parse 方法
                marked.parse = function(text) {
                    return marked(text);
                };
            }
            
            // 设置默认选项
            if (marked.setOptions) {
                marked.setOptions({
                    breaks: true,
                    gfm: true
                });
            }
            
            console.log('[Marked包装器] Marked.js 初始化完成');
        } else {
            console.warn('[Marked包装器] Marked.js 未加载，将在 500ms 后重试');
            setTimeout(ensureMarked, 500);
        }
    }
    
    // 在 DOM 加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', ensureMarked);
    } else {
        ensureMarked();
    }
})();
