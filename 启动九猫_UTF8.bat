@echo off
chcp 65001 >nul

echo ===================================
echo   九猫小说分析系统启动脚本
echo ===================================
echo.
echo 正在启动九猫小说分析系统...
echo.

:: 设置工作目录为脚本所在目录
cd /d %~dp0

:: 检查Python是否安装
echo 检查Python安装...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python。请安装Python并添加到PATH。
    pause
    exit /b 1
)

:: 检查是否存在必要文件
echo 检查必要文件...
if not exist src\web\app.py (
    echo [错误] 在当前目录中未找到src\web\app.py。请将此脚本移动到项目根目录。
    pause
    exit /b 1
)

:: 检查端口5001是否被占用
echo 检查端口5001是否被占用...
netstat -ano | findstr :5001 | findstr LISTENING >nul
if %errorlevel% equ 0 (
    echo [警告] 端口5001已被占用，尝试终止占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5001 ^| findstr LISTENING') do (
        echo 尝试终止进程ID: %%a
        taskkill /f /pid %%a >nul 2>&1
        if %errorlevel% equ 0 (
            echo [信息] 成功终止占用端口5001的进程。
        ) else (
            echo [警告] 无法终止占用端口5001的进程。可能需要手动终止。
        )
    )
)

:: 设置环境变量，禁用DEBUG模式，使用真实API调用
echo 设置环境变量...
set DEBUG=False
set USE_REAL_API=True

:: 启动九猫系统
echo 启动九猫小说分析系统...
python run.py

:: 如果启动失败，提示用户
if %errorlevel% neq 0 (
    echo [错误] 启动失败，请检查日志了解详情。
    pause
    exit /b 1
)

echo [成功] 系统已启动。
echo 请在浏览器中访问: http://localhost:5001
pause
