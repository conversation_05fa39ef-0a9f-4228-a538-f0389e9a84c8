"""
直接使用SQL查询检查章节分析结果
"""

import sqlite3
import os
import re

# 数据库路径
db_path = os.path.join(os.path.dirname(__file__), 'novel_analysis.db')

def check_chapter_outline():
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 查询所有章纲分析结果
        cursor.execute("""
            SELECT car.id, car.chapter_id, car.dimension, car.content, car.reasoning_content, 
                   c.title, c.chapter_number
            FROM chapter_analysis_results car
            JOIN chapters c ON car.chapter_id = c.id
            WHERE car.dimension = 'chapter_outline'
        """)
        
        results = cursor.fetchall()
        print(f"找到 {len(results)} 个章纲分析结果")
        
        # 检查每个结果
        for result in results:
            result_id, chapter_id, dimension, content, reasoning_content, chapter_title, chapter_number = result
            
            chapter_title = chapter_title or f'第{chapter_number}章'
            print(f"章节: {chapter_title}")
            print(f"内容长度: {len(content) if content else 0}")
            print(f"推理过程长度: {len(reasoning_content) if reasoning_content else 0}")
            
            # 检查内容中是否包含"主要内容"
            if content and "## 主要内容" in content:
                main_content_match = re.search(r'## 主要内容\s*\n(.*?)(?:\n##|\Z)', content, re.DOTALL)
                if main_content_match:
                    main_content = main_content_match.group(1).strip()
                    print(f"主要内容长度: {len(main_content)}")
                    print(f"主要内容前100个字符: {main_content[:100]}")
                else:
                    print("未找到主要内容部分")
            else:
                print("内容中不包含'主要内容'部分")
                
            print("-" * 50)
            
        # 查询分析过程记录
        cursor.execute("""
            SELECT cap.id, cap.result_id, cap.processing_stage, 
                   length(cap.output_text) as output_length
            FROM chapter_analysis_processes cap
            JOIN chapter_analysis_results car ON cap.result_id = car.id
            WHERE car.dimension = 'chapter_outline'
              AND cap.processing_stage = 'reasoning'
        """)
        
        processes = cursor.fetchall()
        print(f"找到 {len(processes)} 个分析过程记录")
        
        # 检查每个分析过程记录
        for process in processes:
            process_id, result_id, stage, output_length = process
            print(f"分析过程记录: id={process_id}, result_id={result_id}, stage={stage}")
            print(f"输出文本长度: {output_length}")
            print("-" * 50)
    finally:
        conn.close()

if __name__ == "__main__":
    check_chapter_outline()
