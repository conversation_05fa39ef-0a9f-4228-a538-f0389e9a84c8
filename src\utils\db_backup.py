"""
九猫系统数据库备份和优化模块
定期备份数据库到外部存储，并优化数据库性能
"""
import os
import time
import shutil
import sqlite3
import logging
import threading
from datetime import datetime

logger = logging.getLogger(__name__)

# 外部存储根目录
EXTERNAL_STORAGE_ROOT = os.environ.get('EXTERNAL_STORAGE_ROOT', 'E:\\艹，又来一次\\九猫')

# 数据库备份目录
DB_BACKUP_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "db_backup")

# 确保目录存在
os.makedirs(DB_BACKUP_DIR, exist_ok=True)

class DatabaseBackupService(threading.Thread):
    """数据库备份和优化服务"""
    
    def __init__(self, db_path, backup_interval=3600, optimize_interval=86400):
        """
        初始化数据库备份服务
        
        Args:
            db_path: 数据库文件路径
            backup_interval: 备份间隔（秒）
            optimize_interval: 优化间隔（秒）
        """
        super().__init__(daemon=True)
        self.db_path = db_path
        self.backup_interval = backup_interval
        self.optimize_interval = optimize_interval
        self.running = True
        self.last_backup_time = 0
        self.last_optimize_time = 0
    
    def run(self):
        """线程运行函数"""
        logger.info(f"数据库备份和优化服务已启动，备份间隔: {self.backup_interval}秒，优化间隔: {self.optimize_interval}秒")
        
        # 启动时先进行一次备份
        self.backup_database()
        self.last_backup_time = time.time()
        
        while self.running:
            try:
                current_time = time.time()
                
                # 检查是否需要备份
                if current_time - self.last_backup_time > self.backup_interval:
                    self.backup_database()
                    self.last_backup_time = current_time
                
                # 检查是否需要优化
                if current_time - self.last_optimize_time > self.optimize_interval:
                    self.optimize_database()
                    self.last_optimize_time = current_time
            
            except Exception as e:
                logger.error(f"数据库备份和优化服务出错: {str(e)}")
            
            # 等待一段时间
            for _ in range(min(300, self.backup_interval // 10)):  # 最多等待300秒
                if not self.running:
                    break
                time.sleep(10)  # 每10秒检查一次是否需要停止
    
    def backup_database(self):
        """备份数据库"""
        try:
            # 检查数据库文件是否存在
            if not os.path.exists(self.db_path):
                logger.error(f"数据库文件不存在: {self.db_path}")
                return False
            
            # 创建备份文件名（使用当前日期和时间）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            db_name = os.path.basename(self.db_path)
            backup_file = os.path.join(DB_BACKUP_DIR, f"{db_name}.{timestamp}.bak")
            
            # 复制数据库文件
            shutil.copy2(self.db_path, backup_file)
            
            # 清理旧的备份文件（只保留最近10个）
            self.cleanup_old_backups()
            
            logger.info(f"数据库已备份到: {backup_file}")
            return True
        
        except Exception as e:
            logger.error(f"备份数据库时出错: {str(e)}")
            return False
    
    def optimize_database(self):
        """优化数据库"""
        try:
            # 检查数据库文件是否存在
            if not os.path.exists(self.db_path):
                logger.error(f"数据库文件不存在: {self.db_path}")
                return False
            
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            
            try:
                # 执行VACUUM操作，整理数据库文件
                conn.execute("VACUUM")
                
                # 执行ANALYZE操作，更新统计信息
                conn.execute("ANALYZE")
                
                # 提交事务
                conn.commit()
                
                logger.info("数据库已优化")
                return True
            
            finally:
                # 关闭连接
                conn.close()
        
        except Exception as e:
            logger.error(f"优化数据库时出错: {str(e)}")
            return False
    
    def cleanup_old_backups(self):
        """清理旧的备份文件"""
        try:
            # 获取数据库名称
            db_name = os.path.basename(self.db_path)
            
            # 获取所有备份文件
            backup_files = []
            for file in os.listdir(DB_BACKUP_DIR):
                if file.startswith(f"{db_name}.") and file.endswith(".bak"):
                    backup_files.append(os.path.join(DB_BACKUP_DIR, file))
            
            # 按修改时间排序
            backup_files.sort(key=os.path.getmtime)
            
            # 如果备份文件数量超过10个，删除最旧的文件
            if len(backup_files) > 10:
                for old_file in backup_files[:-10]:
                    try:
                        os.remove(old_file)
                        logger.debug(f"已删除旧备份文件: {old_file}")
                    except (PermissionError, OSError) as e:
                        logger.error(f"删除备份文件失败: {old_file}, 错误: {str(e)}")
        
        except Exception as e:
            logger.error(f"清理旧备份文件时出错: {str(e)}")
    
    def stop(self):
        """停止服务"""
        self.running = False
        logger.info("数据库备份和优化服务已停止")

# 全局服务实例
db_backup_service = None

def start_db_backup_service(db_path, backup_interval=3600, optimize_interval=86400):
    """
    启动数据库备份和优化服务
    
    Args:
        db_path: 数据库文件路径
        backup_interval: 备份间隔（秒）
        optimize_interval: 优化间隔（秒）
    """
    global db_backup_service
    
    if db_backup_service and db_backup_service.is_alive():
        logger.warning("数据库备份和优化服务已在运行")
        return
    
    db_backup_service = DatabaseBackupService(db_path, backup_interval, optimize_interval)
    db_backup_service.start()
    
    logger.info("数据库备份和优化服务已启动")

def stop_db_backup_service():
    """停止数据库备份和优化服务"""
    global db_backup_service
    
    if db_backup_service:
        db_backup_service.stop()
        db_backup_service = None
        
        logger.info("数据库备份和优化服务已停止")

def backup_database_now(db_path):
    """
    立即备份数据库
    
    Args:
        db_path: 数据库文件路径
    
    Returns:
        是否备份成功
    """
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            return False
        
        # 创建备份文件名（使用当前日期和时间）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        db_name = os.path.basename(db_path)
        backup_file = os.path.join(DB_BACKUP_DIR, f"{db_name}.{timestamp}.bak")
        
        # 复制数据库文件
        shutil.copy2(db_path, backup_file)
        
        logger.info(f"数据库已备份到: {backup_file}")
        return True
    
    except Exception as e:
        logger.error(f"备份数据库时出错: {str(e)}")
        return False
