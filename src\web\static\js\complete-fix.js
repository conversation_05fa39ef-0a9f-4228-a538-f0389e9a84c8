/**
 * 九猫 - 完整修复脚本
 * 版本: 2.0.0
 * 
 * 整合了所有修复功能，统一处理DOM操作、堆栈溢出和异常捕获
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 设置全局标记，表示已应用修复
    if (window.__completeFixApplied) {
        console.log('完整修复脚本已经应用，跳过');
        return;
    }
    
    window.__completeFixApplied = true;
    console.log('完整修复脚本已加载 - 最高优先级');
    
    // 存储原始DOM方法
    const originalMethods = {
        appendChild: Node.prototype.appendChild,
        replaceChild: Node.prototype.replaceChild,
        removeChild: Node.prototype.removeChild,
        insertBefore: Node.prototype.insertBefore
    };
    
    // 安全地执行操作，避免堆栈溢出
    function safelyExecute(operation, maxDepth = 1) {
        try {
            // 检查递归深度
            if (!window.__recursionDepth) {
                window.__recursionDepth = 0;
            }
            
            // 增加递归计数
            window.__recursionDepth++;
            
            // 如果递归太深，使用原始方法
            if (window.__recursionDepth > maxDepth) {
                console.error('DOM操作出错: Maximum call stack size exceeded');
                window.__recursionDepth = 0; // 重置计数器
                return null;
            }
            
            // 执行操作
            const result = operation();
            
            // 重置递归计数
            window.__recursionDepth--;
            return result;
        } catch (e) {
            // 如果出错，重置计数器
            window.__recursionDepth = 0;
            
            if (e.message && e.message.includes('Maximum call stack size exceeded')) {
                console.error('备用方法也失败: Maximum call stack size exceeded');
            }
            
            return null;
        }
    }
    
    // 安全地进行appendChild操作
    function safeAppendChild(parent, child) {
        if (!parent || !child) {
            return null;
        }
        
        return safelyExecute(() => {
            // 执行原始操作，确保不会无限递归
            return originalMethods.appendChild.call(parent, child);
        });
    }
    
    // 安全地进行replaceChild操作
    function safeReplaceChild(parent, newChild, oldChild) {
        if (!parent || !newChild || !oldChild) {
            return null;
        }
        
        return safelyExecute(() => {
            // 执行原始操作，确保不会无限递归
            return originalMethods.replaceChild.call(parent, newChild, oldChild);
        });
    }
    
    // 安全地进行removeChild操作
    function safeRemoveChild(parent, child) {
        if (!parent || !child) {
            return null;
        }
        
        return safelyExecute(() => {
            // 执行原始操作，确保不会无限递归
            return originalMethods.removeChild.call(parent, child);
        });
    }
    
    // 安全地进行insertBefore操作
    function safeInsertBefore(parent, newNode, referenceNode) {
        if (!parent || !newNode) {
            return null;
        }
        
        return safelyExecute(() => {
            // 执行原始操作，确保不会无限递归
            return originalMethods.insertBefore.call(parent, newNode, referenceNode);
        });
    }
    
    // 修复DOM方法
    function fixDomMethods() {
        // 修复appendChild
        Node.prototype.appendChild = function(child) {
            return safeAppendChild(this, child);
        };
        
        // 修复replaceChild
        Node.prototype.replaceChild = function(newChild, oldChild) {
            return safeReplaceChild(this, newChild, oldChild);
        };
        
        // 修复removeChild
        Node.prototype.removeChild = function(child) {
            return safeRemoveChild(this, child);
        };
        
        // 修复insertBefore
        Node.prototype.insertBefore = function(newNode, referenceNode) {
            return safeInsertBefore(this, newNode, referenceNode);
        };
    }
    
    // 检查核心静态文件
    function checkCoreStaticFiles() {
        console.log('开始检查核心静态文件');
        
        // 核心静态文件
        const coreFiles = [
            { name: 'jquery.min.js', type: 'script', path: '/static/js/lib/jquery.min.js', fallbackPath: '/direct-static/js/lib/jquery.min.js' },
            { name: 'bootstrap.bundle.min.js', type: 'script', path: '/static/js/lib/bootstrap.bundle.min.js', fallbackPath: '/direct-static/js/lib/bootstrap.bundle.min.js' },
            { name: 'chart.min.js', type: 'script', path: '/static/js/lib/chart.min.js', fallbackPath: '/direct-static/js/lib/chart.min.js' },
            { name: 'bootstrap.min.css', type: 'link', path: '/static/css/lib/bootstrap.min.css', fallbackPath: '/direct-static/css/lib/bootstrap.min.css' }
        ];
        
        // 检查每个文件
        coreFiles.forEach(file => {
            let isLoaded = false;
            
            // 根据文件类型检查
            if (file.type === 'script') {
                // 检查脚本是否已加载
                const scripts = document.querySelectorAll('script');
                for (let i = 0; i < scripts.length; i++) {
                    if (scripts[i].src.includes(file.name)) {
                        isLoaded = true;
                        break;
                    }
                }
            } else if (file.type === 'link') {
                // 检查样式表是否已加载
                const links = document.querySelectorAll('link');
                for (let i = 0; i < links.length; i++) {
                    if (links[i].href.includes(file.name)) {
                        isLoaded = true;
                        break;
                    }
                }
            }
            
            // 如果文件未加载，尝试加载
            if (!isLoaded) {
                console.log(`${file.name} 未加载，尝试加载`);
                
                // 创建元素
                let element;
                if (file.type === 'script') {
                    element = document.createElement('script');
                    element.src = file.path;
                    element.async = false;
                    
                    // 添加错误处理，如果加载失败，尝试备用路径
                    element.onerror = function() {
                        console.log(`加载 ${file.name} 失败，尝试备用路径`);
                        
                        // 创建新元素使用备用路径
                        const fallbackElement = document.createElement('script');
                        fallbackElement.src = file.fallbackPath;
                        fallbackElement.async = false;
                        
                        // 安全地添加到文档
                        try {
                            if (document.head) {
                                document.head.appendChild(fallbackElement);
                            } else if (document.documentElement) {
                                document.documentElement.appendChild(fallbackElement);
                            }
                        } catch (e) {
                            console.error(`加载 ${file.name} 的备用路径失败:`, e.message);
                        }
                    };
                } else if (file.type === 'link') {
                    element = document.createElement('link');
                    element.href = file.path;
                    element.rel = 'stylesheet';
                    
                    // 添加错误处理，如果加载失败，尝试备用路径
                    element.onerror = function() {
                        console.log(`加载 ${file.name} 失败，尝试备用路径`);
                        
                        // 创建新元素使用备用路径
                        const fallbackElement = document.createElement('link');
                        fallbackElement.href = file.fallbackPath;
                        fallbackElement.rel = 'stylesheet';
                        
                        // 安全地添加到文档
                        try {
                            if (document.head) {
                                document.head.appendChild(fallbackElement);
                            } else if (document.documentElement) {
                                document.documentElement.appendChild(fallbackElement);
                            }
                        } catch (e) {
                            console.error(`加载 ${file.name} 的备用路径失败:`, e.message);
                        }
                    };
                }
                
                // 安全地添加到文档
                if (element) {
                    try {
                        // 确保head存在
                        if (document.head) {
                            document.head.appendChild(element);
                        } else if (document.documentElement) {
                            document.documentElement.appendChild(element);
                        }
                    } catch (e) {
                        console.error(`加载 ${file.name} 失败:`, e.message);
                    }
                }
            }
        });
    }
    
    // 添加全局错误处理函数
    function addGlobalErrorHandler() {
        // 保存原始window.onerror
        const originalOnError = window.onerror;
        
        // 设置新的错误处理函数
        window.onerror = function(message, source, lineno, colno, error) {
            // 处理特定错误
            if (message && message.includes('Maximum call stack size exceeded')) {
                console.error('捕获到堆栈溢出错误，已阻止');
                
                // 重置DOM方法，防止继续递归
                Node.prototype.appendChild = originalMethods.appendChild;
                Node.prototype.replaceChild = originalMethods.replaceChild;
                Node.prototype.removeChild = originalMethods.removeChild;
                Node.prototype.insertBefore = originalMethods.insertBefore;
                
                // 重新应用修复
                setTimeout(function() {
                    fixDomMethods();
                }, 0);
                
                // 阻止错误继续传播
                return true;
            }
            
            // 处理appendChild相关错误
            if (message && message.includes('appendChild') && message.includes('null')) {
                console.error('捕获到appendChild空对象错误，已阻止');
                return true;
            }
            
            // 如果有原始错误处理函数，调用它
            if (typeof originalOnError === 'function') {
                return originalOnError(message, source, lineno, colno, error);
            }
            
            // 默认不阻止
            return false;
        };
    }
    
    // 修复所有图表
    function fixAllCharts() {
        console.log('开始修复所有图表');
        
        // 查找所有canvas元素
        const canvases = document.querySelectorAll('canvas');
        console.log(`找到 ${canvases.length} 个canvas元素`);
        
        // 如果没有canvas，不需要修复
        if (canvases.length === 0) {
            return;
        }
        
        // 确保Chart.js已加载
        if (typeof Chart === 'undefined') {
            // 加载Chart.js
            const script = document.createElement('script');
            script.src = '/static/js/lib/chart.min.js';
            script.onerror = function() {
                script.src = '/direct-static/js/lib/chart.min.js';
                
                try {
                    document.head.appendChild(script);
                } catch (e) {
                    console.error('加载Chart.js备用路径失败:', e.message);
                }
            };
            
            try {
                document.head.appendChild(script);
            } catch (e) {
                console.error('加载Chart.js失败:', e.message);
            }
            
            return;
        }
        
        // 修复每个canvas
        canvases.forEach(canvas => {
            // 检查是否已经有图表实例
            if (canvas.__chart) {
                // 已经有图表实例，不需要修复
                return;
            }
            
            // 获取canvas的上下文
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('无法获取canvas上下文');
                return;
            }
            
            // 尝试从data属性获取图表配置
            let config;
            try {
                if (canvas.hasAttribute('data-config')) {
                    config = JSON.parse(canvas.getAttribute('data-config'));
                } else {
                    // 使用默认配置
                    config = {
                        type: 'line',
                        data: {
                            labels: ['示例数据点1', '示例数据点2', '示例数据点3'],
                            datasets: [{
                                label: '示例数据集',
                                data: [10, 20, 30],
                                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                borderColor: 'rgba(75, 192, 192, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    };
                }
                
                // 创建新图表
                canvas.__chart = new Chart(ctx, config);
            } catch (e) {
                console.error('创建图表失败:', e.message);
            }
        });
    }
    
    // 主函数
    function main() {
        // 添加全局错误处理
        addGlobalErrorHandler();
        
        // 修复DOM方法
        fixDomMethods();
        
        // 检查核心静态文件
        checkCoreStaticFiles();
        
        // 在DOM加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，执行剩余修复');
            
            // 再次修复DOM方法（以防被覆盖）
            fixDomMethods();
            
            // 修复所有图表
            fixAllCharts();
        });
    }
    
    // 执行主函数
    main();
})();
