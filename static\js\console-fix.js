/**
 * 九猫控制台修复脚本
 * 用于修复jQuery相关问题和其他控制台功能
 * 版本: 1.0.0
 * 最后更新: 2023-05-25
 */

(function() {
    console.log('[控制台修复] 初始化...');

    // 确保jQuery已加载
    function ensureJQuery(callback) {
        if (typeof jQuery !== 'undefined') {
            console.log('[控制台修复] jQuery已加载，版本:', jQuery.fn.jquery);
            callback(jQuery);
            return;
        }

        console.log('[控制台修复] jQuery未加载，尝试加载...');
        
        // 创建script元素加载jQuery
        const script = document.createElement('script');
        script.src = '/static/js/lib/jquery.min.js';
        
        script.onload = function() {
            console.log('[控制台修复] jQuery加载成功!');
            if (callback) callback(jQuery);
        };
        
        script.onerror = function() {
            console.error('[控制台修复] 加载jQuery失败，尝试使用CDN');
            
            const cdnScript = document.createElement('script');
            cdnScript.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';
            
            cdnScript.onload = function() {
                console.log('[控制台修复] 从CDN加载jQuery成功!');
                if (callback) callback(jQuery);
            };
            
            cdnScript.onerror = function() {
                console.error('[控制台修复] 从CDN加载jQuery也失败，创建内联jQuery');
                createInlineJQuery(callback);
            };
            
            document.head.appendChild(cdnScript);
        };
        
        document.head.appendChild(script);
    }

    // 创建内联jQuery (最小版本)
    function createInlineJQuery(callback) {
        console.log('[控制台修复] 创建内联jQuery...');

        // 创建script元素
        const script = document.createElement('script');
        script.textContent = `
            // 极简jQuery替代品
            window.jQuery = function(selector) {
                if (typeof selector === 'function') {
                    if (document.readyState !== 'loading') {
                        selector();
                    } else {
                        document.addEventListener('DOMContentLoaded', selector);
                    }
                    return;
                }

                const elements = typeof selector === 'string'
                    ? document.querySelectorAll(selector)
                    : [selector];

                const jQueryObject = {
                    length: elements.length,
                    each: function(callback) {
                        for (let i = 0; i < elements.length; i++) {
                            callback.call(elements[i], i, elements[i]);
                        }
                        return this;
                    },
                    on: function(event, selector, data, handler) {
                        // 处理参数
                        if (typeof selector === 'function') {
                            handler = selector;
                            selector = undefined;
                            data = undefined;
                        } else if (typeof data === 'function') {
                            handler = data;
                            data = undefined;
                        }

                        if (!handler) return this;

                        return this.each(function() {
                            this.addEventListener(event.split('.')[0], handler);
                        });
                    },
                    off: function(event, handler) {
                        return this.each(function() {
                            this.removeEventListener(event.split('.')[0], handler);
                        });
                    },
                    trigger: function(event) {
                        return this.each(function() {
                            const evt = new Event(event);
                            this.dispatchEvent(evt);
                        });
                    },
                    html: function(content) {
                        if (content === undefined) {
                            return elements[0] ? elements[0].innerHTML : '';
                        }
                        return this.each(function() {
                            this.innerHTML = content;
                        });
                    },
                    text: function(content) {
                        if (content === undefined) {
                            return elements[0] ? elements[0].textContent : '';
                        }
                        return this.each(function() {
                            this.textContent = content;
                        });
                    },
                    val: function(value) {
                        if (value === undefined) {
                            return elements[0] ? elements[0].value : '';
                        }
                        return this.each(function() {
                            this.value = value;
                        });
                    },
                    append: function(content) {
                        return this.each(function() {
                            if (typeof content === 'string') {
                                this.insertAdjacentHTML('beforeend', content);
                            } else {
                                this.appendChild(content);
                            }
                        });
                    },
                    addClass: function(className) {
                        return this.each(function() {
                            this.classList.add(className);
                        });
                    },
                    removeClass: function(className) {
                        return this.each(function() {
                            this.classList.remove(className);
                        });
                    },
                    toggleClass: function(className) {
                        return this.each(function() {
                            this.classList.toggle(className);
                        });
                    },
                    hasClass: function(className) {
                        return elements[0] ? elements[0].classList.contains(className) : false;
                    },
                    attr: function(name, value) {
                        if (value === undefined) {
                            return elements[0] ? elements[0].getAttribute(name) : null;
                        }
                        return this.each(function() {
                            this.setAttribute(name, value);
                        });
                    },
                    removeAttr: function(name) {
                        return this.each(function() {
                            this.removeAttribute(name);
                        });
                    },
                    css: function(prop, value) {
                        if (typeof prop === 'object') {
                            return this.each(function() {
                                for (const p in prop) {
                                    this.style[p] = prop[p];
                                }
                            });
                        }
                        if (value === undefined) {
                            return elements[0] ? getComputedStyle(elements[0])[prop] : '';
                        }
                        return this.each(function() {
                            this.style[prop] = value;
                        });
                    },
                    show: function() {
                        return this.each(function() {
                            this.style.display = '';
                        });
                    },
                    hide: function() {
                        return this.each(function() {
                            this.style.display = 'none';
                        });
                    },
                    toggle: function() {
                        return this.each(function() {
                            this.style.display = this.style.display === 'none' ? '' : 'none';
                        });
                    },
                    click: function(handler) {
                        if (handler) {
                            return this.on('click', handler);
                        }
                        return this.each(function() {
                            this.click();
                        });
                    },
                    ready: function(handler) {
                        if (document.readyState !== 'loading') {
                            handler();
                        } else {
                            document.addEventListener('DOMContentLoaded', handler);
                        }
                        return this;
                    },
                    tab: function(action) {
                        if (action === 'show') {
                            return this.each(function() {
                                this.click();
                            });
                        }
                        return this;
                    }
                };

                // 添加数组访问方法
                for (let i = 0; i < elements.length; i++) {
                    jQueryObject[i] = elements[i];
                }

                return jQueryObject;
            };

            // 添加静态方法
            jQuery.ajax = function(options) {
                const xhr = new XMLHttpRequest();
                xhr.open(options.type || 'GET', options.url, true);
                
                if (options.contentType) {
                    xhr.setRequestHeader('Content-Type', options.contentType);
                }
                
                xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        if (options.success) {
                            let response;
                            try {
                                response = JSON.parse(xhr.responseText);
                            } catch (e) {
                                response = xhr.responseText;
                            }
                            options.success(response);
                        }
                    } else {
                        if (options.error) {
                            options.error(xhr);
                        }
                    }
                };
                
                xhr.onerror = function() {
                    if (options.error) {
                        options.error(xhr);
                    }
                };
                
                xhr.send(options.data);
            };

            // 设置$别名
            window.$ = jQuery;

            console.log('[内联jQuery] 已创建内联jQuery替代品');
        `;

        document.head.appendChild(script);
        
        if (callback) {
            setTimeout(function() {
                callback(window.jQuery);
            }, 10);
        }
    }

    // 修复jQuery.fn.on方法
    function fixJQueryOn($) {
        if (typeof $.fn.on === 'function') {
            console.log('[控制台修复] jQuery.fn.on方法已存在');
            return;
        }
        
        console.log('[控制台修复] 修复jQuery.fn.on方法');
        
        $.fn.on = function(events, selector, data, handler) {
            // 处理参数
            if (typeof selector === 'function') {
                handler = selector;
                selector = undefined;
                data = undefined;
            } else if (typeof data === 'function') {
                handler = data;
                data = undefined;
            }

            if (!handler) return this;

            // 处理多个事件
            if (events.indexOf(' ') > -1) {
                const eventArray = events.split(' ');
                for (let i = 0; i < eventArray.length; i++) {
                    this.on(eventArray[i], selector, data, handler);
                }
                return this;
            }

            return this.each(function() {
                const element = this;

                // 处理事件委托
                if (selector) {
                    const originalHandler = handler;
                    handler = function(e) {
                        const target = e.target;
                        const matches = element.querySelectorAll(selector);
                        let current = target;

                        while (current && current !== element) {
                            for (let i = 0; i < matches.length; i++) {
                                if (current === matches[i]) {
                                    e.delegateTarget = element;
                                    e.currentTarget = current;
                                    originalHandler.call(current, e);
                                    return;
                                }
                            }
                            current = current.parentNode;
                        }
                    };
                }

                // 处理Bootstrap事件
                if (events.includes('.bs.')) {
                    const baseEvent = events.split('.')[0];
                    element.addEventListener(baseEvent, handler);
                } else {
                    element.addEventListener(events, handler);
                }
            });
        };
    }

    // 修复所有jQuery方法
    function fixAllJQueryMethods($) {
        fixJQueryOn($);
        
        // 确保其他常用方法存在
        if (typeof $.fn.tab !== 'function') {
            $.fn.tab = function(action) {
                if (action === 'show') {
                    return this.each(function() {
                        this.click();
                    });
                }
                return this;
            };
        }
        
        if (typeof $.fn.trigger !== 'function') {
            $.fn.trigger = function(eventType) {
                return this.each(function() {
                    try {
                        const event = new Event(eventType);
                        this.dispatchEvent(event);
                    } catch (e) {
                        try {
                            const event = document.createEvent('Event');
                            event.initEvent(eventType, true, true);
                            this.dispatchEvent(event);
                        } catch (ie_e) {
                            console.error('[控制台修复] 触发事件失败:', ie_e);
                        }
                    }
                });
            };
        }
    }

    // 添加应急修复按钮
    function addEmergencyFixButton() {
        const container = document.createElement('div');
        container.id = 'emergencyFixContainer';
        container.style.position = 'fixed';
        container.style.bottom = '10px';
        container.style.right = '10px';
        container.style.zIndex = '9999';
        
        const button = document.createElement('button');
        button.id = 'emergencyFixBtn';
        button.className = 'btn btn-danger btn-sm';
        button.innerHTML = '<i class="fas fa-wrench"></i> 修复脚本加载';
        button.onclick = function() {
            console.log('[控制台修复] 执行应急修复');
            ensureJQuery(function($) {
                fixAllJQueryMethods($);
                alert('jQuery修复完成，请刷新页面');
            });
        };
        
        container.appendChild(button);
        document.body.appendChild(container);
    }

    // 全局错误处理函数
    function handleGlobalError(event) {
        if (event.message && (
            event.message.includes('$ is not defined') ||
            event.message.includes('$(...).on is not a function') ||
            event.message.includes('jQuery')
        )) {
            console.error('[控制台修复] 捕获到jQuery错误:', event.message);
            
            // 显示应急修复按钮
            const container = document.getElementById('emergencyFixContainer');
            if (container) {
                container.style.display = 'block';
            } else {
                addEmergencyFixButton();
            }
            
            // 尝试自动修复
            ensureJQuery(function($) {
                fixAllJQueryMethods($);
            });
        }
    }

    // 添加全局错误处理
    window.addEventListener('error', handleGlobalError);

    // 导出全局函数
    window.fixConsole = function() {
        ensureJQuery(function($) {
            fixAllJQueryMethods($);
        });
    };

    // 立即执行修复
    ensureJQuery(function($) {
        fixAllJQueryMethods($);
        console.log('[控制台修复] jQuery修复完成');
    });

    // 添加应急修复按钮
    addEmergencyFixButton();

    console.log('[控制台修复] 初始化完成');
})();
