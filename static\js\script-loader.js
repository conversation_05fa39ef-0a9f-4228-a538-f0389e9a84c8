/**
 * 脚本加载器
 * 确保按正确的顺序加载所有必需的脚本
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 */

(function() {
    console.log('[脚本加载器] 初始化...');

    // 脚本加载状态
    const loadedScripts = new Set();

    // 脚本加载顺序
    const scriptSequence = [
        { src: '/static/js/global-jquery-error-handler.js', id: 'globalJQueryErrorHandler' },
        { src: '/static/js/lib/jquery.min.js', id: 'jquery' },
        { src: '/static/js/jquery-on-direct-fix.js', id: 'jqueryOnDirectFix' },
        { src: '/static/js/jquery-on-method-fix.js', id: 'jqueryOnMethodFix' },
        { src: '/static/js/console-fix.js', id: 'consoleFix' },
        { src: '/static/js/lib/bootstrap.bundle.min.js', id: 'bootstrap' },
        { src: '/static/js/v3/console.js', id: 'consoleScript' }
    ];

    // 加载脚本
    function loadScript(scriptInfo) {
        return new Promise((resolve, reject) => {
            if (loadedScripts.has(scriptInfo.id)) {
                console.log(`[脚本加载器] 脚本已加载: ${scriptInfo.id}`);
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = scriptInfo.src;
            script.id = scriptInfo.id;

            script.onload = () => {
                console.log(`[脚本加载器] 加载成功: ${scriptInfo.id}`);
                loadedScripts.add(scriptInfo.id);
                resolve();
            };

            script.onerror = (error) => {
                console.error(`[脚本加载器] 加载失败: ${scriptInfo.id}`, error);
                reject(error);
            };

            document.head.appendChild(script);
        });
    }

    // 按顺序加载所有脚本
    async function loadAllScripts() {
        console.log('[脚本加载器] 开始加载脚本序列');

        for (const scriptInfo of scriptSequence) {
            try {
                await loadScript(scriptInfo);

                // 特殊处理：jQuery加载后
                if (scriptInfo.id === 'jquery') {
                    if (typeof jQuery !== 'undefined') {
                        console.log('[脚本加载器] jQuery加载成功，版本:', jQuery.fn.jquery);
                    } else {
                        console.error('[脚本加载器] jQuery加载异常');
                    }
                }

                // 特殊处理：Bootstrap加载后
                if (scriptInfo.id === 'bootstrap') {
                    if (typeof bootstrap !== 'undefined') {
                        console.log('[脚本加载器] Bootstrap加载成功');
                    } else {
                        console.error('[脚本加载器] Bootstrap加载异常');
                    }
                }
            } catch (error) {
                console.error(`[脚本加载器] 加载脚本失败: ${scriptInfo.id}`, error);
                // 继续加载下一个脚本
            }
        }

        console.log('[脚本加载器] 脚本序列加载完成');

        // 触发自定义事件
        const event = new Event('scriptsLoaded');
        document.dispatchEvent(event);
    }

    // 导出全局函数
    window.reloadScripts = loadAllScripts;

    // 开始加载脚本
    loadAllScripts();

    console.log('[脚本加载器] 初始化完成');
})();