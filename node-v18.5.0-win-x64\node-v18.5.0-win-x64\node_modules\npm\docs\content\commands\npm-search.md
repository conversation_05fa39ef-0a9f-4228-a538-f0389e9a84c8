---
title: npm-search
section: 1
description: Search for packages
---

### Synopsis

<!-- AUTOGENERATED USAGE DESCRIPTIONS START -->
<!-- automatically generated, do not edit manually -->
<!-- see lib/commands/search.js -->

```bash
npm search [search terms ...]

aliases: find, s, se
```

<!-- automatically generated, do not edit manually -->
<!-- see lib/commands/search.js -->

<!-- AUTOGENERATED USAGE DESCRIPTIONS END -->

Note: This command is unaware of workspaces.

### Description

Search the registry for packages matching the search terms. `npm search`
performs a linear, incremental, lexically-ordered search through package
metadata for all files in the registry. If your terminal has color
support, it will further highlight the matches in the results.  This can
be disabled with the config item `color`

Additionally, using the `--searchopts` and `--searchexclude` options
paired with more search terms will include and exclude further patterns.
The main difference between `--searchopts` and the standard search terms
is that the former does not highlight results in the output and you can
use them more fine-grained filtering. Additionally, you can add both of
these to your config to change default search filtering behavior.

Search also allows targeting of maintainers in search results, by prefixing
their npm username with `=`.

If a term starts with `/`, then it's interpreted as a regular expression
and supports standard JavaScript RegExp syntax. In this case search will
ignore a trailing `/` .  (Note you must escape or quote many regular
expression characters in most shells.)

### Configuration

<!-- AUTOGENERATED CONFIG DESCRIPTIONS START -->
<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->
#### `long`

* Default: false
* Type: Boolean

Show extended information in `ls`, `search`, and `help-search`.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `json`

* Default: false
* Type: Boolean

Whether or not to output JSON data, rather than the normal output.

* In `npm pkg set` it enables parsing set values with JSON.parse() before
  saving them to your `package.json`.

Not supported by all npm commands.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `color`

* Default: true unless the NO_COLOR environ is set to something other than '0'
* Type: "always" or Boolean

If false, never shows colors. If `"always"` then always shows colors. If
true, then only prints color codes for tty file descriptors.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `parseable`

* Default: false
* Type: Boolean

Output parseable results from commands that write to standard output. For
`npm search`, this will be tab-separated table format.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `description`

* Default: true
* Type: Boolean

Show the description in `npm search`

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `searchopts`

* Default: ""
* Type: String

Space-separated options that are always passed to search.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `searchexclude`

* Default: ""
* Type: String

Space-separated options that limit the results from search.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `registry`

* Default: "https://registry.npmjs.org/"
* Type: URL

The base URL of the npm registry.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `prefer-online`

* Default: false
* Type: Boolean

If true, staleness checks for cached data will be forced, making the CLI
look for updates immediately even for fresh package data.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `prefer-offline`

* Default: false
* Type: Boolean

If true, staleness checks for cached data will be bypassed, but missing data
will be requested from the server. To force full offline mode, use
`--offline`.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

#### `offline`

* Default: false
* Type: Boolean

Force offline mode: no network requests will be done during install. To
allow the CLI to fill in missing cache data, see `--prefer-offline`.

<!-- automatically generated, do not edit manually -->
<!-- see lib/utils/config/definitions.js -->

<!-- AUTOGENERATED CONFIG DESCRIPTIONS END -->

### See Also

* [npm registry](/using-npm/registry)
* [npm config](/commands/npm-config)
* [npmrc](/configuring-npm/npmrc)
* [npm view](/commands/npm-view)
* [npm cache](/commands/npm-cache)
* https://npm.im/npm-registry-fetch
