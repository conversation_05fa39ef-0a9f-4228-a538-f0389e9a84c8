"""
九猫小说分析写作系统v3.5应用入口
"""
import os
import sys
import logging
import time
import traceback
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_cors import CORS
from sqlalchemy.orm import Session
from sqlalchemy import func

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入配置
import config

# 导入数据库模型
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.api_log import ApiLog
from src.db.connection import Session, engine
from src.models.base import Base

# 导入API客户端
try:
    from src.api.deepseek_client import DeepSeekClient
    from src.api.analysis import NovelAnalyzer
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("无法导入DeepSeekClient或NovelAnalyzer，某些功能可能不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(config.LOG_DIR if hasattr(config, 'LOG_DIR') else 'logs', 'v3_app.log'))
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_CONTENT_LENGTH

# 启用CORS
CORS(app)

# 添加自定义模板过滤器
@app.template_filter('tojson_safe')
def tojson_safe(obj):
    """安全地将对象转换为JSON字符串，用于在JavaScript中使用"""
    import json
    from markupsafe import Markup

    # 处理SQLAlchemy的MetaData对象
    if hasattr(obj, '__class__') and obj.__class__.__name__ == 'MetaData':
        try:
            # 尝试将MetaData对象转换为字典
            if hasattr(obj, 'items') and callable(obj.items):
                obj = {k: v for k, v in obj.items()}
            else:
                obj = {}
        except Exception as e:
            logger.error(f"转换MetaData对象为字典时出错: {str(e)}")
            obj = {}

    # 处理其他不可序列化的对象
    def json_default(o):
        if hasattr(o, '__dict__'):
            return o.__dict__
        elif hasattr(o, 'items') and callable(o.items):
            return {k: v for k, v in o.items()}
        else:
            return str(o)

    try:
        # 使用更安全的JSON序列化方式
        json_str = json.dumps(obj, default=json_default, ensure_ascii=False)

        # 确保返回的是安全的标记，避免Flask自动转义
        return Markup(json_str)
    except Exception as e:
        logger.error(f"JSON序列化对象时出错: {str(e)}")
        # 返回一个空对象，避免前端解析错误
        return Markup("{}")

@app.template_filter('format_number')
def format_number(value):
    """格式化数字，添加千位分隔符"""
    try:
        return "{:,}".format(int(value))
    except (ValueError, TypeError):
        return value

# 确保所有数据库表已创建
Base.metadata.create_all(engine)
logger.info("确认所有数据库表已创建")

# 运行数据库迁移
try:
    from src.db.migrations import run_migrations
    logger.info("开始执行数据库迁移...")
    run_migrations()
    logger.info("数据库迁移执行完成")
except Exception as e:
    logger.error(f"执行数据库迁移时出错: {str(e)}", exc_info=True)

# 导入v3版本的路由
try:
    from src.web.routes.v3_routes import v3_bp
    from src.web.routes.v3_api import v3_api_bp
    logger.info("成功导入v3版本的路由蓝图")
except ImportError as e:
    logger.error(f"无法导入v3版本的路由蓝图: {str(e)}")
    # 不再回退到v2版本，而是抛出异常，确保使用正确的API版本
    raise ImportError(f"无法导入v3版本的路由蓝图，请确保v3_routes.py和v3_api.py文件存在且无错误: {str(e)}")

# 导入预设内容API路由蓝图
try:
    from src.web.routes.preset_api import preset_api_bp
    logger.info("成功导入预设内容API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入预设内容API路由蓝图: {str(e)}")
    preset_api_bp = None

# 导入章节API路由蓝图
try:
    from src.web.routes.v3_chapters_api import v3_chapters_api_bp
    logger.info("成功导入章节API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入章节API路由蓝图: {str(e)}")
    v3_chapters_api_bp = None

# 导入章节推理过程修复路由蓝图
try:
    from src.web.routes.chapter_reasoning_fix import chapter_reasoning_fix_bp
    logger.info("成功导入章节推理过程修复路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入章节推理过程修复路由蓝图: {str(e)}")
    chapter_reasoning_fix_bp = None

# 导入v3.1版本的API路由
try:
    # 尝试导入v3_1_api_new.py中的蓝图
    from src.web.routes.v3_1_api_new import v3_1_api_bp
    logger.info("成功导入v3.1版本的API路由蓝图(v3_1_api_new.py)")
except ImportError as e:
    logger.warning(f"无法导入v3.1版本的API路由蓝图(v3_1_api_new.py): {str(e)}")
    try:
        # 如果导入v3_1_api_new.py失败，尝试导入v3_1_api.py
        from src.web.routes.v3_1_api import v3_1_api_bp
        logger.info("成功导入v3.1版本的API路由蓝图(v3_1_api.py)")
    except ImportError as e2:
        logger.warning(f"无法导入v3.1版本的API路由蓝图(v3_1_api.py): {str(e2)}")
        v3_1_api_bp = None

# 导入v3.1版本的路由
try:
    from src.web.routes.v3_1_routes import v3_1_bp
    logger.info("成功导入v3.1版本的路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入v3.1版本的路由蓝图: {str(e)}")
    v3_1_bp = None

# 导入v3.5版本的路由
try:
    from src.web.routes.v3_5_routes import v3_5_bp
    logger.info("成功导入v3.5版本的路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入v3.5版本的路由蓝图: {str(e)}")
    v3_5_bp = None

# 导入v3.5版本的API路由
try:
    from src.web.routes.v3_5_api import v3_5_api_bp
    logger.info("成功导入v3.5版本的API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入v3.5版本的API路由蓝图: {str(e)}")
    v3_5_api_bp = None

# 导入共享API路由模块
try:
    from src.web.routes.shared_api_routes import shared_api_bp
    logger.info("成功导入共享API路由模块")
except ImportError as e:
    logger.warning(f"无法导入共享API路由模块: {str(e)}")
    shared_api_bp = None

# 导入直接数据库查询蓝图
try:
    from src.web.routes.direct_db_routes import direct_db_bp
    logger.info("成功导入直接数据库查询蓝图")
except ImportError as e:
    logger.warning(f"无法导入直接数据库查询蓝图: {str(e)}")
    direct_db_bp = None

# 导入测试功能API路由蓝图
try:
    from src.web.routes.v3_test_api import v3_test_api_bp
    logger.info("成功导入测试功能API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入测试功能API路由蓝图: {str(e)}")
    v3_test_api_bp = None

# 注册蓝图
app.register_blueprint(v3_bp, url_prefix='/v3')
app.register_blueprint(v3_api_bp, url_prefix='/api')

# 注册预设内容API路由蓝图
if preset_api_bp:
    app.register_blueprint(preset_api_bp)
    logger.info("已注册预设内容API路由蓝图")

# 注册章节API路由蓝图
if v3_chapters_api_bp:
    app.register_blueprint(v3_chapters_api_bp)
    logger.info("已注册章节API路由蓝图")

# 添加全局缓存控制
@app.after_request
def add_cache_control(response):
    """添加缓存控制头，防止浏览器缓存"""
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

# 注册章节推理过程修复路由蓝图
if chapter_reasoning_fix_bp:
    app.register_blueprint(chapter_reasoning_fix_bp)
    logger.info("已注册章节推理过程修复路由蓝图")

# 注册v3.1版本的路由蓝图（先注册页面路由，后注册API路由，避免冲突）
if v3_1_bp:
    app.register_blueprint(v3_1_bp)
    logger.info("已注册v3.1版本的路由蓝图")

# 注册v3.1版本的API路由蓝图
if v3_1_api_bp:
    app.register_blueprint(v3_1_api_bp)
    logger.info("已注册v3.1版本的API路由蓝图")

# 注册v3.5版本的路由蓝图
if v3_5_bp:
    app.register_blueprint(v3_5_bp)
    logger.info("已注册v3.5版本的路由蓝图")

# 注册v3.5版本的API路由蓝图
if v3_5_api_bp:
    app.register_blueprint(v3_5_api_bp)
    logger.info("已注册v3.5版本的API路由蓝图")

# 注册共享API路由模块
if shared_api_bp:
    app.register_blueprint(shared_api_bp)
    logger.info("已注册共享API路由模块")

# 注册直接数据库查询蓝图
if direct_db_bp:
    app.register_blueprint(direct_db_bp)
    logger.info("已注册直接数据库查询蓝图")

# 注册测试功能API路由蓝图
if v3_test_api_bp:
    app.register_blueprint(v3_test_api_bp)
    logger.info("已注册测试功能API路由蓝图")

# 主页路由
@app.route('/')
def index():
    # 获取系统版本
    version = os.environ.get('VERSION', '3.0')  # 默认使用3.0版本
    if version == '3.5' and v3_5_bp:
        return redirect(url_for('v3_5.index'))
    elif version == '3.1' and v3_1_bp:
        return redirect(url_for('v3_1.index'))
    elif version == '3.0' or version == '3':
        return redirect(url_for('v3.index'))
    else:
        # 如果指定版本不可用，强制使用v3.0版本
        return redirect(url_for('v3.index'))

# 小说列表页面重定向
@app.route('/novels')
def novels():
    return redirect(url_for('v3.novels'))

# 上传小说页面重定向
@app.route('/upload')
def upload_novel():
    return redirect(url_for('v3.upload_novel'))

# 查看小说详情页面重定向
@app.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    return redirect(url_for('v3.view_novel', novel_id=novel_id))

# 查看分析结果页面重定向
@app.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    return redirect(url_for('v3.analysis', novel_id=novel_id, dimension=dimension))

# 章节分析页面重定向
@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>')
def view_chapter(novel_id, chapter_id):
    return redirect(url_for('v3.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

# 章节分析结果页面重定向
@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def chapter_analysis(novel_id, chapter_id, dimension):
    return redirect(url_for('v3.chapter_analysis', novel_id=novel_id, chapter_id=chapter_id, dimension=dimension))

# 章节分析汇总页面重定向
@app.route('/novel/<int:novel_id>/chapters/summary')
def chapters_summary(novel_id):
    return redirect(url_for('v3.chapters_summary', novel_id=novel_id))

# 系统监控页面重定向
@app.route('/system_monitor')
def system_monitor():
    return redirect(url_for('v3_1.system_monitor'))

# 展示台页面重定向
@app.route('/showcase')
def showcase_redirect():
    return redirect(url_for('v3_1.showcase'))

# 帮助中心页面重定向
@app.route('/help')
def help_redirect():
    return redirect(url_for('v3_1.help_page'))

# 内容仓库页面重定向
@app.route('/content_repository')
def content_repository_redirect():
    return redirect(url_for('v3_1.content_repository_page'))

# 直接重定向到控制台页面
@app.route('/console')
def console_redirect():
    # 获取系统版本
    version = os.environ.get('VERSION', '3.0')  # 默认使用3.0版本
    use_v3_console = os.environ.get('USE_V3_CONSOLE', 'False').lower() == 'true'

    # 如果设置了USE_V3_CONSOLE=True，强制使用v3.0版本的控制台
    if use_v3_console:
        logger.info("强制使用v3.0版本的控制台")
        return redirect(url_for('v3.console'))

    # 否则按照版本号选择控制台
    if version == '3.5' and v3_5_bp:
        return redirect(url_for('v3_5.console'))
    elif version == '3.1' and v3_1_bp:
        return redirect(url_for('v3_1.console'))
    elif version == '3.0' or version == '3':
        return redirect(url_for('v3.console'))
    else:
        # 如果指定版本不可用，强制使用v3.0版本
        return redirect(url_for('v3.console'))

# 控制台页面重定向
@app.route('/v3/console')
def v3_console():
    return redirect(url_for('v3.console'))

# 重定向到v3.1控制台页面
@app.route('/v3.1/console')
def v3_1_console_redirect():
    return redirect(url_for('v3_1.console'))

# 重定向到v3.5控制台页面
@app.route('/v3.5/console')
def v3_5_console_redirect():
    return redirect(url_for('v3_5.console'))

# 重定向到v3.1预设模板详情页面
@app.route('/v3.1/preset/<int:preset_id>/templates')
def v3_1_preset_templates_redirect(preset_id):
    return redirect(url_for('v3_1.preset_template_detail', preset_id=preset_id))

# 重定向到v3.5预设模板详情页面
@app.route('/v3.5/preset/<int:preset_id>/templates')
def v3_5_preset_templates_redirect(preset_id):
    return redirect(url_for('v3_5.preset_template_detail', preset_id=preset_id))

# 重定向到v3.5文本对比页面
@app.route('/v3.5/text_comparison')
def v3_5_text_comparison_redirect():
    return redirect(url_for('v3_5.text_comparison'))

# 错误处理
@app.errorhandler(404)
def page_not_found(e):
    # 获取系统版本
    version = os.environ.get('VERSION', '3.0')
    if version == '3.5' and v3_5_bp:
        return render_template('v3.5/error.html', error_code=404, error_message='页面未找到'), 404
    elif version == '3.1' and v3_1_bp:
        return render_template('v3.1/error.html', error_code=404, error_message='页面未找到'), 404
    else:
        return render_template('v3/error.html', error_code=404, error_message='页面未找到'), 404

@app.errorhandler(500)
def internal_server_error(e):
    logger.error(f"500错误: {str(e)}", exc_info=True)
    # 获取系统版本
    version = os.environ.get('VERSION', '3.0')
    if version == '3.5' and v3_5_bp:
        return render_template('v3.5/error.html', error_code=500, error_message='服务器内部错误'), 500
    elif version == '3.1' and v3_1_bp:
        return render_template('v3.1/error.html', error_code=500, error_message='服务器内部错误'), 500
    else:
        return render_template('v3/error.html', error_code=500, error_message='服务器内部错误'), 500

# 直接路由处理函数，用于修复 direct_db/analysis 路径
@app.route('/direct_db/analysis')
def direct_db_analysis_redirect():
    """
    直接路由处理函数，将请求转发到 direct_db_bp 蓝图中的处理函数
    """
    try:
        # 获取请求参数
        novel_id = request.args.get('novel_id')
        dimension = request.args.get('dimension')

        if not novel_id or not dimension:
            return jsonify({
                "success": False,
                "error": "缺少必要参数"
            }), 400

        # 尝试从数据库中获取分析结果
        with Session() as session:
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                return jsonify({
                    "success": False,
                    "error": "未找到分析结果"
                }), 404

            # 构建结果字典
            result_dict = {
                "id": result.id,
                "novel_id": result.novel_id,
                "dimension": result.dimension,
                "content": result.content,
                "created_at": str(result.created_at),
                "updated_at": str(result.updated_at)
            }

            # 添加reasoning_content字段（如果存在）
            if hasattr(result, 'reasoning_content'):
                result_dict['reasoning_content'] = result.reasoning_content
            else:
                result_dict['reasoning_content'] = ""

            # 安全处理metadata字段
            result_dict['metadata'] = "metadata字段已省略，避免序列化问题"

            return jsonify({
                "success": True,
                "result": result_dict
            })
    except Exception as e:
        logger.error(f"直接查询分析结果时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": f"服务器内部错误: {str(e)}"
        }), 500

# 直接路由处理函数，用于修复 direct_db/chapter_analysis 路径
@app.route('/direct_db/chapter_analysis')
def direct_db_chapter_analysis_redirect():
    """
    直接路由处理函数，将请求转发到 direct_db_bp 蓝图中的处理函数
    """
    try:
        # 获取请求参数
        novel_id = request.args.get('novel_id')
        chapter_id = request.args.get('chapter_id')
        dimension = request.args.get('dimension')

        if not novel_id or not chapter_id or not dimension:
            return jsonify({
                "success": False,
                "error": "缺少必要参数"
            }), 400

        # 尝试从数据库中获取章节分析结果
        with Session() as session:
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                return jsonify({
                    "success": False,
                    "error": "未找到章节分析结果"
                }), 404

            # 构建结果字典
            result_dict = {
                "id": result.id,
                "novel_id": result.novel_id,
                "chapter_id": result.chapter_id,
                "dimension": result.dimension,
                "content": result.content,
                "created_at": str(result.created_at),
                "updated_at": str(result.updated_at)
            }

            # 添加reasoning_content字段（如果存在）
            if hasattr(result, 'reasoning_content'):
                result_dict['reasoning_content'] = result.reasoning_content
            else:
                result_dict['reasoning_content'] = ""

            # 安全处理metadata字段
            result_dict['metadata'] = "metadata字段已省略，避免序列化问题"

            return jsonify({
                "success": True,
                "result": result_dict
            })
    except Exception as e:
        logger.error(f"直接查询章节分析结果时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": f"服务器内部错误: {str(e)}"
        }), 500

if __name__ == '__main__':
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=False,  # 禁用调试模式，避免PIN验证界面
        threaded=True,
        use_reloader=False
    )
