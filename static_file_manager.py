"""
九猫小说分析系统 - 静态文件管理工具

此脚本提供全面的静态文件管理功能：
1. 修复静态文件路径问题
2. 检查所有必需的静态文件
3. 从CDN下载缺失的库文件
4. 创建必要的符号链接
5. 清理冗余文件

解决"Failed to load resource: the server responded with a status of 404 (NOT FOUND)"等静态文件相关错误。
"""
import os
import sys
import shutil
import logging
import urllib.request
import time
import json
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('static_files_manager.log')
    ]
)
logger = logging.getLogger(__name__)

# CDN URLs for common libraries
CDN_URLS = {
    'bootstrap.min.css': 'https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css',
    'bootstrap.bundle.min.js': 'https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js',
    'jquery.min.js': 'https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js',
    'chart.min.js': 'https://cdn.bootcdn.net/ajax/libs/Chart.js/3.7.0/chart.min.js'
}

# 备用CDN URLs
BACKUP_CDN_URLS = {
    'bootstrap.min.css': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
    'bootstrap.bundle.min.js': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
    'jquery.min.js': 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js',
    'chart.min.js': 'https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js'
}

class StaticFileManager:
    def __init__(self):
        # 获取当前工作目录
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 定义静态文件目录
        self.static_dir = os.path.join(self.base_dir, 'src', 'web', 'static')
        self.css_dir = os.path.join(self.static_dir, 'css')
        self.js_dir = os.path.join(self.static_dir, 'js')
        self.css_lib_dir = os.path.join(self.css_dir, 'lib')
        self.js_lib_dir = os.path.join(self.js_dir, 'lib')
        
        # 确保目录存在
        os.makedirs(self.css_dir, exist_ok=True)
        os.makedirs(self.js_dir, exist_ok=True)
        os.makedirs(self.css_lib_dir, exist_ok=True)
        os.makedirs(self.js_lib_dir, exist_ok=True)
        
        # 需要检查的文件列表
        self.required_files = {
            'css': ['bootstrap.min.css', 'style.css'],
            'js': ['jquery.min.js', 'bootstrap.bundle.min.js', 'chart.min.js', 'main.js']
        }
        
        # 文件映射 {目标文件: 可能的源文件列表}
        self.file_mappings = {
            os.path.join(self.css_dir, 'bootstrap.min.css'): [
                os.path.join(self.css_lib_dir, 'bootstrap.min.css')
            ],
            os.path.join(self.js_dir, 'jquery.min.js'): [
                os.path.join(self.js_lib_dir, 'jquery.min.js'),
                os.path.join(self.js_lib_dir, 'jquery-3.6.0.min.js')
            ],
            os.path.join(self.js_dir, 'bootstrap.bundle.min.js'): [
                os.path.join(self.js_lib_dir, 'bootstrap.bundle.min.js')
            ],
            os.path.join(self.js_dir, 'chart.min.js'): [
                os.path.join(self.js_lib_dir, 'chart.min.js')
            ]
        }
    
    def check_static_files(self):
        """检查所有必需的静态文件是否存在"""
        logger.info("检查静态文件...")
        missing_files = []
        
        # 检查CSS文件
        for css_file in self.required_files['css']:
            file_path = os.path.join(self.css_dir, css_file)
            if not os.path.exists(file_path):
                missing_files.append(file_path)
                logger.warning(f"缺少CSS文件: {css_file}")
        
        # 检查JS文件
        for js_file in self.required_files['js']:
            file_path = os.path.join(self.js_dir, js_file)
            if not os.path.exists(file_path):
                missing_files.append(file_path)
                logger.warning(f"缺少JS文件: {js_file}")
        
        return missing_files
    
    def copy_from_lib(self):
        """从lib子目录复制文件到主目录"""
        logger.info("从lib子目录复制文件到主目录...")
        copied_files = []
        
        for dst, src_list in self.file_mappings.items():
            if os.path.exists(dst):
                logger.info(f"文件已存在，跳过: {os.path.basename(dst)}")
                continue
                
            copied = False
            for src in src_list:
                if os.path.exists(src):
                    try:
                        shutil.copy2(src, dst)
                        logger.info(f"已复制: {os.path.basename(src)} -> {os.path.basename(dst)}")
                        copied_files.append(dst)
                        copied = True
                        break
                    except Exception as e:
                        logger.error(f"复制文件时出错 {src} -> {dst}: {str(e)}")
            
            if not copied:
                logger.warning(f"无法找到源文件来复制到: {dst}")
        
        return copied_files
    
    def download_missing_files(self, missing_files):
        """从CDN下载缺失的文件"""
        logger.info("从CDN下载缺失的文件...")
        downloaded_files = []
        
        for file_path in missing_files:
            file_name = os.path.basename(file_path)
            
            # 跳过非库文件
            if file_name not in CDN_URLS and file_name not in ['style.css', 'main.js']:
                logger.info(f"跳过非库文件: {file_name}")
                continue
            
            # 对于style.css和main.js，创建空文件
            if file_name in ['style.css', 'main.js']:
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        if file_name == 'style.css':
                            f.write("/* 九猫小说分析系统自定义样式 */\n")
                        elif file_name == 'main.js':
                            f.write("// 九猫小说分析系统主JS文件\n")
                    logger.info(f"已创建空文件: {file_name}")
                    downloaded_files.append(file_path)
                    continue
                except Exception as e:
                    logger.error(f"创建文件时出错 {file_path}: {str(e)}")
                    continue
            
            # 下载库文件
            url = CDN_URLS.get(file_name)
            backup_url = BACKUP_CDN_URLS.get(file_name)
            
            if not url:
                logger.warning(f"没有为 {file_name} 找到CDN URL")
                continue
            
            try:
                logger.info(f"正在从 {url} 下载 {file_name}...")
                urllib.request.urlretrieve(url, file_path)
                logger.info(f"已下载: {file_name}")
                downloaded_files.append(file_path)
            except Exception as e:
                logger.warning(f"从主CDN下载 {file_name} 失败: {str(e)}")
                
                # 尝试备用CDN
                if backup_url:
                    try:
                        logger.info(f"正在从备用CDN {backup_url} 下载 {file_name}...")
                        urllib.request.urlretrieve(backup_url, file_path)
                        logger.info(f"已从备用CDN下载: {file_name}")
                        downloaded_files.append(file_path)
                    except Exception as e2:
                        logger.error(f"从备用CDN下载 {file_name} 失败: {str(e2)}")
                else:
                    logger.error(f"没有为 {file_name} 找到备用CDN URL")
        
        return downloaded_files
    
    def create_symlinks(self):
        """创建符号链接，确保lib目录和主目录的文件保持同步"""
        logger.info("创建符号链接...")
        created_links = []
        
        # 在Windows上，需要管理员权限才能创建符号链接
        # 因此，我们使用复制而不是符号链接
        for dst, src_list in self.file_mappings.items():
            if not os.path.exists(dst):
                continue
                
            for src in src_list:
                if not os.path.exists(os.path.dirname(src)):
                    os.makedirs(os.path.dirname(src), exist_ok=True)
                
                if not os.path.exists(src):
                    try:
                        shutil.copy2(dst, src)
                        logger.info(f"已复制到lib目录: {os.path.basename(dst)} -> {src}")
                        created_links.append(src)
                    except Exception as e:
                        logger.error(f"复制到lib目录时出错 {dst} -> {src}: {str(e)}")
        
        return created_links
    
    def fix_static_files(self):
        """修复静态文件问题的主函数"""
        logger.info("开始修复静态文件问题...")
        
        # 步骤1: 从lib子目录复制文件到主目录
        copied_files = self.copy_from_lib()
        logger.info(f"从lib子目录复制了 {len(copied_files)} 个文件")
        
        # 步骤2: 检查是否仍有缺失的文件
        missing_files = self.check_static_files()
        
        # 步骤3: 下载缺失的文件
        if missing_files:
            logger.info(f"发现 {len(missing_files)} 个缺失的文件，尝试下载...")
            downloaded_files = self.download_missing_files(missing_files)
            logger.info(f"下载了 {len(downloaded_files)} 个文件")
        else:
            logger.info("没有缺失的文件需要下载")
            downloaded_files = []
        
        # 步骤4: 创建符号链接
        created_links = self.create_symlinks()
        logger.info(f"创建了 {len(created_links)} 个文件副本到lib目录")
        
        # 步骤5: 最终检查
        remaining_missing = self.check_static_files()
        
        if remaining_missing:
            logger.warning(f"仍有 {len(remaining_missing)} 个文件缺失")
            for file_path in remaining_missing:
                logger.warning(f"缺失: {file_path}")
            return False
        else:
            logger.info("所有静态文件已成功修复")
            return True
    
    def create_direct_static_dir(self):
        """创建direct-static目录并复制文件"""
        logger.info("创建direct-static目录...")
        
        direct_static_dir = os.path.join(self.base_dir, 'src', 'web', 'direct-static')
        direct_css_dir = os.path.join(direct_static_dir, 'css')
        direct_js_dir = os.path.join(direct_static_dir, 'js')
        
        os.makedirs(direct_css_dir, exist_ok=True)
        os.makedirs(direct_js_dir, exist_ok=True)
        
        # 复制文件到direct-static目录
        for css_file in self.required_files['css']:
            src = os.path.join(self.css_dir, css_file)
            dst = os.path.join(direct_css_dir, css_file)
            if os.path.exists(src) and not os.path.exists(dst):
                try:
                    shutil.copy2(src, dst)
                    logger.info(f"已复制到direct-static: {css_file}")
                except Exception as e:
                    logger.error(f"复制到direct-static时出错 {src} -> {dst}: {str(e)}")
        
        for js_file in self.required_files['js']:
            src = os.path.join(self.js_dir, js_file)
            dst = os.path.join(direct_js_dir, js_file)
            if os.path.exists(src) and not os.path.exists(dst):
                try:
                    shutil.copy2(src, dst)
                    logger.info(f"已复制到direct-static: {js_file}")
                except Exception as e:
                    logger.error(f"复制到direct-static时出错 {src} -> {dst}: {str(e)}")
        
        logger.info("direct-static目录创建完成")
        return True
    
    def generate_report(self):
        """生成静态文件状态报告"""
        logger.info("生成静态文件状态报告...")
        
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "static_files": {
                "css": {},
                "js": {}
            },
            "lib_files": {
                "css": {},
                "js": {}
            },
            "direct_static": {
                "exists": False,
                "files": []
            }
        }
        
        # 检查CSS文件
        for css_file in self.required_files['css']:
            file_path = os.path.join(self.css_dir, css_file)
            report["static_files"]["css"][css_file] = os.path.exists(file_path)
        
        # 检查JS文件
        for js_file in self.required_files['js']:
            file_path = os.path.join(self.js_dir, js_file)
            report["static_files"]["js"][js_file] = os.path.exists(file_path)
        
        # 检查lib目录中的文件
        for file_name in ['bootstrap.min.css']:
            file_path = os.path.join(self.css_lib_dir, file_name)
            report["lib_files"]["css"][file_name] = os.path.exists(file_path)
        
        for file_name in ['jquery.min.js', 'jquery-3.6.0.min.js', 'bootstrap.bundle.min.js', 'chart.min.js']:
            file_path = os.path.join(self.js_lib_dir, file_name)
            report["lib_files"]["js"][file_name] = os.path.exists(file_path)
        
        # 检查direct-static目录
        direct_static_dir = os.path.join(self.base_dir, 'src', 'web', 'direct-static')
        report["direct_static"]["exists"] = os.path.exists(direct_static_dir)
        
        if report["direct_static"]["exists"]:
            for root, _, files in os.walk(direct_static_dir):
                for file in files:
                    rel_path = os.path.relpath(os.path.join(root, file), direct_static_dir)
                    report["direct_static"]["files"].append(rel_path)
        
        # 保存报告
        report_path = os.path.join(self.base_dir, 'static_files_report.json')
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"报告已保存到: {report_path}")
        return report

def main():
    print("九猫小说分析系统 - 静态文件管理工具")
    print("=" * 50)
    
    manager = StaticFileManager()
    
    print("\n1. 检查静态文件")
    missing_files = manager.check_static_files()
    if missing_files:
        print(f"发现 {len(missing_files)} 个缺失的文件:")
        for file in missing_files:
            print(f"  - {os.path.basename(file)}")
    else:
        print("所有必需的静态文件都存在")
    
    print("\n2. 修复静态文件")
    success = manager.fix_static_files()
    if success:
        print("静态文件修复成功！")
    else:
        print("静态文件修复部分成功，可能仍有一些文件缺失")
    
    print("\n3. 创建direct-static目录")
    manager.create_direct_static_dir()
    
    print("\n4. 生成报告")
    report = manager.generate_report()
    
    print("\n静态文件状态摘要:")
    print(f"CSS文件: {sum(report['static_files']['css'].values())}/{len(report['static_files']['css'])}")
    print(f"JS文件: {sum(report['static_files']['js'].values())}/{len(report['static_files']['js'])}")
    print(f"direct-static目录: {'存在' if report['direct_static']['exists'] else '不存在'}")
    
    print("\n修复完成！请重新启动九猫系统以应用更改。")
    print("详细日志已保存到 static_files_manager.log")
    print("详细报告已保存到 static_files_report.json")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"发生错误: {str(e)}", exc_info=True)
        print(f"发生错误: {str(e)}")
        print("详细错误信息已记录到日志文件")
    
    input("\n按Enter键退出...")
