/**
 * 九猫 - 分析对话框修复脚本
 * 用于修复普通分析对话框中的维度选择问题
 * 版本: 1.0.0
 */

(function() {
    console.log('分析对话框修复脚本已加载 - 版本1.0.0');
    
    // 正确的维度列表（与后端一致）
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "chapter_outline", name: "章节大纲" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" }
    ];
    
    // 修复分析对话框
    function fixAnalyzeModal() {
        console.log('开始修复分析对话框');
        
        // 查找模态框
        const modal = document.getElementById('analyzeModal');
        if (!modal) {
            console.warn('未找到分析对话框，可能不在相关页面');
            return;
        }
        
        // 查找模态框内容区域
        const modalBody = modal.querySelector('.modal-body');
        if (!modalBody) {
            console.warn('未找到模态框内容区域');
            return;
        }
        
        // 查找全选选项
        const selectAllCheckbox = modalBody.querySelector('#select-all-dimensions');
        if (!selectAllCheckbox) {
            console.warn('未找到全选选项');
            return;
        }
        
        // 查找分隔线
        const hr = modalBody.querySelector('hr');
        if (!hr) {
            console.warn('未找到分隔线');
            return;
        }
        
        // 检查是否已有维度选项
        const existingDimensions = modalBody.querySelectorAll('.dimension-checkbox');
        if (existingDimensions.length > 0) {
            console.log(`已存在 ${existingDimensions.length} 个维度选项，无需修复`);
            return;
        }
        
        // 创建维度列表容器
        const dimensionsContainer = document.createElement('div');
        dimensionsContainer.className = 'dimensions-container';
        dimensionsContainer.style.marginTop = '15px';
        dimensionsContainer.style.maxHeight = '300px';
        dimensionsContainer.style.overflowY = 'auto';
        
        // 添加维度选项
        DIMENSIONS.forEach((dimension, index) => {
            const dimensionItem = document.createElement('div');
            dimensionItem.className = 'form-check';
            dimensionItem.style.marginBottom = '8px';
            
            const dimensionCheckbox = document.createElement('input');
            dimensionCheckbox.type = 'checkbox';
            dimensionCheckbox.className = 'form-check-input dimension-checkbox';
            dimensionCheckbox.id = `dimension-${index}`;
            dimensionCheckbox.value = dimension.key;
            dimensionCheckbox.dataset.dimension = dimension.key;
            dimensionCheckbox.dataset.dimensionName = dimension.name;
            dimensionCheckbox.checked = true;
            
            const dimensionLabel = document.createElement('label');
            dimensionLabel.className = 'form-check-label';
            dimensionLabel.htmlFor = `dimension-${index}`;
            dimensionLabel.textContent = dimension.name;
            
            dimensionItem.appendChild(dimensionCheckbox);
            dimensionItem.appendChild(dimensionLabel);
            dimensionsContainer.appendChild(dimensionItem);
        });
        
        // 将维度列表容器插入到分隔线后面
        hr.parentNode.insertBefore(dimensionsContainer, hr.nextSibling);
        
        console.log(`已添加 ${DIMENSIONS.length} 个维度选项到分析对话框`);
        
        // 修复全选/取消全选功能
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            dimensionsContainer.querySelectorAll('.dimension-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
        
        console.log('已修复全选/取消全选功能');
        
        // 修复表单提交逻辑
        const form = modal.querySelector('form');
        if (form) {
            const originalSubmit = form.onsubmit;
            form.onsubmit = function(event) {
                // 获取选中的维度
                const selectedDimensions = [];
                dimensionsContainer.querySelectorAll('.dimension-checkbox:checked').forEach(checkbox => {
                    selectedDimensions.push(checkbox.value);
                });
                
                // 如果没有选中的维度，阻止提交
                if (selectedDimensions.length === 0) {
                    alert('请至少选择一个维度进行分析');
                    event.preventDefault();
                    return false;
                }
                
                // 清除之前可能存在的维度字段
                form.querySelectorAll('input[name="dimensions"]').forEach(field => field.remove());
                
                // 为每个选中的维度创建一个隐藏字段
                selectedDimensions.forEach(dimension => {
                    const hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.name = 'dimensions';
                    hiddenField.value = dimension;
                    form.appendChild(hiddenField);
                });
                
                console.log(`已添加 ${selectedDimensions.length} 个维度字段到表单`);
                
                // 调用原始提交处理器
                if (originalSubmit) {
                    return originalSubmit.call(this, event);
                }
            };
            
            console.log('已修复表单提交逻辑');
        }
    }
    
    // 监听模态框显示事件
    function setupModalListener() {
        // 使用jQuery监听模态框显示事件
        if (typeof $ !== 'undefined') {
            $(document).on('shown.bs.modal', '#analyzeModal', function() {
                console.log('检测到分析对话框显示，应用修复');
                setTimeout(fixAnalyzeModal, 100);
            });
            console.log('已设置jQuery模态框监听器');
        }
        
        // 使用原生JavaScript监听模态框显示
        document.addEventListener('click', function(event) {
            if (event.target.matches('[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]') ||
                event.target.closest('[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]')) {
                console.log('检测到分析按钮点击');
                setTimeout(function() {
                    const modal = document.getElementById('analyzeModal');
                    if (modal && modal.classList.contains('show')) {
                        console.log('检测到模态框打开 (原生JS)');
                        setTimeout(fixAnalyzeModal, 100);
                    }
                }, 300);
            }
        });
        console.log('已设置原生JS模态框监听器');
        
        // 使用MutationObserver监听模态框显示
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'class' && 
                    mutation.target.id === 'analyzeModal' && 
                    mutation.target.classList.contains('show')) {
                    console.log('检测到模态框打开 (MutationObserver)');
                    setTimeout(fixAnalyzeModal, 100);
                }
            });
        });
        
        const analyzeModal = document.getElementById('analyzeModal');
        if (analyzeModal) {
            observer.observe(analyzeModal, { attributes: true });
            console.log('已设置MutationObserver模态框监听器');
        }
    }
    
    // 在页面加载完成后执行初始化
    function init() {
        console.log('初始化分析对话框修复');
        
        // 检查当前是否有打开的模态框
        const openModal = document.getElementById('analyzeModal');
        if (openModal && openModal.classList.contains('show')) {
            console.log('检测到已打开的分析对话框');
            fixAnalyzeModal();
        }
        
        // 设置模态框监听器
        setupModalListener();
        
        // 导出全局函数
        window.analyzeModalFix = {
            fixAnalyzeModal: fixAnalyzeModal
        };
        
        console.log('分析对话框修复脚本初始化完成');
    }
    
    // 在页面加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
