/**
 * 九猫v3.1 - jQuery修复脚本
 * 解决v3.1版本中jQuery未定义的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('v3.1 jQuery修复脚本已加载');

    // 检查jQuery是否已加载
    function checkJQuery() {
        if (typeof jQuery !== 'undefined') {
            console.log('jQuery已加载，版本:', jQuery.fn.jquery);
            
            // 确保$ 变量可用
            if (typeof $ === 'undefined') {
                console.log('$ 变量未定义，设置 $ = jQuery');
                window.$ = jQuery;
            }
            
            return true;
        } else {
            console.error('jQuery未加载，尝试加载jQuery');
            loadJQuery();
            return false;
        }
    }

    // 加载jQuery
    function loadJQuery() {
        console.log('尝试加载jQuery...');
        
        // 创建script元素加载jQuery
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';
        script.onload = function() {
            console.log('jQuery加载成功!');
            
            // 确保$ 变量可用
            window.$ = jQuery;
            
            // 触发jQuery加载完成事件
            const event = new Event('jQueryLoaded');
            document.dispatchEvent(event);
            
            // 重新加载v3.1的console.js
            reloadConsoleScript();
        };
        script.onerror = function() {
            console.error('加载jQuery失败，尝试使用备用CDN');
            
            // 尝试备用CDN
            const backupScript = document.createElement('script');
            backupScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js';
            backupScript.onload = function() {
                console.log('jQuery通过备用CDN加载成功!');
                
                // 确保$ 变量可用
                window.$ = jQuery;
                
                // 触发jQuery加载完成事件
                const event = new Event('jQueryLoaded');
                document.dispatchEvent(event);
                
                // 重新加载v3.1的console.js
                reloadConsoleScript();
            };
            backupScript.onerror = function() {
                console.error('所有jQuery加载尝试均失败');
            };
            document.head.appendChild(backupScript);
        };
        document.head.appendChild(script);
    }

    // 重新加载console.js脚本
    function reloadConsoleScript() {
        console.log('重新加载v3.1 console.js脚本...');
        
        // 移除旧的console.js脚本
        const oldScripts = document.querySelectorAll('script[src*="v3.1/console.js"]');
        oldScripts.forEach(script => {
            script.parentNode.removeChild(script);
        });
        
        // 加载新的console.js脚本
        const script = document.createElement('script');
        script.src = '/static/js/v3.1/console.js';
        script.onload = function() {
            console.log('v3.1 console.js脚本重新加载成功!');
        };
        script.onerror = function() {
            console.error('重新加载v3.1 console.js脚本失败');
        };
        document.body.appendChild(script);
    }

    // 修改v3.1 console.js的初始化方式
    function patchConsoleInit() {
        console.log('修补v3.1 console.js初始化方式...');
        
        // 检查是否已经存在console.js脚本
        const consoleScripts = document.querySelectorAll('script[src*="v3.1/console.js"]');
        if (consoleScripts.length > 0) {
            console.log('找到v3.1 console.js脚本，准备修补');
            
            // 创建一个新的脚本元素，包含修补代码
            const patchScript = document.createElement('script');
            patchScript.textContent = `
                // 修补v3.1 console.js初始化方式
                if (typeof window.originalDocumentReady === 'undefined') {
                    // 保存原始的$(document).ready方法
                    window.originalDocumentReady = $.fn.ready;
                    
                    // 重写$(document).ready方法
                    $.fn.ready = function(callback) {
                        if (typeof callback === 'function') {
                            // 确保jQuery已加载
                            if (typeof jQuery !== 'undefined') {
                                window.originalDocumentReady.call(this, callback);
                            } else {
                                // 等待jQuery加载完成
                                document.addEventListener('jQueryLoaded', function() {
                                    window.originalDocumentReady.call(this, callback);
                                });
                            }
                        }
                        return this;
                    };
                    
                    console.log('已修补$(document).ready方法');
                }
            `;
            document.head.appendChild(patchScript);
        }
    }

    // 在页面加载时检查jQuery
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                checkJQuery();
                patchConsoleInit();
            }, 100);
        });
    } else {
        setTimeout(function() {
            checkJQuery();
            patchConsoleInit();
        }, 100);
    }

    // 在页面完全加载后再次检查
    window.addEventListener('load', function() {
        setTimeout(function() {
            checkJQuery();
            patchConsoleInit();
        }, 500);
    });

    console.log('v3.1 jQuery修复脚本已加载完成');
})();
