/**
 * 九猫小说分析写作系统 - 分析结果显示修复脚本
 *
 * 此脚本用于修复分析结果显示问题，确保分析完成后结果正确显示
 * 版本: 1.0.0
 */

(function() {
    console.log('[分析结果显示修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        pollInterval: 1000,  // 轮询间隔（毫秒）
        maxRetries: 5,       // 最大重试次数
        selectors: {
            analysisContent: '#analysisContent',
            reasoningContent: '#reasoningContent',
            progressBar: '.progress-bar',
            statusBadge: '.analysis-status-badge',
            loadingIndicator: '.loading-indicator'
        }
    };

    // 状态
    const STATE = {
        novelId: null,
        chapterId: null,
        dimension: null,
        isChapterAnalysis: false,
        isLoading: false,
        retryCount: 0,
        pollTimer: null
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[分析结果显示修复] ${message}`);
        }
    }

    // 获取页面信息
    function getPageInfo() {
        // 尝试从URL获取信息
        const pathParts = window.location.pathname.split('/');

        // 检查是否是章节分析页面
        if (pathParts.includes('chapter') && pathParts.includes('analysis')) {
            const novelIdIndex = pathParts.indexOf('novel') + 1;
            const chapterIdIndex = pathParts.indexOf('chapter') + 1;
            const dimensionIndex = pathParts.indexOf('analysis') + 1;

            if (novelIdIndex > 0 && chapterIdIndex > 0 && dimensionIndex > 0 &&
                novelIdIndex < pathParts.length &&
                chapterIdIndex < pathParts.length &&
                dimensionIndex < pathParts.length) {

                STATE.novelId = pathParts[novelIdIndex];
                STATE.chapterId = pathParts[chapterIdIndex];
                STATE.dimension = pathParts[dimensionIndex];
                STATE.isChapterAnalysis = true;

                debugLog(`检测到章节分析页面: 小说ID=${STATE.novelId}, 章节ID=${STATE.chapterId}, 维度=${STATE.dimension}`);
                return true;
            }
        }

        // 检查是否是整本书分析页面
        if (pathParts.includes('novel') && pathParts.includes('analysis')) {
            const novelIdIndex = pathParts.indexOf('novel') + 1;
            const dimensionIndex = pathParts.indexOf('analysis') + 1;

            if (novelIdIndex > 0 && dimensionIndex > 0 &&
                novelIdIndex < pathParts.length &&
                dimensionIndex < pathParts.length) {

                STATE.novelId = pathParts[novelIdIndex];
                STATE.dimension = pathParts[dimensionIndex];
                STATE.isChapterAnalysis = false;

                debugLog(`检测到整本书分析页面: 小说ID=${STATE.novelId}, 维度=${STATE.dimension}`);
                return true;
            }
        }

        // 尝试从页面元素获取信息
        const novelElement = document.querySelector('[data-novel-id]');
        const chapterElement = document.querySelector('[data-chapter-id]');
        const dimensionElement = document.querySelector('[data-dimension]');

        if (novelElement) {
            STATE.novelId = novelElement.getAttribute('data-novel-id');

            if (chapterElement) {
                STATE.chapterId = chapterElement.getAttribute('data-chapter-id');
                STATE.isChapterAnalysis = true;
            }

            if (dimensionElement) {
                STATE.dimension = dimensionElement.getAttribute('data-dimension');
            }

            if (STATE.novelId && STATE.dimension) {
                debugLog(`从页面元素获取信息: 小说ID=${STATE.novelId}, ${STATE.isChapterAnalysis ? '章节ID=' + STATE.chapterId + ', ' : ''}维度=${STATE.dimension}`);
                return true;
            }
        }

        debugLog('无法获取页面信息', 'warn');
        return false;
    }

    // 加载分析结果
    function loadAnalysisResult() {
        if (STATE.isLoading) {
            debugLog('已有加载请求正在进行，跳过');
            return;
        }

        if (!STATE.novelId || !STATE.dimension) {
            debugLog('缺少必要信息，无法加载分析结果', 'warn');
            return;
        }

        STATE.isLoading = true;
        debugLog('开始加载分析结果');

        // 构建API URL
        let apiUrl;
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            apiUrl = `/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}`;
            debugLog(`使用章节分析API: ${apiUrl}`);
        } else {
            apiUrl = `/api/novel/${STATE.novelId}/analysis/${STATE.dimension}`;
            debugLog(`使用整本书分析API: ${apiUrl}`);
        }

        // 添加时间戳防止缓存
        apiUrl += `?_=${Date.now()}`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到分析结果数据');

                if (data.success === false) {
                    throw new Error(data.error || '获取分析结果失败');
                }

                // 提取结果内容
                let content = '';
                if (data.result && data.result.content) {
                    content = data.result.content;
                } else if (data.content) {
                    content = data.content;
                } else if (data.analysis && data.analysis.content) {
                    content = data.analysis.content;
                }

                if (content) {
                    // 更新分析结果显示
                    updateAnalysisContent(content);
                    debugLog('成功更新分析结果内容');

                    // 加载推理过程
                    loadReasoningContent();
                } else {
                    debugLog('分析结果内容为空', 'warn');

                    // 尝试重试
                    retryLoadAnalysisResult();
                }
            })
            .catch(error => {
                debugLog(`加载分析结果出错: ${error.message}`, 'error');

                // 尝试重试
                retryLoadAnalysisResult();
            })
            .finally(() => {
                STATE.isLoading = false;
            });
    }

    // 重试加载分析结果
    function retryLoadAnalysisResult() {
        if (STATE.retryCount < CONFIG.maxRetries) {
            STATE.retryCount++;
            debugLog(`将在2秒后进行第${STATE.retryCount}次重试`);

            setTimeout(() => {
                loadAnalysisResult();
            }, 2000);
        } else {
            debugLog('达到最大重试次数，放弃加载', 'warn');
            STATE.retryCount = 0;
        }
    }

    // 更新分析结果内容
    function updateAnalysisContent(content) {
        const contentElement = document.querySelector(CONFIG.selectors.analysisContent);
        if (contentElement) {
            contentElement.innerHTML = content;
            debugLog('分析结果内容已更新');

            // 更新进度条为100%
            updateProgressBar(100);

            // 更新状态徽章
            updateStatusBadge();
        } else {
            debugLog('找不到分析结果内容元素', 'warn');
        }
    }

    // 加载推理过程
    function loadReasoningContent() {
        if (!STATE.novelId || !STATE.dimension) {
            debugLog('缺少必要信息，无法加载推理过程', 'warn');
            return;
        }

        debugLog('开始加载推理过程');

        // 构建API URL
        let apiUrl;
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            apiUrl = `/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}/reasoning_content`;
        } else {
            apiUrl = `/api/novel/${STATE.novelId}/analysis/${STATE.dimension}/reasoning_content`;
        }

        // 添加时间戳防止缓存
        apiUrl += `?_=${Date.now()}`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到推理过程数据');

                // 提取推理过程内容
                let reasoningContent = '';
                if (data.reasoning_content) {
                    reasoningContent = data.reasoning_content;
                } else if (data.content) {
                    reasoningContent = data.content;
                }

                if (reasoningContent) {
                    // 更新推理过程显示
                    updateReasoningContent(reasoningContent);
                    debugLog('成功更新推理过程内容');
                } else {
                    debugLog('推理过程内容为空', 'warn');
                }
            })
            .catch(error => {
                debugLog(`加载推理过程出错: ${error.message}`, 'error');
            });
    }

    // 更新推理过程内容
    function updateReasoningContent(content) {
        const contentElement = document.querySelector(CONFIG.selectors.reasoningContent);
        if (contentElement) {
            contentElement.innerHTML = content;
            debugLog('推理过程内容已更新');
        } else {
            debugLog('找不到推理过程内容元素', 'warn');
        }
    }

    // 更新进度条
    function updateProgressBar(progress) {
        const progressBar = document.querySelector(CONFIG.selectors.progressBar);
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
            progressBar.textContent = `${progress}%`;
            debugLog(`进度条已更新为${progress}%`);
        }
    }

    // 更新状态徽章
    function updateStatusBadge() {
        const statusBadge = document.querySelector(CONFIG.selectors.statusBadge);
        if (statusBadge) {
            statusBadge.className = 'badge bg-success analysis-status-badge';
            statusBadge.textContent = '分析已完成';
            debugLog('状态徽章已更新为"分析已完成"');
        }
    }

    // 初始化
    function init() {
        debugLog('初始化分析结果显示修复脚本');

        // 获取页面信息
        if (getPageInfo()) {
            // 加载分析结果
            loadAnalysisResult();

            // 设置轮询定时器，每隔一段时间检查一次分析结果
            STATE.pollTimer = setInterval(() => {
                if (!STATE.isLoading) {
                    loadAnalysisResult();
                }
            }, CONFIG.pollInterval);
        }
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    console.log('[分析结果显示修复] 脚本加载完成');
})();
