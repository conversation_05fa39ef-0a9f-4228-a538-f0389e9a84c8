{% extends "v2/base.html" %}

{% block title %}{{ novel.title }} - {{ chapter.title or '第' + chapter.chapter_number|string + '章' }} - 九猫小说分析系统v2.0{% endblock %}

{% block extra_css %}
<style>
    .chapter-header {
        background-color: var(--primary-light);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    .dimension-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .dimension-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .dimension-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    .chapter-content {
        max-height: 500px;
        overflow-y: auto;
        white-space: pre-wrap;
        font-family: 'Microsoft YaHei', sans-serif;
        line-height: 1.7;
        padding: 1.5rem;
        background-color: var(--card-bg);
        border-radius: 0.5rem;
        border: 1px solid var(--border-color);
    }
</style>
{% endblock %}

{% block content %}
<!-- 返回按钮和标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="{{ url_for('v2.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回小说详情
        </a>
    </div>
    <div>
        <a href="{{ url_for('v2.chapters_summary', novel_id=novel.id) }}" class="btn btn-outline-primary">
            <i class="fas fa-list-alt me-2"></i>章节分析汇总
        </a>
    </div>
</div>

<!-- 章节标题 -->
<div class="chapter-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">{{ chapter.title or '第' + chapter.chapter_number|string + '章' }}</h1>
            <p class="mb-0">
                <span class="badge bg-primary me-2">{{ novel.title }}</span>
                {% if novel.author %}<span class="badge bg-secondary me-2">作者: {{ novel.author }}</span>{% endif %}
                <span class="badge bg-info">{{ chapter.word_count }} 字</span>
            </p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="d-flex justify-content-md-end align-items-center">
                <i class="fas fa-book-open fa-3x text-primary me-3"></i>
                <div>
                    <p class="mb-0 small">章节编号</p>
                    <p class="mb-0 fw-bold">第 {{ chapter.chapter_number }} 章</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 章节内容和分析维度 -->
<div class="row">
    <!-- 章节内容 -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-book me-2"></i>章节内容</h3>
            </div>
            <div class="card-body p-0">
                <div class="chapter-content">{{ chapter.content }}</div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary w-100" id="analyzeAllBtn">
                    <i class="fas fa-play-circle me-1"></i>分析所有维度
                </button>
            </div>
        </div>
    </div>

    <!-- 分析维度 -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-cubes me-2"></i>分析维度</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for dimension in dimensions %}
                        <div class="col-md-4 col-sm-6 mb-4">
                            <div class="card dimension-card h-100 {{ 'border-primary' if dimension.key in available_dimensions else '' }}" 
                                 data-url="{% if dimension.key in available_dimensions %}{{ url_for('v2.chapter_analysis', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}{% else %}#{% endif %}">
                                <div class="card-body text-center">
                                    <i class="{{ dimension.icon }} dimension-icon {{ 'text-primary' if dimension.key in available_dimensions else 'text-muted' }}"></i>
                                    <h4 class="card-title">{{ dimension.name }}</h4>
                                    <p class="card-text small">
                                        {% if dimension.key in available_dimensions %}
                                            已完成分析
                                        {% else %}
                                            未分析
                                        {% endif %}
                                    </p>
                                    {% if dimension.key in available_dimensions %}
                                        <a href="{{ url_for('v2.chapter_analysis', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" class="btn btn-sm btn-primary">
                                            查看结果
                                        </a>
                                    {% else %}
                                        <button class="btn btn-sm btn-outline-secondary analyze-dimension-btn" data-dimension="{{ dimension.key }}">
                                            开始分析
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析进度模态框 -->
<div class="modal fade" id="analysisProgressModal" tabindex="-1" aria-labelledby="analysisProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analysisProgressModalLabel">分析进度</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="analysisProgressContainer">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="analysisProgressBar"></div>
                    </div>
                    <div id="analysisStatus" class="mb-3">准备开始分析...</div>
                    <div id="analysisLog" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto;">
                        <div class="text-muted">等待分析开始...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="refreshAfterAnalysisBtn" style="display: none;">刷新页面</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 分析所有维度按钮
        document.getElementById('analyzeAllBtn').addEventListener('click', function() {
            startAnalysis(null); // null表示分析所有维度
        });

        // 单个维度分析按钮
        document.querySelectorAll('.analyze-dimension-btn').forEach(button => {
            button.addEventListener('click', function(event) {
                event.stopPropagation();
                const dimension = this.getAttribute('data-dimension');
                startAnalysis(dimension);
            });
        });

        // 维度卡片点击事件
        document.querySelectorAll('.dimension-card').forEach(card => {
            card.addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                if (url && url !== '#') {
                    window.location.href = url;
                }
            });
        });

        // 刷新页面按钮
        document.getElementById('refreshAfterAnalysisBtn').addEventListener('click', function() {
            location.reload();
        });

        // 开始分析函数
        function startAnalysis(dimension) {
            // 显示进度模态框
            const progressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
            progressModal.show();
            
            // 重置进度条和日志
            const progressBar = document.getElementById('analysisProgressBar');
            const statusElement = document.getElementById('analysisStatus');
            const logElement = document.getElementById('analysisLog');
            
            progressBar.style.width = '0%';
            statusElement.textContent = '准备开始分析...';
            logElement.innerHTML = '<div class="text-muted">等待分析开始...</div>';
            
            // 准备请求数据
            const requestData = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            };
            
            if (dimension) {
                // 单维度分析
                requestData.body = JSON.stringify({ dimension: dimension });
                logElement.innerHTML += '<div class="text-info">开始分析维度: ' + dimension + '</div>';
            } else {
                // 所有维度分析 - 发送空数组表示分析所有维度
                requestData.body = JSON.stringify({ dimensions: [] });
                logElement.innerHTML += '<div class="text-info">开始分析所有维度</div>';
            }
            
            // 发送API请求
            fetch('/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analyze', requestData)
                .then(function(response) {
                    // 检查响应状态
                    if (!response.ok) {
                        throw new Error('HTTP错误: ' + response.status + ' ' + response.statusText);
                    }
                    return response.json();
                })
                .then(function(data) {
                    if (data.success) {
                        progressBar.style.width = '100%';
                        statusElement.textContent = '分析完成';
                        logElement.innerHTML += '<div class="text-success">分析请求已成功处理</div>';
                        
                        // 显示刷新按钮
                        document.getElementById('refreshAfterAnalysisBtn').style.display = 'block';
                    } else {
                        progressBar.style.width = '0%';
                        statusElement.textContent = '分析失败';
                        logElement.innerHTML += '<div class="text-danger">分析请求失败: ' + (data.error || '未知错误') + '</div>';
                    }
                })
                .catch(function(error) {
                    progressBar.style.width = '0%';
                    statusElement.textContent = '发送请求时出错';
                    logElement.innerHTML += '<div class="text-danger">发送请求时出错: ' + error.message + '</div>';
                });
        }
    });
</script>
{% endblock %}
