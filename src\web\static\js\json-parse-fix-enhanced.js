/**
 * 九猫 - 增强版JSON.parse错误修复脚本
 * 这个脚本会在页面加载时立即执行，修复特定的JSON.parse错误
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('增强版JSON.parse修复脚本已加载');

    // 保存原始的JSON.parse方法
    var originalJSONParse = JSON.parse;

    // 重写JSON.parse方法
    JSON.parse = function(text, reviver) {
        try {
            // 尝试使用原始方法解析
            return originalJSONParse(text, reviver);
        } catch (e) {
            console.error('JSON.parse错误:', e.message);

            // 特别处理"missing ) after argument list"错误
            if (e.message.includes('missing ) after argument list')) {
                console.log('检测到缺少右括号错误，尝试修复');

                // 尝试添加右括号并重新解析
                try {
                    var fixedText = text + ')';
                    console.log('修复后尝试解析');
                    return originalJSONParse(fixedText, reviver);
                } catch (e2) {
                    console.error('第一次修复尝试失败:', e2.message);

                    // 尝试其他修复方法
                    try {
                        // 检查是否是JSON字符串中缺少右括号
                        if (text.includes('JSON.parse(')) {
                            var fixedText2 = text.replace(/JSON\.parse\((['"])(.+?)(?:\1\s*\))?$/, function(match, quote, content) {
                                return 'JSON.parse(' + quote + content + quote + ')';
                            });
                            console.log('尝试修复JSON.parse调用');
                            return eval(fixedText2); // 使用eval执行修复后的代码
                        }
                    } catch (e3) {
                        console.error('第二次修复尝试失败:', e3.message);
                    }
                }
            }

            // 如果所有修复尝试都失败，重新抛出原始错误
            throw e;
        }
    };

    // 安全地替换脚本内容
    function safeReplaceScript(script, newContent) {
        try {
            // 检查脚本是否仍在DOM中
            if (!script.parentNode) {
                console.warn('脚本元素已不在DOM中，无法替换');
                return false;
            }

            // 检查内容是否有效
            if (typeof newContent !== 'string') {
                console.warn('脚本内容无效，无法替换');
                return false;
            }

            // 检查内容是否包含可能导致问题的字符
            if (newContent.includes('</script') ||
                /[\u0000-\u0008\u000B\u000C\u000E-\u001F]/.test(newContent)) {
                console.warn('脚本内容包含可能导致解析问题的字符，进行特殊处理');

                // 替换可能导致问题的字符
                newContent = newContent
                    .replace(/\u0000-\u001F/g, '') // 移除控制字符
                    .replace(/<\/script/gi, '<\\/script'); // 转义script结束标签
            }

            // 创建新脚本
            var newScript = document.createElement('script');
            newScript.type = 'text/javascript';

            // 复制原始脚本的属性
            if (script.attributes) {
                for (var i = 0; i < script.attributes.length; i++) {
                    var attr = script.attributes[i];
                    if (attr.name !== 'type') { // 已经设置了type
                        newScript.setAttribute(attr.name, attr.value);
                    }
                }
            }

            // 设置新内容 - 使用更安全的方式
            try {
                // 使用文本节点而不是直接设置textContent
                var textNode = document.createTextNode(newContent);
                newScript.appendChild(textNode);
            } catch (e) {
                console.error('设置新脚本内容时出错:', e);

                // 尝试备用方法
                try {
                    // 使用innerHTML作为备用（不推荐，但可能在某些情况下有效）
                    newScript.innerHTML = newContent;
                } catch (e2) {
                    console.error('备用方法也失败:', e2);
                    return false;
                }
            }

            // 替换脚本 - 使用更安全的方式
            try {
                // 先插入新脚本，再移除旧脚本
                script.parentNode.insertBefore(newScript, script);

                // 延迟移除旧脚本，确保新脚本已加载
                setTimeout(function() {
                    try {
                        if (script.parentNode) {
                            script.parentNode.removeChild(script);
                        }
                    } catch (removeError) {
                        console.error('移除旧脚本时出错:', removeError);
                    }
                }, 0);

                return true;
            } catch (e) {
                console.error('替换脚本时出错:', e);
                return false;
            }
        } catch (e) {
            console.error('安全替换脚本时出错:', e);
            return false;
        }
    }

    // 修复JSON.parse调用
    function fixJsonParseCalls() {
        console.log('开始修复页面中的JSON.parse调用');

        try {
            // 查找所有内联脚本
            var scripts = document.querySelectorAll('script:not([src])');
            var scriptCount = scripts.length;
            console.log(`找到 ${scriptCount} 个内联脚本`);

            // 创建脚本数组的副本，避免在遍历过程中修改DOM导致的问题
            var scriptsArray = Array.prototype.slice.call(scripts);

            // 遍历所有脚本 - 使用for循环而不是forEach，更安全
            for (var i = 0; i < scriptsArray.length; i++) {
                var script = scriptsArray[i];
                var index = i;

                try {
                    if (!script.textContent) {
                        console.log(`脚本 #${index} 没有内容，跳过`);
                        continue;
                    }

                    // 检查是否包含JSON.parse
                    if (script.textContent.includes('JSON.parse(')) {
                        console.log(`脚本 #${index} 包含JSON.parse调用`);

                        // 修复脚本内容
                        var originalContent = script.textContent;
                        var fixedContent = originalContent;

                        // 修复一般的JSON.parse调用
                        fixedContent = fixedContent.replace(
                            /JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g,
                            function(match, quote, content) {
                                if (!match.endsWith(')')) {
                                    console.log(`修复脚本 #${index} 中缺少右括号的JSON.parse调用`);
                                    return 'JSON.parse(' + quote + content + quote + ')';
                                }
                                return match;
                            }
                        );

                        // 修复serverData = JSON.parse调用
                        fixedContent = fixedContent.replace(
                            /serverData\s*=\s*JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g,
                            function(match, quote, content) {
                                if (!match.endsWith(')')) {
                                    console.log(`修复脚本 #${index} 中缺少右括号的serverData = JSON.parse调用`);
                                    return 'serverData = JSON.parse(' + quote + content + quote + ')';
                                }
                                return match;
                            }
                        );

                        // 修复try-catch块中的JSON.parse调用
                        fixedContent = fixedContent.replace(
                            /try\s*\{\s*([^}]*JSON\.parse\([^)]*)\s*\}\s*catch/g,
                            function(match, tryBlock) {
                                if (tryBlock.includes('JSON.parse(') && !tryBlock.includes('JSON.parse(') && tryBlock.includes(')')) {
                                    console.log(`修复脚本 #${index} 中try块中缺少右括号的JSON.parse调用`);
                                    var fixedTryBlock = tryBlock.replace(
                                        /(JSON\.parse\([^)]*)/g,
                                        '$1)'
                                    );
                                    return 'try { ' + fixedTryBlock + ' } catch';
                                }
                                return match;
                            }
                        );

                        // 如果内容被修改，替换脚本
                        if (fixedContent !== originalContent) {
                            console.log(`替换脚本 #${index} 的内容`);
                            var success = safeReplaceScript(script, fixedContent);
                            if (success) {
                                console.log(`脚本 #${index} 替换成功`);
                            } else {
                                console.error(`脚本 #${index} 替换失败`);
                            }
                        } else {
                            console.log(`脚本 #${index} 不需要修复`);
                        }
                    }
                } catch (e) {
                    console.error(`处理脚本 #${index} 时出错:`, e);
                }
            }
        } catch (e) {
            console.error('修复JSON.parse调用时出错:', e);
        }
    }

    // 修复特定行的JSON.parse错误
    function fixSpecificLineError(lineNumber, columnNumber) {
        console.log(`尝试修复第 ${lineNumber} 行第 ${columnNumber} 列的错误`);

        try {
            // 获取页面中的所有脚本
            var scripts = document.querySelectorAll('script:not([src])');

            // 计算每个脚本的行数
            var scriptLines = [];
            var totalLines = 0;

            // 使用for循环而不是forEach，更安全
            for (var j = 0; j < scripts.length; j++) {
                var script = scripts[j];
                var index = j;

                if (script.textContent) {
                    var lines = script.textContent.split('\n');
                    var lineCount = lines.length;
                    scriptLines.push({
                        script: script,
                        startLine: totalLines + 1,
                        endLine: totalLines + lineCount,
                        lines: lines,
                        index: index
                    });
                    totalLines += lineCount;
                }
            }

            // 查找包含错误行的脚本
            for (var i = 0; i < scriptLines.length; i++) {
                var scriptInfo = scriptLines[i];
                if (lineNumber >= scriptInfo.startLine && lineNumber <= scriptInfo.endLine) {
                    console.log(`错误位于脚本 #${scriptInfo.index} 中的第 ${lineNumber - scriptInfo.startLine + 1} 行`);

                    // 获取错误行
                    var errorLineIndex = lineNumber - scriptInfo.startLine;
                    if (errorLineIndex < 0 || errorLineIndex >= scriptInfo.lines.length) {
                        console.error(`错误行索引超出范围: ${errorLineIndex}, 脚本行数: ${scriptInfo.lines.length}`);
                        continue;
                    }

                    var errorLine = scriptInfo.lines[errorLineIndex];

                    console.log(`错误行内容: ${errorLine}`);

                    // 修复错误行
                    if (errorLine.includes('JSON.parse(')) {
                        var fixedLine = errorLine.replace(
                            /JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g,
                            function(match, quote, content) {
                                if (!match.endsWith(')')) {
                                    console.log('修复缺少右括号的JSON.parse调用');
                                    return 'JSON.parse(' + quote + content + quote + ')';
                                }
                                return match;
                            }
                        );

                        if (fixedLine !== errorLine) {
                            // 替换错误行
                            scriptInfo.lines[errorLineIndex] = fixedLine;

                            // 重建脚本内容
                            var fixedContent = scriptInfo.lines.join('\n');

                            // 替换脚本
                            console.log('替换修复后的脚本');
                            var success = safeReplaceScript(scriptInfo.script, fixedContent);
                            if (success) {
                                console.log('脚本替换成功');
                                return true;
                            } else {
                                console.error('脚本替换失败');
                            }
                        }
                    } else {
                        // 如果错误行不包含JSON.parse，尝试检查前后几行
                        console.log('错误行不包含JSON.parse，检查附近行');

                        // 检查前后5行
                        var startCheck = Math.max(0, errorLineIndex - 5);
                        var endCheck = Math.min(scriptInfo.lines.length - 1, errorLineIndex + 5);

                        for (var k = startCheck; k <= endCheck; k++) {
                            var nearbyLine = scriptInfo.lines[k];
                            if (nearbyLine.includes('JSON.parse(')) {
                                console.log(`在附近第 ${k + scriptInfo.startLine} 行找到JSON.parse调用`);

                                var fixedNearbyLine = nearbyLine.replace(
                                    /JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g,
                                    function(match, quote, content) {
                                        if (!match.endsWith(')')) {
                                            console.log('修复缺少右括号的JSON.parse调用');
                                            return 'JSON.parse(' + quote + content + quote + ')';
                                        }
                                        return match;
                                    }
                                );

                                if (fixedNearbyLine !== nearbyLine) {
                                    // 替换附近行
                                    scriptInfo.lines[k] = fixedNearbyLine;

                                    // 重建脚本内容
                                    var fixedContent = scriptInfo.lines.join('\n');

                                    // 替换脚本
                                    console.log('替换修复后的脚本');
                                    var success = safeReplaceScript(scriptInfo.script, fixedContent);
                                    if (success) {
                                        console.log('脚本替换成功');
                                        return true;
                                    } else {
                                        console.error('脚本替换失败');
                                    }
                                }
                            }
                        }
                    }

                    break;
                }
            }

            return false;
        } catch (e) {
            console.error('修复特定行错误时出错:', e);
            return false;
        }
    }

    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        try {
            // 检查是否是JSON.parse相关错误
            var isJsonParseError = false;

            // 检查错误消息
            if (event.error && event.error.message) {
                // 检查各种可能的JSON.parse错误消息
                var errorMsg = event.error.message.toLowerCase();
                if (
                    errorMsg.includes('missing ) after argument list') ||
                    errorMsg.includes('unexpected token') ||
                    errorMsg.includes('unexpected end of json input') ||
                    errorMsg.includes('json.parse') ||
                    errorMsg.includes('syntax error')
                ) {
                    isJsonParseError = true;
                }
            }

            // 如果是JSON.parse相关错误，尝试修复
            if (isJsonParseError) {
                console.error('捕获到可能的JSON解析错误:', event.error ? event.error.message : '未知错误');
                console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);

                // 尝试修复特定行的错误
                var fixed = false;
                if (event.lineno && event.colno) {
                    fixed = fixSpecificLineError(event.lineno, event.colno);
                }

                if (!fixed) {
                    // 如果特定行修复失败，尝试修复所有JSON.parse调用
                    console.log('特定行修复失败，尝试修复所有JSON.parse调用');
                    fixJsonParseCalls();
                }

                // 阻止错误传播
                event.preventDefault();
                return false;
            }
        } catch (e) {
            console.error('处理错误事件时出错:', e);
        }
    }, true);

    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixJsonParseCalls);
    } else {
        // 如果页面已经加载完成，立即执行修复
        fixJsonParseCalls();
    }

    // 在页面完全加载后再次执行修复（包括图片和样式表）
    window.addEventListener('load', function() {
        setTimeout(fixJsonParseCalls, 1000); // 延迟1秒执行，确保所有动态脚本都已加载
    });
})();
