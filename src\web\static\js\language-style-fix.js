/**
 * 九猫 - 语言风格分析页面修复脚本
 * 专门修复language_style页面的变量重复声明和DOM操作错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('语言风格分析页面修复脚本已加载');
    
    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复语言风格分析页面');
        
        // 检查是否在语言风格分析页面
        const isLanguageStylePage = window.location.pathname.includes('language_style');
        if (!isLanguageStylePage) {
            console.log('不是语言风格分析页面，跳过修复');
            return;
        }
        
        console.log('检测到language_style页面，应用特殊修复');
        
        // 修复变量重复声明问题
        fixVariableDeclarations();
        
        // 修复DOM操作错误
        fixDOMOperations();
        
        // 修复图表初始化
        fixChartInitialization();
    });
    
    // 修复变量重复声明问题
    function fixVariableDeclarations() {
        try {
            console.log('修复变量重复声明问题');
            
            // 确保NineCats命名空间存在
            window.NineCats = window.NineCats || {};
            
            // 创建已声明变量的集合
            window.NineCats.declaredVariables = window.NineCats.declaredVariables || new Set();
            
            // 添加常见的全局变量
            const commonVariables = [
                'novelId', 'novelIdFromTemplate', 'novelTitle', 'novelTitleFromTemplate',
                'dimension', 'dimensionFromTemplate', 'analysisResult', 'analysisResultData',
                'chartData', 'chartOptions', 'chartConfig', 'chartInstance'
            ];
            
            // 将已存在于window对象上的变量添加到跟踪器中
            commonVariables.forEach(function(varName) {
                if (varName in window) {
                    window.NineCats.declaredVariables.add(varName);
                    console.log(`变量 ${varName} 已存在于全局作用域，已添加到跟踪器`);
                }
            });
            
            // 查找所有内联脚本
            const scripts = document.querySelectorAll('script:not([src])');
            let variableNameCounter = 0;
            
            console.log(`找到 ${scripts.length} 个内联脚本，开始检查变量声明`);
            
            // 处理每个脚本
            scripts.forEach(function(script, index) {
                const content = script.textContent || script.innerHTML || '';
                
                // 跳过空脚本或非常短的脚本
                if (content.length < 10) {
                    return;
                }
                
                // 检查是否包含novelId变量声明
                if (content.includes('const novelId =') || 
                    content.includes('let novelId =') || 
                    content.includes('var novelId =')) {
                    
                    console.log(`脚本 #${index} 中发现novelId变量声明`);
                    
                    // 检查是否已经声明过
                    if (window.NineCats.declaredVariables.has('novelId')) {
                        console.log(`novelId已经声明过，进行重命名`);
                        
                        try {
                            // 创建唯一变量名
                            const uniqueName = 'novelId_fixed_' + (++variableNameCounter);
                            
                            // 替换变量声明
                            let modifiedContent = content.replace(
                                /\b(const|let|var)\s+novelId\s*=/g, 
                                '$1 ' + uniqueName + ' ='
                            );
                            
                            // 同时替换该脚本中所有对novelId变量的引用
                            modifiedContent = modifiedContent.replace(
                                /\bnovelId\b(?!\s*=|_)/g, 
                                uniqueName
                            );
                            
                            // 创建新脚本并替换
                            const newScript = document.createElement('script');
                            newScript.textContent = modifiedContent;
                            
                            // 使用安全的替换方法
                            safeReplaceChild(script.parentNode, newScript, script);
                            
                            console.log(`脚本 #${index} 中的novelId变量已重命名为 ${uniqueName}`);
                        } catch (e) {
                            console.error(`修复脚本 #${index} 中的novelId变量时出错:`, e);
                        }
                    } else {
                        // 标记为已声明
                        window.NineCats.declaredVariables.add('novelId');
                        console.log(`标记novelId为已声明`);
                    }
                }
            });
        } catch (e) {
            console.error('修复变量重复声明问题时出错:', e);
        }
    }
    
    // 修复DOM操作错误
    function fixDOMOperations() {
        try {
            console.log('修复DOM操作错误');
            
            // 重写Node.prototype.replaceChild方法，添加错误处理
            const originalReplaceChild = Node.prototype.replaceChild;
            Node.prototype.replaceChild = function(newChild, oldChild) {
                try {
                    return originalReplaceChild.call(this, newChild, oldChild);
                } catch (e) {
                    console.error('replaceChild操作失败，尝试备用方法:', e);
                    
                    try {
                        // 备用方法：先插入新节点，再移除旧节点
                        this.insertBefore(newChild, oldChild);
                        this.removeChild(oldChild);
                        return newChild;
                    } catch (e2) {
                        console.error('备用替换方法也失败:', e2);
                        throw e2;
                    }
                }
            };
        } catch (e) {
            console.error('修复DOM操作错误时出错:', e);
        }
    }
    
    // 修复图表初始化
    function fixChartInitialization() {
        try {
            console.log('修复图表初始化');
            
            // 检查是否有图表容器
            const chartContainers = document.querySelectorAll('.chart-container');
            if (chartContainers.length === 0) {
                console.log('未找到图表容器，跳过图表修复');
                return;
            }
            
            console.log(`找到 ${chartContainers.length} 个图表容器`);
            
            // 检查Chart.js是否已加载
            if (typeof Chart === 'undefined') {
                console.log('Chart.js未加载，尝试加载');
                
                // 尝试先从本地加载
                const script = document.createElement('script');
                script.src = '/static/js/lib/chart.min.js';
                
                script.onload = function() {
                    console.log('Chart.js加载成功，初始化图表');
                    initializeCharts();
                };
                
                script.onerror = function() {
                    console.log('本地Chart.js加载失败，尝试从CDN加载');
                    script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js';
                    
                    script.onload = function() {
                        console.log('CDN Chart.js加载成功，初始化图表');
                        initializeCharts();
                    };
                    
                    script.onerror = function() {
                        console.error('无法加载Chart.js，图表初始化失败');
                    };
                };
                
                document.head.appendChild(script);
            } else {
                console.log('Chart.js已加载，初始化图表');
                initializeCharts();
            }
        } catch (e) {
            console.error('修复图表初始化时出错:', e);
        }
    }
    
    // 初始化图表
    function initializeCharts() {
        try {
            console.log('初始化图表');
            
            // 查找所有图表容器
            const chartContainers = document.querySelectorAll('.chart-container');
            
            // 处理每个图表容器
            chartContainers.forEach(function(container, index) {
                try {
                    console.log(`初始化图表 #${index}`);
                    
                    // 检查是否已经初始化
                    if (container.dataset.chartInitialized === 'true') {
                        console.log(`图表 #${index} 已经初始化，跳过`);
                        return;
                    }
                    
                    // 获取图表类型
                    const chartType = container.dataset.chartType || 'radar';
                    
                    // 创建画布
                    const canvas = document.createElement('canvas');
                    canvas.id = 'chart-' + index;
                    container.appendChild(canvas);
                    
                    // 创建图表配置
                    const config = {
                        type: chartType,
                        data: {
                            labels: ['词汇丰富度', '句式多样性', '修辞手法', '语言风格', '表达清晰度', '情感表达'],
                            datasets: [{
                                label: '语言风格分析指标',
                                data: [80, 75, 85, 90, 70, 85],
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'top',
                                }
                            }
                        }
                    };
                    
                    // 创建图表
                    new Chart(canvas, config);
                    
                    // 标记为已初始化
                    container.dataset.chartInitialized = 'true';
                    
                    console.log(`图表 #${index} 初始化成功`);
                } catch (e) {
                    console.error(`初始化图表 #${index} 时出错:`, e);
                }
            });
        } catch (e) {
            console.error('初始化图表时出错:', e);
        }
    }
    
    // 安全的替换子节点方法
    function safeReplaceChild(parent, newChild, oldChild) {
        if (!parent || !newChild || !oldChild) {
            console.error('safeReplaceChild: 参数无效', { parent, newChild, oldChild });
            return;
        }
        
        try {
            // 尝试直接替换
            parent.replaceChild(newChild, oldChild);
        } catch (e) {
            console.error('直接替换节点失败，尝试备用方法:', e);
            
            try {
                // 备用方法：先插入新节点，再移除旧节点
                parent.insertBefore(newChild, oldChild);
                parent.removeChild(oldChild);
            } catch (e2) {
                console.error('备用替换方法也失败:', e2);
                
                // 最后的尝试：使用innerHTML
                try {
                    const tempDiv = document.createElement('div');
                    tempDiv.appendChild(newChild);
                    const outerHTML = tempDiv.innerHTML;
                    
                    // 获取旧节点的位置
                    const nextSibling = oldChild.nextSibling;
                    parent.removeChild(oldChild);
                    
                    // 在相同位置插入新内容
                    if (nextSibling) {
                        parent.insertAdjacentHTML('beforebegin', outerHTML);
                    } else {
                        parent.insertAdjacentHTML('beforeend', outerHTML);
                    }
                } catch (e3) {
                    console.error('所有替换方法都失败:', e3);
                }
            }
        }
    }
})();
