<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 修复insertBefore错误的脚本 -->
    <script>
        // 立即执行函数，避免污染全局命名空间
        (function() {
            console.log('加载Unexpected identifier "$"修复脚本');

            // 保存原始的insertBefore方法
            const originalInsertBefore = Node.prototype.insertBefore;

            // 重写insertBefore方法
            Node.prototype.insertBefore = function(newNode, referenceNode) {
                try {
                    // 尝试使用原始方法
                    return originalInsertBefore.call(this, newNode, referenceNode);
                } catch (e) {
                    console.error('捕获到insertBefore错误:', e.message);

                    // 检查是否是特定的Unexpected identifier '$'错误
                    if (e.message && e.message.includes('Unexpected identifier')) {
                        console.log('检测到特定的insertBefore错误，尝试安全插入');

                        try {
                            // 检查是否是脚本元素
                            if (newNode.nodeType === Node.ELEMENT_NODE && newNode.tagName.toLowerCase() === 'script') {
                                console.log('检测到脚本元素，使用特殊处理');
                                
                                // 创建新的脚本元素
                                const safeScript = document.createElement('script');
                                
                                // 复制属性
                                for (let i = 0; i < newNode.attributes.length; i++) {
                                    const attr = newNode.attributes[i];
                                    safeScript.setAttribute(attr.name, attr.value);
                                }
                                
                                // 安全设置脚本内容 - 避免使用模板字符串中的${}
                                if (newNode.textContent) {
                                    // 替换所有的${为一个安全标记
                                    const safeContent = newNode.textContent.replace(/\$\{/g, '___DOLLAR_BRACE___');
                                    safeScript.textContent = safeContent;
                                }
                                
                                // 安全插入
                                if (referenceNode) {
                                    return originalInsertBefore.call(this, safeScript, referenceNode);
                                } else {
                                    return this.appendChild(safeScript);
                                }
                            } else {
                                // 对于非脚本元素，创建一个克隆
                                const safeNode = newNode.cloneNode(true);
                                
                                // 安全插入
                                if (referenceNode) {
                                    return originalInsertBefore.call(this, safeNode, referenceNode);
                                } else {
                                    return this.appendChild(safeNode);
                                }
                            }
                        } catch (e2) {
                            console.error('安全插入失败:', e2.message);
                            
                            // 最后尝试：直接添加到文档末尾
                            try {
                                return this.appendChild(newNode);
                            } catch (e3) {
                                console.error('所有方法都失败:', e3.message);
                                throw e; // 重新抛出原始错误
                            }
                        }
                    } else {
                        // 如果不是特定的错误，重新抛出
                        throw e;
                    }
                }
            };
            
            // 初始化完成标记
            window.__fixUnexpectedIdentifierLoaded = true;
            console.log('Unexpected identifier "$"修复脚本加载完成');
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第1章 她爬到了修仙界 - character_relationships 分析 - 九猫</title>

    <!-- 直接修复脚本 - 必须在所有其他脚本之前加载 -->
    <script src="/static/js/direct-fix.js" onerror="this.onerror=null;this.src='/direct-static/js/direct-fix.js';"></script>
    <!-- 高级JSON修复脚本 - 处理位置4476和6499的特定错误 -->
    <script src="/static/js/advanced-json-fix.js" onerror="this.onerror=null;this.src='/direct-static/js/advanced-json-fix.js';"></script>
    <!-- 专门修复novel/4页面的脚本 -->
    <script src="/static/js/novel-4-fix.js" onerror="this.onerror=null;this.src='/direct-static/js/novel-4-fix.js';"></script>
    <!-- 增强版character_relationships修复脚本 -->
    <script src="/static/js/character-relationships-fix-enhanced.js" onerror="this.onerror=null;this.src='/direct-static/js/character-relationships-fix-enhanced.js';"></script>
    <!-- JSON.parse错误修复脚本 -->
    <script src="/static/js/json-parse-fix.js" onerror="this.onerror=null;this.src='/direct-static/js/json-parse-fix.js';"></script>
    <!-- 未终止字符串修复脚本 -->
    <script src="/static/js/json-string-fix.js" onerror="this.onerror=null;this.src='/direct-static/js/json-string-fix.js';"></script>
    <!-- 分析结果修复脚本 -->
    <script src="/static/js/analysis-result-fix.js" onerror="this.onerror=null;this.src='/direct-static/js/analysis-result-fix.js';"></script>
    <!-- 人物关系分析页面修复脚本 -->
    <script src="/static/js/character_relationships_fix.js" onerror="this.onerror=null;this.src='/direct-static/js/character_relationships_fix.js';"></script>

    <!-- 尝试多种路径加载Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/lib/bootstrap.min.css" onerror="this.onerror=null;this.href='/direct-static/css/lib/bootstrap.min.css'">

    <!-- 如果本地加载失败，尝试CDN -->
    <script>
        // 检查Bootstrap CSS是否已加载
        setTimeout(function() {
            var bootstrapLoaded = false;
            var styles = document.styleSheets;
            for (var i = 0; i < styles.length; i++) {
                if (styles[i].href && styles[i].href.indexOf('bootstrap') > -1) {
                    bootstrapLoaded = true;
                    break;
                }
            }

            if (!bootstrapLoaded) {
                console.warn('本地Bootstrap CSS加载失败，尝试CDN');
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css';
                document.head.appendChild(link);
            }
        }, 1000);
    </script>

    <link rel="stylesheet" href="/static/css/style.css" onerror="this.onerror=null;this.href='/direct-static/css/style.css';console.error('样式文件加载失败')">

<style>
    .analysis-content {
        white-space: pre-wrap;
        line-height: 1.6;
    }

    .metadata-item {
        margin-bottom: 0.5rem;
    }

    .visualization-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }
    
    /* 分析过程记录样式 */
    .process-log {
        font-family: monospace;
        white-space: pre-wrap;
        line-height: 1.4;
    }
    
    .log-entry {
        padding: 2px 0;
        border-bottom: 1px dotted #eee;
    }
    
    .log-time {
        color: #666;
    }
    
    .log-level {
        font-weight: bold;
        margin-right: 4px;
    }
    
    .process-steps {
        padding-left: 20px;
    }
    
    .process-step {
        margin-bottom: 12px;
    }
</style>


    <!-- 预加载核心JS资源，确保快速可用 -->
    <link rel="preload" href="/static/js/lib/jquery-3.6.0.min.js" as="script">
    <link rel="preload" href="/static/js/lib/bootstrap.bundle.min.js" as="script">
    <link rel="prefetch" href="/static/js/lib/chart.min.js" as="script">

    <!-- 备用预加载 -->
    <link rel="preload" href="/direct-static/js/lib/jquery-3.6.0.min.js" as="script">
    <link rel="preload" href="/direct-static/js/lib/bootstrap.bundle.min.js" as="script">
    <link rel="prefetch" href="/direct-static/js/lib/chart.min.js" as="script">
<script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';console.error('错误处理脚本加载失败，尝试备用路径')"></script>
    </head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <span class="fs-4">九猫</span>
                <small class="text-muted">小说文本分析系统</small>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/upload">上传小说</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">





<div class="row">
    <div class="col-md-12">
        <!-- 分析进度条 -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">分析进度</h5>
                    <div id="analysisStatus" class="badge badge-info px-3 py-2">加载中...</div>
                </div>
            </div>
            <div class="card-body">
                <div class="progress mb-3" style="height: 20px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>当前维度:</strong> <span id="currentDimension">character_relationships</span></p>
                        <p><strong>分块进度:</strong> <span id="blocksProgress">-</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>预计剩余时间:</strong> <span id="remainingTime">-</span></p>
                        <p><strong>预计完成时间:</strong> <span id="estimatedCompletionTime">-</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="card mb-4">
            <div class="card-header"><h5 class="card-title">分析日志</h5></div>
            <div class="card-body" id="logContainer" data-novel-id="4" style="background:#f8f9fa; height:200px; overflow-y:auto; font-family: monospace; font-size:0.9rem;"></div>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">首页</a></li>
                <li class="breadcrumb-item"><a href="/novel/4">第1章 她爬到了修仙界</a></li>
                <li class="breadcrumb-item active">character_relationships 分析</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <h1>character_relationships 分析</h1>
            <a href="/novel/4" class="btn btn-outline-secondary">
                返回小说页面
            </a>
        </div>

        <div class="row mt-4">
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析结果</h5>
                    </div>
                    <div class="card-body">
                        <div class="visualization-container">
                            <h6>分析可视化</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <canvas id="radarChart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <canvas id="barChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分析过程记录区域 -->
                        <div class="analysis-process-container mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">分析过程记录</h6>
                                <button class="btn btn-sm btn-outline-primary" id="toggleAnalysisProcess">展开/折叠</button>
                            </div>
                            <div id="analysisProcessContent" class="border rounded p-3" style="display: none; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 0.85rem; background-color: #f8f9fa;">
                                <div class="text-center text-muted my-3">正在加载分析过程记录...</div>
                            </div>
                        </div>

                        <div class="analysis-content markdown-content">
                            # 分析过程中出错

## 错误详情
分析维度 **character_relationships** 时遇到了问题。

## 错误信息
```
name 'stats_start' is not defined
```

## 建议操作
请尝试以下解决方法：
1. 刷新页面并重新尝试分析
2. 检查小说文本是否过长或包含特殊字符
3. 确认API连接正常

如果问题持续存在，请联系系统管理员。

                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">小说信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>标题：</strong> 第1章 她爬到了修仙界</p>
                        <p><strong>作者：</strong> 未知</p>
                        <p><strong>字数：</strong> 2643</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析元数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="metadata-item">
                            <strong>分析时间：</strong>
                            <span>2025-05-01 10:04</span>
                        </div>






                            <div class="metadata-item">
                                <strong>最后更新：</strong>
                                <span>2025-05-01 10:04</span>
                            </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">九猫小说文本分析系统 &copy; 2025</span>
            <p class="small text-muted">基于阿里云 DeepSeek R1 的小说深度分析工具</p>
        </div>
    </footer>

    <!-- 直接加载核心JS库 -->
    <script src="/static/js/lib/jquery-3.6.0.min.js" onerror="this.onerror=null;this.src='/direct-static/js/lib/jquery-3.6.0.min.js';console.error('jQuery加载失败，尝试备用路径')"></script>
    <script src="/static/js/lib/bootstrap.bundle.min.js" onerror="this.onerror=null;this.src='/direct-static/js/lib/bootstrap.bundle.min.js';console.error('Bootstrap JS加载失败，尝试备用路径')"></script>

    <!-- 按需加载Chart.js -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 只在需要时加载Chart.js
            if (document.querySelector('.chart-container') || document.querySelector('.analysis-chart')) {
                var chartScript = document.createElement('script');
                chartScript.src = "/static/js/lib/chart.min.js";
                chartScript.onerror = function() {
                    console.error('Chart.js加载失败，尝试备用路径');
                    this.onerror = null;
                    this.src = '/direct-static/js/lib/chart.min.js';
                };
                document.head.appendChild(chartScript);
                console.log('已加载Chart.js');

                // 如果页面上有初始化图表的函数，调用它
                chartScript.onload = function() {
                    if (typeof window.initializeCharts === 'function') {
                        setTimeout(function() {
                            window.initializeCharts();
                        }, 100);
                    }
                };
            }
        });
    </script>

    <!-- 项目主JS文件 -->
    <script src="/static/js/main.js" onerror="this.onerror=null;this.src='/direct-static/js/main.js';console.error('主JS文件加载失败，尝试备用路径')"></script>

    <!-- 人物关系分析模块 -->
    <script src="/static/js/character_relationships.js" onerror="this.onerror=null;this.src='/direct-static/js/character_relationships.js';console.error('人物关系分析模块加载失败，尝试备用路径')"></script>

    <!-- 初始化Bootstrap组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查Bootstrap是否已加载
            if (typeof bootstrap !== 'undefined') {
                console.log('初始化Bootstrap组件');
                try {
                    // 初始化工具提示
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    if (bootstrap.Tooltip) {
                    tooltipTriggerList.map(function(tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                        console.log('Tooltip 组件初始化成功');
                    } else {
                        console.warn('Bootstrap Tooltip 未定义，跳过初始化');
                    }

                    // 初始化弹出框
                    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                    if (bootstrap.Popover) {
                    popoverTriggerList.map(function(popoverTriggerEl) {
                        return new bootstrap.Popover(popoverTriggerEl);
                    });
                        console.log('Popover 组件初始化成功');
                    } else {
                        console.warn('Bootstrap Popover 未定义，跳过初始化');
                    }
                } catch (e) {
                    console.error('初始化Bootstrap组件时出错: ' + e);
                }
            } else {
                console.warn('Bootstrap未加载，跳过组件初始化');
            }
        });
    </script>

    <!-- 全局错误处理已在character_relationships.js中定义 -->


<script>
    // 全局变量
    const novelId = "4";
    const dimension = "character_relationships";

    // 实时进度更新函数
    function fetchAnalysisProgress() {
        return fetch('/api/analysis/progress?novel_id=' + novelId)
            .then(res => res.json())
            .then(data => {
                if (data.success && data.progress && data.progress[dimension]) {
                    return {
                        progress: data.progress[dimension],
                        isRunning: data.is_running
                    };
                }
                throw new Error('无法获取进度数据');
            });
    }

    function updateProgressUI() {
        fetchAnalysisProgress()
            .then(data => {
                const progressData = data.progress;
                const isRunning = data.isRunning;

                // 更新进度条
                const progress = progressData.progress || 0;
                const progressBar = document.getElementById('progressBar');
                progressBar.style.width = progress + '%';
                progressBar.setAttribute('aria-valuenow', progress);
                progressBar.textContent = progress + '%';

                // 更新分析状态
                const statusElement = document.getElementById('analysisStatus');
                if (progress === 100) {
                    statusElement.className = 'badge badge-success px-3 py-2';
                    statusElement.textContent = '已完成';
                    progressBar.className = 'progress-bar bg-success';
                } else if (progress < 0) {
                    statusElement.className = 'badge badge-danger px-3 py-2';
                    statusElement.textContent = '已终止';
                    progressBar.className = 'progress-bar bg-danger';
                } else if (isRunning) {
                    statusElement.className = 'badge badge-primary px-3 py-2';
                    statusElement.textContent = '分析中';
                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
                } else {
                    statusElement.className = 'badge badge-warning px-3 py-2';
                    statusElement.textContent = '已暂停';
                    progressBar.className = 'progress-bar bg-warning';
                }

                // 更新块进度
                if (progressData.blocks_progress) {
                    document.getElementById('blocksProgress').textContent = progressData.blocks_progress;
                }

                // 更新时间信息
                if (progressData.remaining_time) {
                    document.getElementById('remainingTime').textContent = progressData.remaining_time;
                }

                if (progressData.eta) {
                    document.getElementById('estimatedCompletionTime').textContent = progressData.eta;
                }

                // 如果仍在分析，继续轮询
                if (isRunning && progress < 100 && progress >= 0) {
                    setTimeout(updateProgressUI, 5000);
                }
            })
            .catch(err => {
                console.error('获取进度信息失败:', err);
                // 即使出错也继续尝试轮询
                setTimeout(updateProgressUI, 10000);
            });
    }

    // 实时日志拉取函数
    function fetchLogs(since) {
        const logContainer = document.getElementById('logContainer');
        const novelId = logContainer.dataset.novelId;
        let url = '/api/analysis/logs?novel_id=' + novelId + '&level=all&limit=200&dimension=' + dimension;
        if (since) url += '&since=' + since;
        return fetch(url).then(res=>res.json());
    }

    function initLogControls() {
        const logContainer = document.getElementById('logContainer');
        let lastTs = '';

        // 添加控制按钮
        const controlDiv = document.createElement('div');
        controlDiv.className = 'mb-2 d-flex justify-content-between';
        controlDiv.innerHTML = 
            '<div>' +
                '<button id="refreshLogsBtn" class="btn btn-sm btn-outline-primary">刷新日志</button>' +
                '<button id="clearLogsBtn" class="btn btn-sm btn-outline-secondary ml-2">清空</button>' +
            '</div>' +
            '<div>' +
                '<label class="mr-2"><input type="checkbox" id="autoScrollCheck" checked> 自动滚动</label>' +
                '<select id="logLevelFilter" class="form-control form-control-sm d-inline-block" style="width:auto">' +
                    '<option value="all">所有级别</option>' +
                    '<option value="info">信息</option>' +
                    '<option value="warning">警告</option>' +
                    '<option value="error">错误</option>' +
                '</select>' +
            '</div>';
        logContainer.parentNode.insertBefore(controlDiv, logContainer);

        // 控制事件处理
        document.getElementById('refreshLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '<div class="text-center text-muted my-3">正在加载日志...</div>';
            lastTs = '';
            updateLogs(true);
        });

        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '';
        });

        const autoScrollCheck = document.getElementById('autoScrollCheck');

        // 日志级别过滤
        const logLevelFilter = document.getElementById('logLevelFilter');
        logLevelFilter.addEventListener('change', () => {
            const level = logLevelFilter.value;
            Array.from(logContainer.children).forEach(line => {
                if (level === 'all') {
                    line.style.display = '';
                } else {
                    const logText = line.textContent;
                    line.style.display = logText.includes(level.toUpperCase()) ? '' : 'none';
                }
            });
        });

        function updateLogs(force = false) {
            fetchLogs(lastTs).then(data=>{
                if (data.success) {
                    // 如果是首次加载且没有日志，显示提示
                    if (lastTs === '' && data.logs.length === 0) {
                        logContainer.innerHTML = '<div class="text-center text-muted my-3">暂无分析日志，可能分析已完成或尚未开始</div>';
                    }

                    // 显示新日志
                    if (data.logs.length > 0) {
                        if (logContainer.children.length === 1 && logContainer.children[0].classList.contains('text-muted')) {
                            logContainer.innerHTML = '';
                        }

                        data.logs.forEach(log => {
                            const line = document.createElement('div');
                            line.className = log.level === 'error' ? 'text-danger' :
                                            log.level === 'warning' ? 'text-warning' : '';

                            // 增加分析进度信息的突出显示
                            let message = log.message;
                            if (log.important ||
                                message.includes('进度更新') ||
                                message.includes('分析块') ||
                                message.includes('API调用完成') ||
                                message.includes('%') ||
                                message.includes('处理时间') ||
                                message.includes('令牌使用量') ||
                                message.includes('费用') ||
                                message.includes('分析结果') ||
                                message.includes('完成度') ||
                                message.includes('耗时')) {
                                line.className += ' font-weight-bold text-primary';
                            }

                            line.textContent = '[' + log.timestamp + '] ' + log.level.toUpperCase() + ' - ' + message;

                            // 应用日志级别过滤
                            const level = logLevelFilter.value;
                            if (level !== 'all' && !log.level.includes(level)) {
                                line.style.display = 'none';
                            }

                            logContainer.appendChild(line);
                            lastTs = log.timestamp;
                        });

                        // 自动滚动到底部
                        if (autoScrollCheck.checked) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
                    }

                    // 判断是否需要继续轮询
                    if (data.is_running || force) {
                        setTimeout(updateLogs, 3000);  // 增加轮询间隔到3秒，减轻服务器负担
                    }
                }
            }).catch(err=>{
                console.error('获取日志失败', err);
                logContainer.innerHTML += '<div class="text-danger">获取日志失败: ' + err.message + '</div>';
                setTimeout(updateLogs, 5000);  // 发生错误时延长重试时间
            });
        }

        // 启动日志更新
        updateLogs();
    }

    // 加载Chart.js库
    function loadChartJS() {
        return new Promise((resolve, reject) => {
            // 检查Chart是否已经加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载');
                resolve();
                return;
            }

            // 尝试加载本地Chart.js
            var script = document.createElement('script');
            script.src = "/static/js/lib/chart.min.js";

            script.onload = function() {
                console.log('成功从本地加载Chart.js');
                resolve();
            };

            script.onerror = function() {
                // 如果本地加载失败，尝试备用路径
                console.warn('本地Chart.js加载失败，尝试备用路径');
                var backupScript = document.createElement('script');
                backupScript.src = "/direct-static/js/lib/chart.min.js";

                backupScript.onload = function() {
                    console.log('成功从备用路径加载Chart.js');
                    resolve();
                };

                backupScript.onerror = function() {
                    // 如果备用路径也失败，尝试CDN
                    console.warn('备用路径Chart.js加载失败，尝试CDN');
                    var cdnScript = document.createElement('script');
                    cdnScript.src = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";

                    cdnScript.onload = function() {
                        console.log('成功从CDN加载Chart.js');
                        resolve();
                    };

                    cdnScript.onerror = function() {
                        console.error('所有Chart.js加载尝试均失败');
                        reject(new Error('无法加载Chart.js'));
                    };

                    document.head.appendChild(cdnScript);
                };

                document.head.appendChild(backupScript);
            };

            document.head.appendChild(script);
        });
    }

    // 初始化图表
    function initCharts() {
        // 获取分析结果数据
        let resultMetadata = {};
        try {
            resultMetadata = JSON.parse('{"error": "name stats_start is not defined", "formatted_error": true}');
        } catch (e) {
            console.error('解析元数据JSON失败:', e.message);
            resultMetadata = {"error": "name stats_start is not defined", "formatted_error": true};
        }
        console.log('分析结果元数据:', resultMetadata);

        // 确保元数据是有效的对象
        if (!resultMetadata || typeof resultMetadata !== 'object') {
            console.warn('元数据不是有效的对象，尝试解析');
            try {
                if (typeof resultMetadata === 'string') {
                    resultMetadata = JSON.parse(resultMetadata);
                } else {
                    resultMetadata = {};
                }
            } catch (e) {
                console.error('解析元数据失败:', e);
                resultMetadata = {};
            }
        }

        // 从分析结果中提取数据 - 雷达图
        // 使用API统计数据构建雷达图，确保数据是真实的
        let radarLabels = ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'];
        let radarData = [];

        // 从元数据中获取真实数据
        if (resultMetadata && resultMetadata.processing_time) {
            radarData.push(Math.round(resultMetadata.processing_time * 100) / 100);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.chunk_count) {
            radarData.push(resultMetadata.chunk_count);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.api_calls) {
            radarData.push(resultMetadata.api_calls);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.tokens_used) {
            radarData.push(resultMetadata.tokens_used);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.cost) {
            radarData.push(Math.round(resultMetadata.cost * 10000) / 10000);
        } else {
            radarData.push(0);
        }

        // 如果有可视化数据，使用它
        if (resultMetadata && typeof resultMetadata === 'object' &&
            resultMetadata.visualization_data &&
            resultMetadata.visualization_data.radar) {
            const visualData = resultMetadata.visualization_data.radar;
            if (visualData.labels && Array.isArray(visualData.labels) && visualData.labels.length > 0) {
                radarLabels = visualData.labels;
            }
            if (visualData.data && Array.isArray(visualData.data) && visualData.data.length > 0) {
                radarData = visualData.data;
            }
            console.log('使用自定义雷达图数据:', radarLabels, radarData);
        } else {
            console.log('使用从元数据构建的雷达图数据:', radarLabels, radarData);
        }

        // 从分析结果中提取数据 - 柱状图
        // 构建更有意义的柱状图数据
        let barLabels = radarLabels; // 使用相同的标签
        let barData = radarData;     // 使用相同的数据

        // 如果有可视化数据，使用它
        if (resultMetadata && typeof resultMetadata === 'object' &&
            resultMetadata.visualization_data &&
            resultMetadata.visualization_data.bar) {
            const visualData = resultMetadata.visualization_data.bar;
            if (visualData.labels && Array.isArray(visualData.labels) && visualData.labels.length > 0) {
                barLabels = visualData.labels;
            }
            if (visualData.data && Array.isArray(visualData.data) && visualData.data.length > 0) {
                barData = visualData.data;
            }
            console.log('使用自定义柱状图数据:', barLabels, barData);
        } else {
            console.log('使用从元数据构建的柱状图数据:', barLabels, barData);
        }

        // 加载Chart.js并初始化图表
        loadChartJS().then(() => {
            // 初始化雷达图
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: radarLabels,
                    datasets: [{
                        label: 'character_relationships分析评分',
                        data: radarData,
                        fill: true,
                        backgroundColor: 'rgba(74, 107, 223, 0.2)',
                        borderColor: 'rgb(74, 107, 223)',
                        pointBackgroundColor: 'rgb(74, 107, 223)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(74, 107, 223)'
                    }]
                },
                options: {
                    elements: {
                        line: {
                            borderWidth: 3
                        }
                    },
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 初始化柱状图
            const barCtx = document.getElementById('barChart').getContext('2d');
            new Chart(barCtx, {
                type: 'bar',
                data: {
                    labels: barLabels,
                    datasets: [{
                        label: 'character_relationships分析指标',
                        data: barData,
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(255, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(255, 206, 86, 0.2)',
                            'rgba(153, 102, 255, 0.2)',
                            'rgba(255, 159, 64, 0.2)'
                        ],
                        borderColor: [
                            'rgba(75, 192, 192, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            console.log('图表初始化完成');
        }).catch(error => {
            console.error('图表初始化失败:', error);
            // 显示错误信息给用户
            document.querySelectorAll('.chart-container').forEach(container => {
                container.innerHTML = '<div class="alert alert-danger">图表加载失败，请刷新页面重试</div>';
            });
        });
    }

    // 获取分析过程记录的函数
    function fetchAnalysisProcess() {
        const analysisProcessContent = document.getElementById('analysisProcessContent');
        
        // 显示加载中
        analysisProcessContent.innerHTML = '<div class="text-center text-muted my-3">正在加载分析过程记录...</div>';
        
        // 构建API URL
        const url = '/api/analysis/process?novel_id=' + novelId + '&dimension=' + dimension;
        
        // 获取数据
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.process) {
                    // 处理分析过程数据
                    displayAnalysisProcess(data.process);
                } else {
                    throw new Error(data.message || '获取分析过程失败');
                }
            })
            .catch(error => {
                // 显示错误信息
                analysisProcessContent.innerHTML = 
                    '<div class="alert alert-warning">' +
                        '<i class="bi bi-exclamation-triangle-fill me-2"></i>' +
                        '获取分析过程记录失败: ' + error.message +
                        '<hr>' +
                        '<p class="mb-0">备选方案: <a href="#" id="fetchProcessFromLogs" class="alert-link">从日志提取分析过程</a></p>' +
                    '</div>';
                
                // 添加从日志提取过程的事件处理
                document.getElementById('fetchProcessFromLogs').addEventListener('click', function(e) {
                    e.preventDefault();
                    extractProcessFromLogs();
                });
            });
    }
    
    // 从日志中提取分析过程
    function extractProcessFromLogs() {
        const analysisProcessContent = document.getElementById('analysisProcessContent');
        analysisProcessContent.innerHTML = '<div class="text-center text-muted my-3">正在从日志提取分析过程...</div>';
        
        // 获取所有日志
        fetch('/api/analysis/logs?novel_id=' + novelId + '&level=all&limit=500&dimension=' + dimension)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.logs && data.logs.length > 0) {
                    // 过滤与分析过程相关的日志
                    const processLogs = data.logs.filter(log => 
                        log.message.includes('分析中') || 
                        log.message.includes('处理') || 
                        log.message.includes('计算') || 
                        log.message.includes('分块') || 
                        log.message.includes('API') || 
                        log.message.includes('完成') ||
                        log.message.includes('进度') ||
                        log.level === 'debug'
                    );
                    
                    if (processLogs.length > 0) {
                        // 构建分析过程HTML
                        let html = '<div class="process-log">';
                        processLogs.forEach(log => {
                            const logClass = 
                                log.level === 'error' ? 'text-danger' : 
                                log.level === 'warning' ? 'text-warning' : 
                                log.level === 'debug' ? 'text-muted' : '';
                            
                            html += '<div class="log-entry ' + logClass + '">' +
                                    '<span class="log-time">[' + log.timestamp + ']</span> ' +
                                    '<span class="log-level">' + log.level.toUpperCase() + ':</span> ' +
                                    '<span class="log-message">' + log.message + '</span>' +
                                    '</div>';
                        });
                        html += '</div>';
                        
                        analysisProcessContent.innerHTML = html;
                    } else {
                        analysisProcessContent.innerHTML = '<div class="alert alert-info">未找到与分析过程相关的日志记录</div>';
                    }
                } else {
                    analysisProcessContent.innerHTML = '<div class="alert alert-warning">未找到分析日志或日志为空</div>';
                }
            })
            .catch(error => {
                analysisProcessContent.innerHTML = '<div class="alert alert-danger">获取日志失败: ' + error.message + '</div>';
            });
    }
    
    // 显示分析过程
    function displayAnalysisProcess(processData) {
        const analysisProcessContent = document.getElementById('analysisProcessContent');
        
        // 检查是否为字符串，如果是则尝试解析JSON
        if (typeof processData === 'string') {
            try {
                processData = JSON.parse(processData);
            } catch (e) {
                // 如果不是有效的JSON，就当作纯文本处理
            }
        }
        
        // 根据数据类型显示不同内容
        if (typeof processData === 'object' && processData !== null) {
            // 对象类型数据
            if (Array.isArray(processData)) {
                // 数组类型 - 显示为步骤列表
                let html = '<ol class="process-steps">';
                processData.forEach((step, index) => {
                    html += '<li class="process-step">';
                    if (typeof step === 'object') {
                        html += '<strong>' + (step.title || '步骤 ' + (index + 1)) + '</strong><br>';
                        if (step.description) html += step.description + '<br>';
                        if (step.timestamp) html += '<small class="text-muted">时间: ' + step.timestamp + '</small><br>';
                        if (step.duration) html += '<small class="text-muted">耗时: ' + step.duration + '</small>';
                    } else {
                        html += step;
                    }
                    html += '</li>';
                });
                html += '</ol>';
                analysisProcessContent.innerHTML = html;
            } else {
                // 普通对象 - 显示为属性列表
                let html = '<dl class="row">';
                for (const [key, value] of Object.entries(processData)) {
                    html += '<dt class="col-sm-3">' + key + '</dt>';
                    html += '<dd class="col-sm-9">';
                    
                    if (typeof value === 'object' && value !== null) {
                        // 嵌套对象，显示为格式化的JSON
                        html += '<pre class="mb-0"><code>' + JSON.stringify(value, null, 2) + '</code></pre>';
                    } else {
                        // 简单值，直接显示
                        html += value;
                    }
                    
                    html += '</dd>';
                }
                html += '</dl>';
                analysisProcessContent.innerHTML = html;
            }
        } else {
            // 字符串类型数据 - 可能是文本或原始日志
            // 检测是否包含换行符
            if (typeof processData === 'string' && processData.includes('\n')) {
                // 多行文本，按行分割显示
                const lines = processData.split('\n');
                let html = '<div class="process-log">';
                lines.forEach(line => {
                    if (line.trim()) {
                        html += '<div class="log-entry">' + line + '</div>';
                    }
                });
                html += '</div>';
                analysisProcessContent.innerHTML = html;
            } else {
                // 简单文本
                analysisProcessContent.innerHTML = '<p>' + processData + '</p>';
            }
        }
    }

    // 主初始化函数
    document.addEventListener('DOMContentLoaded', function() {
        // 启动进度更新
        updateProgressUI();

        // 初始化日志控制
        initLogControls();

        // 初始化图表
        initCharts();

        // 获取分析过程记录
        fetchAnalysisProcess();
        
        // 分析过程记录的展开/折叠功能
        const toggleAnalysisProcessBtn = document.getElementById('toggleAnalysisProcess');
        const analysisProcessContent = document.getElementById('analysisProcessContent');
        
        toggleAnalysisProcessBtn.addEventListener('click', function() {
            if (analysisProcessContent.style.display === 'none') {
                analysisProcessContent.style.display = 'block';
                toggleAnalysisProcessBtn.textContent = '折叠';
            } else {
                analysisProcessContent.style.display = 'none';
                toggleAnalysisProcessBtn.textContent = '展开';
            }
        });
    });
</script>

</body>
</html>