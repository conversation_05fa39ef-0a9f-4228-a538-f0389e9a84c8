/**
 * 九猫 - 折叠功能样式修复
 * 确保折叠/展开功能的样式正确
 */

/* 折叠区域基本样式 */
.collapse {
    display: none;
}

.collapse.show {
    display: block;
}

/* 折叠按钮样式 */
[data-bs-toggle="collapse"],
[data-toggle="collapse"] {
    cursor: pointer;
    text-decoration: none;
}

/* 分析过程卡片样式 */
.analysis-process-card {
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.25rem;
}

.analysis-process-card .card-header {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.analysis-process-card .card-body {
    padding: 1.25rem;
}

/* 分析日志样式 */
.analysis-logs {
    font-family: monospace;
    font-size: 0.85rem;
    max-height: 400px;
    overflow-y: auto;
}

.log-entry {
    margin-bottom: 0.25rem;
}

/* 手动切换按钮样式 */
.manual-toggle-btn {
    margin-left: 0.5rem;
}

/* 展开/折叠所有按钮样式 */
.collapse-all-btn {
    margin-right: 0.5rem;
}

/* 确保Bootstrap 5兼容性 */
.btn-link {
    font-weight: 400;
    color: #007bff;
    text-decoration: none;
}

.btn-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* 修复Bootstrap 4和Bootstrap 5之间的差异 */
.card-header .btn-link {
    padding: 0;
    margin: 0;
    vertical-align: baseline;
}

/* 确保折叠区域内容正确显示 */
#analysisProcessCollapse .card-body,
#analysisLogsCollapse .card-body {
    max-height: 600px;
    overflow-y: auto;
}

/* 确保pre标签内容正确显示 */
pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

/* 确保分析内容正确显示 */
.analysis-content {
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 修复Bootstrap 5中的一些样式问题 */
.collapse:not(.show) {
    display: none;
}

.collapsing {
    height: 0;
    overflow: hidden;
    transition: height 0.35s ease;
}

/* 确保按钮在折叠区域中正确显示 */
.card-header button.btn-link {
    width: 100%;
    text-align: left;
    position: relative;
}

.card-header button.btn-link::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.35s ease;
}

.card-header button.btn-link[aria-expanded="true"]::after {
    transform: translateY(-50%) rotate(180deg);
}
