<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>scope</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="scope">scope</h1>
<span class="description">Scoped packages</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#description">Description</a></li><li><a href="#installing-scoped-packages">Installing scoped packages</a></li><li><a href="#requiring-scoped-packages">Requiring scoped packages</a></li><li><a href="#publishing-scoped-packages">Publishing scoped packages</a></li><ul><li><a href="#publishing-public-scoped-packages-to-the-primary-npm-registry">Publishing public scoped packages to the primary npm registry</a></li><li><a href="#publishing-private-scoped-packages-to-the-npm-registry">Publishing private scoped packages to the npm registry</a></li></ul><li><a href="#associating-a-scope-with-a-registry">Associating a scope with a registry</a></li><li><a href="#see-also">See also</a></li></ul></div>
</section>

<div id="_content"><h3 id="description">Description</h3>
<p>All npm packages have a name. Some package names also have a scope. A scope
follows the usual rules for package names (URL-safe characters, no leading dots
or underscores). When used in package names, scopes are preceded by an <code>@</code> symbol
and followed by a slash, e.g.</p>
<pre lang="bash"><code>@somescope/somepackagename
</code></pre>
<p>Scopes are a way of grouping related packages together, and also affect a few
things about the way npm treats the package.</p>
<p>Each npm user/organization has their own scope, and only you can add packages
in your scope. This means you don't have to worry about someone taking your
package name ahead of you. Thus it is also a good way to signal official packages
for organizations.</p>
<p>Scoped packages can be published and installed as of <code>npm@2</code> and are supported
by the primary npm registry. Unscoped packages can depend on scoped packages and
vice versa. The npm client is backwards-compatible with unscoped registries,
so it can be used to work with scoped and unscoped registries at the same time.</p>
<h3 id="installing-scoped-packages">Installing scoped packages</h3>
<p>Scoped packages are installed to a sub-folder of the regular installation
folder, e.g. if your other packages are installed in <code>node_modules/packagename</code>,
scoped modules will be installed in <code>node_modules/@myorg/packagename</code>. The scope
folder (<code>@myorg</code>) is simply the name of the scope preceded by an <code>@</code> symbol, and can
contain any number of scoped packages.</p>
<p>A scoped package is installed by referencing it by name, preceded by an
<code>@</code> symbol, in <code>npm install</code>:</p>
<pre lang="bash"><code>npm install @myorg/mypackage
</code></pre>
<p>Or in <code>package.json</code>:</p>
<pre lang="json"><code>"dependencies": {
  "@myorg/mypackage": "^1.3.0"
}
</code></pre>
<p>Note that if the <code>@</code> symbol is omitted, in either case, npm will instead attempt to
install from GitHub; see <a href="../commands/npm-install.html"><code>npm install</code></a>.</p>
<h3 id="requiring-scoped-packages">Requiring scoped packages</h3>
<p>Because scoped packages are installed into a scope folder, you have to
include the name of the scope when requiring them in your code, e.g.</p>
<pre lang="javascript"><code>require('@myorg/mypackage')
</code></pre>
<p>There is nothing special about the way Node treats scope folders. This
simply requires the <code>mypackage</code> module in the folder named <code>@myorg</code>.</p>
<h3 id="publishing-scoped-packages">Publishing scoped packages</h3>
<p>Scoped packages can be published from the CLI as of <code>npm@2</code> and can be
published to any registry that supports them, including the primary npm
registry.</p>
<p>(As of 2015-04-19, and with npm 2.0 or better, the primary npm registry
<strong>does</strong> support scoped packages.)</p>
<p>If you wish, you may associate a scope with a registry; see below.</p>
<h4 id="publishing-public-scoped-packages-to-the-primary-npm-registry">Publishing public scoped packages to the primary npm registry</h4>
<p>Publishing to a scope, you have two options:</p>
<ul>
<li>Publishing to your user scope (example: <code>@username/module</code>)</li>
<li>Publishing to an organization scope (example: <code>@org/module</code>)</li>
</ul>
<p>If publishing a public module to an organization scope, you must
first either create an organization with the name of the scope
that you'd like to publish to or be added to an existing organization
with the appropriate permisssions. For example, if you'd like to
publish to <code>@org</code>, you would  need to create the <code>org</code> organization
on npmjs.com prior to trying to publish.</p>
<p>Scoped packages are not public by default.  You will need to specify
<code>--access public</code> with the initial <code>npm publish</code> command.  This will publish
the package and set access to <code>public</code> as if you had run <code>npm access public</code>
after publishing.  You do not need to do this when publishing new versions of
an existing scoped package.</p>
<h4 id="publishing-private-scoped-packages-to-the-npm-registry">Publishing private scoped packages to the npm registry</h4>
<p>To publish a private scoped package to the npm registry, you must have
an <a href="https://docs.npmjs.com/private-modules/intro">npm Private Modules</a>
account.</p>
<p>You can then publish the module with <code>npm publish</code> or <code>npm publish --access restricted</code>, and it will be present in the npm registry, with
restricted access. You can then change the access permissions, if
desired, with <code>npm access</code> or on the npmjs.com website.</p>
<h3 id="associating-a-scope-with-a-registry">Associating a scope with a registry</h3>
<p>Scopes can be associated with a separate registry. This allows you to
seamlessly use a mix of packages from the primary npm registry and one or more
private registries, such as <a href="https://github.com/features/packages">GitHub Packages</a> or the open source <a href="https://verdaccio.org">Verdaccio</a>
project.</p>
<p>You can associate a scope with a registry at login, e.g.</p>
<pre lang="bash"><code>npm login --registry=http://reg.example.com --scope=@myco
</code></pre>
<p>Scopes have a many-to-one relationship with registries: one registry can
host multiple scopes, but a scope only ever points to one registry.</p>
<p>You can also associate a scope with a registry using <code>npm config</code>:</p>
<pre lang="bash"><code>npm config set @myco:registry http://reg.example.com
</code></pre>
<p>Once a scope is associated with a registry, any <code>npm install</code> for a package
with that scope will request packages from that registry instead. Any
<code>npm publish</code> for a package name that contains the scope will be published to
that registry instead.</p>
<h3 id="see-also">See also</h3>
<ul>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../commands/npm-publish.html">npm publish</a></li>
<li><a href="../commands/npm-access.html">npm access</a></li>
<li><a href="../using-npm/registry.html">npm registry</a></li>
</ul>
</div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/using-npm/scope.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>