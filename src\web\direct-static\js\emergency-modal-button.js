/**
 * 九猫 - 紧急模态框按钮脚本
 * 在页面上添加一个紧急按钮，用于手动触发模态框
 * 版本: 1.0.0
 */

(function() {
    console.log('[紧急模态框按钮] 脚本已加载');

    // 添加紧急按钮
    function addEmergencyButton() {
        console.log('[紧急模态框按钮] 添加紧急按钮');

        // 创建按钮容器
        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.bottom = '20px';
        container.style.right = '20px';
        container.style.zIndex = '9999';
        container.id = 'emergency-button-container';

        // 创建按钮
        const button = document.createElement('button');
        button.className = 'btn btn-danger';
        button.textContent = '打开分析选项';
        button.id = 'emergency-modal-button';
        button.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';

        // 添加事件监听器
        button.addEventListener('click', function() {
            console.log('[紧急模态框按钮] 点击紧急按钮');
            
            // 查找模态框
            const modal = document.getElementById('analyzeModal');
            if (!modal) {
                console.error('[紧急模态框按钮] 找不到分析模态框');
                alert('找不到分析模态框，请刷新页面重试');
                return;
            }
            
            // 直接显示模态框
            modal.style.display = 'block';
            modal.classList.add('show');
            document.body.classList.add('modal-open');
            
            // 创建背景遮罩
            let backdrop = document.querySelector('.modal-backdrop');
            if (!backdrop) {
                backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                document.body.appendChild(backdrop);
            }
            
            console.log('[紧急模态框按钮] 成功显示模态框');
            
            // 确保维度选择正确显示
            setTimeout(function() {
                if (typeof window.fixChapterAnalysisDimensions === 'function') {
                    window.fixChapterAnalysisDimensions();
                    console.log('[紧急模态框按钮] 调用维度修复函数');
                }
            }, 100);
        });

        // 添加到容器
        container.appendChild(button);
        
        // 添加到页面
        document.body.appendChild(container);
        
        console.log('[紧急模态框按钮] 紧急按钮已添加到页面');
    }

    // 初始化
    function init() {
        console.log('[紧急模态框按钮] 初始化中...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(addEmergencyButton, 1000);
            });
        } else {
            setTimeout(addEmergencyButton, 1000);
        }
        
        console.log('[紧急模态框按钮] 初始化完成');
    }

    // 初始化
    init();

    console.log('[紧急模态框按钮] 脚本加载完成');
})();
