/**
 * 综合错误修复脚本
 * 整合网络错误、静态文件404错误、API错误等所有错误处理
 */

(function() {
    'use strict';

    // 错误修复配置
    const ERROR_FIX_CONFIG = {
        // 网络重试配置
        network: {
            maxRetries: 3,
            retryDelay: 1000,
            backoffMultiplier: 2,
            timeoutMs: 10000
        },
        
        // 静态文件配置
        staticFiles: {
            fallbackMap: {
                'favicon.ico': '/static/favicon.ico',
                'bootstrap.bundle.min.js.map': '/static/js/bootstrap.bundle.min.js.map'
            },
            ignoredErrors: ['favicon.ico', '.map', 'sourcemap']
        },
        
        // API错误配置
        api: {
            retryableStatusCodes: [0, 408, 429, 500, 502, 503, 504],
            friendlyMessages: {
                0: '网络连接失败，请检查网络连接',
                404: '请求的资源不存在',
                408: '请求超时，请重试',
                429: '请求过于频繁，请稍后重试',
                500: '服务器内部错误',
                502: '网关错误',
                503: '服务暂时不可用',
                504: '网关超时'
            }
        }
    };

    // 全局错误状态
    let errorState = {
        networkFailures: 0,
        lastNetworkCheck: Date.now(),
        apiErrors: [],
        staticFileErrors: []
    };

    /**
     * 初始化综合错误修复
     */
    function initComprehensiveErrorFix() {
        console.log('[综合错误修复] 开始初始化...');
        
        // 设置全局错误处理
        setupGlobalErrorHandling();
        
        // 设置网络监控
        setupNetworkMonitoring();
        
        // 设置API错误处理
        setupAPIErrorHandling();
        
        // 设置静态文件错误处理
        setupStaticFileErrorHandling();
        
        // 修复现有的test.js错误
        fixTestJSErrors();
        
        // 创建错误监控面板
        createErrorMonitorPanel();
        
        console.log('[综合错误修复] 初始化完成');
    }

    /**
     * 设置全局错误处理
     */
    function setupGlobalErrorHandling() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', function(event) {
            console.warn('[综合错误修复] 未处理的Promise错误:', event.reason);
            
            // 如果是网络相关错误，尝试处理
            if (isNetworkError(event.reason)) {
                handleNetworkError(event.reason);
                event.preventDefault(); // 阻止默认的错误显示
            }
        });

        // 捕获全局JavaScript错误
        window.addEventListener('error', function(event) {
            const error = event.error || event.message;
            console.warn('[综合错误修复] 全局JavaScript错误:', error);
            
            // 记录错误但不阻止默认处理
            recordError('javascript', error);
        });

        // 捕获资源加载错误
        window.addEventListener('error', function(event) {
            if (event.target !== window) {
                const element = event.target;
                const src = element.src || element.href;
                
                if (src) {
                    console.warn('[综合错误修复] 资源加载失败:', src);
                    handleResourceLoadError(src, element);
                }
            }
        }, true);
    }

    /**
     * 设置网络监控
     */
    function setupNetworkMonitoring() {
        // 监听网络状态变化
        window.addEventListener('online', function() {
            errorState.networkFailures = 0;
            console.log('[综合错误修复] 网络连接已恢复');
            updateErrorPanel();
            
            // 尝试恢复中断的操作
            retryFailedOperations();
        });

        window.addEventListener('offline', function() {
            console.log('[综合错误修复] 网络连接已断开');
            updateErrorPanel();
        });

        // 定期检查网络状态
        setInterval(function() {
            if (!navigator.onLine && errorState.networkFailures === 0) {
                errorState.networkFailures = 1;
                updateErrorPanel();
            }
        }, 5000);
    }

    /**
     * 设置API错误处理
     */
    function setupAPIErrorHandling() {
        // 增强jQuery的AJAX错误处理
        if (window.jQuery) {
            $(document).ajaxError(function(event, xhr, settings, thrownError) {
                const error = {
                    url: settings.url,
                    status: xhr.status,
                    statusText: xhr.statusText,
                    error: thrownError,
                    timestamp: new Date()
                };
                
                console.warn('[综合错误修复] AJAX错误:', error);
                recordError('api', error);
                
                // 如果是可重试的错误，尝试重试
                if (ERROR_FIX_CONFIG.api.retryableStatusCodes.includes(xhr.status)) {
                    handleRetryableAPIError(settings, xhr);
                }
            });
        }
    }

    /**
     * 设置静态文件错误处理
     */
    function setupStaticFileErrorHandling() {
        // 拦截fetch请求
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            return originalFetch.apply(this, args)
                .then(response => {
                    if (!response.ok && response.status === 404) {
                        handleStaticFile404(args[0]);
                    }
                    return response;
                })
                .catch(error => {
                    if (isNetworkError(error)) {
                        handleNetworkError(error);
                    }
                    throw error;
                });
        };
    }

    /**
     * 修复test.js中的错误
     */
    function fixTestJSErrors() {
        // 如果存在pollAnalysisStatus函数，增强它
        if (window.pollAnalysisStatus) {
            const originalPollAnalysisStatus = window.pollAnalysisStatus;
            
            window.pollAnalysisStatus = function(taskId) {
                if (window.analysisTimer) {
                    clearTimeout(window.analysisTimer);
                }

                window.analysisTimer = setTimeout(function() {
                    $.ajax({
                        url: `/api/test/status/${taskId}`,
                        type: 'GET',
                        timeout: ERROR_FIX_CONFIG.network.timeoutMs,
                        success: function(response) {
                            // 重置网络错误计数
                            errorState.networkFailures = 0;
                            updateErrorPanel();
                            
                            // 调用原始的成功处理逻辑
                            handleAnalysisStatusSuccess(response, taskId);
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            errorState.networkFailures++;
                            
                            const error = {
                                url: `/api/test/status/${taskId}`,
                                status: xhr.status,
                                statusText: xhr.statusText,
                                error: errorThrown,
                                textStatus: textStatus
                            };
                            
                            console.warn('[综合错误修复] 分析状态获取失败:', error);
                            recordError('api', error);
                            
                            // 如果是网络错误且还有重试机会，重试
                            if (isRetryableError(xhr.status, textStatus) && errorState.networkFailures <= ERROR_FIX_CONFIG.network.maxRetries) {
                                const delay = ERROR_FIX_CONFIG.network.retryDelay * Math.pow(ERROR_FIX_CONFIG.network.backoffMultiplier, errorState.networkFailures - 1);
                                
                                console.log(`[综合错误修复] 第 ${errorState.networkFailures} 次重试，${delay}ms 后重试...`);
                                
                                setTimeout(function() {
                                    window.pollAnalysisStatus(taskId);
                                }, delay);
                                
                                return;
                            }
                            
                            // 重试次数用完，显示友好错误信息
                            const friendlyMessage = getFriendlyErrorMessage(xhr.status, textStatus);
                            handleAnalysisStatusError(friendlyMessage);
                            
                            updateErrorPanel();
                        }
                    });
                }, 2000);
            };
        }
    }

    /**
     * 处理分析状态成功响应
     */
    function handleAnalysisStatusSuccess(response, taskId) {
        if (response.success) {
            const progress = response.progress || 0;
            $('.analysis-progress').css('width', `${progress}%`);

            let statusText = response.status || '正在分析中...';
            let formattedStatus = statusText;

            if (statusText.includes(': ')) {
                const parts = statusText.split(': ');
                if (parts.length === 2) {
                    if (statusText.includes('字')) {
                        formattedStatus = `${parts[0]}: <span class="badge badge-info">${parts[1]}</span>`;
                    } else {
                        formattedStatus = `${parts[0]}: <span class="badge badge-primary">${parts[1]}</span>`;
                    }
                }
            }

            $('#analysisStatus').html(formattedStatus);

            if (progress < 30) {
                $('.analysis-progress').removeClass('bg-success bg-info').addClass('bg-warning');
            } else if (progress < 70) {
                $('.analysis-progress').removeClass('bg-success bg-warning').addClass('bg-info');
            } else {
                $('.analysis-progress').removeClass('bg-info bg-warning').addClass('bg-success');
            }

            if (window.addLogEntry) {
                window.addLogEntry('info', `分析进度: ${progress}%, 状态: ${statusText}`);
            }

            if (response.completed) {
                window.analysisInProgress = false;
                const duration = Math.round((new Date() - window.analysisStartTime) / 1000);

                if (response.error) {
                    if (window.addLogEntry) {
                        window.addLogEntry('error', `分析写作失败: ${response.error}，耗时: ${duration}秒`);
                    }
                    $('#startAnalysisBtn').html('<i class="fas fa-exclamation-triangle mr-1"></i> 处理失败').prop('disabled', false);
                    $('#analysisStatus').text('分析写作失败');
                    $('.analysis-progress').css('width', '100%').removeClass('progress-bar-animated').addClass('bg-danger');
                } else {
                    let completionMessage = `分析写作完成，耗时: ${duration}秒`;
                    if (response.results && response.results.generated_novel_id) {
                        completionMessage += `，生成的内容已保存到内容仓库，ID: ${response.results.generated_novel_id}`;
                    }
                    if (window.addLogEntry) {
                        window.addLogEntry('info', completionMessage);
                    }

                    $('#startAnalysisBtn').html('<i class="fas fa-check mr-1"></i> 处理完成').prop('disabled', false);
                    $('#analysisStatus').text('分析写作已完成');
                    $('.analysis-progress').css('width', '100%').removeClass('progress-bar-animated');

                    if (window.displayResults) {
                        window.displayResults(response.results);
                    }
                }
            } else {
                window.pollAnalysisStatus(taskId);
            }
        } else {
            handleAnalysisStatusError(response.error || '获取分析状态失败');
        }
    }

    /**
     * 处理分析状态错误
     */
    function handleAnalysisStatusError(errorMessage) {
        window.analysisInProgress = false;
        
        if (window.addLogEntry) {
            window.addLogEntry('error', `获取分析状态时出错: ${errorMessage}`);
        }
        
        if (window.showError) {
            window.showError(errorMessage);
        }
        
        $('#startAnalysisBtn').html('<i class="fas fa-magic mr-1"></i> 开始一键分析写作').prop('disabled', false);
    }

    /**
     * 判断是否是网络错误
     */
    function isNetworkError(error) {
        if (typeof error === 'string') {
            return error.includes('ERR_NETWORK_CHANGED') || 
                   error.includes('Network Error') || 
                   error.includes('Failed to fetch');
        }
        
        if (error && error.message) {
            return error.message.includes('ERR_NETWORK_CHANGED') || 
                   error.message.includes('Network Error') || 
                   error.message.includes('Failed to fetch');
        }
        
        return false;
    }

    /**
     * 判断是否是可重试的错误
     */
    function isRetryableError(status, textStatus) {
        return ERROR_FIX_CONFIG.api.retryableStatusCodes.includes(status) || 
               textStatus === 'timeout' || 
               textStatus === 'error';
    }

    /**
     * 获取友好的错误信息
     */
    function getFriendlyErrorMessage(status, textStatus) {
        if (ERROR_FIX_CONFIG.api.friendlyMessages[status]) {
            return ERROR_FIX_CONFIG.api.friendlyMessages[status];
        }
        
        if (textStatus === 'timeout') {
            return '请求超时，请检查网络连接后重试';
        }
        
        if (textStatus === 'error' && status === 0) {
            return '网络连接失败，请检查网络连接';
        }
        
        return `请求失败 (${status}): ${textStatus}`;
    }

    /**
     * 处理网络错误
     */
    function handleNetworkError(error) {
        errorState.networkFailures++;
        console.warn('[综合错误修复] 网络错误:', error);
        updateErrorPanel();
    }

    /**
     * 处理静态文件404错误
     */
    function handleStaticFile404(url) {
        try {
            const urlObj = new URL(url, window.location.origin);
            const filename = urlObj.pathname.split('/').pop();
            
            const fallbackUrl = ERROR_FIX_CONFIG.staticFiles.fallbackMap[filename];
            if (fallbackUrl) {
                console.log(`[综合错误修复] 尝试加载备用静态文件: ${fallbackUrl}`);
                // 这里可以添加实际的备用文件加载逻辑
            }
            
            recordError('static', { url, filename });
        } catch (error) {
            console.error('[综合错误修复] 处理静态文件404错误时出错:', error);
        }
    }

    /**
     * 处理资源加载错误
     */
    function handleResourceLoadError(src, element) {
        recordError('resource', { src, element: element.tagName });
        
        // 如果是图片，可以设置默认图片
        if (element.tagName === 'IMG') {
            element.style.display = 'none';
        }
    }

    /**
     * 记录错误
     */
    function recordError(type, error) {
        const errorRecord = {
            type,
            error,
            timestamp: new Date(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        switch (type) {
            case 'api':
                errorState.apiErrors.push(errorRecord);
                break;
            case 'static':
                errorState.staticFileErrors.push(errorRecord);
                break;
            default:
                console.log('[综合错误修复] 记录错误:', errorRecord);
        }
        
        // 限制错误记录数量
        if (errorState.apiErrors.length > 50) {
            errorState.apiErrors = errorState.apiErrors.slice(-25);
        }
        if (errorState.staticFileErrors.length > 50) {
            errorState.staticFileErrors = errorState.staticFileErrors.slice(-25);
        }
        
        updateErrorPanel();
    }

    /**
     * 重试失败的操作
     */
    function retryFailedOperations() {
        // 如果有正在进行的分析，尝试恢复
        if (window.analysisInProgress && window.currentTaskId) {
            console.log('[综合错误修复] 网络恢复，尝试恢复分析状态检查');
            setTimeout(function() {
                if (window.pollAnalysisStatus) {
                    window.pollAnalysisStatus(window.currentTaskId);
                }
            }, 1000);
        }
    }

    /**
     * 创建错误监控面板
     */
    function createErrorMonitorPanel() {
        const panel = $(`
            <div id="errorMonitorPanel" style="
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 9999;
                background: rgba(0,0,0,0.9);
                color: white;
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
                max-width: 300px;
                display: none;
            ">
                <div style="font-weight: bold; margin-bottom: 5px;">系统状态监控</div>
                <div id="networkStatus">网络状态: <span id="networkStatusText">检查中...</span></div>
                <div id="apiErrorCount">API错误: <span>0</span></div>
                <div id="staticErrorCount">静态文件错误: <span>0</span></div>
                <button id="toggleErrorPanel" style="
                    background: #dc3545;
                    color: white;
                    border: none;
                    padding: 2px 6px;
                    border-radius: 2px;
                    font-size: 10px;
                    margin-top: 5px;
                ">隐藏</button>
            </div>
        `);
        
        $('body').append(panel);
        
        $('#toggleErrorPanel').click(function() {
            $('#errorMonitorPanel').hide();
        });
        
        // 双击显示详细错误信息
        $('#errorMonitorPanel').dblclick(function() {
            showDetailedErrorInfo();
        });
        
        updateErrorPanel();
    }

    /**
     * 更新错误监控面板
     */
    function updateErrorPanel() {
        const $panel = $('#errorMonitorPanel');
        if ($panel.length === 0) return;
        
        // 更新网络状态
        const networkText = navigator.onLine ? 
            (errorState.networkFailures > 0 ? `不稳定 (${errorState.networkFailures} 次失败)` : '正常') : 
            '离线';
        $('#networkStatusText').text(networkText);
        
        // 更新错误计数
        $('#apiErrorCount span').text(errorState.apiErrors.length);
        $('#staticErrorCount span').text(errorState.staticFileErrors.length);
        
        // 决定是否显示面板
        const hasErrors = errorState.networkFailures > 0 || 
                         errorState.apiErrors.length > 0 || 
                         errorState.staticFileErrors.length > 0 ||
                         !navigator.onLine;
        
        if (hasErrors) {
            $panel.show();
        }
    }

    /**
     * 显示详细错误信息
     */
    function showDetailedErrorInfo() {
        const errorInfo = {
            network: {
                isOnline: navigator.onLine,
                failures: errorState.networkFailures
            },
            api: errorState.apiErrors.slice(-5), // 最近5个API错误
            static: errorState.staticFileErrors.slice(-5) // 最近5个静态文件错误
        };
        
        console.group('[综合错误修复] 详细错误信息');
        console.log('网络状态:', errorInfo.network);
        console.log('API错误:', errorInfo.api);
        console.log('静态文件错误:', errorInfo.static);
        console.groupEnd();
    }

    // 初始化
    $(document).ready(function() {
        console.log('[综合错误修复] 开始初始化综合错误修复系统...');
        initComprehensiveErrorFix();
    });

})();
