{"name": "promise-call-limit", "version": "1.0.1", "files": ["index.js"], "description": "Call an array of promise-returning functions, restricting concurrency to a specified limit.", "repository": {"type": "git", "url": "git+https://github.com/isaacs/promise-call-limit"}, "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"tap": "^14.10.6"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}