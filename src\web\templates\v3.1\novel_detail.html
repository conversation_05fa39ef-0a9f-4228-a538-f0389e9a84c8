{% extends "v3.1/base.html" %}

{% block title %}{{ novel.title }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .novel-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px var(--shadow-color);
        position: relative;
        overflow: hidden;
    }

    .novel-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .novel-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    }

    .novel-meta {
        position: relative;
        z-index: 1;
        opacity: 0.9;
    }

    .novel-stats {
        display: flex;
        gap: 2rem;
        margin-top: 1rem;
    }

    .novel-stat {
        display: flex;
        align-items: center;
    }

    .novel-stat-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        margin-right: 0.75rem;
    }

    .tab-content {
        padding: 1.5rem;
    }

    .dimension-card {
        transition: all 0.3s ease;
        height: 100%;
    }

    .dimension-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px var(--shadow-color);
    }

    .dimension-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .dimension-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .chapter-list {
        max-height: 600px;
        overflow-y: auto;
    }

    .chapter-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .chapter-item:hover {
        background-color: var(--shadow-color);
    }

    .chapter-number {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: var(--primary-light);
        color: var(--dark-color);
        font-weight: 600;
        margin-right: 1rem;
    }

    .chapter-title {
        flex: 1;
    }

    .chapter-analysis-count {
        margin-right: 1rem;
        color: var(--primary-color);
        font-weight: 600;
    }

    .novel-content {
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        line-height: 1.8;
        padding: 1rem;
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
    }

    .analysis-progress {
        height: 0.75rem;
        border-radius: 1rem;
        overflow: hidden;
    }
</style>
{% endblock %}

{% block content %}
<!-- 小说头部信息 -->
<div class="novel-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">{{ novel.title }}</h1>
            <div class="novel-meta">
                <p class="lead mb-2">
                    <i class="fas fa-user me-2"></i>{{ novel.author or '未知作者' }}
                </p>
                <div class="novel-stats">
                    <div class="novel-stat">
                        <div class="novel-stat-icon">
                            <i class="fas fa-font"></i>
                        </div>
                        <div>
                            <div>{{ novel.word_count|format_number }}</div>
                            <small>字数</small>
                        </div>
                    </div>
                    <div class="novel-stat">
                        <div class="novel-stat-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <div>
                            <div>{{ chapters|length }}</div>
                            <small>章节</small>
                        </div>
                    </div>
                    <div class="novel-stat">
                        <div class="novel-stat-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div>
                            <div>{{ available_dimensions|length }}/{{ dimensions|length }}</div>
                            <small>分析维度</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <button class="btn btn-light" id="startAnalysisModalBtn">
                    <i class="fas fa-search me-1"></i>开始分析
                </button>
                {% if can_set_as_template %}
                <button class="btn btn-light" id="setAsTemplateBtn">
                    <i class="fas fa-bookmark me-1"></i>设为参考蓝本
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-12">
        <ul class="nav nav-tabs" id="novelTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                    <i class="fas fa-info-circle me-1"></i><strong>概览</strong>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="dimensions-tab" data-bs-toggle="tab" data-bs-target="#dimensions" type="button" role="tab" aria-controls="dimensions" aria-selected="false">
                    <i class="fas fa-search me-1"></i><strong>整本书的分析维度</strong>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">
                    <i class="fas fa-book-open me-1"></i><strong>章节列表</strong>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="false">
                    <i class="fas fa-file-alt me-1"></i><strong>小说内容</strong>
                </button>
            </li>
        </ul>
        <div class="tab-content" id="novelTabContent">
            <!-- 概览标签页 -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card shadow-sm mb-4">
                            <div class="card-header">
                                <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>小说信息</h3>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h5>小说摘要</h5>
                                    <p>{{ novel.get_excerpt(500) }}</p>
                                </div>
                                <div class="mb-3">
                                    <h5>上传信息</h5>
                                    <p><strong>上传时间：</strong> {{ novel.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                                    <p><strong>文件名：</strong> {{ novel.get_file_name() }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card shadow-sm mb-4">
                            <div class="card-header">
                                <h3 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>分析进度</h3>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>整本书分析进度</span>
                                        <span>{{ available_dimensions|length }}/{{ dimensions|length }}</span>
                                    </div>
                                    <div class="progress analysis-progress">
                                        <div class="progress-bar bg-primary" role="progressbar" style="width: {{ (available_dimensions|length / dimensions|length * 100)|int }}%" aria-valuenow="{{ (available_dimensions|length / dimensions|length * 100)|int }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>章节分析进度</span>
                                        <span>
                                            {% set total_chapter_analyses = 0 %}
                                            {% set total_possible_analyses = chapters|length * dimensions|length %}
                                            {% for chapter in chapters %}
                                                {% set chapter_results = chapter_analysis_results.get(chapter.id, {}) %}
                                                {% set total_chapter_analyses = total_chapter_analyses + chapter_results|length %}
                                            {% endfor %}
                                            {{ total_chapter_analyses }}/{{ total_possible_analyses }}
                                        </span>
                                    </div>
                                    <div class="progress analysis-progress">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ (total_chapter_analyses / total_possible_analyses * 100)|int if total_possible_analyses > 0 else 0 }}%" aria-valuenow="{{ (total_chapter_analyses / total_possible_analyses * 100)|int if total_possible_analyses > 0 else 0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    {% if can_set_as_template %}
                                    所有分析已完成，可以将此小说设为参考蓝本。
                                    {% else %}
                                    完成所有维度的分析后，可以将此小说设为参考蓝本。
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析维度标签页 -->
            <div class="tab-pane fade" id="dimensions" role="tabpanel" aria-labelledby="dimensions-tab">
                <div class="row">
                    {% for dimension in dimensions %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card dimension-card shadow-sm">
                            <div class="card-body">
                                <div class="dimension-status">
                                    {% if dimension.key in available_dimensions %}
                                    <span class="badge bg-success">已完成</span>
                                    {% else %}
                                    <span class="badge bg-secondary">未分析</span>
                                    {% endif %}
                                </div>
                                <div class="text-center mb-3">
                                    <div class="dimension-icon text-primary">
                                        <i class="fas fa-{{ dimension.icon|default('search') }}"></i>
                                    </div>
                                    <h4>{{ dimension.name }}</h4>
                                </div>
                                <p class="text-muted">{{ dimension.description|truncate(100) }}</p>
                                <div class="text-center mt-3">
                                    {% if dimension.key in available_dimensions %}
                                    <div class="btn-group w-100">
                                        <a href="{{ url_for('v3_1.analysis', novel_id=novel.id, dimension=dimension.key) }}" class="btn btn-primary">
                                            <i class="fas fa-eye me-1"></i>查看结果
                                        </a>
                                        <button class="btn btn-outline-primary reanalyze-btn" data-dimension="{{ dimension.key }}">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                    </div>
                                    {% else %}
                                    <button class="btn btn-outline-primary start-analysis-btn w-100" data-dimension="{{ dimension.key }}">
                                        <i class="fas fa-search me-1"></i>开始分析
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- 章节列表标签页 -->
            <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0"><i class="fas fa-book-open me-2"></i>章节列表</h3>
                            <div>
                                <a href="{{ url_for('v3_1.chapters_list', novel_id=novel.id) }}" class="btn btn-primary btn-sm me-2">
                                    <i class="fas fa-list me-1"></i>完整章节列表
                                </a>
                                <a href="{{ url_for('v3_1.chapters_summary', novel_id=novel.id) }}" class="btn btn-primary btn-sm me-2">
                                    <i class="fas fa-table me-1"></i>章节分析汇总
                                </a>
                                <a href="{{ url_for('v3_1.view_templates', novel_id=novel.id) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-cog me-1"></i>设定模板
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="chapter-list">
                            {% for chapter in chapters %}
                            <div class="chapter-item">
                                <div class="chapter-number">{{ chapter.chapter_number }}</div>
                                <div class="chapter-title">{{ chapter.title }}</div>
                                <div class="chapter-analysis-count">
                                    {% set chapter_results = chapter_analysis_results.get(chapter.id, {}) %}
                                    {{ chapter_results|length }}/{{ dimensions|length }}
                                </div>
                                <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye me-1"></i>查看
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 小说内容标签页 -->
            <div class="tab-pane fade" id="content" role="tabpanel" aria-labelledby="content-tab">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h3 class="card-title mb-0"><i class="fas fa-file-alt me-2"></i>小说内容</h3>
                    </div>
                    <div class="card-body">
                        <div class="novel-content">{{ novel.content }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 开始分析模态框 -->
<div class="modal fade" id="startAnalysisModal" tabindex="-1" aria-labelledby="startAnalysisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="startAnalysisModalLabel">选择分析维度</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    请选择要分析的维度，分析过程可能需要几分钟时间。
                </div>
                <div class="row">
                    {% for dimension in dimensions %}
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input class="form-check-input dimension-checkbox" type="checkbox" id="dimension_{{ dimension.key }}" value="{{ dimension.key }}" {% if dimension.key in available_dimensions %}disabled checked{% endif %}>
                            <label class="form-check-label" for="dimension_{{ dimension.key }}">
                                {{ dimension.name }}
                                {% if dimension.key in available_dimensions %}
                                <span class="badge bg-success ms-2">已完成</span>
                                {% endif %}
                            </label>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startAnalysisBtn">
                    <i class="fas fa-search me-1"></i>开始分析
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // 开始分析模态框按钮点击事件
        $('#startAnalysisModalBtn').click(function() {
            // 显示模态框
            $('#startAnalysisModal').modal('show');
        });

        // 开始分析按钮点击事件（维度卡片中的按钮）
        $('.start-analysis-btn').click(function() {
            const dimension = $(this).data('dimension');
            // 选中对应的复选框
            $('#dimension_' + dimension).prop('checked', true);
            // 显示模态框
            $('#startAnalysisModal').modal('show');
        });

        // 重新分析按钮点击事件
        $('.reanalyze-btn').click(function() {
            const dimension = $(this).data('dimension');

            if (confirm(`确定要重新分析"${dimension}"维度吗？现有分析结果将被覆盖。`)) {
                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在重新分析，请稍候...</h5>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);

                // 发送AJAX请求
                $.ajax({
                    url: `/v3.1/api/novel/{{ novel.id }}/reanalyze/${dimension}`,
                    type: 'POST',
                    contentType: 'application/json',
                    success: function(response) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        if (response.success) {
                            alert('重新分析已开始，请稍后刷新页面查看结果');
                            // 刷新页面
                            location.reload();
                        } else {
                            alert('重新分析失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        alert('重新分析失败: ' + error);
                    }
                });
            }
        });

        // 模态框中的开始分析按钮点击事件
        $('#startAnalysisBtn').click(function() {
            // 获取选中的维度
            const dimensions = [];
            $('.dimension-checkbox:checked:not(:disabled)').each(function() {
                dimensions.push($(this).val());
            });

            if (dimensions.length === 0) {
                alert('请至少选择一个未分析的维度');
                return;
            }

            // 关闭模态框
            $('#startAnalysisModal').modal('hide');

            // 显示加载提示
            const loadingHtml = `
                <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                    <div class="card p-4 text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h5>正在开始分析，请稍候...</h5>
                    </div>
                </div>
            `;
            $('body').append(loadingHtml);

            // 发送AJAX请求
            $.ajax({
                url: '/v3.1/api/start_analysis',
                type: 'POST',
                data: JSON.stringify({
                    novel_id: {{ novel.id }},
                    dimensions: dimensions
                }),
                contentType: 'application/json',
                success: function(response) {
                    // 移除加载提示
                    $('#loadingOverlay').remove();

                    if (response.success) {
                        alert('分析已开始，请稍后刷新页面查看结果');
                        // 刷新页面
                        location.reload();
                    } else {
                        alert('开始分析失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    // 移除加载提示
                    $('#loadingOverlay').remove();

                    alert('开始分析失败: ' + error);
                }
            });
        });

        // 设为参考蓝本按钮点击事件
        $('#setAsTemplateBtn').click(function() {
            if (confirm('确定要将此小说设为参考蓝本吗？')) {
                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在设置参考蓝本，请稍候...</h5>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);

                // 发送AJAX请求
                $.ajax({
                    url: '/v3.1/api/novel/{{ novel.id }}/set_as_template',
                    type: 'POST',
                    data: JSON.stringify({
                        novel_id: {{ novel.id }}
                    }),
                    contentType: 'application/json',
                    success: function(response) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        if (response.success) {
                            alert('已成功设为参考蓝本');
                            // 添加时间戳参数，防止浏览器缓存
                            const timestamp = new Date().getTime();
                            // 重定向到参考蓝本页面并设置参数强制刷新
                            window.location.href = `{{ url_for('v3_1.reference_templates') }}?_=${timestamp}&force_refresh=true`;
                        } else {
                            alert('设置参考蓝本失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        alert('设置参考蓝本失败: ' + error);
                    }
                });
            }
        });
    });
</script>
{% endblock %}