/**
 * 九猫系统 - 推理过程修复脚本
 * 版本: 1.0.0
 * 
 * 该脚本用于修复推理过程显示错误的问题，确保显示真正的推理过程而不是分析结果
 * 这是一个紧急修复，直接修改DOM，不依赖其他脚本
 */

(function() {
    console.log('[九猫紧急修复] 推理过程修复脚本已启动');

    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {}, settings: {} };

    // 如果已经运行过，不再重复执行
    if (window.__nineCatsFixes.loaded['reasoning-content-fix']) {
        console.log('[九猫紧急修复] 推理过程修复脚本已经运行过，跳过');
        return;
    }

    // 配置
    const CONFIG = {
        // 是否启用调试日志
        enableDebug: true,
        // 是否启用自动修复
        enableAutoFix: true,
        // 自动修复延迟（毫秒）
        autoFixDelay: 1000,
        // 是否启用强制修复
        enableForceFix: true,
        // 是否启用API修复
        enableApiFix: true,
        // 是否启用DOM修复
        enableDomFix: true,
        // 是否启用内容交换
        enableContentSwap: true
    };

    // 辅助函数：HTML转义
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    // 检查内容是否是分析结果
    function isAnalysisResult(content) {
        if (!content) return false;

        const resultMarkers = [
            '### **一、',
            '### 一、',
            '## 一、',
            '# 一、',
            '---\n### **',
            '## **',
            '# **',
            '### **总结',
            '## **总结',
            '# **总结',
            '句式变化分析',
            '重点探讨',
            '以下是对该小说文本的',
            '这部小说文本展现出了',
            '风格特征：',
            '主题架构：',
            '写作技法：',
            '人物塑造：',
            '文化符码：',
            '结构创新：',
            '节奏控制分析',
            '### 二、',
            '### 三、',
            '### 四、',
            '### 五、',
            '世界构建分析',
            '角色塑造分析',
            '情节发展分析',
            '语言风格分析',
            '主题探索分析',
            '叙事视角分析',
            '情感表达分析',
            '冲突设置分析',
            '节奏控制分析',
            '象征意象分析',
            '文化背景分析',
            '结构布局分析',
            '对话艺术分析',
            '开篇效果分析'
        ];

        for (const marker of resultMarkers) {
            if (content.includes(marker)) {
                return true;
            }
        }

        return false;
    }

    // 检查内容是否是推理过程
    function isReasoningProcess(content) {
        if (!content) return false;

        const processMarkers = [
            '好的，我现在要',
            '首先，我需要',
            '我将分析',
            '我需要分析',
            '嗯，用户让我',
            '下面我来分析',
            '让我来分析',
            '我会按照以下步骤',
            '我将按照以下步骤',
            '我将从以下几个方面',
            '我需要从以下几个方面',
            '我将逐步分析',
            '好的，我现在需要',
            '我需要通读一遍',
            '我需要先了解',
            '我会先阅读',
            '我先阅读一遍',
            '我需要分析用户提供的',
            '开始分析这段小说',
            '我需要全面考虑',
            '我得仔细阅读',
            '作为文学分析专家'
        ];

        for (const marker of processMarkers) {
            if (content.includes(marker)) {
                return true;
            }
        }

        return false;
    }

    // 从API响应中获取真正的推理过程
    function getReasoningFromApi(novelId, dimension) {
        return new Promise((resolve, reject) => {
            // 构建API URL
            const apiUrl = `/api/novel/${novelId}/analysis/${dimension}`;

            // 发送请求
            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data.success) {
                        throw new Error('API请求失败');
                    }

                    // 尝试从各种字段中获取推理过程
                    let reasoningContent = null;

                    // 1. 检查metadata.reasoning_content
                    if (data.metadata && data.metadata.reasoning_content && isReasoningProcess(data.metadata.reasoning_content)) {
                        reasoningContent = data.metadata.reasoning_content;
                        console.info('[九猫紧急修复] 从metadata.reasoning_content获取到推理过程');
                    }
                    // 2. 检查reasoning_content
                    else if (data.reasoning_content && isReasoningProcess(data.reasoning_content)) {
                        reasoningContent = data.reasoning_content;
                        console.info('[九猫紧急修复] 从reasoning_content获取到推理过程');
                    }
                    // 3. 检查content是否包含推理过程特征
                    else if (data.content && isReasoningProcess(data.content)) {
                        reasoningContent = data.content;
                        console.info('[九猫紧急修复] 从content获取到推理过程');
                    }

                    resolve(reasoningContent);
                })
                .catch(error => {
                    console.error('[九猫紧急修复] 获取推理过程失败:', error);
                    reject(error);
                });
        });
    }

    // 修复页面上的推理过程显示
    function fixReasoningContent() {
        // 查找所有推理过程容器
        const reasoningContainers = document.querySelectorAll('[data-reasoning-container]');
        if (reasoningContainers.length === 0) {
            console.info('[九猫紧急修复] 未找到推理过程容器');
            return;
        }

        console.info(`[九猫紧急修复] 找到 ${reasoningContainers.length} 个推理过程容器`);

        // 处理每个容器
        reasoningContainers.forEach(container => {
            const novelId = container.getAttribute('data-novel-id');
            const dimension = container.getAttribute('data-dimension');

            if (!novelId || !dimension) {
                console.warn('[九猫紧急修复] 容器缺少必要属性');
                return;
            }

            // 检查容器内容
            const preElements = container.querySelectorAll('pre.reasoning-text');
            if (preElements.length === 0) {
                console.info('[九猫紧急修复] 容器内未找到推理内容元素，可能尚未加载');
                return;
            }

            // 检查每个pre元素的内容
            preElements.forEach(preElement => {
                const content = preElement.textContent;

                // 检查内容是否是分析结果
                if (isAnalysisResult(content)) {
                    console.warn('[九猫紧急修复] 检测到推理过程容器中显示的是分析结果，尝试修复');

                    // 添加警告提示
                    const warningDiv = document.createElement('div');
                    warningDiv.className = 'alert alert-danger mb-3';
                    warningDiv.innerHTML = `
                        <p><i class="fas fa-exclamation-triangle"></i> <strong>错误：</strong> 系统错误地将分析结果显示为推理过程。</p>
                        <p class="small">正在尝试获取真正的推理过程，请稍候...</p>
                    `;
                    preElement.parentNode.insertBefore(warningDiv, preElement);

                    // 尝试从API获取真正的推理过程
                    if (CONFIG.enableApiFix) {
                        getReasoningFromApi(novelId, dimension)
                            .then(reasoningContent => {
                                if (reasoningContent) {
                                    // 更新内容
                                    preElement.textContent = reasoningContent;
                                    
                                    // 更新警告提示
                                    warningDiv.innerHTML = `
                                        <p><i class="fas fa-check-circle"></i> <strong>已修复：</strong> 成功获取到真正的推理过程。</p>
                                    `;
                                    warningDiv.className = 'alert alert-success mb-3';
                                } else {
                                    // 更新警告提示
                                    warningDiv.innerHTML = `
                                        <p><i class="fas fa-exclamation-triangle"></i> <strong>警告：</strong> 无法获取真正的推理过程。</p>
                                        <p class="small">系统可能未正确保存推理过程，或者API返回的数据格式有误。</p>
                                    `;
                                }
                            })
                            .catch(error => {
                                // 更新警告提示
                                warningDiv.innerHTML = `
                                    <p><i class="fas fa-exclamation-triangle"></i> <strong>错误：</strong> 获取推理过程时出错。</p>
                                    <p class="small">错误信息: ${error.message}</p>
                                `;
                            });
                    }
                }
            });
        });
    }

    // 尝试交换分析结果和推理过程
    function trySwapContentIfNeeded() {
        // 查找推理过程容器
        const reasoningContainers = document.querySelectorAll('[data-reasoning-container]');
        if (reasoningContainers.length === 0) return;

        // 查找分析结果容器
        const analysisContainers = document.querySelectorAll('#analysisContent, .analysis-content, .markdown-content');
        if (analysisContainers.length === 0) return;

        // 检查每个推理过程容器
        reasoningContainers.forEach(reasoningContainer => {
            const reasoningPreElements = reasoningContainer.querySelectorAll('pre.reasoning-text');
            if (reasoningPreElements.length === 0) return;

            // 获取推理过程内容
            const reasoningContent = reasoningPreElements[0].textContent;
            if (!reasoningContent) return;

            // 检查是否是分析结果
            if (isAnalysisResult(reasoningContent)) {
                console.warn('[九猫紧急修复] 推理过程容器中显示的是分析结果，尝试与分析结果容器交换内容');

                // 检查每个分析结果容器
                analysisContainers.forEach(analysisContainer => {
                    // 获取分析结果内容
                    const analysisContent = analysisContainer.innerHTML;
                    
                    // 检查是否包含推理过程特征
                    if (analysisContent && isReasoningProcess(analysisContent)) {
                        console.info('[九猫紧急修复] 分析结果容器中可能包含推理过程，尝试交换内容');
                        
                        // 交换内容
                        reasoningPreElements[0].textContent = analysisContent;
                        analysisContainer.innerHTML = reasoningContent;
                        
                        console.info('[九猫紧急修复] 成功交换内容');
                        
                        // 添加提示
                        const successDiv = document.createElement('div');
                        successDiv.className = 'alert alert-success mb-3';
                        successDiv.innerHTML = `
                            <p><i class="fas fa-check-circle"></i> <strong>已修复：</strong> 成功交换推理过程和分析结果。</p>
                        `;
                        reasoningContainer.insertBefore(successDiv, reasoningPreElements[0]);
                        
                        return;
                    }
                });
            }
        });
    }

    // 初始化修复
    function initFix() {
        console.info('[九猫紧急修复] 开始修复推理过程显示');
        
        // 立即执行一次修复
        fixReasoningContent();
        
        // 如果启用了内容交换，尝试交换内容
        if (CONFIG.enableContentSwap) {
            setTimeout(() => {
                trySwapContentIfNeeded();
            }, 1500);
        }
        
        // 如果启用了自动修复，定期检查并修复
        if (CONFIG.enableAutoFix) {
            setTimeout(() => {
                fixReasoningContent();
                
                // 如果启用了内容交换，再次尝试交换内容
                if (CONFIG.enableContentSwap) {
                    setTimeout(() => {
                        trySwapContentIfNeeded();
                    }, 500);
                }
            }, CONFIG.autoFixDelay);
        }
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initFix);
    } else {
        initFix();
    }

    // 标记为已加载
    window.__nineCatsFixes.loaded['reasoning-content-fix'] = true;

    console.log('[九猫紧急修复] 推理过程修复脚本初始化完成');
})();
