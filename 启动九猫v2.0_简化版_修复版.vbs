Option Explicit

' 九猫系统启动脚本 - 修复版
' 修复"开始分析所有维度"功能的400错误问题

' 声明变量
Dim shell, fso, pythonPath, pythonExe, appPath, tempFile, tempFilePath
Dim command, windowStyle, waitOnReturn, pythonFound, errorMsg
Dim fixScriptPath, fixHtmlPath, staticJsPath

' 创建对象
Set shell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' 设置路径
appPath = fso.GetAbsolutePathName(".")
pythonPath = "python"
pythonExe = "python.exe"
fixScriptPath = appPath & "\analyze_all_dimensions_fix.js"
fixHtmlPath = appPath & "\analyze_all_dimensions_fix.html"
staticJsPath = appPath & "\src\web\static\js\"

' 检查Python是否安装
pythonFound = False
On Error Resume Next
shell.Run "where " & pythonExe, 0, True
If Err.Number = 0 Then
    pythonFound = True
End If
On Error Goto 0

' 如果Python未找到，尝试使用py命令
If Not pythonFound Then
    On Error Resume Next
    shell.Run "where py", 0, True
    If Err.Number = 0 Then
        pythonPath = "py"
        pythonFound = True
    End If
    On Error Goto 0
End If

' 如果仍未找到Python，显示错误消息
If Not pythonFound Then
    MsgBox "Python未找到，请确保Python已安装并添加到PATH环境变量中", vbExclamation, "九猫系统启动失败"
    WScript.Quit
End If

' 创建临时批处理文件
tempFilePath = appPath & "\start_jiuma_temp.bat"
Set tempFile = fso.CreateTextFile(tempFilePath, True)

' 写入批处理文件内容
tempFile.WriteLine("@echo off")
tempFile.WriteLine("echo 正在启动九猫系统...")
tempFile.WriteLine("echo.")
tempFile.WriteLine("cd /d " & appPath)
tempFile.WriteLine("echo 检查修复脚本...")

' 检查修复脚本是否存在
tempFile.WriteLine("if not exist """ & fixScriptPath & """ (")
tempFile.WriteLine("    echo 修复脚本不存在，正在创建...")
tempFile.WriteLine("    copy """ & appPath & "\analyze_all_dimensions_fix.js"" """ & staticJsPath & """")
tempFile.WriteLine(")")

' 检查静态JS目录是否存在
tempFile.WriteLine("if not exist """ & staticJsPath & """ (")
tempFile.WriteLine("    echo 创建静态JS目录...")
tempFile.WriteLine("    mkdir """ & staticJsPath & """")
tempFile.WriteLine(")")

' 复制修复脚本到静态JS目录
tempFile.WriteLine("echo 复制修复脚本到静态JS目录...")
tempFile.WriteLine("copy """ & fixScriptPath & """ """ & staticJsPath & """")

' 启动九猫系统
tempFile.WriteLine("echo 启动九猫系统...")
tempFile.WriteLine("echo.")
tempFile.WriteLine("cd /d " & appPath)
tempFile.WriteLine(pythonPath & " -m src.web.v2_app")
tempFile.WriteLine("echo.")
tempFile.WriteLine("echo 九猫系统已关闭")
tempFile.WriteLine("echo.")
tempFile.WriteLine("pause")

' 关闭临时文件
tempFile.Close

' 设置窗口样式和等待选项
windowStyle = 1  ' 1 = 正常窗口
waitOnReturn = False

' 运行批处理文件
shell.Run tempFilePath, windowStyle, waitOnReturn

' 显示成功消息
MsgBox "九猫系统正在启动，请稍候..." & vbCrLf & vbCrLf & _
       "修复说明：" & vbCrLf & _
       "1. 已修复'开始分析所有维度'功能的400错误问题" & vbCrLf & _
       "2. 添加了更详细的分析进度显示" & vbCrLf & _
       "3. 优化了错误处理和用户体验", _
       vbInformation, "九猫系统启动成功"

' 清理对象
Set tempFile = Nothing
Set fso = Nothing
Set shell = Nothing
