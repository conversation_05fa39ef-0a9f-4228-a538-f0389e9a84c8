// 九猫小说分析系统 - 分析结果强制加载器
// 此模块用于确保在所有维度分析完成后能正确显示结果
// 版本: 1.0.0

(function() {
    // 配置
    const CONFIG = {
        debug: true,
        loadTimeout: 3000,
        retryDelay: 1500,
        maxRetries: 3,
        selectors: {
            analysisContent: '.analysis-content',
            previewContent: '.novel-preview',
            dimensionCards: '.dimension-card',
            loadingIndicator: '.result-loading-indicator',
            statusBadge: '.badge-analysis-status',
            analysisContainer: '.analysis-container',
            resultContainer: '.result-container'
        },
        dataAttributes: {
            novelId: 'data-novel-id',
            dimension: 'data-dimension',
            chapterId: 'data-chapter-id',
            loaded: 'data-loaded'
        }
    };

    // 状态
    const STATE = {
        novelId: null,
        isLoading: false,
        retryCount: 0,
        allDimensionsCompleted: false,
        resultCache: {}
    };

    // 初始化
    function init() {
        try {
            // 获取小说ID
            STATE.novelId = getNovelId();
            
            if (!STATE.novelId) {
                safeLog('无法获取小说ID，强制加载器将不会运行', 'warn');
                return;
            }

            safeLog(`九猫分析结果强制加载器已初始化，小说ID: ${STATE.novelId}`);
            
            // 检查是否所有维度都已完成分析
            STATE.allDimensionsCompleted = checkAllDimensionsCompleted();
            
            if (STATE.allDimensionsCompleted) {
                safeLog('检测到所有维度已完成分析，将在页面加载完成后强制加载结果');
                
                // 在DOM完全加载后执行
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', onDOMReady);
                } else {
                    onDOMReady();
                }
            }
            
            // 添加事件监听器
            addEventListeners();
        } catch (e) {
            safeLog(`初始化时出错: ${e.message}`, 'error');
        }
    }

    // DOM加载完成后执行
    function onDOMReady() {
        try {
            safeLog('DOM已加载完成，开始检查分析结果');
            
            // 检查分析内容是否为空
            const contentEmpty = isContentEmpty();
            
            if (contentEmpty) {
                safeLog('分析内容为空，将强制加载结果');
                setTimeout(forceLoadResults, 500);
            } else {
                safeLog('分析内容不为空，无需强制加载');
            }
        } catch (e) {
            safeLog(`DOM加载完成处理时出错: ${e.message}`, 'error');
        }
    }

    // 添加事件监听器
    function addEventListeners() {
        try {
            // 监听DOM变化，检测分析状态变化
            const observer = new MutationObserver(function(mutations) {
                for (const mutation of mutations) {
                    // 检查是否有状态变化
                    if (mutation.type === 'childList' && mutation.target.classList && 
                        (mutation.target.classList.contains('badge') || 
                         mutation.target.classList.contains('status-badge'))) {
                        
                        safeLog('检测到状态变化，检查是否所有维度已完成');
                        
                        // 重新检查是否所有维度都已完成
                        const wasCompleted = STATE.allDimensionsCompleted;
                        STATE.allDimensionsCompleted = checkAllDimensionsCompleted();
                        
                        // 如果状态从未完成变为完成，强制加载结果
                        if (!wasCompleted && STATE.allDimensionsCompleted) {
                            safeLog('检测到所有维度已从未完成变为完成，将强制加载结果');
                            setTimeout(forceLoadResults, 500);
                        }
                    }
                    
                    // 检查是否有内容变化
                    if (mutation.type === 'childList' && 
                        (mutation.target.classList.contains('analysis-content') || 
                         mutation.target.classList.contains('novel-preview'))) {
                        
                        // 检查内容是否为空
                        if (isContentEmpty()) {
                            safeLog('检测到内容为空，将强制加载结果');
                            setTimeout(forceLoadResults, 500);
                        }
                    }
                }
            });
            
            // 观察整个文档
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'data-status']
            });
            
            // 监听自定义事件
            document.addEventListener('analysis-completed', function(e) {
                safeLog('接收到分析完成事件，将强制加载结果');
                STATE.allDimensionsCompleted = true;
                setTimeout(forceLoadResults, 500);
            });
            
            // 监听页面显示事件（从其他页面返回）
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden && STATE.allDimensionsCompleted && isContentEmpty()) {
                    safeLog('页面变为可见，且所有维度已完成但内容为空，将强制加载结果');
                    setTimeout(forceLoadResults, 500);
                }
            });
        } catch (e) {
            safeLog(`添加事件监听器时出错: ${e.message}`, 'error');
        }
    }

    // 获取小说ID
    function getNovelId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
            
            // 从页面元素中获取
            const novelIdElement = document.querySelector(`[${CONFIG.dataAttributes.novelId}]`);
            if (novelIdElement) {
                return novelIdElement.getAttribute(CONFIG.dataAttributes.novelId);
            }
            
            return null;
        } catch (e) {
            safeLog(`获取小说ID时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 获取当前维度
    function getCurrentDimension() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/analysis\/([^\/]+)/);
            if (match && match[1]) {
                return match[1];
            }
            
            // 从页面元素中获取
            const dimensionElement = document.querySelector(`[${CONFIG.dataAttributes.dimension}]`);
            if (dimensionElement) {
                return dimensionElement.getAttribute(CONFIG.dataAttributes.dimension);
            }
            
            // 从当前活动的标签页获取
            const activeTab = document.querySelector('.nav-link.active');
            if (activeTab && activeTab.getAttribute('href')) {
                const tabMatch = activeTab.getAttribute('href').match(/#([^-]+)/);
                if (tabMatch && tabMatch[1]) {
                    return tabMatch[1];
                }
            }
            
            return null;
        } catch (e) {
            safeLog(`获取当前维度时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 获取章节ID
    function getChapterId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/chapter\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }

            // 从页面元素中获取
            const chapterIdElement = document.querySelector(`[${CONFIG.dataAttributes.chapterId}]`);
            if (chapterIdElement) {
                return chapterIdElement.getAttribute(CONFIG.dataAttributes.chapterId);
            }

            return null;
        } catch (e) {
            safeLog(`获取章节ID时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 检查是否在章节分析页面
    function isChapterAnalysisPage() {
        return window.location.pathname.includes('/chapter/') && window.location.pathname.includes('/analysis/');
    }

    // 检查是否所有维度都已完成分析
    function checkAllDimensionsCompleted() {
        try {
            // 查找状态徽章
            const statusBadges = document.querySelectorAll(CONFIG.selectors.statusBadge);
            for (const badge of statusBadges) {
                if (badge.textContent.includes('13/13') || 
                    badge.textContent.includes('13个维度已完成') || 
                    badge.textContent.includes('分析已完成')) {
                    safeLog('检测到所有维度已完成分析');
                    return true;
                }
            }
            return false;
        } catch (e) {
            safeLog(`检查所有维度是否完成时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 检查分析内容是否为空
    function isContentEmpty() {
        try {
            const contentElement = document.querySelector(CONFIG.selectors.analysisContent);
            const previewElement = document.querySelector(CONFIG.selectors.previewContent);
            
            // 检查分析内容
            if (contentElement && contentElement.innerHTML.trim() === '') {
                return true;
            }
            
            // 检查预览内容
            if (previewElement && previewElement.innerHTML.trim() === '') {
                return true;
            }
            
            return false;
        } catch (e) {
            safeLog(`检查内容是否为空时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 强制加载分析结果
    function forceLoadResults() {
        try {
            // 防止重复加载
            if (STATE.isLoading) {
                safeLog('已经在加载结果中，跳过');
                return;
            }
            
            STATE.isLoading = true;
            safeLog('开始强制加载分析结果');
            
            // 显示加载指示器
            showLoadingIndicator();
            
            // 获取必要的信息
            const chapterId = getChapterId();
            const currentDimension = getCurrentDimension();
            const isChapterPage = isChapterAnalysisPage();
            
            safeLog(`加载信息: 小说ID=${STATE.novelId}, 章节ID=${chapterId}, 维度=${currentDimension}, 是否章节页=${isChapterPage}`);
            
            // 根据页面类型加载结果
            if (isChapterPage && chapterId && currentDimension) {
                // 章节分析页面
                loadChapterAnalysisResult(STATE.novelId, chapterId, currentDimension);
            } else if (currentDimension) {
                // 单个维度分析页面
                loadSingleDimensionResult(STATE.novelId, currentDimension);
            } else {
                // 小说概览页面（所有维度）
                loadAllDimensionsResults(STATE.novelId);
            }
            
            // 设置超时处理
            setTimeout(function() {
                if (STATE.isLoading) {
                    safeLog('加载超时，检查是否需要重试', 'warn');
                    STATE.isLoading = false;
                    hideLoadingIndicator();
                    
                    // 如果内容仍为空，尝试重试
                    if (isContentEmpty()) {
                        retryLoadResults();
                    }
                }
            }, CONFIG.loadTimeout);
        } catch (e) {
            safeLog(`强制加载分析结果时出错: ${e.message}`, 'error');
            STATE.isLoading = false;
            hideLoadingIndicator();
        }
    }

    // 加载章节分析结果
    function loadChapterAnalysisResult(novelId, chapterId, dimension) {
        try {
            safeLog(`加载章节分析结果: novelId=${novelId}, chapterId=${chapterId}, dimension=${dimension}`);
            
            // 构建API URL
            const apiUrl = `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}?force_refresh=true`;
            
            // 发送请求
            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && data.success && data.result && data.result.content) {
                        safeLog('成功获取章节分析结果');
                        
                        // 更新分析结果
                        updateAnalysisContent(data.result.content);
                        
                        // 更新状态
                        STATE.isLoading = false;
                        hideLoadingIndicator();
                        
                        // 触发自定义事件
                        dispatchResultLoadedEvent(true);
                    } else {
                        throw new Error('分析结果无效或为空');
                    }
                })
                .catch(error => {
                    safeLog(`加载章节分析结果失败: ${error.message}`, 'error');
                    STATE.isLoading = false;
                    hideLoadingIndicator();
                    retryLoadResults();
                });
        } catch (e) {
            safeLog(`加载章节分析结果时出错: ${e.message}`, 'error');
            STATE.isLoading = false;
            hideLoadingIndicator();
            retryLoadResults();
        }
    }

    // 加载单个维度分析结果
    function loadSingleDimensionResult(novelId, dimension) {
        try {
            safeLog(`加载单个维度分析结果: novelId=${novelId}, dimension=${dimension}`);
            
            // 构建API URL
            const apiUrl = `/api/novel/${novelId}/analysis/${dimension}?force_refresh=true`;
            
            // 发送请求
            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && data.success && data.result && data.result.content) {
                        safeLog('成功获取单个维度分析结果');
                        
                        // 更新分析结果
                        updateAnalysisContent(data.result.content);
                        
                        // 更新状态
                        STATE.isLoading = false;
                        hideLoadingIndicator();
                        
                        // 触发自定义事件
                        dispatchResultLoadedEvent(true);
                    } else {
                        throw new Error('分析结果无效或为空');
                    }
                })
                .catch(error => {
                    safeLog(`加载单个维度分析结果失败: ${error.message}`, 'error');
                    STATE.isLoading = false;
                    hideLoadingIndicator();
                    retryLoadResults();
                });
        } catch (e) {
            safeLog(`加载单个维度分析结果时出错: ${e.message}`, 'error');
            STATE.isLoading = false;
            hideLoadingIndicator();
            retryLoadResults();
        }
    }

    // 加载所有维度分析结果
    function loadAllDimensionsResults(novelId) {
        try {
            safeLog(`加载所有维度分析结果: novelId=${novelId}`);
            
            // 构建API URL
            const apiUrl = `/api/novels/${novelId}/analysis?force_refresh=true`;
            
            // 发送请求
            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(results => {
                    if (Array.isArray(results) && results.length > 0) {
                        safeLog(`成功获取 ${results.length} 个维度的分析结果`);
                        
                        // 更新全局变量
                        if (typeof window.analysisResultsData === 'undefined') {
                            window.analysisResultsData = {};
                        }
                        
                        // 保存所有结果
                        results.forEach(result => {
                            if (result && result.dimension) {
                                window.analysisResultsData[result.dimension] = result;
                                STATE.resultCache[result.dimension] = result;
                            }
                        });
                        
                        // 更新小说预览内容
                        updatePreviewContent(results[0]);
                        
                        // 更新所有维度卡片
                        updateDimensionCards(results);
                        
                        // 更新状态
                        STATE.isLoading = false;
                        hideLoadingIndicator();
                        
                        // 触发自定义事件
                        dispatchResultLoadedEvent(true);
                    } else {
                        throw new Error('未获取到任何分析结果');
                    }
                })
                .catch(error => {
                    safeLog(`加载所有维度分析结果失败: ${error.message}`, 'error');
                    STATE.isLoading = false;
                    hideLoadingIndicator();
                    retryLoadResults();
                });
        } catch (e) {
            safeLog(`加载所有维度分析结果时出错: ${e.message}`, 'error');
            STATE.isLoading = false;
            hideLoadingIndicator();
            retryLoadResults();
        }
    }

    // 更新分析内容
    function updateAnalysisContent(content) {
        try {
            const contentElement = document.querySelector(CONFIG.selectors.analysisContent);
            if (contentElement) {
                contentElement.innerHTML = content;
                contentElement.setAttribute(CONFIG.dataAttributes.loaded, 'true');
                safeLog('成功更新分析内容');
            } else {
                safeLog('找不到分析内容元素', 'warn');
            }
        } catch (e) {
            safeLog(`更新分析内容时出错: ${e.message}`, 'error');
        }
    }

    // 更新预览内容
    function updatePreviewContent(result) {
        try {
            const previewElement = document.querySelector(CONFIG.selectors.previewContent);
            if (previewElement && result && result.content) {
                previewElement.innerHTML = result.content;
                previewElement.setAttribute(CONFIG.dataAttributes.loaded, 'true');
                safeLog('成功更新预览内容');
            } else {
                safeLog('找不到预览内容元素或结果无效', 'warn');
            }
        } catch (e) {
            safeLog(`更新预览内容时出错: ${e.message}`, 'error');
        }
    }

    // 更新维度卡片
    function updateDimensionCards(results) {
        try {
            // 获取所有维度卡片
            const cards = document.querySelectorAll(CONFIG.selectors.dimensionCards);
            safeLog(`找到 ${cards.length} 个维度卡片`);
            
            // 如果没有找到卡片，尝试使用全局函数
            if (cards.length === 0 && window.updateDimensionCard) {
                safeLog('未找到维度卡片，尝试使用全局updateDimensionCard函数');
                results.forEach(result => {
                    if (result && result.dimension) {
                        window.updateDimensionCard(result.dimension, result);
                    }
                });
                return;
            }
            
            // 更新每个卡片
            cards.forEach(card => {
                const dimension = card.getAttribute(CONFIG.dataAttributes.dimension);
                if (!dimension) return;
                
                // 查找对应的结果
                const result = results.find(r => r.dimension === dimension);
                if (!result) return;
                
                // 更新卡片内容
                const cardBody = card.querySelector('.card-body');
                if (!cardBody) return;
                
                // 提取摘要
                let excerpt = '分析已完成，点击"查看详情"查看完整结果。';
                if (result.content) {
                    // 简单提取前100个字符作为摘要
                    excerpt = result.content.substring(0, 100) + '...';
                }
                
                // 更新卡片内容
                cardBody.innerHTML = `
                    <div class="d-flex justify-content-between mb-2">
                        <span class="badge bg-success">分析完成</span>
                        <a href="/novel/${STATE.novelId}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                    </div>
                    <div class="analysis-excerpt mt-2">
                        ${excerpt}
                    </div>
                `;
                
                // 标记为已加载
                card.setAttribute(CONFIG.dataAttributes.loaded, 'true');
                
                safeLog(`成功更新维度 ${dimension} 的卡片`);
            });
        } catch (e) {
            safeLog(`更新维度卡片时出错: ${e.message}`, 'error');
        }
    }

    // 显示加载指示器
    function showLoadingIndicator() {
        try {
            // 检查是否已存在加载指示器
            let loadingIndicator = document.querySelector(CONFIG.selectors.loadingIndicator);
            
            if (!loadingIndicator) {
                // 创建加载指示器
                loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'result-loading-indicator position-fixed';
                loadingIndicator.style.cssText = 'top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; background: rgba(255,255,255,0.9); padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.2);';
                
                loadingIndicator.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0">正在强制加载分析结果，请稍候...</p>
                    </div>
                `;
                
                document.body.appendChild(loadingIndicator);
            } else {
                // 显示已存在的加载指示器
                loadingIndicator.style.display = 'block';
            }
        } catch (e) {
            safeLog(`显示加载指示器时出错: ${e.message}`, 'error');
        }
    }

    // 隐藏加载指示器
    function hideLoadingIndicator() {
        try {
            const loadingIndicator = document.querySelector(CONFIG.selectors.loadingIndicator);
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        } catch (e) {
            safeLog(`隐藏加载指示器时出错: ${e.message}`, 'error');
        }
    }

    // 重试加载结果
    function retryLoadResults() {
        try {
            if (STATE.retryCount >= CONFIG.maxRetries) {
                safeLog(`已达到最大重试次数 ${CONFIG.maxRetries}，停止重试`, 'warn');
                
                // 显示错误消息
                showErrorMessage('无法加载分析结果，请刷新页面重试');
                
                return;
            }
            
            STATE.retryCount++;
            const delay = CONFIG.retryDelay * STATE.retryCount;
            
            safeLog(`将在 ${delay}ms 后进行第 ${STATE.retryCount} 次重试`);
            
            setTimeout(forceLoadResults, delay);
        } catch (e) {
            safeLog(`重试加载结果时出错: ${e.message}`, 'error');
        }
    }

    // 显示错误消息
    function showErrorMessage(message) {
        try {
            // 查找分析内容容器
            const contentElement = document.querySelector(CONFIG.selectors.analysisContent) || 
                                  document.querySelector(CONFIG.selectors.previewContent) ||
                                  document.querySelector(CONFIG.selectors.resultContainer);
            
            if (contentElement) {
                contentElement.innerHTML = `
                    <div class="alert alert-danger mt-3" role="alert">
                        <h4 class="alert-heading">加载失败</h4>
                        <p>${message}</p>
                        <hr>
                        <p class="mb-0">
                            <button class="btn btn-outline-danger btn-sm" onclick="window.location.reload()">
                                刷新页面
                            </button>
                            <button class="btn btn-outline-primary btn-sm ms-2" onclick="window.fixAnalysisResultsDisplay.fix()">
                                重试加载
                            </button>
                        </p>
                    </div>
                `;
            } else {
                // 如果找不到内容容器，创建一个浮动消息
                const errorMessage = document.createElement('div');
                errorMessage.className = 'position-fixed top-0 start-50 translate-middle-x mt-3';
                errorMessage.style.zIndex = '9999';
                
                errorMessage.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">加载失败</h4>
                        <p>${message}</p>
                        <hr>
                        <p class="mb-0">
                            <button class="btn btn-outline-danger btn-sm" onclick="window.location.reload()">
                                刷新页面
                            </button>
                            <button class="btn btn-outline-primary btn-sm ms-2" onclick="window.fixAnalysisResultsDisplay.fix()">
                                重试加载
                            </button>
                        </p>
                    </div>
                `;
                
                document.body.appendChild(errorMessage);
                
                // 5秒后自动隐藏
                setTimeout(() => {
                    errorMessage.style.opacity = '0';
                    errorMessage.style.transition = 'opacity 1s';
                    
                    // 完全隐藏后移除
                    setTimeout(() => {
                        document.body.removeChild(errorMessage);
                    }, 1000);
                }, 5000);
            }
        } catch (e) {
            safeLog(`显示错误消息时出错: ${e.message}`, 'error');
        }
    }

    // 触发结果加载完成事件
    function dispatchResultLoadedEvent(success) {
        try {
            const event = new CustomEvent('analysis-result-loaded', {
                detail: {
                    success: success,
                    novelId: STATE.novelId,
                    dimension: getCurrentDimension(),
                    chapterId: getChapterId(),
                    timestamp: new Date().getTime()
                }
            });
            
            document.dispatchEvent(event);
            safeLog('已触发分析结果加载完成事件');
        } catch (e) {
            safeLog(`触发结果加载完成事件时出错: ${e.message}`, 'error');
        }
    }

    // 安全的日志记录函数
    function safeLog(message, level = 'info') {
        try {
            if (CONFIG.debug) {
                const prefix = '[九猫强制加载]';
                
                switch (level) {
                    case 'error':
                        console.error(`${prefix} ${message}`);
                        break;
                    case 'warn':
                        console.warn(`${prefix} ${message}`);
                        break;
                    default:
                        console.log(`${prefix} ${message}`);
                }
            }
        } catch (e) {
            // 忽略日志错误
        }
    }

    // 暴露全局API
    window.analysisResultLoader = {
        forceLoad: forceLoadResults,
        isAllDimensionsCompleted: checkAllDimensionsCompleted,
        isContentEmpty: isContentEmpty,
        getNovelId: getNovelId,
        getCurrentDimension: getCurrentDimension
    };

    // 启动
    init();
})(); 