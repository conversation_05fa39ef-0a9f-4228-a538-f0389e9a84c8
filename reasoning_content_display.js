/**
 * 九猫系统 - 增强版推理内容显示组件
 * 用于显示结构化的推理内容，支持分析思路和详细分析的分离显示
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('增强版推理内容显示组件已加载');

    // 配置
    const CONFIG = {
        // API路径
        apiPaths: {
            book: '/api/novel/{novelId}/analysis/{dimension}/reasoning_content',
            chapter: '/api/novel/{novelId}/chapter/{chapterId}/analysis/{dimension}/reasoning_content'
        },
        // CSS类名
        cssClasses: {
            container: 'reasoning-content-enhanced',
            header: 'reasoning-header',
            body: 'reasoning-body',
            section: 'reasoning-section',
            sectionHeader: 'reasoning-section-header',
            sectionBody: 'reasoning-section-body',
            collapsed: 'collapsed',
            expanded: 'expanded',
            loading: 'loading',
            error: 'error'
        },
        // 默认文本
        defaultText: {
            loading: '正在加载推理内容，请稍候...',
            error: '加载推理内容时出错，请刷新页面重试',
            empty: '暂无推理内容',
            approach: '分析思路',
            analysis: '详细分析',
            toggleExpand: '展开',
            toggleCollapse: '收起'
        }
    };

    // 加载推理内容
    function loadReasoningContent(options) {
        const {
            novelId,
            dimension,
            containerId = 'reasoningContent',
            chapterId = null,
            autoExpand = false
        } = options;

        console.log(`开始加载推理内容: novel_id=${novelId}, dimension=${dimension}${chapterId ? `, chapter_id=${chapterId}` : ''}`);

        // 获取容器元素
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`找不到推理内容容器: ${containerId}`);
            return;
        }

        // 添加增强版容器类
        container.classList.add(CONFIG.cssClasses.container);

        // 显示加载中状态
        showLoadingState(container);

        // 构建API URL
        const apiUrl = buildApiUrl(novelId, dimension, chapterId);

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && (data.reasoning_content || data.process)) {
                    // 获取推理内容
                    const reasoningContent = data.reasoning_content || data.process;

                    // 渲染结构化内容
                    renderStructuredContent(container, reasoningContent, autoExpand);

                    console.log(`成功加载推理内容: ${dimension}`);
                } else {
                    // 显示错误信息
                    showErrorState(container, '未找到推理内容');
                    console.warn(`未找到推理内容: ${dimension}`);
                }
            })
            .catch(error => {
                // 显示错误信息
                showErrorState(container, error.message);
                console.error(`加载推理内容时出错: ${error.message}`);
            });
    }

    // 构建API URL
    function buildApiUrl(novelId, dimension, chapterId = null) {
        if (chapterId) {
            return CONFIG.apiPaths.chapter
                .replace('{novelId}', novelId)
                .replace('{chapterId}', chapterId)
                .replace('{dimension}', dimension);
        } else {
            return CONFIG.apiPaths.book
                .replace('{novelId}', novelId)
                .replace('{dimension}', dimension);
        }
    }

    // 显示加载中状态
    function showLoadingState(container) {
        container.innerHTML = `
            <div class="${CONFIG.cssClasses.loading}">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">${CONFIG.defaultText.loading}</p>
            </div>
        `;
    }

    // 显示错误状态
    function showErrorState(container, errorMessage) {
        container.innerHTML = `
            <div class="${CONFIG.cssClasses.error} alert alert-warning">
                <p><i class="fas fa-exclamation-circle"></i> ${CONFIG.defaultText.error}</p>
                <p class="small text-muted mt-2">${errorMessage}</p>
            </div>
        `;
    }

    // 渲染结构化内容
    function renderStructuredContent(container, content, autoExpand = false) {
        // 解析内容，分离分析思路和详细分析
        const sections = parseReasoningContent(content);

        // 创建容器
        container.innerHTML = '';

        // 创建头部
        const header = document.createElement('div');
        header.className = CONFIG.cssClasses.header;
        container.appendChild(header);

        // 创建主体
        const body = document.createElement('div');
        body.className = CONFIG.cssClasses.body;
        container.appendChild(body);

        // 添加各个部分
        sections.forEach((section, index) => {
            // 跳过子部分，它们将在父部分中处理
            if (section.isSubSection) {
                return;
            }

            // 创建部分容器
            const sectionElement = document.createElement('div');
            sectionElement.className = CONFIG.cssClasses.section;

            // 为分析思路和详细分析添加特殊类
            if (index === 0) {
                sectionElement.classList.add('approach-section');
            } else if (index === 1) {
                sectionElement.classList.add('analysis-section');
            }

            if (!autoExpand) {
                sectionElement.classList.add(CONFIG.cssClasses.collapsed);
            } else {
                sectionElement.classList.add(CONFIG.cssClasses.expanded);
            }

            // 创建部分标题
            const sectionHeader = document.createElement('div');
            sectionHeader.className = CONFIG.cssClasses.sectionHeader;

            // 为分析思路和详细分析使用不同的标题样式
            if (index === 0 || index === 1) {
                sectionHeader.innerHTML = `
                    <h4>${section.title}</h4>
                    <button class="toggle-btn btn btn-sm btn-outline-primary">
                        <i class="fas ${autoExpand ? 'fa-chevron-up' : 'fa-chevron-down'}"></i>
                        ${autoExpand ? CONFIG.defaultText.toggleCollapse : CONFIG.defaultText.toggleExpand}
                    </button>
                `;
            } else {
                sectionHeader.innerHTML = `
                    <h5>${section.title}</h5>
                    <button class="toggle-btn btn btn-sm btn-outline-secondary">
                        <i class="fas ${autoExpand ? 'fa-chevron-up' : 'fa-chevron-down'}"></i>
                        ${autoExpand ? CONFIG.defaultText.toggleCollapse : CONFIG.defaultText.toggleExpand}
                    </button>
                `;
            }

            sectionElement.appendChild(sectionHeader);

            // 创建部分内容
            const sectionBody = document.createElement('div');
            sectionBody.className = CONFIG.cssClasses.sectionBody;
            sectionBody.style.display = autoExpand ? 'block' : 'none';

            // 检查是否有子部分
            if (section.hasSubSections) {
                // 如果有子部分，创建子部分容器
                const subSectionsContainer = document.createElement('div');
                subSectionsContainer.className = 'sub-sections-container';

                // 查找所有子部分
                const subSections = sections.filter(s => s.isSubSection);

                // 如果有子部分，添加到容器
                if (subSections.length > 0) {
                    // 渲染Markdown内容（主要是前言部分）
                    const introContent = section.content.split(/(?:\n|^)###\s+/)[0].trim();
                    if (introContent) {
                        const introElement = document.createElement('div');
                        introElement.className = 'analysis-intro';

                        if (window.renderMarkdown) {
                            window.renderMarkdown(introContent, introElement);
                        } else {
                            introElement.innerHTML = `<div class="markdown-content">${escapeHtml(introContent)}</div>`;
                        }

                        subSectionsContainer.appendChild(introElement);
                    }

                    // 添加子部分
                    subSections.forEach(subSection => {
                        const subSectionElement = document.createElement('div');
                        subSectionElement.className = 'sub-section';

                        const subSectionHeader = document.createElement('h3');
                        subSectionHeader.className = 'sub-section-header';
                        subSectionHeader.textContent = subSection.title;

                        const subSectionContent = document.createElement('div');
                        subSectionContent.className = 'sub-section-content';

                        if (window.renderMarkdown) {
                            window.renderMarkdown(subSection.content, subSectionContent);
                        } else {
                            subSectionContent.innerHTML = `<div class="markdown-content">${escapeHtml(subSection.content)}</div>`;
                        }

                        subSectionElement.appendChild(subSectionHeader);
                        subSectionElement.appendChild(subSectionContent);
                        subSectionsContainer.appendChild(subSectionElement);
                    });

                    sectionBody.appendChild(subSectionsContainer);
                } else {
                    // 没有找到子部分，直接渲染内容
                    if (window.renderMarkdown) {
                        window.renderMarkdown(section.content, sectionBody);
                    } else {
                        sectionBody.innerHTML = `<div class="markdown-content">${escapeHtml(section.content)}</div>`;
                    }
                }
            } else {
                // 没有子部分，直接渲染内容
                if (window.renderMarkdown) {
                    window.renderMarkdown(section.content, sectionBody);
                } else {
                    sectionBody.innerHTML = `<div class="markdown-content">${escapeHtml(section.content)}</div>`;
                }
            }

            sectionElement.appendChild(sectionBody);

            // 添加到主体
            body.appendChild(sectionElement);

            // 添加切换事件
            const toggleBtn = sectionHeader.querySelector('.toggle-btn');
            toggleBtn.addEventListener('click', function() {
                const isExpanded = sectionElement.classList.contains(CONFIG.cssClasses.expanded);

                if (isExpanded) {
                    // 收起
                    sectionElement.classList.remove(CONFIG.cssClasses.expanded);
                    sectionElement.classList.add(CONFIG.cssClasses.collapsed);
                    sectionBody.style.display = 'none';
                    toggleBtn.innerHTML = `
                        <i class="fas fa-chevron-down"></i>
                        ${CONFIG.defaultText.toggleExpand}
                    `;
                } else {
                    // 展开
                    sectionElement.classList.remove(CONFIG.cssClasses.collapsed);
                    sectionElement.classList.add(CONFIG.cssClasses.expanded);
                    sectionBody.style.display = 'block';
                    toggleBtn.innerHTML = `
                        <i class="fas fa-chevron-up"></i>
                        ${CONFIG.defaultText.toggleCollapse}
                    `;
                }
            });
        });
    }

    // 解析推理内容，分离分析思路和详细分析
    function parseReasoningContent(content) {
        // 默认部分
        const defaultSections = [
            { title: CONFIG.defaultText.approach, content: '' },
            { title: CONFIG.defaultText.analysis, content: '' }
        ];

        // 如果内容为空，返回默认部分
        if (!content) {
            return defaultSections;
        }

        // 尝试按标准格式分割内容
        const approachMatch = content.match(/(?:^|\n)(?:#+\s*分析思路说明[:：]|#+\s*分析思路[:：]|#+\s*Analysis Approach[:：]?)(?:\n|$)([\s\S]*?)(?=\n#+\s*|$)/i);
        const analysisMatch = content.match(/(?:^|\n)(?:#+\s*详细(?:\S+)?分析[:：]|#+\s*Detailed Analysis[:：]?)(?:\n|$)([\s\S]*?)(?=\n#+\s*|$)/i);

        if (approachMatch || analysisMatch) {
            // 找到了结构化内容
            const sections = [];

            // 添加分析思路
            sections.push({
                title: CONFIG.defaultText.approach,
                content: approachMatch ? approachMatch[1].trim() : '未提供分析思路'
            });

            // 添加详细分析
            if (analysisMatch) {
                // 检查详细分析中是否包含三级标题（小节）
                const analysisContent = analysisMatch[1].trim();
                const subSections = analysisContent.split(/(?:\n|^)###\s+/);

                if (subSections.length > 1) {
                    // 有小节，创建主详细分析部分
                    sections.push({
                        title: CONFIG.defaultText.analysis,
                        content: analysisContent,
                        hasSubSections: true
                    });

                    // 提取每个小节作为单独部分
                    for (let i = 1; i < subSections.length; i++) {
                        const subSection = subSections[i];
                        const titleEndIndex = subSection.indexOf('\n');

                        if (titleEndIndex > 0) {
                            const title = subSection.substring(0, titleEndIndex).trim();
                            const content = subSection.substring(titleEndIndex).trim();

                            sections.push({
                                title: title,
                                content: content,
                                isSubSection: true
                            });
                        }
                    }
                } else {
                    // 没有小节，直接添加详细分析
                    sections.push({
                        title: CONFIG.defaultText.analysis,
                        content: analysisContent
                    });
                }
            } else {
                sections.push({
                    title: CONFIG.defaultText.analysis,
                    content: '未提供详细分析'
                });
            }

            return sections;
        } else {
            // 没有找到标准结构，尝试其他格式
            const lines = content.split('\n');

            // 检查是否包含编号列表（可能是分析思路）
            const numberedListMatch = content.match(/(?:^|\n)(?:1\.\s+\*\*[^*]+\*\*[^\n]*(?:\n\d+\.\s+\*\*[^*]+\*\*[^\n]*)+)/);

            if (numberedListMatch) {
                // 找到了编号列表，可能是分析思路
                const approachContent = numberedListMatch[0].trim();

                // 查找编号列表后的内容作为详细分析
                const afterList = content.substring(numberedListMatch.index + numberedListMatch[0].length).trim();

                // 检查详细分析中是否包含三级标题（小节）
                const subSections = afterList.split(/(?:\n|^)###\s+/);

                if (subSections.length > 1) {
                    // 有小节
                    const sections = [
                        { title: CONFIG.defaultText.approach, content: approachContent }
                    ];

                    // 添加详细分析主部分
                    sections.push({
                        title: CONFIG.defaultText.analysis,
                        content: afterList,
                        hasSubSections: true
                    });

                    // 提取每个小节
                    for (let i = 1; i < subSections.length; i++) {
                        const subSection = subSections[i];
                        const titleEndIndex = subSection.indexOf('\n');

                        if (titleEndIndex > 0) {
                            const title = subSection.substring(0, titleEndIndex).trim();
                            const content = subSection.substring(titleEndIndex).trim();

                            sections.push({
                                title: title,
                                content: content,
                                isSubSection: true
                            });
                        }
                    }

                    return sections;
                } else {
                    // 没有小节
                    return [
                        { title: CONFIG.defaultText.approach, content: approachContent },
                        { title: CONFIG.defaultText.analysis, content: afterList }
                    ];
                }
            } else {
                // 没有找到任何结构，尝试简单分割
                // 假设前30%是分析思路，后70%是详细分析
                const splitPoint = Math.floor(content.length * 0.3);
                const lastParagraphBreak = content.substring(0, splitPoint).lastIndexOf('\n\n');

                if (lastParagraphBreak > 0) {
                    const approachContent = content.substring(0, lastParagraphBreak).trim();
                    const analysisContent = content.substring(lastParagraphBreak).trim();

                    return [
                        { title: CONFIG.defaultText.approach, content: approachContent },
                        { title: CONFIG.defaultText.analysis, content: analysisContent }
                    ];
                } else {
                    // 无法分割，将全部内容作为详细分析
                    return [
                        { title: CONFIG.defaultText.approach, content: '未提供明确的分析思路' },
                        { title: CONFIG.defaultText.analysis, content: content.trim() }
                    ];
                }
            }
        }
    }

    // HTML转义函数
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 在页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('增强版推理内容显示组件初始化');

        // 查找所有需要加载推理内容的容器
        const containers = document.querySelectorAll('[data-reasoning-container]');

        containers.forEach(container => {
            const novelId = container.getAttribute('data-novel-id');
            const dimension = container.getAttribute('data-dimension');
            const chapterId = container.getAttribute('data-chapter-id');
            const autoExpand = container.getAttribute('data-auto-expand') === 'true';

            if (novelId && dimension) {
                // 设置容器ID
                const containerId = container.id || `reasoningContent_${Math.random().toString(36).substr(2, 9)}`;
                if (!container.id) {
                    container.id = containerId;
                }

                // 加载推理内容
                loadReasoningContent({
                    novelId,
                    dimension,
                    containerId,
                    chapterId,
                    autoExpand
                });
            }
        });
    });

    // 导出函数供其他模块使用
    window.reasoningContentDisplay = {
        loadReasoningContent: loadReasoningContent
    };
})();
