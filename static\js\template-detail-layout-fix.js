/**
 * 九猫3.0系统模板详情页面布局修复脚本
 * 用于修复模板详情页面的布局，将内容显示在右侧空白处
 */

(function() {
    console.log('[模板详情布局修复] 初始化...');

    // 等待DOM加载完成
    function onDOMReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    // 修复模板详情页面布局
    function fixTemplateDetailLayout() {
        console.log('[模板详情布局修复] 开始修复模板详情页面布局...');

        // 检测是否是模板详情页面
        if (isTemplateDetailPage()) {
            console.log('[模板详情布局修复] 检测到模板详情页面');

            // 添加CSS样式
            addTemplateDetailStyles();

            // 重构页面布局
            restructureTemplateDetailLayout();
        } else {
            console.log('[模板详情布局修复] 当前页面不是模板详情页面');
            // 延迟检查，因为页面可能还在加载中
            setTimeout(fixTemplateDetailLayout, 1000);
        }
    }

    // 检测是否是模板详情页面
    function isTemplateDetailPage() {
        // 检查URL是否包含template或popular_tropes关键词
        const isTemplateURL = window.location.href.includes('template') ||
                             window.location.href.includes('popular_tropes');

        // 检查页面内容是否包含模板相关元素
        const hasTemplateElements = document.querySelector('.popular_tropes-container') !== null ||
                                   document.querySelector('[data-dimension="popular_tropes"]') !== null ||
                                   document.querySelector('.template-container') !== null ||
                                   document.querySelector('.template-settings') !== null;

        // 检查是否有特定的模板内容标记
        const hasTemplateContent = document.body.textContent.includes('popular_tropes') ||
                                  document.body.textContent.includes('热门桥段') ||
                                  document.body.textContent.includes('设定内容') ||
                                  document.body.textContent.includes('章节内容');

        return (isTemplateURL || hasTemplateElements || hasTemplateContent);
    }

    // 添加模板详情页面CSS样式
    function addTemplateDetailStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            /* 模板详情页面两列布局 */
            .template-detail-container {
                display: flex;
                width: 100%;
                min-height: 600px;
                background-color: #fffdf5;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                margin-top: 20px;
            }

            /* 左侧导航/设置列 */
            .template-detail-sidebar {
                width: 280px;
                background-color: #f8f9fa;
                padding: 20px;
                border-right: 1px solid #e9ecef;
                overflow-y: auto;
            }

            /* 右侧内容列 */
            .template-detail-content {
                flex: 1;
                padding: 30px;
                overflow-y: auto;
                background-color: #fff;
            }

            /* 导航标题 */
            .template-detail-sidebar h3 {
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                margin-top: 0;
                margin-bottom: 20px;
                font-size: 1.2rem;
            }

            /* 导航列表 */
            .template-detail-nav {
                list-style-type: none;
                padding: 0;
                margin: 0 0 20px 0;
            }

            .template-detail-nav-item {
                margin-bottom: 8px;
            }

            .template-detail-nav-link {
                display: block;
                padding: 8px 12px;
                background-color: #fff;
                border-radius: 4px;
                color: #333;
                text-decoration: none;
                transition: all 0.2s ease;
            }

            .template-detail-nav-link:hover {
                background-color: #e9ecef;
            }

            .template-detail-nav-link.active {
                background-color: #3498db;
                color: #fff;
            }

            /* 设置区域 */
            .template-detail-settings-section {
                margin-bottom: 20px;
                padding: 15px;
                background-color: #fff;
                border-radius: 8px;
            }

            .template-detail-settings-section h4 {
                margin-top: 0;
                color: #2c3e50;
                font-size: 1rem;
            }

            /* 内容区域标题 */
            .template-detail-content h2 {
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                margin-top: 0;
                margin-bottom: 20px;
            }

            /* 内容区域段落 */
            .template-detail-content p {
                line-height: 1.6;
                margin-bottom: 15px;
            }

            /* 内容区域列表 */
            .template-detail-content ul {
                padding-left: 20px;
                margin-bottom: 15px;
            }

            .template-detail-content li {
                margin-bottom: 8px;
                line-height: 1.5;
            }

            /* 响应式布局 */
            @media (max-width: 768px) {
                .template-detail-container {
                    flex-direction: column;
                }

                .template-detail-sidebar {
                    width: 100%;
                    border-right: none;
                    border-bottom: 1px solid #e9ecef;
                }
            }
        `;

        document.head.appendChild(styleElement);
        console.log('[模板详情布局修复] 添加模板详情页面样式');
    }

    // 重构模板详情页面布局
    function restructureTemplateDetailLayout() {
        console.log('[模板详情布局修复] 开始重构模板详情页面布局...');

        // 查找主要内容容器
        const mainContainer = findMainContainer();

        if (!mainContainer) {
            console.warn('[模板详情布局修复] 未找到主要内容容器，无法重构布局');
            // 延迟重试
            setTimeout(restructureTemplateDetailLayout, 1000);
            return;
        }

        // 检查是否已经应用了布局修复
        if (mainContainer.querySelector('.template-detail-container')) {
            console.log('[模板详情布局修复] 布局已经修复，无需重复操作');
            return;
        }

        // 保存原始内容
        const originalContent = mainContainer.innerHTML;

        // 创建新的两列布局容器
        const templateDetailContainer = document.createElement('div');
        templateDetailContainer.className = 'template-detail-container';

        // 创建左侧导航/设置列
        const templateDetailSidebar = document.createElement('div');
        templateDetailSidebar.className = 'template-detail-sidebar';

        // 创建右侧内容列
        const templateDetailContent = document.createElement('div');
        templateDetailContent.className = 'template-detail-content';

        // 提取设置内容
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = originalContent;

        // 查找设置相关内容
        const settingsElements = tempDiv.querySelectorAll('h3, h4, ul');
        const settingsTitle = document.createElement('h3');
        settingsTitle.textContent = '设置';
        templateDetailSidebar.appendChild(settingsTitle);

        // 将设置内容移动到左侧
        let hasSettingsContent = false;
        settingsElements.forEach(element => {
            if (element.textContent.includes('适用于') ||
                element.textContent.includes('请在此处添加') ||
                element.textContent.includes('可以包括') ||
                element.textContent.includes('设定') ||
                element.textContent.includes('数值和比例')) {

                const settingsSection = document.createElement('div');
                settingsSection.className = 'template-detail-settings-section';
                settingsSection.appendChild(element.cloneNode(true));
                templateDetailSidebar.appendChild(settingsSection);
                hasSettingsContent = true;
            }
        });

        // 如果没有找到设置内容，添加一个默认的设置区域
        if (!hasSettingsContent) {
            const defaultSettings = document.createElement('div');
            defaultSettings.className = 'template-detail-settings-section';
            defaultSettings.innerHTML = '<h4>模板设置</h4><p>在此处可以配置模板的基本参数和使用范围。</p>';
            templateDetailSidebar.appendChild(defaultSettings);
        }

        // 提取内容部分
        const contentTitle = document.createElement('h2');
        contentTitle.textContent = '模板详情';
        templateDetailContent.appendChild(contentTitle);

        // 查找内容相关元素
        const contentElements = tempDiv.querySelectorAll('h3, h4, ul, p');
        let hasContentElements = false;
        contentElements.forEach(element => {
            if (element.textContent.includes('章节内容') ||
                element.textContent.includes('结构建议') ||
                element.textContent.includes('开篇部分') ||
                element.textContent.includes('情节设置') ||
                element.textContent.includes('中间发展部分') ||
                element.textContent.includes('结尾部分')) {

                templateDetailContent.appendChild(element.cloneNode(true));
                hasContentElements = true;
            }
        });

        // 如果右侧内容为空，则将所有内容放入右侧
        if (!hasContentElements) {
            // 清空右侧内容
            templateDetailContent.innerHTML = '';
            templateDetailContent.appendChild(contentTitle);

            // 创建内容包装器
            const contentWrapper = document.createElement('div');
            contentWrapper.className = 'template-detail-content-wrapper';
            contentWrapper.innerHTML = originalContent;

            // 移除已经添加到左侧的设置内容
            const settingsToRemove = contentWrapper.querySelectorAll('h3, h4, ul');
            settingsToRemove.forEach(element => {
                if (element.textContent.includes('适用于') ||
                    element.textContent.includes('请在此处添加') ||
                    element.textContent.includes('可以包括') ||
                    element.textContent.includes('设定') ||
                    element.textContent.includes('数值和比例')) {

                    if (element.parentNode) {
                        element.parentNode.removeChild(element);
                    }
                }
            });

            // 添加剩余内容到右侧
            templateDetailContent.appendChild(contentWrapper);
        }

        // 将两列添加到容器中
        templateDetailContainer.appendChild(templateDetailSidebar);
        templateDetailContainer.appendChild(templateDetailContent);

        // 清空容器并添加新布局
        mainContainer.innerHTML = '';
        mainContainer.appendChild(templateDetailContainer);

        console.log('[模板详情布局修复] 模板详情页面布局重构完成');
    }

    // 查找主要内容容器
    function findMainContainer() {
        // 尝试查找常见的主容器选择器
        const containerSelectors = [
            '.container',
            '.container-fluid',
            '.content-container',
            '.main-content',
            '.template-container',
            '.popular_tropes-container',
            '[data-dimension="popular_tropes"]',
            '#content',
            'main'
        ];

        for (const selector of containerSelectors) {
            const container = document.querySelector(selector);
            if (container) {
                return container;
            }
        }

        // 如果找不到常见容器，尝试查找包含模板内容的容器
        const allDivs = document.querySelectorAll('div');
        for (const div of allDivs) {
            if (div.textContent.includes('popular_tropes') ||
                div.textContent.includes('热门桥段') ||
                div.textContent.includes('设定内容') ||
                div.textContent.includes('章节内容')) {

                // 确保找到的是父容器，而不是内容本身
                if (div.children.length > 2 && div.offsetWidth > 300) {
                    return div;
                }
            }
        }

        // 如果还是找不到，返回body作为最后的选择
        return document.body;
    }

    // 解析原始内容，提取导航/设置和内容
    function parseOriginalContent(originalContent) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = originalContent;

        const navItems = [];
        const settingsItems = [];
        const contentItems = [];

        // 提取导航项
        const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headings.forEach(heading => {
            const navItem = document.createElement('li');
            navItem.className = 'template-detail-nav-item';

            const navLink = document.createElement('a');
            navLink.className = 'template-detail-nav-link';
            navLink.href = '#' + (heading.id || 'section-' + Math.random().toString(36).substr(2, 9));
            navLink.textContent = heading.textContent;

            navItem.appendChild(navLink);
            navItems.push(navItem);
        });

        // 提取设置项
        const settingsElements = tempDiv.querySelectorAll('.popular_tropes-settings, [data-settings="popular_tropes"], .template-settings');
        settingsElements.forEach(element => {
            settingsItems.push(element.cloneNode(true));
        });

        // 如果没有找到设置元素，尝试查找包含"设置"或"设定"的元素
        if (settingsItems.length === 0) {
            const allElements = tempDiv.querySelectorAll('*');
            for (const element of allElements) {
                if (element.textContent.includes('设置') ||
                    element.textContent.includes('设定') ||
                    element.textContent.includes('适用于') ||
                    element.textContent.includes('请在此处添加')) {

                    // 确保找到的是设置容器，而不是内容本身
                    if (element.children.length > 0 && element.tagName !== 'BODY') {
                        settingsItems.push(element.cloneNode(true));
                    }
                }
            }
        }

        // 提取内容项
        const contentElements = tempDiv.querySelectorAll('.popular_tropes-content, .template-content, .content');
        contentElements.forEach(element => {
            contentItems.push(element.cloneNode(true));
        });

        // 如果没有找到内容元素，尝试查找包含"内容"或"建议"的元素
        if (contentItems.length === 0) {
            const allElements = tempDiv.querySelectorAll('*');
            for (const element of allElements) {
                if (element.textContent.includes('内容') ||
                    element.textContent.includes('建议') ||
                    element.textContent.includes('结构') ||
                    element.textContent.includes('开篇部分')) {

                    // 确保找到的是内容容器，而不是导航或设置
                    if (element.children.length > 0 &&
                        !element.textContent.includes('设置') &&
                        !element.textContent.includes('设定') &&
                        element.tagName !== 'BODY') {
                        contentItems.push(element.cloneNode(true));
                    }
                }
            }
        }

        return { navItems, settingsItems, contentItems };
    }

    // 在页面加载完成后执行修复
    onDOMReady(fixTemplateDetailLayout);

    // 监听DOM变化，动态修复模板布局
    function observeDOM() {
        const observer = new MutationObserver(mutations => {
            let shouldFixLayout = false;

            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];

                        if (node.nodeType === 1) { // 元素节点
                            if (node.classList && (
                                node.classList.contains('popular_tropes-container') ||
                                node.getAttribute('data-dimension') === 'popular_tropes' ||
                                node.classList.contains('template-container') ||
                                node.classList.contains('template-settings')
                            )) {
                                shouldFixLayout = true;
                                break;
                            }
                        }
                    }
                }
            });

            if (shouldFixLayout) {
                fixTemplateDetailLayout();
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('[模板详情布局修复] DOM观察器已启动');
    }

    // 启动DOM观察器
    observeDOM();
})();
