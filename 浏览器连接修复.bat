@echo off
chcp 65001 > nul
echo ===================================
echo   九猫系统浏览器连接修复工具
echo ===================================
echo.

echo 步骤1: 关闭所有浏览器进程...
taskkill /F /IM chrome.exe /T > nul 2>&1
taskkill /F /IM msedge.exe /T > nul 2>&1
taskkill /F /IM firefox.exe /T > nul 2>&1
echo 浏览器进程已关闭

echo.
echo 步骤2: 清除DNS缓存...
ipconfig /flushdns > nul
echo DNS缓存已清除

echo.
echo 步骤3: 重置网络连接...
netsh winsock reset > nul
echo 网络连接已重置

echo.
echo 步骤4: 检查端口5001是否被占用...
netstat -ano | findstr :5001 > nul
if %errorlevel% equ 0 (
    echo 警告：端口5001已被占用，尝试关闭占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5001') do (
        echo 正在关闭进程ID: %%a
        taskkill /F /PID %%a > nul 2>&1
    )
) else (
    echo 端口5001未被占用，可以正常启动服务器
)

echo.
echo 步骤5: 重新启动九猫服务器...
start /B cmd /c "python main.py"

echo.
echo 步骤6: 等待服务器启动...
timeout /t 5 /nobreak > nul

echo.
echo 步骤7: 使用IP地址打开浏览器...
start "" http://127.0.0.1:5001

echo.
echo 修复完成！
echo.
echo 如果仍然无法访问，请尝试以下操作：
echo 1. 重启计算机
echo 2. 检查防火墙设置
echo 3. 尝试使用不同的浏览器
echo.
pause
