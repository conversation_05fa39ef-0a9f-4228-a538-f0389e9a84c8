/**
 * 九猫 - 维度选择调试工具
 * 用于直接在控制台中修复维度选择问题
 * 版本: 1.0.0
 */

(function() {
    console.log('维度选择调试工具已加载 - 版本1.0.0');
    
    // 调试工具
    window.dimensionDebugger = {
        // 维度列表
        dimensions: [
            '人物角色', '情节发展', '场景描写', '情感表达', 
            '主题思想', '冲突设置', '叙事视角', '语言风格', 
            '象征意义', '文化背景', '节奏控制', '悬念设计', '结构安排'
        ],
        
        // 查找对话框
        findModal: function() {
            console.log('查找维度选择对话框...');
            
            // 方法1：查找显示的模态框
            let modal = document.querySelector('.modal.show, .modal[style*="display: block"]');
            if (modal) {
                console.log('找到已显示的模态框');
                if (this.isAnalysisModal(modal)) {
                    return modal;
                }
            }
            
            // 方法2：查找所有模态框
            const modals = document.querySelectorAll('.modal');
            console.log(`找到 ${modals.length} 个模态框`);
            
            for (let i = 0; i < modals.length; i++) {
                if (this.isAnalysisModal(modals[i])) {
                    return modals[i];
                }
            }
            
            // 方法3：查找所有标题
            const titles = document.querySelectorAll('.modal-title');
            console.log(`找到 ${titles.length} 个模态框标题`);
            
            for (let i = 0; i < titles.length; i++) {
                if (titles[i].textContent && titles[i].textContent.includes('选择分析维度')) {
                    const modal = titles[i].closest('.modal');
                    if (modal) {
                        return modal;
                    }
                }
            }
            
            console.warn('未找到维度选择对话框');
            return null;
        },
        
        // 检查是否是分析维度对话框
        isAnalysisModal: function(modal) {
            const title = modal.querySelector('.modal-title');
            return title && title.textContent && title.textContent.includes('选择分析维度');
        },
        
        // 检查维度选择对话框的DOM结构
        inspectModal: function() {
            const modal = this.findModal();
            if (!modal) {
                console.error('无法找到维度选择对话框');
                return;
            }
            
            console.log('维度选择对话框结构：', modal);
            
            // 检查模态框内容
            const modalContent = modal.querySelector('.modal-content');
            console.log('模态框内容:', modalContent);
            
            // 检查模态框头部
            const modalHeader = modal.querySelector('.modal-header');
            console.log('模态框头部:', modalHeader);
            
            // 检查模态框标题
            const modalTitle = modal.querySelector('.modal-title');
            console.log('模态框标题:', modalTitle ? modalTitle.textContent : '无标题');
            
            // 检查模态框主体
            const modalBody = modal.querySelector('.modal-body');
            console.log('模态框主体:', modalBody);
            
            // 检查复选框
            const checkboxes = modal.querySelectorAll('input[type="checkbox"]');
            console.log(`找到 ${checkboxes.length} 个复选框:`, checkboxes);
            
            return {
                modal: modal,
                content: modalContent,
                header: modalHeader,
                title: modalTitle,
                body: modalBody,
                checkboxes: checkboxes
            };
        },
        
        // 手动修复维度选择对话框
        fixModal: function() {
            const modal = this.findModal();
            if (!modal) {
                console.error('无法找到维度选择对话框');
                return false;
            }
            
            console.log('开始手动修复维度选择对话框...');
            
            try {
                // 查找维度列表容器
                let dimensionList = modal.querySelector('.modal-body');
                
                if (!dimensionList) {
                    console.log('未找到.modal-body，尝试其他选择器');
                    dimensionList = modal.querySelector('.modal-content > div:not(.modal-header):not(.modal-footer)');
                }
                
                if (!dimensionList) {
                    console.log('仍未找到维度列表容器，创建一个新的');
                    dimensionList = document.createElement('div');
                    dimensionList.className = 'modal-body';
                    const modalContent = modal.querySelector('.modal-content');
                    const modalFooter = modal.querySelector('.modal-footer');
                    if (modalContent) {
                        if (modalFooter) {
                            modalContent.insertBefore(dimensionList, modalFooter);
                        } else {
                            modalContent.appendChild(dimensionList);
                        }
                    }
                }
                
                if (!dimensionList) {
                    console.error('无法找到或创建维度列表容器，修复失败');
                    return false;
                }
                
                // 检查是否已经有维度项
                const existingDimensions = dimensionList.querySelectorAll('input[type="checkbox"]:not(.all-select)');
                if (existingDimensions.length > 1) {
                    console.log(`已存在${existingDimensions.length}个维度项，无需修复`);
                    return true;
                }
                
                // 获取或创建全选复选框
                let selectAllCheckbox = dimensionList.querySelector('input[type="checkbox"]');
                let selectAllLabel;
                
                if (!selectAllCheckbox) {
                    console.log('未找到全选复选框，创建一个新的');
                    
                    const selectAllContainer = document.createElement('div');
                    selectAllContainer.className = 'form-check';
                    
                    selectAllCheckbox = document.createElement('input');
                    selectAllCheckbox.type = 'checkbox';
                    selectAllCheckbox.className = 'form-check-input all-select';
                    selectAllCheckbox.id = 'dimension-all';
                    selectAllCheckbox.checked = true;
                    
                    selectAllLabel = document.createElement('label');
                    selectAllLabel.className = 'form-check-label';
                    selectAllLabel.htmlFor = 'dimension-all';
                    selectAllLabel.textContent = '全选';
                    
                    selectAllContainer.appendChild(selectAllCheckbox);
                    selectAllContainer.appendChild(selectAllLabel);
                    dimensionList.appendChild(selectAllContainer);
                } else {
                    // 找到现有的全选标签
                    selectAllLabel = dimensionList.querySelector('label[for="' + selectAllCheckbox.id + '"]');
                    if (!selectAllLabel) {
                        // 尝试找到与复选框相邻的标签
                        let next = selectAllCheckbox.nextElementSibling;
                        if (next && next.tagName === 'LABEL') {
                            selectAllLabel = next;
                        }
                    }
                    
                    // 确保复选框有正确的class
                    selectAllCheckbox.classList.add('all-select');
                }
                
                // 创建维度列表容器
                const dimensionItemsContainer = document.createElement('div');
                dimensionItemsContainer.className = 'dimension-items';
                dimensionItemsContainer.style.marginTop = '15px';
                dimensionItemsContainer.style.maxHeight = '300px';
                dimensionItemsContainer.style.overflowY = 'auto';
                dimensionItemsContainer.style.border = '1px solid #dee2e6';
                dimensionItemsContainer.style.borderRadius = '4px';
                dimensionItemsContainer.style.padding = '10px';
                
                // 添加维度复选框
                this.dimensions.forEach(function(dimension, index) {
                    const dimensionItem = document.createElement('div');
                    dimensionItem.className = 'form-check';
                    dimensionItem.style.marginBottom = '8px';
                    
                    const dimensionCheckbox = document.createElement('input');
                    dimensionCheckbox.type = 'checkbox';
                    dimensionCheckbox.className = 'form-check-input dimension-checkbox';
                    dimensionCheckbox.id = `dimension-${index}`;
                    dimensionCheckbox.value = dimension;
                    dimensionCheckbox.dataset.dimension = dimension;
                    dimensionCheckbox.checked = true;
                    
                    const dimensionLabel = document.createElement('label');
                    dimensionLabel.className = 'form-check-label';
                    dimensionLabel.htmlFor = `dimension-${index}`;
                    dimensionLabel.textContent = dimension;
                    
                    dimensionItem.appendChild(dimensionCheckbox);
                    dimensionItem.appendChild(dimensionLabel);
                    dimensionItemsContainer.appendChild(dimensionItem);
                });
                
                // 将维度列表添加到对话框中
                dimensionList.appendChild(dimensionItemsContainer);
                
                // 添加全选复选框的功能
                selectAllCheckbox.addEventListener('change', function() {
                    const dimensionCheckboxes = dimensionList.querySelectorAll('.dimension-checkbox');
                    dimensionCheckboxes.forEach(function(checkbox) {
                        checkbox.checked = selectAllCheckbox.checked;
                    });
                });
                
                // 添加维度复选框的联动功能
                const dimensionCheckboxes = dimensionList.querySelectorAll('.dimension-checkbox');
                dimensionCheckboxes.forEach(function(checkbox) {
                    checkbox.addEventListener('change', function() {
                        // 检查是否所有维度都被选中
                        const allChecked = Array.from(dimensionCheckboxes).every(function(cb) {
                            return cb.checked;
                        });
                        
                        // 更新全选复选框状态
                        selectAllCheckbox.checked = allChecked;
                    });
                });
                
                console.log('维度复选框修复完成，已添加13个维度');
                return true;
            } catch (error) {
                console.error('修复维度复选框时出错:', error);
                return false;
            }
        },
        
        // 强制显示维度选择对话框
        showModal: function() {
            const modal = this.findModal();
            if (!modal) {
                console.error('无法找到维度选择对话框');
                return;
            }
            
            console.log('强制显示维度选择对话框');
            
            // 添加必要的类和样式
            modal.classList.add('show');
            modal.style.display = 'block';
            modal.setAttribute('aria-modal', 'true');
            modal.setAttribute('role', 'dialog');
            
            // 添加背景
            let backdrop = document.querySelector('.modal-backdrop');
            if (!backdrop) {
                backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                document.body.appendChild(backdrop);
            }
            
            // 添加样式到body
            document.body.classList.add('modal-open');
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = '17px';
        },
        
        // 运行完整的修复
        fix: function() {
            console.log('开始完整修复流程...');
            
            // 1. 检查对话框
            const modalInfo = this.inspectModal();
            if (!modalInfo || !modalInfo.modal) {
                console.error('无法找到维度选择对话框，修复失败');
                return false;
            }
            
            // 2. 修复对话框
            const fixResult = this.fixModal();
            if (!fixResult) {
                console.error('修复维度选择对话框失败');
                return false;
            }
            
            // 3. 确保对话框可见
            this.showModal();
            
            console.log('修复完成，请检查维度选择对话框');
            return true;
        },
        
        // 显示帮助信息
        help: function() {
            console.log(`
维度选择调试工具使用说明：
--------------------------
window.dimensionDebugger.inspectModal()  - 检查维度选择对话框的DOM结构
window.dimensionDebugger.fixModal()      - 手动修复维度选择对话框
window.dimensionDebugger.showModal()     - 强制显示维度选择对话框
window.dimensionDebugger.fix()           - 运行完整的修复流程
window.dimensionDebugger.help()          - 显示此帮助信息

如何使用:
1. 打开开发者工具 (按F12)
2. 导航到控制台 (Console) 标签
3. 调用以上任意方法，例如: window.dimensionDebugger.fix()
            `);
        }
    };
    
    // 自动显示帮助信息
    window.dimensionDebugger.help();
})(); 