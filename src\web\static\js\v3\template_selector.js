/**
 * 参考蓝本选择器
 * 用于控制台页面选择参考蓝本
 */

// 确保jQuery加载后才执行
(function() {
    // 等待jQuery加载
    function checkJQuery() {
        if (typeof jQuery !== 'undefined') {
            initTemplateSelector();
        } else {
            setTimeout(checkJQuery, 100);
        }
    }

    // 初始化模板选择器
    function initTemplateSelector() {
        jQuery(document).ready(function($) {
            // 选择参考蓝本按钮点击事件
            $('.select-template-btn').click(function() {
                const templateId = $(this).data('template-id');
                const templateTitle = $(this).closest('.card').find('.card-title').text();
                
                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在选择参考蓝本，请稍候...</h5>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);
                
                // 设置当前选中的参考蓝本
                localStorage.setItem('selectedTemplateId', templateId);
                localStorage.setItem('selectedTemplateTitle', templateTitle);
                
                // 高亮显示选中的参考蓝本卡片
                $('.template-card').removeClass('active');
                $(this).closest('.template-card').addClass('active');
                
                // 刷新页面以加载最新数据
                window.location.href = `/v3/console?template_id=${templateId}&_=${new Date().getTime()}`;
            });
            
            // 从URL参数中获取模板ID
            const urlParams = new URLSearchParams(window.location.search);
            const templateId = urlParams.get('template_id');
            
            // 如果URL中有模板ID，高亮显示对应的模板卡片
            if (templateId) {
                $(`.template-card[data-template-id="${templateId}"]`).addClass('active');
                
                // 添加日志
                if (typeof addLogEntry === 'function') {
                    const templateTitle = $(`.template-card[data-template-id="${templateId}"]`).find('.card-title').text();
                    addLogEntry('info', `已选择参考蓝本: ${templateTitle} (ID: ${templateId})`);
                }
            }
        });
    }

    // 开始检查jQuery是否加载
    checkJQuery();
})();
