/**
 * 九猫 - 统一修复脚本
 * 集成了jQuery冲突修复和章节分析路由修复等功能
 * 版本: 1.1.0
 */

(function() {
    console.log('[九猫统一修复] 脚本已加载');

    // ===================== jQuery冲突修复部分 =====================
    // 立即修复matchesSelector问题
    function fixJQueryMatchesSelector() {
        try {
            // 立即阻止后续jQuery加载，减少冲突可能
            preventMultipleJQuery();

        // 确保jQuery已加载
        if (typeof jQuery === 'undefined' || !jQuery.find) {
                console.log('[九猫统一修复] jQuery尚未加载，稍后再试');
                setTimeout(fixJQueryMatchesSelector, 100);
            return;
        }

            console.log('[九猫统一修复] 开始修复jQuery.find.matchesSelector');

            // 修复jQuery.find.matchesSelector
            if (jQuery.find && !jQuery.find.matchesSelector) {
                if (jQuery.find.matches) {
                    jQuery.find.matchesSelector = jQuery.find.matches;
                } else if (typeof Element.prototype.matches !== 'undefined') {
                    jQuery.find.matchesSelector = function(elem, expr) {
                        return Element.prototype.matches.call(elem, expr);
                    };
                } else if (typeof Element.prototype.msMatchesSelector !== 'undefined') {
                    jQuery.find.matchesSelector = function(elem, expr) {
                        return Element.prototype.msMatchesSelector.call(elem, expr);
                    };
                } else {
                    jQuery.find.matchesSelector = function() { return true; };
                }
                console.log('[九猫统一修复] jQuery.find.matchesSelector已修复');
            }

            // 修复Sizzle.matchesSelector
            if (window.Sizzle && !Sizzle.matchesSelector) {
                if (Sizzle.matches) {
                    Sizzle.matchesSelector = Sizzle.matches;
                } else if (jQuery.find && jQuery.find.matchesSelector) {
                    Sizzle.matchesSelector = jQuery.find.matchesSelector;
                } else {
                    Sizzle.matchesSelector = function() { return true; };
                }
                console.log('[九猫统一修复] Sizzle.matchesSelector已修复');
            }

            // 修复S.find.matchesSelector
            if (typeof S !== 'undefined' && S && S.find && !S.find.matchesSelector) {
                if (S.find.matches) {
                    S.find.matchesSelector = S.find.matches;
                } else if (jQuery.find && jQuery.find.matchesSelector) {
                    S.find.matchesSelector = jQuery.find.matchesSelector;
                } else {
                    S.find.matchesSelector = function() { return true; };
                }
                console.log('[九猫统一修复] S.find.matchesSelector已修复');
            }
        } catch (e) {
            console.error('[九猫统一修复] jQuery修复过程中出错:', e);
        }
            }

            // 防止加载多个jQuery版本
    function preventMultipleJQuery() {
        // 如果已经设置了拦截器，不重复设置
        if (window.__jQueryInterceptorSet) return;
        
        // 标记jQuery已加载
        if (typeof jQuery !== 'undefined') {
                window.__jQueryLoaded = true;
            window.__jQueryVersion = jQuery.fn.jquery;
            console.log('[九猫统一修复] jQuery已加载，版本:', window.__jQueryVersion);
        }

                // 拦截后续jQuery加载
        window.__originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = window.__originalCreateElement.call(document, tagName);
            if (tagName.toLowerCase() === 'script') {
                const originalSetAttribute = element.setAttribute;
                element.setAttribute = function(name, value) {
                    if (name === 'src' && typeof value === 'string' && 
                        (value.indexOf('jquery') !== -1 || value.indexOf('jQuery') !== -1) && 
                        window.__jQueryLoaded) {
                        console.warn('[九猫统一修复] 阻止加载重复的jQuery:', value);
                        return element;
                    }
                    return originalSetAttribute.call(this, name, value);
                };
            }
            return element;
        };

        // 拦截appendChild
        const originalAppendChild = document.head.appendChild;
        document.head.appendChild = function(element) {
            if (element.tagName === 'SCRIPT' && 
                element.src && 
                (element.src.indexOf('jquery') !== -1 || element.src.indexOf('jQuery') !== -1) && 
                window.__jQueryLoaded) {
                console.warn('[九猫统一修复] 阻止重复加载jQuery:', element.src);
                // 触发加载完成事件，防止依赖该脚本的其他逻辑卡死
                setTimeout(() => {
                    const event = new Event('load');
                    element.dispatchEvent(event);
                }, 10);
                return element; // 不实际添加元素
            }
            return originalAppendChild.call(this, element);
        };

        window.__jQueryInterceptorSet = true;
        console.log('[九猫统一修复] jQuery加载拦截器已设置');
    }

    // ===================== 章节分析路由修复部分 =====================
    // 修复章节分析按钮点击事件
    function fixChapterAnalysisLinks() {
                        // 获取当前小说ID
                        const novelId = getCurrentNovelId();
        if (!novelId) {
            console.warn('[九猫统一修复] 无法获取小说ID，章节分析修复将部分失效');
            return;
        }

        console.log(`[九猫统一修复] 开始修复章节分析按钮，小说ID: ${novelId}`);

        // 查找所有可能的章节分析按钮
        const buttonSelectors = [
            'a.btn[href*="/chapter"]', 
            'a.btn[href*="chapters"]',
            'a[href*="/direct_chapters"]',
            'a[href*="chapter_fix"]',
            'a.btn-success',
            'button.btn[data-target*="chapter"]'
        ];

        const allButtons = document.querySelectorAll(buttonSelectors.join(', '));
        console.log(`[九猫统一修复] 找到${allButtons.length}个可能的按钮`);

        let fixedCount = 0;

        // 修复找到的按钮
        allButtons.forEach(btn => {
            // 检查按钮文本是否包含章节和分析关键词
            const btnText = btn.textContent.trim().toLowerCase();
            const isChapterButton = btnText.includes('章节') && 
                                    (btnText.includes('分析') || 
                                     btnText.includes('列表') || 
                                     btnText.includes('管理'));

            if (isChapterButton) {
                console.log(`[九猫统一修复] 找到章节分析按钮: "${btn.textContent.trim()}"`, btn);
                
                // 保存原始链接用于日志
                const originalHref = btn.getAttribute('href');
                const originalOnclick = btn.onclick;

                // 设置正确的href属性
                            if (btn.tagName === 'A') {
                                btn.href = `/novel/${novelId}/chapters`;
                            }

                // 完全覆盖点击事件
                    btn.onclick = function(e) {
                        e.preventDefault();
                    e.stopPropagation();
                    console.log(`[九猫统一修复] 拦截章节分析按钮点击，跳转到: /novel/${novelId}/chapters`);
                    
                    // 使用setTimeout来确保事件完全执行后再跳转
                    setTimeout(() => {
                        window.location.href = `/novel/${novelId}/chapters`;
                    }, 10);
                    
                        return false;
                    };

                // 记录修复细节
                console.log(`[九猫统一修复] 已修复章节分析按钮：
                  - 文本: ${btn.textContent.trim()}
                  - 原始链接: ${originalHref || '无'}
                  - 新链接: /novel/${novelId}/chapters
                  - 是否有原始onclick: ${originalOnclick ? '是' : '否'}`);

                fixedCount++;
            }
        });

        console.log(`[九猫统一修复] 完成章节分析按钮修复，共修复${fixedCount}个按钮`);

            // 特别处理小说ID为41的情况
        if (novelId === '41' || window.location.pathname.includes('/novel/41')) {
            console.log('[九猫统一修复] 检测到小说ID为41，应用特殊修复');

            // 查找所有按钮和链接，无论类型
            const allElements = document.querySelectorAll('a, button');
            allElements.forEach(el => {
                const text = el.textContent.trim().toLowerCase();
                if (text.includes('章节') && text.includes('分析')) {
                    console.log('[九猫统一修复] 为小说41特别处理元素:', el);

                    // 完全覆盖点击事件
                    el.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('[九猫统一修复] 小说41特殊处理，跳转到: /novel/41/chapters');
                        window.location.href = '/novel/41/chapters';
                        return false;
                    };

                    // 如果是链接，修改href
                    if (el.tagName === 'A') {
                        el.href = '/novel/41/chapters';
                    }
                }
            });
        }
    }

    // 从URL、数据属性或页面内容获取当前小说ID
    function getCurrentNovelId() {
        // 尝试从URL获取
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        if (match && match[1]) {
            console.log(`[九猫统一修复] 从URL获取到小说ID: ${match[1]}`);
            return match[1];
        }

        // 尝试从data属性获取
        const novelContainer = document.querySelector('[data-novel-id]');
        if (novelContainer && novelContainer.dataset.novelId) {
            console.log(`[九猫统一修复] 从data属性获取到小说ID: ${novelContainer.dataset.novelId}`);
            return novelContainer.dataset.novelId;
        }

        // 尝试从表单获取
        const form = document.querySelector('form[action*="/novel/"]');
        if (form) {
            const formMatch = form.action.match(/\/novel\/(\d+)/);
            if (formMatch && formMatch[1]) {
                console.log(`[九猫统一修复] 从表单获取到小说ID: ${formMatch[1]}`);
                return formMatch[1];
            }
        }

        // 尝试从breadcrumb获取
        const breadcrumbLinks = document.querySelectorAll('.breadcrumb a[href*="/novel/"]');
        if (breadcrumbLinks.length > 0) {
            const lastLink = breadcrumbLinks[breadcrumbLinks.length - 1];
            const breadcrumbMatch = lastLink.href.match(/\/novel\/(\d+)/);
            if (breadcrumbMatch && breadcrumbMatch[1]) {
                console.log(`[九猫统一修复] 从面包屑导航获取到小说ID: ${breadcrumbMatch[1]}`);
                return breadcrumbMatch[1];
            }
        }

        // 直接从页面内容中搜索
        const pageContent = document.body.innerHTML;
        const idPattern = /novel\/(\d+)/g;
        const idMatches = [...pageContent.matchAll(idPattern)];
        if (idMatches.length > 0) {
            // 使用出现最多的ID
            const idCounts = {};
            idMatches.forEach(match => {
                const id = match[1];
                idCounts[id] = (idCounts[id] || 0) + 1;
            });
            
            const mostCommonId = Object.keys(idCounts).reduce((a, b) => 
                idCounts[a] > idCounts[b] ? a : b);
            
            console.log(`[九猫统一修复] 从页面内容分析得到小说ID: ${mostCommonId}`);
            return mostCommonId;
        }

        console.warn('[九猫统一修复] 无法获取小说ID');
        return null;
    }

    // 添加全局点击事件处理
    function setupGlobalClickHandler() {
        // 如果已经设置了全局点击处理器，不重复设置
        if (window.__chapterClickHandlerSet) return;
        
        // 添加全局点击事件处理器
        document.addEventListener('click', function(e) {
            // 只在小说页面处理
            if (!window.location.pathname.match(/\/novel\/\d+/)) return;
            
            // 获取点击的元素及其祖先元素
            let target = e.target;
            while (target && target !== document) {
                // 检查是否匹配章节分析按钮
                if ((target.tagName === 'A' || target.tagName === 'BUTTON') &&
                    target.textContent && 
                    target.textContent.toLowerCase().includes('章节') &&
                    (target.textContent.toLowerCase().includes('分析') || 
                     target.textContent.toLowerCase().includes('列表'))) {
                    
                    // 获取小说ID
                    const novelId = getCurrentNovelId();
                    if (novelId) {
                        console.log(`[九猫统一修复] 捕获到章节分析按钮点击，将跳转到: /novel/${novelId}/chapters`);
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // 确保在所有事件处理完成后再跳转
                        setTimeout(() => {
                            window.location.href = `/novel/${novelId}/chapters`;
                        }, 10);
                    }
                    break;
                }
                target = target.parentNode;
            }
        }, true); // 使用捕获阶段，确保在事件冒泡前处理
        
        window.__chapterClickHandlerSet = true;
        console.log('[九猫统一修复] 已设置全局点击事件处理器');
    }

    // ===================== 初始化函数 =====================
    function initialize() {
        console.log('[九猫统一修复] 开始初始化...');

        // 立即执行jQuery修复
        fixJQueryMatchesSelector();

        // 设置防止多重加载
        preventMultipleJQuery();

        // 监视S对象创建
        monitorSObject();

        // 如果在小说详情页，修复章节分析链接
        if (window.location.pathname.match(/\/novel\/\d+\/?$/)) {
            console.log('[九猫统一修复] 检测到小说详情页，应用章节分析链接修复');
            
            // 等待DOM加载完成再修复章节分析链接
            fixChapterAnalysisLinks();
            
            // 定期检查，应对动态加载的情况
            setInterval(fixChapterAnalysisLinks, 2000);
        }

        // 设置全局点击事件处理器
        setupGlobalClickHandler();

        // 定期执行jQuery修复，确保在动态加载的情况下也能修复
        var jQueryFixAttempts = 0;
        var jQueryFixInterval = setInterval(function() {
            fixJQueryMatchesSelector();
            jQueryFixAttempts++;

            // 20次尝试后降低频率
            if (jQueryFixAttempts >= 20) {
                clearInterval(jQueryFixInterval);
                // 继续以较低频率尝试修复
                setInterval(fixJQueryMatchesSelector, 5000);
                console.log('[九猫统一修复] 已切换到低频率jQuery修复检查');
            }
        }, 500);

        console.log('[九猫统一修复] 初始化完成');
        }

    // 监视S对象创建
    function monitorSObject() {
        if (typeof window.S === 'undefined') {
            Object.defineProperty(window, 'S', {
                configurable: true,
                set: function(newValue) {
                    delete window.S;
                    window.S = newValue;
                    console.log('[九猫统一修复] 检测到S对象被创建，尝试修复');
                    setTimeout(fixJQueryMatchesSelector, 0);
                },
                get: function() {
                    return undefined;
                }
            });
            console.log('[九猫统一修复] 已设置S对象监视器');
                } else {
            console.log('[九猫统一修复] S对象已存在，直接修复');
            setTimeout(fixJQueryMatchesSelector, 0);
        }
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出修复函数供手动调用
    window.unifiedFixes = {
        fixJQuery: fixJQueryMatchesSelector,
        fixChapterLinks: fixChapterAnalysisLinks,
        getNovelId: getCurrentNovelId
    };
})();
