#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
九猫小说分析系统 - 空间优化与性能提升工具
实现临时文件重定向和内存管理优化
"""
import os
import sys
import shutil
import logging
import sqlite3
import subprocess
from datetime import datetime
import json
import tempfile

# 配置日志
log_filename = f"optimize_nine_cats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_temp_directory(base_path):
    """创建自定义临时目录"""
    try:
        # 确保基础路径存在
        if not os.path.exists(base_path):
            os.makedirs(base_path)
            logger.info(f"创建基础目录: {base_path}")
        
        # 创建九猫专用临时目录
        temp_dir = os.path.join(base_path, "nine_cats_temp")
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
            logger.info(f"创建九猫专用临时目录: {temp_dir}")
        
        # 创建子目录
        cache_dir = os.path.join(temp_dir, "cache")
        logs_dir = os.path.join(temp_dir, "logs")
        analysis_dir = os.path.join(temp_dir, "analysis")
        
        for directory in [cache_dir, logs_dir, analysis_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"创建子目录: {directory}")
        
        return temp_dir
    except Exception as e:
        logger.error(f"创建临时目录失败: {str(e)}")
        return None

def update_config_with_temp_paths(temp_dir):
    """更新配置文件，添加临时目录路径"""
    try:
        config_file = "config.py"
        temp_config_file = "config.py.new"
        
        if not os.path.exists(config_file):
            # 如果配置文件不存在，创建一个新的
            with open(config_file, "w", encoding="utf-8") as f:
                f.write(f"""# 九猫小说分析系统配置文件
# 由optimize_nine_cats.py生成

# 临时文件目录
TEMP_DIR = "{temp_dir.replace('\\', '\\\\')}"
CACHE_DIR = "{os.path.join(temp_dir, 'cache').replace('\\', '\\\\')}"
LOG_DIR = "{os.path.join(temp_dir, 'logs').replace('\\', '\\\\')}"
ANALYSIS_TEMP_DIR = "{os.path.join(temp_dir, 'analysis').replace('\\', '\\\\')}"

# 内存优化设置
CHUNK_SIZE = 1024 * 1024  # 1MB
MAX_CACHE_SIZE = 100 * 1024 * 1024  # 100MB
ENABLE_MEMORY_OPTIMIZATION = True
""")
            logger.info(f"创建新的配置文件: {config_file}")
            return True
        
        # 读取现有配置
        with open(config_file, "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 是否已存在临时目录配置
        has_temp_dir = any("TEMP_DIR" in line for line in lines)
        
        # 写入更新后的配置
        with open(temp_config_file, "w", encoding="utf-8") as f:
            if not has_temp_dir:
                # 添加临时目录配置
                f.write(f"""# 临时文件目录 - 由optimize_nine_cats.py添加
TEMP_DIR = "{temp_dir.replace('\\', '\\\\')}"
CACHE_DIR = "{os.path.join(temp_dir, 'cache').replace('\\', '\\\\')}"
LOG_DIR = "{os.path.join(temp_dir, 'logs').replace('\\', '\\\\')}"
ANALYSIS_TEMP_DIR = "{os.path.join(temp_dir, 'analysis').replace('\\', '\\\\')}"

# 内存优化设置 - 由optimize_nine_cats.py添加
CHUNK_SIZE = 1024 * 1024  # 1MB
MAX_CACHE_SIZE = 100 * 1024 * 1024  # 100MB
ENABLE_MEMORY_OPTIMIZATION = True

""")
            
            # 写入原有配置
            for line in lines:
                # 替换已存在的临时目录配置
                if "TEMP_DIR" in line and "=" in line:
                    continue
                elif "CACHE_DIR" in line and "=" in line:
                    continue
                elif "LOG_DIR" in line and "=" in line:
                    continue
                elif "ANALYSIS_TEMP_DIR" in line and "=" in line:
                    continue
                elif "CHUNK_SIZE" in line and "=" in line:
                    continue
                elif "MAX_CACHE_SIZE" in line and "=" in line:
                    continue
                elif "ENABLE_MEMORY_OPTIMIZATION" in line and "=" in line:
                    continue
                else:
                    f.write(line)
        
        # 备份原配置文件
        backup_file = f"config.py.bak.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(config_file, backup_file)
        logger.info(f"备份原配置文件: {backup_file}")
        
        # 替换配置文件
        shutil.move(temp_config_file, config_file)
        logger.info(f"更新配置文件: {config_file}")
        
        return True
    except Exception as e:
        logger.error(f"更新配置文件失败: {str(e)}")
        return False

def create_temp_redirection_module():
    """创建临时文件重定向模块"""
    try:
        module_path = "temp_redirect.py"
        with open(module_path, "w", encoding="utf-8") as f:
            f.write("""# -*- coding: utf-8 -*-
\"\"\"
九猫小说分析系统 - 临时文件重定向模块
在程序启动时导入此模块，强制重定向临时文件
\"\"\"
import os
import sys
import tempfile
import logging
from pathlib import Path

# 尝试导入配置
try:
    import config
    TEMP_DIR = config.TEMP_DIR
except (ImportError, AttributeError):
    # 如果没有配置，使用默认值
    script_dir = os.path.dirname(os.path.abspath(__file__))
    TEMP_DIR = os.path.join(script_dir, "temp")

# 确保临时目录存在
if not os.path.exists(TEMP_DIR):
    try:
        os.makedirs(TEMP_DIR)
    except Exception as e:
        print(f"创建临时目录失败: {str(e)}")

# 设置环境变量
os.environ["TEMP"] = TEMP_DIR
os.environ["TMP"] = TEMP_DIR

# 重定向Python的临时文件目录
tempfile.tempdir = TEMP_DIR

# 记录重定向信息
print(f"临时文件已重定向到: {TEMP_DIR}")
""")
        logger.info(f"创建临时文件重定向模块: {module_path}")
        return True
    except Exception as e:
        logger.error(f"创建临时文件重定向模块失败: {str(e)}")
        return False

def create_memory_optimization_module():
    """创建内存优化模块"""
    try:
        module_path = "memory_optimizer.py"
        with open(module_path, "w", encoding="utf-8") as f:
            f.write("""# -*- coding: utf-8 -*-
\"\"\"
九猫小说分析系统 - 内存优化模块
提供流式处理、垃圾回收和缓存控制功能
\"\"\"
import os
import gc
import sys
import json
import logging
from functools import wraps

# 尝试导入配置
try:
    import config
    CHUNK_SIZE = getattr(config, "CHUNK_SIZE", 1024 * 1024)  # 默认1MB
    MAX_CACHE_SIZE = getattr(config, "MAX_CACHE_SIZE", 100 * 1024 * 1024)  # 默认100MB
    ENABLE_OPTIMIZATION = getattr(config, "ENABLE_MEMORY_OPTIMIZATION", True)
except (ImportError, AttributeError):
    # 默认值
    CHUNK_SIZE = 1024 * 1024  # 1MB
    MAX_CACHE_SIZE = 100 * 1024 * 1024  # 100MB
    ENABLE_OPTIMIZATION = True

def stream_file_processor(func):
    \"\"\"装饰器: 流式处理大文件的装饰器\"\"\"
    @wraps(func)
    def wrapper(file_path, *args, **kwargs):
        if not ENABLE_OPTIMIZATION:
            # 如果优化被禁用，直接调用原函数
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            return func(content, *args, **kwargs)
        
        # 流式处理
        results = []
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                while chunk := f.read(CHUNK_SIZE):
                    chunk_result = func(chunk, *args, **kwargs)
                    results.append(chunk_result)
                    # 强制垃圾回收
                    gc.collect()
            return "".join(results) if isinstance(results[0], str) else results
        except Exception as e:
            print(f"流式处理失败: {str(e)}")
            # 失败时回退到常规方法
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            return func(content, *args, **kwargs)
    return wrapper

def optimize_memory_usage():
    \"\"\"优化内存使用\"\"\"
    # 强制垃圾回收
    gc.collect()
    
    # 获取当前内存使用情况
    import psutil
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    print(f"当前内存使用: {memory_info.rss / 1024 / 1024:.2f} MB")
    return memory_info.rss

def stream_read_novel(file_path):
    \"\"\"流式读取小说文件\"\"\"
    chunks = []
    total_size = os.path.getsize(file_path)
    processed_size = 0
    
    with open(file_path, "r", encoding="utf-8") as f:
        while chunk := f.read(CHUNK_SIZE):
            yield chunk
            processed_size += len(chunk)
            progress = processed_size / total_size * 100
            print(f"读取进度: {progress:.2f}%")

def clear_cache():
    \"\"\"清理缓存\"\"\"
    gc.collect()
    print("内存缓存已清理")
""")
        logger.info(f"创建内存优化模块: {module_path}")
        return True
    except Exception as e:
        logger.error(f"创建内存优化模块失败: {str(e)}")
        return False

def update_main_script_imports():
    """更新主脚本，导入优化模块"""
    try:
        main_files = ["main.py", "app.py", "server.py"]
        main_file = None
        
        # 查找主脚本
        for file in main_files:
            if os.path.exists(file):
                main_file = file
                break
        
        if not main_file:
            logger.warning("未找到主脚本文件，跳过导入更新")
            return False
        
        # 备份主脚本
        backup_file = f"{main_file}.bak.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(main_file, backup_file)
        logger.info(f"备份主脚本: {backup_file}")
        
        # 读取主脚本
        with open(main_file, "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 检查是否已导入优化模块
        has_temp_redirect = any("import temp_redirect" in line for line in lines)
        has_memory_optimizer = any("import memory_optimizer" in line for line in lines)
        
        # 写入更新后的主脚本
        with open(f"{main_file}.new", "w", encoding="utf-8") as f:
            # 添加优化模块导入
            import_added = False
            for line in lines:
                if not import_added and "import" in line and not has_temp_redirect:
                    f.write("# 导入临时文件重定向模块 - 由optimize_nine_cats.py添加\n")
                    f.write("import temp_redirect\n\n")
                    f.write("# 导入内存优化模块 - 由optimize_nine_cats.py添加\n")
                    f.write("import memory_optimizer\n\n")
                    import_added = True
                f.write(line)
            
            # 如果没有添加导入，添加到文件末尾
            if not import_added and not has_temp_redirect:
                f.write("\n\n# 导入临时文件重定向模块 - 由optimize_nine_cats.py添加\n")
                f.write("import temp_redirect\n\n")
                f.write("# 导入内存优化模块 - 由optimize_nine_cats.py添加\n")
                f.write("import memory_optimizer\n")
        
        # 替换主脚本
        shutil.move(f"{main_file}.new", main_file)
        logger.info(f"更新主脚本: {main_file}")
        
        return True
    except Exception as e:
        logger.error(f"更新主脚本失败: {str(e)}")
        return False

def create_symlink_batch():
    """创建符号链接批处理脚本"""
    try:
        batch_path = "create_symlinks.bat"
        with open(batch_path, "w", encoding="utf-8") as f:
            f.write("""@echo off
echo 九猫小说分析系统 - 创建符号链接
echo 此脚本需要管理员权限运行

:: 检查是否为管理员
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置目标目录
set "TARGET_DIR=%~dp0temp"

:: 检查目标目录是否存在
if not exist "%TARGET_DIR%" (
    echo 创建目标目录: %TARGET_DIR%
    mkdir "%TARGET_DIR%"
)

:: 创建符号链接
echo 创建符号链接: C:\\ProgramData\\nine_cats -> %TARGET_DIR%
if exist "C:\\ProgramData\\nine_cats" (
    rmdir "C:\\ProgramData\\nine_cats"
)
mklink /J "C:\\ProgramData\\nine_cats" "%TARGET_DIR%"

echo.
echo 符号链接创建完成！
echo 现在C盘路径 C:\\ProgramData\\nine_cats 将指向 %TARGET_DIR%
echo.

pause
""")
        logger.info(f"创建符号链接批处理脚本: {batch_path}")
        return True
    except Exception as e:
        logger.error(f"创建符号链接批处理脚本失败: {str(e)}")
        return False

def update_startup_script():
    """更新启动脚本"""
    try:
        startup_files = ["启动九猫.bat", "启动九猫_GPU版.bat", "start_nine_cats.bat"]
        startup_file = None
        
        # 查找启动脚本
        for file in startup_files:
            if os.path.exists(file):
                startup_file = file
                break
        
        if not startup_file:
            logger.warning("未找到启动脚本，创建新脚本")
            startup_file = "启动九猫_优化版.bat"
        else:
            # 备份启动脚本
            backup_file = f"{startup_file}.bak.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(startup_file, backup_file)
            logger.info(f"备份启动脚本: {backup_file}")
        
        # 创建新的启动脚本
        new_startup_file = "启动九猫_优化版.bat"
        with open(new_startup_file, "w", encoding="utf-8") as f:
            f.write("""@echo off
echo 九猫小说分析系统 - 优化版

:: 设置临时目录环境变量
set "SCRIPT_DIR=%~dp0"
set "TEMP_DIR=%SCRIPT_DIR%temp"

:: 确保临时目录存在
if not exist "%TEMP_DIR%" (
    echo 创建临时目录: %TEMP_DIR%
    mkdir "%TEMP_DIR%"
)

:: 设置环境变量
set "TEMP=%TEMP_DIR%"
set "TMP=%TEMP_DIR%"

echo 临时文件已重定向到: %TEMP_DIR%
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python未安装或未添加到PATH，请安装Python
    pause
    exit /b 1
)

:: 检查依赖
python -c "import flask" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 安装依赖...
    pip install -r requirements.txt
)

:: 启动服务器
echo 启动九猫小说分析系统...
python main.py

if %ERRORLEVEL% NEQ 0 (
    echo 启动失败，请查看日志获取详细信息
    pause
    exit /b 1
) else (
    echo 服务器已关闭
    pause
)
""")
        logger.info(f"创建新的启动脚本: {new_startup_file}")
        return True
    except Exception as e:
        logger.error(f"更新启动脚本失败: {str(e)}")
        return False

def show_optimization_summary(temp_dir):
    """显示优化总结"""
    print("\n" + "="*60)
    print(" 九猫小说分析系统 - 优化完成")
    print("="*60)
    print(f"临时文件已重定向到: {temp_dir}")
    print("\n已创建以下文件:")
    print("  - temp_redirect.py (临时文件重定向模块)")
    print("  - memory_optimizer.py (内存优化模块)")
    print("  - create_symlinks.bat (符号链接创建脚本，需管理员权限)")
    print("  - 启动九猫_优化版.bat (优化版启动脚本)")
    print("\n优化效果:")
    print("  1. 临时文件不再占用C盘空间")
    print("  2. 大型小说分析时内存占用更低")
    print("  3. 分析过程更流畅，不易崩溃")
    print("\n使用方法:")
    print("  请使用 启动九猫_优化版.bat 启动系统")
    print("="*60)

def main():
    """主函数"""
    logger.info("========== 开始优化九猫小说分析系统 ==========")
    
    # 获取用户输入的临时目录路径
    print("请输入临时文件存储路径 (如 D:\\temp，留空使用当前目录下的temp文件夹):")
    temp_base = input().strip()
    
    if not temp_base:
        # 使用当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        temp_base = os.path.join(current_dir, "temp")
    
    # 创建临时目录
    temp_dir = create_temp_directory(temp_base)
    if not temp_dir:
        logger.error("创建临时目录失败，终止操作")
        return
    
    # 更新配置文件
    if update_config_with_temp_paths(temp_dir):
        logger.info("配置文件更新成功")
    else:
        logger.warning("配置文件更新失败，继续其他优化")
    
    # 创建临时文件重定向模块
    if create_temp_redirection_module():
        logger.info("临时文件重定向模块创建成功")
    else:
        logger.warning("临时文件重定向模块创建失败")
    
    # 创建内存优化模块
    if create_memory_optimization_module():
        logger.info("内存优化模块创建成功")
    else:
        logger.warning("内存优化模块创建失败")
    
    # 更新主脚本导入
    if update_main_script_imports():
        logger.info("主脚本更新成功")
    else:
        logger.warning("主脚本更新失败或不需要更新")
    
    # 创建符号链接批处理脚本
    if create_symlink_batch():
        logger.info("符号链接批处理脚本创建成功")
    else:
        logger.warning("符号链接批处理脚本创建失败")
    
    # 更新启动脚本
    if update_startup_script():
        logger.info("启动脚本更新成功")
    else:
        logger.warning("启动脚本更新失败")
    
    # 显示优化总结
    show_optimization_summary(temp_dir)
    
    logger.info("========== 九猫小说分析系统优化完成 ==========")

if __name__ == "__main__":
    main() 