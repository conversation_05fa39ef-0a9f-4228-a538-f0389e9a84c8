<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-ci</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="npm-ci">npm-ci</h1>
<span class="description">Clean install a project</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#example">Example</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#audit"><code>audit</code></a></li><li><a href="#foreground-scripts"><code>foreground-scripts</code></a></li><li><a href="#ignore-scripts"><code>ignore-scripts</code></a></li><li><a href="#script-shell"><code>script-shell</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<pre lang="bash"><code>npm ci

aliases: clean-install, ic, install-clean, isntall-clean
</code></pre>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h3 id="description">Description</h3>
<p>This command is similar to <a href="../commands/npm-install.html"><code>npm install</code></a>, except
it's meant to be used in automated environments such as test platforms,
continuous integration, and deployment -- or any situation where you want
to make sure you're doing a clean install of your dependencies.</p>
<p>The main differences between using <code>npm install</code> and <code>npm ci</code> are:</p>
<ul>
<li>The project <strong>must</strong> have an existing <code>package-lock.json</code> or
<code>npm-shrinkwrap.json</code>.</li>
<li>If dependencies in the package lock do not match those in <code>package.json</code>,
<code>npm ci</code> will exit with an error, instead of updating the package lock.</li>
<li><code>npm ci</code> can only install entire projects at a time: individual
dependencies cannot be added with this command.</li>
<li>If a <code>node_modules</code> is already present, it will be automatically removed
before <code>npm ci</code> begins its install.</li>
<li>It will never write to <code>package.json</code> or any of the package-locks:
installs are essentially frozen.</li>
</ul>
<p>NOTE: If you create your <code>package-lock.json</code> file by running <code>npm install</code>
with flags that can affect the shape of your dependency tree, such as
<code>--legacy-peer-deps</code> or <code>--install-links</code>, you <em>must</em> provide the same
flags to <code>npm ci</code> or you are likely to encounter errors. An easy way to do
this is to run, for example,
<code>npm config set legacy-peer-deps=true --location=project</code> and commit the
<code>.npmrc</code> file to your repo.</p>
<h3 id="example">Example</h3>
<p>Make sure you have a package-lock and an up-to-date install:</p>
<pre lang="bash"><code>$ cd ./my/npm/project
$ npm install
added 154 packages in 10s
$ ls | grep package-lock
</code></pre>
<p>Run <code>npm ci</code> in that project</p>
<pre lang="bash"><code>$ npm ci
added 154 packages in 5s
</code></pre>
<p>Configure Travis to build using <code>npm ci</code> instead of <code>npm install</code>:</p>
<pre lang="bash"><code># .travis.yml
install:
- npm ci
# keep the npm cache around to speed up installs
cache:
  directories:
  - "$HOME/.npm"
</code></pre>
<h3 id="configuration">Configuration</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="audit"><code>audit</code></h4>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>When "true" submit audit reports alongside the current npm command to the
default registry and all registries configured for scopes. See the
documentation for <a href="../commands/npm-audit.html"><code>npm audit</code></a> for details on what is
submitted.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="foreground-scripts"><code>foreground-scripts</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Run all build scripts (ie, <code>preinstall</code>, <code>install</code>, and <code>postinstall</code>)
scripts for installed packages in the foreground process, sharing standard
input, output, and error with the main npm process.</p>
<p>Note that this will generally make installs run slower, and be much noisier,
but can be useful for debugging.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="ignore-scripts"><code>ignore-scripts</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If true, npm does not run scripts specified in package.json files.</p>
<p>Note that commands explicitly intended to run a particular script, such as
<code>npm start</code>, <code>npm stop</code>, <code>npm restart</code>, <code>npm test</code>, and <code>npm run-script</code>
will still run their intended script if <code>ignore-scripts</code> is set, but they
will <em>not</em> run any pre- or post-scripts.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="script-shell"><code>script-shell</code></h4>
<ul>
<li>Default: '/bin/sh' on POSIX systems, 'cmd.exe' on Windows</li>
<li>Type: null or String</li>
</ul>
<p>The shell to use for scripts run with the <code>npm exec</code>, <code>npm run</code> and <code>npm init &lt;pkg&gt;</code> commands.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../configuring-npm/package-lock-json.html">package-lock.json</a></li>
</ul>
</div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-ci.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>