@echo off
echo 九猫小说分析系统 - 创建符号链接
echo 此脚本需要管理员权限运行

:: 检查是否为管理员
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 获取用户输入的目标目录
set "DEFAULT_DIR=%~dp0temp"
set "TARGET_DIR=%DEFAULT_DIR%"

echo.
echo 请选择临时文件目标目录:
echo 1. 使用默认目录: %DEFAULT_DIR%
echo 2. 指定其他目录 (例如 D:\temp)
echo.

set /p DIR_CHOICE="请输入选择 (1/2): "

if "%DIR_CHOICE%"=="2" (
    set /p TARGET_DIR="请输入目标目录完整路径: "
)

:: 确保目标目录存在
if not exist "%TARGET_DIR%" (
    echo 创建目标目录: %TARGET_DIR%
    mkdir "%TARGET_DIR%"
)

:: 定义需要创建符号链接的目录
set "LINK_DIRS=C:\ProgramData\nine_cats C:\Windows\Temp\nine_cats C:\Users\<USER>\AppData\Local\Temp\nine_cats"

:: 创建符号链接
echo.
echo 开始创建符号链接...

for %%D in (%LINK_DIRS%) do (
    echo 创建符号链接: %%D → %TARGET_DIR%
    
    :: 如果目录已存在，先删除
    if exist "%%D" (
        rmdir "%%D" 2>nul
    )
    
    :: 创建符号链接
    mklink /J "%%D" "%TARGET_DIR%"
)

echo.
echo 符号链接创建完成！
echo 现在以下C盘路径将指向 %TARGET_DIR%:
for %%D in (%LINK_DIRS%) do (
    echo - %%D
)
echo.

:: 修改系统环境变量
echo 是否修改系统环境变量 TEMP 和 TMP? (需要管理员权限)
set /p MODIFY_ENV="确认修改? (Y/N): "

if /i "%MODIFY_ENV%"=="Y" (
    echo 修改系统环境变量...
    setx TEMP "%TARGET_DIR%" /M
    setx TMP "%TARGET_DIR%" /M
    echo 系统环境变量已修改，重启后生效
)

echo.
echo 所有操作完成！
echo 现在九猫小说分析系统的临时文件将保存在 %TARGET_DIR%
echo.

pause 