/**
 * 九猫 - jQuery .on() 方法修复脚本
 * 专门解决 $(...).on is not a function 错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('[jQuery .on() 修复] 脚本已加载');

    // 检查jQuery是否已加载
    function checkJQuery() {
        if (typeof jQuery !== 'undefined') {
            console.log('[jQuery .on() 修复] jQuery已加载，版本:', jQuery.fn.jquery);
            
            // 确保$ 变量可用
            if (typeof $ === 'undefined') {
                console.log('[jQuery .on() 修复] $ 变量未定义，设置 $ = jQuery');
                window.$ = jQuery;
            }
            
            // 检查jQuery.fn.on方法是否存在
            if (typeof jQuery.fn.on !== 'function') {
                console.log('[jQuery .on() 修复] jQuery.fn.on方法不存在，添加实现');
                addOnMethod();
            } else {
                console.log('[jQuery .on() 修复] jQuery.fn.on方法已存在');
            }
            
            // 检查其他常用方法
            checkAndFixCommonMethods();
            
            return true;
        } else {
            console.error('[jQuery .on() 修复] jQuery未加载，等待jQuery加载');
            return false;
        }
    }

    // 添加.on()方法实现
    function addOnMethod() {
        jQuery.fn.on = function(events, selector, data, handler) {
            // 处理不同的参数形式
            if (typeof selector === 'function') {
                handler = selector;
                data = null;
                selector = null;
            } else if (typeof data === 'function') {
                handler = data;
                data = null;
            }

            if (!handler) {
                return this;
            }

            // 分割多个事件
            const eventList = events.split(' ');

            return this.each(function() {
                const element = this;

                eventList.forEach(function(eventName) {
                    // 处理命名空间
                    const parts = eventName.split('.');
                    const baseEvent = parts[0]; // 例如从'click.bs.button'提取'click'

                    if (selector) {
                        // 事件委托
                        element.addEventListener(baseEvent, function(event) {
                            const matches = element.querySelectorAll(selector);
                            let target = event.target;

                            while (target && target !== element) {
                                for (let i = 0; i < matches.length; i++) {
                                    if (matches[i] === target) {
                                        // 创建一个jQuery事件对象
                                        const jQueryEvent = event;
                                        jQueryEvent.delegateTarget = element;

                                        // 调用处理函数
                                        handler.call(target, jQueryEvent);
                                        return;
                                    }
                                }
                                target = target.parentNode;
                            }
                        });
                    } else {
                        // 直接绑定
                        element.addEventListener(baseEvent, function(event) {
                            // 创建一个jQuery事件对象
                            const jQueryEvent = event;
                            handler.call(element, jQueryEvent);
                        });
                    }
                });
            });
        };

        console.log('[jQuery .on() 修复] 已添加jQuery.fn.on方法');
    }

    // 检查并修复其他常用方法
    function checkAndFixCommonMethods() {
        // 检查.off()方法
        if (typeof jQuery.fn.off !== 'function') {
            jQuery.fn.off = function(events, selector, handler) {
                // 简单实现，实际上无法完全移除事件监听器
                console.log('[jQuery .on() 修复] 警告: .off()方法的简单实现无法完全移除事件监听器');
                return this;
            };
            console.log('[jQuery .on() 修复] 已添加jQuery.fn.off方法的简单实现');
        }

        // 检查.one()方法
        if (typeof jQuery.fn.one !== 'function') {
            jQuery.fn.one = function(events, selector, data, handler) {
                // 处理不同的参数形式
                if (typeof selector === 'function') {
                    handler = selector;
                    data = null;
                    selector = null;
                } else if (typeof data === 'function') {
                    handler = data;
                    data = null;
                }

                if (!handler) {
                    return this;
                }

                const self = this;
                const wrappedHandler = function(event) {
                    // 移除事件监听器
                    self.off(events, selector, wrappedHandler);
                    // 调用原始处理函数
                    return handler.call(this, event);
                };

                return this.on(events, selector, data, wrappedHandler);
            };
            console.log('[jQuery .on() 修复] 已添加jQuery.fn.one方法');
        }

        // 检查.trigger()方法
        if (typeof jQuery.fn.trigger !== 'function') {
            jQuery.fn.trigger = function(eventType) {
                return this.each(function() {
                    try {
                        // 创建并分发事件
                        const event = new Event(eventType);
                        this.dispatchEvent(event);
                    } catch (e) {
                        console.error('[jQuery .on() 修复] 触发事件失败:', e);
                    }
                });
            };
            console.log('[jQuery .on() 修复] 已添加jQuery.fn.trigger方法的简单实现');
        }

        // 检查.tab()方法 (Bootstrap特定)
        if (typeof jQuery.fn.tab !== 'function') {
            jQuery.fn.tab = function(action) {
                if (action === 'show') {
                    return this.each(function() {
                        // 尝试模拟Bootstrap的tab行为
                        if (this.getAttribute('data-bs-toggle') === 'tab') {
                            this.click();
                        }
                    });
                }
                return this;
            };
            console.log('[jQuery .on() 修复] 已添加jQuery.fn.tab方法的简单实现');
        }
    }

    // 在页面加载时检查jQuery
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkJQuery, 0);
        });
    } else {
        setTimeout(checkJQuery, 0);
    }

    // 在页面完全加载后再次检查
    window.addEventListener('load', function() {
        setTimeout(checkJQuery, 100);
    });

    // 监听jQuery加载完成事件
    document.addEventListener('jQueryLoaded', function() {
        console.log('[jQuery .on() 修复] 检测到jQuery加载完成事件');
        setTimeout(checkJQuery, 0);
    });

    // 导出全局函数，以便手动调用
    window.fixJQueryOnMethod = function() {
        return checkJQuery();
    };

    console.log('[jQuery .on() 修复] 脚本初始化完成');
})();
