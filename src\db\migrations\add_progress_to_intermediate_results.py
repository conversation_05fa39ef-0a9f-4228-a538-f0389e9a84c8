"""
数据库迁移脚本：为intermediate_results表添加progress和metadata列
"""
import logging
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from sqlalchemy import Column, Integer, JSON, MetaData, Table, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from src.db.connection import engine

logger = logging.getLogger(__name__)

def run_migration():
    """执行迁移：为intermediate_results表添加progress和metadata列"""
    try:
        # 创建元数据对象
        metadata = MetaData()
        metadata.reflect(bind=engine)

        # 检查表是否存在
        if 'intermediate_results' not in metadata.tables:
            logger.error("intermediate_results表不存在，无法执行迁移")
            return False

        # 获取表对象
        intermediate_results = metadata.tables['intermediate_results']

        # 检查列是否已存在
        existing_columns = [c.name for c in intermediate_results.columns]

        # 添加progress列
        if 'progress' not in existing_columns:
            logger.info("添加progress列到intermediate_results表")
            with engine.connect() as conn:
                conn.execute(text('ALTER TABLE intermediate_results ADD COLUMN progress INTEGER'))
                conn.commit()
        else:
            logger.info("progress列已存在")

        # 添加meta_data列
        if 'meta_data' not in existing_columns:
            logger.info("添加meta_data列到intermediate_results表")
            with engine.connect() as conn:
                conn.execute(text('ALTER TABLE intermediate_results ADD COLUMN meta_data JSON'))
                conn.commit()
        else:
            logger.info("meta_data列已存在")

        logger.info("迁移完成")
        return True
    except Exception as e:
        logger.error(f"迁移失败: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 执行迁移
    success = run_migration()

    if success:
        print("迁移成功完成")
        sys.exit(0)
    else:
        print("迁移失败，请查看日志")
        sys.exit(1)
