import os
import re

def find_html_files(start_path):
    """查找所有HTML文件"""
    html_files = []
    for root, dirs, files in os.walk(start_path):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def add_error_handler(file_path):
    """添加错误处理脚本"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 检查是否已经包含错误处理脚本
        if '<script src="/static/js/error-handler.js"' in content:
            print(f"文件 {file_path} 已包含错误处理脚本")
            return False
        
        # 在</head>标签前添加错误处理脚本
        error_handler_script = '<script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src=\'/direct-static/js/error-handler.js\';console.error(\'错误处理脚本加载失败，尝试备用路径\')"></script>\n    '
        
        if '</head>' in content:
            new_content = content.replace('</head>', error_handler_script + '</head>')
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"已在文件 {file_path} 中添加错误处理脚本")
            return True
        else:
            print(f"文件 {file_path} 中没有找到</head>标签")
            return False
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    current_dir = os.getcwd()
    html_files = find_html_files(current_dir)
    
    print(f"找到 {len(html_files)} 个HTML文件")
    
    modified_files = []
    for file_path in html_files:
        if add_error_handler(file_path):
            modified_files.append(file_path)
    
    if modified_files:
        print(f"已修改 {len(modified_files)} 个文件:")
        for file in modified_files:
            print(f"  - {file}")
    else:
        print("没有修改任何文件")

if __name__ == "__main__":
    main()
