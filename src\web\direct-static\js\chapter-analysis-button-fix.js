/**
 * 九猫 - 章节分析按钮修复脚本
 * 专门用于修复章节分析页面的分析按钮问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[章节分析按钮修复] 脚本已加载');

    // 配置
    const CONFIG = {
        autoFix: true,           // 是否自动修复
        fixDelay: 500,           // 修复延迟（毫秒）
        debug: true              // 是否启用调试日志
    };

    // 安全的日志函数
    function safeLog(message, level = 'log') {
        if (!CONFIG.debug && level === 'log') return;
        
        try {
            if (level === 'error') {
                console.error(`[章节分析按钮修复] ${message}`);
            } else if (level === 'warn') {
                console.warn(`[章节分析按钮修复] ${message}`);
            } else {
                console.log(`[章节分析按钮修复] ${message}`);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }

    // 获取当前小说ID和章节ID
    function getCurrentIds() {
        try {
            // 从URL中提取小说ID和章节ID
            const match = window.location.pathname.match(/\/novel\/(\d+)\/chapter\/(\d+)/);
            if (match) {
                return {
                    novelId: match[1],
                    chapterId: match[2]
                };
            }

            // 如果URL不匹配，尝试从页面元素中获取
            const breadcrumb = document.querySelector('.breadcrumb');
            if (breadcrumb) {
                const links = breadcrumb.querySelectorAll('a');
                for (const link of links) {
                    const href = link.getAttribute('href');
                    if (href) {
                        const novelMatch = href.match(/\/novel\/(\d+)/);
                        if (novelMatch) {
                            const chapterMatch = href.match(/\/chapter\/(\d+)/);
                            if (chapterMatch) {
                                return {
                                    novelId: novelMatch[1],
                                    chapterId: chapterMatch[1]
                                };
                            }
                        }
                    }
                }
            }

            safeLog('无法从URL或页面元素中获取小说ID和章节ID', 'warn');
            return null;
        } catch (e) {
            safeLog(`获取当前ID时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 修复开始分析按钮
    function fixStartAnalysisButton() {
        try {
            safeLog('开始修复开始分析按钮');
            
            // 获取当前小说ID和章节ID
            const ids = getCurrentIds();
            if (!ids) {
                safeLog('无法获取当前小说ID和章节ID，跳过修复', 'warn');
                return;
            }
            
            // 查找开始分析按钮
            const startAnalysisBtn = document.getElementById('startAnalysisBtn');
            if (!startAnalysisBtn) {
                safeLog('找不到开始分析按钮，跳过修复', 'warn');
                return;
            }
            
            safeLog('找到开始分析按钮，添加事件处理');
            
            // 移除原有的事件监听器
            const newButton = startAnalysisBtn.cloneNode(true);
            startAnalysisBtn.parentNode.replaceChild(newButton, startAnalysisBtn);
            
            // 添加新的事件监听器
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                safeLog('点击开始分析按钮');
                
                // 获取选中的维度
                const dimensionCheckboxes = document.querySelectorAll('.dimension-checkbox:checked');
                if (dimensionCheckboxes.length === 0) {
                    alert('请至少选择一个维度进行分析');
                    return;
                }
                
                // 获取选中的维度值
                const dimensions = Array.from(dimensionCheckboxes).map(checkbox => checkbox.value);
                safeLog(`选中的维度: ${dimensions.join(', ')}`);
                
                // 获取选中的模型
                const modelSelect = document.getElementById('modelSelect');
                const model = modelSelect ? modelSelect.value : 'deepseek-r1';
                safeLog(`选中的模型: ${model}`);
                
                // 禁用按钮，显示加载状态
                newButton.disabled = true;
                newButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 分析中...';
                
                // 隐藏模态框
                const modal = document.getElementById('analyzeModal');
                if (modal) {
                    // 使用Bootstrap的方法隐藏模态框
                    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                        const bsModal = bootstrap.Modal.getInstance(modal);
                        if (bsModal) {
                            bsModal.hide();
                        } else {
                            // 手动隐藏
                            modal.style.display = 'none';
                            modal.classList.remove('show');
                            document.body.classList.remove('modal-open');
                            
                            // 移除背景遮罩
                            const backdrop = document.querySelector('.modal-backdrop');
                            if (backdrop) {
                                backdrop.parentNode.removeChild(backdrop);
                            }
                        }
                    } else {
                        // 手动隐藏
                        modal.style.display = 'none';
                        modal.classList.remove('show');
                        document.body.classList.remove('modal-open');
                        
                        // 移除背景遮罩
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {
                            backdrop.parentNode.removeChild(backdrop);
                        }
                    }
                }
                
                // 调用API进行分析
                fetch(`/api/novel/${ids.novelId}/chapter/${ids.chapterId}/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dimension: dimensions[0], // 目前只支持单个维度
                        model: model
                    })
                })
                .then(response => response.json())
                .then(data => {
                    safeLog('分析API返回结果:', data);
                    
                    // 恢复按钮状态
                    newButton.disabled = false;
                    newButton.textContent = '开始分析';
                    
                    if (data.success) {
                        safeLog('分析成功，刷新页面');
                        
                        // 显示成功消息
                        alert('分析成功，页面将刷新以显示结果');
                        
                        // 刷新页面
                        window.location.reload();
                    } else {
                        safeLog(`分析失败: ${data.error}`, 'error');
                        
                        // 显示错误消息
                        alert(`分析失败: ${data.error}`);
                    }
                })
                .catch(error => {
                    safeLog(`分析请求出错: ${error.message}`, 'error');
                    
                    // 恢复按钮状态
                    newButton.disabled = false;
                    newButton.textContent = '开始分析';
                    
                    // 显示错误消息
                    alert(`分析请求出错: ${error.message}`);
                });
            });
            
            safeLog('成功修复开始分析按钮');
        } catch (e) {
            safeLog(`修复开始分析按钮时出错: ${e.message}`, 'error');
        }
    }

    // 初始化
    function initialize() {
        safeLog('初始化章节分析按钮修复脚本');
        
        // 检查当前页面是否是章节详情页面
        if (!window.location.pathname.match(/\/novel\/\d+\/chapter\/\d+\/?$/)) {
            safeLog('当前页面不是章节详情页面，跳过初始化');
            return;
        }
        
        // 自动修复
        if (CONFIG.autoFix) {
            safeLog('启用自动修复');
            setTimeout(fixStartAnalysisButton, CONFIG.fixDelay);
        }
        
        // 导出全局函数
        window.chapterAnalysisButtonFix = {
            fixStartAnalysisButton
        };
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    safeLog('脚本加载完成');
})();
