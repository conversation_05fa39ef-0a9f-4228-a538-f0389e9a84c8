"""
数据库迁移脚本：为novels表添加file_name列
"""
import os
import sys
import logging
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from src.db.connection import engine, Session
from sqlalchemy import text

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migration():
    """执行迁移：为novels表添加file_name列"""
    logger.info("开始迁移：为novels表添加file_name列")
    
    # 检查列是否已存在
    check_sql = text("PRAGMA table_info(novels)")
    
    with engine.connect() as conn:
        result = conn.execute(check_sql)
        columns = [row[1] for row in result]
        
        if 'file_name' in columns:
            logger.info("file_name列已存在，无需迁移")
            return
        
        # 添加列
        try:
            alter_sql = text("ALTER TABLE novels ADD COLUMN file_name TEXT")
            conn.execute(alter_sql)
            conn.commit()
            logger.info("成功添加file_name列")
            
            # 更新现有记录，从file_path提取file_name
            update_sql = text("""
                UPDATE novels 
                SET file_name = SUBSTR(file_path, INSTR(file_path, '/')+1) 
                WHERE file_path IS NOT NULL AND file_name IS NULL
            """)
            conn.execute(update_sql)
            conn.commit()
            logger.info("成功更新现有记录的file_name")
        except Exception as e:
            logger.error(f"添加列时出错: {str(e)}")
            raise

if __name__ == "__main__":
    try:
        run_migration()
        logger.info("迁移完成")
    except Exception as e:
        logger.error(f"迁移失败: {str(e)}")
        sys.exit(1) 