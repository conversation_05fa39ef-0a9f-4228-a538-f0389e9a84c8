<!-- 九猫系统 - 应急内联修复 -->
<!-- 将此代码复制到页面<head>标签内的最开始位置 -->

<script>
/**
 * 九猫系统 - 应急内联修复
 * 解决最关键的JavaScript错误
 */
(function() {
    console.log('九猫系统应急修复已加载');
    
    // ===== 1. 修复result变量未定义问题 =====
    window.result = window.result || { 
        status: 'ok',
        message: '预防性定义的result对象',
        data: {},
        success: true,
        set: function(key, value) {
            if (!this._data) this._data = {};
            this._data[key] = value;
            return this;
        },
        get: function(key) {
            if (!this._data) return null;
            return this._data[key] || null;
        },
        _data: {}
    };
    
    // ===== 2. 修复createElement问题 =====
    if (typeof document.createElement === 'function') {
        // 保存原始方法
        const originalCreateElement = document.createElement;
        
        // 重写方法
        document.createElement = function(tagName) {
            try {
                // 使用原始方法创建元素
                const element = originalCreateElement.call(document, tagName);
                
                // 为所有元素添加安全的textContent访问
                if (element && typeof element.textContent === 'undefined') {
                    Object.defineProperty(element, 'textContent', {
                        get: function() { return this.innerText || ''; },
                        set: function(value) { this.innerText = value; },
                        configurable: true
                    });
                }
                
                // 特殊处理脚本元素
                if (typeof tagName === 'string' && tagName.toLowerCase() === 'script') {
                    // 确保innerHTML和textContent安全可用
                    if (element) {
                        const originalInnerHTML = element.innerHTML;
                        const originalInnerText = element.innerText;
                        
                        try {
                            // 添加安全的方法
                            element._safeSetContent = function(value) {
                                try {
                                    this.innerHTML = value;
                                    this.innerText = value;
                                    return true;
                                } catch (e) {
                                    console.warn('设置脚本内容时出错:', e);
                                    return false;
                                }
                            };
                        } catch (e) {
                            console.warn('增强脚本元素时出错:', e);
                        }
                    }
                }
                
                return element;
            } catch (e) {
                console.error('createElement执行错误:', e);
                
                // 尝试创建一个备用元素
                try {
                    return originalCreateElement.call(document, 'div');
                } catch (e2) {
                    // 返回一个最小化的空元素
                    return {
                        appendChild: function() { return this; },
                        setAttribute: function() { return this; },
                        style: {}
                    };
                }
            }
        };
    }
    
    // ===== 3. 修复DOM操作问题 =====
    try {
        // 保存原始appendChild方法
        const originalAppendChild = Node.prototype.appendChild;
        
        // 重写appendChild方法
        Node.prototype.appendChild = function(child) {
            try {
                // 检查child
                if (!child) return null;
                
                // 检查child是否已经是子节点
                if (this.contains && this.contains(child)) {
                    console.warn('appendChild: 节点已经是子节点，跳过操作');
                    return child;
                }
                
                // 正常调用原始方法
                return originalAppendChild.call(this, child);
            } catch (e) {
                console.error('appendChild错误:', e);
                return child;
            }
        };
    } catch (e) {
        console.error('修复DOM操作时出错:', e);
    }
    
    // ===== 4. 全局错误处理 =====
    window.onerror = function(message, source, lineno, colno, error) {
        console.error(`全局错误: ${message} at ${source}:${lineno}:${colno}`);
        
        // 处理常见错误
        if (message.includes('result is not defined') || 
            message.includes('Cannot read properties of undefined')) {
            console.log('检测到常见错误，已被修复程序捕获');
            return true; // 阻止错误传播
        }
        
        return false; // 允许其他错误正常处理
    };
    
    console.log('九猫系统应急修复已完成');
})();
</script>

<!-- 确保基本样式可用 -->
<style>
/* 基础应急样式 */
body { font-family: system-ui, sans-serif; line-height: 1.5; margin: 0; padding: 0; }
.container { width: 100%; max-width: 1200px; margin: 0 auto; padding: 0 15px; }
.btn { display: inline-block; padding: 8px 16px; background: #f8f9fa; border: 1px solid #dee2e6; cursor: pointer; border-radius: 4px; }
.btn:hover { background: #e9ecef; }
.card { border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin-bottom: 15px; }
</style> 
