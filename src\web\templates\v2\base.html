<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{% block title %}九猫小说分析系统v2.0{% endblock %}</title>

    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js" as="script">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" as="script">

    <!-- 样式表 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/v2_style.css') }}?v=1.0.0">

    <!-- 错误处理脚本 -->
    <script>
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('捕获到错误:', message, '来源:', source, '行:', lineno);
            return true; // 阻止错误冒泡
        };
    </script>

    {% block extra_css %}{% endblock %}
<script src="{{ url_for('static', filename='js/chapter-outline-display-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-outline-display-fix.js';"></script>
</head>
<body class="{% block body_class %}{% endblock %}" style="background-color: #fffdf5;">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary" style="background: linear-gradient(to right, #e6b422, #f7d26e) !important; box-shadow: 0 1px 6px rgba(230, 180, 34, 0.05);">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-cat me-2"></i>九猫小说分析系统v2.0
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v2.index') }}"><i class="fas fa-home me-1"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v2.novels') }}"><i class="fas fa-book me-1"></i> 小说列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v2.upload_novel') }}"><i class="fas fa-upload me-1"></i> 上传分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v2.dimensions') }}"><i class="fas fa-cubes me-1"></i> 分析维度</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <button class="btn btn-outline-light btn-sm me-2" id="themeToggle">
                        <i class="fas fa-moon"></i> 切换主题
                    </button>
                    <a href="{{ url_for('v2.system_monitor') }}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-server"></i> 系统监控
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container py-4">
        <!-- 消息提示区域 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="footer mt-auto py-3 bg-light" style="background-color: #fffdf5 !important; border-top: 1px solid #f7f0d7;">
        <div class="container text-center">
            <span class="text-muted">© 2025 九猫小说分析系统v2.0 | <a href="{{ url_for('v2.help') }}">帮助中心</a></span>
        </div>
    </footer>

    <!-- 基础JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <!-- 主题切换脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查本地存储中的主题设置
            const currentTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', currentTheme);

            // 更新主题切换按钮图标
            const themeIcon = document.querySelector('#themeToggle i');
            if (currentTheme === 'dark') {
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                document.body.classList.add('dark-theme');
            }

            // 主题切换按钮点击事件
            document.getElementById('themeToggle').addEventListener('click', function() {
                const theme = document.body.getAttribute('data-theme') === 'dark' ? 'light' : 'dark';
                document.body.setAttribute('data-theme', theme);
                localStorage.setItem('theme', theme);

                // 更新图标
                if (theme === 'dark') {
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                    document.body.classList.add('dark-theme');
                } else {
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                    document.body.classList.remove('dark-theme');
                }
            });
        });
    </script>

    <!-- 通用功能脚本 -->
    <script>
        // Markdown渲染函数
        function renderMarkdown(content, element) {
            if (!content || !element) return;

            try {
                // 配置marked选项
                marked.setOptions({
                    breaks: true,
                    gfm: true,
                    headerIds: true,
                    sanitize: false
                });

                // 渲染Markdown
                element.innerHTML = marked.parse(content);

                // 为所有表格添加Bootstrap类
                const tables = element.querySelectorAll('table');
                tables.forEach(table => {
                    table.classList.add('table', 'table-bordered', 'table-hover', 'table-sm');
                });

                // 为所有图片添加响应式类
                const images = element.querySelectorAll('img');
                images.forEach(img => {
                    img.classList.add('img-fluid');
                    // 添加点击放大功能
                    img.addEventListener('click', function() {
                        const modal = document.createElement('div');
                        modal.classList.add('modal', 'fade');
                        modal.innerHTML = `
                            <div class="modal-dialog modal-lg modal-dialog-centered">
                                <div class="modal-content">
                                    <div class="modal-body p-0">
                                        <img src="${this.src}" class="img-fluid">
                                    </div>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(modal);
                        $(modal).modal('show');
                        $(modal).on('hidden.bs.modal', function() {
                            document.body.removeChild(modal);
                        });
                    });
                });
            } catch (e) {
                console.error('Markdown渲染错误:', e);
                element.innerHTML = `<div class="alert alert-danger">Markdown渲染错误: ${e.message}</div>
                                     <pre>${content}</pre>`;
            }
        }

        // 加载推理过程内容
        function loadReasoningContent(url, containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;

            // 显示加载中状态
            container.innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载推理过程，请稍候...</p>
                </div>
            `;

            // 发送请求
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.reasoning_content) {
                        // 渲染推理过程
                        renderMarkdown(data.reasoning_content, container);
                    } else {
                        container.innerHTML = `
                            <div class="alert alert-info">
                                <p><i class="fas fa-info-circle"></i> 暂无推理过程数据</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载推理过程时出错:', error);
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <p><i class="fas fa-exclamation-circle"></i> 加载推理过程时出错</p>
                            <p class="small">${error.message}</p>
                        </div>
                    `;
                });
        }
    </script>

    <!-- 页面特定JS -->
    {% block extra_js %}{% endblock %}
<script src="{{ url_for('static', filename='js/chapter-outline-display-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-outline-display-fix.js';"></script>
</body>
</html>
