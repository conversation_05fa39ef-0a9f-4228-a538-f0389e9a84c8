{% extends "base.html" %}

{% block title %}错误页面{% endblock %}

{% block head %}
{{ super() }}
<style>
  .error-container {
    padding: 40px 20px;
    max-width: 800px;
    margin: 40px auto;
    text-align: center;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .error-icon {
    font-size: 64px;
    color: #dc3545;
    margin-bottom: 20px;
  }

  .error-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #343a40;
  }

  .error-message {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 30px;
  }

  .error-details {
    font-size: 14px;
    color: #6c757d;
    margin: 20px auto;
    max-width: 600px;
    text-align: left;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .back-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #4361ee;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .back-button:hover {
    background-color: #3a56d4;
    color: white;
    text-decoration: none;
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
  }
</style>
{% endblock %}

{% block content %}
<div class="container">
  <div class="error-container">
    <div class="error-icon">
      <i class="fas fa-exclamation-circle"></i>
    </div>
    <h1 class="error-title">{{ error_message|default('发生错误') }}</h1>
    <p class="error-message">抱歉，系统处理您的请求时遇到了问题</p>

    {% if error_details %}
    <div class="error-details">
      {{ error_details }}
    </div>
    {% endif %}

    <div class="mt-4">
      {% if back_url %}
        <a href="{{ back_url }}" class="back-button">
          <i class="fas fa-arrow-left mr-2"></i> 返回
        </a>
      {% else %}
        <a href="{{ url_for('index') }}" class="back-button">
          <i class="fas fa-home mr-2"></i> 返回首页
        </a>
      {% endif %}

      <button onclick="location.reload()" class="back-button ml-2" style="background-color: #28a745; border: none; cursor: pointer;">
        <i class="fas fa-sync-alt mr-2"></i> 刷新页面
      </button>
    </div>

    <div class="mt-4">
      <p class="small text-muted">
        如果问题持续存在，请联系管理员或尝试重新加载页面
      </p>
    </div>

    <div class="mt-4" id="error-recovery-options" style="display: none;">
      <h5>恢复选项</h5>
      <div class="row justify-content-center mt-3">
        <div class="col-md-4">
          <div class="card">
            <div class="card-body text-center">
              <i class="fas fa-database mb-3" style="font-size: 24px; color: #4361ee;"></i>
              <h6>重置数据库连接</h6>
              <button id="reset-db-connection" class="btn btn-sm btn-outline-primary mt-2">尝试重置</button>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card">
            <div class="card-body text-center">
              <i class="fas fa-broom mb-3" style="font-size: 24px; color: #4361ee;"></i>
              <h6>清除缓存</h6>
              <button id="clear-cache" class="btn btn-sm btn-outline-primary mt-2">清除缓存</button>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card">
            <div class="card-body text-center">
              <i class="fas fa-bug mb-3" style="font-size: 24px; color: #4361ee;"></i>
              <h6>错误诊断</h6>
              <button id="run-diagnostics" class="btn btn-sm btn-outline-primary mt-2">运行诊断</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 错误处理脚本 - 最高优先级 -->
<script src="{{ url_for('static', filename='js/error-handler.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';"></script>

<!-- 错误页面修复脚本 -->
<script src="{{ url_for('static', filename='js/error-page-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/error-page-fix.js';"></script>

<script>
  // 在页面加载完成后显示恢复选项
  document.addEventListener('DOMContentLoaded', function() {
    // 5秒后显示恢复选项
    setTimeout(function() {
      const recoveryOptions = document.getElementById('error-recovery-options');
      if (recoveryOptions) {
        recoveryOptions.style.display = 'block';
      }
    }, 5000);

    // 重置数据库连接按钮
    const resetDbButton = document.getElementById('reset-db-connection');
    if (resetDbButton) {
      resetDbButton.addEventListener('click', function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';

        // 发送重置数据库连接请求
        fetch('/api/system/reset-db-connection', {
          method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('数据库连接已重置，页面将刷新');
            location.reload();
          } else {
            alert('重置数据库连接失败: ' + data.message);
            this.disabled = false;
            this.innerHTML = '尝试重置';
          }
        })
        .catch(error => {
          console.error('重置数据库连接出错:', error);
          alert('重置数据库连接时出错，请稍后再试');
          this.disabled = false;
          this.innerHTML = '尝试重置';
        });
      });
    }

    // 清除缓存按钮
    const clearCacheButton = document.getElementById('clear-cache');
    if (clearCacheButton) {
      clearCacheButton.addEventListener('click', function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';

        // 发送清除缓存请求
        fetch('/api/system/clear-cache', {
          method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('缓存已清除，页面将刷新');
            location.reload();
          } else {
            alert('清除缓存失败: ' + data.message);
            this.disabled = false;
            this.innerHTML = '清除缓存';
          }
        })
        .catch(error => {
          console.error('清除缓存出错:', error);
          alert('清除缓存时出错，请稍后再试');
          this.disabled = false;
          this.innerHTML = '清除缓存';
        });
      });
    }

    // 运行诊断按钮
    const runDiagnosticsButton = document.getElementById('run-diagnostics');
    if (runDiagnosticsButton) {
      runDiagnosticsButton.addEventListener('click', function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';

        // 发送运行诊断请求
        fetch('/api/system/diagnostics', {
          method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // 显示诊断结果
            const resultsHtml = `
              <div class="mt-4">
                <h5>诊断结果</h5>
                <div class="error-details">
                  <p><strong>系统状态:</strong> ${data.system_status}</p>
                  <p><strong>数据库连接:</strong> ${data.database_status}</p>
                  <p><strong>内存使用:</strong> ${data.memory_usage}</p>
                  <p><strong>建议操作:</strong> ${data.recommendation}</p>
                </div>
              </div>
            `;

            // 添加到页面
            const container = document.querySelector('.error-container');
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = resultsHtml;
            container.appendChild(tempDiv.firstElementChild);

            this.disabled = false;
            this.innerHTML = '运行诊断';
          } else {
            alert('运行诊断失败: ' + data.message);
            this.disabled = false;
            this.innerHTML = '运行诊断';
          }
        })
        .catch(error => {
          console.error('运行诊断出错:', error);
          alert('运行诊断时出错，请稍后再试');
          this.disabled = false;
          this.innerHTML = '运行诊断';
        });
      });
    }
  });
</script>
{% endblock %}
