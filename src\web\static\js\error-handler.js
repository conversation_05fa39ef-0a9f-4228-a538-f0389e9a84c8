/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 错误处理模块
 * 版本: 1.3.0 - 修复最大调用堆栈超出和控制台错误显示问题
 */

// 存储原始控制台方法
const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
    debug: console.debug
};

// 错误计数器
let errorCounter = 0;

// 已处理的错误集合
const processedErrors = new Set();

// 存储原始DOM方法
const originalMethods = {
    appendChild: Node.prototype.appendChild,
    replaceChild: Node.prototype.replaceChild,
    removeChild: Node.prototype.removeChild
};

// 防止递归调用的计数器
const recursionCounter = {
    appendChild: 0,
    replaceChild: 0,
    removeChild: 0,
    maxRecursion: 2 // 允许的最大递归深度
};

// 修复DOM方法，防止递归导致堆栈溢出
function fixDomMethods() {
    // 修复appendChild
    Node.prototype.appendChild = function(child) {
        if (!child) return null;
        
        // 增加递归计数
        recursionCounter.appendChild++;
        
        try {
            // 如果递归太深，直接返回，避免堆栈溢出
            if (recursionCounter.appendChild > recursionCounter.maxRecursion) {
                console.error('DOM操作出错: Maximum call stack size exceeded');
                recursionCounter.appendChild = 0;
                return null;
            }
            
            const result = originalMethods.appendChild.call(this, child);
            recursionCounter.appendChild = 0;
            return result;
        } catch (e) {
            console.error('appendChild错误:', e.message);
            recursionCounter.appendChild = 0;
            
            if (e.message && e.message.includes('Maximum call stack size exceeded')) {
                console.error('备用方法也失败: Maximum call stack size exceeded');
                return null;
            }
            
            return null;
        }
    };
    
    // 修复replaceChild
    Node.prototype.replaceChild = function(newChild, oldChild) {
        if (!newChild || !oldChild) return null;
        
        recursionCounter.replaceChild++;
        
        try {
            if (recursionCounter.replaceChild > recursionCounter.maxRecursion) {
                console.error('DOM操作出错: Maximum call stack size exceeded');
                recursionCounter.replaceChild = 0;
                return null;
            }
            
            const result = originalMethods.replaceChild.call(this, newChild, oldChild);
            recursionCounter.replaceChild = 0;
            return result;
        } catch (e) {
            console.error('replaceChild错误:', e.message);
            recursionCounter.replaceChild = 0;
            return null;
        }
    };
}

// 安全的错误记录函数
window.safeLogError = function(message, source, lineno, colno, error) {
    try {
        // 生成错误的唯一标识
        const errorId = `${message}:${source}:${lineno}:${colno}`;

        // 如果已经处理过这个错误，不再重复处理
        if (processedErrors.has(errorId)) {
            return;
        }

        // 添加到已处理集合
        processedErrors.add(errorId);

        // 增加错误计数
        errorCounter++;

        // 在控制台中显示格式化的错误信息
        originalConsole.error(`[原始错误 #${errorCounter}] ${message}`);
        originalConsole.error(`位置: ${source} 行: ${lineno} 列: ${colno}`);
        if (error && error.stack) {
            originalConsole.error(`堆栈: ${error.stack}`);
        }
    } catch (e) {
        originalConsole.error('记录错误时出错:', e);
    }
};

// 检查是否是F12开发者工具
const isF12DevTools = window.location.href.includes('devtools://') ||
                      (window.outerWidth - window.innerWidth > 160) ||
                      (window.outerHeight - window.innerHeight > 160);

// 全局错误处理函数
window.onerror = function(message, source, lineno, colno, error) {
    // 使用安全的错误记录函数
    window.safeLogError(message, source, lineno, colno, error);

    // 如果是F12开发者工具，不阻止默认错误处理
    if (isF12DevTools) {
        originalConsole.log('检测到F12开发者工具，允许错误显示');
        return false; // 不阻止默认错误处理
    }

    return true; // 阻止默认错误处理
};

// 重写 JSON.parse 方法，添加错误处理
(function() {
    const originalJSONParse = JSON.parse;
    JSON.parse = function(text, reviver) {
        try {
            return originalJSONParse(text, reviver);
        } catch (e) {
            console.error('JSON.parse 错误:', e);
            console.error('尝试解析的文本:', text.substring(0, 100) + '...');

            // 尝试修复常见错误
            if (e instanceof SyntaxError) {
                // 检查是否缺少右括号
                if (e.message.includes('missing ) after argument list')) {
                    console.log('尝试修复缺少右括号的错误');
                    // 添加缺失的右括号
                    const fixedText = text + ')';
                    try {
                        return originalJSONParse(fixedText, reviver);
                    } catch (e2) {
                        console.error('修复尝试失败:', e2);
                    }
                }

                // 检查是否缺少右引号
                if (e.message.includes('Unexpected end of JSON input')) {
                    console.log('尝试修复JSON字符串不完整的错误');
                    // 尝试添加缺失的引号和大括号
                    let fixedText = text;
                    if (!fixedText.endsWith('}')) {
                        fixedText += '}';
                    }
                    if (!fixedText.endsWith('"}')) {
                        fixedText = fixedText.replace(/}$/, '"}');
                    }
                    try {
                        return originalJSONParse(fixedText, reviver);
                    } catch (e2) {
                        console.error('修复尝试失败:', e2);
                    }
                }
            }

            // 如果无法修复，重新抛出错误
            throw e;
        }
    };
})();

// 修复 JSON.parse 错误和DOM方法
document.addEventListener('DOMContentLoaded', function() {
    // 应用DOM修复方法
    fixDomMethods();
    
    try {
        // 查找所有包含 JSON.parse 的脚本标签
        const scripts = document.querySelectorAll('script');
        scripts.forEach(script => {
            if (script.textContent && script.textContent.includes('JSON.parse')) {
                console.log('找到包含 JSON.parse 的脚本');

                // 尝试修复 JSON.parse 调用
                const fixedContent = script.textContent.replace(
                    /JSON\.parse\('([^']*)'\)/g,
                    function(match, p1) {
                        // 确保 JSON 字符串正确闭合
                        if (!p1.endsWith("'")) {
                            return "JSON.parse('" + p1 + "')";
                        }
                        return match;
                    }
                );

                // 修复缺少右括号的 JSON.parse 调用
                const fixedContent2 = fixedContent.replace(
                    /JSON\.parse\((['"].*?['"])\s*$/g,
                    function(match, p1) {
                        console.log('修复缺少右括号的 JSON.parse 调用');
                        return match + ')';
                    }
                );

                if (fixedContent2 !== script.textContent) {
                    console.log('修复了 JSON.parse 调用');
                    // 创建新的脚本标签替换旧的
                    const newScript = document.createElement('script');
                    newScript.textContent = fixedContent2;
                    script.parentNode.replaceChild(newScript, script);
                }
            }
        });
    } catch (e) {
        console.error('修复 JSON.parse 错误时出错:', e);
    }
});

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经安装了错误处理程序
    if (window.__errorHandlerInstalled) {
        console.log('错误处理程序已安装，跳过');
        return;
    }
    
    // 标记为已安装
    window.__errorHandlerInstalled = true;
    
    // 错误计数
    const errorCounts = {
        total: 0,
        maxCallStackSize: 0,
        appendChildNull: 0,
        jQueryNotDefined: 0,
        resultNotDefined: 0
    };
    
    // 已处理的错误集合，防止重复处理相同错误
    const processedErrors = new Set();
    
    // 递归调用追踪
    const recursionTracking = {
        appendChild: 0,
        replaceChild: 0,
        insertBefore: 0,
        removeChild: 0,
        maxAllowed: 2
    };
    
    // 重置递归计数
    function resetRecursionTracking() {
        recursionTracking.appendChild = 0;
        recursionTracking.replaceChild = 0;
        recursionTracking.insertBefore = 0;
        recursionTracking.removeChild = 0;
    }
    
    // 安全地执行DOM操作
    function safelyExecuteDOMOperation(operation, node, method) {
        recursionTracking[method]++;
        
        try {
            // 检测递归深度
            if (recursionTracking[method] > recursionTracking.maxAllowed) {
                console.error(`检测到${method}可能的递归调用 (深度: ${recursionTracking[method]})`);
                recursionTracking[method] = 0;
                return null;
            }
            
            // 执行操作
            const result = operation();
            recursionTracking[method] = 0;
            return result;
        } catch (e) {
            console.error(`${method}执行出错:`, e.message);
            recursionTracking[method] = 0;
            
            // 对于堆栈溢出错误，特殊处理
            if (e.message && e.message.includes('Maximum call stack size exceeded')) {
                console.error(`${method}导致堆栈溢出，强制重置`);
                errorCounts.maxCallStackSize++;
                resetRecursionTracking();
                
                // 如果超过阈值，可以考虑恢复到原始DOM方法
                if (errorCounts.maxCallStackSize > 3) {
                    restoreOriginalDOMMethods();
                }
            }
            
            return null;
        }
    }
    
    // 保存原始DOM方法
    const originalDOMMethods = {
        appendChild: Node.prototype.appendChild,
        replaceChild: Node.prototype.replaceChild,
        insertBefore: Node.prototype.insertBefore,
        removeChild: Node.prototype.removeChild
    };
    
    // 恢复原始DOM方法
    function restoreOriginalDOMMethods() {
        console.log('恢复原始DOM方法');
        Node.prototype.appendChild = originalDOMMethods.appendChild;
        Node.prototype.replaceChild = originalDOMMethods.replaceChild;
        Node.prototype.insertBefore = originalDOMMethods.insertBefore;
        Node.prototype.removeChild = originalDOMMethods.removeChild;
        
        // 30秒后重新应用安全方法
        setTimeout(installSafeDOMMethods, 30000);
    }
    
    // 安装安全的DOM方法
    function installSafeDOMMethods() {
        console.log('安装安全的DOM方法');
        
        // 安全的appendChild
        Node.prototype.appendChild = function(child) {
            if (!child) {
                console.error('appendChild: 子节点为null或undefined');
                errorCounts.appendChildNull++;
                return null;
            }
            
            // 防止添加到自身
            if (this === child) {
                console.error('appendChild: 尝试将节点添加到自身');
                return null;
            }
            
            // 防止重复添加
            if (this.contains && typeof this.contains === 'function' && this.contains(child)) {
                console.warn('appendChild: 节点已经是子节点');
                return child;
            }
            
            return safelyExecuteDOMOperation(() => {
                return originalDOMMethods.appendChild.call(this, child);
            }, child, 'appendChild');
        };
        
        // 安全的replaceChild
        Node.prototype.replaceChild = function(newChild, oldChild) {
            if (!newChild) {
                console.error('replaceChild: 新子节点为null或undefined');
                return null;
            }
            
            if (!oldChild) {
                console.error('replaceChild: 旧子节点为null或undefined');
                return null;
            }
            
            // 防止替换相同节点
            if (newChild === oldChild) {
                console.warn('replaceChild: 新节点与旧节点相同');
                return oldChild;
            }
            
            // 确认oldChild确实是子节点
            let isChild = false;
            for (let i = 0; i < this.childNodes.length; i++) {
                if (this.childNodes[i] === oldChild) {
                    isChild = true;
                    break;
                }
            }
            
            if (!isChild) {
                console.error('replaceChild: 旧节点不是当前节点的子节点');
                return null;
            }
            
            return safelyExecuteDOMOperation(() => {
                return originalDOMMethods.replaceChild.call(this, newChild, oldChild);
            }, newChild, 'replaceChild');
        };
        
        // 安全的insertBefore
        Node.prototype.insertBefore = function(newNode, referenceNode) {
            if (!newNode) {
                console.error('insertBefore: 新节点为null或undefined');
                return null;
            }
            
            // referenceNode可以为null，表示在末尾插入
            if (referenceNode !== null) {
                // 确认referenceNode确实是子节点
                let isChild = false;
                for (let i = 0; i < this.childNodes.length; i++) {
                    if (this.childNodes[i] === referenceNode) {
                        isChild = true;
                        break;
                    }
                }
                
                if (!isChild) {
                    console.error('insertBefore: 参考节点不是当前节点的子节点');
                    return null;
                }
            }
            
            // 防止插入自身
            if (this === newNode) {
                console.error('insertBefore: 尝试将节点插入到自身前面');
                return null;
            }
            
            return safelyExecuteDOMOperation(() => {
                return originalDOMMethods.insertBefore.call(this, newNode, referenceNode);
            }, newNode, 'insertBefore');
        };
        
        // 安全的removeChild
        Node.prototype.removeChild = function(child) {
            if (!child) {
                console.error('removeChild: 子节点为null或undefined');
                return null;
            }
            
            // 确认child确实是子节点
            let isChild = false;
            for (let i = 0; i < this.childNodes.length; i++) {
                if (this.childNodes[i] === child) {
                    isChild = true;
                    break;
                }
            }
            
            if (!isChild) {
                console.error('removeChild: 节点不是当前节点的子节点');
                return null;
            }
            
            return safelyExecuteDOMOperation(() => {
                return originalDOMMethods.removeChild.call(this, child);
            }, child, 'removeChild');
        };
    }
    
    // 安装全局错误处理
    function installGlobalErrorHandlers() {
        // 存储原始onerror
        const originalOnError = window.onerror;
        
        // 设置全局onerror
        window.onerror = function(message, source, line, column, error) {
            // 生成错误ID
            const errorId = `${source}:${line}:${column}:${message}`;
            
            // 防止重复处理
            if (processedErrors.has(errorId)) {
                // 如果原始处理程序存在，调用它
                if (typeof originalOnError === 'function') {
                    return originalOnError(message, source, line, column, error);
                }
                return false;
            }
            
            // 标记为已处理
            processedErrors.add(errorId);
            errorCounts.total++;
            
            // 创建错误记录
            if (!window.errorLogs) {
                window.errorLogs = [];
            }
            
            // 记录错误，最多保存100条
            window.errorLogs.push({
                message,
                source,
                line,
                column,
                stack: error && error.stack,
                time: new Date().toISOString()
            });
            
            // 控制错误日志大小
            if (window.errorLogs.length > 100) {
                window.errorLogs.shift();
            }
            
            // 处理特定错误
            if (message) {
                // jQuery未定义错误
                if (message.includes('$ is not defined') || message.includes('jQuery is not defined')) {
                    errorCounts.jQueryNotDefined++;
                    tryLoadJQuery();
                }
                
                // 堆栈溢出错误
                if (message.includes('Maximum call stack size exceeded')) {
                    errorCounts.maxCallStackSize++;
                    resetRecursionTracking();
                    
                    // 如果堆栈溢出错误太多，临时恢复原始DOM方法
                    if (errorCounts.maxCallStackSize > 3) {
                        restoreOriginalDOMMethods();
                    }
                }
                
                // appendChild null错误
                if (message.includes('appendChild') && message.includes('null')) {
                    errorCounts.appendChildNull++;
                }

                // result未定义错误
                if (message.includes('result is not defined')) {
                    console.error('检测到result变量未定义错误');
                    errorCounts.resultNotDefined = (errorCounts.resultNotDefined || 0) + 1;
                    
                    // 尝试创建全局result变量
                    if (typeof window.result === 'undefined') {
                        window.result = { status: 'error', message: '未知错误' };
                        console.log('已创建默认的result变量作为应急处理');
                    }
                }
            }
            
            // 如果原始处理程序存在，调用它
            if (typeof originalOnError === 'function') {
                return originalOnError(message, source, line, column, error);
            }
            
            return false;
        };
        
        // 处理Promise错误
        window.addEventListener('unhandledrejection', function(event) {
            // 生成唯一错误ID
            const errorMessage = event.reason && event.reason.message ? event.reason.message : 'Unknown Promise rejection';
            const errorStack = event.reason && event.reason.stack ? event.reason.stack : '';
            const errorId = `promise:${errorMessage}:${errorStack.substring(0, 100)}`;
            
            // 防止重复处理
            if (processedErrors.has(errorId)) {
                return;
            }
            
            // 标记为已处理
            processedErrors.add(errorId);
            
            // 记录到错误日志
            if (!window.errorLogs) {
                window.errorLogs = [];
            }
            
            window.errorLogs.push({
                type: 'unhandledrejection',
                message: errorMessage,
                stack: errorStack,
                time: new Date().toISOString()
            });
            
            // 控制错误日志大小
            if (window.errorLogs.length > 100) {
                window.errorLogs.shift();
            }
            
            if (event.reason && event.reason.message) {
                // 处理堆栈溢出错误
                if (event.reason.message.includes('Maximum call stack size exceeded')) {
                    console.error('Promise中检测到堆栈溢出错误');
                    errorCounts.maxCallStackSize++;
                    resetRecursionTracking();
                    
                    // 如果堆栈溢出错误太多，临时恢复原始DOM方法
                    if (errorCounts.maxCallStackSize > 3) {
                        restoreOriginalDOMMethods();
                    }
                }
                
                // 处理result未定义错误
                if (event.reason.message.includes('result is not defined')) {
                    console.error('Promise中检测到result变量未定义错误');
                    errorCounts.resultNotDefined = (errorCounts.resultNotDefined || 0) + 1;
                    
                    // 尝试创建全局result变量
                    if (typeof window.result === 'undefined') {
                        window.result = { status: 'error', message: '未知错误' };
                        console.log('已创建默认的result变量作为应急处理');
                    }
                }
            }
        });

        // 添加全局错误数据收集定时器
        setInterval(function() {
            // 如果错误太多，尝试恢复一些基本功能
            if (errorCounts.total > 20) {
                console.warn('检测到大量错误，尝试修复基本功能');
                
                // 尝试加载核心库
                if (typeof jQuery === 'undefined') {
                    tryLoadJQuery();
                }
                
                // 尝试重置DOM方法
                restoreOriginalDOMMethods();
                setTimeout(function() {
                    installSafeDOMMethods();
                }, 1000);
                
                // 重置错误计数器
                errorCounts.total = 0;
            }
        }, 10000);
    }
    
    // 尝试加载jQuery
    function tryLoadJQuery() {
        if (typeof jQuery !== 'undefined') {
            return;
        }
        
        console.log('尝试加载jQuery');
        
        // 创建脚本元素
        const script = document.createElement('script');
        script.src = '/static/js/lib/jquery.min.js';
        script.async = false;
        
        script.onload = function() {
            console.log('jQuery加载成功');
        };
        
        script.onerror = function() {
            // 尝试备用路径
            console.warn('jQuery加载失败，尝试备用路径');
            
            const backupScript = document.createElement('script');
            backupScript.src = '/direct-static/js/lib/jquery.min.js';
            backupScript.async = false;
            
            backupScript.onload = function() {
                console.log('从备用路径加载jQuery成功');
            };
            
            backupScript.onerror = function() {
                // 尝试CDN
                console.warn('备用路径jQuery加载失败，尝试CDN');
                
                const cdnScript = document.createElement('script');
                cdnScript.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
                cdnScript.async = false;
                
                cdnScript.onload = function() {
                    console.log('从CDN加载jQuery成功');
                };
                
                cdnScript.onerror = function() {
                    console.error('所有jQuery加载尝试均失败');
                };
                
                try {
                    document.head.appendChild(cdnScript);
                } catch (e) {
                    console.error('添加jQuery CDN脚本时出错:', e);
                }
            };
            
            try {
                document.head.appendChild(backupScript);
            } catch (e) {
                console.error('添加jQuery备用脚本时出错:', e);
            }
        };
        
        try {
            document.head.appendChild(script);
        } catch (e) {
            console.error('添加jQuery脚本时出错:', e);
        }
    }
    
    // 主函数
    function main() {
        console.log('安装错误处理程序');
        
        // 安装安全的DOM方法
        installSafeDOMMethods();
        
        // 安装全局错误处理
        installGlobalErrorHandlers();
        
        // 定期检查jQuery加载
        if (document.readyState !== 'loading') {
            if (typeof jQuery === 'undefined') {
                tryLoadJQuery();
            }
        } else {
            document.addEventListener('DOMContentLoaded', function() {
                if (typeof jQuery === 'undefined') {
                    tryLoadJQuery();
                }
            });
        }
    }
    
    // 执行主函数
    main();
})();
