"""
九猫 - 分析小说脚本
通过调用API接口分析novel/4的character_relationships维度
"""

import os
import sys
import json
import logging
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def analyze_novel():
    """通过调用API接口分析novel/4的character_relationships维度"""
    logger.info("开始分析小说")
    
    # 尝试不同的API端点
    api_endpoints = [
        "http://localhost:5001/novel/4/analyze/character_relationships",
        "http://localhost:5001/api/novel/4/analyze/character_relationships",
        "http://localhost:5001/novel/analyze",
        "http://localhost:5001/analyze"
    ]
    
    # 准备请求数据
    data = {
        "novel_id": 4,
        "dimension": "character_relationships"
    }
    
    for endpoint in api_endpoints:
        try:
            logger.info(f"尝试调用API: {endpoint}")
            
            # 尝试GET请求
            get_response = requests.get(endpoint)
            
            if get_response.status_code == 200:
                logger.info(f"成功调用API (GET): {endpoint}")
                return True
            else:
                logger.warning(f"调用API失败 (GET): {endpoint} {get_response.status_code}")
            
            # 尝试POST请求
            post_response = requests.post(endpoint, json=data)
            
            if post_response.status_code == 200:
                logger.info(f"成功调用API (POST): {endpoint}")
                return True
            else:
                logger.warning(f"调用API失败 (POST): {endpoint} {post_response.status_code}")
        except Exception as e:
            logger.error(f"调用API时出错: {endpoint} {str(e)}")
    
    # 尝试直接访问页面
    try:
        logger.info("尝试直接访问页面: http://localhost:5001/novel/4")
        response = requests.get("http://localhost:5001/novel/4")
        
        if response.status_code == 200:
            logger.info("成功访问页面")
            
            # 查找分析按钮的URL
            import re
            match = re.search(r'href="([^"]+character_relationships[^"]+)"', response.text)
            
            if match:
                analyze_url = match.group(1)
                logger.info(f"找到分析URL: {analyze_url}")
                
                # 访问分析URL
                analyze_response = requests.get(f"http://localhost:5001{analyze_url}")
                
                if analyze_response.status_code == 200:
                    logger.info("成功访问分析URL")
                    return True
                else:
                    logger.warning(f"访问分析URL失败: {analyze_response.status_code}")
            else:
                logger.warning("未找到分析URL")
        else:
            logger.warning(f"访问页面失败: {response.status_code}")
    except Exception as e:
        logger.error(f"访问页面时出错: {str(e)}")
    
    return False

def main():
    """主函数"""
    logger.info("开始运行分析小说脚本")
    
    # 分析小说
    success = analyze_novel()
    
    if success:
        logger.info("分析小说成功")
        logger.info("请刷新页面以查看更改")
    else:
        logger.error("分析小说失败")

if __name__ == "__main__":
    main()
