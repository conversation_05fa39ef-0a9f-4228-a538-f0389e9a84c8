"""
九猫系统段落级推理复用机制
实现对相似段落分析推理的识别与复用
"""

import logging
import hashlib
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from pathlib import Path
import json
import pickle
import time

logger = logging.getLogger(__name__)

class ParagraphReasoningReuse:
    """段落级推理复用机制，检测并复用相似段落的分析推理"""
    
    def __init__(self, prompt_template: str = "default"):
        """
        初始化复用机制
        
        Args:
            prompt_template: 提示词模板
        """
        self.prompt_template = prompt_template
        self.config = self._load_config()
        
        # 初始化缓存目录
        self.cache_dir = Path("src/cache/paragraph_reasoning")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 性能统计
        self.stats = {
            "paragraph_matches": 0,
            "reused_reasonings": 0,
            "improved_sentences": 0,
            "processing_time_saved": 0.0
        }
        
        logger.info(f"[段落推理复用] 初始化完成，使用模板: {prompt_template}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        # 默认配置
        default_config = {
            "min_paragraph_length": 100,
            "min_similarity_threshold": 0.92 if self.prompt_template == "default" else 0.85,
            "max_cached_paragraphs": 2000 if self.prompt_template == "default" else 1000,
            "enable_paragraph_fingerprinting": True,
            "enable_semantic_matching": self.prompt_template == "default",  # 默认版启用语义匹配
            "ngram_size": 3,
            "cache_expiration": 72 if self.prompt_template == "default" else 48,  # 小时
        }
        
        # 这里可以从配置文件加载，简化实现直接返回默认配置
        return default_config
    
    def process_text(self, text: str, dimension: str) -> Dict[str, Any]:
        """
        处理文本，查找可复用的段落推理
        
        Args:
            text: 待分析文本
            dimension: 分析维度
            
        Returns:
            处理结果，包括可复用的推理
        """
        start_time = time.time()
        
        # 分割成段落
        paragraphs = self._split_into_paragraphs(text)
        
        # 保存原始段落映射
        original_mapping = {i: p for i, p in enumerate(paragraphs)}
        
        # 过滤出有意义的段落
        valid_paragraphs = self._filter_valid_paragraphs(paragraphs)
        
        # 为每个段落查找匹配
        reusable_reasoning = []
        paragraph_matches = 0
        
        for i, paragraph in valid_paragraphs.items():
            # 计算段落指纹
            fingerprint = self._compute_paragraph_fingerprint(paragraph)
            
            # 查找缓存
            cached_reasoning = self._find_cached_reasoning(paragraph, fingerprint, dimension)
            
            if cached_reasoning:
                paragraph_matches += 1
                reusable_reasoning.append({
                    "paragraph_index": i,
                    "original_paragraph": paragraph,
                    "cached_reasoning": cached_reasoning.get("reasoning", ""),
                    "similarity": cached_reasoning.get("similarity", 0.0),
                    "source": cached_reasoning.get("source", "unknown")
                })
        
        # 更新统计信息
        self.stats["paragraph_matches"] += paragraph_matches
        if paragraph_matches > 0:
            self.stats["reused_reasonings"] += 1
        
        processing_time = time.time() - start_time
        self.stats["processing_time_saved"] += processing_time if paragraph_matches > 0 else 0
        
        result = {
            "total_paragraphs": len(paragraphs),
            "valid_paragraphs": len(valid_paragraphs),
            "matched_paragraphs": paragraph_matches,
            "reusable_reasoning": reusable_reasoning,
            "processing_time": processing_time
        }
        
        logger.info(f"[段落推理复用] 维度{dimension}找到{paragraph_matches}个可复用段落推理")
        return result
    
    def _split_into_paragraphs(self, text: str) -> List[str]:
        """
        将文本分割成段落
        
        Args:
            text: 待分割文本
            
        Returns:
            段落列表
        """
        # 简单实现：按照空行分割
        paragraphs = re.split(r'\n\s*\n', text)
        
        # 过滤掉太短的段落
        return [p.strip() for p in paragraphs if p.strip()]
    
    def _filter_valid_paragraphs(self, paragraphs: List[str]) -> Dict[int, str]:
        """
        过滤出有效段落
        
        Args:
            paragraphs: 段落列表
            
        Returns:
            有效段落字典 {索引: 段落内容}
        """
        valid_paragraphs = {}
        min_length = self.config.get("min_paragraph_length", 100)
        
        for i, para in enumerate(paragraphs):
            if len(para) >= min_length:
                valid_paragraphs[i] = para
                
        return valid_paragraphs
    
    def _compute_paragraph_fingerprint(self, paragraph: str) -> Dict[str, Any]:
        """
        计算段落指纹
        
        Args:
            paragraph: 段落文本
            
        Returns:
            段落指纹
        """
        # 基础指纹：字符串哈希
        hash_value = hashlib.md5(paragraph.encode()).hexdigest()
        
        # 如果启用了段落指纹识别
        if self.config.get("enable_paragraph_fingerprinting", True):
            # N-gram特征
            ngram_size = self.config.get("ngram_size", 3)
            ngrams = self._generate_ngrams(paragraph, ngram_size)
            ngram_hash = hashlib.md5("".join(sorted(ngrams)).encode()).hexdigest()
            
            # 抽取关键词
            keywords = self._extract_keywords(paragraph)
            keyword_hash = hashlib.md5("".join(sorted(keywords)).encode()).hexdigest()
            
            return {
                "hash": hash_value,
                "ngram_hash": ngram_hash,
                "keyword_hash": keyword_hash,
                "length": len(paragraph)
            }
        else:
            return {"hash": hash_value, "length": len(paragraph)}
    
    def _generate_ngrams(self, text: str, n: int) -> Set[str]:
        """
        生成N-gram
        
        Args:
            text: 文本
            n: n-gram大小
            
        Returns:
            n-gram集合
        """
        words = re.findall(r'\w+', text.lower())
        ngrams = set()
        
        for i in range(len(words) - n + 1):
            ngram = " ".join(words[i:i+n])
            ngrams.add(ngram)
            
        return ngrams
    
    def _extract_keywords(self, text: str) -> Set[str]:
        """
        提取段落中的关键词
        
        Args:
            text: 文本
            
        Returns:
            关键词集合
        """
        # 停用词表（简化版）
        stopwords = {'的', '了', '和', '在', '是', '我', '有', '他', '她', '们', '这', '那', '就', '都'}
        
        # 提取词语并过滤停用词
        words = re.findall(r'\w+', text)
        keywords = set()
        
        for word in words:
            if len(word) > 1 and word not in stopwords:
                keywords.add(word)
                
        return keywords
    
    def _find_cached_reasoning(self, paragraph: str, fingerprint: Dict[str, Any], dimension: str) -> Optional[Dict[str, Any]]:
        """
        查找缓存的推理结果
        
        Args:
            paragraph: 段落文本
            fingerprint: 段落指纹
            dimension: 分析维度
            
        Returns:
            缓存的推理或None
        """
        # 指纹匹配策略
        if self.config.get("enable_paragraph_fingerprinting", True):
            # 先尝试直接哈希匹配
            hash_match = self._find_by_hash(fingerprint.get("hash", ""), dimension)
            if hash_match:
                return hash_match
                
            # 再尝试n-gram哈希匹配
            ngram_match = self._find_by_hash(fingerprint.get("ngram_hash", ""), dimension, "ngram")
            if ngram_match:
                return ngram_match
                
            # 最后尝试关键词匹配
            keyword_match = self._find_by_hash(fingerprint.get("keyword_hash", ""), dimension, "keyword")
            if keyword_match:
                return keyword_match
        
        # 如果启用了语义匹配（计算成本较高）
        if self.config.get("enable_semantic_matching", False):
            # 在实际实现中，这里应该调用语义匹配服务
            # 简化实现，仅作为示例
            return None
        
        return None
    
    def _find_by_hash(self, hash_value: str, dimension: str, hash_type: str = "direct") -> Optional[Dict[str, Any]]:
        """
        根据哈希值查找缓存
        
        Args:
            hash_value: 哈希值
            dimension: 分析维度
            hash_type: 哈希类型
            
        Returns:
            缓存的推理或None
        """
        if not hash_value:
            return None
        
        # 缓存文件路径
        cache_file = self.cache_dir / f"{dimension}_{hash_type}_{hash_value}.pkl"
        
        if not cache_file.exists():
            return None
            
        try:
            # 加载缓存
            with open(cache_file, "rb") as f:
                cached_data = pickle.load(f)
            
            # 检查过期时间
            expiration = self.config.get("cache_expiration", 72) * 3600  # 转换为秒
            if time.time() - cached_data.get("timestamp", 0) > expiration:
                return None
            
            # 在实际项目中，应该验证相似度
            cached_data["similarity"] = 0.95 if hash_type == "direct" else 0.9 if hash_type == "ngram" else 0.85
                
            return cached_data
        except Exception as e:
            logger.warning(f"[段落推理复用] 加载缓存失败: {e}")
            return None
    
    def save_to_cache(self, paragraph: str, reasoning: str, dimension: str, source: str = "api") -> None:
        """
        将段落推理结果保存到缓存
        
        Args:
            paragraph: 段落文本
            reasoning: 推理结果
            dimension: 分析维度
            source: 来源
        """
        if len(paragraph) < self.config.get("min_paragraph_length", 100):
            return
            
        # 计算指纹
        fingerprint = self._compute_paragraph_fingerprint(paragraph)
        
        # 准备缓存数据
        cache_data = {
            "paragraph": paragraph,
            "reasoning": reasoning,
            "timestamp": time.time(),
            "dimension": dimension,
            "source": source
        }
        
        try:
            # 保存直接哈希缓存
            direct_cache_file = self.cache_dir / f"{dimension}_direct_{fingerprint.get('hash')}.pkl"
            with open(direct_cache_file, "wb") as f:
                pickle.dump(cache_data, f)
                
            # 保存n-gram哈希缓存
            if self.config.get("enable_paragraph_fingerprinting", True):
                ngram_cache_file = self.cache_dir / f"{dimension}_ngram_{fingerprint.get('ngram_hash')}.pkl"
                with open(ngram_cache_file, "wb") as f:
                    pickle.dump(cache_data, f)
                    
                # 保存关键词哈希缓存
                keyword_cache_file = self.cache_dir / f"{dimension}_keyword_{fingerprint.get('keyword_hash')}.pkl"
                with open(keyword_cache_file, "wb") as f:
                    pickle.dump(cache_data, f)
            
            # 缓存清理（防止文件过多）
            self._clean_cache_if_needed()
                
            logger.debug(f"[段落推理复用] 已缓存维度{dimension}的段落推理")
        except Exception as e:
            logger.warning(f"[段落推理复用] 保存缓存失败: {e}")
    
    def _clean_cache_if_needed(self) -> None:
        """清理缓存（如果超出限制）"""
        try:
            # 获取所有缓存文件
            cache_files = list(self.cache_dir.glob("*.pkl"))
            
            # 检查是否超出容量限制
            max_files = self.config.get("max_cached_paragraphs", 1000) * 3  # 每个段落3个缓存文件
            if len(cache_files) <= max_files:
                return
                
            # 按修改时间排序
            cache_files.sort(key=lambda f: f.stat().st_mtime)
            
            # 删除最旧的文件
            for file in cache_files[:(len(cache_files) - max_files)]:
                file.unlink(missing_ok=True)
                
            logger.info(f"[段落推理复用] 清理了过期缓存文件")
        except Exception as e:
            logger.warning(f"[段落推理复用] 清理缓存失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.stats 