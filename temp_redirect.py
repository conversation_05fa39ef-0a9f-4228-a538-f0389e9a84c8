# -*- coding: utf-8 -*-
'''
临时文件重定向模块
将临时文件重定向到外部存储，减轻系统内存压力
'''
import os
import sys
import tempfile
import atexit
import shutil

# 检查是否使用外部存储
USE_EXTERNAL_STORAGE = os.environ.get('USE_EXTERNAL_STORAGE', '0').lower() in ('1', 'true', 'yes')

# 外部存储根目录
EXTERNAL_STORAGE_ROOT = os.environ.get('EXTERNAL_STORAGE_ROOT', 'E:\\艹，又来一次\\九猫')

# 临时目录
TEMP_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, 'temp')

# 如果使用外部存储，重定向临时目录
if USE_EXTERNAL_STORAGE:
    # 确保临时目录存在
    os.makedirs(TEMP_DIR, exist_ok=True)

    # 重定向临时目录
    tempfile.tempdir = TEMP_DIR

    # 设置环境变量
    os.environ['TMPDIR'] = TEMP_DIR
    os.environ['TEMP'] = TEMP_DIR
    os.environ['TMP'] = TEMP_DIR

    # 重定向Python的临时目录
    tempfile.gettempdir = lambda: TEMP_DIR

    # 输出信息
    print(f"临时文件已重定向到外部存储: {TEMP_DIR}")

    # 清理函数
    def cleanup_temp_files():
        """清理临时文件"""
        try:
            # 获取所有临时文件
            temp_files = []
            for root, dirs, files in os.walk(TEMP_DIR):
                for file in files:
                    if file.endswith('.tmp') or file.endswith('.temp'):
                        temp_files.append(os.path.join(root, file))

            # 删除临时文件
            for temp_file in temp_files:
                try:
                    os.remove(temp_file)
                except (PermissionError, OSError):
                    pass

            print(f"已清理 {len(temp_files)} 个临时文件")
        except Exception as e:
            print(f"清理临时文件时出错: {str(e)}")

    # 注册退出时清理临时文件
    atexit.register(cleanup_temp_files)
