import os

def manual_fix():
    # 文件路径
    template_file = 'src/web/templates/analysis.html'
    backup_file = template_file + '.manual_bak'
    
    # 确保文件存在
    if not os.path.exists(template_file):
        print(f"错误: 找不到文件 {template_file}")
        return False
    
    # 读取原始文件
    with open(template_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 创建备份
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    print(f"已创建备份: {backup_file}")
    
    # 输出关键行
    print("原始关键行:")
    for i in range(855, 865):
        if i < len(lines):
            print(f"{i+1}: {lines[i].rstrip()}")
    
    # 手动修复最后的else和endif标签
    if len(lines) >= 859:  # 确保文件有足够的行
        # 检查行858是否包含else标签
        if '{% else %}' in lines[858] or '{%else%}' in lines[858] or '{% else' in lines[858]:
            lines[858] = "{% else %}\n"
            print(f"已修复第859行: {lines[858].rstrip()}")
        
        # 检查行862是否包含endif标签
        if len(lines) >= 863 and ('{% endif %}' in lines[862] or '{%endif%}' in lines[862] or '{% endif' in lines[862]):
            lines[862] = "{% endif %}\n"
            print(f"已修复第863行: {lines[862].rstrip()}")
        
        # 写回修复后的内容
        with open(template_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print("\n修复后的关键行:")
        with open(template_file, 'r', encoding='utf-8') as f:
            updated_lines = f.readlines()
            for i in range(855, 865):
                if i < len(updated_lines):
                    print(f"{i+1}: {updated_lines[i].rstrip()}")
        
        return True
    else:
        print("文件行数不足，无法修复")
        return False

if __name__ == "__main__":
    print("九猫Jinja模板手动修复工具")
    print("-" * 30)
    success = manual_fix()
    if success:
        print("修复完成! 请重新启动九猫系统并测试。")
    else:
        print("修复失败，请尝试手动编辑文件。")
    print("-" * 30) 