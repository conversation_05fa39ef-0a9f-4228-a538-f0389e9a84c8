/**
 * 九猫 - 小说41特殊修复脚本
 * 专门针对小说ID=41的页面进行修复
 * 版本: 1.0.0
 */

(function() {
    console.log('[九猫小说41修复] 脚本已加载');

    // 检查是否是小说41页面
    function isNovel41Page() {
        return window.location.pathname.includes('/novel/41');
    }

    // 修复小说41的章节分析按钮
    function fixNovel41ChapterButtons() {
        if (!isNovel41Page()) return;

        console.log('[九猫小说41修复] 检测到小说41页面，开始修复章节分析按钮');

        // 查找所有可能的按钮
        const allElements = document.querySelectorAll('a, button, [role="button"], .btn');
        let fixedCount = 0;

        allElements.forEach(el => {
            const text = el.textContent ? el.textContent.trim().toLowerCase() : '';
            if (text.includes('章节') && 
               (text.includes('分析') || text.includes('列表') || text.includes('管理'))) {
                
                console.log('[九猫小说41修复] 找到需要修复的元素:', el);
                
                // 保存原始属性，用于日志
                const originalHref = el.getAttribute('href');
                const originalOnclick = el.onclick;
                
                // 设置href属性
                if (el.tagName === 'A') {
                    el.href = '/novel/41/chapters';
                }
                
                // 强制覆盖点击事件
                el.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('[九猫小说41修复] 拦截点击事件，跳转到章节分析页面');
                    
                    // 确保在所有其他事件处理器之后执行跳转
                    setTimeout(function() {
                        window.location.href = '/novel/41/chapters';
                    }, 10);
                    
                    return false;
                };
                
                // 记录日志
                console.log(`[九猫小说41修复] 已修复元素:
                  - 文本: ${text}
                  - 原始链接: ${originalHref || '无'}
                  - 新链接: /novel/41/chapters
                  - 有原始onclick: ${originalOnclick ? '是' : '否'}`);
                
                fixedCount++;
            }
        });
        
        console.log(`[九猫小说41修复] 共修复了 ${fixedCount} 个按钮`);
    }
    
    // 设置全局点击事件拦截
    function setupGlobalClickHandler() {
        if (!isNovel41Page()) return;
        
        // 如果已经设置了处理器，不重复设置
        if (window.__novel41ClickHandlerSet) return;
        
        document.addEventListener('click', function(e) {
            // 获取点击的元素及其祖先元素
            let target = e.target;
            while (target && target !== document) {
                // 检查是否匹配章节分析按钮
                if (target.textContent && 
                    target.textContent.toLowerCase().includes('章节') &&
                    (target.textContent.toLowerCase().includes('分析') || 
                     target.textContent.toLowerCase().includes('列表'))) {
                    
                    console.log('[九猫小说41修复] 通过全局处理器捕获到章节按钮点击');
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 延迟跳转，确保其他事件处理完成
                    setTimeout(function() {
                        window.location.href = '/novel/41/chapters';
                    }, 10);
                    
                    break;
                }
                target = target.parentNode;
            }
        }, true); // 使用捕获阶段，确保在最早阶段处理
        
        window.__novel41ClickHandlerSet = true;
        console.log('[九猫小说41修复] 已设置全局点击事件处理器');
    }
    
    // 初始化函数
    function initialize() {
        // 检查是否是小说41页面
        if (!isNovel41Page()) {
            console.log('[九猫小说41修复] 当前不是小说41页面，脚本不执行');
            return;
        }
        
        console.log('[九猫小说41修复] 初始化中...');
        
        // 修复按钮
        fixNovel41ChapterButtons();
        
        // 设置全局点击处理
        setupGlobalClickHandler();
        
        // 定期检查，处理动态加载的内容
        setInterval(fixNovel41ChapterButtons, 2000);
        
        console.log('[九猫小说41修复] 初始化完成');
    }
    
    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // 立即尝试修复一次
    setTimeout(fixNovel41ChapterButtons, 0);
    
    // 导出到全局命名空间，方便调试和手动调用
    window.novel41Fix = {
        fix: fixNovel41ChapterButtons
    };
})(); 