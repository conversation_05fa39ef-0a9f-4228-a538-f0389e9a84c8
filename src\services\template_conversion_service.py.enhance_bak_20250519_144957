"""
九猫小说分析写作系统v3.1 - 模板转换服务

此服务负责将分析结果转换为预设模板
"""
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.preset import Preset

logger = logging.getLogger(__name__)

class TemplateConversionService:
    """模板转换服务类"""
    
    @staticmethod
    def convert_to_preset_template(template_id: int, knowledge_base_data: Optional[Dict] = None) -> Dict:
        """
        将分析结果转换为预设模板
        
        Args:
            template_id: 参考蓝本ID
            knowledge_base_data: 知识库数据（可选）
        
        Returns:
            转换结果
        """
        try:
            logger.info(f"开始将参考蓝本(ID: {template_id})的分析结果转换为预设模板")
            
            session = Session()
            try:
                # 获取参考蓝本
                template = session.query(Novel).get(template_id)
                if not template:
                    logger.error(f"转换失败: 未找到ID为{template_id}的参考蓝本")
                    return {
                        'success': False,
                        'error': '未找到指定参考蓝本'
                    }
                
                # 检查是否为参考蓝本
                if not (template.novel_metadata and template.novel_metadata.get('is_template')):
                    logger.error(f"转换失败: ID为{template_id}的小说不是参考蓝本")
                    return {
                        'success': False,
                        'error': '指定的小说不是参考蓝本'
                    }
                
                # 如果没有提供知识库数据，则从数据库获取
                if not knowledge_base_data:
                    knowledge_base_data = TemplateConversionService._get_knowledge_base_data(session, template_id)
                
                # 生成整本书预设模板
                book_templates = TemplateConversionService._generate_book_templates(template, knowledge_base_data)
                
                # 生成章节预设模板
                chapter_templates = TemplateConversionService._generate_chapter_templates(template, knowledge_base_data)
                
                # 保存预设模板到数据库
                preset_id = TemplateConversionService._save_preset_templates(session, template, book_templates, chapter_templates)
                
                logger.info(f"参考蓝本《{template.title}》(ID: {template_id})的分析结果已成功转换为预设模板")
                
                return {
                    'success': True,
                    'message': f'参考蓝本《{template.title}》的分析结果已成功转换为预设模板',
                    'preset_id': preset_id,
                    'book_templates': book_templates,
                    'chapter_templates': chapter_templates
                }
            finally:
                session.close()
                logger.info(f"数据库会话已关闭")
        except Exception as e:
            logger.error(f"转换参考蓝本为预设模板时出错: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def _get_knowledge_base_data(session, template_id: int) -> Dict:
        """
        从数据库获取知识库数据
        
        Args:
            session: 数据库会话
            template_id: 参考蓝本ID
        
        Returns:
            知识库数据
        """
        logger.info(f"从数据库获取参考蓝本(ID: {template_id})的知识库数据")
        
        # 获取参考蓝本
        template = session.query(Novel).get(template_id)
        
        # 获取整本书分析结果
        book_analyses = []
        results = session.query(AnalysisResult).filter_by(novel_id=template_id).all()
        for result in results:
            book_analyses.append({
                'dimension': result.dimension,
                'result': result.content,
                'reasoning_content': result.reasoning_content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            })
        
        # 获取章节
        chapters_data = []
        chapters = session.query(Chapter).filter_by(novel_id=template_id).order_by(Chapter.chapter_number).all()
        
        # 获取章节分析结果
        for chapter in chapters:
            chapter_results = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter.id
            ).all()
            
            analysis_results = []
            for result in chapter_results:
                analysis_results.append({
                    'dimension': result.dimension,
                    'result': result.content,
                    'reasoning_content': result.reasoning_content,
                    'created_at': result.created_at.isoformat() if result.created_at else None,
                    'updated_at': result.updated_at.isoformat() if result.updated_at else None
                })
            
            chapters_data.append({
                'id': chapter.id,
                'novel_id': chapter.novel_id,
                'chapter_number': chapter.chapter_number,
                'title': chapter.title or f'第{chapter.chapter_number}章',
                'word_count': chapter.word_count,
                'analysis_results': analysis_results
            })
        
        # 构建知识库数据
        knowledge_base_data = {
            'template': {
                'id': template.id,
                'title': template.title,
                'author': template.author,
                'word_count': template.word_count,
                'created_at': template.created_at.isoformat() if template.created_at else None,
                'updated_at': template.updated_at.isoformat() if template.updated_at else None,
                'template_created_at': template.novel_metadata.get('template_created_at') if template.novel_metadata else None
            },
            'book_analyses': book_analyses,
            'chapters': chapters_data
        }
        
        logger.info(f"成功获取参考蓝本《{template.title}》的知识库数据，包含{len(book_analyses)}个整本书分析维度和{len(chapters_data)}个章节")
        
        return knowledge_base_data
    
    @staticmethod
    def _generate_book_templates(template: Novel, knowledge_base_data: Dict) -> Dict[str, str]:
        """
        生成整本书预设模板
        
        Args:
            template: 参考蓝本
            knowledge_base_data: 知识库数据
        
        Returns:
            整本书预设模板
        """
        logger.info(f"开始生成参考蓝本《{template.title}》的整本书预设模板")
        
        book_templates = {}
        
        # 获取分析维度
        book_analyses = knowledge_base_data.get('book_analyses', [])
        
        # 为每个维度生成预设模板
        for analysis in book_analyses:
            dimension = analysis.get('dimension')
            result = analysis.get('result', '')
            reasoning_content = analysis.get('reasoning_content', '')
            
            # 生成预设模板内容
            template_content = TemplateConversionService._generate_dimension_template(
                template.title,
                dimension,
                result,
                reasoning_content
            )
            
            # 添加到预设模板字典
            book_templates[dimension] = template_content
        
        logger.info(f"成功生成参考蓝本《{template.title}》的整本书预设模板，共{len(book_templates)}个维度")
        
        return book_templates
    
    @staticmethod
    def _generate_chapter_templates(template: Novel, knowledge_base_data: Dict) -> Dict[int, Dict[str, str]]:
        """
        生成章节预设模板
        
        Args:
            template: 参考蓝本
            knowledge_base_data: 知识库数据
        
        Returns:
            章节预设模板
        """
        logger.info(f"开始生成参考蓝本《{template.title}》的章节预设模板")
        
        chapter_templates = {}
        
        # 获取章节
        chapters = knowledge_base_data.get('chapters', [])
        
        # 为每个章节生成预设模板
        for chapter in chapters:
            chapter_id = chapter.get('id')
            chapter_number = chapter.get('chapter_number')
            chapter_title = chapter.get('title', f'第{chapter_number}章')
            analysis_results = chapter.get('analysis_results', [])
            
            # 为每个维度生成预设模板
            dimension_templates = {}
            for result in analysis_results:
                dimension = result.get('dimension')
                content = result.get('result', '')
                reasoning_content = result.get('reasoning_content', '')
                
                # 生成预设模板内容
                template_content = TemplateConversionService._generate_chapter_dimension_template(
                    template.title,
                    chapter_title,
                    chapter_number,
                    dimension,
                    content,
                    reasoning_content
                )
                
                # 添加到维度模板字典
                dimension_templates[dimension] = template_content
            
            # 添加到章节模板字典
            chapter_templates[chapter_id] = dimension_templates
        
        logger.info(f"成功生成参考蓝本《{template.title}》的章节预设模板，共{len(chapter_templates)}个章节")
        
        return chapter_templates
    
    @staticmethod
    def _generate_dimension_template(novel_title: str, dimension: str, result: str, reasoning_content: str) -> str:
        """
        生成维度预设模板
        
        Args:
            novel_title: 小说标题
            dimension: 分析维度
            result: 分析结果
            reasoning_content: 推理过程
        
        Returns:
            预设模板内容
        """
        # 获取维度名称
        dimension_names = {
            'language_style': '语言风格',
            'rhythm_pacing': '节奏节拍',
            'structure': '结构分析',
            'sentence_variation': '句式变化',
            'paragraph_length': '段落长度',
            'perspective_shifts': '视角变化',
            'paragraph_flow': '段落流畅度',
            'novel_characteristics': '小说特点',
            'world_building': '世界构建',
            'character_relationships': '人物关系',
            'opening_effectiveness': '开篇效果',
            'climax_pacing': '高潮节奏',
            'chapter_outline': '章纲分析',
            'outline_analysis': '大纲分析',
            'popular_tropes': '热梗统计'
        }
        dimension_name = dimension_names.get(dimension, dimension)
        
        # 构建预设模板内容
        template_content = f"# {novel_title} - {dimension_name} 预设模板\n\n"
        
        # 添加基本信息部分
        template_content += "## 基本信息\n"
        template_content += f"- 小说: {novel_title}\n"
        template_content += f"- 维度: {dimension_name}\n"
        template_content += f"- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 添加分析结果（如果有）
        if result and result.strip():
            template_content += "## 分析结果\n"
            template_content += result + "\n\n"
        
        # 添加维度说明部分
        template_content += "## 维度说明\n"
        
        # 根据不同维度添加不同的说明
        if dimension == 'language_style':
            template_content += "语言风格是指作者在写作中使用的独特表达方式，包括词汇选择、句式结构、修辞手法等。良好的语言风格能够增强作品的表现力和感染力，使读者更好地理解和感受作品内容。\n\n"
        elif dimension == 'rhythm_pacing':
            template_content += "节奏节拍是指小说情节发展的快慢变化和韵律感，包括场景转换、情节推进、紧张与舒缓的交替等。合理的节奏节拍能够保持读者的阅读兴趣，增强作品的张力和感染力。\n\n"
        elif dimension == 'structure':
            template_content += "结构分析是指小说的整体架构和组织方式，包括章节安排、情节线索、叙事框架等。良好的结构能够使作品逻辑清晰、层次分明，增强作品的整体性和连贯性。\n\n"
        elif dimension == 'sentence_variation':
            template_content += "句式变化是指作者在写作中使用的句子长度、复杂度和多样性，包括简单句、复合句、长句、短句等的交替使用。丰富的句式变化能够增强作品的节奏感和表现力，避免行文单调。\n\n"
        elif dimension == 'paragraph_length':
            template_content += "段落长度是指作者在写作中使用的段落长短变化和组织方式，包括长段落、短段落、过渡段落等的交替使用。合理的段落长度能够增强作品的可读性和节奏感，使读者更容易理解和感受作品内容。\n\n"
        else:
            template_content += f"{dimension_name}是小说分析的重要组成部分，通过对{dimension_name}的分析，可以更好地理解和把握小说的特点和风格。\n\n"
        
        # 添加预设模板部分
        template_content += "## 预设模板\n\n"
        template_content += f"### {dimension_name}模板\n\n"
        
        # 根据不同维度添加不同的模板内容
        if dimension == 'language_style':
            template_content += "#### 词汇选择\n- 常用词汇：\n- 特色词汇：\n- 专业术语：\n- 方言俚语：\n\n"
            template_content += "#### 句式结构\n- 常用句式：\n- 特色句式：\n- 句长变化：\n- 语气特点：\n\n"
            template_content += "#### 修辞手法\n- 常用修辞：\n- 特色修辞：\n- 修辞效果：\n- 修辞频率：\n\n"
            template_content += "#### 语气语调\n- 叙述语气：\n- 对话语气：\n- 情感表达：\n- 语调变化：\n\n"
        elif dimension == 'rhythm_pacing':
            template_content += "#### 场景节奏\n- 场景转换：\n- 场景长度：\n- 场景密度：\n- 场景变化：\n\n"
            template_content += "#### 情节节奏\n- 情节推进：\n- 情节密度：\n- 情节变化：\n- 情节转折：\n\n"
            template_content += "#### 紧张舒缓\n- 紧张段落：\n- 舒缓段落：\n- 节奏变化：\n- 情感波动：\n\n"
            template_content += "#### 时间流速\n- 时间压缩：\n- 时间延展：\n- 时间跳跃：\n- 时间停滞：\n\n"
        else:
            template_content += "#### 基本要素\n- 主要特点：\n- 变化规律：\n- 效果评估：\n- 创新之处：\n\n"
            template_content += "#### 应用建议\n- 适用场景：\n- 注意事项：\n- 常见问题：\n- 优化方向：\n\n"
        
        # 添加应用指南
        template_content += "## 应用指南\n\n"
        template_content += "1. 本预设模板基于知识库内容生成，可根据实际需求调整\n"
        template_content += "2. 使用时可选择性填充相关部分，不必拘泥于模板格式\n"
        template_content += "3. 建议结合参考蓝本的分析结果，进一步丰富预设内容\n"
        template_content += "4. 预设模板可导出为Markdown文件，方便离线使用和编辑\n"
        
        return template_content
    
    @staticmethod
    def _generate_chapter_dimension_template(
        novel_title: str,
        chapter_title: str,
        chapter_number: int,
        dimension: str,
        result: str,
        reasoning_content: str
    ) -> str:
        """
        生成章节维度预设模板
        
        Args:
            novel_title: 小说标题
            chapter_title: 章节标题
            chapter_number: 章节编号
            dimension: 分析维度
            result: 分析结果
            reasoning_content: 推理过程
        
        Returns:
            预设模板内容
        """
        # 获取维度名称
        dimension_names = {
            'language_style': '语言风格',
            'rhythm_pacing': '节奏节拍',
            'structure': '结构分析',
            'sentence_variation': '句式变化',
            'paragraph_length': '段落长度',
            'perspective_shifts': '视角变化',
            'paragraph_flow': '段落流畅度',
            'novel_characteristics': '小说特点',
            'world_building': '世界构建',
            'character_relationships': '人物关系',
            'opening_effectiveness': '开篇效果',
            'climax_pacing': '高潮节奏',
            'chapter_outline': '章纲分析',
            'outline_analysis': '大纲分析',
            'popular_tropes': '热梗统计'
        }
        dimension_name = dimension_names.get(dimension, dimension)
        
        # 构建预设模板内容
        template_content = f"# {novel_title} - {chapter_title} - {dimension_name} 预设模板\n\n"
        
        # 添加章节信息部分
        template_content += "## 章节信息\n"
        template_content += f"- 小说: {novel_title}\n"
        template_content += f"- 章节: {chapter_title}\n"
        template_content += f"- 章节编号: {chapter_number}\n"
        template_content += f"- 维度: {dimension_name}\n\n"
        
        # 添加分析结果（如果有）
        if result and result.strip():
            template_content += "## 分析结果\n"
            template_content += result + "\n\n"
        
        # 添加章节预设模板部分
        template_content += "## 章节预设模板\n\n"
        template_content += f"### {dimension_name}模板\n\n"
        
        # 根据不同维度添加不同的模板内容
        if dimension == 'language_style':
            template_content += "#### 词汇选择\n- 常用词汇：\n- 特色词汇：\n- 专业术语：\n- 方言俚语：\n\n"
            template_content += "#### 句式结构\n- 常用句式：\n- 特色句式：\n- 句长变化：\n- 语气特点：\n\n"
            template_content += "#### 修辞手法\n- 常用修辞：\n- 特色修辞：\n- 修辞效果：\n- 修辞频率：\n\n"
            template_content += "#### 语气语调\n- 叙述语气：\n- 对话语气：\n- 情感表达：\n- 语调变化：\n\n"
        elif dimension == 'rhythm_pacing':
            template_content += "#### 场景节奏\n- 场景转换：\n- 场景长度：\n- 场景密度：\n- 场景变化：\n\n"
            template_content += "#### 情节节奏\n- 情节推进：\n- 情节密度：\n- 情节变化：\n- 情节转折：\n\n"
            template_content += "#### 紧张舒缓\n- 紧张段落：\n- 舒缓段落：\n- 节奏变化：\n- 情感波动：\n\n"
            template_content += "#### 时间流速\n- 时间压缩：\n- 时间延展：\n- 时间跳跃：\n- 时间停滞：\n\n"
        else:
            template_content += "#### 基本要素\n- 主要特点：\n- 变化规律：\n- 效果评估：\n- 创新之处：\n\n"
            template_content += "#### 应用建议\n- 适用场景：\n- 注意事项：\n- 常见问题：\n- 优化方向：\n\n"
        
        # 添加与前后章节的关系
        template_content += "## 与前后章节的关系\n\n"
        template_content += "### 与前一章节的关系\n- 情节连接：\n- 人物发展：\n- 环境变化：\n- 主题延续：\n\n"
        template_content += "### 与后一章节的关系\n- 情节铺垫：\n- 人物伏笔：\n- 环境过渡：\n- 主题深化：\n\n"
        
        # 添加应用指南
        template_content += "## 应用指南\n\n"
        template_content += "1. 本章节预设模板基于知识库内容生成，可根据实际需求调整\n"
        template_content += "2. 使用时可选择性填充相关部分，不必拘泥于模板格式\n"
        template_content += "3. 建议结合参考蓝本的章节分析结果，进一步丰富预设内容\n"
        template_content += "4. 注意与前后章节的连贯性和一致性，确保整体风格的统一\n"
        
        return template_content
    
    @staticmethod
    def _save_preset_templates(
        session,
        template: Novel,
        book_templates: Dict[str, str],
        chapter_templates: Dict[int, Dict[str, str]]
    ) -> int:
        """
        保存预设模板到数据库
        
        Args:
            session: 数据库会话
            template: 参考蓝本
            book_templates: 整本书预设模板
            chapter_templates: 章节预设模板
        
        Returns:
            预设ID
        """
        logger.info(f"开始保存参考蓝本《{template.title}》的预设模板到数据库")
        
        # 创建预设内容
        preset_title = f"预设模板 - {template.title}"
        preset_content = f"""# 预设模板: {template.title}

## 基本信息
- 小说: {template.title}
- 作者: {template.author or '未知'}
- 总字数: {template.word_count}
- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 整本书预设模板
包含 {len(book_templates)} 个维度的预设模板

## 章节预设模板
包含 {len(chapter_templates)} 个章节的预设模板
"""
        
        # 创建预设对象
        preset = Preset(
            title=preset_title,
            content=preset_content,
            category='preset_template',
            created_at=datetime.now(),
            updated_at=datetime.now(),
            meta_info={
                'template_id': template.id,
                'template_title': template.title,
                'book_template_count': len(book_templates),
                'chapter_template_count': len(chapter_templates),
                'created_at': datetime.now().isoformat()
            }
        )
        
        # 保存预设对象
        session.add(preset)
        session.commit()
        
        # 获取预设ID
        preset_id = preset.id
        
        logger.info(f"成功保存参考蓝本《{template.title}》的预设模板到数据库，预设ID: {preset_id}")
        
        return preset_id
