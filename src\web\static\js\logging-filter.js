/**
 * 九猫 - 日志过滤脚本
 * 减少控制台中的重复日志，保留重要信息
 * 版本: 1.1.0 - 修复F12控制台错误显示问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('日志过滤脚本已加载');

    // 日志计数器
    const logCounts = {};

    // 上次日志时间
    let lastLogTime = {};

    // 日志阈值（毫秒）- 相同日志在此时间内只显示一次
    const LOG_THRESHOLD = 2000;

    // 保存原始控制台方法
    const originalConsole = {
        log: console.log,
        warn: console.warn,
        error: console.error,
        info: console.info,
        debug: console.debug
    };

    // 需要过滤的重复日志模式
    const filterPatterns = [
        '已加载',
        '开始扫描',
        '找到',
        '检查',
        '尝试加载',
        '初始化成功',
        'Chart.js已加载',
        '获取进度信息失败',
        '成功解析分析结果元数据',
        '分析结果元数据:',
        '使用从元数据构建的',
        '图表初始化完成'
    ];

    // 始终显示的重要日志模式
    const importantPatterns = [
        '错误:',
        'Error:',
        '失败',
        'Failed',
        '异常',
        'Exception',
        '警告',
        'Warning',
        '创建图表',
        '销毁图表',
        '处理时间',
        '内存使用',
        '分析完成',
        '分析开始'
    ];

    // 检查是否是重复日志
    function isRepeatLog(level, args) {
        // 将参数转换为字符串
        const logString = Array.from(args).map(arg => {
            if (typeof arg === 'object') {
                try {
                    return JSON.stringify(arg);
                } catch (e) {
                    return String(arg);
                }
            }
            return String(arg);
        }).join(' ');

        // 检查是否是重要日志
        for (const pattern of importantPatterns) {
            if (logString.includes(pattern)) {
                return false; // 重要日志始终显示
            }
        }

        // 检查是否是需要过滤的重复日志
        let shouldFilter = false;
        for (const pattern of filterPatterns) {
            if (logString.includes(pattern)) {
                shouldFilter = true;
                break;
            }
        }

        if (!shouldFilter) {
            return false; // 不需要过滤的日志
        }

        // 检查是否在阈值时间内
        const now = Date.now();
        const key = `${level}:${logString}`;

        if (lastLogTime[key] && (now - lastLogTime[key] < LOG_THRESHOLD)) {
            // 增加计数
            logCounts[key] = (logCounts[key] || 0) + 1;
            return true; // 是重复日志
        }

        // 更新最后日志时间
        lastLogTime[key] = now;

        // 如果有累积的日志，显示汇总
        if (logCounts[key]) {
            const count = logCounts[key];
            logCounts[key] = 0; // 重置计数

            // 显示汇总信息
            originalConsole.log(`[日志过滤] 上条日志重复了 ${count} 次`);
        }

        return false; // 不是重复日志
    }

    // 检查是否是F12开发者工具
    const isF12DevTools = window.location.href.includes('devtools://') ||
                          (window.outerWidth - window.innerWidth > 160) ||
                          (window.outerHeight - window.innerHeight > 160);

    // 如果是F12开发者工具，不过滤错误日志
    if (isF12DevTools) {
        console.log('检测到F12开发者工具，不过滤错误日志');

        // 重写控制台方法，但不过滤错误
        console.log = function() {
            if (!isRepeatLog('log', arguments)) {
                originalConsole.log.apply(console, arguments);
            }
        };

        console.warn = function() {
            if (!isRepeatLog('warn', arguments)) {
                originalConsole.warn.apply(console, arguments);
            }
        };

        // 错误始终显示
        console.error = function() {
            originalConsole.error.apply(console, arguments);
        };

        console.info = function() {
            if (!isRepeatLog('info', arguments)) {
                originalConsole.info.apply(console, arguments);
            }
        };

        console.debug = function() {
            if (!isRepeatLog('debug', arguments)) {
                originalConsole.debug.apply(console, arguments);
            }
        };
    } else {
        // 正常过滤模式
        console.log = function() {
            if (!isRepeatLog('log', arguments)) {
                originalConsole.log.apply(console, arguments);
            }
        };

        console.warn = function() {
            if (!isRepeatLog('warn', arguments)) {
                originalConsole.warn.apply(console, arguments);
            }
        };

        console.error = function() {
            // 错误始终显示，但仍然计数重复错误
            if (!isRepeatLog('error', arguments)) {
                originalConsole.error.apply(console, arguments);
            } else {
                // 对于重复错误，仍然要在控制台显示一个点，表示有错误发生
                originalConsole.error('.');
            }
        };

        console.info = function() {
            if (!isRepeatLog('info', arguments)) {
                originalConsole.info.apply(console, arguments);
            }
        };

        console.debug = function() {
            if (!isRepeatLog('debug', arguments)) {
                originalConsole.debug.apply(console, arguments);
            }
        };
    }

    // 定期显示累积的日志计数
    setInterval(function() {
        let hasLogs = false;

        for (const key in logCounts) {
            if (logCounts[key] > 0) {
                hasLogs = true;
                const [level, ...rest] = key.split(':');
                const message = rest.join(':');

                originalConsole.log(`[日志过滤] ${level} 级别的日志 "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" 被过滤了 ${logCounts[key]} 次`);

                logCounts[key] = 0; // 重置计数
            }
        }

        if (hasLogs) {
            originalConsole.log('[日志过滤] 日志过滤统计完成');
        }
    }, 10000);

    // 添加自定义日志方法
    console.important = function() {
        // 重要日志始终显示，并添加标记
        const args = Array.from(arguments);
        originalConsole.log.apply(console, ['[重要]', ...args]);
    };

    console.performance = function() {
        // 性能日志始终显示，并添加标记
        const args = Array.from(arguments);
        originalConsole.log.apply(console, ['[性能]', ...args]);
    };

    console.analysis = function() {
        // 分析日志始终显示，并添加标记
        const args = Array.from(arguments);
        originalConsole.log.apply(console, ['[分析]', ...args]);
    };

    // 在页面卸载前显示所有累积的日志计数
    window.addEventListener('beforeunload', function() {
        for (const key in logCounts) {
            if (logCounts[key] > 0) {
                const [level, ...rest] = key.split(':');
                const message = rest.join(':');

                originalConsole.log(`[日志过滤] ${level} 级别的日志 "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" 被过滤了 ${logCounts[key]} 次`);
            }
        }
    });

    originalConsole.log('日志过滤系统已启用，重复日志将被合并');
})();
