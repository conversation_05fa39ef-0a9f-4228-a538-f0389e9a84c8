/**
 * 九猫小说分析写作系统 - 知识库分析结果修复脚本
 * 
 * 此脚本用于修复知识库页面中分析结果不显示的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[知识库分析结果修复] 初始化...');
    
    // 配置
    const CONFIG = {
        debug: true,
        selectors: {
            knowledgeBaseContent: '#knowledgeBaseContent',
            analysisContent: '.analysis-content',
            reasoningContent: '.reasoning-content',
            dimensionItem: '.dimension-item',
            readAnalysisBtn: '#readAnalysisBtn',
            convertToTemplateBtn: '#convertToTemplateBtn'
        },
        apiPaths: {
            novelAnalysis: '/api/novel/{novelId}/analysis/{dimension}',
            novelReasoning: '/api/novel/{novelId}/analysis/{dimension}/reasoning_content',
            allAnalyses: '/api/novel/{novelId}/analysis'
        }
    };
    
    // 状态
    const STATE = {
        novelId: null,
        selectedDimension: null,
        analysisData: {},
        isLoading: false
    };
    
    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[知识库分析结果修复] ${message}`);
        }
    }
    
    // 初始化
    function init() {
        debugLog('开始初始化...');
        
        // 确保jQuery已加载
        if (typeof window.ensureJQuery !== 'function') {
            debugLog('ensureJQuery函数不存在，等待页面加载完成后再试', 'warn');
            
            // 等待页面加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', init);
                return;
            }
            
            // 尝试直接使用jQuery
            if (typeof jQuery !== 'undefined') {
                debugLog('jQuery已加载，继续初始化');
                initWithJQuery(jQuery);
            } else {
                debugLog('jQuery未加载，无法初始化', 'error');
                return;
            }
        } else {
            window.ensureJQuery(function($) {
                debugLog('jQuery已加载，继续初始化');
                initWithJQuery($);
            });
        }
    }
    
    // 使用jQuery初始化
    function initWithJQuery($) {
        debugLog('使用jQuery初始化');
        
        // 获取页面信息
        getPageInfo($);
        
        // 添加事件监听器
        addEventListeners($);
        
        // 修复已加载的分析结果
        fixLoadedAnalysisResults($);
    }
    
    // 获取页面信息
    function getPageInfo($) {
        debugLog('获取页面信息');
        
        // 尝试从URL获取信息
        const urlParams = new URLSearchParams(window.location.search);
        const novelIdFromUrl = urlParams.get('novel_id');
        
        if (novelIdFromUrl) {
            STATE.novelId = novelIdFromUrl;
            debugLog(`从URL获取小说ID: ${STATE.novelId}`);
        }
        
        // 如果无法从URL获取，尝试从DOM元素获取
        if (!STATE.novelId) {
            const $novelElement = $('[data-novel-id]');
            if ($novelElement.length > 0) {
                STATE.novelId = $novelElement.data('novel-id');
                debugLog(`从DOM元素获取小说ID: ${STATE.novelId}`);
            }
        }
        
        // 如果仍然无法获取，尝试从localStorage获取
        if (!STATE.novelId && window.localStorage) {
            const storedNovelId = localStorage.getItem('selectedNovelId');
            if (storedNovelId) {
                STATE.novelId = storedNovelId;
                debugLog(`从localStorage获取小说ID: ${STATE.novelId}`);
            }
        }
        
        // 如果已经有分析数据，尝试从中获取小说ID
        if (!STATE.novelId && window.analysisData && window.analysisData.novel) {
            STATE.novelId = window.analysisData.novel.id;
            debugLog(`从全局analysisData获取小说ID: ${STATE.novelId}`);
        }
    }
    
    // 添加事件监听器
    function addEventListeners($) {
        debugLog('添加事件监听器');
        
        // 监听读取分析结果按钮点击事件
        $(CONFIG.selectors.readAnalysisBtn).on('click', function() {
            debugLog('点击了读取分析结果按钮');
            readAnalysisResults($);
        });
        
        // 监听维度项点击事件
        $(document).on('click', CONFIG.selectors.dimensionItem, function() {
            const dimension = $(this).data('dimension');
            debugLog(`点击了维度: ${dimension}`);
            
            if (dimension) {
                STATE.selectedDimension = dimension;
                loadDimensionAnalysis($, dimension);
            }
        });
    }
    
    // 修复已加载的分析结果
    function fixLoadedAnalysisResults($) {
        debugLog('修复已加载的分析结果');
        
        // 检查知识库内容区域是否有内容
        const $knowledgeBaseContent = $(CONFIG.selectors.knowledgeBaseContent);
        
        if ($knowledgeBaseContent.length > 0) {
            const contentHtml = $knowledgeBaseContent.html();
            
            // 检查是否已经加载了分析结果
            if (contentHtml && !contentHtml.includes('请先点击"读取分析结果"按钮')) {
                debugLog('知识库内容区域已有内容，检查分析结果');
                
                // 检查分析结果区域
                const $analysisContents = $(CONFIG.selectors.analysisContent);
                
                $analysisContents.each(function() {
                    const $this = $(this);
                    const content = $this.html().trim();
                    
                    // 如果分析结果为空或显示"暂无分析结果"
                    if (!content || content === '暂无分析结果') {
                        const dimension = $this.closest('[data-dimension]').data('dimension');
                        
                        if (dimension) {
                            debugLog(`发现空分析结果，维度: ${dimension}，尝试重新加载`);
                            loadDimensionAnalysis($, dimension);
                        }
                    }
                });
            }
        }
    }
    
    // 读取分析结果
    function readAnalysisResults($) {
        debugLog('读取分析结果');
        
        if (!STATE.novelId) {
            debugLog('缺少小说ID，无法读取分析结果', 'warn');
            alert('无法获取小说ID，请先选择一个小说');
            return;
        }
        
        if (STATE.isLoading) {
            debugLog('正在加载中，请稍后再试', 'warn');
            return;
        }
        
        STATE.isLoading = true;
        
        // 显示加载中提示
        $(CONFIG.selectors.knowledgeBaseContent).html(`
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-3">正在读取分析结果，请稍候...</p>
            </div>
        `);
        
        // 构建API URL
        const apiUrl = CONFIG.apiPaths.allAnalyses.replace('{novelId}', STATE.novelId);
        
        // 添加时间戳防止缓存
        const url = `${apiUrl}?_=${Date.now()}`;
        
        // 发送请求
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                debugLog('获取到所有分析结果数据');
                
                // 缓存分析数据
                STATE.analysisData = response;
                
                // 如果全局变量存在，也更新它
                if (typeof window.analysisData !== 'undefined') {
                    window.analysisData = response;
                }
                
                // 渲染分析结果
                renderAnalysisResults($, response);
                
                STATE.isLoading = false;
            },
            error: function(xhr) {
                debugLog(`获取分析结果失败: ${xhr.status} ${xhr.statusText}`, 'error');
                
                // 显示错误信息
                $(CONFIG.selectors.knowledgeBaseContent).html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        读取分析结果失败: ${xhr.status} ${xhr.statusText}
                    </div>
                    <button id="retryReadAnalysisBtn" class="btn btn-primary">
                        <i class="fas fa-sync-alt me-1"></i>重试
                    </button>
                `);
                
                // 绑定重试按钮事件
                $('#retryReadAnalysisBtn').on('click', function() {
                    readAnalysisResults($);
                });
                
                STATE.isLoading = false;
            }
        });
    }
    
    // 渲染分析结果
    function renderAnalysisResults($, data) {
        debugLog('渲染分析结果');
        
        if (!data || !data.dimensions || data.dimensions.length === 0) {
            $(CONFIG.selectors.knowledgeBaseContent).html(`
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    未找到任何分析结果
                </div>
            `);
            return;
        }
        
        // 创建分析结果HTML
        let html = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                成功读取参考蓝本《${data.novel.title}》的分析结果
            </div>
            <div class="mb-4">
                <h5>整本书分析结果</h5>
                <div class="accordion" id="analysisAccordion">
        `;
        
        // 添加每个维度的分析结果
        data.dimensions.forEach((dimension, index) => {
            const isAnalyzed = dimension.is_analyzed;
            const badgeClass = isAnalyzed ? 'bg-success' : 'bg-secondary';
            const badgeText = isAnalyzed ? '已分析' : '未分析';
            const dimensionName = getDimensionName(dimension.key);
            
            html += `
                <div class="accordion-item" data-dimension="${dimension.key}">
                    <h2 class="accordion-header" id="heading${index}">
                        <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${index}" aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="collapse${index}">
                            ${dimensionName}
                            <span class="badge ${badgeClass} ms-2">${badgeText}</span>
                        </button>
                    </h2>
                    <div id="collapse${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" aria-labelledby="heading${index}" data-bs-parent="#analysisAccordion">
                        <div class="accordion-body">
                            <ul class="nav nav-tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="result-tab-${index}" data-bs-toggle="tab" data-bs-target="#result-${index}" type="button" role="tab" aria-controls="result-${index}" aria-selected="true">分析结果</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="reasoning-tab-${index}" data-bs-toggle="tab" data-bs-target="#reasoning-${index}" type="button" role="tab" aria-controls="reasoning-${index}" aria-selected="false">推理过程</button>
                                </li>
                            </ul>
                            <div class="tab-content mt-3">
                                <div class="tab-pane fade show active" id="result-${index}" role="tabpanel" aria-labelledby="result-tab-${index}">
                                    <div class="analysis-content markdown-body">
                                        ${isAnalyzed ? '加载中...' : '暂无分析结果'}
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="reasoning-${index}" role="tabpanel" aria-labelledby="reasoning-tab-${index}">
                                    <div class="reasoning-content markdown-body">
                                        ${isAnalyzed ? '加载中...' : '暂无推理过程'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
        
        // 更新知识库内容
        $(CONFIG.selectors.knowledgeBaseContent).html(html);
        
        // 加载第一个维度的分析结果
        if (data.dimensions.length > 0 && data.dimensions[0].is_analyzed) {
            loadDimensionAnalysis($, data.dimensions[0].key);
        }
        
        // 监听标签页切换事件
        $('.nav-tabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
            const target = $(e.target).attr('data-bs-target');
            const dimensionElement = $(e.target).closest('[data-dimension]');
            const dimension = dimensionElement.data('dimension');
            
            if (dimension) {
                if (target.includes('reasoning')) {
                    // 加载推理过程
                    loadDimensionReasoning($, dimension);
                } else {
                    // 加载分析结果
                    loadDimensionAnalysis($, dimension);
                }
            }
        });
    }
    
    // 加载维度分析结果
    function loadDimensionAnalysis($, dimension) {
        debugLog(`加载维度分析结果: ${dimension}`);
        
        if (!STATE.novelId) {
            debugLog('缺少小说ID，无法加载分析结果', 'warn');
            return;
        }
        
        // 查找对应的分析内容元素
        const $dimensionElement = $(`[data-dimension="${dimension}"]`);
        const $analysisContent = $dimensionElement.find(CONFIG.selectors.analysisContent);
        
        if ($analysisContent.length === 0) {
            debugLog(`找不到维度 ${dimension} 的分析内容元素`, 'warn');
            return;
        }
        
        // 显示加载中
        $analysisContent.html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><p class="small mt-2">加载中...</p></div>');
        
        // 构建API URL
        const apiUrl = CONFIG.apiPaths.novelAnalysis
            .replace('{novelId}', STATE.novelId)
            .replace('{dimension}', dimension);
        
        // 添加时间戳防止缓存
        const url = `${apiUrl}?_=${Date.now()}`;
        
        // 发送请求
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                debugLog(`获取到维度 ${dimension} 的分析结果数据`);
                
                // 提取结果内容
                let content = '';
                if (response.result && response.result.content) {
                    content = response.result.content;
                } else if (response.content) {
                    content = response.content;
                }
                
                if (content) {
                    // 更新分析结果显示
                    $analysisContent.html(content);
                    debugLog(`成功更新维度 ${dimension} 的分析结果内容`);
                } else {
                    $analysisContent.html('暂无分析结果');
                    debugLog(`维度 ${dimension} 的分析结果内容为空`, 'warn');
                }
            },
            error: function(xhr) {
                debugLog(`加载维度 ${dimension} 的分析结果出错: ${xhr.status} ${xhr.statusText}`, 'error');
                $analysisContent.html(`<div class="alert alert-danger">加载失败: ${xhr.status} ${xhr.statusText}</div>`);
            }
        });
    }
    
    // 加载维度推理过程
    function loadDimensionReasoning($, dimension) {
        debugLog(`加载维度推理过程: ${dimension}`);
        
        if (!STATE.novelId) {
            debugLog('缺少小说ID，无法加载推理过程', 'warn');
            return;
        }
        
        // 查找对应的推理内容元素
        const $dimensionElement = $(`[data-dimension="${dimension}"]`);
        const $reasoningContent = $dimensionElement.find(CONFIG.selectors.reasoningContent);
        
        if ($reasoningContent.length === 0) {
            debugLog(`找不到维度 ${dimension} 的推理内容元素`, 'warn');
            return;
        }
        
        // 显示加载中
        $reasoningContent.html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><p class="small mt-2">加载中...</p></div>');
        
        // 构建API URL
        const apiUrl = CONFIG.apiPaths.novelReasoning
            .replace('{novelId}', STATE.novelId)
            .replace('{dimension}', dimension);
        
        // 添加时间戳防止缓存
        const url = `${apiUrl}?_=${Date.now()}`;
        
        // 发送请求
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                debugLog(`获取到维度 ${dimension} 的推理过程数据`);
                
                // 提取推理过程内容
                let reasoningContent = '';
                if (response.reasoning_content) {
                    reasoningContent = response.reasoning_content;
                } else if (response.content) {
                    reasoningContent = response.content;
                }
                
                if (reasoningContent) {
                    // 更新推理过程显示
                    $reasoningContent.html(reasoningContent);
                    debugLog(`成功更新维度 ${dimension} 的推理过程内容`);
                } else {
                    $reasoningContent.html('暂无推理过程');
                    debugLog(`维度 ${dimension} 的推理过程内容为空`, 'warn');
                }
            },
            error: function(xhr) {
                debugLog(`加载维度 ${dimension} 的推理过程出错: ${xhr.status} ${xhr.statusText}`, 'error');
                $reasoningContent.html(`<div class="alert alert-danger">加载失败: ${xhr.status} ${xhr.statusText}</div>`);
            }
        });
    }
    
    // 获取维度名称
    function getDimensionName(key) {
        const dimensionMap = {
            'plot_structure': '情节结构',
            'character_development': '人物发展',
            'theme_exploration': '主题探索',
            'setting_worldbuilding': '场景与世界构建',
            'conflict_tension': '冲突与张力',
            'narrative_perspective': '叙事视角',
            'dialogue_voice': '对话与声音',
            'pacing_rhythm': '节奏与步调',
            'style_tone': '风格与语调',
            'symbolism_motifs': '象征与主题',
            'emotional_resonance': '情感共鸣',
            'reader_engagement': '读者参与度',
            'genre_conventions': '类型惯例',
            'chapter_structure': '章节结构',
            'overall_assessment': '整体评估',
            'outline_analysis': '章纲分析',
            'hot_meme_statistics': '热梗统计'
        };
        
        return dimensionMap[key] || key;
    }
    
    // 初始化
    init();
    
    console.log('[知识库分析结果修复] 初始化完成');
})();
