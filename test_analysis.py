"""
测试分析功能的脚本，用于验证修复后的代码。
"""
import os
import sys
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的模块
import config
from src.api.deepseek_client import DeepSeekClient
from src.models.novel import Novel
from src.api.analysis import NovelAnalyzer
from src.db.connection import Session

def test_analyze_text():
    """测试直接调用API分析文本"""
    logger.info("开始测试直接调用API分析文本")
    
    # 创建API客户端
    client = DeepSeekClient(model="deepseek-r1")
    
    # 测试文本
    test_text = """
    这是一段测试文本，用于验证API调用是否正常工作。
    这段文本应该足够长，以便API能够进行有意义的分析。
    我们将测试paragraph_flow维度的分析，这是之前出现问题的维度。
    """
    
    # 分析文本
    logger.info("开始分析文本")
    result = client.analyze_text(test_text, "paragraph_flow")
    
    # 检查结果
    if "error" in result:
        logger.error(f"分析出错: {result['error']}")
    else:
        logger.info(f"分析成功，结果类型: {result['type']}")
        logger.info(f"内容长度: {len(result.get('content', ''))}")
        logger.info(f"内容摘要: {result.get('content', '')[:100]}...")
    
    # 获取API调用统计
    stats = DeepSeekClient.get_api_call_stats()
    logger.info(f"API调用统计: {stats}")
    
    return result

def test_analyze_novel():
    """测试分析小说"""
    logger.info("开始测试分析小说")
    
    # 创建一个测试小说
    test_novel = Novel(
        title="测试小说",
        content="""
        这是一部测试小说，用于验证分析功能是否正常工作。
        这部小说应该足够长，以便API能够进行有意义的分析。
        我们将测试paragraph_flow维度的分析，这是之前出现问题的维度。
        
        第一章
        
        主角走在路上，看到了一朵花。
        "这朵花真美。"他想。
        风轻轻吹过，花瓣随风摇曳。
        
        第二章
        
        时间过去了很久，主角再次来到这个地方。
        花已经不在了，但记忆犹存。
        他坐在草地上，回忆起过去的点点滴滴。
        """,
        author="测试作者"
    )
    
    # 创建分析器
    analyzer = NovelAnalyzer(model="deepseek-r1")
    
    # 分析小说
    logger.info("开始分析小说")
    results = analyzer.analyze_novel(test_novel, ["paragraph_flow"])
    
    # 检查结果
    if "paragraph_flow" in results:
        result = results["paragraph_flow"]
        logger.info(f"分析成功，维度: paragraph_flow")
        logger.info(f"内容长度: {len(result.content)}")
        logger.info(f"内容摘要: {result.content[:100]}...")
        logger.info(f"元数据: {result.analysis_metadata}")
    else:
        logger.error("分析失败，未找到paragraph_flow维度的结果")
    
    # 获取API调用统计
    stats = DeepSeekClient.get_api_call_stats()
    logger.info(f"API调用统计: {stats}")
    
    return results

def test_intermediate_result():
    """测试中间结果缓存功能"""
    logger.info("开始测试中间结果缓存功能")
    
    # 创建API客户端
    client = DeepSeekClient(model="deepseek-r1")
    
    # 测试文本
    test_text = "这是一段测试文本，用于验证中间结果缓存功能。"
    
    # 计算哈希值
    chunk_hash = client._get_chunk_hash(test_text)
    
    # 创建测试结果
    test_result = {
        "type": "paragraph_flow",
        "content": "这是测试的分析结果内容。"
    }
    
    # 保存中间结果
    logger.info("保存中间结果")
    client._save_intermediate_result(
        novel_id=999,  # 测试ID
        chunk_index=0,
        chunk_text=test_text,
        analysis_type="paragraph_flow",
        result=test_result
    )
    
    # 获取中间结果
    logger.info("获取中间结果")
    cached_result = client._get_or_create_intermediate_result(
        novel_id=999,  # 测试ID
        chunk_index=0,
        chunk_text=test_text,
        analysis_type="paragraph_flow"
    )
    
    # 检查结果
    if cached_result:
        logger.info(f"成功获取缓存结果，类型: {cached_result.get('type')}")
        logger.info(f"内容: {cached_result.get('content')}")
    else:
        logger.warning("未找到缓存结果")
    
    return cached_result

def main():
    """主函数"""
    logger.info("开始测试")
    
    # 测试API调用
    test_analyze_text()
    
    # 测试中间结果缓存
    test_intermediate_result()
    
    # 测试小说分析
    test_analyze_novel()
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
