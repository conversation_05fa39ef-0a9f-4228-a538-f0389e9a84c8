Option Explicit

' 九猫小说分析系统优化启动脚本（完全使用外部存储）
' 将所有日志、缓存和临时数据存储到外部存储，减轻系统内存压力

' 创建Shell对象
Dim WshShell
Set WshShell = CreateObject("WScript.Shell")

' 设置环境变量
WshShell.Environment("PROCESS")("PYTHONUNBUFFERED") = "1"
WshShell.Environment("PROCESS")("FLASK_ENV") = "production"
WshShell.Environment("PROCESS")("FLASK_DEBUG") = "0"
WshShell.Environment("PROCESS")("PARALLEL_ANALYSIS") = "1"
WshShell.Environment("PROCESS")("THREAD_POOL_SIZE") = "8"
WshShell.Environment("PROCESS")("MAX_WORKERS") = "8"
WshShell.Environment("PROCESS")("DB_POOL_SIZE") = "30"
WshShell.Environment("PROCESS")("DB_MAX_OVERFLOW") = "30"
WshShell.Environment("PROCESS")("USE_EXTERNAL_STORAGE") = "1"
WshShell.Environment("PROCESS")("EXTERNAL_STORAGE_ROOT") = "E:\艹，又来一次\九猫"
WshShell.Environment("PROCESS")("REDIRECT_ALL_LOGS") = "1"
WshShell.Environment("PROCESS")("MEMORY_OPTIMIZATION") = "1"

' 显示启动信息
WScript.Echo "正在启动九猫小说分析系统（完全外部存储版）..."
WScript.Echo "使用更大的线程池和完全外部存储"
WScript.Echo "线程池大小: 8"
WScript.Echo "数据库连接池大小: 30"
WScript.Echo "数据库最大溢出连接: 30"
WScript.Echo "并行分析: 启用"
WScript.Echo "外部存储: 启用"
WScript.Echo "外部存储路径: E:\艹，又来一次\九猫"
WScript.Echo "所有日志重定向: 启用"
WScript.Echo "内存优化: 启用"
WScript.Echo ""
WScript.Echo "系统启动中，请稍候..."

' 确保外部存储目录存在
Dim FSO
Set FSO = CreateObject("Scripting.FileSystemObject")
If Not FSO.FolderExists("E:\艹，又来一次\九猫") Then
    FSO.CreateFolder("E:\艹，又来一次\九猫")
End If

' 创建子目录
If Not FSO.FolderExists("E:\艹，又来一次\九猫\logs") Then
    FSO.CreateFolder("E:\艹，又来一次\九猫\logs")
End If
If Not FSO.FolderExists("E:\艹，又来一次\九猫\cache") Then
    FSO.CreateFolder("E:\艹，又来一次\九猫\cache")
End If
If Not FSO.FolderExists("E:\艹，又来一次\九猫\temp") Then
    FSO.CreateFolder("E:\艹，又来一次\九猫\temp")
End If
If Not FSO.FolderExists("E:\艹，又来一次\九猫\memory_stats") Then
    FSO.CreateFolder("E:\艹，又来一次\九猫\memory_stats")
End If

' 启动应用
Dim Command
Command = "python main.py"
WshShell.Run Command, 1, False

' 等待3秒
WScript.Sleep 3000

' 打开浏览器
WshShell.Run "http://localhost:5001/", 1, False

' 显示完成信息
WScript.Echo "九猫小说分析系统已启动！"
WScript.Echo "请访问: http://localhost:5001/"
WScript.Echo "所有日志和缓存将保存到: E:\艹，又来一次\九猫"
WScript.Echo ""
WScript.Echo "按任意键关闭此窗口..."

' 等待用户按键
WScript.StdIn.ReadLine
