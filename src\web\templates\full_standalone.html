<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫小说分析系统</title>

    <!-- 样式表 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        body {
            font-family: "Microsoft YaHei", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1.1rem;
        }
        
        footer {
            margin-top: 3rem;
            padding: 2rem 0;
            background-color: #343a40;
            color: #fff;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">九猫小说分析系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="/"><i class="fas fa-home me-1"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/novels"><i class="fas fa-book me-1"></i> 小说列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/upload"><i class="fas fa-upload me-1"></i> 上传分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis"><i class="fas fa-chart-bar me-1"></i> 分析记录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard"><i class="fas fa-tachometer-alt me-1"></i> 数据中心</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/settings"><i class="fas fa-cog me-1"></i> 设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">九猫小说文本分析系统</h4>
                    </div>
                    <div class="card-body">
                        <p class="lead">欢迎使用九猫小说文本分析系统，这是一款专门为作家和编辑设计的文本分析工具。</p>
                        
                        <h5 class="mt-4">主要功能：</h5>
                        <ul class="list-group list-group-flush mb-4">
                            <li class="list-group-item">
                                <i class="fas fa-chart-line me-2 text-primary"></i>
                                文本高潮节奏分析
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-project-diagram me-2 text-primary"></i>
                                人物关系网络可视化
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-heart me-2 text-primary"></i>
                                情感曲线追踪
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-feather-alt me-2 text-primary"></i>
                                文体风格识别
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-sitemap me-2 text-primary"></i>
                                情节结构分析
                            </li>
                        </ul>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>提示：</strong> 点击上方导航栏中的"小说列表"按钮，查看已分析的小说或上传新小说开始分析。
                        </div>
                    </div>
                </div>
                
                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">系统优化</h5>
                    </div>
                    <div class="card-body">
                        <p>九猫小说分析系统已应用内存优化和堆栈溢出保护，确保稳定运行。</p>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">系统性能 75%</div>
                        </div>
                        <div id="optimization-status" class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            系统正常运行中...
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">快速开始</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="/dashboard" class="btn btn-success btn-lg mb-3">
                                <i class="fas fa-tachometer-alt me-1"></i> 进入数据中心
                            </a>
                            
                            <a href="/upload" class="btn btn-primary btn-lg mb-3">
                                <i class="fas fa-upload me-1"></i> 开始新的分析
                            </a>
                        </div>
                        
                        <h6 class="border-bottom pb-2 mb-3">最近的小说：</h6>
                        <ul class="list-group">
                            <li class="list-group-item text-center text-muted">
                                <i class="fas fa-book me-1"></i> 暂无小说
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">系统状态</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                系统版本
                                <span class="badge bg-primary">1.0.0</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                分析引擎
                                <span class="badge bg-success">已连接</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                数据库
                                <span class="badge bg-info">已连接</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                内存使用率
                                <span id="memory-usage" class="badge bg-warning text-dark">加载中...</span>
                            </li>
                        </ul>
                        
                        <button class="btn btn-outline-info w-100 mt-3" onclick="refreshSystemStatus()">
                            <i class="fas fa-sync-alt me-1"></i> 刷新状态
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">API 状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>DeepSeek R1 API</h6>
                                <div class="progress mb-3">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 90%" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100">可用性 90%</div>
                                </div>
                                <p class="small text-muted">API密钥: sk-9ff...f3b1</p>
                            </div>
                            <div class="col-md-6">
                                <h6>通义千问-Plus-Latest API</h6>
                                <div class="progress mb-3">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 85%" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100">可用性 85%</div>
                                </div>
                                <p class="small text-muted">API密钥: sk-6f3...278d</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white mt-5 py-3">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>九猫小说分析系统</h5>
                    <p class="small">专为作家和编辑设计的文本分析工具</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="small">版本 1.0.0 | 使用 DeepSeek R1 API</p>
                    <p class="small">© 2025 九猫系统</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 基础JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function refreshSystemStatus() {
            // 模拟刷新系统状态
            const memoryUsage = document.getElementById('memory-usage');
            memoryUsage.innerHTML = '加载中...';
            
            // 模拟API调用
            setTimeout(function() {
                const usage = Math.floor(Math.random() * 30) + 60; // 60-90%
                memoryUsage.innerHTML = usage + '%';
                
                // 根据使用率设置不同的颜色
                if (usage < 70) {
                    memoryUsage.className = 'badge bg-success';
                } else if (usage < 85) {
                    memoryUsage.className = 'badge bg-warning text-dark';
                } else {
                    memoryUsage.className = 'badge bg-danger';
                }
            }, 1000);
        }
        
        // 页面加载时刷新系统状态
        document.addEventListener('DOMContentLoaded', refreshSystemStatus);
    </script>
</body>
</html>
