/**
 * 九猫 - Chart.js内联加载器
 * 在页面加载时立即加载Chart.js
 * 版本: 1.0.0
 */

// 立即执行
(function() {
    console.log('Chart.js内联加载器已执行');
    
    // Chart.js路径列表
    const chartJsPaths = [
        '/static/js/chart.min.js',
        '/static/js/lib/chart.min.js',
        '/direct-static/js/chart.min.js',
        '/direct-static/js/lib/chart.min.js',
        'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js'
    ];
    
    // 检查Chart.js是否已加载
    if (typeof Chart !== 'undefined') {
        console.log('Chart.js已加载，跳过内联加载');
        return;
    }
    
    // 尝试加载Chart.js
    function tryLoadChart(index) {
        if (index >= chartJsPaths.length) {
            console.error('所有Chart.js路径都加载失败');
            return;
        }
        
        const path = chartJsPaths[index];
        console.log(`内联加载器: 尝试从 ${path} 加载Chart.js`);
        
        const script = document.createElement('script');
        script.src = path;
        
        script.onload = function() {
            console.log(`内联加载器: 成功从 ${path} 加载Chart.js`);
        };
        
        script.onerror = function() {
            console.warn(`内联加载器: 从 ${path} 加载Chart.js失败，尝试下一个路径`);
            tryLoadChart(index + 1);
        };
        
        document.head.appendChild(script);
    }
    
    // 开始尝试加载
    tryLoadChart(0);
})();
