"""
九猫系统外部存储模块
将日志、缓存和临时数据存储到外部存储，减轻系统内存压力
"""
import os
import json
import logging
import time
import shutil
from typing import Dict, List, Any, Optional
import threading

logger = logging.getLogger(__name__)

# 从环境变量获取外部存储根目录，如果未设置则使用默认值
EXTERNAL_STORAGE_ROOT = os.environ.get('EXTERNAL_STORAGE_PATH', "E:\\艹，又来一次\\九猫\\external_storage")

# 确保路径使用正确的分隔符
EXTERNAL_STORAGE_ROOT = os.path.normpath(EXTERNAL_STORAGE_ROOT)

# 子目录
LOGS_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "logs")
CACHE_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "cache")
TEMP_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "temp")
DB_BACKUP_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "db_backup")
MEMORY_STATS_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "memory_stats")

# 确保目录存在
def ensure_directories():
    """确保所有外部存储目录存在"""
    for directory in [EXTERNAL_STORAGE_ROOT, LOGS_DIR, CACHE_DIR, TEMP_DIR, DB_BACKUP_DIR, MEMORY_STATS_DIR]:
        os.makedirs(directory, exist_ok=True)
    logger.info(f"外部存储目录已创建: {EXTERNAL_STORAGE_ROOT}")

# 初始化外部存储
ensure_directories()

# 日志存储
def save_logs(novel_id: int, logs: List[Dict[str, Any]]) -> bool:
    """
    将分析日志保存到外部存储

    Args:
        novel_id: 小说ID
        logs: 日志列表

    Returns:
        是否保存成功
    """
    try:
        # 创建小说日志目录
        novel_log_dir = os.path.join(LOGS_DIR, f"novel_{novel_id}")
        os.makedirs(novel_log_dir, exist_ok=True)

        # 创建日志文件名（使用时间戳避免冲突）
        timestamp = int(time.time())
        log_file = os.path.join(novel_log_dir, f"logs_{timestamp}.json")

        # 保存日志
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(logs, f, ensure_ascii=False, indent=2)

        logger.info(f"已将小说 {novel_id} 的 {len(logs)} 条日志保存到外部存储")
        return True
    except Exception as e:
        logger.error(f"保存日志到外部存储时出错: {str(e)}")
        return False

def load_logs(novel_id: int, max_logs: int = 1000) -> List[Dict[str, Any]]:
    """
    从外部存储加载分析日志

    Args:
        novel_id: 小说ID
        max_logs: 最大日志数量

    Returns:
        日志列表
    """
    try:
        # 获取小说日志目录
        novel_log_dir = os.path.join(LOGS_DIR, f"novel_{novel_id}")
        if not os.path.exists(novel_log_dir):
            return []

        # 获取最新的日志文件
        log_files = sorted([f for f in os.listdir(novel_log_dir) if f.startswith("logs_")], reverse=True)
        if not log_files:
            return []

        # 加载最新的日志文件
        log_file = os.path.join(novel_log_dir, log_files[0])
        with open(log_file, 'r', encoding='utf-8') as f:
            logs = json.load(f)

        # 限制日志数量
        if len(logs) > max_logs:
            logs = logs[-max_logs:]

        logger.info(f"已从外部存储加载小说 {novel_id} 的 {len(logs)} 条日志")
        return logs
    except Exception as e:
        logger.error(f"从外部存储加载日志时出错: {str(e)}")
        return []

# 缓存存储
def save_cache(key: str, data: Any) -> bool:
    """
    将缓存数据保存到外部存储

    Args:
        key: 缓存键
        data: 缓存数据

    Returns:
        是否保存成功
    """
    try:
        # 创建缓存文件名
        cache_file = os.path.join(CACHE_DIR, f"{key}.json")

        # 保存缓存
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        logger.debug(f"已将缓存 {key} 保存到外部存储")
        return True
    except Exception as e:
        logger.error(f"保存缓存到外部存储时出错: {str(e)}")
        return False

def load_cache(key: str) -> Optional[Any]:
    """
    从外部存储加载缓存数据

    Args:
        key: 缓存键

    Returns:
        缓存数据，如果不存在则返回None
    """
    try:
        # 获取缓存文件名
        cache_file = os.path.join(CACHE_DIR, f"{key}.json")
        if not os.path.exists(cache_file):
            return None

        # 加载缓存
        with open(cache_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        logger.debug(f"已从外部存储加载缓存 {key}")
        return data
    except Exception as e:
        logger.error(f"从外部存储加载缓存时出错: {str(e)}")
        return None

# 内存统计存储
def save_memory_stats(stats: Dict[str, Any]) -> bool:
    """
    将内存统计数据保存到外部存储

    Args:
        stats: 内存统计数据

    Returns:
        是否保存成功
    """
    try:
        # 创建统计文件名（使用时间戳避免冲突）
        timestamp = int(time.time())
        stats_file = os.path.join(MEMORY_STATS_DIR, f"memory_stats_{timestamp}.json")

        # 保存统计数据
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        # 清理旧的统计文件（只保留最近100个）
        stats_files = sorted([f for f in os.listdir(MEMORY_STATS_DIR) if f.startswith("memory_stats_")])
        if len(stats_files) > 100:
            for old_file in stats_files[:-100]:
                os.remove(os.path.join(MEMORY_STATS_DIR, old_file))

        logger.debug("已将内存统计数据保存到外部存储")
        return True
    except Exception as e:
        logger.error(f"保存内存统计数据到外部存储时出错: {str(e)}")
        return False

def load_memory_stats(count: int = 10) -> List[Dict[str, Any]]:
    """
    从外部存储加载内存统计数据

    Args:
        count: 加载的统计数据数量

    Returns:
        统计数据列表
    """
    try:
        # 获取统计文件
        stats_files = sorted([f for f in os.listdir(MEMORY_STATS_DIR) if f.startswith("memory_stats_")], reverse=True)
        if not stats_files:
            return []

        # 加载最新的统计文件
        result = []
        for stats_file in stats_files[:count]:
            with open(os.path.join(MEMORY_STATS_DIR, stats_file), 'r', encoding='utf-8') as f:
                stats = json.load(f)
                result.append(stats)

        logger.debug(f"已从外部存储加载 {len(result)} 条内存统计数据")
        return result
    except Exception as e:
        logger.error(f"从外部存储加载内存统计数据时出错: {str(e)}")
        return []

# 数据库备份
def backup_database(db_path: str) -> bool:
    """
    备份数据库到外部存储

    Args:
        db_path: 数据库文件路径

    Returns:
        是否备份成功
    """
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            return False

        # 创建备份文件名（使用时间戳避免冲突）
        timestamp = int(time.time())
        db_name = os.path.basename(db_path)
        backup_file = os.path.join(DB_BACKUP_DIR, f"{db_name}.{timestamp}.bak")

        # 复制数据库文件
        shutil.copy2(db_path, backup_file)

        # 清理旧的备份文件（只保留最近10个）
        backup_files = sorted([f for f in os.listdir(DB_BACKUP_DIR) if f.startswith(f"{db_name}.")])
        if len(backup_files) > 10:
            for old_file in backup_files[:-10]:
                os.remove(os.path.join(DB_BACKUP_DIR, old_file))

        logger.info(f"已将数据库备份到外部存储: {backup_file}")
        return True
    except Exception as e:
        logger.error(f"备份数据库到外部存储时出错: {str(e)}")
        return False

# 自动备份线程
class AutoBackupThread(threading.Thread):
    """自动备份线程，定期将内存中的数据保存到外部存储"""

    def __init__(self, interval: int = 300):
        """
        初始化自动备份线程

        Args:
            interval: 备份间隔（秒）
        """
        super().__init__(daemon=True)
        self.interval = interval
        self.running = True

    def run(self):
        """线程运行函数"""
        logger.info(f"自动备份线程已启动，间隔: {self.interval}秒")

        while self.running:
            try:
                # 备份内存统计数据
                try:
                    from src.utils.memory_monitor import MemoryMonitor
                    memory_stats = MemoryMonitor.get_memory_usage()
                    save_memory_stats(memory_stats)
                except ImportError:
                    pass

                # 备份分析日志
                try:
                    from src.web.app import analysis_logs
                    for novel_id, logs in analysis_logs.items():
                        if logs:
                            save_logs(novel_id, logs)
                except ImportError:
                    pass

                # 备份数据库
                try:
                    import config
                    if hasattr(config, 'DATABASE_PATH'):
                        backup_database(config.DATABASE_PATH)
                except ImportError:
                    pass

                logger.debug("自动备份完成")
            except Exception as e:
                logger.error(f"自动备份时出错: {str(e)}")

            # 等待下一次备份
            for _ in range(self.interval):
                if not self.running:
                    break
                time.sleep(1)

    def stop(self):
        """停止线程"""
        self.running = False

# 全局自动备份线程
auto_backup_thread = None

def start_auto_backup(interval: int = 300) -> None:
    """
    启动自动备份

    Args:
        interval: 备份间隔（秒）
    """
    global auto_backup_thread

    if auto_backup_thread and auto_backup_thread.is_alive():
        logger.warning("自动备份线程已在运行")
        return

    auto_backup_thread = AutoBackupThread(interval)
    auto_backup_thread.start()

def stop_auto_backup() -> None:
    """停止自动备份"""
    global auto_backup_thread

    if auto_backup_thread:
        auto_backup_thread.stop()
        auto_backup_thread = None
        logger.info("自动备份线程已停止")
