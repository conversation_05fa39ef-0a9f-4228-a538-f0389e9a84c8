Option Explicit

' 九猫小说分析写作系统 v3.0 修复版启动脚本
' 自动启动九猫系统，无需手动操作

' 设置工作目录为脚本所在目录
Dim fso, shell, pythonCmd, workingDir, runCmd
Set fso = CreateObject("Scripting.FileSystemObject")
Set shell = CreateObject("WScript.Shell")

' 获取当前脚本所在目录
workingDir = fso.GetParentFolderName(WScript.ScriptFullName)
shell.CurrentDirectory = workingDir

' 检查Python环境
pythonCmd = "python"
If Not CheckPythonInstalled(pythonCmd) Then
    pythonCmd = "py"
    If Not CheckPythonInstalled(pythonCmd) Then
        MsgBox "未检测到Python环境，请安装Python 3.8或更高版本。", vbExclamation, "九猫小说分析写作系统"
        WScript.Quit
    End If
End If

' 显示启动消息
MsgBox "九猫小说分析写作系统 v3.0 修复版正在启动..." & vbCrLf & vbCrLf & _
       "此版本包含以下修复:" & vbCrLf & _
       "1. 修复API路径问题（/api/template/ -> /api/novel/）" & vbCrLf & _
       "2. 修复重新分析功能" & vbCrLf & _
       "3. 改进API超时处理" & vbCrLf & _
       "4. 修复分析结果和推理过程显示问题" & vbCrLf & vbCrLf & _
       "系统将在后台运行，浏览器将在几秒钟后自动打开。" & vbCrLf & _
       "请不要关闭命令行窗口，否则需要手动访问: http://localhost:5001/v3/", _
       64, "九猫小说分析写作系统 v3.0 修复版"

' 创建临时批处理文件
Dim tempFile
Set tempFile = fso.CreateTextFile("temp_run_v3_fixed.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & workingDir & """")
tempFile.WriteLine("title 九猫小说分析写作系统 v3.0 修复版")
tempFile.WriteLine("echo 九猫小说分析写作系统 v3.0 修复版")
tempFile.WriteLine("echo ===================================")
tempFile.WriteLine("echo.")
tempFile.WriteLine("echo 正在启动系统，请稍候...")
tempFile.WriteLine("echo.")

' 设置环境变量
tempFile.WriteLine("set VERSION=3.0")
tempFile.WriteLine("set DEFAULT_MODEL=deepseek-r1")
tempFile.WriteLine("set FORCE_REANALYSIS=True")
tempFile.WriteLine("set FORCE_REAL_API=True")
tempFile.WriteLine("set RELOAD_CONFIG=True")
tempFile.WriteLine("set TOTAL_DIMENSIONS=15")
tempFile.WriteLine("set ENABLE_ANALYSIS_STATUS_FIX=True")
tempFile.WriteLine("set USE_V3_CONSOLE=True")
tempFile.WriteLine("set ENABLE_API_COMPAT=True")
tempFile.WriteLine("set ENABLE_REANALYZE_FIX=True")
tempFile.WriteLine("set ENABLE_API_TIMEOUT_FIX=True")
tempFile.WriteLine("set API_TIMEOUT=180")
tempFile.WriteLine("set API_RETRY_COUNT=3")

tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("echo 检查Python是否可用...")
tempFile.WriteLine("python --version")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo Python未找到，请确保Python已安装并添加到PATH环境变量中")
tempFile.WriteLine("    pause")
tempFile.WriteLine("    exit /b 1")
tempFile.WriteLine(")")
tempFile.WriteLine("echo 启动九猫v3.0修复版系统...")
tempFile.WriteLine(pythonCmd & " -u -m src.web.v3_app")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo 启动失败，错误代码: %ERRORLEVEL%")
tempFile.WriteLine("    pause")
tempFile.WriteLine(")")
tempFile.Close

' 在后台运行批处理文件
shell.Run "cmd /c """ & workingDir & "\temp_run_v3_fixed.bat""", 1, False

' 等待5秒钟确保服务启动
WScript.Sleep 5000

' 打开浏览器访问系统
shell.Run "http://localhost:5001/v3/console", 1, False

' 函数：检查Python是否已安装
Function CheckPythonInstalled(cmd)
    On Error Resume Next
    shell.Run cmd & " --version", 0, True
    CheckPythonInstalled = (Err.Number = 0)
    On Error GoTo 0
End Function
