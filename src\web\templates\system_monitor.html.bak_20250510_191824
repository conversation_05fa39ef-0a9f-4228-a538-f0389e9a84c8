{% extends "base.html" %}

{% block title %}系统监控 - 九猫{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        margin-bottom: 20px;
        border-radius: 5px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .dashboard-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e3e6f0;
        padding: 0.75rem 1.25rem;
    }

    .dashboard-card .card-body {
        padding: 1.25rem;
    }

    .metric-card {
        border-left: 4px solid;
        border-radius: 3px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        margin-bottom: 20px;
    }

    .metric-card-primary {
        border-left-color: #4e73df;
    }

    .metric-card-success {
        border-left-color: #1cc88a;
    }

    .metric-card-info {
        border-left-color: #36b9cc;
    }

    .metric-card-warning {
        border-left-color: #f6c23e;
    }

    .metric-card-danger {
        border-left-color: #e74a3b;
    }

    .metric-card .card-body {
        padding: 0.75rem 1.25rem;
    }

    .metric-card .metric-value {
        font-size: 1.5rem;
        font-weight: 700;
    }

    .metric-card .metric-label {
        font-size: 0.8rem;
        color: #858796;
        text-transform: uppercase;
    }

    .chart-container {
        position: relative;
        height: 300px;
    }

    .alert-history {
        max-height: 300px;
        overflow-y: auto;
    }

    .alert-item {
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 5px;
    }

    .alert-critical {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
    }

    .alert-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
    }

    .alert-info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
    }

    .system-status {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .status-indicator {
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin-right: 10px;
    }

    .status-healthy {
        background-color: #1cc88a;
    }

    .status-warning {
        background-color: #f6c23e;
    }

    .status-critical {
        background-color: #e74a3b;
    }

    .log-viewer {
        background-color: #212529;
        color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        font-family: monospace;
        height: 300px;
        overflow-y: auto;
    }

    .log-entry {
        margin-bottom: 5px;
        white-space: pre-wrap;
        word-break: break-all;
    }

    .log-info {
        color: #17a2b8;
    }

    .log-warning {
        color: #ffc107;
    }

    .log-error {
        color: #dc3545;
    }

    .refresh-controls {
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item active">系统监控</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>系统监控</h1>
            <div class="refresh-controls">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary" id="refresh-now">
                        <i class="fas fa-sync-alt"></i> 立即刷新
                    </button>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            自动刷新: <span id="refresh-interval-display">30秒</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item refresh-interval" data-interval="0" href="#">关闭</a></li>
                            <li><a class="dropdown-item refresh-interval" data-interval="10" href="#">10秒</a></li>
                            <li><a class="dropdown-item refresh-interval" data-interval="30" href="#">30秒</a></li>
                            <li><a class="dropdown-item refresh-interval" data-interval="60" href="#">1分钟</a></li>
                            <li><a class="dropdown-item refresh-interval" data-interval="300" href="#">5分钟</a></li>
                        </ul>
                    </div>
                </div>
                <span class="text-muted ms-2" id="last-updated">
                    最后更新: {{ last_updated.strftime('%Y-%m-%d %H:%M:%S') }}
                </span>
            </div>
        </div>

        <!-- 系统状态概览 -->
        <div class="row">
            <div class="col-md-3">
                <div class="card metric-card metric-card-primary">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="metric-label">CPU 使用率</div>
                                <div class="metric-value">{{ system_metrics.cpu_usage }}%</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-microchip fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card metric-card-success">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="metric-label">内存使用率</div>
                                <div class="metric-value">{{ system_metrics.memory_usage }}%</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-memory fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card metric-card-info">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="metric-label">磁盘使用率</div>
                                <div class="metric-value">{{ system_metrics.disk_usage }}%</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-hdd fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card metric-card-warning">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="metric-label">系统运行时间</div>
                                <div class="metric-value">{{ system_metrics.uptime }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API调用统计 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">API调用频率</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="apiCallChart"></canvas>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>今日调用</h6>
                                    <h3>{{ api_stats.today_calls }}</h3>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>本周调用</h6>
                                    <h3>{{ api_stats.week_calls }}</h3>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>本月调用</h6>
                                    <h3>{{ api_stats.month_calls }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">API调用状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="apiStatusChart"></canvas>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>成功率</h6>
                                    <h3>{{ api_stats.success_rate }}%</h3>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>平均响应时间</h6>
                                    <h3>{{ api_stats.avg_response_time }}ms</h3>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>错误数</h6>
                                    <h3>{{ api_stats.error_count }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据库连接池状态 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">数据库连接池状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dbConnectionChart"></canvas>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>活跃连接</h6>
                                    <h3>{{ db_stats.active_connections }}</h3>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>连接池大小</h6>
                                    <h3>{{ db_stats.pool_size }}</h3>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>溢出连接</h6>
                                    <h3>{{ db_stats.overflow }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="system-status mt-3">
                            <div class="status-indicator
                                {% if db_stats.status == 'healthy' %}status-healthy
                                {% elif db_stats.status == 'warning' %}status-warning
                                {% else %}status-critical{% endif %}">
                            </div>
                            <div>
                                {% if db_stats.status == 'healthy' %}
                                    连接池状态良好
                                {% elif db_stats.status == 'warning' %}
                                    连接池接近容量上限
                                {% else %}
                                    连接池已达到容量上限
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">系统告警</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert-history">
                            {% if alerts %}
                                {% for alert in alerts %}
                                    <div class="alert-item alert-{{ alert.level }}">
                                        <div class="d-flex justify-content-between">
                                            <strong>{{ alert.title }}</strong>
                                            <small>{{ alert.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                                        </div>
                                        <p class="mb-0">{{ alert.message }}</p>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center text-muted">
                                    <p>没有告警记录</p>
                                </div>
                            {% endif %}
                        </div>
                        <div class="mt-3">
                            <h6>告警设置</h6>
                            <form method="POST" action="{{ url_for('update_alert_settings') }}" class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_alerts" name="enable_alerts"
                                               {% if alert_settings.enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="enable_alerts">启用系统告警</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="email_alerts" name="email_alerts"
                                               {% if alert_settings.email_enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="email_alerts">发送邮件通知</label>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-primary btn-sm">保存设置</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统日志 -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card dashboard-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">系统日志</h5>
                        <div>
                            <select class="form-select form-select-sm" id="log-level">
                                <option value="all">所有级别</option>
                                <option value="info">信息</option>
                                <option value="warning">警告</option>
                                <option value="error">错误</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="log-viewer">
                            {% for log in logs %}
                                <div class="log-entry log-{{ log.level }}">
                                    <span class="log-time">[{{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}]</span>
                                    <span class="log-level">[{{ log.level.upper() }}]</span>
                                    <span class="log-message">{{ log.message }}</span>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <a href="{{ url_for('download_logs') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-download"></i> 下载完整日志
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 错误处理脚本 - 最高优先级 -->
<script src="{{ url_for('static', filename='js/error-handler.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';"></script>

<!-- 错误页面修复脚本 -->
<script src="{{ url_for('static', filename='js/error-page-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/error-page-fix.js';"></script>

<!-- 系统监控页面修复脚本 -->
<script src="{{ url_for('static', filename='js/system-monitor-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/system-monitor-fix.js';"></script>

<!-- Chart.js 加载 -->
<script src="{{ url_for('static', filename='js/lib/chart.min.js') }}" onerror="this.onerror=null;this.src='/static/js/chart.min.js';"></script>
<!-- 备用 Chart.js CDN -->
<script>
    if (typeof Chart === 'undefined') {
        console.log('Chart.js未加载，尝试从CDN加载');
        document.write('<script src="https://cdn.jsdelivr.net/npm/chart.js"><\/script>');
    }
</script>

<!-- Chart.js 修复脚本 -->
<script src="{{ url_for('static', filename='js/chart-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chart-fix.js';"></script>
<script>
    // 页面加载完成后初始化图表
    document.addEventListener('DOMContentLoaded', function() {
        // API调用频率图表
        const apiCallCtx = document.getElementById('apiCallChart').getContext('2d');
        const apiCallChart = new Chart(apiCallCtx, {
            type: 'line',
            data: {
                labels: {{ api_stats.time_labels|tojson }},
                datasets: [{
                    label: 'API调用次数',
                    data: {{ api_stats.call_counts|tojson }},
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    borderWidth: 2,
                    pointBackgroundColor: '#4e73df',
                    pointBorderColor: '#fff',
                    pointHoverRadius: 3,
                    pointHoverBackgroundColor: '#4e73df',
                    pointHoverBorderColor: '#fff',
                    pointHitRadius: 10,
                    pointBorderWidth: 2,
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // API调用状态图表
        const apiStatusCtx = document.getElementById('apiStatusChart').getContext('2d');
        const apiStatusChart = new Chart(apiStatusCtx, {
            type: 'pie',
            data: {
                labels: ['成功', '失败', '超时'],
                datasets: [{
                    data: [
                        {{ api_stats.success_count }},
                        {{ api_stats.error_count }},
                        {{ api_stats.timeout_count }}
                    ],
                    backgroundColor: ['#1cc88a', '#e74a3b', '#f6c23e'],
                    hoverBackgroundColor: ['#17a673', '#be2617', '#dda20a'],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                cutout: '50%'
            }
        });

        // 数据库连接池状态图表
        const dbConnectionCtx = document.getElementById('dbConnectionChart').getContext('2d');
        const dbConnectionChart = new Chart(dbConnectionCtx, {
            type: 'bar',
            data: {
                labels: ['活跃连接', '空闲连接', '溢出连接'],
                datasets: [{
                    label: '连接数',
                    data: [
                        {{ db_stats.active_connections }},
                        {{ db_stats.pool_size - db_stats.active_connections }},
                        {{ db_stats.overflow }}
                    ],
                    backgroundColor: ['#4e73df', '#1cc88a', '#e74a3b']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // 自动刷新功能
        let refreshInterval = 30; // 默认30秒
        let refreshTimer = null;

        function startAutoRefresh() {
            if (refreshTimer) {
                clearInterval(refreshTimer);
            }

            if (refreshInterval > 0) {
                refreshTimer = setInterval(function() {
                    window.location.reload();
                }, refreshInterval * 1000);
            }
        }

        // 设置刷新间隔
        document.querySelectorAll('.refresh-interval').forEach(function(item) {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                refreshInterval = parseInt(this.getAttribute('data-interval'));
                document.getElementById('refresh-interval-display').textContent =
                    refreshInterval === 0 ? '关闭' :
                    (refreshInterval >= 60 ? (refreshInterval / 60) + '分钟' : refreshInterval + '秒');

                startAutoRefresh();
            });
        });

        // 立即刷新按钮
        document.getElementById('refresh-now').addEventListener('click', function() {
            window.location.reload();
        });

        // 日志过滤功能
        document.getElementById('log-level').addEventListener('change', function() {
            const level = this.value;
            const logEntries = document.querySelectorAll('.log-entry');

            logEntries.forEach(function(entry) {
                if (level === 'all' || entry.classList.contains('log-' + level)) {
                    entry.style.display = '';
                } else {
                    entry.style.display = 'none';
                }
            });
        });

        // 启动自动刷新
        startAutoRefresh();
    });
</script>
{% endblock %}
