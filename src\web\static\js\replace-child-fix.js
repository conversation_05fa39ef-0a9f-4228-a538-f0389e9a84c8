/**
 * 九猫 - replaceChild 修复脚本
 * 解决 "Failed to execute 'replaceChild' on 'Node': Invalid or unexpected token" 错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('replaceChild 修复脚本已加载 v1.0.0');

    // 保存原始的 replaceChild 方法
    if (!window.__originalReplaceChild) {
        window.__originalReplaceChild = Node.prototype.replaceChild;
    }

    // 安全的 HTML 编码函数
    function safeEncodeHTML(html) {
        if (typeof html !== 'string') {
            return html;
        }

        try {
            // 检查HTML是否包含无效字符或标记
            if (html.includes('</script') || html.includes('-->') ||
                html.includes('<!--') || /[\u0000-\u0008\u000B\u000C\u000E-\u001F]/.test(html)) {
                console.warn('HTML包含可能导致解析问题的字符，进行特殊处理');

                // 替换可能导致问题的字符
                let safeHtml = html
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/\u0000-\u001F/g, ''); // 移除控制字符

                return safeHtml;
            }

            // 创建一个临时元素来测试 HTML 是否有效
            const temp = document.createElement('div');
            temp.innerHTML = html;
            return temp.innerHTML;
        } catch (e) {
            console.error('HTML 编码失败:', e);
            // 如果失败，返回安全的转义版本
            return String(html)
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');
        }
    }

    // 安全的节点替换函数
    function safeReplaceChild(newChild, oldChild) {
        try {
            // 检查参数是否有效
            if (!newChild || !oldChild) {
                console.error('replaceChild: 无效的参数', { newChild, oldChild });
                return oldChild;
            }

            // 检查节点是否为脚本节点，如果是，需要特殊处理
            if (newChild.nodeType === Node.ELEMENT_NODE &&
                (newChild.tagName === 'SCRIPT' || newChild.nodeName === 'SCRIPT')) {
                console.log('检测到脚本节点替换，使用特殊处理');

                try {
                    // 创建一个新的脚本节点
                    const safeScript = document.createElement('script');

                    // 复制属性
                    if (newChild.attributes) {
                        for (let i = 0; i < newChild.attributes.length; i++) {
                            const attr = newChild.attributes[i];
                            safeScript.setAttribute(attr.name, attr.value);
                        }
                    }

                    // 使用文本节点添加内容，而不是设置innerHTML或textContent
                    if (newChild.textContent) {
                        const textNode = document.createTextNode(newChild.textContent);
                        safeScript.appendChild(textNode);
                    }

                    // 使用安全的方式替换
                    if (this.contains(oldChild)) {
                        this.insertBefore(safeScript, oldChild);
                        this.removeChild(oldChild);
                    } else {
                        this.appendChild(safeScript);
                    }

                    return safeScript;
                } catch (scriptError) {
                    console.error('脚本节点特殊处理失败:', scriptError);
                }
            }

            // 尝试使用原始方法
            return window.__originalReplaceChild.call(this, newChild, oldChild);
        } catch (e) {
            console.error('replaceChild 错误:', e.message);

            // 如果是语法错误，尝试安全替换
            if (e instanceof SyntaxError || e.message.includes('Invalid or unexpected token') ||
                e.message.includes('Failed to execute')) {
                console.log('检测到语法错误，尝试安全替换');

                try {
                    // 创建一个新的节点
                    let safeNewChild;

                    if (newChild.nodeType === Node.TEXT_NODE) {
                        // 如果是文本节点，创建一个新的文本节点
                        safeNewChild = document.createTextNode(newChild.textContent || '');
                    } else if (newChild.nodeType === Node.ELEMENT_NODE) {
                        // 如果是元素节点，创建一个新的元素
                        safeNewChild = document.createElement(newChild.tagName);

                        // 复制属性
                        if (newChild.attributes) {
                            for (let i = 0; i < newChild.attributes.length; i++) {
                                const attr = newChild.attributes[i];
                                safeNewChild.setAttribute(attr.name, attr.value);
                            }
                        }

                        // 安全地复制内容 - 使用更安全的方法
                        try {
                            // 对于脚本节点，使用特殊处理
                            if (newChild.tagName === 'SCRIPT') {
                                // 使用文本节点添加内容
                                if (newChild.textContent) {
                                    const textNode = document.createTextNode(newChild.textContent);
                                    safeNewChild.appendChild(textNode);
                                }
                            } else {
                                // 对于非脚本节点，使用安全的HTML编码
                                const safeHTML = safeEncodeHTML(newChild.innerHTML || '');
                                safeNewChild.innerHTML = safeHTML;
                            }
                        } catch (contentError) {
                            console.error('复制内容时出错:', contentError);
                            // 出错时不设置内容
                        }
                    } else {
                        // 对于其他类型的节点，尝试克隆
                        try {
                            safeNewChild = newChild.cloneNode(false); // 只克隆节点本身，不克隆子节点
                        } catch (cloneError) {
                            console.error('克隆节点失败:', cloneError);
                            // 创建一个空的div作为后备
                            safeNewChild = document.createElement('div');
                        }
                    }

                    // 尝试替换
                    try {
                        if (this.contains(oldChild)) {
                            this.insertBefore(safeNewChild, oldChild);
                            this.removeChild(oldChild);
                        } else {
                            this.appendChild(safeNewChild);
                        }

                        return safeNewChild;
                    } catch (replaceError) {
                        console.error('替换节点失败:', replaceError);

                        // 最后的尝试：完全移除旧节点
                        try {
                            if (this.contains(oldChild)) {
                                this.removeChild(oldChild);
                            }
                            return null;
                        } catch (e3) {
                            console.error('移除旧节点也失败:', e3.message);
                            return null;
                        }
                    }
                } catch (e2) {
                    console.error('安全替换也失败:', e2.message);

                    // 最后的尝试：完全移除旧节点
                    try {
                        if (this.contains(oldChild)) {
                            this.removeChild(oldChild);
                        }
                        return null;
                    } catch (e3) {
                        console.error('移除旧节点也失败:', e3.message);
                        return null;
                    }
                }
            } else {
                // 如果不是语法错误，重新抛出
                throw e;
            }
        }
    }

    // 替换原始的 replaceChild 方法
    Node.prototype.replaceChild = safeReplaceChild;

    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message &&
            (event.error.message.includes('replaceChild') ||
             event.error.message.includes('Invalid or unexpected token'))) {

            console.error('捕获到 replaceChild 相关错误:', event.error.message);

            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);

    console.log('replaceChild 方法已安全替换');
})();
