/**
 * 九猫 - 分析过程查看器修复脚本
 * 解决分析过程查看器无法正确显示分析过程的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析过程查看器修复脚本已加载 v1.0.0');

    // 配置
    const CONFIG = {
        enableDebug: true,           // 启用调试模式
        maxRetries: 3,               // 最大重试次数
        delayBetweenRetries: 500,    // 重试间隔(毫秒)
        safeMode: true               // 安全模式
    };

    // 监听DOM加载完成事件
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟执行以确保所有资源已加载
        setTimeout(initializeProcessViewerFix, 500);
    });

    // 初始化分析过程查看器修复
    function initializeProcessViewerFix() {
        console.log('初始化分析过程查看器修复');

        // 检查是否在分析过程查看器页面
        if (!isProcessViewerPage()) {
            console.log('不是分析过程查看器页面，跳过修复');
            return;
        }

        // 修复资源加载问题
        fixResourceLoading();

        // 修复分析过程内容加载
        fixProcessContentLoading();

        // 添加错误处理
        addErrorHandling();
    }

    // 检查是否在分析过程查看器页面
    function isProcessViewerPage() {
        return window.location.pathname.includes('/process') ||
               window.location.pathname.includes('/process_viewer');
    }

    // 修复资源加载问题
    function fixResourceLoading() {
        console.log('修复资源加载问题');

        // 检查是否已加载所需的脚本
        const requiredScripts = [
            'universal-chart-fix-enhanced.js',
            'language-style-fix.js',
            'global-error-handler.js',
            'dom-operation-fix.js'
        ];

        // 检查是否已加载
        const loadedScripts = Array.from(document.querySelectorAll('script'))
            .map(script => {
                const src = script.src || '';
                return src.substring(src.lastIndexOf('/') + 1);
            });

        // 加载缺失的脚本
        requiredScripts.forEach(script => {
            if (!loadedScripts.some(s => s === script)) {
                console.log(`加载缺失的脚本: ${script}`);
                loadScript(`/static/js/${script}`);
            }
        });
    }

    // 加载脚本
    function loadScript(url) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.onload = resolve;
            script.onerror = () => {
                console.error(`加载脚本失败: ${url}`);
                reject(new Error(`加载脚本失败: ${url}`));
            };
            document.head.appendChild(script);
        });
    }

    // 修复分析过程内容加载
    function fixProcessContentLoading() {
        console.log('修复分析过程内容加载');

        // 重写loadProcessContent函数，增强其健壮性
        if (window.loadProcessContent) {
            const originalLoadProcessContent = window.loadProcessContent;

            window.loadProcessContent = function() {
                try {
                    // 调用原始函数
                    originalLoadProcessContent();
                } catch (e) {
                    console.error('原始loadProcessContent函数出错:', e);

                    // 尝试直接加载reasoning_content
                    loadReasoningContentDirectly();
                }
            };
        } else {
            // 如果原始函数不存在，添加我们自己的实现
            window.loadProcessContent = function() {
                loadReasoningContentDirectly();
            };
        }

        // 检查是否需要立即加载内容
        setTimeout(checkAndLoadContent, 1000);
    }

    // 检查并加载内容
    function checkAndLoadContent() {
        const contentElement = document.getElementById('processContent');
        const contentView = document.getElementById('contentView');

        if (contentElement && contentView &&
            contentView.style.display === 'block' &&
            (!contentElement.textContent ||
             contentElement.textContent.trim() === '加载中...' ||
             contentElement.innerHTML.includes('spinner-border'))) {

            console.log('检测到内容未加载，尝试加载');
            loadReasoningContentDirectly();
        }
    }

    // 直接加载reasoning_content
    async function loadReasoningContentDirectly() {
        console.log('直接加载reasoning_content');

        const contentElement = document.getElementById('processContent');
        if (!contentElement) return;

        // 显示加载指示器
        contentElement.innerHTML = `
            <div class="loading-indicator">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2">正在加载分析过程内容，请稍候...</p>
            </div>
        `;

        // 获取小说ID和维度
        const novelId = getNovelId();
        const dimension = getDimension();

        if (!novelId || !dimension) {
            contentElement.innerHTML = `
                <div class="alert alert-danger">
                    <strong>错误:</strong> 无法获取小说ID或维度信息
                </div>
            `;
            return;
        }

        // 构建API URL
        const reasoningUrl = `/api/novel/${novelId}/analysis/${dimension}/reasoning_content`;

        console.log('请求reasoning_content URL:', reasoningUrl);

        try {
            // 使用async/await简化代码
            const response = await fetch(reasoningUrl);
            console.log('reasoning_content响应状态:', response.status);

            // 特殊处理404错误，将其视为"暂无数据"而非错误
            if (response.status === 404) {
                console.info('推理内容暂不可用 (HTTP 404)，尝试备用API');
                fallbackToContentAPI();
                return;
            }

            const text = await response.text();
            console.log('reasoning_content响应文本长度:', text.length);

            try {
                // 尝试解析为JSON
                const data = JSON.parse(text);

                if (data.success && data.reasoning_content) {
                    // 成功获取到reasoning_content
                    renderReasoningContent(data.reasoning_content);
                } else if (data.error) {
                    // API返回错误，但使用info级别日志而非错误
                    console.info('API返回信息:', data.error);
                    fallbackToContentAPI();
                } else {
                    // 没有找到reasoning_content
                    console.info('API响应中没有推理内容，尝试备用API');
                    fallbackToContentAPI();
                }
            } catch (e) {
                // 响应不是有效的JSON，尝试直接从文本中提取
                console.info('响应不是有效的JSON，尝试直接从文本中提取');

                // 检查是否包含分析过程特征
                if (text.includes('嗯，用户让我') ||
                    text.includes('首先，我需要') ||
                    text.includes('我将分析')) {
                    renderReasoningContent(text);
                } else {
                    fallbackToContentAPI();
                }
            }
        } catch (error) {
            // 使用警告级别而非错误级别
            console.warn('请求推理内容暂时不可用:', error.message);
            fallbackToContentAPI();

            // 显示友好的提示信息，而非错误信息
            contentElement.innerHTML = `
                <div class="alert alert-info">
                    <p><i class="fas fa-info-circle"></i> 正在尝试加载分析过程内容...</p>
                    <p class="small text-muted mt-2">系统将自动尝试其他方式获取内容</p>
                </div>
                <div class="text-center mt-4">
                    <button class="btn btn-outline-primary" onclick="window.loadProcessContent()">手动重试</button>
                </div>
            `;
        }
    }

    // 回退到内容API
    async function fallbackToContentAPI() {
        console.log('回退到内容API');

        const contentElement = document.getElementById('processContent');
        if (!contentElement) return;

        // 获取小说ID和维度
        const novelId = getNovelId();
        const dimension = getDimension();

        if (!novelId || !dimension) {
            contentElement.innerHTML = `
                <div class="alert alert-danger">
                    <strong>错误:</strong> 无法获取小说ID或维度信息
                </div>
            `;
            return;
        }

        // 构建API URL
        const url = `/api/novel/${novelId}/analysis/${dimension}/process/content`;

        console.log('回退到内容API URL:', url);

        try {
            // 使用async/await简化代码
            const response = await fetch(url);
            console.log('内容API响应状态:', response.status);

            const data = await response.json();
            if (data.success && data.content) {
                renderReasoningContent(data.content);
            } else {
                contentElement.innerHTML = `
                    <div class="alert alert-warning">
                        <strong>提示:</strong> 未找到分析过程内容。这可能是因为该分析是在启用详细过程记录功能之前进行的。
                    </div>
                    <div class="text-center mt-4">
                        <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
                    </div>
                `;
            }
        } catch (error) {
            console.error('请求内容API时出错:', error);
            contentElement.innerHTML = `
                <div class="alert alert-danger">
                    <strong>错误:</strong> 请求分析过程内容时出错: ${error.message}
                </div>
                <div class="text-center mt-4">
                    <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
                </div>
            `;
        }
    }

    // 渲染reasoning_content
    function renderReasoningContent(content) {
        console.log('渲染reasoning_content，内容长度:', content.length);

        const contentElement = document.getElementById('processContent');
        if (!contentElement) return;

        // 获取小说信息
        const novelTitle = getNovelTitle() || '未知小说';
        const dimension = getDimension() || '未知维度';

        // 构建Markdown内容
        let markdown = `# ${novelTitle} - ${dimension} 分析过程\n\n`;

        // 添加提示
        markdown += `<div class="alert alert-info">
            <strong>注意：</strong> 显示的是从分析结果中提取的分析过程内容。
        </div>\n\n`;

        // 如果内容不包含"分析过程"标题，添加一个
        if (!content.includes('分析过程') && !content.includes('## 分析过程')) {
            markdown += "## 分析过程\n\n";
        }

        // 添加内容
        markdown += content;

        // 渲染内容
        contentElement.innerHTML = markdown;

        // 添加样式到代码块和表格
        setTimeout(() => {
            // 为代码块添加样式
            const codeBlocks = contentElement.querySelectorAll('pre code');
            if (codeBlocks.length > 0) {
                codeBlocks.forEach(block => {
                    block.classList.add('language-plaintext');
                });
            }

            // 为表格添加Bootstrap样式
            const tables = contentElement.querySelectorAll('table');
            if (tables.length > 0) {
                tables.forEach(table => {
                    table.classList.add('table', 'table-bordered', 'table-striped', 'table-hover');
                });
            }
        }, 100);
    }

    // 添加错误处理
    function addErrorHandling() {
        console.log('添加错误处理');

        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                // 处理资源不足错误
                if (event.error.message.includes('ERR_INSUFFICIENT_RESOURCES')) {
                    console.error('捕获到资源不足错误:', event.error.message);

                    // 尝试释放资源
                    releaseResources();

                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);

        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);

            // 检查是否是资源加载错误
            if (event.reason && event.reason.message &&
                (event.reason.message.includes('ERR_INSUFFICIENT_RESOURCES') ||
                 event.reason.message.includes('加载脚本失败'))) {

                console.error('捕获到资源加载错误:', event.reason.message);

                // 尝试释放资源
                releaseResources();

                // 阻止错误传播
                event.preventDefault();
                return false;
            }
        });
    }

    // 释放资源
    function releaseResources() {
        console.log('尝试释放资源');

        // 移除不必要的元素
        const elementsToRemove = [
            '.memory-optimization-notice',
            '.process-explanation'
        ];

        elementsToRemove.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                element.remove();
            });
        });

        // 清除不必要的数据
        if (window.processViewerConfig) {
            // 清除不必要的数据
            window.processViewerConfig.processes = [];
            window.processViewerConfig.loadedProcessIds = new Set();
            window.processViewerConfig.contentChunks = [];
        }

        // 强制垃圾回收（虽然不能直接调用，但可以尝试通过一些操作触发）
        try {
            const largeArray = new Array(1000).fill(new Array(1000));
            largeArray.length = 0;
        } catch (e) {
            console.error('尝试触发垃圾回收时出错:', e);
        }
    }

    // 获取小说ID
    function getNovelId() {
        // 从URL中获取
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        if (match && match[1]) {
            return match[1];
        }

        // 从processViewerConfig中获取
        if (window.processViewerConfig && window.processViewerConfig.novelId) {
            return window.processViewerConfig.novelId;
        }

        return null;
    }

    // 获取维度
    function getDimension() {
        // 从URL中获取
        const match = window.location.pathname.match(/\/analysis\/([^\/]+)/);
        if (match && match[1]) {
            return match[1];
        }

        // 从processViewerConfig中获取
        if (window.processViewerConfig && window.processViewerConfig.dimension) {
            return window.processViewerConfig.dimension;
        }

        return null;
    }

    // 获取小说标题
    function getNovelTitle() {
        // 从页面元素中获取
        const titleElement = document.querySelector('.process-summary-card strong:contains("标题:")');
        if (titleElement) {
            return titleElement.nextSibling.textContent.trim();
        }

        // 从processViewerConfig中获取
        if (window.processViewerConfig &&
            window.processViewerConfig.novel &&
            window.processViewerConfig.novel.title) {
            return window.processViewerConfig.novel.title;
        }

        return null;
    }
})();
