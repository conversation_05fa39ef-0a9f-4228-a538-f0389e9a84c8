/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * F12控制台错误修复工具
 * 
 * 这个脚本用于修复F12开发者工具中错误显示与实际代码不一致的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('F12控制台错误修复工具已加载');
    
    // 存储原始控制台方法
    const originalConsole = {
        log: console.log,
        error: console.error,
        warn: console.warn,
        info: console.info,
        debug: console.debug
    };
    
    // 存储原始错误处理函数
    const originalOnError = window.onerror;
    
    // 错误计数器
    let errorCounter = 0;
    
    // 已处理的错误集合
    const processedErrors = new Set();
    
    // 检查是否已经有日志过滤器在运行
    const hasLogFilter = typeof window.browserConsoleCapture !== 'undefined' || 
                         typeof window.consoleLogger !== 'undefined';
    
    // 恢复原始控制台方法
    function restoreOriginalConsole() {
        console.log = originalConsole.log;
        console.error = originalConsole.error;
        console.warn = originalConsole.warn;
        console.info = originalConsole.info;
        console.debug = originalConsole.debug;
        
        console.log('已恢复原始控制台方法');
    }
    
    // 恢复原始错误处理函数
    function restoreOriginalOnError() {
        window.onerror = originalOnError;
        console.log('已恢复原始错误处理函数');
    }
    
    // 创建透明的错误处理函数
    function createTransparentErrorHandler() {
        window.onerror = function(message, source, lineno, colno, error) {
            // 生成错误的唯一标识
            const errorId = `${message}:${source}:${lineno}:${colno}`;
            
            // 如果已经处理过这个错误，不再重复处理
            if (processedErrors.has(errorId)) {
                return false;
            }
            
            // 添加到已处理集合
            processedErrors.add(errorId);
            
            // 增加错误计数
            errorCounter++;
            
            // 在控制台中显示原始错误
            originalConsole.error(`[原始错误 #${errorCounter}] ${message}`);
            originalConsole.error(`位置: ${source} 行: ${lineno} 列: ${colno}`);
            if (error && error.stack) {
                originalConsole.error(`堆栈: ${error.stack}`);
            }
            
            // 不阻止默认错误处理，让错误显示在F12控制台中
            return false;
        };
    }
    
    // 检查是否有错误处理器拦截了错误
    function checkErrorHandlers() {
        if (window.onerror !== originalOnError && originalOnError) {
            console.log('检测到自定义错误处理器，创建透明错误处理函数');
            createTransparentErrorHandler();
        } else {
            console.log('未检测到自定义错误处理器，使用浏览器默认错误处理');
        }
    }
    
    // 添加测试错误按钮
    function addTestErrorButton() {
        // 检查是否已存在
        if (document.getElementById('test-f12-error-button')) {
            return;
        }
        
        // 创建按钮
        const button = document.createElement('button');
        button.id = 'test-f12-error-button';
        button.className = 'btn btn-sm btn-outline-danger';
        button.style.position = 'fixed';
        button.style.bottom = '10px';
        button.style.right = '10px';
        button.style.zIndex = '9999';
        button.textContent = '测试F12错误';
        
        // 添加点击事件
        button.addEventListener('click', function() {
            try {
                // 故意制造一个错误
                const obj = null;
                obj.nonExistentMethod();
            } catch (e) {
                // 直接抛出错误，让它显示在F12控制台中
                throw e;
            }
        });
        
        // 添加到页面
        document.body.appendChild(button);
    }
    
    // 主函数
    function main() {
        console.log('F12控制台错误修复工具开始执行');
        
        // 检查是否有日志过滤器
        if (hasLogFilter) {
            console.log('检测到日志过滤器，确保错误正确显示');
        }
        
        // 检查错误处理器
        checkErrorHandlers();
        
        // 添加测试错误按钮
        setTimeout(addTestErrorButton, 1000);
        
        // 添加全局错误监听
        window.addEventListener('error', function(event) {
            // 确保错误显示在F12控制台中
            if (event.error) {
                // 阻止事件传播，但不阻止默认行为
                event.stopPropagation();
            }
        }, true);
        
        console.log('F12控制台错误修复工具执行完成');
    }
    
    // 当DOM加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', main);
    } else {
        // 如果DOM已加载完成，立即执行
        main();
    }
})();
