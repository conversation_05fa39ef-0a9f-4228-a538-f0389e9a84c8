/**
 * 九猫小说分析写作系统 - 直接数据库路径修复脚本
 *
 * 此脚本用于修复direct_db路径的API调用，确保它们能够正确工作。
 * 版本: 1.0.0
 */

(function() {
    console.log('[直接数据库路径修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,                 // 是否启用调试模式
        checkInterval: 1000,         // 检查间隔（毫秒）
        maxRetries: 5,               // 最大重试次数
        apiPaths: {
            // 需要修复的API路径映射
            '/direct_db/analysis': '/direct_db/analysis',
            '/direct_db/chapter_analysis': '/direct_db/chapter_analysis'
        }
    };

    // 状态
    const STATE = {
        initialized: false,          // 是否已初始化
        retryCount: 0,               // 重试计数
        originalFetch: null,         // 原始fetch函数
        fixedPaths: new Set()        // 已修复的路径
    };

    // 安全日志函数
    function safeLog(message, level = 'info') {
        if (CONFIG.debug) {
            const prefix = '[直接数据库路径修复]';
            try {
                switch (level) {
                    case 'error':
                        console.error(`${prefix} ${message}`);
                        break;
                    case 'warn':
                        console.warn(`${prefix} ${message}`);
                        break;
                    default:
                        console.log(`${prefix} ${message}`);
                }
            } catch (e) {
                // 忽略日志错误
            }
        }
    }

    // 拦截fetch请求
    function interceptFetch() {
        if (!STATE.originalFetch) {
            STATE.originalFetch = window.fetch;

            window.fetch = function(url, options) {
                // 如果URL是字符串，尝试修复
                if (typeof url === 'string') {
                    // 检查是否是direct_db路径
                    if (url.includes('/direct_db/')) {
                        safeLog(`检测到direct_db路径: ${url}`);

                        // 添加时间戳防止缓存
                        if (!url.includes('_=')) {
                            const separator = url.includes('?') ? '&' : '?';
                            url = `${url}${separator}_=${Date.now()}`;
                            safeLog(`添加时间戳: ${url}`);
                        }
                    }
                }

                // 调用原始fetch函数
                return STATE.originalFetch.call(this, url, options)
                    .then(response => {
                        // 如果请求成功，返回响应
                        if (response.ok) {
                            return response;
                        }

                        // 处理错误
                        if ((response.status === 404 || response.status === 500) && typeof url === 'string' && url.includes('/direct_db/')) {
                            safeLog(`API请求错误(${response.status}): ${url}，尝试使用备用路径`);

                            // 构建备用URL
                            let backupUrl = url;

                            // 如果是v3.1版本，尝试使用v3.0版本的路径
                            if (url.includes('/v3.1/direct_db/')) {
                                backupUrl = url.replace('/v3.1/direct_db/', '/direct_db/');
                                safeLog(`尝试备用路径: ${backupUrl}`);

                                // 重新发送请求
                                return STATE.originalFetch.call(this, backupUrl, options);
                            }

                            // 如果是v3.0版本，尝试使用v3.1版本的路径
                            if (url.startsWith('/direct_db/')) {
                                backupUrl = `/v3.1${url}`;
                                safeLog(`尝试备用路径: ${backupUrl}`);

                                // 重新发送请求
                                return STATE.originalFetch.call(this, backupUrl, options);
                            }

                            // 如果是direct_db路径，尝试使用常规API路径
                            if (url.includes('/direct_db/analysis')) {
                                // 提取参数
                                const urlObj = new URL(url, window.location.origin);
                                const novelId = urlObj.searchParams.get('novel_id');
                                const dimension = urlObj.searchParams.get('dimension');

                                if (novelId && dimension) {
                                    backupUrl = `/api/novel/${novelId}/analysis/${dimension}?_=${Date.now()}`;
                                    safeLog(`尝试常规API路径: ${backupUrl}`);

                                    // 重新发送请求
                                    return STATE.originalFetch.call(this, backupUrl, options);
                                }
                            }

                            // 如果是direct_db章节路径，尝试使用常规API路径
                            if (url.includes('/direct_db/chapter_analysis')) {
                                // 提取参数
                                const urlObj = new URL(url, window.location.origin);
                                const novelId = urlObj.searchParams.get('novel_id');
                                const chapterId = urlObj.searchParams.get('chapter_id');
                                const dimension = urlObj.searchParams.get('dimension');

                                if (novelId && chapterId && dimension) {
                                    backupUrl = `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}?_=${Date.now()}`;
                                    safeLog(`尝试常规章节API路径: ${backupUrl}`);

                                    // 重新发送请求
                                    return STATE.originalFetch.call(this, backupUrl, options);
                                }
                            }
                        }

                        // 其他错误，返回原始响应
                        return response;
                    });
            };

            safeLog('已拦截fetch请求');
        }
    }

    // 初始化
    function initialize() {
        if (STATE.initialized) {
            return;
        }

        safeLog('初始化直接数据库路径修复脚本');

        // 拦截fetch请求
        interceptFetch();

        // 标记为已初始化
        STATE.initialized = true;
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出到全局命名空间，以便调试
    window.directDbPathFix = {
        config: CONFIG,
        state: STATE
    };

    safeLog('脚本加载完成');
})();
