@echo off
chcp 65001 > nul
title 九猫小说分析系统 - 路由端点错误修复工具

echo ================================================
echo        九猫小说分析系统 - 路由端点错误修复工具
echo ================================================
echo 此工具将修复"Could not build url for endpoint 'upload'"错误
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python。请确保Python已安装并添加到PATH环境变量中。
    echo.
    echo 按任意键退出...
    pause > nul
    exit /b 1
)

echo [信息] 正在修复路由端点错误...
echo.

python fix_endpoint_error.py

if %errorlevel% neq 0 (
    echo.
    echo [警告] 修复过程中出现错误，请查看日志文件了解详情。
) else (
    echo.
    echo [成功] 修复完成！
)

echo.
echo 请重新启动九猫系统以应用更改。
echo.
echo 按任意键退出...
pause > nul
