/**
 * 九猫 - 图表完全禁用脚本
 * 彻底禁用所有图表功能，节省系统资源
 * 版本: 2.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('图表完全禁用脚本已加载 - 版本2.0.0');

    // 创建一个空的Chart对象，防止其他脚本报错
    window.Chart = function() {
        return {
            destroy: function() { console.log('模拟Chart.destroy被调用'); },
            update: function() { console.log('模拟Chart.update被调用'); },
            data: {},
            options: {},
            config: {},
            canvas: null
        };
    };

    // 添加必要的静态方法和属性
    window.Chart.defaults = {};
    window.Chart.controllers = {};
    window.Chart.helpers = {};
    window.Chart.instances = {};
    window.Chart.registry = {
        getController: function() { return {}; },
        getScale: function() { return {}; },
        register: function() { console.log('模拟Chart.registry.register被调用'); }
    };

    // 模拟Chart.js的版本号
    window.Chart.version = '3.9.1';

    // 阻止加载Chart.js
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.apply(document, arguments);

        if (tagName.toLowerCase() === 'script') {
            // 拦截添加Chart.js的尝试
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
                if (name === 'src' && (
                    value.includes('chart.js') ||
                    value.includes('chart.min.js') ||
                    value.includes('Chart.js') ||
                    value.includes('Chart.min.js') ||
                    value.includes('cdn.jsdelivr.net/npm/chart') ||
                    value.includes('cdnjs.cloudflare.com/ajax/libs/Chart')
                )) {
                    console.log('已阻止加载Chart.js:', value);

                    // 模拟脚本加载成功
                    setTimeout(() => {
                        if (typeof element.onload === 'function') {
                            element.onload();
                        }
                    }, 10);

                    // 不实际设置src属性
                    return;
                }

                return originalSetAttribute.apply(this, arguments);
            };
        }

        if (tagName.toLowerCase() === 'canvas') {
            // 拦截创建canvas元素的尝试
            const originalGetContext = element.getContext;
            element.getContext = function(contextType) {
                if (contextType === '2d') {
                    // 检查是否是图表canvas
                    setTimeout(() => {
                        if (
                            element.className.includes('chart') ||
                            element.getAttribute('data-dimension') ||
                            element.id && (
                                element.id.includes('chart') ||
                                element.id.includes('Chart')
                            )
                        ) {
                            // 是图表canvas，替换为文本提示
                            replaceCanvasWithText(element);
                        }
                    }, 100);
                }

                return originalGetContext.apply(this, arguments);
            };
        }

        return element;
    };

    // 替换canvas为文本提示
    function replaceCanvasWithText(canvas) {
        if (!canvas || !canvas.parentNode) return;

        // 获取维度信息
        const dimension = canvas.getAttribute('data-dimension') || '';

        // 创建替代元素
        const replacementDiv = document.createElement('div');
        replacementDiv.className = 'chart-disabled-notice';
        replacementDiv.style.cssText = 'padding: 15px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; text-align: center; margin: 10px 0;';

        // 设置提示文本
        replacementDiv.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">图表功能已禁用</div>
            <p style="color: #6c757d; margin-bottom: 0;">为节省系统资源，图表显示功能已被禁用。请查看文本分析结果。</p>
            ${dimension ? `<small style="color: #6c757d;">维度: ${dimension}</small>` : ''}
        `;

        // 替换canvas
        try {
            canvas.parentNode.replaceChild(replacementDiv, canvas);
            console.log('已替换图表canvas为文本提示');
        } catch (e) {
            console.error('替换canvas时出错:', e);
        }
    }

    // 替换页面上已存在的图表canvas
    function replaceExistingCharts() {
        // 查找所有图表canvas
        const chartCanvases = document.querySelectorAll('canvas.analysis-chart, canvas[data-dimension], canvas[id*="chart"], canvas[id*="Chart"]');

        console.log(`找到 ${chartCanvases.length} 个图表canvas，准备替换`);

        // 替换每个canvas
        chartCanvases.forEach(canvas => {
            replaceCanvasWithText(canvas);
        });
    }

    // 禁用图表相关的全局函数
    window.initializeCharts = function() {
        console.log('图表初始化函数被调用，但图表功能已禁用');
        return false;
    };

    // 禁用图表加载器
    window.chartLoader = {
        ensureLoaded: function(callback) {
            console.log('图表加载器被调用，但图表功能已禁用');
            if (typeof callback === 'function') {
                setTimeout(callback, 10);
            }
        },
        loadChartJs: function(callback) {
            console.log('图表加载器被调用，但图表功能已禁用');
            if (typeof callback === 'function') {
                setTimeout(callback, 10);
            }
        }
    };

    // 在DOM加载完成后替换已存在的图表
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', replaceExistingCharts);
    } else {
        // 如果DOM已加载完成，立即执行
        replaceExistingCharts();
    }

    // 监听DOM变化，替换新添加的图表
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length) {
                    // 检查是否添加了新的canvas元素
                    mutation.addedNodes.forEach(function(node) {
                        if (node.tagName === 'CANVAS') {
                            // 检查是否是图表canvas
                            if (
                                node.className.includes('chart') ||
                                node.getAttribute('data-dimension') ||
                                node.id && (
                                    node.id.includes('chart') ||
                                    node.id.includes('Chart')
                                )
                            ) {
                                replaceCanvasWithText(node);
                            }
                        } else if (node.nodeType === 1) {
                            // 检查添加的元素内部是否有canvas
                            const canvases = node.querySelectorAll('canvas.analysis-chart, canvas[data-dimension], canvas[id*="chart"], canvas[id*="Chart"]');
                            canvases.forEach(canvas => {
                                replaceCanvasWithText(canvas);
                            });
                        }
                    });
                }
            });
        });

        // 开始观察整个文档
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });

        console.log('已启动DOM变化监听，将替换新添加的图表');
    }

    console.log('图表完全禁用脚本初始化完成');
})();