"""
测试九猫系统监控功能
"""
import os
import sys
import time
import logging
import random
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

import config
from src.web.system_monitor import (
    get_system_metrics, get_api_stats, get_db_stats, 
    get_alerts, get_alert_settings, update_alert_settings,
    get_logs, get_dimension_stats
)
from src.models.system_alert import SystemAlert
from src.models.system_metric import SystemMetric
from src.models.api_log import ApiLog
from src.database import Session

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("system_monitor_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("system_monitor_test")

def test_system_metrics():
    """测试系统指标收集功能"""
    logger.info("=== 测试系统指标收集功能 ===")
    
    # 获取系统指标
    metrics = get_system_metrics()
    
    # 打印指标
    logger.info(f"CPU使用率: {metrics['cpu_usage']}%")
    logger.info(f"内存使用率: {metrics['memory_usage']}%")
    logger.info(f"磁盘使用率: {metrics['disk_usage']}%")
    logger.info(f"系统运行时间: {metrics['uptime']}")
    
    return metrics

def test_api_stats():
    """测试API调用统计功能"""
    logger.info("=== 测试API调用统计功能 ===")
    
    # 创建一些测试API日志
    session = Session()
    try:
        # 清除现有测试数据
        session.query(ApiLog).delete()
        session.commit()
        
        # 创建一些测试API日志
        for i in range(20):
            # 随机生成一些API调用记录
            status_code = random.choice([200, 200, 200, 400, 500, None])
            error_type = None
            if status_code == 400:
                error_type = "bad_request"
            elif status_code == 500:
                error_type = "server_error"
            elif status_code is None:
                error_type = "timeout"
                status_code = None
            
            # 创建API日志
            api_log = ApiLog(
                endpoint="text-generation",
                method="POST",
                parameters={
                    "analysis_type": random.choice(["language_style", "structure", "paragraph_length"]),
                    "max_tokens": 1000,
                    "text_length": random.randint(1000, 10000)
                },
                status_code=status_code,
                response_time=random.uniform(500, 5000),  # 500ms到5000ms之间
                response_size=random.randint(1000, 100000),
                error_type=error_type,
                error_message="测试错误信息" if error_type else None,
                timestamp=datetime.now() - timedelta(minutes=random.randint(0, 1440))  # 随机时间在过去24小时内
            )
            session.add(api_log)
        
        session.commit()
        logger.info(f"已创建20条测试API日志")
        
        # 获取API调用统计
        stats = get_api_stats()
        
        # 打印统计信息
        logger.info(f"今日调用次数: {stats['today_calls']}")
        logger.info(f"本周调用次数: {stats['week_calls']}")
        logger.info(f"本月调用次数: {stats['month_calls']}")
        logger.info(f"成功率: {stats['success_rate']}%")
        logger.info(f"平均响应时间: {stats['avg_response_time']}ms")
        
        return stats
    except Exception as e:
        logger.error(f"测试API调用统计时出错: {str(e)}")
        session.rollback()
        return None
    finally:
        session.close()

def test_db_stats():
    """测试数据库连接池状态功能"""
    logger.info("=== 测试数据库连接池状态功能 ===")
    
    # 获取数据库连接池状态
    stats = get_db_stats()
    
    # 打印状态
    logger.info(f"连接池大小: {stats['pool_size']}")
    logger.info(f"活跃连接数: {stats['active_connections']}")
    logger.info(f"溢出连接数: {stats['overflow']}")
    logger.info(f"连接池使用率: {stats['usage_percent']}%")
    logger.info(f"连接池状态: {stats['status']}")
    
    return stats

def test_alerts():
    """测试系统告警功能"""
    logger.info("=== 测试系统告警功能 ===")
    
    # 创建一些测试告警
    session = Session()
    try:
        # 清除现有测试数据
        session.query(SystemAlert).delete()
        session.commit()
        
        # 创建一些测试告警
        alert_levels = ["info", "warning", "critical"]
        alert_titles = [
            "CPU使用率过高", 
            "内存使用率过高", 
            "磁盘使用率过高", 
            "数据库连接池接近容量上限",
            "API错误率过高"
        ]
        
        for i in range(10):
            level = random.choice(alert_levels)
            title = random.choice(alert_titles)
            
            # 创建告警
            alert = SystemAlert(
                level=level,
                title=title,
                message=f"测试告警消息: {title}，值: {random.randint(80, 100)}%",
                timestamp=datetime.now() - timedelta(minutes=random.randint(0, 1440))  # 随机时间在过去24小时内
            )
            session.add(alert)
        
        session.commit()
        logger.info(f"已创建10条测试告警")
        
        # 获取告警
        alerts = get_alerts(limit=5)
        
        # 打印告警
        logger.info(f"获取到 {len(alerts)} 条告警")
        for alert in alerts:
            logger.info(f"[{alert.level}] {alert.title}: {alert.message}")
        
        return alerts
    except Exception as e:
        logger.error(f"测试系统告警时出错: {str(e)}")
        session.rollback()
        return None
    finally:
        session.close()

def test_alert_settings():
    """测试告警设置功能"""
    logger.info("=== 测试告警设置功能 ===")
    
    # 获取当前设置
    current_settings = get_alert_settings()
    logger.info(f"当前告警设置: {current_settings}")
    
    # 更新设置
    new_settings = {
        'enabled': not current_settings['enabled'],
        'email_enabled': not current_settings['email_enabled'],
        'thresholds': {
            'cpu_usage': 95,
            'memory_usage': 90,
            'disk_usage': 95,
            'db_connections': 85,
            'api_error_rate': 15
        }
    }
    
    success = update_alert_settings(new_settings)
    logger.info(f"更新告警设置: {'成功' if success else '失败'}")
    
    # 获取更新后的设置
    updated_settings = get_alert_settings()
    logger.info(f"更新后的告警设置: {updated_settings}")
    
    return updated_settings

def test_system_metrics_collection():
    """测试系统指标收集和存储功能"""
    logger.info("=== 测试系统指标收集和存储功能 ===")
    
    # 清除现有测试数据
    session = Session()
    try:
        session.query(SystemMetric).delete()
        session.commit()
        
        # 模拟收集24小时的系统指标
        for i in range(24):
            # 创建一个系统指标记录
            metric = SystemMetric(
                timestamp=datetime.now() - timedelta(hours=i),
                cpu_usage=random.uniform(10, 90),
                memory_usage=random.uniform(20, 80),
                disk_usage=random.uniform(30, 70),
                api_calls_total=random.randint(10, 100),
                api_calls_success=random.randint(8, 90),
                api_calls_error=random.randint(0, 10),
                api_calls_timeout=random.randint(0, 5),
                api_avg_response_time=random.uniform(100, 2000),
                db_pool_size=5,
                db_active_connections=random.randint(0, 5),
                db_overflow=random.randint(0, 3),
                db_usage_percent=random.uniform(0, 100)
            )
            session.add(metric)
        
        session.commit()
        logger.info(f"已创建24小时的测试系统指标数据")
        
        # 查询最近的系统指标
        recent_metrics = session.query(SystemMetric).order_by(SystemMetric.timestamp.desc()).limit(5).all()
        
        # 打印最近的系统指标
        logger.info(f"最近的系统指标:")
        for metric in recent_metrics:
            logger.info(f"时间: {metric.timestamp}, CPU: {metric.cpu_usage}%, 内存: {metric.memory_usage}%, 磁盘: {metric.disk_usage}%")
        
        return recent_metrics
    except Exception as e:
        logger.error(f"测试系统指标收集和存储时出错: {str(e)}")
        session.rollback()
        return None
    finally:
        session.close()

def run_all_tests():
    """运行所有测试"""
    logger.info("开始测试九猫系统监控功能...")
    
    # 测试系统指标收集功能
    test_system_metrics()
    
    # 测试API调用统计功能
    test_api_stats()
    
    # 测试数据库连接池状态功能
    test_db_stats()
    
    # 测试系统告警功能
    test_alerts()
    
    # 测试告警设置功能
    test_alert_settings()
    
    # 测试系统指标收集和存储功能
    test_system_metrics_collection()
    
    logger.info("所有测试完成！")

if __name__ == "__main__":
    run_all_tests()
    
    print("\n测试完成！详细结果请查看 system_monitor_test.log 文件。")
