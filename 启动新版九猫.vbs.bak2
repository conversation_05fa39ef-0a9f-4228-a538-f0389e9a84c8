' 新版九猫小说分析系统启动脚本 (VBS版本)
' 此脚本会在后台启动新版九猫系统，并自动打开浏览器，无需显示命令行窗口

' 设置工作目录为脚本所在目录
Set fso = CreateObject("Scripting.FileSystemObject")
currentPath = fso.GetParentFolderName(WScript.ScriptFullName)
Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = currentPath

' 确保工作目录设置正确
On Error Resume Next
Dim testFile
Set testFile = fso.CreateTextFile("test_dir.txt", True)
If Err.Number <> 0 Then
    MsgBox "无法在当前目录创建文件，可能是权限问题。" & vbCrLf & _
           "错误: " & Err.Description & vbCrLf & _
           "当前目录: " & currentPath, _
           vbExclamation, "新版九猫小说分析系统 - 错误"
    WScript.Quit
End If
testFile.WriteLine("目录测试文件")
testFile.Close
fso.DeleteFile "test_dir.txt"
On Error Goto 0

' 检查并创建日志目录
If Not fso.FolderExists("logs") Then
    fso.CreateFolder("logs")
End If



' 清理旧日志文件，只保留最新的5个
Sub CleanupOldLogs()
    On Error Resume Next

    Dim logFolder, logFiles, file
    Set logFolder = fso.GetFolder(currentPath & "\logs")
    Set logFiles = logFolder.Files

    ' 创建一个数组来存储日志文件
    Dim fileArray()
    ReDim fileArray(logFiles.Count - 1)

    ' 填充数组
    Dim i, fileCount
    i = 0
    fileCount = 0

    For Each file In logFiles
        If LCase(Right(file.Name, 4)) = ".log" Then
            fileArray(i) = file.Path
            i = i + 1
            fileCount = fileCount + 1
        End If
    Next

    ' 如果文件数量超过5个，删除最旧的文件
    If fileCount > 5 Then
        ' 按修改日期排序（冒泡排序）
        Dim j, temp
        For i = 0 To fileCount - 2
            For j = 0 To fileCount - i - 2
                If fso.GetFile(fileArray(j)).DateLastModified > fso.GetFile(fileArray(j+1)).DateLastModified Then
                    temp = fileArray(j)
                    fileArray(j) = fileArray(j+1)
                    fileArray(j+1) = temp
                End If
            Next
        Next

        ' 删除最旧的文件（保留最新的5个）
        For i = 0 To fileCount - 6
            If fileArray(i) <> "" Then
                fso.DeleteFile(fileArray(i))
            End If
        Next
    End If
End Sub

' 执行日志清理
CleanupOldLogs()

' 检查psutil是否已安装
Function IsPsutilInstalled()
    Dim result
    result = WshShell.Run("python -c ""import psutil""", 0, True)
    IsPsutilInstalled = (result = 0)
End Function

' 安装psutil
Sub InstallPsutil()
    Dim result
    result = WshShell.Run("pip install psutil", 1, True)
    If result <> 0 Then
        MsgBox "无法安装psutil模块。系统将以有限的内存监控功能运行。" & vbCrLf & _
               "建议手动安装: pip install psutil", _
               48, "新版九猫小说分析系统 - 警告"
    End If
End Sub

' 检查并安装psutil
If Not IsPsutilInstalled() Then
    If MsgBox("未检测到psutil模块，该模块用于内存监控和优化。" & vbCrLf & _
              "是否立即安装？", _
              vbYesNo + vbQuestion, "新版九猫小说分析系统") = vbYes Then
        InstallPsutil()
    End If
End If

' 检查端口5001是否被占用
Function IsPortInUse()
    Dim result
    result = WshShell.Run("netstat -ano | findstr :5001 | findstr LISTENING", 0, True)
    IsPortInUse = (result = 0)
End Function

' 终止占用端口5001的进程
Sub KillPort5001Process()
    On Error Resume Next

    ' 创建临时批处理文件来终止进程
    Set killPortFile = fso.CreateTextFile("kill_port.bat", True)
    killPortFile.WriteLine("@echo off")
    killPortFile.WriteLine("for /f ""tokens=5"" %%a in ('netstat -ano ^| findstr :5001 ^| findstr LISTENING') do (")
    killPortFile.WriteLine("    taskkill /f /pid %%a >nul 2>&1")
    killPortFile.WriteLine(")")
    killPortFile.Close

    ' 运行批处理文件
    WshShell.Run "kill_port.bat", 0, True

    ' 删除临时文件
    fso.DeleteFile "kill_port.bat"
End Sub

' 检查并释放端口
If IsPortInUse() Then
    If MsgBox("端口5001已被占用，需要释放才能启动新版九猫系统。" & vbCrLf & _
              "是否尝试终止占用该端口的进程？", _
              vbYesNo + vbQuestion, "新版九猫小说分析系统") = vbYes Then
        KillPort5001Process()
    End If
End If

' 显示启动消息
MsgBox "新版九猫小说分析系统正在启动..." & vbCrLf & vbCrLf & _
       "系统将在后台运行，浏览器将在10秒钟后自动打开。" & vbCrLf & _
       "请不要关闭浏览器窗口，否则需要手动访问: http://localhost:5001", _
       64, "新版九猫小说分析系统"

' 创建临时批处理文件来设置环境变量并运行Python
Set tempFile = fso.CreateTextFile("temp_run_new.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & currentPath & """")
tempFile.WriteLine("echo 当前工作目录: %CD%")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set USE_MOCK_API=False")
tempFile.WriteLine("set ENABLE_MOCK_ANALYSIS=False")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("set MEMORY_OPTIMIZED=True")
tempFile.WriteLine("set LOW_MEMORY_MODE=True")
tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=75")
tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=85")
tempFile.WriteLine("set MAX_DB_CONNECTIONS=30")
tempFile.WriteLine("set DB_POOL_SIZE=30")
tempFile.WriteLine("set DB_MAX_OVERFLOW=20")
tempFile.WriteLine("set THREAD_POOL_SIZE=8")
tempFile.WriteLine("set MAX_WORKERS=8")
tempFile.WriteLine("set DISABLE_PARALLEL_ANALYSIS=False")
tempFile.WriteLine("set REDUCE_LOGGING=True")
tempFile.WriteLine("set OPTIMIZE_STATIC_FILES=True")
tempFile.WriteLine("set OPTIMIZE_CHART_RENDERING=True")
tempFile.WriteLine("set OPTIMIZE_LARGE_TEXT=True")
tempFile.WriteLine("set USE_EXTERNAL_STORAGE=True")
tempFile.WriteLine("set EXTERNAL_STORAGE_PATH=E:\艹，又来一次\九猫\external_storage")
tempFile.WriteLine("set LOG_PATH=E:\艹，又来一次\九猫\logs")
tempFile.WriteLine("set TEMP_DIR=E:\艹，又来一次\九猫\temp")
tempFile.WriteLine("set CACHE_DIR=E:\艹，又来一次\九猫\cache")
tempFile.WriteLine("set DB_BACKUP_DIR=E:\艹，又来一次\九猫\db_backup")
tempFile.WriteLine("set MEMORY_STATS_DIR=E:\艹，又来一次\九猫\memory_stats")
tempFile.WriteLine("set DISABLE_AUTO_REFRESH=True")
tempFile.WriteLine("set MEMORY_CHECK_INTERVAL=3")
tempFile.WriteLine("set DISABLE_CHARTS=True")
tempFile.WriteLine("set OPTIMIZE_DIMENSION_DETAIL=True")
tempFile.WriteLine("set ENABLE_LOG_FILTER=True")
tempFile.WriteLine("set SEPARATE_ANALYSIS_PROCESS=True")
tempFile.WriteLine("set ENABLE_BUTTON_TEXT_SUPREME_FIX=True")
tempFile.WriteLine("set FORCE_BUTTON_TEXT_VISIBILITY=True")

tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("echo 检查Python是否可用...")
tempFile.WriteLine("python --version")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo Python未找到，请确保Python已安装并添加到PATH环境变量中")
tempFile.WriteLine("    pause")
tempFile.WriteLine("    exit /b 1")
tempFile.WriteLine(")")
tempFile.WriteLine("echo 启动新版九猫系统...")
tempFile.WriteLine("python run.py")
tempFile.WriteLine("if %ERRORLEVEL% NEQ 0 (")
tempFile.WriteLine("    echo 启动失败，错误代码: %ERRORLEVEL%")
tempFile.WriteLine("    pause")
tempFile.WriteLine(")")
tempFile.Close

' 在后台运行批处理文件，但显示窗口以便查看输出和错误信息
Dim runResult
runResult = WshShell.Run("cmd /c """ & currentPath & "\temp_run_new.bat""", 1, False)

' 记录启动信息到日志文件
On Error Resume Next
Dim logFile, logFileName
logFileName = "logs\startup_new_" & Year(Now()) & Month(Now()) & Day(Now()) & "_" & Hour(Now()) & Minute(Now()) & Second(Now()) & ".log"
Set logFile = fso.CreateTextFile(logFileName, True)
If Err.Number = 0 Then
    logFile.WriteLine("新版九猫系统启动时间: " & Now())
    logFile.WriteLine("当前目录: " & currentPath)
    logFile.WriteLine("批处理文件: " & currentPath & "\temp_run_new.bat")
    logFile.WriteLine("运行结果: " & runResult)
    logFile.Close
Else
    MsgBox "无法创建日志文件: " & Err.Description, vbExclamation, "新版九猫小说分析系统 - 警告"
    Err.Clear
End If
On Error Goto 0

' 等待15秒钟确保服务启动
WScript.Sleep 15000

' 打开浏览器 - 使用cmd命令强制使用默认浏览器
WshShell.Run "cmd /c start http://localhost:5001/", 0, False

' 显示启动完成消息
MsgBox "新版九猫小说分析系统已启动！" & vbCrLf & vbCrLf & _
       "如果浏览器没有自动打开，请手动访问: http://localhost:5001/" & vbCrLf & _
       "如果系统无法启动，请查看logs目录下的日志文件。", _
       64, "新版九猫小说分析系统 - 启动完成"
