/**
 * 九猫 - 修复加载器
 * 负责按正确顺序加载所有修复脚本
 * 版本: 1.0.0
 */

(function() {
    // 检查是否已加载
    if (window._fixLoaderLoaded) {
        console.log('[修复加载器] 已加载，不重复执行');
        return;
    }

    // 标记已加载
    window._fixLoaderLoaded = true;

    console.log('[修复加载器] 初始化版本1.0.0');

    // 需要加载的脚本列表，按优先级排序
    const scripts = [
        // 核心修复脚本，处理最基本问题
        { src: '/static/js/global-error-handler.js', id: 'global-error-handler', priority: 1 },
        { src: '/static/js/script-error-fix.js', id: 'script-error-fix', priority: 2 },
        { src: '/static/js/jquery-conflict-fix.js', id: 'jquery-conflict-fix', priority: 3 },

        // 功能修复脚本，专注于特定功能
        { src: '/static/js/chapter-navigation-fix.js', id: 'chapter-navigation-fix', priority: 4 },
        { src: '/static/js/novel-page-redirect-fix.js', id: 'novel-page-redirect-fix', priority: 4 },
        { src: '/static/js/novel-link-fix.js', id: 'novel-link-fix', priority: 5 },

        // 新增的小说功能按钮修复脚本 - 最高优先级
        { src: '/static/js/novel-function-button-fix.js', id: 'novel-function-button-fix', priority: 4 },

        // 统一修复脚本，将多个修复集成在一起
        { src: '/static/js/unified-fixes.js', id: 'unified-fixes', priority: 6 },

        // 最终修复，处理所有其他修复未能解决的问题
        { src: '/static/js/final-fixes.js', id: 'final-fixes', priority: 7 }
    ];

    // 检查DOM元素是否存在
    function doesElementExist(id) {
        return document.getElementById(id) !== null;
    }

    // 按优先级加载脚本
    function loadScriptsInOrder() {
        // 按优先级排序
        const sortedScripts = scripts.sort((a, b) => a.priority - b.priority);

        // 记录哪些脚本需要加载
        const scriptsToLoad = sortedScripts.filter(script => !doesElementExist(script.id));

        console.log(`[修复加载器] 需要加载 ${scriptsToLoad.length} 个修复脚本`);

        // 递归加载脚本
        function loadNextScript(index) {
            if (index >= scriptsToLoad.length) {
                console.log('[修复加载器] 所有修复脚本已加载完成');
                return;
            }

            const script = scriptsToLoad[index];
            console.log(`[修复加载器] 正在加载脚本 (${index + 1}/${scriptsToLoad.length}): ${script.src}`);

            // 创建脚本元素
            const scriptElement = document.createElement('script');
            scriptElement.src = script.src;
            scriptElement.id = script.id;
            scriptElement.setAttribute('crossorigin', 'anonymous');

            // 加载完成或出错后加载下一个脚本
            scriptElement.onload = function() {
                console.log(`[修复加载器] 脚本加载成功: ${script.src}`);
                loadNextScript(index + 1);
            };

            scriptElement.onerror = function() {
                console.error(`[修复加载器] 脚本加载失败: ${script.src}`);
                loadNextScript(index + 1);
            };

            // 添加到文档中
            document.head.appendChild(scriptElement);
        }

        // 开始加载第一个脚本
        if (scriptsToLoad.length > 0) {
            loadNextScript(0);
        }
    }

    // 监测页面加载状态执行加载
    if (document.readyState === 'loading') {
        // 如果DOM还在加载中，等待DOMContentLoaded事件
        document.addEventListener('DOMContentLoaded', loadScriptsInOrder);
    } else {
        // 如果DOM已加载完成，立即加载
        loadScriptsInOrder();
    }

    // 提供手动触发重新加载的方法
    window.fixLoader = {
        reload: loadScriptsInOrder
    };
})();