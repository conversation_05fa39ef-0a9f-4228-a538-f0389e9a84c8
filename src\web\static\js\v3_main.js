/**
 * 九猫小说分析写作系统v3.0 主JavaScript文件
 */

// 在DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('九猫小说分析写作系统v3.0 已加载');

    // 初始化工具提示
    initTooltips();

    // 初始化弹出框
    initPopovers();

    // 初始化Markdown渲染
    initMarkdownRendering();

    // 初始化分析结果折叠面板
    initCollapsiblePanels();

    // 初始化进度条动画
    initProgressBars();

    // 初始化滚动动画
    initScrollAnimations();

    // 初始化错误处理
    initErrorHandling();
});

/**
 * 初始化Bootstrap工具提示
 */
function initTooltips() {
    try {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } catch (error) {
        console.error('初始化工具提示时出错:', error);
    }
}

/**
 * 初始化Bootstrap弹出框
 */
function initPopovers() {
    try {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function(popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    } catch (error) {
        console.error('初始化弹出框时出错:', error);
    }
}

/**
 * 初始化Markdown渲染
 */
function initMarkdownRendering() {
    try {
        const markdownElements = document.querySelectorAll('.markdown-content');
        if (markdownElements.length > 0 && typeof marked !== 'undefined') {
            markdownElements.forEach(element => {
                const content = element.textContent || element.innerText;
                element.innerHTML = marked.parse(content);
            });
        }
    } catch (error) {
        console.error('初始化Markdown渲染时出错:', error);
    }
}

/**
 * 初始化分析结果折叠面板
 */
function initCollapsiblePanels() {
    try {
        const collapsibleHeaders = document.querySelectorAll('.collapsible-header');
        collapsibleHeaders.forEach(header => {
            header.addEventListener('click', function() {
                this.classList.toggle('active');
                const content = this.nextElementSibling;
                if (content.style.maxHeight) {
                    content.style.maxHeight = null;
                } else {
                    content.style.maxHeight = content.scrollHeight + 'px';
                }
            });
        });
    } catch (error) {
        console.error('初始化折叠面板时出错:', error);
    }
}

/**
 * 初始化进度条动画
 */
function initProgressBars() {
    try {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const targetWidth = bar.getAttribute('aria-valuenow') + '%';
            setTimeout(() => {
                bar.style.width = targetWidth;
            }, 100);
        });
    } catch (error) {
        console.error('初始化进度条动画时出错:', error);
    }
}

/**
 * 初始化滚动动画
 */
function initScrollAnimations() {
    try {
        const fadeElements = document.querySelectorAll('.fade-in');

        const fadeInOnScroll = function() {
            fadeElements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.add('visible');
                }
            });
        };

        window.addEventListener('scroll', fadeInOnScroll);
        fadeInOnScroll(); // 初始检查
    } catch (error) {
        console.error('初始化滚动动画时出错:', error);
    }
}

/**
 * 初始化错误处理
 */
function initErrorHandling() {
    window.onerror = function(message, source, lineno, colno, error) {
        // 忽略跨域脚本错误
        if (message === 'Script error.' && !source && lineno === 0 && colno === 0) {
            console.warn('捕获到跨域脚本错误，已忽略');
            return true;
        }

        console.error('捕获到错误:', message, '来源:', source, '行:', lineno, '列:', colno);

        // 如果是JSON解析错误，尝试修复
        if (message.includes('JSON.parse') || message.includes('Unexpected token')) {
            console.warn('检测到JSON解析错误，尝试修复...');
            fixJsonParsingErrors();
        }

        // 如果是Canvas相关错误，尝试修复
        if (message.includes('getContext') || message.includes('canvas')) {
            console.warn('检测到Canvas相关错误，尝试修复...');
            fixCanvasErrors();
        }

        return true; // 阻止错误冒泡
    };
}

/**
 * 尝试修复Canvas相关错误
 */
function fixCanvasErrors() {
    try {
        // 延迟重新初始化所有图表
        setTimeout(function() {
            console.info('尝试重新初始化所有图表...');

            // 查找所有canvas元素
            const canvasElements = document.querySelectorAll('canvas');
            canvasElements.forEach(canvas => {
                // 为每个canvas添加一个标记，表示已经尝试修复
                if (!canvas.dataset.fixAttempted) {
                    canvas.dataset.fixAttempted = 'true';

                    // 重新设置canvas的宽高，触发重绘
                    const width = canvas.width;
                    const height = canvas.height;
                    canvas.width = width;
                    canvas.height = height;

                    console.info('已重置Canvas:', canvas.id || 'unnamed');
                }
            });
        }, 500);
    } catch (error) {
        console.error('修复Canvas错误时出错:', error);
    }
}

/**
 * 尝试修复JSON解析错误
 */
function fixJsonParsingErrors() {
    try {
        // 查找所有可能包含JSON的脚本标签
        const scriptTags = document.querySelectorAll('script:not([src])');
        scriptTags.forEach(script => {
            const content = script.textContent || script.innerText;
            if (content.includes('JSON.parse')) {
                console.warn('找到可能包含JSON解析的脚本:', content.substring(0, 100) + '...');
            }
        });

        // 查找所有data-json属性
        const jsonElements = document.querySelectorAll('[data-json]');
        jsonElements.forEach(element => {
            try {
                const jsonStr = element.getAttribute('data-json');
                JSON.parse(jsonStr); // 尝试解析
            } catch (e) {
                console.warn('找到无效的JSON数据:', element.getAttribute('data-json').substring(0, 100) + '...');
                // 尝试修复常见问题
                let fixedJson = element.getAttribute('data-json')
                    .replace(/'/g, '"') // 单引号替换为双引号
                    .replace(/([{,])\s*(\w+)\s*:/g, '$1"$2":') // 为键名添加引号
                    .replace(/:\s*([^",\{\[\]\}]+)([,\}\]])/g, ':"$1"$2'); // 为非引号包裹的值添加引号

                element.setAttribute('data-json', fixedJson);
                console.info('尝试修复后的JSON:', fixedJson.substring(0, 100) + '...');
            }
        });
    } catch (error) {
        console.error('修复JSON解析错误时出错:', error);
    }
}

/**
 * 加载小说信息
 * @param {number} novelId - 小说ID
 * @param {function} callback - 回调函数
 */
function loadNovelInfo(novelId, callback) {
    // 检查是否已经缓存了小说信息
    if (window.novelInfoCache && window.novelInfoCache[novelId]) {
        callback(null, window.novelInfoCache[novelId]);
        return;
    }

    fetch(`/api/novel/${novelId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误 ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.novel) {
                // 缓存小说信息
                if (!window.novelInfoCache) {
                    window.novelInfoCache = {};
                }
                window.novelInfoCache[novelId] = data.novel;

                callback(null, data.novel);
            } else {
                callback(new Error(data.error || '加载小说信息失败'), null);
            }
        })
        .catch(error => {
            console.error('加载小说信息时出错:', error);
            // 如果API不存在，返回一个空对象，避免前端报错
            if (error.message.includes('404')) {
                console.warn('小说信息API不存在，返回空对象');
                callback(null, {});
            } else {
                callback(error, null);
            }
        });
}

/**
 * 加载分析结果
 * @param {number} novelId - 小说ID
 * @param {string} dimension - 分析维度
 * @param {function} callback - 回调函数
 */
function loadAnalysisResult(novelId, dimension, callback) {
    fetch(`/api/novel/${novelId}/analysis/${dimension}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误 ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.result) {
                callback(null, data.result);
            } else {
                callback(new Error(data.error || '加载分析结果失败'), null);
            }
        })
        .catch(error => {
            console.error('加载分析结果时出错:', error);
            callback(error, null);
        });
}

/**
 * 加载章节分析结果
 * @param {number} novelId - 小说ID
 * @param {number} chapterId - 章节ID
 * @param {string} dimension - 分析维度
 * @param {function} callback - 回调函数
 */
function loadChapterAnalysisResult(novelId, chapterId, dimension, callback) {
    fetch(`/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.result) {
                callback(null, data.result);
            } else {
                callback(new Error(data.error || '加载章节分析结果失败'), null);
            }
        })
        .catch(error => {
            console.error('加载章节分析结果时出错:', error);
            callback(error, null);
        });
}

/**
 * 加载推理过程内容
 * @param {number} novelId - 小说ID
 * @param {string} dimension - 分析维度
 * @param {function} callback - 回调函数
 */
function loadReasoningContent(novelId, dimension, callback) {
    fetch(`/api/novel/${novelId}/analysis/${dimension}/reasoning_content`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.content) {
                callback(null, data.content);
            } else {
                callback(new Error(data.error || '加载推理过程内容失败'), null);
            }
        })
        .catch(error => {
            console.error('加载推理过程内容时出错:', error);
            callback(error, null);
        });
}

/**
 * 加载章节推理过程内容
 * @param {number} novelId - 小说ID
 * @param {number} chapterId - 章节ID
 * @param {string} dimension - 分析维度
 * @param {function} callback - 回调函数
 */
function loadChapterReasoningContent(novelId, chapterId, dimension, callback) {
    fetch(`/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.content) {
                callback(null, data.content);
            } else {
                callback(new Error(data.error || '加载章节推理过程内容失败'), null);
            }
        })
        .catch(error => {
            console.error('加载章节推理过程内容时出错:', error);
            callback(error, null);
        });
}

/**
 * 格式化日期时间
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(dateString) {
    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        console.error('格式化日期时间时出错:', error);
        return dateString;
    }
}

/**
 * 格式化字数
 * @param {number} wordCount - 字数
 * @returns {string} 格式化后的字数
 */
function formatWordCount(wordCount) {
    try {
        if (wordCount >= 10000) {
            return (wordCount / 10000).toFixed(1) + '万';
        } else if (wordCount >= 1000) {
            return (wordCount / 1000).toFixed(1) + '千';
        } else {
            return wordCount.toString();
        }
    } catch (error) {
        console.error('格式化字数时出错:', error);
        return wordCount.toString();
    }
}

/**
 * 显示通知
 * @param {string} message - 消息内容
 * @param {string} type - 通知类型 (success, info, warning, danger)
 * @param {number} duration - 显示时长(毫秒)
 */
function showNotification(message, type = 'info', duration = 3000) {
    try {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show notification`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    } catch (error) {
        console.error('显示通知时出错:', error);
    }
}
