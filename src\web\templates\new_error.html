{% extends "new_base.html" %}

{% block title %}错误 {{ error_code }} - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card text-center">
            <div class="card-body py-5">
                <h1 class="display-1 text-danger mb-4">{{ error_code }}</h1>
                <h2 class="mb-4">{{ error_message }}</h2>
                <p class="lead mb-4">很抱歉，系统遇到了一些问题。</p>
                
                {% if error_code == 404 %}
                <p>您访问的页面不存在或已被移除。</p>
                {% elif error_code == 500 %}
                <p>服务器内部错误，请稍后再试或联系管理员。</p>
                {% endif %}
                
                <div class="mt-5">
                    <a href="{{ url_for('new.index') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>返回首页
                    </a>
                    <button class="btn btn-outline-secondary btn-lg ms-2" onclick="history.back()">
                        <i class="fas fa-arrow-left me-2"></i>返回上一页
                    </button>
                </div>
            </div>
        </div>
        
        {% if error_code == 500 %}
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">技术信息</h3>
            </div>
            <div class="card-body">
                <p>如果问题持续存在，请将以下信息提供给技术支持：</p>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        错误时间
                        <span>{{ now.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        请求路径
                        <span>{{ request.path }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        请求方法
                        <span>{{ request.method }}</span>
                    </li>
                </ul>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
