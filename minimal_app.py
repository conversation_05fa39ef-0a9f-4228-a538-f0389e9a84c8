"""
最小化的Flask应用，用于测试基本功能
"""
from flask import Flask, render_template_string

app = Flask(__name__)

@app.route('/')
def index():
    """首页"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>最小化测试</title>
        <style>
            body { font-family: Arial; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            h1 { color: #333; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>最小化Flask应用测试</h1>
            <p>如果您能看到这个页面，说明Flask应用正常运行。</p>
        </div>
    </body>
    </html>
    """
    return render_template_string(html)

if __name__ == '__main__':
    print("启动最小化Flask应用...")
    print("请访问: http://localhost:5002")
    app.run(host='0.0.0.0', port=5002, debug=True)
