/**
 * 九猫系统 模板布局修复脚本
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于修复章节列表中设定模板的显示问题，使其在右侧显示而不是挤在左侧
 */

(function() {
    console.log('[模板布局修复] 初始化...');
    
    // 全局变量
    let isFixed = false;
    let observer = null;
    
    // 修复模板布局
    function fixTemplateLayout() {
        console.log('[模板布局修复] 开始修复模板布局...');
        
        // 检查是否已经修复
        if (isFixed) {
            console.log('[模板布局修复] 模板布局已修复，无需再次修复');
            return;
        }
        
        try {
            // 查找章节列表容器
            const chapterListContainer = document.querySelector('.chapter-list-container');
            if (!chapterListContainer) {
                console.log('[模板布局修复] 未找到章节列表容器，可能不在章节列表页面');
                return;
            }
            
            // 查找模板容器
            const templateContainer = document.querySelector('.template-container');
            if (!templateContainer) {
                console.log('[模板布局修复] 未找到模板容器，创建新的模板容器');
                
                // 创建新的布局
                createNewLayout(chapterListContainer);
            } else {
                // 修改现有布局
                modifyExistingLayout(chapterListContainer, templateContainer);
            }
            
            // 标记为已修复
            isFixed = true;
            
            console.log('[模板布局修复] 模板布局修复完成');
        } catch (error) {
            console.error('[模板布局修复] 修复模板布局时出错:', error);
        }
    }
    
    // 创建新的布局
    function createNewLayout(chapterListContainer) {
        console.log('[模板布局修复] 创建新的布局...');
        
        // 创建行容器
        const rowContainer = document.createElement('div');
        rowContainer.className = 'row';
        
        // 将章节列表容器移动到行容器中的左侧列
        const leftColumn = document.createElement('div');
        leftColumn.className = 'col-md-4';
        
        // 克隆章节列表容器的父元素
        const parent = chapterListContainer.parentElement;
        
        // 将章节列表容器移动到左侧列
        leftColumn.appendChild(chapterListContainer);
        
        // 创建右侧列用于显示模板
        const rightColumn = document.createElement('div');
        rightColumn.className = 'col-md-8';
        rightColumn.id = 'template-detail-container';
        
        // 创建模板详情卡片
        const templateCard = document.createElement('div');
        templateCard.className = 'card';
        templateCard.id = 'template-detail-card';
        
        // 创建卡片头部
        const cardHeader = document.createElement('div');
        cardHeader.className = 'card-header bg-primary text-white';
        cardHeader.innerHTML = '<h5 class="mb-0">模板详情</h5>';
        
        // 创建卡片内容
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';
        cardBody.id = 'template-detail-content';
        cardBody.innerHTML = '<div class="alert alert-info">请从左侧选择要查看的维度或章节，右侧将显示对应的设定模板内容。</div>';
        
        // 组装卡片
        templateCard.appendChild(cardHeader);
        templateCard.appendChild(cardBody);
        
        // 将卡片添加到右侧列
        rightColumn.appendChild(templateCard);
        
        // 将左右列添加到行容器
        rowContainer.appendChild(leftColumn);
        rowContainer.appendChild(rightColumn);
        
        // 将行容器添加到原始父元素
        parent.appendChild(rowContainer);
        
        console.log('[模板布局修复] 新布局创建完成');
    }
    
    // 修改现有布局
    function modifyExistingLayout(chapterListContainer, templateContainer) {
        console.log('[模板布局修复] 修改现有布局...');
        
        // 检查是否已经有行容器
        const existingRow = chapterListContainer.closest('.row');
        if (existingRow) {
            console.log('[模板布局修复] 已存在行容器，调整列宽');
            
            // 调整列宽
            const leftColumn = chapterListContainer.closest('.col');
            if (leftColumn) {
                leftColumn.className = 'col-md-4';
            }
            
            // 查找或创建右侧列
            let rightColumn = existingRow.querySelector('.col-md-8');
            if (!rightColumn) {
                console.log('[模板布局修复] 创建右侧列');
                
                rightColumn = document.createElement('div');
                rightColumn.className = 'col-md-8';
                rightColumn.id = 'template-detail-container';
                
                // 创建模板详情卡片
                const templateCard = document.createElement('div');
                templateCard.className = 'card';
                templateCard.id = 'template-detail-card';
                
                // 创建卡片头部
                const cardHeader = document.createElement('div');
                cardHeader.className = 'card-header bg-primary text-white';
                cardHeader.innerHTML = '<h5 class="mb-0">模板详情</h5>';
                
                // 创建卡片内容
                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';
                cardBody.id = 'template-detail-content';
                cardBody.innerHTML = '<div class="alert alert-info">请从左侧选择要查看的维度或章节，右侧将显示对应的设定模板内容。</div>';
                
                // 组装卡片
                templateCard.appendChild(cardHeader);
                templateCard.appendChild(cardBody);
                
                // 将卡片添加到右侧列
                rightColumn.appendChild(templateCard);
                
                // 将右侧列添加到行容器
                existingRow.appendChild(rightColumn);
            }
            
            // 将模板容器移动到右侧列
            const templateDetailContent = rightColumn.querySelector('#template-detail-content');
            if (templateDetailContent && templateContainer) {
                // 清空现有内容
                templateDetailContent.innerHTML = '';
                
                // 移动模板容器
                templateDetailContent.appendChild(templateContainer);
            }
        } else {
            console.log('[模板布局修复] 不存在行容器，创建新布局');
            
            // 创建新布局
            createNewLayout(chapterListContainer);
            
            // 将模板容器移动到右侧
            const templateDetailContent = document.querySelector('#template-detail-content');
            if (templateDetailContent && templateContainer) {
                // 清空现有内容
                templateDetailContent.innerHTML = '';
                
                // 移动模板容器
                templateDetailContent.appendChild(templateContainer);
            }
        }
        
        console.log('[模板布局修复] 现有布局修改完成');
    }
    
    // 添加点击事件处理
    function addClickHandlers() {
        console.log('[模板布局修复] 添加点击事件处理...');
        
        // 查找所有章节和维度链接
        document.addEventListener('click', function(event) {
            // 检查是否点击了章节或维度链接
            const target = event.target.closest('.chapter-item, .dimension-item, [data-template-id]');
            if (!target) return;
            
            // 阻止默认行为
            event.preventDefault();
            
            // 获取模板ID
            const templateId = target.getAttribute('data-template-id');
            if (!templateId) {
                console.log('[模板布局修复] 未找到模板ID，无法加载模板');
                return;
            }
            
            // 加载模板
            loadTemplate(templateId);
        });
        
        console.log('[模板布局修复] 点击事件处理添加完成');
    }
    
    // 加载模板
    function loadTemplate(templateId) {
        console.log('[模板布局修复] 加载模板:', templateId);
        
        // 查找模板详情容器
        const templateDetailContent = document.querySelector('#template-detail-content');
        if (!templateDetailContent) {
            console.log('[模板布局修复] 未找到模板详情容器，无法加载模板');
            return;
        }
        
        // 显示加载中
        templateDetailContent.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div><p class="mt-2">加载模板中...</p></div>';
        
        // 发送请求获取模板内容
        fetch(`/api/template/${templateId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 显示模板内容
                if (data.success) {
                    templateDetailContent.innerHTML = `
                        <h4>${data.data.title || '未命名模板'}</h4>
                        <div class="template-content">
                            ${data.data.content || '无内容'}
                        </div>
                    `;
                } else {
                    templateDetailContent.innerHTML = `<div class="alert alert-danger">加载模板失败: ${data.message || '未知错误'}</div>`;
                }
            })
            .catch(error => {
                console.error('[模板布局修复] 加载模板时出错:', error);
                templateDetailContent.innerHTML = `<div class="alert alert-danger">加载模板失败: ${error.message}</div>`;
            });
    }
    
    // 观察DOM变化
    function observeDOMChanges() {
        console.log('[模板布局修复] 开始观察DOM变化...');
        
        // 创建观察器
        observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新的节点添加
                if (mutation.addedNodes.length > 0) {
                    // 检查是否需要修复模板布局
                    setTimeout(fixTemplateLayout, 500);
                }
            });
        });
        
        // 开始观察
        observer.observe(document.body, { childList: true, subtree: true });
        
        console.log('[模板布局修复] DOM变化观察器已启动');
    }
    
    // 初始化
    function initialize() {
        console.log('[模板布局修复] 初始化...');
        
        // 检查页面是否已加载
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('[模板布局修复] 页面已加载，开始修复');
            setTimeout(function() {
                fixTemplateLayout();
                addClickHandlers();
                observeDOMChanges();
            }, 1000);
        } else {
            console.log('[模板布局修复] 页面尚未加载，等待DOMContentLoaded事件');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('[模板布局修复] DOMContentLoaded事件触发，开始修复');
                setTimeout(function() {
                    fixTemplateLayout();
                    addClickHandlers();
                    observeDOMChanges();
                }, 1000);
            });
        }
    }
    
    // 导出全局函数
    window.fixTemplateLayout = fixTemplateLayout;
    
    // 执行初始化
    initialize();
    
    console.log('[模板布局修复] 初始化完成');
})();
