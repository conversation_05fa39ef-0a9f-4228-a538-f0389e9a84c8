<!-- fix-loader.html -->
<!-- 紧急修复加载器，用于修复静态文件加载问题 -->
<script>
    // 定义全局错误处理函数
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('全局错误捕获:', message);

        // 防止错误显示在页面上
        if (message && (
            message.includes('Failed to load resource') ||
            message.includes('404') ||
            message.includes('Not Found')
        )) {
            console.warn('资源加载失败，已被全局错误处理器捕获');
            return true; // 阻止错误显示
        }

        return false; // 允许其他错误正常显示
    };

    // 定义资源加载失败处理函数
    function handleResourceError(event) {
        const target = event.target || event.srcElement;
        const isScript = target.tagName === 'SCRIPT';
        const isLink = target.tagName === 'LINK';

        if (isScript || isLink) {
            console.warn('资源加载失败:', target.src || target.href);
            event.preventDefault();
            event.stopPropagation();
        }
    }

    // 添加资源加载失败事件监听
    window.addEventListener('error', handleResourceError, true);

    console.log('紧急修复加载器已初始化');
</script>
