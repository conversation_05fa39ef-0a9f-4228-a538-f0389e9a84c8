/**
 * 九猫 - 分析维度选择修复增强脚本
 * 这个脚本专门用于修复分析维度选择部分的问题，特别是空白维度问题
 * 版本: 1.0.0
 * 日期: 2025-05-05
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析维度选择修复增强脚本已加载');
    
    // 配置
    const CONFIG = {
        debug: true,                 // 是否启用调试模式
        fixEmptyLabels: true,        // 是否修复空白标签
        fixMissingOptions: true,     // 是否修复缺失选项
        fixTimeout: 300,             // 修复超时时间（毫秒）
        retryInterval: 200,          // 重试间隔（毫秒）
        maxRetries: 10,              // 最大重试次数
        forceRefresh: true           // 是否强制刷新维度列表
    };
    
    // 维度名称映射
    const DIMENSION_NAMES = {
        'language_style': '语言风格',
        'rhythm_pacing': '节奏与节奏',
        'structure': '结构分析',
        'sentence_variation': '句式变化',
        'paragraph_length': '段落长度',
        'perspective_shifts': '视角转换',
        'paragraph_flow': '段落流畅度',
        'novel_characteristics': '小说特点',
        'world_building': '世界构建',
        'chapter_outline': '章节大纲',
        'character_relationships': '人物关系',
        'opening_effectiveness': '开篇效果',
        'climax_pacing': '高潮节奏'
    };
    
    // 所有维度列表
    const ALL_DIMENSIONS = Object.keys(DIMENSION_NAMES);
    
    // 安全的日志函数
    function safeLog(message, level = 'log') {
        if (CONFIG.debug || level === 'error' || level === 'warn') {
            try {
                console[level](`[维度选择修复增强] ${message}`);
            } catch (e) {
                // 忽略日志错误
            }
        }
    }
    
    // 查找维度选择容器
    function findDimensionContainer() {
        try {
            // 首先尝试查找分析模态框
            const modal = document.getElementById('analyzeModal');
            if (!modal) {
                safeLog('找不到分析模态框', 'warn');
                return null;
            }
            
            // 查找表单
            const form = modal.querySelector('form');
            if (!form) {
                safeLog('找不到分析表单', 'warn');
                return null;
            }
            
            // 查找表单体
            const modalBody = form.querySelector('.modal-body');
            if (!modalBody) {
                safeLog('找不到模态框体', 'warn');
                return null;
            }
            
            safeLog('找到维度选择容器');
            return modalBody;
        } catch (e) {
            safeLog(`查找维度选择容器时出错: ${e.message}`, 'error');
            return null;
        }
    }
    
    // 查找所有分析维度选择框
    function findDimensionCheckboxes() {
        try {
            const checkboxes = document.querySelectorAll('.dimension-checkbox');
            safeLog(`找到 ${checkboxes.length} 个分析维度选择框`);
            return checkboxes;
        } catch (e) {
            safeLog(`查找分析维度选择框时出错: ${e.message}`, 'error');
            return [];
        }
    }
    
    // 检查标签是否为空
    function isLabelEmpty(label) {
        try {
            return !label || !label.textContent || label.textContent.trim() === '';
        } catch (e) {
            safeLog(`检查标签是否为空时出错: ${e.message}`, 'error');
            return true;
        }
    }
    
    // 修复空白标签
    function fixEmptyLabel(checkbox) {
        try {
            const dimension = checkbox.value;
            if (!dimension) {
                safeLog('选择框没有维度值，无法修复', 'warn');
                return false;
            }
            
            safeLog(`修复维度 ${dimension} 的空白标签`);
            
            // 获取维度名称
            const dimensionName = DIMENSION_NAMES[dimension] || dimension;
            
            // 查找标签
            const label = checkbox.nextElementSibling;
            if (!label || !label.classList.contains('form-check-label')) {
                safeLog(`找不到维度 ${dimension} 的标签`, 'warn');
                
                // 尝试通过ID查找标签
                const id = checkbox.id;
                if (id) {
                    const labelById = document.querySelector(`label[for="${id}"]`);
                    if (labelById) {
                        labelById.textContent = dimensionName;
                        safeLog(`通过ID修复了维度 ${dimension} 的标签`);
                        return true;
                    }
                }
                
                // 如果找不到标签，创建一个新标签
                const newLabel = document.createElement('label');
                newLabel.className = 'form-check-label';
                newLabel.setAttribute('for', checkbox.id || `dimension-${dimension}`);
                newLabel.textContent = dimensionName;
                
                // 将新标签插入到选择框后面
                checkbox.parentNode.insertBefore(newLabel, checkbox.nextSibling);
                safeLog(`为维度 ${dimension} 创建了新标签`);
                return true;
            }
            
            // 更新标签文本
            label.textContent = dimensionName;
            safeLog(`修复了维度 ${dimension} 的标签`);
            return true;
        } catch (e) {
            safeLog(`修复空白标签时出错: ${e.message}`, 'error');
            return false;
        }
    }
    
    // 修复所有空白标签
    function fixAllEmptyLabels() {
        try {
            const checkboxes = findDimensionCheckboxes();
            let fixedCount = 0;
            
            checkboxes.forEach(checkbox => {
                const label = checkbox.nextElementSibling;
                if (isLabelEmpty(label)) {
                    if (fixEmptyLabel(checkbox)) {
                        fixedCount++;
                    }
                }
            });
            
            safeLog(`修复了 ${fixedCount} 个空白标签`);
            return fixedCount;
        } catch (e) {
            safeLog(`修复所有空白标签时出错: ${e.message}`, 'error');
            return 0;
        }
    }
    
    // 检查是否缺少维度选项
    function checkMissingDimensions() {
        try {
            const checkboxes = findDimensionCheckboxes();
            const existingDimensions = Array.from(checkboxes).map(checkbox => checkbox.value);
            const missingDimensions = [];
            
            // 检查是否有缺失的维度
            ALL_DIMENSIONS.forEach(dimension => {
                if (!existingDimensions.includes(dimension)) {
                    missingDimensions.push(dimension);
                }
            });
            
            safeLog(`发现 ${missingDimensions.length} 个缺失的维度: ${missingDimensions.join(', ')}`);
            return missingDimensions;
        } catch (e) {
            safeLog(`检查缺失维度时出错: ${e.message}`, 'error');
            return [];
        }
    }
    
    // 重建所有维度选项
    function rebuildAllDimensions() {
        try {
            const container = findDimensionContainer();
            if (!container) {
                safeLog('找不到维度选择容器，无法重建维度选项', 'error');
                return 0;
            }
            
            // 查找全选复选框和分隔线
            const selectAllContainer = container.querySelector('.form-check');
            const hr = container.querySelector('hr');
            
            if (!selectAllContainer || !hr) {
                safeLog('找不到全选复选框或分隔线，无法重建维度选项', 'error');
                return 0;
            }
            
            // 查找所有现有的维度选项
            const existingOptions = container.querySelectorAll('.form-check:not(:first-child)');
            
            // 删除所有现有的维度选项
            existingOptions.forEach(option => {
                option.remove();
            });
            
            safeLog(`删除了 ${existingOptions.length} 个现有维度选项`);
            
            // 创建新的维度选项
            let addedCount = 0;
            ALL_DIMENSIONS.forEach((dimension, index) => {
                try {
                    // 创建新的选择项
                    const newCheckboxContainer = document.createElement('div');
                    newCheckboxContainer.className = 'form-check';
                    
                    const newCheckbox = document.createElement('input');
                    newCheckbox.className = 'form-check-input dimension-checkbox';
                    newCheckbox.type = 'checkbox';
                    newCheckbox.name = 'dimensions';
                    newCheckbox.value = dimension;
                    newCheckbox.id = `dimension-${index + 1}`;
                    
                    const newLabel = document.createElement('label');
                    newLabel.className = 'form-check-label';
                    newLabel.setAttribute('for', `dimension-${index + 1}`);
                    newLabel.textContent = DIMENSION_NAMES[dimension] || dimension;
                    
                    // 添加到容器中
                    newCheckboxContainer.appendChild(newCheckbox);
                    newCheckboxContainer.appendChild(newLabel);
                    
                    // 插入到分隔线后面
                    hr.parentNode.insertBefore(newCheckboxContainer, hr.nextSibling);
                    
                    safeLog(`添加了维度 ${dimension} 的选择项`);
                    addedCount++;
                } catch (e) {
                    safeLog(`添加维度 ${dimension} 的选择项时出错: ${e.message}`, 'error');
                }
            });
            
            safeLog(`重建了 ${addedCount} 个维度选择项`);
            return addedCount;
        } catch (e) {
            safeLog(`重建所有维度选项时出错: ${e.message}`, 'error');
            return 0;
        }
    }
    
    // 在页面加载完成后执行修复
    function initFix() {
        safeLog('开始修复分析维度选择问题');
        
        // 延迟执行，确保页面元素已加载
        setTimeout(() => {
            if (CONFIG.forceRefresh) {
                // 强制重建所有维度选项
                rebuildAllDimensions();
            } else {
                // 修复空白标签
                if (CONFIG.fixEmptyLabels) {
                    fixAllEmptyLabels();
                }
                
                // 检查是否缺少维度选项
                if (CONFIG.fixMissingOptions) {
                    const missingDimensions = checkMissingDimensions();
                    if (missingDimensions.length > 0) {
                        // 如果缺少维度选项，重建所有维度选项
                        rebuildAllDimensions();
                    }
                }
            }
        }, CONFIG.fixTimeout);
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initFix);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initFix();
    }
    
    // 导出函数供其他模块使用
    window.dimensionSelectionFixEnhanced = {
        fixAllEmptyLabels: fixAllEmptyLabels,
        rebuildAllDimensions: rebuildAllDimensions
    };
})();
