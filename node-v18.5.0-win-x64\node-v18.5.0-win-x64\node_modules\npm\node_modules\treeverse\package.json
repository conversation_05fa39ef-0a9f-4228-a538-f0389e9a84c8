{"name": "treeverse", "version": "2.0.0", "description": "Walk any kind of tree structure depth- or breadth-first. Supports promises and advanced map-reduce operations with a very small API.", "author": "GitHub Inc.", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/npm/treeverse.git"}, "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "prepublishOnly": "git push origin --follow-tags", "posttest": "npm run lint"}, "tap": {"100": true, "coverage-map": "test/coverage-map.js"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "main": "lib/index.js", "keywords": ["tree", "traversal", "depth first search", "breadth first search"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}}