/**
 * 九猫系统 - 图表阻止器
 * 提供额外的方法来阻止Chart.js加载，作为disable-charts.js的备份方案
 * 版本: 1.0.0
 */

(function() {
    'use strict';
    
    console.log('图表阻止器已加载');
    
    // 使用MutationObserver监视DOM变化，拦截script标签
    function setupScriptObserver() {
        // 检查是否支持MutationObserver
        if (!window.MutationObserver) {
            console.log('浏览器不支持MutationObserver，无法使用此方法监视脚本加载');
            return;
        }
        
        // 创建一个观察器实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新增的节点
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        
                        // 检查是否是script元素
                        if (node.nodeName === 'SCRIPT') {
                            const src = node.src || '';
                            
                            // 检查是否是Chart.js相关脚本
                            if (src && (
                                src.includes('chart.js') || 
                                src.includes('chart.min.js') || 
                                src.includes('chart.bundle.js') ||
                                src.includes('chart.bundle.min.js') ||
                                src.includes('cdn.jsdelivr.net/npm/chart')
                            )) {
                                console.log(`MutationObserver阻止加载Chart.js: ${src}`);
                                
                                // 阻止脚本加载
                                node.type = 'javascript/blocked';
                                
                                // 如果可能，移除节点
                                try {
                                    if (node.parentNode) {
                                        node.parentNode.removeChild(node);
                                    }
                                } catch (e) {
                                    console.warn('无法移除Chart.js脚本节点:', e.message);
                                }
                            }
                        }
                    }
                }
            });
        });
        
        // 配置观察选项
        const config = { 
            childList: true,     // 观察目标子节点的变化
            subtree: true,       // 观察所有后代节点
            attributes: true,    // 观察属性变化
            attributeFilter: ['src']  // 只观察src属性
        };
        
        // 开始观察document
        observer.observe(document, config);
        
        console.log('已设置脚本加载监视器');
        
        return observer;
    }
    
    // 拦截XMLHttpRequest，阻止加载Chart.js
    function interceptXHR() {
        // 保存原始的XMLHttpRequest
        const originalXHR = window.XMLHttpRequest;
        
        // 创建新的XMLHttpRequest构造函数
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            
            // 重写open方法
            xhr.open = function(method, url, ...args) {
                // 检查URL是否是Chart.js
                if (url && typeof url === 'string' && (
                    url.includes('chart.js') || 
                    url.includes('chart.min.js') || 
                    url.includes('chart.bundle.js') ||
                    url.includes('chart.bundle.min.js') ||
                    url.includes('cdn.jsdelivr.net/npm/chart')
                )) {
                    console.log(`拦截XHR请求Chart.js: ${url}`);
                    
                    // 将URL修改为空白资源
                    url = 'data:,';
                }
                
                // 调用原始方法
                return originalOpen.call(this, method, url, ...args);
            };
            
            return xhr;
        };
        
        console.log('已拦截XMLHttpRequest');
    }
    
    // 拦截fetch请求
    function interceptFetch() {
        // 保存原始的fetch
        const originalFetch = window.fetch;
        
        // 重写fetch
        window.fetch = function(resource, init) {
            // 检查资源是否是Chart.js
            if (resource && typeof resource === 'string' && (
                resource.includes('chart.js') || 
                resource.includes('chart.min.js') || 
                resource.includes('chart.bundle.js') ||
                resource.includes('chart.bundle.min.js') ||
                resource.includes('cdn.jsdelivr.net/npm/chart')
            )) {
                console.log(`拦截fetch请求Chart.js: ${resource}`);
                
                // 返回一个空响应
                return Promise.resolve(new Response('', {
                    status: 200,
                    headers: { 'Content-Type': 'application/javascript' }
                }));
            }
            
            // 调用原始方法
            return originalFetch.call(window, resource, init);
        };
        
        console.log('已拦截fetch请求');
    }
    
    // 创建一个空的Chart对象，防止报错
    function createEmptyChart() {
        if (!window.Chart) {
            window.Chart = function(ctx, config) {
                console.log('尝试创建图表，但图表功能已被禁用');
                this.ctx = ctx;
                this.config = config || {};
                
                // 实现必要的方法，防止调用时报错
                this.update = function() { console.log('Chart.update() 被调用，但图表已禁用'); };
                this.destroy = function() { console.log('Chart.destroy() 被调用，但图表已禁用'); };
                this.render = function() { console.log('Chart.render() 被调用，但图表已禁用'); };
                
                return this;
            };
            
            // 添加必要的静态方法
            Chart.register = function() { console.log('Chart.register() 被调用，但图表已禁用'); };
            Chart.defaults = {};
            
            console.log('已创建空的Chart对象');
        }
    }
    
    // 初始化所有拦截器
    function init() {
        // 设置MutationObserver监视脚本加载
        setupScriptObserver();
        
        // 拦截XMLHttpRequest
        interceptXHR();
        
        // 拦截fetch请求
        interceptFetch();
        
        // 创建空的Chart对象
        createEmptyChart();
        
        console.log('图表阻止器初始化完成');
    }
    
    // 在DOM加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // 导出到全局命名空间
    window.ChartBlocker = {
        init: init,
        setupScriptObserver: setupScriptObserver,
        interceptXHR: interceptXHR,
        interceptFetch: interceptFetch,
        createEmptyChart: createEmptyChart
    };
})();
