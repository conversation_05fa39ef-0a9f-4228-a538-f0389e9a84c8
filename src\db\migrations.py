"""
数据库迁移脚本
用于更新数据库结构，添加新列等操作
"""
import logging
import sqlite3
from typing import List, Dict, Any, Optional
import os
from src.db.connection import engine

logger = logging.getLogger(__name__)

def run_migrations():
    """
    运行所有数据库迁移
    """
    logger.info("开始运行数据库迁移...")

    # 获取数据库连接
    conn = None
    try:
        # 使用SQLAlchemy引擎获取连接
        conn = engine.connect().connection

        # 检查并创建migrations表，用于记录已执行的迁移
        create_migrations_table(conn)

        # 获取已执行的迁移
        executed_migrations = get_executed_migrations(conn)
        logger.info(f"已执行的迁移: {executed_migrations}")

        # 定义所有迁移
        migrations = [
            {
                "id": "001_add_reasoning_content_to_chapter_analysis_results",
                "description": "为chapter_analysis_results表添加reasoning_content列",
                "function": add_reasoning_content_to_chapter_analysis_results
            },
            {
                "id": "002_add_reasoning_content_to_analysis_results",
                "description": "为analysis_results表添加reasoning_content列",
                "function": add_reasoning_content_to_analysis_results
            },
            {
                "id": "003_add_reference_templates_table",
                "description": "添加reference_templates表",
                "function": add_reference_templates_table
            },
            {
                "id": "004_add_generated_contents_table",
                "description": "添加generated_contents表",
                "function": add_generated_contents_table
            },
            {
                "id": "005_add_is_template_to_novels",
                "description": "为novels表添加is_template列",
                "function": add_is_template_to_novels
            }
        ]

        # 执行未执行的迁移
        for migration in migrations:
            if migration["id"] not in executed_migrations:
                logger.info(f"执行迁移: {migration['id']} - {migration['description']}")
                try:
                    migration["function"](conn)
                    record_migration(conn, migration["id"], migration["description"])
                    logger.info(f"迁移 {migration['id']} 执行成功")
                except Exception as e:
                    logger.error(f"迁移 {migration['id']} 执行失败: {str(e)}")
                    raise
            else:
                logger.info(f"迁移 {migration['id']} 已执行，跳过")

        logger.info("所有数据库迁移执行完成")
    except Exception as e:
        logger.error(f"执行数据库迁移时出错: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def create_migrations_table(conn):
    """
    创建migrations表，用于记录已执行的迁移
    """
    cursor = conn.cursor()
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS migrations (
        id TEXT PRIMARY KEY,
        description TEXT,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    conn.commit()

def get_executed_migrations(conn) -> List[str]:
    """
    获取已执行的迁移ID列表
    """
    cursor = conn.cursor()
    cursor.execute("SELECT id FROM migrations")
    return [row[0] for row in cursor.fetchall()]

def record_migration(conn, migration_id: str, description: str):
    """
    记录已执行的迁移
    """
    cursor = conn.cursor()
    cursor.execute(
        "INSERT INTO migrations (id, description) VALUES (?, ?)",
        (migration_id, description)
    )
    conn.commit()

def add_reasoning_content_to_chapter_analysis_results(conn):
    """
    为chapter_analysis_results表添加reasoning_content列
    """
    cursor = conn.cursor()

    # 检查列是否已存在
    cursor.execute("PRAGMA table_info(chapter_analysis_results)")
    columns = [column[1] for column in cursor.fetchall()]

    if "reasoning_content" not in columns:
        cursor.execute("ALTER TABLE chapter_analysis_results ADD COLUMN reasoning_content TEXT")
        conn.commit()
        logger.info("已为chapter_analysis_results表添加reasoning_content列")
    else:
        logger.info("chapter_analysis_results表已有reasoning_content列，无需添加")

def add_reasoning_content_to_analysis_results(conn):
    """
    为analysis_results表添加reasoning_content列
    """
    cursor = conn.cursor()

    # 检查列是否已存在
    cursor.execute("PRAGMA table_info(analysis_results)")
    columns = [column[1] for column in cursor.fetchall()]

    if "reasoning_content" not in columns:
        cursor.execute("ALTER TABLE analysis_results ADD COLUMN reasoning_content TEXT")
        conn.commit()
        logger.info("已为analysis_results表添加reasoning_content列")
    else:
        logger.info("analysis_results表已有reasoning_content列，无需添加")

def add_reference_templates_table(conn):
    """
    添加reference_templates表
    """
    cursor = conn.cursor()

    # 检查表是否已存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='reference_templates'")
    if cursor.fetchone() is None:
        # 创建表
        cursor.execute("""
        CREATE TABLE reference_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            novel_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (novel_id) REFERENCES novels (id)
        )
        """)
        conn.commit()
        logger.info("已创建reference_templates表")
    else:
        logger.info("reference_templates表已存在，无需创建")

def add_generated_contents_table(conn):
    """
    添加generated_contents表
    """
    cursor = conn.cursor()

    # 检查表是否已存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='generated_contents'")
    if cursor.fetchone() is None:
        # 创建表
        cursor.execute("""
        CREATE TABLE generated_contents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            prompt TEXT,
            word_count INTEGER DEFAULT 0,
            reference_template_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            content_metadata TEXT,
            FOREIGN KEY (reference_template_id) REFERENCES reference_templates (id)
        )
        """)
        conn.commit()
        logger.info("已创建generated_contents表")
    else:
        logger.info("generated_contents表已存在，无需创建")

def add_is_template_to_novels(conn):
    """
    为novels表添加is_template列
    """
    cursor = conn.cursor()

    # 检查列是否已存在
    cursor.execute("PRAGMA table_info(novels)")
    columns = [column[1] for column in cursor.fetchall()]

    if "is_template" not in columns:
        cursor.execute("ALTER TABLE novels ADD COLUMN is_template BOOLEAN DEFAULT 0")
        conn.commit()
        logger.info("已为novels表添加is_template列")
    else:
        logger.info("novels表已有is_template列，无需添加")

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 运行迁移
    run_migrations()
