/**
 * 九猫 - 章节推理过程修复脚本
 * 用于修复章节推理过程显示问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('章节推理过程修复脚本已加载');

    // 配置
    const CONFIG = {
        // 调试模式
        debug: true,
        // 自动重试次数
        maxRetries: 3,
        // 重试间隔（毫秒）
        retryInterval: 5000,
        // 是否显示详细日志
        verbose: true
    };

    // 日志函数
    function log(message, type = 'info') {
        if (!CONFIG.debug && type !== 'error') return;
        
        const prefix = '[章节推理修复]';
        switch (type) {
            case 'error':
                console.error(`${prefix} ${message}`);
                break;
            case 'warn':
                console.warn(`${prefix} ${message}`);
                break;
            case 'debug':
                if (CONFIG.verbose) {
                    console.debug(`${prefix} ${message}`);
                }
                break;
            default:
                console.log(`${prefix} ${message}`);
        }
    }

    // 加载章节推理过程
    async function loadChapterReasoning(novelId, chapterId, dimension, containerId = 'reasoningContent') {
        log(`开始加载章节推理过程: novel_id=${novelId}, chapter_id=${chapterId}, dimension=${dimension}`);

        // 获取容器元素
        const container = document.getElementById(containerId);
        if (!container) {
            log(`找不到推理过程容器: ${containerId}`, 'error');
            return;
        }

        // 显示加载中状态
        container.innerHTML = `
            <div class="text-center my-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2">正在加载推理过程，请稍候...</p>
            </div>
        `;

        // 构建API URL
        const apiUrl = `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`;
        log(`请求API: ${apiUrl}`);

        try {
            // 发送请求
            const response = await fetch(apiUrl);
            const data = await response.json();

            if (data.success && data.reasoning_content) {
                // 显示推理过程
                log(`成功获取推理过程，长度: ${data.reasoning_content.length}`);
                renderReasoningContent(container, data.reasoning_content);
            } else {
                // 尝试备用API
                log(`主API未返回推理过程，尝试备用API`, 'warn');
                await tryFallbackApi(novelId, chapterId, dimension, container);
            }
        } catch (error) {
            log(`请求推理过程时出错: ${error.message}`, 'error');
            // 尝试备用API
            await tryFallbackApi(novelId, chapterId, dimension, container);
        }
    }

    // 尝试备用API
    async function tryFallbackApi(novelId, chapterId, dimension, container) {
        // 构建备用API URL
        const fallbackUrl = `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/generate_reasoning`;
        log(`请求备用API: ${fallbackUrl}`);

        try {
            // 发送请求
            const response = await fetch(fallbackUrl);
            const data = await response.json();

            if (data.success && data.reasoning_content) {
                // 显示推理过程
                log(`成功从备用API获取推理过程，长度: ${data.reasoning_content.length}`);
                renderReasoningContent(container, data.reasoning_content);
            } else {
                // 显示错误信息
                log(`备用API也未返回推理过程: ${data.error || '未知错误'}`, 'error');
                showError(container, data.error || '未找到推理过程数据');
            }
        } catch (error) {
            log(`请求备用API时出错: ${error.message}`, 'error');
            showError(container, `加载推理过程时出错: ${error.message}`);
        }
    }

    // 渲染推理过程内容
    function renderReasoningContent(container, content) {
        // 格式化内容
        let formattedContent = content;

        // 如果内容不包含Markdown标记，添加基本格式
        if (!content.includes('#') && !content.includes('**')) {
            formattedContent = `## 推理过程\n\n${content}`;
        }

        // 渲染为HTML
        container.innerHTML = `<pre class="reasoning-text">${formattedContent}</pre>`;

        // 应用代码高亮
        if (typeof hljs !== 'undefined') {
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });
        }
    }

    // 显示错误信息
    function showError(container, message) {
        container.innerHTML = `
            <div class="alert alert-warning">
                <p><i class="fas fa-exclamation-circle"></i> ${message}</p>
                <p class="mt-2">
                    <button class="btn btn-sm btn-outline-primary retry-button">
                        <i class="fas fa-sync-alt"></i> 重试
                    </button>
                </p>
            </div>
        `;

        // 添加重试按钮事件
        const retryButton = container.querySelector('.retry-button');
        if (retryButton) {
            retryButton.addEventListener('click', function() {
                // 获取数据属性
                const novelId = container.getAttribute('data-novel-id');
                const chapterId = container.getAttribute('data-chapter-id');
                const dimension = container.getAttribute('data-dimension');

                // 重新加载
                loadChapterReasoning(novelId, chapterId, dimension, container.id);
            });
        }
    }

    // 初始化
    function init() {
        log('初始化章节推理过程修复脚本');

        // 查找所有推理过程容器
        document.querySelectorAll('[data-reasoning-container="true"]').forEach(container => {
            const novelId = container.getAttribute('data-novel-id');
            const chapterId = container.getAttribute('data-chapter-id');
            const dimension = container.getAttribute('data-dimension');

            if (novelId && dimension) {
                log(`找到推理过程容器: novel_id=${novelId}, chapter_id=${chapterId}, dimension=${dimension}`);

                // 如果容器内容为空或包含"未找到推理过程数据"，加载推理过程
                if (!container.textContent.trim() || 
                    container.textContent.includes('未找到推理过程数据') ||
                    container.textContent.includes('加载中')) {
                    loadChapterReasoning(novelId, chapterId, dimension, container.id);
                }
            }
        });

        // 查找并添加推理过程按钮事件
        document.querySelectorAll('.load-reasoning-btn').forEach(button => {
            button.addEventListener('click', function() {
                const container = document.getElementById(this.getAttribute('data-target'));
                if (container) {
                    const novelId = container.getAttribute('data-novel-id');
                    const chapterId = container.getAttribute('data-chapter-id');
                    const dimension = container.getAttribute('data-dimension');

                    loadChapterReasoning(novelId, chapterId, dimension, container.id);
                }
            });
        });
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
