/**
 * 九猫 - 章节导航修复脚本
 * 解决章节分析页面的导航和跳转问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._chapterNavigationFixLoaded) {
        console.log('[章节导航修复] 章节导航修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._chapterNavigationFixLoaded = true;
    
    console.log('[章节导航修复] 章节导航修复脚本已加载 - 版本1.0.0');
    
    // 安全日志函数
    function safeLog(message) {
        try {
            console.log('[章节导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    function safeError(message) {
        try {
            console.error('[章节导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 初始化函数
    function initialize() {
        safeLog('初始化章节导航修复');
        
        // 获取当前页面URL
        const currentUrl = window.location.href;
        safeLog('当前页面URL: ' + currentUrl);
        
        // 检查是否是章节相关页面
        if (currentUrl.includes('/chapter/') || currentUrl.includes('/chapters')) {
            safeLog('检测到章节相关页面');
            
            // 修复章节列表页面
            if (currentUrl.includes('/chapters') && !currentUrl.includes('/chapters/')) {
                fixChapterListPage();
            }
            
            // 修复章节详情页面
            else if (currentUrl.includes('/chapter/')) {
                fixChapterDetailPage();
            }
            
            // 修复章节分析汇总页面
            else if (currentUrl.includes('/chapters/summary')) {
                fixChapterSummaryPage();
            }
            
            // 修复章节分析详情页面
            else if (currentUrl.includes('/chapter/') && currentUrl.includes('/analysis/')) {
                fixChapterAnalysisPage();
            }
        }
        
        // 修复所有章节相关链接
        fixAllChapterLinks();
        
        // 修复所有章节相关按钮
        fixAllChapterButtons();
        
        // 修复所有章节相关表单
        fixAllChapterForms();
        
        // 监听DOM变化，修复新添加的元素
        observeDOMChanges();
        
        safeLog('章节导航修复完成');
    }
    
    // 修复章节列表页面
    function fixChapterListPage() {
        safeLog('修复章节列表页面');
        
        // 获取所有章节链接
        const chapterLinks = document.querySelectorAll('a[href*="/chapter/"]');
        safeLog('找到 ' + chapterLinks.length + ' 个章节链接');
        
        chapterLinks.forEach(link => {
            // 获取链接的href属性
            const href = link.getAttribute('href');
            
            // 添加点击事件，确保正确跳转
            link.addEventListener('click', function(e) {
                safeLog('点击章节链接: ' + href);
            });
        });
        
        // 修复分析按钮
        const analyzeButton = document.querySelector('button[data-bs-target="#analyzeModal"]');
        if (analyzeButton) {
            safeLog('找到分析按钮');
            
            // 添加点击事件，确保正确处理
            analyzeButton.addEventListener('click', function(e) {
                safeLog('点击分析按钮');
            });
        }
    }
    
    // 修复章节详情页面
    function fixChapterDetailPage() {
        safeLog('修复章节详情页面');
        
        // 获取所有维度链接
        const dimensionLinks = document.querySelectorAll('a[href*="/analysis/"]');
        safeLog('找到 ' + dimensionLinks.length + ' 个维度链接');
        
        dimensionLinks.forEach(link => {
            // 获取链接的href属性
            const href = link.getAttribute('href');
            
            // 添加点击事件，确保正确跳转
            link.addEventListener('click', function(e) {
                safeLog('点击维度链接: ' + href);
            });
        });
        
        // 修复分析按钮
        const analyzeButton = document.querySelector('button[data-bs-target="#analyzeModal"]');
        if (analyzeButton) {
            safeLog('找到分析按钮');
            
            // 添加点击事件，确保正确处理
            analyzeButton.addEventListener('click', function(e) {
                safeLog('点击分析按钮');
            });
        }
    }
    
    // 修复章节分析汇总页面
    function fixChapterSummaryPage() {
        safeLog('修复章节分析汇总页面');
        
        // 获取所有章节链接
        const chapterLinks = document.querySelectorAll('a[href*="/chapter/"]');
        safeLog('找到 ' + chapterLinks.length + ' 个章节链接');
        
        chapterLinks.forEach(link => {
            // 获取链接的href属性
            const href = link.getAttribute('href');
            
            // 添加点击事件，确保正确跳转
            link.addEventListener('click', function(e) {
                safeLog('点击章节链接: ' + href);
            });
        });
        
        // 修复维度过滤器
        const dimensionFilter = document.getElementById('dimensionFilter');
        if (dimensionFilter) {
            safeLog('找到维度过滤器');
            
            // 添加变化事件，确保正确处理
            dimensionFilter.addEventListener('change', function(e) {
                safeLog('维度过滤器变化: ' + dimensionFilter.value);
            });
        }
    }
    
    // 修复章节分析详情页面
    function fixChapterAnalysisPage() {
        safeLog('修复章节分析详情页面');
        
        // 获取所有返回链接
        const backLinks = document.querySelectorAll('a.back-btn, a[href*="/chapter/"]');
        safeLog('找到 ' + backLinks.length + ' 个返回链接');
        
        backLinks.forEach(link => {
            // 获取链接的href属性
            const href = link.getAttribute('href');
            
            // 添加点击事件，确保正确跳转
            link.addEventListener('click', function(e) {
                safeLog('点击返回链接: ' + href);
            });
        });
    }
    
    // 修复所有章节相关链接
    function fixAllChapterLinks() {
        safeLog('修复所有章节相关链接');
        
        // 获取所有章节相关链接
        const chapterLinks = document.querySelectorAll('a[href*="/chapter/"], a[href*="/chapters"]');
        safeLog('找到 ' + chapterLinks.length + ' 个章节相关链接');
        
        chapterLinks.forEach(link => {
            // 获取链接的href属性
            const href = link.getAttribute('href');
            
            // 检查是否是章节分析链接
            if (href.includes('/chapter/') && href.includes('/analysis/')) {
                safeLog('找到章节分析链接: ' + href);
                
                // 添加点击事件，确保正确跳转
                link.addEventListener('click', function(e) {
                    safeLog('点击章节分析链接: ' + href);
                });
            }
            
            // 检查是否是章节详情链接
            else if (href.includes('/chapter/') && !href.includes('/analysis/')) {
                safeLog('找到章节详情链接: ' + href);
                
                // 添加点击事件，确保正确跳转
                link.addEventListener('click', function(e) {
                    safeLog('点击章节详情链接: ' + href);
                });
            }
            
            // 检查是否是章节列表链接
            else if (href.includes('/chapters') && !href.includes('/chapters/')) {
                safeLog('找到章节列表链接: ' + href);
                
                // 添加点击事件，确保正确跳转
                link.addEventListener('click', function(e) {
                    safeLog('点击章节列表链接: ' + href);
                });
            }
            
            // 检查是否是章节分析汇总链接
            else if (href.includes('/chapters/summary')) {
                safeLog('找到章节分析汇总链接: ' + href);
                
                // 添加点击事件，确保正确跳转
                link.addEventListener('click', function(e) {
                    safeLog('点击章节分析汇总链接: ' + href);
                });
            }
        });
    }
    
    // 修复所有章节相关按钮
    function fixAllChapterButtons() {
        safeLog('修复所有章节相关按钮');
        
        // 获取所有章节相关按钮
        const chapterButtons = document.querySelectorAll('button.chapter-analysis-btn, button.analyze-btn, button.view-btn');
        safeLog('找到 ' + chapterButtons.length + ' 个章节相关按钮');
        
        chapterButtons.forEach(button => {
            // 添加点击事件，确保正确处理
            button.addEventListener('click', function(e) {
                safeLog('点击章节相关按钮: ' + button.textContent);
            });
        });
    }
    
    // 修复所有章节相关表单
    function fixAllChapterForms() {
        safeLog('修复所有章节相关表单');
        
        // 获取所有章节相关表单
        const chapterForms = document.querySelectorAll('form[action*="/chapter/"], form[action*="/chapters"]');
        safeLog('找到 ' + chapterForms.length + ' 个章节相关表单');
        
        chapterForms.forEach(form => {
            // 获取表单的action属性
            const action = form.getAttribute('action');
            
            // 添加提交事件，确保正确提交
            form.addEventListener('submit', function(e) {
                safeLog('提交章节相关表单: ' + action);
            });
        });
    }
    
    // 监听DOM变化，修复新添加的元素
    function observeDOMChanges() {
        try {
            // 创建MutationObserver
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // 检查是否有新添加的节点
                    if (mutation.addedNodes.length > 0) {
                        mutation.addedNodes.forEach(function(node) {
                            // 检查是否是元素节点
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                // 修复新添加的章节相关链接
                                const chapterLinks = node.querySelectorAll('a[href*="/chapter/"], a[href*="/chapters"]');
                                chapterLinks.forEach(link => {
                                    // 获取链接的href属性
                                    const href = link.getAttribute('href');
                                    
                                    // 添加点击事件，确保正确跳转
                                    link.addEventListener('click', function(e) {
                                        safeLog('点击新添加的章节相关链接: ' + href);
                                    });
                                });
                                
                                // 修复新添加的章节相关按钮
                                const chapterButtons = node.querySelectorAll('button.chapter-analysis-btn, button.analyze-btn, button.view-btn');
                                chapterButtons.forEach(button => {
                                    // 添加点击事件，确保正确处理
                                    button.addEventListener('click', function(e) {
                                        safeLog('点击新添加的章节相关按钮: ' + button.textContent);
                                    });
                                });
                                
                                // 修复新添加的章节相关表单
                                const chapterForms = node.querySelectorAll('form[action*="/chapter/"], form[action*="/chapters"]');
                                chapterForms.forEach(form => {
                                    // 获取表单的action属性
                                    const action = form.getAttribute('action');
                                    
                                    // 添加提交事件，确保正确提交
                                    form.addEventListener('submit', function(e) {
                                        safeLog('提交新添加的章节相关表单: ' + action);
                                    });
                                });
                            }
                        });
                    }
                });
            });
            
            // 配置观察选项
            const config = { childList: true, subtree: true };
            
            // 开始观察
            observer.observe(document.body, config);
            
            safeLog('已开始监听DOM变化');
        } catch (e) {
            safeError('监听DOM变化时出错: ' + e.message);
        }
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }
    
    // 导出API，方便调试和手动调用
    window.chapterNavigationFix = {
        initialize: initialize,
        fixChapterListPage: fixChapterListPage,
        fixChapterDetailPage: fixChapterDetailPage,
        fixChapterSummaryPage: fixChapterSummaryPage,
        fixChapterAnalysisPage: fixChapterAnalysisPage,
        fixAllChapterLinks: fixAllChapterLinks,
        fixAllChapterButtons: fixAllChapterButtons,
        fixAllChapterForms: fixAllChapterForms
    };
})();
