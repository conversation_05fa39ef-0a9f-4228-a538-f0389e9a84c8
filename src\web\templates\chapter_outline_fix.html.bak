{% extends "base.html" %}

{% block title %}{{ novel.title }} - 章节大纲分析 - 九猫{% endblock %}

{% block head %}
{{ super() }}
<!-- 加载修复脚本 -->
<script src="{{ url_for('static', filename='js/json-parse-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/json-parse-fix-enhanced.js';"></script>
<script src="{{ url_for('static', filename='js/node-replace-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/node-replace-fix.js';"></script>
<script src="{{ url_for('static', filename='js/chapter-outline-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-outline-fix.js';"></script>
{% endblock %}

{% block extra_css %}
<style>
    .analysis-content {
        white-space: pre-wrap;
        line-height: 1.6;
    }

    .metadata-item {
        margin-bottom: 0.5rem;
    }

    .visualization-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }

    .analysis-logs {
        font-family: monospace;
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .log-entry {
        padding: 2px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .log-entry:last-child {
        border-bottom: none;
    }

    #analysisLogsCollapse .card-body,
    #analysisProcessCollapse .card-body {
        padding: 0.5rem;
        background-color: #f8f9fa;
    }

    .analysis-process-card {
        margin-bottom: 1rem;
        border: 1px solid rgba(0,0,0,.125);
    }

    .analysis-process-card .card-header {
        background-color: rgba(0,0,0,.03);
        padding: 0.5rem 1rem;
    }

    .analysis-process-card .card-body {
        padding: 0.75rem 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1>章节大纲分析</h1>
        <a href="{{ url_for('novel.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
            返回小说页面
        </a>
    </div>

    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">分析结果</h5>
                </div>
                <div class="card-body">
                    <div id="analysis-content" class="analysis-content">
                        {% if analysis_result and analysis_result.content %}
                            {{ analysis_result.content|safe }}
                        {% else %}
                            <p class="text-muted">暂无分析结果</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">分析元数据</h5>
                </div>
                <div class="card-body">
                    <div id="analysis-metadata" style="display:none;" data-metadata='{{ analysis_result.metadata|tojson_safe if analysis_result and analysis_result.metadata else "{}" }}'></div>
                    <div class="metadata-container">
                        {% if analysis_result and analysis_result.metadata %}
                            <div class="metadata-item"><strong>处理时间：</strong> {{ analysis_result.metadata.processing_time|default(0)|round(2) }} 秒</div>
                            <div class="metadata-item"><strong>分块数量：</strong> {{ analysis_result.metadata.chunk_count|default(0) }}</div>
                            <div class="metadata-item"><strong>API调用次数：</strong> {{ analysis_result.metadata.api_calls|default(0) }}</div>
                            <div class="metadata-item"><strong>令牌使用量：</strong> {{ analysis_result.metadata.tokens_used|default(0) }}</div>
                            <div class="metadata-item"><strong>费用：</strong> {{ analysis_result.metadata.cost|default(0)|round(4) }} 元</div>
                        {% else %}
                            <div class="metadata-item"><strong>处理时间：</strong> 0.00 秒</div>
                            <div class="metadata-item"><strong>分块数量：</strong> 0</div>
                            <div class="metadata-item"><strong>API调用次数：</strong> 0</div>
                            <div class="metadata-item"><strong>令牌使用量：</strong> 0</div>
                            <div class="metadata-item"><strong>费用：</strong> 0.0000 元</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">可视化</h5>
                </div>
                <div class="card-body">
                    <div class="visualization-container">
                        <div class="chart-container">
                            <canvas id="chapter-outline-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">操作</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary w-100 mb-2 reanalyze-btn" data-dimension="chapter_outline" data-novel-id="{{ novel.id }}">
                        <i class="fas fa-sync"></i> 重新分析
                    </button>
                    <a href="{{ url_for('novel.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary w-100">
                        返回小说页面
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if analysis_result and analysis_result.logs %}
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header" id="analysisLogsHeader">
                    <h5 class="mb-0">
                        <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#analysisLogsCollapse" aria-expanded="false" aria-controls="analysisLogsCollapse">
                            分析日志
                        </button>
                    </h5>
                </div>
                <div id="analysisLogsCollapse" class="collapse" aria-labelledby="analysisLogsHeader">
                    <div class="card-body">
                        <div class="analysis-logs">
                            {% for log in analysis_result.logs %}
                                <div class="log-entry">
                                    [{{ log.timestamp }}] {{ log.level|upper }} - {{ log.message }}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if analysis_result and analysis_result.process %}
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header" id="analysisProcessHeader">
                    <h5 class="mb-0">
                        <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#analysisProcessCollapse" aria-expanded="false" aria-controls="analysisProcessCollapse">
                            分析过程
                        </button>
                    </h5>
                </div>
                <div id="analysisProcessCollapse" class="collapse" aria-labelledby="analysisProcessHeader">
                    <div class="card-body">
                        {% for step in analysis_result.process %}
                            <div class="analysis-process-card">
                                <div class="card-header">
                                    <strong>步骤 {{ loop.index }}:</strong> {{ step.title }}
                                </div>
                                <div class="card-body">
                                    <pre class="mb-0">{{ step.content }}</pre>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<!-- 分析结果页面修复脚本 -->
<script src="{{ url_for('static', filename='js/analysis-page-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-page-fix.js';"></script>
<!-- 图表修复脚本 -->
<script src="{{ url_for('static', filename='js/chart-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chart-fix.js';"></script>
<!-- 章节大纲图表专用修复脚本 -->
<script src="{{ url_for('static', filename='js/chapter-outline-chart-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-outline-chart-fix.js';"></script>
<!-- 统一图表修复脚本 -->
<script src="{{ url_for('static', filename='js/consolidated-chart-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/consolidated-chart-fix.js';"></script>

<script>
    // 全局变量
    const novelId = "{{ novel.id }}";
    const dimension = "chapter_outline";

    // 错误处理函数，防止JS错误导致整个页面失效
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
        return true; // 阻止默认错误处理
    };

    // 实时进度更新函数
    function fetchAnalysisProgress() {
        return fetch(`/api/analysis/progress?novel_id=${novelId}`)
            .then(res => res.json())
            .then(data => {
                if (data.success && data.progress && data.progress[dimension]) {
                    return {
                        progress: data.progress[dimension],
                        isRunning: data.is_running
                    };
                }
                throw new Error('无法获取进度数据');
            });
    }

    // 更新进度UI
    function updateProgressUI() {
        fetchAnalysisProgress()
            .then(({ progress, isRunning }) => {
                console.log(`分析进度: ${progress}%, 是否运行中: ${isRunning}`);

                // 更新进度条
                const progressBar = document.getElementById('analysisProgressBar');
                if (progressBar) {
                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                    progressBar.textContent = `${progress}%`;
                }

                // 更新状态文本
                const statusText = document.getElementById('analysisStatusText');
                if (statusText) {
                    if (isRunning) {
                        statusText.textContent = `分析中 (${progress}%)`;
                    } else if (progress >= 100) {
                        statusText.textContent = '分析完成';
                        // 刷新页面以显示结果
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        statusText.textContent = '分析已停止';
                    }
                }

                // 如果仍在分析，继续轮询
                if (isRunning && progress < 100 && progress >= 0) {
                    setTimeout(updateProgressUI, 5000);
                }
            })
            .catch(err => {
                console.error('获取进度信息失败:', err);
                // 即使出错也继续尝试轮询
                setTimeout(updateProgressUI, 10000);
            });
    }

    // 加载Chart.js库
    function loadChartJS() {
        return new Promise((resolve, reject) => {
            // 检查Chart是否已经加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载');
                resolve();
                return;
            }

            // 尝试加载本地Chart.js
            var script = document.createElement('script');
            script.src = "{{ url_for('static', filename='js/lib/chart.min.js') }}";

            script.onload = function() {
                console.log('成功从本地加载Chart.js');
                resolve();
            };

            script.onerror = function() {
                // 如果本地加载失败，尝试备用路径
                console.warn('本地Chart.js加载失败，尝试备用路径');
                var backupScript = document.createElement('script');
                backupScript.src = "/direct-static/js/lib/chart.min.js";

                backupScript.onload = function() {
                    console.log('成功从备用路径加载Chart.js');
                    resolve();
                };

                backupScript.onerror = function() {
                    // 如果备用路径也失败，尝试CDN
                    console.warn('备用路径Chart.js加载失败，尝试CDN');
                    var cdnScript = document.createElement('script');
                    cdnScript.src = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";

                    cdnScript.onload = function() {
                        console.log('成功从CDN加载Chart.js');
                        resolve();
                    };

                    cdnScript.onerror = function() {
                        console.error('所有Chart.js加载尝试均失败');
                        reject(new Error('无法加载Chart.js'));
                    };

                    document.head.appendChild(cdnScript);
                };

                document.head.appendChild(backupScript);
            };

            document.head.appendChild(script);
        });
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，初始化');

        // 绑定重新分析按钮事件
        document.querySelectorAll('.reanalyze-btn').forEach(button => {
            button.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');
                const novelId = this.getAttribute('data-novel-id');

                if (!dimension || !novelId) {
                    console.error('缺少必要的数据属性');
                    return;
                }

                console.log(`开始重新分析 ${dimension}`);

                // 禁用按钮
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中...';

                // 发送分析请求
                fetch('/api/analysis/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        novel_id: novelId,
                        dimension: dimension
                    })
                })
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                        console.log('分析任务已启动');

                        // 显示进度条
                        const progressContainer = document.createElement('div');
                        progressContainer.className = 'mt-3';
                        progressContainer.innerHTML = `
                            <div class="progress">
                                <div id="analysisProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                                     style="width: 0%">0%</div>
                            </div>
                            <div class="text-center mt-1">
                                <small id="analysisStatusText">分析中 (0%)</small>
                            </div>
                        `;

                        // 插入进度条
                        this.parentNode.appendChild(progressContainer);

                        // 开始更新进度
                        updateProgressUI();
                    } else {
                        console.error('启动分析任务失败:', data.message);

                        // 恢复按钮状态
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-sync"></i> 重新分析';

                        // 显示错误信息
                        alert(`启动分析任务失败: ${data.message}`);
                    }
                })
                .catch(err => {
                    console.error('请求出错:', err);

                    // 恢复按钮状态
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-sync"></i> 重新分析';

                    // 显示错误信息
                    alert('请求出错，请稍后重试');
                });
            });
        });

        // 加载Chart.js并初始化图表
        loadChartJS()
            .then(() => {
                console.log('Chart.js加载成功，初始化图表');

                // 获取元数据
                const metadataElement = document.getElementById('analysis-metadata');
                let metadata = {};

                if (metadataElement) {
                    try {
                        metadata = JSON.parse(metadataElement.getAttribute('data-metadata') || '{}');
                    } catch (e) {
                        console.error('解析元数据时出错:', e);
                    }
                }

                // 初始化图表
                const canvas = document.getElementById('chapter-outline-chart');
                if (canvas) {
                    const ctx = canvas.getContext('2d');

                    // 章节大纲特定的图表数据
                    let labels = ['章节数量', '平均章节长度', '最长章节', '最短章节', '章节一致性', '结构完整性'];
                    let data = [0, 0, 0, 0, 0, 0];

                    // 如果元数据中有可视化数据，使用元数据中的数据
                    if (metadata.visualization_data && metadata.visualization_data.radar) {
                        const visualData = metadata.visualization_data.radar;
                        if (visualData && Array.isArray(visualData.labels) && Array.isArray(visualData.data)) {
                            console.log('使用元数据中的可视化数据');
                            labels = visualData.labels;
                            data = visualData.data;
                        }
                    } else {
                        // 使用默认数据
                        console.log('使用默认的章节大纲图表数据');

                        // 尝试从分析内容中提取章节数量
                        const contentElement = document.querySelector('.analysis-content');
                        if (contentElement) {
                            const content = contentElement.textContent || '';

                            // 简单估计章节数量
                            const chapterMatches = content.match(/第[一二三四五六七八九十百千万零\d]+章|Chapter\s+\d+/g);
                            const chapterCount = chapterMatches ? chapterMatches.length : 0;

                            if (chapterCount > 0) {
                                data[0] = chapterCount;
                                data[1] = 75; // 假设平均章节长度评分
                                data[2] = 85; // 假设最长章节评分
                                data[3] = 65; // 假设最短章节评分
                                data[4] = 80; // 假设章节一致性评分
                                data[5] = 90; // 假设结构完整性评分
                            } else {
                                // 使用随机数据
                                data = [
                                    Math.floor(Math.random() * 30) + 10, // 章节数量
                                    Math.floor(Math.random() * 30) + 60, // 平均章节长度
                                    Math.floor(Math.random() * 20) + 70, // 最长章节
                                    Math.floor(Math.random() * 20) + 60, // 最短章节
                                    Math.floor(Math.random() * 20) + 70, // 章节一致性
                                    Math.floor(Math.random() * 20) + 70  // 结构完整性
                                ];
                            }
                        }
                    }

                    // 创建雷达图
                    try {
                        console.log('使用安全方式创建章节大纲图表');

                        // 使用专用修复脚本创建图表（如果可用）
                        if (window.chapterOutlineChartFix && typeof window.chapterOutlineChartFix.safeCreateChapterOutlineChart === 'function') {
                            console.log('使用专用修复脚本创建图表');
                            window.chapterOutlineChartFix.safeCreateChapterOutlineChart(canvas);
                        }
                        // 使用统一图表修复脚本（如果可用）
                        else if (window.chartFix && typeof window.chartFix.createRadarChart === 'function') {
                            console.log('使用统一图表修复脚本创建图表');
                            const chartData = {
                                labels: labels,
                                datasets: [{
                                    label: '章节大纲分析',
                                    data: data,
                                    backgroundColor: 'rgba(74, 107, 223, 0.2)',
                                    borderColor: 'rgba(74, 107, 223, 1)',
                                    borderWidth: 2,
                                    pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                                    pointBorderColor: '#fff',
                                    pointHoverBackgroundColor: '#fff',
                                    pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                                }]
                            };
                            window.chartFix.createRadarChart(canvas, chartData);
                        }
                        // 使用传统方式创建图表
                        else {
                            console.log('使用传统方式创建图表');

                            // 检查是否已有图表实例，如果有则销毁
                            if (window.chartInstances && window.chartInstances['chapter-outline-chart']) {
                                window.chartInstances['chapter-outline-chart'].destroy();
                            }

                            // 创建新图表
                            const chartInstance = new Chart(ctx, {
                                type: 'radar',
                                data: {
                                    labels: labels,
                                    datasets: [{
                                        label: '章节大纲分析',
                                        data: data,
                                        backgroundColor: 'rgba(74, 107, 223, 0.2)',
                                        borderColor: 'rgba(74, 107, 223, 1)',
                                        borderWidth: 2,
                                        pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                                        pointBorderColor: '#fff',
                                        pointHoverBackgroundColor: '#fff',
                                        pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    scales: {
                                        r: {
                                            angleLines: {
                                                display: true
                                            },
                                            suggestedMin: 0,
                                            suggestedMax: 100
                                        }
                                    }
                                }
                            });

                            // 保存图表实例
                            if (!window.chartInstances) {
                                window.chartInstances = {};
                            }

                            // 保存图表实例
                            window.chartInstances['chapter-outline-chart'] = chartInstance;
                        }

                        console.log('章节大纲图表创建成功');
                    } catch (e) {
                        console.error('创建图表时出错:', e);
                    }
                }
            })
            .catch(err => {
                console.error('加载Chart.js失败:', err);
            });
    });
</script>
{% endblock %}
