"""
九猫 - 浏览器修复脚本
通过打开浏览器修复novel/4的character_relationships维度
"""

import os
import sys
import webbrowser
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def fix_in_browser():
    """通过打开浏览器修复novel/4的character_relationships维度"""
    logger.info("开始在浏览器中修复")
    
    # 尝试打开浏览器
    try:
        # 打开小说页面
        logger.info("打开小说页面: http://localhost:5001/novel/4")
        webbrowser.open("http://localhost:5001/novel/4")
        
        # 等待用户操作
        input("请在浏览器中手动修复问题，然后按回车键继续...")
        
        return True
    except Exception as e:
        logger.error(f"打开浏览器时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始运行浏览器修复脚本")
    
    # 在浏览器中修复
    success = fix_in_browser()
    
    if success:
        logger.info("浏览器修复成功")
    else:
        logger.error("浏览器修复失败")

if __name__ == "__main__":
    main()
