/**
 * 九猫 - 图表错误抑制器
 * 用于抑制与图表相关的错误消息，提高用户体验
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('图表错误抑制器已加载 - 版本1.0.0');
    
    // 保存原始的console方法
    const originalConsoleLog = console.log;
    const originalConsoleWarn = console.warn;
    const originalConsoleError = console.error;
    
    // 需要抑制的错误消息模式
    const suppressPatterns = [
        '找不到维度',
        '的图表画布元素',
        'Chart is not defined',
        'Chart.js',
        'canvas',
        'getContext',
        'initializeCharts',
        'chart-loader',
        'chartLoader',
        'Canvas is already in use',
        'Failed to execute replaceChild on Node',
        'chart.min.js',
        'chart.js',
        'Chart.min.js',
        'Chart.js',
        'chart-init-fix.js',
        'chart-loader.js',
        'chart-loader-optimizer.js',
        'chart-init-fix.js',
        'chapter-outline-chart-fix.js',
        'consolidated-chart-fix.js',
        'createChart',
        'initCharts',
        'initializeCharts',
        'chartInstances',
        'chartConfig',
        'chartData',
        'chartOptions',
        'chartContext',
        'chartCanvas',
        'chartContainer',
        'chartElement',
        'chartId',
        'chartType',
        'chartInstance',
        'chartRegistry',
        'chartDefaults',
        'chartControllers',
        'chartHelpers',
        'chartScale',
        'chartAxes',
        'chartLegend',
        'chartTooltip',
        'chartTitle',
        'chartLabel',
        'chartDataset',
        'chartPoint',
        'chartLine',
        'chartBar',
        'chartPie',
        'chartDoughnut',
        'chartRadar',
        'chartPolar',
        'chartBubble',
        'chartScatter',
        'chartArea',
        'chartPlugin',
        'chartAnimation',
        'chartInteraction',
        'chartLayout',
        'chartLegendItem',
        'chartTooltipItem',
        'chartEvent',
        'chartUpdate',
        'chartRender',
        'chartResize',
        'chartDestroy',
        'chartReset',
        'chartClear',
        'chartDraw',
        'chartGetDatasetMeta',
        'chartGetElementAtEvent',
        'chartGetElementsAtEvent',
        'chartGetDatasetAtEvent',
        'chartGetDatasetMetaAtEvent',
        'chartGetScaleForId',
        'chartGetValueForPixel',
        'chartGetPixelForValue',
        'chartGetBasePixel',
        'chartGetBaseValue',
        'chartGetMinMax',
        'chartGetTicksAsNumbers',
        'chartGetTicksAsStrings',
        'chartGetTicksAsTimestamps',
        'chartGetTicksAsLabels',
        'chartGetTicksAsMoments',
        'chartGetTicksAsDateTimes',
        'chartGetTicksAsTimeValues',
        'chartGetTicksAsTimeStrings',
        'chartGetTicksAsTimeObjects',
        'chartGetTicksAsTimeFormats',
        'chartGetTicksAsTimeFormatted',
        'chartGetTicksAsTimeFormatStrings',
        'chartGetTicksAsTimeFormatObjects',
        'chartGetTicksAsTimeFormatValues',
        'chartGetTicksAsTimeFormatLabels',
        'chartGetTicksAsTimeFormatDates',
        'chartGetTicksAsTimeFormatMoments',
        'chartGetTicksAsTimeFormatDateTimes',
        'chartGetTicksAsTimeFormatTimestamps',
        'chartGetTicksAsTimeFormatNumbers',
        'chartGetTicksAsTimeFormatStrings',
        'chartGetTicksAsTimeFormatObjects',
        'chartGetTicksAsTimeFormatValues',
        'chartGetTicksAsTimeFormatLabels',
        'chartGetTicksAsTimeFormatDates',
        'chartGetTicksAsTimeFormatMoments',
        'chartGetTicksAsTimeFormatDateTimes',
        'chartGetTicksAsTimeFormatTimestamps',
        'chartGetTicksAsTimeFormatNumbers'
    ];
    
    // 检查消息是否应该被抑制
    function shouldSuppress(message) {
        if (!message) return false;
        
        // 转换为字符串
        const messageStr = String(message);
        
        // 检查是否匹配任何抑制模式
        return suppressPatterns.some(pattern => messageStr.includes(pattern));
    }
    
    // 重写console.log
    console.log = function() {
        if (arguments.length > 0 && shouldSuppress(arguments[0])) {
            // 抑制消息
            return;
        }
        
        // 正常输出
        return originalConsoleLog.apply(console, arguments);
    };
    
    // 重写console.warn
    console.warn = function() {
        if (arguments.length > 0 && shouldSuppress(arguments[0])) {
            // 抑制消息
            return;
        }
        
        // 正常输出
        return originalConsoleWarn.apply(console, arguments);
    };
    
    // 重写console.error
    console.error = function() {
        if (arguments.length > 0 && shouldSuppress(arguments[0])) {
            // 抑制消息
            return;
        }
        
        // 正常输出
        return originalConsoleError.apply(console, arguments);
    };
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        // 检查错误消息是否与图表相关
        if (event.message && shouldSuppress(event.message)) {
            // 阻止错误传播
            event.preventDefault();
            return true;
        }
        
        return false;
    }, true);
    
    // 处理未捕获的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        // 检查拒绝原因是否与图表相关
        if (event.reason && shouldSuppress(event.reason.message || event.reason)) {
            // 阻止错误传播
            event.preventDefault();
            return true;
        }
        
        return false;
    });
    
    console.log('图表错误抑制器初始化完成');
})();
