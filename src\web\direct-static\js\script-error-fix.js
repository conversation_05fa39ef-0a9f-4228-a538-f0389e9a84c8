/**
 * 九猫 - Script Error修复脚本
 * 用于处理跨域脚本错误和未知来源错误
 * 版本: 1.0.0
 */

(function() {
    console.log('[Script Error修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,                 // 是否启用调试模式
        suppressScriptErrors: true,  // 是否抑制Script Error错误
        fixCrossOriginErrors: true,  // 是否修复跨域错误
        fixUnknownSourceErrors: true // 是否修复未知来源错误
    };

    // 状态
    const STATE = {
        initialized: false,          // 是否已初始化
        originalOnError: null,       // 原始onerror函数
        errorCount: 0,               // 错误计数
        maxErrors: 50,               // 最大错误数，超过后将停止详细日志
        handledErrors: new Set()     // 已处理的错误ID
    };

    // 安全日志函数
    function safeLog(message, level = 'info') {
        if (CONFIG.debug) {
            const prefix = '[Script Error修复]';
            try {
                switch (level) {
                    case 'error':
                        console.error(`${prefix} ${message}`);
                        break;
                    case 'warn':
                        console.warn(`${prefix} ${message}`);
                        break;
                    default:
                        console.log(`${prefix} ${message}`);
                }
            } catch (e) {
                // 忽略日志错误
            }
        }
    }

    // 生成错误ID
    function generateErrorId(error) {
        try {
            const { message, filename, lineno, colno } = error;
            return `${filename || 'unknown'}:${lineno || 0}:${colno || 0}:${message || 'unknown'}`;
        } catch (e) {
            return 'error-id-generation-failed';
        }
    }

    // 检查是否是Script Error
    function isScriptError(message) {
        return message === 'Script error.' || 
               message === 'Script error' || 
               message.includes('Script error');
    }

    // 检查是否是未知来源错误
    function isUnknownSourceError(source) {
        return !source || 
               source === 'unknown' || 
               source === 'undefined' || 
               source === 'null' || 
               source.includes('unknown');
    }

    // 修复跨域脚本
    function fixCrossOriginScripts() {
        // 查找所有外部脚本
        document.querySelectorAll('script[src]').forEach(script => {
            // 如果脚本没有crossorigin属性，添加它
            if (!script.hasAttribute('crossorigin')) {
                script.setAttribute('crossorigin', 'anonymous');
                safeLog(`为脚本添加crossorigin属性: ${script.src}`, 'info');
            }
        });

        // 监听动态添加的脚本
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.tagName === 'SCRIPT' && node.src && !node.hasAttribute('crossorigin')) {
                            node.setAttribute('crossorigin', 'anonymous');
                            safeLog(`为动态添加的脚本添加crossorigin属性: ${node.src}`, 'info');
                        }
                    });
                }
            });
        });

        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });

        safeLog('已设置跨域脚本修复', 'info');
    }

    // 设置全局错误处理
    function setupGlobalErrorHandler() {
        // 保存原始onerror
        STATE.originalOnError = window.onerror;

        // 设置新的onerror
        window.onerror = function(message, source, lineno, colno, error) {
            // 增加错误计数
            STATE.errorCount++;

            // 生成错误ID
            const errorId = generateErrorId({ message, filename: source, lineno, colno, error });

            // 如果已经处理过，跳过
            if (STATE.handledErrors.has(errorId)) {
                return true;
            }

            // 记录错误
            STATE.handledErrors.add(errorId);

            // 检查是否超过最大错误数
            if (STATE.errorCount > STATE.maxErrors) {
                if (STATE.errorCount === STATE.maxErrors + 1) {
                    safeLog(`错误太多 (${STATE.maxErrors}+)，停止输出详细日志以避免性能问题`, 'warn');
                }
                return true;
            }

            // 处理Script Error
            if (isScriptError(message) && CONFIG.suppressScriptErrors) {
                safeLog(`捕获到Script Error: ${message}`, 'warn');
                return true; // 阻止错误继续传播
            }

            // 处理未知来源错误
            if (isUnknownSourceError(source) && CONFIG.fixUnknownSourceErrors) {
                safeLog(`捕获到未知来源错误: ${message}`, 'warn');
                return true; // 阻止错误继续传播
            }

            // 如果有原始onerror，调用它
            if (typeof STATE.originalOnError === 'function') {
                return STATE.originalOnError(message, source, lineno, colno, error);
            }

            return false; // 不阻止错误继续传播
        };

        safeLog('已设置全局错误处理', 'info');
    }

    // 初始化
    function initialize() {
        if (STATE.initialized) {
            return;
        }

        safeLog('初始化Script Error修复脚本', 'info');

        // 修复跨域脚本
        if (CONFIG.fixCrossOriginErrors) {
            fixCrossOriginScripts();
        }

        // 设置全局错误处理
        setupGlobalErrorHandler();

        // 标记为已初始化
        STATE.initialized = true;

        safeLog('Script Error修复脚本初始化完成', 'info');
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出到全局命名空间，以便调试
    window.scriptErrorFix = {
        config: CONFIG,
        state: STATE,
        initialize: initialize,
        fixCrossOriginScripts: fixCrossOriginScripts
    };

    safeLog('脚本加载完成', 'info');
})();
