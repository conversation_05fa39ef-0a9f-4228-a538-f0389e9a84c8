"""
九猫 - 删除分析结果脚本
删除novel/4页面的character_relationships分析结果
"""

import os
import sqlite3
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_database_file():
    """查找数据库文件"""
    # 可能的数据库文件路径
    possible_paths = [
        'data/novels.db',
        'src/data/novels.db',
        'novels.db',
        'instance/novels.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"找到数据库文件: {path}")
            return path
    
    # 如果没有找到，搜索当前目录及其子目录
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                path = os.path.join(root, file)
                logger.info(f"找到数据库文件: {path}")
                return path
    
    logger.error("未找到数据库文件")
    return None

def delete_analysis_result():
    """删除novel/4页面的character_relationships分析结果"""
    # 查找数据库文件
    db_path = find_database_file()
    if not db_path:
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询要删除的分析结果
        cursor.execute("""
            SELECT id, novel_id, dimension FROM analysis_results 
            WHERE novel_id = 4 AND dimension = 'character_relationships'
        """)
        
        result = cursor.fetchone()
        
        if not result:
            logger.warning("未找到要删除的分析结果")
            
            # 查询所有novel_id=4的分析结果
            cursor.execute("""
                SELECT id, dimension FROM analysis_results 
                WHERE novel_id = 4
            """)
            
            results = cursor.fetchall()
            logger.info(f"novel_id=4的所有分析结果: {results}")
            
            conn.close()
            return False
        
        result_id, novel_id, dimension = result
        logger.info(f"找到要删除的分析结果ID: {result_id}, novel_id: {novel_id}, dimension: {dimension}")
        
        # 删除分析结果
        cursor.execute("""
            DELETE FROM analysis_results 
            WHERE id = ?
        """, (result_id,))
        
        # 提交更改
        conn.commit()
        
        # 验证删除结果
        cursor.execute("""
            SELECT id FROM analysis_results 
            WHERE id = ?
        """, (result_id,))
        
        if cursor.fetchone():
            logger.error("删除失败")
            conn.close()
            return False
        
        logger.info("成功删除分析结果")
        
        # 关闭连接
        conn.close()
        
        return True
    except Exception as e:
        logger.error(f"删除分析结果时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始删除分析结果")
    
    # 删除分析结果
    success = delete_analysis_result()
    
    if success:
        logger.info("删除分析结果成功")
        logger.info("请重启服务器以应用更改")
    else:
        logger.error("删除分析结果失败")

if __name__ == "__main__":
    main()
