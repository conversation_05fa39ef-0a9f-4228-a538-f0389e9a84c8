"""
字数追踪脚本，用于实时监控字数并反馈给API写作
"""

import re
import logging
from typing import Dict, Tuple, List, Optional

# 配置日志
logger = logging.getLogger(__name__)

class WordCountTracker:
    """
    字数追踪器，用于实时监控字数并反馈给API写作
    """

    def __init__(self, target_word_count: int, min_ratio: float = 0.92, max_ratio: float = 1.08):
        """
        初始化字数追踪器

        Args:
            target_word_count: 目标字数
            min_ratio: 最小字数比例（默认为0.92，即目标字数的92%）
            max_ratio: 最大字数比例（默认为1.08，即目标字数的108%）
        """
        self.target_word_count = target_word_count
        self.min_word_count = int(target_word_count * min_ratio)
        self.max_word_count = int(target_word_count * max_ratio)
        self.current_word_count = 0
        self.sections = []  # 存储各部分内容及其字数
        self.progress_history = []  # 存储字数变化历史

    def update_content(self, content: str) -> Dict:
        """
        更新内容并计算字数

        Args:
            content: 当前内容

        Returns:
            字数统计信息
        """
        # 移除章节框架规划部分
        cleaned_content = self._remove_planning_content(content)

        # 计算字数
        self.current_word_count = len(cleaned_content)

        # 记录字数变化
        self.progress_history.append(self.current_word_count)

        # 分析段落和章节结构
        self._analyze_sections(cleaned_content)

        # 计算完成度
        completion_percentage = min(100, round(self.current_word_count / self.target_word_count * 100, 1))

        # 计算字数差异
        word_count_diff = self.current_word_count - self.target_word_count
        word_count_diff_percentage = round(abs(word_count_diff) / self.target_word_count * 100, 1)

        # 判断字数是否在目标范围内
        is_within_target = self.min_word_count <= self.current_word_count <= self.max_word_count

        # 生成字数统计信息
        stats = {
            "current_word_count": self.current_word_count,
            "target_word_count": self.target_word_count,
            "min_word_count": self.min_word_count,
            "max_word_count": self.max_word_count,
            "completion_percentage": completion_percentage,
            "word_count_diff": word_count_diff,
            "word_count_diff_percentage": word_count_diff_percentage,
            "is_within_target": is_within_target,
            "sections": self.sections,
            "progress_history": self.progress_history
        }

        # 记录日志
        logger.info(f"字数统计: 当前字数 {self.current_word_count}，目标字数 {self.target_word_count}，完成度 {completion_percentage}%")

        return stats

    def generate_progress_comment(self) -> str:
        """
        生成进度注释

        Returns:
            进度注释
        """
        # 避免除零错误
        if self.target_word_count > 0:
            completion_percentage = min(100, round(self.current_word_count / self.target_word_count * 100, 1))
        else:
            completion_percentage = 0

        return f"<!-- 当前进度：约{self.current_word_count}字，目标{self.target_word_count}字，完成度{completion_percentage}% -->"

    def generate_feedback(self) -> Dict:
        """
        生成反馈信息

        Returns:
            反馈信息
        """
        # 避免除零错误
        if self.target_word_count > 0:
            completion_percentage = min(100, round(self.current_word_count / self.target_word_count * 100, 1))
        else:
            completion_percentage = 0
        word_count_diff = self.current_word_count - self.target_word_count

        # 判断字数是否在目标范围内
        is_within_target = self.min_word_count <= self.current_word_count <= self.max_word_count

        # 生成反馈信息
        feedback = {
            "status": "within_target" if is_within_target else "out_of_target",
            "message": "",
            "suggestions": []
        }

        # 根据字数情况生成反馈信息
        if self.current_word_count < self.min_word_count:
            feedback["message"] = f"当前字数({self.current_word_count})小于目标范围的下限({self.min_word_count})，需要增加内容"
            feedback["suggestions"] = [
                "增加细节描写",
                "增加对话内容",
                "增加场景描写",
                "增加人物心理活动描写"
            ]
        elif self.current_word_count > self.max_word_count:
            feedback["message"] = f"当前字数({self.current_word_count})大于目标范围的上限({self.max_word_count})，需要精简内容"
            feedback["suggestions"] = [
                "精简冗余描写",
                "减少重复内容",
                "合并相似场景",
                "删除不必要的细节"
            ]
        else:
            feedback["message"] = f"当前字数({self.current_word_count})在目标范围内，继续保持"

            # 如果接近目标字数的90%，提示开始规划结尾
            if completion_percentage >= 90 and completion_percentage < 95:
                feedback["suggestions"].append("已接近目标字数的90%，可以开始规划结尾部分")
            # 如果接近目标字数的95%，提示准备结束
            elif completion_percentage >= 95:
                feedback["suggestions"].append("已接近目标字数的95%，准备结束本章内容")

        return feedback

    def _analyze_sections(self, content: str) -> None:
        """
        分析段落和章节结构

        Args:
            content: 当前内容
        """
        # 清空之前的分析结果
        self.sections = []

        # 按段落分割内容
        paragraphs = content.split("\n\n")

        # 分析每个段落
        current_section = {"title": "开头", "content": "", "word_count": 0}

        for paragraph in paragraphs:
            # 如果是标题，创建新的章节
            if paragraph.startswith("# ") or paragraph.startswith("## "):
                # 如果当前章节不为空，保存它
                if current_section["word_count"] > 0:
                    self.sections.append(current_section)

                # 创建新的章节
                current_section = {
                    "title": paragraph.strip("# "),
                    "content": paragraph,
                    "word_count": len(paragraph)
                }
            else:
                # 将段落添加到当前章节
                current_section["content"] += "\n\n" + paragraph
                current_section["word_count"] += len(paragraph)

        # 保存最后一个章节
        if current_section["word_count"] > 0:
            self.sections.append(current_section)

    def _remove_planning_content(self, content: str) -> str:
        """
        移除生成内容中的章节框架规划部分和字数注释

        Args:
            content: 生成的原始内容

        Returns:
            移除章节框架规划和字数注释后的内容
        """
        # 如果内容为空，直接返回
        if not content:
            return content

        # 定义可能的章节框架规划标记
        planning_markers = [
            "章节框架规划",
            "第一步：章节定位与主题",
            "第二步：情节与场景规划",
            "第三步：人物规划",
            "第四步：维度应用规划",
            "第五步：结构与节奏规划",
            "第六步：字数分配规划",
            "第七步：风格与语言规划",
            "第八步：章节间连贯性规划",
            "检查要点：",
            "情节连贯和人物形象一致"
        ]

        # 查找章节标题的位置
        chapter_title_match = re.search(r'# 第\d+章', content)
        if chapter_title_match:
            # 如果找到章节标题，检查标题前的内容是否包含规划内容
            title_pos = chapter_title_match.start()
            pre_title_content = content[:title_pos].strip()

            # 检查标题前的内容是否包含规划标记
            contains_planning = any(marker in pre_title_content for marker in planning_markers)
            if contains_planning:
                # 如果包含规划内容，只保留从章节标题开始的部分
                content = content[title_pos:].strip()

        # 尝试查找规划部分的开始和结束位置
        for marker in planning_markers:
            if marker in content:
                # 找到规划部分的开始位置
                planning_start = content.find(marker)

                # 查找规划部分之后的第一个章节标题
                chapter_match = re.search(r'# 第\d+章', content[planning_start:])
                if chapter_match:
                    # 找到章节标题的位置
                    chapter_pos = planning_start + chapter_match.start()

                    # 移除规划部分，只保留章节标题及之后的内容
                    content = content[chapter_pos:].strip()
                    break

                # 如果没有找到章节标题，尝试查找规划部分的结束位置
                # 可能的结束标记
                end_markers = ["开始写作", "正文开始", "章节内容", "# "]
                for end_marker in end_markers:
                    end_pos = content.find(end_marker, planning_start + len(marker))
                    if end_pos > planning_start:
                        # 找到结束位置，移除规划部分
                        content = (content[:planning_start] + content[end_pos:]).strip()
                        break

        # 移除字数注释 <!-- 当前字数：1200字 -->
        content = re.sub(r'<!--\s*当前字数：\d+字\s*-->', '', content)

        # 移除进度注释 <!-- 当前进度：约X字，目标Y字，完成度Z% -->
        content = re.sub(r'<!--\s*当前进度：约\d+字，目标\d+字，完成度\d+%\s*-->', '', content)

        # 移除任何其他HTML注释
        content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)

        # 移除多余的空行（连续的两个以上换行符替换为两个换行符）
        content = re.sub(r'\n{3,}', '\n\n', content)

        return content.strip()
