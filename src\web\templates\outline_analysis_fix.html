{% extends "base.html" %}

{% block title %}{{ novel.title }} - 大纲分析 - 九猫{% endblock %}

{% block head %}
{{ super() }}
<!-- 加载修复脚本 -->
<script src="{{ url_for('static', filename='js/json-parse-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/json-parse-fix-enhanced.js';"></script>
<script src="{{ url_for('static', filename='js/node-replace-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/node-replace-fix.js';"></script>
<script src="{{ url_for('static', filename='js/outline-analysis-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/outline-analysis-fix.js';"></script>
{% endblock %}

{% block extra_css %}
<style>
    .analysis-content {
        white-space: pre-wrap;
        line-height: 1.6;
    }

    .metadata-item {
        margin-bottom: 0.5rem;
    }

    .visualization-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }

    .analysis-logs {
        font-family: monospace;
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .log-entry {
        padding: 2px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .log-entry:last-child {
        border-bottom: none;
    }

    #analysisLogsCollapse .card-body,
    #analysisProcessCollapse .card-body {
        padding: 0.5rem;
        background-color: #f8f9fa;
    }

    .analysis-process-card {
        margin-bottom: 1rem;
        border: 1px solid rgba(0,0,0,.125);
    }

    .analysis-process-card .card-header {
        background-color: rgba(0,0,0,.03);
        padding: 0.5rem 1rem;
    }

    /* 推理过程样式 */
    .reasoning-text.markdown-content h2 {
        color: #8a6d3b;
        background-color: #fcf8e3;
        padding: 10px;
        border-left: 4px solid #f0ad4e;
        margin-top: 20px;
        margin-bottom: 15px;
        font-size: 1.4rem;
    }

    .reasoning-text.markdown-content h3 {
        color: #31708f;
        border-bottom: 1px solid #bce8f1;
        padding-bottom: 5px;
        margin-top: 15px;
        margin-bottom: 10px;
        font-size: 1.2rem;
    }

    .reasoning-text.markdown-content strong {
        color: #333;
    }

    .reasoning-text.markdown-content ol {
        padding-left: 20px;
    }

    .reasoning-text.markdown-content ol li {
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <!-- 分析进度条 -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">分析进度</h5>
                    <div id="analysisStatus" class="badge badge-info px-3 py-2">加载中...</div>
                </div>
            </div>
            <div class="card-body">
                <div class="progress mb-3" style="height: 20px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>当前维度:</strong> <span id="currentDimension">大纲分析</span></p>
                        <p><strong>分块进度:</strong> <span id="blocksProgress">-</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>预计剩余时间:</strong> <span id="remainingTime">-</span></p>
                        <p><strong>预计完成时间:</strong> <span id="estimatedCompletionTime">-</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="card mb-4">
            <div class="card-header"><h5 class="card-title">分析日志</h5></div>
            <div class="card-body" id="logContainer" data-novel-id="{{ novel.id }}" style="background:#f8f9fa; height:200px; overflow-y:auto; font-family: monospace; font-size:0.9rem;"></div>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
                <li class="breadcrumb-item active">大纲分析</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <h1>大纲分析</h1>
            <div>
                <a href="{{ url_for('v2.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary mr-2">
                返回小说页面
            </a>
                <a href="{{ url_for('analysis_process.view_analysis_process_viewer', novel_id=novel.id, dimension='outline_analysis') }}" class="btn btn-outline-primary">
                    查看完整分析过程
                </a>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-9">
                <!-- 标签页导航 -->
                <ul class="nav nav-tabs mb-3" id="analysisTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link active" id="result-tab" data-toggle="tab" href="#result" role="tab" aria-controls="result" aria-selected="true">分析结果</a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" id="reasoning-tab" data-toggle="tab" href="#reasoning" role="tab" aria-controls="reasoning" aria-selected="false">推理过程</a>
                    </li>
                </ul>
                
                <!-- 标签页内容 -->
                <div class="tab-content" id="analysisTabContent">
                    <!-- 分析结果标签页 -->
                    <div class="tab-pane fade show active" id="result" role="tabpanel" aria-labelledby="result-tab">
                        <div class="card">
                            <div class="card-body">
                                <div class="analysis-content markdown-content" data-dimension="outline_analysis">
                                    {{ analysis_result.content|safe }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 推理过程标签页 -->
                    <div class="tab-pane fade" id="reasoning" role="tabpanel" aria-labelledby="reasoning-tab">
                        <div class="card">
                            <div class="card-body">
                                <div id="reasoning-container-outline_analysis"
                                     data-reasoning-container="true"
                                     data-novel-id="{{ novel.id }}"
                                     data-dimension="outline_analysis">
                                    {% if analysis_result.reasoning_content %}
                                    <div class="reasoning-text markdown-content">
                                        {{ analysis_result.reasoning_content|safe }}
                                    </div>
                                    {% elif analysis_result.metadata and analysis_result.metadata.reasoning_content %}
                                    <div class="reasoning-text markdown-content">
                                        {{ analysis_result.metadata.reasoning_content|safe }}
                                    </div>
                                    {% elif analysis_result.analysis_metadata and analysis_result.analysis_metadata.reasoning_content %}
                                    <div class="reasoning-text markdown-content">
                                        {{ analysis_result.analysis_metadata.reasoning_content|safe }}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载推理过程，请稍候...</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分析过程链接 -->
                <div class="card analysis-process-card mb-3 mt-3">
                    <div class="card-header">
                        <h6 class="mb-0 d-flex justify-content-between align-items-center">
                            <span>分析过程</span>
                            <a href="{{ url_for('analysis_process.view_analysis_process_viewer', novel_id=novel.id, dimension='outline_analysis') }}" class="btn btn-sm btn-outline-primary">
                                查看完整分析过程
                            </a>
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-0">
                            为了优化内存使用和提高页面加载速度，分析过程已移至单独页面。点击上方按钮查看完整分析过程。
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">小说信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>标题：</strong> {{ novel.title }}</p>
                        <p><strong>作者：</strong> {{ novel.author or '未知' }}</p>
                        <p><strong>字数：</strong> {{ novel.word_count }}</p>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">分析元数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="metadata-item">
                            <strong>分析时间：</strong>
                            <span>{{ analysis_result.created_at.strftime('%Y-%m-%d %H:%M') if analysis_result and analysis_result.created_at else '未知' }}</span>
                        </div>

                        {% if analysis_result and analysis_result.analysis_metadata %}
                            <div class="metadata-item"><strong>处理时间：</strong> {{ analysis_result.analysis_metadata.processing_time|default(0)|round(2) }} 秒</div>
                            <div class="metadata-item"><strong>分块数量：</strong> {{ analysis_result.analysis_metadata.chunk_count|default(0) }}</div>
                            <div class="metadata-item"><strong>API调用次数：</strong> {{ analysis_result.analysis_metadata.api_calls|default(0) }}</div>
                            <div class="metadata-item"><strong>令牌使用量：</strong> {{ analysis_result.analysis_metadata.tokens_used|default(0) }}</div>
                            <div class="metadata-item"><strong>费用：</strong> {{ analysis_result.analysis_metadata.cost|default(0)|round(4) }} 元</div>
                        {% else %}
                            <div class="metadata-item"><strong>处理时间：</strong> 0.00 秒</div>
                            <div class="metadata-item"><strong>分块数量：</strong> 0</div>
                            <div class="metadata-item"><strong>API调用次数：</strong> 0</div>
                            <div class="metadata-item"><strong>令牌使用量：</strong> 0</div>
                            <div class="metadata-item"><strong>费用：</strong> 0.0000 元</div>
                        {% endif %}

                        {% if analysis_result and analysis_result.updated_at and analysis_result.updated_at != analysis_result.created_at %}
                            <div class="metadata-item">
                                <strong>最后更新：</strong>
                                <span>{{ analysis_result.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">操作</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary w-100 mb-2 reanalyze-btn" data-dimension="outline_analysis" data-novel-id="{{ novel.id }}">
                            <i class="fas fa-sync"></i> 重新分析
                        </button>
                        <a href="{{ url_for('v2.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary w-100">
                            返回小说页面
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 预先定义分析结果元数据 -->
<div id="analysis-metadata" style="display:none;" data-metadata='{}'></div>
<!-- 注意：元数据已被禁用以避免序列化错误 -->

<!-- 分析详情页面刷新阻止脚本 - 必须最先加载 -->
<script src="{{ url_for('static', filename='js/lib/analysis-detail-refresh-blocker.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/lib/analysis-detail-refresh-blocker.js';"></script>

<!-- 进度信息修复脚本（增强版） -->
<script src="{{ url_for('static', filename='js/analysis-progress-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-progress-fix-enhanced.js';"></script>
<!-- 维度详情页面修复脚本（增强版） -->
<script src="{{ url_for('static', filename='js/dimension-detail-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-detail-fix-enhanced.js';"></script>

<!-- 图表修复脚本 -->
<script src="{{ url_for('static', filename='js/chart-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chart-fix.js';"></script>
<!-- 统一图表修复脚本 -->
<script src="{{ url_for('static', filename='js/consolidated-chart-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/consolidated-chart-fix.js';"></script>

<!-- 分析结果页面修复脚本 -->
<script src="{{ url_for('static', filename='js/analysis-page-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-page-fix.js';"></script>
<!-- 分析完成修复脚本 - 解决13个维度全部完成时分析结果不可见的问题 -->
<script src="{{ url_for('static', filename='js/analysis-complete-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-complete-fix.js';"></script>
<!-- 统一推理过程修复脚本 - 替代所有其他推理过程修复脚本 -->
<script src="{{ url_for('static', filename='js/unified-reasoning-content-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/unified-reasoning-content-fix.js';"></script>

<!-- 维度分析通用修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-analysis-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-analysis-fix.js';"></script>

<!-- 分析过程日志过滤脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 日志过滤按钮
    const showAllLogsBtn = document.getElementById('showAllLogs');
    const showImportantLogsBtn = document.getElementById('showImportantLogs');
    const showWarningLogsBtn = document.getElementById('showWarningLogs');
    const showErrorLogsBtn = document.getElementById('showErrorLogs');

    if (showAllLogsBtn) {
        showAllLogsBtn.addEventListener('click', function() {
            filterLogs('all');
            setActiveButton(this);
        });
    }

    if (showImportantLogsBtn) {
        showImportantLogsBtn.addEventListener('click', function() {
            filterLogs('important');
            setActiveButton(this);
        });
    }

    if (showWarningLogsBtn) {
        showWarningLogsBtn.addEventListener('click', function() {
            filterLogs('warning');
            setActiveButton(this);
        });
    }

    if (showErrorLogsBtn) {
        showErrorLogsBtn.addEventListener('click', function() {
            filterLogs('error');
            setActiveButton(this);
        });
    }

    // 默认显示全部日志
    if (showAllLogsBtn) {
        showAllLogsBtn.click();
    }

    // 设置活动按钮样式
    function setActiveButton(button) {
        const buttons = document.querySelectorAll('.btn-group .btn');
        buttons.forEach(btn => {
            btn.classList.remove('active');

            // 移除所有颜色类
            btn.classList.remove('btn-secondary', 'btn-primary', 'btn-warning', 'btn-danger');

            // 添加回轮廓类
            if (btn.id === 'showAllLogs') btn.classList.add('btn-outline-secondary');
            if (btn.id === 'showImportantLogs') btn.classList.add('btn-outline-primary');
            if (btn.id === 'showWarningLogs') btn.classList.add('btn-outline-warning');
            if (btn.id === 'showErrorLogs') btn.classList.add('btn-outline-danger');
        });

        // 添加活动类
        button.classList.add('active');

        // 移除轮廓类，添加实心类
        if (button.id === 'showAllLogs') {
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-secondary');
        }
        if (button.id === 'showImportantLogs') {
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-primary');
        }
        if (button.id === 'showWarningLogs') {
            button.classList.remove('btn-outline-warning');
            button.classList.add('btn-warning');
        }
        if (button.id === 'showErrorLogs') {
            button.classList.remove('btn-outline-danger');
            button.classList.add('btn-danger');
        }
    }

    // 过滤日志
    function filterLogs(type) {
        const logEntries = document.querySelectorAll('.log-entry');

        logEntries.forEach(entry => {
            if (type === 'all') {
                entry.style.display = '';
            } else if (type === 'important' && entry.classList.contains('important-log')) {
                entry.style.display = '';
            } else if (type === 'warning' && entry.classList.contains('warning-log')) {
                entry.style.display = '';
            } else if (type === 'error' && entry.classList.contains('error-log')) {
                entry.style.display = '';
            } else {
                entry.style.display = 'none';
            }
        });
    }
});
</script>

<script>
    // 全局变量
    const novelId = "{{ novel.id }}";
    const dimension = "outline_analysis";

    // 实时进度更新函数
    function fetchAnalysisProgress() {
        return fetch(`/api/analysis/progress?novel_id=${novelId}`)
            .then(res => res.json())
            .then(data => {
                if (data.success && data.progress) {
                    // 检查当前维度是否存在于progress中
                    if (data.progress[dimension]) {
                        return {
                            progress: data.progress[dimension],
                            isRunning: data.is_running
                        };
                    } else if (Object.keys(data.progress).length > 0) {
                        // 如果当前维度不存在但有其他维度的进度数据,
                        // 则可能是分析已完成但未正确显示
                        // 返回一个完成状态
                        console.log('[九猫修复] 当前维度未在进度数据中找到，但发现其他维度数据，返回完成状态');
                        return {
                            progress: 100,
                            isRunning: false
                        };
                    }
                }
                throw new Error('无法获取进度数据');
            });
    }

    // 添加全局fetchAnalysisResult函数
    window.fetchAnalysisResult = function(novelId, chapterId, dimension) {
        console.log(`[全局API] 获取分析结果: novelId=${novelId}, dimension=${dimension}`);

        // 构建适当的URL
        let url;
        if (chapterId) {
            url = `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}`;
        } else {
            url = `/api/novel/${novelId}/analysis/${dimension}`;
        }

        console.log(`[全局API] 请求URL: ${url}`);

        return fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data && data.success && data.result) {
                    return data.result;
                } else {
                    throw new Error('分析结果为空或无效');
                }
            });
    };

    // 添加全局updateAnalysisResult函数
    window.updateAnalysisResult = function(result) {
        console.log('[全局API] 更新分析结果显示');

        // 查找内容容器
        const contentContainer = document.querySelector('.analysis-content');
        if (!contentContainer) {
            console.error('[全局API] 找不到内容容器');
            return false;
        }

        // 更新内容
        if (result.content) {
            contentContainer.innerHTML = result.content;
            console.log('[全局API] 成功更新分析结果内容');
            return true;
        }

        return false;
    };

    function updateProgressUI() {
        fetchAnalysisProgress()
            .then(data => {
                const progressData = data.progress;
                const isRunning = data.isRunning;

                // 更新进度条
                const progress = progressData.progress || 0;
                const progressBar = document.getElementById('progressBar');
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                progressBar.textContent = `${progress}%`;

                // 更新分析状态
                const statusElement = document.getElementById('analysisStatus');
                if (progress === 100) {
                    statusElement.className = 'badge badge-success px-3 py-2';
                    statusElement.textContent = '已完成';
                    progressBar.className = 'progress-bar bg-success';
                } else if (progress < 0) {
                    statusElement.className = 'badge badge-danger px-3 py-2';
                    statusElement.textContent = '已终止';
                    progressBar.className = 'progress-bar bg-danger';
                } else if (isRunning) {
                    statusElement.className = 'badge badge-primary px-3 py-2';
                    statusElement.textContent = '分析中';
                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
                } else {
                    statusElement.className = 'badge badge-warning px-3 py-2';
                    statusElement.textContent = '已暂停';
                    progressBar.className = 'progress-bar bg-warning';
                }

                // 更新块进度
                if (progressData.blocks_progress) {
                    document.getElementById('blocksProgress').textContent = progressData.blocks_progress;
                }

                // 更新时间信息
                if (progressData.remaining_time) {
                    document.getElementById('remainingTime').textContent = progressData.remaining_time;
                }

                if (progressData.eta) {
                    document.getElementById('estimatedCompletionTime').textContent = progressData.eta;
                }

                // 禁用自动刷新
                console.log('[九猫修复] 已禁用自动刷新');
            })
            .catch(err => {
                console.error('获取进度信息失败:', err);
                // 禁用自动刷新
                console.log('[九猫修复] 已禁用进度信息错误后的自动刷新');
            });
    }

    // 实时日志拉取函数
    function fetchLogs(since) {
        const logContainer = document.getElementById('logContainer');
        const novelId = logContainer.dataset.novelId;
        let url = `/api/analysis/logs?novel_id=${novelId}&level=all&limit=200&dimension=${dimension}`;
        if (since) url += `&since=${since}`;
        return fetch(url).then(res=>res.json());
    }

    function initLogControls() {
        const logContainer = document.getElementById('logContainer');
        let lastTs = '';

        // 添加控制按钮
        const controlDiv = document.createElement('div');
        controlDiv.className = 'mb-2 d-flex justify-content-between';
        controlDiv.innerHTML = `
            <div>
                <button id="refreshLogsBtn" class="btn btn-sm btn-outline-primary">刷新日志</button>
                <button id="clearLogsBtn" class="btn btn-sm btn-outline-secondary ml-2">清空</button>
            </div>
            <div>
                <label class="mr-2"><input type="checkbox" id="autoScrollCheck" checked> 自动滚动</label>
                <select id="logLevelFilter" class="form-control form-control-sm d-inline-block" style="width:auto">
                    <option value="all">所有级别</option>
                    <option value="info">信息</option>
                    <option value="warning">警告</option>
                    <option value="error">错误</option>
                </select>
            </div>
        `;
        logContainer.parentNode.insertBefore(controlDiv, logContainer);

        // 控制事件处理
        document.getElementById('refreshLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '<div class="text-center text-muted my-3">正在加载日志...</div>';
            lastTs = '';
            updateLogs(true);
        });

        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '';
        });

        const autoScrollCheck = document.getElementById('autoScrollCheck');

        // 日志级别过滤
        const logLevelFilter = document.getElementById('logLevelFilter');
        logLevelFilter.addEventListener('change', () => {
            const level = logLevelFilter.value;
            Array.from(logContainer.children).forEach(line => {
                if (level === 'all') {
                    line.style.display = '';
                } else {
                    const logText = line.textContent;
                    line.style.display = logText.includes(`${level.toUpperCase()}`) ? '' : 'none';
                }
            });
        });

        function updateLogs(force = false) {
            fetchLogs(lastTs).then(data=>{
                if (data.success) {
                    // 如果是首次加载且没有日志，显示提示
                    if (lastTs === '' && data.logs.length === 0) {
                        logContainer.innerHTML = '<div class="text-center text-muted my-3">暂无分析日志，可能分析已完成或尚未开始</div>';
                    }

                    // 显示新日志
                    if (data.logs.length > 0) {
                        if (logContainer.children.length === 1 && logContainer.children[0].classList.contains('text-muted')) {
                            logContainer.innerHTML = '';
                        }

                        data.logs.forEach(log => {
                            const line = document.createElement('div');
                            line.className = log.level === 'error' ? 'text-danger' :
                                            log.level === 'warning' ? 'text-warning' : '';

                            // 增加分析进度信息的突出显示
                            let message = log.message || '';
                            if ((log.important) ||
                                (message && (
                                    message.includes('进度更新') ||
                                    message.includes('分析块') ||
                                    message.includes('API调用完成') ||
                                    message.includes('%') ||
                                    message.includes('处理时间') ||
                                    message.includes('令牌使用量') ||
                                    message.includes('费用') ||
                                    message.includes('分析结果') ||
                                    message.includes('完成度') ||
                                    message.includes('耗时')
                                ))) {
                                line.className += ' font-weight-bold text-primary';
                            }

                            line.textContent = `[${log.timestamp}] ${log.level.toUpperCase()} - ${message}`;

                            // 应用日志级别过滤
                            const level = logLevelFilter.value;
                            if (level !== 'all' && !log.level.includes(level)) {
                                line.style.display = 'none';
                            }

                            logContainer.appendChild(line);
                            lastTs = log.timestamp;
                        });

                        // 自动滚动到底部
                        if (autoScrollCheck.checked) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
                    }

                    // 禁用自动刷新
                    console.log('[九猫修复] 已禁用日志自动刷新');
                }
            }).catch(err=>{
                console.error('获取日志失败', err);
                logContainer.innerHTML += `<div class="text-danger">获取日志失败: ${err.message}</div>`;
                // 禁用自动刷新
                console.log('[九猫修复] 已禁用日志错误后的自动刷新');
            });
        }

        // 启动日志更新
        updateLogs();
    }

    // 错误处理函数，防止JS错误导致整个页面失效
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
        return true; // 阻止默认错误处理
    };

    // 主初始化函数
    document.addEventListener('DOMContentLoaded', function() {
        // 启动进度更新
        updateProgressUI();

        // 初始化日志控制
        initLogControls();

        // 初始化大纲分析图表
        try {
            // 尝试初始化图表
            if (window.initOutlineAnalysisChart) {
                window.initOutlineAnalysisChart();
            }
        } catch (error) {
            console.error('初始化图表时出错:', error);
            document.querySelector('.chart-container').innerHTML = '<div class="alert alert-warning">图表加载失败</div>';
        }

        // 重新分析按钮事件
        const reanalyzeBtn = document.querySelector('.reanalyze-btn');
        if (reanalyzeBtn) {
            reanalyzeBtn.addEventListener('click', function() {
                const novelId = this.getAttribute('data-novel-id');
                const dimension = this.getAttribute('data-dimension');

                if (!novelId || !dimension) {
                    console.error('缺少必要的数据属性');
                    return;
                }

                // 禁用按钮防止重复点击
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';

                // 发送重新分析请求
                fetch(`/api/analysis/reanalyze?novel_id=${novelId}&dimension=${dimension}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 成功启动分析，刷新页面
                        window.location.reload();
                    } else {
                        // 显示错误
                        alert('启动分析失败: ' + (data.message || '未知错误'));
                        // 恢复按钮状态
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-sync"></i> 重新分析';
                    }
                })
                .catch(error => {
                    console.error('请求出错:', error);
                    alert('请求出错，请稍后再试');
                    // 恢复按钮状态
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-sync"></i> 重新分析';
                });
            });
        }
    });
</script>

<!-- 页面优化脚本 -->
<script src="{{ url_for('static', filename='js/analysis-page-optimizer.js') }}" data-critical="true"></script>
<!-- 历史分析修复脚本 -->
<script src="{{ url_for('static', filename='js/historical-analysis-fix.js') }}" data-critical="true"></script>
{% endblock %}
