"""
Analysis process data model for the 九猫 (Nine Cats) novel analysis system.
用于记录详细的分析过程，包括每个分块的处理过程和中间结果。
"""
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, Union
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, ForeignKey, <PERSON><PERSON>an
from sqlalchemy.orm import relationship

from src.models.base import Base

def utc_now():
    """返回当前的UTC时间，替代已弃用的datetime.utcnow"""
    return datetime.now(timezone.utc)

class AnalysisProcess(Base):
    """详细分析过程数据模型，记录分析的每个步骤和中间结果。"""

    __tablename__ = "analysis_processes"

    id = Column(Integer, primary_key=True)
    novel_id = Column(Integer, ForeignKey("novels.id"), nullable=False)
    result_id = Column(Integer, ForeignKey("analysis_results.id"), nullable=True)
    dimension = Column(String(255), nullable=False)
    block_index = Column(Integer, nullable=False)  # 分块索引
    total_blocks = Column(Integer, nullable=False)  # 总分块数
    processing_stage = Column(String(255), nullable=False)  # 处理阶段：init, chunk_analysis, combine, finalize
    stage_index = Column(Integer, nullable=False, default=0)  # 阶段内索引
    input_text = Column(Text, nullable=True)  # 输入文本
    output_text = Column(Text, nullable=True)  # 输出文本
    prompt_used = Column(Text, nullable=True)  # 使用的提示词
    api_request = Column(JSON, nullable=True)  # API请求
    api_response = Column(JSON, nullable=True)  # API响应
    processing_time = Column(Integer, nullable=True)  # 处理时间（毫秒）
    tokens_used = Column(Integer, nullable=True)  # 使用的令牌数
    is_successful = Column(Boolean, default=True)  # 是否成功
    error_message = Column(Text, nullable=True)  # 错误信息
    process_metadata = Column(JSON, nullable=True)  # 其他元数据
    created_at = Column(DateTime, default=utc_now)

    # 关系
    novel = relationship("Novel", back_populates="analysis_processes")
    result = relationship("AnalysisResult", back_populates="processes")

    def __init__(
        self,
        novel_id: int,
        dimension: str,
        block_index: int,
        total_blocks: int,
        processing_stage: str,
        stage_index: int = 0,
        result_id: Optional[int] = None,
        input_text: Optional[str] = None,
        output_text: Optional[str] = None,
        prompt_used: Optional[str] = None,
        api_request: Optional[Dict[str, Any]] = None,
        api_response: Optional[Dict[str, Any]] = None,
        processing_time: Optional[int] = None,
        tokens_used: Optional[int] = None,
        is_successful: bool = True,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        初始化分析过程记录。

        Args:
            novel_id: 小说ID
            dimension: 分析维度
            block_index: 分块索引（从0开始）
            total_blocks: 总分块数
            processing_stage: 处理阶段
            stage_index: 阶段内索引
            result_id: 关联的分析结果ID
            input_text: 输入文本
            output_text: 输出文本
            prompt_used: 使用的提示词
            api_request: API请求详情
            api_response: API响应详情
            processing_time: 处理时间（毫秒）
            tokens_used: 使用的令牌数
            is_successful: 是否成功
            error_message: 错误信息
            metadata: 其他元数据
        """
        self.novel_id = novel_id
        self.dimension = dimension
        self.block_index = block_index
        self.total_blocks = total_blocks
        self.processing_stage = processing_stage
        self.stage_index = stage_index
        self.result_id = result_id
        self.input_text = input_text
        self.output_text = output_text
        self.prompt_used = prompt_used
        self.api_request = api_request or {}
        self.api_response = api_response or {}
        self.processing_time = processing_time
        self.tokens_used = tokens_used
        self.is_successful = is_successful
        self.error_message = error_message
        self.process_metadata = metadata or {}

    def to_dict(self) -> Dict[str, Any]:
        """
        将分析过程转换为字典表示。

        Returns:
            分析过程的字典表示。
        """
        return {
            "id": self.id,
            "novel_id": self.novel_id,
            "result_id": self.result_id,
            "dimension": self.dimension,
            "block_index": self.block_index,
            "total_blocks": self.total_blocks,
            "processing_stage": self.processing_stage,
            "stage_index": self.stage_index,
            "input_text_preview": self.get_text_preview(self.input_text, 200) if self.input_text else None,
            "output_text_preview": self.get_text_preview(self.output_text, 200) if self.output_text else None,
            "prompt_used_preview": self.get_text_preview(self.prompt_used, 100) if self.prompt_used else None,
            "processing_time": self.processing_time,
            "tokens_used": self.tokens_used,
            "is_successful": self.is_successful,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "has_api_details": bool(self.api_request or self.api_response),
            "metadata": self.process_metadata
        }

    def get_text_preview(self, text: Optional[str], max_length: int = 100) -> Optional[str]:
        """
        获取文本的预览版本。

        Args:
            text: 要预览的文本
            max_length: 最大长度

        Returns:
            截断的文本预览
        """
        if not text:
            return None
        if len(text) <= max_length:
            return text
        return text[:max_length] + "..."

    def get_stage_name(self) -> str:
        """
        获取阶段的中文名称。

        Returns:
            阶段的中文名称
        """
        stage_names = {
            "init": "初始化",
            "chunk_analysis": "分块分析",
            "combine": "结果合并",
            "finalize": "最终处理",
            "prompt_generation": "提示词生成",
            "api_request": "API请求",
            "api_response": "API响应",
            "error_handling": "错误处理"
        }
        return stage_names.get(self.processing_stage, self.processing_stage) 