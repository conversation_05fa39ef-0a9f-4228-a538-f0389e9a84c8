/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 人物关系分析页面修复脚本
 * 专门修复character_relationships页面的JSON解析错误
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('人物关系分析页面修复脚本已加载');
    
    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复人物关系分析页面');
        
        // 检查是否在人物关系分析页面
        const isCharacterRelationshipsPage = window.location.pathname.includes('character_relationships');
        if (!isCharacterRelationshipsPage) {
            return;
        }
        
        console.log('检测到character_relationships页面，应用特殊修复');
        
        // 修复initCharts函数中的JSON.parse错误
        fixInitChartsFunction();
        
        // 修复分析内容显示
        fixAnalysisContent();
    });
    
    // 修复initCharts函数中的JSON.parse错误
    function fixInitChartsFunction() {
        try {
            // 查找所有内联脚本
            var scripts = document.querySelectorAll('script:not([src])');
            
            scripts.forEach(function(script) {
                var content = script.textContent || '';
                
                // 检查是否包含initCharts函数
                if (content.includes('function initCharts()') && content.includes('JSON.parse(')) {
                    console.log('找到包含initCharts函数的脚本');
                    
                    // 检查是否包含错误的JSON.parse调用
                    if (content.includes('stats_start') && content.includes('JSON.parse("{"error":')) {
                        console.log('找到包含错误JSON.parse调用的initCharts函数');
                        
                        // 修复JSON.parse调用
                        var fixedContent = content.replace(
                            /(JSON\.parse\s*\(\s*["'])(.*?)(["']\s*\))/g,
                            function(match, prefix, jsonStr, suffix) {
                                // 检查是否包含错误信息
                                if (jsonStr.includes('error') && jsonStr.includes('stats_start')) {
                                    console.log('修复initCharts函数中的JSON.parse调用');
                                    // 替换为有效的JSON对象
                                    return 'JSON.parse(\'{"error": "name stats_start is not defined", "formatted_error": true}\')';
                                }
                                return match;
                            }
                        );
                        
                        // 如果内容被修改，替换脚本
                        if (fixedContent !== content) {
                            console.log('替换修复后的initCharts函数');
                            var newScript = document.createElement('script');
                            newScript.textContent = fixedContent;
                            script.parentNode.replaceChild(newScript, script);
                        }
                    }
                }
            });
        } catch (e) {
            console.error('修复initCharts函数时出错:', e.message);
        }
    }
    
    // 修复分析内容显示
    function fixAnalysisContent() {
        try {
            // 查找分析内容元素
            var analysisContentElement = document.querySelector('.analysis-content');
            if (!analysisContentElement) {
                console.log('未找到分析内容元素，跳过修复');
                return;
            }
            
            var content = analysisContentElement.textContent || '';
            if (!content) {
                console.log('分析内容为空，跳过修复');
                return;
            }
            
            // 检查是否包含错误信息
            if (content.includes('character_relationships** 时遇到了问题')) {
                console.log('找到包含错误信息的分析内容');
                
                // 检查是否是JSON格式
                if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
                    try {
                        // 尝试解析JSON
                        var jsonData = JSON.parse(content);
                        console.log('分析内容已经是有效的JSON');
                    } catch (e) {
                        console.error('解析分析内容为JSON失败:', e.message);
                        
                        // 检查是否是未终止的字符串错误
                        if (e.message.includes('Unterminated string')) {
                            console.log('检测到未终止的字符串错误，尝试修复');
                            
                            // 找到错误位置
                            var errorPos = -1;
                            var match = e.message.match(/position (\d+)/);
                            if (match) {
                                errorPos = parseInt(match[1]);
                            } else {
                                // 如果没有明确的位置，使用已知的错误位置
                                errorPos = 4475;
                            }
                            
                            console.log('尝试修复分析内容中的未终止字符串错误，位置:', errorPos);
                            var fixedContent = content.substring(0, errorPos) + '"' + content.substring(errorPos);
                            
                            try {
                                // 尝试解析修复后的内容
                                JSON.parse(fixedContent);
                                console.log('成功修复分析内容中的未终止字符串错误');
                                
                                // 更新元素内容
                                analysisContentElement.textContent = fixedContent;
                            } catch (e2) {
                                console.error('修复分析内容中的未终止字符串错误失败:', e2.message);
                            }
                        }
                    }
                }
            }
        } catch (e) {
            console.error('修复分析内容时出错:', e.message);
        }
    }
})();
