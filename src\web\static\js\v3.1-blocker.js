/**
 * 九猫小说分析写作系统 - v3.1脚本阻止器
 * 
 * 此脚本用于阻止v3.1版本的脚本加载，避免版本冲突
 * 版本: 1.0.0
 */

(function() {
    console.log('[v3.1阻止器] 脚本加载中...');

    // 保存原始的createElement方法
    const originalCreateElement = document.createElement;

    // 重写createElement方法
    document.createElement = function(tagName) {
        // 调用原始方法创建元素
        const element = originalCreateElement.call(document, tagName);

        // 如果是script元素，重写其src属性的setter
        if (tagName.toLowerCase() === 'script') {
            // 保存原始的src属性描述符
            const originalSrcDescriptor = Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype, 'src');
            
            // 定义新的src属性
            Object.defineProperty(element, 'src', {
                set: function(value) {
                    // 检查是否是v3.1的脚本
                    if (typeof value === 'string' && (
                        value.includes('/v3.1/') || 
                        value.includes('v3.1') ||
                        value.includes('/v3_1/') ||
                        value.includes('v3_1')
                    )) {
                        console.warn(`[v3.1阻止器] 阻止加载v3.1脚本: ${value}`);
                        // 不加载v3.1脚本
                        return;
                    }
                    
                    // 对于其他脚本，正常设置src
                    originalSrcDescriptor.set.call(this, value);
                },
                get: function() {
                    return originalSrcDescriptor.get.call(this);
                },
                configurable: true
            });
        }
        
        return element;
    };

    // 保存原始的XMLHttpRequest.open方法
    const originalXhrOpen = XMLHttpRequest.prototype.open;

    // 重写XMLHttpRequest.open方法
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        // 检查是否是v3.1的请求
        if (typeof url === 'string' && (
            url.includes('/v3.1/') || 
            url.includes('v3.1') ||
            url.includes('/v3_1/') ||
            url.includes('v3_1')
        )) {
            console.warn(`[v3.1阻止器] 阻止v3.1 API请求: ${url}`);
            
            // 修改URL为v3.0的对应路径
            url = url.replace('/v3.1/', '/v3/')
                     .replace('v3.1', 'v3')
                     .replace('/v3_1/', '/v3/')
                     .replace('v3_1', 'v3');
                     
            console.log(`[v3.1阻止器] 重定向到v3.0路径: ${url}`);
        }
        
        // 调用原始方法
        return originalXhrOpen.call(this, method, url, async, user, password);
    };

    // 保存原始的fetch方法
    const originalFetch = window.fetch;

    // 重写fetch方法
    window.fetch = function(url, options) {
        // 检查是否是v3.1的请求
        if (typeof url === 'string' && (
            url.includes('/v3.1/') || 
            url.includes('v3.1') ||
            url.includes('/v3_1/') ||
            url.includes('v3_1')
        )) {
            console.warn(`[v3.1阻止器] 阻止v3.1 fetch请求: ${url}`);
            
            // 修改URL为v3.0的对应路径
            url = url.replace('/v3.1/', '/v3/')
                     .replace('v3.1', 'v3')
                     .replace('/v3_1/', '/v3/')
                     .replace('v3_1', 'v3');
                     
            console.log(`[v3.1阻止器] 重定向到v3.0路径: ${url}`);
        }
        
        // 调用原始方法
        return originalFetch.call(window, url, options);
    };

    // 如果jQuery已加载，重写$.ajax方法
    if (typeof $ !== 'undefined' && typeof $.ajax === 'function') {
        // 保存原始的$.ajax方法
        const originalAjax = $.ajax;
        
        // 重写$.ajax方法
        $.ajax = function(options) {
            if (typeof options === 'string') {
                // 如果options是字符串，则它是URL
                if (options.includes('/v3.1/') || 
                    options.includes('v3.1') ||
                    options.includes('/v3_1/') ||
                    options.includes('v3_1')) {
                    
                    console.warn(`[v3.1阻止器] 阻止v3.1 $.ajax请求: ${options}`);
                    
                    // 修改URL为v3.0的对应路径
                    options = options.replace('/v3.1/', '/v3/')
                                     .replace('v3.1', 'v3')
                                     .replace('/v3_1/', '/v3/')
                                     .replace('v3_1', 'v3');
                                     
                    console.log(`[v3.1阻止器] 重定向到v3.0路径: ${options}`);
                }
            } else if (options && options.url) {
                // 如果options是对象，则修改url属性
                if (typeof options.url === 'string' && (
                    options.url.includes('/v3.1/') || 
                    options.url.includes('v3.1') ||
                    options.url.includes('/v3_1/') ||
                    options.url.includes('v3_1')
                )) {
                    console.warn(`[v3.1阻止器] 阻止v3.1 $.ajax请求: ${options.url}`);
                    
                    // 修改URL为v3.0的对应路径
                    options.url = options.url.replace('/v3.1/', '/v3/')
                                            .replace('v3.1', 'v3')
                                            .replace('/v3_1/', '/v3/')
                                            .replace('v3_1', 'v3');
                                            
                    console.log(`[v3.1阻止器] 重定向到v3.0路径: ${options.url}`);
                }
            }
            
            // 调用原始方法
            return originalAjax.call($, options);
        };
    }

    console.log('[v3.1阻止器] 脚本加载完成，已阻止v3.1脚本和API请求');
})();
