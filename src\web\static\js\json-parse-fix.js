/**
 * 九猫 - 专门修复JSON.parse错误的脚本
 * 这个脚本会在页面加载时立即执行，修复特定的JSON.parse错误
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('JSON.parse修复脚本已加载');
    
    // 保存原始的JSON.parse方法
    var originalJSONParse = JSON.parse;
    
    // 重写JSON.parse方法
    JSON.parse = function(text, reviver) {
        try {
            // 尝试使用原始方法解析
            return originalJSONParse(text, reviver);
        } catch (e) {
            console.error('JSON.parse错误:', e.message);
            
            // 特别处理"missing ) after argument list"错误
            if (e.message.includes('missing ) after argument list')) {
                console.log('检测到缺少右括号错误，尝试修复');
                
                // 尝试添加右括号并重新解析
                try {
                    var fixedText = text + ')';
                    console.log('修复后尝试解析');
                    return originalJSONParse(fixedText, reviver);
                } catch (e2) {
                    console.error('第一次修复尝试失败:', e2.message);
                    
                    // 尝试其他修复方法
                    try {
                        // 检查是否是JSON字符串中缺少右括号
                        if (text.includes('JSON.parse(')) {
                            var fixedText2 = text.replace(/JSON\.parse\((['"])(.+?)(?:\1\s*\))?$/, function(match, quote, content) {
                                return 'JSON.parse(' + quote + content + quote + ')';
                            });
                            console.log('尝试修复JSON.parse调用');
                            return eval(fixedText2); // 使用eval执行修复后的代码
                        }
                    } catch (e3) {
                        console.error('第二次修复尝试失败:', e3.message);
                    }
                }
            }
            
            // 如果所有修复尝试都失败，重新抛出原始错误
            throw e;
        }
    };
    
    // 在页面加载完成后扫描并修复所有脚本
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始扫描页面中的JSON.parse调用');
        
        // 查找所有脚本标签
        var scripts = document.querySelectorAll('script');
        
        scripts.forEach(function(script) {
            if (script.textContent && script.textContent.includes('JSON.parse(')) {
                console.log('找到包含JSON.parse的脚本');
                
                // 修复脚本内容
                var fixedContent = script.textContent.replace(/JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g, function(match, quote, content) {
                    if (!match.endsWith(')')) {
                        console.log('修复缺少右括号的JSON.parse调用');
                        return 'JSON.parse(' + quote + content + quote + ')';
                    }
                    return match;
                });
                
                // 如果内容被修改，替换脚本
                if (fixedContent !== script.textContent) {
                    console.log('替换修复后的脚本');
                    var newScript = document.createElement('script');
                    newScript.textContent = fixedContent;
                    script.parentNode.replaceChild(newScript, script);
                }
            }
        });
        
        // 特别处理novel_4.html中的问题
        var novelScripts = document.querySelectorAll('script:not([src])');
        novelScripts.forEach(function(script) {
            if (script.textContent && script.textContent.includes('serverData = JSON.parse(')) {
                console.log('找到novel_4.html中的JSON.parse调用');
                
                // 修复特定的JSON.parse调用
                var fixedContent = script.textContent.replace(
                    /serverData\s*=\s*JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g, 
                    function(match, quote, content) {
                        if (!match.endsWith(')')) {
                            console.log('修复novel_4.html中缺少右括号的JSON.parse调用');
                            return 'serverData = JSON.parse(' + quote + content + quote + ')';
                        }
                        return match;
                    }
                );
                
                // 如果内容被修改，替换脚本
                if (fixedContent !== script.textContent) {
                    console.log('替换novel_4.html中修复后的脚本');
                    var newScript = document.createElement('script');
                    newScript.textContent = fixedContent;
                    script.parentNode.replaceChild(newScript, script);
                }
            }
        });
    });
    
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && event.error.message.includes('missing ) after argument list')) {
            console.error('捕获到缺少右括号错误:', event.error.message);
            console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
})();
