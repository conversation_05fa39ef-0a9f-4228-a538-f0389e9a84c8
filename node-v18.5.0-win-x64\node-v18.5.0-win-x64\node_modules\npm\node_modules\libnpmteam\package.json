{"name": "libnpmteam", "description": "npm Team management APIs", "version": "4.0.3", "author": "GitHub Inc.", "license": "ISC", "main": "lib/index.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "test": "tap", "posttest": "npm run lint", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "nock": "^13.2.4", "tap": "^16.0.1"}, "repository": {"type": "git", "url": "https://github.com/npm/cli.git", "directory": "workspaces/libnpmteam"}, "files": ["bin/", "lib/"], "homepage": "https://npmjs.com/package/libnpmteam", "dependencies": {"aproba": "^2.0.0", "npm-registry-fetch": "^13.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}}