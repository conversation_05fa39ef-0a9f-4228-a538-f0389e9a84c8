{% extends "v3.5/base.html" %}

{% block title %}首页 - 九猫小说分析写作系统v3.5{% endblock %}

{% block content %}
<div class="row">
    <!-- 主要内容区域 -->
    <div class="col-lg-8">
        <!-- 欢迎卡片 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-cat fa-3x text-primary me-3"></i>
                    <h2 class="card-title mb-0">欢迎使用九猫小说分析写作系统v3.5</h2>
                </div>
                <p class="lead">九猫是一款专为作家和编辑设计的智能小说分析写作工具，基于先进的AI技术，提供全方位的文本分析和写作辅助服务。</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-start mt-4">
                    <a href="{{ url_for('v3.upload_novel') }}" class="btn btn-primary btn-lg px-4 me-md-2">
                        <i class="fas fa-upload me-2"></i>开始分析
                    </a>
                    <a href="{{ url_for('v3_5.console') }}" class="btn btn-success btn-lg px-4 me-md-2">
                        <i class="fas fa-terminal me-2"></i>进入控制台
                    </a>
                    <a href="{{ url_for('v3_5.text_comparison') }}" class="btn btn-info btn-lg px-4 text-white">
                        <i class="fas fa-exchange-alt me-2"></i>原文对比
                    </a>
                </div>
            </div>
        </div>

        <!-- 功能特点 -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-star me-2"></i>系统特点</h4>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-chart-line fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5>全方位分析</h5>
                                <p>支持15个维度的深度分析，包括语言风格、结构分析、人物关系等。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-brain fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5>AI驱动</h5>
                                <p>采用阿里云DeepSeekR1 API，支持300万字的超长文本分析。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-pen-fancy fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5>自动写作</h5>
                                <p>基于参考蓝本生成符合特定风格和结构的内容。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exchange-alt fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5>原文对比</h5>
                                <p>全新功能！自动对比生成内容与原文，高亮显示相似部分。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近分析的小说 -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-book me-2"></i>最近分析的小说</h4>
            </div>
            <div class="card-body">
                {% if novels %}
                <div class="list-group">
                    {% for novel in novels %}
                    <a href="{{ url_for('v3.view_novel', novel_id=novel.id) }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ novel.title }}</h5>
                            <small>{{ novel.created_at.strftime('%Y-%m-%d') }}</small>
                        </div>
                        <p class="mb-1">{{ novel.author or '未知作者' }} | {{ novel.word_count or 0 }}字</p>
                    </a>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                    <p>暂无分析记录，立即上传小说开始分析！</p>
                    <a href="{{ url_for('v3.upload_novel') }}" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>上传小说
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 系统状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>系统状态</h4>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-server fa-2x text-primary"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-0">系统版本</h5>
                        <p class="mb-0">v3.5 (2024)</p>
                    </div>
                </div>
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-database fa-2x text-primary"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-0">内容统计</h5>
                        <p class="mb-0">
                            小说: {{ generated_content_stats.novel_count or 0 }} |
                            分析: {{ generated_content_stats.analysis_count or 0 }}
                        </p>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-brain fa-2x text-primary"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-0">API状态</h5>
                        <p class="mb-0">DeepSeekR1 API 正常</p>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('v3_5.system_monitor') }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-2"></i>查看详情
                    </a>
                </div>
            </div>
        </div>

        <!-- 参考蓝本 -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-bookmark me-2"></i>参考蓝本</h4>
            </div>
            <div class="card-body">
                {% if reference_templates %}
                <div class="list-group">
                    {% for template in reference_templates[:3] %}
                    <a href="{{ url_for('v3_5.console') }}?template={{ template.id }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ template.title }}</h5>
                            <small>{{ template.created_at.strftime('%Y-%m-%d') }}</small>
                        </div>
                        <p class="mb-1">{{ template.description or '无描述' }}</p>
                    </a>
                    {% endfor %}
                </div>
                {% if reference_templates|length > 3 %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('v3_5.console') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>查看全部
                    </a>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-bookmark fa-3x text-muted mb-3"></i>
                    <p>暂无参考蓝本，分析小说后可设为参考蓝本。</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 新功能介绍 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-star me-2"></i>v3.5新功能</h4>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5><i class="fas fa-exchange-alt text-primary me-2"></i>原文对比</h5>
                    <p>自动对比生成内容与原文，高亮显示重叠或相似的内容，帮助您避免抄袭风险。</p>
                    <a href="{{ url_for('v3_5.text_comparison') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-arrow-right me-1"></i>立即体验
                    </a>
                </div>
                <div class="mb-3">
                    <h5><i class="fas fa-palette text-primary me-2"></i>全新界面</h5>
                    <p>采用淡雅配色和优化布局，提供更舒适的使用体验。支持亮色/暗色主题切换。</p>
                </div>
                <div>
                    <h5><i class="fas fa-tachometer-alt text-primary me-2"></i>性能优化</h5>
                    <p>优化系统性能，提高分析速度，支持更大规模的文本分析。</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
