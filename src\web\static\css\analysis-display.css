/**
 * 九猫分析系统 - 分析结果显示样式
 */

/* 分析状态卡片 */
.analysis-status-card {
    background-color: #fffbf0; /* 浅米黄色 */
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.analysis-status-card .card-header {
    background-color: #fff8e1; /* 更浅的米黄色 */
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    padding: 15px 20px;
}

.analysis-status-card .card-body {
    padding: 20px;
}

.analysis-status-badge {
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 20px;
}

/* 分析卡片 */
.analysis-card {
    background-color: #ffffff; /* 淡白色 */
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.analysis-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.analysis-card .card-header {
    background-color: #fffbf0; /* 浅米黄色 */
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    padding: 12px 15px;
}

.analysis-card .card-body {
    padding: 15px;
}

.analysis-card .card-title {
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 600;
    color: #5d4037; /* 深棕色 */
}

.analysis-excerpt {
    font-size: 14px;
    color: #666;
    max-height: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

/* 分析结果页面 */
.analysis-result-card {
    background-color: #ffffff; /* 淡白色 */
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.analysis-result-card .card-header {
    background-color: #fffbf0; /* 浅米黄色 */
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.analysis-result-card .card-body {
    padding: 20px;
}

.markdown-content {
    font-size: 15px;
    line-height: 1.6;
    color: #333;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 1.5em;
    margin-bottom: 0.75em;
    color: #5d4037; /* 深棕色 */
}

.markdown-content h1 {
    font-size: 1.8em;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.3em;
}

.markdown-content h2 {
    font-size: 1.6em;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.3em;
}

.markdown-content h3 {
    font-size: 1.4em;
}

.markdown-content h4 {
    font-size: 1.2em;
}

.markdown-content p {
    margin-bottom: 1em;
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 1em;
    padding-left: 2em;
}

.markdown-content blockquote {
    padding: 0.5em 1em;
    margin-bottom: 1em;
    border-left: 4px solid #ffe0b2; /* 浅橙色 */
    background-color: #fff8e1; /* 更浅的米黄色 */
    color: #5d4037; /* 深棕色 */
}

.markdown-content code {
    background-color: #f5f5f5;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 0.9em;
}

.markdown-content pre {
    background-color: #f5f5f5;
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
    margin-bottom: 1em;
}

.markdown-content pre code {
    background-color: transparent;
    padding: 0;
}

.markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1em;
}

.markdown-content table th,
.markdown-content table td {
    border: 1px solid #ddd;
    padding: 8px 12px;
}

.markdown-content table th {
    background-color: #fffbf0; /* 浅米黄色 */
    text-align: left;
    font-weight: 600;
}

.markdown-content table tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* 加载指示器 */
#loadingIndicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #ffb74d; /* 橙色 */
    z-index: 9999;
    animation: loading 2s infinite;
    display: none;
}

@keyframes loading {
    0% {
        width: 0;
    }
    50% {
        width: 50%;
    }
    100% {
        width: 100%;
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .analysis-card .card-header {
        padding: 10px 15px;
    }
    
    .analysis-card .card-body {
        padding: 12px;
    }
    
    .analysis-result-card .card-header {
        padding: 12px 15px;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .analysis-result-card .card-header .btn-group {
        margin-top: 10px;
        align-self: flex-end;
    }
    
    .analysis-result-card .card-body {
        padding: 15px;
    }
    
    .markdown-content {
        font-size: 14px;
    }
}
