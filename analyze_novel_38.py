import requests
import json
import time
import sys

# 配置
NOVEL_ID = 38
BASE_URL = "http://localhost:5001"
DIMENSIONS = [
    "rhythm_pacing",
    "perspective_shifts",
    "paragraph_flow", 
    "novel_characteristics",
    "world_building",
    "chapter_outline",
    "character_relationships",
    "opening_effectiveness",
    "climax_pacing"
]

def analyze_dimension(novel_id, dimension):
    """为指定小说的指定维度启动分析"""
    url = f"{BASE_URL}/novel/{novel_id}/analyze"
    data = {
        "dimensions": [dimension],
        "model": "deepseek-r1",
        "parallel_analysis": True,
        "use_cache": True
    }
    
    try:
        print(f"开始分析维度: {dimension}")
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"分析请求成功: {result}")
            return True
        else:
            print(f"分析请求失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"分析请求异常: {str(e)}")
        return False

def check_analysis_status(novel_id, dimension):
    """检查指定维度的分析状态"""
    url = f"{BASE_URL}/api/analysis/result/{novel_id}/{dimension}"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"维度 {dimension} 分析已完成")
                return True
            else:
                print(f"维度 {dimension} 分析未完成: {result.get('message', '未知原因')}")
                return False
        else:
            print(f"检查状态失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"检查状态异常: {str(e)}")
        return False

def main():
    """主函数"""
    print(f"开始为小说 {NOVEL_ID} 执行分析")
    
    # 如果指定了维度，只分析该维度
    if len(sys.argv) > 1:
        dimensions_to_analyze = [sys.argv[1]]
    else:
        dimensions_to_analyze = DIMENSIONS
    
    for dimension in dimensions_to_analyze:
        # 检查是否已经分析过
        if check_analysis_status(NOVEL_ID, dimension):
            print(f"维度 {dimension} 已经分析过，跳过")
            continue
        
        # 启动分析
        success = analyze_dimension(NOVEL_ID, dimension)
        if success:
            print(f"维度 {dimension} 分析请求已发送")
        else:
            print(f"维度 {dimension} 分析请求失败")
        
        # 等待一段时间再发送下一个请求
        time.sleep(2)
    
    print("所有分析请求已发送")

if __name__ == "__main__":
    main()
