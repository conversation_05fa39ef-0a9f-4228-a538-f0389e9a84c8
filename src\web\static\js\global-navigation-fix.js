/**
 * 九猫 - 全局导航修复脚本
 * 解决所有页面的导航和跳转问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._globalNavigationFixLoaded) {
        console.log('[导航修复] 全局导航修复脚本已加载，避免重复执行');
        return;
    }

    // 标记脚本已加载
    window._globalNavigationFixLoaded = true;

    console.log('[导航修复] 全局导航修复脚本已加载 - 版本1.0.0');

    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalFetch = window.fetch;
    const originalOpen = window.open;
    const originalAssign = window.location.assign;
    const originalReplace = window.location.replace;

    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }

    function safeError(message) {
        try {
            originalConsoleError.call(console, '[导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }

    // 初始化函数
    function initialize() {
        safeLog('初始化全局导航修复');

        // 修复所有链接
        fixAllLinks();

        // 修复所有表单
        fixAllForms();

        // 修复所有按钮
        fixAllButtons();

        // 修复导航栏
        fixNavigation();

        // 修复面包屑导航
        fixBreadcrumbs();

        // 修复API请求
        fixApiRequests();

        // 监听DOM变化，修复新添加的元素
        observeDOMChanges();

        // 添加页面可见性变化事件处理
        handleVisibilityChange();

        safeLog('全局导航修复完成');
    }

    // 修复所有链接
    function fixAllLinks() {
        safeLog('修复所有链接');

        // 修复所有a标签
        const links = document.querySelectorAll('a');
        safeLog('找到 ' + links.length + ' 个链接');

        links.forEach(link => {
            fixLink(link);
        });
    }

    // 修复链接
    function fixLink(link) {
        try {
            // 检查是否有href属性
            if (!link.hasAttribute('href')) {
                return;
            }

            const href = link.getAttribute('href');

            // 跳过外部链接和锚点链接
            if (href.startsWith('http') || href.startsWith('#') || href.startsWith('javascript:')) {
                return;
            }

            // 检查是否是相对路径
            if (href.startsWith('/')) {
                // 检查是否是章节分析链接
                if (href.includes('/chapters') || href.includes('/chapter/')) {
                    safeLog('找到章节分析链接: ' + href);

                    // 添加点击事件，确保正确跳转
                    link.addEventListener('click', function(e) {
                        safeLog('点击章节分析链接: ' + href);
                        // 不阻止默认行为，让链接正常跳转
                    });
                }

                // 检查是否是维度分析链接
                else if (href.includes('/analysis/')) {
                    safeLog('找到维度分析链接: ' + href);

                    // 添加点击事件，确保正确跳转
                    link.addEventListener('click', function(e) {
                        safeLog('点击维度分析链接: ' + href);
                        // 不阻止默认行为，让链接正常跳转
                    });
                }

                // 检查是否是小说详情链接
                else if (href.match(/^\/novel\/\d+\/?$/)) {
                    safeLog('找到小说详情链接: ' + href);

                    // 添加点击事件，确保正确跳转
                    link.addEventListener('click', function(e) {
                        safeLog('点击小说详情链接: ' + href);
                        // 不阻止默认行为，让链接正常跳转
                    });
                }

                // 确保链接不会被其他脚本拦截
                if (!link.__navigationFixed) {
                    link.__navigationFixed = true;

                    // 保存原始的onclick处理函数
                    const originalOnClick = link.onclick;

                    // 设置新的onclick处理函数
                    link.onclick = function(e) {
                        // 如果是特殊链接，确保正确跳转
                        if (href.includes('/novel/') ||
                            href.includes('/chapters') ||
                            href.includes('/chapter/') ||
                            href.includes('/analysis/')) {

                            // 阻止事件冒泡，防止其他处理函数干扰
                            e.stopPropagation();

                            // 记录跳转
                            safeLog('确保链接正确跳转: ' + href);

                            // 直接设置location.href，确保跳转
                            setTimeout(function() {
                                window.location.href = href;
                            }, 0);

                            return false;
                        }

                        // 对于其他链接，调用原始的onclick处理函数
                        if (typeof originalOnClick === 'function') {
                            return originalOnClick.call(this, e);
                        }
                    };
                }
            }
        } catch (e) {
            safeError('修复链接时出错: ' + e.message);
        }
    }

    // 修复所有表单
    function fixAllForms() {
        safeLog('修复所有表单');

        // 修复所有form标签
        const forms = document.querySelectorAll('form');
        safeLog('找到 ' + forms.length + ' 个表单');

        forms.forEach(form => {
            fixForm(form);
        });
    }

    // 修复表单
    function fixForm(form) {
        try {
            // 检查是否有action属性
            if (!form.hasAttribute('action')) {
                return;
            }

            const action = form.getAttribute('action');

            // 跳过外部链接
            if (action.startsWith('http')) {
                return;
            }

            // 检查是否是相对路径
            if (action.startsWith('/')) {
                // 检查是否是分析表单
                if (action.includes('/analyze') || action.includes('/analysis')) {
                    safeLog('找到分析表单: ' + action);

                    // 添加提交事件，确保正确提交
                    form.addEventListener('submit', function(e) {
                        safeLog('提交分析表单: ' + action);
                    });
                }
            }
        } catch (e) {
            safeError('修复表单时出错: ' + e.message);
        }
    }

    // 修复所有按钮
    function fixAllButtons() {
        safeLog('修复所有按钮');

        // 修复所有button标签
        const buttons = document.querySelectorAll('button');
        safeLog('找到 ' + buttons.length + ' 个按钮');

        buttons.forEach(button => {
            fixButton(button);
        });
    }

    // 修复按钮
    function fixButton(button) {
        try {
            // 检查是否是分析按钮
            if (button.classList.contains('analyze-btn') ||
                button.classList.contains('analyze-single-dimension') ||
                button.classList.contains('reanalyze-btn')) {

                safeLog('找到分析按钮: ' + button.textContent);

                // 添加点击事件，确保正确处理
                button.addEventListener('click', function(e) {
                    safeLog('点击分析按钮: ' + button.textContent);
                });
            }

            // 检查是否是查看按钮
            else if (button.classList.contains('view-btn')) {
                safeLog('找到查看按钮: ' + button.textContent);

                // 添加点击事件，确保正确跳转
                button.addEventListener('click', function(e) {
                    safeLog('点击查看按钮: ' + button.textContent);
                });
            }
        } catch (e) {
            safeError('修复按钮时出错: ' + e.message);
        }
    }

    // 修复导航栏
    function fixNavigation() {
        safeLog('修复导航栏');

        // 修复导航栏链接
        const navLinks = document.querySelectorAll('nav a');
        navLinks.forEach(link => {
            fixLink(link);
        });
    }

    // 修复面包屑导航
    function fixBreadcrumbs() {
        safeLog('修复面包屑导航');

        // 修复面包屑导航链接
        const breadcrumbLinks = document.querySelectorAll('.breadcrumb a');
        breadcrumbLinks.forEach(link => {
            fixLink(link);
        });
    }

    // 修复API请求
    function fixApiRequests() {
        safeLog('修复API请求');

        // 重写fetch方法，拦截API请求
        window.fetch = function(url, options) {
            // 检查是否是API请求
            if (typeof url === 'string' && url.includes('/api/')) {
                safeLog('拦截API请求: ' + url);
            }

            // 调用原始fetch方法
            return originalFetch.apply(this, arguments);
        };
    }

    // 监听DOM变化，修复新添加的元素
    function observeDOMChanges() {
        try {
            // 创建MutationObserver
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // 检查是否有新添加的节点
                    if (mutation.addedNodes.length > 0) {
                        mutation.addedNodes.forEach(function(node) {
                            // 检查是否是元素节点
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                // 修复新添加的链接
                                const links = node.querySelectorAll('a');
                                links.forEach(link => fixLink(link));

                                // 修复新添加的按钮
                                const buttons = node.querySelectorAll('button');
                                buttons.forEach(button => fixButton(button));

                                // 修复新添加的表单
                                const forms = node.querySelectorAll('form');
                                forms.forEach(form => fixForm(form));

                                // 如果节点本身是链接、按钮或表单，也修复它
                                if (node.tagName === 'A') {
                                    fixLink(node);
                                } else if (node.tagName === 'BUTTON') {
                                    fixButton(node);
                                } else if (node.tagName === 'FORM') {
                                    fixForm(node);
                                }
                            }
                        });
                    }
                });
            });

            // 配置观察选项
            const config = { childList: true, subtree: true };

            // 开始观察
            observer.observe(document.body, config);

            safeLog('已开始监听DOM变化');
        } catch (e) {
            safeError('监听DOM变化时出错: ' + e.message);
        }
    }

    // 处理页面可见性变化
    function handleVisibilityChange() {
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，再次修复所有元素
                safeLog('页面变为可见，再次修复所有元素');
                setTimeout(function() {
                    fixAllLinks();
                    fixAllForms();
                    fixAllButtons();
                    fixNavigation();
                    fixBreadcrumbs();
                }, 100);
            }
        });
    }

    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }

    // 导出API，方便调试和手动调用
    window.globalNavigationFix = {
        initialize: initialize,
        fixAllLinks: fixAllLinks,
        fixAllForms: fixAllForms,
        fixAllButtons: fixAllButtons,
        fixNavigation: fixNavigation,
        fixBreadcrumbs: fixBreadcrumbs
    };
})();
