{% extends "base.html" %}

{% block title %}数据中心 - 九猫{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>九猫小说分析系统 - 数据中心</h1>
    
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">系统监控</h5>
                </div>
                <div class="card-body">
                    <p>查看系统运行状态、内存使用情况和性能指标。</p>
                    <a href="{{ url_for('system_monitor.system_monitor') }}" class="btn btn-primary">进入系统监控</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">小说管理</h5>
                </div>
                <div class="card-body">
                    <p>管理已上传的小说，查看分析结果。</p>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">小说列表</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">API监控</h5>
                </div>
                <div class="card-body">
                    <p>查看API调用情况、使用统计和性能指标。</p>
                    <a href="{{ url_for('api_monitor.api_monitor') }}" class="btn btn-primary">API监控</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">系统工具</h5>
                </div>
                <div class="card-body">
                    <p>系统维护和优化工具。</p>
                    <a href="{{ url_for('tools.tools_index') }}" class="btn btn-primary">系统工具</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">上传小说</h5>
                </div>
                <div class="card-body">
                    <p>上传新的小说进行分析。</p>
                    <a href="{{ url_for('upload_novel') }}" class="btn btn-primary">上传小说</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
