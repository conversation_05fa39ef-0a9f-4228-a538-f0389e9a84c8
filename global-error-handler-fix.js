/**
 * 九猫 - 全局错误处理修复脚本
 * 修复全局错误处理相关问题，确保错误信息正确显示和处理
 * 版本: 1.0.1
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('全局错误处理修复脚本已加载');
    
    // 检查是否已应用此修复
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded && window.__nineCatsFixes.loaded['global-error-handler-fix']) {
        console.log('全局错误处理修复脚本已经应用，跳过');
        return;
    }
    
    // 初始化修复标记
    window.__nineCatsFixes = window.__nineCatsFixes || {};
    window.__nineCatsFixes.loaded = window.__nineCatsFixes.loaded || {};
    
    // 保存原始的window.onerror
    const originalOnError = window.onerror;
    
    // 设置新的全局错误处理函数
    window.onerror = function(message, source, lineno, colno, error) {
        // 检查是否是跨域脚本错误
        if (message === "Script error." && !source) {
            console.error('捕获到跨域脚本错误，无法获取详细信息');
            // 调用原始处理函数
            if (typeof originalOnError === 'function') {
                return originalOnError.apply(this, arguments);
            }
            return true;
        }
        
        // 输出错误信息
        console.error(`捕获到JS错误: ${message}`);
        
        // 调用原始处理函数
        if (typeof originalOnError === 'function') {
            return originalOnError.apply(this, arguments);
        }
        
        return true; // 防止默认处理
    };
    
    // 保存原始的window.onunhandledrejection
    const originalOnUnhandledRejection = window.onunhandledrejection;
    
    // 设置新的Promise错误处理函数
    window.onunhandledrejection = function(event) {
        // 获取Promise错误信息
        const reason = event.reason || '未知原因';
        const message = reason.message || String(reason);
        
        // 输出错误信息
        console.error(`未处理的Promise拒绝: ${message}`);
        
        // 调用原始处理函数
        if (typeof originalOnUnhandledRejection === 'function') {
            return originalOnUnhandledRejection.apply(this, arguments);
        }
        
        return true; // 防止默认处理
    };
    
    // 标记修复已加载
    window.__nineCatsFixes.loaded['global-error-handler-fix'] = true;
    
    console.log('全局错误处理修复脚本加载完成');
})();
