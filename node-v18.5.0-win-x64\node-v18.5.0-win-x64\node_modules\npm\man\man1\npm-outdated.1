.TH "NPM\-OUTDATED" "1" "June 2022" "" ""
.SH "NAME"
\fBnpm-outdated\fR \- Check for outdated packages
.SS Synopsis
.P
.RS 2
.nf
npm outdated [[<@scope>/]<pkg> \.\.\.]
.fi
.RE
.SS Description
.P
This command will check the registry to see if any (or, specific) installed
packages are currently outdated\.
.P
By default, only the direct dependencies of the root project and direct
dependencies of your configured \fIworkspaces\fR are shown\.
Use \fB\-\-all\fP to find all outdated meta\-dependencies as well\.
.P
In the output:
.RS 0
.IP \(bu 2
\fBwanted\fP is the maximum version of the package that satisfies the semver
range specified in \fBpackage\.json\fP\|\. If there's no available semver range
(i\.e\.  you're running \fBnpm outdated \-\-global\fP, or the package isn't
included in \fBpackage\.json\fP), then \fBwanted\fP shows the currently\-installed
version\.
.IP \(bu 2
\fBlatest\fP is the version of the package tagged as latest in the registry\.
Running \fBnpm publish\fP with no special configuration will publish the
package with a dist\-tag of \fBlatest\fP\|\. This may or may not be the maximum
version of the package, or the most\-recently published version of the
package, depending on how the package's developer manages the latest
npm help dist\-tag\.
.IP \(bu 2
\fBlocation\fP is where in the physical tree the package is located\.
.IP \(bu 2
\fBdepended by\fP shows which package depends on the displayed dependency
.IP \(bu 2
\fBpackage type\fP (when using \fB\-\-long\fP / \fB\-l\fP) tells you whether this
package is a \fBdependency\fP or a dev/peer/optional dependency\. Packages not
included in \fBpackage\.json\fP are always marked \fBdependencies\fP\|\.
.IP \(bu 2
\fBhomepage\fP (when using \fB\-\-long\fP / \fB\-l\fP) is the \fBhomepage\fP value contained
in the package's packument
.IP \(bu 2
Red means there's a newer version matching your semver requirements, so
you should update now\.
.IP \(bu 2
Yellow indicates that there's a newer version \fIabove\fR your semver
requirements (usually new major, or new 0\.x minor) so proceed with
caution\.

.RE
.SS An example
.P
.RS 2
.nf
$ npm outdated
Package      Current   Wanted   Latest  Location                  Depended by
glob          5\.0\.15   5\.0\.15    6\.0\.1  node_modules/glob         dependent\-package\-name
nothingness    0\.0\.3      git      git  node_modules/nothingness  dependent\-package\-name
npm            3\.5\.1    3\.5\.2    3\.5\.1  node_modules/npm          dependent\-package\-name
local\-dev      0\.0\.3   linked   linked  local\-dev                 dependent\-package\-name
once           1\.3\.2    1\.3\.3    1\.3\.3  node_modules/once         dependent\-package\-name
.fi
.RE
.P
With these \fBdependencies\fP:
.P
.RS 2
.nf
{
  "glob": "^5\.0\.15",
  "nothingness": "github:othiym23/nothingness#master",
  "npm": "^3\.5\.1",
  "once": "^1\.3\.1"
}
.fi
.RE
.P
A few things to note:
.RS 0
.IP \(bu 2
\fBglob\fP requires \fB^5\fP, which prevents npm from installing \fBglob@6\fP, which
is outside the semver range\.
.IP \(bu 2
Git dependencies will always be reinstalled, because of how they're
specified\.  The installed committish might satisfy the dependency
specifier (if it's something immutable, like a commit SHA), or it might
not, so \fBnpm outdated\fP and \fBnpm update\fP have to fetch Git repos to check\.
This is why currently doing a reinstall of a Git dependency always forces
a new clone and install\.
.IP \(bu 2
\fBnpm@3\.5\.2\fP is marked as "wanted", but "latest" is \fBnpm@3\.5\.1\fP because
npm uses dist\-tags to manage its \fBlatest\fP and \fBnext\fP release channels\.
\fBnpm update\fP will install the \fInewest\fR version, but \fBnpm install npm\fP
(with no semver range) will install whatever's tagged as \fBlatest\fP\|\.
.IP \(bu 2
\fBonce\fP is just plain out of date\. Reinstalling \fBnode_modules\fP from
scratch or running \fBnpm update\fP will bring it up to spec\.

.RE
.SS Configuration
.SS \fBall\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
When running \fBnpm outdated\fP and \fBnpm ls\fP, setting \fB\-\-all\fP will show all
outdated or installed packages, rather than only those directly depended
upon by the current project\.
.SS \fBjson\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Whether or not to output JSON data, rather than the normal output\.
.RS 0
.IP \(bu 2
In \fBnpm pkg set\fP it enables parsing set values with JSON\.parse() before
saving them to your \fBpackage\.json\fP\|\.

.RE
.P
Not supported by all npm commands\.
.SS \fBlong\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Show extended information in \fBls\fP, \fBsearch\fP, and \fBhelp\-search\fP\|\.
.SS \fBparseable\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Output parseable results from commands that write to standard output\. For
\fBnpm search\fP, this will be tab\-separated table format\.
.SS \fBglobal\fP
.RS 0
.IP \(bu 2
Default: false
.IP \(bu 2
Type: Boolean

.RE
.P
Operates in "global" mode, so that packages are installed into the \fBprefix\fP
folder instead of the current working directory\. See
npm help folders for more on the differences in behavior\.
.RS 0
.IP \(bu 2
packages are installed into the \fB{prefix}/lib/node_modules\fP folder, instead
of the current working directory\.
.IP \(bu 2
bin files are linked to \fB{prefix}/bin\fP
.IP \(bu 2
man pages are linked to \fB{prefix}/share/man\fP

.RE
.SS \fBworkspace\fP
.RS 0
.IP \(bu 2
Default:
.IP \(bu 2
Type: String (can be set multiple times)

.RE
.P
Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option\.
.P
Valid values for the \fBworkspace\fP config are either:
.RS 0
.IP \(bu 2
Workspace names
.IP \(bu 2
Path to a workspace directory
.IP \(bu 2
Path to a parent workspace directory (will result in selecting all
workspaces within that folder)

.RE
.P
When set for the \fBnpm init\fP command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project\.
.P
This value is not exported to the environment for child processes\.
.SS See Also
.RS 0
.IP \(bu 2
npm help update
.IP \(bu 2
npm help dist\-tag
.IP \(bu 2
npm help registry
.IP \(bu 2
npm help folders
.IP \(bu 2
npm help workspaces

.RE
