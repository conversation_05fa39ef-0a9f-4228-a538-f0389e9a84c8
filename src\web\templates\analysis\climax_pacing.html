{% extends "base.html" %}

{% block title %}{{ novel.title }} - 高潮节奏分析 - 九猫{% endblock %}

{% block extra_css %}
<style>
    .analysis-content {
        white-space: pre-wrap;
        line-height: 1.6;
    }

    .metadata-item {
        margin-bottom: 0.5rem;
    }

    .visualization-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <!-- 进度面板 -->
            <div class="card mb-4">
                <div class="card-header"><h5 class="card-title">分析进度</h5></div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div id="progressBar" class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">100%</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>状态:</strong> <span id="statusText">完成</span></p>
                            <p><strong>块进度:</strong> <span id="blocksProgress">1/1</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>预计剩余时间:</strong> <span id="remainingTime">0秒</span></p>
                            <p><strong>预计完成时间:</strong> <span id="estimatedCompletionTime">{{ result.created_at|default('已完成') }}</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 日志面板 -->
            <div class="card mb-4">
                <div class="card-header"><h5 class="card-title">分析日志</h5></div>
                <div class="card-body" id="logContainer" data-novel-id="{{ novel.id }}" style="background:#f8f9fa; height:200px; overflow-y:auto; font-family: monospace; font-size:0.9rem;"></div>
            </div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
                    <li class="breadcrumb-item active">高潮节奏分析</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center">
                <h1>高潮节奏分析</h1>
                <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
                    返回小说页面
                </a>
            </div>

            <div class="row mt-4">
                <div class="col-md-9">
                    <!-- 推理过程卡片 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title">推理过程</h5>
                        </div>
                        <div class="card-body">
                            <div class="reasoning-content">
                                {% if result.reasoning_content %}
                                <div class="reasoning-text">
                                    <pre>{{ result.reasoning_content }}</pre>
                                </div>
                                {% else %}
                                <div class="alert alert-info">
                                    暂无推理过程数据
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- 分析结果卡片 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">分析结果</h5>
                        </div>
                        <div class="card-body">
                            <div class="analysis-content markdown-content">
                                {{ result.content|safe }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title">小说信息</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>标题：</strong> {{ novel.title }}</p>
                            <p><strong>作者：</strong> {{ novel.author|default('未知') }}</p>
                            <p><strong>字数：</strong> {{ novel.word_count }}</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">分析元数据</h5>
                        </div>
                        <div class="card-body">
                            <div class="metadata-item">
                                <strong>分析时间：</strong>
                                <span>{{ result.created_at }}</span>
                            </div>

                            <!-- 元数据已禁用以避免序列化错误 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 通用图表修复增强脚本 -->
<script src="{{ url_for('static', filename='js/universal-chart-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/universal-chart-fix-enhanced.js';"></script>

<script>
    // 全局变量
    const novelId = "{{ novel.id }}";
    const dimension = "{{ result.dimension }}";

    // 错误处理函数，防止JS错误导致整个页面失效
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
        return true; // 阻止默认错误处理
    };

    // 实时进度更新函数
    function fetchAnalysisProgress() {
        return fetch(`/api/analysis/progress?novel_id=${novelId}`)
            .then(res => res.json())
            .then(data => {
                if (data.success && data.progress) {
                    // 添加额外检查，确保dimension存在，如果不存在则返回默认值
                    if (!data.progress[dimension]) {
                        console.warn(`维度 ${dimension} 的进度数据不存在，使用默认值`);
                        return {
                            progress: {
                                progress: 100,
                                status: '完成',
                                blocks_progress: '1/1',
                                remaining_time: '0秒',
                                eta: '已完成'
                            },
                            isRunning: false
                        };
                    }
                    return {
                        progress: data.progress[dimension],
                        isRunning: data.is_running
                    };
                }
                // 如果没有成功获取数据，返回默认值
                console.warn('无法获取进度数据，使用默认值');
                return {
                    progress: {
                        progress: 100,
                        status: '完成',
                        blocks_progress: '1/1',
                        remaining_time: '0秒',
                        eta: '已完成'
                    },
                    isRunning: false
                };
            });
    }

    function updateProgressUI() {
        fetchAnalysisProgress()
            .then(data => {
                // 确保数据存在
                const progressData = data.progress || {
                    progress: 100,
                    status: '完成',
                    blocks_progress: '1/1',
                    remaining_time: '0秒',
                    eta: '已完成'
                };
                const isRunning = data.isRunning || false;

                // 更新进度条
                const progress = progressData.progress || 0;
                const progressBar = document.getElementById('progressBar');
                if (progressBar) {
                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                    progressBar.textContent = `${progress}%`;
                }

                // 更新状态文本
                const statusText = document.getElementById('statusText');
                if (statusText) {
                    statusText.textContent = progressData.status || '完成';
                }

                // 设置进度条颜色
                if (progressBar) {
                    if (progress >= 100) {
                        progressBar.className = 'progress-bar bg-success';
                    } else if (progress >= 75) {
                        progressBar.className = 'progress-bar bg-info';
                    } else if (progress >= 50) {
                        progressBar.className = 'progress-bar bg-primary';
                    } else if (progress >= 25) {
                        progressBar.className = 'progress-bar bg-warning';
                    }
                }

                // 更新块进度
                const blocksProgress = document.getElementById('blocksProgress');
                if (blocksProgress && progressData.blocks_progress) {
                    blocksProgress.textContent = progressData.blocks_progress;
                }

                // 更新时间信息
                const remainingTime = document.getElementById('remainingTime');
                if (remainingTime && progressData.remaining_time) {
                    remainingTime.textContent = progressData.remaining_time;
                }

                const estimatedCompletionTime = document.getElementById('estimatedCompletionTime');
                if (estimatedCompletionTime && progressData.eta) {
                    estimatedCompletionTime.textContent = progressData.eta;
                }

                // 如果仍在分析，继续轮询
                if (isRunning && progress < 100 && progress >= 0) {
                    setTimeout(updateProgressUI, 5000);
                }
            })
            .catch(err => {
                console.error('获取进度信息失败:', err);
                // 出错时设置为默认值
                const progressBar = document.getElementById('progressBar');
                if (progressBar) {
                    progressBar.style.width = '100%';
                    progressBar.setAttribute('aria-valuenow', 100);
                    progressBar.textContent = '100%';
                    progressBar.className = 'progress-bar bg-success';
                }

                const statusText = document.getElementById('statusText');
                if (statusText) {
                    statusText.textContent = '完成';
                }

                const blocksProgress = document.getElementById('blocksProgress');
                if (blocksProgress) {
                    blocksProgress.textContent = '1/1';
                }

                const remainingTime = document.getElementById('remainingTime');
                if (remainingTime) {
                    remainingTime.textContent = '0秒';
                }

                const estimatedCompletionTime = document.getElementById('estimatedCompletionTime');
                if (estimatedCompletionTime) {
                    estimatedCompletionTime.textContent = '已完成';
                }

                // 即使出错也继续尝试轮询，但间隔时间更长
                setTimeout(updateProgressUI, 10000);
            });
    }

    // 加载Chart.js库
    function loadChartJS() {
        return new Promise((resolve, reject) => {
            // 检查Chart是否已经加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载');
                resolve();
                return;
            }

            // 尝试加载本地Chart.js
            var script = document.createElement('script');
            script.src = "{{ url_for('static', filename='js/lib/chart.min.js') }}";

            script.onload = function() {
                console.log('成功从本地加载Chart.js');
                resolve();
            };

            script.onerror = function() {
                // 如果本地加载失败，尝试备用路径
                console.warn('本地Chart.js加载失败，尝试备用路径');
                var backupScript = document.createElement('script');
                backupScript.src = "/direct-static/js/lib/chart.min.js";

                backupScript.onload = function() {
                    console.log('成功从备用路径加载Chart.js');
                    resolve();
                };

                backupScript.onerror = function() {
                    // 如果备用路径也失败，尝试CDN
                    console.warn('备用路径Chart.js加载失败，尝试CDN');
                    var cdnScript = document.createElement('script');
                    cdnScript.src = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";

                    cdnScript.onload = function() {
                        console.log('成功从CDN加载Chart.js');
                        resolve();
                    };

                    cdnScript.onerror = function() {
                        console.error('所有Chart.js加载尝试均失败');
                        reject(new Error('无法加载Chart.js'));
                    };

                    document.head.appendChild(cdnScript);
                };

                document.head.appendChild(backupScript);
            };

            document.head.appendChild(script);
        });
    }

    // 初始化图表
    function initCharts() {
        // 元数据已禁用以避免序列化错误
        let resultMetadata = {};

        // 保存到全局变量，供通用修复脚本使用
        window.resultMetadata = resultMetadata;
        console.log('分析结果元数据:', resultMetadata);

        // 从分析结果中提取数据 - 雷达图
        let radarLabels = ['高潮铺垫', '紧张感营造', '情节转折', '角色反应', '节奏控制', '情感渲染'];
        let radarData = [85, 80, 90, 75, 85, 70];

        // 从元数据中获取真实数据
        if (resultMetadata && resultMetadata.visualization_data && resultMetadata.visualization_data.radar) {
            const vizData = resultMetadata.visualization_data.radar;
            if (vizData.labels && Array.isArray(vizData.labels) && vizData.labels.length > 0) {
                radarLabels = vizData.labels;
                console.log('使用自定义雷达图标签:', radarLabels);
            }
            if (vizData.data && Array.isArray(vizData.data) && vizData.data.length > 0) {
                radarData = vizData.data;
                console.log('使用自定义雷达图数据:', radarData);
            }
        }

        // 从分析结果中提取数据 - 柱状图
        let barLabels = ['高潮铺垫', '紧张感营造', '情节转折', '角色反应', '节奏控制', '情感渲染'];
        let barData = [85, 80, 90, 75, 85, 70];

        // 从元数据中获取真实数据
        if (resultMetadata && resultMetadata.visualization_data && resultMetadata.visualization_data.bar) {
            const vizData = resultMetadata.visualization_data.bar;
            if (vizData.labels && Array.isArray(vizData.labels) && vizData.labels.length > 0) {
                barLabels = vizData.labels;
                console.log('使用自定义柱状图标签:', barLabels);
            }
            if (vizData.data && Array.isArray(vizData.data) && vizData.data.length > 0) {
                barData = vizData.data;
                console.log('使用自定义柱状图数据:', barData);
            }
        }

        // 加载Chart.js并初始化图表
        loadChartJS().then(() => {
            try {
                // 初始化雷达图
                const radarCtx = document.getElementById('radarChart').getContext('2d');

                // 检查是否已有图表实例，如果有则销毁
                if (window.radarChart) {
                    window.radarChart.destroy();
                }

                window.radarChart = new Chart(radarCtx, {
                    type: 'radar',
                    data: {
                        labels: radarLabels,
                        datasets: [{
                            label: '高潮节奏分析评分',
                            data: radarData,
                            fill: true,
                            backgroundColor: 'rgba(74, 107, 223, 0.2)',
                            borderColor: 'rgb(74, 107, 223)',
                            pointBackgroundColor: 'rgb(74, 107, 223)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgb(74, 107, 223)'
                        }]
                    },
                    options: {
                        elements: {
                            line: {
                                borderWidth: 3
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                suggestedMin: 0,
                                suggestedMax: 100
                            }
                        }
                    }
                });

                console.log('雷达图初始化成功');

                // 初始化柱状图
                const barCtx = document.getElementById('barChart').getContext('2d');

                // 检查是否已有图表实例，如果有则销毁
                if (window.barChart) {
                    window.barChart.destroy();
                }

                window.barChart = new Chart(barCtx, {
                    type: 'bar',
                    data: {
                        labels: barLabels,
                        datasets: [{
                            label: '高潮节奏分析指标',
                            data: barData,
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.2)',
                                'rgba(255, 99, 132, 0.2)',
                                'rgba(54, 162, 235, 0.2)',
                                'rgba(255, 206, 86, 0.2)',
                                'rgba(153, 102, 255, 0.2)',
                                'rgba(255, 159, 64, 0.2)'
                            ],
                            borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(153, 102, 255, 1)',
                                'rgba(255, 159, 64, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });

                console.log('柱状图初始化成功');
            } catch (e) {
                console.error('图表初始化失败:', e);
                // 如果初始化失败，尝试使用通用修复脚本
                setTimeout(() => {
                    if (typeof fixAllCharts === 'function') {
                        fixAllCharts();
                    }
                }, 500);
            }
        }).catch(error => {
            console.error('图表初始化失败:', error);
        });
    }

    // 更新分析日志
    function updateAnalysisLog() {
        const logContainer = document.getElementById('logContainer');
        if (!logContainer) return;

        const novelId = logContainer.getAttribute('data-novel-id');
        if (!novelId) return;

        fetch(`/api/analysis/logs?novel_id=${novelId}&dimension=${dimension}`)
            .then(res => res.json())
            .then(data => {
                if (data.success && data.logs) {
                    // 格式化日志
                    let formattedLogs = '';
                    data.logs.forEach(log => {
                        formattedLogs += `<div class="log-entry">[${log.timestamp}] ${log.message}</div>`;
                    });

                    logContainer.innerHTML = formattedLogs || '<div class="text-muted">暂无日志</div>';

                    // 滚动到底部
                    logContainer.scrollTop = logContainer.scrollHeight;
                }
            })
            .catch(err => {
                console.error('获取分析日志失败:', err);
                logContainer.innerHTML = '<div class="text-danger">获取日志失败</div>';
            });
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化进度更新
        updateProgressUI();

        // 初始化图表
        initCharts();

        // 更新分析日志
        updateAnalysisLog();
        setInterval(updateAnalysisLog, 10000);
    });
</script>

<!-- 引入进度数据修复脚本 -->
<script src="{{ url_for('static', filename='js/progress-data-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/progress-data-fix.js';"></script>
{% endblock %}
