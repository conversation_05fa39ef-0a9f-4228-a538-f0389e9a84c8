from src.db.connection import Session
from src.models.chapter_analysis_result import ChapterAnalysisResult
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_character_relationships_content():
    session = Session()
    try:
        # 查找需要检查的记录
        result = session.query(ChapterAnalysisResult).filter_by(
            novel_id=41, 
            chapter_id=8, 
            dimension='character_relationships'
        ).first()
        
        if not result:
            logger.error("未找到记录")
            return
        
        logger.info(f"找到记录: ID={result.id}, 维度={result.dimension}")
        logger.info(f"分析内容: {result.content}")
        logger.info(f"分析内容长度: {len(result.content) if result.content else 0}")
        logger.info(f"推理内容长度: {len(result.reasoning_content) if result.reasoning_content else 0}")
        
    except Exception as e:
        logger.error(f"检查分析内容时出错: {str(e)}")
    finally:
        session.close()

if __name__ == "__main__":
    check_character_relationships_content()
