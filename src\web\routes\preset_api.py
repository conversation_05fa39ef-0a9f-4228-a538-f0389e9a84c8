"""
九猫小说分析写作系统 - 预设内容API路由
"""
import logging
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify
from src.db.connection import Session
from src.models.preset import Preset

preset_api_bp = Blueprint('preset_api', __name__)
logger = logging.getLogger(__name__)

@preset_api_bp.route('/api/presets', methods=['GET'])
def api_get_presets():
    """获取预设内容列表"""
    try:
        session = Session()
        try:
            # 获取查询参数
            category = request.args.get('category')
            
            # 构建查询
            query = session.query(Preset)
            
            # 应用过滤条件
            if category:
                query = query.filter_by(category=category)
                
            # 按创建时间排序
            query = query.order_by(Preset.created_at.desc())
            
            # 执行查询
            presets = query.all()
            
            # 转换为字典列表
            presets_list = []
            for preset in presets:
                presets_list.append({
                    'id': preset.id,
                    'title': preset.title,
                    'category': preset.category,
                    'created_at': preset.created_at.isoformat() if preset.created_at else None,
                    'updated_at': preset.updated_at.isoformat() if preset.updated_at else None
                })
                
            return jsonify({
                'success': True,
                'presets': presets_list
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设内容列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@preset_api_bp.route('/api/presets/<int:preset_id>', methods=['GET'])
def api_get_preset(preset_id):
    """获取单个预设内容"""
    try:
        session = Session()
        try:
            # 获取预设内容
            preset = session.query(Preset).get(preset_id)
            if not preset:
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设内容'
                }), 404
                
            # 转换为字典
            preset_dict = {
                'id': preset.id,
                'title': preset.title,
                'content': preset.content,
                'category': preset.category,
                'created_at': preset.created_at.isoformat() if preset.created_at else None,
                'updated_at': preset.updated_at.isoformat() if preset.updated_at else None,
                'meta_info': preset.meta_info
            }
                
            return jsonify({
                'success': True,
                'preset': preset_dict
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@preset_api_bp.route('/api/presets', methods=['POST'])
def api_create_preset():
    """创建或更新预设内容"""
    try:
        # 获取请求数据
        data = request.json or {}
        preset_id = data.get('id')
        title = data.get('title')
        content = data.get('content')
        category = data.get('category', 'other')
        
        # 验证必要字段
        if not title:
            return jsonify({
                'success': False,
                'error': '标题不能为空'
            }), 400
            
        if not content:
            return jsonify({
                'success': False,
                'error': '内容不能为空'
            }), 400
            
        session = Session()
        try:
            if preset_id:
                # 更新现有预设
                preset = session.query(Preset).get(preset_id)
                if not preset:
                    return jsonify({
                        'success': False,
                        'error': '未找到指定预设内容'
                    }), 404
                    
                # 更新字段
                preset.title = title
                preset.content = content
                preset.category = category
                preset.updated_at = datetime.now()
            else:
                # 创建新预设
                preset = Preset(
                    title=title,
                    content=content,
                    category=category
                )
                session.add(preset)
                
            # 提交事务
            session.commit()
            
            # 转换为字典
            preset_dict = {
                'id': preset.id,
                'title': preset.title,
                'category': preset.category,
                'created_at': preset.created_at.isoformat() if preset.created_at else None,
                'updated_at': preset.updated_at.isoformat() if preset.updated_at else None
            }
                
            return jsonify({
                'success': True,
                'preset': preset_dict
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"创建或更新预设内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@preset_api_bp.route('/api/presets/<int:preset_id>', methods=['DELETE'])
def api_delete_preset(preset_id):
    """删除预设内容"""
    try:
        session = Session()
        try:
            # 获取预设内容
            preset = session.query(Preset).get(preset_id)
            if not preset:
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设内容'
                }), 404
                
            # 删除预设
            session.delete(preset)
            session.commit()
                
            return jsonify({
                'success': True,
                'message': '预设内容已删除'
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"删除预设内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
