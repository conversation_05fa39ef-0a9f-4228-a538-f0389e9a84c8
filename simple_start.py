"""
简化版的九猫启动脚本，只启动最基本的功能
"""
import os
import sys
import time

def main():
    """主函数"""
    print("=== 简化版九猫启动脚本 ===")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查关键文件
    if not os.path.isfile("main.py"):
        print("错误: 找不到main.py文件")
        return
    
    # 修改config.py，确保DEBUG=False
    if os.path.isfile("config.py"):
        try:
            with open("config.py", "r", encoding="utf-8") as f:
                config_content = f.read()
            
            if "DEBUG = True" in config_content:
                config_content = config_content.replace("DEBUG = True", "DEBUG = False")
                with open("config.py", "w", encoding="utf-8") as f:
                    f.write(config_content)
                print("已将DEBUG模式设置为False")
        except Exception as e:
            print(f"修改config.py时出错: {str(e)}")
    
    # 启动主程序
    print("正在启动九猫系统...")
    print("请在浏览器中访问: http://localhost:5001")
    
    try:
        import subprocess
        process = subprocess.Popen(
            ["python", "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        print("九猫系统已启动，正在等待输出...")
        
        # 等待并打印输出
        for i in range(10):  # 只等待10秒
            output = process.stdout.readline()
            if output:
                print(output.strip())
            
            error = process.stderr.readline()
            if error:
                print(f"错误: {error.strip()}")
            
            time.sleep(1)
        
        print("九猫系统已启动，请在浏览器中访问: http://localhost:5001")
        print("按Ctrl+C停止程序")
        
        # 保持程序运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止九猫系统...")
        process.terminate()
    except Exception as e:
        print(f"启动九猫系统时出错: {str(e)}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"运行时出错: {str(e)}")
    finally:
        input("按回车键继续...")
