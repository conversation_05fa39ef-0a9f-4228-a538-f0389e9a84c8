/**
 * 九猫 - 增强版维度详情页面修复脚本
 * 解决维度详情页面加载失败和崩溃问题
 * 版本: 2.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('增强版维度详情页面修复脚本已加载 v2.0.0');
    
    // 配置参数
    const CONFIG = {
        lazyLoadContent: true,       // 懒加载内容
        paginateResults: true,       // 分页显示结果
        optimizeCharts: true,        // 优化图表
        reduceAnimations: true,      // 减少动画
        maxContentLength: 10000,     // 最大内容长度（超过则分页）
        maxLogEntries: 100,          // 最大日志条目数
        useSimpleCharts: false,      // 使用简化图表（紧急情况）
        monitorMemory: true          // 监控内存使用
    };
    
    // 存储原始内容
    let originalContent = {
        analysisResult: null,
        analysisProcess: null,
        analysisLogs: null
    };
    
    // 修复分析过程显示问题
    function fixAnalysisProcessDisplay() {
        console.log('修复分析过程显示问题');
        
        // 查找所有折叠按钮
        const toggleButtons = document.querySelectorAll('.toggle-process-btn, .collapse-btn, .expand-btn');
        
        // 为每个按钮添加点击事件
        toggleButtons.forEach(button => {
            // 移除现有事件监听器
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // 添加新的事件监听器
            newButton.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const target = document.getElementById(targetId);
                
                if (target) {
                    // 切换显示状态
                    if (target.style.display === 'none') {
                        target.style.display = 'block';
                        this.textContent = this.textContent.replace('显示', '隐藏');
                    } else {
                        target.style.display = 'none';
                        this.textContent = this.textContent.replace('隐藏', '显示');
                    }
                }
            });
        });
        
        // 查找所有折叠内容
        const collapsibleContents = document.querySelectorAll('.process-content, .collapsible-content');
        
        // 为每个折叠内容添加样式
        collapsibleContents.forEach(content => {
            // 设置初始状态为隐藏
            content.style.display = 'none';
        });
    }
    
    // 优化内存使用
    function optimizeMemoryUsage() {
        console.log('优化内存使用');
        
        // 查找所有大型内容区域
        const largeContents = document.querySelectorAll('.analysis-content, .process-content, .log-content');
        
        // 为每个大型内容区域添加分页
        largeContents.forEach(content => {
            // 获取原始内容
            const originalText = content.innerHTML;
            
            // 如果内容超过最大长度，添加分页
            if (originalText.length > CONFIG.maxContentLength) {
                console.log(`内容长度 ${originalText.length} 超过最大长度 ${CONFIG.maxContentLength}，添加分页`);
                
                // 存储原始内容
                content.setAttribute('data-original-content', originalText);
                
                // 分割内容
                const chunks = splitContent(originalText);
                
                // 创建分页容器
                const paginationContainer = document.createElement('div');
                paginationContainer.className = 'pagination-container';
                
                // 创建内容容器
                const contentContainer = document.createElement('div');
                contentContainer.className = 'paginated-content';
                
                // 添加第一页内容
                contentContainer.innerHTML = chunks[0];
                
                // 创建分页控制
                const paginationControls = document.createElement('div');
                paginationControls.className = 'pagination-controls';
                
                // 添加上一页按钮
                const prevButton = document.createElement('button');
                prevButton.className = 'btn btn-sm btn-outline-secondary';
                prevButton.textContent = '上一页';
                prevButton.disabled = true;
                
                // 添加页码显示
                const pageInfo = document.createElement('span');
                pageInfo.className = 'page-info';
                pageInfo.textContent = `1 / ${chunks.length}`;
                
                // 添加下一页按钮
                const nextButton = document.createElement('button');
                nextButton.className = 'btn btn-sm btn-outline-secondary';
                nextButton.textContent = '下一页';
                nextButton.disabled = chunks.length <= 1;
                
                // 添加事件监听器
                let currentPage = 0;
                
                prevButton.addEventListener('click', function() {
                    if (currentPage > 0) {
                        currentPage--;
                        contentContainer.innerHTML = chunks[currentPage];
                        pageInfo.textContent = `${currentPage + 1} / ${chunks.length}`;
                        prevButton.disabled = currentPage === 0;
                        nextButton.disabled = false;
                    }
                });
                
                nextButton.addEventListener('click', function() {
                    if (currentPage < chunks.length - 1) {
                        currentPage++;
                        contentContainer.innerHTML = chunks[currentPage];
                        pageInfo.textContent = `${currentPage + 1} / ${chunks.length}`;
                        prevButton.disabled = false;
                        nextButton.disabled = currentPage === chunks.length - 1;
                    }
                });
                
                // 添加控制按钮
                paginationControls.appendChild(prevButton);
                paginationControls.appendChild(pageInfo);
                paginationControls.appendChild(nextButton);
                
                // 添加分页容器
                paginationContainer.appendChild(contentContainer);
                paginationContainer.appendChild(paginationControls);
                
                // 替换原始内容
                content.innerHTML = '';
                content.appendChild(paginationContainer);
            }
        });
    }
    
    // 分割内容
    function splitContent(content) {
        // 尝试按标题分割
        const headingRegex = /<h[1-6][^>]*>.*?<\/h[1-6]>/gi;
        const headings = content.match(headingRegex);
        
        if (headings && headings.length > 1) {
            // 按标题分割
            const chunks = [];
            let lastIndex = 0;
            
            for (let i = 1; i < headings.length; i++) {
                const headingIndex = content.indexOf(headings[i], lastIndex);
                if (headingIndex > 0) {
                    chunks.push(content.substring(lastIndex, headingIndex));
                    lastIndex = headingIndex;
                }
            }
            
            // 添加最后一个块
            chunks.push(content.substring(lastIndex));
            
            return chunks.length > 0 ? chunks : [content];
        } else {
            // 没有足够的标题，按长度分割
            const chunks = [];
            const maxChunkLength = 3000;
            
            for (let i = 0; i < content.length; i += maxChunkLength) {
                chunks.push(content.substring(i, i + maxChunkLength));
            }
            
            return chunks;
        }
    }
    
    // 添加全局错误处理
    function addGlobalErrorHandling() {
        console.log('添加全局错误处理');
        
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                console.error('捕获到全局错误:', event.error.message);
                
                // 检查是否与图表相关
                if (event.error.message.includes('Chart') || 
                    event.error.message.includes('canvas') || 
                    event.error.message.includes('Canvas')) {
                    console.log('尝试修复图表相关错误');
                    
                    // 尝试使用全局chartFix对象修复
                    if (window.chartFix && typeof window.chartFix.fixAllCharts === 'function') {
                        setTimeout(window.chartFix.fixAllCharts, 200);
                    }
                }
            }
        });
    }
    
    // 创建维度对应的图表
    function createDimensionChart(canvas, dimension) {
        console.log(`为维度 ${dimension} 创建图表`);
        
        // 默认雷达图配置
        const defaultRadarConfig = {
            type: 'radar',
            data: {
                labels: ['分析深度', '准确性', '洞察力', '分析质量', '内容相关性', '实用价值'],
                datasets: [{
                    label: '分析评分',
                    data: [85, 78, 90, 80, 88, 75],
                    backgroundColor: 'rgba(74, 107, 223, 0.2)',
                    borderColor: 'rgba(74, 107, 223, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true
                    }
                },
                scales: {
                    r: {
                        angleLines: {
                            display: true
                        },
                        suggestedMin: 0,
                        suggestedMax: 100
                    }
                }
            }
        };
        
        // 默认柱状图配置
        const defaultBarConfig = {
            type: 'bar',
            data: {
                labels: ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'],
                datasets: [{
                    label: '分析效率',
                    data: [120, 5, 8, 2500, 0.5],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(153, 102, 255, 0.2)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        };
        
        // 尝试从页面获取数据
        try {
            // 尝试根据维度获取元数据
            let metadata = null;
            
            // 检查是否有全局结果元数据对象
            if (window.resultMetadata) {
                metadata = window.resultMetadata;
            } else {
                // 尝试从页面元素获取
                const metadataElement = document.getElementById('analysis-metadata');
                if (metadataElement && metadataElement.textContent) {
                    try {
                        metadata = JSON.parse(metadataElement.textContent);
                    } catch (parseError) {
                        console.error('解析元数据JSON失败:', parseError);
                    }
                }
            }
            
            // 如果找到元数据，尝试使用其中的可视化数据
            if (metadata && metadata.visualization_data) {
                const vizData = metadata.visualization_data;
                
                if (canvas.id === 'radarChart' && vizData.radar) {
                    // 更新雷达图数据
                    if (vizData.radar.labels && Array.isArray(vizData.radar.labels)) {
                        defaultRadarConfig.data.labels = vizData.radar.labels;
                    }
                    if (vizData.radar.data && Array.isArray(vizData.radar.data)) {
                        defaultRadarConfig.data.datasets[0].data = vizData.radar.data;
                    }
                    if (vizData.radar.label) {
                        defaultRadarConfig.data.datasets[0].label = vizData.radar.label;
                    }
                } else if (canvas.id === 'barChart' && vizData.bar) {
                    // 更新柱状图数据
                    if (vizData.bar.labels && Array.isArray(vizData.bar.labels)) {
                        defaultBarConfig.data.labels = vizData.bar.labels;
                    }
                    if (vizData.bar.data && Array.isArray(vizData.bar.data)) {
                        defaultBarConfig.data.datasets[0].data = vizData.bar.data;
                    }
                    if (vizData.bar.label) {
                        defaultBarConfig.data.datasets[0].label = vizData.bar.label;
                    }
                }
            }
        } catch (e) {
            console.error('获取图表数据时出错:', e);
        }
        
        // 根据canvas ID选择创建什么类型的图表
        if (canvas.id === 'radarChart') {
            // 使用Chart.js的全局chartFix对象创建图表
            if (window.chartFix && typeof window.chartFix.safeCreateChart === 'function') {
                window.chartFix.safeCreateChart(canvas, defaultRadarConfig);
            } else {
                // 如果没有全局对象，使用普通方式创建
                try {
                    const ctx = canvas.getContext('2d');
                    new Chart(ctx, defaultRadarConfig);
                } catch (e) {
                    console.error('创建雷达图失败:', e);
                }
            }
        } else if (canvas.id === 'barChart') {
            // 使用Chart.js的全局chartFix对象创建图表
            if (window.chartFix && typeof window.chartFix.safeCreateChart === 'function') {
                window.chartFix.safeCreateChart(canvas, defaultBarConfig);
            } else {
                // 如果没有全局对象，使用普通方式创建
                try {
                    const ctx = canvas.getContext('2d');
                    new Chart(ctx, defaultBarConfig);
                } catch (e) {
                    console.error('创建柱状图失败:', e);
                }
            }
        }
    }
    
    // 初始化函数
    function init() {
        console.log('开始修复维度详情页面显示问题');
        
        // 先修复分析过程显示问题
        fixAnalysisProcessDisplay();
        
        // 优化内存使用
        if (CONFIG.lazyLoadContent) {
            optimizeMemoryUsage();
        }
        
        // 添加全局错误处理
        addGlobalErrorHandling();
        
        // 检查页面上的canvas元素
        const canvases = document.querySelectorAll('canvas');
        if (canvases.length > 0) {
            console.log(`找到 ${canvases.length} 个canvas元素，准备初始化图表`);
            
            // 对每个canvas创建图表
            canvases.forEach(function(canvas) {
                if (canvas.id === 'radarChart' || canvas.id === 'barChart') {
                    // 获取所在页面的维度
                    let dimension = '';
                    try {
                        const pathParts = window.location.pathname.split('/');
                        dimension = pathParts[pathParts.length - 1];
                    } catch (e) {
                        console.error('无法从URL获取维度:', e);
                    }
                    
                    // 创建图表
                    createDimensionChart(canvas, dimension);
                }
            });
        }
    }
    
    // 在DOM加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        // 页面已加载完成，直接执行
        init();
    }
})();
