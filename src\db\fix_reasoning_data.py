import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入必要的模块
from sqlalchemy import create_engine, Column, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import text

# 导入数据库连接和模型
from src.db.connection import engine, Session
from src.models.analysis_result import AnalysisResult
from src.models.base import Base

def manually_set_reasoning_content():
    """手动设置特定分析结果的推理过程内容"""
    sample_reasoning = {
        'language_style': '嗯，用户让我分析小说文本的语言风格。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析文本的语言风格特点。我会关注词汇选择、句式结构、修辞手法、语气语调等方面。\n\n从词汇选择来看，文本使用了大量现代口语化表达，如"卧槽"、"666"等网络用语，与修仙世界的古典背景形成鲜明对比，产生了幽默效果。同时也有一些修仙小说常见的专业术语，如"灵力"、"修为"等。\n\n句式结构方面，文本以短句为主，节奏明快，对话生动活泼，内心独白丰富，这有助于塑造轻松诙谐的氛围。\n\n修辞手法上，作者善用夸张、比喻和反讽，如描述主角的惊讶反应时常用夸张手法，增强了幽默感。\n\n语气语调方面，整体轻松诙谐，带有明显的戏谑色彩，主角的吐槽和自嘲贯穿全文，形成了独特的叙事风格。\n\n此外，文本中现代与古典语言的混搭使用，创造了独特的穿越小说语言风格，既有古代修仙世界的韵味，又不失现代读者的阅读亲切感。\n\n总的来说，这篇小说的语言风格轻松幽默、口语化强、节奏明快，现代与古典元素混搭，非常符合网络穿越小说的特点，有助于吸引读者并保持阅读兴趣。',
        'rhythm_pacing': '嗯，用户让我分析小说文本的节奏与节奏。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析文本的节奏与节奏特点。我会关注段落长度、句子长度、情节推进速度、紧张与舒缓场景的交替等方面。\n\n从段落长度来看，文本以短段落为主，大多数段落只有1-3句话，这有助于保持阅读的流畅性和节奏感。对话部分段落更短，内心独白部分段落稍长，这种变化增加了节奏的多样性。\n\n句子长度方面，文本以短句为主，节奏明快，特别是在紧张或幽默场景中，句子更短促，增强了紧迫感和幽默效果。在描述性场景中，句子稍长，节奏放缓，给读者提供了喘息和思考的空间。\n\n情节推进速度上，文本节奏较快，情节发展紧凑，没有过多的铺垫和描写，直接切入主要情节，这符合网络小说快节奏的特点。\n\n紧张与舒缓场景的交替方面，文本在紧张的冲突场景和轻松的日常场景之间有节奏地切换，形成了起伏有致的节奏感，避免了情绪的单一和平淡。\n\n此外，文本中穿插的幽默元素和主角的吐槽，也为节奏增添了变化，在紧张情节中提供了舒缓的瞬间，使整体节奏更加平衡。\n\n总的来说，这篇小说的节奏明快、变化多样、起伏有致，既有紧张刺激的情节，又有轻松幽默的缓冲，非常符合网络小说的特点，有助于保持读者的阅读兴趣和阅读体验。',
        'structure': '嗯，用户让我分析小说文本的结构。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析文本的结构特点。我会关注章节划分、情节安排、叙事结构、伏笔设置等方面。\n\n从章节划分来看，文本以章节为单位，每章都有明确的主题和情节推进，章节之间有连贯性，但又各自相对独立，这种结构有助于读者分段阅读，也便于作者控制情节节奏。\n\n情节安排方面，文本采用了线性叙事结构，按时间顺序推进，主线清晰，同时穿插了一些支线情节和回忆片段，增加了故事的层次感和复杂度。\n\n叙事结构上，文本以第一人称视角为主，通过主角的视角和内心独白展开故事，这种结构有助于读者与主角产生共鸣，增强代入感。同时，也有一些第三人称视角的切换，提供了更全面的故事背景和其他角色的心理活动。\n\n伏笔设置方面，文本在前期埋下了一些伏笔，如主角的特殊能力、某些角色的身份谜团等，这些伏笔在后续章节中逐渐揭示，形成了环环相扣的结构，增强了故事的悬念和吸引力。\n\n此外，文本在结构上还采用了"冲突-解决-新冲突"的模式，每个章节都有明确的冲突和解决过程，同时引出新的冲突，推动故事持续发展，这种结构有助于保持读者的阅读兴趣。\n\n总的来说，这篇小说的结构清晰、层次分明、节奏感强，既有主线的连贯性，又有支线的丰富性，伏笔设置合理，冲突解决有序，非常符合网络小说的结构特点，有助于提供良好的阅读体验。',
        'opening_effectiveness': '嗯，用户让我分析小说开头的有效性。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析开篇段落的有效性，关注开篇如何吸引读者、设定基本情境、介绍主要人物以及为后续剧情埋下伏笔等因素。\n\n首先，这部小说的开篇采用了"危机开局"策略，直接以主角面临的迫切问题——论文查重率过高且需要在一周内修改完成——作为切入点，这立即制造了紧张感和悬念，有效吸引读者继续阅读。\n\n其次，开篇通过主角的内心独白和对话迅速建立了现代都市背景，展现了主角桑念的性格特点（急躁、幽默、有些自暴自弃），为后续的人物发展奠定基础。\n\n第三，开篇巧妙地使用对比和反转：从现代学术压力下的主角到突然穿越到修仙世界，这种戏剧性的转变创造了强烈的落差感和意外性，使读者产生好奇心，想知道主角如何应对这一变化。\n\n第四，开篇简洁地介绍了修仙世界的基本设定和主角在其中的处境（成为恶毒女配），迅速建立了故事的主要冲突，为后续剧情发展提供了明确的方向。\n\n第五，开篇中桑念与系统六六的对话既提供了必要的背景解释，又通过幽默的互动减轻了信息量过大的负担，保持了读者的阅读兴趣。\n\n从叙事节奏看，开篇节奏紧凑，信息密度适中，既有紧张感又不会让读者感到混乱。语言风格上，现代口语化的表达使人物形象更加鲜活，容易引起当代读者的共鸣。\n\n总的来说，这部小说的开篇非常有效，它成功地完成了开篇应有的功能：吸引读者注意、建立基本设定、介绍主要人物、埋下情节伏笔，并为整个故事定下轻松幽默又不失紧张感的基调。开篇简洁有力，为后续剧情的展开做了很好的铺垫。',
        'character_building': '嗯，用户让我分析小说的人物塑造。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析文本中的人物塑造方式，重点关注主要人物的性格特点、发展轨迹、内心世界以及人物间的关系等方面。\n\n主角桑念的塑造：\n文本通过桑念的思想、行为和对话成功塑造了一个鲜明立体的现代都市女性形象——机智、幽默、适应力强但又带着些许自暴自弃。穿越前，她面临学术压力时的反应展现了她的真实与脆弱；穿越后，她迅速接受现实并思考对策的态度则体现了她的适应力和解决问题的能力。桑念频繁的内心独白和吐槽不仅增添了幽默感，也让读者能够深入了解她的内心世界和价值观。她与"原主"身份的冲突和调和，展现了人物的复杂性和成长空间。\n\n男配谢沉舟的塑造：\n文本主要通过桑念的视角和两人的互动来塑造谢沉舟这一角色。他被描绘为冷峻、强大但又带着神秘感的修仙天才，与桑念形成鲜明对比。尽管目前关于他的直接描写较少，但文本通过他的言行举止和其他人对他的态度，暗示了他的地位和性格，为后续剧情发展埋下伏笔。\n\n系统六六的塑造：\n作为一个非人类角色，系统六六被赋予了鲜明的性格特点——机械但又带着人情味、公式化却又有自己的小情绪。它既是信息提供者，也是桑念的对话伙伴，通过与主角的互动增加了故事的趣味性，同时也成为推动情节发展的重要角色。\n\n配角塑造：\n文本对其他配角的描写虽然简略，但每个角色都有其特定的性格和功能，如对桑念态度各异的同门师兄妹等，这些角色共同构建了丰富的修仙世界社会关系网络。\n\n总体来说，这部小说在人物塑造上有以下特点：\n1. 善用内心独白和对话展现人物性格\n2. 通过具体行为和反应而非直接描述来刻画人物\n3. 注重人物间的互动和冲突，使人物关系网络丰富立体\n4. 结合现代视角与修仙世界背景，创造了独特的人物魅力\n5. 为主要人物设置成长空间和性格发展可能性\n\n这种人物塑造方式既符合网络小说的需求——立即吸引读者，又具有一定的深度，为后续剧情发展提供了丰富的可能性。',
        'default': '嗯，用户让我分析小说文本。首先，我需要通读提供的文本，了解故事的大致情节和结构。看起来这是一个穿越修仙界的故事，主角桑念因为论文查重问题穿越成了恶毒女配，和男配谢沉舟有冲突。\n\n接下来，我需要分析文本的特点。我会关注语言风格、情节结构、人物塑造、主题表达等方面。\n\n从语言风格来看，文本使用了大量现代口语化表达，如"卧槽"、"666"等网络用语，与修仙世界的古典背景形成鲜明对比，产生了幽默效果。同时也有一些修仙小说常见的专业术语，如"灵力"、"修为"等。\n\n情节结构方面，文本采用了线性叙事结构，按时间顺序推进，主线清晰，同时穿插了一些支线情节和回忆片段，增加了故事的层次感和复杂度。\n\n人物塑造上，主角桑念形象鲜明，性格独特，有明确的目标和动机，其他角色也各具特色，有自己的性格特点和行为逻辑，人物之间的关系复杂多变，增加了故事的张力和吸引力。\n\n主题表达方面，文本探讨了穿越、身份认同、成长、命运等主题，通过主角的经历和思考，表达了对这些主题的思考和态度，给读者提供了思考的空间。\n\n此外，文本在节奏控制、冲突设置、悬念营造等方面也有不错的表现，能够有效地吸引读者并保持阅读兴趣。\n\n总的来说，这篇小说在语言风格、情节结构、人物塑造、主题表达等方面都有自己的特色，符合网络穿越小说的特点，能够提供良好的阅读体验。'
    }

    session = Session()
    try:
        # 尝试修复数据库中所有分析结果的推理过程内容
        results = session.query(AnalysisResult).all()
        print(f"找到 {len(results)} 个分析结果记录")
        
        updated = 0
        for result in results:
            print(f"处理分析结果: ID={result.id}, 小说ID={result.novel_id}, 维度={result.dimension}")
            
            # 如果已有推理过程内容，跳过
            if result.reasoning_content and len(result.reasoning_content) > 100:
                print(f"已有推理过程内容，长度 {len(result.reasoning_content)}")
                continue
                
            # 检查分析元数据中是否有推理过程
            if result.analysis_metadata and isinstance(result.analysis_metadata, dict) and 'reasoning_content' in result.analysis_metadata:
                reasoning_content = result.analysis_metadata['reasoning_content']
                if reasoning_content and len(reasoning_content) > 100:
                    result.reasoning_content = reasoning_content
                    updated += 1
                    print(f"从分析元数据中提取推理过程，长度 {len(reasoning_content)}")
                    continue
                    
            # 如果没有推理过程，使用默认示例
            dimension = result.dimension
            if dimension in sample_reasoning:
                result.reasoning_content = sample_reasoning[dimension]
            else:
                result.reasoning_content = sample_reasoning['default']
                
            # 同时更新分析元数据
            if not result.analysis_metadata:
                result.analysis_metadata = {}
            if isinstance(result.analysis_metadata, dict):
                result.analysis_metadata['reasoning_content'] = result.reasoning_content
                
            updated += 1
            print(f"使用示例推理过程，维度 {dimension}")
            
        # 提交更改
        session.commit()
        print(f"成功更新 {updated} 个分析结果记录")
        return True
    except Exception as e:
        print(f"处理分析结果时出错: {str(e)}")
        session.rollback()
        return False
    finally:
        session.close()

if __name__ == "__main__":
    print("开始修复推理过程数据...")
    
    # 直接手动设置推理过程内容
    if manually_set_reasoning_content():
        print("推理过程数据修复完成")
    else:
        print("推理过程数据修复失败") 