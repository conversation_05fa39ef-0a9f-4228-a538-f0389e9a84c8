"""
增强版数据库连接管理模块
提供更稳定的数据库连接池和自动恢复机制
"""
import logging
import time
import threading
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError, OperationalError, DisconnectionError

import config
from src.models.base import Base

# 导入所有模型，确保在创建数据库表时注册它们
import src.models.api_log
import src.models.system_alert
import src.models.system_metric
import src.models.novel
import src.models.analysis_result
import src.models.analysis_checkpoint
import src.models.intermediate_result

logger = logging.getLogger(__name__)

# 数据库连接池监控
_pool_stats = {
    "total_connections": 0,
    "active_connections": 0,
    "connection_errors": 0,
    "last_error_time": 0,
    "last_error_message": "",
    "last_reset_time": 0,
    "consecutive_errors": 0,
    "health_check_count": 0,
    "last_health_check_time": 0
}

# 连接池监控锁
_pool_stats_lock = threading.Lock()

# 连接池健康状态
_pool_healthy = True

# 声明全局变量
engine = None
session_factory = None
Session = None

# 创建数据库目录（如果不存在）
def ensure_db_directory():
    """确保数据库目录存在"""
    db_uri = config.DATABASE_URI
    if db_uri.startswith('sqlite:///'):
        db_path = db_uri[10:]  # 移除 'sqlite:///'
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
            logger.info(f"创建数据库目录: {db_dir}")

# 确保数据库目录存在
ensure_db_directory()

# 连接错误处理器
def connection_error_handler(connection_record, connection_error):
    """处理连接错误"""
    global _pool_healthy
    
    with _pool_stats_lock:
        _pool_stats["connection_errors"] += 1
        _pool_stats["last_error_time"] = time.time()
        _pool_stats["last_error_message"] = str(connection_error)
        _pool_stats["consecutive_errors"] += 1
    
    logger.error(f"数据库连接错误: {str(connection_error)}")
    
    # 如果连续错误次数过多，标记连接池为不健康
    if _pool_stats["consecutive_errors"] > 3:
        _pool_healthy = False
        logger.warning("连接池标记为不健康，将在下次请求时重置")
    
    # 返回True表示应该断开连接
    return True

# 创建数据库引擎，使用优化的连接池配置
def create_db_engine():
    """创建数据库引擎"""
    global _pool_healthy
    
    # 重置连续错误计数
    with _pool_stats_lock:
        _pool_stats["consecutive_errors"] = 0
    
    # 标记连接池为健康
    _pool_healthy = True
    
    # 创建引擎
    new_engine = create_engine(
        config.DATABASE_URI,
        poolclass=QueuePool,
        pool_size=20,  # 减小连接池大小，避免资源耗尽
        max_overflow=20,  # 减小最大溢出连接数
        pool_timeout=30,  # 减小连接超时时间
        pool_recycle=300,  # 每5分钟回收连接，避免连接过期
        pool_pre_ping=True,  # 在使用连接前先ping一下，确保连接有效
        echo=config.DEBUG,  # 在DEBUG模式下记录所有SQL语句，帮助调试
        connect_args={"check_same_thread": False}  # 允许在不同线程中使用同一连接（仅适用于SQLite）
    )
    
    # 注册连接事件处理器（兼容性修改）
    try:
        from sqlalchemy import event
        # 仅注册基本事件，不使用可能不兼容的高级事件
        event.listen(new_engine, 'connect', lambda conn, rec: logger.debug('数据库连接已创建'))
        event.listen(new_engine, 'checkout', lambda conn, rec, proxy: logger.debug('数据库连接已检出'))
        event.listen(new_engine, 'checkin', lambda conn, rec: logger.debug('数据库连接已归还'))
        # 不再使用可能不兼容的connection_error事件
        # event.listen(new_engine, 'connection_error', connection_error_handler)
    except Exception as e:
        logger.warning(f"注册数据库连接事件时出错: {str(e)}")
    
    # 记录连接池配置
    logger.info(f"数据库连接池配置: pool_size={new_engine.pool.size()}, max_overflow={new_engine.pool._max_overflow}, pool_timeout={new_engine.pool._timeout}秒")
    
    return new_engine

# 初始化全局对象
def init_db():
    """初始化数据库相关全局对象"""
    global engine, session_factory, Session
    
    # 创建数据库引擎
    engine = create_db_engine()
    
    # 创建会话工厂
    session_factory = sessionmaker(bind=engine)
    
    # 创建线程安全的会话
    Session = scoped_session(session_factory)
    
    # 创建数据库表
    create_tables()

# 创建数据库表
def create_tables():
    """创建数据库表"""
    try:
        Base.metadata.create_all(engine)
        logger.info("数据库表已创建")
    except Exception as e:
        logger.error(f"创建数据库表时出错: {str(e)}")

# 初始化数据库
init_db()

def get_session():
    """
    获取数据库会话。
    自动检查连接健康状态，如果发现问题则尝试修复。

    Returns:
        SQLAlchemy会话对象。
    """
    global _pool_healthy, engine, session_factory, Session
    
    # 如果连接池不健康，重置它
    if not _pool_healthy:
        logger.warning("连接池不健康，正在重置...")
        dispose_engine()
        # 重新创建引擎
        engine = create_db_engine()
        # 更新会话工厂
        session_factory = sessionmaker(bind=engine)
        # 更新线程安全的会话
        Session = scoped_session(session_factory)
    
    # 检查连接健康状态
    try:
        # 每100次请求检查一次连接健康状态
        with _pool_stats_lock:
            _pool_stats["total_connections"] += 1
            if _pool_stats["total_connections"] % 100 == 0 or time.time() - _pool_stats["last_health_check_time"] > 300:
                _pool_stats["health_check_count"] += 1
                _pool_stats["last_health_check_time"] = time.time()
                should_check_health = True
            else:
                should_check_health = False
        
        # 如果需要检查健康状态
        if should_check_health:
            if not check_connection_health():
                logger.warning("连接健康检查失败，尝试重置连接池")
                dispose_engine()
                # 重新创建引擎
                engine = create_db_engine()
                # 更新会话工厂
                session_factory = sessionmaker(bind=engine)
                # 更新线程安全的会话
                Session = scoped_session(session_factory)

        # 获取会话
        session = Session()

        # 更新统计信息
        with _pool_stats_lock:
            _pool_stats["active_connections"] = engine.pool.checkedout()

        return session
    except Exception as e:
        logger.error(f"获取数据库会话时出错: {str(e)}")
        # 出错时尝试重置连接池
        dispose_engine()
        # 重新创建引擎
        engine = create_db_engine()
        # 更新会话工厂
        session_factory = sessionmaker(bind=engine)
        # 更新线程安全的会话
        Session = scoped_session(session_factory)
        # 重试一次
        return Session()

def close_session():
    """
    关闭当前线程的数据库会话。
    """
    try:
        Session.remove()
        logger.debug("数据库会话已关闭")
    except Exception as e:
        logger.error(f"关闭数据库会话时出错: {str(e)}")

def dispose_engine():
    """
    释放数据库引擎的所有连接。
    在应用重启或遇到连接池问题时调用。
    """
    try:
        engine.dispose()
        logger.info("数据库引擎连接池已释放")

        # 更新连接池统计信息
        with _pool_stats_lock:
            _pool_stats["total_connections"] = 0
            _pool_stats["active_connections"] = 0
            _pool_stats["last_reset_time"] = time.time()
            _pool_stats["consecutive_errors"] = 0

    except Exception as e:
        logger.error(f"释放数据库引擎连接池时出错: {str(e)}")

def check_connection_health():
    """
    检查数据库连接健康状态。
    如果连接不健康，会尝试修复。

    Returns:
        布尔值，表示连接是否健康。
    """
    global _pool_healthy
    
    try:
        # 尝试执行简单查询
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))

        # 更新统计信息
        with _pool_stats_lock:
            _pool_stats["active_connections"] = engine.pool.checkedout()
            _pool_stats["total_connections"] = engine.pool.size() + engine.pool.overflow()
            _pool_stats["consecutive_errors"] = 0  # 重置连续错误计数

        # 标记连接池为健康
        _pool_healthy = True

        return True
    except (OperationalError, DisconnectionError) as e:
        # 连接错误，记录并尝试修复
        with _pool_stats_lock:
            _pool_stats["connection_errors"] += 1
            _pool_stats["last_error_time"] = time.time()
            _pool_stats["last_error_message"] = str(e)
            _pool_stats["consecutive_errors"] += 1

        logger.error(f"数据库连接错误: {str(e)}")

        # 如果错误次数超过阈值，尝试重置连接池
        if _pool_stats["consecutive_errors"] > 3:
            logger.warning("数据库连接错误次数过多，尝试重置连接池")
            dispose_engine()
            # 标记连接池为不健康
            _pool_healthy = False

        return False
    except SQLAlchemyError as e:
        logger.error(f"数据库查询错误: {str(e)}")
        return False

def get_connection_stats():
    """
    获取连接池统计信息。

    Returns:
        字典，包含连接池统计信息。
    """
    with _pool_stats_lock:
        stats = dict(_pool_stats)
        stats["pool_healthy"] = _pool_healthy
        stats["pool_size"] = engine.pool.size()
        stats["pool_overflow"] = engine.pool.overflow()
        stats["pool_checkedout"] = engine.pool.checkedout()
        return stats
