{% extends "v3.5/base.html" %}

{% block title %}参考蓝本 - 九猫小说分析写作系统v3.5{% endblock %}

{% block extra_css %}
<style>
    .template-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
    }
    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .template-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .template-category {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        margin-bottom: 1.5rem;
        overflow: hidden;
    }
    .template-category:hover {
        background-color: rgba(0,0,0,0.03);
    }
    .template-category-header {
        padding: 1.25rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .template-category-body {
        padding: 0 1.25rem 1.25rem 1.25rem;
    }
    .search-container {
        position: relative;
        margin-bottom: 2rem;
    }
    .search-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--bs-gray-500);
    }
    #searchTemplates {
        padding-left: 40px;
        border-radius: 50px;
        border: 1px solid var(--bs-gray-300);
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    #searchTemplates:focus {
        box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    }
    .step-icon {
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin: 0 auto 1.5rem;
        background-color: rgba(var(--bs-primary-rgb), 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h1 class="mb-3"><i class="fas fa-bookmark text-primary me-2"></i>参考蓝本</h1>
                <p class="lead">参考蓝本是已完成全部分析维度的小说，可以作为自动写作的参考和指导。</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong> 只有完成所有15个维度分析的小说才能设为参考蓝本。参考蓝本可以在控制台中用于自动写作。
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="mb-0">蓝本列表</h2>
            <div>
                <a href="{{ url_for('v3.novels') }}" class="btn btn-outline-primary">
                    <i class="fas fa-book me-1"></i>查看所有小说
                </a>
            </div>
        </div>

        <!-- 搜索框 -->
        <div class="search-container">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="form-control" id="searchTemplates" placeholder="搜索参考蓝本...">
        </div>

        {% if templates %}
            <!-- 按类别分组的参考蓝本 -->
            {% set categories = {
                '小说': [],
                '散文': [],
                '诗歌': [],
                '其他': []
            } %}

            <!-- 将模板分类 -->
            {% for template in templates %}
                {% if '小说' in template.title|lower or 'novel' in template.title|lower %}
                    {% set _ = categories['小说'].append(template) %}
                {% elif '散文' in template.title|lower or 'essay' in template.title|lower %}
                    {% set _ = categories['散文'].append(template) %}
                {% elif '诗' in template.title|lower or 'poem' in template.title|lower %}
                    {% set _ = categories['诗歌'].append(template) %}
                {% else %}
                    {% set _ = categories['其他'].append(template) %}
                {% endif %}
            {% endfor %}

            <!-- 显示分类的参考蓝本 -->
            {% for category_name, category_templates in categories.items() %}
                {% if category_templates|length > 0 %}
                    <div class="template-category shadow-sm">
                        <div class="template-category-header" data-bs-toggle="collapse" data-bs-target="#category-{{ category_name }}">
                            <h3 class="mb-0">{{ category_name }} ({{ category_templates|length }})</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="collapse show template-category-body" id="category-{{ category_name }}">
                            <div class="row">
                                {% for template in category_templates %}
                                    <div class="col-md-6 col-lg-4 mb-4">
                                        <div class="card h-100 template-card shadow-sm">
                                            <div class="card-body">
                                                <span class="badge bg-primary template-badge">参考蓝本</span>
                                                <h3 class="card-title">{{ template.title }}</h3>
                                                <p class="card-text text-muted">
                                                    {% if template.author %}作者: {{ template.author }}<br>{% endif %}
                                                    字数: {{ template.word_count }}<br>
                                                    章节数: {{ template.chapter_count }}<br>
                                                    分析维度: {{ template.dimension_count }}/15<br>
                                                    创建时间: {{ template.created_at.split('T')[0] if template.created_at is string else template.created_at.strftime('%Y-%m-%d') }}
                                                </p>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-flex justify-content-between flex-wrap">
                                                    <a href="{{ url_for('v3.view_novel', novel_id=template.id) }}" class="btn btn-outline-primary mb-2">
                                                        <i class="fas fa-eye me-1"></i>查看详情
                                                    </a>
                                                    <a href="{{ url_for('v3.chapters_list', novel_id=template.id) }}" class="btn btn-outline-info mb-2">
                                                        <i class="fas fa-list me-1"></i>章节列表
                                                    </a>
                                                    <a href="{{ url_for('v3_5.console') }}?template_id={{ template.id }}" class="btn btn-primary mb-2">
                                                        <i class="fas fa-terminal me-1"></i>使用蓝本
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endfor %}
        {% else %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-bookmark fa-4x text-muted mb-3"></i>
                    <h3>暂无参考蓝本</h3>
                    <p class="text-muted">您需要先上传小说并完成所有15个维度的分析，然后才能将其设为参考蓝本。</p>
                    <div class="mt-4">
                        <a href="{{ url_for('v3.upload_novel') }}" class="btn btn-primary me-2">
                            <i class="fas fa-upload me-1"></i>上传小说
                        </a>
                        <a href="{{ url_for('v3.novels') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-book me-1"></i>查看已有小说
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0"><i class="fas fa-question-circle me-2"></i>如何创建参考蓝本</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <div class="step-icon">
                                <i class="fas fa-upload fa-2x text-primary"></i>
                            </div>
                            <h5>1. 上传小说</h5>
                            <p class="small">上传TXT文件或直接粘贴文本内容</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <div class="step-icon">
                                <i class="fas fa-cogs fa-2x text-primary"></i>
                            </div>
                            <h5>2. 分析所有维度</h5>
                            <p class="small">完成所有15个维度的分析</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <div class="step-icon">
                                <i class="fas fa-list-ol fa-2x text-primary"></i>
                            </div>
                            <h5>3. 分析所有章节</h5>
                            <p class="small">确保每个章节都完成所有维度分析</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <div class="step-icon">
                                <i class="fas fa-bookmark fa-2x text-primary"></i>
                            </div>
                            <h5>4. 设为参考蓝本</h5>
                            <p class="small">在小说详情页点击"设为参考蓝本"按钮</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 参考蓝本页面的JavaScript代码
        console.log('参考蓝本页面已加载');

        // 折叠/展开分类
        $('.template-category-header').click(function() {
            // 切换箭头图标
            const icon = $(this).find('i');
            if (icon.hasClass('fa-chevron-down')) {
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            } else {
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            }
        });

        // 搜索功能
        $('#searchTemplates').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();

            // 如果搜索框为空，显示所有分类
            if (searchTerm === '') {
                $('.template-category').show();
                $('.template-card').show();
                return;
            }

            // 遍历所有模板卡片
            $('.template-card').each(function() {
                const title = $(this).find('.card-title').text().toLowerCase();
                const author = $(this).find('.card-text').text().toLowerCase();
                const isMatch = title.includes(searchTerm) || author.includes(searchTerm);

                // 显示或隐藏卡片
                $(this).toggle(isMatch);
            });

            // 隐藏空分类
            $('.template-category').each(function() {
                const visibleCards = $(this).find('.template-card:visible').length;
                $(this).toggle(visibleCards > 0);
            });
        });
    });
</script>
{% endblock %}
