"""
推理过程优化器 - 温和而有效的降本增效方案
专门用于优化推理过程和分析结果的输出长度，在保证质量的前提下降低成本
"""

import logging
import re
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ReasoningOptimizer:
    """推理过程优化器"""

    @staticmethod
    def optimize_reasoning_output(prompt: str, analysis_type: str, prompt_template: str = "default") -> str:
        """
        优化推理过程输出要求（只针对分析功能，不影响写作功能）

        Args:
            prompt: 原始提示词
            analysis_type: 分析类型
            prompt_template: 提示词模板类型

        Returns:
            优化后的提示词
        """
        if prompt_template != "simplified":
            return prompt  # 只在精简版模式下优化

        # 检查是否为写作相关功能，如果是则不进行优化
        writing_analysis_types = [
            "chapter_content_generation", "chapter_framework", "chapter_generation",
            "content_generation", "writing", "story_generation", "novel_generation"
        ]

        if analysis_type in writing_analysis_types:
            logger.info(f"[写作功能保护] 推理过程优化器：{analysis_type}为写作功能，不进行推理过程优化")
            return prompt

        try:
            # 只对分析功能进行优化
            # 方案1：结构化输出控制
            structured_requirements = ReasoningOptimizer._get_structured_output_requirements(analysis_type)

            # 方案2：分层输出控制
            layered_control = ReasoningOptimizer._get_layered_output_control(analysis_type)

            # 方案3：智能长度限制
            length_limits = ReasoningOptimizer._get_intelligent_length_limits(analysis_type)

            # 组合所有优化要求
            optimization_section = f"""

## 精简版分析输出优化要求（仅限分析功能）

### 结构化控制
{structured_requirements}

### 分层输出
{layered_control}

### 长度控制
{length_limits}

### 质量保证
- 保持分析的核心观点和关键洞察
- 确保逻辑清晰，要点突出
- 避免重复表达和冗余描述
- 使用精准的词汇，减少修饰语
"""

            # 在提示词末尾添加优化要求
            optimized_prompt = prompt + optimization_section

            logger.info(f"为分析维度{analysis_type}添加推理过程优化要求，预计减少输出50-70%")
            return optimized_prompt

        except Exception as e:
            logger.error(f"优化推理过程输出时出错: {str(e)}")
            return prompt

    @staticmethod
    def _get_structured_output_requirements(analysis_type: str) -> str:
        """获取结构化输出要求（优化表达，不限制长度）"""

        # 根据分析类型定制输出结构，重点是优化表达方式而非限制长度
        if analysis_type in ["language_style", "rhythm_pacing"]:
            return """- 推理过程：清晰阐述核心分析思路和方法
- 分析结果：按重要性组织，突出主要特点和表现
- 总结评价：简洁概括主要发现和价值
- 表达优化：使用精准词汇，避免冗余描述"""

        elif analysis_type in ["structure", "character_development"]:
            return """- 推理过程：系统阐述分析步骤和逻辑
- 分析结果：按重要性排序，详述关键发现
- 关键洞察：突出最重要的分析价值
- 表达优化：结构清晰，重点突出"""

        elif analysis_type in ["chapter_outline", "outline_analysis"]:
            return """- 推理过程：简述分析思路和方法
- 内容重现：保留核心情节和主要人物发展
- 分析要点：重点突出，逻辑清晰
- 表达优化：充分利用分析结果，确保价值最大化"""

        else:
            return """- 推理过程：清晰阐述核心思路
- 分析结果：要点明确，逻辑清晰
- 总体评价：简洁概括分析价值
- 表达优化：保持分析完整性和深度"""

    @staticmethod
    def _get_layered_output_control(analysis_type: str) -> str:
        """获取分层输出控制要求"""

        return """- 第一层：核心观点（必须包含，占总输出40%）
- 第二层：支撑论据（重要内容，占总输出35%）
- 第三层：补充说明（可适度精简，占总输出25%）
- 优先级：确保第一层完整，第二层充实，第三层精简"""

    @staticmethod
    def _get_intelligent_length_limits(analysis_type: str) -> str:
        """获取智能表达优化要求（不限制长度，只优化表达）"""

        # 重要：Token已经消耗，不应该限制输出长度，只优化表达方式
        return """- 表达优化：使用简洁明了的语言，避免冗余表达
- 结构清晰：确保逻辑清晰，要点突出
- 内容完整：充分利用已消耗的Token，不人为截断分析结果
- 质量保证：保持分析深度和价值，确保分析的完整性
- 智能优化：在保持内容完整的前提下优化表达方式"""

    @staticmethod
    def post_process_reasoning_content(content: str, analysis_type: str, prompt_template: str = "default") -> str:
        """
        后处理推理过程内容，只进行格式优化，不截断内容

        重要：Token已经消耗，应该充分利用分析结果，不进行内容截断

        Args:
            content: 原始推理过程内容
            analysis_type: 分析类型
            prompt_template: 提示词模板类型

        Returns:
            格式优化后的推理过程内容
        """
        if prompt_template != "simplified" or not content:
            return content

        # 检查是否为写作相关功能，如果是则不进行后处理优化
        writing_analysis_types = [
            "chapter_content_generation", "chapter_framework", "chapter_generation",
            "content_generation", "writing", "story_generation", "novel_generation"
        ]

        if analysis_type in writing_analysis_types:
            logger.info(f"[写作功能保护] 推理过程后处理：{analysis_type}为写作功能，不进行后处理优化")
            return content

        try:
            # 重要：只进行格式优化，不截断内容，充分利用已消耗的Token
            # 只进行基础的格式清理，保持内容完整性
            formatted_content = ReasoningOptimizer._format_only_optimization(content, analysis_type)

            logger.info(f"[智能优化] 推理过程格式优化完成：{analysis_type}，保持内容完整性，长度: {len(formatted_content)}字符")

            return formatted_content

        except Exception as e:
            logger.error(f"后处理推理过程内容时出错: {str(e)}")
            return content

    @staticmethod
    def _format_only_optimization(content: str, analysis_type: str) -> str:
        """
        只进行格式优化，不截断内容
        充分利用已消耗的Token，保持分析结果的完整性
        """
        try:
            # 只进行基础的格式清理，不截断内容
            formatted = content

            # 移除多余的空行（但保持段落结构）
            formatted = re.sub(r'\n{4,}', '\n\n\n', formatted)

            # 移除行首行尾的多余空格
            lines = formatted.split('\n')
            cleaned_lines = [line.rstrip() for line in lines]
            formatted = '\n'.join(cleaned_lines)

            # 确保标准的分段标题格式
            if '## 分析思路说明' not in formatted and '## 详细分析' not in formatted:
                # 尝试添加标准格式，但不截断内容
                if len(formatted) > 100:
                    # 简单的格式化，保持内容完整
                    formatted = f"## 分析思路说明：\n{formatted}\n\n## 详细分析：\n[已包含在上述内容中]"

            logger.info(f"[格式优化] 完成格式清理，保持内容完整性，长度: {len(formatted)}字符")
            return formatted

        except Exception as e:
            logger.error(f"格式优化时出错: {str(e)}")
            return content

    @staticmethod
    def _compress_reasoning_content(content: str, analysis_type: str) -> str:
        """智能压缩推理过程内容"""
        try:
            # 移除冗余的表达
            compressed = content

            # 压缩重复的修饰词
            redundant_phrases = [
                "非常详细地", "极其仔细地", "特别认真地", "十分深入地",
                "我认为", "我觉得", "我发现", "我注意到",
                "显然", "毫无疑问", "不难发现", "可以看出",
                "总的来说", "综上所述", "最后", "最终"
            ]

            for phrase in redundant_phrases:
                compressed = compressed.replace(phrase, "")

            # 压缩重复的标点符号
            compressed = re.sub(r'[。]{2,}', '。', compressed)
            compressed = re.sub(r'[，]{2,}', '，', compressed)
            compressed = re.sub(r'[！]{2,}', '！', compressed)

            # 移除多余的空行
            compressed = re.sub(r'\n{3,}', '\n\n', compressed)

            return compressed.strip()

        except Exception as e:
            logger.error(f"压缩推理过程内容时出错: {str(e)}")
            return content

    @staticmethod
    def _extract_key_information(content: str, analysis_type: str) -> str:
        """提取关键信息"""
        try:
            # 分离推理过程和分析结果
            sections = content.split('## 详细分析')

            if len(sections) >= 2:
                reasoning_part = sections[0]
                analysis_part = '## 详细分析' + sections[1]

                # 优化推理过程部分
                optimized_reasoning = ReasoningOptimizer._optimize_reasoning_section(reasoning_part, analysis_type)

                # 优化分析结果部分
                optimized_analysis = ReasoningOptimizer._optimize_analysis_section(analysis_part, analysis_type)

                return optimized_reasoning + '\n\n' + optimized_analysis
            else:
                # 如果无法分离，整体优化
                return ReasoningOptimizer._optimize_whole_content(content, analysis_type)

        except Exception as e:
            logger.error(f"提取关键信息时出错: {str(e)}")
            return content

    @staticmethod
    def _optimize_reasoning_section(reasoning: str, analysis_type: str) -> str:
        """优化推理过程部分"""
        try:
            # 提取核心要点
            lines = reasoning.split('\n')
            key_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 保留包含关键词的行
                key_indicators = ['分析', '方法', '步骤', '思路', '要点', '关键', '重要']
                if any(indicator in line for indicator in key_indicators):
                    # 简化表达
                    simplified_line = line.replace('我需要', '').replace('首先', '').replace('然后', '')
                    if len(simplified_line) > 50:
                        simplified_line = simplified_line[:47] + '...'
                    key_lines.append(simplified_line)
                elif len(line) < 30 and ('：' in line or '。' in line):
                    # 保留简短的关键句
                    key_lines.append(line)

            # 限制要点数量
            if len(key_lines) > 5:
                key_lines = key_lines[:5]

            return '\n'.join(key_lines)

        except Exception as e:
            logger.error(f"优化推理过程部分时出错: {str(e)}")
            return reasoning

    @staticmethod
    def _optimize_analysis_section(analysis: str, analysis_type: str) -> str:
        """优化分析结果部分"""
        try:
            # 保留关键段落
            paragraphs = analysis.split('\n\n')
            key_paragraphs = []

            for para in paragraphs:
                para = para.strip()
                if not para:
                    continue

                # 保留包含具体分析的段落
                if len(para) > 50 and ('分析' in para or '特点' in para or '表现' in para):
                    # 适度压缩
                    if len(para) > 150:
                        # 保留前100字符和关键结论
                        key_part = para[:100]
                        if '。' in para[100:]:
                            conclusion = para[para.rfind('。', 100):para.rfind('。') + 1]
                            para = key_part + '...' + conclusion
                        else:
                            para = key_part + '...'
                    key_paragraphs.append(para)
                elif len(para) < 100:
                    # 保留简短段落
                    key_paragraphs.append(para)

            # 限制段落数量
            if len(key_paragraphs) > 4:
                key_paragraphs = key_paragraphs[:4]

            return '\n\n'.join(key_paragraphs)

        except Exception as e:
            logger.error(f"优化分析结果部分时出错: {str(e)}")
            return analysis

    @staticmethod
    def _optimize_whole_content(content: str, analysis_type: str) -> str:
        """优化整体内容"""
        try:
            # 按段落分割
            paragraphs = content.split('\n\n')
            optimized_paragraphs = []

            for para in paragraphs:
                para = para.strip()
                if not para:
                    continue

                # 压缩长段落
                if len(para) > 120:
                    # 保留前80字符和最后的句号前内容
                    if '。' in para[80:]:
                        end_pos = para.find('。', 80) + 1
                        para = para[:80] + '...' + para[end_pos-20:end_pos]
                    else:
                        para = para[:100] + '...'

                optimized_paragraphs.append(para)

            # 限制总段落数
            if len(optimized_paragraphs) > 6:
                optimized_paragraphs = optimized_paragraphs[:6]

            return '\n\n'.join(optimized_paragraphs)

        except Exception as e:
            logger.error(f"优化整体内容时出错: {str(e)}")
            return content

    @staticmethod
    def _optimize_format(content: str, analysis_type: str) -> str:
        """优化格式"""
        try:
            # 确保有标准的分段标题
            if '## 分析思路说明' not in content and '## 详细分析' not in content:
                # 尝试添加标准格式
                lines = content.split('\n')
                if len(lines) > 3:
                    mid_point = len(lines) // 3
                    reasoning_part = '\n'.join(lines[:mid_point])
                    analysis_part = '\n'.join(lines[mid_point:])

                    content = f"## 分析思路说明：\n{reasoning_part}\n\n## 详细分析：\n{analysis_part}"

            return content

        except Exception as e:
            logger.error(f"优化格式时出错: {str(e)}")
            return content
