"""
九猫小说分析写作系统v3.1 - 路由
"""
import logging
from datetime import datetime
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from sqlalchemy import func

from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.preset import Preset
from src.services.chapter_analysis_service import ChapterAnalysisService
from src.services.analysis_service import aggregate_chapter_analyses
import config

logger = logging.getLogger(__name__)

v3_1_bp = Blueprint('v3_1', __name__, url_prefix='/v3.1')

@v3_1_bp.route('/')
def index():
    """首页"""
    # 默认统计数据，确保即使出错也有默认值
    default_stats = {
        'total_chapters': 0,
        'total_words': 0,
        'avg_words_per_chapter': 0,
        'total_hours': 24
    }

    session = Session()
    try:
        # 获取最近分析的小说
        novels = session.query(Novel).filter(
            Novel.novel_metadata.is_(None) | ~Novel.novel_metadata.contains({'is_template': True})
        ).order_by(Novel.created_at.desc()).limit(5).all()

        # 获取参考蓝本列表
        reference_templates = session.query(Novel).filter(
            Novel.novel_metadata.contains({'is_template': True})
        ).order_by(Novel.created_at.desc()).limit(5).all()

        try:
            # 统计展示台数据
            total_chapters = session.query(func.count(Chapter.id)).scalar() or 0
            total_words = session.query(func.sum(Novel.word_count)).scalar() or 0

            # 计算平均每章字数
            avg_words_per_chapter = 0
            if total_chapters > 0:
                avg_words_per_chapter = int(total_words / total_chapters)

            # 系统运行时间（小时）
            # 这里假设系统是最近24小时内启动的
            total_hours = 24

            # 创建展示台统计数据
            generated_content_stats = {
                'total_chapters': total_chapters,
                'total_words': total_words,
                'avg_words_per_chapter': avg_words_per_chapter,
                'total_hours': total_hours
            }
        except Exception as stats_error:
            logger.error(f"获取统计数据出错: {str(stats_error)}", exc_info=True)
            generated_content_stats = default_stats

        return render_template('v3.1/index.html',
                             novels=novels,
                             reference_templates=reference_templates,
                             generated_content_stats=generated_content_stats)
    except Exception as e:
        logger.error(f"首页加载出错: {str(e)}", exc_info=True)
        return render_template('v3.1/index.html',
                             novels=[],
                             reference_templates=[],
                             generated_content_stats=default_stats)
    finally:
        session.close()

@v3_1_bp.route('/novels')
def novels():
    """小说列表"""
    session = Session()
    try:
        # 获取所有小说
        novels = session.query(Novel).order_by(Novel.created_at.desc()).all()

        # 获取分析维度
        from config import ANALYSIS_DIMENSIONS

        # 获取每本小说的分析结果
        for novel in novels:
            # 获取章节
            novel.chapters = session.query(Chapter).filter_by(novel_id=novel.id).all()

            # 获取分析结果
            novel.analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel.id).all()

            # 判断是否全部维度已分析
            analyzed_dimensions = [result.dimension for result in novel.analysis_results]
            novel.is_fully_analyzed = len(analyzed_dimensions) == len(ANALYSIS_DIMENSIONS)
            novel.is_partially_analyzed = len(analyzed_dimensions) > 0 and not novel.is_fully_analyzed

            # 判断是否可以设为参考蓝本
            novel.can_set_as_template = novel.is_fully_analyzed and not (novel.novel_metadata and novel.novel_metadata.get('is_template', False))

        return render_template('v3.1/novels.html', novels=novels, dimensions=ANALYSIS_DIMENSIONS)
    except Exception as e:
        logger.error(f"小说列表页面加载出错: {str(e)}", exc_info=True)
        return render_template('v3.1/novels.html', novels=[], dimensions=[])
    finally:
        session.close()

@v3_1_bp.route('/upload_novel', methods=['GET', 'POST'])
def upload_novel():
    """上传小说"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            title = request.form.get('title')
            author = request.form.get('author')
            upload_type = request.form.get('uploadType')
            auto_analyze = 'autoAnalyze' in request.form
            dimensions = request.form.getlist('dimensions')

            # 验证标题
            if not title:
                return render_template('v3.1/upload_novel.html', error='请输入小说标题')

            # 获取小说内容
            content = None
            if upload_type == 'file':
                # 处理文件上传
                if 'file' not in request.files:
                    return render_template('v3.1/upload_novel.html', error='未找到上传文件')

                file = request.files['file']
                if file.filename == '':
                    return render_template('v3.1/upload_novel.html', error='未选择文件')

                if file:
                    try:
                        content = file.read().decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            # 尝试使用GBK编码
                            file.seek(0)
                            content = file.read().decode('gbk')
                        except UnicodeDecodeError:
                            return render_template('v3.1/upload_novel.html', error='文件编码不支持，请使用UTF-8或GBK编码')
            else:
                # 处理文本粘贴
                content = request.form.get('content')
                if not content:
                    return render_template('v3.1/upload_novel.html', error='请输入小说内容')

            # 保存小说到数据库
            session = Session()
            try:
                # 创建小说
                novel = Novel(
                    title=title,
                    author=author,
                    content=content
                )
                session.add(novel)
                session.flush()  # 获取novel.id

                # 分章节
                from src.utils.text_processor import TextProcessor

                # 使用TextProcessor分割章节
                chapter_texts = TextProcessor.split_into_chapters(content)

                # 创建章节对象
                chapters = []
                for i, chapter_text in enumerate(chapter_texts):
                    # 尝试提取章节标题
                    title = None
                    first_line = chapter_text.strip().split('\n')[0] if chapter_text.strip() else ""
                    if first_line and len(first_line) < 100:  # 标题通常不会太长
                        title = first_line

                    # 创建章节
                    chapter = Chapter(
                        novel_id=novel.id,
                        chapter_number=i + 1,
                        content=chapter_text,
                        title=title or f'第{i+1}章'
                    )
                    session.add(chapter)
                    chapters.append(chapter)

                # 更新小说章节数
                novel.chapter_count = len(chapters)
                session.commit()

                # 如果需要自动分析
                if auto_analyze and dimensions:
                    # 重定向到分析页面
                    return redirect(url_for('v3_1.view_novel', novel_id=novel.id))

                # 重定向到小说详情页
                return redirect(url_for('v3_1.view_novel', novel_id=novel.id))
            except Exception as e:
                session.rollback()
                logger.error(f"上传小说时出错: {str(e)}", exc_info=True)
                return render_template('v3.1/upload_novel.html', error=f'上传小说时出错: {str(e)}')
            finally:
                session.close()
        except Exception as e:
            logger.error(f"上传小说时出错: {str(e)}", exc_info=True)
            return render_template('v3.1/upload_novel.html', error=f'上传小说时出错: {str(e)}')

    # 获取分析维度
    from config import ANALYSIS_DIMENSIONS
    return render_template('v3.1/upload_novel.html', dimensions=ANALYSIS_DIMENSIONS)

@v3_1_bp.route('/reference_templates')
def reference_templates():
    """参考蓝本"""
    return render_template('v3.1/reference_templates.html')

@v3_1_bp.route('/console')
def console():
    """控制台"""
    from datetime import datetime
    session = Session()
    try:
        # 获取参考蓝本列表
        templates = session.query(Novel).filter(
            Novel.novel_metadata.contains({'is_template': True})
        ).all()

        # 获取预设模板列表
        presets = session.query(Preset).filter_by(category='preset_template').all()

        # 统计展示台数据
        total_chapters = session.query(func.count(Chapter.id)).scalar() or 0
        total_words = session.query(func.sum(Novel.word_count)).scalar() or 0

        # 计算平均每章字数
        avg_words_per_chapter = 0
        if total_chapters > 0:
            avg_words_per_chapter = int(total_words / total_chapters)

        # 系统运行时间（小时）
        total_hours = 24

        # 创建展示台统计数据
        generated_content_stats = {
            'total_chapters': total_chapters,
            'total_words': total_words,
            'avg_words_per_chapter': avg_words_per_chapter,
            'total_hours': total_hours
        }

        return render_template('v3.1/console.html',
                             templates=templates,
                             presets=presets,
                             now=datetime.now(),
                             generated_content_stats=generated_content_stats)
    except Exception as e:
        logger.error(f"控制台页面加载出错: {str(e)}", exc_info=True)
        # 如果出错，提供默认数据
        default_stats = {
            'total_chapters': 0,
            'total_words': 0,
            'avg_words_per_chapter': 0,
            'total_hours': 0
        }
        return render_template('v3.1/console.html',
                             templates=[],
                             presets=[],
                             now=datetime.now(),
                             generated_content_stats=default_stats)
    finally:
        session.close()

@v3_1_bp.route('/showcase')
def showcase():
    """展示台"""
    session = Session()
    try:
        # 统计展示台数据
        total_chapters = session.query(func.count(Chapter.id)).scalar() or 0
        total_words = session.query(func.sum(Novel.word_count)).scalar() or 0

        # 计算平均每章字数
        avg_words_per_chapter = 0
        if total_chapters > 0:
            avg_words_per_chapter = int(total_words / total_chapters)

        # 系统运行时间（小时）
        total_hours = 24

        # 创建展示台统计数据
        generated_content_stats = {
            'total_chapters': total_chapters,
            'total_words': total_words,
            'avg_words_per_chapter': avg_words_per_chapter,
            'total_hours': total_hours
        }

        # 获取所有小说
        novels = session.query(Novel).filter(
            Novel.novel_metadata.is_(None) | ~Novel.novel_metadata.contains({'is_template': True})
        ).all()

        logger.info(f"展示台数据: {generated_content_stats}")

        return render_template('v3.1/showcase.html',
                             novels=novels,
                             generated_content_stats=generated_content_stats)
    except Exception as e:
        logger.error(f"展示台页面加载出错: {str(e)}", exc_info=True)
        # 如果出错，提供默认统计数据
        default_stats = {
            'total_chapters': 0,
            'total_words': 0,
            'avg_words_per_chapter': 0,
            'total_hours': 0
        }
        logger.info(f"使用默认展示台数据: {default_stats}")
        return render_template('v3.1/showcase.html',
                             novels=[],
                             generated_content_stats=default_stats)
    finally:
        session.close()

@v3_1_bp.route('/content_repository')
def content_repository_page():
    """内容仓库"""
    session = Session()
    try:
        # 获取最近分析的小说
        novels = session.query(Novel).filter(
            Novel.novel_metadata.is_(None) | ~Novel.novel_metadata.contains({'is_template': True})
        ).order_by(Novel.created_at.desc()).limit(5).all()

        # 获取参考蓝本列表
        reference_templates = session.query(Novel).filter(
            Novel.novel_metadata.contains({'is_template': True})
        ).order_by(Novel.created_at.desc()).limit(5).all()

        # 统计展示台数据
        total_chapters = session.query(func.count(Chapter.id)).scalar() or 0
        total_words = session.query(func.sum(Novel.word_count)).scalar() or 0

        # 计算平均每章字数
        avg_words_per_chapter = 0
        if total_chapters > 0:
            avg_words_per_chapter = int(total_words / total_chapters)

        # 系统运行时间（小时）
        total_hours = 24

        # 创建展示台统计数据
        generated_content_stats = {
            'total_chapters': total_chapters,
            'total_words': total_words,
            'avg_words_per_chapter': avg_words_per_chapter,
            'total_hours': total_hours
        }

        return render_template('v3.1/content_repository.html',
                              novels=novels,
                              reference_templates=reference_templates,
                              generated_content_stats=generated_content_stats)
    except Exception as e:
        logger.error(f"内容仓库页面加载出错: {str(e)}", exc_info=True)
        # 如果出错，提供默认统计数据
        default_stats = {
            'total_chapters': 0,
            'total_words': 0,
            'avg_words_per_chapter': 0,
            'total_hours': 0
        }
        return render_template('v3.1/content_repository.html',
                             novels=[],
                             reference_templates=[],
                             generated_content_stats=default_stats)
    finally:
        session.close()

@v3_1_bp.route('/help')
def help_page():
    """帮助中心"""
    session = Session()
    try:
        # 统计展示台数据
        total_chapters = session.query(func.count(Chapter.id)).scalar() or 0
        total_words = session.query(func.sum(Novel.word_count)).scalar() or 0

        # 计算平均每章字数
        avg_words_per_chapter = 0
        if total_chapters > 0:
            avg_words_per_chapter = int(total_words / total_chapters)

        # 系统运行时间（小时）
        total_hours = 24

        # 创建展示台统计数据
        generated_content_stats = {
            'total_chapters': total_chapters,
            'total_words': total_words,
            'avg_words_per_chapter': avg_words_per_chapter,
            'total_hours': total_hours
        }

        return render_template('v3.1/help.html',
                             generated_content_stats=generated_content_stats)
    except Exception as e:
        logger.error(f"帮助中心页面加载出错: {str(e)}", exc_info=True)
        # 如果出错，提供默认统计数据
        default_stats = {
            'total_chapters': 0,
            'total_words': 0,
            'avg_words_per_chapter': 0,
            'total_hours': 0
        }
        return render_template('v3.1/help.html',
                             generated_content_stats=default_stats)
    finally:
        session.close()

@v3_1_bp.route('/system_monitor')
def system_monitor():
    """系统监控"""
    session = Session()
    try:
        # 统计展示台数据
        total_chapters = session.query(func.count(Chapter.id)).scalar() or 0
        total_words = session.query(func.sum(Novel.word_count)).scalar() or 0

        # 计算平均每章字数
        avg_words_per_chapter = 0
        if total_chapters > 0:
            avg_words_per_chapter = int(total_words / total_chapters)

        # 系统运行时间（小时）
        total_hours = 24

        # 创建展示台统计数据
        generated_content_stats = {
            'total_chapters': total_chapters,
            'total_words': total_words,
            'avg_words_per_chapter': avg_words_per_chapter,
            'total_hours': total_hours
        }

        return render_template('v3.1/system_monitor.html',
                             generated_content_stats=generated_content_stats)
    except Exception as e:
        logger.error(f"系统监控页面加载出错: {str(e)}", exc_info=True)
        # 如果出错，提供默认统计数据
        default_stats = {
            'total_chapters': 0,
            'total_words': 0,
            'avg_words_per_chapter': 0,
            'total_hours': 0
        }
        return render_template('v3.1/system_monitor.html',
                             generated_content_stats=default_stats)
    finally:
        session.close()

@v3_1_bp.route('/preset/<int:preset_id>/templates')
def preset_template_detail(preset_id):
    """预设模板详情"""
    session = Session()
    try:
        # 获取预设模板
        preset = session.query(Preset).get(preset_id)
        if not preset:
            flash('未找到指定预设模板', 'danger')
            return redirect(url_for('v3_1.console'))

        # 获取参考蓝本
        template = None
        if preset.meta_info and preset.meta_info.get('template_id'):
            template_id = preset.meta_info.get('template_id')
            template = session.query(Novel).get(template_id)

        # 获取章节列表
        chapters = []
        if template:
            chapters = session.query(Chapter).filter_by(novel_id=template.id).order_by(Chapter.chapter_number).all()

        # 获取分析维度
        from config import ANALYSIS_DIMENSIONS

        # 确保preset.meta_info存在
        if not preset.meta_info:
            preset.meta_info = {}

        # 如果template存在但meta_info中没有template_id，添加它
        if template and not preset.meta_info.get('template_id'):
            preset.meta_info['template_id'] = template.id
            session.commit()

        # 记录日志
        logger.info(f"预设模板详情 [预设ID: {preset_id}, 模板ID: {template.id if template else None}, 章节数量: {len(chapters)}, 维度数量: {len(ANALYSIS_DIMENSIONS)}]")

        return render_template('v3.1/preset_template_detail.html',
                              preset=preset,
                              template=template,
                              chapters=chapters,
                              dimensions=ANALYSIS_DIMENSIONS)
    except Exception as e:
        logger.error(f"预设模板详情页面加载出错: {str(e)}", exc_info=True)
        flash(f'加载预设模板详情时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.console'))
    finally:
        session.close()

@v3_1_bp.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    """查看小说详情"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取章节列表
            chapters = session.query(Chapter).filter_by(novel_id=novel.id).order_by(Chapter.chapter_number).all()

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS

            # 获取已分析的维度
            analyzed_dimensions = []
            analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel.id).all()
            for result in analysis_results:
                if result.dimension not in analyzed_dimensions:
                    analyzed_dimensions.append(result.dimension)

            # 更新小说的已分析维度
            novel.analyzed_dimensions = analyzed_dimensions
            session.commit()

            return render_template('v3.1/view_novel.html', novel=novel, chapters=chapters, dimensions=ANALYSIS_DIMENSIONS)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看小说详情时出错: {str(e)}", exc_info=True)
        flash(f'查看小说详情时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.novels'))

@v3_1_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>')
def view_chapter(novel_id, chapter_id):
    """查看章节详情"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter or chapter.novel_id != novel.id:
                flash('未找到指定章节', 'danger')
                return redirect(url_for('v3_1.view_novel', novel_id=novel.id))

            # 获取上一章和下一章
            prev_chapter = session.query(Chapter).filter(
                Chapter.novel_id == novel.id,
                Chapter.chapter_number < chapter.chapter_number
            ).order_by(Chapter.chapter_number.desc()).first()

            next_chapter = session.query(Chapter).filter(
                Chapter.novel_id == novel.id,
                Chapter.chapter_number > chapter.chapter_number
            ).order_by(Chapter.chapter_number).first()

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS

            # 获取已分析的维度
            analyzed_dimensions = []
            analysis_results = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel.id, chapter_id=chapter.id
            ).all()
            for result in analysis_results:
                if result.dimension not in analyzed_dimensions:
                    analyzed_dimensions.append(result.dimension)

            # 更新章节的已分析维度
            chapter.analyzed_dimensions = analyzed_dimensions
            session.commit()

            return render_template('v3.1/view_chapter.html',
                                  novel=novel,
                                  chapter=chapter,
                                  prev_chapter=prev_chapter,
                                  next_chapter=next_chapter,
                                  dimensions=ANALYSIS_DIMENSIONS)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节详情时出错: {str(e)}", exc_info=True)
        flash(f'查看章节详情时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_novel', novel_id=novel_id))

@v3_1_bp.route('/novel/<int:novel_id>/chapters')
def chapters_list(novel_id):
    """章节列表"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取章节列表
            chapters = session.query(Chapter).filter_by(novel_id=novel.id).order_by(Chapter.chapter_number).all()

            # 获取已分析的章节数量
            analyzed_chapters = 0
            for chapter in chapters:
                if hasattr(chapter, 'analyzed_dimensions') and chapter.analyzed_dimensions:
                    analyzed_chapters += 1

            # 更新小说的已分析章节数量
            novel.analyzed_chapters = analyzed_chapters
            novel.chapter_count = len(chapters)
            session.commit()

            # 分页
            page = request.args.get('page', 1, type=int)
            per_page = 12
            total_pages = (len(chapters) + per_page - 1) // per_page
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            current_chapters = chapters[start_idx:end_idx]

            # 获取章节分析结果
            chapter_analysis_results = {}
            for chapter in chapters:
                results = session.query(ChapterAnalysisResult).filter_by(novel_id=novel.id, chapter_id=chapter.id).all()
                chapter_analysis_results[chapter.id] = {result.dimension: result for result in results}

            return render_template('v3.1/chapters_list.html',
                                  novel=novel,
                                  chapters=current_chapters,
                                  current_page=page,
                                  total_pages=total_pages,
                                  chapter_analysis_results=chapter_analysis_results)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节列表时出错: {str(e)}", exc_info=True)
        flash(f'查看章节列表时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_novel', novel_id=novel_id))

@v3_1_bp.route('/novel/<int:novel_id>/chapters/summary')
def chapters_summary(novel_id):
    """章节分析汇总"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取章节列表
            chapters = session.query(Chapter).filter_by(novel_id=novel.id).order_by(Chapter.chapter_number).all()

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS

            # 获取章节分析结果
            chapter_analyses = {}
            dimension_stats = {}
            chapter_analysis_counts = {0: 0, '1-5': 0, '6-10': 0, '11-14': 0, 15: 0}
            total_analyses = 0
            analyzed_dimensions = 0

            for chapter in chapters:
                chapter_id = chapter.id
                chapter_analyses[chapter_id] = {}

                # 获取章节的分析结果
                results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel.id, chapter_id=chapter_id
                ).all()

                analyzed_count = 0
                for result in results:
                    chapter_analyses[chapter_id][result.dimension] = result
                    total_analyses += 1
                    analyzed_count += 1

                    # 更新维度统计
                    if result.dimension in dimension_stats:
                        dimension_stats[result.dimension] += 1
                    else:
                        dimension_stats[result.dimension] = 1
                        analyzed_dimensions += 1

                # 更新章节分析计数
                if analyzed_count == 0:
                    chapter_analysis_counts[0] += 1
                elif analyzed_count <= 5:
                    chapter_analysis_counts['1-5'] += 1
                elif analyzed_count <= 10:
                    chapter_analysis_counts['6-10'] += 1
                elif analyzed_count <= 14:
                    chapter_analysis_counts['11-14'] += 1
                else:
                    chapter_analysis_counts[15] += 1

            # 计算完成度百分比
            total_possible = len(chapters) * len(ANALYSIS_DIMENSIONS)
            completion_percentage = (total_analyses / total_possible * 100) if total_possible > 0 else 0

            return render_template('v3.1/chapters_summary.html',
                                  novel=novel,
                                  chapters=chapters,
                                  dimensions=ANALYSIS_DIMENSIONS,
                                  chapter_analyses=chapter_analyses,
                                  dimension_stats=dimension_stats,
                                  chapter_analysis_counts=chapter_analysis_counts,
                                  total_analyses=total_analyses,
                                  analyzed_chapters=novel.analyzed_chapters,
                                  analyzed_dimensions=analyzed_dimensions,
                                  completion_percentage=round(completion_percentage, 1))
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节分析汇总时出错: {str(e)}", exc_info=True)
        flash(f'查看章节分析汇总时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_novel', novel_id=novel_id))

@v3_1_bp.route('/novel/<int:novel_id>/dimension/<string:dimension>')
def dimension_detail(novel_id, dimension):
    """维度详情"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS
            dimension_info = None
            for dim in ANALYSIS_DIMENSIONS:
                if dim['key'] == dimension:
                    dimension_info = dim
                    break

            if not dimension_info:
                flash('未找到指定分析维度', 'danger')
                return redirect(url_for('v3_1.view_novel', novel_id=novel.id))

            # 获取分析结果
            analysis = session.query(AnalysisResult).filter_by(
                novel_id=novel.id, dimension=dimension
            ).first()

            return render_template('v3.1/dimension_detail.html',
                                  novel=novel,
                                  dimension=dimension,
                                  dimension_info=dimension_info,
                                  analysis=analysis,
                                  dimensions=ANALYSIS_DIMENSIONS)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看维度详情时出错: {str(e)}", exc_info=True)
        flash(f'查看维度详情时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_novel', novel_id=novel_id))

@v3_1_bp.route('/analysis/<int:novel_id>/<string:dimension>')
def analysis_result(novel_id, dimension):
    """分析结果页面 - 用于API返回的结果URL"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS
            dimension_info = None
            for dim in ANALYSIS_DIMENSIONS:
                if dim['key'] == dimension:
                    dimension_info = dim
                    break

            if not dimension_info:
                flash('未找到指定分析维度', 'danger')
                return redirect(url_for('v3_1.view_novel', novel_id=novel.id))

            # 获取分析结果
            analysis = session.query(AnalysisResult).filter_by(
                novel_id=novel.id, dimension=dimension
            ).first()

            # 记录分析结果信息
            if analysis:
                logger.info(f"找到分析结果: novel_id={novel.id}, dimension={dimension}, content_length={len(analysis.content) if analysis.content else 0}")
                if hasattr(analysis, 'reasoning_content') and analysis.reasoning_content:
                    logger.info(f"分析结果包含推理过程，长度: {len(analysis.reasoning_content)}")
                else:
                    logger.info(f"分析结果不包含推理过程")
            else:
                logger.warning(f"未找到分析结果: novel_id={novel.id}, dimension={dimension}")

            # 如果找不到分析结果，重定向到分析页面
            if not analysis:
                flash(f'未找到维度 {dimension} 的分析结果，请先进行分析', 'warning')
                return redirect(url_for('v3_1.analyze_dimension', novel_id=novel.id, dimension=dimension))

            # 确保analysis对象包含所有必要的属性
            if analysis and not hasattr(analysis, 'reasoning_content'):
                analysis.reasoning_content = ''
                logger.info(f"为分析结果添加空的reasoning_content属性")

            return render_template('v3.1/analysis_result.html',
                                  novel=novel,
                                  dimension=dimension,
                                  dimension_info=dimension_info,
                                  analysis=analysis,
                                  dimensions=ANALYSIS_DIMENSIONS)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看分析结果时出错: {str(e)}", exc_info=True)
        flash(f'查看分析结果时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_novel', novel_id=novel_id))

@v3_1_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/dimension/<string:dimension>')
def chapter_dimension_detail(novel_id, chapter_id, dimension):
    """章节维度详情"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter or chapter.novel_id != novel.id:
                flash('未找到指定章节', 'danger')
                return redirect(url_for('v3_1.view_novel', novel_id=novel.id))

            # 获取上一章和下一章
            prev_chapter = session.query(Chapter).filter(
                Chapter.novel_id == novel.id,
                Chapter.chapter_number < chapter.chapter_number
            ).order_by(Chapter.chapter_number.desc()).first()

            next_chapter = session.query(Chapter).filter(
                Chapter.novel_id == novel.id,
                Chapter.chapter_number > chapter.chapter_number
            ).order_by(Chapter.chapter_number).first()

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS
            dimension_info = None
            for dim in ANALYSIS_DIMENSIONS:
                if dim['key'] == dimension:
                    dimension_info = dim
                    break

            if not dimension_info:
                flash('未找到指定分析维度', 'danger')
                return redirect(url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=chapter.id))

            # 获取分析结果
            analysis = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel.id, chapter_id=chapter.id, dimension=dimension
            ).first()

            return render_template('v3.1/chapter_dimension_detail.html',
                                  novel=novel,
                                  chapter=chapter,
                                  prev_chapter=prev_chapter,
                                  next_chapter=next_chapter,
                                  dimension=dimension,
                                  dimension_info=dimension_info,
                                  analysis=analysis,
                                  dimensions=ANALYSIS_DIMENSIONS)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节维度详情时出错: {str(e)}", exc_info=True)
        flash(f'查看章节维度详情时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

@v3_1_bp.route('/novel/<int:novel_id>/analyze_dimension')
def analyze_dimension(novel_id):
    """分析维度"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取维度
            dimension = request.args.get('dimension')
            force = request.args.get('force', 0, type=int)

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS
            dimension_info = None
            for dim in ANALYSIS_DIMENSIONS:
                if dim['key'] == dimension:
                    dimension_info = dim
                    break

            if not dimension_info:
                flash('未找到指定分析维度', 'danger')
                return redirect(url_for('v3_1.view_novel', novel_id=novel.id))

            return render_template('v3.1/analyze_dimension.html',
                                  novel=novel,
                                  dimension=dimension,
                                  dimension_info=dimension_info,
                                  force=force,
                                  now=datetime.now())
        finally:
            session.close()
    except Exception as e:
        logger.error(f"分析维度时出错: {str(e)}", exc_info=True)
        flash(f'分析维度时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_novel', novel_id=novel_id))

@v3_1_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze_dimension')
def analyze_chapter_dimension(novel_id, chapter_id):
    """分析章节维度"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter or chapter.novel_id != novel.id:
                flash('未找到指定章节', 'danger')
                return redirect(url_for('v3_1.view_novel', novel_id=novel.id))

            # 获取上一章和下一章
            prev_chapter = session.query(Chapter).filter(
                Chapter.novel_id == novel.id,
                Chapter.chapter_number < chapter.chapter_number
            ).order_by(Chapter.chapter_number.desc()).first()

            next_chapter = session.query(Chapter).filter(
                Chapter.novel_id == novel.id,
                Chapter.chapter_number > chapter.chapter_number
            ).order_by(Chapter.chapter_number).first()

            # 获取维度
            dimension = request.args.get('dimension')
            force = request.args.get('force', 0, type=int)

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS
            dimension_info = None
            for dim in ANALYSIS_DIMENSIONS:
                if dim['key'] == dimension:
                    dimension_info = dim
                    break

            if not dimension_info:
                flash('未找到指定分析维度', 'danger')
                return redirect(url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=chapter.id))

            return render_template('v3.1/analyze_chapter_dimension.html',
                                  novel=novel,
                                  chapter=chapter,
                                  prev_chapter=prev_chapter,
                                  next_chapter=next_chapter,
                                  dimension=dimension,
                                  dimension_info=dimension_info,
                                  force=force,
                                  now=datetime.now())
        finally:
            session.close()
    except Exception as e:
        logger.error(f"分析章节维度时出错: {str(e)}", exc_info=True)
        flash(f'分析章节维度时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

@v3_1_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze')
def analyze_chapter(novel_id, chapter_id):
    """分析章节"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash('未找到指定小说', 'danger')
                return redirect(url_for('v3_1.novels'))

            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter or chapter.novel_id != novel.id:
                flash('未找到指定章节', 'danger')
                return redirect(url_for('v3_1.view_novel', novel_id=novel.id))

            # 获取上一章和下一章
            prev_chapter = session.query(Chapter).filter(
                Chapter.novel_id == novel.id,
                Chapter.chapter_number < chapter.chapter_number
            ).order_by(Chapter.chapter_number.desc()).first()

            next_chapter = session.query(Chapter).filter(
                Chapter.novel_id == novel.id,
                Chapter.chapter_number > chapter.chapter_number
            ).order_by(Chapter.chapter_number).first()

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS

            # 获取已分析的维度
            analyzed_dimensions = []
            analysis_results = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel.id, chapter_id=chapter.id
            ).all()
            for result in analysis_results:
                if result.dimension not in analyzed_dimensions:
                    analyzed_dimensions.append(result.dimension)

            # 更新章节的已分析维度
            chapter.analyzed_dimensions = analyzed_dimensions
            session.commit()

            # 获取正在分析的维度
            analyzing_dimensions = []  # 这里需要从分析任务队列中获取，暂时为空

            return render_template('v3.1/analyze_chapter.html',
                                  novel=novel,
                                  chapter=chapter,
                                  prev_chapter=prev_chapter,
                                  next_chapter=next_chapter,
                                  dimensions=ANALYSIS_DIMENSIONS,
                                  analyzing_dimensions=analyzing_dimensions)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"分析章节时出错: {str(e)}", exc_info=True)
        flash(f'分析章节时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

@v3_1_bp.route('/novel/<int:novel_id>/analyze_all_chapters')
def analyze_all_chapters(novel_id):
    """分析所有章节"""
    try:
        # 重定向到章节列表页面
        flash('已开始分析所有章节，请在章节列表页面查看进度', 'success')
        return redirect(url_for('v3_1.chapters_list', novel_id=novel_id))
    except Exception as e:
        logger.error(f"分析所有章节时出错: {str(e)}", exc_info=True)
        flash(f'分析所有章节时出错: {str(e)}', 'danger')
        return redirect(url_for('v3_1.view_novel', novel_id=novel_id))

@v3_1_bp.route('/view_templates')
@v3_1_bp.route('/novel/<int:novel_id>/view_templates')
def view_templates(novel_id=None):
    """查看设定模板"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = None
            if novel_id:
                novel = session.query(Novel).get(novel_id)
                if not novel:
                    flash('未找到指定小说', 'danger')
                    return redirect(url_for('v3_1.novels'))

            # 获取预设模板列表
            presets = session.query(Preset).filter_by(category='preset_template').all()

            return render_template('v3.1/view_templates.html', novel=novel, presets=presets)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看设定模板时出错: {str(e)}", exc_info=True)
        flash(f'查看设定模板时出错: {str(e)}', 'danger')
        if novel_id:
            return redirect(url_for('v3_1.view_novel', novel_id=novel_id))
        else:
            return redirect(url_for('v3_1.console'))
