"""
Analysis checkpoint data model for the 九猫 (Nine Cats) novel analysis system.
"""
from datetime import datetime, timezone
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from src.models.base import Base

class AnalysisCheckpoint(Base):
    """Analysis checkpoint data model for resuming interrupted analyses."""

    __tablename__ = "analysis_checkpoints"

    id = Column(Integer, primary_key=True)
    novel_id = Column(Integer, ForeignKey("novels.id"), nullable=False)
    dimension = Column(String(255), nullable=False)
    current_block = Column(Integer, default=0)
    total_blocks = Column(Integer, default=0)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationship with Novel
    novel = relationship("Novel", back_populates="analysis_checkpoints")

    def __init__(
        self,
        novel_id: int,
        dimension: str,
        current_block: int = 0,
        total_blocks: int = 0
    ):
        """
        Initialize an analysis checkpoint.

        Args:
            novel_id: ID of the analyzed novel.
            dimension: Analysis dimension.
            current_block: Current processing block index.
            total_blocks: Total number of blocks in the analysis.
        """
        self.novel_id = novel_id
        self.dimension = dimension
        self.current_block = current_block
        self.total_blocks = total_blocks
