/**
 * 九猫系统 - 数据库连接池优化脚本
 * 用于优化数据库连接使用和避免连接池耗尽
 */

(function() {
    // 配置参数
    const CONFIG = {
        enabled: true,                   // 是否启用数据库优化
        reserveConnections: 5,           // 预留连接数，用于关键操作
        gracefulRetryDelay: 2000,        // 优雅重试延迟(ms)
        maxConnectionRetries: 5,         // 最大连接重试次数
        enableSmartBatching: true,       // 启用智能批处理
        batchPageSize: 50,               // 批处理页面大小
        healthCheckInterval: 60000,      // 健康检查间隔(ms)
        logToConsole: true               // 记录日志到控制台
    };

    // 连接池统计信息
    let poolStats = {
        maxSize: 0,
        usedConnections: 0,
        availableConnections: 0,
        pendingConnections: 0,
        lastUpdated: null
    };

    // 批处理队列
    let batchQueue = [];
    let batchTimer = null;
    
    // 初始化
    function init() {
        if (!CONFIG.enabled) {
            console.log('[数据库优化] 数据库优化功能未启用');
            return;
        }
        
        console.log('[数据库优化] 启动数据库连接池优化...');
        
        // 启动定期健康检查
        startHealthCheck();
        
        // 监听分析页面请求
        setupAnalysisRequestOptimization();
        
        // 设置批处理
        if (CONFIG.enableSmartBatching) {
            setupSmartBatching();
        }
        
        console.log('[数据库优化] 数据库连接池优化已初始化');
    }
    
    // 启动连接池健康检查
    function startHealthCheck() {
        getPoolStats().then(stats => {
            updatePoolStats(stats);
            
            // 如果池统计信息可用，记录初始状态
            if (stats && stats.maxSize) {
                logPoolStats();
            }
        });
        
        // 设置定期健康检查
        setInterval(() => {
            getPoolStats().then(stats => {
                updatePoolStats(stats);
                
                // 检查连接池状态
                checkPoolHealth();
            });
        }, CONFIG.healthCheckInterval);
    }
    
    // 获取连接池统计信息
    function getPoolStats() {
        return fetch('/api/system/db_pool_stats')
            .then(response => {
                if (!response.ok) {
                    console.log('[数据库优化] 获取连接池统计信息请求失败:', response.status);
                    return null;
                }
                return response.json();
            })
            .then(data => {
                if (data && data.success) {
                    return data.stats;
                }
                if (data) {
                    throw new Error('无法获取连接池统计信息');
                }
                return null;
            })
            .catch(error => {
                console.error('[数据库优化] 获取连接池统计信息失败:', error);
                return null;
            });
    }
    
    // 更新连接池统计信息
    function updatePoolStats(stats) {
        if (!stats) return;
        
        poolStats = {
            maxSize: stats.max_size || poolStats.maxSize,
            usedConnections: stats.used || poolStats.usedConnections,
            availableConnections: stats.available || poolStats.availableConnections,
            pendingConnections: stats.pending || poolStats.pendingConnections,
            lastUpdated: new Date()
        };
    }
    
    // 记录连接池统计信息
    function logPoolStats() {
        if (!CONFIG.logToConsole) return;
        
        console.log('[数据库优化] 连接池状态:',
            `总大小=${poolStats.maxSize}`,
            `已用=${poolStats.usedConnections}`,
            `可用=${poolStats.availableConnections}`,
            `等待=${poolStats.pendingConnections}`);
    }
    
    // 检查连接池健康状态
    function checkPoolHealth() {
        const availableRatio = poolStats.availableConnections / poolStats.maxSize;
        const pendingCount = poolStats.pendingConnections;
        
        // 检查连接池是否接近耗尽
        if (availableRatio < 0.2 || pendingCount > 5) {
            console.warn('[数据库优化] 连接池压力大:',
                `可用率=${(availableRatio * 100).toFixed(2)}%`,
                `等待连接=${pendingCount}`);
            
            // 添加警告到页面
            showPoolWarning();
            
            // 采取措施减轻连接池压力
            reducePoolPressure();
        } else {
            // 隐藏警告
            hidePoolWarning();
        }
        
        // 记录统计信息
        logPoolStats();
    }
    
    // 显示连接池警告
    function showPoolWarning() {
        // 检查警告是否已存在
        if (document.getElementById('db-pool-warning')) {
            return;
        }
        
        // 创建警告元素
        const warningElement = document.createElement('div');
        warningElement.id = 'db-pool-warning';
        warningElement.className = 'alert alert-warning alert-dismissible fade show fixed-top m-3';
        warningElement.innerHTML = `
            <strong>数据库连接池压力大</strong>
            <p>当前可用连接: ${poolStats.availableConnections}/${poolStats.maxSize}</p>
            <p>正在等待的连接: ${poolStats.pendingConnections}</p>
            <p>这可能导致页面加载缓慢或错误。建议减少同时分析的维度数量。</p>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
        `;
        
        // 添加到页面
        document.body.appendChild(warningElement);
        
        // 添加关闭按钮事件
        const closeButton = warningElement.querySelector('.btn-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                warningElement.remove();
            });
        }
    }
    
    // 隐藏连接池警告
    function hidePoolWarning() {
        const warningElement = document.getElementById('db-pool-warning');
        if (warningElement) {
            warningElement.remove();
        }
    }
    
    // 减轻连接池压力的措施
    function reducePoolPressure() {
        // 1. 延迟非关键请求
        delayNonCriticalRequests();
        
        // 2. 取消冗余请求
        cancelRedundantRequests();
        
        // 3. 合并类似请求
        batchSimilarRequests();
    }
    
    // 延迟非关键请求
    function delayNonCriticalRequests() {
        // 将待处理的非关键请求添加到队列
        const pendingFetchRequests = window._pendingFetchRequests || [];
        
        for (let i = 0; i < pendingFetchRequests.length; i++) {
            const request = pendingFetchRequests[i];
            
            // 检查是否是非关键请求
            if (request && request.url && !isHighPriorityRequest(request.url)) {
                // 延迟非关键请求
                pendingFetchRequests[i].delay = true;
            }
        }
    }
    
    // 取消冗余请求
    function cancelRedundantRequests() {
        // 将待处理的请求按URL分组
        const pendingFetchRequests = window._pendingFetchRequests || [];
        const requestsByUrl = {};
        
        for (let i = 0; i < pendingFetchRequests.length; i++) {
            const request = pendingFetchRequests[i];
            
            if (request && request.url) {
                if (!requestsByUrl[request.url]) {
                    requestsByUrl[request.url] = [];
                }
                
                requestsByUrl[request.url].push(request);
            }
        }
        
        // 对于每个URL，只保留一个请求
        for (const url in requestsByUrl) {
            const requests = requestsByUrl[url];
            
            if (requests.length > 1) {
                console.log(`[数据库优化] 发现冗余请求: ${url} (${requests.length}个)`);
                
                // 保留第一个请求，取消其余请求
                for (let i = 1; i < requests.length; i++) {
                    requests[i].cancel = true;
                }
            }
        }
    }
    
    // 合并类似请求
    function batchSimilarRequests() {
        // 启用批处理，合并类似请求
        if (CONFIG.enableSmartBatching) {
            processBatchQueue();
        }
    }
    
    // 检查是否是高优先级请求
    function isHighPriorityRequest(url) {
        // 定义高优先级请求的模式
        const highPriorityPatterns = [
            '/api/user/',
            '/api/auth/',
            '/api/system/health',
            '/api/system/db_pool_stats'
        ];
        
        return highPriorityPatterns.some(pattern => url.includes(pattern));
    }
    
    // 设置分析请求优化
    function setupAnalysisRequestOptimization() {
        // 拦截fetch请求
        const originalFetch = window.fetch;
        
        // 创建待处理请求数组
        window._pendingFetchRequests = window._pendingFetchRequests || [];
        
        window.fetch = function(resource, init) {
            const url = (resource instanceof Request) ? resource.url : resource;
            
            // 检查是否是分析相关请求
            if (typeof url === 'string' && url.includes('/api/analysis/')) {
                // 如果连接池压力大，延迟非关键请求
                if (poolStats.availableConnections < CONFIG.reserveConnections && !isHighPriorityRequest(url)) {
                    console.log(`[数据库优化] 延迟非关键请求: ${url}`);
                    
                    return new Promise((resolve, reject) => {
                        // 创建请求对象
                        const requestObj = {
                            url: url,
                            init: init,
                            resolve: resolve,
                            reject: reject,
                            delay: true,
                            retryCount: 0
                        };
                        
                        // 添加到待处理队列
                        window._pendingFetchRequests.push(requestObj);
                        
                        // 设置延迟执行
                        setTimeout(() => {
                            processPendingRequest(requestObj);
                        }, CONFIG.gracefulRetryDelay);
                    });
                }
                
                // 检查是否可以批处理这个请求
                if (CONFIG.enableSmartBatching && canBatchRequest(url)) {
                    return batchRequest(url, init);
                }
            }
            
            // 正常执行请求
            return originalFetch.apply(this, arguments);
        };
        
        // 定期处理待处理请求
        setInterval(processPendingRequests, CONFIG.gracefulRetryDelay);
    }
    
    // 处理待处理请求
    function processPendingRequests() {
        const pendingRequests = window._pendingFetchRequests || [];
        
        // 处理最长等待的请求，FIFO
        const maxProcessCount = Math.min(poolStats.availableConnections - CONFIG.reserveConnections, 5);
        
        if (maxProcessCount <= 0 || pendingRequests.length === 0) {
            return;
        }
        
        console.log(`[数据库优化] 处理${Math.min(maxProcessCount, pendingRequests.length)}个待处理请求，共${pendingRequests.length}个`);
        
        // 处理请求
        for (let i = 0; i < maxProcessCount && pendingRequests.length > 0; i++) {
            const request = pendingRequests.shift();
            processPendingRequest(request);
        }
    }
    
    // 处理单个待处理请求
    function processPendingRequest(request) {
        if (!request || request.cancel) {
            return;
        }
        
        // 检查重试次数
        if (request.retryCount >= CONFIG.maxConnectionRetries) {
            console.error(`[数据库优化] 请求重试次数过多: ${request.url}`);
            request.reject(new Error('请求重试次数过多'));
            return;
        }
        
        // 执行请求
        const originalFetch = window.fetch;
        originalFetch(request.url, request.init)
            .then(response => {
                request.resolve(response);
            })
            .catch(error => {
                // 如果是连接错误，重试
                if (error.message.includes('connection') || error.message.includes('timeout')) {
                    console.warn(`[数据库优化] 连接错误，将重试: ${request.url}`);
                    
                    // 递增重试计数
                    request.retryCount++;
                    
                    // 重新添加到队列
                    window._pendingFetchRequests.push(request);
                } else {
                    request.reject(error);
                }
            });
    }
    
    // 设置智能批处理
    function setupSmartBatching() {
        // 拦截查询参数类似的请求
        console.log('[数据库优化] 启用智能批处理');
    }
    
    // 检查请求是否可以批处理
    function canBatchRequest(url) {
        // 实现批处理逻辑
        // 检查URL是否包含可批处理的模式
        const batchablePatterns = [
            '/api/analysis/result',
            '/api/novel/metadata'
        ];
        
        return batchablePatterns.some(pattern => url.includes(pattern));
    }
    
    // 批处理请求
    function batchRequest(url, init) {
        return new Promise((resolve, reject) => {
            // 创建批处理请求
            const batchItem = {
                url: url,
                init: init,
                resolve: resolve,
                reject: reject
            };
            
            // 添加到批处理队列
            batchQueue.push(batchItem);
            
            // 如果队列变大，立即处理
            if (batchQueue.length >= CONFIG.batchPageSize) {
                processBatchQueue();
            } else {
                // 否则，设置一个计时器
                if (!batchTimer) {
                    batchTimer = setTimeout(processBatchQueue, 100);
                }
            }
        });
    }
    
    // 处理批处理队列
    function processBatchQueue() {
        // 清除计时器
        if (batchTimer) {
            clearTimeout(batchTimer);
            batchTimer = null;
        }
        
        // 检查队列是否为空
        if (batchQueue.length === 0) {
            return;
        }
        
        console.log(`[数据库优化] 处理批处理队列，共${batchQueue.length}个请求`);
        
        // 将队列复制一份并清空原队列
        const currentBatch = batchQueue.slice();
        batchQueue = [];
        
        // 按URL分组
        const batchesByUrl = {};
        
        for (const item of currentBatch) {
            const urlBase = item.url.split('?')[0];
            
            if (!batchesByUrl[urlBase]) {
                batchesByUrl[urlBase] = [];
            }
            
            batchesByUrl[urlBase].push(item);
        }
        
        // 对每组URL执行批处理
        for (const urlBase in batchesByUrl) {
            const items = batchesByUrl[urlBase];
            
            if (items.length === 1) {
                // 只有一个请求，直接执行
                const item = items[0];
                window.fetch(item.url, item.init)
                    .then(response => item.resolve(response))
                    .catch(error => item.reject(error));
            } else {
                // 多个请求，创建批处理
                executeBatchedRequest(urlBase, items);
            }
        }
    }
    
    // 执行批处理请求
    function executeBatchedRequest(urlBase, items) {
        // 提取所有请求的查询参数
        const queryParams = items.map(item => {
            const url = new URL(item.url, window.location.origin);
            return Object.fromEntries(url.searchParams.entries());
        });
        
        // 创建批处理请求
        const batchUrl = urlBase + '/batch';
        const batchInit = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                requests: queryParams
            })
        };
        
        // 执行批处理请求
        window.fetch(batchUrl, batchInit)
            .then(response => response.json())
            .then(data => {
                if (data.success && Array.isArray(data.results)) {
                    // 将结果分配给各个请求
                    for (let i = 0; i < items.length && i < data.results.length; i++) {
                        const result = data.results[i];
                        
                        // 创建Response对象
                        const responseInit = {
                            status: result.status || 200,
                            statusText: result.statusText || 'OK',
                            headers: result.headers || {}
                        };
                        
                        const responseBody = JSON.stringify(result.data || {});
                        const response = new Response(responseBody, responseInit);
                        
                        // 解析请求
                        items[i].resolve(response);
                    }
                } else {
                    // 批处理失败，单独执行每个请求
                    for (const item of items) {
                        window.fetch(item.url, item.init)
                            .then(response => item.resolve(response))
                            .catch(error => item.reject(error));
                    }
                }
            })
            .catch(error => {
                console.error('[数据库优化] 批处理请求失败:', error);
                
                // 批处理失败，单独执行每个请求
                for (const item of items) {
                    window.fetch(item.url, item.init)
                        .then(response => item.resolve(response))
                        .catch(error => item.reject(error));
                }
            });
    }
    
    // 启动初始化
    init();
})(); 