{% extends "base.html" %}

{% block title %}{{ novel.title }} - {{ result.dimension }} 分析过程 - 九猫{% endblock %}

{% block extra_css %}
<style>
    .process-timeline {
        position: relative;
        margin-bottom: 2rem;
    }

    .timeline-track {
        position: absolute;
        top: 20px;
        bottom: 0;
        left: 30px;
        width: 4px;
        background-color: #e9ecef;
        z-index: 0;
    }

    .timeline-node {
        position: relative;
        margin-bottom: 1.5rem;
        padding-left: 50px;
    }

    .timeline-node::before {
        content: '';
        position: absolute;
        left: 22px;
        top: 20px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #ffffff;
        border: 4px solid #007bff;
        z-index: 1;
    }

    .timeline-node.init::before {
        border-color: #6c757d;
    }

    .timeline-node.chunk_analysis::before {
        border-color: #007bff;
    }

    .timeline-node.combine::before {
        border-color: #28a745;
    }

    .timeline-node.finalize::before {
        border-color: #dc3545;
    }

    .timeline-node.error::before {
        border-color: #dc3545;
        background-color: #f8d7da;
    }

    .timeline-card {
        border-radius: 0.25rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }

    .timeline-card .card-header {
        padding: 0.5rem 1rem;
    }

    .process-details {
        font-family: monospace;
        white-space: pre-wrap;
        font-size: 0.85rem;
        overflow-x: auto;
    }

    .process-details code {
        display: block;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        border: 1px solid #e9ecef;
        margin-bottom: 0.5rem;
    }

    .stage-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .process-filter-container {
        position: sticky;
        top: 0;
        background-color: #ffffff;
        padding: 1rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
        z-index: 10;
    }

    .prompt-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
        background-color: #f8f9fa;
        color: #495057;
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
    }

    .api-details {
        border: 1px dashed #dee2e6;
        padding: 0.5rem;
        margin-top: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
    }

    .api-details pre {
        margin-bottom: 0;
        font-size: 0.75rem;
    }

    .api-toggle {
        font-size: 0.85rem;
        margin-top: 0.5rem;
        color: #007bff;
        cursor: pointer;
    }

    .api-toggle:hover {
        text-decoration: underline;
    }

    .process-navigation {
        position: sticky;
        top: 80px;
        max-height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .process-summary-card {
        margin-bottom: 1rem;
    }

    .step-link {
        display: block;
        padding: 0.5rem;
        margin-bottom: 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.85rem;
        color: #495057;
        text-decoration: none;
    }

    .step-link:hover {
        background-color: #f8f9fa;
        text-decoration: none;
    }

    .step-link.active {
        background-color: #007bff;
        color: #ffffff;
    }

    .summary-value {
        font-weight: bold;
    }

    .chunk-badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
        margin-left: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 col-xl-2">
            <div class="process-navigation">
                <div class="card process-summary-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">分析摘要</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>小说：</strong>{{ novel.title }}</p>
                        <p><strong>维度：</strong>{{ result.dimension }}</p>
                        <p><strong>总分块：</strong><span class="summary-value">{{ processes|length }}</span> 块</p>
                        <p><strong>分析时间：</strong><span class="summary-value">{{ result.created_at.strftime('%Y-%m-%d %H:%M') }}</span></p>
                        <!-- 处理时间已禁用以避免序列化错误 -->
                        <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary btn-sm btn-block mt-3">返回小说页面</a>
                        <a href="{{ url_for('view_analysis', novel_id=novel.id, dimension=result.dimension) }}" class="btn btn-outline-primary btn-sm btn-block">返回分析结果</a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">处理步骤</h5>
                    </div>
                    <div class="card-body p-2">
                        <div class="list-group list-group-flush">
                            {% for stage in stages %}
                            <div class="mb-2">
                                <h6 class="text-muted">{{ stage.name }}</h6>
                                {% for process in stage.processes %}
                                <a href="#process-{{ process.id }}" class="step-link" data-stage="{{ process.processing_stage }}">
                                    {% if process.processing_stage == 'init' %}
                                    <i class="fas fa-rocket mr-2"></i>
                                    {% elif process.processing_stage == 'chunk_analysis' %}
                                    <i class="fas fa-cube mr-2"></i>
                                    {% elif process.processing_stage == 'combine' %}
                                    <i class="fas fa-object-group mr-2"></i>
                                    {% elif process.processing_stage == 'finalize' %}
                                    <i class="fas fa-check-circle mr-2"></i>
                                    {% else %}
                                    <i class="fas fa-cog mr-2"></i>
                                    {% endif %}
                                    {{ process.stage_name }}
                                    {% if process.block_index >= 0 %}
                                    <span class="badge badge-light chunk-badge">块 {{ process.block_index + 1 }}/{{ process.total_blocks }}</span>
                                    {% endif %}
                                </a>
                                {% endfor %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-9 col-xl-10">
            <div class="process-filter-container">
                <h2>{{ novel.title }} - {{ result.dimension }} 分析过程</h2>
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary stage-filter" data-stage="all">全部步骤</button>
                            <button type="button" class="btn btn-outline-secondary stage-filter" data-stage="init">初始化</button>
                            <button type="button" class="btn btn-outline-primary stage-filter" data-stage="chunk_analysis">分块分析</button>
                            <button type="button" class="btn btn-outline-success stage-filter" data-stage="combine">结果合并</button>
                            <button type="button" class="btn btn-outline-danger stage-filter" data-stage="finalize">最终处理</button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showApiDetails">
                            <label class="form-check-label" for="showApiDetails">
                                显示API详情
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showPromptDetails">
                            <label class="form-check-label" for="showPromptDetails">
                                显示提示词
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="process-timeline">
                <div class="timeline-track"></div>

                {% for process in processes %}
                <div id="process-{{ process.id }}" class="timeline-node {{ process.processing_stage }} {% if not process.is_successful %}error{% endif %}" data-stage="{{ process.processing_stage }}">
                    <div class="card timeline-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                {% if process.processing_stage == 'init' %}
                                <span class="badge badge-secondary stage-badge">初始化</span>
                                {% elif process.processing_stage == 'chunk_analysis' %}
                                <span class="badge badge-primary stage-badge">分块分析</span>
                                {% elif process.processing_stage == 'combine' %}
                                <span class="badge badge-success stage-badge">结果合并</span>
                                {% elif process.processing_stage == 'finalize' %}
                                <span class="badge badge-danger stage-badge">最终处理</span>
                                {% else %}
                                <span class="badge badge-info stage-badge">{{ process.processing_stage }}</span>
                                {% endif %}

                                {% if process.block_index >= 0 %}
                                <span class="ml-2">块 {{ process.block_index + 1 }}/{{ process.total_blocks }}</span>
                                {% endif %}

                                {% if process.is_successful %}
                                <span class="badge badge-success ml-2">成功</span>
                                {% else %}
                                <span class="badge badge-danger ml-2">失败</span>
                                {% endif %}
                            </h5>
                            <small class="text-muted">{{ process.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    {% if process.processing_time %}
                                    <span class="badge badge-light">处理时间: {{ process.processing_time }}ms</span>
                                    {% endif %}

                                    {% if process.tokens_used %}
                                    <span class="badge badge-light">使用令牌: {{ process.tokens_used }}</span>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 text-right">
                                    {% if process.prompt_used %}
                                    <a href="#" class="prompt-toggle" data-target="prompt-{{ process.id }}">显示提示词</a>
                                    {% endif %}

                                    {% if process.api_request or process.api_response %}
                                    <a href="#" class="api-toggle ml-3" data-target="api-{{ process.id }}">显示API详情</a>
                                    {% endif %}
                                </div>
                            </div>

                            {% if process.prompt_used %}
                            <div id="prompt-{{ process.id }}" class="prompt-details mt-3" style="display: none;">
                                <h6>提示词:</h6>
                                <pre><code>{{ process.prompt_used }}</code></pre>
                            </div>
                            {% endif %}

                            {% if process.input_text %}
                            <div class="process-details mt-3">
                                <h6>输入文本:</h6>
                                <div class="text-preview">{{ process.input_text|truncate(300) }}</div>
                                {% if process.input_text|length > 300 %}
                                <a href="#" class="toggle-text" data-target="input-{{ process.id }}">显示完整文本</a>
                                <div id="input-{{ process.id }}" class="full-text" style="display: none;">
                                    <pre><code>{{ process.input_text }}</code></pre>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if process.output_text %}
                            <div class="process-details mt-3">
                                <h6>输出文本:</h6>
                                <div class="text-preview">{{ process.output_text|truncate(300) }}</div>
                                {% if process.output_text|length > 300 %}
                                <a href="#" class="toggle-text" data-target="output-{{ process.id }}">显示完整文本</a>
                                <div id="output-{{ process.id }}" class="full-text" style="display: none;">
                                    <pre><code>{{ process.output_text }}</code></pre>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if process.error_message %}
                            <div class="alert alert-danger mt-3">
                                <h6>错误信息:</h6>
                                <pre>{{ process.error_message }}</pre>
                            </div>
                            {% endif %}

                            {% if process.api_request or process.api_response %}
                            <div id="api-{{ process.id }}" class="api-details mt-3" style="display: none;">
                                {% if process.api_request %}
                                <h6>API请求:</h6>
                                <pre>{{ process.api_request }}</pre>
                                {% endif %}

                                {% if process.api_response %}
                                <h6>API响应:</h6>
                                <pre>{{ process.api_response }}</pre>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入分析过程页面修复脚本 -->
<script src="{{ url_for('static', filename='js/analysis-process-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-process-fix.js';"></script>
<!-- 引入分析过程页面优化脚本 -->
<script src="{{ url_for('static', filename='js/analysis-process-optimizer.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-process-optimizer.js';"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 阶段过滤
    const stageFilters = document.querySelectorAll('.stage-filter');
    const timelineNodes = document.querySelectorAll('.timeline-node');

    stageFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            const stage = this.dataset.stage;

            // 更新过滤按钮样式
            stageFilters.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // 过滤时间线节点
            timelineNodes.forEach(node => {
                if (stage === 'all' || node.dataset.stage === stage) {
                    node.style.display = '';
                } else {
                    node.style.display = 'none';
                }
            });
        });
    });

    // 默认选中"全部"按钮
    document.querySelector('.stage-filter[data-stage="all"]').click();

    // 提示词切换
    const promptToggles = document.querySelectorAll('.prompt-toggle');
    promptToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.dataset.target;
            const target = document.getElementById(targetId);
            if (target.style.display === 'none') {
                target.style.display = 'block';
                this.textContent = '隐藏提示词';
            } else {
                target.style.display = 'none';
                this.textContent = '显示提示词';
            }
        });
    });

    // API详情切换
    const apiToggles = document.querySelectorAll('.api-toggle');
    apiToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.dataset.target;
            const target = document.getElementById(targetId);
            if (target.style.display === 'none') {
                target.style.display = 'block';
                this.textContent = '隐藏API详情';
            } else {
                target.style.display = 'none';
                this.textContent = '显示API详情';
            }
        });
    });

    // 文本展开/折叠
    const textToggles = document.querySelectorAll('.toggle-text');
    textToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.dataset.target;
            const target = document.getElementById(targetId);
            if (target.style.display === 'none') {
                target.style.display = 'block';
                this.textContent = '收起文本';
            } else {
                target.style.display = 'none';
                this.textContent = '显示完整文本';
            }
        });
    });

    // 侧边栏步骤链接激活状态
    const stepLinks = document.querySelectorAll('.step-link');

    function updateActiveStep() {
        const scrollPosition = window.scrollY;

        let activeNode = null;

        // 找到当前视口中的第一个节点
        document.querySelectorAll('.timeline-node:not([style*="display: none"])').forEach(node => {
            const nodeTop = node.offsetTop;
            const nodeHeight = node.offsetHeight;

            if (nodeTop <= scrollPosition + 200 && nodeTop + nodeHeight > scrollPosition) {
                if (!activeNode) {
                    activeNode = node;
                }
            }
        });

        if (activeNode) {
            const activeId = activeNode.id;

            // 更新活动链接
            stepLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${activeId}`) {
                    link.classList.add('active');

                    // 滚动导航使活动项可见
                    const nav = document.querySelector('.process-navigation');
                    if (nav && link) {
                        const linkTop = link.offsetTop;
                        const navHeight = nav.offsetHeight;
                        const navScrollTop = nav.scrollTop;

                        if (linkTop < navScrollTop || linkTop > navScrollTop + navHeight - 50) {
                            nav.scrollTo({
                                top: linkTop - navHeight / 2,
                                behavior: 'smooth'
                            });
                        }
                    }
                }
            });
        }
    }

    // 监听滚动以更新活动步骤
    window.addEventListener('scroll', updateActiveStep);

    // 点击步骤链接滚动到相应节点
    stepLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 100,
                    behavior: 'smooth'
                });

                // 更新活动状态
                stepLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });

    // API和提示词详情全局切换
    const apiDetailsCheckbox = document.getElementById('showApiDetails');
    const promptDetailsCheckbox = document.getElementById('showPromptDetails');

    apiDetailsCheckbox.addEventListener('change', function() {
        const apiDetails = document.querySelectorAll('.api-details');
        apiDetails.forEach(details => {
            details.style.display = this.checked ? 'block' : 'none';
        });

        // 更新切换按钮文本
        apiToggles.forEach(toggle => {
            toggle.textContent = this.checked ? '隐藏API详情' : '显示API详情';
        });
    });

    promptDetailsCheckbox.addEventListener('change', function() {
        const promptDetails = document.querySelectorAll('.prompt-details');
        promptDetails.forEach(details => {
            details.style.display = this.checked ? 'block' : 'none';
        });

        // 更新切换按钮文本
        promptToggles.forEach(toggle => {
            toggle.textContent = this.checked ? '隐藏提示词' : '显示提示词';
        });
    });

    // 初始化活动步骤
    updateActiveStep();
});
</script>
{% endblock %}