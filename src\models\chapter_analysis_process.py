"""
Chapter analysis process data model for the 九猫 (Nine Cats) novel analysis system.
"""
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Boolean, ForeignKey
from sqlalchemy.orm import relationship

from src.models.base import Base

# 创建一个函数来替代已弃用的datetime.utcnow
def utc_now():
    """返回当前的UTC时间，替代已弃用的datetime.utcnow"""
    return datetime.now(timezone.utc)

class ChapterAnalysisProcess(Base):
    """Chapter analysis process data model."""

    __tablename__ = "chapter_analysis_processes"

    id = Column(Integer, primary_key=True)
    chapter_id = Column(Integer, ForeignKey("chapters.id"), nullable=False)
    novel_id = Column(Integer, ForeignKey("novels.id"), nullable=False)
    result_id = Column(Integer, ForeignKey("chapter_analysis_results.id"), nullable=True)
    dimension = Column(String(255), nullable=False)  # 分析维度
    processing_stage = Column(String(255), nullable=False)  # 处理阶段
    stage_index = Column(Integer, nullable=True)  # 阶段内索引
    input_text = Column(Text, nullable=True)  # 输入文本
    output_text = Column(Text, nullable=True)  # 输出文本
    prompt_used = Column(Text, nullable=True)  # 使用的提示词
    api_request = Column(JSON, nullable=True)  # API请求详情
    api_response = Column(JSON, nullable=True)  # API响应详情
    processing_time = Column(Integer, nullable=True)  # 处理时间（毫秒）
    tokens_used = Column(Integer, nullable=True)  # 使用的令牌数
    is_successful = Column(Boolean, default=True)  # 是否成功
    error_message = Column(Text, nullable=True)  # 错误信息
    process_metadata = Column(JSON, nullable=True)  # 过程元数据
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # 与ChapterAnalysisResult的关系
    result = relationship("ChapterAnalysisResult", back_populates="processes")

    def __init__(
        self,
        chapter_id: int,
        novel_id: int,
        dimension: str,
        processing_stage: str,
        stage_index: Optional[int] = None,
        result_id: Optional[int] = None,
        input_text: Optional[str] = None,
        output_text: Optional[str] = None,
        prompt_used: Optional[str] = None,
        api_request: Optional[Dict[str, Any]] = None,
        api_response: Optional[Dict[str, Any]] = None,
        processing_time: Optional[int] = None,
        tokens_used: Optional[int] = None,
        is_successful: bool = True,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        初始化章节分析过程。

        Args:
            chapter_id: 章节ID
            novel_id: 小说ID
            dimension: 分析维度
            processing_stage: 处理阶段
            stage_index: 阶段内索引
            result_id: 关联的分析结果ID
            input_text: 输入文本
            output_text: 输出文本
            prompt_used: 使用的提示词
            api_request: API请求详情
            api_response: API响应详情
            processing_time: 处理时间（毫秒）
            tokens_used: 使用的令牌数
            is_successful: 是否成功
            error_message: 错误信息
            metadata: 其他元数据
        """
        self.chapter_id = chapter_id
        self.novel_id = novel_id
        self.dimension = dimension
        self.processing_stage = processing_stage
        self.stage_index = stage_index
        self.result_id = result_id
        self.input_text = input_text
        self.output_text = output_text
        self.prompt_used = prompt_used
        self.api_request = api_request or {}
        self.api_response = api_response or {}
        self.processing_time = processing_time
        self.tokens_used = tokens_used
        self.is_successful = is_successful
        self.error_message = error_message
        self.process_metadata = metadata or {}

    def to_dict(self) -> Dict[str, Any]:
        """
        将章节分析过程转换为字典。

        Returns:
            章节分析过程的字典表示
        """
        return {
            "id": self.id,
            "chapter_id": self.chapter_id,
            "novel_id": self.novel_id,
            "result_id": self.result_id,
            "dimension": self.dimension,
            "processing_stage": self.processing_stage,
            "stage_index": self.stage_index,
            "input_text": self.input_text,
            "output_text": self.output_text,
            "prompt_used": self.prompt_used,
            "api_request": self.api_request,
            "api_response": self.api_response,
            "processing_time": self.processing_time,
            "tokens_used": self.tokens_used,
            "is_successful": self.is_successful,
            "error_message": self.error_message,
            "metadata": self.process_metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
