import os
import re
import shutil
from datetime import datetime

def find_template_files(start_path="src/web/templates"):
    """查找所有模板文件"""
    template_files = []
    for root, dirs, files in os.walk(start_path):
        for file in files:
            if file.endswith('.html'):
                template_files.append(os.path.join(root, file))
    return template_files

def check_jinja_tags(file_path):
    """检查Jinja标签是否匹配"""
    print(f"检查文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 计数各种标签
        if_count = content.count('{% if')
        else_count = content.count('{% else')
        endif_count = content.count('{% endif')
        
        print(f"  找到 {if_count} 个if标签, {else_count} 个else标签, {endif_count} 个endif标签")
        
        # 检查是否匹配
        if if_count != endif_count:
            print(f"  警告: if标签({if_count})和endif标签({endif_count})数量不匹配!")
            return True
        
        # 检查标签格式
        problems = []
        for i, line in enumerate(lines):
            # 检查if标签
            if '{% if' in line and not line.strip().startswith('{% if') and not line.strip().endswith('%}'):
                problems.append((i+1, line, "if标签格式不正确"))
            
            # 检查else标签
            if '{% else' in line and not line.strip() == '{% else %}':
                problems.append((i+1, line, "else标签格式不正确"))
            
            # 检查endif标签
            if '{% endif' in line and not line.strip() == '{% endif %}':
                problems.append((i+1, line, "endif标签格式不正确"))
        
        if problems:
            print(f"  发现 {len(problems)} 个问题:")
            for line_num, line_content, problem in problems:
                print(f"    第{line_num}行: {line_content.strip()} - {problem}")
            return True
        
        return False
    
    except Exception as e:
        print(f"  检查文件时出错: {str(e)}")
        return False

def fix_jinja_tags(file_path):
    """修复Jinja标签"""
    print(f"修复文件: {file_path}")
    
    # 创建备份
    backup_file = f"{file_path}.bak_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy2(file_path, backup_file)
        print(f"  已创建备份: {backup_file}")
    except Exception as e:
        print(f"  创建备份时出错: {str(e)}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        fixed_lines = []
        fixed_count = 0
        
        for line in lines:
            original_line = line
            
            # 修复if标签
            if '{% if' in line and not line.strip().startswith('{% if'):
                # 提取if条件
                match = re.search(r'{%\s*if\s+(.+?)%}', line)
                if match:
                    condition = match.group(1).strip()
                    line = line.replace(match.group(0), f"{{% if {condition} %}}")
                    fixed_count += 1
            
            # 修复else标签
            if '{% else' in line and not line.strip() == '{% else %}':
                line = re.sub(r'{%\s*else\s*%}', '{% else %}', line)
                fixed_count += 1
            
            # 修复endif标签
            if '{% endif' in line and not line.strip() == '{% endif %}':
                line = re.sub(r'{%\s*endif\s*%}', '{% endif %}', line)
                fixed_count += 1
            
            fixed_lines.append(line)
            
            # 如果行被修改，打印出来
            if line != original_line:
                print(f"  修复: {original_line.strip()} -> {line.strip()}")
        
        # 写回修复后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(fixed_lines)
        
        print(f"  修复了 {fixed_count} 处问题")
        return fixed_count > 0
    
    except Exception as e:
        print(f"  修复文件时出错: {str(e)}")
        # 如果出错，恢复备份
        try:
            shutil.copy2(backup_file, file_path)
            print(f"  已恢复备份")
        except:
            pass
        return False

def main():
    """主函数"""
    print("开始检查和修复Jinja模板文件...")
    
    # 查找所有模板文件
    template_files = find_template_files()
    print(f"找到 {len(template_files)} 个模板文件")
    
    # 检查并修复每个文件
    fixed_files = []
    for file_path in template_files:
        if check_jinja_tags(file_path):
            if fix_jinja_tags(file_path):
                fixed_files.append(file_path)
    
    # 打印结果
    if fixed_files:
        print(f"\n已修复 {len(fixed_files)} 个文件:")
        for file in fixed_files:
            print(f"  - {file}")
        print("\n请重新启动九猫系统进行测试")
    else:
        print("\n没有发现需要修复的问题")

if __name__ == "__main__":
    main()
