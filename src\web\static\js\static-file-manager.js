/**
 * 九猫 - 静态文件管理器
 * 确保所有必要的静态文件都能正确加载
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('静态文件管理器已初始化');
    
    // 核心静态文件列表
    const coreStaticFiles = [
        { type: 'css', name: 'bootstrap.min.css', path: '/static/css/bootstrap.min.css', libPath: '/static/css/lib/bootstrap.min.css', cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' },
        { type: 'css', name: 'main.css', path: '/static/css/main.css', libPath: null, cdn: null },
        { type: 'js', name: 'jquery.min.js', path: '/static/js/jquery.min.js', libPath: '/static/js/lib/jquery.min.js', cdn: 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js' },
        { type: 'js', name: 'bootstrap.bundle.min.js', path: '/static/js/bootstrap.bundle.min.js', libPath: '/static/js/lib/bootstrap.bundle.min.js', cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js' }
    ];
    
    // 检查并修复静态文件
    function checkAndFixStaticFiles() {
        console.log('开始检查核心静态文件');
        
        coreStaticFiles.forEach(file => {
            checkAndLoadFile(file);
        });
    }
    
    // 检查并加载单个文件
    function checkAndLoadFile(file) {
        console.log(`检查 ${file.name} 文件`);
        
        if (file.type === 'css') {
            checkAndLoadCSS(file);
        } else if (file.type === 'js') {
            checkAndLoadJS(file);
        }
    }
    
    // 检查并加载CSS文件
    function checkAndLoadCSS(file) {
        // 检查是否已加载
        const existingLinks = document.querySelectorAll(`link[href*="${file.name}"]`);
        if (existingLinks.length > 0) {
            console.log(`${file.name} 已加载`);
            return;
        }
        
        // 尝试加载CSS
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = file.path;
        
        // 添加加载错误处理
        link.onerror = function() {
            console.warn(`无法加载 ${file.path}，尝试备用路径`);
            
            // 尝试从lib目录加载
            if (file.libPath) {
                const libLink = document.createElement('link');
                libLink.rel = 'stylesheet';
                libLink.href = file.libPath;
                
                libLink.onerror = function() {
                    console.warn(`无法加载 ${file.libPath}，尝试CDN`);
                    
                    // 尝试从CDN加载
                    if (file.cdn) {
                        const cdnLink = document.createElement('link');
                        cdnLink.rel = 'stylesheet';
                        cdnLink.href = file.cdn;
                        document.head.appendChild(cdnLink);
                    }
                };
                
                document.head.appendChild(libLink);
            } else if (file.cdn) {
                // 直接尝试CDN
                const cdnLink = document.createElement('link');
                cdnLink.rel = 'stylesheet';
                cdnLink.href = file.cdn;
                document.head.appendChild(cdnLink);
            }
        };
        
        document.head.appendChild(link);
    }
    
    // 检查并加载JS文件
    function checkAndLoadJS(file) {
        // 检查是否已加载
        const existingScripts = document.querySelectorAll(`script[src*="${file.name}"]`);
        if (existingScripts.length > 0) {
            console.log(`${file.name} 已加载`);
            return;
        }
        
        // 检查是否已通过其他方式加载
        if (file.name === 'jquery.min.js' && window.jQuery) {
            console.log('jQuery 已通过其他方式加载');
            return;
        }
        
        if (file.name === 'bootstrap.bundle.min.js' && window.bootstrap) {
            console.log('Bootstrap 已通过其他方式加载');
            return;
        }
        
        // 尝试加载JS
        const script = document.createElement('script');
        script.src = file.path;
        
        // 添加加载错误处理
        script.onerror = function() {
            console.warn(`无法加载 ${file.path}，尝试备用路径`);
            
            // 尝试从lib目录加载
            if (file.libPath) {
                const libScript = document.createElement('script');
                libScript.src = file.libPath;
                
                libScript.onerror = function() {
                    console.warn(`无法加载 ${file.libPath}，尝试CDN`);
                    
                    // 尝试从CDN加载
                    if (file.cdn) {
                        const cdnScript = document.createElement('script');
                        cdnScript.src = file.cdn;
                        document.head.appendChild(cdnScript);
                    }
                };
                
                document.head.appendChild(libScript);
            } else if (file.cdn) {
                // 直接尝试CDN
                const cdnScript = document.createElement('script');
                cdnScript.src = file.cdn;
                document.head.appendChild(cdnScript);
            }
        };
        
        document.head.appendChild(script);
    }
    
    // 在DOM加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkAndFixStaticFiles);
    } else {
        checkAndFixStaticFiles();
    }
    
    // 导出到全局，以便其他脚本可以使用
    window.StaticFileManager = {
        checkAndFixStaticFiles: checkAndFixStaticFiles,
        checkAndLoadFile: checkAndLoadFile
    };
    
    console.log('静态文件管理器设置完成');
})();
