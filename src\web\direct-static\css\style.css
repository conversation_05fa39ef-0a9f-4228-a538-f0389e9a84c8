/* 九猫 (<PERSON> Cats) - 小说文本分析系统 */

body {
    font-family: "Noto Sans SC", "Microsoft YaHei", sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.navbar-brand {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.navbar-brand small {
    font-size: 0.7rem;
    opacity: 0.8;
}

.footer {
    margin-top: 3rem;
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.125);
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.jumbotron {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.novel-excerpt {
    font-family: "Noto Serif SC", serif;
    line-height: 1.8;
}

.analysis-content {
    font-family: "Noto Serif SC", serif;
    line-height: 1.8;
}

/* Markdown 内容样式 */
.markdown-content {
    font-family: "Noto Serif SC", serif;
    line-height: 1.8;
    color: #333;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 1.5em;
    margin-bottom: 0.75em;
    font-weight: 600;
    color: #222;
}

.markdown-content h1 {
    font-size: 1.8rem;
    border-bottom: 1px solid #eaecef;
    padding-bottom: 0.3em;
}

.markdown-content h2 {
    font-size: 1.5rem;
    border-bottom: 1px solid #eaecef;
    padding-bottom: 0.3em;
}

.markdown-content h3 {
    font-size: 1.3rem;
}

.markdown-content h4 {
    font-size: 1.1rem;
}

.markdown-content p {
    margin-bottom: 1em;
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 1em;
    padding-left: 2em;
}

.markdown-content li {
    margin-bottom: 0.5em;
}

.markdown-content blockquote {
    padding: 0.5em 1em;
    margin-left: 0;
    margin-right: 0;
    border-left: 4px solid #dfe2e5;
    color: #6a737d;
}

.markdown-content code {
    padding: 0.2em 0.4em;
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 85%;
}

.markdown-content pre {
    padding: 1em;
    overflow: auto;
    background-color: #f6f8fa;
    border-radius: 3px;
    margin-bottom: 1em;
}

.markdown-content pre code {
    padding: 0;
    background-color: transparent;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .novel-excerpt {
        max-height: 200px;
    }
}

/* 分析结果卡片样式 */
.analysis-card .card-header {
    padding: 0.75rem 1rem;
}

.analysis-card .card-body {
    padding: 1rem;
}

.analysis-content {
    font-size: 0.9rem;
    color: #333;
}

/* 表格样式 */
.table {
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 2px solid rgba(0, 0, 0, 0.125);
}

/* 按钮样式 */
.btn-primary {
    background-color: #4a6bdf;
    border-color: #4a6bdf;
}

.btn-primary:hover {
    background-color: #3a5bce;
    border-color: #3a5bce;
}

.btn-outline-primary {
    color: #4a6bdf;
    border-color: #4a6bdf;
}

.btn-outline-primary:hover {
    background-color: #4a6bdf;
    border-color: #4a6bdf;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0.5rem 0;
    margin-bottom: 1.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
}

/* 控制台样式 */
.console-output {
    background-color: #1e1e1e;
    color: #f0f0f0;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 0.9rem;
    border-radius: 0 0 0.5rem 0.5rem;
    height: 400px;
    overflow-y: auto;
    padding: 0;
}

.console-inner {
    padding: 0.5rem 1rem;
}

.console-line {
    padding: 0.1rem 0;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
}

.log-welcome {
    color: #8a8a8a;
    font-style: italic;
}

.log-info {
    color: #f0f0f0;
}

.log-debug {
    color: #6a9955;
}

.log-warning {
    color: #dcdcaa;
}

.log-error {
    color: #f14c4c;
}

.log-timestamp {
    color: #569cd6;
    margin-right: 0.5rem;
}

.log-dimension {
    color: #ce9178;
    margin-right: 0.5rem;
}

.log-progress {
    color: #b5cea8;
    margin-right: 0.5rem;
}
