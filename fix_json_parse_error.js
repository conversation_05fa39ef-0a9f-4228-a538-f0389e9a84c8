// 修复 JSON.parse 错误的函数
window.safeLogError = function(message, source, lineno, colno, error) {
    try {
        // 添加调试日志，验证参数是否正确传递
        console.log('Error Details:', { message, source, lineno, colno, error });
        
        // 确保 console.error 参数列表正确闭合
        console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
    } catch (e) {
        console.error('记录错误时出错:', e);
    }
};

// 全局错误处理函数
window.onerror = function(message, source, lineno, colno, error) {
    // 使用安全的错误记录函数
    window.safeLogError(message, source, lineno, colno, error);
    return true; // 阻止默认错误处理
};

// 修复 JSON.parse 错误
document.addEventListener('DOMContentLoaded', function() {
    try {
        // 查找所有包含 JSON.parse 的脚本标签
        const scripts = document.querySelectorAll('script');
        scripts.forEach(script => {
            if (script.textContent && script.textContent.includes('JSON.parse')) {
                console.log('找到包含 JSON.parse 的脚本:', script.textContent.substring(0, 100) + '...');
                
                // 尝试修复 JSON.parse 调用
                const fixedContent = script.textContent.replace(
                    /JSON\.parse\('([^']*)'\)/g, 
                    function(match, p1) {
                        // 确保 JSON 字符串正确闭合
                        if (!p1.endsWith("'")) {
                            return "JSON.parse('" + p1 + "')";
                        }
                        return match;
                    }
                );
                
                if (fixedContent !== script.textContent) {
                    console.log('修复了 JSON.parse 调用');
                    // 创建新的脚本标签替换旧的
                    const newScript = document.createElement('script');
                    newScript.textContent = fixedContent;
                    script.parentNode.replaceChild(newScript, script);
                }
            }
        });
    } catch (e) {
        console.error('修复 JSON.parse 错误时出错:', e);
    }
});
