/**
 * 九猫 - 最终修复脚本
 * 这是在所有其他修复脚本都失败的情况下的最后手段
 * 版本: 1.0.0
 */

(function() {
    console.log('[九猫最终修复] 脚本已加载，开始扫描错误');
    
    // 立即劫持console.error，拦截特定错误
    function setupConsoleErrorInterceptor() {
        if (window.__finalFixConsoleInterceptorApplied) return;
        
        const originalConsoleError = console.error;
        console.error = function() {
            // 检查错误信息
            let errorMessage = '';
            if (arguments.length > 0) {
                errorMessage = String(arguments[0] || '');
            }
            
            // 检查是否包含我们要处理的特定错误
            if (errorMessage.includes('matchesSelector is not a function') || 
                errorMessage.includes('S.find.matchesSelector') ||
                errorMessage.includes('41:33') ||
                errorMessage.includes('41:30')) {
                
                console.log('[九猫最终修复] 捕获到jQuery错误，尝试修复');
                
                // 立即尝试修复jQuery
                fixJQueryProblems();
                
                // 不输出错误到控制台
                return;
            }
            
            // 其他错误正常输出
            return originalConsoleError.apply(console, arguments);
        };
        
        window.__finalFixConsoleInterceptorApplied = true;
        console.log('[九猫最终修复] 控制台错误拦截器已设置');
    }
    
    // jQuery错误修复
    function fixJQueryProblems() {
        try {
            // 覆盖jQuery.find.matchesSelector
            if (typeof jQuery !== 'undefined' && jQuery.find) {
                if (!jQuery.find.matchesSelector) {
                    console.log('[九猫最终修复] jQuery.find缺少matchesSelector，开始修复');
                    
                    // 尝试使用多种方法来修复
                    if (jQuery.find.matches) {
                        jQuery.find.matchesSelector = jQuery.find.matches;
                    } else if (typeof Element.prototype.matches !== 'undefined') {
                        jQuery.find.matchesSelector = function(elem, expr) {
                            return Element.prototype.matches.call(elem, expr);
                        };
                    } else if (typeof Element.prototype.msMatchesSelector !== 'undefined') {
                        jQuery.find.matchesSelector = function(elem, expr) {
                            return Element.prototype.msMatchesSelector.call(elem, expr);
                        };
                    } else {
                        jQuery.find.matchesSelector = function() { return true; };
                    }
                    
                    console.log('[九猫最终修复] jQuery.find.matchesSelector已修复');
                }
            }
            
            // 修复Sizzle对象
            if (typeof Sizzle !== 'undefined') {
                if (!Sizzle.matchesSelector) {
                    console.log('[九猫最终修复] Sizzle缺少matchesSelector，开始修复');
                    
                    if (Sizzle.matches) {
                        Sizzle.matchesSelector = Sizzle.matches;
                    } else if (jQuery && jQuery.find && jQuery.find.matchesSelector) {
                        Sizzle.matchesSelector = jQuery.find.matchesSelector;
                    } else {
                        Sizzle.matchesSelector = function() { return true; };
                    }
                    
                    console.log('[九猫最终修复] Sizzle.matchesSelector已修复');
                }
            }
            
            // 修复S对象
            if (typeof S !== 'undefined' && S && S.find) {
                if (!S.find.matchesSelector) {
                    console.log('[九猫最终修复] S.find缺少matchesSelector，开始修复');
                    
                    if (S.find.matches) {
                        S.find.matchesSelector = S.find.matches;
                    } else if (jQuery && jQuery.find && jQuery.find.matchesSelector) {
                        S.find.matchesSelector = jQuery.find.matchesSelector;
                    } else {
                        S.find.matchesSelector = function() { return true; };
                    }
                    
                    console.log('[九猫最终修复] S.find.matchesSelector已修复');
                }
            }
        } catch (e) {
            console.log('[九猫最终修复] 修复jQuery过程中出错:', e);
        }
    }
    
    // 检查是否是小说页面
    function isNovelPage() {
        return window.location.pathname.match(/\/novel\/\d+/);
    }
    
    // 获取小说ID
    function getNovelId() {
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        return match ? match[1] : null;
    }
    
    // 修复章节分析按钮
    function fixChapterAnalysisButtons() {
        if (!isNovelPage()) return;
        
        const novelId = getNovelId();
        if (!novelId) return;
        
        console.log('[九猫最终修复] 检测到小说页面，ID:', novelId);
        
        // 获取所有按钮和链接
        const elements = document.querySelectorAll('a, button, [role="button"], .btn');
        let fixedCount = 0;
        
        elements.forEach(el => {
            if (!el.textContent) return;
            
            const text = el.textContent.trim().toLowerCase();
            if (text.includes('章节') && 
               (text.includes('分析') || text.includes('列表'))) {
                
                console.log('[九猫最终修复] 找到章节分析相关元素:', text);
                
                // 设置href属性
                if (el.tagName === 'A') {
                    el.href = `/novel/${novelId}/chapters`;
                }
                
                // 创建一个新的点击事件处理函数，覆盖原有的
                const originalOnclick = el.onclick;
                el.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('[九猫最终修复] 拦截点击事件，跳转到章节页');
                    
                    setTimeout(function() {
                        window.location.href = `/novel/${novelId}/chapters`;
                    }, 10);
                    
                    return false;
                };
                
                fixedCount++;
            }
        });
        
        if (fixedCount > 0) {
            console.log(`[九猫最终修复] 共修复了 ${fixedCount} 个章节分析按钮`);
        }
    }
    
    // 添加全局点击事件拦截
    function setupGlobalClickInterceptor() {
        if (!isNovelPage()) return;
        
        // 避免重复设置
        if (window.__finalFixClickInterceptorApplied) return;
        
        document.addEventListener('click', function(e) {
            // 获取点击的元素
            let target = e.target;
            
            // 向上遍历DOM树，查找章节分析按钮
            while (target && target !== document) {
                if (target.textContent) {
                    const text = target.textContent.trim().toLowerCase();
                    if (text.includes('章节') && 
                       (text.includes('分析') || text.includes('列表'))) {
                        
                        const novelId = getNovelId();
                        if (novelId) {
                            console.log(`[九猫最终修复] 通过全局点击拦截器捕获到章节分析点击，小说ID: ${novelId}`);
                            e.preventDefault();
                            e.stopPropagation();
                            
                            // 延迟执行，确保不被其他事件处理器干扰
                            setTimeout(function() {
                                window.location.href = `/novel/${novelId}/chapters`;
                            }, 10);
                        }
                        
                        break;
                    }
                }
                
                target = target.parentElement;
            }
        }, true); // 使用捕获阶段，确保最先执行
        
        window.__finalFixClickInterceptorApplied = true;
        console.log('[九猫最终修复] 全局点击拦截器已设置');
    }
    
    // 监听错误事件
    function setupErrorListener() {
        window.addEventListener('error', function(event) {
            // 检查是否是我们关注的错误
            const errorMessage = event.message || '';
            const errorSource = event.filename || '';
            
            if ((errorMessage.includes('matchesSelector') || 
                errorSource.includes('jquery') || 
                errorSource.includes('41:33') || 
                errorSource.includes('41:30')) && 
                event.error) {
                
                console.log('[九猫最终修复] 捕获到错误事件:', errorMessage);
                
                // 尝试修复jQuery问题
                fixJQueryProblems();
                
                // 阻止事件继续传播
                event.preventDefault();
                return true;
            }
            
            return false;
        }, true);
        
        console.log('[九猫最终修复] 错误事件监听器已设置');
    }
    
    // 定期检查是否出现错误
    function setupPeriodicErrorCheck() {
        // 定期检查jQuery
        setInterval(function() {
            // 检查jQuery是否有matchesSelector问题
            if (typeof jQuery !== 'undefined' && jQuery.find && !jQuery.find.matchesSelector) {
                console.log('[九猫最终修复] 周期检查发现jQuery.find.matchesSelector缺失，尝试修复');
                fixJQueryProblems();
            }
            
            // 针对小说页面，检查章节分析按钮
            if (isNovelPage()) {
                fixChapterAnalysisButtons();
            }
        }, 3000);
        
        console.log('[九猫最终修复] 周期性错误检查已设置');
    }
    
    // 初始化
    function initialize() {
        console.log('[九猫最终修复] 初始化中...');
        
        // 设置控制台错误拦截
        setupConsoleErrorInterceptor();
        
        // 设置错误监听
        setupErrorListener();
        
        // 立即尝试修复jQuery问题
        fixJQueryProblems();
        
        // 如果是小说页面，修复章节分析按钮
        if (isNovelPage()) {
            setTimeout(fixChapterAnalysisButtons, 500);
        }
        
        // 设置全局点击事件拦截
        setupGlobalClickInterceptor();
        
        // 设置周期性错误检查
        setupPeriodicErrorCheck();
        
        console.log('[九猫最终修复] 初始化完成');
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // 也立即尝试执行一次初始化
    setTimeout(initialize, 0);
    
    // 暴露公共API，方便手动调用
    window.finalFixes = {
        fixJQuery: fixJQueryProblems,
        fixChapterButtons: fixChapterAnalysisButtons
    };
})(); 