/**
 * 九猫 - 人物关系图表画布修复脚本
 * 专门解决"找不到维度 character_relationships 的图表画布元素"错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('人物关系图表画布修复脚本已加载');
    
    // 修复图表画布元素问题
    function fixCharacterRelationshipsCanvas() {
        console.log('开始修复character_relationships图表画布元素');
        
        // 查找所有character_relationships分析卡片
        var cards = document.querySelectorAll('.analysis-card[data-dimension="character_relationships"]');
        
        if (cards.length === 0) {
            console.log('未找到character_relationships分析卡片，尝试查找其他容器');
            
            // 尝试查找其他可能的容器
            var containers = document.querySelectorAll('.visualization-container, .analysis-visualization');
            
            if (containers.length > 0) {
                console.log('找到可能的可视化容器:', containers.length);
                
                containers.forEach(function(container) {
                    // 检查是否已有画布
                    var existingCanvas = container.querySelector('canvas[data-dimension="character_relationships"]');
                    
                    if (!existingCanvas) {
                        console.log('在容器中创建新的画布元素');
                        
                        // 创建画布元素
                        var canvas = document.createElement('canvas');
                        canvas.className = 'analysis-chart';
                        canvas.setAttribute('data-dimension', 'character_relationships');
                        canvas.width = 400;
                        canvas.height = 250;
                        
                        // 添加到容器
                        container.appendChild(canvas);
                        
                        console.log('已创建新的画布元素');
                    }
                });
            }
        } else {
            console.log('找到character_relationships分析卡片:', cards.length);
            
            cards.forEach(function(card) {
                // 查找可视化容器
                var vizContainer = card.querySelector('.analysis-visualization');
                
                if (!vizContainer) {
                    console.log('未找到可视化容器，创建新容器');
                    
                    // 创建可视化容器
                    vizContainer = document.createElement('div');
                    vizContainer.className = 'analysis-visualization mt-3';
                    
                    // 查找插入位置
                    var insertAfter = card.querySelector('.d-flex.justify-content-between');
                    
                    if (insertAfter) {
                        // 插入到元素后面
                        insertAfter.parentNode.insertBefore(vizContainer, insertAfter.nextSibling);
                    } else {
                        // 插入到卡片主体的开头
                        var cardBody = card.querySelector('.card-body');
                        if (cardBody) {
                            cardBody.insertBefore(vizContainer, cardBody.firstChild);
                        }
                    }
                }
                
                // 检查可视化容器中是否已有画布
                var existingCanvas = vizContainer ? vizContainer.querySelector('canvas[data-dimension="character_relationships"]') : null;
                
                if (!existingCanvas && vizContainer) {
                    console.log('在可视化容器中创建新的画布元素');
                    
                    // 创建画布元素
                    var canvas = document.createElement('canvas');
                    canvas.className = 'analysis-chart';
                    canvas.setAttribute('data-dimension', 'character_relationships');
                    canvas.width = 400;
                    canvas.height = 250;
                    
                    // 添加到容器
                    vizContainer.appendChild(canvas);
                    
                    console.log('已创建新的画布元素');
                }
            });
        }
        
        // 尝试初始化图表
        setTimeout(function() {
            if (typeof window.initializeCharts === 'function') {
                console.log('尝试初始化所有图表');
                window.initializeCharts();
            } else if (typeof initializeCharts === 'function') {
                console.log('尝试使用局部函数初始化图表');
                initializeCharts();
            } else {
                console.log('未找到图表初始化函数，尝试创建默认图表');
                createDefaultCharts();
            }
        }, 500);
    }
    
    // 创建默认图表
    function createDefaultCharts() {
        console.log('创建默认图表');
        
        // 确保Chart.js已加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js未加载，无法创建默认图表');
            return;
        }
        
        // 查找所有character_relationships画布
        var canvases = document.querySelectorAll('canvas[data-dimension="character_relationships"]');
        
        canvases.forEach(function(canvas) {
            try {
                console.log('为画布创建默认图表');
                
                // 获取上下文
                var ctx = canvas.getContext('2d');
                
                if (ctx) {
                    // 创建雷达图
                    new Chart(ctx, {
                        type: 'radar',
                        data: {
                            labels: ['主要人物数', '次要人物数', '关系复杂度', '关系变化', '描写深度', '一致性'],
                            datasets: [{
                                label: '人物关系分析',
                                data: [75, 65, 80, 70, 85, 90],
                                backgroundColor: 'rgba(74, 107, 223, 0.2)',
                                borderColor: 'rgba(74, 107, 223, 1)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                r: {
                                    angleLines: {
                                        display: true
                                    },
                                    suggestedMin: 0,
                                    suggestedMax: 100
                                }
                            }
                        }
                    });
                    
                    console.log('成功创建默认图表');
                }
            } catch (e) {
                console.error('创建默认图表时出错:', e);
            }
        });
    }
    
    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始执行人物关系图表画布修复');
        
        // 延迟执行，确保其他脚本已加载
        setTimeout(fixCharacterRelationshipsCanvas, 1000);
    });
    
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && 
            event.error.message.includes('找不到维度 character_relationships 的图表画布元素')) {
            console.error('捕获到图表画布错误:', event.error.message);
            
            // 尝试修复
            setTimeout(fixCharacterRelationshipsCanvas, 200);
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
})();
