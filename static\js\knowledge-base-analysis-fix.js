/**
 * 知识库分析修复脚本
 *
 * 这个脚本用于修复知识库分析相关的问题，确保3.0和3.1系统的兼容性
 */

// 加载知识库（修复版）
window.loadKnowledgeBaseWithFix = function(templateId, callback) {
    console.log('[知识库分析修复] 加载知识库:', templateId);

    // 加载知识库
    $.ajax({
        url: `/api/novel/${templateId}/analysis`,
        method: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                // 处理分析结果
                const content = processAnalysisResultsToMarkdown(response.data);
                callback(true, content, null);
            } else {
                // 如果v3.0 API失败，尝试使用v3.1 API路径
                $.ajax({
                    url: `/api/novels/${templateId}/analysis`,
                    method: 'GET',
                    success: function(response) {
                        if (response.success && response.data) {
                            // 处理分析结果
                            const content = processAnalysisResultsToMarkdown(response.data);
                            callback(true, content, null);
                        } else {
                            callback(false, null, response.message || '加载分析结果失败');
                        }
                    },
                    error: function(error) {
                        console.error('[知识库分析修复] 加载分析结果错误 (v3.1 API):', error);
                        callback(false, null, '加载分析结果时发生错误');
                    }
                });
            }
        },
        error: function(error) {
            console.error('[知识库分析修复] 加载分析结果错误 (v3.0 API):', error);

            // 如果v3.0 API失败，尝试使用v3.1 API路径
            $.ajax({
                url: `/api/novels/${templateId}/analysis`,
                method: 'GET',
                success: function(response) {
                    if (response.success && response.data) {
                        // 处理分析结果
                        const content = processAnalysisResultsToMarkdown(response.data);
                        callback(true, content, null);
                    } else {
                        callback(false, null, response.message || '加载分析结果失败');
                    }
                },
                error: function(error) {
                    console.error('[知识库分析修复] 加载分析结果错误 (v3.1 API):', error);
                    callback(false, null, '加载分析结果时发生错误');
                }
            });
        }
    });
};

// 将分析结果处理为Markdown格式
function processAnalysisResultsToMarkdown(data) {
    console.log('[知识库分析修复] 处理分析结果为Markdown格式');

    // 获取小说信息
    const novelData = data.novel || {};

    // 构建Markdown内容
    let markdown = `# ${novelData.title || '未知小说'} 分析知识库\n\n`;
    markdown += `## 基本信息\n\n`;
    markdown += `- 标题：${novelData.title || '未知'}\n`;
    markdown += `- 作者：${novelData.author || '未知'}\n`;
    markdown += `- 字数：${novelData.word_count || 0}\n`;
    markdown += `- 章节数：${novelData.chapters ? novelData.chapters.length : 0}\n\n`;

    // 添加分析结果
    if (data.book_analysis) {
        const bookAnalysis = data.book_analysis;

        markdown += `## 分析维度\n\n`;

        for (const dimension in bookAnalysis) {
            if (bookAnalysis.hasOwnProperty(dimension)) {
                const result = bookAnalysis[dimension];
                const dimensionName = getDimensionName(dimension);

                // 获取分析结果内容
                let content = result.content || '';
                const reasoningContent = result.reasoning_content || '';

                // 如果分析结果为空但推理过程不为空，则从推理过程中提取分析结果
                if (!content && reasoningContent) {
                    console.log(`[知识库分析修复] 维度 ${dimension} 的分析结果为空，但推理过程不为空，尝试提取分析结果`);
                    content = extractResultFromReasoning(reasoningContent) || reasoningContent;
                }

                // 添加分析结果
                markdown += `### ${dimensionName}\n\n`;
                markdown += `${content}\n\n`;
            }
        }
    }

    return markdown;
}

// 转化为预设模板（修复版）
window.convertToPresetTemplateWithFix = function(templateId, callback) {
    console.log('[知识库分析修复] 转化为预设模板:', templateId);

    // 获取小说信息
    $.ajax({
        url: `/api/novel/${templateId}`,
        method: 'GET',
        success: function(novelResponse) {
            const novelData = novelResponse.success ? novelResponse.data : {};

            // 加载分析结果
            $.ajax({
                url: `/api/novel/${templateId}/analysis`,
                method: 'GET',
                success: function(response) {
                    if (response.success && response.data) {
                        // 处理分析结果
                        response.data.novel = novelData;
                        const content = processAnalysisResultsToMarkdown(response.data);

                        // 创建预设
                        const preset = {
                            id: `preset_${Date.now()}`,
                            title: `${novelData.title || '未知小说'} 分析知识库`,
                            content: content,
                            category: 'knowledge_base'
                        };

                        // 保存预设
                        savePreset(preset, callback);
                    } else {
                        // 如果v3.0 API失败，尝试使用v3.1 API路径
                        $.ajax({
                            url: `/api/novels/${templateId}/analysis`,
                            method: 'GET',
                            success: function(response) {
                                if (response.success && response.data) {
                                    // 处理分析结果
                                    response.data.novel = novelData;
                                    const content = processAnalysisResultsToMarkdown(response.data);

                                    // 创建预设
                                    const preset = {
                                        id: `preset_${Date.now()}`,
                                        title: `${novelData.title || '未知小说'} 分析知识库`,
                                        content: content,
                                        category: 'knowledge_base'
                                    };

                                    // 保存预设
                                    savePreset(preset, callback);
                                } else {
                                    callback(false, null, response.message || '加载分析结果失败');
                                }
                            },
                            error: function(error) {
                                console.error('[知识库分析修复] 加载分析结果错误 (v3.1 API):', error);
                                callback(false, null, '加载分析结果时发生错误');
                            }
                        });
                    }
                },
                error: function(error) {
                    console.error('[知识库分析修复] 加载分析结果错误 (v3.0 API):', error);

                    // 如果v3.0 API失败，尝试使用v3.1 API路径
                    $.ajax({
                        url: `/api/novels/${templateId}/analysis`,
                        method: 'GET',
                        success: function(response) {
                            if (response.success && response.data) {
                                // 处理分析结果
                                response.data.novel = novelData;
                                const content = processAnalysisResultsToMarkdown(response.data);

                                // 创建预设
                                const preset = {
                                    id: `preset_${Date.now()}`,
                                    title: `${novelData.title || '未知小说'} 分析知识库`,
                                    content: content,
                                    category: 'knowledge_base'
                                };

                                // 保存预设
                                savePreset(preset, callback);
                            } else {
                                callback(false, null, response.message || '加载分析结果失败');
                            }
                        },
                        error: function(error) {
                            console.error('[知识库分析修复] 加载分析结果错误 (v3.1 API):', error);
                            callback(false, null, '加载分析结果时发生错误');
                        }
                    });
                }
            });
        },
        error: function(error) {
            console.error('[知识库分析修复] 获取小说信息错误 (v3.0 API):', error);

            // 如果v3.0 API失败，尝试使用v3.1 API路径
            $.ajax({
                url: `/api/novels/${templateId}`,
                method: 'GET',
                success: function(novelResponse) {
                    const novelData = novelResponse.success ? novelResponse.data : {};

                    // 加载分析结果
                    $.ajax({
                        url: `/api/novels/${templateId}/analysis`,
                        method: 'GET',
                        success: function(response) {
                            if (response.success && response.data) {
                                // 处理分析结果
                                response.data.novel = novelData;
                                const content = processAnalysisResultsToMarkdown(response.data);

                                // 创建预设
                                const preset = {
                                    id: `preset_${Date.now()}`,
                                    title: `${novelData.title || '未知小说'} 分析知识库`,
                                    content: content,
                                    category: 'knowledge_base'
                                };

                                // 保存预设
                                savePreset(preset, callback);
                            } else {
                                callback(false, null, response.message || '加载分析结果失败');
                            }
                        },
                        error: function(error) {
                            console.error('[知识库分析修复] 加载分析结果错误 (v3.1 API):', error);
                            callback(false, null, '加载分析结果时发生错误');
                        }
                    });
                },
                error: function(error) {
                    console.error('[知识库分析修复] 获取小说信息错误 (v3.1 API):', error);
                    callback(false, null, '获取小说信息时发生错误');
                }
            });
        }
    });
};

// 保存预设
function savePreset(preset, callback) {
    console.log('[知识库分析修复] 保存预设:', preset.title);

    // 保存预设
    $.ajax({
        url: '/v3/api/presets',
        method: 'POST',
        data: JSON.stringify(preset),
        contentType: 'application/json',
        success: function(response) {
            if (response.success) {
                // 更新预设ID
                preset.id = response.preset_id;
                callback(true, preset, null);
            } else {
                callback(false, null, response.message || '保存预设失败');
            }
        },
        error: function(error) {
            console.error('[知识库分析修复] 保存预设错误:', error);
            callback(false, null, '保存预设时发生错误');
        }
    });
}

// 确保在jQuery加载后执行
$(document).ready(function() {
    console.log('[知识库分析修复] 初始化...');

    // 全局变量
    let currentTemplateId = null;
    let novelData = null;

    // 监听"转化为预设模板"按钮点击事件
    $('#convertToTemplateBtn').on('click', function() {
        console.log('[知识库分析修复] 转化为预设模板按钮被点击');

        // 获取当前选中的模板ID
        currentTemplateId = getCurrentTemplateId();

        if (!currentTemplateId) {
            showAlert('请先读取分析结果', 'warning');
            return;
        }

        // 获取小说信息
        fetchNovelInfo(currentTemplateId);
    });

    // 获取当前选中的模板ID
    function getCurrentTemplateId() {
        // 尝试从全局变量获取
        if (window.selectedTemplateId) {
            return window.selectedTemplateId;
        }

        // 尝试从DOM元素获取
        const selectedTemplate = $('.template-card.selected');
        if (selectedTemplate.length > 0) {
            return selectedTemplate.data('template-id');
        }

        return null;
    }

    // 获取小说信息
    function fetchNovelInfo(novelId) {
        console.log('[知识库分析修复] 获取小说信息，ID:', novelId);

        // 显示加载中提示
        showAlert('正在获取小说信息...', 'info');

        // 首先尝试使用v3.0 API路径
        $.ajax({
            url: `/api/novel/${novelId}`,
            method: 'GET',
            success: function(response) {
                console.log('[知识库分析修复] 成功获取小说信息 (v3.0 API):', response);
                processNovelInfo(response);
            },
            error: function(xhr, status, error) {
                console.error('[知识库分析修复] 获取小说信息失败 (v3.0 API):', error);

                // 如果v3.0 API失败，尝试使用v3.1 API路径
                $.ajax({
                    url: `/api/novels/${novelId}`,
                    method: 'GET',
                    success: function(response) {
                        console.log('[知识库分析修复] 成功获取小说信息 (v3.1 API):', response);
                        processNovelInfo(response);
                    },
                    error: function(xhr, status, error) {
                        console.error('[知识库分析修复] 获取小说信息失败 (v3.1 API):', error);
                        showAlert('获取小说信息失败', 'danger');
                    }
                });
            }
        });
    }

    // 处理小说信息
    function processNovelInfo(response) {
        if (!response.success) {
            showAlert('获取小说信息失败: ' + (response.error || '未知错误'), 'danger');
            return;
        }

        // 保存小说信息
        novelData = response.data;

        // 获取分析结果
        fetchAnalysisResults(currentTemplateId);
    }

    // 获取分析结果
    function fetchAnalysisResults(novelId) {
        console.log('[知识库分析修复] 获取小说分析结果，ID:', novelId);

        // 显示加载中提示
        showAlert('正在获取分析结果...', 'info');

        // 首先尝试使用v3.0 API路径
        $.ajax({
            url: `/api/novel/${novelId}/analysis`,
            method: 'GET',
            success: function(response) {
                console.log('[知识库分析修复] 成功获取分析结果 (v3.0 API):', response);
                processAnalysisResults(response);
            },
            error: function(xhr, status, error) {
                console.error('[知识库分析修复] 获取分析结果失败 (v3.0 API):', error);

                // 如果v3.0 API失败，尝试使用v3.1 API路径
                $.ajax({
                    url: `/api/novels/${novelId}/analysis`,
                    method: 'GET',
                    success: function(response) {
                        console.log('[知识库分析修复] 成功获取分析结果 (v3.1 API):', response);
                        processAnalysisResults(response);
                    },
                    error: function(xhr, status, error) {
                        console.error('[知识库分析修复] 获取分析结果失败 (v3.1 API):', error);
                        showAlert('获取分析结果失败，请确保小说已完成分析', 'danger');
                    }
                });
            }
        });
    }

    // 处理分析结果
    function processAnalysisResults(response) {
        if (!response.success) {
            showAlert('获取分析结果失败: ' + (response.error || '未知错误'), 'danger');
            return;
        }

        // 创建预设模板
        createPresetTemplate(response.data);
    }

    // 创建预设模板
    function createPresetTemplate(analysisData) {
        console.log('[知识库分析修复] 创建预设模板');

        if (!novelData) {
            showAlert('小说信息不完整，无法创建预设模板', 'danger');
            return;
        }

        // 显示加载中提示
        showAlert('正在创建预设模板...', 'info');

        // 构建预设内容
        let presetContent = `# ${novelData.title} 分析知识库\n\n`;
        presetContent += `## 基本信息\n\n`;
        presetContent += `- 标题：${novelData.title}\n`;
        presetContent += `- 作者：${novelData.author || '未知'}\n`;
        presetContent += `- 字数：${novelData.word_count || 0}\n`;
        presetContent += `- 章节数：${novelData.chapters ? novelData.chapters.length : 0}\n\n`;

        // 添加分析结果
        if (analysisData && analysisData.book_analysis) {
            const bookAnalysis = analysisData.book_analysis;

            presetContent += `## 分析维度\n\n`;

            for (const dimension in bookAnalysis) {
                if (bookAnalysis.hasOwnProperty(dimension)) {
                    const result = bookAnalysis[dimension];
                    const dimensionName = getDimensionName(dimension);

                    // 获取分析结果内容
                    let content = result.content || '';
                    const reasoningContent = result.reasoning_content || '';

                    // 如果分析结果为空但推理过程不为空，则从推理过程中提取分析结果
                    if (!content && reasoningContent) {
                        console.log(`[知识库分析修复] 维度 ${dimension} 的分析结果为空，但推理过程不为空，尝试提取分析结果`);
                        content = extractResultFromReasoning(reasoningContent) || reasoningContent;
                    }

                    // 添加分析结果
                    presetContent += `### ${dimensionName}\n\n`;
                    presetContent += `${content}\n\n`;
                }
            }
        }

        // 设置预设表单和显示
        setupPresetFormAndDisplay(presetContent);
    }

    // 从推理过程中提取分析结果
    function extractResultFromReasoning(reasoningContent) {
        // 如果推理过程为空，返回空字符串
        if (!reasoningContent) return '';

        // 尝试从推理过程中提取"分析结果"部分
        const resultPatterns = [
            /分析结果：([\s\S]*?)(?=\n##|\n#|$)/i,
            /最终分析：([\s\S]*?)(?=\n##|\n#|$)/i,
            /最终结果：([\s\S]*?)(?=\n##|\n#|$)/i,
            /分析总结：([\s\S]*?)(?=\n##|\n#|$)/i,
            /总结：([\s\S]*?)(?=\n##|\n#|$)/i,
            /结论：([\s\S]*?)(?=\n##|\n#|$)/i
        ];

        for (const pattern of resultPatterns) {
            const match = reasoningContent.match(pattern);
            if (match && match[1] && match[1].trim()) {
                console.log('[知识库分析修复] 从推理过程中提取到分析结果');
                return match[1].trim();
            }
        }

        // 如果没有找到明确的"分析结果"部分，尝试提取最后一个段落或章节
        const sections = reasoningContent.split(/\n#+\s/);
        if (sections.length > 1) {
            // 返回最后一个章节
            return sections[sections.length - 1].trim();
        }

        // 如果没有章节，尝试提取最后几个段落
        const paragraphs = reasoningContent.split(/\n\s*\n/);
        if (paragraphs.length > 2) {
            // 返回最后两个段落
            return paragraphs.slice(-2).join('\n\n').trim();
        }

        // 如果以上方法都失败，返回整个推理过程
        return reasoningContent;
    }

    // 设置预设表单和显示
    function setupPresetFormAndDisplay(presetContent) {
        // 设置预设表单
        $('#presetTitle').val(`${novelData.title} 分析知识库`);
        $('#presetContent').val(presetContent);
        $('#presetCategory').val('knowledge_base');
        $('#presetId').val('');

        // 切换到预设编辑区域
        $('#presetEditorTitle').text('新建预设模板');
        $('#knowledge-tab').tab('show');

        // 滚动到预设编辑区域
        $('html, body').animate({
            scrollTop: $('#presetEditorTitle').offset().top - 100
        }, 500);

        // 显示成功提示
        showAlert('预设模板创建成功，请点击"保存"按钮保存', 'success');
    }

    // 获取维度名称
    function getDimensionName(dimensionKey) {
        const dimensionMap = {
            'language_style': '语言风格',
            'rhythm_pace': '节奏节拍',
            'character_development': '人物发展',
            'emotional_expression': '情感表达',
            'scene_description': '场景描写',
            'dialogue_analysis': '对话分析',
            'theme_exploration': '主题探索',
            'conflict_setup': '冲突设置',
            'perspective_shifts': '视角转换',
            'symbolism_imagery': '象征意象',
            'cultural_context': '文化背景',
            'plot_development': '情节发展',
            'structure_layout': '结构布局',
            'outline_analysis': '章纲分析',
            'hot_meme_statistics': '热梗统计'
        };

        return dimensionMap[dimensionKey] || dimensionKey;
    }

    // 显示提示信息
    function showAlert(message, type = 'info') {
        const alertHtml = `<div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>`;

        // 如果页面上已有提示，则替换
        if ($('.alert').length > 0) {
            $('.alert').replaceWith(alertHtml);
        } else {
            // 否则在页面顶部添加
            $('#knowledgeBaseContent').prepend(alertHtml);
        }
    }
});
