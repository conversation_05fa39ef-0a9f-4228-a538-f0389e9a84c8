"""
九猫小说分析写作系统共享API路由模块
此模块包含所有版本共用的API路由处理函数，确保不同版本的系统能够处理相同的API请求
"""
import logging
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify
from sqlalchemy import func
from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.analysis_result import AnalysisResult
import config

# 创建蓝图
shared_api_bp = Blueprint('shared_api', __name__)
logger = logging.getLogger(__name__)

# 获取小说所有分析结果
@shared_api_bp.route('/api/novel/<int:novel_id>/analysis', methods=['GET'])
@shared_api_bp.route('/api/novels/<int:novel_id>/analysis', methods=['GET'])  # 兼容旧路径
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/analysis', methods=['GET'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/analysis', methods=['GET'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/analysis', methods=['GET'])
def get_novel_all_analysis(novel_id):
    """
    获取小说的所有分析结果，包括整本书的分析维度和各章节的分析维度。
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取小说ID={novel_id}的所有分析结果（包括整本书和章节）")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到ID为{novel_id}的小说")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取整本书的所有分析结果
            book_analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()

            # 获取所有章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 转换整本书分析结果为字典列表
            book_results_list = []
            for result in book_analysis_results:
                # 处理元数据，确保可以序列化为JSON
                metadata = result.metadata
                if metadata and not isinstance(metadata, dict):
                    try:
                        # 尝试将元数据转换为字典
                        metadata = dict(metadata)
                    except:
                        # 如果无法转换，则使用字符串表示
                        metadata = str(metadata)

                # 获取推理过程内容
                reasoning_content = ''
                if hasattr(result, 'reasoning_content') and result.reasoning_content:
                    reasoning_content = result.reasoning_content
                elif isinstance(metadata, dict) and 'reasoning_content' in metadata:
                    reasoning_content = metadata['reasoning_content']

                result_dict = {
                    "id": result.id,
                    "novel_id": result.novel_id,
                    "dimension": result.dimension,
                    "content": result.content,
                    "reasoning_content": reasoning_content,
                    "metadata": metadata,
                    "created_at": result.created_at.isoformat() if result.created_at else None,
                    "updated_at": result.updated_at.isoformat() if result.updated_at else None
                }
                book_results_list.append(result_dict)

            # 获取并转换章节分析结果
            chapters_results = []
            for chapter in chapters:
                # 获取章节的所有分析结果
                chapter_analysis_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).all()

                # 转换章节分析结果为字典列表
                chapter_results_list = []
                for result in chapter_analysis_results:
                    # 处理元数据，确保可以序列化为JSON
                    metadata = result.analysis_metadata
                    if metadata and not isinstance(metadata, dict):
                        try:
                            # 尝试将元数据转换为字典
                            metadata = dict(metadata)
                        except:
                            # 如果无法转换，则使用字符串表示
                            metadata = str(metadata)

                    # 获取推理过程内容
                    reasoning_content = ''
                    if hasattr(result, 'reasoning_content') and result.reasoning_content:
                        reasoning_content = result.reasoning_content
                    elif isinstance(metadata, dict) and 'reasoning_content' in metadata:
                        reasoning_content = metadata['reasoning_content']

                    result_dict = {
                        "id": result.id,
                        "novel_id": result.novel_id,
                        "chapter_id": result.chapter_id,
                        "dimension": result.dimension,
                        "content": result.content,
                        "reasoning_content": reasoning_content,
                        "metadata": metadata,
                        "created_at": result.created_at.isoformat() if result.created_at else None,
                        "updated_at": result.updated_at.isoformat() if result.updated_at else None
                    }
                    chapter_results_list.append(result_dict)

                # 添加章节信息和分析结果
                chapters_results.append({
                    "id": chapter.id,
                    "novel_id": chapter.novel_id,
                    "chapter_number": chapter.chapter_number,
                    "title": chapter.title or f'第{chapter.chapter_number}章',
                    "word_count": chapter.word_count,
                    "analysis_results": chapter_results_list,
                    "analysis_count": len(chapter_results_list)
                })

            logger.info(f"成功获取小说《{novel.title}》的所有分析结果：整本书{len(book_results_list)}个维度，{len(chapters)}个章节")

            # 获取所有分析维度的定义
            all_dimensions = []
            for dim in config.ANALYSIS_DIMENSIONS:
                # 定义维度图标映射
                dimension_icons = {
                    'outline_analysis': 'project-diagram',
                    'chapter_outline': 'list-ol',
                    'language_style': 'language',
                    'rhythm_pacing': 'drum',
                    'structure': 'sitemap',
                    'sentence_variation': 'text-width',
                    'paragraph_length': 'paragraph',
                    'perspective_shifts': 'exchange-alt',
                    'paragraph_flow': 'stream',
                    'novel_characteristics': 'fingerprint',
                    'world_building': 'globe',
                    'character_relationships': 'users',
                    'opening_effectiveness': 'door-open',
                    'climax_pacing': 'mountain',
                    'theme_exploration': 'lightbulb'
                }

                dim_key = dim.get('key')
                icon = dimension_icons.get(dim_key, 'star')

                # 检查整本书是否已分析此维度
                is_book_analyzed = any(result['dimension'] == dim_key for result in book_results_list)

                all_dimensions.append({
                    'key': dim_key,
                    'name': dim.get('name', dim_key),
                    'icon': icon,
                    'is_analyzed': is_book_analyzed
                })

            return jsonify({
                "success": True,
                "novel": {
                    "id": novel.id,
                    "title": novel.title,
                    "author": novel.author,
                    "word_count": novel.word_count,
                    "created_at": novel.created_at.isoformat() if novel.created_at else None,
                    "updated_at": novel.updated_at.isoformat() if novel.updated_at else None,
                    "is_template": novel.novel_metadata and novel.novel_metadata.get('is_template', False) if novel.novel_metadata else False
                },
                "book_analyses": book_results_list,
                "chapters": chapters_results,
                "dimensions": all_dimensions,
                "total_dimensions": len(config.ANALYSIS_DIMENSIONS),
                "analyzed_book_dimensions": len(book_results_list),
                "total_chapters": len(chapters)
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"API获取小说所有分析结果时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})

# 获取小说分析推理过程内容
@shared_api_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_novel_reasoning_content(novel_id, dimension):
    """
    获取小说分析推理过程内容
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取小说分析推理过程 [小说ID: {novel_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到小说分析结果 [小说ID: {novel_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的分析结果',
                    'message': '请确保已完成该维度的分析'
                }), 404

            # 初始化变量
            reasoning_content = None

            # 尝试方法1: 直接从 reasoning_content 字段获取
            if hasattr(result, 'reasoning_content') and result.reasoning_content:
                reasoning_content = result.reasoning_content
                logger.info(f"从 reasoning_content 字段找到推理过程，长度: {len(reasoning_content)}")

                return jsonify({
                    "success": True,
                    "reasoning_content": reasoning_content,
                    "source": "direct_field",
                    "dimension": dimension,
                    "novel_id": novel_id
                })

            # 尝试方法2: 从元数据中获取
            if hasattr(result, 'analysis_metadata') and result.analysis_metadata:
                metadata = result.analysis_metadata

                # 如果是字符串，尝试解析为JSON
                if isinstance(metadata, str):
                    try:
                        import json
                        metadata = json.loads(metadata)
                        logger.info("成功将元数据从字符串解析为JSON")
                    except Exception as e:
                        logger.error(f"解析元数据时出错: {str(e)}")
                        metadata = {}

                # 记录元数据结构
                if isinstance(metadata, dict):
                    logger.info(f"元数据键: {list(metadata.keys())}")

                # 方法2.1: 直接从metadata的顶层字段查找
                if isinstance(metadata, dict) and 'reasoning_content' in metadata:
                    reasoning_content = metadata['reasoning_content']
                    logger.info(f"从元数据的reasoning_content字段找到推理过程，长度: {len(reasoning_content)}")

                    return jsonify({
                        "success": True,
                        "reasoning_content": reasoning_content,
                        "source": "metadata_top_level",
                        "dimension": dimension,
                        "novel_id": novel_id
                    })

                # 方法2.2: 从response字段查找
                if isinstance(metadata, dict) and 'response' in metadata:
                    response = metadata['response']

                    if isinstance(response, dict) and 'reasoning_content' in response:
                        reasoning_content = response['reasoning_content']
                        logger.info(f"从元数据的response.reasoning_content找到推理过程，长度: {len(reasoning_content)}")

                        return jsonify({
                            "success": True,
                            "reasoning_content": reasoning_content,
                            "source": "metadata_response",
                            "dimension": dimension,
                            "novel_id": novel_id
                        })

            # 如果没有找到推理过程，返回空内容
            logger.warning(f"未找到小说ID={novel_id}，维度={dimension}的推理过程内容")
            return jsonify({
                "success": True,
                "reasoning_content": "未找到推理过程内容。",
                "source": "default",
                "dimension": dimension,
                "novel_id": novel_id
            })
        finally:
            session.close()
    except Exception as e:
        import traceback
        logger.error(f"获取推理过程内容时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# 获取章节分析推理过程内容
@shared_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_chapter_reasoning_content(novel_id, chapter_id, dimension):
    """
    获取章节分析推理过程内容
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取章节分析推理过程 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        session = Session()
        try:
            from src.models.chapter_analysis_result import ChapterAnalysisResult
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()
            if not result:
                logger.error(f"未找到章节分析结果 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到章节分析结果',
                    'message': '请确保已完成该章节的该维度分析'
                }), 404

            return jsonify({
                'success': True,
                'dimension': dimension,
                'reasoning_content': result.reasoning_content,  # 修改字段名为reasoning_content
                'content': result.reasoning_content,  # 保留content字段以兼容旧代码
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

# 获取小说信息
@shared_api_bp.route('/api/novel/<int:novel_id>', methods=['GET'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>', methods=['GET'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>', methods=['GET'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>', methods=['GET'])
def get_novel_info(novel_id):
    """
    获取小说信息
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取小说信息 [小说ID: {novel_id}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 [小说ID: {novel_id}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取分析结果
            analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            available_dimensions = [result.dimension for result in analysis_results]

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 获取章节分析结果
            chapter_analysis_results = {}
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).all()

                if chapter_results:
                    chapter_analysis_results[chapter.id] = {result.dimension: result for result in chapter_results}

            # 检查是否可以设为参考蓝本
            all_dimensions = [d['key'] for d in config.ANALYSIS_DIMENSIONS]
            book_dimensions_complete = all(dim in available_dimensions for dim in all_dimensions)

            chapters_dimensions_complete = True
            for chapter in chapters:
                chapter_results = chapter_analysis_results.get(chapter.id, {})
                if not all(dim in chapter_results for dim in all_dimensions):
                    chapters_dimensions_complete = False
                    break

            # 转换为字典
            novel_dict = {
                'id': novel.id,
                'title': novel.title,
                'author': novel.author,
                'word_count': novel.word_count,
                'created_at': novel.created_at.isoformat() if novel.created_at else None,
                'updated_at': novel.updated_at.isoformat() if novel.updated_at else None,
                'available_dimensions': available_dimensions,
                'chapter_count': len(chapters),
                'is_fully_analyzed': book_dimensions_complete and chapters_dimensions_complete,
                'is_partially_analyzed': len(available_dimensions) > 0 and not (book_dimensions_complete and chapters_dimensions_complete),
                'is_template': novel.novel_metadata and novel.novel_metadata.get('is_template', False) if novel.novel_metadata else False,
                'can_set_as_template': book_dimensions_complete and chapters_dimensions_complete
            }

            # 转换章节为字典列表
            chapters_list = []
            for chapter in chapters:
                chapter_dict = {
                    'id': chapter.id,
                    'novel_id': chapter.novel_id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'word_count': chapter.word_count,
                    'content_preview': chapter.content[:200] + '...' if len(chapter.content) > 200 else chapter.content,
                    'analysis_count': len(chapter_analysis_results.get(chapter.id, {}))
                }
                chapters_list.append(chapter_dict)

            # 强制添加所有分析维度，即使小说没有分析过这些维度
            # 这样前端可以显示所有可能的维度
            all_dimension_names = {}
            for dim in config.ANALYSIS_DIMENSIONS:
                all_dimension_names[dim['key']] = dim['name']

            # 创建维度列表，包含所有可能的维度
            dimensions_list = []
            for dim_key, dim_name in all_dimension_names.items():
                # 定义维度图标映射
                dimension_icons = {
                    'outline_analysis': 'project-diagram',
                    'chapter_outline': 'list-ol',
                    'language_style': 'language',
                    'rhythm_pacing': 'drum',
                    'structure': 'sitemap',
                    'sentence_variation': 'text-width',
                    'paragraph_length': 'paragraph',
                    'perspective_shifts': 'exchange-alt',
                    'paragraph_flow': 'stream',
                    'novel_characteristics': 'fingerprint',
                    'world_building': 'globe',
                    'character_relationships': 'users',
                    'opening_effectiveness': 'door-open',
                    'climax_pacing': 'mountain',
                    'theme_exploration': 'lightbulb'
                }

                icon = dimension_icons.get(dim_key, 'star')

                dimensions_list.append({
                    'key': dim_key,
                    'name': dim_name,
                    'icon': icon,
                    'is_analyzed': dim_key in available_dimensions
                })

            return jsonify({
                'success': True,
                'novel': novel_dict,
                'chapters': chapters_list,
                'dimensions': dimensions_list
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取小说列表
@shared_api_bp.route('/api/novels', methods=['GET'])
@shared_api_bp.route('/v3.0/api/novels', methods=['GET'])
@shared_api_bp.route('/v3.1/api/novels', methods=['GET'])
@shared_api_bp.route('/v3.5/api/novels', methods=['GET'])
def get_novels():
    """
    获取小说列表
    支持所有版本的API路径
    """
    try:
        logger.info("获取小说列表")
        session = Session()
        try:
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()

            # 转换为JSON格式
            novels_json = []
            for novel in novels:
                novels_json.append({
                    'id': novel.id,
                    'title': novel.title,
                    'author': novel.author,
                    'word_count': novel.word_count,
                    'created_at': novel.created_at.isoformat() if novel.created_at else None,
                    'updated_at': novel.updated_at.isoformat() if novel.updated_at else None
                })

            return jsonify({
                'success': True,
                'novels': novels_json
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取小说分析结果
@shared_api_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/analysis/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/analysis/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/analysis/<dimension>', methods=['GET'])
def get_novel_analysis(novel_id, dimension):
    """
    获取小说分析结果
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取小说分析结果 [小说ID: {novel_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                # 获取维度名称
                dimension_name = dimension
                for dim in config.ANALYSIS_DIMENSIONS:
                    if dim.get('key') == dimension:
                        dimension_name = dim.get('name', dimension)
                        break

                # 返回一个空的分析结果
                return jsonify({
                    "success": False,
                    "error": "未找到分析结果",
                    "message": f"该小说尚未进行{dimension_name}分析，请先进行分析。",
                    "status": 404
                }), 404

            # 转换为字典，确保元数据可以序列化为JSON
            # 处理元数据，确保可以序列化为JSON
            metadata = result.metadata if hasattr(result, 'metadata') else None
            if metadata and not isinstance(metadata, dict):
                try:
                    # 尝试将元数据转换为字典
                    metadata = dict(metadata)
                except:
                    # 如果无法转换，则使用字符串表示
                    metadata = str(metadata)

            result_dict = {
                "id": result.id,
                "novel_id": result.novel_id,
                "dimension": result.dimension,
                "content": result.content,
                "metadata": metadata,
                "created_at": result.created_at.isoformat() if result.created_at else None,
                "updated_at": result.updated_at.isoformat() if result.updated_at else None
            }

            return jsonify({
                "success": True,
                "result": result_dict
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({"success": False, "error": str(e)}), 500

# 获取章节分析结果
@shared_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
def get_chapter_analysis(novel_id, chapter_id, dimension):
    """
    获取章节分析结果
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取章节分析结果 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        # 导入章节分析服务
        from src.services.chapter_analysis_service import ChapterAnalysisService

        # 获取分析结果
        result = ChapterAnalysisService.get_chapter_analysis_result(chapter_id, dimension)

        # 记录日志，帮助调试
        logger.info(f"获取章节 {chapter_id} 的 {dimension} 分析结果: {result['success']}")

        # 如果没有找到分析结果，尝试创建一个空结果
        if not result['success'] and "未找到分析结果" in result.get('error', ''):
            # 获取维度名称
            dimension_name = dimension
            for dim in config.ANALYSIS_DIMENSIONS:
                if dim.get('key') == dimension:
                    dimension_name = dim.get('name', dimension)
                    break

            # 创建一个空的分析结果
            empty_result = {
                "success": True,
                "result": {
                    "id": None,
                    "chapter_id": chapter_id,
                    "dimension": dimension,
                    "content": f"<div class='alert alert-info'>该章节尚未进行{dimension_name}分析，请先进行分析。</div>",
                    "metadata": {}
                }
            }
            return jsonify(empty_result)

        return jsonify(result)
    except Exception as e:
        logger.error(f"获取章节分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({"success": False, "error": str(e)}), 500

# 删除小说
@shared_api_bp.route('/api/novel/<int:novel_id>/delete', methods=['POST'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/delete', methods=['POST'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/delete', methods=['POST'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/delete', methods=['POST'])
def delete_novel(novel_id):
    """
    删除小说
    支持所有版本的API路径
    """
    try:
        logger.info(f"删除小说 [小说ID: {novel_id}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 [小说ID: {novel_id}]")
                return jsonify({
                    'success': False,
                    'message': '未找到指定小说'
                }), 404

            # 获取小说标题用于日志
            novel_title = novel.title

            # 删除相关的章节分析结果
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
            for chapter in chapters:
                session.query(ChapterAnalysisResult).filter_by(chapter_id=chapter.id).delete()

            # 删除相关的章节
            session.query(Chapter).filter_by(novel_id=novel_id).delete()

            # 删除相关的分析结果
            session.query(AnalysisResult).filter_by(novel_id=novel_id).delete()

            # 删除小说
            session.delete(novel)
            session.commit()

            logger.info(f"小说《{novel_title}》已删除")
            return jsonify({
                'success': True,
                'message': f'小说《{novel_title}》已删除'
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"删除小说时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 重新分析小说的指定维度
@shared_api_bp.route('/api/novel/<int:novel_id>/reanalyze/<dimension>', methods=['POST'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/reanalyze/<dimension>', methods=['POST'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/reanalyze/<dimension>', methods=['POST'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/reanalyze/<dimension>', methods=['POST'])
def reanalyze_novel(novel_id, dimension):
    """
    重新分析小说的指定维度
    支持所有版本的API路径
    """
    try:
        logger.info(f"重新分析小说 [小说ID: {novel_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 [小说ID: {novel_id}]")
                return jsonify({
                    'success': False,
                    'message': '未找到指定小说'
                }), 404

            # 保存小说标题，避免在会话关闭后使用novel对象
            novel_title = novel.title

            # 删除现有的分析结果
            session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).delete()
            session.commit()

            # 导入单维度分析模块
            from src.api.analyze_dimension import analyze_dimension

            # 在当前会话中重新分析
            try:
                # 使用新的会话进行分析，避免分离实例问题
                analyze_dimension(novel, dimension, use_real_api=True)
                logger.info(f"成功启动对小说《{novel_title}》的{dimension}维度重新分析")

                # 返回成功响应
                return jsonify({
                    'success': True,
                    'message': f'小说《{novel_title}》的{dimension}维度已开始重新分析'
                })
            except Exception as analyze_error:
                logger.error(f"重新分析时出错: {str(analyze_error)}", exc_info=True)
                # 返回更详细的错误信息
                return jsonify({
                    'success': False,
                    'message': f'分析过程中出错: {str(analyze_error)}'
                }), 500
        finally:
            session.close()
    except Exception as e:
        logger.error(f"重新分析小说时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 设置小说为参考蓝本
@shared_api_bp.route('/api/novel/<int:novel_id>/set_as_template', methods=['POST'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/set_as_template', methods=['POST'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/set_as_template', methods=['POST'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/set_as_template', methods=['POST'])
def set_as_template(novel_id):
    """
    设置小说为参考蓝本
    支持所有版本的API路径
    """
    try:
        logger.info(f"设置小说为参考蓝本 [小说ID: {novel_id}]")
        # 使用新的会话，确保获取最新数据
        session = Session()
        session.expire_all()  # 清除会话缓存

        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"设置参考蓝本失败: 未找到ID为{novel_id}的小说")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            logger.info(f"开始设置小说《{novel.title}》(ID: {novel_id})为参考蓝本")

            # 检查是否满足参考蓝本条件
            # 获取分析结果
            analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            available_dimensions = [result.dimension for result in analysis_results]

            logger.info(f"小说《{novel.title}》已完成的维度: {available_dimensions}")

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
            logger.info(f"小说《{novel.title}》共有{len(chapters)}个章节")

            # 获取章节分析结果
            chapter_analysis_results = {}
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).all()

                if chapter_results:
                    chapter_analysis_results[chapter.id] = {result.dimension: result for result in chapter_results}

            # 检查是否满足参考蓝本条件
            all_dimensions = [d['key'] for d in config.ANALYSIS_DIMENSIONS]
            book_dimensions_complete = all(dim in available_dimensions for dim in all_dimensions)

            if not book_dimensions_complete:
                missing_dimensions = [dim for dim in all_dimensions if dim not in available_dimensions]
                logger.warning(f"小说《{novel.title}》整本书缺少维度: {missing_dimensions}")

            chapters_dimensions_complete = True
            incomplete_chapters = []
            for chapter in chapters:
                chapter_results = chapter_analysis_results.get(chapter.id, {})
                chapter_dimensions = list(chapter_results.keys())
                if not all(dim in chapter_dimensions for dim in all_dimensions):
                    chapters_dimensions_complete = False
                    missing_chapter_dims = [dim for dim in all_dimensions if dim not in chapter_dimensions]
                    incomplete_chapters.append({
                        'chapter_id': chapter.id,
                        'chapter_number': chapter.chapter_number,
                        'chapter_title': chapter.title,
                        'missing_dimensions': missing_chapter_dims
                    })

            if not chapters_dimensions_complete:
                logger.warning(f"小说《{novel.title}》有{len(incomplete_chapters)}个章节未完成所有维度分析")
                for ch in incomplete_chapters[:3]:  # 只记录前3个不完整的章节
                    logger.warning(f"章节 {ch['chapter_number']} '{ch['chapter_title']}' 缺少维度: {ch['missing_dimensions']}")

            if not (book_dimensions_complete and chapters_dimensions_complete):
                logger.error(f"设置参考蓝本失败: 小说《{novel.title}》不满足参考蓝本条件")
                return jsonify({
                    'success': False,
                    'error': '该小说不满足参考蓝本条件，需要完成所有维度的分析'
                }), 400

            # 先取消所有其他小说的参考蓝本设置
            # 查询所有标记为参考蓝本的小说
            all_novels = session.query(Novel).all()
            for other_novel in all_novels:
                if (other_novel.id != novel_id and
                    other_novel.novel_metadata and
                    other_novel.novel_metadata.get('is_template')):
                    # 记录日志
                    logger.info(f"取消小说《{other_novel.title}》(ID: {other_novel.id})的参考蓝本设置，因为要将小说《{novel.title}》(ID: {novel_id})设为参考蓝本")
                    # 取消参考蓝本设置 - 使用字典替换方式
                    new_metadata = dict(other_novel.novel_metadata)
                    new_metadata['is_template'] = False
                    new_metadata['template_removed_at'] = datetime.now().isoformat()
                    other_novel.novel_metadata = new_metadata
                    session.add(other_novel)

            # 更新小说元数据，标记为参考蓝本
            if not novel.novel_metadata:
                novel.novel_metadata = {}

            # 创建一个全新的字典，而不是修改现有字典
            # 这样SQLAlchemy会将整个字段视为已更改
            new_metadata = dict(novel.novel_metadata)  # 复制原有数据
            new_metadata['is_template'] = True
            new_metadata['template_created_at'] = datetime.now().isoformat()
            novel.novel_metadata = new_metadata  # 整体替换字典

            # 确保此小说的元数据字段已更新
            session.add(novel)
            # 提交所有更改
            session.commit()
            logger.info(f"数据库事务已提交: 小说《{novel.title}》(ID: {novel_id})已成功设置为参考蓝本")

            # 清除之前的缓存
            session.expire_all()
            # 重新从数据库加载小说对象，确保获取到最新数据
            session.refresh(novel)

            # 验证更改是否已保存
            if novel.novel_metadata and novel.novel_metadata.get('is_template'):
                logger.info(f"验证成功: 小说《{novel.title}》的novel_metadata中is_template已设置为{novel.novel_metadata.get('is_template')}")
            else:
                logger.warning(f"验证失败: 小说《{novel.title}》的novel_metadata中is_template未正确设置: {novel.novel_metadata}")
                # 重试一次设置，使用更强制的方式
                try:
                    # 直接执行SQL更新语句
                    import json
                    new_metadata = dict(novel.novel_metadata)  # 再次复制
                    new_metadata['is_template'] = True
                    new_metadata['template_created_at'] = datetime.now().isoformat()

                    # 将字典转换为JSON字符串
                    metadata_json = json.dumps(new_metadata)

                    # 执行直接更新SQL
                    session.execute(
                        f"UPDATE novels SET novel_metadata = '{metadata_json}' WHERE id = {novel_id}"
                    )
                    session.commit()
                    logger.info("已通过直接SQL更新重试设置参考蓝本")
                except Exception as sql_e:
                    logger.error(f"SQL更新尝试失败: {str(sql_e)}")

                # 再次验证
                session.refresh(novel)
                if novel.novel_metadata and novel.novel_metadata.get('is_template'):
                    logger.info(f"重试后验证成功: 小说《{novel.title}》的novel_metadata已正确设置")
                else:
                    logger.error(f"重试后验证仍失败: 小说《{novel.title}》的novel_metadata未正确设置: {novel.novel_metadata}")
                    return jsonify({
                        'success': False,
                        'error': '数据库更新验证失败，请重试'
                    }), 500

            # 记录详细日志
            logger.info(f"小说《{novel.title}》(ID: {novel_id})已成功设置为参考蓝本")
            logger.info(f"参考蓝本元数据: {novel.novel_metadata}")

            return jsonify({
                'success': True,
                'message': f'小说《{novel.title}》已设置为参考蓝本',
                'novel_id': novel_id,
                'timestamp': datetime.now().isoformat()
            })
        finally:
            session.close()
            logger.info(f"数据库会话已关闭")
    except Exception as e:
        logger.error(f"设置参考蓝本时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取参考蓝本列表
@shared_api_bp.route('/api/reference_templates', methods=['GET'])
@shared_api_bp.route('/v3.0/api/reference_templates', methods=['GET'])
@shared_api_bp.route('/v3.1/api/reference_templates', methods=['GET'])
@shared_api_bp.route('/v3.5/api/reference_templates', methods=['GET'])
def get_reference_templates():
    """
    获取参考蓝本列表
    支持所有版本的API路径
    """
    try:
        logger.info("获取参考蓝本列表")
        session = Session()
        try:
            # 查询所有标记为参考蓝本的小说
            templates = []
            novels = session.query(Novel).all()

            for novel in novels:
                if novel.novel_metadata and novel.novel_metadata.get('is_template'):
                    # 获取章节数量
                    chapter_count = session.query(Chapter).filter_by(novel_id=novel.id).count()

                    templates.append({
                        'id': novel.id,
                        'title': novel.title,
                        'author': novel.author,
                        'word_count': novel.word_count,
                        'created_at': novel.created_at.isoformat() if novel.created_at else None,
                        'template_created_at': novel.novel_metadata.get('template_created_at'),
                        'chapter_count': chapter_count
                    })

            return jsonify({
                'success': True,
                'templates': templates
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取参考蓝本列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 一键分析所有章节维度
@shared_api_bp.route('/api/novel/<int:novel_id>/analyze_all_chapters', methods=['POST'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/analyze_all_chapters', methods=['POST'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/analyze_all_chapters', methods=['POST'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/analyze_all_chapters', methods=['POST'])
def analyze_all_chapters(novel_id):
    """
    一键分析小说的所有章节的所有维度
    支持所有版本的API路径
    """
    try:
        logger.info(f"一键分析小说的所有章节 [小说ID: {novel_id}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 [小说ID: {novel_id}]")
                return jsonify({
                    'success': False,
                    'message': '未找到指定小说'
                }), 404

            # 获取所有章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
            if not chapters:
                logger.error(f"小说没有章节 [小说ID: {novel_id}]")
                return jsonify({
                    'success': False,
                    'message': '该小说没有章节'
                }), 400

            # 获取所有分析维度
            all_dimensions = [d['key'] for d in config.ANALYSIS_DIMENSIONS]

            # 为每个章节启动所有维度的分析
            chapter_count = len(chapters)
            dimension_count = len(all_dimensions)
            total_tasks = chapter_count * dimension_count

            logger.info(f"开始一键分析小说《{novel.title}》的所有章节({chapter_count}章)的所有维度({dimension_count}个)")

            # 导入章节分析服务
            from src.services.chapter_analysis_service import ChapterAnalysisService

            # 逐个章节、逐个维度启动分析
            for chapter in chapters:
                for dimension in all_dimensions:
                    # 启动章节分析
                    try:
                        ChapterAnalysisService.analyze_chapter(
                            chapter_id=chapter.id,
                            dimension=dimension,
                            use_cache=True  # 使用缓存，避免重复分析
                        )
                    except Exception as analyze_error:
                        logger.error(f"分析章节 {chapter.id} 的维度 {dimension} 时出错: {str(analyze_error)}", exc_info=True)
                        # 继续分析其他章节和维度，不中断整个过程

            return jsonify({
                'success': True,
                'message': f'已启动对小说《{novel.title}》的所有章节({chapter_count}章)的所有维度({dimension_count}个)分析，共{total_tasks}个任务'
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"一键分析所有章节维度时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 获取统计数据
@shared_api_bp.route('/api/statistics', methods=['GET'])
@shared_api_bp.route('/v3.0/api/statistics', methods=['GET'])
@shared_api_bp.route('/v3.1/api/statistics', methods=['GET'])
@shared_api_bp.route('/v3.5/api/statistics', methods=['GET'])
def get_statistics():
    """
    获取统计数据
    支持所有版本的API路径
    """
    try:
        logger.info("获取统计数据")
        session = Session()
        try:
            # 获取真实数据
            novel_count = session.query(Novel).count()
            chapter_count = session.query(Chapter).count()

            # 计算总字数
            total_words = session.query(func.sum(Novel.word_count)).scalar() or 0

            # 计算平均每章字数
            avg_words_per_chapter = total_words / chapter_count if chapter_count > 0 else 0

            # 获取系统运行时间（模拟数据）
            total_hours = 20.1

            stats = {
                'total_novels': novel_count,
                'total_chapters': chapter_count,
                'total_words': int(total_words),
                'avg_words_per_chapter': int(avg_words_per_chapter),
                'total_hours': total_hours
            }

            return jsonify({
                'success': True,
                'stats': stats
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取统计数据时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取参考蓝本中特定章节的特定维度分析结果
@shared_api_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.0/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.1/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.5/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
def get_template_chapter_dimension_analysis(template_id, chapter_id, dimension):
    """
    获取参考蓝本中特定章节的特定维度分析结果和推理过程
    支持所有版本的API路径
    """
    try:
        # 获取强制刷新参数
        force_refresh = request.args.get('force_refresh', 'false').lower() == 'true'
        timestamp = request.args.get('timestamp', '')

        logger.info(f"获取参考蓝本中特定章节的特定维度分析结果 [参考蓝本ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}, 强制刷新: {force_refresh}, 时间戳: {timestamp}]")

        # 创建新会话并清除缓存
        session = Session()
        session.expire_all()  # 清除会话缓存

        try:
            # 检查参考蓝本是否存在
            template = session.query(Novel).get(template_id)
            if not template:
                logger.error(f"未找到参考蓝本 [参考蓝本ID: {template_id}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 检查参考蓝本是否真的是参考蓝本
            if not (template.novel_metadata and template.novel_metadata.get('is_template')):
                logger.error(f"指定小说不是参考蓝本 [小说ID: {template_id}]")
                return jsonify({
                    'success': False,
                    'error': '指定小说不是参考蓝本'
                }), 400

            # 检查章节是否存在
            chapter = session.query(Chapter).filter_by(
                novel_id=template_id,
                id=chapter_id
            ).first()

            if not chapter:
                logger.error(f"未找到章节 [参考蓝本ID: {template_id}, 章节ID: {chapter_id}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 获取章节分析结果
            chapter_analysis = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not chapter_analysis:
                logger.error(f"未找到章节分析结果 [参考蓝本ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的章节分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'template_id': template_id,
                'chapter_id': chapter_id,
                'dimension': dimension,
                'analysis': {
                    'content': chapter_analysis.content,
                    'reasoning_content': chapter_analysis.reasoning_content,
                    'created_at': chapter_analysis.created_at.isoformat() if chapter_analysis.created_at else None,
                    'updated_at': chapter_analysis.updated_at.isoformat() if chapter_analysis.updated_at else None
                }
            }

            return jsonify(response_data)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取参考蓝本中特定章节的特定维度分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取参考蓝本中特定章节的特定维度推理过程
@shared_api_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
@shared_api_bp.route('/v3.0/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
@shared_api_bp.route('/v3.1/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
@shared_api_bp.route('/v3.5/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_template_chapter_reasoning_content(template_id, chapter_id, dimension):
    """
    获取参考蓝本中特定章节的特定维度推理过程
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取参考蓝本中特定章节的特定维度推理过程 [参考蓝本ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")

        # 创建新会话并清除缓存
        session = Session()
        session.expire_all()  # 清除会话缓存

        try:
            # 检查参考蓝本是否存在
            template = session.query(Novel).get(template_id)
            if not template:
                logger.error(f"未找到参考蓝本 [参考蓝本ID: {template_id}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 检查参考蓝本是否真的是参考蓝本
            if not (template.novel_metadata and template.novel_metadata.get('is_template')):
                logger.error(f"指定小说不是参考蓝本 [小说ID: {template_id}]")
                return jsonify({
                    'success': False,
                    'error': '指定小说不是参考蓝本'
                }), 400

            # 检查章节是否存在
            chapter = session.query(Chapter).filter_by(
                novel_id=template_id,
                id=chapter_id
            ).first()

            if not chapter:
                logger.error(f"未找到章节 [参考蓝本ID: {template_id}, 章节ID: {chapter_id}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 获取章节分析结果
            chapter_analysis = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not chapter_analysis:
                logger.error(f"未找到章节分析结果 [参考蓝本ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的章节分析结果'
                }), 404

            # 检查是否有推理过程内容
            if not chapter_analysis.reasoning_content:
                logger.warning(f"章节分析结果没有推理过程内容 [参考蓝本ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': True,
                    'content': "API未返回推理过程",
                    'reasoning_content': "API未返回推理过程"
                })

            # 构建响应数据
            response_data = {
                'success': True,
                'dimension': dimension,
                'content': chapter_analysis.reasoning_content,  # 保留content字段以兼容旧代码
                'reasoning_content': chapter_analysis.reasoning_content,
                'created_at': chapter_analysis.created_at.isoformat() if chapter_analysis.created_at else None,
                'updated_at': chapter_analysis.updated_at.isoformat() if chapter_analysis.updated_at else None
            }

            logger.info(f"成功获取参考蓝本中特定章节的特定维度推理过程 [参考蓝本ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
            return jsonify(response_data)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取参考蓝本中特定章节的特定维度推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 将小说分析结果同步到章节
@shared_api_bp.route('/api/novel/<int:novel_id>/sync_to_chapters', methods=['POST'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/sync_to_chapters', methods=['POST'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/sync_to_chapters', methods=['POST'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/sync_to_chapters', methods=['POST'])
def sync_to_chapters(novel_id):
    """
    将小说分析结果同步到章节
    支持所有版本的API路径
    """
    try:
        logger.info(f"将小说分析结果同步到章节 [小说ID: {novel_id}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 [小说ID: {novel_id}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取小说的所有分析结果
            analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            if not analysis_results:
                logger.error(f"小说没有分析结果 [小说ID: {novel_id}]")
                return jsonify({
                    'success': False,
                    'error': '该小说没有分析结果，请先进行分析'
                }), 400

            # 获取小说的所有章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
            if not chapters:
                logger.error(f"小说没有章节 [小说ID: {novel_id}]")
                return jsonify({
                    'success': False,
                    'error': '该小说没有章节'
                }), 400

            # 导入章节分析服务
            from src.services.analysis_service import aggregate_chapter_analyses

            # 同步分析结果到章节
            sync_results = []
            for dimension in [result.dimension for result in analysis_results]:
                try:
                    # 调用聚合函数
                    result = aggregate_chapter_analyses(novel_id, dimension)
                    sync_results.append({
                        'dimension': dimension,
                        'success': result.get('success', False),
                        'message': result.get('message', '同步成功')
                    })
                except Exception as sync_error:
                    logger.error(f"同步维度 {dimension} 时出错: {str(sync_error)}", exc_info=True)
                    sync_results.append({
                        'dimension': dimension,
                        'success': False,
                        'message': str(sync_error)
                    })

            # 统计成功和失败的数量
            success_count = sum(1 for result in sync_results if result['success'])
            fail_count = len(sync_results) - success_count

            return jsonify({
                'success': True,
                'message': f'同步完成: {success_count}个维度成功，{fail_count}个维度失败',
                'results': sync_results
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"将小说分析结果同步到章节时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 自动写作功能
@shared_api_bp.route('/api/auto_write', methods=['POST'])
@shared_api_bp.route('/v3.0/api/auto_write', methods=['POST'])
@shared_api_bp.route('/v3.1/api/auto_write', methods=['POST'])
@shared_api_bp.route('/v3.5/api/auto_write', methods=['POST'])
def auto_write():
    """
    自动写作
    支持所有版本的API路径
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        template_id = data.get('template_id')
        if not template_id:
            return jsonify({
                'success': False,
                'error': '预设模板ID不能为空'
            }), 400

        prompt = data.get('prompt', '')

        logger.info(f"开始自动写作: 模板ID={template_id}, 提示={prompt}")

        # 模拟自动写作过程
        session = Session()
        try:
            # 获取预设模板
            from src.models.preset import Preset
            preset = session.query(Preset).get(template_id)
            if not preset:
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设模板'
                }), 404

            # 获取参考蓝本
            template = None
            if preset.meta_info and preset.meta_info.get('template_id'):
                template_id = preset.meta_info.get('template_id')
                template = session.query(Novel).get(template_id)

            # 生成内容
            content = f"# 基于《{preset.title}》的自动写作\n\n"
            content += f"## 写作提示\n{prompt}\n\n"
            content += f"## 生成内容\n"
            content += f"这是基于预设模板《{preset.title}》"

            if template:
                content += f"和参考蓝本《{template.title}》"

            content += "生成的内容。\n\n"
            content += "自动写作功能正在开发中，敬请期待！\n\n"
            content += "您可以在此处查看完整的预设模板内容，并根据需要进行修改。"

            # 构建响应数据
            response_data = {
                'success': True,
                'content': {
                    'title': f"基于《{preset.title}》的自动写作",
                    'content': content,
                    'original_text': template.content if template else ""
                }
            }

            logger.info(f"自动写作完成: 模板ID={template_id}")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"自动写作时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 比较两段文本
@shared_api_bp.route('/api/compare_texts', methods=['POST'])
@shared_api_bp.route('/v3.0/api/compare_texts', methods=['POST'])
@shared_api_bp.route('/v3.1/api/compare_texts', methods=['POST'])
@shared_api_bp.route('/v3.5/api/compare_texts', methods=['POST'])
def compare_texts():
    """
    比较两段文本，找出相似部分
    支持所有版本的API路径
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        text1 = data.get('text1', '')
        text2 = data.get('text2', '')
        similarity_threshold = float(data.get('similarity_threshold', 0.7))

        if not text1 or not text2:
            return jsonify({
                'success': False,
                'error': '两段文本都不能为空'
            }), 400

        logger.info(f"开始比较文本: 文本1长度={len(text1)}, 文本2长度={len(text2)}")

        # 将文本分割成段落
        import re
        import difflib
        paragraphs1 = re.split(r'\n+', text1)
        paragraphs2 = re.split(r'\n+', text2)

        # 计算整体相似度
        similarity_score = difflib.SequenceMatcher(None, text1, text2).ratio()

        # 查找相似段落
        similar_segments = []
        for i, p1 in enumerate(paragraphs1):
            if len(p1.strip()) < 10:  # 忽略太短的段落
                continue

            for j, p2 in enumerate(paragraphs2):
                if len(p2.strip()) < 10:  # 忽略太短的段落
                    continue

                # 计算段落相似度
                similarity = difflib.SequenceMatcher(None, p1, p2).ratio()

                if similarity >= similarity_threshold:
                    # 计算在原文中的位置
                    text1_start = text1.find(p1)
                    text1_end = text1_start + len(p1)
                    text2_start = text2.find(p2)
                    text2_end = text2_start + len(p2)

                    similar_segments.append({
                        'text1_start': text1_start,
                        'text1_end': text1_end,
                        'text1_segment': p1,
                        'text2_start': text2_start,
                        'text2_end': text2_end,
                        'text2_segment': p2,
                        'similarity': similarity
                    })

        # 按相似度排序
        similar_segments.sort(key=lambda x: x['similarity'], reverse=True)

        # 构建响应数据
        response_data = {
            'success': True,
            'comparison_result': {
                'similarity_score': similarity_score,
                'similar_segments': similar_segments
            }
        }

        logger.info(f"文本比较完成: 相似度={similarity_score:.2f}, 找到{len(similar_segments)}个相似片段")
        return jsonify(response_data), 200
    except Exception as e:
        logger.error(f"比较文本时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取预设内容列表
@shared_api_bp.route('/api/presets', methods=['GET'])
@shared_api_bp.route('/v3.0/api/presets', methods=['GET'])
@shared_api_bp.route('/v3.1/api/presets', methods=['GET'])
@shared_api_bp.route('/v3.5/api/presets', methods=['GET'])
def get_presets():
    """
    获取预设内容列表
    支持所有版本的API路径
    """
    try:
        logger.info("获取预设内容列表")
        session = Session()
        try:
            # 检查表是否存在
            try:
                from src.models.preset import Preset
                # 查询所有预设内容
                presets = session.query(Preset).order_by(Preset.created_at.desc()).all()

                # 转换为字典列表
                presets_list = [preset.to_dict() for preset in presets]

                return jsonify({
                    'success': True,
                    'presets': presets_list
                })
            except Exception as e:
                logger.error(f"查询预设内容列表时出错: {str(e)}", exc_info=True)
                # 如果查询失败，返回空列表
                return jsonify({
                    'success': True,
                    'presets': []
                })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设内容列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取单个预设内容
@shared_api_bp.route('/api/presets/<int:preset_id>', methods=['GET'])
@shared_api_bp.route('/v3.0/api/presets/<int:preset_id>', methods=['GET'])
@shared_api_bp.route('/v3.1/api/presets/<int:preset_id>', methods=['GET'])
@shared_api_bp.route('/v3.5/api/presets/<int:preset_id>', methods=['GET'])
def get_preset(preset_id):
    """
    获取单个预设内容
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取预设内容 [预设ID: {preset_id}]")
        session = Session()
        try:
            try:
                from src.models.preset import Preset
                # 查询预设内容
                preset = session.query(Preset).get(preset_id)
                if not preset:
                    return jsonify({
                        'success': False,
                        'error': '未找到指定预设内容'
                    }), 404

                return jsonify({
                    'success': True,
                    'preset': preset.to_dict()
                })
            except Exception as e:
                logger.error(f"查询预设内容时出错: {str(e)}", exc_info=True)
                return jsonify({
                    'success': False,
                    'error': '查询预设内容时出错'
                }), 500
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 创建或更新预设内容
@shared_api_bp.route('/api/presets', methods=['POST'])
@shared_api_bp.route('/v3.0/api/presets', methods=['POST'])
@shared_api_bp.route('/v3.1/api/presets', methods=['POST'])
@shared_api_bp.route('/v3.5/api/presets', methods=['POST'])
def create_or_update_preset():
    """
    创建或更新预设内容
    支持所有版本的API路径
    """
    try:
        # 获取请求数据
        data = request.json or {}
        preset_id = data.get('id')
        title = data.get('title')
        content = data.get('content')
        category = data.get('category', 'other')

        if not title:
            return jsonify({
                'success': False,
                'error': '预设标题不能为空'
            }), 400

        if not content:
            return jsonify({
                'success': False,
                'error': '预设内容不能为空'
            }), 400

        session = Session()
        try:
            try:
                from src.models.preset import Preset
                if preset_id:
                    # 更新预设内容
                    preset = session.query(Preset).get(preset_id)
                    if not preset:
                        return jsonify({
                            'success': False,
                            'error': '未找到指定预设内容'
                        }), 404

                    preset.title = title
                    preset.content = content
                    preset.category = category
                    preset.updated_at = datetime.now()
                else:
                    # 创建新预设内容
                    preset = Preset(
                        title=title,
                        content=content,
                        category=category,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(preset)

                session.commit()

                return jsonify({
                    'success': True,
                    'preset': preset.to_dict()
                })
            except Exception as e:
                session.rollback()
                logger.error(f"保存预设内容时出错: {str(e)}", exc_info=True)
                return jsonify({
                    'success': False,
                    'error': '保存预设内容时出错'
                }), 500
        finally:
            session.close()
    except Exception as e:
        logger.error(f"创建或更新预设内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 删除预设内容
@shared_api_bp.route('/api/presets/<int:preset_id>', methods=['DELETE'])
@shared_api_bp.route('/v3.0/api/presets/<int:preset_id>', methods=['DELETE'])
@shared_api_bp.route('/v3.1/api/presets/<int:preset_id>', methods=['DELETE'])
@shared_api_bp.route('/v3.5/api/presets/<int:preset_id>', methods=['DELETE'])
def delete_preset(preset_id):
    """
    删除预设内容
    支持所有版本的API路径
    """
    try:
        logger.info(f"删除预设内容 [预设ID: {preset_id}]")
        session = Session()
        try:
            try:
                from src.models.preset import Preset
                # 查询预设内容
                preset = session.query(Preset).get(preset_id)
                if not preset:
                    return jsonify({
                        'success': False,
                        'error': '未找到指定预设内容'
                    }), 404

                # 删除预设内容
                session.delete(preset)
                session.commit()

                return jsonify({
                    'success': True,
                    'message': '预设内容已删除'
                })
            except Exception as e:
                session.rollback()
                logger.error(f"删除预设内容时出错: {str(e)}", exc_info=True)
                return jsonify({
                    'success': False,
                    'error': '删除预设内容时出错'
                }), 500
        finally:
            session.close()
    except Exception as e:
        logger.error(f"删除预设内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取整本书的预设模板内容
@shared_api_bp.route('/api/novel/<int:novel_id>/book_template/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/book_template/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/book_template/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/book_template/<dimension>', methods=['GET'])
def get_book_template(novel_id, dimension):
    """
    获取整本书的预设模板内容
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取整本书的预设模板内容 [小说ID: {novel_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取分析结果
            analysis_result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not analysis_result:
                return jsonify({
                    'success': False,
                    'error': f'未找到维度 {dimension} 的分析结果'
                }), 404

            # 构建预设模板内容
            template_content = f"""# {novel.title} - {dimension} 预设模板

## 基本信息
- 小说: {novel.title}
- 作者: {novel.author or '未知'}
- 维度: {dimension}
- 适用字数范围：30-300万字
- 生成时间：{analysis_result.created_at.isoformat() if analysis_result.created_at else datetime.now().isoformat()}

## 设定内容
"""
            # 根据不同维度添加特定内容
            if dimension == "language_style":
                template_content += """### 语言风格设定
- 总体风格：[简洁明快/华丽典雅/质朴自然/锋利犀利]
- 语言氛围：[轻松幽默/庄重肃穆/神秘悬疑/浪漫温馨]
- 特色词汇：[专业领域词汇/方言俚语/古典词汇/现代流行语]
- 句式偏好：[长短句搭配比例/复合句使用频率/排比句运用]
"""
            elif dimension == "rhythm_pacing":
                template_content += """### 节奏节拍设定
- 总体节奏：[快速紧凑型/舒缓铺陈型/波动变化型/情节驱动型]
- 加速减速模式：[递进式/波浪式/起伏式/阶梯式]
- 章节内节奏变化：[开头-中段-结尾的节奏变化规律]
- 情节与节奏的匹配度：[快节奏场景/慢节奏场景的内容特点]
"""
            else:
                template_content += f"""### {dimension}设定
- 请在此处添加{dimension}的具体设定内容
- 可以包括具体的数值和比例
- 适用于30-300万字的长篇小说创作
"""

            template_content += """
## 应用指南
本设定模板适用于30-300万字的长篇小说创作，请根据您的具体需求和创作风格进行调整。在使用过程中，建议：

1. **灵活应用**：根据您的故事情节和风格需求，有选择地应用模板中的元素
2. **保持连贯性**：确保各个维度之间的设定保持一致，避免风格冲突
3. **创新发展**：在模板基础上进行创新，发展自己独特的创作风格

---
© """ + str(datetime.now().year) + """ 九猫写作系统 - 设定模板
"""

            return jsonify({
                'success': True,
                'template': template_content
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取整本书设定模板内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取章节的设定模板内容
@shared_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>', methods=['GET'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>', methods=['GET'])
def get_chapter_template(novel_id, chapter_id, dimension):
    """
    获取章节的设定模板内容
    支持所有版本的API路径
    """
    try:
        logger.info(f"获取章节的设定模板内容 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取小说和章节
            novel = session.query(Novel).get(novel_id)
            chapter = session.query(Chapter).get(chapter_id)

            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            if not chapter:
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 获取章节分析结果
            chapter_analysis = session.query(ChapterAnalysisResult).filter_by(
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            # 构建章节设定模板内容
            template_content = f"""# {novel.title} - {chapter.title or f'第{chapter.chapter_number}章'} - {dimension} 设定模板

## 章节基本信息
- 小说: {novel.title}
- 章节: {chapter.title or f'第{chapter.chapter_number}章'}
- 维度: {dimension}
- 适用字数范围: 5000-10000字/章

## 设定内容
"""
            # 根据不同维度添加特定内容
            if dimension == "language_style":
                template_content += """### 本章语言风格设定
- 主要语气：[正式/轻松/紧张/舒缓/幽默/严肃]
- 词汇选择：[专业术语比例/口语化程度/华丽词藻使用频率]
- 句式特点：[长短句比例/复杂句使用/特殊句式安排]
- 修辞偏好：[本章重点使用的修辞手法及效果]
"""
            elif dimension == "rhythm_pacing":
                template_content += """### 本章节奏节拍设定
- 开场节奏：[缓慢铺垫/中速展开/快速进入]
- 中段变化：[渐进加速/平稳过渡/起伏波动]
- 高潮设计：[位置/持续长度/强度]
- 结尾节奏：[戛然而止/缓慢收束/余韵悠长]
"""
            else:
                template_content += f"""### 本章{dimension}设定
- 请在此处添加本章{dimension}的具体设定内容
- 可以包括具体的数值和比例
- 适用于本章节的创作
"""

            template_content += """
## 章节内容结构建议
### 开篇部分
- 开场方式：[直接开场/环境描写开场/对话开场/悬念开场]
- 情境设置：[时间地点人物活动的明确交代]

### 中间发展部分
- 情节推进：[新情节的引入节奏/线索展开方式]
- 冲突设置：[冲突类型/展现手法/强度控制]

### 结尾部分
- 章节收束：[圆满式/悬念式/暗示式/开放式]
- 后续铺垫：[对下一章的自然引导]

---
© """ + str(datetime.now().year) + """ 九猫写作系统 - 章节设定模板
"""

            return jsonify({
                'success': True,
                'novel_id': novel_id,
                'chapter_id': chapter_id,
                'dimension': dimension,
                'template': template_content,
                'chapter': {
                    'id': chapter.id,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'chapter_number': chapter.chapter_number
                }
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节设定模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f"获取章节设定模板时出错: {str(e)}"
        }), 500

# 修复整本书的chapter_outline分析推理过程
@shared_api_bp.route('/api/fix_chapter_outline_reasoning', methods=['POST'])
@shared_api_bp.route('/v3.0/api/fix_chapter_outline_reasoning', methods=['POST'])
@shared_api_bp.route('/v3.1/api/fix_chapter_outline_reasoning', methods=['POST'])
@shared_api_bp.route('/v3.5/api/fix_chapter_outline_reasoning', methods=['POST'])
def fix_chapter_outline_reasoning():
    """
    修复整本书的chapter_outline分析推理过程
    支持所有版本的API路径
    """
    try:
        logger.info("开始修复整本书的chapter_outline分析推理过程")

        # 导入修复脚本
        from fix_book_chapter_outline_reasoning import fix_book_chapter_outline_reasoning

        # 执行修复
        fix_book_chapter_outline_reasoning()

        return jsonify({
            'success': True,
            'message': '成功修复整本书的chapter_outline分析推理过程'
        })
    except Exception as e:
        logger.error(f"修复整本书的chapter_outline分析推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 将参考蓝本的分析结果转换为预设模板
@shared_api_bp.route('/api/convert_to_template', methods=['POST'])
@shared_api_bp.route('/v3.0/api/convert_to_template', methods=['POST'])
@shared_api_bp.route('/v3.1/api/convert_to_template', methods=['POST'])
@shared_api_bp.route('/v3.5/api/convert_to_template', methods=['POST'])
def convert_to_template():
    """
    将参考蓝本的分析结果转换为预设模板
    支持所有版本的API路径
    """
    try:
        data = request.get_json()
        if not data:
            logger.error("转换预设模板请求体为空")
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        template_id = data.get('template_id')
        if not template_id:
            logger.error("转换预设模板缺少参考蓝本ID")
            return jsonify({
                'success': False,
                'error': '参考蓝本ID不能为空'
            }), 400

        # 获取知识库数据（可选）
        _ = data.get('knowledge_base_data')  # 不使用，但保留参数
        logger.info(f"开始转换参考蓝本 ID {template_id} 为预设模板")

        # 创建一个简单的预设模板
        session = Session()
        try:
            # 获取参考蓝本信息
            template = session.query(Novel).get(template_id)
            if not template:
                logger.error(f"未找到参考蓝本 ID {template_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 创建预设模板
            from src.models.preset import Preset
            preset = Preset(
                title=f"预设模板-{template.title}",
                content=f"# {template.title} 预设模板\n\n## 基本信息\n- 参考蓝本ID: {template_id}\n- 参考蓝本: {template.title}\n- 作者: {template.author or '未知'}\n- 字数: {template.word_count or 0}\n- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n## 预设模板内容\n这是基于参考蓝本《{template.title}》生成的预设模板。",
                category="preset_template",
                meta_info={
                    'template_id': template_id,
                    'template_title': template.title,
                    'created_at': datetime.now().isoformat()
                }
            )

            session.add(preset)
            session.commit()

            # 构建响应数据
            result = {
                'success': True,
                'message': '成功转换为预设模板',
                'preset_id': preset.id,
                'template': {
                    'id': template.id,
                    'title': template.title
                },
                'book_templates': {},
                'chapter_templates': {}
            }

            logger.info(f"成功创建预设模板 ID {preset.id}")
            return jsonify(result), 200
        except Exception as e:
            session.rollback()
            logger.error(f"创建预设模板时出错: {str(e)}", exc_info=True)
            raise
        finally:
            session.close()
    except Exception as e:
        logger.error(f"转换参考蓝本为预设模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 分析小说的指定维度
@shared_api_bp.route('/api/novel/<int:novel_id>/analyze_dimension', methods=['POST'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/analyze_dimension', methods=['POST'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/analyze_dimension', methods=['POST'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/analyze_dimension', methods=['POST'])
def analyze_novel_dimension(novel_id):
    """
    创建小说维度分析任务
    支持所有版本的API路径

    请求体:
    {
        "dimension": 维度名称,
        "force": 是否强制重新分析（可选）
    }

    响应:
    {
        "success": 是否成功,
        "analysis_id": 分析任务ID,
        "message": 成功消息
    }
    """
    try:
        data = request.json or {}
        dimension = data.get('dimension')
        if not dimension:
            return jsonify({
                'success': False,
                'error': '维度名称不能为空'
            }), 400

        force = data.get('force', 0)

        logger.info(f"开始分析小说维度 [小说ID: {novel_id}, 维度: {dimension}, 强制: {force}]")

        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 [小说ID: {novel_id}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 导入单维度分析模块
            from src.api.analyze_dimension import analyze_dimension

            # 启动分析
            analyze_dimension(novel, dimension, use_real_api=True)

            # 生成分析ID
            analysis_id = f"{novel_id}_{dimension}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            logger.info(f"成功创建分析任务 [小说ID: {novel_id}, 维度: {dimension}, 分析ID: {analysis_id}]")

            return jsonify({
                'success': True,
                'analysis_id': analysis_id,
                'message': f'成功创建分析任务，正在分析维度 {dimension}'
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"创建分析任务时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 分析章节的指定维度
@shared_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze_dimension', methods=['POST'])
@shared_api_bp.route('/v3.0/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze_dimension', methods=['POST'])
@shared_api_bp.route('/v3.1/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze_dimension', methods=['POST'])
@shared_api_bp.route('/v3.5/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze_dimension', methods=['POST'])
def analyze_chapter_dimension(novel_id, chapter_id):
    """
    分析章节特定维度
    支持所有版本的API路径

    请求体:
    {
        "dimension": 维度名称,
        "force": 是否强制重新分析（可选）
    }

    响应:
    {
        "success": 是否成功,
        "analysis_id": 分析任务ID,
        "message": 成功消息
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '请求体不能为空'}), 400

        dimension = data.get('dimension')
        if not dimension:
            return jsonify({'success': False, 'error': '维度名称不能为空'}), 400

        force = data.get('force', 0)

        logger.info(f"开始分析章节维度 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}, 强制: {force}]")

        session = Session()
        try:
            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter or chapter.novel_id != int(novel_id):
                logger.error(f"未找到章节 [小说ID: {novel_id}, 章节ID: {chapter_id}]")
                return jsonify({'success': False, 'error': '未找到指定章节'}), 404

            # 导入章节分析服务
            from src.services.chapter_analysis_service import ChapterAnalysisService

            # 启动分析
            result = ChapterAnalysisService.analyze_chapter(
                chapter_id=chapter_id,
                dimension=dimension,
                force=bool(force)
            )

            if not result['success']:
                logger.error(f"分析章节维度失败 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]: {result['error']}")
                return jsonify(result), 500

            # 生成分析ID
            analysis_id = f"{novel_id}_{chapter_id}_{dimension}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            logger.info(f"成功创建章节分析任务 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}, 分析ID: {analysis_id}]")

            return jsonify({
                'success': True,
                'analysis_id': analysis_id,
                'message': f'成功创建章节分析任务，正在分析维度 {dimension}'
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"创建章节分析任务时出错: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

# 获取分析任务状态
@shared_api_bp.route('/api/analysis/<analysis_id>/status', methods=['GET'])
@shared_api_bp.route('/v3.0/api/analysis/<analysis_id>/status', methods=['GET'])
@shared_api_bp.route('/v3.1/api/analysis/<analysis_id>/status', methods=['GET'])
@shared_api_bp.route('/v3.5/api/analysis/<analysis_id>/status', methods=['GET'])
def get_analysis_status(analysis_id):
    """
    获取分析任务状态
    支持所有版本的API路径

    响应:
    {
        "success": 是否成功,
        "status": 任务状态,
        "progress": 进度百分比,
        "result_url": 结果URL（仅当状态为completed时）,
        "error": 错误信息（仅当状态为failed时）,
        "log_entries": 日志条目列表
    }
    """
    try:
        # 解析分析ID
        parts = analysis_id.split('_')
        if len(parts) < 3:
            logger.error(f"无效的分析ID: {analysis_id}")
            return jsonify({
                'success': False,
                'error': '无效的分析ID'
            }), 400

        # 提取小说ID和维度
        novel_id = parts[0]
        dimension = parts[1]

        logger.info(f"获取分析任务状态 [分析ID: {analysis_id}, 小说ID: {novel_id}, 维度: {dimension}]")

        # 尝试将novel_id转换为整数
        try:
            novel_id_int = int(novel_id)
        except ValueError:
            novel_id_int = novel_id

        # 获取小说和分析结果
        session = Session()
        try:
            # 检查分析结果是否存在
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id_int,
                dimension=dimension
            ).first()

            # 检查分析是否完成
            if result:
                # 检查分析结果是否有内容，如果有内容则认为分析已完成
                if result.content and len(result.content.strip()) > 10:
                    status = 'completed'
                    progress = 100
                    result_url = f"/api/novel/{novel_id}/analysis/{dimension}"
                    log_entries = [
                        {'level': 'info', 'message': '分析已完成', 'timestamp': datetime.now().isoformat()},
                        {'level': 'success', 'message': '分析结果已保存', 'timestamp': datetime.now().isoformat()}
                    ]

                    # 记录日志
                    logger.info(f"分析已完成: 小说ID={novel_id}, 维度={dimension}")

                    return jsonify({
                        'success': True,
                        'status': status,
                        'progress': progress,
                        'result_url': result_url,
                        'log_entries': log_entries
                    })
                else:
                    # 有分析结果记录但内容为空，可能是分析过程中创建的占位记录
                    status = 'analyzing'
                    progress = 90  # 提高进度显示，表示接近完成
                    result_url = None
                    log_entries = [
                        {'level': 'info', 'message': '分析即将完成...', 'timestamp': datetime.now().isoformat()}
                    ]

                    # 记录日志
                    logger.info(f"分析即将完成: 小说ID={novel_id}, 维度={dimension}, 进度=90%")
            else:
                # 检查是否正在分析中
                # 这里可以添加更复杂的逻辑来检查分析是否正在进行
                # 例如检查分析进度表或临时文件
                status = 'analyzing'
                progress = 75  # 提高进度显示，避免一直显示50%
                result_url = None
                log_entries = [
                    {'level': 'info', 'message': '分析正在进行中...', 'timestamp': datetime.now().isoformat()}
                ]

                # 记录日志
                logger.info(f"分析进行中: 小说ID={novel_id}, 维度={dimension}, 进度=75%")

                return jsonify({
                    'success': True,
                    'status': status,
                    'progress': progress,
                    'result_url': result_url,
                    'log_entries': log_entries
                })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取分析任务状态时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500