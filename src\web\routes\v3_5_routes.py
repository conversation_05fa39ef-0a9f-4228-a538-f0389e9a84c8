"""
九猫小说分析写作系统v3.5 - 路由
"""
# 确保所有v3.5版本的路由都在这里定义
import logging
from datetime import datetime
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from sqlalchemy import func

from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.preset import Preset
from src.services.chapter_analysis_service import ChapterAnalysisService
from src.services.analysis_service import aggregate_chapter_analyses
import config

logger = logging.getLogger(__name__)

v3_5_bp = Blueprint('v3_5', __name__, url_prefix='/v3.5')

def get_reference_templates(session):
    """获取参考蓝本列表"""
    try:
        templates = session.query(Preset).filter(
            Preset.type == 'reference_template'
        ).order_by(Preset.created_at.desc()).all()
        return templates
    except Exception as e:
        logger.error(f"获取参考蓝本列表时出错: {str(e)}", exc_info=True)
        return []

def get_generated_content_stats(session):
    """获取生成内容统计"""
    try:
        # 获取预设内容数量
        preset_count = session.query(func.count(Preset.id)).filter(
            Preset.type != 'reference_template'
        ).scalar() or 0

        # 获取小说数量
        novel_count = session.query(func.count(Novel.id)).scalar() or 0

        # 获取分析结果数量
        analysis_count = session.query(func.count(AnalysisResult.id)).scalar() or 0

        # 获取章节分析结果数量
        chapter_analysis_count = session.query(func.count(ChapterAnalysisResult.id)).scalar() or 0

        return {
            'preset_count': preset_count,
            'novel_count': novel_count,
            'analysis_count': analysis_count,
            'chapter_analysis_count': chapter_analysis_count,
            'total_count': preset_count + novel_count + analysis_count + chapter_analysis_count
        }
    except Exception as e:
        logger.error(f"获取生成内容统计时出错: {str(e)}", exc_info=True)
        return {
            'preset_count': 0,
            'novel_count': 0,
            'analysis_count': 0,
            'chapter_analysis_count': 0,
            'total_count': 0
        }

@v3_5_bp.route('/')
def index():
    """首页"""
    try:
        session = Session()
        try:
            # 获取最近的小说
            novels = session.query(Novel).order_by(Novel.created_at.desc()).limit(5).all()

            # 获取参考蓝本列表
            reference_templates = get_reference_templates(session)

            # 获取生成内容统计
            generated_content_stats = get_generated_content_stats(session)

            return render_template('v3.5/index.html',
                                  novels=novels,
                                  reference_templates=reference_templates,
                                  generated_content_stats=generated_content_stats)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载首页时出错: {str(e)}", exc_info=True)
        return render_template('v3.5/index.html', novels=[], reference_templates=[], generated_content_stats={})

@v3_5_bp.route('/console')
def console():
    """控制台页面"""
    try:
        logger.info("访问v3.5控制台页面")
        session = Session()
        try:
            # 获取所有参考蓝本
            templates = get_reference_templates(session)
            logger.info(f"获取到 {len(templates)} 个参考蓝本")

            # 获取当前时间，用于日志显示
            now = datetime.now()

            # 渲染模板
            response = render_template('v3.5/console.html', templates=templates, now=now)
            logger.info("成功渲染v3.5控制台页面模板")
            return response
        finally:
            session.close()
    except Exception as e:
        logger.error(f"访问v3.5控制台页面时出错: {str(e)}", exc_info=True)
        return render_template('v3.5/error.html', error_code=500, error_message=f"加载控制台页面时出错: {str(e)}")

@v3_5_bp.route('/text_comparison')
def text_comparison():
    """文本对比页面"""
    try:
        logger.info("访问文本对比页面")
        session = Session()
        try:
            # 获取所有参考蓝本
            templates = get_reference_templates(session)
            logger.info(f"获取到 {len(templates)} 个参考蓝本")

            # 获取当前时间，用于日志显示
            now = datetime.now()

            # 渲染模板
            response = render_template('v3.5/text_comparison.html', templates=templates, now=now)
            logger.info("成功渲染文本对比页面模板")
            return response
        finally:
            session.close()
    except Exception as e:
        logger.error(f"访问文本对比页面时出错: {str(e)}", exc_info=True)
        return render_template('v3.5/error.html', error_code=500, error_message=f"加载文本对比页面时出错: {str(e)}")

@v3_5_bp.route('/system_monitor')
def system_monitor():
    """系统监控页面"""
    try:
        logger.info("访问系统监控页面")
        return render_template('v3.5/system_monitor.html')
    except Exception as e:
        logger.error(f"访问系统监控页面时出错: {str(e)}", exc_info=True)
        return render_template('v3.5/error.html', error_code=500, error_message=f"加载系统监控页面时出错: {str(e)}")

@v3_5_bp.route('/showcase')
def showcase():
    """展示台页面"""
    try:
        logger.info("访问展示台页面")
        return render_template('v3.5/showcase.html')
    except Exception as e:
        logger.error(f"访问展示台页面时出错: {str(e)}", exc_info=True)
        return render_template('v3.5/error.html', error_code=500, error_message=f"加载展示台页面时出错: {str(e)}")

@v3_5_bp.route('/help')
def help_page():
    """帮助中心页面"""
    try:
        logger.info("访问帮助中心页面")
        return render_template('v3.5/help.html')
    except Exception as e:
        logger.error(f"访问帮助中心页面时出错: {str(e)}", exc_info=True)
        return render_template('v3.5/error.html', error_code=500, error_message=f"加载帮助中心页面时出错: {str(e)}")

@v3_5_bp.route('/reference_templates')
def reference_templates():
    """参考蓝本页面"""
    try:
        logger.info("访问参考蓝本页面")
        session = Session()
        try:
            # 获取所有参考蓝本
            templates = session.query(Novel).filter(
                Novel.novel_metadata.contains({'is_template': True})
            ).order_by(Novel.created_at.desc()).all()

            return render_template('v3.5/reference_templates.html', templates=templates)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"访问参考蓝本页面时出错: {str(e)}", exc_info=True)
        return render_template('v3.5/error.html', error_code=500, error_message=f"加载参考蓝本页面时出错: {str(e)}")

@v3_5_bp.route('/content_repository')
def content_repository_page():
    """内容仓库页面"""
    try:
        logger.info("访问内容仓库页面")
        return render_template('v3.5/content_repository.html')
    except Exception as e:
        logger.error(f"访问内容仓库页面时出错: {str(e)}", exc_info=True)
        return render_template('v3.5/error.html', error_code=500, error_message=f"加载内容仓库页面时出错: {str(e)}")

@v3_5_bp.route('/preset/<int:preset_id>/templates')
def preset_template_detail(preset_id):
    """预设模板详情页面"""
    try:
        logger.info(f"访问预设模板详情页面，预设ID: {preset_id}")
        session = Session()
        try:
            # 获取预设模板
            preset = session.query(Preset).get(preset_id)
            if not preset:
                logger.warning(f"未找到预设模板，ID: {preset_id}")
                return render_template('v3.5/error.html', error_code=404, error_message="未找到预设模板")

            # 渲染模板
            return render_template('v3.5/preset_template_detail.html', preset=preset)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"访问预设模板详情页面时出错: {str(e)}", exc_info=True)
        return render_template('v3.5/error.html', error_code=500, error_message=f"加载预设模板详情页面时出错: {str(e)}")
