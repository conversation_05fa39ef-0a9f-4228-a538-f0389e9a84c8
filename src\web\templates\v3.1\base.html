<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{% block title %}九猫小说分析写作系统v3.1{% endblock %}</title>

    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js" as="script">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" as="script">

    <!-- 样式表 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/v3_style.css') }}?v=1.0.0">

    <!-- 错误处理脚本 -->
    <script>
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('捕获到错误:', message, '来源:', source, '行:', lineno);
            return true; // 阻止错误冒泡
        };
    </script>

    <!-- 添加资源加载修复脚本 -->
    <script src="{{ url_for('static', filename='js/resource-loader-fix.js') }}"></script>

    <!-- 添加预设模板修复脚本 -->
    <script src="{{ url_for('static', filename='js/preset-template-fix.js') }}"></script>

    <!-- 添加API路径修复脚本 -->
    <script src="{{ url_for('static', filename='js/v3.1-api-path-fix.js') }}"></script>

    <!-- 添加API路径统一修复脚本 -->
    <script src="{{ url_for('static', filename='js/api-path-unifier.js') }}"></script>

    <!-- 添加分析结果显示修复脚本 -->
    <script src="{{ url_for('static', filename='js/analysis-result-display-fix.js') }}"></script>

    <!-- 添加分析进度修复脚本 -->
    <script src="{{ url_for('static', filename='js/analysis-progress-fix-v2.js') }}"></script>

    <!-- 添加分析结果修复脚本v2 -->
    <script src="{{ url_for('static', filename='js/analysis-result-fix.js') }}"></script>

    <!-- 添加直接数据库路径修复脚本 -->
    <script src="{{ url_for('static', filename='js/direct-db-path-fix.js') }}"></script>

    {% block extra_css %}{% endblock %}
    {% block head %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('v3_1.index') }}">
                <i class="fas fa-cat me-2"></i>九猫小说分析写作系统v3.1
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3_1.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3_1.novels') }}">
                            <i class="fas fa-book me-1"></i>小说列表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3_1.upload_novel') }}">
                            <i class="fas fa-upload me-1"></i>上传小说
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3_1.reference_templates') }}">
                            <i class="fas fa-bookmark me-1"></i>参考蓝本
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3_1.console') }}">
                            <i class="fas fa-terminal me-1"></i>控制台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3_1.showcase') }}">
                            <i class="fas fa-chart-bar me-1"></i>展示台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3_1.content_repository_page') }}">
                            <i class="fas fa-archive me-1"></i>内容仓库
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="{{ url_for('v3_1.help_page') }}" class="btn btn-outline-light me-2">
                        <i class="fas fa-question-circle me-1"></i>帮助
                    </a>
                    <a href="{{ url_for('v3_1.system_monitor') }}" class="btn btn-outline-light">
                        <i class="fas fa-tachometer-alt me-1"></i>系统监控
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container my-4">
        <!-- 闪现消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <footer class="footer mt-auto py-3">
        <div class="container text-center">
            <span>© 2025 九猫小说分析写作系统v3.1 | <a href="{{ url_for('v3_1.help_page') }}" class="text-decoration-none" style="color: var(--primary-color);">帮助中心</a></span>
        </div>
    </footer>

    <!-- 基础JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <!-- jQuery加载器备用脚本 -->
    <script>
        // 确保jQuery正确加载
        if (typeof jQuery === 'undefined') {
            console.warn('jQuery未正确加载，尝试备用CDN');
            document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
        }
    </script>

    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/v3.1_main.js') }}?v=1.0.0"></script>

    <!-- 页面特定JS -->
    {% block scripts %}{% endblock %}

    <!-- 直接分析结果修复脚本 -->
    <script src="{{ url_for('static', filename='js/direct-analysis-fix.js') }}"></script>
</body>
</html>
