/**
 * 九猫 - 章节分析路由修复脚本
 * 用于修复小说页面上的章节分析按钮跳转问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[九猫路由修复] 脚本已加载');

    // 修复章节分析按钮点击事件
    function fixChapterAnalysisLinks() {
        // 查找页面上所有章节分析按钮
        const chapterAnalysisButtons = document.querySelectorAll('a.btn[href*="/chapter"], a.btn[href*="chapters"]');

        // 如果找不到，查找可能的替代匹配项
        if (chapterAnalysisButtons.length === 0) {
            console.log('[九猫路由修复] 未找到标准章节分析按钮，尝试查找替代项');

            // 查找包含"章节分析"文本的按钮
            const textButtons = document.querySelectorAll('a.btn, button.btn');
            textButtons.forEach(btn => {
                if (btn.textContent.includes('章节分析') || btn.textContent.includes('章节') && btn.textContent.includes('分析')) {
                    console.log('[九猫路由修复] 找到章节分析按钮：', btn.textContent);

                    // 获取当前小说ID
                    const novelId = getCurrentNovelId();
                    if (novelId) {
                        // 设置正确的跳转链接
                        btn.onclick = function(e) {
                            e.preventDefault();
                            console.log(`[九猫路由修复] 点击章节分析按钮，跳转到: /novel/${novelId}/chapters`);
                            window.location.href = `/novel/${novelId}/chapters`;
                            return false;
                        };

                        // 如果是链接，也设置href属性
                        if (btn.tagName === 'A') {
                            btn.href = `/novel/${novelId}/chapters`;
                        }

                        console.log(`[九猫路由修复] 已修复章节分析按钮，设置链接为: /novel/${novelId}/chapters`);
                    }
                }
            });
        } else {
            console.log(`[九猫路由修复] 找到${chapterAnalysisButtons.length}个章节分析按钮`);

            // 获取当前小说ID
            const novelId = getCurrentNovelId();
            if (!novelId) return;

            // 修复每个按钮
            chapterAnalysisButtons.forEach(btn => {
                const originalHref = btn.getAttribute('href');
                console.log(`[九猫路由修复] 原始链接: ${originalHref}`);

                // 设置正确的跳转链接
                btn.href = `/novel/${novelId}/chapters`;

                // 添加点击事件处理
                btn.onclick = function(e) {
                    e.preventDefault();
                    console.log(`[九猫路由修复] 点击章节分析按钮，跳转到: /novel/${novelId}/chapters`);
                    window.location.href = `/novel/${novelId}/chapters`;
                    return false;
                };

                console.log(`[九猫路由修复] 修复后链接: ${btn.href}`);
            });
        }

        // 特别处理小说ID为41的情况
        if (window.location.pathname.includes('/novel/41')) {
            console.log('[九猫路由修复] 检测到小说ID为41，应用特殊修复');
            fixNovel41ChapterLinks();
        }
    }

    // 特别修复小说ID为41的章节链接
    function fixNovel41ChapterLinks() {
        // 查找所有可能的章节分析按钮
        const allButtons = document.querySelectorAll('a.btn, button.btn');

        allButtons.forEach(btn => {
            if (btn.textContent.includes('章节分析') || btn.textContent.includes('章节') && btn.textContent.includes('分析')) {
                console.log('[九猫路由修复] 找到小说41的章节分析按钮：', btn.textContent);

                // 强制设置正确的跳转链接
                btn.onclick = function(e) {
                    e.preventDefault();
                    console.log('[九猫路由修复] 点击小说41的章节分析按钮，跳转到: /novel/41/chapters');
                    window.location.href = '/novel/41/chapters';
                    return false;
                };

                // 如果是链接，也设置href属性
                if (btn.tagName === 'A') {
                    btn.href = '/novel/41/chapters';
                }

                console.log('[九猫路由修复] 已修复小说41的章节分析按钮');
            }
        });
    }

    // 从URL、数据属性或页面内容获取当前小说ID
    function getCurrentNovelId() {
        // 尝试从URL获取
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        if (match && match[1]) {
            console.log(`[九猫路由修复] 从URL获取到小说ID: ${match[1]}`);
            return match[1];
        }

        // 尝试从data属性获取
        const novelContainer = document.querySelector('[data-novel-id]');
        if (novelContainer && novelContainer.dataset.novelId) {
            console.log(`[九猫路由修复] 从data属性获取到小说ID: ${novelContainer.dataset.novelId}`);
            return novelContainer.dataset.novelId;
        }

        // 尝试从表单获取
        const form = document.querySelector('form[action*="/novel/"]');
        if (form) {
            const formMatch = form.action.match(/\/novel\/(\d+)/);
            if (formMatch && formMatch[1]) {
                console.log(`[九猫路由修复] 从表单获取到小说ID: ${formMatch[1]}`);
                return formMatch[1];
            }
        }

        // 尝试从breadcrumb获取
        const breadcrumbLinks = document.querySelectorAll('.breadcrumb a[href*="/novel/"]');
        if (breadcrumbLinks.length > 0) {
            const lastLink = breadcrumbLinks[breadcrumbLinks.length - 1];
            const breadcrumbMatch = lastLink.href.match(/\/novel\/(\d+)/);
            if (breadcrumbMatch && breadcrumbMatch[1]) {
                console.log(`[九猫路由修复] 从面包屑导航获取到小说ID: ${breadcrumbMatch[1]}`);
                return breadcrumbMatch[1];
            }
        }

        console.warn('[九猫路由修复] 无法获取小说ID');
        return null;
    }

    // 初始化函数
    function initialize() {
        console.log('[九猫路由修复] 初始化中...');

        // 检查是否在小说详情页
        if (window.location.pathname.match(/\/novel\/\d+\/?$/)) {
            console.log('[九猫路由修复] 检测到小说详情页，应用修复');

            // 修复章节分析链接
            fixChapterAnalysisLinks();

            // 定期检查，应对动态加载的情况
            setInterval(fixChapterAnalysisLinks, 2000);
        } else if (window.location.pathname.includes('/novel/41')) {
            // 特别处理小说ID为41的情况
            console.log('[九猫路由修复] 检测到小说ID为41，应用特殊修复');
            fixNovel41ChapterLinks();

            // 定期检查，应对动态加载的情况
            setInterval(fixNovel41ChapterLinks, 2000);
        } else {
            console.log('[九猫路由修复] 不是小说详情页，无需修复');
        }

        // 添加全局点击事件委托，处理动态添加的按钮
        document.addEventListener('click', function(e) {
            // 检查是否点击了章节分析按钮
            let target = e.target;

            // 向上查找最近的按钮或链接
            while (target && target !== document) {
                if ((target.tagName === 'A' || target.tagName === 'BUTTON') &&
                    (target.textContent.includes('章节分析') ||
                     (target.textContent.includes('章节') && target.textContent.includes('分析')))) {

                    // 获取当前小说ID
                    const novelId = getCurrentNovelId();
                    if (novelId) {
                        e.preventDefault();
                        console.log(`[九猫路由修复] 通过委托处理点击章节分析按钮，跳转到: /novel/${novelId}/chapters`);
                        window.location.href = `/novel/${novelId}/chapters`;
                    }
                    break;
                }
                target = target.parentNode;
            }
        });

        console.log('[九猫路由修复] 初始化完成');
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出到全局命名空间，方便调试
    window.chapterAnalysisRouteFix = {
        fix: fixChapterAnalysisLinks,
        getNovelId: getCurrentNovelId
    };
})();