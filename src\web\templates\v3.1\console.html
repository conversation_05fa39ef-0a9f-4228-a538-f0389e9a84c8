{% extends "v3.1/base.html" %}

{% block title %}控制台 - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/knowledge-base-compact.css') }}">
<style>
    /* 控制台基本样式 */
    .console-container {
        height: calc(100vh - 250px);
        min-height: 600px;
    }

    /* 控制台导航栏 */
    .console-nav {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .console-nav .nav-link {
        color: #495057;
        padding: 1rem;
        border-radius: 0;
        border-bottom: 3px solid transparent;
    }

    .console-nav .nav-link.active {
        color: #4a6bff;
        background-color: transparent;
        border-bottom: 3px solid #4a6bff;
    }

    /* 控制台内容区域 */
    .console-content {
        padding: 1.5rem;
        height: 100%;
        overflow-y: auto;
    }

    /* 运行日志样式 */
    .log-container {
        background-color: #1e1e1e;
        color: #f0f0f0;
        font-family: 'Consolas', 'Courier New', monospace;
        padding: 1rem;
        border-radius: 0.25rem;
        height: 500px;
        overflow-y: auto;
    }

    .log-line {
        margin-bottom: 0.25rem;
        line-height: 1.5;
    }

    .log-timestamp {
        color: #6c757d;
        margin-right: 0.5rem;
    }

    .log-info {
        color: #17a2b8;
    }

    .log-warn {
        color: #ffc107;
    }

    .log-error {
        color: #dc3545;
    }

    .log-welcome {
        color: #28a745;
    }

    /* 分析展示样式 */
    .analysis-container {
        height: 100%;
    }

    .template-card {
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .template-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* 知识库样式 */
    .knowledge-base-container {
        height: 100%;
    }

    /* 自动写作样式 */
    .auto-writing-container {
        height: 100%;
    }

    /* 通用样式 */
    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .card-header-tabs .nav-link {
        padding: 0.75rem 1rem;
    }

    .markdown-body {
        padding: 1rem;
        background-color: #fff;
        border-radius: 0.25rem;
        border: 1px solid #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 控制台导航栏 -->
    <ul class="nav nav-tabs console-nav mb-4" id="consoleTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="true">
                <i class="fas fa-terminal me-2"></i>运行日志
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab" aria-controls="analysis" aria-selected="false">
                <i class="fas fa-chart-bar me-2"></i>分析展示
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="knowledge-tab" data-bs-toggle="tab" data-bs-target="#knowledge" type="button" role="tab" aria-controls="knowledge" aria-selected="false">
                <i class="fas fa-book me-2"></i>知识库
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="writing-tab" data-bs-toggle="tab" data-bs-target="#writing" type="button" role="tab" aria-controls="writing" aria-selected="false">
                <i class="fas fa-pen-fancy me-2"></i>自动写作
            </button>
        </li>
    </ul>

    <!-- 控制台内容区域 -->
    <div class="tab-content console-container" id="consoleTabContent">
        <!-- 运行日志 -->
        <div class="tab-pane fade show active" id="logs" role="tabpanel" aria-labelledby="logs-tab">
            <div class="console-content">
                <h2 class="section-title"><i class="fas fa-terminal me-2"></i>运行日志</h2>

                <div class="card mb-4">
                    <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">系统日志</h5>
                        <div>
                            <button id="clearLogsBtn" class="btn btn-sm btn-outline-light me-2">
                                <i class="fas fa-trash-alt me-1"></i>清除
                            </button>
                            <button id="refreshLogsBtn" class="btn btn-sm btn-outline-light">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="log-container" id="logContent">
                            <div class="log-line log-welcome">
                                <span class="log-timestamp">[{{ now.strftime('%H:%M:%S') }}]</span> 欢迎使用九猫系统v3.1运行日志。此日志显示系统运行过程中的重要信息。
                            </div>
                            <div class="log-line log-info">
                                <span class="log-timestamp">[{{ now.strftime('%H:%M:%S') }}]</span> 系统已启动，准备就绪。
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="input-group">
                            <input type="text" id="logSearch" class="form-control" placeholder="搜索日志...">
                            <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">系统监控</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h5 class="card-title">API调用统计</h5>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>总调用次数:</span>
                                            <span class="badge bg-primary" id="apiCallCount">0</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <span>估计成本:</span>
                                            <span class="badge bg-info" id="apiCostEstimate">¥0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">系统状态</h5>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>运行时间:</span>
                                            <span class="badge bg-success" id="systemUptime">00:00:00</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <span>当前任务:</span>
                                            <span class="badge bg-secondary" id="currentTask">无</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析展示 -->
        <div class="tab-pane fade" id="analysis" role="tabpanel" aria-labelledby="analysis-tab">
            <div class="console-content">
                <h2 class="section-title"><i class="fas fa-chart-bar me-2"></i>分析展示</h2>

                <!-- 参考蓝本选择 -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">选择参考蓝本</h5>
                    </div>
                    <div class="card-body">
                        {% if templates %}
                            <div class="row">
                                {% for template in templates %}
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 template-card" data-template-id="{{ template.id }}">
                                            <div class="card-body">
                                                <h5 class="card-title">{{ template.title }}</h5>
                                                <p class="card-text text-muted">
                                                    {% if template.author %}作者: {{ template.author }}<br>{% endif %}
                                                    字数: {{ template.word_count }}<br>
                                                    章节数: {{ template.chapter_count }}
                                                </p>
                                                <div class="text-center mt-3">
                                                    <button class="btn btn-sm btn-outline-primary select-template-btn" data-template-id="{{ template.id }}">
                                                        <i class="fas fa-check me-1"></i>选择
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                未找到参考蓝本，请先在小说列表中设置参考蓝本。
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- 分析内容展示 -->
                <div class="card" id="analysisDisplayCard" style="display: none;">
                    <div class="card-header bg-light">
                        <ul class="nav nav-tabs card-header-tabs" id="analysisTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="book-tab" data-bs-toggle="tab" data-bs-target="#book" type="button" role="tab" aria-controls="book" aria-selected="true">
                                    整本书
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">
                                    章节列表
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="analysisTabContent">
                            <!-- 整本书分析 -->
                            <div class="tab-pane fade show active" id="book" role="tabpanel" aria-labelledby="book-tab">
                                <div class="row">
                                    <div class="col-md-3">
                                        <h5 class="mb-3">整本书的分析维度</h5>
                                        <div class="list-group" id="dimensionList">
                                            <!-- 维度列表将通过JavaScript动态加载 -->
                                            <div class="text-center py-3">
                                                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                                <p class="small mt-2">加载中...</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <ul class="nav nav-tabs card-header-tabs" id="analysisResultTab" role="tablist">
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link active" id="result-tab" data-bs-toggle="tab" data-bs-target="#result" type="button" role="tab" aria-controls="result" aria-selected="true">
                                                            分析结果
                                                        </button>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link" id="reasoning-tab" data-bs-toggle="tab" data-bs-target="#reasoning" type="button" role="tab" aria-controls="reasoning" aria-selected="false">
                                                            推理过程
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="card-body">
                                                <div class="tab-content" id="analysisResultTabContent">
                                                    <div class="tab-pane fade show active" id="result" role="tabpanel" aria-labelledby="result-tab">
                                                        <div id="analysisContent" class="markdown-body">
                                                            请从左侧选择一个分析维度，查看对应的分析结果。
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade" id="reasoning" role="tabpanel" aria-labelledby="reasoning-tab">
                                                        <div id="reasoningContent" class="markdown-body">
                                                            选择分析维度后，这里将显示详细的推理过程。
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 章节列表 -->
                            <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                                <div class="row">
                                    <div class="col-md-3">
                                        <h5 class="mb-3">章节列表</h5>
                                        <div class="list-group" id="chapterList">
                                            <!-- 章节列表将通过JavaScript动态加载 -->
                                            <div class="text-center py-3">
                                                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                                <p class="small mt-2">加载中...</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <ul class="nav nav-tabs card-header-tabs" id="chapterAnalysisResultTab" role="tablist">
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link active" id="chapter-result-tab" data-bs-toggle="tab" data-bs-target="#chapter-result" type="button" role="tab" aria-controls="chapter-result" aria-selected="true">
                                                            分析结果
                                                        </button>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link" id="chapter-reasoning-tab" data-bs-toggle="tab" data-bs-target="#chapter-reasoning" type="button" role="tab" aria-controls="chapter-reasoning" aria-selected="false">
                                                            推理过程
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="card-body">
                                                <div class="tab-content" id="chapterAnalysisResultTabContent">
                                                    <div class="tab-pane fade show active" id="chapter-result" role="tabpanel" aria-labelledby="chapter-result-tab">
                                                        <div id="chapterAnalysisContent" class="markdown-body">
                                                            请从左侧选择一个章节，查看对应的分析结果。
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade" id="chapter-reasoning" role="tabpanel" aria-labelledby="chapter-reasoning-tab">
                                                        <div id="chapterReasoningContent" class="markdown-body">
                                                            选择章节后，这里将显示详细的推理过程。
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识库 -->
        <div class="tab-pane fade" id="knowledge" role="tabpanel" aria-labelledby="knowledge-tab">
            <div class="console-content">
                <h2 class="section-title"><i class="fas fa-book me-2"></i>知识库</h2>

                <!-- 知识库内容 -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">参考蓝本知识库</h5>
                        <div>
                            <button id="readAnalysisBtn" class="btn btn-sm btn-light me-2">
                                <i class="fas fa-sync-alt me-1"></i>读取分析结果
                            </button>
                            <button id="convertToTemplateBtn" class="btn btn-sm btn-light">
                                <i class="fas fa-magic me-1"></i>转化为预设模板
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="knowledgeBaseContent">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                请先点击"读取分析结果"按钮，从分析展示中读取参考蓝本的分析结果。
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 预设模板 -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">预设模板</h5>
                    </div>
                    <div class="card-body">
                        <div id="presetTemplateContent">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                请先点击"转化为预设模板"按钮，将知识库内容转化为预设模板。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自动写作 -->
        <div class="tab-pane fade" id="writing" role="tabpanel" aria-labelledby="writing-tab">
            <div class="console-content">
                <h2 class="section-title"><i class="fas fa-pen-fancy me-2"></i>自动写作</h2>

                <!-- 写作控制台 -->
                <div class="card mb-4">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">写作控制台</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="templateSelect" class="form-label">选择预设模板</label>
                            <select class="form-select" id="templateSelect">
                                <option value="">-- 请选择预设模板 --</option>
                                <!-- 预设模板列表将通过JavaScript动态加载 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="writingPrompt" class="form-label">写作提示</label>
                            <textarea class="form-control" id="writingPrompt" rows="3" placeholder="请输入写作提示..."></textarea>
                        </div>
                        <div class="d-grid">
                            <button id="startWritingBtn" class="btn btn-primary">
                                <i class="fas fa-play me-1"></i>开始写作
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 写作结果 -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">写作结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="writingResult" class="markdown-body">
                            写作结果将显示在这里。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入v3.1控制台JavaScript -->
<script src="{{ url_for('static', filename='js/v3.1/console.js') }}"></script>
<!-- 引入知识库紧凑显示脚本 -->
<script src="{{ url_for('static', filename='js/knowledge-base-compact.js') }}"></script>
{% endblock %}
