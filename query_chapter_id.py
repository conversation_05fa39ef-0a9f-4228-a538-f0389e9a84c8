"""
查询章节ID
"""

import os
import sys
import sqlite3

# 数据库路径
db_path = os.path.join(os.path.dirname(__file__), 'novel_analysis.db')

def query_chapters():
    """查询所有章节"""
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 查询所有章节
        cursor.execute("""
            SELECT c.id, c.novel_id, c.chapter_number, c.title, n.title as novel_title
            FROM chapters c
            JOIN novels n ON c.novel_id = n.id
            ORDER BY c.novel_id, c.chapter_number
        """)
        
        chapters = cursor.fetchall()
        print(f"找到 {len(chapters)} 个章节")
        
        # 显示章节信息
        for chapter in chapters:
            chapter_id, novel_id, chapter_number, chapter_title, novel_title = chapter
            chapter_title = chapter_title or f'第{chapter_number}章'
            print(f"章节ID: {chapter_id}, 小说: {novel_title}, 章节: {chapter_title}")
        
        # 查询章纲分析结果
        cursor.execute("""
            SELECT car.id, car.chapter_id, car.dimension, car.novel_id,
                   c.title, c.chapter_number, n.title as novel_title
            FROM chapter_analysis_results car
            JOIN chapters c ON car.chapter_id = c.id
            JOIN novels n ON car.novel_id = n.id
            WHERE car.dimension = 'chapter_outline'
            ORDER BY car.novel_id, c.chapter_number
        """)
        
        results = cursor.fetchall()
        print(f"\n找到 {len(results)} 个章纲分析结果")
        
        # 显示章纲分析结果信息
        for result in results:
            result_id, chapter_id, dimension, novel_id, chapter_title, chapter_number, novel_title = result
            chapter_title = chapter_title or f'第{chapter_number}章'
            print(f"结果ID: {result_id}, 章节ID: {chapter_id}, 小说: {novel_title}, 章节: {chapter_title}")
    finally:
        conn.close()

if __name__ == "__main__":
    query_chapters()
