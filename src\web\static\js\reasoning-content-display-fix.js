/**
 * 九猫系统 - 推理内容显示修复
 * 
 * 该脚本直接从API获取完整的推理过程并显示，
 * 绕过现有的推理内容加载器中的内容验证和过滤逻辑
 */
(function() {
    console.log('[九猫修复] 推理内容显示修复已启动');

    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {}, settings: {} };

    // 如果已经运行过，不再重复执行
    if (window.__nineCatsFixes.loaded['reasoning-content-display-fix']) {
        console.log('[九猫修复] 推理内容显示修复已经运行过，跳过');
        return;
    }

    // 立即标记为已加载，防止其他处理器执行
    window.__nineCatsFixes.loaded['reasoning-content-display-fix'] = true;
    
    // 禁用其他推理内容处理器
    window.__nineCatsFixes.loaded['reasoning-content-loader-enhanced'] = true;
    window.__nineCatsFixes.loaded['unified-reasoning-content-fix'] = true;
    window.__nineCatsFixes.loaded['reasoning-content-fix'] = true;
    window.__nineCatsFixes.loaded['reasoning-content-fix-v2'] = true;
    window.__nineCatsFixes.loaded['reasoning-content-extractor'] = true;
    window.__nineCatsFixes.loaded['reasoning-content-emergency-fix'] = true;
    
    console.log('[九猫修复] 已禁用其他推理内容处理器，确保本修复方案优先执行');

    // 辅助函数：HTML转义
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    // 主函数：初始化所有推理内容容器
    function initAllReasoningContainers() {
        // 页面加载完成后执行
        window.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                // 查找所有推理内容容器
                const containers = document.querySelectorAll('[data-reasoning-container]');
    
                if (containers.length > 0) {
                    console.info(`[九猫修复] 找到 ${containers.length} 个推理内容容器`);
    
                    // 初始化每个容器
                    containers.forEach(container => {
                        const novelId = container.getAttribute('data-novel-id');
                        const dimension = container.getAttribute('data-dimension');
    
                        if (novelId && dimension) {
                            // 直接从API获取推理内容
                            fetchAndDisplayReasoningContent(novelId, dimension, container);
                        } else {
                            console.warn(`[九猫修复] 容器缺少必要属性: novel-id=${novelId}, dimension=${dimension}`);
                        }
                    });
                } else {
                    console.info('[九猫修复] 未找到推理内容容器，将在页面完全加载后再次检查');
                    window.addEventListener('load', checkContainersAgain);
                }
            }, 500); // 延迟500毫秒确保DOM已加载
        });
    }

    // 再次检查容器（页面完全加载后）
    function checkContainersAgain() {
        const containers = document.querySelectorAll('[data-reasoning-container]');
        
        if (containers.length > 0) {
            console.info(`[九猫修复] 页面加载后找到 ${containers.length} 个推理内容容器`);

            // 初始化每个容器
            containers.forEach(container => {
                const novelId = container.getAttribute('data-novel-id');
                const dimension = container.getAttribute('data-dimension');

                if (novelId && dimension) {
                    // 直接从API获取推理内容
                    fetchAndDisplayReasoningContent(novelId, dimension, container);
                }
            });
        } else {
            console.warn('[九猫修复] 未找到任何推理内容容器');
            // 尝试使用更通用的选择器
            const reasoningContents = document.querySelectorAll('.reasoning-content, #reasoningContent');
            if (reasoningContents.length > 0) {
                console.info(`[九猫修复] 找到 ${reasoningContents.length} 个通用推理内容容器`);
                reasoningContents.forEach(content => {
                    // 尝试从页面URL或内容获取novelId和dimension
                    try {
                        const path = window.location.pathname.split('/');
                        let novelId = null;
                        let dimension = null;
                        
                        // 从URL中提取novelId
                        for (let i = 0; i < path.length; i++) {
                            if (path[i] === 'novel' && i+1 < path.length) {
                                novelId = path[i+1];
                                break;
                            }
                        }
                        
                        // 尝试从页面内容中获取dimension
                        const dimensionElement = document.querySelector('[data-dimension]');
                        if (dimensionElement) {
                            dimension = dimensionElement.getAttribute('data-dimension');
                        }
                        
                        // 如果找到了novelId和dimension，获取推理内容
                        if (novelId && dimension) {
                            console.info(`[九猫修复] 从URL和页面提取到: novelId=${novelId}, dimension=${dimension}`);
                            // 创建一个推理内容容器
                            const container = document.createElement('div');
                            container.setAttribute('data-reasoning-container', 'true');
                            container.setAttribute('data-novel-id', novelId);
                            container.setAttribute('data-dimension', dimension);
                            
                            // 替换现有内容
                            content.innerHTML = '';
                            content.appendChild(container);
                            
                            // 获取推理内容
                            fetchAndDisplayReasoningContent(novelId, dimension, container);
                        }
                    } catch (error) {
                        console.error(`[九猫修复] 尝试处理通用容器时出错: ${error.message}`);
                    }
                });
            }
        }
    }

    // 从API获取并显示推理内容
    function fetchAndDisplayReasoningContent(novelId, dimension, container) {
        // 验证输入
        if (!novelId || !dimension || !container) {
            console.error('[九猫修复] 缺少必要参数: novelId, dimension 或 container');
            
            // 发布加载失败事件
            const errorEvent = new CustomEvent('reasoning-content-loaded', {
                detail: { success: false, error: '缺少必要参数' }
            });
            document.dispatchEvent(errorEvent);
            
            return;
        }
        
        // 显示加载中状态
        container.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载推理过程，请稍候...</p>
            </div>
        `;

        // 构建API URL - 优先使用专用API
        const apiUrl = `/api/novel/${novelId}/analysis/${dimension}/reasoning_content`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    // 如果专用API失败，尝试从常规分析API获取
                    return fetch(`/api/novel/${novelId}/analysis/${dimension}`);
                }
                return response;
            })
            .then(response => response.json())
            .then(data => {
                console.log('[九猫修复] API响应:', data);
                
                // 尝试从多个可能的位置获取推理内容
                let reasoningContent = null;
                
                // 1. 专用API响应中的reasoning_content字段
                if (data.reasoning_content) {
                    reasoningContent = data.reasoning_content;
                    console.log('[九猫修复] 从API响应的reasoning_content字段获取到推理内容');
                }
                // 2. 常规API响应中的metadata.reasoning_content字段
                else if (data.result && data.result.metadata && data.result.metadata.reasoning_content) {
                    reasoningContent = data.result.metadata.reasoning_content;
                    console.log('[九猫修复] 从API响应的result.metadata.reasoning_content字段获取到推理内容');
                }
                // 3. 常规API响应中的result.reasoning_content字段
                else if (data.result && data.result.reasoning_content) {
                    reasoningContent = data.result.reasoning_content;
                    console.log('[九猫修复] 从API响应的result.reasoning_content字段获取到推理内容');
                }
                // 4. metadata中的reasoning_content字段
                else if (data.metadata && data.metadata.reasoning_content) {
                    reasoningContent = data.metadata.reasoning_content;
                    console.log('[九猫修复] 从API响应的metadata.reasoning_content字段获取到推理内容');
                }
                // 5. analysis_metadata中的reasoning_content字段
                else if (data.result && data.result.analysis_metadata && data.result.analysis_metadata.reasoning_content) {
                    reasoningContent = data.result.analysis_metadata.reasoning_content;
                    console.log('[九猫修复] 从API响应的result.analysis_metadata.reasoning_content字段获取到推理内容');
                }
                
                if (reasoningContent) {
                    // 显示推理内容，不进行任何内容验证
                    container.innerHTML = `
                        <div class="alert alert-success mb-3">
                            <p><i class="fas fa-check-circle"></i> <strong>成功：</strong> 成功加载完整推理过程内容。</p>
                            <p class="small">内容长度: ${reasoningContent.length} 字符</p>
                        </div>
                        <pre class="reasoning-text">${escapeHtml(reasoningContent)}</pre>
                    `;
                    console.info(`[九猫修复] 成功显示推理内容 (长度: ${reasoningContent.length}字符)`);
                    
                    // 发布加载成功事件
                    const successEvent = new CustomEvent('reasoning-content-loaded', {
                        detail: { success: true, length: reasoningContent.length }
                    });
                    document.dispatchEvent(successEvent);
                } else {
                    // 显示错误信息
                    container.innerHTML = `
                        <div class="alert alert-warning">
                            <p><i class="fas fa-exclamation-circle"></i> 未找到推理过程内容</p>
                            <p class="small">可能的原因：</p>
                            <ul class="small">
                                <li>该分析尚未生成推理过程</li>
                                <li>API响应格式不符合预期</li>
                                <li>服务器端出现错误</li>
                            </ul>
                        </div>
                    `;
                    console.warn('[九猫修复] 未找到推理内容');
                    
                    // 发布加载失败事件
                    const errorEvent = new CustomEvent('reasoning-content-loaded', {
                        detail: { success: false, error: '未找到推理内容' }
                    });
                    document.dispatchEvent(errorEvent);
                }
            })
            .catch(error => {
                // 显示错误信息
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <p><i class="fas fa-times-circle"></i> 加载推理内容时出错</p>
                        <p class="small">错误信息: ${error.message}</p>
                    </div>
                `;
                console.error(`[九猫修复] 加载推理内容时出错: ${error.message}`);
                
                // 发布加载失败事件
                const errorEvent = new CustomEvent('reasoning-content-loaded', {
                    detail: { success: false, error: error.message }
                });
                document.dispatchEvent(errorEvent);
            });
    }

    // 添加MutationObserver监听DOM变化，动态处理新添加的推理内容容器
    function setupMutationObserver() {
        const observer = new MutationObserver(mutations => {
            let needsCheck = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 检查是否有新添加的节点包含推理内容容器
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) { // Element节点
                            if (node.matches('[data-reasoning-container]')) {
                                needsCheck = true;
                            } else if (node.querySelector('[data-reasoning-container]')) {
                                needsCheck = true;
                            }
                        }
                    });
                }
            });
            
            if (needsCheck) {
                console.log('[九猫修复] 检测到DOM变化，重新检查推理内容容器');
                checkContainersAgain();
            }
        });
        
        // 监听整个文档的变化
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('[九猫修复] 已设置DOM变化监听器');
    }
    
    // 添加事件监听器，支持手动触发重新加载
    function setupEventListeners() {
        document.addEventListener('reasoning-content-reload', function(e) {
            console.log('[九猫修复] 接收到推理内容重新加载事件', e.detail);
            
            if (e.detail && e.detail.novelId && e.detail.dimension) {
                const novelId = e.detail.novelId;
                const dimension = e.detail.dimension;
                
                // 查找对应的容器
                const container = document.querySelector(`[data-reasoning-container][data-novel-id="${novelId}"][data-dimension="${dimension}"]`);
                
                if (container) {
                    console.log(`[九猫修复] 找到匹配的容器，开始加载内容`);
                    fetchAndDisplayReasoningContent(novelId, dimension, container);
                } else {
                    console.warn(`[九猫修复] 未找到匹配的容器`);
                    
                    // 查找可能的通用容器
                    const genericContainers = document.querySelectorAll('[data-reasoning-container]');
                    if (genericContainers.length > 0) {
                        // 使用第一个容器
                        const genericContainer = genericContainers[0];
                        console.log(`[九猫修复] 使用通用容器 ${genericContainer.id || '未命名'}`);
                        
                        // 更新容器属性
                        genericContainer.setAttribute('data-novel-id', novelId);
                        genericContainer.setAttribute('data-dimension', dimension);
                        
                        // 获取推理内容
                        fetchAndDisplayReasoningContent(novelId, dimension, genericContainer);
                    } else {
                        console.error(`[九猫修复] 未找到任何可用容器来显示推理内容`);
                        
                        // 发布加载失败事件
                        const errorEvent = new CustomEvent('reasoning-content-loaded', {
                            detail: { success: false, error: '未找到可用容器' }
                        });
                        document.dispatchEvent(errorEvent);
                    }
                }
            } else {
                console.error('[九猫修复] 推理内容重新加载事件缺少必要参数');
            }
        });
        
        console.log('[九猫修复] 已设置推理内容重新加载事件监听器');
    }

    // 启动修复
    initAllReasoningContainers();
    setupMutationObserver();
    setupEventListeners();

    console.log('[九猫修复] 推理内容显示修复初始化完成');
})(); 