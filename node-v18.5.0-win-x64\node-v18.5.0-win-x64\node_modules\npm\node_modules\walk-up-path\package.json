{"name": "walk-up-path", "version": "1.0.0", "files": ["index.js"], "description": "Given a path string, return a generator that walks up the path, emitting each dirname.", "repository": {"type": "git", "url": "git+https://github.com/isaacs/walk-up-path"}, "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"tap": "^14.10.7", "require-inject": "^1.4.4"}}