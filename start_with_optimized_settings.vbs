Option Explicit

' 九猫小说分析系统优化启动脚本
' 使用更大的线程池和内存限制启动应用

' 创建Shell对象
Dim WshShell
Set WshShell = CreateObject("WScript.Shell")

' 设置环境变量
WshShell.Environment("PROCESS")("PYTHONUNBUFFERED") = "1"
WshShell.Environment("PROCESS")("FLASK_ENV") = "production"
WshShell.Environment("PROCESS")("FLASK_DEBUG") = "0"
WshShell.Environment("PROCESS")("PARALLEL_ANALYSIS") = "1"
WshShell.Environment("PROCESS")("THREAD_POOL_SIZE") = "8"
WshShell.Environment("PROCESS")("MAX_WORKERS") = "8"
WshShell.Environment("PROCESS")("DB_POOL_SIZE") = "30"
WshShell.Environment("PROCESS")("DB_MAX_OVERFLOW") = "30"

' 显示启动信息
WScript.Echo "正在启动九猫小说分析系统（优化版）..."
WScript.Echo "使用更大的线程池和内存限制"
WScript.Echo "线程池大小: 8"
WScript.Echo "数据库连接池大小: 30"
WScript.Echo "数据库最大溢出连接: 30"
WScript.Echo "并行分析: 启用"
WScript.Echo ""
WScript.Echo "系统启动中，请稍候..."

' 启动应用
Dim Command
Command = "python run.py"
WshShell.Run Command, 1, False

' 等待3秒
WScript.Sleep 3000

' 打开浏览器
WshShell.Run "http://localhost:5001/", 1, False

' 显示完成信息
WScript.Echo "九猫小说分析系统已启动！"
WScript.Echo "请访问: http://localhost:5001/"
WScript.Echo ""
WScript.Echo "按任意键关闭此窗口..."

' 等待用户按键
WScript.StdIn.ReadLine
