@echo off
chcp 65001 > nul
echo 九猫系统 - JavaScript错误修复工具
echo ==============================
echo.

REM 检查目录结构
if not exist "src\web\static\js" (
    echo 创建目录: src\web\static\js
    mkdir "src\web\static\js" 2>nul
)

REM 复制修复脚本
echo 正在复制修复脚本...
copy /Y "unified-error-handler.js" "src\web\static\js\" >nul 2>&1
copy /Y "console-logger-fix.js" "src\web\static\js\" >nul 2>&1
copy /Y "global-error-handler-fix.js" "src\web\static\js\" >nul 2>&1

REM 检查是否成功
if exist "src\web\static\js\unified-error-handler.js" (
    echo - unified-error-handler.js 已复制
) else (
    echo - 警告: unified-error-handler.js 复制失败
)

if exist "src\web\static\js\console-logger-fix.js" (
    echo - console-logger-fix.js 已复制
) else (
    echo - 警告: console-logger-fix.js 复制失败
)

if exist "src\web\static\js\global-error-handler-fix.js" (
    echo - global-error-handler-fix.js 已复制
) else (
    echo - 警告: global-error-handler-fix.js 复制失败
)

echo.
echo 正在检查HTML模板文件...

REM 查找模板文件
set "templates_found=0"
set "templates_dir=src\web\templates"

if exist "%templates_dir%" (
    echo 找到模板目录: %templates_dir%
    
    REM 检查base.html
    if exist "%templates_dir%\base.html" (
        echo 找到模板文件: %templates_dir%\base.html
        set "templates_found=1"
        
        REM 检查是否已包含修复脚本
        findstr /C:"unified-error-handler.js" "%templates_dir%\base.html" >nul
        if errorlevel 1 (
            echo 正在修改 base.html 添加修复脚本引用...
            
            REM 创建临时文件
            type "%templates_dir%\base.html" > "%templates_dir%\base.html.tmp"
            
            REM 添加修复脚本引用
            echo ^<!-- 错误处理修复脚本 --^> > "%templates_dir%\base.html"
            echo ^<script src="/static/js/unified-error-handler.js"^>^</script^> >> "%templates_dir%\base.html"
            echo ^<script src="/static/js/console-logger-fix.js"^>^</script^> >> "%templates_dir%\base.html"
            echo ^<script src="/static/js/global-error-handler-fix.js"^>^</script^> >> "%templates_dir%\base.html"
            echo. >> "%templates_dir%\base.html"
            
            REM 添加原始内容
            type "%templates_dir%\base.html.tmp" >> "%templates_dir%\base.html"
            
            REM 删除临时文件
            del "%templates_dir%\base.html.tmp" >nul 2>&1
            
            echo - base.html 已修改
        ) else (
            echo - base.html 已包含修复脚本引用，无需修改
        )
    )
    
    REM 检查layout.html
    if exist "%templates_dir%\layout.html" (
        echo 找到模板文件: %templates_dir%\layout.html
        set "templates_found=1"
        
        REM 检查是否已包含修复脚本
        findstr /C:"unified-error-handler.js" "%templates_dir%\layout.html" >nul
        if errorlevel 1 (
            echo 正在修改 layout.html 添加修复脚本引用...
            
            REM 创建临时文件
            type "%templates_dir%\layout.html" > "%templates_dir%\layout.html.tmp"
            
            REM 添加修复脚本引用
            echo ^<!-- 错误处理修复脚本 --^> > "%templates_dir%\layout.html"
            echo ^<script src="/static/js/unified-error-handler.js"^>^</script^> >> "%templates_dir%\layout.html"
            echo ^<script src="/static/js/console-logger-fix.js"^>^</script^> >> "%templates_dir%\layout.html"
            echo ^<script src="/static/js/global-error-handler-fix.js"^>^</script^> >> "%templates_dir%\layout.html"
            echo. >> "%templates_dir%\layout.html"
            
            REM 添加原始内容
            type "%templates_dir%\layout.html.tmp" >> "%templates_dir%\layout.html"
            
            REM 删除临时文件
            del "%templates_dir%\layout.html.tmp" >nul 2>&1
            
            echo - layout.html 已修改
        ) else (
            echo - layout.html 已包含修复脚本引用，无需修改
        )
    )
) else (
    echo 未找到模板目录: %templates_dir%
)

REM 如果没有找到模板文件，提示手动修改
if "%templates_found%"=="0" (
    echo.
    echo 警告: 未找到模板文件，请手动修改HTML模板文件，添加以下代码:
    echo.
    echo ^<!-- 错误处理修复脚本 --^>
    echo ^<script src="/static/js/unified-error-handler.js"^>^</script^>
    echo ^<script src="/static/js/console-logger-fix.js"^>^</script^>
    echo ^<script src="/static/js/global-error-handler-fix.js"^>^</script^>
)

echo.
echo 修复完成！请重启九猫系统并刷新浏览器页面。
echo.
pause
