# 分析v3_routes.py文件的语法错误
file_path = 'src/web/routes/v3_routes.py'

# 读取文件内容
with open(file_path, 'r', encoding='utf-8') as f:
    lines = f.readlines()

# 显示关键行内容
start_line = 500
end_line = 530
print(f"显示第{start_line}到{end_line}行内容:")
print("-" * 80)

for i in range(start_line-1, min(end_line, len(lines))):
    line_num = i + 1
    line = lines[i].rstrip()
    print(f"{line_num}: {line}")

print("-" * 80)

# 尝试直接修复问题
# 根据错误信息，问题在第522行，应该是一个缩进问题
fixed_content = []
fixed = False

for i, line in enumerate(lines):
    if i == 521 and "except Exception as e:" in line:  # 第522行
        # 这行的缩进可能是错误的
        indent_level = len(line) - len(line.lstrip())
        if indent_level > 16:  # 如果缩进超过16个空格
            fixed_line = " " * 16 + line.lstrip()  # 使用16个空格的缩进
            fixed_content.append(fixed_line)
            fixed = True
            print(f"修复第{i+1}行: 将 '{line.rstrip()}' 改为 '{fixed_line.rstrip()}'")
        else:
            fixed_content.append(line)
    else:
        fixed_content.append(line)

if fixed:
    # 创建新的备份
    with open(file_path + '.analyzed_backup', 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    # 写入修复后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(fixed_content)
    print(f"已写入修复后的内容到 {file_path}")
else:
    print("未能自动修复问题") 