{"name": "lru-cache", "description": "A cache object that deletes the least-recently-used items.", "version": "7.9.0", "author": "<PERSON> <<EMAIL>>", "keywords": ["mru", "lru", "cache"], "scripts": {"build": "", "test": "tap", "snap": "tap", "size": "size-limit", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "main": "index.js", "repository": "git://github.com/isaacs/node-lru-cache.git", "devDependencies": {"@size-limit/preset-small-lib": "^7.0.8", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "tap": "^15.1.6"}, "license": "ISC", "files": ["index.js"], "engines": {"node": ">=12"}, "tap": {"coverage-map": "map.js", "node-arg": ["--expose-gc"]}, "size-limit": [{"path": "./index.js"}]}