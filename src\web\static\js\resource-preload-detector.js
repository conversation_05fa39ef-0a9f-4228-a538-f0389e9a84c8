/**
 * 九猫系统 - 资源预加载检测器
 * 版本: 1.0.0
 * 
 * 该脚本在页面加载前检测网络连接状态，决定使用CDN还是本地资源
 * 采用非侵入式设计，不影响系统原有功能
 */

(function() {
    console.log('[九猫修复] 资源预加载检测器已启动');
    
    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {}, settings: {} };
    
    // 如果已经运行过，不再重复执行
    if (window.__nineCatsFixes.loaded['resource-preload-detector']) {
        console.log('[九猫修复] 资源预加载检测器已经运行过，跳过');
        return;
    }
    
    // 资源配置
    const resourceConfig = {
        // 关键资源列表
        criticalResources: [
            {
                type: 'css',
                cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
                local: '/static/css/bootstrap.min.css',
                id: 'bootstrap-css'
            },
            {
                type: 'js',
                cdn: 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js',
                local: '/static/js/lib/jquery.min.js',
                id: 'jquery-js'
            },
            {
                type: 'js',
                cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
                local: '/static/js/lib/bootstrap.bundle.min.js',
                id: 'bootstrap-js'
            },
            {
                type: 'css',
                cdn: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
                local: '/static/css/fontawesome.min.css',
                id: 'fontawesome-css'
            }
        ],
        
        // 检测超时时间（毫秒）
        timeout: 1500,
        
        // 默认使用本地资源的概率（0-100）
        // 设置为较高值可以减少CDN连接尝试，提高启动速度
        defaultLocalProbability: 80
    };
    
    // 存储检测结果
    const detectionResults = {
        networkStatus: 'unknown', // 'online', 'offline', 'slow', 'unknown'
        cdnAccessible: false,
        detectionComplete: false,
        resourceStatus: {}
    };
    
    // 检测网络连接状态
    function detectNetworkStatus() {
        // 检查navigator.onLine
        if (navigator.onLine === false) {
            detectionResults.networkStatus = 'offline';
            detectionResults.cdnAccessible = false;
            return Promise.resolve(false);
        }
        
        // 尝试加载一个小图片来测试连接
        return new Promise((resolve) => {
            const startTime = Date.now();
            const img = new Image();
            let timeout;
            
            // 设置超时
            timeout = setTimeout(() => {
                detectionResults.networkStatus = 'slow';
                detectionResults.cdnAccessible = false;
                resolve(false);
            }, resourceConfig.timeout);
            
            // 图片加载成功
            img.onload = function() {
                clearTimeout(timeout);
                const loadTime = Date.now() - startTime;
                
                if (loadTime < 500) {
                    detectionResults.networkStatus = 'online';
                    detectionResults.cdnAccessible = true;
                    resolve(true);
                } else {
                    detectionResults.networkStatus = 'slow';
                    detectionResults.cdnAccessible = false;
                    resolve(false);
                }
            };
            
            // 图片加载失败
            img.onerror = function() {
                clearTimeout(timeout);
                detectionResults.networkStatus = 'offline';
                detectionResults.cdnAccessible = false;
                resolve(false);
            };
            
            // 使用jsdelivr的favicon进行测试
            img.src = 'https://cdn.jsdelivr.net/favicon.ico?_=' + Date.now();
        });
    }
    
    // 决定使用CDN还是本地资源
    function decideResourceStrategy() {
        // 如果之前有存储的决策，使用之前的决策
        const storedStrategy = localStorage.getItem('nineCats_resourceStrategy');
        if (storedStrategy) {
            try {
                const strategy = JSON.parse(storedStrategy);
                // 如果存储的决策不超过1小时，使用它
                if (strategy.timestamp && (Date.now() - strategy.timestamp < 3600000)) {
                    console.log('[九猫修复] 使用存储的资源策略:', strategy.useCDN ? 'CDN' : '本地');
                    return Promise.resolve(strategy.useCDN);
                }
            } catch (e) {
                console.error('[九猫修复] 解析存储的资源策略失败:', e);
            }
        }
        
        // 随机决定是否使用本地资源（根据配置的概率）
        // 这可以减少CDN连接尝试，提高启动速度
        const randomDecision = Math.random() * 100 < resourceConfig.defaultLocalProbability;
        if (randomDecision) {
            console.log('[九猫修复] 随机决定使用本地资源');
            saveResourceStrategy(false);
            return Promise.resolve(false);
        }
        
        // 检测网络状态
        return detectNetworkStatus().then(cdnAccessible => {
            console.log('[九猫修复] 网络状态检测结果:', 
                        detectionResults.networkStatus, 
                        '可访问CDN:', cdnAccessible);
            
            // 保存决策
            saveResourceStrategy(cdnAccessible);
            
            return cdnAccessible;
        });
    }
    
    // 保存资源策略到localStorage
    function saveResourceStrategy(useCDN) {
        try {
            const strategy = {
                useCDN: useCDN,
                timestamp: Date.now(),
                networkStatus: detectionResults.networkStatus
            };
            localStorage.setItem('nineCats_resourceStrategy', JSON.stringify(strategy));
        } catch (e) {
            console.error('[九猫修复] 保存资源策略失败:', e);
        }
    }
    
    // 预加载关键资源
    function preloadCriticalResources(useCDN) {
        window.__nineCatsFixes.settings.useCDN = useCDN;
        console.log('[九猫修复] 预加载关键资源，使用', useCDN ? 'CDN' : '本地资源');
        
        // 移除现有的预加载链接
        document.querySelectorAll('link[rel="preload"]').forEach(link => {
            if (link.href && (link.href.includes('bootstrap') || 
                             link.href.includes('jquery') || 
                             link.href.includes('fontawesome'))) {
                link.remove();
            }
        });
        
        // 添加新的预加载链接
        resourceConfig.criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = useCDN ? resource.cdn : resource.local;
            link.as = resource.type === 'js' ? 'script' : 'style';
            link.id = 'preload-' + resource.id;
            link.fetchpriority = 'high';
            document.head.appendChild(link);
            
            // 记录资源状态
            detectionResults.resourceStatus[resource.id] = {
                useCDN: useCDN,
                url: useCDN ? resource.cdn : resource.local,
                loaded: false
            };
        });
        
        // 标记检测完成
        detectionResults.detectionComplete = true;
    }
    
    // 执行检测和预加载
    decideResourceStrategy().then(useCDN => {
        preloadCriticalResources(useCDN);
        
        // 将决策保存到全局对象，供其他脚本使用
        window.__nineCatsFixes.resourceStrategy = {
            useCDN: useCDN,
            networkStatus: detectionResults.networkStatus,
            timestamp: Date.now()
        };
        
        // 触发自定义事件，通知其他脚本
        const event = new CustomEvent('resourceStrategyDecided', {
            detail: window.__nineCatsFixes.resourceStrategy
        });
        document.dispatchEvent(event);
    });
    
    // 标记为已加载
    window.__nineCatsFixes.loaded['resource-preload-detector'] = true;
    
    console.log('[九猫修复] 资源预加载检测器初始化完成');
})();
