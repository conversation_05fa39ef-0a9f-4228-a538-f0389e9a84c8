# 九猫系统浏览器连接问题解决方案

如果您在访问九猫系统时遇到"无法访问此网站"或"ERR_CONNECTION_REFUSED"错误，请尝试以下解决方案：

## 基本解决方案

1. **确保服务器正在运行**
   - 运行`python main.py`启动服务器
   - 确认控制台输出中显示"Running on http://0.0.0.0:5001/"

2. **使用正确的URL**
   - 确保使用`http://localhost:5001`或`http://127.0.0.1:5001`
   - 不要使用`https`，因为系统不支持SSL

3. **清除浏览器缓存**
   - Chrome: 按Ctrl+Shift+Delete，选择"缓存的图片和文件"，点击"清除数据"
   - Edge: 按Ctrl+Shift+Delete，选择"缓存的图片和文件"，点击"立即清除"
   - Firefox: 按Ctrl+Shift+Delete，选择"缓存"，点击"立即清除"

4. **尝试不同的浏览器**
   - 如果Chrome不工作，尝试Edge、Firefox或其他浏览器

## 高级解决方案

1. **检查端口占用**
   - 运行`检查端口.bat`脚本查看端口5001是否被占用
   - 如果被占用，关闭占用该端口的进程

2. **完全重启系统**
   - 运行`完全重启九猫.bat`脚本，它将：
     - 关闭所有Chrome进程
     - 关闭所有Python进程
     - 清除DNS缓存
     - 重置网络连接
     - 重新启动服务器
     - 打开浏览器

3. **检查防火墙设置**
   - 确保Windows防火墙允许Python和端口5001的访问
   - 控制面板 -> 系统和安全 -> Windows Defender防火墙 -> 允许应用通过防火墙

4. **检查网络连接**
   - 运行`ipconfig /flushdns`清除DNS缓存
   - 运行`netsh winsock reset`重置网络连接

5. **检查hosts文件**
   - 编辑`C:\Windows\System32\drivers\etc\hosts`文件
   - 确保没有阻止localhost的条目

## 特定错误解决方案

### ERR_CONNECTION_REFUSED

这通常意味着服务器没有运行或端口被占用：
1. 确保服务器正在运行
2. 检查端口5001是否被占用
3. 尝试使用不同的端口（修改`main.py`中的端口号）

### ERR_NAME_NOT_RESOLVED

这通常是DNS问题：
1. 使用IP地址`127.0.0.1:5001`而不是`localhost:5001`
2. 清除DNS缓存：`ipconfig /flushdns`

### ERR_NETWORK_CHANGED

这通常是网络连接问题：
1. 检查网络连接
2. 重启路由器
3. 重置网络连接：`netsh winsock reset`

## 如果以上方法都不起作用

1. 重启计算机
2. 临时禁用防病毒软件和防火墙
3. 检查系统是否有最新的Windows更新
4. 检查Python和Flask是否正确安装

如果您仍然遇到问题，请联系系统管理员寻求帮助。
