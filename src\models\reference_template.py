"""
参考蓝本模型
"""
import json
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from src.models.base import Base

class ReferenceTemplate(Base):
    """参考蓝本模型"""
    __tablename__ = 'reference_templates'

    id = Column(Integer, primary_key=True)
    novel_id = Column(Integer, ForeignKey('novels.id'), nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联关系
    novel = relationship("Novel", back_populates="reference_templates")
    generated_contents = relationship("GeneratedContent", back_populates="reference_template")
    
    def __init__(self, novel_id, title, description=None):
        self.novel_id = novel_id
        self.title = title
        self.description = description
    
    def to_dict(self):
        """将对象转换为字典"""
        return {
            'id': self.id,
            'novel_id': self.novel_id,
            'title': self.title,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f"<ReferenceTemplate(id={self.id}, title='{self.title}', novel_id={self.novel_id})>"
