/**
 * 九猫 - 章纲分析图表专用修复脚本
 * 解决章纲分析页面的Chart.js相关问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('章纲分析图表专用修复脚本已加载');

    // 存储图表实例的全局对象
    window.chapterOutlineChartInstances = window.chapterOutlineChartInstances || {};

    // 彻底销毁图表实例
    function destroyChartCompletely(canvas) {
        try {
            console.log('彻底销毁图表实例');

            // 1. 尝试从全局chartInstances对象中销毁
            if (window.chartInstances) {
                const chartId = canvas.id || '';
                if (chartId && window.chartInstances[chartId]) {
                    console.log(`销毁全局chartInstances中的图表: ${chartId}`);
                    window.chartInstances[chartId].destroy();
                    delete window.chartInstances[chartId];
                }
            }

            // 2. 尝试从我们的专用对象中销毁
            if (window.chapterOutlineChartInstances) {
                const chartId = canvas.id || '';
                if (chartId && window.chapterOutlineChartInstances[chartId]) {
                    console.log(`销毁专用chapterOutlineChartInstances中的图表: ${chartId}`);
                    window.chapterOutlineChartInstances[chartId].destroy();
                    delete window.chapterOutlineChartInstances[chartId];
                }
            }

            // 3. 尝试使用Chart.js的getChart方法销毁
            if (typeof Chart !== 'undefined' && canvas) {
                try {
                    const existingChart = Chart.getChart(canvas);
                    if (existingChart) {
                        console.log('使用Chart.getChart销毁图表');
                        existingChart.destroy();
                    }
                } catch (e) {
                    console.log('使用Chart.getChart销毁图表时出错 (可以忽略):', e.message);
                }
            }

            // 4. 尝试销毁canvas上直接存储的图表实例
            if (canvas._chart) {
                console.log('销毁canvas上存储的图表实例');
                canvas._chart.destroy();
                delete canvas._chart;
            }

            // 5. 清除canvas内容
            const ctx = canvas.getContext('2d');
            if (ctx) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
        } catch (e) {
            console.log('销毁图表时出错 (可以忽略):', e.message);
        }
    }

    // 创建全新的canvas元素替换旧的
    function recreateCanvas(oldCanvas) {
        try {
            console.log('创建全新的canvas元素替换旧的');

            // 创建新的canvas元素
            const newCanvas = document.createElement('canvas');

            // 复制属性
            if (oldCanvas.id) newCanvas.id = oldCanvas.id;
            if (oldCanvas.className) newCanvas.className = oldCanvas.className;
            if (oldCanvas.getAttribute('data-dimension'))
                newCanvas.setAttribute('data-dimension', oldCanvas.getAttribute('data-dimension'));
            if (oldCanvas.getAttribute('data-chart-id'))
                newCanvas.setAttribute('data-chart-id', oldCanvas.getAttribute('data-chart-id'));

            // 设置尺寸
            newCanvas.width = oldCanvas.width || 400;
            newCanvas.height = oldCanvas.height || 300;

            // 安全替换canvas
            try {
                // 先销毁旧canvas上的图表
                destroyChartCompletely(oldCanvas);

                // 使用安全的替换方法
                if (oldCanvas.parentNode) {
                    try {
                        // 尝试使用replaceChild
                        oldCanvas.parentNode.replaceChild(newCanvas, oldCanvas);
                    } catch (replaceError) {
                        console.log('replaceChild失败，使用替代方法:', replaceError.message);

                        // 如果replaceChild失败，使用移除+添加的方式
                        oldCanvas.parentNode.removeChild(oldCanvas);
                        oldCanvas.parentNode.appendChild(newCanvas);
                    }
                }

                return newCanvas;
            } catch (e) {
                console.error('替换canvas时出错:', e);
                return oldCanvas; // 如果替换失败，返回原始canvas
            }
        } catch (e) {
            console.error('重新创建canvas时出错:', e);
            return oldCanvas; // 如果出错，返回原始canvas
        }
    }

    // 安全地创建章节大纲图表
    function safeCreateChapterOutlineChart(canvas) {
        try {
            console.log('安全创建章节大纲图表');

            // 确保Chart.js已加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载，无法创建图表');
                return null;
            }

            // 先销毁现有图表
            destroyChartCompletely(canvas);

            // 获取元数据
            let metadata = {};
            const metadataElement = document.getElementById('analysis-metadata');

            if (metadataElement) {
                try {
                    metadata = JSON.parse(metadataElement.getAttribute('data-metadata') || '{}');
                } catch (e) {
                    console.error('解析元数据时出错:', e);
                }
            }

            // 章节大纲特定的图表数据
            let labels = ['章节数量', '平均章节长度', '最长章节', '最短章节', '章节一致性', '结构完整性'];
            let data = [0, 0, 0, 0, 0, 0];

            // 如果元数据中有可视化数据，使用元数据中的数据
            if (metadata.visualization_data && metadata.visualization_data.radar) {
                const visualData = metadata.visualization_data.radar;
                if (visualData && Array.isArray(visualData.labels) && Array.isArray(visualData.data)) {
                    console.log('使用元数据中的可视化数据');
                    labels = visualData.labels;
                    data = visualData.data;
                }
            }

            // 创建图表配置
            const config = {
                type: 'radar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '章纲分析',
                        data: data,
                        backgroundColor: 'rgba(74, 107, 223, 0.2)',
                        borderColor: 'rgba(74, 107, 223, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            };

            try {
                // 获取上下文
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    throw new Error('无法获取canvas上下文');
                }

                // 创建新图表
                const chart = new Chart(ctx, config);

                // 存储图表实例
                if (canvas.id) {
                    window.chapterOutlineChartInstances[canvas.id] = chart;
                    // 同时存储到全局chartInstances中
                    if (window.chartInstances) {
                        window.chartInstances[canvas.id] = chart;
                    }
                }

                // 直接在canvas上存储图表实例
                canvas._chart = chart;

                console.log('章节大纲图表创建成功');
                return chart;
            } catch (e) {
                // 如果是Canvas已在使用的错误，尝试更激进的修复
                if (e.message && e.message.includes('Canvas is already in use')) {
                    console.log('检测到Canvas已在使用错误，尝试重新创建canvas');

                    // 重新创建canvas
                    const newCanvas = recreateCanvas(canvas);

                    // 在新canvas上创建图表
                    try {
                        const ctx = newCanvas.getContext('2d');
                        const chart = new Chart(ctx, config);

                        // 存储图表实例
                        if (newCanvas.id) {
                            window.chapterOutlineChartInstances[newCanvas.id] = chart;
                            // 同时存储到全局chartInstances中
                            if (window.chartInstances) {
                                window.chartInstances[newCanvas.id] = chart;
                            }
                        }

                        // 直接在canvas上存储图表实例
                        newCanvas._chart = chart;

                        console.log('在新canvas上创建图表成功');
                        return chart;
                    } catch (e2) {
                        console.error('在新canvas上创建图表时出错:', e2);
                        return null;
                    }
                } else {
                    console.error('创建图表时出错:', e);
                    return null;
                }
            }
        } catch (e) {
            console.error('创建图表的过程中出错:', e);
            return null;
        }
    }

    // 修复章节大纲页面的图表
    function fixChapterOutlineChart() {
        console.log('开始修复章节大纲页面的图表');

        // 查找章节大纲图表canvas
        const canvas = document.getElementById('chapter-outline-chart');

        if (canvas) {
            console.log('找到章节大纲图表canvas，开始修复');
            safeCreateChapterOutlineChart(canvas);
        } else {
            console.log('未找到章节大纲图表canvas，尝试查找图表容器');

            // 查找图表容器
            const containers = document.querySelectorAll('.chart-container, .visualization-container');

            if (containers.length > 0) {
                console.log(`找到 ${containers.length} 个图表容器`);

                containers.forEach(function(container, index) {
                    console.log(`处理图表容器 ${index + 1}`);

                    // 检查容器中是否已有canvas
                    let canvas = container.querySelector('canvas');

                    if (!canvas) {
                        console.log('容器中没有canvas，创建新的canvas');

                        // 创建canvas元素
                        canvas = document.createElement('canvas');
                        canvas.id = 'chapter-outline-chart';
                        canvas.width = 400;
                        canvas.height = 300;
                        canvas.className = 'analysis-chart';
                        canvas.setAttribute('data-dimension', 'chapter_outline');

                        // 添加到容器
                        container.appendChild(canvas);
                    } else {
                        console.log('容器中已有canvas，重新创建');
                        canvas = recreateCanvas(canvas);
                    }

                    // 创建图表
                    safeCreateChapterOutlineChart(canvas);
                });
            } else {
                console.log('未找到图表容器，无法修复图表');
            }
        }
    }

    // 修复replaceChild错误
    function fixReplaceChildError() {
        console.log('修复replaceChild错误');

        // 保存原始的replaceChild方法
        const originalReplaceChild = Node.prototype.replaceChild;

        // 重写replaceChild方法
        Node.prototype.replaceChild = function(newChild, oldChild) {
            try {
                // 尝试使用原始方法
                return originalReplaceChild.call(this, newChild, oldChild);
            } catch (e) {
                console.error('replaceChild错误:', e.message);

                try {
                    // 安全替换：先移除旧节点，再添加新节点
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }
                    this.appendChild(newChild);
                    return newChild;
                } catch (e2) {
                    console.error('替代方法也失败:', e2.message);

                    // 最后的尝试：直接返回newChild，不执行实际替换
                    return newChild;
                }
            }
        };
    }

    // 修复appendChild错误
    function fixAppendChildError() {
        console.log('修复appendChild错误');

        // 保存原始的appendChild方法
        const originalAppendChild = Node.prototype.appendChild;

        // 重写appendChild方法
        Node.prototype.appendChild = function(newChild) {
            try {
                // 尝试使用原始方法
                return originalAppendChild.call(this, newChild);
            } catch (e) {
                console.error('appendChild错误:', e.message);

                // 如果是canvas元素，尝试特殊处理
                if (newChild.nodeName === 'CANVAS') {
                    try {
                        console.log('尝试特殊处理canvas元素');

                        // 创建新的canvas元素
                        const newCanvas = document.createElement('canvas');

                        // 复制属性
                        if (newChild.id) newCanvas.id = newChild.id;
                        if (newChild.className) newCanvas.className = newChild.className;
                        if (newChild.getAttribute('data-dimension'))
                            newCanvas.setAttribute('data-dimension', newChild.getAttribute('data-dimension'));
                        if (newChild.getAttribute('data-chart-id'))
                            newCanvas.setAttribute('data-chart-id', newChild.getAttribute('data-chart-id'));

                        newCanvas.width = newChild.width || 400;
                        newCanvas.height = newChild.height || 300;

                        // 尝试添加新canvas
                        return originalAppendChild.call(this, newCanvas);
                    } catch (e2) {
                        console.error('特殊处理canvas元素失败:', e2.message);
                    }
                }

                // 如果特殊处理失败或不是canvas元素，返回原始元素
                return newChild;
            }
        };
    }

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始执行章节大纲图表专用修复');

        // 检查是否在章节大纲分析页面
        const isChapterOutlinePage = window.location.pathname.includes('chapter_outline');
        if (!isChapterOutlinePage) {
            console.log('不是章节大纲页面，跳过修复');
            return;
        }

        console.log('检测到chapter_outline页面，应用专用修复');

        // 修复replaceChild错误
        fixReplaceChildError();

        // 修复appendChild错误
        fixAppendChildError();

        // 如果Chart.js已加载，直接执行修复
        if (typeof Chart !== 'undefined') {
            console.log('Chart.js已加载，直接执行修复');
            setTimeout(fixChapterOutlineChart, 500);
        } else {
            console.log('Chart.js未加载，等待加载完成后执行修复');

            // 监听Chart.js加载
            const checkChartInterval = setInterval(function() {
                if (typeof Chart !== 'undefined') {
                    console.log('检测到Chart.js已加载，执行修复');
                    clearInterval(checkChartInterval);
                    setTimeout(fixChapterOutlineChart, 500);
                }
            }, 200);

            // 设置超时，避免无限等待
            setTimeout(function() {
                clearInterval(checkChartInterval);
            }, 10000);
        }

        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                // 处理Chart.js相关错误
                if (event.error.message.includes('Chart with ID') ||
                    event.error.message.includes('Canvas is already in use')) {
                    console.error('捕获到Chart.js相关错误:', event.error.message);

                    // 尝试修复
                    setTimeout(fixChapterOutlineChart, 100);

                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }

                // 处理DOM操作错误
                if (event.error.message.includes('Failed to execute \'replaceChild\' on \'Node\'') ||
                    event.error.message.includes('Failed to execute \'appendChild\' on \'Node\'')) {
                    console.error('捕获到DOM操作错误:', event.error.message);

                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);
    });

    // 导出公共函数
    window.chapterOutlineChartFix = {
        destroyChartCompletely: destroyChartCompletely,
        recreateCanvas: recreateCanvas,
        safeCreateChapterOutlineChart: safeCreateChapterOutlineChart,
        fixChapterOutlineChart: fixChapterOutlineChart
    };

    // 初始化章节大纲图表的主函数
    window.initChapterOutlineChart = function() {
        console.log('初始化章节大纲图表');

        // 获取canvas元素
        const canvas = document.getElementById('chapter-outline-chart');
        if (!canvas) {
            console.error('未找到章节大纲图表canvas元素');
            return;
        }

        // 确保Chart.js已加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js未加载，尝试动态加载');

            // 尝试动态加载Chart.js
            loadChartJS().then(() => {
                // Chart.js加载成功后创建图表
                safeCreateChapterOutlineChart(canvas);
            }).catch(error => {
                console.error('加载Chart.js失败:', error);
                const container = canvas.parentNode;
                if (container) {
                    container.innerHTML = '<div class="alert alert-warning">图表加载失败：无法加载Chart.js库</div>';
                }
            });
        } else {
            // Chart.js已加载，直接创建图表
            safeCreateChapterOutlineChart(canvas);
        }
    };

    // 动态加载Chart.js库
    function loadChartJS() {
        return new Promise((resolve, reject) => {
            // 检查Chart是否已经加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载');
                resolve();
                return;
            }

            // 尝试加载Chart.js
            const script = document.createElement('script');
            script.src = "/static/js/lib/chart.min.js"; // 优先使用本地路径

            script.onload = function() {
                console.log('成功加载Chart.js');
                resolve();
            };

            script.onerror = function() {
                // 如果本地加载失败，尝试备用路径
                console.warn('本地Chart.js加载失败，尝试备用路径');
                const backupScript = document.createElement('script');
                backupScript.src = "/direct-static/js/lib/chart.min.js";

                backupScript.onload = function() {
                    console.log('成功从备用路径加载Chart.js');
                    resolve();
                };

                backupScript.onerror = function() {
                    // 如果备用路径也失败，尝试CDN
                    console.warn('备用路径Chart.js加载失败，尝试CDN');
                    const cdnScript = document.createElement('script');
                    cdnScript.src = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";

                    cdnScript.onload = function() {
                        console.log('成功从CDN加载Chart.js');
                        resolve();
                    };

                    cdnScript.onerror = function() {
                        console.error('所有Chart.js加载尝试均失败');
                        reject(new Error('无法加载Chart.js'));
                    };

                    document.head.appendChild(cdnScript);
                };

                document.head.appendChild(backupScript);
            };

            document.head.appendChild(script);
        });
    }

    // 尝试在DOM加载完成后自动初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否在章节大纲分析页面
        const canvas = document.getElementById('chapter-outline-chart');
        if (canvas) {
            console.log('检测到章节大纲分析页面，自动初始化图表');
            setTimeout(window.initChapterOutlineChart, 100); // 延迟一点时间确保其他脚本先加载
        }
    });
})();
