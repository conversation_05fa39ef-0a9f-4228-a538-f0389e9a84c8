/**
 * 九猫系统 - 推理内容提取器
 * 版本: 1.0.0
 *
 * 该脚本用于从分析结果中提取推理过程
 */

(function() {
    console.log('[九猫修复] 推理内容提取器已启动');

    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {}, settings: {} };

    // 如果已经运行过，不再重复执行
    if (window.__nineCatsFixes.loaded['reasoning-content-extractor']) {
        console.log('[九猫修复] 推理内容提取器已经运行过，跳过');
        return;
    }

    // 配置
    const CONFIG = {
        // 是否启用调试日志
        enableDebug: false,
        // 是否启用自动提取
        enableAutoExtract: true,
        // 是否启用本地缓存
        enableLocalCache: true,
        // 本地缓存过期时间（毫秒）
        localCacheExpiry: 3600000 // 1小时
    };

    // 缓存对象
    const extractionCache = {};

    // 辅助函数：从本地存储获取缓存
    function getFromCache(cacheKey) {
        if (!CONFIG.enableLocalCache) return null;

        try {
            const cachedData = localStorage.getItem(`extracted_${cacheKey}`);
            if (!cachedData) return null;

            const data = JSON.parse(cachedData);

            // 检查缓存是否过期
            if (data.timestamp && (Date.now() - data.timestamp < CONFIG.localCacheExpiry)) {
                console.info(`[九猫修复] 从本地缓存加载提取内容: ${cacheKey}`);
                return data.content;
            }

            // 缓存已过期，删除
            localStorage.removeItem(`extracted_${cacheKey}`);
            return null;
        } catch (e) {
            console.warn(`[九猫修复] 读取本地缓存失败: ${e.message}`);
            return null;
        }
    }

    // 辅助函数：保存到本地存储
    function saveToCache(cacheKey, content) {
        if (!CONFIG.enableLocalCache) return;

        try {
            const data = {
                content: content,
                timestamp: Date.now()
            };

            localStorage.setItem(`extracted_${cacheKey}`, JSON.stringify(data));
            console.info(`[九猫修复] 提取内容已保存到本地缓存: ${cacheKey}`);
        } catch (e) {
            console.warn(`[九猫修复] 保存到本地缓存失败: ${e.message}`);
        }
    }

    // 主函数：从分析结果中提取推理过程
    function extractReasoningContent(content, dimension) {
        if (!content) return null;

        // 检查缓存
        const cacheKey = `${dimension}_${content.length}`;
        const cachedContent = getFromCache(cacheKey);

        if (cachedContent) {
            return cachedContent;
        }

        // 检查内容是否已经是推理过程
        // 推理过程通常以这些标记开头
        const reasoningStartMarkers = [
            '好的，我现在要',
            '首先，我需要',
            '嗯，用户让我',
            '我将分析',
            '我需要分析'
        ];

        // 检查内容是否以推理过程标记开头
        for (const marker of reasoningStartMarkers) {
            if (content.trim().startsWith(marker)) {
                console.info(`[九猫修复] 内容已经是推理过程，以"${marker}"开头`);

                // 保存到缓存
                saveToCache(cacheKey, content);

                return content;
            }
        }

        // 如果内容不是推理过程，尝试提取
        // 推理过程标记
        const processMarkers = [
            '分析过程',
            '首先，我需要',
            '我将分析',
            '我需要分析',
            '嗯，用户让我',
            '好的，我现在要',
            '下面我来分析',
            '让我来分析',
            '我会按照以下步骤',
            '我将按照以下步骤',
            '我将从以下几个方面',
            '我需要从以下几个方面',
            '我将逐步分析'
        ];

        // 结束标记
        const endMarkers = [
            '# 分析结果',
            '## 分析结果',
            '\n\n分析结果',
            '\n\n# ',
            '\n\n## ',
            '\n\n1. ',
            '\n\n总结',
            '\n\n综上所述',
            '\n\n综合以上分析',
            '\n\n通过以上分析'
        ];

        // 尝试提取推理过程
        let extractedContent = null;

        // 首先尝试使用维度特定的标记
        const dimensionMarkers = getDimensionSpecificMarkers(dimension);

        // 合并所有标记
        const allMarkers = [...dimensionMarkers, ...processMarkers];

        // 尝试每个标记
        for (const marker of allMarkers) {
            if (content.includes(marker)) {
                // 找到标记的位置
                const startIdx = content.indexOf(marker);
                let endIdx = -1;

                // 尝试每个结束标记
                for (const endMarker of endMarkers) {
                    const tempIdx = content.indexOf(endMarker, startIdx);
                    if (tempIdx !== -1 && (endIdx === -1 || tempIdx < endIdx)) {
                        endIdx = tempIdx;
                    }
                }

                // 提取内容
                if (endIdx !== -1) {
                    extractedContent = content.substring(startIdx, endIdx);
                } else {
                    // 如果找不到结束标记，使用整个剩余内容
                    extractedContent = content.substring(startIdx);
                }

                // 如果提取的内容太短，继续尝试其他标记
                if (extractedContent && extractedContent.length > 100) {
                    break;
                }
            }
        }

        // 如果找不到标记，尝试使用启发式方法
        if (!extractedContent || extractedContent.length < 100) {
            // 尝试查找包含"分析"、"思考"、"推理"等关键词的段落
            const paragraphs = content.split('\n\n');
            const relevantParagraphs = [];

            for (const paragraph of paragraphs) {
                if (paragraph.includes('分析') ||
                    paragraph.includes('思考') ||
                    paragraph.includes('推理') ||
                    paragraph.includes('步骤') ||
                    paragraph.includes('过程')) {
                    relevantParagraphs.push(paragraph);
                }
            }

            if (relevantParagraphs.length > 0) {
                extractedContent = relevantParagraphs.join('\n\n');
            }
        }

        // 如果仍然找不到，返回null
        if (!extractedContent || extractedContent.length < 50) {
            return null;
        }

        // 保存到缓存
        saveToCache(cacheKey, extractedContent);

        return extractedContent;
    }

    // 获取维度特定的标记
    function getDimensionSpecificMarkers(dimension) {
        const dimensionMarkers = {
            'language_style': [
                '分析语言风格',
                '语言风格分析',
                '我将分析这篇小说的语言风格',
                '我需要分析这篇小说的语言风格'
            ],
            'rhythm_pacing': [
                '分析节奏与节奏',
                '节奏与节奏分析',
                '我将分析这篇小说的节奏与节奏',
                '我需要分析这篇小说的节奏与节奏'
            ],
            'structure': [
                '分析结构',
                '结构分析',
                '我将分析这篇小说的结构',
                '我需要分析这篇小说的结构'
            ],
            'sentence_variation': [
                '分析句式变化',
                '句式变化分析',
                '我将分析这篇小说的句式变化',
                '我需要分析这篇小说的句式变化'
            ],
            'paragraph_length': [
                '分析段落长度',
                '段落长度分析',
                '我将分析这篇小说的段落长度',
                '我需要分析这篇小说的段落长度'
            ],
            'perspective_shifts': [
                '分析视角转换',
                '视角转换分析',
                '我将分析这篇小说的视角转换',
                '我需要分析这篇小说的视角转换'
            ],
            'paragraph_flow': [
                '分析段落流畅度',
                '段落流畅度分析',
                '我将分析这篇小说的段落流畅度',
                '我需要分析这篇小说的段落流畅度'
            ],
            'novel_characteristics': [
                '分析小说特点',
                '小说特点分析',
                '我将分析这篇小说的特点',
                '我需要分析这篇小说的特点'
            ],
            'world_building': [
                '分析世界构建',
                '世界构建分析',
                '我将分析这篇小说的世界构建',
                '我需要分析这篇小说的世界构建'
            ],
            'chapter_outline': [
                '分析章节大纲',
                '章节大纲分析',
                '我将分析这篇小说的章节大纲',
                '我需要分析这篇小说的章节大纲'
            ],
            'character_relationships': [
                '分析人物关系',
                '人物关系分析',
                '我将分析这篇小说的人物关系',
                '我需要分析这篇小说的人物关系'
            ],
            'opening_effectiveness': [
                '分析开篇效果',
                '开篇效果分析',
                '我将分析这篇小说的开篇效果',
                '我需要分析这篇小说的开篇效果'
            ],
            'climax_pacing': [
                '分析高潮节奏',
                '高潮节奏分析',
                '我将分析这篇小说的高潮节奏',
                '我需要分析这篇小说的高潮节奏'
            ]
        };

        return dimensionMarkers[dimension] || [];
    }

    // 自动提取页面上的推理内容
    function autoExtractReasoningContent() {
        if (!CONFIG.enableAutoExtract) return;

        // 查找所有分析结果容器
        const resultContainers = document.querySelectorAll('.analysis-content');

        if (resultContainers.length > 0) {
            console.info(`[九猫修复] 找到 ${resultContainers.length} 个分析结果容器`);

            // 查找所有推理内容容器
            const reasoningContainers = document.querySelectorAll('[data-reasoning-container]');

            // 如果有推理内容容器，但内容为空，尝试从分析结果中提取
            if (reasoningContainers.length > 0) {
                reasoningContainers.forEach(container => {
                    // 检查容器是否为空
                    if (container.querySelector('.alert') ||
                        container.querySelector('.spinner-border') ||
                        container.innerHTML.trim() === '') {

                        const dimension = container.getAttribute('data-dimension');

                        // 查找对应的分析结果
                        resultContainers.forEach(resultContainer => {
                            // 检查是否是同一个维度
                            const resultDimension = resultContainer.closest('[data-dimension]')?.getAttribute('data-dimension');

                            if (resultDimension === dimension) {
                                // 提取推理内容
                                const content = resultContainer.textContent;
                                const extractedContent = extractReasoningContent(content, dimension);

                                if (extractedContent) {
                                    // 显示提取的推理内容
                                    container.innerHTML = `<pre class="reasoning-text">${extractedContent}</pre>`;
                                    console.info(`[九猫修复] 成功从分析结果中提取推理内容: ${dimension}`);
                                }
                            }
                        });
                    }
                });
            }
        }
    }

    // 导出全局函数
    window.extractReasoningContent = extractReasoningContent;

    // 页面加载完成后自动提取
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoExtractReasoningContent);
    } else {
        // 如果DOM已加载完成，直接执行
        autoExtractReasoningContent();
    }

    // 标记为已加载
    window.__nineCatsFixes.loaded['reasoning-content-extractor'] = true;

    console.log('[九猫修复] 推理内容提取器初始化完成');
})();
