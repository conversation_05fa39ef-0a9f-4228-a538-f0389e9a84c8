/**
 * 九猫 - 综合图表修复脚本
 * 解决所有Chart.js加载和初始化问题
 * 版本: 3.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('综合图表修复脚本已加载 v3.0.0');
    
    // 存储图表实例
    window.chartInstances = window.chartInstances || {};
    
    // 已初始化的canvas集合
    const initializedCanvases = new Set();
    
    // Chart.js CDN地址列表 - 按优先级排序
    const chartJsPaths = [
        '/static/js/chart.min.js',      // 首先尝试js根目录的版本
        '/static/js/lib/chart.min.js',  // 然后尝试lib文件夹中的版本
        '/direct-static/js/chart.min.js', // 直接静态目录版本
        '/direct-static/js/lib/chart.min.js', // 直接静态lib目录版本
        'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js', // CDN备用
        'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js', // CDN备用
        'https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js', // 新版CDN备用
        'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.3.0/chart.umd.min.js' // 新版CDN备用
    ];
    
    // 修复DOM操作
    function fixDOMOperations() {
        console.log('修复DOM操作');
        
        // 修复replaceChild方法
        if (!window.__replaceChildFixed) {
            const originalReplaceChild = Node.prototype.replaceChild;
            
            Node.prototype.replaceChild = function(newChild, oldChild) {
                try {
                    // 尝试使用原始方法
                    return originalReplaceChild.call(this, newChild, oldChild);
                } catch (e) {
                    console.error('replaceChild错误:', e.message);
                    
                    // 尝试安全替换：先移除旧节点，再添加新节点
                    try {
                        if (this.contains(oldChild)) {
                            this.removeChild(oldChild);
                        }
                        this.appendChild(newChild);
                        return newChild;
                    } catch (e2) {
                        console.error('安全替换也失败:', e2.message);
                        return null;
                    }
                }
            };
            
            window.__replaceChildFixed = true;
            console.log('已修复replaceChild方法');
        }
    }
    
    // 确保Chart对象存在
    function ensureChartExists() {
        if (typeof Chart === 'undefined') {
            console.log('Chart对象未定义，创建备用Chart对象');
            
            // 定义全局Chart对象
            window.Chart = window.Chart || function(ctx, config) {
                console.log('使用备用Chart构造函数');
                this.ctx = ctx;
                this.config = config || {};
                this.data = config?.data || {};
                this.options = config?.options || {};
                this.type = config?.type || 'line';
                
                // 实现一些基本方法
                this.update = function() {
                    console.log('Chart.update() 被调用');
                    this.render();
                };
                
                this.destroy = function() {
                    console.log('Chart.destroy() 被调用');
                    // 清空画布
                    const canvas = ctx.canvas;
                    if (canvas) {
                        const context = canvas.getContext('2d');
                        if (context) {
                            context.clearRect(0, 0, canvas.width, canvas.height);
                        }
                    }
                };
                
                this.render = function() {
                    // 绘制简单的占位图
                    const canvas = ctx.canvas;
                    if (canvas) {
                        const context = canvas.getContext('2d');
                        if (context) {
                            // 清空画布
                            context.clearRect(0, 0, canvas.width, canvas.height);
                            
                            // 绘制边框
                            context.strokeStyle = '#cccccc';
                            context.lineWidth = 2;
                            context.strokeRect(2, 2, canvas.width - 4, canvas.height - 4);
                            
                            // 绘制类型和标题
                            context.fillStyle = '#666666';
                            context.font = 'bold 14px Arial';
                            context.textAlign = 'center';
                            context.textBaseline = 'middle';
                            context.fillText(`图表加载中... (${this.type})`, canvas.width / 2, 20);
                            
                            // 绘制加载中提示
                            context.fillStyle = '#999999';
                            context.font = '12px Arial';
                            context.fillText('Chart.js加载失败，使用备用渲染', canvas.width / 2, canvas.height / 2);
                            context.fillText('请刷新页面重试', canvas.width / 2, canvas.height / 2 + 20);
                        }
                    }
                };
                
                // 初始渲染
                this.render();
                
                return this;
            };
            
            // 添加必要的静态方法和属性
            Chart.register = function() { console.log('Chart.register() 被调用'); };
            Chart.defaults = {
                global: {
                    responsive: true,
                    maintainAspectRatio: true
                },
                font: {
                    family: 'Arial, sans-serif',
                    size: 12
                }
            };
            
            console.log('已创建Chart备用对象');
        }
    }
    
    // 加载Chart.js的函数
    function loadChartJs() {
        return new Promise((resolve, reject) => {
            // 检查是否已加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载，跳过重复加载');
                return resolve(window.Chart);
            }
            
            console.log('Chart.js未加载，开始尝试加载');
            
            // 记录加载尝试
            let loadAttempts = 0;
            let chartJsLoaded = false;
            
            // 从路径列表中加载
            function tryNextPath(index) {
                if (index >= chartJsPaths.length) {
                    console.error('所有Chart.js路径都加载失败，使用备用Chart对象');
                    ensureChartExists();
                    return resolve(window.Chart);
                }
                
                loadAttempts++;
                const url = chartJsPaths[index];
                
                console.log(`尝试从 ${url} 加载Chart.js (尝试 ${loadAttempts}/${chartJsPaths.length})`);
                
                // 创建脚本标签
                const script = document.createElement('script');
                script.src = url;
                script.async = false;
                
                // 成功加载
                script.onload = function() {
                    if (!chartJsLoaded && window.Chart) {
                        chartJsLoaded = true;
                        console.log(`成功从 ${url} 加载Chart.js`);
                        
                        // 确保Chart实例有destroy方法
                        ensureChartHasDestroyMethod();
                        
                        // 执行成功回调
                        resolve(window.Chart);
                    }
                };
                
                // 加载失败，尝试下一个路径
                script.onerror = function() {
                    console.warn(`从 ${url} 加载Chart.js失败，尝试下一个路径`);
                    
                    // 延迟尝试下一个，避免连续请求
                    setTimeout(function() {
                        if (!chartJsLoaded) {
                            tryNextPath(index + 1);
                        }
                    }, 100);
                };
                
                // 添加到文档
                document.head.appendChild(script);
            }
            
            // 开始尝试第一个路径
            tryNextPath(0);
        });
    }
    
    // 确保Chart.js实例有destroy方法
    function ensureChartHasDestroyMethod() {
        if (window.Chart && window.Chart.prototype) {
            // 检查原型上是否存在destroy方法
            if (typeof window.Chart.prototype.destroy !== 'function') {
                console.log('为Chart.js实例添加缺失的destroy方法');
                window.Chart.prototype.destroy = function() {
                    console.log('使用兼容的destroy方法清除图表');
                    try {
                        // 尝试使用现有的Chart.js方法
                        if (typeof Chart.getChart === 'function' && this.ctx && this.ctx.canvas) {
                            const existingChart = Chart.getChart(this.ctx.canvas);
                            if (existingChart && typeof existingChart.destroy === 'function') {
                                return existingChart.destroy();
                            }
                        }
                        
                        // 如果上述方法不可用，则手动清除画布
                        if (this.ctx && this.ctx.canvas) {
                            this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
                        }
                        return true;
                    } catch (e) {
                        console.warn('使用兼容的destroy方法时出错:', e);
                        return false;
                    }
                };
            }
        }
    }
    
    // 安全地创建图表
    function safeCreateChart(canvasId, config) {
        console.log(`尝试安全创建图表: ${canvasId}`);
        
        // 获取canvas元素
        let canvas;
        if (typeof canvasId === 'string') {
            canvas = document.getElementById(canvasId);
        } else {
            canvas = canvasId;
            canvasId = canvas.id || 'unknown';
        }
        
        if (!canvas) {
            console.error(`找不到canvas元素: ${canvasId}`);
            return null;
        }
        
        // 检查Chart.js是否已加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js未加载，无法创建图表');
            
            // 尝试加载Chart.js
            loadChartJs().then(() => {
                // 加载成功后重试创建图表
                setTimeout(() => safeCreateChart(canvas, config), 100);
            });
            
            return null;
        }
        
        try {
            // 先销毁已有图表
            safeDestroyChart(canvas);
            
            // 创建新图表
            const ctx = canvas.getContext('2d');
            const chart = new Chart(ctx, config);
            
            // 存储图表实例
            window.chartInstances[canvasId] = chart;
            
            // 直接在canvas上存储图表实例
            canvas._chart = chart;
            
            // 标记为已初始化
            initializedCanvases.add(canvasId);
            
            console.log(`成功创建图表: ${canvasId}`);
            return chart;
        } catch (e) {
            console.error(`创建图表时出错: ${canvasId}`, e);
            
            // 如果是Canvas已在使用的错误，尝试更激进的修复
            if (e.message && e.message.includes('Canvas is already in use')) {
                console.log('检测到Canvas已在使用错误，尝试重新创建canvas');
                
                try {
                    // 创建新的canvas元素替换旧的
                    const oldCanvas = canvas;
                    const newCanvas = document.createElement('canvas');
                    
                    // 复制所有属性
                    newCanvas.id = oldCanvas.id || '';
                    newCanvas.className = oldCanvas.className || '';
                    newCanvas.style.cssText = oldCanvas.style.cssText || '';
                    newCanvas.width = oldCanvas.width || 400;
                    newCanvas.height = oldCanvas.height || 300;
                    
                    // 复制数据属性
                    Array.from(oldCanvas.attributes).forEach(attr => {
                        if (attr.name.startsWith('data-')) {
                            newCanvas.setAttribute(attr.name, attr.value);
                        }
                    });
                    
                    // 安全替换canvas
                    try {
                        // 先尝试正常替换
                        oldCanvas.parentNode.replaceChild(newCanvas, oldCanvas);
                    } catch (replaceError) {
                        console.error('替换canvas时出错:', replaceError);
                        
                        // 如果替换失败，尝试先移除旧节点，再添加新节点
                        try {
                            if (oldCanvas.parentNode) {
                                oldCanvas.parentNode.removeChild(oldCanvas);
                                oldCanvas.parentNode.appendChild(newCanvas);
                            }
                        } catch (nodeError) {
                            console.error('节点操作失败:', nodeError);
                            return null;
                        }
                    }
                    
                    // 在新canvas上创建图表
                    const ctx = newCanvas.getContext('2d');
                    const chart = new Chart(ctx, config);
                    
                    // 存储图表实例
                    window.chartInstances[canvasId] = chart;
                    
                    // 直接在canvas上存储图表实例
                    newCanvas._chart = chart;
                    
                    // 标记为已初始化
                    initializedCanvases.add(canvasId);
                    
                    console.log(`在新canvas上成功创建图表: ${canvasId}`);
                    return chart;
                } catch (e2) {
                    console.error('激进修复也失败:', e2);
                    return null;
                }
            }
            
            return null;
        }
    }
    
    // 安全地销毁图表
    function safeDestroyChart(canvas) {
        let canvasId;
        
        // 处理不同类型的输入
        if (typeof canvas === 'string') {
            canvasId = canvas;
            canvas = document.getElementById(canvasId);
        } else if (canvas) {
            canvasId = canvas.id || 'unknown';
        } else {
            return false;
        }
        
        if (!canvas) {
            console.warn(`找不到canvas元素: ${canvasId}`);
            return false;
        }
        
        try {
            // 检查是否有存储在全局对象中的图表实例
            if (window.chartInstances && window.chartInstances[canvasId]) {
                console.log(`销毁全局存储的图表实例: ${canvasId}`);
                window.chartInstances[canvasId].destroy();
                delete window.chartInstances[canvasId];
            }
            
            // 检查是否有直接存储在canvas上的图表实例
            if (canvas._chart) {
                console.log(`销毁canvas上存储的图表实例: ${canvasId}`);
                canvas._chart.destroy();
                canvas._chart = null;
            }
            
            // 从已初始化集合中移除
            initializedCanvases.delete(canvasId);
            
            return true;
        } catch (e) {
            console.error(`销毁图表时出错: ${canvasId}`, e);
            return false;
        }
    }
    
    // 修复所有图表
    function fixAllCharts() {
        console.log('开始修复所有图表');
        
        // 获取所有canvas元素
        const canvases = document.querySelectorAll('canvas');
        console.log(`找到 ${canvases.length} 个canvas元素`);
        
        // 如果没有找到canvas元素，可能是页面还没有完全加载
        if (canvases.length === 0) {
            console.log('未找到canvas元素，可能页面尚未完全加载，稍后重试');
            setTimeout(fixAllCharts, 500);
            return;
        }
        
        // 确保Chart.js已加载
        loadChartJs().then(() => {
            // 遍历所有canvas元素
            canvases.forEach(canvas => {
                const canvasId = canvas.id || `canvas-${Math.random().toString(36).substr(2, 9)}`;
                
                // 如果canvas没有ID，添加一个
                if (!canvas.id) {
                    canvas.id = canvasId;
                }
                
                // 检查是否已初始化
                if (initializedCanvases.has(canvasId)) {
                    console.log(`跳过已初始化的canvas: ${canvasId}`);
                    return;
                }
                
                // 获取维度信息
                let dimension = canvas.getAttribute('data-dimension');
                
                // 如果没有直接的维度属性，尝试从父元素获取
                if (!dimension && canvas.closest) {
                    const card = canvas.closest('.analysis-card');
                    if (card) {
                        dimension = card.getAttribute('data-dimension');
                    }
                }
                
                // 创建默认图表配置
                const defaultConfig = {
                    type: 'bar',
                    data: {
                        labels: ['分析结果'],
                        datasets: [{
                            label: dimension || '分析',
                            data: [100],
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                };
                
                // 尝试从全局分析结果获取数据
                if (dimension && window.analysisResultsData && window.analysisResultsData[dimension]) {
                    const result = window.analysisResultsData[dimension];
                    
                    // 尝试获取可视化数据
                    if (result.metadata && result.metadata.visualization_data) {
                        try {
                            let visData;
                            
                            // 尝试解析可视化数据
                            if (typeof result.metadata.visualization_data === 'string') {
                                visData = JSON.parse(result.metadata.visualization_data);
                            } else {
                                visData = result.metadata.visualization_data;
                            }
                            
                            // 如果有有效的标签和数据，更新配置
                            if (visData && visData.labels && visData.data) {
                                defaultConfig.data.labels = visData.labels;
                                defaultConfig.data.datasets[0].data = visData.data;
                                
                                // 如果有图表类型，更新类型
                                if (visData.type) {
                                    defaultConfig.type = visData.type;
                                }
                                
                                console.log(`为维度 ${dimension} 使用可视化数据`);
                            }
                        } catch (e) {
                            console.warn(`解析维度 ${dimension} 的可视化数据时出错:`, e);
                        }
                    }
                }
                
                // 安全创建图表
                safeCreateChart(canvas, defaultConfig);
            });
        }).catch(err => {
            console.error('加载Chart.js失败:', err);
            
            // 使用备用Chart对象
            ensureChartExists();
            
            // 尝试使用备用对象创建图表
            canvases.forEach(canvas => {
                const canvasId = canvas.id || `canvas-${Math.random().toString(36).substr(2, 9)}`;
                
                // 如果canvas没有ID，添加一个
                if (!canvas.id) {
                    canvas.id = canvasId;
                }
                
                // 检查是否已初始化
                if (initializedCanvases.has(canvasId)) {
                    return;
                }
                
                // 创建简单的备用图表
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    // 清空画布
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    
                    // 绘制边框
                    ctx.strokeStyle = '#cccccc';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(2, 2, canvas.width - 4, canvas.height - 4);
                    
                    // 绘制提示文字
                    ctx.fillStyle = '#666666';
                    ctx.font = 'bold 14px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText('图表加载失败', canvas.width / 2, canvas.height / 2 - 10);
                    ctx.font = '12px Arial';
                    ctx.fillText('请刷新页面重试', canvas.width / 2, canvas.height / 2 + 10);
                }
            });
        });
    }
    
    // 添加全局错误处理
    function addGlobalErrorHandling() {
        console.log('添加全局错误处理');
        
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                // 处理Chart.js相关错误
                if (event.error.message.includes('Chart with ID') ||
                    event.error.message.includes('Canvas is already in use')) {
                    console.error('捕获到Chart.js相关错误:', event.error.message);
                    
                    // 尝试修复
                    setTimeout(fixAllCharts, 100);
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
                
                // 处理replaceChild错误
                if (event.error.message.includes('Failed to execute \'replaceChild\' on \'Node\'')) {
                    console.error('捕获到replaceChild错误:', event.error.message);
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);
    }
    
    // 修复分析过程显示问题
    function fixAnalysisProcessDisplay() {
        console.log('修复分析过程显示问题');
        
        // 查找所有折叠按钮
        const toggleButtons = document.querySelectorAll('.toggle-process-btn');
        
        // 为每个按钮添加点击事件
        toggleButtons.forEach(button => {
            // 移除现有事件监听器
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // 添加新的事件监听器
            newButton.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const target = document.getElementById(targetId);
                
                if (target) {
                    // 切换显示状态
                    if (target.style.display === 'none') {
                        target.style.display = 'block';
                        this.textContent = '隐藏分析过程';
                    } else {
                        target.style.display = 'none';
                        this.textContent = '显示分析过程';
                    }
                }
            });
        });
    }
    
    // 初始化函数
    function initialize() {
        console.log('初始化综合图表修复');
        
        // 修复DOM操作
        fixDOMOperations();
        
        // 添加全局错误处理
        addGlobalErrorHandling();
        
        // 修复分析过程显示问题
        fixAnalysisProcessDisplay();
        
        // 加载Chart.js
        loadChartJs().then(() => {
            console.log('Chart.js加载成功，开始修复图表');
            
            // 延迟执行图表修复，确保DOM已完全加载
            setTimeout(fixAllCharts, 300);
        }).catch(err => {
            console.error('Chart.js加载失败:', err);
            
            // 使用备用Chart对象
            ensureChartExists();
            
            // 延迟执行图表修复
            setTimeout(fixAllCharts, 300);
        });
    }
    
    // 在页面加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 页面已加载，立即执行
        initialize();
    }
    
    // 也在window.load时执行，以确保所有资源都已加载
    window.addEventListener('load', function() {
        // 再次尝试修复图表
        setTimeout(fixAllCharts, 500);
    });
    
    // 定期检查和修复
    setInterval(() => {
        // 检查是否有新的canvas元素
        const canvases = document.querySelectorAll('canvas');
        const uninitializedCanvases = Array.from(canvases).filter(canvas => {
            const canvasId = canvas.id || '';
            return !initializedCanvases.has(canvasId);
        });
        
        if (uninitializedCanvases.length > 0) {
            console.log(`发现 ${uninitializedCanvases.length} 个未初始化的canvas元素，尝试修复`);
            fixAllCharts();
        }
    }, 3000); // 每3秒检查一次
    
    // 导出全局方法以允许手动修复
    window.chartComprehensiveFix = {
        loadChartJs: loadChartJs,
        safeCreateChart: safeCreateChart,
        safeDestroyChart: safeDestroyChart,
        fixAllCharts: fixAllCharts,
        fixAnalysisProcessDisplay: fixAnalysisProcessDisplay,
        ensureChartExists: ensureChartExists
    };
    
    // 同时提供小写版本的全局变量，以确保兼容性
    window.chartFix = window.chartComprehensiveFix;
})();
