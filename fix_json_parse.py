import os
import re

def find_html_files(start_path):
    """查找所有HTML文件"""
    html_files = []
    for root, dirs, files in os.walk(start_path):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def fix_json_parse_error(file_path):
    """修复JSON.parse错误"""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # 查找JSON.parse调用
    json_parse_pattern = r'JSON\.parse\((.*?)\);'
    matches = re.findall(json_parse_pattern, content, re.DOTALL)
    
    if not matches:
        print(f"在文件 {file_path} 中没有找到 JSON.parse 调用")
        return False
    
    fixed = False
    new_content = content
    
    for match in matches:
        # 检查引号是否平衡
        if match.count("'") % 2 != 0:
            print(f"在文件 {file_path} 中找到不平衡的引号")
            # 修复引号不平衡的问题
            fixed_match = match
            if match.endswith("'"):
                fixed_match = match + "'"
            new_content = new_content.replace(match, fixed_match)
            fixed = True
        
        # 检查括号是否平衡
        if match.count('(') != match.count(')'):
            print(f"在文件 {file_path} 中找到不平衡的括号")
            # 修复括号不平衡的问题
            fixed_match = match
            if match.count('(') > match.count(')'):
                fixed_match = match + ')'
            new_content = new_content.replace(match, fixed_match)
            fixed = True
    
    if fixed:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"已修复文件 {file_path}")
        return True
    
    return False

def main():
    """主函数"""
    current_dir = os.getcwd()
    html_files = find_html_files(current_dir)
    
    print(f"找到 {len(html_files)} 个HTML文件")
    
    fixed_files = []
    for file_path in html_files:
        if fix_json_parse_error(file_path):
            fixed_files.append(file_path)
    
    if fixed_files:
        print(f"已修复 {len(fixed_files)} 个文件:")
        for file in fixed_files:
            print(f"  - {file}")
    else:
        print("没有找到需要修复的文件")

if __name__ == "__main__":
    main()
