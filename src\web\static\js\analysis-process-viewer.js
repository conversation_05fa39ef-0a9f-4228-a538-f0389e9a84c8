/**
 * 九猫 - 分析过程查看器
 * 用于优化分析过程的加载和显示，减少内存占用
 */

// 全局变量
var processViewerConfig = {
    novelId: null,
    dimension: null,
    apiUrl: null,
    pageSize: 10,
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    currentStage: 'all',
    searchQuery: '',
    processes: [],
    loadedProcessIds: new Set(),
    processContent: '', // 存储分析过程内容
    isPaginated: false, // 是否分页显示
    contentChunks: [], // 分页内容块
    currentContentPage: 0, // 当前内容页
    novel: { title: '未知小说', word_count: '未知' } // 小说信息
};

// 初始化分析过程查看器
function initProcessViewer(config) {
    console.log('初始化分析过程查看器', config);

    // 合并配置
    if (config) {
        processViewerConfig = {...processViewerConfig, ...config};
    }

    // 设置API URL
    if (!processViewerConfig.apiUrl) {
        processViewerConfig.apiUrl = `/api/novel/${processViewerConfig.novelId}/analysis/${processViewerConfig.dimension}/process`;
    }

    // 更新页面上的小说信息
    updateNovelInfo();

    // 初始化事件监听
    initEventListeners();

    // 加载分析过程内容
    loadProcessContent();

    // 加载第一页数据
    loadProcessData();
}

// 更新小说信息显示
function updateNovelInfo() {
    // 更新小说信息显示
    try {
        // 确保novel属性存在
        if (!processViewerConfig.novel) {
            processViewerConfig.novel = { title: '未知小说', word_count: '未知' };
            console.log('创建默认novel对象');
        }

        const wordCountElement = document.getElementById('wordCountValue');
        if (wordCountElement) {
            wordCountElement.textContent = processViewerConfig.novel.word_count || '未知';
        }
    } catch (e) {
        console.error('更新小说信息时出错:', e);
    }
}

// 初始化事件监听
function initEventListeners() {
    try {
        // 阶段过滤下拉框
        const stageFilter = document.getElementById('stage-filter');
        if (stageFilter) {
            stageFilter.addEventListener('change', function() {
                const stage = this.value;
                filterByStage(stage);
            });
        }

        // 搜索框
        const searchInput = document.getElementById('process-search');
        if (searchInput) {
            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    searchProcesses(this.value);
                }
            });
        }

        // 切换视图按钮
        const toggleViewButton = document.getElementById('toggleView');
        if (toggleViewButton) {
            toggleViewButton.addEventListener('click', function() {
                toggleProcessContentView();
            });
        }
    } catch (e) {
        console.error('初始化事件监听时出错:', e);
    }
}

// 加载分析过程数据
function loadProcessData() {
    const processList = document.getElementById('processList');
    if (!processList) {
        console.error('找不到processList元素');
        return;
    }

    processList.innerHTML = `
        <div class="text-center my-5">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">加载中...</span>
            </div>
            <p class="mt-2">正在加载分析过程数据，请稍候...</p>
        </div>
    `;

    // 构建API URL
    let url = `${processViewerConfig.apiUrl}?page=${processViewerConfig.currentPage}&limit=${processViewerConfig.pageSize}`;

    // 添加阶段过滤
    if (processViewerConfig.currentStage !== 'all') {
        url += `&stage=${processViewerConfig.currentStage}`;
    }

    // 添加搜索查询
    if (processViewerConfig.searchQuery) {
        url += `&search=${encodeURIComponent(processViewerConfig.searchQuery)}`;
    }

    console.log('请求数据URL:', url);

    // 请求数据
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新总数和分页信息
                processViewerConfig.totalItems = data.count;
                processViewerConfig.totalPages = Math.ceil(data.count / processViewerConfig.pageSize);

                // 更新摘要信息
                updateSummaryInfo(data);

                // 渲染分析过程
                renderProcesses(data.processes);

                // 更新分页
                renderPagination();
            } else {
                showError('加载分析过程数据失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('请求分析过程数据时出错:', error);
            showError('请求分析过程数据时出错: ' + error.message);
        });
}

// 更新摘要信息
function updateSummaryInfo(data) {
    try {
        // 更新总字数
        const wordCountElement = document.getElementById('wordCountValue');
        if (wordCountElement) {
            if (processViewerConfig.novel && processViewerConfig.novel.word_count) {
                wordCountElement.textContent = processViewerConfig.novel.word_count;
            } else {
                wordCountElement.textContent = '未知';
            }
        }

        // 更新创建时间
        const createdAtElement = document.getElementById('createdAtValue');
        if (createdAtElement) {
            if (data.metadata && data.metadata.created_at) {
                createdAtElement.textContent = formatDateTime(data.metadata.created_at);
            } else {
                createdAtElement.textContent = '未知';
            }
        }

        // 更新处理时间
        const processingTimeElement = document.getElementById('processingTimeValue');
        if (processingTimeElement) {
            if (data.metadata && data.metadata.processing_time) {
                processingTimeElement.textContent =
                    typeof data.metadata.processing_time === 'number'
                        ? `${data.metadata.processing_time.toFixed(2)} 秒`
                        : data.metadata.processing_time;
            } else {
                processingTimeElement.textContent = '未知';
            }
        }
    } catch (e) {
        console.error('更新摘要信息时出错:', e);
    }
}

// 渲染分析过程
function renderProcesses(processes) {
    const processList = document.getElementById('processList');
    if (!processList) {
        console.error('找不到processList元素');
        return;
    }

    // 清空现有内容
    processList.innerHTML = '';

    // 如果没有数据
    if (!processes || processes.length === 0) {
        processList.innerHTML = `
            <div class="alert alert-info">
                没有找到匹配的分析过程数据。
            </div>
        `;
        return;
    }

    // 渲染每个过程
    processes.forEach(process => {
        // 创建过程节点
        const node = document.createElement('div');
        node.id = `process-${process.id}`;
        node.className = `timeline-node ${process.processing_stage} ${process.is_successful ? '' : 'error'}`;
        node.setAttribute('data-stage', process.processing_stage);

        // 设置节点内容
        node.innerHTML = `
            <div class="card timeline-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        ${getStageBadge(process.processing_stage)}
                        ${getStageTitle(process.processing_stage, process.block_index, process.total_blocks)}
                    </h5>
                    <div>
                        ${process.is_successful
                            ? '<span class="badge badge-success">成功</span>'
                            : '<span class="badge badge-danger">失败</span>'}
                    </div>
                </div>
                <div class="card-body">
                    <div class="process-metadata mb-2">
                        <div class="row">
                            <div class="col-md-6">
                                <small>
                                    <i class="fas fa-clock"></i> 处理时间:
                                    ${process.processing_time
                                        ? `${process.processing_time} 毫秒`
                                        : '未记录'}
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small>
                                    <i class="fas fa-microchip"></i> 令牌数:
                                    ${process.tokens_used || '未记录'}
                                </small>
                            </div>
                        </div>
                    </div>

                    ${process.error_message ? `
                        <div class="alert alert-danger">
                            <strong>错误:</strong> ${process.error_message}
                        </div>
                    ` : ''}

                    <div class="process-content">
                        <div class="process-content-header mb-2">
                            <button class="btn btn-sm btn-outline-secondary process-content-toggle"
                                    data-toggle="collapse"
                                    data-target="#input-${process.id}"
                                    aria-expanded="false">
                                <i class="fas fa-caret-right"></i> 输入文本
                            </button>

                            <button class="btn btn-sm btn-outline-secondary process-content-toggle ml-2"
                                    data-toggle="collapse"
                                    data-target="#output-${process.id}"
                                    aria-expanded="false">
                                <i class="fas fa-caret-right"></i> 输出文本
                            </button>

                            <button class="btn btn-sm btn-outline-secondary process-content-toggle ml-2"
                                    data-toggle="collapse"
                                    data-target="#prompt-${process.id}"
                                    aria-expanded="false">
                                <i class="fas fa-caret-right"></i> 提示词
                            </button>
                        </div>

                        <div class="collapse" id="input-${process.id}">
                            <h6>输入文本:</h6>
                            <div class="process-content-body" id="input-content-${process.id}">
                                ${process.input_text_preview || '无输入文本'}
                                ${process.input_text_preview && !processViewerConfig.loadedProcessIds.has(process.id)
                                    ? '<div class="text-center mt-2"><button class="btn btn-sm btn-link load-full-content" data-process-id="' + process.id + '" data-content-type="input">加载完整内容</button></div>'
                                    : ''}
                            </div>
                        </div>

                        <div class="collapse" id="output-${process.id}">
                            <h6>输出文本:</h6>
                            <div class="process-content-body" id="output-content-${process.id}">
                                ${process.output_text_preview || '无输出文本'}
                                ${process.output_text_preview && !processViewerConfig.loadedProcessIds.has(process.id)
                                    ? '<div class="text-center mt-2"><button class="btn btn-sm btn-link load-full-content" data-process-id="' + process.id + '" data-content-type="output">加载完整内容</button></div>'
                                    : ''}
                            </div>
                        </div>

                        <div class="collapse" id="prompt-${process.id}">
                            <h6>提示词:</h6>
                            <div class="process-content-body" id="prompt-content-${process.id}">
                                ${process.prompt_used_preview || '无提示词'}
                                ${process.prompt_used_preview && !processViewerConfig.loadedProcessIds.has(process.id)
                                    ? '<div class="text-center mt-2"><button class="btn btn-sm btn-link load-full-content" data-process-id="' + process.id + '" data-content-type="prompt">加载完整内容</button></div>'
                                    : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到时间线
        processList.appendChild(node);
    });

    // 添加加载完整内容的事件监听
    document.querySelectorAll('.load-full-content').forEach(button => {
        button.addEventListener('click', function() {
            const processId = this.getAttribute('data-process-id');
            const contentType = this.getAttribute('data-content-type');
            loadFullContent(processId, contentType);
        });
    });

    // 添加折叠/展开事件
    document.querySelectorAll('.process-content-toggle').forEach(button => {
        button.addEventListener('click', function() {
            // 切换图标
            const icon = this.querySelector('i');
            if (icon.classList.contains('fa-caret-right')) {
                icon.classList.remove('fa-caret-right');
                icon.classList.add('fa-caret-down');
            } else {
                icon.classList.remove('fa-caret-down');
                icon.classList.add('fa-caret-right');
            }
        });
    });
}

// 加载完整内容
function loadFullContent(processId, contentType) {
    console.log(`加载完整内容: processId=${processId}, contentType=${contentType}`);

    // 显示加载指示器
    const contentElement = document.getElementById(`${contentType}-content-${processId}`);
    const originalContent = contentElement.innerHTML;
    contentElement.innerHTML = `
        <div class="text-center">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="sr-only">加载中...</span>
            </div>
            <p class="mt-2">正在加载完整内容，请稍候...</p>
        </div>
    `;

    // 请求完整内容
    fetch(`${processViewerConfig.apiUrl}/${processId}?content_type=${contentType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.process) {
                // 更新内容
                let fullContent = '';
                if (contentType === 'input') {
                    fullContent = data.process.input_text || '无输入文本';
                } else if (contentType === 'output') {
                    fullContent = data.process.output_text || '无输出文本';
                } else if (contentType === 'prompt') {
                    fullContent = data.process.prompt_used || '无提示词';
                }

                contentElement.textContent = fullContent;

                // 标记为已加载
                processViewerConfig.loadedProcessIds.add(parseInt(processId));
            } else {
                // 恢复原始内容
                contentElement.innerHTML = originalContent;

                // 显示错误
                alert('加载完整内容失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            // 恢复原始内容
            contentElement.innerHTML = originalContent;

            // 显示错误
            alert('请求完整内容时出错: ' + error.message);
        });
}

// 渲染分页
function renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) {
        console.error('找不到pagination元素');
        return;
    }

    // 清空现有内容
    pagination.innerHTML = '';

    // 如果只有一页，不显示分页
    if (processViewerConfig.totalPages <= 1) {
        return;
    }

    // 上一页按钮
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${processViewerConfig.currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `
        <a class="page-link" href="#" aria-label="上一页">
            <span aria-hidden="true">&laquo;</span>
        </a>
    `;
    if (processViewerConfig.currentPage > 1) {
        prevLi.querySelector('a').addEventListener('click', function(e) {
            e.preventDefault();
            goToPage(processViewerConfig.currentPage - 1);
        });
    }
    pagination.appendChild(prevLi);

    // 页码按钮
    const maxVisiblePages = 5;
    let startPage = Math.max(1, processViewerConfig.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(processViewerConfig.totalPages, startPage + maxVisiblePages - 1);

    // 调整startPage，确保显示maxVisiblePages个页码
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // 第一页
    if (startPage > 1) {
        const firstLi = document.createElement('li');
        firstLi.className = 'page-item';
        firstLi.innerHTML = '<a class="page-link" href="#">1</a>';
        firstLi.querySelector('a').addEventListener('click', function(e) {
            e.preventDefault();
            goToPage(1);
        });
        pagination.appendChild(firstLi);

        // 省略号
        if (startPage > 2) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = '<a class="page-link" href="#">...</a>';
            pagination.appendChild(ellipsisLi);
        }
    }

    // 页码
    for (let i = startPage; i <= endPage; i++) {
        const pageLi = document.createElement('li');
        pageLi.className = `page-item ${i === processViewerConfig.currentPage ? 'active' : ''}`;
        pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;

        if (i !== processViewerConfig.currentPage) {
            pageLi.querySelector('a').addEventListener('click', function(e) {
                e.preventDefault();
                goToPage(i);
            });
        }

        pagination.appendChild(pageLi);
    }

    // 最后一页
    if (endPage < processViewerConfig.totalPages) {
        // 省略号
        if (endPage < processViewerConfig.totalPages - 1) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = '<a class="page-link" href="#">...</a>';
            pagination.appendChild(ellipsisLi);
        }

        const lastLi = document.createElement('li');
        lastLi.className = 'page-item';
        lastLi.innerHTML = `<a class="page-link" href="#">${processViewerConfig.totalPages}</a>`;
        lastLi.querySelector('a').addEventListener('click', function(e) {
            e.preventDefault();
            goToPage(processViewerConfig.totalPages);
        });
        pagination.appendChild(lastLi);
    }

    // 下一页按钮
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${processViewerConfig.currentPage === processViewerConfig.totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `
        <a class="page-link" href="#" aria-label="下一页">
            <span aria-hidden="true">&raquo;</span>
        </a>
    `;
    if (processViewerConfig.currentPage < processViewerConfig.totalPages) {
        nextLi.querySelector('a').addEventListener('click', function(e) {
            e.preventDefault();
            goToPage(processViewerConfig.currentPage + 1);
        });
    }
    pagination.appendChild(nextLi);
}

// 跳转到指定页
function goToPage(page) {
    if (page < 1 || page > processViewerConfig.totalPages) {
        return;
    }

    processViewerConfig.currentPage = page;
    loadProcessData();

    // 滚动到顶部
    window.scrollTo(0, 0);
}

// 按阶段过滤
function filterByStage(stage) {
    processViewerConfig.currentStage = stage;
    processViewerConfig.currentPage = 1;
    loadProcessData();
}

// 搜索分析过程
function searchProcesses(query) {
    processViewerConfig.searchQuery = query;
    processViewerConfig.currentPage = 1;
    loadProcessData();
}

// 显示错误
function showError(message) {
    const processList = document.getElementById('processList');
    if (!processList) {
        console.error('找不到processList元素，无法显示错误信息:', message);
        return;
    }

    processList.innerHTML = `
        <div class="alert alert-danger">
            <strong>错误:</strong> ${message}
        </div>
    `;

    console.error('显示错误:', message);
}

// 获取阶段徽章
function getStageBadge(stage) {
    switch (stage) {
        case 'init':
            return '<span class="badge badge-secondary stage-badge">初始化</span>';
        case 'chunk_analysis':
            return '<span class="badge badge-primary stage-badge">分块分析</span>';
        case 'combine':
            return '<span class="badge badge-success stage-badge">结果合并</span>';
        case 'finalize':
            return '<span class="badge badge-danger stage-badge">最终处理</span>';
        default:
            return '<span class="badge badge-info stage-badge">未知阶段</span>';
    }
}

// 获取阶段标题
function getStageTitle(stage, blockIndex, totalBlocks) {
    switch (stage) {
        case 'init':
            return '初始化分析';
        case 'chunk_analysis':
            return `分块分析 (${blockIndex + 1}/${totalBlocks})`;
        case 'combine':
            return '合并分析结果';
        case 'finalize':
            return '最终处理';
        default:
            return '未知阶段';
    }
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    try {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (e) {
        return dateTimeStr;
    }
}

// 从响应中提取分析过程内容
function extractReasoningContentFromResponse(responseText) {
    if (!responseText) return null;

    // 如果是JSON格式的响应，尝试解析
    try {
        const data = JSON.parse(responseText);

        // 检查是否有reasoning_content字段
        if (data.reasoning_content) {
            console.log('从JSON响应中找到reasoning_content字段');
            return data.reasoning_content;
        }

        // 嵌套在result中
        if (data.result && data.result.reasoning_content) {
            console.log('从JSON响应的result字段中找到reasoning_content');
            return data.result.reasoning_content;
        }

        // 嵌套在data中
        if (data.data && data.data.reasoning_content) {
            console.log('从JSON响应的data字段中找到reasoning_content');
            return data.data.reasoning_content;
        }

        // 检查content字段是否包含reasoning_content
        if (data.content && typeof data.content === 'string' && data.content.includes('reasoning_content')) {
            console.log('从content字段中提取reasoning_content');
            return extractProcessContent(data.content);
        }

        // 检查是否包含分析过程特征而不是结果
        if (data.content && typeof data.content === 'string' &&
            (data.content.includes('首先，我需要') ||
             data.content.includes('用户让我') ||
             data.content.includes('我将分析'))) {
            console.log('内容包含分析过程的特征');
            return data.content;
        }
    } catch (e) {
        console.log('响应不是有效的JSON', e);
    }

    // 检查原始响应是否包含reasoning_content
    if (responseText.includes('reasoning_content')) {
        console.log('从原始响应文本中提取reasoning_content');

        // 尝试使用正则表达式提取
        const regexes = [
            /"reasoning_content"\s*:\s*"(.*?)(?:"\s*\}|\s*",)/s,  // JSON格式，双引号
            /'reasoning_content'\s*:\s*'(.*?)(?:'\s*\}|\s*',)/s,  // JSON格式，单引号
            /'reasoning_content':\s*'([^']*)'/s,                 // 简单格式，单引号
            /"reasoning_content":\s*"([^"]*)"/s,                 // 简单格式，双引号
            /reasoning_content':\s*'(.*?)(?:'}}|'})/s,          // 结尾格式1
            /reasoning_content':\s*'([^']*)'/s                   // 任意单引号内容
        ];

        for (const regex of regexes) {
            const matches = responseText.match(regex);
            if (matches && matches[1]) {
                console.log(`通过正则表达式找到reasoning_content`);
                return matches[1].replace(/\\n/g, '\n').replace(/\\\\/g, '\\');
            }
        }

        // 尝试使用标记查找
        const startMarkers = [
            "'reasoning_content': '",
            "'reasoning_content':'",
            '"reasoning_content": "',
            '"reasoning_content":"'
        ];

        const endMarkers = ["'}}", "'}", '"}', '"}}}', "'}}", "',", '",'];

        for (const startMarker of startMarkers) {
            if (responseText.includes(startMarker)) {
                const startIdx = responseText.indexOf(startMarker) + startMarker.length;

                for (const endMarker of endMarkers) {
                    const endIdx = responseText.indexOf(endMarker, startIdx);
                    if (endIdx > startIdx) {
                        const extracted = responseText.substring(startIdx, endIdx);
                        console.log(`通过标记 ${startMarker}...${endMarker} 找到reasoning_content`);
                        return extracted.replace(/\\n/g, '\n').replace(/\\\\/g, '\\');
                    }
                }
            }
        }

        return extractProcessContent(responseText);
    }

    // 如果找不到reasoning_content，尝试查找常见的分析过程开头
    const processStarts = [
        "嗯，用户让我",
        "好的，我现在要",
        "我将分析",
        "下面我来分析",
        "首先，我需要",
        "我需要分析",
        "用户让我对",
        "我需要对",
        "我将对"
    ];

    for (const phrase of processStarts) {
        if (responseText.includes(phrase)) {
            console.log(`从原始响应中找到分析过程特征: "${phrase}"`);

            // 尝试提取从这个短语开始到结果部分前的内容
            const startIdx = responseText.indexOf(phrase);
            const endMarkers = ["## 分析结果", "# 分析结果", "\n\n1. ", "\n\n总结", "总结：", "总结:"];
            let endIdx = -1;

            for (const marker of endMarkers) {
                const pos = responseText.indexOf(marker, startIdx);
                if (pos !== -1 && (endIdx === -1 || pos < endIdx)) {
                    endIdx = pos;
                }
            }

            const processContent = endIdx !== -1
                ? responseText.substring(startIdx, endIdx)
                : responseText.substring(startIdx);

            return processContent;
        }
    }

    // 检查是否包含示例中提到的格式
    if (responseText.includes("'reasoning_content':")) {
        const startIdx = responseText.indexOf("'reasoning_content':") + "'reasoning_content':".length;
        let endIdx = responseText.indexOf("'}", startIdx);
        if (endIdx === -1) {
            endIdx = responseText.indexOf("',", startIdx);
        }
        if (endIdx === -1) {
            endIdx = responseText.indexOf("'", startIdx + 1);
        }

        if (endIdx > startIdx) {
            let content = responseText.substring(startIdx, endIdx).trim();
            // 移除开头的单引号或双引号
            if (content.startsWith("'") || content.startsWith('"')) {
                content = content.substring(1);
            }
            console.log('通过特殊格式提取到reasoning_content');
            return content.replace(/\\n/g, '\n').replace(/\\\\/g, '\\');
        }
    }

    // 如果无法从响应中提取分析过程内容，检查是否整个响应就是分析过程内容
    // 避免直接显示分析结果
    if (responseText && responseText.length > 100 &&
        !responseText.startsWith('1.') &&
        !responseText.startsWith('# ') &&
        !responseText.startsWith('## ')) {
        console.log('将整个响应视为分析过程内容');
        return responseText;
    }

    return null;
}

// 处理从分析结果中提取的分析过程内容
function extractProcessContent(content) {
    if (!content) return null;

    console.log('尝试从内容中提取分析过程部分');

    // 如果内容看起来已经是分析过程，直接返回
    if (content.includes('首先，我需要') ||
        content.includes('用户让我') ||
        content.includes('我将分析')) {
        console.log('内容已经是分析过程');
        return content;
    }

    // 查找"分析过程"部分
    if (content.includes('分析过程')) {
        const processStart = content.indexOf('分析过程');
        // 找到下一节的开始
        const nextSections = ['分析结果', '## 结论', '# 结果', '# 分析结果', '## 分析结果'];
        let processEnd = -1;

        for (const section of nextSections) {
            const pos = content.indexOf(section, processStart);
            if (pos !== -1 && (processEnd === -1 || pos < processEnd)) {
                processEnd = pos;
            }
        }

        // 提取分析过程部分
        const processContent = processEnd !== -1
            ? content.substring(processStart, processEnd)
            : content.substring(processStart);

        console.log('从内容中提取到分析过程部分');
        return processContent;
    }

    // 如果内容中包含"reasoning_content"，尝试提取JSON格式
    if (content.includes('reasoning_content')) {
        try {
            // 尝试使用正则表达式提取
            const regexes = [
                /"reasoning_content"\s*:\s*"(.*?)"(?:\s*\}|\s*,)/s,  // JSON格式，双引号
                /'reasoning_content'\s*:\s*'(.*?)'(?:\s*\}|\s*,)/s,  // JSON格式，单引号
                /'reasoning_content':\s*'([^']*)'/s,                 // 简单格式，单引号
                /"reasoning_content":\s*"([^"]*)"/s,                 // 简单格式，双引号
                /reasoning_content':\s*'(.*?)(?:'}}|'})/s,          // 结尾格式1
                /reasoning_content':\s*'([^']*)'/s                   // 任意单引号内容
            ];

            for (const regex of regexes) {
                const matches = content.match(regex);
                if (matches && matches[1]) {
                    console.log(`通过正则表达式 ${regex} 找到reasoning_content`);
                    return matches[1].replace(/\\n/g, '\n').replace(/\\\\/g, '\\');
                }
            }

            // 尝试使用标记查找
            const startMarkers = [
                "'reasoning_content': '",
                "'reasoning_content':'",
                '"reasoning_content": "',
                '"reasoning_content":"'
            ];

            const endMarkers = ["'}}", "'}", '"}', '"}}}', "'}}", "',", '",'];

            for (const startMarker of startMarkers) {
                if (content.includes(startMarker)) {
                    const startIdx = content.indexOf(startMarker) + startMarker.length;

                    for (const endMarker of endMarkers) {
                        const endIdx = content.indexOf(endMarker, startIdx);
                        if (endIdx > startIdx) {
                            const extracted = content.substring(startIdx, endIdx);
                            console.log(`通过标记 ${startMarker}...${endMarker} 找到reasoning_content`);
                            return extracted.replace(/\\n/g, '\n').replace(/\\\\/g, '\\');
                        }
                    }
                }
            }

            // 尝试解析为JSON
            const jsonRegex = /\{[^{]*"reasoning_content"[^}]*\}/;
            const jsonMatch = content.match(jsonRegex);
            if (jsonMatch) {
                const jsonStr = jsonMatch[0];
                const jsonObj = JSON.parse(jsonStr);

                if (jsonObj.reasoning_content) {
                    console.log('通过JSON解析找到reasoning_content');
                    return jsonObj.reasoning_content;
                }
            }
        } catch (e) {
            console.error('提取reasoning_content时出错:', e);
        }
    }

    // 如果以上方法都无法提取出分析过程，检查内容是否符合分析过程的特征
    if (!content.startsWith('1.') && !content.startsWith('#') && content.length > 100) {
        // 如果内容不是以数字列表或标题开始，可能是分析过程而非结果
        console.log('内容不符合结果格式，可能是分析过程');
        return content;
    }

    // 无法提取到分析过程
    console.log('无法从内容中提取分析过程');
    return null;
}

// 加载分析过程内容
async function loadProcessContent() {
    const contentElement = document.getElementById('processContent');
    if (!contentElement) return;

    // 显示加载指示器
    contentElement.innerHTML = `
        <div class="loading-indicator">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">加载中...</span>
            </div>
            <p class="mt-2">正在加载分析过程内容，请稍候...</p>
        </div>
    `;

    // 首先尝试直接获取reasoning_content
    const reasoningUrl = `/api/novel/${processViewerConfig.novelId}/analysis/${processViewerConfig.dimension}/reasoning_content`;

    console.log('请求reasoning_content URL:', reasoningUrl);

    // 使用更可靠的方式请求数据
    try {
        // 使用async/await简化代码
        const response = await fetch(reasoningUrl);
        console.log('reasoning_content响应状态:', response.status);

        // 特殊处理404错误，将其视为"暂无数据"而非错误
        if (response.status === 404) {
            console.info('推理内容暂不可用 (HTTP 404)，尝试备用API');
            fallbackToProcessContent();
            return;
        }

        const text = await response.text();
        console.log('reasoning_content响应文本长度:', text.length);

        // 尝试从原始响应中提取reasoning_content
        const extractedContent = extractReasoningContentFromResponse(text);

        if (extractedContent) {
            console.log('成功从响应中提取到reasoning_content，长度:', extractedContent.length);

            // 构建Markdown内容
            const novel = processViewerConfig.novel || { title: '未知小说' };
            const dimension = processViewerConfig.dimension;

            let content = `# ${novel.title} - ${dimension} 分析过程\n\n`;
            content += "## 分析摘要\n\n";
            if (novel.title) content += `- **小说标题**: ${novel.title}\n`;
            if (novel.author) content += `- **作者**: ${novel.author}\n`;
            content += `- **分析维度**: ${dimension}\n`;
            if (novel.word_count) content += `- **总字数**: ${novel.word_count}\n\n`;

            // 如果内容不包含"分析过程"标题，添加一个
            if (!extractedContent.includes('分析过程') && !extractedContent.includes('## 分析过程')) {
                content += "## 分析过程\n\n";
            }

            content += extractedContent;

            // 保存内容
            processViewerConfig.processContent = content;

            // 添加提示
            const notice = `<div class="alert alert-info">
                <strong>注意：</strong> 显示的是从分析结果中提取的分析过程内容。
            </div>`;

            // 在第一个标题后插入提示
            const firstHeadingEnd = processViewerConfig.processContent.indexOf('\n\n', processViewerConfig.processContent.indexOf('# '));
            if (firstHeadingEnd !== -1) {
                processViewerConfig.processContent =
                    processViewerConfig.processContent.substring(0, firstHeadingEnd + 2) +
                    notice +
                    processViewerConfig.processContent.substring(firstHeadingEnd + 2);
            } else {
                processViewerConfig.processContent = notice + processViewerConfig.processContent;
            }

            // 检查内容是否是JSON格式的错误信息
            if (processViewerConfig.processContent.startsWith('{') && processViewerConfig.processContent.includes('error')) {
                try {
                    const errorObj = JSON.parse(processViewerConfig.processContent);
                    if (errorObj.error) {
                        console.error('内容包含错误信息:', errorObj.error);
                        showProcessContentError('加载分析过程内容失败: ' + errorObj.error);
                        return;
                    }
                } catch (e) {
                    console.log('内容不是有效的JSON，继续渲染');
                }
            }

            // 渲染内容
            renderProcessContent();
        } else {
            // 如果无法从响应中提取reasoning_content，尝试解析为JSON
            try {
                const data = JSON.parse(text);

                if (data.success && data.reasoning_content) {
                    console.log('从JSON响应中获取到reasoning_content，长度:', data.reasoning_content.length);

                    // 构建Markdown内容
                    const novel = processViewerConfig.novel || { title: '未知小说' };
                    const dimension = processViewerConfig.dimension;

                    let content = `# ${novel.title} - ${dimension} 分析过程\n\n`;
                    content += "## 分析摘要\n\n";
                    if (novel.title) content += `- **小说标题**: ${novel.title}\n`;
                    if (novel.author) content += `- **作者**: ${novel.author}\n`;
                    content += `- **分析维度**: ${dimension}\n`;
                    if (novel.word_count) content += `- **总字数**: ${novel.word_count}\n\n`;

                    // 处理reasoning_content，提取分析过程部分
                    let processContent = extractProcessContent(data.reasoning_content);

                    // 检查提取的内容是否为空或太短
                    if (!processContent || processContent.trim().length < 20) {
                        console.log('提取的分析过程内容太短，使用原始内容');
                        processContent = data.reasoning_content;
                    }

                    // 如果内容不包含"分析过程"标题，添加一个
                    if (!processContent.includes('分析过程') && !processContent.includes('## 分析过程')) {
                        content += "## 分析过程\n\n";
                    }

                    content += processContent;

                    // 保存内容
                    processViewerConfig.processContent = content;

                    // 添加提示
                    const notice = `<div class="alert alert-info">
                        <strong>注意：</strong> 显示的是从分析结果中提取的分析过程内容。
                    </div>`;

                    // 在第一个标题后插入提示
                    const firstHeadingEnd = processViewerConfig.processContent.indexOf('\n\n', processViewerConfig.processContent.indexOf('# '));
                    if (firstHeadingEnd !== -1) {
                        processViewerConfig.processContent =
                            processViewerConfig.processContent.substring(0, firstHeadingEnd + 2) +
                            notice +
                            processViewerConfig.processContent.substring(firstHeadingEnd + 2);
                    } else {
                        processViewerConfig.processContent = notice + processViewerConfig.processContent;
                    }

                    // 检查内容是否是JSON格式的错误信息
                    if (processViewerConfig.processContent.startsWith('{') && processViewerConfig.processContent.includes('error')) {
                        try {
                            const errorObj = JSON.parse(processViewerConfig.processContent);
                            if (errorObj.error) {
                                console.error('内容包含错误信息:', errorObj.error);
                                showProcessContentError('加载分析过程内容失败: ' + errorObj.error);
                                return;
                            }
                        } catch (e) {
                            console.log('内容不是有效的JSON，继续渲染');
                        }
                    }

                    // 渲染内容
                    renderProcessContent();
                } else {
                    // 如果直接获取reasoning_content失败，尝试使用原来的API
                    fallbackToProcessContent();
                }
            } catch (e) {
                console.error('解析JSON响应时出错:', e);
                // 如果解析JSON出错，尝试使用原来的API
                fallbackToProcessContent();
            }
        }
    } catch (error) {
        // 使用警告级别而非错误级别
        console.warn('请求推理内容暂时不可用:', error.message);
        // 如果出错，尝试使用原来的API
        fallbackToProcessContent();
    }
}

// 回退到原来的分析过程内容API
async function fallbackToProcessContent() {
    const contentElement = document.getElementById('processContent');
    if (!contentElement) return;

    // 构建API URL
    const url = `/api/novel/${processViewerConfig.novelId}/analysis/${processViewerConfig.dimension}/process/content`;

    console.log('回退到原API URL:', url);

    try {
        // 使用async/await简化代码
        const response = await fetch(url);
        console.log('分析过程内容响应状态:', response.status);

        const data = await response.json();
        console.log('分析过程内容响应数据:', data);

        if (data.success && data.content) {
            // 尝试从内容中提取分析过程部分
            let content = data.content;
            console.log('获取到content，长度:', content.length);

            // 使用辅助函数提取分析过程部分
            const processContent = extractProcessContent(content);

            // 检查提取的内容是否为空或太短
            if (processContent && processContent.trim().length >= 20 && processContent !== content) {
                console.log('成功提取到分析过程部分');
                // 如果成功提取到分析过程部分，重新构建内容
                const novel = processViewerConfig.novel || { title: '未知小说' };
                const dimension = processViewerConfig.dimension;

                let newContent = `# ${novel.title} - ${dimension} 分析过程\n\n`;
                newContent += "## 分析摘要\n\n";
                if (novel.title) newContent += `- **小说标题**: ${novel.title}\n`;
                if (novel.author) newContent += `- **作者**: ${novel.author}\n`;
                newContent += `- **分析维度**: ${dimension}\n`;
                if (novel.word_count) newContent += `- **总字数**: ${novel.word_count}\n\n`;

                // 如果内容不包含"分析过程"标题，添加一个
                if (!processContent.includes('分析过程') && !processContent.includes('## 分析过程')) {
                    newContent += "## 分析过程\n\n";
                }

                newContent += processContent;

                content = newContent;
            } else {
                console.log('未能提取到有效的分析过程部分，使用原始内容');
            }

            // 检查内容是否是JSON格式的错误信息
            if (content.startsWith('{') && content.includes('error')) {
                try {
                    const errorObj = JSON.parse(content);
                    if (errorObj.error) {
                        console.error('内容包含错误信息:', errorObj.error);
                        showProcessContentError('加载分析过程内容失败: ' + errorObj.error);
                        return;
                    }
                } catch (e) {
                    console.log('内容不是有效的JSON，继续渲染');
                }
            }

            // 保存内容
            processViewerConfig.processContent = content;

            // 根据内容来源添加适当的提示
            if (data.source) {
                let noticeText = '';
                let noticeType = 'info';

                if (data.source === 'reasoning_content') {
                    noticeText = '显示的是从分析结果中提取的分析过程内容。';
                } else if (data.source === 'analysis_logs') {
                    noticeText = '没有找到完整的分析过程记录，显示的是从分析日志中提取的内容。';
                } else if (data.source === 'analysis_result' || data.source === 'analysis_result_content') {
                    noticeText = '没有找到完整的分析过程记录，显示的是从分析结果中提取的内容。';
                } else if (data.is_mock) {
                    noticeText = '没有找到实际的分析过程记录，显示的是模拟的分析过程内容。';
                    noticeType = 'warning';
                }

                if (noticeText) {
                    // 在内容顶部添加提示
                    const notice = `<div class="alert alert-${noticeType}">
                        <strong>注意：</strong> ${noticeText}
                    </div>`;

                    // 在第一个标题后插入提示
                    const firstHeadingEnd = processViewerConfig.processContent.indexOf('\n\n', processViewerConfig.processContent.indexOf('# '));
                    if (firstHeadingEnd !== -1) {
                        processViewerConfig.processContent =
                            processViewerConfig.processContent.substring(0, firstHeadingEnd + 2) +
                            notice +
                            processViewerConfig.processContent.substring(firstHeadingEnd + 2);
                    } else {
                        processViewerConfig.processContent = notice + processViewerConfig.processContent;
                    }
                }
            }

            // 渲染内容
            renderProcessContent();
        } else {
            showProcessContentError('加载分析过程内容失败: ' + (data.error || '未找到分析过程内容'));
        }
    } catch (error) {
        // 使用警告级别而非错误级别
        console.warn('请求分析过程内容暂时不可用:', error.message);

        // 显示友好的提示信息，而非错误信息
        contentElement.innerHTML = `
            <div class="alert alert-info">
                <p><i class="fas fa-info-circle"></i> 暂无分析过程数据</p>
                <p class="small text-muted mt-2">可能的原因：</p>
                <ul class="small text-muted">
                    <li>该分析是在启用分析过程记录功能之前进行的</li>
                    <li>分析过程中未生成分析过程数据</li>
                    <li>分析过程数据已被清理</li>
                    <li>分析尚未完成，请稍后再查看</li>
                </ul>
            </div>
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" onclick="location.reload()">刷新页面</button>
            </div>
        `;
    }
}

// 渲染分析过程内容
function renderProcessContent() {
    const contentElement = document.getElementById('processContent');
    if (!contentElement) return;

    // 如果没有内容
    if (!processViewerConfig.processContent) {
        contentElement.innerHTML = `
            <div class="alert alert-info">
                没有找到分析过程内容。这可能是因为该分析是在启用详细过程记录功能之前进行的。
            </div>
        `;
        return;
    }

    // 检查内容是否是JSON格式的错误信息
    if (typeof processViewerConfig.processContent === 'string' &&
        processViewerConfig.processContent.startsWith('{') &&
        processViewerConfig.processContent.includes('error')) {
        try {
            const errorObj = JSON.parse(processViewerConfig.processContent);
            if (errorObj.error) {
                console.error('内容包含错误信息:', errorObj.error);
                showProcessContentError('加载分析过程内容失败: ' + errorObj.error);
                return;
            }
        } catch (e) {
            console.log('内容不是有效的JSON，继续渲染');
        }
    }

    // 如果是分页模式
    if (processViewerConfig.isPaginated) {
        // 如果还没有分割内容
        if (processViewerConfig.contentChunks.length === 0) {
            processViewerConfig.contentChunks = splitContent(processViewerConfig.processContent);
        }

        // 显示当前页
        contentElement.innerHTML = processViewerConfig.contentChunks[processViewerConfig.currentContentPage];

        // 更新分页控制
        updateProcessContentPagination();
    } else {
        // 显示完整内容
        contentElement.innerHTML = processViewerConfig.processContent;
    }

    // 添加样式到代码块
    setTimeout(() => {
        // 为代码块添加样式
        const codeBlocks = contentElement.querySelectorAll('pre code');
        if (codeBlocks.length > 0) {
            codeBlocks.forEach(block => {
                block.classList.add('language-plaintext');
            });
        }

        // 为表格添加Bootstrap样式
        const tables = contentElement.querySelectorAll('table');
        if (tables.length > 0) {
            tables.forEach(table => {
                table.classList.add('table', 'table-bordered', 'table-striped', 'table-hover');
            });
        }

        // 为图片添加响应式样式
        const images = contentElement.querySelectorAll('img');
        if (images.length > 0) {
            images.forEach(img => {
                img.classList.add('img-fluid');
            });
        }
    }, 100);
}

// 切换分析过程内容视图
function toggleProcessContentView() {
    try {
        const timelineView = document.getElementById('timelineView');
        const contentView = document.getElementById('contentView');
        const toggleButton = document.getElementById('toggleView');

        if (!timelineView || !contentView || !toggleButton) {
            console.error('找不到视图切换所需的元素');
            return;
        }

        // 切换视图
        if (timelineView.style.display !== 'none') {
            // 切换到内容视图
            timelineView.style.display = 'none';
            contentView.style.display = 'block';
            toggleButton.innerHTML = '<i class="fas fa-list"></i> 切换到时间线视图';

            // 确保内容已加载
            if (!processViewerConfig.processContent) {
                loadProcessContent();
            } else {
                renderProcessContent();
            }
        } else {
            // 切换到时间线视图
            timelineView.style.display = 'block';
            contentView.style.display = 'none';
            toggleButton.innerHTML = '<i class="fas fa-file-alt"></i> 切换到完整过程视图';
        }
    } catch (e) {
        console.error('切换视图时出错:', e);
    }
}

// 更新分析过程内容分页控制
function updateProcessContentPagination() {
    const paginationElement = document.getElementById('processContentPagination');
    if (!paginationElement) return;

    // 如果只有一页，不显示分页
    if (processViewerConfig.contentChunks.length <= 1) {
        paginationElement.classList.add('d-none');
        return;
    }

    // 显示分页控制
    paginationElement.classList.remove('d-none');

    // 更新分页控制内容
    paginationElement.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <button class="btn btn-sm btn-outline-secondary" id="prevContentPage" ${processViewerConfig.currentContentPage === 0 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i> 上一页
            </button>
            <span class="page-info">第 ${processViewerConfig.currentContentPage + 1} 页，共 ${processViewerConfig.contentChunks.length} 页</span>
            <button class="btn btn-sm btn-outline-secondary" id="nextContentPage" ${processViewerConfig.currentContentPage === processViewerConfig.contentChunks.length - 1 ? 'disabled' : ''}>
                下一页 <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    `;

    // 添加分页按钮事件
    document.getElementById('prevContentPage').addEventListener('click', function() {
        if (processViewerConfig.currentContentPage > 0) {
            processViewerConfig.currentContentPage--;
            renderProcessContent();
            // 滚动到顶部
            document.getElementById('processContent').scrollIntoView({ behavior: 'smooth' });
        }
    });

    document.getElementById('nextContentPage').addEventListener('click', function() {
        if (processViewerConfig.currentContentPage < processViewerConfig.contentChunks.length - 1) {
            processViewerConfig.currentContentPage++;
            renderProcessContent();
            // 滚动到顶部
            document.getElementById('processContent').scrollIntoView({ behavior: 'smooth' });
        }
    });
}

// 分割内容为多个块
function splitContent(content) {
    // 检查内容长度，如果不够长，不需要分页
    if (content.length < 5000) {
        return [content];
    }

    // 尝试按标题分割
    const headingRegex = /<h[1-3][^>]*>.*?<\/h[1-3]>/gi;
    const headingMatches = content.match(headingRegex);

    if (headingMatches && headingMatches.length > 1) {
        // 有多个标题，按标题分割
        const chunks = [];
        let lastIndex = 0;
        let currentChunk = '';
        let headingsInChunk = 0;

        // 遍历内容，按标题分割
        for (let i = 0; i < headingMatches.length; i++) {
            const heading = headingMatches[i];
            const headingIndex = content.indexOf(heading, lastIndex);

            if (headingIndex > lastIndex) {
                // 添加标题前的内容
                currentChunk += content.substring(lastIndex, headingIndex);
            }

            // 添加当前标题
            currentChunk += heading;
            lastIndex = headingIndex + heading.length;
            headingsInChunk++;

            // 每2-3个标题创建一个新块
            if (headingsInChunk >= 2 || currentChunk.length > 3000) {
                // 添加标题后的内容，直到下一个标题
                if (i < headingMatches.length - 1) {
                    const nextHeading = headingMatches[i + 1];
                    const nextHeadingIndex = content.indexOf(nextHeading, lastIndex);
                    if (nextHeadingIndex > lastIndex) {
                        currentChunk += content.substring(lastIndex, nextHeadingIndex);
                        lastIndex = nextHeadingIndex;
                    }
                }

                chunks.push(currentChunk);
                currentChunk = '';
                headingsInChunk = 0;
            }
        }

        // 添加剩余内容
        if (lastIndex < content.length) {
            currentChunk += content.substring(lastIndex);
            if (currentChunk.trim()) {
                chunks.push(currentChunk);
            }
        }

        return chunks.length > 0 ? chunks : [content];
    } else {
        // 没有足够的标题，按长度分割
        const chunks = [];
        const maxChunkLength = 3000;

        for (let i = 0; i < content.length; i += maxChunkLength) {
            chunks.push(content.substring(i, i + maxChunkLength));
        }

        return chunks;
    }
}

// 显示分析过程内容错误
function showProcessContentError(message) {
    const contentElement = document.getElementById('processContent');
    if (!contentElement) return;

    contentElement.innerHTML = `
        <div class="alert alert-danger">
            <strong>错误:</strong> ${message}
        </div>
    `;
}
