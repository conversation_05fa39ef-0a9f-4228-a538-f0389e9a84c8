/**
 * 九猫 - 独立维度选择对话框脚本
 * 直接在页面上添加一个独立的维度选择对话框
 * 版本: 1.0.0
 */

(function() {
    console.log('[独立维度选择] 脚本已加载');

    // 维度列表 - 与config.py中的ANALYSIS_DIMENSIONS保持一致
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "chapter_outline", name: "章节大纲" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" },
        { key: "outline_analysis", name: "大纲分析" },
        { key: "popular_tropes", name: "热梗统计" }
    ];

    // 添加独立维度选择对话框
    function addStandaloneDimensionSelector() {
        console.log('[独立维度选择] 添加独立维度选择对话框');

        // 获取当前页面的小说ID和章节ID
        let novelId = null;
        let chapterId = null;

        // 尝试从URL中获取小说ID和章节ID
        const urlMatch = window.location.pathname.match(/\/novel\/(\d+)\/chapter\/(\d+)/);
        if (urlMatch) {
            novelId = urlMatch[1];
            chapterId = urlMatch[2];
            console.log(`[独立维度选择] 从URL获取到小说ID: ${novelId}, 章节ID: ${chapterId}`);
        } else {
            // 尝试从页面中获取小说ID和章节ID
            const breadcrumbs = document.querySelectorAll('.breadcrumb-item a');
            breadcrumbs.forEach(link => {
                const href = link.getAttribute('href');
                if (href) {
                    const novelMatch = href.match(/\/novel\/(\d+)/);
                    if (novelMatch) {
                        novelId = novelMatch[1];
                    }
                }
            });

            // 尝试从当前页面标题获取章节ID
            const titleElement = document.querySelector('.card-header h2');
            if (titleElement) {
                const titleText = titleElement.textContent;
                const chapterMatch = titleText.match(/第(\d+)章/);
                if (chapterMatch) {
                    // 这里只是获取章节编号，不是章节ID
                    const chapterNumber = chapterMatch[1];
                    console.log(`[独立维度选择] 从标题获取到章节编号: ${chapterNumber}`);
                }
            }
        }

        if (!novelId || !chapterId) {
            console.error('[独立维度选择] 无法获取小说ID或章节ID');
            return;
        }

        // 创建对话框容器
        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.top = '50%';
        container.style.left = '50%';
        container.style.transform = 'translate(-50%, -50%)';
        container.style.zIndex = '10000';
        container.style.backgroundColor = 'white';
        container.style.padding = '20px';
        container.style.borderRadius = '5px';
        container.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';
        container.style.maxWidth = '500px';
        container.style.width = '90%';
        container.style.maxHeight = '80vh';
        container.style.overflowY = 'auto';
        container.style.display = 'none';
        container.id = 'standalone-dimension-selector';

        // 创建对话框内容
        container.innerHTML = `
            <div class="standalone-dialog-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0;">选择分析维度</h3>
                <button id="close-standalone-dialog" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            </div>
            <div class="standalone-dialog-body">
                <div class="form-check" style="margin-bottom: 10px;">
                    <input class="form-check-input" type="checkbox" id="standalone-select-all" checked>
                    <label class="form-check-label" for="standalone-select-all">
                        <strong>全选</strong>
                    </label>
                </div>
                <hr style="margin: 10px 0;">
                <div id="standalone-dimensions-container">
                    ${DIMENSIONS.map((dimension, index) => `
                    <div class="form-check" style="margin-bottom: 10px;">
                        <input class="form-check-input standalone-dimension-checkbox" type="checkbox" value="${dimension.key}" id="standalone-dimension-${index}" checked>
                        <label class="form-check-label" for="standalone-dimension-${index}">
                            ${dimension.name}
                        </label>
                    </div>
                    `).join('')}
                </div>
                <div style="margin-top: 15px;">
                    <label for="standalone-model-select" style="display: block; margin-bottom: 5px;">选择分析模型</label>
                    <select id="standalone-model-select" style="width: 100%; padding: 5px; border-radius: 3px; border: 1px solid #ccc;">
                        <option value="deepseek-r1">DeepSeek R1</option>
                        <option value="qwen-plus-latest">通义千问-Plus-Latest</option>
                    </select>
                </div>
                <div style="margin-top: 15px; color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 3px;">
                    <small>注意：分析过程可能需要较长时间，尤其是对于长篇小说。</small>
                </div>
            </div>
            <div class="standalone-dialog-footer" style="margin-top: 20px; text-align: right;">
                <button id="standalone-cancel-btn" style="padding: 5px 10px; margin-right: 10px; background-color: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer;">取消</button>
                <button id="standalone-start-btn" style="padding: 5px 10px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">开始分析</button>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(container);

        // 创建背景遮罩
        const backdrop = document.createElement('div');
        backdrop.style.position = 'fixed';
        backdrop.style.top = '0';
        backdrop.style.left = '0';
        backdrop.style.width = '100%';
        backdrop.style.height = '100%';
        backdrop.style.backgroundColor = 'rgba(0,0,0,0.5)';
        backdrop.style.zIndex = '9999';
        backdrop.style.display = 'none';
        backdrop.id = 'standalone-backdrop';
        document.body.appendChild(backdrop);

        // 创建触发按钮
        const button = document.createElement('button');
        button.className = 'btn btn-primary';
        button.textContent = '选择维度分析';
        button.id = 'standalone-trigger-btn';
        button.style.position = 'fixed';
        button.style.bottom = '20px';
        button.style.right = '20px';
        button.style.zIndex = '9998';
        button.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';
        document.body.appendChild(button);

        // 设置事件监听器
        // 打开对话框
        document.getElementById('standalone-trigger-btn').addEventListener('click', function() {
            document.getElementById('standalone-dimension-selector').style.display = 'block';
            document.getElementById('standalone-backdrop').style.display = 'block';
        });

        // 关闭对话框
        document.getElementById('close-standalone-dialog').addEventListener('click', function() {
            document.getElementById('standalone-dimension-selector').style.display = 'none';
            document.getElementById('standalone-backdrop').style.display = 'none';
        });

        document.getElementById('standalone-cancel-btn').addEventListener('click', function() {
            document.getElementById('standalone-dimension-selector').style.display = 'none';
            document.getElementById('standalone-backdrop').style.display = 'none';
        });

        // 全选按钮
        document.getElementById('standalone-select-all').addEventListener('change', function() {
            const isChecked = this.checked;
            document.querySelectorAll('.standalone-dimension-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });

        // 开始分析按钮
        document.getElementById('standalone-start-btn').addEventListener('click', function() {
            console.log('[独立维度选择] 点击开始分析按钮');

            // 获取选中的维度
            const dimensions = [];
            document.querySelectorAll('.standalone-dimension-checkbox:checked').forEach(checkbox => {
                dimensions.push(checkbox.value);
            });

            const model = document.getElementById('standalone-model-select').value;

            if (dimensions.length === 0) {
                alert('请至少选择一个分析维度');
                return;
            }

            // 使用第一个维度作为主维度
            const dimension = dimensions[0];

            // 禁用按钮并显示加载状态
            const startBtn = document.getElementById('standalone-start-btn');
            startBtn.disabled = true;
            startBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';

            // 关闭对话框
            document.getElementById('standalone-dimension-selector').style.display = 'none';
            document.getElementById('standalone-backdrop').style.display = 'none';

            // 显示进度条
            const progressContainer = document.getElementById('analysisProgressContainer');
            if (progressContainer) {
                progressContainer.style.display = 'block';
                document.getElementById('analysisProgressBar').style.width = '0%';
                document.getElementById('analysisProgressBar').setAttribute('aria-valuenow', 0);
                document.getElementById('analysisProgressBar').textContent = '0%';
                document.getElementById('analysisStatus').textContent = '正在分析...';
            }

            // 创建表单数据
            const formData = new FormData();
            formData.append('dimension', dimension);
            formData.append('model', model);

            // 发送AJAX请求
            fetch(`/api/novel/${novelId}/chapter/${chapterId}/analyze`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('[独立维度选择] 分析请求已发送，响应:', data);

                // 检查分析进度
                checkAnalysisProgress(novelId, chapterId);
            })
            .catch(error => {
                console.error('[独立维度选择] 分析请求失败:', error);
                alert('分析请求失败，请刷新页面重试');

                // 恢复按钮状态
                startBtn.disabled = false;
                startBtn.textContent = '开始分析';
            });
        });

        console.log('[独立维度选择] 独立维度选择对话框已添加到页面');
    }

    // 检查分析进度
    function checkAnalysisProgress(novelId, chapterId) {
        console.log(`[独立维度选择] 开始检查分析进度: 小说ID=${novelId}, 章节ID=${chapterId}`);

        const progressInterval = setInterval(function() {
            fetch(`/api/novel/${novelId}/chapter/${chapterId}/progress`)
            .then(response => response.json())
            .then(data => {
                console.log('[独立维度选择] 分析进度:', data);

                const progressBar = document.getElementById('analysisProgressBar');
                const statusText = document.getElementById('analysisStatus');

                if (progressBar && statusText) {
                    if (data.status === 'completed') {
                        // 分析完成
                        progressBar.style.width = '100%';
                        progressBar.setAttribute('aria-valuenow', 100);
                        progressBar.textContent = '100%';
                        statusText.textContent = '分析已完成';

                        // 停止检查进度
                        clearInterval(progressInterval);

                        // 刷新页面显示结果
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else if (data.status === 'processing') {
                        // 分析中
                        const progress = data.progress || 0;
                        progressBar.style.width = `${progress}%`;
                        progressBar.setAttribute('aria-valuenow', progress);
                        progressBar.textContent = `${progress}%`;
                        statusText.textContent = data.message || '正在分析...';
                    } else {
                        // 其他状态
                        statusText.textContent = data.message || '等待分析...';
                    }
                }
            })
            .catch(error => {
                console.error('[独立维度选择] 检查分析进度失败:', error);
            });
        }, 2000);
    }

    // 初始化
    function init() {
        console.log('[独立维度选择] 初始化中...');

        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(addStandaloneDimensionSelector, 1000);
            });
        } else {
            setTimeout(addStandaloneDimensionSelector, 1000);
        }

        console.log('[独立维度选择] 初始化完成');
    }

    // 初始化
    init();

    console.log('[独立维度选择] 脚本加载完成');
})();
