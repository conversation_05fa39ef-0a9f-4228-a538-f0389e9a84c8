# 手动修复v3_routes.py文件
with open('src/web/routes/v3_routes.py', 'r', encoding='utf-8') as file:
    content = file.read()

# 备份原文件
with open('src/web/routes/v3_routes.py.bak2', 'w', encoding='utf-8') as file:
    file.write(content)
print("已创建备份文件")

# 替换问题的代码块
old_code = """                    # 如果仍未找到ID，尝试从内容中提取小说名称
                    if not novel_id:
                        title_match = re.search(r"参考蓝本[:：]\s*(.*?)[\n\r]", preset.content)
                        if title_match:
                            template_name = title_match.group(1).strip()
                            logger.info(f"从内容中提取参考蓝本名称: {template_name}")
                            templates = session.query(Novel).filter(Novel.title == template_name).all()
                            for template in templates:
                                if template.novel_metadata and template.novel_metadata.get('is_template'):
                                    novel_id = template.id
                                    novel = template
                                    logger.info(f"从名称'{template_name}'找到参考蓝本ID: {novel_id}")
                                    break
                    except Exception as e:
                    logger.error(f"从内容中解析参考蓝本ID或名称失败: {str(e)}")"""

new_code = """                    # 如果仍未找到ID，尝试从内容中提取小说名称
                    if not novel_id:
                        title_match = re.search(r"参考蓝本[:：]\s*(.*?)[\n\r]", preset.content)
                        if title_match:
                            template_name = title_match.group(1).strip()
                            logger.info(f"从内容中提取参考蓝本名称: {template_name}")
                            templates = session.query(Novel).filter(Novel.title == template_name).all()
                            for template in templates:
                                if template.novel_metadata and template.novel_metadata.get('is_template'):
                                    novel_id = template.id
                                    novel = template
                                    logger.info(f"从名称'{template_name}'找到参考蓝本ID: {novel_id}")
                                    break
                except Exception as e:
                    logger.error(f"从内容中解析参考蓝本ID或名称失败: {str(e)}")"""

# 检查原代码是否存在
if old_code in content:
    # 替换代码
    new_content = content.replace(old_code, new_code)
    with open('src/web/routes/v3_routes.py', 'w', encoding='utf-8') as file:
        file.write(new_content)
    print("成功修复文件")
else:
    print("未找到需要修复的代码块")
    
    # 尝试替换更宽松的匹配
    lines = content.split('\n')
    
    # 查找可能的问题代码行
    for i, line in enumerate(lines):
        if "except Exception as e:" in line and i > 500 and i < 530:
            print(f"在第 {i+1} 行找到可能的问题: {line}")
            
        if "# 如果仍未找到ID" in line:
            print(f"在第 {i+1} 行找到: {line}")
            
    # 尝试另一种方式替换
    alternate_old = "                    except Exception as e:"
    alternate_new = "                except Exception as e:"
    
    if alternate_old in content:
        fixed_content = content.replace(alternate_old, alternate_new)
        with open('src/web/routes/v3_routes.py', 'w', encoding='utf-8') as file:
            file.write(fixed_content)
        print("使用替代方法修复文件")
    else:
        print("替代方法也无法找到问题代码") 