/**
 * 九猫 - 分析详情页面优化脚本
 * 专门解决分析详情页面加载慢和崩溃问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析详情页面优化脚本已加载');
    
    // 配置参数
    const CONFIG = {
        lazyLoadContent: true,       // 懒加载内容
        paginateResults: true,       // 分页显示结果
        optimizeCharts: true,        // 优化图表
        reduceAnimations: true,      // 减少动画
        maxContentLength: 10000,     // 最大内容长度（超过则分页）
        maxLogEntries: 100,          // 最大日志条目数
        useSimpleCharts: false,      // 使用简化图表（紧急情况）
        monitorMemory: true          // 监控内存使用
    };
    
    // 存储原始内容
    let originalContent = {
        analysisResult: null,
        analysisProcess: null,
        analysisLogs: null
    };
    
    // 存储图表实例
    let chartInstances = {};
    
    // 在页面加载完成后执行优化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始优化分析详情页面');
        
        // 检测页面类型
        if (!isAnalysisDetailPage()) {
            console.log('不是分析详情页面，跳过优化');
            return;
        }
        
        // 1. 优化内容加载
        optimizeContentLoading();
        
        // 2. 优化图表
        if (CONFIG.optimizeCharts) {
            optimizeCharts();
        }
        
        // 3. 添加内存监控
        if (CONFIG.monitorMemory) {
            setupMemoryMonitoring();
        }
        
        // 4. 修复折叠功能
        fixCollapseFunction();
    });
    
    // 检测是否是分析详情页面
    function isAnalysisDetailPage() {
        const path = window.location.pathname;
        return path.includes('/novel/') && path.includes('/analysis/');
    }
    
    // 优化内容加载
    function optimizeContentLoading() {
        try {
            // 1. 处理分析结果内容
            const resultContent = document.querySelector('.analysis-card .markdown-content');
            if (resultContent && resultContent.innerHTML.length > CONFIG.maxContentLength) {
                console.log('分析结果内容过长，进行分页处理');
                originalContent.analysisResult = resultContent.innerHTML;
                paginateContent(resultContent);
            }
            
            // 2. 处理分析过程内容
            const processContent = document.querySelector('#analysisProcessCollapse .card-body');
            if (processContent) {
                console.log('优化分析过程内容');
                originalContent.analysisProcess = processContent.innerHTML;
                
                // 默认隐藏分析过程
                const processCollapse = document.getElementById('analysisProcessCollapse');
                if (processCollapse && !processCollapse.classList.contains('collapse')) {
                    processCollapse.classList.add('collapse');
                }
                
                // 如果内容过长，进行懒加载
                if (CONFIG.lazyLoadContent && processContent.innerHTML.length > CONFIG.maxContentLength) {
                    lazyLoadContent(processContent, 'analysisProcess');
                }
            }
            
            // 3. 处理分析日志内容
            const logsContent = document.querySelector('#analysisLogsCollapse .card-body');
            if (logsContent) {
                console.log('优化分析日志内容');
                originalContent.analysisLogs = logsContent.innerHTML;
                
                // 默认隐藏分析日志
                const logsCollapse = document.getElementById('analysisLogsCollapse');
                if (logsCollapse && !logsCollapse.classList.contains('collapse')) {
                    logsCollapse.classList.add('collapse');
                }
                
                // 限制日志条目数量
                limitLogEntries(logsContent);
            }
        } catch (e) {
            console.error('优化内容加载时出错:', e);
        }
    }
    
    // 分页显示内容
    function paginateContent(container) {
        try {
            const content = container.innerHTML;
            container.innerHTML = '';
            
            // 创建分页容器
            const paginationContainer = document.createElement('div');
            paginationContainer.className = 'paginated-content';
            
            // 分割内容
            const contentChunks = splitContent(content, CONFIG.maxContentLength);
            console.log(`内容已分为 ${contentChunks.length} 页`);
            
            // 创建内容显示区域
            const contentDisplay = document.createElement('div');
            contentDisplay.className = 'paginated-display';
            contentDisplay.innerHTML = contentChunks[0] || '';
            
            // 创建分页控制
            const paginationControls = document.createElement('div');
            paginationControls.className = 'pagination-controls mt-3 d-flex justify-content-between';
            
            // 添加上一页按钮
            const prevButton = document.createElement('button');
            prevButton.className = 'btn btn-sm btn-outline-secondary';
            prevButton.innerHTML = '&laquo; 上一页';
            prevButton.disabled = true;
            
            // 添加页码显示
            const pageInfo = document.createElement('span');
            pageInfo.className = 'page-info';
            pageInfo.textContent = `1 / ${contentChunks.length}`;
            
            // 添加下一页按钮
            const nextButton = document.createElement('button');
            nextButton.className = 'btn btn-sm btn-outline-secondary';
            nextButton.innerHTML = '下一页 &raquo;';
            nextButton.disabled = contentChunks.length <= 1;
            
            // 组装分页控制
            paginationControls.appendChild(prevButton);
            paginationControls.appendChild(pageInfo);
            paginationControls.appendChild(nextButton);
            
            // 组装分页容器
            paginationContainer.appendChild(contentDisplay);
            paginationContainer.appendChild(paginationControls);
            
            // 添加到原容器
            container.appendChild(paginationContainer);
            
            // 当前页码
            let currentPage = 0;
            
            // 添加分页事件
            prevButton.addEventListener('click', function() {
                if (currentPage > 0) {
                    currentPage--;
                    contentDisplay.innerHTML = contentChunks[currentPage];
                    pageInfo.textContent = `${currentPage + 1} / ${contentChunks.length}`;
                    nextButton.disabled = false;
                    prevButton.disabled = currentPage === 0;
                }
            });
            
            nextButton.addEventListener('click', function() {
                if (currentPage < contentChunks.length - 1) {
                    currentPage++;
                    contentDisplay.innerHTML = contentChunks[currentPage];
                    pageInfo.textContent = `${currentPage + 1} / ${contentChunks.length}`;
                    prevButton.disabled = false;
                    nextButton.disabled = currentPage === contentChunks.length - 1;
                }
            });
        } catch (e) {
            console.error('分页显示内容时出错:', e);
            // 恢复原始内容
            container.innerHTML = content;
        }
    }
    
    // 分割内容为多个块
    function splitContent(content, maxLength) {
        // 如果内容不够长，直接返回
        if (content.length <= maxLength) {
            return [content];
        }
        
        const chunks = [];
        let remainingContent = content;
        
        while (remainingContent.length > 0) {
            // 找到合适的分割点
            let splitPoint = maxLength;
            if (remainingContent.length > maxLength) {
                // 尝试在段落结束处分割
                const paragraphEnd = remainingContent.lastIndexOf('</p>', maxLength);
                if (paragraphEnd > maxLength / 2) {
                    splitPoint = paragraphEnd + 4; // 包含</p>
                } else {
                    // 尝试在句子结束处分割
                    const sentenceEnd = Math.max(
                        remainingContent.lastIndexOf('. ', maxLength),
                        remainingContent.lastIndexOf('。', maxLength),
                        remainingContent.lastIndexOf('! ', maxLength),
                        remainingContent.lastIndexOf('？', maxLength)
                    );
                    
                    if (sentenceEnd > maxLength / 2) {
                        splitPoint = sentenceEnd + 1;
                    }
                }
            }
            
            // 添加当前块
            chunks.push(remainingContent.substring(0, splitPoint));
            
            // 更新剩余内容
            remainingContent = remainingContent.substring(splitPoint);
        }
        
        return chunks;
    }
    
    // 懒加载内容
    function lazyLoadContent(container, contentType) {
        try {
            // 保存原始内容
            const originalHtml = container.innerHTML;
            
            // 创建加载按钮
            const loadButton = document.createElement('button');
            loadButton.className = 'btn btn-outline-primary btn-sm mt-2';
            loadButton.textContent = '加载完整内容';
            
            // 创建占位内容
            const placeholder = document.createElement('div');
            placeholder.className = 'content-placeholder';
            placeholder.innerHTML = '<p>内容较大，点击下方按钮加载完整内容...</p>';
            
            // 清空容器并添加占位内容和按钮
            container.innerHTML = '';
            container.appendChild(placeholder);
            container.appendChild(loadButton);
            
            // 添加加载事件
            loadButton.addEventListener('click', function() {
                // 显示加载中
                placeholder.innerHTML = '<p>正在加载内容，请稍候...</p>';
                loadButton.disabled = true;
                
                // 使用setTimeout模拟异步加载，避免UI阻塞
                setTimeout(function() {
                    container.innerHTML = originalHtml;
                }, 100);
            });
        } catch (e) {
            console.error('懒加载内容时出错:', e);
            // 恢复原始内容
            container.innerHTML = originalHtml;
        }
    }
    
    // 限制日志条目数量
    function limitLogEntries(container) {
        try {
            // 查找所有日志条目
            const logEntries = container.querySelectorAll('.log-entry');
            
            // 如果条目数量超过限制
            if (logEntries.length > CONFIG.maxLogEntries) {
                console.log(`日志条目数量(${logEntries.length})超过限制(${CONFIG.maxLogEntries})，进行截断`);
                
                // 创建日志容器
                const logContainer = document.createElement('div');
                logContainer.className = 'limited-logs';
                
                // 添加最新的日志条目
                for (let i = logEntries.length - CONFIG.maxLogEntries; i < logEntries.length; i++) {
                    logContainer.appendChild(logEntries[i].cloneNode(true));
                }
                
                // 添加提示信息
                const infoMessage = document.createElement('div');
                infoMessage.className = 'alert alert-info mt-2';
                infoMessage.textContent = `仅显示最新的 ${CONFIG.maxLogEntries} 条日志（共 ${logEntries.length} 条）`;
                
                // 更新容器内容
                container.innerHTML = '';
                container.appendChild(infoMessage);
                container.appendChild(logContainer);
            }
        } catch (e) {
            console.error('限制日志条目时出错:', e);
        }
    }
    
    // 优化图表
    function optimizeCharts() {
        try {
            // 检查Chart.js是否已加载
            if (typeof Chart === 'undefined') {
                console.log('Chart.js未加载，等待加载完成后优化图表');
                
                // 等待Chart.js加载完成
                const checkInterval = setInterval(function() {
                    if (typeof Chart !== 'undefined') {
                        clearInterval(checkInterval);
                        console.log('Chart.js已加载，开始优化图表');
                        applyChartOptimizations();
                    }
                }, 500);
                
                // 设置超时
                setTimeout(function() {
                    clearInterval(checkInterval);
                    console.log('等待Chart.js加载超时，尝试使用简化图表');
                    if (CONFIG.useSimpleCharts) {
                        useSimpleCharts();
                    }
                }, 5000);
            } else {
                console.log('Chart.js已加载，直接优化图表');
                applyChartOptimizations();
            }
        } catch (e) {
            console.error('优化图表时出错:', e);
        }
    }
    
    // 应用图表优化
    function applyChartOptimizations() {
        try {
            // 备份原始Chart构造函数
            const originalChart = window.Chart;
            
            // 替换Chart构造函数
            window.Chart = function(ctx, config) {
                // 获取canvas ID
                const canvasId = ctx.canvas ? (ctx.canvas.id || 'unknown') : 'unknown';
                console.log(`创建图表: ${canvasId}`);
                
                // 优化配置
                const optimizedConfig = optimizeChartConfig(config);
                
                // 检查是否已有图表实例
                if (chartInstances[canvasId]) {
                    console.log(`销毁已有图表: ${canvasId}`);
                    chartInstances[canvasId].destroy();
                    delete chartInstances[canvasId];
                }
                
                // 创建新图表
                const chart = new originalChart(ctx, optimizedConfig);
                
                // 存储图表实例
                chartInstances[canvasId] = chart;
                
                return chart;
            };
            
            // 复制原型和静态方法
            window.Chart.prototype = originalChart.prototype;
            Object.keys(originalChart).forEach(key => {
                window.Chart[key] = originalChart[key];
            });
            
            // 查找并初始化所有图表
            const canvasElements = document.querySelectorAll('canvas');
            console.log(`找到 ${canvasElements.length} 个canvas元素`);
            
            // 如果页面上有initCharts函数，调用它
            if (typeof window.initCharts === 'function') {
                console.log('调用页面的initCharts函数');
                setTimeout(window.initCharts, 100);
            }
        } catch (e) {
            console.error('应用图表优化时出错:', e);
        }
    }
    
    // 优化图表配置
    function optimizeChartConfig(config) {
        // 创建配置副本
        const optimizedConfig = JSON.parse(JSON.stringify(config));
        
        // 减少动画
        if (CONFIG.reduceAnimations) {
            optimizedConfig.options = optimizedConfig.options || {};
            optimizedConfig.options.animation = {
                duration: 400,  // 减少动画时间
                easing: 'linear'  // 使用简单的动画函数
            };
            
            // 禁用悬停动画
            optimizedConfig.options.hover = {
                animationDuration: 0
            };
        }
        
        // 减少绘制点数
        if (optimizedConfig.data && optimizedConfig.data.datasets) {
            optimizedConfig.data.datasets.forEach(dataset => {
                // 如果数据点过多，进行抽样
                if (dataset.data && dataset.data.length > 50) {
                    dataset.data = sampleData(dataset.data, 50);
                }
            });
        }
        
        // 优化刻度
        optimizedConfig.options = optimizedConfig.options || {};
        optimizedConfig.options.scales = optimizedConfig.options.scales || {};
        
        // X轴优化
        if (optimizedConfig.options.scales.x) {
            optimizedConfig.options.scales.x.ticks = optimizedConfig.options.scales.x.ticks || {};
            optimizedConfig.options.scales.x.ticks.maxTicksLimit = 10;  // 限制刻度数量
        }
        
        // Y轴优化
        if (optimizedConfig.options.scales.y) {
            optimizedConfig.options.scales.y.ticks = optimizedConfig.options.scales.y.ticks || {};
            optimizedConfig.options.scales.y.ticks.maxTicksLimit = 8;  // 限制刻度数量
        }
        
        // 禁用图例点击
        optimizedConfig.options.legend = optimizedConfig.options.legend || {};
        optimizedConfig.options.legend.onClick = null;
        
        return optimizedConfig;
    }
    
    // 数据抽样
    function sampleData(data, maxPoints) {
        if (data.length <= maxPoints) {
            return data;
        }
        
        const result = [];
        const step = Math.floor(data.length / maxPoints);
        
        for (let i = 0; i < maxPoints; i++) {
            result.push(data[i * step]);
        }
        
        return result;
    }
    
    // 使用简化图表
    function useSimpleCharts() {
        try {
            console.log('使用简化图表替代Chart.js');
            
            // 查找所有canvas元素
            const canvasElements = document.querySelectorAll('canvas');
            
            canvasElements.forEach(canvas => {
                // 获取canvas上下文
                const ctx = canvas.getContext('2d');
                if (!ctx) return;
                
                // 清除canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制简单的占位图
                ctx.fillStyle = '#f0f0f0';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = '#888';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('图表加载失败，请刷新页面重试', canvas.width / 2, canvas.height / 2);
                
                // 添加刷新按钮
                const container = canvas.parentElement;
                if (container) {
                    const refreshButton = document.createElement('button');
                    refreshButton.className = 'btn btn-sm btn-outline-secondary mt-2';
                    refreshButton.textContent = '刷新图表';
                    refreshButton.addEventListener('click', function() {
                        location.reload();
                    });
                    
                    container.appendChild(refreshButton);
                }
            });
        } catch (e) {
            console.error('使用简化图表时出错:', e);
        }
    }
    
    // 修复折叠功能
    function fixCollapseFunction() {
        try {
            // 查找所有折叠按钮
            const collapseButtons = document.querySelectorAll('[data-bs-toggle="collapse"], [data-toggle="collapse"]');
            
            collapseButtons.forEach(button => {
                // 获取目标元素
                let targetSelector = button.getAttribute('data-bs-target') || button.getAttribute('data-target');
                if (!targetSelector) return;
                
                const targetElement = document.querySelector(targetSelector);
                if (!targetElement) return;
                
                // 确保目标元素有collapse类
                if (!targetElement.classList.contains('collapse')) {
                    targetElement.classList.add('collapse');
                }
                
                // 移除现有的点击事件
                button.removeEventListener('click', handleCollapseClick);
                
                // 添加新的点击事件
                button.addEventListener('click', handleCollapseClick);
            });
            
            // 添加展开全部和折叠全部按钮
            addExpandCollapseAllButtons();
        } catch (e) {
            console.error('修复折叠功能时出错:', e);
        }
    }
    
    // 处理折叠点击事件
    function handleCollapseClick(event) {
        event.preventDefault();
        
        // 获取目标元素
        const targetSelector = this.getAttribute('data-bs-target') || this.getAttribute('data-target');
        if (!targetSelector) return;
        
        const targetElement = document.querySelector(targetSelector);
        if (!targetElement) return;
        
        // 切换折叠状态
        targetElement.classList.toggle('show');
        
        // 更新aria-expanded属性
        this.setAttribute('aria-expanded', targetElement.classList.contains('show') ? 'true' : 'false');
    }
    
    // 添加展开全部和折叠全部按钮
    function addExpandCollapseAllButtons() {
        try {
            // 检查是否已存在按钮
            if (document.getElementById('expandAllButton')) return;
            
            // 查找合适的容器
            const container = document.querySelector('.container > .row > .col-md-8, .container > .row > .col-md-9, .container > .row > .col-md-12');
            if (!container) return;
            
            // 创建按钮组
            const buttonGroup = document.createElement('div');
            buttonGroup.className = 'btn-group mb-3';
            buttonGroup.setAttribute('role', 'group');
            
            // 创建展开全部按钮
            const expandAllButton = document.createElement('button');
            expandAllButton.id = 'expandAllButton';
            expandAllButton.className = 'btn btn-sm btn-outline-primary';
            expandAllButton.textContent = '展开全部';
            expandAllButton.addEventListener('click', function() {
                document.querySelectorAll('.collapse').forEach(element => {
                    element.classList.add('show');
                });
                
                document.querySelectorAll('[data-bs-toggle="collapse"], [data-toggle="collapse"]').forEach(button => {
                    button.setAttribute('aria-expanded', 'true');
                });
            });
            
            // 创建折叠全部按钮
            const collapseAllButton = document.createElement('button');
            collapseAllButton.id = 'collapseAllButton';
            collapseAllButton.className = 'btn btn-sm btn-outline-secondary';
            collapseAllButton.textContent = '折叠全部';
            collapseAllButton.addEventListener('click', function() {
                document.querySelectorAll('.collapse').forEach(element => {
                    element.classList.remove('show');
                });
                
                document.querySelectorAll('[data-bs-toggle="collapse"], [data-toggle="collapse"]').forEach(button => {
                    button.setAttribute('aria-expanded', 'false');
                });
            });
            
            // 组装按钮组
            buttonGroup.appendChild(expandAllButton);
            buttonGroup.appendChild(collapseAllButton);
            
            // 添加到容器
            container.insertBefore(buttonGroup, container.firstChild);
        } catch (e) {
            console.error('添加展开全部和折叠全部按钮时出错:', e);
        }
    }
    
    // 设置内存监控
    function setupMemoryMonitoring() {
        try {
            // 检查是否支持性能监控API
            if (!window.performance || !window.performance.memory) {
                console.log('浏览器不支持内存监控API');
                return;
            }
            
            // 定期检查内存使用情况
            const memoryCheckInterval = setInterval(function() {
                const memoryInfo = window.performance.memory;
                const usedHeapSize = memoryInfo.usedJSHeapSize / 1024 / 1024;
                const totalHeapSize = memoryInfo.totalJSHeapSize / 1024 / 1024;
                const heapLimit = memoryInfo.jsHeapSizeLimit / 1024 / 1024;
                
                console.log(`内存使用: ${usedHeapSize.toFixed(2)}MB / ${totalHeapSize.toFixed(2)}MB (限制: ${heapLimit.toFixed(2)}MB)`);
                
                // 如果内存使用接近限制，尝试释放内存
                if (usedHeapSize > heapLimit * 0.8) {
                    console.warn('内存使用接近限制，尝试释放内存');
                    releaseMemory();
                }
            }, 10000);  // 每10秒检查一次
            
            // 页面卸载时清除定时器
            window.addEventListener('beforeunload', function() {
                clearInterval(memoryCheckInterval);
            });
        } catch (e) {
            console.error('设置内存监控时出错:', e);
        }
    }
    
    // 释放内存
    function releaseMemory() {
        try {
            console.log('尝试释放内存');
            
            // 1. 销毁所有图表实例
            for (const id in chartInstances) {
                if (chartInstances[id]) {
                    chartInstances[id].destroy();
                    delete chartInstances[id];
                }
            }
            
            // 2. 清理不必要的DOM元素
            document.querySelectorAll('.temp-element, .cache-element').forEach(element => {
                element.parentNode.removeChild(element);
            });
            
            // 3. 清理事件监听器
            document.querySelectorAll('.dynamic-element').forEach(element => {
                element.replaceWith(element.cloneNode(true));
            });
            
            // 4. 提示垃圾回收
            if (window.gc) {
                window.gc();
            } else {
                // 间接提示垃圾回收
                const largeArray = [];
                for (let i = 0; i < 1000; i++) {
                    largeArray.push(new Array(10000).join('x'));
                }
                largeArray.length = 0;
            }
            
            console.log('内存释放完成');
        } catch (e) {
            console.error('释放内存时出错:', e);
        }
    }
})();
