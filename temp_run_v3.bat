@echo off
cd /d "E:\艹，又来一次\九猫"
echo 当前工作目录: %CD%
set DEBUG=False
set USE_REAL_API=True
set USE_MOCK_API=False
set ENABLE_MOCK_ANALYSIS=False
set HOST=127.0.0.1
set MEMORY_OPTIMIZED=True
set LOW_MEMORY_MODE=True
set MEMORY_WARNING_THRESHOLD=75
set MEMORY_CRITICAL_THRESHOLD=85
set MAX_DB_CONNECTIONS=30
set DB_POOL_SIZE=30
set DB_MAX_OVERFLOW=20
set DISABLE_CHARTS=True
set VERSION=3.0
set USE_SIMPLIFIED_MODE=False
set MEMORY_STATS_DIR=E:\艹，又来一次\九猫\memory_stats
set DISABLE_AUTO_REFRESH=True
set MEMORY_CHECK_INTERVAL=3
set OPTIMIZE_DIMENSION_DETAIL=True
set ENABLE_LOG_FILTER=True
set SEPARATE_ANALYSIS_PROCESS=True
set ENABLE_BUTTON_TEXT_SUPREME_FIX=True
set FORCE_BUTTON_TEXT_VISIBILITY=True
set DEEPSEEK_API_KEY=***********************************
set QWEN_API_KEY=sk-6f3b4c6ad9f64f78b22bed422c5d278d
set DEFAULT_MODEL=deepseek-r1
set FORCE_REANALYSIS=True
set FORCE_REAL_API=True
set RELOAD_CONFIG=True
set DEFAULT_VERSION=v3.0
set TOTAL_DIMENSIONS=15
set ENABLE_ANALYSIS_STATUS_FIX=True
set USE_V3_CONSOLE=True
set ENABLE_CHAPTER_DELETE=True
set API_ROUTE_FIX=True
set ENABLE_NOVEL_ANALYSIS_API_FIX=True
set USE_LOCAL_RESOURCES=True
set DISABLE_CDN=True
set ENABLE_RESOURCE_FIX=True
set CDN_TIMEOUT=1000
set LOAD_BOOTSTRAP_ICONS_LOCALLY=True
set ENABLE_RESULT_CACHE=True
set ENABLE_CROSS_CHAPTER_REASONING=True
set ENABLE_PARAGRAPH_REASONING=True
set CACHE_EXPIRATION_HOURS=72
set SIMILARITY_THRESHOLD=0.92
set MAX_CACHE_ENTRIES=2000
set MIN_PARAGRAPH_LENGTH=100
set MAX_PREVIOUS_CHAPTERS=3
set CROSS_CHAPTER_SIMILARITY=0.7
set MAX_WORKERS=8
set BATCH_SIZE=10
set API_ADAPTIVE_DELAY=True
set MIN_API_DELAY=0.2
set REUSE_DB_CONNECTIONS=True
set ENABLE_SERIALIZATION_BOTTLENECK_OPTIMIZER=True
set AGGRESSIVE_OPTIMIZATION=False
set OPTIMIZE_FOR_COST=False
set MIN_REASONING_SIMILARITY=0.7
if not exist logs mkdir logs
if not exist src\cache mkdir src\cache
if not exist src\cache\result_cache mkdir src\cache\result_cache
if not exist src\cache\paragraph_reasoning mkdir src\cache\paragraph_reasoning
echo 检查Python是否可用...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo Python未找到，请确保Python已安装并添加到PATH环境变量中
    pause
    exit /b 1
)
echo 启动九猫v3.0系统...
python -u -m src.web.v3_0_app
if %ERRORLEVEL% NEQ 0 (
    echo 启动失败，错误代码: %ERRORLEVEL%
    pause
)
