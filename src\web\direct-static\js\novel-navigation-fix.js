/**
 * 九猫 - 小说导航修复脚本
 * 解决小说详情页面点击功能按钮跳转到首页的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._novelNavigationFixLoaded) {
        console.log('[小说导航修复] 脚本已加载，避免重复执行');
        return;
    }

    // 标记脚本已加载
    window._novelNavigationFixLoaded = true;

    console.log('[小说导航修复] 脚本已加载 - 版本1.0.0');

    // 安全日志函数
    function safeLog(message) {
        try {
            console.log('[小说导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }

    function safeError(message) {
        try {
            console.error('[小说导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }

    // 获取当前小说ID
    function getCurrentNovelId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                safeLog('从URL获取到小说ID: ' + match[1]);
                return match[1];
            }

            // 从页面元素中获取
            const novelContainer = document.querySelector('[data-novel-id]');
            if (novelContainer && novelContainer.dataset.novelId) {
                safeLog('从data属性获取到小说ID: ' + novelContainer.dataset.novelId);
                return novelContainer.dataset.novelId;
            }

            // 从全局变量中获取
            if (window.novelId) {
                safeLog('从全局变量获取到小说ID: ' + window.novelId);
                return window.novelId;
            }

            if (window.novelIdFromTemplate) {
                safeLog('从模板变量获取到小说ID: ' + window.novelIdFromTemplate);
                return window.novelIdFromTemplate;
            }

            safeError('无法获取小说ID');
            return null;
        } catch (e) {
            safeError('获取小说ID时出错: ' + e.message);
            return null;
        }
    }

    // 修复所有导航链接
    function fixAllNavigationLinks() {
        safeLog('开始修复所有导航链接');

        // 获取当前小说ID
        const novelId = getCurrentNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法修复链接');
            return;
        }

        safeLog('当前小说ID: ' + novelId);

        // 修复章节分析按钮
        fixChapterAnalysisButtons(novelId);

        // 修复章节分析汇总按钮
        fixChapterSummaryButtons(novelId);

        // 修复其他功能按钮
        fixOtherFunctionButtons(novelId);

        safeLog('所有导航链接修复完成');
    }

    // 修复章节分析按钮
    function fixChapterAnalysisButtons(novelId) {
        const chapterButtons = document.querySelectorAll('.chapter-analysis-btn, a[href*="/chapters"], button:contains("章节分析")');
        safeLog('找到 ' + chapterButtons.length + ' 个章节分析按钮');

        chapterButtons.forEach(button => {
            // 设置正确的href
            if (button.tagName === 'A') {
                button.href = '/novel/' + novelId + '/chapters';
                safeLog('修复章节分析按钮href: ' + button.href);
            }

            // 移除所有现有的点击事件处理函数
            button.onclick = null;

            // 添加新的点击事件处理函数
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                safeLog('点击章节分析按钮，跳转到: /novel/' + novelId + '/chapters');
                window.location.href = '/novel/' + novelId + '/chapters';
                return false;
            }, true);
        });
    }

    // 修复章节分析汇总按钮
    function fixChapterSummaryButtons(novelId) {
        const summaryButtons = document.querySelectorAll('.chapter-summary-btn, a[href*="/summary"], button:contains("章节分析汇总")');
        safeLog('找到 ' + summaryButtons.length + ' 个章节分析汇总按钮');

        summaryButtons.forEach(button => {
            // 设置正确的href
            if (button.tagName === 'A') {
                button.href = '/novel/' + novelId + '/chapters/summary';
                safeLog('修复章节分析汇总按钮href: ' + button.href);
            }

            // 移除所有现有的点击事件处理函数
            button.onclick = null;

            // 添加新的点击事件处理函数
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                safeLog('点击章节分析汇总按钮，跳转到: /novel/' + novelId + '/chapters/summary');
                window.location.href = '/novel/' + novelId + '/chapters/summary';
                return false;
            }, true);
        });
    }

    // 修复其他功能按钮
    function fixOtherFunctionButtons(novelId) {
        // 修复同步到章节按钮
        const syncButton = document.getElementById('syncToChaptersBtn');
        if (syncButton) {
            syncButton.onclick = function(e) {
                e.preventDefault();
                safeLog('点击同步到章节按钮');
                
                // 发送同步请求
                fetch('/api/novel/' + novelId + '/sync_to_chapters', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    safeLog('同步结果: ' + JSON.stringify(data));
                    alert(data.success ? '同步成功！' : '同步失败: ' + data.error);
                })
                .catch(error => {
                    safeError('同步请求出错: ' + error);
                    alert('同步请求出错: ' + error);
                });
            };
        }

        // 修复汇总章节分析按钮
        const aggregateButton = document.getElementById('aggregateChaptersBtn');
        if (aggregateButton) {
            aggregateButton.onclick = function(e) {
                e.preventDefault();
                safeLog('点击汇总章节分析按钮');
                
                // 发送汇总请求
                fetch('/api/novel/' + novelId + '/aggregate_chapters', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    safeLog('汇总结果: ' + JSON.stringify(data));
                    alert(data.success ? '汇总成功！' : '汇总失败: ' + data.error);
                })
                .catch(error => {
                    safeError('汇总请求出错: ' + error);
                    alert('汇总请求出错: ' + error);
                });
            };
        }

        // 修复维度分析详情链接
        const viewButtons = document.querySelectorAll('.view-analysis-btn, .view-btn');
        viewButtons.forEach(button => {
            const dimension = button.getAttribute('data-dimension');
            if (!dimension) return;

            // 设置正确的href
            if (button.tagName === 'A') {
                button.href = '/novel/' + novelId + '/analysis/' + dimension;
                safeLog('修复维度分析详情按钮href: ' + button.href);
            }

            // 移除所有现有的点击事件处理函数
            button.onclick = null;

            // 添加新的点击事件处理函数
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                safeLog('点击维度分析详情按钮，跳转到: /novel/' + novelId + '/analysis/' + dimension);
                window.location.href = '/novel/' + novelId + '/analysis/' + dimension;
                return false;
            }, true);
        });
    }

    // 添加全局点击事件处理
    function addGlobalClickHandler() {
        safeLog('添加全局点击事件处理');

        // 获取当前小说ID
        const novelId = getCurrentNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法添加全局点击事件处理');
            return;
        }

        // 添加全局点击事件处理
        document.addEventListener('click', function(e) {
            // 向上查找最近的按钮或链接
            let target = e.target;
            while (target && target !== document) {
                // 检查是否是章节分析按钮
                if ((target.tagName === 'A' || target.tagName === 'BUTTON') &&
                    target.textContent &&
                    target.textContent.toLowerCase().includes('章节') &&
                    target.textContent.toLowerCase().includes('分析')) {
                    
                    // 阻止默认行为和事件冒泡
                    e.preventDefault();
                    e.stopPropagation();
                    
                    safeLog('通过全局处理器捕获章节分析按钮点击，跳转到: /novel/' + novelId + '/chapters');
                    window.location.href = '/novel/' + novelId + '/chapters';
                    return false;
                }
                
                // 检查是否是章节分析汇总按钮
                if ((target.tagName === 'A' || target.tagName === 'BUTTON') &&
                    target.textContent &&
                    target.textContent.toLowerCase().includes('章节') &&
                    target.textContent.toLowerCase().includes('汇总')) {
                    
                    // 阻止默认行为和事件冒泡
                    e.preventDefault();
                    e.stopPropagation();
                    
                    safeLog('通过全局处理器捕获章节分析汇总按钮点击，跳转到: /novel/' + novelId + '/chapters/summary');
                    window.location.href = '/novel/' + novelId + '/chapters/summary';
                    return false;
                }
                
                target = target.parentElement;
            }
        }, true);
    }

    // 初始化函数
    function initialize() {
        safeLog('初始化小说导航修复');
        
        // 检查是否在小说详情页
        const novelId = getCurrentNovelId();
        if (novelId) {
            safeLog('检测到小说详情页，小说ID: ' + novelId);
            
            // 修复所有导航链接
            fixAllNavigationLinks();
            
            // 添加全局点击事件处理
            addGlobalClickHandler();
        } else {
            safeLog('不是小说详情页，跳过修复');
        }
    }

    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }

    // 导出API，方便调试和手动调用
    window.novelNavigationFix = {
        initialize: initialize,
        fixAllNavigationLinks: fixAllNavigationLinks
    };
})();
