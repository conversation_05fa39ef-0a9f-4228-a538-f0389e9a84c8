﻿@echo off
chcp 65001 >nul

echo ===================================
echo   NineCats Novel Analysis System Startup Script
echo ===================================
echo.
echo Starting NineCats novel analysis system...
echo.

:: 设置工作目录为脚本所在目录
cd /d %~dp0

:: 检查Python是否安装
echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found. Please install Python and add to PATH.
    exit /b 1
)

:: 检查是否存在src/web/app.py
echo Verifying src/web/app.py exists...
if not exist src\web\app.py (
    echo [ERROR] src\web\app.py not found in this directory. Please move this script to project root.
    exit /b 1
)

:: 下载前端依赖库
echo Checking front-end dependencies...
if not exist "src\web\static\js\lib\jquery.min.js" (
    echo [INFO] Downloading front-end dependencies...
    python download_libs.py
)

:: 设置环境变量，禁用DEBUG模式，使用真实API调用
echo Setting environment variables...
set DEBUG=False
set USE_REAL_API=True
set HOST=127.0.0.1

:: 启动九猫系统
echo Launching NineCats novel analysis system...

:: 在后台启动浏览器，延迟10秒以确保后端有充分时间启动
start "" cmd /c "timeout /t 10 >nul && start http://localhost:5001"

echo [INFO] 系统启动中，请稍候...
echo [INFO] 浏览器将在后端服务启动后自动打开...
echo [INFO] 后端服务正在运行，请不要关闭此窗口！

:: 直接在当前窗口运行后端
python run.py
