{% extends "base.html" %}

{% block title %}{{ novel.title }} - {{ dimension_name }} - 九猫{% endblock %}

{% block extra_css %}
<style>
    .analysis-content {
        white-space: pre-wrap;
        line-height: 1.6;
    }

    .metadata-item {
        margin-bottom: 0.5rem;
    }

    /* 推理过程样式 */
    .reasoning-text {
        white-space: pre-wrap;
        line-height: 1.6;
        font-family: "Courier New", monospace;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        border-left: 4px solid #6c757d;
        font-size: 0.9rem;
        color: #333;
    }

    /* 分析结果与推理过程的视觉区分 */
    .analysis-content {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #dee2e6;
    }

    /* 分析结果标题 */
    .analysis-result-header {
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #007bff;
    }

    .visualization-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }

    .refresh-status-container {
        position: fixed;
        bottom: 10px;
        right: 10px;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 9999;
        display: flex;
        align-items: center;
    }
</style>
<!-- 添加刷新阻止器脚本 -->
<script src="{{ url_for('static', filename='js/stop-refresh.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/stop-refresh.js';"></script>
<!-- 添加API修复脚本 -->
<script src="{{ url_for('static', filename='js/novel-api-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-api-fix.js';"></script>
<!-- 添加维度分析通用修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-analysis-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-analysis-fix.js';"></script>
{% endblock %}

{% block head %}
{{ super() }}
<!-- 推理过程样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/reasoning-content.css') }}" onerror="this.onerror=null;this.href='/direct-static/css/reasoning-content.css';">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <!-- 分析进度条 -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">分析进度</h5>
                    <div id="analysisStatus" class="badge badge-info px-3 py-2">加载中...</div>
                </div>
            </div>
            <div class="card-body">
                <div class="progress mb-3" style="height: 20px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>当前维度:</strong> <span id="currentDimension">{{ result.dimension }}</span></p>
                        <p><strong>分块进度:</strong> <span id="blocksProgress">-</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>预计剩余时间:</strong> <span id="remainingTime">-</span></p>
                        <p><strong>预计完成时间:</strong> <span id="estimatedCompletionTime">-</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="card mb-4">
            <div class="card-header"><h5 class="card-title">分析日志</h5></div>
            <div class="card-body" id="logContainer" data-novel-id="{{ novel.id }}" style="background:#f8f9fa; height:200px; overflow-y:auto; font-family: monospace; font-size:0.9rem;"></div>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
                <li class="breadcrumb-item active">{{ dimension_name }}</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ dimension_name }}</h1>
            <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
                返回小说页面
            </a>
        </div>

        <div class="row mt-4">
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析结果</h5>
                    </div>
                    <div class="card-body">
                        <!-- 先显示推理过程 -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-brain mr-2"></i>AI推理过程
                                    <small class="text-white-50 ml-2">(AI思考的原始记录)</small>
                                </h5>
                                <button class="btn btn-sm btn-light" id="toggleReasoningBtn">
                                    <i class="fas fa-expand-alt mr-1"></i>展开/收起
                                </button>
                            </div>
                            <div class="card-body bg-light">
                                <!-- 折叠的推理过程预览 -->
                                <div id="reasoningContentCollapsed">
                                    <div class="alert alert-info mb-3">
                                        <p><i class="fas fa-info-circle"></i> <strong>提示：</strong> 推理过程是AI分析文本时的思考过程，通常使用第一人称表述，包含思考方法和计划。</p>
                                        <p class="small mb-0">与分析结果不同，推理过程展示了AI是如何一步步形成分析的，更像是工作笔记而非最终报告。点击上方的"展开/收起"按钮查看完整内容。</p>
                                    </div>
                                </div>
                                
                                <!-- 完整的推理过程内容 -->
                                <div id="reasoningContentFull" style="display: none;">
                                    <div class="reasoning-content" id="reasoningContent" data-reasoning-container="true" data-novel-id="{{ novel.id }}" data-dimension="{{ dimension }}">
                                        <div class="text-center my-3">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="sr-only">加载中...</span>
                                            </div>
                                            <p class="mt-2">正在加载推理过程，请稍候...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 然后显示分析结果 -->
                        <div class="card mt-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-line mr-2"></i>分析结果
                                    <small class="text-white-50 ml-2">(AI的最终分析报告)</small>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="analysis-content markdown-content">
                                    {{ result.content|safe }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">小说信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>标题：</strong> {{ novel.title }}</p>
                        <p><strong>作者：</strong> {{ novel.author or '未知' }}</p>
                        <p><strong>字数：</strong> {{ novel.word_count }}</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析元数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="metadata-item">
                            <strong>分析时间：</strong>
                            <span>{{ result.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </div>

                        {% if result.metadata is mapping and result.metadata.get('processing_time') %}
                            <div class="metadata-item">
                                <strong>处理时间：</strong>
                                <span>{{ "%.2f"|format(result.metadata.processing_time) }} 秒</span>
                            </div>
                        {% endif %}

                        {% if result.metadata is mapping and result.metadata.get('chunk_count') %}
                            <div class="metadata-item">
                                <strong>分析块数：</strong>
                                <span>{{ result.metadata.chunk_count }}</span>
                            </div>
                        {% endif %}

                        {% if result.updated_at and result.updated_at != result.created_at %}
                            <div class="metadata-item">
                                <strong>最后更新：</strong>
                                <span>{{ result.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入刷新控制模块 -->
<script src="{{ url_for('static', filename='js/refresh-control.js') }}"></script>
<!-- 引入推理过程加载器 -->
<script src="{{ url_for('static', filename='js/reasoning-content-loader.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-loader.js';"></script>
<script>
    // 全局变量
    const novelId = "{{ novel.id }}";
    const dimension = "{{ result.dimension }}";

    // 错误处理函数，防止JS错误导致整个页面失效
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
        return true; // 阻止默认错误处理
    };

    // 推理过程展开/收起功能
    document.addEventListener('DOMContentLoaded', function() {
        const toggleReasoningBtn = document.getElementById('toggleReasoningBtn');
        const reasoningContentCollapsed = document.getElementById('reasoningContentCollapsed');
        const reasoningContentFull = document.getElementById('reasoningContentFull');

        if (toggleReasoningBtn && reasoningContentCollapsed && reasoningContentFull) {
            // 使用推理过程加载器初始化
            if (window.reasoningContentLoader && window.reasoningContentLoader.initReasoningContent) {
                console.log('使用推理过程加载器初始化推理过程功能');
                window.reasoningContentLoader.initReasoningContent(novelId, dimension);
            } else {
                console.log('推理过程加载器未找到，使用内置功能');
                // 保留原始点击事件处理
                toggleReasoningBtn.addEventListener('click', function() {
                    if (reasoningContentFull.style.display === 'none') {
                        reasoningContentCollapsed.style.display = 'none';
                        reasoningContentFull.style.display = 'block';
                        toggleReasoningBtn.innerHTML = '<i class="fas fa-compress-alt mr-1"></i>收起';
                    } else {
                        reasoningContentCollapsed.style.display = 'block';
                        reasoningContentFull.style.display = 'none';
                        toggleReasoningBtn.innerHTML = '<i class="fas fa-expand-alt mr-1"></i>展开/收起';
                    }
                });
                
                // 手动加载推理过程，确保请求完整内容
                const reasoningContent = document.getElementById('reasoningContent');
                if (reasoningContent) {
                    fetch(`/api/novel/${novelId}/analysis/${dimension}/reasoning_content?full=true`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.reasoning_content) {
                                // 直接显示完整的推理过程
                                const content = data.reasoning_content
                                    .replace(/&/g, "&amp;")
                                    .replace(/</g, "&lt;")
                                    .replace(/>/g, "&gt;")
                                    .replace(/"/g, "&quot;")
                                    .replace(/'/g, "&#039;");
                                reasoningContent.innerHTML = `<pre class="reasoning-text">${content}</pre>`;
                                console.log('手动加载推理过程成功，来源:', data.source || '未知');
                            } else {
                                reasoningContent.innerHTML = `
                                    <div class="alert alert-info">
                                        <p><i class="fas fa-info-circle"></i> 暂无推理过程数据</p>
                                    </div>
                                `;
                            }
                        })
                        .catch(error => {
                            console.error('获取推理过程时出错:', error);
                            reasoningContent.innerHTML = `
                                <div class="alert alert-warning">
                                    <p><i class="fas fa-exclamation-circle"></i> 加载推理过程时出错</p>
                                </div>
                            `;
                        });
                }
            }
        }
    });

    // 实时进度更新函数
    function fetchAnalysisProgress() {
        // 添加随机参数防止缓存
        const url = `/api/analysis/progress?novel_id=${novelId}&dimension=${dimension}&_=${Date.now()}`;

        return fetch(url)
            .then(res => {
                if (!res.ok) {
                    throw new Error(`HTTP错误: ${res.status}`);
                }
                return res.json();
            })
            .then(data => {
                if (data.success && data.progress) {
                    // 添加额外检查，确保dimension存在，如果不存在则返回默认值
                    if (!data.progress[dimension]) {
                        console.warn(`维度 ${dimension} 的进度数据不存在，使用默认值`);
                        return {
                            progress: {
                                progress: 100,
                                status: '完成',
                                blocks_progress: '1/1',
                                remaining_time: '0秒',
                                eta: '已完成'
                            },
                            isRunning: false
                        };
                    }
                    return {
                        progress: data.progress[dimension],
                        isRunning: data.is_running
                    };
                }
                // 如果没有成功获取数据，返回默认值
                console.warn('无法获取进度数据，使用默认值');
                return {
                    progress: {
                        progress: 100,
                        status: '完成',
                        blocks_progress: '1/1',
                        remaining_time: '0秒',
                        eta: '已完成'
                    },
                    isRunning: false
                };
            })
            .catch(err => {
                console.error('获取分析进度失败:', err);
                // 返回一个默认值，避免错误传播
                return {
                    progress: {
                        progress: 100,
                        status: '完成',
                        blocks_progress: '1/1',
                        remaining_time: '0秒',
                        eta: '已完成'
                    },
                    isRunning: false
                };
            });
    }

    function updateProgressUI() {
        fetchAnalysisProgress()
            .then(data => {
                // 确保数据存在
                const progressData = data.progress || {
                    progress: 100,
                    status: '完成',
                    blocks_progress: '1/1',
                    remaining_time: '0秒',
                    eta: '已完成'
                };
                const isRunning = data.isRunning || false;

                // 更新进度条
                const progress = progressData.progress || 0;
                const progressBar = document.getElementById('progressBar');
                if (!progressBar) {
                    console.warn('找不到进度条元素');
                    return;
                }

                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                progressBar.textContent = `${progress}%`;

                // 更新分析状态
                const statusElement = document.getElementById('analysisStatus');
                if (!statusElement) {
                    console.warn('找不到状态元素');
                    return;
                }

                if (progress === 100) {
                    statusElement.className = 'badge badge-success px-3 py-2';
                    statusElement.textContent = '已完成';
                    progressBar.className = 'progress-bar bg-success';
                    console.log('分析已完成，进度UI更新');
                } else if (progress < 0) {
                    statusElement.className = 'badge badge-danger px-3 py-2';
                    statusElement.textContent = '已终止';
                    progressBar.className = 'progress-bar bg-danger';
                } else if (isRunning) {
                    statusElement.className = 'badge badge-primary px-3 py-2';
                    statusElement.textContent = '分析中';
                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
                    console.log('分析进行中，进度UI更新');
                } else {
                    statusElement.className = 'badge badge-warning px-3 py-2';
                    statusElement.textContent = '已暂停';
                    progressBar.className = 'progress-bar bg-warning';
                }

                // 更新块进度
                const blocksProgressElem = document.getElementById('blocksProgress');
                if (blocksProgressElem && progressData.blocks_progress) {
                    blocksProgressElem.textContent = progressData.blocks_progress;
                } else if (blocksProgressElem) {
                    blocksProgressElem.textContent = '1/1';
                }

                // 更新时间信息
                const remainingTimeElem = document.getElementById('remainingTime');
                if (remainingTimeElem && progressData.remaining_time) {
                    remainingTimeElem.textContent = progressData.remaining_time;
                } else if (remainingTimeElem) {
                    remainingTimeElem.textContent = '0秒';
                }

                const etaElem = document.getElementById('estimatedCompletionTime');
                if (etaElem && progressData.eta) {
                    etaElem.textContent = progressData.eta;
                } else if (etaElem) {
                    etaElem.textContent = '已完成';
                }

                // 如果分析仍在进行中且进度小于100%，每5秒更新一次
                if (isRunning && progress < 100 && progress >= 0) {
                    setTimeout(updateProgressUI, 5000);
                    console.log('分析进行中，下次更新进度UI在5秒后');
                } else {
                    console.log('进度UI已更新，不再自动刷新');
                }
            })
            .catch(err => {
                console.error('更新进度UI时发生错误:', err);
                // 出错时设置为默认值
                const progressBar = document.getElementById('progressBar');
                if (progressBar) {
                    progressBar.style.width = '100%';
                    progressBar.setAttribute('aria-valuenow', 100);
                    progressBar.textContent = '100%';
                    progressBar.className = 'progress-bar bg-success';
                }

                const statusElement = document.getElementById('analysisStatus');
                if (statusElement) {
                    statusElement.className = 'badge badge-success px-3 py-2';
                    statusElement.textContent = '已完成';
                }

                const blocksProgressElem = document.getElementById('blocksProgress');
                if (blocksProgressElem) {
                    blocksProgressElem.textContent = '1/1';
                }

                const remainingTimeElem = document.getElementById('remainingTime');
                if (remainingTimeElem) {
                    remainingTimeElem.textContent = '0秒';
                }

                const etaElem = document.getElementById('estimatedCompletionTime');
                if (etaElem) {
                    etaElem.textContent = '已完成';
                }

                // 即使出错也继续尝试轮询，但间隔时间更长
                setTimeout(updateProgressUI, 10000);
            });
    }

    // 实时日志拉取函数
    function fetchLogs(since) {
        const logContainer = document.getElementById('logContainer');
        const novelId = logContainer.dataset.novelId;
        let url = `/api/analysis/logs?novel_id=${novelId}&level=all&limit=200&dimension=${dimension}`;
        if (since) url += `&since=${since}`;
        return fetch(url).then(res=>res.json());
    }

    function initLogControls() {
        const logContainer = document.getElementById('logContainer');
        let lastTs = '';

        // 添加控制按钮
        const controlDiv = document.createElement('div');
        controlDiv.className = 'mb-2 d-flex justify-content-between';
        controlDiv.innerHTML = `
            <div>
                <button id="refreshLogsBtn" class="btn btn-sm btn-outline-primary">刷新日志</button>
                <button id="clearLogsBtn" class="btn btn-sm btn-outline-secondary ml-2">清空</button>
            </div>
            <div>
                <label class="mr-2"><input type="checkbox" id="autoScrollCheck" checked> 自动滚动</label>
                <select id="logLevelFilter" class="form-control form-control-sm d-inline-block" style="width:auto">
                    <option value="all">所有级别</option>
                    <option value="info">信息</option>
                    <option value="warning">警告</option>
                    <option value="error">错误</option>
                </select>
            </div>
        `;
        logContainer.parentNode.insertBefore(controlDiv, logContainer);

        // 控制事件处理
        document.getElementById('refreshLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '<div class="text-center text-muted my-3">正在加载日志...</div>';
            lastTs = '';
            updateLogs(true);
        });

        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '';
        });

        const autoScrollCheck = document.getElementById('autoScrollCheck');

        // 日志级别过滤
        const logLevelFilter = document.getElementById('logLevelFilter');
        logLevelFilter.addEventListener('change', () => {
            const level = logLevelFilter.value;
            Array.from(logContainer.children).forEach(line => {
                if (level === 'all') {
                    line.style.display = '';
                } else {
                    const logText = line.textContent;
                    line.style.display = logText.includes(`${level.toUpperCase()}`) ? '' : 'none';
                }
            });
        });

        function updateLogs(force = false) {
            fetchLogs(lastTs).then(data=>{
                if (data.success) {
                    // 如果是首次加载且没有日志，显示提示
                    if (lastTs === '' && data.logs.length === 0) {
                        logContainer.innerHTML = '<div class="text-center text-muted my-3">暂无分析日志，可能分析已完成或尚未开始</div>';
                    }

                    // 显示新日志
                    if (data.logs.length > 0) {
                        if (logContainer.children.length === 1 && logContainer.children[0].classList.contains('text-muted')) {
                            logContainer.innerHTML = '';
                        }

                        data.logs.forEach(log => {
                            const line = document.createElement('div');
                            line.className = log.level === 'error' ? 'text-danger' :
                                            log.level === 'warning' ? 'text-warning' : '';

                            // 增加分析进度信息的突出显示
                            let message = log.message || '';
                            if ((log.important) ||
                                (message && (
                                    message.includes('进度更新') ||
                                    message.includes('分析块') ||
                                    message.includes('API调用完成') ||
                                    message.includes('%') ||
                                    message.includes('处理时间') ||
                                    message.includes('令牌使用量') ||
                                    message.includes('费用') ||
                                    message.includes('分析结果') ||
                                    message.includes('完成度') ||
                                    message.includes('耗时')
                                ))) {
                                line.className += ' font-weight-bold text-primary';
                            }

                            line.textContent = `[${log.timestamp}] ${log.level.toUpperCase()} - ${message}`;

                            // 应用日志级别过滤
                            const level = logLevelFilter.value;
                            if (level !== 'all' && !log.level.includes(level)) {
                                line.style.display = 'none';
                            }

                            logContainer.appendChild(line);
                            lastTs = log.timestamp;
                        });

                        // 自动滚动到底部
                        if (autoScrollCheck.checked) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
                    }

                    // 判断是否需要继续轮询
                    if (data.is_running || force) {
                        setTimeout(updateLogs, 3000);  // 增加轮询间隔到3秒，减轻服务器负担
                    }
                }
            }).catch(err=>{
                console.error('获取日志失败', err);
                logContainer.innerHTML += `<div class="text-danger">获取日志失败: ${err.message}</div>`;
                setTimeout(updateLogs, 5000);  // 发生错误时延长重试时间
            });
        }

        // 启动日志更新
        updateLogs();
    }

    // 加载Chart.js库
    function loadChartJS() {
        return new Promise((resolve, reject) => {
            // 检查Chart是否已经加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载');
                resolve();
                return;
            }

            // 尝试加载本地Chart.js
            var script = document.createElement('script');
            script.src = "{{ url_for('static', filename='js/lib/chart.min.js') }}";

            script.onload = function() {
                console.log('成功从本地加载Chart.js');
                resolve();
            };

            script.onerror = function() {
                // 如果本地加载失败，尝试备用路径
                console.warn('本地Chart.js加载失败，尝试备用路径');
                var backupScript = document.createElement('script');
                backupScript.src = "/direct-static/js/lib/chart.min.js";

                backupScript.onload = function() {
                    console.log('成功从备用路径加载Chart.js');
                    resolve();
                };

                backupScript.onerror = function() {
                    // 如果备用路径也失败，尝试CDN
                    console.warn('备用路径Chart.js加载失败，尝试CDN');
                    var cdnScript = document.createElement('script');
                    cdnScript.src = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";

                    cdnScript.onload = function() {
                        console.log('成功从CDN加载Chart.js');
                        resolve();
                    };

                    cdnScript.onerror = function() {
                        console.error('所有Chart.js加载尝试均失败');
                        reject(new Error('无法加载Chart.js'));
                    };

                    document.head.appendChild(cdnScript);
                };

                document.head.appendChild(backupScript);
            };

            document.head.appendChild(script);
        });
    }

    // 初始化图表
    function initCharts() {
        // 元数据已禁用以避免序列化错误
        let resultMetadata = {};
        console.log('分析结果元数据已禁用以避免序列化错误');

        // 确保元数据是有效的对象
        if (!resultMetadata || typeof resultMetadata !== 'object') {
            console.warn('元数据不是有效的对象，尝试解析');
            try {
                if (typeof resultMetadata === 'string') {
                    resultMetadata = JSON.parse(resultMetadata);
                } else {
                    resultMetadata = {};
                }
            } catch (e) {
                console.error('解析元数据失败:', e);
                resultMetadata = {};
            }
        }

        // 从分析结果中提取数据 - 雷达图
        // 使用API统计数据构建雷达图，确保数据是真实的
        let radarLabels = ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'];
        let radarData = [];

        // 从元数据中获取真实数据
        if (resultMetadata && resultMetadata.processing_time) {
            radarData.push(Math.round(resultMetadata.processing_time * 100) / 100);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.chunk_count) {
            radarData.push(resultMetadata.chunk_count);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.api_calls) {
            radarData.push(resultMetadata.api_calls);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.tokens_used) {
            radarData.push(resultMetadata.tokens_used);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.cost) {
            radarData.push(Math.round(resultMetadata.cost * 10000) / 10000);
        } else {
            radarData.push(0);
        }

        // 如果有可视化数据，使用它
        if (resultMetadata && typeof resultMetadata === 'object' &&
            resultMetadata.visualization_data &&
            resultMetadata.visualization_data.radar) {
            const visualData = resultMetadata.visualization_data.radar;
            if (visualData.labels && Array.isArray(visualData.labels) && visualData.labels.length > 0) {
                radarLabels = visualData.labels;
            }
            if (visualData.data && Array.isArray(visualData.data) && visualData.data.length > 0) {
                radarData = visualData.data;
            }
            console.log('使用自定义雷达图数据:', radarLabels, radarData);
        } else {
            console.log('使用从元数据构建的雷达图数据:', radarLabels, radarData);
        }

        // 从分析结果中提取数据 - 柱状图
        // 构建更有意义的柱状图数据
        let barLabels = radarLabels; // 使用相同的标签
        let barData = radarData;     // 使用相同的数据

        // 如果有可视化数据，使用它
        if (resultMetadata && typeof resultMetadata === 'object' &&
            resultMetadata.visualization_data &&
            resultMetadata.visualization_data.bar) {
            const visualData = resultMetadata.visualization_data.bar;
            if (visualData.labels && Array.isArray(visualData.labels) && visualData.labels.length > 0) {
                barLabels = visualData.labels;
            }
            if (visualData.data && Array.isArray(visualData.data) && visualData.data.length > 0) {
                barData = visualData.data;
            }
            console.log('使用自定义柱状图数据:', barLabels, barData);
        } else {
            console.log('使用从元数据构建的柱状图数据:', barLabels, barData);
        }

        // 加载Chart.js并初始化图表
        loadChartJS().then(() => {
            // 初始化雷达图
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: radarLabels,
                    datasets: [{
                        label: '{{ result.dimension }}分析评分',
                        data: radarData,
                        fill: true,
                        backgroundColor: 'rgba(74, 107, 223, 0.2)',
                        borderColor: 'rgb(74, 107, 223)',
                        pointBackgroundColor: 'rgb(74, 107, 223)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(74, 107, 223)'
                    }]
                },
                options: {
                    elements: {
                        line: {
                            borderWidth: 3
                        }
                    },
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 初始化柱状图
            const barCtx = document.getElementById('barChart').getContext('2d');

            // 确保数据有效
            const safeBarLabels = Array.isArray(barLabels) ? barLabels : [];
            const safeBarData = Array.isArray(barData) ? barData : [];

            // 创建柱状图
            new Chart(barCtx, {
                type: 'bar',
                data: {
                    labels: safeBarLabels,
                    datasets: [{
                        label: '{{ result.dimension }}分析指标',
                        data: safeBarData,
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(255, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(255, 206, 86, 0.2)',
                            'rgba(153, 102, 255, 0.2)',
                            'rgba(255, 159, 64, 0.2)'
                        ],
                        borderColor: [
                            'rgba(75, 192, 192, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            console.log('图表初始化完成');
        }).catch(error => {
            console.error('图表初始化失败:', error);
            // 显示错误信息给用户
            document.querySelectorAll('.chart-container').forEach(container => {
                container.innerHTML = '<div class="alert alert-danger">图表加载失败，请刷新页面重试</div>';
            });
        });
    }

    // 主初始化函数
    document.addEventListener('DOMContentLoaded', function() {
        // 禁用所有可能的自动刷新
        const disableAllAutoRefresh = function() {
            // 清除所有可能的定时器
            for (let i = 1; i < 10000; i++) {
                try {
                    clearTimeout(i);
                    clearInterval(i);
                } catch (e) {
                    // 忽略错误
                }
            }

            // 禁用页面的自动刷新元标记
            const metaTags = document.getElementsByTagName('meta');
            for (let i = 0; i < metaTags.length; i++) {
                if (metaTags[i].httpEquiv === 'refresh') {
                    metaTags[i].parentNode.removeChild(metaTags[i]);
                }
            }

            console.log('[分析页面] 已禁用所有自动刷新机制');
        };

        // 立即禁用所有自动刷新
        disableAllAutoRefresh();

        // 一次性更新进度UI
        updateProgressUI();

        // 初始化日志控制
        initLogControls();

        // 初始化图表
        initCharts();

        // 初始化刷新控制器（仅提供手动刷新功能）
        if (window.RefreshController) {
            console.log('[分析页面] 初始化刷新控制器（仅手动刷新）');
            window.RefreshController.init({
                novelId: novelId,
                dimension: dimension,
                showRefreshNotice: true,
                enableAutoRefresh: false, // 强制禁用自动刷新
                checkAnalysisStatus: false, // 不检查分析状态
                refreshStatusPosition: 'bottom-right',
                debugMode: true // 启用调试模式
            });
        }

        // 再次禁用所有自动刷新（确保没有其他代码设置了定时器）
        setTimeout(disableAllAutoRefresh, 1000);
    });
</script>
{% endblock %}
