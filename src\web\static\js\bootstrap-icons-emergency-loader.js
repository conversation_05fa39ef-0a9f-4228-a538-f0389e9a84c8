/**
 * 九猫系统 - Bootstrap Icons 应急加载器
 * 版本: 1.0.0
 * 
 * 此脚本用于在所有其他方法都失败时加载Bootstrap Icons
 */

(function() {
    console.log('[九猫修复] Bootstrap Icons 应急加载器已加载');
    
    // 检查是否已应用此修复
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded && window.__nineCatsFixes.loaded['bootstrap-icons-emergency-loader']) {
        console.log('[九猫修复] Bootstrap Icons 应急加载器已经应用，跳过');
        return;
    }
    
    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {} };
    
    // 检查Bootstrap Icons是否已加载
    function checkBootstrapIcons() {
        let hasBootstrapIcons = false;
        document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            if (link.href && link.href.includes('bootstrap-icons') && link.sheet) {
                hasBootstrapIcons = true;
            }
        });
        
        return hasBootstrapIcons;
    }
    
    // 加载内联Bootstrap Icons样式
    function loadInlineBootstrapIcons() {
        if (checkBootstrapIcons()) {
            console.log('[九猫修复] Bootstrap Icons已加载，无需应急加载');
            return;
        }
        
        console.log('[九猫修复] 应用Bootstrap Icons应急样式');
        
        // 创建样式元素
        const style = document.createElement('style');
        style.textContent = `
            /* 应急Bootstrap Icons样式 */
            .bi::before,
            [class^="bi-"]::before,
            [class*=" bi-"]::before {
                display: inline-block;
                font-family: sans-serif;
                font-style: normal;
                font-weight: normal;
                font-variant: normal;
                text-transform: none;
                line-height: 1;
                vertical-align: -0.125em;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
            
            /* 基本图标 */
            .bi-arrow-up::before { content: "↑"; }
            .bi-arrow-down::before { content: "↓"; }
            .bi-arrow-left::before { content: "←"; }
            .bi-arrow-right::before { content: "→"; }
            .bi-check::before { content: "✓"; }
            .bi-x::before { content: "✕"; }
            .bi-plus::before { content: "+"; }
            .bi-dash::before { content: "-"; }
            .bi-exclamation::before { content: "!"; }
            .bi-info::before { content: "i"; }
            .bi-question::before { content: "?"; }
            .bi-search::before { content: "🔍"; }
            .bi-eye::before { content: "👁"; }
            .bi-person::before { content: "👤"; }
            .bi-house::before { content: "🏠"; }
            .bi-gear::before { content: "⚙"; }
            .bi-file::before { content: "📄"; }
            .bi-folder::before { content: "📁"; }
            .bi-bookmark::before { content: "🔖"; }
            .bi-heart::before { content: "❤"; }
            .bi-star::before { content: "★"; }
            .bi-trash::before { content: "🗑"; }
            .bi-pencil::before { content: "✏"; }
            .bi-chat::before { content: "💬"; }
            .bi-bell::before { content: "🔔"; }
            .bi-envelope::before { content: "✉"; }
            .bi-calendar::before { content: "📅"; }
            .bi-clock::before { content: "🕒"; }
            .bi-image::before { content: "🖼"; }
            .bi-camera::before { content: "📷"; }
            .bi-play::before { content: "▶"; }
            .bi-pause::before { content: "⏸"; }
            .bi-list::before { content: "☰"; }
            .bi-grid::before { content: "⊞"; }
            .bi-filter::before { content: "⚙"; }
            .bi-three-dots::before { content: "⋯"; }
            .bi-arrow-clockwise::before { content: "↻"; }
            .bi-arrow-counterclockwise::before { content: "↺"; }
            .bi-download::before { content: "⬇"; }
            .bi-upload::before { content: "⬆"; }
            .bi-link::before { content: "🔗"; }
            .bi-emoji-smile::before { content: "😊"; }
            .bi-hand-thumbs-up::before { content: "👍"; }
            .bi-hand-thumbs-down::before { content: "👎"; }
            .bi-share::before { content: "↗"; }
            .bi-printer::before { content: "🖨"; }
            .bi-lock::before { content: "🔒"; }
            .bi-unlock::before { content: "🔓"; }
            .bi-shield::before { content: "🛡"; }
            .bi-flag::before { content: "🚩"; }
            
            /* 填充变体 */
            .bi-check-circle-fill::before { content: "✓"; }
            .bi-x-circle-fill::before { content: "✕"; }
            .bi-exclamation-circle-fill::before { content: "!"; }
            .bi-info-circle-fill::before { content: "i"; }
            .bi-question-circle-fill::before { content: "?"; }
            .bi-eye-fill::before { content: "👁"; }
            .bi-person-fill::before { content: "👤"; }
            .bi-house-fill::before { content: "🏠"; }
            .bi-gear-fill::before { content: "⚙"; }
            .bi-file-fill::before { content: "📄"; }
            .bi-folder-fill::before { content: "📁"; }
            .bi-bookmark-fill::before { content: "🔖"; }
            .bi-heart-fill::before { content: "❤"; }
            .bi-star-fill::before { content: "★"; }
            .bi-trash-fill::before { content: "🗑"; }
            .bi-pencil-fill::before { content: "✏"; }
            .bi-chat-fill::before { content: "💬"; }
            .bi-bell-fill::before { content: "🔔"; }
            .bi-envelope-fill::before { content: "✉"; }
            .bi-calendar-fill::before { content: "📅"; }
            .bi-clock-fill::before { content: "🕒"; }
            .bi-image-fill::before { content: "🖼"; }
            .bi-camera-fill::before { content: "📷"; }
            .bi-play-fill::before { content: "▶"; }
            .bi-pause-fill::before { content: "⏸"; }
        `;
        
        document.head.appendChild(style);
        console.log('[九猫修复] Bootstrap Icons应急样式已应用');
    }
    
    // 尝试加载本地Bootstrap Icons
    function tryLoadLocalBootstrapIcons() {
        if (checkBootstrapIcons()) {
            console.log('[九猫修复] Bootstrap Icons已加载，无需本地加载');
            return;
        }
        
        console.log('[九猫修复] 尝试加载本地Bootstrap Icons');
        
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = '/static/css/bootstrap-icons.css';
        
        link.addEventListener('load', function() {
            console.log('[九猫修复] 本地Bootstrap Icons加载成功');
        });
        
        link.addEventListener('error', function() {
            console.error('[九猫修复] 本地Bootstrap Icons加载失败，应用应急样式');
            loadInlineBootstrapIcons();
        });
        
        document.head.appendChild(link);
    }
    
    // 延迟执行，确保在其他资源加载器之后运行
    setTimeout(function() {
        if (!checkBootstrapIcons()) {
            console.log('[九猫修复] Bootstrap Icons未加载，尝试加载');
            tryLoadLocalBootstrapIcons();
        }
    }, 1000);
    
    // 页面加载完成后再次检查
    window.addEventListener('load', function() {
        setTimeout(function() {
            if (!checkBootstrapIcons()) {
                console.log('[九猫修复] 页面加载完成后Bootstrap Icons仍未加载，应用应急样式');
                loadInlineBootstrapIcons();
            }
        }, 2000);
    });
    
    // 标记修复已加载
    window.__nineCatsFixes.loaded['bootstrap-icons-emergency-loader'] = true;
    
    console.log('[九猫修复] Bootstrap Icons 应急加载器加载完成');
})();
