#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第一批次基础框架构建失败问题的修复效果
验证批次内容验证逻辑的改进
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.test_service import TestService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_batch_content_validation_improvements():
    """测试批次内容验证的改进"""
    print("=== 测试批次内容验证改进 ===")
    
    test_cases = [
        {
            "name": "短但有效的第一批次内容",
            "content": """第1章 新的开始
            
林小雨站在窗前，看着外面的雨滴。今天是她来到这个城市的第一天。

"你好，我是林小雨。"她对着镜子练习着自我介绍。

手机响了，是妈妈打来的。

"小雨，到了吗？"妈妈问道。

"刚到，还在适应中。"林小雨回答。""",
            "batch_number": 1,
            "expected": True,
            "description": "长度约300字符，包含基本故事元素"
        },
        {
            "name": "无标准标题但有内容的第一批次",
            "content": """新的开始

林小雨站在窗前，看着外面的雨滴打在玻璃上。今天是她来到这个城市的第一天，心情既紧张又兴奋。

她深吸一口气，准备迎接新的挑战。这是她人生的新篇章，她要好好把握。

走出房间，林小雨决定先熟悉一下周围的环境。楼下有一家咖啡店，香味飘到了楼上。

"也许可以去那里坐坐。"她想着，拿起钥匙走向门口。""",
            "batch_number": 1,
            "expected": True,
            "description": "无标准章节标题，但有替代标题和故事内容"
        },
        {
            "name": "21字符无效内容",
            "content": "API返回的数据格式不正确",
            "batch_number": 1,
            "expected": False,
            "description": "典型的21字符错误信息"
        },
        {
            "name": "提示词内容误判测试",
            "content": """# 第1章 测试章节

这是一个测试章节的内容。林小雨走在路上，想着今天的计划。

"我需要去买些东西。"她自言自语道。

突然，她看到了一家新开的书店。""",
            "batch_number": 1,
            "expected": True,
            "description": "包含写作特征，不应被误判为提示词"
        },
        {
            "name": "真正的提示词内容",
            "content": """# 九猫写作系统 - 核心写作指令

## 📖 小说信息
- 标题：《测试小说》
- 章节：第1章

## 🎯 核心创作要求

### 1. 语言风格要求（必须严格遵循）
请基于以上分析结果创作新的章节内容...

### 2. 人物塑造要求
我需要分析以下文本的特点...""",
            "batch_number": 1,
            "expected": False,
            "description": "真正的提示词内容，应该被正确识别"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试：{case['name']}")
        print(f"   描述：{case['description']}")
        print(f"   内容长度：{len(case['content'])}字符")
        print(f"   预期结果：{'通过' if case['expected'] else '失败'}")
        
        # 执行验证
        result = TestService._validate_batch_content(
            case['content'], 
            case['batch_number'], 
            min_length=500
        )
        
        print(f"   实际结果：{'通过' if result else '失败'}")
        print(f"   验证状态：{'✅ 正确' if result == case['expected'] else '❌ 错误'}")
        
        # 如果验证失败，提供诊断信息
        if not result:
            diagnosis = TestService._diagnose_batch_content_failure(case['content'], case['batch_number'])
            print(f"   失败诊断：{diagnosis}")

def test_prompt_content_detection_improvements():
    """测试提示词内容检测的改进"""
    print("\n=== 测试提示词内容检测改进 ===")
    
    test_cases = [
        {
            "name": "正常写作内容",
            "content": """# 第1章 新的开始

林小雨站在窗前，看着外面的雨滴。她想着今天的计划。

"我需要去买些东西。"她说道。

突然，她听到了敲门声。""",
            "expected": False,
            "description": "正常的写作内容，不应被误判为提示词"
        },
        {
            "name": "包含分析词汇但是写作内容",
            "content": """# 第1章 分析师的一天

林小雨是一名数据分析师。她需要分析以下数据。

"我将从以下几个方面来看这个问题。"她对同事说道。

她走到白板前，开始分析市场趋势。""",
            "expected": False,
            "description": "虽然包含'分析'等词汇，但是写作内容"
        },
        {
            "name": "真正的提示词",
            "content": """## 分析思路说明
### 分析方法
我需要分析以下文本的特点
### 分析过程
让我来分析这段文本
## 详细分析
基于以上分析结果""",
            "expected": True,
            "description": "包含多个提示词特征，应被识别为提示词"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试：{case['name']}")
        print(f"   描述：{case['description']}")
        print(f"   预期结果：{'提示词' if case['expected'] else '写作内容'}")
        
        # 执行检测
        result = TestService._is_prompt_content_strict(case['content'])
        
        print(f"   实际结果：{'提示词' if result else '写作内容'}")
        print(f"   检测状态：{'✅ 正确' if result == case['expected'] else '❌ 错误'}")

def test_length_requirements_relaxation():
    """测试长度要求的放宽"""
    print("\n=== 测试长度要求放宽 ===")
    
    # 测试第一批次的长度要求
    short_content = "第1章\n\n这是一个简短但有效的基础框架。林小雨走在路上。她想着今天的计划。"
    
    print(f"测试内容长度：{len(short_content)}字符")
    print(f"原要求：500字符")
    print(f"第一批次新要求：{max(200, 500 * 0.4)}字符")
    
    # 使用新的验证逻辑
    result = TestService._validate_batch_content(short_content, 1, min_length=500)
    print(f"验证结果：{'通过' if result else '失败'}")
    
    if not result:
        diagnosis = TestService._diagnose_batch_content_failure(short_content, 1)
        print(f"失败诊断：{diagnosis}")

def main():
    """主函数"""
    print("九猫系统 - 第一批次基础框架构建失败问题修复验证")
    print("=" * 70)
    
    try:
        # 测试批次内容验证改进
        test_batch_content_validation_improvements()
        
        # 测试提示词内容检测改进
        test_prompt_content_detection_improvements()
        
        # 测试长度要求放宽
        test_length_requirements_relaxation()
        
        print("\n" + "=" * 70)
        print("✅ 修复验证完成！")
        print("\n📝 修复要点总结：")
        print("1. 放宽第一批次长度要求：从500字符降至200字符或要求的40%")
        print("2. 改进提示词检测：提高误判阈值，减少有效内容被误判")
        print("3. 扩大故事内容检测范围：增加更多故事元素模式")
        print("4. 放宽章节标题要求：支持多种标题格式")
        print("5. 增强诊断功能：提供详细的失败原因分析")
        print("6. 优化验证逻辑：第一批次作为基础框架，要求更宽松")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    main()
