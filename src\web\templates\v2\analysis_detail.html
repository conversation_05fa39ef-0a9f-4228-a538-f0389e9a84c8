{% extends "v2/base.html" %}

{% block title %}{{ novel.title }} - {{ dimension_info.name }} 分析 - 九猫小说分析系统v2.0{% endblock %}

{% block extra_css %}
<style>
    .analysis-header {
        background-color: var(--primary-light);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    .tab-content {
        padding: 1.5rem 0;
    }
    .nav-pills .nav-link {
        color: var(--text-color);
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        margin-right: 0.5rem;
        font-weight: 500;
    }
    .nav-pills .nav-link.active {
        background-color: var(--primary-color);
        color: white;
    }
    .markdown-content {
        line-height: 1.7;
    }
    .markdown-content h1, 
    .markdown-content h2, 
    .markdown-content h3 {
        margin-top: 1.5rem;
        margin-bottom: 1rem;
    }
    .markdown-content ul, 
    .markdown-content ol {
        padding-left: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 返回按钮和标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="{{ url_for('v2.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回小说详情
        </a>
    </div>
    <div>
        <button class="btn btn-primary" id="reanalyzeBtn">
            <i class="fas fa-sync-alt me-2"></i>重新分析
        </button>
    </div>
</div>

<!-- 分析标题 -->
<div class="analysis-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">{{ dimension_info.name }} 分析</h1>
            <p class="mb-0">
                <span class="badge bg-primary me-2">{{ novel.title }}</span>
                {% if novel.author %}<span class="badge bg-secondary me-2">作者: {{ novel.author }}</span>{% endif %}
                <span class="badge bg-info">{{ novel.word_count }} 字</span>
            </p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="d-flex justify-content-md-end align-items-center">
                <i class="{{ dimension_info.icon }} fa-3x text-primary me-3"></i>
                <div>
                    <p class="mb-0 small">分析时间</p>
                    <p class="mb-0 fw-bold">{{ result.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 内容导航 -->
<ul class="nav nav-pills mb-4" id="analysisTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="result-tab" data-bs-toggle="tab" data-bs-target="#result" type="button" role="tab" aria-controls="result" aria-selected="true">
            <i class="fas fa-chart-bar me-1"></i>分析结果
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="reasoning-tab" data-bs-toggle="tab" data-bs-target="#reasoning" type="button" role="tab" aria-controls="reasoning" aria-selected="false">
            <i class="fas fa-brain me-1"></i>推理过程
        </button>
    </li>
</ul>

<!-- 选项卡内容 -->
<div class="tab-content" id="analysisTabsContent">
    <!-- 分析结果选项卡 -->
    <div class="tab-pane fade show active" id="result" role="tabpanel" aria-labelledby="result-tab">
        <div class="card">
            <div class="card-body">
                <div class="markdown-content" id="analysisContent">
                    {{ result.content|safe }}
                </div>
            </div>
        </div>
    </div>

    <!-- 推理过程选项卡 -->
    <div class="tab-pane fade" id="reasoning" role="tabpanel" aria-labelledby="reasoning-tab">
        <div class="card">
            <div class="card-body">
                <div class="markdown-content" id="reasoningContent">
                    <!-- 推理过程内容将通过JavaScript加载 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 重新分析确认模态框 -->
<div class="modal fade" id="reanalyzeModal" tabindex="-1" aria-labelledby="reanalyzeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reanalyzeModalLabel">确认重新分析</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要重新分析"{{ dimension_info.name }}"维度吗？这将覆盖现有的分析结果。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmReanalyzeBtn">确认重新分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 渲染分析结果
        const analysisContent = document.getElementById('analysisContent');
        renderMarkdown(analysisContent.innerHTML, analysisContent);

        // 加载推理过程
        const reasoningTab = document.getElementById('reasoning-tab');
        reasoningTab.addEventListener('click', function() {
            // 只在第一次点击时加载
            if (!this.dataset.loaded) {
                loadReasoningContent('/api/novel/{{ novel.id }}/analysis/{{ dimension }}/reasoning_content', 'reasoningContent');
                this.dataset.loaded = 'true';
            }
        });

        // 重新分析按钮
        document.getElementById('reanalyzeBtn').addEventListener('click', function() {
            const reanalyzeModal = new bootstrap.Modal(document.getElementById('reanalyzeModal'));
            reanalyzeModal.show();
        });

        // 确认重新分析按钮
        document.getElementById('confirmReanalyzeBtn').addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>分析中...';
            
            // 发送API请求
            fetch(`/api/novel/{{ novel.id }}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dimensions: ['{{ dimension }}'],
                    force_reanalysis: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 刷新页面
                    location.reload();
                } else {
                    alert(`重新分析失败: ${data.error}`);
                    this.disabled = false;
                    this.innerHTML = '确认重新分析';
                }
            })
            .catch(error => {
                alert(`发送请求时出错: ${error.message}`);
                this.disabled = false;
                this.innerHTML = '确认重新分析';
            });
        });
    });
</script>
{% endblock %}
