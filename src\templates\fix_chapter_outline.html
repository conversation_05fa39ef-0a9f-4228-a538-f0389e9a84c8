<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复整本书章纲分析推理过程 - 九猫写作系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .btn-fix {
            margin-top: 20px;
        }
        .result-container {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            display: none;
        }
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        .loading img {
            width: 50px;
            height: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">修复整本书章纲分析推理过程</h1>
        
        <div class="alert alert-info">
            <p><strong>问题描述：</strong>整本书的章纲分析（chapter_outline）推理过程显示的是一个简短的摘要，而不是详细的分析内容。</p>
            <p><strong>修复方法：</strong>点击下方按钮，系统将自动修复所有小说的章纲分析推理过程。</p>
        </div>
        
        <div class="text-center">
            <button id="fixButton" class="btn btn-primary btn-lg btn-fix">开始修复</button>
        </div>
        
        <div id="loading" class="loading">
            <img src="{{ url_for('static', filename='img/loading.gif') }}" alt="加载中">
            <p>正在修复，请稍候...</p>
        </div>
        
        <div id="resultContainer" class="result-container">
            <h4>修复结果：</h4>
            <div id="resultContent"></div>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/jquery-3.6.0.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#fixButton').click(function() {
                // 显示加载中
                $('#loading').show();
                $('#resultContainer').hide();
                $('#fixButton').prop('disabled', true);
                
                // 发送修复请求
                $.ajax({
                    url: '/api/fix_chapter_outline_reasoning',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({}),
                    success: function(response) {
                        // 显示成功结果
                        $('#loading').hide();
                        $('#resultContainer').show();
                        $('#resultContent').html(`
                            <div class="alert alert-success">
                                <p><strong>修复成功！</strong></p>
                                <p>${response.message}</p>
                            </div>
                        `);
                        $('#fixButton').prop('disabled', false);
                    },
                    error: function(xhr, status, error) {
                        // 显示错误结果
                        $('#loading').hide();
                        $('#resultContainer').show();
                        
                        let errorMessage = '修复失败，请联系管理员。';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.error) {
                                errorMessage = response.error;
                            }
                        } catch (e) {
                            console.error('解析错误响应失败', e);
                        }
                        
                        $('#resultContent').html(`
                            <div class="alert alert-danger">
                                <p><strong>修复失败！</strong></p>
                                <p>${errorMessage}</p>
                            </div>
                        `);
                        $('#fixButton').prop('disabled', false);
                    }
                });
            });
        });
    </script>
</body>
</html>
