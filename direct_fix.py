import os
import shutil
import re

def fix_jinja_template():
    # 文件路径
    template_file = 'src/web/templates/analysis.html'
    
    # 确保文件存在
    if not os.path.exists(template_file):
        print(f"错误: 找不到文件 {template_file}")
        return False
    
    # 创建备份
    backup_file = template_file + '.bak_direct'
    shutil.copy2(template_file, backup_file)
    print(f"已创建备份: {backup_file}")
    
    try:
        # 读取文件内容
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 直接修复第246行的多重嵌套Jinja标签
        pattern1 = r'<div class="log-entry {% if log\.level == \'error\' %}text-danger{% elif log\.level == \'warning\' %}text-warning{% endif %} {% if log\.important %}font-weight-bold text-primary{% endif %}">'
        replacement1 = '<div class="log-entry {% if log.level == \'error\' %}text-danger{% elif log.level == \'warning\' %}text-warning{% endif %} {% if log.important %}font-weight-bold text-primary{% endif %}">'
        
        # 显示原始内容
        matches = re.findall(pattern1, content)
        if matches:
            print(f"找到需要修复的复杂标签: {matches[0]}")
            content = re.sub(pattern1, replacement1, content)
            print("已修复复杂标签")
        
        # 直接修复第858行-862行的else-endif结构
        # 找到problem_section的开始和结束位置
        else_pattern = r'{%\s*else\s*%}'
        endif_pattern = r'{%\s*endif\s*%}'
        
        # 定位问题区域
        matches_else = list(re.finditer(else_pattern, content))
        matches_endif = list(re.finditer(endif_pattern, content))
        
        if matches_else and matches_endif:
            # 获取最后的else和endif标签
            last_else = matches_else[-1]
            last_endif = matches_endif[-1]
            
            print(f"找到最后的else标签: {content[last_else.start():last_else.end()]}")
            print(f"找到最后的endif标签: {content[last_endif.start():last_endif.end()]}")
            
            # 确保它们格式正确
            new_content = (
                content[:last_else.start()] + 
                "{% else %}" + 
                content[last_else.end():last_endif.start()] + 
                "{% endif %}" + 
                content[last_endif.end():]
            )
            
            # 写回修复后的内容
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("已修复else-endif结构")
            return True
        else:
            print("未找到需要修复的else-endif结构")
            return False
    
    except Exception as e:
        print(f"修复过程中出错: {str(e)}")
        # 如果出错，恢复备份
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, template_file)
            print("已恢复备份")
        return False

if __name__ == "__main__":
    print("九猫Jinja模板直接修复工具")
    print("-" * 30)
    success = fix_jinja_template()
    if success:
        print("修复完成! 请重新启动九猫系统并测试。")
    else:
        print("修复失败，请尝试手动修复。")
    print("-" * 30)

# 直接修复特定行的脚本
with open('src/web/routes/v3_routes.py', 'r', encoding='utf-8') as file:
    lines = file.readlines()

# 备份原文件
with open('src/web/routes/v3_routes.py.bak3', 'w', encoding='utf-8') as file:
    file.writelines(lines)
print("已创建备份文件")

# 直接修复第522行 (index 521)
if "except Exception as e:" in lines[521]:
    original = lines[521]
    lines[521] = "                except Exception as e:\n"
    print(f"修复了第522行：从\n{original}修改为\n{lines[521]}")

# 写回文件
with open('src/web/routes/v3_routes.py', 'w', encoding='utf-8') as file:
    file.writelines(lines)
print("已写回文件") 