"""
九猫系统 - 小说预设模板生成器
用于生成基于15个维度的小说预设模板
"""

def get_novel_preset_template(title=None):
    """
    生成15个维度的小说预设模板

    Args:
        title: 小说标题（可选）

    Returns:
        完整的预设模板字符串
    """
    novel_title = f"《{title}》" if title else "《小说标题》"
    
    template = f"""# {novel_title} 小说设定模板

## 语言风格
- 叙述风格：[例：偏正式/偏口语/混合]
- 句式类型分布：[例：简短句40%，中等句50%，长句10%]
- 抒情性段落占比：[例：25% - 适合情感表达场景]
- 叙事性段落占比：[例：60% - 适合推动情节发展]
- 五感描写偏好：[例：视觉描写为主(50%)，听觉描写次之(30%)，触觉(10%)，嗅觉(5%)，味觉(5%)]
- 修辞手法偏好：[例：比喻为主，夸张次之，适量使用排比和对偶]
- 特色表达：[例：独特的口头禅，特定意象系统，标志性句式]

## 节奏节拍
- 情节波动曲线：[例：起伏频率每2-3万字一个小高潮，每8-10万字一个大高潮]
- 章节长度：[例：短章节(3000字以内)占30%，中等章节(3000-5000字)占60%，长章节(5000字以上)占10%]
- 每千字冲突点数量：[例：慢节奏(0.8-1)，中等节奏(1-1.5)，快节奏(1.5+)]
- 时间流速控制：[例：关键场景慢镜头处理，时间流速比例3:1，日常场景可适当快进]
- 章节钩子类型分布：[例：悬念型40%，反转型30%，情感型20%，行动型10%]
- 场景切换频率：[例：平均每5000字一次主要场景转换]
- 多线叙事交织频率：[例：每3-4章进行一次线索切换]

## 结构分析
- 整体结构类型：[例：三幕式结构，英雄旅程，五幕式结构]
- 开端-发展-结局占比：[例：开端15%，发展70%，结局15%]
- 主线情节与支线比例：[例：主线70%，支线30%]
- 主要转折点位置：[例：第一转折点(总字数20-25%)，中点危机(45-55%)，第二转折点(75-80%)]
- 叙事视角：[例：第一人称/第三人称/多重视角]
- 时间线处理：[例：线性，非线性(倒叙15%，插叙10%)]
- 伏笔系统：[例：主线伏笔回收率90%，支线伏笔回收率70%，首卷伏笔50%在30万字内回收]

## 句式变化
- 长短句分布：[例：短句(1-10词)占40%，中句(11-25词)占50%，长句(26词以上)占10%]
- 句式类型比例：[例：陈述句60%，疑问句10%，感叹句15%，祈使句5%，复合句10%]
- 特色句式特征：[例：排比句，叠词句，设问句的使用频率与场景]
- 段落首句特点：[例：直接切入，环境描写引入，对话引入的比例]
- 段落尾句特点：[例：悬念设置，情感强化，场景转换暗示的比例]
- 对话句与叙述句比例：[例：对话40%，叙述60%]
- 句式节奏变化规律：[例：情绪紧张时使用短句，舒缓场景使用长句]

## 段落长度
- 短段落频率：[例：1-3行段落占比50%]
- 中长段落频率：[例：4-6行段落占比40%]
- 超长段落频率：[例：7行以上段落占比10%]
- 段落长度与内容类型关系：[例：对话段落偏短，描写段落偏长，动作段落长度适中]
- 段落长度变化规律：[例：高潮前段落逐渐缩短，高潮后段落逐渐延长]
- 不同人物对应的段落长度特征：[例：主角思考段落较长，配角描写段落较短]
- 特殊场景的段落长度设计：[例：战斗场景短段落密集，回忆场景长段落为主]

## 视角变化
- 主要叙事视角：[例：限制性第三人称，全知视角，第一人称]
- 视角切换频率：[例：以每章为单位，保持单一视角；或每2-3章切换一次主视角]
- 同一场景多视角处理：[例：关键场景通过2-3个视角重复展现，提供不同角度]
- 视角深度设置：[例：主角深入心理描写，次要角色有限视角]
- 视角与情感强度关系：[例：情感强烈场景采用第一人称，客观场景采用第三人称]
- 视角转换技巧：[例：使用场景切换，章节分隔，意识流等方式实现自然转换]
- 视角选择的特殊处理：[例：隐藏信息的视角选择，误导性视角，反转性视角]

## 段落流畅度
- 段落间逻辑连接方式：[例：因果连接，时间推进，空间转换，心理关联]
- 段落过渡词使用频率：[例：每3-4个段落使用1次明确的过渡词或短语]
- 意象衔接技巧：[例：通过重复关键意象实现段落间的潜在连接]
- 情感递进模式：[例：采用波浪式情感递进，或阶梯式情感堆叠]
- 段落主题聚焦程度：[例：每个段落围绕单一主题展开，避免主题发散]
- 衔接与断裂的平衡：[例：正常叙事采用90%衔接率，转折点处使用有意断裂]
- 回顾与铺垫的分布：[例：每章包含2-3处前文回顾和1-2处后续铺垫]

## 小说特点
- 核心题材：[例：玄幻/科幻/都市/武侠/言情/悬疑]
- 世界观开放度：[例：完全原创世界，现实+超自然元素，历史改编]
- 主要情感基调：[例：热血向，治愈系，悲剧色彩，喜剧风格]
- 写作风格倾向：[例：写实主义，浪漫主义，意识流，魔幻现实主义]
- 情节驱动类型：[例：以角色成长为主，以悬念解谜为主，以关系发展为主]
- 文化元素融合度：[例：东方文化为主，多元文化混搭，特定历史文化还原]
- 创新亮点：[例：独特的魔法体系，创新的社会结构，新颖的人物能力设计]

## 世界构建
- 世界类型：[例：架空历史，平行宇宙，现代+超自然，纯科幻未来]
- 物理规则设定：[例：现实物理规则为基础，加入特定超自然规则]
- 社会结构设计：[例：等级制度，行会组织，部族联盟，现代社会结构]
- 文化系统：[例：宗教信仰，传统习俗，历史传承，技术发展]
- 地理环境描绘：[例：多大陆结构，城乡分布，特殊地理奇观]
- 超自然元素系统：[例：魔法体系，超能力分类，科技水平，修炼体系]
- 世界观展示节奏：[例：渐进式揭示，每章平均增加1-2个世界观元素]

## 人物关系
- 核心人物关系网：[例：以主角为中心的射线型，多中心交织型，阵营对抗型]
- 主要关系类型分布：[例：亲情20%，友情30%，爱情15%，师徒10%，敌对25%]
- 关系发展模式：[例：渐进式发展，剧烈冲突后和解，误解到理解]
- 关系转折点设置：[例：每条重要关系线设置2-3个明确转折点]
- 关系对人物成长的影响：[例：关键关系推动角色性格转变和能力突破]
- 群体关系动态：[例：派系形成与变化，团队凝聚与分化过程]
- 隐藏关系设计：[例：身份隐瞒，误解关系，暗中联系]

## 开篇效果
- 开篇类型：[例：悬念型，角色展示型，世界观铺陈型，事件导入型]
- 首章关键元素：[例：主角特性展示，核心冲突暗示，世界特点呈现]
- 读者期待构建：[例：通过悬念、共鸣点或独特设定引发好奇]
- 信息量控制：[例：首章核心信息3-5个，新世界元素不超过7个]
- 情感基调确立：[例：通过特定场景和语言风格确立作品基调]
- 叙事节奏设置：[例：快速导入主事件，或缓慢铺陈背景]
- 开篇钩子强度：[例：悬念强度，冲突强度，情感强度的设置]

## 高潮节奏
- 高潮点分布密度：[例：小高潮(每2-3万字)，中高潮(每8-10万字)，大高潮(每25-30万字)]
- 高潮类型比例：[例：战斗型40%，情感型30%，悬念解答型20%，能力突破型10%]
- 高潮铺垫长度：[例：大高潮前3-5章铺垫，中高潮前1-2章铺垫]
- 高潮持续长度：[例：小高潮0.5-1章，中高潮1-2章，大高潮2-3章]
- 高潮后缓冲处理：[例：大高潮后1-2章缓冲过渡，小高潮后0.3-0.5章缓冲]
- 多线交汇高潮设计：[例：重要高潮点2-3条线索同时交汇，形成复合效果]
- 递进式高潮结构：[例：设计高潮强度的递进关系，形成期待和满足的节奏]

## 章纲分析
- 章节功能定位：[例：推动主线型章节，人物塑造型章节，世界观展示型章节，过渡型章节]
- 章节内部结构：[例："问题-尝试-冲突-解决"结构，或"铺垫-高潮-余波"结构]
- 章节之间的连接方式：[例：直接承接，平行发展，时间跳跃，视角转换]
- 章节焦点分配：[例：单一焦点型，双焦点交替型，多焦点并行型]
- 章节情感曲线：[例：起伏型，递进型，平缓型，反转型]
- 章节信息密度：[例：高密度信息章节与舒缓节奏章节的交替]
- 特殊章节设计：[例：插叙章，回忆章，梦境章，平行视角章]

## 大纲分析
- 主线故事脉络：[详细描述主线故事的完整发展路径，包括起因、经过、结果]
- 次要情节线索：[列举2-4条重要的次要情节线索，及其与主线的交织点]
- 情感发展线索：[描述主要角色的情感发展轨迹，包括关键转折点]
- 冲突设置层次：[外部冲突与内部冲突的设置及发展规划]
- 能力/成长进阶节点：[主角和重要角色的能力/心智成长关键节点规划]
- 世界观展开计划：[世界观元素的逐步揭示计划，包括时机和方式]
- 高潮/转折点分布：[规划小说中的主要高潮点和转折点的位置与内容]

## 热梗统计
- 流行梗使用密度：[例：每10万字的热梗使用数量，高/中/低密度]
- 热梗类型偏好：[例：网络流行梗20%，影视梗30%，游戏梗15%，文学梗20%，历史梗15%]
- 经典长寿梗比例：[例：长寿梗占40%，阶段性流行梗占50%，新兴热梗占10%]
- 热梗使用场景分布：[例：对话中70%，叙述中20%，人物思想中10%]
- 梗与角色匹配度：[例：特定角色专用梗的设置，梗与角色性格的匹配]
- 原创梗潜力：[例：可能成为原创梗的元素设计，特色表达]
- 热梗融入自然度：[例：有机融入情节，强行使用，变形使用的比例]
"""
    return template

def get_chapter_preset_template(title=None, chapter_title=None):
    """
    生成15个维度的章节预设模板

    Args:
        title: 小说标题（可选）
        chapter_title: 章节标题（可选）

    Returns:
        完整的章节预设模板字符串
    """
    novel_title = f"《{title}》" if title else "《小说标题》"
    chapter_name = f"《{chapter_title}》" if chapter_title else "《章节标题》"
    
    template = f"""# {novel_title} - {chapter_name} 章节设定模板

## 语言风格
- 叙述风格：[例：偏正式/偏口语/混合]
- 句式类型分布：[例：简短句40%，中等句50%，长句10%]
- 抒情性段落占比：[例：25% - 适合情感表达场景]
- 叙事性段落占比：[例：60% - 适合推动情节发展]
- 五感描写偏好：[例：视觉描写为主(50%)，听觉描写次之(30%)，触觉(10%)，嗅觉(5%)，味觉(5%)]
- 修辞手法偏好：[例：比喻为主，夸张次之，适量使用排比和对偶]
- 与整体风格的一致性：[例：保持整体风格的基础上，针对本章特殊场景做适当调整]

## 节奏节拍
- 本章节奏定位：[例：快节奏推进章，舒缓过渡章，高潮爆发章]
- 段落长度变化规律：[例：前段平稳-中段紧凑-后段舒展]
- 本章冲突点设置：[例：2-3个小冲突，1个核心冲突，冲突类型与解决方式]
- 场景切换频率：[例：2-3次主要场景转换，切换方式与理由]
- 时间流速控制：[例：特定段落慢镜头处理，快速推进处理]
- 钩子设计：[例：章首钩子类型，章末钩子强度与指向]
- 与上下章节的节奏衔接：[例：承接上章情绪尾声，为下章埋设情绪基础]

## 结构分析
- 本章在整体结构中的位置：[例：引入期，发展期，高潮前，高潮中，尾声]
- 章节内部结构：[例：起-承-转-合，或问题-尝试-失败-突破-解决]
- 伏笔设置与回收：[例：本章设置的伏笔，回收的前文伏笔]
- 视角设计：[例：单一视角，双视角交替，多视角并行]
- 时间跨度：[例：数小时，一天，数天，时间压缩或展开处理]
- 与主线的关联度：[例：直接推动主线，侧面补充主线，平行发展支线]
- 结构创新点：[例：特殊的叙事结构，非线性展现，框架式结构]

## 句式变化
- 本章句式特点：[例：以短句为主，长短句交替，复合句占比上升]
- 特殊句式使用计划：[例：排比句在高潮处使用，设问句增加悬念]
- 对话句与叙述句比例：[例：对话占45%，叙述占55%]
- 句式与情绪的匹配：[例：紧张情绪短促句式，沉思场景复合句]
- 句首句尾特点：[例：段落首句多用动作导入，尾句多用心理总结]
- 重复强调的关键句：[例：本章需要重复强调的核心表述或意象]
- 与角色特点的匹配：[例：特定角色的标志性句式与表达方式]

## 段落长度
- 本章段落长度基调：[例：以中短段落为主，长段落少用]
- 短段落使用场景：[例：激烈对抗，紧张气氛，快速推进]
- 中长段落使用场景：[例：日常对话，一般叙事，环境描写]
- 超长段落使用场景：[例：心理活动，重要背景介绍，关键说明]
- 段落长度变化曲线：[例：起始短-中段长-高潮短-结尾中]
- 特殊段落长度处理：[例：特定场景的刻意段落设计]
- 与角色视角的关联：[例：主角视角段落较长，配角视角简洁]

## 视角变化
- 本章主视角：[例：主角A第三人称限制视角]
- 视角切换计划：[例：中段临时切换至角色B视角，结尾回到主视角]
- 视角深度：[例：主视角深入心理，次要视角浅表描写]
- 多视角场景设计：[例：关键事件通过2个视角展现，形成对比]
- 视角限制与信息控制：[例：主视角知晓的信息范围，读者与角色信息差]
- 视角转换技巧：[例：场景切换式转换，过渡式转换，直接切换]
- 与情感表达的关系：[例：情感深入时视角更加深入，抽离时视角更客观]

## 段落流畅度
- 段落间主要连接方式：[例：因果连接，时间推进，场景转换]
- 过渡句设计：[例：使用回顾性过渡，预示性过渡，中间过渡]
- 意象连接使用：[例：通过重复"水"的意象连接不同场景]
- 信息节奏控制：[例：信息密集段与舒缓段交替，保持阅读节奏]
- 脉络清晰度要求：[例：确保4条以上信息脉络清晰可辨]
- 特殊流畅度处理：[例：高潮前刻意断裂，情感爆发处快速切换]
- 自然衔接技巧：[例：利用环境，共同人物，共同事件实现衔接]

## 小说特点
- 本章体现的核心题材特点：[例：突出玄幻元素，强化悬疑氛围]
- 风格突出点：[例：本章偏向写实主义，减少夸张元素]
- 情感基调：[例：基调紧张，穿插温情，结尾留悬念]
- 情节驱动力：[例：以角色内心冲突为主要驱动力]
- 文化元素使用：[例：融入特定历史文化背景，增加代入感]
- 类型文学特征：[例：符合玄幻小说的能力展示，世界设定]
- 创新尝试：[例：在传统框架下尝试新的叙事手法]

## 世界构建
- 本章新增世界元素：[例：新的地理位置，社会组织，文化习俗]
- 已有世界观深化：[例：深化魔法体系规则，完善社会结构]
- 世界元素与情节结合：[例：如何通过情节自然展现世界设定]
- 信息量控制：[例：控制新世界元素在3-5个以内，避免信息过载]
- 世界一致性维护：[例：确保新元素与已有设定不冲突]
- 读者认知负担考量：[例：复杂设定分步解释，易理解设定直接呈现]
- 世界感官化处理：[例：通过角色感官体验让世界设定立体化]

## 人物关系
- 本章核心关系线：[例：主角与角色A的师徒关系变化]
- 次要关系互动：[例：配角B与C的矛盾加深]
- 关系转折点设计：[例：主角与角色D关系从敌对转为合作]
- 潜在关系暗示：[例：暗示角色E与F之间的隐秘联系]
- 群体关系变化：[例：阵营内部分化，或团队凝聚加强]
- 关系表现手法：[例：通过对话展现，通过行动反映，通过心理活动揭示]
- 关系对情节的推动：[例：关系变化如何引发下一步情节发展]

## 开篇效果
- 章首场景设计：[例：紧接上章，时间跳跃，全新场景]
- 吸引力构建：[例：悬念型开头，冲突型开头，意境型开头]
- 首段关键元素：[例：核心人物出场，关键信息提示，情感基调确立]
- 节奏设定：[例：缓慢铺垫，快速导入，情绪渲染]
- 与上章衔接：[例：直接承接上章悬念，时间间隔后的呼应]
- 读者期待引导：[例：设置明确的章内期待点，预示可能发展]
- 基调确立：[例：通过语言风格和场景氛围确立本章基调]

## 高潮节奏
- 本章高潮定位：[例：小高潮，中高潮，大高潮前奏，大高潮核心]
- 高潮类型：[例：战斗高潮，情感高潮，悬念解答，能力突破]
- 铺垫设计：[例：前半章埋设3处铺垫，中段加速推进]
- 高潮展现技巧：[例：语言节奏加快，段落缩短，感官描写增强]
- 高潮强度控制：[例：中等强度，为后续更大高潮做铺垫]
- 高潮后处理：[例：简短缓冲，直接切入下一情节，留悬念]
- 多线交汇设计：[例：2条线索在高潮处交汇，形成复合效果]

## 章纲分析
- 本章核心功能：[例：推动主线型，角色成长型，世界观展示型]
- 起承转合安排：[例：起(15%)承(40%)转(30%)合(15%)]
- 核心冲突设置：[例：内在冲突，人际冲突，环境冲突的具体安排]
- 章节节奏规划：[例：波浪式推进，递进式展开，回环式结构]
- 信息点布置：[例：均匀分布信息点，或集中在特定段落]
- 情感曲线设计：[例：低-高-低，平-升-平，起伏交替]
- 与全书的衔接点：[例：呼应前文，铺垫后续，独立成篇]

## 大纲分析
- 本章在全书大纲中的定位：[例：第一卷第三章，起始阶段的关键转折点]
- 主线推进内容：[例：推动的具体主线内容和程度]
- 支线发展计划：[例：发展或引入的支线内容]
- 角色弧线发展：[例：主角成长路径上的位置，突破点或瓶颈]
- 世界观展示计划：[例：计划中的世界观元素揭示]
- 情感线索发展：[例：重要情感线索的推进方向和程度]
- 未来章节铺垫：[例：为未来3-5章设置的铺垫内容]

## 热梗统计
- 计划使用的热梗数量：[例：3-5个主要热梗元素]
- 热梗类型分布：[例：网络流行梗1个，影视梗2个，游戏梗1个]
- 热梗与场景匹配：[例：轻松场景使用喜剧梗，紧张场景使用经典台词]
- 梗与角色匹配：[例：特定角色使用符合其人设的梗]
- 原创梗潜力点：[例：可能形成原创梗的设计点]
- 热梗融入自然度要求：[例：确保80%以上的梗自然融入情节]
- 梗的时效性考量：[例：避免过时梗，选择经典长寿梗]
"""
    return template

# 如果直接运行此脚本，则输出模板示例
if __name__ == "__main__":
    print("小说预设模板示例:")
    print(get_novel_preset_template("示例小说"))
    print("\n\n章节预设模板示例:")
    print(get_chapter_preset_template("示例小说", "第一章")) 