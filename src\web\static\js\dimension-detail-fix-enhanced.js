/**
 * 九猫 - 维度详情页面修复脚本（增强版）
 * 修复维度详情页面的各种问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('[九猫修复] 维度详情页面修复脚本（增强版）已加载');

    // 配置
    const CONFIG = {
        // 是否启用调试日志
        debug: true,

        // 是否禁用自动刷新
        disableAutoRefresh: true,

        // 是否禁用图表
        disableCharts: true,

        // 是否修复进度信息
        fixProgressInfo: true,

        // 是否修复内容显示
        fixContentDisplay: true
    };

    // 调试日志
    function debugLog(...args) {
        if (CONFIG.debug) {
            console.log('[九猫修复-维度详情]', ...args);
        }
    }

    // 获取当前页面的小说ID和维度
    function getPageInfo() {
        // 从URL中获取
        const match = window.location.pathname.match(/\/novel\/(\d+)\/analysis\/([^\/]+)/);
        if (match && match[1] && match[2]) {
            return {
                novelId: match[1],
                dimension: match[2]
            };
        }

        return null;
    }

    // 禁用自动刷新
    function disableAutoRefresh() {
        if (!CONFIG.disableAutoRefresh) {
            return;
        }

        debugLog('禁用自动刷新');

        // 清除所有定时器
        for (let i = 1; i < 10000; i++) {
            window.clearInterval(i);
            window.clearTimeout(i);
        }

        // 重写setInterval和setTimeout
        const originalSetInterval = window.setInterval;
        window.setInterval = function(callback, delay, ...args) {
            // 允许进度更新的定时器
            if (delay >= 1000 && String(callback).includes('progress')) {
                debugLog('允许进度更新定时器');
                return originalSetInterval(callback, delay, ...args);
            }

            debugLog('阻止定时器:', delay, String(callback).substring(0, 100));
            return 0;
        };

        const originalSetTimeout = window.setTimeout;
        window.setTimeout = function(callback, delay, ...args) {
            // 允许短延迟的定时器
            if (delay < 1000) {
                return originalSetTimeout(callback, delay, ...args);
            }

            // 允许进度更新的定时器
            if (String(callback).includes('progress')) {
                debugLog('允许进度更新延时器');
                return originalSetTimeout(callback, delay, ...args);
            }

            // 允许页面刷新的定时器（分析完成后）
            if (String(callback).includes('location.reload')) {
                debugLog('允许页面刷新延时器');
                return originalSetTimeout(callback, delay, ...args);
            }

            debugLog('阻止延时器:', delay, String(callback).substring(0, 100));
            return 0;
        };
    }

    // 禁用图表
    function disableCharts() {
        if (!CONFIG.disableCharts) {
            return;
        }

        debugLog('禁用图表');

        // 移除所有图表容器
        const chartContainers = document.querySelectorAll('.chart-container');
        chartContainers.forEach(container => {
            debugLog('移除图表容器');
            container.remove();
        });

        // 移除所有canvas元素
        const canvasElements = document.querySelectorAll('canvas');
        canvasElements.forEach(canvas => {
            debugLog('移除canvas元素');
            canvas.remove();
        });

        // 如果存在Chart对象，禁用它
        if (window.Chart) {
            debugLog('禁用Chart对象');

            // 保存原始的Chart构造函数
            const originalChart = window.Chart;

            // 重写Chart构造函数
            window.Chart = function() {
                debugLog('阻止创建Chart实例');
                return {
                    update: function() {},
                    destroy: function() {},
                    render: function() {},
                    stop: function() {},
                    resize: function() {},
                    clear: function() {},
                    toBase64Image: function() { return ''; },
                    generateLegend: function() { return ''; },
                    getElementAtEvent: function() { return []; },
                    getElementsAtEvent: function() { return []; },
                    getDatasetAtEvent: function() { return []; },
                    getDatasetMeta: function() { return {}; }
                };
            };

            // 复制原始Chart的静态属性
            for (const prop in originalChart) {
                if (originalChart.hasOwnProperty(prop)) {
                    window.Chart[prop] = originalChart[prop];
                }
            }
        }
    }

    // 修复内容显示
    function fixContentDisplay() {
        if (!CONFIG.fixContentDisplay) {
            return;
        }

        debugLog('修复内容显示');

        // 查找内容容器
        const contentContainer = document.querySelector('.analysis-content');
        if (!contentContainer) {
            debugLog('找不到内容容器');
            return;
        }

        // 确保内容容器可见
        contentContainer.style.display = 'block';
        contentContainer.style.visibility = 'visible';
        contentContainer.style.opacity = '1';

        // 移除可能的隐藏类
        contentContainer.classList.remove('d-none', 'hidden', 'invisible');

        // 确保内容容器有足够的高度
        contentContainer.style.maxHeight = 'none';
        contentContainer.style.minHeight = '300px';

        // 如果内容为空，添加提示信息
        if (!contentContainer.textContent.trim()) {
            debugLog('内容为空，添加提示信息');
            contentContainer.innerHTML = `
                <div class="alert alert-info">
                    <p>正在加载分析内容，请稍候...</p>
                    <p>如果长时间未显示，请尝试刷新页面。</p>
                </div>
            `;
        }
    }

    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        debugLog('页面加载完成，初始化维度详情页面修复');

        // 检查是否是维度详情页面
        const pageInfo = getPageInfo();
        if (!pageInfo) {
            debugLog('不是维度详情页面，不执行修复');
            return;
        }

        debugLog('当前页面信息:', pageInfo);

        // 禁用自动刷新
        disableAutoRefresh();

        // 禁用图表
        disableCharts();

        // 修复内容显示
        fixContentDisplay();
    });

    // 如果页面已经加载完成，立即执行
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        debugLog('页面已加载，立即初始化维度详情页面修复');

        // 检查是否是维度详情页面
        const pageInfo = getPageInfo();
        if (!pageInfo) {
            debugLog('不是维度详情页面，不执行修复');
            return;
        }

        debugLog('当前页面信息:', pageInfo);

        // 禁用自动刷新
        disableAutoRefresh();

        // 禁用图表
        disableCharts();

        // 修复内容显示
        fixContentDisplay();
    }

    console.log('[九猫修复] 维度详情页面修复脚本（增强版）加载完成');
})();