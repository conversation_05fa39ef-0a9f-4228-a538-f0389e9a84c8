/**
 * 简化版 Marked.js
 * 提供基本的 Markdown 解析功能
 */

// 创建全局 marked 对象
window.marked = (function() {
    console.log('[Marked] 初始化简化版 Marked.js');
    
    // 基本的 HTML 转义函数
    function escapeHtml(text) {
        const escapeMap = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;'
        };
        return text.replace(/[&<>"']/g, match => escapeMap[match]);
    }
    
    // 简单的 Markdown 解析函数
    function parseMarkdown(text) {
        if (!text) return '';
        
        // 处理代码块
        text = text.replace(/```([a-z]*)\n([\s\S]*?)\n```/g, function(match, lang, code) {
            return '<pre><code class="language-' + lang + '">' + escapeHtml(code) + '</code></pre>';
        });
        
        // 处理标题
        text = text.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
        text = text.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
        text = text.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
        text = text.replace(/^#### (.*?)$/gm, '<h4>$1</h4>');
        text = text.replace(/^##### (.*?)$/gm, '<h5>$1</h5>');
        text = text.replace(/^###### (.*?)$/gm, '<h6>$1</h6>');
        
        // 处理粗体和斜体
        text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // 处理行内代码
        text = text.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // 处理链接
        text = text.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');
        
        // 处理图片
        text = text.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1">');
        
        // 处理无序列表
        text = text.replace(/^\* (.*?)$/gm, '<li>$1</li>');
        text = text.replace(/^- (.*?)$/gm, '<li>$1</li>');
        text = text.replace(/(<li>.*?<\/li>\n)+/g, function(match) {
            return '<ul>' + match + '</ul>';
        });
        
        // 处理有序列表
        text = text.replace(/^\d+\. (.*?)$/gm, '<li>$1</li>');
        text = text.replace(/(<li>.*?<\/li>\n)+/g, function(match) {
            return '<ol>' + match + '</ol>';
        });
        
        // 处理引用
        text = text.replace(/^> (.*?)$/gm, '<blockquote>$1</blockquote>');
        
        // 处理水平线
        text = text.replace(/^---+$/gm, '<hr>');
        
        // 处理段落
        text = text.replace(/^(?!<[a-z])(.*?)$/gm, function(match, p1) {
            if (p1.trim() === '') return '';
            return '<p>' + p1 + '</p>';
        });
        
        return text;
    }
    
    // 创建 marked 对象
    const marked = {
        // 主解析函数
        parse: function(text, options) {
            console.log('[Marked] 解析 Markdown 文本');
            return parseMarkdown(text);
        },
        
        // 设置选项函数
        setOptions: function(options) {
            console.log('[Marked] 设置选项:', options);
            // 简化版不需要设置选项
            return this;
        },
        
        // 兼容原始 marked 函数调用
        defaults: function(options) {
            console.log('[Marked] 设置默认选项');
            return this;
        }
    };
    
    // 确保 marked 可以作为函数调用
    const markedFunction = function(text, options) {
        return marked.parse(text, options);
    };
    
    // 复制 marked 对象的所有属性到函数上
    for (const key in marked) {
        if (marked.hasOwnProperty(key)) {
            markedFunction[key] = marked[key];
        }
    }
    
    console.log('[Marked] 简化版 Marked.js 初始化完成');
    return markedFunction;
})();
