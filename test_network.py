"""
测试网络连接到阿里云API服务器
"""
import requests
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_connection():
    """测试与阿里云API服务器的连接"""
    # 阿里云API服务器地址
    url = "https://dashscope.aliyuncs.com/api/v1"
    
    logger.info(f"正在测试连接到: {url}")
    start_time = time.time()
    
    try:
        # 发送简单的HEAD请求
        response = requests.head(url, timeout=10)
        end_time = time.time()
        
        logger.info(f"连接成功! 响应时间: {end_time - start_time:.2f}秒")
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应头: {dict(response.headers)}")
        
        return True
    except requests.exceptions.ConnectionError:
        logger.error("连接错误: 无法连接到服务器")
        return False
    except requests.exceptions.Timeout:
        logger.error("连接超时: 服务器响应时间过长")
        return False
    except Exception as e:
        logger.error(f"连接测试出错: {str(e)}")
        return False

if __name__ == "__main__":
    test_connection()
