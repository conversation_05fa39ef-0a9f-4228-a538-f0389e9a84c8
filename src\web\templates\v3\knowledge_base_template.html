{% extends "v3/base.html" %}

{% block title %}知识库预设模板 - 九猫{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/knowledge-base-compact.css') }}">
<style>
    .nav-tabs .nav-link {
        border: none;
        color: #495057;
        font-weight: 500;
        padding: 0.75rem 1rem;
    }

    .nav-tabs .nav-link.active {
        color: #4a6bff;
        border-bottom: 3px solid #4a6bff;
        background-color: transparent;
    }

    .dimension-item {
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .dimension-item:hover {
        background-color: #f8f9fa;
    }

    .dimension-item.active {
        background-color: #e9ecef;
        font-weight: bold;
    }

    .accordion-button {
        padding: 0.75rem 1rem;
        font-weight: 500;
    }

    .accordion-button:not(.collapsed) {
        background-color: #e9ecef;
        color: #4a6bff;
    }

    .accordion-body {
        padding: 0;
    }

    .chapter-dimension-item {
        cursor: pointer;
        padding: 0.5rem 1rem 0.5rem 2rem;
        transition: all 0.2s ease;
        border-left: 0;
        border-right: 0;
        border-radius: 0;
    }

    .chapter-dimension-item:hover {
        background-color: #f8f9fa;
    }

    .chapter-dimension-item.active {
        background-color: #e9ecef;
        font-weight: bold;
        color: #4a6bff;
        border-left: 3px solid #4a6bff;
    }

    .template-content {
        white-space: pre-wrap;
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        border: 1px solid #dee2e6;
        min-height: 400px;
        max-height: 70vh;
        overflow-y: auto;
    }

    .progress-badge {
        font-size: 0.8rem;
        padding: 0.2rem 0.5rem;
        margin-left: 0.5rem;
        vertical-align: middle;
    }

    .dimension-progress {
        display: inline-block;
        width: 50px;
        height: 6px;
        background-color: #e9ecef;
        border-radius: 3px;
        margin-left: 0.5rem;
        vertical-align: middle;
    }

    .dimension-progress-bar {
        height: 100%;
        background-color: #4a6bff;
        border-radius: 3px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="mb-0">{{ preset.title }} - 预设模板</h1>
                    <div>
                        <a href="{{ url_for('v3.view_preset', preset_id=preset.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回预设详情
                        </a>
                        <a href="{{ url_for('v3.console') }}" class="btn btn-outline-primary ms-2">
                            <i class="fas fa-terminal me-1"></i>控制台
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <p class="lead">知识库预设模板是基于参考蓝本的分析结果生成的，包含整本书的15个预设维度模板和章节列表。</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>提示：</strong> 请从左侧选择要查看的维度或章节，右侧将显示对应的预设模板内容。
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <ul class="nav nav-tabs card-header-tabs" id="templateTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="book-tab" data-bs-toggle="tab" data-bs-target="#book" type="button" role="tab" aria-controls="book" aria-selected="true">
                                <i class="fas fa-book me-1"></i>整本书
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">
                                <i class="fas fa-list-ol me-1"></i>章节
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body p-0">
                    <div class="tab-content" id="templateTabContent">
                        <!-- 整本书标签页 -->
                        <div class="tab-pane fade show active" id="book" role="tabpanel" aria-labelledby="book-tab">
                            <div class="row g-0">
                                <div class="col-md-3 p-3 border-end">
                                    <h5 class="mb-3">维度列表</h5>
                                    <div class="list-group">
                                        {% for dimension in dimensions %}
                                        <a href="javascript:void(0)" class="list-group-item list-group-item-action dimension-item" data-dimension="{{ dimension.key }}">
                                            <i class="{{ dimension.icon }} me-2"></i>{{ dimension.name }}
                                        </a>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="col-md-9 p-3">
                                    <div id="bookTemplateContent" class="template-content markdown-body">
                                        <div class="text-center py-5">
                                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                            <h4>请选择维度</h4>
                                            <p class="text-muted">从左侧选择一个维度，查看对应的预设模板内容。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 章节标签页 -->
                        <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                            <div class="row g-0">
                                <div class="col-md-3 p-3 border-end">
                                    <h5 class="mb-3">章节列表</h5>
                                    <div class="accordion" id="chaptersAccordion">
                                        {% if chapters and chapters|length > 0 %}
                                            {% for chapter in chapters %}
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="chapter-heading-{{ chapter.id }}">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter-collapse-{{ chapter.id }}" aria-expanded="false" aria-controls="chapter-collapse-{{ chapter.id }}">
                                                        第{{ chapter.chapter_number }}章: {{ chapter.title }}
                                                    </button>
                                                </h2>
                                                <div id="chapter-collapse-{{ chapter.id }}" class="accordion-collapse collapse" aria-labelledby="chapter-heading-{{ chapter.id }}" data-bs-parent="#chaptersAccordion">
                                                    <div class="accordion-body p-0">
                                                        <div class="list-group list-group-flush">
                                                            {% for dimension in dimensions %}
                                                            <a href="javascript:void(0)" class="list-group-item list-group-item-action chapter-dimension-item"
                                                               data-chapter-id="{{ chapter.id }}" data-dimension="{{ dimension.key }}">
                                                                <i class="{{ dimension.icon }} me-2"></i>{{ dimension.name }}
                                                            </a>
                                                            {% endfor %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        {% else %}
                                            <div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                <strong>未找到参考蓝本的章节列表</strong>，请确保：
                                                <ol>
                                                    <li>已正确设置参考蓝本（参考蓝本ID: {{ novel_id }}）</li>
                                                    <li>参考蓝本包含章节</li>
                                                    <li>知识库与参考蓝本正确关联</li>
                                                </ol>
                                                <p>您可以返回控制台，重新设置参考蓝本或创建新的知识库。</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-9 p-3">
                                    <div id="chapterTemplateContent" class="template-content markdown-body">
                                        <div class="text-center py-5">
                                            <i class="fas fa-list-ol fa-3x text-muted mb-3"></i>
                                            <h4>请选择章节和维度</h4>
                                            <p class="text-muted">从左侧先选择一个章节，然后选择一个维度，查看对应的预设模板内容。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script src="{{ url_for('static', filename='js/knowledge-base-compact.js') }}"></script>
<script>
    $(document).ready(function() {
        // 定义变量
        var selectedChapterId = null;
        var selectedDimension = null;
        var bookTemplateContent = {};
        var chapterTemplateContent = {};
        var templateId = "{{ novel_id }}"; // 参考蓝本ID

        // 页面加载时清除缓存
        console.log("页面加载，清除缓存...");
        $.ajax({
            url: "/api/clear_template_cache",
            type: "POST",
            success: function(response) {
                console.log("页面加载时缓存清除成功:", response);
            },
            error: function(xhr) {
                console.log("页面加载时缓存清除失败:", xhr.status);
            }
        });

        // 选择整本书维度
        $('.dimension-item').click(function() {
            $('.dimension-item').removeClass('active');
            $(this).addClass('active');

            var dimension = $(this).data('dimension');
            selectedDimension = dimension;

            // 显示加载中
            $('#bookTemplateContent').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中，请稍候...</p></div>');

            console.log("尝试获取维度分析:", dimension, "模板ID:", templateId);

            // 添加时间戳和强制刷新参数，防止缓存
            var timestamp = new Date().getTime();
            var apiUrl = "/api/template/" + templateId + "/analysis/" + dimension + "?force_refresh=true&timestamp=" + timestamp;

            console.log("请求API:", apiUrl);

            // 先清除缓存
            $.ajax({
                url: "/api/clear_template_cache",
                type: "POST",
                success: function(response) {
                    console.log("缓存清除成功:", response);

                    // 然后请求最新数据
                    $.ajax({
                        url: apiUrl,
                        type: "GET",
                        cache: false, // 禁用jQuery缓存
                        headers: {
                            "Cache-Control": "no-cache, no-store, must-revalidate",
                            "Pragma": "no-cache",
                            "Expires": "0"
                        },
                        success: function(response) {
                            console.log("新API成功:", response);
                            if (response.success && response.analysis && response.analysis.result) {
                                // 有分析结果，显示分析结果
                                var analyzedContent = response.analysis.result;
                                generateBookTemplate(dimension, analyzedContent);
                            } else {
                                console.log("新API返回成功但无分析结果，尝试旧API");
                                // 尝试旧的API路径
                                tryOldApi(dimension);
                            }
                        },
                        error: function(xhr) {
                            console.log("新API请求错误，尝试旧API:", xhr.status);
                            // 尝试旧的API路径
                            tryOldApi(dimension);
                        }
                    });
                },
                error: function(xhr) {
                    console.log("缓存清除失败:", xhr.status);

                    // 即使缓存清除失败，也尝试请求数据
                    $.ajax({
                        url: apiUrl,
                        type: "GET",
                        cache: false,
                        success: function(response) {
                            console.log("新API成功:", response);
                            if (response.success && response.analysis && response.analysis.result) {
                                var analyzedContent = response.analysis.result;
                                generateBookTemplate(dimension, analyzedContent);
                            } else {
                                tryOldApi(dimension);
                            }
                        },
                        error: function(xhr) {
                            tryOldApi(dimension);
                        }
                    });
                }
            });

            function tryOldApi(dimension) {
                $.ajax({
                    url: "/novel/" + templateId + "/analysis/" + dimension,
                    type: "GET",
                    success: function(response) {
                        console.log("旧API成功:", response);
                        // 处理旧API响应
                        if (response.success && response.analysis && response.analysis.result) {
                            var analyzedContent = response.analysis.result;
                            generateBookTemplate(dimension, analyzedContent);
                        } else {
                            console.log("旧API也没有返回有效结果，生成默认模板");
                            generateBookTemplate(dimension, "");
                        }
                    },
                    error: function(xhr) {
                        console.log("旧API也失败，生成默认模板:", xhr.status);
                        generateBookTemplate(dimension, "");
                    }
                });
            }
        });

        // 章节维度点击事件
        $('.chapter-dimension-item').click(function() {
            $('.chapter-dimension-item').removeClass('active');
            $(this).addClass('active');

            var chapterId = $(this).data('chapter-id');
            var dimension = $(this).data('dimension');
            selectedChapterId = chapterId;
            selectedChapterDimension = dimension;

            // 显示加载中
            $('#chapterTemplateContent').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中，请稍候...</p></div>');

            console.log("尝试获取章节维度分析:", chapterId, dimension, "模板ID:", templateId);

            // 添加时间戳和强制刷新参数，防止缓存
            var timestamp = new Date().getTime();
            var apiUrl = "/api/template/" + templateId + "/chapter/" + chapterId + "/analysis/" + dimension + "?force_refresh=true&timestamp=" + timestamp;

            console.log("请求章节API:", apiUrl);

            // 先清除缓存
            $.ajax({
                url: "/api/clear_template_cache",
                type: "POST",
                success: function(response) {
                    console.log("缓存清除成功:", response);

                    // 然后请求最新数据
                    $.ajax({
                        url: apiUrl,
                        type: "GET",
                        cache: false, // 禁用jQuery缓存
                        headers: {
                            "Cache-Control": "no-cache, no-store, must-revalidate",
                            "Pragma": "no-cache",
                            "Expires": "0"
                        },
                        success: function(response) {
                            console.log("新章节API成功:", response);
                            if (response.success && response.analysis && response.analysis.result) {
                                // 有分析结果，显示分析结果
                                var analyzedContent = response.analysis.result;
                                generateChapterTemplate(chapterId, dimension, analyzedContent);
                            } else {
                                console.log("新章节API返回成功但无分析结果，尝试旧API");
                                // 尝试旧的API路径
                                tryOldChapterApi(chapterId, dimension);
                            }
                        },
                        error: function(xhr) {
                            console.log("新章节API请求错误，尝试旧API:", xhr.status);
                            // 尝试旧的API路径
                            tryOldChapterApi(chapterId, dimension);
                        }
                    });
                },
                error: function(xhr) {
                    console.log("缓存清除失败:", xhr.status);

                    // 即使缓存清除失败，也尝试请求数据
                    $.ajax({
                        url: apiUrl,
                        type: "GET",
                        cache: false,
                        success: function(response) {
                            console.log("新章节API成功:", response);
                            if (response.success && response.analysis && response.analysis.result) {
                                var analyzedContent = response.analysis.result;
                                generateChapterTemplate(chapterId, dimension, analyzedContent);
                            } else {
                                tryOldChapterApi(chapterId, dimension);
                            }
                        },
                        error: function(xhr) {
                            tryOldChapterApi(chapterId, dimension);
                        }
                    });
                }
            });

            function tryOldChapterApi(chapterId, dimension) {
                $.ajax({
                    url: "/novel/" + templateId + "/chapter/" + chapterId + "/analysis/" + dimension,
                    type: "GET",
                    success: function(response) {
                        console.log("旧章节API成功:", response);
                        // 处理旧API响应
                        if (response.success && response.analysis && response.analysis.result) {
                            var analyzedContent = response.analysis.result;
                            generateChapterTemplate(chapterId, dimension, analyzedContent);
                        } else {
                            console.log("旧章节API也没有返回有效结果，生成默认模板");
                            generateChapterTemplate(chapterId, dimension, "");
                        }
                    },
                    error: function(xhr) {
                        console.log("旧章节API也失败，生成默认模板:", xhr.status);
                        generateChapterTemplate(chapterId, dimension, "");
                    }
                });
            }
        });

        // 生成整本书预设模板
        function generateBookTemplate(dimension, analysisResult) {
            // 获取维度名称
            var dimensionName = $('.dimension-item[data-dimension="' + dimension + '"]').text().trim();

            // 构建预设模板内容
            var templateContent = '# {{ preset.title }} - ' + dimensionName + ' 预设模板\n\n';

            // 添加基本信息部分
            templateContent += '## 基本信息\n';
            templateContent += '- 知识库: {{ preset.title }}\n';
            templateContent += '- 维度: ' + dimensionName + '\n';
            templateContent += '- 创建时间: {{ preset.created_at }}\n\n';

            // 添加分析结果（如果有）
            if (analysisResult && analysisResult.trim() !== "") {
                templateContent += '## 分析结果\n' + analysisResult + '\n\n';
            }

            // 添加维度说明部分
            templateContent += '## 维度说明\n';

            // 根据不同维度添加不同的说明
            switch(dimension) {
                case 'language_style':
                    templateContent += '语言风格是指作者在写作中使用的独特表达方式，包括词汇选择、句式结构、修辞手法等。良好的语言风格能够增强作品的表现力和感染力，使读者更好地理解和感受作品内容。\n\n';
                    break;
                case 'rhythm_pacing':
                    templateContent += '节奏节拍是指小说情节发展的快慢变化和韵律感，包括场景转换、情节推进、紧张与舒缓的交替等。合理的节奏节拍能够保持读者的阅读兴趣，增强作品的张力和感染力。\n\n';
                    break;
                case 'structure':
                    templateContent += '结构分析是指小说的整体架构和组织方式，包括章节安排、情节线索、叙事框架等。良好的结构能够使作品逻辑清晰、层次分明，增强作品的整体性和连贯性。\n\n';
                    break;
                case 'sentence_variation':
                    templateContent += '句式变化是指作者在写作中使用的句子长度、复杂度和多样性，包括简单句、复合句、长句、短句等的交替使用。丰富的句式变化能够增强作品的节奏感和表现力，避免行文单调。\n\n';
                    break;
                case 'paragraph_length':
                    templateContent += '段落长度是指作者在写作中使用的段落长短变化和组织方式，包括长段落、短段落、过渡段落等的交替使用。合理的段落长度能够增强作品的可读性和节奏感，使读者更容易理解和感受作品内容。\n\n';
                    break;
                case 'perspective_shifts':
                    templateContent += '视角转换是指小说叙事视角的变化和转换，包括第一人称、第三人称、全知视角等的交替使用。灵活的视角转换能够增强作品的表现力和多样性，使读者从不同角度理解和感受作品内容。\n\n';
                    break;
                case 'paragraph_flow':
                    templateContent += '段落流畅度是指段落之间的连贯性和过渡，包括段落之间的逻辑关系、情感连接、场景转换等。良好的段落流畅度能够使作品行文流畅、自然，增强作品的整体性和连贯性。\n\n';
                    break;
                case 'novel_characteristics':
                    templateContent += '小说特点是指作品的独特风格和特征，包括主题表达、人物塑造、情节设计、环境描写等方面的特色。鲜明的小说特点能够增强作品的辨识度和吸引力，使作品在众多作品中脱颖而出。\n\n';
                    break;
                case 'world_building':
                    templateContent += '世界构建是指小说世界观的构建方式，包括地理环境、历史背景、社会制度、文化习俗等方面的设定。丰富而合理的世界构建能够增强作品的真实感和沉浸感，使读者更好地理解和感受作品内容。\n\n';
                    break;
                case 'character_relationships':
                    templateContent += '人物关系是指小说中角色之间的互动和关系，包括亲情、友情、爱情、敌对关系等。复杂而真实的人物关系能够增强作品的深度和感染力，使读者更好地理解和感受人物的情感和成长。\n\n';
                    break;
                case 'opening_effectiveness':
                    templateContent += '开篇效果是指小说开头的设计和效果，包括场景设置、人物引入、悬念设计等。良好的开篇效果能够迅速吸引读者的注意力，引导读者进入小说世界，为后续情节发展奠定基础。\n\n';
                    break;
                case 'climax_pacing':
                    templateContent += '高潮节奏是指小说高潮部分的节奏和张力，包括情节推进、冲突升级、悬念设计等。合理的高潮节奏能够增强作品的戏剧性和感染力，使读者更好地理解和感受作品的主题和情感。\n\n';
                    break;
                case 'chapter_outline':
                    templateContent += '章纲分析是指各章节的结构和功能，包括章节主题、情节推进、人物发展等。清晰的章纲能够使作品结构更加合理，情节发展更加流畅，增强作品的整体性和连贯性。\n\n';
                    break;
                case 'outline_analysis':
                    templateContent += '大纲分析是指小说的整体结构和情节发展，包括主要情节线索、次要情节线索、情节转折点等。合理的大纲能够使作品结构清晰、情节紧凑，增强作品的整体性和连贯性。\n\n';
                    break;
                case 'popular_tropes':
                    templateContent += '热梗统计是指小说中使用的流行元素和套路，包括常见情节、人物类型、场景设置等。了解热梗的使用情况能够帮助作者避免套路化，增强作品的创新性和独特性。\n\n';
                    break;
                default:
                    templateContent += '该维度是小说分析的重要组成部分，通过对该维度的分析，可以更好地理解和把握小说的特点和风格。\n\n';
            }

            // 添加预设模板部分
            templateContent += '## 预设模板\n\n';
            templateContent += '### ' + dimensionName + '模板\n\n';

            // 根据不同维度添加不同的模板内容
            switch(dimension) {
                case 'language_style':
                    templateContent += '#### 词汇选择\n- 常用词汇：\n- 特色词汇：\n- 专业术语：\n- 方言俚语：\n\n';
                    templateContent += '#### 句式结构\n- 常用句式：\n- 特色句式：\n- 句长变化：\n- 语气特点：\n\n';
                    templateContent += '#### 修辞手法\n- 常用修辞：\n- 特色修辞：\n- 修辞效果：\n- 修辞频率：\n\n';
                    templateContent += '#### 语气语调\n- 叙述语气：\n- 对话语气：\n- 情感表达：\n- 语调变化：\n\n';
                    break;
                case 'rhythm_pacing':
                    templateContent += '#### 场景节奏\n- 场景转换：\n- 场景长度：\n- 场景密度：\n- 场景变化：\n\n';
                    templateContent += '#### 情节节奏\n- 情节推进：\n- 情节密度：\n- 情节变化：\n- 情节转折：\n\n';
                    templateContent += '#### 紧张舒缓\n- 紧张段落：\n- 舒缓段落：\n- 节奏变化：\n- 情感波动：\n\n';
                    templateContent += '#### 时间流速\n- 时间压缩：\n- 时间延展：\n- 时间跳跃：\n- 时间停滞：\n\n';
                    break;
                // 其他维度的模板内容...
                default:
                    templateContent += '#### 基本要素\n- 主要特点：\n- 变化规律：\n- 效果评估：\n- 创新之处：\n\n';
                    templateContent += '#### 应用建议\n- 适用场景：\n- 注意事项：\n- 常见问题：\n- 优化方向：\n\n';
            }

            // 添加应用指南
            templateContent += '## 应用指南\n\n';
            templateContent += '1. 本预设模板基于知识库内容生成，可根据实际需求调整\n';
            templateContent += '2. 使用时可选择性填充相关部分，不必拘泥于模板格式\n';
            templateContent += '3. 建议结合参考蓝本的分析结果，进一步丰富预设内容\n';
            templateContent += '4. 预设模板可导出为Markdown文件，方便离线使用和编辑\n';

            // 保存内容到变量
            bookTemplateContent[dimension] = templateContent;

            // 使用marked.js渲染Markdown内容
            var renderedContent = marked.parse(templateContent);

            // 添加导出按钮和标题
            var headerHtml =
                '<div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">' +
                    '<h3>' + dimensionName + ' 预设模板</h3>' +
                    '<button class="btn btn-sm btn-outline-primary export-template-btn" data-template-type="book" data-dimension="' + dimension + '">' +
                        '<i class="fas fa-download me-1"></i>导出模板' +
                    '</button>' +
                '</div>';

            $('#bookTemplateContent').html(headerHtml + renderedContent);

            // 绑定导出按钮事件
            $('.export-template-btn[data-template-type="book"]').click(function() {
                var dimension = $(this).data('dimension');
                exportTemplate('book', dimension);
            });
        }

        // 生成章节预设模板
        function generateChapterTemplate(chapterId, dimension, analysisResult) {
            // 获取章节标题和维度名称
            var chapterTitle = $('#chapter-heading-' + chapterId + ' .accordion-button').text().trim();
            var dimensionName = $('.chapter-dimension-item[data-chapter-id="' + chapterId + '"][data-dimension="' + dimension + '"]').text().trim();

            // 构建预设模板内容
            var templateContent = '# ' + chapterTitle + ' - ' + dimensionName + ' 预设模板\n\n';

            // 添加基本信息部分
            templateContent += '## 章节信息\n';
            templateContent += '- 知识库: {{ preset.title }}\n';
            templateContent += '- 章节: ' + chapterTitle + '\n';
            templateContent += '- 维度: ' + dimensionName + '\n\n';

            // 添加分析结果（如果有）
            if (analysisResult && analysisResult.trim() !== "") {
                templateContent += '## 分析结果\n' + analysisResult + '\n\n';
            }

            // 添加维度说明部分
            templateContent += '## 维度说明\n';

            // 根据不同维度添加不同的说明（与整本书相同）
            switch(dimension) {
                case 'language_style':
                    templateContent += '语言风格是指作者在写作中使用的独特表达方式，包括词汇选择、句式结构、修辞手法等。良好的语言风格能够增强作品的表现力和感染力，使读者更好地理解和感受作品内容。\n\n';
                    break;
                // 其他维度的说明...与整本书相同
                default:
                    templateContent += '该维度是小说分析的重要组成部分，通过对该维度的分析，可以更好地理解和把握小说的特点和风格。\n\n';
            }

            // 添加预设模板部分
            templateContent += '## 章节预设模板\n\n';
            templateContent += '### ' + dimensionName + '模板\n\n';

            // 根据不同维度添加不同的模板内容
            switch(dimension) {
                case 'language_style':
                    templateContent += '#### 词汇选择\n- 常用词汇：\n- 特色词汇：\n- 专业术语：\n- 方言俚语：\n\n';
                    templateContent += '#### 句式结构\n- 常用句式：\n- 特色句式：\n- 句长变化：\n- 语气特点：\n\n';
                    templateContent += '#### 修辞手法\n- 常用修辞：\n- 特色修辞：\n- 修辞效果：\n- 修辞频率：\n\n';
                    templateContent += '#### 语气语调\n- 叙述语气：\n- 对话语气：\n- 情感表达：\n- 语调变化：\n\n';
                    break;
                // 其他维度的模板内容...与整本书类似
                default:
                    templateContent += '#### 基本要素\n- 主要特点：\n- 变化规律：\n- 效果评估：\n- 创新之处：\n\n';
                    templateContent += '#### 应用建议\n- 适用场景：\n- 注意事项：\n- 常见问题：\n- 优化方向：\n\n';
            }

            // 添加与前后章节的关系
            templateContent += '## 与前后章节的关系\n\n';
            templateContent += '### 与前一章节的关系\n- 情节连接：\n- 人物发展：\n- 环境变化：\n- 主题延续：\n\n';
            templateContent += '### 与后一章节的关系\n- 情节铺垫：\n- 人物伏笔：\n- 环境过渡：\n- 主题深化：\n\n';

            // 添加应用指南
            templateContent += '## 应用指南\n\n';
            templateContent += '1. 本章节预设模板基于知识库内容生成，可根据实际需求调整\n';
            templateContent += '2. 使用时可选择性填充相关部分，不必拘泥于模板格式\n';
            templateContent += '3. 建议结合参考蓝本的章节分析结果，进一步丰富预设内容\n';
            templateContent += '4. 注意与前后章节的连贯性和一致性，确保整体风格的统一\n';

            // 保存内容到变量
            if (!chapterTemplateContent[chapterId]) {
                chapterTemplateContent[chapterId] = {};
            }
            chapterTemplateContent[chapterId][dimension] = templateContent;

            // 使用marked.js渲染Markdown内容
            var renderedContent = marked.parse(templateContent);

            // 添加导出按钮和标题
            var headerHtml =
                '<div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">' +
                    '<h3>' + chapterTitle + ' - ' + dimensionName + ' 预设模板</h3>' +
                    '<button class="btn btn-sm btn-outline-primary export-chapter-btn" data-chapter-id="' + chapterId + '" data-dimension="' + dimension + '">' +
                        '<i class="fas fa-download me-1"></i>导出模板' +
                    '</button>' +
                '</div>';

            $('#chapterTemplateContent').html(headerHtml + renderedContent);

            // 绑定导出按钮事件
            $('.export-chapter-btn').click(function() {
                var chapterId = $(this).data('chapter-id');
                var dimension = $(this).data('dimension');
                exportChapter(chapterId, dimension);
            });
        }

        // 导出模板函数
        function exportTemplate(type, dimension) {
            let content, filename;
            if (type === 'book') {
                content = bookTemplateContent[dimension];
                var dimensionName = $('.dimension-item[data-dimension="' + dimension + '"]').text().trim();
                filename = '{{ preset.title }}_' + dimensionName + '_预设模板.md';
            }

            if (content) {
                var blob = new Blob([content], { type: 'text/markdown' });
                var url = URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }

        // 导出章节模板函数
        function exportChapter(chapterId, dimension) {
            if (chapterTemplateContent[chapterId] && chapterTemplateContent[chapterId][dimension]) {
                var content = chapterTemplateContent[chapterId][dimension];
                var chapterTitle = $('#chapter-heading-' + chapterId + ' .accordion-button').text().trim();
                var dimensionName = $('.chapter-dimension-item[data-chapter-id="' + chapterId + '"][data-dimension="' + dimension + '"]').text().trim();
                var filename = '{{ preset.title }}_' + chapterTitle + '_' + dimensionName + '_预设模板.md';

                var blob = new Blob([content], { type: 'text/markdown' });
                var url = URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }

        // 初始化页面
        function initPage() {
            // 自动选择第一个维度
            if ($('.dimension-item').length > 0) {
                $('.dimension-item').first().click();
            }
        }

        // 初始化页面
        initPage();
    });
</script>
{% endblock %}