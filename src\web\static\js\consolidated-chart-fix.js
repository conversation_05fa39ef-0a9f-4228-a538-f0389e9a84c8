/**
 * 九猫 - 统一图表修复脚本
 * 解决所有图表相关问题，包括Canvas重用和replaceChild错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('统一图表修复脚本已加载');
    
    // 存储图表实例的全局对象
    window.chartInstances = window.chartInstances || {};
    
    // 安全地销毁图表
    function safeDestroyChart(canvas) {
        try {
            // 检查是否有ID
            const chartId = canvas.id || canvas.getAttribute('data-chart-id');
            
            // 如果有ID，尝试从全局对象中获取图表实例
            if (chartId && window.chartInstances[chartId]) {
                console.log('销毁现有图表实例:', chartId);
                window.chartInstances[chartId].destroy();
                delete window.chartInstances[chartId];
            }
            
            // 检查是否有维度属性
            const dimension = canvas.getAttribute('data-dimension');
            if (dimension && window.chartInstances[dimension]) {
                console.log('销毁维度图表实例:', dimension);
                window.chartInstances[dimension].destroy();
                delete window.chartInstances[dimension];
            }
            
            // 如果canvas上直接存储了图表实例
            if (canvas._chart) {
                console.log('销毁canvas上存储的图表实例');
                canvas._chart.destroy();
                delete canvas._chart;
            }
        } catch (e) {
            console.log('销毁图表时出错 (可以忽略):', e.message);
        }
    }
    
    // 安全地创建图表
    function safeCreateChart(canvas, config) {
        try {
            // 确保Chart.js已加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载，无法创建图表');
                return null;
            }
            
            // 先销毁现有图表
            safeDestroyChart(canvas);
            
            // 如果canvas已被使用，创建一个新的canvas替换它
            try {
                // 获取上下文
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    throw new Error('无法获取canvas上下文');
                }
                
                // 创建新图表
                const chart = new Chart(ctx, config);
                
                // 存储图表实例
                const chartId = canvas.id || canvas.getAttribute('data-chart-id');
                if (chartId) {
                    window.chartInstances[chartId] = chart;
                }
                
                const dimension = canvas.getAttribute('data-dimension');
                if (dimension) {
                    window.chartInstances[dimension] = chart;
                }
                
                // 直接在canvas上存储图表实例
                canvas._chart = chart;
                
                return chart;
            } catch (e) {
                // 如果是Canvas已在使用的错误，尝试更激进的修复
                if (e.message && e.message.includes('Canvas is already in use')) {
                    console.log('检测到Canvas已在使用错误，尝试创建新canvas');
                    
                    // 创建新的canvas元素替换旧的
                    const oldCanvas = canvas;
                    const newCanvas = document.createElement('canvas');
                    
                    // 复制属性
                    if (oldCanvas.id) newCanvas.id = oldCanvas.id;
                    if (oldCanvas.className) newCanvas.className = oldCanvas.className;
                    if (oldCanvas.getAttribute('data-dimension')) 
                        newCanvas.setAttribute('data-dimension', oldCanvas.getAttribute('data-dimension'));
                    if (oldCanvas.getAttribute('data-chart-id'))
                        newCanvas.setAttribute('data-chart-id', oldCanvas.getAttribute('data-chart-id'));
                    
                    newCanvas.width = oldCanvas.width || 400;
                    newCanvas.height = oldCanvas.height || 300;
                    
                    // 安全替换canvas
                    try {
                        oldCanvas.parentNode.replaceChild(newCanvas, oldCanvas);
                    } catch (replaceError) {
                        console.error('替换canvas时出错:', replaceError);
                        // 如果替换失败，尝试先移除旧节点，再添加新节点
                        if (oldCanvas.parentNode) {
                            oldCanvas.parentNode.removeChild(oldCanvas);
                            oldCanvas.parentNode.appendChild(newCanvas);
                        }
                    }
                    
                    // 在新canvas上创建图表
                    const ctx = newCanvas.getContext('2d');
                    const chart = new Chart(ctx, config);
                    
                    // 存储图表实例
                    if (newCanvas.id) {
                        window.chartInstances[newCanvas.id] = chart;
                    }
                    
                    const dimension = newCanvas.getAttribute('data-dimension');
                    if (dimension) {
                        window.chartInstances[dimension] = chart;
                    }
                    
                    // 直接在canvas上存储图表实例
                    newCanvas._chart = chart;
                    
                    return chart;
                } else {
                    console.error('创建图表时出错:', e);
                    return null;
                }
            }
        } catch (e) {
            console.error('创建图表的过程中出错:', e);
            return null;
        }
    }
    
    // 创建雷达图
    function createRadarChart(canvas, data) {
        console.log('创建雷达图');
        
        // 默认数据
        const defaultData = {
            labels: ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'],
            datasets: [{
                label: '分析性能指标',
                data: [0, 0, 0, 0, 0],
                backgroundColor: 'rgba(74, 107, 223, 0.2)',
                borderColor: 'rgba(74, 107, 223, 1)',
                borderWidth: 2,
                pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
            }]
        };
        
        // 使用提供的数据或默认数据
        const chartData = data || defaultData;
        
        // 配置
        const config = {
            type: 'radar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        angleLines: {
                            display: true
                        },
                        suggestedMin: 0,
                        suggestedMax: 100
                    }
                }
            }
        };
        
        // 创建图表
        return safeCreateChart(canvas, config);
    }
    
    // 创建柱状图
    function createBarChart(canvas, data) {
        console.log('创建柱状图');
        
        // 默认数据
        const defaultData = {
            labels: ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'],
            datasets: [{
                label: '分析性能指标',
                data: [0, 0, 0, 0, 0],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(255, 206, 86, 0.2)',
                    'rgba(153, 102, 255, 0.2)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(153, 102, 255, 1)'
                ],
                borderWidth: 1
            }]
        };
        
        // 使用提供的数据或默认数据
        const chartData = data || defaultData;
        
        // 配置
        const config = {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        };
        
        // 创建图表
        return safeCreateChart(canvas, config);
    }
    
    // 修复所有图表
    function fixAllCharts() {
        console.log('开始修复所有图表');
        
        // 查找所有canvas元素
        const canvases = document.querySelectorAll('canvas');
        console.log(`找到 ${canvases.length} 个canvas元素`);
        
        // 如果没有找到canvas元素，可能需要创建
        if (canvases.length === 0) {
            console.log('未找到canvas元素，尝试查找图表容器');
            
            // 查找图表容器
            const containers = document.querySelectorAll('.chart-container, .analysis-chart, .visualization-container');
            
            containers.forEach(function(container) {
                console.log('为容器创建canvas元素');
                
                // 创建canvas元素
                const canvas = document.createElement('canvas');
                canvas.width = 400;
                canvas.height = 300;
                canvas.className = 'analysis-chart';
                
                // 如果容器有data-dimension属性，复制到canvas
                const dimension = container.getAttribute('data-dimension');
                if (dimension) {
                    canvas.setAttribute('data-dimension', dimension);
                    canvas.setAttribute('data-chart-id', `chart-${dimension}`);
                }
                
                // 添加到容器
                container.appendChild(canvas);
                
                // 根据维度创建适当的图表
                if (dimension === 'character_relationships') {
                    createRadarChart(canvas);
                } else {
                    // 默认创建柱状图
                    createBarChart(canvas);
                }
            });
            
            return;
        }
        
        // 处理所有找到的canvas元素
        canvases.forEach(function(canvas) {
            try {
                // 根据canvas ID或data-dimension属性创建适当的图表
                if (canvas.id === 'radarChart' || canvas.getAttribute('data-chart-type') === 'radar') {
                    createRadarChart(canvas);
                } else if (canvas.id === 'barChart' || canvas.getAttribute('data-chart-type') === 'bar') {
                    createBarChart(canvas);
                } else {
                    // 检查是否有维度属性
                    const dimension = canvas.getAttribute('data-dimension');
                    if (dimension === 'character_relationships') {
                        // 人物关系分析通常使用雷达图
                        createRadarChart(canvas);
                    } else if (dimension) {
                        // 其他维度默认使用柱状图
                        createBarChart(canvas);
                    } else {
                        console.log(`跳过未知canvas: ${canvas.id || '无ID'}`);
                    }
                }
            } catch (e) {
                console.error(`处理canvas时出错:`, e);
            }
        });
    }
    
    // 修复replaceChild错误
    function fixReplaceChildError() {
        // 保存原始的replaceChild方法
        const originalReplaceChild = Node.prototype.replaceChild;
        
        // 重写replaceChild方法
        Node.prototype.replaceChild = function(newChild, oldChild) {
            try {
                // 尝试使用原始方法
                return originalReplaceChild.call(this, newChild, oldChild);
            } catch (e) {
                console.error('replaceChild错误:', e.message);
                
                // 检查是否包含"Unexpected identifier"错误
                if (e.message && e.message.includes('Unexpected identifier')) {
                    console.log('检测到特定的replaceChild错误，尝试安全替换');
                    
                    try {
                        // 安全替换：先移除旧节点，再添加新节点
                        if (this.contains(oldChild)) {
                            this.removeChild(oldChild);
                        }
                        this.appendChild(newChild);
                        return newChild;
                    } catch (e2) {
                        console.error('替代方法也失败:', e2.message);
                    }
                }
                
                // 如果特定修复失败，尝试通用修复
                try {
                    // 创建一个新的节点，复制newChild的内容
                    const tempNode = document.createElement(newChild.nodeName);
                    
                    // 复制属性
                    for (let i = 0; i < newChild.attributes.length; i++) {
                        const attr = newChild.attributes[i];
                        tempNode.setAttribute(attr.name, attr.value);
                    }
                    
                    // 复制内容
                    tempNode.innerHTML = newChild.innerHTML;
                    
                    // 替换节点
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }
                    this.appendChild(tempNode);
                    
                    return tempNode;
                } catch (e3) {
                    console.error('所有替代方法都失败:', e3.message);
                    // 最后的尝试：直接返回newChild，不执行实际替换
                    return newChild;
                }
            }
        };
    }
    
    // 修复initCharts函数
    function fixInitChartsFunction() {
        // 如果页面上有initCharts函数，修复它
        if (typeof window.initCharts === 'function') {
            console.log('修复initCharts函数');
            
            // 保存原始函数
            const originalInitCharts = window.initCharts;
            
            // 重写函数
            window.initCharts = function() {
                try {
                    // 先销毁所有现有图表
                    document.querySelectorAll('canvas').forEach(safeDestroyChart);
                    
                    // 调用原始函数
                    return originalInitCharts();
                } catch (e) {
                    console.error('调用initCharts函数时出错:', e);
                    
                    // 如果原始函数失败，尝试使用我们的修复
                    setTimeout(fixAllCharts, 100);
                }
            };
        }
        
        // 如果页面上有initializeCharts函数，修复它
        if (typeof window.initializeCharts === 'function') {
            console.log('修复initializeCharts函数');
            
            // 保存原始函数
            const originalInitializeCharts = window.initializeCharts;
            
            // 重写函数
            window.initializeCharts = function() {
                try {
                    // 先销毁所有现有图表
                    document.querySelectorAll('canvas').forEach(safeDestroyChart);
                    
                    // 调用原始函数
                    return originalInitializeCharts();
                } catch (e) {
                    console.error('调用initializeCharts函数时出错:', e);
                    
                    // 如果原始函数失败，尝试使用我们的修复
                    setTimeout(fixAllCharts, 100);
                }
            };
        }
    }
    
    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始执行统一图表修复');
        
        // 修复replaceChild错误
        fixReplaceChildError();
        
        // 修复initCharts函数
        fixInitChartsFunction();
        
        // 如果Chart.js已加载，直接执行修复
        if (typeof Chart !== 'undefined') {
            console.log('Chart.js已加载，直接执行修复');
            setTimeout(fixAllCharts, 500);
        } else {
            console.log('Chart.js未加载，等待加载完成后执行修复');
            
            // 监听Chart.js加载
            const checkChartInterval = setInterval(function() {
                if (typeof Chart !== 'undefined') {
                    console.log('检测到Chart.js已加载，执行修复');
                    clearInterval(checkChartInterval);
                    setTimeout(fixAllCharts, 500);
                }
            }, 200);
            
            // 设置超时，避免无限等待
            setTimeout(function() {
                clearInterval(checkChartInterval);
            }, 10000);
        }
        
        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                // 处理Chart.js相关错误
                if (event.error.message.includes('Chart with ID') || 
                    event.error.message.includes('Canvas is already in use')) {
                    console.error('捕获到Chart.js相关错误:', event.error.message);
                    
                    // 尝试修复
                    setTimeout(fixAllCharts, 100);
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
                
                // 处理replaceChild错误
                if (event.error.message.includes('Failed to execute \'replaceChild\' on \'Node\'')) {
                    console.error('捕获到replaceChild错误:', event.error.message);
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);
    });
    
    // 导出公共函数
    window.chartFix = {
        safeDestroyChart: safeDestroyChart,
        safeCreateChart: safeCreateChart,
        createRadarChart: createRadarChart,
        createBarChart: createBarChart,
        fixAllCharts: fixAllCharts
    };
})();
