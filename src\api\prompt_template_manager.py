"""
九猫系统 - 提示词模板管理器
专门用于管理默认版和精简版提示词，确保明确区分，避免混淆
"""

import logging
from typing import Dict, Any, Optional
from enum import Enum

logger = logging.getLogger(__name__)

class PromptTemplateType(Enum):
    """提示词模板类型枚举"""
    DEFAULT = "default"
    SIMPLIFIED = "simplified"

class PromptTemplateManager:
    """提示词模板管理器"""

    @staticmethod
    def get_analysis_prompt(dimension: str, template_type: PromptTemplateType) -> str:
        """
        获取分析提示词

        Args:
            dimension: 分析维度
            template_type: 提示词模板类型

        Returns:
            对应的提示词
        """
        if template_type == PromptTemplateType.SIMPLIFIED:
            return PromptTemplateManager._get_simplified_analysis_prompt(dimension)
        else:
            return PromptTemplateManager._get_default_analysis_prompt(dimension)

    @staticmethod
    def _get_default_analysis_prompt(dimension: str) -> str:
        """获取默认版分析提示词"""

        # 默认版通用要求
        default_requirements = """
【默认版分析要求 - 完整详细模式】
1. 必须使用原文：分析必须100%基于小说的实际内容，不允许泛泛而谈或猜测
2. 必须详细叙述原文内容：极其详尽地描述小说中的关键场景、事件、对话和人物心理活动
3. 不限字数，越详细越好：分析结果必须极其详细，不设字数限制，至少8000字以上
4. 必须提供深入的分析：不仅描述现象，还要分析原因、效果和意义
5. 语言必须通俗易懂：使用清晰、生动的语言，避免过多专业术语

分析内容必须包含：
- 详细的文本引用：引用原文中的具体段落、句子或词语，作为分析的依据
- 完整的分析框架：使用清晰的结构，包括总体特点、具体表现、变化规律、效果评估和创新点
- 深入的解读：对引用的文本进行深入解读，分析其含义、作用和效果
- 全面的覆盖：分析应覆盖小说的各个部分，不仅限于开头或结尾
- 具体的例证：使用具体的例子支持分析观点，不做空泛的评价

以上要求是首要执行的，必须执行的！在进行分析时，请首先考虑这些要求。
"""

        # 默认版特定维度要求
        dimension_specific = {
            "language_style": """
【语言风格分析 - 默认版详细要求】
分析内容必须包含：
- 词汇选择分析（至少2000字）：分析作者使用的词汇特点，如正式/口语、具体/抽象、简单/复杂等
- 句式结构解析（至少2000字）：分析句子长短变化、复杂程度和语法特点
- 修辞手法识别（至少2000字）：识别比喻、拟人、排比等修辞手法的使用及其效果
- 语气语调评估（至少2000字）：分析文本的整体语气，如幽默、严肃、讽刺、温暖等
- 风格一致性考察（至少2000字）：评估风格在全文中的一致性或有意的变化
""",
            "rhythm_pacing": """
【节奏节拍分析 - 默认版详细要求】
分析内容必须包含：
- 整体节奏特点（至少2000字）：分析作品的整体节奏特点，如快速紧凑型、舒缓铺陈型、波动变化型等
- 章节节奏分析（至少2000字）：分析不同章节的节奏特点及其变化
- 场景节奏控制（至少2000字）：分析关键场景的节奏控制方式及其效果
- 情节推进速度（至少2000字）：分析情节推进的速度变化及其原因
- 高潮铺垫技巧（至少2000字）：分析高潮前的铺垫和节奏控制技巧
""",
            "chapter_outline": """
【章纲分析 - 默认版详细要求】
内容重现部分必须包含：
- 详细的章节概述（至少5000字）：按照章节顺序，详细描述每个章节的主要内容和情节发展
- 完整的事件过程（至少5000字）：按照发生顺序详细描述小说中的主要事件的起因、经过和结果
- 人物发展轨迹（至少3000字）：描述主要人物在不同章节中的表现和发展变化
- 情节线索梳理（至少2000字）：梳理小说中的主要情节线索，分析它们如何在各章节中展开和发展
""",
            "outline_analysis": """
【大纲分析 - 默认版详细要求】
内容重现部分必须包含：
- 详细的场景描述（至少5000字）：描述作品中的关键场景的环境、氛围和背景，包括光线、声音、气味等感官细节
- 完整的事件过程（至少5000字）：按照发生顺序详细描述整部作品中的每个重要事件的起因、经过和结果
- 人物对话与互动（至少5000字）：完整记录主要人物的对话内容，尽可能使用原文中的重要对话
- 人物心理活动（至少3000字）：深入描述人物的心理活动、情感变化和内心冲突
- 情节发展脉络（至少2000字）：清晰展示情节如何从开始发展到结束，包括转折点和高潮
"""
        }

        specific_requirement = dimension_specific.get(dimension, "")
        return default_requirements + specific_requirement

    @staticmethod
    def _get_simplified_analysis_prompt(dimension: str) -> str:
        """获取精简版分析提示词"""

        # 精简版通用要求
        simplified_requirements = """
【精简版分析要求 - 降本增效模式】
1. 基于原文分析：分析必须基于小说的实际内容，避免泛泛而谈
2. 重点突出：重点描述关键场景、事件和人物关系
3. 适度详细：分析结果要详细但精炼，控制在合理范围内
4. 核心分析：重点分析原因、效果和关键意义
5. 简洁明了：使用清晰、简洁的语言表达

分析内容应包含：
- 关键文本引用：引用原文中的重要段落或句子作为依据
- 核心分析框架：包括主要特点、具体表现、关键效果
- 重点解读：对关键内容进行深入解读
- 主要覆盖：分析覆盖小说的主要部分
- 典型例证：使用典型例子支持分析观点

以上要求针对精简版模式优化，在保证质量的同时提高效率。
"""

        # 精简版特定维度要求
        dimension_specific = {
            "language_style": """
【语言风格分析 - 精简版高效要求】
分析内容应包含：
- 词汇选择分析：分析作者使用的主要词汇特点
- 句式结构解析：分析句子的主要特点和语法特征
- 修辞手法识别：识别主要修辞手法及其效果
- 语气语调评估：分析文本的整体语气特点
- 风格一致性考察：评估风格的一致性或变化
""",
            "rhythm_pacing": """
【节奏节拍分析 - 精简版高效要求】
分析内容应包含：
- 整体节奏特点：分析作品的主要节奏特点
- 章节节奏分析：分析主要章节的节奏特点
- 场景节奏控制：分析关键场景的节奏控制方式
- 情节推进速度：分析情节推进的主要特点
- 高潮铺垫技巧：分析高潮前的主要铺垫技巧
""",
            "chapter_outline": """
【章纲分析 - 精简版高效要求】
内容重现应包含：
- 主要章节概述：按章节顺序描述主要内容和情节发展
- 关键事件过程：描述主要事件的起因、经过和结果
- 人物发展要点：描述主要人物的表现和发展变化
- 情节线索梳理：梳理主要情节线索及其发展
""",
            "outline_analysis": """
【大纲分析 - 精简版高效要求】
内容重现应包含：
- 主要场景描述：描述关键场景的环境和氛围
- 关键事件过程：描述重要事件的起因、经过和结果
- 人物对话要点：记录主要人物的重要对话内容
- 人物心理活动：描述人物的主要心理活动和情感变化
- 情节发展脉络：展示情节的主要发展过程
"""
        }

        specific_requirement = dimension_specific.get(dimension, "")
        return simplified_requirements + specific_requirement

    @staticmethod
    def enhance_prompt_with_template(base_prompt: str, dimension: str, template_type: PromptTemplateType) -> str:
        """
        使用指定模板增强提示词

        Args:
            base_prompt: 基础提示词
            dimension: 分析维度
            template_type: 模板类型

        Returns:
            增强后的提示词
        """
        enhancement = PromptTemplateManager.get_analysis_prompt(dimension, template_type)

        # 检查是否已经包含增强内容
        if template_type == PromptTemplateType.SIMPLIFIED:
            if "精简版分析要求" in base_prompt:
                logger.info(f"提示词已包含精简版增强内容，无需再次添加")
                return base_prompt
        else:
            if "默认版分析要求" in base_prompt:
                logger.info(f"提示词已包含默认版增强内容，无需再次添加")
                return base_prompt

        # 在提示词开头添加增强内容
        enhanced_prompt = enhancement + "\n\n" + base_prompt

        logger.info(f"成功增强{dimension}维度的分析提示词（{template_type.value}版）")
        return enhanced_prompt

    @staticmethod
    def get_template_type_from_string(template_str: str) -> PromptTemplateType:
        """
        从字符串获取模板类型

        Args:
            template_str: 模板字符串

        Returns:
            模板类型枚举
        """
        if template_str == "simplified":
            return PromptTemplateType.SIMPLIFIED
        else:
            return PromptTemplateType.DEFAULT

    @staticmethod
    def get_template_info(template_type: PromptTemplateType) -> Dict[str, Any]:
        """
        获取模板信息

        Args:
            template_type: 模板类型

        Returns:
            模板信息字典
        """
        if template_type == PromptTemplateType.SIMPLIFIED:
            return {
                "name": "精简版",
                "description": "降本增效模式",
                "features": [
                    "适度详细分析",
                    "简洁明了表达",
                    "重点突出",
                    "控制输出长度",
                    "优化API成本"
                ],
                "icon": "💰",
                "color": "orange"
            }
        else:
            return {
                "name": "默认版",
                "description": "完整详细模式",
                "features": [
                    "极其详细分析",
                    "不限字数",
                    "全面覆盖",
                    "深入解读",
                    "完整分析框架"
                ],
                "icon": "📚",
                "color": "blue"
            }
