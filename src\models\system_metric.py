"""
系统指标模型
用于存储系统监控数据
"""
from datetime import datetime
from sqlalchemy import Column, Integer, Float, String, DateTime, Text

from src.database import Base

class SystemMetric(Base):
    """系统指标模型，用于存储系统监控数据"""
    
    __tablename__ = 'system_metrics'
    
    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, default=datetime.now, index=True)
    
    # 系统资源指标
    cpu_usage = Column(Float, nullable=False)  # CPU使用率（百分比）
    memory_usage = Column(Float, nullable=False)  # 内存使用率（百分比）
    disk_usage = Column(Float, nullable=False)  # 磁盘使用率（百分比）
    
    # API调用指标
    api_calls_total = Column(Integer, default=0)  # API总调用次数
    api_calls_success = Column(Integer, default=0)  # API成功调用次数
    api_calls_error = Column(Integer, default=0)  # API错误调用次数
    api_calls_timeout = Column(Integer, default=0)  # API超时调用次数
    api_avg_response_time = Column(Float, default=0)  # API平均响应时间（毫秒）
    
    # 数据库连接池指标
    db_pool_size = Column(Integer, default=0)  # 连接池大小
    db_active_connections = Column(Integer, default=0)  # 活跃连接数
    db_overflow = Column(Integer, default=0)  # 溢出连接数
    db_usage_percent = Column(Float, default=0)  # 连接池使用率（百分比）
    
    def __repr__(self):
        return f"<SystemMetric(id={self.id}, timestamp={self.timestamp}, cpu_usage={self.cpu_usage}%)>"
