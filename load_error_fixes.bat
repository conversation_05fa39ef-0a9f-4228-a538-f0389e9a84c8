@echo off
echo 九猫小说分析系统 - 加载错误修复脚本

:: 检查脚本文件是否存在
if not exist "fix_chart_errors.js" (
    echo 错误修复脚本不存在，请确保fix_chart_errors.js文件在当前目录
    pause
    exit /b 1
)

:: 检查模板目录
set "template_dir=templates\js"
if not exist "%template_dir%" (
    echo 创建脚本目录: %template_dir%
    mkdir "%template_dir%" 2>nul
)

:: 复制修复脚本到模板目录
echo 复制错误修复脚本到模板目录...
copy /Y "fix_chart_errors.js" "%template_dir%\" >nul

:: 检查静态目录
set "static_dir=static\js"
if not exist "%static_dir%" (
    echo 创建静态目录: %static_dir%
    mkdir "%static_dir%" 2>nul
)

:: 复制修复脚本到静态目录
echo 复制错误修复脚本到静态目录...
copy /Y "fix_chart_errors.js" "%static_dir%\" >nul

echo.
echo 错误修复脚本已成功安装
echo 现在启动九猫小说分析系统...
echo.

:: 启动九猫小说分析系统
call 启动九猫.bat

if %ERRORLEVEL% NEQ 0 (
    echo 启动失败，请手动运行启动九猫.bat
    pause
    exit /b 1
)

exit /b 0 