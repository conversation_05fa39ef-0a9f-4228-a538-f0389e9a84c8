@echo off
echo 九猫 - 全面修复脚本
echo =====================
echo.

echo 步骤1: 修复数据库结构...
python fix_analysis_logs_column.py
if %ERRORLEVEL% NEQ 0 (
    echo 修复数据库结构失败，请查看日志
    pause
    exit /b 1
)
echo 数据库结构修复完成！
echo.

echo 步骤2: 全面修复数据库...
python fix_database_comprehensive.py
if %ERRORLEVEL% NEQ 0 (
    echo 全面修复数据库失败，请查看日志
    pause
    exit /b 1
)
echo 全面修复数据库完成！
echo.

echo 步骤3: 切换到增强版数据库连接管理模块...
python switch_to_enhanced_connection.py
if %ERRORLEVEL% NEQ 0 (
    echo 切换到增强版数据库连接管理模块失败，请查看日志
    pause
    exit /b 1
)
echo 切换到增强版数据库连接管理模块完成！
echo.

echo 所有修复步骤已完成！
echo 请重启应用以应用所有更改。
echo.

pause
