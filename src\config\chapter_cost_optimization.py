"""
九猫系统章节分析累积成本优化配置
解决章节分析结果累积导致成本呈指数级增长的问题
"""

import logging
from typing import Dict, Any, List
import re

logger = logging.getLogger(__name__)

class ChapterCostOptimization:
    """章节分析累积成本优化管理器"""
    
    @staticmethod
    def optimize_previous_analyses(previous_analyses: List[Dict], 
                                 current_chapter_number: int,
                                 dimension: str,
                                 prompt_template: str = "default") -> str:
        """
        优化前序章节分析结果，控制累积成本
        
        Args:
            previous_analyses: 前序章节分析结果列表
            current_chapter_number: 当前章节编号
            dimension: 分析维度
            prompt_template: 提示词模板
            
        Returns:
            优化后的前序章节摘要
        """
        if not previous_analyses:
            return ""
        
        # 根据提示词模板选择不同的优化策略
        if prompt_template == "simplified":
            return ChapterCostOptimization._create_simplified_summary(
                previous_analyses, current_chapter_number, dimension
            )
        else:
            return ChapterCostOptimization._create_balanced_summary(
                previous_analyses, current_chapter_number, dimension
            )
    
    @staticmethod
    def _create_simplified_summary(previous_analyses: List[Dict], 
                                 current_chapter_number: int,
                                 dimension: str) -> str:
        """
        创建精简版前序章节摘要（激进降本）
        
        策略：
        1. 只保留最近2个章节的核心信息
        2. 每个章节摘要限制在200字以内
        3. 只提取关键结论，去除详细分析过程
        4. 使用关键词提取而非全文引用
        """
        try:
            # 限制章节数量：只保留最近2个章节
            recent_analyses = previous_analyses[-2:] if len(previous_analyses) > 2 else previous_analyses
            
            summary_parts = []
            summary_parts.append("## 前序章节核心要点：\n")
            
            for analysis in recent_analyses:
                chapter_title = analysis.get("chapter_title", f"第{analysis.get('chapter_number')}章")
                content = analysis.get("content", "")
                
                # 提取核心要点（限制200字）
                core_points = ChapterCostOptimization._extract_core_points(content, max_length=200)
                
                summary_parts.append(f"### {chapter_title}：\n{core_points}\n")
            
            result = "".join(summary_parts)
            logger.info(f"[精简版摘要] 生成前序章节摘要，原始{len(previous_analyses)}章节 → 精简{len(recent_analyses)}章节，总长度: {len(result)}字符")
            
            return result
            
        except Exception as e:
            logger.error(f"创建精简版前序章节摘要时出错: {str(e)}")
            return "## 前序章节信息获取失败\n"
    
    @staticmethod
    def _create_balanced_summary(previous_analyses: List[Dict], 
                               current_chapter_number: int,
                               dimension: str) -> str:
        """
        创建平衡版前序章节摘要（温和降本）
        
        策略：
        1. 保留最近3个章节的信息
        2. 每个章节摘要限制在400字以内
        3. 保留关键分析结论和重要细节
        4. 使用智能摘要而非简单截取
        """
        try:
            # 限制章节数量：保留最近3个章节
            recent_analyses = previous_analyses[-3:] if len(previous_analyses) > 3 else previous_analyses
            
            summary_parts = []
            summary_parts.append("## 前序章节分析摘要：\n")
            
            for analysis in recent_analyses:
                chapter_title = analysis.get("chapter_title", f"第{analysis.get('chapter_number')}章")
                content = analysis.get("content", "")
                
                # 创建智能摘要（限制400字）
                smart_summary = ChapterCostOptimization._create_smart_summary(content, max_length=400)
                
                summary_parts.append(f"### {chapter_title}：\n{smart_summary}\n")
            
            result = "".join(summary_parts)
            logger.info(f"[平衡版摘要] 生成前序章节摘要，原始{len(previous_analyses)}章节 → 保留{len(recent_analyses)}章节，总长度: {len(result)}字符")
            
            return result
            
        except Exception as e:
            logger.error(f"创建平衡版前序章节摘要时出错: {str(e)}")
            return "## 前序章节分析摘要获取失败\n"
    
    @staticmethod
    def _extract_core_points(content: str, max_length: int = 200) -> str:
        """
        提取内容的核心要点（激进压缩）
        
        策略：
        1. 提取结论性句子
        2. 去除详细分析过程
        3. 保留关键词和核心观点
        4. 严格控制字数
        """
        if not content:
            return "暂无分析内容"
        
        try:
            # 按段落分割
            paragraphs = content.split('\n')
            
            core_points = []
            current_length = 0
            
            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                if not paragraph:
                    continue
                
                # 优先提取结论性内容
                if any(keyword in paragraph for keyword in ['总结', '结论', '特点', '主要', '核心', '关键']):
                    # 简化句子，去除冗余
                    simplified = ChapterCostOptimization._simplify_sentence(paragraph)
                    
                    if current_length + len(simplified) <= max_length:
                        core_points.append(simplified)
                        current_length += len(simplified)
                    else:
                        # 如果超出长度限制，截取剩余部分
                        remaining = max_length - current_length
                        if remaining > 20:  # 至少保留20字符
                            core_points.append(simplified[:remaining] + "...")
                        break
            
            result = "；".join(core_points) if core_points else content[:max_length] + "..."
            return result
            
        except Exception as e:
            logger.error(f"提取核心要点时出错: {str(e)}")
            return content[:max_length] + "..."
    
    @staticmethod
    def _create_smart_summary(content: str, max_length: int = 400) -> str:
        """
        创建智能摘要（温和压缩）
        
        策略：
        1. 保留重要分析结论
        2. 简化详细描述
        3. 保持逻辑连贯性
        4. 控制总体长度
        """
        if not content:
            return "暂无分析内容"
        
        try:
            # 按段落分割
            paragraphs = content.split('\n')
            
            summary_parts = []
            current_length = 0
            
            # 优先级关键词
            high_priority = ['主要特点', '核心', '关键', '重要', '显著']
            medium_priority = ['分析', '表现', '体现', '反映']
            
            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                if not paragraph or paragraph.startswith('#'):
                    continue
                
                # 计算段落优先级
                priority_score = 0
                for keyword in high_priority:
                    if keyword in paragraph:
                        priority_score += 3
                for keyword in medium_priority:
                    if keyword in paragraph:
                        priority_score += 1
                
                # 如果是高优先级内容或当前长度还有余量
                if priority_score > 0 or current_length < max_length * 0.7:
                    # 适度简化段落
                    simplified = ChapterCostOptimization._moderate_simplify(paragraph)
                    
                    if current_length + len(simplified) <= max_length:
                        summary_parts.append(simplified)
                        current_length += len(simplified)
                    else:
                        # 部分截取
                        remaining = max_length - current_length
                        if remaining > 30:
                            summary_parts.append(simplified[:remaining] + "...")
                        break
            
            result = "\n".join(summary_parts) if summary_parts else content[:max_length] + "..."
            return result
            
        except Exception as e:
            logger.error(f"创建智能摘要时出错: {str(e)}")
            return content[:max_length] + "..."
    
    @staticmethod
    def _simplify_sentence(sentence: str) -> str:
        """简化句子，去除冗余表达"""
        # 去除常见的冗余表达
        redundant_patterns = [
            r'可以看出，?',
            r'我们可以发现，?',
            r'通过分析，?',
            r'从.*来看，?',
            r'总的来说，?',
            r'综上所述，?',
            r'具体来说，?',
            r'换句话说，?'
        ]
        
        result = sentence
        for pattern in redundant_patterns:
            result = re.sub(pattern, '', result)
        
        # 去除多余的标点符号
        result = re.sub(r'[，。]{2,}', '。', result)
        result = re.sub(r'^\s*[，。]', '', result)
        
        return result.strip()
    
    @staticmethod
    def _moderate_simplify(paragraph: str) -> str:
        """适度简化段落，保持核心信息"""
        # 保留重要信息，简化描述性内容
        if len(paragraph) <= 100:
            return paragraph
        
        # 分句处理
        sentences = re.split(r'[。！？]', paragraph)
        important_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # 保留包含关键信息的句子
            if any(keyword in sentence for keyword in ['特点', '表现', '体现', '主要', '重要', '关键']):
                important_sentences.append(sentence)
            elif len(important_sentences) < 2:  # 至少保留2句
                important_sentences.append(sentence)
        
        result = '。'.join(important_sentences[:3])  # 最多保留3句
        return result + '。' if result and not result.endswith('。') else result
    
    @staticmethod
    def calculate_cost_reduction(original_length: int, optimized_length: int) -> Dict[str, Any]:
        """
        计算成本降低效果
        
        Args:
            original_length: 原始内容长度
            optimized_length: 优化后内容长度
            
        Returns:
            成本降低统计信息
        """
        if original_length == 0:
            return {"reduction_rate": 0, "saved_tokens": 0, "estimated_cost_saving": 0}
        
        reduction_rate = (original_length - optimized_length) / original_length
        saved_tokens = (original_length - optimized_length) * 0.75  # 估算token数
        estimated_cost_saving = saved_tokens * 0.0001  # 估算成本节省（元）
        
        return {
            "original_length": original_length,
            "optimized_length": optimized_length,
            "reduction_rate": reduction_rate,
            "saved_tokens": int(saved_tokens),
            "estimated_cost_saving": round(estimated_cost_saving, 4)
        }
    
    @staticmethod
    def get_optimization_config(prompt_template: str = "default") -> Dict[str, Any]:
        """
        获取优化配置
        
        Args:
            prompt_template: 提示词模板
            
        Returns:
            优化配置
        """
        if prompt_template == "simplified":
            return {
                "max_previous_chapters": 2,      # 最多保留2个前序章节
                "max_summary_length": 200,      # 每章节摘要最大长度
                "compression_level": "aggressive",  # 压缩级别：激进
                "preserve_reasoning": False,     # 不保留推理过程
                "extract_keywords_only": True,  # 只提取关键词
                "cost_priority": "high"         # 成本优先级：高
            }
        else:
            return {
                "max_previous_chapters": 3,      # 最多保留3个前序章节
                "max_summary_length": 400,      # 每章节摘要最大长度
                "compression_level": "moderate", # 压缩级别：温和
                "preserve_reasoning": True,      # 保留部分推理过程
                "extract_keywords_only": False, # 保留重要分析内容
                "cost_priority": "balanced"     # 成本优先级：平衡
            }
