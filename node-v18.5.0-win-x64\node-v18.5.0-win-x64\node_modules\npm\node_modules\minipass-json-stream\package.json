{"name": "minipass-json-stream", "version": "1.0.1", "description": "Like JSONStream, but using Minipass streams", "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "MIT", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"JSONStream": "^1.3.5", "tap": "^14.6.9"}, "dependencies": {"jsonparse": "^1.3.1", "minipass": "^3.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/npm/minipass-json-stream.git"}, "keywords": ["stream", "json", "parse", "minipass", "JSONStream"], "files": ["index.js"]}