<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫 - 错误处理测试</title>
    
    <!-- 加载错误处理脚本 -->
    <script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';console.error('错误处理脚本加载失败，尝试备用路径')"></script>
    <script src="/static/js/console-error-fix.js" onerror="this.onerror=null;this.src='/direct-static/js/console-error-fix.js';console.error('控制台错误修复脚本加载失败，尝试备用路径')"></script>
    <script src="/static/js/console-format-fix.js" onerror="this.onerror=null;this.src='/direct-static/js/console-format-fix.js';console.error('控制台格式修复脚本加载失败，尝试备用路径')"></script>
    
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            padding: 0;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #343a40;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .btn:hover {
            background-color: #0069d9;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>九猫 - 错误处理测试</h1>
        
        <p>这个页面用于测试错误处理和控制台错误格式修复功能。</p>
        
        <div>
            <button id="test-error" class="btn btn-danger">测试错误</button>
            <button id="test-format" class="btn btn-danger">测试格式错误</button>
            <button id="test-json" class="btn btn-danger">测试JSON错误</button>
            <a href="/" class="btn">返回首页</a>
        </div>
        
        <div class="result" id="result">
            <p>请点击上方按钮测试错误处理功能，然后在浏览器控制台中查看结果。</p>
        </div>
    </div>
    
    <script>
        // 测试错误按钮
        document.getElementById('test-error').addEventListener('click', function() {
            try {
                // 故意制造一个错误
                const obj = null;
                obj.nonExistentMethod();
            } catch (e) {
                // 显示错误信息
                document.getElementById('result').innerHTML = `
                    <p>已触发错误: ${e.message}</p>
                    <p>请在浏览器控制台中查看格式化的错误信息。</p>
                `;
                
                // 抛出错误，让它显示在控制台中
                throw e;
            }
        });
        
        // 测试格式错误按钮
        document.getElementById('test-format').addEventListener('click', function() {
            // 模拟格式错误的控制台输出
            console.error('人类易读模式: ${errorCounter}].${message}');
            console.error('位置: ${source}.行: ${lineno}.列: ${colno}');
            console.error('堆栈: ${error.stack}');
            
            document.getElementById('result').innerHTML = `
                <p>已触发格式错误</p>
                <p>请在浏览器控制台中查看是否正确修复了格式问题。</p>
            `;
        });
        
        // 测试JSON错误按钮
        document.getElementById('test-json').addEventListener('click', function() {
            try {
                // 故意制造一个JSON解析错误
                JSON.parse('{name: "test"');
            } catch (e) {
                // 显示错误信息
                document.getElementById('result').innerHTML = `
                    <p>已触发JSON错误: ${e.message}</p>
                    <p>请在浏览器控制台中查看是否正确处理了JSON错误。</p>
                `;
                
                // 抛出错误，让它显示在控制台中
                throw e;
            }
        });
    </script>
</body>
</html>
