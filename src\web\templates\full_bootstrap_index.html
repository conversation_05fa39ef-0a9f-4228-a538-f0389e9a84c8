{% extends "base.html" %}

{% block title %}首页 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">九猫小说文本分析系统</h4>
            </div>
            <div class="card-body">
                <p class="lead">欢迎使用九猫小说文本分析系统，这是一款专门为作家和编辑设计的文本分析工具。</p>
                
                <h5 class="mt-4">主要功能：</h5>
                <ul class="list-group list-group-flush mb-4">
                    <li class="list-group-item">
                        <i class="fas fa-chart-line me-2 text-primary"></i>
                        文本高潮节奏分析
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-project-diagram me-2 text-primary"></i>
                        人物关系网络可视化
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-heart me-2 text-primary"></i>
                        情感曲线追踪
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-feather-alt me-2 text-primary"></i>
                        文体风格识别
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-sitemap me-2 text-primary"></i>
                        情节结构分析
                    </li>
                </ul>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong> 点击上方导航栏中的"小说列表"按钮，查看已分析的小说或上传新小说开始分析。
                </div>
            </div>
        </div>
        
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">系统优化</h5>
            </div>
            <div class="card-body">
                <p>九猫小说分析系统已应用内存优化和堆栈溢出保护，确保稳定运行。</p>
                <div class="progress mb-3">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">系统性能 75%</div>
                </div>
                <div id="optimization-status" class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    系统正常运行中...
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">快速开始</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/dashboard" class="btn btn-success btn-lg mb-3">
                        <i class="fas fa-tachometer-alt me-1"></i> 进入数据中心
                    </a>
                    
                    <a href="/upload" class="btn btn-primary btn-lg mb-3">
                        <i class="fas fa-upload me-1"></i> 开始新的分析
                    </a>
                </div>
                
                <h6 class="border-bottom pb-2 mb-3">最近的小说：</h6>
                <ul class="list-group">
                    {% if novels %}
                        {% for novel in novels[:3] %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="text-decoration-none">
                                        {{ novel.title }}
                                    </a>
                                    <small class="text-muted d-block">{{ novel.created_at.strftime('%Y-%m-%d') }}</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">
                                    <i class="fas fa-eye"></i>
                                </span>
                            </li>
                        {% endfor %}
                    {% else %}
                        <li class="list-group-item text-center text-muted">
                            <i class="fas fa-book me-1"></i> 暂无小说
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
        
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">系统状态</h5>
            </div>
            <div class="card-body">
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        系统版本
                        <span class="badge bg-primary">1.0.0</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        分析引擎
                        <span class="badge bg-success">已连接</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        数据库
                        <span class="badge bg-info">已连接</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        内存使用率
                        <span id="memory-usage" class="badge bg-warning text-dark">加载中...</span>
                    </li>
                </ul>
                
                <button class="btn btn-outline-info w-100 mt-3" onclick="refreshSystemStatus()">
                    <i class="fas fa-sync-alt me-1"></i> 刷新状态
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">API 状态</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>DeepSeek R1 API</h6>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 90%" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100">可用性 90%</div>
                        </div>
                        <p class="small text-muted">API密钥: sk-9ff...f3b1</p>
                    </div>
                    <div class="col-md-6">
                        <h6>通义千问-Plus-Latest API</h6>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-info" role="progressbar" style="width: 85%" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100">可用性 85%</div>
                        </div>
                        <p class="small text-muted">API密钥: sk-6f3...278d</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function refreshSystemStatus() {
        // 模拟刷新系统状态
        const memoryUsage = document.getElementById('memory-usage');
        memoryUsage.innerHTML = '加载中...';
        
        // 模拟API调用
        setTimeout(function() {
            const usage = Math.floor(Math.random() * 30) + 60; // 60-90%
            memoryUsage.innerHTML = usage + '%';
            
            // 根据使用率设置不同的颜色
            if (usage < 70) {
                memoryUsage.className = 'badge bg-success';
            } else if (usage < 85) {
                memoryUsage.className = 'badge bg-warning text-dark';
            } else {
                memoryUsage.className = 'badge bg-danger';
            }
        }, 1000);
    }
    
    // 页面加载时刷新系统状态
    document.addEventListener('DOMContentLoaded', refreshSystemStatus);
</script>
{% endblock %}
