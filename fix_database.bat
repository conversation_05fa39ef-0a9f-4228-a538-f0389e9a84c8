@echo off
echo 开始修复数据库...

REM 查找数据库文件
set DB_FILE=data\novels.db
if not exist %DB_FILE% (
    echo 错误：找不到数据库文件 %DB_FILE%
    pause
    exit /b 1
)

REM 创建临时SQL文件
set SQL_FILE=fix_database.sql
echo -- 修复character_relationships分析结果 > %SQL_FILE%
echo BEGIN TRANSACTION; >> %SQL_FILE%
echo -- 删除错误的分析结果 >> %SQL_FILE%
echo DELETE FROM analysis_results WHERE novel_id = 4 AND dimension = 'character_relationships'; >> %SQL_FILE%
echo -- 插入新的分析结果 >> %SQL_FILE%
echo INSERT INTO analysis_results (novel_id, dimension, content, analysis_metadata, created_at, updated_at) >> %SQL_FILE%
echo VALUES (4, 'character_relationships', >> %SQL_FILE%
echo '# 分析过程中出错\n\n## 错误详情\n分析维度 **character_relationships** 时遇到了问题。\n\n## 错误信息\n```\nname ''stats_start'' is not defined\n```\n\n## 建议操作\n请尝试以下解决方法：\n1. 刷新页面并重新尝试分析\n2. 检查小说文本是否过长或包含特殊字符\n3. 确认API连接正常\n\n如果问题持续存在，请联系系统管理员。', >> %SQL_FILE%
echo '{"processing_time": 0, "chunk_count": 0, "api_calls": 0, "tokens_used": 0, "cost": 0, "fixed_by_script": true, "fix_timestamp": "2025-05-02T18:30:00"}', >> %SQL_FILE%
echo datetime('now'), datetime('now')); >> %SQL_FILE%
echo COMMIT; >> %SQL_FILE%

REM 执行SQL文件
echo 执行SQL修复脚本...
sqlite3 %DB_FILE% < %SQL_FILE%

if %ERRORLEVEL% NEQ 0 (
    echo 错误：执行SQL脚本失败
    pause
    exit /b 1
)

echo 数据库修复成功！
echo 请重启服务器以应用更改。
pause
