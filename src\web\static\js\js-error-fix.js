/**
 * 九猫 - JavaScript错误修复脚本
 * 解决常见的JavaScript运行时错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('JavaScript错误修复脚本已加载');
    
    // 安全地访问对象属性
    function safeGet(obj, path, defaultValue) {
        if (!obj) return defaultValue;
        
        const keys = path.split('.');
        let current = obj;
        
        for (let i = 0; i < keys.length; i++) {
            if (current === null || current === undefined) {
                return defaultValue;
            }
            
            current = current[keys[i]];
        }
        
        return current !== undefined ? current : defaultValue;
    }
    
    // 安全地调用函数
    function safeCall(fn, context, args, defaultValue) {
        if (typeof fn !== 'function') {
            return defaultValue;
        }
        
        try {
            return fn.apply(context, args);
        } catch (e) {
            console.error(`函数调用错误: ${e.message}`);
            return defaultValue;
        }
    }
    
    // 修复数组方法
    function fixArrayMethods() {
        // 确保Array.prototype.push方法安全
        const originalArrayPush = Array.prototype.push;
        Array.prototype.push = function() {
            if (!this) {
                console.error('尝试在undefined或null上调用push方法');
                return 0;
            }
            
            // 如果this不是数组，尝试转换
            if (!Array.isArray(this)) {
                console.warn('在非数组对象上调用push方法，尝试转换');
                try {
                    const arr = [];
                    return originalArrayPush.apply(arr, arguments);
                } catch (e) {
                    console.error(`转换为数组失败: ${e.message}`);
                    return 0;
                }
            }
            
            return originalArrayPush.apply(this, arguments);
        };
        
        // 确保其他常用数组方法安全
        ['pop', 'shift', 'unshift', 'splice', 'slice', 'map', 'filter', 'forEach', 'reduce'].forEach(method => {
            const original = Array.prototype[method];
            Array.prototype[method] = function() {
                if (!this) {
                    console.error(`尝试在undefined或null上调用${method}方法`);
                    return method === 'slice' || method === 'map' || method === 'filter' ? [] : undefined;
                }
                
                // 如果this不是数组，尝试转换
                if (!Array.isArray(this)) {
                    console.warn(`在非数组对象上调用${method}方法，尝试转换`);
                    try {
                        const arr = Array.from(this);
                        return original.apply(arr, arguments);
                    } catch (e) {
                        console.error(`转换为数组失败: ${e.message}`);
                        return method === 'slice' || method === 'map' || method === 'filter' ? [] : undefined;
                    }
                }
                
                return original.apply(this, arguments);
            };
        });
    }
    
    // 修复对象方法
    function fixObjectMethods() {
        // 确保Object.keys方法安全
        const originalObjectKeys = Object.keys;
        Object.keys = function(obj) {
            if (obj === null || obj === undefined) {
                console.warn('尝试在null或undefined上调用Object.keys，返回空数组');
                return [];
            }
            
            return originalObjectKeys(obj);
        };
        
        // 确保Object.values方法安全
        if (Object.values) {
            const originalObjectValues = Object.values;
            Object.values = function(obj) {
                if (obj === null || obj === undefined) {
                    console.warn('尝试在null或undefined上调用Object.values，返回空数组');
                    return [];
                }
                
                return originalObjectValues(obj);
            };
        }
        
        // 确保Object.entries方法安全
        if (Object.entries) {
            const originalObjectEntries = Object.entries;
            Object.entries = function(obj) {
                if (obj === null || obj === undefined) {
                    console.warn('尝试在null或undefined上调用Object.entries，返回空数组');
                    return [];
                }
                
                return originalObjectEntries(obj);
            };
        }
    }
    
    // 修复字符串方法
    function fixStringMethods() {
        // 确保String.prototype方法安全
        ['split', 'replace', 'slice', 'substring', 'indexOf', 'lastIndexOf', 'trim'].forEach(method => {
            const original = String.prototype[method];
            String.prototype[method] = function() {
                if (this === null || this === undefined) {
                    console.warn(`尝试在null或undefined上调用String.${method}，返回空字符串或默认值`);
                    return method === 'split' ? [] : '';
                }
                
                return original.apply(this, arguments);
            };
        });
    }
    
    // 修复DOM方法
    function fixDOMMethods() {
        // 确保document.querySelector方法安全
        const originalQuerySelector = document.querySelector;
        document.querySelector = function(selector) {
            if (!selector) {
                console.warn('document.querySelector调用时选择器为空，返回null');
                return null;
            }
            
            try {
                return originalQuerySelector.call(this, selector);
            } catch (e) {
                console.error(`document.querySelector错误: ${e.message}`);
                return null;
            }
        };
        
        // 确保document.querySelectorAll方法安全
        const originalQuerySelectorAll = document.querySelectorAll;
        document.querySelectorAll = function(selector) {
            if (!selector) {
                console.warn('document.querySelectorAll调用时选择器为空，返回空NodeList');
                return document.createDocumentFragment().childNodes;
            }
            
            try {
                return originalQuerySelectorAll.call(this, selector);
            } catch (e) {
                console.error(`document.querySelectorAll错误: ${e.message}`);
                return document.createDocumentFragment().childNodes;
            }
        };
        
        // 确保Element.prototype.querySelector方法安全
        if (Element.prototype.querySelector) {
            const originalElementQuerySelector = Element.prototype.querySelector;
            Element.prototype.querySelector = function(selector) {
                if (!selector) {
                    console.warn('Element.querySelector调用时选择器为空，返回null');
                    return null;
                }
                
                try {
                    return originalElementQuerySelector.call(this, selector);
                } catch (e) {
                    console.error(`Element.querySelector错误: ${e.message}`);
                    return null;
                }
            };
        }
        
        // 确保Element.prototype.querySelectorAll方法安全
        if (Element.prototype.querySelectorAll) {
            const originalElementQuerySelectorAll = Element.prototype.querySelectorAll;
            Element.prototype.querySelectorAll = function(selector) {
                if (!selector) {
                    console.warn('Element.querySelectorAll调用时选择器为空，返回空NodeList');
                    return document.createDocumentFragment().childNodes;
                }
                
                try {
                    return originalElementQuerySelectorAll.call(this, selector);
                } catch (e) {
                    console.error(`Element.querySelectorAll错误: ${e.message}`);
                    return document.createDocumentFragment().childNodes;
                }
            };
        }
    }
    
    // 修复JSON方法
    function fixJSONMethods() {
        // 确保JSON.parse方法安全
        const originalJSONParse = JSON.parse;
        JSON.parse = function(text, reviver) {
            if (!text) {
                console.warn('JSON.parse调用时文本为空，返回空对象');
                return {};
            }
            
            try {
                return originalJSONParse.call(JSON, text, reviver);
            } catch (e) {
                console.error(`JSON.parse错误: ${e.message}`);
                return {};
            }
        };
        
        // 确保JSON.stringify方法安全
        const originalJSONStringify = JSON.stringify;
        JSON.stringify = function(value, replacer, space) {
            try {
                return originalJSONStringify.call(JSON, value, replacer, space);
            } catch (e) {
                console.error(`JSON.stringify错误: ${e.message}`);
                return '{}';
            }
        };
    }
    
    // 添加全局错误处理
    function addGlobalErrorHandling() {
        window.addEventListener('error', function(event) {
            // 检查是否是TypeError
            if (event.error instanceof TypeError) {
                console.error('捕获到TypeError:', event.error.message);
                
                // 检查是否是"Cannot read property"错误
                if (event.error.message.includes('Cannot read') && 
                    (event.error.message.includes('property') || event.error.message.includes('properties'))) {
                    console.warn('检测到属性访问错误，尝试恢复');
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);
        
        // 添加未处理的Promise拒绝处理
        window.addEventListener('unhandledrejection', function(event) {
            console.error('捕获到未处理的Promise拒绝:', event.reason);
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        });
    }
    
    // 导出工具函数到全局
    window.safeGet = safeGet;
    window.safeCall = safeCall;
    
    // 应用所有修复
    fixArrayMethods();
    fixObjectMethods();
    fixStringMethods();
    fixDOMMethods();
    fixJSONMethods();
    addGlobalErrorHandling();
    
    console.log('JavaScript错误修复脚本应用完成');
})();
