<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫小说分析系统</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #007bff;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .card-header {
            background-color: #f1f1f1;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        .card-body {
            padding: 20px;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0069d9;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        .novel-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .novel-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            transition: transform 0.2s;
        }
        .novel-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .novel-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .novel-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-complete {
            background-color: #28a745;
            color: white;
        }
        .status-analyzing {
            background-color: #ffc107;
            color: #333;
        }
        .status-pending {
            background-color: #6c757d;
            color: white;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
        }
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        footer {
            margin-top: 50px;
            padding: 20px 0;
            background-color: #f1f1f1;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">九猫小说分析系统</div>
            <div>
                <a href="/direct-test" class="btn">测试页面</a>
                <a href="/static/direct-test.html" class="btn btn-secondary">静态测试页面</a>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="alert alert-warning">
            <strong>系统提示：</strong> 系统正在维护中，部分功能可能暂时不可用。我们正在努力修复问题，请稍后再试。
        </div>

        <div class="card">
            <div class="card-header">小说列表</div>
            <div class="card-body">
                <div class="novel-list">
                    <div class="novel-card">
                        <div class="novel-title">封总，太太想跟你离婚很久了</div>
                        <div class="novel-info">
                            <div>字数：约300,000字</div>
                            <div>上传时间：2023-05-15</div>
                        </div>
                        <div>
                            <span class="status-badge status-complete">分析完成</span>
                            <a href="/novel/1" class="btn">查看分析</a>
                        </div>
                    </div>
                    
                    <div class="novel-card">
                        <div class="novel-title">我在末世种田</div>
                        <div class="novel-info">
                            <div>字数：约500,000字</div>
                            <div>上传时间：2023-06-20</div>
                        </div>
                        <div>
                            <span class="status-badge status-complete">分析完成</span>
                            <a href="/novel/2" class="btn">查看分析</a>
                        </div>
                    </div>
                    
                    <div class="novel-card">
                        <div class="novel-title">重生之都市修仙</div>
                        <div class="novel-info">
                            <div>字数：约800,000字</div>
                            <div>上传时间：2023-07-05</div>
                        </div>
                        <div>
                            <span class="status-badge status-analyzing">分析中</span>
                            <a href="/novel/3" class="btn">查看进度</a>
                        </div>
                    </div>
                    
                    <div class="novel-card">
                        <div class="novel-title">斗破苍穹</div>
                        <div class="novel-info">
                            <div>字数：约2,500,000字</div>
                            <div>上传时间：2023-08-10</div>
                        </div>
                        <div>
                            <span class="status-badge status-pending">待分析</span>
                            <a href="/novel/4" class="btn">开始分析</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">上传新小说</div>
            <div class="card-body">
                <p>选择一个TXT文件上传进行分析：</p>
                <form id="upload-form">
                    <input type="file" id="novel-file" accept=".txt" disabled>
                    <button type="submit" class="btn" disabled>上传并分析</button>
                </form>
                <p><small>注意：上传功能暂时不可用，请稍后再试。</small></p>
            </div>
        </div>

        <div class="card">
            <div class="card-header">系统状态</div>
            <div class="card-body">
                <p><strong>API状态：</strong> <span style="color: orange;">部分可用</span></p>
                <p><strong>数据库状态：</strong> <span style="color: green;">正常</span></p>
                <p><strong>内存使用率：</strong> <span id="memory-usage">加载中...</span></p>
                <p><strong>系统版本：</strong> 九猫分析系统 v1.0.5</p>
                <button onclick="checkSystemStatus()" class="btn">刷新状态</button>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>九猫小说分析系统 &copy; 2023-2025 版权所有</p>
            <p>使用DeepSeek R1 API提供AI分析支持</p>
        </div>
    </footer>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('紧急首页已加载');
            document.getElementById('memory-usage').textContent = '约78%';
        });

        // 检查系统状态
        function checkSystemStatus() {
            const memoryUsage = document.getElementById('memory-usage');
            memoryUsage.textContent = '正在检查...';
            
            // 模拟异步请求
            setTimeout(function() {
                const usage = Math.floor(70 + Math.random() * 15);
                memoryUsage.textContent = `约${usage}%`;
                
                // 根据使用率设置颜色
                if (usage > 85) {
                    memoryUsage.style.color = 'red';
                } else if (usage > 75) {
                    memoryUsage.style.color = 'orange';
                } else {
                    memoryUsage.style.color = 'green';
                }
                
                alert('系统状态已刷新！');
            }, 1000);
        }
    </script>
</body>
</html>
