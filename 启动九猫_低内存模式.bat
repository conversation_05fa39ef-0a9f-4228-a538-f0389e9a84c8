@echo off
chcp 65001 >nul
title 九猫小说分析系统 - 低内存模式

echo ===================================
echo    九猫小说分析系统 - 低内存模式
echo ===================================
echo.

:: 检查是否已安装psutil
python -c "import psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 未检测到psutil模块，将尝试安装...
    pip install psutil
    if %errorlevel% neq 0 (
        echo [错误] 安装psutil失败，系统将以有限的内存监控功能运行
        echo 建议手动安装: pip install psutil
        pause
    ) else (
        echo [成功] psutil安装成功
    )
)

:: 检查端口5001是否被占用
echo 检查端口5001是否被占用...
netstat -ano | findstr :5001 | findstr LISTENING >nul
if %errorlevel% equ 0 (
    echo [警告] 端口5001已被占用，尝试终止占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5001 ^| findstr LISTENING') do (
        echo 尝试终止进程ID: %%a
        taskkill /f /pid %%a >nul 2>&1
        if %errorlevel% equ 0 (
            echo [信息] 成功终止占用端口5001的进程。
        ) else (
            echo [警告] 无法终止占用端口5001的进程。可能需要手动终止。
        )
    )
)

:: 设置环境变量
echo 设置环境变量...
set DEBUG=False
set USE_REAL_API=True
set MEMORY_OPTIMIZED=True
set LOW_MEMORY_MODE=True

:: 创建日志目录
if not exist logs mkdir logs

:: 清理旧日志文件（保留最近的3个）
echo 清理旧日志文件...
if exist logs\*.log (
    for /f "skip=3 delims=" %%i in ('dir /b /o-d logs\*.log') do (
        del /q "logs\%%i" >nul 2>&1
    )
)

:: 设置更低的内存警告阈值
set MEMORY_WARNING_THRESHOLD=70
set MEMORY_CRITICAL_THRESHOLD=80

:: 启动九猫系统
echo 启动九猫小说分析系统（低内存模式）...
echo 日志将保存到 logs 目录

:: 使用pythonw.exe启动（无控制台窗口）以减少内存占用
start "" pythonw.exe main.py

:: 等待系统启动
echo 正在启动系统，请稍候...
timeout /t 5 /nobreak >nul

:: 打开浏览器
echo 正在打开浏览器...
start http://localhost:5001

echo.
echo [成功] 系统已启动（低内存模式）。
echo 请在浏览器中使用: http://localhost:5001
echo.
echo 提示: 如需查看日志，请查看 logs 目录下的文件
echo 提示: 如需停止系统，请在任务管理器中结束 pythonw.exe 进程
echo 提示: 低内存模式下，系统将减少日志输出并优化内存使用
echo.
pause
