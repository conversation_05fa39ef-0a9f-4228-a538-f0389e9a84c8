"""
修复章节分析推理过程数据

此脚本用于修复章节分析结果中缺失的推理过程数据。
它会检查所有章节分析结果，如果发现缺少推理过程数据，
会尝试从分析过程记录中恢复或重新生成推理过程数据。
"""

import os
import sys
import logging
import json
import traceback
from datetime import datetime
from sqlalchemy import create_engine, or_
from sqlalchemy.orm import sessionmaker

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_chapter_reasoning_process.log')
    ]
)
logger = logging.getLogger(__name__)

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的模块
try:
    from src.db.connection import Session
    from src.models.chapter_analysis_result import ChapterAnalysisResult
    from src.models.chapter_analysis_process import ChapterAnalysisProcess
    from src.models.chapter import Chapter
    from src.models.novel import Novel
    from reasoning_content_template import format_reasoning_content
except ImportError as e:
    logger.error(f"导入模块时出错: {str(e)}")
    sys.exit(1)

def fix_chapter_reasoning_process(chapter_id=None, dimension=None, force=False):
    """
    修复章节分析推理过程数据

    Args:
        chapter_id: 章节ID，如果为None则处理所有章节
        dimension: 分析维度，如果为None则处理所有维度
        force: 是否强制重新生成推理过程数据，即使已存在
    """
    session = Session()
    try:
        # 构建查询
        query = session.query(ChapterAnalysisResult)

        # 如果指定了章节ID，只处理该章节
        if chapter_id:
            query = query.filter(ChapterAnalysisResult.chapter_id == chapter_id)

        # 如果指定了维度，只处理该维度
        if dimension:
            query = query.filter(ChapterAnalysisResult.dimension == dimension)

        # 如果不强制重新生成，只处理缺少推理过程的结果
        if not force:
            query = query.filter(or_(
                ChapterAnalysisResult.reasoning_content == None,
                ChapterAnalysisResult.reasoning_content == '',
                ChapterAnalysisResult.reasoning_content == '[]',
                ChapterAnalysisResult.reasoning_content == '{}'
            ))

        # 获取所有需要处理的分析结果
        results = query.all()
        logger.info(f"找到 {len(results)} 个需要修复的章节分析结果")

        # 处理每个分析结果
        for result in results:
            try:
                # 获取章节和小说信息
                chapter = session.query(Chapter).filter(Chapter.id == result.chapter_id).first()
                novel = session.query(Novel).filter(Novel.id == result.novel_id).first()

                if not chapter or not novel:
                    logger.warning(f"找不到章节或小说: chapter_id={result.chapter_id}, novel_id={result.novel_id}")
                    continue

                logger.info(f"处理章节分析结果: id={result.id}, chapter_id={result.chapter_id}, dimension={result.dimension}")

                # 尝试从分析过程记录中恢复推理过程数据
                process = session.query(ChapterAnalysisProcess).filter(
                    ChapterAnalysisProcess.result_id == result.id,
                    ChapterAnalysisProcess.processing_stage == "reasoning"
                ).first()

                if process and process.output_text:
                    # 从分析过程记录中恢复推理过程数据
                    reasoning_content = process.output_text
                    logger.info(f"从分析过程记录中恢复推理过程数据: length={len(reasoning_content)}")

                    # 格式化推理过程内容
                    try:
                        formatted_reasoning = format_reasoning_content(reasoning_content)
                        reasoning_content = formatted_reasoning
                        logger.info(f"成功格式化推理过程内容")
                    except Exception as e:
                        logger.error(f"格式化推理过程内容时出错: {str(e)}")

                    # 更新分析结果
                    result.reasoning_content = reasoning_content
                    session.commit()
                    logger.info(f"成功更新推理过程数据")
                elif result.content:
                    # 如果没有分析过程记录，但有分析结果内容，使用内容作为推理过程
                    logger.info(f"使用分析结果内容作为推理过程数据")

                    # 构建推理过程内容
                    reasoning_content = f"""## 分析思路说明：
1. **内容重现与叙事梳理**：全面详细地重现本章节的完整内容，以小说叙述的方式进行
2. **结构功能与定位分析**：全面分析章节的内部结构和功能定位
3. **关键事件与转折点**：详细分析本章节中的关键事件和转折点
4. **人物表现与发展轨迹**：详细分析本章节中主要人物的表现、行动和发展
5. **章节关联与叙事连贯性**：深入分析本章节与前后章节的关系和叙事连贯性
6. **伏笔设置与悬念管理**：分析本章节中的伏笔设置和悬念管理
7. **主题情感与氛围营造**：评估章节的主题明确性和情感基调
8. **创新特色与读者体验**：分析本章节的特色元素和创新点

## 详细分析：
{result.content}
"""

                    # 更新分析结果
                    result.reasoning_content = reasoning_content
                    session.commit()
                    logger.info(f"成功使用分析结果内容作为推理过程数据")
                else:
                    logger.warning(f"无法恢复推理过程数据: 没有分析过程记录和分析结果内容")
            except Exception as e:
                logger.error(f"处理章节分析结果时出错: {str(e)}")
                logger.error(traceback.format_exc())
                session.rollback()

        logger.info(f"完成修复章节分析推理过程数据")
    except Exception as e:
        logger.error(f"修复章节分析推理过程数据时出错: {str(e)}")
        logger.error(traceback.format_exc())
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="修复章节分析推理过程数据")
    parser.add_argument("--chapter_id", type=int, help="章节ID，如果不指定则处理所有章节")
    parser.add_argument("--dimension", type=str, help="分析维度，如果不指定则处理所有维度")
    parser.add_argument("--force", action="store_true", help="是否强制重新生成推理过程数据，即使已存在")
    args = parser.parse_args()

    # 执行修复
    fix_chapter_reasoning_process(args.chapter_id, args.dimension, args.force)
