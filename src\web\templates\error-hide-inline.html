<!-- error-hide-inline.html -->
<!-- 错误提示隐藏内联脚本 -->
<style>
    /* 隐藏错误提示框 */
    div[style*="border: 1px solid rgb(238, 0, 0)"],
    div[style*="border:1px solid #e00"],
    div[style*="border:1px solid red"],
    div[style*="background-color: rgb(255, 236, 236)"],
    div[style*="color: rgb(136, 0, 0)"],
    div[style*="color:#800"],
    div[style*="background-color:#fee"] {
        display: none !important;
        visibility: hidden !important;
        height: 0 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        pointer-events: none !important;
        z-index: -9999 !important;
        clip: rect(0, 0, 0, 0) !important;
        clip-path: inset(50%) !important;
    }
</style>
<script>
    // 错误提示隐藏脚本
    document.addEventListener('DOMContentLoaded', function() {
        // 定期检查并隐藏错误提示框
        function hideErrors() {
            const errorDivs = document.querySelectorAll('div[style*="border: 1px solid rgb(238, 0, 0)"], div[style*="border:1px solid #e00"], div[style*="border:1px solid red"], div[style*="background-color: rgb(255, 236, 236)"], div[style*="color: rgb(136, 0, 0)"], div[style*="color:#800"], div[style*="background-color:#fee"]');

            errorDivs.forEach(function(div) {
                div.style.display = 'none';
                div.style.visibility = 'hidden';
                div.style.height = '0';
                div.style.overflow = 'hidden';
                div.style.opacity = '0';
            });

            if (errorDivs.length > 0) {
                console.log('已隐藏 ' + errorDivs.length + ' 个错误提示框');
            }
        }

        // 立即执行一次
        hideErrors();

        // 每秒检查一次
        setInterval(hideErrors, 1000);

        console.log('错误提示隐藏已应用');
    });
</script>
