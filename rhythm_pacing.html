<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第1章 她爬到了修仙界 - rhythm_pacing 分析 - 九猫</title>

    <!-- 尝试多种路径加载Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/lib/bootstrap.min.css" onerror="this.onerror=null;this.href='/direct-static/css/lib/bootstrap.min.css'">

    <!-- 如果本地加载失败，尝试CDN -->
    <script>
        // 检查Bootstrap CSS是否已加载
        setTimeout(function() {
            var bootstrapLoaded = false;
            var styles = document.styleSheets;
            for (var i = 0; i < styles.length; i++) {
                if (styles[i].href && styles[i].href.indexOf('bootstrap') > -1) {
                    bootstrapLoaded = true;
                    break;
                }
            }

            if (!bootstrapLoaded) {
                console.warn('本地Bootstrap CSS加载失败，尝试CDN');
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css';
                document.head.appendChild(link);
            }
        }, 1000);
    </script>

    <link rel="stylesheet" href="/static/css/style.css" onerror="this.onerror=null;this.href='/direct-static/css/style.css';console.error('样式文件加载失败')">
    
<style>
    .analysis-content {
        white-space: pre-wrap;
        line-height: 1.6;
    }

    .metadata-item {
        margin-bottom: 0.5rem;
    }

    .visualization-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }
</style>


    <!-- 预加载核心JS资源，确保快速可用 -->
    <link rel="preload" href="/static/js/lib/jquery-3.6.0.min.js" as="script">
    <link rel="preload" href="/static/js/lib/bootstrap.bundle.min.js" as="script">
    <link rel="prefetch" href="/static/js/lib/chart.min.js" as="script">

    <!-- 备用预加载 -->
    <link rel="preload" href="/direct-static/js/lib/jquery-3.6.0.min.js" as="script">
    <link rel="preload" href="/direct-static/js/lib/bootstrap.bundle.min.js" as="script">
    <link rel="prefetch" href="/direct-static/js/lib/chart.min.js" as="script">
<script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';console.error('错误处理脚本加载失败，尝试备用路径')"></script>
    </head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <span class="fs-4">九猫</span>
                <small class="text-muted">小说文本分析系统</small>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/upload">上传小说</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        
            
        

        
<div class="row">
    <div class="col-md-12">
        <!-- 分析进度条 -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">分析进度</h5>
                    <div id="analysisStatus" class="badge badge-info px-3 py-2">加载中...</div>
                </div>
            </div>
            <div class="card-body">
                <div class="progress mb-3" style="height: 20px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>当前维度:</strong> <span id="currentDimension">rhythm_pacing</span></p>
                        <p><strong>分块进度:</strong> <span id="blocksProgress">-</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>预计剩余时间:</strong> <span id="remainingTime">-</span></p>
                        <p><strong>预计完成时间:</strong> <span id="estimatedCompletionTime">-</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="card mb-4">
            <div class="card-header"><h5 class="card-title">分析日志</h5></div>
            <div class="card-body" id="logContainer" data-novel-id="4" style="background:#f8f9fa; height:200px; overflow-y:auto; font-family: monospace; font-size:0.9rem;"></div>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">首页</a></li>
                <li class="breadcrumb-item"><a href="/novel/4">第1章 她爬到了修仙界</a></li>
                <li class="breadcrumb-item active">rhythm_pacing 分析</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <h1>rhythm_pacing 分析</h1>
            <a href="/novel/4" class="btn btn-outline-secondary">
                返回小说页面
            </a>
        </div>

        <div class="row mt-4">
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析结果</h5>
                    </div>
                    <div class="card-body">
                        <div class="visualization-container">
                            <h6>分析可视化</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <canvas id="radarChart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <canvas id="barChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="analysis-content markdown-content">
                            ### 节奏分析（逐字逐句解析）

---

#### **1. 开篇：快速引入冲突与悬念**
- **段落长度**：首段仅一句话，短促有力，通过“注意看”的引导式语言直接拉近读者距离，迅速交代主角“桑念”穿越的荒诞原因（论文查重0%→阴暗爬行→穿越修仙界）。  
- **句子节奏**：短句为主（如“现在，她正面临有史以来最大的危机”），配合情节的荒诞性（穿越逻辑），形成跳跃式推进，营造紧迫感。  
- **情节速度**：极速展开核心矛盾——桑念穿越至危险情境（与谢沉舟的洞房现场），省略背景铺垫，直接切入高潮场景，符合网文快节奏开篇模式。

---

#### **2. 场景描写：张弛交替的氛围渲染**
- **段落长度**：中长段落集中描写环境（如“烛光暧昧摇晃……妖”），通过细腻的视觉意象（鲛绡帐、红绳金铃、胭脂色眼尾）营造香艳与危险交织的氛围，节奏暂时放缓。  
- **句子节奏**：长句铺陈细节（如“少年乌发散乱……勾人魂魄的妖”），用比喻（“瞳若点漆”）和感官描写（触觉“红绳金玲缚”、视觉“胭脂色”）增强画面感，为后续冲突蓄势。  
- **情节速度**：通过桑念视角的宕机与反应（“问号变感叹号”“火速爬下”），从静态描写转为动态动作，节奏陡然加快，形成“静→动”的张力。

---

#### **3. 对话与动作：加速危机升级**
- **段落长度**：短段落密集出现（如丫鬟春儿的对话与锁门动作），切割场景流动性，强化紧张感。  
- **句子节奏**：短句+动作动词主导（“跌跌撞撞往外跑”“门关上，顺便锁死”），配合口语化对话（“圆房要紧”“看他还怎么跑”），模拟真实时间流速，增强代入感。  
- **情节速度**：锁门事件彻底切断退路，矛盾升级为“必须面对谢沉舟”，推动情节进入核心冲突阶段。

---

#### **4. 系统介入：节奏的幽默缓冲与任务压力**
- **段落长度**：中长段落穿插系统六六的自我介绍（“小米与瓜子杀手……榴门高级信徒”），通过冗长头衔和荒诞设定（“小鸡教教主”）制造喜剧效果，缓解前文紧张。  
- **句子节奏**：系统对话夹杂网络梗（“大悲羊”“古拉拉黑暗之神大战青青草原黑皮体育生”），语言风格突变，形成节奏的断裂与反差，短暂拉慢主线推进。  
- **情节速度**：系统任务（折磨谢沉舟→被杀死才能返回）与桑念的抗拒（“抽自己一鞭子”）制造新矛盾，将荒诞喜剧转为生存压力，节奏从轻松回调至紧张。

---

#### **5. 核心冲突：动作与心理的拉锯战**
- **段落长度**：交替使用短段落（如“谢沉舟咬牙，‘桑、蕴、灵。’”）和长段落（解绳子的动作细节），模拟角色心理的焦灼感。  
- **句子节奏**：动词密集（“解”“翻找”“磨红”“拍拍”）表现桑念的慌乱，配合谢沉舟的生理反应（“身体抖得厉害”“迷情丹”）和威胁性台词（“你又想玩什么花样？”），形成动作与语言的对抗性节奏。  
- **情节速度**：解绳失败→发现禁制→迷情丹药效→系统强制任务，多线压力叠加，情节密度极高，但通过桑念的黑色幽默应对（自抽鞭子），维持节奏的戏剧性起伏。

---

### **整体节奏总结**
1. **快慢交替**：开篇极速切入冲突→香艳场景的细腻描写放缓节奏→锁门事件加速危机→系统介入插入幽默缓冲→任务压力再次拉紧，形成“过山车式”节奏体验。  
2. **语言风格**：口语化短句（“嗷一下倒地”“唉呀妈呀这网真卡”）与文学化长句（“眼尾洇开薄薄的胭脂色”）混搭，适应年轻读者偏好，平衡深度与可读性。  
3. **情节密度**：每段均含关键信息（穿越原因、身份危机、系统任务、谢沉舟的威胁），无冗余描写，符合网文“高信息量+强情绪输出”的节奏特征。  
4. **情绪张力**：通过桑念的求生欲与系统的荒诞要求、谢沉舟的阴戾形成三角对抗，持续输出紧张、搞笑、危险交织的多维情绪，维持读者黏性。

---

### **改进建议**
- **段落切割**：部分长段落（如系统自我介绍）可拆分为更短单元，避免喜剧效果稀释紧张氛围。  
- **节奏平衡**：系统掉线/上线的插科打诨可稍作精简，确保主线压迫感不被过度打断。  
- **细节留白**：谢沉舟的“心理健康问题”可逐步透露而非一次性说明，增强后续揭秘的节奏吸引力。
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">小说信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>标题：</strong> 第1章 她爬到了修仙界</p>
                        <p><strong>作者：</strong> 未知</p>
                        <p><strong>字数：</strong> 2643</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析元数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="metadata-item">
                            <strong>分析时间：</strong>
                            <span>2025-05-01 08:38</span>
                        </div>

                        
                            <div class="metadata-item">
                                <strong>处理时间：</strong>
                                <span>88.57 秒</span>
                            </div>
                        

                        
                            <div class="metadata-item">
                                <strong>分析块数：</strong>
                                <span>1</span>
                            </div>
                        

                        
                            <div class="metadata-item">
                                <strong>最后更新：</strong>
                                <span>2025-05-01 08:38</span>
                            </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">九猫小说文本分析系统 &copy; 2025</span>
            <p class="small text-muted">基于阿里云 DeepSeek R1 的小说深度分析工具</p>
        </div>
    </footer>

    <!-- 直接加载核心JS库 -->
    <script src="/static/js/lib/jquery-3.6.0.min.js" onerror="this.onerror=null;this.src='/direct-static/js/lib/jquery-3.6.0.min.js';console.error('jQuery加载失败，尝试备用路径')"></script>
    <script src="/static/js/lib/bootstrap.bundle.min.js" onerror="this.onerror=null;this.src='/direct-static/js/lib/bootstrap.bundle.min.js';console.error('Bootstrap JS加载失败，尝试备用路径')"></script>

    <!-- 按需加载Chart.js -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 只在需要时加载Chart.js
            if (document.querySelector('.chart-container') || document.querySelector('.analysis-chart')) {
                var chartScript = document.createElement('script');
                chartScript.src = "/static/js/lib/chart.min.js";
                chartScript.onerror = function() {
                    console.error('Chart.js加载失败，尝试备用路径');
                    this.onerror = null;
                    this.src = '/direct-static/js/lib/chart.min.js';
                };
                document.head.appendChild(chartScript);
                console.log('已加载Chart.js');

                // 如果页面上有初始化图表的函数，调用它
                chartScript.onload = function() {
                    if (typeof window.initializeCharts === 'function') {
                        setTimeout(function() {
                            window.initializeCharts();
                        }, 100);
                    }
                };
            }
        });
    </script>

    <!-- 项目主JS文件 -->
    <script src="/static/js/main.js" onerror="this.onerror=null;this.src='/direct-static/js/main.js';console.error('主JS文件加载失败，尝试备用路径')"></script>

    <!-- 初始化Bootstrap组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查Bootstrap是否已加载
            if (typeof bootstrap !== 'undefined') {
                console.log('初始化Bootstrap组件');
                try {
                    // 初始化工具提示
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    if (bootstrap.Tooltip) {
                    tooltipTriggerList.map(function(tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                        console.log('Tooltip 组件初始化成功');
                    } else {
                        console.warn('Bootstrap Tooltip 未定义，跳过初始化');
                    }

                    // 初始化弹出框
                    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                    if (bootstrap.Popover) {
                    popoverTriggerList.map(function(popoverTriggerEl) {
                        return new bootstrap.Popover(popoverTriggerEl);
                    });
                        console.log('Popover 组件初始化成功');
                    } else {
                        console.warn('Bootstrap Popover 未定义，跳过初始化');
                    }
                } catch (e) {
                    console.error('初始化Bootstrap组件时出错: ' + e);
                }
            } else {
                console.warn('Bootstrap未加载，跳过组件初始化');
            }
        });
    </script>

    <!-- 全局错误处理 -->
    <script>
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
            return true; // 阻止默认错误处理
        };
    </script>

    
<script>
    // 全局变量
    const novelId = "4";
    const dimension = "rhythm_pacing";

    // 实时进度更新函数
    function fetchAnalysisProgress() {
        return fetch(`/api/analysis/progress?novel_id=${novelId}`)
            .then(res => res.json())
            .then(data => {
                if (data.success && data.progress && data.progress[dimension]) {
                    return {
                        progress: data.progress[dimension],
                        isRunning: data.is_running
                    };
                }
                throw new Error('无法获取进度数据');
            });
    }

    function updateProgressUI() {
        fetchAnalysisProgress()
            .then(data => {
                const progressData = data.progress;
                const isRunning = data.isRunning;

                // 更新进度条
                const progress = progressData.progress || 0;
                const progressBar = document.getElementById('progressBar');
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                progressBar.textContent = `${progress}%`;

                // 更新分析状态
                const statusElement = document.getElementById('analysisStatus');
                if (progress === 100) {
                    statusElement.className = 'badge badge-success px-3 py-2';
                    statusElement.textContent = '已完成';
                    progressBar.className = 'progress-bar bg-success';
                } else if (progress < 0) {
                    statusElement.className = 'badge badge-danger px-3 py-2';
                    statusElement.textContent = '已终止';
                    progressBar.className = 'progress-bar bg-danger';
                } else if (isRunning) {
                    statusElement.className = 'badge badge-primary px-3 py-2';
                    statusElement.textContent = '分析中';
                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
                } else {
                    statusElement.className = 'badge badge-warning px-3 py-2';
                    statusElement.textContent = '已暂停';
                    progressBar.className = 'progress-bar bg-warning';
                }

                // 更新块进度
                if (progressData.blocks_progress) {
                    document.getElementById('blocksProgress').textContent = progressData.blocks_progress;
                }

                // 更新时间信息
                if (progressData.remaining_time) {
                    document.getElementById('remainingTime').textContent = progressData.remaining_time;
                }

                if (progressData.eta) {
                    document.getElementById('estimatedCompletionTime').textContent = progressData.eta;
                }

                // 如果仍在分析，继续轮询
                if (isRunning && progress < 100 && progress >= 0) {
                    setTimeout(updateProgressUI, 5000);
                }
            })
            .catch(err => {
                console.error('获取进度信息失败:', err);
                // 即使出错也继续尝试轮询
                setTimeout(updateProgressUI, 10000);
            });
    }

    // 实时日志拉取函数
    function fetchLogs(since) {
        const logContainer = document.getElementById('logContainer');
        const novelId = logContainer.dataset.novelId;
        let url = `/api/analysis/logs?novel_id=${novelId}&level=all&limit=200&dimension=${dimension}`;
        if (since) url += `&since=${since}`;
        return fetch(url).then(res=>res.json());
    }

    function initLogControls() {
        const logContainer = document.getElementById('logContainer');
        let lastTs = '';

        // 添加控制按钮
        const controlDiv = document.createElement('div');
        controlDiv.className = 'mb-2 d-flex justify-content-between';
        controlDiv.innerHTML = `
            <div>
                <button id="refreshLogsBtn" class="btn btn-sm btn-outline-primary">刷新日志</button>
                <button id="clearLogsBtn" class="btn btn-sm btn-outline-secondary ml-2">清空</button>
            </div>
            <div>
                <label class="mr-2"><input type="checkbox" id="autoScrollCheck" checked> 自动滚动</label>
                <select id="logLevelFilter" class="form-control form-control-sm d-inline-block" style="width:auto">
                    <option value="all">所有级别</option>
                    <option value="info">信息</option>
                    <option value="warning">警告</option>
                    <option value="error">错误</option>
                </select>
            </div>
        `;
        logContainer.parentNode.insertBefore(controlDiv, logContainer);

        // 控制事件处理
        document.getElementById('refreshLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '<div class="text-center text-muted my-3">正在加载日志...</div>';
            lastTs = '';
            updateLogs(true);
        });

        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '';
        });

        const autoScrollCheck = document.getElementById('autoScrollCheck');

        // 日志级别过滤
        const logLevelFilter = document.getElementById('logLevelFilter');
        logLevelFilter.addEventListener('change', () => {
            const level = logLevelFilter.value;
            Array.from(logContainer.children).forEach(line => {
                if (level === 'all') {
                    line.style.display = '';
                } else {
                    const logText = line.textContent;
                    line.style.display = logText.includes(`${level.toUpperCase()}`) ? '' : 'none';
                }
            });
        });

        function updateLogs(force = false) {
            fetchLogs(lastTs).then(data=>{
                if (data.success) {
                    // 如果是首次加载且没有日志，显示提示
                    if (lastTs === '' && data.logs.length === 0) {
                        logContainer.innerHTML = '<div class="text-center text-muted my-3">暂无分析日志，可能分析已完成或尚未开始</div>';
                    }

                    // 显示新日志
                    if (data.logs.length > 0) {
                        if (logContainer.children.length === 1 && logContainer.children[0].classList.contains('text-muted')) {
                            logContainer.innerHTML = '';
                        }

                        data.logs.forEach(log => {
                            const line = document.createElement('div');
                            line.className = log.level === 'error' ? 'text-danger' :
                                            log.level === 'warning' ? 'text-warning' : '';

                            // 增加分析进度信息的突出显示
                            let message = log.message;
                            if (log.important ||
                                message.includes('进度更新') ||
                                message.includes('分析块') ||
                                message.includes('API调用完成') ||
                                message.includes('%') ||
                                message.includes('处理时间') ||
                                message.includes('令牌使用量') ||
                                message.includes('费用') ||
                                message.includes('分析结果') ||
                                message.includes('完成度') ||
                                message.includes('耗时')) {
                                line.className += ' font-weight-bold text-primary';
                            }

                            line.textContent = `[${log.timestamp}] ${log.level.toUpperCase()} - ${message}`;

                            // 应用日志级别过滤
                            const level = logLevelFilter.value;
                            if (level !== 'all' && !log.level.includes(level)) {
                                line.style.display = 'none';
                            }

                            logContainer.appendChild(line);
                            lastTs = log.timestamp;
                        });

                        // 自动滚动到底部
                        if (autoScrollCheck.checked) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
                    }

                    // 判断是否需要继续轮询
                    if (data.is_running || force) {
                        setTimeout(updateLogs, 3000);  // 增加轮询间隔到3秒，减轻服务器负担
                    }
                }
            }).catch(err=>{
                console.error('获取日志失败', err);
                logContainer.innerHTML += `<div class="text-danger">获取日志失败: ${err.message}</div>`;
                setTimeout(updateLogs, 5000);  // 发生错误时延长重试时间
            });
        }

        // 启动日志更新
        updateLogs();
    }

    // 加载Chart.js库
    function loadChartJS() {
        return new Promise((resolve, reject) => {
            // 检查Chart是否已经加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载');
                resolve();
                return;
            }

            // 尝试加载本地Chart.js
            var script = document.createElement('script');
            script.src = "/static/js/lib/chart.min.js";

            script.onload = function() {
                console.log('成功从本地加载Chart.js');
                resolve();
            };

            script.onerror = function() {
                // 如果本地加载失败，尝试备用路径
                console.warn('本地Chart.js加载失败，尝试备用路径');
                var backupScript = document.createElement('script');
                backupScript.src = "/direct-static/js/lib/chart.min.js";

                backupScript.onload = function() {
                    console.log('成功从备用路径加载Chart.js');
                    resolve();
                };

                backupScript.onerror = function() {
                    // 如果备用路径也失败，尝试CDN
                    console.warn('备用路径Chart.js加载失败，尝试CDN');
                    var cdnScript = document.createElement('script');
                    cdnScript.src = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";

                    cdnScript.onload = function() {
                        console.log('成功从CDN加载Chart.js');
                        resolve();
                    };

                    cdnScript.onerror = function() {
                        console.error('所有Chart.js加载尝试均失败');
                        reject(new Error('无法加载Chart.js'));
                    };

                    document.head.appendChild(cdnScript);
                };

                document.head.appendChild(backupScript);
            };

            document.head.appendChild(script);
        });
    }

    // 初始化图表
    function initCharts() {
        // 获取分析结果数据
        let resultMetadata = JSON.parse("{"processing_time": 88.56627416610718, "chunk_count": 1, "start_time": "2025-05-01 16:37:10", "end_time": "2025-05-01 16:38:39", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}");
        console.log('分析结果元数据:', resultMetadata);

        // 确保元数据是有效的对象
        if (!resultMetadata || typeof resultMetadata !== 'object') {
            console.warn('元数据不是有效的对象，尝试解析');
            try {
                if (typeof resultMetadata === 'string') {
                    resultMetadata = JSON.parse(resultMetadata);
                } else {
                    resultMetadata = {};
                }
            } catch (e) {
                console.error('解析元数据失败:', e);
                resultMetadata = {};
            }
        }

        // 从分析结果中提取数据 - 雷达图
        // 使用API统计数据构建雷达图，确保数据是真实的
        let radarLabels = ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'];
        let radarData = [];

        // 从元数据中获取真实数据
        if (resultMetadata && resultMetadata.processing_time) {
            radarData.push(Math.round(resultMetadata.processing_time * 100) / 100);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.chunk_count) {
            radarData.push(resultMetadata.chunk_count);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.api_calls) {
            radarData.push(resultMetadata.api_calls);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.tokens_used) {
            radarData.push(resultMetadata.tokens_used);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.cost) {
            radarData.push(Math.round(resultMetadata.cost * 10000) / 10000);
        } else {
            radarData.push(0);
        }

        // 如果有可视化数据，使用它
        if (resultMetadata && typeof resultMetadata === 'object' &&
            resultMetadata.visualization_data &&
            resultMetadata.visualization_data.radar) {
            const visualData = resultMetadata.visualization_data.radar;
            if (visualData.labels && Array.isArray(visualData.labels) && visualData.labels.length > 0) {
                radarLabels = visualData.labels;
            }
            if (visualData.data && Array.isArray(visualData.data) && visualData.data.length > 0) {
                radarData = visualData.data;
            }
            console.log('使用自定义雷达图数据:', radarLabels, radarData);
        } else {
            console.log('使用从元数据构建的雷达图数据:', radarLabels, radarData);
        }

        // 从分析结果中提取数据 - 柱状图
        // 构建更有意义的柱状图数据
        let barLabels = radarLabels; // 使用相同的标签
        let barData = radarData;     // 使用相同的数据

        // 如果有可视化数据，使用它
        if (resultMetadata && typeof resultMetadata === 'object' &&
            resultMetadata.visualization_data &&
            resultMetadata.visualization_data.bar) {
            const visualData = resultMetadata.visualization_data.bar;
            if (visualData.labels && Array.isArray(visualData.labels) && visualData.labels.length > 0) {
                barLabels = visualData.labels;
            }
            if (visualData.data && Array.isArray(visualData.data) && visualData.data.length > 0) {
                barData = visualData.data;
            }
            console.log('使用自定义柱状图数据:', barLabels, barData);
        } else {
            console.log('使用从元数据构建的柱状图数据:', barLabels, barData);
        }

        // 加载Chart.js并初始化图表
        loadChartJS().then(() => {
            // 初始化雷达图
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: radarLabels,
                    datasets: [{
                        label: 'rhythm_pacing分析评分',
                        data: radarData,
                        fill: true,
                        backgroundColor: 'rgba(74, 107, 223, 0.2)',
                        borderColor: 'rgb(74, 107, 223)',
                        pointBackgroundColor: 'rgb(74, 107, 223)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(74, 107, 223)'
                    }]
                },
                options: {
                    elements: {
                        line: {
                            borderWidth: 3
                        }
                    },
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 初始化柱状图
            const barCtx = document.getElementById('barChart').getContext('2d');
            new Chart(barCtx, {
                type: 'bar',
                data: {
                    labels: barLabels,
                    datasets: [{
                        label: 'rhythm_pacing分析指标',
                        data: barData,
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(255, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(255, 206, 86, 0.2)',
                            'rgba(153, 102, 255, 0.2)',
                            'rgba(255, 159, 64, 0.2)'
                        ],
                        borderColor: [
                            'rgba(75, 192, 192, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            console.log('图表初始化完成');
        }).catch(error => {
            console.error('图表初始化失败:', error);
            // 显示错误信息给用户
            document.querySelectorAll('.chart-container').forEach(container => {
                container.innerHTML = '<div class="alert alert-danger">图表加载失败，请刷新页面重试</div>';
            });
        });
    }

    // 主初始化函数
    document.addEventListener('DOMContentLoaded', function() {
        // 启动进度更新
        updateProgressUI();

        // 初始化日志控制
        initLogControls();

        // 初始化图表
        initCharts();
    });
</script>

</body>
</html>