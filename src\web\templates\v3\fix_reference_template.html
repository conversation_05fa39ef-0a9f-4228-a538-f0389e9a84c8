<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参考蓝本设置修复工具 - 九猫写作系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .header-icon {
            font-size: 3rem;
            color: #28a745;
            margin-bottom: 1rem;
        }
        .step-card {
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        .step-number {
            width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            border-radius: 50%;
            background-color: #28a745;
            color: white;
            font-weight: bold;
            margin-right: 0.75rem;
        }
        pre {
            background-color: #f5f5f5;
            padding: 1rem;
            border-radius: 0.5rem;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
        }
        .footer {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="text-center mb-5">
                    <i class="fas fa-tools header-icon"></i>
                    <h1 class="mb-3">参考蓝本设置修复工具</h1>
                    <p class="lead">
                        此工具用于修复"设置参考蓝本"功能中的显示问题。
                        当你设置小说为参考蓝本时，系统弹窗提示成功，但实际上小说卡片未显示橙色"参考蓝本"标记，或参考蓝本页面未显示新设置的参考蓝本。
                    </p>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>问题原因：</strong> 此问题可能是由于浏览器缓存或前端刷新机制导致的，下面的步骤会帮助你解决这个问题。
                </div>

                <!-- 修复步骤 -->
                <h2 class="mb-4">修复步骤</h2>

                <!-- 步骤1：后端修复 -->
                <div class="card step-card">
                    <div class="card-header bg-success text-white">
                        <div class="d-flex align-items-center">
                            <div class="step-number">1</div>
                            <h4 class="mb-0">后端数据修复</h4>
                        </div>
                    </div>
                    <div class="card-body">
                        <p>运行以下Python脚本，确保数据库中正确标记了参考蓝本：</p>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" onclick="copyToClipboard('backendFix')">
                                <i class="fas fa-copy"></i>
                            </button>
                            <pre id="backendFix">python fix_reference_template_display.py</pre>
                        </div>
                        <p class="mt-3">
                            此脚本会检查数据库中的参考蓝本设置，并修复任何不正确的设置。
                            如果没有找到参考蓝本，它会提示你选择一个小说设为参考蓝本。
                        </p>
                    </div>
                </div>

                <!-- 步骤2：重启应用 -->
                <div class="card step-card">
                    <div class="card-header bg-success text-white">
                        <div class="d-flex align-items-center">
                            <div class="step-number">2</div>
                            <h4 class="mb-0">重启应用</h4>
                        </div>
                    </div>
                    <div class="card-body">
                        <p>使用以下命令重启应用，确保应用加载最新的数据：</p>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" onclick="copyToClipboard('restartCmd')">
                                <i class="fas fa-copy"></i>
                            </button>
                            <pre id="restartCmd">python -u -m src.web.v3_app</pre>
                        </div>
                    </div>
                </div>

                <!-- 步骤3：前端修复 -->
                <div class="card step-card">
                    <div class="card-header bg-success text-white">
                        <div class="d-flex align-items-center">
                            <div class="step-number">3</div>
                            <h4 class="mb-0">前端刷新修复</h4>
                        </div>
                    </div>
                    <div class="card-body">
                        <p>
                            在浏览器中打开参考蓝本页面后，按F12打开开发者工具，切换到"控制台(Console)"标签，
                            然后复制粘贴以下JavaScript代码，按回车执行：
                        </p>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" onclick="copyToClipboard('frontendFix')">
                                <i class="fas fa-copy"></i>
                            </button>
                            <pre id="frontendFix">// 载入修复脚本
fetch('/fix_reference_template_refresh.js')
    .then(response => response.text())
    .then(script => {
        eval(script);  // 执行脚本
    })
    .catch(error => {
        console.error('无法加载修复脚本:', error);
        alert('无法加载修复脚本，请确保fix_reference_template_refresh.js文件在正确的位置');
    });</pre>
                        </div>
                        <p class="mt-3">
                            这段代码会加载并执行修复脚本，刷新参考蓝本的显示，并添加一个"刷新参考蓝本"按钮到页面右下角，
                            你可以点击该按钮手动刷新页面。
                        </p>
                    </div>
                </div>

                <!-- 步骤4：清除浏览器缓存 -->
                <div class="card step-card">
                    <div class="card-header bg-success text-white">
                        <div class="d-flex align-items-center">
                            <div class="step-number">4</div>
                            <h4 class="mb-0">清除浏览器缓存</h4>
                        </div>
                    </div>
                    <div class="card-body">
                        <p>如果上述步骤仍不能解决问题，请清除浏览器缓存：</p>
                        <ol>
                            <li><strong>Chrome/Edge:</strong> 按 Ctrl+Shift+Delete，选择"缓存的图片和文件"，点击"清除数据"</li>
                            <li><strong>Firefox:</strong> 按 Ctrl+Shift+Delete，选择"缓存"，点击"清除数据"</li>
                            <li>或者直接在参考蓝本页面按 Ctrl+F5 强制刷新</li>
                        </ol>
                    </div>
                </div>
                
                <!-- 修复验证 -->
                <h2 class="mb-4 mt-5">验证修复是否成功</h2>
                <div class="card step-card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-check-circle me-2"></i>检查以下内容</h4>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>
                                <strong>参考蓝本页面:</strong> 导航到"参考蓝本"页面，应该能看到你设置的参考蓝本小说
                            </li>
                            <li>
                                <strong>小说列表页面:</strong> 在小说列表页面，设为参考蓝本的小说卡片上方应该有橙色底色的"参考蓝本"标记
                            </li>
                            <li>
                                <strong>小说详情页面:</strong> 进入参考蓝本小说的详情页面，应该看不到"设为参考蓝本"按钮（因为已经是参考蓝本了）
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="footer">
                    <p>
                        <i class="fas fa-code me-1"></i>
                        九猫小说分析写作系统 - 参考蓝本修复工具
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.innerText;
            
            navigator.clipboard.writeText(text).then(() => {
                // 复制成功
                const btn = element.previousElementSibling;
                const originalHTML = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                btn.classList.remove('btn-outline-secondary');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    btn.innerHTML = originalHTML;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-secondary');
                }, 2000);
            }).catch(err => {
                console.error('复制失败: ', err);
                alert('复制失败，请手动选择文本并复制');
            });
        }
    </script>
</body>
</html> 