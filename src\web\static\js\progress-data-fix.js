/**
 * 进度数据修复脚本
 * 解决当维度进度数据为空时的问题
 * v1.0.0
 */

// 立即清除所有可能的定时器，防止已有的轮询
(function() {
    console.log('正在清除所有可能的定时器...');
    // 清除0-1000范围内的所有定时器（保守方法）
    for (let i = 0; i < 1000; i++) {
        clearTimeout(i);
        clearInterval(i);
    }
    console.log('已清除所有可能的定时器');
})();

/**
 * 安全获取分析进度数据
 * 添加额外的错误处理和默认值
 * 
 * @param {string} novelId 小说ID
 * @param {string} dimension 维度名称
 * @returns {Promise<Object>} 进度数据对象
 */
function safeGetProgress(novelId, dimension) {
    console.log(`安全获取进度数据: novel_id=${novelId}, dimension=${dimension}`);
    return fetch(`/api/analysis/progress?novel_id=${novelId}&_=${Date.now()}`)
        .then(res => res.json())
        .then(data => {
            if (data.success && data.progress) {
                // 如果找不到特定维度的数据，返回默认值
                if (!data.progress[dimension]) {
                    console.warn(`找不到维度 ${dimension} 的进度数据，使用默认值`);
                    return {
                        progress: {
                            progress: 100,
                            status: '完成',
                            blocks_progress: '1/1',
                            remaining_time: '0秒',
                            eta: '已完成'
                        },
                        isRunning: false
                    };
                }
                
                // 找到了维度数据
                return {
                    progress: data.progress[dimension],
                    isRunning: data.is_running
                };
            }
            
            // API返回失败或没有进度数据
            console.warn('API返回失败或没有进度数据，使用默认值');
            return {
                progress: {
                    progress: 100,
                    status: '完成',
                    blocks_progress: '1/1',
                    remaining_time: '0秒',
                    eta: '已完成'
                },
                isRunning: false
            };
        })
        .catch(err => {
            console.error('获取进度数据时出错:', err);
            // 发生错误时返回默认值
            return {
                progress: {
                    progress: 100,
                    status: '完成',
                    blocks_progress: '1/1',
                    remaining_time: '0秒',
                    eta: '已完成'
                },
                isRunning: false
            };
        });
}

/**
 * 更新页面上的进度UI元素
 * 
 * @param {string} novelId 小说ID 
 * @param {string} dimension 维度名称
 */
function updateProgressUI(novelId, dimension) {
    safeGetProgress(novelId, dimension)
        .then(data => {
            // 确保数据存在
            const progressData = data.progress || {
                progress: 100,
                status: '完成',
                blocks_progress: '1/1',
                remaining_time: '0秒',
                eta: '已完成'
            };
            const isRunning = data.isRunning || false;

            // 更新进度条
            const progress = progressData.progress || 0;
            const progressBar = document.getElementById('progressBar');
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                progressBar.textContent = `${progress}%`;

                // 设置进度条颜色
                if (progress >= 100) {
                    progressBar.className = 'progress-bar bg-success';
                } else if (progress >= 75) {
                    progressBar.className = 'progress-bar bg-info';
                } else if (progress >= 50) {
                    progressBar.className = 'progress-bar bg-primary';
                } else if (progress >= 25) {
                    progressBar.className = 'progress-bar bg-warning';
                }
            }

            // 更新状态文本
            const statusElement = document.getElementById('statusText');
            if (statusElement) {
                statusElement.textContent = progressData.status || '完成';
            }

            // 更新块进度
            const blocksProgress = document.getElementById('blocksProgress');
            if (blocksProgress) {
                blocksProgress.textContent = progressData.blocks_progress || '1/1';
            }

            // 更新时间信息
            const remainingTime = document.getElementById('remainingTime');
            if (remainingTime) {
                remainingTime.textContent = progressData.remaining_time || '0秒';
            }

            const estimatedCompletionTime = document.getElementById('estimatedCompletionTime');
            if (estimatedCompletionTime) {
                estimatedCompletionTime.textContent = progressData.eta || '已完成';
            }

            console.log(`进度UI已更新: progress=${progress}%, isRunning=${isRunning}`);
        })
        .catch(err => {
            console.error('更新进度UI时出错:', err);
            // 出错时设置为默认值
            setDefaultProgress();
        });
}

/**
 * 设置默认的进度值
 */
function setDefaultProgress() {
    // 更新进度条
    const progressBar = document.getElementById('progressBar');
    if (progressBar) {
        progressBar.style.width = '100%';
        progressBar.setAttribute('aria-valuenow', 100);
        progressBar.textContent = '100%';
        progressBar.className = 'progress-bar bg-success';
    }
    
    // 更新状态文本
    const statusText = document.getElementById('statusText');
    if (statusText) {
        statusText.textContent = '完成';
    }
    
    // 更新块进度
    const blocksProgress = document.getElementById('blocksProgress');
    if (blocksProgress) {
        blocksProgress.textContent = '1/1';
    }
    
    // 更新时间信息
    const remainingTime = document.getElementById('remainingTime');
    if (remainingTime) {
        remainingTime.textContent = '0秒';
    }
    
    const estimatedCompletionTime = document.getElementById('estimatedCompletionTime');
    if (estimatedCompletionTime) {
        estimatedCompletionTime.textContent = '已完成';
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('进度数据修复脚本已加载');
    
    // 尝试从页面获取小说ID和维度信息
    const novelIdElement = document.querySelector('[data-novel-id]');
    const dimensionElement = document.querySelector('[data-dimension]');
    
    if (novelIdElement) {
        const novelId = novelIdElement.getAttribute('data-novel-id');
        let dimension = '';
        
        // 尝试从维度元素获取
        if (dimensionElement) {
            dimension = dimensionElement.getAttribute('data-dimension');
        }
        
        // 如果还没有找到维度，尝试从全局变量获取
        if (!dimension && window.dimension) {
            dimension = window.dimension;
        }
        
        // 如果找到了必要的信息，初始化进度UI更新
        if (novelId && dimension) {
            console.log(`初始化进度UI更新: novel_id=${novelId}, dimension=${dimension}`);
            
            // 立即更新一次
            updateProgressUI(novelId, dimension);
            
            // 每5秒更新一次
            setInterval(() => {
                updateProgressUI(novelId, dimension);
            }, 5000);
        } else {
            console.warn('无法获取小说ID或维度信息');
            setDefaultProgress();
        }
    } else {
        console.warn('找不到小说ID元素');
        setDefaultProgress();
    }
}); 