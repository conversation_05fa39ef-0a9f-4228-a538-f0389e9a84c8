/**
 * DOM操作增强修复脚本
 * 作用：专门处理DOM操作中的$标识符问题
 */

(function() {
    // 安全地执行代码的函数
    function safeExecute(fn, fallback) {
        try {
            return fn();
        } catch (e) {
            console.log('DOM操作增强修复：捕获到错误并安全处理', e.message);
            return fallback;
        }
    }

    // 安全地解析HTML字符串，处理$相关语法错误
    function safeParseHTML(htmlString) {
        // 替换可能导致问题的$标识符
        if (typeof htmlString === 'string') {
            // 检测未转义的$标识符（通常用于jQuery或模板字符串）
            htmlString = htmlString.replace(/\${/g, '$$${'); // 转义模板字符串标记
            htmlString = htmlString.replace(/([^\\])\$/g, '$1$$'); // 转义单独的$符号
        }
        
        // 创建一个安全的文档片段
        const fragment = document.createDocumentFragment();
        const tempDiv = document.createElement('div');
        
        // 安全设置innerHTML
        safeExecute(() => {
            tempDiv.innerHTML = htmlString;
        }, null);
        
        // 将tempDiv的内容移到片段中
        while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
        }
        
        return fragment;
    }

    // 替换innerHTML设置操作
    const originalDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
    if (originalDescriptor && originalDescriptor.set) {
        const originalSetter = originalDescriptor.set;
        
        Object.defineProperty(Element.prototype, 'innerHTML', {
            ...originalDescriptor,
            set: function(htmlString) {
                if (typeof htmlString === 'string' && (htmlString.includes('$') || htmlString.includes('${') || htmlString.includes('`'))) {
                    // 预处理HTML字符串，避免$符号导致的问题
                    try {
                        const preprocessedHTML = htmlString
                            .replace(/\${/g, '$$${')
                            .replace(/([^\\])\$/g, '$1$$');
                        
                        return originalSetter.call(this, preprocessedHTML);
                    } catch (e) {
                        console.log('DOM操作增强修复：innerHTML设置错误，使用安全模式', e.message);
                        // 如果预处理后仍有错误，尝试逐个添加子节点
                        this.textContent = ''; // 清空当前内容
                        const fragment = safeParseHTML(htmlString);
                        this.appendChild(fragment);
                        return htmlString;
                    }
                }
                
                return originalSetter.call(this, htmlString);
            }
        });
    }

    // 处理script脚本中的$符号问题
    const createElementOriginal = document.createElement;
    document.createElement = function(tagName, options) {
        const element = createElementOriginal.call(document, tagName, options);
        
        if (tagName.toLowerCase() === 'script') {
            // 拦截script元素的textContent设置
            const originalDescriptor = Object.getOwnPropertyDescriptor(Node.prototype, 'textContent');
            if (originalDescriptor && originalDescriptor.set) {
                const originalTextContentSetter = originalDescriptor.set;
                
                Object.defineProperty(element, 'textContent', {
                    ...originalDescriptor,
                    set: function(content) {
                        if (typeof content === 'string' && content.includes('$')) {
                            // 处理脚本内容中的$符号
                            try {
                                // 尝试解析为合法的JS代码
                                const fixedContent = content
                                    .replace(/\$(?=\{)/g, '\\$')  // 转义${ 为 \${
                                    .replace(/([^\\])\$/g, '$1\\$'); // 转义 $ 为 \$
                                
                                return originalTextContentSetter.call(this, fixedContent);
                            } catch (e) {
                                console.log('DOM操作增强修复：脚本内容修复失败，保留原内容', e.message);
                                return originalTextContentSetter.call(this, content);
                            }
                        }
                        
                        return originalTextContentSetter.call(this, content);
                    }
                });
            }
        }
        
        return element;
    };

    // 拦截eval和Function构造函数，处理可能的$符号问题
    const originalEval = window.eval;
    window.eval = function(code) {
        if (typeof code === 'string' && code.includes('$')) {
            try {
                return originalEval(code);
            } catch (e) {
                if (e.message.includes('$')) {
                    console.log('DOM操作增强修复：eval执行错误，尝试修复$符号', e.message);
                    // 尝试修复代码
                    const fixedCode = code
                        .replace(/\$(?=\{)/g, '\\$')
                        .replace(/([^\\])\$/g, '$1\\$');
                    
                    try {
                        return originalEval(fixedCode);
                    } catch (innerError) {
                        console.log('DOM操作增强修复：修复后eval仍然出错', innerError.message);
                        // 返回一个空对象而不是抛出错误
                        return {};
                    }
                } else {
                    throw e; // 其他类型的错误正常抛出
                }
            }
        }
        
        return originalEval(code);
    };

    console.log('DOM操作增强修复脚本已加载');
})(); 