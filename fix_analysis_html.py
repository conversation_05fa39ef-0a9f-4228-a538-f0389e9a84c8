import os
import re
import shutil
from datetime import datetime

def fix_analysis_html():
    """修复analysis.html文件中的Jinja标签问题"""
    file_path = 'src/web/templates/analysis.html'
    
    print(f"修复文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 {file_path}")
        return False
    
    # 创建备份
    backup_file = f"{file_path}.bak_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy2(file_path, backup_file)
        print(f"已创建备份: {backup_file}")
    except Exception as e:
        print(f"创建备份时出错: {str(e)}")
        return False
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 计数各种标签
        if_count = content.count('{% if')
        else_count = content.count('{% else')
        endif_count = content.count('{% endif')
        
        print(f"找到 {if_count} 个if标签, {else_count} 个else标签, {endif_count} 个endif标签")
        
        # 检查是否匹配
        if if_count != endif_count:
            print(f"警告: if标签({if_count})和endif标签({endif_count})数量不匹配!")
        
        # 读取文件内容为行
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找并修复问题
        fixed_lines = []
        fixed_count = 0
        
        for i, line in enumerate(lines):
            original_line = line
            
            # 修复if标签
            if '{% if' in line and not (line.strip().startswith('{% if') or '{% if' in line and '%}' in line):
                match = re.search(r'{%\s*if\s+(.+?)%}', line)
                if match:
                    condition = match.group(1).strip()
                    line = line.replace(match.group(0), f"{{% if {condition} %}}")
                    fixed_count += 1
                    print(f"修复第{i+1}行if标签: {original_line.strip()} -> {line.strip()}")
            
            # 修复else标签
            if '{% else' in line and not line.strip() == '{% else %}':
                line = re.sub(r'{%\s*else\s*%}', '{% else %}', line)
                fixed_count += 1
                print(f"修复第{i+1}行else标签: {original_line.strip()} -> {line.strip()}")
            
            # 修复endif标签
            if '{% endif' in line and not line.strip() == '{% endif %}':
                line = re.sub(r'{%\s*endif\s*%}', '{% endif %}', line)
                fixed_count += 1
                print(f"修复第{i+1}行endif标签: {original_line.strip()} -> {line.strip()}")
            
            fixed_lines.append(line)
        
        # 特别检查第395行附近的if标签和第859行附近的else标签
        print("\n检查关键区域:")
        
        # 打印第395行附近的内容
        print("第395行附近(if标签):")
        for i in range(max(0, 395-5), min(len(fixed_lines), 395+5)):
            print(f"{i+1}: {fixed_lines[i].rstrip()}")
        
        # 打印第859行附近的内容
        print("\n第859行附近(else标签):")
        for i in range(max(0, 859-5), min(len(fixed_lines), 859+5)):
            print(f"{i+1}: {fixed_lines[i].rstrip()}")
        
        # 确保第395行的if标签和第859行的else标签匹配
        if 395 < len(fixed_lines) and 859 < len(fixed_lines):
            if '{% if' in fixed_lines[394] and '{% else' in fixed_lines[858]:
                print("\n确认第395行的if标签和第859行的else标签是匹配的")
                
                # 确保格式正确
                if not fixed_lines[394].strip().startswith('{% if'):
                    match = re.search(r'{%\s*if\s+(.+?)%}', fixed_lines[394])
                    if match:
                        condition = match.group(1).strip()
                        fixed_lines[394] = fixed_lines[394].replace(match.group(0), f"{{% if {condition} %}}")
                        fixed_count += 1
                        print(f"修复第395行if标签: {fixed_lines[394].strip()}")
                
                if not fixed_lines[858].strip() == '{% else %}':
                    fixed_lines[858] = '{% else %}\n'
                    fixed_count += 1
                    print(f"修复第859行else标签: {fixed_lines[858].strip()}")
                
                # 查找对应的endif标签
                for i in range(859, len(fixed_lines)):
                    if '{% endif' in fixed_lines[i]:
                        if not fixed_lines[i].strip() == '{% endif %}':
                            fixed_lines[i] = '{% endif %}\n'
                            fixed_count += 1
                            print(f"修复第{i+1}行endif标签: {fixed_lines[i].strip()}")
                        break
        
        # 写回修复后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(fixed_lines)
        
        print(f"\n修复了 {fixed_count} 处问题")
        print("请重新启动九猫系统进行测试")
        return True
    
    except Exception as e:
        print(f"修复文件时出错: {str(e)}")
        # 如果出错，恢复备份
        try:
            shutil.copy2(backup_file, file_path)
            print(f"已恢复备份")
        except:
            pass
        return False

if __name__ == "__main__":
    fix_analysis_html()
