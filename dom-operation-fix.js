/**
 * 九猫系统 - DOM操作修复脚本
 * 版本: 1.0.0
 * 
 * 该脚本解决各种DOM操作中的错误，包括appendChild、insertBefore、replaceChild等
 */

(function() {
    console.log('[九猫修复] DOM操作修复脚本已加载');
    
    // 检查是否已应用此修复
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded && window.__nineCatsFixes.loaded['dom-operation-fix']) {
        console.log('[九猫修复] DOM操作修复已经应用，跳过');
        return;
    }
    
    // 操作计数器和限制 - 防止无限递归
    const counters = {
        appendChild: 0,
        insertBefore: 0,
        replaceChild: 0,
        removeChild: 0,
        querySelector: 0,
        querySelectorAll: 0,
        limit: 50 // 最大操作次数，防止无限循环
    };
    
    // 重置计数器的函数
    function resetCounter(operation) {
        setTimeout(function() {
            counters[operation] = 0;
        }, 0);
    }
    
    // 检查计数器并增加
    function incrementCounter(operation) {
        counters[operation]++;
        
        // 如果达到限制，重置并返回true表示应该中断
        if (counters[operation] > counters.limit) {
            console.error('[九猫修复] 检测到可能的无限' + operation + '循环，已中断');
            resetCounter(operation);
            return true;
        }
        
        return false;
    }
    
    // ===== 保存原始DOM方法 =====
    const originalMethods = {
        appendChild: Node.prototype.appendChild,
        insertBefore: Node.prototype.insertBefore,
        replaceChild: Node.prototype.replaceChild,
        removeChild: Node.prototype.removeChild,
        querySelector: Element.prototype.querySelector,
        querySelectorAll: Element.prototype.querySelectorAll
    };
    
    // ===== 重写appendChild方法 =====
    Node.prototype.appendChild = function(child) {
        // 检查循环
        if (incrementCounter('appendChild')) {
            return child; // 直接返回child，防止崩溃
        }
        
        try {
            // 检查child是否为null或undefined
            if (!child) {
                console.warn('[九猫修复] appendChild被调用时child为null或undefined');
                resetCounter('appendChild');
                return null;
            }
            
            // 检查child是否已经是该节点的子节点
            if (this.contains && this.contains(child)) {
                console.warn('[九猫修复] appendChild: 节点已经是子节点，跳过操作');
                resetCounter('appendChild');
                return child;
            }
            
            // 正常调用原始方法
            const result = originalMethods.appendChild.call(this, child);
            resetCounter('appendChild');
            return result;
        } catch (e) {
            console.error('[九猫修复] appendChild错误:', e.message);
            
            // 尝试错误恢复
            try {
                // 如果是文本节点，尝试创建新的文本节点
                if (child.nodeType === 3) {
                    const newTextNode = document.createTextNode(child.textContent || '');
                    return originalMethods.appendChild.call(this, newTextNode);
                }
                
                // 如果是元素节点，尝试克隆它
                if (child.nodeType === 1) {
                    const newElement = child.cloneNode(true);
                    return originalMethods.appendChild.call(this, newElement);
                }
            } catch (e2) {
                console.error('[九猫修复] appendChild恢复尝试失败:', e2.message);
            }
            
            resetCounter('appendChild');
            return child; // 返回原始child以防止进一步错误
        }
    };
    
    // ===== 重写insertBefore方法 =====
    Node.prototype.insertBefore = function(newNode, referenceNode) {
        // 检查循环
        if (incrementCounter('insertBefore')) {
            return newNode;
        }
        
        try {
            // 检查参数
            if (!newNode) {
                console.warn('[九猫修复] insertBefore被调用时newNode为null或undefined');
                resetCounter('insertBefore');
                return null;
            }
            
            // 如果referenceNode为null，退回到appendChild
            if (!referenceNode) {
                console.log('[九猫修复] insertBefore: referenceNode为null，使用appendChild');
                return this.appendChild(newNode);
            }
            
            // 检查newNode是否已经是该节点的子节点
            if (this.contains && this.contains(newNode)) {
                console.warn('[九猫修复] insertBefore: 节点已经是子节点，尝试移动位置');
                // 先移除，再插入
                try {
                    this.removeChild(newNode);
                } catch (e) {
                    console.warn('[九猫修复] 从当前父节点移除失败:', e.message);
                }
            }
            
            // 检查referenceNode是否是该节点的子节点
            if (referenceNode.parentNode !== this) {
                console.warn('[九猫修复] insertBefore: referenceNode不是该节点的子节点，使用appendChild');
                return this.appendChild(newNode);
            }
            
            // 正常调用原始方法
            const result = originalMethods.insertBefore.call(this, newNode, referenceNode);
            resetCounter('insertBefore');
            return result;
        } catch (e) {
            console.error('[九猫修复] insertBefore错误:', e.message);
            
            // 特殊处理Unexpected identifier '$'错误
            if (e.message && e.message.includes('Unexpected identifier')) {
                console.log('[九猫修复] 检测到Unexpected identifier错误，尝试特殊处理');
                
                try {
                    // 对于脚本元素，清理内容中可能的模板字符串
                    if (newNode.tagName && newNode.tagName.toLowerCase() === 'script') {
                        if (newNode.textContent) {
                            // 替换所有的${为一个安全标记
                            const safeContent = newNode.textContent.replace(/\$\{/g, '/*$*/{');
                            newNode.textContent = safeContent;
                            return originalMethods.insertBefore.call(this, newNode, referenceNode);
                        }
                    }
                } catch (e2) {
                    console.error('[九猫修复] 特殊处理失败:', e2.message);
                }
            }
            
            // 尝试回退到appendChild
            try {
                console.log('[九猫修复] insertBefore失败，尝试使用appendChild');
                return this.appendChild(newNode);
            } catch (e2) {
                console.error('[九猫修复] appendChild回退也失败:', e2.message);
            }
            
            resetCounter('insertBefore');
            return newNode;
        }
    };
    
    // ===== 重写replaceChild方法 =====
    Node.prototype.replaceChild = function(newChild, oldChild) {
        // 检查循环
        if (incrementCounter('replaceChild')) {
            return oldChild;
        }
        
        try {
            // 检查参数
            if (!newChild) {
                console.warn('[九猫修复] replaceChild被调用时newChild为null或undefined');
                resetCounter('replaceChild');
                return oldChild;
            }
            
            if (!oldChild) {
                console.warn('[九猫修复] replaceChild被调用时oldChild为null或undefined');
                resetCounter('replaceChild');
                return null;
            }
            
            // 检查oldChild是否是该节点的子节点
            if (oldChild.parentNode !== this) {
                console.warn('[九猫修复] replaceChild: oldChild不是该节点的子节点，使用appendChild');
                return this.appendChild(newChild);
            }
            
            // 正常调用原始方法
            const result = originalMethods.replaceChild.call(this, newChild, oldChild);
            resetCounter('replaceChild');
            return result;
        } catch (e) {
            console.error('[九猫修复] replaceChild错误:', e.message);
            
            // 尝试先移除旧节点，再添加新节点
            try {
                this.removeChild(oldChild);
                return this.appendChild(newChild);
            } catch (e2) {
                console.error('[九猫修复] replaceChild恢复尝试失败:', e2.message);
            }
            
            resetCounter('replaceChild');
            return oldChild;
        }
    };
    
    // ===== 重写removeChild方法 =====
    Node.prototype.removeChild = function(child) {
        // 检查循环
        if (incrementCounter('removeChild')) {
            return child;
        }
        
        try {
            // 检查参数
            if (!child) {
                console.warn('[九猫修复] removeChild被调用时child为null或undefined');
                resetCounter('removeChild');
                return null;
            }
            
            // 检查child是否是该节点的子节点
            if (child.parentNode !== this) {
                console.warn('[九猫修复] removeChild: child不是该节点的子节点，跳过操作');
                resetCounter('removeChild');
                return child;
            }
            
            // 正常调用原始方法
            const result = originalMethods.removeChild.call(this, child);
            resetCounter('removeChild');
            return result;
        } catch (e) {
            console.error('[九猫修复] removeChild错误:', e.message);
            resetCounter('removeChild');
            return child;
        }
    };
    
    // ===== 添加安全的查询选择器 =====
    // 重写querySelector方法
    Element.prototype.querySelector = function(selector) {
        // 检查循环
        if (incrementCounter('querySelector')) {
            return null;
        }
        
        try {
            // 检查选择器
            if (!selector || typeof selector !== 'string') {
                console.warn('[九猫修复] querySelector被调用时selector无效:', selector);
                resetCounter('querySelector');
                return null;
            }
            
            // 尝试修复无效的选择器
            try {
                // 移除可能导致错误的字符
                selector = selector.replace(/[#.][0-9-]/g, function(match) {
                    return '\\' + match;
                });
            } catch (e) {
                console.warn('[九猫修复] 修复选择器失败:', e.message);
            }
            
            // 正常调用原始方法
            const result = originalMethods.querySelector.call(this, selector);
            resetCounter('querySelector');
            return result;
        } catch (e) {
            console.error('[九猫修复] querySelector错误:', e.message, '选择器:', selector);
            resetCounter('querySelector');
            return null;
        }
    };
    
    // 重写querySelectorAll方法
    Element.prototype.querySelectorAll = function(selector) {
        // 检查循环
        if (incrementCounter('querySelectorAll')) {
            return [];
        }
        
        try {
            // 检查选择器
            if (!selector || typeof selector !== 'string') {
                console.warn('[九猫修复] querySelectorAll被调用时selector无效:', selector);
                resetCounter('querySelectorAll');
                return [];
            }
            
            // 尝试修复无效的选择器
            try {
                // 移除可能导致错误的字符
                selector = selector.replace(/[#.][0-9-]/g, function(match) {
                    return '\\' + match;
                });
            } catch (e) {
                console.warn('[九猫修复] 修复选择器失败:', e.message);
            }
            
            // 正常调用原始方法
            const result = originalMethods.querySelectorAll.call(this, selector);
            resetCounter('querySelectorAll');
            return result;
        } catch (e) {
            console.error('[九猫修复] querySelectorAll错误:', e.message, '选择器:', selector);
            resetCounter('querySelectorAll');
            return [];
        }
    };
    
    // ===== 添加全局DOM错误处理 =====
    window.addEventListener('error', function(event) {
        // 检查是否为DOM操作相关错误
        if (event.message && (
            event.message.includes('appendChild') ||
            event.message.includes('insertBefore') ||
            event.message.includes('replaceChild') ||
            event.message.includes('removeChild') ||
            event.message.includes('querySelector') ||
            event.message.includes('innerHTML') ||
            event.message.includes('textContent') ||
            event.message.includes('Unexpected identifier')
        )) {
            console.log('[九猫修复] 捕获到DOM操作错误:', event.message);
            
            // 对于Unexpected identifier错误，尝试清理所有脚本标签
            if (event.message.includes('Unexpected identifier')) {
                try {
                    const scripts = document.querySelectorAll('script:not([src])');
                    scripts.forEach(function(script) {
                        if (script.textContent && script.textContent.includes('${')) {
                            console.log('[九猫修复] 清理包含${的内联脚本');
                            script.textContent = script.textContent.replace(/\$\{/g, '/*$*/{');
                        }
                    });
                } catch (e) {
                    console.error('[九猫修复] 清理脚本失败:', e.message);
                }
            }
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
    
    // 标记修复已加载
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded) {
        window.__nineCatsFixes.loaded['dom-operation-fix'] = true;
    }
    
    console.log('[九猫修复] DOM操作修复脚本加载完成');
})(); 