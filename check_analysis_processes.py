"""
检查数据库中的分析过程记录
"""
import os
import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

# 导入必要的模块
try:
    from src.db.connection import Session
    from src.models.analysis_process import AnalysisProcess
    from src.models.novel import Novel
    from src.models.analysis_result import AnalysisResult
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    sys.exit(1)

def check_database_tables():
    """检查数据库表是否存在"""
    try:
        session = Session()
        
        # 检查小说表
        novel_count = session.query(Novel).count()
        logger.info(f"小说表中有 {novel_count} 条记录")
        
        # 检查分析结果表
        result_count = session.query(AnalysisResult).count()
        logger.info(f"分析结果表中有 {result_count} 条记录")
        
        # 检查分析过程表
        try:
            process_count = session.query(AnalysisProcess).count()
            logger.info(f"分析过程表中有 {process_count} 条记录")
            table_exists = True
        except Exception as e:
            logger.error(f"查询分析过程表时出错: {e}")
            logger.error("分析过程表可能不存在")
            table_exists = False
        
        session.close()
        return table_exists
    except Exception as e:
        logger.error(f"检查数据库表时出错: {e}")
        return False

def check_novel_with_id(novel_id):
    """检查指定ID的小说是否存在"""
    try:
        session = Session()
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if novel:
            logger.info(f"找到小说: ID={novel.id}, 标题={novel.title}")
            exists = True
        else:
            logger.warning(f"未找到ID为{novel_id}的小说")
            exists = False
        session.close()
        return exists
    except Exception as e:
        logger.error(f"检查小说时出错: {e}")
        return False

def check_analysis_results_for_novel(novel_id):
    """检查指定小说的分析结果"""
    try:
        session = Session()
        results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
        logger.info(f"小说ID={novel_id}的分析结果: {len(results)} 条")
        for result in results:
            logger.info(f"  维度={result.dimension}, 创建时间={result.created_at}")
        session.close()
        return len(results) > 0
    except Exception as e:
        logger.error(f"检查分析结果时出错: {e}")
        return False

def check_analysis_processes_for_novel(novel_id):
    """检查指定小说的分析过程记录"""
    try:
        session = Session()
        processes = session.query(AnalysisProcess).filter_by(novel_id=novel_id).all()
        logger.info(f"小说ID={novel_id}的分析过程记录: {len(processes)} 条")
        
        # 按维度分组
        dimension_groups = {}
        for process in processes:
            if process.dimension not in dimension_groups:
                dimension_groups[process.dimension] = []
            dimension_groups[process.dimension].append(process)
        
        # 显示每个维度的记录数
        for dimension, dimension_processes in dimension_groups.items():
            logger.info(f"  维度={dimension}: {len(dimension_processes)} 条记录")
        
        session.close()
        return len(processes) > 0
    except Exception as e:
        logger.error(f"检查分析过程记录时出错: {e}")
        return False

def check_analysis_processes_for_dimension(novel_id, dimension):
    """检查指定小说和维度的分析过程记录"""
    try:
        session = Session()
        processes = session.query(AnalysisProcess).filter_by(
            novel_id=novel_id, dimension=dimension
        ).order_by(
            AnalysisProcess.processing_stage,
            AnalysisProcess.block_index,
            AnalysisProcess.stage_index
        ).all()
        
        logger.info(f"小说ID={novel_id}, 维度={dimension}的分析过程记录: {len(processes)} 条")
        
        # 按阶段分组
        stage_groups = {}
        for process in processes:
            if process.processing_stage not in stage_groups:
                stage_groups[process.processing_stage] = []
            stage_groups[process.processing_stage].append(process)
        
        # 显示每个阶段的记录数
        for stage, stage_processes in stage_groups.items():
            logger.info(f"  阶段={stage}: {len(stage_processes)} 条记录")
            # 显示前3条记录的详细信息
            for i, process in enumerate(stage_processes[:3]):
                logger.info(f"    记录{i+1}: ID={process.id}, 块索引={process.block_index}, 创建时间={process.created_at}")
                if process.input_text:
                    input_preview = process.input_text[:50] + "..." if len(process.input_text) > 50 else process.input_text
                    logger.info(f"      输入文本: {input_preview}")
                if process.output_text:
                    output_preview = process.output_text[:50] + "..." if len(process.output_text) > 50 else process.output_text
                    logger.info(f"      输出文本: {output_preview}")
        
        session.close()
        return len(processes) > 0
    except Exception as e:
        logger.error(f"检查分析过程记录时出错: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始检查数据库中的分析过程记录")
    
    # 检查数据库表
    tables_ok = check_database_tables()
    if not tables_ok:
        logger.error("数据库表检查失败，无法继续")
        return
    
    # 检查小说ID=4的记录
    novel_id = 4
    novel_ok = check_novel_with_id(novel_id)
    if not novel_ok:
        logger.warning(f"未找到ID为{novel_id}的小说，尝试查找其他小说")
        # 尝试查找其他小说
        try:
            session = Session()
            novel = session.query(Novel).first()
            if novel:
                novel_id = novel.id
                logger.info(f"找到小说: ID={novel.id}, 标题={novel.title}")
                novel_ok = True
            session.close()
        except Exception as e:
            logger.error(f"查找小说时出错: {e}")
    
    if not novel_ok:
        logger.error("未找到任何小说，无法继续")
        return
    
    # 检查分析结果
    results_ok = check_analysis_results_for_novel(novel_id)
    if not results_ok:
        logger.warning(f"未找到小说ID={novel_id}的分析结果")
    
    # 检查分析过程记录
    processes_ok = check_analysis_processes_for_novel(novel_id)
    if not processes_ok:
        logger.warning(f"未找到小说ID={novel_id}的分析过程记录")
    
    # 检查language_style维度的分析过程记录
    dimension = "language_style"
    dimension_processes_ok = check_analysis_processes_for_dimension(novel_id, dimension)
    if not dimension_processes_ok:
        logger.warning(f"未找到小说ID={novel_id}, 维度={dimension}的分析过程记录")
    
    logger.info("检查完成")

if __name__ == "__main__":
    main()
