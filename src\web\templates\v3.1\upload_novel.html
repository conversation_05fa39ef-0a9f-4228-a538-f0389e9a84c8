{% extends "v3.1/base.html" %}

{% block title %}上传小说 - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .upload-option {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .upload-option:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px var(--shadow-color);
    }

    .upload-option.active {
        border-color: var(--primary-color);
        background-color: rgba(230, 180, 34, 0.05);
    }

    .upload-option-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }

    .dimension-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .dimension-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .dimension-item:hover {
        background-color: var(--shadow-color);
    }

    .dimension-item.active {
        background-color: var(--primary-light);
        border-color: var(--primary-color);
        color: var(--dark-color);
    }

    .dimension-item input[type="checkbox"] {
        margin-right: 0.5rem;
    }

    #fileUploadArea, #textUploadArea {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h1 class="mb-2"><i class="fas fa-upload text-primary me-2"></i>上传小说</h1>
                <p class="lead mb-0">上传您的小说文件或直接粘贴文本内容，开始分析您的作品。</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0">上传信息</h3>
            </div>
            <div class="card-body">
                {% if error %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                </div>
                {% endif %}

                <form method="post" action="{{ url_for('v3_1.upload_novel') }}" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="title" class="form-label">小说标题 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>

                    <div class="mb-4">
                        <label for="author" class="form-label">作者</label>
                        <input type="text" class="form-control" id="author" name="author" placeholder="可选">
                    </div>

                    <div class="mb-4">
                        <label class="form-label">上传方式 <span class="text-danger">*</span></label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="upload-option active" id="fileUploadOption">
                                    <div class="text-center">
                                        <div class="upload-option-icon">
                                            <i class="fas fa-file-upload"></i>
                                        </div>
                                        <h4>文件上传</h4>
                                        <p class="text-muted">上传TXT格式的小说文件</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="upload-option" id="textUploadOption">
                                    <div class="text-center">
                                        <div class="upload-option-icon">
                                            <i class="fas fa-paste"></i>
                                        </div>
                                        <h4>文本粘贴</h4>
                                        <p class="text-muted">直接粘贴小说文本内容</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="uploadType" id="uploadType" value="file">
                    </div>

                    <div id="fileUploadArea">
                        <div class="mb-4">
                            <label for="file" class="form-label">选择文件 <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="file" name="file" accept=".txt">
                            <div class="form-text">支持的文件格式：TXT，最大文件大小：10MB</div>
                        </div>
                    </div>

                    <div id="textUploadArea">
                        <div class="mb-4">
                            <label for="content" class="form-label">小说内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="10" placeholder="在此粘贴小说内容..."></textarea>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoAnalyze" name="autoAnalyze">
                            <label class="form-check-label" for="autoAnalyze">
                                上传后自动分析
                            </label>
                        </div>
                    </div>

                    <div id="dimensionsArea" style="display: none;">
                        <div class="mb-4">
                            <label class="form-label">选择分析维度</label>
                            <div class="dimension-list">
                                {% for dimension in dimensions %}
                                <div class="dimension-item">
                                    <input type="checkbox" id="dimension_{{ dimension.key }}" name="dimensions" value="{{ dimension.key }}">
                                    <label for="dimension_{{ dimension.key }}">{{ dimension.name }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <div class="text-end">
                        <a href="{{ url_for('v3_1.novels') }}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i>返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>上传
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0">上传说明</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5><i class="fas fa-file-alt me-2 text-primary"></i>文件格式</h5>
                    <p>支持的文件格式为TXT纯文本文件，建议使用UTF-8编码。</p>
                </div>
                <div class="mb-3">
                    <h5><i class="fas fa-text-height me-2 text-primary"></i>文本格式</h5>
                    <p>建议小说文本有明确的章节划分，如"第一章"、"Chapter 1"等，以便系统自动识别章节。</p>
                </div>
                <div class="mb-3">
                    <h5><i class="fas fa-search me-2 text-primary"></i>自动分析</h5>
                    <p>选择"上传后自动分析"可以在上传完成后立即开始分析，您可以选择需要分析的维度。</p>
                </div>
                <div>
                    <h5><i class="fas fa-info-circle me-2 text-primary"></i>注意事项</h5>
                    <p>分析过程可能需要几分钟时间，请耐心等待。您可以在分析过程中离开页面，系统会在后台继续分析。</p>
                </div>
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0">分析维度说明</h3>
            </div>
            <div class="card-body">
                <div class="accordion" id="dimensionAccordion">
                    {% for dimension in dimensions %}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading{{ dimension.key }}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ dimension.key }}" aria-expanded="false" aria-controls="collapse{{ dimension.key }}">
                                {{ dimension.name }}
                            </button>
                        </h2>
                        <div id="collapse{{ dimension.key }}" class="accordion-collapse collapse" aria-labelledby="heading{{ dimension.key }}" data-bs-parent="#dimensionAccordion">
                            <div class="accordion-body">
                                {{ dimension.description }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 显示文件上传区域
        $('#fileUploadArea').show();

        // 上传方式切换
        $('#fileUploadOption').click(function() {
            $(this).addClass('active');
            $('#textUploadOption').removeClass('active');
            $('#fileUploadArea').show();
            $('#textUploadArea').hide();
            $('#uploadType').val('file');
        });

        $('#textUploadOption').click(function() {
            $(this).addClass('active');
            $('#fileUploadOption').removeClass('active');
            $('#textUploadArea').show();
            $('#fileUploadArea').hide();
            $('#uploadType').val('text');
        });

        // 自动分析复选框
        $('#autoAnalyze').change(function() {
            if ($(this).is(':checked')) {
                $('#dimensionsArea').slideDown();
            } else {
                $('#dimensionsArea').slideUp();
            }
        });

        // 维度选择项点击
        $('.dimension-item').click(function() {
            $(this).toggleClass('active');
            const checkbox = $(this).find('input[type="checkbox"]');
            checkbox.prop('checked', !checkbox.prop('checked'));
        });

        // 防止复选框点击事件冒泡
        $('.dimension-item input[type="checkbox"]').click(function(e) {
            e.stopPropagation();
            $(this).closest('.dimension-item').toggleClass('active');
        });

        // 表单提交验证
        $('form').submit(function(e) {
            const uploadType = $('#uploadType').val();

            // 检查文件上传
            if (uploadType === 'file') {
                const fileInput = $('#file')[0];
                if (!fileInput.files || fileInput.files.length === 0) {
                    e.preventDefault();
                    alert('请选择要上传的文件');
                    return false;
                }
            }

            // 检查文本粘贴
            if (uploadType === 'text') {
                const content = $('#content').val().trim();
                if (!content) {
                    e.preventDefault();
                    alert('请输入小说内容');
                    return false;
                }
            }

            // 检查自动分析选项
            if ($('#autoAnalyze').is(':checked')) {
                const selectedDimensions = $('input[name="dimensions"]:checked');
                if (selectedDimensions.length === 0) {
                    e.preventDefault();
                    alert('请至少选择一个分析维度');
                    return false;
                }
            }

            return true;
        });
    });
</script>
{% endblock %}