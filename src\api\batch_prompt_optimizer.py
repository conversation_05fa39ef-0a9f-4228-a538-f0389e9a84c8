"""
分批次分段提示词优化器
专门用于处理长提示词的分批次执行，充分利用DeepSeek R1的思考能力
"""

import logging
from typing import Dict, List, Tuple, Any
import time

logger = logging.getLogger(__name__)


class BatchPromptOptimizer:
    """分批次分段提示词优化器"""

    @staticmethod
    def optimize_long_prompt_for_deepseek_r1(base_prompt: str,
                                           analysis_type: str = "writing",
                                           max_tokens_per_batch: int = 4000) -> List[Dict[str, Any]]:
        """
        将长提示词优化为分批次执行的策略，充分利用DeepSeek R1思考能力

        Args:
            base_prompt: 原始长提示词
            analysis_type: 分析类型
            max_tokens_per_batch: 每批次最大token数

        Returns:
            分批次执行计划列表
        """
        logger.info(f"开始优化长提示词，原始长度: {len(base_prompt)} 字符")

        # 分析提示词结构
        prompt_structure = BatchPromptOptimizer._analyze_prompt_structure(base_prompt)

        # 根据分析类型选择优化策略
        if analysis_type == "writing":
            return BatchPromptOptimizer._optimize_writing_prompt(base_prompt, prompt_structure)
        elif analysis_type == "analysis":
            return BatchPromptOptimizer._optimize_analysis_prompt(base_prompt, prompt_structure)
        else:
            return BatchPromptOptimizer._optimize_general_prompt(base_prompt, prompt_structure)

    @staticmethod
    def _analyze_prompt_structure(prompt: str) -> Dict[str, Any]:
        """分析提示词结构"""
        structure = {
            "total_length": len(prompt),
            "sections": [],
            "has_examples": "例如" in prompt or "示例" in prompt,
            "has_requirements": "要求" in prompt or "必须" in prompt,
            "has_constraints": "禁止" in prompt or "不要" in prompt,
            "complexity_level": "high" if len(prompt) > 5000 else "medium" if len(prompt) > 2000 else "low"
        }

        # 识别主要段落
        sections = prompt.split('\n\n')
        for i, section in enumerate(sections):
            if len(section.strip()) > 50:
                section_type = BatchPromptOptimizer._identify_section_type(section)
                structure["sections"].append({
                    "index": i,
                    "content": section,
                    "type": section_type,
                    "length": len(section),
                    "priority": BatchPromptOptimizer._calculate_section_priority(section, section_type)
                })

        return structure

    @staticmethod
    def _identify_section_type(section: str) -> str:
        """识别段落类型"""
        section_lower = section.lower()

        if any(keyword in section_lower for keyword in ["思考", "分析", "理解"]):
            return "thinking_activation"
        elif any(keyword in section_lower for keyword in ["要求", "指令", "任务"]):
            return "requirements"
        elif any(keyword in section_lower for keyword in ["例子", "示例", "样本"]):
            return "examples"
        elif any(keyword in section_lower for keyword in ["禁止", "不要", "限制"]):
            return "constraints"
        elif any(keyword in section_lower for keyword in ["背景", "信息", "数据"]):
            return "context"
        else:
            return "general"

    @staticmethod
    def _calculate_section_priority(section: str, section_type: str) -> int:
        """计算段落优先级（1-5，5最高）"""
        priority_map = {
            "thinking_activation": 5,  # 思考激活最重要
            "requirements": 4,         # 要求次重要
            "constraints": 4,          # 限制同样重要
            "context": 3,              # 背景信息中等
            "examples": 2,             # 示例较低
            "general": 1               # 一般内容最低
        }

        base_priority = priority_map.get(section_type, 1)

        # 根据内容长度调整优先级
        if len(section) > 1000:
            base_priority = max(1, base_priority - 1)  # 过长的段落降低优先级

        return base_priority

    @staticmethod
    def _optimize_writing_prompt(prompt: str, structure: Dict[str, Any]) -> List[Dict[str, Any]]:
        """优化写作类提示词"""
        batches = []

        # 第一批次：思考激活 + 核心要求
        batch_1_sections = []
        batch_2_sections = []
        batch_3_sections = []

        # 按优先级分配到不同批次
        for section in structure["sections"]:
            if section["priority"] >= 4:
                batch_1_sections.append(section)
            elif section["priority"] >= 2:
                batch_2_sections.append(section)
            else:
                batch_3_sections.append(section)

        # 构建第一批次：思考激活 + 核心指令
        if batch_1_sections:
            batch_1_content = BatchPromptOptimizer._build_thinking_activation_prompt()
            batch_1_content += "\n\n" + "\n\n".join([s["content"] for s in batch_1_sections])

            batches.append({
                "batch_number": 1,
                "purpose": "思考激活与核心指令理解",
                "content": batch_1_content,
                "expected_output": "深度思考过程 + 基础框架",
                "deepseek_r1_focus": "激活思考链，理解核心要求",
                "max_tokens": 6000
            })

        # 构建第二批次：基于第一批次结果的深化
        if batch_2_sections:
            batch_2_content = BatchPromptOptimizer._build_enhancement_prompt()
            batch_2_content += "\n\n" + "\n\n".join([s["content"] for s in batch_2_sections])

            batches.append({
                "batch_number": 2,
                "purpose": "基于第一批次结果的内容深化",
                "content": batch_2_content,
                "expected_output": "深化优化的内容",
                "deepseek_r1_focus": "基于前次思考结果进行深化分析",
                "max_tokens": 8000,
                "depends_on_batch": 1
            })

        # 构建第三批次：最终精炼
        if batch_3_sections:
            batch_3_content = BatchPromptOptimizer._build_refinement_prompt()
            batch_3_content += "\n\n" + "\n\n".join([s["content"] for s in batch_3_sections])

            batches.append({
                "batch_number": 3,
                "purpose": "最终质量检查与精炼",
                "content": batch_3_content,
                "expected_output": "最终精炼的高质量内容",
                "deepseek_r1_focus": "综合前两次结果进行最终优化",
                "max_tokens": 6000,
                "depends_on_batch": 2
            })

        logger.info(f"写作提示词已优化为 {len(batches)} 个批次")
        return batches

    @staticmethod
    def _build_thinking_activation_prompt() -> str:
        """构建思考激活提示词，包含四个强制执行要求"""
        return """
## 🧠 DeepSeek R1深度思考激活指令

请在处理任务前，充分发挥你的思考能力：

### 🚨 四个强制执行要求（必须严格遵守）
**无论在哪个批次，以下四点必须100%执行，不得有任何妥协：**

1. **直接套用原文的句式结构、语言特征**
   - 必须深度学习原文的句式模式和语言风格
   - 严格模仿原文的表达方式和叙述习惯
   - 禁止复制情节，但必须复制语言特征

2. **更偏向于短句、日常化通俗口语化**
   - 优先使用短句，避免长句和复杂从句
   - 使用日常生活化的词汇和表达
   - 采用通俗口语化的叙述方式

3. **要有十分严密的逻辑性、因果联系**
   - 每个事件都必须有明确的原因和结果
   - 人物行为必须符合逻辑和性格设定
   - 情节发展必须有清晰的因果关系链

4. **特别注意生成内容章节之间的联系与继承**
   - 必须与前序章节保持连贯性
   - 人物名称、性格、关系绝对一致
   - 情节发展必须自然承接前文

### 第一阶段：全面理解
1. **深度分析**：仔细分析所有给定的信息和要求
2. **四要求确认**：确认完全理解上述四个强制要求
3. **目标明确**：确定最终要达成的具体目标

### 第二阶段：策略规划
1. **方法选择**：选择最适合的处理方法和策略
2. **四要求融入**：将四个强制要求融入执行策略
3. **质量标准**：确定质量检查的标准和要求

### 第三阶段：执行准备
1. **资源整合**：整合所有可用的信息和资源
2. **四要求检查**：再次确认四个要求的执行方案
3. **执行确认**：确认理解无误后开始执行

⚠️ 重要提醒：四个强制要求是不可妥协的底线，必须在每个批次中严格执行！

请充分思考后再开始执行任务。
"""

    @staticmethod
    def _build_enhancement_prompt() -> str:
        """构建深化优化提示词，强化四个强制要求"""
        return """
## 🔧 第二批次：基于前次结果的深化优化

### 🚨 四个强制执行要求（再次强调，必须严格遵守）
**在第二批次中，以下四点必须继续100%执行：**

1. **直接套用原文的句式结构、语言特征**
   - 继续深度学习和模仿原文的语言风格
   - 在深化内容时保持原文的句式特征
   - 绝不复制情节，但必须复制语言模式

2. **更偏向于短句、日常化通俗口语化**
   - 在扩展内容时优先使用短句
   - 深化部分必须保持通俗易懂
   - 避免因为深化而使用复杂表达

3. **要有十分严密的逻辑性、因果联系**
   - 深化的每个细节都必须有逻辑支撑
   - 新增内容必须与原有逻辑完全吻合
   - 确保因果关系链的完整性

4. **特别注意生成内容章节之间的联系与继承**
   - 深化内容必须与前序章节完全连贯
   - 绝对不能改变已确定的人物设定
   - 新增情节必须自然承接前文

### 任务说明
基于第一批次的思考和初步结果，进行深化优化：

1. **继承前次成果**：充分利用第一批次的思考成果
2. **深化分析**：在前次基础上进行更深入的分析
3. **优化改进**：针对发现的问题进行优化改进
4. **质量提升**：提升整体质量和完整性

### 执行要求
- 不要重复第一批次已完成的工作
- 专注于深化和优化
- 保持与前次结果的一致性
- **严格执行四个强制要求，不得有任何松懈**

⚠️ 重要提醒：四个强制要求在第二批次中同样是不可妥协的底线！
"""

    @staticmethod
    def _build_refinement_prompt() -> str:
        """构建精炼优化提示词，最终强化四个强制要求"""
        return """
## ✨ 第三批次：最终精炼与质量检查

### 🚨 四个强制执行要求（最终检查，必须100%达标）
**在第三批次最终精炼中，以下四点必须达到完美执行：**

1. **直接套用原文的句式结构、语言特征**
   - 最终检查：是否完美模仿了原文的语言风格
   - 精炼调整：确保每个句子都符合原文特征
   - 绝对禁止：任何偏离原文语言模式的表达

2. **更偏向于短句、日常化通俗口语化**
   - 最终检查：是否所有句子都简洁明了
   - 精炼调整：将任何复杂句子改为短句
   - 绝对禁止：文绉绉或过于书面化的表达

3. **要有十分严密的逻辑性、因果联系**
   - 最终检查：每个情节是否都有清晰的因果关系
   - 精炼调整：修复任何逻辑漏洞或矛盾
   - 绝对禁止：任何突兀或缺乏逻辑的情节

4. **特别注意生成内容章节之间的联系与继承**
   - 最终检查：是否与前序章节完美连贯
   - 精炼调整：确保人物设定和情节发展一致
   - 绝对禁止：任何与前文矛盾的内容

### 任务说明
基于前两批次的成果，进行最终精炼：

1. **综合整合**：整合前两批次的所有成果
2. **质量检查**：进行全面的质量检查
3. **细节完善**：完善所有细节和不足
4. **最终优化**：进行最终的优化和调整

### 质量标准
- 逻辑严密，无矛盾
- 表达清晰，易理解
- 内容完整，无遗漏
- 质量优秀，达到要求
- **四个强制要求100%达标**

### 🔍 最终检查清单
在输出前，必须逐一检查：
□ 是否完美模仿了原文的句式和语言特征？
□ 是否全部使用短句和通俗口语化表达？
□ 是否每个情节都有严密的逻辑和因果关系？
□ 是否与前序章节完全连贯一致？

⚠️ 最终警告：四个强制要求是质量底线，任何一项不达标都不能输出！
"""

    @staticmethod
    def _optimize_analysis_prompt(prompt: str, structure: Dict[str, Any]) -> List[Dict[str, Any]]:
        """优化分析类提示词"""
        # 分析类提示词的优化策略
        batches = []

        # 简化为两个批次：理解分析 + 结果生成
        batch_1_content = BatchPromptOptimizer._build_thinking_activation_prompt()
        batch_1_content += "\n\n" + prompt[:len(prompt)//2]

        batch_2_content = "基于第一批次的理解，继续完成分析任务：\n\n"
        batch_2_content += prompt[len(prompt)//2:]

        batches.append({
            "batch_number": 1,
            "purpose": "深度理解分析要求",
            "content": batch_1_content,
            "expected_output": "分析思路和初步结果",
            "deepseek_r1_focus": "理解分析要求，制定分析策略",
            "max_tokens": 6000
        })

        batches.append({
            "batch_number": 2,
            "purpose": "完成详细分析",
            "content": batch_2_content,
            "expected_output": "完整的分析结果",
            "deepseek_r1_focus": "基于理解进行详细分析",
            "max_tokens": 8000,
            "depends_on_batch": 1
        })

        return batches

    @staticmethod
    def _optimize_general_prompt(prompt: str, structure: Dict[str, Any]) -> List[Dict[str, Any]]:
        """优化一般类提示词"""
        # 一般提示词的简单分割策略
        batches = []

        if structure["total_length"] > 3000:
            # 分为两个批次
            mid_point = len(prompt) // 2

            batch_1_content = BatchPromptOptimizer._build_thinking_activation_prompt()
            batch_1_content += "\n\n" + prompt[:mid_point]

            batch_2_content = "继续处理任务：\n\n" + prompt[mid_point:]

            batches.append({
                "batch_number": 1,
                "purpose": "理解任务要求",
                "content": batch_1_content,
                "expected_output": "任务理解和初步处理",
                "deepseek_r1_focus": "深度理解任务要求",
                "max_tokens": 6000
            })

            batches.append({
                "batch_number": 2,
                "purpose": "完成任务处理",
                "content": batch_2_content,
                "expected_output": "完整的任务结果",
                "deepseek_r1_focus": "基于理解完成任务",
                "max_tokens": 8000,
                "depends_on_batch": 1
            })
        else:
            # 单批次处理
            enhanced_prompt = BatchPromptOptimizer._build_thinking_activation_prompt()
            enhanced_prompt += "\n\n" + prompt

            batches.append({
                "batch_number": 1,
                "purpose": "完整任务处理",
                "content": enhanced_prompt,
                "expected_output": "完整的任务结果",
                "deepseek_r1_focus": "深度思考并完成任务",
                "max_tokens": 8000
            })

        return batches

    @staticmethod
    def execute_batch_sequence(batches: List[Dict[str, Any]],
                             api_client,
                             delay_between_batches: int = 3) -> str:
        """
        执行分批次序列

        Args:
            batches: 批次列表
            api_client: API客户端
            delay_between_batches: 批次间延迟（秒）

        Returns:
            最终结果
        """
        logger.info(f"开始执行 {len(batches)} 个批次的序列")

        results = {}

        for batch in batches:
            batch_num = batch["batch_number"]
            logger.info(f"执行第 {batch_num} 批次：{batch['purpose']}")

            # 如果依赖前一批次，需要将前一批次结果加入提示词
            prompt = batch["content"]
            if "depends_on_batch" in batch and batch["depends_on_batch"] in results:
                previous_result = results[batch["depends_on_batch"]]
                prompt = f"前一批次结果：\n{previous_result}\n\n{prompt}"

            try:
                # 调用API
                result = api_client.analyze_text(
                    text=prompt,
                    analysis_type="batch_processing",
                    max_tokens=batch.get("max_tokens", 6000),
                    temperature=0.3
                )

                results[batch_num] = result
                logger.info(f"第 {batch_num} 批次完成，结果长度: {len(str(result))}")

                # 批次间延迟
                if batch_num < len(batches):
                    logger.info(f"批次间延迟 {delay_between_batches} 秒")
                    time.sleep(delay_between_batches)

            except Exception as e:
                logger.error(f"第 {batch_num} 批次执行失败: {str(e)}")
                # 如果某个批次失败，返回已有的最佳结果
                if results:
                    return str(results[max(results.keys())])
                else:
                    raise e

        # 返回最后一个批次的结果
        final_result = results[max(results.keys())]
        logger.info(f"分批次执行完成，最终结果长度: {len(str(final_result))}")

        return str(final_result)
