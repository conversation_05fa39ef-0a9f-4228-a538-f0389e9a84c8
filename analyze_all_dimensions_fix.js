/**
 * 九猫系统 - 分析所有维度修复脚本
 * 修复"开始分析所有维度"功能的400错误问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('[九猫修复] 分析所有维度修复脚本已加载');

    // 配置
    const CONFIG = {
        // 调试模式
        debugMode: false,
        // 所有维度列表
        allDimensions: [
            'language_style',
            'rhythm_pacing',
            'structure',
            'sentence_variation',
            'paragraph_length',
            'perspective_shifts',
            'paragraph_flow',
            'novel_characteristics',
            'world_building',
            'character_relationships',
            'opening_effectiveness',
            'climax_pacing',
            'theme_analysis'
        ],
        // API路径
        apiPaths: {
            analyzeNovel: '/api/novel/{novelId}/analyze',
            analyzeChapter: '/api/novel/{novelId}/chapter/{chapterId}/analyze'
        },
        // 进度轮询间隔（毫秒）
        pollInterval: 2000
    };

    // 状态
    const STATE = {
        // 是否正在分析
        isAnalyzing: false,
        // 轮询定时器ID
        pollTimerId: null,
        // 当前小说ID
        novelId: null,
        // 当前章节ID
        chapterId: null,
        // 分析开始时间
        startTime: null
    };

    // 日志函数
    function log(message, level = 'info') {
        const prefix = '[九猫修复-分析所有维度]';
        
        if (level === 'error') {
            console.error(`${prefix} ${message}`);
        } else if (level === 'warn') {
            console.warn(`${prefix} ${message}`);
        } else if (CONFIG.debugMode || level === 'info') {
            console.info(`${prefix} ${message}`);
        }
    }

    // 获取页面信息
    function getPageInfo() {
        // 尝试从URL获取小说ID和章节ID
        const pathParts = window.location.pathname.split('/');
        let novelId = null;
        let chapterId = null;

        // 查找小说ID
        for (let i = 0; i < pathParts.length; i++) {
            if (pathParts[i] === 'novel' && i + 1 < pathParts.length) {
                novelId = pathParts[i + 1];
                break;
            }
        }

        // 查找章节ID
        for (let i = 0; i < pathParts.length; i++) {
            if (pathParts[i] === 'chapter' && i + 1 < pathParts.length) {
                chapterId = pathParts[i + 1];
                break;
            }
        }

        // 如果URL中没有，尝试从页面元素获取
        if (!novelId) {
            const novelElement = document.querySelector('[data-novel-id]');
            if (novelElement) {
                novelId = novelElement.getAttribute('data-novel-id');
            }
        }

        if (!chapterId) {
            const chapterElement = document.querySelector('[data-chapter-id]');
            if (chapterElement) {
                chapterId = chapterElement.getAttribute('data-chapter-id');
            }
        }

        return { novelId, chapterId };
    }

    // 开始分析所有维度
    function startAnalyzeAllDimensions() {
        // 获取页面信息
        const pageInfo = getPageInfo();
        if (!pageInfo.novelId) {
            log('无法获取小说ID，无法开始分析', 'error');
            alert('无法获取小说ID，请刷新页面后重试');
            return;
        }

        // 更新状态
        STATE.isAnalyzing = true;
        STATE.novelId = pageInfo.novelId;
        STATE.chapterId = pageInfo.chapterId;
        STATE.startTime = new Date();

        log(`开始分析所有维度: 小说ID=${STATE.novelId}${STATE.chapterId ? ', 章节ID=' + STATE.chapterId : ''}`);

        // 显示进度对话框
        showProgressDialog();

        // 构建API URL
        let apiUrl;
        if (STATE.chapterId) {
            // 章节分析
            apiUrl = CONFIG.apiPaths.analyzeChapter
                .replace('{novelId}', STATE.novelId)
                .replace('{chapterId}', STATE.chapterId);
        } else {
            // 整本书分析
            apiUrl = CONFIG.apiPaths.analyzeNovel
                .replace('{novelId}', STATE.novelId);
        }

        // 发送分析请求
        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            },
            body: JSON.stringify({
                dimensions: CONFIG.allDimensions
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            log('分析请求成功:', data);
            
            if (data.success) {
                // 开始轮询进度
                startPollingProgress();
            } else {
                // 显示错误信息
                showErrorMessage(data.error || '分析请求失败，请重试');
                STATE.isAnalyzing = false;
            }
        })
        .catch(error => {
            log(`分析请求失败: ${error.message}`, 'error');
            showErrorMessage(`分析请求失败: ${error.message}`);
            STATE.isAnalyzing = false;
        });
    }

    // 显示进度对话框
    function showProgressDialog() {
        // 检查是否已存在进度对话框
        let progressDialog = document.getElementById('analyzeAllProgressDialog');
        
        if (!progressDialog) {
            // 创建进度对话框
            progressDialog = document.createElement('div');
            progressDialog.id = 'analyzeAllProgressDialog';
            progressDialog.className = 'modal fade';
            progressDialog.setAttribute('tabindex', '-1');
            progressDialog.setAttribute('role', 'dialog');
            progressDialog.setAttribute('aria-labelledby', 'analyzeAllProgressTitle');
            progressDialog.setAttribute('aria-hidden', 'true');
            
            progressDialog.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="analyzeAllProgressTitle">分析进度</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                        </div>
                        <div class="modal-body">
                            <div class="progress mb-3">
                                <div id="analyzeAllProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <p id="analyzeAllProgressMessage">正在准备分析...</p>
                            <p id="analyzeAllProgressTime" class="text-muted small">已用时间: 0秒</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" id="analyzeAllRefreshBtn">刷新页面</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(progressDialog);
            
            // 绑定刷新按钮事件
            document.getElementById('analyzeAllRefreshBtn').addEventListener('click', function() {
                window.location.reload();
            });
        }
        
        // 显示对话框
        try {
            // 尝试使用Bootstrap 5的方式显示
            const modal = new bootstrap.Modal(progressDialog);
            modal.show();
        } catch (e) {
            // 如果失败，尝试使用jQuery方式显示
            try {
                $(progressDialog).modal('show');
            } catch (e2) {
                // 如果还是失败，使用原生方式显示
                progressDialog.style.display = 'block';
                progressDialog.classList.add('show');
            }
        }
    }

    // 显示错误信息
    function showErrorMessage(message) {
        const progressMessage = document.getElementById('analyzeAllProgressMessage');
        if (progressMessage) {
            progressMessage.innerHTML = `<div class="alert alert-danger">${message}</div>`;
        } else {
            alert(message);
        }
    }

    // 开始轮询进度
    function startPollingProgress() {
        // 清除现有定时器
        if (STATE.pollTimerId) {
            clearInterval(STATE.pollTimerId);
        }
        
        // 设置定时器
        STATE.pollTimerId = setInterval(updateProgress, CONFIG.pollInterval);
        
        // 立即更新一次
        updateProgress();
    }

    // 更新进度
    function updateProgress() {
        // 更新已用时间
        updateElapsedTime();
        
        // 如果没有小说ID，停止轮询
        if (!STATE.novelId) {
            clearInterval(STATE.pollTimerId);
            return;
        }
        
        // 构建API URL
        const apiUrl = `/api/analysis/progress?novel_id=${STATE.novelId}`;
        
        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok && response.status !== 404) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 检查是否有进度数据
                if (data.success && data.progress) {
                    // 计算总进度
                    let totalProgress = 0;
                    let completedDimensions = 0;
                    
                    // 遍历所有维度
                    for (const dimension in data.progress) {
                        const dimensionProgress = data.progress[dimension].progress || 0;
                        totalProgress += dimensionProgress;
                        
                        if (dimensionProgress >= 100) {
                            completedDimensions++;
                        }
                    }
                    
                    // 计算平均进度
                    const avgProgress = Math.min(100, Math.round(totalProgress / CONFIG.allDimensions.length));
                    
                    // 更新进度条
                    updateProgressBar(avgProgress);
                    
                    // 更新进度信息
                    const progressMessage = document.getElementById('analyzeAllProgressMessage');
                    if (progressMessage) {
                        progressMessage.textContent = `已完成 ${completedDimensions}/${CONFIG.allDimensions.length} 个维度，总进度: ${avgProgress}%`;
                    }
                    
                    // 检查是否全部完成
                    if (avgProgress >= 100 || completedDimensions >= CONFIG.allDimensions.length) {
                        // 停止轮询
                        clearInterval(STATE.pollTimerId);
                        
                        // 更新状态
                        STATE.isAnalyzing = false;
                        
                        // 显示完成信息
                        if (progressMessage) {
                            progressMessage.innerHTML = `<div class="alert alert-success">分析已完成！请点击"刷新页面"按钮查看结果。</div>`;
                        }
                    }
                }
            })
            .catch(error => {
                log(`获取进度失败: ${error.message}`, 'warn');
            });
    }

    // 更新进度条
    function updateProgressBar(progress) {
        const progressBar = document.getElementById('analyzeAllProgressBar');
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }
    }

    // 更新已用时间
    function updateElapsedTime() {
        if (!STATE.startTime) return;
        
        const elapsedTimeElement = document.getElementById('analyzeAllProgressTime');
        if (!elapsedTimeElement) return;
        
        const now = new Date();
        const elapsedSeconds = Math.floor((now - STATE.startTime) / 1000);
        
        // 格式化时间
        let timeText = '';
        if (elapsedSeconds < 60) {
            timeText = `${elapsedSeconds}秒`;
        } else if (elapsedSeconds < 3600) {
            const minutes = Math.floor(elapsedSeconds / 60);
            const seconds = elapsedSeconds % 60;
            timeText = `${minutes}分${seconds}秒`;
        } else {
            const hours = Math.floor(elapsedSeconds / 3600);
            const minutes = Math.floor((elapsedSeconds % 3600) / 60);
            const seconds = elapsedSeconds % 60;
            timeText = `${hours}小时${minutes}分${seconds}秒`;
        }
        
        elapsedTimeElement.textContent = `已用时间: ${timeText}`;
    }

    // 在页面加载完成后初始化
    function initialize() {
        log('初始化分析所有维度修复脚本');
        
        // 查找"分析所有维度"按钮
        const analyzeAllButtons = document.querySelectorAll('button, a, .btn').filter(el => {
            return el.textContent && (
                el.textContent.includes('分析所有维度') || 
                el.textContent.includes('开始分析所有') ||
                (el.textContent.includes('分析') && el.id && el.id.includes('analyzeAll'))
            );
        });
        
        // 如果找到按钮，绑定事件
        if (analyzeAllButtons.length > 0) {
            log(`找到 ${analyzeAllButtons.length} 个"分析所有维度"按钮`);
            
            analyzeAllButtons.forEach(button => {
                // 移除现有事件监听器
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);
                
                // 添加新的事件监听器
                newButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 如果正在分析，不重复启动
                    if (STATE.isAnalyzing) {
                        log('已有分析正在进行中，不重复启动');
                        return;
                    }
                    
                    // 开始分析
                    startAnalyzeAllDimensions();
                });
            });
        } else {
            log('未找到"分析所有维度"按钮，尝试创建');
            
            // 尝试查找可能的容器
            const containers = [
                document.querySelector('.card-footer'),
                document.querySelector('.action-buttons'),
                document.querySelector('.novel-actions'),
                document.querySelector('.chapter-actions')
            ].filter(Boolean);
            
            if (containers.length > 0) {
                log(`找到 ${containers.length} 个可能的容器`);
                
                // 在第一个容器中创建按钮
                const container = containers[0];
                const button = document.createElement('button');
                button.className = 'btn btn-primary w-100 mt-2';
                button.innerHTML = '<i class="fas fa-play-circle me-1"></i>分析所有维度';
                
                // 添加事件监听器
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 如果正在分析，不重复启动
                    if (STATE.isAnalyzing) {
                        log('已有分析正在进行中，不重复启动');
                        return;
                    }
                    
                    // 开始分析
                    startAnalyzeAllDimensions();
                });
                
                // 添加到容器
                container.appendChild(button);
                log('成功创建"分析所有维度"按钮');
            }
        }
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
})();
