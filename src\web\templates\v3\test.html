{% extends "v3/base.html" %}

{% block title %}测试功能 - 九猫小说分析写作系统v3.0{% endblock %}

{% block extra_css %}
<style>
    /* 测试功能页面样式 */
    .test-container {
        background-color: #fffbf0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .test-header {
        border-bottom: 1px solid #e0e0e0;
        margin-bottom: 20px;
        padding-bottom: 10px;
    }

    .test-section {
        margin-bottom: 30px;
    }

    .test-result {
        background-color: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        padding: 15px;
        margin-top: 15px;
        max-height: 500px;
        overflow-y: auto;
    }

    .progress-container {
        margin: 20px 0;
    }

    .analysis-progress {
        height: 20px;
        border-radius: 10px;
    }

    .test-tabs .nav-link {
        color: #495057;
        background-color: #f8f9fa;
        border-color: #dee2e6 #dee2e6 #fff;
        border-radius: 5px 5px 0 0;
        margin-right: 5px;
    }

    .test-tabs .nav-link.active {
        color: #495057;
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
    }

    .dimension-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;
    }

    .dimension-item {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 8px 12px;
        cursor: pointer;
    }

    .dimension-item.selected {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .chapter-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
    }

    .chapter-item {
        padding: 8px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
    }

    .chapter-item:hover {
        background-color: #f8f9fa;
    }

    .chapter-item.selected {
        background-color: #e9f5ff;
    }

    .writing-result {
        margin-top: 20px;
        padding: 15px;
        background-color: #f9f9f9;
        border-radius: 5px;
        border-left: 3px solid #007bff;
    }

    .log-container {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        height: 200px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 0.9rem;
    }

    .log-entry {
        margin-bottom: 5px;
        padding: 3px 0;
        border-bottom: 1px solid #eee;
    }

    .log-info {
        color: #0c5460;
    }

    .log-warn {
        color: #856404;
    }

    .log-error {
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="test-container">
        <div class="test-header">
            <h2 class="mb-0">九猫测试功能</h2>
            <p class="text-muted">上传小说、分析内容并自动生成新作品</p>
        </div>

        <!-- 上传小说部分 -->
        <div class="test-section" id="uploadSection">
            <h3>第一步：上传小说</h3>
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="title">小说标题 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="title" name="title" required>
                </div>

                <div class="form-group">
                    <label for="author">作者</label>
                    <input type="text" class="form-control" id="author" name="author" placeholder="可选">
                </div>

                <div class="form-group">
                    <label>上传方式</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="uploadType" id="uploadTypeFile" value="file" checked>
                        <label class="form-check-label" for="uploadTypeFile">
                            上传文件
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="uploadType" id="uploadTypeText" value="text">
                        <label class="form-check-label" for="uploadTypeText">
                            粘贴文本
                        </label>
                    </div>
                </div>

                <div id="fileUploadArea">
                    <div class="form-group">
                        <label for="file">选择文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="file" name="file" accept=".txt">
                        <small class="form-text text-muted">支持的文件格式：TXT，最大文件大小：10MB</small>
                    </div>
                </div>

                <div id="textUploadArea" style="display: none;">
                    <div class="form-group">
                        <label for="content">小说内容 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="content" name="content" rows="10" placeholder="在此粘贴小说内容..."></textarea>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary" id="uploadBtn">
                    <i class="fas fa-upload mr-1"></i> 上传小说
                </button>
            </form>
        </div>

        <!-- 一键分析写作部分 -->
        <div class="test-section" id="analysisSection" style="display: none;">
            <h3>第二步：一键分析写作</h3>
            <p>选择模型并点击下方按钮开始分析小说并自动生成新作品</p>

            <div class="form-group mb-3">
                <label for="modelSelect">选择模型</label>
                <select class="form-control" id="modelSelect">
                    <option value="deepseek-r1">DeepSeek R1</option>
                    <option value="qwen-plus-latest">通义千问-Plus-Latest</option>
                    <option value="qwen-qvq-max">通义千问-QVQ-Max</option>
                </select>
                <small class="form-text text-muted">不同模型可能会产生不同风格的分析和生成结果</small>
            </div>

            <div class="form-group mb-3">
                <label for="novelTypeSelect">选择小说类型</label>
                <select class="form-control" id="novelTypeSelect">
                    <option value="long">长篇小说</option>
                    <option value="short">短篇小说</option>
                </select>
                <small class="form-text text-muted">长篇小说使用传统网文写作模式，短篇小说采用"知乎体（盐选故事）"格式：开篇即高潮、情绪驱动、快节奏强冲突，约1万字标准</small>
            </div>

            <div class="form-group mb-3">
                <label for="promptTemplateSelect">选择分析提示词</label>
                <select class="form-control" id="promptTemplateSelect">
                    <option value="default">默认提示词</option>
                    <option value="simplified">精简版提示词</option>
                </select>
                <small class="form-text text-muted">默认提示词更详细全面，精简版提示词更简洁高效</small>
            </div>

            <button type="button" class="btn btn-success btn-lg" id="startAnalysisBtn">
                <i class="fas fa-magic mr-1"></i> 开始一键分析写作
            </button>

            <div class="progress-container" style="display: none;">
                <p id="analysisStatus">正在分析中...</p>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated analysis-progress" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <!-- 结果展示部分 -->
        <div class="test-section" id="resultSection" style="display: none;">
            <h3>分析与写作结果</h3>

            <ul class="nav nav-tabs test-tabs" id="resultTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="analysis-tab" data-toggle="tab" data-bs-toggle="tab" href="#analysisResult" role="tab" aria-controls="analysisResult" aria-selected="true">分析结果</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="writing-tab" data-toggle="tab" data-bs-toggle="tab" href="#writingResult" role="tab" aria-controls="writingResult" aria-selected="false">写作结果</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="log-tab" data-toggle="tab" data-bs-toggle="tab" href="#logResult" role="tab" aria-controls="logResult" aria-selected="false">处理日志</a>
                </li>
            </ul>

            <div class="tab-content" id="resultTabsContent">
                <div class="tab-pane fade show active" id="analysisResult" role="tabpanel" aria-labelledby="analysis-tab">
                    <div class="test-result" id="analysisContent">
                        分析结果将显示在这里...
                    </div>
                </div>
                <div class="tab-pane fade" id="writingResult" role="tabpanel" aria-labelledby="writing-tab">
                    <div class="test-result" id="writingContent">
                        生成的新作品将显示在这里...
                    </div>
                </div>
                <div class="tab-pane fade" id="logResult" role="tabpanel" aria-labelledby="log-tab">
                    <div class="log-container" id="logContent">
                        <!-- 日志内容将显示在这里 -->
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <button type="button" class="btn btn-outline-primary" id="downloadResultBtn">
                    <i class="fas fa-download mr-1"></i> 下载结果
                </button>
                <button type="button" class="btn btn-outline-secondary" id="resetTestBtn">
                    <i class="fas fa-redo mr-1"></i> 重新测试
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 全局错误处理 -->
<script>
// 全局错误处理
window.onerror = function(message, source, lineno, colno, error) {
    console.error('[全局错误] 捕获到错误:', message, 'at', source, lineno, colno);

    // 如果是marked相关错误，尝试加载备用实现
    if (message.includes('marked') || (source && source.includes('marked'))) {
        console.warn('[全局错误] 检测到Marked.js错误，加载备用实现');

        // 动态加载备用实现
        var script = document.createElement('script');
        script.src = "{{ url_for('static', filename='js/marked-fallback.js') }}";
        document.head.appendChild(script);
    }

    return false; // 让错误继续传播
};
</script>

<!-- 引入Marked.js用于Markdown渲染 (添加错误处理) -->
<script src="{{ url_for('static', filename='js/marked.min.js') }}" onerror="console.error('Marked.js加载失败，将使用备用实现'); var script = document.createElement('script'); script.src = '{{ url_for('static', filename='js/marked-fallback.js') }}'; document.head.appendChild(script);"></script>

<!-- 引入Marked.js兼容性包装器 -->
<script src="{{ url_for('static', filename='js/marked-wrapper.js') }}"></script>

<!-- 引入标签页修复脚本 -->
<script src="{{ url_for('static', filename='js/test-tab-fix.js') }}"></script>

<!-- 引入测试功能脚本 (放在最后加载) -->
<script src="{{ url_for('static', filename='js/v3/test.js') }}"></script>
{% endblock %}
