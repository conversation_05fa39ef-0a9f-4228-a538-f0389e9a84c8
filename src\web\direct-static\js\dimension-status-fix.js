/**
 * 维度状态修复脚本
 *
 * 这个脚本用于修复维度状态显示问题，特别是修复显示为"未知"但实际已分析完成的维度。
 */

(function() {
    // 配置
    const CONFIG = {
        // 调试模式
        debug: true,
        // 检查间隔（毫秒）
        checkInterval: 2000,
        // 最大重试次数
        maxRetries: 5,
        // 是否强制刷新状态
        forceRefresh: true
    };

    // 状态
    const STATE = {
        // 是否已初始化
        initialized: false,
        // 重试计数
        retryCount: 0,
        // 定时器ID
        timerId: null,
        // 已修复的维度
        fixedDimensions: new Set(),
        // 所有维度
        allDimensions: [
            'language_style',
            'rhythm_pacing',
            'structure',
            'sentence_variation',
            'paragraph_length',
            'perspective_shifts',
            'paragraph_flow',
            'novel_characteristics',
            'world_building',
            'chapter_outline',
            'character_relationships',
            'opening_effectiveness',
            'climax_pacing'
        ]
    };

    // 调试日志
    function debugLog(message, level = 'info') {
        if (CONFIG.debug) {
            const prefix = '[维度状态修复]';
            switch (level) {
                case 'error':
                    console.error(`${prefix} ${message}`);
                    break;
                case 'warn':
                    console.warn(`${prefix} ${message}`);
                    break;
                default:
                    console.log(`${prefix} ${message}`);
            }
        }
    }

    // 获取小说ID
    function getNovelId() {
        try {
            // 尝试从URL获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }

            // 尝试从DOM获取
            const novelContainer = document.querySelector('[data-novel-id]');
            if (novelContainer && novelContainer.dataset.novelId) {
                return novelContainer.dataset.novelId;
            }

            // 尝试从全局变量获取
            if (window.novelIdFromTemplate) {
                return window.novelIdFromTemplate;
            }

            debugLog('无法获取小说ID', 'warn');
            return null;
        } catch (e) {
            debugLog(`获取小说ID时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 检查维度状态
    function checkDimensionStatus() {
        const novelId = getNovelId();
        if (!novelId) {
            debugLog('无法获取小说ID，跳过检查', 'warn');
            return;
        }

        debugLog(`检查小说 ${novelId} 的维度状态`);

        // 获取所有维度的分析结果
        fetch(`/api/novels/${novelId}/analysis`)
            .then(response => response.json())
            .then(data => {
                if (Array.isArray(data) && data.length > 0) {
                    debugLog(`获取到 ${data.length} 个维度的分析结果`);

                    // 处理每个维度
                    data.forEach(result => {
                        if (result && result.dimension) {
                            updateDimensionStatus(result.dimension, true);
                        }
                    });

                    // 重置重试计数
                    STATE.retryCount = 0;
                } else {
                    debugLog('未获取到任何维度的分析结果', 'warn');
                    handleRetry();
                }
            })
            .catch(error => {
                debugLog(`获取分析结果时出错: ${error.message}`, 'error');
                handleRetry();
            });
    }

    // 更新维度状态
    function updateDimensionStatus(dimension, isAnalyzed) {
        try {
            // 如果已经修复过这个维度，跳过
            if (STATE.fixedDimensions.has(dimension)) {
                return;
            }

            // 查找维度行
            const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
            if (!row) {
                debugLog(`找不到维度 ${dimension} 的行`, 'warn');
                return;
            }

            // 查找状态单元格
            const statusCell = row.querySelector('.dimension-status');
            if (!statusCell) {
                debugLog(`找不到维度 ${dimension} 的状态单元格`, 'warn');
                return;
            }

            // 检查当前状态
            const currentStatus = statusCell.textContent.trim();
            const isUnknown = currentStatus.includes('未知');

            // 如果状态为"未知"且已分析完成，更新状态
            if (isUnknown && isAnalyzed) {
                debugLog(`更新维度 ${dimension} 的状态从"未知"到"已完成"`);

                // 更新状态单元格
                statusCell.innerHTML = '<span class="badge bg-success">已完成</span>';

                // 更新进度条
                const progressCell = row.querySelector('.dimension-progress');
                if (progressCell) {
                    const progressBar = progressCell.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = '100%';
                        progressBar.setAttribute('aria-valuenow', '100');
                        progressBar.textContent = '100%';
                    }
                }

                // 更新操作按钮
                const actionsCell = row.querySelector('.dimension-actions');
                if (actionsCell) {
                    const novelId = getNovelId();
                    actionsCell.innerHTML = `
                        <button class="btn btn-sm btn-outline-secondary reanalyze-btn"
                                data-dimension="${dimension}"
                                data-novel-id="${novelId}">
                            <i class="fas fa-sync"></i> 重新分析
                        </button>
                        <a href="/novel/${novelId}/analysis/${dimension}"
                           class="btn btn-sm btn-outline-primary ms-1">
                            查看详情
                        </a>
                    `;
                }

                // 标记为已修复
                STATE.fixedDimensions.add(dimension);

                // 重新绑定按钮事件
                bindButtonEvents();
            }
        } catch (e) {
            debugLog(`更新维度 ${dimension} 状态时出错: ${e.message}`, 'error');
        }
    }

    // 绑定按钮事件
    function bindButtonEvents() {
        try {
            // 绑定重新分析按钮事件
            document.querySelectorAll('.reanalyze-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const dimension = this.getAttribute('data-dimension');
                    const novelId = this.getAttribute('data-novel-id');
                    if (dimension && novelId) {
                        debugLog(`点击了维度 ${dimension} 的重新分析按钮`);
                        if (window.startSingleDimensionAnalysis) {
                            window.startSingleDimensionAnalysis(novelId, dimension);
                        }
                    }
                });
            });
        } catch (e) {
            debugLog(`绑定按钮事件时出错: ${e.message}`, 'error');
        }
    }

    // 处理重试
    function handleRetry() {
        STATE.retryCount++;
        if (STATE.retryCount <= CONFIG.maxRetries) {
            debugLog(`第 ${STATE.retryCount} 次重试，共 ${CONFIG.maxRetries} 次`);
        } else {
            debugLog(`已达到最大重试次数 ${CONFIG.maxRetries}，停止重试`, 'warn');
            clearInterval(STATE.timerId);
        }
    }

    // 强制更新所有维度状态
    function forceUpdateAllDimensions() {
        if (!CONFIG.forceRefresh) {
            return;
        }

        debugLog('强制更新所有维度状态');

        // 遍历所有维度
        STATE.allDimensions.forEach(dimension => {
            // 查找维度行
            const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
            if (!row) {
                return;
            }

            // 查找状态单元格
            const statusCell = row.querySelector('.dimension-status');
            if (!statusCell) {
                return;
            }

            // 检查当前状态
            const currentStatus = statusCell.textContent.trim();
            const isUnknown = currentStatus.includes('未知');

            // 如果状态为"未知"，检查该维度是否已分析完成
            if (isUnknown) {
                const novelId = getNovelId();
                if (novelId) {
                    fetch(`/api/analysis/result/${novelId}/${dimension}`)
                        .then(response => {
                            if (response.ok) {
                                return response.json();
                            }
                            throw new Error(`状态码: ${response.status}`);
                        })
                        .then(data => {
                            if (data && data.success) {
                                updateDimensionStatus(dimension, true);
                            }
                        })
                        .catch(error => {
                            debugLog(`检查维度 ${dimension} 状态时出错: ${error.message}`, 'warn');
                        });
                }
            }
        });
    }

    // 初始化
    function initialize() {
        if (STATE.initialized) {
            return;
        }

        debugLog('初始化维度状态修复脚本');

        // 检查是否在小说页面
        if (!window.location.pathname.includes('/novel/')) {
            debugLog('不在小说页面，跳过初始化', 'warn');
            return;
        }

        // 立即检查一次
        checkDimensionStatus();

        // 强制更新所有维度状态
        forceUpdateAllDimensions();

        // 设置定时检查
        STATE.timerId = setInterval(checkDimensionStatus, CONFIG.checkInterval);

        // 标记为已初始化
        STATE.initialized = true;
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
})();
