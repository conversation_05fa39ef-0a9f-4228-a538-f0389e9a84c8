"""
测试九猫系统的并行分析功能
"""
import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

import config
from src.models.novel import Novel
from src.api.analysis import NovelAnalyzer
from src.api.deepseek_client import DeepSeekClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("parallel_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("parallel_test")

def test_parallel_analysis():
    """测试并行分析功能"""
    logger.info("=== 开始测试并行分析功能 ===")

    # 创建一个测试小说对象
    test_content = open("test_novel.txt", "r", encoding="utf-8").read() if os.path.exists("test_novel.txt") else "这是一个测试小说，内容较短。" * 1000
    test_novel = Novel(
        title="测试小说",
        content=test_content,
        author="测试作者",
        file_path="test_novel.txt"
    )
    # 手动设置ID，因为这只是测试用途
    test_novel.id = 999

    # 创建分析器
    analyzer = NovelAnalyzer()

    # 测试维度
    test_dimensions = ["language_style", "structure", "paragraph_length"]

    # 1. 测试串行分析
    logger.info("开始串行分析测试...")
    config.PARALLEL_ANALYSIS_ENABLED = False
    start_time = time.time()
    serial_results = analyzer.analyze_novel(test_novel, test_dimensions)
    serial_time = time.time() - start_time
    logger.info(f"串行分析完成，耗时: {serial_time:.2f}秒")

    # 2. 测试并行分析
    logger.info("开始并行分析测试...")
    config.PARALLEL_ANALYSIS_ENABLED = True
    config.MAX_PARALLEL_ANALYSES = 3
    config.MAX_CHUNK_WORKERS = 2
    start_time = time.time()
    parallel_results = analyzer.analyze_novel_parallel(test_novel, test_dimensions)
    parallel_time = time.time() - start_time
    logger.info(f"并行分析完成，耗时: {parallel_time:.2f}秒")

    # 比较结果
    logger.info(f"串行分析耗时: {serial_time:.2f}秒")
    logger.info(f"并行分析耗时: {parallel_time:.2f}秒")
    logger.info(f"加速比: {serial_time/parallel_time:.2f}x")

    # 验证结果是否一致
    dimensions_match = set(serial_results.keys()) == set(parallel_results.keys())
    logger.info(f"维度匹配: {'是' if dimensions_match else '否'}")

    # 检查每个维度的结果长度
    for dimension in test_dimensions:
        if dimension in serial_results and dimension in parallel_results:
            serial_len = len(serial_results[dimension].content)
            parallel_len = len(parallel_results[dimension].content)
            logger.info(f"维度 {dimension}: 串行结果长度={serial_len}, 并行结果长度={parallel_len}")

    logger.info("=== 并行分析测试完成 ===")
    return serial_time, parallel_time

if __name__ == "__main__":
    # 创建测试小说文件（如果不存在）
    if not os.path.exists("test_novel.txt"):
        with open("test_novel.txt", "w", encoding="utf-8") as f:
            f.write("这是一个测试小说，用于测试九猫系统的并行分析功能。\n" * 1000)
        logger.info("已创建测试小说文件")

    # 运行测试
    serial_time, parallel_time = test_parallel_analysis()

    # 输出结果摘要
    print("\n测试结果摘要:")
    print(f"串行分析耗时: {serial_time:.2f}秒")
    print(f"并行分析耗时: {parallel_time:.2f}秒")
    print(f"加速比: {serial_time/parallel_time:.2f}x")
    print(f"详细日志请查看 parallel_test.log")
