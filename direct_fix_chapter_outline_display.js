/**
 * 九猫 - 章纲分析推理过程显示修复脚本
 * 专门修复章纲分析推理过程显示问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('[章纲分析推理过程修复] 脚本已加载');

    // 配置
    const CONFIG = {
        // 是否启用调试模式
        debug: true,
        // 是否启用自动修复
        enableAutoFix: true,
        // 是否启用直接API调用
        enableDirectApiCall: true,
        // 是否启用缓存
        enableCache: true,
        // 缓存过期时间（毫秒）
        cacheExpiration: 30 * 60 * 1000, // 30分钟
        // 是否启用自动重试
        enableAutoRetry: true,
        // 自动重试延迟（毫秒）
        autoRetryDelay: 5000, // 5秒
        // 最大重试次数
        maxRetries: 3
    };

    // 缓存对象
    const cache = {};

    // 加载状态
    const loadingStatus = {
        pendingRequests: {},
        loadedContainers: new Set(),
        retries: {}
    };

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[章纲分析推理过程修复] 开始修复');
        
        // 检查是否在章节分析页面
        const isChapterAnalysisPage = window.location.pathname.includes('/chapter/') && 
                                     (window.location.pathname.includes('/analysis/') || 
                                      window.location.search.includes('dimension='));
        
        if (!isChapterAnalysisPage) {
            console.log('[章纲分析推理过程修复] 不是章节分析页面，跳过修复');
            return;
        }
        
        // 检查是否是章纲分析页面
        const isChapterOutlinePage = window.location.pathname.includes('chapter_outline') || 
                                    window.location.search.includes('dimension=chapter_outline');
        
        if (!isChapterOutlinePage) {
            console.log('[章纲分析推理过程修复] 不是章纲分析页面，跳过修复');
            return;
        }
        
        console.log('[章纲分析推理过程修复] 检测到章纲分析页面，应用特殊修复');
        
        // 修复推理过程显示
        fixReasoningDisplay();
        
        // 监听DOM变化，处理动态加载的内容
        observeDOMChanges();
    });

    // 修复推理过程显示
    function fixReasoningDisplay() {
        // 查找推理过程容器
        const reasoningContainers = document.querySelectorAll(
            '#reasoningContent, ' +
            '.reasoning-container, ' +
            '[data-reasoning-container="true"], ' +
            '.reasoning-content-full'
        );
        
        console.log(`[章纲分析推理过程修复] 找到 ${reasoningContainers.length} 个推理过程容器`);
        
        if (reasoningContainers.length === 0) {
            // 如果没有找到容器，等待一段时间后再次尝试
            setTimeout(fixReasoningDisplay, 1000);
            return;
        }
        
        // 处理每个容器
        reasoningContainers.forEach(function(container) {
            // 检查容器内容
            if (container.textContent.includes('未找到推理过程数据') || 
                container.textContent.includes('暂无推理过程数据') ||
                container.innerHTML.includes('alert-warning') ||
                container.innerHTML.includes('fa-exclamation-circle')) {
                
                console.log('[章纲分析推理过程修复] 发现错误提示，尝试修复');
                
                // 获取小说ID和章节ID
                const novelId = container.dataset.novelId || 
                               document.querySelector('[data-novel-id]')?.dataset.novelId ||
                               getNovelIdFromUrl();
                
                const chapterId = container.dataset.chapterId || 
                                 document.querySelector('[data-chapter-id]')?.dataset.chapterId ||
                                 getChapterIdFromUrl();
                
                const dimension = container.dataset.dimension || 'chapter_outline';
                
                if (!novelId || !chapterId) {
                    console.error('[章纲分析推理过程修复] 无法获取小说ID或章节ID');
                    return;
                }
                
                console.log(`[章纲分析推理过程修复] 尝试加载推理过程: novel_id=${novelId}, chapter_id=${chapterId}, dimension=${dimension}`);
                
                // 显示加载中状态
                container.innerHTML = `
                    <div class="text-center my-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载推理过程，请稍候...</p>
                    </div>
                `;
                
                // 构建API URL
                const apiUrl = `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content?full=true&t=${Date.now()}`;
                
                // 发送请求
                fetch(apiUrl)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP错误: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.reasoning_content) {
                            // 显示推理过程
                            container.innerHTML = `<pre class="reasoning-content">${escapeHtml(data.reasoning_content)}</pre>`;
                            console.log('[章纲分析推理过程修复] 成功加载推理过程');
                        } else {
                            // 如果API没有返回推理过程，尝试生成推理过程
                            return generateReasoningContent(novelId, chapterId, dimension)
                                .then(generatedData => {
                                    if (generatedData.success && generatedData.reasoning_content) {
                                        container.innerHTML = `<pre class="reasoning-content">${escapeHtml(generatedData.reasoning_content)}</pre>`;
                                        console.log('[章纲分析推理过程修复] 成功生成推理过程');
                                    } else {
                                        // 如果生成失败，显示错误信息
                                        container.innerHTML = `
                                            <div class="alert alert-info">
                                                <p><i class="fas fa-info-circle"></i> 正在生成推理过程数据，请稍后刷新页面查看。</p>
                                                <button class="btn btn-sm btn-primary mt-2" onclick="location.reload()">刷新页面</button>
                                            </div>
                                        `;
                                    }
                                });
                        }
                    })
                    .catch(error => {
                        console.error('[章纲分析推理过程修复] 加载推理过程时出错:', error);
                        container.innerHTML = `
                            <div class="alert alert-info">
                                <p><i class="fas fa-info-circle"></i> 正在生成推理过程数据，请稍后刷新页面查看。</p>
                                <button class="btn btn-sm btn-primary mt-2" onclick="location.reload()">刷新页面</button>
                            </div>
                        `;
                    });
            }
        });
    }

    // 生成推理过程内容
    function generateReasoningContent(novelId, chapterId, dimension) {
        console.log('[章纲分析推理过程修复] 尝试生成推理过程');
        
        // 构建API URL
        const apiUrl = `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/generate_reasoning`;
        
        // 发送请求
        return fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('[章纲分析推理过程修复] 生成推理过程时出错:', error);
                return { success: false, error: error.message };
            });
    }

    // 从URL中获取小说ID
    function getNovelIdFromUrl() {
        const match = window.location.pathname.match(/\/novel\/(\d+)/);
        return match ? match[1] : null;
    }

    // 从URL中获取章节ID
    function getChapterIdFromUrl() {
        const match = window.location.pathname.match(/\/chapter\/(\d+)/);
        return match ? match[1] : null;
    }

    // HTML转义
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 监听DOM变化，处理动态加载的内容
    function observeDOMChanges() {
        // 创建MutationObserver实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // 检查是否有新的推理过程容器被添加
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // 元素节点
                            if (node.id === 'reasoningContent' || 
                                node.classList.contains('reasoning-container') ||
                                node.dataset.reasoningContainer === 'true' ||
                                node.classList.contains('reasoning-content-full')) {
                                
                                console.log('[章纲分析推理过程修复] 检测到新的推理过程容器，应用修复');
                                fixReasoningDisplay();
                            }
                        }
                    }
                }
            });
        });
        
        // 配置观察选项
        const config = { childList: true, subtree: true };
        
        // 开始观察文档
        observer.observe(document.body, config);
    }
})();
