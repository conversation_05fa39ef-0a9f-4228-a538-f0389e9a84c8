/**
 * 九猫 - 图表完全禁用脚本
 * 彻底禁用所有图表功能，节省系统资源
 * 版本: 2.0.0
 */

(function() {
    'use strict';

    console.log('图表禁用脚本已加载');

    // 检查是否设置了禁用图表环境变量
    const disableCharts = true; // 默认禁用

    if (!disableCharts) {
        console.log('图表未被禁用，脚本不执行任何操作');
        return;
    }

    console.log('图表已被禁用，开始执行禁用操作');

    // 创建一个空的Chart对象，防止报错
    window.Chart = function(ctx, config) {
        console.log('尝试创建图表，但图表功能已被禁用');
        this.ctx = ctx;
        this.config = config || {};

        // 实现必要的方法，防止调用时报错
        this.update = function() { console.log('Chart.update() 被调用，但图表已禁用'); };
        this.destroy = function() { console.log('Chart.destroy() 被调用，但图表已禁用'); };
        this.render = function() { console.log('Chart.render() 被调用，但图表已禁用'); };

        return this;
    };

    // 添加必要的静态方法
    Chart.register = function() { console.log('Chart.register() 被调用，但图表已禁用'); };
    Chart.defaults = {};

    // 替换图表管理器
    window.ChartManager = {
        init: function() { console.log('图表管理器初始化，但图表已禁用'); },
        createChart: function() { console.log('尝试创建图表，但图表已禁用'); return null; },
        updateChart: function() { console.log('尝试更新图表，但图表已禁用'); return null; },
        destroyChart: function() { console.log('尝试销毁图表，但图表已禁用'); },
        destroyAllCharts: function() { console.log('尝试销毁所有图表，但图表已禁用'); },
        ensureChartJsLoaded: function(callback) {
            console.log('尝试加载Chart.js，但图表已禁用');
            if (callback && typeof callback === 'function') {
                callback();
            }
        }
    };

    // 替换图表加载器
    window.chartLoader = {
        ensureLoaded: function(callback) {
            console.log('尝试加载Chart.js，但图表已禁用');
            if (callback && typeof callback === 'function') {
                callback();
            }
        }
    };

    // 替换初始化图表函数
    window.initializeCharts = function() {
        console.log('尝试初始化图表，但图表已禁用');

        // 替换所有图表容器为文本说明
        replaceChartContainers();
    };

    // 替换图表容器为文本说明
    function replaceChartContainers() {
        // 查找所有图表容器
        const chartContainers = document.querySelectorAll('.chart-container, .analysis-visualization');
        console.log(`找到 ${chartContainers.length} 个图表容器`);

        chartContainers.forEach(container => {
            // 创建替代内容
            const replacementDiv = document.createElement('div');
            replacementDiv.className = 'chart-disabled-notice';
            replacementDiv.style.padding = '10px';
            replacementDiv.style.backgroundColor = '#f8f9fa';
            replacementDiv.style.border = '1px solid #dee2e6';
            replacementDiv.style.borderRadius = '4px';
            replacementDiv.style.marginBottom = '15px';
            replacementDiv.style.textAlign = 'center';

            // 设置替代内容
            replacementDiv.innerHTML = '<p style="margin: 0; color: #6c757d;">图表功能已禁用以提高系统性能</p>';

            // 替换容器内容
            container.innerHTML = '';
            container.appendChild(replacementDiv);
        });

        // 查找所有canvas元素
        const canvasElements = document.querySelectorAll('canvas.analysis-chart, canvas[data-chart-type]');
        console.log(`找到 ${canvasElements.length} 个图表Canvas元素`);

        canvasElements.forEach(canvas => {
            const parent = canvas.parentNode;

            // 创建替代内容
            const replacementDiv = document.createElement('div');
            replacementDiv.className = 'chart-disabled-notice';
            replacementDiv.style.padding = '10px';
            replacementDiv.style.backgroundColor = '#f8f9fa';
            replacementDiv.style.border = '1px solid #dee2e6';
            replacementDiv.style.borderRadius = '4px';
            replacementDiv.style.marginBottom = '15px';
            replacementDiv.style.textAlign = 'center';

            // 设置替代内容
            replacementDiv.innerHTML = '<p style="margin: 0; color: #6c757d;">图表功能已禁用以提高系统性能</p>';

            // 替换Canvas元素
            parent.replaceChild(replacementDiv, canvas);
        });
    }

    // 在DOM加载完成后执行替换操作
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM加载完成，执行图表容器替换');
        replaceChartContainers();

        // 阻止加载Chart.js
        preventChartJsLoading();
    });

    // 阻止加载Chart.js
    function preventChartJsLoading() {
        // 重写document.createElement，拦截script标签创建
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(document, tagName);

            if (tagName.toLowerCase() === 'script') {
                // 监听src属性设置
                const originalSetAttribute = element.setAttribute;
                element.setAttribute = function(name, value) {
                    if (name === 'src' && value && (
                        value.includes('chart.js') ||
                        value.includes('chart.min.js') ||
                        value.includes('chart.bundle.js') ||
                        value.includes('chart.bundle.min.js') ||
                        value.includes('cdn.jsdelivr.net/npm/chart')
                    )) {
                        console.log(`阻止加载Chart.js: ${value}`);
                        // 不设置src，阻止加载
                        return element;
                    }

                    return originalSetAttribute.call(this, name, value);
                };

                // 监听src属性直接赋值 - 使用更安全的方法
                try {
                    // 使用更安全的方法监听src属性
                    const originalSrc = element.src;
                    let blockedSrc = null;

                    // 使用原始的setAttribute方法来设置一个自定义属性
                    const originalSetAttribute = element.setAttribute;
                    originalSetAttribute.call(element, 'data-original-src-handler', 'installed');

                    // 添加一个事件监听器来检测src变化
                    element.addEventListener('beforescriptexecute', function(e) {
                        const src = element.getAttribute('src') || '';
                        if (src && (
                            src.includes('chart.js') ||
                            src.includes('chart.min.js') ||
                            src.includes('chart.bundle.js') ||
                            src.includes('chart.bundle.min.js') ||
                            src.includes('cdn.jsdelivr.net/npm/chart')
                        )) {
                            console.log(`阻止执行Chart.js脚本: ${src}`);
                            e.preventDefault();
                            e.stopPropagation();
                        }
                    }, true);

                    // 尝试使用更安全的方法拦截src设置
                    // 注意：这种方法可能在某些浏览器中不起作用，但不会导致错误
                    try {
                        let srcDescriptor = Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype, 'src');
                        // 只有在属性可配置的情况下才尝试重新定义
                        if (srcDescriptor && srcDescriptor.configurable) {
                            Object.defineProperty(element, 'src', {
                                configurable: true,  // 保持可配置
                                enumerable: true,    // 保持可枚举
                                set: function(value) {
                                    if (value && (
                                        value.includes('chart.js') ||
                                        value.includes('chart.min.js') ||
                                        value.includes('chart.bundle.js') ||
                                        value.includes('chart.bundle.min.js') ||
                                        value.includes('cdn.jsdelivr.net/npm/chart')
                                    )) {
                                        console.log(`阻止加载Chart.js: ${value}`);
                                        blockedSrc = value;  // 存储被阻止的URL
                                        return;
                                    }

                                    // 调用原始setter
                                    srcDescriptor.set.call(this, value);
                                },
                                get: function() {
                                    // 如果有被阻止的URL，返回空字符串
                                    if (blockedSrc) return '';
                                    // 否则返回实际值
                                    return srcDescriptor.get.call(this);
                                }
                            });
                        }
                    } catch (propError) {
                        console.log('无法重定义src属性，使用备用方法:', propError.message);
                        // 不抛出错误，继续使用其他方法
                    }
                } catch (e) {
                    console.warn('设置src监听器时出错:', e.message);
                    // 错误不会中断脚本执行
                }
            }

            return element;
        };
    }

    // 立即执行一次替换操作，以防脚本在DOM加载完成后加载
    setTimeout(replaceChartContainers, 100);

    // 定期检查并替换新添加的图表容器
    setInterval(replaceChartContainers, 2000);

    console.log('图表禁用脚本初始化完成');
})();
