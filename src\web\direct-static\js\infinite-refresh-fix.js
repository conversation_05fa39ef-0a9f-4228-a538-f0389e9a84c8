/**
 * 九猫 - 无限刷新修复脚本
 * 专门处理页面不停刷新/重新渲染的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._infiniteRefreshFixLoaded) {
        console.log('无限刷新修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._infiniteRefreshFixLoaded = true;
    
    console.log('无限刷新修复脚本已加载 - 版本1.0.0');
    
    // 记录DOM更新次数和时间
    const domUpdateStats = {
        updateCount: 0,
        lastUpdateTime: Date.now(),
        updateTimes: [],
        maxUpdatesPerSecond: 10, // 每秒最大更新次数阈值
        isMonitoring: false,
        isThrottling: false,
        originalMethods: {}
    };
    
    // 开始监控DOM更新
    function startMonitoring() {
        if (domUpdateStats.isMonitoring) {
            return;
        }
        
        console.log('开始监控DOM更新频率');
        domUpdateStats.isMonitoring = true;
        
        // 保存原始方法
        saveOriginalMethods();
        
        // 监控appendChild
        overrideMethod(Node.prototype, 'appendChild', function(originalMethod) {
            return function(node) {
                trackDomUpdate('appendChild');
                return originalMethod.apply(this, arguments);
            };
        });
        
        // 监控insertBefore
        overrideMethod(Node.prototype, 'insertBefore', function(originalMethod) {
            return function(node, referenceNode) {
                trackDomUpdate('insertBefore');
                return originalMethod.apply(this, arguments);
            };
        });
        
        // 监控removeChild
        overrideMethod(Node.prototype, 'removeChild', function(originalMethod) {
            return function(node) {
                trackDomUpdate('removeChild');
                return originalMethod.apply(this, arguments);
            };
        });
        
        // 监控replaceChild
        overrideMethod(Node.prototype, 'replaceChild', function(originalMethod) {
            return function(newChild, oldChild) {
                trackDomUpdate('replaceChild');
                return originalMethod.apply(this, arguments);
            };
        });
        
        // 监控innerHTML
        monitorProperty(Element.prototype, 'innerHTML');
        
        // 监控textContent
        monitorProperty(Node.prototype, 'textContent');
        
        // 监控innerText
        monitorProperty(HTMLElement.prototype, 'innerText');
        
        // 定期检查DOM更新频率
        setInterval(checkUpdateFrequency, 1000);
    }
    
    // 保存原始方法
    function saveOriginalMethods() {
        domUpdateStats.originalMethods = {
            appendChild: Node.prototype.appendChild,
            insertBefore: Node.prototype.insertBefore,
            removeChild: Node.prototype.removeChild,
            replaceChild: Node.prototype.replaceChild
        };
    }
    
    // 重写方法
    function overrideMethod(prototype, methodName, wrapper) {
        const originalMethod = prototype[methodName];
        prototype[methodName] = wrapper(originalMethod);
    }
    
    // 监控属性
    function monitorProperty(prototype, propertyName) {
        const descriptor = Object.getOwnPropertyDescriptor(prototype, propertyName);
        if (descriptor && descriptor.set) {
            const originalSetter = descriptor.set;
            
            Object.defineProperty(prototype, propertyName, {
                ...descriptor,
                set: function(value) {
                    trackDomUpdate(`set ${propertyName}`);
                    return originalSetter.call(this, value);
                }
            });
        }
    }
    
    // 记录DOM更新
    function trackDomUpdate(methodName) {
        const now = Date.now();
        domUpdateStats.updateCount++;
        domUpdateStats.lastUpdateTime = now;
        domUpdateStats.updateTimes.push(now);
        
        // 只保留最近2秒的更新记录
        const twoSecondsAgo = now - 2000;
        domUpdateStats.updateTimes = domUpdateStats.updateTimes.filter(time => time > twoSecondsAgo);
    }
    
    // 检查更新频率
    function checkUpdateFrequency() {
        const now = Date.now();
        const oneSecondAgo = now - 1000;
        
        // 计算过去1秒内的更新次数
        const recentUpdates = domUpdateStats.updateTimes.filter(time => time > oneSecondAgo).length;
        
        // 如果更新频率过高，可能存在无限循环
        if (recentUpdates > domUpdateStats.maxUpdatesPerSecond) {
            console.warn(`检测到异常高频DOM更新: ${recentUpdates}次/秒，可能存在无限循环渲染`);
            
            if (!domUpdateStats.isThrottling) {
                applyThrottling();
            }
        } else if (domUpdateStats.isThrottling && recentUpdates < domUpdateStats.maxUpdatesPerSecond / 2) {
            // 如果更新频率已经降低，恢复正常
            console.log('DOM更新频率已恢复正常，解除限制');
            removeThrottling();
        }
    }
    
    // 应用节流
    function applyThrottling() {
        console.log('应用DOM操作节流，防止无限循环');
        domUpdateStats.isThrottling = true;
        
        // 找到并禁用可能导致无限循环的脚本
        findAndDisableProblematicScripts();
        
        // 禁用可能导致问题的MutationObserver
        disableMutationObservers();
        
        // 限制DOM操作频率
        throttleDomOperations();
    }
    
    // 移除节流
    function removeThrottling() {
        console.log('移除DOM操作节流');
        domUpdateStats.isThrottling = false;
        
        // 恢复原始DOM方法
        restoreOriginalMethods();
    }
    
    // 恢复原始方法
    function restoreOriginalMethods() {
        for (const [methodName, originalMethod] of Object.entries(domUpdateStats.originalMethods)) {
            if (methodName === 'appendChild') Node.prototype.appendChild = originalMethod;
            if (methodName === 'insertBefore') Node.prototype.insertBefore = originalMethod;
            if (methodName === 'removeChild') Node.prototype.removeChild = originalMethod;
            if (methodName === 'replaceChild') Node.prototype.replaceChild = originalMethod;
        }
    }
    
    // 查找并禁用可能导致问题的脚本
    function findAndDisableProblematicScripts() {
        // 检查是否存在自动刷新的脚本
        const scripts = document.querySelectorAll('script');
        for (const script of scripts) {
            const content = script.textContent || '';
            
            // 检查是否包含可能导致无限循环的代码
            if (content.includes('location.reload') || 
                content.includes('window.location') || 
                content.includes('setInterval') || 
                content.includes('setTimeout')) {
                
                console.log('发现可能导致无限循环的脚本:', 
                    content.substring(0, 100) + (content.length > 100 ? '...' : ''));
                
                // 尝试禁用脚本
                try {
                    script.setAttribute('data-disabled-by-fix', 'true');
                    script.type = 'text/disabled';
                } catch (e) {
                    console.error('禁用脚本失败:', e);
                }
            }
        }
    }
    
    // 禁用可能导致问题的MutationObserver
    function disableMutationObservers() {
        // 保存原始的MutationObserver构造函数
        if (!window._originalMutationObserver) {
            window._originalMutationObserver = window.MutationObserver;
        }
        
        // 重写MutationObserver构造函数
        window.MutationObserver = function(callback) {
            const wrappedCallback = function(mutations, observer) {
                // 如果正在节流，限制回调频率
                if (domUpdateStats.isThrottling) {
                    console.log('限制MutationObserver回调频率');
                    setTimeout(() => callback(mutations, observer), 1000);
                } else {
                    return callback(mutations, observer);
                }
            };
            
            return new window._originalMutationObserver(wrappedCallback);
        };
    }
    
    // 限制DOM操作频率
    function throttleDomOperations() {
        // 重写appendChild方法，添加延迟
        Node.prototype.appendChild = function(node) {
            if (domUpdateStats.isThrottling) {
                return new Promise(resolve => {
                    setTimeout(() => {
                        const result = domUpdateStats.originalMethods.appendChild.call(this, node);
                        resolve(result);
                    }, 100);
                });
            } else {
                return domUpdateStats.originalMethods.appendChild.call(this, node);
            }
        };
        
        // 类似地重写其他DOM方法
        // insertBefore, removeChild, replaceChild 等
    }
    
    // 检查页面是否在分析详情页面
    function isAnalysisDetailPage() {
        // 检查URL是否包含分析详情页面的特征
        const url = window.location.href;
        return url.includes('/analysis/') || 
               url.includes('/novel/') && url.includes('/dimension/') ||
               url.includes('chapter') && url.includes('analysis');
    }
    
    // 在页面加载完成后开始监控
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            if (isAnalysisDetailPage()) {
                startMonitoring();
            }
        });
    } else {
        if (isAnalysisDetailPage()) {
            startMonitoring();
        }
    }
    
    console.log('无限刷新修复脚本初始化完成');
})(); 