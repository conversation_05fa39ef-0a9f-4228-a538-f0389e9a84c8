#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API内容验证修复效果
验证21字符问题的解决方案
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.api.deepseek_client import DeepSeekClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_error_content_detection():
    """测试错误内容检测"""
    print("=== 测试错误内容检测 ===")
    
    client = DeepSeekClient()
    
    test_cases = [
        {
            "name": "21字符错误信息",
            "content": "API返回的数据格式不正确",
            "expected": True,
            "description": "典型的21字符错误信息"
        },
        {
            "name": "短错误信息",
            "content": "生成内容失败",
            "expected": True,
            "description": "短的错误信息"
        },
        {
            "name": "英文错误信息",
            "content": "Error: Request failed",
            "expected": True,
            "description": "英文错误信息"
        },
        {
            "name": "有效的短内容",
            "content": "第1章 新的开始\n\n林小雨站在窗前，看着外面的雨滴。",
            "expected": False,
            "description": "有效但较短的写作内容"
        },
        {
            "name": "正常写作内容",
            "content": """第1章 新的开始

林小雨站在窗前，看着外面的雨滴打在玻璃上。今天是她来到这个城市的第一天，心情既紧张又兴奋。

"你好，我是林小雨。"她对着镜子练习着自我介绍。

手机响了，是妈妈打来的。

"小雨，到了吗？"妈妈问道。

"刚到，还在适应中。"林小雨回答。""",
            "expected": False,
            "description": "正常的写作内容"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试：{case['name']}")
        print(f"   描述：{case['description']}")
        print(f"   内容长度：{len(case['content'])}字符")
        print(f"   预期结果：{'错误内容' if case['expected'] else '正常内容'}")
        
        # 执行检测
        result = client._is_error_content(case['content'])
        
        print(f"   实际结果：{'错误内容' if result else '正常内容'}")
        print(f"   检测状态：{'✅ 正确' if result == case['expected'] else '❌ 错误'}")

def test_valid_writing_content_detection():
    """测试有效写作内容检测"""
    print("\n=== 测试有效写作内容检测 ===")
    
    client = DeepSeekClient()
    
    test_cases = [
        {
            "name": "基础框架内容",
            "content": "第1章 开始\n\n主角出现，开始新的冒险。",
            "analysis_type": "chapter_framework",
            "expected": True,
            "description": "简短但有效的基础框架"
        },
        {
            "name": "完整章节内容",
            "content": """第1章 新的开始

林小雨站在窗前，看着外面的雨滴。她想着今天的计划。

"我需要去买些东西。"她说道。

突然，她听到了敲门声。""",
            "analysis_type": "chapter_content_generation",
            "expected": True,
            "description": "完整的章节内容"
        },
        {
            "name": "错误信息",
            "content": "API返回的数据格式不正确",
            "analysis_type": "chapter_content_generation",
            "expected": False,
            "description": "错误信息，不是写作内容"
        },
        {
            "name": "过短无效内容",
            "content": "测试",
            "analysis_type": "chapter_content_generation",
            "expected": False,
            "description": "过短的无效内容"
        },
        {
            "name": "无标点的文本",
            "content": "这是一段没有标点符号的文本内容",
            "analysis_type": "chapter_content_generation",
            "expected": False,
            "description": "缺少写作特征的文本"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试：{case['name']}")
        print(f"   描述：{case['description']}")
        print(f"   分析类型：{case['analysis_type']}")
        print(f"   内容长度：{len(case['content'])}字符")
        print(f"   预期结果：{'有效内容' if case['expected'] else '无效内容'}")
        
        # 执行检测
        result = client._is_valid_writing_content(case['content'], case['analysis_type'])
        
        print(f"   实际结果：{'有效内容' if result else '无效内容'}")
        print(f"   检测状态：{'✅ 正确' if result == case['expected'] else '❌ 错误'}")

def test_content_length_validation_improvement():
    """测试内容长度验证的改进"""
    print("\n=== 测试内容长度验证改进 ===")
    
    # 模拟API响应处理
    test_cases = [
        {
            "name": "21字符错误信息",
            "content": "API返回的数据格式不正确",
            "analysis_type": "chapter_content_generation",
            "should_be_rejected": True,
            "reason": "错误内容检测"
        },
        {
            "name": "短但有效的基础框架",
            "content": "第1章\n\n林小雨开始了新的生活。她走在街上，思考着未来。",
            "analysis_type": "chapter_framework",
            "should_be_rejected": False,
            "reason": "有效的基础框架内容"
        },
        {
            "name": "短但有效的章节内容",
            "content": """第1章 新开始

林小雨站在窗前。"今天是新的一天。"她说道。

她走出房间，准备迎接挑战。""",
            "analysis_type": "chapter_content_generation",
            "should_be_rejected": False,
            "reason": "包含多个写作特征的有效内容"
        },
        {
            "name": "过短且无效的内容",
            "content": "测试内容",
            "analysis_type": "chapter_content_generation",
            "should_be_rejected": True,
            "reason": "过短且缺少写作特征"
        }
    ]
    
    client = DeepSeekClient()
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试：{case['name']}")
        print(f"   内容：{case['content'][:50]}...")
        print(f"   分析类型：{case['analysis_type']}")
        print(f"   内容长度：{len(case['content'])}字符")
        print(f"   预期：{'应被拒绝' if case['should_be_rejected'] else '应被接受'}")
        print(f"   原因：{case['reason']}")
        
        # 检查错误内容
        is_error = client._is_error_content(case['content'])
        print(f"   错误检测：{'是错误内容' if is_error else '不是错误内容'}")
        
        # 检查有效写作内容
        is_valid = client._is_valid_writing_content(case['content'], case['analysis_type'])
        print(f"   有效性检测：{'有效写作内容' if is_valid else '无效写作内容'}")
        
        # 综合判断
        should_reject = is_error or (len(case['content']) < 100 and not is_valid)
        actual_result = "被拒绝" if should_reject else "被接受"
        expected_result = "被拒绝" if case['should_be_rejected'] else "被接受"
        
        print(f"   实际结果：{actual_result}")
        print(f"   验证状态：{'✅ 正确' if actual_result == expected_result else '❌ 错误'}")

def main():
    """主函数"""
    print("九猫系统 - API内容验证修复效果测试")
    print("=" * 70)
    
    try:
        # 测试错误内容检测
        test_error_content_detection()
        
        # 测试有效写作内容检测
        test_valid_writing_content_detection()
        
        # 测试内容长度验证改进
        test_content_length_validation_improvement()
        
        print("\n" + "=" * 70)
        print("✅ API内容验证修复测试完成！")
        print("\n📝 修复要点总结：")
        print("1. 新增错误内容检测：识别21字符等错误信息")
        print("2. 新增有效写作内容检测：基于写作特征判断内容有效性")
        print("3. 大幅放宽长度要求：从500字符降至50-200字符")
        print("4. 智能质量检测：不仅看长度，更看内容质量")
        print("5. 分类处理：基础框架和完整内容采用不同标准")
        print("6. 错误优先：优先检测和拒绝明显的错误内容")
        
        print("\n🎯 解决的核心问题：")
        print("- 21字符错误信息被正确识别和拒绝")
        print("- 短但有效的写作内容不再被误判")
        print("- 基础框架内容得到更宽松的处理")
        print("- API返回的真实写作内容能够正确通过验证")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    main()
