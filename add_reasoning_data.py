# 为已有的分析结果添加推理过程数据
import os
import sys
import json
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# 导入模型
from src.models.analysis_result import AnalysisResult
from src.db.connection import Session, engine

def add_reasoning_data(novel_id, dimension, reasoning_content):
    """为指定的分析结果添加推理过程数据"""
    session = Session()
    try:
        # 查找分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id,
            dimension=dimension
        ).first()
        
        if not result:
            print(f"错误: 未找到小说ID {novel_id} 的 {dimension} 维度分析结果")
            return False
        
        # 更新或添加推理过程数据
        if not result.analysis_metadata:
            result.analysis_metadata = {}
        
        # 确保是字典类型
        if isinstance(result.analysis_metadata, dict):
            result.analysis_metadata['reasoning_content'] = reasoning_content
            session.commit()
            print(f"成功为小说ID {novel_id} 的 {dimension} 维度添加推理过程数据")
            return True
        else:
            print(f"错误: 分析元数据不是字典类型: {type(result.analysis_metadata)}")
            return False
            
    except Exception as e:
        print(f"添加推理过程数据时出错: {str(e)}")
        session.rollback()
        return False
    finally:
        session.close()

def extract_reasoning_from_logs(novel_id, dimension):
    """从分析日志中提取推理过程"""
    session = Session()
    try:
        # 查找分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id,
            dimension=dimension
        ).first()
        
        if not result:
            print(f"错误: 未找到小说ID {novel_id} 的 {dimension} 维度分析结果")
            return False
        
        # 从日志中提取推理过程
        if result.analysis_logs and isinstance(result.analysis_logs, list):
            for log in result.analysis_logs:
                if isinstance(log, dict) and 'message' in log:
                    if 'reasoning_content' in log['message']:
                        # 尝试从JSON字符串中提取推理内容
                        try:
                            # 可能包含JSON
                            import re
                            json_pattern = r'reasoning_content\':\s*\'([^\']+)'
                            match = re.search(json_pattern, log['message'])
                            
                            if match:
                                reasoning_content = match.group(1)
                                print(f"从日志中提取到推理过程: {reasoning_content[:50]}...")
                                
                                # 更新分析元数据
                                if not result.analysis_metadata:
                                    result.analysis_metadata = {}
                                    
                                if isinstance(result.analysis_metadata, dict):
                                    result.analysis_metadata['reasoning_content'] = reasoning_content
                                    session.commit()
                                    print(f"成功为小说ID {novel_id} 的 {dimension} 维度添加推理过程数据")
                                    return True
                                else:
                                    print(f"错误: 分析元数据不是字典类型: {type(result.analysis_metadata)}")
                        except Exception as ex:
                            print(f"从日志提取推理过程时出错: {str(ex)}")
            
            print(f"未在日志中找到推理过程数据")
            return False
        else:
            print(f"分析结果没有日志数据或日志格式不正确")
            return False
    except Exception as e:
        print(f"提取推理过程时出错: {str(e)}")
        return False
    finally:
        session.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="为分析结果添加推理过程数据")
    parser.add_argument("--novel-id", type=int, help="小说ID", required=True)
    parser.add_argument("--dimension", type=str, help="分析维度", required=True)
    parser.add_argument("--extract", action="store_true", help="从日志中提取推理过程")
    parser.add_argument("--content", type=str, help="推理过程内容")
    parser.add_argument("--file", type=str, help="包含推理过程内容的文件")
    
    args = parser.parse_args()
    
    if args.extract:
        # 从日志中提取推理过程
        extract_reasoning_from_logs(args.novel_id, args.dimension)
    elif args.content:
        # 使用命令行参数中的内容
        add_reasoning_data(args.novel_id, args.dimension, args.content)
    elif args.file:
        # 从文件中读取内容
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                content = f.read()
            add_reasoning_data(args.novel_id, args.dimension, content)
        except Exception as e:
            print(f"读取文件时出错: {str(e)}")
    else:
        print("错误: 必须提供推理过程内容或指定从日志中提取")
        parser.print_help() 