{"name": "semver", "version": "7.3.7", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "prepublishOnly": "git push origin --follow-tags", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.3.2", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "files": ["bin/", "classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": "GitHub Inc.", "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.3.2", "engines": ">=10", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x"], "distPaths": ["bin/", "classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"]}}