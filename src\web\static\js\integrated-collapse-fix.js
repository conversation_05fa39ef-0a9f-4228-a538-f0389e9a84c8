/**
 * 九猫 - 折叠功能综合修复脚本
 * 整合所有与折叠/展开功能相关的修复
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('折叠功能综合修复脚本已加载');

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始执行折叠功能综合修复');
        
        // 修复Bootstrap兼容性问题
        fixBootstrapCompatibility();
        
        // 修复折叠按钮
        fixCollapseButtons();
        
        // 修复分析过程折叠区域
        fixAnalysisProcessCollapse();
        
        // 修复分析日志折叠区域
        fixAnalysisLogsCollapse();
        
        // 添加全局折叠/展开功能
        addGlobalCollapseFunction();
    });
    
    // 修复Bootstrap兼容性问题
    function fixBootstrapCompatibility() {
        try {
            // 检测Bootstrap版本
            const isBootstrap5 = typeof bootstrap !== 'undefined';
            const isBootstrap4 = typeof $ !== 'undefined' && typeof $.fn.collapse !== 'undefined';
            
            console.log('Bootstrap版本检测: Bootstrap 5 - ' + isBootstrap5 + ', Bootstrap 4 - ' + isBootstrap4);
            
            if (!isBootstrap5 && !isBootstrap4) {
                console.log('未检测到Bootstrap，尝试加载');
                
                // 加载Bootstrap
                const bootstrapScript = document.createElement('script');
                bootstrapScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js';
                document.head.appendChild(bootstrapScript);
                
                // 等待Bootstrap加载完成
                bootstrapScript.onload = function() {
                    console.log('Bootstrap加载完成，重新执行修复');
                    
                    // 重新执行修复
                    fixCollapseButtons();
                    fixAnalysisProcessCollapse();
                    fixAnalysisLogsCollapse();
                };
            }
            
            // 修复数据属性
            if (isBootstrap5) {
                // 从Bootstrap 4升级到Bootstrap 5
                document.querySelectorAll('[data-toggle="collapse"]').forEach(function(element) {
                    if (!element.hasAttribute('data-bs-toggle')) {
                        element.setAttribute('data-bs-toggle', 'collapse');
                        console.log('将data-toggle="collapse"转换为data-bs-toggle="collapse"');
                        
                        // 更新目标属性
                        if (element.hasAttribute('data-target')) {
                            const target = element.getAttribute('data-target');
                            element.setAttribute('data-bs-target', target);
                        }
                    }
                });
            }
        } catch (e) {
            console.error('修复Bootstrap兼容性问题时出错:', e);
        }
    }
    
    // 修复折叠按钮
    function fixCollapseButtons() {
        try {
            // 查找所有折叠按钮
            const collapseButtons = document.querySelectorAll('[data-toggle="collapse"], [data-bs-toggle="collapse"]');
            console.log('找到 ' + collapseButtons.length + ' 个折叠按钮');
            
            // 修复每个折叠按钮
            collapseButtons.forEach(function(button) {
                // 确保按钮有正确的属性
                if (button.hasAttribute('data-toggle') && !button.hasAttribute('data-bs-toggle')) {
                    // 从Bootstrap 4升级到Bootstrap 5
                    const target = button.getAttribute('data-toggle');
                    if (target === 'collapse') {
                        console.log('将data-toggle="collapse"转换为data-bs-toggle="collapse"');
                        button.setAttribute('data-bs-toggle', 'collapse');
                        
                        // 更新目标属性
                        if (button.hasAttribute('data-target')) {
                            const targetSelector = button.getAttribute('data-target');
                            button.setAttribute('data-bs-target', targetSelector);
                        }
                    }
                }
                
                // 确保按钮有点击事件处理程序
                if (!button._hasCollapseHandler) {
                    button.addEventListener('click', function(event) {
                        event.preventDefault();
                        
                        // 获取目标元素
                        const targetSelector = button.getAttribute('data-bs-target') || button.getAttribute('data-target');
                        if (!targetSelector) {
                            console.warn('折叠按钮没有目标选择器');
                            return;
                        }
                        
                        const targetElement = document.querySelector(targetSelector);
                        if (!targetElement) {
                            console.warn('找不到折叠目标元素: ' + targetSelector);
                            return;
                        }
                        
                        console.log('切换折叠状态: ' + targetSelector);
                        
                        // 手动切换折叠状态
                        targetElement.classList.toggle('show');
                        
                        // 更新aria-expanded属性
                        const isExpanded = targetElement.classList.contains('show');
                        button.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
                    });
                    
                    button._hasCollapseHandler = true;
                    console.log('为折叠按钮添加了点击事件处理程序');
                }
            });
        } catch (e) {
            console.error('修复折叠按钮时出错:', e);
        }
    }
    
    // 修复分析过程折叠区域
    function fixAnalysisProcessCollapse() {
        try {
            // 查找分析过程折叠区域
            const analysisProcessCollapse = document.getElementById('analysisProcessCollapse');
            if (!analysisProcessCollapse) {
                console.log('未找到分析过程折叠区域，跳过修复');
                return;
            }
            
            console.log('找到分析过程折叠区域，尝试修复');
            
            // 确保折叠区域有正确的类
            if (!analysisProcessCollapse.classList.contains('collapse')) {
                analysisProcessCollapse.classList.add('collapse');
                console.log('添加了collapse类');
            }
            
            // 添加手动切换按钮
            addManualToggleButton(analysisProcessCollapse, '切换分析过程');
        } catch (e) {
            console.error('修复分析过程折叠区域时出错:', e);
        }
    }
    
    // 修复分析日志折叠区域
    function fixAnalysisLogsCollapse() {
        try {
            // 查找分析日志折叠区域
            const analysisLogsCollapse = document.getElementById('analysisLogsCollapse');
            if (!analysisLogsCollapse) {
                console.log('未找到分析日志折叠区域，跳过修复');
                return;
            }
            
            console.log('找到分析日志折叠区域，尝试修复');
            
            // 确保折叠区域有正确的类
            if (!analysisLogsCollapse.classList.contains('collapse')) {
                analysisLogsCollapse.classList.add('collapse');
                console.log('添加了collapse类');
            }
            
            // 添加手动切换按钮
            addManualToggleButton(analysisLogsCollapse, '切换分析日志');
        } catch (e) {
            console.error('修复分析日志折叠区域时出错:', e);
        }
    }
    
    // 添加手动切换按钮
    function addManualToggleButton(collapseElement, buttonText) {
        try {
            // 检查是否已经有手动切换按钮
            if (collapseElement.previousElementSibling && 
                collapseElement.previousElementSibling.querySelector('.manual-toggle-btn')) {
                console.log('已存在手动切换按钮，跳过创建');
                return;
            }
            
            // 创建手动切换按钮
            const toggleButton = document.createElement('button');
            toggleButton.className = 'btn btn-sm btn-outline-secondary manual-toggle-btn ms-2';
            toggleButton.textContent = buttonText || '手动切换';
            toggleButton.onclick = function() {
                // 手动切换折叠状态
                collapseElement.classList.toggle('show');
                console.log('手动切换折叠状态: ' + (collapseElement.classList.contains('show') ? '展开' : '折叠'));
            };
            
            // 添加到页面
            const cardHeader = collapseElement.previousElementSibling;
            if (cardHeader && cardHeader.classList.contains('card-header')) {
                // 添加到卡片标题
                const titleElement = cardHeader.querySelector('h5') || cardHeader;
                titleElement.appendChild(toggleButton);
                console.log('添加了手动切换按钮到卡片标题');
            } else {
                // 创建一个新的容器
                const container = document.createElement('div');
                container.className = 'text-end mt-2 mb-2';
                container.appendChild(toggleButton);
                
                // 插入到折叠元素之前
                collapseElement.parentNode.insertBefore(container, collapseElement);
                console.log('添加了手动切换按钮到折叠元素之前');
            }
        } catch (e) {
            console.error('添加手动切换按钮时出错:', e);
        }
    }
    
    // 添加全局折叠/展开功能
    function addGlobalCollapseFunction() {
        try {
            // 添加全局辅助函数
            window.toggleCollapse = function(targetId) {
                try {
                    const targetElement = document.getElementById(targetId);
                    if (!targetElement) {
                        console.warn('找不到折叠目标元素: ' + targetId);
                        return;
                    }
                    
                    console.log('手动切换折叠状态: ' + targetId);
                    
                    // 手动切换折叠状态
                    targetElement.classList.toggle('show');
                    
                    // 更新对应按钮的aria-expanded属性
                    const button = document.querySelector('[data-bs-target="#' + targetId + '"], [data-target="#' + targetId + '"]');
                    if (button) {
                        const isExpanded = targetElement.classList.contains('show');
                        button.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
                    }
                } catch (e) {
                    console.error('手动切换折叠状态时出错:', e);
                }
            };
            
            // 添加全局折叠/展开所有功能
            window.toggleAllCollapses = function(show) {
                try {
                    // 查找所有折叠区域
                    const collapseElements = document.querySelectorAll('.collapse');
                    console.log('找到 ' + collapseElements.length + ' 个折叠区域');
                    
                    // 切换所有折叠区域
                    collapseElements.forEach(function(element) {
                        if (show === true) {
                            // 展开
                            if (!element.classList.contains('show')) {
                                element.classList.add('show');
                            }
                        } else if (show === false) {
                            // 折叠
                            if (element.classList.contains('show')) {
                                element.classList.remove('show');
                            }
                        } else {
                            // 切换
                            element.classList.toggle('show');
                        }
                        
                        // 更新对应按钮的aria-expanded属性
                        const id = element.id;
                        if (id) {
                            const button = document.querySelector('[data-bs-target="#' + id + '"], [data-target="#' + id + '"]');
                            if (button) {
                                const isExpanded = element.classList.contains('show');
                                button.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
                            }
                        }
                    });
                    
                    console.log('已' + (show === true ? '展开' : (show === false ? '折叠' : '切换')) + '所有折叠区域');
                } catch (e) {
                    console.error('切换所有折叠区域时出错:', e);
                }
            };
            
            // 添加展开所有和折叠所有按钮
            addCollapseAllButtons();
        } catch (e) {
            console.error('添加全局折叠/展开功能时出错:', e);
        }
    }
    
    // 添加展开所有和折叠所有按钮
    function addCollapseAllButtons() {
        try {
            // 检查是否已经有展开所有和折叠所有按钮
            if (document.querySelector('.collapse-all-btn')) {
                console.log('已存在展开所有和折叠所有按钮，跳过创建');
                return;
            }
            
            // 查找所有折叠区域
            const collapseElements = document.querySelectorAll('.collapse');
            if (collapseElements.length === 0) {
                console.log('未找到折叠区域，跳过创建展开所有和折叠所有按钮');
                return;
            }
            
            // 创建按钮容器
            const container = document.createElement('div');
            container.className = 'text-end mt-3 mb-3';
            
            // 创建展开所有按钮
            const expandAllButton = document.createElement('button');
            expandAllButton.className = 'btn btn-sm btn-outline-primary collapse-all-btn me-2';
            expandAllButton.textContent = '展开所有';
            expandAllButton.onclick = function() {
                window.toggleAllCollapses(true);
            };
            
            // 创建折叠所有按钮
            const collapseAllButton = document.createElement('button');
            collapseAllButton.className = 'btn btn-sm btn-outline-secondary collapse-all-btn';
            collapseAllButton.textContent = '折叠所有';
            collapseAllButton.onclick = function() {
                window.toggleAllCollapses(false);
            };
            
            // 添加按钮到容器
            container.appendChild(expandAllButton);
            container.appendChild(collapseAllButton);
            
            // 查找合适的位置添加按钮
            const mainContent = document.querySelector('.container > .row > .col-md-8, .container > .row > .col-md-9, .container > .row > .col-md-12');
            if (mainContent) {
                // 添加到主内容区域的顶部
                mainContent.insertBefore(container, mainContent.firstChild);
                console.log('添加了展开所有和折叠所有按钮到主内容区域');
            } else {
                // 添加到第一个折叠区域之前
                const firstCollapse = collapseElements[0];
                if (firstCollapse.parentNode) {
                    firstCollapse.parentNode.insertBefore(container, firstCollapse);
                    console.log('添加了展开所有和折叠所有按钮到第一个折叠区域之前');
                }
            }
        } catch (e) {
            console.error('添加展开所有和折叠所有按钮时出错:', e);
        }
    }
})();
