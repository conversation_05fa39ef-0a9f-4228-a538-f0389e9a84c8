Option Explicit

' 九猫小说分析系统 v2.0 简化版启动脚本
' 此脚本会检查必要的依赖并启动系统

' 创建Shell对象
Dim WshShell
Set WshShell = CreateObject("WScript.Shell")

' 显示启动信息
WScript.Echo "正在启动九猫小说分析系统 v2.0 简化版..."
WScript.Echo "正在检查环境和依赖..."

' 检查Python是否安装
On Error Resume Next
WshShell.Run "python --version", 0, True
If Err.Number <> 0 Then
    WScript.Echo "错误: 未检测到Python。请安装Python 3.8或更高版本。"
    WScript.Quit
End If
On Error GoTo 0

' 创建并运行依赖检查和安装脚本
Dim fso, checkDepsFile
Set fso = CreateObject("Scripting.FileSystemObject")
checkDepsFile = "check_dependencies.py"

' 创建依赖检查脚本
Set checkDepsFile = fso.CreateTextFile(checkDepsFile, True)
checkDepsFile.WriteLine("import sys")
checkDepsFile.WriteLine("import subprocess")
checkDepsFile.WriteLine("import os")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("# 检查并安装必要的依赖")
checkDepsFile.WriteLine("required_packages = [")
checkDepsFile.WriteLine("    'flask',")
checkDepsFile.WriteLine("    'requests',")
checkDepsFile.WriteLine("    'sqlalchemy',")
checkDepsFile.WriteLine("    'werkzeug',")
checkDepsFile.WriteLine("    'markdown',")
checkDepsFile.WriteLine("    'markupsafe',")
checkDepsFile.WriteLine("    'psutil',")
checkDepsFile.WriteLine("    'python-dotenv'")
checkDepsFile.WriteLine("]")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("def check_and_install_packages():")
checkDepsFile.WriteLine("    print('检查必要的Python包...')")
checkDepsFile.WriteLine("    for package in required_packages:")
checkDepsFile.WriteLine("        try:")
checkDepsFile.WriteLine("            __import__(package.split('[')[0])")
checkDepsFile.WriteLine("            print(f'✓ {package} 已安装')")
checkDepsFile.WriteLine("        except ImportError:")
checkDepsFile.WriteLine("            print(f'安装 {package}...')")
checkDepsFile.WriteLine("            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])")
checkDepsFile.WriteLine("            print(f'✓ {package} 安装完成')")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("def fix_import_issues():")
checkDepsFile.WriteLine("    # 创建.env文件以设置环境变量")
checkDepsFile.WriteLine("    with open('.env', 'w') as env_file:")
checkDepsFile.WriteLine("        env_file.write('PYTHONPATH=.\\n')")
checkDepsFile.WriteLine("        env_file.write('FLASK_APP=src.web.app\\n')")
checkDepsFile.WriteLine("        env_file.write('FLASK_ENV=development\\n')")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("    # 创建启动脚本")
checkDepsFile.WriteLine("    with open('run_fixed.py', 'w') as run_file:")
checkDepsFile.WriteLine("        run_file.write('''\"\"\"")
checkDepsFile.WriteLine("九猫小说分析系统启动脚本 - 修复版")
checkDepsFile.WriteLine("\"\"\"")
checkDepsFile.WriteLine("import os")
checkDepsFile.WriteLine("import sys")
checkDepsFile.WriteLine("import logging")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("# 确保工作目录为脚本所在目录")
checkDepsFile.WriteLine("os.chdir(os.path.dirname(os.path.abspath(__file__)))")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("# 添加当前目录到Python路径")
checkDepsFile.WriteLine("sys.path.insert(0, os.path.abspath('.'))")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("# 配置日志")
checkDepsFile.WriteLine("logging.basicConfig(")
checkDepsFile.WriteLine("    level=logging.INFO,")
checkDepsFile.WriteLine("    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',")
checkDepsFile.WriteLine("    handlers=[")
checkDepsFile.WriteLine("        logging.StreamHandler(),")
checkDepsFile.WriteLine("        logging.FileHandler('app.log')")
checkDepsFile.WriteLine("    ]")
checkDepsFile.WriteLine(")")
checkDepsFile.WriteLine("logger = logging.getLogger(__name__)")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("def main():")
checkDepsFile.WriteLine("    \"\"\"主函数，启动九猫小说分析系统\"\"\"")
checkDepsFile.WriteLine("    logger.info('正在启动九猫小说分析系统...')")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("    # 尝试加载dotenv")
checkDepsFile.WriteLine("    try:")
checkDepsFile.WriteLine("        from dotenv import load_dotenv")
checkDepsFile.WriteLine("        load_dotenv()")
checkDepsFile.WriteLine("        logger.info('已加载环境变量')")
checkDepsFile.WriteLine("    except ImportError:")
checkDepsFile.WriteLine("        logger.warning('无法加载dotenv，将使用默认环境变量')")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("    # 检查是否存在main.py")
checkDepsFile.WriteLine("    if os.path.exists('main.py'):")
checkDepsFile.WriteLine("        logger.info('找到main.py，使用它启动系统')")
checkDepsFile.WriteLine("        try:")
checkDepsFile.WriteLine("            import main")
checkDepsFile.WriteLine("            # 如果main模块有run_app_with_monitoring函数，则调用它")
checkDepsFile.WriteLine("            if hasattr(main, 'run_app_with_monitoring'):")
checkDepsFile.WriteLine("                logger.info('使用run_app_with_monitoring启动系统')")
checkDepsFile.WriteLine("                main.run_app_with_monitoring()")
checkDepsFile.WriteLine("            # 否则，如果main是作为脚本运行的，则直接导入它")
checkDepsFile.WriteLine("            else:")
checkDepsFile.WriteLine("                logger.info('直接导入main模块')")
checkDepsFile.WriteLine("        except Exception as e:")
checkDepsFile.WriteLine("            logger.error(f'使用main.py启动系统时出错: {str(e)}')")
checkDepsFile.WriteLine("            logger.info('尝试使用src.web.app启动系统')")
checkDepsFile.WriteLine("            _start_with_app()")
checkDepsFile.WriteLine("    else:")
checkDepsFile.WriteLine("        logger.info('未找到main.py，使用src.web.app启动系统')")
checkDepsFile.WriteLine("        _start_with_app()")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("def _start_with_app():")
checkDepsFile.WriteLine("    \"\"\"使用src.web.app启动系统\"\"\"")
checkDepsFile.WriteLine("    try:")
checkDepsFile.WriteLine("        # 导入配置")
checkDepsFile.WriteLine("        import config")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("        # 导入Flask应用")
checkDepsFile.WriteLine("        from src.web.app import app")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("        # 启动应用")
checkDepsFile.WriteLine("        logger.info(f'启动九猫小说分析系统，监听 {config.HOST}:{config.PORT}')")
checkDepsFile.WriteLine("        app.run(")
checkDepsFile.WriteLine("            host=config.HOST,")
checkDepsFile.WriteLine("            port=config.PORT,")
checkDepsFile.WriteLine("            debug=config.DEBUG,")
checkDepsFile.WriteLine("            threaded=True,")
checkDepsFile.WriteLine("            use_reloader=False  # 禁用reloader以避免启动两个进程")
checkDepsFile.WriteLine("        )")
checkDepsFile.WriteLine("    except Exception as e:")
checkDepsFile.WriteLine("        logger.error(f'启动系统时出错: {str(e)}')")
checkDepsFile.WriteLine("        sys.exit(1)")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("if __name__ == '__main__':")
checkDepsFile.WriteLine("    main()")
checkDepsFile.WriteLine("''')")
checkDepsFile.WriteLine("")
checkDepsFile.WriteLine("if __name__ == '__main__':")
checkDepsFile.WriteLine("    check_and_install_packages()")
checkDepsFile.WriteLine("    fix_import_issues()")
checkDepsFile.WriteLine("    print('所有依赖已检查并安装完成')")
checkDepsFile.WriteLine("    print('已创建修复后的启动脚本: run_fixed.py')")
checkDepsFile.Close

' 运行依赖检查脚本
WScript.Echo "正在检查和安装必要的依赖..."
WshShell.Run "python " & checkDepsFile, 1, True

' 启动系统
WScript.Echo "正在启动九猫小说分析系统..."
WshShell.Run "python run_fixed.py", 1, False

' 清理
Set fso = Nothing
Set WshShell = Nothing

WScript.Echo "九猫小说分析系统已在后台启动，请稍候..."
