"""
模板兼容性路由
用于处理模板相关的API请求，确保前端能够正确访问模板数据
"""

import logging
from flask import Blueprint, jsonify
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.db.connection import Session
from datetime import datetime

# 创建蓝图
template_compat_bp = Blueprint('template_compat', __name__)

# 配置日志
logger = logging.getLogger(__name__)

# 直接处理模板请求的路由 - 添加更多路径变体以提高兼容性
@template_compat_bp.route('/api/novel/<int:novel_id>/template/<dimension>', methods=['GET'])
@template_compat_bp.route('/api/novels/<int:novel_id>/template/<dimension>', methods=['GET'])
@template_compat_bp.route('/v3/api/novel/<int:novel_id>/template/<dimension>', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/templates/<dimension>', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/structure', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/rhythm_pacing', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/language_style', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/opening_effectiveness', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/climax_pacing', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/theme_exploration', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/character_relationships', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/world_building', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/novel_characteristics', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/paragraph_flow', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/perspective_shifts', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/paragraph_length', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/sentence_variation', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/outline_analysis', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/template/chapter_outline', methods=['GET'])
def get_novel_template(novel_id, dimension="structure"):
    """
    获取小说的模板内容
    直接处理/api/novel/<novel_id>/template/<dimension>请求及其变体
    支持所有15个维度的固定路径和多种URL格式，提高兼容性
    """
    try:
        logger.info(f"直接处理模板请求: /api/novel/{novel_id}/template/{dimension}")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 ID {novel_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            # 构建模板内容
            template_content = None
            if result and result.content:
                # 从分析结果生成模板
                template_content = f"# {novel.title} - {dimension} 设定模板\n\n"
                template_content += f"## 基本信息\n"
                template_content += f"- 小说: {novel.title}\n"
                template_content += f"- 作者: {novel.author}\n"
                template_content += f"- 维度: {dimension}\n"
                template_content += f"- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                template_content += f"## 设定内容\n"
                template_content += result.content
            else:
                # 如果没有找到分析结果，生成默认模板内容
                logger.warning(f"未找到小说 ID {novel_id} 的 {dimension} 维度分析结果，生成默认模板")
                template_content = f"# {novel.title} - {dimension} 设定模板\n\n"
                template_content += f"## 基本信息\n"
                template_content += f"- 小说: {novel.title}\n"
                template_content += f"- 作者: {novel.author}\n"
                template_content += f"- 维度: {dimension}\n"
                template_content += f"- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                template_content += f"## 设定内容\n"
                template_content += f"该小说尚未进行 {dimension} 维度的分析，请先在控制台进行分析。"

            # 构建响应数据
            response_data = {
                'success': True,
                'template': template_content,
                'template_content': template_content
            }

            logger.info(f"成功获取小说设定模板 [小说ID: {novel_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说设定模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 直接处理章节模板请求的路由 - 添加更多路径变体以提高兼容性
@template_compat_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>', methods=['GET'])
@template_compat_bp.route('/api/novels/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>', methods=['GET'])
@template_compat_bp.route('/v3/api/novel/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/chapters/<int:chapter_id>/template/<dimension>', methods=['GET'])
@template_compat_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/templates/<dimension>', methods=['GET'])
def get_chapter_template(novel_id, chapter_id, dimension):
    """
    获取章节的模板内容
    直接处理/api/novel/<novel_id>/chapter/<chapter_id>/template/<dimension>请求及其变体
    支持多种URL格式，提高兼容性
    """
    try:
        logger.info(f"直接处理章节模板请求: /api/novel/{novel_id}/chapter/{chapter_id}/template/{dimension}")
        session = Session()
        try:
            # 获取小说和章节
            novel = session.query(Novel).get(novel_id)
            chapter = session.query(Chapter).get(chapter_id)

            if not novel:
                logger.error(f"未找到小说 ID {novel_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            if not chapter:
                logger.error(f"未找到章节 ID {chapter_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 获取章节分析结果
            chapter_result = session.query(ChapterAnalysisResult).filter_by(
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            # 构建章节模板内容
            template_content = None
            if chapter_result and chapter_result.content:
                # 从分析结果生成模板
                template_content = f"# {novel.title} - {chapter.title or f'第{chapter.chapter_number}章'} - {dimension} 设定模板\n\n"
                template_content += f"## 基本信息\n"
                template_content += f"- 小说: {novel.title}\n"
                template_content += f"- 章节: {chapter.title or f'第{chapter.chapter_number}章'}\n"
                template_content += f"- 维度: {dimension}\n"
                template_content += f"- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                template_content += f"## 设定内容\n"
                template_content += chapter_result.content
            else:
                # 如果没有找到章节分析结果，生成默认模板内容
                logger.warning(f"未找到章节 ID {chapter_id} 的 {dimension} 维度分析结果，生成默认模板")
                template_content = f"# {novel.title} - {chapter.title or f'第{chapter.chapter_number}章'} - {dimension} 设定模板\n\n"
                template_content += f"## 基本信息\n"
                template_content += f"- 小说: {novel.title}\n"
                template_content += f"- 章节: {chapter.title or f'第{chapter.chapter_number}章'}\n"
                template_content += f"- 维度: {dimension}\n"
                template_content += f"- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                template_content += f"## 设定内容\n"
                template_content += f"该章节尚未进行 {dimension} 维度的分析，请先在控制台进行分析。"

            # 构建响应数据
            response_data = {
                'success': True,
                'template': template_content,
                'template_content': template_content
            }

            logger.info(f"成功获取章节设定模板 [小说ID: {novel_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节设定模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
