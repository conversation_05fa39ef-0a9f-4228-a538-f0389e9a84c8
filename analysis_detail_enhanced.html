{% extends "base.html" %}

{% block title %}{{ dimension_name }}分析 - {{ novel.title }}{% endblock %}

{% block styles %}
<!-- 引入增强版推理内容显示组件样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/reasoning_content_display.css') }}">
<style>
    /* 页面特定样式 */
    .analysis-container {
        background-color: #fffdf7;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    
    .analysis-header {
        margin-bottom: 20px;
        border-bottom: 1px solid #e8e0c5;
        padding-bottom: 15px;
    }
    
    .analysis-header h1 {
        color: #5a4a2f;
        font-size: 24px;
        margin-bottom: 10px;
    }
    
    .analysis-header .novel-title {
        color: #8a7a5f;
        font-size: 16px;
    }
    
    .analysis-tabs {
        margin-bottom: 20px;
    }
    
    .analysis-tabs .nav-link {
        color: #8a7a5f;
        border: 1px solid #e8e0c5;
        margin-right: 5px;
        border-radius: 4px;
    }
    
    .analysis-tabs .nav-link.active {
        background-color: #f9f5e8;
        color: #5a4a2f;
        border-color: #d9c89e;
    }
    
    .analysis-content {
        background-color: #fff;
        border: 1px solid #e8e0c5;
        border-radius: 8px;
        padding: 20px;
    }
    
    .actions-bar {
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="analysis-container">
        <!-- 分析头部 -->
        <div class="analysis-header">
            <h1>{{ dimension_name }}分析</h1>
            <div class="novel-title">
                <i class="fas fa-book"></i> {{ novel.title }}
                {% if is_chapter %}
                <span class="ms-2">- {{ chapter.title }}</span>
                {% endif %}
            </div>
        </div>
        
        <!-- 操作栏 -->
        <div class="actions-bar d-flex justify-content-between">
            <div>
                <a href="{{ url_for('novel.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> 返回小说
                </a>
                {% if is_chapter %}
                <a href="{{ url_for('chapter.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-file-alt"></i> 查看章节
                </a>
                {% endif %}
            </div>
            <div>
                <button id="reanalyzeBtn" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> 重新分析
                </button>
            </div>
        </div>
        
        <!-- 分析标签页 -->
        <div class="analysis-tabs">
            <ul class="nav nav-tabs" id="analysisTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="result-tab" data-bs-toggle="tab" data-bs-target="#result" type="button" role="tab" aria-controls="result" aria-selected="true">
                        分析结果
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reasoning-tab" data-bs-toggle="tab" data-bs-target="#reasoning" type="button" role="tab" aria-controls="reasoning" aria-selected="false">
                        推理过程
                    </button>
                </li>
            </ul>
        </div>
        
        <!-- 标签页内容 -->
        <div class="tab-content" id="analysisTabsContent">
            <!-- 分析结果标签页 -->
            <div class="tab-pane fade show active" id="result" role="tabpanel" aria-labelledby="result-tab">
                <div class="analysis-content markdown-content" id="analysisContent">
                    {{ result.content|safe }}
                </div>
            </div>
            
            <!-- 推理过程标签页 -->
            <div class="tab-pane fade" id="reasoning" role="tabpanel" aria-labelledby="reasoning-tab">
                <!-- 增强版推理内容容器 -->
                <div id="reasoningContent" 
                     data-reasoning-container="true" 
                     data-novel-id="{{ novel.id }}" 
                     data-dimension="{{ dimension }}"
                     {% if is_chapter %}data-chapter-id="{{ chapter.id }}"{% endif %}
                     data-auto-expand="true">
                    <!-- 内容将由JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 重新分析确认模态框 -->
<div class="modal fade" id="reanalyzeModal" tabindex="-1" aria-labelledby="reanalyzeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reanalyzeModalLabel">确认重新分析</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要重新分析{{ dimension_name }}吗？这将覆盖现有的分析结果。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmReanalyze">确认重新分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入增强版推理内容显示组件 -->
<script src="{{ url_for('static', filename='js/reasoning_content_display.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 渲染分析结果
        const analysisContent = document.getElementById('analysisContent');
        if (window.renderMarkdown) {
            window.renderMarkdown(analysisContent.innerHTML, analysisContent);
        }
        
        // 重新分析按钮
        document.getElementById('reanalyzeBtn').addEventListener('click', function() {
            const reanalyzeModal = new bootstrap.Modal(document.getElementById('reanalyzeModal'));
            reanalyzeModal.show();
        });
        
        // 确认重新分析
        document.getElementById('confirmReanalyze').addEventListener('click', function() {
            // 隐藏模态框
            bootstrap.Modal.getInstance(document.getElementById('reanalyzeModal')).hide();
            
            // 显示加载中状态
            document.getElementById('analysisContent').innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在重新分析，请稍候...</p>
                </div>
            `;
            
            // 构建API URL
            {% if is_chapter %}
            const apiUrl = `/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analyze`;
            {% else %}
            const apiUrl = `/api/novel/{{ novel.id }}/analyze`;
            {% endif %}
            
            // 发送请求
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    dimensions: ['{{ dimension }}'],
                    use_real_api: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 分析已开始，显示提示
                    document.getElementById('analysisContent').innerHTML = `
                        <div class="alert alert-info">
                            <p><i class="fas fa-info-circle"></i> 分析已开始，请稍后刷新页面查看结果</p>
                        </div>
                    `;
                } else {
                    // 显示错误信息
                    document.getElementById('analysisContent').innerHTML = `
                        <div class="alert alert-danger">
                            <p><i class="fas fa-exclamation-circle"></i> 启动分析失败</p>
                            <p class="small text-muted mt-2">${data.error || '未知错误'}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // 显示错误信息
                document.getElementById('analysisContent').innerHTML = `
                    <div class="alert alert-danger">
                        <p><i class="fas fa-exclamation-circle"></i> 请求出错</p>
                        <p class="small text-muted mt-2">${error.message}</p>
                    </div>
                `;
            });
        });
    });
</script>
{% endblock %}
