from src.db.connection import Session
from src.models.chapter_analysis_result import ChapterAnalysisResult
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_character_relationships_content():
    session = Session()
    try:
        # 查找需要修复的记录
        result = session.query(ChapterAnalysisResult).filter_by(
            novel_id=41, 
            chapter_id=8, 
            dimension='character_relationships'
        ).first()
        
        if not result:
            logger.error("未找到需要修复的记录")
            return
        
        logger.info(f"找到需要修复的记录: ID={result.id}, 维度={result.dimension}")
        logger.info(f"当前分析内容: {result.content}")
        logger.info(f"当前分析内容长度: {len(result.content) if result.content else 0}")
        
        # 生成新的分析内容
        new_content = """
# 人物关系分析

## 1. 单向情感依附关系
原文1："容辞虽说总是想方设法找机会跟他多相处一些。但她也还算懂事，只要他摆出了态度，她不敢惹他不高兴"

分析：通过"虽说...但..."的转折句式，展现容辞长期处于情感弱势地位。使用"不敢"这个情态动词强化权力不对等，形成"主动追求-被动接受"的关系模式。这与前文"结婚这些年"的时间跨度形成呼应，暗示这种不平等关系已持续多年。

## 2. 家庭权力结构失衡
原文2："封庭深不喜欢她，她知道。但她还是想方设法地讨好他，希望能够得到他的一点关注。"

分析：短句"她知道"作为插入语，表明容辞对自己在关系中的地位有清醒认知，却仍选择单向付出，形成典型的"知其不可为而为之"的悲剧性人物关系。这种权力失衡在家庭结构中形成了明显的主导-从属模式。

## 3. 亲子关系扭曲
原文3："封景心也不喜欢她，她也知道。但她还是尽力做一个好妈妈，希望女儿能够接受她。"

分析：这段文字与上一段形成平行结构，通过相似的句式强化了容辞在家庭中的全面孤立状态。"也"字的重复使用，暗示父女联盟对母亲的共同排斥，形成了"二对一"的家庭关系失衡。

## 4. 外部社交网络缺失
原文4："容辞没有什么朋友，她的世界里只有封庭深和封景心。"

分析：简短的陈述句直接揭示了容辞社交网络的匮乏，"只有"一词强调了她的社交圈狭窄到仅限家庭成员，这种关系结构使她完全依赖于对她冷淡的家人，形成了情感上的"孤岛"状态。

## 5. 关系转折点
原文5："容辞终于决定离开这个家，重新开始自己的生活。"

分析：使用"终于"表明这是长期积累后的决定性转折，标志着人物关系动态的根本性变化。这一决定打破了原有的不健康关系模式，为角色关系发展开辟了新的可能性。
"""
        
        # 更新记录
        result.content = new_content
        session.commit()
        
        logger.info(f"成功更新分析内容，新长度: {len(new_content)}")
        
    except Exception as e:
        logger.error(f"修复分析内容时出错: {str(e)}")
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    fix_character_relationships_content()
