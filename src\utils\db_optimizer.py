"""
九猫系统数据库连接优化模块
优化数据库连接池，减少内存占用
"""
import logging
import time
import threading
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

# 全局变量，用于跟踪数据库连接状态
db_connections_count = 0
last_connection_check = 0
db_cleanup_lock = threading.Lock()

def optimize_db_connection_pool():
    """
    优化数据库连接池配置，减少内存占用
    """
    try:
        # 导入数据库连接模块
        from src.db.connection import engine
        
        # 获取当前连接池大小
        pool_size = engine.pool.size()
        max_overflow = engine.pool._max_overflow
        
        logger.info(f"当前数据库连接池状态: 大小={pool_size}, 最大溢出={max_overflow}")
        
        # 如果连接池过大，减小连接池大小
        if pool_size > 5:
            # 设置更保守的连接池参数
            engine.pool._pool.maxsize = 3
            engine.pool._max_overflow = 2
            
            logger.info(f"已优化数据库连接池: 大小=3, 最大溢出=2")
            
            # 释放连接池
            dispose_engine()
            
            return True
    except Exception as e:
        logger.error(f"优化数据库连接池时出错: {str(e)}")
    
    return False

def check_and_cleanup_db_connections(force: bool = False):
    """
    检查并清理数据库连接
    
    Args:
        force: 是否强制清理
    """
    global last_connection_check, db_connections_count
    
    # 使用锁防止多线程同时清理
    if not db_cleanup_lock.acquire(blocking=False):
        return
    
    try:
        current_time = time.time()
        
        # 如果距离上次检查不到30秒且不是强制清理，则跳过
        if not force and current_time - last_connection_check < 30:
            return
            
        last_connection_check = current_time
        
        try:
            # 导入数据库连接模块
            from src.db.connection import engine, dispose_engine, Session
            
            # 获取当前连接池状态
            pool_size = engine.pool.size()
            checked_out = engine.pool.checkedout()
            
            # 记录连接数变化
            old_count = db_connections_count
            db_connections_count = checked_out
            
            # 如果连接数增加，记录警告
            if checked_out > old_count + 2:
                logger.warning(f"数据库连接数快速增加: {old_count} -> {checked_out}")
            
            # 如果已检出连接数超过阈值，尝试清理
            if checked_out > 10 or force:
                logger.warning(f"数据库连接数过多 ({checked_out})，尝试清理...")
                
                # 关闭所有会话
                Session.remove()
                
                # 释放连接池
                dispose_engine()
                
                # 记录清理结果
                new_checked_out = engine.pool.checkedout()
                logger.info(f"数据库连接清理完成: {checked_out} -> {new_checked_out}")
                
                # 更新连接数
                db_connections_count = new_checked_out
                
                return True
        except Exception as e:
            logger.error(f"清理数据库连接时出错: {str(e)}")
    finally:
        db_cleanup_lock.release()
    
    return False

def dispose_engine():
    """
    释放数据库引擎连接池
    """
    try:
        from src.db.connection import dispose_engine as _dispose_engine
        _dispose_engine()
        logger.info("已释放数据库连接池")
        return True
    except Exception as e:
        logger.error(f"释放数据库连接池时出错: {str(e)}")
        return False

def get_db_connection_status() -> Dict[str, Any]:
    """
    获取数据库连接状态
    
    Returns:
        包含数据库连接状态的字典
    """
    try:
        from src.db.connection import engine
        
        pool_size = engine.pool.size()
        checked_out = engine.pool.checkedout()
        
        return {
            "pool_size": pool_size,
            "checked_out": checked_out,
            "available": pool_size - checked_out,
            "last_check": last_connection_check,
            "connections_count": db_connections_count
        }
    except Exception as e:
        logger.error(f"获取数据库连接状态时出错: {str(e)}")
        return {
            "error": str(e)
        }
