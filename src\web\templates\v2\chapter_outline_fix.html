{% extends "v2/base.html" %}

{% block title %}{{ novel.title }} - 章纲分析 - 九猫小说分析系统v2.0{% endblock %}

{% block head %}
{{ super() }}
<!-- 加载修复脚本 -->
<script src="{{ url_for('static', filename='js/json-parse-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/json-parse-fix-enhanced.js';"></script>
<script src="{{ url_for('static', filename='js/node-replace-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/node-replace-fix.js';"></script>
<script src="{{ url_for('static', filename='js/chapter-outline-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-outline-fix.js';"></script>
{% endblock %}

{% block extra_css %}
<style>
    .analysis-header {
        background-color: var(--primary-light);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    .tab-content {
        padding: 1.5rem 0;
    }
    .nav-pills .nav-link {
        color: var(--text-color);
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        margin-right: 0.5rem;
        font-weight: 500;
    }
    .nav-pills .nav-link.active {
        background-color: var(--primary-color);
        color: white;
    }
    .markdown-content {
        line-height: 1.7;
    }
    .markdown-content h1, 
    .markdown-content h2, 
    .markdown-content h3 {
        margin-top: 1.5rem;
        margin-bottom: 1rem;
    }
    .markdown-content ul, 
    .markdown-content ol {
        padding-left: 1.5rem;
    }
    
    .metadata-item {
        margin-bottom: 0.5rem;
    }

    .visualization-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }

    .analysis-logs {
        font-family: monospace;
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .log-entry {
        padding: 2px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .log-entry:last-child {
        border-bottom: none;
    }

    #analysisLogsCollapse .card-body,
    #analysisProcessCollapse .card-body {
        padding: 0.5rem;
        background-color: #f8f9fa;
    }

    .analysis-process-card {
        margin-bottom: 1rem;
        border: 1px solid rgba(0,0,0,.125);
    }

    .analysis-process-card .card-header {
        background-color: rgba(0,0,0,.03);
        padding: 0.5rem 1rem;
    }

    /* 推理过程样式 */
    .reasoning-text.markdown-content h2 {
        color: #8a6d3b;
        background-color: #fcf8e3;
        padding: 10px;
        border-left: 4px solid #f0ad4e;
        margin-top: 20px;
        margin-bottom: 15px;
        font-size: 1.4rem;
    }

    .reasoning-text.markdown-content h3 {
        color: #31708f;
        border-bottom: 1px solid #bce8f1;
        padding-bottom: 5px;
        margin-top: 15px;
        margin-bottom: 10px;
        font-size: 1.2rem;
    }

    .reasoning-text.markdown-content strong {
        color: #333;
    }

    .reasoning-text.markdown-content ol {
        padding-left: 20px;
    }

    .reasoning-text.markdown-content ol li {
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 返回按钮和标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="{{ url_for('v2.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回小说详情
        </a>
    </div>
    <div>
        <button class="btn btn-primary" id="reanalyzeBtn">
            <i class="fas fa-sync-alt me-2"></i>重新分析
        </button>
    </div>
</div>

<!-- 分析标题 -->
<div class="analysis-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">章纲分析</h1>
            <p class="mb-0">
                <span class="badge bg-primary me-2">{{ novel.title }}</span>
                {% if novel.author %}<span class="badge bg-secondary me-2">作者: {{ novel.author }}</span>{% endif %}
                <span class="badge bg-info">{{ novel.word_count }} 字</span>
            </p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="d-flex justify-content-md-end align-items-center">
                <i class="fas fa-list-ol fa-3x text-primary me-3"></i>
                <div>
                    <p class="mb-0 small">分析时间</p>
                    <p class="mb-0 fw-bold">{{ analysis_result.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 内容导航 -->
<ul class="nav nav-pills mb-4" id="analysisTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="result-tab" data-bs-toggle="tab" data-bs-target="#result" type="button" role="tab" aria-controls="result" aria-selected="true">
            <i class="fas fa-chart-bar me-1"></i>分析结果
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="reasoning-tab" data-bs-toggle="tab" data-bs-target="#reasoning" type="button" role="tab" aria-controls="reasoning" aria-selected="false">
            <i class="fas fa-brain me-1"></i>推理过程
        </button>
    </li>
</ul>

<!-- 选项卡内容 -->
<div class="tab-content" id="analysisTabsContent">
    <!-- 分析结果选项卡 -->
    <div class="tab-pane fade show active" id="result" role="tabpanel" aria-labelledby="result-tab">
        <div class="card">
            <div class="card-body">
                <div class="markdown-content" id="analysisContent">
                    {% if analysis_result and analysis_result.content %}
                        {{ analysis_result.content|safe }}
                    {% else %}
                        <p class="text-muted">暂无分析结果</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 推理过程选项卡 -->
    <div class="tab-pane fade" id="reasoning" role="tabpanel" aria-labelledby="reasoning-tab">
        <div class="card">
            <div class="card-body">
                <div class="markdown-content" id="reasoningContent">
                    {% if analysis_result.reasoning_content %}
                    <div class="reasoning-text markdown-content">
                        {{ analysis_result.reasoning_content|safe }}
                    </div>
                    {% elif analysis_result.metadata and analysis_result.metadata.reasoning_content %}
                    <div class="reasoning-text markdown-content">
                        {{ analysis_result.metadata.reasoning_content|safe }}
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>未找到推理过程数据。
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 元数据卡片 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">分析元数据</h5>
            </div>
            <div class="card-body">
                <div id="analysis-metadata" style="display:none;" data-metadata='{{ analysis_result.safe_metadata|tojson_safe if analysis_result and analysis_result.analysis_metadata else "{}" }}'></div>
                <div class="metadata-container">
                    {% if analysis_result and analysis_result.analysis_metadata %}
                        <div class="metadata-item"><strong>处理时间：</strong> {{ analysis_result.analysis_metadata.processing_time|default(0)|round(2) }} 秒</div>
                        <div class="metadata-item"><strong>分块数量：</strong> {{ analysis_result.analysis_metadata.chunk_count|default(0) }}</div>
                        <div class="metadata-item"><strong>API调用次数：</strong> {{ analysis_result.analysis_metadata.api_calls|default(0) }}</div>
                        <div class="metadata-item"><strong>令牌使用量：</strong> {{ analysis_result.analysis_metadata.tokens_used|default(0) }}</div>
                        <div class="metadata-item"><strong>费用：</strong> {{ analysis_result.analysis_metadata.cost|default(0)|round(4) }} 元</div>
                    {% else %}
                        <div class="metadata-item"><strong>处理时间：</strong> 0.00 秒</div>
                        <div class="metadata-item"><strong>分块数量：</strong> 0</div>
                        <div class="metadata-item"><strong>API调用次数：</strong> 0</div>
                        <div class="metadata-item"><strong>令牌使用量：</strong> 0</div>
                        <div class="metadata-item"><strong>费用：</strong> 0.0000 元</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 重新分析确认模态框 -->
<div class="modal fade" id="reanalyzeModal" tabindex="-1" aria-labelledby="reanalyzeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reanalyzeModalLabel">确认重新分析</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要重新分析"章纲分析"维度吗？这将覆盖现有的分析结果。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmReanalyzeBtn">确认重新分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 分析结果页面修复脚本 -->
<script src="{{ url_for('static', filename='js/analysis-page-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-page-fix.js';"></script>

<script>
    // 全局变量
    const novelId = "{{ novel.id }}";
    const dimension = "chapter_outline";

    document.addEventListener('DOMContentLoaded', function() {
        // 渲染分析结果
        const analysisContent = document.getElementById('analysisContent');
        if (window.renderMarkdown) {
            renderMarkdown(analysisContent.innerHTML, analysisContent);
        }
        
        // 加载推理过程
        const reasoningTab = document.getElementById('reasoning-tab');
        reasoningTab.addEventListener('click', function() {
            // 已经在HTML中加载了推理过程，不需要再次加载
        });

        // 重新分析按钮
        document.getElementById('reanalyzeBtn').addEventListener('click', function() {
            const reanalyzeModal = new bootstrap.Modal(document.getElementById('reanalyzeModal'));
            reanalyzeModal.show();
        });

        // 确认重新分析按钮
        document.getElementById('confirmReanalyzeBtn').addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>分析中...';
            
            // 发送API请求
            fetch(`/api/novel/{{ novel.id }}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dimensions: ['chapter_outline'],
                    force_reanalysis: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 刷新页面
                    location.reload();
                } else {
                    alert(`重新分析失败: ${data.error}`);
                    this.disabled = false;
                    this.innerHTML = '确认重新分析';
                }
            })
            .catch(error => {
                alert(`发送请求时出错: ${error.message}`);
                this.disabled = false;
                this.innerHTML = '确认重新分析';
            });
        });
    });
</script>
{% endblock %}
