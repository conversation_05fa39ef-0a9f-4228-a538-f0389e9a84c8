/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 控制台日志修复工具
 *
 * 这个脚本用于修复控制台日志初始化问题和JSON.parse语法错误
 */

(function() {
    console.log('控制台日志修复工具已加载');

    // 修复控制台日志初始化问题
    function fixConsoleLogger() {
        console.log('尝试修复控制台日志初始化问题');

        // 检查是否已经有initializeConsoleLogger函数
        if (typeof window.initializeConsoleLogger === 'function') {
            console.log('已存在initializeConsoleLogger函数，进行替换');

            // 保存原始函数
            const originalInitializeConsoleLogger = window.initializeConsoleLogger;

            // 替换为增强版本
            window.initializeConsoleLogger = function() {
                console.log('执行增强版initializeConsoleLogger');

                try {
                    // 确保consoleLogger对象存在
                    if (!window.consoleLogger) {
                        console.log('创建consoleLogger对象');
                        window.consoleLogger = {
                            lastLogTimestamp: '',
                            isPollingLogs: false,
                            logPollingInterval: null,
                            currentNovelId: null,
                            currentLogLevel: 'all',
                            autoScroll: true,
                            lastUpdateTime: 0,
                            hasAddedPlaceholder: false,
                            addLog: function(message, level, timestamp, dimension, progress) {
                                console.log(`[${level}] ${message}`);
                            }
                        };
                    }

                    // 获取小说ID和标题 - 使用更安全的变量名
                    let novelId = null;
                    let novelTitle = null;
                    try {
                        const novelContainerEl = document.getElementById('novel-container');
                        if (novelContainerEl) {
                            novelId = novelContainerEl.getAttribute('data-novel-id');
                            novelTitle = novelContainerEl.getAttribute('data-novel-title');
                        }
                    } catch (e) {
                        console.error('获取小说容器元素时出错:', e);
                    }

                    console.log(`获取到小说ID: ${novelId}, 标题: ${novelTitle}`);

                    // 确保initConsoleLogger函数存在
                    if (typeof window.initConsoleLogger !== 'function') {
                        console.log('创建initConsoleLogger函数');
                        window.initConsoleLogger = function(novelId, novelTitle) {
                            console.log(`初始化控制台日志: 小说ID=${novelId}, 标题=${novelTitle}`);
                            window.consoleLogger.currentNovelId = novelId;

                            // 添加欢迎消息到控制台
                            const consoleInner = document.querySelector('.console-inner');
                            if (consoleInner) {
                                consoleInner.innerHTML = `
                                    <div class="console-line log-welcome">// 九猫分析控制台已准备就绪，等待分析开始...</div>
                                    <div class="console-line log-welcome">// 当前小说: 《${novelTitle || novelId}》</div>
                                    <div class="console-line log-welcome">// 分析开始后将在此显示实时日志</div>
                                `;
                            }

                            // 确保startLogPolling函数存在
                            if (typeof window.startLogPolling !== 'function') {
                                window.startLogPolling = function() {
                                    console.log('开始轮询日志');
                                    window.consoleLogger.isPollingLogs = true;
                                };
                            }

                            // 确保clearConsole函数存在
                            if (typeof window.clearConsole !== 'function') {
                                window.clearConsole = function() {
                                    console.log('清空控制台');
                                    const consoleInner = document.querySelector('.console-inner');
                                    if (consoleInner) {
                                        consoleInner.innerHTML = '';
                                    }
                                };
                            }
                        };
                    }

                    // 调用原始函数
                    originalInitializeConsoleLogger();

                    // 如果原始函数失败，使用我们的备用实现
                    if (!window.consoleLogger.currentNovelId) {
                        console.log('原始初始化失败，使用备用实现');
                        if (typeof window.initConsoleLogger === 'function') {
                            window.initConsoleLogger(novelId, novelTitle);
                        }
                    }
                } catch (e) {
                    console.error('增强版initializeConsoleLogger出错:', e);

                    // 尝试使用最简单的实现
                    try {
                        console.log('使用最简单的控制台日志实现');
                        const consoleInner = document.querySelector('.console-inner');
                        if (consoleInner) {
                            consoleInner.innerHTML = `
                                <div class="console-line log-welcome">// 九猫分析控制台已准备就绪</div>
                                <div class="console-line log-welcome">// 控制台日志初始化时出错，使用简化版本</div>
                            `;
                        }
                    } catch (innerError) {
                        console.error('最简单的控制台实现也失败了:', innerError);
                    }
                }
            };

            // 如果DOM已加载完成，立即执行修复后的函数
            if (document.readyState === 'complete' || document.readyState === 'interactive') {
                console.log('DOM已加载，立即执行修复后的initializeConsoleLogger');
                window.initializeConsoleLogger();
            }
        } else {
            console.log('未找到initializeConsoleLogger函数，创建新函数');

            // 创建新函数
            window.initializeConsoleLogger = function() {
                console.log('执行新创建的initializeConsoleLogger');

                try {
                    // 确保consoleLogger对象存在
                    if (!window.consoleLogger) {
                        window.consoleLogger = {
                            lastLogTimestamp: '',
                            isPollingLogs: false,
                            logPollingInterval: null,
                            currentNovelId: null,
                            currentLogLevel: 'all',
                            autoScroll: true,
                            lastUpdateTime: 0,
                            hasAddedPlaceholder: false,
                            addLog: function(message, level, timestamp, dimension, progress) {
                                console.log(`[${level}] ${message}`);
                            }
                        };
                    }

                    // 获取小说ID和标题 - 使用更安全的变量名
                    let novelId = null;
                    let novelTitle = null;
                    try {
                        const novelContainerEl = document.getElementById('novel-container');
                        if (novelContainerEl) {
                            novelId = novelContainerEl.getAttribute('data-novel-id');
                            novelTitle = novelContainerEl.getAttribute('data-novel-title');
                        }
                    } catch (e) {
                        console.error('获取小说容器元素时出错:', e);
                    }

                    // 确保initConsoleLogger函数存在
                    if (typeof window.initConsoleLogger !== 'function') {
                        window.initConsoleLogger = function(novelId, novelTitle) {
                            console.log(`初始化控制台日志: 小说ID=${novelId}, 标题=${novelTitle}`);
                            window.consoleLogger.currentNovelId = novelId;

                            // 添加欢迎消息到控制台
                            const consoleInner = document.querySelector('.console-inner');
                            if (consoleInner) {
                                consoleInner.innerHTML = `
                                    <div class="console-line log-welcome">// 九猫分析控制台已准备就绪，等待分析开始...</div>
                                    <div class="console-line log-welcome">// 当前小说: 《${novelTitle || novelId}》</div>
                                    <div class="console-line log-welcome">// 分析开始后将在此显示实时日志</div>
                                `;
                            }
                        };
                    }

                    // 确保startLogPolling函数存在
                    if (typeof window.startLogPolling !== 'function') {
                        window.startLogPolling = function() {
                            console.log('开始轮询日志');
                            window.consoleLogger.isPollingLogs = true;
                        };
                    }

                    // 确保clearConsole函数存在
                    if (typeof window.clearConsole !== 'function') {
                        window.clearConsole = function() {
                            console.log('清空控制台');
                            const consoleInner = document.querySelector('.console-inner');
                            if (consoleInner) {
                                consoleInner.innerHTML = '';
                            }
                        };
                    }

                    // 调用initConsoleLogger
                    if (typeof window.initConsoleLogger === 'function') {
                        window.initConsoleLogger(novelId, novelTitle);
                    }
                } catch (e) {
                    console.error('新创建的initializeConsoleLogger出错:', e);
                }
            };

            // 如果DOM已加载完成，立即执行新函数
            if (document.readyState === 'complete' || document.readyState === 'interactive') {
                console.log('DOM已加载，立即执行新创建的initializeConsoleLogger');
                window.initializeConsoleLogger();
            } else {
                // 否则等待DOM加载完成
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('DOM加载完成，执行新创建的initializeConsoleLogger');
                    window.initializeConsoleLogger();
                });
            }
        }
    }

    // 修复JSON.parse语法错误
    function fixJsonParseErrors() {
        console.log('尝试修复JSON.parse语法错误');

        // 查找所有内联脚本
        const scripts = document.querySelectorAll('script:not([src])');

        scripts.forEach(function(script, index) {
            if (script.textContent) {
                // 检查是否包含JSON.parse
                if (script.textContent.includes('JSON.parse(')) {
                    console.log(`找到包含JSON.parse的脚本 #${index}`);

                    try {
                        // 修复缺少右括号的JSON.parse调用
                        const fixedContent = script.textContent.replace(
                            /JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g,
                            function(match, quote, content) {
                                if (!match.endsWith(')')) {
                                    console.log('修复缺少右括号的JSON.parse调用');
                                    return `JSON.parse(${quote}${content}${quote})`;
                                }
                                return match;
                            }
                        );

                        // 特别处理serverData = JSON.parse调用
                        const serverDataFixed = fixedContent.replace(
                            /serverData\s*=\s*JSON\.parse\((['"])(.+?)(?:\1\s*\))?/g,
                            function(match, quote, content) {
                                if (!match.endsWith(')')) {
                                    console.log('修复serverData = JSON.parse调用');
                                    return `serverData = JSON.parse(${quote}${content}${quote})`;
                                }
                                return match;
                            }
                        );

                        // 如果内容被修改，替换脚本
                        if (serverDataFixed !== script.textContent) {
                            console.log('替换修复后的脚本');
                            try {
                                const newScript = document.createElement('script');
                                // 使用文本节点而不是直接设置textContent，避免解析问题
                                const textNode = document.createTextNode(serverDataFixed);
                                newScript.appendChild(textNode);

                                // 使用安全的替换方法
                                if (script.parentNode) {
                                    script.parentNode.insertBefore(newScript, script);
                                    script.parentNode.removeChild(script);
                                    console.log('脚本替换成功');
                                } else {
                                    console.warn('脚本父节点不存在，无法替换');
                                }
                            } catch (replaceError) {
                                console.error('替换脚本时出错:', replaceError);
                            }
                        }
                    } catch (e) {
                        console.error(`修复脚本 #${index} 时出错:`, e);
                    }
                }

                // 检查是否包含model-select.checked
                if (script.textContent.includes('model-select') && script.textContent.includes('.check')) {
                    console.log(`找到包含model-select.check的脚本 #${index}`);

                    try {
                        // 修复model-select.check为model-select.checked
                        const fixedContent = script.textContent.replace(
                            /document\.getElementById\(['"]model-select['"]\)\.check(?!e)/g,
                            'document.getElementById(\'model-select\').checked'
                        );

                        // 如果内容被修改，替换脚本
                        if (fixedContent !== script.textContent) {
                            console.log('替换修复后的脚本');
                            const newScript = document.createElement('script');
                            newScript.textContent = fixedContent;
                            script.parentNode.replaceChild(newScript, script);
                        }
                    } catch (e) {
                        console.error(`修复脚本 #${index} 时出错:`, e);
                    }
                }
            }
        });
    }

    // 添加全局错误处理
    function addGlobalErrorHandler() {
        console.log('添加全局错误处理');

        window.addEventListener('error', function(event) {
            console.error('捕获到JS错误:', event.message);
            console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);

            // 记录错误到控制台
            const errorMessage = `JS错误: ${event.message} 来源: ${event.filename} 行: ${event.lineno} 列: ${event.colno}`;

            // 使用安全的方式记录错误
            safeLogError(errorMessage);

            // 特别处理container变量重复声明错误
            if (event.message && (
                event.message.includes("Identifier 'container' has already been declared") ||
                event.message.includes("container") && event.message.includes("has already been declared")
            )) {
                console.log('检测到container变量重复声明错误，尝试修复');

                // 尝试修复所有内联脚本中的container变量
                try {
                    fixContainerVariables();
                } catch (e) {
                    console.error('修复container变量时出错:', e);
                }
            }

            // 特别处理novelIdFromTemplate变量重复声明错误
            if (event.message && (
                event.message.includes("Identifier 'novelIdFromTemplate' has already been declared") ||
                event.message.includes("novelIdFromTemplate") && event.message.includes("has already been declared")
            )) {
                console.log('检测到novelIdFromTemplate变量重复声明错误，尝试修复');

                // 尝试修复所有内联脚本中的novelIdFromTemplate变量
                try {
                    fixNovelIdVariables();
                } catch (e) {
                    console.error('修复novelIdFromTemplate变量时出错:', e);
                }
            }

            // 如果是JSON.parse相关错误，尝试修复
            if (event.message && (
                event.message.includes('JSON.parse') ||
                event.message.includes('missing ) after argument list')
            )) {
                console.log('检测到JSON.parse相关错误，尝试修复');
                fixJsonParseErrors();
            }

            // 如果是控制台日志相关错误，尝试修复
            if (event.message && (
                event.message.includes('consoleLogger') ||
                event.message.includes('initConsoleLogger') ||
                event.message.includes('initializeConsoleLogger')
            )) {
                console.log('检测到控制台日志相关错误，尝试修复');
                fixConsoleLogger();
            }

            // 阻止错误传播
            event.preventDefault();
            return true;
        }, true);
    }

    // 修复container变量重复声明问题 - 使用闭包包装脚本
    function fixContainerVariables() {
        console.log('尝试修复container变量重复声明问题 - 使用闭包包装');

        // 查找所有内联脚本
        const scripts = document.querySelectorAll('script:not([src])');

        scripts.forEach(function(script, index) {
            const content = script.textContent || script.innerHTML || '';

            // 跳过空脚本或已经是立即执行函数的脚本
            if (!content.trim() || content.trim().startsWith('(function') || content.trim().startsWith('(()')) {
                return;
            }

            // 检查是否包含变量声明
            if (content.includes('const ') || content.includes('let ') || content.includes('var ')) {
                console.log(`脚本 #${index} 中可能包含变量声明，使用闭包包装`);

                try {
                    // 将脚本内容包装在立即执行函数中
                    const wrappedContent = `
// 九猫安全脚本包装器 - 避免变量污染
(function() {
    try {
${content}
    } catch (e) {
        console.error('脚本执行错误:', e);
    }
})();`;

                    // 创建新脚本并替换
                    try {
                        const newScript = document.createElement('script');

                        // 使用文本节点添加包装后的内容
                        try {
                            const textNode = document.createTextNode(wrappedContent);
                            newScript.appendChild(textNode);
                        } catch (nodeError) {
                            console.error('创建文本节点时出错:', nodeError);
                            // 备用方法 - 直接设置text属性
                            newScript.text = wrappedContent;
                        }

                        // 使用安全的替换方法
                        if (script.parentNode) {
                            // 先插入新脚本
                            script.parentNode.insertBefore(newScript, script);

                            // 延迟移除旧脚本
                            setTimeout(function() {
                                try {
                                    if (script.parentNode) {
                                        script.parentNode.removeChild(script);
                                    }
                                } catch (removeError) {
                                    console.error('移除旧脚本时出错:', removeError);
                                }
                            }, 0);

                            console.log(`脚本 #${index} 已安全包装在闭包中`);
                        } else {
                            console.warn('脚本父节点不存在，无法替换');
                        }
                    } catch (replaceError) {
                        console.error('替换脚本时出错:', replaceError);
                    }
                } catch (e) {
                    console.error(`包装脚本 #${index} 时出错:`, e);
                }
            }
        });
    }

    // 修复novelIdFromTemplate变量重复声明问题 - 使用与fixContainerVariables相同的闭包方法
    function fixNovelIdVariables() {
        console.log('尝试修复模板变量重复声明问题 - 使用闭包包装');

        // 这个函数现在只是fixContainerVariables的别名，因为我们使用了更通用的方法
        fixContainerVariables();
    }

    // 安全记录错误的函数
    function safeLogError(message) {
        try {
            // 创建或获取错误日志容器
            const errorLogContainer = document.getElementById('error-log-container');
            if (errorLogContainer) {
                // 容器已存在，直接添加错误消息
                const errorElement = document.createElement('div');
                errorElement.className = 'error-log-entry';
                errorElement.textContent = `[${new Date().toISOString()}] ${message}`;
                errorLogContainer.appendChild(errorElement);
            } else {
                // 创建错误日志容器
                const errorContainerElement = document.createElement('div');
                errorContainerElement.id = 'error-log-container';
                errorContainerElement.className = 'error-log-container';
                errorContainerElement.style.cssText = 'position: fixed; bottom: 10px; right: 10px; max-width: 80%; max-height: 200px; overflow-y: auto; background: rgba(255,0,0,0.1); border: 1px solid red; padding: 10px; z-index: 9999;';

                // 添加错误消息
                const errorElement = document.createElement('div');
                errorElement.className = 'error-log-entry';
                errorElement.textContent = `[${new Date().toISOString()}] ${message}`;
                errorContainerElement.appendChild(errorElement);

                // 添加到页面
                document.body.appendChild(errorContainerElement);
            }
        } catch (e) {
            // 尝试使用最简单的方式记录错误
            try {
                console.error('Error logging failed:', e);
                console.error('Original error:', message);

                // 尝试使用alert显示错误（仅在开发环境使用，生产环境应避免）
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                    setTimeout(() => {
                        alert(`错误: ${message}\n\n日志记录失败: ${e.message}`);
                    }, 1000);
                }
            } catch (innerError) {
                // 无法记录或显示错误
            }
        }
    }

    // 轮询分析状态
    function pollAnalysisStatus() {
        try {
            // 获取小说ID
            let novelId = null;
            try {
                const novelContainerEl = document.getElementById('novel-container');
                if (novelContainerEl) {
                    novelId = novelContainerEl.getAttribute('data-novel-id');
                }

                if (!novelId) {
                    // 尝试从URL获取
                    const match = window.location.pathname.match(/\/novel\/(\d+)/);
                    if (match && match[1]) {
                        novelId = match[1];
                    }
                }
            } catch (e) {
                console.error('获取小说ID时出错:', e);
            }

            if (!novelId) {
                console.warn('无法获取小说ID，无法轮询分析状态');
                return;
            }

            // 检查是否所有维度都已完成分析
            const statusBadges = document.querySelectorAll('.badge');
            let allCompleted = false;

            // 遍历所有徽章，查找包含"13/13 分析已完成"的徽章
            for (const badge of statusBadges) {
                if (badge.textContent.includes('13/13 分析已完成')) {
                    allCompleted = true;
                    break;
                }
            }

            if (allCompleted) {
                console.log('所有维度已完成分析，添加完成日志');

                // 添加完成日志
                const consoleInner = document.querySelector('.console-inner');
                if (consoleInner) {
                    // 检查是否已经添加了完成日志
                    const completionLogs = consoleInner.querySelectorAll('.log-completion');
                    if (completionLogs.length === 0) {
                        const logEntry = document.createElement('div');
                        logEntry.className = 'console-line log-info log-completion';

                        const timestamp = new Date().toLocaleTimeString();
                        logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> <span class="log-dimension">[系统]</span> 所有维度分析已完成，可以查看各维度的详细结果。`;

                        consoleInner.appendChild(logEntry);
                        consoleInner.scrollTop = consoleInner.scrollHeight;
                    }
                }

                return;
            }

            // 获取分析状态
            fetch(`/api/novel/${novelId}/analysis_status?_=${new Date().getTime()}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && data.success) {
                        // 处理分析状态
                        if (data.logs && Array.isArray(data.logs)) {
                            // 添加新的日志条目
                            const consoleInner = document.querySelector('.console-inner');
                            if (consoleInner) {
                                data.logs.forEach(log => {
                                    if (log.message) {
                                        const logEntry = document.createElement('div');
                                        logEntry.className = `console-line log-${log.level || 'info'}`;

                                        let logContent = '';

                                        // 添加时间戳
                                        const timestamp = log.timestamp ? new Date(log.timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();
                                        logContent += `<span class="log-timestamp">[${timestamp}]</span> `;

                                        // 添加维度信息
                                        if (log.dimension) {
                                            logContent += `<span class="log-dimension">[${log.dimension}]</span> `;
                                        }

                                        // 添加进度信息
                                        if (log.progress) {
                                            logContent += `<span class="log-progress">[${log.progress}%]</span> `;
                                        }

                                        // 添加消息
                                        logContent += log.message;

                                        logEntry.innerHTML = logContent;
                                        consoleInner.appendChild(logEntry);
                                    }
                                });

                                // 滚动到底部
                                consoleInner.scrollTop = consoleInner.scrollHeight;
                            }
                        }

                        // 处理维度状态
                        if (data.dimensions && typeof data.dimensions === 'object') {
                            Object.keys(data.dimensions).forEach(dimension => {
                                const dimensionStatus = data.dimensions[dimension];
                                if (dimensionStatus && dimensionStatus.status) {
                                    // 更新维度状态
                                    updateDimensionStatus(dimension, dimensionStatus);
                                }
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error(`获取分析状态时出错: ${error.message}`);
                });
        } catch (e) {
            console.error(`轮询分析状态时出错: ${e.message}`);
        }
    }

    // 更新维度状态
    function updateDimensionStatus(dimension, status) {
        try {
            // 查找维度行
            const dimensionRow = document.querySelector(`tr[data-dimension="${dimension}"]`);
            if (!dimensionRow) return;

            // 更新状态
            const statusCell = dimensionRow.querySelector('.dimension-status');
            if (statusCell) {
                let statusHtml = '';
                switch (status.status) {
                    case 'completed':
                        statusHtml = '<span class="badge bg-success">已完成</span>';
                        break;
                    case 'in_progress':
                        statusHtml = '<span class="badge bg-primary">分析中</span>';
                        break;
                    case 'pending':
                        statusHtml = '<span class="badge bg-warning">等待中</span>';
                        break;
                    case 'failed':
                        statusHtml = '<span class="badge bg-danger">失败</span>';
                        break;
                    default:
                        statusHtml = '<span class="badge bg-secondary">未知</span>';
                }
                statusCell.innerHTML = statusHtml;
            }

            // 更新进度
            const progressCell = dimensionRow.querySelector('.dimension-progress');
            if (progressCell && status.progress !== undefined) {
                const progressBar = progressCell.querySelector('.progress-bar');
                if (progressBar) {
                    const progress = Math.max(0, Math.min(100, status.progress));
                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                    progressBar.textContent = `${progress}%`;
                }
            }
        } catch (e) {
            console.error(`更新维度状态时出错: ${e.message}`);
        }
    }

    // 执行修复
    function executeAllFixes() {
        console.log('执行所有修复');

        // 添加全局错误处理
        addGlobalErrorHandler();

        // 修复JSON.parse语法错误
        fixJsonParseErrors();

        // 修复控制台日志初始化问题
        fixConsoleLogger();

        // 修复变量重复声明问题 - 使用闭包包装
        fixContainerVariables();

        // 开始轮询分析状态
        setTimeout(() => {
            pollAnalysisStatus();
            // 设置定期轮询
            setInterval(pollAnalysisStatus, 3000);
        }, 1000);
    }

    // 导出到全局命名空间，以便其他脚本可以调用
    window.executeAllFixes = executeAllFixes;
    window.fixContainerVariables = fixContainerVariables;

    // 当DOM加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', executeAllFixes);
    } else {
        // 如果DOM已加载完成，立即执行修复
        executeAllFixes();
    }
})();
