# Nine Cats Novel Analysis System - Analysis Process Recording Patch
# Place this file in the src/services directory and import it in analysis_service.py

import logging
import json
import time
from datetime import datetime
from src.db.connection import Session
from src.models.analysis_process import AnalysisProcess

logger = logging.getLogger(__name__)

class ProcessRecorder:
    """Analysis process recorder for recording detailed information about the analysis process"""

    @staticmethod
    def record_process(novel_id, dimension, block_index, total_blocks, stage,
                      input_text=None, output_text=None, prompt=None,
                      api_request=None, api_response=None,
                      processing_time=None, tokens=None,
                      is_successful=True, error_message=None,
                      result_id=None, metadata=None):
        """
        Record an analysis process

        Args:
            novel_id: Novel ID
            dimension: Analysis dimension
            block_index: Block index
            total_blocks: Total number of blocks
            stage: Processing stage
            input_text: Input text
            output_text: Output text
            prompt: Prompt used
            api_request: API request
            api_response: API response
            processing_time: Processing time (milliseconds)
            tokens: Number of tokens used
            is_successful: Whether the process was successful
            error_message: Error message if any
            result_id: Analysis result ID
            metadata: Additional metadata
        """
        # Check if detailed process recording is enabled
        if not ProcessRecorder.is_process_recording_enabled():
            logger.debug(f"Detailed process recording is not enabled, skipping record")
            return None

        try:
            session = Session()

            # Create analysis process record
            process = AnalysisProcess(
                novel_id=novel_id,
                dimension=dimension,
                block_index=block_index,
                total_blocks=total_blocks,
                processing_stage=stage,
                result_id=result_id,
                input_text=input_text,
                output_text=output_text,
                prompt_used=prompt,
                api_request=api_request,
                api_response=api_response,
                processing_time=processing_time,
                tokens_used=tokens,
                is_successful=is_successful,
                error_message=error_message,
                metadata=metadata or {}
            )

            session.add(process)
            session.commit()

            process_id = process.id
            logger.debug(f"Recorded analysis process: ID={process_id}, novel_id={novel_id}, dimension={dimension}, stage={stage}")

            session.close()
            return process_id

        except Exception as e:
            logger.error(f"Error recording analysis process: {str(e)}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return None

    @staticmethod
    def is_process_recording_enabled():
        """Check if detailed process recording is enabled"""
        import os
        return os.environ.get('ENABLE_DETAILED_PROCESS_RECORDING', 'False').lower() == 'true'

    @staticmethod
    def record_init(novel_id, dimension, input_text=None, metadata=None):
        """Record initialization stage"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            stage='init',
            input_text=input_text,
            metadata=metadata
        )

    @staticmethod
    def record_chunk_analysis(novel_id, dimension, block_index, total_blocks,
                             input_text, output_text=None, prompt=None,
                             api_request=None, api_response=None,
                             processing_time=None, tokens=None,
                             is_successful=True, error_message=None):
        """Record chunk analysis stage"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=block_index,
            total_blocks=total_blocks,
            stage='chunk_analysis',
            input_text=input_text,
            output_text=output_text,
            prompt=prompt,
            api_request=api_request,
            api_response=api_response,
            processing_time=processing_time,
            tokens=tokens,
            is_successful=is_successful,
            error_message=error_message
        )

    @staticmethod
    def record_combine(novel_id, dimension, input_chunks, output_text=None,
                      processing_time=None, is_successful=True, error_message=None):
        """Record result combination stage"""
        # Convert input chunks to string
        if isinstance(input_chunks, list):
            input_text = json.dumps(input_chunks, ensure_ascii=False)
        else:
            input_text = str(input_chunks)

        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            stage='combine',
            input_text=input_text,
            output_text=output_text,
            processing_time=processing_time,
            is_successful=is_successful,
            error_message=error_message
        )

    @staticmethod
    def record_finalize(novel_id, dimension, result_id, input_text=None,
                       output_text=None, processing_time=None,
                       is_successful=True, error_message=None):
        """Record finalization stage"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            stage='finalize',
            result_id=result_id,
            input_text=input_text,
            output_text=output_text,
            processing_time=processing_time,
            is_successful=is_successful,
            error_message=error_message
        )

    @staticmethod
    def record_error(novel_id, dimension, stage, error_message,
                    block_index=0, total_blocks=1, input_text=None):
        """Record error"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=block_index,
            total_blocks=total_blocks,
            stage=stage,
            input_text=input_text,
            is_successful=False,
            error_message=error_message
        )

# Export ProcessRecorder class
__all__ = ['ProcessRecorder']
