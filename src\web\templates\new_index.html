{% extends "new_base.html" %}

{% block title %}首页 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 主要内容区域 -->
    <div class="col-lg-8">
        <!-- 欢迎卡片 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-cat fa-3x text-primary me-3"></i>
                    <h2 class="card-title mb-0">欢迎使用九猫小说分析系统</h2>
                </div>
                <p class="lead">九猫是一款专为作家和编辑设计的智能小说分析工具，基于先进的AI技术，提供全方位的文本分析服务。</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-start mt-4">
                    <a href="/upload" class="btn btn-primary btn-lg px-4 me-md-2">
                        <i class="fas fa-upload me-2"></i>开始分析
                    </a>
                    <a href="/novels" class="btn btn-outline-secondary btn-lg px-4">
                        <i class="fas fa-book me-2"></i>查看小说
                    </a>
                </div>
            </div>
        </div>

        <!-- 功能特点卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-star me-2"></i>系统特点</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-chart-line fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>多维度分析</h4>
                                <p>提供13个专业维度的深度分析，全方位评估小说质量。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-brain fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>AI驱动</h4>
                                <p>采用先进的AI模型，提供专业水准的文学分析。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-tachometer-alt fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>高效处理</h4>
                                <p>优化的处理流程，快速分析长篇小说文本。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-lock fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>安全可靠</h4>
                                <p>本地部署，确保您的创作内容安全保密。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析维度卡片 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-cubes me-2"></i>分析维度</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="dimension-item">
                            <span class="dimension-badge">语言风格</span>
                            <p>分析作者的用词、修辞手法和表达特点。</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="dimension-item">
                            <span class="dimension-badge">节奏与节奏</span>
                            <p>评估故事节奏的快慢变化和情感起伏。</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="dimension-item">
                            <span class="dimension-badge">结构分析</span>
                            <p>分析小说的整体结构和章节安排。</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="dimension-item">
                            <span class="dimension-badge">句式变化</span>
                            <p>评估句子长度、复杂度和多样性。</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="dimension-item">
                            <span class="dimension-badge">段落长度</span>
                            <p>分析段落长度分布和变化规律。</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="dimension-item">
                            <span class="dimension-badge">视角转换</span>
                            <p>识别叙事视角的变化和转换技巧。</p>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="/dimensions" class="btn btn-outline-primary">查看全部13个分析维度</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 快速入门卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-rocket me-2"></i>快速入门</h3>
            </div>
            <div class="card-body">
                <ol class="list-group list-group-numbered mb-3">
                    <li class="list-group-item d-flex">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">上传小说</div>
                            上传TXT文件或直接粘贴文本内容
                        </div>
                    </li>
                    <li class="list-group-item d-flex">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">选择分析维度</div>
                            从13个专业维度中选择需要分析的方面
                        </div>
                    </li>
                    <li class="list-group-item d-flex">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">查看分析结果</div>
                            获取详细的分析报告和改进建议
                        </div>
                    </li>
                </ol>
                <a href="/help" class="btn btn-outline-primary w-100">查看详细教程</a>
            </div>
        </div>

        <!-- 最近分析卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-history me-2"></i>最近分析</h3>
            </div>
            <div class="card-body">
                {% if novels %}
                <ul class="list-group">
                    {% for novel in novels[:5] %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="text-decoration-none">
                            {{ novel.title }}
                        </a>
                        <span class="badge bg-primary rounded-pill">{{ novel.created_at.strftime('%m-%d') }}</span>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-center text-muted">暂无分析记录</p>
                {% endif %}
            </div>
        </div>

        <!-- 系统状态卡片 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-server me-2"></i>系统状态</h3>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>内存使用</span>
                    <span id="memory-usage">加载中...</span>
                </div>
                <div class="progress mb-3">
                    <div id="memory-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>API调用</span>
                    <span id="api-calls">加载中...</span>
                </div>
                <div class="progress mb-3">
                    <div id="api-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <a href="/system_monitor" class="btn btn-outline-primary w-100">查看详细状态</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 系统状态更新
    function updateSystemStatus() {
        fetch('/api/system/status')
            .then(response => response.json())
            .then(data => {
                // 更新内存使用
                const memoryUsage = document.getElementById('memory-usage');
                const memoryBar = document.getElementById('memory-bar');
                memoryUsage.textContent = `${data.memory.used_percent}%`;
                memoryBar.style.width = `${data.memory.used_percent}%`;
                
                // 更新API调用
                const apiCalls = document.getElementById('api-calls');
                const apiBar = document.getElementById('api-bar');
                apiCalls.textContent = `${data.api.used}/${data.api.limit}`;
                const apiPercent = (data.api.used / data.api.limit) * 100;
                apiBar.style.width = `${apiPercent}%`;
            })
            .catch(error => {
                console.error('获取系统状态失败:', error);
            });
    }
    
    // 页面加载完成后更新系统状态
    document.addEventListener('DOMContentLoaded', function() {
        updateSystemStatus();
        // 每60秒更新一次
        setInterval(updateSystemStatus, 60000);
    });
</script>
{% endblock %}
