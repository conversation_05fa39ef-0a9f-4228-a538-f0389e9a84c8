@echo off
echo 九猫小说分析系统 - 静态文件修复工具
echo 此工具将修复"Failed to load resource: the server responded with a status of 404 (NOT FOUND)"错误
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未检测到Python。请确保Python已安装并添加到PATH环境变量中。
    pause
    exit /b 1
)

echo 正在修复静态文件...
python fix_static_files.py

if %errorlevel% neq 0 (
    echo 修复过程中出现错误，请查看日志文件了解详情。
) else (
    echo 修复完成！
    echo 请重新启动九猫系统以应用更改。
)

pause
