/**
 * 九猫 - 强制添加维度按钮脚本
 * 为分析维度选择对话框添加一个"强制显示维度"按钮
 * 版本: 1.0.0
 */

(function() {
    console.log('[九猫维度按钮] 脚本已加载');

    // 维度列表 - 与config.py中的ANALYSIS_DIMENSIONS保持一致
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "chapter_outline", name: "章节大纲" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" },
        { key: "outline_analysis", name: "大纲分析" },
        { key: "popular_tropes", name: "热梗统计" }
    ];

    // 添加强制显示维度按钮
    function addForceShowDimensionsButton() {
        const analyzeModals = document.querySelectorAll('.modal');

        analyzeModals.forEach(modal => {
            // 检查是否是分析维度选择对话框
            const modalTitle = modal.querySelector('.modal-title');
            if (modalTitle && modalTitle.textContent && modalTitle.textContent.includes('选择分析维度')) {
                // 检查是否已经添加了按钮
                if (modal.querySelector('.force-show-dimensions-btn')) {
                    return;
                }

                // 查找模态框底部操作区域
                const modalFooter = modal.querySelector('.modal-footer');
                if (!modalFooter) {
                    return;
                }

                // 创建按钮
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-warning force-show-dimensions-btn';
                button.textContent = '强制显示13个维度';
                button.style.marginRight = 'auto'; // 放在左侧

                // 添加点击事件
                button.addEventListener('click', function() {
                    // 调用维度选择修复函数
                    if (window.dimensionSelectionFix && typeof window.dimensionSelectionFix.fixNow === 'function') {
                        window.dimensionSelectionFix.fixNow();
                        button.textContent = '已添加13个维度';
                        button.className = 'btn btn-success force-show-dimensions-btn';

                        // 禁用按钮
                        setTimeout(() => {
                            button.disabled = true;
                        }, 1000);
                    } else {
                        // 如果修复函数不可用，则手动添加维度
                        forceAddDimensions(modal);
                        button.textContent = '已手动添加维度';
                        button.className = 'btn btn-success force-show-dimensions-btn';

                        // 禁用按钮
                        setTimeout(() => {
                            button.disabled = true;
                        }, 1000);
                    }
                });

                // 添加到模态框底部
                modalFooter.insertBefore(button, modalFooter.firstChild);

                console.log('[九猫维度按钮] 已添加强制显示维度按钮');
            }
        });
    }

    // 手动添加维度
    function forceAddDimensions(modal) {
        console.log('[九猫维度按钮] 手动添加维度...');

        // 查找模态框内容区域
        const modalBody = modal.querySelector('.modal-body');
        if (!modalBody) return;

        // 创建维度选项容器
        const dimensionsContainer = document.createElement('div');
        dimensionsContainer.id = 'dimensions-container';
        dimensionsContainer.className = 'mb-3';

        // 添加标题
        const title = document.createElement('h5');
        title.className = 'mb-3';
        title.textContent = '所有13个维度:';
        dimensionsContainer.appendChild(title);

        // 添加所有维度选项
        DIMENSIONS.forEach((dimension, index) => {
            // 创建新的选择项
            const checkboxContainer = document.createElement('div');
            checkboxContainer.className = 'form-check mb-2';

            const checkbox = document.createElement('input');
            checkbox.className = 'form-check-input dimension-checkbox';
            checkbox.type = 'checkbox';
            checkbox.name = 'dimensions';
            checkbox.value = dimension.key;
            checkbox.id = `dimension-manual-${index}`;
            checkbox.checked = true; // 默认选中

            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.setAttribute('for', `dimension-manual-${index}`);
            label.textContent = dimension.name;

            // 添加到容器中
            checkboxContainer.appendChild(checkbox);
            checkboxContainer.appendChild(label);
            dimensionsContainer.appendChild(checkboxContainer);
        });

        // 添加到模态框内容区域
        modalBody.appendChild(dimensionsContainer);

        console.log(`[九猫维度按钮] 已手动添加${DIMENSIONS.length}个维度选项`);
    }

    // 监听DOM变化
    function setupObserver() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 检查添加的节点中是否有模态框
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.classList && node.classList.contains('modal')) {
                            // 延迟执行，确保模态框完全加载
                            setTimeout(addForceShowDimensionsButton, 300);
                        }
                    });
                }

                // 检查class变化，可能是模态框打开
                if (mutation.type === 'attributes' &&
                    mutation.attributeName === 'class' &&
                    mutation.target.classList &&
                    mutation.target.classList.contains('modal') &&
                    mutation.target.classList.contains('show')) {
                    // 延迟执行，确保模态框完全加载
                    setTimeout(addForceShowDimensionsButton, 300);
                }
            });
        });

        // 配置观察器
        const config = { childList: true, subtree: true, attributes: true, attributeFilter: ['class'] };

        // 开始观察
        observer.observe(document.body, config);

        console.log('[九猫维度按钮] DOM变化监听已设置');
    }

    // 初始化
    function initialize() {
        console.log('[九猫维度按钮] 初始化中...');

        // 尝试立即添加按钮
        addForceShowDimensionsButton();

        // 设置监听器
        setupObserver();

        console.log('[九猫维度按钮] 初始化完成');
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出到全局命名空间，方便调试
    window.dimensionAddButton = {
        addButton: addForceShowDimensionsButton,
        forceAddDimensions: forceAddDimensions
    };
})();