{% extends "v3.1/base.html" %}

{% block title %}错误 {{ error_code }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow-sm">
            <div class="card-body text-center py-5">
                <h1 class="display-1 text-danger mb-4">{{ error_code }}</h1>
                <h2 class="mb-4">{{ error_message }}</h2>
                <p class="lead text-muted mb-4">
                    {% if error_code == 404 %}
                        您访问的页面不存在或已被移除。
                    {% elif error_code == 500 %}
                        服务器内部错误，请稍后再试。
                    {% else %}
                        发生了一个错误，请稍后再试。
                    {% endif %}
                </p>
                <div class="mt-4">
                    <a href="{{ url_for('v3_1.index') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>返回首页
                    </a>
                </div>
            </div>
        </div>

        {% if error_details %}
        <div class="card mt-4 shadow-sm">
            <div class="card-header bg-danger text-white">
                <h3 class="card-title mb-0">错误详情</h3>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded"><code>{{ error_details }}</code></pre>
            </div>
        </div>
        {% endif %}

        <div class="card mt-4 shadow-sm">
            <div class="card-header bg-info text-white">
                <h3 class="card-title mb-0">可能的解决方法</h3>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    {% if error_code == 404 %}
                        <li class="list-group-item">检查您输入的URL是否正确</li>
                        <li class="list-group-item">返回首页，使用导航菜单访问所需功能</li>
                        <li class="list-group-item">如果您是通过书签访问，可能该页面已被移除或重命名</li>
                    {% elif error_code == 500 %}
                        <li class="list-group-item">刷新页面，重试操作</li>
                        <li class="list-group-item">清除浏览器缓存后重试</li>
                        <li class="list-group-item">稍后再试，服务器可能暂时不可用</li>
                    {% else %}
                        <li class="list-group-item">刷新页面，重试操作</li>
                        <li class="list-group-item">返回首页，使用导航菜单访问所需功能</li>
                        <li class="list-group-item">如果问题持续存在，请联系系统管理员</li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
