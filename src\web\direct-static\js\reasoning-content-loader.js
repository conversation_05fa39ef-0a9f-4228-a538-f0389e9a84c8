/**
 * 九猫 - 推理过程加载器
 * 用于从API获取推理过程并显示在页面上
 * 版本: 1.1.0 - 修复推理过程显示错误
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('推理过程加载器已加载');

    // 加载推理过程
    function loadReasoningContent(novelId, dimension, containerId = 'reasoningContent') {
        console.log(`开始加载推理过程: novel_id=${novelId}, dimension=${dimension}`);

        // 获取容器元素
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`找不到推理过程容器: ${containerId}`);
            return;
        }

        // 显示加载中状态
        container.innerHTML = `
            <div class="text-center my-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2">正在加载推理过程，请稍候...</p>
            </div>
        `;

        // 构建API URL
        const apiUrl = `/api/novel/${novelId}/analysis/${dimension}/reasoning_content`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                // 特殊处理404错误，将其视为"暂无数据"而非错误
                if (response.status === 404) {
                    return { success: false, error: '暂无推理过程数据', status: 404 };
                }

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                if (data.success && data.reasoning_content) {
                    // 验证是否真的是推理过程而不是分析结果
                    const content = data.reasoning_content;

                    // 检查是否是分析结果而不是推理过程
                    const resultMarkers = [
                        '### **一、',
                        '### 一、',
                        '## 一、',
                        '# 一、',
                        '---\n### **',
                        '## **',
                        '# **',
                        '### **总结',
                        '## **总结',
                        '# **总结',
                        '句式变化分析',
                        '重点探讨',
                        '以下是对该小说文本的',
                        '这部小说文本展现出了',
                        '风格特征：',
                        '主题架构：',
                        '写作技法：',
                        '人物塑造：',
                        '文化符码：',
                        '结构创新：',
                        '节奏控制分析',
                        '### 二、',
                        '### 三、',
                        '### 四、',
                        '### 五、'
                    ];

                    // 检查内容是否是推理过程
                    const processMarkers = [
                        '好的，我现在要',
                        '首先，我需要',
                        '我将分析',
                        '我需要分析',
                        '嗯，用户让我',
                        '下面我来分析',
                        '让我来分析',
                        '我会按照以下步骤',
                        '我将按照以下步骤',
                        '我将从以下几个方面',
                        '我需要从以下几个方面',
                        '我将逐步分析',
                        '好的，我现在需要',
                        '我需要通读一遍',
                        '我需要先了解',
                        '我会先阅读',
                        '我先阅读一遍',
                        '我需要分析用户提供的',
                        '开始分析这段小说',
                        '我需要全面考虑',
                        '我得仔细阅读',
                        '作为文学分析专家',
                        '现在需要把这些思考整理成',
                        '可能还需要考虑',
                        '另外，需要关注'
                    ];

                    let isAnalysisResult = false;
                    for (const marker of resultMarkers) {
                        if (content.includes(marker)) {
                            isAnalysisResult = true;
                            break;
                        }
                    }

                    let isReasoningProcess = false;
                    for (const marker of processMarkers) {
                        if (content.includes(marker)) {
                            isReasoningProcess = true;
                            break;
                        }
                    }

                    // 根据内容类型显示不同的提示
                    if (isReasoningProcess && !isAnalysisResult) {
                        // 确认是推理过程，正常显示
                        container.innerHTML = `<pre class="reasoning-text">${escapeHtml(content)}</pre>`;
                        console.log('推理过程加载成功');
                    } else if (isAnalysisResult) {
                        // 这可能是分析结果而不是推理过程，显示警告
                        container.innerHTML = `
                            <div class="alert alert-danger mb-3">
                                <p><i class="fas fa-exclamation-triangle"></i> <strong>错误：</strong> 系统错误地将分析结果显示为推理过程。</p>
                                <p class="small">这是因为系统无法正确区分推理过程和分析结果。推理过程应该包含AI的思考过程，而不是格式化的分析报告。</p>
                                <p class="small">请联系管理员修复此问题。</p>
                            </div>
                            <pre class="reasoning-text">${escapeHtml(content)}</pre>
                        `;
                        console.warn('警告：API返回的是分析结果而不是推理过程');
                    } else {
                        // 无法确定，但仍然显示内容
                        container.innerHTML = `
                            <div class="alert alert-info mb-3">
                                <p><i class="fas fa-info-circle"></i> <strong>提示：</strong> 无法确定内容类型，但仍然显示获取到的内容。</p>
                            </div>
                            <pre class="reasoning-text">${escapeHtml(content)}</pre>
                        `;
                        console.log('推理过程加载成功，但无法确定内容类型');
                    }
                } else {
                    // 显示友好的提示信息（不是错误）
                    container.innerHTML = `
                        <div class="alert alert-info">
                            <p><i class="fas fa-info-circle"></i> 暂无推理过程数据</p>
                            <p class="small text-muted mt-2">可能的原因：</p>
                            <ul class="small text-muted">
                                <li>该分析是在启用推理过程记录功能之前进行的</li>
                                <li>分析过程中未生成推理过程</li>
                                <li>推理过程数据已被清理</li>
                                <li>分析尚未完成，请稍后再查看</li>
                            </ul>
                        </div>
                    `;
                    // 使用info级别日志而非警告或错误
                    console.info('暂无推理过程数据', data.status === 404 ? '(资源不存在)' : (data.error || ''));
                }
            })
            .catch(error => {
                // 显示错误信息，但使用更友好的样式
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <p><i class="fas fa-exclamation-circle"></i> 无法加载推理过程</p>
                        <p class="small text-muted mt-2">系统将在稍后自动重试，您也可以刷新页面重试。</p>
                    </div>
                `;
                // 记录错误但不在控制台显示为错误
                console.warn('加载推理过程时遇到问题:', error.message);

                // 30秒后自动重试
                setTimeout(() => {
                    console.info('自动重试加载推理过程...');
                    loadReasoningContent(novelId, dimension, containerId);
                }, 30000);
            });
    }

    // HTML转义函数
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 在页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('推理过程加载器初始化');

        // 查找所有需要加载推理过程的容器
        const containers = document.querySelectorAll('[data-reasoning-container]');

        containers.forEach(container => {
            const novelId = container.getAttribute('data-novel-id');
            const dimension = container.getAttribute('data-dimension');

            if (novelId && dimension) {
                // 设置容器ID
                const containerId = container.id || `reasoningContent_${Math.random().toString(36).substr(2, 9)}`;
                if (!container.id) {
                    container.id = containerId;
                }

                // 加载推理过程
                loadReasoningContent(novelId, dimension, containerId);
            }
        });
    });

    // 导出函数供其他模块使用
    window.reasoningContentLoader = {
        loadReasoningContent: loadReasoningContent
    };
})();
