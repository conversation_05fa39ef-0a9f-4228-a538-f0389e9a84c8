/**
 * 九猫小说分析写作系统v3.5 - 控制台JavaScript
 */

// 全局变量
let logUpdateInterval = null;
let isPaused = false;
let selectedTemplateId = null;
let currentPresetId = null;

// 在DOM加载完成后执行
$(document).ready(function() {
    console.log('九猫控制台v3.5已加载');
    
    // 初始化控制台
    initConsole();
    
    // 绑定事件
    bindEvents();
    
    // 启动日志更新
    startLogUpdates();
});

/**
 * 初始化控制台
 */
function initConsole() {
    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const templateId = urlParams.get('template');
    
    if (templateId) {
        // 选择指定的参考蓝本
        selectTemplate(templateId);
    }
    
    // 初始化Markdown渲染
    marked.setOptions({
        breaks: true,
        gfm: true
    });
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 绑定日志相关按钮
    bindLogButtons();
    
    // 绑定分析展示相关事件
    bindAnalysisEvents();
    
    // 绑定知识库相关事件
    bindKnowledgeEvents();
    
    // 绑定自动写作相关事件
    bindWritingEvents();
}

/**
 * 绑定日志相关按钮
 */
function bindLogButtons() {
    // 清空日志按钮
    $('#clearLogsBtn').click(function() {
        $('#logContainer').empty();
        addLogEntry('info', '日志已清空');
    });
    
    // 暂停/继续更新按钮
    $('#pauseLogsBtn').click(function() {
        isPaused = !isPaused;
        if (isPaused) {
            $(this).html('<i class="fas fa-play me-1"></i>继续更新');
            addLogEntry('warn', '日志更新已暂停');
        } else {
            $(this).html('<i class="fas fa-pause me-1"></i>暂停更新');
            addLogEntry('info', '日志更新已继续');
        }
    });
    
    // 日志搜索
    $('#logSearch').on('input', function() {
        applyLogFilters();
    });
    
    // 清空搜索按钮
    $('#clearLogSearchBtn').click(function() {
        $('#logSearch').val('');
        applyLogFilters();
    });
}

/**
 * 绑定分析展示相关事件
 */
function bindAnalysisEvents() {
    // 参考蓝本点击事件
    $('.template-item').click(function() {
        const templateId = $(this).data('template-id');
        selectTemplate(templateId);
    });
}

/**
 * 绑定知识库相关事件
 */
function bindKnowledgeEvents() {
    // 知识库项点击事件
    $('.knowledge-item').click(function() {
        const templateId = $(this).data('template-id');
        loadKnowledgeContent(templateId);
    });
    
    // 读取蓝本按钮
    $('#readTemplateBtn').click(function() {
        if (!selectedTemplateId) {
            addLogEntry('warn', '请先选择一个参考蓝本');
            return;
        }
        
        readTemplate(selectedTemplateId);
    });
    
    // 导出知识按钮
    $('#exportKnowledgeBtn').click(function() {
        if (!selectedTemplateId) {
            addLogEntry('warn', '请先选择一个参考蓝本');
            return;
        }
        
        exportKnowledge(selectedTemplateId);
    });
    
    // 展开查看按钮
    $('#expandViewBtn').click(function() {
        if (!selectedTemplateId) {
            addLogEntry('warn', '请先选择一个参考蓝本');
            return;
        }
        
        window.location.href = `/v3.5/preset/${selectedTemplateId}/templates`;
    });
    
    // 知识库搜索
    $('#knowledgeSearch').on('input', function() {
        const searchText = $(this).val().toLowerCase();
        
        $('.knowledge-item').each(function() {
            const itemText = $(this).text().toLowerCase();
            if (searchText === '' || itemText.includes(searchText)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
    
    // 清空知识库搜索按钮
    $('#clearKnowledgeSearchBtn').click(function() {
        $('#knowledgeSearch').val('');
        $('.knowledge-item').show();
    });
}

/**
 * 绑定自动写作相关事件
 */
function bindWritingEvents() {
    // 开始写作按钮
    $('#startWritingBtn').click(function() {
        const templateId = $('#templateSelect').val();
        const prompt = $('#writingPrompt').val();
        
        if (!templateId) {
            addLogEntry('warn', '请选择预设模板');
            return;
        }
        
        startAutoWriting(templateId, prompt);
    });
    
    // 与原文对比按钮
    $('#compareWithOriginalBtn').click(function() {
        const templateId = $('#templateSelect').val();
        const prompt = $('#writingPrompt').val();
        
        if (!templateId) {
            addLogEntry('warn', '请选择预设模板');
            return;
        }
        
        // 跳转到文本对比页面
        window.location.href = `/v3.5/text_comparison?template=${templateId}&prompt=${encodeURIComponent(prompt)}`;
    });
}

/**
 * 启动日志更新
 */
function startLogUpdates() {
    // 每35秒更新一次日志
    logUpdateInterval = setInterval(function() {
        if (!isPaused) {
            updateLogs();
        }
    }, 35000);
}

/**
 * 更新日志
 */
function updateLogs() {
    // 添加一条系统日志
    const now = new Date();
    const timeString = now.toTimeString().split(' ')[0];
    addLogEntry('info', '系统正常运行中...');
}

/**
 * 添加日志条目
 * @param {string} type 日志类型：info, success, warn, error
 * @param {string} message 日志消息
 */
function addLogEntry(type, message) {
    const now = new Date();
    const timeString = now.toTimeString().split(' ')[0];
    
    const logLine = $('<div class="log-line"></div>');
    logLine.append(`<span class="log-timestamp">${timeString}</span>`);
    logLine.append(`<span class="log-${type}">${message}</span>`);
    
    $('#logContainer').append(logLine);
    
    // 滚动到底部
    const logContainer = document.getElementById('logContainer');
    logContainer.scrollTop = logContainer.scrollHeight;
}

/**
 * 应用日志过滤器
 */
function applyLogFilters() {
    const searchText = $('#logSearch').val().toLowerCase();
    
    $('.log-line').each(function() {
        const lineText = $(this).text().toLowerCase();
        if (searchText === '' || lineText.includes(searchText)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

/**
 * 选择参考蓝本
 * @param {string} templateId 参考蓝本ID
 */
function selectTemplate(templateId) {
    // 更新选中状态
    $('.template-item').removeClass('active');
    $(`.template-item[data-template-id="${templateId}"]`).addClass('active');
    
    // 更新全局变量
    selectedTemplateId = templateId;
    
    // 更新下拉框
    $('#templateSelect').val(templateId);
    
    // 加载分析结果
    loadAnalysisResult(templateId);
    
    // 加载知识库内容
    loadKnowledgeContent(templateId);
    
    // 添加日志
    addLogEntry('info', `已选择参考蓝本ID: ${templateId}`);
}

/**
 * 加载分析结果
 * @param {string} templateId 参考蓝本ID
 */
function loadAnalysisResult(templateId) {
    $('#analysisResult').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">加载分析结果中...</p></div>');
    
    // 调用API获取分析结果
    $.ajax({
        url: `/v3.5/api/novels/${templateId}/analysis`,
        type: 'GET',
        success: function(response) {
            if (response.success && response.dimensions) {
                // 渲染分析结果
                renderAnalysisResult(response.dimensions);
                addLogEntry('success', '成功加载分析结果');
            } else {
                $('#analysisResult').html('<div class="alert alert-danger">加载分析结果失败</div>');
                addLogEntry('error', '加载分析结果失败: ' + (response.error || '未知错误'));
            }
        },
        error: function(xhr) {
            $('#analysisResult').html('<div class="alert alert-danger">加载分析结果失败</div>');
            addLogEntry('error', `加载分析结果失败: ${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 渲染分析结果
 * @param {Array} dimensions 分析维度数组
 */
function renderAnalysisResult(dimensions) {
    let html = '<div class="list-group">';
    
    dimensions.forEach(function(dimension) {
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1">${dimension.name}</h5>
                    <small>${dimension.completed ? '已完成' : '未完成'}</small>
                </div>
                <p class="mb-1">${dimension.summary || '暂无分析结果'}</p>
                <small>
                    <a href="#" class="view-analysis" data-dimension="${dimension.key}">查看详情</a>
                </small>
            </div>
        `;
    });
    
    html += '</div>';
    
    $('#analysisResult').html(html);
    
    // 绑定查看详情事件
    $('.view-analysis').click(function(e) {
        e.preventDefault();
        const dimension = $(this).data('dimension');
        viewAnalysisDetail(selectedTemplateId, dimension);
    });
}

/**
 * 查看分析详情
 * @param {string} templateId 参考蓝本ID
 * @param {string} dimension 分析维度
 */
function viewAnalysisDetail(templateId, dimension) {
    $('#analysisResult').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">加载分析详情中...</p></div>');
    
    // 调用API获取分析详情
    $.ajax({
        url: `/v3.5/api/novels/${templateId}/analysis/${dimension}`,
        type: 'GET',
        success: function(response) {
            if (response.success && response.result) {
                // 渲染分析详情
                const content = marked.parse(response.result.content || '暂无分析结果');
                $('#analysisResult').html(`
                    <div class="mb-3">
                        <button class="btn btn-outline-primary btn-sm back-to-list">
                            <i class="fas fa-arrow-left me-1"></i>返回列表
                        </button>
                    </div>
                    <div class="markdown-content">${content}</div>
                `);
                
                // 绑定返回列表事件
                $('.back-to-list').click(function() {
                    loadAnalysisResult(templateId);
                });
                
                addLogEntry('info', `已加载维度 ${dimension} 的分析详情`);
            } else {
                $('#analysisResult').html('<div class="alert alert-danger">加载分析详情失败</div>');
                addLogEntry('error', '加载分析详情失败: ' + (response.error || '未知错误'));
            }
        },
        error: function(xhr) {
            $('#analysisResult').html('<div class="alert alert-danger">加载分析详情失败</div>');
            addLogEntry('error', `加载分析详情失败: ${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 加载知识库内容
 * @param {string} templateId 参考蓝本ID
 */
function loadKnowledgeContent(templateId) {
    $('#knowledgeContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">加载知识库内容中...</p></div>');
    
    // 更新选中状态
    $('.knowledge-item').removeClass('active');
    $(`.knowledge-item[data-template-id="${templateId}"]`).addClass('active');
    
    // 调用API获取知识库内容
    $.ajax({
        url: `/v3.5/api/novels/${templateId}/preset`,
        type: 'GET',
        success: function(response) {
            if (response.success && response.preset) {
                // 渲染知识库内容
                const content = marked.parse(response.preset.content || '暂无预设内容');
                $('#knowledgeContent').html(`<div class="markdown-content">${content}</div>`);
                addLogEntry('success', '成功加载知识库内容');
            } else {
                $('#knowledgeContent').html('<div class="alert alert-warning">该参考蓝本尚未生成预设模板，请点击"读取蓝本"按钮生成</div>');
                addLogEntry('warn', '参考蓝本尚未生成预设模板');
            }
        },
        error: function(xhr) {
            $('#knowledgeContent').html('<div class="alert alert-danger">加载知识库内容失败</div>');
            addLogEntry('error', `加载知识库内容失败: ${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 读取参考蓝本
 * @param {string} templateId 参考蓝本ID
 */
function readTemplate(templateId) {
    $('#knowledgeContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在读取参考蓝本...</p></div>');
    addLogEntry('info', '开始读取参考蓝本...');
    
    // 调用API读取参考蓝本
    $.ajax({
        url: `/v3.5/api/novels/${templateId}/read_template`,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({}),
        success: function(response) {
            if (response.success) {
                $('#knowledgeContent').html('<div class="alert alert-success">参考蓝本读取成功，正在生成预设模板...</div>');
                addLogEntry('success', '参考蓝本读取成功，正在生成预设模板...');
                
                // 延迟加载知识库内容
                setTimeout(function() {
                    loadKnowledgeContent(templateId);
                }, 2000);
            } else {
                $('#knowledgeContent').html(`<div class="alert alert-danger">读取参考蓝本失败: ${response.error || '未知错误'}</div>`);
                addLogEntry('error', `读取参考蓝本失败: ${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#knowledgeContent').html('<div class="alert alert-danger">读取参考蓝本失败</div>');
            addLogEntry('error', `读取参考蓝本失败: ${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 导出知识
 * @param {string} templateId 参考蓝本ID
 */
function exportKnowledge(templateId) {
    addLogEntry('info', '开始导出知识...');
    
    // 调用API导出知识
    $.ajax({
        url: `/v3.5/api/novels/${templateId}/export_preset`,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({}),
        success: function(response) {
            if (response.success && response.preset) {
                addLogEntry('success', `预设模板导出成功：${response.preset.title}`);
            } else {
                addLogEntry('error', `预设模板导出失败：${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            addLogEntry('error', `预设模板导出失败：${xhr.status} ${xhr.statusText}`);
        }
    });
}

/**
 * 开始自动写作
 * @param {string} templateId 预设模板ID
 * @param {string} prompt 写作提示
 */
function startAutoWriting(templateId, prompt) {
    $('#writingResult').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">生成中，请稍候...</p></div>');
    
    // 调用API开始自动写作
    $.ajax({
        url: '/v3.5/api/auto_write',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            template_id: templateId,
            prompt: prompt
        }),
        success: function(response) {
            if (response.success && response.content) {
                // 渲染写作结果
                const content = marked.parse(response.content.content);
                $('#writingResult').html(content);
                addLogEntry('info', '成功生成自动写作内容');
            } else {
                $('#writingResult').html(`<div class="alert alert-danger">${response.error || '无法生成自动写作内容'}</div>`);
                addLogEntry('error', `生成自动写作内容失败: ${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#writingResult').html('<div class="alert alert-danger">生成自动写作内容时出错</div>');
            addLogEntry('error', `生成自动写作内容失败: ${xhr.status} ${xhr.statusText}`);
        }
    });
}
