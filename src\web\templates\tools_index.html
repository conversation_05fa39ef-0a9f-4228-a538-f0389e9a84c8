<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫 - 开发工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}" onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        .tool-icon {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #007bff;
        }
        .tools-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .page-header {
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 15px;
        }
        .back-link {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="back-link">
            <a href="/" class="btn btn-outline-secondary btn-sm">&larr; 返回首页</a>
        </div>

        <div class="page-header">
            <h1>九猫开发工具</h1>
            <p class="lead">这些工具可以帮助您调试和开发九猫系统</p>
        </div>

        <div class="tools-container">
            <div class="row">
                {% for tool in tools %}
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="tool-icon">
                                <i class="fas fa-{{ tool.icon }}"></i>
                            </div>
                            <h5 class="card-title">{{ tool.name }}</h5>
                            <p class="card-text">{{ tool.description }}</p>
                            <a href="{{ tool.url }}" class="btn btn-primary">打开工具</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
