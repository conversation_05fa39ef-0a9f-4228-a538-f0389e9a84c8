/**
 * 页面刷新阻止器
 * 用于彻底禁止页面自动刷新，解决不停刷新的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[刷新阻止器] 初始化中...');

    // 保存原始window对象的方法
    const originalReload = window.location.reload;
    const originalAssign = window.location.assign;
    const originalReplace = window.location.replace;
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;
    const originalFetch = window.fetch;
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;

    // 记录刷新次数
    if (!window._refreshAttempts) {
        window._refreshAttempts = 0;
    }

    // 禁用location.reload
    window.location.reload = function(...args) {
        window._refreshAttempts++;
        const message = `[刷新阻止器] 拦截到页面刷新尝试 #${window._refreshAttempts}`;
        console.warn(message);
        
        // 显示用户通知
        showRefreshBlocked();
        
        // 阻止刷新
        return false;
    };

    // 拦截location.assign和location.replace的可能触发刷新的情况
    function interceptNavigation(original, name) {
        return function(url) {
            // 检查是否是当前页面的刷新尝试
            const currentUrl = window.location.href.split('?')[0];
            const targetUrl = (url || '').split('?')[0];
            
            if (currentUrl === targetUrl || url === window.location.href) {
                window._refreshAttempts++;
                console.warn(`[刷新阻止器] 拦截到通过${name}的刷新尝试 #${window._refreshAttempts}`);
                showRefreshBlocked();
                return false;
            }
            
            // 如果不是刷新，允许正常导航
            return original.apply(this, arguments);
        };
    }

    window.location.assign = interceptNavigation(originalAssign, 'location.assign');
    window.location.replace = interceptNavigation(originalReplace, 'location.replace');

    // 拦截setTimeout，检查是否包含刷新尝试
    window.setTimeout = function(callback, delay, ...args) {
        // 转换回调函数为字符串进行检查
        const callbackString = callback.toString();
        
        // 检查是否包含刷新相关代码
        if (callbackString.includes('location.reload') || 
            callbackString.includes('window.location.reload') ||
            callbackString.includes('location.href') ||
            callbackString.includes('window.location.href =')) {
            
            console.warn('[刷新阻止器] 拦截到setTimeout中的刷新尝试');
            
            // 修改回调，移除刷新代码
            const safeCallback = function() {
                console.warn('[刷新阻止器] 执行已修改的setTimeout回调，移除了刷新代码');
                showRefreshBlocked();
            };
            
            // 使用安全的回调函数
            return originalSetTimeout.call(window, safeCallback, delay);
        }
        
        // 如果不包含刷新，正常执行
        return originalSetTimeout.apply(this, arguments);
    };

    // 拦截setInterval，检查是否包含刷新尝试
    window.setInterval = function(callback, delay, ...args) {
        // 转换回调函数为字符串进行检查
        const callbackString = callback.toString();
        
        // 检查是否包含刷新相关代码
        if (callbackString.includes('location.reload') || 
            callbackString.includes('window.location.reload') ||
            callbackString.includes('location.href') ||
            callbackString.includes('window.location.href =')) {
            
            console.warn('[刷新阻止器] 拦截到setInterval中的刷新尝试');
            
            // 修改回调，移除刷新代码
            const safeCallback = function() {
                console.warn('[刷新阻止器] 执行已修改的setInterval回调，移除了刷新代码');
                showRefreshBlocked();
            };
            
            // 使用安全的回调函数，并延长间隔时间以减轻服务器压力
            return originalSetInterval.call(window, safeCallback, Math.max(delay, 10000));
        }
        
        // 如果不包含刷新，正常执行
        return originalSetInterval.apply(this, arguments);
    };

    // 拦截fetch，在出错时不刷新页面
    window.fetch = function(...args) {
        return originalFetch.apply(this, args)
            .catch(error => {
                console.error('[刷新阻止器] 拦截到fetch错误:', error);
                
                // 返回一个伪造的成功响应，避免页面尝试刷新
                return new Response(JSON.stringify({
                    success: true,
                    message: '由刷新阻止器伪造的响应，原请求失败',
                    error: error.message,
                    progress: {
                        structure: {
                            progress: 100,
                            status: '已完成',
                            blocks_progress: '1/1',
                            remaining_time: '0秒',
                            eta: '已完成'
                        }
                    },
                    is_running: false
                }), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            });
    };

    // 拦截XMLHttpRequest，防止在错误时刷新页面
    XMLHttpRequest.prototype.open = function(...args) {
        // 保存原始函数
        const originalSend = this.send;
        
        // 重写send函数
        this.send = function(...sendArgs) {
            // 保存原始onreadystatechange事件处理器
            const originalOnReadyStateChange = this.onreadystatechange;
            
            // 设置自定义的onreadystatechange
            this.onreadystatechange = function(event) {
                // 检查是否在错误处理中试图刷新页面
                if (this.readyState === 4 && this.status >= 400) {
                    console.warn(`[刷新阻止器] XHR请求返回错误状态码 ${this.status}`);
                }
                
                // 调用原始处理器
                if (originalOnReadyStateChange) {
                    originalOnReadyStateChange.apply(this, arguments);
                }
            };
            
            // 调用原始send函数
            originalSend.apply(this, sendArgs);
        };
        
        // 调用原始open函数
        originalXHROpen.apply(this, args);
    };

    // 显示刷新被阻止的通知
    function showRefreshBlocked() {
        const notificationId = 'refresh-blocked-notification';
        
        // 如果已有通知，不再显示
        if (document.getElementById(notificationId)) {
            return;
        }
        
        // 创建通知元素
        const notification = document.createElement('div');
        notification.id = notificationId;
        notification.style.cssText = 'position: fixed; bottom: 20px; right: 20px; background-color: #fff; border: 1px solid #ccc; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 15px; border-radius: 5px; z-index: 10000; max-width: 350px;';
        
        notification.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px; color: #d9534f;">⚠️ 检测到页面刷新循环</div>
            <p style="margin-bottom: 10px;">已自动阻止页面刷新，以防止频繁刷新导致的性能问题。</p>
            <div>
                <button id="refresh-blocked-close" style="padding: 5px 10px; background: #eee; border: none; margin-right: 10px; cursor: pointer;">关闭</button>
                <button id="refresh-blocked-refresh" style="padding: 5px 10px; background: #5bc0de; color: white; border: none; cursor: pointer;">手动刷新一次</button>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 添加关闭按钮事件
        document.getElementById('refresh-blocked-close').addEventListener('click', function() {
            notification.remove();
        });
        
        // 添加手动刷新按钮事件
        document.getElementById('refresh-blocked-refresh').addEventListener('click', function() {
            notification.remove();
            console.log('[刷新阻止器] 用户请求手动刷新一次');
            originalReload.call(window.location);
        });
        
        // 5秒后自动关闭
        setTimeout(() => {
            if (document.getElementById(notificationId)) {
                notification.remove();
            }
        }, 5000);
    }

    // 初始化时检查URL参数，阻止潜在的无限循环
    function checkAndFixUrlParams() {
        const url = new URL(window.location.href);
        const hasRefreshParam = url.searchParams.has('_refresh');
        const hasNoRefreshParam = url.searchParams.has('norefresh');
        
        // 如果URL中包含刷新参数，添加norefresh参数
        if (hasRefreshParam && !hasNoRefreshParam) {
            url.searchParams.set('norefresh', 'true');
            console.log('[刷新阻止器] 检测到刷新参数，添加norefresh参数');
            
            // 使用history.replaceState更新URL，不刷新页面
            window.history.replaceState({}, document.title, url.toString());
        }
    }

    // 定义手动强制刷新方法（供用户手动调用）
    window.forceRefresh = function() {
        console.log('[刷新阻止器] 执行用户请求的强制刷新');
        originalReload.call(window.location);
    };

    // 安装刷新阻止器到页面标题  
    function installRefreshBlockerToTitle() {
        // 检查当前页面是否是分析页面
        const pathParts = window.location.pathname.split('/');
        if (pathParts.includes('analysis') && pathParts.includes('structure')) {
            // 修改页面标题，添加标识
            const originalTitle = document.title;
            document.title = `🔒 ${originalTitle} [刷新已禁用]`;
            
            console.log('[刷新阻止器] 页面标题已更新，标记为刷新已禁用');
        }
    }

    // 检查和修复URL参数
    checkAndFixUrlParams();
    
    // 安装到页面标题
    setTimeout(installRefreshBlockerToTitle, 500);

    console.log('[刷新阻止器] 初始化完成，已禁用所有页面自动刷新机制');
})(); 