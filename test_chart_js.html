<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js 测试</title>
    <script src="http://localhost:5001/static/js/chart.min.js"></script>
</head>
<body>
    <h1>Chart.js 加载测试</h1>
    <p>如果Chart.js加载成功，下面的画布将显示一个图表</p>
    <canvas id="myChart" width="400" height="200"></canvas>

    <script>
        // 检查Chart是否已加载
        window.onload = function() {
            if (typeof Chart !== 'undefined') {
                document.body.innerHTML += '<p style="color: green; font-weight: bold;">Chart.js 加载成功!</p>';
                
                // 创建一个简单的图表
                const ctx = document.getElementById('myChart').getContext('2d');
                const myChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['红色', '蓝色', '黄色', '绿色', '紫色', '橙色'],
                        datasets: [{
                            label: '测试数据',
                            data: [12, 19, 3, 5, 2, 3],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.2)',
                                'rgba(54, 162, 235, 0.2)',
                                'rgba(255, 206, 86, 0.2)',
                                'rgba(75, 192, 192, 0.2)',
                                'rgba(153, 102, 255, 0.2)',
                                'rgba(255, 159, 64, 0.2)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)',
                                'rgba(153, 102, 255, 1)',
                                'rgba(255, 159, 64, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            } else {
                document.body.innerHTML += '<p style="color: red; font-weight: bold;">Chart.js 加载失败!</p>';
            }
        };
    </script>
</body>
</html>
