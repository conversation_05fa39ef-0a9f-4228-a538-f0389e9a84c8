/**
 * 九猫 - 修复测试脚本
 * 用于测试jQuery错误和章节分析跳转修复
 * 版本: 1.0.0
 */

(function() {
    console.log('[九猫测试] 修复测试脚本已加载');
    
    // 测试S.find.matchesSelector修复
    function testSizzleFix() {
        console.log('[九猫测试] 开始测试S.find.matchesSelector修复');
        
        try {
            // 创建一个模拟的S对象
            if (typeof window.S === 'undefined') {
                window.S = {
                    find: {
                        matches: function() { return true; }
                    }
                };
                console.log('[九猫测试] 创建了模拟的S对象');
            }
            
            // 检查matchesSelector是否被正确修复
            setTimeout(function() {
                if (window.S && window.S.find && window.S.find.matchesSelector) {
                    console.log('[九猫测试] S.find.matchesSelector修复成功');
                } else {
                    console.error('[九猫测试] S.find.matchesSelector修复失败');
                }
            }, 1000);
        } catch (e) {
            console.error('[九猫测试] 测试S.find.matchesSelector修复时出错:', e);
        }
    }
    
    // 测试章节分析跳转修复
    function testChapterAnalysisRouteFix() {
        console.log('[九猫测试] 开始测试章节分析跳转修复');
        
        try {
            // 检查是否在小说详情页
            if (window.location.pathname.match(/\/novel\/\d+\/?$/)) {
                console.log('[九猫测试] 检测到小说详情页，测试章节分析按钮');
                
                // 查找章节分析按钮
                const chapterAnalysisButtons = document.querySelectorAll('a.btn[href*="/chapter"], a.btn[href*="chapters"], button.btn:contains("章节分析")');
                
                if (chapterAnalysisButtons.length > 0) {
                    console.log(`[九猫测试] 找到${chapterAnalysisButtons.length}个章节分析按钮`);
                    
                    // 检查按钮的href属性
                    chapterAnalysisButtons.forEach(btn => {
                        if (btn.tagName === 'A') {
                            const href = btn.getAttribute('href');
                            const novelId = window.location.pathname.match(/\/novel\/(\d+)/)[1];
                            const expectedHref = `/novel/${novelId}/chapters`;
                            
                            if (href === expectedHref) {
                                console.log(`[九猫测试] 按钮href属性正确: ${href}`);
                            } else {
                                console.warn(`[九猫测试] 按钮href属性不正确: ${href}，应为: ${expectedHref}`);
                            }
                        }
                    });
                } else {
                    console.warn('[九猫测试] 未找到章节分析按钮');
                }
            } else if (window.location.pathname.includes('/novel/41')) {
                console.log('[九猫测试] 检测到小说ID为41，测试特殊修复');
                
                // 查找章节分析按钮
                const chapterAnalysisButtons = document.querySelectorAll('a.btn, button.btn');
                let found = false;
                
                chapterAnalysisButtons.forEach(btn => {
                    if (btn.textContent.includes('章节分析') || (btn.textContent.includes('章节') && btn.textContent.includes('分析'))) {
                        found = true;
                        console.log('[九猫测试] 找到小说41的章节分析按钮');
                        
                        if (btn.tagName === 'A') {
                            const href = btn.getAttribute('href');
                            if (href === '/novel/41/chapters') {
                                console.log('[九猫测试] 小说41的章节分析按钮href属性正确');
                            } else {
                                console.warn(`[九猫测试] 小说41的章节分析按钮href属性不正确: ${href}，应为: /novel/41/chapters`);
                            }
                        }
                    }
                });
                
                if (!found) {
                    console.warn('[九猫测试] 未找到小说41的章节分析按钮');
                }
            } else {
                console.log('[九猫测试] 不是小说详情页，跳过章节分析按钮测试');
            }
        } catch (e) {
            console.error('[九猫测试] 测试章节分析跳转修复时出错:', e);
        }
    }
    
    // 初始化函数
    function initialize() {
        console.log('[九猫测试] 初始化中...');
        
        // 延迟执行测试，确保其他脚本已加载
        setTimeout(function() {
            // 测试S.find.matchesSelector修复
            testSizzleFix();
            
            // 测试章节分析跳转修复
            testChapterAnalysisRouteFix();
            
            console.log('[九猫测试] 测试完成');
        }, 2000);
    }
    
    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
})();
