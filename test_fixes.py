"""
简单测试脚本，用于验证修复后的代码。
"""
import os
import sys
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入配置
import config
logger.info(f"DeepSeek API密钥: {config.DEEPSEEK_API_KEY[:6]}...")

# 测试时间计算
import time
from src.api.deepseek_client import DeepSeekClient

# 设置测试时间
DeepSeekClient._session_start_time = time.time() - 100  # 100秒前
DeepSeekClient._analysis_start_time = time.time() - 50  # 50秒前

# 获取API调用统计
stats = DeepSeekClient.get_api_call_stats()
logger.info(f"会话时长: {stats['session_duration_minutes']:.2f}分钟")
logger.info(f"分析时长: {stats['analysis_duration_minutes']:.2f}分钟")
logger.info(f"会话时长(秒): {stats['session_duration_seconds']:.2f}秒")
logger.info(f"分析时长(秒): {stats['analysis_duration_seconds']:.2f}秒")

# 测试中间结果模型
from src.models.intermediate_result import IntermediateResult

# 创建测试对象
test_result = IntermediateResult(
    novel_id=999,
    chunk_index=0,
    chunk_hash="test_hash",
    dimension="paragraph_flow",
    dimension_result="测试结果内容",
    result_data={"test": "data"}
)

logger.info(f"创建的中间结果对象: {test_result}")
logger.info(f"中间结果对象的维度: {test_result.dimension}")
logger.info(f"中间结果对象的内容: {test_result.dimension_result}")
logger.info(f"中间结果对象的数据: {test_result.result_data}")

logger.info("测试完成")
