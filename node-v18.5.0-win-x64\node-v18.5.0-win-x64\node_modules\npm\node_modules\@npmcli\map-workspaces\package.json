{"name": "@npmcli/map-workspaces", "version": "2.0.3", "main": "lib/index.js", "files": ["bin/", "lib/"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "description": "Retrieves a name:pathname Map for a given workspaces config", "repository": {"type": "git", "url": "https://github.com/npm/map-workspaces.git"}, "keywords": ["npm", "npmcli", "libnpm", "cli", "workspaces", "map-workspaces"], "author": "GitHub Inc.", "license": "ISC", "scripts": {"lint": "eslint \"**/*.js\"", "pretest": "npm run lint", "test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.4.1", "tap": "^16.0.1"}, "dependencies": {"@npmcli/name-from-folder": "^1.0.1", "glob": "^8.0.1", "minimatch": "^5.0.1", "read-package-json-fast": "^2.0.3"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.4.1"}}