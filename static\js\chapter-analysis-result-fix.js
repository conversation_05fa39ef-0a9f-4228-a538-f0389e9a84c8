/**
 * 九猫 - 章节分析结果修复脚本
 * 专门用于修复章节分析页面的结果显示问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[章节分析结果修复] 脚本已加载');

    // 配置
    const CONFIG = {
        autoFix: true,           // 是否自动修复
        fixDelay: 500,           // 修复延迟（毫秒）
        checkInterval: 2000,     // 检查间隔（毫秒）
        maxRetries: 5,           // 最大重试次数
        debug: true              // 是否启用调试日志
    };

    // 安全的日志函数
    function safeLog(message, level = 'log') {
        if (!CONFIG.debug && level === 'log') return;
        
        try {
            if (level === 'error') {
                console.error(`[章节分析结果修复] ${message}`);
            } else if (level === 'warn') {
                console.warn(`[章节分析结果修复] ${message}`);
            } else {
                console.log(`[章节分析结果修复] ${message}`);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }

    // 获取当前小说ID和章节ID
    function getCurrentIds() {
        try {
            // 从URL中提取小说ID和章节ID
            const match = window.location.pathname.match(/\/novel\/(\d+)\/chapter\/(\d+)/);
            if (match) {
                return {
                    novelId: match[1],
                    chapterId: match[2]
                };
            }

            // 如果URL不匹配，尝试从页面元素中获取
            const breadcrumb = document.querySelector('.breadcrumb');
            if (breadcrumb) {
                const links = breadcrumb.querySelectorAll('a');
                for (const link of links) {
                    const href = link.getAttribute('href');
                    if (href) {
                        const novelMatch = href.match(/\/novel\/(\d+)/);
                        if (novelMatch) {
                            const chapterMatch = href.match(/\/chapter\/(\d+)/);
                            if (chapterMatch) {
                                return {
                                    novelId: novelMatch[1],
                                    chapterId: chapterMatch[1]
                                };
                            }
                        }
                    }
                }
            }

            safeLog('无法从URL或页面元素中获取小说ID和章节ID', 'warn');
            return null;
        } catch (e) {
            safeLog(`获取当前ID时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 获取分析结果
    function fetchAnalysisResult(novelId, chapterId, dimension) {
        return new Promise((resolve, reject) => {
            safeLog(`从API获取章节分析结果: novelId=${novelId}, chapterId=${chapterId}, dimension=${dimension}`);
            
            fetch(`/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && data.success && data.result && data.result.content) {
                        safeLog(`成功获取章节分析结果: dimension=${dimension}`);
                        resolve(data.result);
                    } else if (data && data.result) {
                        // 即使没有content字段，也尝试使用结果
                        safeLog(`获取到章节分析结果，但缺少content字段: dimension=${dimension}`, 'warn');
                        resolve(data.result);
                    } else {
                        reject(new Error('分析结果为空或无效'));
                    }
                })
                .catch(error => {
                    safeLog(`获取章节分析结果时出错: ${error.message}`, 'error');
                    reject(error);
                });
        });
    }

    // 更新分析结果显示
    function updateAnalysisResult(result) {
        try {
            safeLog('更新分析结果显示');
            
            // 查找分析结果容器
            const resultContainer = document.querySelector('.analysis-result');
            if (!resultContainer) {
                safeLog('找不到分析结果容器', 'error');
                return false;
            }

            // 查找内容容器
            const contentContainer = resultContainer.querySelector('.analysis-content');
            if (!contentContainer) {
                safeLog('找不到内容容器', 'error');
                return false;
            }

            // 更新内容
            if (result.content) {
                contentContainer.innerHTML = result.content;
                safeLog('成功更新分析结果内容');
            }

            // 更新元数据
            const metadataContainer = resultContainer.querySelector('#analysis-metadata');
            if (metadataContainer && result.metadata) {
                metadataContainer.setAttribute('data-metadata', JSON.stringify(result.metadata));
                const preElement = metadataContainer.querySelector('pre.metadata-json');
                if (preElement) {
                    preElement.textContent = JSON.stringify(result.metadata, null, 2);
                }
                safeLog('成功更新分析结果元数据');
            }

            // 更新日志
            const logsContainer = resultContainer.querySelector('.logs-container');
            if (logsContainer && result.analysis_logs) {
                logsContainer.innerHTML = '';
                result.analysis_logs.forEach(log => {
                    const logEntry = document.createElement('div');
                    logEntry.className = `log-entry ${log.level || 'info'}`;
                    
                    const timestamp = document.createElement('span');
                    timestamp.className = 'log-timestamp';
                    timestamp.textContent = log.timestamp || new Date().toISOString();
                    
                    const level = document.createElement('span');
                    level.className = 'log-level';
                    level.textContent = log.level || 'info';
                    
                    const message = document.createElement('span');
                    message.className = 'log-message';
                    message.textContent = log.message || '';
                    
                    logEntry.appendChild(timestamp);
                    logEntry.appendChild(level);
                    logEntry.appendChild(message);
                    logsContainer.appendChild(logEntry);
                });
                safeLog('成功更新分析结果日志');
            }

            return true;
        } catch (e) {
            safeLog(`更新分析结果显示时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 检查并修复分析结果
    function checkAndFixAnalysisResult() {
        try {
            safeLog('检查并修复分析结果');
            
            // 获取当前小说ID和章节ID
            const ids = getCurrentIds();
            if (!ids) {
                // 尝试从隐藏的数据元素中获取
                const dataElement = document.getElementById('analysis-data');
                if (dataElement) {
                    const novelId = dataElement.getAttribute('data-novel-id');
                    const chapterId = dataElement.getAttribute('data-chapter-id');
                    const dimension = dataElement.getAttribute('data-dimension');
                    
                    if (novelId && chapterId && dimension) {
                        safeLog(`从数据元素中获取ID: novelId=${novelId}, chapterId=${chapterId}, dimension=${dimension}`);
                        
                        // 获取分析结果
                        fetchAnalysisResult(novelId, chapterId, dimension)
                            .then(result => {
                                // 更新分析结果显示
                                if (updateAnalysisResult(result)) {
                                    safeLog('成功修复分析结果');
                                }
                            })
                            .catch(error => {
                                safeLog(`获取分析结果时出错: ${error.message}`, 'warn');
                            });
                        
                        return;
                    }
                }
                
                safeLog('无法获取当前小说ID和章节ID，跳过修复', 'warn');
                return;
            }

            // 获取当前维度
            const dimensionMatch = window.location.pathname.match(/\/analysis\/([^\/]+)$/);
            if (!dimensionMatch) {
                // 尝试从内容元素中获取
                const contentElement = document.querySelector('.analysis-content');
                if (contentElement) {
                    const dimension = contentElement.getAttribute('data-dimension');
                    if (dimension) {
                        safeLog(`从内容元素中获取维度: ${dimension}`);
                        
                        // 获取分析结果
                        fetchAnalysisResult(ids.novelId, ids.chapterId, dimension)
                            .then(result => {
                                // 更新分析结果显示
                                if (updateAnalysisResult(result)) {
                                    safeLog('成功修复分析结果');
                                }
                            })
                            .catch(error => {
                                safeLog(`获取分析结果时出错: ${error.message}`, 'warn');
                            });
                        
                        return;
                    }
                }
                
                safeLog('无法从URL或元素中获取维度，跳过修复', 'warn');
                return;
            }

            const dimension = dimensionMatch[1];
            safeLog(`当前维度: ${dimension}`);

            // 获取分析结果
            fetchAnalysisResult(ids.novelId, ids.chapterId, dimension)
                .then(result => {
                    // 更新分析结果显示
                    if (updateAnalysisResult(result)) {
                        safeLog('成功修复分析结果');
                    }
                })
                .catch(error => {
                    safeLog(`获取分析结果时出错: ${error.message}`, 'warn');
                });
        } catch (e) {
            safeLog(`检查并修复分析结果时出错: ${e.message}`, 'error');
        }
    }

    // 初始化
    function initialize() {
        safeLog('初始化章节分析结果修复脚本');
        
        // 检查当前页面是否是章节分析页面
        const isChapterAnalysisPage = window.location.pathname.match(/\/novel\/\d+\/chapter\/\d+\/analysis\/[^\/]+$/);
        
        // 检查是否有分析数据元素
        const dataElement = document.getElementById('analysis-data');
        
        if (!isChapterAnalysisPage && !dataElement) {
            safeLog('当前页面不是章节分析页面，跳过初始化');
            return;
        }

        // 自动修复
        if (CONFIG.autoFix) {
            safeLog('启用自动修复');
            setTimeout(() => {
                checkAndFixAnalysisResult();
                
                // 设置定期检查
                const intervalId = setInterval(() => {
                    // 检查分析内容是否为空
                    const contentElement = document.querySelector('.analysis-content');
                    if (contentElement && contentElement.textContent.trim() === '') {
                        safeLog('检测到分析内容为空，尝试修复');
                        checkAndFixAnalysisResult();
                    } else {
                        safeLog('定期检查分析结果');
                        checkAndFixAnalysisResult();
                    }
                }, CONFIG.checkInterval);
                
                // 保存定时器ID，以便后续可以清除
                window._chapterAnalysisCheckIntervalId = intervalId;
            }, CONFIG.fixDelay);
        }

        // 导出全局函数
        window.chapterAnalysisResultFix = {
            checkAndFixAnalysisResult,
            updateAnalysisResult,
            fetchAnalysisResult,
            stopChecking: function() {
                if (window._chapterAnalysisCheckIntervalId) {
                    clearInterval(window._chapterAnalysisCheckIntervalId);
                    safeLog('已停止定期检查');
                }
            }
        };
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    safeLog('脚本加载完成');
})();
