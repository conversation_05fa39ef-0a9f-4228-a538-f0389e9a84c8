/**
 * 九猫 - DOM操作修复脚本
 * 用于修复各种DOM操作错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('DOM操作修复脚本已加载');

    // 安全执行函数
    function safeExecute(fn, fallback) {
        try {
            return fn();
        } catch (e) {
            console.warn('执行函数时出错:', e);
            return fallback;
        }
    }

    // 修复appendChild方法
    function fixAppendChild() {
        const originalAppendChild = Node.prototype.appendChild;

        Node.prototype.appendChild = function(child) {
            // 检查child是否已经是子节点
            if (this.contains(child)) {
                console.warn('appendChild: 节点已经是子节点');
                // 先移除，再添加
                try {
                    this.removeChild(child);
                } catch (e) {
                    // 忽略移除错误
                }
            }

            // 安全地执行原始appendChild
            try {
                return originalAppendChild.call(this, child);
            } catch (e) {
                console.error('appendChild执行错误:', e);
                return child;
            }
        };

        console.log('修复appendChild方法');
    }

    // 修复replaceChild方法
    function fixReplaceChild() {
        const originalReplaceChild = Node.prototype.replaceChild;

        Node.prototype.replaceChild = function(newChild, oldChild) {
            // 检查参数是否有效
            if (!newChild || !oldChild) {
                console.warn('replaceChild: 无效的参数', { newChild, oldChild });
                return newChild || oldChild || null;
            }

            // 确保旧节点是子节点
            if (!this.contains(oldChild)) {
                console.warn('replaceChild: 旧节点不是子节点');
                return newChild;
            }

            // 检查是否为脚本节点
            if (newChild.nodeType === Node.ELEMENT_NODE &&
                (newChild.tagName === 'SCRIPT' || newChild.nodeName === 'SCRIPT')) {
                console.log('检测到脚本节点替换，使用特殊处理');

                try {
                    // 创建一个新的脚本节点
                    const safeScript = document.createElement('script');

                    // 复制属性
                    if (newChild.attributes) {
                        for (let i = 0; i < newChild.attributes.length; i++) {
                            const attr = newChild.attributes[i];
                            safeScript.setAttribute(attr.name, attr.value);
                        }
                    }

                    // 使用文本节点添加内容，而不是设置innerHTML或textContent
                    if (newChild.textContent) {
                        const textNode = document.createTextNode(newChild.textContent);
                        safeScript.appendChild(textNode);
                    }

                    // 使用安全的方式替换
                    this.insertBefore(safeScript, oldChild);
                    this.removeChild(oldChild);

                    return safeScript;
                } catch (scriptError) {
                    console.error('脚本节点特殊处理失败:', scriptError);
                }
            }

            // 安全地执行原始replaceChild
            try {
                return originalReplaceChild.call(this, newChild, oldChild);
            } catch (e) {
                console.error('replaceChild执行错误:', e);
                try {
                    // 尝试先移除旧节点，再添加新节点
                    this.removeChild(oldChild);
                    this.appendChild(newChild);
                } catch (innerError) {
                    console.error('替代方案执行错误:', innerError);
                }
                return newChild;
            }
        };

        console.log('修复replaceChild方法');
    }

    // 修复removeChild方法
    function fixRemoveChild() {
        const originalRemoveChild = Node.prototype.removeChild;

        Node.prototype.removeChild = function(child) {
            // 确保是子节点
            if (!this.contains(child)) {
                console.warn('removeChild: 节点不是子节点');
                return child;
            }

            // 安全地执行原始removeChild
            try {
                return originalRemoveChild.call(this, child);
            } catch (e) {
                console.error('removeChild执行错误:', e);
                return child;
            }
        };

        console.log('修复removeChild方法');
    }

    // 修复insertBefore方法
    function fixInsertBefore() {
        const originalInsertBefore = Node.prototype.insertBefore;

        Node.prototype.insertBefore = function(newChild, refChild) {
            // 检查参数是否有效
            if (!newChild) {
                console.warn('insertBefore: 新节点为空');
                return null;
            }

            // 如果引用节点为null，相当于appendChild
            if (refChild === null) {
                return this.appendChild(newChild);
            }

            // 确保引用节点是子节点
            if (!this.contains(refChild)) {
                console.warn('insertBefore: 引用节点不是子节点');
                return this.appendChild(newChild);
            }

            // 检查是否为脚本节点
            if (newChild.nodeType === Node.ELEMENT_NODE &&
                (newChild.tagName === 'SCRIPT' || newChild.nodeName === 'SCRIPT')) {
                console.log('检测到脚本节点插入，使用特殊处理');

                try {
                    // 创建一个新的脚本节点
                    const safeScript = document.createElement('script');

                    // 复制属性
                    if (newChild.attributes) {
                        for (let i = 0; i < newChild.attributes.length; i++) {
                            const attr = newChild.attributes[i];
                            safeScript.setAttribute(attr.name, attr.value);
                        }
                    }

                    // 使用文本节点添加内容，但将内容包装在立即执行函数中以避免变量污染
                    if (newChild.textContent) {
                        const content = newChild.textContent.trim();

                        // 如果内容不为空，包装在立即执行函数中
                        if (content) {
                            // 检查内容是否已经是立即执行函数
                            if (!content.startsWith('(function') && !content.startsWith('(()')) {
                                // 包装在立即执行函数中
                                const wrappedContent = `
// 九猫安全脚本包装器 - 避免变量污染
(function() {
    try {
${content}
    } catch (e) {
        console.error('脚本执行错误:', e);
    }
})();`;

                                // 使用文本节点添加包装后的内容
                                const textNode = document.createTextNode(wrappedContent);
                                safeScript.appendChild(textNode);
                            } else {
                                // 已经是立即执行函数，直接使用
                                const textNode = document.createTextNode(content);
                                safeScript.appendChild(textNode);
                            }
                        }
                    }

                    // 如果新节点已经在DOM中，先将其移除
                    if (newChild.parentNode) {
                        try {
                            newChild.parentNode.removeChild(newChild);
                        } catch (e) {
                            // 忽略移除错误
                        }
                    }

                    // 使用安全的方式插入
                    return originalInsertBefore.call(this, safeScript, refChild);
                } catch (scriptError) {
                    console.error('脚本节点特殊处理失败:', scriptError);
                }
            }

            // 如果新节点已经在DOM中，先将其移除
            if (newChild.parentNode) {
                try {
                    newChild.parentNode.removeChild(newChild);
                } catch (e) {
                    // 忽略移除错误
                }
            }

            // 安全地执行原始insertBefore
            try {
                return originalInsertBefore.call(this, newChild, refChild);
            } catch (e) {
                console.error('insertBefore执行错误:', e);

                // 如果是语法错误或变量重复声明错误，尝试安全插入
                if (e instanceof SyntaxError ||
                    (e.message && (e.message.includes('Identifier') || e.message.includes('has already been declared')))) {
                    console.log('检测到语法错误或变量重复声明，尝试安全插入');

                    try {
                        // 尝试插入到引用节点之后
                        const nextSibling = refChild.nextSibling;
                        if (nextSibling) {
                            return originalInsertBefore.call(this, newChild, nextSibling);
                        } else {
                            return this.appendChild(newChild);
                        }
                    } catch (innerError) {
                        console.error('替代方案执行错误:', innerError);
                        return this.appendChild(newChild);
                    }
                } else {
                    // 其他错误，使用原始的错误处理
                    try {
                        // 尝试插入到引用节点之后
                        const nextSibling = refChild.nextSibling;
                        if (nextSibling) {
                            return originalInsertBefore.call(this, newChild, nextSibling);
                        } else {
                            return this.appendChild(newChild);
                        }
                    } catch (innerError) {
                        console.error('替代方案执行错误:', innerError);
                        return this.appendChild(newChild);
                    }
                }
            }
        };

        console.log('修复insertBefore方法');
    }

    // 修复innerHTML操作
    function fixInnerHTML() {
        const originalInnerHTMLDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');

        if (originalInnerHTMLDescriptor && originalInnerHTMLDescriptor.set) {
            Object.defineProperty(Element.prototype, 'innerHTML', {
                get: originalInnerHTMLDescriptor.get,
                set: function(value) {
                    try {
                        return originalInnerHTMLDescriptor.set.call(this, value);
                    } catch (e) {
                        console.error('设置innerHTML时出错:', e);

                        // 尝试清空当前内容并添加新内容
                        try {
                            // 清空当前内容
                            while (this.firstChild) {
                                this.removeChild(this.firstChild);
                            }

                            // 创建一个临时容器
                            const temp = document.createElement('div');
                            originalInnerHTMLDescriptor.set.call(temp, value);

                            // 复制节点
                            const fragment = document.createDocumentFragment();
                            while (temp.firstChild) {
                                fragment.appendChild(temp.firstChild);
                            }

                            this.appendChild(fragment);
                        } catch (innerError) {
                            console.error('替代方案执行错误:', innerError);
                        }
                    }
                },
                enumerable: originalInnerHTMLDescriptor.enumerable,
                configurable: originalInnerHTMLDescriptor.configurable
            });

            console.log('修复innerHTML操作');
        }
    }

    // 修复querySelector操作
    function fixQuerySelector() {
        const originalQuerySelector = Element.prototype.querySelector;

        Element.prototype.querySelector = function(selectors) {
            try {
                // 检查选择器是否有效
                if (!selectors || typeof selectors !== 'string') {
                    console.warn('querySelector被调用时选择器无效:', selectors);
                    return null;
                }

                // 修复特殊选择器
                if (selectors === '\\' || selectors === '' || selectors === '*,:x') {
                    console.warn('修复无效选择器:', selectors, '-> "*"');
                    return originalQuerySelector.call(this, '*');
                }

                // 修复包含反斜杠的选择器
                if (selectors.includes('\\')) {
                    const fixedSelector = selectors.replace(/\\/g, '*');
                    console.warn('修复包含反斜杠的选择器:', selectors, '->', fixedSelector || '*');
                    return originalQuerySelector.call(this, fixedSelector || '*');
                }

                return originalQuerySelector.call(this, selectors);
            } catch (e) {
                console.error('querySelector执行错误:', e);
                return null;
            }
        };

        const originalQuerySelectorAll = Element.prototype.querySelectorAll;

        Element.prototype.querySelectorAll = function(selectors) {
            try {
                // 检查选择器是否有效
                if (!selectors || typeof selectors !== 'string') {
                    console.warn('querySelectorAll被调用时选择器无效:', selectors);
                    return document.createDocumentFragment().querySelectorAll('*');
                }

                // 修复特殊选择器
                if (selectors === '\\' || selectors === '' || selectors === '*,:x') {
                    console.warn('修复无效选择器:', selectors, '-> "*"');
                    return originalQuerySelectorAll.call(this, '*');
                }

                // 修复包含反斜杠的选择器
                if (selectors.includes('\\')) {
                    const fixedSelector = selectors.replace(/\\/g, '*');
                    console.warn('修复包含反斜杠的选择器:', selectors, '->', fixedSelector || '*');
                    return originalQuerySelectorAll.call(this, fixedSelector || '*');
                }

                return originalQuerySelectorAll.call(this, selectors);
            } catch (e) {
                console.error('querySelectorAll执行错误:', e, '选择器:', selectors);
                return document.createDocumentFragment().querySelectorAll('*');
            }
        };

        console.log('修复querySelector操作');
    }

    // 执行所有修复
    function applyAllFixes() {
        safeExecute(fixAppendChild);
        safeExecute(fixReplaceChild);
        safeExecute(fixRemoveChild);
        safeExecute(fixInsertBefore);
        safeExecute(fixInnerHTML);
        safeExecute(fixQuerySelector);

        console.log('DOM操作修复已应用');
    }

    // 页面加载时应用修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyAllFixes);
    } else {
        applyAllFixes();
    }

    // 导出到全局命名空间
    window.DOMFixer = {
        applyAllFixes: applyAllFixes,
        fixAppendChild: fixAppendChild,
        fixReplaceChild: fixReplaceChild,
        fixRemoveChild: fixRemoveChild,
        fixInsertBefore: fixInsertBefore,
        fixInnerHTML: fixInnerHTML,
        fixQuerySelector: fixQuerySelector
    };
})();