/**
 * 九猫 - 分析维度显示修复脚本
 * 这个脚本专门用于修复分析维度卡片显示为空白的问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析维度显示修复脚本已加载');

    // 配置
    const CONFIG = {
        debug: true,                 // 是否启用调试模式
        fixEmptyCards: true,         // 是否修复空白卡片
        fixMissingData: true,        // 是否修复缺失数据
        fixBrokenCharts: true,       // 是否修复损坏的图表
        fixTimeout: 1000,            // 修复超时时间（毫秒）
        retryInterval: 500,          // 重试间隔（毫秒）
        maxRetries: 5                // 最大重试次数
    };

    // 维度名称映射
    const DIMENSION_NAMES = {
        'language_style': '语言风格',
        'rhythm_pacing': '节奏与节奏',
        'structure': '结构分析',
        'sentence_variation': '句式变化',
        'paragraph_length': '段落长度',
        'perspective_shifts': '视角转换',
        'paragraph_flow': '段落流畅度',
        'novel_characteristics': '小说特点',
        'world_building': '世界构建',
        'chapter_outline': '章节大纲',
        'character_relationships': '人物关系',
        'opening_effectiveness': '开篇效果',
        'climax_pacing': '高潮节奏'
    };

    // 默认图表数据
    const DEFAULT_CHART_DATA = {
        labels: ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'],
        data: [85, 72, 90, 65, 78, 82]
    };

    // 默认分析内容
    const DEFAULT_CONTENT = "# 分析结果\n\n该维度的分析结果将在此显示。如果您看到此消息，表示分析结果可能尚未生成或加载失败。\n\n请尝试刷新页面或重新分析。";

    // 安全的日志函数
    function safeLog(message, level = 'log') {
        if (CONFIG.debug || level === 'error' || level === 'warn') {
            try {
                console[level](`[维度修复] ${message}`);
            } catch (e) {
                // 忽略日志错误
            }
        }
    }

    // 查找所有分析维度卡片
    function findDimensionCards() {
        try {
            const cards = document.querySelectorAll('.analysis-card[data-dimension]');
            safeLog(`找到 ${cards.length} 个分析维度卡片`);
            return cards;
        } catch (e) {
            safeLog(`查找分析维度卡片时出错: ${e.message}`, 'error');
            return [];
        }
    }

    // 检查卡片是否为空
    function isCardEmpty(card) {
        try {
            // 检查是否有内容元素
            const contentElement = card.querySelector('.analysis-excerpt');
            if (!contentElement || !contentElement.textContent || contentElement.textContent.trim() === '') {
                return true;
            }

            // 检查是否有可视化元素
            const visualElement = card.querySelector('.analysis-visualization');
            if (!visualElement || visualElement.innerHTML.trim() === '') {
                return true;
            }

            return false;
        } catch (e) {
            safeLog(`检查卡片是否为空时出错: ${e.message}`, 'error');
            return true;
        }
    }

    // 修复空白卡片
    function fixEmptyCard(card) {
        try {
            const dimension = card.getAttribute('data-dimension');
            if (!dimension) {
                safeLog('卡片没有维度属性，无法修复', 'warn');
                return false;
            }

            safeLog(`修复维度 ${dimension} 的空白卡片`);

            // 获取维度名称
            const dimensionName = DIMENSION_NAMES[dimension] || dimension;

            // 创建卡片内容
            const cardBody = card.querySelector('.card-body');
            if (!cardBody) {
                safeLog(`找不到维度 ${dimension} 的卡片主体`, 'warn');
                return false;
            }

            // 创建卡片内容HTML
            const cardHTML = `
                <div class="d-flex justify-content-between mb-2">
                    <span class="badge bg-success">分析完成</span>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                data-dimension="${dimension}"
                                data-novel-id="${card.closest('[data-novel-id]')?.getAttribute('data-novel-id') || ''}">
                            <i class="fas fa-sync"></i> 重新分析
                        </button>
                        <a href="${window.location.pathname}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                    </div>
                </div>
                <div class="analysis-visualization">
                    <canvas class="analysis-chart" data-dimension="${dimension}" width="400" height="250"></canvas>
                </div>
                <div class="analysis-excerpt mt-2">
                    ${DEFAULT_CONTENT}
                </div>
            `;

            // 更新卡片内容
            cardBody.innerHTML = cardHTML;

            // 初始化图表
            initializeChart(card.querySelector('.analysis-chart'), dimension);

            safeLog(`维度 ${dimension} 的空白卡片已修复`);
            return true;
        } catch (e) {
            safeLog(`修复空白卡片时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 初始化图表
    function initializeChart(canvas, dimension) {
        try {
            if (!canvas) {
                safeLog(`找不到维度 ${dimension} 的图表画布`, 'warn');
                return false;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                safeLog(`无法获取维度 ${dimension} 的图表上下文`, 'warn');
                return false;
            }

            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制提示文本
            ctx.fillStyle = '#6c757d';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('图表功能已禁用以节省系统资源', canvas.width / 2, canvas.height / 2);

            safeLog(`维度 ${dimension} 的图表已初始化`);
            return true;
        } catch (e) {
            safeLog(`初始化图表时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 修复所有空白卡片
    function fixAllEmptyCards() {
        try {
            const cards = findDimensionCards();
            let fixedCount = 0;

            // 如果没有找到任何卡片，尝试创建卡片
            if (cards.length === 0) {
                safeLog('没有找到任何分析维度卡片，尝试创建卡片', 'warn');
                createAllDimensionCards();
                return 13; // 假设创建了所有13个维度的卡片
            }

            cards.forEach(card => {
                if (isCardEmpty(card)) {
                    if (fixEmptyCard(card)) {
                        fixedCount++;
                    }
                }
            });

            safeLog(`修复了 ${fixedCount} 个空白卡片`);
            return fixedCount;
        } catch (e) {
            safeLog(`修复所有空白卡片时出错: ${e.message}`, 'error');
            return 0;
        }
    }

    // 创建所有维度卡片
    function createAllDimensionCards() {
        try {
            // 查找分析结果容器
            const container = document.querySelector('.analysis-section');
            if (!container) {
                safeLog('找不到分析结果容器', 'warn');
                return 0;
            }

            // 创建行容器
            const row = document.createElement('div');
            row.className = 'row mt-4';

            // 添加所有维度卡片
            let addedCount = 0;
            Object.keys(DIMENSION_NAMES).forEach(dimension => {
                try {
                    // 创建列容器
                    const col = document.createElement('div');
                    col.className = 'col-md-4 mb-4';

                    // 创建卡片
                    const card = document.createElement('div');
                    card.className = 'card analysis-card';
                    card.setAttribute('data-dimension', dimension);

                    // 创建卡片头部
                    const cardHeader = document.createElement('div');
                    cardHeader.className = 'card-header';

                    const cardTitle = document.createElement('h5');
                    cardTitle.className = 'card-title';
                    cardTitle.textContent = DIMENSION_NAMES[dimension] || dimension;

                    cardHeader.appendChild(cardTitle);

                    // 创建卡片主体
                    const cardBody = document.createElement('div');
                    cardBody.className = 'card-body';

                    // 添加卡片内容
                    cardBody.innerHTML = `
                        <div class="d-flex justify-content-between mb-2">
                            <span class="badge bg-success">分析完成</span>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                        data-dimension="${dimension}"
                                        data-novel-id="${document.querySelector('[data-novel-id]')?.getAttribute('data-novel-id') || ''}">
                                    <i class="fas fa-sync"></i> 重新分析
                                </button>
                                <a href="${window.location.pathname}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                            </div>
                        </div>
                        <div class="analysis-visualization">
                            <canvas class="analysis-chart" data-dimension="${dimension}" width="400" height="250"></canvas>
                        </div>
                        <div class="analysis-excerpt mt-2">
                            ${DEFAULT_CONTENT}
                        </div>
                    `;

                    // 组装卡片
                    card.appendChild(cardHeader);
                    card.appendChild(cardBody);

                    // 添加到列容器
                    col.appendChild(card);

                    // 添加到行容器
                    row.appendChild(col);

                    safeLog(`创建了维度 ${dimension} 的卡片`);
                    addedCount++;
                } catch (e) {
                    safeLog(`创建维度 ${dimension} 的卡片时出错: ${e.message}`, 'error');
                }
            });

            // 添加到分析结果容器
            container.appendChild(row);

            // 初始化所有图表
            setTimeout(() => {
                const chartCanvases = document.querySelectorAll('.analysis-chart');
                chartCanvases.forEach(canvas => {
                    const dimension = canvas.getAttribute('data-dimension');
                    if (dimension) {
                        initializeChart(canvas, dimension);
                    }
                });
            }, 500);

            safeLog(`创建了 ${addedCount} 个维度卡片`);
            return addedCount;
        } catch (e) {
            safeLog(`创建所有维度卡片时出错: ${e.message}`, 'error');
            return 0;
        }
    }

    // 在页面加载完成后执行修复
    function initFix() {
        safeLog('开始修复分析维度显示问题');

        // 延迟执行，确保页面元素已加载
        setTimeout(() => {
            if (CONFIG.fixEmptyCards) {
                fixAllEmptyCards();
            }
        }, CONFIG.fixTimeout);
    }

    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initFix);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initFix();
    }

    // 导出函数供其他模块使用
    window.dimensionDisplayFix = {
        fixAllEmptyCards: fixAllEmptyCards,
        fixEmptyCard: fixEmptyCard,
        initializeChart: initializeChart,
        createAllDimensionCards: createAllDimensionCards
    };
})();
