/**
 * 九猫 - 综合修复脚本
 * 包含所有修复功能，解决静态文件加载、Chart.js初始化和JSON解析等问题
 * 版本: 2.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('综合修复脚本已加载');

    // 存储图表实例的全局对象
    if (!window.chartInstances) {
        window.chartInstances = {};
    }

    // 修复静态文件加载问题
    function fixStaticFileLoading() {
        // 核心静态文件列表
        const coreStaticFiles = [
            { type: 'css', name: 'bootstrap.min.css', path: '/static/css/lib/bootstrap.min.css', backup: '/direct-static/css/lib/bootstrap.min.css', cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' },
            { type: 'js', name: 'jquery.min.js', path: '/static/js/lib/jquery-3.6.0.min.js', backup: '/direct-static/js/lib/jquery-3.6.0.min.js', cdn: 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js' },
            { type: 'js', name: 'bootstrap.bundle.min.js', path: '/static/js/lib/bootstrap.bundle.min.js', backup: '/direct-static/js/lib/bootstrap.bundle.min.js', cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js' },
            { type: 'js', name: 'chart.min.js', path: '/static/js/lib/chart.min.js', backup: '/direct-static/js/lib/chart.min.js', cdn: 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js' }
        ];

        // 检查并加载每个文件
        coreStaticFiles.forEach(file => {
            if (file.type === 'css') {
                // 检查CSS是否已加载
                let isLoaded = false;
                const styles = document.styleSheets;
                for (let i = 0; i < styles.length; i++) {
                    if (styles[i].href && (styles[i].href.includes(file.name) || styles[i].href === file.path || styles[i].href === file.backup || styles[i].href === file.cdn)) {
                        isLoaded = true;
                        break;
                    }
                }

                if (!isLoaded) {
                    console.log(`${file.name} 未加载，尝试加载`);

                    // 尝试加载
                    const link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = file.path;

                    link.onerror = function() {
                        console.warn(`${file.path} 加载失败，尝试备用路径`);
                        link.href = file.backup;

                        link.onerror = function() {
                            console.warn(`${file.backup} 加载失败，尝试CDN`);
                            link.href = file.cdn;
                        };
                    };

                    document.head.appendChild(link);
                }
            } else if (file.type === 'js') {
                // 检查JS是否已加载
                let isLoaded = false;

                // 特殊检查
                if (file.name === 'chart.min.js' && typeof Chart !== 'undefined') {
                    isLoaded = true;
                } else if (file.name === 'jquery.min.js' && typeof jQuery !== 'undefined') {
                    isLoaded = true;
                } else if (file.name === 'bootstrap.bundle.min.js' && typeof bootstrap !== 'undefined') {
                    isLoaded = true;
                } else {
                    // 检查所有脚本
                    const scripts = document.scripts;
                    for (let i = 0; i < scripts.length; i++) {
                        if (scripts[i].src && (scripts[i].src.includes(file.name) || scripts[i].src === file.path || scripts[i].src === file.backup || scripts[i].src === file.cdn)) {
                            isLoaded = true;
                            break;
                        }
                    }
                }

                if (!isLoaded) {
                    console.log(`${file.name} 未加载，尝试加载`);

                    // 尝试加载
                    const script = document.createElement('script');
                    script.src = file.path;

                    script.onerror = function() {
                        console.warn(`${file.path} 加载失败，尝试备用路径`);
                        script.src = file.backup;

                        script.onerror = function() {
                            console.warn(`${file.backup} 加载失败，尝试CDN`);
                            script.src = file.cdn;
                        };
                    };

                    document.head.appendChild(script);
                }
            }
        });
    }

    // 应用页面特定修复
    function applyPageSpecificFixes() {
        // 检查当前页面路径
        const path = window.location.pathname;

        // 修复分析页面
        if (path.includes('/novel/') && path.includes('/analysis/')) {
            fixAnalysisPage();
        }

        // 修复小说页面
        if (path.match(/\/novel\/\d+$/) || path === '/novel/list') {
            fixNovelPage();
        }

        // 修复climax_pacing页面
        if (path.includes('climax_pacing')) {
            fixClimaxPacingPage();
        }
    }

    // 安全地销毁图表
    function safeDestroyChart(canvas) {
        try {
            // 尝试获取与canvas关联的图表实例
            if (typeof Chart !== 'undefined') {
                const existingChart = Chart.getChart(canvas);
                if (existingChart) {
                    console.log('销毁现有图表实例');
                    existingChart.destroy();
                    return true;
                }
            }

            // 检查全局存储的实例
            const canvasId = canvas.id;
            if (canvasId && window.chartInstances && window.chartInstances[canvasId]) {
                console.log(`销毁ID为 ${canvasId} 的存储图表实例`);
                window.chartInstances[canvasId].destroy();
                delete window.chartInstances[canvasId];
                return true;
            }

            return false;
        } catch (e) {
            console.error('销毁图表时出错:', e);
            return false;
        }
    }

    // 安全地创建图表
    function safeCreateChart(canvas, config) {
        try {
            // 先销毁现有图表
            safeDestroyChart(canvas);

            // 创建新图表
            const ctx = canvas.getContext('2d');
            const chart = new Chart(ctx, config);

            // 存储图表实例
            window.chartInstances[canvas.id] = chart;

            return chart;
        } catch (e) {
            console.error('创建图表时出错:', e);

            // 如果是Canvas已在使用的错误，尝试更激进的修复
            if (e.message && e.message.includes('Canvas is already in use')) {
                console.log('检测到Canvas已在使用错误，尝试更激进的修复');

                try {
                    // 创建新的canvas元素替换旧的
                    const oldCanvas = canvas;
                    const newCanvas = document.createElement('canvas');
                    newCanvas.id = oldCanvas.id;
                    newCanvas.width = oldCanvas.width;
                    newCanvas.height = oldCanvas.height;
                    newCanvas.className = oldCanvas.className;

                    // 替换canvas
                    oldCanvas.parentNode.replaceChild(newCanvas, oldCanvas);

                    // 重新创建图表
                    const ctx = newCanvas.getContext('2d');
                    const chart = new Chart(ctx, config);

                    // 存储图表实例
                    window.chartInstances[newCanvas.id] = chart;

                    return chart;
                } catch (e2) {
                    console.error('激进修复也失败:', e2);
                }
            }

            return null;
        }
    }

    // 修复climax_pacing页面
    function fixClimaxPacingPage() {
        console.log('应用climax_pacing页面特定修复');

        // 确保图表正确初始化
        setTimeout(() => {
            // 查找所有canvas元素
            const canvases = document.querySelectorAll('canvas');
            console.log(`找到 ${canvases.length} 个canvas元素`);

            canvases.forEach(function(canvas) {
                try {
                    // 销毁现有图表
                    safeDestroyChart(canvas);

                    // 根据canvas ID创建适当的图表
                    if (canvas.id === 'radarChart') {
                        // 创建雷达图配置
                        const config = {
                            type: 'radar',
                            data: {
                                labels: ['高潮铺垫', '紧张感营造', '情节转折', '角色反应', '节奏控制', '情感渲染'],
                                datasets: [{
                                    label: '高潮节奏分析评分',
                                    data: [85, 80, 90, 75, 85, 70],
                                    fill: true,
                                    backgroundColor: 'rgba(74, 107, 223, 0.2)',
                                    borderColor: 'rgb(74, 107, 223)',
                                    pointBackgroundColor: 'rgb(74, 107, 223)',
                                    pointBorderColor: '#fff',
                                    pointHoverBackgroundColor: '#fff',
                                    pointHoverBorderColor: 'rgb(74, 107, 223)'
                                }]
                            },
                            options: {
                                elements: {
                                    line: {
                                        borderWidth: 3
                                    }
                                },
                                scales: {
                                    r: {
                                        angleLines: {
                                            display: true
                                        },
                                        suggestedMin: 0,
                                        suggestedMax: 100
                                    }
                                }
                            }
                        };

                        // 创建图表
                        safeCreateChart(canvas, config);
                    } else if (canvas.id === 'barChart') {
                        // 创建柱状图配置
                        const config = {
                            type: 'bar',
                            data: {
                                labels: ['高潮铺垫', '紧张感营造', '情节转折', '角色反应', '节奏控制', '情感渲染'],
                                datasets: [{
                                    label: '高潮节奏分析指标',
                                    data: [85, 80, 90, 75, 85, 70],
                                    backgroundColor: [
                                        'rgba(75, 192, 192, 0.2)',
                                        'rgba(255, 99, 132, 0.2)',
                                        'rgba(54, 162, 235, 0.2)',
                                        'rgba(255, 206, 86, 0.2)',
                                        'rgba(153, 102, 255, 0.2)',
                                        'rgba(255, 159, 64, 0.2)'
                                    ],
                                    borderColor: [
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)'
                                    ],
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        max: 100
                                    }
                                }
                            }
                        };

                        // 创建图表
                        safeCreateChart(canvas, config);
                    }
                } catch (e) {
                    console.error(`处理canvas时出错:`, e);
                }
            });
        }, 1000);
    }

    // 修复分析页面
    function fixAnalysisPage() {
        console.log('应用分析页面特定修复');

        // 修复replaceChild错误
        fixReplaceChildError();

        // 修复JSON.parse错误
        fixJSONParseError();

        // 确保图表正确初始化
        setTimeout(() => {
            // 查找所有canvas元素
            const canvases = document.querySelectorAll('canvas');
            console.log(`找到 ${canvases.length} 个canvas元素`);

            canvases.forEach(function(canvas) {
                try {
                    // 销毁现有图表
                    safeDestroyChart(canvas);

                    // 获取当前页面的维度
                    const pathParts = window.location.pathname.split('/');
                    const dimension = pathParts[pathParts.length - 1];
                    console.log(`当前维度: ${dimension}`);

                    // 根据canvas ID创建适当的图表
                    if (canvas.id === 'radarChart') {
                        // 创建雷达图配置
                        const config = {
                            type: 'radar',
                            data: {
                                labels: ['指标1', '指标2', '指标3', '指标4', '指标5', '指标6'],
                                datasets: [{
                                    label: '分析评分',
                                    data: [85, 80, 90, 75, 85, 70],
                                    fill: true,
                                    backgroundColor: 'rgba(74, 107, 223, 0.2)',
                                    borderColor: 'rgb(74, 107, 223)',
                                    pointBackgroundColor: 'rgb(74, 107, 223)',
                                    pointBorderColor: '#fff',
                                    pointHoverBackgroundColor: '#fff',
                                    pointHoverBorderColor: 'rgb(74, 107, 223)'
                                }]
                            },
                            options: {
                                elements: {
                                    line: {
                                        borderWidth: 3
                                    }
                                },
                                scales: {
                                    r: {
                                        angleLines: {
                                            display: true
                                        },
                                        suggestedMin: 0,
                                        suggestedMax: 100
                                    }
                                }
                            }
                        };

                        // 根据维度调整标签
                        if (dimension === 'character_relationships') {
                            config.data.labels = ['主要人物数', '次要人物数', '关系复杂度', '关系变化', '描写深度', '一致性'];
                            config.data.datasets[0].label = '人物关系分析评分';
                        } else if (dimension === 'language_style') {
                            config.data.labels = ['词汇丰富度', '句式多样性', '修辞手法', '语言风格', '表达清晰度', '情感表达'];
                            config.data.datasets[0].label = '语言风格分析评分';
                        } else if (dimension === 'rhythm_pacing') {
                            config.data.labels = ['节奏变化', '情节推进', '紧张感营造', '松弛感处理', '高潮设置', '过渡处理'];
                            config.data.datasets[0].label = '节奏分析评分';
                        } else if (dimension === 'structure') {
                            config.data.labels = ['结构完整性', '章节安排', '情节连贯性', '伏笔设置', '高潮处理', '结局处理'];
                            config.data.datasets[0].label = '结构分析评分';
                        } else if (dimension === 'climax_pacing') {
                            config.data.labels = ['高潮铺垫', '紧张感营造', '情节转折', '角色反应', '节奏控制', '情感渲染'];
                            config.data.datasets[0].label = '高潮节奏分析评分';
                        }

                        // 创建图表
                        safeCreateChart(canvas, config);
                    } else if (canvas.id === 'barChart') {
                        // 创建柱状图配置
                        const config = {
                            type: 'bar',
                            data: {
                                labels: ['指标1', '指标2', '指标3', '指标4', '指标5', '指标6'],
                                datasets: [{
                                    label: '分析指标',
                                    data: [85, 80, 90, 75, 85, 70],
                                    backgroundColor: [
                                        'rgba(75, 192, 192, 0.2)',
                                        'rgba(255, 99, 132, 0.2)',
                                        'rgba(54, 162, 235, 0.2)',
                                        'rgba(255, 206, 86, 0.2)',
                                        'rgba(153, 102, 255, 0.2)',
                                        'rgba(255, 159, 64, 0.2)'
                                    ],
                                    borderColor: [
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)'
                                    ],
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        max: 100
                                    }
                                }
                            }
                        };

                        // 根据维度调整标签
                        if (dimension === 'character_relationships') {
                            config.data.labels = ['主要人物数', '次要人物数', '关系复杂度', '关系变化', '描写深度', '一致性'];
                            config.data.datasets[0].label = '人物关系分析指标';
                        } else if (dimension === 'language_style') {
                            config.data.labels = ['词汇丰富度', '句式多样性', '修辞手法', '语言风格', '表达清晰度', '情感表达'];
                            config.data.datasets[0].label = '语言风格分析指标';
                        } else if (dimension === 'rhythm_pacing') {
                            config.data.labels = ['节奏变化', '情节推进', '紧张感营造', '松弛感处理', '高潮设置', '过渡处理'];
                            config.data.datasets[0].label = '节奏分析指标';
                        } else if (dimension === 'structure') {
                            config.data.labels = ['结构完整性', '章节安排', '情节连贯性', '伏笔设置', '高潮处理', '结局处理'];
                            config.data.datasets[0].label = '结构分析指标';
                        } else if (dimension === 'climax_pacing') {
                            config.data.labels = ['高潮铺垫', '紧张感营造', '情节转折', '角色反应', '节奏控制', '情感渲染'];
                            config.data.datasets[0].label = '高潮节奏分析指标';
                        }

                        // 创建图表
                        safeCreateChart(canvas, config);
                    }
                } catch (e) {
                    console.error(`处理canvas时出错:`, e);
                }
            });
        }, 1000);
    }

    // 修复小说页面
    function fixNovelPage() {
        console.log('应用小说页面特定修复');

        // 修复replaceChild错误
        fixReplaceChildError();

        // 修复JSON.parse错误
        fixJSONParseError();
    }

    // 修复replaceChild错误
    function fixReplaceChildError() {
        // 保存原始的replaceChild方法
        const originalReplaceChild = Node.prototype.replaceChild;

        // 重写replaceChild方法
        Node.prototype.replaceChild = function(newChild, oldChild) {
            try {
                return originalReplaceChild.call(this, newChild, oldChild);
            } catch (e) {
                console.error('replaceChild错误:', e.message);

                // 尝试修复：如果失败，尝试先移除旧节点，再添加新节点
                try {
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }
                    this.appendChild(newChild);
                    return newChild;
                } catch (e2) {
                    console.error('替代方法也失败:', e2.message);
                    throw e; // 重新抛出原始错误
                }
            }
        };
    }

    // 修复JSON.parse错误
    function fixJSONParseError() {
        // 保存原始的JSON.parse方法
        const originalJSONParse = JSON.parse;

        // 重写JSON.parse方法
        JSON.parse = function(text, reviver) {
            try {
                // 尝试使用原始方法解析
                return originalJSONParse(text, reviver);
            } catch (e) {
                console.error('JSON.parse错误:', e.message);

                // 检查是否是位置4476附近的错误
                if (e.message.includes('position 447') ||
                    (e.message.includes("Expected ',' or '}'") && text.includes('character_relationships'))) {
                    console.log('检测到位置4476特定错误，应用修复');

                    // 尝试修复JSON字符串
                    try {
                        // 简单修复：如果是未终止的字符串，添加引号
                        if (e.message.includes('Unterminated string')) {
                            text = text + '"';
                        }

                        // 如果是缺少逗号，尝试在错误位置附近添加逗号
                        if (e.message.includes("Expected ',' or '}'")) {
                            const position = parseInt(e.message.match(/position (\d+)/)[1]);
                            const before = text.substring(0, position);
                            const after = text.substring(position);
                            text = before + ',' + after;
                        }

                        // 再次尝试解析
                        return originalJSONParse(text, reviver);
                    } catch (e2) {
                        console.error('修复JSON失败，返回空对象:', e2);
                        return {};
                    }
                }

                // 如果不是特定错误，返回空对象
                console.error('无法解析JSON，返回空对象');
                return {};
            }
        };
    }

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始执行综合修复');

        // 修复静态文件加载问题
        fixStaticFileLoading();

        // 修复replaceChild错误
        fixReplaceChildError();

        // 修复JSON.parse错误
        fixJSONParseError();

        // 应用页面特定修复
        applyPageSpecificFixes();

        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                // 处理Chart.js相关错误
                if (event.error.message.includes('Chart with ID') ||
                    event.error.message.includes('Canvas is already in use')) {
                    console.error('捕获到Chart.js相关错误:', event.error.message);

                    // 尝试修复
                    setTimeout(applyPageSpecificFixes, 100);

                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }

                // 处理replaceChild错误
                if (event.error.message.includes('Failed to execute \'replaceChild\' on \'Node\'')) {
                    console.error('捕获到replaceChild错误:', event.error.message);

                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }

                // 处理JSON.parse错误
                if (event.error.message.includes('JSON.parse')) {
                    console.error('捕获到JSON.parse错误:', event.error.message);

                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);
    });
})();
