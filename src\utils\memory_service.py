"""
九猫系统内存监控和日志清理服务
定期监控内存使用情况并清理日志，减轻系统内存压力
"""
import os
import sys
import time
import json
import logging
import threading
import gc
from datetime import datetime

logger = logging.getLogger(__name__)

# 外部存储根目录
EXTERNAL_STORAGE_ROOT = os.environ.get('EXTERNAL_STORAGE_ROOT', 'E:\\艹，又来一次\\九猫')

# 内存统计目录
MEMORY_STATS_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "memory_stats")

# 确保目录存在
os.makedirs(MEMORY_STATS_DIR, exist_ok=True)

class MemoryService(threading.Thread):
    """内存监控和日志清理服务"""
    
    def __init__(self, check_interval=60, cleanup_interval=3600):
        """
        初始化内存服务
        
        Args:
            check_interval: 内存检查间隔（秒）
            cleanup_interval: 日志清理间隔（秒）
        """
        super().__init__(daemon=True)
        self.check_interval = check_interval
        self.cleanup_interval = cleanup_interval
        self.running = True
        self.last_cleanup_time = 0
    
    def run(self):
        """线程运行函数"""
        logger.info(f"内存监控和日志清理服务已启动，检查间隔: {self.check_interval}秒，清理间隔: {self.cleanup_interval}秒")
        
        while self.running:
            try:
                # 检查内存使用情况
                self.check_memory()
                
                # 检查是否需要清理日志
                current_time = time.time()
                if current_time - self.last_cleanup_time > self.cleanup_interval:
                    self.cleanup_logs()
                    self.last_cleanup_time = current_time
            except Exception as e:
                logger.error(f"内存服务出错: {str(e)}")
            
            # 等待下一次检查
            time.sleep(self.check_interval)
    
    def check_memory(self):
        """检查内存使用情况"""
        try:
            import psutil
            
            # 获取内存信息
            memory = psutil.virtual_memory()
            process = psutil.Process(os.getpid())
            process_memory = process.memory_info()
            
            # 创建内存统计数据
            stats = {
                "timestamp": time.time(),
                "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "system_total": memory.total,
                "system_available": memory.available,
                "system_used": memory.used,
                "system_percent": memory.percent,
                "process_rss": process_memory.rss,
                "process_vms": process_memory.vms,
                "process_rss_mb": process_memory.rss / (1024 * 1024),
                "process_vms_mb": process_memory.vms / (1024 * 1024)
            }
            
            # 保存内存统计数据
            self.save_memory_stats(stats)
            
            # 检查内存使用率
            if memory.percent > 80:
                logger.warning(f"系统内存使用率较高: {memory.percent}%，尝试释放内存")
                self.free_memory()
            
            # 检查进程内存使用
            if process_memory.rss > 500 * 1024 * 1024:  # 500MB
                logger.warning(f"进程内存使用较高: {process_memory.rss / (1024 * 1024):.2f}MB，尝试释放内存")
                self.free_memory()
        
        except ImportError:
            logger.warning("psutil未安装，无法监控内存使用情况")
    
    def save_memory_stats(self, stats):
        """保存内存统计数据"""
        try:
            # 创建文件名
            timestamp = int(time.time())
            stats_file = os.path.join(MEMORY_STATS_DIR, f"memory_{timestamp}.json")
            
            # 保存统计数据
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            
            # 清理旧的统计文件（只保留最近100个）
            self.cleanup_stats_files()
        
        except Exception as e:
            logger.error(f"保存内存统计数据时出错: {str(e)}")
    
    def cleanup_stats_files(self):
        """清理旧的统计文件"""
        try:
            import glob
            
            # 获取所有统计文件
            stats_files = sorted(glob.glob(os.path.join(MEMORY_STATS_DIR, "memory_*.json")))
            
            # 如果文件数量超过100，删除最旧的文件
            if len(stats_files) > 100:
                for old_file in stats_files[:-100]:
                    try:
                        os.remove(old_file)
                    except (PermissionError, OSError):
                        pass
        
        except Exception as e:
            logger.error(f"清理统计文件时出错: {str(e)}")
    
    def free_memory(self):
        """释放内存"""
        # 强制垃圾回收
        gc.collect()
        
        # 清理SQLAlchemy缓存
        self.cleanup_sqlalchemy_cache()
        
        # 清理日志缓冲区
        self.flush_log_handlers()
        
        logger.info("内存清理完成")
    
    def cleanup_sqlalchemy_cache(self):
        """清理SQLAlchemy缓存"""
        try:
            # 尝试清理会话
            try:
                from src.db.connection import Session
                Session.remove()
                logger.info("已清理SQLAlchemy会话")
            except (ImportError, AttributeError):
                pass
            
            # 尝试清理引擎缓存
            try:
                from sqlalchemy import pool
                pool._refs.clear()
                logger.info("已清理SQLAlchemy连接池引用")
            except (ImportError, AttributeError):
                pass
        
        except Exception as e:
            logger.error(f"清理SQLAlchemy缓存时出错: {str(e)}")
    
    def flush_log_handlers(self):
        """刷新日志处理器"""
        for handler in logging.getLogger().handlers:
            try:
                handler.flush()
            except:
                pass
    
    def cleanup_logs(self):
        """清理日志"""
        try:
            # 导入日志重定向模块
            from src.utils.log_redirector import cleanup_old_logs
            
            # 清理旧日志
            cleanup_old_logs(max_days=7)
            
            logger.info("日志清理完成")
        
        except ImportError:
            logger.warning("日志重定向模块不可用，无法清理日志")
    
    def stop(self):
        """停止服务"""
        self.running = False
        logger.info("内存监控和日志清理服务已停止")

# 全局服务实例
memory_service = None

def start_memory_service(check_interval=60, cleanup_interval=3600):
    """启动内存监控和日志清理服务"""
    global memory_service
    
    if memory_service and memory_service.is_alive():
        logger.warning("内存监控和日志清理服务已在运行")
        return
    
    memory_service = MemoryService(check_interval, cleanup_interval)
    memory_service.start()
    
    logger.info("内存监控和日志清理服务已启动")

def stop_memory_service():
    """停止内存监控和日志清理服务"""
    global memory_service
    
    if memory_service:
        memory_service.stop()
        memory_service = None
        
        logger.info("内存监控和日志清理服务已停止")
