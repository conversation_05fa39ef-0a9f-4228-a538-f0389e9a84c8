
"""
测试 sync_to_chapters API 端点
"""
import requests
import json

def test_sync_api():
    """测试 sync_to_chapters API 端点"""
    url = "http://localhost:5001/api/novel/40/sync_to_chapters"
    headers = {
        "Content-Type": "application/json"
    }
    data = {
        "dimension": None  # 同步所有维度
    }
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("API 端点测试成功")
            return True
        else:
            print("API 端点测试失败")
            return False
    except Exception as e:
        print(f"测试时出错: {str(e)}")
        return False

if __name__ == "__main__":
    test_sync_api()
