/**
 * 九猫系统模板路径修复脚本
 * 用于修复模板相关API路径问题
 */

(function() {
    console.log('[模板路径修复] 初始化...');

    // 等待DOM加载完成
    function onDOMReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    // 修复模板相关API路径
    function fixTemplateAPIPaths() {
        console.log('[模板路径修复] 开始修复模板相关API路径...');

        // 拦截所有AJAX请求
        if (typeof jQuery !== 'undefined' && jQuery.ajax) {
            const originalAjax = jQuery.ajax;

            jQuery.ajax = function(options) {
                if (typeof options === 'string') {
                    options = { url: options };
                }

                if (options.url) {
                    // 检查是否是模板相关路径
                    if (options.url.includes('/novel/') && options.url.includes('/template/')) {
                        const match = options.url.match(/\/novel\/(\d+)\/template\/([^\/\?]+)/);
                        if (match && match[1] && match[2]) {
                            const novelId = match[1];
                            const templateType = match[2];

                            // 修复路径 - 直接使用v3_api.py中定义的路径格式
                            const fixedUrl = `/api/novel/${novelId}/book_template/${templateType}`;
                            console.log(`[模板路径修复] 修复模板路径: ${options.url} -> ${fixedUrl}`);
                            options.url = fixedUrl;
                        }
                    }
                }

                return originalAjax.apply(this, arguments);
            };

            console.log('[模板路径修复] 已拦截jQuery.ajax方法');
        } else {
            console.warn('[模板路径修复] jQuery未定义或jQuery.ajax不可用，无法修复模板路径');
        }

        // 修复fetch请求
        if (typeof window.fetch === 'function') {
            const originalFetch = window.fetch;

            window.fetch = function(input, init) {
                if (typeof input === 'string' && input.includes('/novel/') && input.includes('/template/')) {
                    const match = input.match(/\/novel\/(\d+)\/template\/([^\/\?]+)/);
                    if (match && match[1] && match[2]) {
                        const novelId = match[1];
                        const templateType = match[2];

                        // 修复路径 - 直接使用v3_api.py中定义的路径格式
                        const fixedUrl = `/api/novel/${novelId}/book_template/${templateType}`;
                        console.log(`[模板路径修复] 修复模板路径(fetch): ${input} -> ${fixedUrl}`);
                        input = fixedUrl;
                    }
                }

                return originalFetch.call(this, input, init);
            };

            console.log('[模板路径修复] 已拦截fetch方法');
        }

        // 修复XMLHttpRequest
        if (typeof window.XMLHttpRequest === 'function') {
            const originalOpen = XMLHttpRequest.prototype.open;

            XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                if (typeof url === 'string' && url.includes('/novel/') && url.includes('/template/')) {
                    const match = url.match(/\/novel\/(\d+)\/template\/([^\/\?]+)/);
                    if (match && match[1] && match[2]) {
                        const novelId = match[1];
                        const templateType = match[2];

                        // 修复路径
                        const fixedUrl = `/api/template/${novelId}/${templateType}`;
                        console.log(`[模板路径修复] 修复模板路径(XHR): ${url} -> ${fixedUrl}`);
                        url = fixedUrl;
                    }
                }

                return originalOpen.call(this, method, url, async, user, password);
            };

            console.log('[模板路径修复] 已拦截XMLHttpRequest.open方法');
        }

        console.log('[模板路径修复] 模板路径修复完成');
    }

    // 修复模板相关链接
    function fixTemplateLinks() {
        console.log('[模板路径修复] 开始修复模板相关链接...');

        // 查找并修复所有模板相关链接
        const links = document.querySelectorAll('a[href*="/novel/"][href*="/template/"]');
        links.forEach(link => {
            const href = link.getAttribute('href');
            const match = href.match(/\/novel\/(\d+)\/template\/([^\/\?]+)/);

            if (match && match[1] && match[2]) {
                const novelId = match[1];
                const templateType = match[2];

                // 修复链接 - 直接使用v3_api.py中定义的路径格式
                const fixedHref = `/api/novel/${novelId}/book_template/${templateType}`;
                console.log(`[模板路径修复] 修复模板链接: ${href} -> ${fixedHref}`);
                link.setAttribute('href', fixedHref);
            }
        });

        console.log('[模板路径修复] 模板相关链接修复完成');
    }

    // 修复模板相关表单
    function fixTemplateForms() {
        console.log('[模板路径修复] 开始修复模板相关表单...');

        // 查找并修复所有模板相关表单
        const forms = document.querySelectorAll('form[action*="/novel/"][action*="/template/"]');
        forms.forEach(form => {
            const action = form.getAttribute('action');
            const match = action.match(/\/novel\/(\d+)\/template\/([^\/\?]+)/);

            if (match && match[1] && match[2]) {
                const novelId = match[1];
                const templateType = match[2];

                // 修复表单action - 直接使用v3_api.py中定义的路径格式
                const fixedAction = `/api/novel/${novelId}/book_template/${templateType}`;
                console.log(`[模板路径修复] 修复模板表单action: ${action} -> ${fixedAction}`);
                form.setAttribute('action', fixedAction);
            }
        });

        console.log('[模板路径修复] 模板相关表单修复完成');
    }

    // 监听DOM变化，动态修复模板相关元素
    function observeDOM() {
        const observer = new MutationObserver(mutations => {
            let shouldFixLinks = false;
            let shouldFixForms = false;

            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];

                        if (node.nodeType === 1) { // 元素节点
                            if (node.tagName === 'A' && node.href && node.href.includes('/novel/') && node.href.includes('/template/')) {
                                shouldFixLinks = true;
                            } else if (node.tagName === 'FORM' && node.action && node.action.includes('/novel/') && node.action.includes('/template/')) {
                                shouldFixForms = true;
                            } else if (node.querySelectorAll) {
                                if (node.querySelectorAll('a[href*="/novel/"][href*="/template/"]').length > 0) {
                                    shouldFixLinks = true;
                                }
                                if (node.querySelectorAll('form[action*="/novel/"][action*="/template/"]').length > 0) {
                                    shouldFixForms = true;
                                }
                            }
                        }
                    }
                }
            });

            if (shouldFixLinks) {
                fixTemplateLinks();
            }

            if (shouldFixForms) {
                fixTemplateForms();
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('[模板路径修复] DOM观察器已启动');
    }

    // 初始化
    onDOMReady(() => {
        fixTemplateAPIPaths();
        fixTemplateLinks();
        fixTemplateForms();
        observeDOM();
    });
})();
