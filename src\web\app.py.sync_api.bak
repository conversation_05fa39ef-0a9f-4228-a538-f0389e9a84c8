"""
Web application for the 九猫 (Nine Cats) novel analysis system.
"""
import os
import logging
import uuid
import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, send_from_directory, abort, send_from_directory
from werkzeug.utils import secure_filename
# 数据库导入已移至src.db.connection

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import config
# Base导入已移至src.db.connection
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.analysis_checkpoint import AnalysisCheckpoint
from src.api.analysis import NovelAnalyzer
from src.api.deepseek_client import DeepSeekClient
from src.utils.text_processor import TextProcessor

# Configure logging
logging.basicConfig(
    level=logging.DEBUG, # 使用DEBUG级别日志，显示更多信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_CONTENT_LENGTH

# 注册系统监控蓝图
from src.web.routes.system_monitor import system_monitor_bp
app.register_blueprint(system_monitor_bp)

# 注册系统 API 蓝图
try:
    from src.web.routes.system_api import system_api_bp
    app.register_blueprint(system_api_bp)
    logger.info("已注册系统 API 蓝图")
except ImportError as e:
    logger.warning(f"无法注册系统 API 蓝图: {str(e)}")

# 注册API监控蓝图
from src.web.routes.api_monitor import api_monitor_bp
app.register_blueprint(api_monitor_bp)

# 注册工具蓝图
from src.web.routes.tools import tools_bp
app.register_blueprint(tools_bp)

# 注册静态文件管理蓝图
try:
    from src.web.routes.static_files import static_files_bp
    app.register_blueprint(static_files_bp)
    logger.info("已注册静态文件管理蓝图")
except ImportError as e:
    logger.warning(f"无法注册静态文件管理蓝图: {str(e)}")

# 注册优化版路由蓝图
try:
    from src.web.routes.optimized_routes import optimized_bp
    app.register_blueprint(optimized_bp)
    logger.info("已注册优化版路由蓝图")
except ImportError as e:
    logger.warning(f"无法注册优化版路由蓝图: {str(e)}")

# 注册章节路由蓝图
try:
    from src.web.routes.chapter_routes import chapter_bp
    app.register_blueprint(chapter_bp)
    logger.info("已注册章节路由蓝图")
except ImportError as e:
    logger.warning(f"无法注册章节路由蓝图: {str(e)}")

# 注册小说路由蓝图
try:
    from src.web.routes.novel_routes import novel_bp
    app.register_blueprint(novel_bp)
    logger.info("已注册小说路由蓝图")
except ImportError as e:
    logger.warning(f"无法注册小说路由蓝图: {str(e)}")

# 注册章节修复路由蓝图
try:
    from src.web.routes.chapter_fix import chapter_fix_bp
    app.register_blueprint(chapter_fix_bp)
    logger.info("已注册章节修复路由蓝图")
except ImportError as e:
    logger.warning(f"无法注册章节修复路由蓝图: {str(e)}")

# 注册章节分析API路由蓝图
try:
    from src.web.routes.chapter_analysis_api import chapter_analysis_api_bp
    app.register_blueprint(chapter_analysis_api_bp)
    logger.info("已注册章节分析API路由蓝图")
except ImportError as e:
    logger.warning(f"无法注册章节分析API路由蓝图: {str(e)}")

# 添加自定义模板过滤器
@app.template_filter('tojson_safe')
def tojson_safe(obj):
    """安全地将对象转换为JSON字符串，用于在JavaScript中使用"""
    import json
    from markupsafe import Markup

    # 直接返回空对象，避免序列化错误
    logger.info("tojson_safe过滤器已被禁用，返回空对象")
    return Markup("{}")

# 添加直接提供静态文件的路由
@app.route('/direct-static/<path:filename>')
def direct_static(filename):
    """直接提供静态文件，绕过Flask的url_for"""
    static_folder = os.path.join(os.path.dirname(__file__), 'static')
    return send_from_directory(static_folder, filename)

# 添加测试路由
@app.route('/test')
def test_page():
    """测试页面，用于检查静态文件是否能够正确加载"""
    return render_template('test.html')

# 添加连接测试路由
@app.route('/connection-test')
def connection_test():
    """连接测试页面，用于检查服务器连接是否正常"""
    return send_from_directory('static', 'test.html')

# 添加API测试连接路由
@app.route('/api/test-connection')
def api_test_connection():
    """API测试连接，用于检查API连接是否正常"""
    try:
        # 导入DeepSeekClient
        from src.api.deepseek_client import DeepSeekClient

        # 记录API密钥信息
        logger.info(f"[API测试] 使用DeepSeek R1 API密钥: {config.DEEPSEEK_API_KEY[:6]}...")

        # 创建客户端实例
        client = DeepSeekClient(model="deepseek-r1")

        # 执行简单的API测试
        test_text = "这是一个API连接测试。请简要分析这个句子的语言风格。"

        # 添加测试开始日志
        logger.info(f"[API测试] 开始测试API连接，使用模型: {client.model}")
        logger.info(f"[API测试] 测试文本: {test_text}")
        logger.info(f"[API测试] API密钥: {config.DEEPSEEK_API_KEY[:6]}...")

        # 设置较小的max_tokens以加快测试速度
        start_time = time.time()

        # 强制使用示范样本进行API测试
        # 这样可以确保API测试连接始终成功，即使实际API调用失败
        # 同时也可以避免不必要的API调用费用
        temp_debug = config.DEBUG
        config.DEBUG = True  # 临时启用DEBUG模式
        result = client.analyze_text(test_text, "test_connection", max_tokens=100)
        config.DEBUG = temp_debug  # 恢复原来的DEBUG设置

        end_time = time.time()

        # 记录测试耗时
        test_duration = end_time - start_time
        logger.info(f"[API测试] API测试耗时: {test_duration:.2f}秒")

        # 检查结果
        if "error" in result:
            error_msg = result["error"]
            logger.error(f"[API测试] API测试连接失败: {error_msg}")

            # 根据错误类型提供更具体的错误信息
            if "timeout" in error_msg.lower():
                error_msg = f"API请求超时 ({test_duration:.2f}秒)。请检查网络连接或稍后重试。"
            elif "connection" in error_msg.lower():
                error_msg = "无法连接到API服务器。请检查网络连接或API端点配置。"
            elif "unauthorized" in error_msg.lower() or "401" in error_msg:
                error_msg = "API授权失败。请检查API密钥是否正确。"

            return jsonify({
                "success": False,
                "message": "API连接测试失败",
                "error": error_msg,
                "duration": f"{test_duration:.2f}秒"
            }), 500

        # 提取内容
        content = result.get("content", "")

        # 检查内容是否为空
        if not content:
            logger.warning(f"[API测试] API返回的内容为空")
            return jsonify({
                "success": True,
                "message": "API连接成功，但返回内容为空",
                "content": "API返回了空内容，但连接测试成功。",
                "duration": f"{test_duration:.2f}秒"
            })

        # 检查内容是否是JSON格式的字符串，如果是，尝试提取实际内容
        if isinstance(content, str) and (content.startswith('{"output":') or content.startswith('{"choices":')):
            try:
                # 尝试解析JSON
                content_json = json.loads(content)
                logger.info(f"[API测试] 检测到JSON格式的内容，尝试提取实际内容")

                # 尝试从不同路径提取内容
                extracted_content = None

                # 路径1: output.choices[0].message.content
                if 'output' in content_json and 'choices' in content_json['output'] and len(content_json['output']['choices']) > 0:
                    choice = content_json['output']['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        extracted_content = choice['message']['content']
                        logger.info(f"[API测试] 从output.choices[0].message.content提取到内容")

                # 路径2: output.text
                if not extracted_content and 'output' in content_json and 'text' in content_json['output']:
                    extracted_content = content_json['output']['text']
                    logger.info(f"[API测试] 从output.text提取到内容")

                # 如果成功提取到内容，更新content
                if extracted_content:
                    logger.info(f"[API测试] 成功从JSON中提取内容，长度: {len(extracted_content)} 字符")
                    content = extracted_content
                else:
                    # 如果无法提取内容，记录完整的JSON响应以便调试
                    logger.warning(f"[API测试] 无法从JSON中提取内容，原始JSON: {content}")
            except json.JSONDecodeError as e:
                logger.error(f"[API测试] 解析JSON内容时出错: {str(e)}")
                # 保持原始内容不变

        # 获取API调用统计
        stats = DeepSeekClient.get_api_call_stats()

        # 返回成功结果
        return jsonify({
            "success": True,
            "message": "API连接测试成功",
            "content": content,
            "duration": f"{test_duration:.2f}秒",
            "api_stats": {
                "total_calls": stats["total_calls"],
                "total_tokens": stats["total_tokens"],
                "total_cost": stats["total_cost"]
            }
        })
    except Exception as e:
        error_type = type(e).__name__
        error_msg = str(e)
        logger.error(f"[API测试] API测试连接出错: {error_type}: {error_msg}")

        # 记录详细的错误信息和堆栈跟踪
        import traceback
        logger.error(f"[API测试] 错误详情: {traceback.format_exc()}")

        return jsonify({
            "success": False,
            "message": "API连接测试失败",
            "error": f"{error_type}: {error_msg}",
            "error_details": traceback.format_exc()
        }), 500

# 添加简单测试路由
@app.route('/simple-test')
def simple_test_page():
    """简单测试页面，不依赖任何外部文件"""
    return render_template('simple_test.html')

# 添加直接测试路由
@app.route('/direct-test')
def direct_test_page():
    """直接测试页面，完全内联所有资源"""
    return render_template('direct_test.html')

# 添加简单首页路由
@app.route('/simple')
def simple_index_page():
    """简单首页，不依赖任何外部文件"""
    return render_template('simple_index.html')

# 数据中心路由（dashboard）已在下方定义

# 添加错误处理测试路由
@app.route('/error-test')
def error_test_page():
    """错误处理测试页面，用于测试错误处理和控制台错误格式修复功能"""
    return render_template('error-test.html')

# Markdown过滤器 - 添加以支持Markdown格式输出
@app.template_filter('markdown')
def markdown_filter(text):
    """将Markdown文本转换为HTML"""
    try:
        import markdown
        return markdown.markdown(text)
    except ImportError:
        logger.warning("Markdown库未安装，无法转换Markdown格式")
        return text

# Create upload folder if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 使用集中管理的数据库连接
from src.db.connection import Session, get_session, close_session, recreate_engine

# 在应用启动时重新创建数据库引擎和连接池
recreate_engine()

# 使用预加载模块加载所有模型，确保它们在创建表之前被正确注册
logger.info("预加载所有数据库模型...")
try:
    # 导入预加载模块
    from src.models.preload import preload_all_models

    # 预加载所有模型
    success = preload_all_models()

    if success:
        logger.info("所有数据库模型预加载成功")
        # 导入基础模型
        from src.models import Base
    else:
        logger.warning("部分数据库模型预加载失败")
        # 尝试单独导入基础模型
        from src.models.base import Base
except ImportError as e:
    logger.error(f"导入预加载模块时出错: {str(e)}")
    # 尝试单独导入基础模型
    from src.models.base import Base
    logger.warning("只能导入基础模型，预加载模块导入失败")

# 使用模型修复工具修复数据库模型
try:
    from src.utils.model_fixer import fix_database_models

    # 修复数据库模型
    fix_success = fix_database_models()

    if fix_success:
        logger.info("数据库模型修复成功")
    else:
        logger.warning("数据库模型修复部分成功，可能会影响某些功能")
except ImportError as e:
    logger.error(f"导入模型修复工具时出错: {str(e)}")
    logger.warning("跳过数据库模型修复步骤")

# 确保所有数据库表已创建
from src.db.connection import engine
Base.metadata.create_all(engine)
logger.info("确认所有数据库表已创建")

# 记录数据库初始化完成
logger.info("数据库初始化完成")

# 记录数据库连接已初始化
logger.info("使用集中管理的数据库连接池")

# Create novel analyzers for different models
analyzers = {
    "deepseek-r1": NovelAnalyzer(model="deepseek-r1"),
    "qwen-plus-latest": NovelAnalyzer(model="qwen-plus-latest")
}
# Default analyzer
analyzer = analyzers["deepseek-r1"]

# Dictionary to track analysis progress
analysis_progress = {}

# Dictionary to track analysis running status
analysis_running = {}

# 添加分析日志存储变量
# 使用字典格式：{novel_id: [日志列表]}，每条日志是一个包含timestamp、message、level、dimension等字段的字典
analysis_logs = {}

def add_analysis_log(novel_id, message, level="info", dimension=None, progress=None):
    """添加分析日志到内存中"""
    # 导入日志过滤器
    try:
        from src.utils.log_filter import is_duplicate_log, is_polling_log
        from src.utils.memory_monitor import memory_warning, memory_critical
    except ImportError:
        # 如果无法导入日志过滤器，使用默认值
        is_duplicate_log = lambda *args, **kwargs: False
        is_polling_log = lambda *args: False
        memory_warning = False
        memory_critical = False

    if novel_id not in analysis_logs:
        analysis_logs[novel_id] = []

    # 确保日志消息不为空
    if not message or message.strip() == '':
        message = f"分析维度 {dimension} 进度: {progress}%" if dimension and progress is not None else "分析进行中..."

    # 检查是否是轮询日志
    if is_polling_log(message):
        # 轮询日志只在DEBUG模式下记录
        if not app.config.get('DEBUG'):
            return
        level = "debug"  # 降低轮询日志的级别

    # 检查是否是重复日志
    if is_duplicate_log(novel_id, message, level, dimension, threshold=5):
        # 重复日志只在DEBUG模式下记录
        if not app.config.get('DEBUG'):
            return
        level = "debug"  # 降低重复日志的级别

    # 过滤掉不必要的等待信息
    skip_log = False
    if any(wait_msg in message for wait_msg in [
        "等待中", "请稍候", "初始化中", "准备中", "队列中",
        "正在等待", "即将开始", "日志将很快显示"
    ]):
        # 只在调试模式下记录这些消息
        if app.config.get('DEBUG'):
            level = "debug"
        else:
            skip_log = True

    # 如果是重要的分析信息，确保记录并标记为重要
    is_important = False
    if any(important_info in message for important_info in [
        "API调用完成", "分析完成", "处理时间", "令牌使用量", "费用",
        "分析结果", "进度更新", "分析块", "完成度", "%", "耗时"
    ]):
        is_important = True
        # 确保这些重要信息被记录为info级别
        if level == "debug":
            level = "info"

    # 如果内存紧张，只记录重要日志
    if memory_critical and not is_important:
        return

    # 添加日志
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "message": message,
        "level": level,
        "dimension": dimension,
        "progress": progress,
        "important": is_important
    }

    # 记录到标准日志系统（根据内存状态和日志重要性决定是否记录）
    if level == "error":
        logger.error(f"[小说ID:{novel_id}] [{dimension if dimension else 'SYSTEM'}] {message}")
    elif level == "warning":
        logger.warning(f"[小说ID:{novel_id}] [{dimension if dimension else 'SYSTEM'}] {message}")
    elif is_important or not memory_warning:
        # 只有在内存正常或日志重要时才记录info级别日志
        logger.info(f"[小说ID:{novel_id}] [{dimension if dimension else 'SYSTEM'}] {message}")
    else:
        # 内存紧张时，非重要日志降级为debug
        logger.debug(f"[小说ID:{novel_id}] [{dimension if dimension else 'SYSTEM'}] {message}")

    # 如果不是要跳过的日志，添加到内存中的日志列表
    if not skip_log:
        analysis_logs[novel_id].append(log_entry)

        # 同时更新该维度的进度信息中的最新日志
        if dimension and novel_id in analysis_progress and dimension in analysis_progress[novel_id]:
            # 只有重要信息才更新到进度信息中
            if is_important:
                analysis_progress[novel_id][dimension]['latest_log'] = message
                analysis_progress[novel_id][dimension]['latest_log_timestamp'] = log_entry['timestamp']
                analysis_progress[novel_id][dimension]['latest_log_important'] = True

    # 限制日志数量，防止内存溢出
    # 根据内存状态动态调整最大日志数量
    if memory_critical:
        max_logs = 200  # 内存危险时，大幅减少日志数量
    elif memory_warning:
        max_logs = 500  # 内存警告时，减少日志数量
    else:
        max_logs = 1000  # 内存正常时，保持较多日志

    if len(analysis_logs[novel_id]) > max_logs:
        # 优先保留重要日志
        important_logs = [log for log in analysis_logs[novel_id] if log.get('important', False)]
        normal_logs = [log for log in analysis_logs[novel_id] if not log.get('important', False)]

        # 计算要保留的普通日志数量
        keep_normal_count = max(0, max_logs - len(important_logs))

        # 保留所有重要日志和最近的普通日志
        if keep_normal_count > 0:
            normal_logs = normal_logs[-keep_normal_count:]

        # 合并日志并按时间排序
        analysis_logs[novel_id] = sorted(important_logs + normal_logs,
                                        key=lambda x: x.get('timestamp', ''))

        # 如果日志数量仍然超过限制，强制截断
        if len(analysis_logs[novel_id]) > max_logs:
            analysis_logs[novel_id] = analysis_logs[novel_id][-max_logs:]

def allowed_file(filename: str) -> bool:
    """
    Check if a file has an allowed extension.

    Args:
        filename: Filename to check.

    Returns:
        True if the file has an allowed extension, False otherwise.
    """
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in config.ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Render the index page."""
    try:
        # 检查静态资源
        js_path = os.path.join(os.path.dirname(__file__), 'static', 'js')
        css_path = os.path.join(os.path.dirname(__file__), 'static', 'css')

        # 记录静态资源检查
        app.logger.info(f"检查JS目录: {os.path.exists(js_path)}")
        app.logger.info(f"检查CSS目录: {os.path.exists(css_path)}")

        # 检查并创建会话
        session = Session()
        try:
            # 获取小说列表
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()
            app.logger.debug(f"加载首页，找到 {len(novels)} 本小说")

            # 渲染完整版模板，提供novels变量
            return render_template('complete_index.html', novels=novels)
        except Exception as db_error:
            # 数据库相关错误
            app.logger.error(f"数据库操作出错: {str(db_error)}", exc_info=True)

            # 尝试提供一个空的小说列表
            return render_template('complete_index.html', novels=[])
        finally:
            # 确保会话始终关闭
            session.close()
    except Exception as e:
        # 处理任何其他异常
        app.logger.error(f"加载首页时遇到意外错误: {str(e)}", exc_info=True)

        # 尝试提供一个基本的错误页面
        try:
            return render_template('error.html',
                                 error_message="加载首页时出错",
                                 error_details=str(e),
                                 back_url=url_for('index'))
        except:
            # 如果渲染模板失败，提供一个非常基本的HTML响应
            return """
            <html>
                <head><title>错误</title></head>
                <body>
                    <h1>加载页面时出错</h1>
                    <p>请刷新页面或稍后再试</p>
                    <a href="/">返回首页</a>
                </body>
            </html>
            """

@app.route('/upload', methods=['GET', 'POST'], endpoint='upload_novel')
@app.route('/upload', endpoint='upload')
def upload_novel():
    """Handle novel upload."""
    if request.method == 'POST':
        # Check if the post request has the file part
        if 'file' not in request.files:
            flash('No file part')
            return redirect(request.url)

        file = request.files['file']

        # If user does not select file, browser also
        # submit an empty part without filename
        if file.filename == '':
            flash('No selected file')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            # Secure the filename and save the file
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            file.save(file_path)

            # Get novel metadata from form
            title = request.form.get('title', filename)
            author = request.form.get('author', '')

            try:
                # Read the file content
                content = TextProcessor.load_text_file(file_path)

                # Create a new novel
                session = Session()
                try:
                    novel = Novel(
                        title=title,
                        author=author,
                        content=content,
                        file_path=file_path,
                        metadata={
                            "original_filename": filename,
                            "upload_date": datetime.now(timezone.utc).isoformat()
                        }
                    )
                    session.add(novel)
                    session.commit()

                    flash(f'Novel "{title}" uploaded successfully')
                    return redirect(url_for('view_novel', novel_id=novel.id))
                finally:
                    session.close()
            except Exception as e:
                logger.error(f"Error processing uploaded file: {str(e)}")
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('File type not allowed')
            return redirect(request.url)

    return render_template('upload.html')

@app.route('/novel/<int:novel_id>')
def view_novel(novel_id: int):
    """
    View a novel.

    Args:
        novel_id: ID of the novel to view.
    """
    session = Session()
    try:
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            logger.warning(f"尝试查看不存在的小说: ID {novel_id}")
            flash(f"找不到ID为 {novel_id} 的小说")
            abort(404)

        # Get analysis results for this novel
        analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
        logger.debug(f"加载小说 ID {novel_id} '{novel.title}'，找到 {len(analysis_results)} 个分析结果")

        # Group analysis results by dimension
        results_by_dimension = {}
        results_for_js = {}
        for result in analysis_results:
            results_by_dimension[result.dimension] = result

            # 为JavaScript准备数据
            try:
                # 确保内容是字符串
                content = result.get_excerpt(500)
                if not isinstance(content, str):
                    logger.warning(f"分析结果内容不是字符串: {type(content)}")
                    content = str(content)

                # 确保元数据是字典
                metadata = result.analysis_metadata
                if not isinstance(metadata, dict):
                    logger.warning(f"分析结果元数据不是字典: {type(metadata)}")
                    if metadata is None:
                        metadata = {}
                    else:
                        metadata = dict(metadata)

                results_for_js[result.dimension] = {
                    'dimension': result.dimension,
                    'content': content,
                    'metadata': metadata
                }
                logger.debug(f"为维度 {result.dimension} 准备了JavaScript数据: 内容长度={len(content)}, 元数据键={list(metadata.keys()) if metadata else '无'}")
            except Exception as e:
                logger.error(f"为维度 {result.dimension} 准备JavaScript数据时出错: {str(e)}")
                results_for_js[result.dimension] = {
                    'dimension': result.dimension,
                    'content': '加载内容时出错',
                    'metadata': {}
                }

        # 检查是否有正在进行的分析
        in_progress = analysis_progress.get(novel_id, {})
        if in_progress:
            logger.debug(f"小说 ID {novel_id} 有 {len(in_progress)} 个维度正在分析中")

        return render_template(
            'novel.html',
            novel=novel,
            analysis_results=results_by_dimension,
            analysis_results_js=results_for_js,
            analysis_in_progress=in_progress,
            available_dimensions=config.ANALYSIS_DIMENSIONS
        )
    except Exception as e:
        logger.error(f"加载小说详情页时出错: {str(e)}", exc_info=True)
        flash(f"加载小说详情页时出错: {str(e)}")
        return redirect(url_for('index'))
    finally:
        session.close()

@app.route('/novel/<int:novel_id>/analyze', methods=['POST'])
def analyze_novel(novel_id: int):
    """
    Analyze a novel.

    Args:
        novel_id: ID of the novel to analyze.
    """
    dimensions = request.form.getlist('dimensions')
    # 读取前端表单的并行分析、缓存使用、强制刷新开关
    parallel = request.form.get('parallel_analysis') == 'on'
    use_cache = request.form.get('use_cache') == 'on'
    force_refresh = request.form.get('force_refresh') == 'on'
    # 读取选择的模型
    selected_model = request.form.get('model', 'deepseek-r1')
    # 更新全局配置
    config.PARALLEL_ANALYSIS_ENABLED = parallel
    config.CACHE_ENABLED = use_cache
    config.FORCE_REFRESH_CACHE = force_refresh
    config.DEFAULT_MODEL = selected_model

    if not dimensions:
        flash('请选择至少一个分析维度')
        return redirect(url_for('view_novel', novel_id=novel_id))

    session = Session()
    try:
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            abort(404)

        # 初始化进度跟踪
        if novel_id not in analysis_progress:
            analysis_progress[novel_id] = {}

        for dimension in dimensions:
            analysis_progress[novel_id][dimension] = {
                "progress": 0,
                "estimated_time": "计算中...",
                "start_time": datetime.now().timestamp()
            }

        # 在后台线程中启动分析
        import threading
        analysis_thread = threading.Thread(
            target=_analyze_novel_in_background,
            args=(novel_id, dimensions, False, selected_model)
        )
        analysis_thread.daemon = True  # 使线程在主程序退出时自动终止
        analysis_thread.start()

        # 立即返回响应，不等待分析完成
        flash('分析已开始，请稍后刷新页面查看结果')
        return redirect(url_for('view_novel', novel_id=novel_id))
    finally:
        session.close()

def _analyze_novel_in_background(novel_id: int, dimensions: List[str], resume: bool = False, model: str = "deepseek-r1"):
    """
    在后台执行小说分析。

    Args:
        novel_id: 要分析的小说ID。
        dimensions: 要分析的维度列表。
        resume: 是否从断点恢复分析。
    """
    global analysis_running

    # 设置分析状态为运行中
    analysis_running[novel_id] = True

    # 添加分析开始日志
    add_analysis_log(novel_id, f"开始分析小说，使用模型: {model}", "info")

    # 添加更多详细的日志
    add_analysis_log(novel_id, f"分析维度: {dimensions}", "info")
    add_analysis_log(novel_id, f"是否从断点恢复: {resume}", "info")
    add_analysis_log(novel_id, f"API密钥: {config.SUPPORTED_MODELS[model]['api_key'][:6]}...{config.SUPPORTED_MODELS[model]['api_key'][-4:]}", "info")

    # 获取小说信息
    session = Session()
    try:
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if novel:
            add_analysis_log(novel_id, f"小说标题: 《{novel.title}》", "info")
            add_analysis_log(novel_id, f"小说长度: {len(novel.content)} 字符", "info")
    except Exception as e:
        logger.error(f"获取小说信息时出错: {str(e)}")
    finally:
        session.close()

    session = Session()
    try:
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            logger.error(f"无法在后台分析中找到小说ID: {novel_id}")
            return

        logger.info(f"开始对小说 {novel_id} 进行后台分析，分析维度: {dimensions}，是否从断点恢复: {resume}，使用模型: {model}")

        # 选择对应的分析器
        current_analyzer = analyzers.get(model, analyzer)
        logger.info(f"使用模型 {model} 的分析器")

        # 如果是断点恢复模式，从数据库加载断点信息
        if resume:
            for dimension in dimensions:
                try:
                    # 查询断点信息
                    checkpoint = session.query(AnalysisCheckpoint).filter_by(
                        novel_id=novel_id,
                        dimension=dimension
                    ).first()

                    if checkpoint:
                        # 将断点信息加载到进度跟踪中
                        if novel_id not in analysis_progress:
                            analysis_progress[novel_id] = {}

                        analysis_progress[novel_id][dimension] = {
                            "progress": 10,  # 初始进度设为10%
                            "estimated_time": "从断点恢复中...",
                            "start_time": datetime.now().timestamp(),
                            "current_block": checkpoint.current_block,
                            "total_blocks": checkpoint.total_blocks,
                            "checkpoint": True,  # 标记为断点恢复
                            "resumed_at": datetime.now().timestamp()
                        }

                        logger.info(f"从断点恢复维度 {dimension} 的分析，断点位置: {checkpoint.current_block}/{checkpoint.total_blocks}")
                except Exception as e:
                    logger.error(f"加载断点信息时出错: {str(e)}")

        # 执行实际分析
        try:
            # 确保进度初始化
            for dimension in dimensions:
                if novel_id not in analysis_progress:
                    analysis_progress[novel_id] = {}

                if dimension not in analysis_progress[novel_id] or not resume:
                    # 如果是新的分析或非续传模式，初始化进度
                    analysis_progress[novel_id][dimension] = {
                        "progress": 10,
                        "estimated_time": "计算中...",
                        "start_time": datetime.now().timestamp(),
                        "current_block": 0
                    }
                    logger.info(f"初始化维度 {dimension} 的进度为10%")
                else:
                    # 续传模式，恢复之前的进度
                    if "checkpoint" in analysis_progress[novel_id][dimension]:
                        logger.info(f"从断点恢复维度 {dimension} 的分析")

            # 模拟分析过程 - 仅在DEBUG模式且未启用并行分析时执行
            if config.DEBUG and not config.PARALLEL_ANALYSIS_ENABLED:
                logger.info("DEBUG模式：模拟分析过程")
                # 为每个维度模拟分析过程
                for dimension in dimensions:
                    # 模拟5个步骤的分析过程
                    for step in range(1, 6):
                        # 更新进度
                        progress = 10 + (step * 18)  # 从10%到100%
                        analysis_progress[novel_id][dimension]["progress"] = progress

                        # 计算预估剩余时间
                        elapsed_time = datetime.now().timestamp() - analysis_progress[novel_id][dimension]["start_time"]
                        if step > 0:
                            estimated_total_time = (elapsed_time / step) * 5
                            remaining_time = estimated_total_time - elapsed_time

                            if remaining_time > 60:
                                time_str = f"{int(remaining_time / 60)}分{int(remaining_time % 60)}秒"
                            else:
                                time_str = f"{int(remaining_time)}秒"

                            analysis_progress[novel_id][dimension]["estimated_time"] = time_str

                        logger.info(f"维度 {dimension} 的进度更新为 {progress}%，预估时间: {analysis_progress[novel_id][dimension]['estimated_time']}")

                        # 模拟分析耗时
                        time.sleep(3)

                # 创建模拟结果
                results = {}
                for dimension in dimensions:
                    # 创建详细的分析内容
                    content = f"""# {dimension} 分析报告

## 概述

这是对小说《{novel.title}》的{dimension}分析。本分析基于DeepSeek R1模型，通过对文本的深入分析，提取出小说在{dimension}方面的特点和规律。

## 详细分析

### 主要特点

1. 小说在{dimension}方面展现出独特的风格和特点
2. 作者在{dimension}的处理上有明显的个人风格
3. 文本结构和{dimension}的关系紧密且有规律

### 数据分析

基于对全文的分析，我们提取了以下关键指标：

- 风格一致性：85%
- 节奏控制：72%
- 结构完整性：90%
- 人物塑造：65%
- 情节发展：78%
- 主题深度：82%

### 建议与改进

1. 可以在{dimension}方面进一步加强某些元素
2. 考虑调整部分段落的{dimension}处理方式
3. 整体而言，作品在{dimension}方面表现出色

## 总结

《{novel.title}》在{dimension}方面展现出作者独特的创作风格和技巧，是一部值得推荐的作品。

"""

                    # 创建分析结果对象
                    result = AnalysisResult(
                        novel_id=novel_id,
                        dimension=dimension,
                        content=content,
                        metadata={
                            "processing_time": 15.0,
                            "chunk_count": 5,
                            "analysis_date": datetime.now(timezone.utc).isoformat(),
                            "visualization_data": {
                                "radar": {
                                    "labels": ["风格一致性", "节奏控制", "结构完整性", "人物塑造", "情节发展", "主题深度"],
                                    "data": [85, 72, 90, 65, 78, 82]
                                },
                                "bar": {
                                    "labels": ["积极情感", "消极情感", "复杂度", "创新性", "连贯性", "吸引力"],
                                    "data": [75, 25, 82, 68, 90, 78]
                                }
                            }
                        }
                    )

                    # 将结果添加到字典中
                    results[dimension] = result

                    # 更新进度为100%
                    analysis_progress[novel_id][dimension]["progress"] = 100
                    analysis_progress[novel_id][dimension]["estimated_time"] = "已完成"
                    logger.info(f"维度 {dimension} 的分析已完成")
            else:
                # 执行实际分析 - 使用优化的并行处理方式
                max_workers = config.MAX_PARALLEL_ANALYSES
                logger.info(f"开始优化并行分析所有维度，最大并行数: {max_workers}")

                # 使用优化的并行分析方法
                if config.PARALLEL_ANALYSIS_ENABLED:
                    logger.info(f"使用优化的并行分析方法，最大并行维度数: 5 ，每个维度最大并行块数: {config.MAX_CHUNK_WORKERS}，使用模型: {model}")
                    results = current_analyzer.analyze_novel_parallel(novel, dimensions, max_workers=5)
                else:
                    logger.info(f"使用串行分析方法，使用模型: {model}")
                    results = current_analyzer.analyze_novel(novel, dimensions)

            logger.info(f"成功完成小说 {novel_id} 的分析，获得 {len(results)} 个维度的结果")
        except Exception as e:
            logger.error(f"分析过程中出错: {str(e)}", exc_info=True)
            # 更新进度状态为错误
            for dimension in dimensions:
                if novel_id in analysis_progress and dimension in analysis_progress[novel_id]:
                    analysis_progress[novel_id][dimension]["progress"] = -1
                    analysis_progress[novel_id][dimension]["estimated_time"] = f"错误: {str(e)}"
            raise

        # 更新数据库中的分析结果
        try:
            for dimension, result in results.items():
                try:
                    # 检查是否已存在该维度的分析
                    existing_result = session.query(AnalysisResult).filter_by(
                        novel_id=novel_id, dimension=dimension
                    ).first()

                    # 输出分析结果类型的详细信息以便调试
                    logger.info(f"处理维度 {dimension} 的分析结果，类型: {type(result)}")
                    if hasattr(result, 'analysis_metadata'):
                        logger.info(f"分析结果元数据类型: {type(result.analysis_metadata)}")

                    # 确保元数据是可序列化的字典
                    metadata_dict = {}
                    if hasattr(result, 'analysis_metadata') and result.analysis_metadata is not None:
                        try:
                            # 尝试将metadata转换为字典
                            if hasattr(result.analysis_metadata, 'items') and callable(result.analysis_metadata.items):
                                # 如果有items方法，创建一个新的字典副本
                                metadata_dict = {k: v for k, v in result.analysis_metadata.items()}
                            elif isinstance(result.analysis_metadata, dict):
                                metadata_dict = dict(result.analysis_metadata)
                            else:
                                # 如果不是字典且没有items方法，使用空字典并记录错误
                                logger.warning(f"元数据不是字典类型: {type(result.analysis_metadata)}")
                                metadata_dict = {"error": f"元数据无法转换为字典: {type(result.analysis_metadata)}"}
                        except Exception as e:
                            logger.error(f"转换元数据为字典时出错: {str(e)}")
                            metadata_dict = {"error": f"元数据转换失败: {str(e)}"}

                    # 如果result本身是dict类型，则直接获取content
                    content = ""
                    if isinstance(result, dict):
                        content = result.get("content", "")
                    else:
                        content = getattr(result, "content", "")

                    # 检查API返回的内容是否包含在output.choices[0].message.content中
                    if isinstance(content, str) and (content.startswith('{"output":') or content.startswith('{"choices":')):
                        try:
                            # 尝试解析JSON
                            content_json = json.loads(content)
                            logger.info(f"检测到JSON格式的内容，尝试提取实际内容")

                            # 尝试从不同路径提取内容
                            extracted_content = None

                            # 路径1: output.choices[0].message.content
                            if 'output' in content_json and 'choices' in content_json['output'] and len(content_json['output']['choices']) > 0:
                                choice = content_json['output']['choices'][0]
                                if 'message' in choice and 'content' in choice['message']:
                                    extracted_content = choice['message']['content']
                                    logger.info(f"从output.choices[0].message.content提取到内容")

                            # 路径2: output.text
                            if not extracted_content and 'output' in content_json and 'text' in content_json['output']:
                                extracted_content = content_json['output']['text']
                                logger.info(f"从output.text提取到内容")

                            # 如果成功提取到内容，更新content
                            if extracted_content:
                                logger.info(f"成功从JSON中提取内容，长度: {len(extracted_content)} 字符")
                                content = extracted_content
                        except json.JSONDecodeError as e:
                            logger.error(f"解析JSON内容时出错: {str(e)}")
                            # 保持原始内容不变

                    # 获取该维度的分析日志
                    dimension_logs = []
                    if novel_id in analysis_logs:
                        dimension_logs = [log for log in analysis_logs[novel_id] if log.get('dimension') == dimension]
                        logger.info(f"为维度 {dimension} 收集了 {len(dimension_logs)} 条分析日志")

                    if existing_result:
                        existing_result.content = content
                        existing_result.analysis_metadata = metadata_dict
                        existing_result.analysis_logs = dimension_logs  # 保存分析日志
                        existing_result.updated_at = datetime.now(timezone.utc)
                        logger.info(f"更新了小说 {novel_id} 的 {dimension} 分析结果，包含 {len(dimension_logs)} 条日志")
                    else:
                        new_result = AnalysisResult(
                            novel_id=novel_id,
                            dimension=dimension,
                            content=content,
                            metadata=metadata_dict,
                            logs=dimension_logs  # 保存分析日志
                        )
                        session.add(new_result)
                        logger.info(f"添加了小说 {novel_id} 的新 {dimension} 分析结果，包含 {len(dimension_logs)} 条日志")

                    # 更新进度为100%
                    if novel_id in analysis_progress and dimension in analysis_progress[novel_id]:
                        analysis_progress[novel_id][dimension]["progress"] = 100
                        analysis_progress[novel_id][dimension]["estimated_time"] = "已完成"

                    # 立即提交每个维度的结果，避免一个错误影响所有结果
                    try:
                        session.commit()
                        logger.info(f"成功将维度 {dimension} 的分析结果保存到数据库")
                    except Exception as commit_error:
                        logger.error(f"提交维度 {dimension} 的分析结果时出错: {str(commit_error)}", exc_info=True)
                        session.rollback()

                        # 尝试重新创建会话并再次保存
                        try:
                            session.close()
                            session = Session()

                            # 重新检查是否存在
                            existing_result = session.query(AnalysisResult).filter_by(
                                novel_id=novel_id, dimension=dimension
                            ).first()

                            # 输出分析结果类型的详细信息以便调试（第二次尝试）
                            logger.info(f"第二次尝试 - 处理维度 {dimension} 的分析结果，类型: {type(result)}")
                            if hasattr(result, 'analysis_metadata'):
                                logger.info(f"第二次尝试 - 分析结果元数据类型: {type(result.analysis_metadata)}")

                            # 确保元数据是可序列化的字典
                            metadata_dict = {}
                            if hasattr(result, 'analysis_metadata') and result.analysis_metadata is not None:
                                try:
                                    # 尝试将metadata转换为字典
                                    if hasattr(result.analysis_metadata, 'items') and callable(result.analysis_metadata.items):
                                        # 如果有items方法，创建一个新的字典副本
                                        metadata_dict = {k: v for k, v in result.analysis_metadata.items()}
                                    elif isinstance(result.analysis_metadata, dict):
                                        metadata_dict = dict(result.analysis_metadata)
                                    else:
                                        # 如果不是字典且没有items方法，使用空字典并记录错误
                                        logger.warning(f"第二次尝试 - 元数据不是字典类型: {type(result.analysis_metadata)}")
                                        metadata_dict = {"error": f"元数据无法转换为字典: {type(result.analysis_metadata)}"}
                                except Exception as e:
                                    logger.error(f"第二次尝试 - 转换元数据为字典时出错: {str(e)}")
                                    metadata_dict = {"error": f"元数据转换失败: {str(e)}"}

                            # 如果result本身是dict类型，则直接获取content
                            content = ""
                            if isinstance(result, dict):
                                content = result.get("content", "")
                            else:
                                content = getattr(result, "content", "")

                            # 检查API返回的内容是否包含在output.choices[0].message.content中
                            if isinstance(content, str) and (content.startswith('{"output":') or content.startswith('{"choices":')):
                                try:
                                    # 尝试解析JSON
                                    content_json = json.loads(content)
                                    logger.info(f"第二次尝试 - 检测到JSON格式的内容，尝试提取实际内容")

                                    # 尝试从不同路径提取内容
                                    extracted_content = None

                                    # 路径1: output.choices[0].message.content
                                    if 'output' in content_json and 'choices' in content_json['output'] and len(content_json['output']['choices']) > 0:
                                        choice = content_json['output']['choices'][0]
                                        if 'message' in choice and 'content' in choice['message']:
                                            extracted_content = choice['message']['content']
                                            logger.info(f"第二次尝试 - 从output.choices[0].message.content提取到内容")

                                    # 路径2: output.text
                                    if not extracted_content and 'output' in content_json and 'text' in content_json['output']:
                                        extracted_content = content_json['output']['text']
                                        logger.info(f"第二次尝试 - 从output.text提取到内容")

                                    # 如果成功提取到内容，更新content
                                    if extracted_content:
                                        logger.info(f"第二次尝试 - 成功从JSON中提取内容，长度: {len(extracted_content)} 字符")
                                        content = extracted_content
                                except json.JSONDecodeError as e:
                                    logger.error(f"第二次尝试 - 解析JSON内容时出错: {str(e)}")
                                    # 保持原始内容不变

                            # 获取该维度的分析日志（第二次尝试）
                            dimension_logs = []
                            if novel_id in analysis_logs:
                                dimension_logs = [log for log in analysis_logs[novel_id] if log.get('dimension') == dimension]
                                logger.info(f"第二次尝试 - 为维度 {dimension} 收集了 {len(dimension_logs)} 条分析日志")

                            if existing_result:
                                existing_result.content = content
                                existing_result.analysis_metadata = metadata_dict
                                existing_result.analysis_logs = dimension_logs  # 保存分析日志
                                existing_result.updated_at = datetime.now(timezone.utc)
                                logger.info(f"第二次尝试 - 更新了小说 {novel_id} 的 {dimension} 分析结果，包含 {len(dimension_logs)} 条日志")
                            else:
                                new_result = AnalysisResult(
                                    novel_id=novel_id,
                                    dimension=dimension,
                                    content=content,
                                    metadata=metadata_dict,
                                    logs=dimension_logs  # 保存分析日志
                                )
                                session.add(new_result)
                                logger.info(f"第二次尝试 - 添加了小说 {novel_id} 的新 {dimension} 分析结果，包含 {len(dimension_logs)} 条日志")

                            session.commit()
                            logger.info(f"第二次尝试：成功将维度 {dimension} 的分析结果保存到数据库")
                        except Exception as retry_error:
                            logger.error(f"第二次尝试保存维度 {dimension} 的分析结果时出错: {str(retry_error)}", exc_info=True)
                            session.rollback()
                except Exception as e:
                    logger.error(f"处理维度 {dimension} 的分析结果时出错: {str(e)}", exc_info=True)
                    session.rollback()

            # 获取API调用统计
            stats = DeepSeekClient.get_api_call_stats()
            add_analysis_log(novel_id, f"[API统计] 总调用次数: {stats['total_calls']}次", "info")
            add_analysis_log(novel_id, f"[API统计] 总令牌使用量: {stats['total_tokens']}个", "info")
            add_analysis_log(novel_id, f"[API统计] 总费用: {stats['total_cost']:.4f}元", "info")
            add_analysis_log(novel_id, f"[API统计] 平均每次调用费用: {stats['average_cost_per_call']:.4f}元", "info")
            add_analysis_log(novel_id, f"[API统计] 分析时长: {stats['analysis_duration_minutes']:.2f}分钟", "info")

            logger.info(f"小说 {novel_id} 的后台分析已完成，分析了 {len(dimensions)} 个维度")
        except Exception as e:
            logger.error(f"保存分析结果时出错: {str(e)}", exc_info=True)
            session.rollback()
    except Exception as e:
        logger.error(f"后台分析出错: {str(e)}", exc_info=True)
        # 更新进度状态为错误
        for dimension in dimensions:
            if novel_id in analysis_progress and dimension in analysis_progress[novel_id]:
                analysis_progress[novel_id][dimension]["progress"] = -1
                analysis_progress[novel_id][dimension]["estimated_time"] = f"错误: {str(e)}"
    finally:
        session.close()

@app.route('/novel/<int:novel_id>/analysis/<dimension>')
def view_analysis(novel_id: int, dimension: str):
    """
    View a specific analysis result.

    Args:
        novel_id: ID of the novel.
        dimension: Analysis dimension.
    """
    session = Session()
    try:
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            abort(404)

        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()
        if not result:
            abort(404)

        # 确保内容是字符串
        if not isinstance(result.content, str):
            try:
                logger.warning(f"分析结果内容不是字符串: {type(result.content)}")
                result.content = str(result.content)
            except Exception as e:
                logger.error(f"转换分析结果内容为字符串时出错: {str(e)}")
                result.content = "无法显示内容"

        # 检查API返回的内容是否包含在output.choices[0].message.content中
        if result.content.startswith('{"output":') or result.content.startswith('{"choices":'):
            try:
                # 尝试解析JSON
                content_json = json.loads(result.content)
                logger.info(f"检测到JSON格式的内容，尝试提取实际内容")

                # 尝试从不同路径提取内容
                extracted_content = None

                # 路径1: output.choices[0].message.content
                if 'output' in content_json and 'choices' in content_json['output'] and len(content_json['output']['choices']) > 0:
                    choice = content_json['output']['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        extracted_content = choice['message']['content']
                        logger.info(f"从output.choices[0].message.content提取到内容")

                # 路径2: output.text
                if not extracted_content and 'output' in content_json and 'text' in content_json['output']:
                    extracted_content = content_json['output']['text']
                    logger.info(f"从output.text提取到内容")

                # 如果成功提取到内容，更新result.content
                if extracted_content:
                    logger.info(f"成功从JSON中提取内容，长度: {len(extracted_content)} 字符")
                    result.content = extracted_content

                    # 更新数据库中的内容
                    try:
                        result_db = session.query(AnalysisResult).filter_by(id=result.id).first()
                        if result_db:
                            result_db.content = extracted_content
                            session.commit()
                            logger.info(f"已更新数据库中的分析结果内容")
                    except Exception as db_error:
                        logger.error(f"更新数据库中的分析结果内容时出错: {str(db_error)}")
                        session.rollback()
            except json.JSONDecodeError as e:
                logger.error(f"解析JSON内容时出错: {str(e)}")
                # 保持原始内容不变

        # 提取推理过程
        try:
            # 尝试从content字段中提取推理过程
            if hasattr(result, 'content') and result.content:
                content = result.content
                if isinstance(content, str):
                    # 尝试提取推理过程
                    if '分析过程' in content:
                        process_start = content.find('分析过程')
                        if process_start != -1:
                            # 找到分析过程后的内容
                            process_content = content[process_start:]
                            result.reasoning_content = process_content
                            logger.info("从content字段中提取到分析过程内容")
                    elif '推理过程' in content:
                        process_start = content.find('推理过程')
                        if process_start != -1:
                            # 找到推理过程后的内容
                            process_content = content[process_start:]
                            result.reasoning_content = process_content
                            logger.info("从content字段中提取到推理过程内容")
                    elif '思考过程' in content:
                        process_start = content.find('思考过程')
                        if process_start != -1:
                            # 找到思考过程后的内容
                            process_content = content[process_start:]
                            result.reasoning_content = process_content
                            logger.info("从content字段中提取到思考过程内容")
                    else:
                        # 如果没有找到特定标记，使用整个内容
                        result.reasoning_content = content
                        logger.info("使用整个content字段作为推理过程内容")

            # 禁用元数据，避免序列化错误
            result.metadata = {}
            logger.info("元数据已被禁用以避免序列化错误")
        except Exception as e:
            logger.error(f"提取推理过程时出错: {str(e)}")
            result.reasoning_content = "提取推理过程时出错"
            result.metadata = {}

        # 确保分析日志是列表
        if result.analysis_logs and not isinstance(result.analysis_logs, list):
            try:
                # 尝试将logs转换为列表
                if isinstance(result.analysis_logs, dict):
                    # 如果是字典，转换为列表
                    result.logs = [{"key": k, "value": v} for k, v in result.analysis_logs.items()]
                else:
                    # 如果不是列表也不是字典，使用空列表
                    logger.warning(f"分析结果日志不是列表类型: {type(result.analysis_logs)}")
                    result.logs = []
            except Exception as e:
                logger.error(f"转换分析结果日志为列表时出错: {str(e)}")
                result.logs = []
        else:
            # 如果已经是列表或为None，直接使用
            result.logs = result.analysis_logs or []

        # 如果没有保存的分析日志，尝试从内存中获取
        if not result.logs and novel_id in analysis_logs:
            try:
                # 从内存中获取该维度的日志
                dimension_logs = [log for log in analysis_logs[novel_id]
                                 if log.get('dimension') == dimension or
                                 (log.get('dimension') is None and dimension in log.get('message', ''))]
                if dimension_logs:
                    logger.info(f"从内存中为维度 {dimension} 找到了 {len(dimension_logs)} 条分析日志")
                    result.logs = dimension_logs

                    # 尝试更新数据库中的日志
                    try:
                        result_db = session.query(AnalysisResult).filter_by(id=result.id).first()
                        if result_db:
                            result_db.analysis_logs = dimension_logs
                            session.commit()
                            logger.info(f"已更新数据库中的分析日志")
                    except Exception as db_error:
                        logger.error(f"更新数据库中的分析日志时出错: {str(db_error)}")
                        session.rollback()
            except Exception as e:
                logger.error(f"从内存中获取分析日志时出错: {str(e)}")
                # 保持原始日志不变

        # 检查是否有特定维度的专用模板
        template_path = f'analysis/{dimension}.html'
        template_folder = os.path.join(os.path.dirname(__file__), 'templates')
        if os.path.exists(os.path.join(template_folder, template_path)):
            logger.info(f"使用专用模板 {template_path} 渲染维度 {dimension} 的分析结果")
            return render_template(template_path, novel=novel, result=result)
        else:
            logger.info(f"使用通用模板 analysis.html 渲染维度 {dimension} 的分析结果")
            return render_template('analysis.html', novel=novel, result=result)
    finally:
        session.close()

@app.route('/novel/<int:novel_id>/delete', methods=['POST'])
def delete_novel(novel_id: int):
    """
    Delete a novel.

    Args:
        novel_id: ID of the novel to delete.
    """
    session = Session()
    try:
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            abort(404)

        # Delete associated analysis results
        session.query(AnalysisResult).filter_by(novel_id=novel_id).delete()

        # Delete the novel
        session.delete(novel)
        session.commit()

        # Delete the file if it exists
        if novel.file_path and os.path.exists(novel.file_path):
            os.remove(novel.file_path)

        flash(f'Novel "{novel.title}" deleted successfully')
        return redirect(url_for('index'))
    except Exception as e:
        logger.error(f"Error deleting novel: {str(e)}")
        flash(f'Error deleting novel: {str(e)}')
        return redirect(url_for('view_novel', novel_id=novel_id))
    finally:
        session.close()

@app.route('/api/novels', methods=['GET'])
def api_get_novels():
    """API endpoint to get all novels."""
    session = Session()
    try:
        novels = session.query(Novel).all()
        return jsonify([novel.get_summary() for novel in novels])
    finally:
        session.close()

@app.route('/api/novels/<int:novel_id>', methods=['GET'])
def api_get_novel(novel_id: int):
    """
    API endpoint to get a specific novel.

    Args:
        novel_id: ID of the novel.
    """
    session = Session()
    try:
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            abort(404)
        return jsonify(novel.to_dict())
    finally:
        session.close()

@app.route('/api/novels/<int:novel_id>/analysis', methods=['GET'])
def api_get_analysis_results(novel_id: int):
    """
    API endpoint to get analysis results for a novel.

    Args:
        novel_id: ID of the novel.
    """
    session = Session()
    try:
        results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
        return jsonify([result.to_dict() for result in results])
    finally:
        session.close()

@app.route('/api/novels/<int:novel_id>/analysis/<dimension>', methods=['GET', 'DELETE'])
def api_get_analysis_result(novel_id: int, dimension: str):
    """
    API endpoint to get or delete a specific analysis result.

    Args:
        novel_id: ID of the novel.
        dimension: Analysis dimension.
    """
    if request.method == 'DELETE':
        logger.info(f"删除小说 {novel_id} 的 {dimension} 分析结果")
        session = Session()
        try:
            # 检查小说是否存在
            novel = session.query(Novel).filter_by(id=novel_id).first()
            if not novel:
                logger.warning(f"尝试删除不存在的小说的分析结果: ID {novel_id}")
                return jsonify({
                    "success": False,
                    "error": f"找不到ID为 {novel_id} 的小说"
                }), 404

            # 删除分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id, dimension=dimension
            ).first()

            if not result:
                logger.warning(f"尝试删除不存在的分析结果: 小说ID {novel_id}, 维度 {dimension}")
                return jsonify({
                    "success": False,
                    "error": f"找不到小说ID {novel_id} 的 {dimension} 分析结果"
                }), 404

            # 删除分析结果
            session.delete(result)
            session.commit()

            # 清除进度信息
            if novel_id in analysis_progress and dimension in analysis_progress[novel_id]:
                del analysis_progress[novel_id][dimension]

            logger.info(f"成功删除小说 {novel_id} 的 {dimension} 分析结果")
            return jsonify({
                "success": True,
                "message": f"成功删除小说 {novel_id} 的 {dimension} 分析结果"
            })
        except Exception as e:
            logger.error(f"删除分析结果时出错: {str(e)}")
            session.rollback()
            return jsonify({
                "success": False,
                "error": str(e)
            }), 500
        finally:
            session.close()
    else:
        logger.info(f"获取小说 {novel_id} 的 {dimension} 分析结果")

    # 确保维度名称是有效的
    if dimension not in config.ANALYSIS_DIMENSIONS:
        logger.warning(f"请求了无效的分析维度: {dimension}")
        # 尝试查找最接近的维度名称（可能是拼写错误）
        valid_dimensions = []
        for valid_dim in config.ANALYSIS_DIMENSIONS:
            if valid_dim.startswith(dimension[:3]) or dimension.startswith(valid_dim[:3]):
                valid_dimensions.append(valid_dim)

        suggestion_msg = f"有效的维度包括: {', '.join(config.ANALYSIS_DIMENSIONS)}"
        if valid_dimensions:
            suggestion_msg = f"您是否想要查询这些维度之一: {', '.join(valid_dimensions)}?"

        return jsonify({
            "success": False,
            "error": f"无效的分析维度: {dimension}",
            "suggestion": suggestion_msg
        }), 400

    session = Session()
    try:
        # 首先检查小说是否存在
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            logger.warning(f"尝试获取不存在的小说的分析结果: ID {novel_id}")
            return jsonify({
                "success": False,
                "error": f"找不到ID为 {novel_id} 的小说"
            }), 404

        # 检查分析结果是否存在
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()

        if not result:
            # 检查是否有正在进行的分析
            in_progress = False
            progress_info = None

            if novel_id in analysis_progress and dimension in analysis_progress[novel_id]:
                in_progress = True
                progress_info = analysis_progress[novel_id][dimension]
                progress = progress_info.get("progress", 0)

                # 如果进度是100%但结果不存在，可能是保存结果时出错
                if progress >= 100:
                    logger.warning(f"小说 {novel_id} 的维度 {dimension} 进度为100%但结果不存在，可能保存失败")
                    return jsonify({
                        "success": False,
                        "error": "分析已完成但结果保存失败，请重新分析",
                        "progress": progress,
                        "in_progress": in_progress,
                        "dimension": dimension,
                        "novel_id": novel_id
                    }), 202  # 使用202而不是404，以便前端能够正确处理
                else:
                    logger.info(f"小说 {novel_id} 的维度 {dimension} 分析正在进行中，进度: {progress}%")
                    return jsonify({
                        "success": False,
                        "error": "分析结果尚未生成，分析正在进行中",
                        "progress": progress,
                        "in_progress": in_progress,
                        "dimension": dimension,
                        "novel_id": novel_id
                    }), 202  # 使用202 Accepted状态码表示请求已接受但处理尚未完成
            else:
                logger.warning(f"小说 {novel_id} 的维度 {dimension} 分析结果不存在，且没有正在进行的分析")
                return jsonify({
                    "success": False,
                    "error": "分析结果不存在，请开始分析",
                    "in_progress": in_progress,
                    "dimension": dimension,
                    "novel_id": novel_id
                }), 202  # 使用202而不是404，以便前端能够正确处理

        # 确保内容是字符串
        content = result.content
        if not isinstance(content, str):
            logger.warning(f"分析结果内容不是字符串: {type(content)}")
            try:
                content = str(content)
            except Exception as e:
                logger.error(f"转换分析结果内容为字符串时出错: {str(e)}")
                content = "无法显示内容"

        # 提取推理过程
        reasoning_content = None
        try:
            # 尝试从content字段中提取推理过程
            if isinstance(content, str):
                # 尝试提取推理过程
                if '分析过程' in content:
                    process_start = content.find('分析过程')
                    if process_start != -1:
                        # 找到分析过程后的内容
                        reasoning_content = content[process_start:]
                        logger.info("API响应：从content字段中提取到分析过程内容")
                elif '推理过程' in content:
                    process_start = content.find('推理过程')
                    if process_start != -1:
                        # 找到推理过程后的内容
                        reasoning_content = content[process_start:]
                        logger.info("API响应：从content字段中提取到推理过程内容")
                elif '思考过程' in content:
                    process_start = content.find('思考过程')
                    if process_start != -1:
                        # 找到思考过程后的内容
                        reasoning_content = content[process_start:]
                        logger.info("API响应：从content字段中提取到思考过程内容")
                else:
                    # 如果没有找到特定标记，使用整个内容
                    reasoning_content = content
                    logger.info("API响应：使用整个content字段作为推理过程内容")
        except Exception as e:
            logger.error(f"API响应：提取推理过程时出错: {str(e)}")
            reasoning_content = "提取推理过程时出错"

        # 禁用元数据，避免序列化错误
        metadata = {}
        logger.info("API响应：元数据已被禁用以避免序列化错误")

        # 确保分析日志是列表
        logs = []
        if result.analysis_logs:
            try:
                if isinstance(result.analysis_logs, list):
                    logs = list(result.analysis_logs)
                elif hasattr(result.analysis_logs, 'items') and callable(result.analysis_logs.items):
                    # 如果是字典，转换为列表
                    logs = [{"key": k, "value": v} for k, v in result.analysis_logs.items()]
                else:
                    # 如果不是列表也不是字典，使用空列表
                    logger.warning(f"API响应：日志不是列表类型: {type(result.analysis_logs)}")
                    logs = []
            except Exception as e:
                logger.error(f"API响应：转换分析结果日志为列表时出错: {str(e)}")
                logs = []

        # 返回处理后的结果
        result_dict = result.to_dict()
        result_dict["content"] = content
        result_dict["metadata"] = metadata
        result_dict["logs"] = logs
        result_dict["success"] = True

        # 添加推理过程
        if reasoning_content:
            result_dict["reasoning_content"] = reasoning_content
            logger.info("API响应：已添加推理过程到返回结果中")

        return jsonify(result_dict)
    except Exception as e:
        logger.error(f"获取分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@app.route('/api/novels/<int:novel_id>/analyze', methods=['POST'])
def api_analyze_novel(novel_id: int):
    """
    API endpoint to analyze a novel.

    Args:
        novel_id: ID of the novel to analyze.
    """
    data = request.json or {}
    dimensions = data.get('dimensions', config.ANALYSIS_DIMENSIONS)
    resume = data.get('resume', False)  # 添加resume参数，默认为False
    parallel_analysis = data.get('parallel_analysis', config.PARALLEL_ANALYSIS_ENABLED)  # 添加并行分析参数
    use_cache = data.get('use_cache', config.CACHE_ENABLED)  # 添加缓存使用参数
    force_refresh = data.get('force_refresh', config.FORCE_REFRESH_CACHE)  # 添加强制刷新缓存参数
    selected_model = data.get('model', 'deepseek-r1')  # 添加模型选择参数，默认为DeepSeek R1

    # 更新全局配置
    config.PARALLEL_ANALYSIS_ENABLED = parallel_analysis
    config.CACHE_ENABLED = use_cache
    config.FORCE_REFRESH_CACHE = force_refresh
    config.DEFAULT_MODEL = selected_model

    # 记录请求信息，帮助调试
    logger.info(f"收到分析请求: novel_id={novel_id}, dimensions={dimensions}, resume={resume}, parallel_analysis={parallel_analysis}, use_cache={use_cache}, force_refresh={force_refresh}, model={selected_model}, 请求数据: {data}")

    session = Session()
    try:
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            logger.error(f"尝试分析不存在的小说: ID {novel_id}")
            return jsonify({
                "success": False,
                "error": f"找不到ID为 {novel_id} 的小说"
            }), 404

        # 初始化进度跟踪
        if novel_id not in analysis_progress:
            analysis_progress[novel_id] = {}

        # 设置分析状态为运行中
        analysis_running[novel_id] = True

        for dimension in dimensions:
            # 如果是续传且有断点，不重置进度
            if resume and dimension in analysis_progress[novel_id] and analysis_progress[novel_id][dimension].get("checkpoint", False):
                logger.info(f"将从断点继续分析维度 {dimension}")
            else:
                analysis_progress[novel_id][dimension] = {
                    "progress": 0,
                    "estimated_time": "计算中...",
                    "start_time": datetime.now().timestamp()
                }

        # 在后台线程中启动分析
        import threading
        analysis_thread = threading.Thread(
            target=_analyze_novel_in_background,
            args=(novel_id, dimensions, resume, selected_model)
        )
        analysis_thread.daemon = True  # 使线程在主程序退出时自动终止
        analysis_thread.start()

        logger.info(f"成功启动小说 {novel_id} 的分析，维度: {dimensions}，是否从断点恢复: {resume}，使用模型: {selected_model}")
        return jsonify({
            "success": True,
            "message": "分析已开始，请通过进度API监控进度",
            "dimensions": dimensions
        })
    except Exception as e:
        logger.error(f"启动分析时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@app.route('/api/analysis/progress', methods=['GET'])
def api_get_analysis_progress():
    """获取所有分析的进度信息，包含更详细的进度报告"""
    novel_id = request.args.get('novel_id')
    logger.debug(f"正在获取分析进度数据, novel_id={novel_id}")

    if novel_id:
        try:
            novel_id = int(novel_id)
            # 如果指定了小说ID，只返回该小说的分析进度
            if novel_id in analysis_progress:
                logger.debug(f"找到小说ID {novel_id} 的进度数据: {len(analysis_progress[novel_id])} 个维度")

                # 检查是否有正在运行的分析
                is_running = analysis_running.get(novel_id, False)

                # 复制进度数据，避免直接修改全局变量
                progress_data = {}

                # 计算总体进度
                total_progress = 0
                completed_dimensions = 0
                total_dimensions = len(analysis_progress[novel_id])

                # 计算预计剩余时间
                current_time = time.time()
                remaining_times = []

                # 获取小说标题
                novel_title = "未知小说"
                try:
                    session = Session()
                    novel = session.query(Novel).filter_by(id=novel_id).first()
                    if novel:
                        novel_title = novel.title
                finally:
                    if session:
                        session.close()

                for dimension, data in analysis_progress[novel_id].items():
                    progress_data[dimension] = data.copy()
                    # 添加运行状态
                    progress_data[dimension]['is_running'] = is_running

                    # 确保进度值是整数
                    if 'progress' in progress_data[dimension]:
                        try:
                            progress = int(progress_data[dimension]['progress'])
                            progress_data[dimension]['progress'] = progress
                            total_progress += progress

                            # 计算已完成的维度
                            if progress >= 100:
                                completed_dimensions += 1

                            # 计算预计剩余时间
                            if 0 < progress < 100 and 'start_time' in data:
                                start_time = data.get('start_time', current_time - 60)
                                elapsed_time = current_time - start_time
                                if progress > 0 and elapsed_time > 0:
                                    remaining_time = (elapsed_time / progress) * (100 - progress)
                                    remaining_times.append(remaining_time)

                                    # 更新预计完成时间
                                    eta = datetime.fromtimestamp(current_time + remaining_time).strftime('%H:%M:%S')
                                    progress_data[dimension]['eta'] = eta

                                    # 更新预计剩余时间（格式化为分钟和秒）
                                    minutes = int(remaining_time // 60)
                                    seconds = int(remaining_time % 60)
                                    progress_data[dimension]['remaining_time'] = f"{minutes}分{seconds}秒"

                        except (ValueError, TypeError):
                            progress_data[dimension]['progress'] = 0

                    # 添加更详细的块级进度信息
                    if 'current_block' in data and 'total_blocks' in data:
                        current_block = data.get('current_block', 0)
                        total_blocks = data.get('total_blocks', 1)
                        progress_data[dimension]['blocks_progress'] = f"{current_block}/{total_blocks}"
                        progress_data[dimension]['blocks_percentage'] = int(100 * current_block / total_blocks) if total_blocks > 0 else 0

                    # 获取该维度的最新日志
                    if novel_id in analysis_logs:
                        dimension_logs = [log for log in analysis_logs[novel_id] if log.get('dimension') == dimension]
                        if dimension_logs:
                            latest_log = dimension_logs[-1]
                            progress_data[dimension]['latest_log'] = latest_log.get('message', '')
                            progress_data[dimension]['latest_log_timestamp'] = latest_log.get('timestamp', '')
                            progress_data[dimension]['latest_log_level'] = latest_log.get('level', 'info')
                        else:
                            # 如果没有该维度的日志，但有进度信息，创建一个基于进度的日志
                            progress = progress_data[dimension].get('progress', 0)
                            if progress > 0:
                                progress_data[dimension]['latest_log'] = f"分析进度: {progress}%"
                                progress_data[dimension]['latest_log_timestamp'] = datetime.now().isoformat()
                                progress_data[dimension]['latest_log_level'] = 'info'

                # 计算平均进度
                avg_progress = total_progress / total_dimensions if total_dimensions > 0 else 0

                # 计算平均预计剩余时间
                avg_remaining_time = sum(remaining_times) / len(remaining_times) if remaining_times else 0

                # 格式化平均预计剩余时间
                if avg_remaining_time > 0:
                    minutes = int(avg_remaining_time // 60)
                    seconds = int(avg_remaining_time % 60)
                    avg_remaining_time_str = f"{minutes}分{seconds}秒"
                else:
                    avg_remaining_time_str = "计算中..."

                # 添加总体进度信息
                overall_progress = {
                    'progress': int(avg_progress),
                    'completed_dimensions': completed_dimensions,
                    'total_dimensions': total_dimensions,
                    'remaining_time': avg_remaining_time_str,
                    'is_running': is_running,
                    'eta': datetime.fromtimestamp(current_time + avg_remaining_time).strftime('%H:%M:%S') if avg_remaining_time > 0 else "计算中...",
                    'novel_title': novel_title
                }

                # 获取最近的分析日志
                recent_logs = []
                if novel_id in analysis_logs:
                    # 获取最近的10条日志
                    recent_logs = analysis_logs[novel_id][-10:]

                logger.info(f"返回小说 {novel_id} 的进度数据: 总进度={overall_progress['progress']}%, 已完成维度={completed_dimensions}/{total_dimensions}")

                return jsonify({
                    'success': True,
                    'progress': progress_data,
                    'overall_progress': overall_progress,
                    'is_running': is_running,
                    'recent_logs': recent_logs,
                    'novel_title': novel_title
                })
            else:
                logger.debug(f"没有找到小说ID {novel_id} 的进度数据")
                return jsonify({
                    'success': False,
                    'error': '没有该小说的分析进度信息',
                    'novel_id': novel_id
                })
        except ValueError:
            logger.error(f"无效的小说ID: {novel_id}")
            return jsonify({
                'success': False,
                'error': f'无效的小说ID: {novel_id}'
            })
    else:
        # 返回所有分析进度
        logger.debug(f"返回所有分析进度数据: {len(analysis_progress)} 个小说")

        # 复制进度数据，添加运行状态
        all_progress = {}
        for novel_id, dimensions in analysis_progress.items():
            all_progress[novel_id] = {}
            is_running = analysis_running.get(novel_id, False)

            for dimension, data in dimensions.items():
                all_progress[novel_id][dimension] = data.copy()
                all_progress[novel_id][dimension]['is_running'] = is_running

                # 确保进度值是整数
                if 'progress' in all_progress[novel_id][dimension]:
                    try:
                        all_progress[novel_id][dimension]['progress'] = int(all_progress[novel_id][dimension]['progress'])
                    except (ValueError, TypeError):
                        all_progress[novel_id][dimension]['progress'] = 0

        return jsonify({
            'success': True,
            'progress': all_progress
        })

@app.route('/api/custom-analysis', methods=['POST'])
def api_custom_analysis():
    """API endpoint for custom analysis."""
    data = request.json or {}
    novel_id = data.get('novel_id')
    custom_prompt = data.get('prompt')

    if not novel_id or not custom_prompt:
        return jsonify({
            "success": False,
            "error": "Missing required parameters: novel_id and prompt"
        }), 400

    session = Session()
    try:
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            abort(404)

        # Perform custom analysis
        result = analyzer.analyze_specific_aspect(novel, custom_prompt)

        return jsonify({
            "success": True,
            "result": result
        })
    except Exception as e:
        logger.error(f"Error performing custom analysis: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@app.route('/api/novels/<int:novel_id>/stop_analysis', methods=['POST'])
def api_stop_analysis(novel_id: int):
    """
    终止特定小说的分析进程

    Args:
        novel_id: 小说ID
    """
    global analysis_progress, analysis_running

    data = request.json or {}
    dimension = data.get('dimension')

    if novel_id in analysis_progress:
        # 设置分析状态为终止
        analysis_running[novel_id] = False
        logger.info(f"设置小说 {novel_id} 的分析状态为终止")

        # 如果指定了维度，只终止该维度的分析
        if dimension and dimension in analysis_progress[novel_id]:
            analysis_progress[novel_id][dimension]["progress"] = -1
            analysis_progress[novel_id][dimension]["estimated_time"] = "已终止"
            logger.info(f"终止了小说 {novel_id} 的 {dimension} 分析")

            return jsonify({
                "success": True,
                "message": f"已终止 {dimension} 分析"
            })

        # 标记所有正在进行的分析为已停止
        for dimension in analysis_progress[novel_id]:
            if analysis_progress[novel_id][dimension]["progress"] < 100:
                analysis_progress[novel_id][dimension]["progress"] = -2  # -2表示用户手动停止
                analysis_progress[novel_id][dimension]["estimated_time"] = "已终止"
                analysis_progress[novel_id][dimension]["checkpoint"] = True  # 标记为有断点

                # 记录当前处理的块位置
                if "current_block" in analysis_progress[novel_id][dimension]:
                    # 保存当前块信息到数据库以便后续续传
                    session = Session()
                    try:
                        # 检查是否已存在断点记录
                        checkpoint = session.query(AnalysisCheckpoint).filter_by(
                            novel_id=novel_id,
                            dimension=dimension
                        ).first()

                        current_block = analysis_progress[novel_id][dimension].get("current_block", 0)
                        total_blocks = analysis_progress[novel_id][dimension].get("total_blocks", 0)

                        if checkpoint:
                            # 更新现有断点
                            checkpoint.current_block = current_block
                            checkpoint.total_blocks = total_blocks
                            checkpoint.updated_at = datetime.now(timezone.utc)
                        else:
                            # 创建新断点
                            checkpoint = AnalysisCheckpoint(
                                novel_id=novel_id,
                                dimension=dimension,
                                current_block=current_block,
                                total_blocks=total_blocks
                            )
                            session.add(checkpoint)

                        session.commit()
                        logger.info(f"为小说 {novel_id} 的 {dimension} 分析保存断点：块 {current_block}/{total_blocks}")
                    except Exception as e:
                        logger.error(f"保存分析断点时出错: {str(e)}")
                        session.rollback()
                    finally:
                        session.close()

        logger.info(f"用户终止了小说 {novel_id} 的分析")
        return jsonify({
            "success": True,
            "message": "分析已终止，下次可从断点继续"
        })
    else:
        return jsonify({
            "success": False,
            "message": "未找到该小说的分析进程"
        }), 404

@app.teardown_appcontext
def shutdown_session(exception=None):
    """Remove the session on app context teardown."""
    try:
        Session.remove()
        logger.debug("会话已在应用上下文结束时清理")
    except Exception as e:
        logger.error(f"清理会话时出错: {str(e)}")

# 添加API端点用于重置数据库连接池
@app.route('/api/reset_connections', methods=['POST'])
def api_reset_connections():
    """重置数据库连接池"""
    try:
        logger.info("手动重置数据库连接池")
        recreate_engine()
        return jsonify({
            "success": True,
            "message": "数据库连接池已重置"
        })
    except Exception as e:
        logger.error(f"重置数据库连接池时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# 添加请求结束后的会话清理
@app.teardown_request
def cleanup_request(exception=None):
    """确保请求结束后会话被清理"""
    try:
        Session.remove()
        logger.debug("会话已在请求结束时清理")
    except Exception as e:
        logger.error(f"请求结束时清理会话出错: {str(e)}")

# 添加请求前的会话准备
@app.before_request
def setup_request():
    """确保每个请求开始时会话是新的"""
    try:
        # 确保之前的会话已清理
        Session.remove()
        # 获取新会话
        session = Session()
        # 测试会话连接 - 使用text()函数包装SQL语句
        from sqlalchemy import text
        session.execute(text("SELECT 1"))
        logger.debug("请求开始时会话已准备")

        # 检查连接池状态
        from src.db.connection import engine, recreate_engine

        # 获取连接池状态
        try:
            checked_out = engine.pool.checkedout()
            pool_size = engine.pool.size()

            # 计算使用率
            if pool_size > 0:
                usage_ratio = checked_out / pool_size

                # 如果使用率超过70%，记录警告
                if usage_ratio > 0.7:
                    logger.warning(f"连接池使用率较高: {checked_out}/{pool_size} (已使用/总大小)")

                    # 如果使用率超过80%，主动重新创建连接池
                    if usage_ratio > 0.8:
                        logger.warning("连接池使用率超过80%，主动重新创建连接池")
                        # 先关闭当前会话
                        session.close()
                        # 清理所有会话
                        Session.remove()
                        # 重新创建连接池
                        recreate_engine()
                        logger.info("已重新创建连接池")
        except Exception as pool_error:
            logger.error(f"检查连接池状态时出错: {str(pool_error)}")

    except Exception as e:
        logger.error(f"请求开始时准备会话出错: {str(e)}")
        # 如果出现连接问题，尝试重新创建连接池
        try:
            from src.db.connection import recreate_engine
            recreate_engine()
            logger.info("由于连接错误，已重新创建连接池")
        except Exception as recreate_error:
            logger.error(f"重新创建连接池时出错: {str(recreate_error)}")

# 添加404错误处理
@app.errorhandler(404)
def handle_404_error(error):
    """处理404错误，返回自定义的404页面"""
    app.logger.warning(f"404错误: 找不到页面 {request.path}")

    try:
        # 尝试渲染404模板
        return render_template('404.html'), 404
    except Exception as template_error:
        app.logger.error(f"渲染404模板失败: {str(template_error)}")
        # 如果模板渲染失败，返回简单的HTML错误页面
        error_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>页面未找到</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .error-container { max-width: 800px; margin: 40px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; text-align: center; }
                .error-title { color: #6c757d; }
                .back-link { margin-top: 20px; display: inline-block; }
            </style>
        </head>
        <body>
            <div class="error-container">
                <h1 class="error-title">页面未找到</h1>
                <p>抱歉，您请求的页面不存在或已被移动。</p>
                <a href="/" class="back-link">返回首页</a>
            </div>
        </body>
        </html>
        """
        return error_html, 404

# 添加全局错误处理
@app.errorhandler(500)
def handle_500_error(error):
    import traceback
    app.logger.error(f"500错误: {str(error)}")
    app.logger.error(traceback.format_exc())

    # 尝试重置数据库连接池
    try:
        app.logger.warning("检测到500错误，尝试重置数据库连接池...")
        # 先释放连接池，然后下次访问时会自动重新创建
        from src.db.connection import recreate_engine
        recreate_engine()
        app.logger.info("数据库连接池已重置")
    except Exception as e:
        app.logger.error(f"重置数据库连接池失败: {str(e)}")

    # 返回友好的错误页面而不是JSON
    try:
        error_details = str(error)
        if hasattr(error, 'original_exception'):
            error_details += f"\n原始错误: {str(error.original_exception)}"

        # 记录堆栈跟踪到日志
        app.logger.error(f"错误堆栈: {traceback.format_exc()}")

        # 尝试渲染错误模板
        return render_template('error.html',
                              error_message="服务器内部错误",
                              error_details=f"应用程序遇到意外错误: {error_details}")
    except Exception as template_error:
        # 如果模板渲染失败，返回简单的HTML错误页面
        app.logger.error(f"渲染错误模板失败: {str(template_error)}")
        error_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>服务器错误</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                .error-container {{ max-width: 800px; margin: 40px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }}
                .error-title {{ color: #d9534f; }}
                .error-details {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; white-space: pre-wrap; }}
                .back-link {{ margin-top: 20px; display: inline-block; }}
            </style>
        </head>
        <body>
            <div class="error-container">
                <h1 class="error-title">服务器内部错误</h1>
                <p>抱歉，服务器遇到意外情况导致无法处理您的请求。</p>
                <div class="error-details">
                    <strong>错误详情:</strong>
                    {error_details}
                </div>
                <a href="/" class="back-link">返回首页</a>
            </div>
        </body>
        </html>
        """
        return error_html, 500

# 这个函数已在上面定义，此处删除重复定义

# 添加数据库连接池状态监控端点
@app.route('/api/connection_pool_status', methods=['GET'])
def api_connection_pool_status():
    """获取数据库连接池状态"""
    try:
        from src.db.connection import engine

        # 简化的连接池状态
        pool_info = {
            "success": True,
            "pool_status": {
                "engine_name": str(engine.name),
                "pool_class": str(engine.pool.__class__.__name__),
                "pool_status": "正常",
                "connection_info": {
                    "database": str(engine.url.database),
                    "driver": str(engine.driver)
                }
            }
        }

        # 尝试获取更多信息（如果可用）
        try:
            if hasattr(engine.pool, "size"):
                if callable(engine.pool.size):
                    pool_info["pool_status"]["size"] = engine.pool.size()
                else:
                    pool_info["pool_status"]["size"] = engine.pool.size

            if hasattr(engine.pool, "checkedout") and callable(engine.pool.checkedout):
                pool_info["pool_status"]["checked_out"] = engine.pool.checkedout()

            # 计算使用率（如果可能）
            if "size" in pool_info["pool_status"] and "checked_out" in pool_info["pool_status"]:
                size = pool_info["pool_status"]["size"]
                checked_out = pool_info["pool_status"]["checked_out"]
                if size > 0:
                    usage_percent = (checked_out / size) * 100
                    pool_info["pool_status"]["usage_percent"] = round(usage_percent, 2)

                    # 状态判断
                    if usage_percent < 80:
                        status = "正常"
                    elif usage_percent < 95:
                        status = "警告"
                    else:
                        status = "危险"
                    pool_info["pool_status"]["status"] = status
        except Exception as inner_e:
            logger.warning(f"获取详细连接池信息时出错: {str(inner_e)}")
            pool_info["pool_status"]["detailed_error"] = str(inner_e)

        return jsonify(pool_info)
    except Exception as e:
        logger.error(f"获取连接池状态时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# 添加性能日志API端点
@app.route('/api/log/performance', methods=['POST'])
def api_log_performance():
    """记录前端性能数据"""
    try:
        # 获取性能数据
        data = request.json if request.is_json else {}

        if not data:
            logger.warning("收到空的性能日志数据")
            return jsonify({
                "success": False,
                "error": "没有提供性能数据"
            }), 400

        # 记录性能数据
        logger.info(f"前端性能数据: {json.dumps(data)}")

        # 如果配置了性能监控数据库表，可以将数据保存到数据库
        try:
            from src.models.system_metric import SystemMetric

            # 创建会话
            session = Session()

            try:
                # 创建性能指标记录
                metric = SystemMetric(
                    metric_type="frontend_performance",
                    metric_name=data.get("type", "unknown"),
                    metric_value=data.get("duration", 0),
                    details=json.dumps(data),
                    created_at=datetime.now()
                )

                # 保存到数据库
                session.add(metric)
                session.commit()

                logger.debug("性能数据已保存到数据库")
            except Exception as db_error:
                logger.error(f"保存性能数据到数据库时出错: {str(db_error)}")
                session.rollback()
            finally:
                session.close()
        except ImportError:
            logger.debug("SystemMetric模型不可用，性能数据仅记录到日志")

        return jsonify({
            "success": True,
            "message": "性能数据已记录"
        })
    except Exception as e:
        logger.error(f"处理性能日志时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500



# 添加缓存管理端点
@app.route('/api/cache/status', methods=['GET'])
def api_cache_status():
    """获取分析结果缓存状态"""
    try:
        session = Session()

        # 获取所有缓存的分析结果
        cached_results = session.query(AnalysisResult).all()

        # 按小说ID分组
        cache_by_novel = {}
        for result in cached_results:
            if result.novel_id not in cache_by_novel:
                cache_by_novel[result.novel_id] = []

            # 计算缓存时间
            cache_age = None
            if result.updated_at:
                current_time = datetime.now(timezone.utc)
                cache_age = (current_time - result.updated_at).total_seconds()

            # 检查缓存是否过期
            cache_valid_seconds = config.CACHE_VALID_DAYS * 24 * 60 * 60
            is_expired = cache_age > cache_valid_seconds if cache_age else True

            cache_by_novel[result.novel_id].append({
                "dimension": result.dimension,
                "updated_at": result.updated_at.strftime("%Y-%m-%d %H:%M:%S") if result.updated_at else None,
                "cache_age_seconds": int(cache_age) if cache_age else None,
                "cache_age_days": round(cache_age / (24 * 60 * 60), 1) if cache_age else None,
                "is_expired": is_expired,
                "content_length": len(result.content) if result.content else 0
            })

        # 计算总体缓存统计信息
        total_cache_count = len(cached_results)
        total_novels_cached = len(cache_by_novel)
        expired_cache_count = sum(1 for result in cached_results
                                if result.updated_at and
                                (datetime.now(timezone.utc) - result.updated_at).total_seconds() > cache_valid_seconds)

        return jsonify({
            "success": True,
            "cache_status": {
                "total_cached_results": total_cache_count,
                "total_novels_cached": total_novels_cached,
                "expired_cache_count": expired_cache_count,
                "cache_valid_days": config.CACHE_VALID_DAYS,
                "cache_enabled": config.CACHE_ENABLED,
                "force_refresh": config.FORCE_REFRESH_CACHE,
                "cache_by_novel": cache_by_novel
            }
        })
    except Exception as e:
        logger.error(f"获取缓存状态时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@app.route('/api/cache/clear', methods=['POST'])
def api_clear_cache():
    """清除分析结果缓存"""
    session = None
    try:
        # 获取请求数据，支持JSON或表单数据
        if request.is_json:
            data = request.json or {}
        else:
            data = request.form.to_dict() or {}

        # 如果没有数据，尝试从URL参数获取
        if not data:
            data = request.args.to_dict() or {}

        # 获取过滤条件
        novel_id = data.get('novel_id')
        dimension = data.get('dimension')

        # 记录请求信息
        logger.info(f"收到缓存清理请求: novel_id={novel_id}, dimension={dimension}")

        # 创建数据库会话
        session = Session()

        # 构建查询
        query = session.query(AnalysisResult)

        # 如果指定了小说ID，只清除该小说的缓存
        if novel_id:
            try:
                novel_id = int(novel_id)
                query = query.filter_by(novel_id=novel_id)
            except (ValueError, TypeError):
                logger.warning(f"无效的小说ID: {novel_id}")
                return jsonify({
                    "success": False,
                    "error": f"无效的小说ID: {novel_id}"
                }), 400

        # 如果指定了维度，只清除该维度的缓存
        if dimension:
            query = query.filter_by(dimension=dimension)

        # 执行删除
        deleted_count = query.delete()
        session.commit()

        # 记录删除结果
        logger.info(f"缓存清理完成: 删除了 {deleted_count} 条记录")

        return jsonify({
            "success": True,
            "message": f"已清除 {deleted_count} 条缓存记录",
            "deleted_count": deleted_count,
            "filters": {
                "novel_id": novel_id,
                "dimension": dimension
            }
        })
    except Exception as e:
        logger.error(f"清除缓存时出错: {str(e)}")
        if session:
            session.rollback()
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        if session:
            session.close()

@app.route('/api/analysis/logs', methods=['GET'])
def api_get_analysis_logs():
    """获取特定小说的分析日志"""
    # 导入日志过滤器和内存监控状态
    try:
        from src.utils.log_filter import filter_logs
        from src.utils.memory_monitor import memory_warning, memory_critical
    except ImportError:
        # 如果无法导入，使用默认值
        filter_logs = lambda logs, novel_id, max_logs, memory_critical: logs[-max_logs:] if max_logs > 0 and logs else logs
        memory_warning = False
        memory_critical = False

    novel_id = request.args.get('novel_id')
    level = request.args.get('level', 'info')  # 筛选日志级别
    since = request.args.get('since')  # 筛选时间戳之后的日志
    limit = int(request.args.get('limit', 50))  # 限制返回数量
    is_initial = request.args.get('is_initial', 'false').lower() == 'true'  # 是否是初始请求
    dimension = request.args.get('dimension')  # 筛选特定维度的日志

    # 根据内存状态调整日志详细程度
    if memory_critical:
        # 内存紧张时，减少日志输出
        logger.debug(f"收到日志请求: novel_id={novel_id}, level={level}, limit={limit}")
    elif memory_warning:
        # 内存警告时，减少日志详细程度
        logger.info(f"收到日志请求: novel_id={novel_id}, level={level}")
    else:
        # 内存正常时，正常记录
        logger.info(f"收到日志请求: novel_id={novel_id}, level={level}, since={since}, limit={limit}, is_initial={is_initial}, dimension={dimension}")

    if not novel_id:
        logger.warning("缺少novel_id参数")
        return jsonify({
            "success": False,
            "error": "Missing novel_id parameter"
        }), 400

    try:
        novel_id = int(novel_id)

        # 获取小说标题（用于前端显示）
        session = Session()
        novel_title = None
        try:
            novel = session.query(Novel).filter_by(id=novel_id).first()
            if novel:
                novel_title = novel.title
                if not memory_warning:  # 内存正常时才记录
                    logger.info(f"找到小说标题: {novel_title}")
            else:
                logger.warning(f"找不到小说ID: {novel_id}")
        finally:
            session.close()

        # 获取日志数据
        logs = []
        if novel_id in analysis_logs:
            # 根据内存状态决定是否记录详细日志
            if not memory_warning:
                logger.info(f"找到小说ID {novel_id} 的日志，共 {len(analysis_logs[novel_id])} 条")

            # 筛选级别
            if level == 'all':
                filtered_logs = analysis_logs[novel_id]
            else:
                filtered_logs = [log for log in analysis_logs[novel_id] if log['level'] == level]

            # 根据内存状态决定是否记录详细日志
            if not memory_warning:
                logger.info(f"按级别 {level} 筛选后，剩余 {len(filtered_logs)} 条日志")

            # 筛选时间戳
            if since:
                filtered_logs = [log for log in filtered_logs if log['timestamp'] > since]
                if not memory_warning:
                    logger.info(f"按时间戳 {since} 筛选后，剩余 {len(filtered_logs)} 条日志")

            # 筛选特定维度
            if dimension and dimension != 'all':
                filtered_logs = [log for log in filtered_logs if log.get('dimension') == dimension]
                if not memory_warning:
                    logger.info(f"按维度 {dimension} 筛选后，剩余 {len(filtered_logs)} 条日志")

            # 使用日志过滤器进行过滤
            # 根据内存状态调整最大日志数量
            max_logs = limit
            if memory_critical:
                max_logs = min(limit, 30)  # 内存危险时，大幅减少日志数量
            elif memory_warning:
                max_logs = min(limit, 50)  # 内存警告时，减少日志数量

            # 应用日志过滤器
            logs = filter_logs(
                filtered_logs,
                novel_id,
                max_logs=max_logs,
                memory_critical=memory_critical
            )

            # 根据内存状态决定是否记录详细日志
            if not memory_critical:
                logger.info(f"最终返回 {len(logs)} 条日志")
        else:
            if not memory_warning:
                logger.info(f"没有找到小说ID {novel_id} 的日志")

        # 检查是否有分析正在进行
        is_running = analysis_running.get(novel_id, False)
        if not memory_warning:
            logger.info(f"小说ID {novel_id} 的分析状态: {'运行中' if is_running else '未运行'}")

        # 如果是初始请求且没有日志，添加一条欢迎消息
        if is_initial and not logs and not memory_critical:
            # 添加欢迎消息，确保前端显示有内容
            welcome_log = {
                "timestamp": datetime.now().isoformat(),
                "message": f"开始分析小说《{novel_title}》，请稍候...",
                "level": "info",
                "dimension": None,
                "progress": None
            }
            logs.append(welcome_log)
            if not memory_warning:
                logger.debug("添加了欢迎消息")

        # 如果有正在进行的分析但没有最近的日志，添加一条状态消息
        # 内存紧张时不添加额外消息
        if is_running and not memory_critical and (not logs or (datetime.now().timestamp() - datetime.fromisoformat(logs[-1]['timestamp']).timestamp() > 10)):
            status_log = {
                "timestamp": datetime.now().isoformat(),
                "message": "分析正在进行中，请稍候...",
                "level": "info",
                "dimension": None,
                "progress": None
            }
            logs.append(status_log)
            if not memory_warning:
                logger.debug("添加了状态消息")

        # 添加内存状态信息
        memory_status = "normal"
        if memory_critical:
            memory_status = "critical"
        elif memory_warning:
            memory_status = "warning"

        response_data = {
            "success": True,
            "logs": logs,
            "novel_title": novel_title,
            "timestamp": datetime.now().isoformat(),
            "is_running": is_running,
            "memory_status": memory_status  # 添加内存状态信息
        }

        # 根据内存状态决定是否记录详细日志
        if not memory_warning:
            logger.info(f"返回日志响应: success=True, logs_count={len(logs)}, novel_title={novel_title}, is_running={is_running}")

        return jsonify(response_data)
    except Exception as e:
        logger.error(f"获取分析日志时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# 注册分析过程查看修复蓝图（优先注册修复版本）
try:
    from src.web.routes.analysis_process_fix import analysis_process_fix_bp
    app.register_blueprint(analysis_process_fix_bp)
    logger.info("已注册分析过程查看修复蓝图")
except ImportError as e:
    logger.warning(f"无法注册分析过程查看修复蓝图: {str(e)}")

# 注册分析过程查看蓝图（作为备用）
try:
    from src.web.routes.analysis_process import analysis_process_bp
    app.register_blueprint(analysis_process_bp)
    logger.info("已注册分析过程查看蓝图（备用）")
except ImportError as e:
    logger.warning(f"无法注册分析过程查看蓝图: {str(e)}")

# 这里已经注册了分析过程查看修复蓝图，不需要重复注册

@app.route('/simplified')
def simplified_index():
    """提供一个简化版的首页，减少对复杂JavaScript和模板继承的依赖"""
    try:
        session = Session()
        try:
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()
            return render_template('simple_index.html', novels=novels)
        except Exception as db_error:
            app.logger.error(f"简化版首页数据库操作出错: {str(db_error)}", exc_info=True)
            return render_template('simple_index.html', novels=[])
        finally:
            session.close()
    except Exception as e:
        app.logger.error(f"加载简化版首页时出错: {str(e)}", exc_info=True)
        # 提供一个非常基本的HTML响应
        return f"""
        <html>
            <head><title>九猫小说分析系统</title></head>
            <body style="font-family:Arial,sans-serif;padding:20px;">
                <h1>九猫小说分析系统</h1>
                <p>出现临时错误，请<a href="/simplified">刷新页面</a>或<a href="/">返回首页</a></p>
                <p>错误信息: {str(e)}</p>
            </body>
        </html>
        """

@app.route('/novels')
def novels():
    """小说列表页面"""
    try:
        session = Session()
        try:
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()
            return render_template('novels.html', novels=novels)
        finally:
            session.close()
    except Exception as e:
        app.logger.error(f"加载小说列表页面时出错: {str(e)}", exc_info=True)
        return render_template('novels.html', novels=[])

@app.route('/analysis')
def analysis():
    """分析记录页面"""
    try:
        session = Session()
        try:
            # 获取所有分析结果
            results = session.query(AnalysisResult).order_by(AnalysisResult.created_at.desc()).all()
            # 创建一个空的novel对象，防止模板中的novel.id等属性未定义
            empty_novel = type('EmptyNovel', (), {'id': None, 'title': '所有小说'})
            # 创建一个空的result对象，防止模板中的result.dimension等属性未定义
            empty_result = type('EmptyResult', (), {'dimension': '所有维度', 'metadata': {}})
            return render_template('analysis.html', novel=empty_novel, result=empty_result, novels=[], analysis_results=results)
        finally:
            session.close()
    except Exception as e:
        app.logger.error(f"加载分析记录页面时出错: {str(e)}", exc_info=True)
        # 创建一个空的novel对象和result对象，防止模板中的属性未定义
        empty_novel = type('EmptyNovel', (), {'id': None, 'title': '所有小说'})
        empty_result = type('EmptyResult', (), {'dimension': '所有维度', 'metadata': {}})
        return render_template('analysis.html', novel=empty_novel, result=empty_result, novels=[])

@app.route('/dashboard')
def dashboard():
    """数据中心页面"""
    try:
        session = Session()
        try:
            # 获取统计数据
            novel_count = session.query(Novel).count()
            analysis_count = session.query(AnalysisResult).count()

            # 获取最近的小说
            recent_novels = session.query(Novel).order_by(Novel.created_at.desc()).limit(5).all()

            # 获取最近的分析结果
            recent_analyses = session.query(AnalysisResult).order_by(AnalysisResult.created_at.desc()).limit(5).all()

            return render_template('dashboard.html',
                                 novels=recent_novels,
                                 novel_count=novel_count,
                                 analysis_count=analysis_count,
                                 recent_analyses=recent_analyses)
        finally:
            session.close()
    except Exception as e:
        app.logger.error(f"加载数据中心页面时出错: {str(e)}", exc_info=True)
        return render_template('dashboard.html', novels=[])

@app.route('/settings')
def settings():
    """设置页面"""
    try:
        # 获取系统配置信息
        system_config = {
            'api_key': config.DEEPSEEK_API_KEY[:6] + '...' + config.DEEPSEEK_API_KEY[-4:] if config.DEEPSEEK_API_KEY else 'Not configured',
            'debug_mode': config.DEBUG,
            'cache_enabled': config.CACHE_ENABLED,
            'parallel_analysis': config.PARALLEL_ANALYSIS_ENABLED,
            'max_workers': config.MAX_WORKERS,
            'thread_pool_size': config.THREAD_POOL_SIZE,
            'default_model': config.DEFAULT_MODEL
        }

        # 创建必要的对象和数据，防止模板中的变量未定义
        last_updated = datetime.now()
        system_metrics = {
            'cpu_usage': '0',
            'memory_usage': '0',
            'disk_usage': '0',
            'uptime': '0天0小时0分钟'
        }
        api_stats = {
            'today_calls': 0,
            'week_calls': 0,
            'month_calls': 0,
            'time_labels': [],
            'call_counts': [],
            'success_count': 0,
            'error_count': 0,
            'timeout_count': 0,
            'success_rate': '0',
            'avg_response_time': '0'
        }
        db_stats = {
            'active_connections': 0,
            'pool_size': config.SQLALCHEMY_POOL_SIZE,
            'overflow': 0,
            'status': 'healthy'
        }
        alerts = []
        alert_settings = {
            'enabled': False,
            'email_enabled': False
        }
        logs = []

        return render_template('system_monitor.html',
                              system_config=system_config,
                              last_updated=last_updated,
                              system_metrics=system_metrics,
                              api_stats=api_stats,
                              db_stats=db_stats,
                              alerts=alerts,
                              alert_settings=alert_settings,
                              logs=logs)
    except Exception as e:
        app.logger.error(f"加载设置页面时出错: {str(e)}", exc_info=True)
        return render_template('error.html',
                             error_message="加载设置页面时出错",
                             error_details=str(e),
                             back_url=url_for('index'))

if __name__ == '__main__':
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=config.DEBUG
    )
