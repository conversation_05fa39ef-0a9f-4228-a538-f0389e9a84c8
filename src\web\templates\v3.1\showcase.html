{% extends "v3.1/base.html" %}

{% block title %}展示台 - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    /* 展示台特定样式 */
    .showcase-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px var(--shadow-color);
        position: relative;
        overflow: hidden;
    }

    .showcase-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .showcase-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    }

    .showcase-header p {
        position: relative;
        z-index: 1;
        opacity: 0.9;
    }

    .stat-card {
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.4s ease;
        border: none;
        height: 100%;
    }

    .stat-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 20px var(--shadow-color);
    }

    .stat-card-header {
        padding: 1.5rem;
        text-align: center;
        color: white;
    }

    .stat-card-body {
        padding: 1.5rem;
        text-align: center;
        background-color: var(--card-bg);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        width: 80px;
        height: 80px;
        line-height: 80px;
        border-radius: 50%;
    }

    .stat-value {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stat-label {
        font-size: 1.1rem;
        opacity: 0.8;
    }

    .chart-container {
        height: 300px;
        position: relative;
    }

    .chart-card {
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.4s ease;
    }

    .chart-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px var(--shadow-color);
    }

    .chart-card-header {
        padding: 1.25rem;
        color: white;
        font-weight: 600;
    }

    .chart-card-body {
        padding: 1.5rem;
        background-color: var(--card-bg);
    }

    .activity-item {
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .activity-item:hover {
        background-color: var(--shadow-color);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        margin-right: 1rem;
        color: white;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .activity-time {
        font-size: 0.85rem;
        opacity: 0.7;
    }

    .performance-item {
        margin-bottom: 1.5rem;
    }

    .performance-item:last-child {
        margin-bottom: 0;
    }

    .performance-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .performance-title {
        font-weight: 600;
    }

    .performance-value {
        font-weight: 600;
    }

    .performance-progress {
        height: 0.75rem;
        border-radius: 1rem;
        overflow: hidden;
    }
</style>
{% endblock %}

{% block content %}
<!-- 展示台头部 -->
<div class="showcase-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">
                <i class="fas fa-chart-bar me-3"></i>创作展示台
            </h1>
            <p class="lead mb-0">
                展示台汇总了您的创作成果和系统运行状态，帮助您直观了解创作进度和系统性能。
                <br>所有数据实时更新，反映最新状态。
            </p>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <i class="fas fa-chart-line fa-5x" style="opacity: 0.5;"></i>
        </div>
    </div>
</div>

<!-- 主要统计数据 -->
<div class="row mb-5">
    <div class="col-md-3 mb-4">
        <div class="stat-card shadow">
            <div class="stat-card-header" style="background: linear-gradient(135deg, #FF6B6B, #FF8E8E);">
                <i class="fas fa-book-open stat-icon"></i>
            </div>
            <div class="stat-card-body">
                <h2 class="stat-value">{{ generated_content_stats.total_chapters if generated_content_stats else 0 }}</h2>
                <p class="stat-label mb-0">已创作章节</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="stat-card shadow">
            <div class="stat-card-header" style="background: linear-gradient(135deg, #36D1DC, #5B86E5);">
                <i class="fas fa-font stat-icon"></i>
            </div>
            <div class="stat-card-body">
                <h2 class="stat-value">{{ generated_content_stats.total_words if generated_content_stats else 0 }}</h2>
                <p class="stat-label mb-0">总字数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="stat-card shadow">
            <div class="stat-card-header" style="background: linear-gradient(135deg, #11998e, #38ef7d);">
                <i class="fas fa-calculator stat-icon"></i>
            </div>
            <div class="stat-card-body">
                <h2 class="stat-value">{{ generated_content_stats.avg_words_per_chapter if generated_content_stats else 0 }}</h2>
                <p class="stat-label mb-0">平均字数/章</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="stat-card shadow">
            <div class="stat-card-header" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-light));">
                <i class="fas fa-clock stat-icon"></i>
            </div>
            <div class="stat-card-body">
                <h2 class="stat-value">{{ generated_content_stats.total_hours if generated_content_stats else 0 }}</h2>
                <p class="stat-label mb-0">运行时间(小时)</p>
            </div>
        </div>
    </div>
</div>

<!-- 图表和活动记录 -->
<div class="row mb-5">
    <!-- 创作趋势图表 -->
    <div class="col-lg-8 mb-4">
        <div class="chart-card shadow">
            <div class="chart-card-header" style="background: linear-gradient(to right, var(--primary-color), var(--primary-light));">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0"><i class="fas fa-chart-line me-2"></i>创作趋势</h3>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-light" id="weekBtn">周</button>
                        <button type="button" class="btn btn-sm btn-light active" id="monthBtn">月</button>
                        <button type="button" class="btn btn-sm btn-light" id="yearBtn">年</button>
                    </div>
                </div>
            </div>
            <div class="chart-card-body">
                <div class="chart-container" id="creationTrendChart"></div>
            </div>
        </div>
    </div>

    <!-- 最近活动 -->
    <div class="col-lg-4 mb-4">
        <div class="chart-card shadow h-100">
            <div class="chart-card-header" style="background: linear-gradient(to right, #FF6B6B, #FF8E8E);">
                <h3 class="mb-0"><i class="fas fa-history me-2"></i>最近活动</h3>
            </div>
            <div class="chart-card-body p-0">
                <div class="activity-item d-flex align-items-center">
                    <div class="activity-icon" style="background-color: #36D1DC;">
                        <i class="fas fa-pencil-alt"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">创建了新章节</div>
                        <div class="activity-meta d-flex justify-content-between">
                            <span>第2章 - 黎明的曙光</span>
                            <span class="activity-time">今天 14:30</span>
                        </div>
                    </div>
                </div>
                <div class="activity-item d-flex align-items-center">
                    <div class="activity-icon" style="background-color: #11998e;">
                        <i class="fas fa-magic"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">完成自动写作</div>
                        <div class="activity-meta d-flex justify-content-between">
                            <span>1,500字内容已生成</span>
                            <span class="activity-time">昨天 18:45</span>
                        </div>
                    </div>
                </div>
                <div class="activity-item d-flex align-items-center">
                    <div class="activity-icon" style="background-color: var(--primary-color);">
                        <i class="fas fa-bookmark"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">设置参考蓝本</div>
                        <div class="activity-meta d-flex justify-content-between">
                            <span>《星辰大海》已设为蓝本</span>
                            <span class="activity-time">3天前</span>
                        </div>
                    </div>
                </div>
                <div class="activity-item d-flex align-items-center">
                    <div class="activity-icon" style="background-color: #5B86E5;">
                        <i class="fas fa-upload"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">上传新小说</div>
                        <div class="activity-meta d-flex justify-content-between">
                            <span>《星辰大海》(15万字)</span>
                            <span class="activity-time">5天前</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 内容分布和系统性能 -->
<div class="row">
    <!-- 内容分布 -->
    <div class="col-lg-5 mb-4">
        <div class="chart-card shadow">
            <div class="chart-card-header" style="background: linear-gradient(to right, #11998e, #38ef7d);">
                <h3 class="mb-0"><i class="fas fa-chart-pie me-2"></i>内容分布</h3>
            </div>
            <div class="chart-card-body">
                <div class="chart-container" id="contentDistributionChart"></div>
            </div>
        </div>
    </div>

    <!-- 系统性能 -->
    <div class="col-lg-7 mb-4">
        <div class="chart-card shadow">
            <div class="chart-card-header" style="background: linear-gradient(to right, #36D1DC, #5B86E5);">
                <h3 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>系统性能</h3>
            </div>
            <div class="chart-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="performance-item">
                            <div class="performance-label">
                                <span class="performance-title">CPU使用率</span>
                                <span class="performance-value">25%</span>
                            </div>
                            <div class="progress performance-progress">
                                <div class="progress-bar" role="progressbar" style="width: 25%; background: linear-gradient(to right, #11998e, #38ef7d);" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-label">
                                <span class="performance-title">内存使用率</span>
                                <span class="performance-value">40%</span>
                            </div>
                            <div class="progress performance-progress">
                                <div class="progress-bar" role="progressbar" style="width: 40%; background: linear-gradient(to right, #36D1DC, #5B86E5);" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="performance-item">
                            <div class="performance-label">
                                <span class="performance-title">磁盘使用率</span>
                                <span class="performance-value">60%</span>
                            </div>
                            <div class="progress performance-progress">
                                <div class="progress-bar" role="progressbar" style="width: 60%; background: linear-gradient(to right, #FF6B6B, #FF8E8E);" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-label">
                                <span class="performance-title">API调用限额</span>
                                <span class="performance-value">30%</span>
                            </div>
                            <div class="progress performance-progress">
                                <div class="progress-bar" role="progressbar" style="width: 30%; background: linear-gradient(to right, var(--primary-color), var(--primary-light));" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <h4 class="mb-0">150</h4>
                                <p class="text-muted mb-0">API调用次数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <h4 class="mb-0">10</h4>
                                <p class="text-muted mb-0">小说总数</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // 创作趋势图表 - 月视图（默认）
        const monthLabels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
        const monthChapterData = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        const monthWordData = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];

        // 周视图数据
        const weekLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        const weekChapterData = [0, 0, 0, 0, 0, 0, 0];
        const weekWordData = [0, 0, 0, 0, 0, 0, 0];

        // 年视图数据
        const yearLabels = ['2020', '2021', '2022', '2023', '2024', '2025'];
        const yearChapterData = [0, 0, 0, 0, 0, 0];
        const yearWordData = [0, 0, 0, 0, 0, 0];

        // 检查元素是否存在
        const creationTrendElement = document.getElementById('creationTrendChart');
        if (!creationTrendElement) {
            console.error('找不到creationTrendChart元素');
            return;
        }

        // 创建图表
        const creationTrendCtx = creationTrendElement.getContext('2d');
        if (!creationTrendCtx) {
            console.error('无法获取creationTrendChart的2D上下文');
            return;
        }

        const creationTrendChart = new Chart(creationTrendCtx, {
            type: 'line',
            data: {
                labels: monthLabels,
                datasets: [{
                    label: '章节数',
                    data: monthChapterData,
                    borderColor: 'rgba(230, 180, 34, 1)',
                    backgroundColor: 'rgba(230, 180, 34, 0.1)',
                    tension: 0.4,
                    fill: true,
                    borderWidth: 3
                }, {
                    label: '字数(千)',
                    data: monthWordData,
                    borderColor: 'rgba(17, 153, 142, 1)',
                    backgroundColor: 'rgba(17, 153, 142, 0.1)',
                    tension: 0.4,
                    fill: true,
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            font: {
                                size: 14
                            },
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: 'rgba(200, 200, 200, 0.5)',
                        borderWidth: 1,
                        padding: 15,
                        boxPadding: 10,
                        cornerRadius: 8,
                        titleFont: {
                            size: 16,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 14
                        },
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    if (context.dataset.label === '章节数') {
                                        label += context.parsed.y + ' 章';
                                    } else {
                                        label += context.parsed.y + 'k 字';
                                    }
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(200, 200, 200, 0.2)'
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            padding: 10
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(200, 200, 200, 0.2)'
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            padding: 10
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                }
            }
        });

        // 切换视图按钮事件
        $('#weekBtn').click(function() {
            $(this).addClass('active').siblings().removeClass('active');
            updateChart(creationTrendChart, weekLabels, weekChapterData, weekWordData);
        });

        $('#monthBtn').click(function() {
            $(this).addClass('active').siblings().removeClass('active');
            updateChart(creationTrendChart, monthLabels, monthChapterData, monthWordData);
        });

        $('#yearBtn').click(function() {
            $(this).addClass('active').siblings().removeClass('active');
            updateChart(creationTrendChart, yearLabels, yearChapterData, yearWordData);
        });

        // 更新图表函数
        function updateChart(chart, labels, chapterData, wordData) {
            chart.data.labels = labels;
            chart.data.datasets[0].data = chapterData;
            chart.data.datasets[1].data = wordData;
            chart.update();
        }

        // 内容分布图表
        const contentDistributionElement = document.getElementById('contentDistributionChart');
        if (!contentDistributionElement) {
            console.error('找不到contentDistributionChart元素');
            return;
        }

        const contentDistributionCtx = contentDistributionElement.getContext('2d');
        if (!contentDistributionCtx) {
            console.error('无法获取contentDistributionChart的2D上下文');
            return;
        }

        const contentDistributionChart = new Chart(contentDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['暂无章节'],
                datasets: [{
                    data: [0],
                    backgroundColor: [
                        'rgba(230, 180, 34, 0.8)'
                    ],
                    borderColor: [
                        'rgba(230, 180, 34, 1)'
                    ],
                    borderWidth: 2,
                    hoverOffset: 15
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '65%',
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 12
                            },
                            padding: 15,
                            boxWidth: 15,
                            boxHeight: 15
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: 'rgba(200, 200, 200, 0.5)',
                        borderWidth: 1,
                        padding: 15,
                        boxPadding: 10,
                        cornerRadius: 8,
                        titleFont: {
                            size: 16,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 14
                        },
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} 字 (${percentage}%)`;
                            }
                        }
                    }
                },
                elements: {
                    arc: {
                        borderWidth: 2
                    }
                },
                animation: {
                    animateScale: true,
                    animateRotate: true
                }
            }
        });

        // 添加中心文本
        Chart.register({
            id: 'doughnutCenterText',
            beforeDraw: function(chart) {
                if (chart.config.type === 'doughnut') {
                    // 获取画布上下文
                    const ctx = chart.ctx;

                    // 保存当前状态
                    ctx.save();

                    // 计算中心位置
                    const centerX = (chart.chartArea.left + chart.chartArea.right) / 2;
                    const centerY = (chart.chartArea.top + chart.chartArea.bottom) / 2;

                    // 计算总字数
                    const total = chart.data.datasets[0].data.reduce((a, b) => a + b, 0);

                    // 绘制总字数
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.font = 'bold 24px Arial';
                    ctx.fillStyle = '#333';
                    ctx.fillText(total, centerX, centerY - 10);

                    // 绘制"总字数"文本
                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#666';
                    ctx.fillText('总字数', centerX, centerY + 15);

                    // 恢复状态
                    ctx.restore();
                }
            }
        });
    });
</script>
{% endblock %}