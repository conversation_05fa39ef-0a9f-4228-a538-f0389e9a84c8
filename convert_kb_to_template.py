#!/usr/bin/env python
"""
将知识库内容转化为预设模板的工具
使用DeepSeek API根据知识库内容生成完整的15维度预设模板
"""
import os
import sys
import json
import logging
import argparse
from datetime import datetime
import requests
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的类和函数
from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.preset import Preset
from src.models.knowledge_base import KnowledgeBase

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'convert_kb_to_template_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

# DeepSeek API配置
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"
DEEPSEEK_API_KEY = os.environ.get("DEEPSEEK_API_KEY", "")  # 从环境变量获取API密钥

class KnowledgeBaseTemplateConverter:
    """知识库预设模板转换器"""
    
    def __init__(self, api_key=None):
        """
        初始化转换器
        
        Args:
            api_key: DeepSeek API密钥（可选，默认从环境变量获取）
        """
        self.api_key = api_key or DEEPSEEK_API_KEY
        if not self.api_key:
            logger.warning("未设置DeepSeek API密钥，将无法调用AI生成预设模板")
    
    def convert_kb_to_template(self, kb_id: int) -> Dict:
        """
        将知识库内容转换为预设模板
        
        Args:
            kb_id: 知识库ID
        
        Returns:
            转换结果
        """
        try:
            logger.info(f"开始将知识库(ID: {kb_id})的内容转换为预设模板")
            
            session = Session()
            try:
                # 获取知识库
                kb = session.query(KnowledgeBase).get(kb_id)
                if not kb:
                    logger.error(f"转换失败: 未找到ID为{kb_id}的知识库")
                    return {
                        'success': False,
                        'error': '未找到指定知识库'
                    }
                
                # 获取知识库内容
                kb_content = kb.content
                if not kb_content:
                    logger.error(f"转换失败: ID为{kb_id}的知识库内容为空")
                    return {
                        'success': False,
                        'error': '知识库内容为空'
                    }
                
                # 提取知识库摘要信息
                kb_summary = {
                    'id': kb.id,
                    'title': kb.title,
                    'content_preview': kb.content[:2000] + "..." if len(kb.content) > 2000 else kb.content,
                    'created_at': kb.created_at.isoformat() if kb.created_at else None,
                    'updated_at': kb.updated_at.isoformat() if kb.updated_at else None,
                    'metadata': kb.metadata
                }
                
                # 生成预设模板
                template_result = self._generate_preset_template(kb)
                
                if not template_result.get('success'):
                    return template_result
                
                # 获取生成的预设模板
                book_template = template_result.get('book_template', '')
                chapter_template = template_result.get('chapter_template', '')
                
                # 保存预设模板到数据库
                preset_id = self._save_preset_template(session, kb, book_template, chapter_template)
                
                logger.info(f"知识库《{kb.title}》(ID: {kb_id})的内容已成功转换为预设模板")
                
                return {
                    'success': True,
                    'message': f'知识库《{kb.title}》的内容已成功转换为预设模板',
                    'preset_id': preset_id,
                    'book_template': book_template,
                    'chapter_template': chapter_template
                }
            finally:
                session.close()
                logger.info(f"数据库会话已关闭")
        except Exception as e:
            logger.error(f"转换知识库为预设模板时出错: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_preset_template(self, kb: KnowledgeBase) -> Dict:
        """
        生成预设模板
        
        Args:
            kb: 知识库对象
        
        Returns:
            生成结果
        """
        logger.info(f"开始生成知识库《{kb.title}》的预设模板")
        
        if not self.api_key:
            logger.error("生成失败: 未设置DeepSeek API密钥")
            return {
                'success': False,
                'error': '未设置DeepSeek API密钥，无法调用AI生成预设模板'
            }
        
        try:
            # 准备请求数据
            book_prompt = self._prepare_book_template_prompt(kb)
            chapter_prompt = self._prepare_chapter_template_prompt(kb)
            
            # 调用API生成整本书预设模板
            book_template = self._call_deepseek_api(book_prompt)
            
            # 调用API生成章节预设模板
            chapter_template = self._call_deepseek_api(chapter_prompt)
            
            if not book_template or not chapter_template:
                logger.error("生成失败: API调用返回空结果")
                return {
                    'success': False,
                    'error': 'API调用返回空结果'
                }
            
            logger.info(f"成功生成知识库《{kb.title}》的预设模板")
            
            return {
                'success': True,
                'book_template': book_template,
                'chapter_template': chapter_template
            }
        except Exception as e:
            logger.error(f"生成预设模板时出错: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e)
            }
    
    def _prepare_book_template_prompt(self, kb: KnowledgeBase) -> str:
        """
        准备整本书预设模板生成的提示语
        
        Args:
            kb: 知识库对象
        
        Returns:
            提示语
        """
        return f"""你是一位网络小说写作顾问，需要根据以下知识库内容，创建一个详细的小说预设模板，包含以下15个维度：
语言风格、节奏节拍、结构分析、句式变化、段落长度、视角变化、段落流畅度、小说特点、世界构建、人物关系、开篇效果、高潮节奏、章纲分析、大纲分析、热梗统计。

知识库内容：
标题：{kb.title}
内容：
{kb.content[:10000]}...

需求：
1. 请根据知识库内容，为每个维度生成详细的预设模板，应支撑30-300万字的长篇小说创作。
2. 模板应包含具体的数值和比例，例如"短段落(1-3行)占比50%"而非泛泛而谈。
3. 语言风格、节奏节拍、结构分析、句式变化、段落长度、视角变化、段落流畅度、小说特点等维度应预填写具体内容。
4. 章纲分析、大纲分析、热梗统计等维度需要参考原文创造全新的内容，不要照抄原文。
5. 请创建一个完整的、条理清晰的预设模板，以Markdown格式输出。

请输出一个完整的、结构化的小说预设模板："""
    
    def _prepare_chapter_template_prompt(self, kb: KnowledgeBase) -> str:
        """
        准备章节预设模板生成的提示语
        
        Args:
            kb: 知识库对象
        
        Returns:
            提示语
        """
        return f"""你是一位网络小说写作顾问，需要根据以下知识库内容，创建一个详细的小说章节预设模板，包含以下15个维度：
语言风格、节奏节拍、结构分析、句式变化、段落长度、视角变化、段落流畅度、小说特点、世界构建、人物关系、开篇效果、高潮节奏、章纲分析、大纲分析、热梗统计。

知识库内容：
标题：{kb.title}
内容：
{kb.content[:10000]}...

需求：
1. 请根据知识库内容，为每个维度生成详细的章节级预设模板，应能指导单个章节的写作。
2. 模板应包含具体的数值和比例，例如"本章对话占比45%，叙述占比55%"而非泛泛而谈。
3. 语言风格、节奏节拍、结构分析、句式变化、段落长度、视角变化、段落流畅度、小说特点等维度应预填写具体内容。
4. 章纲分析、大纲分析、热梗统计等维度需要参考原文创造全新的内容，不要照抄原文。
5. 请考虑章节与整书的关系，包括章节在整体结构中的位置、与前后章节的关系等。
6. 请创建一个完整的、条理清晰的章节预设模板，以Markdown格式输出。

请输出一个完整的、结构化的小说章节预设模板："""
    
    def _call_deepseek_api(self, prompt: str) -> str:
        """
        调用DeepSeek API生成内容
        
        Args:
            prompt: 提示语
        
        Returns:
            生成的内容
        """
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": "deepseek-chat",
                "messages": [
                    {"role": "system", "content": "你是一位专业的网络小说写作顾问，擅长创建详细的预设模板。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 4000
            }
            
            response = requests.post(DEEPSEEK_API_URL, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            return content
        except requests.exceptions.RequestException as e:
            logger.error(f"API调用失败: {str(e)}")
            return ""
    
    def _save_preset_template(self, session, kb: KnowledgeBase, book_template: str, chapter_template: str) -> int:
        """
        保存预设模板到数据库
        
        Args:
            session: 数据库会话
            kb: 知识库对象
            book_template: 整本书预设模板
            chapter_template: 章节预设模板
        
        Returns:
            预设ID
        """
        logger.info(f"开始保存知识库《{kb.title}》的预设模板到数据库")
        
        # 创建预设内容
        preset_title = f"预设模板 - {kb.title} (知识库转换)"
        preset_content = f"""# 预设模板: {kb.title} (知识库转换)

## 基本信息
- 知识库: {kb.title}
- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 整本书预设模板
包含15个维度的预设模板

## 章节预设模板
包含15个维度的章节预设模板
"""
        
        # 创建预设对象
        preset = Preset(
            title=preset_title,
            content=preset_content,
            category='preset_template',
            created_at=datetime.now(),
            updated_at=datetime.now(),
            meta_info={
                'kb_id': kb.id,
                'kb_title': kb.title,
                'dimension_count': 15,
                'book_template': book_template,
                'chapter_template': chapter_template,
                'created_at': datetime.now().isoformat(),
                'source': 'knowledge_base'
            }
        )
        
        # 保存预设对象
        session.add(preset)
        session.commit()
        
        # 获取预设ID
        preset_id = preset.id
        
        logger.info(f"成功保存知识库《{kb.title}》的预设模板到数据库，预设ID: {preset_id}")
        
        return preset_id

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="将知识库内容转换为预设模板")
    parser.add_argument("kb_id", type=int, help="知识库ID")
    parser.add_argument("--api-key", type=str, help="DeepSeek API密钥")
    args = parser.parse_args()
    
    api_key = args.api_key or os.environ.get("DEEPSEEK_API_KEY")
    if not api_key:
        logger.error("未设置DeepSeek API密钥，请通过--api-key参数或DEEPSEEK_API_KEY环境变量设置")
        sys.exit(1)
    
    converter = KnowledgeBaseTemplateConverter(api_key)
    result = converter.convert_kb_to_template(args.kb_id)
    
    if result.get('success'):
        logger.info(f"转换成功! 预设模板ID: {result.get('preset_id')}")
        logger.info("生成的整本书预设模板预览:")
        logger.info(result.get('book_template')[:500] + "...")
        logger.info("生成的章节预设模板预览:")
        logger.info(result.get('chapter_template')[:500] + "...")
    else:
        logger.error(f"转换失败: {result.get('error')}")
        sys.exit(1)

if __name__ == "__main__":
    main() 