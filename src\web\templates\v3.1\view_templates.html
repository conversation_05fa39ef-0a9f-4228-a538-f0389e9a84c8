{% extends "v3.1/base.html" %}

{% block title %}设定模板详情 - {{ novel.title }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .template-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px var(--shadow-color);
        position: relative;
        overflow: hidden;
    }

    .template-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .template-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    }

    .template-meta {
        position: relative;
        z-index: 1;
        opacity: 0.9;
    }

    .template-card {
        transition: all 0.3s ease;
        height: 100%;
        border: 1px solid var(--border-color);
    }

    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px var(--shadow-color);
    }

    .template-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }

    .template-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .chapter-list {
        max-height: 600px;
        overflow-y: auto;
    }

    .chapter-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .chapter-item:hover {
        background-color: var(--shadow-color);
    }

    .chapter-number {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: var(--primary-light);
        color: var(--dark-color);
        font-weight: 600;
        margin-right: 1rem;
    }

    .chapter-title {
        flex: 1;
    }

    .tab-content {
        padding: 1.5rem;
    }

    .dimension-list {
        max-height: 500px;
        overflow-y: auto;
    }

    .dimension-item {
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .dimension-item:hover {
        background-color: var(--shadow-color);
    }

    .dimension-item:last-child {
        border-bottom: none;
    }

    .dimension-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: var(--primary-light);
        color: var(--dark-color);
        margin-right: 1rem;
    }

    .dimension-content {
        flex: 1;
    }

    .dimension-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .dimension-description {
        font-size: 0.9rem;
        color: var(--text-muted);
    }

    .template-content {
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        line-height: 1.8;
        padding: 1rem;
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 设定模板头部信息 -->
<div class="template-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">设定模板详情</h1>
            <div class="template-meta">
                <p class="lead mb-2">
                    <i class="fas fa-book me-2"></i>{{ novel.title }}
                </p>
                <p class="mb-0">
                    <i class="fas fa-user me-2"></i>{{ novel.author or '未知作者' }}
                </p>
            </div>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3_1.view_novel', novel_id=novel.id) }}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-1"></i>返回小说
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-12">
        <ul class="nav nav-tabs" id="templateTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="book-tab" data-bs-toggle="tab" data-bs-target="#book" type="button" role="tab" aria-controls="book" aria-selected="true">
                    <i class="fas fa-book me-1"></i><strong>整本书设定模板</strong>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">
                    <i class="fas fa-book-open me-1"></i><strong>章节设定模板</strong>
                </button>
            </li>
        </ul>
        <div class="tab-content" id="templateTabContent">
            <!-- 整本书设定模板标签页 -->
            <div class="tab-pane fade show active" id="book" role="tabpanel" aria-labelledby="book-tab">
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h3 class="card-title mb-0"><i class="fas fa-cog me-2"></i>整本书设定模板</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            整本书设定模板包含了15个维度的分析提示词，用于指导AI进行整本书的分析。
                        </div>

                        <div class="dimension-list">
                            {% for dimension in dimensions %}
                            <div class="dimension-item d-flex align-items-start">
                                <div class="dimension-icon">
                                    <i class="fas fa-{{ dimension.icon|default('search') }}"></i>
                                </div>
                                <div class="dimension-content">
                                    <div class="dimension-title">{{ dimension.name }}</div>
                                    <div class="dimension-description mb-2">{{ dimension.description }}</div>
                                    <button class="btn btn-sm btn-outline-primary view-template-btn" data-dimension="{{ dimension.key }}" data-type="book">
                                        <i class="fas fa-eye me-1"></i>查看模板
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 章节设定模板标签页 -->
            <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h3 class="card-title mb-0"><i class="fas fa-list me-2"></i>章节设定模板</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            章节设定模板包含了每个章节15个维度的分析提示词，用于指导AI进行章节分析。
                        </div>

                        <div class="chapter-list mb-4">
                            {% for chapter in chapters %}
                            <div class="chapter-item">
                                <div class="chapter-number">{{ chapter.chapter_number }}</div>
                                <div class="chapter-title">{{ chapter.title }}</div>
                                <button class="btn btn-sm btn-outline-primary view-chapter-templates-btn" data-chapter-id="{{ chapter.id }}">
                                    <i class="fas fa-eye me-1"></i>查看模板
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模板查看模态框 -->
<div class="modal fade" id="templateModal" tabindex="-1" aria-labelledby="templateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="templateModalLabel">模板详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h4 id="templateTitle"></h4>
                <div class="template-content" id="templateContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="copyTemplateBtn">
                    <i class="fas fa-copy me-1"></i>复制
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 章节模板查看模态框 -->
<div class="modal fade" id="chapterTemplatesModal" tabindex="-1" aria-labelledby="chapterTemplatesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chapterTemplatesModalLabel">章节模板详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h4 id="chapterTitle"></h4>
                <ul class="nav nav-tabs" id="chapterTemplateTabs" role="tablist">
                    {% for dimension in dimensions %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link {% if loop.first %}active{% endif %}" id="chapter-dimension-{{ dimension.key }}-tab" data-bs-toggle="tab" data-bs-target="#chapter-dimension-{{ dimension.key }}" type="button" role="tab" aria-controls="chapter-dimension-{{ dimension.key }}" aria-selected="{{ 'true' if loop.first else 'false' }}">
                            {{ dimension.name }}
                        </button>
                    </li>
                    {% endfor %}
                </ul>
                <div class="tab-content mt-3" id="chapterTemplateTabContent">
                    {% for dimension in dimensions %}
                    <div class="tab-pane fade {% if loop.first %}show active{% endif %}" id="chapter-dimension-{{ dimension.key }}" role="tabpanel" aria-labelledby="chapter-dimension-{{ dimension.key }}-tab">
                        <div class="template-content" id="chapter-template-{{ dimension.key }}"></div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="copyChapterTemplateBtn">
                    <i class="fas fa-copy me-1"></i>复制当前模板
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 查看整本书模板
        $('.view-template-btn').click(function() {
            const dimension = $(this).data('dimension');
            const type = $(this).data('type');

            // 显示加载中状态
            $('#templateTitle').text('加载中...');
            $('#templateContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在加载模板内容...</p></div>');

            // 显示模态框
            const templateModal = new bootstrap.Modal(document.getElementById('templateModal'));
            templateModal.show();

            // 发送AJAX请求获取模板内容
            $.ajax({
                url: `/v3.1/api/novel/{{ novel.id }}/template/${type}/${dimension}`,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        // 获取维度名称
                        const dimensionName = $(`[data-dimension="${dimension}"]`).closest('.dimension-item').find('.dimension-title').text();

                        // 更新模态框内容
                        $('#templateTitle').text(`${dimensionName} 模板`);
                        $('#templateContent').text(response.template_content || '暂无模板内容');
                    } else {
                        $('#templateContent').html(`<div class="alert alert-danger">${response.message || '获取模板内容失败'}</div>`);
                    }
                },
                error: function(xhr, status, error) {
                    $('#templateContent').html(`<div class="alert alert-danger">获取模板内容失败: ${error}</div>`);
                }
            });
        });

        // 查看章节模板
        $('.view-chapter-templates-btn').click(function() {
            const chapterId = $(this).data('chapter-id');

            // 获取章节标题
            const chapterTitle = $(this).closest('.chapter-item').find('.chapter-title').text();
            $('#chapterTitle').text(chapterTitle);

            // 清空所有模板内容并显示加载中状态
            $('.template-content').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在加载模板内容...</p></div>');

            // 显示模态框
            const chapterTemplatesModal = new bootstrap.Modal(document.getElementById('chapterTemplatesModal'));
            chapterTemplatesModal.show();

            // 发送AJAX请求获取章节所有维度的模板内容
            $.ajax({
                url: `/v3.1/api/novel/{{ novel.id }}/chapter/${chapterId}/templates`,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        // 更新各维度模板内容
                        for (const dimension in response.templates) {
                            $(`#chapter-template-${dimension}`).text(response.templates[dimension] || '暂无模板内容');
                        }
                    } else {
                        $('.template-content').html(`<div class="alert alert-danger">${response.message || '获取模板内容失败'}</div>`);
                    }
                },
                error: function(xhr, status, error) {
                    $('.template-content').html(`<div class="alert alert-danger">获取模板内容失败: ${error}</div>`);
                }
            });
        });

        // 复制模板内容
        $('#copyTemplateBtn').click(function() {
            const content = $('#templateContent').text();
            copyToClipboard(content);

            // 显示复制成功提示
            const originalText = $(this).html();
            $(this).html('<i class="fas fa-check me-1"></i>已复制');

            // 2秒后恢复原始文本
            setTimeout(() => {
                $(this).html(originalText);
            }, 2000);
        });

        // 复制章节模板内容
        $('#copyChapterTemplateBtn').click(function() {
            // 获取当前激活的标签页ID
            const activeTabId = $('#chapterTemplateTabContent .tab-pane.active').attr('id');
            const content = $(`#${activeTabId} .template-content`).text();
            copyToClipboard(content);

            // 显示复制成功提示
            const originalText = $(this).html();
            $(this).html('<i class="fas fa-check me-1"></i>已复制');

            // 2秒后恢复原始文本
            setTimeout(() => {
                $(this).html(originalText);
            }, 2000);
        });

        // 复制到剪贴板函数
        function copyToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }
    });
</script>
{% endblock %}