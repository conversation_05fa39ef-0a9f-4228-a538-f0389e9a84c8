{"author": "GitHub Inc.", "name": "ini", "description": "An ini encoder/decoder for node", "version": "3.0.0", "repository": {"type": "git", "url": "https://github.com/npm/ini.git"}, "main": "lib/ini.js", "scripts": {"eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "test": "tap", "snap": "tap", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "tap": "^16.0.1"}, "license": "ISC", "files": ["bin/", "lib/"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}}