"""
启动九猫分析系统，确保使用真实API调用而不是模拟数据。
"""
import os
import sys
import logging
import subprocess
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def disable_debug_mode():
    """禁用所有DEBUG模式的模拟分析，确保使用真实API调用"""
    logger.info("正在禁用所有DEBUG模式...")

    # 修改config.py
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            config_content = f.read()

        # 确保DEBUG设置为False
        if 'DEBUG = True' in config_content:
            config_content = config_content.replace('DEBUG = True', 'DEBUG = False')
            logger.info("已将config.py中的DEBUG设置为False")

        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        logger.info("已更新config.py，确保DEBUG=False")
    except Exception as e:
        logger.error(f"修改config.py时出错: {str(e)}")

    # 修改src/api/analysis.py中的DEBUG检查
    try:
        analysis_path = os.path.join('src', 'api', 'analysis.py')
        with open(analysis_path, 'r', encoding='utf-8') as f:
            analysis_content = f.read()

        # 替换所有if config.DEBUG:为if False:
        if 'if config.DEBUG:' in analysis_content:
            analysis_content = analysis_content.replace('if config.DEBUG:', 'if False:  # 强制禁用DEBUG模式')
            logger.info("已将src/api/analysis.py中的DEBUG检查替换为False")

        with open(analysis_path, 'w', encoding='utf-8') as f:
            f.write(analysis_content)
        logger.info("已更新src/api/analysis.py，禁用所有DEBUG模式检查")
    except Exception as e:
        logger.error(f"修改src/api/analysis.py时出错: {str(e)}")

    # 修改src/api/deepseek_client.py中的DEBUG检查
    try:
        deepseek_path = os.path.join('src', 'api', 'deepseek_client.py')
        with open(deepseek_path, 'r', encoding='utf-8') as f:
            deepseek_content = f.read()

        # 确保已经禁用了DEBUG模式
        if 'if config.DEBUG:' in deepseek_content:
            deepseek_content = deepseek_content.replace('if config.DEBUG:', 'if False:  # 强制禁用DEBUG模式')
            logger.info("已将src/api/deepseek_client.py中的DEBUG检查替换为False")

        with open(deepseek_path, 'w', encoding='utf-8') as f:
            f.write(deepseek_content)
        logger.info("已更新src/api/deepseek_client.py，禁用所有DEBUG模式检查")
    except Exception as e:
        logger.error(f"修改src/api/deepseek_client.py时出错: {str(e)}")

    logger.info("所有DEBUG模式的模拟分析已禁用，系统将始终使用真实API调用")

def main():
    """主函数"""
    logger.info("开始启动九猫分析系统")

    # 禁用DEBUG模式
    disable_debug_mode()

    # 检查环境
    logger.info("检查Python环境")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"当前工作目录: {os.getcwd()}")

    # 启动Flask应用
    logger.info("启动Flask应用")
    try:
        # 使用subprocess启动Flask应用，不重定向输出，直接显示在控制台
        flask_process = subprocess.Popen(
            ["python", "main.py"],
            # 不重定向标准输出和标准错误，让它们直接显示在控制台
            shell=True,
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )

        # 等待应用启动
        logger.info("等待应用启动...")
        time.sleep(5)

        # 检查进程是否还在运行
        if flask_process.poll() is None:
            logger.info("应用已成功启动")
            logger.info("请在浏览器中访问 http://localhost:5001")

            # 打印启动信息
            logger.info("九猫分析系统已启动，可以开始使用了")
            logger.info("按Ctrl+C停止应用")

            # 等待用户按Ctrl+C
            try:
                # 简单等待进程结束或用户中断
                logger.info("应用已在新窗口中启动，请在那里查看输出")
                logger.info("按Ctrl+C停止此监控进程")

                # 持续检查进程是否还在运行
                while flask_process.poll() is None:
                    time.sleep(1)

                # 如果进程已结束，打印退出码
                logger.info(f"应用已退出，退出码: {flask_process.returncode}")

            except KeyboardInterrupt:
                logger.info("收到停止信号，正在关闭应用...")
                flask_process.terminate()
                flask_process.wait()
                logger.info("应用已关闭")
        else:
            # 应用启动失败
            logger.error(f"应用启动失败，退出码: {flask_process.returncode}")
            logger.error("请查看控制台输出以获取更多信息")
    except Exception as e:
        logger.error(f"启动应用时出错: {str(e)}")

    logger.info("脚本执行完毕")

if __name__ == "__main__":
    main()
