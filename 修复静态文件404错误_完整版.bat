@echo off
echo 九猫系统 - 静态文件404错误修复工具
echo ======================================
echo.
echo 此工具将修复以下404错误:
echo 1. fontawesome.min.css
echo 2. bootstrap.min.js
echo 3. favicon.ico
echo.

REM 检查目录结构
echo 正在检查目录结构...
if not exist "src\web\static\css" (
    echo 创建 src\web\static\css 目录...
    mkdir "src\web\static\css"
)

if not exist "src\web\static\js" (
    echo 创建 src\web\static\js 目录...
    mkdir "src\web\static\js"
)

if not exist "src\web\static\js\lib" (
    echo 创建 src\web\static\js\lib 目录...
    mkdir "src\web\static\js\lib"
)

if not exist "src\web\static\css\lib" (
    echo 创建 src\web\static\css\lib 目录...
    mkdir "src\web\static\css\lib"
)

REM 检查文件是否已存在
echo 正在检查文件...

if not exist "src\web\static\css\fontawesome.min.css" (
    echo 创建 fontawesome.min.css 文件...
    echo /* > "src\web\static\css\fontawesome.min.css"
    echo  * 九猫系统 - Font Awesome 最小化CSS >> "src\web\static\css\fontawesome.min.css"
    echo  * 这是一个简化版本，只包含基本图标 >> "src\web\static\css\fontawesome.min.css"
    echo  * 完整版可从CDN加载: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css >> "src\web\static\css\fontawesome.min.css"
    echo  */ >> "src\web\static\css\fontawesome.min.css"
    echo. >> "src\web\static\css\fontawesome.min.css"
    echo @font-face { >> "src\web\static\css\fontawesome.min.css"
    echo   font-family: 'Font Awesome 5 Free'; >> "src\web\static\css\fontawesome.min.css"
    echo   font-style: normal; >> "src\web\static\css\fontawesome.min.css"
    echo   font-weight: 900; >> "src\web\static\css\fontawesome.min.css"
    echo   font-display: block; >> "src\web\static\css\fontawesome.min.css"
    echo   src: url^("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/webfonts/fa-solid-900.woff2"^) format^("woff2"^); >> "src\web\static\css\fontawesome.min.css"
    echo } >> "src\web\static\css\fontawesome.min.css"
    echo. >> "src\web\static\css\fontawesome.min.css"
    echo .fa, .fas { font-family: 'Font Awesome 5 Free'; font-weight: 900; } >> "src\web\static\css\fontawesome.min.css"
) else (
    echo fontawesome.min.css 已存在，跳过创建
)

if not exist "src\web\static\js\bootstrap.min.js" (
    echo 创建 bootstrap.min.js 文件...
    echo /** > "src\web\static\js\bootstrap.min.js"
    echo  * 九猫系统 - Bootstrap.min.js 重定向文件 >> "src\web\static\js\bootstrap.min.js"
    echo  * >> "src\web\static\js\bootstrap.min.js"
    echo  * 此文件用于解决bootstrap.min.js 404错误 >> "src\web\static\js\bootstrap.min.js"
    echo  * 实际上重定向到bootstrap.bundle.min.js，因为系统使用的是bundle版本 >> "src\web\static\js\bootstrap.min.js"
    echo  */ >> "src\web\static\js\bootstrap.min.js"
    echo. >> "src\web\static\js\bootstrap.min.js"
    echo // 检查是否已加载bootstrap.bundle.min.js >> "src\web\static\js\bootstrap.min.js"
    echo if ^(typeof bootstrap === 'undefined'^) { >> "src\web\static\js\bootstrap.min.js"
    echo     console.log^('bootstrap.min.js: 检测到bootstrap未加载，尝试加载bootstrap.bundle.min.js'^); >> "src\web\static\js\bootstrap.min.js"
    echo     >> "src\web\static\js\bootstrap.min.js"
    echo     // 创建脚本元素 >> "src\web\static\js\bootstrap.min.js"
    echo     var script = document.createElement^('script'^); >> "src\web\static\js\bootstrap.min.js"
    echo     >> "src\web\static\js\bootstrap.min.js"
    echo     // 尝试从本地加载 >> "src\web\static\js\bootstrap.min.js"
    echo     script.src = '/static/js/lib/bootstrap.bundle.min.js'; >> "src\web\static\js\bootstrap.min.js"
    echo     >> "src\web\static\js\bootstrap.min.js"
    echo     // 错误处理 - 尝试从备用位置加载 >> "src\web\static\js\bootstrap.min.js"
    echo     script.onerror = function^(^) { >> "src\web\static\js\bootstrap.min.js"
    echo         console.warn^('无法从本地加载bootstrap.bundle.min.js，尝试从CDN加载'^); >> "src\web\static\js\bootstrap.min.js"
    echo         >> "src\web\static\js\bootstrap.min.js"
    echo         var cdnScript = document.createElement^('script'^); >> "src\web\static\js\bootstrap.min.js"
    echo         cdnScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'; >> "src\web\static\js\bootstrap.min.js"
    echo         >> "src\web\static\js\bootstrap.min.js"
    echo         cdnScript.onerror = function^(^) { >> "src\web\static\js\bootstrap.min.js"
    echo             console.error^('无法从CDN加载bootstrap.bundle.min.js'^); >> "src\web\static\js\bootstrap.min.js"
    echo         }; >> "src\web\static\js\bootstrap.min.js"
    echo         >> "src\web\static\js\bootstrap.min.js"
    echo         document.head.appendChild^(cdnScript^); >> "src\web\static\js\bootstrap.min.js"
    echo     }; >> "src\web\static\js\bootstrap.min.js"
    echo     >> "src\web\static\js\bootstrap.min.js"
    echo     // 添加到文档 >> "src\web\static\js\bootstrap.min.js"
    echo     document.head.appendChild^(script^); >> "src\web\static\js\bootstrap.min.js"
    echo } else { >> "src\web\static\js\bootstrap.min.js"
    echo     console.log^('bootstrap.min.js: bootstrap已加载，无需重复加载'^); >> "src\web\static\js\bootstrap.min.js"
    echo } >> "src\web\static\js\bootstrap.min.js"
) else (
    echo bootstrap.min.js 已存在，跳过创建
)

if not exist "src\web\static\js\lib\bootstrap.min.js" (
    echo 创建 lib目录下的bootstrap.min.js 文件...
    echo /** > "src\web\static\js\lib\bootstrap.min.js"
    echo  * 九猫系统 - Bootstrap.min.js 重定向文件 ^(lib目录版本^) >> "src\web\static\js\lib\bootstrap.min.js"
    echo  * >> "src\web\static\js\lib\bootstrap.min.js"
    echo  * 此文件用于解决bootstrap.min.js 404错误 >> "src\web\static\js\lib\bootstrap.min.js"
    echo  * 实际上重定向到bootstrap.bundle.min.js，因为系统使用的是bundle版本 >> "src\web\static\js\lib\bootstrap.min.js"
    echo  */ >> "src\web\static\js\lib\bootstrap.min.js"
    echo. >> "src\web\static\js\lib\bootstrap.min.js"
    echo // 检查是否已加载bootstrap.bundle.min.js >> "src\web\static\js\lib\bootstrap.min.js"
    echo if ^(typeof bootstrap === 'undefined'^) { >> "src\web\static\js\lib\bootstrap.min.js"
    echo     console.log^('bootstrap.min.js ^(lib^): 检测到bootstrap未加载，尝试加载bootstrap.bundle.min.js'^); >> "src\web\static\js\lib\bootstrap.min.js"
    echo     >> "src\web\static\js\lib\bootstrap.min.js"
    echo     // 创建脚本元素 >> "src\web\static\js\lib\bootstrap.min.js"
    echo     var script = document.createElement^('script'^); >> "src\web\static\js\lib\bootstrap.min.js"
    echo     >> "src\web\static\js\lib\bootstrap.min.js"
    echo     // 尝试从本地加载 >> "src\web\static\js\lib\bootstrap.min.js"
    echo     script.src = '/static/js/lib/bootstrap.bundle.min.js'; >> "src\web\static\js\lib\bootstrap.min.js"
    echo     >> "src\web\static\js\lib\bootstrap.min.js"
    echo     // 错误处理 - 尝试从备用位置加载 >> "src\web\static\js\lib\bootstrap.min.js"
    echo     script.onerror = function^(^) { >> "src\web\static\js\lib\bootstrap.min.js"
    echo         console.warn^('无法从本地加载bootstrap.bundle.min.js，尝试从CDN加载'^); >> "src\web\static\js\lib\bootstrap.min.js"
    echo         >> "src\web\static\js\lib\bootstrap.min.js"
    echo         var cdnScript = document.createElement^('script'^); >> "src\web\static\js\lib\bootstrap.min.js"
    echo         cdnScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'; >> "src\web\static\js\lib\bootstrap.min.js"
    echo         >> "src\web\static\js\lib\bootstrap.min.js"
    echo         cdnScript.onerror = function^(^) { >> "src\web\static\js\lib\bootstrap.min.js"
    echo             console.error^('无法从CDN加载bootstrap.bundle.min.js'^); >> "src\web\static\js\lib\bootstrap.min.js"
    echo         }; >> "src\web\static\js\lib\bootstrap.min.js"
    echo         >> "src\web\static\js\lib\bootstrap.min.js"
    echo         document.head.appendChild^(cdnScript^); >> "src\web\static\js\lib\bootstrap.min.js"
    echo     }; >> "src\web\static\js\lib\bootstrap.min.js"
    echo     >> "src\web\static\js\lib\bootstrap.min.js"
    echo     // 添加到文档 >> "src\web\static\js\lib\bootstrap.min.js"
    echo     document.head.appendChild^(script^); >> "src\web\static\js\lib\bootstrap.min.js"
    echo } else { >> "src\web\static\js\lib\bootstrap.min.js"
    echo     console.log^('bootstrap.min.js ^(lib^): bootstrap已加载，无需重复加载'^); >> "src\web\static\js\lib\bootstrap.min.js"
    echo } >> "src\web\static\js\lib\bootstrap.min.js"
) else (
    echo lib目录下的bootstrap.min.js 已存在，跳过创建
)

if not exist "src\web\templates\favicon-fix.html" (
    echo 创建 favicon-fix.html 文件...
    echo ^<!-- 九猫系统 - Favicon修复 --^> > "src\web\templates\favicon-fix.html"
    echo ^<link rel="icon" href="data:image/svg+xml,^<svg xmlns=%%22http://www.w3.org/2000/svg%%22 viewBox=%%220 0 100 100%%22^>^<text y=%%22.9em%%22 font-size=%%2290%%22^>📚^</text^>^</svg^>" type="image/svg+xml"^> >> "src\web\templates\favicon-fix.html"
    echo ^<link rel="shortcut icon" href="data:image/svg+xml,^<svg xmlns=%%22http://www.w3.org/2000/svg%%22 viewBox=%%220 0 100 100%%22^>^<text y=%%22.9em%%22 font-size=%%2290%%22^>📚^</text^>^</svg^>" type="image/svg+xml"^> >> "src\web\templates\favicon-fix.html"
    echo. >> "src\web\templates\favicon-fix.html"
    echo ^<!-- 备用favicon链接 --^> >> "src\web\templates\favicon-fix.html"
    echo ^<script^> >> "src\web\templates\favicon-fix.html"
    echo (function() { >> "src\web\templates\favicon-fix.html"
    echo     // 检查是否已经有favicon >> "src\web\templates\favicon-fix.html"
    echo     var existingFavicon = document.querySelector('link[rel="icon"]'); >> "src\web\templates\favicon-fix.html"
    echo     var existingShortcut = document.querySelector('link[rel="shortcut icon"]'); >> "src\web\templates\favicon-fix.html"
    echo     >> "src\web\templates\favicon-fix.html"
    echo     // 如果内联SVG favicon加载失败，尝试使用CDN上的图标 >> "src\web\templates\favicon-fix.html"
    echo     if (existingFavicon) { >> "src\web\templates\favicon-fix.html"
    echo         existingFavicon.addEventListener('error', function() { >> "src\web\templates\favicon-fix.html"
    echo             console.log('内联SVG favicon加载失败，尝试使用CDN图标'); >> "src\web\templates\favicon-fix.html"
    echo             this.href = 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/icons/book.svg'; >> "src\web\templates\favicon-fix.html"
    echo         }); >> "src\web\templates\favicon-fix.html"
    echo     } >> "src\web\templates\favicon-fix.html"
    echo     >> "src\web\templates\favicon-fix.html"
    echo     if (existingShortcut) { >> "src\web\templates\favicon-fix.html"
    echo         existingShortcut.addEventListener('error', function() { >> "src\web\templates\favicon-fix.html"
    echo             console.log('内联SVG shortcut icon加载失败，尝试使用CDN图标'); >> "src\web\templates\favicon-fix.html"
    echo             this.href = 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/icons/book.svg'; >> "src\web\templates\favicon-fix.html"
    echo         }); >> "src\web\templates\favicon-fix.html"
    echo     } >> "src\web\templates\favicon-fix.html"
    echo })(); >> "src\web\templates\favicon-fix.html"
    echo ^</script^> >> "src\web\templates\favicon-fix.html"
) else (
    echo favicon-fix.html 已存在，跳过创建
)

REM 检查base.html是否已包含favicon-fix.html引用
echo 检查base.html是否已包含favicon-fix.html引用...
findstr /C:"favicon-fix.html" "src\web\templates\base.html" >nul
if %errorlevel% neq 0 (
    echo 修改base.html，添加favicon-fix.html引用...

    REM 创建临时文件
    type "src\web\templates\base.html" > base.html.tmp

    REM 替换内容
    powershell -Command "(Get-Content base.html.tmp) -replace '({% include ''button-fix-inline.html'' %})\r?\n\r?\n    (<!-- 紧急修复脚本)', '$1\r\n\r\n    <!-- Favicon修复，解决favicon.ico 404错误 -->\r\n    {% include ''favicon-fix.html'' %}\r\n\r\n    $2'" > base.html.new

    REM 备份原文件
    copy "src\web\templates\base.html" "src\web\templates\base.html.bak" >nul

    REM 替换原文件
    copy base.html.new "src\web\templates\base.html" >nul

    REM 删除临时文件
    del base.html.tmp >nul
    del base.html.new >nul

    echo base.html 已修改
) else (
    echo base.html 已包含favicon-fix.html引用，跳过修改
)

REM 检查base.html是否已包含fontawesome.min.css引用
echo 检查base.html是否已包含fontawesome.min.css引用...
findstr /C:"fontawesome.min.css" "src\web\templates\base.html" >nul
if %errorlevel% neq 0 (
    echo 修改base.html，添加fontawesome.min.css引用...

    REM 创建临时文件
    type "src\web\templates\base.html" > base.html.tmp

    REM 替换内容
    powershell -Command "(Get-Content base.html.tmp) -replace '(    <link rel=\"stylesheet\" href=\"/static/css/main.css\">)\r?\n', '$1\r\n    <!-- Font Awesome 图标库 -->\r\n    <link rel=\"stylesheet\" href=\"/static/css/fontawesome.min.css\" onerror=\"this.onerror=null;this.href=''https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css'';\">\r\n'" > base.html.new

    REM 备份原文件
    copy "src\web\templates\base.html" "src\web\templates\base.html.bak2" >nul

    REM 替换原文件
    copy base.html.new "src\web\templates\base.html" >nul

    REM 删除临时文件
    del base.html.tmp >nul
    del base.html.new >nul

    echo base.html 已修改，添加了fontawesome.min.css引用
) else (
    echo base.html 已包含fontawesome.min.css引用，跳过修改
)

echo.
echo 修复完成！
echo.
echo 已修复以下404错误:
echo 1. fontawesome.min.css - 创建了本地文件并添加了引用
echo 2. bootstrap.min.js - 创建了重定向文件（同时在js和js/lib目录下）
echo 3. favicon.ico - 添加了内联SVG图标作为favicon
echo.
echo 请重新启动九猫系统以应用修复。
echo.
pause
