"""
九猫小说分析写作系统 - API兼容性路由
用于处理API路径兼容性问题，确保旧版API路径能够正确工作
"""

import logging
from flask import Blueprint, jsonify, request, redirect, url_for, current_app
from ..utils.db_utils import get_db_connection
from sqlalchemy.orm import Session
from ..models.models import Novel, Chapter, AnalysisResult, ChapterAnalysisResult

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
api_compat_bp = Blueprint('api_compat', __name__)

# 兼容旧前端：将 /api/template/<int:template_id>/analysis/<dimension> 重定向到 /api/novel/<int:novel_id>/analysis/<dimension>
@api_compat_bp.route('/api/template/<int:template_id>/analysis/<dimension>', methods=['GET'])
def template_analysis_compat(template_id, dimension):
    """将旧的模板分析API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/analysis/{dimension} -> /api/novel/{template_id}/analysis/{dimension}")
    return redirect(f'/api/novel/{template_id}/analysis/{dimension}')

# 兼容旧前端：将 /api/template/<int:template_id>/analysis/<dimension>/reasoning_content 重定向到 /api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content
@api_compat_bp.route('/api/template/<int:template_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def template_analysis_reasoning_compat(template_id, dimension):
    """将旧的模板分析推理过程API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/analysis/{dimension}/reasoning_content -> /api/novel/{template_id}/analysis/{dimension}/reasoning_content")
    return redirect(f'/api/novel/{template_id}/analysis/{dimension}/reasoning_content')

# 兼容旧前端：将 /api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension> 重定向到 /api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>
@api_compat_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
def template_chapter_analysis_compat(template_id, chapter_id, dimension):
    """将旧的模板章节分析API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/chapter/{chapter_id}/analysis/{dimension} -> /api/novel/{template_id}/chapter/{chapter_id}/analysis/{dimension}")
    return redirect(f'/api/novel/{template_id}/chapter/{chapter_id}/analysis/{dimension}')

# 兼容旧前端：将 /api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content 重定向到 /api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content
@api_compat_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def template_chapter_analysis_reasoning_compat(template_id, chapter_id, dimension):
    """将旧的模板章节分析推理过程API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content -> /api/novel/{template_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content")
    return redirect(f'/api/novel/{template_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content')

# 兼容旧前端：将 /api/template/<int:template_id>/reanalyze/<dimension> 重定向到 /api/novel/<int:novel_id>/reanalyze/<dimension>
@api_compat_bp.route('/api/template/<int:template_id>/reanalyze/<dimension>', methods=['POST'])
def template_reanalyze_compat(template_id, dimension):
    """将旧的模板重新分析API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/reanalyze/{dimension} -> /api/novel/{template_id}/reanalyze/{dimension}")
    return redirect(f'/api/novel/{template_id}/reanalyze/{dimension}', code=307)  # 使用307保留POST方法

# 兼容旧前端：将 /api/template/<int:template_id>/chapter/<int:chapter_id>/reanalyze/<dimension> 重定向到 /api/novel/<int:novel_id>/chapter/<int:chapter_id>/reanalyze/<dimension>
@api_compat_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/reanalyze/<dimension>', methods=['POST'])
def template_chapter_reanalyze_compat(template_id, chapter_id, dimension):
    """将旧的模板章节重新分析API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/chapter/{chapter_id}/reanalyze/{dimension} -> /api/novel/{template_id}/chapter/{chapter_id}/reanalyze/{dimension}")
    return redirect(f'/api/novel/{template_id}/chapter/{chapter_id}/reanalyze/{dimension}', code=307)  # 使用307保留POST方法

# 兼容旧前端：将 /api/template/<int:template_id>/chapters 重定向到 /api/novel/<int:novel_id>/chapters
@api_compat_bp.route('/api/template/<int:template_id>/chapters', methods=['GET'])
def template_chapters_compat(template_id):
    """将旧的模板章节列表API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/chapters -> /api/novel/{template_id}/chapters")
    return redirect(f'/api/novel/{template_id}/chapters')

# 兼容旧前端：将 /api/template/<int:template_id> 重定向到 /api/novel/<int:novel_id>
@api_compat_bp.route('/api/template/<int:template_id>', methods=['GET'])
def template_detail_compat(template_id):
    """将旧的模板详情API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id} -> /api/novel/{template_id}")
    return redirect(f'/api/novel/{template_id}')

# 兼容旧前端：将 /api/templates 重定向到 /api/novels
@api_compat_bp.route('/api/templates', methods=['GET'])
def templates_list_compat():
    """将旧的模板列表API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/templates -> /api/novels")
    return redirect(f'/api/novels')

# 兼容旧前端：将 /api/template/<int:template_id>/analysis 重定向到 /api/novel/<int:novel_id>/analysis
@api_compat_bp.route('/api/template/<int:template_id>/analysis', methods=['GET'])
def template_all_analysis_compat(template_id):
    """将旧的模板所有分析结果API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/analysis -> /api/novel/{template_id}/analysis")
    return redirect(f'/api/novel/{template_id}/analysis')

# 兼容旧前端：将 /api/template/<int:template_id>/chapter/<int:chapter_id>/analysis 重定向到 /api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis
@api_compat_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis', methods=['GET'])
def template_chapter_all_analysis_compat(template_id, chapter_id):
    """将旧的模板章节所有分析结果API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/chapter/{chapter_id}/analysis -> /api/novel/{template_id}/chapter/{chapter_id}/analysis")
    return redirect(f'/api/novel/{template_id}/chapter/{chapter_id}/analysis')

# 兼容旧前端：将 /api/template/<int:template_id>/chapter/<int:chapter_id> 重定向到 /api/novel/<int:novel_id>/chapter/<int:chapter_id>
@api_compat_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>', methods=['GET'])
def template_chapter_detail_compat(template_id, chapter_id):
    """将旧的模板章节详情API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/chapter/{chapter_id} -> /api/novel/{template_id}/chapter/{chapter_id}")
    return redirect(f'/api/novel/{template_id}/chapter/{chapter_id}')

# 兼容旧前端：将 /api/template/<int:template_id>/book_template/<dimension> 重定向到 /api/novel/<int:novel_id>/book_template/<dimension>
@api_compat_bp.route('/api/template/<int:template_id>/book_template/<dimension>', methods=['GET'])
def template_book_template_compat(template_id, dimension):
    """将旧的模板整本书预设模板API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/book_template/{dimension} -> /api/novel/{template_id}/book_template/{dimension}")
    return redirect(f'/api/novel/{template_id}/book_template/{dimension}')

# 兼容旧前端：将 /api/template/<int:template_id>/chapter/<int:chapter_id>/template/<dimension> 重定向到 /api/novel/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>
@api_compat_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/template/<dimension>', methods=['GET'])
def template_chapter_template_compat(template_id, chapter_id, dimension):
    """将旧的模板章节预设模板API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/chapter/{chapter_id}/template/{dimension} -> /api/novel/{template_id}/chapter/{chapter_id}/template/{dimension}")
    return redirect(f'/api/novel/{template_id}/chapter/{chapter_id}/template/{dimension}')

# 兼容旧前端：将 /api/template/<int:template_id>/analysis/<dimension>/status 重定向到 /api/novel/<int:novel_id>/analysis/<dimension>/status
@api_compat_bp.route('/api/template/<int:template_id>/analysis/<dimension>/status', methods=['GET'])
def template_analysis_status_compat(template_id, dimension):
    """将旧的模板分析状态API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/analysis/{dimension}/status -> /api/novel/{template_id}/analysis/{dimension}/status")
    return redirect(f'/api/novel/{template_id}/analysis/{dimension}/status')

# 兼容旧前端：将 /api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/status 重定向到 /api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/status
@api_compat_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/status', methods=['GET'])
def template_chapter_analysis_status_compat(template_id, chapter_id, dimension):
    """将旧的模板章节分析状态API路径重定向到新的API路径"""
    logger.info(f"重定向旧API路径: /api/template/{template_id}/chapter/{chapter_id}/analysis/{dimension}/status -> /api/novel/{template_id}/chapter/{chapter_id}/analysis/{dimension}/status")
    return redirect(f'/api/novel/{template_id}/chapter/{chapter_id}/analysis/{dimension}/status')
