/**
 * 九猫系统 - 推理过程紧急修复脚本
 * 版本: 1.0.0
 * 
 * 该脚本用于紧急修复推理过程显示错误的问题，直接在前端进行内容交换
 */

(function() {
    console.log('[九猫紧急修复] 推理过程紧急修复脚本已启动');

    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {}, settings: {} };

    // 如果已经运行过，不再重复执行
    if (window.__nineCatsFixes.loaded['reasoning-content-emergency-fix']) {
        console.log('[九猫紧急修复] 推理过程紧急修复脚本已经运行过，跳过');
        return;
    }

    // 配置
    const CONFIG = {
        // 是否启用调试日志
        enableDebug: true,
        // 是否启用自动修复
        enableAutoFix: true,
        // 自动修复延迟（毫秒）
        autoFixDelay: 500,
        // 是否启用强制修复
        enableForceFix: true,
        // 是否启用内容交换
        enableContentSwap: true
    };

    // 推理过程特征标记
    const REASONING_MARKERS = [
        '好的，我现在要',
        '首先，我需要',
        '我将分析',
        '我需要分析',
        '嗯，用户让我',
        '下面我来分析',
        '让我来分析',
        '我会按照以下步骤',
        '我将按照以下步骤',
        '我将从以下几个方面',
        '我需要从以下几个方面',
        '我将逐步分析',
        '好的，我现在需要',
        '我需要通读一遍',
        '我需要先了解',
        '我会先阅读',
        '我先阅读一遍',
        '我需要分析用户提供的',
        '开始分析这段小说',
        '我需要全面考虑',
        '我得仔细阅读',
        '作为文学分析专家',
        '我应该观察',
        '我需要注意',
        '我将关注',
        '我要分析',
        '我要考虑',
        '我要思考',
        '我要探讨',
        '我要研究',
        '我要理解',
        '我要解析',
        '我要评估',
        '我要判断',
        '我要确定',
        '我要找出',
        '我要识别',
        '我要辨别',
        '我要区分',
        '我要比较',
        '我要对比',
        '我要总结',
        '我要归纳',
        '我要提炼',
        '我要整理',
        '我要组织',
        '我要构建',
        '我要形成',
        '我要得出',
        '我要推导',
        '我要推理',
        '我要推断',
        '我要推测',
        '我要猜测',
        '我要假设',
        '我要设想',
        '我要想象',
        '我要预测'
    ];

    // 分析结果特征标记
    const RESULT_MARKERS = [
        '### **一、',
        '### 一、',
        '## 一、',
        '# 一、',
        '---\n### **',
        '## **',
        '# **',
        '### **总结',
        '## **总结',
        '# **总结',
        '句式变化分析',
        '重点探讨',
        '以下是对该小说文本的',
        '这部小说文本展现出了',
        '风格特征：',
        '主题架构：',
        '写作技法：',
        '人物塑造：',
        '文化符码：',
        '结构创新：',
        '节奏控制分析',
        '### 二、',
        '### 三、',
        '### 四、',
        '### 五、',
        '世界构建分析',
        '角色塑造分析',
        '情节发展分析',
        '语言风格分析',
        '主题探索分析',
        '叙事视角分析',
        '情感表达分析',
        '冲突设置分析',
        '节奏控制分析',
        '象征意象分析',
        '文化背景分析',
        '结构布局分析',
        '对话艺术分析',
        '开篇效果分析',
        '---语言风格核心特征---',
        '---第一章文本分析---',
        '---修辞特色---',
        '---语言创新点---',
        '---叙述者立场---'
    ];

    // 检查内容是否是推理过程
    function isReasoningProcess(content) {
        if (!content) return false;
        
        for (const marker of REASONING_MARKERS) {
            if (content.includes(marker)) {
                return true;
            }
        }
        
        return false;
    }

    // 检查内容是否是分析结果
    function isAnalysisResult(content) {
        if (!content) return false;
        
        for (const marker of RESULT_MARKERS) {
            if (content.includes(marker)) {
                return true;
            }
        }
        
        return false;
    }

    // 紧急修复推理过程显示
    function emergencyFixReasoningContent() {
        console.info('[九猫紧急修复] 开始紧急修复推理过程显示');
        
        // 查找页面上的所有内容容器
        const reasoningContainers = document.querySelectorAll('[data-reasoning-container], #reasoningContent, .reasoning-content');
        const analysisContainers = document.querySelectorAll('#analysisContent, .analysis-content, .markdown-content');
        
        if (reasoningContainers.length === 0) {
            console.info('[九猫紧急修复] 未找到推理过程容器');
            return;
        }
        
        console.info(`[九猫紧急修复] 找到 ${reasoningContainers.length} 个推理过程容器和 ${analysisContainers.length} 个分析结果容器`);
        
        // 检查每个推理过程容器
        reasoningContainers.forEach(reasoningContainer => {
            // 获取推理过程内容
            const reasoningPreElements = reasoningContainer.querySelectorAll('pre');
            if (reasoningPreElements.length === 0) return;
            
            const reasoningContent = reasoningPreElements[0].textContent;
            if (!reasoningContent) return;
            
            // 检查是否是分析结果
            if (isAnalysisResult(reasoningContent) && !isReasoningProcess(reasoningContent)) {
                console.warn('[九猫紧急修复] 推理过程容器中显示的是分析结果，尝试修复');
                
                // 添加警告提示
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-danger mb-3';
                warningDiv.innerHTML = `
                    <p><i class="fas fa-exclamation-triangle"></i> <strong>错误：</strong> 系统错误地将分析结果显示为推理过程。</p>
                    <p class="small">正在尝试修复，请稍候...</p>
                `;
                reasoningContainer.insertBefore(warningDiv, reasoningPreElements[0]);
                
                // 尝试从分析结果容器中找到推理过程
                if (CONFIG.enableContentSwap && analysisContainers.length > 0) {
                    let foundReasoning = false;
                    
                    analysisContainers.forEach(analysisContainer => {
                        if (foundReasoning) return;
                        
                        const analysisContent = analysisContainer.innerHTML;
                        if (!analysisContent) return;
                        
                        // 检查是否包含推理过程特征
                        if (isReasoningProcess(analysisContent) && !isAnalysisResult(analysisContent)) {
                            console.info('[九猫紧急修复] 在分析结果容器中找到推理过程，交换内容');
                            
                            // 交换内容
                            reasoningPreElements[0].textContent = analysisContent;
                            analysisContainer.innerHTML = reasoningContent;
                            
                            // 更新警告提示
                            warningDiv.className = 'alert alert-success mb-3';
                            warningDiv.innerHTML = `
                                <p><i class="fas fa-check-circle"></i> <strong>已修复：</strong> 成功交换推理过程和分析结果。</p>
                            `;
                            
                            foundReasoning = true;
                        }
                    });
                    
                    if (!foundReasoning) {
                        // 更新警告提示
                        warningDiv.innerHTML = `
                            <p><i class="fas fa-exclamation-triangle"></i> <strong>警告：</strong> 无法找到真正的推理过程。</p>
                            <p class="small">系统可能未正确保存推理过程，或者推理过程与分析结果相同。</p>
                        `;
                    }
                }
            }
        });
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(emergencyFixReasoningContent, CONFIG.autoFixDelay);
        });
    } else {
        setTimeout(emergencyFixReasoningContent, CONFIG.autoFixDelay);
    }

    // 标记为已加载
    window.__nineCatsFixes.loaded['reasoning-content-emergency-fix'] = true;

    console.log('[九猫紧急修复] 推理过程紧急修复脚本初始化完成');
})();
