"""
Chapter analysis result data model for the 九猫 (Nine Cats) novel analysis system.
"""
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship

from src.models.base import Base

# 创建一个函数来替代已弃用的datetime.utcnow
def utc_now():
    """返回当前的UTC时间，替代已弃用的datetime.utcnow"""
    return datetime.now(timezone.utc)

class ChapterAnalysisResult(Base):
    """Chapter analysis result data model."""

    __tablename__ = "chapter_analysis_results"

    id = Column(Integer, primary_key=True)
    chapter_id = Column(Integer, ForeignKey("chapters.id"), nullable=False)
    novel_id = Column(Integer, ForeignKey("novels.id"), nullable=False)
    dimension = Column(String(255), nullable=False)  # 分析维度
    content = Column(Text, nullable=False)  # 分析内容
    reasoning_content = Column(Text, nullable=True)  # 分析推理过程
    analysis_metadata = Column(JSON, nullable=True)  # 分析元数据
    analysis_logs = Column(JSON, nullable=True)  # 分析日志
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # 与Chapter的关系
    chapter = relationship("Chapter", back_populates="analysis_results")

    # 与Novel的关系
    novel = relationship("Novel")

    # 与ChapterAnalysisProcess的关系
    processes = relationship("ChapterAnalysisProcess", back_populates="result", cascade="all, delete-orphan")

    @property
    def safe_metadata(self):
        """
        安全地获取元数据，确保返回的是可序列化的字典。

        Returns:
            Dict: 可序列化的元数据字典
        """
        if not self.analysis_metadata:
            return {}

        try:
            # 尝试将metadata转换为字典
            if hasattr(self.analysis_metadata, 'items') and callable(self.analysis_metadata.items):
                # 如果有items方法，创建一个新的字典副本
                return {k: v for k, v in self.analysis_metadata.items()}
            elif isinstance(self.analysis_metadata, dict):
                # 如果是字典，直接使用
                return dict(self.analysis_metadata)
            else:
                # 如果不是字典且没有items方法，使用空字典并记录错误
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"safe_metadata属性: 元数据不是字典类型: {type(self.analysis_metadata)}")
                return {"error": f"元数据无法转换为字典: {type(self.analysis_metadata)}"}
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"safe_metadata属性: 元数据处理失败: {str(e)}")
            return {"error": f"元数据处理失败: {str(e)}"}

    def __init__(
        self,
        chapter_id: int,
        novel_id: int,
        dimension: str,
        content: str,
        reasoning_content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        logs: Optional[List[Dict[str, Any]]] = None
    ):
        """
        初始化章节分析结果。

        Args:
            chapter_id: 章节ID
            novel_id: 小说ID
            dimension: 分析维度
            content: 分析内容
            reasoning_content: 分析推理过程
            metadata: 额外的元数据
            logs: 分析日志
        """
        self.chapter_id = chapter_id
        self.novel_id = novel_id
        self.dimension = dimension
        self.content = content
        self.reasoning_content = reasoning_content
        self.analysis_metadata = metadata or {}
        self.analysis_logs = logs or []

    def to_dict(self) -> Dict[str, Any]:
        """
        将章节分析结果转换为字典。

        Returns:
            章节分析结果的字典表示
        """
        # 处理内容，确保它是字符串
        content = self.content
        if not isinstance(content, str):
            try:
                content = str(content)
            except Exception:
                content = "无法转换为字符串的内容"

        # 处理推理过程，确保它是字符串
        reasoning_content = self.reasoning_content
        if reasoning_content and not isinstance(reasoning_content, str):
            try:
                reasoning_content = str(reasoning_content)
            except Exception:
                reasoning_content = "无法转换为字符串的推理过程"

        # 处理元数据，确保它是字典
        metadata = self.analysis_metadata
        if not isinstance(metadata, dict):
            try:
                metadata = dict(metadata) if metadata else {}
            except Exception:
                metadata = {}

        # 处理日志，确保它是列表
        logs = self.analysis_logs
        if not isinstance(logs, list):
            try:
                logs = list(logs) if logs else []
            except Exception:
                logs = []

        return {
            "id": self.id,
            "chapter_id": self.chapter_id,
            "novel_id": self.novel_id,
            "dimension": self.dimension,
            "content": content,
            "reasoning_content": reasoning_content,
            "metadata": metadata,
            "logs": logs,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "has_detailed_process": len(self.processes) > 0 if hasattr(self, 'processes') else False
        }
