' 九猫小说分析系统启动脚本 (VBS版本) - 大型文本处理版
' 此脚本会在后台启动九猫系统，并自动打开浏览器，无需显示命令行窗口
' 针对大型文本（数百万字）处理进行了内存管理和并行处理优化

' 设置工作目录为脚本所在目录
On Error Resume Next
Set fso = CreateObject("Scripting.FileSystemObject")
currentPath = fso.GetParentFolderName(WScript.ScriptFullName)
Set WshShell = CreateObject("WScript.Shell")

' 检查路径是否包含特殊字符，如果包含特殊字符，使用短路径名
If InStr(currentPath, "艹") > 0 Or InStr(currentPath, ",") > 0 Or InStr(currentPath, " ") > 0 Then
    currentPath = WshShell.Exec("cmd /c for %I in (""" & currentPath & """) do @echo %~sI").StdOut.ReadAll
    currentPath = Replace(currentPath, vbCrLf, "")
End If

WshShell.CurrentDirectory = currentPath

' 记录启动日志
Dim logFile
logFile = currentPath & "\logs\startup\startup_" & Replace(Replace(Replace(Now(), "/", ""), ":", ""), " ", "_") & ".log"

' 确保日志目录存在
If Not fso.FolderExists(currentPath & "\logs\startup") Then
    fso.CreateFolder(currentPath & "\logs\startup")
End If

Set logStream = fso.CreateTextFile(logFile, True)
logStream.WriteLine("启动时间: " & Now)
logStream.WriteLine("工作目录: " & currentPath)

' 检查并创建日志目录
If Not fso.FolderExists("logs") Then
    logStream.WriteLine("创建日志目录")
    fso.CreateFolder("logs")
End If

' 创建各种日志子目录
Dim logSubdirs
logSubdirs = Array("startup", "analysis", "error", "performance", "api", "ui")
For Each subdir In logSubdirs
    If Not fso.FolderExists("logs\" & subdir) Then
        logStream.WriteLine("创建日志子目录: logs\" & subdir)
        fso.CreateFolder("logs\" & subdir)
    End If
Next

' 清理旧日志文件，只保留最新的10个
Sub CleanupOldLogs()
    On Error Resume Next

    Dim logFolder, logFiles, file
    Set logFolder = fso.GetFolder(currentPath & "\logs")
    Set logFiles = logFolder.Files

    ' 创建一个数组来存储日志文件
    Dim fileArray()
    ReDim fileArray(logFiles.Count - 1)

    ' 填充数组
    Dim i, fileCount
    i = 0
    fileCount = 0

    For Each file In logFiles
        If LCase(Right(file.Name, 4)) = ".log" Then
            fileArray(i) = file.Path
            i = i + 1
            fileCount = fileCount + 1
        End If
    Next

    ' 如果文件数量超过10个，删除最旧的文件
    If fileCount > 10 Then
        ' 按修改日期排序（冒泡排序）
        Dim j, temp
        For i = 0 To fileCount - 2
            For j = 0 To fileCount - i - 2
                If fso.GetFile(fileArray(j)).DateLastModified > fso.GetFile(fileArray(j+1)).DateLastModified Then
                    temp = fileArray(j)
                    fileArray(j) = fileArray(j+1)
                    fileArray(j+1) = temp
                End If
            Next
        Next

        ' 删除最旧的文件（保留最新的10个）
        For i = 0 To fileCount - 11
            If fileArray(i) <> "" Then
                fso.DeleteFile(fileArray(i))
            End If
        Next
    End If
End Sub

' 执行日志清理
CleanupOldLogs()

' 检查psutil是否已安装
Function IsPsutilInstalled()
    Dim result
    result = WshShell.Run("cmd /c python -c ""import psutil""", 0, True)
    IsPsutilInstalled = (result = 0)
End Function

' 检查tqdm是否已安装
Function IsTqdmInstalled()
    Dim result
    result = WshShell.Run("cmd /c python -c ""import tqdm""", 0, True)
    IsTqdmInstalled = (result = 0)
End Function

' 检查memory_profiler是否已安装
Function IsMemoryProfilerInstalled()
    Dim result
    result = WshShell.Run("cmd /c python -c ""import memory_profiler""", 0, True)
    IsMemoryProfilerInstalled = (result = 0)
End Function

' 安装必要的模块
Sub InstallRequiredModules()
    Dim result
    Dim missingModules
    missingModules = ""
    
    ' 检查并记录缺失的模块
    If Not IsPsutilInstalled() Then
        missingModules = missingModules & "psutil "
    End If
    
    If Not IsTqdmInstalled() Then
        missingModules = missingModules & "tqdm "
    End If
    
    If Not IsMemoryProfilerInstalled() Then
        missingModules = missingModules & "memory-profiler "
    End If
    
    ' 如果有缺失的模块，安装它们
    If missingModules <> "" Then
        result = WshShell.Run("cmd /c pip install " & missingModules, 1, True)
        logStream.WriteLine("安装模块: " & missingModules & ", 结果: " & result)
        If result <> 0 Then
            MsgBox "无法安装必要的模块：" & missingModules & vbCrLf & _
                   "建议手动安装: pip install " & missingModules, _
                   48, "九猫小说分析系统 - 警告"
        End If
    End If
End Sub

' 检查并安装必要的模块
If MsgBox("即将检查并安装必要的Python模块，用于优化大型文本处理。" & vbCrLf & _
          "包括psutil（内存监控）、tqdm（进度显示）和memory-profiler（内存分析）。" & vbCrLf & _
          "是否继续？", _
          vbYesNo + vbQuestion, "九猫小说分析系统 - 大型文本处理版") = vbYes Then
    InstallRequiredModules()
End If

' 检查端口5001是否被占用
Function IsPortInUse()
    Dim result
    result = WshShell.Run("cmd /c netstat -ano | findstr :5001 | findstr LISTENING", 0, True)
    IsPortInUse = (result = 0)
End Function

' 终止占用端口5001的进程
Sub KillPort5001Process()
    On Error Resume Next

    ' 创建临时批处理文件来终止进程
    Set killPortFile = fso.CreateTextFile(currentPath & "\kill_port.bat", True)
    killPortFile.WriteLine("@echo off")
    killPortFile.WriteLine("for /f ""tokens=5"" %%a in ('netstat -ano ^| findstr :5001 ^| findstr LISTENING') do (")
    killPortFile.WriteLine("    taskkill /f /pid %%a >nul 2>&1")
    killPortFile.WriteLine(")")
    killPortFile.Close

    ' 运行批处理文件
    WshShell.Run "cmd /c " & currentPath & "\kill_port.bat", 0, True

    ' 删除临时文件
    fso.DeleteFile currentPath & "\kill_port.bat"
End Sub

' 检查并释放端口
If IsPortInUse() Then
    If MsgBox("端口5001已被占用，需要释放才能启动九猫系统。" & vbCrLf & _
              "是否尝试终止占用该端口的进程？", _
              vbYesNo + vbQuestion, "九猫小说分析系统") = vbYes Then
        KillPort5001Process()
    End If
End If

' 显示启动消息
MsgBox "九猫小说分析系统正在启动（大型文本处理模式）..." & vbCrLf & vbCrLf & _
       "系统将在后台运行，浏览器将在15秒钟后自动打开。" & vbCrLf & _
       "请不要关闭浏览器窗口，否则需要手动访问: http://localhost:5001" & vbCrLf & vbCrLf & _
       "大型文本处理模式已启用：" & vbCrLf & _
       "1. 增强的内存管理，防止崩溃" & vbCrLf & _
       "2. 优化的分块策略，适合处理数百万字文本" & vbCrLf & _
       "3. 智能并行处理，根据系统资源自动调整" & vbCrLf & _
       "4. 动态内存释放，减少内存占用" & vbCrLf & _
       "5. 分析过程完整记录" & vbCrLf & _
       "6. 大型缓存支持，加速重复分析" & vbCrLf & vbCrLf & _
       "注意：首次分析大型文本可能较慢，请耐心等待。", _
       64, "九猫小说分析系统 - 大型文本处理模式"

' 创建临时批处理文件来设置环境变量并运行Python
Dim batchFilePath
batchFilePath = currentPath & "\temp_run.bat"
logStream.WriteLine("创建临时批处理文件: " & batchFilePath)

Set tempFile = fso.CreateTextFile(batchFilePath, True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & currentPath & """")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("set MEMORY_OPTIMIZED=True")
tempFile.WriteLine("set LOW_MEMORY_MODE=False")
tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=90")
tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=95")
tempFile.WriteLine("set MAX_DB_CONNECTIONS=50")
tempFile.WriteLine("set DB_POOL_SIZE=30")
tempFile.WriteLine("set DB_MAX_OVERFLOW=20")
tempFile.WriteLine("set THREAD_POOL_SIZE=16")
tempFile.WriteLine("set MAX_WORKERS=16")
tempFile.WriteLine("set DISABLE_PARALLEL_ANALYSIS=False")
tempFile.WriteLine("set REDUCE_LOGGING=True")
tempFile.WriteLine("set ENABLE_DETAILED_PROCESS_RECORDING=True")
tempFile.WriteLine("set SAVE_FULL_API_INTERACTIONS=True")
tempFile.WriteLine("set SAVE_PROMPTS=True")
tempFile.WriteLine("set ENHANCED_LOG_CATEGORIES=True")
tempFile.WriteLine("set RECORD_INTERMEDIATE_RESULTS=True")
tempFile.WriteLine("set LARGE_TEXT_MODE=True")
tempFile.WriteLine("set ADAPTIVE_CHUNKING=True")
tempFile.WriteLine("set DYNAMIC_MEMORY_MANAGEMENT=True")
tempFile.WriteLine("set MAX_CHUNK_SIZE=10000")
tempFile.WriteLine("set CHUNK_OVERLAP=100")
tempFile.WriteLine("set ADAPTIVE_PARALLELISM=True")
tempFile.WriteLine("set MAX_CACHE_SIZE=1024")
tempFile.WriteLine("set LARGE_TEXT_THRESHOLD=1000000")
tempFile.WriteLine("set GC_FREQUENCY=1000")
tempFile.WriteLine("set MEMORY_RELEASE_INTERVAL=10")
tempFile.WriteLine("set USE_EXTERNAL_BUFFER=True")
tempFile.WriteLine("set BUFFER_CLEANUP_INTERVAL=5")
tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("if not exist logs\startup mkdir logs\startup")
tempFile.WriteLine("if not exist logs\analysis mkdir logs\analysis")
tempFile.WriteLine("if not exist logs\error mkdir logs\error")
tempFile.WriteLine("if not exist logs\performance mkdir logs\performance")
tempFile.WriteLine("if not exist logs\api mkdir logs\api")
tempFile.WriteLine("if not exist logs\ui mkdir logs\ui")
tempFile.WriteLine("title 九猫小说分析系统 - 大型文本处理版 [运行中]")
tempFile.WriteLine("color 0A")
tempFile.WriteLine("cls")
tempFile.WriteLine("echo.")
tempFile.WriteLine("echo   ██████╗ ██████╗  █████╗ ████████╗███████╗")
tempFile.WriteLine("echo   ██╔════╝██╔════╝ ██╔══██╗╚══██╔══╝██╔════╝")
tempFile.WriteLine("echo   ██║     ██║  ███╗███████║   ██║   ███████╗")
tempFile.WriteLine("echo   ██║     ██║   ██║██╔══██║   ██║   ╚════██║")
tempFile.WriteLine("echo   ╚██████╗╚██████╔╝██║  ██║   ██║   ███████║")
tempFile.WriteLine("echo    ╚═════╝ ╚═════╝ ╚═╝  ╚═╝   ╚═╝   ╚══════╝")
tempFile.WriteLine("echo.")
tempFile.WriteLine("echo   九猫小说分析系统 - 大型文本处理版 [v2.5.8]")
tempFile.WriteLine("echo   ========================================")
tempFile.WriteLine("echo.")
tempFile.WriteLine("echo   [信息] 正在初始化系统...")
tempFile.WriteLine("ping -n 2 127.0.0.1 > nul")
tempFile.WriteLine("echo   [信息] 正在加载分析引擎...")
tempFile.WriteLine("ping -n 2 127.0.0.1 > nul")
tempFile.WriteLine("echo   [信息] 正在启动API服务...")
tempFile.WriteLine("ping -n 2 127.0.0.1 > nul")
tempFile.WriteLine("echo   [信息] 正在配置DeepSeek R1模型...")
tempFile.WriteLine("ping -n 1 127.0.0.1 > nul")
tempFile.WriteLine("echo   [成功] 系统启动完成！服务运行在 http://localhost:5001")
tempFile.WriteLine("echo.")
tempFile.WriteLine("echo   [警告] 请不要关闭此窗口，关闭窗口将停止整个系统运行")
tempFile.WriteLine("echo   [信息] 浏览器将自动打开，如未打开请手动访问 http://localhost:5001")
tempFile.WriteLine("echo.")
tempFile.WriteLine("echo   ======== 以下为系统运行日志 ========")
tempFile.WriteLine("echo.")
tempFile.WriteLine("python run.py")
tempFile.Close

' 在前台运行批处理文件，显示窗口以查看输出和错误信息
logStream.WriteLine("运行批处理文件")
WshShell.Run "cmd /c " & batchFilePath, 1, False

' 等待15秒钟确保服务启动
WScript.Sleep 15000

' 打开浏览器 - 使用cmd命令强制使用默认浏览器
logStream.WriteLine("打开浏览器")
WshShell.Run "cmd /c start http://localhost:5001", 0, False

' 关闭日志
logStream.WriteLine("启动过程完成: " & Now)
logStream.Close 