<!-- 浏览器扩展警告组件 -->
<div id="extension-warning" class="alert alert-warning alert-dismissible fade show d-none" role="alert">
    <h5 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> 浏览器扩展冲突警告</h5>
    <p>检测到浏览器扩展可能干扰页面正常运行，导致以下错误：</p>
    <ul class="extension-errors">
        <li>未知脚本错误（位置: 41:30）</li>
        <li>消息通道关闭错误</li>
    </ul>
    <p><strong>建议解决方法：</strong></p>
    <ol>
        <li>暂时禁用广告拦截器、隐私保护或页面修改类扩展</li>
        <li>使用浏览器的无痕/隐私模式访问（不加载扩展）</li>
        <li>尝试使用其他浏览器</li>
    </ol>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>

<script>
// 浏览器扩展警告检测与显示
(function() {
    // 错误检测计数器
    let extensionErrorCount = 0;
    
    // 检查是否存在特定的扩展错误模式
    function checkForExtensionErrors(error) {
        if (!error) return false;
        
        const errorMessage = typeof error === 'string' ? error : 
                            (error.message || error.toString());
        
        // 检查常见的扩展错误模式
        return (
            errorMessage.includes('message channel closed') ||
            errorMessage.includes('asynchronous response') ||
            errorMessage.includes('extension') ||
            (errorMessage.includes('Script error') && errorMessage.includes('41:30')) ||
            errorMessage.includes('Extension context invalidated') ||
            errorMessage.includes('Message manager disconnected')
        );
    }
    
    // 捕获错误并检查
    window.addEventListener('error', function(event) {
        if (checkForExtensionErrors(event.error || event.message)) {
            extensionErrorCount++;
            showWarningIfNeeded();
        }
    });
    
    // 捕获Promise错误
    window.addEventListener('unhandledrejection', function(event) {
        if (checkForExtensionErrors(event.reason)) {
            extensionErrorCount++;
            showWarningIfNeeded();
        }
    });
    
    // 显示警告（如果错误次数达到阈值）
    function showWarningIfNeeded() {
        // 如果错误次数超过3次，显示警告
        if (extensionErrorCount >= 3) {
            const warningEl = document.getElementById('extension-warning');
            if (warningEl && warningEl.classList.contains('d-none')) {
                warningEl.classList.remove('d-none');
                console.log('显示浏览器扩展警告');
                
                // 添加特定错误信息
                const errorsList = warningEl.querySelector('.extension-errors');
                if (errorsList) {
                    // 清空现有的错误
                    errorsList.innerHTML = '';
                    
                    // 添加当前错误
                    const errorItem = document.createElement('li');
                    errorItem.textContent = '检测到浏览器扩展冲突: 消息通道关闭错误';
                    errorsList.appendChild(errorItem);
                    
                    const scriptErrorItem = document.createElement('li');
                    scriptErrorItem.textContent = '未知脚本错误 (Script error)';
                    errorsList.appendChild(scriptErrorItem);
                }
            }
        }
    }
})();
</script> 