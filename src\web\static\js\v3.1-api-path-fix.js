/**
 * 九猫小说分析写作系统v3.1 - API路径修复脚本
 *
 * 此脚本用于修复API路径，确保所有API调用都使用正确的前缀
 * 版本: 1.0.0
 */

(function() {
    console.log('[v3.1 API路径修复] 脚本加载中...');

    // 保存原始的fetch和XMLHttpRequest.open方法
    const originalFetch = window.fetch;
    const originalXhrOpen = XMLHttpRequest.prototype.open;

    // 需要修复的API路径模式和对应的正确路径
    // 注意：我们现在添加对v3.1/api路径的支持，以便在v3.1路径失败时尝试常规API路径
    const pathPatterns = [
        // 添加对v3.1/api路径的支持
        {
            pattern: /^\/v3\.1\/api\/analysis\/([^\/]+)\/status/,
            replacement: '/api/analysis/$1/status'
        },
        {
            pattern: /^\/v3\.1\/api\/novel\/([^\/]+)\/analysis\/([^\/]+)/,
            replacement: '/api/novel/$1/analysis/$2'
        },
        {
            pattern: /^\/v3\.1\/api\/novel\/([^\/]+)\/chapter\/([^\/]+)\/analysis\/([^\/]+)/,
            replacement: '/api/novel/$1/chapter/$2/analysis/$3'
        }
    ];

    // 修复API路径
    function fixApiPath(url) {
        if (typeof url !== 'string') {
            return url;
        }

        // 检查是否匹配需要修复的路径模式
        for (const pattern of pathPatterns) {
            if (pattern.pattern.test(url)) {
                const fixedUrl = url.replace(pattern.pattern, pattern.replacement);
                console.log(`[v3.1 API路径修复] 修复API路径: ${url} -> ${fixedUrl}`);
                return fixedUrl;
            }
        }

        return url;
    }

    // 重写fetch函数
    window.fetch = function(resource, options) {
        if (typeof resource === 'string') {
            resource = fixApiPath(resource);
        } else if (resource && resource.url) {
            resource.url = fixApiPath(resource.url);
        }
        return originalFetch.apply(this, arguments);
    };

    // 重写XMLHttpRequest.open方法
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        const fixedUrl = fixApiPath(url);
        return originalXhrOpen.call(this, method, fixedUrl, async, user, password);
    };

    // jQuery的AJAX请求修复将在DOMContentLoaded事件中处理
    // 这样可以确保jQuery已完全加载

    // 在页面加载完成后检查并修复内联脚本中的API路径
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[v3.1 API路径修复] 页面加载完成，开始检查内联脚本');

        // 延迟执行，确保所有脚本都已加载
        setTimeout(function() {
            // 检查是否有jQuery
            if (window.jQuery) {
                console.log('[v3.1 API路径修复] 检测到jQuery，修复jQuery AJAX设置');

                // 重写jQuery的ajax方法
                if (jQuery.ajax) {
                    const originalJQueryAjax = jQuery.ajax;
                    jQuery.ajax = function(url, options) {
                        // 处理两种调用形式：$.ajax(url, options) 和 $.ajax(options)
                        if (typeof url === 'string') {
                            url = fixApiPath(url);
                            return originalJQueryAjax.call(this, url, options);
                        } else {
                            // url参数实际上是options对象
                            const ajaxOptions = url;
                            if (ajaxOptions && ajaxOptions.url) {
                                ajaxOptions.url = fixApiPath(ajaxOptions.url);
                            }
                            return originalJQueryAjax.call(this, ajaxOptions);
                        }
                    };
                    console.log('[v3.1 API路径修复] jQuery.ajax方法已重写');
                }
            }
        }, 500);
    });

    console.log('[v3.1 API路径修复] 脚本初始化完成');
})();
