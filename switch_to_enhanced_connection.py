"""
九猫 - 切换到增强版数据库连接管理模块
"""

import os
import sys
import logging
import shutil
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("switch_connection.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def backup_file(file_path):
    """备份文件"""
    if not os.path.exists(file_path):
        logger.warning(f"文件不存在，无法备份: {file_path}")
        return False
    
    # 创建备份文件名
    backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    try:
        # 复制文件
        shutil.copy2(file_path, backup_path)
        logger.info(f"已备份文件: {file_path} -> {backup_path}")
        return True
    except Exception as e:
        logger.error(f"备份文件时出错: {str(e)}")
        return False

def switch_to_enhanced_connection():
    """切换到增强版数据库连接管理模块"""
    # 原始连接模块路径
    original_path = "src/db/connection.py"
    
    # 增强版连接模块路径
    enhanced_path = "src/db/enhanced_connection.py"
    
    # 检查文件是否存在
    if not os.path.exists(enhanced_path):
        logger.error(f"增强版连接模块不存在: {enhanced_path}")
        return False
    
    # 备份原始连接模块
    if os.path.exists(original_path):
        if not backup_file(original_path):
            logger.error("备份原始连接模块失败，操作已取消")
            return False
    
    try:
        # 复制增强版连接模块到原始连接模块位置
        shutil.copy2(enhanced_path, original_path)
        logger.info(f"已切换到增强版数据库连接管理模块: {enhanced_path} -> {original_path}")
        return True
    except Exception as e:
        logger.error(f"切换连接模块时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始切换到增强版数据库连接管理模块...")
    
    success = switch_to_enhanced_connection()
    
    if success:
        logger.info("成功切换到增强版数据库连接管理模块")
        print("\n已成功切换到增强版数据库连接管理模块！请重启应用以应用更改。\n")
    else:
        logger.error("切换到增强版数据库连接管理模块失败")
        print("\n切换到增强版数据库连接管理模块失败，请查看日志文件了解详情。\n")

if __name__ == "__main__":
    main()
