<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-config</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, <PERSON>urier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="npm-config">npm-config</h1>
<span class="description">Manage the npm configuration files</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#sub-commands">Sub-commands</a></li><ul><li><a href="#set">set</a></li><li><a href="#get">get</a></li><li><a href="#list">list</a></li><li><a href="#delete">delete</a></li><li><a href="#edit2">edit</a></li></ul><li><a href="#configuration">Configuration</a></li><ul><li><a href="#json"><code>json</code></a></li><li><a href="#global"><code>global</code></a></li><li><a href="#editor"><code>editor</code></a></li><li><a href="#location"><code>location</code></a></li><li><a href="#long"><code>long</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<pre lang="bash"><code>npm config set &lt;key&gt;=&lt;value&gt; [&lt;key&gt;=&lt;value&gt; ...]
npm config get [&lt;key&gt; [&lt;key&gt; ...]]
npm config delete &lt;key&gt; [&lt;key&gt; ...]
npm config list [--json]
npm config edit

alias: c
</code></pre>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<p>Note: This command is unaware of workspaces.</p>
<h3 id="description">Description</h3>
<p>npm gets its config settings from the command line, environment
variables, <code>npmrc</code> files, and in some cases, the <code>package.json</code> file.</p>
<p>See <a href="../configuring-npm/npmrc.html">npmrc</a> for more information about the npmrc
files.</p>
<p>See <a href="../using-npm/config.html">config(7)</a> for a more thorough explanation of the
mechanisms involved, and a full list of config options available.</p>
<p>The <code>npm config</code> command can be used to update and edit the contents
of the user and global npmrc files.</p>
<h3 id="sub-commands">Sub-commands</h3>
<p>Config supports the following sub-commands:</p>
<h4 id="set">set</h4>
<pre lang="bash"><code>npm config set key=value [key=value...]
npm set key=value [key=value...]
</code></pre>
<p>Sets each of the config keys to the value provided.</p>
<p>If value is omitted, then it sets it to an empty string.</p>
<p>Note: for backwards compatibility, <code>npm config set key value</code> is supported
as an alias for <code>npm config set key=value</code>.</p>
<h4 id="get">get</h4>
<pre lang="bash"><code>npm config get [key ...]
npm get [key ...]
</code></pre>
<p>Echo the config value(s) to stdout.</p>
<p>If multiple keys are provided, then the values will be prefixed with the
key names.</p>
<p>If no keys are provided, then this command behaves the same as <code>npm config list</code>.</p>
<h4 id="list">list</h4>
<pre lang="bash"><code>npm config list
</code></pre>
<p>Show all the config settings. Use <code>-l</code> to also show defaults. Use <code>--json</code>
to show the settings in json format.</p>
<h4 id="delete">delete</h4>
<pre lang="bash"><code>npm config delete key [key ...]
</code></pre>
<p>Deletes the specified keys from all configuration files.</p>
<h4 id="edit2">edit</h4>
<pre lang="bash"><code>npm config edit
</code></pre>
<p>Opens the config file in an editor.  Use the <code>--global</code> flag to edit the
global config.</p>
<h3 id="configuration">Configuration</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="json"><code>json</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to output JSON data, rather than the normal output.</p>
<ul>
<li>In <code>npm pkg set</code> it enables parsing set values with JSON.parse() before
saving them to your <code>package.json</code>.</li>
</ul>
<p>Not supported by all npm commands.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="global"><code>global</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Operates in "global" mode, so that packages are installed into the <code>prefix</code>
folder instead of the current working directory. See
<a href="../configuring-npm/folders.html">folders</a> for more on the differences in behavior.</p>
<ul>
<li>packages are installed into the <code>{prefix}/lib/node_modules</code> folder, instead
of the current working directory.</li>
<li>bin files are linked to <code>{prefix}/bin</code></li>
<li>man pages are linked to <code>{prefix}/share/man</code></li>
</ul>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="editor"><code>editor</code></h4>
<ul>
<li>Default: The EDITOR or VISUAL environment variables, or 'notepad.exe' on
Windows, or 'vim' on Unix systems</li>
<li>Type: String</li>
</ul>
<p>The command to run for <code>npm edit</code> and <code>npm config edit</code>.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="location"><code>location</code></h4>
<ul>
<li>Default: "user" unless <code>--global</code> is passed, which will also set this value
to "global"</li>
<li>Type: "global", "user", or "project"</li>
</ul>
<p>When passed to <code>npm config</code> this refers to which config file to use.</p>
<p>When set to "global" mode, packages are installed into the <code>prefix</code> folder
instead of the current working directory. See
<a href="../configuring-npm/folders.html">folders</a> for more on the differences in behavior.</p>
<ul>
<li>packages are installed into the <code>{prefix}/lib/node_modules</code> folder, instead
of the current working directory.</li>
<li>bin files are linked to <code>{prefix}/bin</code></li>
<li>man pages are linked to <code>{prefix}/share/man</code></li>
</ul>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="long"><code>long</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show extended information in <code>ls</code>, <code>search</code>, and <code>help-search</code>.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../configuring-npm/folders.html">npm folders</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../configuring-npm/package-json.html">package.json</a></li>
<li><a href="../configuring-npm/npmrc.html">npmrc</a></li>
<li><a href="../commands/npm.html">npm</a></li>
</ul>
</div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-config.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>