<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <!-- 预加载核心JS库 -->
    <link rel="preload" href="/static/js/lib/jquery.min.js" as="script">
    <link rel="preload" href="/static/js/lib/bootstrap.bundle.min.js" as="script">
    
    <!-- 预加载CSS -->
    <link rel="preload" href="/static/css/lib/bootstrap.min.css" as="style">
    <link rel="stylesheet" href="/static/css/lib/bootstrap.min.css">
    
    <!-- 优先加载错误处理和修复脚本 -->
    <script src="/static/js/error-handler.js" onerror="console.error('错误处理脚本加载失败')"></script>
    <script src="/static/js/lib/dom-safe.js" onerror="console.error('DOM安全操作库加载失败')"></script>
    <script src="/static/js/stack-overflow-fix.js" onerror="console.error('堆栈溢出修复脚本加载失败')"></script>
    <script src="/static/js/complete-fix.js" onerror="console.error('完整修复脚本加载失败')"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h2>测试页面</h2>
                    </div>
                    <div class="card-body">
                        <p class="lead">如果您能看到这个页面，说明基本HTML加载正常。</p>
                        <p>如果页面样式正常（有卡片边框、间距等），说明Bootstrap CSS加载正常。</p>
                        <button id="testBtn" class="btn btn-primary">测试jQuery和Bootstrap JS</button>
                        <div id="result" class="mt-3"></div>
                        
                        <hr>
                        
                        <h3>DOM安全操作测试</h3>
                        <button id="testDomSafe" class="btn btn-success">测试DOM安全操作</button>
                        <div id="domSafeResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery先加载，确保在Bootstrap JS之前 -->
    <script src="/static/js/lib/jquery.min.js" onerror="console.error('jQuery加载失败')"></script>
    <!-- Bootstrap JS依赖jQuery -->
    <script src="/static/js/lib/bootstrap.bundle.min.js" onerror="console.error('Bootstrap JS加载失败')"></script>
    <script>
        // 在文档加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 检查jQuery是否已加载
            if (typeof jQuery === 'undefined') {
                console.error('jQuery未加载，尝试手动加载');
                
                // 使用安全的DOM操作方法
                if (window.DOMSafe) {
                    const script = DOMSafe.createElement('script');
                    script.src = '/static/js/lib/jquery.min.js';
                    DOMSafe.appendChild(document.head, script);
                } else {
                    try {
                        var jqueryScript = document.createElement('script');
                        jqueryScript.src = '/static/js/lib/jquery.min.js';
                        document.head.appendChild(jqueryScript);
                    } catch (e) {
                        console.error('手动加载jQuery失败:', e.message);
                    }
                }
            } else {
                console.log('jQuery已加载，版本:', jQuery.fn.jquery);
                // 初始化按钮事件
                $('#testBtn').click(function() {
                    $('#result').html('<div class="alert alert-success">jQuery和Bootstrap JS加载正常！</div>');
                });
            }
            
            // 测试DOM安全操作库
            const testDomSafeBtn = document.getElementById('testDomSafe');
            const domSafeResult = document.getElementById('domSafeResult');
            
            if (testDomSafeBtn && domSafeResult) {
                testDomSafeBtn.addEventListener('click', function() {
                    if (window.DOMSafe) {
                        // 创建元素
                        const div = DOMSafe.createElement('div');
                        DOMSafe.addClass(div, 'alert');
                        DOMSafe.addClass(div, 'alert-info');
                        DOMSafe.setInnerHTML(div, 'DOM安全操作库加载正常且可用！');
                        
                        // 清空结果区域
                        while (domSafeResult.firstChild) {
                            DOMSafe.removeChild(domSafeResult, domSafeResult.firstChild);
                        }
                        
                        // 添加新的结果
                        DOMSafe.appendChild(domSafeResult, div);
                    } else {
                        try {
                            domSafeResult.innerHTML = '<div class="alert alert-danger">DOM安全操作库未加载！</div>';
                        } catch (e) {
                            console.error('无法更新DOM安全操作测试结果:', e.message);
                        }
                    }
                });
            }
            
            console.log('页面加载完成，DOM已初始化');
        });
    </script>
</body>
</html>
