Option Explicit

' 九猫小说分析写作系统v3.5启动脚本
' 此脚本用于启动九猫小说分析写作系统v3.5版本

' 设置变量
Dim WshShell, fso, currentDir, pythonCmd, appModule
Dim browser, browserPath, defaultBrowser

' 创建对象
Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' 获取当前目录
currentDir = fso.GetAbsolutePathName(".")

' 设置Python命令和应用模块
pythonCmd = "python -u -m src.web.v3.5_app"

' 显示启动信息
WScript.Echo "正在启动九猫小说分析写作系统v3.5版本..."
WScript.Echo "当前目录: " & currentDir
WScript.Echo "启动命令: " & pythonCmd

' 复制修复脚本到静态文件目录
On Error Resume Next
If fso.FileExists("fix_chapter_outline_button.js") Then
    If Not fso.FolderExists("src\web\static\js") Then
        fso.CreateFolder("src\web\static\js")
    End If
    fso.CopyFile "fix_chapter_outline_button.js", "src\web\static\js\fix_chapter_outline_button.js", True
    WScript.Echo "已复制修复脚本到静态文件目录"
End If
On Error GoTo 0

' 启动应用
WshShell.Run pythonCmd, 1, False

' 等待应用启动
WScript.Sleep 2000

' 打开浏览器
WshShell.Run "http://localhost:5001", 1, False

WScript.Echo "九猫小说分析写作系统v3.5版本已启动"
WScript.Echo "请在浏览器中访问: http://localhost:5001"

' 等待用户按任意键退出
WScript.Echo "按任意键退出此窗口..."
WshShell.Run "cmd /c pause", 1, True
