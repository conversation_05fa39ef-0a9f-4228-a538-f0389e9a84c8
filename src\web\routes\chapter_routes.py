"""
Chapter routes for the 九猫 (Nine Cats) novel analysis system.
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.db.connection import Session
from src.services.chapter_analysis_service import ChapterAnalysisService
import json
import logging
import os
import traceback
import config

chapter_bp = Blueprint('chapter', __name__)
logger = logging.getLogger(__name__)

# 分析维度配置
ANALYSIS_DIMENSIONS = config.ANALYSIS_DIMENSIONS

@chapter_bp.route('/novel/<int:novel_id>/chapters')
def list_chapters(novel_id):
    """
    列出小说的所有章节。

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter(Novel.id == novel_id).first()
        if not novel:
            flash('小说不存在', 'error')
            return redirect(url_for('index'))

        # 获取章节
        chapters = session.query(Chapter).filter(Chapter.novel_id == novel_id).order_by(Chapter.chapter_number).all()

        # 如果没有章节，尝试预览分割
        if not chapters:
            return redirect(url_for('chapter.preview_chapters', novel_id=novel_id))

        # 获取每个章节的分析结果数量
        chapter_analysis_counts = {}
        for chapter in chapters:
            count = session.query(ChapterAnalysisResult).filter(ChapterAnalysisResult.chapter_id == chapter.id).count()
            chapter_analysis_counts[chapter.id] = count

        return render_template(
            'chapters/list.html',
            novel=novel,
            chapters=chapters,
            analysis_counts=chapter_analysis_counts,
            dimensions=ANALYSIS_DIMENSIONS
        )
    except Exception as e:
        logger.error(f"列出章节时出错: {str(e)}")
        flash(f'列出章节时出错: {str(e)}', 'error')
        return redirect(url_for('index'))
    finally:
        session.close()

@chapter_bp.route('/novel/<int:novel_id>/chapters/preview')
def preview_chapters(novel_id):
    """
    预览章节分割。

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter(Novel.id == novel_id).first()
        if not novel:
            flash('小说不存在', 'error')
            return redirect(url_for('index'))

        # 使用TextProcessor预览章节分割
        from src.utils.text_processor import TextProcessor
        preview_info = TextProcessor.preview_chapter_splitting(novel.content)

        # 转换为JSON，用于JavaScript
        import json
        chapters_json = json.dumps(preview_info)

        return render_template(
            'chapters/preview.html',
            novel=novel,
            chapters=preview_info,
            chapters_json=chapters_json
        )
    except Exception as e:
        logger.error(f"预览章节分割时出错: {str(e)}")
        logger.error(traceback.format_exc())
        flash(f'预览章节分割时出错: {str(e)}', 'error')
        return redirect(url_for('view_novel', novel_id=novel_id))
    finally:
        session.close()

@chapter_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>')
def view_chapter(novel_id, chapter_id):
    """
    查看章节详情。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter(Novel.id == novel_id).first()
        if not novel:
            flash('小说不存在', 'error')
            return redirect(url_for('index'))

        # 获取章节
        chapter = session.query(Chapter).filter(Chapter.id == chapter_id, Chapter.novel_id == novel_id).first()
        if not chapter:
            flash('章节不存在', 'error')
            return redirect(url_for('chapter.list_chapters', novel_id=novel_id))

        # 获取章节的分析结果
        results = session.query(ChapterAnalysisResult).filter(ChapterAnalysisResult.chapter_id == chapter_id).all()

        # 按维度组织分析结果
        analysis_results = {}
        for result in results:
            analysis_results[result.dimension] = result

        return render_template(
            'chapters/view.html',
            novel=novel,
            chapter=chapter,
            analysis_results=analysis_results,
            dimensions=ANALYSIS_DIMENSIONS
        )
    except Exception as e:
        logger.error(f"查看章节时出错: {str(e)}")
        flash(f'查看章节时出错: {str(e)}', 'error')
        return redirect(url_for('chapter.list_chapters', novel_id=novel_id))
    finally:
        session.close()

@chapter_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def view_chapter_analysis(novel_id, chapter_id, dimension):
    """
    查看章节分析结果。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter(Novel.id == novel_id).first()
        if not novel:
            flash('小说不存在', 'error')
            return redirect(url_for('index'))

        # 获取章节
        chapter = session.query(Chapter).filter(Chapter.id == chapter_id, Chapter.novel_id == novel_id).first()
        if not chapter:
            flash('章节不存在', 'error')
            return redirect(url_for('chapter.list_chapters', novel_id=novel_id))

        # 获取分析结果
        result = session.query(ChapterAnalysisResult).filter(
            ChapterAnalysisResult.chapter_id == chapter_id,
            ChapterAnalysisResult.dimension == dimension
        ).first()

        if not result:
            flash(f'章节的{dimension}分析结果不存在', 'error')
            return redirect(url_for('chapter.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

        # 记录推理过程内容长度，帮助调试
        reasoning_content_length = len(result.reasoning_content) if result.reasoning_content else 0
        logger.info(f"章节 {chapter_id} 的 {dimension} 推理过程内容长度: {reasoning_content_length}字符")

        # 如果没有推理过程内容，尝试从元数据中获取
        if reasoning_content_length == 0 and result.analysis_metadata:
            try:
                metadata = result.analysis_metadata
                if isinstance(metadata, dict) and 'reasoning_content' in metadata:
                    result.reasoning_content = metadata['reasoning_content']
                    reasoning_content_length = len(result.reasoning_content)
                    logger.info(f"从元数据中获取到推理过程内容，长度: {reasoning_content_length}字符")
            except Exception as e:
                logger.error(f"从元数据中获取推理过程内容时出错: {str(e)}")

        # 获取维度名称
        dimension_name = dimension
        for dim in ANALYSIS_DIMENSIONS:
            if dim.get('key') == dimension:
                dimension_name = dim.get('name', dimension)
                break

        return render_template(
            'chapters/analysis.html',
            novel=novel,
            chapter=chapter,
            result=result,
            dimension=dimension,
            dimension_name=dimension_name,
            reasoning_content_length=reasoning_content_length
        )
    except Exception as e:
        logger.error(f"查看章节分析结果时出错: {str(e)}")
        flash(f'查看章节分析结果时出错: {str(e)}', 'error')
        return redirect(url_for('chapter.view_chapter', novel_id=novel_id, chapter_id=chapter_id))
    finally:
        session.close()

@chapter_bp.route('/novel/<int:novel_id>/chapters/analyze', methods=['POST'])
def analyze_chapters(novel_id):
    """
    分析小说的所有章节。

    Args:
        novel_id: 小说ID
    """
    try:
        # 获取请求参数 - 支持JSON和表单提交
        if request.is_json:
            data = request.json or {}
        else:
            data = request.form or {}

        dimension = data.get('dimension')
        model = data.get('model', config.DEFAULT_MODEL)

        # 处理布尔值和整数值
        parallel_value = data.get('parallel')
        if isinstance(parallel_value, str):
            parallel = parallel_value.lower() in ('true', 'yes', 'y', '1', 'on')
        else:
            parallel = bool(parallel_value) if parallel_value is not None else True

        max_workers_value = data.get('max_workers')
        try:
            max_workers = int(max_workers_value) if max_workers_value is not None else 4
        except (ValueError, TypeError):
            max_workers = 4

        if not dimension:
            return jsonify({"success": False, "error": "缺少维度参数"})

        # 验证并行处理参数
        if not isinstance(parallel, bool):
            parallel = True

        if not isinstance(max_workers, int) or max_workers < 1:
            max_workers = 4
        elif max_workers > 8:  # 限制最大工作线程数
            max_workers = 8

        # 启动分析任务
        result = ChapterAnalysisService.start_chapter_analysis_task(
            novel_id, dimension, model, parallel, max_workers
        )

        # 根据请求类型返回不同的响应
        if request.is_json:
            return jsonify(result)
        else:
            # 如果是表单提交，重定向回章节列表页面
            if result.get('success'):
                flash('分析任务已启动，请稍后刷新页面查看结果', 'success')
            else:
                flash(f'启动分析任务失败: {result.get("error", "未知错误")}', 'error')
            return redirect(url_for('chapter.list_chapters', novel_id=novel_id))
    except Exception as e:
        logger.error(f"启动章节分析任务时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})

@chapter_bp.route('/novel/<int:novel_id>/chapters/progress')
def get_chapters_analysis_progress(novel_id):
    """
    获取章节分析进度。

    Args:
        novel_id: 小说ID
    """
    try:
        # 获取分析进度
        progress = ChapterAnalysisService.get_chapter_analysis_task_progress(novel_id)

        return jsonify(progress)
    except Exception as e:
        logger.error(f"获取章节分析进度时出错: {str(e)}")
        return jsonify({"success": False, "error": str(e)})

@chapter_bp.route('/novel/<int:novel_id>/chapters/cancel', methods=['POST'])
def cancel_chapters_analysis(novel_id):
    """
    取消章节分析任务。

    Args:
        novel_id: 小说ID
    """
    try:
        # 取消分析任务
        result = ChapterAnalysisService.cancel_chapter_analysis_task(novel_id)

        return jsonify(result)
    except Exception as e:
        logger.error(f"取消章节分析任务时出错: {str(e)}")
        return jsonify({"success": False, "error": str(e)})

@chapter_bp.route('/api/novel/<int:novel_id>/chapters')
def api_list_chapters(novel_id):
    """
    API: 列出小说的所有章节。

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter(Novel.id == novel_id).first()
        if not novel:
            return jsonify({"success": False, "error": "小说不存在"})

        # 获取章节
        chapters = session.query(Chapter).filter(Chapter.novel_id == novel_id).order_by(Chapter.chapter_number).all()

        # 转换为字典
        chapters_dict = [chapter.get_summary() for chapter in chapters]

        return jsonify({
            "success": True,
            "novel": novel.get_summary(),
            "chapters": chapters_dict
        })
    except Exception as e:
        logger.error(f"API列出章节时出错: {str(e)}")
        return jsonify({"success": False, "error": str(e)})
    finally:
        session.close()

@chapter_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>')
def api_get_chapter(novel_id, chapter_id):
    """
    API: 获取章节详情。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
    """
    session = Session()
    try:
        # 获取章节
        chapter = session.query(Chapter).filter(Chapter.id == chapter_id, Chapter.novel_id == novel_id).first()
        if not chapter:
            return jsonify({"success": False, "error": "章节不存在"})

        return jsonify({
            "success": True,
            "chapter": chapter.to_dict()
        })
    except Exception as e:
        logger.error(f"API获取章节时出错: {str(e)}")
        return jsonify({"success": False, "error": str(e)})
    finally:
        session.close()

@chapter_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def api_get_chapter_analysis(novel_id, chapter_id, dimension):
    """
    API: 获取章节分析结果。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
    """
    try:
        # 获取分析结果
        result = ChapterAnalysisService.get_chapter_analysis_result(chapter_id, dimension)

        # 记录日志，帮助调试
        logger.info(f"获取章节 {chapter_id} 的 {dimension} 分析结果: {result['success']}")

        # 如果没有找到分析结果，尝试创建一个空结果
        if not result['success'] and "未找到分析结果" in result.get('error', ''):
            # 获取维度名称
            dimension_name = dimension
            for dim in ANALYSIS_DIMENSIONS:
                if dim.get('key') == dimension:
                    dimension_name = dim.get('name', dimension)
                    break

            # 创建一个空的分析结果
            empty_result = {
                "success": True,
                "result": {
                    "id": None,
                    "chapter_id": chapter_id,
                    "dimension": dimension,
                    "content": f"<div class='alert alert-info'>该章节尚未进行{dimension_name}分析，请先进行分析。</div>",
                    "metadata": {}
                }
            }
            return jsonify(empty_result)

        return jsonify(result)
    except Exception as e:
        logger.error(f"API获取章节分析结果时出错: {str(e)}")
        return jsonify({"success": False, "error": str(e)})

@chapter_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content')
def api_get_chapter_reasoning_content(novel_id, chapter_id, dimension):
    """
    API: 获取章节分析的推理过程内容。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
    """
    try:
        # 获取请求参数
        full = request.args.get('full', 'false').lower() == 'true'

        # 记录详细的请求信息
        logger.info(f"收到章节推理过程请求: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, full={full}")

        # 获取分析结果
        result = ChapterAnalysisService.get_chapter_analysis_result(chapter_id, dimension)

        # 记录日志，帮助调试
        logger.info(f"获取章节 {chapter_id} 的 {dimension} 推理过程: {result['success']}")

        # 如果没有找到分析结果，返回错误
        if not result['success']:
            logger.warning(f"未找到章节 {chapter_id} 的 {dimension} 分析结果: {result.get('error', '未知错误')}")
            return jsonify({
                "success": False,
                "error": result.get('error', '未找到分析结果'),
                "status": 404
            }), 404

        # 从分析结果中提取推理过程
        analysis_result = result.get('result', {})
        reasoning_content = analysis_result.get('reasoning_content', '')
        logger.info(f"从分析结果中提取推理过程，长度: {len(reasoning_content) if reasoning_content else 0}")

        # 如果没有推理过程，尝试从元数据中获取
        if not reasoning_content and analysis_result.get('metadata'):
            metadata_reasoning = analysis_result.get('metadata', {}).get('reasoning_content', '')
            if metadata_reasoning:
                reasoning_content = metadata_reasoning
                logger.info(f"从元数据中获取到推理过程，长度: {len(reasoning_content)}")

        # 如果仍然没有推理过程，尝试直接从数据库查询
        if not reasoning_content:
            logger.info(f"从分析结果中未找到推理过程内容，尝试直接从数据库查询")
            session = Session()
            try:
                # 直接从数据库查询章节分析结果
                result_db = session.query(ChapterAnalysisResult).filter(
                    ChapterAnalysisResult.chapter_id == chapter_id,
                    ChapterAnalysisResult.dimension == dimension
                ).first()

                if result_db and result_db.reasoning_content:
                    reasoning_content = result_db.reasoning_content
                    logger.info(f"从数据库直接查询到推理过程内容，长度: {len(reasoning_content)}")
                else:
                    # 如果数据库中也没有推理过程内容，尝试使用章节推理过程修复API生成
                    logger.warning(f"数据库中也未找到推理过程内容，尝试使用修复API生成")
                    try:
                        # 构建修复API URL
                        fix_url = f"/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/generate_reasoning"

                        # 使用内部重定向调用修复API
                        from flask import current_app
                        with current_app.test_client() as client:
                            response = client.get(fix_url)

                            if response.status_code == 200:
                                fix_data = response.get_json()
                                if fix_data.get('success') and fix_data.get('reasoning_content'):
                                    reasoning_content = fix_data['reasoning_content']
                                    logger.info(f"成功使用修复API生成推理过程，长度: {len(reasoning_content)}")
                                else:
                                    logger.warning(f"修复API返回成功但没有推理内容: {fix_data}")
                            else:
                                logger.warning(f"修复API调用失败，状态码: {response.status_code}")
                    except Exception as fix_error:
                        logger.error(f"调用修复API时出错: {str(fix_error)}")

                    # 如果仍然没有推理过程内容，返回错误
                    if not reasoning_content:
                        logger.warning(f"无法获取或生成推理过程内容")
                        return jsonify({
                            "success": False,
                            "error": "未找到推理过程内容",
                            "status": 404
                        }), 404
            except Exception as db_error:
                logger.error(f"直接查询数据库时出错: {str(db_error)}")
                logger.error(traceback.format_exc())
                # 继续使用原来的错误处理
                return jsonify({
                    "success": False,
                    "error": "未找到推理过程内容",
                    "status": 404
                }), 404
            finally:
                session.close()

        # 确保推理过程内容格式正确
        try:
            # 导入格式化函数
            from reasoning_content_template import format_reasoning_content

            # 格式化推理过程内容
            formatted_reasoning = format_reasoning_content(reasoning_content)
            logger.info(f"推理过程内容已格式化，原长度: {len(reasoning_content)}，新长度: {len(formatted_reasoning)}")

            # 使用格式化后的内容
            reasoning_content = formatted_reasoning
        except Exception as e:
            # 如果格式化失败，记录错误但继续使用原始内容
            logger.error(f"格式化推理过程内容时出错: {str(e)}")
            logger.error(traceback.format_exc())

        # 返回推理过程
        return jsonify({
            "success": True,
            "reasoning_content": reasoning_content,
            "source": "chapter_analysis_result",
            "full": full
        })
    except Exception as e:
        logger.error(f"API获取章节推理过程时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})

@chapter_bp.route('/api/novel/<int:novel_id>/chapters/analysis')
def api_get_chapters_analysis(novel_id):
    """
    API: 获取小说所有章节的分析结果。

    Args:
        novel_id: 小说ID
    """
    try:
        # 获取请求参数
        dimension = request.args.get('dimension')

        # 获取分析结果
        results = ChapterAnalysisService.get_chapter_analysis_results(novel_id, dimension)

        return jsonify(results)
    except Exception as e:
        logger.error(f"API获取章节分析结果时出错: {str(e)}")
        return jsonify({"success": False, "error": str(e)})

@chapter_bp.route('/api/novel/<int:novel_id>/preview_split', methods=['POST'])
def api_preview_split(novel_id):
    """
    API: 预览章节分割。

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter(Novel.id == novel_id).first()
        if not novel:
            return jsonify({"success": False, "error": "小说不存在"})

        # 获取请求参数
        data = request.json or {}
        min_chapter_length = data.get('min_chapter_length', 100)
        max_chapter_length = data.get('max_chapter_length', 50000)
        split_method = data.get('split_method', 'auto')

        # 使用TextProcessor预览章节分割
        from src.utils.text_processor import TextProcessor

        # 根据分割方法选择不同的参数
        if split_method == 'auto':
            preview_info = TextProcessor.preview_chapter_splitting(
                novel.content,
                max_preview_length=100
            )
        else:
            # 这里可以根据不同的分割方法设置不同的参数
            preview_info = TextProcessor.preview_chapter_splitting(
                novel.content,
                max_preview_length=100
            )

        return jsonify({
            "success": True,
            "chapters": preview_info
        })
    except Exception as e:
        logger.error(f"API预览章节分割时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})
    finally:
        session.close()

@chapter_bp.route('/api/novel/<int:novel_id>/confirm_split', methods=['POST'])
def api_confirm_split(novel_id):
    """
    API: 确认章节分割，创建章节。

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter(Novel.id == novel_id).first()
        if not novel:
            return jsonify({"success": False, "error": "小说不存在"})

        # 获取请求参数
        data = request.json or {}
        chapters_data = data.get('chapters', [])

        if not chapters_data:
            return jsonify({"success": False, "error": "没有章节数据"})

        # 删除现有章节
        existing_chapters = session.query(Chapter).filter(Chapter.novel_id == novel_id).all()
        for chapter in existing_chapters:
            session.delete(chapter)

        # 创建新章节
        created_chapters = []
        for i, chapter_data in enumerate(chapters_data):
            chapter = Chapter(
                novel_id=novel_id,
                chapter_number=i + 1,
                content=chapter_data.get('content', ''),
                title=chapter_data.get('title', f'第{i+1}章')
            )
            session.add(chapter)
            created_chapters.append(chapter)

        # 提交事务
        session.commit()

        return jsonify({
            "success": True,
            "message": f"成功创建 {len(created_chapters)} 个章节",
            "chapters": [chapter.get_summary() for chapter in created_chapters]
        })
    except Exception as e:
        logger.error(f"API确认章节分割时出错: {str(e)}")
        logger.error(traceback.format_exc())
        session.rollback()
        return jsonify({"success": False, "error": str(e)})
    finally:
        session.close()

@chapter_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze', methods=['POST'])
def api_analyze_chapter(novel_id, chapter_id):
    """
    API: 分析单个章节。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
    """
    try:
        # 记录请求开始
        logger.info(f"收到章节分析请求: novel_id={novel_id}, chapter_id={chapter_id}")

        # 获取请求参数 - 支持JSON和表单提交
        if request.is_json:
            data = request.json or {}
        else:
            data = request.form or {}

        dimension = data.get('dimension')
        model = data.get('model', config.DEFAULT_MODEL)

        logger.info(f"分析参数: dimension={dimension}, model={model}")

        if not dimension:
            logger.warning("缺少维度参数")
            return jsonify({"success": False, "error": "缺少维度参数"})

        # 验证章节属于该小说
        session = Session()
        try:
            chapter = session.query(Chapter).filter(Chapter.id == chapter_id, Chapter.novel_id == novel_id).first()

            if not chapter:
                logger.warning(f"章节不存在或不属于该小说: novel_id={novel_id}, chapter_id={chapter_id}")
                return jsonify({"success": False, "error": "章节不存在或不属于该小说"})

            logger.info(f"找到章节: id={chapter.id}, title={chapter.title}, novel_id={chapter.novel_id}")
        finally:
            session.close()

        # 启动章节分析
        logger.info(f"开始分析小说 {novel_id} 的章节 {chapter_id}, 维度={dimension}, 模型={model}")

        # 确保使用真实API调用，不使用缓存
        logger.info(f"确保使用真实API调用，不使用缓存")

        # 添加到控制台日志
        try:
            from src.web.app import add_analysis_log
            add_analysis_log(novel_id, f"[章节分析] 开始分析章节 {chapter_id} 的 {dimension} 维度", "info", dimension)
        except Exception as log_error:
            logger.error(f"添加分析开始日志时出错: {str(log_error)}")

        # 记录实际使用的小说ID和章节ID
        logger.info(f"实际使用的小说ID: {novel_id}, 章节ID: {chapter_id}")

        result = ChapterAnalysisService.analyze_chapter(
            chapter_id=chapter_id,
            dimension=dimension,
            model=model,
            use_cache=False,  # 强制重新分析
            save_process=True  # 保存分析过程
        )

        logger.info(f"章节分析完成: success={result.get('success', False)}")
        if not result.get('success', False):
            logger.error(f"章节分析失败: {result.get('error', '未知错误')}")

        # 根据请求类型返回不同的响应
        if request.is_json:
            return jsonify(result)
        else:
            # 如果是表单提交，重定向回章节详情页面
            if result.get('success'):
                flash('分析完成', 'success')
            else:
                flash(f'分析失败: {result.get("error", "未知错误")}', 'error')
            return redirect(url_for('chapter.view_chapter', novel_id=novel_id, chapter_id=chapter_id))
    except Exception as e:
        logger.error(f"分析章节时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})

@chapter_bp.route('/api/novel/<int:novel_id>/chapter_preview/<int:chapter_index>')
def api_get_chapter_preview(novel_id, chapter_index):
    """
    API: 获取章节预览内容。

    Args:
        novel_id: 小说ID
        chapter_index: 章节索引
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter(Novel.id == novel_id).first()
        if not novel:
            return jsonify({"success": False, "error": "小说不存在"})

        # 使用TextProcessor分割章节
        from src.utils.text_processor import TextProcessor
        chapters = TextProcessor.split_into_chapters(novel.content)

        # 检查章节索引是否有效
        if chapter_index < 0 or chapter_index >= len(chapters):
            return jsonify({"success": False, "error": "无效的章节索引"})

        # 获取章节内容
        chapter_content = chapters[chapter_index]

        return jsonify({
            "success": True,
            "content": chapter_content
        })
    except Exception as e:
        logger.error(f"API获取章节预览内容时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})
    finally:
        session.close()

@chapter_bp.route('/api/novel/<int:novel_id>/chapters/progress')
def api_get_chapters_analysis_progress(novel_id):
    """
    API: 获取章节分析进度。

    Args:
        novel_id: 小说ID
    """
    try:
        # 获取分析进度
        progress = ChapterAnalysisService.get_chapter_analysis_task_progress(novel_id)

        return jsonify(progress)
    except Exception as e:
        logger.error(f"API获取章节分析进度时出错: {str(e)}")
        return jsonify({"success": False, "error": str(e)})

@chapter_bp.route('/api/novel/<int:novel_id>/chapters/batch_content', methods=['POST'])
def api_get_batch_chapter_content(novel_id):
    """
    API: 批量获取章节内容。

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 获取请求参数
        data = request.json or {}
        chapter_ids = data.get('chapter_ids', [])
        dimension = data.get('dimension')

        if not chapter_ids:
            return jsonify({"success": False, "error": "缺少章节ID列表"})

        # 获取章节内容
        chapters = {}
        for chapter_id in chapter_ids:
            try:
                chapter_id = int(chapter_id)
                chapter = session.query(Chapter).filter(Chapter.id == chapter_id).first()

                if chapter:
                    # 如果指定了维度，获取分析结果
                    if dimension:
                        analysis_result = session.query(ChapterAnalysisResult).filter(
                            ChapterAnalysisResult.chapter_id == chapter_id,
                            ChapterAnalysisResult.dimension == dimension
                        ).first()

                        if analysis_result:
                            chapters[str(chapter_id)] = {
                                "id": chapter_id,
                                "title": chapter.title,
                                "content": analysis_result.content,
                                "has_analysis": True
                            }
                        else:
                            chapters[str(chapter_id)] = {
                                "id": chapter_id,
                                "title": chapter.title,
                                "content": "该章节尚未进行此维度的分析",
                                "has_analysis": False
                            }
                    else:
                        # 获取章节内容
                        chapters[str(chapter_id)] = {
                            "id": chapter_id,
                            "title": chapter.title,
                            "content": chapter.content,
                            "has_analysis": False
                        }
            except Exception as e:
                logger.error(f"获取章节 {chapter_id} 内容时出错: {str(e)}")
                chapters[str(chapter_id)] = {
                    "id": chapter_id,
                    "error": str(e)
                }

        return jsonify({
            "success": True,
            "chapters": chapters
        })
    except Exception as e:
        logger.error(f"API批量获取章节内容时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})
    finally:
        session.close()

@chapter_bp.route('/novel/<int:novel_id>/chapters/summary')
def chapters_summary(novel_id):
    """
    章节分析汇总页面，优化版本支持懒加载。
    显示每个章节的13个维度分析结果，使用折叠式UI。

    Args:
        novel_id: 小说ID
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter(Novel.id == novel_id).first()
        if not novel:
            flash('小说不存在', 'error')
            return redirect(url_for('novels'))

        # 获取章节
        chapters = session.query(Chapter).filter(Chapter.novel_id == novel_id).order_by(Chapter.chapter_number).all()
        if not chapters:
            flash('小说没有章节', 'error')
            return redirect(url_for('view_novel', novel_id=novel_id))

        # 获取每个章节的分析维度（只获取维度信息，不获取具体内容）
        chapter_dimensions = {}
        for chapter in chapters:
            # 使用子查询获取每个章节的分析维度，避免加载完整内容
            dimensions = session.query(ChapterAnalysisResult.dimension).filter(
                ChapterAnalysisResult.chapter_id == chapter.id
            ).all()
            chapter_dimensions[chapter.id] = [dim[0] for dim in dimensions]

            # 记录日志，帮助调试
            logger.info(f"章节 {chapter.id} (第{chapter.chapter_number}章) 已分析维度: {chapter_dimensions[chapter.id]}")

        # 将章节分组（每10个章节一组）
        chapter_groups = []
        for i in range(0, len(chapters), 10):
            group_chapters = chapters[i:i+10]

            # 为每个章节添加分析维度
            for chapter in group_chapters:
                chapter.analysis_dimensions = chapter_dimensions.get(chapter.id, [])

            chapter_groups.append({
                'start': group_chapters[0].chapter_number,
                'end': group_chapters[-1].chapter_number,
                'chapters': group_chapters,
                'group_id': f"group_{i//10 + 1}"  # 添加组ID，用于懒加载
            })

        # 记录维度信息，帮助调试
        dimension_keys = [dim["key"] for dim in ANALYSIS_DIMENSIONS]
        logger.info(f"所有可用维度: {dimension_keys}")

        return render_template(
            'chapters/summary.html',
            novel=novel,
            chapters=chapters,
            chapter_groups=chapter_groups,
            dimensions=ANALYSIS_DIMENSIONS,
            lazy_load=True  # 启用懒加载
        )
    except Exception as e:
        logger.error(f"章节分析汇总页面出错: {str(e)}")
        logger.error(traceback.format_exc())
        flash(f'章节分析汇总页面出错: {str(e)}', 'error')
        return redirect(url_for('chapter.list_chapters', novel_id=novel_id))
    finally:
        session.close()
