/**
 * 九猫 - 应急资源加载器
 * 当静态资源加载失败时，提供内联备用资源
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
  console.log('应急资源加载器已初始化');

  // 检查资源是否已加载
  function checkResources() {
    console.log('检查必要的资源是否已加载');

    // 添加全局命名空间
    if (!window.NineCats) {
      window.NineCats = {
        containers: {},
        variables: {},
        utils: {}
      };
    }

    // 检查CSS
    if (!document.querySelector('link[href*="bootstrap"]')) {
      console.warn('未找到Bootstrap CSS，尝试加载');
      loadEmergencyCSS();
    }

    // 检查jQuery
    if (typeof window.jQuery === 'undefined') {
      console.warn('未找到jQuery，尝试加载');
      loadEmergencyJQuery();
    }

    // 检查Bootstrap
    if (typeof window.bootstrap === 'undefined') {
      console.warn('未找到Bootstrap JS，尝试加载');
      loadEmergencyBootstrap();
    }

    // 检查Chart.js
    if (typeof window.Chart === 'undefined') {
      console.warn('未找到Chart.js，尝试加载');
      loadEmergencyChart();
    }
  }

  // 加载紧急样式
  function loadEmergencyCSS() {
    const style = document.createElement('style');
    style.textContent = `
      /* 最小化的Bootstrap风格CSS */
      body { font-family: system-ui, sans-serif; line-height: 1.5; margin: 0; }
      .container { width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto; }
      .row { display: flex; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
      .col, .col-12, .col-md-6 { position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
      .col-12 { flex: 0 0 100%; max-width: 100%; }
      @media (min-width: 768px) { .col-md-6 { flex: 0 0 50%; max-width: 50%; } }
      .card { position: relative; display: flex; flex-direction: column; min-width: 0; word-wrap: break-word; background-color: #fff; border: 1px solid rgba(0,0,0,.125); border-radius: 0.25rem; }
      .card-body { flex: 1 1 auto; min-height: 1px; padding: 1.25rem; }
      .card-title { margin-bottom: 0.75rem; font-size: 1.25rem; }
      .btn { display: inline-block; font-weight: 400; text-align: center; vertical-align: middle; user-select: none; padding: .375rem .75rem; font-size: 1rem; line-height: 1.5; border-radius: .25rem; cursor: pointer; background-color: #f8f9fa; border: 1px solid transparent; }
      .btn-primary { color: #fff; background-color: #007bff; border-color: #007bff; }
      .btn-secondary { color: #fff; background-color: #6c757d; border-color: #6c757d; }
      .collapse { display: none; }
      .collapse.show { display: block; }
    `;
    document.head.appendChild(style);
    console.log('已加载紧急CSS样式');
  }

  // 加载紧急jQuery替代品
  function loadEmergencyJQuery() {
    if (window.jQuery) return;

    window.jQuery = window.$ = function(selector) {
      // 简单的jQuery仿制品
      const elements = typeof selector === 'string'
        ? document.querySelectorAll(selector)
        : [selector];

      const jQueryObject = {
        elements: elements,
        length: elements.length,

        ready: function(fn) {
          if (document.readyState !== 'loading') {
            fn();
          } else {
            document.addEventListener('DOMContentLoaded', fn);
          }
          return this;
        },

        on: function(event, handler) {
          this.each(function() {
            this.addEventListener(event, handler);
          });
          return this;
        },

        each: function(callback) {
          for (let i = 0; i < this.elements.length; i++) {
            callback.call(this.elements[i], i, this.elements[i]);
          }
          return this;
        },

        addClass: function(className) {
          this.each(function() {
            this.classList.add(className);
          });
          return this;
        },

        removeClass: function(className) {
          this.each(function() {
            this.classList.remove(className);
          });
          return this;
        },

        toggleClass: function(className) {
          this.each(function() {
            this.classList.toggle(className);
          });
          return this;
        },

        attr: function(attr, value) {
          if (value === undefined) {
            return this.elements[0] ? this.elements[0].getAttribute(attr) : null;
          }
          this.each(function() {
            this.setAttribute(attr, value);
          });
          return this;
        },

        html: function(content) {
          if (content === undefined) {
            return this.elements[0] ? this.elements[0].innerHTML : "";
          }
          this.each(function() {
            this.innerHTML = content;
          });
          return this;
        },

        text: function(content) {
          if (content === undefined) {
            return this.elements[0] ? this.elements[0].textContent : "";
          }
          this.each(function() {
            this.textContent = content;
          });
          return this;
        },

        show: function() {
          this.each(function() {
            this.style.display = '';
          });
          return this;
        },

        hide: function() {
          this.each(function() {
            this.style.display = 'none';
          });
          return this;
        },

        ajax: function(options) {
          console.warn('jQuery.ajax()的简化版本已调用');
          const xhr = new XMLHttpRequest();
          xhr.open(options.type || 'GET', options.url, true);

          if (options.contentType) {
            xhr.setRequestHeader('Content-Type', options.contentType);
          }

          xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
              if (options.success) {
                let response = xhr.responseText;
                if (options.dataType === 'json') {
                  try {
                    response = JSON.parse(response);
                  } catch (e) {
                    console.error('JSON解析错误', e);
                  }
                }
                options.success(response, xhr.statusText, xhr);
              }
            } else if (options.error) {
              options.error(xhr, xhr.statusText);
            }
          };

          xhr.onerror = function() {
            if (options.error) {
              options.error(xhr, xhr.statusText);
            }
          };

          xhr.send(options.data);

          return {
            done: function(callback) {
              xhr.addEventListener('load', function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                  callback(xhr.responseText, xhr.statusText, xhr);
                }
              });
              return this;
            },
            fail: function(callback) {
              xhr.addEventListener('error', function() {
                callback(xhr, xhr.statusText);
              });
              return this;
            }
          };
        }
      };

      // 使基本属性访问可用
      for (let i = 0; i < elements.length; i++) {
        jQueryObject[i] = elements[i];
      }

      return jQueryObject;
    };

    // 添加静态方法
    window.jQuery.each = function(obj, callback) {
      if (Array.isArray(obj)) {
        for (let i = 0; i < obj.length; i++) {
          callback.call(obj[i], i, obj[i]);
        }
      } else {
        for (let key in obj) {
          if (obj.hasOwnProperty(key)) {
            callback.call(obj[key], key, obj[key]);
          }
        }
      }
      return obj;
    };

    window.jQuery.ajax = window.jQuery().ajax;

    console.log('已加载紧急jQuery替代品');
  }

  // 加载紧急Bootstrap替代品
  function loadEmergencyBootstrap() {
    if (window.bootstrap) return;

    window.bootstrap = {
      Collapse: function(element, options) {
        this.element = typeof element === 'string' ? document.querySelector(element) : element;
        this.options = options || {};

        this.toggle = function() {
          if (this.element.classList.contains('show')) {
            this.hide();
          } else {
            this.show();
          }
        };

        this.show = function() {
          this.element.classList.add('show');
          const trigger = document.querySelector(`[data-bs-target="#${this.element.id}"], [href="#${this.element.id}"]`);
          if (trigger) {
            trigger.setAttribute('aria-expanded', 'true');
          }
        };

        this.hide = function() {
          this.element.classList.remove('show');
          const trigger = document.querySelector(`[data-bs-target="#${this.element.id}"], [href="#${this.element.id}"]`);
          if (trigger) {
            trigger.setAttribute('aria-expanded', 'false');
          }
        };
      },

      Tooltip: function(element, options) {
        this.element = typeof element === 'string' ? document.querySelector(element) : element;
        this.options = options || {};

        this.show = function() { console.log('Tooltip.show() 被调用'); };
        this.hide = function() { console.log('Tooltip.hide() 被调用'); };
      },

      Popover: function(element, options) {
        this.element = typeof element === 'string' ? document.querySelector(element) : element;
        this.options = options || {};

        this.show = function() { console.log('Popover.show() 被调用'); };
        this.hide = function() { console.log('Popover.hide() 被调用'); };
      }
    };

    // 初始化事件处理
    document.addEventListener('click', function(event) {
      const target = event.target;

      // 处理折叠组件
      if (target.hasAttribute('data-bs-toggle') &&
          target.getAttribute('data-bs-toggle') === 'collapse') {
        event.preventDefault();

        const selector = target.getAttribute('data-bs-target') ||
                        target.getAttribute('href');

        if (selector) {
          const content = document.querySelector(selector);
          if (content) {
            const collapse = new bootstrap.Collapse(content);
            collapse.toggle();
          }
        }
      }
    });

    console.log('已加载紧急Bootstrap替代品');
  }

  // 加载紧急Chart.js替代品
  function loadEmergencyChart() {
    console.log('加载Chart.js紧急替代品');

    // 如果Chart.js已加载，不需要替代
    if (window.Chart) {
      console.log('Chart.js已加载，无需替代');
      return;
    }

    // 创建Chart.js替代品
    window.Chart = function(ctx, config) {
      console.log('使用Chart.js替代品创建图表');

      // 使用NineCats命名空间存储画布引用，避免变量名冲突
      if (!window.NineCats.chartCanvases) {
        window.NineCats.chartCanvases = [];
      }

      // 存储画布引用
      const canvasIndex = window.NineCats.chartCanvases.length;
      window.NineCats.chartCanvases.push(ctx.canvas);

      this.canvasIndex = canvasIndex;
      this.ctx = ctx;
      this.config = config || {};
      this.data = config?.data || {};
      this.options = config?.options || {};
      this.type = config?.type || 'line';

      // 渲染函数
      this.render = function() {
        console.log('Chart.render() 被调用');
        if (this.ctx && this.ctx.canvas) {
          // 简单渲染示例
          this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
          this.ctx.font = '14px Arial';
          this.ctx.fillStyle = '#666';
          this.ctx.textAlign = 'center';
          this.ctx.fillText('图表加载中...', this.ctx.canvas.width / 2, this.ctx.canvas.height / 2);
        }
      };

      // 更新方法
      this.update = function() {
        console.log('Chart.update() 被调用');
        this.render();
      };

      // 销毁方法
      this.destroy = function() {
        console.log('Chart.destroy() 被调用');
        // 清空画布
        if (this.ctx && this.ctx.canvas) {
          this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        }

        // 从存储中移除
        if (window.NineCats.chartCanvases[this.canvasIndex]) {
          window.NineCats.chartCanvases[this.canvasIndex] = null;
        }

        return true; // 返回成功标志
      };

      // 立即渲染
      this.render();

      return this;
    };

    // 添加静态属性
    window.Chart.defaults = {
      global: {
        responsive: true,
        maintainAspectRatio: true
      }
    };

    console.log('已加载紧急Chart.js替代品');
  }

  // 尝试从多个CDN加载资源
  function loadFromMultipleCDNs(resourceType, urls) {
    let loaded = false;

    urls.forEach(function(url, index) {
      setTimeout(function() {
        if (!loaded) {
          if (resourceType === 'css') {
            loadCSS(url);
          } else if (resourceType === 'js') {
            loadJS(url);
          }
        }
      }, index * 100); // 延迟加载，避免同时请求
    });
  }

  // 加载CSS
  function loadCSS(url) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = url;

    link.onload = function() {
      console.log(`CSS加载成功: ${url}`);
    };

    link.onerror = function() {
      console.warn(`CSS加载失败: ${url}`);
    };

    document.head.appendChild(link);
  }

  // 加载JS
  function loadJS(url) {
    const script = document.createElement('script');
    script.src = url;
    script.async = false;

    script.onload = function() {
      console.log(`脚本加载成功: ${url}`);
    };

    script.onerror = function() {
      console.warn(`脚本加载失败: ${url}`);
    };

    document.head.appendChild(script);
  }

  // 预定义资源CDN地址
  const resourceCDNs = {
    bootstrap_css: [
      'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
      'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css',
      'https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/css/bootstrap.min.css'
    ],
    fontawesome_css: [
      '/static/css/fontawesome.min.css',  // 首先尝试本地版本
      'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css',
      'https://use.fontawesome.com/releases/v5.15.3/css/all.min.css',
      'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.3/css/all.min.css'
    ],
    jquery: [
      'https://code.jquery.com/jquery-3.6.4.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js',
      'https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js'
    ],
    bootstrap_js: [
      '/static/js/bootstrap.min.js',  // 首先尝试本地版本
      '/static/js/lib/bootstrap.bundle.min.js',  // 然后尝试bundle版本
      'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js',
      'https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.bundle.min.js'
    ],
    chart_js: [
      '/static/js/lib/chart.min.js',  // 首先尝试本地lib文件夹中的版本
      '/static/js/chart.min.js',  // 然后尝试js根目录的版本
      'https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.3.0/chart.umd.min.js'
    ]
  };

  // 尝试从CDN加载资源
  function tryLoadFromCDNs() {
    // 检查是否需要加载各种资源
    if (!document.querySelector('link[href*="bootstrap"]')) {
      loadFromMultipleCDNs('css', resourceCDNs.bootstrap_css);
    }

    // 检查是否需要加载Font Awesome
    if (!document.querySelector('link[href*="fontawesome"]') && !document.querySelector('link[href*="font-awesome"]')) {
      console.log('未找到Font Awesome CSS，尝试加载');
      loadFromMultipleCDNs('css', resourceCDNs.fontawesome_css);
    }

    if (typeof window.jQuery === 'undefined') {
      loadFromMultipleCDNs('js', resourceCDNs.jquery);
    }

    if (typeof window.bootstrap === 'undefined') {
      loadFromMultipleCDNs('js', resourceCDNs.bootstrap_js);
    }

    if (typeof window.Chart === 'undefined') {
      loadFromMultipleCDNs('js', resourceCDNs.chart_js);
    }
  }

  // 清理无用的日志消息
  function setupLogFiltering() {
    // 要忽略的消息模式
    const ignoredPatterns = [
      '未设置小说ID',
      '找不到控制台内部元素',
      'Cannot read properties of null'
    ];

    // 保存原始console方法
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    // 重写console.error
    console.error = function() {
      const message = Array.from(arguments).join(' ');

      // 检查是否匹配任何忽略模式
      const shouldIgnore = ignoredPatterns.some(pattern =>
        message.includes(pattern)
      );

      if (shouldIgnore) {
        // 只在debug模式下显示
        if (window.DEBUG_MODE) {
          originalConsoleError.apply(console, arguments);
        }
        return;
      }

      // 正常显示其他错误
      originalConsoleError.apply(console, arguments);
    };

    // 重写console.warn
    console.warn = function() {
      const message = Array.from(arguments).join(' ');

      // 检查是否匹配任何忽略模式
      const shouldIgnore = ignoredPatterns.some(pattern =>
        message.includes(pattern)
      );

      if (shouldIgnore) {
        // 只在debug模式下显示
        if (window.DEBUG_MODE) {
          originalConsoleWarn.apply(console, arguments);
        }
        return;
      }

      // 正常显示其他警告
      originalConsoleWarn.apply(console, arguments);
    };
  }

  // 在页面加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      checkResources();
      tryLoadFromCDNs();
      setupLogFiltering();
    });
  } else {
    checkResources();
    tryLoadFromCDNs();
    setupLogFiltering();
  }

  // 最后一次尝试
  window.addEventListener('load', function() {
    setTimeout(function() {
      checkResources();
    }, 2000);
  });

  // 导出到全局命名空间
  window.EmergencyResourceLoader = {
    checkResources: checkResources,
    loadEmergencyCSS: loadEmergencyCSS,
    loadEmergencyJQuery: loadEmergencyJQuery,
    loadEmergencyBootstrap: loadEmergencyBootstrap,
    loadEmergencyChart: loadEmergencyChart,
    tryLoadFromCDNs: tryLoadFromCDNs
  };

  console.log('应急资源加载器设置完成');
})();