/**
 * 标识符错误修复脚本
 * 作用：专门处理"Uncaught SyntaxError: Failed to execute 'replaceChild' on 'Node': Identifier"错误
 */

(function() {
    // 保存原始的replaceChild和appendChild方法
    const originalReplaceChild = Node.prototype.replaceChild;
    const originalAppendChild = Node.prototype.appendChild;
    
    // 识别并处理包含$标识符的内容
    function sanitizeNodeContent(node) {
        if (node && node.nodeType === Node.TEXT_NODE && node.textContent) {
            // 检查文本节点是否包含可能导致问题的$标识符
            if (node.textContent.includes('$')) {
                // 复制一个新的文本节点，但替换$为转义形式
                const sanitizedText = node.textContent
                    .replace(/\$(?=\{)/g, '\\$')  // 转义${ 为 \${
                    .replace(/([^\\])\$/g, '$1\\$'); // 转义 $ 为 \$
                
                const newNode = document.createTextNode(sanitizedText);
                return newNode;
            }
        } else if (node && node.nodeType === Node.ELEMENT_NODE) {
            // 处理元素节点
            try {
                if (node.tagName.toLowerCase() === 'script') {
                    // 特别处理脚本节点的内容
                    const scriptContent = node.textContent;
                    if (scriptContent && scriptContent.includes('$')) {
                        const sanitizedContent = scriptContent
                            .replace(/\$(?=\{)/g, '\\$')
                            .replace(/([^\\])\$/g, '$1\\$');
                        
                        // 创建一个新的脚本元素
                        const newScript = document.createElement('script');
                        newScript.textContent = sanitizedContent;
                        
                        // 复制所有属性
                        Array.from(node.attributes).forEach(attr => {
                            newScript.setAttribute(attr.name, attr.value);
                        });
                        
                        return newScript;
                    }
                }
            } catch (e) {
                console.log('标识符错误修复：处理元素节点时出错', e.message);
            }
        }
        
        // 如果没有需要修复的，返回原始节点
        return node;
    }
    
    // 替换replaceChild方法
    Node.prototype.replaceChild = function(newChild, oldChild) {
        try {
            // 尝试直接替换
            return originalReplaceChild.call(this, newChild, oldChild);
        } catch (e) {
            // 如果出错，检查是否为标识符错误
            if (e.message && e.message.includes('Identifier')) {
                console.log('标识符错误修复：处理replaceChild错误', e.message);
                
                // 尝试清理新节点内容
                const sanitizedNewChild = sanitizeNodeContent(newChild);
                
                try {
                    // 再次尝试替换
                    return originalReplaceChild.call(this, sanitizedNewChild, oldChild);
                } catch (innerError) {
                    console.log('标识符错误修复：二次处理replaceChild仍然失败', innerError.message);
                    
                    // 作为最后手段，尝试移除旧节点并添加新节点
                    try {
                        this.removeChild(oldChild);
                        return originalAppendChild.call(this, sanitizedNewChild);
                    } catch (finalError) {
                        console.log('标识符错误修复：最终尝试也失败', finalError.message);
                        // 返回旧节点，避免操作完全失败
                        return oldChild;
                    }
                }
            } else {
                // 其他类型的错误，继续抛出
                throw e;
            }
        }
    };
    
    // 替换appendChild方法
    Node.prototype.appendChild = function(newChild) {
        try {
            // 尝试直接添加
            return originalAppendChild.call(this, newChild);
        } catch (e) {
            // 如果出错，检查是否为标识符错误
            if (e.message && e.message.includes('Identifier')) {
                console.log('标识符错误修复：处理appendChild错误', e.message);
                
                // 尝试清理新节点内容
                const sanitizedNewChild = sanitizeNodeContent(newChild);
                
                try {
                    // 再次尝试添加
                    return originalAppendChild.call(this, sanitizedNewChild);
                } catch (innerError) {
                    console.log('标识符错误修复：二次处理appendChild仍然失败', innerError.message);
                    
                    // 作为最后手段，创建一个空的文本节点作为占位符
                    console.log('标识符错误修复：添加一个空文本节点作为占位符');
                    return originalAppendChild.call(this, document.createTextNode(''));
                }
            } else {
                // 其他类型的错误，继续抛出
                throw e;
            }
        }
    };
    
    // 替换insertBefore方法
    const originalInsertBefore = Node.prototype.insertBefore;
    Node.prototype.insertBefore = function(newChild, refChild) {
        try {
            // 尝试直接插入
            return originalInsertBefore.call(this, newChild, refChild);
        } catch (e) {
            // 如果出错，检查是否为标识符错误
            if (e.message && e.message.includes('Identifier')) {
                console.log('标识符错误修复：处理insertBefore错误', e.message);
                
                // 尝试清理新节点内容
                const sanitizedNewChild = sanitizeNodeContent(newChild);
                
                try {
                    // 再次尝试插入
                    return originalInsertBefore.call(this, sanitizedNewChild, refChild);
                } catch (innerError) {
                    console.log('标识符错误修复：二次处理insertBefore仍然失败', innerError.message);
                    
                    // 作为最后手段，尝试使用appendChild
                    console.log('标识符错误修复：尝试使用appendChild代替');
                    return originalAppendChild.call(this, sanitizedNewChild);
                }
            } else {
                // 其他类型的错误，继续抛出
                throw e;
            }
        }
    };
    
    console.log('标识符错误修复脚本已加载');
})(); 