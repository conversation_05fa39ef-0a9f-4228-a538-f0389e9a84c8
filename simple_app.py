"""
简单的Flask应用，用于测试
"""
from flask import Flask, render_template, send_from_directory
import os

app = Flask(__name__, 
            template_folder=os.path.join('src', 'web', 'templates'),
            static_folder=os.path.join('src', 'web', 'static'))

@app.route('/')
def index():
    """首页"""
    return render_template('simple_index.html')

@app.route('/test')
def test():
    """测试页面"""
    return render_template('simple_test.html')

@app.route('/direct-static/<path:filename>')
def direct_static(filename):
    """直接提供静态文件"""
    return send_from_directory(app.static_folder, filename)

if __name__ == '__main__':
    print("启动简单Flask应用...")
    print(f"模板目录: {app.template_folder}")
    print(f"静态文件目录: {app.static_folder}")
    app.run(host='0.0.0.0', port=5001, debug=True)
