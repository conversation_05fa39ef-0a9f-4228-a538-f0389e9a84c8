"""
修复novels表结构 - 添加缺失的is_analyzed列
"""
import os
import sys
import logging
import sqlite3
from datetime import datetime

# 配置日志
log_filename = f"fix_novels_schema_v2_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def backup_database():
    """备份数据库"""
    try:
        backup_filename = f"novels_backup_schema_v2_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        import shutil
        shutil.copy2("novels.db", backup_filename)
        logger.info(f"数据库已备份为 {backup_filename}")
        return True
    except Exception as e:
        logger.error(f"备份数据库失败: {str(e)}")
        return False

def export_novels_data():
    """导出novels表数据"""
    try:
        conn = sqlite3.connect("novels.db")
        cursor = conn.cursor()
        
        # 获取novels表的列名
        cursor.execute("PRAGMA table_info(novels)")
        columns = [row[1] for row in cursor.fetchall()]
        logger.info(f"当前列名: {columns}")
        
        # 获取所有数据
        cursor.execute("SELECT * FROM novels")
        data = cursor.fetchall()
        logger.info(f"导出了 {len(data)} 条记录")
        
        conn.close()
        return columns, data
    except Exception as e:
        logger.error(f"导出数据失败: {str(e)}")
        return [], []

def recreate_novels_table(columns, data):
    """重新创建novels表并导入数据"""
    try:
        conn = sqlite3.connect("novels.db")
        cursor = conn.cursor()
        
        # 创建临时表，添加is_analyzed列
        logger.info("创建临时表...")
        cursor.execute("""
        CREATE TABLE novels_new (
            id INTEGER NOT NULL,
            title VARCHAR(255) NOT NULL,
            author VARCHAR(255),
            content TEXT NOT NULL,
            file_path VARCHAR(255),
            word_count INTEGER,
            novel_metadata JSON,
            created_at DATETIME,
            updated_at DATETIME,
            file_name VARCHAR(255),
            is_analyzed BOOLEAN DEFAULT 0,
            PRIMARY KEY (id)
        )
        """)
        
        # 标记关键列
        has_is_analyzed = 'is_analyzed' in columns
        
        # 导入数据到临时表
        logger.info("导入数据到临时表...")
        
        if has_is_analyzed:
            # 如果已有is_analyzed列，直接复制
            placeholders = ", ".join(["?"] * len(columns))
            insert_sql = f"INSERT INTO novels_new ({', '.join(columns)}) VALUES ({placeholders})"
            
            for row in data:
                cursor.execute(insert_sql, row)
        else:
            # 如果没有is_analyzed列，需要添加默认值0
            new_columns = columns + ['is_analyzed']
            placeholders = ", ".join(["?"] * len(columns)) + ", 0"
            insert_sql = f"INSERT INTO novels_new ({', '.join(new_columns)}) VALUES ({placeholders})"
            
            for row in data:
                cursor.execute(insert_sql, row)
        
        conn.commit()
        
        # 删除原表，重命名新表
        logger.info("替换原表...")
        cursor.execute("DROP TABLE novels")
        cursor.execute("ALTER TABLE novels_new RENAME TO novels")
        
        conn.commit()
        conn.close()
        
        logger.info("novels表结构已修复，添加了is_analyzed列")
        return True
    except Exception as e:
        logger.error(f"重建表失败: {str(e)}")
        try:
            conn.rollback()
            conn.close()
        except:
            pass
        return False

def verify_table_structure():
    """验证表结构是否正确"""
    try:
        conn = sqlite3.connect("novels.db")
        cursor = conn.cursor()
        
        # 检查novels表的列名
        cursor.execute("PRAGMA table_info(novels)")
        columns = [row[1] for row in cursor.fetchall()]
        logger.info(f"修复后列名: {columns}")
        
        # 检查是否有is_analyzed列
        if 'is_analyzed' in columns:
            logger.info("验证成功：is_analyzed列已添加")
            success = True
        else:
            logger.error("验证失败：is_analyzed列不存在")
            success = False
        
        conn.close()
        return success
    except Exception as e:
        logger.error(f"验证表结构失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("========== 开始修复novels表结构（添加is_analyzed列）==========")
    
    # 备份数据库
    if not backup_database():
        logger.error("备份数据库失败，终止操作")
        return
    
    # 导出数据
    columns, data = export_novels_data()
    if not columns or not data:
        logger.error("导出数据失败，终止操作")
        return
    
    # 重建表
    if recreate_novels_table(columns, data):
        # 验证修复
        if verify_table_structure():
            print("\n✓ novels表结构修复完成！已添加is_analyzed列")
            print("✓ 请尝试重新使用九猫小说分析系统")
        else:
            print("\n✗ novels表结构验证失败，请查看日志")
            print(f"✗ 日志文件: {log_filename}")
    else:
        print("\n✗ novels表结构修复失败")
        print(f"✗ 请查看日志: {log_filename}")
    
    logger.info("========== 修复novels表结构结束 ==========")

if __name__ == "__main__":
    main() 