"""
测试直接同步API
"""
import sys
import json
import requests

def test_direct_sync_api(novel_id, dimension=None):
    """
    测试直接同步API
    
    Args:
        novel_id: 小说ID
        dimension: 分析维度，如果为None则同步所有维度
    """
    # 构建请求数据
    data = {
        "dimension": dimension
    }
    
    # 发送请求
    url = f"http://localhost:5002/api/novel/{novel_id}/sync_to_chapters"
    print(f"发送请求到: {url}")
    print(f"请求数据: {data}")
    
    try:
        response = requests.post(url, json=data)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get("success"):
                    print("测试成功: API返回成功")
                    return True
                else:
                    print(f"测试失败: API返回错误: {result.get('error')}")
                    return False
            except Exception as e:
                print(f"测试失败: 无法解析JSON响应: {str(e)}")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"测试失败: 响应状态码不是200: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
    except Exception as e:
        print(f"测试失败: 请求出错: {str(e)}")
        return False

if __name__ == "__main__":
    # 从命令行参数获取小说ID和维度
    novel_id = int(sys.argv[1]) if len(sys.argv) > 1 else 40
    dimension = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 测试直接同步API
    success = test_direct_sync_api(novel_id, dimension)
    
    # 退出代码
    sys.exit(0 if success else 1)
