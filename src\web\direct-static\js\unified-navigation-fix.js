/**
 * 九猫 - 统一导航修复脚本 (直接静态版本)
 * 解决所有页面导航和跳转问题的综合解决方案
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._unifiedNavigationFixLoaded) {
        console.log('[统一导航修复] 脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._unifiedNavigationFixLoaded = true;
    
    console.log('[统一导航修复] 统一导航修复脚本已加载 - 版本1.0.0 (直接静态版本)');
    
    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalWindowLocation = window.location;
    
    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[统一导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    function safeError(message) {
        try {
            originalConsoleError.call(console, '[统一导航修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 获取当前页面类型
    function getPageType() {
        const path = window.location.pathname;
        
        if (/^\/novel\/\d+\/?$/.test(path)) {
            return 'novel_detail';
        } else if (/^\/novel\/\d+\/chapters\/?$/.test(path)) {
            return 'chapter_list';
        } else if (/^\/novel\/\d+\/chapter\/\d+\/?$/.test(path)) {
            return 'chapter_detail';
        } else if (/^\/novel\/\d+\/analysis\/\w+\/?$/.test(path)) {
            return 'dimension_detail';
        } else if (path === '/' || path === '/index' || path === '/home') {
            return 'home';
        } else {
            return 'other';
        }
    }
    
    // 获取当前小说ID
    function getCurrentNovelId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
            
            // 从页面元素中获取
            const novelContainer = document.querySelector('[data-novel-id]');
            if (novelContainer && novelContainer.dataset.novelId) {
                return novelContainer.dataset.novelId;
            }
            
            // 从全局变量中获取
            if (window.novelId) {
                return window.novelId;
            }
            
            if (window.novelIdFromTemplate) {
                return window.novelIdFromTemplate;
            }
            
            return null;
        } catch (e) {
            safeError('获取小说ID时出错: ' + e.message);
            return null;
        }
    }
    
    // 修复所有导航链接
    function fixAllNavigationLinks() {
        safeLog('修复所有导航链接');
        
        const novelId = getCurrentNovelId();
        if (!novelId) {
            safeLog('无法获取小说ID，跳过导航链接修复');
            return;
        }
        
        safeLog('当前小说ID: ' + novelId);
        
        // 修复所有链接
        fixAllLinks(novelId);
        
        // 修复所有按钮
        fixAllButtons(novelId);
        
        // 修复导航栏
        fixNavigation(novelId);
        
        // 修复面包屑导航
        fixBreadcrumbs(novelId);
    }
    
    // 修复所有链接
    function fixAllLinks(novelId) {
        safeLog('修复所有链接');
        
        const links = document.querySelectorAll('a');
        safeLog('找到 ' + links.length + ' 个链接');
        
        links.forEach(link => {
            // 跳过已经修复过的链接
            if (link.__unifiedLinkFixed) {
                return;
            }
            
            // 标记链接已修复
            link.__unifiedLinkFixed = true;
            
            // 获取链接文本和href
            const text = link.textContent ? link.textContent.trim().toLowerCase() : '';
            const href = link.getAttribute('href');
            
            // 修复章节分析链接
            if (text.includes('章节') && (text.includes('分析') || text.includes('列表'))) {
                const newHref = '/novel/' + novelId + '/chapters';
                safeLog('修复章节分析链接: ' + (href || '无') + ' -> ' + newHref);
                
                // 设置正确的href
                link.href = newHref;
                
                // 设置点击事件处理
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    safeLog('点击章节分析链接，跳转到: ' + newHref);
                    
                    // 使用setTimeout确保在所有事件处理完成后跳转
                    setTimeout(function() {
                        window.location.href = newHref;
                    }, 0);
                    
                    return false;
                }, true);
            }
            
            // 修复维度分析链接
            else if (link.hasAttribute('data-dimension')) {
                const dimension = link.getAttribute('data-dimension');
                if (dimension) {
                    const newHref = '/novel/' + novelId + '/analysis/' + dimension;
                    safeLog('修复维度分析链接: ' + (href || '无') + ' -> ' + newHref);
                    
                    // 设置正确的href
                    link.href = newHref;
                    
                    // 设置点击事件处理
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        safeLog('点击维度分析链接，跳转到: ' + newHref);
                        
                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = newHref;
                        }, 0);
                        
                        return false;
                    }, true);
                }
            }
        });
    }
    
    // 修复所有按钮
    function fixAllButtons(novelId) {
        safeLog('修复所有按钮');
        
        const buttons = document.querySelectorAll('button.btn, a.btn, .btn');
        safeLog('找到 ' + buttons.length + ' 个按钮');
        
        buttons.forEach(button => {
            // 跳过已经修复过的按钮
            if (button.__unifiedButtonFixed) {
                return;
            }
            
            // 标记按钮已修复
            button.__unifiedButtonFixed = true;
            
            // 获取按钮文本
            const text = button.textContent ? button.textContent.trim().toLowerCase() : '';
            
            // 修复章节分析按钮
            if (text.includes('章节') && (text.includes('分析') || text.includes('列表'))) {
                const newHref = '/novel/' + novelId + '/chapters';
                safeLog('修复章节分析按钮: ' + text);
                
                // 如果是链接，设置href属性
                if (button.tagName === 'A') {
                    button.href = newHref;
                }
                
                // 移除所有现有的点击事件处理函数
                button.onclick = null;
                
                // 设置新的点击事件处理函数
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    safeLog('点击章节分析按钮，跳转到: ' + newHref);
                    
                    // 使用setTimeout确保在所有事件处理完成后跳转
                    setTimeout(function() {
                        window.location.href = newHref;
                    }, 0);
                    
                    return false;
                }, true);
            }
            
            // 修复维度分析按钮
            else if (button.hasAttribute('data-dimension')) {
                const dimension = button.getAttribute('data-dimension');
                if (dimension) {
                    const newHref = '/novel/' + novelId + '/analysis/' + dimension;
                    safeLog('修复维度分析按钮: ' + text);
                    
                    // 如果是链接，设置href属性
                    if (button.tagName === 'A') {
                        button.href = newHref;
                    }
                    
                    // 移除所有现有的点击事件处理函数
                    button.onclick = null;
                    
                    // 设置新的点击事件处理函数
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        safeLog('点击维度分析按钮，跳转到: ' + newHref);
                        
                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = newHref;
                        }, 0);
                        
                        return false;
                    }, true);
                }
            }
        });
    }
    
    // 修复导航栏
    function fixNavigation(novelId) {
        // 实现导航栏修复逻辑
    }
    
    // 修复面包屑导航
    function fixBreadcrumbs(novelId) {
        // 实现面包屑导航修复逻辑
    }
    
    // 添加全局点击事件处理
    function addGlobalClickHandler() {
        safeLog('添加全局点击事件处理');
        
        // 如果已经添加过全局点击处理器，不再重复添加
        if (window.__unifiedGlobalHandlerAdded) {
            return;
        }
        
        // 获取当前小说ID
        const novelId = getCurrentNovelId();
        if (!novelId) {
            safeError('无法获取小说ID，无法添加全局点击事件处理');
            return;
        }
        
        // 添加全局点击事件处理
        document.addEventListener('click', function(e) {
            // 向上查找最近的按钮或链接
            let target = e.target;
            while (target && target !== document) {
                // 检查是否是章节分析按钮
                if ((target.tagName === 'A' || target.tagName === 'BUTTON' || target.classList.contains('btn')) &&
                    target.textContent &&
                    target.textContent.toLowerCase().includes('章节') &&
                    (target.textContent.toLowerCase().includes('分析') || target.textContent.toLowerCase().includes('列表'))) {
                    
                    // 阻止默认行为和事件冒泡
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 记录点击
                    safeLog('通过全局处理器捕获章节分析按钮点击，跳转到: /novel/' + novelId + '/chapters');
                    
                    // 使用setTimeout确保在所有事件处理完成后跳转
                    setTimeout(function() {
                        window.location.href = '/novel/' + novelId + '/chapters';
                    }, 0);
                    
                    return false;
                }
                
                target = target.parentElement;
            }
        }, true);
        
        window.__unifiedGlobalHandlerAdded = true;
        safeLog('已添加全局点击事件处理');
    }
    
    // 初始化函数
    function initialize() {
        safeLog('初始化统一导航修复');
        
        // 修复所有导航链接
        fixAllNavigationLinks();
        
        // 添加全局点击事件处理
        addGlobalClickHandler();
        
        // 监听DOM变化
        observeDOMChanges();
        
        safeLog('统一导航修复初始化完成');
    }
    
    // 监听DOM变化
    function observeDOMChanges() {
        try {
            // 创建MutationObserver
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // 检查是否有新添加的节点
                    if (mutation.addedNodes.length > 0) {
                        // 延迟执行修复，确保DOM完全更新
                        setTimeout(function() {
                            fixAllNavigationLinks();
                        }, 100);
                    }
                });
            });
            
            // 配置观察选项
            const config = { childList: true, subtree: true };
            
            // 开始观察
            observer.observe(document.body, config);
            
            safeLog('已开始监听DOM变化');
        } catch (e) {
            safeError('监听DOM变化时出错: ' + e.message);
        }
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }
    
    // 导出API，方便调试和手动调用
    window.unifiedNavigationFix = {
        initialize: initialize,
        fixAllNavigationLinks: fixAllNavigationLinks,
        getCurrentNovelId: getCurrentNovelId
    };
})();
