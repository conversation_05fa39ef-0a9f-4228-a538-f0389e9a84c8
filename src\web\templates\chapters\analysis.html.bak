{% extends "base.html" %}

{% block title %}{{ novel.title }} - 第{{ chapter.chapter_number }}章 - {{ dimension_name }}分析{% endblock %}

{% block head %}
{{ super() }}
<!-- 章节分析详情页面专用修复脚本 -->
<script>
// 立即执行函数，避免污染全局命名空间
(function() {
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[章节分析详情页面修复] 初始化');

        // 修复所有返回链接
        const backLinks = document.querySelectorAll('.breadcrumb a');
        backLinks.forEach(function(link) {
            // 获取原始href
            const originalHref = link.getAttribute('href');

            // 确保链接正确
            link.addEventListener('click', function(e) {
                console.log('[章节分析详情页面修复] 点击返回链接: ' + originalHref);
            });
        });
    });
})();
</script>

<!-- 章节分析结果修复脚本 -->
<script src="{{ url_for('static', filename='js/chapter-analysis-result-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-analysis-result-fix.js';" crossorigin="anonymous"></script>

<!-- 推理过程加载器脚本 -->
<script src="{{ url_for('static', filename='js/reasoning-content-loader.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-loader.js';" crossorigin="anonymous"></script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">小说列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('chapter.list_chapters', novel_id=novel.id) }}">章节列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('chapter.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}">第{{ chapter.chapter_number }}章</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ dimension_name }}分析</li>
        </ol>
    </nav>

    <div class="row">
        <!-- 左侧：章节内容 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h2>{{ chapter.title or '第' + chapter.chapter_number|string + '章' }}</h2>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p><strong>章节编号：</strong>{{ chapter.chapter_number }}</p>
                        <p><strong>字数：</strong>{{ chapter.word_count }}</p>
                    </div>
                    <div class="chapter-content">
                        {% for paragraph in chapter.content.split('\n') %}
                            {% if paragraph.strip() %}
                                <p>{{ paragraph }}</p>
                            {% else %}
                                <br>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：分析结果 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h2>{{ dimension_name }}分析结果</h2>
                </div>
                <div class="card-body">
                    <div class="analysis-result">
                        <!-- 分析结果内容 -->
                        <div class="analysis-content markdown-content" data-dimension="{{ dimension }}">
                            {{ result.content|markdown }}
                        </div>

                        <!-- 推理过程 -->
                        <div class="reasoning-section mt-4">
                            <h4>推理过程</h4>
                            <div class="reasoning-controls">
                                <button id="toggleReasoningBtn" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-expand-alt me-1"></i>展开/收起
                                </button>
                            </div>
                            
                            <!-- 简化版推理过程 -->
                            <div id="reasoningContentCollapsed" class="reasoning-content-collapsed">
                                <div class="alert alert-info">点击"展开/收起"按钮查看完整推理过程</div>
                            </div>
                            
                            <!-- 完整推理过程 -->
                            <div id="reasoningContentFull" class="reasoning-content-full" style="display: none;">
                                <div id="reasoningContent" class="reasoning-container my-3" 
                                     data-reasoning-container="true"
                                     data-novel-id="{{ novel.id }}" 
                                     data-dimension="{{ dimension }}">
                                    <!-- 推理内容将由JavaScript加载 -->
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="sr-only">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载推理过程，请稍候...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分析元数据 -->
                        {% if result.analysis_metadata %}
                        <div class="analysis-metadata mt-4">
                            <h4>分析元数据</h4>
                            <div id="analysis-metadata" data-metadata="{{ result.analysis_metadata|tojson }}">
                                <pre class="metadata-json">{{ result.analysis_metadata|tojson(indent=2) }}</pre>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 分析日志 -->
                        {% if result.analysis_logs %}
                        <div class="analysis-logs mt-4">
                            <h4>分析日志</h4>
                            <div class="logs-container">
                                {% for log in result.analysis_logs %}
                                <div class="log-entry {{ log.level }}">
                                    <span class="log-timestamp">{{ log.timestamp }}</span>
                                    <span class="log-level">{{ log.level }}</span>
                                    <span class="log-message">{{ log.message }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- 隐藏的数据元素，用于存储分析结果 -->
                        <div id="analysis-data" style="display: none;"
                             data-novel-id="{{ novel.id }}"
                             data-chapter-id="{{ chapter.id }}"
                             data-dimension="{{ dimension }}">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .chapter-content {
        max-height: 500px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 5px;
    }

    .analysis-content {
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 5px;
        background-color: #f9f9f9;
    }

    .reasoning-container {
        max-height: 600px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 5px;
        background-color: #f5f5f5;
    }

    .reasoning-text {
        white-space: pre-wrap;
        word-break: break-word;
        font-family: monospace;
        font-size: 0.9em;
        line-height: 1.5;
    }

    .reasoning-controls {
        margin-bottom: 10px;
        display: flex;
        justify-content: flex-end;
    }

    .metadata-json {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 5px;
    }

    .logs-container {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 5px;
    }

    .log-entry {
        margin-bottom: 5px;
        padding: 5px;
        border-radius: 3px;
    }

    .log-entry.info {
        background-color: #e8f4f8;
    }

    .log-entry.warning {
        background-color: #fff3cd;
    }

    .log-entry.error {
        background-color: #f8d7da;
    }

    .log-timestamp {
        color: #666;
        margin-right: 10px;
    }

    .log-level {
        font-weight: bold;
        margin-right: 10px;
    }

    .log-message {
        word-break: break-word;
    }
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- 分析结果修复脚本 -->
<script src="{{ url_for('static', filename='js/analysis-result-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-result-fix.js';" crossorigin="anonymous"></script>

<!-- 章节分析结果修复脚本 -->
<script src="{{ url_for('static', filename='js/chapter-analysis-result-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-analysis-result-fix.js';" crossorigin="anonymous"></script>

<script>
// 在页面加载完成后手动触发修复
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行，确保所有脚本都已加载
    setTimeout(function() {
        console.log('页面加载完成，手动触发章节分析结果修复');
        if (window.chapterAnalysisResultFix && window.chapterAnalysisResultFix.checkAndFixAnalysisResult) {
            window.chapterAnalysisResultFix.checkAndFixAnalysisResult();
        }
        
        // 初始化推理过程功能
        if (window.reasoningContentLoader && window.reasoningContentLoader.initReasoningContent) {
            window.reasoningContentLoader.initReasoningContent('{{ novel.id }}', '{{ dimension }}');
        }
    }, 1000);
});
</script>
{% endblock %}
