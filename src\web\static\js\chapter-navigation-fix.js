/**
 * 九猫 - 章节导航修复脚本
 * 专门修复点击小说详情页的章节按钮跳转到首页的问题
 * 版本: 1.0.0
 */

(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._chapterNavigationFixLoaded) {
        console.log('[章节导航修复] 脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._chapterNavigationFixLoaded = true;
    
    console.log('[章节导航修复] 脚本已加载 - 版本1.0.0');
    
    // 保存原始方法，用于安全操作
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[章节导航修复] ' + message);
        } catch (e) {
            // 忽略日志错误
        }
    }
    
    function safeError(message) {
        try {
            originalConsoleError.call(console, '[章节导航修复] ' + message);
        } catch (e) {
            // 忽略日志错误
        }
    }
    
    // 初始化函数
    function initialize() {
        safeLog('开始初始化');
        
        // 获取当前小说ID
        const novelId = getCurrentNovelId();
        if (!novelId) {
            safeLog('未能获取小说ID，将使用通用修复');
        } else {
            safeLog('当前小说ID: ' + novelId);
        }
        
        // 修复所有章节导航按钮
        fixAllChapterNavigationButtons(novelId);
        
        // 监听DOM变化，修复新添加的按钮
        observeDOMChanges(novelId);
        
        // 设置全局点击拦截器
        setupGlobalClickInterceptor(novelId);
        
        safeLog('初始化完成');
    }
    
    // 获取当前小说ID
    function getCurrentNovelId() {
        try {
            // 从URL获取
            const urlMatch = window.location.pathname.match(/\/novel\/(\d+)/);
            if (urlMatch && urlMatch[1]) {
                return urlMatch[1];
            }
            
            // 从DOM元素获取
            const novelContainer = document.getElementById('novel-container');
            if (novelContainer && novelContainer.dataset.novelId) {
                return novelContainer.dataset.novelId;
            }
            
            // 从全局变量获取
            if (window.novelId) {
                return window.novelId;
            }
            
            if (window.novelIdFromTemplate) {
                return window.novelIdFromTemplate;
            }
            
            // 从表单action获取
            const analyzeForm = document.getElementById('analyze-form');
            if (analyzeForm && analyzeForm.getAttribute('data-novel-id')) {
                return analyzeForm.getAttribute('data-novel-id');
            }
            
            // 查找带有data-novel-id属性的元素
            const elementWithNovelId = document.querySelector('[data-novel-id]');
            if (elementWithNovelId) {
                return elementWithNovelId.getAttribute('data-novel-id');
            }
            
            return null;
        } catch (e) {
            safeError('获取小说ID出错: ' + e.message);
            return null;
        }
    }
    
    // 修复所有章节导航按钮
    function fixAllChapterNavigationButtons(novelId) {
        safeLog('开始修复所有章节导航按钮');
        
        // 查找所有可能的章节导航按钮
        const buttons = document.querySelectorAll('a, button');
        let fixedCount = 0;
        
        buttons.forEach(btn => {
            if (fixChapterNavigationButton(btn, novelId)) {
                fixedCount++;
            }
        });
        
        safeLog(`共修复了 ${fixedCount} 个章节导航按钮`);
    }
    
    // 修复单个章节导航按钮
    function fixChapterNavigationButton(btn, novelId) {
        try {
            // 检查是否是章节导航按钮
            const text = btn.textContent ? btn.textContent.trim().toLowerCase() : '';
            if (!text) return false;
            
            const isChapterButton = (
                text.includes('章节') && 
                (text.includes('分析') || text.includes('列表') || text.includes('汇总'))
            );
            
            if (!isChapterButton) return false;
            
            safeLog(`找到章节导航按钮: ${text}`);
            
            // 根据按钮文本确定正确的链接
            let targetUrl;
            if (text.includes('汇总')) {
                targetUrl = novelId ? `/novel/${novelId}/chapters/summary` : '/chapters/summary';
            } else {
                targetUrl = novelId ? `/novel/${novelId}/chapters` : '/chapters';
            }
            
            // 修复链接
            if (btn.tagName === 'A') {
                // 保存原始链接用于日志
                const originalHref = btn.getAttribute('href');
                btn.href = targetUrl;
                safeLog(`修复链接: ${originalHref || '无'} -> ${targetUrl}`);
            }
            
            // 添加点击事件处理
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                safeLog(`拦截点击事件，将跳转到: ${targetUrl}`);
                
                // 确保在事件循环的下一个周期执行跳转，避免潜在干扰
                setTimeout(() => {
                    window.location.href = targetUrl;
                }, 10);
                
                return false;
            }, true); // 使用捕获，确保在其他事件处理之前执行
            
            // 标记已修复
            btn.setAttribute('data-chapter-nav-fixed', 'true');
            return true;
        } catch (e) {
            safeError(`修复章节导航按钮出错: ${e.message}`);
            return false;
        }
    }
    
    // 监听DOM变化，修复新添加的按钮
    function observeDOMChanges(novelId) {
        try {
            const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(node => {
                            // 检查添加的节点是否为元素节点
                            if (node.nodeType === 1) {
                                // 如果是按钮或链接，直接修复
                                if (node.tagName === 'A' || node.tagName === 'BUTTON') {
                                    fixChapterNavigationButton(node, novelId);
                                }
                                
                                // 如果是其他元素，检查其子元素
                                const buttons = node.querySelectorAll('a, button');
                                buttons.forEach(btn => {
                                    fixChapterNavigationButton(btn, novelId);
                                });
                            }
                        });
                    }
                });
            });
            
            observer.observe(document, {
                childList: true,
                subtree: true
            });
            
            safeLog('已设置DOM变化监听');
        } catch (e) {
            safeError(`设置DOM变化监听出错: ${e.message}`);
        }
    }
    
    // 设置全局点击拦截器
    function setupGlobalClickInterceptor(novelId) {
        try {
            // 如果已设置全局点击拦截器，不重复设置
            if (window._chapterNavClickInterceptorSet) return;
            
            document.addEventListener('click', function(e) {
                let target = e.target;
                
                // 向上遍历DOM树，寻找章节导航按钮
                while (target && target !== document) {
                    // 如果已经修复过，不再处理
                    if (target.getAttribute('data-chapter-nav-fixed') === 'true') {
                        break;
                    }
                    
                    // 检查是否是章节导航按钮
                    const text = target.textContent ? target.textContent.trim().toLowerCase() : '';
                    const isChapterButton = (
                        text.includes('章节') && 
                        (text.includes('分析') || text.includes('列表') || text.includes('汇总'))
                    );
                    
                    if (isChapterButton && (target.tagName === 'A' || target.tagName === 'BUTTON')) {
                        safeLog(`全局拦截器捕获到章节导航按钮点击: ${text}`);
                        
                        // 确定正确的链接
                        let targetUrl;
                        if (text.includes('汇总')) {
                            targetUrl = novelId ? `/novel/${novelId}/chapters/summary` : '/chapters/summary';
                        } else {
                            targetUrl = novelId ? `/novel/${novelId}/chapters` : '/chapters';
                        }
                        
                        e.preventDefault();
                        e.stopPropagation();
                        
                        safeLog(`跳转到: ${targetUrl}`);
                        window.location.href = targetUrl;
                        break;
                    }
                    
                    target = target.parentElement;
                }
            }, true); // 使用捕获，确保在其他事件处理之前执行
            
            window._chapterNavClickInterceptorSet = true;
            safeLog('已设置全局点击拦截器');
        } catch (e) {
            safeError(`设置全局点击拦截器出错: ${e.message}`);
        }
    }
    
    // 在DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果DOM已加载完成，立即初始化
        initialize();
    }
    
    // 在页面完全加载后再次修复
    window.addEventListener('load', function() {
        safeLog('页面已加载完成，再次修复章节导航按钮');
        const novelId = getCurrentNovelId();
        fixAllChapterNavigationButtons(novelId);
    });
    
    // 导出功能供手动调用
    window.chapterNavigationFix = {
        fix: function() {
            const novelId = getCurrentNovelId();
            fixAllChapterNavigationButtons(novelId);
        },
        getNovelId: getCurrentNovelId
    };
})();
