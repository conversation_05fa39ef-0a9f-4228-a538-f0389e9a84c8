/**
 * 九猫 - 分析维度选择紧急修复脚本
 * 用于修复分析维度选择对话框中缺少维度选项的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[维度选择紧急修复] 脚本加载中...');

    // 维度列表 - 与config.py中的ANALYSIS_DIMENSIONS保持一致
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "chapter_outline", name: "章节大纲" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" },
        { key: "outline_analysis", name: "大纲分析" },
        { key: "popular_tropes", name: "热梗统计" }
    ];

    // 配置
    const CONFIG = {
        debug: true,                 // 是否启用调试模式
        checkInterval: 300,          // 检查间隔（毫秒）
        maxRetries: 10,              // 最大重试次数
        modalOpenDelay: 100,         // 模态框打开后延迟（毫秒）
        dimensionsContainerId: 'dimensions-container' // 维度容器ID
    };

    // 状态
    const STATE = {
        initialized: false,          // 是否已初始化
        retryCount: 0,               // 重试计数
        fixedModals: new Set(),      // 已修复的模态框
        observers: []                // MutationObserver实例
    };

    // 安全日志函数
    function safeLog(message, level = 'info') {
        if (CONFIG.debug) {
            const prefix = '[维度选择紧急修复]';
            try {
                switch (level) {
                    case 'error':
                        console.error(`${prefix} ${message}`);
                        break;
                    case 'warn':
                        console.warn(`${prefix} ${message}`);
                        break;
                    default:
                        console.log(`${prefix} ${message}`);
                }
            } catch (e) {
                // 忽略日志错误
            }
        }
    }

    // 查找分析模态框
    function findAnalyzeModal() {
        try {
            // 方法1：通过ID查找
            const modal = document.getElementById('analyzeModal');
            if (modal) {
                safeLog('通过ID找到分析模态框');
                return modal;
            }

            // 方法2：查找显示的模态框
            const visibleModal = document.querySelector('.modal.show, .modal[style*="display: block"]');
            if (visibleModal) {
                const title = visibleModal.querySelector('.modal-title');
                if (title && title.textContent && title.textContent.includes('选择分析维度')) {
                    safeLog('找到显示中的分析模态框');
                    return visibleModal;
                }
            }

            // 方法3：查找所有模态框标题
            const titles = document.querySelectorAll('.modal-title');
            for (let i = 0; i < titles.length; i++) {
                if (titles[i].textContent && titles[i].textContent.includes('选择分析维度')) {
                    const modal = titles[i].closest('.modal');
                    if (modal) {
                        safeLog('通过标题找到分析模态框');
                        return modal;
                    }
                }
            }

            safeLog('找不到分析模态框', 'warn');
            return null;
        } catch (e) {
            safeLog(`查找分析模态框时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 紧急修复维度选择对话框
    function emergencyFixDimensionSelection() {
        try {
            safeLog('开始紧急修复维度选择对话框');

            // 查找模态框
            const modal = findAnalyzeModal();
            if (!modal) {
                safeLog('找不到分析模态框，无法修复', 'warn');
                return false;
            }

            // 检查是否已修复
            if (STATE.fixedModals.has(modal)) {
                safeLog('该模态框已修复，跳过');
                return true;
            }

            // 查找模态框内容区域
            const modalBody = modal.querySelector('.modal-body');
            if (!modalBody) {
                safeLog('找不到模态框内容区域', 'warn');
                return false;
            }

            // 查找全选选项
            const selectAllCheckbox = modalBody.querySelector('#select-all-dimensions');
            if (!selectAllCheckbox) {
                safeLog('找不到全选选项', 'warn');
                return false;
            }

            // 查找分隔线
            const hr = modalBody.querySelector('hr');
            if (!hr) {
                safeLog('找不到分隔线', 'warn');
                return false;
            }

            // 检查是否已有维度选项
            const existingDimensions = modalBody.querySelectorAll('.dimension-checkbox, [name="dimensions"]');
            if (existingDimensions.length > 0) {
                safeLog(`已存在 ${existingDimensions.length} 个维度选项，无需修复`);
                STATE.fixedModals.add(modal);
                return true;
            }

            // 创建维度选项容器
            const dimensionsContainer = document.createElement('div');
            dimensionsContainer.id = CONFIG.dimensionsContainerId;
            dimensionsContainer.className = 'mb-3';

            // 添加所有维度选项
            DIMENSIONS.forEach((dimension, index) => {
                // 创建新的选择项
                const checkboxContainer = document.createElement('div');
                checkboxContainer.className = 'form-check mb-2';

                const checkbox = document.createElement('input');
                checkbox.className = 'form-check-input dimension-checkbox';
                checkbox.type = 'checkbox';
                checkbox.name = 'dimensions';
                checkbox.value = dimension.key;
                checkbox.id = `dimension-${index}`;
                checkbox.checked = true; // 默认选中

                const label = document.createElement('label');
                label.className = 'form-check-label';
                label.setAttribute('for', `dimension-${index}`);
                label.textContent = dimension.name;

                // 添加到容器中
                checkboxContainer.appendChild(checkbox);
                checkboxContainer.appendChild(label);
                dimensionsContainer.appendChild(checkboxContainer);
            });

            // 将维度选项容器插入到分隔线后面
            hr.parentNode.insertBefore(dimensionsContainer, hr.nextSibling);

            safeLog(`成功添加了 ${DIMENSIONS.length} 个维度选项`);

            // 设置全选选项的事件处理
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = dimensionsContainer.querySelectorAll('.dimension-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // 标记为已修复
            STATE.fixedModals.add(modal);
            return true;
        } catch (e) {
            safeLog(`紧急修复维度选择对话框时出错: ${e.message}`, 'error');
            return false;
        }
    }

    // 监听模态框打开事件
    function setupModalListeners() {
        try {
            safeLog('设置模态框监听器');

            // 方法1：使用jQuery监听模态框显示事件
            if (typeof $ !== 'undefined') {
                $(document).on('shown.bs.modal', '#analyzeModal', function() {
                    safeLog('检测到模态框打开 (jQuery)');
                    setTimeout(emergencyFixDimensionSelection, CONFIG.modalOpenDelay);
                });
                safeLog('已设置jQuery模态框监听器');
            }

            // 方法2：使用原生JavaScript监听分析按钮点击
            document.addEventListener('click', function(event) {
                if (event.target.matches('[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]') ||
                    event.target.closest('[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]')) {
                    safeLog('检测到分析按钮点击');
                    setTimeout(function() {
                        emergencyFixDimensionSelection();
                    }, CONFIG.modalOpenDelay + 200);
                }
            });
            safeLog('已设置原生JS按钮监听器');

            // 方法3：使用MutationObserver监听模态框显示
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && 
                        mutation.attributeName === 'class' && 
                        mutation.target.id === 'analyzeModal' && 
                        mutation.target.classList.contains('show')) {
                        safeLog('检测到模态框打开 (MutationObserver)');
                        setTimeout(emergencyFixDimensionSelection, CONFIG.modalOpenDelay);
                    }
                });
            });

            const analyzeModal = document.getElementById('analyzeModal');
            if (analyzeModal) {
                observer.observe(analyzeModal, { attributes: true });
                STATE.observers.push(observer);
                safeLog('已设置MutationObserver模态框监听器');
            }

            // 方法4：使用MutationObserver监听DOM变化
            const bodyObserver = new MutationObserver(function(mutations) {
                // 检查是否有模态框显示
                const modal = document.querySelector('.modal.show, .modal[style*="display: block"]');
                if (modal) {
                    const title = modal.querySelector('.modal-title');
                    if (title && title.textContent && title.textContent.includes('选择分析维度')) {
                        safeLog('检测到分析模态框显示 (DOM变化)');
                        setTimeout(emergencyFixDimensionSelection, CONFIG.modalOpenDelay);
                    }
                }
            });

            bodyObserver.observe(document.body, { 
                childList: true, 
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'style']
            });
            STATE.observers.push(bodyObserver);
            safeLog('已设置DOM变化监听器');
        } catch (e) {
            safeLog(`设置模态框监听器时出错: ${e.message}`, 'error');
        }
    }

    // 初始化
    function initialize() {
        if (STATE.initialized) {
            return;
        }

        safeLog('初始化维度选择紧急修复脚本');

        // 设置模态框监听器
        setupModalListeners();

        // 立即尝试修复一次
        setTimeout(emergencyFixDimensionSelection, 500);

        // 标记为已初始化
        STATE.initialized = true;
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出到全局命名空间，以便调试
    window.dimensionSelectionEmergencyFix = {
        fixNow: emergencyFixDimensionSelection,
        dimensions: DIMENSIONS,
        state: STATE
    };

    safeLog('脚本加载完成');
})();
