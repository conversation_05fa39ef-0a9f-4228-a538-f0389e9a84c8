"""
分析过程路由模块修复版本，解决SQLAlchemy 2.0兼容性问题。
"""
import logging
import json
from flask import Blueprint, render_template, jsonify, request, abort
from sqlalchemy import desc, case

from src.db.connection import Session
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.analysis_process import AnalysisProcess

logger = logging.getLogger(__name__)

# 创建蓝图
analysis_process_fix_bp = Blueprint('analysis_process_fix', __name__)

@analysis_process_fix_bp.route('/novel/<int:novel_id>/analysis/<dimension>/process/viewer')
def view_analysis_process_viewer(novel_id, dimension):
    """
    查看特定分析维度的详细分析过程（优化版）。
    
    使用分页加载和按需加载内容，减少内存占用。
    
    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            abort(404, description=f"找不到ID为{novel_id}的小说")
            
        # 获取分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()
        if not result:
            abort(404, description=f"找不到维度为{dimension}的分析结果")
            
        # 检查是否有分析过程记录
        process_count = session.query(AnalysisProcess).filter_by(
            novel_id=novel_id, dimension=dimension
        ).count()
        
        if process_count == 0:
            # 如果没有记录详细过程，返回提示信息
            return render_template('error.html',
                                   error_title="未找到分析过程记录",
                                   error_message=f"没有找到小说 '{novel.title}' 维度 '{dimension}' 的详细分析过程记录。\n"
                                               f"这可能是因为该分析是在启用详细过程记录功能之前进行的。\n"
                                               f"请尝试使用 '启动九猫_分析过程完整记录版.vbs' 启动，并重新进行分析。")
        
        # 渲染优化版分析过程查看器
        return render_template('analysis_process_viewer.html',
                               novel=novel,
                               dimension=dimension,
                               process_count=process_count)
    except Exception as e:
        logger.error(f"查看分析过程时出错: {str(e)}", exc_info=True)
        return render_template('error.html',
                               error_title="查看分析过程时出错",
                               error_message=str(e))
    finally:
        session.close()

@analysis_process_fix_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>/process')
def api_get_analysis_processes(novel_id, dimension):
    """
    获取分析过程列表的API。
    
    支持分页、过滤和搜索，用于优化版分析过程查看器。
    
    Args:
        novel_id: 小说ID
        dimension: 分析维度
    """
    session = Session()
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        stage = request.args.get('stage', None)
        search = request.args.get('search', None)
        block_index = request.args.get('block_index', None)

        # 构建查询
        query = session.query(AnalysisProcess).filter_by(
            novel_id=novel_id, dimension=dimension
        )

        # 应用阶段过滤
        if stage and stage != 'all':
            query = query.filter(AnalysisProcess.processing_stage == stage)
        elif block_index is not None:
            try:
                block_index = int(block_index)
                query = query.filter_by(block_index=block_index)
            except ValueError:
                pass

        # 应用搜索过滤
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (AnalysisProcess.input_text.ilike(search_term)) |
                (AnalysisProcess.output_text.ilike(search_term)) |
                (AnalysisProcess.prompt_used.ilike(search_term)) |
                (AnalysisProcess.error_message.ilike(search_term))
            )

        # 获取总数
        total_count = query.count()

        # 应用排序 - 使用SQLAlchemy 2.0兼容的case函数
        stage_order = case(
            {
                "init": 1,
                "chunk_analysis": 2,
                "combine": 3,
                "finalize": 4
            },
            value=AnalysisProcess.processing_stage,
            else_=5
        )
        
        query = query.order_by(
            stage_order,
            AnalysisProcess.block_index,
            AnalysisProcess.stage_index
        )

        # 应用分页
        query = query.offset((page - 1) * limit).limit(limit)

        # 执行查询
        processes = query.all()

        # 获取分析结果元数据
        result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, dimension=dimension
        ).first()

        result_metadata = {}
        if result:
            result_metadata = {
                "created_at": result.created_at.isoformat() if result.created_at else None,
                "processing_time": None
            }

            # 提取处理时间
            if hasattr(result, 'metadata') and result.metadata:
                try:
                    metadata = json.loads(result.metadata)
                    if 'processing_time' in metadata:
                        result_metadata['processing_time'] = metadata['processing_time']
                except:
                    pass

        # 转换为字典表示
        process_dicts = [process.to_dict() for process in processes]

        return jsonify({
            "success": True,
            "processes": process_dicts,
            "count": total_count,
            "page": page,
            "limit": limit,
            "pages": (total_count + limit - 1) // limit,
            "metadata": result_metadata
        })
    except Exception as e:
        logger.error(f"获取分析过程API时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()

@analysis_process_fix_bp.route('/api/novel/<int:novel_id>/analysis/<dimension>/process/<int:process_id>')
def api_get_analysis_process_detail(novel_id, dimension, process_id):
    """
    获取特定分析过程详情的API。

    支持按需加载完整内容，用于优化版分析过程查看器。
    可以通过content_type参数指定要获取的内容类型（input、output、prompt）。

    Args:
        novel_id: 小说ID
        dimension: 分析维度
        process_id: 过程ID
    """
    session = Session()
    try:
        # 获取内容类型参数
        content_type = request.args.get('content_type', None)

        # 获取特定过程记录
        process = session.query(AnalysisProcess).filter_by(
            id=process_id, novel_id=novel_id, dimension=dimension
        ).first()

        if not process:
            return jsonify({
                "success": False,
                "error": "未找到指定的分析过程记录"
            }), 404

        # 转换为字典表示
        process_dict = process.to_dict()

        return jsonify({
            "success": True,
            "process": process_dict
        })
    except Exception as e:
        logger.error(f"获取分析过程详情API时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        session.close()
