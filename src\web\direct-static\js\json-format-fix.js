/**
 * 九猫 - JSON格式错误修复脚本
 * 专门处理JSON格式错误，例如"Unexpected non-whitespace character after JSON"
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._jsonFormatFixLoaded) {
        console.log('JSON格式修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._jsonFormatFixLoaded = true;
    
    console.log('JSON格式修复脚本已加载 - 版本1.0.0');
    
    // 保存原始的JSON.parse方法
    var originalJSONParse = JSON.parse;
    
    // 防止递归调用的标志
    var isFixing = false;
    
    // 重写JSON.parse方法
    JSON.parse = function(text, reviver) {
        // 如果已经在修复过程中，直接使用原始方法避免递归
        if (isFixing) {
            return originalJSONParse.call(JSO<PERSON>, text, reviver);
        }
        
        try {
            // 尝试使用原始方法解析
            return originalJSONParse.call(JSO<PERSON>, text, reviver);
        } catch (e) {
            // 设置修复标志，防止递归
            isFixing = true;
            
            try {
                console.log('JSON.parse错误:', e.message);
                
                // 检查是否是"Unexpected non-whitespace character after JSON"错误
                if (e.message && e.message.includes('Unexpected non-whitespace character after JSON')) {
                    console.log('检测到非法字符错误，尝试修复');
                    
                    // 获取错误位置
                    var posMatch = e.message.match(/position (\d+)/);
                    var pos = posMatch ? parseInt(posMatch[1]) : -1;
                    
                    // 如果能够确定错误位置
                    if (pos >= 0 && pos < text.length) {
                        console.log(`错误位置: ${pos}, 字符: "${text.charAt(pos)}"`);
                        
                        // 尝试修复方法1: 移除错误位置的字符
                        var fixedText1 = text.substring(0, pos) + text.substring(pos + 1);
                        try {
                            console.log('尝试修复方法1: 移除错误位置的字符');
                            var result1 = originalJSONParse.call(JSON, fixedText1, reviver);
                            console.log('修复方法1成功');
                            isFixing = false;
                            return result1;
                        } catch (e1) {
                            console.log('修复方法1失败:', e1.message);
                        }
                        
                        // 尝试修复方法2: 在错误位置前截断JSON
                        try {
                            console.log('尝试修复方法2: 在错误位置前截断JSON');
                            var fixedText2 = text.substring(0, pos);
                            // 确保JSON结构完整
                            if (fixedText2.endsWith('}') || fixedText2.endsWith(']') || 
                                fixedText2.endsWith('"') || fixedText2.endsWith("'") ||
                                /\d$/.test(fixedText2)) {
                                var result2 = originalJSONParse.call(JSON, fixedText2, reviver);
                                console.log('修复方法2成功');
                                isFixing = false;
                                return result2;
                            }
                        } catch (e2) {
                            console.log('修复方法2失败:', e2.message);
                        }
                        
                        // 尝试修复方法3: 移除JSON字符串两端的非法字符
                        try {
                            console.log('尝试修复方法3: 移除JSON字符串两端的非法字符');
                            var trimmedText = text.trim();
                            
                            // 查找JSON的实际起始和结束位置
                            var startPos = trimmedText.indexOf('{');
                            var endPos = trimmedText.lastIndexOf('}');
                            
                            if (startPos >= 0 && endPos > startPos) {
                                var fixedText3 = trimmedText.substring(startPos, endPos + 1);
                                var result3 = originalJSONParse.call(JSON, fixedText3, reviver);
                                console.log('修复方法3成功');
                                isFixing = false;
                                return result3;
                            }
                        } catch (e3) {
                            console.log('修复方法3失败:', e3.message);
                        }
                        
                        // 尝试修复方法4: 对于数组，查找数组的起始和结束位置
                        try {
                            console.log('尝试修复方法4: 查找数组的起始和结束位置');
                            var arrayStartPos = trimmedText.indexOf('[');
                            var arrayEndPos = trimmedText.lastIndexOf(']');
                            
                            if (arrayStartPos >= 0 && arrayEndPos > arrayStartPos) {
                                var fixedText4 = trimmedText.substring(arrayStartPos, arrayEndPos + 1);
                                var result4 = originalJSONParse.call(JSON, fixedText4, reviver);
                                console.log('修复方法4成功');
                                isFixing = false;
                                return result4;
                            }
                        } catch (e4) {
                            console.log('修复方法4失败:', e4.message);
                        }
                    }
                    
                    // 尝试修复方法5: 检查并修复常见的JSON格式问题
                    try {
                        console.log('尝试修复方法5: 检查并修复常见的JSON格式问题');
                        
                        // 移除可能导致问题的BOM标记
                        var fixedText5 = text.replace(/^\uFEFF/, '');
                        
                        // 移除JSON外部的引号（有时JSON字符串被错误地包含在引号中）
                        if ((fixedText5.startsWith('"') && fixedText5.endsWith('"')) || 
                            (fixedText5.startsWith("'") && fixedText5.endsWith("'"))) {
                            fixedText5 = fixedText5.substring(1, fixedText5.length - 1);
                        }
                        
                        // 尝试解析修复后的文本
                        var result5 = originalJSONParse.call(JSON, fixedText5, reviver);
                        console.log('修复方法5成功');
                        isFixing = false;
                        return result5;
                    } catch (e5) {
                        console.log('修复方法5失败:', e5.message);
                    }
                }
                
                // 如果是其他JSON解析错误，检查常见错误
                if (e.message) {
                    // 尝试修复缺少引号的键名
                    try {
                        console.log('尝试修复缺少引号的键名');
                        var fixedText6 = text.replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":');
                        var result6 = originalJSONParse.call(JSON, fixedText6, reviver);
                        console.log('修复缺少引号的键名成功');
                        isFixing = false;
                        return result6;
                    } catch (e6) {
                        console.log('修复缺少引号的键名失败:', e6.message);
                    }
                    
                    // 尝试修复单引号替换为双引号
                    try {
                        console.log('尝试将单引号替换为双引号');
                        var fixedText7 = text.replace(/'([^']*)'(\s*:)/g, '"$1"$2')
                                             .replace(/:(\s*)'([^']*)'/g, ':$1"$2"');
                        var result7 = originalJSONParse.call(JSON, fixedText7, reviver);
                        console.log('修复单引号替换为双引号成功');
                        isFixing = false;
                        return result7;
                    } catch (e7) {
                        console.log('修复单引号替换为双引号失败:', e7.message);
                    }
                    
                    // 尝试修复尾部逗号
                    try {
                        console.log('尝试修复尾部逗号');
                        var fixedText8 = text.replace(/,(\s*[\}\]])/g, '$1');
                        var result8 = originalJSONParse.call(JSON, fixedText8, reviver);
                        console.log('修复尾部逗号成功');
                        isFixing = false;
                        return result8;
                    } catch (e8) {
                        console.log('修复尾部逗号失败:', e8.message);
                    }
                }
                
                // 如果所有修复尝试都失败，返回空对象
                console.warn('修复失败，返回空对象');
                isFixing = false;
                return {};
            } finally {
                // 无论成功与否，重置修复标志
                isFixing = false;
            }
        }
    };
    
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && 
            event.error.message.includes('Unexpected non-whitespace character after JSON')) {
            console.log('捕获到JSON格式错误，已被处理');
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
    
    console.log('JSON格式修复脚本初始化完成');
})(); 