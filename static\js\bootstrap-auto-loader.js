/**
 * 九猫系统 Bootstrap 自动加载器
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于自动加载 Bootstrap 修复脚本
 */

(function() {
    console.log('[Bootstrap 自动加载器] 初始化...');
    
    // 加载修复脚本
    function loadFixScripts() {
        console.log('[Bootstrap 自动加载器] 加载修复脚本');
        
        // 加载 Bootstrap Bundle 替换脚本
        loadScript('/static/js/replace-bootstrap-bundle.js', function() {
            console.log('[Bootstrap 自动加载器] Bootstrap Bundle 替换脚本加载成功');
        });
        
        // 加载 Bootstrap 自动修复脚本
        loadScript('/static/js/auto-fix-bootstrap.js', function() {
            console.log('[Bootstrap 自动加载器] Bootstrap 自动修复脚本加载成功');
        });
        
        // 加载 Bootstrap Bundle 修复脚本
        loadScript('/static/js/bootstrap-bundle-fix.js', function() {
            console.log('[Bootstrap 自动加载器] Bootstrap Bundle 修复脚本加载成功');
        });
    }
    
    // 加载脚本
    function loadScript(src, callback) {
        // 检查是否已加载
        if (document.querySelector(`script[src="${src}"]`)) {
            console.log('[Bootstrap 自动加载器] 脚本已加载:', src);
            if (callback) callback();
            return;
        }
        
        // 创建脚本元素
        const script = document.createElement('script');
        script.src = src;
        
        // 加载成功回调
        script.onload = function() {
            console.log('[Bootstrap 自动加载器] 脚本加载成功:', src);
            if (callback) callback();
        };
        
        // 加载失败回调
        script.onerror = function() {
            console.error('[Bootstrap 自动加载器] 脚本加载失败:', src);
        };
        
        // 添加到文档
        document.head.appendChild(script);
    }
    
    // 初始化
    function initialize() {
        // 检查页面是否已加载
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('[Bootstrap 自动加载器] 页面已加载，立即加载修复脚本');
            loadFixScripts();
        } else {
            console.log('[Bootstrap 自动加载器] 页面尚未加载，等待 DOMContentLoaded 事件');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('[Bootstrap 自动加载器] DOMContentLoaded 事件触发，加载修复脚本');
                loadFixScripts();
            });
        }
    }
    
    // 执行初始化
    initialize();
    
    console.log('[Bootstrap 自动加载器] 初始化完成');
})();
