/**
 * 九猫 - 维度状态容器修复脚本
 * 修复"找不到主容器，无法添加维度状态"的错误
 * 版本: 1.0.0
 */

(function() {
    console.log('[维度容器修复] 脚本加载中...');

    // 配置参数
    const CONFIG = {
        enableDebug: true,                // 启用调试日志
        containerSelector: '.analysis-section', // 维度状态容器选择器
        fallbackSelector: '#novel-container', // 备用容器选择器
        checkInterval: 1000,              // 检查间隔(ms) - 增加到1秒
        maxChecks: 30                     // 最大检查次数 - 增加到30次
    };

    // 状态变量
    const STATE = {
        checkCount: 0,                    // 检查计数
        isFixActive: false,               // 修复是否激活
        hasFixed: false,                  // 是否已修复
        dimensions: [                     // 所有维度列表及其显示名称
            { key: "language_style", name: "语言风格" },
            { key: "rhythm_pacing", name: "节奏与节奏" },
            { key: "structure", name: "结构分析" },
            { key: "sentence_variation", name: "句式变化" },
            { key: "paragraph_length", name: "段落长度" },
            { key: "perspective_shifts", name: "视角转换" },
            { key: "paragraph_flow", name: "段落流畅度" },
            { key: "novel_characteristics", name: "小说特点" },
            { key: "world_building", name: "世界构建" },
            { key: "chapter_outline", name: "章节大纲" },
            { key: "character_relationships", name: "人物关系" },
            { key: "opening_effectiveness", name: "开篇效果" },
            { key: "climax_pacing", name: "高潮节奏" }
        ]
    };

    // 调试日志
    function debugLog(...args) {
        if (CONFIG.enableDebug) {
            console.log('[维度容器修复]', ...args);
        }
    }

    // 查找维度状态容器
    function findDimensionContainer() {
        // 尝试使用主选择器查找容器
        let container = document.querySelector(CONFIG.containerSelector);

        // 如果找到了主容器，直接返回
        if (container) {
            debugLog('找到主容器(.analysis-section)');
            return container;
        }

        // 如果找不到主容器，尝试使用备用选择器
        debugLog(`找不到主容器(${CONFIG.containerSelector})，尝试使用备用容器(${CONFIG.fallbackSelector})`);
        container = document.querySelector(CONFIG.fallbackSelector);

        // 如果找到了备用容器，在其中创建一个新的维度状态容器
        if (container) {
            debugLog('找到备用容器，创建新的维度状态容器');
            const newContainer = document.createElement('div');
            newContainer.className = 'analysis-section mt-3';
            newContainer.id = 'analysis-section-container';

            // 查找插入位置
            const insertAfter = container.querySelector('.row.mt-4, .row.mt-5, .alert.alert-info.mt-3');
            if (insertAfter) {
                debugLog('在现有元素后插入新容器');
                insertAfter.parentNode.insertBefore(newContainer, insertAfter);
            } else {
                // 尝试找到控制台区域
                const consoleArea = document.querySelector('#analysis-console');
                if (consoleArea) {
                    const consoleParent = consoleArea.parentNode;
                    debugLog('在控制台区域后插入新容器');
                    consoleParent.parentNode.insertBefore(newContainer, consoleParent.nextSibling);
                } else {
                    // 如果找不到特定位置，就添加到备用容器的开头
                    debugLog('在备用容器开头添加新容器');
                    const firstChild = container.firstChild;
                    if (firstChild) {
                        container.insertBefore(newContainer, firstChild);
                    } else {
                        container.appendChild(newContainer);
                    }
                }
            }

            container = newContainer;
        } else {
            // 如果连备用容器都找不到，尝试创建一个新的容器并添加到body
            debugLog('找不到任何容器，创建新容器并添加到body');
            container = document.createElement('div');
            container.className = 'analysis-section mt-3 container';
            container.id = 'analysis-section-container';

            // 尝试找到一个合适的位置插入
            const mainContent = document.querySelector('.container.mt-4');
            if (mainContent) {
                debugLog('在主内容区域添加新容器');
                mainContent.appendChild(container);
            } else {
                debugLog('添加新容器到body');
                document.body.appendChild(container);
            }
        }

        return container;
    }

    // 创建维度状态表格
    function createDimensionStatusTable() {
        const container = findDimensionContainer();
        if (!container) {
            debugLog('找不到维度状态容器，无法创建维度状态表格');
            return false;
        }

        debugLog('创建维度状态表格');

        // 创建表格
        const table = document.createElement('table');
        table.className = 'table table-bordered table-hover';
        table.id = 'dimensions-status-table';

        // 创建表头
        const thead = document.createElement('thead');
        thead.className = 'table-light';
        const headerRow = document.createElement('tr');

        const headers = ['维度', '状态', '进度', '操作'];
        headers.forEach(headerText => {
            const th = document.createElement('th');
            th.textContent = headerText;
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // 创建表格主体
        const tbody = document.createElement('tbody');

        // 获取小说ID
        const novelId = document.querySelector('[data-novel-id]')?.dataset.novelId || '40';

        // 添加维度行
        STATE.dimensions.forEach(dimension => {
            const row = document.createElement('tr');
            row.dataset.dimension = dimension.key;

            // 维度名称
            const nameCell = document.createElement('td');
            nameCell.textContent = dimension.name;
            row.appendChild(nameCell);

            // 状态
            const statusCell = document.createElement('td');
            statusCell.innerHTML = '<span class="badge bg-secondary">未知</span>';
            statusCell.className = 'dimension-status';
            row.appendChild(statusCell);

            // 进度
            const progressCell = document.createElement('td');
            progressCell.innerHTML = `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar" role="progressbar" style="width: 0%;"
                         aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
            `;
            progressCell.className = 'dimension-progress';
            row.appendChild(progressCell);

            // 操作
            const actionCell = document.createElement('td');
            actionCell.innerHTML = `
                <button class="btn btn-sm btn-outline-primary analyze-btn" data-dimension="${dimension.key}" data-novel-id="${novelId}">
                    分析
                </button>
                <a href="/novel/${novelId}/analysis/${dimension.key}" class="btn btn-sm btn-outline-secondary ms-1 view-btn">
                    查看
                </a>
            `;
            actionCell.className = 'dimension-actions';
            row.appendChild(actionCell);

            tbody.appendChild(row);
        });

        table.appendChild(tbody);

        // 添加到容器
        container.innerHTML = ''; // 清空容器
        container.appendChild(table);

        // 添加表格标题
        const tableTitle = document.createElement('h3');
        tableTitle.textContent = '维度分析状态';
        tableTitle.className = 'mb-3';
        container.insertBefore(tableTitle, table);

        debugLog('维度状态表格创建完成');
        return true;
    }

    // 检查并修复维度状态容器
    function checkAndFixDimensionContainer() {
        STATE.checkCount++;

        // 检查是否已经超过最大检查次数
        if (STATE.checkCount > CONFIG.maxChecks) {
            debugLog(`已达到最大检查次数(${CONFIG.maxChecks})，停止检查`);
            return;
        }

        // 检查是否已经有维度状态表格
        const existingTable = document.getElementById('dimensions-status-table');
        if (existingTable) {
            debugLog('已存在维度状态表格，跳过创建');
            STATE.hasFixed = true;

            // 确保表格中的按钮有正确的事件处理器
            attachEventHandlers(existingTable);

            // 触发一次displayAllDimensions函数，确保表格内容被更新
            if (window.displayAllDimensions && typeof window.displayAllDimensions === 'function') {
                debugLog('调用全局displayAllDimensions函数更新表格内容');
                try {
                    window.displayAllDimensions();
                } catch (e) {
                    debugLog('调用displayAllDimensions函数时出错:', e);
                }
            }

            return;
        }

        // 尝试创建维度状态表格
        const success = createDimensionStatusTable();
        if (success) {
            debugLog('维度状态容器修复成功');
            STATE.hasFixed = true;

            // 触发一次displayAllDimensions函数，确保表格内容被更新
            if (window.displayAllDimensions && typeof window.displayAllDimensions === 'function') {
                debugLog('调用全局displayAllDimensions函数更新表格内容');
                try {
                    window.displayAllDimensions();
                } catch (e) {
                    debugLog('调用displayAllDimensions函数时出错:', e);
                }
            }
        } else {
            // 如果失败，稍后再试
            debugLog(`维度状态容器修复失败，${CONFIG.checkInterval}ms后重试`);
            setTimeout(checkAndFixDimensionContainer, CONFIG.checkInterval);
        }
    }

    // 为表格中的按钮添加事件处理器
    function attachEventHandlers(table) {
        if (!table) return;

        debugLog('为表格中的按钮添加事件处理器');

        // 获取所有分析按钮
        const analyzeButtons = table.querySelectorAll('.analyze-btn');
        analyzeButtons.forEach(button => {
            // 避免重复添加事件处理器
            if (button.dataset.handlerAttached) return;

            const dimension = button.dataset.dimension;
            const novelId = button.dataset.novelId;

            button.addEventListener('click', function() {
                debugLog(`点击了维度 ${dimension} 的分析按钮`);
                // 这里可以添加分析按钮的点击处理逻辑
                // 例如，调用API开始分析

                // 标记已添加事件处理器
                button.dataset.handlerAttached = 'true';
            });
        });

        debugLog('事件处理器添加完成');
    }

    // 初始化
    function init() {
        debugLog('初始化维度状态容器修复');
        STATE.isFixActive = true;

        // 添加延迟，确保页面完全加载
        setTimeout(function() {
            debugLog('开始检查并修复维度状态容器');
            checkAndFixDimensionContainer();
        }, 2000); // 延迟2秒执行
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM加载完成，准备初始化');
            setTimeout(init, 1000); // DOM加载完成后再延迟1秒初始化
        });
    } else {
        debugLog('DOM已加载，直接初始化');
        setTimeout(init, 1000); // 直接延迟1秒初始化
    }

    // 添加一个全局函数，方便手动触发修复
    window.fixDimensionContainer = function() {
        debugLog('手动触发维度状态容器修复');
        STATE.checkCount = 0; // 重置检查计数
        STATE.hasFixed = false; // 重置修复状态
        checkAndFixDimensionContainer();
        return '维度状态容器修复已触发，请查看控制台日志';
    };

    console.log('[维度容器修复] 脚本加载完成');
})();
