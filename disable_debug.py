import os
import sys
import re

def disable_debug_mode():
    """
    禁用所有DEBUG模式的模拟分析，确保使用真实API调用
    """
    # 修改config.py
    with open('config.py', 'r', encoding='utf-8') as f:
        config_content = f.read()
    
    # 确保DEBUG设置为False
    config_content = re.sub(r'DEBUG\s*=\s*True', 'DEBUG = False', config_content)
    config_content = re.sub(r'DEBUG\s*=\s*os\.getenv.*', '# DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")\nDEBUG = False', config_content)
    
    with open('config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    print("已更新config.py，确保DEBUG=False")
    
    # 修改src/api/analysis.py
    analysis_path = os.path.join('src', 'api', 'analysis.py')
    with open(analysis_path, 'r', encoding='utf-8') as f:
        analysis_content = f.read()
    
    # 替换所有DEBUG模式检查的代码块
    pattern = r'if config\.DEBUG:.*?else:'
    replacement = '# 完全禁用DEBUG模式的模拟分析，始终使用真实API调用'
    analysis_content = re.sub(pattern, replacement, analysis_content, flags=re.DOTALL)
    
    # 确保没有其他DEBUG模式检查
    analysis_content = analysis_content.replace('if config.DEBUG:', '# if config.DEBUG:')
    
    with open(analysis_path, 'w', encoding='utf-8') as f:
        f.write(analysis_content)
    print("已更新src/api/analysis.py，禁用所有DEBUG模式检查")
    
    # 修改src/api/deepseek_client.py
    deepseek_path = os.path.join('src', 'api', 'deepseek_client.py')
    with open(deepseek_path, 'r', encoding='utf-8') as f:
        deepseek_content = f.read()
    
    # 替换DeepSeek客户端中的DEBUG模式检查
    pattern = r'if config\.DEBUG:.*?return sample_response'
    replacement = 'if False: # 完全禁用DEBUG模式\n            pass'
    deepseek_content = re.sub(pattern, replacement, deepseek_content, flags=re.DOTALL)
    
    with open(deepseek_path, 'w', encoding='utf-8') as f:
        f.write(deepseek_content)
    print("已更新src/api/deepseek_client.py，禁用所有DEBUG模式检查")
    
    # 修改src/web/app.py
    app_path = os.path.join('src', 'web', 'app.py')
    with open(app_path, 'r', encoding='utf-8') as f:
        app_content = f.read()
    
    # 替换app.py中的DEBUG模式检查
    pattern = r'if config\.DEBUG.*?else:'
    replacement = '# 完全禁用DEBUG模式的模拟分析，始终使用真实API调用'
    app_content = re.sub(pattern, replacement, app_content, flags=re.DOTALL)
    
    with open(app_path, 'w', encoding='utf-8') as f:
        f.write(app_content)
    print("已更新src/web/app.py，禁用所有DEBUG模式检查")
    
    print("所有DEBUG模式的模拟分析已禁用，系统将始终使用真实API调用")

if __name__ == "__main__":
    disable_debug_mode()
