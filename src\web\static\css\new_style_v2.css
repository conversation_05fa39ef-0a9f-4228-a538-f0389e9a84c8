/* 全局样式 - 浅米黄色和淡白色主题 V2 */
:root {
    --primary-color: #e6b422; /* 金黄色主色调 */
    --primary-dark: #c99a17; /* 深金黄色 */
    --primary-light: #f7d26e; /* 浅金黄色 */
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #fffdf5; /* 更淡的米黄色背景 */
    --card-bg: #ffffff; /* 纯白色卡片 */
    --text-color: #4a4a4a;
    --border-color: #f7f0d7; /* 更淡的边框颜色 */
    --shadow-color: rgba(230, 180, 34, 0.05);
    --footer-bg: #fffdf5;
    --transition-speed: 0.3s;
}

/* 深色主题变量 */
body[data-theme="dark"] {
    --primary-color: #e6b422; /* 保持金黄色 */
    --primary-dark: #c99a17;
    --primary-light: #f7d26e;
    --secondary-color: #495057;
    --body-bg: #2d2a20; /* 深棕色背景 */
    --card-bg: #3a3629; /* 深棕黄色卡片 */
    --text-color: #f0e0b2; /* 浅金色文字 */
    --border-color: #6b5c29; /* 深金色边框 */
    --shadow-color: rgba(230, 180, 34, 0.2);
    --footer-bg: #2a2620;
}

body {
    background-color: var(--body-bg);
    color: var(--text-color);
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    transition: background-color var(--transition-speed), color var(--transition-speed);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 1px 6px var(--shadow-color);
    transition: background-color var(--transition-speed);
    background: linear-gradient(to right, var(--primary-color), var(--primary-light)) !important;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
    color: #fff !important;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
}

.nav-link {
    font-weight: 500;
    transition: all 0.2s ease;
    color: rgba(255,255,255,0.9) !important;
}

.nav-link:hover {
    transform: translateY(-2px);
    color: #fff !important;
}

/* 主要内容区域 */
main {
    flex: 1;
    padding-bottom: 2rem;
}

/* 卡片样式 */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.card-header {
    background: linear-gradient(to right, var(--primary-color), var(--primary-light));
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: #fff;
    padding: 1rem 1.25rem;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
}

/* 按钮样式 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1.25rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px var(--shadow-color);
}

.btn-primary {
    background: linear-gradient(to right, var(--primary-color), var(--primary-light));
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-dark);
}

/* 表格样式 */
.table {
    color: var(--text-color);
}

.table thead th {
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
}

/* 分析结果卡片 */
.analysis-card {
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.analysis-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px var(--shadow-color);
}

.analysis-card .card-header {
    padding: 1rem;
    background-color: var(--primary-color);
    color: white;
}

.analysis-content {
    padding: 1.5rem;
}

/* 进度条样式 */
.progress {
    height: 0.8rem;
    border-radius: 0.4rem;
    background-color: var(--border-color);
}

.progress-bar {
    background-color: var(--primary-color);
}

/* 页脚样式 */
.footer {
    background-color: var(--footer-bg);
    border-top: 1px solid var(--border-color);
    padding: 1rem 0;
    margin-top: auto;
    transition: background-color var(--transition-speed);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* 分析维度标签 */
.dimension-badge {
    display: inline-block;
    padding: 0.5em 0.85em;
    font-size: 0.8em;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
}

/* Markdown内容样式 */
.markdown-content {
    line-height: 1.7;
    font-size: 1.05rem;
}

.markdown-content h1, 
.markdown-content h2, 
.markdown-content h3, 
.markdown-content h4, 
.markdown-content h5, 
.markdown-content h6 {
    margin-top: 1.8rem;
    margin-bottom: 1.2rem;
    font-weight: 600;
    color: var(--primary-dark);
}

.markdown-content h1 {
    font-size: 2rem;
    border-bottom: 2px solid var(--primary-light);
    padding-bottom: 0.5rem;
}

.markdown-content h2 {
    font-size: 1.6rem;
    border-bottom: 1px solid var(--primary-light);
    padding-bottom: 0.4rem;
}

.markdown-content p {
    margin-bottom: 1.2rem;
}

.markdown-content ul, 
.markdown-content ol {
    margin-bottom: 1.2rem;
    padding-left: 2rem;
}

.markdown-content li {
    margin-bottom: 0.5rem;
}

.markdown-content blockquote {
    border-left: 4px solid var(--primary-color);
    padding: 0.8rem 1.2rem;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 1.2rem;
    background-color: rgba(230, 180, 34, 0.05);
    border-radius: 0 0.5rem 0.5rem 0;
    color: var(--primary-dark);
    font-style: italic;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 深色主题特定样式 */
.dark-theme .navbar {
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark)) !important;
}

.dark-theme .footer {
    background-color: #2a2620;
    color: #f0e0b2;
}

.dark-theme .card {
    background-color: #3a3629;
    border-color: #6b5c29;
}

.dark-theme .card-header {
    background: linear-gradient(to right, var(--primary-color), #a17e1a);
    color: #fff;
}

.dark-theme .table {
    color: #f0e0b2;
}

.dark-theme .form-control {
    background-color: #2d2a20;
    border-color: #6b5c29;
    color: #f0e0b2;
}

.dark-theme .form-control:focus {
    background-color: #2d2a20;
    color: #f0e0b2;
    border-color: var(--primary-color);
}

.dark-theme .btn-outline-primary {
    color: var(--primary-light);
    border-color: var(--primary-light);
}

.dark-theme .btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: #2d2a20;
}

.dark-theme .list-group-item {
    background-color: #3a3629;
    border-color: #6b5c29;
    color: #f0e0b2;
}

.dark-theme .markdown-content h1,
.dark-theme .markdown-content h2,
.dark-theme .markdown-content h3,
.dark-theme .markdown-content h4,
.dark-theme .markdown-content h5,
.dark-theme .markdown-content h6 {
    color: var(--primary-light);
}

.dark-theme .markdown-content blockquote {
    background-color: rgba(230, 180, 34, 0.1);
    color: #f0e0b2;
}

/* 下拉菜单样式 */
.dimension-dropdown .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
    width: 100%;
    z-index: 1021; /* 确保在其他元素之上 */
}

.dimension-dropdown .dropdown-menu.show {
    display: block !important;
}

/* 维度卡片样式 */
.analysis-dimension-card {
    height: 100%;
    transition: transform 0.2s;
}

.analysis-dimension-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 修复下拉菜单位置问题 */
.dropdown-menu {
    position: absolute;
    transform: translate3d(0px, 38px, 0px);
    top: 0px;
    left: 0px;
    will-change: transform;
}
