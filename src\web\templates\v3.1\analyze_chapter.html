{% extends "v3.1/base.html" %}

{% block title %}分析章节 - {{ chapter.title }} - {{ novel.title }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .analysis-header {
        background: linear-gradient(135deg, #FFF8E1, #FFFCF5);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
        border: 1px solid #E6D7B9;
    }

    .analysis-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .analysis-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        color: #5D4037;
    }

    .analysis-header p {
        position: relative;
        z-index: 1;
        color: #5D4037;
    }

    .btn-primary {
        background-color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-primary:hover {
        background-color: #F57C00;
        border-color: #F57C00;
    }

    .btn-outline-primary {
        color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-outline-primary:hover {
        background-color: #FF8F00;
        border-color: #FF8F00;
        color: white;
    }

    .badge.bg-primary {
        background-color: #FF8F00 !important;
    }

    .badge.bg-secondary {
        background-color: #757575 !important;
    }

    .badge.bg-success {
        background-color: #43A047 !important;
    }

    .badge.bg-warning {
        background-color: #FFB300 !important;
    }

    .badge.bg-info {
        background-color: #29B6F6 !important;
    }

    .analysis-card {
        border: 1px solid #E6D7B9;
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .analysis-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }

    .analysis-card .card-header {
        background-color: #FFF8E1;
        border-bottom: 1px solid #E6D7B9;
        padding: 1rem;
    }

    .analysis-content {
        padding: 1.5rem;
    }

    .dimension-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #FF8F00;
    }

    .dimension-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .progress {
        height: 0.5rem;
        background-color: #E6D7B9;
    }

    .progress-bar {
        background-color: #FF8F00;
    }

    .chapter-navigation {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
    }

    .analysis-meta {
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #E6D7B9;
    }

    .analysis-meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .analysis-meta-item i {
        width: 24px;
        margin-right: 0.5rem;
        color: #FF8F00;
    }

    .analysis-status {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .analysis-status-icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    .analysis-status-icon.completed {
        background-color: #43A047;
    }

    .analysis-status-icon.in-progress {
        background-color: #FFB300;
    }

    .analysis-status-icon.not-started {
        background-color: #757575;
    }

    .analysis-status-text {
        font-weight: 500;
    }

    .analysis-status-text.completed {
        color: #43A047;
    }

    .analysis-status-text.in-progress {
        color: #FFB300;
    }

    .analysis-status-text.not-started {
        color: #757575;
    }

    .dimension-group {
        margin-bottom: 2rem;
    }

    .dimension-group-title {
        font-weight: 600;
        color: #5D4037;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #E6D7B9;
    }
</style>
{% endblock %}

{% block content %}
<!-- 章节分析头部信息 -->
<div class="analysis-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">章节分析</h1>
            <p class="lead mb-2">
                <i class="fas fa-book me-2"></i>{{ novel.title }} - {{ chapter.title }}
                {% if novel.author %}
                <span class="ms-2"><i class="fas fa-user me-1"></i>{{ novel.author }}</span>
                {% endif %}
            </p>
            <p class="mb-0">
                <span class="badge bg-primary me-2">第{{ chapter.chapter_number }}章</span>
                <span class="badge bg-info me-2">{{ chapter.word_count }} 字</span>
                <span class="badge bg-success me-2">{{ chapter.analyzed_dimensions|default([])|length }}/15 维度已分析</span>
            </p>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回章节
                </a>
                <a href="{{ url_for('v3_1.chapters_list', novel_id=novel.id) }}" class="btn btn-outline-primary">
                    <i class="fas fa-list me-1"></i>章节列表
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 章节导航 -->
<div class="chapter-navigation mb-4">
    {% if prev_chapter %}
    <a href="{{ url_for('v3_1.analyze_chapter', novel_id=novel.id, chapter_id=prev_chapter.id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-chevron-left me-1"></i>上一章：{{ prev_chapter.title }}
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        <i class="fas fa-chevron-left me-1"></i>已是第一章
    </button>
    {% endif %}

    {% if next_chapter %}
    <a href="{{ url_for('v3_1.analyze_chapter', novel_id=novel.id, chapter_id=next_chapter.id) }}" class="btn btn-outline-secondary">
        下一章：{{ next_chapter.title }}<i class="fas fa-chevron-right ms-1"></i>
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        已是最后一章<i class="fas fa-chevron-right ms-1"></i>
    </button>
    {% endif %}
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-md-4 mb-4">
        <!-- 章节信息 -->
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>章节信息</h3>
            </div>
            <div class="card-body">
                <div class="analysis-meta">
                    <div class="analysis-meta-item">
                        <i class="fas fa-heading"></i>
                        <span>标题: {{ chapter.title }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-sort-numeric-up"></i>
                        <span>章节编号: {{ chapter.chapter_number }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-font"></i>
                        <span>字数: {{ chapter.word_count }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-chart-pie"></i>
                        <span>分析维度: {{ chapter.analyzed_dimensions|default([])|length }}/15</span>
                    </div>
                </div>

                <h5 class="mt-3 mb-2">分析状态</h5>
                <div class="progress mb-3">
                    <div class="progress-bar" role="progressbar" style="width: {{ (chapter.analyzed_dimensions|default([])|length / 15) * 100 }}%;" aria-valuenow="{{ chapter.analyzed_dimensions|default([])|length }}" aria-valuemin="0" aria-valuemax="15"></div>
                </div>

                <div class="analysis-status">
                    <div class="analysis-status-icon completed"></div>
                    <span class="analysis-status-text completed">已完成: {{ chapter.analyzed_dimensions|default([])|length }}</span>
                </div>
                <div class="analysis-status">
                    <div class="analysis-status-icon in-progress"></div>
                    <span class="analysis-status-text in-progress">进行中: {{ analyzing_dimensions|default([])|length }}</span>
                </div>
                <div class="analysis-status">
                    <div class="analysis-status-icon not-started"></div>
                    <span class="analysis-status-text not-started">未开始: {{ 15 - chapter.analyzed_dimensions|default([])|length - analyzing_dimensions|default([])|length }}</span>
                </div>

                <div class="d-grid gap-2 mt-3">
                    <button id="analyzeAllBtn" class="btn btn-primary">
                        <i class="fas fa-cogs me-1"></i>分析所有维度
                    </button>
                    <button id="cancelAnalysisBtn" class="btn btn-outline-danger" {% if not analyzing_dimensions %}disabled{% endif %}>
                        <i class="fas fa-stop-circle me-1"></i>取消分析
                    </button>
                </div>
            </div>
        </div>

        <!-- 章节内容预览 -->
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-file-alt me-2"></i>章节内容预览</h3>
            </div>
            <div class="card-body">
                <div style="max-height: 300px; overflow-y: auto; white-space: pre-wrap; font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; line-height: 1.8;">
                    {% if chapter.content %}
                        {{ chapter.content[:500] }}
                        {% if chapter.content|length > 500 %}
                        <div class="text-center mt-3">
                            <span class="badge bg-secondary">内容过长，仅显示前500字</span>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5>内容不可用</h5>
                            <p class="text-muted">该章节内容不可用或尚未加载。</p>
                        </div>
                    {% endif %}
                </div>
                <div class="d-grid mt-3">
                    <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>查看完整内容
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- 分析维度列表 -->
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>分析维度</h3>
            </div>
            <div class="card-body">
                <!-- 分组1：结构分析 -->
                <div class="dimension-group">
                    <h4 class="dimension-group-title"><i class="fas fa-sitemap me-2"></i>结构分析</h4>
                    <div class="row">
                        {% for dimension in dimensions if dimension.group == 'structure' %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="analysis-card h-100">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ dimension.name }}</h5>
                                    {% if dimension.key in chapter.analyzed_dimensions|default([]) %}
                                    <span class="badge bg-success dimension-status">已分析</span>
                                    {% elif dimension.key in analyzing_dimensions|default([]) %}
                                    <span class="badge bg-warning dimension-status">分析中</span>
                                    {% else %}
                                    <span class="badge bg-secondary dimension-status">未分析</span>
                                    {% endif %}
                                </div>
                                <div class="card-body text-center">
                                    <i class="{{ dimension.icon }} dimension-icon"></i>
                                    <p class="card-text small">{{ dimension.description }}</p>
                                </div>
                                <div class="card-footer bg-transparent">
                                    {% if dimension.key in chapter.analyzed_dimensions|default([]) %}
                                    <a href="{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-eye me-1"></i>查看分析
                                    </a>
                                    {% elif dimension.key in analyzing_dimensions|default([]) %}
                                    <button class="btn btn-outline-warning w-100" disabled>
                                        <i class="fas fa-spinner fa-spin me-1"></i>分析中...
                                    </button>
                                    {% else %}
                                    <button class="btn btn-primary w-100 start-analysis-btn" data-dimension="{{ dimension.key }}">
                                        <i class="fas fa-cogs me-1"></i>开始分析
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 分组2：内容分析 -->
                <div class="dimension-group">
                    <h4 class="dimension-group-title"><i class="fas fa-file-alt me-2"></i>内容分析</h4>
                    <div class="row">
                        {% for dimension in dimensions if dimension.group == 'content' %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="analysis-card h-100">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ dimension.name }}</h5>
                                    {% if dimension.key in chapter.analyzed_dimensions|default([]) %}
                                    <span class="badge bg-success dimension-status">已分析</span>
                                    {% elif dimension.key in analyzing_dimensions|default([]) %}
                                    <span class="badge bg-warning dimension-status">分析中</span>
                                    {% else %}
                                    <span class="badge bg-secondary dimension-status">未分析</span>
                                    {% endif %}
                                </div>
                                <div class="card-body text-center">
                                    <i class="{{ dimension.icon }} dimension-icon"></i>
                                    <p class="card-text small">{{ dimension.description }}</p>
                                </div>
                                <div class="card-footer bg-transparent">
                                    {% if dimension.key in chapter.analyzed_dimensions|default([]) %}
                                    <a href="{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-eye me-1"></i>查看分析
                                    </a>
                                    {% elif dimension.key in analyzing_dimensions|default([]) %}
                                    <button class="btn btn-outline-warning w-100" disabled>
                                        <i class="fas fa-spinner fa-spin me-1"></i>分析中...
                                    </button>
                                    {% else %}
                                    <button class="btn btn-primary w-100 start-analysis-btn" data-dimension="{{ dimension.key }}">
                                        <i class="fas fa-cogs me-1"></i>开始分析
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 分组3：其他分析 -->
                <div class="dimension-group">
                    <h4 class="dimension-group-title"><i class="fas fa-chart-pie me-2"></i>其他分析</h4>
                    <div class="row">
                        {% for dimension in dimensions if dimension.group == 'other' %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="analysis-card h-100">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ dimension.name }}</h5>
                                    {% if dimension.key in chapter.analyzed_dimensions|default([]) %}
                                    <span class="badge bg-success dimension-status">已分析</span>
                                    {% elif dimension.key in analyzing_dimensions|default([]) %}
                                    <span class="badge bg-warning dimension-status">分析中</span>
                                    {% else %}
                                    <span class="badge bg-secondary dimension-status">未分析</span>
                                    {% endif %}
                                </div>
                                <div class="card-body text-center">
                                    <i class="{{ dimension.icon }} dimension-icon"></i>
                                    <p class="card-text small">{{ dimension.description }}</p>
                                </div>
                                <div class="card-footer bg-transparent">
                                    {% if dimension.key in chapter.analyzed_dimensions|default([]) %}
                                    <a href="{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-eye me-1"></i>查看分析
                                    </a>
                                    {% elif dimension.key in analyzing_dimensions|default([]) %}
                                    <button class="btn btn-outline-warning w-100" disabled>
                                        <i class="fas fa-spinner fa-spin me-1"></i>分析中...
                                    </button>
                                    {% else %}
                                    <button class="btn btn-primary w-100 start-analysis-btn" data-dimension="{{ dimension.key }}">
                                        <i class="fas fa-cogs me-1"></i>开始分析
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 章节导航（底部） -->
<div class="chapter-navigation mt-4">
    {% if prev_chapter %}
    <a href="{{ url_for('v3_1.analyze_chapter', novel_id=novel.id, chapter_id=prev_chapter.id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-chevron-left me-1"></i>上一章：{{ prev_chapter.title }}
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        <i class="fas fa-chevron-left me-1"></i>已是第一章
    </button>
    {% endif %}

    {% if next_chapter %}
    <a href="{{ url_for('v3_1.analyze_chapter', novel_id=novel.id, chapter_id=next_chapter.id) }}" class="btn btn-outline-secondary">
        下一章：{{ next_chapter.title }}<i class="fas fa-chevron-right ms-1"></i>
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        已是最后一章<i class="fas fa-chevron-right ms-1"></i>
    </button>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 开始分析按钮点击事件
        $('.start-analysis-btn').click(function() {
            const dimension = $(this).data('dimension');
            const $btn = $(this);

            if (confirm(`确定要开始分析"${dimension}"维度吗？\n\n分析过程可能需要几分钟时间，请耐心等待。`)) {
                // 显示加载状态
                $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...');
                $btn.prop('disabled', true);
                $btn.closest('.analysis-card').find('.dimension-status').removeClass('bg-secondary').addClass('bg-warning').text('分析中');

                // 发送分析请求
                $.ajax({
                    url: '/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analyze_dimension',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        dimension: dimension
                    }),
                    success: function(response) {
                        if (response.success) {
                            // 跳转到分析页面
                            window.location.href = "{{ url_for('v3_1.analyze_chapter_dimension', novel_id=novel.id, chapter_id=chapter.id) }}?dimension=" + dimension;
                        } else {
                            // 恢复按钮状态
                            $btn.html('<i class="fas fa-cogs me-1"></i>开始分析');
                            $btn.prop('disabled', false);
                            $btn.closest('.analysis-card').find('.dimension-status').removeClass('bg-warning').addClass('bg-secondary').text('未分析');

                            // 显示错误信息
                            alert('开始分析失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 恢复按钮状态
                        $btn.html('<i class="fas fa-cogs me-1"></i>开始分析');
                        $btn.prop('disabled', false);
                        $btn.closest('.analysis-card').find('.dimension-status').removeClass('bg-warning').addClass('bg-secondary').text('未分析');

                        // 显示错误信息
                        alert('开始分析失败: ' + error);
                    }
                });
            }
        });

        // 分析所有维度按钮点击事件
        $('#analyzeAllBtn').click(function() {
            if (confirm('确定要分析所有未分析的维度吗？\n\n这将启动多个分析任务，可能需要较长时间，请耐心等待。')) {
                // 显示加载状态
                const $btn = $(this);
                $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...');
                $btn.prop('disabled', true);

                // 获取所有未分析的维度
                const unanalyzedDimensions = [];
                $('.dimension-status').each(function() {
                    if ($(this).hasClass('bg-secondary')) {
                        const dimension = $(this).closest('.analysis-card').find('.start-analysis-btn').data('dimension');
                        if (dimension) {
                            unanalyzedDimensions.push(dimension);
                        }
                    }
                });

                if (unanalyzedDimensions.length === 0) {
                    alert('没有未分析的维度');
                    $btn.html('<i class="fas fa-cogs me-1"></i>分析所有维度');
                    $btn.prop('disabled', false);
                    return;
                }

                // 发送分析请求
                $.ajax({
                    url: '/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analyze_all_dimensions',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        dimensions: unanalyzedDimensions
                    }),
                    success: function(response) {
                        if (response.success) {
                            // 更新UI
                            unanalyzedDimensions.forEach(dimension => {
                                const $card = $(`.start-analysis-btn[data-dimension="${dimension}"]`).closest('.analysis-card');
                                $card.find('.dimension-status').removeClass('bg-secondary').addClass('bg-warning').text('分析中');
                                $card.find('.start-analysis-btn').html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...').prop('disabled', true);
                            });

                            // 启用取消按钮
                            $('#cancelAnalysisBtn').prop('disabled', false);

                            // 显示成功信息
                            alert(`已开始分析${unanalyzedDimensions.length}个维度，请稍后刷新页面查看结果。`);

                            // 5秒后刷新页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 5000);
                        } else {
                            // 恢复按钮状态
                            $btn.html('<i class="fas fa-cogs me-1"></i>分析所有维度');
                            $btn.prop('disabled', false);

                            // 显示错误信息
                            alert('开始分析失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 恢复按钮状态
                        $btn.html('<i class="fas fa-cogs me-1"></i>分析所有维度');
                        $btn.prop('disabled', false);

                        // 显示错误信息
                        alert('开始分析失败: ' + error);
                    }
                });
            }
        });

        // 取消分析按钮点击事件
        $('#cancelAnalysisBtn').click(function() {
            if (confirm('确定要取消所有正在进行的分析吗？\n\n已完成的部分将保留，但未完成的分析将被取消。')) {
                // 显示加载状态
                const $btn = $(this);
                $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>取消中...');
                $btn.prop('disabled', true);

                // 发送取消请求
                $.ajax({
                    url: '/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/cancel_analysis',
                    type: 'POST',
                    contentType: 'application/json',
                    success: function(response) {
                        if (response.success) {
                            // 更新UI
                            $('.dimension-status.bg-warning').removeClass('bg-warning').addClass('bg-secondary').text('未分析');
                            $('.start-analysis-btn:disabled').html('<i class="fas fa-cogs me-1"></i>开始分析').prop('disabled', false);

                            // 禁用取消按钮
                            $btn.html('<i class="fas fa-stop-circle me-1"></i>取消分析');
                            $btn.prop('disabled', true);

                            // 显示成功信息
                            alert('已取消所有正在进行的分析');

                            // 刷新页面
                            window.location.reload();
                        } else {
                            // 恢复按钮状态
                            $btn.html('<i class="fas fa-stop-circle me-1"></i>取消分析');
                            $btn.prop('disabled', false);

                            // 显示错误信息
                            alert('取消分析失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 恢复按钮状态
                        $btn.html('<i class="fas fa-stop-circle me-1"></i>取消分析');
                        $btn.prop('disabled', false);

                        // 显示错误信息
                        alert('取消分析失败: ' + error);
                    }
                });
            }
        });

        // 键盘导航
        $(document).keydown(function(e) {
            // 左箭头键 - 上一章
            if (e.keyCode === 37) {
                const prevChapterLink = $('a:contains("上一章")').first();
                if (prevChapterLink.length && !prevChapterLink.hasClass('disabled')) {
                    window.location.href = prevChapterLink.attr('href');
                }
            }
            // 右箭头键 - 下一章
            else if (e.keyCode === 39) {
                const nextChapterLink = $('a:contains("下一章")').first();
                if (nextChapterLink.length && !nextChapterLink.hasClass('disabled')) {
                    window.location.href = nextChapterLink.attr('href');
                }
            }
        });

        // 定期检查分析状态
        function checkAnalysisStatus() {
            if ($('.dimension-status.bg-warning').length > 0) {
                $.ajax({
                    url: '/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analysis_status',
                    type: 'GET',
                    success: function(response) {
                        if (response.success) {
                            const analyzedDimensions = response.analyzed_dimensions || [];
                            const analyzingDimensions = response.analyzing_dimensions || [];

                            // 更新UI
                            $('.dimension-status').each(function() {
                                const dimension = $(this).closest('.analysis-card').find('.start-analysis-btn').data('dimension');
                                if (!dimension) return;

                                if (analyzedDimensions.includes(dimension)) {
                                    // 已分析完成
                                    $(this).removeClass('bg-warning bg-secondary').addClass('bg-success').text('已分析');
                                    const $btn = $(this).closest('.analysis-card').find('.start-analysis-btn');
                                    if ($btn.length) {
                                        const viewUrl = "{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=chapter.id) }}?dimension=" + dimension;
                                        $btn.replaceWith(`<a href="${viewUrl}" class="btn btn-outline-primary w-100"><i class="fas fa-eye me-1"></i>查看分析</a>`);
                                    }
                                } else if (analyzingDimensions.includes(dimension)) {
                                    // 分析中
                                    $(this).removeClass('bg-success bg-secondary').addClass('bg-warning').text('分析中');
                                    const $btn = $(this).closest('.analysis-card').find('.start-analysis-btn');
                                    if ($btn.length && !$btn.prop('disabled')) {
                                        $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...').prop('disabled', true);
                                    }
                                }
                            });

                            // 更新进度条
                            const totalDimensions = 15;
                            const completedDimensions = analyzedDimensions.length;
                            const progressPercent = (completedDimensions / totalDimensions) * 100;
                            $('.progress-bar').css('width', progressPercent + '%').attr('aria-valuenow', completedDimensions);

                            // 更新状态文本
                            $('.analysis-status-text.completed').text('已完成: ' + completedDimensions);
                            $('.analysis-status-text.in-progress').text('进行中: ' + analyzingDimensions.length);
                            $('.analysis-status-text.not-started').text('未开始: ' + (totalDimensions - completedDimensions - analyzingDimensions.length));

                            // 如果没有正在分析的维度，禁用取消按钮
                            if (analyzingDimensions.length === 0) {
                                $('#cancelAnalysisBtn').prop('disabled', true);
                            } else {
                                $('#cancelAnalysisBtn').prop('disabled', false);
                            }
                        }
                    }
                });
            }
        }

        // 每5秒检查一次分析状态
        setInterval(checkAnalysisStatus, 5000);
    });
</script>
{% endblock %}