{"name": "npm-pick-manifest", "version": "7.0.1", "description": "Resolves a matching manifest from a package metadata document according to standard npm semver resolution rules.", "main": "./lib", "files": ["bin/", "lib/"], "scripts": {"coverage": "tap", "lint": "eslint \"**/*.js\"", "test": "tap", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/npm-pick-manifest.git"}, "keywords": ["npm", "semver", "package manager"], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"npm-install-checks": "^5.0.0", "npm-normalize-package-bin": "^1.0.1", "npm-package-arg": "^9.0.0", "semver": "^7.3.5"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "tap": "^16.0.1"}, "tap": {"check-coverage": true}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}}