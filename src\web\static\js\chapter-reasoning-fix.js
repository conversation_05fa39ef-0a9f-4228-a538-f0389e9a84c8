/**
 * 九猫 - 章节推理过程修复脚本
 * 用于修复章节推理过程显示问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('章节推理过程修复脚本已加载');

    // 配置
    const CONFIG = {
        // 调试模式
        debug: true,
        // 自动重试次数
        maxRetries: 3,
        // 重试间隔（毫秒）
        retryInterval: 5000,
        // 是否显示详细日志
        verbose: true
    };

    // 日志函数
    function log(message, type = 'info') {
        if (!CONFIG.debug && type !== 'error') return;

        const prefix = '[章节推理修复]';
        switch (type) {
            case 'error':
                console.error(`${prefix} ${message}`);
                break;
            case 'warn':
                console.warn(`${prefix} ${message}`);
                break;
            case 'debug':
                if (CONFIG.verbose) {
                    console.debug(`${prefix} ${message}`);
                }
                break;
            default:
                console.log(`${prefix} ${message}`);
        }
    }

    // 加载章节推理过程
    async function loadChapterReasoning(novelId, chapterId, dimension, containerId = 'reasoningContent') {
        log(`开始加载章节推理过程: novel_id=${novelId}, chapter_id=${chapterId}, dimension=${dimension}`);

        // 获取容器元素
        const container = document.getElementById(containerId);
        if (!container) {
            log(`找不到推理过程容器: ${containerId}`, 'error');
            return;
        }

        // 显示加载中状态
        container.innerHTML = `
            <div class="text-center my-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2">正在加载推理过程，请稍候...</p>
            </div>
        `;

        // 构建API URL
        const apiUrl = `/v3.1/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`;
        log(`请求API: ${apiUrl}`);

        try {
            // 发送请求
            const response = await fetch(apiUrl);
            const data = await response.json();

            if (data.success && data.reasoning_content) {
                // 显示推理过程
                log(`成功获取推理过程，长度: ${data.reasoning_content.length}`);
                renderReasoningContent(container, data.reasoning_content);
            } else {
                // 尝试备用API
                log(`主API未返回推理过程，尝试备用API`, 'warn');
                await tryFallbackApi(novelId, chapterId, dimension, container);
            }
        } catch (error) {
            log(`请求推理过程时出错: ${error.message}`, 'error');
            // 尝试备用API
            await tryFallbackApi(novelId, chapterId, dimension, container);
        }
    }

    // 尝试备用API
    async function tryFallbackApi(novelId, chapterId, dimension, container) {
        // 构建备用API URL
        const fallbackUrl = `/v3.1/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/generate_reasoning`;
        log(`请求备用API: ${fallbackUrl}`);

        try {
            // 发送请求
            const response = await fetch(fallbackUrl);
            const data = await response.json();

            if (data.success && data.reasoning_content) {
                // 显示推理过程
                log(`成功从备用API获取推理过程，长度: ${data.reasoning_content.length}`);
                renderReasoningContent(container, data.reasoning_content);
            } else {
                // 显示错误信息
                log(`备用API也未返回推理过程: ${data.error || '未知错误'}`, 'error');
                showError(container, data.error || '未找到推理过程数据');
            }
        } catch (error) {
            log(`请求备用API时出错: ${error.message}`, 'error');
            showError(container, `加载推理过程时出错: ${error.message}`);
        }
    }

    // 渲染推理过程内容
    function renderReasoningContent(container, content) {
        // 格式化内容
        let formattedContent = content;

        // 如果内容不包含Markdown标记，添加基本格式
        if (!content.includes('#') && !content.includes('**')) {
            formattedContent = `## 推理过程\n\n${content}`;
        }

        // 渲染为HTML
        container.innerHTML = `<pre class="reasoning-text">${formattedContent}</pre>`;

        // 应用代码高亮
        if (typeof hljs !== 'undefined') {
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });
        }
    }

    // 显示错误信息
    function showError(container, message) {
        container.innerHTML = `
            <div class="alert alert-warning">
                <p><i class="fas fa-exclamation-circle"></i> ${message}</p>
                <p class="mt-2">
                    <button class="btn btn-sm btn-outline-primary retry-button">
                        <i class="fas fa-sync-alt"></i> 重试
                    </button>
                </p>
            </div>
        `;

        // 添加重试按钮事件
        const retryButton = container.querySelector('.retry-button');
        if (retryButton) {
            retryButton.addEventListener('click', function() {
                // 获取数据属性
                const novelId = container.getAttribute('data-novel-id');
                const chapterId = container.getAttribute('data-chapter-id');
                const dimension = container.getAttribute('data-dimension');

                // 重新加载
                loadChapterReasoning(novelId, chapterId, dimension, container.id);
            });
        }
    }

    // 修复控制台页面章节分析
    function fixConsoleChapterAnalysis() {
        log('开始修复控制台页面章节分析');

        // 检查是否在控制台页面
        if (window.location.pathname.includes('/v3/console') || window.location.pathname.includes('/console')) {
            log('检测到控制台页面，开始修复章节分析');

            // 修复loadChapterAnalysis函数
            if (typeof window.loadChapterAnalysis === 'function') {
                log('重写loadChapterAnalysis函数');

                // 保存原始函数
                const originalLoadChapterAnalysis = window.loadChapterAnalysis;

                // 重写函数
                window.loadChapterAnalysis = function(templateId, chapterId) {
                    log(`修复的loadChapterAnalysis被调用: 模板ID=${templateId}, 章节ID=${chapterId}`);

                    // 恢复原始的章节分析内容结构，包含推理过程标签页
                    const fixedContainer = `
                        <div class="card">
                            <div class="card-header bg-light">
                                <ul class="nav nav-tabs card-header-tabs" id="chapterAnalysisResultTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="chapter-result-tab" data-bs-toggle="tab" data-bs-target="#chapter-result" type="button" role="tab" aria-controls="chapter-result" aria-selected="true">分析结果</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="chapter-reasoning-tab" data-bs-toggle="tab" data-bs-target="#chapter-reasoning" type="button" role="tab" aria-controls="chapter-reasoning" aria-selected="false">推理过程</button>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content" id="chapterAnalysisResultTabContent">
                                    <div class="tab-pane fade show active" id="chapter-result" role="tabpanel" aria-labelledby="chapter-result-tab">
                                        <div id="chapterAnalysisContent">
                                            <div class="text-center py-3">
                                                <div class="spinner-border text-primary" role="status"></div>
                                                <p class="mt-3">加载中，请稍候...</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="chapter-reasoning" role="tabpanel" aria-labelledby="chapter-reasoning-tab">
                                        <div id="chapterReasoningContent">
                                            <div class="text-center py-3">
                                                <div class="spinner-border text-primary" role="status"></div>
                                                <p class="mt-3">加载中，请稍候...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 更新页面内容
                    const cardContainer = document.querySelector('#chapterAnalysisContent').closest('.card');
                    if (cardContainer) {
                        cardContainer.outerHTML = fixedContainer;
                        log('已替换章节分析内容结构，添加推理过程标签页');
                    }

                    // 调用原始函数
                    originalLoadChapterAnalysis(templateId, chapterId);
                };

                log('loadChapterAnalysis函数重写完成');
            }

            // 监听章节点击事件
            document.addEventListener('click', function(event) {
                if (event.target.classList.contains('chapter-item') ||
                    event.target.closest('.chapter-item')) {
                    log('检测到章节点击事件，确保推理过程标签页存在');
                    setTimeout(ensureChapterReasoningTabExists, 500);
                }
            });

            // 监听章节分析按钮点击事件
            document.addEventListener('click', function(event) {
                if (event.target.classList.contains('view-chapter-analysis-btn') ||
                    event.target.closest('.view-chapter-analysis-btn')) {
                    log('检测到章节分析按钮点击事件，确保推理过程标签页存在');
                    setTimeout(ensureChapterReasoningTabExists, 500);
                }
            });

            // 初始检查
            setTimeout(ensureChapterReasoningTabExists, 1000);
        }
    }

    // 确保推理过程标签页存在
    function ensureChapterReasoningTabExists() {
        // 检查是否已存在推理过程标签页
        const tabList = document.querySelector('#chapterAnalysisResultTab');
        if (tabList && !tabList.querySelector('#chapter-reasoning-tab')) {
            log('推理过程标签页不存在，添加标签页');

            // 添加推理过程标签页
            const tabItem = document.createElement('li');
            tabItem.className = 'nav-item';
            tabItem.setAttribute('role', 'presentation');
            tabItem.innerHTML = `
                <button class="nav-link" id="chapter-reasoning-tab" data-bs-toggle="tab" data-bs-target="#chapter-reasoning" type="button" role="tab" aria-controls="chapter-reasoning" aria-selected="false">推理过程</button>
            `;
            tabList.appendChild(tabItem);

            // 添加推理过程内容面板
            const tabContent = document.querySelector('#chapterAnalysisResultTabContent');
            if (tabContent && !tabContent.querySelector('#chapter-reasoning')) {
                const tabPane = document.createElement('div');
                tabPane.className = 'tab-pane fade';
                tabPane.id = 'chapter-reasoning';
                tabPane.setAttribute('role', 'tabpanel');
                tabPane.setAttribute('aria-labelledby', 'chapter-reasoning-tab');
                tabPane.innerHTML = `
                    <div id="chapterReasoningContent">
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p class="mt-3">加载中，请稍候...</p>
                        </div>
                    </div>
                `;
                tabContent.appendChild(tabPane);
            }

            log('已添加推理过程标签页和内容面板');
        }
    }

    // 初始化
    function init() {
        log('初始化章节推理过程修复脚本');

        // 查找所有推理过程容器
        document.querySelectorAll('[data-reasoning-container="true"]').forEach(container => {
            const novelId = container.getAttribute('data-novel-id');
            const chapterId = container.getAttribute('data-chapter-id');
            const dimension = container.getAttribute('data-dimension');

            if (novelId && dimension) {
                log(`找到推理过程容器: novel_id=${novelId}, chapter_id=${chapterId}, dimension=${dimension}`);

                // 如果容器内容为空或包含"未找到推理过程数据"，加载推理过程
                if (!container.textContent.trim() ||
                    container.textContent.includes('未找到推理过程数据') ||
                    container.textContent.includes('加载中')) {
                    loadChapterReasoning(novelId, chapterId, dimension, container.id);
                }
            }
        });

        // 查找并添加推理过程按钮事件
        document.querySelectorAll('.load-reasoning-btn').forEach(button => {
            button.addEventListener('click', function() {
                const container = document.getElementById(this.getAttribute('data-target'));
                if (container) {
                    const novelId = container.getAttribute('data-novel-id');
                    const chapterId = container.getAttribute('data-chapter-id');
                    const dimension = container.getAttribute('data-dimension');

                    loadChapterReasoning(novelId, chapterId, dimension, container.id);
                }
            });
        });

        // 修复控制台页面章节分析
        fixConsoleChapterAnalysis();
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
