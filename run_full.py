"""
九猫小说分析系统 - 完整版启动脚本
"""

import os
import sys
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境变量和目录"""
    logger.info("正在设置环境...")
    
    # 确保静态文件目录存在
    os.makedirs("src/web/static/js", exist_ok=True)
    os.makedirs("src/web/static/css", exist_ok=True)
    os.makedirs("src/web/static/img", exist_ok=True)
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 设置API密钥环境变量
    os.environ['DEEPSEEK_API_KEY'] = '***********************************'
    os.environ['QIANWEN_API_KEY'] = 'sk-6f3b4c6ad9f64f78b22bed422c5d278d'
    
    logger.info("环境设置完成")

def check_dependencies():
    """检查依赖项"""
    logger.info("正在检查依赖项...")
    
    try:
        import flask
        import sqlalchemy
        logger.info(f"Flask版本: {flask.__version__}")
        logger.info(f"SQLAlchemy版本: {sqlalchemy.__version__}")
    except ImportError as e:
        logger.error(f"缺少依赖项: {str(e)}")
        logger.info("尝试安装依赖项...")
        os.system("pip install flask sqlalchemy psutil")
        logger.info("依赖项安装完成，请重新启动程序")
        sys.exit(1)
    
    logger.info("依赖项检查完成")

def run_application():
    """运行应用程序"""
    logger.info("正在启动九猫小说分析系统...")
    
    # 记录启动时间
    start_time = datetime.now()
    logger.info(f"启动时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示API密钥信息
    deepseek_api_key = os.environ.get('DEEPSEEK_API_KEY', '')
    if deepseek_api_key:
        masked_key = deepseek_api_key[:5] + '...' + deepseek_api_key[-4:]
        print(f"使用DeepSeek R1 API密钥: {masked_key}")
    
    # 导入并运行应用
    try:
        from src.web.app_full import app
        app.run(host='0.0.0.0', port=5001, debug=False)
    except Exception as e:
        logger.error(f"启动应用程序时出错: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    print("=" * 50)
    print("九猫小说分析系统 - 完整版")
    print("=" * 50)
    
    setup_environment()
    check_dependencies()
    run_application()
