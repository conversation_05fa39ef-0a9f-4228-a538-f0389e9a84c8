/**
 * 九猫 - 推理过程样式
 * 用于所有分析维度页面统一显示推理过程
 */

/* 推理过程容器样式 */
.reasoning-content {
    white-space: pre-wrap;
    line-height: 1.6;
    font-family: monospace;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
    overflow-x: auto;
    max-width: 100%;
}

/* 推理文本样式 */
.reasoning-text {
    white-space: pre-wrap;
    font-family: monospace;
    line-height: 1.6;
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    max-width: 100%;
}

/* 折叠状态的推理过程预览 */
.reasoning-content-preview {
    max-height: 150px;
    overflow: hidden;
    position: relative;
}

/* 折叠状态的渐变遮罩 */
.reasoning-content-preview::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: linear-gradient(to bottom, transparent, #f8f9fa);
    pointer-events: none;
}

/* 完整显示的推理过程 */
.reasoning-content-full {
    max-height: none;
    overflow: visible;
}

/* 亮色主题下的强调 */
.reasoning-content em, .reasoning-text em {
    font-style: italic;
    color: #0056b3;
}

/* 亮色主题下的强调 */
.reasoning-content strong, .reasoning-text strong {
    font-weight: bold;
    color: #004085;
}

/* 可能的推理步骤分隔线 */
.reasoning-step-divider {
    border-top: 1px dashed #ccc;
    margin: 15px 0;
}

/* 结构化推理内容样式 */
.reasoning-structured {
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    background-color: #fffdf7; /* 浅米黄色背景 */
    border: 1px solid #e8e0c5;
    border-radius: 8px;
    padding: 0;
    margin: 15px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.reasoning-section {
    margin-bottom: 20px;
    padding: 15px;
}

.reasoning-section:first-child {
    border-bottom: 1px solid #e8e0c5;
    background-color: #f9f5e8; /* 更深的米黄色 */
    border-radius: 8px 8px 0 0;
}

.reasoning-section-title {
    color: #5a4a2f;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8e0c5;
}

.reasoning-section-content {
    line-height: 1.6;
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    color: #333;
    padding: 10px;
}

/* 分析思路文本 */
.reasoning-approach-text {
    white-space: pre-wrap;
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    line-height: 1.6;
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    max-width: 100%;
    color: #333;
}

/* 详细分析文本 */
.reasoning-analysis-text {
    white-space: pre-wrap;
    font-family: monospace;
    line-height: 1.6;
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    max-width: 100%;
    color: #333;
}

/* 深入分析部分 */
.reasoning-detailed-section {
    border-top: 1px solid #e8e0c5;
    margin-top: 20px;
    padding-top: 15px;
    background-color: #f5f9ff; /* 浅蓝色背景，区分于其他部分 */
}

.reasoning-detailed-text {
    white-space: pre-wrap;
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    line-height: 1.6;
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    max-width: 100%;
    color: #333;
    font-size: 1em;
}

/* 章节间联系分析部分 */
.reasoning-connection-section {
    border-top: 1px solid #e8e0c5;
    margin-top: 20px;
    padding-top: 15px;
    background-color: #f9f5ff; /* 浅紫色背景，区分于其他部分 */
}

.reasoning-connection-text {
    white-space: pre-wrap;
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    line-height: 1.6;
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    max-width: 100%;
    color: #333;
    font-size: 1em;
    font-weight: 500; /* 稍微加粗，突出重要性 */
}

/* 原始分析内容部分 */
.reasoning-original-section {
    border-top: 1px dashed #e8e0c5;
    margin-top: 20px;
    padding-top: 15px;
    background-color: #f9f9f9;
}

.reasoning-original-text {
    white-space: pre-wrap;
    font-family: monospace;
    line-height: 1.6;
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    max-width: 100%;
    color: #666;
    font-size: 0.9em;
}

/* 富结构化内容样式 */
.reasoning-structured-rich {
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    background-color: #fffdf7; /* 浅米黄色背景 */
    border: 1px solid #e8e0c5;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.reasoning-rich-title {
    color: #5a4a2f;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8e0c5;
}

.reasoning-rich-subtitle {
    color: #7a6a4f;
    font-weight: 600;
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.reasoning-rich-content {
    margin-bottom: 25px;
}

.reasoning-rich-paragraph {
    margin-bottom: 10px;
    line-height: 1.6;
}

.reasoning-rich-bullet-list {
    margin-left: 0;
    padding-left: 20px;
    margin-bottom: 15px;
}

.reasoning-rich-bullet {
    margin-bottom: 8px;
    line-height: 1.5;
}

.reasoning-rich-numbered-item {
    margin-bottom: 8px;
    line-height: 1.5;
    padding-left: 25px;
    position: relative;
}

.reasoning-rich-number {
    position: absolute;
    left: 0;
    font-weight: bold;
    color: #7a6a4f;
}

/* 引用的原文样式 */
.reasoning-text-quote {
    color: #0056b3;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    border-left: 2px solid #0056b3;
    margin: 0 2px;
    display: inline-block;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .reasoning-content {
        background-color: #212529;
        border-left-color: #0d6efd;
    }

    .reasoning-text {
        color: #e9ecef;
    }

    .reasoning-content-preview::after {
        background: linear-gradient(to bottom, transparent, #212529);
    }

    .reasoning-content em, .reasoning-text em {
        color: #6ea8fe;
    }

    .reasoning-content strong, .reasoning-text strong {
        color: #9ec5fe;
    }

    .reasoning-step-divider {
        border-top-color: #495057;
    }

    .reasoning-structured {
        background-color: #2c2c2c;
        border-color: #444;
    }

    .reasoning-section:first-child {
        background-color: #333;
        border-color: #444;
    }

    .reasoning-section-title {
        color: #e0e0e0;
        border-color: #444;
    }

    .reasoning-section-content {
        color: #ccc;
    }

    .reasoning-approach-text {
        color: #ccc;
    }

    .reasoning-analysis-text {
        color: #ccc;
    }

    .reasoning-detailed-section {
        background-color: #2a3040; /* 深蓝灰色背景 */
        border-color: #444;
    }

    .reasoning-detailed-text {
        color: #bbb;
    }

    .reasoning-connection-section {
        background-color: #302a40; /* 深紫灰色背景 */
        border-color: #444;
    }

    .reasoning-connection-text {
        color: #ccc;
    }

    .reasoning-original-section {
        background-color: #2a2a2a;
        border-color: #444;
    }

    .reasoning-original-text {
        color: #999;
    }

    .reasoning-structured-rich {
        background-color: #2c2c2c;
        border-color: #444;
    }

    .reasoning-rich-title {
        color: #e0e0e0;
        border-color: #444;
    }

    .reasoning-rich-subtitle {
        color: #ccc;
    }

    .reasoning-rich-paragraph {
        color: #bbb;
    }

    .reasoning-rich-bullet {
        color: #bbb;
    }

    .reasoning-rich-numbered-item {
        color: #bbb;
    }

    .reasoning-rich-number {
        color: #ccc;
    }

    .reasoning-text-quote {
        color: #6ea8fe;
        background-color: #2a2a2a;
        border-left-color: #6ea8fe;
    }
}