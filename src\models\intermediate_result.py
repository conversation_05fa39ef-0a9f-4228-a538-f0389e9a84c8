"""
中间分析结果模型，用于缓存和复用分析过程中的中间结果。
"""
from datetime import datetime, timezone
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship

from src.models.base import Base

# 创建一个函数来替代已弃用的datetime.utcnow
def utc_now():
    """返回当前的UTC时间，替代已弃用的datetime.utcnow"""
    return datetime.now(timezone.utc)

class IntermediateResult(Base):
    """中间分析结果模型，用于缓存和复用分析过程中的中间结果"""

    __tablename__ = 'intermediate_results'

    id = Column(Integer, primary_key=True)
    novel_id = Column(Integer, nullable=False, index=True)
    chunk_index = Column(Integer, nullable=False)  # 块索引
    chunk_hash = Column(String(64), nullable=False, index=True)  # 块内容的哈希值，用于快速比较

    # 中间结果数据，可以被多个维度共享使用
    result_data = Column(JSON, nullable=True)  # 存储基础分析结果，如文本统计、关键词等

    # 维度特定的中间结果
    dimension = Column(String(50), nullable=True)  # 如果是维度特定的结果，存储维度名称
    dimension_result = Column(Text, nullable=True)  # 维度特定的分析结果

    # 进度信息
    progress = Column(Integer, nullable=True)  # 分析进度，0-100
    meta_data = Column(JSON, nullable=True)  # 元数据，如预计剩余时间等

    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    def __init__(
        self,
        novel_id: int = None,
        chunk_index: int = None,
        chunk_hash: str = None,
        dimension: str = None,
        dimension_result: str = None,
        result_data: dict = None,
        progress: int = None,
        meta_data: dict = None
    ):
        """
        初始化中间分析结果对象。

        Args:
            novel_id: 小说ID
            chunk_index: 块索引
            chunk_hash: 块哈希值
            dimension: 分析维度
            dimension_result: 维度特定的分析结果
            result_data: 基础分析结果数据
            progress: 分析进度，0-100
            meta_data: 元数据，如预计剩余时间等
        """
        self.novel_id = novel_id
        self.chunk_index = chunk_index
        self.chunk_hash = chunk_hash
        self.dimension = dimension
        self.dimension_result = dimension_result
        self.progress = progress
        self.meta_data = meta_data

        # 确保result_data是可序列化的字典
        if result_data is not None:
            try:
                # 尝试将result_data转换为字典
                if hasattr(result_data, 'items') and callable(result_data.items):
                    # 如果有items方法，创建一个新的字典副本
                    self.result_data = {k: v for k, v in result_data.items()}
                elif isinstance(result_data, dict):
                    # 如果是字典，直接使用
                    self.result_data = dict(result_data)
                else:
                    # 如果不是字典且没有items方法，使用空字典并记录错误
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"result_data不是字典类型: {type(result_data)}")
                    self.result_data = {"error": f"result_data无法转换为字典: {type(result_data)}"}
            except Exception as e:
                # 如果转换失败，使用空字典
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"result_data处理失败: {str(e)}")
                self.result_data = {"error": f"result_data处理失败: {str(e)}"}
        else:
            self.result_data = None

    def __repr__(self):
        return f"<IntermediateResult(id={self.id}, novel_id={self.novel_id}, chunk_index={self.chunk_index}, dimension={self.dimension})>"
