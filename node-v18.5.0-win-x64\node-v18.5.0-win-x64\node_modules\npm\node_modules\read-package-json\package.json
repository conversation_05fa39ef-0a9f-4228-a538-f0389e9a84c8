{"name": "read-package-json", "version": "5.0.1", "author": "GitHub Inc.", "description": "The thing npm uses to read package.json files with semantics and defaults and validation", "repository": {"type": "git", "url": "https://github.com/npm/read-package-json.git"}, "main": "lib/read-json.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "release": "standard-version -s", "test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "dependencies": {"glob": "^8.0.1", "json-parse-even-better-errors": "^2.3.1", "normalize-package-data": "^4.0.0", "npm-normalize-package-bin": "^1.0.1"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.4.1", "tap": "^16.0.1"}, "license": "ISC", "files": ["bin/", "lib/"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "tap": {"branches": 68, "functions": 83, "lines": 76, "statements": 77}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.4.1"}}