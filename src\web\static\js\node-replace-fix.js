/**
 * 九猫 - DOM节点替换修复脚本
 * 解决"Failed to execute 'replaceChild' on 'Node': Unexpected identifier '$'"错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('DOM节点替换修复脚本已加载');
    
    // 保存原始的replaceChild方法
    const originalReplaceChild = Node.prototype.replaceChild;
    
    // 重写replaceChild方法
    Node.prototype.replaceChild = function(newChild, oldChild) {
        try {
            // 尝试使用原始方法
            return originalReplaceChild.call(this, newChild, oldChild);
        } catch (e) {
            console.error('replaceChild错误:', e.message);
            
            // 检查是否包含"Unexpected identifier '$'"错误
            if (e.message && e.message.includes('Unexpected identifier')) {
                console.log('检测到特定的replaceChild错误，尝试安全替换');
                
                try {
                    // 安全替换：先移除旧节点，再添加新节点
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }
                    this.appendChild(newChild);
                    return newChild;
                } catch (e2) {
                    console.error('安全替换也失败:', e2.message);
                    
                    // 最后的尝试：创建新节点并替换内容
                    try {
                        console.log('尝试创建新节点并替换内容');
                        
                        // 创建与oldChild相同类型的新节点
                        let replacementNode;
                        if (oldChild.nodeType === Node.ELEMENT_NODE) {
                            replacementNode = document.createElement(oldChild.tagName);
                            
                            // 复制属性
                            for (let i = 0; i < oldChild.attributes.length; i++) {
                                const attr = oldChild.attributes[i];
                                replacementNode.setAttribute(attr.name, attr.value);
                            }
                        } else if (oldChild.nodeType === Node.TEXT_NODE) {
                            replacementNode = document.createTextNode(oldChild.textContent);
                        } else {
                            // 对于其他节点类型，创建一个空的div
                            replacementNode = document.createElement('div');
                        }
                        
                        // 如果新节点是元素节点，复制其内容
                        if (newChild.nodeType === Node.ELEMENT_NODE) {
                            replacementNode.innerHTML = newChild.innerHTML;
                        } else if (newChild.nodeType === Node.TEXT_NODE) {
                            replacementNode.textContent = newChild.textContent;
                        }
                        
                        // 替换节点
                        if (this.contains(oldChild)) {
                            this.replaceChild(replacementNode, oldChild);
                        } else {
                            this.appendChild(replacementNode);
                        }
                        
                        return replacementNode;
                    } catch (e3) {
                        console.error('所有替换尝试都失败:', e3.message);
                        throw e; // 重新抛出原始错误
                    }
                }
            } else {
                // 如果不是特定错误，重新抛出
                throw e;
            }
        }
    };
    
    // 修复createElement方法，确保创建的元素有效
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        try {
            // 尝试使用原始方法
            return originalCreateElement.call(document, tagName);
        } catch (e) {
            console.error('createElement错误:', e.message);
            
            // 如果创建失败，尝试使用div作为后备
            console.log('创建元素失败，使用div作为后备');
            return originalCreateElement.call(document, 'div');
        }
    };
    
    // 修复appendChild方法
    const originalAppendChild = Node.prototype.appendChild;
    Node.prototype.appendChild = function(newChild) {
        try {
            // 尝试使用原始方法
            return originalAppendChild.call(this, newChild);
        } catch (e) {
            console.error('appendChild错误:', e.message);
            
            // 检查是否是特定错误
            if (e.message && (e.message.includes('Unexpected identifier') || e.message.includes('Failed to execute'))) {
                console.log('检测到特定的appendChild错误，尝试安全添加');
                
                try {
                    // 创建一个新的元素并复制内容
                    let safeChild;
                    if (newChild.nodeType === Node.ELEMENT_NODE) {
                        safeChild = document.createElement(newChild.tagName);
                        
                        // 复制属性
                        for (let i = 0; i < newChild.attributes.length; i++) {
                            const attr = newChild.attributes[i];
                            safeChild.setAttribute(attr.name, attr.value);
                        }
                        
                        // 复制内容
                        safeChild.innerHTML = newChild.innerHTML;
                    } else if (newChild.nodeType === Node.TEXT_NODE) {
                        safeChild = document.createTextNode(newChild.textContent);
                    } else {
                        // 对于其他节点类型，创建一个空的div
                        safeChild = document.createElement('div');
                    }
                    
                    // 添加安全节点
                    return originalAppendChild.call(this, safeChild);
                } catch (e2) {
                    console.error('安全添加也失败:', e2.message);
                    throw e; // 重新抛出原始错误
                }
            } else {
                // 如果不是特定错误，重新抛出
                throw e;
            }
        }
    };
    
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && (
            event.error.message.includes('replaceChild') || 
            event.error.message.includes('appendChild') ||
            event.error.message.includes('Unexpected identifier') ||
            event.error.message.includes('Failed to execute')
        )) {
            console.error('捕获到DOM操作错误:', event.error.message);
            console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);
            
            // 阻止错误传播
            event.preventDefault();
            return false;
        }
    }, true);
})();
