/**
 * 九猫系统 - 内存压力转移脚本
 * 将数据库连接池、日志和缓存等资源重定向到磁盘，减轻内存压力
 */

(function() {
    // 配置参数
    const CONFIG = {
        enabled: true,                      // 是否启用内存转移
        diskStoragePath: "E:/艹，又来一次/九猫",  // 磁盘存储路径
        memoryThreshold: 85,                // 内存使用率阈值(%)
        offloadInterval: 30000,             // 转移检查间隔(ms)
        logRedirection: true,               // 启用日志重定向
        dbPoolOffload: true,                // 启用数据库连接池转移
        resultCacheOffload: true,           // 启用结果缓存转移
        memoryMonitoring: true,             // 启用内存监控
        logToConsole: true                  // 记录到控制台
    };
    
    // 内存使用统计
    let memoryStats = {
        totalMemory: 0,
        usedMemory: 0,
        usagePercent: 0,
        lastUpdated: null
    };
    
    // 资源跟踪
    const offloadedResources = {
        dbConnections: 0,
        logEntries: 0,
        cacheItems: 0,
        totalSaved: 0 // 估计节省的内存(MB)
    };
    
    // 初始化
    function init() {
        if (!CONFIG.enabled) {
            console.log('[内存转移] 内存转移功能未启用');
            return;
        }
        
        console.log('[内存转移] 初始化内存压力转移...');
        console.log(`[内存转移] 目标路径: ${CONFIG.diskStoragePath}`);
        
        // 检查存储路径可用性
        checkStoragePath();
        
        // 启动内存监控
        if (CONFIG.memoryMonitoring) {
            startMemoryMonitoring();
        }
        
        // 设置数据库连接池转移
        if (CONFIG.dbPoolOffload) {
            setupDbPoolOffload();
        }
        
        // 设置日志重定向
        if (CONFIG.logRedirection) {
            setupLogRedirection();
        }
        
        // 设置结果缓存转移
        if (CONFIG.resultCacheOffload) {
            setupResultCacheOffload();
        }
        
        // 设置定期内存压力检查
        setInterval(checkMemoryPressure, CONFIG.offloadInterval);
        
        console.log('[内存转移] 内存压力转移初始化完成');
    }
    
    // 检查存储路径可用性
    function checkStoragePath() {
        // 向服务器发送请求检查路径可用性
        fetch('/api/system/check_storage_path', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                path: CONFIG.diskStoragePath
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`[内存转移] 存储路径检查成功: ${CONFIG.diskStoragePath}`);
                
                // 创建需要的子目录
                createRequiredDirectories();
            } else {
                console.error(`[内存转移] 存储路径不可用: ${data.error}`);
                
                // 显示错误消息
                showStoragePathError(data.error);
            }
        })
        .catch(error => {
            console.error('[内存转移] 检查存储路径时出错:', error);
        });
    }
    
    // 创建需要的子目录
    function createRequiredDirectories() {
        // 创建用于存储各种资源的子目录
        const directories = [
            'db_pool',
            'logs',
            'cache',
            'temp'
        ];
        
        // 每个目录再细分
        const subDirectories = {
            'logs': ['analysis', 'api', 'system', 'error', 'debug'],
            'cache': ['analysis_results', 'api_responses', 'visualizations'],
            'db_pool': ['connections', 'queries', 'backups'],
            'temp': ['uploads', 'exports', 'processing']
        };
        
        // 发送创建目录请求
        fetch('/api/system/create_directories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                base_path: CONFIG.diskStoragePath,
                directories: directories,
                sub_directories: subDirectories
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('[内存转移] 创建目录成功');
            } else {
                console.error('[内存转移] 创建目录失败:', data.error);
            }
        })
        .catch(error => {
            console.error('[内存转移] 创建目录时出错:', error);
        });
    }
    
    // 显示存储路径错误
    function showStoragePathError(error) {
        // 创建错误消息元素
        let errorElement = document.getElementById('storage-path-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.id = 'storage-path-error';
            errorElement.className = 'alert alert-danger alert-dismissible fade show';
            errorElement.innerHTML = `
                <strong>存储路径错误:</strong> ${error}
                <p>内存转移功能将被禁用，系统可能在处理大型文本时遇到内存不足问题。</p>
                <p>请确保路径 <code>${CONFIG.diskStoragePath}</code> 存在且有写入权限。</p>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
            `;
            
            // 添加到页面
            const container = document.querySelector('.container') || document.body;
            container.prepend(errorElement);
        }
    }
    
    // 启动内存监控
    function startMemoryMonitoring() {
        // 获取初始内存统计
        getMemoryStats();
        
        // 设置定期获取内存统计
        setInterval(() => {
            getMemoryStats();
            
            // 如果内存使用率高，显示警告
            if (memoryStats.usagePercent > CONFIG.memoryThreshold) {
                showHighMemoryWarning();
            }
        }, 10000); // 每10秒检查一次
    }
    
    // 获取内存统计
    function getMemoryStats() {
        fetch('/api/system/memory_stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    memoryStats = {
                        totalMemory: data.total_memory,
                        usedMemory: data.used_memory,
                        usagePercent: data.usage_percent,
                        lastUpdated: new Date()
                    };
                    
                    if (CONFIG.logToConsole) {
                        console.log(`[内存监控] 内存使用率: ${memoryStats.usagePercent.toFixed(1)}% (${(memoryStats.usedMemory / 1024).toFixed(1)}MB / ${(memoryStats.totalMemory / 1024).toFixed(1)}MB)`);
                    }
                }
            })
            .catch(error => {
                console.error('[内存监控] 获取内存统计失败:', error);
            });
    }
    
    // 显示高内存使用警告
    function showHighMemoryWarning() {
        // 检查警告是否已存在
        if (document.getElementById('high-memory-warning')) {
            // 更新现有警告的内容
            document.getElementById('memory-usage-value').textContent = `${memoryStats.usagePercent.toFixed(1)}%`;
            document.getElementById('memory-usage-detail').textContent = `(${(memoryStats.usedMemory / 1024).toFixed(1)}MB / ${(memoryStats.totalMemory / 1024).toFixed(1)}MB)`;
            return;
        }
        
        // 创建警告元素
        const warningElement = document.createElement('div');
        warningElement.id = 'high-memory-warning';
        warningElement.className = 'alert alert-warning alert-dismissible fade show fixed-bottom m-3';
        warningElement.style.maxWidth = '400px';
        warningElement.style.right = '0';
        warningElement.style.left = 'auto';
        
        warningElement.innerHTML = `
            <strong>系统内存使用率高</strong>
            <p>当前内存使用率: <span id="memory-usage-value">${memoryStats.usagePercent.toFixed(1)}%</span> <span id="memory-usage-detail">(${(memoryStats.usedMemory / 1024).toFixed(1)}MB / ${(memoryStats.totalMemory / 1024).toFixed(1)}MB)</span></p>
            <p>系统正在将数据转移到磁盘存储以减轻内存压力。</p>
            <p>已转移资源: 数据库连接 ${offloadedResources.dbConnections}个, 日志 ${offloadedResources.logEntries}条, 缓存项 ${offloadedResources.cacheItems}个</p>
            <p>预计节省内存: ${offloadedResources.totalSaved.toFixed(1)}MB</p>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
        `;
        
        // 添加到页面
        document.body.appendChild(warningElement);
        
        // 添加关闭按钮事件
        const closeButton = warningElement.querySelector('.btn-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                warningElement.remove();
            });
        }
    }
    
    // 设置数据库连接池转移
    function setupDbPoolOffload() {
        // 向服务器发送启用数据库连接池转移的请求
        fetch('/api/system/enable_db_pool_offload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                offload_path: `${CONFIG.diskStoragePath}/db_pool`
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('[内存转移] 数据库连接池转移已启用');
                offloadedResources.dbConnections = data.connections_offloaded || 0;
                offloadedResources.totalSaved += data.memory_saved || 0;
            } else {
                console.error('[内存转移] 启用数据库连接池转移失败:', data.error);
            }
        })
        .catch(error => {
            console.error('[内存转移] 启用数据库连接池转移时出错:', error);
        });
        
        // 设置连接池状态监控和显示
        setInterval(() => {
            fetch('/api/system/db_pool_offload_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新连接池转移统计
                        offloadedResources.dbConnections = data.connections_offloaded || offloadedResources.dbConnections;
                        offloadedResources.totalSaved = (offloadedResources.totalSaved - (data.previous_memory_saved || 0)) + (data.memory_saved || 0);
                        
                        // 更新内存使用显示
                        updateOffloadStats();
                    }
                })
                .catch(error => {
                    console.error('[内存转移] 获取连接池转移统计失败:', error);
                });
        }, 30000); // 每30秒更新一次
    }
    
    // 设置日志重定向
    function setupLogRedirection() {
        // 拦截控制台日志方法
        if (CONFIG.logRedirection && console) {
            // 保存原始方法
            const originalConsoleLog = console.log;
            const originalConsoleError = console.error;
            const originalConsoleWarn = console.warn;
            const originalConsoleInfo = console.info;
            
            // 替换log方法
            console.log = function() {
                // 调用原始方法
                originalConsoleLog.apply(console, arguments);
                
                // 将日志重定向到服务器
                if (arguments.length > 0) {
                    logToServer('info', Array.from(arguments).join(' '));
                }
            };
            
            // 替换error方法
            console.error = function() {
                // 调用原始方法
                originalConsoleError.apply(console, arguments);
                
                // 将日志重定向到服务器
                if (arguments.length > 0) {
                    logToServer('error', Array.from(arguments).join(' '));
                }
            };
            
            // 替换warn方法
            console.warn = function() {
                // 调用原始方法
                originalConsoleWarn.apply(console, arguments);
                
                // 将日志重定向到服务器
                if (arguments.length > 0) {
                    logToServer('warning', Array.from(arguments).join(' '));
                }
            };
            
            // 替换info方法
            console.info = function() {
                // 调用原始方法
                originalConsoleInfo.apply(console, arguments);
                
                // 将日志重定向到服务器
                if (arguments.length > 0) {
                    logToServer('info', Array.from(arguments).join(' '));
                }
            };
            
            console.log('[内存转移] 日志重定向已启用');
        }
    }
    
    // 将日志发送到服务器
    function logToServer(level, message) {
        // 过滤不需要的日志
        if (message.includes('[内存监控]') && !message.includes('错误')) {
            return;
        }
        
        // 发送日志到服务器
        fetch('/api/system/log', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                level: level,
                message: message,
                source: 'browser',
                path: `${CONFIG.diskStoragePath}/logs/${level}`,
                timestamp: new Date().toISOString()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新转移的日志计数
                offloadedResources.logEntries++;
                offloadedResources.totalSaved += 0.001; // 估算每条日志约1KB
            }
        })
        .catch(() => {
            // 忽略日志发送错误，防止无限递归
        });
    }
    
    // 设置结果缓存转移
    function setupResultCacheOffload() {
        // 向服务器发送启用结果缓存转移的请求
        fetch('/api/system/enable_cache_offload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                offload_path: `${CONFIG.diskStoragePath}/cache`
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('[内存转移] 结果缓存转移已启用');
                offloadedResources.cacheItems = data.cache_items_offloaded || 0;
                offloadedResources.totalSaved += data.memory_saved || 0;
                
                // 设置分析结果缓存转移
                setupAnalysisResultCache();
            } else {
                console.error('[内存转移] 启用结果缓存转移失败:', data.error);
            }
        })
        .catch(error => {
            console.error('[内存转移] 启用结果缓存转移时出错:', error);
        });
        
        // 设置缓存状态监控和显示
        setInterval(() => {
            fetch('/api/system/cache_offload_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新缓存转移统计
                        offloadedResources.cacheItems = data.cache_items_offloaded || offloadedResources.cacheItems;
                        offloadedResources.totalSaved = (offloadedResources.totalSaved - (data.previous_memory_saved || 0)) + (data.memory_saved || 0);
                        
                        // 更新内存使用显示
                        updateOffloadStats();
                    }
                })
                .catch(error => {
                    console.error('[内存转移] 获取缓存转移统计失败:', error);
                });
        }, 30000); // 每30秒更新一次
    }
    
    // 设置分析结果缓存
    function setupAnalysisResultCache() {
        // 替换所有分析结果获取逻辑，使用缓存版本
        const originalFetch = window.fetch;
        
        window.fetch = function(resource, init) {
            const url = (resource instanceof Request) ? resource.url : resource;
            
            // 检查是否是分析结果请求
            if (typeof url === 'string' && url.includes('/novel/') && url.includes('/analysis/')) {
                console.log(`[内存转移] 拦截分析结果请求: ${url}`);
                
                // 修改为缓存友好的请求
                const cacheFriendlyUrl = url + (url.includes('?') ? '&' : '?') + 'use_disk_cache=true';
                
                // 使用修改后的URL
                return originalFetch(cacheFriendlyUrl, init)
                    .then(response => {
                        if (response.ok) {
                            return response;
                        }
                        
                        // 如果缓存请求失败，尝试原始请求
                        console.warn(`[内存转移] 缓存请求失败，回退到原始请求: ${url}`);
                        return originalFetch(url, init);
                    })
                    .catch(error => {
                        console.error(`[内存转移] 缓存请求错误，回退到原始请求: ${error}`);
                        return originalFetch(url, init);
                    });
            }
            
            // 对于其他请求，保持原样
            return originalFetch.apply(this, arguments);
        };
    }
    
    // 更新转移统计信息显示
    function updateOffloadStats() {
        const warningElement = document.getElementById('high-memory-warning');
        if (warningElement) {
            // 更新转移资源信息
            warningElement.innerHTML = `
                <strong>系统内存使用率高</strong>
                <p>当前内存使用率: <span id="memory-usage-value">${memoryStats.usagePercent.toFixed(1)}%</span> <span id="memory-usage-detail">(${(memoryStats.usedMemory / 1024).toFixed(1)}MB / ${(memoryStats.totalMemory / 1024).toFixed(1)}MB)</span></p>
                <p>系统正在将数据转移到磁盘存储以减轻内存压力。</p>
                <p>已转移资源: 数据库连接 ${offloadedResources.dbConnections}个, 日志 ${offloadedResources.logEntries}条, 缓存项 ${offloadedResources.cacheItems}个</p>
                <p>预计节省内存: ${offloadedResources.totalSaved.toFixed(1)}MB</p>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
            `;
            
            // 重新添加关闭按钮事件
            const closeButton = warningElement.querySelector('.btn-close');
            if (closeButton) {
                closeButton.addEventListener('click', function() {
                    warningElement.remove();
                });
            }
        }
    }
    
    // 检查内存压力
    function checkMemoryPressure() {
        if (memoryStats.usagePercent > CONFIG.memoryThreshold) {
            console.warn(`[内存监控] 内存压力大: ${memoryStats.usagePercent.toFixed(1)}%，触发内存转移`);
            triggerMemoryOffload();
        }
    }
    
    // 触发内存转移
    function triggerMemoryOffload() {
        // 发送内存转移请求
        fetch('/api/system/offload_memory', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('[内存转移] 内存转移成功');
                
                // 更新转移统计
                const resources = data.resources || {};
                offloadedResources.dbConnections += resources.db_connections || 0;
                offloadedResources.logEntries += resources.log_entries || 0;
                offloadedResources.cacheItems += resources.cache_items || 0;
                offloadedResources.totalSaved += resources.memory_saved || 0;
                
                // 显示转移结果
                showOffloadResult(resources);
            } else {
                console.error('[内存转移] 内存转移失败:', data.error);
            }
        })
        .catch(error => {
            console.error('[内存转移] 触发内存转移时出错:', error);
        });
    }
    
    // 显示转移结果
    function showOffloadResult(resources) {
        // 创建or更新转移结果显示
        let resultElement = document.getElementById('memory-offload-result');
        if (!resultElement) {
            resultElement = document.createElement('div');
            resultElement.id = 'memory-offload-result';
            resultElement.className = 'toast align-items-center text-white bg-success border-0';
            resultElement.setAttribute('role', 'alert');
            resultElement.setAttribute('aria-live', 'assertive');
            resultElement.setAttribute('aria-atomic', 'true');
            resultElement.style.position = 'fixed';
            resultElement.style.bottom = '10px';
            resultElement.style.right = '10px';
            resultElement.style.minWidth = '300px';
            resultElement.style.zIndex = '9999';
            
            resultElement.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <strong>内存转移成功</strong><br>
                        转移数据库连接: ${resources.db_connections || 0}个<br>
                        转移日志: ${resources.log_entries || 0}条<br>
                        转移缓存项: ${resources.cache_items || 0}个<br>
                        节省内存: ${(resources.memory_saved || 0).toFixed(1)}MB
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="关闭"></button>
                </div>
            `;
            
            // 添加到页面
            document.body.appendChild(resultElement);
            
            // 使用Bootstrap显示toast（如果可用）
            if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                const toast = new bootstrap.Toast(resultElement, { delay: 5000 });
                toast.show();
            } else {
                // 手动显示和隐藏
                resultElement.style.display = 'block';
                setTimeout(() => {
                    resultElement.style.display = 'none';
                }, 5000);
            }
        } else {
            // 更新现有显示
            resultElement.querySelector('.toast-body').innerHTML = `
                <strong>内存转移成功</strong><br>
                转移数据库连接: ${resources.db_connections || 0}个<br>
                转移日志: ${resources.log_entries || 0}条<br>
                转移缓存项: ${resources.cache_items || 0}个<br>
                节省内存: ${(resources.memory_saved || 0).toFixed(1)}MB
            `;
        }
    }
    
    // 添加可视化内存使用
    function addMemoryVisualization() {
        // 创建可视化元素
        let vizContainer = document.getElementById('memory-visualization');
        if (!vizContainer) {
            vizContainer = document.createElement('div');
            vizContainer.id = 'memory-visualization';
            vizContainer.className = 'position-fixed';
            vizContainer.style.bottom = '10px';
            vizContainer.style.left = '10px';
            vizContainer.style.width = '200px';
            vizContainer.style.height = '100px';
            vizContainer.style.backgroundColor = 'rgba(0,0,0,0.7)';
            vizContainer.style.color = 'white';
            vizContainer.style.padding = '10px';
            vizContainer.style.borderRadius = '5px';
            vizContainer.style.zIndex = '9000';
            vizContainer.style.fontSize = '12px';
            
            vizContainer.innerHTML = `
                <div class="mb-2">内存使用情况</div>
                <div class="progress mb-2">
                    <div id="memory-usage-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="small" id="memory-usage-text">0MB / 0MB (0%)</div>
                <div class="mt-1">
                    <span class="badge bg-success">节省: <span id="memory-saved-text">0MB</span></span>
                </div>
            `;
            
            document.body.appendChild(vizContainer);
        }
        
        // 更新可视化
        updateMemoryVisualization();
        
        // 设置定期更新
        setInterval(updateMemoryVisualization, 5000);
    }
    
    // 更新内存可视化
    function updateMemoryVisualization() {
        const usageBar = document.getElementById('memory-usage-bar');
        const usageText = document.getElementById('memory-usage-text');
        const savedText = document.getElementById('memory-saved-text');
        
        if (usageBar && usageText && savedText) {
            // 更新内存使用条
            usageBar.style.width = `${memoryStats.usagePercent}%`;
            
            // 更新颜色
            if (memoryStats.usagePercent > CONFIG.memoryThreshold) {
                usageBar.className = 'progress-bar bg-danger';
            } else if (memoryStats.usagePercent > 70) {
                usageBar.className = 'progress-bar bg-warning';
            } else {
                usageBar.className = 'progress-bar bg-success';
            }
            
            // 更新文本
            usageText.textContent = `${(memoryStats.usedMemory / 1024).toFixed(1)}MB / ${(memoryStats.totalMemory / 1024).toFixed(1)}MB (${memoryStats.usagePercent.toFixed(1)}%)`;
            savedText.textContent = `${offloadedResources.totalSaved.toFixed(1)}MB`;
        }
    }
    
    // 启动初始化
    init();
    
    // 添加内存可视化
    if (CONFIG.memoryMonitoring) {
        // 延迟1秒添加可视化，确保页面已加载
        setTimeout(addMemoryVisualization, 1000);
    }
})(); 