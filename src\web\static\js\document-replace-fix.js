/**
 * 九猫 - 文档替换修复工具
 * 专门解决appendChild和脚本替换相关问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('文档替换修复工具已加载');
    
    // 安全的脚本替换函数
    window.safeReplaceScript = function(oldScript, newContent) {
        try {
            // 创建新脚本
            const newScript = document.createElement('script');
            newScript.type = 'text/javascript';
            
            // 复制原始脚本的属性
            if (oldScript.id) newScript.id = oldScript.id;
            if (oldScript.className) newScript.className = oldScript.className;
            if (oldScript.dataset) {
                for (const key in oldScript.dataset) {
                    newScript.dataset[key] = oldScript.dataset[key];
                }
            }
            
            // 设置内容
            // 修改：直接使用script.text属性设置内容，这样能更安全地处理${}等模板字符串
            try {
                // 使用script.text属性而不是创建文本节点
                newScript.text = newContent;
            } catch (e) {
                console.error('设置脚本内容时出错:', e);
                // 备用方法：尝试转义特殊字符
                try {
                    // 处理模板字符串中的$符号
                    const safeContent = newContent.replace(/\$\{/g, '\\${');
                    newScript.textContent = safeContent;
                } catch (e2) {
                    console.error('备用设置脚本内容方法也失败:', e2);
                }
            }
            
            // 记录父节点
            const parent = oldScript.parentNode;
            if (!parent) {
                console.error('替换脚本失败: 无法找到父节点');
                return false;
            }
            
            // 使用安全的方式插入和移除
            try {
                // 在旧脚本之前插入新脚本
                parent.insertBefore(newScript, oldScript);
                // 移除旧脚本
                parent.removeChild(oldScript);
                return true;
            } catch (e) {
                console.error('替换脚本过程中出错:', e);
                
                // 如果失败，尝试通过临时容器添加
                try {
                    const tempDiv = document.createElement('div');
                    tempDiv.style.display = 'none';
                    document.body.appendChild(tempDiv);
                    tempDiv.appendChild(newScript);
                    document.body.removeChild(tempDiv);
                    return true;
                } catch (e2) {
                    console.error('备用替换方法也失败:', e2);
                    return false;
                }
            }
        } catch (e) {
            console.error('脚本替换失败:', e);
            return false;
        }
    };
    
    // 安全的脚本添加函数
    window.safeAppendScript = function(content, target) {
        try {
            // 创建新脚本
            const script = document.createElement('script');
            script.type = 'text/javascript';
            
            // 设置内容
            try {
                // 使用script.text属性而不是创建文本节点
                script.text = content;
            } catch (e) {
                console.error('设置脚本内容时出错:', e);
                // 备用方法：尝试转义特殊字符
                try {
                    // 处理模板字符串中的$符号
                    const safeContent = content.replace(/\$\{/g, '\\${');
                    script.textContent = safeContent;
                } catch (e2) {
                    console.error('备用设置脚本内容方法也失败:', e2);
                }
            }
            
            // 确定目标元素
            const targetElement = target || document.head || document.body || document.documentElement;
            
            // 安全添加
            try {
                targetElement.appendChild(script);
                return true;
            } catch (e) {
                console.error('添加脚本时出错:', e);
                
                // 备用方法
                try {
                    const tempDiv = document.createElement('div');
                    tempDiv.style.display = 'none';
                    document.body.appendChild(tempDiv);
                    tempDiv.appendChild(script);
                    return true;
                } catch (e2) {
                    console.error('备用添加方法也失败:', e2);
                    return false;
                }
            }
        } catch (e) {
            console.error('脚本添加失败:', e);
            return false;
        }
    };
    
    // 修复预加载资源问题
    function fixPreloadResources() {
        try {
            // 查找所有预加载链接
            const preloads = document.querySelectorAll('link[rel="preload"]');
            
            preloads.forEach(function(preload) {
                // 确保设置了正确的as属性
                if (!preload.hasAttribute('as')) {
                    const href = preload.getAttribute('href') || '';
                    
                    // 根据文件扩展名设置正确的as属性
                    if (href.endsWith('.css')) {
                        preload.setAttribute('as', 'style');
                    } else if (href.endsWith('.js')) {
                        preload.setAttribute('as', 'script');
                    } else if (href.endsWith('.woff') || href.endsWith('.woff2') || href.endsWith('.ttf')) {
                        preload.setAttribute('as', 'font');
                    } else if (href.endsWith('.jpg') || href.endsWith('.jpeg') || href.endsWith('.png') || href.endsWith('.gif') || href.endsWith('.webp')) {
                        preload.setAttribute('as', 'image');
                    }
                }
            });
            
            console.log('已修复预加载资源');
        } catch (e) {
            console.error('修复预加载资源时出错:', e);
        }
    }
    
    // 创建缺失的large-text-fix.js
    function createLargeTextFixScript() {
        console.log('创建缺失的large-text-fix.js脚本');
        
        const largeTextFixContent = `
/**
 * 九猫 - 超大文本模式优化脚本
 * 自动创建的修复版本
 */
(function() {
    console.log('超大文本模式优化脚本已加载');
    
    // 优化DOM操作性能
    function optimizeDOMOperations() {
        // 批量DOM更新
        const batchDOMUpdates = function(callback) {
            // 创建文档片段，所有操作在片段中完成后一次性添加到DOM
            const fragment = document.createDocumentFragment();
            callback(fragment);
            return fragment;
        };
        
        // 添加到全局
        window.batchDOMUpdates = batchDOMUpdates;
        
        // 虚拟滚动优化
        window.setupVirtualScroll = function(container, itemHeight, renderFunc) {
            if (!container) return;
            
            const viewportHeight = container.clientHeight;
            const totalItems = 1000; // 示例数量，实际应基于数据量
            
            // 计算可见项数
            const visibleItems = Math.ceil(viewportHeight / itemHeight) + 2;
            
            // 设置容器高度
            container.style.height = (totalItems * itemHeight) + 'px';
            
            let lastScrollTop = 0;
            let renderTimeout = null;
            
            container.addEventListener('scroll', function() {
                // 防抖处理
                if (renderTimeout) {
                    clearTimeout(renderTimeout);
                }
                
                renderTimeout = setTimeout(function() {
                    const scrollTop = container.scrollTop;
                    
                    // 计算首个可见项
                    const firstVisibleItem = Math.floor(scrollTop / itemHeight);
                    
                    // 仅当滚动足够距离时才重新渲染
                    if (Math.abs(scrollTop - lastScrollTop) > (itemHeight / 2)) {
                        renderFunc(firstVisibleItem, visibleItems);
                        lastScrollTop = scrollTop;
                    }
                }, 100);
            });
        };
        
        console.log('DOM操作性能优化已应用');
    }
    
    // 优化内存使用
    function optimizeMemoryUsage() {
        // 内存监控
        const memoryMonitor = {
            checkInterval: 30000, // 30秒检查一次
            warningThreshold: 80, // 80%警告
            criticalThreshold: 90, // 90%严重警告
            
            init: function() {
                if (!window.performance || !window.performance.memory) {
                    console.log('浏览器不支持内存监控API');
                    return;
                }
                
                // 定期检查内存使用情况
                setInterval(function() {
                    const memoryInfo = window.performance.memory;
                    const usedPercent = (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100;
                    
                    if (usedPercent > memoryMonitor.criticalThreshold) {
                        console.error('内存使用严重警告: ' + usedPercent.toFixed(1) + '%');
                        memoryMonitor.forceGC();
                    } else if (usedPercent > memoryMonitor.warningThreshold) {
                        console.warn('内存使用警告: ' + usedPercent.toFixed(1) + '%');
                    }
                }, memoryMonitor.checkInterval);
                
                console.log('内存监控已启动');
            },
            
            forceGC: function() {
                // 尝试释放内存
                console.log('尝试释放未使用的内存');
                
                // 清理一些可能的内存泄漏
                if (window.chartInstances) {
                    for (const key in window.chartInstances) {
                        if (window.chartInstances[key]) {
                            window.chartInstances[key].destroy();
                            delete window.chartInstances[key];
                        }
                    }
                }
                
                // 移除未使用的事件监听器
                const obsoleteElements = document.querySelectorAll('.removed-item, .destroyed');
                obsoleteElements.forEach(function(element) {
                    element.parentNode.removeChild(element);
                });
                
                // 提示浏览器GC
                if (window.gc) {
                    window.gc();
                } else {
                    // 间接提示GC
                    const arr = [];
                    for (let i = 0; i < 10000; i++) {
                        arr.push(new Array(10000).join('x'));
                    }
                }
            }
        };
        
        // 启动内存监控
        memoryMonitor.init();
        
        console.log('内存优化已应用');
    }
    
    // 主函数
    function main() {
        // 应用DOM操作优化
        optimizeDOMOperations();
        
        // 应用内存优化
        optimizeMemoryUsage();
        
        console.log('超大文本模式优化完成');
    }
    
    // 执行主函数
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', main);
    } else {
        main();
    }
})();
`;
        
        // 创建脚本元素
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.id = 'large-text-fix';
        
        // 设置内容
        const textNode = document.createTextNode(largeTextFixContent);
        script.appendChild(textNode);
        
        // 添加到文档
        document.head.appendChild(script);
        
        console.log('large-text-fix.js脚本已创建并添加');
    }
    
    // 修复脚本替换函数
    function patchScriptReplaceFunctions() {
        // 修复chart-fix.js中的脚本替换逻辑
        if (typeof fixProgressFetching === 'function') {
            // 重写函数
            const originalFixProgressFetching = fixProgressFetching;
            fixProgressFetching = function() {
                console.log('使用安全版本的进度获取函数修复');
                
                // 查找所有分析结果页面的脚本
                const scripts = document.querySelectorAll('script');
                
                scripts.forEach(function(script, index) {
                    if (script.textContent && script.textContent.includes('fetchAnalysisProgress')) {
                        console.log('找到包含fetchAnalysisProgress的脚本 #' + index);
                        
                        try {
                            // 修复fetchAnalysisProgress函数
                            const fixedContent = script.textContent.replace(
                                /function\s+fetchAnalysisProgress\(\)\s*\{[\s\S]*?\}/,
                                `function fetchAnalysisProgress() {
                                    return fetch(\`/api/analysis/progress?novel_id=\${novelId}\`)
                                        .then(res => res.json())
                                        .then(data => {
                                            if (data.success) {
                                                if (data.progress && data.progress[dimension]) {
                                                    return {
                                                        progress: data.progress[dimension],
                                                        isRunning: data.is_running
                                                    };
                                                } else {
                                                    console.log('进度数据中没有当前维度的信息');
                                                    return {
                                                        progress: { progress: 0 },
                                                        isRunning: false
                                                    };
                                                }
                                            }
                                            console.log('进度数据获取成功但格式不正确');
                                            return {
                                                progress: { progress: 0 },
                                                isRunning: false
                                            };
                                        })
                                        .catch(err => {
                                            console.error('获取进度信息失败:', err);
                                            return {
                                                progress: { progress: 0 },
                                                isRunning: false
                                            };
                                        });
                                }`
                            );
                            
                            // 如果内容被修改，使用安全的方式替换脚本
                            if (fixedContent !== script.textContent) {
                                console.log('使用安全方法替换进度获取脚本');
                                window.safeReplaceScript(script, fixedContent);
                            }
                        } catch (e) {
                            console.error('修复进度获取函数时出错:', e);
                        }
                    }
                });
            };
        }
        
        // 修复chart-fix.js中的图表初始化函数逻辑
        if (typeof fixChartInitialization === 'function') {
            // 重写函数
            const originalFixChartInitialization = fixChartInitialization;
            fixChartInitialization = function() {
                console.log('使用安全版本的图表初始化函数修复');
                
                // 查找所有分析结果页面的脚本
                const scripts = document.querySelectorAll('script');
                
                scripts.forEach(function(script, index) {
                    if (script.textContent && script.textContent.includes('initCharts')) {
                        console.log('找到包含initCharts的脚本 #' + index);
                        
                        try {
                            // 修复initCharts函数中的图表初始化部分
                            const fixedContent = script.textContent.replace(
                                /new\s+Chart\((\w+Ctx),\s*\{/g,
                                'window.safeInitChart($1.canvas.id, '
                            ).replace(
                                /\}\s*\)\s*;/g,
                                '});'
                            );
                            
                            // 如果内容被修改，使用安全的方式替换脚本
                            if (fixedContent !== script.textContent) {
                                console.log('使用安全方法替换图表初始化脚本');
                                window.safeReplaceScript(script, fixedContent);
                            }
                        } catch (e) {
                            console.error('修复图表初始化函数时出错:', e);
                        }
                    }
                });
            };
        }
        
        console.log('脚本替换函数已修补');
    }
    
    // 主函数
    function main() {
        // 修复预加载资源
        fixPreloadResources();
        
        // 创建缺失的large-text-fix.js
        createLargeTextFixScript();
        
        // 修复脚本替换函数
        patchScriptReplaceFunctions();
        
        console.log('文档替换修复完成');
    }
    
    // 执行主函数
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', main);
    } else {
        main();
    }
})(); 