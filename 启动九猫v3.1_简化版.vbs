Option Explicit

' 九猫小说分析写作系统v3.1 简化版启动脚本
' 此脚本是简化版的九猫v3.1启动器，只设置核心变量并启动系统

' 创建对象
Dim objShell, objFSO, strCurrentDir
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取当前目录
strCurrentDir = objFSO.GetAbsolutePathName(".")

' 设置工作目录
objShell.CurrentDirectory = strCurrentDir

' 设置核心环境变量
objShell.Environment("PROCESS")("VERSION") = "3.1"

' 启动消息
MsgBox "正在启动九猫小说分析写作系统v3.1..." & vbCrLf & vbCrLf & _
       "系统将在后台运行，稍后将自动打开浏览器。", vbInformation, "九猫v3.1启动器"

' 创建启动批处理文件
Dim tempFile
Set tempFile = objFSO.CreateTextFile("quick_start_v3_1.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & strCurrentDir & """")
tempFile.WriteLine("set VERSION=3.1")
tempFile.WriteLine("echo 启动九猫v3.1系统...")
tempFile.WriteLine("start /b python -u -m src.web.v3_app")
tempFile.Close

' 执行批处理文件
objShell.Run "cmd /c """ & strCurrentDir & "\quick_start_v3_1.bat""", 0, False

' 等待几秒确保系统启动
WScript.Sleep 3000

' 打开浏览器访问系统
objShell.Run "http://localhost:5001/v3.1/console", 1, False

' 完成消息
MsgBox "九猫小说分析写作系统v3.1已启动！" & vbCrLf & vbCrLf & _
       "系统已在浏览器中打开，地址：http://localhost:5001/v3.1/console", _
       vbInformation, "九猫v3.1已启动"

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
