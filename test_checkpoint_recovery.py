"""
测试九猫系统的断点恢复功能
"""
import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.db.connection import Session
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.analysis_checkpoint import AnalysisCheckpoint
from src.api.analysis import NovelAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_checkpoint_recovery():
    """测试断点恢复功能"""
    session = Session()
    try:
        # 获取一个测试小说
        novel = session.query(Novel).first()
        if not novel:
            logger.error("没有找到测试小说，请先添加小说")
            return
        
        logger.info(f"使用小说 '{novel.title}' (ID: {novel.id}) 进行测试")
        
        # 选择一个测试维度
        test_dimension = "language_style"
        
        # 检查是否已有断点
        checkpoint = session.query(AnalysisCheckpoint).filter_by(
            novel_id=novel.id,
            dimension=test_dimension
        ).first()
        
        if checkpoint:
            logger.info(f"找到现有断点: 维度 {test_dimension}, 当前块 {checkpoint.current_block}/{checkpoint.total_blocks}")
        else:
            logger.info(f"没有找到维度 {test_dimension} 的断点，将创建新断点")
            
            # 创建一个测试断点
            checkpoint = AnalysisCheckpoint(
                novel_id=novel.id,
                dimension=test_dimension,
                current_block=3,  # 假设已处理了3个块
                total_blocks=10   # 总共有10个块
            )
            session.add(checkpoint)
            session.commit()
            logger.info(f"创建了测试断点: 维度 {test_dimension}, 当前块 {checkpoint.current_block}/{checkpoint.total_blocks}")
        
        # 检查是否有部分分析结果
        partial_result = session.query(AnalysisResult).filter_by(
            novel_id=novel.id,
            dimension=test_dimension
        ).first()
        
        if partial_result and partial_result.analysis_metadata and "partial_results" in partial_result.analysis_metadata:
            logger.info(f"找到部分分析结果: {len(partial_result.analysis_metadata['partial_results'])} 个块")
        else:
            logger.info("没有找到部分分析结果")
        
        # 初始化分析器
        analyzer = NovelAnalyzer()
        
        # 模拟从断点恢复分析
        logger.info("开始从断点恢复分析...")
        
        # 这里我们不实际执行分析，只是验证断点恢复的逻辑
        # 在实际应用中，这里会调用analyzer.analyze_novel_parallel或类似方法
        
        logger.info("断点恢复测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
    finally:
        session.close()

if __name__ == "__main__":
    logger.info("开始测试断点恢复功能")
    test_checkpoint_recovery()
    logger.info("测试完成")
