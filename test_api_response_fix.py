#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API响应处理修复效果
用于验证21字符无效内容问题的修复
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.test_service import TestService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_api_response_diagnosis():
    """测试API响应诊断功能"""
    print("=== 测试API响应诊断功能 ===")
    
    # 测试用例1: None响应
    print("\n1. 测试None响应:")
    diagnosis = TestService._diagnose_api_response_issue(None)
    print(diagnosis)
    
    # 测试用例2: 21字符短响应
    print("\n2. 测试21字符短响应:")
    short_response = "401 Unauthorized"
    diagnosis = TestService._diagnose_api_response_issue(short_response)
    print(diagnosis)
    
    # 测试用例3: 字典响应但content字段过短
    print("\n3. 测试字典响应但content字段过短:")
    dict_response = {
        "content": "Error 500",
        "status": "failed"
    }
    diagnosis = TestService._diagnose_api_response_issue(dict_response)
    print(diagnosis)
    
    # 测试用例4: 正常响应
    print("\n4. 测试正常响应:")
    normal_response = {
        "content": "这是一个正常的长度足够的API响应内容，包含了足够的文本来通过验证检查。",
        "type": "chapter_content_generation"
    }
    diagnosis = TestService._diagnose_api_response_issue(normal_response)
    print(diagnosis)

def test_batch_content_validation():
    """测试批次内容验证功能"""
    print("\n=== 测试批次内容验证功能 ===")
    
    # 测试用例1: 21字符无效内容
    print("\n1. 测试21字符无效内容:")
    short_content = "API返回的数据格式不正确"
    result = TestService._validate_batch_content(short_content, 1, 500)
    print(f"验证结果: {result}")
    
    # 测试用例2: 错误信息内容
    print("\n2. 测试错误信息内容:")
    error_content = "生成内容失败: API调用失败，请稍后重试"
    result = TestService._validate_batch_content(error_content, 1, 500)
    print(f"验证结果: {result}")
    
    # 测试用例3: 提示词内容
    print("\n3. 测试提示词内容:")
    prompt_content = """
    # 九猫写作系统 - 核心写作指令
    
    ## 原文信息
    - 小说标题: 测试小说
    - 章节标题: 第1章
    
    请基于以上分析结果创作新的章节内容...
    """
    result = TestService._validate_batch_content(prompt_content, 1, 500)
    print(f"验证结果: {result}")
    
    # 测试用例4: 正常章节内容
    print("\n4. 测试正常章节内容:")
    normal_content = """
    # 第1章 新的开始
    
    林小雨站在窗前，看着外面的雨滴打在玻璃上。今天是她来到这个城市的第一天，心情既紧张又兴奋。
    
    "你好，我是林小雨。"她对着镜子练习着自我介绍，希望能给新同事留下好印象。
    
    手机响了，是妈妈打来的。
    
    "小雨，到了吗？新环境怎么样？"妈妈关切的声音传来。
    
    "刚到，还在适应中。"林小雨回答道，"公司看起来不错，同事们也很友善。"
    
    挂了电话，她深吸一口气，准备迎接新的挑战。这是她人生的新篇章，她要好好把握。
    
    走出房间，林小雨决定先熟悉一下周围的环境。楼下有一家咖啡店，香味飘到了楼上。
    
    "也许可以去那里坐坐。"她想着，拿起钥匙走向门口。
    """
    result = TestService._validate_batch_content(normal_content, 1, 500)
    print(f"验证结果: {result}")

def main():
    """主函数"""
    print("九猫系统API响应处理修复测试")
    print("=" * 50)
    
    try:
        # 测试API响应诊断
        test_api_response_diagnosis()
        
        # 测试批次内容验证
        test_batch_content_validation()
        
        print("\n" + "=" * 50)
        print("测试完成！")
        print("\n修复要点总结：")
        print("1. 增强了API响应的详细日志记录")
        print("2. 添加了21字符无效内容的特殊检测")
        print("3. 实现了多层次的内容提取策略")
        print("4. 增强了批次内容验证机制")
        print("5. 添加了第一批次构建的重试机制")
        print("6. 提供了详细的API响应问题诊断")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    main()
