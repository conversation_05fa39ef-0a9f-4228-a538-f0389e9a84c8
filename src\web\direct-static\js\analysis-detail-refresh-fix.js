/**
 * 九猫 - 分析详情页面无限刷新修复脚本
 * 专门处理分析详情页面不停刷新/重新渲染的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._analysisDetailRefreshFixLoaded) {
        console.log('分析详情页面无限刷新修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._analysisDetailRefreshFixLoaded = true;
    
    console.log('分析详情页面无限刷新修复脚本已加载 - 版本1.0.0');
    
    // 保存原始的console.log方法，用于调试
    const originalConsoleLog = console.log;
    
    // 可能导致无限刷新的计时器ID
    let problematicTimers = new Set();
    
    // 检查并修复无限刷新问题
    function checkAndFixInfiniteRefresh() {
        // 检查是否在分析详情页面
        if (!isAnalysisDetailPage()) {
            return;
        }
        
        originalConsoleLog('检查分析详情页面无限刷新问题');
        
        // 修复自动刷新脚本
        fixAutoRefreshScripts();
        
        // 修复可能导致无限循环的定时器
        fixProblematicTimers();
        
        // 修复可能导致问题的事件监听器
        fixEventListeners();
        
        // 修复可能导致问题的MutationObserver
        fixMutationObservers();
        
        // 修复可能导致问题的AJAX轮询
        fixAjaxPolling();
        
        // 修复可能导致问题的WebSocket连接
        fixWebSockets();
    }
    
    // 检查是否在分析详情页面
    function isAnalysisDetailPage() {
        const url = window.location.href;
        return url.includes('/analysis/') || 
               url.includes('/novel/') && url.includes('/dimension/') ||
               url.includes('chapter') && url.includes('analysis');
    }
    
    // 修复自动刷新脚本
    function fixAutoRefreshScripts() {
        // 查找并禁用包含自动刷新代码的脚本
        const scripts = document.querySelectorAll('script:not([src])');
        for (const script of scripts) {
            const content = script.textContent || '';
            
            // 检查是否包含自动刷新代码
            if (content.includes('location.reload') || 
                content.includes('window.location.reload') || 
                (content.includes('setTimeout') && content.includes('reload'))) {
                
                originalConsoleLog('发现可能导致自动刷新的脚本:', 
                    content.substring(0, 100) + (content.length > 100 ? '...' : ''));
                
                // 标记脚本为已禁用
                script.setAttribute('data-disabled-by-fix', 'true');
                
                // 尝试替换脚本内容
                try {
                    const newScript = document.createElement('script');
                    newScript.textContent = content
                        .replace(/location\.reload/g, '/* location.reload */')
                        .replace(/window\.location\.reload/g, '/* window.location.reload */')
                        .replace(/setTimeout\s*\(\s*function\s*\(\s*\)\s*\{\s*(?:window\.)?location\.reload/g, 
                                'setTimeout(function() { /* location.reload disabled */');
                    
                    // 替换原始脚本
                    if (script.parentNode) {
                        script.parentNode.replaceChild(newScript, script);
                        originalConsoleLog('已禁用自动刷新脚本');
                    }
                } catch (e) {
                    originalConsoleLog('禁用自动刷新脚本失败:', e);
                }
            }
        }
    }
    
    // 修复可能导致无限循环的定时器
    function fixProblematicTimers() {
        // 保存原始的setTimeout和setInterval方法
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;
        const originalClearTimeout = window.clearTimeout;
        const originalClearInterval = window.clearInterval;
        
        // 重写setTimeout方法
        window.setTimeout = function(callback, delay, ...args) {
            // 检查回调函数是否可能导致无限循环
            const callbackString = callback.toString();
            const isProblematic = 
                callbackString.includes('location.reload') || 
                callbackString.includes('window.location') ||
                callbackString.includes('innerHTML') ||
                callbackString.includes('appendChild') ||
                callbackString.includes('insertBefore') ||
                callbackString.includes('replaceChild');
            
            if (isProblematic && delay < 5000) {
                originalConsoleLog('检测到可能导致无限循环的setTimeout，延长间隔时间');
                delay = Math.max(delay, 5000); // 至少5秒
            }
            
            const timerId = originalSetTimeout.call(this, callback, delay, ...args);
            
            if (isProblematic) {
                problematicTimers.add(timerId);
                originalConsoleLog('已记录可能导致问题的定时器:', timerId);
            }
            
            return timerId;
        };
        
        // 重写setInterval方法
        window.setInterval = function(callback, delay, ...args) {
            // 检查回调函数是否可能导致无限循环
            const callbackString = callback.toString();
            const isProblematic = 
                callbackString.includes('location.reload') || 
                callbackString.includes('window.location') ||
                callbackString.includes('innerHTML') ||
                callbackString.includes('appendChild') ||
                callbackString.includes('insertBefore') ||
                callbackString.includes('replaceChild');
            
            if (isProblematic && delay < 5000) {
                originalConsoleLog('检测到可能导致无限循环的setInterval，延长间隔时间');
                delay = Math.max(delay, 5000); // 至少5秒
            }
            
            const timerId = originalSetInterval.call(this, callback, delay, ...args);
            
            if (isProblematic) {
                problematicTimers.add(timerId);
                originalConsoleLog('已记录可能导致问题的定时器:', timerId);
            }
            
            return timerId;
        };
        
        // 重写clearTimeout方法
        window.clearTimeout = function(id) {
            problematicTimers.delete(id);
            return originalClearTimeout.call(this, id);
        };
        
        // 重写clearInterval方法
        window.clearInterval = function(id) {
            problematicTimers.delete(id);
            return originalClearInterval.call(this, id);
        };
        
        // 清除所有可能导致问题的定时器
        originalConsoleLog('清除可能导致问题的定时器');
        problematicTimers.forEach(id => {
            try {
                originalClearTimeout(id);
                originalClearInterval(id);
                originalConsoleLog('已清除定时器:', id);
            } catch (e) {
                originalConsoleLog('清除定时器失败:', e);
            }
        });
    }
    
    // 修复可能导致问题的事件监听器
    function fixEventListeners() {
        // 保存原始的addEventListener方法
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        
        // 重写addEventListener方法
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            // 检查是否是可能导致无限循环的事件类型
            if (['DOMContentLoaded', 'load', 'readystatechange'].includes(type)) {
                // 包装监听器，防止无限循环
                const wrappedListener = function(event) {
                    try {
                        // 检查是否已经执行过
                        if (this._listenerExecuted) {
                            originalConsoleLog('阻止重复执行事件监听器:', type);
                            return;
                        }
                        
                        // 标记为已执行
                        this._listenerExecuted = true;
                        
                        // 执行原始监听器
                        return listener.call(this, event);
                    } catch (e) {
                        originalConsoleLog('事件监听器执行出错:', e);
                    }
                };
                
                return originalAddEventListener.call(this, type, wrappedListener, options);
            }
            
            // 对于其他事件类型，使用原始方法
            return originalAddEventListener.call(this, type, listener, options);
        };
    }
    
    // 修复可能导致问题的MutationObserver
    function fixMutationObservers() {
        // 保存原始的MutationObserver构造函数
        const originalMutationObserver = window.MutationObserver;
        
        // 重写MutationObserver构造函数
        window.MutationObserver = function(callback) {
            // 包装回调函数，防止无限循环
            const wrappedCallback = function(mutations, observer) {
                // 添加节流，避免过于频繁的更新
                if (!observer._lastCallTime || Date.now() - observer._lastCallTime > 1000) {
                    observer._lastCallTime = Date.now();
                    return callback(mutations, observer);
                } else {
                    originalConsoleLog('MutationObserver回调被节流');
                }
            };
            
            return new originalMutationObserver(wrappedCallback);
        };
    }
    
    // 修复可能导致问题的AJAX轮询
    function fixAjaxPolling() {
        // 如果存在jQuery
        if (window.jQuery) {
            // 保存原始的jQuery.ajax方法
            const originalAjax = jQuery.ajax;
            
            // 重写jQuery.ajax方法
            jQuery.ajax = function(url, options) {
                // 规范化参数
                if (typeof url === 'object') {
                    options = url;
                    url = undefined;
                }
                
                options = options || {};
                
                // 检查是否是轮询请求
                const isPolling = 
                    options.url && (options.url.includes('progress') || options.url.includes('status')) ||
                    url && (url.includes('progress') || url.includes('status'));
                
                if (isPolling) {
                    // 确保轮询间隔不会太短
                    const originalComplete = options.complete;
                    
                    options.complete = function(jqXHR, textStatus) {
                        // 如果请求完成后有计划的轮询，确保间隔至少为5秒
                        if (originalComplete) {
                            // 延迟执行原始的complete回调
                            setTimeout(function() {
                                originalComplete.call(this, jqXHR, textStatus);
                            }, 5000);
                            
                            // 阻止原始complete回调立即执行
                            return false;
                        }
                    };
                }
                
                // 调用原始的ajax方法
                return originalAjax.call(jQuery, url, options);
            };
        }
    }
    
    // 修复可能导致问题的WebSocket连接
    function fixWebSockets() {
        // 保存原始的WebSocket构造函数
        const originalWebSocket = window.WebSocket;
        
        // 重写WebSocket构造函数
        window.WebSocket = function(url, protocols) {
            originalConsoleLog('创建WebSocket连接:', url);
            
            // 创建WebSocket实例
            const ws = new originalWebSocket(url, protocols);
            
            // 包装onmessage处理函数，防止无限循环
            const originalOnMessage = ws.onmessage;
            
            ws.onmessage = function(event) {
                // 添加节流，避免过于频繁的更新
                if (!ws._lastMessageTime || Date.now() - ws._lastMessageTime > 1000) {
                    ws._lastMessageTime = Date.now();
                    
                    if (originalOnMessage) {
                        return originalOnMessage.call(this, event);
                    }
                } else {
                    originalConsoleLog('WebSocket消息处理被节流');
                }
            };
            
            return ws;
        };
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkAndFixInfiniteRefresh);
    } else {
        // 如果页面已经加载完成，立即执行修复
        checkAndFixInfiniteRefresh();
    }
    
    // 在页面完全加载后再次执行修复
    window.addEventListener('load', function() {
        setTimeout(checkAndFixInfiniteRefresh, 1000);
    });
    
    console.log('分析详情页面无限刷新修复脚本初始化完成');
})(); 