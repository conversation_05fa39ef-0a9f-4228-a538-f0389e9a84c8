{% extends "new_base.html" %}

{% block title %}{{ novel.title }} - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-0">{{ novel.title }}</h1>
        <p class="text-muted">
            {% if novel.author %}作者: {{ novel.author }} | {% endif %}
            上传时间: {{ novel.created_at.strftime('%Y-%m-%d %H:%M') }} |
            字数: {{ novel.word_count or '未知' }}
        </p>
    </div>
    <div class="col-md-4 text-md-end">
        <a href="{{ url_for('new.novels') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回列表
        </a>
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteNovelModal">
            <i class="fas fa-trash-alt me-2"></i>删除
        </button>
    </div>
</div>

<div class="row">
    <!-- 左侧内容区 -->
    <div class="col-lg-8">
        <!-- 分析状态卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-chart-line me-2"></i>分析状态</h3>
            </div>
            <div class="card-body">
                {% if completed_dimensions_count == total_dimensions_count %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>{{ completed_dimensions_count }}/{{ total_dimensions_count }} 分析已完成
                    </div>
                {% elif completed_dimensions_count > 0 %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>{{ completed_dimensions_count }}/{{ total_dimensions_count }} 分析未完成
                    </div>

                    <h4 class="mt-4 mb-3">已完成的分析维度</h4>
                    <div class="row">
                        {% for dimension, result in analysis_results.items() %}
                            <div class="col-md-6 mb-3">
                                <div class="card analysis-dimension-card">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            {% if dimension == 'language_style' %}
                                                <i class="fas fa-language me-2"></i>语言风格
                                            {% elif dimension == 'rhythm_pacing' %}
                                                <i class="fas fa-wave-square me-2"></i>节奏与节奏
                                            {% elif dimension == 'structure' %}
                                                <i class="fas fa-sitemap me-2"></i>结构分析
                                            {% elif dimension == 'sentence_variation' %}
                                                <i class="fas fa-text-width me-2"></i>句式变化
                                            {% elif dimension == 'paragraph_length' %}
                                                <i class="fas fa-indent me-2"></i>段落长度
                                            {% elif dimension == 'perspective_shifts' %}
                                                <i class="fas fa-eye me-2"></i>视角转换
                                            {% elif dimension == 'paragraph_flow' %}
                                                <i class="fas fa-stream me-2"></i>段落流畅度
                                            {% elif dimension == 'novel_characteristics' %}
                                                <i class="fas fa-fingerprint me-2"></i>小说特点
                                            {% elif dimension == 'world_building' %}
                                                <i class="fas fa-globe me-2"></i>世界构建
                                            {% elif dimension == 'chapter_outline' %}
                                                <i class="fas fa-list-ol me-2"></i>章节大纲
                                            {% elif dimension == 'character_relationships' %}
                                                <i class="fas fa-users me-2"></i>人物关系
                                            {% elif dimension == 'opening_effectiveness' %}
                                                <i class="fas fa-door-open me-2"></i>开篇效果
                                            {% elif dimension == 'climax_pacing' %}
                                                <i class="fas fa-mountain me-2"></i>高潮节奏
                                            {% else %}
                                                <i class="fas fa-chart-bar me-2"></i>{{ dimension }}
                                            {% endif %}
                                        </h5>
                                        <div class="d-flex gap-2">
                                            <a href="{{ url_for('analysis', novel_id=novel.id, dimension=dimension) }}" class="btn btn-outline-primary btn-sm flex-grow-1">
                                                查看分析结果
                                            </a>
                                            <button class="btn btn-outline-secondary btn-sm start-analysis" data-dimension="{{ dimension }}">
                                                <i class="fas fa-sync-alt"></i> 重新分析
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>

                    {% if missing_dimensions %}
                        <h4 class="mt-4 mb-3">未分析的维度</h4>
                        <div class="row">
                            {% for dimension in missing_dimensions %}
                                <div class="col-md-6 mb-3">
                                    <div class="card analysis-dimension-card bg-light">
                                        <div class="card-body">
                                            <h5 class="card-title text-muted">
                                                {% if dimension == 'language_style' %}
                                                    <i class="fas fa-language me-2"></i>语言风格
                                                {% elif dimension == 'rhythm_pacing' %}
                                                    <i class="fas fa-wave-square me-2"></i>节奏与节奏
                                                {% elif dimension == 'structure' %}
                                                    <i class="fas fa-sitemap me-2"></i>结构分析
                                                {% elif dimension == 'sentence_variation' %}
                                                    <i class="fas fa-text-width me-2"></i>句式变化
                                                {% elif dimension == 'paragraph_length' %}
                                                    <i class="fas fa-indent me-2"></i>段落长度
                                                {% elif dimension == 'perspective_shifts' %}
                                                    <i class="fas fa-eye me-2"></i>视角转换
                                                {% elif dimension == 'paragraph_flow' %}
                                                    <i class="fas fa-stream me-2"></i>段落流畅度
                                                {% elif dimension == 'novel_characteristics' %}
                                                    <i class="fas fa-fingerprint me-2"></i>小说特点
                                                {% elif dimension == 'world_building' %}
                                                    <i class="fas fa-globe me-2"></i>世界构建
                                                {% elif dimension == 'chapter_outline' %}
                                                    <i class="fas fa-list-ol me-2"></i>章节大纲
                                                {% elif dimension == 'character_relationships' %}
                                                    <i class="fas fa-users me-2"></i>人物关系
                                                {% elif dimension == 'opening_effectiveness' %}
                                                    <i class="fas fa-door-open me-2"></i>开篇效果
                                                {% elif dimension == 'climax_pacing' %}
                                                    <i class="fas fa-mountain me-2"></i>高潮节奏
                                                {% else %}
                                                    <i class="fas fa-chart-bar me-2"></i>{{ dimension }}
                                                {% endif %}
                                            </h5>
                                            <div class="d-grid">
                                                <button class="btn btn-outline-secondary btn-sm start-analysis" data-dimension="{{ dimension }}">
                                                    开始分析
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>该小说尚未进行分析
                    </div>

                    <div class="text-center my-4">
                        <button class="btn btn-primary btn-lg" id="startAnalysisBtn">
                            <i class="fas fa-play-circle me-2"></i>开始分析
                        </button>
                    </div>

                    <div id="dimensionSelectionSection" style="display: none;">
                        <h4 class="mb-3">选择分析维度</h4>
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="language_style" value="language_style" checked>
                                            <label class="form-check-label" for="language_style">语言风格</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="rhythm_pacing" value="rhythm_pacing" checked>
                                            <label class="form-check-label" for="rhythm_pacing">节奏与节奏</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="structure" value="structure" checked>
                                            <label class="form-check-label" for="structure">结构分析</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="sentence_variation" value="sentence_variation">
                                            <label class="form-check-label" for="sentence_variation">句式变化</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="paragraph_length" value="paragraph_length">
                                            <label class="form-check-label" for="paragraph_length">段落长度</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="perspective_shifts" value="perspective_shifts">
                                            <label class="form-check-label" for="perspective_shifts">视角转换</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="paragraph_flow" value="paragraph_flow">
                                            <label class="form-check-label" for="paragraph_flow">段落流畅度</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="novel_characteristics" value="novel_characteristics">
                                            <label class="form-check-label" for="novel_characteristics">小说特点</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="world_building" value="world_building">
                                            <label class="form-check-label" for="world_building">世界构建</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="chapter_outline" value="chapter_outline">
                                            <label class="form-check-label" for="chapter_outline">章节大纲</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="character_relationships" value="character_relationships">
                                            <label class="form-check-label" for="character_relationships">人物关系</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="opening_effectiveness" value="opening_effectiveness">
                                            <label class="form-check-label" for="opening_effectiveness">开篇效果</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input dimension-checkbox" type="checkbox" id="climax_pacing" value="climax_pacing">
                                            <label class="form-check-label" for="climax_pacing">高潮节奏</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllDimensions">全选</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllDimensions">取消全选</button>
                                </div>
                                <div class="d-grid mt-3">
                                    <button class="btn btn-primary" id="confirmAnalysisBtn">
                                        <i class="fas fa-play-circle me-2"></i>开始分析
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 小说内容预览卡片 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title mb-0"><i class="fas fa-book-open me-2"></i>小说内容预览</h3>
                <button class="btn btn-sm btn-outline-primary" id="toggleContentBtn">
                    <i class="fas fa-expand-alt me-1"></i>展开/收起
                </button>
            </div>
            <div class="card-body">
                <div id="novelContentPreview" class="novel-content-preview">
                    {{ novel.content }}{% if novel.content|length > 1000 %}...{% endif %}
                </div>
                <div id="novelContentFull" class="novel-content-full" style="display: none;">
                    {{ novel.content }}
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧边栏 -->
    <div class="col-lg-4">
        <!-- 章节分析卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-list-ol me-2"></i>章节分析</h3>
            </div>
            <div class="card-body">
                <p>对小说的各个章节进行单独分析，获取更详细的章节级评估。</p>
                <div class="d-grid">
                    <a href="{{ url_for('new.list_chapters', novel_id=novel.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-list-ol me-2"></i>查看章节列表
                    </a>
                </div>
            </div>
        </div>

        <!-- 分析控制台卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-terminal me-2"></i>分析控制台</h3>
            </div>
            <div class="card-body">
                <p>查看分析过程的详细日志和进度信息。</p>
                <div class="d-grid">
                    <a href="{{ url_for('new.analysis_console', novel_id=novel.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-terminal me-2"></i>打开控制台
                    </a>
                </div>
            </div>
        </div>

        <!-- 导出选项卡片 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-file-export me-2"></i>导出选项</h3>
            </div>
            <div class="card-body">
                <p>将分析结果导出为不同格式，方便保存和分享。</p>
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-pdf me-2"></i>导出为PDF
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-word me-2"></i>导出为Word文档
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-alt me-2"></i>导出为Markdown
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除小说确认模态框 -->
<div class="modal fade" id="deleteNovelModal" tabindex="-1" aria-labelledby="deleteNovelModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteNovelModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除小说 "{{ novel.title }}" 吗？</p>
                <p class="text-danger">此操作不可撤销，小说及其所有分析结果将被永久删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('new.delete_novel', novel_id=novel.id) }}" method="POST">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 分析进度模态框 -->
<div class="modal fade" id="analysisProgressModal" tabindex="-1" aria-labelledby="analysisProgressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analysisProgressModalLabel">分析进行中</h5>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <h5 id="currentDimensionLabel">正在分析: <span id="currentDimension">准备中...</span></h5>

                <div class="progress mb-3">
                    <div id="dimensionProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>

                <div class="d-flex justify-content-between mb-4">
                    <span id="progressPercentage">0%</span>
                    <span id="estimatedTime">预计剩余时间: 计算中...</span>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>分析过程可能需要几分钟时间，请耐心等待。您可以关闭此窗口，稍后再回来查看结果。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">在后台继续</button>
                <button type="button" class="btn btn-danger" id="cancelAnalysisBtn">取消分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 显示/隐藏小说内容
        const toggleContentBtn = document.getElementById('toggleContentBtn');
        const novelContentPreview = document.getElementById('novelContentPreview');
        const novelContentFull = document.getElementById('novelContentFull');

        if (toggleContentBtn) {
            toggleContentBtn.addEventListener('click', function() {
                if (novelContentPreview.style.display !== 'none') {
                    novelContentPreview.style.display = 'none';
                    novelContentFull.style.display = 'block';
                    toggleContentBtn.innerHTML = '<i class="fas fa-compress-alt me-1"></i>收起';
                } else {
                    novelContentPreview.style.display = 'block';
                    novelContentFull.style.display = 'none';
                    toggleContentBtn.innerHTML = '<i class="fas fa-expand-alt me-1"></i>展开/收起';
                }
            });
        }

        // 开始分析按钮
        const startAnalysisBtn = document.getElementById('startAnalysisBtn');
        if (startAnalysisBtn) {
            startAnalysisBtn.addEventListener('click', function() {
                // 确保维度选择区域存在
                const dimensionSelectionSection = document.getElementById('dimensionSelectionSection');
                if (dimensionSelectionSection) {
                    // 显示维度选择区域并隐藏开始分析按钮
                    dimensionSelectionSection.style.display = 'block';
                    this.style.display = 'none';
                    
                    // 强制重新计算布局以确保显示正确
                    dimensionSelectionSection.offsetHeight;
                    
                    // 自动滚动到维度选择区域
                    dimensionSelectionSection.scrollIntoView({behavior: 'smooth'});
                    
                    console.log('已显示维度选择区域');
                } else {
                    console.error('无法找到维度选择区域元素');
                    alert('系统错误：无法找到维度选择区域');
                }
            });
        }

        // 分析维度全选/取消全选
        const selectAllDimensions = document.getElementById('selectAllDimensions');
        const deselectAllDimensions = document.getElementById('deselectAllDimensions');
        if (selectAllDimensions && deselectAllDimensions) {
            const dimensionCheckboxes = document.querySelectorAll('.dimension-checkbox');

            selectAllDimensions.addEventListener('click', function() {
                dimensionCheckboxes.forEach(checkbox => {
                    checkbox.checked = true;
                });
            });

            deselectAllDimensions.addEventListener('click', function() {
                dimensionCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
            });
        }

        // 确认分析按钮
        const confirmAnalysisBtn = document.getElementById('confirmAnalysisBtn');
        if (confirmAnalysisBtn) {
            confirmAnalysisBtn.addEventListener('click', function() {
                const dimensionCheckboxes = document.querySelectorAll('.dimension-checkbox:checked');
                if (dimensionCheckboxes.length === 0) {
                    alert('请至少选择一个分析维度');
                    return;
                }

                // 收集选中的维度
                const dimensions = Array.from(dimensionCheckboxes).map(checkbox => checkbox.value);

                // 显示进度模态框
                const analysisProgressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
                analysisProgressModal.show();

                // 发送分析请求
                startAnalysis(dimensions);
            });
        }

        // 单个维度分析按钮
        const startAnalysisButtons = document.querySelectorAll('.start-analysis');
        startAnalysisButtons.forEach(button => {
            button.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');

                // 显示进度模态框
                const analysisProgressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
                analysisProgressModal.show();

                // 发送分析请求
                startAnalysis([dimension]);
            });
        });

        // 取消分析按钮
        const cancelAnalysisBtn = document.getElementById('cancelAnalysisBtn');
        if (cancelAnalysisBtn) {
            cancelAnalysisBtn.addEventListener('click', function() {
                if (confirm('确定要取消当前分析吗？已完成的部分将被保存。')) {
                    cancelAnalysis();
                }
            });
        }

        // 开始分析函数
        function startAnalysis(dimensions) {
            // 更新UI
            document.getElementById('currentDimension').textContent = getDimensionName(dimensions[0]);
            document.getElementById('dimensionProgress').style.width = '0%';
            document.getElementById('progressPercentage').textContent = '0%';
            document.getElementById('estimatedTime').textContent = '预计剩余时间: 计算中...';

            // 发送API请求
            fetch('/api/novel/{{ novel.id }}/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    dimensions: dimensions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 开始轮询进度
                    pollAnalysisProgress(dimensions);
                } else {
                    alert('启动分析失败: ' + data.error);
                    bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
                }
            })
            .catch(error => {
                console.error('启动分析出错:', error);
                alert('启动分析时出错，请查看控制台了解详情');
                bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
            });
        }

        // 轮询分析进度
        function pollAnalysisProgress(dimensions) {
            let currentDimensionIndex = 0;
            let intervalId = setInterval(() => {
                if (currentDimensionIndex >= dimensions.length) {
                    clearInterval(intervalId);
                    window.location.reload();
                    return;
                }

                const currentDimension = dimensions[currentDimensionIndex];

                fetch(`/api/novel/{{ novel.id }}/analysis/${currentDimension}/progress`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新进度UI
                            document.getElementById('currentDimension').textContent = getDimensionName(currentDimension);
                            document.getElementById('dimensionProgress').style.width = `${data.progress}%`;
                            document.getElementById('progressPercentage').textContent = `${data.progress}%`;

                            if (data.estimated_time) {
                                document.getElementById('estimatedTime').textContent = `预计剩余时间: ${data.estimated_time}`;
                            }

                            // 检查是否完成
                            if (data.status === 'completed') {
                                currentDimensionIndex++;
                                if (currentDimensionIndex < dimensions.length) {
                                    // 重置进度条，准备下一个维度
                                    document.getElementById('dimensionProgress').style.width = '0%';
                                    document.getElementById('progressPercentage').textContent = '0%';
                                }
                            } else if (data.status === 'failed') {
                                alert(`分析维度 ${getDimensionName(currentDimension)} 失败: ${data.error}`);
                                currentDimensionIndex++;
                            }
                        } else {
                            console.error('获取进度失败:', data.error);
                        }
                    })
                    .catch(error => {
                        console.error('轮询进度出错:', error);
                    });
            }, 2000);
        }

        // 取消分析
        function cancelAnalysis() {
            fetch('/api/novel/{{ novel.id }}/analyze/cancel', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
                    alert('分析已取消');
                } else {
                    alert('取消分析失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('取消分析出错:', error);
                alert('取消分析时出错，请查看控制台了解详情');
            });
        }

        // 获取维度名称
        function getDimensionName(dimension) {
            const dimensionNames = {
                'language_style': '语言风格',
                'rhythm_pacing': '节奏与节奏',
                'structure': '结构分析',
                'sentence_variation': '句式变化',
                'paragraph_length': '段落长度',
                'perspective_shifts': '视角转换',
                'paragraph_flow': '段落流畅度',
                'novel_characteristics': '小说特点',
                'world_building': '世界构建',
                'chapter_outline': '章节大纲',
                'character_relationships': '人物关系',
                'opening_effectiveness': '开篇效果',
                'climax_pacing': '高潮节奏'
            };

            return dimensionNames[dimension] || dimension;
        }
    });
</script>
{% endblock %}
