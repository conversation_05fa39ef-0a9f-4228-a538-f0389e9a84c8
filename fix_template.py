#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
九猫修复工具 - 修复Jinja模板错误
"""

import os
import sys
import re
from datetime import datetime

def fix_template_issue():
    """修复analysis.html中的Jinja模板标签问题"""
    template_file = os.path.join('src', 'web', 'templates', 'analysis.html')
    backup_file = os.path.join('src', 'web', 'templates', f'analysis.html.bak_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    fixed_file = os.path.join('src', 'web', 'templates', 'analysis_fixed.html')
    
    print(f"开始修复 {template_file}...")
    
    # 确保文件存在
    if not os.path.exists(template_file):
        print(f"错误: 找不到文件 {template_file}")
        return False
    
    # 创建备份
    try:
        with open(template_file, 'r', encoding='utf-8') as src, open(backup_file, 'w', encoding='utf-8') as dst:
            content = src.read()
            dst.write(content)
        print(f"已创建备份: {backup_file}")
    except Exception as e:
        print(f"创建备份时出错: {str(e)}")
        return False
    
    # 查找并修复问题
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 找到第246行复杂的嵌套if标签
        for i, line in enumerate(lines):
            if i >= 240 and i <= 250 and 'log.level' in line and 'log.important' in line:
                # 确保这行有正确闭合的if-endif结构
                fixed_line = line.strip()
                print(f"发现复杂嵌套的行 #{i+1}: {fixed_line}")
                # 不改变这行，但检查它

        # 检查if-else-endif结构
        if_count = 0
        else_count = 0
        endif_count = 0
        for i, line in enumerate(lines):
            if '{%' in line and 'if ' in line:
                if_count += 1
            if '{%' in line and ' else' in line:
                else_count += 1
            if '{%' in line and 'endif' in line:
                endif_count += 1
        
        print(f"找到 {if_count} 个if标签, {else_count} 个else标签, {endif_count} 个endif标签")
        
        # 检查最后的if-else-endif结构
        for i in range(850, len(lines)):
            if '{%' in lines[i] and ' else' in lines[i]:
                print(f"发现else标签在行 #{i+1}: {lines[i].strip()}")
            if '{%' in lines[i] and 'endif' in lines[i]:
                print(f"发现endif标签在行 #{i+1}: {lines[i].strip()}")

        # 创建修复后的文件
        with open(fixed_file, 'w', encoding='utf-8') as f:
            in_problematic_section = False
            for i, line in enumerate(lines):
                # 如果是问题部分，确保if-else-endif标签完全匹配
                if i >= 857 and i <= 863:
                    # 这是问题部分的行号范围
                    if '{%' in line and ' else' in line:
                        # 确保else标签格式正确
                        f.write("{% else %}\n")
                        in_problematic_section = True
                    elif '{%' in line and 'endif' in line:
                        # 确保endif标签格式正确
                        f.write("{% endif %}\n")
                        in_problematic_section = False
                    else:
                        # 其他行保持不变
                        f.write(line)
                else:
                    # 非问题部分的行保持不变
                    f.write(line)
        
        print(f"已创建修复后的文件: {fixed_file}")
        
        # 替换原始文件
        os.replace(fixed_file, template_file)
        print(f"已用修复后的文件替换原始文件")
        
        return True
    except Exception as e:
        print(f"修复过程中出错: {str(e)}")
        print(f"尝试手动修复: 打开文件 {template_file} 并检查第858行附近的 {{% else %}} 标签和第862行附近的 {{% endif %}} 标签")
        return False

if __name__ == "__main__":
    print("九猫模板修复工具")
    print("-" * 30)
    success = fix_template_issue()
    if success:
        print("修复完成! 请重新启动九猫系统并测试。")
    else:
        print("修复过程中出现问题，请查看上面的错误信息。")
    print("-" * 30)

# 修复Jinja2模板语法错误的脚本
with open('src/web/templates/new_novel.html', 'r', encoding='utf-8') as f:
    content = f.read()

# 将第二个else替换为elif
fixed_content = content.replace(
    '{% else %}\n                    <div class="alert alert-warning">',
    '{% elif completed_dimensions_count > 0 %}\n                    <div class="alert alert-warning">'
)

# 写回文件
with open('src/web/templates/new_novel.html', 'w', encoding='utf-8') as f:
    f.write(fixed_content)

print("模板文件修复完成！") 