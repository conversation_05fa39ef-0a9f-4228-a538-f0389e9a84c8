/**
 * 九猫 - 章节分析维度选择修复脚本
 * 专门用于修复章节分析页面的维度选择问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[章节分析维度修复] 脚本已加载');

    // 维度列表 - 与config.py中的ANALYSIS_DIMENSIONS保持一致
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" },
        { key: "chapter_outline", name: "章纲分析" },
        { key: "outline_analysis", name: "大纲分析" },
        { key: "popular_tropes", name: "热梗统计" }
    ];

    // 修复章节分析页面的维度选择
    function fixChapterAnalysisDimensions() {
        console.log('[章节分析维度修复] 开始修复章节分析页面的维度选择');

        // 查找分析模态框
        const modal = document.getElementById('analyzeModal');
        if (!modal) {
            console.error('[章节分析维度修复] 找不到分析模态框');
            return;
        }

        // 查找模态框内容区域
        const modalBody = modal.querySelector('.modal-body');
        if (!modalBody) {
            console.error('[章节分析维度修复] 找不到模态框内容区域');
            return;
        }

        // 查找维度容器
        const dimensionsContainer = modalBody.querySelector('#dimensions-container');
        if (!dimensionsContainer) {
            console.error('[章节分析维度修复] 找不到维度容器');
            return;
        }

        // 检查是否已经有维度选项
        const existingCheckboxes = dimensionsContainer.querySelectorAll('.dimension-checkbox');
        if (existingCheckboxes.length > 0) {
            console.log(`[章节分析维度修复] 已存在 ${existingCheckboxes.length} 个维度选项，检查是否需要修复`);

            // 如果维度数量不匹配，清空并重新创建
            if (existingCheckboxes.length !== DIMENSIONS.length) {
                console.log(`[章节分析维度修复] 维度数量不匹配，清空并重新创建`);
                dimensionsContainer.innerHTML = '';
            } else {
                // 检查是否所有维度都存在
                let allDimensionsExist = true;
                DIMENSIONS.forEach(dimension => {
                    const checkbox = dimensionsContainer.querySelector(`input[value="${dimension.key}"]`);
                    if (!checkbox) {
                        allDimensionsExist = false;
                    }
                });

                if (allDimensionsExist) {
                    console.log(`[章节分析维度修复] 所有维度都存在，无需修复`);
                    return;
                } else {
                    console.log(`[章节分析维度修复] 部分维度缺失，清空并重新创建`);
                    dimensionsContainer.innerHTML = '';
                }
            }
        }

        // 创建所有维度选项
        DIMENSIONS.forEach((dimension, index) => {
            // 创建新的选择项
            const checkboxContainer = document.createElement('div');
            checkboxContainer.className = 'form-check mb-2';

            const checkbox = document.createElement('input');
            checkbox.className = 'form-check-input dimension-checkbox';
            checkbox.type = 'checkbox';
            checkbox.name = 'dimensions';
            checkbox.value = dimension.key;
            checkbox.id = `dimension-${index}`;
            checkbox.checked = true; // 默认选中

            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.setAttribute('for', `dimension-${index}`);
            label.textContent = dimension.name;

            // 添加到容器中
            checkboxContainer.appendChild(checkbox);
            checkboxContainer.appendChild(label);
            dimensionsContainer.appendChild(checkboxContainer);
        });

        console.log(`[章节分析维度修复] 成功添加了 ${DIMENSIONS.length} 个维度选项`);

        // 设置全选选项的事件处理
        const selectAllCheckbox = modalBody.querySelector('#select-all-dimensions');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = dimensionsContainer.querySelectorAll('.dimension-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        }
    }

    // 监听模态框打开事件
    function setupModalListener() {
        // 使用jQuery监听模态框显示事件
        if (typeof $ !== 'undefined') {
            $(document).on('shown.bs.modal', '#analyzeModal', function() {
                console.log('[章节分析维度修复] 检测到模态框打开 (jQuery)');
                setTimeout(fixChapterAnalysisDimensions, 100);
            });
            console.log('[章节分析维度修复] 已设置jQuery模态框监听器');
        }

        // 使用原生JavaScript监听模态框显示
        document.addEventListener('click', function(event) {
            if (event.target.matches('[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]') ||
                event.target.closest('[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]')) {
                console.log('[章节分析维度修复] 检测到分析按钮点击');
                setTimeout(function() {
                    const modal = document.getElementById('analyzeModal');
                    if (modal && modal.classList.contains('show')) {
                        console.log('[章节分析维度修复] 检测到模态框打开 (原生JS)');
                        setTimeout(fixChapterAnalysisDimensions, 100);
                    }
                }, 300);
            }
        });
        console.log('[章节分析维度修复] 已设置原生JS模态框监听器');

        // 使用MutationObserver监听模态框显示
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' &&
                    mutation.attributeName === 'class' &&
                    mutation.target.id === 'analyzeModal' &&
                    mutation.target.classList.contains('show')) {
                    console.log('[章节分析维度修复] 检测到模态框打开 (MutationObserver)');
                    setTimeout(fixChapterAnalysisDimensions, 100);
                }
            });
        });

        const analyzeModal = document.getElementById('analyzeModal');
        if (analyzeModal) {
            observer.observe(analyzeModal, { attributes: true });
            console.log('[章节分析维度修复] 已设置MutationObserver模态框监听器');
        }
    }

    // 初始化
    function init() {
        console.log('[章节分析维度修复] 初始化中...');

        // 检查当前是否有打开的模态框
        const openModal = document.getElementById('analyzeModal');
        if (openModal && openModal.classList.contains('show')) {
            console.log('[章节分析维度修复] 检测到已打开的分析模态框');
            fixChapterAnalysisDimensions();
        }

        // 设置模态框监听器
        setupModalListener();

        // 导出全局函数
        window.fixChapterAnalysisDimensions = fixChapterAnalysisDimensions;

        console.log('[章节分析维度修复] 初始化完成');
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    console.log('[章节分析维度修复] 脚本加载完成');
})();
