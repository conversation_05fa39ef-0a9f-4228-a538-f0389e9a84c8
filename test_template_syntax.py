#!/usr/bin/env python3
"""
测试模板语法是否正确
"""

from jinja2 import Template, Environment

# 测试当前的模板语法
template_content = '''
<script>
    // 预先定义小说标题 - 使用JSON编码避免特殊字符问题
    var novelTitle = {{ novel.title|tojson }};

    // 预先定义维度数据 - 使用JSON编码避免特殊字符问题
    var dimensionData = {};
    {% for dimension_key, dimension_name in dimensions.items() %}
    dimensionData[{{ dimension_key|tojson }}] = {
        name: {{ dimension_name|tojson }},
        isAvailable: {% if dimension_key in available_dimensions %}true{% else %}false{% endif %}
    };
    {% endfor %}
</script>
'''

def test_template_syntax():
    """测试模板语法"""
    try:
        env = Environment()
        template = env.from_string(template_content)
        
        # 测试数据
        test_data = {
            'novel': {'title': '测试小说"带引号"的标题'},
            'dimensions': {
                'language_style': '语言风格',
                'plot_structure': '情节结构',
                'character_development': '角色发展'
            },
            'available_dimensions': ['language_style', 'plot_structure']
        }
        
        # 渲染模板
        result = template.render(**test_data)
        print("✅ 模板语法正确！")
        print("渲染结果:")
        print(result)
        
        # 检查JavaScript语法
        import re
        js_content = re.search(r'<script>(.*?)</script>', result, re.DOTALL)
        if js_content:
            js_code = js_content.group(1).strip()
            print("\n提取的JavaScript代码:")
            print(js_code)
            
            # 简单的语法检查
            if 'var novelTitle =' in js_code and 'dimensionData[' in js_code:
                print("✅ JavaScript变量定义正确")
            else:
                print("❌ JavaScript变量定义可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板语法错误: {e}")
        return False

if __name__ == '__main__':
    test_template_syntax()
