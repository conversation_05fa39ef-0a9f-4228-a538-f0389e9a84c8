/**
 * 九猫小说分析写作系统v3.0 - 测试功能JavaScript
 */

// 全局变量
let novelId = null;
let analysisInProgress = false;
let analysisTimer = null;
let analysisStartTime = null;
let generatedChapters = [];

// 在DOM加载完成后执行
$(document).ready(function() {
    console.log('九猫测试功能已加载');

    // 初始化页面
    initTestPage();

    // 添加日志
    addLogEntry('info', '测试功能初始化完成');
});

/**
 * 初始化测试页面
 */
function initTestPage() {
    // 绑定上传方式切换事件
    $('input[name="uploadType"]').change(function() {
        const uploadType = $(this).val();
        if (uploadType === 'file') {
            $('#fileUploadArea').show();
            $('#textUploadArea').hide();
        } else {
            $('#fileUploadArea').hide();
            $('#textUploadArea').show();
        }
    });

    // 绑定表单提交事件
    $('#uploadForm').submit(function(e) {
        e.preventDefault();
        uploadNovel();
    });

    // 绑定开始分析按钮事件
    $('#startAnalysisBtn').click(function() {
        startAnalysisAndWriting();
    });

    // 绑定下载结果按钮事件
    $('#downloadResultBtn').click(function() {
        downloadResults();
    });

    // 绑定重新测试按钮事件
    $('#resetTestBtn').click(function() {
        resetTest();
    });

    // 初始化Markdown渲染器
    function initMarked() {
        if (typeof marked !== 'undefined') {
            // 确保 parse 方法存在
            if (!marked.parse && typeof marked === 'function') {
                console.log('添加 marked.parse 方法');
                marked.parse = function(text) {
                    return marked(text);
                };
            }

            // 设置选项
            if (marked.setOptions) {
                marked.setOptions({
                    breaks: true,
                    gfm: true
                });
            }

            addLogEntry('info', 'Marked.js 初始化完成');
        } else {
            console.warn('Marked.js未加载，Markdown渲染可能不可用');
            addLogEntry('warn', 'Marked.js未加载，Markdown渲染可能不可用');

            // 500ms后重试
            setTimeout(initMarked, 500);
        }
    }

    // 初始化Marked
    initMarked();
}

/**
 * 上传小说
 */
function uploadNovel() {
    // 显示加载状态
    $('#uploadBtn').html('<i class="fas fa-spinner fa-spin mr-1"></i> 上传中...').prop('disabled', true);
    addLogEntry('info', '开始上传小说');

    // 获取表单数据
    const formData = new FormData();
    formData.append('title', $('#title').val());
    formData.append('author', $('#author').val());

    const uploadType = $('input[name="uploadType"]:checked').val();
    if (uploadType === 'file') {
        const fileInput = $('#file')[0];
        if (fileInput.files.length > 0) {
            formData.append('file', fileInput.files[0]);
        } else {
            showError('请选择要上传的文件');
            return;
        }
    } else {
        const content = $('#content').val().trim();
        if (content) {
            formData.append('content', content);
        } else {
            showError('请输入小说内容');
            return;
        }
    }

    // 发送上传请求
    $.ajax({
        url: '/api/test/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                novelId = response.novel_id;
                addLogEntry('info', `小说上传成功，ID: ${novelId}`);

                // 显示分析部分
                $('#uploadSection').hide();
                $('#analysisSection').show();

                // 更新UI
                $('#uploadBtn').html('<i class="fas fa-check mr-1"></i> 上传成功').prop('disabled', false);
            } else {
                showError(response.error || '上传失败');
            }
        },
        error: function(xhr) {
            showError('上传过程中发生错误: ' + xhr.status + ' ' + xhr.statusText);
        }
    });
}

/**
 * 开始分析和写作
 */
function startAnalysisAndWriting() {
    if (!novelId) {
        showError('请先上传小说');
        return;
    }

    if (analysisInProgress) {
        showError('分析已在进行中，请等待完成');
        return;
    }

    // 更新UI
    $('#startAnalysisBtn').html('<i class="fas fa-spinner fa-spin mr-1"></i> 处理中...').prop('disabled', true);
    $('.progress-container').show();
    $('#analysisStatus').text('正在准备分析...');
    $('.analysis-progress').css('width', '0%');

    // 显示结果部分
    $('#resultSection').show();

    // 记录开始时间
    analysisStartTime = new Date();
    analysisInProgress = true;

    addLogEntry('info', '开始一键分析写作流程');

    // 获取选择的模型、提示词模板和小说类型
    const selectedModel = $('#modelSelect').val();
    const selectedPromptTemplate = $('#promptTemplateSelect').val();
    const selectedNovelType = $('#novelTypeSelect').val();

    // 发送分析请求
    $.ajax({
        url: `/api/test/analyze/${novelId}`,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            model: selectedModel,
            prompt_template: selectedPromptTemplate,
            novel_type: selectedNovelType
        }),
        success: function(response) {
            if (response.success) {
                addLogEntry('info', '分析任务已创建，任务ID: ' + response.task_id);

                // 开始轮询分析状态
                pollAnalysisStatus(response.task_id);
            } else {
                analysisInProgress = false;
                showError(response.error || '创建分析任务失败');
                $('#startAnalysisBtn').html('<i class="fas fa-magic mr-1"></i> 开始一键分析写作').prop('disabled', false);
            }
        },
        error: function(xhr) {
            analysisInProgress = false;
            showError('创建分析任务时出错: ' + xhr.status + ' ' + xhr.statusText);
            $('#startAnalysisBtn').html('<i class="fas fa-magic mr-1"></i> 开始一键分析写作').prop('disabled', false);
        }
    });
}

/**
 * 轮询分析状态
 */
function pollAnalysisStatus(taskId) {
    if (analysisTimer) {
        clearTimeout(analysisTimer);
    }

    analysisTimer = setTimeout(function() {
        $.ajax({
            url: `/api/test/status/${taskId}`,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    // 更新进度
                    const progress = response.progress || 0;
                    $('.analysis-progress').css('width', `${progress}%`);

                    // 格式化状态显示，突出显示维度和字数信息
                    let statusText = response.status || '正在分析中...';
                    let formattedStatus = statusText;

                    // 如果状态包含维度信息或字数信息，添加样式
                    if (statusText.includes(': ')) {
                        const parts = statusText.split(': ');
                        if (parts.length === 2) {
                            // 检查是否包含字数信息
                            if (statusText.includes('字')) {
                                // 写作状态，包含字数
                                const chapterPart = parts[0];
                                const wordCountPart = parts[1];
                                formattedStatus = `${chapterPart}: <span class="badge badge-info">${wordCountPart}</span>`;
                            } else {
                                // 分析状态，包含维度
                                const chapterPart = parts[0];
                                const dimensionPart = parts[1];
                                formattedStatus = `${chapterPart}: <span class="badge badge-primary">${dimensionPart}</span>`;
                            }
                        }
                    }

                    $('#analysisStatus').html(formattedStatus);

                    // 更新进度条颜色
                    if (progress < 30) {
                        $('.analysis-progress').removeClass('bg-success bg-info').addClass('bg-warning');
                    } else if (progress < 70) {
                        $('.analysis-progress').removeClass('bg-success bg-warning').addClass('bg-info');
                    } else {
                        $('.analysis-progress').removeClass('bg-info bg-warning').addClass('bg-success');
                    }

                    addLogEntry('info', `分析进度: ${progress}%, 状态: ${statusText}`);

                    if (response.completed) {
                        // 分析完成
                        analysisInProgress = false;
                        const duration = Math.round((new Date() - analysisStartTime) / 1000);

                        // 检查是否有错误
                        if (response.error) {
                            // 任务失败
                            addLogEntry('error', `分析写作失败: ${response.error}，耗时: ${duration}秒`);
                            $('#startAnalysisBtn').html('<i class="fas fa-exclamation-triangle mr-1"></i> 处理失败').prop('disabled', false);
                            $('#analysisStatus').text('分析写作失败');
                            $('.analysis-progress').css('width', '100%').removeClass('progress-bar-animated').addClass('bg-danger');

                            // 显示错误信息
                            $('#resultSection').show();
                            $('#analysisContent').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>分析写作失败: ${response.error}</div>`);
                            $('#writingContent').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>由于分析失败，无法生成内容。</div>`);
                        } else {
                            // 任务成功完成
                            // 检查是否有生成的小说ID
                            let completionMessage = `分析写作完成，耗时: ${duration}秒`;
                            if (response.results && response.results.generated_novel_id) {
                                completionMessage += `，生成的内容已保存到内容仓库，ID: ${response.results.generated_novel_id}`;
                            }
                            addLogEntry('info', completionMessage);

                            // 更新UI
                            $('#startAnalysisBtn').html('<i class="fas fa-check mr-1"></i> 处理完成').prop('disabled', false);

                            // 更新状态文本，包含内容仓库链接
                            if (response.results && response.results.generated_novel_id) {
                                $('#analysisStatus').html(`分析写作已完成，<a href="/v3/content-repository" class="alert-link">点击查看内容仓库</a>`);
                            } else {
                                $('#analysisStatus').text('分析写作已完成');
                            }

                            $('.analysis-progress').css('width', '100%').removeClass('progress-bar-animated');

                            // 显示结果区域
                            $('#resultSection').show();

                            // 显示结果
                            displayResults(response.results);
                        }
                    } else {
                        // 继续轮询
                        pollAnalysisStatus(taskId);
                    }
                } else {
                    analysisInProgress = false;
                    showError(response.error || '获取分析状态失败');
                    $('#startAnalysisBtn').html('<i class="fas fa-magic mr-1"></i> 开始一键分析写作').prop('disabled', false);
                }
            },
            error: function(xhr) {
                analysisInProgress = false;
                showError('获取分析状态时出错: ' + xhr.status + ' ' + xhr.statusText);
                $('#startAnalysisBtn').html('<i class="fas fa-magic mr-1"></i> 开始一键分析写作').prop('disabled', false);
            }
        });
    }, 2000); // 每2秒轮询一次
}

/**
 * 显示分析和写作结果
 */
function displayResults(results) {
    // 检查results是否存在，如果不存在则显示默认信息
    if (!results) {
        addLogEntry('warn', '任务已完成，但未获取到结果数据，可能是任务执行过程中出现了问题');

        // 显示默认的结果界面
        $('#analysisContent').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>任务已完成，但未获取到分析结果数据。请检查日志或重新运行分析。</div>');
        $('#writingContent').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>任务已完成，但未获取到生成内容数据。请检查日志或重新运行分析。</div>');

        // 显示结果区域
        $('#resultSection').show();
        return;
    }

    // 保存生成的章节
    generatedChapters = results.generated_chapters || [];

    // 显示分析结果
    let analysisHtml = '<h4>小说分析结果</h4>';

    if (results.analysis_summary) {
        analysisHtml += marked.parse(results.analysis_summary);
    }

    if (results.dimensions && results.dimensions.length > 0) {
        analysisHtml += '<h5>维度分析</h5><div class="dimension-list">';
        results.dimensions.forEach(function(dimension) {
            analysisHtml += `<div class="dimension-item" data-dimension="${dimension.key}">${dimension.name}</div>`;
        });
        analysisHtml += '</div>';

        // 添加维度详情区域
        analysisHtml += `
            <div id="dimensionDetail" class="mt-3">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="dimensionTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="result-tab" data-toggle="tab" data-bs-toggle="tab" href="#resultTab" role="tab" aria-controls="resultTab" aria-selected="true">分析结果</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="reasoning-tab" data-toggle="tab" data-bs-toggle="tab" href="#reasoningTab" role="tab" aria-controls="reasoningTab" aria-selected="false">推理过程</a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="dimensionTabContent">
                            <div class="tab-pane fade show active" id="resultTab" role="tabpanel" aria-labelledby="result-tab">
                                <p class="text-muted">请选择一个维度查看分析结果</p>
                            </div>
                            <div class="tab-pane fade" id="reasoningTab" role="tabpanel" aria-labelledby="reasoning-tab">
                                <p class="text-muted">请选择一个维度查看推理过程</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;
    }

    $('#analysisContent').html(analysisHtml);

    // 绑定维度点击事件
    $('.dimension-item').click(function() {
        const dimensionKey = $(this).data('dimension');
        $('.dimension-item').removeClass('selected');
        $(this).addClass('selected');

        // 获取并显示维度详情
        getDimensionDetail(dimensionKey);
    });

    // 显示写作结果
    let writingHtml = '<h4>生成的新作品</h4>';

    // 如果有生成的小说ID，添加内容仓库链接
    if (results.generated_novel_id) {
        writingHtml += `
        <div class="alert alert-success mb-3">
            <i class="fas fa-check-circle me-2"></i>
            生成的内容已保存到内容仓库！
            <div class="mt-2">
                <a href="/v3/content-repository" class="btn btn-sm btn-primary me-2">
                    <i class="fas fa-archive me-1"></i>查看内容仓库
                </a>
                <a href="/v3/novel/${results.generated_novel_id}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-book me-1"></i>直接查看生成的小说
                </a>
            </div>
        </div>`;

        // 添加章节字数对比信息
        if (results.chapter_word_count_comparison && results.chapter_word_count_comparison.length > 0) {
            writingHtml += `
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">章节字数对比</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>章节</th>
                                    <th>原文字数</th>
                                    <th>生成字数</th>
                                    <th>差异</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>`;

            // 添加每个章节的字数对比信息
            results.chapter_word_count_comparison.forEach(function(chapter) {
                // 根据差异百分比确定状态
                let statusClass = 'success';
                let statusIcon = 'check-circle';
                let statusText = '良好';

                if (chapter.difference_percent > 30) {
                    statusClass = 'danger';
                    statusIcon = 'exclamation-triangle';
                    statusText = '差异过大';
                } else if (chapter.difference_percent > 15) {
                    statusClass = 'warning';
                    statusIcon = 'exclamation-circle';
                    statusText = '差异较大';
                }

                writingHtml += `
                <tr>
                    <td>${chapter.chapter_title}</td>
                    <td>${chapter.original_word_count}字</td>
                    <td>${chapter.generated_word_count}字</td>
                    <td>${chapter.difference_percent}%</td>
                    <td><span class="badge bg-${statusClass}"><i class="fas fa-${statusIcon} me-1"></i>${statusText}</span></td>
                </tr>`;
            });

            writingHtml += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>`;
        }
    }

    if (generatedChapters.length > 0) {
        writingHtml += '<div class="row"><div class="col-md-4"><div class="chapter-list">';
        generatedChapters.forEach(function(chapter, index) {
            writingHtml += `<div class="chapter-item" data-index="${index}">${chapter.title || '第' + (index + 1) + '章'}</div>`;
        });
        writingHtml += '</div></div>';
        writingHtml += '<div class="col-md-8"><div id="chapterContent" class="writing-result">选择左侧章节查看内容</div></div></div>';
    } else {
        writingHtml += '<p>未生成任何章节内容</p>';
    }

    $('#writingContent').html(writingHtml);

    // 绑定章节点击事件
    $('.chapter-item').click(function() {
        const index = $(this).data('index');
        $('.chapter-item').removeClass('selected');
        $(this).addClass('selected');

        // 显示章节内容
        if (generatedChapters[index]) {
            $('#chapterContent').html(marked.parse(generatedChapters[index].content));
        }
    });

    // 默认选中第一个章节
    if (generatedChapters.length > 0) {
        $('.chapter-item').first().click();
    }

    // 默认选中第一个维度
    if (results.dimensions && results.dimensions.length > 0) {
        $('.dimension-item').first().click();
    }
}

/**
 * 获取维度详情
 */
function getDimensionDetail(dimensionKey) {
    // 显示加载状态
    $('#resultTab').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载分析结果中...</div>');
    $('#reasoningTab').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载推理过程中...</div>');

    $.ajax({
        url: `/api/test/dimension/${novelId}/${dimensionKey}`,
        type: 'GET',
        success: function(response) {
            if (response.success && response.detail) {
                // 解析返回的详情内容
                const detailText = response.detail;

                // 分离分析结果和推理过程
                let resultContent = '';
                let reasoningContent = '';

                // 检查是否包含推理过程部分
                if (detailText.includes('## 分析思路')) {
                    // 提取推理过程
                    const reasoningMatch = detailText.match(/## 分析思路\n([\s\S]*?)(?=## 分析结果|$)/);
                    if (reasoningMatch && reasoningMatch[1]) {
                        reasoningContent = reasoningMatch[1].trim();
                    }

                    // 提取分析结果
                    const resultMatch = detailText.match(/## 分析结果\n([\s\S]*?)$/);
                    if (resultMatch && resultMatch[1]) {
                        resultContent = resultMatch[1].trim();
                    } else {
                        // 如果没有找到分析结果部分，使用除了推理过程之外的所有内容
                        resultContent = detailText.replace(/# .*?\n## 分析思路\n[\s\S]*?(?=## 分析结果|$)/g, '').trim();
                    }
                } else {
                    // 如果没有推理过程部分，所有内容都是分析结果
                    resultContent = detailText.replace(/# .*?\n/g, '').trim();
                }

                // 显示分析结果
                if (resultContent) {
                    $('#resultTab').html(marked.parse(resultContent));
                } else {
                    $('#resultTab').html('<div class="alert alert-warning">未找到分析结果</div>');
                }

                // 显示推理过程
                if (reasoningContent) {
                    $('#reasoningTab').html(marked.parse(reasoningContent));
                } else {
                    $('#reasoningTab').html('<div class="alert alert-warning">未找到推理过程</div>');
                }
            } else {
                $('#resultTab').html('<div class="alert alert-warning">未找到该维度的详细分析</div>');
                $('#reasoningTab').html('<div class="alert alert-warning">未找到该维度的推理过程</div>');
            }
        },
        error: function(xhr) {
            $('#resultTab').html('<div class="alert alert-danger">获取维度详情失败</div>');
            $('#reasoningTab').html('<div class="alert alert-danger">获取维度详情失败</div>');
        }
    });
}

/**
 * 下载结果
 */
function downloadResults() {
    if (!novelId) {
        showError('没有可下载的结果');
        return;
    }

    window.location.href = `/api/test/download/${novelId}`;
}

/**
 * 重置测试
 */
function resetTest() {
    // 重置全局变量
    novelId = null;
    analysisInProgress = false;
    if (analysisTimer) {
        clearTimeout(analysisTimer);
        analysisTimer = null;
    }
    generatedChapters = [];

    // 重置UI
    $('#uploadForm')[0].reset();
    $('#uploadBtn').html('<i class="fas fa-upload mr-1"></i> 上传小说').prop('disabled', false);
    $('#startAnalysisBtn').html('<i class="fas fa-magic mr-1"></i> 开始一键分析写作').prop('disabled', false);
    $('.progress-container').hide();
    $('.analysis-progress').css('width', '0%').addClass('progress-bar-animated');

    // 显示上传部分，隐藏其他部分
    $('#uploadSection').show();
    $('#analysisSection').hide();
    $('#resultSection').hide();

    // 清空结果
    $('#analysisContent').html('分析结果将显示在这里...');
    $('#writingContent').html('生成的新作品将显示在这里...');

    addLogEntry('info', '测试已重置');
}

/**
 * 显示错误信息
 */
function showError(message) {
    alert(message);
    addLogEntry('error', message);
}

/**
 * 添加日志条目
 */
function addLogEntry(level, message) {
    const timestamp = new Date().toLocaleTimeString();
    const logClass = level === 'error' ? 'log-error' : (level === 'warn' ? 'log-warn' : 'log-info');
    const logHtml = `<div class="log-entry ${logClass}">[${timestamp}] ${level.toUpperCase()}: ${message}</div>`;

    $('#logContent').append(logHtml);

    // 滚动到底部
    const logContainer = document.getElementById('logContent');
    if (logContainer) {
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    // 输出到控制台
    console[level](`[测试功能] ${message}`);
}
