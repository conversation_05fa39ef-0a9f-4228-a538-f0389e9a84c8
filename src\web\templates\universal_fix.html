{% extends "base.html" %}

{% block title %}{{ dimension|capitalize if dimension else '分析' }} - 通用修复版{% endblock %}

{% block head %}
{{ super() }}
<!-- 加载修复脚本 -->
<script src="{{ url_for('static', filename='js/json-parse-fix-enhanced.js') }}"></script>
<script src="{{ url_for('static', filename='js/node-replace-fix.js') }}"></script>
<script src="{{ url_for('static', filename='js/universal-chart-fix.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1>{{ dimension_name|default(dimension|capitalize, true) }} 分析</h1>
        <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
            返回小说页面
        </a>
    </div>

    <div class="row mt-4">
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">分析结果</h5>
                </div>
                <div class="card-body">
                    <div class="visualization-container">
                        <h6>分析可视化</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="radarChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="barChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析过程记录区域 -->
                    <div class="analysis-process-container mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">分析过程记录</h6>
                            <button class="btn btn-sm btn-outline-primary" id="toggleAnalysisProcess">展开/折叠</button>
                        </div>
                        <div id="analysisProcessContent" class="border rounded p-3" style="display: none; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 0.85rem; background-color: #f8f9fa;">
                            <div class="text-center text-muted my-3">正在加载分析过程记录...</div>
                        </div>
                    </div>

                    <div class="analysis-content markdown-content">
                        {{ analysis_result.content|safe if analysis_result and analysis_result.content else '# 分析过程中出错\n\n## 错误详情\n分析维度 **' + dimension + '** 时遇到了问题。\n\n## 错误信息\n```\n无法获取分析结果\n```\n\n## 建议操作\n请尝试以下解决方法：\n1. 刷新页面并重新尝试分析\n2. 检查小说文本是否过长或包含特殊字符\n3. 确认API连接正常' }}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">分析信息</h5>
                </div>
                <div class="card-body">
                    <p><strong>小说:</strong> {{ novel.title }}</p>
                    <p><strong>作者:</strong> {{ novel.author }}</p>
                    <p><strong>分析时间:</strong> {{ analysis_result.created_at.strftime('%Y-%m-%d %H:%M') if analysis_result and analysis_result.created_at else '未知' }}</p>
                    
                    <div class="mt-3">
                        <button class="btn btn-primary w-100 reanalyze-btn" 
                                data-dimension="{{ dimension }}"
                                data-novel-id="{{ novel.id }}">
                            <i class="fas fa-sync"></i> 重新分析
                        </button>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">分析元数据</h5>
                </div>
                <div class="card-body">
                    {% if analysis_result and analysis_result.metadata %}
                        {% set metadata = analysis_result.metadata %}
                        <p><strong>处理时间:</strong> {{ metadata.processing_time|round(2) if metadata.processing_time else 0 }} 秒</p>
                        <p><strong>分块数量:</strong> {{ metadata.chunk_count if metadata.chunk_count else 0 }}</p>
                        <p><strong>API调用:</strong> {{ metadata.api_calls if metadata.api_calls else 0 }} 次</p>
                        <p><strong>令牌使用:</strong> {{ metadata.tokens_used if metadata.tokens_used else 0 }}</p>
                        <p><strong>估计成本:</strong> {{ metadata.cost|round(4) if metadata.cost else 0 }} 元</p>
                    {% else %}
                        <p>无元数据信息</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析进度模态框 -->
<div class="modal fade" id="analysisProgressModal" tabindex="-1" aria-labelledby="analysisProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analysisProgressModalLabel">分析进行中</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="mt-3" id="progressMessage">
                    正在准备分析...
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelAnalysisBtn">取消分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 绑定重新分析按钮事件
        document.querySelectorAll('.reanalyze-btn').forEach(button => {
            button.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');
                const novelId = this.getAttribute('data-novel-id');
                
                if (confirm(`确定要重新分析${dimension}维度吗？这将覆盖现有的分析结果。`)) {
                    startAnalysis(novelId, dimension);
                }
            });
        });
        
        // 获取分析过程记录
        fetchAnalysisProcess();
        
        // 分析过程记录的展开/折叠功能
        const toggleAnalysisProcessBtn = document.getElementById('toggleAnalysisProcess');
        const analysisProcessContent = document.getElementById('analysisProcessContent');
        
        toggleAnalysisProcessBtn.addEventListener('click', function() {
            if (analysisProcessContent.style.display === 'none') {
                analysisProcessContent.style.display = 'block';
                toggleAnalysisProcessBtn.textContent = '折叠';
            } else {
                analysisProcessContent.style.display = 'none';
                toggleAnalysisProcessBtn.textContent = '展开';
            }
        });
        
        // 确保图表正确初始化
        if (typeof fixAllCharts === 'function') {
            setTimeout(fixAllCharts, 500);
        }
    });
    
    // 开始分析
    function startAnalysis(novelId, dimension) {
        console.log(`开始分析小说 ${novelId} 的 ${dimension} 维度`);
        
        // 显示进度模态框
        const progressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
        progressModal.show();
        
        // 重置进度条
        const progressBar = document.querySelector('#analysisProgressModal .progress-bar');
        progressBar.style.width = '0%';
        document.getElementById('progressMessage').textContent = '正在准备分析...';
        
        // 发送分析请求
        fetch(`/api/analysis/start`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                novel_id: novelId,
                dimension: dimension
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('分析请求已发送:', data);
            
            if (data.success) {
                // 开始轮询分析进度
                const taskId = data.task_id;
                pollAnalysisProgress(taskId, novelId, dimension);
                
                // 绑定取消分析按钮
                document.getElementById('cancelAnalysisBtn').onclick = function() {
                    cancelAnalysis(taskId);
                    progressModal.hide();
                };
            } else {
                alert(`分析请求失败: ${data.message}`);
                progressModal.hide();
            }
        })
        .catch(error => {
            console.error('分析请求错误:', error);
            alert(`请求错误: ${error.message}`);
            progressModal.hide();
        });
    }
    
    // 轮询分析进度
    function pollAnalysisProgress(taskId, novelId, dimension) {
        console.log(`轮询分析任务 ${taskId} 的进度`);
        
        const progressBar = document.querySelector('#analysisProgressModal .progress-bar');
        const progressMessage = document.getElementById('progressMessage');
        
        // 设置轮询间隔
        const pollInterval = setInterval(() => {
            fetch(`/api/analysis/progress/${taskId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('分析进度:', data);
                
                if (data.success) {
                    // 更新进度条
                    const progress = data.progress || 0;
                    progressBar.style.width = `${progress}%`;
                    progressMessage.textContent = data.message || '分析中...';
                    
                    // 检查是否完成
                    if (data.status === 'completed') {
                        clearInterval(pollInterval);
                        
                        // 显示完成消息
                        progressBar.style.width = '100%';
                        progressMessage.textContent = '分析完成！正在刷新页面...';
                        
                        // 刷新页面以显示新结果
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else if (data.status === 'failed') {
                        clearInterval(pollInterval);
                        
                        // 显示错误消息
                        progressBar.classList.remove('bg-primary');
                        progressBar.classList.add('bg-danger');
                        progressMessage.textContent = `分析失败: ${data.message}`;
                        
                        // 不自动关闭模态框，让用户手动关闭
                    }
                } else {
                    console.error('获取进度失败:', data.message);
                }
            })
            .catch(error => {
                console.error('轮询进度错误:', error);
                clearInterval(pollInterval);
                
                // 显示错误消息
                progressBar.classList.remove('bg-primary');
                progressBar.classList.add('bg-danger');
                progressMessage.textContent = `获取进度错误: ${error.message}`;
            });
        }, 2000); // 每2秒轮询一次
    }
    
    // 取消分析
    function cancelAnalysis(taskId) {
        console.log(`取消分析任务 ${taskId}`);
        
        fetch(`/api/analysis/cancel/${taskId}`, {
            method: 'POST'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('取消分析请求结果:', data);
            
            if (data.success) {
                console.log('分析已取消');
            } else {
                console.error('取消分析失败:', data.message);
            }
        })
        .catch(error => {
            console.error('取消分析请求错误:', error);
            alert(`请求错误: ${error.message}`);
        });
    }
    
    // 获取分析过程记录的函数
    function fetchAnalysisProcess() {
        const analysisProcessContent = document.getElementById('analysisProcessContent');
        
        // 显示加载中
        analysisProcessContent.innerHTML = '<div class="text-center text-muted my-3">正在加载分析过程记录...</div>';
        
        // 构建API URL
        const url = '/api/analysis/process?novel_id={{ novel.id }}&dimension={{ dimension }}';
        
        // 获取数据
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.process) {
                    // 处理分析过程数据
                    displayAnalysisProcess(data.process);
                } else {
                    throw new Error(data.message || '获取分析过程失败');
                }
            })
            .catch(error => {
                // 显示错误信息
                analysisProcessContent.innerHTML = 
                    '<div class="alert alert-warning">' +
                        '<i class="bi bi-exclamation-triangle-fill me-2"></i>' +
                        '获取分析过程记录失败: ' + error.message +
                        '<hr>' +
                        '<p class="mb-0">备选方案: <a href="#" id="fetchProcessFromLogs" class="alert-link">从日志提取分析过程</a></p>' +
                    '</div>';
                
                // 添加从日志提取过程的事件处理
                document.getElementById('fetchProcessFromLogs').addEventListener('click', function(e) {
                    e.preventDefault();
                    extractProcessFromLogs();
                });
            });
    }
    
    // 从日志中提取分析过程
    function extractProcessFromLogs() {
        const analysisProcessContent = document.getElementById('analysisProcessContent');
        analysisProcessContent.innerHTML = '<div class="text-center text-muted my-3">正在从日志提取分析过程...</div>';
        
        // 获取所有日志
        fetch('/api/analysis/logs?novel_id={{ novel.id }}&level=all&limit=500&dimension={{ dimension }}')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.logs && data.logs.length > 0) {
                    // 过滤与分析过程相关的日志
                    const processLogs = data.logs.filter(log => 
                        log.message.includes('分析中') || 
                        log.message.includes('处理') || 
                        log.message.includes('计算') || 
                        log.message.includes('分块') || 
                        log.message.includes('API') || 
                        log.message.includes('完成') ||
                        log.message.includes('进度') ||
                        log.level === 'debug'
                    );
                    
                    if (processLogs.length > 0) {
                        // 构建分析过程HTML
                        let html = '<div class="process-log">';
                        processLogs.forEach(log => {
                            const logClass = 
                                log.level === 'error' ? 'text-danger' : 
                                log.level === 'warning' ? 'text-warning' : 
                                log.level === 'debug' ? 'text-muted' : '';
                            
                            html += '<div class="log-entry ' + logClass + '">' +
                                    '<span class="log-time">[' + log.timestamp + ']</span> ' +
                                    '<span class="log-level">' + log.level.toUpperCase() + ':</span> ' +
                                    '<span class="log-message">' + log.message + '</span>' +
                                    '</div>';
                        });
                        html += '</div>';
                        
                        analysisProcessContent.innerHTML = html;
                    } else {
                        analysisProcessContent.innerHTML = '<div class="alert alert-info">未找到与分析过程相关的日志记录</div>';
                    }
                } else {
                    analysisProcessContent.innerHTML = '<div class="alert alert-warning">未找到分析日志或日志为空</div>';
                }
            })
            .catch(error => {
                analysisProcessContent.innerHTML = '<div class="alert alert-danger">获取日志失败: ' + error.message + '</div>';
            });
    }
    
    // 显示分析过程
    function displayAnalysisProcess(processData) {
        const analysisProcessContent = document.getElementById('analysisProcessContent');
        
        // 检查是否为字符串，如果是则尝试解析JSON
        if (typeof processData === 'string') {
            try {
                processData = JSON.parse(processData);
            } catch (e) {
                // 如果不是有效的JSON，就当作纯文本处理
            }
        }
        
        // 根据数据类型显示不同内容
        if (typeof processData === 'object' && processData !== null) {
            // 对象类型数据
            if (Array.isArray(processData)) {
                // 数组类型 - 显示为步骤列表
                let html = '<ol class="process-steps">';
                processData.forEach((step, index) => {
                    html += '<li class="process-step">';
                    if (typeof step === 'object') {
                        html += '<strong>' + (step.title || '步骤 ' + (index + 1)) + '</strong><br>';
                        if (step.description) html += step.description + '<br>';
                        if (step.timestamp) html += '<small class="text-muted">时间: ' + step.timestamp + '</small><br>';
                        if (step.duration) html += '<small class="text-muted">耗时: ' + step.duration + '</small>';
                    } else {
                        html += step;
                    }
                    html += '</li>';
                });
                html += '</ol>';
                analysisProcessContent.innerHTML = html;
            } else {
                // 普通对象 - 显示为属性列表
                let html = '<dl class="row">';
                for (const [key, value] of Object.entries(processData)) {
                    html += '<dt class="col-sm-3">' + key + '</dt>';
                    html += '<dd class="col-sm-9">';
                    
                    if (typeof value === 'object' && value !== null) {
                        // 嵌套对象，显示为格式化的JSON
                        html += '<pre class="mb-0"><code>' + JSON.stringify(value, null, 2) + '</code></pre>';
                    } else {
                        // 简单值，直接显示
                        html += value;
                    }
                    
                    html += '</dd>';
                }
                html += '</dl>';
                analysisProcessContent.innerHTML = html;
            }
        } else {
            // 字符串类型数据 - 可能是文本或原始日志
            // 检测是否包含换行符
            if (typeof processData === 'string' && processData.includes('\n')) {
                // 多行文本，按行分割显示
                const lines = processData.split('\n');
                let html = '<div class="process-log">';
                lines.forEach(line => {
                    if (line.trim()) {
                        html += '<div class="log-entry">' + line + '</div>';
                    }
                });
                html += '</div>';
                analysisProcessContent.innerHTML = html;
            } else {
                // 简单文本
                analysisProcessContent.innerHTML = '<p>' + processData + '</p>';
            }
        }
    }
</script>
{% endblock %}
