<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}九猫小说分析系统{% endblock %}</title>

    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js" as="script">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" as="script">

    <!-- 样式表 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 内联错误处理 -->
    <script>
        // 全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            console.log('捕获到错误: ' + message);
            return true; // 阻止错误冒泡
        };
        
        // 资源加载错误处理
        window.addEventListener('error', function(event) {
            if (event.target.tagName === 'LINK' || event.target.tagName === 'SCRIPT') {
                console.log('资源加载失败: ' + (event.target.src || event.target.href));
                
                // 尝试从CDN加载关键资源
                if ((event.target.src && event.target.src.includes('jquery')) || 
                    (event.target.href && event.target.href.includes('jquery'))) {
                    loadScript('https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js');
                }
                
                if ((event.target.src && event.target.src.includes('bootstrap')) || 
                    (event.target.href && event.target.href.includes('bootstrap'))) {
                    if (event.target.tagName === 'LINK') {
                        loadCSS('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
                    } else {
                        loadScript('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js');
                    }
                }
            }
        }, true);
        
        // 加载脚本
        function loadScript(src) {
            var script = document.createElement('script');
            script.src = src;
            document.head.appendChild(script);
        }
        
        // 加载CSS
        function loadCSS(href) {
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            document.head.appendChild(link);
        }
    </script>

    <!-- 内联备用样式 -->
    <style>
        body { 
            font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; 
            line-height: 1.5; 
            margin: 0; 
            padding: 0; 
            color: #212529; 
            background-color: #f8f9fa;
        }
        .container { 
            width: 100%; 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 0 15px; 
        }
        .navbar { 
            display: flex; 
            align-items: center; 
            padding: 8px 16px; 
            background-color: #343a40; 
            color: white; 
        }
        .navbar-brand { 
            font-size: 1.25rem; 
            color: white; 
            text-decoration: none; 
            font-weight: bold;
        }
        .card { 
            border: 1px solid #dee2e6; 
            border-radius: 4px; 
            margin-bottom: 1rem;
            background-color: #fff;
        }
        .card-header {
            padding: 0.75rem 1.25rem;
            background-color: rgba(0,0,0,.03);
            border-bottom: 1px solid rgba(0,0,0,.125);
        }
        .card-body {
            padding: 1.25rem;
        }
        .btn { 
            display: inline-block; 
            font-weight: 400;
            text-align: center;
            vertical-align: middle;
            user-select: none;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
            text-decoration: none;
        }
        .btn-primary { 
            color: #fff; 
            background-color: #007bff; 
            border-color: #007bff; 
        }
        .btn-success { 
            color: #fff; 
            background-color: #28a745; 
            border-color: #28a745; 
        }
        .btn-info { 
            color: #fff; 
            background-color: #17a2b8; 
            border-color: #17a2b8; 
        }
        .btn-warning { 
            color: #212529; 
            background-color: #ffc107; 
            border-color: #ffc107; 
        }
        .btn-danger { 
            color: #fff; 
            background-color: #dc3545; 
            border-color: #dc3545; 
        }
        .alert {
            position: relative;
            padding: 0.75rem 1.25rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.25rem;
        }
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .list-group {
            display: flex;
            flex-direction: column;
            padding-left: 0;
            margin-bottom: 0;
            border-radius: 0.25rem;
        }
        .list-group-item {
            position: relative;
            display: block;
            padding: 0.75rem 1.25rem;
            background-color: #fff;
            border: 1px solid rgba(0,0,0,.125);
        }
        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        .bg-primary { background-color: #007bff !important; }
        .bg-success { background-color: #28a745 !important; }
        .bg-info { background-color: #17a2b8 !important; }
        .bg-warning { background-color: #ffc107 !important; }
        .bg-danger { background-color: #dc3545 !important; }
        .text-white { color: #fff !important; }
        .text-dark { color: #343a40 !important; }
        .text-muted { color: #6c757d !important; }
        .mt-4 { margin-top: 1.5rem !important; }
        .mb-3 { margin-bottom: 1rem !important; }
        .mb-4 { margin-bottom: 1.5rem !important; }
        .me-1 { margin-right: 0.25rem !important; }
        .me-2 { margin-right: 0.5rem !important; }
        .pb-2 { padding-bottom: 0.5rem !important; }
        .w-100 { width: 100% !important; }
        .d-block { display: block !important; }
        .d-flex { display: flex !important; }
        .justify-content-between { justify-content: space-between !important; }
        .align-items-center { align-items: center !important; }
        .border-bottom { border-bottom: 1px solid #dee2e6 !important; }
        .rounded-pill { border-radius: 50rem !important; }
        .small { font-size: 80%; font-weight: 400; }
        .progress {
            display: flex;
            height: 1rem;
            overflow: hidden;
            font-size: 0.75rem;
            background-color: #e9ecef;
            border-radius: 0.25rem;
        }
        .progress-bar {
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            background-color: #007bff;
            transition: width 0.6s ease;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -15px;
            margin-left: -15px;
        }
        .col-12 { flex: 0 0 100%; max-width: 100%; }
        .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
        .col-md-6 { flex: 0 0 50%; max-width: 50%; }
        .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
        
        @media (max-width: 767.98px) {
            .col-md-4, .col-md-6, .col-md-8 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">九猫小说分析系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="/"><i class="fas fa-home me-1"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/novels"><i class="fas fa-book me-1"></i> 小说列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/upload"><i class="fas fa-upload me-1"></i> 上传分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis"><i class="fas fa-chart-bar me-1"></i> 分析记录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard"><i class="fas fa-tachometer-alt me-1"></i> 数据中心</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/settings"><i class="fas fa-cog me-1"></i> 设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <footer class="bg-dark text-white mt-5 py-3">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>九猫小说分析系统</h5>
                    <p class="small">专为作家和编辑设计的文本分析工具</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="small">版本 1.0.0 | 使用 DeepSeek R1 API</p>
                    <p class="small">© 2025 九猫系统</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 基础JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 内联Chart.js替代 -->
    <script>
        // 创建一个空的Chart对象，防止报错
        window.Chart = window.Chart || function(ctx, config) {
            console.log('尝试创建图表，但图表功能已被禁用');
            this.ctx = ctx;
            this.config = config || {};

            // 实现必要的方法，防止调用时报错
            this.update = function() { console.log('Chart.update() 被调用，但图表已禁用'); };
            this.destroy = function() { console.log('Chart.destroy() 被调用，但图表已禁用'); };
            this.render = function() { console.log('Chart.render() 被调用，但图表已禁用'); };

            return this;
        };
    </script>

    <!-- 资源加载检查 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查jQuery是否加载
            if (typeof jQuery === 'undefined') {
                console.warn('jQuery未加载，尝试从CDN加载');
                loadScript('https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js');
            }
            
            // 检查Bootstrap是否加载
            if (typeof bootstrap === 'undefined') {
                console.warn('Bootstrap未加载，尝试从CDN加载');
                loadScript('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js');
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
