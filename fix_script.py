# 修复缩进问题的脚本
import os
file_path = 'src/web/routes/v3_routes.py'

print(f"尝试修复文件: {file_path}")
print(f"当前工作目录: {os.getcwd()}")

try:
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        exit(1)
        
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    print(f"成功读取文件，共 {len(lines)} 行")
    
    # 备份原文件
    backup_path = f"{file_path}.bak"
    with open(backup_path, 'w', encoding='utf-8') as backup:
        backup.writelines(lines)
    print(f"已创建备份文件: {backup_path}")
    
    # 查找并修复问题行
    fixed_lines = []
    changes_made = 0
    
    for i, line in enumerate(lines):
        line_num = i + 1
        original_line = line
        
        if "# 如果仍未找到ID" in line:
            # 修正缩进
            new_line = line.replace("                        # 如果仍未找到ID", "                    # 如果仍未找到ID")
            if new_line != original_line:
                print(f"第 {line_num} 行: 修复了 '# 如果仍未找到ID' 的缩进")
                changes_made += 1
                fixed_lines.append(new_line)
            else:
                fixed_lines.append(original_line)
        elif "except Exception as e:" in line and i > 500 and i < 540:
            # 修正缩进
            new_line = line.replace("                    except Exception as e:", "                except Exception as e:")
            if new_line != original_line:
                print(f"第 {line_num} 行: 修复了 'except Exception as e:' 的缩进")
                changes_made += 1
                fixed_lines.append(new_line)
            else:
                fixed_lines.append(original_line)
        else:
            fixed_lines.append(original_line)
    
    if changes_made == 0:
        print("未发现需要修复的问题")
        exit(0)
        
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as file:
        file.writelines(fixed_lines)
    
    print(f"成功修复了 {file_path} 的缩进问题，共修改了 {changes_made} 处")
except Exception as e:
    print(f"修复文件时出错: {str(e)}") 