/**
 * 九猫3.0系统 - 批量操作功能
 * 支持小说列表和生成仓库的批量删除
 */

class BatchOperations {
    constructor() {
        this.selectedItems = new Set();
        this.currentMode = 'novels'; // 'novels' 或 'content'
        this.init();
    }

    init() {
        this.bindEvents();
        this.createBatchToolbar();
    }

    /**
     * 创建批量操作工具栏
     */
    createBatchToolbar() {
        const toolbar = `
            <div id="batchToolbar" class="batch-toolbar" style="display: none;">
                <div class="d-flex justify-content-between align-items-center p-3 bg-light border rounded mb-3">
                    <div class="batch-info">
                        <span class="badge bg-primary me-2" id="selectedCount">0</span>
                        <span>已选择项目</span>
                    </div>
                    <div class="batch-actions">
                        <button class="btn btn-outline-primary btn-sm me-2" id="selectAllBtn">
                            <i class="fas fa-check-square me-1"></i>全选
                        </button>
                        <button class="btn btn-outline-secondary btn-sm me-2" id="unselectAllBtn">
                            <i class="fas fa-square me-1"></i>取消全选
                        </button>
                        <button class="btn btn-danger btn-sm me-2" id="batchDeleteBtn">
                            <i class="fas fa-trash me-1"></i>批量删除
                        </button>
                        <button class="btn btn-secondary btn-sm" id="cancelSelectionBtn">
                            <i class="fas fa-times me-1"></i>取消选择
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 在页面主要内容区域前插入工具栏
        const mainContent = document.querySelector('.card-body') || document.querySelector('.container-fluid');
        if (mainContent) {
            mainContent.insertAdjacentHTML('afterbegin', toolbar);
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 全选/取消全选
        document.addEventListener('change', (e) => {
            if (e.target.id === 'selectAll') {
                this.toggleSelectAll(e.target.checked);
            } else if (e.target.classList.contains('item-checkbox')) {
                this.toggleItem(e.target.value, e.target.checked);
            }
        });

        // 批量操作按钮
        document.addEventListener('click', (e) => {
            if (e.target.id === 'batchDeleteBtn' || e.target.closest('#batchDeleteBtn')) {
                this.confirmBatchDelete();
            } else if (e.target.id === 'cancelSelectionBtn' || e.target.closest('#cancelSelectionBtn')) {
                this.cancelSelection();
            } else if (e.target.id === 'selectAllBtn' || e.target.closest('#selectAllBtn')) {
                this.selectAllItems();
            } else if (e.target.id === 'unselectAllBtn' || e.target.closest('#unselectAllBtn')) {
                this.unselectAllItems();
            }
        });
    }

    /**
     * 添加选择框到现有的列表项
     */
    addCheckboxesToItems(mode = 'novels') {
        this.currentMode = mode;

        if (mode === 'novels') {
            this.addCheckboxesToNovels();
        } else if (mode === 'content') {
            this.addCheckboxesToContent();
        }
    }

    /**
     * 为小说列表添加选择框
     */
    addCheckboxesToNovels() {
        // 检查是否是卡片布局还是表格布局
        const novelCards = document.querySelectorAll('.novel-card');
        const tableRows = document.querySelectorAll('table tbody tr');

        if (novelCards.length > 0) {
            // 卡片布局 - 添加全选框到页面顶部
            this.addSelectAllToCardLayout();

            // 为每个卡片添加选择框（如果还没有的话）
            novelCards.forEach((card) => {
                if (!card.querySelector('.item-checkbox')) {
                    const novelId = this.extractNovelIdFromCard(card);
                    if (novelId) {
                        // 选择框已经在模板中通过JavaScript添加了
                        // 这里只需要确保事件绑定正确
                    }
                }
            });
        } else if (tableRows.length > 0) {
            // 表格布局 - 添加全选框到表头
            const tableHeader = document.querySelector('table thead tr');
            if (tableHeader && !tableHeader.querySelector('.select-column')) {
                const selectHeader = document.createElement('th');
                selectHeader.className = 'select-column';
                selectHeader.innerHTML = `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                        <label class="form-check-label" for="selectAll">全选</label>
                    </div>
                `;
                tableHeader.insertBefore(selectHeader, tableHeader.firstChild);
            }

            // 为每行添加选择框
            tableRows.forEach((row, index) => {
                if (!row.querySelector('.item-checkbox')) {
                    const novelId = this.extractNovelId(row);
                    if (novelId) {
                        const selectCell = document.createElement('td');
                        selectCell.innerHTML = `
                            <div class="form-check">
                                <input class="form-check-input item-checkbox" type="checkbox" value="${novelId}" id="select_${novelId}">
                            </div>
                        `;
                        row.insertBefore(selectCell, row.firstChild);
                    }
                }
            });
        }
    }

    /**
     * 为卡片布局添加全选框
     */
    addSelectAllToCardLayout() {
        const toolbar = document.getElementById('batchToolbar');
        if (toolbar && !toolbar.querySelector('#selectAll')) {
            const batchInfo = toolbar.querySelector('.batch-info');
            if (batchInfo) {
                batchInfo.innerHTML = `
                    <div class="form-check d-inline-block me-3">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                        <label class="form-check-label" for="selectAll">全选</label>
                    </div>
                    <span class="badge bg-primary me-2" id="selectedCount">0</span>
                    <span>已选择项目</span>
                `;
            }
        }
    }

    /**
     * 全选/取消全选所有项目
     */
    selectAllItems() {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            this.toggleItem(checkbox.value, true);
        });
    }

    /**
     * 取消全选所有项目
     */
    unselectAllItems() {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            this.toggleItem(checkbox.value, false);
        });
    }

    /**
     * 为生成内容添加选择框
     */
    addCheckboxesToContent() {
        // 类似的逻辑，但针对生成内容列表
        const contentItems = document.querySelectorAll('.content-item, .generated-content-card');
        contentItems.forEach((item, index) => {
            if (!item.querySelector('.item-checkbox')) {
                const contentId = this.extractContentId(item);
                if (contentId) {
                    const checkbox = document.createElement('div');
                    checkbox.className = 'position-absolute top-0 start-0 p-2';
                    checkbox.innerHTML = `
                        <div class="form-check">
                            <input class="form-check-input item-checkbox" type="checkbox" value="${contentId}" id="select_content_${contentId}">
                        </div>
                    `;
                    item.style.position = 'relative';
                    item.appendChild(checkbox);
                }
            }
        });
    }

    /**
     * 从表格行中提取小说ID
     */
    extractNovelId(row) {
        // 尝试从链接中提取ID
        const link = row.querySelector('a[href*="/novel/"]');
        if (link) {
            const match = link.href.match(/\/novel\/(\d+)/);
            return match ? match[1] : null;
        }

        // 尝试从data属性中提取
        return row.dataset.novelId || row.dataset.id;
    }

    /**
     * 从卡片中提取小说ID
     */
    extractNovelIdFromCard(card) {
        // 优先从data属性中提取
        if (card.dataset.novelId) {
            return card.dataset.novelId;
        }

        // 尝试从链接中提取ID
        const link = card.querySelector('a[href*="/novel/"]');
        if (link) {
            const match = link.href.match(/\/novel\/(\d+)/);
            return match ? match[1] : null;
        }

        // 尝试从按钮的data属性中提取
        const button = card.querySelector('[data-novel-id]');
        if (button) {
            return button.dataset.novelId;
        }

        return null;
    }

    /**
     * 从内容项中提取内容ID
     */
    extractContentId(item) {
        return item.dataset.contentId || item.dataset.id;
    }

    /**
     * 切换全选状态
     */
    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            this.toggleItem(checkbox.value, checked);
        });
    }

    /**
     * 切换单个项目选择状态
     */
    toggleItem(itemId, checked) {
        if (checked) {
            this.selectedItems.add(itemId);
        } else {
            this.selectedItems.delete(itemId);
        }

        this.updateUI();
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        const toolbar = document.getElementById('batchToolbar');
        const selectedCount = document.getElementById('selectedCount');
        const selectAllCheckbox = document.getElementById('selectAll');

        if (this.selectedItems.size > 0) {
            toolbar.style.display = 'block';
            selectedCount.textContent = this.selectedItems.size;
        } else {
            toolbar.style.display = 'none';
        }

        // 更新全选框状态
        const allCheckboxes = document.querySelectorAll('.item-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.item-checkbox:checked');

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = allCheckboxes.length > 0 && checkedCheckboxes.length === allCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
        }
    }

    /**
     * 确认批量删除
     */
    confirmBatchDelete() {
        if (this.selectedItems.size === 0) {
            alert('请先选择要删除的项目');
            return;
        }

        const itemType = this.currentMode === 'novels' ? '小说' : '生成内容';
        const message = `确定要删除选中的 ${this.selectedItems.size} 个${itemType}吗？\n\n此操作不可撤销！`;

        if (confirm(message)) {
            this.executeBatchDelete();
        }
    }

    /**
     * 执行批量删除
     */
    async executeBatchDelete() {
        const itemIds = Array.from(this.selectedItems);
        const apiUrl = this.currentMode === 'novels'
            ? '/v3/api/novels/batch-delete'
            : '/v3/api/content-repository/batch-delete';

        const requestData = this.currentMode === 'novels'
            ? { novel_ids: itemIds }
            : { content_ids: itemIds };

        try {
            // 显示加载状态
            this.showLoadingState(true);

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.success) {
                alert(result.message);

                // 刷新页面或移除已删除的项目
                this.removeDeletedItems(itemIds);
                this.cancelSelection();
            } else {
                alert(`删除失败: ${result.error}`);
            }

        } catch (error) {
            console.error('批量删除出错:', error);
            alert(`删除失败: ${error.message}`);
        } finally {
            this.showLoadingState(false);
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoadingState(show) {
        const deleteBtn = document.getElementById('batchDeleteBtn');
        if (show) {
            deleteBtn.disabled = true;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
        } else {
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = '<i class="fas fa-trash me-1"></i>批量删除';
        }
    }

    /**
     * 移除已删除的项目
     */
    removeDeletedItems(deletedIds) {
        deletedIds.forEach(id => {
            // 尝试通过checkbox找到项目
            const checkbox = document.querySelector(`input[value="${id}"]`);
            if (checkbox) {
                const item = checkbox.closest('tr') ||
                           checkbox.closest('.content-item') ||
                           checkbox.closest('.generated-content-card') ||
                           checkbox.closest('.novel-card')?.parentElement; // 卡片的父容器（col-md-6等）
                if (item) {
                    item.remove();
                }
            } else {
                // 如果没有checkbox，尝试通过data属性找到项目
                const card = document.querySelector(`[data-novel-id="${id}"]`);
                if (card) {
                    const container = card.closest('.col-md-6, .col-lg-4, .col-sm-6') || card;
                    container.remove();
                }
            }
        });
    }

    /**
     * 取消选择
     */
    cancelSelection() {
        this.selectedItems.clear();

        // 取消所有选择框
        const checkboxes = document.querySelectorAll('.item-checkbox, #selectAll');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.indeterminate = false;
        });

        this.updateUI();
    }
}

// 全局实例
window.batchOperations = new BatchOperations();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 根据当前页面自动判断模式
    if (window.location.pathname.includes('content-repository')) {
        window.batchOperations.addCheckboxesToItems('content');
    } else if (window.location.pathname.includes('novels') || document.querySelector('table')) {
        window.batchOperations.addCheckboxesToItems('novels');
    }
});
