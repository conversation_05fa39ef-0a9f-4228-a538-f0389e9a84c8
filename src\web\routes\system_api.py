"""
系统 API 端点
提供系统维护和恢复功能的 API 端点
"""
import os
import logging
import psutil
import time
import threading
import shutil
from datetime import datetime
from flask import Blueprint, jsonify, request

from src.db.connection import Session, engine
from src.utils.cache_manager import clear_cache as clear_cache_util
from src.utils.memory_monitor import get_memory_usage

# 创建蓝图
system_api_bp = Blueprint('system_api', __name__)

# 配置日志
logger = logging.getLogger(__name__)

@system_api_bp.route('/api/system/status', methods=['GET'])
def system_status():
    """获取系统状态"""
    try:
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # 获取系统启动时间
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        days, seconds = uptime.days, uptime.seconds
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        uptime_str = f"{days}天 {hours}小时 {minutes}分钟"

        # 获取进程信息
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'status']):
            try:
                pinfo = proc.info
                # 只包含 CPU 或内存使用率较高的进程
                if pinfo['cpu_percent'] > 1.0 or pinfo['memory_percent'] > 1.0:
                    processes.append({
                        'pid': pinfo['pid'],
                        'name': pinfo['name'],
                        'cpu_percent': round(pinfo['cpu_percent'], 2),
                        'memory_percent': round(pinfo['memory_percent'], 2),
                        'status': pinfo['status']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        # 按 CPU 使用率排序
        processes.sort(key=lambda x: x['cpu_percent'], reverse=True)

        # 只返回前 10 个进程
        processes = processes[:10]

        return jsonify({
            'success': True,
            'system': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used': round(memory.used / (1024 * 1024), 2),  # MB
                'memory_total': round(memory.total / (1024 * 1024), 2),  # MB
                'disk_percent': disk.percent,
                'disk_used': round(disk.used / (1024 * 1024 * 1024), 2),  # GB
                'disk_total': round(disk.total / (1024 * 1024 * 1024), 2),  # GB
                'boot_time': boot_time.strftime('%Y-%m-%d %H:%M:%S'),
                'uptime': uptime_str
            },
            'processes': processes,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取系统状态时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_api_bp.route('/api/system/reset-db-connection', methods=['POST'])
def reset_db_connection():
    """重置数据库连接池"""
    try:
        logger.info("正在重置数据库连接池...")

        # 关闭所有现有会话
        Session.remove()

        # 尝试关闭并重新创建连接池
        if hasattr(engine, 'dispose'):
            engine.dispose()
            logger.info("已关闭并重置数据库连接池")

        # 创建测试连接确认连接池正常工作
        from sqlalchemy import text
        session = Session()
        session.execute(text("SELECT 1"))
        session.close()

        logger.info("数据库连接池已成功重置")

        return jsonify({
            'success': True,
            'message': '数据库连接池已重置',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"重置数据库连接池时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_api_bp.route('/api/system/clear-cache', methods=['POST'])
def clear_cache():
    """清除系统缓存"""
    try:
        logger.info("正在清除系统缓存...")

        # 调用缓存管理器的清除缓存函数
        cache_cleared = clear_cache_util()

        # 清除临时文件
        temp_dir = os.path.join(os.getcwd(), 'temp')
        if os.path.exists(temp_dir):
            try:
                # 使用线程异步清理，避免阻塞请求
                def clean_temp_dir():
                    try:
                        for filename in os.listdir(temp_dir):
                            file_path = os.path.join(temp_dir, filename)
                            try:
                                if os.path.isfile(file_path):
                                    os.unlink(file_path)
                                elif os.path.isdir(file_path):
                                    shutil.rmtree(file_path)
                            except Exception as e:
                                logger.error(f"清除临时文件时出错: {str(e)}")
                        logger.info("临时文件清除完成")
                    except Exception as e:
                        logger.error(f"清除临时目录时出错: {str(e)}")

                # 启动清理线程
                threading.Thread(target=clean_temp_dir).start()
            except Exception as e:
                logger.error(f"启动清理临时目录线程时出错: {str(e)}")

        logger.info("系统缓存已清除")

        return jsonify({
            'success': True,
            'message': '系统缓存已清除',
            'cache_cleared': cache_cleared,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"清除系统缓存时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_api_bp.route('/api/system/diagnostics', methods=['POST'])
def run_diagnostics():
    """运行系统诊断"""
    try:
        logger.info("正在运行系统诊断...")

        # 检查系统资源
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # 检查数据库连接
        db_status = "正常"
        db_error = None
        try:
            from sqlalchemy import text
            session = Session()
            session.execute(text("SELECT 1"))
            session.close()
        except Exception as e:
            db_status = "异常"
            db_error = str(e)
            logger.error(f"数据库连接测试失败: {str(e)}")

        # 检查内存使用情况
        memory_usage = get_memory_usage()

        # 确定系统状态
        system_status = "正常"
        if cpu_percent > 90 or memory.percent > 90 or disk.percent > 90:
            system_status = "资源紧张"

        # 生成建议
        recommendations = []
        if cpu_percent > 90:
            recommendations.append("CPU使用率过高，建议关闭不必要的进程")
        if memory.percent > 90:
            recommendations.append("内存使用率过高，建议清理内存或增加内存")
        if disk.percent > 90:
            recommendations.append("磁盘使用率过高，建议清理磁盘空间")
        if db_status == "异常":
            recommendations.append("数据库连接异常，建议重置数据库连接池")

        if not recommendations:
            recommendations.append("系统运行正常，无需采取措施")

        # 记录诊断结果
        logger.info(f"系统诊断完成: 系统状态={system_status}, 数据库状态={db_status}")

        return jsonify({
            'success': True,
            'system_status': system_status,
            'database_status': db_status,
            'memory_usage': memory_usage,
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'disk_percent': disk.percent,
            'recommendation': "; ".join(recommendations),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"运行系统诊断时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_api_bp.route('/api/system/memory', methods=['GET'])
def memory_status():
    """获取内存使用状态"""
    try:
        # 获取内存使用情况
        memory_usage = get_memory_usage()

        # 获取系统内存信息
        memory = psutil.virtual_memory()

        return jsonify({
            'success': True,
            'memory_usage': memory_usage,
            'system_memory': {
                'total': round(memory.total / (1024 * 1024), 2),  # MB
                'available': round(memory.available / (1024 * 1024), 2),  # MB
                'used': round(memory.used / (1024 * 1024), 2),  # MB
                'percent': memory.percent
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取内存状态时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_api_bp.route('/api/system/version', methods=['GET'])
def api_version():
    """获取API版本信息"""
    try:
        # 获取系统版本
        version = os.environ.get('VERSION', '3.0')  # 默认使用3.0版本

        # 检查是否存在v3_api_bp
        v3_api_exists = False
        try:
            from src.web.routes.v3_api import v3_api_bp
            v3_api_exists = True
        except ImportError:
            pass

        # 检查是否存在v2_api_bp
        v2_api_exists = False
        try:
            from src.web.routes.v2_api import v2_api_bp
            v2_api_exists = True
        except ImportError:
            pass

        # 确定实际使用的API版本
        actual_api_version = '3.0'
        if not v3_api_exists and v2_api_exists:
            actual_api_version = '2.0'

        # 确定API路径前缀
        api_prefix = '/api/novel/'
        if actual_api_version == '2.0':
            api_prefix = '/api/novels/'

        return jsonify({
            'success': True,
            'version': version,
            'api_version': actual_api_version,
            'api_prefix': api_prefix,
            'v3_api_available': v3_api_exists,
            'v2_api_available': v2_api_exists,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取API版本信息时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
