"""
测试推理过程内容的脚本
"""
import os
import sys
import logging
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入配置
import config

# 导入数据库模型
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.db.connection import Session, engine
from src.models.base import Base

# 导入API客户端
from src.api.deepseek_client import DeepSeekClient
from src.api.analysis import NovelAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_reasoning_content.log')
    ]
)
logger = logging.getLogger(__name__)

def test_analyze_dimension():
    """测试分析维度功能，确保推理过程内容被正确保存"""
    logger.info("开始测试分析维度功能")
    
    # 创建API客户端
    client = DeepSeekClient()
    analyzer = NovelAnalyzer(api_client=client)
    
    # 测试文本
    test_text = """
    这是一段测试文本，用于测试分析维度功能。
    这是第二行文本。
    这是第三行文本。
    """
    
    # 分析维度
    dimension = "language_style"
    
    # 调用分析维度方法
    result = analyzer.analyze_dimension(test_text, dimension, "测试标题")
    
    # 检查结果
    logger.info(f"分析结果: {result}")
    
    # 检查推理过程内容
    if "reasoning_content" in result:
        logger.info(f"推理过程内容长度: {len(result['reasoning_content'])}")
        logger.info(f"推理过程内容前100个字符: {result['reasoning_content'][:100]}")
    else:
        logger.error("结果中没有推理过程内容")
    
    return result

def test_chapter_analysis():
    """测试章节分析功能，确保推理过程内容被正确保存到数据库"""
    logger.info("开始测试章节分析功能")
    
    session = Session()
    try:
        # 查找一个测试章节
        chapter = session.query(Chapter).first()
        if not chapter:
            logger.error("没有找到测试章节")
            return False
        
        logger.info(f"找到测试章节: id={chapter.id}, title={chapter.title}, novel_id={chapter.novel_id}")
        
        # 分析维度
        dimension = "language_style"
        
        # 调用章节分析服务
        from src.services.chapter_analysis_service import ChapterAnalysisService
        result = ChapterAnalysisService.analyze_chapter(
            chapter_id=chapter.id,
            dimension=dimension,
            use_cache=False  # 强制重新分析
        )
        
        logger.info(f"章节分析结果: {result}")
        
        # 检查数据库中的结果
        result_id = result.get("result", {}).get("id")
        if not result_id:
            logger.error("分析结果中没有ID")
            return False
        
        # 查询数据库中的结果
        result = session.query(ChapterAnalysisResult).get(result_id)
        
        if result:
            logger.info(f"找到现有分析结果: id={result.id}, dimension={result.dimension}")
            logger.info(f"分析结果内容长度: {len(result.content) if result.content else 0}")
            logger.info(f"推理过程内容长度: {len(result.reasoning_content) if result.reasoning_content else 0}")
            
            # 检查推理过程内容
            if result.reasoning_content:
                logger.info(f"推理过程内容前100个字符: {result.reasoning_content[:100]}")
                return True
            else:
                logger.error("推理过程内容为空")
                return False
        else:
            logger.info(f"未找到章节 {chapter.id} 的 {dimension} 维度分析结果，将创建新的分析结果")
            
            # 创建一个测试分析结果
            from src.models.novel import Novel
            
            # 获取小说
            novel = session.query(Novel).get(chapter.novel_id)
            if not novel:
                logger.error(f"小说不存在: {chapter.novel_id}")
                return False
            
            # 创建测试分析结果
            test_result = ChapterAnalysisResult(
                chapter_id=chapter.id,
                novel_id=chapter.novel_id,
                dimension=dimension,
                content="这是测试分析结果内容",
                reasoning_content="这是测试推理过程内容，用于验证数据库字段是否正常工作。" * 10,
                analysis_metadata={"test": True},
                analysis_logs=[{"timestamp": "2023-01-01T00:00:00", "level": "info", "message": "测试日志"}]
            )
            
            # 保存到数据库
            session.add(test_result)
            session.commit()
            
            logger.info(f"成功创建测试分析结果: id={test_result.id}")
            return True
    except Exception as e:
        logger.error(f"测试章节分析API时出错: {str(e)}")
        session.rollback()
        return False
    finally:
        session.close()

def test_api_route():
    """测试API路由，确保推理过程内容可以通过API获取"""
    logger.info("开始测试API路由")
    
    session = Session()
    try:
        # 查找一个测试章节分析结果
        result = session.query(ChapterAnalysisResult).first()
        if not result:
            logger.error("没有找到测试章节分析结果")
            return False
        
        logger.info(f"找到测试章节分析结果: id={result.id}, dimension={result.dimension}, chapter_id={result.chapter_id}, novel_id={result.novel_id}")
        
        # 检查推理过程内容
        if result.reasoning_content:
            logger.info(f"推理过程内容长度: {len(result.reasoning_content)}")
            logger.info(f"推理过程内容前100个字符: {result.reasoning_content[:100]}")
        else:
            logger.error("推理过程内容为空")
            
        # 打印API路由URL
        api_url = f"/api/novel/{result.novel_id}/chapter/{result.chapter_id}/analysis/{result.dimension}/reasoning_content"
        logger.info(f"API路由URL: {api_url}")
        
        return True
    except Exception as e:
        logger.error(f"测试API路由时出错: {str(e)}")
        return False
    finally:
        session.close()

if __name__ == "__main__":
    logger.info("开始测试推理过程内容")
    
    # 测试分析维度功能
    # result = test_analyze_dimension()
    
    # 测试章节分析功能
    test_chapter_analysis()
    
    # 测试API路由
    test_api_route()
    
    logger.info("测试完成")
