"""
简单测试九猫系统的并行分析功能
"""
import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

import config
from src.models.novel import Novel
from src.api.analysis import NovelAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("simple_test")

# 创建测试小说内容
test_content = "这是一个测试小说，用于测试九猫系统的并行分析功能。\n" * 1000

# 创建测试小说对象
test_novel = Novel(
    title="测试小说",
    content=test_content,
    author="测试作者"
)
test_novel.id = 999  # 手动设置ID

# 创建分析器
analyzer = NovelAnalyzer()

# 测试维度
test_dimensions = ["language_style", "structure"]

# 启用调试模式，避免实际调用API
config.DEBUG = True

# 测试并行分析
print("开始并行分析测试...")
config.PARALLEL_ANALYSIS_ENABLED = True
start_time = time.time()
parallel_results = analyzer.analyze_novel_parallel(test_novel, test_dimensions)
parallel_time = time.time() - start_time
print(f"并行分析完成，耗时: {parallel_time:.2f}秒")

# 输出结果
for dimension in test_dimensions:
    if dimension in parallel_results:
        result_len = len(parallel_results[dimension].content)
        print(f"维度 {dimension}: 结果长度={result_len}")

print("测试完成")
