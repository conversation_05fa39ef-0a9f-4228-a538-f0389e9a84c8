/**
 * 九猫小说分析写作系统 - 分析结果内容修复脚本
 * 
 * 此脚本用于修复分析结果内容为空但推理过程有内容的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[分析结果内容修复] 初始化...');
    
    // 配置
    const CONFIG = {
        debug: true,
        selectors: {
            analysisContent: '.analysis-content',
            reasoningContent: '.reasoning-content',
            dimensionItem: '[data-dimension]',
            dimensionAccordion: '.accordion-item',
            tabContent: '.tab-content',
            tabPane: '.tab-pane'
        }
    };
    
    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[分析结果内容修复] ${message}`);
        }
    }
    
    // 初始化
    function init() {
        debugLog('开始初始化...');
        
        // 确保jQuery已加载
        if (typeof window.ensureJQuery !== 'function') {
            debugLog('ensureJQuery函数不存在，等待页面加载完成后再试', 'warn');
            
            // 等待页面加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', init);
                return;
            }
            
            // 尝试直接使用jQuery
            if (typeof jQuery !== 'undefined') {
                debugLog('jQuery已加载，继续初始化');
                initWithJQuery(jQuery);
            } else {
                debugLog('jQuery未加载，无法初始化', 'error');
                return;
            }
        } else {
            window.ensureJQuery(function($) {
                debugLog('jQuery已加载，继续初始化');
                initWithJQuery($);
            });
        }
    }
    
    // 使用jQuery初始化
    function initWithJQuery($) {
        debugLog('使用jQuery初始化');
        
        // 添加事件监听器
        addEventListeners($);
        
        // 修复分析结果内容
        fixAnalysisContent($);
    }
    
    // 添加事件监听器
    function addEventListeners($) {
        debugLog('添加事件监听器');
        
        // 监听DOM变化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 检查是否添加了分析内容元素
                    const $addedNodes = $(mutation.addedNodes);
                    const hasAnalysisContent = $addedNodes.find(CONFIG.selectors.analysisContent).length > 0 ||
                                              $addedNodes.filter(CONFIG.selectors.analysisContent).length > 0;
                    
                    if (hasAnalysisContent) {
                        debugLog('检测到分析内容元素添加，尝试修复');
                        fixAnalysisContent($);
                    }
                }
            });
        });
        
        // 观察整个文档
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // 监听标签页切换事件
        $(document).on('shown.bs.tab', 'button[data-bs-toggle="tab"]', function(e) {
            debugLog('检测到标签页切换事件');
            
            // 延迟执行，确保内容已加载
            setTimeout(function() {
                fixAnalysisContent($);
            }, 500);
        });
        
        // 监听折叠区域展开事件
        $(document).on('shown.bs.collapse', '.accordion-collapse', function() {
            debugLog('检测到折叠区域展开事件');
            
            // 延迟执行，确保内容已加载
            setTimeout(function() {
                fixAnalysisContent($);
            }, 500);
        });
    }
    
    // 修复分析结果内容
    function fixAnalysisContent($) {
        debugLog('修复分析结果内容');
        
        // 查找所有维度项
        const $dimensionItems = $(CONFIG.selectors.dimensionItem);
        
        if ($dimensionItems.length === 0) {
            debugLog('未找到维度项，可能尚未加载分析结果', 'warn');
            return;
        }
        
        debugLog(`找到 ${$dimensionItems.length} 个维度项`);
        
        // 遍历所有维度项
        $dimensionItems.each(function() {
            const $dimensionItem = $(this);
            const dimension = $dimensionItem.data('dimension');
            
            if (dimension) {
                // 检查分析结果内容
                const $analysisContent = $dimensionItem.find(CONFIG.selectors.analysisContent);
                
                if ($analysisContent.length > 0) {
                    const content = $analysisContent.html().trim();
                    
                    // 如果分析结果为空或显示"暂无分析结果"
                    if (!content || content === '暂无分析结果') {
                        debugLog(`维度 ${dimension} 的分析结果为空或显示"暂无分析结果"，尝试修复`);
                        
                        // 检查推理过程内容
                        const $reasoningContent = $dimensionItem.find(CONFIG.selectors.reasoningContent);
                        
                        if ($reasoningContent.length > 0) {
                            const reasoningContent = $reasoningContent.html().trim();
                            
                            // 如果推理过程有内容
                            if (reasoningContent && reasoningContent !== '暂无推理过程') {
                                debugLog(`维度 ${dimension} 的推理过程有内容，尝试从推理过程中提取分析结果`);
                                
                                // 尝试从推理过程中提取分析结果
                                const analysisResult = extractAnalysisResultFromReasoning(reasoningContent);
                                
                                if (analysisResult) {
                                    debugLog(`成功从推理过程中提取维度 ${dimension} 的分析结果`);
                                    $analysisContent.html(analysisResult);
                                }
                            }
                        }
                    }
                }
            }
        });
    }
    
    // 从推理过程中提取分析结果
    function extractAnalysisResultFromReasoning(reasoningContent) {
        debugLog('尝试从推理过程中提取分析结果');
        
        // 尝试查找"分析结果"部分
        const resultMatch = reasoningContent.match(/<h2>分析结果<\/h2>([\s\S]*?)(<h2>|$)/i);
        
        if (resultMatch && resultMatch[1]) {
            debugLog('找到分析结果部分');
            return resultMatch[1].trim();
        }
        
        // 尝试查找"最终分析"部分
        const finalMatch = reasoningContent.match(/<h2>最终分析<\/h2>([\s\S]*?)(<h2>|$)/i);
        
        if (finalMatch && finalMatch[1]) {
            debugLog('找到最终分析部分');
            return finalMatch[1].trim();
        }
        
        // 尝试查找"总结"部分
        const summaryMatch = reasoningContent.match(/<h2>总结<\/h2>([\s\S]*?)(<h2>|$)/i);
        
        if (summaryMatch && summaryMatch[1]) {
            debugLog('找到总结部分');
            return summaryMatch[1].trim();
        }
        
        // 尝试查找最后一个h2标签后的内容
        const lastH2Match = reasoningContent.match(/<h2>[^<]+<\/h2>([\s\S]*)$/i);
        
        if (lastH2Match && lastH2Match[1]) {
            debugLog('找到最后一个h2标签后的内容');
            return lastH2Match[1].trim();
        }
        
        // 尝试查找markdown格式的标题
        const markdownResultMatch = reasoningContent.match(/##\s*分析结果\s*\n([\s\S]*?)(\n##|$)/i);
        
        if (markdownResultMatch && markdownResultMatch[1]) {
            debugLog('找到markdown格式的分析结果部分');
            return markdownResultMatch[1].trim();
        }
        
        // 尝试查找markdown格式的最终分析
        const markdownFinalMatch = reasoningContent.match(/##\s*最终分析\s*\n([\s\S]*?)(\n##|$)/i);
        
        if (markdownFinalMatch && markdownFinalMatch[1]) {
            debugLog('找到markdown格式的最终分析部分');
            return markdownFinalMatch[1].trim();
        }
        
        // 尝试查找markdown格式的总结
        const markdownSummaryMatch = reasoningContent.match(/##\s*总结\s*\n([\s\S]*?)(\n##|$)/i);
        
        if (markdownSummaryMatch && markdownSummaryMatch[1]) {
            debugLog('找到markdown格式的总结部分');
            return markdownSummaryMatch[1].trim();
        }
        
        debugLog('未能从推理过程中提取分析结果', 'warn');
        return null;
    }
    
    // 初始化
    init();
    
    console.log('[分析结果内容修复] 初始化完成');
})();
