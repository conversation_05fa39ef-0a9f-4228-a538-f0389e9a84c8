/* 九猫小说分析写作系统v3.0 样式表 */

/* 全局样式 - 浅米黄色和淡白色主题 */
:root {
    --primary-color: #e6b422; /* 金黄色主色调 */
    --primary-dark: #c99a17; /* 深金黄色 */
    --primary-light: #f7d26e; /* 浅金黄色 */
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #fffdf5; /* 更淡的米黄色背景 */
    --card-bg: #ffffff; /* 纯白色卡片 */
    --text-color: #4a4a4a;
    --border-color: #f7f0d7; /* 更淡的边框颜色 */
    --shadow-color: rgba(230, 180, 34, 0.05);
    --footer-bg: #fffdf5;
    --transition-speed: 0.3s;
    --primary-gradient: linear-gradient(to right, var(--primary-color), var(--primary-light));
    --box-shadow: 0 2px 8px var(--shadow-color);
    --transition: all 0.3s ease;
}

/* 深色主题变量 */
body[data-theme="dark"] {
    --primary-color: #e6b422; /* 保持金黄色 */
    --primary-dark: #c99a17;
    --primary-light: #f7d26e;
    --secondary-color: #495057;
    --body-bg: #2d2a20; /* 深棕色背景 */
    --card-bg: #3a3629; /* 深棕黄色卡片 */
    --text-color: #f0e0b2; /* 浅金色文字 */
    --border-color: #6b5c29; /* 深金色边框 */
    --shadow-color: rgba(230, 180, 34, 0.2);
    --footer-bg: #2a2620;
}

body {
    background-color: var(--body-bg);
    color: var(--text-color);
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    transition: background-color var(--transition-speed), color var(--transition-speed);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    line-height: 1.6;
}

/* 卡片样式 */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.card-header {
    background: var(--primary-gradient);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: #fff;
    padding: 1rem 1.25rem;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
}

.card-body {
    padding: 1.25rem;
    background-color: var(--card-bg);
}

.card-footer {
    border-top: 1px solid var(--border-color);
    background-color: var(--card-bg);
    padding: 1rem 1.25rem;
}

/* 按钮样式 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1.25rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px var(--shadow-color);
}

.btn-light {
    background-color: var(--light-color);
    border-color: var(--secondary-color);
    color: var(--dark-color);
}

.btn-light:hover {
    background-color: #e2e6ea;
    border-color: var(--secondary-color);
    color: var(--dark-color);
}

.btn-primary {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
    border-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-dark);
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 1px 6px var(--shadow-color);
    transition: background-color var(--transition-speed);
    background: var(--primary-gradient) !important;
    padding: 0.75rem 1rem;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
    color: #fff !important;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
}

.nav-link {
    font-weight: 500;
    transition: all 0.2s ease;
    color: rgba(255,255,255,0.9) !important;
    padding: 0.5rem 1rem;
}

.nav-link:hover {
    transform: translateY(-2px);
    color: #fff !important;
}

/* 标签页导航样式 */
.nav-tabs {
    border-bottom: 1px solid var(--primary-color);
}

.nav-tabs .nav-link {
    color: var(--dark-color) !important;
    background-color: var(--light-color);
    border: 1px solid var(--primary-color);
    border-bottom: none;
    margin-right: 0.3rem;
    border-radius: 0.5rem 0.5rem 0 0;
    font-weight: 600;
}

.nav-tabs .nav-link:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.nav-tabs .nav-link.active {
    color: white !important;
    background: var(--primary-gradient);
    border-color: var(--primary-color);
}

/* 表单样式 */
.form-control {
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(230, 180, 34, 0.25);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.form-select {
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.5rem 2.25rem 0.5rem 0.75rem;
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(230, 180, 34, 0.25);
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* 表格样式 */
.table {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
    width: 100%;
    margin-bottom: 1rem;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid var(--border-color);
}

.table thead th {
    background-color: var(--primary-light);
    color: var(--dark-color);
    border-color: var(--border-color);
    vertical-align: bottom;
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
}

.table tbody + tbody {
    border-top: 2px solid var(--border-color);
}

.table-hover tbody tr:hover {
    background-color: var(--shadow-color);
}

/* 徽章样式 */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 600;
    border-radius: 0.5rem;
}

.badge-primary {
    background-color: var(--primary-color);
    color: white;
}

/* 维度徽章 */
.dimension-badge {
    display: inline-block;
    padding: 0.5em 0.85em;
    font-size: 0.8em;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* 进度条样式 */
.progress {
    height: 0.75rem;
    border-radius: 0.5rem;
    background-color: var(--border-color);
    margin-bottom: 1rem;
    overflow: hidden;
}

.progress-bar {
    background: var(--primary-gradient);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 5px 15px var(--shadow-color);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
    background: var(--primary-gradient);
    color: white;
}

.modal-body {
    padding: 1.5rem;
    background-color: var(--card-bg);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1rem;
    background-color: var(--card-bg);
}

/* 分页样式 */
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.5rem;
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: var(--primary-color);
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.page-link:hover {
    z-index: 2;
    color: var(--primary-dark);
    text-decoration: none;
    background-color: var(--border-color);
    border-color: var(--border-color);
    transform: translateY(-2px);
}

.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background: var(--primary-gradient);
    border-color: var(--primary-color);
}

.page-item:first-child .page-link {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}

.page-item:last-child .page-link {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

/* 自定义动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-title {
        font-size: 1.25rem;
    }

    .lead {
        font-size: 1rem;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* 控制台特定样式 */
.console-output {
    font-family: 'Courier New', Courier, monospace;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    white-space: pre-wrap;
    overflow-y: auto;
}

/* 页脚样式 */
.footer {
    background-color: var(--footer-bg) !important;
    color: var(--text-color);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 0;
    margin-top: 2rem;
}

/* 分析结果样式 */
.analysis-result {
    background-color: var(--card-bg);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
}

.analysis-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
    text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
}

.analysis-content {
    line-height: 1.8;
    color: var(--text-color);
}

/* 章节列表样式 */
.chapter-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.chapter-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    color: var(--text-color);
    cursor: pointer;
}

.chapter-item:hover {
    background-color: var(--shadow-color);
    transform: translateX(5px);
}

.chapter-item.active {
    background-color: var(--primary-light);
    color: var(--dark-color);
    font-weight: 600;
    border-left: 3px solid var(--primary-color);
}

/* 维度列表样式 */
.dimension-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dimension-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    color: var(--text-color);
    cursor: pointer;
}

.dimension-item:hover {
    background-color: var(--shadow-color);
    transform: translateX(5px);
}

.dimension-item.active {
    background-color: var(--primary-light);
    color: var(--dark-color);
    font-weight: 600;
    border-left: 3px solid var(--primary-color);
}

/* Markdown内容样式 */
.markdown-content {
    line-height: 1.7;
    font-size: 1.05rem;
    color: var(--text-color);
}
