/**
 * 九猫 - Sizzle修复脚本
 * 专门修复S.find.matchesSelector错误
 * 版本: 1.0.0
 */

(function() {
    console.log('[九猫Sizzle修复] 脚本已加载');
    
    // 立即修复S.find.matchesSelector问题
    function fixSizzleMatchesSelector() {
        // 检查S对象
        if (typeof window.S !== 'undefined' && window.S && window.S.find) {
            console.log('[九猫Sizzle修复] 检测到S对象，尝试修复matchesSelector');
            
            try {
                // 检查是否存在matchesSelector错误
                if (!window.S.find.matchesSelector) {
                    console.log('[九猫Sizzle修复] S.find缺少matchesSelector函数，尝试修复');
                    
                    // 使用matches作为matchesSelector的替代
                    if (window.S.find.matches) {
                        window.S.find.matchesSelector = window.S.find.matches;
                        console.log('[九猫Sizzle修复] 使用S.find.matches作为matchesSelector的替代');
                    } 
                    // 从jQuery获取
                    else if (typeof jQuery !== 'undefined' && jQuery.find && jQuery.find.matchesSelector) {
                        window.S.find.matchesSelector = jQuery.find.matchesSelector;
                        console.log('[九猫Sizzle修复] 使用jQuery.find.matchesSelector作为替代');
                    }
                    // 使用浏览器原生方法
                    else if (typeof Element.prototype.matches !== 'undefined') {
                        window.S.find.matchesSelector = function(elem, expr) {
                            return Element.prototype.matches.call(elem, expr);
                        };
                        console.log('[九猫Sizzle修复] 使用Element.prototype.matches创建matchesSelector');
                    }
                    // 兼容IE
                    else if (typeof Element.prototype.msMatchesSelector !== 'undefined') {
                        window.S.find.matchesSelector = function(elem, expr) {
                            return Element.prototype.msMatchesSelector.call(elem, expr);
                        };
                        console.log('[九猫Sizzle修复] 使用msMatchesSelector创建matchesSelector');
                    }
                    // 最后的备选方案
                    else {
                        window.S.find.matchesSelector = function() { return true; };
                        console.log('[九猫Sizzle修复] 无法找到合适的替代，使用默认实现');
                    }
                    
                    console.log('[九猫Sizzle修复] S.find.matchesSelector已修复');
                } else {
                    console.log('[九猫Sizzle修复] S.find.matchesSelector已存在，无需修复');
                }
            } catch (e) {
                console.error('[九猫Sizzle修复] 修复S.find.matchesSelector时出错:', e);
            }
        } else {
            console.log('[九猫Sizzle修复] S对象不存在或S.find不存在，稍后再试');
        }
    }
    
    // 监听S对象的创建
    function monitorSObject() {
        if (typeof window.S === 'undefined') {
            console.log('[九猫Sizzle修复] 设置S对象监听器');
            
            // 使用Object.defineProperty监听S对象的创建
            Object.defineProperty(window, 'S', {
                configurable: true,
                set: function(newValue) {
                    // 删除这个属性描述符
                    delete window.S;
                    // 设置实际值
                    window.S = newValue;
                    // 立即尝试修复
                    console.log('[九猫Sizzle修复] 检测到S对象被创建，尝试修复');
                    setTimeout(fixSizzleMatchesSelector, 0);
                },
                get: function() {
                    return undefined;
                }
            });
        } else {
            console.log('[九猫Sizzle修复] S对象已存在，尝试直接修复');
            fixSizzleMatchesSelector();
        }
    }
    
    // 初始化函数
    function initialize() {
        console.log('[九猫Sizzle修复] 初始化中...');
        
        // 尝试立即修复
        fixSizzleMatchesSelector();
        
        // 设置S对象监听
        monitorSObject();
        
        // 定期检查，在10秒内每500ms尝试修复一次
        var attempts = 0;
        var interval = setInterval(function() {
            fixSizzleMatchesSelector();
            attempts++;
            
            // 20次尝试后停止(10秒)
            if (attempts >= 20) {
                clearInterval(interval);
                console.log('[九猫Sizzle修复] 修复尝试完成');
            }
        }, 500);
        
        console.log('[九猫Sizzle修复] 初始化完成');
    }
    
    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // 导出修复函数供手动调用
    window.fixSizzleMatchesSelector = fixSizzleMatchesSelector;
})();
