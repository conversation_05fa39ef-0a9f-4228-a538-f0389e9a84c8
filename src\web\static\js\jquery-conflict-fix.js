/**
 * 九猫 - jQuery冲突修复脚本
 * 用于解决多个jQuery版本加载造成的S.find.matchesSelector错误
 * 版本: 1.0.0
 */

(function() {
    console.log('[九猫jQuery修复] 脚本已加载');

    // 立即修复matchesSelector问题
    function fixMatchesSelector() {
        // 确保jQuery已加载
        if (typeof jQuery === 'undefined' || !jQuery.find) {
            console.log('[九猫jQuery修复] jQuery尚未加载，稍后再试');
            setTimeout(fixMatchesSelector, 100);
            return;
        }

        try {
            // 检查是否存在matchesSelector错误
            if (jQuery.find && !jQuery.find.matchesSelector) {
                console.log('[九猫jQuery修复] 正在修复matchesSelector函数');

                // 使用matches作为matchesSelector的替代
                if (jQuery.find.matches) {
                    jQuery.find.matchesSelector = jQuery.find.matches;
                    console.log('[九猫jQuery修复] 使用matches作为matchesSelector的替代');
                } else if (typeof Element.prototype.matches !== 'undefined') {
                    // 如果jQuery.find.matches不存在，但浏览器支持Element.prototype.matches
                    jQuery.find.matchesSelector = function(elem, expr) {
                        return Element.prototype.matches.call(elem, expr);
                    };
                    console.log('[九猫jQuery修复] 使用Element.prototype.matches创建matchesSelector');
                } else if (typeof Element.prototype.msMatchesSelector !== 'undefined') {
                    // 兼容IE
                    jQuery.find.matchesSelector = function(elem, expr) {
                        return Element.prototype.msMatchesSelector.call(elem, expr);
                    };
                    console.log('[九猫jQuery修复] 使用msMatchesSelector创建matchesSelector');
                } else {
                    // 最后的备选方案
                    jQuery.find.matchesSelector = function() { return true; };
                    console.log('[九猫jQuery修复] 无法找到合适的替代，使用默认实现');
                }

                console.log('[九猫jQuery修复] matchesSelector已修复');
            }

            // 如果Sizzle存在并且有同样的问题
            if (window.Sizzle && !Sizzle.matchesSelector) {
                console.log('[九猫jQuery修复] 正在修复Sizzle.matchesSelector函数');

                if (Sizzle.matches) {
                    Sizzle.matchesSelector = Sizzle.matches;
                } else if (jQuery.find && jQuery.find.matchesSelector) {
                    Sizzle.matchesSelector = jQuery.find.matchesSelector;
                } else if (typeof Element.prototype.matches !== 'undefined') {
                    Sizzle.matchesSelector = function(elem, expr) {
                        return Element.prototype.matches.call(elem, expr);
                    };
                } else {
                    Sizzle.matchesSelector = function() { return true; };
                }

                console.log('[九猫jQuery修复] Sizzle.matchesSelector已修复');
            }

            // 修复S.find.matchesSelector (jQuery内部使用的Sizzle别名)
            if (typeof S !== 'undefined' && S && S.find && !S.find.matchesSelector) {
                console.log('[九猫jQuery修复] 正在修复S.find.matchesSelector函数');

                if (S.find.matches) {
                    S.find.matchesSelector = S.find.matches;
                } else if (jQuery.find && jQuery.find.matchesSelector) {
                    S.find.matchesSelector = jQuery.find.matchesSelector;
                } else {
                    S.find.matchesSelector = function() { return true; };
                }

                console.log('[九猫jQuery修复] S.find.matchesSelector已修复');
            }
        } catch (e) {
            console.error('[九猫jQuery修复] 修复过程中出错:', e);
        }
    }

    // 防止加载多个jQuery版本
    function preventMultipleJQuery() {
        // 标记jQuery已加载，防止重复加载
        if (typeof jQuery !== 'undefined') {
            window.__jQueryLoaded = true;
            console.log('[九猫jQuery修复] jQuery已加载，版本:', jQuery.fn.jquery);

            // 拦截后续jQuery加载
            var originalAppendChild = document.head.appendChild;
            document.head.appendChild = function(element) {
                // 检查是否是加载jQuery的脚本
                if (element.tagName === 'SCRIPT' &&
                    element.src &&
                    (element.src.indexOf('jquery') !== -1 || element.src.indexOf('jQuery') !== -1) &&
                    window.__jQueryLoaded) {
                    console.warn('[九猫jQuery修复] 阻止重复加载jQuery:', element.src);
                    return element; // 不实际添加元素
                }

                // 其他元素正常添加
                return originalAppendChild.call(this, element);
            };
        }
    }

    // 在页面加载时执行修复
    function initialize() {
        // 尝试立即修复
        fixMatchesSelector();

        // 设置防止多重加载
        preventMultipleJQuery();

        // 监听DOM变化，在10秒内每500ms尝试修复一次
        var attempts = 0;
        var interval = setInterval(function() {
            fixMatchesSelector();
            attempts++;

            // 20次尝试后停止(10秒)
            if (attempts >= 20) {
                clearInterval(interval);
                console.log('[九猫jQuery修复] 修复尝试完成');
            }
        }, 500);

        // 添加全局S对象检测和修复
        if (typeof window.S === 'undefined') {
            // 监听S对象的创建
            Object.defineProperty(window, 'S', {
                configurable: true,
                set: function(newValue) {
                    // 删除这个属性描述符
                    delete window.S;
                    // 设置实际值
                    window.S = newValue;
                    // 立即尝试修复
                    console.log('[九猫jQuery修复] 检测到S对象被创建，尝试修复');
                    setTimeout(fixMatchesSelector, 0);
                },
                get: function() {
                    return undefined;
                }
            });
        }
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出修复函数供手动调用
    window.fixJQueryMatchesSelector = fixMatchesSelector;
})();