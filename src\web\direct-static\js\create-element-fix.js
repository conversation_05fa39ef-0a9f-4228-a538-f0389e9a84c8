/**
 * 九猫 - createElement 修复脚本
 * 用于修复 document.createElement 方法，确保安全地创建脚本和样式元素
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('createElement 修复脚本已加载');
    
    // 保存原始 createElement 方法
    const originalCreateElement = document.createElement;
    
    // 安全获取对象的属性
    function safeGetProperty(obj, prop) {
        try {
            return obj[prop];
        } catch (e) {
            return undefined;
        }
    }
    
    // 安全设置属性
    function safeSetProperty(obj, prop, value) {
        try {
            obj[prop] = value;
            return true;
        } catch (e) {
            console.warn(`设置属性 ${prop} 失败:`, e);
            return false;
        }
    }

    // 重写 createElement 方法 - 使用更安全的方式
    document.createElement = function(tagName) {
        try {
            // 使用原始方法创建元素
            const element = originalCreateElement.call(document, tagName);
            
            // 标记元素已经被处理过，避免重复处理
            if (element._elementFixed) {
                return element;
            }
            
            // 标记元素已处理
            try {
                element._elementFixed = true;
            } catch (e) {
                // 忽略错误，继续处理
            }
            
            // 特别处理脚本元素
            if (typeof tagName === 'string' && tagName.toLowerCase() === 'script') {
                try {
                    // 为脚本元素添加安全的textContent设置方法
                    let textContent = '';
                    
                    // 检查textContent属性是否已经被定义
                    let textContentDescriptor = Object.getOwnPropertyDescriptor(element, 'textContent');
                    
                    // 只有在属性不存在或可配置的情况下才尝试重新定义
                    if (!textContentDescriptor || textContentDescriptor.configurable) {
                        try {
                            // 使用Object.defineProperty安全地定义textContent属性
                            Object.defineProperty(element, 'textContent', {
                                get: function() {
                                    return textContent;
                                },
                                set: function(value) {
                                    textContent = value;
                                    try {
                                        // 尝试使用innerHTML
                                        element.innerHTML = value;
                                    } catch (e) {
                                        console.warn('设置脚本textContent失败:', e);
                                        // 如果innerHTML失败，尝试使用text属性
                                        if ('text' in element) {
                                            element.text = value;
                                        }
                                    }
                                },
                                enumerable: true,
                                configurable: true
                            });
                        } catch (propError) {
                            console.warn('无法重定义textContent属性:', propError.message);
                            // 不抛出错误，继续使用其他方法
                        }
                    }
                    
                    // 添加安全的appendChild方法
                    const originalAppendChild = element.appendChild;
                    element.appendChild = function(child) {
                        try {
                            return originalAppendChild.call(this, child);
                        } catch (e) {
                            console.warn('脚本appendChild失败:', e);
                            return child;
                        }
                    };
                } catch (e) {
                    console.warn('修复脚本元素时出错:', e);
                }
            }
            
            // 特别处理样式元素
            if (typeof tagName === 'string' && tagName.toLowerCase() === 'style') {
                try {
                    // 为样式元素添加安全的操作方法
                    let styleContent = '';
                    
                    // 检查textContent属性是否已经被定义
                    let textContentDescriptor = Object.getOwnPropertyDescriptor(element, 'textContent');
                    
                    // 只有在属性不存在或可配置的情况下才尝试重新定义
                    if (!textContentDescriptor || textContentDescriptor.configurable) {
                        try {
                            // 安全地定义样式内容属性
                            Object.defineProperty(element, 'textContent', {
                                get: function() {
                                    return styleContent;
                                },
                                set: function(value) {
                                    styleContent = value;
                                    try {
                                        element.innerHTML = value;
                                    } catch (e) {
                                        console.warn('设置样式textContent失败:', e);
                                        // 如果innerHTML失败，尝试使用cssText属性
                                        if (element.styleSheet) {
                                            element.styleSheet.cssText = value;
                                        }
                                    }
                                },
                                enumerable: true,
                                configurable: true
                            });
                        } catch (propError) {
                            console.warn('无法重定义样式元素的textContent属性:', propError.message);
                            // 不抛出错误，继续使用其他方法
                        }
                    }
                } catch (e) {
                    console.warn('修复样式元素时出错:', e);
                }
            }
            
            return element;
        } catch (createError) {
            console.error('createElement修复出错:', createError.message);
            // 出错时返回原始方法的结果
            return originalCreateElement.call(document, tagName);
        }
    };
    
    console.log('createElement 修复已应用');
})();
