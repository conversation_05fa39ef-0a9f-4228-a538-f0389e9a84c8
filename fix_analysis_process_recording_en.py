"""
Nine Cats Novel Analysis System - Analysis Process Recording Fix Tool
Fix the analysis process recording functionality to ensure processes are properly recorded in the database
"""
import os
import sys
import logging
import sqlite3
from datetime import datetime

# Configure logging
log_filename = f"fix_analysis_process_recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def backup_database():
    """Backup the database"""
    try:
        backup_filename = f"novels_backup_process_recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        import shutil
        shutil.copy2("novels.db", backup_filename)
        logger.info(f"Database backed up as {backup_filename}")
        return True
    except Exception as e:
        logger.error(f"Failed to backup database: {str(e)}")
        return False

def check_table_exists(conn, table_name):
    """Check if a table exists"""
    cursor = conn.cursor()
    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
    return cursor.fetchone() is not None

def check_analysis_process_table(conn):
    """Check if the analysis process table exists and has the correct structure"""
    if not check_table_exists(conn, 'analysis_processes'):
        logger.error("Analysis process table does not exist, please run fix_db_for_analysis_process.py first")
        return False
    
    # Check table structure
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(analysis_processes)")
    columns = [row[1] for row in cursor.fetchall()]
    
    required_columns = [
        'id', 'novel_id', 'result_id', 'dimension', 'block_index', 'total_blocks',
        'processing_stage', 'stage_index', 'input_text', 'output_text', 'prompt_used',
        'api_request', 'api_response', 'processing_time', 'tokens_used', 'is_successful',
        'error_message', 'process_metadata', 'created_at'
    ]
    
    missing_columns = [col for col in required_columns if col not in columns]
    if missing_columns:
        logger.error(f"Analysis process table is missing the following columns: {missing_columns}")
        return False
    
    logger.info("Analysis process table structure is correct")
    return True

def check_analysis_results_table(conn):
    """Check if the analysis results table has the necessary columns"""
    if not check_table_exists(conn, 'analysis_results'):
        logger.error("Analysis results table does not exist")
        return False
    
    # Check table structure
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(analysis_results)")
    columns = [row[1] for row in cursor.fetchall()]
    
    required_columns = [
        'id', 'novel_id', 'dimension', 'content', 'analysis_metadata', 
        'analysis_logs', 'created_at', 'updated_at'
    ]
    
    missing_columns = [col for col in required_columns if col not in columns]
    if missing_columns:
        logger.error(f"Analysis results table is missing the following columns: {missing_columns}")
        return False
    
    logger.info("Analysis results table structure is correct")
    return True

def check_environment_variables():
    """Check if environment variables are set correctly"""
    env_vars = {
        'ENABLE_DETAILED_PROCESS_RECORDING': os.environ.get('ENABLE_DETAILED_PROCESS_RECORDING', 'False'),
        'SAVE_FULL_API_INTERACTIONS': os.environ.get('SAVE_FULL_API_INTERACTIONS', 'False'),
        'SAVE_PROMPTS': os.environ.get('SAVE_PROMPTS', 'False'),
        'ENHANCED_LOG_CATEGORIES': os.environ.get('ENHANCED_LOG_CATEGORIES', 'False'),
        'RECORD_INTERMEDIATE_RESULTS': os.environ.get('RECORD_INTERMEDIATE_RESULTS', 'False')
    }
    
    logger.info("Current environment variable settings:")
    for var, value in env_vars.items():
        logger.info(f"  {var} = {value}")
    
    # Check if any variables are not set to True
    missing_vars = [var for var, value in env_vars.items() if value.lower() != 'true']
    if missing_vars:
        logger.warning(f"The following environment variables are not set to True: {missing_vars}")
        logger.warning("This may cause the analysis process recording functionality to be incomplete")
        return False
    
    logger.info("All necessary environment variables are set correctly")
    return True

def create_test_process_record(conn):
    """Create a test analysis process record to verify functionality"""
    try:
        cursor = conn.cursor()
        
        # Get a valid novel ID
        cursor.execute("SELECT id FROM novels LIMIT 1")
        novel_id_row = cursor.fetchone()
        if not novel_id_row:
            logger.error("No novel records in the database, cannot create test record")
            return False
        
        novel_id = novel_id_row[0]
        
        # Get an analysis result ID for this novel
        cursor.execute("SELECT id FROM analysis_results WHERE novel_id = ? LIMIT 1", (novel_id,))
        result_id_row = cursor.fetchone()
        result_id = result_id_row[0] if result_id_row else None
        
        # Create test record
        cursor.execute("""
        INSERT INTO analysis_processes (
            novel_id, result_id, dimension, block_index, total_blocks,
            processing_stage, stage_index, input_text, output_text,
            prompt_used, processing_time, tokens_used, is_successful,
            process_metadata, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        """, (
            novel_id, result_id, 'test_dimension', 0, 1,
            'test', 0, 'Test input text', 'Test output text',
            'Test prompt', 100, 50, 1,
            '{"test": true}'
        ))
        
        conn.commit()
        logger.info(f"Created test analysis process record, novel ID: {novel_id}")
        
        # Verify record was created successfully
        cursor.execute("SELECT id FROM analysis_processes WHERE novel_id = ? AND processing_stage = 'test'", (novel_id,))
        test_record = cursor.fetchone()
        if test_record:
            logger.info(f"Test record created successfully, ID: {test_record[0]}")
            
            # Delete test record
            cursor.execute("DELETE FROM analysis_processes WHERE id = ?", (test_record[0],))
            conn.commit()
            logger.info("Test record deleted")
            return True
        else:
            logger.error("Test record creation failed")
            return False
    
    except Exception as e:
        logger.error(f"Error creating test record: {str(e)}")
        conn.rollback()
        return False

def create_process_recording_patch():
    """Create a patch file to fix the analysis process recording functionality"""
    patch_filename = "process_recording_patch.py"
    
    patch_content = """
# Nine Cats Novel Analysis System - Analysis Process Recording Patch
# Place this file in the src/services directory and import it in analysis_service.py

import logging
import json
import time
from datetime import datetime
from src.db.connection import Session
from src.models.analysis_process import AnalysisProcess

logger = logging.getLogger(__name__)

class ProcessRecorder:
    """Analysis process recorder for recording detailed information about the analysis process"""
    
    @staticmethod
    def record_process(novel_id, dimension, block_index, total_blocks, stage, 
                      input_text=None, output_text=None, prompt=None, 
                      api_request=None, api_response=None, 
                      processing_time=None, tokens=None, 
                      is_successful=True, error_message=None,
                      result_id=None, metadata=None):
        """
        Record an analysis process
        
        Args:
            novel_id: Novel ID
            dimension: Analysis dimension
            block_index: Block index
            total_blocks: Total number of blocks
            stage: Processing stage
            input_text: Input text
            output_text: Output text
            prompt: Prompt used
            api_request: API request
            api_response: API response
            processing_time: Processing time (milliseconds)
            tokens: Number of tokens used
            is_successful: Whether the process was successful
            error_message: Error message if any
            result_id: Analysis result ID
            metadata: Additional metadata
        """
        # Check if detailed process recording is enabled
        if not ProcessRecorder.is_process_recording_enabled():
            logger.debug(f"Detailed process recording is not enabled, skipping record")
            return None
        
        try:
            session = Session()
            
            # Create analysis process record
            process = AnalysisProcess(
                novel_id=novel_id,
                dimension=dimension,
                block_index=block_index,
                total_blocks=total_blocks,
                processing_stage=stage,
                result_id=result_id,
                input_text=input_text,
                output_text=output_text,
                prompt_used=prompt,
                api_request=api_request,
                api_response=api_response,
                processing_time=processing_time,
                tokens_used=tokens,
                is_successful=is_successful,
                error_message=error_message,
                metadata=metadata or {}
            )
            
            session.add(process)
            session.commit()
            
            process_id = process.id
            logger.debug(f"Recorded analysis process: ID={process_id}, novel_id={novel_id}, dimension={dimension}, stage={stage}")
            
            session.close()
            return process_id
        
        except Exception as e:
            logger.error(f"Error recording analysis process: {str(e)}")
            if session:
                session.rollback()
                session.close()
            return None
    
    @staticmethod
    def is_process_recording_enabled():
        """Check if detailed process recording is enabled"""
        import os
        return os.environ.get('ENABLE_DETAILED_PROCESS_RECORDING', 'False').lower() == 'true'
    
    @staticmethod
    def record_init(novel_id, dimension, input_text=None, metadata=None):
        """Record initialization stage"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            stage='init',
            input_text=input_text,
            metadata=metadata
        )
    
    @staticmethod
    def record_chunk_analysis(novel_id, dimension, block_index, total_blocks, 
                             input_text, output_text=None, prompt=None,
                             api_request=None, api_response=None,
                             processing_time=None, tokens=None,
                             is_successful=True, error_message=None):
        """Record chunk analysis stage"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=block_index,
            total_blocks=total_blocks,
            stage='chunk_analysis',
            input_text=input_text,
            output_text=output_text,
            prompt=prompt,
            api_request=api_request,
            api_response=api_response,
            processing_time=processing_time,
            tokens=tokens,
            is_successful=is_successful,
            error_message=error_message
        )
    
    @staticmethod
    def record_combine(novel_id, dimension, input_chunks, output_text=None,
                      processing_time=None, is_successful=True, error_message=None):
        """Record result combination stage"""
        # Convert input chunks to string
        if isinstance(input_chunks, list):
            input_text = json.dumps(input_chunks, ensure_ascii=False)
        else:
            input_text = str(input_chunks)
        
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            stage='combine',
            input_text=input_text,
            output_text=output_text,
            processing_time=processing_time,
            is_successful=is_successful,
            error_message=error_message
        )
    
    @staticmethod
    def record_finalize(novel_id, dimension, result_id, input_text=None, 
                       output_text=None, processing_time=None,
                       is_successful=True, error_message=None):
        """Record finalization stage"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            stage='finalize',
            result_id=result_id,
            input_text=input_text,
            output_text=output_text,
            processing_time=processing_time,
            is_successful=is_successful,
            error_message=error_message
        )
    
    @staticmethod
    def record_error(novel_id, dimension, stage, error_message,
                    block_index=0, total_blocks=1, input_text=None):
        """Record error"""
        return ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension=dimension,
            block_index=block_index,
            total_blocks=total_blocks,
            stage=stage,
            input_text=input_text,
            is_successful=False,
            error_message=error_message
        )

# Export ProcessRecorder class
__all__ = ['ProcessRecorder']
"""
    
    with open(patch_filename, 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    logger.info(f"Created analysis process recording patch file: {patch_filename}")
    logger.info(f"Please copy this file to the src/services directory and import it in analysis_service.py")
    
    return patch_filename

def create_integration_instructions():
    """Create integration instructions file"""
    instructions_filename = "process_recording_integration.md"
    
    instructions_content = """# Nine Cats Novel Analysis System - Analysis Process Recording Integration Guide

## Problem Description

The current system does not properly record detailed information about the analysis process when analyzing novels, resulting in the "Analysis Process" section of the analysis detail page being blank.

## Solution

1. Ensure the database table structure is correct
2. Add analysis process recording functionality
3. Ensure environment variables are set correctly

## Integration Steps

### 1. Copy the Patch File

Copy the `process_recording_patch.py` file to the `src/services` directory.

### 2. Modify analysis_service.py

Add the following import statement to the `src/services/analysis_service.py` file:

```python
from src.services.process_recording_patch import ProcessRecorder
```

### 3. Add Analysis Process Recording in the run_analysis Function

In the `run_analysis` function, add the following recording code at the appropriate locations:

#### Initialization Stage

After updating the task status to 'in_progress', add:

```python
# Record initialization stage
ProcessRecorder.record_init(novel_id, dimension, input_text=None, metadata={
    'start_time': time.time(),
    'task_id': task_id
})
```

#### Chunk Analysis Stage

In the text analysis stage, add:

```python
# Record chunk analysis stage
ProcessRecorder.record_chunk_analysis(
    novel_id=novel_id,
    dimension=dimension,
    block_index=0,  # Replace with actual block index
    total_blocks=1,  # Replace with actual total blocks
    input_text="Analysis text content",  # Replace with actual input text
    output_text="Analysis result",  # Replace with actual output text
    prompt="Prompt used",  # Replace with actual prompt
    processing_time=processing_time_ms,  # Processing time in milliseconds
    tokens=tokens_used  # Number of tokens used
)
```

#### Result Combination Stage

Before generating the analysis result, add:

```python
# Record result combination stage
ProcessRecorder.record_combine(
    novel_id=novel_id,
    dimension=dimension,
    input_chunks=chunk_results,  # List of chunk results
    output_text="Combined result",  # Replace with actual combined result
    processing_time=combine_time_ms  # Combination processing time in milliseconds
)
```

#### Finalization Stage

After saving the analysis result, add:

```python
# Record finalization stage
ProcessRecorder.record_finalize(
    novel_id=novel_id,
    dimension=dimension,
    result_id=result.id,  # Analysis result ID
    output_text=result.content,  # Final analysis content
    processing_time=total_time_ms  # Total processing time in milliseconds
)
```

#### Error Handling

In the error handling section, add:

```python
# Record error
ProcessRecorder.record_error(
    novel_id=novel_id,
    dimension=dimension,
    stage='error_handling',
    error_message=str(e)
)
```

### 4. Ensure Environment Variables are Set Correctly

In the startup script, ensure the following environment variables are set to `True`:

- `ENABLE_DETAILED_PROCESS_RECORDING`
- `SAVE_FULL_API_INTERACTIONS`
- `SAVE_PROMPTS`
- `ENHANCED_LOG_CATEGORIES`
- `RECORD_INTERMEDIATE_RESULTS`

### 5. Restart the System

Use the `启动九猫_分析过程修复版.vbs` script to start the system, and perform a new analysis.

## Verification

1. Perform a new analysis
2. View the "View Analysis Process" link on the analysis detail page
3. Confirm that the analysis process page shows detailed analysis steps and intermediate results

## Troubleshooting

If the analysis process is still blank, check:

1. If there are records in the `analysis_processes` table in the database
2. If the environment variables are set correctly
3. If there are any related error messages in the logs
"""
    
    with open(instructions_filename, 'w', encoding='utf-8') as f:
        f.write(instructions_content)
    
    logger.info(f"Created integration instructions file: {instructions_filename}")
    
    return instructions_filename

def main():
    """Main function"""
    logger.info("========== Starting Analysis Process Recording Fix ==========")
    
    # Backup database
    if not backup_database():
        logger.error("Failed to backup database, aborting operation")
        return
    
    try:
        # Connect to database
        conn = sqlite3.connect("novels.db")
        
        # Check table structure
        if not check_analysis_process_table(conn):
            logger.error("Analysis process table structure is incorrect, please run fix_db_for_analysis_process.py first")
            conn.close()
            return
        
        if not check_analysis_results_table(conn):
            logger.warning("Analysis results table structure is incomplete, may affect functionality")
        
        # Check environment variables
        check_environment_variables()
        
        # Create test record
        if create_test_process_record(conn):
            logger.info("Database table structure test successful, can record analysis processes normally")
        else:
            logger.warning("Database table structure test failed, may not be able to record analysis processes normally")
        
        # Close connection
        conn.close()
        
        # Create patch file
        patch_file = create_process_recording_patch()
        
        # Create integration instructions
        instructions_file = create_integration_instructions()
        
        print("\n✓ Fix tool execution complete!")
        print(f"✓ Created patch file: {patch_file}")
        print(f"✓ Created integration instructions: {instructions_file}")
        print("✓ Please follow the steps in the integration instructions to fix the issue")
        
    except Exception as e:
        logger.error(f"Error during fix process: {str(e)}")
        print("\n✗ Fix tool execution failed")
        print(f"✗ Please check the log: {log_filename}")
    
    logger.info("========== Fix Tool Execution Complete ==========")

if __name__ == "__main__":
    main()
