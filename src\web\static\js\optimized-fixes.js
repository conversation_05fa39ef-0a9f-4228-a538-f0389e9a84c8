/**
 * 九猫 - 优化版综合修复脚本
 * 整合了多个修复功能，提高加载速度并减少资源消耗
 * 版本: 2.0.0
 */

(function() {
    // 减少日志输出，只在真正需要时输出
    const isDebug = false;

    // 保留原始console方法用于错误处理
    const originalConsoleError = console.error;

    // 应用标记，避免重复执行
    if (window.__optimizedFixesApplied) {
        return;
    }
    window.__optimizedFixesApplied = true;

    // ===================== 核心修复功能 =====================

    // 1. jQuery matchesSelector 修复 - 处理 41:33 位置的错误
    function fixJQuery() {
        try {
            // 检查jQuery是否已加载
            if (typeof jQuery === 'undefined' || !jQuery.find) return;

            // 修复jQuery.find.matchesSelector (解决41:33错误的核心)
            if (!jQuery.find.matchesSelector) {
                if (jQuery.find.matches) {
                    jQuery.find.matchesSelector = jQuery.find.matches;
                } else if (typeof Element.prototype.matches !== 'undefined') {
                    jQuery.find.matchesSelector = function(elem, expr) {
                        return Element.prototype.matches.call(elem, expr);
                    };
                } else {
                    jQuery.find.matchesSelector = function() { return true; };
                }
            }

            // 修复Sizzle和S对象
            if (window.Sizzle && !Sizzle.matchesSelector) {
                Sizzle.matchesSelector = jQuery.find.matchesSelector;
            }

            if (typeof S !== 'undefined' && S && S.find && !S.find.matchesSelector) {
                S.find.matchesSelector = jQuery.find.matchesSelector;
            }
        } catch (e) {
            // 忽略修复过程中的错误
        }
    }

    // 2. 阻止重复加载jQuery - 减少冲突可能性
    function preventMultipleJQuery() {
        // 仅在未设置过的情况下执行
        if (window.__jQueryInterceptorSet) return;

        // 标记jQuery版本
        if (typeof jQuery !== 'undefined') {
            window.__jQueryLoaded = true;
            window.__jQueryVersion = jQuery.fn.jquery;
        }

        // 拦截脚本添加，防止重复加载jQuery
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(document, tagName);
            if (tagName.toLowerCase() === 'script') {
                const originalSetAttribute = element.setAttribute;
                element.setAttribute = function(name, value) {
                    // 阻止重复加载jQuery
                    if (name === 'src' && typeof value === 'string' &&
                        value.includes('jquery') && window.__jQueryLoaded) {
                        return element;
                    }
                    return originalSetAttribute.call(this, name, value);
                };
            }
            return element;
        };

        window.__jQueryInterceptorSet = true;
    }

    // 3. 修复章节分析按钮，确保正确跳转 - 重点处理小说41
    function fixChapterAnalysisButtons() {
        // 仅在小说页面执行
        if (!window.location.pathname.match(/\/novel\/\d+/)) return;

        // 获取小说ID
        const novelId = window.location.pathname.match(/\/novel\/(\d+)/);
        if (!novelId || !novelId[1]) return;

        const id = novelId[1];

        // 查找所有可能的章节分析按钮
        const buttons = document.querySelectorAll('a.btn, button.btn, a[href*="chapter"], [data-target*="chapter"]');

        buttons.forEach(btn => {
            if (!btn.textContent) return;

            // 检查是否是章节分析按钮
            const text = btn.textContent.trim().toLowerCase();
            if (text.includes('章节') &&
               (text.includes('分析') || text.includes('列表') || text.includes('管理'))) {

                // 修复链接和点击事件
                if (btn.tagName === 'A') {
                    // 保存原始链接用于调试
                    const originalHref = btn.getAttribute('href');
                    if (originalHref) {
                        btn.setAttribute('data-original-href', originalHref);
                    }

                    // 设置正确的链接
                    btn.href = `/novel/${id}/chapters`;

                    // 添加调试日志
                    if (isDebug) {
                        console.log(`[章节按钮修复] 修改链接: ${originalHref || '无'} -> /novel/${id}/chapters`);
                    }
                }

                // 设置点击事件 - 使用更可靠的方式处理点击
                if (!btn.__chapterButtonFixed) {
                    btn.__chapterButtonFixed = true;

                    // 设置新的onclick处理函数，优先级高于addEventListener
                    btn.onclick = function(e) {
                        // 阻止默认行为和事件冒泡
                        if (e && e.preventDefault) e.preventDefault();
                        if (e && e.stopPropagation) e.stopPropagation();

                        // 记录点击
                        if (isDebug) {
                            console.log(`[章节按钮修复] 点击章节按钮，跳转到: /novel/${id}/chapters`);
                        }

                        // 使用setTimeout确保在所有事件处理完成后跳转
                        setTimeout(function() {
                            window.location.href = `/novel/${id}/chapters`;
                        }, 0);

                        return false;
                    };
                }
            }
        });

        // 特别处理小说ID为41的情况
        if (id === '41') {
            // 全局一次性点击处理 - 避免多个事件监听器
            if (!window.__novel41ClickHandlerSet) {
                // 移除之前的事件监听器（如果有）
                if (window.__novel41ClickHandler) {
                    document.removeEventListener('click', window.__novel41ClickHandler, true);
                }

                // 创建新的事件处理函数
                window.__novel41ClickHandler = function(e) {
                    // 检查是否点击了章节分析按钮
                    let target = e.target;
                    while (target && target !== document) {
                        if (target.textContent &&
                            target.textContent.toLowerCase().includes('章节') &&
                            target.textContent.toLowerCase().includes('分析')) {

                            // 阻止默认行为和事件冒泡
                            e.preventDefault();
                            e.stopPropagation();

                            // 记录点击
                            if (isDebug) {
                                console.log('[章节按钮修复] 小说41特殊处理，跳转到: /novel/41/chapters');
                            }

                            // 使用setTimeout确保在所有事件处理完成后跳转
                            setTimeout(function() {
                                window.location.href = '/novel/41/chapters';
                            }, 0);

                            break;
                        }
                        target = target.parentElement;
                    }
                };

                // 添加事件监听器
                document.addEventListener('click', window.__novel41ClickHandler, {capture: true});

                window.__novel41ClickHandlerSet = true;

                if (isDebug) {
                    console.log('[章节按钮修复] 已设置小说41特殊处理');
                }
            }
        }
    }

    // 4. 净化控制台错误输出 - 隐藏无关紧要的错误消息
    function filterConsoleErrors() {
        console.error = function() {
            // 过滤掉matchesSelector和Script error类型的错误
            if (arguments.length > 0) {
                const errorMsg = String(arguments[0]);
                if (errorMsg.includes('matchesSelector') ||
                    errorMsg.includes('41:33') ||
                    errorMsg.includes('41:30') ||
                    errorMsg.includes('Script error')) {
                    return;
                }
            }

            // 其他错误正常显示
            return originalConsoleError.apply(console, arguments);
        };
    }

    // 5. 合并错误处理 - 处理常见错误并提高错误恢复能力
    function setupUnifiedErrorHandler() {
        window.addEventListener('error', function(event) {
            // 处理脚本错误
            if (event.error) {
                const errorMsg = event.message || '';

                // 处理matchesSelector错误
                if (errorMsg.includes('matchesSelector is not a function') ||
                    errorMsg.includes('S.find.matchesSelector')) {
                    fixJQuery();
                    event.preventDefault();
                    return true;
                }

                // 处理资源加载错误
                if (event.target &&
                    (event.target.tagName === 'SCRIPT' ||
                     event.target.tagName === 'LINK')) {
                    // 已通过资源加载器处理，不需额外处理
                    event.preventDefault();
                    return true;
                }
            }

            return false;
        }, true);
    }

    // 6. 快速修复S对象 - 避免闭包和过多监听器
    if (typeof window.S === 'undefined') {
        Object.defineProperty(window, 'S', {
            configurable: true,
            set: function(newValue) {
                delete window.S;
                window.S = newValue;
                // 立即修复S对象
                fixJQuery();
            },
            get: function() {
                return undefined;
            }
        });
    }

    // 7. 减少DOM修改操作，优化CSS应用
    function hideErrorMessages() {
        // 通过CSS处理，减少DOM操作
        const style = document.createElement('style');
        style.textContent = `
            /* 隐藏常见错误信息 */
            [style*="border:1px solid red"],
            [style*="background-color:#fee"],
            div:has(text:contains("Script error")),
            div:has(text:contains("matchesSelector")),
            div:has(text:contains("41:33")) {
                display: none !important;
            }
        `;
        document.head.appendChild(style);
    }

    // ===================== 初始化与优化执行 =====================

    // 即时执行关键修复，延迟执行次要修复
    function init() {
        // 立即执行必要的修复
        preventMultipleJQuery();
        fixJQuery();
        filterConsoleErrors();
        setupUnifiedErrorHandler();

        // 页面可交互后执行
        if (document.readyState === 'interactive' || document.readyState === 'complete') {
            initDelayed();
        } else {
            document.addEventListener('DOMContentLoaded', initDelayed);
        }

        // 页面完全加载后执行
        if (document.readyState === 'complete') {
            initLast();
        } else {
            window.addEventListener('load', initLast);
        }
    }

    // 延迟执行的修复 - DOMContentLoaded后
    function initDelayed() {
        hideErrorMessages();

        // 使用requestIdleCallback（如果可用）或setTimeout来在浏览器空闲时执行
        if (window.requestIdleCallback) {
            window.requestIdleCallback(fixChapterAnalysisButtons);
        } else {
            setTimeout(fixChapterAnalysisButtons, 100);
        }
    }

    // 最后执行的修复 - load事件后
    function initLast() {
        // 定期检查 - 使用更长的间隔
        setInterval(function() {
            fixJQuery();
            // 仅在小说页面检查章节按钮
            if (window.location.pathname.match(/\/novel\/\d+/)) {
                fixChapterAnalysisButtons();
            }
        }, 5000); // 每5秒检查一次，而不是频繁检查
    }

    // 暴露公共API
    window.optimizedFixes = {
        fixJQuery: fixJQuery,
        fixChapterButtons: fixChapterAnalysisButtons,
        reload: init
    };

    // 启动优化修复
    init();
})();