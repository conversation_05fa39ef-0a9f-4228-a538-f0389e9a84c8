/**
 * 九猫系统 - 本地资源修复脚本
 * 版本: 1.0.0
 *
 * 该脚本专门用于解决CDN资源连接超时问题
 * 强制使用本地资源，不依赖CDN
 */

(function() {
    console.log('[九猫修复] 本地资源修复脚本已加载');

    // 检查是否已应用此修复
    if (window.__nineCatsFixes && window.__nineCatsFixes.loaded && window.__nineCatsFixes.loaded['local-resources-fix']) {
        console.log('[九猫修复] 本地资源修复脚本已经应用，跳过');
        return;
    }

    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {} };

    // 本地资源映射
    const localResources = {
        // jQuery
        jquery: '/static/js/lib/jquery.min.js',
        // Bootstrap JS
        bootstrap: '/static/js/lib/bootstrap.bundle.min.js',
        // Bootstrap CSS
        bootstrapCss: '/static/css/bootstrap.min.css',
        // Bootstrap Icons
        bootstrapIcons: '/static/css/bootstrap-icons.css',
        // Font Awesome
        fontawesome: '/static/css/fontawesome.min.css',
        // Chart.js
        chartjs: '/static/js/lib/chart.min.js'
    };

    // 资源加载状态
    const resourceState = {
        loaded: {},
        failed: {},
        attempts: {}
    };

    // 立即检查并加载关键资源
    function checkAndLoadCriticalResources() {
        // 检查jQuery
        if (typeof jQuery === 'undefined') {
            console.log('[九猫修复] jQuery未检测到，加载本地版本');
            loadLocalResource('jquery', 'script');
        }

        // 检查Bootstrap
        if (typeof bootstrap === 'undefined') {
            console.log('[九猫修复] Bootstrap未检测到，加载本地版本');
            loadLocalResource('bootstrap', 'script');
        }

        // 检查Bootstrap CSS
        let hasBootstrapCSS = false;
        document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            if (link.href && link.href.includes('bootstrap') && link.href.includes('bootstrap.min.css') && link.sheet) {
                hasBootstrapCSS = true;
            }
        });

        if (!hasBootstrapCSS) {
            console.log('[九猫修复] Bootstrap CSS未检测到或加载失败，加载本地版本');
            loadLocalResource('bootstrapCss', 'style');
        }

        // 检查Bootstrap Icons
        let hasBootstrapIcons = false;
        document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            if (link.href && link.href.includes('bootstrap-icons') && link.sheet) {
                hasBootstrapIcons = true;
            }
        });

        if (!hasBootstrapIcons) {
            console.log('[九猫修复] Bootstrap Icons未检测到或加载失败，加载本地版本');
            loadLocalResource('bootstrapIcons', 'style');

            // 添加备用图标样式
            const backupIconStyle = document.createElement('style');
            backupIconStyle.textContent = `
                /* 备用图标样式 */
                .bi-arrow-up::before { content: "↑"; }
                .bi-arrow-down::before { content: "↓"; }
                .bi-arrow-left::before { content: "←"; }
                .bi-arrow-right::before { content: "→"; }
                .bi-check::before { content: "✓"; }
                .bi-x::before { content: "✕"; }
                .bi-plus::before { content: "+"; }
                .bi-dash::before { content: "-"; }
                .bi-exclamation::before { content: "!"; }
                .bi-info::before { content: "i"; }
                .bi-question::before { content: "?"; }
                .bi-search::before { content: "🔍"; }
                .bi-eye::before { content: "👁"; }
                .bi-person::before { content: "👤"; }
                .bi-house::before { content: "🏠"; }
                .bi-gear::before { content: "⚙"; }
                .bi-file::before { content: "📄"; }
                .bi-folder::before { content: "📁"; }
                .bi-bookmark::before { content: "🔖"; }
                .bi-heart::before { content: "❤"; }
                .bi-star::before { content: "★"; }
                .bi-trash::before { content: "🗑"; }
                .bi-pencil::before { content: "✏"; }
                .bi-chat::before { content: "💬"; }
                .bi-bell::before { content: "🔔"; }
                .bi-envelope::before { content: "✉"; }
                .bi-calendar::before { content: "📅"; }
                .bi-clock::before { content: "🕒"; }
                .bi-image::before { content: "🖼"; }
                .bi-camera::before { content: "📷"; }
                .bi-play::before { content: "▶"; }
                .bi-pause::before { content: "⏸"; }
                .bi-list::before { content: "☰"; }
                .bi-grid::before { content: "⊞"; }
                .bi-filter::before { content: "⚙"; }
                .bi-three-dots::before { content: "⋯"; }
                .bi-arrow-clockwise::before { content: "↻"; }
                .bi-arrow-counterclockwise::before { content: "↺"; }
                .bi-download::before { content: "⬇"; }
                .bi-upload::before { content: "⬆"; }
                .bi-link::before { content: "🔗"; }
                .bi-emoji-smile::before { content: "😊"; }
                .bi-hand-thumbs-up::before { content: "👍"; }
                .bi-hand-thumbs-down::before { content: "👎"; }
                .bi-share::before { content: "↗"; }
                .bi-printer::before { content: "🖨"; }
                .bi-lock::before { content: "🔒"; }
                .bi-unlock::before { content: "🔓"; }
                .bi-shield::before { content: "🛡"; }
                .bi-flag::before { content: "🚩"; }
            `;
            document.head.appendChild(backupIconStyle);
            console.log('[九猫修复] 已添加备用图标样式');
        }
    }

    // 加载本地资源
    function loadLocalResource(resourceType, elementType) {
        if (!localResources[resourceType]) {
            console.error('[九猫修复] 未找到本地资源:', resourceType);
            return;
        }

        const resourcePath = localResources[resourceType];
        console.log('[九猫修复] 加载本地资源:', resourcePath);

        if (elementType === 'script') {
            const script = document.createElement('script');
            script.src = resourcePath;
            script.async = false; // 确保按顺序执行

            // 添加加载事件监听
            script.addEventListener('load', function() {
                console.log('[九猫修复] 本地资源加载成功:', resourcePath);
                resourceState.loaded[resourcePath] = true;
            });

            script.addEventListener('error', function() {
                console.error('[九猫修复] 本地资源加载失败:', resourcePath);
                resourceState.failed[resourcePath] = true;

                // 尝试使用备用路径
                const backupPath = resourcePath.replace('/static/', '/direct-static/');
                if (!resourceState.attempts[backupPath]) {
                    resourceState.attempts[backupPath] = true;
                    console.log('[九猫修复] 尝试使用备用路径:', backupPath);

                    const backupScript = document.createElement('script');
                    backupScript.src = backupPath;
                    backupScript.async = false;
                    document.head.appendChild(backupScript);
                }
            });

            document.head.appendChild(script);
        } else if (elementType === 'style') {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = resourcePath;

            // 添加加载事件监听
            link.addEventListener('load', function() {
                console.log('[九猫修复] 本地样式表加载成功:', resourcePath);
                resourceState.loaded[resourcePath] = true;
            });

            link.addEventListener('error', function() {
                console.error('[九猫修复] 本地样式表加载失败:', resourcePath);
                resourceState.failed[resourcePath] = true;

                // 尝试使用备用路径
                const backupPath = resourcePath.replace('/static/', '/direct-static/');
                if (!resourceState.attempts[backupPath]) {
                    resourceState.attempts[backupPath] = true;
                    console.log('[九猫修复] 尝试使用备用路径:', backupPath);

                    const backupLink = document.createElement('link');
                    backupLink.rel = 'stylesheet';
                    backupLink.href = backupPath;
                    document.head.appendChild(backupLink);
                }
            });

            document.head.appendChild(link);
        }
    }

    // 立即执行
    checkAndLoadCriticalResources();

    // 页面加载完成后再次检查
    window.addEventListener('load', checkAndLoadCriticalResources);

    // 标记修复已加载
    window.__nineCatsFixes.loaded['local-resources-fix'] = true;

    console.log('[九猫修复] 本地资源修复脚本加载完成');
})();
