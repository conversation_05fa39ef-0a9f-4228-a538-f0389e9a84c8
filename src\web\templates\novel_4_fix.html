{% extends "base.html" %}

{% block title %}小说分析 - 修复版{% endblock %}

{% block head %}
{{ super() }}
<!-- 加载修复脚本 -->
<script src="{{ url_for('static', filename='js/json-parse-fix-enhanced.js') }}"></script>
<script src="{{ url_for('static', filename='js/character-relationships-fix.js') }}"></script>
<script src="{{ url_for('static', filename='js/character-relationships-canvas-fix.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1>{{ novel.title }}</h1>
        <a href="{{ url_for('novel.novel_list') }}" class="btn btn-outline-secondary">
            返回小说列表
        </a>
    </div>

    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">小说信息</h5>
                </div>
                <div class="card-body">
                    <p><strong>作者:</strong> {{ novel.author }}</p>
                    <p><strong>字数:</strong> {{ novel.word_count }}</p>
                    <p><strong>上传时间:</strong> {{ novel.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    <p><strong>最后更新:</strong> {{ novel.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>

                    <div class="mt-3">
                        <a href="{{ url_for('novel.edit_novel', novel_id=novel.id) }}" class="btn btn-outline-primary">编辑信息</a>
                        <a href="{{ url_for('novel.view_novel_text', novel_id=novel.id) }}" class="btn btn-outline-secondary">查看全文</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">分析维度</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for dimension in dimensions %}
                        <div class="col-md-6 mb-4">
                            <div class="card analysis-card" data-dimension="{{ dimension.key }}">
                                <div class="card-header">
                                    <h5 class="card-title">
                                        {{ dimension.name }}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    {% if dimension.key in analysis_results %}
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="badge bg-success">分析完成</span>
                                            <div>
                                                <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                                        data-dimension="{{ dimension.key }}"
                                                        data-novel-id="{{ novel.id }}">
                                                    <i class="fas fa-sync"></i> 重新分析
                                                </button>
                                                <a href="{{ url_for('novel.view_analysis', novel_id=novel.id, dimension=dimension.key) }}" class="btn btn-sm btn-outline-primary">查看详情</a>
                                            </div>
                                        </div>
                                        <div class="analysis-visualization">
                                            <canvas class="analysis-chart" data-dimension="{{ dimension.key }}" width="400" height="250"></canvas>
                                        </div>
                                        <div class="analysis-excerpt mt-2">
                                            {{ analysis_results[dimension.key].content[:300] + '...' if analysis_results[dimension.key].content else '无内容' }}
                                        </div>
                                    {% else %}
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="badge bg-secondary">未分析</span>
                                            <button class="btn btn-sm btn-primary analyze-btn"
                                                    data-dimension="{{ dimension.key }}"
                                                    data-novel-id="{{ novel.id }}">
                                                开始分析
                                            </button>
                                        </div>
                                        <div class="analysis-placeholder">
                                            <p class="text-muted">点击"开始分析"按钮进行分析</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析进度模态框 -->
<div class="modal fade" id="analysisProgressModal" tabindex="-1" aria-labelledby="analysisProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analysisProgressModalLabel">分析进行中</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="mt-3" id="progressMessage">
                    正在准备分析...
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelAnalysisBtn">取消分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 全局变量，用于存储分析结果数据
    let analysisResultsData = {{ analysis_results_json|safe }};

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化所有图表
        if (typeof initializeCharts === 'function') {
            initializeCharts();
        }

        // 绑定分析按钮事件
        document.querySelectorAll('.analyze-btn').forEach(button => {
            button.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');
                const novelId = this.getAttribute('data-novel-id');
                startAnalysis(novelId, dimension);
            });
        });

        // 绑定重新分析按钮事件
        document.querySelectorAll('.reanalyze-btn').forEach(button => {
            button.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');
                const novelId = this.getAttribute('data-novel-id');

                if (confirm(`确定要重新分析 ${dimension} 维度吗？这将覆盖现有的分析结果。`)) {
                    startAnalysis(novelId, dimension);
                }
            });
        });
    });

    // 开始分析
    function startAnalysis(novelId, dimension) {
        console.log(`开始分析小说 ${novelId} 的 ${dimension} 维度`);

        // 显示进度模态框
        const progressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
        progressModal.show();

        // 重置进度条
        const progressBar = document.querySelector('#analysisProgressModal .progress-bar');
        progressBar.style.width = '0%';
        document.getElementById('progressMessage').textContent = '正在准备分析...';

        // 发送分析请求
        fetch(`/api/analysis/start`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                novel_id: novelId,
                dimension: dimension
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('分析请求已发送:', data);

            if (data.success) {
                // 开始轮询分析进度
                const taskId = data.task_id;
                pollAnalysisProgress(taskId, novelId, dimension);

                // 绑定取消分析按钮
                document.getElementById('cancelAnalysisBtn').onclick = function() {
                    cancelAnalysis(taskId);
                    progressModal.hide();
                };
            } else {
                alert(`分析请求失败: ${data.message}`);
                progressModal.hide();
            }
        })
        .catch(error => {
            console.error('分析请求错误:', error);
            alert(`请求错误: ${error.message}`);
            progressModal.hide();
        });
    }

    // 轮询分析进度
    function pollAnalysisProgress(taskId, novelId, dimension) {
        console.log(`轮询分析任务 ${taskId} 的进度`);

        const progressBar = document.querySelector('#analysisProgressModal .progress-bar');
        const progressMessage = document.getElementById('progressMessage');

        // 设置轮询间隔
        const pollInterval = setInterval(() => {
            fetch(`/api/analysis/progress/${taskId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('分析进度:', data);

                if (data.success) {
                    // 更新进度条
                    const progress = data.progress || 0;
                    progressBar.style.width = `${progress}%`;
                    progressMessage.textContent = data.message || '分析中...';

                    // 检查是否完成
                    if (data.status === 'completed') {
                        clearInterval(pollInterval);

                        // 显示完成消息
                        progressBar.style.width = '100%';
                        progressMessage.textContent = '分析完成！正在更新页面...';

                        // 获取分析结果
                        getAnalysisResult(novelId, dimension);

                        // 关闭模态框
                        setTimeout(() => {
                            const progressModal = bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal'));
                            if (progressModal) {
                                progressModal.hide();
                            }
                        }, 1500);
                    } else if (data.status === 'failed') {
                        clearInterval(pollInterval);

                        // 显示错误消息
                        progressBar.classList.remove('bg-primary');
                        progressBar.classList.add('bg-danger');
                        progressMessage.textContent = `分析失败: ${data.message}`;

                        // 不自动关闭模态框，让用户手动关闭
                    }
                } else {
                    console.error('获取进度失败:', data.message);
                }
            })
            .catch(error => {
                console.error('轮询进度错误:', error);
                clearInterval(pollInterval);

                // 显示错误消息
                progressBar.classList.remove('bg-primary');
                progressBar.classList.add('bg-danger');
                progressMessage.textContent = `获取进度错误: ${error.message}`;
            });
        }, 2000); // 每2秒轮询一次
    }

    // 获取分析结果
    function getAnalysisResult(novelId, dimension) {
        console.log(`获取小说 ${novelId} 的 ${dimension} 分析结果`);

        fetch(`/api/analysis/result/${novelId}/${dimension}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('获取分析结果成功:', data);

            if (data.success) {
                // 更新全局数据
                if (!analysisResultsData) {
                    analysisResultsData = {};
                }
                analysisResultsData[dimension] = data.result;

                // 更新页面
                updateDimensionCard(dimension, data.result);
            } else {
                alert(`获取分析结果失败: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('获取分析结果错误:', error);
            alert(`请求错误: ${error.message}`);
        });
    }

    // 取消分析
    function cancelAnalysis(taskId) {
        console.log(`取消分析任务 ${taskId}`);

        fetch(`/api/analysis/cancel/${taskId}`, {
            method: 'POST'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('取消分析请求结果:', data);

            if (data.success) {
                console.log('分析已取消');
            } else {
                console.error('取消分析失败:', data.message);
            }
        })
        .catch(error => {
            console.error('取消分析请求错误:', error);
            alert(`请求错误: ${error.message}`);
        });
    }
</script>
{% endblock %}
