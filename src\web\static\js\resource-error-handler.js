/**
 * 九猫系统 - 资源错误处理器
 * 版本: 1.0.0
 * 
 * 该脚本专门处理资源加载错误，提供备用方案
 */

(function() {
    console.log('[九猫修复] 资源错误处理器已启动');
    
    // 初始化九猫修复对象
    window.__nineCatsFixes = window.__nineCatsFixes || { loaded: {}, settings: {} };
    
    // 如果已经运行过，不再重复执行
    if (window.__nineCatsFixes.loaded['resource-error-handler']) {
        console.log('[九猫修复] 资源错误处理器已经运行过，跳过');
        return;
    }
    
    // 资源映射
    const resourceMap = {
        // jQuery
        'jquery': {
            cdn: [
                'jquery.min.js',
                'jquery.js',
                'jquery-3.6'
            ],
            local: '/static/js/lib/jquery.min.js',
            check: function() { return typeof jQuery !== 'undefined'; },
            fallback: function() {
                console.log('[九猫修复] 加载jQuery内联备用');
                // 最小版本的jQuery，只包含基本选择器和DOM操作
                window.jQuery = window.$ = function(selector) {
                    if (typeof selector === 'function') {
                        if (document.readyState === 'loading') {
                            document.addEventListener('DOMContentLoaded', selector);
                        } else {
                            selector();
                        }
                        return;
                    }
                    
                    const elements = typeof selector === 'string' 
                        ? document.querySelectorAll(selector) 
                        : [selector];
                    
                    const jQueryObject = {
                        elements: Array.from(elements),
                        length: elements.length,
                        
                        // 基本选择器方法
                        find: function(subSelector) {
                            const results = [];
                            this.elements.forEach(el => {
                                const found = el.querySelectorAll(subSelector);
                                results.push(...found);
                            });
                            return jQuery(results);
                        },
                        
                        // 基本DOM操作
                        html: function(content) {
                            if (content === undefined) {
                                return this.elements[0] ? this.elements[0].innerHTML : '';
                            }
                            this.elements.forEach(el => el.innerHTML = content);
                            return this;
                        },
                        text: function(content) {
                            if (content === undefined) {
                                return this.elements[0] ? this.elements[0].textContent : '';
                            }
                            this.elements.forEach(el => el.textContent = content);
                            return this;
                        },
                        
                        // 基本事件处理
                        on: function(event, handler) {
                            this.elements.forEach(el => el.addEventListener(event, handler));
                            return this;
                        },
                        off: function(event, handler) {
                            this.elements.forEach(el => el.removeEventListener(event, handler));
                            return this;
                        }
                    };
                    
                    // 添加索引访问器
                    jQueryObject.elements.forEach((el, i) => {
                        jQueryObject[i] = el;
                    });
                    
                    return jQueryObject;
                };
                
                // 添加基本工具方法
                jQuery.extend = jQuery.fn = {};
            }
        },
        
        // Bootstrap
        'bootstrap': {
            cdn: [
                'bootstrap.bundle.min.js',
                'bootstrap.min.js',
                'bootstrap.js',
                'bootstrap-5'
            ],
            local: '/static/js/lib/bootstrap.bundle.min.js',
            check: function() { return typeof bootstrap !== 'undefined'; }
        },
        
        // Bootstrap CSS
        'bootstrap-css': {
            cdn: [
                'bootstrap.min.css',
                'bootstrap.css',
                'bootstrap-5'
            ],
            local: '/static/css/bootstrap.min.css',
            fallback: function() {
                console.log('[九猫修复] 加载Bootstrap CSS内联备用');
                const style = document.createElement('style');
                style.textContent = `
                    /* 基本Bootstrap样式 - 最小版本 */
                    .container { width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto; }
                    @media (min-width: 576px) { .container { max-width: 540px; } }
                    @media (min-width: 768px) { .container { max-width: 720px; } }
                    @media (min-width: 992px) { .container { max-width: 960px; } }
                    @media (min-width: 1200px) { .container { max-width: 1140px; } }
                    .row { display: flex; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                    .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 { position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                    .col { flex-basis: 0; flex-grow: 1; max-width: 100%; }
                    .col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
                    .col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
                    .col-3 { flex: 0 0 25%; max-width: 25%; }
                    .col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
                    .col-6 { flex: 0 0 50%; max-width: 50%; }
                    .col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
                    .col-12 { flex: 0 0 100%; max-width: 100%; }
                    
                    /* 按钮 */
                    .btn { display: inline-block; font-weight: 400; text-align: center; vertical-align: middle; user-select: none; border: 1px solid transparent; padding: .375rem .75rem; font-size: 1rem; line-height: 1.5; border-radius: .25rem; transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out; }
                    .btn-primary { color: #fff; background-color: #007bff; border-color: #007bff; }
                    .btn-secondary { color: #fff; background-color: #6c757d; border-color: #6c757d; }
                    .btn-success { color: #fff; background-color: #28a745; border-color: #28a745; }
                    .btn-danger { color: #fff; background-color: #dc3545; border-color: #dc3545; }
                    
                    /* 导航栏 */
                    .navbar { display: flex; align-items: center; padding: 8px 16px; background-color: #343a40; color: white; }
                    .navbar-brand { font-size: 1.25rem; color: white; text-decoration: none; }
                    .navbar-nav { display: flex; list-style: none; margin: 0; padding: 0; }
                    .nav-item { margin-right: 15px; }
                    .nav-link { color: rgba(255,255,255,.5); text-decoration: none; }
                    .nav-link:hover { color: rgba(255,255,255,.75); }
                `;
                document.head.appendChild(style);
            }
        },
        
        // Font Awesome
        'fontawesome': {
            cdn: [
                'fontawesome',
                'font-awesome',
                'all.min.css',
                'all.css'
            ],
            local: '/static/css/fontawesome.min.css'
        }
    };
    
    // 处理资源加载错误
    function handleResourceError(event) {
        const target = event.target || event.srcElement;
        
        // 只处理脚本和样式表加载错误
        if (!target || (target.tagName !== 'SCRIPT' && target.tagName !== 'LINK')) {
            return;
        }
        
        const url = target.src || target.href;
        if (!url) return;
        
        console.error(`[九猫修复] 资源加载错误: ${url}`);
        
        // 查找匹配的资源类型
        let resourceType = null;
        for (const [type, resource] of Object.entries(resourceMap)) {
            if (resource.cdn.some(pattern => url.includes(pattern))) {
                resourceType = type;
                break;
            }
        }
        
        // 如果找到匹配的资源类型，尝试加载本地资源
        if (resourceType) {
            const resource = resourceMap[resourceType];
            
            // 检查资源是否已加载
            if (resource.check && resource.check()) {
                console.log(`[九猫修复] 资源已通过其他方式加载: ${resourceType}`);
                return;
            }
            
            console.log(`[九猫修复] 尝试加载本地资源: ${resource.local}`);
            
            // 创建新元素
            if (target.tagName === 'SCRIPT') {
                const script = document.createElement('script');
                script.src = resource.local;
                script.async = false;
                
                script.onerror = function() {
                    console.error(`[九猫修复] 本地资源加载失败: ${resource.local}`);
                    
                    // 如果有备用方案，使用它
                    if (resource.fallback) {
                        resource.fallback();
                    }
                };
                
                document.head.appendChild(script);
            } else if (target.tagName === 'LINK' && target.rel === 'stylesheet') {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = resource.local;
                
                link.onerror = function() {
                    console.error(`[九猫修复] 本地资源加载失败: ${resource.local}`);
                    
                    // 如果有备用方案，使用它
                    if (resource.fallback) {
                        resource.fallback();
                    }
                };
                
                document.head.appendChild(link);
            }
        }
    }
    
    // 监听资源加载错误
    window.addEventListener('error', function(event) {
        handleResourceError(event);
    }, true);
    
    // 检查关键资源
    function checkCriticalResources() {
        console.log('[九猫修复] 检查关键资源');
        
        // 检查jQuery
        if (!resourceMap.jquery.check()) {
            console.warn('[九猫修复] jQuery未加载，尝试加载本地版本');
            const script = document.createElement('script');
            script.src = resourceMap.jquery.local;
            script.onerror = resourceMap.jquery.fallback;
            document.head.appendChild(script);
        }
        
        // 检查Bootstrap
        if (!resourceMap.bootstrap.check()) {
            console.warn('[九猫修复] Bootstrap未加载，尝试加载本地版本');
            const script = document.createElement('script');
            script.src = resourceMap.bootstrap.local;
            document.head.appendChild(script);
        }
    }
    
    // 页面加载完成后检查关键资源
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkCriticalResources);
    } else {
        checkCriticalResources();
    }
    
    // 标记为已加载
    window.__nineCatsFixes.loaded['resource-error-handler'] = true;
    
    console.log('[九猫修复] 资源错误处理器初始化完成');
})();
