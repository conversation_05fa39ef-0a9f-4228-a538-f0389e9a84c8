/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 浏览器控制台日志捕获工具
 *
 * 这个工具可以捕获Chrome F12开发者工具中的console.log、console.error等日志
 * 并将它们显示在应用程序的UI中，方便调试和问题排查
 */

// 全局命名空间
window.browserConsoleCapture = window.browserConsoleCapture || {};

// 配置选项
window.browserConsoleCapture.config = {
    maxLogEntries: 1000,        // 最大日志条目数
    captureConsoleLog: true,    // 是否捕获console.log
    captureConsoleError: true,  // 是否捕获console.error
    captureConsoleWarn: true,   // 是否捕获console.warn
    captureConsoleInfo: true,   // 是否捕获console.info
    captureNetworkRequests: true, // 是否捕获网络请求
    captureJSErrors: true,      // 是否捕获JavaScript错误
    autoScroll: true,           // 是否自动滚动到最新日志
    timestampFormat: 'HH:mm:ss.SSS', // 时间戳格式
    enabled: true               // 是否启用捕获
};

// 存储捕获的日志
window.browserConsoleCapture.logs = [];

// 存储原始控制台方法
window.browserConsoleCapture.originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
    debug: console.debug
};

// 初始化函数
window.browserConsoleCapture.init = function() {
    console.log('初始化浏览器控制台日志捕获工具...');

    // 创建UI元素
    this.createUI();

    // 拦截控制台方法
    this.interceptConsoleMethods();

    // 拦截JavaScript错误
    this.interceptJSErrors();

    // 拦截网络请求
    if (this.config.captureNetworkRequests) {
        this.interceptNetworkRequests();
    }

    console.log('浏览器控制台日志捕获工具初始化完成');
};

// 创建UI元素
window.browserConsoleCapture.createUI = function() {
    // 检查是否已存在
    if (document.getElementById('browser-console-capture')) {
        return;
    }

    // 创建主容器
    const container = document.createElement('div');
    container.id = 'browser-console-capture';
    container.className = 'browser-console-capture';
    container.style.display = 'none'; // 默认隐藏

    // 创建标题栏
    const header = document.createElement('div');
    header.className = 'browser-console-header';
    header.innerHTML = `
        <div class="browser-console-title">浏览器控制台日志</div>
        <div class="browser-console-controls">
            <button id="browser-console-clear" title="清空日志">清空</button>
            <button id="browser-console-copy" title="复制日志">复制</button>
            <button id="browser-console-save" title="保存日志">保存</button>
            <button id="browser-console-close" title="关闭">关闭</button>
        </div>
    `;

    // 创建过滤器
    const filter = document.createElement('div');
    filter.className = 'browser-console-filter';
    filter.innerHTML = `
        <div class="filter-group">
            <label><input type="checkbox" id="filter-log" checked> Log</label>
            <label><input type="checkbox" id="filter-error" checked> Error</label>
            <label><input type="checkbox" id="filter-warn" checked> Warn</label>
            <label><input type="checkbox" id="filter-info" checked> Info</label>
            <label><input type="checkbox" id="filter-network" checked> Network</label>
        </div>
        <div class="search-group">
            <input type="text" id="console-search" placeholder="搜索日志...">
            <button id="console-search-clear" title="清除搜索">×</button>
        </div>
    `;

    // 创建日志容器
    const logContainer = document.createElement('div');
    logContainer.className = 'browser-console-logs';
    logContainer.id = 'browser-console-logs';

    // 组装UI
    container.appendChild(header);
    container.appendChild(filter);
    container.appendChild(logContainer);

    // 添加到文档
    document.body.appendChild(container);

    // 添加样式
    this.addStyles();

    // 绑定事件
    this.bindEvents();
};

// 添加样式
window.browserConsoleCapture.addStyles = function() {
    const style = document.createElement('style');
    style.textContent = `
        .browser-console-capture {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40%;
            background-color: #1e1e1e;
            color: #f8f8f8;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            border-top: 1px solid #444;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.3);
        }

        .browser-console-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 10px;
            background-color: #2d2d2d;
            border-bottom: 1px solid #444;
        }

        .browser-console-title {
            font-weight: bold;
        }

        .browser-console-controls button {
            background-color: #3c3c3c;
            border: none;
            color: #f8f8f8;
            padding: 3px 8px;
            margin-left: 5px;
            border-radius: 3px;
            cursor: pointer;
        }

        .browser-console-controls button:hover {
            background-color: #505050;
        }

        .browser-console-filter {
            display: flex;
            justify-content: space-between;
            padding: 5px 10px;
            background-color: #2d2d2d;
            border-bottom: 1px solid #444;
        }

        .filter-group label {
            margin-right: 10px;
            cursor: pointer;
        }

        .search-group {
            display: flex;
            align-items: center;
        }

        .search-group input {
            background-color: #3c3c3c;
            border: 1px solid #555;
            color: #f8f8f8;
            padding: 3px 8px;
            border-radius: 3px;
            width: 200px;
        }

        .search-group button {
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            font-size: 16px;
            margin-left: -25px;
        }

        .browser-console-logs {
            flex: 1;
            overflow-y: auto;
            padding: 5px 0;
        }

        .log-entry {
            padding: 2px 10px;
            border-bottom: 1px solid #333;
            white-space: pre-wrap;
            word-break: break-word;
        }

        .log-entry:hover {
            background-color: #2a2a2a;
        }

        .log-entry .timestamp {
            color: #888;
            margin-right: 8px;
        }

        .log-entry .type {
            display: inline-block;
            width: 50px;
            margin-right: 8px;
            text-align: center;
            border-radius: 3px;
            padding: 0 3px;
        }

        .log-entry .content {
            color: #f8f8f8;
        }

        .log-type-log .type {
            background-color: #2a2a2a;
            color: #f8f8f8;
        }

        .log-type-error .type {
            background-color: #5c2b2b;
            color: #ff5f5f;
        }

        .log-type-warn .type {
            background-color: #5c4b2b;
            color: #ffcf5f;
        }

        .log-type-info .type {
            background-color: #2b3c5c;
            color: #5f9fff;
        }

        .log-type-network .type {
            background-color: #2b5c3c;
            color: #5fff9f;
        }

        .toggle-console-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #3c3c3c;
            color: #f8f8f8;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            z-index: 9998;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toggle-console-button:hover {
            background-color: #505050;
        }
    `;

    document.head.appendChild(style);
};

// 绑定事件
window.browserConsoleCapture.bindEvents = function() {
    // 创建切换按钮
    const toggleButton = document.createElement('button');
    toggleButton.className = 'toggle-console-button';
    toggleButton.innerHTML = '<span>F12</span>';
    toggleButton.title = '显示/隐藏浏览器控制台日志';
    document.body.appendChild(toggleButton);

    // 切换控制台显示
    toggleButton.addEventListener('click', () => {
        const consoleEl = document.getElementById('browser-console-capture');
        if (consoleEl.style.display === 'none') {
            consoleEl.style.display = 'flex';
            toggleButton.innerHTML = '<span>×</span>';
        } else {
            consoleEl.style.display = 'none';
            toggleButton.innerHTML = '<span>F12</span>';
        }
    });

    // 关闭按钮
    document.getElementById('browser-console-close').addEventListener('click', () => {
        document.getElementById('browser-console-capture').style.display = 'none';
        document.querySelector('.toggle-console-button').innerHTML = '<span>F12</span>';
    });

    // 清空按钮
    document.getElementById('browser-console-clear').addEventListener('click', () => {
        this.clearLogs();
    });

    // 复制按钮
    document.getElementById('browser-console-copy').addEventListener('click', () => {
        this.copyLogs();
    });

    // 保存按钮
    document.getElementById('browser-console-save').addEventListener('click', () => {
        this.saveLogs();
    });

    // 过滤器变化
    document.getElementById('filter-log').addEventListener('change', () => this.applyFilters());
    document.getElementById('filter-error').addEventListener('change', () => this.applyFilters());
    document.getElementById('filter-warn').addEventListener('change', () => this.applyFilters());
    document.getElementById('filter-info').addEventListener('change', () => this.applyFilters());
    document.getElementById('filter-network').addEventListener('change', () => this.applyFilters());

    // 搜索框
    document.getElementById('console-search').addEventListener('input', () => this.applyFilters());
    document.getElementById('console-search-clear').addEventListener('click', () => {
        document.getElementById('console-search').value = '';
        this.applyFilters();
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
        // Ctrl+Shift+F12 切换控制台
        if (e.ctrlKey && e.shiftKey && e.keyCode === 123) {
            const consoleEl = document.getElementById('browser-console-capture');
            consoleEl.style.display = consoleEl.style.display === 'none' ? 'flex' : 'none';
            document.querySelector('.toggle-console-button').innerHTML =
                consoleEl.style.display === 'none' ? '<span>F12</span>' : '<span>×</span>';
            e.preventDefault();
        }
    });
};

// 拦截控制台方法
window.browserConsoleCapture.interceptConsoleMethods = function() {
    const self = this;

    // 拦截 console.log
    if (this.config.captureConsoleLog) {
        console.log = function() {
            try {
                // 调用原始方法
                self.originalConsole.log.apply(console, arguments);

                // 捕获日志
                if (self.config.enabled) {
                    self.captureLog('log', Array.from(arguments));
                }
            } catch (e) {
                // 恢复原始方法以避免无限循环
                console.log = self.originalConsole.log;
                console.log("控制台日志捕获工具出错:", e);
            }
        };
    }

    // 拦截 console.error
    if (this.config.captureConsoleError) {
        console.error = function() {
            try {
                // 调用原始方法
                self.originalConsole.error.apply(console, arguments);

                // 捕获日志
                if (self.config.enabled) {
                    self.captureLog('error', Array.from(arguments));
                }
            } catch (e) {
                // 恢复原始方法以避免无限循环
                console.error = self.originalConsole.error;
                console.error("控制台日志捕获工具出错:", e);
            }
        };
    }

    // 拦截 console.warn
    if (this.config.captureConsoleWarn) {
        console.warn = function() {
            try {
                // 调用原始方法
                self.originalConsole.warn.apply(console, arguments);

                // 捕获日志
                if (self.config.enabled) {
                    self.captureLog('warn', Array.from(arguments));
                }
            } catch (e) {
                // 恢复原始方法以避免无限循环
                console.warn = self.originalConsole.warn;
                console.warn("控制台日志捕获工具出错:", e);
            }
        };
    }

    // 拦截 console.info
    if (this.config.captureConsoleInfo) {
        console.info = function() {
            try {
                // 调用原始方法
                self.originalConsole.info.apply(console, arguments);

                // 捕获日志
                if (self.config.enabled) {
                    self.captureLog('info', Array.from(arguments));
                }
            } catch (e) {
                // 恢复原始方法以避免无限循环
                console.info = self.originalConsole.info;
                console.info("控制台日志捕获工具出错:", e);
            }
        };
    }
};

// 拦截JavaScript错误
window.browserConsoleCapture.interceptJSErrors = function() {
    const self = this;

    if (this.config.captureJSErrors) {
        window.addEventListener('error', function(event) {
            if (self.config.enabled) {
                const errorInfo = {
                    message: event.message,
                    source: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    error: event.error
                };

                self.captureLog('error', [`JavaScript错误: ${event.message}`, errorInfo]);
            }
        });

        window.addEventListener('unhandledrejection', function(event) {
            if (self.config.enabled) {
                self.captureLog('error', [`未处理的Promise拒绝: ${event.reason}`, event.reason]);
            }
        });
    }
};

// 拦截网络请求
window.browserConsoleCapture.interceptNetworkRequests = function() {
    const self = this;

    try {
        // 使用Fetch API拦截
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function() {
                try {
                    let url = arguments[0];
                    const options = arguments[1] || {};
                    const method = options.method || 'GET';

                    // 处理Request对象
                    if (url instanceof Request) {
                        url = url.url;
                    }

                    // 简化URL显示（移除查询参数）
                    const displayUrl = url.toString().split('?')[0];

                    const startTime = Date.now();
                    self.captureLog('network', [`${method} ${displayUrl} - 开始请求`]);

                    return originalFetch.apply(this, arguments)
                        .then(response => {
                            const duration = Date.now() - startTime;
                            const status = response.status;
                            const statusText = response.statusText;

                            // 根据状态码确定日志级别
                            const logType = status >= 400 ? 'error' : 'network';

                            self.captureLog(logType, [
                                `${method} ${displayUrl} - ${status} ${statusText} (${duration}ms)`
                            ]);

                            return response;
                        })
                        .catch(error => {
                            const duration = Date.now() - startTime;

                            self.captureLog('error', [
                                `${method} ${displayUrl} - 请求失败 (${duration}ms)`,
                                error
                            ]);

                            throw error;
                        });
                } catch (e) {
                    // 恢复原始方法以避免无限循环
                    window.fetch = originalFetch;
                    console.error("拦截fetch请求时出错:", e);
                    return originalFetch.apply(this, arguments);
                }
            };
        }

        // 使用XMLHttpRequest拦截
        if (window.XMLHttpRequest) {
            const originalXHROpen = XMLHttpRequest.prototype.open;
            const originalXHRSend = XMLHttpRequest.prototype.send;

            XMLHttpRequest.prototype.open = function() {
                try {
                    this._requestMethod = arguments[0];
                    this._requestUrl = arguments[1];
                    this._requestStartTime = Date.now();
                    return originalXHROpen.apply(this, arguments);
                } catch (e) {
                    // 恢复原始方法以避免无限循环
                    XMLHttpRequest.prototype.open = originalXHROpen;
                    console.error("拦截XHR open时出错:", e);
                    return originalXHROpen.apply(this, arguments);
                }
            };

            XMLHttpRequest.prototype.send = function() {
                try {
                    const xhr = this;
                    const method = xhr._requestMethod || 'GET';
                    const url = xhr._requestUrl || 'unknown';

                    // 简化URL显示（移除查询参数）
                    const displayUrl = url.toString().split('?')[0];

                    xhr._requestStartTime = xhr._requestStartTime || Date.now();

                    self.captureLog('network', [`${method} ${displayUrl} - 开始请求 (XHR)`]);

                    xhr.addEventListener('load', function() {
                        try {
                            const duration = Date.now() - xhr._requestStartTime;
                            const status = xhr.status;
                            const statusText = xhr.statusText;

                            // 根据状态码确定日志级别
                            const logType = status >= 400 ? 'error' : 'network';

                            self.captureLog(logType, [
                                `${method} ${displayUrl} - ${status} ${statusText} (${duration}ms) (XHR)`
                            ]);
                        } catch (e) {
                            console.error("处理XHR load事件时出错:", e);
                        }
                    });

                    xhr.addEventListener('error', function() {
                        try {
                            const duration = Date.now() - xhr._requestStartTime;

                            self.captureLog('error', [
                                `${method} ${displayUrl} - 请求失败 (${duration}ms) (XHR)`
                            ]);
                        } catch (e) {
                            console.error("处理XHR error事件时出错:", e);
                        }
                    });

                    return originalXHRSend.apply(this, arguments);
                } catch (e) {
                    // 恢复原始方法以避免无限循环
                    XMLHttpRequest.prototype.send = originalXHRSend;
                    console.error("拦截XHR send时出错:", e);
                    return originalXHRSend.apply(this, arguments);
                }
            };
        }
    } catch (e) {
        console.error("初始化网络请求拦截时出错:", e);
    }
};

// 捕获日志
window.browserConsoleCapture.captureLog = function(type, args) {
    // 创建日志条目
    const logEntry = {
        timestamp: new Date(),
        type: type,
        args: args,
        formattedArgs: this.formatArgs(args)
    };

    // 添加到日志数组
    this.logs.push(logEntry);

    // 限制日志数量
    if (this.logs.length > this.config.maxLogEntries) {
        this.logs.shift();
    }

    // 更新UI
    this.updateLogDisplay(logEntry);
};

// 格式化参数
window.browserConsoleCapture.formatArgs = function(args) {
    return args.map(arg => {
        if (arg === null) return 'null';
        if (arg === undefined) return 'undefined';

        if (typeof arg === 'object') {
            try {
                // 处理错误对象
                if (arg instanceof Error) {
                    return `${arg.name}: ${arg.message}\n${arg.stack || ''}`;
                }

                // 处理DOM元素
                if (arg instanceof HTMLElement) {
                    return `<${arg.tagName.toLowerCase()}${arg.id ? ' id="' + arg.id + '"' : ''}${arg.className ? ' class="' + arg.className + '"' : ''}>`;
                }

                // 处理数组
                if (Array.isArray(arg)) {
                    return `Array(${arg.length}): ${JSON.stringify(arg, null, 2)}`;
                }

                // 处理其他对象
                return JSON.stringify(arg, (key, value) => {
                    // 处理循环引用
                    if (typeof value === 'object' && value !== null) {
                        if (this.seen && this.seen.has(value)) {
                            return '[Circular Reference]';
                        }
                        this.seen = this.seen || new WeakSet();
                        this.seen.add(value);
                    }
                    return value;
                }, 2);
            } catch (e) {
                // 如果JSON序列化失败，尝试使用toString()
                try {
                    return arg.toString();
                } catch (e2) {
                    return '[Object]';
                }
            } finally {
                // 清除seen集合
                if (this.seen) {
                    this.seen = null;
                }
            }
        }

        // 处理函数
        if (typeof arg === 'function') {
            return `function ${arg.name || '(anonymous)'}() {...}`;
        }

        return String(arg);
    }).join(' ');
};

// 更新日志显示
window.browserConsoleCapture.updateLogDisplay = function(logEntry) {
    const logsContainer = document.getElementById('browser-console-logs');
    if (!logsContainer) return;

    // 创建日志元素
    const logElement = document.createElement('div');
    logElement.className = `log-entry log-type-${logEntry.type}`;
    logElement.dataset.type = logEntry.type;

    // 格式化时间戳
    const timestamp = this.formatTimestamp(logEntry.timestamp);

    // 设置内容
    logElement.innerHTML = `
        <span class="timestamp">${timestamp}</span>
        <span class="type">${logEntry.type.toUpperCase()}</span>
        <span class="content">${this.escapeHtml(logEntry.formattedArgs)}</span>
    `;

    // 添加到容器
    logsContainer.appendChild(logElement);

    // 自动滚动到底部
    if (this.config.autoScroll) {
        logsContainer.scrollTop = logsContainer.scrollHeight;
    }

    // 应用过滤器
    this.applyFilters();
};

// 格式化时间戳
window.browserConsoleCapture.formatTimestamp = function(date) {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

    return `${hours}:${minutes}:${seconds}.${milliseconds}`;
};

// 转义HTML
window.browserConsoleCapture.escapeHtml = function(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
};

// 应用过滤器
window.browserConsoleCapture.applyFilters = function() {
    const showLog = document.getElementById('filter-log').checked;
    const showError = document.getElementById('filter-error').checked;
    const showWarn = document.getElementById('filter-warn').checked;
    const showInfo = document.getElementById('filter-info').checked;
    const showNetwork = document.getElementById('filter-network').checked;

    const searchText = document.getElementById('console-search').value.toLowerCase();

    const logEntries = document.querySelectorAll('.log-entry');

    logEntries.forEach(entry => {
        const type = entry.dataset.type;
        const content = entry.querySelector('.content').textContent.toLowerCase();

        let visible = false;

        // 类型过滤
        if (type === 'log' && showLog) visible = true;
        if (type === 'error' && showError) visible = true;
        if (type === 'warn' && showWarn) visible = true;
        if (type === 'info' && showInfo) visible = true;
        if (type === 'network' && showNetwork) visible = true;

        // 搜索过滤
        if (searchText && !content.includes(searchText)) {
            visible = false;
        }

        entry.style.display = visible ? 'block' : 'none';
    });
};

// 清空日志
window.browserConsoleCapture.clearLogs = function() {
    this.logs = [];
    const logsContainer = document.getElementById('browser-console-logs');
    if (logsContainer) {
        logsContainer.innerHTML = '';
    }
};

// 复制日志
window.browserConsoleCapture.copyLogs = function() {
    const visibleLogs = Array.from(document.querySelectorAll('.log-entry'))
        .filter(entry => entry.style.display !== 'none')
        .map(entry => {
            const timestamp = entry.querySelector('.timestamp').textContent;
            const type = entry.querySelector('.type').textContent;
            const content = entry.querySelector('.content').textContent;
            return `[${timestamp}] [${type}] ${content}`;
        })
        .join('\n');

    navigator.clipboard.writeText(visibleLogs)
        .then(() => {
            alert('日志已复制到剪贴板');
        })
        .catch(err => {
            console.error('复制失败:', err);
            alert('复制失败: ' + err.message);
        });
};

// 保存日志
window.browserConsoleCapture.saveLogs = function() {
    const visibleLogs = Array.from(document.querySelectorAll('.log-entry'))
        .filter(entry => entry.style.display !== 'none')
        .map(entry => {
            const timestamp = entry.querySelector('.timestamp').textContent;
            const type = entry.querySelector('.type').textContent;
            const content = entry.querySelector('.content').textContent;
            return `[${timestamp}] [${type}] ${content}`;
        })
        .join('\n');

    const blob = new Blob([visibleLogs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const now = new Date();
    const dateStr = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);

    const a = document.createElement('a');
    a.href = url;
    a.download = `browser-console-logs-${dateStr}.txt`;
    a.click();

    URL.revokeObjectURL(url);
};

// 启用/禁用捕获
window.browserConsoleCapture.setEnabled = function(enabled) {
    this.config.enabled = enabled;
};

// 设置配置
window.browserConsoleCapture.setConfig = function(config) {
    Object.assign(this.config, config);
};

// 获取日志
window.browserConsoleCapture.getLogs = function() {
    return this.logs;
};

// 在文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.browserConsoleCapture.init();
});

// 导出全局函数
window.toggleBrowserConsole = function() {
    const consoleEl = document.getElementById('browser-console-capture');
    if (consoleEl) {
        consoleEl.style.display = consoleEl.style.display === 'none' ? 'flex' : 'none';
        document.querySelector('.toggle-console-button').innerHTML =
            consoleEl.style.display === 'none' ? '<span>F12</span>' : '<span>×</span>';
    }
};

// 导出全局函数
window.clearBrowserConsole = function() {
    window.browserConsoleCapture.clearLogs();
};
