{% extends "base.html" %}

{% block title %}首页 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">九猫小说文本分析系统</h4>
            </div>
            <div class="card-body">
                <p class="lead">欢迎使用九猫小说文本分析系统，这是一款专门为作家和编辑设计的文本分析工具。</p>

                <h5 class="mt-4">主要功能：</h5>
                <ul class="list-group list-group-flush mb-4">
                    <li class="list-group-item">
                        <i class="fas fa-chart-line me-2 text-primary"></i>
                        文本高潮节奏分析
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-project-diagram me-2 text-primary"></i>
                        人物关系网络可视化
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-heart me-2 text-primary"></i>
                        情感曲线追踪
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-feather-alt me-2 text-primary"></i>
                        文体风格识别
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-sitemap me-2 text-primary"></i>
                        情节结构分析
                    </li>
                </ul>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong> 点击上方导航栏中的"小说列表"按钮，查看已分析的小说或上传新小说开始分析。
                </div>
            </div>
        </div>

        <div class="card shadow-sm mt-4">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">关于九猫系统</h5>
            </div>
            <div class="card-body">
                <p>九猫小说分析系统使用先进的AI技术，对小说文本进行全方位分析，帮助作家和编辑更好地理解和优化作品。</p>
                <p>系统采用DeepSeek R1 API进行深度文本分析，能够处理长达300万字的小说文本，提供专业、全面的分析结果。</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="/about" class="btn btn-outline-dark">
                        <i class="fas fa-info-circle me-1"></i> 了解更多
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">快速开始</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/dashboard" class="btn btn-success btn-lg mb-3">
                        <i class="fas fa-tachometer-alt me-1"></i> 进入数据中心
                    </a>

                    <a href="/upload" class="btn btn-primary btn-lg mb-3">
                        <i class="fas fa-upload me-1"></i> 开始新的分析
                    </a>
                </div>

                <h6 class="border-bottom pb-2 mb-3">最近的小说：</h6>
                <ul class="list-group">
                    {% if novels %}
                        {% for novel in novels[:3] %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="text-decoration-none">
                                        {{ novel.title }}
                                    </a>
                                    <small class="text-muted d-block">{{ novel.created_at.strftime('%Y-%m-%d') }}</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">
                                    <i class="fas fa-eye"></i>
                                </span>
                            </li>
                        {% endfor %}
                    {% else %}
                        <li class="list-group-item text-center text-muted">
                            <i class="fas fa-book me-1"></i> 暂无小说
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>

        <div class="card shadow-sm mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">系统状态</h5>
            </div>
            <div class="card-body">
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-code-branch me-2"></i> 系统版本</span>
                        <span class="badge bg-primary">1.0.5</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-brain me-2"></i> 分析引擎</span>
                        <span class="badge bg-success">已连接</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-database me-2"></i> 数据库</span>
                        <span class="badge bg-info">已连接</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-memory me-2"></i> 内存使用</span>
                        <span class="badge bg-warning text-dark" id="memory-usage">加载中...</span>
                    </li>
                </ul>
                <div class="d-grid mt-3">
                    <button class="btn btn-outline-info btn-sm" onclick="refreshSystemStatus()">
                        <i class="fas fa-sync-alt me-1"></i> 刷新状态
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">分析维度</h5>
            </div>
            <div class="card-body">
                <div class="row row-cols-1 row-cols-md-3 g-4">
                    <div class="col">
                        <div class="card h-100 border-primary">
                            <div class="card-body">
                                <h5 class="card-title text-primary">
                                    <i class="fas fa-language me-2"></i>语言风格
                                </h5>
                                <p class="card-text">分析作品的语言特点、修辞手法和表达方式，揭示作者的语言风格特征。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card h-100 border-success">
                            <div class="card-body">
                                <h5 class="card-title text-success">
                                    <i class="fas fa-users me-2"></i>人物塑造
                                </h5>
                                <p class="card-text">分析小说中的人物形象、性格特点和发展变化，评估人物塑造的立体感和真实感。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card h-100 border-info">
                            <div class="card-body">
                                <h5 class="card-title text-info">
                                    <i class="fas fa-chart-line me-2"></i>情节节奏
                                </h5>
                                <p class="card-text">分析小说情节的起伏变化、紧张度和节奏感，评估故事的吸引力和可读性。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function refreshSystemStatus() {
        // 模拟刷新系统状态
        const memoryUsage = document.getElementById('memory-usage');
        memoryUsage.innerHTML = '加载中...';

        // 模拟API调用
        setTimeout(function() {
            const usage = Math.floor(Math.random() * 30) + 60; // 60-90%
            memoryUsage.innerHTML = usage + '%';

            // 根据使用率设置不同的颜色
            if (usage < 70) {
                memoryUsage.className = 'badge bg-success';
            } else if (usage < 85) {
                memoryUsage.className = 'badge bg-warning text-dark';
            } else {
                memoryUsage.className = 'badge bg-danger';
            }
        }, 1000);
    }

    // 页面加载时刷新系统状态
    document.addEventListener('DOMContentLoaded', refreshSystemStatus);
</script>
{% endblock %}
