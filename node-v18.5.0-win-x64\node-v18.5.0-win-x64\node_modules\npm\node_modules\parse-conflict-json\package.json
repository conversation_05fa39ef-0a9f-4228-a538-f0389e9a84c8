{"name": "parse-conflict-json", "version": "2.0.2", "description": "Parse a JSON string that has git merge conflicts, resolving if possible", "author": "GitHub Inc.", "license": "ISC", "main": "lib", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "prepublishOnly": "git push origin --follow-tags", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.0", "tap": "^16.0.1"}, "dependencies": {"json-parse-even-better-errors": "^2.3.1", "just-diff": "^5.0.1", "just-diff-apply": "^5.2.0"}, "repository": {"type": "git", "url": "https://github.com/npm/parse-conflict-json.git"}, "files": ["bin/", "lib/"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.0"}}