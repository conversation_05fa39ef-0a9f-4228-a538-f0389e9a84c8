"""
Analysis result data model for the 九猫 (Nine Cats) novel analysis system.
"""
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship

from src.models.base import Base

# 创建一个函数来替代已弃用的datetime.utcnow
def utc_now():
    """返回当前的UTC时间，替代已弃用的datetime.utcnow"""
    return datetime.now(timezone.utc)

class AnalysisResult(Base):
    """Analysis result data model."""

    __tablename__ = "analysis_results"

    id = Column(Integer, primary_key=True)
    novel_id = Column(Integer, ForeignKey("novels.id"), nullable=False)
    dimension = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    analysis_metadata = Column(JSON, nullable=True)
    analysis_logs = Column(JSON, nullable=True)  # 存储分析过程日志
    reasoning_content = Column(Text, nullable=True)  # 专门存储推理过程内容
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Relationship with Novel
    novel = relationship("Novel", back_populates="analysis_results")

    # 新增：与AnalysisProcess的关系定义
    processes = relationship("AnalysisProcess", back_populates="result", cascade="all, delete-orphan")

    @property
    def safe_metadata(self):
        """
        安全地获取元数据，确保返回的是可序列化的字典。

        Returns:
            Dict: 可序列化的元数据字典
        """
        if not self.analysis_metadata:
            return {}

        try:
            # 尝试将metadata转换为字典
            if hasattr(self.analysis_metadata, 'items') and callable(self.analysis_metadata.items):
                # 如果有items方法，创建一个新的字典副本
                return {k: v for k, v in self.analysis_metadata.items()}
            elif isinstance(self.analysis_metadata, dict):
                # 如果是字典，直接使用
                return dict(self.analysis_metadata)
            else:
                # 如果不是字典且没有items方法，使用空字典并记录错误
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"safe_metadata属性: 元数据不是字典类型: {type(self.analysis_metadata)}")
                return {"error": f"元数据无法转换为字典: {type(self.analysis_metadata)}"}
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"safe_metadata属性: 元数据处理失败: {str(e)}")
            return {"error": f"元数据处理失败: {str(e)}"}

    def __init__(
        self,
        novel_id: int,
        dimension: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        logs: Optional[List[Dict[str, Any]]] = None,
        analysis_metadata: Optional[Dict[str, Any]] = None,
        reasoning_content: Optional[str] = None
    ):
        """
        Initialize an analysis result.

        Args:
            novel_id: ID of the analyzed novel.
            dimension: Analysis dimension.
            content: Analysis content.
            metadata: Additional metadata. This will be stored in the analysis_metadata attribute.
            logs: Analysis process logs. This will be stored in the analysis_logs attribute.
            analysis_metadata: 直接设置analysis_metadata属性，如果提供则优先使用，否则使用metadata
            reasoning_content: 直接设置reasoning_content属性
        """
        self.novel_id = novel_id
        self.dimension = dimension
        self.content = content

        # 如果提供了reasoning_content，直接设置
        if reasoning_content is not None:
            self.reasoning_content = reasoning_content

        # 如果提供了analysis_metadata，直接使用，否则使用metadata
        metadata_to_use = analysis_metadata if analysis_metadata is not None else metadata

        # 确保metadata是一个可序列化的字典，而不是SQLAlchemy的MetaData对象
        if metadata_to_use is not None:
            try:
                # 尝试将metadata转换为字典
                if hasattr(metadata_to_use, 'items') and callable(metadata_to_use.items):
                    # 如果有items方法，创建一个新的字典副本
                    self.analysis_metadata = {k: v for k, v in metadata_to_use.items()}
                elif isinstance(metadata_to_use, dict):
                    # 如果是字典，直接使用
                    self.analysis_metadata = dict(metadata_to_use)
                else:
                    # 如果不是字典且没有items方法，使用空字典并记录错误
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"元数据不是字典类型: {type(metadata_to_use)}")
                    self.analysis_metadata = {"error": f"元数据无法转换为字典: {type(metadata_to_use)}"}
            except Exception as e:
                # 如果转换失败，使用空字典
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"元数据处理失败: {str(e)}")
                self.analysis_metadata = {"error": f"元数据处理失败: {str(e)}"}
        else:
            self.analysis_metadata = {}

        # 设置分析日志
        if logs is not None:
            try:
                if isinstance(logs, list):
                    # 新增：确保日志包含必要字段并添加分类标签
                    enhanced_logs = []
                    for log in logs:
                        if isinstance(log, dict):
                            # 复制日志项，避免修改原始数据
                            enhanced_log = dict(log)

                            # 确保包含必要字段
                            if "timestamp" not in enhanced_log:
                                enhanced_log["timestamp"] = datetime.now(timezone.utc).isoformat()
                            if "level" not in enhanced_log:
                                enhanced_log["level"] = "info"
                            if "message" not in enhanced_log:
                                enhanced_log["message"] = str(enhanced_log)

                            # 添加分类标签
                            if "category" not in enhanced_log:
                                # 基于消息内容推断分类
                                msg = enhanced_log["message"].lower()
                                if "api" in msg or "令牌" in msg or "token" in msg:
                                    enhanced_log["category"] = "api"
                                elif "块" in msg or "chunk" in msg:
                                    enhanced_log["category"] = "chunk"
                                elif "进度" in msg or "progress" in msg or "%" in msg:
                                    enhanced_log["category"] = "progress"
                                elif "错误" in msg or "error" in msg or "失败" in msg or "exception" in msg:
                                    enhanced_log["category"] = "error"
                                elif "警告" in msg or "warning" in msg:
                                    enhanced_log["category"] = "warning"
                                elif "合并" in msg or "combine" in msg:
                                    enhanced_log["category"] = "combine"
                                elif "初始化" in msg or "init" in msg:
                                    enhanced_log["category"] = "init"
                                elif "完成" in msg or "结束" in msg or "finish" in msg:
                                    enhanced_log["category"] = "complete"
                                else:
                                    enhanced_log["category"] = "general"

                            # 添加重要性标记
                            if "important" not in enhanced_log:
                                important_keywords = ["错误", "error", "异常", "exception", "失败", "failed",
                                                     "完成", "finished", "警告", "warning", "成功", "success",
                                                     "关键", "critical", "重要", "important", "100%"]
                                enhanced_log["important"] = any(keyword in enhanced_log["message"] for keyword in important_keywords)

                            enhanced_logs.append(enhanced_log)
                        else:
                            # 如果不是字典，转换为标准格式
                            enhanced_logs.append({
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                                "level": "info",
                                "message": str(log),
                                "category": "general",
                                "important": False
                            })

                    self.analysis_logs = enhanced_logs
                else:
                    # 如果不是列表，尝试转换
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"日志不是列表类型: {type(logs)}")
                    self.analysis_logs = [{
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "level": "warning",
                        "message": str(logs),
                        "category": "system",
                        "important": True
                    }]
            except Exception as e:
                # 如果转换失败，使用空列表
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"日志处理失败: {str(e)}")
                self.analysis_logs = [{
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "level": "error",
                    "message": f"日志处理失败: {str(e)}",
                    "category": "system",
                    "important": True
                }]
        else:
            self.analysis_logs = []

    def get_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the analysis result.

        Returns:
            Dictionary with analysis result summary information.
        """
        # 确保元数据是字典
        metadata = {}
        if self.analysis_metadata:
            try:
                # 尝试将metadata转换为字典
                if hasattr(self.analysis_metadata, 'items') and callable(self.analysis_metadata.items):
                    # 如果有items方法，创建一个新的字典副本
                    metadata = {k: v for k, v in self.analysis_metadata.items()}
                elif isinstance(self.analysis_metadata, dict):
                    # 如果是字典，直接使用
                    metadata = dict(self.analysis_metadata)
                else:
                    # 如果不是字典且没有items方法，使用空字典并记录错误
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"get_summary: 元数据不是字典类型: {type(self.analysis_metadata)}")
                    metadata = {"error": f"元数据无法转换为字典: {type(self.analysis_metadata)}"}
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"get_summary: 元数据处理失败: {str(e)}")
                metadata = {"error": f"元数据处理失败: {str(e)}"}

        return {
            "id": self.id,
            "novel_id": self.novel_id,
            "dimension": self.dimension,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "metadata": metadata
        }

    def get_excerpt(self, length: int = 500) -> str:
        """
        Get an excerpt from the analysis content.

        Args:
            length: Maximum length of the excerpt.

        Returns:
            Analysis content excerpt.
        """
        if len(self.content) <= length:
            return self.content
        return self.content[:length] + "..."

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the analysis result to a dictionary.

        Returns:
            Dictionary representation of the analysis result.
        """
        # 确保内容是字符串
        content = self.content
        if not isinstance(content, str):
            try:
                content = str(content)
            except Exception:
                content = "无法显示内容"

        # 确保元数据是字典
        metadata = {}
        if self.analysis_metadata:
            try:
                # 尝试将metadata转换为字典
                if hasattr(self.analysis_metadata, 'items') and callable(self.analysis_metadata.items):
                    # 如果有items方法，创建一个新的字典副本
                    metadata = {k: v for k, v in self.analysis_metadata.items()}
                elif isinstance(self.analysis_metadata, dict):
                    # 如果是字典，直接使用
                    metadata = dict(self.analysis_metadata)
                else:
                    # 如果不是字典且没有items方法，使用空字典并记录错误
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"to_dict: 元数据不是字典类型: {type(self.analysis_metadata)}")
                    metadata = {"error": f"元数据无法转换为字典: {type(self.analysis_metadata)}"}
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"to_dict: 元数据处理失败: {str(e)}")
                metadata = {"error": f"元数据处理失败: {str(e)}"}

        # 确保分析日志是列表
        logs = []
        if self.analysis_logs:
            try:
                if isinstance(self.analysis_logs, list):
                    logs = list(self.analysis_logs)
                elif hasattr(self.analysis_logs, 'items') and callable(self.analysis_logs.items):
                    # 如果是字典，转换为列表
                    logs = [{"key": k, "value": v, "timestamp": datetime.now(timezone.utc).isoformat(),
                             "level": "info", "category": "system"}
                           for k, v in self.analysis_logs.items()]
                else:
                    # 如果不是列表也不是字典，尝试转换
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"to_dict: 日志不是列表类型: {type(self.analysis_logs)}")
                    logs = [{
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "level": "warning",
                        "message": str(self.analysis_logs),
                        "category": "system",
                        "important": True
                    }]
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"to_dict: 日志处理失败: {str(e)}")
                logs = [{
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "level": "error",
                    "message": f"日志处理失败: {str(e)}",
                    "category": "system",
                    "important": True
                }]

        return {
            "id": self.id,
            "novel_id": self.novel_id,
            "dimension": self.dimension,
            "content": content,
            "metadata": metadata,
            "logs": logs,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "has_detailed_process": len(self.processes) > 0 if hasattr(self, 'processes') else False
        }

    def add_log(self, message: str, level: str = "info", category: str = None, important: bool = None):
        """
        添加日志记录到分析结果中。

        Args:
            message: 日志消息
            level: 日志级别（info, warning, error等）
            category: 日志分类
            important: 是否重要
        """
        if not isinstance(self.analysis_logs, list):
            self.analysis_logs = []

        # 自动分类
        if category is None:
            msg = message.lower()
            if "api" in msg or "令牌" in msg or "token" in msg:
                category = "api"
            elif "块" in msg or "chunk" in msg:
                category = "chunk"
            elif "进度" in msg or "progress" in msg or "%" in msg:
                category = "progress"
            elif "错误" in msg or "error" in msg or "失败" in msg or "exception" in msg:
                category = "error"
                level = "error" if level == "info" else level
            elif "警告" in msg or "warning" in msg:
                category = "warning"
                level = "warning" if level == "info" else level
            elif "合并" in msg or "combine" in msg:
                category = "combine"
            elif "初始化" in msg or "init" in msg:
                category = "init"
            elif "完成" in msg or "结束" in msg or "finish" in msg:
                category = "complete"
            else:
                category = "general"

        # 自动判断重要性
        if important is None:
            important_keywords = ["错误", "error", "异常", "exception", "失败", "failed",
                                 "完成", "finished", "警告", "warning", "成功", "success",
                                 "关键", "critical", "重要", "important", "100%"]
            important = any(keyword in message for keyword in important_keywords)

        # 创建日志条目
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "level": level,
            "message": message,
            "category": category,
            "important": important
        }

        self.analysis_logs.append(log_entry)
        return log_entry
