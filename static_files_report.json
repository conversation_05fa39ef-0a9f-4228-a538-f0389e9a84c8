{"timestamp": "2025-05-05 12:56:14", "static_files": {"css": {"bootstrap.min.css": true, "style.css": true}, "js": {"jquery.min.js": true, "bootstrap.bundle.min.js": true, "chart.min.js": true, "main.js": true}}, "lib_files": {"css": {"bootstrap.min.css": true}, "js": {"jquery.min.js": true, "jquery-3.6.0.min.js": true, "bootstrap.bundle.min.js": true, "chart.min.js": true}}, "direct_static": {"exists": true, "files": ["css\\bootstrap.min.css", "css\\main.css", "css\\style.css", "js\\bootstrap.bundle.min.js", "js\\chart-blocker.js", "js\\chart-fix.js", "js\\chart.min.js", "js\\create-element-fix.js", "js\\disable-charts.js", "js\\error-handler.js", "js\\error-page-fix.js", "js\\fix-unexpected-identifier.js", "js\\jquery.min.js", "js\\main.js", "js\\replace-child-fix.js", "js\\system-monitor-fix.js", "js\\lib\\chart.min.js"]}}