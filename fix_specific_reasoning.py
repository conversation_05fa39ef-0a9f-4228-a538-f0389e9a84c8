"""
修复特定章节的章纲分析推理过程
"""

import sqlite3
import os
import sys

# 数据库路径
db_path = os.path.join(os.path.dirname(__file__), 'novel_analysis.db')

def fix_specific_reasoning(chapter_id):
    """修复特定章节的章纲分析推理过程"""
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 查询章节信息
        cursor.execute("""
            SELECT c.id, c.title, c.chapter_number, n.id as novel_id, n.title as novel_title
            FROM chapters c
            JOIN novels n ON c.novel_id = n.id
            WHERE c.id = ?
        """, (chapter_id,))
        
        chapter = cursor.fetchone()
        if not chapter:
            print(f"找不到章节: {chapter_id}")
            return
        
        chapter_id, chapter_title, chapter_number, novel_id, novel_title = chapter
        chapter_title = chapter_title or f'第{chapter_number}章'
        print(f"处理章节: {chapter_title}, 小说: {novel_title}")
        
        # 查询章纲分析结果
        cursor.execute("""
            SELECT id, content, reasoning_content
            FROM chapter_analysis_results
            WHERE chapter_id = ? AND dimension = 'chapter_outline'
        """, (chapter_id,))
        
        result = cursor.fetchone()
        if not result:
            print(f"找不到章纲分析结果")
            return
        
        result_id, content, reasoning_content = result
        print(f"找到章纲分析结果: id={result_id}")
        
        # 检查推理过程
        if not reasoning_content:
            print(f"推理过程为空，需要修复")
            
            # 查询分析过程记录
            cursor.execute("""
                SELECT output_text
                FROM chapter_analysis_processes
                WHERE result_id = ? AND processing_stage = 'reasoning'
                ORDER BY id DESC
                LIMIT 1
            """, (result_id,))
            
            process = cursor.fetchone()
            
            if process and process[0]:
                # 从分析过程记录中恢复推理过程
                reasoning_content = process[0]
                print(f"从分析过程记录中恢复推理过程，长度: {len(reasoning_content)}")
                
                # 更新数据库
                cursor.execute("""
                    UPDATE chapter_analysis_results
                    SET reasoning_content = ?
                    WHERE id = ?
                """, (reasoning_content, result_id))
                conn.commit()
                print(f"成功更新推理过程")
            else:
                # 如果没有分析过程记录，生成新的推理过程
                print(f"没有分析过程记录，生成新的推理过程")
                
                # 构建推理过程内容
                reasoning_content = f"""## 分析思路说明：
1. **内容重现与叙事梳理**：全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。内容不设字数限制，越详细越好。
2. **结构功能与定位分析**：全面分析章节的内部结构和功能定位，评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性
3. **关键事件与转折点**：详细分析本章节中的关键事件和转折点，识别并详述章节中的转折点、冲突点和高潮点
4. **人物表现与发展轨迹**：详细分析本章节中主要人物的表现、行动和发展，描述每个重要人物在本章中的言行和思想变化
5. **章节关联与叙事连贯性**：深入分析本章节与前后章节的关系和叙事连贯性，评估章节过渡的自然度和技巧多样性
6. **伏笔设置与悬念管理**：分析本章节中的伏笔设置和悬念管理，指出章节中埋下的伏笔和暗示，以及它们可能的未来发展方向
7. **主题情感与氛围营造**：评估章节的主题明确性和情感基调，分析本章节的核心主题和情感色彩
8. **创新特色与读者体验**：分析本章节的特色元素和创新点，识别章节中独特的写作技巧，评估如何吸引和维持读者兴趣

## 详细章纲分析：
{content}
"""
                
                # 更新数据库
                cursor.execute("""
                    UPDATE chapter_analysis_results
                    SET reasoning_content = ?
                    WHERE id = ?
                """, (reasoning_content, result_id))
                conn.commit()
                print(f"成功生成并保存新的推理过程")
                
                # 保存分析过程记录
                cursor.execute("""
                    INSERT INTO chapter_analysis_processes
                    (chapter_id, novel_id, result_id, dimension, processing_stage, output_text, is_successful)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (chapter_id, novel_id, result_id, "chapter_outline", "reasoning", reasoning_content, 1))
                conn.commit()
                print(f"成功保存分析过程记录")
        else:
            print(f"推理过程已存在，长度: {len(reasoning_content)}")
        
        print(f"完成修复章节 {chapter_title} 的章纲分析推理过程")
    except Exception as e:
        print(f"修复章节 {chapter_id} 的章纲分析推理过程时出错: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python fix_specific_reasoning.py <chapter_id>")
        sys.exit(1)
    
    chapter_id = int(sys.argv[1])
    fix_specific_reasoning(chapter_id)
