"""
九猫系统 - 增强版维度分析模块
用于生成更详细、结构化的推理过程

此模块是对原有analyze_dimension.py的增强版本，专注于生成高质量的推理内容
"""

import logging
import time
import json
from typing import Dict, Any, Optional, List

from src.db.connection import Session
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.api.deepseek_client import DeepSeekClient
import config

# 导入推理内容模板生成器
from reasoning_content_template import generate_reasoning_content_template, format_reasoning_content

logger = logging.getLogger(__name__)

def analyze_dimension_enhanced(
    novel: Novel, 
    dimension: str, 
    analyzer=None, 
    use_real_api=True,
    is_chapter=False,
    chapter_id=None
) -> Optional[Dict[str, Any]]:
    """
    增强版的维度分析函数，生成更详细的推理过程
    
    Args:
        novel: 要分析的小说
        dimension: 分析维度
        analyzer: 可选的分析器对象
        use_real_api: 是否使用真实API
        is_chapter: 是否为章节分析
        chapter_id: 章节ID（仅在is_chapter=True时使用）
        
    Returns:
        分析结果字典，包含content和reasoning_content字段
    """
    start_time = time.time()
    logger.info(f"开始增强版分析: {'章节' if is_chapter else '整本书'} ID:{novel.id} 维度:{dimension}")
    
    # 创建分析器（如果未提供）
    if analyzer is None:
        api_client = DeepSeekClient()
        analyzer = NovelAnalyzer(api_client=api_client)
    
    try:
        # 获取分析文本
        if is_chapter and chapter_id:
            session = Session()
            try:
                chapter = session.query(Chapter).get(chapter_id)
                if not chapter:
                    logger.error(f"找不到章节: {chapter_id}")
                    return None
                text = chapter.content
                title = novel.title
                chapter_title = chapter.title or f"第{chapter.chapter_number}章"
            finally:
                session.close()
        else:
            text = novel.content
            title = novel.title
            chapter_title = None
        
        # 生成推理内容模板
        reasoning_template = generate_reasoning_content_template(
            dimension=dimension,
            title=title,
            is_chapter=is_chapter,
            chapter_title=chapter_title
        )
        
        # 使用模板进行分析
        result = analyze_with_template(
            analyzer=analyzer,
            text=text,
            dimension=dimension,
            title=title,
            template=reasoning_template,
            is_chapter=is_chapter,
            chapter_title=chapter_title
        )
        
        # 处理分析结果
        if result:
            # 格式化推理内容
            if "reasoning_content" in result:
                result["reasoning_content"] = format_reasoning_content(result["reasoning_content"])
            
            # 添加元数据
            result["metadata"] = {
                "processing_time": time.time() - start_time,
                "timestamp": time.time(),
                "using_real_api": use_real_api,
                "is_chapter_analysis": is_chapter,
                "enhanced_reasoning": True
            }
            
            # 保存结果到数据库
            if is_chapter and chapter_id:
                save_chapter_analysis_result(
                    novel_id=novel.id,
                    chapter_id=chapter_id,
                    dimension=dimension,
                    result=result
                )
            else:
                save_book_analysis_result(
                    novel_id=novel.id,
                    dimension=dimension,
                    result=result
                )
            
            return result
        else:
            logger.error(f"分析失败: {'章节' if is_chapter else '整本书'} ID:{novel.id} 维度:{dimension}")
            return None
    except Exception as e:
        logger.error(f"增强版分析出错: {str(e)}", exc_info=True)
        return None

def analyze_with_template(
    analyzer, 
    text: str, 
    dimension: str, 
    title: str, 
    template: str,
    is_chapter: bool = False,
    chapter_title: Optional[str] = None
) -> Dict[str, Any]:
    """
    使用模板进行分析，确保生成结构化的推理内容
    
    Args:
        analyzer: 分析器对象
        text: 要分析的文本
        dimension: 分析维度
        title: 文本标题
        template: 推理内容模板
        is_chapter: 是否为章节分析
        chapter_title: 章节标题
        
    Returns:
        包含分析结果的字典
    """
    logger.info(f"使用模板分析: {'章节' if is_chapter else '整本书'} 标题:{title} 维度:{dimension}")
    
    # 构建分析提示词
    analysis_object = f"章节《{chapter_title}》" if is_chapter else f"小说《{title}》"
    prompt = f"""请分析以下{analysis_object}的{dimension}特点，并按照提供的模板格式详细说明你的分析思路和推理过程。

文本标题：《{title}》
{f"章节标题：{chapter_title}" if is_chapter else ""}

分析模板：
{template}

文本内容：
{text[:10000]}...

请按照模板格式提供详细的分析思路和推理过程。确保分析思路部分详细说明您的分析方法，而详细分析部分应用这些方法对文本进行具体分析。
"""
    
    # 调用分析器进行分析
    try:
        # 这里假设analyzer有一个analyze_with_prompt方法
        # 如果没有，需要根据实际情况调整
        result = analyzer.analyze_with_prompt(text, prompt, dimension)
        
        # 确保结果包含必要的字段
        if not result or "content" not in result:
            logger.error(f"分析结果格式不正确: {result}")
            return {
                "content": f"分析{dimension}时出错，请重试",
                "reasoning_content": f"分析{dimension}时出错，未能生成推理内容"
            }
        
        # 如果没有推理内容，使用内容作为推理内容
        if "reasoning_content" not in result:
            result["reasoning_content"] = result["content"]
        
        return result
    except Exception as e:
        logger.error(f"使用模板分析时出错: {str(e)}", exc_info=True)
        return {
            "content": f"分析{dimension}时出错: {str(e)}",
            "reasoning_content": f"分析{dimension}时出错: {str(e)}"
        }

def save_book_analysis_result(novel_id: int, dimension: str, result: Dict[str, Any]) -> bool:
    """
    保存整本书的分析结果到数据库
    
    Args:
        novel_id: 小说ID
        dimension: 分析维度
        result: 分析结果
        
    Returns:
        是否成功保存
    """
    session = Session()
    try:
        # 查找现有结果
        existing = session.query(AnalysisResult).filter_by(
            novel_id=novel_id,
            dimension=dimension
        ).first()
        
        if existing:
            # 更新现有结果
            existing.content = result["content"]
            existing.reasoning_content = result["reasoning_content"]
            existing.analysis_metadata = result.get("metadata", {})
            existing.updated_at = time.time()
        else:
            # 创建新结果
            new_result = AnalysisResult(
                novel_id=novel_id,
                dimension=dimension,
                content=result["content"],
                reasoning_content=result["reasoning_content"],
                analysis_metadata=result.get("metadata", {}),
                analysis_logs=[]
            )
            session.add(new_result)
        
        session.commit()
        return True
    except Exception as e:
        logger.error(f"保存整本书分析结果时出错: {str(e)}", exc_info=True)
        session.rollback()
        return False
    finally:
        session.close()

def save_chapter_analysis_result(
    novel_id: int, 
    chapter_id: int, 
    dimension: str, 
    result: Dict[str, Any]
) -> bool:
    """
    保存章节分析结果到数据库
    
    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
        dimension: 分析维度
        result: 分析结果
        
    Returns:
        是否成功保存
    """
    session = Session()
    try:
        # 查找现有结果
        existing = session.query(ChapterAnalysisResult).filter_by(
            novel_id=novel_id,
            chapter_id=chapter_id,
            dimension=dimension
        ).first()
        
        if existing:
            # 更新现有结果
            existing.content = result["content"]
            existing.reasoning_content = result["reasoning_content"]
            existing.analysis_metadata = result.get("metadata", {})
            existing.updated_at = time.time()
        else:
            # 创建新结果
            new_result = ChapterAnalysisResult(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension,
                content=result["content"],
                reasoning_content=result["reasoning_content"],
                analysis_metadata=result.get("metadata", {}),
                analysis_logs=[]
            )
            session.add(new_result)
        
        session.commit()
        return True
    except Exception as e:
        logger.error(f"保存章节分析结果时出错: {str(e)}", exc_info=True)
        session.rollback()
        return False
    finally:
        session.close()

# 分析器类（示例）
class NovelAnalyzer:
    def __init__(self, api_client):
        self.api_client = api_client
    
    def analyze_with_prompt(self, text, prompt, dimension):
        """使用自定义提示词进行分析"""
        # 这里是示例实现，实际应根据API客户端的实现调整
        try:
            # 调用API进行分析
            response = self.api_client.analyze_with_prompt(text, prompt)
            
            # 处理API响应
            if response and "content" in response:
                # 提取推理内容
                reasoning_content = response.get("reasoning_content", response.get("reasoning", response["content"]))
                
                return {
                    "content": response["content"],
                    "reasoning_content": reasoning_content,
                    "metadata": {
                        "api_response": "success",
                        "timestamp": time.time()
                    }
                }
            else:
                logger.error(f"API响应格式不正确: {response}")
                return None
        except Exception as e:
            logger.error(f"调用API时出错: {str(e)}", exc_info=True)
            return None
