/**
 * 九猫 - 维度详情页面修复脚本
 * 专门解决维度详情页面的图表加载和内存管理问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('维度详情页面修复脚本已加载');
    
    // 存储图表实例
    const chartInstances = {};
    
    // 安全地创建图表
    function safeCreateChart(canvas, config) {
        if (!canvas || !config) {
            console.error('创建图表失败：缺少canvas或配置');
            return null;
        }
        
        const canvasId = canvas.id || 'unknown';
        console.log(`尝试创建图表: ${canvasId}`);
        
        try {
            // 先检查是否已有图表实例，如果有则销毁
            if (chartInstances[canvasId]) {
                console.log(`销毁已存在的图表: ${canvasId}`);
                chartInstances[canvasId].destroy();
                delete chartInstances[canvasId];
            }
            
            // 检查canvas是否已被其他图表使用
            if (canvas._chart) {
                console.log(`Canvas已被其他图表使用，尝试销毁: ${canvasId}`);
                try {
                    canvas._chart.destroy();
                } catch (e) {
                    console.warn(`销毁canvas上的旧图表失败: ${e.message}`);
                }
                canvas._chart = null;
            }
            
            // 创建新图表
            const ctx = canvas.getContext('2d');
            const chart = new Chart(ctx, config);
            
            // 存储图表实例
            chartInstances[canvasId] = chart;
            canvas._chart = chart;
            
            console.log(`图表创建成功: ${canvasId}`);
            return chart;
        } catch (e) {
            console.error(`创建图表时出错: ${e.message}`);
            
            // 如果是Canvas已在使用的错误，尝试更激进的修复
            if (e.message && e.message.includes('Canvas is already in use')) {
                console.log('检测到Canvas已在使用错误，尝试更激进的修复');
                
                try {
                    // 创建新的canvas元素替换旧的
                    const oldCanvas = canvas;
                    const newCanvas = document.createElement('canvas');
                    
                    // 复制所有属性
                    newCanvas.id = oldCanvas.id || '';
                    newCanvas.className = oldCanvas.className || '';
                    newCanvas.style.cssText = oldCanvas.style.cssText || '';
                    newCanvas.width = oldCanvas.width || 400;
                    newCanvas.height = oldCanvas.height || 300;
                    
                    // 复制数据属性
                    Array.from(oldCanvas.attributes).forEach(attr => {
                        if (attr.name.startsWith('data-')) {
                            newCanvas.setAttribute(attr.name, attr.value);
                        }
                    });
                    
                    // 替换canvas
                    try {
                        oldCanvas.parentNode.replaceChild(newCanvas, oldCanvas);
                    } catch (replaceError) {
                        console.error(`替换Canvas元素失败: ${replaceError.message}`);
                        
                        // 尝试替代方法：先移除旧canvas，再添加新canvas
                        try {
                            const parent = oldCanvas.parentNode;
                            parent.removeChild(oldCanvas);
                            parent.appendChild(newCanvas);
                        } catch (alternativeError) {
                            console.error(`替代方法也失败: ${alternativeError.message}`);
                            return null;
                        }
                    }
                    
                    // 在新canvas上创建图表
                    const ctx = newCanvas.getContext('2d');
                    const chart = new Chart(ctx, config);
                    
                    // 存储图表实例
                    chartInstances[newCanvas.id] = chart;
                    newCanvas._chart = chart;
                    
                    console.log(`在新canvas上创建图表成功: ${newCanvas.id}`);
                    return chart;
                } catch (e2) {
                    console.error(`激进修复也失败: ${e2.message}`);
                }
            }
            
            return null;
        }
    }
    
    // 安全地销毁图表
    function safeDestroyChart(canvasOrId) {
        let canvasId;
        
        if (typeof canvasOrId === 'string') {
            canvasId = canvasOrId;
        } else if (canvasOrId && canvasOrId.id) {
            canvasId = canvasOrId.id;
        } else {
            console.warn('销毁图表失败：无效的canvas或ID');
            return false;
        }
        
        try {
            if (chartInstances[canvasId]) {
                console.log(`销毁图表: ${canvasId}`);
                chartInstances[canvasId].destroy();
                delete chartInstances[canvasId];
                return true;
            }
            
            // 如果没有找到图表实例，尝试通过canvas元素查找
            if (typeof canvasOrId !== 'string') {
                const canvas = canvasOrId;
                if (canvas._chart) {
                    console.log(`通过canvas元素销毁图表: ${canvasId}`);
                    canvas._chart.destroy();
                    canvas._chart = null;
                    return true;
                }
            }
            
            return false;
        } catch (e) {
            console.error(`销毁图表时出错: ${e.message}`);
            return false;
        }
    }
    
    // 修复所有图表
    function fixAllCharts() {
        console.log('开始修复所有图表');
        
        // 获取所有canvas元素
        const canvases = document.querySelectorAll('canvas');
        
        // 销毁所有现有图表
        canvases.forEach(canvas => {
            safeDestroyChart(canvas);
        });
        
        // 重新初始化图表
        if (typeof initCharts === 'function') {
            console.log('调用页面的initCharts函数');
            setTimeout(initCharts, 100);
        } else {
            console.log('页面没有定义initCharts函数，无法自动重新初始化图表');
        }
    }
    
    // 修复replaceChild错误
    function fixReplaceChildError() {
        // 保存原始的replaceChild方法
        const originalReplaceChild = Node.prototype.replaceChild;
        
        // 重写replaceChild方法
        Node.prototype.replaceChild = function(newChild, oldChild) {
            try {
                return originalReplaceChild.call(this, newChild, oldChild);
            } catch (e) {
                console.error(`replaceChild错误: ${e.message}`);
                
                // 尝试修复：如果失败，尝试先移除旧节点，再添加新节点
                try {
                    if (this.contains(oldChild)) {
                        this.removeChild(oldChild);
                    }
                    this.appendChild(newChild);
                    return newChild;
                } catch (e2) {
                    console.error(`替代方法也失败: ${e2.message}`);
                    throw e; // 重新抛出原始错误
                }
            }
        };
    }
    
    // 优化内存使用
    function optimizeMemoryUsage() {
        // 限制日志输出
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        // 日志计数器
        let logCounter = 0;
        const LOG_LIMIT = 1000; // 限制日志数量
        
        // 重写console.log
        console.log = function(...args) {
            if (logCounter < LOG_LIMIT) {
                originalConsoleLog.apply(console, args);
                logCounter++;
            } else if (logCounter === LOG_LIMIT) {
                originalConsoleLog.call(console, '日志输出已达到限制，后续日志将被抑制以节省内存');
                logCounter++;
            }
        };
        
        // 重写console.error
        console.error = function(...args) {
            // 错误总是输出
            originalConsoleError.apply(console, args);
        };
        
        // 重写console.warn
        console.warn = function(...args) {
            if (logCounter < LOG_LIMIT) {
                originalConsoleWarn.apply(console, args);
                logCounter++;
            }
        };
        
        // 定期清理未使用的图表实例
        setInterval(() => {
            for (const id in chartInstances) {
                const canvas = document.getElementById(id);
                if (!canvas || !document.body.contains(canvas)) {
                    console.log(`清理未使用的图表实例: ${id}`);
                    safeDestroyChart(id);
                }
            }
        }, 30000); // 每30秒清理一次
    }
    
    // 修复分析过程显示问题
    function fixAnalysisProcessDisplay() {
        // 查找所有分析过程链接
        const processLinks = document.querySelectorAll('a[href*="analysis_process"]');
        
        processLinks.forEach(link => {
            // 确保链接正确
            if (!link.href.includes('/analysis_process/')) {
                const href = link.getAttribute('href');
                const novelId = href.match(/novel_id=(\d+)/)?.[1];
                const dimension = href.match(/dimension=([^&]+)/)?.[1];
                
                if (novelId && dimension) {
                    link.href = `/novel/${novelId}/analysis_process/${dimension}`;
                }
            }
            
            // 添加点击事件处理
            link.addEventListener('click', function(e) {
                // 不阻止默认行为，但记录点击
                console.log('点击了分析过程链接:', this.href);
            });
        });
    }
    
    // 添加全局错误处理
    function addGlobalErrorHandling() {
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                // 处理Chart.js相关错误
                if (event.error.message.includes('Chart with ID') ||
                    event.error.message.includes('Canvas is already in use')) {
                    console.error(`捕获到Chart.js相关错误: ${event.error.message}`);
                    
                    // 尝试修复
                    setTimeout(fixAllCharts, 100);
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
                
                // 处理replaceChild错误
                if (event.error.message.includes('Failed to execute \'replaceChild\' on \'Node\'')) {
                    console.error(`捕获到replaceChild错误: ${event.error.message}`);
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
                
                // 处理内存不足错误
                if (event.error.message.includes('out of memory') ||
                    event.error.message.includes('内存不足')) {
                    console.error(`捕获到内存不足错误: ${event.error.message}`);
                    
                    // 尝试释放内存
                    for (const id in chartInstances) {
                        safeDestroyChart(id);
                    }
                    
                    // 显示友好的错误消息
                    alert('系统内存不足，请刷新页面或稍后再试。');
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);
        
        // 添加未捕获的Promise错误处理
        window.addEventListener('unhandledrejection', function(event) {
            console.error(`未处理的Promise错误: ${event.reason}`);
            
            // 不阻止默认行为，只记录错误
        });
    }
    
    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始执行维度详情页面修复');
        
        // 修复replaceChild错误
        fixReplaceChildError();
        
        // 优化内存使用
        optimizeMemoryUsage();
        
        // 修复分析过程显示问题
        fixAnalysisProcessDisplay();
        
        // 添加全局错误处理
        addGlobalErrorHandling();
        
        // 延迟执行图表修复，确保其他脚本已加载
        setTimeout(fixAllCharts, 500);
    });
    
    // 导出函数供其他模块使用
    window.dimensionDetailFix = {
        safeCreateChart: safeCreateChart,
        safeDestroyChart: safeDestroyChart,
        fixAllCharts: fixAllCharts
    };
})();
