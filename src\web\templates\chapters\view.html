{% extends "base.html" %}

{% block title %}{{ novel.title }} - 第{{ chapter.chapter_number }}章{% endblock %}

{% block head %}
{{ super() }}
<!-- 章节详情页面专用修复脚本 -->
<script>
// 立即执行函数，避免污染全局命名空间
(function() {
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[章节详情页面修复] 初始化');

        // 修复所有维度链接
        const dimensionLinks = document.querySelectorAll('.list-group-item');
        dimensionLinks.forEach(function(link) {
            // 获取原始href
            const originalHref = link.getAttribute('href');

            // 如果链接是禁用的或者是#，不处理
            if (link.classList.contains('disabled') || originalHref === '#') {
                return;
            }

            // 确保链接正确
            link.addEventListener('click', function(e) {
                console.log('[章节详情页面修复] 点击维度链接: ' + originalHref);
            });
        });

        // 修复分析按钮
        const analyzeButton = document.getElementById('startAnalysisBtn');
        if (analyzeButton) {
            analyzeButton.addEventListener('click', function(e) {
                console.log('[章节详情页面修复] 点击分析按钮');
            });
        }
    });
})();
</script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">小说列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('chapter.list_chapters', novel_id=novel.id) }}">章节列表</a></li>
            <li class="breadcrumb-item active" aria-current="page">第{{ chapter.chapter_number }}章</li>
        </ol>
    </nav>

    <div class="row">
        <!-- 左侧：章节内容 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h2>{{ chapter.title or '第' + chapter.chapter_number|string + '章' }}</h2>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p><strong>章节编号：</strong>{{ chapter.chapter_number }}</p>
                        <p><strong>字数：</strong>{{ chapter.word_count }}</p>
                    </div>
                    <div class="chapter-content">
                        {% for paragraph in chapter.content.split('\n') %}
                            {% if paragraph.strip() %}
                                <p>{{ paragraph }}</p>
                            {% else %}
                                <br>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：分析结果 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>分析结果</h2>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                            分析章节
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 分析进度条 -->
                    <div id="analysisProgressContainer" class="mb-4" style="display: none;">
                        <h4>分析进度</h4>
                        <div class="progress mb-2">
                            <div id="analysisProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                        <div id="analysisStatus" class="text-muted">准备中...</div>
                        <div class="mt-2">
                            <button id="cancelAnalysisBtn" class="btn btn-sm btn-danger">取消分析</button>
                        </div>
                    </div>

                    <!-- 分析结果列表 -->
                    <div class="list-group analysis-results-list">
                        {% for dimension in dimensions %}
                            {% set result = analysis_results.get(dimension.key) %}
                            <a href="{{ url_for('chapter.view_chapter_analysis', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) if result else '#' }}"
                               class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {{ 'disabled' if not result }}"
                               data-dimension="{{ dimension.key }}">
                                {{ dimension.name }}
                                {% if result %}
                                    <span class="badge bg-success">已分析</span>
                                {% else %}
                                    <span class="badge bg-secondary">未分析</span>
                                {% endif %}
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析模态框 -->
<div class="modal fade" id="analyzeModal" tabindex="-1" aria-labelledby="analyzeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analyzeModalLabel">分析章节</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="analyzeForm" action="/api/novel/{{ novel.id }}/chapter/{{ chapter.id }}/analyze" method="POST">
                    <div class="mb-3">
                        <p>请选择要分析的维度：</p>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="select-all-dimensions">
                            <label class="form-check-label" for="select-all-dimensions">
                                <strong>全选</strong>
                            </label>
                        </div>
                        <hr>
                        <!-- 维度列表将由JavaScript动态生成 -->
                        <div id="dimensions-container">
                            {% for dimension in dimensions %}
                            <div class="form-check mb-2">
                                <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="{{ dimension.key }}" id="dimension-{{ loop.index }}" checked>
                                <label class="form-check-label" for="dimension-{{ loop.index }}">
                                    {{ dimension.name }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="alert alert-warning mt-3">
                            <small>注意：分析过程可能需要较长时间，尤其是对于长篇小说。</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="modelSelect" class="form-label">选择分析模型</label>
                        <select class="form-select" id="modelSelect">
                            <option value="deepseek-r1">DeepSeek R1</option>
                            <option value="qwen-plus-latest">通义千问-Plus-Latest</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startAnalysisBtn">开始分析</button>
            </div>
        </div>
    </div>
</div>

<!-- 隐藏的数据元素，包含当前章节信息 -->
<div id="chapter-data" style="display: none;"
     data-novel-id="{{ novel.id }}"
     data-chapter-id="{{ chapter.id }}"></div>
{% endblock %}

{% block scripts %}
<!-- 模态框修复脚本 - 必须最先加载 -->
<script src="{{ url_for('static', filename='js/modal-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/modal-fix.js';" crossorigin="anonymous"></script>

<!-- 章节模态框直接修复脚本 - 专门用于修复章节分析页面的模态框问题 -->
<script src="{{ url_for('static', filename='js/chapter-modal-direct-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-modal-direct-fix.js';" crossorigin="anonymous"></script>

<!-- 章节分析维度修复脚本 -->
<script src="{{ url_for('static', filename='js/chapter-analysis-dimension-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-analysis-dimension-fix.js';" crossorigin="anonymous"></script>

<!-- 章节分析按钮修复脚本 -->
<script src="{{ url_for('static', filename='js/chapter-analysis-button-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-analysis-button-fix.js';" crossorigin="anonymous"></script>

<!-- 章节分析结果修复脚本 -->
<script src="{{ url_for('static', filename='js/chapter-analysis-result-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-analysis-result-fix.js';" crossorigin="anonymous"></script>

<!-- 紧急模态框按钮脚本 - 在页面上添加一个紧急按钮，用于手动触发模态框 -->
<script src="{{ url_for('static', filename='js/emergency-modal-button.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/emergency-modal-button.js';" crossorigin="anonymous"></script>

<!-- 替换模态框HTML脚本 - 直接替换模态框的HTML代码 -->
<script src="{{ url_for('static', filename='js/replace-modal-html.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/replace-modal-html.js';" crossorigin="anonymous"></script>

<!-- 独立维度选择对话框脚本 - 直接在页面上添加一个独立的维度选择对话框 -->
<script src="{{ url_for('static', filename='js/standalone-dimension-selector.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/standalone-dimension-selector.js';" crossorigin="anonymous"></script>

<!-- 章节分析结果修复脚本的初始化 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行，确保所有脚本都已加载
    setTimeout(function() {
        console.log('页面加载完成，手动触发章节分析结果修复');
        if (window.chapterAnalysisResultFix && window.chapterAnalysisResultFix.checkAndFixAnalysisCards) {
            window.chapterAnalysisResultFix.checkAndFixAnalysisCards();
        }
    }, 1000);
});
</script>
{% endblock %}
