"""
工具路由
提供各种开发和调试工具
"""
import logging
from flask import Blueprint, render_template, jsonify, request

# 创建蓝图
tools_bp = Blueprint('tools', __name__)

logger = logging.getLogger(__name__)

@tools_bp.route('/tools/console-capture')
def console_capture_demo():
    """浏览器控制台日志捕获工具演示页面"""
    try:
        return render_template('console_capture_demo.html')
    except Exception as e:
        logger.error(f"加载控制台捕获演示页面时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@tools_bp.route('/tools')
def tools_index():
    """工具索引页面"""
    tools_list = [
        {
            'name': '浏览器控制台日志捕获',
            'description': '捕获Chrome F12开发者工具中的console.log等日志，并在应用程序UI中显示',
            'url': '/tools/console-capture',
            'icon': 'terminal'
        }
        # 可以在这里添加更多工具
    ]
    
    try:
        return render_template('tools_index.html', tools=tools_list)
    except Exception as e:
        logger.error(f"加载工具索引页面时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
