/**
 * 九猫3.0系统模板API路径直接修复脚本
 * 用于修复模板API路径问题，特别是/api/novel/*/template/*路径
 */

(function() {
    console.log('[模板API直接修复] 初始化...');

    // 修复模板API路径
    function fixTemplateApiPaths() {
        console.log('[模板API直接修复] 开始修复模板API路径...');

        // 拦截所有AJAX请求
        if (typeof jQuery !== 'undefined' && jQuery.ajax) {
            const originalAjax = jQuery.ajax;
            
            jQuery.ajax = function(options) {
                if (typeof options === 'string') {
                    options = { url: options };
                }
                
                if (options && options.url) {
                    // 检查是否是模板相关路径
                    if (options.url.includes('/novel/') && options.url.includes('/template/')) {
                        const match = options.url.match(/\/novel\/(\d+)\/template\/([^\/\?]+)/);
                        if (match && match[1] && match[2]) {
                            const novelId = match[1];
                            const templateType = match[2];
                            
                            // 修复路径 - 直接使用v3_api.py中定义的路径格式
                            const fixedUrl = `/api/novel/${novelId}/book_template/${templateType}`;
                            console.log(`[模板API直接修复] 修复模板路径: ${options.url} -> ${fixedUrl}`);
                            options.url = fixedUrl;
                        }
                    }
                    
                    // 检查是否是章节模板相关路径
                    if (options.url.includes('/novel/') && options.url.includes('/chapter/') && options.url.includes('/template/')) {
                        const match = options.url.match(/\/novel\/(\d+)\/chapter\/(\d+)\/template\/([^\/\?]+)/);
                        if (match && match[1] && match[2] && match[3]) {
                            const novelId = match[1];
                            const chapterId = match[2];
                            const templateType = match[3];
                            
                            // 修复路径 - 直接使用v3_api.py中定义的路径格式
                            const fixedUrl = `/api/novel/${novelId}/chapter/${chapterId}/template/${templateType}`;
                            console.log(`[模板API直接修复] 修复章节模板路径: ${options.url} -> ${fixedUrl}`);
                            options.url = fixedUrl;
                        }
                    }
                }
                
                return originalAjax.apply(this, arguments);
            };
            
            console.log('[模板API直接修复] 已拦截jQuery.ajax方法');
        } else {
            console.warn('[模板API直接修复] jQuery未定义或jQuery.ajax不可用，无法修复模板路径');
        }

        // 修复fetch请求
        if (typeof window.fetch === 'function') {
            const originalFetch = window.fetch;
            
            window.fetch = function(input, init) {
                if (typeof input === 'string') {
                    // 检查是否是模板相关路径
                    if (input.includes('/novel/') && input.includes('/template/')) {
                        const match = input.match(/\/novel\/(\d+)\/template\/([^\/\?]+)/);
                        if (match && match[1] && match[2]) {
                            const novelId = match[1];
                            const templateType = match[2];
                            
                            // 修复路径
                            const fixedUrl = `/api/novel/${novelId}/book_template/${templateType}`;
                            console.log(`[模板API直接修复] 修复模板路径(fetch): ${input} -> ${fixedUrl}`);
                            input = fixedUrl;
                        }
                    }
                    
                    // 检查是否是章节模板相关路径
                    if (input.includes('/novel/') && input.includes('/chapter/') && input.includes('/template/')) {
                        const match = input.match(/\/novel\/(\d+)\/chapter\/(\d+)\/template\/([^\/\?]+)/);
                        if (match && match[1] && match[2] && match[3]) {
                            const novelId = match[1];
                            const chapterId = match[2];
                            const templateType = match[3];
                            
                            // 修复路径
                            const fixedUrl = `/api/novel/${novelId}/chapter/${chapterId}/template/${templateType}`;
                            console.log(`[模板API直接修复] 修复章节模板路径(fetch): ${input} -> ${fixedUrl}`);
                            input = fixedUrl;
                        }
                    }
                }
                
                return originalFetch.call(this, input, init);
            };
            
            console.log('[模板API直接修复] 已拦截fetch方法');
        }

        // 修复XMLHttpRequest
        if (typeof window.XMLHttpRequest === 'function') {
            const originalOpen = XMLHttpRequest.prototype.open;
            
            XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                if (typeof url === 'string') {
                    // 检查是否是模板相关路径
                    if (url.includes('/novel/') && url.includes('/template/')) {
                        const match = url.match(/\/novel\/(\d+)\/template\/([^\/\?]+)/);
                        if (match && match[1] && match[2]) {
                            const novelId = match[1];
                            const templateType = match[2];
                            
                            // 修复路径
                            const fixedUrl = `/api/novel/${novelId}/book_template/${templateType}`;
                            console.log(`[模板API直接修复] 修复模板路径(XHR): ${url} -> ${fixedUrl}`);
                            url = fixedUrl;
                        }
                    }
                    
                    // 检查是否是章节模板相关路径
                    if (url.includes('/novel/') && url.includes('/chapter/') && url.includes('/template/')) {
                        const match = url.match(/\/novel\/(\d+)\/chapter\/(\d+)\/template\/([^\/\?]+)/);
                        if (match && match[1] && match[2] && match[3]) {
                            const novelId = match[1];
                            const chapterId = match[2];
                            const templateType = match[3];
                            
                            // 修复路径
                            const fixedUrl = `/api/novel/${novelId}/chapter/${chapterId}/template/${templateType}`;
                            console.log(`[模板API直接修复] 修复章节模板路径(XHR): ${url} -> ${fixedUrl}`);
                            url = fixedUrl;
                        }
                    }
                }
                
                return originalOpen.call(this, method, url, async, user, password);
            };
            
            console.log('[模板API直接修复] 已拦截XMLHttpRequest.open方法');
        }

        console.log('[模板API直接修复] 模板API路径修复完成');
    }

    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixTemplateApiPaths);
    } else {
        fixTemplateApiPaths();
    }
})();
