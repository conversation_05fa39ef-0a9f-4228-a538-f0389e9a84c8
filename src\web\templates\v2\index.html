{% extends "v2/base.html" %}

{% block title %}首页 - 九猫小说分析系统v2.0{% endblock %}

{% block content %}
<div class="row">
    <!-- 主要内容区域 -->
    <div class="col-lg-8">
        <!-- 欢迎卡片 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-cat fa-3x text-primary me-3"></i>
                    <h2 class="card-title mb-0">欢迎使用九猫小说分析系统v2.0</h2>
                </div>
                <p class="lead">九猫是一款专为作家和编辑设计的智能小说分析工具，基于先进的AI技术，提供全方位的文本分析服务。</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-start mt-4">
                    <a href="{{ url_for('v2.upload_novel') }}" class="btn btn-primary btn-lg px-4 me-md-2">
                        <i class="fas fa-upload me-2"></i>开始分析
                    </a>
                    <a href="{{ url_for('v2.novels') }}" class="btn btn-outline-secondary btn-lg px-4">
                        <i class="fas fa-book me-2"></i>查看小说
                    </a>
                </div>
            </div>
        </div>

        <!-- 系统特点 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-star me-2"></i>系统特点</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-cubes fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>多维度分析</h4>
                                <p>提供13个专业维度的深度分析，全方位评估小说质量。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-brain fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>AI驱动</h4>
                                <p>采用先进的AI模型，提供专业水准的文学分析。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-tachometer-alt fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>高效处理</h4>
                                <p>优化的处理流程，快速分析长篇小说文本。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-lock fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>安全可靠</h4>
                                <p>本地部署，确保您的创作内容安全保密。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用流程 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-tasks me-2"></i>使用流程</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <i class="fas fa-upload fa-3x text-primary mb-3"></i>
                            <h5>1. 上传小说</h5>
                            <p class="small">上传TXT文件或直接粘贴文本内容</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <i class="fas fa-cogs fa-3x text-primary mb-3"></i>
                            <h5>2. 选择维度</h5>
                            <p class="small">从13个专业维度中选择需要分析的方面</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <i class="fas fa-spinner fa-3x text-primary mb-3"></i>
                            <h5>3. 等待分析</h5>
                            <p class="small">系统自动进行分析，无需人工干预</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <i class="fas fa-chart-bar fa-3x text-primary mb-3"></i>
                            <h5>4. 查看结果</h5>
                            <p class="small">获取详细的分析报告和改进建议</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 最近分析 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-history me-2"></i>最近分析</h3>
            </div>
            <div class="card-body">
                {% if novels %}
                    <ul class="list-group list-group-flush">
                        {% for novel in novels %}
                            <li class="list-group-item bg-transparent border-bottom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="{{ url_for('v2.view_novel', novel_id=novel.id) }}" class="text-decoration-none">
                                        <h5 class="mb-1">{{ novel.title }}</h5>
                                        <p class="small text-muted mb-0">
                                            {% if novel.author %}作者: {{ novel.author }}{% endif %}
                                            <span class="ms-2">{{ novel.word_count }} 字</span>
                                        </p>
                                    </a>
                                    <span class="badge bg-primary rounded-pill">{{ novel.created_at.strftime('%m-%d') }}</span>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-book fa-3x text-muted mb-3"></i>
                        <p>暂无分析记录</p>
                        <a href="{{ url_for('v2.upload_novel') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-upload me-1"></i>上传小说
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 分析维度 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-cubes me-2"></i>分析维度</h3>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-6">
                        <div class="p-2 border rounded text-center">
                            <i class="fas fa-language text-primary"></i>
                            <p class="small mb-0">语言风格</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="p-2 border rounded text-center">
                            <i class="fas fa-drum text-primary"></i>
                            <p class="small mb-0">节奏节拍</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="p-2 border rounded text-center">
                            <i class="fas fa-sitemap text-primary"></i>
                            <p class="small mb-0">结构分析</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="p-2 border rounded text-center">
                            <i class="fas fa-users text-primary"></i>
                            <p class="small mb-0">人物关系</p>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('v2.dimensions') }}" class="btn btn-outline-primary btn-sm">
                        查看全部13个维度
                    </a>
                </div>
            </div>
        </div>

        <!-- 快速入门 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-rocket me-2"></i>快速入门</h3>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li class="mb-2">上传您的小说文本</li>
                    <li class="mb-2">选择需要分析的维度</li>
                    <li class="mb-2">查看整本书分析结果</li>
                    <li class="mb-2">查看章节分析结果</li>
                    <li>获取章节分析汇总</li>
                </ol>
                <div class="text-center mt-3">
                    <a href="{{ url_for('v2.help') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-question-circle me-1"></i>查看帮助
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
