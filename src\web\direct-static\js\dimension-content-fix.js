/**
 * 九猫 - 维度内容修复脚本
 * 用于修复分析结果卡片显示问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('[九猫修复] 维度内容修复脚本已加载');
    
    // 配置
    const CONFIG = {
        // 调试模式
        debug: true,
        
        // 是否在页面加载时自动修复
        autoFix: true,
        
        // 修复延迟（毫秒）
        fixDelay: 1000,
        
        // 重试次数
        maxRetries: 3,
        
        // 重试间隔（毫秒）
        retryInterval: 2000
    };
    
    // 安全日志函数
    function safeLog(message, level = 'log') {
        try {
            if (CONFIG.debug || level === 'error' || level === 'warn') {
                console[level](`[九猫修复] ${message}`);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }
    
    // 维度名称映射
    const DIMENSION_NAMES = {
        'language_style': '语言风格',
        'rhythm': '高潮节奏',
        'perspective_shifts': '视角转换',
        'character_development': '人物发展',
        'plot_structure': '情节结构',
        'theme_exploration': '主题探索',
        'setting_description': '场景描写',
        'paragraph_flow': '段落流畅度',
        'dialogue_quality': '对话质量',
        'emotional_impact': '情感冲击',
        'conflict_handling': '冲突处理',
        'symbolism': '象征手法',
        'character_relationships': '人物关系'
    };
    
    // 查找所有维度卡片
    function findDimensionCards() {
        return Array.from(document.querySelectorAll('.analysis-card[data-dimension]'));
    }
    
    // 检查卡片是否为空白或显示占位符
    function isCardEmpty(card) {
        const cardBody = card.querySelector('.card-body');
        if (!cardBody) return true;
        
        // 检查是否包含占位符文本
        const excerptDiv = cardBody.querySelector('.analysis-excerpt');
        if (!excerptDiv) return true;
        
        const text = excerptDiv.textContent.trim();
        return text.includes('分析结果将在此显示') || 
               text.includes('表示分析结果可能尚未生成') || 
               text.includes('请尝试刷新页面') ||
               text === '';
    }
    
    // 修复单个维度卡片
    function fixDimensionCard(card) {
        try {
            const dimension = card.getAttribute('data-dimension');
            if (!dimension) {
                safeLog('卡片没有维度属性，无法修复', 'warn');
                return false;
            }
            
            safeLog(`修复维度 ${dimension} 的卡片`);
            
            // 获取小说ID
            const novelId = getNovelId();
            if (!novelId) {
                safeLog('无法获取小说ID，无法修复卡片', 'warn');
                return false;
            }
            
            // 获取卡片主体
            const cardBody = card.querySelector('.card-body');
            if (!cardBody) {
                safeLog(`找不到维度 ${dimension} 的卡片主体`, 'warn');
                return false;
            }
            
            // 显示加载状态
            cardBody.innerHTML = `
                <div class="d-flex justify-content-between mb-2">
                    <span class="badge bg-warning">正在加载...</span>
                    <a href="/novel/${novelId}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                </div>
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;
            
            // 获取分析结果
            fetchAnalysisResult(novelId, dimension)
                .then(result => {
                    if (result && result.content) {
                        updateCardWithResult(card, result);
                        return true;
                    } else {
                        safeLog(`维度 ${dimension} 的分析结果为空`, 'warn');
                        showEmptyResult(card, dimension);
                        return false;
                    }
                })
                .catch(error => {
                    safeLog(`获取维度 ${dimension} 的分析结果时出错: ${error.message}`, 'error');
                    showErrorResult(card, dimension, error.message);
                    return false;
                });
            
            return true;
        } catch (e) {
            safeLog(`修复维度卡片时出错: ${e.message}`, 'error');
            return false;
        }
    }
    
    // 获取小说ID
    function getNovelId() {
        try {
            // 从URL中获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
            
            // 从页面元素中获取
            const novelIdElement = document.querySelector('[data-novel-id]');
            if (novelIdElement) {
                return novelIdElement.getAttribute('data-novel-id');
            }
            
            // 从分析按钮中获取
            const analyzeButton = document.querySelector('.analyze-button[data-novel-id]');
            if (analyzeButton) {
                return analyzeButton.getAttribute('data-novel-id');
            }
            
            return null;
        } catch (e) {
            safeLog(`获取小说ID时出错: ${e.message}`, 'error');
            return null;
        }
    }
    
    // 获取分析结果
    function fetchAnalysisResult(novelId, dimension) {
        return new Promise((resolve, reject) => {
            fetch(`/api/novels/${novelId}/analysis/${dimension}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && data.content) {
                        resolve(data);
                    } else {
                        // 尝试从全局变量中获取
                        if (window.analysisResultsData && window.analysisResultsData[dimension]) {
                            resolve(window.analysisResultsData[dimension]);
                        } else {
                            reject(new Error('分析结果为空'));
                        }
                    }
                })
                .catch(reject);
        });
    }
    
    // 使用分析结果更新卡片
    function updateCardWithResult(card, result) {
        try {
            const dimension = card.getAttribute('data-dimension');
            const cardBody = card.querySelector('.card-body');
            if (!cardBody) return false;
            
            // 获取维度名称
            const dimensionName = DIMENSION_NAMES[dimension] || dimension;
            
            // 提取内容的前300个字符作为摘要
            let excerpt = '';
            try {
                // 去除HTML标签，只保留文本内容
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = result.content || '';
                const textContent = tempDiv.textContent || tempDiv.innerText || '';
                excerpt = textContent.substring(0, 300) + (textContent.length > 300 ? '...' : '');
            } catch (e) {
                safeLog(`提取摘要时出错: ${e.message}`, 'warn');
                excerpt = (result.content || '').substring(0, 300) + '...';
            }
            
            // 创建可视化HTML
            let visualizationHtml = '';
            if (result.metadata && result.metadata.visualization_data) {
                visualizationHtml = `
                    <div class="analysis-visualization mt-3">
                        <canvas class="analysis-chart" data-dimension="${dimension}" width="400" height="250"></canvas>
                    </div>
                `;
            }
            
            // 更新卡片内容
            cardBody.innerHTML = `
                <div class="d-flex justify-content-between mb-2">
                    <span class="badge bg-success">分析完成</span>
                    <a href="/novel/${getNovelId()}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                </div>
                ${visualizationHtml}
                <div class="analysis-excerpt mt-2">
                    ${excerpt}
                </div>
            `;
            
            // 保存结果数据到全局变量，供图表使用
            if (typeof window.analysisResultsData === 'undefined') {
                window.analysisResultsData = {};
            }
            window.analysisResultsData[dimension] = result;
            
            // 初始化图表
            if (visualizationHtml) {
                setTimeout(() => {
                    const canvas = card.querySelector('.analysis-chart');
                    if (canvas && window.initializeCharts) {
                        window.initializeCharts();
                    }
                }, 100);
            }
            
            safeLog(`成功更新维度 ${dimension} 的卡片内容`);
            return true;
        } catch (e) {
            safeLog(`更新卡片内容时出错: ${e.message}`, 'error');
            return false;
        }
    }
    
    // 显示空结果
    function showEmptyResult(card, dimension) {
        const cardBody = card.querySelector('.card-body');
        if (!cardBody) return;
        
        cardBody.innerHTML = `
            <div class="d-flex justify-content-between mb-2">
                <span class="badge bg-warning">暂无结果</span>
                <a href="/novel/${getNovelId()}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
            </div>
            <div class="analysis-excerpt mt-2">
                <p>该维度的分析结果尚未生成或加载失败。</p>
                <p>请点击"重新分析"按钮进行分析。</p>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary btn-sm analyze-single-dimension" data-dimension="${dimension}" data-novel-id="${getNovelId()}">
                    <i class="fas fa-sync-alt"></i> 重新分析
                </button>
            </div>
        `;
        
        // 绑定分析按钮事件
        const analyzeButton = cardBody.querySelector('.analyze-single-dimension');
        if (analyzeButton && window.startSingleDimensionAnalysis) {
            analyzeButton.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');
                const novelId = this.getAttribute('data-novel-id');
                window.startSingleDimensionAnalysis(novelId, dimension);
            });
        }
    }
    
    // 显示错误结果
    function showErrorResult(card, dimension, errorMessage) {
        const cardBody = card.querySelector('.card-body');
        if (!cardBody) return;
        
        cardBody.innerHTML = `
            <div class="d-flex justify-content-between mb-2">
                <span class="badge bg-danger">加载失败</span>
                <a href="/novel/${getNovelId()}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
            </div>
            <div class="analysis-excerpt mt-2">
                <p>加载分析结果时出错: ${errorMessage}</p>
                <p>请点击"重新加载"按钮尝试再次加载。</p>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary btn-sm reload-dimension" data-dimension="${dimension}">
                    <i class="fas fa-sync-alt"></i> 重新加载
                </button>
            </div>
        `;
        
        // 绑定重新加载按钮事件
        const reloadButton = cardBody.querySelector('.reload-dimension');
        if (reloadButton) {
            reloadButton.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');
                fixDimensionCard(card);
            });
        }
    }
    
    // 修复所有维度卡片
    function fixAllDimensionCards() {
        try {
            const cards = findDimensionCards();
            let fixedCount = 0;
            
            safeLog(`找到 ${cards.length} 个维度卡片`);
            
            cards.forEach(card => {
                if (isCardEmpty(card)) {
                    if (fixDimensionCard(card)) {
                        fixedCount++;
                    }
                }
            });
            
            safeLog(`修复了 ${fixedCount} 个空白卡片`);
            return fixedCount;
        } catch (e) {
            safeLog(`修复所有维度卡片时出错: ${e.message}`, 'error');
            return 0;
        }
    }
    
    // 导出公共方法
    window.dimensionContentFix = {
        fixDimensionCard,
        fixAllDimensionCards,
        isCardEmpty,
        findDimensionCards
    };
    
    // 自动修复
    if (CONFIG.autoFix) {
        setTimeout(() => {
            safeLog('开始自动修复维度卡片');
            fixAllDimensionCards();
        }, CONFIG.fixDelay);
    }
})();
