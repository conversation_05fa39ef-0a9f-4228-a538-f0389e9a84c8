# 九猫系统 - 永久修改系统环境变量指南

本指南详细说明如何永久修改系统环境变量，将临时文件重定向到非系统盘，以保证C盘空间充足并确保系统正常运行。

## 问题背景

九猫小说分析系统在分析大型小说文件时，会产生大量临时文件。默认情况下，这些文件存储在C盘的临时目录中，可能导致以下问题：

1. C盘空间不足
2. 系统运行变慢
3. 分析大型文件时可能崩溃
4. 可能影响其他应用程序的正常运行

## 解决方案

我们提供了三种解决方案，建议按照以下优先级使用：

### 方案一：使用自动化脚本（推荐）

我们提供了专用脚本，可以一键修改系统环境变量：

1. 右键点击 `修改系统环境变量.bat`，选择"以管理员身份运行"
2. 按照提示选择临时文件存储位置（如D盘或E盘）
3. 确认修改，并在完成后重启计算机
4. 重启后，所有应用程序（包括九猫系统）都将使用新的临时目录

### 方案二：使用优化版启动脚本

如果不想永久修改系统环境变量，可以使用优化版启动脚本：

1. 双击运行 `启动九猫_优化版.bat`
2. 脚本会自动设置当前会话的临时目录
3. 九猫系统会使用指定的临时目录，不占用C盘空间

### 方案三：手动修改系统环境变量

如果需要手动修改环境变量，请按照以下步骤操作：

1. 右键点击"此电脑"或"我的电脑"，选择"属性"
2. 点击"高级系统设置"
3. 在"高级"选项卡下，点击"环境变量"
4. 在"系统变量"部分，找到并编辑 `TEMP` 和 `TMP` 变量
5. 将它们的值改为非C盘路径，例如 `D:\Temp`
6. 点击"确定"保存更改
7. 重启计算机以使更改生效

## 定期维护建议

为确保系统持续正常运行，建议：

1. 定期运行 `监控C盘空间.bat` 检查C盘使用情况
2. 定期清理临时文件夹中的旧文件
3. 如果使用方案二，每次启动九猫系统都使用优化版启动脚本

## 常见问题

### 问题：修改环境变量后，某些程序仍然往C盘写入临时文件

**解决方案**：使用符号链接。我们的 `修改系统环境变量.bat` 脚本提供了创建符号链接的选项，可以将常用的C盘临时目录链接到非C盘位置。

### 问题：修改环境变量后系统出现异常

**解决方案**：如果出现问题，可以恢复默认设置：

1. 打开环境变量设置
2. 将 `TEMP` 和 `TMP` 变量值改回默认值：
   - 系统变量：`%SystemRoot%\TEMP`
   - 用户变量：`%USERPROFILE%\AppData\Local\Temp`

### 问题：不知道应该将临时文件设置在哪个盘

**建议**：选择空间较大的非系统盘。通常D盘或E盘是较好的选择。确保该磁盘有至少10GB的可用空间。

## 技术原理

系统环境变量 `TEMP` 和 `TMP` 告诉操作系统和应用程序应该在哪里存储临时文件。通过修改这些变量，我们可以改变临时文件的存储位置，从而减轻C盘负担。

大多数应用程序（包括九猫系统）会遵循这些环境变量，但某些应用可能硬编码了临时文件路径。对于这类应用，符号链接技术可以"欺骗"它们，使它们以为在写入C盘，实际却写入到其他磁盘。

---

**注意**：修改系统环境变量需要管理员权限，并且可能影响系统中的所有应用程序。如果不确定，请选择方案二（使用优化版启动脚本）。 