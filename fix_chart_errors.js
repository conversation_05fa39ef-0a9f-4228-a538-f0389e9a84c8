/**
 * 九猫小说分析系统 - 图表和DOM错误修复脚本
 * 解决图表重复初始化和DOM操作冲突问题
 */
(function() {
    console.log("图表和DOM错误修复脚本已加载");

    // 跟踪已初始化的图表
    const initializedCharts = {};
    
    // 防止重复初始化图表
    function preventChartReinitialization() {
        // 备份原始Chart构造函数
        const originalChart = window.Chart;
        if (!originalChart) {
            console.log("未找到Chart对象，等待Chart.js加载");
            return false;
        }

        // 替换Chart构造函数
        window.Chart = function(ctx, config) {
            const canvasId = ctx.canvas ? ctx.canvas.id : "unknown";
            
            // 如果画布已有图表，先销毁
            if (initializedCharts[canvasId]) {
                console.log(`销毁已存在的图表 (ID: ${canvasId})`);
                initializedCharts[canvasId].destroy();
            }
            
            // 创建新图表
            const chart = new originalChart(ctx, config);
            initializedCharts[canvasId] = chart;
            console.log(`成功创建图表 (ID: ${canvasId})`);
            return chart;
        };
        
        // 复制原型和静态属性
        window.Chart.prototype = originalChart.prototype;
        Object.keys(originalChart).forEach(key => {
            window.Chart[key] = originalChart[key];
        });
        
        return true;
    }
    
    // 修复DOM操作冲突
    function fixDOMOperations() {
        // 修复appendChild
        const originalAppendChild = Node.prototype.appendChild;
        Node.prototype.appendChild = function(node) {
            try {
                if (this.contains(node)) {
                    console.log("appendChild: 节点已经是子节点，跳过操作");
                    return node;
                }
                return originalAppendChild.call(this, node);
            } catch (error) {
                console.log(`安全的appendChild: 错误已拦截 - ${error.message}`);
                return node;
            }
        };
        
        // 修复insertBefore
        const originalInsertBefore = Node.prototype.insertBefore;
        Node.prototype.insertBefore = function(newNode, referenceNode) {
            try {
                if (this.contains(newNode)) {
                    console.log("insertBefore: 节点已经是子节点，尝试移动位置");
                    // 移除节点后再插入
                    newNode.parentNode.removeChild(newNode);
                }
                return originalInsertBefore.call(this, newNode, referenceNode);
            } catch (error) {
                console.log(`安全的insertBefore: 错误已拦截 - ${error.message}`);
                return newNode;
            }
        };
    }
    
    // 修复资源预加载问题
    function fixResourcePreloading() {
        // 移除无用的预加载链接
        document.querySelectorAll('link[rel="preload"]').forEach(link => {
            const href = link.getAttribute('href');
            if (href && (href.includes('jquery.min.js') || href.includes('bootstrap.bundle.min.js'))) {
                // 确认资源已加载后移除预加载链接
                if (window.jQuery || window.bootstrap) {
                    console.log(`移除多余的预加载链接: ${href}`);
                    link.remove();
                }
            }
        });
    }
    
    // 修复rhythm_pacing图表特有问题
    function fixRhythmPacingCharts() {
        // 查找页面路径判断是否在节奏分析页面
        const currentPath = window.location.pathname;
        if (currentPath.includes('/analysis/rhythm_pacing')) {
            console.log("检测到节奏分析页面，应用特定修复");
            
            // 监听图表初始化
            const originalInitCharts = window.initCharts;
            if (originalInitCharts) {
                window.initCharts = function() {
                    // 确保画布清理
                    document.querySelectorAll('canvas').forEach(canvas => {
                        const ctx = canvas.getContext('2d');
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        
                        // 如果有图表ID，确保销毁旧图表
                        const chartId = canvas.id || canvas.getAttribute('data-chart-id');
                        if (chartId && initializedCharts[chartId]) {
                            initializedCharts[chartId].destroy();
                        }
                    });
                    
                    // 调用原始初始化函数
                    return originalInitCharts.apply(this, arguments);
                };
                console.log("已修复initCharts函数");
            }
        }
    }
    
    // 等待DOM加载完成后执行修复
    function init() {
        console.log("开始应用图表和DOM错误修复");
        
        // 修复DOM操作
        fixDOMOperations();
        console.log("DOM操作修复已应用");
        
        // 延迟执行以确保Chart.js已加载
        const checkChartAndApplyFix = function() {
            if (window.Chart) {
                const success = preventChartReinitialization();
                if (success) {
                    console.log("图表重复初始化问题已修复");
                    
                    // 修复特定图表问题
                    fixRhythmPacingCharts();
                    
                    // 修复资源预加载问题
                    fixResourcePreloading();
                }
            } else {
                // 继续等待Chart.js加载
                setTimeout(checkChartAndApplyFix, 500);
            }
        };
        
        checkChartAndApplyFix();
    }
    
    // 检查DOM是否已加载
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", init);
    } else {
        init();
    }
    
    // 在window.load之后再次检查，确保所有资源都已加载
    window.addEventListener("load", function() {
        setTimeout(function() {
            fixResourcePreloading();
        }, 3000); // 等待3秒，确保预加载警告已出现
    });
})(); 