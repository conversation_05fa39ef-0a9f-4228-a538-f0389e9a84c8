"""
简单检查章节分析结果
"""

from src.db.connection import Session
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.chapter import Chapter
from src.models.novel import Novel

def check_chapter_outline():
    session = Session()
    try:
        # 查询所有章纲分析结果
        results = session.query(ChapterAnalysisResult).filter_by(dimension="chapter_outline").all()
        print(f"找到 {len(results)} 个章纲分析结果")
        
        # 检查每个结果
        for result in results:
            chapter = session.query(Chapter).get(result.chapter_id)
            if not chapter:
                print(f"找不到章节: {result.chapter_id}")
                continue
                
            print(f"章节: {chapter.title or f'第{chapter.chapter_number}章'}")
            print(f"内容长度: {len(result.content) if result.content else 0}")
            print(f"推理过程长度: {len(result.reasoning_content) if result.reasoning_content else 0}")
            
            # 检查内容中是否包含"主要内容"
            if result.content and "## 主要内容" in result.content:
                import re
                main_content_match = re.search(r'## 主要内容\s*\n(.*?)(?:\n##|\Z)', result.content, re.DOTALL)
                if main_content_match:
                    main_content = main_content_match.group(1).strip()
                    print(f"主要内容长度: {len(main_content)}")
                    print(f"主要内容前100个字符: {main_content[:100]}")
                else:
                    print("未找到主要内容部分")
            else:
                print("内容中不包含'主要内容'部分")
                
            print("-" * 50)
    finally:
        session.close()

if __name__ == "__main__":
    check_chapter_outline()
