"""
九猫小说分析写作系统v3.0 - 测试功能API路由
"""
import logging
import os
import json
import time
from datetime import datetime
from flask import Blueprint, request, jsonify, send_file, current_app
from werkzeug.utils import secure_filename
import io
import zipfile

from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.db.connection import Session
from src.services.test_service import TestService
import config

# 创建蓝图
v3_test_api_bp = Blueprint('v3_test_api', __name__)
logger = logging.getLogger(__name__)

@v3_test_api_bp.route('/api/test/upload', methods=['POST'])
def upload_novel():
    """上传小说API"""
    try:
        title = request.form.get('title', '未命名小说')
        author = request.form.get('author', '')
        content = ''
        file_path = None
        file_name = None

        # 处理文件上传
        if 'file' in request.files:
            file = request.files['file']
            if file.filename:
                # 保存文件
                filename = secure_filename(file.filename)
                upload_folder = os.path.join(current_app.root_path, config.UPLOAD_FOLDER)

                # 确保上传目录存在
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)

                file_path = os.path.join(upload_folder, filename)
                file.save(file_path)
                file_name = filename

                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

        # 处理文本粘贴
        elif 'content' in request.form:
            content = request.form['content']

        if not content:
            return jsonify({
                'success': False,
                'error': '请上传文件或提供文本内容'
            }), 400

        # 调用服务上传小说
        success, novel_id, error = TestService.upload_novel(
            title=title,
            content=content,
            author=author,
            file_path=file_path,
            file_name=file_name
        )

        if success:
            return jsonify({
                'success': True,
                'novel_id': novel_id,
                'message': f'小说《{title}》上传成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': error
            }), 500
    except Exception as e:
        logger.error(f"上传小说API出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'上传小说失败: {str(e)}'
        }), 500

@v3_test_api_bp.route('/api/test/analyze/<int:novel_id>', methods=['POST'])
def analyze_novel(novel_id):
    """分析小说API"""
    try:
        # 获取请求数据
        data = request.json or {}
        model = data.get('model', 'deepseek-r1')  # 默认使用 DeepSeek R1 模型
        prompt_template = data.get('prompt_template', 'default')  # 默认使用默认提示词模板
        novel_type = data.get('novel_type', 'long')  # 默认为长篇小说

        # 记录选择的模型、提示词模板和小说类型
        logger.info(f"使用模型 {model}、提示词模板 {prompt_template}、小说类型 {novel_type} 分析小说 ID: {novel_id}")

        # 调用服务开始分析任务
        success, task_id, error = TestService.start_analysis_task(novel_id, model=model, prompt_template=prompt_template, novel_type=novel_type)

        if success:
            return jsonify({
                'success': True,
                'task_id': task_id,
                'message': f'分析任务已创建，任务ID: {task_id}，使用模型: {model}，提示词模板: {prompt_template}，小说类型: {novel_type}'
            })
        else:
            return jsonify({
                'success': False,
                'error': error
            }), 500
    except Exception as e:
        logger.error(f"分析小说API出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'创建分析任务失败: {str(e)}'
        }), 500

@v3_test_api_bp.route('/api/test/status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """获取任务状态API"""
    try:
        # 调用服务获取任务状态
        success, status_data, error = TestService.get_task_status(task_id)

        if success:
            return jsonify({
                'success': True,
                **status_data
            })
        else:
            return jsonify({
                'success': False,
                'error': error
            }), 404
    except Exception as e:
        logger.error(f"获取任务状态API出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'获取任务状态失败: {str(e)}'
        }), 500

@v3_test_api_bp.route('/api/test/results/<task_id>', methods=['GET'])
def get_task_results(task_id):
    """获取任务结果API"""
    try:
        # 调用服务获取任务状态
        success, status_data, error = TestService.get_task_status(task_id)

        if success:
            # 检查任务是否完成
            if status_data.get('completed', False) and 'results' in status_data:
                return jsonify({
                    'success': True,
                    'results': status_data['results']
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '任务尚未完成或没有结果数据'
                }), 404
        else:
            return jsonify({
                'success': False,
                'error': error
            }), 404
    except Exception as e:
        logger.error(f"获取任务结果API出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'获取任务结果失败: {str(e)}'
        }), 500

@v3_test_api_bp.route('/api/test/dimension/<int:novel_id>/<dimension>', methods=['GET'])
def get_dimension_detail(novel_id, dimension):
    """获取维度详情API"""
    try:
        # 调用服务获取维度详情
        success, detail, error = TestService.get_dimension_detail(novel_id, dimension)

        if success:
            return jsonify({
                'success': True,
                'detail': detail
            })
        else:
            return jsonify({
                'success': False,
                'error': error
            }), 404
    except Exception as e:
        logger.error(f"获取维度详情API出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'获取维度详情失败: {str(e)}'
        }), 500

@v3_test_api_bp.route('/api/test/download/<int:novel_id>', methods=['GET'])
def download_results(novel_id):
    """下载结果API"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': f'未找到ID为{novel_id}的小说'
                }), 404

            # 获取分析结果
            analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 获取章节分析结果
            chapter_analysis_results = {}
            for chapter in chapters:
                results = session.query(ChapterAnalysisResult).filter_by(chapter_id=chapter.id).all()
                if results:
                    chapter_analysis_results[chapter.id] = results

            # 创建内存中的ZIP文件
            memory_file = io.BytesIO()
            with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
                # 添加小说信息
                novel_info = {
                    'id': novel.id,
                    'title': novel.title,
                    'author': novel.author,
                    'word_count': novel.word_count,
                    'created_at': novel.created_at.isoformat() if novel.created_at else None
                }
                zf.writestr('novel_info.json', json.dumps(novel_info, ensure_ascii=False, indent=2))

                # 添加小说内容
                zf.writestr('novel_content.txt', novel.content)

                # 添加整本书分析结果
                if analysis_results:
                    os.makedirs('book_analysis', exist_ok=True)
                    for result in analysis_results:
                        file_name = f'book_analysis/{result.dimension}.md'
                        content = result.content

                        # 添加推理过程（如果有）
                        if result.analysis_metadata and 'reasoning_content' in result.analysis_metadata:
                            reasoning = result.analysis_metadata['reasoning_content']
                            content = f"# 推理过程\n\n{reasoning}\n\n# 分析结果\n\n{content}"

                        zf.writestr(file_name, content)

                # 添加章节内容和分析结果
                if chapters:
                    os.makedirs('chapters', exist_ok=True)
                    for chapter in chapters:
                        # 添加章节内容
                        chapter_file_name = f'chapters/chapter_{chapter.chapter_number}.txt'
                        zf.writestr(chapter_file_name, chapter.content)

                        # 添加章节分析结果
                        if chapter.id in chapter_analysis_results:
                            chapter_dir = f'chapters/chapter_{chapter.chapter_number}_analysis'
                            os.makedirs(chapter_dir, exist_ok=True)

                            for result in chapter_analysis_results[chapter.id]:
                                file_name = f'{chapter_dir}/{result.dimension}.md'
                                content = result.content

                                # 添加推理过程（如果有）
                                if result.analysis_metadata and 'reasoning_content' in result.analysis_metadata:
                                    reasoning = result.analysis_metadata['reasoning_content']
                                    content = f"# 推理过程\n\n{reasoning}\n\n# 分析结果\n\n{content}"

                                zf.writestr(file_name, content)

            # 设置文件指针到开始位置
            memory_file.seek(0)

            # 生成下载文件名
            download_name = f"{novel.title}_分析结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            download_name = secure_filename(download_name)

            return send_file(
                memory_file,
                mimetype='application/zip',
                as_attachment=True,
                download_name=download_name
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"下载结果API出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'下载结果失败: {str(e)}'
        }), 500
