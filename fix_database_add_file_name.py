"""
数据库修复脚本：为novels表添加file_name列
"""
import os
import sys
import logging
import traceback
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 配置日志
log_filename = f"fix_database_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_query(conn, query, params=None):
    """执行数据库查询并记录"""
    try:
        if params:
            logger.debug(f"执行查询: {query} 参数: {params}")
            return conn.execute(query, params)
        else:
            logger.debug(f"执行查询: {query}")
            return conn.execute(query)
    except Exception as e:
        logger.error(f"查询执行失败: {str(e)}")
        raise

def add_file_name_column():
    """为novels表添加file_name列"""
    try:
        # 动态导入以避免早期导入错误
        from sqlalchemy import text
        from src.db.connection import engine

        logger.info("开始添加file_name列到novels表...")
        
        # 检查列是否已存在
        with engine.connect() as conn:
            # 使用事务
            with conn.begin():
                # 检查列是否存在
                check_sql = text("PRAGMA table_info(novels)")
                result = run_query(conn, check_sql)
                columns = [row[1] for row in result]
                
                if 'file_name' in columns:
                    logger.info("file_name列已存在，无需添加")
                else:
                    # 添加列
                    logger.info("添加file_name列...")
                    alter_sql = text("ALTER TABLE novels ADD COLUMN file_name TEXT")
                    run_query(conn, alter_sql)
                    logger.info("成功添加file_name列")
                    
                    # 从file_path提取file_name更新现有记录
                    logger.info("更新现有记录...")
                    # Windows路径使用反斜杠，SQLite SUBSTR和INSTR可能会有问题
                    # 使用更通用的方法来提取文件名
                    update_sql = text("""
                        UPDATE novels 
                        SET file_name = SUBSTR(file_path, INSTR(file_path, '\\') + 1) 
                        WHERE file_path IS NOT NULL AND file_name IS NULL AND INSTR(file_path, '\\') > 0
                    """)
                    run_query(conn, update_sql)
                    
                    # 处理使用正斜杠的路径
                    update_sql = text("""
                        UPDATE novels 
                        SET file_name = SUBSTR(file_path, INSTR(file_path, '/') + 1) 
                        WHERE file_path IS NOT NULL AND file_name IS NULL AND INSTR(file_path, '/') > 0
                    """)
                    run_query(conn, update_sql)
                    
                    # 如果file_path本身就是文件名（没有路径分隔符）
                    update_sql = text("""
                        UPDATE novels 
                        SET file_name = file_path 
                        WHERE file_path IS NOT NULL AND file_name IS NULL 
                          AND INSTR(file_path, '\\') = 0 AND INSTR(file_path, '/') = 0
                    """)
                    run_query(conn, update_sql)
                    
                    logger.info("成功更新现有记录")
        
        logger.info("novels表file_name列处理完成")
        return True
    except Exception as e:
        logger.error(f"添加file_name列时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("========== 开始数据库修复 ==========")
    
    # 添加file_name列
    success = add_file_name_column()
    
    if success:
        logger.info("数据库修复成功完成！")
        print("\n✓ 数据库修复成功！novels表已添加file_name列。")
        print("✓ 现在您可以正常使用九猫小说分析系统了。")
    else:
        logger.error("数据库修复失败！")
        print("\n✗ 数据库修复失败，请查看日志获取详细信息。")
        print(f"✗ 日志文件: {log_filename}")
    
    logger.info("========== 数据库修复结束 ==========")

if __name__ == "__main__":
    main() 