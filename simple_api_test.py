"""
简单的API调用测试，不依赖项目代码
"""
import requests
import json
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API密钥
API_KEY = "sk-9ff38bc35eaa44db82c4121bbba4f3b1"

def test_api():
    """测试API调用"""
    # 设置API端点
    endpoint = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # 设置请求体
    payload = {
        "model": "deepseek-r1",
        "input": {
            "prompt": "请分析以下文本的语言风格：\n\n这是一段测试文本，用于验证API调用是否正常工作。",
            "parameters": {
                "max_tokens": 500,
                "temperature": 0.1,
                "top_p": 0.8,
                "top_k": 50,
                "repetition_penalty": 1.1,
                "stop": []
            }
        }
    }
    
    # 发送请求
    logger.info("发送API请求...")
    start_time = time.time()
    
    try:
        for i in range(3):  # 最多重试3次
            try:
                response = requests.post(endpoint, headers=headers, json=payload, timeout=320)
                end_time = time.time()
                logger.info(f"请求耗时: {end_time - start_time:.2f}秒")
                logger.info(f"响应状态码: {response.status_code}")
                logger.info(f"响应内容: {response.text[:500]}...")
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"解析的JSON: {json.dumps(data, indent=2)[:500]}...")
                        if "output" in data and "text" in data["output"]:
                            text = data["output"]["text"]
                            logger.info(f"生成的文本: {text[:200]}...")
                        else:
                            logger.warning("未找到生成的文本")
                    except Exception as e:
                        logger.error(f"解析JSON时出错: {str(e)}")
                    break
                elif response.status_code == 500:
                    logger.error(f"服务器内部错误（500），第{i+1}次重试...")
                    if i == 2:
                        logger.error("多次尝试后仍然失败，请稍后重试。")
                else:
                    logger.error(f"请求失败: {response.text}")
                    break
            except requests.exceptions.Timeout:
                logger.error(f"请求超时，第{i+1}次重试...")
                if i == 2:
                    logger.error("多次超时，请检查网络或稍后重试。")
            except Exception as e:
                logger.error(f"发送请求时出错: {str(e)}")
                break
    except Exception as e:
        logger.error(f"发送请求时出错: {str(e)}")

if __name__ == "__main__":
    test_api()
