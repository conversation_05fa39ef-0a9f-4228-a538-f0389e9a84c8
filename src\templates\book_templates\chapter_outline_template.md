# 章纲分析设定模板

## 整体章节规划

### 章节数量与分布
- **总章节数**：[预计总章节数范围，如80-100章]
- **卷/部划分**：[是否分卷/部，每卷/部章节数]
- **章节密度分布**：[各阶段章节密度，如开端10%，发展70%，高潮15%，结局5%]
- **特殊章节设置**：[序章/尾章/番外的设置与功能]

### 章节长度规划
- **标准章节长度**：[字数范围，如3000-5000字]
- **长度变化规律**：[固定长度/渐进变长/根据内容调整]
- **特殊长度章节**：[特别长/特别短章节的设置原则与位置]
- **长度与节奏关系**：[章节长度如何配合情节节奏]

## 章节类型与功能

### 功能型章节设计
- **推进型章节**：[占比，特点，分布规律]
- **铺垫型章节**：[占比，特点，分布规律]
- **转折型章节**：[占比，特点，分布规律]
- **高潮型章节**：[占比，特点，分布规律]
- **过渡型章节**：[占比，特点，分布规律]
- **回顾型章节**：[占比，特点，分布规律]

### 内容型章节设计
- **行动章节**：[以行动/事件为主的章节设计]
- **对话章节**：[以对话/交流为主的章节设计]
- **心理章节**：[以心理活动为主的章节设计]
- **描写章节**：[以环境/氛围描写为主的章节设计]
- **混合型章节**：[多种元素平衡的章节设计]

## 章节内部结构

### 标准章节结构
- **开头设计**：[直接开始/回顾铺垫/悬念引入/场景描写]
- **中段发展**：[单一发展线/多线并行/波浪式推进]
- **结尾处理**：[自然收束/悬念设置/伏笔埋设/情感共鸣]
- **结构比例**：[开头:中段:结尾的理想比例，如1:3:1]

### 章节节奏控制
- **节奏变化模式**：[递进式/波浪式/平稳式/突变式]
- **紧张度曲线**：[章节内紧张度的变化规律]
- **信息密度控制**：[高密度/中密度/低密度段落的分布]
- **场景转换频率**：[单场景/双场景/多场景章节的设计原则]

## 章节连接技巧

### 章节间过渡设计
- **直接连接**：[时间/空间/情节的直接延续]
- **跳转连接**：[时间跳跃/空间转换/视角切换]
- **并行叙事**：[同一时间不同地点/人物的叙事切换]
- **回环结构**：[首尾呼应/循环往复的结构设计]

### 章节间关系设计
- **因果关系**：[前章结果导致后章发展]
- **并列关系**：[不同角度展现同一事件/主题]
- **递进关系**：[情节/情感/认知的逐步深入]
- **对比关系**：[通过对比强化主题/情感]

## 章节目标与冲突

### 章节目标设计
- **明确目标**：[每章核心目标的设定原则]
- **目标层次**：[表层目标/中层目标/深层目标]
- **目标变化**：[目标如何随情节发展而变化]
- **多重目标**：[不同角色目标的交织与冲突]

### 章节冲突设计
- **冲突类型**：[内心冲突/人际冲突/环境冲突的分配]
- **冲突强度**：[低/中/高强度冲突的分布规律]
- **冲突解决度**：[完全解决/部分解决/悬而未决的比例]
- **冲突升级模式**：[如何通过章节设计实现冲突逐步升级]

## 章节叙事策略

### 章节视角设计
- **视角选择**：[第一人称/第三人称限制/第三人称全知]
- **视角转换**：[固定视角/章节转换/章内转换]
- **多视角策略**：[多视角章节的安排原则]
- **视角深度**：[表层观察/中度渗透/深度沉浸]

### 章节时间处理
- **叙事时间**：[当前进行/回忆过去/预示未来]
- **时间跨度**：[短时间(数小时内)/中等(数天)/长时间(数月以上)]
- **时间流速**：[真实时间/加速/减速/静止]
- **时序变化**：[顺序/倒叙/插叙/平行时间]

## 章节情感设计

### 情感基调设置
- **主导情感**：[每章的主导情感设定]
- **情感变化**：[单一情感/情感转变/情感复杂化]
- **情感强度**：[低/中/高强度情感的分布]
- **情感对比**：[章节间情感对比的设计原则]

### 情感与情节配合
- **情感铺垫**：[情感如何为情节发展做铺垫]
- **情感高潮**：[情感高潮与情节高潮的配合]
- **情感余韵**：[章节结束后的情感延续效果]
- **读者情感引导**：[如何通过章节设计引导读者情感]

## 章节悬念与伏笔

### 悬念设计
- **悬念类型**：[事件悬念/命运悬念/身份悬念/关系悬念]
- **悬念层次**：[主要悬念/次要悬念/微小悬念]
- **悬念解答时机**：[短期(1-3章)/中期(4-10章)/长期(10章以上)]
- **悬念链设计**：[悬念如何首尾相连形成链条]

### 伏笔布置
- **伏笔类型**：[情节伏笔/人物伏笔/环境伏笔/对话伏笔]
- **伏笔密度**：[每章伏笔数量控制]
- **伏笔回收周期**：[短期/中期/长期伏笔的比例]
- **伏笔显著度**：[明显伏笔/半隐藏伏笔/深度隐藏伏笔]

## 章节主题与象征

### 章节主题设计
- **主题分配**：[各章如何分担展现整体主题]
- **主题变奏**：[同一主题的不同角度展现]
- **主题深化**：[主题如何通过章节逐步深化]
- **主题对比**：[通过章节对比强化主题]

### 章节象征系统
- **核心象征**：[贯穿全书的核心象征在各章的呈现]
- **章节特有象征**：[特定章节的独特象征设计]
- **象征变化**：[象征意义如何随情节发展而变化]
- **象征密度**：[不同阶段象征元素的密度控制]

## 章节标题设计

### 标题命名策略
- **标题类型**：[描述型/暗示型/象征型/引用型]
- **标题长度**：[短标题(1-3字)/中等(4-7字)/长标题(8字以上)]
- **标题风格**：[统一风格/多样化风格]
- **标题与内容关系**：[直接相关/间接暗示/反向对比]

### 标题系统设计
- **标题编号**：[数字编号/特殊编号/无编号]
- **标题序列**：[是否形成有意义的序列]
- **标题呼应**：[标题间的呼应与联系]
- **标题预示**：[标题如何预示章节内容与走向]

## 章节阅读体验

### 读者期待管理
- **期待建立**：[如何通过章节设计建立读者期待]
- **期待满足**：[满足/超越/颠覆期待的比例]
- **期待延迟**：[延迟满足的策略与技巧]
- **新期待创造**：[满足旧期待同时创造新期待]

### 章节钩子设计
- **开头钩子**：[吸引读者继续阅读的开头设计]
- **中段钩子**：[维持阅读兴趣的中段设计]
- **结尾钩子**：[促使读者阅读下一章的结尾设计]
- **钩子类型变化**：[不同类型钩子的交替使用]

---
© 2025 九猫写作系统 - 设定模板
