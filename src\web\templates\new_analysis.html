{% extends "new_base.html" %}

{% block title %}{{ novel.title }} - {{ dimension_name }} 分析 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-0">{{ dimension_name }}分析</h1>
        <p class="text-muted">
            小说: <a href="{{ url_for('new.view_novel', novel_id=novel.id) }}">{{ novel.title }}</a> |
            分析时间: {{ result.created_at.strftime('%Y-%m-%d %H:%M') }}
        </p>
    </div>
    <div class="col-md-4 text-md-end">
        <a href="{{ url_for('new.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回小说
        </a>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-file-export me-2"></i>导出
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="#">
                    <i class="fas fa-file-pdf me-2"></i>导出为PDF
                </a></li>
                <li><a class="dropdown-item" href="#">
                    <i class="fas fa-file-word me-2"></i>导出为Word文档
                </a></li>
                <li><a class="dropdown-item" href="#">
                    <i class="fas fa-file-alt me-2"></i>导出为Markdown
                </a></li>
            </ul>
        </div>
    </div>
</div>

<div class="row">
    <!-- 主要内容区域 -->
    <div class="col-lg-8">
        <!-- 分析结果卡片 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>分析结果</h3>
                <div>
                    <button class="btn btn-sm btn-outline-primary" id="printBtn">
                        <i class="fas fa-print me-1"></i>打印
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" id="copyBtn">
                        <i class="fas fa-copy me-1"></i>复制
                    </button>
                    <button class="btn btn-sm btn-outline-warning" id="reanalyzeBtn">
                        <i class="fas fa-sync-alt me-1"></i>重新分析
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="markdown-content" id="analysisContent">
                    {{ result.content|safe }}
                </div>
            </div>
        </div>

        <!-- 推理过程卡片 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title mb-0"><i class="fas fa-brain me-2"></i>推理过程</h3>
                <button class="btn btn-sm btn-outline-primary" id="toggleReasoningBtn">
                    <i class="fas fa-expand-alt me-1"></i>展开/收起
                </button>
            </div>
            <div class="card-body">
                <div id="reasoningContentCollapsed" class="reasoning-content-preview">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>点击上方的"展开/收起"按钮查看完整的AI推理过程。
                    </div>
                </div>
                <div id="reasoningContentFull" class="reasoning-content-full" style="display: none;">
                    {% if result.reasoning_content %}
                        <pre class="reasoning-text">{{ result.reasoning_content }}</pre>
                    {% elif result.analysis_metadata and result.analysis_metadata.reasoning_content %}
                        <pre class="reasoning-text">{{ result.analysis_metadata.reasoning_content }}</pre>
                    {% elif result.metadata and result.metadata.reasoning_content %}
                        <pre class="reasoning-text">{{ result.metadata.reasoning_content }}</pre>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-circle me-2"></i>未找到推理过程数据。
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 小说信息卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-book me-2"></i>小说信息</h3>
            </div>
            <div class="card-body">
                <p><strong>标题：</strong> {{ novel.title }}</p>
                <p><strong>作者：</strong> {{ novel.author or '未知' }}</p>
                <p><strong>字数：</strong> {{ novel.word_count or '未知' }}</p>
                <p><strong>上传时间：</strong> {{ novel.created_at.strftime('%Y-%m-%d') }}</p>
            </div>
        </div>

        <!-- 其他维度卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-cubes me-2"></i>其他维度</h3>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for dimension_key, dimension_name in dimensions.items() %}
                        {% if dimension_key != result.dimension %}
                            {% if dimension_key in available_dimensions %}
                                <a href="{{ url_for('analysis', novel_id=novel.id, dimension=dimension_key) }}" class="list-group-item list-group-item-action">
                                    {{ dimension_name }}
                                </a>
                            {% else %}
                                <button class="list-group-item list-group-item-action disabled" disabled>
                                    {{ dimension_name }} <span class="badge bg-secondary float-end">未分析</span>
                                </button>
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- 分析元数据卡片 -->
        {% if result.metadata %}
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-info-circle me-2"></i>分析元数据</h3>
            </div>
            <div class="card-body">
                <ul class="list-group">
                    {% if result.metadata.processing_time %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        处理时间
                        <span class="badge bg-primary rounded-pill">{{ result.metadata.processing_time }}秒</span>
                    </li>
                    {% endif %}

                    {% if result.metadata.tokens_used %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        使用令牌数
                        <span class="badge bg-primary rounded-pill">{{ result.metadata.tokens_used }}</span>
                    </li>
                    {% endif %}

                    {% if result.metadata.model %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        使用模型
                        <span class="badge bg-primary rounded-pill">{{ result.metadata.model }}</span>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 推理过程展开/收起
        const toggleReasoningBtn = document.getElementById('toggleReasoningBtn');
        const reasoningContentCollapsed = document.getElementById('reasoningContentCollapsed');
        const reasoningContentFull = document.getElementById('reasoningContentFull');

        toggleReasoningBtn.addEventListener('click', function() {
            if (reasoningContentFull.style.display === 'none') {
                reasoningContentCollapsed.style.display = 'none';
                reasoningContentFull.style.display = 'block';
                toggleReasoningBtn.innerHTML = '<i class="fas fa-compress-alt me-1"></i>收起';
            } else {
                reasoningContentCollapsed.style.display = 'block';
                reasoningContentFull.style.display = 'none';
                toggleReasoningBtn.innerHTML = '<i class="fas fa-expand-alt me-1"></i>展开/收起';
            }
        });

        // 打印功能
        const printBtn = document.getElementById('printBtn');
        printBtn.addEventListener('click', function() {
            const printContent = document.getElementById('analysisContent').innerHTML;
            const printWindow = window.open('', '_blank');

            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>{{ novel.title }} - {{ dimension_name }} 分析</title>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }
                        h1, h2, h3, h4, h5, h6 { margin-top: 1.5rem; margin-bottom: 1rem; }
                        h1 { font-size: 1.8rem; border-bottom: 2px solid #ddd; padding-bottom: 0.5rem; }
                        h2 { font-size: 1.5rem; border-bottom: 1px solid #ddd; padding-bottom: 0.3rem; }
                        p { margin-bottom: 1rem; }
                        ul, ol { margin-bottom: 1rem; padding-left: 2rem; }
                        blockquote { border-left: 4px solid #007bff; padding-left: 1rem; margin-left: 0; color: #6c757d; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .footer { text-align: center; margin-top: 30px; font-size: 0.9em; color: #6c757d; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>{{ novel.title }}</h1>
                        <h2>{{ dimension_name }}分析</h2>
                        <p>分析时间: {{ result.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                    <div class="content">
                        ${printContent}
                    </div>
                    <div class="footer">
                        <p>由九猫小说分析系统生成</p>
                    </div>
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.focus();

            // 等待内容加载完成后打印
            setTimeout(function() {
                printWindow.print();
                printWindow.close();
            }, 500);
        });

        // 复制功能
        const copyBtn = document.getElementById('copyBtn');
        copyBtn.addEventListener('click', function() {
            const analysisContent = document.getElementById('analysisContent');

            // 创建一个临时元素来存储纯文本内容
            const tempElement = document.createElement('div');
            tempElement.innerHTML = analysisContent.innerHTML;

            // 获取纯文本内容
            const textContent = tempElement.textContent;

            // 使用Clipboard API复制文本
            navigator.clipboard.writeText(textContent)
                .then(() => {
                    // 显示成功提示
                    copyBtn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
                    copyBtn.classList.remove('btn-outline-secondary');
                    copyBtn.classList.add('btn-success');

                    // 2秒后恢复按钮状态
                    setTimeout(() => {
                        copyBtn.innerHTML = '<i class="fas fa-copy me-1"></i>复制';
                        copyBtn.classList.remove('btn-success');
                        copyBtn.classList.add('btn-outline-secondary');
                    }, 2000);
                })
                .catch(err => {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动选择文本并复制');
                });
        });

        // 重新分析功能
        const reanalyzeBtn = document.getElementById('reanalyzeBtn');
        reanalyzeBtn.addEventListener('click', function() {
            if (confirm('确定要重新分析吗？这将删除现有的分析结果并重新生成。')) {
                // 显示加载状态
                reanalyzeBtn.disabled = true;
                reanalyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>分析中...';

                // 发送分析请求
                fetch('/api/novel/{{ novel.id }}/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        dimensions: ['{{ result.dimension }}']
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示成功提示
                        reanalyzeBtn.innerHTML = '<i class="fas fa-check me-1"></i>分析已开始';
                        reanalyzeBtn.classList.remove('btn-outline-warning');
                        reanalyzeBtn.classList.add('btn-success');

                        // 创建进度检查函数
                        const checkProgress = () => {
                            fetch(`/api/novel/{{ novel.id }}/analysis/{{ result.dimension }}/progress`)
                                .then(response => response.json())
                                .then(progressData => {
                                    if (progressData.success) {
                                        if (progressData.status === 'completed') {
                                            // 分析完成，刷新页面
                                            window.location.reload();
                                        } else {
                                            // 继续检查进度
                                            setTimeout(checkProgress, 2000);
                                        }
                                    }
                                })
                                .catch(error => {
                                    console.error('检查进度出错:', error);
                                });
                        };

                        // 开始检查进度
                        setTimeout(checkProgress, 2000);
                    } else {
                        // 显示错误提示
                        reanalyzeBtn.innerHTML = '<i class="fas fa-exclamation-circle me-1"></i>分析失败';
                        reanalyzeBtn.classList.remove('btn-outline-warning');
                        reanalyzeBtn.classList.add('btn-danger');
                        alert('启动分析失败: ' + data.error);

                        // 2秒后恢复按钮状态
                        setTimeout(() => {
                            reanalyzeBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>重新分析';
                            reanalyzeBtn.classList.remove('btn-danger');
                            reanalyzeBtn.classList.add('btn-outline-warning');
                            reanalyzeBtn.disabled = false;
                        }, 2000);
                    }
                })
                .catch(error => {
                    console.error('启动分析出错:', error);
                    alert('启动分析时出错，请查看控制台了解详情');

                    // 恢复按钮状态
                    reanalyzeBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>重新分析';
                    reanalyzeBtn.disabled = false;
                });
            }
        });
    });
</script>
{% endblock %}
