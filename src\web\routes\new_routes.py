"""
新版九猫小说分析系统路由
"""
import os
import json
import logging
import time
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.api_log import ApiLog
from src.db.connection import Session
from src.api.deepseek_client import DeepSeekClient
from src.api.analysis import NovelAnalyzer
import config

# 创建蓝图
new_bp = Blueprint('new', __name__)
logger = logging.getLogger(__name__)

# 分析维度配置
ANALYSIS_DIMENSIONS = {
    "language_style": "语言风格",
    "rhythm_pacing": "节奏与节奏",
    "structure": "结构分析",
    "sentence_variation": "句式变化",
    "paragraph_length": "段落长度",
    "perspective_shifts": "视角转换",
    "paragraph_flow": "段落流畅度",
    "novel_characteristics": "小说特点",
    "world_building": "世界构建",
    "chapter_outline": "章节大纲",
    "character_relationships": "人物关系",
    "opening_effectiveness": "开篇效果",
    "climax_pacing": "高潮节奏"
}

@new_bp.route('/')
def index():
    """新版首页"""
    try:
        session = Session()
        try:
            # 获取最近的小说
            novels = session.query(Novel).order_by(Novel.created_at.desc()).limit(5).all()
            return render_template('new_index.html', novels=novels)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载首页时出错: {str(e)}", exc_info=True)
        return render_template('new_index.html', novels=[])

@new_bp.route('/novels')
def novels():
    """小说列表页面"""
    try:
        session = Session()
        try:
            # 获取所有小说
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()
            return render_template('new_novels.html', novels=novels)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载小说列表页面时出错: {str(e)}", exc_info=True)
        return render_template('new_novels.html', novels=[])

@new_bp.route('/upload', methods=['GET', 'POST'])
def upload_novel():
    """上传小说页面"""
    if request.method == 'GET':
        return render_template('new_upload.html')

    try:
        title = request.form.get('title', '未命名小说')
        author = request.form.get('author', '')
        content = ''

        # 处理文件上传
        if 'file' in request.files and request.form.get('uploadType') == 'file':
            file = request.files['file']
            if file.filename:
                content = file.read().decode('utf-8', errors='ignore')

        # 处理文本粘贴
        elif 'content' in request.form and request.form.get('uploadType') == 'text':
            content = request.form['content']

        if not content:
            return render_template('new_upload.html', error="请上传文件或粘贴文本内容")

        # 保存到数据库
        session = Session()
        try:
            novel = Novel(
                title=title,
                author=author,
                content=content
            )

            # 字数会在Novel的__init__方法中自动计算
            session.add(novel)
            session.commit()

            # 检查是否需要自动分析
            auto_analyze = request.form.get('autoAnalyze') == 'on'
            if auto_analyze:
                # 获取选中的分析维度
                dimensions = request.form.getlist('dimensions')
                if dimensions:
                    # 启动分析
                    # 导入单维度分析模块
                    from src.api.analyze_dimension import analyze_dimension

                    # 逐个维度分析
                    for dimension in dimensions:
                        analyze_dimension(novel, dimension)

                    # 获取已完成的维度数量
                    completed_dimensions_count = len(dimensions)

                    # 获取总维度数量
                    total_dimensions_count = len(ANALYSIS_DIMENSIONS)

                    # 只有当所有维度都分析完成时，才将is_analyzed设置为True
                    if completed_dimensions_count == total_dimensions_count:
                        novel.is_analyzed = True
                        session.commit()

                    flash(f"小说《{title}》已上传并开始分析", "success")
                else:
                    flash(f"小说《{title}》已上传，但未选择分析维度", "warning")
            else:
                flash(f"小说《{title}》已上传", "success")

            # 重定向到小说详情页
            return redirect(url_for('new.view_novel', novel_id=novel.id))
        finally:
            session.close()
    except Exception as e:
        logger.error(f"上传小说时出错: {str(e)}", exc_info=True)
        return render_template('new_upload.html', error=f"上传失败: {str(e)}")

@new_bp.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    """查看小说详情页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('new.novels'))

            # 获取分析结果
            analysis_results = {}
            available_dimensions = []

            results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            for result in results:
                analysis_results[result.dimension] = result
                available_dimensions.append(result.dimension)

            # 计算缺失的维度
            missing_dimensions = [dim for dim in ANALYSIS_DIMENSIONS.keys() if dim not in available_dimensions]

            # 获取已完成的维度数量
            completed_dimensions_count = len(analysis_results)

            # 获取总维度数量
            total_dimensions_count = len(ANALYSIS_DIMENSIONS)

            # 更新小说的分析状态
            if completed_dimensions_count == total_dimensions_count and not novel.is_analyzed:
                novel.is_analyzed = True
                session.commit()
            elif completed_dimensions_count < total_dimensions_count and novel.is_analyzed:
                novel.is_analyzed = False
                session.commit()

            return render_template(
                'new_novel.html',
                novel=novel,
                analysis_results=analysis_results,
                missing_dimensions=missing_dimensions,
                completed_dimensions_count=completed_dimensions_count,
                total_dimensions_count=total_dimensions_count
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看小说详情页面时出错: {str(e)}", exc_info=True)
        flash(f"加载小说详情失败: {str(e)}", "danger")
        return redirect(url_for('new.novels'))

@new_bp.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    """查看分析结果页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('new.novels'))

            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                flash(f"未找到{ANALYSIS_DIMENSIONS.get(dimension, dimension)}的分析结果", "warning")
                return redirect(url_for('new.view_novel', novel_id=novel_id))

            # 检查是否有推理过程
            try:
                has_reasoning = False
                # 首先检查分析元数据中是否包含推理过程
                if result.analysis_metadata and isinstance(result.analysis_metadata, dict) and 'reasoning_content' in result.analysis_metadata:
                    # 将推理过程添加到结果对象，方便模板访问
                    result.reasoning_content = result.analysis_metadata['reasoning_content']
                    has_reasoning = True
                    logger.info(f"成功从analysis_metadata获取分析结果 {result.id} 的推理过程数据")
                # 兼容旧版API，检查metadata属性
                elif hasattr(result, 'metadata') and result.metadata and isinstance(result.metadata, dict) and 'reasoning_content' in result.metadata:
                    result.reasoning_content = result.metadata['reasoning_content']
                    has_reasoning = True
                    logger.info(f"成功从metadata获取分析结果 {result.id} 的推理过程数据")
                # 检查日志中是否包含推理过程
                elif result.analysis_logs and isinstance(result.analysis_logs, list):
                    for log in result.analysis_logs:
                        if isinstance(log, dict) and 'message' in log and ('推理过程' in log['message'] or 'reasoning' in log['message'].lower()):
                            result.reasoning_content = log['message']
                            has_reasoning = True
                            logger.info(f"成功从analysis_logs获取分析结果 {result.id} 的推理过程数据")
                            break

                if not has_reasoning:
                    # 如果没有推理过程，使用默认内容
                    result.reasoning_content = f"# {dimension}分析推理过程\n\nAPI未返回推理过程数据，这可能是因为使用了缓存结果或分析过程中未生成推理内容。"
                    logger.warning(f"分析结果 {result.id} 没有推理过程数据，使用默认内容")
            except Exception as e:
                # 如果出错，使用默认内容
                result.reasoning_content = f"# {dimension}分析推理过程\n\n获取推理过程时出错: {str(e)}"
                logger.error(f"获取分析结果 {result.id} 的推理过程数据时出错: {str(e)}", exc_info=True)

            # 获取可用的维度
            available_dimensions = []
            results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            for r in results:
                available_dimensions.append(r.dimension)

            return render_template(
                'new_analysis.html',
                novel=novel,
                result=result,
                dimension_name=ANALYSIS_DIMENSIONS.get(dimension, dimension),
                dimensions=ANALYSIS_DIMENSIONS,
                available_dimensions=available_dimensions
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看分析结果页面时出错: {str(e)}", exc_info=True)
        flash(f"加载分析结果失败: {str(e)}", "danger")
        return redirect(url_for('new.view_novel', novel_id=novel_id))

@new_bp.route('/novel/<int:novel_id>/delete', methods=['POST'])
def delete_novel(novel_id):
    """删除小说"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('new.novels'))

            # 删除小说及相关数据
            session.delete(novel)
            session.commit()

            flash(f"小说《{novel.title}》已删除", "success")
            return redirect(url_for('new.novels'))
        finally:
            session.close()
    except Exception as e:
        logger.error(f"删除小说时出错: {str(e)}", exc_info=True)
        flash(f"删除小说失败: {str(e)}", "danger")
        return redirect(url_for('new.view_novel', novel_id=novel_id))

@new_bp.route('/system_monitor')
def system_monitor():
    """系统监控页面"""
    return render_template('new_system_monitor.html')

@new_bp.route('/dimensions')
def dimensions():
    """分析维度介绍页面"""
    return render_template('new_dimensions.html', dimensions=ANALYSIS_DIMENSIONS)

@new_bp.route('/help')
def help():
    """帮助中心页面"""
    return render_template('new_help.html')

@new_bp.route('/novel/<int:novel_id>/chapters')
def list_chapters(novel_id):
    """章节列表页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('new.novels'))

            # 模拟章节数据 - 实际应用中应从数据库获取
            chapters = []
            # 根据小说内容自动分割章节
            import re
            chapter_pattern = re.compile(r'第[一二三四五六七八九十百千万\d]+章|第[一二三四五六七八九十百千万\d]+回|Chapter\s*\d+', re.IGNORECASE)
            content = novel.content

            # 查找所有章节标题
            matches = list(chapter_pattern.finditer(content))

            if matches:
                for i, match in enumerate(matches):
                    start_pos = match.start()
                    end_pos = matches[i+1].start() if i < len(matches) - 1 else len(content)

                    # 提取章节标题和内容
                    title = content[start_pos:start_pos + 50].split('\n')[0].strip()
                    chapter_content = content[start_pos:end_pos].strip()

                    chapters.append({
                        'id': i + 1,
                        'title': title,
                        'content': chapter_content,
                        'word_count': len(chapter_content)
                    })
            else:
                # 如果没有找到章节标记，按照固定长度分割
                chunk_size = 5000  # 每章约5000字
                total_chunks = (len(content) + chunk_size - 1) // chunk_size

                for i in range(total_chunks):
                    start_pos = i * chunk_size
                    end_pos = min((i + 1) * chunk_size, len(content))

                    chapters.append({
                        'id': i + 1,
                        'title': f"第{i+1}章",
                        'content': content[start_pos:end_pos].strip(),
                        'word_count': end_pos - start_pos
                    })

            return render_template('new_chapters.html', novel=novel, chapters=chapters)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节列表页面时出错: {str(e)}", exc_info=True)
        flash(f"加载章节列表失败: {str(e)}", "danger")
        return redirect(url_for('new.view_novel', novel_id=novel_id))

@new_bp.route('/api/system/resources')
def api_system_resources():
    """获取系统资源使用情况"""
    try:
        import psutil

        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.5)

        # 内存使用情况
        memory = psutil.virtual_memory()

        # 磁盘使用情况
        disk = psutil.disk_usage('/')

        # 网络使用情况
        net_io_counters = psutil.net_io_counters()

        # 等待一秒，再次获取网络数据以计算速率
        time.sleep(1)
        net_io_counters_new = psutil.net_io_counters()

        bytes_sent_per_second = net_io_counters_new.bytes_sent - net_io_counters.bytes_sent
        bytes_recv_per_second = net_io_counters_new.bytes_recv - net_io_counters.bytes_recv

        return jsonify({
            'success': True,
            'cpu': {
                'percent': cpu_percent
            },
            'memory': {
                'total': memory.total,
                'used': memory.used,
                'free': memory.available,
                'percent': memory.percent
            },
            'disk': {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': disk.percent
            },
            'network': {
                'bytes_sent': net_io_counters_new.bytes_sent,
                'bytes_recv': net_io_counters_new.bytes_recv,
                'bytes_sent_per_second': bytes_sent_per_second,
                'bytes_recv_per_second': bytes_recv_per_second,
                'total_per_second': bytes_sent_per_second + bytes_recv_per_second
            }
        })
    except ImportError:
        return jsonify({
            'success': False,
            'error': 'psutil模块未安装，无法获取系统资源信息'
        })
    except Exception as e:
        logger.error(f"获取系统资源信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

@new_bp.route('/api/system/api_stats')
def api_system_api_stats():
    """获取API调用统计"""
    try:
        session = Session()
        try:
            # 获取总调用次数
            total_calls = session.query(func.count(ApiLog.id)).scalar() or 0

            # 获取今日调用次数
            today = datetime.now().date()
            today_calls = session.query(func.count(ApiLog.id)).filter(
                func.date(ApiLog.timestamp) == today
            ).scalar() or 0

            # 获取本小时调用次数
            hour_start = datetime.now().replace(minute=0, second=0, microsecond=0)
            hour_calls = session.query(func.count(ApiLog.id)).filter(
                ApiLog.timestamp >= hour_start
            ).scalar() or 0

            # 按维度统计
            dimension_stats = []
            dimensions = session.query(ApiLog.analysis_type).distinct().all()

            for dimension in dimensions:
                if not dimension[0]:
                    continue

                count = session.query(func.count(ApiLog.id)).filter(
                    ApiLog.analysis_type == dimension[0]
                ).scalar() or 0

                # 使用0作为平均处理时间，因为ApiLog模型可能没有processing_time属性
                avg_time = 0

                dimension_stats.append({
                    'dimension': dimension[0],
                    'count': count,
                    'avg_time': avg_time
                })

            # 按调用次数排序
            dimension_stats.sort(key=lambda x: x['count'], reverse=True)

            return jsonify({
                'success': True,
                'total_calls': total_calls,
                'today_calls': today_calls,
                'today_limit': config.API_CALL_LIMIT_PER_DAY if hasattr(config, 'API_CALL_LIMIT_PER_DAY') else 1000,
                'hour_calls': hour_calls,
                'hour_limit': config.API_CALL_LIMIT_PER_HOUR if hasattr(config, 'API_CALL_LIMIT_PER_HOUR') else 100,
                'by_dimension': dimension_stats
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取API调用统计时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 分析控制台路由
@new_bp.route('/novel/<int:novel_id>/console')
def analysis_console(novel_id):
    """分析控制台页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('new.novels'))

            # 获取分析结果
            analysis_results = {}
            available_dimensions = []

            results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            for result in results:
                analysis_results[result.dimension] = result
                available_dimensions.append(result.dimension)

            return render_template(
                'new_analysis_console.html',
                novel=novel,
                analysis_results=analysis_results,
                available_dimensions=available_dimensions,
                dimensions=ANALYSIS_DIMENSIONS
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看分析控制台页面时出错: {str(e)}", exc_info=True)
        flash(f"加载分析控制台失败: {str(e)}", "danger")
        return redirect(url_for('new.view_novel', novel_id=novel_id))

# 章节分析进度API路由
@new_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/progress')
def api_chapter_analysis_progress(novel_id, chapter_id):
    """获取章节分析进度"""
    try:
        # 使用实际的小说ID和章节ID组合
        temp_novel_id = novel_id

        # 查询分析结果
        session = Session()
        try:
            # 检查是否有分析结果
            results = session.query(AnalysisResult).filter_by(novel_id=temp_novel_id).all()

            if not results:
                # 没有结果，可能尚未开始分析
                return jsonify({
                    'success': True,
                    'status': 'not_started',
                    'progress': 0,
                    'message': '分析尚未开始'
                })

            # 计算进度
            # 假设每个维度权重相同，计算已完成的维度比例
            total_dimensions = 12  # 总共12个维度
            completed_dimensions = len(results)
            progress = int((completed_dimensions / total_dimensions) * 100)

            # 确定状态
            if progress >= 100:
                status = 'completed'
                message = '分析已完成'
            else:
                status = 'in_progress'
                message = f'已完成 {completed_dimensions}/{total_dimensions} 个维度'

            # 估算剩余时间（假设每个维度需要30秒）
            remaining_dimensions = total_dimensions - completed_dimensions
            estimated_seconds = remaining_dimensions * 30

            if estimated_seconds > 60:
                estimated_time = f"{estimated_seconds // 60}分{estimated_seconds % 60}秒"
            else:
                estimated_time = f"{estimated_seconds}秒"

            return jsonify({
                'success': True,
                'status': status,
                'progress': progress,
                'message': message,
                'completed_dimensions': completed_dimensions,
                'total_dimensions': total_dimensions,
                'estimated_time': estimated_time
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析进度时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 章节分析API路由
@new_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze', methods=['POST'])
def api_analyze_chapter(novel_id, chapter_id):
    """分析章节"""
    try:
        # 获取请求数据
        data = request.json or {}
        dimensions = data.get('dimensions', [])

        if not dimensions:
            # 如果没有指定维度，使用所有维度
            dimensions = [
                'language_style', 'rhythm_pacing', 'structure', 'sentence_variation',
                'paragraph_length', 'perspective_shifts', 'paragraph_flow',
                'novel_characteristics', 'world_building', 'character_relationships',
                'opening_effectiveness', 'climax_pacing'
            ]

        # 获取小说和章节
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 从小说内容中提取章节内容
            import re
            chapter_pattern = re.compile(r'第[一二三四五六七八九十百千万\d]+章|第[一二三四五六七八九十百千万\d]+回|Chapter\s*\d+', re.IGNORECASE)
            content = novel.content

            # 查找所有章节标题
            matches = list(chapter_pattern.finditer(content))

            chapter_content = ""
            chapter_title = ""

            if matches and 1 <= chapter_id <= len(matches):
                i = chapter_id - 1
                start_pos = matches[i].start()
                end_pos = matches[i+1].start() if i < len(matches) - 1 else len(content)

                # 提取章节标题和内容
                chapter_title = content[start_pos:start_pos + 50].split('\n')[0].strip()
                chapter_content = content[start_pos:end_pos].strip()
            else:
                # 如果没有找到章节标记或章节ID无效，返回错误
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 在后台线程中启动分析
            import threading
            analysis_thread = threading.Thread(
                target=_analyze_chapter_in_background,
                args=(novel_id, chapter_id, chapter_title, chapter_content, dimensions)
            )
            analysis_thread.daemon = True
            analysis_thread.start()

            return jsonify({
                'success': True,
                'message': '章节分析已开始',
                'chapter_id': chapter_id,
                'chapter_title': chapter_title,
                'dimensions': dimensions
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"启动章节分析时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def _analyze_chapter_in_background(novel_id, chapter_id, chapter_title, chapter_content, dimensions):
    """在后台线程中分析章节"""
    try:
        logger.info(f"开始分析小说 {novel_id} 的章节 {chapter_id}: {chapter_title}")

        # 导入分析模块
        from src.api.analyze_dimension import analyze_dimension
        from src.api.analysis import NovelAnalyzer
        from src.api.deepseek_client import DeepSeekClient

        # 创建一个临时Novel对象用于分析
        from src.models.novel import Novel
        temp_novel = Novel(
            title=f"章节分析: {chapter_title}",
            content=chapter_content
        )
        temp_novel.id = novel_id  # 使用实际的小说ID

        # 将临时小说对象保存到数据库，这样后续查询可以找到它
        session = Session()
        try:
            # 检查是否已存在，如果存在则先删除
            existing_novel = session.query(Novel).filter_by(id=temp_novel.id).first()
            if existing_novel:
                logger.info(f"删除已存在的临时小说 ID: {temp_novel.id}")
                session.delete(existing_novel)
                session.commit()

            # 添加新的临时小说对象
            session.add(temp_novel)
            session.commit()
            logger.info(f"已将临时小说保存到数据库，ID: {temp_novel.id}")
        finally:
            session.close()

        # 创建真实的API客户端和分析器
        api_client = DeepSeekClient()
        analyzer = NovelAnalyzer(api_client=api_client)

        # 为每个维度创建单独的会话和分析
        for dimension in dimensions:
            try:
                logger.info(f"分析章节 {chapter_id} 的维度: {dimension}")

                # 使用真实API分析 - 与小说整体分析使用相同的API调用
                # 首先检查是否有已有分析结果
                session = Session()
                try:
                    existing_result = session.query(AnalysisResult).filter_by(
                        novel_id=temp_novel.id,
                        dimension=dimension
                    ).first()

                    if existing_result:
                        # 删除已有结果，重新分析
                        session.delete(existing_result)
                        session.commit()
                except Exception as e:
                    logger.error(f"检查或删除现有分析结果时出错: {str(e)}", exc_info=True)
                finally:
                    session.close()

                # 使用带API客户端的分析函数进行分析
                result = analyze_dimension(temp_novel, dimension, analyzer=analyzer, use_real_api=True)

                if result:
                    logger.info(f"章节 {chapter_id} 的维度 {dimension} 分析完成")
                else:
                    logger.error(f"章节 {chapter_id} 的维度 {dimension} 分析失败")
            except Exception as e:
                logger.error(f"分析章节 {chapter_id} 的维度 {dimension} 时出错: {str(e)}", exc_info=True)

        # 更新原小说的分析状态
        session = Session()
        try:
            # 获取原小说并更新状态
            novel = session.query(Novel).get(novel_id)
            if novel:
                novel.is_analyzed = True
                session.commit()
                logger.info(f"小说 {novel_id} 的分析状态已更新为已分析")
        except Exception as e:
            logger.error(f"更新小说 {novel_id} 的分析状态时出错: {str(e)}", exc_info=True)
        finally:
            session.close()

        logger.info(f"章节 {chapter_id} 的所有维度分析已完成")
    except Exception as e:
        logger.error(f"后台分析章节时出错: {str(e)}", exc_info=True)

def register_routes(app):
    """注册路由到应用"""
    app.register_blueprint(new_bp, url_prefix='/new')
