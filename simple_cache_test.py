"""
简单测试九猫系统的缓存功能
"""
import os
import sys
import time
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

import config
from src.models.novel import Novel
from src.api.analysis import NovelAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("simple_cache_test")

# 启用调试模式，避免实际调用API
config.DEBUG = True

# 创建测试小说内容
test_content = "这是一个测试小说，用于测试九猫系统的缓存功能。\n" * 1000

# 创建测试小说对象
test_novel = Novel(
    title="缓存测试小说",
    content=test_content,
    author="测试作者"
)
test_novel.id = 999  # 手动设置ID

# 创建分析器
analyzer = NovelAnalyzer()

# 测试维度
test_dimensions = ["language_style"]

# 第一次分析：不使用缓存
print("第一次分析：不使用缓存")
config.CACHE_ENABLED = False
start_time = time.time()
result1 = analyzer.analyze_novel(test_novel, test_dimensions)
time1 = time.time() - start_time
print(f"第一次分析完成，耗时: {time1:.2f}秒")

# 第二次分析：使用缓存
print("第二次分析：启用缓存")
config.CACHE_ENABLED = True
config.FORCE_REFRESH_CACHE = False
start_time = time.time()
result2 = analyzer.analyze_novel(test_novel, test_dimensions)
time2 = time.time() - start_time
print(f"第二次分析完成，耗时: {time2:.2f}秒")

# 输出结果摘要
print("\n测试结果摘要:")
print(f"无缓存分析耗时: {time1:.2f}秒")
print(f"使用缓存分析耗时: {time2:.2f}秒")
if time2 > 0:
    print(f"缓存加速比: {time1/time2:.2f}x")
else:
    print("缓存加速比: 无法计算（分母为0）")
