@echo off
chcp 65001 > nul
echo ===================================
echo   九猫系统完全重启工具
echo ===================================
echo.

echo 正在关闭所有Chrome进程...
taskkill /F /IM chrome.exe /T > nul 2>&1
if %errorlevel% equ 0 (
    echo Chrome已关闭
) else (
    echo Chrome未运行或无法关闭
)

echo 正在关闭所有Python进程...
taskkill /F /IM python.exe /T > nul 2>&1
if %errorlevel% equ 0 (
    echo Python进程已关闭
) else (
    echo Python进程未运行或无法关闭
)

echo 正在清除DNS缓存...
ipconfig /flushdns > nul
echo DNS缓存已清除

echo 正在清除网络连接...
netsh winsock reset > nul
echo 网络连接已重置

echo 正在等待5秒...
timeout /t 5 /nobreak > nul

echo 正在启动九猫服务器...
start /B cmd /c "python main.py"

echo 正在等待10秒，确保服务器启动...
timeout /t 10 /nobreak > nul

echo 正在打开浏览器...
start "" http://localhost:5001

echo 完成！
echo.
echo 如果仍然无法访问，请尝试以下操作：
echo 1. 重启计算机
echo 2. 检查防火墙设置
echo 3. 尝试使用不同的浏览器
echo 4. 检查端口5001是否被其他程序占用
echo.
pause
