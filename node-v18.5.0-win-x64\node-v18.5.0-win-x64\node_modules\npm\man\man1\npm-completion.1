.TH "NPM\-COMPLETION" "1" "June 2022" "" ""
.SH "NAME"
\fBnpm-completion\fR \- Tab Completion for npm
.SS Synopsis
.P
.RS 2
.nf
npm completion
.fi
.RE
.P
Note: This command is unaware of workspaces\.
.SS Description
.P
Enables tab\-completion in all npm commands\.
.P
The synopsis above
loads the completions into your current shell\.  Adding it to
your ~/\.bashrc or ~/\.zshrc will make the completions available
everywhere:
.P
.RS 2
.nf
npm completion >> ~/\.bashrc
npm completion >> ~/\.zshrc
.fi
.RE
.P
You may of course also pipe the output of \fBnpm completion\fP to a file
such as \fB/usr/local/etc/bash_completion\.d/npm\fP or 
\fB/etc/bash_completion\.d/npm\fP if you have a system that will read 
that file for you\.
.P
When \fBCOMP_CWORD\fP, \fBCOMP_LINE\fP, and \fBCOMP_POINT\fP are defined in the
environment, \fBnpm completion\fP acts in "plumbing mode", and outputs
completions based on the arguments\.
.SS See Also
.RS 0
.IP \(bu 2
npm help developers
.IP \(bu 2
npm help npm

.RE
