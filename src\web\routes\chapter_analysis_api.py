"""
章节分析API路由
"""
import logging
import traceback
from flask import Blueprint, request, jsonify
from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.services.chapter_analysis_service import ChapterAnalysisService
import config

chapter_analysis_api_bp = Blueprint('chapter_analysis_api', __name__)
logger = logging.getLogger(__name__)

@chapter_analysis_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze', methods=['POST'])
def api_analyze_chapter(novel_id, chapter_id):
    """
    API: 分析单个章节。

    Args:
        novel_id: 小说ID
        chapter_id: 章节ID
    """
    try:
        # 记录请求开始
        logger.info(f"收到章节分析请求: novel_id={novel_id}, chapter_id={chapter_id}")
        
        # 获取请求参数
        data = request.json or {}
        dimension = data.get('dimension')
        model = data.get('model', config.DEFAULT_MODEL)
        
        logger.info(f"分析参数: dimension={dimension}, model={model}")

        if not dimension:
            logger.warning("缺少维度参数")
            return jsonify({"success": False, "error": "缺少维度参数"})

        # 验证章节属于该小说
        session = Session()
        try:
            chapter = session.query(Chapter).filter(Chapter.id == chapter_id, Chapter.novel_id == novel_id).first()
            
            if not chapter:
                logger.warning(f"章节不存在或不属于该小说: novel_id={novel_id}, chapter_id={chapter_id}")
                return jsonify({"success": False, "error": "章节不存在或不属于该小说"})
                
            logger.info(f"找到章节: id={chapter.id}, title={chapter.title}, novel_id={chapter.novel_id}")
        finally:
            session.close()

        # 启动章节分析
        logger.info(f"开始分析章节: chapter_id={chapter_id}, dimension={dimension}, model={model}")
        result = ChapterAnalysisService.analyze_chapter(
            chapter_id=chapter_id,
            dimension=dimension,
            model=model,
            use_cache=False  # 强制重新分析
        )
        
        logger.info(f"章节分析完成: success={result.get('success', False)}")
        if not result.get('success', False):
            logger.error(f"章节分析失败: {result.get('error', '未知错误')}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"分析章节时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})
