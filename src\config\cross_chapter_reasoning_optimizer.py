"""
九猫系统跨章节推理优化器
专注于优化多章节分析中的推理复用
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
import re
import hashlib
import json

logger = logging.getLogger(__name__)

class CrossChapterReasoningOptimizer:
    """
    优化跨章节推理过程，减少重复分析
    与ResultCacheOptimizer协同工作
    """
    
    def __init__(self, prompt_template: str = "default"):
        """
        初始化优化器
        
        Args:
            prompt_template: 提示词模板
        """
        self.prompt_template = prompt_template
        self.config = self._load_config()
        logger.info(f"[跨章节推理优化器] 初始化完成，使用模板: {prompt_template}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        # 默认配置
        config = {
            "max_previous_chapters": 3 if self.prompt_template == "default" else 2,
            "core_reasoning_ratio": 0.4 if self.prompt_template == "default" else 0.3,
            "min_reasoning_similarity": 0.6,  # 降低相似度阈值，使优化更容易被应用
            "extract_key_patterns": True,
            "reasoning_compression_rate": 0.6 if self.prompt_template == "default" else 0.5,
        }
        
        return config
    
    def optimize(self, current_chapter: Dict[str, Any], previous_analyses: List[Dict[str, Any]], dimension: str) -> Dict[str, Any]:
        """
        优化跨章节推理过程
        
        Args:
            current_chapter: 当前章节信息
            previous_analyses: 前序章节分析结果列表
            dimension: 分析维度
            
        Returns:
            优化后的分析请求
        """
        if not previous_analyses:
            return current_chapter
            
        # 提取前序章节的核心推理链
        core_reasoning_chains = self.extract_core_reasoning(previous_analyses, dimension)
        
        if not core_reasoning_chains:
            return current_chapter
            
        # 检测哪些推理可以直接复用
        reusable_reasoning = self.detect_reusable_reasoning(core_reasoning_chains, current_chapter.get("content", ""))
        
        if reusable_reasoning:
            # 生成优化后的分析提示词
            current_chapter["optimized_prompt"] = self.generate_optimized_prompt(
                current_chapter.get("content", ""), 
                reusable_reasoning, 
                dimension
            )
            
            # 添加元数据
            current_chapter["reasoning_optimization"] = {
                "reused_chains_count": len(reusable_reasoning),
                "source_chapters": [r.get("chapter_id") for r in reusable_reasoning],
                "optimization_type": "cross_chapter_reasoning"
            }
            
            logger.info(f"[跨章节推理优化] 为维度{dimension}找到{len(reusable_reasoning)}个可复用的推理链")
            
        return current_chapter
    
    def extract_core_reasoning(self, previous_analyses: List[Dict[str, Any]], dimension: str) -> List[Dict[str, Any]]:
        """
        提取前序章节的核心推理链
        
        Args:
            previous_analyses: 前序章节分析结果
            dimension: 分析维度
            
        Returns:
            核心推理链列表
        """
        core_chains = []
        
        # 限制使用的前序章节数量
        max_previous = self.config.get("max_previous_chapters", 3 if self.prompt_template == "default" else 2)
        recent_analyses = previous_analyses[-max_previous:] if len(previous_analyses) > max_previous else previous_analyses
        
        for analysis in recent_analyses:
            # 尝试从不同字段获取推理内容
            reasoning = analysis.get("reasoning", "")
            if not reasoning and "content" in analysis:
                # 如果没有专门的reasoning字段，尝试从content中提取
                reasoning = analysis.get("content", "")
            
            chapter_id = analysis.get("chapter_id")
            
            if not reasoning or not chapter_id:
                continue
                
            # 提取核心推理片段
            core_reasoning = self._extract_core_segments(reasoning)
            
            if core_reasoning:
                core_chains.append({
                    "chapter_id": chapter_id,
                    "reasoning": core_reasoning,
                    "patterns": self._identify_reasoning_patterns(core_reasoning, dimension),
                    "hash": self._compute_reasoning_hash(core_reasoning)
                })
        
        return core_chains
    
    def _extract_core_segments(self, reasoning: str) -> str:
        """
        从推理文本中提取核心片段
        
        Args:
            reasoning: 完整推理文本
            
        Returns:
            核心推理片段
        """
        # 找出关键段落
        paragraphs = reasoning.split("\n\n")
        
        # 关键词列表
        key_phrases = [
            "核心分析", "主要特点", "关键发现", "重要结论",
            "分析表明", "值得注意", "突出表现", "显著特点"
        ]
        
        # 找出包含关键词的段落
        core_paragraphs = []
        for para in paragraphs:
            if any(phrase in para for phrase in key_phrases):
                core_paragraphs.append(para)
                
        # 如果没有找到关键段落，则按比例选择
        if not core_paragraphs:
            # 按配置的压缩率选择段落
            target_count = max(1, int(len(paragraphs) * self.config.get("reasoning_compression_rate", 0.6)))
            
            # 尝试通过段落长度和位置找出重要段落
            ranked_paragraphs = [(i, p, len(p)) for i, p in enumerate(paragraphs)]
            ranked_paragraphs.sort(key=lambda x: x[2], reverse=True)  # 按长度排序
            
            # 选择最长的几个段落
            selected_indexes = [item[0] for item in ranked_paragraphs[:target_count]]
            selected_indexes.sort()  # 恢复原始顺序
            
            core_paragraphs = [paragraphs[i] for i in selected_indexes]
        
        # 确保有至少一个段落
        if not core_paragraphs and paragraphs:
            core_paragraphs = [paragraphs[0]]
            
        # 组合核心段落
        return "\n\n".join(core_paragraphs)
    
    def _identify_reasoning_patterns(self, reasoning: str, dimension: str) -> List[Dict[str, Any]]:
        """
        识别推理模式
        
        Args:
            reasoning: 推理文本
            dimension: 分析维度
            
        Returns:
            识别出的模式列表
        """
        patterns = []
        
        if not self.config.get("extract_key_patterns", True):
            return patterns
            
        # 针对不同维度的模式识别规则
        dimension_patterns = {
            "language_style": [
                r"语言风格[^。]*特点是([^。]*)",
                r"修辞手法主要包括([^。]*)",
                r"词汇选择([^。]*)"
            ],
            "rhythm_pacing": [
                r"节奏特点([^。]*)",
                r"叙事节奏([^。]*)",
                r"情节推进([^。]*)"
            ],
            "structure": [
                r"结构特点([^。]*)",
                r"叙事结构([^。]*)",
                r"章节布局([^。]*)"
            ]
        }
        
        # 使用当前维度的规则或通用规则
        patterns_to_use = dimension_patterns.get(dimension, [
            r"主要特点([^。]*)",
            r"核心发现([^。]*)",
            r"关键结论([^。]*)"
        ])
        
        # 添加更通用的模式，提高匹配成功率
        general_patterns = [
            r"([^。]{20,100})", 
            r"(\w+的特点是[^。]*)"
        ]
        
        patterns_to_use.extend(general_patterns)
        
        # 应用模式识别
        for pattern in patterns_to_use:
            try:
                matches = re.findall(pattern, reasoning)
                for match in matches:
                    # 只保留有意义的匹配
                    if len(match) > 5:  # 降低有效匹配的长度要求
                        patterns.append({
                            "pattern": pattern,
                            "content": match.strip(),
                            "confidence": 0.8  # 置信度
                        })
            except Exception:
                # 忽略正则表达式错误
                continue
        
        return patterns
    
    def _compute_reasoning_hash(self, reasoning: str) -> str:
        """计算推理文本的哈希值"""
        return hashlib.md5(reasoning.encode()).hexdigest()
    
    def detect_reusable_reasoning(self, reasoning_chains: List[Dict[str, Any]], current_text: str) -> List[Dict[str, Any]]:
        """
        检测哪些推理可以在当前章节复用
        
        Args:
            reasoning_chains: 核心推理链列表
            current_text: 当前章节文本
            
        Returns:
            可复用的推理链
        """
        reusable = []
        
        # 提取当前文本的特征
        current_features = self._extract_text_features(current_text)
        
        for chain in reasoning_chains:
            # 检查推理模式是否适用于当前文本
            patterns = chain.get("patterns", [])
            
            # 总是添加至少一个推理链，确保优化能被应用
            if not patterns:
                # 如果没有模式，基于文本相似性判断
                applicability = 0.7  # 默认适用率
                chain["applicability"] = applicability
                reusable.append(chain)
                continue
                
            applicable_count = 0
            
            for pattern in patterns:
                pattern_content = pattern.get("content", "")
                if any(feature in pattern_content or pattern_content in feature for feature in current_features):
                    applicable_count += 1
                    
            # 计算适用率
            applicability = applicable_count / len(patterns) if patterns else 0
            
            # 如果适用率达到阈值，认为可复用
            if applicability >= self.config.get("min_reasoning_similarity", 0.6):
                chain["applicability"] = applicability
                reusable.append(chain)
        
        # 如果没有找到可复用的推理，至少添加一个
        if not reusable and reasoning_chains:
            best_chain = max(reasoning_chains, key=lambda x: len(x.get("reasoning", "")))
            best_chain["applicability"] = 0.5  # 默认适用率
            reusable.append(best_chain)
            
        # 按适用率排序
        reusable.sort(key=lambda x: x.get("applicability", 0), reverse=True)
        
        return reusable
    
    def _extract_text_features(self, text: str) -> List[str]:
        """
        提取文本特征
        
        Args:
            text: 文本
            
        Returns:
            特征列表
        """
        # 提取关键词（实际项目中应该使用更复杂的算法）
        # 简单实现：选择高频词
        words = re.findall(r'\w+', text)
        word_freq = {}
        
        for word in words:
            if len(word) > 1:  # 忽略单字符词
                word_freq[word] = word_freq.get(word, 0) + 1
                
        # 选择频率最高的词作为特征
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        features = [word for word, freq in sorted_words[:30]]  # 使用前30个高频词
        
        # 添加句子片段作为特征
        sentences = re.split(r'[。！？]', text)
        for sentence in sentences[:5]:  # 取前5个句子
            if len(sentence) > 10:
                features.append(sentence[:20])  # 取句子前20个字符
        
        return features
    
    def generate_optimized_prompt(self, text: str, reusable_reasoning: List[Dict[str, Any]], dimension: str) -> str:
        """
        生成优化后的分析提示词
        
        Args:
            text: 当前章节文本
            reusable_reasoning: 可复用的推理
            dimension: 分析维度
            
        Returns:
            优化后的提示词
        """
        # 基础提示词
        prompt = f"请分析以下文本的{dimension}。\n\n"
        
        # 添加可复用的推理线索
        if reusable_reasoning:
            prompt += "根据前序章节的分析，以下是可以复用的推理模式和结论：\n\n"
            
            # 添加前3个最适用的推理
            for i, reasoning in enumerate(reusable_reasoning[:3]):
                prompt += f"推理模式 {i+1}：\n{reasoning.get('reasoning', '')}\n\n"
            
            prompt += "请在分析当前文本时，考虑上述推理模式和结论，并根据当前文本内容进行适当修改和扩展。\n\n"
        
        # 添加本文内容
        prompt += f"当前文本：\n{text}\n\n"
        
        # 维度特定的分析指导
        dimension_guidance = {
            "language_style": "请重点关注语言风格、词汇选择、修辞手法等方面，分析其与前序章节的一致性和变化。",
            "rhythm_pacing": "请重点关注叙事节奏、情节推进速度、紧张感营造等方面，分析其与前序章节的一致性和变化。",
            "structure": "请重点关注章节结构、叙事框架、情节组织等方面，分析其与前序章节的一致性和变化。",
        }
        
        # 添加维度特定指导
        prompt += dimension_guidance.get(dimension, "请详细分析上述文本，并考虑与前序章节的连贯性。")
        
        return prompt 