<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫静态文件测试页面</title>
    
    <!-- 测试Bootstrap CSS -->
    <link id="bootstrap-css" href="src/web/static/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- 测试自定义CSS -->
    <link id="custom-css" href="src/web/static/css/style.css" rel="stylesheet">
    
    <style>
        .test-item {
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .failure {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .pending {
            background-color: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>九猫静态文件测试页面</h1>
        <p>此页面用于测试静态文件是否正确加载。</p>
        
        <div class="card mt-4">
            <div class="card-header">
                <h2>CSS文件测试</h2>
            </div>
            <div class="card-body">
                <div id="bootstrap-css-test" class="test-item pending">
                    <h3>Bootstrap CSS</h3>
                    <p>测试中...</p>
                </div>
                
                <div id="custom-css-test" class="test-item pending">
                    <h3>自定义CSS</h3>
                    <p>测试中...</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h2>JavaScript文件测试</h2>
            </div>
            <div class="card-body">
                <div id="jquery-test" class="test-item pending">
                    <h3>jQuery</h3>
                    <p>测试中...</p>
                </div>
                
                <div id="bootstrap-js-test" class="test-item pending">
                    <h3>Bootstrap JS</h3>
                    <p>测试中...</p>
                </div>
                
                <div id="chart-js-test" class="test-item pending">
                    <h3>Chart.js</h3>
                    <p>测试中...</p>
                </div>
                
                <div id="main-js-test" class="test-item pending">
                    <h3>主JS文件</h3>
                    <p>测试中...</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h2>功能测试</h2>
            </div>
            <div class="card-body">
                <div id="bootstrap-collapse-test" class="test-item pending">
                    <h3>Bootstrap折叠功能</h3>
                    <p>测试中...</p>
                    <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                        点击测试折叠功能
                    </button>
                    <div class="collapse mt-2" id="collapseExample">
                        <div class="card card-body">
                            如果您能看到这段文字，说明Bootstrap折叠功能正常工作。
                        </div>
                    </div>
                </div>
                
                <div id="chart-js-drawing-test" class="test-item pending">
                    <h3>Chart.js绘图功能</h3>
                    <p>测试中...</p>
                    <canvas id="testChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="mt-4 mb-5">
            <h2>测试结果摘要</h2>
            <div id="test-summary" class="alert alert-info">
                测试进行中...
            </div>
            <button id="retest-btn" class="btn btn-primary">重新测试</button>
            <a href="/" class="btn btn-secondary ml-2">返回首页</a>
        </div>
    </div>

    <!-- 测试jQuery -->
    <script id="jquery-js" src="src/web/static/js/jquery.min.js"></script>
    
    <!-- 测试Bootstrap JS -->
    <script id="bootstrap-js" src="src/web/static/js/bootstrap.bundle.min.js"></script>
    
    <!-- 测试Chart.js -->
    <script id="chart-js" src="src/web/static/js/chart.min.js"></script>
    
    <!-- 测试主JS文件 -->
    <script id="main-js" src="src/web/static/js/main.js"></script>
    
    <script>
        // 测试函数
        function runTests() {
            let totalTests = 8;
            let passedTests = 0;
            
            // 测试Bootstrap CSS
            setTimeout(function() {
                const bootstrapCssTest = document.getElementById('bootstrap-css-test');
                try {
                    // 检查是否有Bootstrap样式
                    const hasBootstrapStyles = getComputedStyle(document.body).getPropertyValue('font-family').includes('system-ui') || 
                                              document.querySelector('.btn-primary').classList.contains('btn-primary');
                    
                    if (hasBootstrapStyles) {
                        bootstrapCssTest.classList.remove('pending');
                        bootstrapCssTest.classList.add('success');
                        bootstrapCssTest.querySelector('p').textContent = '成功: Bootstrap CSS已正确加载';
                        passedTests++;
                    } else {
                        bootstrapCssTest.classList.remove('pending');
                        bootstrapCssTest.classList.add('failure');
                        bootstrapCssTest.querySelector('p').textContent = '失败: 未检测到Bootstrap样式';
                    }
                } catch (e) {
                    bootstrapCssTest.classList.remove('pending');
                    bootstrapCssTest.classList.add('failure');
                    bootstrapCssTest.querySelector('p').textContent = '失败: ' + e.message;
                }
                updateSummary(passedTests, totalTests);
            }, 500);
            
            // 测试自定义CSS
            setTimeout(function() {
                const customCssTest = document.getElementById('custom-css-test');
                try {
                    // 检查自定义CSS是否加载
                    const customCssLink = document.getElementById('custom-css');
                    const isLoaded = customCssLink.sheet !== null;
                    
                    if (isLoaded) {
                        customCssTest.classList.remove('pending');
                        customCssTest.classList.add('success');
                        customCssTest.querySelector('p').textContent = '成功: 自定义CSS已加载';
                        passedTests++;
                    } else {
                        customCssTest.classList.remove('pending');
                        customCssTest.classList.add('failure');
                        customCssTest.querySelector('p').textContent = '失败: 自定义CSS未加载';
                    }
                } catch (e) {
                    customCssTest.classList.remove('pending');
                    customCssTest.classList.add('failure');
                    customCssTest.querySelector('p').textContent = '失败: ' + e.message;
                }
                updateSummary(passedTests, totalTests);
            }, 600);
            
            // 测试jQuery
            setTimeout(function() {
                const jqueryTest = document.getElementById('jquery-test');
                try {
                    if (typeof jQuery !== 'undefined') {
                        jqueryTest.classList.remove('pending');
                        jqueryTest.classList.add('success');
                        jqueryTest.querySelector('p').textContent = '成功: jQuery ' + jQuery.fn.jquery + ' 已加载';
                        passedTests++;
                    } else {
                        jqueryTest.classList.remove('pending');
                        jqueryTest.classList.add('failure');
                        jqueryTest.querySelector('p').textContent = '失败: jQuery未加载';
                    }
                } catch (e) {
                    jqueryTest.classList.remove('pending');
                    jqueryTest.classList.add('failure');
                    jqueryTest.querySelector('p').textContent = '失败: ' + e.message;
                }
                updateSummary(passedTests, totalTests);
            }, 700);
            
            // 测试Bootstrap JS
            setTimeout(function() {
                const bootstrapJsTest = document.getElementById('bootstrap-js-test');
                try {
                    // 检查Bootstrap JS是否加载
                    if (typeof bootstrap !== 'undefined') {
                        bootstrapJsTest.classList.remove('pending');
                        bootstrapJsTest.classList.add('success');
                        bootstrapJsTest.querySelector('p').textContent = '成功: Bootstrap JS已加载';
                        passedTests++;
                    } else {
                        bootstrapJsTest.classList.remove('pending');
                        bootstrapJsTest.classList.add('failure');
                        bootstrapJsTest.querySelector('p').textContent = '失败: Bootstrap JS未加载';
                    }
                } catch (e) {
                    bootstrapJsTest.classList.remove('pending');
                    bootstrapJsTest.classList.add('failure');
                    bootstrapJsTest.querySelector('p').textContent = '失败: ' + e.message;
                }
                updateSummary(passedTests, totalTests);
            }, 800);
            
            // 测试Chart.js
            setTimeout(function() {
                const chartJsTest = document.getElementById('chart-js-test');
                try {
                    // 检查Chart.js是否加载
                    if (typeof Chart !== 'undefined') {
                        chartJsTest.classList.remove('pending');
                        chartJsTest.classList.add('success');
                        chartJsTest.querySelector('p').textContent = '成功: Chart.js已加载';
                        passedTests++;
                    } else {
                        chartJsTest.classList.remove('pending');
                        chartJsTest.classList.add('failure');
                        chartJsTest.querySelector('p').textContent = '失败: Chart.js未加载';
                    }
                } catch (e) {
                    chartJsTest.classList.remove('pending');
                    chartJsTest.classList.add('failure');
                    chartJsTest.querySelector('p').textContent = '失败: ' + e.message;
                }
                updateSummary(passedTests, totalTests);
            }, 900);
            
            // 测试主JS文件
            setTimeout(function() {
                const mainJsTest = document.getElementById('main-js-test');
                try {
                    // 检查主JS文件是否加载
                    const mainJsScript = document.getElementById('main-js');
                    if (mainJsScript && !mainJsScript.hasAttribute('error')) {
                        mainJsTest.classList.remove('pending');
                        mainJsTest.classList.add('success');
                        mainJsTest.querySelector('p').textContent = '成功: 主JS文件已加载';
                        passedTests++;
                    } else {
                        mainJsTest.classList.remove('pending');
                        mainJsTest.classList.add('failure');
                        mainJsTest.querySelector('p').textContent = '失败: 主JS文件未加载';
                    }
                } catch (e) {
                    mainJsTest.classList.remove('pending');
                    mainJsTest.classList.add('failure');
                    mainJsTest.querySelector('p').textContent = '失败: ' + e.message;
                }
                updateSummary(passedTests, totalTests);
            }, 1000);
            
            // 测试Bootstrap折叠功能
            setTimeout(function() {
                const bootstrapCollapseTest = document.getElementById('bootstrap-collapse-test');
                try {
                    // 检查Bootstrap折叠功能是否正常
                    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Collapse !== 'undefined') {
                        bootstrapCollapseTest.classList.remove('pending');
                        bootstrapCollapseTest.classList.add('success');
                        bootstrapCollapseTest.querySelector('p').textContent = '成功: Bootstrap折叠功能可用';
                        passedTests++;
                    } else {
                        bootstrapCollapseTest.classList.remove('pending');
                        bootstrapCollapseTest.classList.add('failure');
                        bootstrapCollapseTest.querySelector('p').textContent = '失败: Bootstrap折叠功能不可用';
                    }
                } catch (e) {
                    bootstrapCollapseTest.classList.remove('pending');
                    bootstrapCollapseTest.classList.add('failure');
                    bootstrapCollapseTest.querySelector('p').textContent = '失败: ' + e.message;
                }
                updateSummary(passedTests, totalTests);
            }, 1100);
            
            // 测试Chart.js绘图功能
            setTimeout(function() {
                const chartJsDrawingTest = document.getElementById('chart-js-drawing-test');
                try {
                    // 检查Chart.js绘图功能是否正常
                    if (typeof Chart !== 'undefined') {
                        const ctx = document.getElementById('testChart').getContext('2d');
                        const chart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: ['红', '蓝', '黄', '绿', '紫', '橙'],
                                datasets: [{
                                    label: '测试数据',
                                    data: [12, 19, 3, 5, 2, 3],
                                    backgroundColor: [
                                        'rgba(255, 99, 132, 0.2)',
                                        'rgba(54, 162, 235, 0.2)',
                                        'rgba(255, 206, 86, 0.2)',
                                        'rgba(75, 192, 192, 0.2)',
                                        'rgba(153, 102, 255, 0.2)',
                                        'rgba(255, 159, 64, 0.2)'
                                    ],
                                    borderColor: [
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)'
                                    ],
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                        
                        chartJsDrawingTest.classList.remove('pending');
                        chartJsDrawingTest.classList.add('success');
                        chartJsDrawingTest.querySelector('p').textContent = '成功: Chart.js绘图功能正常';
                        passedTests++;
                    } else {
                        chartJsDrawingTest.classList.remove('pending');
                        chartJsDrawingTest.classList.add('failure');
                        chartJsDrawingTest.querySelector('p').textContent = '失败: Chart.js绘图功能不可用';
                    }
                } catch (e) {
                    chartJsDrawingTest.classList.remove('pending');
                    chartJsDrawingTest.classList.add('failure');
                    chartJsDrawingTest.querySelector('p').textContent = '失败: ' + e.message;
                }
                updateSummary(passedTests, totalTests);
            }, 1200);
        }
        
        // 更新测试摘要
        function updateSummary(passed, total) {
            const summaryElement = document.getElementById('test-summary');
            const percentage = Math.round((passed / total) * 100);
            
            if (passed === total) {
                summaryElement.className = 'alert alert-success';
                summaryElement.textContent = `测试完成: 所有测试通过 (${passed}/${total}, 100%)`;
            } else if (passed > 0) {
                summaryElement.className = 'alert alert-warning';
                summaryElement.textContent = `测试完成: 部分测试通过 (${passed}/${total}, ${percentage}%)`;
            } else {
                summaryElement.className = 'alert alert-danger';
                summaryElement.textContent = `测试完成: 所有测试失败 (${passed}/${total}, 0%)`;
            }
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', runTests);
        
        // 重新测试按钮
        document.getElementById('retest-btn').addEventListener('click', function() {
            // 重置所有测试项
            const testItems = document.querySelectorAll('.test-item');
            testItems.forEach(function(item) {
                item.classList.remove('success', 'failure');
                item.classList.add('pending');
                item.querySelector('p').textContent = '测试中...';
            });
            
            // 重置摘要
            const summaryElement = document.getElementById('test-summary');
            summaryElement.className = 'alert alert-info';
            summaryElement.textContent = '测试进行中...';
            
            // 重新运行测试
            runTests();
        });
    </script>
</body>
</html>
