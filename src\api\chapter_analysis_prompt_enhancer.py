"""
九猫系统 - 章节分析提示词增强器
用于增强章节分析提示词，确保分析更加详细和连贯
"""

import logging
import re
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

def enhance_chapter_analysis_prompt(original_prompt: str, prompt_template: str = "default") -> str:
    """
    增强章节分析提示词，添加用户要求的补充内容

    Args:
        original_prompt: 原始提示词
        prompt_template: 提示词模板，默认为 default，可选 simplified

    Returns:
        增强后的提示词
    """
    # 根据提示词模板选择不同的增强内容
    if prompt_template == "simplified":
        # 精简版增强内容 - 温和优化模式
        enhancement = """
重要补充要求（精简版温和优化模式）：
1. 基于原文分析：分析必须基于章节的实际内容，避免泛泛而谈
2. 重点叙述原文内容：重点描述章节中的关键场景、事件、对话和人物心理活动
3. 精炼详细：分析结果要精炼但保持必要详细度，重点突出核心内容
4. 承接上一章节：将前序章节的核心分析作为本章分析的基础，展示章节间的主要连贯性
5. 简洁明了：使用清晰、简洁的语言表达，避免冗余和过多专业术语

以上要求针对精简版温和优化模式，在保证分析质量的同时进一步控制成本。
"""
    else:
        # 默认版增强内容 - 完整模式
        enhancement = """
重要补充要求（必须首先执行）：
1. 必须使用原文：分析必须基于章节的实际内容，不允许泛泛而谈或猜测
2. 必须叙述原文内容：详细描述章节中的场景、事件、对话和人物心理活动
3. 不限字数不做限制：分析结果越详细越好，不要因为字数限制而简化分析
4. 必须承接上一章节同维度的分析结果和推理过程：将前序章节的分析作为本章分析的基础，展示章节间的连贯性和发展
5. 语言必须通俗易懂：使用清晰、简洁的语言表达，避免过多专业术语

以上要求是首要执行的，必须执行的！在进行分析时，请首先考虑这些要求。
"""

    # 检查原始提示词是否已经包含增强内容
    if prompt_template == "simplified":
        # 精简版检查
        if "精简版温和优化模式" in original_prompt:
            logger.info("精简版提示词已包含增强内容，无需再次添加")
            return original_prompt
    else:
        # 默认版检查
        if "必须使用原文" in original_prompt and "必须叙述原文内容" in original_prompt:
            logger.info("默认版提示词已包含增强内容，无需再次添加")
            return original_prompt

    # 在提示词的开头添加增强内容
    # 查找合适的插入位置：在第一个"你是"或"请"之后
    match = re.search(r'(你是.*?专家.*?[。，,.]|请.*?分析.*?[。，,.])', original_prompt)
    if match:
        insert_position = match.end()
        enhanced_prompt = original_prompt[:insert_position] + enhancement + original_prompt[insert_position:]
    else:
        # 如果找不到合适的位置，就在开头添加
        enhanced_prompt = enhancement + original_prompt

    logger.info(f"成功增强章节分析提示词（{prompt_template}版）")
    return enhanced_prompt

def enhance_chapter_outline_prompt(original_prompt: str) -> str:
    """
    特别增强章纲分析提示词

    Args:
        original_prompt: 原始提示词

    Returns:
        增强后的提示词
    """
    # 章纲分析特殊增强内容
    chapter_outline_enhancement = """
章纲分析特别要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 必须详细叙述原文内容：极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理活动
3. 不限字数，越详细越好：内容重现部分必须极其详细，不设字数限制
4. 必须承接上一章节的章纲分析：将前序章节的分析作为本章分析的基础，展示章节间的连贯性和发展
5. 语言必须通俗易懂：使用清晰、生动的语言，模仿原文的风格和语气

内容重现部分必须包含：
- 详细的场景描述：环境、氛围、背景、光线、声音、气味等感官细节
- 完整的事件过程：按照发生顺序详细描述每个重要事件的起因、经过和结果
- 人物对话与互动：完整记录主要人物的对话内容，尽可能使用原文中的重要对话
- 人物心理活动：深入描述人物的心理活动、情感变化和内心冲突
- 情节发展脉络：清晰展示情节如何从开始发展到结束，包括转折点和高潮

以上要求是首要执行的，必须执行的！在进行章纲分析时，请首先考虑这些要求。
"""

    # 检查原始提示词是否已经包含增强内容
    if "必须使用原文" in original_prompt and "必须详细叙述原文内容" in original_prompt:
        logger.info("章纲分析提示词已包含增强内容，无需再次添加")
        return original_prompt

    # 在提示词的开头添加增强内容
    # 查找合适的插入位置：在第一个"你是"或"请"之后
    match = re.search(r'(你是.*?专家.*?[。，,.]|请.*?分析.*?[。，,.])', original_prompt)
    if match:
        insert_position = match.end()
        enhanced_prompt = original_prompt[:insert_position] + chapter_outline_enhancement + original_prompt[insert_position:]
    else:
        # 如果找不到合适的位置，就在开头添加
        enhanced_prompt = chapter_outline_enhancement + original_prompt

    logger.info("成功增强章纲分析提示词")
    return enhanced_prompt
