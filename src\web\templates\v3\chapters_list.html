{% extends "v3/base.html" %}

{% block title %}章节列表 - {{ novel.title }} - 九猫小说分析写作系统v3.0{% endblock %}

{% block head %}
{{ super() }}
<!-- 章节列表页面专用修复脚本 -->
<script src="/static/js/chapter-list-fix-loader.js"></script>
{% endblock %}

{% block extra_css %}
<style>
    .chapter-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px var(--shadow-color);
        position: relative;
        overflow: hidden;
    }

    .chapter-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .chapter-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    }

    .chapter-meta {
        position: relative;
        z-index: 1;
        opacity: 0.9;
    }

    .chapter-table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
    }

    .chapter-table th,
    .chapter-table td {
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
    }

    .chapter-table th {
        background-color: var(--primary-light);
        color: var(--dark-color);
        font-weight: 600;
        text-align: left;
    }

    .chapter-table tr:hover {
        background-color: var(--shadow-color);
    }

    .chapter-number {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: var(--primary-light);
        color: var(--dark-color);
        font-weight: 600;
        display: inline-block;
        margin-right: 0.5rem;
    }

    .chapter-actions {
        display: flex;
        gap: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 章节列表头部信息 -->
<div class="chapter-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">章节列表</h1>
            <div class="chapter-meta">
                <p class="lead mb-2">
                    <i class="fas fa-book me-2"></i>{{ novel.title }}
                </p>
                <p class="mb-0">
                    <i class="fas fa-user me-2"></i>{{ novel.author or '未知作者' }}
                </p>
            </div>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3.view_novel', novel_id=novel.id) }}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-1"></i>返回小说
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 筛选功能 -->
<div class="card shadow-sm mb-4">
    <div class="card-header">
        <h3 class="card-title mb-0"><i class="fas fa-filter me-2"></i>筛选章节</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="filterTitle" class="form-label">章节标题</label>
                <input type="text" class="form-control" id="filterTitle" placeholder="输入关键词筛选标题">
            </div>
            <div class="col-md-4 mb-3">
                <label for="filterWordCount" class="form-label">字数范围</label>
                <select class="form-select" id="filterWordCount">
                    <option value="all">全部</option>
                    <option value="0-1000">1000字以下</option>
                    <option value="1000-3000">1000-3000字</option>
                    <option value="3000-5000">3000-5000字</option>
                    <option value="5000+">5000字以上</option>
                </select>
            </div>
            <div class="col-md-4 mb-3">
                <label for="filterAnalysisStatus" class="form-label">分析状态</label>
                <select class="form-select" id="filterAnalysisStatus">
                    <option value="all">全部</option>
                    <option value="analyzed">已分析</option>
                    <option value="not-analyzed">未分析</option>
                    <option value="complete">分析完成</option>
                    <option value="partial">部分分析</option>
                </select>
            </div>
        </div>
        <div class="d-flex justify-content-end">
            <button id="resetFilterBtn" class="btn btn-outline-secondary me-2">
                <i class="fas fa-undo me-1"></i>重置筛选
            </button>
            <button id="applyFilterBtn" class="btn btn-primary">
                <i class="fas fa-filter me-1"></i>应用筛选
            </button>
        </div>
    </div>
</div>

<!-- 章节列表 -->
<div class="card shadow-sm">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0"><i class="fas fa-list me-2"></i>章节列表</h3>
            <div>
                <a href="{{ url_for('v3.chapters_summary', novel_id=novel.id) }}" class="btn btn-primary btn-sm me-2">
                    <i class="fas fa-table me-1"></i>章节分析汇总
                </a>
                <button id="analyzeAllChapterDimensionsBtn" class="btn btn-success btn-sm">
                    <i class="fas fa-magic me-1"></i>一键分析所有章节维度
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="chapter-table">
                <thead>
                    <tr>
                        <th>章节</th>
                        <th>标题</th>
                        <th>字数</th>
                        <th>分析状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="chapterTableBody">
                    {% for chapter in chapters %}
                    <tr class="chapter-row"
                        data-title="{{ chapter.title }}"
                        data-word-count="{{ chapter.word_count }}"
                        data-chapter-id="{{ chapter.id }}">
                        <td>
                            <span class="chapter-number">{{ chapter.chapter_number }}</span>
                        </td>
                        <td>{{ chapter.title }}</td>
                        <td>{{ chapter.word_count }}</td>
                        <td>
                            {% set chapter_results = chapter_analysis_results.get(chapter.id, {}) %}
                            <span class="badge {% if chapter_results|length > 0 %}bg-success{% else %}bg-secondary{% endif %}"
                                  data-analysis-count="{{ chapter_results|length }}"
                                  data-total-dimensions="{{ dimensions|length }}">
                                {{ chapter_results|length }}/{{ dimensions|length }}
                            </span>
                            {% if chapter_results|length > 0 %}
                                <button class="btn btn-sm btn-outline-info ms-2 show-dimensions-btn" data-bs-toggle="modal" data-bs-target="#dimensionsModal-{{ chapter.id }}">
                                    <i class="fas fa-list-ul"></i>
                                </button>

                                <!-- 分析维度模态框 -->
                                <div class="modal fade" id="dimensionsModal-{{ chapter.id }}" tabindex="-1" aria-labelledby="dimensionsModalLabel-{{ chapter.id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="dimensionsModalLabel-{{ chapter.id }}">{{ chapter.title }} - 分析维度</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    {% for dimension in dimensions %}
                                                        <div class="col-md-4 mb-3">
                                                            <div class="card h-100">
                                                                <div class="card-body">
                                                                    <h5 class="card-title">{{ dimension.name }}</h5>
                                                                    <p class="card-text">
                                                                        {% if dimension.key in chapter_results %}
                                                                            <span class="badge bg-success">已分析</span>
                                                                        {% else %}
                                                                            <span class="badge bg-secondary">未分析</span>
                                                                        {% endif %}
                                                                    </p>
                                                                    {% if dimension.key in chapter_results %}
                                                                        <div class="d-flex justify-content-between">
                                                                            <a href="{{ url_for('v3.chapter_analysis', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension.key) }}" class="btn btn-sm btn-primary">
                                                                                查看结果
                                                                            </a>
                                                                            <button class="btn btn-sm btn-outline-danger delete-chapter-analysis-btn"
                                                                                    data-novel-id="{{ novel.id }}"
                                                                                    data-chapter-id="{{ chapter.id }}"
                                                                                    data-dimension="{{ dimension.key }}"
                                                                                    data-dimension-name="{{ dimension.name }}">
                                                                                <i class="fas fa-trash"></i> 删除
                                                                            </button>
                                                                        </div>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                                <a href="{{ url_for('v3.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-primary">
                                                    查看章节详情
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </td>
                        <td>
                            <div class="chapter-actions">
                                <a href="{{ url_for('v3.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye me-1"></i>查看
                                </a>
                                {% if chapter_results|length > 0 %}
                                <button class="btn btn-sm btn-outline-primary reanalyze-chapter-btn"
                                        data-novel-id="{{ novel.id }}"
                                        data-chapter-id="{{ chapter.id }}">
                                    <i class="fas fa-sync-alt me-1"></i>重新分析
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 章节分析维度删除按钮点击事件
        $(document).on('click', '.delete-chapter-analysis-btn', function() {
            const novelId = $(this).data('novel-id');
            const chapterId = $(this).data('chapter-id');
            const dimension = $(this).data('dimension');
            const dimensionName = $(this).data('dimension-name');

            if (!novelId || !chapterId || !dimension) {
                console.error('缺少必要的数据属性');
                return;
            }

            if (confirm(`确定要删除章节的"${dimensionName}"分析结果吗？此操作不可恢复。`)) {
                // 禁用按钮防止重复点击
                $(this).prop('disabled', true);
                const originalText = $(this).html();
                $(this).html('<i class="fas fa-spinner fa-spin"></i>');

                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-danger mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在删除分析结果，请稍候...</h5>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);

                // 调用API删除章节分析结果
                $.ajax({
                    url: `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/delete`,
                    type: 'POST',
                    success: function(response) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        if (response.success) {
                            alert(`成功删除章节的"${dimensionName}"分析结果`);
                            // 刷新页面
                            location.reload();
                        } else {
                            alert(`删除分析结果失败: ${response.message || '未知错误'}`);
                            // 恢复按钮状态
                            $(this).html(originalText);
                            $(this).prop('disabled', false);

                            // 尝试使用备用API路径
                            $.ajax({
                                url: `/api/novels/${novelId}/chapter/${chapterId}/analysis/${dimension}/delete`,
                                type: 'POST',
                                success: function(backupResponse) {
                                    if (backupResponse.success) {
                                        alert(`成功删除章节的"${dimensionName}"分析结果`);
                                        // 刷新页面
                                        location.reload();
                                    } else {
                                        alert(`删除分析结果失败: ${backupResponse.message || '未知错误'}`);
                                        // 恢复按钮状态
                                        $(this).html(originalText);
                                        $(this).prop('disabled', false);
                                    }
                                },
                                error: function(xhr) {
                                    alert(`所有API路径均失败，无法删除分析结果: ${xhr.status} ${xhr.statusText}`);
                                    // 恢复按钮状态
                                    $(this).html(originalText);
                                    $(this).prop('disabled', false);
                                }
                            });
                        }
                    },
                    error: function(xhr) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        alert(`删除分析结果失败: ${xhr.status} ${xhr.statusText}`);
                        // 恢复按钮状态
                        $(this).html(originalText);
                        $(this).prop('disabled', false);

                        // 尝试使用备用API路径
                        $.ajax({
                            url: `/api/novels/${novelId}/chapter/${chapterId}/analysis/${dimension}/delete`,
                            type: 'POST',
                            success: function(backupResponse) {
                                if (backupResponse.success) {
                                    alert(`成功删除章节的"${dimensionName}"分析结果`);
                                    // 刷新页面
                                    location.reload();
                                }
                            }
                        });
                    }
                });
            }
        });
        // 章节重新分析按钮点击事件
        $('.reanalyze-chapter-btn').click(function() {
            const novelId = $(this).data('novel-id');
            const chapterId = $(this).data('chapter-id');

            if (!novelId || !chapterId) {
                console.error('缺少必要的数据属性');
                return;
            }

            if (confirm('确定要重新分析该章节的所有维度吗？这将覆盖现有的分析结果。')) {
                // 禁用按钮防止重复点击
                $(this).prop('disabled', true);
                const originalText = $(this).html();
                $(this).html('<i class="fas fa-spinner fa-spin"></i>');

                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在启动重新分析，请稍候...</h5>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);

                // 获取所有维度
                const dimensions = [];
                {% for dimension in dimensions %}
                dimensions.push('{{ dimension.key }}');
                {% endfor %}

                // 创建一个计数器来跟踪完成的分析请求
                let completedRequests = 0;
                let successfulRequests = 0;
                const totalRequests = dimensions.length;

                // 对每个维度发送单独的分析请求
                dimensions.forEach(dimension => {
                    // 构建API URL
                    const apiUrl = `/api/novel/${novelId}/chapter/${chapterId}/analyze_dimension`;

                    // 发送重新分析请求
                    fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            dimension: dimension,
                            force: true
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        completedRequests++;
                        if (data.success) {
                            successfulRequests++;
                        }

                        // 更新加载提示
                        $('#loadingOverlay .card h5').text(`正在重新分析维度: ${completedRequests}/${totalRequests} (成功: ${successfulRequests})`);

                        // 如果所有请求都已完成，刷新页面
                        if (completedRequests === totalRequests) {
                            setTimeout(() => {
                                // 移除加载提示
                                $('#loadingOverlay').remove();

                                // 显示成功消息
                                alert(`重新分析完成，成功: ${successfulRequests}/${totalRequests}`);

                                // 刷新页面
                                window.location.reload();
                            }, 1000);
                        }
                    })
                    .catch(error => {
                        completedRequests++;
                        console.error(`分析维度 ${dimension} 时出错:`, error);

                        // 更新加载提示
                        $('#loadingOverlay .card h5').text(`正在重新分析维度: ${completedRequests}/${totalRequests} (成功: ${successfulRequests})`);

                        // 如果所有请求都已完成，刷新页面
                        if (completedRequests === totalRequests) {
                            setTimeout(() => {
                                // 移除加载提示
                                $('#loadingOverlay').remove();

                                // 显示成功消息
                                alert(`重新分析完成，成功: ${successfulRequests}/${totalRequests}`);

                                // 刷新页面
                                window.location.reload();
                            }, 1000);
                        }
                    });
                });
            }
        });

        // 一键分析所有章节维度
        $('#analyzeAllChapterDimensionsBtn').click(function() {
            if (confirm('确定要一键分析所有章节的所有维度吗？这可能需要较长时间。')) {
                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在启动分析，请稍候...</h5>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);

                // 发送AJAX请求
                $.ajax({
                    url: '/api/novel/{{ novel.id }}/analyze_all_chapters',
                    type: 'POST',
                    contentType: 'application/json',
                    success: function(response) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        if (response.success) {
                            alert('分析已开始，请稍后查看结果。\n' + response.message);
                        } else {
                            alert('开始分析失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        alert('开始分析失败: ' + error);
                    }
                });
            }
        });

        // 筛选功能实现
        function applyFilters() {
            const titleFilter = $('#filterTitle').val().toLowerCase();
            const wordCountFilter = $('#filterWordCount').val();
            const analysisStatusFilter = $('#filterAnalysisStatus').val();

            // 遍历所有章节行
            $('.chapter-row').each(function() {
                const $row = $(this);
                const title = $row.data('title').toLowerCase();
                const wordCount = parseInt($row.data('word-count'));
                const $badge = $row.find('.badge');
                const analysisCount = parseInt($badge.data('analysis-count'));
                const totalDimensions = parseInt($badge.data('total-dimensions'));

                // 标题筛选
                let showByTitle = true;
                if (titleFilter) {
                    showByTitle = title.includes(titleFilter);
                }

                // 字数筛选
                let showByWordCount = true;
                if (wordCountFilter !== 'all') {
                    if (wordCountFilter === '0-1000') {
                        showByWordCount = wordCount < 1000;
                    } else if (wordCountFilter === '1000-3000') {
                        showByWordCount = wordCount >= 1000 && wordCount < 3000;
                    } else if (wordCountFilter === '3000-5000') {
                        showByWordCount = wordCount >= 3000 && wordCount < 5000;
                    } else if (wordCountFilter === '5000+') {
                        showByWordCount = wordCount >= 5000;
                    }
                }

                // 分析状态筛选
                let showByAnalysisStatus = true;
                if (analysisStatusFilter !== 'all') {
                    if (analysisStatusFilter === 'analyzed') {
                        showByAnalysisStatus = analysisCount > 0;
                    } else if (analysisStatusFilter === 'not-analyzed') {
                        showByAnalysisStatus = analysisCount === 0;
                    } else if (analysisStatusFilter === 'complete') {
                        showByAnalysisStatus = analysisCount === totalDimensions;
                    } else if (analysisStatusFilter === 'partial') {
                        showByAnalysisStatus = analysisCount > 0 && analysisCount < totalDimensions;
                    }
                }

                // 综合所有筛选条件
                if (showByTitle && showByWordCount && showByAnalysisStatus) {
                    $row.show();
                } else {
                    $row.hide();
                }
            });

            // 显示筛选结果统计
            const visibleRows = $('.chapter-row:visible').length;
            const totalRows = $('.chapter-row').length;

            // 添加筛选结果提示
            if ($('#filterResultInfo').length === 0) {
                $('.card-header:eq(1)').append(
                    '<div id="filterResultInfo" class="mt-2 small text-muted"></div>'
                );
            }

            $('#filterResultInfo').text(`显示 ${visibleRows}/${totalRows} 个章节`);
        }

        // 应用筛选按钮点击事件
        $('#applyFilterBtn').click(function() {
            applyFilters();
        });

        // 重置筛选按钮点击事件
        $('#resetFilterBtn').click(function() {
            $('#filterTitle').val('');
            $('#filterWordCount').val('all');
            $('#filterAnalysisStatus').val('all');
            $('.chapter-row').show();

            // 清除筛选结果提示
            $('#filterResultInfo').remove();
        });

        // 标题输入框回车键触发筛选
        $('#filterTitle').keypress(function(e) {
            if (e.which === 13) {
                applyFilters();
            }
        });
    });
</script>
{% endblock %}
