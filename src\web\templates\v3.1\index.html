{% extends "v3.1/base.html" %}

{% block title %}首页 - 九猫小说分析写作系统v3.1{% endblock %}

{% block content %}
<div class="row">
    <!-- 主要内容区域 -->
    <div class="col-lg-8">
        <!-- 欢迎卡片 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-cat fa-3x text-primary me-3"></i>
                    <h2 class="card-title mb-0">欢迎使用九猫小说分析写作系统v3.1</h2>
                </div>
                <p class="lead">九猫是一款专为作家和编辑设计的智能小说分析写作工具，基于先进的AI技术，提供全方位的文本分析和写作辅助服务。</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-start mt-4">
                    <a href="{{ url_for('v3_1.upload_novel') }}" class="btn btn-primary btn-lg px-4 me-md-2">
                        <i class="fas fa-upload me-2"></i>开始分析
                    </a>
                    <a href="{{ url_for('v3_1.console') }}" class="btn btn-success btn-lg px-4 me-md-2">
                        <i class="fas fa-terminal me-2"></i>进入控制台
                    </a>
                    <a href="{{ url_for('v3_1.novels') }}" class="btn btn-outline-secondary btn-lg px-4">
                        <i class="fas fa-book me-2"></i>查看小说
                    </a>
                </div>
            </div>
        </div>

        <!-- 系统功能介绍 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0"><i class="fas fa-star me-2"></i>系统功能</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-search fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>小说分析</h4>
                                <p class="text-muted">全面分析小说的15个维度，包括语言风格、结构分析、人物关系等。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-bookmark fa-2x text-success"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>参考蓝本</h4>
                                <p class="text-muted">保存已完成全部分析的书籍作为参考，为写作提供灵感和指导。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-terminal fa-2x text-info"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>控制台</h4>
                                <p class="text-muted">核心功能，具有自动写作能力，可调用参考蓝本中的分析结果。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-chart-bar fa-2x text-warning"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>展示台</h4>
                                <p class="text-muted">直观展示创作统计数据，包括章节数、总字数、平均字数等。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-archive fa-2x text-danger"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>内容仓库</h4>
                                <p class="text-muted">存放自动写作生成的内容，方便查看、编辑和管理。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-sync fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h4>功能联通</h4>
                                <p class="text-muted">所有功能紧密联通，协同合作发挥最大效果。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用流程 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0"><i class="fas fa-tasks me-2"></i>使用流程</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <i class="fas fa-upload fa-3x text-primary mb-3"></i>
                            <h5>1. 上传小说</h5>
                            <p class="small">上传TXT文件或直接粘贴文本内容</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <i class="fas fa-cogs fa-3x text-primary mb-3"></i>
                            <h5>2. 选择维度</h5>
                            <p class="small">从15个专业维度中选择需要分析的方面</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <i class="fas fa-bookmark fa-3x text-primary mb-3"></i>
                            <h5>3. 设为蓝本</h5>
                            <p class="small">将分析完成的小说设为参考蓝本</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="p-3">
                            <i class="fas fa-pencil-alt fa-3x text-primary mb-3"></i>
                            <h5>4. 自动写作</h5>
                            <p class="small">在控制台中使用参考蓝本进行自动写作</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 最近分析 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0"><i class="fas fa-history me-2"></i>最近分析</h3>
            </div>
            <div class="card-body">
                {% if novels %}
                    <ul class="list-group list-group-flush">
                        {% for novel in novels %}
                            <li class="list-group-item bg-transparent border-bottom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="{{ url_for('v3_1.view_novel', novel_id=novel.id) }}" class="text-decoration-none">
                                        <h5 class="mb-1">{{ novel.title }}</h5>
                                        <p class="small text-muted mb-0">
                                            {% if novel.author %}作者: {{ novel.author }}{% endif %}
                                            <span class="ms-2">{{ novel.word_count }} 字</span>
                                        </p>
                                    </a>
                                    <span class="badge bg-primary rounded-pill">{{ novel.created_at.strftime('%m-%d') if novel.created_at is not string else novel.created_at }}</span>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p>暂无分析记录</p>
                        <a href="{{ url_for('v3_1.upload_novel') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-upload me-1"></i>上传小说
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 参考蓝本 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-success text-white">
                <h3 class="card-title mb-0"><i class="fas fa-bookmark me-2"></i>参考蓝本</h3>
            </div>
            <div class="card-body">
                {% if reference_templates %}
                    <ul class="list-group list-group-flush">
                        {% for template in reference_templates %}
                            <li class="list-group-item bg-transparent border-bottom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="{{ url_for('v3_1.view_novel', novel_id=template.id) }}" class="text-decoration-none">
                                        <h5 class="mb-1">{{ template.title }}</h5>
                                        <p class="small text-muted mb-0">
                                            {% if template.author %}作者: {{ template.author }}{% endif %}
                                            <span class="ms-2">{{ template.word_count }} 字</span>
                                        </p>
                                    </a>
                                    <a href="{{ url_for('v3_1.console') }}?template_id={{ template.id }}" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-terminal me-1"></i>使用
                                    </a>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-bookmark fa-3x text-muted mb-3"></i>
                        <p>暂无参考蓝本</p>
                        <a href="{{ url_for('v3_1.reference_templates') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus me-1"></i>创建蓝本
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 展示台 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h3 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>展示台</h3>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="p-3 border rounded">
                            <h2 class="text-primary">{{ generated_content_stats.total_chapters }}</h2>
                            <p class="small mb-0">已创作章节</p>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="p-3 border rounded">
                            <h2 class="text-primary">{{ generated_content_stats.total_words }}</h2>
                            <p class="small mb-0">总字数</p>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="p-3 border rounded">
                            <h2 class="text-primary">{{ generated_content_stats.avg_words_per_chapter }}</h2>
                            <p class="small mb-0">平均字数/章</p>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="p-3 border rounded">
                            <h2 class="text-primary">{{ generated_content_stats.total_hours }}</h2>
                            <p class="small mb-0">运行时间(小时)</p>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('v3_1.showcase') }}" class="btn btn-warning btn-sm">
                        <i class="fas fa-chart-bar me-1"></i>查看详情
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
