/**
 * 九猫小说分析写作系统 - 直接分析结果修复脚本
 *
 * 此脚本用于直接从数据库获取分析结果并显示在页面上，
 * 绕过API调用，确保分析结果能够正确显示。
 * 版本: 1.0.0
 */

(function() {
    console.log('[直接分析结果修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        pollInterval: 1000,  // 轮询间隔（毫秒）
        maxRetries: 5,       // 最大重试次数
        selectors: {
            analysisContent: '#analysisContent, .analysis-content',
            reasoningContent: '#reasoningContent, #reasoningContentExpanded, .reasoning-content',
            progressBar: '.progress-bar',
            statusBadge: '.analysis-status-badge',
            loadingIndicator: '.loading-indicator',
            analysisResult: '.analysis-result',
            noResultMessage: '.text-center.py-5'
        }
    };

    // 状态
    const STATE = {
        novelId: null,
        chapterId: null,
        dimension: null,
        isChapterAnalysis: false,
        isLoading: false,
        retryCount: 0,
        pollTimer: null
    };

    // 调试日志
    function debugLog(message, level = 'info') {
        if (CONFIG.debug) {
            const prefix = '[直接分析结果修复]';
            switch (level) {
                case 'error':
                    console.error(`${prefix} ${message}`);
                    break;
                case 'warn':
                    console.warn(`${prefix} ${message}`);
                    break;
                default:
                    console.log(`${prefix} ${message}`);
            }
        }
    }

    // 从页面获取信息
    function getPageInfo() {
        // 尝试从URL获取信息
        const urlParams = new URLSearchParams(window.location.search);
        const pathParts = window.location.pathname.split('/');

        // 尝试从URL路径获取小说ID和维度
        let novelId = null;
        let chapterId = null;
        let dimension = null;
        let isChapterAnalysis = false;

        // 检查URL路径中是否包含novel和dimension
        for (let i = 0; i < pathParts.length; i++) {
            if (pathParts[i] === 'novel' && i + 1 < pathParts.length) {
                novelId = pathParts[i + 1];
            } else if (pathParts[i] === 'chapter' && i + 1 < pathParts.length) {
                chapterId = pathParts[i + 1];
                isChapterAnalysis = true;
            } else if (pathParts[i] === 'dimension' && i + 1 < pathParts.length) {
                dimension = pathParts[i + 1];
            }
        }

        // 如果URL中没有，尝试从URL参数获取
        if (!novelId) novelId = urlParams.get('novel_id');
        if (!chapterId) chapterId = urlParams.get('chapter_id');
        if (!dimension) dimension = urlParams.get('dimension');

        // 如果URL中没有，尝试从页面元素获取
        if (!novelId || !dimension) {
            // 尝试从页面元素获取
            const novelElement = document.querySelector('[data-novel-id]');
            const dimensionElement = document.querySelector('[data-dimension]');
            const chapterElement = document.querySelector('[data-chapter-id]');

            if (novelElement) {
                novelId = novelElement.getAttribute('data-novel-id');
            }

            if (chapterElement) {
                chapterId = chapterElement.getAttribute('data-chapter-id');
                isChapterAnalysis = true;
            }

            if (dimensionElement) {
                dimension = dimensionElement.getAttribute('data-dimension');
            }
        }

        // 更新状态
        STATE.novelId = novelId;
        STATE.chapterId = chapterId;
        STATE.dimension = dimension;
        STATE.isChapterAnalysis = isChapterAnalysis;

        if (STATE.novelId && STATE.dimension) {
            debugLog(`获取到页面信息: 小说ID=${STATE.novelId}, ${STATE.isChapterAnalysis ? '章节ID=' + STATE.chapterId + ', ' : ''}维度=${STATE.dimension}`);
            return true;
        }

        debugLog('无法获取页面信息', 'warn');
        return false;
    }

    // 直接加载分析结果
    function loadDirectAnalysisResult() {
        if (STATE.isLoading) {
            debugLog('已有加载请求正在进行，跳过');
            return;
        }

        if (!STATE.novelId || !STATE.dimension) {
            debugLog('缺少必要信息，无法加载分析结果', 'warn');
            return;
        }

        STATE.isLoading = true;
        debugLog('开始直接加载分析结果');

        // 构建直接数据库查询URL
        let queryUrl;
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            queryUrl = `/direct_db/chapter_analysis?novel_id=${STATE.novelId}&chapter_id=${STATE.chapterId}&dimension=${STATE.dimension}`;
        } else {
            queryUrl = `/direct_db/analysis?novel_id=${STATE.novelId}&dimension=${STATE.dimension}`;
        }

        // 添加时间戳防止缓存
        queryUrl += `&_=${Date.now()}`;

        // 尝试多个路径
        const tryPaths = [
            queryUrl,
            queryUrl.replace('/direct_db/', '/v3.1/direct_db/'),
            `/api/novel/${STATE.novelId}/analysis/${STATE.dimension}?_=${Date.now()}`
        ];

        // 如果是章节分析，添加章节API路径
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            tryPaths.push(`/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}?_=${Date.now()}`);
        }

        debugLog(`将尝试以下路径: ${tryPaths.join(', ')}`);

        // 尝试第一个路径
        tryNextPath(0);

        // 递归尝试下一个路径
        function tryNextPath(index) {
            if (index >= tryPaths.length) {
                debugLog('所有路径都已尝试，无法加载分析结果', 'error');
                STATE.isLoading = false;
                return;
            }

            const currentPath = tryPaths[index];
            debugLog(`尝试路径 ${index + 1}/${tryPaths.length}: ${currentPath}`);

            // 发送请求
            fetch(currentPath)
                .then(response => {
                    if (!response.ok) {
                        debugLog(`路径 ${currentPath} 返回错误: ${response.status}`, 'warn');
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    debugLog(`从路径 ${currentPath} 获取到数据`);

                    if (data.success === false) {
                        throw new Error(data.error || '获取分析结果失败');
                    }

                    // 提取结果内容
                    let content = '';
                    let reasoningContent = '';

                    if (data.result) {
                        content = data.result.content || '';
                        reasoningContent = data.result.reasoning_content || '';
                    } else if (data.content) {
                        content = data.content;
                    } else if (data.analysis && data.analysis.content) {
                        content = data.analysis.content;
                    }

                    if (content) {
                        // 更新分析结果显示
                        updateAnalysisContent(content);
                        debugLog(`成功从路径 ${currentPath} 更新分析结果内容`);

                        // 更新推理过程
                        if (reasoningContent) {
                            updateReasoningContent(reasoningContent);
                            debugLog('成功更新推理过程内容');
                        } else if (currentPath.includes('/api/')) {
                            // 如果是API路径，尝试加载推理过程
                            loadReasoningContent();
                        }
                    } else {
                        debugLog(`从路径 ${currentPath} 获取的分析结果内容为空`, 'warn');
                        // 尝试下一个路径
                        tryNextPath(index + 1);
                    }
                })
                .catch(error => {
                    debugLog(`从路径 ${currentPath} 加载分析结果出错: ${error.message}`, 'error');
                    // 尝试下一个路径
                    tryNextPath(index + 1);
                })
                .finally(() => {
                    if (index === tryPaths.length - 1) {
                        STATE.isLoading = false;
                    }
                });
        }
    }

    // 加载推理过程
    function loadReasoningContent() {
        if (!STATE.novelId || !STATE.dimension) {
            return;
        }

        debugLog('尝试加载推理过程');

        // 构建API URL
        let apiUrl;
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            apiUrl = `/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}/reasoning_content`;
        } else {
            apiUrl = `/api/novel/${STATE.novelId}/analysis/${STATE.dimension}/reasoning_content`;
        }

        // 添加时间戳防止缓存
        apiUrl += `?_=${Date.now()}`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到推理过程数据');

                if (data.success === false) {
                    throw new Error(data.error || '获取推理过程失败');
                }

                // 提取推理过程内容
                let reasoningContent = '';
                if (data.reasoning_content) {
                    reasoningContent = data.reasoning_content;
                }

                if (reasoningContent) {
                    // 更新推理过程
                    updateReasoningContent(reasoningContent);
                    debugLog('成功更新推理过程内容');
                } else {
                    debugLog('推理过程内容为空', 'warn');
                }
            })
            .catch(error => {
                debugLog(`加载推理过程出错: ${error.message}`, 'error');
            });
    }
    }

    // 常规API加载分析结果
    function loadAnalysisResult() {
        if (STATE.isLoading) {
            debugLog('已有加载请求正在进行，跳过');
            return;
        }

        STATE.isLoading = true;
        debugLog('尝试通过常规API加载分析结果');

        // 构建API URL
        let apiUrl;
        if (STATE.isChapterAnalysis && STATE.chapterId) {
            apiUrl = `/api/novel/${STATE.novelId}/chapter/${STATE.chapterId}/analysis/${STATE.dimension}`;
        } else {
            apiUrl = `/api/novel/${STATE.novelId}/analysis/${STATE.dimension}`;
        }

        // 添加时间戳防止缓存
        apiUrl += `?_=${Date.now()}`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到API分析结果数据');

                if (data.success === false) {
                    throw new Error(data.error || '获取分析结果失败');
                }

                // 提取结果内容
                let content = '';
                if (data.result && data.result.content) {
                    content = data.result.content;
                } else if (data.content) {
                    content = data.content;
                } else if (data.analysis && data.analysis.content) {
                    content = data.analysis.content;
                }

                if (content) {
                    // 更新分析结果显示
                    updateAnalysisContent(content);
                    debugLog('成功更新分析结果内容');
                } else {
                    debugLog('API分析结果内容为空', 'warn');
                }
            })
            .catch(error => {
                debugLog(`API加载分析结果出错: ${error.message}`, 'error');
            })
            .finally(() => {
                STATE.isLoading = false;
            });
    }

    // 更新分析结果内容
    function updateAnalysisContent(content) {
        const contentElements = document.querySelectorAll(CONFIG.selectors.analysisContent);
        if (contentElements.length > 0) {
            contentElements.forEach(element => {
                // 检查是否有"暂无分析结果"的提示
                const noResultMessage = element.querySelector(CONFIG.selectors.noResultMessage);
                if (noResultMessage) {
                    // 移除"暂无分析结果"的提示
                    noResultMessage.remove();

                    // 创建分析结果元素
                    const resultElement = document.createElement('div');
                    resultElement.className = 'analysis-result';
                    resultElement.innerHTML = content;

                    // 添加到内容元素中
                    element.appendChild(resultElement);
                    debugLog('创建并添加了新的分析结果元素');
                } else {
                    // 查找现有的分析结果元素
                    const resultElement = element.querySelector(CONFIG.selectors.analysisResult);
                    if (resultElement) {
                        // 更新现有的分析结果元素
                        resultElement.innerHTML = content;
                        debugLog('更新了现有的分析结果元素');
                    } else {
                        // 直接更新内容元素
                        element.innerHTML = content;
                        debugLog('直接更新了内容元素');
                    }
                }
            });
            debugLog('分析结果内容已更新');
        } else {
            debugLog('找不到分析结果内容元素', 'warn');
        }
    }

    // 更新推理过程内容
    function updateReasoningContent(content) {
        const contentElements = document.querySelectorAll(CONFIG.selectors.reasoningContent);
        if (contentElements.length > 0) {
            contentElements.forEach(element => {
                element.innerHTML = content;
            });
            debugLog('推理过程内容已更新');
        } else {
            debugLog('找不到推理过程内容元素', 'warn');
        }
    }

    // 初始化
    function init() {
        debugLog('初始化直接分析结果修复脚本');

        // 获取页面信息
        if (getPageInfo()) {
            // 直接加载分析结果
            loadDirectAnalysisResult();
        }
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    console.log('[直接分析结果修复] 脚本加载完成');
})();
