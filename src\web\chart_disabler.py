"""
九猫系统 - 图表禁用模块
用于在后端禁用所有图表相关功能，减轻系统资源占用
"""

import os
import logging
from functools import wraps

# 配置日志
logger = logging.getLogger(__name__)

# 检查是否启用图表禁用
DISABLE_CHARTS = os.environ.get('DISABLE_CHARTS', 'True').lower() in ('true', '1', 'yes')

def is_charts_disabled():
    """检查图表是否被禁用"""
    return DISABLE_CHARTS

def disable_chart_routes(app):
    """
    禁用所有与图表相关的路由
    
    Args:
        app: Flask应用实例
    """
    if not DISABLE_CHARTS:
        logger.info("图表未被禁用，保持所有图表路由")
        return
    
    logger.info("图表已被禁用，开始禁用图表相关路由")
    
    # 获取所有路由
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append((rule.endpoint, rule.rule))
    
    # 图表相关路由关键词
    chart_keywords = [
        'chart', 'visualization', 'visual', 'graph', 'plot'
    ]
    
    # 禁用的路由计数
    disabled_count = 0
    
    # 遍历所有路由，禁用图表相关路由
    for endpoint, rule in routes:
        # 检查路由是否与图表相关
        is_chart_route = any(keyword in endpoint.lower() or keyword in rule.lower() for keyword in chart_keywords)
        
        if is_chart_route:
            try:
                # 获取原始视图函数
                view_func = app.view_functions.get(endpoint)
                
                if view_func:
                    # 创建一个替代函数，返回禁用消息
                    @wraps(view_func)
                    def disabled_view(*args, **kwargs):
                        return {
                            "success": False,
                            "error": "图表功能已禁用",
                            "message": "为了提高系统性能，图表功能已被禁用"
                        }, 200
                    
                    # 替换原始视图函数
                    app.view_functions[endpoint] = disabled_view
                    disabled_count += 1
                    logger.info(f"已禁用图表路由: {endpoint} ({rule})")
            except Exception as e:
                logger.error(f"禁用图表路由时出错: {endpoint} ({rule}): {str(e)}")
    
    logger.info(f"图表禁用完成，共禁用 {disabled_count} 个路由")

def inject_chart_disabled_flag(app):
    """
    向所有模板注入图表禁用标志
    
    Args:
        app: Flask应用实例
    """
    @app.context_processor
    def inject_chart_disabled():
        return {
            'charts_disabled': DISABLE_CHARTS
        }
    
    logger.info(f"已向模板注入图表禁用标志: charts_disabled={DISABLE_CHARTS}")

def initialize_chart_disabler(app):
    """
    初始化图表禁用功能
    
    Args:
        app: Flask应用实例
    """
    if DISABLE_CHARTS:
        logger.info("图表禁用功能已启用")
        
        # 禁用图表相关路由
        disable_chart_routes(app)
        
        # 向模板注入图表禁用标志
        inject_chart_disabled_flag(app)
    else:
        logger.info("图表禁用功能未启用")
