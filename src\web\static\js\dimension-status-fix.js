/**
 * 维度状态修复脚本
 *
 * 这个脚本用于修复维度状态显示问题，特别是修复显示为"未知"但实际已分析完成的维度。
 */

(function() {
    // 配置
    const CONFIG = {
        // 调试模式
        debug: true,
        // 检查间隔（毫秒）
        checkInterval: 2000,
        // 最大重试次数
        maxRetries: 5,
        // 是否强制刷新状态
        forceRefresh: true
    };

    // 状态
    const STATE = {
        // 是否已初始化
        initialized: false,
        // 重试计数
        retryCount: 0,
        // 定时器ID
        timerId: null,
        // 已修复的维度
        fixedDimensions: new Set(),
        // 所有维度
        allDimensions: [
            'language_style',
            'rhythm_pacing',
            'structure',
            'sentence_variation',
            'paragraph_length',
            'perspective_shifts',
            'paragraph_flow',
            'novel_characteristics',
            'world_building',
            'chapter_outline',
            'character_relationships',
            'opening_effectiveness',
            'climax_pacing'
        ]
    };

    // 调试日志
    function debugLog(message, level = 'info') {
        if (CONFIG.debug) {
            const prefix = '[维度状态修复]';
            switch (level) {
                case 'error':
                    console.error(`${prefix} ${message}`);
                    break;
                case 'warn':
                    console.warn(`${prefix} ${message}`);
                    break;
                default:
                    console.log(`${prefix} ${message}`);
            }
        }
    }

    // 获取小说ID
    function getNovelId() {
        try {
            // 尝试从URL获取
            const match = window.location.pathname.match(/\/novel\/(\d+)/);
            if (match && match[1]) {
                return match[1];
            }

            // 尝试从DOM获取
            const novelContainer = document.querySelector('[data-novel-id]');
            if (novelContainer && novelContainer.dataset.novelId) {
                return novelContainer.dataset.novelId;
            }

            // 尝试从全局变量获取
            if (window.novelIdFromTemplate) {
                return window.novelIdFromTemplate;
            }

            debugLog('无法获取小说ID', 'warn');
            return null;
        } catch (e) {
            debugLog(`获取小说ID时出错: ${e.message}`, 'error');
            return null;
        }
    }

    // 检查维度状态
    function checkDimensionStatus() {
        const novelId = getNovelId();
        if (!novelId) {
            debugLog('无法获取小说ID，跳过检查', 'warn');
            return;
        }

        debugLog(`检查小说 ${novelId} 的维度状态`);

        // 检查每个维度的状态
        STATE.allDimensions.forEach(dimension => {
            checkSingleDimensionStatus(novelId, dimension);
        });
    }

    // 检查单个维度的状态
    function checkSingleDimensionStatus(novelId, dimension) {
        debugLog(`检查维度 ${dimension} 的状态`);

        // 获取维度的分析进度
        fetch(`/api/novel/${novelId}/analysis/${dimension}/progress`)
            .then(response => {
                if (!response.ok) {
                    // 如果API请求失败，尝试备用方法：检查分析结果是否存在
                    debugLog(`API请求失败(${response.status})，尝试备用检查方法`);
                    return fetch(`/api/novel/${novelId}/analysis/${dimension}`)
                        .then(resultResponse => {
                            if (resultResponse.ok) {
                                return { success: true, status: 'completed', progress: 100 };
                            } else {
                                throw new Error(`备用检查也失败: ${resultResponse.status}`);
                            }
                        });
                }
                return response.json();
            })
            .then(data => {
                if (data && data.success) {
                    if (data.status === 'completed') {
                        debugLog(`维度 ${dimension} 已完成分析`);
                        updateDimensionStatus(dimension, true);
                    } else {
                        debugLog(`维度 ${dimension} 正在分析中，进度: ${data.progress}%`);
                        // 如果正在分析中，更新进度条
                        updateDimensionProgress(dimension, data.progress);
                    }
                } else if (data) {
                    // 检查结果本身是否包含内容，如果包含则认为已完成
                    if (data.content || (data.result && data.result.content)) {
                        debugLog(`维度 ${dimension} 已有内容，标记为已完成`);
                        updateDimensionStatus(dimension, true);
                    } else {
                        debugLog(`获取维度 ${dimension} 的状态失败: ${data.error || '未知错误'}`, 'warn');
                    }
                } else {
                    debugLog(`获取维度 ${dimension} 的状态失败: 返回数据为空`, 'warn');
                }
            })
            .catch(error => {
                debugLog(`检查维度 ${dimension} 状态时出错: ${error.message}`, 'error');
                
                // 在API出错的情况下尝试直接检查页面中的内容
                tryDetectCompletionFromDOM(dimension);
            });
    }

    // 从DOM中尝试检测分析是否完成
    function tryDetectCompletionFromDOM(dimension) {
        try {
            // 检查是否有分析结果显示在页面上
            const resultContainer = document.querySelector(`[data-analysis-result="${dimension}"]`);
            if (resultContainer && resultContainer.textContent.trim().length > 10) {
                debugLog(`通过DOM检测到维度 ${dimension} 已有分析结果`);
                updateDimensionStatus(dimension, true);
                return;
            }
            
            // 检查"查看详情"链接是否存在
            const viewDetailLink = document.querySelector(`a[href*="/analysis/${dimension}"]`);
            if (viewDetailLink) {
                debugLog(`通过"查看详情"链接检测到维度 ${dimension} 可能已完成`);
                updateDimensionStatus(dimension, true);
                return;
            }
            
            debugLog(`无法从DOM中检测维度 ${dimension} 的完成状态`);
        } catch (e) {
            debugLog(`从DOM检测分析状态时出错: ${e.message}`, 'error');
        }
    }

    // 更新维度状态
    function updateDimensionStatus(dimension, isAnalyzed) {
        try {
            // 如果已经修复过这个维度，跳过
            if (STATE.fixedDimensions.has(dimension)) {
                return;
            }

            // 查找维度行
            const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
            if (!row) {
                debugLog(`找不到维度 ${dimension} 的行`, 'warn');
                return;
            }

            // 查找状态单元格
            const statusCell = row.querySelector('.dimension-status');
            if (!statusCell) {
                debugLog(`找不到维度 ${dimension} 的状态单元格`, 'warn');
                return;
            }

            // 检查当前状态
            const currentStatus = statusCell.textContent.trim();
            const isUnknown = currentStatus.includes('未知');

            // 如果状态为"未知"且已分析完成，更新状态
            if ((isUnknown || currentStatus.includes('进行中')) && isAnalyzed) {
                debugLog(`更新维度 ${dimension} 的状态到"已完成"`);

                // 更新状态单元格
                statusCell.innerHTML = '<span class="badge bg-success">已完成</span>';

                // 更新进度条
                const progressCell = row.querySelector('.dimension-progress');
                if (progressCell) {
                    const progressBar = progressCell.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = '100%';
                        progressBar.setAttribute('aria-valuenow', '100');
                        progressBar.textContent = '100%';
                        progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                    }
                }

                // 更新操作按钮
                const actionsCell = row.querySelector('.dimension-actions');
                if (actionsCell) {
                    const novelId = getNovelId();
                    actionsCell.innerHTML = `
                        <button class="btn btn-sm btn-outline-secondary reanalyze-btn"
                                data-dimension="${dimension}"
                                data-novel-id="${novelId}">
                            <i class="fas fa-sync"></i> 重新分析
                        </button>
                        <a href="/novel/${novelId}/analysis/${dimension}"
                           class="btn btn-sm btn-outline-primary ms-1">
                            查看详情
                        </a>
                    `;
                }

                // 标记为已修复
                STATE.fixedDimensions.add(dimension);

                // 重新绑定按钮事件
                bindButtonEvents();

                // 更新分析卡片（如果存在）
                updateAnalysisCard(dimension, true);
            }
        } catch (e) {
            debugLog(`更新维度 ${dimension} 状态时出错: ${e.message}`, 'error');
        }
    }

    // 更新维度进度
    function updateDimensionProgress(dimension, progress) {
        try {
            // 查找维度行
            const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
            if (!row) {
                debugLog(`找不到维度 ${dimension} 的行`, 'warn');
                return;
            }

            // 查找状态单元格
            const statusCell = row.querySelector('.dimension-status');
            if (!statusCell) {
                debugLog(`找不到维度 ${dimension} 的状态单元格`, 'warn');
                return;
            }

            // 检查当前状态
            const currentStatus = statusCell.textContent.trim();
            const isCompleted = currentStatus.includes('已完成');

            // 如果已完成，不更新进度
            if (isCompleted) {
                return;
            }

            // 更新状态单元格
            statusCell.innerHTML = '<span class="badge bg-info">进行中</span>';

            // 更新进度条
            const progressCell = row.querySelector('.dimension-progress');
            if (progressCell) {
                const progressBar = progressCell.querySelector('.progress-bar');
                if (progressBar) {
                    // 确保进度至少为10%，以便用户能看到进度条
                    const displayProgress = Math.max(10, progress);
                    progressBar.style.width = `${displayProgress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                    progressBar.textContent = `${progress}%`;
                    progressBar.classList.add('progress-bar-striped', 'progress-bar-animated');
                }
            }

            // 更新操作按钮
            const actionsCell = row.querySelector('.dimension-actions');
            if (actionsCell) {
                const novelId = getNovelId();
                actionsCell.innerHTML = `
                    <button class="btn btn-sm btn-outline-danger cancel-btn"
                            data-dimension="${dimension}"
                            data-novel-id="${novelId}">
                        <i class="fas fa-times"></i> 取消
                    </button>
                `;

                // 绑定取消按钮事件
                const cancelBtn = actionsCell.querySelector('.cancel-btn');
                if (cancelBtn) {
                    cancelBtn.addEventListener('click', function() {
                        cancelAnalysis(novelId, dimension);
                    });
                }
            }

            // 更新分析卡片（如果存在）
            updateAnalysisCard(dimension, false, progress);
        } catch (e) {
            debugLog(`更新维度 ${dimension} 进度时出错: ${e.message}`, 'error');
        }
    }

    // 取消分析
    function cancelAnalysis(novelId, dimension) {
        debugLog(`取消维度 ${dimension} 的分析`);

        fetch(`/api/novel/${novelId}/analyze/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ dimension: dimension })
        })
        .then(response => response.json())
        .then(data => {
            if (data && data.success) {
                debugLog(`成功取消维度 ${dimension} 的分析`);

                // 重置维度状态
                const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
                if (row) {
                    const statusCell = row.querySelector('.dimension-status');
                    if (statusCell) {
                        statusCell.innerHTML = '<span class="badge bg-secondary">未知</span>';
                    }

                    const progressCell = row.querySelector('.dimension-progress');
                    if (progressCell) {
                        const progressBar = progressCell.querySelector('.progress-bar');
                        if (progressBar) {
                            progressBar.style.width = '0%';
                            progressBar.setAttribute('aria-valuenow', '0');
                            progressBar.textContent = '0%';
                            progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        }
                    }

                    const actionsCell = row.querySelector('.dimension-actions');
                    if (actionsCell) {
                        actionsCell.innerHTML = `
                            <button class="btn btn-sm btn-outline-primary analyze-btn"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelId}">
                                分析
                            </button>
                            <a href="/novel/${novelId}/analysis/${dimension}"
                               class="btn btn-sm btn-outline-secondary ms-1 view-btn"
                               data-dimension="${dimension}"
                               data-novel-id="${novelId}">
                                查看
                            </a>
                        `;

                        // 重新绑定按钮事件
                        bindButtonEvents();
                    }
                }
            } else {
                debugLog(`取消维度 ${dimension} 的分析失败: ${data.error || '未知错误'}`, 'error');
            }
        })
        .catch(error => {
            debugLog(`取消维度 ${dimension} 的分析时出错: ${error.message}`, 'error');
        });
    }

    // 更新分析卡片
    function updateAnalysisCard(dimension, isCompleted, progress = 0) {
        try {
            // 查找分析卡片
            const card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
            if (!card) {
                // 如果找不到卡片，可能是因为页面结构不同，不报错
                return;
            }

            const cardBody = card.querySelector('.card-body');
            if (!cardBody) {
                return;
            }

            if (isCompleted) {
                // 如果分析已完成，更新卡片内容
                const novelId = getNovelId();

                // 检查是否已经有"分析完成"标记
                if (!cardBody.querySelector('.badge.bg-success')) {
                    // 添加"分析完成"标记和按钮
                    const statusDiv = document.createElement('div');
                    statusDiv.className = 'd-flex justify-content-between mb-2';
                    statusDiv.innerHTML = `
                        <span class="badge bg-success">分析完成</span>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelId}">
                                <i class="fas fa-sync"></i> 重新分析
                            </button>
                            <a href="/novel/${novelId}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                        </div>
                    `;

                    // 清空卡片内容并添加新内容
                    cardBody.innerHTML = '';
                    cardBody.appendChild(statusDiv);

                    // 添加分析摘要（这里需要从服务器获取，但为简化起见，添加一个占位符）
                    const excerptDiv = document.createElement('div');
                    excerptDiv.className = 'analysis-excerpt mt-2';
                    excerptDiv.innerHTML = '<p>分析已完成，点击"查看详情"查看完整分析结果。</p>';
                    cardBody.appendChild(excerptDiv);

                    // 重新绑定按钮事件
                    bindButtonEvents();
                }
            } else {
                // 如果分析正在进行中，更新进度条
                // 检查是否已经有进度条
                let progressDiv = cardBody.querySelector('.analysis-progress');

                if (!progressDiv) {
                    // 创建进度条
                    cardBody.innerHTML = '';

                    progressDiv = document.createElement('div');
                    progressDiv.className = 'analysis-progress';
                    progressDiv.innerHTML = `
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                 aria-valuenow="${progress}"
                                 aria-valuemin="0"
                                 aria-valuemax="100"
                                 style="width: ${Math.max(10, progress)}%">
                                ${progress}%
                            </div>
                        </div>
                        <div class="analysis-status">
                            <span>分析中...</span>
                            <span>预计剩余时间: 计算中...</span>
                        </div>
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">正在分析中，请稍候...</p>
                        </div>
                    `;

                    cardBody.appendChild(progressDiv);
                } else {
                    // 更新现有进度条
                    const progressBar = progressDiv.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = `${Math.max(10, progress)}%`;
                        progressBar.setAttribute('aria-valuenow', progress);
                        progressBar.textContent = `${progress}%`;
                    }
                }
            }
        } catch (e) {
            debugLog(`更新维度 ${dimension} 的分析卡片时出错: ${e.message}`, 'error');
        }
    }

    // 绑定按钮事件
    function bindButtonEvents() {
        try {
            // 绑定重新分析按钮事件
            document.querySelectorAll('.reanalyze-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const dimension = this.getAttribute('data-dimension');
                    const novelId = this.getAttribute('data-novel-id');
                    if (dimension && novelId) {
                        debugLog(`点击了维度 ${dimension} 的重新分析按钮`);
                        if (window.startSingleDimensionAnalysis) {
                            window.startSingleDimensionAnalysis(novelId, dimension);
                        }
                    }
                });
            });
        } catch (e) {
            debugLog(`绑定按钮事件时出错: ${e.message}`, 'error');
        }
    }

    // 处理重试
    function handleRetry() {
        STATE.retryCount++;
        if (STATE.retryCount <= CONFIG.maxRetries) {
            debugLog(`第 ${STATE.retryCount} 次重试，共 ${CONFIG.maxRetries} 次`);
        } else {
            debugLog(`已达到最大重试次数 ${CONFIG.maxRetries}，停止重试`, 'warn');
            clearInterval(STATE.timerId);
        }
    }

    // 强制更新所有维度状态
    function forceUpdateAllDimensions() {
        if (!CONFIG.forceRefresh) {
            return;
        }

        debugLog('强制更新所有维度状态');

        // 遍历所有维度
        STATE.allDimensions.forEach(dimension => {
            // 查找维度行
            const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
            if (!row) {
                return;
            }

            // 查找状态单元格
            const statusCell = row.querySelector('.dimension-status');
            if (!statusCell) {
                return;
            }

            // 检查当前状态
            const currentStatus = statusCell.textContent.trim();
            const isUnknown = currentStatus.includes('未知');

            // 如果状态为"未知"，检查该维度是否已分析完成
            if (isUnknown) {
                const novelId = getNovelId();
                if (novelId) {
                    fetch(`/api/analysis/result/${novelId}/${dimension}`)
                        .then(response => {
                            if (response.ok) {
                                return response.json();
                            }
                            throw new Error(`状态码: ${response.status}`);
                        })
                        .then(data => {
                            if (data && data.success) {
                                updateDimensionStatus(dimension, true);
                            }
                        })
                        .catch(error => {
                            debugLog(`检查维度 ${dimension} 状态时出错: ${error.message}`, 'warn');
                        });
                }
            }
        });
    }

    // 初始化
    function initialize() {
        if (STATE.initialized) {
            debugLog('已经初始化过，跳过');
            return;
        }

        debugLog('开始初始化维度状态修复');

        // 绑定按钮事件
        bindButtonEvents();

        // 立即检查一次
        checkDimensionStatus();
        
        // 立即尝试强制更新所有维度
        forceUpdateAllDimensions();

        // 设置定时检查
        STATE.timerId = setInterval(() => {
            if (STATE.retryCount < CONFIG.maxRetries) {
                debugLog(`定时检查维度状态 (${STATE.retryCount + 1}/${CONFIG.maxRetries})`);
                checkDimensionStatus();
                STATE.retryCount++;
            } else {
                debugLog('已达到最大重试次数，停止定时检查');
                if (STATE.timerId) {
                    clearInterval(STATE.timerId);
                    STATE.timerId = null;
                }
            }
        }, CONFIG.checkInterval);

        STATE.initialized = true;
        debugLog('维度状态修复初始化完成');
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
})();
