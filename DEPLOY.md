
# 部署说明

## 前置条件
1. 确保服务器上已安装 Python 3.x
2. 确保有 sudo 权限
3. 服务器需要开放 80 端口

## 部署步骤
1. 将项目文件传输到服务器：
   ```bash
   scp -r ./* username@115.190.28.157:/var/www/jiuma/
   ```

2. SSH 登录到服务器：
   ```bash
   ssh username@115.190.28.157
   ```

3. 执行部署脚本：
   ```bash
   cd /var/www/jiuma
   chmod +x deploy.sh
   ./deploy.sh
   ```

4. 检查服务状态：
   ```bash
   sudo systemctl status jiuma
   ```

## 常见问题排查
1. 如果网站无法访问，检查防火墙设置：
   ```bash
   sudo ufw status
   sudo ufw allow 80
   ```

2. 查看应用日志：
   ```bash
   sudo journalctl -u jiuma
   ```

3. 查看 Nginx 日志：
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```
