@echo off
chcp 65001 > nul
echo ===================================
echo   九猫系统网络诊断工具
echo ===================================
echo.

echo 正在检查网络连接...
ping 127.0.0.1 -n 4
if %errorlevel% equ 0 (
    echo 本地网络连接正常
) else (
    echo 警告：本地网络连接异常
)

echo.
echo 正在检查端口5001...
netstat -ano | findstr :5001
if %errorlevel% equ 0 (
    echo 端口5001被占用，以下是占用该端口的进程：
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5001') do (
        echo 进程ID: %%a
        tasklist | findstr %%a
    )
) else (
    echo 端口5001未被占用
)

echo.
echo 正在检查DNS解析...
ping localhost -n 4
if %errorlevel% equ 0 (
    echo localhost DNS解析正常
) else (
    echo 警告：localhost DNS解析异常
)

echo.
echo 正在检查hosts文件...
type C:\Windows\System32\drivers\etc\hosts | findstr localhost
if %errorlevel% equ 0 (
    echo hosts文件中存在localhost条目
) else (
    echo 警告：hosts文件中不存在localhost条目
)

echo.
echo 正在检查防火墙状态...
netsh advfirewall show allprofiles state
echo.

echo 诊断完成！
echo.
echo 如果您仍然遇到问题，请查看"浏览器连接问题解决方案.md"
echo.
pause
