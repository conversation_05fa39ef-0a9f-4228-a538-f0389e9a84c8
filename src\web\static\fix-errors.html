<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫系统 - 错误修复页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-success {
            background-color: #2ecc71;
        }
        .btn-success:hover {
            background-color: #27ae60;
        }
        .btn-warning {
            background-color: #f39c12;
        }
        .btn-warning:hover {
            background-color: #e67e22;
        }
        .btn-danger {
            background-color: #e74c3c;
        }
        .btn-danger:hover {
            background-color: #c0392b;
        }
        .log {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .log-info {
            color: #3498db;
        }
        .log-error {
            color: #e74c3c;
        }
        .log-warning {
            color: #f39c12;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>九猫系统 - 错误修复页面</h1>
    
    <div class="card">
        <div class="card-title">系统状态</div>
        <div id="systemStatus" class="status">正在检查系统状态...</div>
        
        <div>
            <button id="checkStatus" class="btn">检查状态</button>
            <button id="fixBootstrap" class="btn btn-warning">修复 Bootstrap</button>
            <button id="fixJQuery" class="btn btn-warning">修复 jQuery</button>
            <button id="reloadPage" class="btn btn-danger">重新加载页面</button>
        </div>
    </div>
    
    <div class="card">
        <div class="card-title">修复说明</div>
        <p>此页面用于修复九猫系统中的常见错误，特别是 Bootstrap 和 jQuery 相关的错误。</p>
        <p>主要修复内容：</p>
        <ul>
            <li>修复 bootstrap.bundle.min.js 中第6行第20744列处的语法错误</li>
            <li>修复 jQuery 相关错误</li>
            <li>修复其他常见 JavaScript 错误</li>
        </ul>
        <p>使用方法：</p>
        <ol>
            <li>点击"检查状态"按钮检查系统状态</li>
            <li>如果发现错误，点击相应的修复按钮</li>
            <li>修复完成后，点击"重新加载页面"按钮刷新页面</li>
        </ol>
    </div>
    
    <div class="card">
        <div class="card-title">修复日志</div>
        <div id="logContainer" class="log"></div>
    </div>
    
    <!-- 加载修复脚本 -->
    <script src="/static/js/global-error-handler.js"></script>
    <script src="/static/js/bootstrap-bundle-fix-loader.js"></script>
    <script src="/static/js/json-parse-position-4476-fix.js"></script>
    
    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 同时输出到控制台
            if (type === 'error') {
                console.error(message);
            } else if (type === 'warning') {
                console.warn(message);
            } else {
                console.log(message);
            }
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            const statusElement = document.getElementById('systemStatus');
            
            log('正在检查系统状态...');
            
            // 检查 Bootstrap
            let bootstrapStatus = '未知';
            if (typeof bootstrap !== 'undefined') {
                try {
                    // 尝试创建一个 Modal 实例
                    const modalElement = document.createElement('div');
                    modalElement.className = 'modal';
                    document.body.appendChild(modalElement);
                    
                    const modal = new bootstrap.Modal(modalElement);
                    modal.dispose();
                    document.body.removeChild(modalElement);
                    
                    bootstrapStatus = '正常';
                    log('Bootstrap 状态: 正常');
                } catch (e) {
                    bootstrapStatus = '异常: ' + e.message;
                    log('Bootstrap 状态: 异常 - ' + e.message, 'error');
                }
            } else {
                bootstrapStatus = '未加载';
                log('Bootstrap 状态: 未加载', 'warning');
            }
            
            // 检查 jQuery
            let jQueryStatus = '未知';
            if (typeof jQuery !== 'undefined') {
                try {
                    // 尝试使用 jQuery
                    const version = jQuery.fn.jquery;
                    jQueryStatus = '正常 (版本: ' + version + ')';
                    log('jQuery 状态: 正常 (版本: ' + version + ')');
                } catch (e) {
                    jQueryStatus = '异常: ' + e.message;
                    log('jQuery 状态: 异常 - ' + e.message, 'error');
                }
            } else {
                jQueryStatus = '未加载';
                log('jQuery 状态: 未加载', 'warning');
            }
            
            // 更新状态显示
            if (bootstrapStatus === '正常' && jQueryStatus.startsWith('正常')) {
                statusElement.className = 'status status-success';
                statusElement.innerHTML = `
                    <strong>系统状态: 正常</strong><br>
                    Bootstrap: ${bootstrapStatus}<br>
                    jQuery: ${jQueryStatus}
                `;
            } else if (bootstrapStatus === '未加载' || jQueryStatus === '未加载') {
                statusElement.className = 'status status-warning';
                statusElement.innerHTML = `
                    <strong>系统状态: 需要修复</strong><br>
                    Bootstrap: ${bootstrapStatus}<br>
                    jQuery: ${jQueryStatus}
                `;
            } else {
                statusElement.className = 'status status-error';
                statusElement.innerHTML = `
                    <strong>系统状态: 异常</strong><br>
                    Bootstrap: ${bootstrapStatus}<br>
                    jQuery: ${jQueryStatus}
                `;
            }
        }
        
        // 修复 Bootstrap
        function fixBootstrap() {
            log('开始修复 Bootstrap...');
            
            if (typeof window.fixBootstrapBundle === 'function') {
                window.fixBootstrapBundle();
                log('已调用 Bootstrap 修复函数');
            } else {
                log('未找到 Bootstrap 修复函数，尝试加载修复脚本', 'warning');
                
                // 加载修复脚本
                const script = document.createElement('script');
                script.src = '/static/js/bootstrap-bundle-fix-loader.js';
                
                script.onload = function() {
                    log('Bootstrap 修复脚本加载成功');
                    
                    if (typeof window.fixBootstrapBundle === 'function') {
                        window.fixBootstrapBundle();
                        log('已调用 Bootstrap 修复函数');
                    } else {
                        log('加载修复脚本后仍未找到 Bootstrap 修复函数', 'error');
                    }
                };
                
                script.onerror = function() {
                    log('Bootstrap 修复脚本加载失败', 'error');
                };
                
                document.head.appendChild(script);
            }
            
            // 延迟检查状态
            setTimeout(checkSystemStatus, 2000);
        }
        
        // 修复 jQuery
        function fixJQuery() {
            log('开始修复 jQuery...');
            
            if (typeof jQuery !== 'undefined') {
                log('jQuery 已加载，版本: ' + jQuery.fn.jquery);
            } else {
                log('jQuery 未加载，尝试加载', 'warning');
                
                // 加载 jQuery
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';
                
                script.onload = function() {
                    log('jQuery 加载成功，版本: ' + jQuery.fn.jquery);
                    window.$ = jQuery;
                };
                
                script.onerror = function() {
                    log('jQuery 加载失败', 'error');
                };
                
                document.head.appendChild(script);
            }
            
            // 延迟检查状态
            setTimeout(checkSystemStatus, 2000);
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            
            // 初始检查
            setTimeout(checkSystemStatus, 1000);
            
            // 绑定按钮事件
            document.getElementById('checkStatus').addEventListener('click', checkSystemStatus);
            document.getElementById('fixBootstrap').addEventListener('click', fixBootstrap);
            document.getElementById('fixJQuery').addEventListener('click', fixJQuery);
            document.getElementById('reloadPage').addEventListener('click', function() {
                log('重新加载页面...');
                window.location.reload();
            });
        });
    </script>
</body>
</html>
