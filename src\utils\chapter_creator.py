"""
章节创建工具，负责为小说创建章节
"""
import logging
import re
from typing import List, Dict, Any, Tuple

from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter

logger = logging.getLogger(__name__)

class ChapterCreator:
    """章节创建工具类"""
    
    @staticmethod
    def create_chapters_for_novel(novel_id: int) -> Tuple[bool, List[Dict[str, Any]], str]:
        """
        为小说创建章节
        
        Args:
            novel_id: 小说ID
            
        Returns:
            成功/失败标志, 章节列表, 错误消息
        """
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).filter_by(id=novel_id).first()
            if not novel:
                return False, [], "小说不存在"
                
            # 检查是否已有章节
            existing_chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
            if existing_chapters:
                chapter_list = [ch.get_summary() for ch in existing_chapters]
                return True, chapter_list, f"已存在{len(existing_chapters)}个章节"
            
            # 分割章节
            chapters_data = ChapterCreator.split_content(novel.content)
            
            # 创建章节记录
            created_chapters = []
            for i, chapter_data in enumerate(chapters_data):
                chapter = Chapter(
                    novel_id=novel_id,
                    chapter_number=i+1,
                    title=chapter_data.get('title', f'第{i+1}章'),
                    content=chapter_data.get('content', '')
                )
                session.add(chapter)
                created_chapters.append(chapter)
            
            # 提交事务
            session.commit()
            
            # 返回创建的章节
            chapter_list = [ch.get_summary() for ch in created_chapters]
            return True, chapter_list, f"成功创建{len(created_chapters)}个章节"
            
        except Exception as e:
            logger.error(f"创建章节时出错: {str(e)}")
            session.rollback()
            return False, [], f"创建章节失败: {str(e)}"
        finally:
            session.close()
    
    @staticmethod
    def split_content(content: str) -> List[Dict[str, Any]]:
        """
        将内容分割成章节
        
        Args:
            content: 小说内容
            
        Returns:
            章节列表，每个章节包含标题和内容
        """
        # 常见章节标题模式
        patterns = [
            r'第\s*[0-9一二三四五六七八九十百千万]+\s*章.*?\n',
            r'Chapter\s*[0-9]+.*?\n',
            r'第\s*[0-9一二三四五六七八九十百千万]+\s*节.*?\n',
            r'第\s*[0-9一二三四五六七八九十百千万]+\s*回.*?\n',
            r'Chapter\s*[IVXLCDM]+.*?\n'
        ]
        
        # 合并模式
        combined_pattern = '|'.join(patterns)
        
        # 查找所有匹配的章节标题
        matches = re.finditer(combined_pattern, content, re.IGNORECASE)
        
        # 获取所有匹配位置
        positions = []
        for match in matches:
            positions.append((match.start(), match.group().strip()))
        
        # 如果没有找到章节标记，尝试按照段落分割
        if not positions:
            paragraphs = content.split('\n\n')
            result = []
            
            # 每5个段落作为一个章节
            for i in range(0, len(paragraphs), 5):
                chunk = paragraphs[i:i+5]
                chapter_content = '\n\n'.join(chunk)
                result.append({
                    'title': f'第{i//5+1}章',
                    'content': chapter_content
                })
            
            return result
        
        # 根据位置分割内容
        chapters = []
        for i in range(len(positions)):
            start_pos, title = positions[i]
            end_pos = positions[i+1][0] if i < len(positions) - 1 else len(content)
            
            chapter_content = content[start_pos:end_pos]
            chapters.append({
                'title': title,
                'content': chapter_content
            })
        
        return chapters 