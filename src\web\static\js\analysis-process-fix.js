/**
 * 九猫 - 分析过程展示修复脚本
 * 专门修复所有维度的分析过程展示问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析过程展示修复脚本已加载');

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复分析过程展示');
        
        // 检测当前页面类型
        detectPageType();
        
        // 修复分析过程展示
        fixAnalysisProcessDisplay();
        
        // 修复分析过程折叠功能
        fixAnalysisProcessCollapse();
        
        // 添加展开/折叠所有按钮
        addExpandCollapseAllButtons();
    });
    
    // 检测当前页面类型
    function detectPageType() {
        try {
            // 获取当前URL路径
            const path = window.location.pathname;
            console.log('当前页面路径: ' + path);
            
            // 检查是否是分析结果页面
            if (path.includes('/analysis/') || 
                path.includes('/character_relationships') || 
                path.includes('/chapter_outline') || 
                path.includes('/climax_rhythm') || 
                path.includes('/language_style') || 
                path.includes('/paragraph_flow') || 
                path.includes('/rhythm_pacing') || 
                path.includes('/sentence_variation') || 
                path.includes('/structure_analysis') || 
                path.includes('/theme_exploration')) {
                
                console.log('检测到分析结果页面');
                window.isAnalysisPage = true;
                
                // 获取分析维度
                const pathParts = path.split('/');
                for (let i = 0; i < pathParts.length; i++) {
                    if (pathParts[i] === 'analysis' && i + 1 < pathParts.length) {
                        window.analysisDimension = pathParts[i + 1];
                        console.log('分析维度: ' + window.analysisDimension);
                        break;
                    } else if (['character_relationships', 'chapter_outline', 'climax_rhythm', 
                               'language_style', 'paragraph_flow', 'rhythm_pacing', 
                               'sentence_variation', 'structure_analysis', 'theme_exploration'].includes(pathParts[i])) {
                        window.analysisDimension = pathParts[i];
                        console.log('分析维度: ' + window.analysisDimension);
                        break;
                    }
                }
            } else {
                console.log('不是分析结果页面，跳过修复');
                window.isAnalysisPage = false;
            }
        } catch (e) {
            console.error('检测页面类型时出错:', e);
        }
    }
    
    // 修复分析过程展示
    function fixAnalysisProcessDisplay() {
        try {
            if (!window.isAnalysisPage) {
                return;
            }
            
            console.log('修复分析过程展示');
            
            // 查找分析过程内容
            const analysisProcessElements = document.querySelectorAll('.analysis-process-card, #analysisProcessCollapse, .analysis-logs');
            if (analysisProcessElements.length === 0) {
                console.log('未找到分析过程元素，尝试查找其他可能的元素');
                
                // 查找可能包含分析过程的元素
                const possibleElements = document.querySelectorAll('.card-body pre, .markdown-content pre, .analysis-content pre');
                if (possibleElements.length > 0) {
                    console.log('找到可能包含分析过程的元素: ' + possibleElements.length + '个');
                    
                    // 检查是否需要创建折叠区域
                    const needsCollapse = !document.querySelector('#analysisProcessCollapse');
                    if (needsCollapse) {
                        console.log('需要创建折叠区域');
                        
                        // 创建折叠区域
                        createAnalysisProcessCollapse(possibleElements);
                    }
                }
            } else {
                console.log('找到分析过程元素: ' + analysisProcessElements.length + '个');
                
                // 确保所有分析过程元素都可见
                analysisProcessElements.forEach(function(element) {
                    // 如果元素在折叠区域内，确保折叠区域可以正常工作
                    const collapseParent = element.closest('.collapse');
                    if (collapseParent) {
                        console.log('分析过程元素在折叠区域内，确保折叠区域可以正常工作');
                        
                        // 确保折叠区域有正确的类
                        if (!collapseParent.classList.contains('collapse')) {
                            collapseParent.classList.add('collapse');
                            console.log('添加了collapse类');
                        }
                        
                        // 查找对应的按钮
                        const collapseId = collapseParent.id;
                        let collapseButton = document.querySelector('[data-bs-target="#' + collapseId + '"], [data-target="#' + collapseId + '"]');
                        
                        if (!collapseButton) {
                            console.log('未找到折叠按钮，尝试创建');
                            
                            // 查找可能的父元素
                            const header = collapseParent.previousElementSibling;
                            if (header && header.classList.contains('card-header')) {
                                // 创建新按钮
                                const newButton = document.createElement('button');
                                newButton.className = 'btn btn-link';
                                newButton.setAttribute('type', 'button');
                                newButton.setAttribute('data-bs-toggle', 'collapse');
                                newButton.setAttribute('data-bs-target', '#' + collapseId);
                                newButton.setAttribute('aria-expanded', 'false');
                                newButton.setAttribute('aria-controls', collapseId);
                                newButton.textContent = collapseId.includes('Process') ? '分析过程' : '分析日志';
                                
                                // 添加到页面
                                const h5 = document.createElement('h5');
                                h5.className = 'mb-0';
                                h5.appendChild(newButton);
                                header.innerHTML = '';
                                header.appendChild(h5);
                                
                                console.log('创建了新的折叠按钮');
                                
                                // 添加点击事件
                                newButton.addEventListener('click', function(event) {
                                    event.preventDefault();
                                    collapseParent.classList.toggle('show');
                                    newButton.setAttribute('aria-expanded', collapseParent.classList.contains('show') ? 'true' : 'false');
                                });
                            }
                        }
                    }
                    
                    // 确保元素可见
                    if (element.style.display === 'none') {
                        element.style.display = '';
                        console.log('显示了隐藏的分析过程元素');
                    }
                });
            }
        } catch (e) {
            console.error('修复分析过程展示时出错:', e);
        }
    }
    
    // 创建分析过程折叠区域
    function createAnalysisProcessCollapse(elements) {
        try {
            // 查找合适的容器
            const container = document.querySelector('.container > .row > .col-md-8, .container > .row > .col-md-9, .container > .row > .col-md-12');
            if (!container) {
                console.log('未找到合适的容器，跳过创建折叠区域');
                return;
            }
            
            console.log('找到合适的容器，创建折叠区域');
            
            // 创建折叠区域
            const collapseCard = document.createElement('div');
            collapseCard.className = 'card mt-4';
            
            // 创建卡片标题
            const cardHeader = document.createElement('div');
            cardHeader.className = 'card-header';
            cardHeader.id = 'analysisProcessHeader';
            
            // 创建标题内容
            const title = document.createElement('h5');
            title.className = 'mb-0';
            
            // 创建折叠按钮
            const collapseButton = document.createElement('button');
            collapseButton.className = 'btn btn-link';
            collapseButton.setAttribute('type', 'button');
            collapseButton.setAttribute('data-bs-toggle', 'collapse');
            collapseButton.setAttribute('data-bs-target', '#analysisProcessCollapse');
            collapseButton.setAttribute('aria-expanded', 'false');
            collapseButton.setAttribute('aria-controls', 'analysisProcessCollapse');
            collapseButton.textContent = '分析过程';
            
            // 组装标题
            title.appendChild(collapseButton);
            cardHeader.appendChild(title);
            
            // 创建折叠内容
            const collapseContent = document.createElement('div');
            collapseContent.id = 'analysisProcessCollapse';
            collapseContent.className = 'collapse';
            collapseContent.setAttribute('aria-labelledby', 'analysisProcessHeader');
            
            // 创建卡片内容
            const cardBody = document.createElement('div');
            cardBody.className = 'card-body';
            
            // 将找到的元素添加到卡片内容
            elements.forEach(function(element, index) {
                // 创建分析步骤卡片
                const stepCard = document.createElement('div');
                stepCard.className = 'analysis-process-card mb-3';
                
                // 创建步骤标题
                const stepHeader = document.createElement('div');
                stepHeader.className = 'card-header';
                stepHeader.innerHTML = '<strong>步骤 ' + (index + 1) + ':</strong> 分析内容';
                
                // 创建步骤内容
                const stepBody = document.createElement('div');
                stepBody.className = 'card-body';
                
                // 创建内容容器
                const pre = document.createElement('pre');
                pre.className = 'mb-0';
                pre.textContent = element.textContent;
                
                // 组装步骤卡片
                stepBody.appendChild(pre);
                stepCard.appendChild(stepHeader);
                stepCard.appendChild(stepBody);
                
                // 添加到卡片内容
                cardBody.appendChild(stepCard);
            });
            
            // 组装折叠内容
            collapseContent.appendChild(cardBody);
            
            // 组装卡片
            collapseCard.appendChild(cardHeader);
            collapseCard.appendChild(collapseContent);
            
            // 添加到容器
            container.appendChild(collapseCard);
            
            console.log('创建了分析过程折叠区域');
            
            // 添加点击事件
            collapseButton.addEventListener('click', function(event) {
                event.preventDefault();
                collapseContent.classList.toggle('show');
                collapseButton.setAttribute('aria-expanded', collapseContent.classList.contains('show') ? 'true' : 'false');
            });
        } catch (e) {
            console.error('创建分析过程折叠区域时出错:', e);
        }
    }
    
    // 修复分析过程折叠功能
    function fixAnalysisProcessCollapse() {
        try {
            if (!window.isAnalysisPage) {
                return;
            }
            
            console.log('修复分析过程折叠功能');
            
            // 查找所有折叠按钮
            const collapseButtons = document.querySelectorAll('[data-bs-toggle="collapse"], [data-toggle="collapse"]');
            console.log('找到 ' + collapseButtons.length + ' 个折叠按钮');
            
            // 修复每个折叠按钮
            collapseButtons.forEach(function(button) {
                // 确保按钮有正确的属性
                if (button.hasAttribute('data-toggle') && !button.hasAttribute('data-bs-toggle')) {
                    // 从Bootstrap 4升级到Bootstrap 5
                    const target = button.getAttribute('data-toggle');
                    if (target === 'collapse') {
                        console.log('将data-toggle="collapse"转换为data-bs-toggle="collapse"');
                        button.setAttribute('data-bs-toggle', 'collapse');
                        
                        // 更新目标属性
                        if (button.hasAttribute('data-target')) {
                            const targetSelector = button.getAttribute('data-target');
                            button.setAttribute('data-bs-target', targetSelector);
                        }
                    }
                }
                
                // 确保按钮有点击事件处理程序
                if (!button._hasCollapseHandler) {
                    button.addEventListener('click', function(event) {
                        event.preventDefault();
                        
                        // 获取目标元素
                        const targetSelector = button.getAttribute('data-bs-target') || button.getAttribute('data-target');
                        if (!targetSelector) {
                            console.warn('折叠按钮没有目标选择器');
                            return;
                        }
                        
                        const targetElement = document.querySelector(targetSelector);
                        if (!targetElement) {
                            console.warn('找不到折叠目标元素: ' + targetSelector);
                            return;
                        }
                        
                        console.log('切换折叠状态: ' + targetSelector);
                        
                        // 手动切换折叠状态
                        targetElement.classList.toggle('show');
                        
                        // 更新aria-expanded属性
                        const isExpanded = targetElement.classList.contains('show');
                        button.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
                    });
                    
                    button._hasCollapseHandler = true;
                    console.log('为折叠按钮添加了点击事件处理程序');
                }
            });
            
            // 查找所有折叠区域
            const collapseElements = document.querySelectorAll('.collapse');
            console.log('找到 ' + collapseElements.length + ' 个折叠区域');
            
            // 修复每个折叠区域
            collapseElements.forEach(function(element) {
                // 确保折叠区域有正确的类
                if (!element.classList.contains('collapse')) {
                    element.classList.add('collapse');
                    console.log('添加了collapse类');
                }
                
                // 查找对应的按钮
                const id = element.id;
                if (id) {
                    const button = document.querySelector('[data-bs-target="#' + id + '"], [data-target="#' + id + '"]');
                    if (!button) {
                        console.log('未找到折叠按钮，尝试创建: ' + id);
                        
                        // 查找可能的父元素
                        const header = element.previousElementSibling;
                        if (header && header.classList.contains('card-header')) {
                            // 创建新按钮
                            const newButton = document.createElement('button');
                            newButton.className = 'btn btn-link';
                            newButton.setAttribute('type', 'button');
                            newButton.setAttribute('data-bs-toggle', 'collapse');
                            newButton.setAttribute('data-bs-target', '#' + id);
                            newButton.setAttribute('aria-expanded', 'false');
                            newButton.setAttribute('aria-controls', id);
                            newButton.textContent = id.includes('Process') ? '分析过程' : '分析日志';
                            
                            // 添加到页面
                            const h5 = document.createElement('h5');
                            h5.className = 'mb-0';
                            h5.appendChild(newButton);
                            header.innerHTML = '';
                            header.appendChild(h5);
                            
                            console.log('创建了新的折叠按钮');
                            
                            // 添加点击事件
                            newButton.addEventListener('click', function(event) {
                                event.preventDefault();
                                element.classList.toggle('show');
                                newButton.setAttribute('aria-expanded', element.classList.contains('show') ? 'true' : 'false');
                            });
                        }
                    }
                }
            });
        } catch (e) {
            console.error('修复分析过程折叠功能时出错:', e);
        }
    }
    
    // 添加展开/折叠所有按钮
    function addExpandCollapseAllButtons() {
        try {
            if (!window.isAnalysisPage) {
                return;
            }
            
            console.log('添加展开/折叠所有按钮');
            
            // 检查是否已经有展开所有和折叠所有按钮
            if (document.querySelector('.collapse-all-btn')) {
                console.log('已存在展开所有和折叠所有按钮，跳过创建');
                return;
            }
            
            // 查找所有折叠区域
            const collapseElements = document.querySelectorAll('.collapse');
            if (collapseElements.length === 0) {
                console.log('未找到折叠区域，跳过创建展开所有和折叠所有按钮');
                return;
            }
            
            // 创建按钮容器
            const container = document.createElement('div');
            container.className = 'text-end mt-3 mb-3';
            
            // 创建展开所有按钮
            const expandAllButton = document.createElement('button');
            expandAllButton.className = 'btn btn-sm btn-outline-primary collapse-all-btn me-2';
            expandAllButton.textContent = '展开所有分析过程';
            expandAllButton.onclick = function() {
                // 展开所有折叠区域
                collapseElements.forEach(function(element) {
                    if (!element.classList.contains('show')) {
                        element.classList.add('show');
                        
                        // 更新对应按钮的aria-expanded属性
                        const id = element.id;
                        if (id) {
                            const button = document.querySelector('[data-bs-target="#' + id + '"], [data-target="#' + id + '"]');
                            if (button) {
                                button.setAttribute('aria-expanded', 'true');
                            }
                        }
                    }
                });
                
                console.log('展开了所有折叠区域');
            };
            
            // 创建折叠所有按钮
            const collapseAllButton = document.createElement('button');
            collapseAllButton.className = 'btn btn-sm btn-outline-secondary collapse-all-btn';
            collapseAllButton.textContent = '折叠所有分析过程';
            collapseAllButton.onclick = function() {
                // 折叠所有折叠区域
                collapseElements.forEach(function(element) {
                    if (element.classList.contains('show')) {
                        element.classList.remove('show');
                        
                        // 更新对应按钮的aria-expanded属性
                        const id = element.id;
                        if (id) {
                            const button = document.querySelector('[data-bs-target="#' + id + '"], [data-target="#' + id + '"]');
                            if (button) {
                                button.setAttribute('aria-expanded', 'false');
                            }
                        }
                    }
                });
                
                console.log('折叠了所有折叠区域');
            };
            
            // 添加按钮到容器
            container.appendChild(expandAllButton);
            container.appendChild(collapseAllButton);
            
            // 查找合适的位置添加按钮
            const mainContent = document.querySelector('.container > .row > .col-md-8, .container > .row > .col-md-9, .container > .row > .col-md-12');
            if (mainContent) {
                // 添加到主内容区域的顶部
                mainContent.insertBefore(container, mainContent.firstChild);
                console.log('添加了展开所有和折叠所有按钮到主内容区域');
            } else {
                // 添加到第一个折叠区域之前
                const firstCollapse = collapseElements[0];
                if (firstCollapse.parentNode) {
                    firstCollapse.parentNode.insertBefore(container, firstCollapse);
                    console.log('添加了展开所有和折叠所有按钮到第一个折叠区域之前');
                }
            }
        } catch (e) {
            console.error('添加展开/折叠所有按钮时出错:', e);
        }
    }
})();
