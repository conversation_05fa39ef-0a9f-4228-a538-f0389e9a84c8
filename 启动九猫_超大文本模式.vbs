' 九猫小说分析系统启动脚本 (VBS版本) - 超大文本模式
' 此脚本会在后台启动九猫系统，并自动打开浏览器，无需显示命令行窗口
' 超大文本模式：特别针对几百万字的长文本进行优化，大幅提高数据库连接池和内存配置

' 设置工作目录为脚本所在目录
Set fso = CreateObject("Scripting.FileSystemObject")
Set WshShell = CreateObject("WScript.Shell")
currentPath = fso.GetParentFolderName(WScript.ScriptFullName)
WshShell.CurrentDirectory = currentPath

' 检查并创建日志目录
If Not fso.FolderExists("logs") Then
    fso.CreateFolder("logs")
End If

' 清理旧日志文件，只保留最新的5个
Sub CleanupOldLogs()
    On Error Resume Next

    Dim logFolder, logFiles, file
    Set logFolder = fso.GetFolder(currentPath & "\logs")
    Set logFiles = logFolder.Files

    ' 创建一个数组来存储日志文件
    Dim fileArray()
    ReDim fileArray(logFiles.Count - 1)

    ' 填充数组
    Dim i, fileCount
    i = 0
    fileCount = 0

    For Each file In logFiles
        If LCase(Right(file.Name, 4)) = ".log" Then
            fileArray(i) = file.Path
            i = i + 1
            fileCount = fileCount + 1
        End If
    Next

    ' 如果文件数量超过5个，删除最旧的文件
    If fileCount > 5 Then
        ' 按修改日期排序（冒泡排序）
        Dim j, temp
        For i = 0 To fileCount - 2
            For j = 0 To fileCount - i - 2
                If fso.GetFile(fileArray(j)).DateLastModified > fso.GetFile(fileArray(j+1)).DateLastModified Then
                    temp = fileArray(j)
                    fileArray(j) = fileArray(j+1)
                    fileArray(j+1) = temp
                End If
            Next
        Next

        ' 删除最旧的文件（保留最新的5个）
        For i = 0 To fileCount - 6
            If fileArray(i) <> "" Then
                fso.DeleteFile(fileArray(i))
            End If
        Next
    End If
End Sub

' 检查psutil是否已安装
Function IsPsutilInstalled()
    Dim result
    result = WshShell.Run("python -c ""import psutil""", 0, True)
    IsPsutilInstalled = (result = 0)
End Function

' 安装psutil
Sub InstallPsutil()
    Dim result
    result = WshShell.Run("pip install psutil", 1, True)
    If result <> 0 Then
        MsgBox "无法安装psutil模块。系统将以有限的内存监控功能运行。" & vbCrLf & _
               "建议手动安装: pip install psutil", _
               48, "九猫小说分析系统 - 警告"
    End If
End Sub

' 检查端口5001是否被占用
Function IsPortInUse()
    Dim result
    result = WshShell.Run("netstat -ano | findstr :5001 | findstr LISTENING", 0, True)
    IsPortInUse = (result = 0)
End Function

' 终止占用端口5001的进程
Sub KillPort5001Process()
    On Error Resume Next

    ' 创建临时批处理文件来终止进程
    Set killPortFile = fso.CreateTextFile("kill_port.bat", True)
    killPortFile.WriteLine("@echo off")
    killPortFile.WriteLine("for /f ""tokens=5"" %%a in ('netstat -ano ^| findstr :5001 ^| findstr LISTENING') do (")
    killPortFile.WriteLine("    taskkill /f /pid %%a >nul 2>&1")
    killPortFile.WriteLine(")")
    killPortFile.Close

    ' 运行批处理文件
    WshShell.Run "kill_port.bat", 0, True

    ' 删除临时文件
    fso.DeleteFile "kill_port.bat"
End Sub

' 检查Python是否可用
Function IsPythonAvailable()
    Dim result
    result = WshShell.Run("python --version", 0, True)
    IsPythonAvailable = (result = 0)
End Function

' 检查run.py文件是否存在
Function IsRunPyExists()
    IsRunPyExists = fso.FileExists(currentPath & "\run.py")
End Function

' 执行日志清理
CleanupOldLogs

' 检查Python是否可用
If Not IsPythonAvailable() Then
    MsgBox "未检测到Python环境，请确保已安装Python并添加到系统PATH中。", _
           vbCritical, "九猫小说分析系统 - 错误"
    WScript.Quit
End If

' 检查run.py文件是否存在
If Not IsRunPyExists() Then
    MsgBox "未找到run.py文件，请确保脚本位于九猫系统根目录中。", _
           vbCritical, "九猫小说分析系统 - 错误"
    WScript.Quit
End If

' 检查并安装psutil
If Not IsPsutilInstalled() Then
    If MsgBox("未检测到psutil模块，该模块用于内存监控和优化。" & vbCrLf & _
              "是否立即安装？", _
              vbYesNo + vbQuestion, "九猫小说分析系统") = vbYes Then
        InstallPsutil
    End If
End If

' 检查并释放端口
If IsPortInUse() Then
    If MsgBox("端口5001已被占用，需要释放才能启动九猫系统。" & vbCrLf & _
              "是否尝试终止占用该端口的进程？", _
              vbYesNo + vbQuestion, "九猫小说分析系统") = vbYes Then
        KillPort5001Process
    End If
End If

' 显示启动消息
MsgBox "九猫小说分析系统正在启动（超大文本模式）..." & vbCrLf & vbCrLf & _
       "系统将在后台运行，浏览器将在20秒钟后自动打开。" & vbCrLf & _
       "请不要关闭浏览器窗口，否则需要手动访问: http://localhost:5001" & vbCrLf & vbCrLf & _
       "超大文本模式已启用，特别优化了以下配置：" & vbCrLf & _
       "- 超大数据库连接池（200/200）" & vbCrLf & _
       "- 增大线程池大小（32线程）" & vbCrLf & _
       "- 优化分块处理参数" & vbCrLf & _
       "- 增大分析块大小（12000字）" & vbCrLf & _
       "- 增加内存限制（95%警告）" & vbCrLf & _
       "- 特别为百万字小说优化", _
       64, "九猫小说分析系统 - 超大文本优化模式"

' 创建启动日志文件
Dim logFile
Set logFile = fso.CreateTextFile("logs\startup_" & Replace(Replace(Replace(Now(), ":", "-"), "/", "-"), " ", "_") & ".log", True)
logFile.WriteLine("九猫小说分析系统启动日志")
logFile.WriteLine("时间: " & Now())
logFile.WriteLine("工作目录: " & currentPath)
logFile.WriteLine("超大文本模式已启用")
logFile.WriteLine("-----------------------------------")
logFile.Close

' 创建临时批处理文件来设置环境变量并运行Python
Set tempFile = fso.CreateTextFile("temp_run.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("cd /d """ & currentPath & """")
tempFile.WriteLine("echo 九猫小说分析系统启动中...")
tempFile.WriteLine("echo 工作目录: %CD%")
tempFile.WriteLine("echo -----------------------------------")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("set MEMORY_OPTIMIZED=True")
tempFile.WriteLine("set LOW_MEMORY_MODE=False")
tempFile.WriteLine("rem 内存限制提高")
tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=90")
tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=95")
tempFile.WriteLine("rem 超大数据库连接池")
tempFile.WriteLine("set MAX_DB_CONNECTIONS=300")
tempFile.WriteLine("set DB_POOL_SIZE=200")
tempFile.WriteLine("set DB_MAX_OVERFLOW=200")
tempFile.WriteLine("set DB_CONNECTION_TIMEOUT=180")
tempFile.WriteLine("set DB_RECYCLE_SECONDS=7200")
tempFile.WriteLine("rem 线程池优化")
tempFile.WriteLine("set THREAD_POOL_SIZE=32")
tempFile.WriteLine("set MAX_WORKERS=32")
tempFile.WriteLine("set DISABLE_PARALLEL_ANALYSIS=False")
tempFile.WriteLine("set PARALLEL_ANALYSIS_ENABLED=True")
tempFile.WriteLine("set REDUCE_LOGGING=True")
tempFile.WriteLine("rem 分块处理优化")
tempFile.WriteLine("set MAX_CHUNK_SIZE=12000")
tempFile.WriteLine("set OVERLAP_SIZE=200")
tempFile.WriteLine("set MAX_PARALLEL_ANALYSES=16")
tempFile.WriteLine("set MAX_CHUNK_WORKERS=16")
tempFile.WriteLine("rem 缓存优化")
tempFile.WriteLine("set CACHE_ENABLED=True")
tempFile.WriteLine("set CACHE_INTERMEDIATE_RESULTS=True")
tempFile.WriteLine("set CACHE_REUSE_ACROSS_DIMENSIONS=True")
tempFile.WriteLine("set CACHE_VALID_DAYS=14")
tempFile.WriteLine("set INTERMEDIATE_CACHE_VALID_DAYS=7")
tempFile.WriteLine("rem 各维度分块大小优化")
tempFile.WriteLine("set DIMENSION_CHUNK_SIZES_LANGUAGE_STYLE=12000")
tempFile.WriteLine("set DIMENSION_CHUNK_SIZES_RHYTHM_PACING=15000")
tempFile.WriteLine("set DIMENSION_CHUNK_SIZES_STRUCTURE=18000")
tempFile.WriteLine("set DIMENSION_CHUNK_SIZES_CHARACTER_RELATIONSHIPS=15000")
tempFile.WriteLine("set DIMENSION_CHUNK_SIZES_WORLD_BUILDING=18000")
tempFile.WriteLine("set DIMENSION_CHUNK_SIZES_CHAPTER_OUTLINE=20000")
tempFile.WriteLine("set DIMENSION_CHUNK_SIZES_CLIMAX_PACING=15000")
tempFile.WriteLine("rem 各维度输出token数优化")
tempFile.WriteLine("set DIMENSION_MAX_TOKENS_LANGUAGE_STYLE=3500")
tempFile.WriteLine("set DIMENSION_MAX_TOKENS_RHYTHM_PACING=3500")
tempFile.WriteLine("set DIMENSION_MAX_TOKENS_STRUCTURE=4000")
tempFile.WriteLine("set DIMENSION_MAX_TOKENS_CHARACTER_RELATIONSHIPS=4000")
tempFile.WriteLine("set DIMENSION_MAX_TOKENS_WORLD_BUILDING=4000")
tempFile.WriteLine("set DIMENSION_MAX_TOKENS_CHAPTER_OUTLINE=5000")
tempFile.WriteLine("set DIMENSION_MAX_TOKENS_CLIMAX_PACING=3500")
tempFile.WriteLine("rem 超大文本专用优化")
tempFile.WriteLine("set OPTIMIZE_MEMORY_FOR_LARGE_ANALYSES=True")
tempFile.WriteLine("set LARGE_TEXT_MODE=True")
tempFile.WriteLine("set MAX_NOVEL_SIZE=10000000")
tempFile.WriteLine("set BATCH_PROCESSING_ENABLED=True")
tempFile.WriteLine("set BATCH_SIZE=500000")
tempFile.WriteLine("rem 错误处理和恢复配置")
tempFile.WriteLine("set AUTO_RESET_ON_ERROR=True")
tempFile.WriteLine("set MAX_RETRIES_ON_ERROR=5")
tempFile.WriteLine("set ERROR_RECOVERY_DELAY=10")
tempFile.WriteLine("set ENABLE_CONNECTION_POOL_MONITORING=True")
tempFile.WriteLine("rem 内存管理优化")
tempFile.WriteLine("set GC_COLLECT_FREQUENCY=30")
tempFile.WriteLine("set CLEAN_TEMP_FILES=True")
tempFile.WriteLine("set AGGRESSIVE_GC=True")
tempFile.WriteLine("set FREE_UNUSED_MEMORY=True")
tempFile.WriteLine("set STARTUP_MODE=True")
tempFile.WriteLine("rem 数据库优化")
tempFile.WriteLine("set SQLITE_PRAGMA_MEMORY_SIZE=1000000")
tempFile.WriteLine("set SQLITE_PRAGMA_CACHE_SIZE=100000")
tempFile.WriteLine("set SQLITE_PRAGMA_JOURNAL_MODE=WAL")
tempFile.WriteLine("set SQLITE_PRAGMA_SYNCHRONOUS=NORMAL")
tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("echo 启动Python服务...")
tempFile.WriteLine("echo -----------------------------------")
tempFile.WriteLine("python run.py")
tempFile.WriteLine("if errorlevel 1 (")
tempFile.WriteLine("    echo 启动失败，请检查错误信息")
tempFile.WriteLine("    echo 按任意键退出...")
tempFile.WriteLine("    pause > nul")
tempFile.WriteLine(")")
tempFile.Close

' 在后台运行批处理文件，显示窗口以便查看输出和错误信息
WshShell.Run "temp_run.bat", 1, False

' 等待20秒钟确保服务启动
WScript.Sleep 20000

' 检查服务是否已启动
Dim isServerRunning
isServerRunning = False

' 尝试3次检查服务是否启动
For i = 1 To 3
    If IsPortInUse() Then
        isServerRunning = True
        Exit For
    End If
    WScript.Sleep 5000 ' 等待5秒再次检查
Next

' 如果服务已启动，打开浏览器
If isServerRunning Then
    ' 打开浏览器 - 使用cmd命令强制使用默认浏览器
    WshShell.Run "cmd /c start http://localhost:5001", 0, False
Else
    ' 服务未启动，显示错误消息
    MsgBox "九猫系统服务未能正常启动，请检查temp_run.bat窗口中的错误信息。" & vbCrLf & _
           "可能的原因：" & vbCrLf & _
           "1. Python环境配置问题" & vbCrLf & _
           "2. 缺少必要的Python依赖" & vbCrLf & _
           "3. 端口5001被占用" & vbCrLf & _
           "4. 工作目录设置不正确", _
           vbCritical, "九猫小说分析系统 - 启动失败"
End If 