/**
 * 九猫系统 - 创建知识库预设内容
 *
 * 这个脚本用于创建一个示例知识库预设内容
 * 在控制台页面加载后自动运行
 */

// 在页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('知识库创建脚本已加载');

    // 检查是否已经存在知识库预设内容
    setTimeout(function() {
        checkAndCreateKnowledgeBase();
    }, 2000);
});

// 检查并创建知识库预设内容
function checkAndCreateKnowledgeBase() {
    // 调用API获取预设列表
    fetch('/api/presets')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const presets = data.presets || [];

                // 检查是否已经存在知识库预设内容
                const hasKnowledgeBase = presets.some(preset =>
                    preset.category === 'knowledge_base' &&
                    preset.title.includes('九猫知识库示例')
                );

                if (!hasKnowledgeBase) {
                    console.log('未找到知识库预设内容，准备创建...');
                    createKnowledgeBase();
                } else {
                    console.log('已存在知识库预设内容，无需创建');
                }
            }
        })
        .catch(error => {
            console.error('获取预设列表失败:', error);
        });
}

// 创建知识库预设内容
function createKnowledgeBase() {
    const knowledgeBaseContent = {
        title: '九猫知识库示例',
        content: `# 九猫知识库示例

这是一个示例知识库，用于演示九猫系统的知识库功能。知识库可以保存参考蓝本的分析结果和推理过程，帮助您更好地理解和应用小说分析。

## 使用方法

1. 选择一个参考蓝本（必须是已完成所有15个维度分析的小说）
2. 在预设内容中找到知识库，点击'读取'按钮
3. 系统将自动读取蓝本的所有分析维度和章节，并生成知识库内容

## 知识库内容结构

知识库包含以下两个主要部分：

### 整本书分析理解

这部分包含整本书的15个分析维度的结果和推理过程：

- 语言风格：作者在写作中使用的独特表达方式
- 节奏节拍：情节发展的快慢变化和韵律感
- 结构分析：小说的整体架构和组织方式
- 句式变化：句子长度、复杂度和多样性
- 段落长度：段落的长短变化和组织方式
- 视角转换：叙事视角的变化和转换
- 段落流畅度：段落之间的连贯性和过渡
- 小说特点：作品的独特风格和特征
- 世界构建：小说世界观的构建方式
- 人物关系：角色之间的互动和关系
- 开篇效果：小说开头的设计和效果
- 高潮节奏：高潮部分的节奏和张力
- 主题探索：小说探讨的核心主题
- 大纲分析：整体结构和情节发展
- 章纲分析：各章节的结构和功能

### 章节分析理解

这部分包含第一章的所有分析维度的结果和推理过程，作为章节分析的示例。

## 知识库应用

您可以利用知识库中的分析结果和推理过程：

1. 学习和理解不同小说的写作技巧
2. 参考优秀作品的结构和风格特点
3. 改进自己的写作方法和技巧
4. 为自动写作提供参考和指导

## 注意事项

- 知识库内容是基于AI分析生成的，可能需要人工审核和调整
- 不同类型的小说可能有不同的分析重点和特点
- 建议创建多个参考蓝本的知识库，以便比较和学习不同的写作风格`,
        category: 'knowledge_base'
    };

    // 调用API创建预设内容
    fetch('/api/presets', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(knowledgeBaseContent)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('知识库预设内容创建成功:', data.preset.id);
        } else {
            console.error('知识库预设内容创建失败:', data.error);
        }
    })
    .catch(error => {
        console.error('知识库预设内容创建请求失败:', error);
    });
}
