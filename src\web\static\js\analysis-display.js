/**
 * 九猫分析系统 - 分析结果显示
 *
 * 这个文件负责处理分析结果的显示，包括：
 * 1. 在小说详情页面显示所有维度的分析状态
 * 2. 在分析结果详情页面显示完整的分析结果
 * 3. 处理分析结果的加载和渲染
 */

// 全局状态
const STATE = {
    novelId: null,
    currentDimension: null,
    analysisResults: {},
    isLoading: false
};

// 初始化函数
function initAnalysisDisplay() {
    console.log('[九猫分析] 初始化分析结果显示');

    // 从页面获取小说ID
    const novelContainer = document.querySelector('[data-novel-id]');
    if (novelContainer) {
        STATE.novelId = novelContainer.getAttribute('data-novel-id');
        console.log(`[九猫分析] 获取到小说ID: ${STATE.novelId}`);
    }

    // 检查是否在分析结果详情页面
    const analysisContent = document.getElementById('analysisContent');
    if (analysisContent) {
        // 获取当前维度
        const urlParts = window.location.pathname.split('/');
        STATE.currentDimension = urlParts[urlParts.length - 1];
        console.log(`[九猫分析] 当前在分析结果详情页面，维度: ${STATE.currentDimension}`);

        // 加载当前维度的分析结果
        loadAnalysisResult(STATE.novelId, STATE.currentDimension);
    } else {
        // 在小说详情页面，加载所有维度的分析状态
        loadAllAnalysisResults(STATE.novelId);
    }

    // 绑定事件处理器
    bindEventHandlers();
}

// 加载所有维度的分析结果
function loadAllAnalysisResults(novelId) {
    if (!novelId) {
        console.error('[九猫分析] 无法加载分析结果：缺少小说ID');
        return;
    }

    console.log(`[九猫分析] 加载小说 ${novelId} 的所有分析结果`);
    STATE.isLoading = true;

    // 显示加载状态
    updateLoadingStatus(true);

    // 发送API请求
    fetch(`/api/novels/${novelId}/analysis?_=${Date.now()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.json();
        })
        .then(results => {
            console.log(`[九猫分析] 获取到 ${results.length} 个分析结果`);

            // 更新全局状态
            results.forEach(result => {
                if (result && result.dimension) {
                    STATE.analysisResults[result.dimension] = result;
                }
            });

            // 更新UI
            updateAnalysisCards();

            // 更新分析状态
            updateAnalysisStatus();
        })
        .catch(error => {
            console.error('[九猫分析] 加载分析结果出错:', error);
            showErrorMessage('加载分析结果失败，请刷新页面重试');
        })
        .finally(() => {
            STATE.isLoading = false;
            updateLoadingStatus(false);
        });
}

// 加载特定维度的分析结果
function loadAnalysisResult(novelId, dimension) {
    if (!novelId || !dimension) {
        console.error('[九猫分析] 无法加载分析结果：缺少小说ID或维度');
        return;
    }

    console.log(`[九猫分析] 加载小说 ${novelId} 的 ${dimension} 维度分析结果`);
    STATE.isLoading = true;

    // 显示加载状态
    updateLoadingStatus(true);

    // 发送API请求
    fetch(`/api/novels/${novelId}/analysis/${dimension}?_=${Date.now()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('[九猫分析] 获取到分析结果:', data);

            if (data.success === false) {
                throw new Error(data.error || '获取分析结果失败');
            }

            // 提取结果数据
            const result = data.result || data;

            // 更新全局状态
            STATE.analysisResults[dimension] = result;

            // 更新UI
            displayAnalysisResult(result);
        })
        .catch(error => {
            console.error('[九猫分析] 加载分析结果出错:', error);
            showErrorMessage('加载分析结果失败，请刷新页面重试');
        })
        .finally(() => {
            STATE.isLoading = false;
            updateLoadingStatus(false);
        });
}

// 更新分析卡片
function updateAnalysisCards() {
    console.log('[九猫分析] 更新分析卡片');

    // 获取所有分析卡片
    const cards = document.querySelectorAll('.analysis-card');

    // 遍历所有维度的分析结果
    for (const dimension in STATE.analysisResults) {
        const result = STATE.analysisResults[dimension];

        // 查找对应的卡片
        let card = Array.from(cards).find(card => card.getAttribute('data-dimension') === dimension);

        // 如果找不到卡片，创建一个新的
        if (!card) {
            card = createAnalysisCard(dimension);
        }

        // 更新卡片内容
        updateAnalysisCard(card, result);
    }
}

// 创建分析卡片
function createAnalysisCard(dimension) {
    console.log(`[九猫分析] 创建维度 ${dimension} 的分析卡片`);

    // 获取维度名称
    const dimensionName = getDimensionName(dimension);

    // 创建卡片元素
    const card = document.createElement('div');
    card.className = 'analysis-card card mb-3';
    card.setAttribute('data-dimension', dimension);

    // 设置卡片内容
    card.innerHTML = `
        <div class="card-header">
            <h5 class="card-title">${dimensionName}</h5>
        </div>
        <div class="card-body">
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载分析结果...</p>
            </div>
        </div>
    `;

    // 添加到分析部分
    const analysisSection = document.getElementById('analysis-section-container');
    if (analysisSection) {
        analysisSection.appendChild(card);
    }

    return card;
}

// 更新分析卡片
function updateAnalysisCard(card, result) {
    if (!card || !result) return;

    console.log(`[九猫分析] 更新维度 ${result.dimension} 的分析卡片`);

    // 获取卡片主体
    const cardBody = card.querySelector('.card-body');
    if (!cardBody) return;

    // 提取摘要
    let excerpt = '分析已完成，点击"查看详情"查看完整结果。';
    if (result.content) {
        // 简单提取前100个字符作为摘要
        excerpt = result.content.substring(0, 100) + '...';
    }

    // 更新卡片内容
    cardBody.innerHTML = `
        <div class="d-flex justify-content-between mb-2">
            <span class="badge bg-success">分析完成</span>
            <a href="/novel/${STATE.novelId}/analysis/${result.dimension}"
               class="btn btn-sm btn-outline-primary view-analysis-btn"
               data-novel-id="${STATE.novelId}"
               data-dimension="${result.dimension}">
                查看详情
            </a>
        </div>
        <div class="analysis-excerpt mt-2">
            ${excerpt}
        </div>
    `;
}

// 显示分析结果
function displayAnalysisResult(result) {
    console.log('[九猫分析] 显示分析结果');

    // 获取分析结果容器
    const analysisContent = document.getElementById('analysisContent');
    if (!analysisContent) {
        console.error('[九猫分析] 找不到分析结果容器');
        return;
    }

    // 显示分析结果
    if (result.content) {
        analysisContent.innerHTML = result.content;
        console.log('[九猫分析] 成功显示分析结果');
    } else {
        analysisContent.innerHTML = '<div class="alert alert-warning">分析结果为空</div>';
        console.warn('[九猫分析] 分析结果为空');
    }
}

// 更新分析状态
function updateAnalysisStatus() {
    console.log('[九猫分析] 更新分析状态');

    // 获取状态徽章
    const statusBadge = document.querySelector('.analysis-status-badge');
    if (!statusBadge) {
        console.warn('[九猫分析] 找不到状态徽章');
        return;
    }

    // 计算已完成的分析数量
    const completedCount = Object.keys(STATE.analysisResults).length;
    const totalCount = 15; // 总共15个维度

    // 更新状态徽章
    statusBadge.innerHTML = `${completedCount}/${totalCount} 分析已完成`;

    // 更新徽章颜色
    if (completedCount === totalCount) {
        statusBadge.className = 'badge bg-success analysis-status-badge';
        console.log('[九猫分析] 所有维度分析已完成');
    } else {
        statusBadge.className = 'badge bg-warning analysis-status-badge';
        console.log(`[九猫分析] 分析进度: ${completedCount}/${totalCount}`);
    }
}

// 更新加载状态
function updateLoadingStatus(isLoading) {
    // 获取加载指示器
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = isLoading ? 'block' : 'none';
    }
}

// 显示错误消息
function showErrorMessage(message) {
    console.error('[九猫分析] 错误:', message);

    // 创建错误提示
    const errorAlert = document.createElement('div');
    errorAlert.className = 'alert alert-danger alert-dismissible fade show';
    errorAlert.innerHTML = `
        <strong>错误:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
    `;

    // 添加到页面
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(errorAlert, container.firstChild);
    }
}

// 绑定事件处理器
function bindEventHandlers() {
    console.log('[九猫分析] 绑定事件处理器');

    // 绑定查看详情按钮点击事件
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('view-analysis-btn') ||
            event.target.closest('.view-analysis-btn')) {

            const button = event.target.classList.contains('view-analysis-btn') ?
                event.target : event.target.closest('.view-analysis-btn');

            const novelId = button.getAttribute('data-novel-id');
            const dimension = button.getAttribute('data-dimension');

            if (novelId && dimension) {
                console.log(`[九猫分析] 点击查看详情按钮: novelId=${novelId}, dimension=${dimension}`);

                // 构建URL
                const url = `/novel/${novelId}/analysis/${dimension}`;

                // 跳转到详情页面
                window.location.href = url;
            }
        }
    });

    // 绑定重新分析按钮点击事件
    const reanalyzeBtn = document.getElementById('reanalyzeBtn');
    if (reanalyzeBtn) {
        reanalyzeBtn.addEventListener('click', function() {
            if (confirm('确定要重新分析吗？这将删除现有的分析结果并重新生成。')) {
                reanalyzeAnalysis(STATE.novelId, STATE.currentDimension);
            }
        });
    }
}

// 重新分析
function reanalyzeAnalysis(novelId, dimension) {
    if (!novelId || !dimension) {
        console.error('[九猫分析] 无法重新分析：缺少小说ID或维度');
        return;
    }

    console.log(`[九猫分析] 重新分析小说 ${novelId} 的 ${dimension} 维度`);

    // 显示加载状态
    const reanalyzeBtn = document.getElementById('reanalyzeBtn');
    if (reanalyzeBtn) {
        reanalyzeBtn.disabled = true;
        reanalyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>分析中...';
    }

    // 发送分析请求
    fetch(`/api/novels/${novelId}/analyze`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            dimensions: [dimension]
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('[九猫分析] 重新分析响应:', data);

        if (data.success) {
            // 显示成功消息
            alert('分析任务已启动，请稍后刷新页面查看结果');

            // 5秒后刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 5000);
        } else {
            alert(`启动分析失败: ${data.error || '未知错误'}`);

            // 恢复按钮状态
            if (reanalyzeBtn) {
                reanalyzeBtn.disabled = false;
                reanalyzeBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>重新分析';
            }
        }
    })
    .catch(error => {
        console.error('[九猫分析] 重新分析出错:', error);
        alert(`请求错误: ${error.message}`);

        // 恢复按钮状态
        if (reanalyzeBtn) {
            reanalyzeBtn.disabled = false;
            reanalyzeBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>重新分析';
        }
    });
}

// 获取维度名称
function getDimensionName(dimension) {
    const dimensionMap = {
        'language_style': '语言风格',
        'rhythm_pacing': '节奏与节奏',
        'structure': '结构分析',
        'sentence_variation': '句式变化',
        'paragraph_length': '段落长度',
        'perspective_shifts': '视角转换',
        'paragraph_flow': '段落流畅度',
        'novel_characteristics': '小说特点',
        'world_building': '世界构建',
        'chapter_outline': '章节大纲',
        'character_relationships': '人物关系',
        'opening_effectiveness': '开篇效果',
        'climax_pacing': '高潮节奏'
    };

    return dimensionMap[dimension] || dimension;
}

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', initAnalysisDisplay);
