"""
九猫小说分析写作系统v3.0 API路由
"""
import logging
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify
from sqlalchemy import func
from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.analysis_result import AnalysisResult
from src.models.preset import Preset
from src.services.chapter_analysis_service import ChapterAnalysisService
import config

v3_api_bp = Blueprint('v3_api', __name__)
logger = logging.getLogger(__name__)

# 从v2_api继承基本功能
from src.web.routes.v2_api import (
    api_get_chapter_reasoning_content,
    api_get_chapter_analysis,
    api_get_novel_analysis,
    api_get_novel_reasoning_content
)

# 注册继承的路由
v3_api_bp.add_url_rule('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content',
                      view_func=api_get_chapter_reasoning_content)
v3_api_bp.add_url_rule('/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content',
                      view_func=api_get_chapter_reasoning_content)
v3_api_bp.add_url_rule('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>',
                      view_func=api_get_chapter_analysis)
v3_api_bp.add_url_rule('/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>',
                      view_func=api_get_chapter_analysis)
v3_api_bp.add_url_rule('/api/novel/<int:novel_id>/analysis/<dimension>',
                      view_func=api_get_novel_analysis)
v3_api_bp.add_url_rule('/api/novels/<int:novel_id>/analysis/<dimension>',
                      view_func=api_get_novel_analysis)
v3_api_bp.add_url_rule('/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content',
                      view_func=api_get_novel_reasoning_content)
v3_api_bp.add_url_rule('/api/novels/<int:novel_id>/analysis/<dimension>/reasoning_content',
                      view_func=api_get_novel_reasoning_content)

# 新增API路由 - 获取小说所有分析结果
@v3_api_bp.route('/api/novel/<int:novel_id>/analysis')
@v3_api_bp.route('/api/novels/<int:novel_id>/analysis')  # 兼容旧路径
def api_get_novel_all_analysis(novel_id):
    """
    API: 获取小说的所有分析结果，包括整本书的分析维度和各章节的分析维度。

    Args:
        novel_id: 小说ID
    """
    try:
        logger.info(f"获取小说ID={novel_id}的所有分析结果（包括整本书和章节）")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到ID为{novel_id}的小说")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取整本书的所有分析结果
            book_analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()

            # 获取所有章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 转换整本书分析结果为字典列表
            book_results_list = []
            for result in book_analysis_results:
                # 处理元数据，确保可以序列化为JSON
                metadata = result.metadata
                if metadata and not isinstance(metadata, dict):
                    try:
                        # 尝试将元数据转换为字典
                        metadata = dict(metadata)
                    except:
                        # 如果无法转换，则使用字符串表示
                        metadata = str(metadata)

                # 获取推理过程内容
                reasoning_content = ''
                if hasattr(result, 'reasoning_content') and result.reasoning_content:
                    reasoning_content = result.reasoning_content
                elif isinstance(metadata, dict) and 'reasoning_content' in metadata:
                    reasoning_content = metadata['reasoning_content']

                result_dict = {
                    "id": result.id,
                    "novel_id": result.novel_id,
                    "dimension": result.dimension,
                    "content": result.content,
                    "reasoning_content": reasoning_content,
                    "metadata": metadata,
                    "created_at": result.created_at.isoformat() if result.created_at else None,
                    "updated_at": result.updated_at.isoformat() if result.updated_at else None
                }
                book_results_list.append(result_dict)

            # 获取并转换章节分析结果
            chapters_results = []
            for chapter in chapters:
                # 获取章节的所有分析结果
                chapter_analysis_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).all()

                # 转换章节分析结果为字典列表
                chapter_results_list = []
                for result in chapter_analysis_results:
                    # 处理元数据，确保可以序列化为JSON
                    metadata = result.analysis_metadata
                    if metadata and not isinstance(metadata, dict):
                        try:
                            # 尝试将元数据转换为字典
                            metadata = dict(metadata)
                        except:
                            # 如果无法转换，则使用字符串表示
                            metadata = str(metadata)

                    # 获取推理过程内容
                    reasoning_content = ''
                    if hasattr(result, 'reasoning_content') and result.reasoning_content:
                        reasoning_content = result.reasoning_content
                    elif isinstance(metadata, dict) and 'reasoning_content' in metadata:
                        reasoning_content = metadata['reasoning_content']

                    result_dict = {
                        "id": result.id,
                        "novel_id": result.novel_id,
                        "chapter_id": result.chapter_id,
                        "dimension": result.dimension,
                        "content": result.content,
                        "reasoning_content": reasoning_content,
                        "metadata": metadata,
                        "created_at": result.created_at.isoformat() if result.created_at else None,
                        "updated_at": result.updated_at.isoformat() if result.updated_at else None
                    }
                    chapter_results_list.append(result_dict)

                # 添加章节信息和分析结果
                chapters_results.append({
                    "id": chapter.id,
                    "novel_id": chapter.novel_id,
                    "chapter_number": chapter.chapter_number,
                    "title": chapter.title or f'第{chapter.chapter_number}章',
                    "word_count": chapter.word_count,
                    "analysis_results": chapter_results_list,
                    "analysis_count": len(chapter_results_list)
                })

            logger.info(f"成功获取小说《{novel.title}》的所有分析结果：整本书{len(book_results_list)}个维度，{len(chapters)}个章节")

            # 获取所有分析维度的定义
            all_dimensions = []
            for dim in config.ANALYSIS_DIMENSIONS:
                # 定义维度图标映射
                dimension_icons = {
                    'outline_analysis': 'project-diagram',
                    'chapter_outline': 'list-ol',
                    'language_style': 'language',
                    'rhythm_pacing': 'drum',
                    'structure': 'sitemap',
                    'sentence_variation': 'text-width',
                    'paragraph_length': 'paragraph',
                    'perspective_shifts': 'exchange-alt',
                    'paragraph_flow': 'stream',
                    'novel_characteristics': 'fingerprint',
                    'world_building': 'globe',
                    'character_relationships': 'users',
                    'opening_effectiveness': 'door-open',
                    'climax_pacing': 'mountain',
                    'theme_exploration': 'lightbulb'
                }

                dim_key = dim.get('key')
                icon = dimension_icons.get(dim_key, 'star')

                # 检查整本书是否已分析此维度
                is_book_analyzed = any(result['dimension'] == dim_key for result in book_results_list)

                all_dimensions.append({
                    'key': dim_key,
                    'name': dim.get('name', dim_key),
                    'icon': icon,
                    'is_book_analyzed': is_book_analyzed
                })

            return jsonify({
                "success": True,
                "novel": {
                    "id": novel.id,
                    "title": novel.title,
                    "author": novel.author,
                    "word_count": novel.word_count,
                    "created_at": novel.created_at.isoformat() if novel.created_at else None,
                    "updated_at": novel.updated_at.isoformat() if novel.updated_at else None,
                    "is_template": novel.novel_metadata and novel.novel_metadata.get('is_template', False) if novel.novel_metadata else False
                },
                "book_analyses": book_results_list,
                "chapters": chapters_results,
                "dimensions": all_dimensions,
                "total_dimensions": len(config.ANALYSIS_DIMENSIONS),
                "analyzed_book_dimensions": len(book_results_list),
                "total_chapters": len(chapters)
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"API获取小说所有分析结果时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)})

# 添加v3.1 API路由的别名，确保v3.1系统也能使用这些API
v3_api_bp.add_url_rule('/v3.1/api/novel/<int:novel_id>/analysis/<dimension>',
                      view_func=api_get_novel_analysis)
v3_api_bp.add_url_rule('/v3.1/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content',
                      view_func=api_get_novel_reasoning_content)
v3_api_bp.add_url_rule('/v3.1/api/novel/<int:novel_id>/analysis',
                      view_func=api_get_novel_all_analysis)

# 新增API路由 - 设置参考蓝本
@v3_api_bp.route('/api/novel/<int:novel_id>/set_as_template', methods=['POST'])
def api_set_as_template(novel_id):
    """设置小说为参考蓝本"""
    try:
        # 使用新的会话，确保获取最新数据
        session = Session()
        session.expire_all()  # 清除会话缓存

        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"设置参考蓝本失败: 未找到ID为{novel_id}的小说")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            logger.info(f"开始设置小说《{novel.title}》(ID: {novel_id})为参考蓝本")

            # 检查是否满足参考蓝本条件
            # 获取分析结果
            analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            available_dimensions = [result.dimension for result in analysis_results]

            logger.info(f"小说《{novel.title}》已完成的维度: {available_dimensions}")

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
            logger.info(f"小说《{novel.title}》共有{len(chapters)}个章节")

            # 获取章节分析结果
            chapter_analysis_results = {}
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).all()

                if chapter_results:
                    chapter_analysis_results[chapter.id] = {result.dimension: result for result in chapter_results}

            # 检查是否满足参考蓝本条件
            all_dimensions = [d['key'] for d in config.ANALYSIS_DIMENSIONS]
            book_dimensions_complete = all(dim in available_dimensions for dim in all_dimensions)

            if not book_dimensions_complete:
                missing_dimensions = [dim for dim in all_dimensions if dim not in available_dimensions]
                logger.warning(f"小说《{novel.title}》整本书缺少维度: {missing_dimensions}")

            chapters_dimensions_complete = True
            incomplete_chapters = []
            for chapter in chapters:
                chapter_results = chapter_analysis_results.get(chapter.id, {})
                chapter_dimensions = list(chapter_results.keys())
                if not all(dim in chapter_dimensions for dim in all_dimensions):
                    chapters_dimensions_complete = False
                    missing_chapter_dims = [dim for dim in all_dimensions if dim not in chapter_dimensions]
                    incomplete_chapters.append({
                        'chapter_id': chapter.id,
                        'chapter_number': chapter.chapter_number,
                        'chapter_title': chapter.title,
                        'missing_dimensions': missing_chapter_dims
                    })

            if not chapters_dimensions_complete:
                logger.warning(f"小说《{novel.title}》有{len(incomplete_chapters)}个章节未完成所有维度分析")
                for ch in incomplete_chapters[:3]:  # 只记录前3个不完整的章节
                    logger.warning(f"章节 {ch['chapter_number']} '{ch['chapter_title']}' 缺少维度: {ch['missing_dimensions']}")

            if not (book_dimensions_complete and chapters_dimensions_complete):
                logger.error(f"设置参考蓝本失败: 小说《{novel.title}》不满足参考蓝本条件")
                return jsonify({
                    'success': False,
                    'error': '该小说不满足参考蓝本条件，需要完成所有维度的分析'
                }), 400

            # 先取消所有其他小说的参考蓝本设置
            # 查询所有标记为参考蓝本的小说
            all_novels = session.query(Novel).all()
            for other_novel in all_novels:
                if (other_novel.id != novel_id and
                    other_novel.novel_metadata and
                    other_novel.novel_metadata.get('is_template')):
                    # 记录日志
                    logger.info(f"取消小说《{other_novel.title}》(ID: {other_novel.id})的参考蓝本设置，因为要将小说《{novel.title}》(ID: {novel_id})设为参考蓝本")
                    # 取消参考蓝本设置 - 使用字典替换方式
                    new_metadata = dict(other_novel.novel_metadata)
                    new_metadata['is_template'] = False
                    new_metadata['template_removed_at'] = datetime.now().isoformat()
                    other_novel.novel_metadata = new_metadata
                    session.add(other_novel)

            # 更新小说元数据，标记为参考蓝本
            if not novel.novel_metadata:
                novel.novel_metadata = {}

            # 创建一个全新的字典，而不是修改现有字典
            # 这样SQLAlchemy会将整个字段视为已更改
            new_metadata = dict(novel.novel_metadata)  # 复制原有数据
            new_metadata['is_template'] = True
            new_metadata['template_created_at'] = datetime.now().isoformat()
            novel.novel_metadata = new_metadata  # 整体替换字典

            # 确保此小说的元数据字段已更新
            session.add(novel)
            # 提交所有更改
            session.commit()
            logger.info(f"数据库事务已提交: 小说《{novel.title}》(ID: {novel_id})已成功设置为参考蓝本")

            # 清除之前的缓存
            session.expire_all()
            # 重新从数据库加载小说对象，确保获取到最新数据
            session.refresh(novel)

            # 验证更改是否已保存
            if novel.novel_metadata and novel.novel_metadata.get('is_template'):
                logger.info(f"验证成功: 小说《{novel.title}》的novel_metadata中is_template已设置为{novel.novel_metadata.get('is_template')}")
            else:
                logger.warning(f"验证失败: 小说《{novel.title}》的novel_metadata中is_template未正确设置: {novel.novel_metadata}")
                # 重试一次设置，使用更强制的方式
                try:
                    # 直接执行SQL更新语句
                    import json
                    new_metadata = dict(novel.novel_metadata)  # 再次复制
                    new_metadata['is_template'] = True
                    new_metadata['template_created_at'] = datetime.now().isoformat()

                    # 将字典转换为JSON字符串
                    metadata_json = json.dumps(new_metadata)

                    # 执行直接更新SQL
                    session.execute(
                        f"UPDATE novels SET novel_metadata = '{metadata_json}' WHERE id = {novel_id}"
                    )
                    session.commit()
                    logger.info("已通过直接SQL更新重试设置参考蓝本")
                except Exception as sql_e:
                    logger.error(f"SQL更新尝试失败: {str(sql_e)}")

                # 再次验证
                session.refresh(novel)
                if novel.novel_metadata and novel.novel_metadata.get('is_template'):
                    logger.info(f"重试后验证成功: 小说《{novel.title}》的novel_metadata已正确设置")
                else:
                    logger.error(f"重试后验证仍失败: 小说《{novel.title}》的novel_metadata未正确设置: {novel.novel_metadata}")
                    return jsonify({
                        'success': False,
                        'error': '数据库更新验证失败，请重试'
                    }), 500

            # 记录详细日志
            logger.info(f"小说《{novel.title}》(ID: {novel_id})已成功设置为参考蓝本")
            logger.info(f"参考蓝本元数据: {novel.novel_metadata}")

            return jsonify({
                'success': True,
                'message': f'小说《{novel.title}》已设置为参考蓝本',
                'novel_id': novel_id,
                'timestamp': datetime.now().isoformat()
            })
        finally:
            session.close()
            logger.info(f"数据库会话已关闭")
    except Exception as e:
        logger.error(f"设置参考蓝本时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 取消参考蓝本
@v3_api_bp.route('/api/novel/<int:novel_id>/unset_as_template', methods=['POST'])
def api_unset_as_template(novel_id):
    """取消小说的参考蓝本设置"""
    try:
        session = Session()
        session.expire_all()  # 清除会话缓存

        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 更新小说元数据，取消参考蓝本标记
            if novel.novel_metadata and novel.novel_metadata.get('is_template'):
                # 创建一个全新的字典，而不是修改现有字典
                # 这样SQLAlchemy会将整个字段视为已更改
                new_metadata = dict(novel.novel_metadata)  # 复制原有数据
                new_metadata['is_template'] = False
                new_metadata['template_removed_at'] = datetime.now().isoformat()
                novel.novel_metadata = new_metadata  # 整体替换字典

                session.add(novel)
                session.commit()

                # 验证更改是否已保存
                session.refresh(novel)
                if not novel.novel_metadata.get('is_template'):
                    logger.info(f"验证成功: 小说《{novel.title}》的参考蓝本设置已取消")
                else:
                    logger.warning(f"验证失败: 小说《{novel.title}》的参考蓝本设置未能取消")
                    # 重试一次设置，使用更强制的方式
                    try:
                        # 直接执行SQL更新语句
                        import json
                        new_metadata = dict(novel.novel_metadata)  # 再次复制
                        new_metadata['is_template'] = False
                        new_metadata['template_removed_at'] = datetime.now().isoformat()

                        # 将字典转换为JSON字符串
                        metadata_json = json.dumps(new_metadata)

                        # 执行直接更新SQL
                        session.execute(
                            f"UPDATE novels SET novel_metadata = '{metadata_json}' WHERE id = {novel_id}"
                        )
                        session.commit()
                        logger.info("已通过直接SQL更新取消参考蓝本设置")
                    except Exception as sql_e:
                        logger.error(f"SQL更新尝试失败: {str(sql_e)}")

                return jsonify({
                    'success': True,
                    'message': f'小说《{novel.title}》已取消参考蓝本设置'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '该小说不是参考蓝本'
                }), 400
        finally:
            session.close()
            logger.info(f"数据库会话已关闭")
    except Exception as e:
        logger.error(f"取消参考蓝本设置时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 获取参考蓝本列表
@v3_api_bp.route('/api/reference_templates')
def api_get_reference_templates():
    """获取参考蓝本列表"""
    try:
        session = Session()
        try:
            # 查询所有标记为参考蓝本的小说
            templates = []
            novels = session.query(Novel).all()

            for novel in novels:
                if novel.novel_metadata and novel.novel_metadata.get('is_template'):
                    # 获取章节数量
                    chapter_count = session.query(Chapter).filter_by(novel_id=novel.id).count()

                    templates.append({
                        'id': novel.id,
                        'title': novel.title,
                        'author': novel.author,
                        'word_count': novel.word_count,
                        'created_at': novel.created_at.isoformat() if novel.created_at else None,
                        'template_created_at': novel.novel_metadata.get('template_created_at'),
                        'chapter_count': chapter_count
                    })

            return jsonify({
                'success': True,
                'templates': templates
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取参考蓝本列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 自动写作
@v3_api_bp.route('/api/auto_write', methods=['POST'])
def api_auto_write():
    """自动写作API"""
    try:
        # 获取请求数据
        data = request.json or {}
        template_id = data.get('template_id')
        prompt = data.get('prompt', '')

        if not template_id:
            return jsonify({
                'success': False,
                'error': '未指定参考蓝本'
            }), 400

        # 这里实现自动写作逻辑
        # 暂时返回模拟数据
        generated_content = {
            'id': 1,
            'title': '自动生成的内容',
            'content': f'这是基于参考蓝本 {template_id} 生成的内容。\n\n用户提示: {prompt}\n\n这里是生成的正文内容...',
            'word_count': 1000,
            'created_at': datetime.now().isoformat()
        }

        return jsonify({
            'success': True,
            'content': generated_content
        })
    except Exception as e:
        logger.error(f"自动写作时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 删除重复的路由函数

# 删除重复的路由函数

# 新增API路由 - 获取生成内容列表
@v3_api_bp.route('/api/generated_contents')
def api_get_generated_contents():
    """获取生成内容列表"""
    try:
        # 暂时返回模拟数据
        contents = [
            {
                'id': 1,
                'title': '自动生成的内容1',
                'word_count': 1000,
                'created_at': '2025-05-10T10:00:00'
            },
            {
                'id': 2,
                'title': '自动生成的内容2',
                'word_count': 1500,
                'created_at': '2025-05-11T15:30:00'
            }
        ]

        return jsonify({
            'success': True,
            'contents': contents
        })
    except Exception as e:
        logger.error(f"获取生成内容列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 开始分析
@v3_api_bp.route('/v3/start_analysis', methods=['POST'])
def api_start_analysis():
    """开始分析小说"""
    try:
        # 获取请求数据
        data = request.json or {}
        novel_id = data.get('novel_id')
        dimensions = data.get('dimensions', [])

        if not novel_id:
            return jsonify({
                'success': False,
                'message': '未指定小说ID'
            }), 400

        if not dimensions:
            return jsonify({
                'success': False,
                'message': '未指定分析维度'
            }), 400

        # 导入单维度分析模块
        from src.api.analyze_dimension import analyze_dimension

        # 逐个维度分析
        for dimension in dimensions:
            # 为每个维度创建新的会话，避免会话关闭后使用对象
            session = Session()
            try:
                # 获取小说
                novel = session.query(Novel).get(novel_id)
                if not novel:
                    return jsonify({
                        'success': False,
                        'message': '未找到指定小说'
                    }), 404

                # 在当前会话中启动分析
                analyze_dimension(novel, dimension)
            finally:
                session.close()

        return jsonify({
            'success': True,
            'message': '分析已开始'
        })
    except Exception as e:
        logger.error(f"开始分析时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 新增API路由 - 删除章节分析结果
@v3_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete', methods=['POST'])
def api_delete_chapter_analysis(novel_id, chapter_id, dimension):
    """删除章节分析结果和推理过程"""
    try:
        logger.info(f"开始删除章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")

        session = Session()
        try:
            # 查找章节分析结果
            chapter_analysis = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not chapter_analysis:
                logger.warning(f"未找到要删除的章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
                return jsonify({
                    'success': False,
                    'message': '未找到指定的章节分析结果'
                }), 404

            # 删除章节分析结果
            session.delete(chapter_analysis)
            session.commit()

            # 清除会话缓存，确保后续查询能获取最新数据
            session.expire_all()

            # 验证删除是否成功
            verify_result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if verify_result:
                logger.warning(f"删除章节分析结果后验证失败，记录仍然存在: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
                # 尝试强制删除
                try:
                    session.execute(
                        f"DELETE FROM chapter_analysis_results WHERE novel_id = {novel_id} AND chapter_id = {chapter_id} AND dimension = '{dimension}'"
                    )
                    session.commit()
                    logger.info(f"通过直接SQL删除章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")
                except Exception as sql_e:
                    logger.error(f"SQL删除尝试失败: {str(sql_e)}")

            logger.info(f"成功删除章节分析结果: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}")

            # 清除缓存
            try:
                from src.utils.cache_manager import clear_cache
                clear_cache()
                logger.info("已清除系统缓存")
            except Exception as cache_e:
                logger.warning(f"清除缓存失败: {str(cache_e)}")

            return jsonify({
                'success': True,
                'message': '章节分析结果已删除',
                'novel_id': novel_id,
                'chapter_id': chapter_id,
                'dimension': dimension
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"删除章节分析结果时出错: novel_id={novel_id}, chapter_id={chapter_id}, dimension={dimension}, error={str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'删除章节分析结果时出错: {str(e)}'
        }), 500

# 添加删除章节分析结果API的别名路由
v3_api_bp.add_url_rule('/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete',
                      view_func=api_delete_chapter_analysis, methods=['POST'])

# 新增API路由 - 开始章节分析
@v3_api_bp.route('/v3/start_chapter_analysis', methods=['POST'])
def api_start_chapter_analysis():
    """开始分析章节"""
    try:
        # 获取请求数据
        data = request.json or {}
        novel_id = data.get('novel_id')
        chapter_id = data.get('chapter_id')
        dimensions = data.get('dimensions', [])
        force = data.get('force', False)

        if not novel_id or not chapter_id:
            return jsonify({
                'success': False,
                'message': '未指定小说ID或章节ID'
            }), 400

        if not dimensions:
            return jsonify({
                'success': False,
                'message': '未指定分析维度'
            }), 400

        # 验证章节是否存在
        session = Session()
        try:
            chapter = session.query(Chapter).filter_by(
                novel_id=novel_id,
                id=chapter_id
            ).first()

            if not chapter:
                return jsonify({
                    'success': False,
                    'message': '未找到指定章节'
                }), 404

            # 验证通过，记录章节ID
            chapter_id_verified = chapter_id
        finally:
            session.close()

        # 在会话关闭后，使用验证过的章节ID进行分析
        # 逐个维度分析
        for dimension in dimensions:
            # 启动章节分析，使用验证过的章节ID
            ChapterAnalysisService.analyze_chapter(
                chapter_id=chapter_id_verified,
                dimension=dimension,
                use_cache=not force  # 如果force为True，则不使用缓存
            )

        return jsonify({
            'success': True,
            'message': '章节分析已开始'
        })
    except Exception as e:
        logger.error(f"开始章节分析时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 新增API路由 - 删除小说
@v3_api_bp.route('/api/novel/<int:novel_id>/delete', methods=['POST'])
def api_delete_novel(novel_id):
    """删除小说"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'message': '未找到指定小说'
                }), 404

            # 获取小说标题用于日志
            novel_title = novel.title

            # 删除相关的章节分析结果
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
            for chapter in chapters:
                session.query(ChapterAnalysisResult).filter_by(chapter_id=chapter.id).delete()

            # 删除相关的章节
            session.query(Chapter).filter_by(novel_id=novel_id).delete()

            # 删除相关的分析结果
            session.query(AnalysisResult).filter_by(novel_id=novel_id).delete()

            # 删除小说
            session.delete(novel)
            session.commit()

            logger.info(f"小说《{novel_title}》已删除")
            return jsonify({
                'success': True,
                'message': f'小说《{novel_title}》已删除'
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"删除小说时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 新增API路由 - 重新分析
@v3_api_bp.route('/api/novel/<int:novel_id>/reanalyze/<dimension>', methods=['POST'])
def api_reanalyze_novel(novel_id, dimension):
    """重新分析小说的指定维度"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'message': '未找到指定小说'
                }), 404

            # 删除现有的分析结果
            session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).delete()
            session.commit()

            # 导入单维度分析模块
            from src.api.analyze_dimension import analyze_dimension

            # 重新分析 - 使用try-except捕获可能的错误
            try:
                # 确保novel对象是当前会话的对象
                analyze_dimension(novel, dimension, use_real_api=True)
                logger.info(f"成功启动对小说《{novel.title}》的{dimension}维度重新分析")
            except Exception as analyze_error:
                logger.error(f"重新分析时出错: {str(analyze_error)}", exc_info=True)
                # 返回更详细的错误信息
                return jsonify({
                    'success': False,
                    'message': f'分析过程中出错: {str(analyze_error)}'
                }), 500

            return jsonify({
                'success': True,
                'message': f'小说《{novel.title}》的{dimension}维度已开始重新分析'
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"重新分析小说时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 新增API路由 - 一键分析所有章节维度
@v3_api_bp.route('/api/novel/<int:novel_id>/analyze_all_chapters', methods=['POST'])
def api_analyze_all_chapters(novel_id):
    """一键分析小说的所有章节的所有维度"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'message': '未找到指定小说'
                }), 404

            # 保存小说标题，避免在会话关闭后使用novel对象
            novel_title = novel.title

            # 获取所有章节ID和信息
            chapters_info = []
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
            if not chapters:
                return jsonify({
                    'success': False,
                    'message': '该小说没有章节'
                }), 400

            # 提取章节ID和必要信息，避免在会话关闭后使用chapter对象
            for chapter in chapters:
                chapters_info.append({
                    'id': chapter.id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title
                })

            # 获取所有分析维度
            all_dimensions = [d['key'] for d in config.ANALYSIS_DIMENSIONS]

            # 为每个章节启动所有维度的分析
            chapter_count = len(chapters_info)
            dimension_count = len(all_dimensions)
            total_tasks = chapter_count * dimension_count

            logger.info(f"开始一键分析小说《{novel_title}》的所有章节({chapter_count}章)的所有维度({dimension_count}个)")
        finally:
            # 关闭会话，避免会话过期问题
            session.close()

        # 在会话关闭后，使用提取的信息进行分析
        # 逐个章节、逐个维度启动分析
        for chapter_info in chapters_info:
            for dimension in all_dimensions:
                # 启动章节分析
                try:
                    ChapterAnalysisService.analyze_chapter(
                        chapter_id=chapter_info['id'],
                        dimension=dimension,
                        use_cache=True  # 使用缓存，避免重复分析
                    )
                except Exception as analyze_error:
                    logger.error(f"分析章节 {chapter_info['id']} 的维度 {dimension} 时出错: {str(analyze_error)}", exc_info=True)
                    # 继续分析其他章节和维度，不中断整个过程

        return jsonify({
            'success': True,
            'message': f'已启动对小说《{novel_title}》的所有章节({chapter_count}章)的所有维度({dimension_count}个)分析，共{total_tasks}个任务'
        })
    except Exception as e:
        logger.error(f"一键分析所有章节维度时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 新增API路由 - 获取小说信息
@v3_api_bp.route('/api/novel/<int:novel_id>')
def api_get_novel(novel_id):
    """获取小说信息"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取分析结果
            analysis_results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            available_dimensions = [result.dimension for result in analysis_results]

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 获取章节分析结果
            chapter_analysis_results = {}
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).all()

                if chapter_results:
                    chapter_analysis_results[chapter.id] = {result.dimension: result for result in chapter_results}

            # 检查是否可以设为参考蓝本
            all_dimensions = [d['key'] for d in config.ANALYSIS_DIMENSIONS]
            book_dimensions_complete = all(dim in available_dimensions for dim in all_dimensions)

            chapters_dimensions_complete = True
            for chapter in chapters:
                chapter_results = chapter_analysis_results.get(chapter.id, {})
                if not all(dim in chapter_results for dim in all_dimensions):
                    chapters_dimensions_complete = False
                    break

            # 转换为字典
            novel_dict = {
                'id': novel.id,
                'title': novel.title,
                'author': novel.author,
                'word_count': novel.word_count,
                'created_at': novel.created_at.isoformat() if novel.created_at else None,
                'updated_at': novel.updated_at.isoformat() if novel.updated_at else None,
                'available_dimensions': available_dimensions,
                'chapter_count': len(chapters),
                'is_fully_analyzed': book_dimensions_complete and chapters_dimensions_complete,
                'is_partially_analyzed': len(available_dimensions) > 0 and not (book_dimensions_complete and chapters_dimensions_complete),
                'is_template': novel.novel_metadata and novel.novel_metadata.get('is_template', False) if novel.novel_metadata else False,
                'can_set_as_template': book_dimensions_complete and chapters_dimensions_complete
            }

            # 转换章节为字典列表
            chapters_list = []
            for chapter in chapters:
                chapter_dict = {
                    'id': chapter.id,
                    'novel_id': chapter.novel_id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'word_count': chapter.word_count,
                    'content_preview': chapter.content[:200] + '...' if len(chapter.content) > 200 else chapter.content,
                    'analysis_count': len(chapter_analysis_results.get(chapter.id, {}))
                }
                chapters_list.append(chapter_dict)

            # 强制添加所有分析维度，即使小说没有分析过这些维度
            # 这样前端可以显示所有可能的维度
            all_dimension_names = {}
            for dim in config.ANALYSIS_DIMENSIONS:
                all_dimension_names[dim['key']] = dim['name']

            # 创建维度列表，包含所有可能的维度
            dimensions_list = []
            for dim_key, dim_name in all_dimension_names.items():
                # 定义维度图标映射
                dimension_icons = {
                    'outline_analysis': 'project-diagram',
                    'chapter_outline': 'list-ol',
                    'language_style': 'language',
                    'rhythm_pacing': 'drum',
                    'structure': 'sitemap',
                    'sentence_variation': 'text-width',
                    'paragraph_length': 'paragraph',
                    'perspective_shifts': 'exchange-alt',
                    'paragraph_flow': 'stream',
                    'novel_characteristics': 'fingerprint',
                    'world_building': 'globe',
                    'character_relationships': 'users',
                    'opening_effectiveness': 'door-open',
                    'climax_pacing': 'mountain',
                    'theme_exploration': 'lightbulb'
                }

                icon = dimension_icons.get(dim_key, 'star')

                dimensions_list.append({
                    'key': dim_key,
                    'name': dim_name,
                    'icon': icon,
                    'is_analyzed': dim_key in available_dimensions
                })

            # 调试信息已移除

            return jsonify({
                'success': True,
                'novel': novel_dict,
                'chapters': chapters_list,
                'dimensions': dimensions_list
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 获取统计数据
@v3_api_bp.route('/api/statistics')
def api_get_statistics():
    """获取统计数据"""
    try:
        session = Session()
        try:
            # 获取真实数据
            novel_count = session.query(Novel).count()
            chapter_count = session.query(Chapter).count()

            # 计算总字数
            total_words = session.query(func.sum(Novel.word_count)).scalar() or 0

            # 计算平均每章字数
            avg_words_per_chapter = total_words / chapter_count if chapter_count > 0 else 0

            # 获取系统运行时间（模拟数据）
            total_hours = 20.1

            stats = {
                'total_novels': novel_count,
                'total_chapters': chapter_count,
                'total_words': int(total_words),
                'avg_words_per_chapter': int(avg_words_per_chapter),
                'total_hours': total_hours
            }

            return jsonify({
                'success': True,
                'stats': stats
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取统计数据时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 预设内容API
@v3_api_bp.route('/api/presets', methods=['GET'])
def api_get_presets():
    """获取预设内容列表"""
    try:
        session = Session()
        try:
            # 检查表是否存在
            try:
                # 查询所有预设内容
                presets = session.query(Preset).order_by(Preset.created_at.desc()).all()

                # 转换为字典列表
                presets_list = [preset.to_dict() for preset in presets]

                return jsonify({
                    'success': True,
                    'presets': presets_list
                })
            except Exception as e:
                logger.error(f"查询预设内容列表时出错: {str(e)}", exc_info=True)
                # 如果查询失败，返回空列表
                return jsonify({
                    'success': True,
                    'presets': []
                })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设内容列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 整本书预设模板API
@v3_api_bp.route('/api/novel/<int:novel_id>/book_template/<dimension>')
def api_get_book_template(novel_id, dimension):
    """获取整本书的预设模板内容"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取分析结果
            analysis_result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not analysis_result:
                return jsonify({
                    'success': False,
                    'error': f'未找到维度 {dimension} 的分析结果'
                }), 404

            # 构建预设模板内容
            template_content = f"""# {novel.title} - {dimension} 预设模板

## 基本信息
- 小说: {novel.title}
- 作者: {novel.author or '未知'}
- 维度: {dimension}
- 适用字数范围：30-300万字
- 生成时间：{analysis_result.created_at.isoformat() if analysis_result.created_at else datetime.now().isoformat()}

## 设定内容
"""
            # 根据不同维度添加特定内容
            if dimension == "language_style":
                template_content += """### 语言风格设定
- 总体风格：[简洁明快/华丽典雅/质朴自然/锋利犀利]
- 语言氛围：[轻松幽默/庄重肃穆/神秘悬疑/浪漫温馨]
- 特色词汇：[专业领域词汇/方言俚语/古典词汇/现代流行语]
- 句式偏好：[长短句搭配比例/复合句使用频率/排比句运用]
"""
            elif dimension == "rhythm_pacing":
                template_content += """### 节奏节拍设定
- 总体节奏：[快速紧凑型/舒缓铺陈型/波动变化型/情节驱动型]
- 加速减速模式：[递进式/波浪式/起伏式/阶梯式]
- 章节内节奏变化：[开头-中段-结尾的节奏变化规律]
- 情节与节奏的匹配度：[快节奏场景/慢节奏场景的内容特点]
"""
            else:
                template_content += f"""### {dimension}设定
- 请在此处添加{dimension}的具体设定内容
- 可以包括具体的数值和比例
- 适用于30-300万字的长篇小说创作
"""

            template_content += """
## 应用指南
本设定模板适用于30-300万字的长篇小说创作，请根据您的具体需求和创作风格进行调整。在使用过程中，建议：

1. **灵活应用**：根据您的故事情节和风格需求，有选择地应用模板中的元素
2. **保持连贯性**：确保各个维度之间的设定保持一致，避免风格冲突
3. **创新发展**：在模板基础上进行创新，发展自己独特的创作风格

---
© """ + str(datetime.now().year) + """ 九猫写作系统 - 设定模板
"""

            return jsonify({
                'success': True,
                'template': template_content
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取整本书设定模板内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 章节预设模板API
@v3_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/template/<dimension>')
def api_get_chapter_template(novel_id, chapter_id, dimension):
    """获取章节的设定模板内容"""
    try:
        session = Session()
        try:
            # 获取小说和章节
            novel = session.query(Novel).get(novel_id)
            chapter = session.query(Chapter).get(chapter_id)

            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            if not chapter:
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 获取章节分析结果
            chapter_analysis_result = session.query(ChapterAnalysisResult).filter_by(
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            # 构建章节设定模板内容
            template_content = f"""# {novel.title} - {chapter.title or f'第{chapter.chapter_number}章'} - {dimension} 设定模板

## 章节基本信息
- 小说: {novel.title}
- 章节: {chapter.title or f'第{chapter.chapter_number}章'}
- 维度: {dimension}
- 适用字数范围: 5000-10000字/章

## 设定内容
"""
            # 根据不同维度添加特定内容
            if dimension == "language_style":
                template_content += """### 本章语言风格设定
- 主要语气：[正式/轻松/紧张/舒缓/幽默/严肃]
- 词汇选择：[专业术语比例/口语化程度/华丽词藻使用频率]
- 句式特点：[长短句比例/复杂句使用/特殊句式安排]
- 修辞偏好：[本章重点使用的修辞手法及效果]
"""
            elif dimension == "rhythm_pacing":
                template_content += """### 本章节奏节拍设定
- 开场节奏：[缓慢铺垫/中速展开/快速进入]
- 中段变化：[渐进加速/平稳过渡/起伏波动]
- 高潮设计：[位置/持续长度/强度]
- 结尾节奏：[戛然而止/缓慢收束/余韵悠长]
"""
            else:
                template_content += f"""### 本章{dimension}设定
- 请在此处添加本章{dimension}的具体设定内容
- 可以包括具体的数值和比例
- 适用于本章节的创作
"""

            template_content += """
## 章节内容结构建议
### 开篇部分
- 开场方式：[直接开场/环境描写开场/对话开场/悬念开场]
- 情境设置：[时间地点人物活动的明确交代]

### 中间发展部分
- 情节推进：[新情节的引入节奏/线索展开方式]
- 冲突设置：[冲突类型/展现手法/强度控制]

### 结尾部分
- 章节收束：[圆满式/悬念式/暗示式/开放式]
- 后续铺垫：[对下一章的自然引导]

---
© """ + str(datetime.now().year) + """ 九猫写作系统 - 章节设定模板
"""

            return jsonify({
                'success': True,
                'novel_id': novel_id,
                'chapter_id': chapter_id,
                'dimension': dimension,
                'template': template_content,
                'chapter': {
                    'id': chapter.id,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'chapter_number': chapter.chapter_number
                }
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节设定模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f"获取章节设定模板时出错: {str(e)}"
        }), 500

@v3_api_bp.route('/api/presets/<int:preset_id>', methods=['GET'])
def api_get_preset(preset_id):
    """获取单个预设内容"""
    try:
        session = Session()
        try:
            try:
                # 查询预设内容
                preset = session.query(Preset).get(preset_id)
                if not preset:
                    return jsonify({
                        'success': False,
                        'error': '未找到指定预设内容'
                    }), 404

                return jsonify({
                    'success': True,
                    'preset': preset.to_dict()
                })
            except Exception as e:
                logger.error(f"查询预设内容时出错: {str(e)}", exc_info=True)
                return jsonify({
                    'success': False,
                    'error': '查询预设内容时出错'
                }), 500
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_api_bp.route('/api/presets', methods=['POST'])
def api_create_or_update_preset():
    """创建或更新预设内容"""
    try:
        # 获取请求数据
        data = request.json or {}
        preset_id = data.get('id')
        title = data.get('title')
        content = data.get('content')
        category = data.get('category', 'other')

        if not title:
            return jsonify({
                'success': False,
                'error': '预设标题不能为空'
            }), 400

        if not content:
            return jsonify({
                'success': False,
                'error': '预设内容不能为空'
            }), 400

        session = Session()
        try:
            try:
                if preset_id:
                    # 更新预设内容
                    preset = session.query(Preset).get(preset_id)
                    if not preset:
                        return jsonify({
                            'success': False,
                            'error': '未找到指定预设内容'
                        }), 404

                    preset.title = title
                    preset.content = content
                    preset.category = category
                    preset.updated_at = datetime.now()
                else:
                    # 创建新预设内容
                    preset = Preset(
                        title=title,
                        content=content,
                        category=category,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(preset)

                session.commit()

                return jsonify({
                    'success': True,
                    'preset': preset.to_dict()
                })
            except Exception as e:
                session.rollback()
                logger.error(f"保存预设内容时出错: {str(e)}", exc_info=True)
                return jsonify({
                    'success': False,
                    'error': '保存预设内容时出错'
                }), 500
        finally:
            session.close()
    except Exception as e:
        logger.error(f"创建或更新预设内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_api_bp.route('/api/presets/<int:preset_id>', methods=['DELETE'])
def api_delete_preset(preset_id):
    """删除预设内容"""
    try:
        session = Session()
        try:
            try:
                # 查询预设内容
                preset = session.query(Preset).get(preset_id)
                if not preset:
                    return jsonify({
                        'success': False,
                        'error': '未找到指定预设内容'
                    }), 404

                # 删除预设内容
                session.delete(preset)
                session.commit()

                return jsonify({
                    'success': True,
                    'message': '预设内容已删除'
                })
            except Exception as e:
                session.rollback()
                logger.error(f"删除预设内容时出错: {str(e)}", exc_info=True)
                return jsonify({
                    'success': False,
                    'error': '删除预设内容时出错'
                }), 500
        finally:
            session.close()
    except Exception as e:
        logger.error(f"删除预设内容时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 获取参考蓝本分析结果
@v3_api_bp.route('/api/template/<int:template_id>/analysis')
def api_get_template_analysis(template_id):
    """获取参考蓝本的分析结果和推理过程"""
    try:
        logger.info(f"开始读取参考蓝本(ID: {template_id})的分析结果...")
        session = Session()
        try:
            # 获取小说（参考蓝本）
            template = session.query(Novel).get(template_id)
            if not template:
                logger.error(f"参考蓝本读取失败: 未找到ID为{template_id}的小说")
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 检查是否为参考蓝本
            if not (template.novel_metadata and template.novel_metadata.get('is_template')):
                logger.error(f"参考蓝本读取失败: ID为{template_id}的小说不是参考蓝本")
                return jsonify({
                    'success': False,
                    'error': '指定的小说不是参考蓝本'
                }), 400

            logger.info(f"找到参考蓝本《{template.title}》，作者: {template.author}，总字数: {template.word_count}")

            # 思考过程记录
            thinking_content = f"# 参考蓝本《{template.title}》思考过程\n\n"

            # 第一阶段：阅读原文建立基础理解
            logger.info(f"开始阅读参考蓝本原文，建立基础理解...")
            thinking_content += "## 第一阶段：阅读原文，建立基础理解\n\n"
            thinking_content += f"《{template.title}》是一部由{template.author or '未知作者'}创作的作品，总计约{template.word_count}字。"
            thinking_content += "通过阅读原文，我需要理解作品的整体风格、结构和特点。\n\n"

            # 获取整本书的分析结果
            book_analysis_results = session.query(AnalysisResult).filter_by(novel_id=template_id).all()
            logger.info(f"读取到参考蓝本整本书分析结果，共{len(book_analysis_results)}个维度")

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=template_id).order_by(Chapter.chapter_number).all()
            logger.info(f"读取到参考蓝本章节列表，共{len(chapters)}个章节")

            # 章节基本理解
            thinking_content += f"作品共有{len(chapters)}个章节，包括：\n"
            for chapter in chapters[:5]:  # 只列出前5章避免过长
                thinking_content += f"- 第{chapter.chapter_number}章: {chapter.title or f'第{chapter.chapter_number}章'} ({chapter.word_count}字)\n"
            if len(chapters) > 5:
                thinking_content += f"- ...（共{len(chapters)}章）\n"
            thinking_content += "\n"

            # 第二阶段：分析各个维度
            thinking_content += "## 第二阶段：分析各个维度的特点\n\n"

            # 获取章节分析结果
            chapter_analysis_results = {}
            dimensions_list = []
            for chapter in chapters:
                logger.info(f"正在读取章节{chapter.chapter_number}: {chapter.title or f'第{chapter.chapter_number}章'}的分析结果...")
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=template_id,
                    chapter_id=chapter.id
                ).all()

                if chapter_results:
                    chapter_analysis_results[chapter.id] = []
                    chapter_dimensions = []
                    for result in chapter_results:
                        chapter_analysis_results[chapter.id].append({
                            'dimension': result.dimension,
                            'result': result.content,  # 修改为content属性
                            'reasoning_content': result.reasoning_content,
                            'status': 'completed',  # 固定为completed状态
                            'created_at': result.created_at.isoformat() if result.created_at else None,
                            'updated_at': result.updated_at.isoformat() if result.updated_at else None
                        })
                        chapter_dimensions.append(result.dimension)

                    logger.info(f"章节{chapter.chapter_number}分析结果读取完成，包含维度: {', '.join(chapter_dimensions)}")
                else:
                    logger.warning(f"章节{chapter.chapter_number}没有分析结果")

            # 构建整本书分析结果
            book_analyses = []
            book_dimensions = []
            logger.info(f"开始深入理解整本书的分析数据...")

            # 详细思考每个维度
            for result in book_analysis_results:
                book_analyses.append({
                    'dimension': result.dimension,
                    'result': result.content,  # 修改为content属性
                    'reasoning_content': result.reasoning_content,
                    'status': 'completed',  # 固定为completed状态
                    'created_at': result.created_at.isoformat() if result.created_at else None,
                    'updated_at': result.updated_at.isoformat() if result.updated_at else None
                })
                book_dimensions.append(result.dimension)

                # 记录维度思考过程
                logger.info(f"深入理解整本书的{result.dimension}维度分析结果...")
                thinking_content += f"### {result.dimension}维度分析\n\n"
                thinking_content += "思考要点：\n"
                thinking_content += f"1. 该作品在{result.dimension}维度的主要特点是什么？\n"
                thinking_content += "2. 这些特点如何影响读者的阅读体验？\n"
                thinking_content += "3. 这些特点如何服务于作品的主题表达？\n"
                thinking_content += "4. 在创作类似作品时，如何借鉴这些特点？\n\n"

                # 添加分析总结
                thinking_content += "分析总结：\n"
                if result.content:  # 修改为content属性
                    # 将结果分成多行，更易阅读
                    result_lines = result.content.split('\n')
                    for line in result_lines[:5]:  # 只取前几行作为总结
                        if line.strip():
                            thinking_content += f"> {line.strip()}\n"
                    thinking_content += "\n"

                # 添加思考结论
                thinking_content += "思考结论：\n"
                # 根据不同维度生成不同的思考结论
                if result.dimension == 'language_style':
                    thinking_content += "该作品的语言风格是其成功的关键因素之一。在借鉴时，应注重语言的一致性和特色，但需要避免直接模仿，而是融入自己的创作风格。\n\n"
                elif result.dimension == 'structure':
                    thinking_content += "作品的结构安排体现了作者对故事节奏的精心控制。在创作类似作品时，可以参考其章节安排和情节布局方式，但应结合自己故事的需要进行调整。\n\n"
                elif result.dimension == 'character_relationships':
                    thinking_content += "人物关系网络是支撑故事发展的重要因素。在创作中，可以借鉴其人物关系的设置方式，但应确保人物关系服务于自己的故事主题。\n\n"
                else:
                    thinking_content += f"该作品在{result.dimension}维度展现出独特的创作特点，值得在创作类似作品时参考。但需要注意的是，任何借鉴都应该是有选择的，并且需要与自己作品的整体风格和主题保持一致。\n\n"

            # 构建章节数据
            chapters_data = []
            for chapter in chapters:
                chapter_data = {
                    'id': chapter.id,
                    'novel_id': chapter.novel_id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'word_count': chapter.word_count,
                    'analysis_results': chapter_analysis_results.get(chapter.id, [])
                }
                chapters_data.append(chapter_data)

            # 第三阶段：综合分析与创作启示
            thinking_content += "## 第三阶段：综合分析与创作启示\n\n"
            thinking_content += "### 整体观察\n\n"
            thinking_content += f"《{template.title}》作为参考蓝本，在{', '.join(book_dimensions)}等维度都展现出独特的创作特点。"
            thinking_content += "通过对这些维度的分析，我可以提取出该作品的核心创作理念和技巧。\n\n"

            thinking_content += "### 创作启示\n\n"
            thinking_content += "1. **风格借鉴**：在保持自我风格的同时，可以借鉴该作品在语言表达、结构布局等方面的成功经验。\n"
            thinking_content += "2. **技巧应用**：特别关注该作品在情节推进、人物塑造、场景描写等方面的技巧，有选择地应用到自己的创作中。\n"
            thinking_content += "3. **创新点**：寻找该作品的创新点，思考如何在此基础上进行创新发展，避免简单模仿。\n"
            thinking_content += "4. **读者反应**：考虑该作品的哪些特点最能引起读者共鸣，并思考如何在自己的创作中实现类似效果。\n"
            thinking_content += "5. **整体把控**：学习该作品如何在整体上保持一致性和连贯性，同时保持读者的阅读兴趣。\n\n"

            # 记录日志
            logger.info(f"参考蓝本《{template.title}》(ID: {template_id})的分析结果读取完成")
            logger.info(f"整本书分析维度: {', '.join(book_dimensions)}")
            logger.info(f"章节数量: {len(chapters_data)}")
            logger.info(f"思考过程已完成，共记录{len(thinking_content.split('\\n'))}行")

            # 将参考蓝本分析结果和思考过程保存为预设内容
            try:
                # 检查是否已存在同名预设
                existing_preset = session.query(Preset).filter_by(
                    title=f"参考蓝本-{template.title}").first()

                # 构建预设内容，添加思考过程章节
                preset_content = f"""# 参考蓝本: {template.title}

## 基本信息
- 标题: {template.title}
- 作者: {template.author or '未知'}
- 总字数: {template.word_count}
- 章节数: {len(chapters)}
- 分析维度: {', '.join(book_dimensions)}

## 整本书分析结果摘要
"""
                # 添加各维度分析结果
                for analysis in book_analyses:
                    preset_content += f"\n### {analysis['dimension']}\n{analysis['result']}\n"

                # 添加章节简介
                preset_content += "\n## 章节简介\n"
                for chapter in chapters_data[:5]:  # 只添加前5个章节避免过长
                    preset_content += f"\n### 第{chapter['chapter_number']}章: {chapter['title']}\n"
                    preset_content += f"字数: {chapter['word_count']}\n"
                    if len(chapter['analysis_results']) > 0:
                        preset_content += f"已分析维度: {', '.join([r['dimension'] for r in chapter['analysis_results']])}\n"
                    else:
                        preset_content += "暂无分析结果\n"

                if len(chapters_data) > 5:
                    preset_content += f"\n...共{len(chapters_data)}章...\n"

                # 添加思考过程章节
                preset_content += "\n## 思考过程\n"
                preset_content += thinking_content

                # 创建或更新预设
                if existing_preset:
                    logger.info(f"更新参考蓝本《{template.title}》的预设内容")
                    existing_preset.content = preset_content
                    existing_preset.updated_at = datetime.now()
                    session.add(existing_preset)
                else:
                    logger.info(f"创建参考蓝本《{template.title}》的预设内容")
                    new_preset = Preset(
                        title=f"参考蓝本-{template.title}",
                        content=preset_content,
                        category="reference_template",
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(new_preset)

                # 单独保存思考过程
                thinking_preset_title = f"思考过程-参考蓝本-{template.title}"
                existing_thinking_preset = session.query(Preset).filter_by(
                    title=thinking_preset_title).first()

                if existing_thinking_preset:
                    logger.info(f"更新参考蓝本《{template.title}》的思考过程预设")
                    existing_thinking_preset.content = thinking_content
                    existing_thinking_preset.updated_at = datetime.now()
                    session.add(existing_thinking_preset)
                else:
                    logger.info(f"创建参考蓝本《{template.title}》的思考过程预设")
                    new_thinking_preset = Preset(
                        title=thinking_preset_title,
                        content=thinking_content,
                        category="template_thinking",
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(new_thinking_preset)

                session.commit()
                logger.info(f"参考蓝本《{template.title}》的分析结果和思考过程已保存到预设内容数据库")
            except Exception as preset_error:
                logger.error(f"保存参考蓝本预设内容时出错: {str(preset_error)}", exc_info=True)
                # 继续执行，不影响API返回

            return jsonify({
                'success': True,
                'template': {
                    'id': template.id,
                    'title': template.title,
                    'author': template.author,
                    'word_count': template.word_count,
                    'created_at': template.created_at.isoformat() if template.created_at else None,
                    'updated_at': template.updated_at.isoformat() if template.updated_at else None,
                    'template_created_at': template.novel_metadata.get('template_created_at') if template.novel_metadata else None
                },
                'book_analyses': book_analyses,
                'chapters': chapters_data,
                'thinking_process': thinking_content  # 添加思考过程到返回结果
            })
        finally:
            session.close()
            logger.info(f"数据库会话已关闭")
    except Exception as e:
        logger.error(f"获取参考蓝本分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 获取参考蓝本特定章节的分析结果
@v3_api_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis')
def api_get_template_chapter_analysis(template_id, chapter_id):
    """获取参考蓝本中特定章节的分析结果和推理过程"""
    try:
        logger.info(f"开始读取参考蓝本(ID: {template_id})的章节(ID: {chapter_id})分析结果...")
        session = Session()
        try:
            # 获取小说（参考蓝本）
            template = session.query(Novel).get(template_id)
            if not template:
                logger.error(f"参考蓝本读取失败: 未找到ID为{template_id}的小说")
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 检查是否为参考蓝本
            if not (template.novel_metadata and template.novel_metadata.get('is_template')):
                logger.error(f"参考蓝本读取失败: ID为{template_id}的小说不是参考蓝本")
                return jsonify({
                    'success': False,
                    'error': '指定的小说不是参考蓝本'
                }), 400

            # 获取章节
            chapter = session.query(Chapter).filter_by(
                novel_id=template_id,
                id=chapter_id
            ).first()

            if not chapter:
                logger.error(f"参考蓝本《{template.title}》没有ID为{chapter_id}的章节")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            logger.info(f"找到参考蓝本《{template.title}》的章节{chapter.chapter_number}: {chapter.title or f'第{chapter.chapter_number}章'}")
            logger.info(f"开始阅读章节原文，建立基础理解...")

            # 获取章节分析结果
            chapter_results = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id
            ).all()

            logger.info(f"读取到章节分析结果，共{len(chapter_results)}个维度")

            # 构建章节分析结果
            analysis_results = []
            dimensions = []
            logger.info(f"开始深入理解章节分析数据...")
            for result in chapter_results:
                analysis_results.append({
                    'dimension': result.dimension,
                    'result': result.content,  # 修改为content属性
                    'reasoning_content': result.reasoning_content,
                    'status': 'completed',  # 固定为completed状态
                    'created_at': result.created_at.isoformat() if result.created_at else None,
                    'updated_at': result.updated_at.isoformat() if result.updated_at else None
                })
                dimensions.append(result.dimension)
                logger.info(f"理解章节的{result.dimension}维度分析结果...")

            # 将分析结果保存为预设内容
            try:
                # 检查是否已存在同名预设
                preset_title = f"参考蓝本-{template.title}-章节{chapter.chapter_number}"
                existing_preset = session.query(Preset).filter_by(title=preset_title).first()

                # 构建预设内容
                preset_content = f"""# 参考蓝本: {template.title} - 章节{chapter.chapter_number}分析

## 章节信息
- 小说: {template.title}
- 章节: {chapter.title or f'第{chapter.chapter_number}章'}
- 章节编号: {chapter.chapter_number}
- 字数: {chapter.word_count}
- 分析维度: {', '.join(dimensions)}

## 章节内容摘要
{chapter.content[:500]}...

## 各维度分析结果
"""
                # 添加各维度分析结果
                for analysis in analysis_results:
                    preset_content += f"\n### {analysis['dimension']}\n{analysis['result']}\n"

                # 创建或更新预设
                if existing_preset:
                    logger.info(f"更新参考蓝本《{template.title}》章节{chapter.chapter_number}的预设内容")
                    existing_preset.content = preset_content
                    existing_preset.updated_at = datetime.now()
                    session.add(existing_preset)
                else:
                    logger.info(f"创建参考蓝本《{template.title}》章节{chapter.chapter_number}的预设内容")
                    new_preset = Preset(
                        title=preset_title,
                        content=preset_content,
                        category="template_chapter",
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(new_preset)

                session.commit()
                logger.info(f"参考蓝本《{template.title}》章节{chapter.chapter_number}的分析结果已保存到预设内容数据库")
            except Exception as preset_error:
                logger.error(f"保存参考蓝本章节预设内容时出错: {str(preset_error)}", exc_info=True)
                # 继续执行，不影响API返回

            logger.info(f"参考蓝本《{template.title}》章节{chapter.chapter_number}分析结果读取完成，包含维度: {', '.join(dimensions)}")

            return jsonify({
                'success': True,
                'template': {
                    'id': template.id,
                    'title': template.title
                },
                'chapter': {
                    'id': chapter.id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'word_count': chapter.word_count,
                    'content': chapter.content
                },
                'analysis_results': analysis_results
            })
        finally:
            session.close()
            logger.info(f"数据库会话已关闭")
    except Exception as e:
        logger.error(f"获取参考蓝本章节分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 获取参考蓝本特定分析维度
@v3_api_bp.route('/api/template/<int:template_id>/analysis/<dimension>')
def api_get_template_dimension_analysis(template_id, dimension):
    """获取参考蓝本特定维度的分析结果和推理过程"""
    try:
        # 获取强制刷新参数
        force_refresh = request.args.get('force_refresh', 'false').lower() == 'true'
        timestamp = request.args.get('timestamp', '')

        logger.info(f"开始读取参考蓝本(ID: {template_id})的{dimension}维度分析结果... [强制刷新: {force_refresh}, 时间戳: {timestamp}]")

        # 创建新会话并清除缓存
        session = Session()
        session.expire_all()  # 清除会话缓存

        try:
            # 获取小说（参考蓝本）
            template = session.query(Novel).get(template_id)
            if not template:
                logger.error(f"参考蓝本读取失败: 未找到ID为{template_id}的小说")
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 检查是否为参考蓝本
            if not (template.novel_metadata and template.novel_metadata.get('is_template')):
                logger.error(f"参考蓝本读取失败: ID为{template_id}的小说不是参考蓝本")
                return jsonify({
                    'success': False,
                    'error': '指定的小说不是参考蓝本'
                }), 400

            logger.info(f"找到参考蓝本《{template.title}》，开始读取{dimension}维度分析...")

            # 获取整本书特定维度的分析结果
            analysis_result = session.query(AnalysisResult).filter_by(
                novel_id=template_id,
                dimension=dimension
            ).first()

            if not analysis_result:
                logger.error(f"参考蓝本《{template.title}》没有{dimension}维度的分析结果")
                return jsonify({
                    'success': False,
                    'error': f'未找到维度 {dimension} 的分析结果'
                }), 404

            logger.info(f"成功读取参考蓝本《{template.title}》的{dimension}维度分析结果")
            logger.info(f"开始理解分析结果内容...")

            # 记录维度思考过程
            thinking_content = f"# 参考蓝本《{template.title}》{dimension}维度思考过程\n\n"

            # 第一阶段：维度基本理解
            thinking_content += "## 第一阶段：维度基本理解\n\n"
            thinking_content += f"{dimension}维度是小说分析的重要组成部分，它主要关注的是"

            # 根据不同维度添加不同的描述
            if dimension == 'language_style':
                thinking_content += "作品的语言风格，包括语言的简洁性、华丽程度、专业性、口语化程度等特点。语言风格直接影响读者的阅读体验和对作品的感受。\n\n"
            elif dimension == 'structure':
                thinking_content += "作品的结构安排，包括情节发展脉络、章节布局、叙事节奏等。良好的结构有助于故事的连贯性和读者的沉浸感。\n\n"
            elif dimension == 'character_relationships':
                thinking_content += "作品中人物之间的互动关系，包括主次人物关系、对立关系、协作关系等。人物关系网络是推动情节发展的重要因素。\n\n"
            elif dimension == 'sentence_variation':
                thinking_content += "作品中句子的多样性和变化，包括长短句搭配、简单句复杂句交替等。句式变化有助于避免行文单调，增强表达效果。\n\n"
            elif dimension == 'paragraph_length':
                thinking_content += "作品中段落的长度控制，包括长段落与短段落的搭配使用。段落长度影响阅读节奏和文本的视觉呈现。\n\n"
            elif dimension == 'perspective_shifts':
                thinking_content += "作品中视角的转换，包括第一人称、第三人称等不同视角的使用。视角转换影响读者获取信息的方式和对故事的理解角度。\n\n"
            else:
                thinking_content += "作品的特定创作元素，这一维度反映了作者在特定方面的创作特点和技巧。\n\n"

            # 第二阶段：深入分析
            thinking_content += "## 第二阶段：深入分析\n\n"
            thinking_content += f"在《{template.title}》中，{dimension}维度的表现有以下特点：\n\n"

            # 分析结果解读
            if analysis_result.content:
                # 将结果分成段落，更易理解
                result_paragraphs = analysis_result.content.split('\n\n')
                for i, para in enumerate(result_paragraphs[:3]):  # 只取前几段作为基础
                    if para.strip():
                        thinking_content += f"### 特点{i+1}\n"
                        thinking_content += f"{para.strip()}\n\n"

                        # 添加思考延伸
                        thinking_content += "思考延伸：\n"
                        thinking_content += f"- 这一特点如何体现作者的创作意图？\n"
                        thinking_content += f"- 读者会如何感受这一特点？\n"
                        thinking_content += f"- 在创作中如何灵活运用这一特点？\n\n"

            # 第三阶段：推理过程分析
            thinking_content += "## 第三阶段：推理过程分析\n\n"
            thinking_content += "分析这一维度时，需要考虑以下因素：\n\n"
            thinking_content += "1. 作品整体风格与该维度的一致性\n"
            thinking_content += "2. 该维度特点与作品主题的呼应关系\n"
            thinking_content += "3. 该维度在不同章节的变化趋势\n"
            thinking_content += "4. 该维度与其他维度的相互影响\n\n"

            # 添加推理过程摘要
            if analysis_result.reasoning_content:
                thinking_content += "推理过程摘要：\n\n"
                reasoning_lines = analysis_result.reasoning_content.split('\n')
                key_points = [line.strip() for line in reasoning_lines if len(line.strip()) > 20 and not line.startswith("#") and not line.startswith("-")]
                for point in key_points[:5]:  # 只取几个关键点
                    thinking_content += f"> {point}\n\n"

            # 第四阶段：创作应用
            thinking_content += "## 第四阶段：创作应用\n\n"
            thinking_content += f"基于对《{template.title}》在{dimension}维度的分析，在创作中可以采取以下策略：\n\n"

            if dimension == 'language_style':
                thinking_content += "1. **风格一致性**：保持作品语言风格的一致性，确立自己独特的语言特点。\n"
                thinking_content += "2. **场景匹配**：根据不同场景的需要，灵活调整语言的简练或华丽程度。\n"
                thinking_content += "3. **人物差异化**：通过语言风格区分不同人物的性格特点。\n"
                thinking_content += "4. **情感渲染**：利用语言风格的变化增强情感表达的效果。\n"
            elif dimension == 'structure':
                thinking_content += "1. **整体规划**：在创作前做好结构规划，确保情节发展的连贯性。\n"
                thinking_content += "2. **节奏控制**：通过章节长短、情节强弱的变化控制叙事节奏。\n"
                thinking_content += "3. **高潮设计**：合理安排故事高潮，保持读者兴趣。\n"
                thinking_content += "4. **伏笔安排**：在结构中巧妙设置伏笔，增强故事的完整性。\n"
            else:
                thinking_content += "1. **特点提炼**：从参考蓝本中提炼出该维度的核心特点，理解其创作原理。\n"
                thinking_content += "2. **创新应用**：在保留原有特点优势的同时，结合自己作品特点进行创新。\n"
                thinking_content += "3. **读者考量**：考虑目标读者群体，调整该维度特点以适应读者期望。\n"
                thinking_content += "4. **整体平衡**：将该维度与作品其他元素进行平衡，确保整体协调。\n"

            logger.info(f"思考过程已完成，共记录{len(thinking_content.split('\\n'))}行")

            # 将分析结果和思考过程保存为预设内容
            try:
                # 检查是否已存在同名预设
                preset_title = f"参考蓝本-{template.title}-{dimension}"
                existing_preset = session.query(Preset).filter_by(title=preset_title).first()

                # 构建预设内容，添加思考过程
                preset_content = f"""# 参考蓝本: {template.title} - {dimension}维度分析

## 基本信息
- 标题: {template.title}
- 作者: {template.author or '未知'}
- 维度: {dimension}
- 分析时间: {analysis_result.updated_at.isoformat() if analysis_result.updated_at else analysis_result.created_at.isoformat() if analysis_result.created_at else '未知'}

## 分析结果
{analysis_result.content}

## 推理过程
{analysis_result.reasoning_content or '无推理过程记录'}

## 思考过程
{thinking_content}

## 应用建议
基于上述分析，在创作类似作品时，可以参考以下要点：

1. 语言风格特点
2. 结构布局方式
3. 人物塑造手法
4. 情节发展规律
5. 主题表达方式
"""

                # 创建或更新预设
                if existing_preset:
                    logger.info(f"更新参考蓝本《{template.title}》的{dimension}维度预设内容")
                    existing_preset.content = preset_content
                    existing_preset.updated_at = datetime.now()
                    session.add(existing_preset)
                else:
                    logger.info(f"创建参考蓝本《{template.title}》的{dimension}维度预设内容")
                    new_preset = Preset(
                        title=preset_title,
                        content=preset_content,
                        category="template_dimension",
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(new_preset)

                # 单独保存思考过程
                thinking_preset_title = f"思考过程-参考蓝本-{template.title}-{dimension}"
                existing_thinking_preset = session.query(Preset).filter_by(
                    title=thinking_preset_title).first()

                if existing_thinking_preset:
                    logger.info(f"更新参考蓝本《{template.title}》{dimension}维度的思考过程预设")
                    existing_thinking_preset.content = thinking_content
                    existing_thinking_preset.updated_at = datetime.now()
                    session.add(existing_thinking_preset)
                else:
                    logger.info(f"创建参考蓝本《{template.title}》{dimension}维度的思考过程预设")
                    new_thinking_preset = Preset(
                        title=thinking_preset_title,
                        content=thinking_content,
                        category="template_dimension_thinking",
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(new_thinking_preset)

                session.commit()
                logger.info(f"参考蓝本《{template.title}》的{dimension}维度分析结果和思考过程已保存到预设内容数据库")
            except Exception as preset_error:
                logger.error(f"保存参考蓝本{dimension}维度预设内容时出错: {str(preset_error)}", exc_info=True)
                # 继续执行，不影响API返回

            return jsonify({
                'success': True,
                'template': {
                    'id': template.id,
                    'title': template.title
                },
                'analysis': {
                    'dimension': analysis_result.dimension,
                    'result': analysis_result.content,
                    'reasoning_content': analysis_result.reasoning_content,
                    'created_at': analysis_result.created_at.isoformat() if analysis_result.created_at else None,
                    'updated_at': analysis_result.updated_at.isoformat() if analysis_result.updated_at else None
                },
                'thinking_process': thinking_content  # 添加思考过程到返回结果
            })
        finally:
            session.close()
            logger.info(f"数据库会话已关闭")
    except Exception as e:
        logger.error(f"获取参考蓝本维度分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 清除模板缓存
@v3_api_bp.route('/api/clear_template_cache', methods=['POST'])
def api_clear_template_cache():
    """清除所有模板相关的缓存"""
    try:
        # 清除内存和文件缓存
        from src.utils.cache_manager import clear_cache
        cache_cleared = clear_cache()

        # 清除SQLAlchemy会话缓存
        session = Session()
        session.expire_all()
        session.close()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '模板缓存已清除',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"清除模板缓存时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 新增API路由 - 获取参考蓝本特定章节的特定维度分析结果
@v3_api_bp.route('/api/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def api_get_template_chapter_dimension_analysis(template_id, chapter_id, dimension):
    """获取参考蓝本中特定章节的特定维度分析结果和推理过程"""
    try:
        # 获取强制刷新参数
        force_refresh = request.args.get('force_refresh', 'false').lower() == 'true'
        timestamp = request.args.get('timestamp', '')

        logger.info(f"开始读取参考蓝本(ID: {template_id})的章节(ID: {chapter_id})的{dimension}维度分析结果... [强制刷新: {force_refresh}, 时间戳: {timestamp}]")

        # 创建新会话并清除缓存
        session = Session()
        session.expire_all()  # 清除会话缓存

        try:
            # 获取小说（参考蓝本）
            template = session.query(Novel).get(template_id)
            if not template:
                logger.error(f"参考蓝本读取失败: 未找到ID为{template_id}的小说")
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 检查是否为参考蓝本
            if not (template.novel_metadata and template.novel_metadata.get('is_template')):
                logger.error(f"参考蓝本读取失败: ID为{template_id}的小说不是参考蓝本")
                return jsonify({
                    'success': False,
                    'error': '指定的小说不是参考蓝本'
                }), 400

            # 获取章节
            chapter = session.query(Chapter).filter_by(
                novel_id=template_id,
                id=chapter_id
            ).first()

            if not chapter:
                logger.error(f"参考蓝本《{template.title}》没有ID为{chapter_id}的章节")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            logger.info(f"找到参考蓝本《{template.title}》的章节{chapter.chapter_number}: {chapter.title or f'第{chapter.chapter_number}章'}")
            logger.info(f"开始读取章节的{dimension}维度分析...")

            # 获取章节特定维度的分析结果
            chapter_result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not chapter_result:
                logger.error(f"参考蓝本《{template.title}》的章节{chapter.chapter_number}没有{dimension}维度的分析结果")
                return jsonify({
                    'success': False,
                    'error': f'未找到章节维度 {dimension} 的分析结果'
                }), 404

            logger.info(f"成功读取章节{dimension}维度分析结果")
            logger.info(f"开始理解章节{dimension}维度分析数据...")

            # 将分析结果保存为预设内容
            try:
                # 检查是否已存在同名预设
                preset_title = f"参考蓝本-{template.title}-章节{chapter.chapter_number}-{dimension}"
                existing_preset = session.query(Preset).filter_by(title=preset_title).first()

                # 构建预设内容
                preset_content = f"""# 参考蓝本: {template.title} - 章节{chapter.chapter_number} - {dimension}维度分析

## 章节信息
- 小说: {template.title}
- 章节: {chapter.title or f'第{chapter.chapter_number}章'}
- 章节编号: {chapter.chapter_number}
- 维度: {dimension}
- 分析时间: {chapter_result.updated_at.isoformat() if chapter_result.updated_at else chapter_result.created_at.isoformat() if chapter_result.created_at else '未知'}

## 章节内容摘要
{chapter.content[:300]}...

## 分析结果
{chapter_result.content}

## 推理过程
{chapter_result.reasoning_content or '无推理过程记录'}

## 应用建议
基于上述分析，在创作类似章节时，可以参考以下要点：

1. 章节开头设计手法
2. 情节推进方式
3. 人物对话特点
4. 场景描写技巧
5. 章节结尾处理
"""

                # 创建或更新预设
                if existing_preset:
                    logger.info(f"更新参考蓝本《{template.title}》章节{chapter.chapter_number}的{dimension}维度预设内容")
                    existing_preset.content = preset_content
                    existing_preset.updated_at = datetime.now()
                    session.add(existing_preset)
                else:
                    logger.info(f"创建参考蓝本《{template.title}》章节{chapter.chapter_number}的{dimension}维度预设内容")
                    new_preset = Preset(
                        title=preset_title,
                        content=preset_content,
                        category="template_chapter_dimension",
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(new_preset)

                session.commit()
                logger.info(f"参考蓝本《{template.title}》章节{chapter.chapter_number}的{dimension}维度分析结果已保存到预设内容数据库")
            except Exception as preset_error:
                logger.error(f"保存参考蓝本章节{dimension}维度预设内容时出错: {str(preset_error)}", exc_info=True)
                # 继续执行，不影响API返回

            logger.info(f"参考蓝本《{template.title}》章节{chapter.chapter_number}的{dimension}维度分析结果读取完成")

            return jsonify({
                'success': True,
                'template': {
                    'id': template.id,
                    'title': template.title
                },
                'chapter': {
                    'id': chapter.id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title or f'第{chapter.chapter_number}章'
                },
                'analysis': {
                    'dimension': chapter_result.dimension,
                    'result': chapter_result.content,
                    'reasoning_content': chapter_result.reasoning_content,
                    'created_at': chapter_result.created_at.isoformat() if chapter_result.created_at else None,
                    'updated_at': chapter_result.updated_at.isoformat() if chapter_result.updated_at else None
                }
            })
        finally:
            session.close()
            logger.info(f"数据库会话已关闭")
    except Exception as e:
        logger.error(f"获取参考蓝本章节维度分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500