/**
 * 九猫 - 分析页面折叠功能修复脚本
 * 专门修复分析结果页面中的折叠/展开功能
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析页面折叠功能修复脚本已加载');

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复分析页面折叠功能');
        
        // 修复分析过程折叠区域
        fixAnalysisProcessCollapse();
        
        // 修复分析日志折叠区域
        fixAnalysisLogsCollapse();
        
        // 添加折叠/展开按钮事件监听器
        addCollapseEventListeners();
    });
    
    // 修复分析过程折叠区域
    function fixAnalysisProcessCollapse() {
        try {
            // 查找分析过程折叠区域
            const analysisProcessCollapse = document.getElementById('analysisProcessCollapse');
            if (!analysisProcessCollapse) {
                console.log('未找到分析过程折叠区域，跳过修复');
                return;
            }
            
            console.log('找到分析过程折叠区域，尝试修复');
            
            // 查找对应的按钮
            let analysisProcessButton = document.querySelector('[data-bs-target="#analysisProcessCollapse"]');
            if (!analysisProcessButton) {
                analysisProcessButton = document.querySelector('[data-target="#analysisProcessCollapse"]');
            }
            
            if (!analysisProcessButton) {
                console.log('未找到分析过程折叠按钮，尝试查找替代元素');
                
                // 查找可能包含按钮的元素
                const possibleHeaders = document.querySelectorAll('.card-header');
                for (const header of possibleHeaders) {
                    if (header.textContent.includes('分析过程')) {
                        console.log('找到可能的分析过程标题元素');
                        
                        // 检查是否已经有按钮
                        const existingButton = header.querySelector('button');
                        if (existingButton) {
                            // 更新现有按钮
                            existingButton.setAttribute('data-bs-toggle', 'collapse');
                            existingButton.setAttribute('data-bs-target', '#analysisProcessCollapse');
                            existingButton.setAttribute('aria-expanded', 'false');
                            existingButton.setAttribute('aria-controls', 'analysisProcessCollapse');
                            console.log('更新了现有分析过程按钮');
                        } else {
                            // 创建新按钮
                            const newButton = document.createElement('button');
                            newButton.className = 'btn btn-link';
                            newButton.setAttribute('type', 'button');
                            newButton.setAttribute('data-bs-toggle', 'collapse');
                            newButton.setAttribute('data-bs-target', '#analysisProcessCollapse');
                            newButton.setAttribute('aria-expanded', 'false');
                            newButton.setAttribute('aria-controls', 'analysisProcessCollapse');
                            newButton.textContent = '分析过程';
                            
                            // 添加到页面
                            const h5 = document.createElement('h5');
                            h5.className = 'mb-0';
                            h5.appendChild(newButton);
                            header.innerHTML = '';
                            header.appendChild(h5);
                            
                            console.log('创建了新的分析过程折叠按钮');
                        }
                        break;
                    }
                }
            } else {
                // 确保按钮有正确的属性
                if (analysisProcessButton.hasAttribute('data-toggle') && !analysisProcessButton.hasAttribute('data-bs-toggle')) {
                    analysisProcessButton.setAttribute('data-bs-toggle', 'collapse');
                    analysisProcessButton.setAttribute('data-bs-target', '#analysisProcessCollapse');
                    console.log('更新了分析过程按钮属性');
                }
            }
            
            // 确保折叠区域有正确的类
            if (!analysisProcessCollapse.classList.contains('collapse')) {
                analysisProcessCollapse.classList.add('collapse');
                console.log('添加了collapse类到分析过程区域');
            }
            
            // 添加手动切换功能
            addManualToggleButton(analysisProcessCollapse, '切换分析过程显示');
        } catch (e) {
            console.error('修复分析过程折叠区域时出错:', e);
        }
    }
    
    // 修复分析日志折叠区域
    function fixAnalysisLogsCollapse() {
        try {
            // 查找分析日志折叠区域
            const analysisLogsCollapse = document.getElementById('analysisLogsCollapse');
            if (!analysisLogsCollapse) {
                console.log('未找到分析日志折叠区域，跳过修复');
                return;
            }
            
            console.log('找到分析日志折叠区域，尝试修复');
            
            // 查找对应的按钮
            let analysisLogsButton = document.querySelector('[data-bs-target="#analysisLogsCollapse"]');
            if (!analysisLogsButton) {
                analysisLogsButton = document.querySelector('[data-target="#analysisLogsCollapse"]');
            }
            
            if (!analysisLogsButton) {
                console.log('未找到分析日志折叠按钮，尝试查找替代元素');
                
                // 查找可能包含按钮的元素
                const possibleHeaders = document.querySelectorAll('.card-header');
                for (const header of possibleHeaders) {
                    if (header.textContent.includes('分析日志')) {
                        console.log('找到可能的分析日志标题元素');
                        
                        // 检查是否已经有按钮
                        const existingButton = header.querySelector('button');
                        if (existingButton) {
                            // 更新现有按钮
                            existingButton.setAttribute('data-bs-toggle', 'collapse');
                            existingButton.setAttribute('data-bs-target', '#analysisLogsCollapse');
                            existingButton.setAttribute('aria-expanded', 'false');
                            existingButton.setAttribute('aria-controls', 'analysisLogsCollapse');
                            console.log('更新了现有分析日志按钮');
                        } else {
                            // 创建新按钮
                            const newButton = document.createElement('button');
                            newButton.className = 'btn btn-link';
                            newButton.setAttribute('type', 'button');
                            newButton.setAttribute('data-bs-toggle', 'collapse');
                            newButton.setAttribute('data-bs-target', '#analysisLogsCollapse');
                            newButton.setAttribute('aria-expanded', 'false');
                            newButton.setAttribute('aria-controls', 'analysisLogsCollapse');
                            newButton.textContent = '分析日志';
                            
                            // 添加到页面
                            const h5 = document.createElement('h5');
                            h5.className = 'mb-0';
                            h5.appendChild(newButton);
                            header.innerHTML = '';
                            header.appendChild(h5);
                            
                            console.log('创建了新的分析日志折叠按钮');
                        }
                        break;
                    }
                }
            } else {
                // 确保按钮有正确的属性
                if (analysisLogsButton.hasAttribute('data-toggle') && !analysisLogsButton.hasAttribute('data-bs-toggle')) {
                    analysisLogsButton.setAttribute('data-bs-toggle', 'collapse');
                    analysisLogsButton.setAttribute('data-bs-target', '#analysisLogsCollapse');
                    console.log('更新了分析日志按钮属性');
                }
            }
            
            // 确保折叠区域有正确的类
            if (!analysisLogsCollapse.classList.contains('collapse')) {
                analysisLogsCollapse.classList.add('collapse');
                console.log('添加了collapse类到分析日志区域');
            }
            
            // 添加手动切换功能
            addManualToggleButton(analysisLogsCollapse, '切换分析日志显示');
        } catch (e) {
            console.error('修复分析日志折叠区域时出错:', e);
        }
    }
    
    // 添加手动切换按钮
    function addManualToggleButton(collapseElement, buttonText) {
        try {
            // 检查是否已经有手动切换按钮
            const existingButton = collapseElement.previousElementSibling?.querySelector('.manual-toggle-btn');
            if (existingButton) {
                console.log('已存在手动切换按钮，跳过创建');
                return;
            }
            
            // 创建手动切换按钮
            const toggleButton = document.createElement('button');
            toggleButton.className = 'btn btn-sm btn-outline-secondary manual-toggle-btn ms-2';
            toggleButton.textContent = buttonText || '手动切换';
            toggleButton.onclick = function() {
                // 手动切换折叠状态
                collapseElement.classList.toggle('show');
                console.log('手动切换折叠状态: ' + (collapseElement.classList.contains('show') ? '展开' : '折叠'));
            };
            
            // 添加到页面
            const cardHeader = collapseElement.previousElementSibling;
            if (cardHeader && cardHeader.classList.contains('card-header')) {
                // 添加到卡片标题
                const titleElement = cardHeader.querySelector('h5') || cardHeader;
                titleElement.appendChild(toggleButton);
                console.log('添加了手动切换按钮到卡片标题');
            } else {
                // 创建一个新的容器
                const container = document.createElement('div');
                container.className = 'text-end mt-2 mb-2';
                container.appendChild(toggleButton);
                
                // 插入到折叠元素之前
                collapseElement.parentNode.insertBefore(container, collapseElement);
                console.log('添加了手动切换按钮到折叠元素之前');
            }
        } catch (e) {
            console.error('添加手动切换按钮时出错:', e);
        }
    }
    
    // 添加折叠/展开按钮事件监听器
    function addCollapseEventListeners() {
        try {
            // 查找所有折叠按钮
            const collapseButtons = document.querySelectorAll('[data-bs-toggle="collapse"], [data-toggle="collapse"]');
            console.log('找到 ' + collapseButtons.length + ' 个折叠按钮');
            
            // 为每个按钮添加事件监听器
            collapseButtons.forEach(function(button) {
                // 检查是否已经有事件监听器
                if (!button._hasCollapseHandler) {
                    button.addEventListener('click', function(event) {
                        event.preventDefault();
                        
                        // 获取目标元素
                        const targetSelector = button.getAttribute('data-bs-target') || button.getAttribute('data-target');
                        if (!targetSelector) {
                            console.warn('折叠按钮没有目标选择器');
                            return;
                        }
                        
                        const targetElement = document.querySelector(targetSelector);
                        if (!targetElement) {
                            console.warn('找不到折叠目标元素: ' + targetSelector);
                            return;
                        }
                        
                        console.log('切换折叠状态: ' + targetSelector);
                        
                        // 手动切换折叠状态
                        targetElement.classList.toggle('show');
                        
                        // 更新aria-expanded属性
                        const isExpanded = targetElement.classList.contains('show');
                        button.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
                    });
                    
                    button._hasCollapseHandler = true;
                    console.log('为折叠按钮添加了点击事件处理程序');
                }
            });
        } catch (e) {
            console.error('添加折叠/展开按钮事件监听器时出错:', e);
        }
    }
    
    // 添加全局辅助函数
    window.manualToggleCollapse = function(targetId) {
        try {
            const targetElement = document.getElementById(targetId);
            if (!targetElement) {
                console.warn('找不到折叠目标元素: ' + targetId);
                return;
            }
            
            console.log('手动切换折叠状态: ' + targetId);
            
            // 手动切换折叠状态
            targetElement.classList.toggle('show');
            
            // 更新对应按钮的aria-expanded属性
            const button = document.querySelector('[data-bs-target="#' + targetId + '"], [data-target="#' + targetId + '"]');
            if (button) {
                const isExpanded = targetElement.classList.contains('show');
                button.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
            }
        } catch (e) {
            console.error('手动切换折叠状态时出错:', e);
        }
    };
})();
