/**
 * 九猫 - 增强版图表修复脚本
 * 解决所有维度详情页面的Chart.js相关问题
 * 版本: 2.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('增强版图表修复脚本已加载 v2.0.0');
    
    // 存储图表实例
    window.chartInstances = window.chartInstances || {};
    
    // 已初始化的canvas集合
    const initializedCanvases = new Set();
    
    // 修复DOM操作
    function fixDOMOperations() {
        console.log('修复DOM操作');
        
        // 修复replaceChild方法
        if (!window.__replaceChildFixed) {
            const originalReplaceChild = Node.prototype.replaceChild;
            
            Node.prototype.replaceChild = function(newChild, oldChild) {
                try {
                    // 尝试使用原始方法
                    return originalReplaceChild.call(this, newChild, oldChild);
                } catch (e) {
                    console.error('replaceChild错误:', e.message);
                    
                    // 尝试安全替换：先移除旧节点，再添加新节点
                    try {
                        if (this.contains(oldChild)) {
                            this.removeChild(oldChild);
                        }
                        this.appendChild(newChild);
                        return newChild;
                    } catch (e2) {
                        console.error('安全替换也失败:', e2.message);
                        return null;
                    }
                }
            };
            
            window.__replaceChildFixed = true;
            console.log('已修复replaceChild方法');
        }
    }
    
    // 安全地创建图表
    function safeCreateChart(canvasId, config) {
        console.log(`尝试安全创建图表: ${canvasId}`);
        
        // 获取canvas元素
        let canvas;
        if (typeof canvasId === 'string') {
            canvas = document.getElementById(canvasId);
        } else {
            canvas = canvasId;
            canvasId = canvas.id || 'unknown';
        }
        
        if (!canvas) {
            console.error(`找不到canvas元素: ${canvasId}`);
            return null;
        }
        
        // 检查Chart.js是否已加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js未加载，无法创建图表');
            return null;
        }
        
        try {
            // 先销毁已有图表
            safeDestroyChart(canvas);
            
            // 创建新图表
            const ctx = canvas.getContext('2d');
            const chart = new Chart(ctx, config);
            
            // 存储图表实例
            window.chartInstances[canvasId] = chart;
            
            // 直接在canvas上存储图表实例
            canvas._chart = chart;
            
            // 标记为已初始化
            initializedCanvases.add(canvasId);
            
            console.log(`成功创建图表: ${canvasId}`);
            return chart;
        } catch (e) {
            console.error(`创建图表时出错: ${canvasId}`, e);
            
            // 如果是Canvas已在使用的错误，尝试更激进的修复
            if (e.message && e.message.includes('Canvas is already in use')) {
                console.log('检测到Canvas已在使用错误，尝试重新创建canvas');
                
                try {
                    // 创建新的canvas元素替换旧的
                    const oldCanvas = canvas;
                    const newCanvas = document.createElement('canvas');
                    
                    // 复制所有属性
                    newCanvas.id = oldCanvas.id || '';
                    newCanvas.className = oldCanvas.className || '';
                    newCanvas.style.cssText = oldCanvas.style.cssText || '';
                    newCanvas.width = oldCanvas.width || 400;
                    newCanvas.height = oldCanvas.height || 300;
                    
                    // 复制数据属性
                    Array.from(oldCanvas.attributes).forEach(attr => {
                        if (attr.name.startsWith('data-')) {
                            newCanvas.setAttribute(attr.name, attr.value);
                        }
                    });
                    
                    // 安全替换canvas
                    try {
                        // 先尝试正常替换
                        oldCanvas.parentNode.replaceChild(newCanvas, oldCanvas);
                    } catch (replaceError) {
                        console.error('替换canvas时出错:', replaceError);
                        
                        // 如果替换失败，尝试先移除旧节点，再添加新节点
                        try {
                            if (oldCanvas.parentNode) {
                                oldCanvas.parentNode.removeChild(oldCanvas);
                                oldCanvas.parentNode.appendChild(newCanvas);
                            }
                        } catch (nodeError) {
                            console.error('节点操作失败:', nodeError);
                            return null;
                        }
                    }
                    
                    // 在新canvas上创建图表
                    const ctx = newCanvas.getContext('2d');
                    const chart = new Chart(ctx, config);
                    
                    // 存储图表实例
                    window.chartInstances[canvasId] = chart;
                    
                    // 直接在canvas上存储图表实例
                    newCanvas._chart = chart;
                    
                    // 标记为已初始化
                    initializedCanvases.add(canvasId);
                    
                    console.log(`在新canvas上成功创建图表: ${canvasId}`);
                    return chart;
                } catch (e2) {
                    console.error('激进修复也失败:', e2);
                    return null;
                }
            }
            
            return null;
        }
    }
    
    // 安全地销毁图表
    function safeDestroyChart(canvas) {
        let canvasId;
        
        // 处理不同类型的输入
        if (typeof canvas === 'string') {
            canvasId = canvas;
            canvas = document.getElementById(canvasId);
        } else if (canvas) {
            canvasId = canvas.id || 'unknown';
        } else {
            return false;
        }
        
        if (!canvas) {
            console.warn(`找不到canvas元素: ${canvasId}`);
            return false;
        }
        
        try {
            // 检查是否有存储在全局对象中的图表实例
            if (window.chartInstances && window.chartInstances[canvasId]) {
                console.log(`销毁全局存储的图表实例: ${canvasId}`);
                window.chartInstances[canvasId].destroy();
                delete window.chartInstances[canvasId];
            }
            
            // 检查是否有直接存储在canvas上的图表实例
            if (canvas._chart) {
                console.log(`销毁canvas上存储的图表实例: ${canvasId}`);
                canvas._chart.destroy();
                canvas._chart = null;
            }
            
            // 从已初始化集合中移除
            initializedCanvases.delete(canvasId);
            
            return true;
        } catch (e) {
            console.error(`销毁图表时出错: ${canvasId}`, e);
            return false;
        }
    }
    
    // 修复所有图表
    function fixAllCharts() {
        console.log('开始修复所有图表');
        
        // 获取所有canvas元素
        const canvases = document.querySelectorAll('canvas');
        console.log(`找到 ${canvases.length} 个canvas元素`);
        
        // 遍历所有canvas元素
        canvases.forEach(canvas => {
            const canvasId = canvas.id || 'unknown';
            
            // 检查是否已初始化
            if (initializedCanvases.has(canvasId)) {
                console.log(`跳过已初始化的canvas: ${canvasId}`);
                return;
            }
            
            // 获取维度信息
            const dimension = canvas.getAttribute('data-dimension');
            
            // 如果有维度信息，尝试创建图表
            if (dimension) {
                console.log(`尝试为维度 ${dimension} 创建图表`);
                
                // 创建简单的图表数据
                const chartData = {
                    labels: ['分析结果'],
                    datasets: [{
                        label: dimension,
                        data: [100],
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                };
                
                // 创建图表配置
                const config = {
                    type: 'bar',
                    data: chartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                };
                
                // 安全创建图表
                safeCreateChart(canvas, config);
            }
        });
    }
    
    // 添加全局错误处理
    function addGlobalErrorHandling() {
        console.log('添加全局错误处理');
        
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                // 处理Chart.js相关错误
                if (event.error.message.includes('Chart with ID') ||
                    event.error.message.includes('Canvas is already in use')) {
                    console.error('捕获到Chart.js相关错误:', event.error.message);
                    
                    // 尝试修复
                    setTimeout(fixAllCharts, 100);
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
                
                // 处理replaceChild错误
                if (event.error.message.includes('Failed to execute \'replaceChild\' on \'Node\'')) {
                    console.error('捕获到replaceChild错误:', event.error.message);
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);
    }
    
    // 修复分析过程显示问题
    function fixAnalysisProcessDisplay() {
        console.log('修复分析过程显示问题');
        
        // 查找所有折叠按钮
        const toggleButtons = document.querySelectorAll('.toggle-process-btn');
        
        // 为每个按钮添加点击事件
        toggleButtons.forEach(button => {
            // 移除现有事件监听器
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // 添加新的事件监听器
            newButton.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const target = document.getElementById(targetId);
                
                if (target) {
                    // 切换显示状态
                    if (target.style.display === 'none') {
                        target.style.display = 'block';
                        this.textContent = '隐藏分析过程';
                    } else {
                        target.style.display = 'none';
                        this.textContent = '显示分析过程';
                    }
                }
            });
        });
    }
    
    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，开始执行修复');
        
        // 修复DOM操作
        fixDOMOperations();
        
        // 添加全局错误处理
        addGlobalErrorHandling();
        
        // 修复分析过程显示问题
        fixAnalysisProcessDisplay();
        
        // 延迟执行图表修复，确保其他脚本已加载
        setTimeout(fixAllCharts, 500);
    });
    
    // 导出函数供其他模块使用
    window.enhancedChartFix = {
        safeCreateChart: safeCreateChart,
        safeDestroyChart: safeDestroyChart,
        fixAllCharts: fixAllCharts,
        fixAnalysisProcessDisplay: fixAnalysisProcessDisplay
    };
})();
