/**
 * 九猫系统综合修复脚本
 * 用于自动加载所有修复脚本
 */

(function() {
    console.log('[综合修复] 初始化...');

    // 修复脚本列表
    const fixScripts = [
        '/static/js/resource-loader-fix.js',
        '/static/js/jquery-on-direct-fix.js',
        '/static/js/jquery-on-method-fix.js',
        '/static/js/global-jquery-error-handler.js',
        '/static/js/api-path-fix.js',
        '/static/js/template-path-fix.js',
        '/static/js/layout-fix.js',
        '/static/js/popular-tropes-fix.js',
        '/static/js/template-layout-fix.js',
        '/static/js/direct-template-fix.js',
        '/static/js/template-detail-layout-fix.js',
        '/static/js/chapter-template-layout-fix.js'
    ];

    // 已加载的脚本
    const loadedScripts = new Set();

    // 加载脚本
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            if (loadedScripts.has(src)) {
                console.log(`[综合修复] 脚本已加载: ${src}`);
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.async = true;

            script.onload = () => {
                console.log(`[综合修复] 脚本加载成功: ${src}`);
                loadedScripts.add(src);
                resolve();
            };

            script.onerror = (error) => {
                console.error(`[综合修复] 脚本加载失败: ${src}`, error);
                reject(error);
            };

            document.head.appendChild(script);
        });
    }

    // 加载所有修复脚本
    async function loadAllFixScripts() {
        console.log('[综合修复] 开始加载所有修复脚本...');

        for (const scriptSrc of fixScripts) {
            try {
                await loadScript(scriptSrc);
            } catch (error) {
                console.error(`[综合修复] 加载脚本失败: ${scriptSrc}`, error);
            }
        }

        console.log('[综合修复] 所有修复脚本加载完成');
    }

    // 添加应急修复按钮
    function addEmergencyFixButton() {
        // 检查是否已存在应急修复按钮
        if (document.getElementById('emergencyFixContainer')) {
            return;
        }

        const container = document.createElement('div');
        container.id = 'emergencyFixContainer';
        container.style.position = 'fixed';
        container.style.bottom = '10px';
        container.style.right = '10px';
        container.style.zIndex = '9999';

        const button = document.createElement('button');
        button.id = 'emergencyFixBtn';
        button.className = 'btn btn-danger btn-sm';
        button.innerHTML = '<i class="fas fa-wrench"></i> 修复脚本加载';
        button.onclick = reloadScripts;

        container.appendChild(button);
        document.body.appendChild(container);

        console.log('[综合修复] 添加应急修复按钮');
    }

    // 重新加载所有脚本
    function reloadScripts() {
        console.log('[综合修复] 重新加载所有脚本...');

        // 清除已加载脚本记录
        loadedScripts.clear();

        // 重新加载所有修复脚本
        loadAllFixScripts().then(() => {
            console.log('[综合修复] 脚本重新加载完成');
            alert('脚本已重新加载，请刷新页面以应用更改。');
        });
    }

    // 监听错误事件
    function listenForErrors() {
        window.addEventListener('error', function(event) {
            if (event.message && (
                event.message.includes('$ is not defined') ||
                event.message.includes('$(...).on is not a function') ||
                event.message.includes('jQuery') ||
                event.message.includes('404') ||
                event.message.includes('NOT FOUND')
            )) {
                // 显示应急修复按钮
                const container = document.getElementById('emergencyFixContainer');
                if (container) {
                    container.style.display = 'block';
                }
            }
        });

        console.log('[综合修复] 错误监听器已启动');
    }

    // 等待DOM加载完成
    function onDOMReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    // 初始化
    onDOMReady(() => {
        loadAllFixScripts();
        addEmergencyFixButton();
        listenForErrors();

        // 将重新加载脚本函数暴露给全局
        window.reloadScripts = reloadScripts;
    });
})();
