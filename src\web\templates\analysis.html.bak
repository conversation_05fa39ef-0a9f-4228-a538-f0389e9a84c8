{% extends "base.html" %}

{% block title %}{{ novel.title }} - {{ result.dimension }} 分析 - 九猫{% endblock %}

{% block extra_css %}
<style>
    .analysis-content {
        white-space: pre-wrap;
        line-height: 1.6;
    }

    .metadata-item {
        margin-bottom: 0.5rem;
    }

    .visualization-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }

    .analysis-logs {
        font-family: monospace;
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .log-entry {
        padding: 2px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .log-entry:last-child {
        border-bottom: none;
    }

    #analysisLogsCollapse .card-body,
    #analysisProcessCollapse .card-body {
        padding: 0.5rem;
        background-color: #f8f9fa;
    }

    .analysis-process-card {
        margin-bottom: 1rem;
        border: 1px solid rgba(0,0,0,.125);
    }

    .analysis-process-card .card-header {
        background-color: rgba(0,0,0,.03);
        padding: 0.5rem 1rem;
    }

    .analysis-process-card .card-header button {
        color: #007bff;
        text-decoration: none;
        width: 100%;
        text-align: left;
        padding: 0;
    }

    .analysis-process-card .card-header button:hover {
        color: #0056b3;
        text-decoration: none;
    }

    .analysis-process-card .card-header button:focus {
        box-shadow: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <!-- 分析进度条 -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">分析进度</h5>
                    <div id="analysisStatus" class="badge badge-info px-3 py-2">加载中...</div>
                </div>
            </div>
            <div class="card-body">
                <div class="progress mb-3" style="height: 20px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>当前维度:</strong> <span id="currentDimension">{{ result.dimension }}</span></p>
                        <p><strong>分块进度:</strong> <span id="blocksProgress">-</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>预计剩余时间:</strong> <span id="remainingTime">-</span></p>
                        <p><strong>预计完成时间:</strong> <span id="estimatedCompletionTime">-</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="card mb-4">
            <div class="card-header"><h5 class="card-title">分析日志</h5></div>
            <div class="card-body" id="logContainer" data-novel-id="{{ novel.id }}" style="background:#f8f9fa; height:200px; overflow-y:auto; font-family: monospace; font-size:0.9rem;"></div>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
                <li class="breadcrumb-item active">{{ result.dimension }} 分析</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ result.dimension }} 分析</h1>
            <div>
                <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary mr-2">
                返回小说页面
            </a>
                <a href="{{ url_for('analysis_process.view_analysis_process_viewer', novel_id=novel.id, dimension=result.dimension) }}" class="btn btn-outline-primary">
                    查看完整分析过程
                </a>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-9">
                <!-- 推理过程卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">推理过程</h5>
                    </div>
                    <div class="card-body">
                        <div class="reasoning-content">
                            <div id="reasoning-container-{{ result.dimension }}"
                                 data-reasoning-container="true"
                                 data-novel-id="{{ novel.id }}"
                                 data-dimension="{{ result.dimension }}">
                                {% if result.reasoning_content %}
                                <div class="reasoning-text">
                                    <pre>{{ result.reasoning_content }}</pre>
                                </div>
                                {% elif result.metadata and result.metadata.reasoning_content %}
                                <div class="reasoning-text">
                                    <pre>{{ result.metadata.reasoning_content }}</pre>
                                </div>
                                {% else %}
                                <div class="text-center py-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载推理过程，请稍候...</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分析过程链接 -->
                <div class="card analysis-process-card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0 d-flex justify-content-between align-items-center">
                            <span>分析过程</span>
                            <a href="{{ url_for('analysis_process.view_analysis_process_viewer', novel_id=novel.id, dimension=result.dimension) }}" class="btn btn-sm btn-outline-primary">
                                查看完整分析过程
                            </a>
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-0">
                            为了优化内存使用和提高页面加载速度，分析过程已移至单独页面。点击上方按钮查看完整分析过程。
                        </p>
                    </div>
                </div>

                <!-- 分析结果卡片 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析结果</h5>
                    </div>
                    <div class="card-body">
                        <div class="analysis-content markdown-content">
                            {{ result.content|safe }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">小说信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>标题：</strong> {{ novel.title }}</p>
                        <p><strong>作者：</strong> {{ novel.author or '未知' }}</p>
                        <p><strong>字数：</strong> {{ novel.word_count }}</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析元数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="metadata-item">
                            <strong>分析时间：</strong>
                            <span>{{ result.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </div>

                        {% if result.metadata is mapping and result.metadata.get('processing_time') %}
                            <div class="metadata-item">
                                <strong>处理时间：</strong>
                                <span>{{ "%.2f"|format(result.metadata.processing_time) }} 秒</span>
                            </div>
                        {% endif %}

                        {% if result.metadata is mapping and result.metadata.get('chunk_count') %}
                            <div class="metadata-item">
                                <strong>分析块数：</strong>
                                <span>{{ result.metadata.chunk_count }}</span>
                            </div>
                        {% endif %}

                        {% if result.updated_at and result.updated_at != result.created_at %}
                            <div class="metadata-item">
                                <strong>最后更新：</strong>
                                <span>{{ result.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
                            </div>
                        {% endif %}

                        <!-- 分析过程日志折叠区域 -->
                        {% if result.logs %}
                            <div class="metadata-item mt-3">
                                <button class="btn btn-sm btn-outline-primary" type="button" data-toggle="collapse" data-target="#analysisLogsCollapse" aria-expanded="true" aria-controls="analysisLogsCollapse">
                                    查看分析过程日志 <span class="badge badge-light">{{ result.logs|length }}</span>
                                </button>
                                <div class="collapse show mt-2" id="analysisLogsCollapse">
                                    <div class="card card-body p-2" style="max-height: 400px; overflow-y: auto;">
                                        <div class="analysis-logs" style="font-family: monospace; font-size: 0.85rem;">
                                            {% for log in result.logs %}
                                                <div class="log-entry {% if log.level == 'error' %}text-danger{% elif log.level == 'warning' %}text-warning{% endif %} {% if log.important %}font-weight-bold text-primary{% endif %}">
                                                    [{{ log.timestamp }}] {{ log.level|upper }} - {{ log.message }}
                                                </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 预先定义分析结果元数据 -->
<div id="analysis-metadata" style="display:none;" data-metadata='{}'></div>
<!-- 注意：元数据已被禁用以避免序列化错误 -->

<!-- 分析详情页面刷新阻止脚本 - 必须最先加载 -->
<script src="{{ url_for('static', filename='js/lib/analysis-detail-refresh-blocker.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/lib/analysis-detail-refresh-blocker.js';"></script>

<!-- 进度信息修复脚本（增强版） -->
<script src="{{ url_for('static', filename='js/analysis-progress-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-progress-fix-enhanced.js';"></script>
<!-- 维度详情页面修复脚本（增强版） -->
<script src="{{ url_for('static', filename='js/dimension-detail-fix-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-detail-fix-enhanced.js';"></script>

<!-- 图表禁用脚本 -->
<script src="{{ url_for('static', filename='js/disable-charts.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/disable-charts.js';"></script>
<!-- 分析结果页面修复脚本 -->
<script src="{{ url_for('static', filename='js/analysis-page-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-page-fix.js';"></script>
<!-- 增强版推理内容加载器 -->
<script src="{{ url_for('static', filename='js/reasoning-content-loader-enhanced.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-loader-enhanced.js';"></script>
<!-- 推理内容提取器 -->
<script src="{{ url_for('static', filename='js/reasoning-content-extractor.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-extractor.js';"></script>
<!-- 推理过程修复脚本 -->
<script src="{{ url_for('static', filename='js/reasoning-content-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-fix.js';"></script>
<!-- 推理过程紧急修复脚本 -->
<script src="{{ url_for('static', filename='js/reasoning-content-emergency-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-emergency-fix.js';"></script>
<!-- 推理过程修复脚本V2 -->
<script src="{{ url_for('static', filename='js/reasoning-content-fix-v2.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-fix-v2.js';"></script>
<!-- 维度分析通用修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-analysis-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-analysis-fix.js';"></script>

<!-- 分析过程日志过滤脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 日志过滤按钮
    const showAllLogsBtn = document.getElementById('showAllLogs');
    const showImportantLogsBtn = document.getElementById('showImportantLogs');
    const showWarningLogsBtn = document.getElementById('showWarningLogs');
    const showErrorLogsBtn = document.getElementById('showErrorLogs');

    if (showAllLogsBtn) {
        showAllLogsBtn.addEventListener('click', function() {
            filterLogs('all');
            setActiveButton(this);
        });
    }

    if (showImportantLogsBtn) {
        showImportantLogsBtn.addEventListener('click', function() {
            filterLogs('important');
            setActiveButton(this);
        });
    }

    if (showWarningLogsBtn) {
        showWarningLogsBtn.addEventListener('click', function() {
            filterLogs('warning');
            setActiveButton(this);
        });
    }

    if (showErrorLogsBtn) {
        showErrorLogsBtn.addEventListener('click', function() {
            filterLogs('error');
            setActiveButton(this);
        });
    }

    // 默认显示全部日志
    if (showAllLogsBtn) {
        showAllLogsBtn.click();
    }

    // 设置活动按钮样式
    function setActiveButton(button) {
        const buttons = document.querySelectorAll('.btn-group .btn');
        buttons.forEach(btn => {
            btn.classList.remove('active');

            // 移除所有颜色类
            btn.classList.remove('btn-secondary', 'btn-primary', 'btn-warning', 'btn-danger');

            // 添加回轮廓类
            if (btn.id === 'showAllLogs') btn.classList.add('btn-outline-secondary');
            if (btn.id === 'showImportantLogs') btn.classList.add('btn-outline-primary');
            if (btn.id === 'showWarningLogs') btn.classList.add('btn-outline-warning');
            if (btn.id === 'showErrorLogs') btn.classList.add('btn-outline-danger');
        });

        // 添加活动类
        button.classList.add('active');

        // 移除轮廓类，添加实心类
        if (button.id === 'showAllLogs') {
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-secondary');
        }
        if (button.id === 'showImportantLogs') {
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-primary');
        }
        if (button.id === 'showWarningLogs') {
            button.classList.remove('btn-outline-warning');
            button.classList.add('btn-warning');
        }
        if (button.id === 'showErrorLogs') {
            button.classList.remove('btn-outline-danger');
            button.classList.add('btn-danger');
        }
    }

    // 过滤日志
    function filterLogs(type) {
        const logEntries = document.querySelectorAll('.log-entry');

        logEntries.forEach(entry => {
            if (type === 'all') {
                entry.style.display = '';
            } else if (type === 'important' && entry.classList.contains('important-log')) {
                entry.style.display = '';
            } else if (type === 'warning' && entry.classList.contains('warning-log')) {
                entry.style.display = '';
            } else if (type === 'error' && entry.classList.contains('error-log')) {
                entry.style.display = '';
            } else {
                entry.style.display = 'none';
            }
        });
    }
});
</script>

{% if not (result.metadata is mapping and result.metadata.get('formatted_error')) %}
<script>
    // 全局变量
    const novelId = "{{ novel.id }}";
    const dimension = "{{ result.dimension }}";

    // 实时进度更新函数
    function fetchAnalysisProgress() {
        return fetch(`/api/analysis/progress?novel_id=${novelId}`)
            .then(res => res.json())
            .then(data => {
                if (data.success && data.progress && data.progress[dimension]) {
                    return {
                        progress: data.progress[dimension],
                        isRunning: data.is_running
                    };
                }
                throw new Error('无法获取进度数据');
            });
    }

    function updateProgressUI() {
        fetchAnalysisProgress()
            .then(data => {
                const progressData = data.progress;
                const isRunning = data.isRunning;

                // 更新进度条
                const progress = progressData.progress || 0;
                const progressBar = document.getElementById('progressBar');
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                progressBar.textContent = `${progress}%`;

                // 更新分析状态
                const statusElement = document.getElementById('analysisStatus');
                if (progress === 100) {
                    statusElement.className = 'badge badge-success px-3 py-2';
                    statusElement.textContent = '已完成';
                    progressBar.className = 'progress-bar bg-success';
                } else if (progress < 0) {
                    statusElement.className = 'badge badge-danger px-3 py-2';
                    statusElement.textContent = '已终止';
                    progressBar.className = 'progress-bar bg-danger';
                } else if (isRunning) {
                    statusElement.className = 'badge badge-primary px-3 py-2';
                    statusElement.textContent = '分析中';
                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
                } else {
                    statusElement.className = 'badge badge-warning px-3 py-2';
                    statusElement.textContent = '已暂停';
                    progressBar.className = 'progress-bar bg-warning';
                }

                // 更新块进度
                if (progressData.blocks_progress) {
                    document.getElementById('blocksProgress').textContent = progressData.blocks_progress;
                }

                // 更新时间信息
                if (progressData.remaining_time) {
                    document.getElementById('remainingTime').textContent = progressData.remaining_time;
                }

                if (progressData.eta) {
                    document.getElementById('estimatedCompletionTime').textContent = progressData.eta;
                }

                // 禁用自动刷新
                // 如果仍在分析，继续轮询
                // if (isRunning && progress < 100 && progress >= 0) {
                //     setTimeout(updateProgressUI, 5000);
                // }
                console.log('[九猫修复] 已禁用自动刷新');
            })
            .catch(err => {
                console.error('获取进度信息失败:', err);
                // 禁用自动刷新
                // 即使出错也继续尝试轮询
                // setTimeout(updateProgressUI, 10000);
                console.log('[九猫修复] 已禁用进度信息错误后的自动刷新');
            });
    }

    // 实时日志拉取函数
    function fetchLogs(since) {
        const logContainer = document.getElementById('logContainer');
        const novelId = logContainer.dataset.novelId;
        let url = `/api/analysis/logs?novel_id=${novelId}&level=all&limit=200&dimension=${dimension}`;
        if (since) url += `&since=${since}`;
        return fetch(url).then(res=>res.json());
    }

    function initLogControls() {
        const logContainer = document.getElementById('logContainer');
        let lastTs = '';

        // 添加控制按钮
        const controlDiv = document.createElement('div');
        controlDiv.className = 'mb-2 d-flex justify-content-between';
        controlDiv.innerHTML = `
            <div>
                <button id="refreshLogsBtn" class="btn btn-sm btn-outline-primary">刷新日志</button>
                <button id="clearLogsBtn" class="btn btn-sm btn-outline-secondary ml-2">清空</button>
            </div>
            <div>
                <label class="mr-2"><input type="checkbox" id="autoScrollCheck" checked> 自动滚动</label>
                <select id="logLevelFilter" class="form-control form-control-sm d-inline-block" style="width:auto">
                    <option value="all">所有级别</option>
                    <option value="info">信息</option>
                    <option value="warning">警告</option>
                    <option value="error">错误</option>
                </select>
            </div>
        `;
        logContainer.parentNode.insertBefore(controlDiv, logContainer);

        // 控制事件处理
        document.getElementById('refreshLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '<div class="text-center text-muted my-3">正在加载日志...</div>';
            lastTs = '';
            updateLogs(true);
        });

        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            logContainer.innerHTML = '';
        });

        const autoScrollCheck = document.getElementById('autoScrollCheck');

        // 日志级别过滤
        const logLevelFilter = document.getElementById('logLevelFilter');
        logLevelFilter.addEventListener('change', () => {
            const level = logLevelFilter.value;
            Array.from(logContainer.children).forEach(line => {
                if (level === 'all') {
                    line.style.display = '';
                } else {
                    const logText = line.textContent;
                    line.style.display = logText.includes(`${level.toUpperCase()}`) ? '' : 'none';
                }
            });
        });

        function updateLogs(force = false) {
            fetchLogs(lastTs).then(data=>{
                if (data.success) {
                    // 如果是首次加载且没有日志，显示提示
                    if (lastTs === '' && data.logs.length === 0) {
                        logContainer.innerHTML = '<div class="text-center text-muted my-3">暂无分析日志，可能分析已完成或尚未开始</div>';
                    }

                    // 显示新日志
                    if (data.logs.length > 0) {
                        if (logContainer.children.length === 1 && logContainer.children[0].classList.contains('text-muted')) {
                            logContainer.innerHTML = '';
                        }

                        data.logs.forEach(log => {
                            const line = document.createElement('div');
                            line.className = log.level === 'error' ? 'text-danger' :
                                            log.level === 'warning' ? 'text-warning' : '';

                            // 增加分析进度信息的突出显示
                            let message = log.message || '';
                            if ((log.important) ||
                                (message && (
                                    message.includes('进度更新') ||
                                    message.includes('分析块') ||
                                    message.includes('API调用完成') ||
                                    message.includes('%') ||
                                    message.includes('处理时间') ||
                                    message.includes('令牌使用量') ||
                                    message.includes('费用') ||
                                    message.includes('分析结果') ||
                                    message.includes('完成度') ||
                                    message.includes('耗时')
                                ))) {
                                line.className += ' font-weight-bold text-primary';
                            }

                            line.textContent = `[${log.timestamp}] ${log.level.toUpperCase()} - ${message}`;

                            // 应用日志级别过滤
                            const level = logLevelFilter.value;
                            if (level !== 'all' && !log.level.includes(level)) {
                                line.style.display = 'none';
                            }

                            logContainer.appendChild(line);
                            lastTs = log.timestamp;
                        });

                        // 自动滚动到底部
                        if (autoScrollCheck.checked) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
                    }

                    // 禁用自动刷新
                    // 判断是否需要继续轮询
                    // if (data.is_running || force) {
                    //     setTimeout(updateLogs, 3000);  // 增加轮询间隔到3秒，减轻服务器负担
                    // }
                    console.log('[九猫修复] 已禁用日志自动刷新');
                }
            }).catch(err=>{
                console.error('获取日志失败', err);
                logContainer.innerHTML += `<div class="text-danger">获取日志失败: ${err.message}</div>`;
                // 禁用自动刷新
                // setTimeout(updateLogs, 5000);  // 发生错误时延长重试时间
                console.log('[九猫修复] 已禁用日志错误后的自动刷新');
            });
        }

        // 启动日志更新
        updateLogs();
    }

    // 加载Chart.js库
    function loadChartJS() {
        return new Promise((resolve, reject) => {
            // 检查Chart是否已经加载
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载');
                resolve();
                return;
            }

            // 尝试加载本地Chart.js
            var script = document.createElement('script');
            script.src = "{{ url_for('static', filename='js/lib/chart.min.js') }}";

            script.onload = function() {
                console.log('成功从本地加载Chart.js');
                resolve();
            };

            script.onerror = function() {
                // 如果本地加载失败，尝试备用路径
                console.warn('本地Chart.js加载失败，尝试备用路径');
                var backupScript = document.createElement('script');
                backupScript.src = "/direct-static/js/lib/chart.min.js";

                backupScript.onload = function() {
                    console.log('成功从备用路径加载Chart.js');
                    resolve();
                };

                backupScript.onerror = function() {
                    // 如果备用路径也失败，尝试CDN
                    console.warn('备用路径Chart.js加载失败，尝试CDN');
                    var cdnScript = document.createElement('script');
                    cdnScript.src = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";

                    cdnScript.onload = function() {
                        console.log('成功从CDN加载Chart.js');
                        resolve();
                    };

                    cdnScript.onerror = function() {
                        console.error('所有Chart.js加载尝试均失败');
                        reject(new Error('无法加载Chart.js'));
                    };

                    document.head.appendChild(cdnScript);
                };

                document.head.appendChild(backupScript);
            };

            document.head.appendChild(script);
        });
    }

    // 初始化图表
    function initCharts() {
        // 元数据已被禁用，使用空对象
        let resultMetadata = {};
        console.log('元数据已被禁用，使用空对象');

        // 从分析结果中提取数据 - 雷达图
        // 使用API统计数据构建雷达图，确保数据是真实的
        let radarLabels = ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'];
        let radarData = [];

        // 从元数据中获取真实数据
        if (resultMetadata && resultMetadata.processing_time) {
            radarData.push(Math.round(resultMetadata.processing_time * 100) / 100);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.chunk_count) {
            radarData.push(resultMetadata.chunk_count);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.api_calls) {
            radarData.push(resultMetadata.api_calls);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.tokens_used) {
            radarData.push(resultMetadata.tokens_used);
        } else {
            radarData.push(0);
        }

        if (resultMetadata && resultMetadata.cost) {
            radarData.push(Math.round(resultMetadata.cost * 10000) / 10000);
        } else {
            radarData.push(0);
        }

        // 如果有可视化数据，使用它
        if (resultMetadata && typeof resultMetadata === 'object' &&
            resultMetadata.visualization_data &&
            resultMetadata.visualization_data.radar) {
            const visualData = resultMetadata.visualization_data.radar;
            if (visualData.labels && Array.isArray(visualData.labels) && visualData.labels.length > 0) {
                radarLabels = visualData.labels;
            }
            if (visualData.data && Array.isArray(visualData.data) && visualData.data.length > 0) {
                radarData = visualData.data;
            }
            console.log('使用自定义雷达图数据:', radarLabels, radarData);
        } else {
            console.log('使用从元数据构建的雷达图数据:', radarLabels, radarData);
        }

        // 从分析结果中提取数据 - 柱状图
        // 构建更有意义的柱状图数据
        let barLabels = radarLabels; // 使用相同的标签
        let barData = radarData;     // 使用相同的数据

        // 如果有可视化数据，使用它
        if (resultMetadata && typeof resultMetadata === 'object' &&
            resultMetadata.visualization_data &&
            resultMetadata.visualization_data.bar) {
            const visualData = resultMetadata.visualization_data.bar;
            if (visualData.labels && Array.isArray(visualData.labels) && visualData.labels.length > 0) {
                barLabels = visualData.labels;
            }
            if (visualData.data && Array.isArray(visualData.data) && visualData.data.length > 0) {
                barData = visualData.data;
            }
            console.log('使用自定义柱状图数据:', barLabels, barData);
        } else {
            console.log('使用从元数据构建的柱状图数据:', barLabels, barData);
        }

        // 加载Chart.js并初始化图表
        loadChartJS().then(() => {
            // 初始化雷达图
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: radarLabels,
                    datasets: [{
                        label: '{{ result.dimension }}分析评分',
                        data: radarData,
                        fill: true,
                        backgroundColor: 'rgba(74, 107, 223, 0.2)',
                        borderColor: 'rgb(74, 107, 223)',
                        pointBackgroundColor: 'rgb(74, 107, 223)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(74, 107, 223)'
                    }]
                },
                options: {
                    elements: {
                        line: {
                            borderWidth: 3
                        }
                    },
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 初始化柱状图
            const barCtx = document.getElementById('barChart').getContext('2d');

            // 确保数据有效
            const safeBarLabels = Array.isArray(barLabels) ? barLabels : [];
            const safeBarData = Array.isArray(barData) ? barData : [];

            // 创建柱状图
            new Chart(barCtx, {
                type: 'bar',
                data: {
                    labels: safeBarLabels,
                    datasets: [{
                        label: '{{ result.dimension }}分析指标',
                        data: safeBarData,
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(255, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(255, 206, 86, 0.2)',
                            'rgba(153, 102, 255, 0.2)',
                            'rgba(255, 159, 64, 0.2)'
                        ],
                        borderColor: [
                            'rgba(75, 192, 192, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            console.log('图表初始化完成');
        }).catch(error => {
            console.error('图表初始化失败:', error);
            // 显示错误信息给用户
            document.querySelectorAll('.chart-container').forEach(container => {
                container.innerHTML = '<div class="alert alert-danger">图表加载失败，请刷新页面重试</div>';
            });
        });
    }

    // 错误处理函数，防止JS错误导致整个页面失效
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
        return true; // 阻止默认错误处理
    };

    // 主初始化函数
    document.addEventListener('DOMContentLoaded', function() {
        // 启动进度更新
        updateProgressUI();

        // 初始化日志控制
        initLogControls();

        // 初始化图表
        initCharts();
    });
</script>
{% else %}
<script>
  console.warn('检测到分析错误，已跳过脚本初始化');
</script>
{% endif %}

<!-- 页面优化脚本 -->
<script src="{{ url_for('static', filename='js/analysis-page-optimizer.js') }}" data-critical="true"></script>
{% endblock %}