/**
 * 知识库分析结果修复脚本
 *
 * 这个脚本用于修复知识库中只有推理过程而没有分析结果的问题
 * 主要针对3.0系统和3.1系统之间的兼容性问题
 */

// 确保在jQuery加载后执行
$(document).ready(function() {
    console.log('[知识库分析结果修复] 初始化...');

    // 全局变量
    let currentTemplateId = null;
    let analysisResults = {};
    let analysisReasoningProcesses = {};

    // 监听"读取分析结果"按钮点击事件
    $('#readAnalysisBtn').on('click', function() {
        console.log('[知识库分析结果修复] 读取分析结果按钮被点击');

        // 获取当前选中的模板ID
        currentTemplateId = getCurrentTemplateId();

        if (!currentTemplateId) {
            showAlert('请先在分析展示标签页选择一个参考蓝本', 'warning');
            return;
        }

        // 读取分析结果
        fetchAnalysisResults(currentTemplateId);
    });

    // 获取当前选中的模板ID
    function getCurrentTemplateId() {
        // 尝试从全局变量获取
        if (window.selectedTemplateId) {
            return window.selectedTemplateId;
        }

        // 尝试从DOM元素获取
        const selectedTemplate = $('.template-card.selected');
        if (selectedTemplate.length > 0) {
            return selectedTemplate.data('template-id');
        }

        return null;
    }

    // 获取分析结果
    function fetchAnalysisResults(novelId) {
        console.log('[知识库分析结果修复] 获取小说分析结果，ID:', novelId);

        // 显示加载中提示
        $('#knowledgeBaseContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在读取分析结果...</p></div>');

        // 首先尝试使用v3.0 API路径
        $.ajax({
            url: `/api/novel/${novelId}/analysis`,
            method: 'GET',
            success: function(response) {
                console.log('[知识库分析结果修复] 成功获取分析结果 (v3.0 API):', response);
                processAnalysisResults(response);
            },
            error: function(xhr, status, error) {
                console.error('[知识库分析结果修复] 获取分析结果失败 (v3.0 API):', error);

                // 如果v3.0 API失败，尝试使用v3.1 API路径
                $.ajax({
                    url: `/api/novels/${novelId}/analysis`,
                    method: 'GET',
                    success: function(response) {
                        console.log('[知识库分析结果修复] 成功获取分析结果 (v3.1 API):', response);
                        processAnalysisResults(response);
                    },
                    error: function(xhr, status, error) {
                        console.error('[知识库分析结果修复] 获取分析结果失败 (v3.1 API):', error);
                        showAlert('获取分析结果失败，请确保小说已完成分析', 'danger');
                        $('#knowledgeBaseContent').html('<div class="alert alert-danger">获取分析结果失败，请确保小说已完成分析</div>');
                    }
                });
            }
        });
    }

    // 处理分析结果
    function processAnalysisResults(response) {
        if (!response.success) {
            showAlert('获取分析结果失败: ' + (response.error || '未知错误'), 'danger');
            $('#knowledgeBaseContent').html('<div class="alert alert-danger">获取分析结果失败: ' + (response.error || '未知错误') + '</div>');
            return;
        }

        // 保存分析结果
        analysisResults = {};
        analysisReasoningProcesses = {};

        // 处理整本书分析结果
        if (response.data && response.data.book_analysis) {
            const bookAnalysis = response.data.book_analysis;

            for (const dimension in bookAnalysis) {
                if (bookAnalysis.hasOwnProperty(dimension)) {
                    const result = bookAnalysis[dimension];

                    // 保存分析结果 - 如果content为空但reasoning_content不为空，则使用reasoning_content作为content
                    const content = result.content || '';
                    const reasoningContent = result.reasoning_content || '';

                    // 如果分析结果为空但推理过程不为空，则使用推理过程作为分析结果
                    if (!content && reasoningContent) {
                        console.log(`[知识库分析结果修复] 维度 ${dimension} 的分析结果为空，但推理过程不为空，使用推理过程作为分析结果`);
                        // 从推理过程中提取分析结果
                        const extractedResult = extractResultFromReasoning(reasoningContent);
                        analysisResults[dimension] = extractedResult || reasoningContent;
                    } else {
                        analysisResults[dimension] = content;
                    }

                    // 保存推理过程
                    analysisReasoningProcesses[dimension] = reasoningContent;
                }
            }
        }

        // 显示知识库内容
        displayKnowledgeBase();
    }

    // 从推理过程中提取分析结果
    function extractResultFromReasoning(reasoningContent) {
        // 如果推理过程为空，返回空字符串
        if (!reasoningContent) return '';

        // 尝试从推理过程中提取"分析结果"部分
        const resultPatterns = [
            /分析结果：([\s\S]*?)(?=\n##|\n#|$)/i,
            /最终分析：([\s\S]*?)(?=\n##|\n#|$)/i,
            /最终结果：([\s\S]*?)(?=\n##|\n#|$)/i,
            /分析总结：([\s\S]*?)(?=\n##|\n#|$)/i,
            /总结：([\s\S]*?)(?=\n##|\n#|$)/i,
            /结论：([\s\S]*?)(?=\n##|\n#|$)/i
        ];

        for (const pattern of resultPatterns) {
            const match = reasoningContent.match(pattern);
            if (match && match[1] && match[1].trim()) {
                console.log('[知识库分析结果修复] 从推理过程中提取到分析结果');
                return match[1].trim();
            }
        }

        // 如果没有找到明确的"分析结果"部分，尝试提取最后一个段落或章节
        const sections = reasoningContent.split(/\n#+\s/);
        if (sections.length > 1) {
            // 返回最后一个章节
            return sections[sections.length - 1].trim();
        }

        // 如果没有章节，尝试提取最后几个段落
        const paragraphs = reasoningContent.split(/\n\s*\n/);
        if (paragraphs.length > 2) {
            // 返回最后两个段落
            return paragraphs.slice(-2).join('\n\n').trim();
        }

        // 如果以上方法都失败，返回整个推理过程
        return reasoningContent;
    }

    // 显示知识库内容
    function displayKnowledgeBase() {
        console.log('[知识库分析结果修复] 显示知识库内容');

        let html = '<div class="accordion" id="knowledgeAccordion">';

        // 添加各个维度的分析结果
        let dimensionCount = 0;

        for (const dimension in analysisResults) {
            if (analysisResults.hasOwnProperty(dimension)) {
                dimensionCount++;

                const dimensionName = getDimensionName(dimension);
                const content = analysisResults[dimension];
                const reasoningContent = analysisReasoningProcesses[dimension] || '';

                html += `
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading${dimension}">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${dimension}" aria-expanded="false" aria-controls="collapse${dimension}">
                            ${dimensionName}
                        </button>
                    </h2>
                    <div id="collapse${dimension}" class="accordion-collapse collapse" aria-labelledby="heading${dimension}" data-bs-parent="#knowledgeAccordion">
                        <div class="accordion-body">
                            <ul class="nav nav-tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="result-tab-${dimension}" data-bs-toggle="tab" data-bs-target="#result-${dimension}" type="button" role="tab" aria-controls="result-${dimension}" aria-selected="true">分析结果</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="reasoning-tab-${dimension}" data-bs-toggle="tab" data-bs-target="#reasoning-${dimension}" type="button" role="tab" aria-controls="reasoning-${dimension}" aria-selected="false">推理过程</button>
                                </li>
                            </ul>
                            <div class="tab-content mt-3">
                                <div class="tab-pane fade show active" id="result-${dimension}" role="tabpanel" aria-labelledby="result-tab-${dimension}">
                                    <div class="markdown-body">${marked(content)}</div>
                                </div>
                                <div class="tab-pane fade" id="reasoning-${dimension}" role="tabpanel" aria-labelledby="reasoning-tab-${dimension}">
                                    <div class="markdown-body">${marked(reasoningContent)}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;
            }
        }

        html += '</div>';

        if (dimensionCount === 0) {
            html = '<div class="alert alert-warning">未找到任何分析结果，请确保小说已完成分析</div>';
        }

        $('#knowledgeBaseContent').html(html);
    }

    // 获取维度名称
    function getDimensionName(dimensionKey) {
        const dimensionMap = {
            'language_style': '语言风格',
            'rhythm_pace': '节奏节拍',
            'character_development': '人物发展',
            'emotional_expression': '情感表达',
            'scene_description': '场景描写',
            'dialogue_analysis': '对话分析',
            'theme_exploration': '主题探索',
            'conflict_setup': '冲突设置',
            'perspective_shifts': '视角转换',
            'symbolism_imagery': '象征意象',
            'cultural_context': '文化背景',
            'plot_development': '情节发展',
            'structure_layout': '结构布局',
            'outline_analysis': '章纲分析',
            'hot_meme_statistics': '热梗统计'
        };

        return dimensionMap[dimensionKey] || dimensionKey;
    }

    // 显示提示信息
    function showAlert(message, type = 'info') {
        const alertHtml = `<div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>`;

        // 如果页面上已有提示，则替换
        if ($('.alert').length > 0) {
            $('.alert').replaceWith(alertHtml);
        } else {
            // 否则在页面顶部添加
            $('#knowledgeBaseContent').prepend(alertHtml);
        }
    }
});
