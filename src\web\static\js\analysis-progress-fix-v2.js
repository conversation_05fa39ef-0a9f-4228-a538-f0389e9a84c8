/**
 * 九猫小说分析写作系统 - 分析进度修复脚本 v2
 *
 * 此脚本用于修复分析进度显示问题，确保分析完成后进度显示为100%
 * 版本: 2.0.0
 */

(function() {
    console.log('[分析进度修复v2] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        pollInterval: 35000,  // 轮询间隔（毫秒），设置为35秒
        maxRetries: 5,       // 最大重试次数
        forceCompleteTimeout: 120000,  // 强制完成超时（毫秒），设置为2分钟
        selectors: {
            progressBar: '.progress-bar',
            statusText: '.status-message, #analysisStatus',
            loadingIndicator: '.loading-indicator, .spinner-border'
        }
    };

    // 状态
    const STATE = {
        novelId: null,
        chapterId: null,
        dimension: null,
        analysisId: null,
        isChapterAnalysis: false,
        isLoading: false,
        retryCount: 0,
        pollTimer: null,
        lastProgress: 0,
        startTime: Date.now(),
        forceCompleteTimer: null
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[分析进度修复v2] ${message}`);
        }
    }

    // 获取页面信息
    function getPageInfo() {
        // 尝试从URL获取信息
        const pathParts = window.location.pathname.split('/');
        const urlParams = new URLSearchParams(window.location.search);

        // 尝试从URL参数获取分析ID
        if (urlParams.has('analysis_id')) {
            STATE.analysisId = urlParams.get('analysis_id');
            debugLog(`从URL参数获取分析ID: ${STATE.analysisId}`);

            // 从分析ID解析信息
            const idParts = STATE.analysisId.split('_');
            if (idParts.length >= 2) {
                STATE.novelId = idParts[0];

                if (idParts.length >= 3) {
                    // 检查第二部分是否为数字（章节ID）
                    if (!isNaN(idParts[1])) {
                        STATE.chapterId = idParts[1];
                        STATE.dimension = idParts[2];
                        STATE.isChapterAnalysis = true;
                    } else {
                        // 第二部分是维度名称
                        STATE.dimension = idParts[1];
                        STATE.isChapterAnalysis = false;
                    }
                }

                debugLog(`从分析ID解析信息: 小说ID=${STATE.novelId}, ${STATE.isChapterAnalysis ? '章节ID=' + STATE.chapterId + ', ' : ''}维度=${STATE.dimension}`);
                return true;
            }
        }

        // 检查是否是分析页面
        if (pathParts.includes('analyze_dimension')) {
            // 尝试从页面元素获取信息
            const novelIdInput = document.querySelector('input[name="novel_id"]');
            const chapterIdInput = document.querySelector('input[name="chapter_id"]');
            const dimensionSelect = document.querySelector('select[name="dimension"]');

            if (novelIdInput) {
                STATE.novelId = novelIdInput.value;

                if (chapterIdInput && chapterIdInput.value) {
                    STATE.chapterId = chapterIdInput.value;
                    STATE.isChapterAnalysis = true;
                }

                if (dimensionSelect) {
                    STATE.dimension = dimensionSelect.value;
                }

                debugLog(`从表单元素获取信息: 小说ID=${STATE.novelId}, ${STATE.isChapterAnalysis ? '章节ID=' + STATE.chapterId + ', ' : ''}维度=${STATE.dimension}`);
                return true;
            }
        }

        // 尝试从页面元素获取信息
        const novelElement = document.querySelector('[data-novel-id]');
        const chapterElement = document.querySelector('[data-chapter-id]');
        const dimensionElement = document.querySelector('[data-dimension]');

        if (novelElement) {
            STATE.novelId = novelElement.getAttribute('data-novel-id');

            if (chapterElement) {
                STATE.chapterId = chapterElement.getAttribute('data-chapter-id');
                STATE.isChapterAnalysis = true;
            }

            if (dimensionElement) {
                STATE.dimension = dimensionElement.getAttribute('data-dimension');
            }

            if (STATE.novelId && STATE.dimension) {
                debugLog(`从页面元素获取信息: 小说ID=${STATE.novelId}, ${STATE.isChapterAnalysis ? '章节ID=' + STATE.chapterId + ', ' : ''}维度=${STATE.dimension}`);
                return true;
            }
        }

        debugLog('无法获取页面信息', 'warn');
        return false;
    }

    // 获取分析进度
    function getAnalysisProgress() {
        if (STATE.isLoading) {
            debugLog('已有请求正在进行，跳过');
            return;
        }

        if (!STATE.novelId || !STATE.dimension) {
            debugLog('缺少必要信息，无法获取分析进度', 'warn');
            return;
        }

        STATE.isLoading = true;

        // 构建API URL
        let apiUrl;
        if (STATE.analysisId) {
            apiUrl = `/api/analysis/${STATE.analysisId}/status`;
            debugLog(`使用分析ID获取进度: ${apiUrl}`);
        } else if (STATE.isChapterAnalysis && STATE.chapterId) {
            apiUrl = `/api/analysis/progress?novel_id=${STATE.novelId}&chapter_id=${STATE.chapterId}&dimension=${STATE.dimension}`;
            debugLog(`使用章节分析API获取进度: ${apiUrl}`);
        } else {
            apiUrl = `/api/analysis/progress?novel_id=${STATE.novelId}&dimension=${STATE.dimension}`;
            debugLog(`使用整本书分析API获取进度: ${apiUrl}`);
        }

        // 添加时间戳防止缓存
        apiUrl += `&_=${Date.now()}`;

        // 发送请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('获取到分析进度数据');

                // 提取进度信息
                let progress = 0;
                let status = '';

                if (data.success && data.progress !== undefined) {
                    // 直接包含进度
                    progress = data.progress;
                    status = data.status || '';
                } else if (data.success && data.dimensions && STATE.dimension && data.dimensions[STATE.dimension]) {
                    // 包含多个维度的进度
                    const dimensionProgress = data.dimensions[STATE.dimension];
                    progress = dimensionProgress.progress || 0;
                    status = dimensionProgress.status || '';
                }

                // 更新进度显示
                updateProgressDisplay(progress, status);

                // 检查是否完成
                if (progress >= 100 || status === 'completed') {
                    handleAnalysisCompleted();
                } else if (progress > STATE.lastProgress) {
                    // 进度有更新，重置强制完成计时器
                    STATE.lastProgress = progress;
                    resetForceCompleteTimer();
                }
            })
            .catch(error => {
                debugLog(`获取分析进度出错: ${error.message}`, 'error');

                // 检查是否需要强制完成
                checkForceComplete();
            })
            .finally(() => {
                STATE.isLoading = false;
            });
    }

    // 更新进度显示
    function updateProgressDisplay(progress, status) {
        // 更新进度条
        const progressBar = document.querySelector(CONFIG.selectors.progressBar);
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
            progressBar.textContent = `${progress}%`;
            debugLog(`进度条已更新为${progress}%`);
        }

        // 更新状态文本
        const statusText = document.querySelector(CONFIG.selectors.statusText);
        if (statusText) {
            if (status) {
                statusText.textContent = status;
            } else if (progress >= 100) {
                statusText.textContent = '分析已完成';
                statusText.className = statusText.className.replace(/bg-\w+/g, '') + ' bg-success';
            } else if (progress > 0) {
                statusText.textContent = `分析进行中 (${progress}%)`;
            }
            debugLog(`状态文本已更新: ${statusText.textContent}`);
        }
    }

    // 处理分析完成
    function handleAnalysisCompleted() {
        debugLog('分析已完成');

        // 停止轮询
        if (STATE.pollTimer) {
            clearInterval(STATE.pollTimer);
            STATE.pollTimer = null;
        }

        // 取消强制完成计时器
        if (STATE.forceCompleteTimer) {
            clearTimeout(STATE.forceCompleteTimer);
            STATE.forceCompleteTimer = null;
        }

        // 更新进度为100%
        updateProgressDisplay(100, '分析已完成');

        // 延迟2秒后刷新页面
        setTimeout(() => {
            debugLog('刷新页面以显示分析结果');
            window.location.reload();
        }, 2000);
    }

    // 检查是否需要强制完成
    function checkForceComplete() {
        const elapsedTime = Date.now() - STATE.startTime;

        // 如果已经运行超过强制完成超时时间，则强制完成
        if (elapsedTime > CONFIG.forceCompleteTimeout) {
            debugLog('已超过强制完成超时时间，强制完成分析', 'warn');
            handleAnalysisCompleted();
        }
    }

    // 重置强制完成计时器
    function resetForceCompleteTimer() {
        if (STATE.forceCompleteTimer) {
            clearTimeout(STATE.forceCompleteTimer);
        }

        STATE.forceCompleteTimer = setTimeout(() => {
            debugLog('强制完成计时器触发', 'warn');
            handleAnalysisCompleted();
        }, CONFIG.forceCompleteTimeout);
    }

    // 初始化
    function init() {
        debugLog('初始化分析进度修复脚本');

        // 获取页面信息
        if (getPageInfo()) {
            // 立即获取一次分析进度
            getAnalysisProgress();

            // 设置轮询定时器
            STATE.pollTimer = setInterval(() => {
                if (!STATE.isLoading) {
                    getAnalysisProgress();
                }
            }, CONFIG.pollInterval);

            // 设置强制完成计时器
            resetForceCompleteTimer();
        }
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    console.log('[分析进度修复v2] 脚本加载完成');
})();
