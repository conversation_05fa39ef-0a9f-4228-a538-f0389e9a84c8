from src.db.connection import Session
from src.models.chapter_analysis_result import ChapterAnalysisResult

def check_chapter_analysis_results():
    session = Session()
    try:
        results = session.query(ChapterAnalysisResult).filter_by(novel_id=41, chapter_id=8).all()
        print('章节分析结果:')
        for r in results:
            print(f'ID: {r.id}, 维度: {r.dimension}, 推理内容长度: {len(r.reasoning_content) if r.reasoning_content else 0}')
    finally:
        session.close()

if __name__ == "__main__":
    check_chapter_analysis_results()
