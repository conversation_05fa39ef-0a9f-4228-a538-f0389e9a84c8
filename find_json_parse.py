import os
import re

def find_html_files(start_path):
    """查找所有HTML文件"""
    html_files = []
    for root, dirs, files in os.walk(start_path):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def find_json_parse(file_path):
    """查找包含JSON.parse的文件"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        if 'JSON.parse' in content:
            print(f"找到包含JSON.parse的文件: {file_path}")
            return True
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
    
    return False

def main():
    """主函数"""
    current_dir = os.getcwd()
    html_files = find_html_files(current_dir)
    
    print(f"找到 {len(html_files)} 个HTML文件")
    
    json_parse_files = []
    for file_path in html_files:
        if find_json_parse(file_path):
            json_parse_files.append(file_path)
    
    if json_parse_files:
        print(f"找到 {len(json_parse_files)} 个包含JSON.parse的文件:")
        for file in json_parse_files:
            print(f"  - {file}")
    else:
        print("没有找到包含JSON.parse的文件")

if __name__ == "__main__":
    main()
