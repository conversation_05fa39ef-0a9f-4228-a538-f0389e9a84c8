{% extends 'base.html' %}

{% block title %}推理内容显示测试 - 九猫系统{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3>推理内容显示测试</h3>
                </div>
                <div class="card-body">
                    <p class="lead">此页面用于测试推理内容显示修复是否有效。下方将显示来自API的推理内容。</p>
                    
                    <div class="mb-4">
                        <label for="novelIdInput" class="form-label">小说ID</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="novelIdInput" placeholder="输入小说ID">
                            <select class="form-select" id="dimensionSelect" style="max-width: 200px;">
                                <option value="language_style">语言风格</option>
                                <option value="character_feature">人物特点</option>
                                <option value="plot_structure">情节结构</option>
                                <option value="theme">主题思想</option>
                                <option value="narrative_perspective">叙述视角</option>
                                <option value="conflict_setup">矛盾设置</option>
                                <option value="emotion_expression">情感表达</option>
                                <option value="creative_technique">创作手法</option>
                            </select>
                            <button class="btn btn-primary" id="loadContentBtn">加载推理内容</button>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h5>调试信息</h5>
                        <div id="debugInfo"></div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title">推理过程</h5>
                        </div>
                        <div class="card-body">
                            <div class="reasoning-content">
                                <div id="reasoning-container-test" 
                                     data-reasoning-container="true" 
                                     data-novel-id="" 
                                     data-dimension="language_style">
                                    <div class="text-center py-3">
                                        <p>请在上方输入小说ID并点击"加载推理内容"按钮</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const novelIdInput = document.getElementById('novelIdInput');
    const dimensionSelect = document.getElementById('dimensionSelect');
    const loadContentBtn = document.getElementById('loadContentBtn');
    const reasoningContainer = document.getElementById('reasoning-container-test');
    const debugInfo = document.getElementById('debugInfo');
    
    // 显示初始调试信息
    updateDebugInfo('页面已加载，等待用户输入');
    
    // 加载按钮点击事件
    loadContentBtn.addEventListener('click', function() {
        const novelId = novelIdInput.value.trim();
        const dimension = dimensionSelect.value;
        
        if (!novelId) {
            updateDebugInfo('错误: 请输入小说ID', 'error');
            return;
        }
        
        updateDebugInfo(`正在加载: novelId=${novelId}, dimension=${dimension}`);
        
        // 更新容器属性
        reasoningContainer.setAttribute('data-novel-id', novelId);
        reasoningContainer.setAttribute('data-dimension', dimension);
        
        // 手动触发重新加载
        const event = new CustomEvent('reasoning-content-reload', {
            detail: { novelId, dimension }
        });
        document.dispatchEvent(event);
        
        updateDebugInfo(`已更新容器属性并触发重新加载事件`);
    });
    
    // 辅助函数：更新调试信息
    function updateDebugInfo(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const className = type === 'error' ? 'text-danger' : 'text-info';
        
        debugInfo.innerHTML += `
            <div class="${className}">
                <strong>[${timestamp}]</strong> ${message}
            </div>
        `;
        
        // 滚动到底部
        debugInfo.scrollTop = debugInfo.scrollHeight;
    }
    
    // 监听推理内容加载事件
    document.addEventListener('reasoning-content-loaded', function(e) {
        if (e.detail && e.detail.success) {
            updateDebugInfo(`推理内容加载成功: 长度=${e.detail.length}字符`);
        } else {
            updateDebugInfo(`推理内容加载失败: ${e.detail ? e.detail.error : '未知错误'}`, 'error');
        }
    });
    
    // 如果URL中包含参数，自动填充并加载
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('novelId')) {
        const novelId = urlParams.get('novelId');
        const dimension = urlParams.get('dimension') || 'language_style';
        
        novelIdInput.value = novelId;
        dimensionSelect.value = dimension;
        
        // 延迟加载，确保脚本初始化完成
        setTimeout(() => {
            loadContentBtn.click();
        }, 1000);
    }
});
</script>
{% endblock %} 