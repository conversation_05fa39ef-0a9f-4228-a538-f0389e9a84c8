"""
API监控路由
提供API监控页面和相关API
"""
import json
import logging
from datetime import datetime, timedelta
from flask import Blueprint, render_template, jsonify, request
from sqlalchemy import func, desc

import config
from src.db.connection import Session
from src.models.api_log import ApiLog
from src.models.analysis_result import AnalysisResult

# 创建蓝图
api_monitor_bp = Blueprint('api_monitor', __name__)

logger = logging.getLogger(__name__)

@api_monitor_bp.route('/api-monitor')
def api_monitor():
    """API监控页面"""
    try:
        return render_template('api_monitor.html', config=config)
    except Exception as e:
        logger.error(f"加载API监控页面时出错: {str(e)}")
        return render_template('error.html', error=f"加载API监控页面时出错: {str(e)}")

@api_monitor_bp.route('/api/api-stats')
def api_stats():
    """获取API调用统计数据"""
    try:
        session = Session()
        
        # 获取时间范围
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today - timedelta(days=today.weekday())
        month_start = today.replace(day=1)
        
        # 获取调用次数
        today_calls = session.query(func.count(ApiLog.id)).filter(
            ApiLog.timestamp >= today
        ).scalar() or 0
        
        week_calls = session.query(func.count(ApiLog.id)).filter(
            ApiLog.timestamp >= week_start
        ).scalar() or 0
        
        month_calls = session.query(func.count(ApiLog.id)).filter(
            ApiLog.timestamp >= month_start
        ).scalar() or 0
        
        # 获取最近24小时的调用次数（按小时分组）
        one_day_ago = datetime.now() - timedelta(days=1)
        hourly_calls = session.query(
            func.date_trunc('hour', ApiLog.timestamp).label('hour'),
            func.count(ApiLog.id).label('count')
        ).filter(
            ApiLog.timestamp >= one_day_ago
        ).group_by('hour').order_by('hour').all()
        
        # 格式化时间标签和调用次数
        time_labels = []
        call_counts = []
        
        # 创建24小时的时间点
        for i in range(24):
            hour = (datetime.now() - timedelta(hours=24-i-1)).replace(minute=0, second=0, microsecond=0)
            time_labels.append(hour.strftime('%H:%M'))
            
            # 查找该小时的调用次数
            count = 0
            for h, c in hourly_calls:
                if h.hour == hour.hour and h.day == hour.day:
                    count = c
                    break
            
            call_counts.append(count)
        
        # 获取调用状态分布
        recent_calls = session.query(ApiLog).filter(
            ApiLog.timestamp >= one_day_ago
        ).all()
        
        success_count = 0
        error_count = 0
        timeout_count = 0
        
        for call in recent_calls:
            if call.status_code and 200 <= call.status_code < 300:
                success_count += 1
            elif call.error_type == 'timeout':
                timeout_count += 1
            else:
                error_count += 1
        
        # 计算成功率
        total_recent = success_count + error_count + timeout_count
        success_rate = round((success_count / total_recent) * 100) if total_recent > 0 else 0
        
        # 计算平均响应时间
        avg_response_time = session.query(func.avg(ApiLog.response_time)).filter(
            ApiLog.timestamp >= one_day_ago,
            ApiLog.response_time.isnot(None)
        ).scalar() or 0
        
        # 获取令牌使用统计
        token_stats = {
            'input_tokens': 0,
            'output_tokens': 0,
            'total_tokens': 0,
            'today_cost': 0,
            'week_cost': 0,
            'month_cost': 0
        }
        
        # 计算总令牌使用量
        total_input = session.query(func.sum(ApiLog.input_tokens)).filter(
            ApiLog.timestamp >= one_day_ago,
            ApiLog.input_tokens.isnot(None)
        ).scalar() or 0
        
        total_output = session.query(func.sum(ApiLog.output_tokens)).filter(
            ApiLog.timestamp >= one_day_ago,
            ApiLog.output_tokens.isnot(None)
        ).scalar() or 0
        
        token_stats['input_tokens'] = total_input
        token_stats['output_tokens'] = total_output
        token_stats['total_tokens'] = total_input + total_output
        
        # 计算费用
        today_cost = session.query(func.sum(ApiLog.total_cost)).filter(
            ApiLog.timestamp >= today,
            ApiLog.total_cost.isnot(None)
        ).scalar() or 0
        
        week_cost = session.query(func.sum(ApiLog.total_cost)).filter(
            ApiLog.timestamp >= week_start,
            ApiLog.total_cost.isnot(None)
        ).scalar() or 0
        
        month_cost = session.query(func.sum(ApiLog.total_cost)).filter(
            ApiLog.timestamp >= month_start,
            ApiLog.total_cost.isnot(None)
        ).scalar() or 0
        
        token_stats['today_cost'] = today_cost
        token_stats['week_cost'] = week_cost
        token_stats['month_cost'] = month_cost
        
        return jsonify({
            'success': True,
            'stats': {
                'today_calls': today_calls,
                'week_calls': week_calls,
                'month_calls': month_calls,
                'time_labels': time_labels,
                'call_counts': call_counts,
                'success_count': success_count,
                'error_count': error_count,
                'timeout_count': timeout_count,
                'success_rate': success_rate,
                'avg_response_time': round(avg_response_time, 2),
                'token_stats': token_stats
            }
        })
    except Exception as e:
        logger.error(f"获取API统计数据时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
    finally:
        session.close()

@api_monitor_bp.route('/api/dimension-stats')
def dimension_stats():
    """获取维度分析统计数据"""
    try:
        session = Session()
        
        # 获取各维度的分析次数
        dimension_counts = session.query(
            AnalysisResult.dimension,
            func.count(AnalysisResult.id).label('count')
        ).group_by(
            AnalysisResult.dimension
        ).order_by(
            desc('count')
        ).all()
        
        # 格式化结果
        labels = [d[0] for d in dimension_counts]
        counts = [d[1] for d in dimension_counts]
        
        return jsonify({
            'success': True,
            'stats': {
                'labels': labels,
                'counts': counts
            }
        })
    except Exception as e:
        logger.error(f"获取维度统计数据时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
    finally:
        session.close()

@api_monitor_bp.route('/api/api-logs')
def api_logs():
    """获取API调用日志"""
    try:
        session = Session()
        
        # 获取最近的API调用日志
        limit = request.args.get('limit', 50, type=int)
        logs = session.query(ApiLog).order_by(
            ApiLog.timestamp.desc()
        ).limit(limit).all()
        
        # 格式化结果
        logs_json = []
        for log in logs:
            logs_json.append({
                'id': log.id,
                'timestamp': log.timestamp.isoformat(),
                'endpoint': log.endpoint,
                'method': log.method,
                'status_code': log.status_code,
                'response_time': log.response_time,
                'input_tokens': log.input_tokens,
                'output_tokens': log.output_tokens,
                'total_tokens': log.total_tokens,
                'input_cost': log.input_cost,
                'output_cost': log.output_cost,
                'total_cost': log.total_cost,
                'novel_id': log.novel_id,
                'analysis_type': log.analysis_type,
                'error_type': log.error_type
            })
        
        return jsonify({
            'success': True,
            'logs': logs_json
        })
    except Exception as e:
        logger.error(f"获取API日志时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
    finally:
        session.close()

@api_monitor_bp.route('/api/update-api-config', methods=['POST'])
def update_api_config():
    """更新API配置"""
    try:
        data = request.json
        
        # 更新分块大小配置
        if 'dimension_chunk_sizes' in data:
            for dimension, size in data['dimension_chunk_sizes'].items():
                if dimension in config.DIMENSION_CHUNK_SIZES or dimension == 'default':
                    config.DIMENSION_CHUNK_SIZES[dimension] = int(size)
        
        # 更新最大输出Token配置
        if 'dimension_max_tokens' in data:
            for dimension, tokens in data['dimension_max_tokens'].items():
                if dimension in config.DIMENSION_MAX_TOKENS or dimension == 'default':
                    config.DIMENSION_MAX_TOKENS[dimension] = int(tokens)
        
        # 保存配置到文件
        try:
            with open('config_override.json', 'w', encoding='utf-8') as f:
                json.dump({
                    'DIMENSION_CHUNK_SIZES': config.DIMENSION_CHUNK_SIZES,
                    'DIMENSION_MAX_TOKENS': config.DIMENSION_MAX_TOKENS
                }, f, ensure_ascii=False, indent=4)
            logger.info("API配置已保存到config_override.json")
        except Exception as save_error:
            logger.error(f"保存配置文件时出错: {str(save_error)}")
        
        return jsonify({
            'success': True,
            'message': 'API配置已更新'
        })
    except Exception as e:
        logger.error(f"更新API配置时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
