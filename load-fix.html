<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加载修复脚本</title>
    
    <!-- 1. 先加载修复脚本 -->
    <script>
        // 检测是否已经加载过修复脚本
        if (!window.__fixUnexpectedIdentifierLoaded) {
            console.log('开始加载修复脚本');
            
            // 创建脚本元素
            var fixScript = document.createElement('script');
            
            // 设置脚本属性
            fixScript.src = '/fix-unexpected-identifier.js';
            
            // 错误处理
            fixScript.onerror = function() {
                console.error('加载修复脚本失败，尝试备用路径');
                fixScript.src = './fix-unexpected-identifier.js';
            };
            
            // 添加到文档
            document.head.appendChild(fixScript);
        } else {
            console.log('修复脚本已加载，无需重复加载');
        }
    </script>
    
    <!-- 2. 添加内联版本作为备用 -->
    <script>
        // 检测是否已经加载过修复脚本
        setTimeout(function() {
            if (!window.__fixUnexpectedIdentifierLoaded) {
                console.log('外部修复脚本加载失败，使用内联版本');
                
                // 内联版本的修复脚本
                (function() {
                    console.log('加载内联版本的Unexpected identifier "$"修复脚本');
                
                    // 保存原始的insertBefore方法
                    const originalInsertBefore = Node.prototype.insertBefore;
                
                    // 重写insertBefore方法
                    Node.prototype.insertBefore = function(newNode, referenceNode) {
                        try {
                            // 尝试使用原始方法
                            return originalInsertBefore.call(this, newNode, referenceNode);
                        } catch (e) {
                            console.error('捕获到insertBefore错误:', e.message);
                
                            // 检查是否是特定的Unexpected identifier '$'错误
                            if (e.message && e.message.includes('Unexpected identifier')) {
                                console.log('检测到特定的insertBefore错误，尝试安全插入');
                
                                try {
                                    // 检查是否是脚本元素
                                    if (newNode.nodeType === Node.ELEMENT_NODE && newNode.tagName.toLowerCase() === 'script') {
                                        console.log('检测到脚本元素，使用特殊处理');
                                        
                                        // 创建新的脚本元素
                                        const safeScript = document.createElement('script');
                                        
                                        // 复制属性
                                        for (let i = 0; i < newNode.attributes.length; i++) {
                                            const attr = newNode.attributes[i];
                                            safeScript.setAttribute(attr.name, attr.value);
                                        }
                                        
                                        // 安全设置脚本内容 - 避免使用模板字符串中的${}
                                        if (newNode.textContent) {
                                            // 替换所有的${为一个安全标记
                                            const safeContent = newNode.textContent.replace(/\$\{/g, '___DOLLAR_BRACE___');
                                            safeScript.textContent = safeContent;
                                        }
                                        
                                        // 安全插入
                                        if (referenceNode) {
                                            return originalInsertBefore.call(this, safeScript, referenceNode);
                                        } else {
                                            return this.appendChild(safeScript);
                                        }
                                    } else {
                                        // 对于非脚本元素，创建一个克隆
                                        const safeNode = newNode.cloneNode(true);
                                        
                                        // 安全插入
                                        if (referenceNode) {
                                            return originalInsertBefore.call(this, safeNode, referenceNode);
                                        } else {
                                            return this.appendChild(safeNode);
                                        }
                                    }
                                } catch (e2) {
                                    console.error('安全插入失败:', e2.message);
                                    
                                    // 最后尝试：直接添加到文档末尾
                                    try {
                                        return this.appendChild(newNode);
                                    } catch (e3) {
                                        console.error('所有方法都失败:', e3.message);
                                    }
                                }
                            } else {
                                // 如果不是特定的错误，重新抛出
                                throw e;
                            }
                        }
                    };
                    
                    // 初始化完成标记
                    window.__fixUnexpectedIdentifierLoaded = true;
                    console.log('内联版本的Unexpected identifier "$"修复脚本加载完成');
                })();
            }
        }, 1000); // 给外部脚本1秒加载时间
    </script>
    
    <!-- 3. 添加页面重定向 -->
    <script>
        // 5秒后重定向到character_relationships页面
        setTimeout(function() {
            // 获取当前URL
            var currentUrl = window.location.href;
            
            // 检查是否需要重定向
            if (!currentUrl.includes('character_relationships')) {
                console.log('5秒后重定向到character_relationships页面');
                window.location.href = '/novel/11/analysis/character_relationships';
            } else {
                console.log('已在character_relationships页面，无需重定向');
            }
        }, 5000);
    </script>
</head>
<body>
    <div style="text-align: center; margin-top: 50px; font-family: sans-serif;">
        <h1>正在加载修复脚本</h1>
        <p>修复 "Failed to execute 'insertBefore' on 'Node': Unexpected identifier '$'" 错误</p>
        <p>5秒后将自动跳转到角色关系分析页面...</p>
        <div id="countdown" style="font-size: 24px; margin: 20px;">5</div>
        <button onclick="window.location.href='/novel/11/analysis/character_relationships'" style="padding: 10px 20px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
            立即跳转
        </button>
    </div>
    
    <script>
        // 倒计时效果
        var countdown = 5;
        var countdownElement = document.getElementById('countdown');
        
        var timer = setInterval(function() {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                countdownElement.textContent = '正在跳转...';
            }
        }, 1000);
    </script>
</body>
</html> 