"""
九猫 - 修复数据库脚本
使用Python的SQLite模块直接修复数据库
"""

import os
import sys
import sqlite3
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def find_database_file():
    """查找数据库文件"""
    # 可能的数据库文件路径
    possible_paths = [
        'data/novels.db',
        'src/data/novels.db',
        'novels.db',
        'instance/novels.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"找到数据库文件: {path}")
            return path
    
    # 如果没有找到，搜索当前目录及其子目录
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                path = os.path.join(root, file)
                logger.info(f"找到数据库文件: {path}")
                return path
    
    logger.error("未找到数据库文件")
    return None

def fix_database():
    """修复数据库"""
    # 查找数据库文件
    db_path = find_database_file()
    if not db_path:
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 开始事务
        cursor.execute("BEGIN TRANSACTION")
        
        # 查询要修复的分析结果
        cursor.execute("""
            SELECT id FROM analysis_results 
            WHERE novel_id = 4 AND dimension = 'character_relationships'
        """)
        
        result = cursor.fetchone()
        
        if result:
            result_id = result[0]
            logger.info(f"找到要修复的分析结果ID: {result_id}")
            
            # 删除现有分析结果
            cursor.execute("""
                DELETE FROM analysis_results 
                WHERE id = ?
            """, (result_id,))
            
            logger.info("已删除现有分析结果")
        
        # 创建有效的内容
        valid_content = """# 分析过程中出错

## 错误详情
分析维度 **character_relationships** 时遇到了问题。

## 错误信息
```
name 'stats_start' is not defined
```

## 建议操作
请尝试以下解决方法：
1. 刷新页面并重新尝试分析
2. 检查小说文本是否过长或包含特殊字符
3. 确认API连接正常

如果问题持续存在，请联系系统管理员。
"""
        
        # 创建有效的元数据
        valid_metadata = {
            "processing_time": 0,
            "chunk_count": 0,
            "api_calls": 0,
            "tokens_used": 0,
            "cost": 0,
            "fixed_by_script": True,
            "fix_timestamp": datetime.now().isoformat()
        }
        
        # 插入新的分析结果
        cursor.execute("""
            INSERT INTO analysis_results 
            (novel_id, dimension, content, analysis_metadata, created_at, updated_at) 
            VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))
        """, (
            4, 
            'character_relationships', 
            valid_content, 
            json.dumps(valid_metadata)
        ))
        
        # 提交事务
        cursor.execute("COMMIT")
        
        # 验证修复结果
        cursor.execute("""
            SELECT id FROM analysis_results 
            WHERE novel_id = 4 AND dimension = 'character_relationships'
        """)
        
        new_result = cursor.fetchone()
        
        if not new_result:
            logger.error("修复失败：未找到新插入的分析结果")
            return False
        
        new_result_id = new_result[0]
        logger.info(f"修复成功，新分析结果ID: {new_result_id}")
        
        # 关闭连接
        conn.close()
        
        return True
    except Exception as e:
        logger.error(f"修复数据库时出错: {str(e)}")
        try:
            # 回滚事务
            cursor.execute("ROLLBACK")
        except:
            pass
        return False

def main():
    """主函数"""
    logger.info("开始修复数据库")
    
    # 修复数据库
    success = fix_database()
    
    if success:
        logger.info("数据库修复成功")
        logger.info("请重启服务器以应用更改")
    else:
        logger.error("数据库修复失败")

if __name__ == "__main__":
    main()
