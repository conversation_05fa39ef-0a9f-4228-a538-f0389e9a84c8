"""
九猫小说分析写作系统 - 示例推理过程插入脚本

此脚本用于向数据库中插入示例推理过程内容，
确保分析结果能够正确显示推理过程。
"""
import os
import sys
import sqlite3
import logging
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('insert_sample_reasoning')

# 数据库路径
DB_PATH = 'novels.db'  # 主数据库文件在根目录

# 示例推理过程内容
SAMPLE_REASONING = """
# 分析思路说明

1. **文本特征提取**：首先对文本进行基础特征提取，包括词频统计、句式分析和情感倾向评估。
2. **结构化分析**：将文本按照叙事结构进行分解，识别开端、发展、高潮和结局等关键部分。
3. **角色关系图谱**：构建角色之间的关系网络，分析主要角色的互动模式和情感连接。
4. **冲突点识别**：定位文本中的主要冲突点，评估冲突的强度和解决方式。
5. **主题深度探索**：挖掘文本中隐含的主题和寓意，分析作者的价值观表达。
6. **读者情感预测**：预测读者在阅读过程中可能产生的情感反应和共鸣点。
7. **写作技巧评估**：分析作者使用的写作技巧，包括修辞手法、叙事视角和节奏控制。
8. **市场定位分析**：评估作品的市场定位和目标读者群体，预测商业潜力。

## 详细分析

本文通过多层次分析，揭示了作品的核心价值和艺术特色。文本结构清晰，情节发展自然，角色塑造立体。主题表达深刻，通过巧妙的叙事手法和细节描写，成功引发读者共鸣。

在角色关系方面，主角与配角之间形成了复杂而真实的互动网络，每个角色都有其独特的成长轨迹和内心变化。冲突设置合理，既有外部环境带来的挑战，也有角色内心的矛盾和挣扎。

写作技巧上，作者善于运用多种修辞手法和叙事视角，节奏控制得当，高潮部分扣人心弦。语言风格独特，既有文学性的优美描写，也有接地气的对话和内心独白。

总体而言，这是一部具有深度和广度的作品，既能满足普通读者的娱乐需求，也能为追求思想深度的读者提供精神食粮。市场前景看好，特别适合25-45岁的成熟读者群体。
"""

def insert_sample_reasoning(conn):
    """向数据库中插入示例推理过程内容"""
    cursor = conn.cursor()
    
    # 获取所有分析结果
    cursor.execute("SELECT id, dimension FROM analysis_results")
    results = cursor.fetchall()
    logger.info(f"找到 {len(results)} 条分析结果")
    
    updated_count = 0
    for result in results:
        result_id, dimension = result
        
        # 更新分析结果
        cursor.execute(
            "UPDATE analysis_results SET reasoning_content = ? WHERE id = ?",
            (f"维度 {dimension} 的推理过程:\n\n{SAMPLE_REASONING}", result_id)
        )
        updated_count += 1
        logger.info(f"已更新分析结果 ID={result_id}, 维度={dimension}")
    
    conn.commit()
    logger.info(f"成功更新 {updated_count}/{len(results)} 条分析结果")
    
    # 获取所有章节分析结果
    cursor.execute("SELECT id, dimension, chapter_id FROM chapter_analysis_results")
    chapter_results = cursor.fetchall()
    logger.info(f"找到 {len(chapter_results)} 条章节分析结果")
    
    chapter_updated_count = 0
    for result in chapter_results:
        result_id, dimension, chapter_id = result
        
        # 更新章节分析结果
        cursor.execute(
            "UPDATE chapter_analysis_results SET reasoning_content = ? WHERE id = ?",
            (f"章节 {chapter_id} 维度 {dimension} 的推理过程:\n\n{SAMPLE_REASONING}", result_id)
        )
        chapter_updated_count += 1
        logger.info(f"已更新章节分析结果 ID={result_id}, 章节ID={chapter_id}, 维度={dimension}")
    
    conn.commit()
    logger.info(f"成功更新 {chapter_updated_count}/{len(chapter_results)} 条章节分析结果")

def main():
    """主函数"""
    logger.info("开始插入示例推理过程")
    
    # 检查数据库文件是否存在
    if not os.path.exists(DB_PATH):
        logger.error(f"数据库文件 {DB_PATH} 不存在")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(DB_PATH)
        
        # 插入示例推理过程
        insert_sample_reasoning(conn)
        
        # 关闭连接
        conn.close()
        
        logger.info("示例推理过程插入完成")
    except Exception as e:
        logger.error(f"插入示例推理过程时出错: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
