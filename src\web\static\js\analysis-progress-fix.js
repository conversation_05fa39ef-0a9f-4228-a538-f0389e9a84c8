/**
 * 九猫 - 分析进度修复脚本（完全禁用版）
 * 此脚本已被完全禁用，不再执行任何自动刷新功能
 * 版本: 4.0.0 - 完全禁用版
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 简化日志输出，避免控制台日志过多
    console.log('分析进度修复脚本已加载 v4.0.0 (完全禁用版) - 所有自动刷新功能已禁用');

    // 配置 - 所有功能已禁用
    const CONFIG = {
        enableDebug: false,          // 禁用调试模式
        pollInterval: 0,             // 禁用轮询
        autoRefreshDelay: 0,         // 禁用自动刷新
        maxRetries: 0,               // 禁用重试
        retryDelay: 0,               // 禁用重试延迟
        domCheckInterval: 0,         // 禁用DOM检查
        consoleCheckInterval: 0,     // 禁用控制台检查
        forceRefreshTimeout: 0,      // 禁用强制刷新
        autoRefreshInterval: 0,      // 禁用自动刷新间隔
        maxConsoleBufferSize: 0,     // 禁用控制台缓冲区
        logThrottleInterval: 0,      // 禁用日志节流
        completionKeywords: []       // 禁用完成关键词
    };

    // 全局变量
    let novelId = null;
    let dimension = null;
    let pollTimer = null;
    let domCheckTimer = null;
    let consoleCheckTimer = null;
    let forceRefreshTimer = null;
    let autoRefreshTimer = null;
    let memoryCleanupTimer = null;
    let retryCount = 0;
    let lastProgress = 0;
    let isAnalysisRunning = false;
    let analysisStartTime = null;
    let completionDetected = false;
    let pageRefreshing = false;
    let originalConsoleLog = console.log;
    let consoleBuffer = [];
    let loadingSpinnerFound = false;
    let analysisCompletedTextFound = false;
    let lastLogTime = 0;
    let lastRefreshTime = 0;
    let refreshCount = 0;

    // 内存优化：安全日志函数
    function logDebug(message) {
        if (!CONFIG.enableDebug) return;

        const now = Date.now();
        if (now - lastLogTime > CONFIG.logThrottleInterval) {
            // 使用原始的console.log，避免递归调用
            originalConsoleLog.call(console, message);
            lastLogTime = now;
        }
    }

    // 安全日志函数，直接使用原始console.log，避免递归
    function safeLog(message) {
        // 使用原始的console.log，避免递归调用
        originalConsoleLog.call(console, message);
    }

    // 内存优化：定期清理内存
    function setupMemoryCleanup() {
        if (memoryCleanupTimer) {
            clearInterval(memoryCleanupTimer);
        }

        // 每30秒执行一次内存清理
        memoryCleanupTimer = setInterval(() => {
            // 清空不再需要的大型数组
            consoleBuffer = consoleBuffer.slice(-CONFIG.maxConsoleBufferSize);

            // 手动触发垃圾回收（仅在某些浏览器中有效）
            if (window.gc) {
                try {
                    window.gc();
                } catch (e) {
                    // 忽略错误
                }
            }
        }, 30000);
    }

    // 检查当前页面是否是分析页面
    function isAnalysisPage() {
        // 检查URL路径
        const path = window.location.pathname;

        // 分析页面的URL模式
        const analysisPatterns = [
            /\/novel\/\d+\/analysis\//,  // 例如 /novel/34/analysis/language_style
            /\/analysis\/\d+\//,         // 例如 /analysis/34/
            /\/analysis_detail\//        // 例如 /analysis_detail/
        ];

        // 检查是否匹配任何分析页面模式
        for (const pattern of analysisPatterns) {
            if (pattern.test(path)) {
                return true;
            }
        }

        // 检查是否有分析相关的元素
        const analysisElements = [
            document.querySelector('.analysis-progress'),
            document.querySelector('.analysis-result'),
            document.querySelector('.analysis-console'),
            document.querySelector('[data-analysis-id]'),
            document.querySelector('[data-dimension]')
        ];

        // 如果找到任何分析相关元素，认为是分析页面
        return analysisElements.some(el => el !== null);
    }

    // 检查当前页面是否需要自动刷新
    function shouldEnableAutoRefresh() {
        // 如果是小说详情页面（如/novel/34），完全禁用自动刷新
        if (window.location.pathname.match(/\/novel\/\d+\/?$/)) {
            safeLog('当前是小说详情页面，禁用自动刷新');
            return false;
        }

        // 如果是分析页面，启用自动刷新
        if (isAnalysisPage()) {
            return true;
        }

        // 如果URL中包含特定参数，启用自动刷新
        if (window.location.search.includes('auto_refresh=true')) {
            return true;
        }

        // 默认禁用自动刷新
        return false;
    }

    // 初始化（完全禁用版）
    function initialize() {
        // 保存原始console.log引用
        originalConsoleLog = console.log;

        // 使用安全日志
        safeLog('初始化分析进度修复（完全禁用版）');
        safeLog('所有自动刷新功能已禁用，不会执行任何自动刷新操作');

        // 清除所有可能的定时器
        function clearAllTimers() {
            try {
                // 清除所有可能的定时器
                for (let i = 1; i < 10000; i++) {
                    try {
                        clearTimeout(i);
                        clearInterval(i);
                    } catch (e) {
                        // 忽略错误
                    }
                }
                safeLog('已清除所有可能的定时器');
            } catch (e) {
                safeLog('清除定时器时出错: ' + e.message);
            }
        }

        // 立即清除所有定时器
        clearAllTimers();

        // 禁用页面的自动刷新元标记
        try {
            const metaTags = document.getElementsByTagName('meta');
            for (let i = 0; i < metaTags.length; i++) {
                if (metaTags[i].httpEquiv === 'refresh') {
                    metaTags[i].parentNode.removeChild(metaTags[i]);
                    safeLog('已移除页面自动刷新元标记');
                }
            }
        } catch (e) {
            safeLog('移除自动刷新元标记时出错: ' + e.message);
        }

        // 再次清除所有定时器（确保没有其他代码设置了定时器）
        setTimeout(clearAllTimers, 1000);
        setTimeout(clearAllTimers, 5000);
        setTimeout(clearAllTimers, 10000);

        safeLog('分析进度修复初始化完成 - 所有自动刷新功能已禁用');
    }

    // 清理资源
    function cleanupResources() {
        // 清除所有定时器
        if (pollTimer) clearInterval(pollTimer);
        if (domCheckTimer) clearInterval(domCheckTimer);
        if (consoleCheckTimer) clearInterval(consoleCheckTimer);
        if (forceRefreshTimer) clearTimeout(forceRefreshTimer);
        if (autoRefreshTimer) clearTimeout(autoRefreshTimer);
        if (memoryCleanupTimer) clearInterval(memoryCleanupTimer);

        // 清空大型数组
        consoleBuffer = [];

        // 恢复原始console.log
        if (originalConsoleLog) {
            console.log = originalConsoleLog;
        }
    }

    // 从页面中提取小说ID和维度
    function extractNovelAndDimension() {
        // 尝试从全局变量中获取
        if (window.novelId && window.dimension) {
            novelId = window.novelId;
            dimension = window.dimension;
            return;
        }

        // 尝试从数据属性中获取
        const novelElement = document.querySelector('[data-novel-id]');
        if (novelElement) {
            novelId = novelElement.dataset.novelId;
        }

        const dimensionElement = document.querySelector('[data-dimension]');
        if (dimensionElement) {
            dimension = dimensionElement.dataset.dimension;
        }

        // 尝试从表单中获取
        const form = document.getElementById('analyze-form');
        if (form && form.dataset.novelId) {
            novelId = form.dataset.novelId;
        }

        // 尝试从进度条中获取
        const progressBar = document.querySelector('.progress-bar[data-novel-id][data-dimension]');
        if (progressBar) {
            novelId = progressBar.dataset.novelId;
            dimension = progressBar.dataset.dimension;
        }
    }

    // 从URL中提取小说ID和维度
    function extractFromURL() {
        const path = window.location.pathname;
        const pathParts = path.split('/');

        // 尝试匹配 /novel/{id}/analysis/{dimension} 格式
        for (let i = 0; i < pathParts.length; i++) {
            if (pathParts[i] === 'novel' && i + 1 < pathParts.length) {
                novelId = pathParts[i + 1];
            }
            if (pathParts[i] === 'analysis' && i + 1 < pathParts.length) {
                dimension = pathParts[i + 1];
            }
        }

        // 如果找到了小说ID和维度，开始轮询进度
        if (novelId && dimension) {
            console.log(`从URL中提取到小说ID: ${novelId}, 维度: ${dimension}`);
            startProgressPolling();
        }
    }

    // 开始轮询进度
    function startProgressPolling() {
        if (pollTimer) {
            clearInterval(pollTimer);
        }

        console.log(`开始轮询分析进度: 小说ID=${novelId}, 维度=${dimension}`);

        // 立即获取一次进度
        fetchProgress();

        // 设置定期轮询
        pollTimer = setInterval(fetchProgress, CONFIG.pollInterval);
    }

    // 获取分析进度
    function fetchProgress() {
        if (!novelId) {
            console.info('无法获取进度：未找到小说ID');
            return;
        }

        const url = `/api/analysis/progress?novel_id=${novelId}`;

        fetch(url)
            .then(response => {
                // 特殊处理404错误，将其视为"暂无数据"而非错误
                if (response.status === 404) {
                    return {
                        success: false,
                        error: '暂无进度数据',
                        status: 404,
                        progress: {},
                        is_running: false
                    };
                }

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                if (CONFIG.enableDebug) {
                    console.log('进度数据:', data);
                }

                // 如果是404错误，特殊处理
                if (data.status === 404) {
                    // 不视为错误，只是暂时没有数据
                    if (CONFIG.enableDebug) {
                        console.info(`暂无进度数据 (HTTP 404)`);
                    }
                    return;
                }

                if (data.success) {
                    // 重置重试计数
                    retryCount = 0;

                    // 检查是否有当前维度的进度数据
                    if (data.progress && dimension && data.progress[dimension]) {
                        const progressData = data.progress[dimension];
                        const progress = progressData.progress || 0;
                        isAnalysisRunning = data.is_running;

                        // 更新进度条
                        updateProgressBar(progress);

                        // 更新状态文本
                        updateStatusText(progress, isAnalysisRunning);

                        // 检查是否完成
                        if (progress >= 100 || (progress > 0 && !isAnalysisRunning && progressData.status === 'completed')) {
                            handleAnalysisCompleted();
                        }

                        // 记录最后的进度
                        lastProgress = progress;
                    } else if (CONFIG.enableDebug) {
                        console.info(`未找到维度 ${dimension} 的进度数据`);
                    }
                } else {
                    // 使用info级别日志而非警告
                    console.info('暂无进度数据:', data.error || '未知原因');

                    // 只有在非404错误时才调用错误处理
                    if (data.status !== 404) {
                        handleProgressError();
                    }
                }
            })
            .catch(error => {
                // 使用警告级别而非错误级别
                console.warn('获取进度暂时不可用:', error.message);
                handleProgressError();
            });
    }

    // 更新进度条
    function updateProgressBar(progress) {
        // 查找所有可能的进度条
        const progressSelectors = [
            `#progressBar[data-dimension="${dimension}"]`,
            `#progressBar`,
            `.progress-bar[data-dimension="${dimension}"]`,
            `.progress-bar[role="progressbar"]`,
            `#progress-${dimension}`,
            `#analysisProgressBar`
        ];

        let progressBar = null;

        // 尝试每个选择器
        for (const selector of progressSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                progressBar = element;
                break;
            }
        }

        // 如果找到了进度条，更新它
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
            progressBar.textContent = `${progress}%`;

            // 根据进度更新样式
            if (progress >= 100) {
                progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated', 'bg-warning', 'bg-danger');
                progressBar.classList.add('bg-success');
            } else if (!isAnalysisRunning) {
                progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated', 'bg-success', 'bg-danger');
                progressBar.classList.add('bg-warning');
            } else {
                progressBar.classList.remove('bg-warning', 'bg-success', 'bg-danger');
                progressBar.classList.add('progress-bar-striped', 'progress-bar-animated');
            }

            if (CONFIG.enableDebug) {
                console.log(`更新进度条: ${progress}%, 运行状态: ${isAnalysisRunning}`);
            }
        } else if (CONFIG.enableDebug) {
            console.log(`未找到进度条元素，无法更新进度: ${progress}%`);
        }
    }

    // 更新状态文本
    function updateStatusText(progress, isRunning) {
        // 查找所有可能的状态文本元素
        const statusSelectors = [
            `#analysisStatus`,
            `#statusText`,
            `#analysisStatusText`,
            `.analysis-status[data-dimension="${dimension}"]`,
            `#status-${dimension}`
        ];

        let statusElement = null;

        // 尝试每个选择器
        for (const selector of statusSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                statusElement = element;
                break;
            }
        }

        // 如果找到了状态元素，更新它
        if (statusElement) {
            if (progress >= 100) {
                statusElement.textContent = '已完成';
                statusElement.className = statusElement.className.replace(/badge-\w+/g, '') + ' badge-success';
            } else if (isRunning) {
                statusElement.textContent = `分析中 (${progress}%)`;
                statusElement.className = statusElement.className.replace(/badge-\w+/g, '') + ' badge-primary';
            } else {
                statusElement.textContent = '已暂停';
                statusElement.className = statusElement.className.replace(/badge-\w+/g, '') + ' badge-warning';
            }
        }

        // 更新其他可能的状态显示
        const blocksProgress = document.getElementById('blocksProgress');
        if (blocksProgress) {
            // 尝试从进度数据中获取块进度
            fetch(`/api/analysis/progress?novel_id=${novelId}`)
                .then(res => res.json())
                .then(data => {
                    if (data.success && data.progress && data.progress[dimension] && data.progress[dimension].blocks_progress) {
                        blocksProgress.textContent = data.progress[dimension].blocks_progress;
                    }
                })
                .catch(err => console.error('获取块进度失败:', err));
        }
    }

    // 处理分析完成（安全版）
    function handleAnalysisCompleted(force = false) {
        // 如果已经检测到完成或页面正在刷新，不重复处理
        if (completionDetected && !force) {
            return;
        }

        safeLog('分析已完成，准备刷新页面显示结果');
        completionDetected = true;

        try {
            // 停止所有定时器
            cleanupResources();

            // 显示完成消息
            const messageSelectors = [
                '#progressMessage',
                '#analysisMessage',
                '.analysis-message',
                '.progress-message',
                '.status-message'
            ];

            let messageElement = null;

            // 尝试每个选择器
            for (const selector of messageSelectors) {
                try {
                    const element = document.querySelector(selector);
                    if (element) {
                        messageElement = element;
                        break;
                    }
                } catch (e) {
                    // 忽略选择器错误
                }
            }

            // 如果找到了消息元素，更新它
            if (messageElement) {
                try {
                    messageElement.textContent = '分析完成，正在刷新页面...';
                } catch (e) {
                    // 忽略更新错误
                }
            }

            try {
                // 更新所有进度条为100%（最多处理5个）
                const progressBars = Array.from(document.querySelectorAll('.progress-bar, [role="progressbar"]')).slice(0, 5);
                for (const bar of progressBars) {
                    bar.style.width = '100%';
                    bar.setAttribute('aria-valuenow', '100');
                    bar.textContent = '100%';
                    bar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                    bar.classList.add('bg-success');
                }
            } catch (e) {
                // 忽略进度条更新错误
            }

            try {
                // 更新所有状态文本（最多处理5个）
                const statusElements = Array.from(document.querySelectorAll('.status-text, .analysis-status')).slice(0, 5);
                for (const element of statusElements) {
                    element.textContent = '已完成';
                    if (element.classList.contains('badge') || element.classList.contains('label')) {
                        element.className = element.className.replace(/badge-\w+|label-\w+/g, '') + ' badge-success';
                    }
                }
            } catch (e) {
                // 忽略状态文本更新错误
            }

            try {
                // 隐藏所有加载图标（最多处理5个）
                const spinners = Array.from(document.querySelectorAll('.spinner, .loading, .fa-spinner, .fa-spin')).slice(0, 5);
                for (const spinner of spinners) {
                    spinner.style.display = 'none';
                }
            } catch (e) {
                // 忽略加载图标更新错误
            }

            try {
                // 创建一个简单的通知（使用更少的样式和DOM操作）
                let notification = document.querySelector('#analysis-completion-notice');
                if (!notification) {
                    notification = document.createElement('div');
                    notification.id = 'analysis-completion-notice';
                    notification.style.position = 'fixed';
                    notification.style.top = '10px';
                    notification.style.right = '10px';
                    notification.style.backgroundColor = 'rgba(40, 167, 69, 0.9)';
                    notification.style.color = 'white';
                    notification.style.padding = '10px 15px';
                    notification.style.borderRadius = '5px';
                    notification.style.zIndex = '9999';
                    notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                    notification.textContent = '分析已完成，正在刷新页面...';
                    document.body.appendChild(notification);
                }
            } catch (e) {
                // 忽略通知创建错误
            }

            // 防止重复刷新
            if (pageRefreshing) {
                return;
            }

            // 再次检查当前页面是否需要自动刷新
            if (!shouldEnableAutoRefresh()) {
                safeLog('当前页面不需要自动刷新，跳过页面刷新');

                // 显示一个通知，告知用户可以手动刷新
                try {
                    let manualRefreshNotice = document.querySelector('#manual-refresh-notice');
                    if (!manualRefreshNotice) {
                        manualRefreshNotice = document.createElement('div');
                        manualRefreshNotice.id = 'manual-refresh-notice';
                        manualRefreshNotice.style.position = 'fixed';
                        manualRefreshNotice.style.bottom = '10px';
                        manualRefreshNotice.style.right = '10px';
                        manualRefreshNotice.style.backgroundColor = 'rgba(0, 123, 255, 0.9)';
                        manualRefreshNotice.style.color = 'white';
                        manualRefreshNotice.style.padding = '10px 15px';
                        manualRefreshNotice.style.borderRadius = '5px';
                        manualRefreshNotice.style.zIndex = '9999';
                        manualRefreshNotice.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                        manualRefreshNotice.style.cursor = 'pointer';
                        manualRefreshNotice.textContent = '分析已完成，点击刷新页面';
                        manualRefreshNotice.onclick = function() {
                            window.location.reload();
                        };
                        document.body.appendChild(manualRefreshNotice);

                        // 5秒后自动隐藏
                        setTimeout(() => {
                            if (manualRefreshNotice && manualRefreshNotice.parentNode) {
                                manualRefreshNotice.parentNode.removeChild(manualRefreshNotice);
                            }
                        }, 5000);
                    }
                } catch (e) {
                    // 忽略通知创建错误
                }

                return;
            }

            // 检查是否需要立即刷新或等待
            const now = Date.now();
            const timeSinceLastRefresh = now - lastRefreshTime;

            // 如果距离上次刷新不到5分钟，设置定时器等待
            if (timeSinceLastRefresh < CONFIG.autoRefreshInterval && lastRefreshTime > 0) {
                const waitTime = CONFIG.autoRefreshInterval - timeSinceLastRefresh;
                safeLog(`距离上次刷新不足5分钟，将在${Math.round(waitTime/1000)}秒后刷新`);

                // 显示倒计时通知
                try {
                    let countdownNotice = document.querySelector('#countdown-refresh-notice');
                    if (!countdownNotice) {
                        countdownNotice = document.createElement('div');
                        countdownNotice.id = 'countdown-refresh-notice';
                        countdownNotice.style.position = 'fixed';
                        countdownNotice.style.bottom = '10px';
                        countdownNotice.style.right = '10px';
                        countdownNotice.style.backgroundColor = 'rgba(40, 167, 69, 0.9)';
                        countdownNotice.style.color = 'white';
                        countdownNotice.style.padding = '10px 15px';
                        countdownNotice.style.borderRadius = '5px';
                        countdownNotice.style.zIndex = '9999';
                        countdownNotice.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                        countdownNotice.style.cursor = 'pointer';
                        countdownNotice.innerHTML = `分析已完成，将在<span id="refresh-countdown">${Math.round(waitTime/1000)}</span>秒后刷新<br><small>点击立即刷新</small>`;
                        countdownNotice.onclick = function() {
                            performPageRefresh();
                        };
                        document.body.appendChild(countdownNotice);

                        // 更新倒计时
                        const countdownElement = document.getElementById('refresh-countdown');
                        let remainingSeconds = Math.round(waitTime/1000);
                        const countdownInterval = setInterval(() => {
                            remainingSeconds--;
                            if (countdownElement) {
                                countdownElement.textContent = remainingSeconds;
                            }
                            if (remainingSeconds <= 0) {
                                clearInterval(countdownInterval);
                            }
                        }, 1000);
                    }
                } catch (e) {
                    // 忽略通知创建错误
                }

                // 设置定时器，等待适当的时间后刷新
                if (autoRefreshTimer) {
                    clearTimeout(autoRefreshTimer);
                }

                pageRefreshing = true;
                autoRefreshTimer = setTimeout(() => {
                    performPageRefresh();
                }, waitTime);

                return;
            }

            // 如果是首次刷新或距离上次刷新已超过5分钟，立即刷新
            pageRefreshing = true;
            performPageRefresh();

            // 执行页面刷新
            function performPageRefresh() {
                // 更新最后刷新时间
                lastRefreshTime = Date.now();
                refreshCount++;

                safeLog(`刷新页面以显示分析结果 (第${refreshCount}次刷新)`);

                try {
                    // 使用简单的刷新方式
                    window.location.reload();
                } catch (e) {
                    // 如果刷新失败，尝试其他方式
                    safeLog(`刷新页面失败: ${e.message}，尝试备用方法`);
                    try {
                        window.location.href = window.location.href;
                    } catch (e2) {
                        // 忽略备用方法错误
                    }
                }
            }
        } catch (e) {
            // 捕获任何可能的错误，确保不会崩溃
            safeLog(`处理分析完成时出错: ${e.message}`);

            // 如果出错，尝试直接刷新页面
            try {
                window.location.reload();
            } catch (e2) {
                // 忽略刷新错误
            }
        }
    }

    // 处理进度获取错误
    function handleProgressError() {
        retryCount++;

        if (retryCount <= CONFIG.maxRetries) {
            // 使用info级别日志而非警告或错误
            console.info(`进度获取暂时不可用，${CONFIG.retryDelay / 1000}秒后重试 (${retryCount}/${CONFIG.maxRetries})`);

            // 暂停轮询
            if (pollTimer) {
                clearInterval(pollTimer);
                pollTimer = null;
            }

            // 延迟后重试
            setTimeout(() => {
                console.info(`重试获取进度 (${retryCount}/${CONFIG.maxRetries})`);
                fetchProgress();

                // 恢复轮询
                if (!pollTimer) {
                    pollTimer = setInterval(fetchProgress, CONFIG.pollInterval);
                }
            }, CONFIG.retryDelay);
        } else {
            // 使用警告级别而非错误级别
            console.warn(`进度获取暂时不可用，已达到最大重试次数 (${CONFIG.maxRetries})`);

            // 重置重试计数，以便下次轮询可以再次尝试
            retryCount = 0;

            // 不要停止轮询，继续尝试获取进度
            if (!pollTimer) {
                pollTimer = setInterval(fetchProgress, CONFIG.pollInterval);
            }
        }
    }

    // 监听DOM变化，查找进度条和分析状态（优化版）
    function observeDOM() {
        // 创建一个观察器实例
        const observer = new MutationObserver((mutations) => {
            // 如果已检测到完成，不再处理DOM变化
            if (completionDetected) return;

            // 检查是否有新的进度条元素
            if (!novelId || !dimension) {
                extractNovelAndDimension();

                if (novelId && dimension && !pollTimer) {
                    logDebug(`DOM变化后找到小说ID: ${novelId}, 维度: ${dimension}`);
                    startProgressPolling();
                }
            }

            // 限制处理的变化数量，避免过度消耗CPU
            const significantMutations = mutations.filter(mutation => {
                // 只处理可能包含分析状态的变化
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    return true;
                }

                if (mutation.type === 'characterData' &&
                    mutation.target &&
                    mutation.target.textContent &&
                    (mutation.target.textContent.includes('分析') ||
                     mutation.target.textContent.includes('完成') ||
                     mutation.target.textContent.includes('100%'))) {
                    return true;
                }

                return false;
            }).slice(0, 5); // 最多处理5个变化

            if (significantMutations.length > 0) {
                // 检查是否有加载中的旋转图标变化
                checkForLoadingSpinner();

                // 检查DOM中的分析状态变化
                for (const mutation of significantMutations) {
                    if (mutation.type === 'childList') {
                        // 检查新添加的节点中是否包含完成信息
                        const nodes = mutation.addedNodes;
                        for (let i = 0; i < Math.min(nodes.length, 3); i++) { // 最多处理3个节点
                            const node = nodes[i];
                            if (node.nodeType === Node.TEXT_NODE) {
                                if (node.textContent &&
                                    (node.textContent.includes('分析') ||
                                     node.textContent.includes('完成') ||
                                     node.textContent.includes('100%'))) {
                                    checkTextForCompletion(node.textContent);
                                }
                            } else if (node.nodeType === Node.ELEMENT_NODE) {
                                // 只检查可能包含状态信息的元素
                                if (node.tagName === 'DIV' ||
                                    node.tagName === 'SPAN' ||
                                    node.tagName === 'P' ||
                                    node.classList.contains('progress-bar') ||
                                    node.getAttribute('role') === 'progressbar') {
                                    checkElementForCompletion(node);
                                }
                            }
                        }
                    }
                }
            }
        });

        // 配置观察选项 - 减少观察范围，只观察主要内容区域
        const config = { childList: true, subtree: true, characterData: false };

        // 尝试找到主要内容区域
        const mainContent = document.querySelector('.container, main, #content, #main');

        // 开始观察
        if (mainContent) {
            observer.observe(mainContent, config);
        } else {
            observer.observe(document.body, config);
        }
    }

    // 拦截控制台日志以检测分析完成信息（安全版）
    function interceptConsoleLogs() {
        // 标记，防止递归调用
        let isProcessingLog = false;

        console.log = function() {
            // 防止递归调用
            if (isProcessingLog) {
                return originalConsoleLog.apply(console, arguments);
            }

            isProcessingLog = true;

            try {
                // 调用原始的console.log（仅在调试模式下）
                if (CONFIG.enableDebug) {
                    originalConsoleLog.apply(console, arguments);
                }

                // 将日志添加到缓冲区（仅保留与分析相关的日志）
                let logMessage;
                try {
                    logMessage = Array.from(arguments).join(' ');
                } catch (e) {
                    // 如果转换失败，使用简单的字符串连接
                    logMessage = String(arguments[0] || '');
                }

                // 仅保留可能包含分析完成信息的日志
                if (logMessage.includes('分析') ||
                    logMessage.includes('完成') ||
                    logMessage.includes('progress') ||
                    logMessage.includes('100%') ||
                    logMessage.includes('analysis')) {

                    // 安全地添加到缓冲区
                    try {
                        consoleBuffer.push(logMessage);

                        // 保持缓冲区大小合理
                        if (consoleBuffer.length > CONFIG.maxConsoleBufferSize) {
                            consoleBuffer.shift();
                        }
                    } catch (e) {
                        // 忽略缓冲区错误
                    }

                    // 检查是否包含完成关键词，但不使用checkTextForCompletion避免递归
                    try {
                        for (const keyword of CONFIG.completionKeywords) {
                            if (logMessage.includes(keyword)) {
                                safeLog(`检测到完成关键词: "${keyword}"`);
                                handleAnalysisCompleted();
                                break;
                            }
                        }
                    } catch (e) {
                        // 忽略检查错误
                    }
                }
            } finally {
                // 确保标记被重置
                isProcessingLog = false;
            }
        };
    }

    // 启动定期DOM检查
    function startDOMChecks() {
        if (domCheckTimer) {
            clearInterval(domCheckTimer);
        }

        domCheckTimer = setInterval(() => {
            // 检查页面中是否有加载中的旋转图标
            checkForLoadingSpinner();

            // 检查页面中是否有分析完成的文本
            checkForCompletionText();

            // 检查页面中的分析控制台输出
            checkConsoleOutput();

            // 检查页面中的进度条状态
            checkProgressBarStatus();
        }, CONFIG.domCheckInterval);
    }

    // 启动控制台日志检查
    function startConsoleChecks() {
        if (consoleCheckTimer) {
            clearInterval(consoleCheckTimer);
        }

        consoleCheckTimer = setInterval(() => {
            // 检查控制台缓冲区中是否有完成信息
            for (const log of consoleBuffer) {
                checkTextForCompletion(log);
            }
        }, CONFIG.consoleCheckInterval);
    }

    // 设置强制刷新定时器
    function setupForceRefresh() {
        if (forceRefreshTimer) {
            clearTimeout(forceRefreshTimer);
        }

        // 记录分析开始时间
        analysisStartTime = new Date();

        // 设置强制刷新定时器
        forceRefreshTimer = setTimeout(() => {
            console.log('分析时间过长，执行强制刷新');

            // 检查页面中是否有分析控制台输出
            const consoleOutput = document.querySelector('.analysis-console, .console-output, pre, code');
            if (consoleOutput && consoleOutput.textContent.includes('[')) {
                console.log('检测到分析控制台输出，可能分析已完成但未刷新');
                handleAnalysisCompleted(true);
            } else {
                // 如果没有明确的完成迹象，但时间已经很长，也刷新页面
                const elapsedTime = new Date() - analysisStartTime;
                if (elapsedTime > CONFIG.forceRefreshTimeout) {
                    console.log(`分析已运行 ${elapsedTime/1000} 秒，执行强制刷新`);
                    handleAnalysisCompleted(true);
                }
            }
        }, CONFIG.forceRefreshTimeout);
    }

    // 检查文本中是否包含完成信息（安全版）
    function checkTextForCompletion(text) {
        if (!text) return false;

        try {
            // 检查是否包含完成关键词
            for (const keyword of CONFIG.completionKeywords) {
                if (text.includes(keyword)) {
                    // 使用安全日志函数
                    safeLog(`检测到完成关键词: "${keyword}"`);
                    handleAnalysisCompleted();
                    return true;
                }
            }

            // 检查是否包含100%进度信息
            if (text.match(/进度[:：]?\s*100%/) || text.match(/progress[:：]?\s*100%/i)) {
                // 使用安全日志函数
                safeLog(`检测到100%进度信息`);
                handleAnalysisCompleted();
                return true;
            }
        } catch (e) {
            // 捕获任何可能的错误，确保不会崩溃
            safeLog(`检查文本时出错: ${e.message}`);
        }

        return false;
    }

    // 检查元素中是否包含完成信息（安全版）
    function checkElementForCompletion(element) {
        if (!element) return false;

        try {
            // 检查元素文本
            if (element.textContent) {
                if (checkTextForCompletion(element.textContent)) {
                    return true;
                }
            }

            // 检查特定类型的元素
            if (element.tagName === 'DIV' || element.tagName === 'SPAN' || element.tagName === 'P') {
                // 检查是否是进度条
                if (element.classList.contains('progress-bar') || element.getAttribute('role') === 'progressbar') {
                    const width = element.style.width;
                    if (width === '100%') {
                        safeLog('检测到进度条宽度为100%');
                        handleAnalysisCompleted();
                        return true;
                    }
                }

                // 检查是否包含"分析完成"等文本
                if (element.textContent.includes('分析完成') || element.textContent.includes('已完成')) {
                    safeLog(`检测到完成文本`);
                    handleAnalysisCompleted();
                    return true;
                }
            }
        } catch (e) {
            // 捕获任何可能的错误，确保不会崩溃
            safeLog(`检查元素时出错: ${e.message}`);
        }

        return false;
    }

    // 检查页面中是否有加载中的旋转图标（安全版）
    function checkForLoadingSpinner() {
        try {
            const spinners = document.querySelectorAll('.spinner, .loading, .fa-spinner, .fa-spin');
            const spinnerFound = spinners.length > 0;

            // 如果之前有旋转图标，现在没有了，可能表示分析已完成
            if (loadingSpinnerFound && !spinnerFound) {
                safeLog('检测到加载图标消失，可能分析已完成');
                setTimeout(() => {
                    try {
                        // 再次检查，确认不是临时消失
                        const spinners = document.querySelectorAll('.spinner, .loading, .fa-spinner, .fa-spin');
                        if (spinners.length === 0) {
                            handleAnalysisCompleted();
                        }
                    } catch (e) {
                        // 忽略错误
                    }
                }, 1000);
            }

            loadingSpinnerFound = spinnerFound;
        } catch (e) {
            // 捕获任何可能的错误，确保不会崩溃
            safeLog(`检查加载图标时出错: ${e.message}`);
        }
    }

    // 检查页面中是否有分析完成的文本
    function checkForCompletionText() {
        // 查找所有文本节点
        const textNodes = [];
        const walk = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, null, false);
        let node;
        while (node = walk.nextNode()) {
            textNodes.push(node);
        }

        // 检查文本节点中是否包含完成信息
        for (const node of textNodes) {
            if (checkTextForCompletion(node.textContent)) {
                return true;
            }
        }

        return false;
    }

    // 检查页面中的分析控制台输出
    function checkConsoleOutput() {
        const consoleElements = document.querySelectorAll('.analysis-console, .console-output, pre, code');

        for (const element of consoleElements) {
            if (checkElementForCompletion(element)) {
                return true;
            }
        }

        return false;
    }

    // 检查页面中的进度条状态（安全版）
    function checkProgressBarStatus() {
        try {
            const progressBars = document.querySelectorAll('.progress-bar, [role="progressbar"]');

            for (const bar of progressBars) {
                const width = bar.style.width;
                const ariaValue = bar.getAttribute('aria-valuenow');

                if (width === '100%' || ariaValue === '100') {
                    safeLog('检测到进度条为100%');
                    handleAnalysisCompleted();
                    return true;
                }
            }
        } catch (e) {
            // 捕获任何可能的错误，确保不会崩溃
            safeLog(`检查进度条状态时出错: ${e.message}`);
        }

        return false;
    }

    // 初始化 - 使用延迟初始化，确保在其他脚本之后运行
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟初始化，确保在其他脚本之后运行
        setTimeout(initialize, 500);
    });

    // 如果文档已经加载完成，延迟初始化
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(initialize, 500);
    }

    // 添加页面可见性变化事件处理
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            // 页面变为可见时，再次清除所有定时器
            console.log('页面变为可见，再次禁用所有自动刷新功能');
            setTimeout(initialize, 100);
        }
    });

    // 导出一个空的API，防止其他脚本调用刷新功能
    window.AnalysisProgressFix = {
        refresh: function() { console.log('自动刷新功能已禁用'); },
        checkProgress: function() { console.log('进度检查功能已禁用'); },
        forceRefresh: function() { console.log('强制刷新功能已禁用'); }
    };
})();
