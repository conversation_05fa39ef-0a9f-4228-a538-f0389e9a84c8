import sys
import os
from pathlib import Path

print("检查导入路径和文件...")

# 获取src目录的绝对路径
src_dir = os.path.abspath("src")
print(f"src目录路径: {src_dir}")

# 检查config目录
config_dir = os.path.join(src_dir, "config")
print(f"config目录路径: {config_dir}")
print(f"config目录是否存在: {os.path.exists(config_dir)}")

# 列出config目录中的文件
if os.path.exists(config_dir):
    print("\nconfig目录中的文件:")
    for file in os.listdir(config_dir):
        print(f"- {file}")

# 检查具体文件
files_to_check = [
    "src/config/result_cache_optimizer.py",
    "src/config/cross_chapter_reasoning_optimizer.py",
    "src/config/paragraph_reasoning_reuse.py",
    "src/config/serialization_bottleneck_optimizer.py"
]

print("\n检查具体文件:")
for file_path in files_to_check:
    print(f"{file_path}: {'存在' if os.path.exists(file_path) else '不存在'}")

print("\n系统路径:")
for path in sys.path:
    print(f"- {path}")

print("\n结束检查") 