{% extends "v3.5/base.html" %}

{% block title %}原文对比 - 九猫小说分析写作系统v3.5{% endblock %}

{% block extra_css %}
<style>
    /* 文本对比页面样式 */
    .comparison-container {
        height: calc(100vh - 250px);
        min-height: 600px;
        display: flex;
        flex-direction: column;
    }
    
    .text-comparison-row {
        display: flex;
        flex: 1;
        min-height: 300px;
    }
    
    .text-panel {
        flex: 1;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        margin: 0.5rem;
        background-color: var(--card-bg);
        overflow-y: auto;
    }
    
    .text-panel-header {
        font-weight: 600;
        margin-bottom: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .highlight {
        background-color: rgba(214, 158, 46, 0.2);
        border-radius: 3px;
        padding: 2px 0;
    }
    
    .similarity-high {
        background-color: rgba(124, 176, 124, 0.3);
    }
    
    .similarity-medium {
        background-color: rgba(217, 183, 120, 0.3);
    }
    
    .similarity-low {
        background-color: rgba(197, 120, 120, 0.2);
    }
    
    .similarity-legend {
        display: flex;
        align-items: center;
        margin-right: 1rem;
    }
    
    .legend-item {
        width: 20px;
        height: 20px;
        border-radius: 3px;
        margin-right: 0.5rem;
    }
    
    .controls-panel {
        padding: 1rem;
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        margin-bottom: 1rem;
    }
    
    .similarity-score {
        font-size: 1.5rem;
        font-weight: 600;
    }
    
    .similarity-meter {
        height: 8px;
        border-radius: 4px;
        margin-top: 0.5rem;
        background-color: var(--border-color);
        overflow: hidden;
    }
    
    .similarity-fill {
        height: 100%;
        background: linear-gradient(to right, var(--danger-color), var(--warning-color), var(--success-color));
        transition: width 0.5s ease;
    }
    
    @media (max-width: 768px) {
        .text-comparison-row {
            flex-direction: column;
        }
        
        .text-panel {
            margin: 0.5rem 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="mb-3">原文对比</h1>
        <p class="lead">比较自动写作生成的内容与原文，查找相似或重叠的部分。</p>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-cog me-2"></i>对比设置
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label for="templateSelect" class="form-label">选择参考蓝本</label>
                    <select class="form-select" id="templateSelect">
                        <option value="">-- 请选择参考蓝本 --</option>
                        {% for template in templates %}
                        <option value="{{ template.id }}">{{ template.title }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label for="similarityThreshold" class="form-label">相似度阈值</label>
                    <div class="input-group">
                        <input type="range" class="form-range" id="similarityThreshold" min="0.5" max="0.9" step="0.05" value="0.7">
                        <span class="input-group-text" id="thresholdValue">0.7</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label for="comparisonMode" class="form-label">对比模式</label>
                    <select class="form-select" id="comparisonMode">
                        <option value="paragraph">段落对比</option>
                        <option value="sentence">句子对比</option>
                        <option value="phrase">短语对比</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <label for="writingPrompt" class="form-label">写作提示</label>
                    <textarea class="form-control" id="writingPrompt" rows="3" placeholder="请输入写作提示，例如：写一个类似于参考蓝本风格的章节，主题是..."></textarea>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <div class="d-grid gap-2 w-100">
                    <button class="btn btn-primary" id="generateAndCompareBtn">
                        <i class="fas fa-sync-alt me-2"></i>生成并对比
                    </button>
                    <button class="btn btn-outline-secondary" id="clearComparisonBtn">
                        <i class="fas fa-eraser me-2"></i>清空对比
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="controls-panel mb-4" id="resultsPanel" style="display: none;">
    <div class="row">
        <div class="col-md-6">
            <h5>对比结果</h5>
            <div class="similarity-score" id="similarityScore">相似度: <span>0%</span></div>
            <div class="similarity-meter">
                <div class="similarity-fill" id="similarityFill" style="width: 0%;"></div>
            </div>
        </div>
        <div class="col-md-6">
            <h5>图例</h5>
            <div class="d-flex flex-wrap">
                <div class="similarity-legend">
                    <div class="legend-item similarity-high"></div>
                    <span>高相似度 (>80%)</span>
                </div>
                <div class="similarity-legend">
                    <div class="legend-item similarity-medium"></div>
                    <span>中相似度 (60-80%)</span>
                </div>
                <div class="similarity-legend">
                    <div class="legend-item similarity-low"></div>
                    <span>低相似度 (<60%)</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="comparison-container">
    <div class="text-comparison-row">
        <div class="text-panel" id="originalTextPanel">
            <div class="text-panel-header">原文</div>
            <div class="text-panel-content" id="originalText">
                <div class="text-center py-5">
                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                    <p>请选择参考蓝本并点击"生成并对比"按钮</p>
                </div>
            </div>
        </div>
        <div class="text-panel" id="generatedTextPanel">
            <div class="text-panel-header">生成内容</div>
            <div class="text-panel-content" id="generatedText">
                <div class="text-center py-5">
                    <i class="fas fa-pen-fancy fa-3x text-muted mb-3"></i>
                    <p>生成的内容将显示在这里</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 更新相似度阈值显示
        $('#similarityThreshold').on('input', function() {
            $('#thresholdValue').text($(this).val());
        });
        
        // 生成并对比按钮点击事件
        $('#generateAndCompareBtn').click(function() {
            const templateId = $('#templateSelect').val();
            const prompt = $('#writingPrompt').val();
            const threshold = parseFloat($('#similarityThreshold').val());
            const mode = $('#comparisonMode').val();
            
            if (!templateId) {
                alert('请选择参考蓝本');
                return;
            }
            
            // 显示加载状态
            $('#originalText').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载原文中...</p></div>');
            $('#generatedText').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">生成内容中...</p></div>');
            
            // 调用自动写作API
            $.ajax({
                url: '/v3.5/api/auto_write',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    template_id: templateId,
                    prompt: prompt
                }),
                success: function(response) {
                    if (response.success && response.content) {
                        // 显示原文和生成内容
                        const originalText = response.content.original_text || '未找到原文';
                        const generatedContent = response.content.content || '生成内容失败';
                        
                        $('#originalText').text(originalText);
                        $('#generatedText').html(marked.parse(generatedContent));
                        
                        // 调用文本对比API
                        compareTexts(originalText, generatedContent, threshold, mode);
                    } else {
                        $('#originalText').html('<div class="alert alert-danger">加载原文失败</div>');
                        $('#generatedText').html('<div class="alert alert-danger">生成内容失败: ' + (response.error || '未知错误') + '</div>');
                    }
                },
                error: function(xhr) {
                    $('#originalText').html('<div class="alert alert-danger">加载原文失败</div>');
                    $('#generatedText').html('<div class="alert alert-danger">生成内容失败: ' + xhr.status + ' ' + xhr.statusText + '</div>');
                }
            });
        });
        
        // 清空对比按钮点击事件
        $('#clearComparisonBtn').click(function() {
            $('#originalText').html('<div class="text-center py-5"><i class="fas fa-book fa-3x text-muted mb-3"></i><p>请选择参考蓝本并点击"生成并对比"按钮</p></div>');
            $('#generatedText').html('<div class="text-center py-5"><i class="fas fa-pen-fancy fa-3x text-muted mb-3"></i><p>生成的内容将显示在这里</p></div>');
            $('#resultsPanel').hide();
        });
        
        // 文本对比函数
        function compareTexts(text1, text2, threshold, mode) {
            $.ajax({
                url: '/v3.5/api/compare_texts',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    text1: text1,
                    text2: text2,
                    similarity_threshold: threshold
                }),
                success: function(response) {
                    if (response.success && response.comparison_result) {
                        const result = response.comparison_result;
                        
                        // 显示相似度分数
                        const similarityPercent = Math.round(result.similarity_score * 100);
                        $('#similarityScore span').text(similarityPercent + '%');
                        $('#similarityFill').css('width', similarityPercent + '%');
                        
                        // 显示结果面板
                        $('#resultsPanel').show();
                        
                        // 高亮相似段落
                        highlightSimilarSegments(text1, text2, result.similar_segments);
                    } else {
                        alert('文本对比失败: ' + (response.error || '未知错误'));
                    }
                },
                error: function(xhr) {
                    alert('文本对比失败: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        }
        
        // 高亮相似段落
        function highlightSimilarSegments(text1, text2, segments) {
            let originalHtml = text1;
            let generatedHtml = text2;
            
            // 按位置从后向前处理，避免位置偏移
            segments.sort((a, b) => b.text1_start - a.text1_start);
            
            // 处理原文
            for (const segment of segments) {
                const similarityClass = getSimilarityClass(segment.similarity);
                const highlightedText = `<span class="highlight ${similarityClass}">${segment.text1_segment}</span>`;
                originalHtml = originalHtml.substring(0, segment.text1_start) + 
                              highlightedText + 
                              originalHtml.substring(segment.text1_end);
            }
            
            // 按位置从后向前处理，避免位置偏移
            segments.sort((a, b) => b.text2_start - a.text2_start);
            
            // 处理生成内容
            for (const segment of segments) {
                const similarityClass = getSimilarityClass(segment.similarity);
                const highlightedText = `<span class="highlight ${similarityClass}">${segment.text2_segment}</span>`;
                generatedHtml = generatedHtml.substring(0, segment.text2_start) + 
                               highlightedText + 
                               generatedHtml.substring(segment.text2_end);
            }
            
            // 更新显示
            $('#originalText').html(originalHtml.replace(/\n/g, '<br>'));
            $('#generatedText').html(marked.parse(generatedHtml));
        }
        
        // 获取相似度类别
        function getSimilarityClass(similarity) {
            if (similarity >= 0.8) return 'similarity-high';
            if (similarity >= 0.6) return 'similarity-medium';
            return 'similarity-low';
        }
    });
</script>
{% endblock %}
