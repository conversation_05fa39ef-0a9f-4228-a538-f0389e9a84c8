/**
 * 九猫系统 - 知识库紧凑显示脚本
 * 优化知识库内容展示，实现智能折叠/展开功能
 */

(function() {
    'use strict';

    // 在DOM加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化知识库内容处理
        initKnowledgeBaseCompact();

        // 监听标签页切换事件
        const tabLinks = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');
        tabLinks.forEach(function(tabLink) {
            tabLink.addEventListener('click', function() {
                // 延迟执行，确保内容已加载
                setTimeout(function() {
                    initKnowledgeBaseCompact();
                }, 300);
            });
        });

        // 监听维度项点击事件
        const dimensionItems = document.querySelectorAll('.dimension-item');
        dimensionItems.forEach(function(item) {
            item.addEventListener('click', function() {
                // 延迟执行，确保内容已加载
                setTimeout(function() {
                    processTemplateContent();
                }, 500);
            });
        });

        // 监听章节项点击事件
        const chapterItems = document.querySelectorAll('.chapter-item');
        chapterItems.forEach(function(item) {
            item.addEventListener('click', function() {
                // 延迟执行，确保内容已加载
                setTimeout(function() {
                    processTemplateContent();
                }, 500);
            });
        });
    });

    /**
     * 初始化知识库紧凑显示
     */
    function initKnowledgeBaseCompact() {
        // 处理模板内容
        processTemplateContent();

        // 添加全局展开/折叠按钮
        addGlobalToggleButtons();
    }

    /**
     * 处理模板内容，添加折叠/展开功能
     */
    function processTemplateContent() {
        // 获取模板内容容器
        const bookTemplateContent = document.getElementById('bookTemplateContent');
        const chapterTemplateContent = document.getElementById('chapterTemplateContent');

        // 处理整本书模板内容
        if (bookTemplateContent) {
            processMarkdownContent(bookTemplateContent);
        }

        // 处理章节模板内容
        if (chapterTemplateContent) {
            processMarkdownContent(chapterTemplateContent);
        }
    }

    /**
     * 处理Markdown内容，添加折叠/展开功能
     * @param {HTMLElement} container - 内容容器元素
     */
    function processMarkdownContent(container) {
        // 查找所有二级标题
        const headings = container.querySelectorAll('h2');

        headings.forEach(function(heading) {
            // 跳过已处理的标题
            if (heading.classList.contains('processed')) {
                return;
            }

            // 创建折叠区域
            createCollapsibleSection(heading);

            // 标记为已处理
            heading.classList.add('processed');
        });

        // 查找所有三级标题
        const subHeadings = container.querySelectorAll('h3');

        subHeadings.forEach(function(heading) {
            // 跳过已处理的标题
            if (heading.classList.contains('processed')) {
                return;
            }

            // 创建折叠区域
            createCollapsibleSection(heading);

            // 标记为已处理
            heading.classList.add('processed');
        });

        // 特殊处理分析结果和推理过程部分
        processAnalysisAndReasoning(container);
    }

    /**
     * 创建可折叠区域
     * @param {HTMLElement} heading - 标题元素
     */
    function createCollapsibleSection(heading) {
        // 获取标题文本
        const headingText = heading.textContent.trim();

        // 跳过不需要折叠的标题
        if (headingText === '基本信息' || headingText === '应用指南') {
            return;
        }

        // 创建折叠区域容器
        const section = document.createElement('div');
        section.className = 'collapsible-section';

        // 创建折叠区域标题
        const header = document.createElement('div');
        header.className = 'collapsible-header';
        header.innerHTML = `
            <${heading.tagName.toLowerCase()}>${headingText}</${heading.tagName.toLowerCase()}>
            <i class="fas fa-chevron-down"></i>
        `;

        // 创建折叠区域内容
        const content = document.createElement('div');
        content.className = 'collapsible-content';

        // 收集标题后面的所有元素，直到下一个相同级别或更高级别的标题
        let nextElement = heading.nextElementSibling;
        const elements = [];

        while (nextElement &&
               !(nextElement.tagName === heading.tagName ||
                 (heading.tagName === 'H3' && nextElement.tagName === 'H2') ||
                 (heading.tagName === 'H4' && (nextElement.tagName === 'H3' || nextElement.tagName === 'H2')))) {
            elements.push(nextElement);
            nextElement = nextElement.nextElementSibling;
        }

        // 将元素移动到折叠区域内容中
        elements.forEach(function(element) {
            content.appendChild(element.cloneNode(true));
        });

        // 添加点击事件
        header.addEventListener('click', function() {
            content.classList.toggle('expanded');
            header.querySelector('i').classList.toggle('fa-chevron-down');
            header.querySelector('i').classList.toggle('fa-chevron-up');
        });

        // 组装折叠区域
        section.appendChild(header);
        section.appendChild(content);

        // 替换原标题
        heading.parentNode.replaceChild(section, heading);

        // 移除已移动的元素
        elements.forEach(function(element) {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });
    }

    /**
     * 特殊处理分析结果、推理过程和整本书思考过程部分
     * @param {HTMLElement} container - 内容容器元素
     */
    function processAnalysisAndReasoning(container) {
        // 查找分析结果部分
        const analysisHeading = Array.from(container.querySelectorAll('h2')).find(h =>
            h.textContent.trim() === '分析结果');

        if (analysisHeading) {
            // 创建分析结果容器
            const analysisContainer = document.createElement('div');
            analysisContainer.className = 'analysis-result-container';

            // 收集分析结果内容
            let nextElement = analysisHeading.nextElementSibling;
            const elements = [];

            while (nextElement && nextElement.tagName !== 'H2') {
                elements.push(nextElement);
                nextElement = nextElement.nextElementSibling;
            }

            // 将元素移动到分析结果容器中
            elements.forEach(function(element) {
                analysisContainer.appendChild(element.cloneNode(true));
            });

            // 替换原标题和内容
            analysisHeading.parentNode.replaceChild(analysisContainer, analysisHeading);

            // 移除已移动的元素
            elements.forEach(function(element) {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            });
        }

        // 查找推理过程部分
        const reasoningHeading = Array.from(container.querySelectorAll('h2')).find(h =>
            h.textContent.trim() === '推理过程');

        if (reasoningHeading) {
            // 创建推理过程容器
            const reasoningContainer = document.createElement('div');
            reasoningContainer.className = 'reasoning-process-container collapsible-section';

            // 创建推理过程标题
            const reasoningHeader = document.createElement('div');
            reasoningHeader.className = 'collapsible-header';
            reasoningHeader.innerHTML = `
                <h2>推理过程</h2>
                <i class="fas fa-chevron-down"></i>
            `;

            // 创建推理过程内容
            const reasoningContent = document.createElement('div');
            reasoningContent.className = 'collapsible-content';

            // 收集推理过程内容
            let nextElement = reasoningHeading.nextElementSibling;
            const elements = [];

            while (nextElement && nextElement.tagName !== 'H2') {
                elements.push(nextElement);
                nextElement = nextElement.nextElementSibling;
            }

            // 将元素移动到推理过程内容中
            elements.forEach(function(element) {
                reasoningContent.appendChild(element.cloneNode(true));
            });

            // 添加点击事件
            reasoningHeader.addEventListener('click', function() {
                reasoningContent.classList.toggle('expanded');
                reasoningHeader.querySelector('i').classList.toggle('fa-chevron-down');
                reasoningHeader.querySelector('i').classList.toggle('fa-chevron-up');
            });

            // 组装推理过程容器
            reasoningContainer.appendChild(reasoningHeader);
            reasoningContainer.appendChild(reasoningContent);

            // 替换原标题和内容
            reasoningHeading.parentNode.replaceChild(reasoningContainer, reasoningHeading);

            // 移除已移动的元素
            elements.forEach(function(element) {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            });
        }

        // 处理控制台页面中的整本书思考过程
        processThinkingProcess();
    }

    /**
     * 处理整本书思考过程
     */
    function processThinkingProcess() {
        // 查找整本书思考过程卡片
        const thinkingHeaders = Array.from(document.querySelectorAll('.card-header'));
        const thinkingCards = thinkingHeaders.filter(header =>
            header.textContent.includes('整本书思考过程'));

        thinkingCards.forEach(function(header) {
            // 获取卡片
            const card = header.closest('.card');
            if (!card) return;

            // 获取卡片内容
            const cardBody = card.querySelector('.card-body');
            if (!cardBody) return;

            // 如果已经有折叠按钮，说明已经处理过
            if (cardBody.querySelector('.thinking-process-toggle')) return;

            // 创建折叠内容容器
            const collapsibleContent = document.createElement('div');
            collapsibleContent.className = 'collapsible-content';

            // 移动原内容到折叠容器
            while (cardBody.firstChild) {
                collapsibleContent.appendChild(cardBody.firstChild);
            }

            // 创建折叠按钮
            const toggleButton = document.createElement('button');
            toggleButton.className = 'btn btn-sm btn-outline-secondary w-100 mb-2';
            toggleButton.innerHTML = '<i class="fas fa-chevron-down me-1"></i>展开思考过程';

            // 添加点击事件
            toggleButton.addEventListener('click', function() {
                collapsibleContent.classList.toggle('expanded');
                if (collapsibleContent.classList.contains('expanded')) {
                    toggleButton.innerHTML = '<i class="fas fa-chevron-up me-1"></i>收起思考过程';
                } else {
                    toggleButton.innerHTML = '<i class="fas fa-chevron-down me-1"></i>展开思考过程';
                }
            });

            // 添加按钮和折叠内容到卡片
            cardBody.appendChild(toggleButton);
            cardBody.appendChild(collapsibleContent);
        });

        // 处理知识库页面中的整本书思考过程
        processKnowledgeBaseThinkingProcess();
    }

    /**
     * 处理知识库页面中的整本书思考过程
     */
    function processKnowledgeBaseThinkingProcess() {
        // 查找所有思考过程容器
        const thinkingProcessContainers = document.querySelectorAll('.thinking-process');

        thinkingProcessContainers.forEach(function(container) {
            // 获取父卡片
            const card = container.closest('.card');
            if (!card) return;

            // 获取卡片内容
            const cardBody = container.closest('.card-body');
            if (!cardBody) return;

            // 如果已经有折叠按钮或者容器已经在折叠容器内，说明已经处理过
            if (cardBody.querySelector('.thinking-process-toggle') ||
                container.closest('.collapsible-content') ||
                container.closest('.thinking-process-container')) {
                return;
            }

            // 创建折叠内容容器
            const collapsibleContent = document.createElement('div');
            collapsibleContent.className = 'collapsible-content';

            // 克隆原内容到折叠容器
            collapsibleContent.appendChild(container.cloneNode(true));

            // 创建折叠按钮
            const toggleButton = document.createElement('button');
            toggleButton.className = 'btn btn-sm btn-outline-secondary w-100 mb-2 thinking-process-toggle';
            toggleButton.innerHTML = '<i class="fas fa-chevron-down me-1"></i>展开思考过程';

            // 添加点击事件
            toggleButton.addEventListener('click', function() {
                collapsibleContent.classList.toggle('expanded');
                if (collapsibleContent.classList.contains('expanded')) {
                    toggleButton.innerHTML = '<i class="fas fa-chevron-up me-1"></i>收起思考过程';
                } else {
                    toggleButton.innerHTML = '<i class="fas fa-chevron-down me-1"></i>展开思考过程';
                }
            });

            // 清空原内容
            cardBody.innerHTML = '';

            // 添加按钮和折叠内容到卡片
            cardBody.appendChild(toggleButton);
            cardBody.appendChild(collapsibleContent);
        });
    }

    /**
     * 添加全局展开/折叠按钮
     */
    function addGlobalToggleButtons() {
        // 获取模板内容容器
        const bookTemplateContent = document.getElementById('bookTemplateContent');
        const chapterTemplateContent = document.getElementById('chapterTemplateContent');

        // 处理整本书模板内容
        if (bookTemplateContent && !bookTemplateContent.querySelector('.global-toggle-buttons')) {
            addToggleButtons(bookTemplateContent);
        }

        // 处理章节模板内容
        if (chapterTemplateContent && !chapterTemplateContent.querySelector('.global-toggle-buttons')) {
            addToggleButtons(chapterTemplateContent);
        }
    }

    /**
     * 为容器添加展开/折叠按钮
     * @param {HTMLElement} container - 内容容器元素
     */
    function addToggleButtons(container) {
        // 创建按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'global-toggle-buttons d-flex justify-content-end mb-3';

        // 创建展开全部按钮
        const expandAllButton = document.createElement('button');
        expandAllButton.className = 'btn btn-sm btn-outline-secondary me-2';
        expandAllButton.innerHTML = '<i class="fas fa-expand-alt me-1"></i>展开全部';
        expandAllButton.addEventListener('click', function() {
            const contents = container.querySelectorAll('.collapsible-content');
            contents.forEach(function(content) {
                content.classList.add('expanded');
            });

            const icons = container.querySelectorAll('.collapsible-header i');
            icons.forEach(function(icon) {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            });
        });

        // 创建折叠全部按钮
        const collapseAllButton = document.createElement('button');
        collapseAllButton.className = 'btn btn-sm btn-outline-secondary';
        collapseAllButton.innerHTML = '<i class="fas fa-compress-alt me-1"></i>折叠全部';
        collapseAllButton.addEventListener('click', function() {
            const contents = container.querySelectorAll('.collapsible-content');
            contents.forEach(function(content) {
                content.classList.remove('expanded');
            });

            const icons = container.querySelectorAll('.collapsible-header i');
            icons.forEach(function(icon) {
                icon.classList.add('fa-chevron-down');
                icon.classList.remove('fa-chevron-up');
            });
        });

        // 添加按钮到容器
        buttonContainer.appendChild(expandAllButton);
        buttonContainer.appendChild(collapseAllButton);

        // 将按钮容器添加到内容容器的顶部
        container.insertBefore(buttonContainer, container.firstChild);
    }
})();
