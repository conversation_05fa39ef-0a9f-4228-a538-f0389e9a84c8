{"name": "libnpmfund", "version": "3.0.2", "main": "lib/index.js", "files": ["bin/", "lib/"], "description": "Programmatic API for npm fund", "repository": {"type": "git", "url": "https://github.com/npm/cli.git", "directory": "workspaces/libnpmfund"}, "keywords": ["npm", "npmcli", "libnpm", "cli", "git", "fund", "gitfund"], "author": "GitHub Inc.", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://ruyadorno.com", "twitter": "ruyadorno"}], "license": "ISC", "scripts": {"eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "tap": "^16.0.1"}, "dependencies": {"@npmcli/arborist": "^5.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}}