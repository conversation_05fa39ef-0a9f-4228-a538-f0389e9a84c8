<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九猫系统 - 静态资源修复测试</title>
    <!-- 先加载我们的修复脚本 -->
    <script src="fix-static-resources.js"></script>
    <!-- 故意使用错误的路径来测试修复功能 -->
    <link rel="stylesheet" href="/static/css/bootstrap-icons.css">
    <script src="/static/js/lib/jquery.min.js"></script>
    <script src="/static/js/lib/bootstrap.bundle.min.js"></script>
    <script src="/static/js/lib/marked.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h1 class="card-title">静态资源修复测试</h1>
                        <p class="card-text">这个页面用于测试静态资源修复功能。</p>
                        
                        <h2>测试Bootstrap图标</h2>
                        <div class="mt-3">
                            <i class="bi bi-check-circle-fill text-success"></i> 成功图标
                            <i class="bi bi-exclamation-triangle-fill text-warning"></i> 警告图标
                            <i class="bi bi-x-circle-fill text-danger"></i> 错误图标
                        </div>
                        
                        <h2>测试jQuery</h2>
                        <div class="mt-3">
                            <button id="test-jquery" class="btn btn-primary">测试jQuery</button>
                            <div id="jquery-result" class="mt-2"></div>
                        </div>
                        
                        <h2>测试Bootstrap</h2>
                        <div class="mt-3">
                            <button id="test-bootstrap" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#testModal">打开模态框</button>
                        </div>
                        
                        <h2>测试Marked</h2>
                        <div class="mt-3">
                            <div class="card">
                                <div class="card-body">
                                    <div id="markdown-output"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap Modal -->
    <div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="testModalLabel">Bootstrap测试</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    如果你能看到这个模态框，说明Bootstrap JS加载成功了！
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 测试jQuery
            const testJQueryBtn = document.getElementById('test-jquery');
            if (testJQueryBtn) {
                testJQueryBtn.addEventListener('click', function() {
                    const jqueryResult = document.getElementById('jquery-result');
                    if (window.jQuery) {
                        jqueryResult.innerHTML = '<div class="alert alert-success">jQuery加载成功！版本: ' + jQuery.fn.jquery + '</div>';
                    } else {
                        jqueryResult.innerHTML = '<div class="alert alert-danger">jQuery加载失败！</div>';
                    }
                });
            }
            
            // 测试Marked
            const markdownOutput = document.getElementById('markdown-output');
            if (markdownOutput) {
                const markdownText = `
# Markdown测试

这是一个**Markdown**测试。

## 功能列表

- 项目1
- 项目2
- 项目3

> 如果你能看到格式化的Markdown，说明marked.js加载成功了！
                `;
                
                // 检查marked是否已加载
                setTimeout(function() {
                    if (window.marked) {
                        markdownOutput.innerHTML = marked.parse(markdownText);
                    } else {
                        markdownOutput.innerHTML = '<div class="alert alert-danger">marked.js加载失败！</div>';
                    }
                }, 2000); // 给资源加载一些时间
            }
            
            // 显示控制台提示
            console.log('请打开浏览器控制台查看资源加载情况');
        });
    </script>
</body>
</html> 