<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-diff</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="npm-diff">npm-diff</h1>
<span class="description">The registry diff command</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<pre lang="bash"><code>npm diff [...&lt;paths&gt;]
</code></pre>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h3 id="description">Description</h3>
<p>Similar to its <code>git diff</code> counterpart, this command will print diff patches
of files for packages published to the npm registry.</p>
<ul>
<li>
<p><code>npm diff --diff=&lt;spec-a&gt; --diff=&lt;spec-b&gt;</code></p>
<p>Compares two package versions using their registry specifiers, e.g:
<code>npm diff --diff=pkg@1.0.0 --diff=pkg@^2.0.0</code>. It's also possible to
compare across forks of any package,
e.g: <code>npm diff --diff=pkg@1.0.0 --diff=pkg-fork@1.0.0</code>.</p>
<p>Any valid spec can be used, so that it's also possible to compare
directories or git repositories,
e.g: <code>npm diff --diff=pkg@latest --diff=./packages/pkg</code></p>
<p>Here's an example comparing two different versions of a package named
<code>abbrev</code> from the registry:</p>
<pre lang="bash"><code>npm diff --diff=abbrev@1.1.0 --diff=abbrev@1.1.1
</code></pre>
<p>On success, output looks like:</p>
<pre lang="bash"><code>diff --git a/package.json b/package.json
index v1.1.0..v1.1.1 100644
--- a/package.json
+++ b/package.json
@@ -1,6 +1,6 @@
 {
   "name": "abbrev",
-  "version": "1.1.0",
+  "version": "1.1.1",
   "description": "Like ruby's abbrev module, but in js",
   "author": "Isaac Z. Schlueter &lt;<EMAIL>&gt;",
   "main": "abbrev.js",
</code></pre>
<p>Given the flexible nature of npm specs, you can also target local
directories or git repos just like when using <code>npm install</code>:</p>
<pre lang="bash"><code>npm diff --diff=https://github.com/npm/libnpmdiff --diff=./local-path
</code></pre>
<p>In the example above we can compare the contents from the package installed
from the git repo at <code>github.com/npm/libnpmdiff</code> with the contents of the
<code>./local-path</code> that contains a valid package, such as a modified copy of
the original.</p>
</li>
<li>
<p><code>npm diff</code> (in a package directory, no arguments):</p>
<p>If the package is published to the registry, <code>npm diff</code> will fetch the
tarball version tagged as <code>latest</code> (this value can be configured using the
<code>tag</code> option) and proceed to compare the contents of files present in that
tarball, with the current files in your local file system.</p>
<p>This workflow provides a handy way for package authors to see what
package-tracked files have been changed in comparison with the latest
published version of that package.</p>
</li>
<li>
<p><code>npm diff --diff=&lt;pkg-name&gt;</code> (in a package directory):</p>
<p>When using a single package name (with no version or tag specifier) as an
argument, <code>npm diff</code> will work in a similar way to
<a href="npm-outdated"><code>npm-outdated</code></a> and reach for the registry to figure out
what current published version of the package named <code>&lt;pkg-name&gt;</code>
will satisfy its dependent declared semver-range. Once that specific
version is known <code>npm diff</code> will print diff patches comparing the
current version of <code>&lt;pkg-name&gt;</code> found in the local file system with
that specific version returned by the registry.</p>
<p>Given a package named <code>abbrev</code> that is currently installed:</p>
<pre lang="bash"><code>npm diff --diff=abbrev
</code></pre>
<p>That will request from the registry its most up to date version and
will print a diff output comparing the currently installed version to this
newer one if the version numbers are not the same.</p>
</li>
<li>
<p><code>npm diff --diff=&lt;spec-a&gt;</code> (in a package directory):</p>
<p>Similar to using only a single package name, it's also possible to declare
a full registry specifier version if you wish to compare the local version
of an installed package with the specific version/tag/semver-range provided
in <code>&lt;spec-a&gt;</code>.</p>
<p>An example: assuming <code>pkg@1.0.0</code> is installed in the current <code>node_modules</code>
folder, running:</p>
<pre lang="bash"><code>npm diff --diff=pkg@2.0.0
</code></pre>
<p>It will effectively be an alias to
<code>npm diff --diff=pkg@1.0.0 --diff=pkg@2.0.0</code>.</p>
</li>
<li>
<p><code>npm diff --diff=&lt;semver-a&gt; [--diff=&lt;semver-b&gt;]</code> (in a package directory):</p>
<p>Using <code>npm diff</code> along with semver-valid version numbers is a shorthand
to compare different versions of the current package.</p>
<p>It needs to be run from a package directory, such that for a package named
<code>pkg</code> running <code>npm diff --diff=1.0.0 --diff=1.0.1</code> is the same as running
<code>npm diff --diff=pkg@1.0.0 --diff=pkg@1.0.1</code>.</p>
<p>If only a single argument <code>&lt;version-a&gt;</code> is provided, then the current local
file system is going to be compared against that version.</p>
<p>Here's an example comparing two specific versions (published to the
configured registry) of the current project directory:</p>
<pre lang="bash"><code>npm diff --diff=1.0.0 --diff=1.1.0
</code></pre>
</li>
</ul>
<p>Note that tag names are not valid <code>--diff</code> argument values, if you wish to
compare to a published tag, you must use the <code>pkg@tagname</code> syntax.</p>
<h4 id="filtering-files">Filtering files</h4>
<p>It's possible to also specify positional arguments using file names or globs
pattern matching in order to limit the result of diff patches to only a subset
of files for a given package, e.g:</p>
<pre lang="bash"><code>npm diff --diff=pkg@2 ./lib/ CHANGELOG.md
</code></pre>
<p>In the example above the diff output is only going to print contents of files
located within the folder <code>./lib/</code> and changed lines of code within the
<code>CHANGELOG.md</code> file.</p>
<h3 id="configuration">Configuration</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="diff"><code>diff</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Define arguments to compare in <code>npm diff</code>.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="diff-name-only"><code>diff-name-only</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Prints only filenames when using <code>npm diff</code>.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="diff-unified"><code>diff-unified</code></h4>
<ul>
<li>Default: 3</li>
<li>Type: Number</li>
</ul>
<p>The number of lines of context to print in <code>npm diff</code>.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="diff-ignore-all-space"><code>diff-ignore-all-space</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Ignore whitespace when comparing lines in <code>npm diff</code>.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="diff-no-prefix"><code>diff-no-prefix</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Do not show any source or destination prefix in <code>npm diff</code> output.</p>
<p>Note: this causes <code>npm diff</code> to ignore the <code>--diff-src-prefix</code> and
<code>--diff-dst-prefix</code> configs.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="diff-src-prefix"><code>diff-src-prefix</code></h4>
<ul>
<li>Default: "a/"</li>
<li>Type: String</li>
</ul>
<p>Source prefix to be used in <code>npm diff</code> output.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="diff-dst-prefix"><code>diff-dst-prefix</code></h4>
<ul>
<li>Default: "b/"</li>
<li>Type: String</li>
</ul>
<p>Destination prefix to be used in <code>npm diff</code> output.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="diff-text"><code>diff-text</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Treat all files as text in <code>npm diff</code>.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="global"><code>global</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Operates in "global" mode, so that packages are installed into the <code>prefix</code>
folder instead of the current working directory. See
<a href="../configuring-npm/folders.html">folders</a> for more on the differences in behavior.</p>
<ul>
<li>packages are installed into the <code>{prefix}/lib/node_modules</code> folder, instead
of the current working directory.</li>
<li>bin files are linked to <code>{prefix}/bin</code></li>
<li>man pages are linked to <code>{prefix}/share/man</code></li>
</ul>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="tag"><code>tag</code></h4>
<ul>
<li>Default: "latest"</li>
<li>Type: String</li>
</ul>
<p>If you ask npm to install a package and don't tell it a specific version,
then it will install the specified tag.</p>
<p>Also the tag that is added to the package@version specified by the <code>npm tag</code>
command, if no explicit tag is given.</p>
<p>When used by the <code>npm diff</code> command, this is the tag used to fetch the
tarball that will be compared with the local files by default.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="workspace"><code>workspace</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option.</p>
<p>Valid values for the <code>workspace</code> config are either:</p>
<ul>
<li>Workspace names</li>
<li>Path to a workspace directory</li>
<li>Path to a parent workspace directory (will result in selecting all
workspaces within that folder)</li>
</ul>
<p>When set for the <code>npm init</code> command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project.</p>
<p>This value is not exported to the environment for child processes.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="workspaces"><code>workspaces</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or Boolean</li>
</ul>
<p>Set to true to run the command in the context of <strong>all</strong> configured
workspaces.</p>
<p>Explicitly setting this to false will cause commands like <code>install</code> to
ignore workspaces altogether. When not set explicitly:</p>
<ul>
<li>Commands that operate on the <code>node_modules</code> tree (install, update, etc.)
will link workspaces into the <code>node_modules</code> folder. - Commands that do
other things (test, exec, publish, etc.) will operate on the root project,
<em>unless</em> one or more workspaces are specified in the <code>workspace</code> config.</li>
</ul>
<p>This value is not exported to the environment for child processes.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="include-workspace-root"><code>include-workspace-root</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Include the workspace root when workspaces are enabled for a command.</p>
<p>When false, specifying individual workspaces via the <code>workspace</code> config, or
all workspaces via the <code>workspaces</code> flag, will cause npm to operate only on
the specified workspaces, and not on the root project.</p>
<p>This value is not exported to the environment for child processes.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h2 id="see-also">See Also</h2>
<ul>
<li><a href="../commands/npm-outdated.html">npm outdated</a></li>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../using-npm/registry.html">npm registry</a></li>
</ul>
</div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-diff.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>