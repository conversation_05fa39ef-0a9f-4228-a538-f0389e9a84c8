"""
九猫 - 使用API修复数据库脚本
通过调用API接口修复数据库
"""

import os
import sys
import json
import logging
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def fix_database():
    """通过调用API接口修复数据库"""
    logger.info("开始通过API修复数据库")
    
    # 创建有效的内容
    valid_content = """# 分析过程中出错

## 错误详情
分析维度 **character_relationships** 时遇到了问题。

## 错误信息
```
name 'stats_start' is not defined
```

## 建议操作
请尝试以下解决方法：
1. 刷新页面并重新尝试分析
2. 检查小说文本是否过长或包含特殊字符
3. 确认API连接正常

如果问题持续存在，请联系系统管理员。
"""
    
    # 创建有效的元数据
    valid_metadata = {
        "processing_time": 0,
        "chunk_count": 0,
        "api_calls": 0,
        "tokens_used": 0,
        "cost": 0,
        "fixed_by_script": True,
        "fix_timestamp": datetime.now().isoformat()
    }
    
    # 准备请求数据
    data = {
        "novel_id": 4,
        "dimension": "character_relationships",
        "content": valid_content,
        "metadata": valid_metadata
    }
    
    try:
        # 尝试删除现有分析结果
        delete_url = "http://localhost:5001/api/analysis/delete"
        delete_data = {
            "novel_id": 4,
            "dimension": "character_relationships"
        }
        
        logger.info(f"尝试删除现有分析结果: {delete_url}")
        delete_response = requests.post(delete_url, json=delete_data)
        
        if delete_response.status_code == 200:
            logger.info("成功删除现有分析结果")
        else:
            logger.warning(f"删除现有分析结果失败: {delete_response.status_code} {delete_response.text}")
        
        # 创建新的分析结果
        create_url = "http://localhost:5001/api/analysis/create"
        
        logger.info(f"尝试创建新的分析结果: {create_url}")
        create_response = requests.post(create_url, json=data)
        
        if create_response.status_code == 200:
            logger.info("成功创建新的分析结果")
            return True
        else:
            logger.error(f"创建新的分析结果失败: {create_response.status_code} {create_response.text}")
            return False
    except Exception as e:
        logger.error(f"调用API时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始运行API修复脚本")
    
    # 修复数据库
    success = fix_database()
    
    if success:
        logger.info("数据库修复成功")
        logger.info("请刷新页面以查看更改")
    else:
        logger.error("数据库修复失败")

if __name__ == "__main__":
    main()
