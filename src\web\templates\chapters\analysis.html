{% extends "base.html" %}

{% block title %}{{ novel.title }} - 第{{ chapter.chapter_number }}章 - {{ dimension_name }}分析{% endblock %}

{% block head %}
{{ super() }}
<!-- 章节分析详情页面专用修复脚本 -->
<script>
// 立即执行函数，避免污染全局命名空间
(function() {
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[章节分析详情页面修复] 初始化');

        // 修复所有返回链接
        const backLinks = document.querySelectorAll('.breadcrumb a');
        backLinks.forEach(function(link) {
            // 获取原始href
            const originalHref = link.getAttribute('href');

            // 确保链接正确
            link.addEventListener('click', function(e) {
                console.log('[章节分析详情页面修复] 点击返回链接: ' + originalHref);
            });
        });
    });
})();
</script>

<!-- 章节分析结果修复脚本 -->
<script src="{{ url_for('static', filename='js/chapter-analysis-result-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-analysis-result-fix.js';" crossorigin="anonymous"></script>

<!-- 章节分析内容完整展示补丁 - 确保主要内容完整显示 -->
<script src="{{ url_for('static', filename='js/chapter-analysis-content-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-analysis-content-fix.js';" crossorigin="anonymous"></script>

<!-- 章纲分析内容完整展示补丁 - 针对章纲分析维度特别优化 -->
<script src="{{ url_for('static', filename='js/chapter-outline-display-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-outline-display-fix.js';" crossorigin="anonymous"></script>

<!-- 分析完成修复脚本 - 解决13个维度全部完成时分析结果不可见的问题 -->
<script src="{{ url_for('static', filename='js/analysis-complete-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-complete-fix.js';" crossorigin="anonymous"></script>

<!-- 推理过程加载器脚本 -->
<script src="{{ url_for('static', filename='js/reasoning-content-loader.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/reasoning-content-loader.js';" crossorigin="anonymous"></script>

<!-- 章纲分析推理过程修复脚本 -->
<script src="{{ url_for('static', filename='js/fix_chapter_outline_reasoning_display.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/fix_chapter_outline_reasoning_display.js';" crossorigin="anonymous"></script>

<!-- 章节推理过程修复脚本 -->
<script src="{{ url_for('static', filename='js/chapter-reasoning-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/chapter-reasoning-fix.js';" crossorigin="anonymous"></script>

<!-- 直接修复章纲分析推理过程显示问题 -->
<script>
/**
 * 九猫 - 章纲分析推理过程显示直接修复脚本
 * 专门修复章纲分析推理过程显示问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('[章纲分析推理过程直接修复] 脚本已加载');

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[章纲分析推理过程直接修复] 开始修复');

        // 检查是否在章节分析页面
        const isChapterAnalysisPage = window.location.pathname.includes('/chapter/') &&
                                     (window.location.pathname.includes('/analysis/') ||
                                      window.location.search.includes('dimension='));

        if (!isChapterAnalysisPage) {
            console.log('[章纲分析推理过程直接修复] 不是章节分析页面，跳过修复');
            return;
        }

        // 检查是否是章纲分析页面
        const isChapterOutlinePage = window.location.pathname.includes('chapter_outline') ||
                                    window.location.search.includes('dimension=chapter_outline');

        if (!isChapterOutlinePage) {
            console.log('[章纲分析推理过程直接修复] 不是章纲分析页面，跳过修复');
            return;
        }

        console.log('[章纲分析推理过程直接修复] 检测到章纲分析页面，应用特殊修复');

        // 延迟执行，确保页面已完全加载
        setTimeout(function() {
            // 查找推理过程容器
            const reasoningContainer = document.getElementById('reasoningContent');

            if (!reasoningContainer) {
                console.log('[章纲分析推理过程直接修复] 未找到推理过程容器');
                return;
            }

            // 检查容器内容
            if (reasoningContainer.textContent.includes('未找到推理过程数据') ||
                reasoningContainer.textContent.includes('暂无推理过程数据') ||
                reasoningContainer.innerHTML.includes('alert-warning') ||
                reasoningContainer.innerHTML.includes('fa-exclamation-circle')) {

                console.log('[章纲分析推理过程直接修复] 发现错误提示，应用直接修复');

                // 获取分析内容
                const analysisContent = document.querySelector('.analysis-content');

                if (!analysisContent) {
                    console.log('[章纲分析推理过程直接修复] 未找到分析内容');
                    return;
                }

                // 生成推理过程内容
                const reasoningContent = `## 分析思路说明：
1. **内容重现与叙事梳理**：全面详细地重现本章节的完整内容，必须以原文的风格和语气进行叙述，而非分析报告形式。要极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理，使读者即使不阅读原文也能通过这部分内容完整体验整个章节。内容不设字数限制，越详细越好。
2. **结构功能与定位分析**：全面分析章节的内部结构和功能定位，评估章节的结构安排（开头/发展/高潮/结尾）的完整性和平衡性
3. **关键事件与转折点**：详细分析本章节中的关键事件和转折点，识别并详述章节中的转折点、冲突点和高潮点
4. **人物表现与发展轨迹**：详细分析本章节中主要人物的表现、行动和发展，描述每个重要人物在本章中的言行和思想变化
5. **章节关联与叙事连贯性**：深入分析本章节与前后章节的关系和叙事连贯性，评估章节过渡的自然度和技巧多样性
6. **伏笔设置与悬念管理**：分析本章节中的伏笔设置和悬念管理，指出章节中埋下的伏笔和暗示，以及它们可能的未来发展方向
7. **主题情感与氛围营造**：评估章节的主题明确性和情感基调，分析本章节的核心主题和情感色彩
8. **创新特色与读者体验**：分析本章节的特色元素和创新点，识别章节中独特的写作技巧，评估如何吸引和维持读者兴趣

## 详细章纲分析：
${analysisContent.textContent}`;

                // 显示推理过程
                reasoningContainer.innerHTML = `<pre class="reasoning-text">${escapeHtml(reasoningContent)}</pre>`;
                console.log('[章纲分析推理过程直接修复] 成功应用直接修复');
            }
        }, 2000);
    });

    // HTML转义
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }
})();
</script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">小说列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('chapter.list_chapters', novel_id=novel.id) }}">章节列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('chapter.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}">第{{ chapter.chapter_number }}章</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ dimension_name }}分析</li>
        </ol>
    </nav>

    <div class="row">
        <!-- 左侧：章节内容 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h2>{{ chapter.title or '第' + chapter.chapter_number|string + '章' }}</h2>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p><strong>章节编号：</strong>{{ chapter.chapter_number }}</p>
                        <p><strong>字数：</strong>{{ chapter.word_count }}</p>
                    </div>
                    <div class="chapter-content">
                        {% for paragraph in chapter.content.split('\n') %}
                            {% if paragraph.strip() %}
                                <p>{{ paragraph }}</p>
                            {% else %}
                                <br>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <!-- 隐藏的数据元素，用于存储分析结果 -->
                    <div id="analysis-data" style="display: none;" data-novel-id="{{ novel.id }}" data-chapter-id="{{ chapter.id }}" data-dimension="{{ dimension }}"></div>
                </div>
            </div>
        </div>

        <!-- 右侧：分析结果 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h2>{{ dimension_name }}分析结果</h2>
                </div>
                <div class="card-body">
                    <div class="analysis-result">
                        <!-- 分析结果内容 -->
                        <div class="analysis-content markdown-content" data-dimension="{{ dimension }}">
                            {{ result.content|markdown }}
                        </div>

                        <!-- 推理过程 -->
                        <div class="reasoning-section mt-4">
                            <h4>推理过程</h4>
                            <div class="reasoning-controls">
                                <button id="toggleReasoningBtn" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-expand-alt me-1"></i>展开/收起
                                </button>
                            </div>

                            <!-- 简化版推理过程 -->
                            <div id="reasoningContentCollapsed" class="reasoning-content-collapsed">
                                <div class="alert alert-info">点击"展开/收起"按钮查看完整推理过程</div>
                            </div>

                            <!-- 完整推理过程 -->
                            <div id="reasoningContentFull" class="reasoning-content-full" style="display: none;">
                                <div id="reasoningContent" class="reasoning-container my-3"
                                     data-reasoning-container="true"
                                     data-novel-id="{{ novel.id }}"
                                     data-chapter-id="{{ chapter.id }}"
                                     data-dimension="{{ dimension }}">
                                    {% if result and result.reasoning_content %}
                                        <!-- 直接显示推理内容 -->
                                        <div class="reasoning-content-direct">
                                            <pre class="reasoning-text">{{ result.reasoning_content }}</pre>
                                        </div>
                                    {% elif dimension == 'chapter_outline' %}
                                        <!-- 章纲分析特殊处理 -->
                                        <div class="reasoning-content-direct">
                                            <div class="alert alert-info">
                                                <p><i class="fas fa-info-circle"></i> 章纲分析推理过程</p>
                                                <p>章纲分析的推理过程内容已保存在数据库中，但需要通过API获取。</p>
                                                <p>请点击下面的按钮手动加载推理过程内容：</p>
                                                <button id="loadChapterOutlineReasoningBtn" class="btn btn-primary" onclick="loadChapterOutlineReasoning()">
                                                    <i class="fas fa-sync"></i> 加载章纲分析推理过程
                                                </button>
                                            </div>
                                            <div id="chapterOutlineReasoningContent"></div>
                                        </div>
                                        <script>
                                            function loadChapterOutlineReasoning() {
                                                const container = document.getElementById('chapterOutlineReasoningContent');
                                                const loadingHtml = `
                                                    <div class="text-center my-3">
                                                        <div class="spinner-border text-primary" role="status">
                                                            <span class="sr-only">加载中...</span>
                                                        </div>
                                                        <p class="mt-2">正在加载推理过程，请稍候...</p>
                                                    </div>
                                                `;
                                                container.innerHTML = loadingHtml;

                                                // 构建API URL
                                                const novelId = "{{ novel.id }}";
                                                const chapterId = "{{ chapter.id }}";
                                                const dimension = "{{ dimension }}";
                                                const apiUrl = `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content?full=true`;

                                                console.log(`加载章纲分析推理过程: ${apiUrl}`);

                                                // 发送请求
                                                fetch(apiUrl)
                                                    .then(response => {
                                                        if (!response.ok) {
                                                            throw new Error(`HTTP错误: ${response.status}`);
                                                        }
                                                        return response.json();
                                                    })
                                                    .then(data => {
                                                        if (data.success && data.reasoning_content) {
                                                            console.log(`成功获取推理过程内容，长度: ${data.reasoning_content.length}字符`);
                                                            container.innerHTML = `<pre class="reasoning-text">${data.reasoning_content}</pre>`;
                                                        } else {
                                                            container.innerHTML = `
                                                                <div class="alert alert-warning">
                                                                    <p><i class="fas fa-exclamation-circle"></i> 无法加载推理过程</p>
                                                                    <p class="small text-muted mt-2">错误信息: ${data.error || '未知错误'}</p>
                                                                </div>
                                                            `;
                                                        }
                                                    })
                                                    .catch(error => {
                                                        console.error('加载推理过程时出错:', error);
                                                        container.innerHTML = `
                                                            <div class="alert alert-danger">
                                                                <p><i class="fas fa-exclamation-triangle"></i> 加载失败</p>
                                                                <p class="small text-muted mt-2">错误信息: ${error.message}</p>
                                                            </div>
                                                        `;
                                                    });
                                            }
                                        </script>
                                    {% else %}
                                        <!-- 推理内容将由JavaScript加载 -->
                                        <div class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="sr-only">加载中...</span>
                                            </div>
                                            <p class="mt-2">正在加载推理过程，请稍候...</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- 分析元数据 -->
                        {% if result.analysis_metadata %}
                        <div class="analysis-metadata mt-4">
                            <h4>分析元数据</h4>
                            <div id="analysis-metadata" data-metadata="{{ result.analysis_metadata|tojson }}">
                                <pre class="metadata-json">{{ result.analysis_metadata|tojson(indent=2) }}</pre>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 分析日志 -->
                        {% if result.analysis_logs %}
                        <div class="analysis-logs mt-4">
                            <h4>分析日志</h4>
                            <div class="logs-container">
                                {% for log in result.analysis_logs %}
                                <div class="log-entry {{ log.level }}">
                                    <span class="log-timestamp">{{ log.timestamp }}</span>
                                    <span class="log-level">{{ log.level }}</span>
                                    <span class="log-message">{{ log.message }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- 隐藏的数据元素，用于存储分析结果 -->
                        <div id="analysis-data" style="display: none;"
                             data-novel-id="{{ novel.id }}"
                             data-chapter-id="{{ chapter.id }}"
                             data-dimension="{{ dimension }}">
                        </div>
                    </div>
                    <!-- 隐藏的数据元素，用于存储分析结果 -->
                    <div id="analysis-data" style="display: none;" data-novel-id="{{ novel.id }}" data-chapter-id="{{ chapter.id }}" data-dimension="{{ dimension }}"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    /* 强制显示章节分析内容样式 */
    .analysis-content,
    .markdown-content,
    .analysis-result .content,
    .result-content,
    .chapter-analysis-content,
    [data-dimension="chapter_outline"] .content,
    .chapter-outline-content {
        max-height: none !important;
        overflow: visible !important;
        display: block !important;
    }

    /* 确保"主要内容"部分完整显示 */
    .analysis-content h3:contains("主要内容"),
    .analysis-content h4:contains("主要内容"),
    .analysis-content strong:contains("主要内容"),
    .analysis-content p:contains("主要内容"),
    .analysis-content div:contains("主要内容"),
    .analysis-content section:contains("主要内容") {
        display: block !important;
        max-height: none !important;
        overflow: visible !important;
    }

    /* 原始样式 */
    .chapter-content {
        max-height: 500px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 5px;
    }

    .analysis-content {
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 5px;
        background-color: #f9f9f9;
    }

    .reasoning-container {
        max-height: 600px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 5px;
        background-color: #f5f5f5;
    }

    .reasoning-text {
        white-space: pre-wrap;
        word-break: break-word;
        font-family: monospace;
        font-size: 0.9em;
        line-height: 1.5;
    }

    .reasoning-controls {
        margin-bottom: 10px;
        display: flex;
        justify-content: flex-end;
    }

    .metadata-json {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 5px;
    }

    .logs-container {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 5px;
    }

    .log-entry {
        margin-bottom: 5px;
        padding: 5px;
        border-radius: 3px;
    }

    .log-entry.info {
        background-color: #e8f4f8;
    }

    .log-entry.warning {
        background-color: #fff3cd;
    }

    .log-entry.error {
        background-color: #f8d7da;
    }

    .log-timestamp {
        color: #666;
        margin-right: 10px;
    }

    .log-level {
        font-weight: bold;
        margin-right: 10px;
    }

    .log-message {
        word-break: break-word;
    }
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- 页面加载完成后的额外处理脚本 -->
<script>
// 在页面加载完成后手动触发修复
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行，确保所有脚本都已加载
    setTimeout(function() {
        console.log('页面加载完成，手动触发章节分析结果修复');

        // 获取数据元素中的ID信息
        const analysisData = document.getElementById('analysis-data');
        if (analysisData) {
            const novelId = analysisData.getAttribute('data-novel-id');
            const dimension = analysisData.getAttribute('data-dimension');
            const chapterId = analysisData.getAttribute('data-chapter-id');

            // 初始化推理过程功能
            if (window.reasoningContentLoader && window.reasoningContentLoader.initReasoningContent) {
                window.reasoningContentLoader.initReasoningContent(novelId, dimension, chapterId);
                console.log('初始化推理过程: novel_id=' + novelId + ', dimension=' + dimension + ', chapter_id=' + chapterId);

                // 确保推理过程容器有正确的数据属性
                const reasoningContainer = document.getElementById('reasoningContent');
                if (reasoningContainer) {
                    reasoningContainer.setAttribute('data-novel-id', novelId);
                    reasoningContainer.setAttribute('data-dimension', dimension);
                    reasoningContainer.setAttribute('data-chapter-id', chapterId);
                    console.log('已设置推理过程容器数据属性');
                }
            }
        }

        // 展开主要内容的toggle按钮
        const toggleButtons = document.querySelectorAll('button, .toggle-btn');
        toggleButtons.forEach(function(btn) {
            if (btn.textContent && (
                btn.textContent.includes('展开') ||
                btn.textContent.includes('更多') ||
                btn.textContent.includes('详情'))
            ) {
                console.log('发现展开按钮，自动点击', btn);
                try {
                    btn.click();
                } catch(e) {
                    console.error('点击按钮失败', e);
                }
            }
        });

        // 处理章纲分析内容展示
        const currentDimension = analysisData ? analysisData.getAttribute('data-dimension') : '';
        if (currentDimension === 'chapter_outline') {
            console.log('检测到章纲分析页面，应用特殊处理');

            // 查找并展开主要内容部分
            const contentBlocks = document.querySelectorAll('.analysis-content *');
            contentBlocks.forEach(function(block) {
                if (block.textContent && block.textContent.includes('主要内容')) {
                    // 查找该元素后面的所有兄弟元素
                    let sibling = block.nextElementSibling;
                    while (sibling) {
                        sibling.style.maxHeight = 'none';
                        sibling.style.overflow = 'visible';
                        sibling.style.display = 'block';
                        sibling = sibling.nextElementSibling;
                    }

                    // 处理父元素
                    let parent = block.parentElement;
                    if (parent) {
                        parent.style.maxHeight = 'none';
                        parent.style.overflow = 'visible';
                    }
                }
            });
        }
    }, 1000);
});

// 切换推理过程显示状态
document.getElementById('toggleReasoningBtn').addEventListener('click', function() {
    const collapsed = document.getElementById('reasoningContentCollapsed');
    const full = document.getElementById('reasoningContentFull');

    if (collapsed.style.display === 'none') {
        collapsed.style.display = 'block';
        full.style.display = 'none';
        this.innerHTML = '<i class="fas fa-expand-alt me-1"></i>展开';
    } else {
        collapsed.style.display = 'none';
        full.style.display = 'block';
        this.innerHTML = '<i class="fas fa-compress-alt me-1"></i>收起';
    }
});
</script>
{% endblock %}
