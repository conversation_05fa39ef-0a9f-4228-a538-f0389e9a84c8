/**
 * 九猫 - 分析维度选择修复脚本
 * 用于修复分析维度选择对话框中缺少维度选项的问题
 * 版本: 1.1.0
 */

(function() {
    console.log('[九猫维度选择修复] 脚本已加载');

    // 维度列表 - 与config.py中的ANALYSIS_DIMENSIONS保持一致
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "chapter_outline", name: "章节大纲" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" },
        { key: "outline_analysis", name: "大纲分析" },
        { key: "popular_tropes", name: "热梗统计" }
    ];

    // 尝试立即修复当前页面中的维度选择对话框
    function fixDimensionSelectionNow() {
        console.log('[九猫维度选择修复] 尝试立即修复维度选择...');

        // 查找所有可能的分析对话框
        const analyzeModals = document.querySelectorAll('.modal');

        analyzeModals.forEach(modal => {
            // 检查是否是分析维度选择对话框
            const modalTitle = modal.querySelector('.modal-title');
            if (modalTitle && modalTitle.textContent && modalTitle.textContent.includes('选择分析维度')) {
                console.log('[九猫维度选择修复] 找到分析维度选择对话框，开始修复');

                // 尝试查找维度容器
                const modalBody = modal.querySelector('.modal-body');
                if (!modalBody) return;

                // 查找全选选项
                const selectAllOption = modalBody.querySelector('#select-all-dimensions, [data-select-all="dimensions"]');
                if (!selectAllOption) {
                    console.log('[九猫维度选择修复] 未找到全选选项，可能不是维度选择对话框');
                    return;
                }

                // 查找已存在的维度选项
                const existingDimensions = modalBody.querySelectorAll('.dimension-checkbox, [name="dimensions"]');

                // 如果已有维度选项，且数量与DIMENSIONS相符，则无需修复
                if (existingDimensions.length >= DIMENSIONS.length) {
                    console.log(`[九猫维度选择修复] 已有${existingDimensions.length}个维度选项，无需修复`);
                    return;
                }

                console.log(`[九猫维度选择修复] 需要添加维度选项，当前仅有${existingDimensions.length}个`);

                // 创建维度选项容器
                const dimensionsContainer = document.createElement('div');
                dimensionsContainer.id = 'dimensions-container';
                dimensionsContainer.className = 'mb-3';

                // 添加所有维度选项
                DIMENSIONS.forEach((dimension, index) => {
                    // 创建新的选择项
                    const checkboxContainer = document.createElement('div');
                    checkboxContainer.className = 'form-check mb-2';

                    const checkbox = document.createElement('input');
                    checkbox.className = 'form-check-input dimension-checkbox';
                    checkbox.type = 'checkbox';
                    checkbox.name = 'dimensions';
                    checkbox.value = dimension.key;
                    checkbox.id = `dimension-${index}`;
                    checkbox.checked = true; // 默认选中

                    const label = document.createElement('label');
                    label.className = 'form-check-label';
                    label.setAttribute('for', `dimension-${index}`);
                    label.textContent = dimension.name;

                    // 添加到容器中
                    checkboxContainer.appendChild(checkbox);
                    checkboxContainer.appendChild(label);
                    dimensionsContainer.appendChild(checkboxContainer);
                });

                // 查找合适的插入位置
                const hr = modalBody.querySelector('hr');
                if (hr) {
                    // 在分隔线后插入
                    if (hr.nextSibling) {
                        hr.parentNode.insertBefore(dimensionsContainer, hr.nextSibling);
                    } else {
                        hr.parentNode.appendChild(dimensionsContainer);
                    }
                } else {
                    // 如果没有分隔线，尝试找其他位置
                    const selectAllContainer = selectAllOption.closest('.form-check, .mb-3');
                    if (selectAllContainer && selectAllContainer.nextSibling) {
                        selectAllContainer.parentNode.insertBefore(dimensionsContainer, selectAllContainer.nextSibling);
                    } else {
                        // 尝试直接加到模态框内容最后
                        modalBody.appendChild(dimensionsContainer);
                    }
                }

                console.log(`[九猫维度选择修复] 已添加${DIMENSIONS.length}个维度选项`);

                // 设置全选按钮功能
                if (selectAllOption) {
                    selectAllOption.addEventListener('change', function() {
                        const checkboxes = dimensionsContainer.querySelectorAll('.dimension-checkbox');
                        checkboxes.forEach(checkbox => {
                            checkbox.checked = selectAllOption.checked;
                        });
                    });
                }
            }
        });
    }

    // 监听DOM变化，以便在对话框打开时自动修复
    function setupMutationObserver() {
        // 创建一个观察器实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 检查添加的节点中是否有模态框
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.classList && node.classList.contains('modal')) {
                            // 延迟执行，确保模态框完全加载
                            setTimeout(fixDimensionSelectionNow, 200);
                        }
                    });
                }

                // 检查class变化，可能是模态框打开
                if (mutation.type === 'attributes' &&
                    mutation.attributeName === 'class' &&
                    mutation.target.classList &&
                    mutation.target.classList.contains('modal') &&
                    mutation.target.classList.contains('show')) {
                    // 延迟执行，确保模态框完全加载
                    setTimeout(fixDimensionSelectionNow, 200);
                }
            });
        });

        // 配置观察器
        const config = { childList: true, subtree: true, attributes: true, attributeFilter: ['class'] };

        // 开始观察
        observer.observe(document.body, config);

        console.log('[九猫维度选择修复] DOM变化监听已设置');
    }

    // 监听页面上的分析按钮点击
    function listenToAnalyzeButtons() {
        document.addEventListener('click', function(event) {
            // 找到可能是分析按钮的元素
            const target = event.target;

            // 检查是否点击了分析按钮
            if (target.classList &&
                (target.classList.contains('analyze-btn') ||
                 target.getAttribute('data-bs-target') === '#analyzeModal' ||
                 target.classList.contains('btn-analyze'))) {
                console.log('[九猫维度选择修复] 检测到分析按钮点击');

                // 延迟执行，等待模态框打开
                setTimeout(fixDimensionSelectionNow, 300);
            }

            // 也检查父元素
            const parent = target.parentElement;
            if (parent && parent.classList &&
                (parent.classList.contains('analyze-btn') ||
                 parent.getAttribute('data-bs-target') === '#analyzeModal' ||
                 parent.classList.contains('btn-analyze'))) {
                console.log('[九猫维度选择修复] 检测到分析按钮容器点击');

                // 延迟执行，等待模态框打开
                setTimeout(fixDimensionSelectionNow, 300);
            }
        });

        console.log('[九猫维度选择修复] 分析按钮点击监听已设置');
    }

    // 在jQuery函数上添加钩子，捕获可能的模态框打开操作
    function hookJQuery() {
        if (typeof window.$ !== 'undefined' && typeof window.$.fn !== 'undefined') {
            // 保存原始的modal方法
            const originalModal = window.$.fn.modal;

            // 重写modal方法
            window.$.fn.modal = function(option) {
                const result = originalModal.apply(this, arguments);

                // 如果是打开模态框
                if (option === 'show' || option === undefined || option.show) {
                    // 检查是否是分析模态框
                    const modalElement = this.get(0);
                    if (modalElement &&
                        (modalElement.id === 'analyzeModal' ||
                         modalElement.querySelector('.modal-title')?.textContent?.includes('选择分析维度'))) {
                        console.log('[九猫维度选择修复] 通过jQuery钩子检测到模态框打开');
                        setTimeout(fixDimensionSelectionNow, 200);
                    }
                }

                return result;
            };

            console.log('[九猫维度选择修复] jQuery钩子已设置');
        }
    }

    // 在Bootstrap函数上添加钩子，捕获可能的模态框打开操作
    function hookBootstrap() {
        if (typeof window.bootstrap !== 'undefined' && typeof window.bootstrap.Modal !== 'undefined') {
            // 保存原始的show方法
            const originalShow = window.bootstrap.Modal.prototype.show;

            // 重写show方法
            window.bootstrap.Modal.prototype.show = function() {
                const result = originalShow.apply(this, arguments);

                // 检查是否是分析模态框
                const modalElement = this._element;
                if (modalElement &&
                    (modalElement.id === 'analyzeModal' ||
                     modalElement.querySelector('.modal-title')?.textContent?.includes('选择分析维度'))) {
                    console.log('[九猫维度选择修复] 通过Bootstrap钩子检测到模态框打开');
                    setTimeout(fixDimensionSelectionNow, 200);
                }

                return result;
            };

            console.log('[九猫维度选择修复] Bootstrap钩子已设置');
        }
    }

    // 初始化函数
    function initialize() {
        console.log('[九猫维度选择修复] 初始化中...');

        // 尝试立即修复当前页面上的维度选择对话框
        fixDimensionSelectionNow();

        // 设置监听器以捕获后续的对话框打开
        setupMutationObserver();
        listenToAnalyzeButtons();

        // 设置框架钩子
        hookJQuery();
        hookBootstrap();

        console.log('[九猫维度选择修复] 初始化完成');
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 导出到全局命名空间，方便调试
    window.dimensionSelectionFix = {
        fixNow: fixDimensionSelectionNow,
        dimensions: DIMENSIONS
    };
})();
