{% extends "new_base.html" %}

{% block title %}{{ novel.title }} - 章节列表 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-0">章节列表</h1>
        <p class="text-muted">
            小说: <a href="{{ url_for('new.view_novel', novel_id=novel.id) }}">{{ novel.title }}</a> |
            总章节数: {{ chapters|length }} |
            总字数: {{ novel.word_count or '未知' }}
        </p>
    </div>
    <div class="col-md-4 text-md-end">
        <a href="{{ url_for('new.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回小说
        </a>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-cog me-2"></i>操作
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="#" id="analyzeAllChapters">
                    <i class="fas fa-play-circle me-2"></i>分析所有章节
                </a></li>
                <li><a class="dropdown-item" href="#" id="exportChapterList">
                    <i class="fas fa-file-export me-2"></i>导出章节列表
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" id="refreshChapters">
                    <i class="fas fa-sync-alt me-2"></i>重新划分章节
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- 章节列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h3 class="card-title"><i class="fas fa-list-ol me-2"></i>章节列表</h3>
        <div>
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-secondary" id="expandAllChapters">
                    <i class="fas fa-expand-alt me-1"></i>全部展开
                </button>
                <button type="button" class="btn btn-outline-secondary" id="collapseAllChapters">
                    <i class="fas fa-compress-alt me-1"></i>全部收起
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="accordion" id="chaptersAccordion">
            {% for chapter in chapters %}
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading{{ chapter.id }}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ chapter.id }}" aria-expanded="false" aria-controls="collapse{{ chapter.id }}">
                        <div class="d-flex justify-content-between align-items-center w-100 me-3">
                            <div>
                                <span class="chapter-number badge bg-primary me-2">{{ chapter.id }}</span>
                                <span class="chapter-title">{{ chapter.title }}</span>
                            </div>
                            <div class="text-muted small">
                                {{ chapter.word_count }} 字
                            </div>
                        </div>
                    </button>
                </h2>
                <div id="collapse{{ chapter.id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ chapter.id }}" data-bs-parent="#chaptersAccordion">
                    <div class="accordion-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="chapter-content">
                                    {{ chapter.content[:500] }}{% if chapter.content|length > 500 %}...{% endif %}
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-sm btn-outline-primary view-full-chapter" data-chapter-id="{{ chapter.id }}">
                                        <i class="fas fa-book-open me-1"></i>查看完整内容
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">章节分析</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-sm btn-primary analyze-chapter" data-chapter-id="{{ chapter.id }}">
                                                <i class="fas fa-chart-bar me-1"></i>分析此章节
                                            </button>
                                            <div class="dropdown dimension-dropdown" data-chapter-id="{{ chapter.id }}">
                                                <button class="btn btn-sm btn-outline-primary dropdown-toggle w-100" type="button" id="dimensionDropdown{{ chapter.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-cubes me-1"></i>选择分析维度
                                                </button>
                                                <ul class="dropdown-menu w-100" aria-labelledby="dimensionDropdown{{ chapter.id }}">
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="language_style">语言风格</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="rhythm_pacing">节奏与节奏</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="structure">结构分析</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="sentence_variation">句式变化</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="paragraph_length">段落长度</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="perspective_shifts">视角转换</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="paragraph_flow">段落流畅度</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="novel_characteristics">小说特点</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="world_building">世界构建</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="character_relationships">人物关系</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="opening_effectiveness">开篇效果</a></li>
                                                    <li><a class="dropdown-item dimension-item" href="#" data-dimension="climax_pacing">高潮节奏</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item dimension-all" href="#" data-chapter-id="{{ chapter.id }}"><strong>分析全部维度</strong></a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- 章节内容模态框 -->
<div class="modal fade" id="chapterContentModal" tabindex="-1" aria-labelledby="chapterContentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chapterContentModalLabel">章节内容</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="fullChapterContent" class="chapter-full-content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="analyzeFromModal">
                    <i class="fas fa-chart-bar me-1"></i>分析此章节
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 分析进度模态框 -->
<div class="modal fade" id="analysisProgressModal" tabindex="-1" aria-labelledby="analysisProgressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analysisProgressModalLabel">章节分析进行中</h5>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <h5 id="currentChapterLabel">正在分析: <span id="currentChapter">准备中...</span></h5>
                <h5 id="currentDimensionLabel">分析维度: <span id="currentDimension">准备中...</span></h5>

                <div class="progress mb-3">
                    <div id="dimensionProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>

                <div class="d-flex justify-content-between mb-4">
                    <span id="progressPercentage">0%</span>
                    <span id="estimatedTime">预计剩余时间: 计算中...</span>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>分析过程可能需要几分钟时间，请耐心等待。您可以关闭此窗口，稍后再回来查看结果。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">在后台继续</button>
                <button type="button" class="btn btn-danger" id="cancelAnalysisBtn">取消分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 全部展开/收起
        document.getElementById('expandAllChapters').addEventListener('click', function() {
            document.querySelectorAll('.accordion-button.collapsed').forEach(button => {
                button.click();
            });
        });

        document.getElementById('collapseAllChapters').addEventListener('click', function() {
            document.querySelectorAll('.accordion-button:not(.collapsed)').forEach(button => {
                button.click();
            });
        });

        // 注释：原有的下拉菜单处理代码已被移除
        // 现在使用dimension-dropdown-fix.js脚本处理维度下拉菜单
        console.log('维度下拉菜单将由dimension-dropdown-fix.js脚本处理');

        // 查看完整章节内容
        const chapterContentModal = new bootstrap.Modal(document.getElementById('chapterContentModal'));
        const fullChapterContent = document.getElementById('fullChapterContent');
        let currentChapterId = null;

        document.querySelectorAll('.view-full-chapter').forEach(button => {
            button.addEventListener('click', function() {
                const chapterId = this.getAttribute('data-chapter-id');
                currentChapterId = chapterId;

                // 获取章节内容
                const chapterContent = document.querySelector(`#collapse${chapterId} .chapter-content`).textContent;
                const chapterTitle = document.querySelector(`#heading${chapterId} .chapter-title`).textContent;

                // 更新模态框
                document.getElementById('chapterContentModalLabel').textContent = chapterTitle;
                fullChapterContent.textContent = chapterContent;

                // 显示模态框
                chapterContentModal.show();
            });
        });

        // 从模态框分析章节
        document.getElementById('analyzeFromModal').addEventListener('click', function() {
            if (currentChapterId) {
                // 关闭章节内容模态框
                chapterContentModal.hide();

                // 显示分析进度模态框
                const analysisProgressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
                analysisProgressModal.show();

                // 更新进度信息
                const chapterTitle = document.querySelector(`#heading${currentChapterId} .chapter-title`).textContent;
                document.getElementById('currentChapter').textContent = chapterTitle;
                document.getElementById('currentDimension').textContent = '全部维度';

                // 获取小说ID
                const novelId = {{ novel.id }};

                console.log(`正在调用API分析章节: ${novelId}/${currentChapterId}`);

                // 调用API进行实际分析
                fetch(`/api/novel/${novelId}/chapter/${currentChapterId}/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        dimensions: [] // 空数组表示使用所有维度
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('章节分析已开始:', data);
                        // 开始轮询分析进度
                        startProgressPolling(novelId, currentChapterId);
                    } else {
                        console.error('章节分析启动失败:', data.error);
                        alert('分析启动失败: ' + data.error);
                        bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
                    }
                })
                .catch(error => {
                    console.error('调用分析API时出错:', error);
                    alert('调用分析API时出错: ' + error);
                    bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
                });
            }
        });

        // 直接分析章节
        document.querySelectorAll('.analyze-chapter').forEach(button => {
            button.addEventListener('click', function() {
                const chapterId = this.getAttribute('data-chapter-id');
                currentChapterId = chapterId;

                // 显示分析进度模态框
                const analysisProgressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
                analysisProgressModal.show();

                // 更新进度信息
                const chapterTitle = document.querySelector(`#heading${chapterId} .chapter-title`).textContent;
                document.getElementById('currentChapter').textContent = chapterTitle;
                document.getElementById('currentDimension').textContent = '全部维度';

                // 获取小说ID
                const novelId = {{ novel.id }};

                console.log(`点击"分析此章节"按钮，章节ID: ${chapterId}`);

                // 调用API进行实际分析
                fetch(`/api/novel/${novelId}/chapter/${chapterId}/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        dimensions: [] // 空数组表示使用所有维度
                    })
                })
                .then(response => {
                    console.log('API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        console.log('章节分析已开始:', data);
                        // 开始轮询分析进度
                        startProgressPolling(novelId, chapterId);
                    } else {
                        console.error('章节分析启动失败:', data.error);
                        alert('分析启动失败: ' + data.error);
                        bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
                    }
                })
                .catch(error => {
                    console.error('调用分析API时出错:', error);
                    alert('调用分析API时出错: ' + error);
                    bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
                });
            });
        });

        // 轮询分析进度
        function startProgressPolling(novelId, chapterId) {
            console.log(`开始轮询章节分析进度: ${novelId}/${chapterId}`);

            const progressBar = document.getElementById('dimensionProgress');
            const progressPercentage = document.getElementById('progressPercentage');
            const estimatedTime = document.getElementById('estimatedTime');

            // 创建一个API轮询函数
            let isCompleted = false;

            const pollProgress = () => {
                // 调用真实的进度API
                fetch(`/api/novel/${novelId}/chapter/${chapterId}/progress`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            console.log('进度数据:', data);

                            const progress = data.progress;
                            progressBar.style.width = `${progress}%`;
                            progressPercentage.textContent = `${progress}%`;

                            // 更新预计剩余时间
                            if (data.estimated_time) {
                                estimatedTime.textContent = `预计剩余时间: ${data.estimated_time}`;
                            }

                            // 检查是否完成
                            if (data.status === 'completed' || progress >= 100) {
                                isCompleted = true;
                                console.log('分析完成');

                                // 分析完成后关闭模态框
                                setTimeout(() => {
                                    bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
                                    alert('分析完成！请刷新页面查看结果。');
                                    // 刷新页面以显示最新结果
                                    window.location.reload();
                                }, 1000);

                                return;
                            }

                            // 如果未完成，继续轮询
                            if (!isCompleted) {
                                setTimeout(pollProgress, 3000); // 每3秒轮询一次
                            }
                        } else {
                            console.error('获取进度信息失败:', data.error);
                            alert(`获取进度信息失败: ${data.error}`);

                            // 即使出错，也继续轮询
                            if (!isCompleted) {
                                setTimeout(pollProgress, 5000); // 出错后5秒再试
                            }
                        }
                    })
                    .catch(error => {
                        console.error('轮询进度时出错:', error);

                        // 即使出错，也继续轮询
                        if (!isCompleted) {
                            setTimeout(pollProgress, 5000); // 出错后5秒再试
                        }
                    });
            };

            // 开始轮询
            pollProgress();
        }

        // 分析所有章节
        document.getElementById('analyzeAllChapters').addEventListener('click', function() {
            if (confirm('确定要分析所有章节吗？这可能需要较长时间。')) {
                // 显示分析进度模态框
                const analysisProgressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
                analysisProgressModal.show();

                // 更新进度信息
                document.getElementById('currentChapter').textContent = '所有章节';
                document.getElementById('currentDimension').textContent = '全部维度';

                // 获取小说ID和所有章节ID
                const novelId = {{ novel.id }};
                const chapterButtons = document.querySelectorAll('.analyze-chapter');
                const totalChapters = chapterButtons.length;
                let completedChapters = 0;

                // 逐个分析章节
                chapterButtons.forEach((button, index) => {
                    const chapterId = button.getAttribute('data-chapter-id');

                    // 延迟启动每个章节的分析，避免同时发送太多请求
                    setTimeout(() => {
                        // 调用API进行实际分析
                        fetch(`/api/novel/${novelId}/chapter/${chapterId}/analyze`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                dimensions: [] // 空数组表示使用所有维度
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                console.log(`章节 ${chapterId} 分析已开始:`, data);
                                completedChapters++;

                                // 更新进度
                                const progress = Math.round((completedChapters / totalChapters) * 100);
                                document.getElementById('dimensionProgress').style.width = `${progress}%`;
                                document.getElementById('progressPercentage').textContent = `${progress}%`;

                                // 如果所有章节都已启动分析，显示完成消息
                                if (completedChapters >= totalChapters) {
                                    setTimeout(() => {
                                        bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
                                        alert('所有章节的分析已启动！请稍后刷新页面查看结果。');
                                    }, 1000);
                                }
                            } else {
                                console.error(`章节 ${chapterId} 分析启动失败:`, data.error);
                            }
                        })
                        .catch(error => {
                            console.error(`调用章节 ${chapterId} 分析API时出错:`, error);
                            completedChapters++;
                        });
                    }, index * 500); // 每500毫秒启动一个章节的分析
                });
            }
        });

        // 模拟分析进度 - 已弃用，使用startProgressPolling代替
        function simulateAnalysisProgress() {
            console.warn('警告: 使用了已弃用的simulateAnalysisProgress函数，应该使用startProgressPolling代替');

            // 获取小说ID和章节ID
            const novelId = {{ novel.id }};
            const chapterId = currentChapterId;

            if (!chapterId) {
                console.error('无法获取当前章节ID');
                return;
            }

            // 调用API进行实际分析
            fetch(`/api/novel/${novelId}/chapter/${chapterId}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    dimensions: [] // 空数组表示使用所有维度
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('章节分析已开始:', data);
                    // 开始轮询分析进度
                    startProgressPolling(novelId, chapterId);
                } else {
                    console.error('章节分析启动失败:', data.error);
                    alert('分析启动失败: ' + data.error);
                    bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
                }
            })
            .catch(error => {
                console.error('调用分析API时出错:', error);
                alert('调用分析API时出错: ' + error);
                bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
            });
        }

        // 取消分析
        document.getElementById('cancelAnalysisBtn').addEventListener('click', function() {
            if (confirm('确定要取消当前分析吗？')) {
                bootstrap.Modal.getInstance(document.getElementById('analysisProgressModal')).hide();
            }
        });

        // 注释：原有的维度选择处理代码已被移除
        // 现在使用dimension-dropdown-fix.js脚本处理维度选择
        // 该脚本会自动处理维度选择和分析请求
        console.log('维度选择处理将由dimension-dropdown-fix.js脚本处理');

        // 导出全局函数，供dimension-dropdown-fix.js调用
        window.chapterAnalysisHelpers = {
            // 获取维度名称
            getDimensionName: function(dimension) {
                return getDimensionName(dimension);
            },

            // 显示分析进度模态框
            showProgressModal: function(chapterId, dimension) {
                const analysisProgressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
                analysisProgressModal.show();

                // 更新进度信息
                const chapterTitle = document.querySelector(`#heading${chapterId} .chapter-title`).textContent;
                document.getElementById('currentChapter').textContent = chapterTitle;
                document.getElementById('currentDimension').textContent = dimension ? getDimensionName(dimension) : '全部维度';

                return analysisProgressModal;
            },

            // 开始轮询分析进度
            startProgressPolling: function(novelId, chapterId) {
                startProgressPolling(novelId, chapterId);
            }
        };

        // 获取维度名称
        function getDimensionName(dimension) {
            const dimensionNames = {
                'language_style': '语言风格',
                'rhythm_pacing': '节奏与节奏',
                'structure': '结构分析',
                'sentence_variation': '句式变化',
                'paragraph_length': '段落长度',
                'perspective_shifts': '视角转换',
                'paragraph_flow': '段落流畅度',
                'novel_characteristics': '小说特点',
                'world_building': '世界构建',
                'character_relationships': '人物关系',
                'opening_effectiveness': '开篇效果',
                'climax_pacing': '高潮节奏'
            };

            return dimensionNames[dimension] || dimension;
        }
    });
</script>
{% endblock %}

{% block extra_css %}
<style>
    .chapter-number {
        min-width: 2.5rem;
        text-align: center;
    }

    .chapter-content {
        white-space: pre-line;
        font-size: 1rem;
        line-height: 1.7;
        max-height: 300px;
        overflow-y: auto;
    }

    .chapter-full-content {
        white-space: pre-line;
        font-size: 1.1rem;
        line-height: 1.8;
    }

    .accordion-button:not(.collapsed) {
        background-color: rgba(230, 180, 34, 0.1);
        color: var(--primary-dark);
    }

    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(230, 180, 34, 0.25);
    }
</style>
{% endblock %}
