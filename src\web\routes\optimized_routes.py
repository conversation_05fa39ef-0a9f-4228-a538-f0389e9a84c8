from flask import Blueprint, render_template, request, abort
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.db.connection import Session
import json
import logging

optimized_bp = Blueprint('optimized', __name__, url_prefix='/optimized')
logger = logging.getLogger(__name__)

@optimized_bp.route('/character_relationships/<int:novel_id>')
def character_relationships(novel_id):
    """优化版人物关系分析页面"""
    session = Session()
    try:
        # 获取小说信息
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            session.close()
            abort(404)
        
        # 获取分析结果
        analysis_result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id, 
            dimension='character_relationships'
        ).first()
        
        # 如果有分析结果，解析元数据
        if analysis_result and analysis_result.metadata:
            try:
                metadata = json.loads(analysis_result.metadata)
                analysis_result.metadata = metadata
            except json.JSONDecodeError:
                logger.error(f"解析元数据时出错: {analysis_result.metadata}")
                analysis_result.metadata = {}
        
        # 渲染优化版模板
        return render_template(
            'character_relationships_optimized.html',
            novel=novel,
            analysis_result=analysis_result
        )
    except Exception as e:
        logger.error(f"加载优化版人物关系分析页面时出错: {str(e)}", exc_info=True)
        # 返回错误页面
        return render_template(
            'error.html',
            error_message=f"加载页面时出错: {str(e)}"
        )
    finally:
        session.close()
