* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    --primary-color: #4a90e2;
    --secondary-color: #50c878;
    --background-color: #f4f7f6;
    --text-color: #333;
    --card-background: #ffffff;
    --border-radius: 12px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

.container {
    display: flex;
    gap: 30px;
    max-width: 1000px;
    width: 100%;
}

.editor-reset-section {
    flex: 1;
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    padding: 30px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.editor-reset-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

h1 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.5em;
    text-align: center;
    position: relative;
    padding-bottom: 10px;
}

h1::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

button {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 20px;
    box-shadow: 0 4px 10px rgba(74, 144, 226, 0.3);
}

button:hover {
    background-color: #3a7bd5;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(74, 144, 226, 0.4);
}

button:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.status {
    text-align: center;
    min-height: 30px;
    margin: 15px 0;
    font-weight: 500;
}

.result-container {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    border: 1px solid #e9e9e9;
}

.result-container div {
    margin-bottom: 10px;
    word-break: break-all;
}

.result-container strong {
    color: var(--text-color);
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.result-container span {
    color: var(--primary-color);
    font-size: 0.9em;
}

.result-container button {
    width: auto;
    margin-top: 10px;
    background-color: var(--secondary-color);
    box-shadow: 0 4px 10px rgba(80, 200, 120, 0.3);
}

.result-container button:hover {
    background-color: #3cb371;
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;
        gap: 20px;
    }
}
