{% extends "v3.1/base.html" %}

{% block title %}{{ dimension_info.name }} 分析结果 - {{ novel.title }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .dimension-header {
        background: linear-gradient(135deg, #FFF8E1, #FFFCF5);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
        border: 1px solid #E6D7B9;
    }

    .dimension-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .dimension-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        color: #5D4037;
    }

    .dimension-header p {
        position: relative;
        z-index: 1;
        color: #5D4037;
    }

    .dimension-icon-large {
        font-size: 3rem;
        color: #FF8F00;
        margin-bottom: 1rem;
    }

    .analysis-card {
        border: 1px solid #E6D7B9;
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .analysis-card .card-header {
        background-color: #FFF8E1;
        border-bottom: 1px solid #E6D7B9;
        padding: 1rem;
    }

    .analysis-content {
        padding: 1.5rem;
    }

    .reasoning-content {
        padding: 1.5rem;
        background-color: #FFFCF5;
        border-top: 1px solid #E6D7B9;
        max-height: 500px;
        overflow-y: auto;
    }

    .btn-primary {
        background-color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-primary:hover {
        background-color: #F57C00;
        border-color: #F57C00;
    }

    .btn-outline-primary {
        color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-outline-primary:hover {
        background-color: #FF8F00;
        border-color: #FF8F00;
        color: white;
    }

    .badge.bg-primary {
        background-color: #FF8F00 !important;
    }

    .badge.bg-secondary {
        background-color: #757575 !important;
    }

    .badge.bg-success {
        background-color: #43A047 !important;
    }

    .badge.bg-warning {
        background-color: #FFB300 !important;
    }

    .badge.bg-info {
        background-color: #29B6F6 !important;
    }

    .analysis-meta {
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #E6D7B9;
    }

    .analysis-meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .analysis-meta-item i {
        width: 24px;
        margin-right: 0.5rem;
        color: #FF8F00;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 1.5rem;
    }

    .toggle-reasoning-btn {
        cursor: pointer;
        padding: 0.5rem 1rem;
        background-color: #FFF8E1;
        border: 1px solid #E6D7B9;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .toggle-reasoning-btn:hover {
        background-color: #FFE0B2;
    }

    .reasoning-section {
        display: none;
    }

    .reasoning-section.show {
        display: block;
    }

    .reasoning-content {
        white-space: pre-wrap;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        line-height: 1.8;
    }

    .analysis-result {
        white-space: pre-wrap;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        line-height: 1.8;
    }
</style>
{% endblock %}

{% block content %}
<!-- 维度头部信息 -->
<div class="dimension-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">{{ dimension_info.name }} 分析结果</h1>
            <p class="lead mb-2">
                <i class="fas fa-book me-2"></i>{{ novel.title }}
                {% if novel.author %}
                <span class="ms-2"><i class="fas fa-user me-1"></i>{{ novel.author }}</span>
                {% endif %}
            </p>
            <p class="mb-0">
                <span class="badge bg-primary me-2">整本书分析</span>
                <span class="badge bg-info me-2">{{ novel.word_count }} 字</span>
                <span class="badge bg-success me-2">分析完成</span>
            </p>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3_1.view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回小说
                </a>
                <a href="{{ url_for('v3_1.analyze_dimension', novel_id=novel.id, dimension=dimension, force=1) }}" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-1"></i>重新分析
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-md-4 mb-4">
        <!-- 维度信息 -->
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>维度信息</h3>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <i class="{{ dimension_info.icon }} dimension-icon-large"></i>
                    <h3>{{ dimension_info.name }}</h3>
                </div>

                <div class="analysis-meta">
                    <div class="analysis-meta-item">
                        <i class="fas fa-book"></i>
                        <span>小说: {{ novel.title }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-chart-pie"></i>
                        <span>维度: {{ dimension_info.name }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-font"></i>
                        <span>字数: {{ novel.word_count }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>分析时间: {{ analysis.created_at.strftime('%Y-%m-%d %H:%M') if analysis.created_at is not string else analysis.created_at }}</span>
                    </div>
                </div>

                <p class="card-text">{{ dimension_info.description }}</p>

                <div class="d-grid gap-2 mt-3">
                    <a href="{{ url_for('v3_1.view_templates', novel_id=novel.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-cog me-1"></i>查看设定模板
                    </a>
                </div>
            </div>
        </div>

        <!-- 其他维度 -->
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>其他维度</h3>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for dim in dimensions %}
                        {% if dim.key != dimension %}
                            <a href="{{ url_for('v3_1.analysis_result', novel_id=novel.id, dimension=dim.key) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="{{ dim.icon }} me-2"></i>{{ dim.name }}</span>
                                {% if dim.key in novel.analyzed_dimensions|default([]) %}
                                <span class="badge bg-success rounded-pill">已分析</span>
                                {% else %}
                                <span class="badge bg-secondary rounded-pill">未分析</span>
                                {% endif %}
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- 分析结果 -->
        <div class="analysis-card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i>分析结果</h3>
                <button class="btn btn-sm btn-outline-primary" id="copyResultBtn">
                    <i class="fas fa-copy me-1"></i>复制结果
                </button>
            </div>
            <div class="analysis-content">
                {% if analysis and analysis.content %}
                    <div class="analysis-result" data-novel-id="{{ novel.id }}" data-dimension="{{ dimension }}">{{ analysis.content }}</div>
                {% else %}
                    <div class="text-center py-5" data-novel-id="{{ novel.id }}" data-dimension="{{ dimension }}">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h4>暂无分析结果</h4>
                        <p class="text-muted">该维度尚未分析或分析结果不可用。</p>
                        <button class="btn btn-primary mt-3 load-analysis-btn">
                            <i class="fas fa-sync-alt me-1"></i>加载分析结果
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 分析过程 -->
        <div class="analysis-card shadow-sm">
            <div class="toggle-reasoning-btn" id="toggleReasoningBtn">
                <h3 class="card-title mb-0"><i class="fas fa-brain me-2"></i>分析过程</h3>
                <i class="fas fa-chevron-down" id="reasoningIcon"></i>
            </div>
            <div class="reasoning-section" id="reasoningSection">
                <div class="reasoning-content">
                    {% if analysis and analysis.reasoning_content %}
                        {{ analysis.reasoning_content }}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-brain fa-3x text-muted mb-3"></i>
                            <h4>暂无分析过程</h4>
                            <p class="text-muted">该维度的分析过程不可用。</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 切换分析过程显示/隐藏
        $('#toggleReasoningBtn').click(function() {
            $('#reasoningSection').toggleClass('show');
            const icon = $('#reasoningIcon');
            if (icon.hasClass('fa-chevron-down')) {
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            } else {
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            }
        });

        // 复制分析结果
        $('#copyResultBtn').click(function() {
            const resultText = $('.analysis-result').text();
            copyToClipboard(resultText);

            // 显示复制成功提示
            const originalText = $(this).html();
            $(this).html('<i class="fas fa-check me-1"></i>已复制');

            // 2秒后恢复原始文本
            setTimeout(() => {
                $(this).html(originalText);
            }, 2000);
        });

        // 复制到剪贴板函数
        function copyToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }

        // 手动加载分析结果按钮点击事件
        $('.load-analysis-btn').click(function() {
            const container = $(this).closest('[data-novel-id]');
            const novelId = container.data('novel-id');
            const dimension = container.data('dimension');

            if (!novelId || !dimension) {
                console.error('缺少必要的小说ID或维度信息');
                return;
            }

            // 显示加载中状态
            $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>加载中...');
            $(this).prop('disabled', true);

            // 构建API URL - 使用v3.1路径
            const apiUrl = `/v3.1/api/novel/${novelId}/analysis/${dimension}?_=${Date.now()}`;

            // 发送请求
            $.ajax({
                url: apiUrl,
                type: 'GET',
                success: function(data) {
                    if (data.success === false) {
                        console.error('获取分析结果失败:', data.error);
                        return;
                    }

                    // 提取结果内容
                    let content = '';
                    if (data.result && data.result.content) {
                        content = data.result.content;
                    } else if (data.content) {
                        content = data.content;
                    } else if (data.analysis && data.analysis.content) {
                        content = data.analysis.content;
                    }

                    if (content) {
                        // 替换整个容器内容
                        container.parent().html(`<div class="analysis-result" data-novel-id="${novelId}" data-dimension="${dimension}">${content}</div>`);
                    } else {
                        // 更新按钮状态
                        $('.load-analysis-btn').html('<i class="fas fa-exclamation-circle me-1"></i>加载失败');
                        $('.load-analysis-btn').prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('请求分析结果出错:', error);
                    // 更新按钮状态
                    $('.load-analysis-btn').html('<i class="fas fa-exclamation-circle me-1"></i>加载失败');
                    $('.load-analysis-btn').prop('disabled', false);
                }
            });
        });
    });
</script>
{% endblock %}
