@echo off
echo 九猫小说分析系统 - 一键优化版
echo 此脚本会自动执行优化步骤并启动大型文本处理版

:: 设置临时目录环境变量
set "SCRIPT_DIR=%~dp0"
set "TEMP_DIR=%SCRIPT_DIR%temp"

:: 确保临时目录存在
if not exist "%TEMP_DIR%" (
    echo 创建临时目录: %TEMP_DIR%
    mkdir "%TEMP_DIR%"
)

:: 设置环境变量
set "TEMP=%TEMP_DIR%"
set "TMP=%TEMP_DIR%"

echo 临时文件已重定向到: %TEMP_DIR%
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python未安装或未添加到PATH，请安装Python
    pause
    exit /b 1
)

:: 检查优化脚本是否存在
if not exist "optimize_nine_cats.py" (
    echo 优化脚本不存在，跳过优化步骤
) else (
    echo 检查系统优化状态...
    python -c "import os; print('优化模块已安装' if os.path.exists('temp_redirect.py') and os.path.exists('memory_optimizer.py') else '需要安装优化模块')"
    
    echo 自动运行系统优化脚本...
    python optimize_nine_cats.py
)

:: 安装psutil（用于内存监控）
python -c "import psutil" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 安装psutil（用于内存监控）...
    pip install psutil tqdm memory-profiler
)

echo.
echo 优化完成，即将启动大型文本处理版...
echo.

:: 启动大型文本处理版
if exist "启动九猫_大型文本处理版.vbs" (
    echo 正在启动九猫小说分析系统（大型文本处理版）...
    start "" "启动九猫_大型文本处理版.vbs"
) else (
    echo 找不到大型文本处理版脚本，将使用优化版启动
    call 启动九猫_优化版.bat
) 