/**
 * Favicon错误处理脚本
 * 用于处理favicon.ico 404错误
 */

(function() {
    console.log('Favicon错误处理脚本已加载');
    
    // 监听所有资源加载错误
    window.addEventListener('error', function(event) {
        // 检查是否是favicon.ico加载错误
        if (event.target && event.target.tagName === 'LINK' && 
            event.target.rel === 'icon' && 
            event.target.href && 
            event.target.href.includes('favicon.ico')) {
            
            console.log('检测到favicon.ico加载错误，尝试创建动态favicon');
            
            // 创建一个canvas元素
            const canvas = document.createElement('canvas');
            canvas.width = 32;
            canvas.height = 32;
            const ctx = canvas.getContext('2d');
            
            // 设置背景色为深蓝色
            ctx.fillStyle = '#19376D';
            ctx.fillRect(0, 0, 32, 32);
            
            // 绘制文字"九"
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 24px Arial, "Microsoft YaHei", Sim<PERSON>ei, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('九', 16, 16);
            
            // 将canvas转换为favicon
            const link = document.createElement('link');
            link.rel = 'icon';
            link.href = canvas.toDataURL('image/png');
            document.head.appendChild(link);
            
            // 阻止错误继续传播
            event.preventDefault();
            return true;
        }
    }, true);
    
    // 主动创建favicon
    function createFavicon() {
        // 检查是否已经有favicon
        const existingFavicon = document.querySelector('link[rel="icon"]');
        if (existingFavicon) {
            console.log('已存在favicon，不需要创建');
            return;
        }
        
        console.log('主动创建favicon');
        
        // 创建一个canvas元素
        const canvas = document.createElement('canvas');
        canvas.width = 32;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');
        
        // 设置背景色为深蓝色
        ctx.fillStyle = '#19376D';
        ctx.fillRect(0, 0, 32, 32);
        
        // 绘制文字"九"
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 24px Arial, "Microsoft YaHei", SimHei, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('九', 16, 16);
        
        // 将canvas转换为favicon
        const link = document.createElement('link');
        link.rel = 'icon';
        link.href = canvas.toDataURL('image/png');
        document.head.appendChild(link);
    }
    
    // 页面加载完成后主动创建favicon
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createFavicon);
    } else {
        createFavicon();
    }
})();
