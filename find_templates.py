import os
import sys

def find_files(directory, pattern):
    """查找指定目录下匹配模式的文件"""
    result = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if pattern in file:
                result.append(os.path.join(root, file))
    return result

def main():
    """主函数"""
    # 获取当前目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 查找模板文件
    console_templates = find_files(".", "console.html")
    print("\n找到的控制台模板文件:")
    for template in console_templates:
        print(template)
    
    # 查找所有模板目录
    template_dirs = []
    for root, dirs, files in os.walk("."):
        for dir in dirs:
            if dir == "templates" or "template" in dir.lower():
                template_dirs.append(os.path.join(root, dir))
    
    print("\n找到的模板目录:")
    for dir in template_dirs:
        print(dir)
    
    # 查找v3目录
    v3_dirs = []
    for root, dirs, files in os.walk("."):
        for dir in dirs:
            if dir == "v3" or "v3" in dir.lower():
                v3_dirs.append(os.path.join(root, dir))
    
    print("\n找到的v3目录:")
    for dir in v3_dirs:
        print(dir)

if __name__ == "__main__":
    main()
