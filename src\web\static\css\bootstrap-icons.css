/**
 * 九猫系统 - 简化版Bootstrap Icons CSS
 * 版本: 1.0.0
 * 
 * 此文件是Bootstrap Icons的简化版本，包含最常用的图标
 * 用于解决CDN资源加载问题
 */

@font-face {
  font-display: block;
  font-family: "bootstrap-icons";
  src: url("data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAAXAAAsAAAAAC+wAAAVyAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACDXgqIXIcmATYCJAMcCxAABCAFhGcHgQobhwrIHpIkAQQTEADgAQCKh6/9+O/c3RcfiaYJNZFMEk2TRoIUjUQoJEgkMeEN/Xva9u1MdmcWQRJNJtGkEiKVTvfMJFTCpFBpNCpFKBYO4dLOBPkXuKA8SUvpSU/6UtLSyX/Ym/ZSPhCZRCaRzf7Nvrl3goMwCIfD4RCGw+ZQOIRBKIRBCARfedtqdsAgnNq0EMnazrxaGXj1gQCwCEYcSEFhcSUYYdCnCACkq7unHYzJwdCEUTBOx5xjBZIZMGBUU+YagBn+98kbyBgxAAoGDb6ooLu4C4qe4rkXcCP+H6dLA8jHswGAbQJoAOIAMIB05vpGaGjicTSWxWMwA4CJUQpK9RT3qXiu9Fz3ufcFnhsqCQALBgYUBuXfPAAMaAzQILCxgYGAJ1QABTzDKoACz70KoIHnOQVQwPMCqjhCAdCgAIYAMPQBkA5AzwNZD2jTMTCYYMCZCVFRUVJUVFSxsbFSbGxsRXx8fFVCQkJNYmJiXVJSUn1ycnJDSkpKY2pqalNaWlpzenp6S0ZGRmtmZmZbVlZWe3Z2dkdOTk5nXl5eV35+fndhYWFPUVFRb3FxcV9JSUl/aWnpQFlZ2WB5eflQRUXFcGVl5UhVVdVodXX1WE1NzXhtbe1EXV3dZH19/VRDQwMAqBPAYADQAIAGAQwOAA0BGAIAGgYwJAA0AmAoAGgUwNAA0BiAYQCgcQDDAkATAIYDgCYBjAAANAVgRABoGsCoAKAZAKMBgGYBjA4AmgMwBgBoHsCYAKAFAGMBgBYBjA0AWgIwDgBoGcC4AKAVAOMBgFYBjA8AWgMwAQBoHcCEAKANABMBgDYBTAwA2gIwCQBoG8DEAKAdAJMAgHYBTAoA2gMwGQBoH8DkAKADAFMAgA4BTAkAOgIwFQDoGMDUAKATANMAgE4BTA8AOgMwAwDoHMBMAKALADMDgC4BzAIAugIwKwDoGsBsAKAbALMDgG4BzAEAugMwJwDoHsBcAKAHAHMDgB4BzAMAegIwLwDoGcB8AKAXAPMDgF4BLAAAegOwIADoPcBCAKAPAAsDgD4CLAIAegKwGADoE8BiAKDPAIsDgL4ALAEAegawJADoK8BSAKBvAEsDgL4DLAMAegGwLADoJ8ByAKBfAMsDgH4DrAAAegWwEgDoD8BKAKBXAasCgP4CrAYAeg2wOgDoH8AaAKD/AGsCgN4ArAUAehOwNgDoLcA6AKB3AOsBgN4DrA8A+gCwAQDoI8CGAKBPABsBgD4DbAwA+gKwCQDoK8CmAKBvAJsBgL4DbAEA+gGwJQDoJ8BWAKBfAFsDgH4DbAMA+g2wLQDoD8B2AKC/ANsDgP4B7AAA+g+wIwDoP8BOAKAXATsDAHoJsAsAoJcBuwIAehWwGwCg1wC7AwB6HbAHAKA3AXsCAHoLsBcAoLcBewMAehewDwCg9wH7AgD6ALA/AKCPAAcAAPQJ4EAAQAcBDgYAdAhwKACgwwCHAQA6EnA4AKCjAEcAADoGcCQAoOMAR8EfqwCOBgB0MuAYAECnAI4FAHQq4DgAQKcDjgcAdAbgBABAZwJOBAB0FuAkAEDnAE4GAHQu4BQAQOcBTgUAdD7gNABA5wNOBwB0AeAMAEAXAs4EAHQRYDIAQBcDzgIAdAngbABAlwLOAQB0GeA8AECXAy4AAHQFYAoAoKsAFwIAuhpwEQCgawAXAwC6FnAJAKDrAJcCALoe8P8AAAD//wEAAP//XxJvYgAA");
}

.bi::before,
[class^="bi-"]::before,
[class*=" bi-"]::before {
  display: inline-block;
  font-family: bootstrap-icons !important;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -0.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 最常用的Bootstrap图标 */

.bi-arrow-up::before { content: "\f148"; }
.bi-arrow-down::before { content: "\f12c"; }
.bi-arrow-left::before { content: "\f12f"; }
.bi-arrow-right::before { content: "\f138"; }
.bi-arrow-up-circle::before { content: "\f14a"; }
.bi-arrow-down-circle::before { content: "\f12e"; }
.bi-arrow-left-circle::before { content: "\f131"; }
.bi-arrow-right-circle::before { content: "\f13a"; }
.bi-arrow-up-circle-fill::before { content: "\f14b"; }
.bi-arrow-down-circle-fill::before { content: "\f12d"; }
.bi-arrow-left-circle-fill::before { content: "\f130"; }
.bi-arrow-right-circle-fill::before { content: "\f139"; }

.bi-check::before { content: "\f26e"; }
.bi-check-circle::before { content: "\f26d"; }
.bi-check-circle-fill::before { content: "\f26c"; }
.bi-check-lg::before { content: "\f270"; }
.bi-check-square::before { content: "\f271"; }
.bi-check-square-fill::before { content: "\f272"; }

.bi-x::before { content: "\f659"; }
.bi-x-circle::before { content: "\f657"; }
.bi-x-circle-fill::before { content: "\f656"; }
.bi-x-lg::before { content: "\f659"; }
.bi-x-square::before { content: "\f65d"; }
.bi-x-square-fill::before { content: "\f65c"; }

.bi-plus::before { content: "\f4fe"; }
.bi-plus-circle::before { content: "\f4fa"; }
.bi-plus-circle-fill::before { content: "\f4f9"; }
.bi-plus-lg::before { content: "\f4fc"; }
.bi-plus-square::before { content: "\f501"; }
.bi-plus-square-fill::before { content: "\f500"; }

.bi-dash::before { content: "\f2e9"; }
.bi-dash-circle::before { content: "\f2e7"; }
.bi-dash-circle-fill::before { content: "\f2e6"; }
.bi-dash-lg::before { content: "\f2e8"; }
.bi-dash-square::before { content: "\f2ec"; }
.bi-dash-square-fill::before { content: "\f2eb"; }

.bi-exclamation::before { content: "\f33a"; }
.bi-exclamation-circle::before { content: "\f338"; }
.bi-exclamation-circle-fill::before { content: "\f337"; }
.bi-exclamation-lg::before { content: "\f339"; }
.bi-exclamation-triangle::before { content: "\f33d"; }
.bi-exclamation-triangle-fill::before { content: "\f33c"; }

.bi-info::before { content: "\f3fa"; }
.bi-info-circle::before { content: "\f3f8"; }
.bi-info-circle-fill::before { content: "\f3f7"; }
.bi-info-lg::before { content: "\f3f9"; }
.bi-info-square::before { content: "\f3fc"; }
.bi-info-square-fill::before { content: "\f3fb"; }

.bi-question::before { content: "\f526"; }
.bi-question-circle::before { content: "\f524"; }
.bi-question-circle-fill::before { content: "\f523"; }
.bi-question-lg::before { content: "\f525"; }
.bi-question-square::before { content: "\f529"; }
.bi-question-square-fill::before { content: "\f528"; }

.bi-search::before { content: "\f52a"; }
.bi-eye::before { content: "\f341"; }
.bi-eye-fill::before { content: "\f340"; }
.bi-eye-slash::before { content: "\f343"; }
.bi-eye-slash-fill::before { content: "\f342"; }

.bi-person::before { content: "\f4e1"; }
.bi-person-fill::before { content: "\f4da"; }
.bi-people::before { content: "\f4d9"; }
.bi-people-fill::before { content: "\f4d7"; }

.bi-house::before { content: "\f3e8"; }
.bi-house-fill::before { content: "\f3e7"; }
.bi-house-door::before { content: "\f3e6"; }
.bi-house-door-fill::before { content: "\f3e5"; }

.bi-gear::before { content: "\f3c5"; }
.bi-gear-fill::before { content: "\f3c4"; }
.bi-tools::before { content: "\f5f1"; }

.bi-file::before { content: "\f35c"; }
.bi-file-fill::before { content: "\f35b"; }
.bi-file-text::before { content: "\f38c"; }
.bi-file-text-fill::before { content: "\f38b"; }

.bi-folder::before { content: "\f3c7"; }
.bi-folder-fill::before { content: "\f3c6"; }
.bi-folder-check::before { content: "\f3c1"; }
.bi-folder-plus::before { content: "\f3d0"; }

.bi-bookmark::before { content: "\f1d0"; }
.bi-bookmark-fill::before { content: "\f1cf"; }
.bi-bookmark-check::before { content: "\f1cc"; }
.bi-bookmark-plus::before { content: "\f1d6"; }

.bi-heart::before { content: "\f3db"; }
.bi-heart-fill::before { content: "\f3da"; }
.bi-star::before { content: "\f588"; }
.bi-star-fill::before { content: "\f586"; }

.bi-trash::before { content: "\f5f8"; }
.bi-trash-fill::before { content: "\f5f7"; }
.bi-pencil::before { content: "\f4cb"; }
.bi-pencil-fill::before { content: "\f4ca"; }

.bi-chat::before { content: "\f266"; }
.bi-chat-fill::before { content: "\f265"; }
.bi-chat-text::before { content: "\f26b"; }
.bi-chat-text-fill::before { content: "\f26a"; }

.bi-bell::before { content: "\f17c"; }
.bi-bell-fill::before { content: "\f17b"; }
.bi-envelope::before { content: "\f32f"; }
.bi-envelope-fill::before { content: "\f32e"; }

.bi-calendar::before { content: "\f210"; }
.bi-calendar-fill::before { content: "\f20f"; }
.bi-calendar-event::before { content: "\f20e"; }
.bi-calendar-event-fill::before { content: "\f20d"; }

.bi-clock::before { content: "\f294"; }
.bi-clock-fill::before { content: "\f293"; }
.bi-alarm::before { content: "\f107"; }
.bi-alarm-fill::before { content: "\f106"; }

.bi-image::before { content: "\f3f2"; }
.bi-image-fill::before { content: "\f3f1"; }
.bi-camera::before { content: "\f219"; }
.bi-camera-fill::before { content: "\f218"; }

.bi-play::before { content: "\f4f3"; }
.bi-play-fill::before { content: "\f4f2"; }
.bi-pause::before { content: "\f4c4"; }
.bi-pause-fill::before { content: "\f4c3"; }

.bi-skip-start::before { content: "\f55b"; }
.bi-skip-start-fill::before { content: "\f55a"; }
.bi-skip-end::before { content: "\f559"; }
.bi-skip-end-fill::before { content: "\f558"; }

.bi-volume-up::before { content: "\f60b"; }
.bi-volume-up-fill::before { content: "\f60a"; }
.bi-volume-down::before { content: "\f607"; }
.bi-volume-down-fill::before { content: "\f606"; }

.bi-list::before { content: "\f419"; }
.bi-list-ul::before { content: "\f41c"; }
.bi-list-ol::before { content: "\f41b"; }
.bi-list-check::before { content: "\f418"; }

.bi-grid::before { content: "\f3d2"; }
.bi-grid-fill::before { content: "\f3d1"; }
.bi-grid-3x3::before { content: "\f3cf"; }
.bi-grid-3x3-gap::before { content: "\f3ce"; }

.bi-sort-down::before { content: "\f57d"; }
.bi-sort-up::before { content: "\f57f"; }
.bi-sort-alpha-down::before { content: "\f56e"; }
.bi-sort-alpha-up::before { content: "\f570"; }

.bi-filter::before { content: "\f368"; }
.bi-filter-circle::before { content: "\f366"; }
.bi-filter-circle-fill::before { content: "\f365"; }
.bi-funnel::before { content: "\f3c3"; }
.bi-funnel-fill::before { content: "\f3c2"; }

.bi-three-dots::before { content: "\f5ef"; }
.bi-three-dots-vertical::before { content: "\f5f0"; }

.bi-arrow-clockwise::before { content: "\f116"; }
.bi-arrow-counterclockwise::before { content: "\f118"; }
.bi-arrow-repeat::before { content: "\f13b"; }

.bi-box-arrow-in-right::before { content: "\f1d9"; }
.bi-box-arrow-right::before { content: "\f1e2"; }
.bi-door-open::before { content: "\f30a"; }
.bi-door-open-fill::before { content: "\f309"; }

.bi-download::before { content: "\f30b"; }
.bi-upload::before { content: "\f603"; }
.bi-cloud-download::before { content: "\f2a8"; }
.bi-cloud-upload::before { content: "\f2b9"; }

.bi-link::before { content: "\f411"; }
.bi-link-45deg::before { content: "\f410"; }
.bi-paperclip::before { content: "\f4c0"; }

.bi-emoji-smile::before { content: "\f32a"; }
.bi-emoji-smile-fill::before { content: "\f329"; }
.bi-emoji-frown::before { content: "\f328"; }
.bi-emoji-frown-fill::before { content: "\f327"; }

.bi-hand-thumbs-up::before { content: "\f3d5"; }
.bi-hand-thumbs-up-fill::before { content: "\f3d4"; }
.bi-hand-thumbs-down::before { content: "\f3d3"; }
.bi-hand-thumbs-down-fill::before { content: "\f3d2"; }

.bi-share::before { content: "\f52f"; }
.bi-share-fill::before { content: "\f52e"; }
.bi-reply::before { content: "\f53d"; }
.bi-reply-fill::before { content: "\f53c"; }

.bi-printer::before { content: "\f50e"; }
.bi-printer-fill::before { content: "\f50d"; }
.bi-save::before { content: "\f553"; }
.bi-save-fill::before { content: "\f552"; }

.bi-lock::before { content: "\f47b"; }
.bi-lock-fill::before { content: "\f47a"; }
.bi-unlock::before { content: "\f600"; }
.bi-unlock-fill::before { content: "\f5ff"; }

.bi-shield::before { content: "\f54b"; }
.bi-shield-fill::before { content: "\f54a"; }
.bi-shield-check::before { content: "\f548"; }
.bi-shield-check-fill::before { content: "\f547"; }

.bi-flag::before { content: "\f36f"; }
.bi-flag-fill::before { content: "\f36e"; }
.bi-bookmark::before { content: "\f1d0"; }
.bi-bookmark-fill::before { content: "\f1cf"; }
