"""
分析服务模块，处理小说分析任务
"""

import json
import logging
import time
import uuid
from threading import Thread
from src.db.connection import Session
from src.models.analysis_result import AnalysisResult
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.novel import Novel
from src.services.process_recording_patch import ProcessRecorder
import config
from src.config.serialization_bottleneck_optimizer import SerializationBottleneckOptimizer
from typing import Dict, Any, List
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logger = logging.getLogger(__name__)

# 存储任务状态的字典
tasks = {}

def start_analysis_task(novel_id, dimension):
    """
    启动分析任务

    Args:
        novel_id: 小说ID
        dimension: 分析维度

    Returns:
        task_id: 任务ID
    """
    # 生成任务ID
    task_id = str(uuid.uuid4())

    # 初始化任务状态
    tasks[task_id] = {
        'novel_id': novel_id,
        'dimension': dimension,
        'status': 'pending',
        'progress': 0,
        'message': '准备开始分析...',
        'result': None,
        'error': None
    }

    # 启动后台线程执行分析
    thread = Thread(target=run_analysis, args=(task_id, novel_id, dimension))
    thread.daemon = True
    thread.start()

    logger.info(f"启动分析任务: {task_id}, 小说ID: {novel_id}, 维度: {dimension}")
    return task_id

def get_task_progress(task_id):
    """
    获取任务进度

    Args:
        task_id: 任务ID

    Returns:
        progress_data: 进度数据
    """
    if task_id not in tasks:
        logger.warning(f"任务不存在: {task_id}")
        return {
            'status': 'not_found',
            'progress': 0,
            'message': '任务不存在'
        }

    task = tasks[task_id]

    # 构建进度数据
    progress_data = {
        'status': task['status'],
        'progress': task['progress'],
        'message': task['message']
    }

    # 如果任务已完成，清理任务数据
    if task['status'] in ['completed', 'failed']:
        # 保留任务状态一段时间，然后清理
        if 'cleanup_time' not in task:
            task['cleanup_time'] = time.time() + 300  # 5分钟后清理
        elif time.time() > task['cleanup_time']:
            logger.info(f"清理任务数据: {task_id}")
            del tasks[task_id]

    return progress_data

def cancel_analysis_task(task_id):
    """
    取消分析任务

    Args:
        task_id: 任务ID
    """
    if task_id not in tasks:
        logger.warning(f"任务不存在: {task_id}")
        return

    task = tasks[task_id]

    # 只能取消未完成的任务
    if task['status'] not in ['completed', 'failed']:
        task['status'] = 'cancelled'
        task['message'] = '任务已取消'
        logger.info(f"取消分析任务: {task_id}")

    # 标记清理时间
    task['cleanup_time'] = time.time() + 60  # 1分钟后清理

def run_analysis(task_id, novel_id, dimension):
    """
    执行分析任务

    Args:
        task_id: 任务ID
        novel_id: 小说ID
        dimension: 分析维度
    """
    task = tasks[task_id]
    start_time = time.time()

    try:
        # 更新任务状态
        task['status'] = 'in_progress'
        task['progress'] = 10
        task['message'] = '正在加载小说内容...'

        # 记录初始化阶段
        ProcessRecorder.record_init(novel_id, dimension, metadata={
            'start_time': start_time,
            'task_id': task_id
        })

        # 模拟分析过程
        time.sleep(2)

        # 检查是否取消
        if task['status'] == 'cancelled':
            logger.info(f"任务已取消: {task_id}")
            return

        task['progress'] = 30
        task['message'] = '正在分析文本...'

        # 记录分块分析阶段
        chunk_start_time = time.time()
        input_text = "模拟小说文本内容"
        output_text = "模拟分析结果"
        prompt = "分析小说的语言风格、节奏和结构特点"

        # 模拟分析过程
        time.sleep(3)
        chunk_end_time = time.time()
        chunk_processing_time = int((chunk_end_time - chunk_start_time) * 1000)  # 毫秒

        ProcessRecorder.record_chunk_analysis(
            novel_id=novel_id,
            dimension=dimension,
            block_index=0,
            total_blocks=1,
            input_text=input_text,
            output_text=output_text,
            prompt=prompt,
            processing_time=chunk_processing_time,
            tokens=1000
        )

        # 检查是否取消
        if task['status'] == 'cancelled':
            logger.info(f"任务已取消: {task_id}")
            return

        task['progress'] = 60
        task['message'] = '正在生成分析结果...'

        # 记录结果合并阶段
        combine_start_time = time.time()

        # 模拟分析过程
        time.sleep(2)
        combine_end_time = time.time()
        combine_processing_time = int((combine_end_time - combine_start_time) * 1000)  # 毫秒

        combined_result = "合并后的分析结果"

        ProcessRecorder.record_combine(
            novel_id=novel_id,
            dimension=dimension,
            input_chunks=[output_text],
            output_text=combined_result,
            processing_time=combine_processing_time
        )

        # 检查是否取消
        if task['status'] == 'cancelled':
            logger.info(f"任务已取消: {task_id}")
            return

        task['progress'] = 90
        task['message'] = '正在保存分析结果...'

        # 创建分析结果
        result = create_analysis_result(novel_id, dimension)

        # 记录最终处理阶段
        end_time = time.time()
        total_processing_time = int((end_time - start_time) * 1000)  # 毫秒

        ProcessRecorder.record_finalize(
            novel_id=novel_id,
            dimension=dimension,
            result_id=result.id if result else None,
            output_text=result.content if result else None,
            processing_time=total_processing_time
        )

        # 完成任务
        task['status'] = 'completed'
        task['progress'] = 100
        task['message'] = '分析完成'

        logger.info(f"分析任务完成: {task_id}")

    except Exception as e:
        logger.error(f"分析任务出错: {task_id}, 错误: {str(e)}")

        # 记录错误
        ProcessRecorder.record_error(
            novel_id=novel_id,
            dimension=dimension,
            stage='error_handling',
            error_message=str(e)
        )

        # 更新任务状态
        task['status'] = 'failed'
        task['progress'] = 0
        task['message'] = f'分析失败: {str(e)}'
        task['error'] = str(e)

def create_analysis_result(novel_id, dimension):
    """
    创建分析结果

    Args:
        novel_id: 小说ID
        dimension: 分析维度

    Returns:
        result: 分析结果对象
    """
    session = Session()
    result = None

    try:
        # 查询是否已存在分析结果
        existing_result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id,
            dimension=dimension
        ).first()

        if existing_result:
            logger.info(f"更新现有分析结果: 小说ID {novel_id}, 维度 {dimension}")

            # 更新现有结果
            existing_result.content = generate_sample_content(dimension)
            existing_result.metadata = generate_sample_metadata(dimension)

            session.commit()
            result = existing_result
        else:
            logger.info(f"创建新分析结果: 小说ID {novel_id}, 维度 {dimension}")

            # 创建新结果
            new_result = AnalysisResult(
                novel_id=novel_id,
                dimension=dimension,
                content=generate_sample_content(dimension),
                metadata=generate_sample_metadata(dimension)
            )

            session.add(new_result)
            session.commit()
            result = new_result

        logger.info(f"分析结果已保存: 小说ID {novel_id}, 维度 {dimension}")
        return result

    except Exception as e:
        logger.error(f"保存分析结果时出错: {str(e)}")
        session.rollback()
        raise

    finally:
        session.close()

def generate_sample_content(dimension):
    """
    生成示例分析内容

    Args:
        dimension: 分析维度

    Returns:
        content: 分析内容
    """
    content_templates = {
        'language_style': """# 语言风格分析

## 总体评价
该小说的语言风格简洁明了，用词准确，句式多样，具有较强的表现力和感染力。

## 词汇选择
作者使用了丰富的词汇，既有日常用语，也有专业术语，使得描述更加准确生动。

## 句式结构
句式变化丰富，有简短有力的短句，也有结构复杂的长句，节奏感强。

## 修辞手法
运用了比喻、拟人、排比等多种修辞手法，增强了文本的表现力。

## 语言特色
语言风格独特，具有鲜明的个人特色，能够有效地传达情感和思想。""",

        'rhythm_pacing': """# 节奏与节奏分析

## 总体评价
该小说的节奏控制得当，情节推进流畅，紧张与松弛交替，读者体验良好。

## 章节节奏
章节长度适中，内容安排合理，每章都有明确的主题和情节推进。

## 情节推进
情节发展有快有慢，关键时刻节奏加快，平缓处节奏放慢，形成对比。

## 紧张感营造
在关键情节处通过短句、对话和动作描写等手法加快节奏，营造紧张氛围。

## 过渡处理
场景转换和时间跨度的过渡处理自然，不突兀，保持了叙事的连贯性。""",

        'structure': """# 结构分析

## 总体评价
该小说结构完整，布局合理，情节发展有序，首尾呼应，整体性强。

## 开篇设置
开篇简洁有力，迅速引入主题，设置悬念，吸引读者继续阅读。

## 情节发展
情节发展符合逻辑，有起承转合，高潮设置恰当，结局处理得当。

## 伏笔安排
伏笔设置巧妙，为后续情节发展做了充分铺垫，增强了故事的完整性。

## 结构特点
采用了线性叙事结构，时间顺序清晰，情节发展连贯，易于理解。""",

        'character_relationships': """# 人物关系分析

## 总体评价
该小说中的人物关系复杂而真实，主要角色之间的互动丰富，关系发展自然。

## 主要人物关系
主角与其他角色的关系发展合理，有冲突也有和解，体现了人物性格的多面性。

## 次要人物关系
次要人物之间的关系也得到了适当描写，丰富了故事背景，增加了叙事层次。

## 关系变化
人物关系随着情节发展而变化，符合角色成长和故事发展的需要。

## 关系张力
人物之间的矛盾冲突营造了张力，推动了情节发展，增强了故事的吸引力。""",

        'world_building': """# 世界构建分析

## 总体评价
该小说的世界构建详实而有深度，背景设定丰富，规则系统合理，具有很强的沉浸感。

## 背景设定
世界背景设定详细，历史、地理、文化等方面都有所涉及，增强了故事的真实感。

## 规则系统
世界运行的规则系统清晰，逻辑自洽，没有明显漏洞，使读者容易理解和接受。

## 细节描写
细节描写丰富，从环境、服饰到风俗习惯都有所涉及，增强了世界的立体感。

## 世界特色
构建的世界具有独特性，有别于其他作品，给读者留下深刻印象。""",

        'novel_characteristics': """# 小说特点分析

## 总体评价
该小说具有鲜明的个人风格，主题深刻，情节引人入胜，人物形象丰满，语言表达独特。

## 主题特色
主题深刻而有现实意义，通过故事传达了作者的思想和价值观。

## 情节特色
情节设计巧妙，悬念设置得当，转折自然，结局令人满意。

## 人物特色
人物形象鲜明，性格特点突出，成长轨迹清晰，令人印象深刻。

## 语言特色
语言风格独特，表达方式新颖，修辞手法丰富，增强了作品的艺术性。""",

        'climax_pacing': """# 高潮节奏分析

## 总体评价
该小说的高潮设置恰当，铺垫充分，节奏控制得当，情感渲染到位，给读者留下深刻印象。

## 高潮铺垫
高潮前的铺垫充分，通过伏笔、暗示等手法为高潮做了充分准备。

## 节奏控制
高潮部分的节奏控制得当，通过句式、段落长度等变化增强了紧张感。

## 情感渲染
高潮部分的情感渲染到位，角色的情感变化真实，能引起读者共鸣。

## 高潮效果
高潮部分达到了预期效果，解决了主要矛盾，推动了情节发展，满足了读者期待。"""
    }

    # 返回对应维度的内容，如果没有则返回通用内容
    return content_templates.get(dimension, """# 分析结果

## 总体评价
该小说在这一维度表现良好，具有一定的艺术价值和阅读价值。

## 详细分析
通过对文本的分析，发现该小说在结构、语言、人物等方面都有独到之处。

## 优点
1. 结构完整，情节发展合理
2. 语言表达准确，风格独特
3. 人物形象鲜明，性格特点突出

## 不足
1. 部分情节发展略显牵强
2. 个别段落描写冗长
3. 次要人物刻画不够深入

## 建议
建议作者在修改时注意情节的合理性，精简冗长段落，加强次要人物的刻画。""")

def generate_sample_metadata(dimension):
    """
    生成示例元数据

    Args:
        dimension: 分析维度

    Returns:
        metadata_json: 元数据JSON字符串
    """
    import random

    # 基础元数据
    metadata = {
        "processing_time": random.uniform(30, 60),
        "chunk_count": random.randint(1, 5),
        "api_calls": random.randint(1, 10),
        "tokens_used": random.randint(0, 5000),
        "cost": random.uniform(0, 0.5)
    }

    # 添加可视化数据
    if dimension == 'language_style':
        metadata["visualization_data"] = {
            "radar": {
                "labels": ["词汇丰富度", "句式多样性", "修辞手法", "语言风格", "表达清晰度", "情感表达"],
                "data": [random.randint(60, 95) for _ in range(6)]
            }
        }
    elif dimension == 'rhythm_pacing':
        metadata["visualization_data"] = {
            "radar": {
                "labels": ["节奏变化", "情节推进", "紧张感营造", "松弛感处理", "高潮设置", "过渡处理"],
                "data": [random.randint(60, 95) for _ in range(6)]
            }
        }
    elif dimension == 'structure':
        metadata["visualization_data"] = {
            "radar": {
                "labels": ["结构完整性", "章节安排", "情节连贯性", "伏笔设置", "高潮处理", "结局处理"],
                "data": [random.randint(60, 95) for _ in range(6)]
            }
        }
    elif dimension == 'character_relationships':
        metadata["visualization_data"] = {
            "radar": {
                "labels": ["主要人物数", "次要人物数", "关系复杂度", "关系变化", "描写深度", "一致性"],
                "data": [random.randint(60, 95) for _ in range(6)]
            }
        }
    elif dimension == 'world_building':
        metadata["visualization_data"] = {
            "radar": {
                "labels": ["背景设定", "规则系统", "历史深度", "地理环境", "文化习俗", "细节描写"],
                "data": [random.randint(60, 95) for _ in range(6)]
            }
        }
    elif dimension == 'novel_characteristics':
        metadata["visualization_data"] = {
            "radar": {
                "labels": ["主题深度", "情节创新", "人物塑造", "语言特色", "结构设计", "艺术价值"],
                "data": [random.randint(60, 95) for _ in range(6)]
            }
        }
    elif dimension == 'climax_pacing':
        metadata["visualization_data"] = {
            "radar": {
                "labels": ["高潮铺垫", "紧张感营造", "情节转折", "角色反应", "节奏控制", "情感渲染"],
                "data": [random.randint(60, 95) for _ in range(6)]
            }
        }
    else:
        metadata["visualization_data"] = {
            "radar": {
                "labels": ["指标1", "指标2", "指标3", "指标4", "指标5", "指标6"],
                "data": [random.randint(60, 95) for _ in range(6)]
            }
        }

    return json.dumps(metadata)

def sync_novel_analysis_to_chapters(novel_id, dimension=None):
    """
    将整本书的分析结果同步到各个章节的分析结果中

    Args:
        novel_id: 小说ID
        dimension: 分析维度，如果为None则同步所有维度

    Returns:
        result: 同步结果，包含成功和失败信息
    """
    session = Session()
    try:
        # 获取小说的所有章节
        chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
        if not chapters:
            logger.warning(f"小说 {novel_id} 没有章节，无法同步分析结果")
            return {
                "success": False,
                "error": "小说没有章节，请先创建章节",
                "novel_id": novel_id
            }

        # 获取整本书的分析结果
        query = session.query(AnalysisResult).filter_by(novel_id=novel_id)
        if dimension:
            query = query.filter_by(dimension=dimension)

        novel_results = query.all()
        if not novel_results:
            logger.warning(f"小说 {novel_id} 没有分析结果，无法同步")
            return {
                "success": False,
                "error": "小说没有分析结果，请先进行整本书分析",
                "novel_id": novel_id
            }

        # 同步结果统计
        sync_stats = {
            "total_dimensions": len(novel_results),
            "total_chapters": len(chapters),
            "created_results": 0,
            "updated_results": 0,
            "skipped_results": 0,
            "dimensions": []
        }

        # 为每个维度同步结果
        for novel_result in novel_results:
            dimension = novel_result.dimension
            sync_stats["dimensions"].append(dimension)

            # 为每个章节创建或更新分析结果
            for chapter in chapters:
                # 检查章节是否已有该维度的分析结果
                existing_result = session.query(ChapterAnalysisResult).filter_by(
                    chapter_id=chapter.id,
                    novel_id=novel_id,
                    dimension=dimension
                ).first()

                # 准备分析内容
                content = f"""# {dimension.replace('_', ' ').title()} 分析

## 注意
此分析结果是基于整本书的分析自动生成的，可能不完全适用于本章节。
建议对本章节单独进行分析以获取更准确的结果。

## 章节信息
- 章节标题: {chapter.title or f'第{chapter.chapter_number}章'}
- 章节编号: {chapter.chapter_number}
- 字数: {chapter.word_count}

## 整本书分析结果
{novel_result.content}

"""

                # 准备元数据
                metadata = novel_result.analysis_metadata.copy() if novel_result.analysis_metadata else {}
                metadata["synced_from_novel_analysis"] = True
                metadata["sync_time"] = time.time()
                metadata["novel_result_id"] = novel_result.id
                metadata["is_chapter_specific"] = False  # 标记为非章节特定分析

                if existing_result:
                    # 如果已有结果是章节特定分析，则跳过
                    if existing_result.analysis_metadata and existing_result.analysis_metadata.get("is_chapter_specific", False):
                        sync_stats["skipped_results"] += 1
                        continue

                    # 更新现有结果
                    existing_result.content = content
                    existing_result.analysis_metadata = metadata
                    sync_stats["updated_results"] += 1
                else:
                    # 创建新结果
                    new_result = ChapterAnalysisResult(
                        chapter_id=chapter.id,
                        novel_id=novel_id,
                        dimension=dimension,
                        content=content,
                        metadata=metadata
                    )
                    session.add(new_result)
                    sync_stats["created_results"] += 1

                # 更新章节的is_analyzed标志
                chapter.is_analyzed = True

        # 提交事务
        session.commit()

        logger.info(f"成功同步小说 {novel_id} 的分析结果到 {len(chapters)} 个章节，共 {len(novel_results)} 个维度")
        return {
            "success": True,
            "message": f"成功同步分析结果到 {len(chapters)} 个章节",
            "stats": sync_stats
        }

    except Exception as e:
        logger.error(f"同步分析结果时出错: {str(e)}")
        session.rollback()
        return {
            "success": False,
            "error": f"同步分析结果时出错: {str(e)}",
            "novel_id": novel_id
        }
    finally:
        session.close()

def aggregate_chapter_analyses(novel_id, dimension=None):
    """
    将所有章节的分析结果汇总，创建一个整本书的汇总分析结果
    使用维度优先级配置，优先汇总章纲分析和大纲分析

    Args:
        novel_id: 小说ID
        dimension: 分析维度，如果为None则汇总所有维度，并按优先级顺序处理

    Returns:
        result: 汇总结果，包含成功和失败信息
    """
    session = Session()
    try:
        # 获取小说
        novel = session.query(Novel).filter_by(id=novel_id).first()
        if not novel:
            logger.warning(f"小说 {novel_id} 不存在，无法汇总分析结果")
            return {
                "success": False,
                "error": "小说不存在",
                "novel_id": novel_id
            }

        # 提前获取小说信息，避免会话关闭后访问
        novel_info = {
            "id": novel.id,
            "title": novel.title,
            "author": novel.author or '未知',
            "word_count": novel.word_count
        }

        # 获取小说的所有章节
        chapters = session.query(Chapter).filter_by(novel_id=novel_id).all()
        if not chapters:
            logger.warning(f"小说 {novel_id} 没有章节，无法汇总分析结果")
            return {
                "success": False,
                "error": "小说没有章节，请先创建章节",
                "novel_id": novel_id
            }

        # 提前获取章节信息，避免会话关闭后访问
        chapter_info = []
        for chapter in chapters:
            chapter_info.append({
                "id": chapter.id,
                "title": chapter.title,
                "chapter_number": chapter.chapter_number
            })

        # 获取所有章节的分析结果
        query = session.query(ChapterAnalysisResult).filter_by(novel_id=novel_id)
        if dimension:
            query = query.filter_by(dimension=dimension)

        # 按维度分组，并提前获取所有需要的数据
        chapter_results_by_dimension = {}
        for result in query.all():
            # 汇总所有章节分析结果，不再限制只汇总章节特定的分析结果
            if result.dimension not in chapter_results_by_dimension:
                chapter_results_by_dimension[result.dimension] = []

            # 获取章节信息
            chapter_data = None
            for ch in chapter_info:
                if ch["id"] == result.chapter_id:
                    chapter_data = ch
                    break

            if chapter_data:
                # 提前准备好分析结果数据
                result_data = {
                    "id": result.id,
                    "chapter_id": result.chapter_id,
                    "dimension": result.dimension,
                    "content": result.content,
                    "chapter_title": chapter_data["title"] or f'第{chapter_data["chapter_number"]}章',
                    "chapter_number": chapter_data["chapter_number"]
                }

                # 获取推理过程
                if hasattr(result, 'reasoning_content') and result.reasoning_content:
                    result_data["reasoning_content"] = result.reasoning_content
                elif result.analysis_metadata and 'reasoning_content' in result.analysis_metadata:
                    result_data["reasoning_content"] = result.analysis_metadata['reasoning_content']
                else:
                    result_data["reasoning_content"] = None

                chapter_results_by_dimension[result.dimension].append(result_data)

        if not chapter_results_by_dimension:
            logger.warning(f"小说 {novel_id} 没有章节分析结果，无法汇总")
            return {
                "success": False,
                "error": "没有找到章节分析结果，请先对章节进行分析",
                "novel_id": novel_id
            }

        # 汇总结果统计
        aggregate_stats = {
            "total_dimensions": len(chapter_results_by_dimension),
            "total_chapters": len(chapters),
            "created_results": 0,
            "updated_results": 0,
            "dimensions": list(chapter_results_by_dimension.keys())
        }

        # 获取维度信息
        dimension_info = {}
        for dim in config.ANALYSIS_DIMENSIONS:
            dimension_info[dim.get('key')] = dim.get('name', dim.get('key', '').replace('_', ' ').title())

        # 如果dimension为None，则汇总所有维度，并按优先级排序
        if dimension is None:
            try:
                # 导入维度优先级配置
                from dimension_priority import get_prioritized_dimensions

                # 对维度进行优先级排序
                dimensions_to_process = list(chapter_results_by_dimension.keys())
                prioritized_dimensions = get_prioritized_dimensions(dimensions_to_process)
                logger.info(f"维度优先级排序: 原始顺序={dimensions_to_process}, 排序后={prioritized_dimensions}")

                # 创建有序的字典，按优先级顺序处理维度
                ordered_chapter_results = {}
                for dim in prioritized_dimensions:
                    if dim in chapter_results_by_dimension:
                        ordered_chapter_results[dim] = chapter_results_by_dimension[dim]

                # 使用排序后的维度字典
                chapter_results_by_dimension = ordered_chapter_results
                logger.info(f"将按优先级顺序处理以下维度: {list(chapter_results_by_dimension.keys())}")
            except Exception as e:
                logger.warning(f"使用维度优先级排序时出错: {str(e)}，将使用原始维度顺序")
                # 如果导入失败，继续使用原始维度字典

        # 为每个维度汇总结果
        for dim_key, results in chapter_results_by_dimension.items():
            # 检查是否已有该维度的汇总结果
            existing_result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dim_key
            ).first()

            # 准备汇总内容
            chapter_contents = []
            chapter_reasoning_contents = []

            # 获取所有章节的分析结果和推理过程
            for result in results:
                # 添加分析结果
                chapter_contents.append(f"### {result['chapter_title']}\n\n{result['content']}")

                # 添加推理过程（如果有）
                if result.get("reasoning_content"):
                    chapter_reasoning_contents.append(f"### {result['chapter_title']} 推理过程\n\n{result['reasoning_content']}")

            # 获取维度名称
            dimension_name = dimension_info.get(dim_key, dim_key.replace('_', ' ').title())

            # 使用真实API进行汇总分析
            try:
                # 在调用API之前，先准备好所有需要的数据
                # 这样在会话关闭后就不需要访问数据库对象
                novel_title = novel_info['title']
                novel_author = novel_info['author']
                novel_word_count = novel_info['word_count']
                chapter_count = len(chapter_info)
                chapter_contents_text = '\n\n'.join(chapter_contents)

                # 关闭当前会话，避免在API调用期间保持会话打开
                session.commit()
                session.close()

                # 创建一个新的会话用于后续操作
                new_session = Session()

                from src.api.deepseek_client import DeepSeekClient

                # 准备汇总提示词
                summary_prompt = f"""请对以下小说《{novel_title}》的各章节"{dimension_name}"维度分析结果进行汇总分析。

小说信息:
- 标题: {novel_title}
- 作者: {novel_author}
- 总字数: {novel_word_count}
- 章节数: {chapter_count}

各章节分析结果:
{chapter_contents_text}

请提供一个全面的汇总分析，包括:
1. 整体趋势和模式
2. 各章节之间的对比和变化
3. 特别突出或需要改进的地方
4. 对整本书在这个维度上的总体评价

请确保分析深入、具体，并基于各章节的实际分析结果。"""

                # 调用API进行汇总分析
                client = DeepSeekClient(model="deepseek-r1")
                api_result = client.analyze_text(summary_prompt, f"chapter_summary_{dim_key}", novel_id=novel_info['id'])

                # 提取API返回的内容
                api_summary = api_result.get('content', '')

                # 如果API调用失败，使用简单的拼接方式
                if not api_summary:
                    api_summary = "API调用失败，无法生成智能汇总分析。以下是各章节分析结果的简单汇总。"

                # 重新使用新会话
                session = new_session

                # 构建最终的汇总内容
                aggregated_content = f"""# {dimension_name} 汇总分析

## 汇总说明
此分析结果是基于所有章节的分析结果，通过AI进行智能汇总生成的。

## 小说信息
- 标题: {novel_title}
- 作者: {novel_author}
- 总字数: {novel_word_count}
- 章节数: {chapter_count}

## 智能汇总分析
{api_summary}

## 各章节分析详情
{chapter_contents_text}

"""

                # 如果有推理过程，添加到汇总内容中
                chapter_reasoning_contents_text = '\n\n'.join(chapter_reasoning_contents)
                if chapter_reasoning_contents:
                    aggregated_content += f"""
## 各章节推理过程
{chapter_reasoning_contents_text}
"""

            except Exception as e:
                logger.error(f"调用API进行汇总分析时出错: {str(e)}")

                # 如果API调用失败，使用简单的拼接方式
                aggregated_content = f"""# {dimension_name} 汇总分析

## 汇总说明
此分析结果是基于所有章节的分析结果自动汇总生成的。

## 小说信息
- 标题: {novel_title}
- 作者: {novel_author}
- 总字数: {novel_word_count}
- 章节数: {chapter_count}

## 章节分析汇总
{chapter_contents_text}

"""
                # 如果有推理过程，添加到汇总内容中
                chapter_reasoning_contents_text = '\n\n'.join(chapter_reasoning_contents)
                if chapter_reasoning_contents:
                    aggregated_content += f"""
## 各章节推理过程
{chapter_reasoning_contents_text}
"""

                # 确保会话有效
                if session.is_active:
                    session.close()
                session = Session()

            # 准备元数据
            metadata = {
                "aggregated_from_chapters": True,
                "aggregate_time": time.time(),
                "chapter_count": len(results),
                "total_chapters": len(chapter_info),
                "is_aggregated": True  # 标记为汇总分析
            }

            if existing_result:
                # 如果已有结果不是汇总分析，则创建新的汇总结果
                if not existing_result.analysis_metadata or not existing_result.analysis_metadata.get("is_aggregated", False):
                    # 创建新的汇总结果，保留原有结果
                    new_result = AnalysisResult(
                        novel_id=novel_id,
                        dimension=f"{dim_key}_aggregated",  # 使用不同的维度名称
                        content=aggregated_content,
                        metadata=metadata
                    )
                    session.add(new_result)
                    aggregate_stats["created_results"] += 1
                else:
                    # 更新现有汇总结果
                    existing_result.content = aggregated_content
                    existing_result.analysis_metadata = metadata
                    aggregate_stats["updated_results"] += 1
            else:
                # 创建新的汇总结果
                new_result = AnalysisResult(
                    novel_id=novel_id,
                    dimension=dim_key,
                    content=aggregated_content,
                    metadata=metadata
                )
                session.add(new_result)
                aggregate_stats["created_results"] += 1

        # 提交事务
        session.commit()

        logger.info(f"成功汇总小说 {novel_id} 的章节分析结果，共 {len(chapter_results_by_dimension)} 个维度")
        return {
            "success": True,
            "message": f"成功汇总章节分析结果，共 {len(chapter_results_by_dimension)} 个维度",
            "stats": aggregate_stats
        }

    except Exception as e:
        logger.error(f"汇总分析结果时出错: {str(e)}")
        session.rollback()
        return {
            "success": False,
            "error": f"汇总分析结果时出错: {str(e)}",
            "novel_id": novel_id
        }
    finally:
        session.close()

class AnalysisService:
    def analyze_novel(self, novel_id: int, dimension: str, prompt_template: str = "default") -> Dict[str, Any]:
        """
        分析整本小说
        
        Args:
            novel_id: 小说ID
            dimension: 分析维度
            prompt_template: 提示词模板类型
            
        Returns:
            分析结果
        """
        try:
            # 加载小说内容
            novel = self._load_novel(novel_id)
            if not novel:
                return {"success": False, "error": f"小说(ID: {novel_id})不存在或内容为空"}
            
            # 初始化串行化瓶颈优化器
            optimizer = SerializationBottleneckOptimizer(prompt_template)
            
            # 构建分析请求
            analysis_request = {
                "novel_id": novel_id,
                "text": novel.get("content", ""),
                "title": novel.get("title", ""),
                "dimension": dimension,
                "prompt_template": prompt_template
            }
            
            # 应用智能结果缓存与推理复用优化
            optimized_request = optimizer.optimize_analysis_request(analysis_request)
            
            # 继续使用优化后的请求进行分析
            # ... existing analysis code ...
            
            # 分析完成后保存结果到缓存
            optimizer.save_analysis_result(result, optimized_request)
            
            # 添加优化统计信息
            result["optimization_stats"] = optimizer.get_optimization_stats()
            
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"分析小说失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def analyze_novel_parallel(self, novel_id: int, dimensions: List[str], prompt_template: str = "default") -> Dict[str, Any]:
        """
        并行分析多个维度
        
        Args:
            novel_id: 小说ID
            dimensions: 维度列表
            prompt_template: 提示词模板类型
            
        Returns:
            分析结果
        """
        try:
            # 初始化串行化瓶颈优化器
            optimizer = SerializationBottleneckOptimizer(prompt_template)
            
            # ... existing parallel analysis code ...
            
            # 在并行处理中应用智能结果缓存优化
            def analyze_with_optimization(dimension):
                # 构建维度分析请求
                analysis_request = {
                    "novel_id": novel_id,
                    "text": novel_content,
                    "title": novel_title,
                    "dimension": dimension,
                    "prompt_template": prompt_template
                }
                
                # 应用智能缓存优化
                optimized_request = optimizer.optimize_analysis_request(analysis_request)
                
                # 使用优化后的请求执行分析
                dimension_result = self._analyze_dimension(novel_id, optimized_request)
                
                # 保存分析结果到缓存
                if dimension_result.get("success"):
                    optimizer.save_analysis_result(dimension_result.get("result", {}), optimized_request)
                
                return dimension, dimension_result
            
            # 使用线程池执行并行分析
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = [executor.submit(analyze_with_optimization, dim) for dim in dimensions]
                
                # 收集结果
                results = {}
                for future in as_completed(futures):
                    dimension, result = future.result()
                    results[dimension] = result
            
            # 添加优化统计信息
            combined_result = {
                "success": True, 
                "results": results,
                "optimization_stats": optimizer.get_optimization_stats()
            }
            
            return combined_result
        except Exception as e:
            logger.error(f"并行分析小说失败: {str(e)}")
            return {"success": False, "error": str(e)}
            
    # ... existing code ...
