' 九猫小说分析系统启动脚本 (VBS版本) - 分析过程修复版
' 此脚本会在后台启动九猫系统，并自动打开浏览器，无需显示命令行窗口
' 启用了完整的分析过程记录功能，并修复了分析过程记录问题

' 设置工作目录为脚本所在目录
Set fso = CreateObject("Scripting.FileSystemObject")
currentPath = fso.GetParentFolderName(WScript.ScriptFullName)
Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = currentPath

' 检查并创建日志目录
If Not fso.FolderExists("logs") Then
    fso.CreateFolder("logs")
End If

' 清理旧日志文件，只保留最新的5个
Sub CleanupOldLogs()
    On Error Resume Next

    Dim logFolder, logFiles, file
    Set logFolder = fso.GetFolder(currentPath & "\logs")
    Set logFiles = logFolder.Files

    ' 创建一个数组来存储日志文件
    Dim fileArray()
    ReDim fileArray(logFiles.Count - 1)

    ' 填充数组
    Dim i, fileCount
    i = 0
    fileCount = 0

    For Each file In logFiles
        If LCase(Right(file.Name, 4)) = ".log" Then
            fileArray(i) = file.Path
            i = i + 1
            fileCount = fileCount + 1
        End If
    Next

    ' 如果文件数量超过5个，删除最旧的文件
    If fileCount > 5 Then
        ' 按修改日期排序（冒泡排序）
        Dim j, temp
        For i = 0 To fileCount - 2
            For j = 0 To fileCount - i - 2
                If fso.GetFile(fileArray(j)).DateLastModified > fso.GetFile(fileArray(j+1)).DateLastModified Then
                    temp = fileArray(j)
                    fileArray(j) = fileArray(j+1)
                    fileArray(j+1) = temp
                End If
            Next
        Next

        ' 删除最旧的文件（保留最新的5个）
        For i = 0 To fileCount - 6
            If fileArray(i) <> "" Then
                fso.DeleteFile(fileArray(i))
            End If
        Next
    End If
End Sub

' 执行日志清理
CleanupOldLogs()

' 检查psutil是否已安装
Function IsPsutilInstalled()
    Dim result
    result = WshShell.Run("python -c ""import psutil""", 0, True)
    IsPsutilInstalled = (result = 0)
End Function

' 安装psutil
Sub InstallPsutil()
    Dim result
    result = WshShell.Run("pip install psutil", 1, True)
    If result <> 0 Then
        MsgBox "无法安装psutil模块。系统将以有限的内存监控功能运行。" & vbCrLf & _
               "建议手动安装: pip install psutil", _
               48, "九猫小说分析系统 - 警告"
    End If
End Sub

' 检查并安装psutil
If Not IsPsutilInstalled() Then
    If MsgBox("未检测到psutil模块，该模块用于内存监控和优化。" & vbCrLf & _
              "是否立即安装？", _
              vbYesNo + vbQuestion, "九猫小说分析系统") = vbYes Then
        InstallPsutil()
    End If
End If

' 检查端口5001是否被占用
Function IsPortInUse()
    Dim result
    result = WshShell.Run("netstat -ano | findstr :5001 | findstr LISTENING", 0, True)
    IsPortInUse = (result = 0)
End Function

' 终止占用端口5001的进程
Sub KillPort5001Process()
    On Error Resume Next

    ' 创建临时批处理文件来终止进程
    Set killPortFile = fso.CreateTextFile("kill_port.bat", True)
    killPortFile.WriteLine("@echo off")
    killPortFile.WriteLine("for /f ""tokens=5"" %%a in ('netstat -ano ^| findstr :5001 ^| findstr LISTENING') do (")
    killPortFile.WriteLine("    taskkill /f /pid %%a >nul 2>&1")
    killPortFile.WriteLine(")")
    killPortFile.Close

    ' 运行批处理文件
    WshShell.Run "kill_port.bat", 0, True

    ' 删除临时文件
    fso.DeleteFile "kill_port.bat"
End Sub

' 检查并释放端口
If IsPortInUse() Then
    If MsgBox("端口5001已被占用，需要释放才能启动九猫系统。" & vbCrLf & _
              "是否尝试终止占用该端口的进程？", _
              vbYesNo + vbQuestion, "九猫小说分析系统") = vbYes Then
        KillPort5001Process()
    End If
End If

' 显示启动消息
MsgBox "九猫小说分析系统正在启动（分析过程修复版）..." & vbCrLf & vbCrLf & _
       "系统将在后台运行，浏览器将在10秒钟后自动打开。" & vbCrLf & _
       "请不要关闭浏览器窗口，否则需要手动访问: http://localhost:5001" & vbCrLf & vbCrLf & _
       "分析过程修复版特性：" & vbCrLf & _
       "1. 修复了分析过程记录功能" & vbCrLf & _
       "2. 记录每个分块的详细分析过程" & vbCrLf & _
       "3. 保存中间结果和API交互详情" & vbCrLf & _
       "4. 保存使用的提示词和分析参数" & vbCrLf & _
       "5. 增强日志分类和过滤功能" & vbCrLf & vbCrLf & _
       "注意：此模式会增加数据库体积和内存使用量", _
       64, "九猫小说分析系统 - 分析过程修复版"

' 创建临时批处理文件来设置环境变量并运行Python
Set tempFile = fso.CreateTextFile("temp_run.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("set MEMORY_OPTIMIZED=False")
tempFile.WriteLine("set LOW_MEMORY_MODE=False")
tempFile.WriteLine("set MEMORY_WARNING_THRESHOLD=85")
tempFile.WriteLine("set MEMORY_CRITICAL_THRESHOLD=95")
tempFile.WriteLine("set MAX_DB_CONNECTIONS=20")
tempFile.WriteLine("set DB_POOL_SIZE=10")
tempFile.WriteLine("set DB_MAX_OVERFLOW=10")
tempFile.WriteLine("set THREAD_POOL_SIZE=8")
tempFile.WriteLine("set MAX_WORKERS=8")
tempFile.WriteLine("set DISABLE_PARALLEL_ANALYSIS=False")
tempFile.WriteLine("set REDUCE_LOGGING=False")
tempFile.WriteLine("set ENABLE_DETAILED_PROCESS_RECORDING=True
tempFile.WriteLine("set PYTHONPATH=%PYTHONPATH%;%CD%")")
tempFile.WriteLine("set SAVE_FULL_API_INTERACTIONS=True")
tempFile.WriteLine("set SAVE_PROMPTS=True")
tempFile.WriteLine("set ENHANCED_LOG_CATEGORIES=True")
tempFile.WriteLine("set RECORD_INTERMEDIATE_RESULTS=True")
tempFile.WriteLine("set PROCESS_RECORDING_DEBUG=True")
tempFile.WriteLine("set PROCESS_RECORDING_VERBOSE=True")
tempFile.WriteLine("set FORCE_PROCESS_RECORDING=True
tempFile.WriteLine("set NINECATS_FORCE_PROCESS_RECORDING=True")")
tempFile.WriteLine("if not exist logs mkdir logs")
tempFile.WriteLine("python run.py")
tempFile.Close

' 在后台运行批处理文件
WshShell.Run "temp_run.bat", 1, False

' 等待10秒钟确保服务启动
WScript.Sleep 10000

' 打开浏览器 - 使用cmd命令强制使用默认浏览器
WshShell.Run "cmd /c start http://localhost:5001", 0, False
