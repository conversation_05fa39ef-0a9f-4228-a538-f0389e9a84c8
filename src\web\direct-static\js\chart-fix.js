/**
 * 九猫 - 专门修复图表初始化和进度获取问题
 * 这个脚本会在分析结果页面加载时执行，修复Chart.js相关问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('图表修复脚本已加载');

    // 存储图表实例的全局对象
    window.chartInstances = window.chartInstances || {};

    // 安全的图表初始化函数
    window.safeInitChart = function(canvasId, type, data, options) {
        console.log('安全初始化图表:', canvasId);

        try {
            // 检查Chart.js是否已加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载，无法初始化图表');
                return null;
            }

            // 获取canvas元素
            const canvas = document.getElementById(canvasId);
            if (!canvas) {
                console.error('找不到canvas元素:', canvasId);
                return null;
            }

            // 检查是否已有图表实例，如果有则销毁
            if (window.chartInstances[canvasId]) {
                console.log('销毁已有图表实例:', canvasId);
                window.chartInstances[canvasId].destroy();
                delete window.chartInstances[canvasId];
            }

            // 创建新的图表实例
            const ctx = canvas.getContext('2d');
            const chart = new Chart(ctx, {
                type: type,
                data: data,
                options: options
            });

            // 存储图表实例
            window.chartInstances[canvasId] = chart;
            console.log('成功创建图表:', canvasId);

            return chart;
        } catch (e) {
            console.error('初始化图表时出错:', e);
            return null;
        }
    };

    // 修复进度获取函数
    function fixProgressFetching() {
        console.log('修复进度获取函数');

        // 查找所有分析结果页面的脚本
        const scripts = document.querySelectorAll('script');

        scripts.forEach(function(script, index) {
            if (script.textContent && script.textContent.includes('fetchAnalysisProgress')) {
                console.log('找到包含fetchAnalysisProgress的脚本 #' + index);

                try {
                    // 修复fetchAnalysisProgress函数 - 注意这里我们避免直接使用模板字符串
                    const fixedContent = script.textContent.replace(
                        /function\s+fetchAnalysisProgress\(\)\s*\{[\s\S]*?\}/,
                        "function fetchAnalysisProgress() {\n" +
                        "    return fetch('/api/analysis/progress?novel_id=' + novelId)\n" +
                        "        .then(res => res.json())\n" +
                        "        .then(data => {\n" +
                        "            if (data.success) {\n" +
                        "                if (data.progress && data.progress[dimension]) {\n" +
                        "                    return {\n" +
                        "                        progress: data.progress[dimension],\n" +
                        "                        isRunning: data.is_running\n" +
                        "                    };\n" +
                        "                } else {\n" +
                        "                    console.log('进度数据中没有当前维度的信息');\n" +
                        "                    return {\n" +
                        "                        progress: { progress: 0 },\n" +
                        "                        isRunning: false\n" +
                        "                    };\n" +
                        "                }\n" +
                        "            }\n" +
                        "            console.log('进度数据获取成功但格式不正确');\n" +
                        "            return {\n" +
                        "                progress: { progress: 0 },\n" +
                        "                isRunning: false\n" +
                        "            };\n" +
                        "        })\n" +
                        "        .catch(err => {\n" +
                        "            console.error('获取进度信息失败:', err);\n" +
                        "            return {\n" +
                        "                progress: { progress: 0 },\n" +
                        "                isRunning: false\n" +
                        "            };\n" +
                        "        });\n" +
                        "}"
                    );

                    // 如果内容被修改，替换脚本
                    if (fixedContent !== script.textContent) {
                        console.log('替换修复后的进度获取脚本');
                        
                        // 使用安全的脚本替换函数（如果可用）
                        if (window.safeReplaceScript) {
                            window.safeReplaceScript(script, fixedContent);
                        } else {
                            const newScript = document.createElement('script');
                            newScript.text = fixedContent;

                            try {
                                // 安全替换脚本
                                if (script.parentNode) {
                                    // 先保存父节点的引用
                                    const parentNode = script.parentNode;
                                    
                                    // 移除旧脚本
                                    parentNode.removeChild(script);
                                    
                                    // 添加新脚本
                                    parentNode.appendChild(newScript);
                                } else {
                                    // 备用方案：直接添加到head
                                    document.head.appendChild(newScript);
                                }
                            } catch (replaceError) {
                                console.error('替换脚本时出错:', replaceError);
                                
                                // 如果替换失败，尝试直接添加新脚本
                                try {
                                    document.head.appendChild(newScript);
                                } catch (appendError) {
                                    console.error('添加新脚本时出错:', appendError);
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.error('修复进度获取函数时出错:', e);
                }
            }
        });
    }

    // 修复图表初始化函数
    function fixChartInitialization() {
        console.log('修复图表初始化函数');

        // 查找所有分析结果页面的脚本
        const scripts = document.querySelectorAll('script');

        scripts.forEach(function(script, index) {
            if (script.textContent && script.textContent.includes('initCharts')) {
                console.log('找到包含initCharts的脚本 #' + index);

                try {
                    // 修复initCharts函数中的图表初始化部分
                    const fixedContent = script.textContent.replace(
                        /new\s+Chart\((\w+Ctx),\s*\{/g,
                        'window.safeInitChart($1.canvas.id, '
                    ).replace(
                        /\}\s*\)\s*;/g,
                        '});'
                    );

                    // 如果内容被修改，替换脚本
                    if (fixedContent !== script.textContent) {
                        console.log('替换修复后的图表初始化脚本');
                        
                        // 使用安全的脚本替换函数（如果可用）
                        if (window.safeReplaceScript) {
                            window.safeReplaceScript(script, fixedContent);
                        } else {
                            const newScript = document.createElement('script');
                            newScript.text = fixedContent;

                            try {
                                // 安全替换脚本
                                if (script.parentNode) {
                                    // 先保存父节点的引用
                                    const parentNode = script.parentNode;
                                    
                                    // 移除旧脚本
                                    parentNode.removeChild(script);
                                    
                                    // 添加新脚本
                                    parentNode.appendChild(newScript);
                                } else {
                                    // 备用方案：直接添加到head
                                    document.head.appendChild(newScript);
                                }
                            } catch (replaceError) {
                                console.error('替换脚本时出错:', replaceError);
                                
                                // 如果替换失败，尝试直接添加新脚本
                                try {
                                    document.head.appendChild(newScript);
                                } catch (appendError) {
                                    console.error('添加新脚本时出错:', appendError);
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.error('修复图表初始化函数时出错:', e);
                }
            }
        });
    }

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复图表和进度获取问题');

        // 修复进度获取函数
        fixProgressFetching();

        // 修复图表初始化函数
        fixChartInitialization();

        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message &&
                (event.error.message.includes('Chart with ID') ||
                 event.error.message.includes('Canvas is already in use'))) {
                console.error('捕获到Chart.js相关错误:', event.error.message);
                console.error('错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);

                // 尝试修复
                setTimeout(function() {
                    // 重新初始化图表
                    if (typeof window.initCharts === 'function') {
                        console.log('尝试重新初始化图表');
                        window.initCharts();
                    }
                }, 500);

                // 阻止错误传播
                event.preventDefault();
                return false;
            }
        }, true);
    });
})();
