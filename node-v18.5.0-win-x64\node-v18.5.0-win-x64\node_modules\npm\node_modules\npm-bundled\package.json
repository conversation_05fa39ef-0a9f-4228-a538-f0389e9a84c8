{"name": "npm-bundled", "version": "1.1.2", "description": "list things in node_modules that are bundledDependencies, or transitive dependencies thereof", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/npm/npm-bundled.git"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "devDependencies": {"mkdirp": "^0.5.1", "mutate-fs": "^1.1.0", "rimraf": "^2.6.1", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js -J --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "files": ["index.js"], "dependencies": {"npm-normalize-package-bin": "^1.0.1"}}