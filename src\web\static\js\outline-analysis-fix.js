/**
 * 九猫 - 大纲分析专用修复脚本
 * 解决大纲分析页面的Chart.js相关问题
 * 版本: 1.0.0
 */

(function() {
    // 确保脚本只加载一次
    if (window.outlineAnalysisFixLoaded) return;
    window.outlineAnalysisFixLoaded = true;
    
    console.log('大纲分析专用修复脚本已加载');

    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化图表
        initOutlineAnalysisChart();
    });

    // 全局变量
    let outlineAnalysisChart = null;

    // 初始化大纲分析图表
    window.initOutlineAnalysisChart = function() {
        try {
            const canvas = document.getElementById('outline-analysis-chart');
            if (!canvas) {
                console.error('未找到大纲分析图表画布元素');
                return;
            }

            // 获取元数据
            const metadataElement = document.getElementById('analysis-metadata');
            if (!metadataElement) {
                console.error('未找到元数据元素');
                return;
            }

            let metadata;
            try {
                metadata = JSON.parse(metadataElement.getAttribute('data-metadata') || '{}');
            } catch (error) {
                console.error('解析元数据时出错:', error);
                metadata = {};
            }

            // 提取图表数据
            const chartData = extractChartData(metadata);
            if (!chartData || !chartData.labels || chartData.labels.length === 0) {
                console.warn('未找到有效的图表数据');
                canvas.parentElement.innerHTML = '<div class="alert alert-info">暂无图表数据</div>';
                return;
            }

            // 创建图表
            createOutlineAnalysisChart(canvas, chartData);
        } catch (error) {
            console.error('初始化大纲分析图表时出错:', error);
        }
    };

    // 从元数据中提取图表数据
    function extractChartData(metadata) {
        // 默认图表数据
        const defaultData = {
            labels: ['结构完整性', '情节连贯性', '人物塑造', '主题深度', '世界构建', '叙事节奏'],
            data: [0, 0, 0, 0, 0, 0]
        };

        // 如果没有元数据，返回默认数据
        if (!metadata || !metadata.metrics) {
            return defaultData;
        }

        try {
            // 尝试从元数据中提取图表数据
            const metrics = metadata.metrics;
            
            // 如果有预定义的图表数据，直接使用
            if (metrics.chart_data) {
                return metrics.chart_data;
            }
            
            // 否则，从各种指标中构建图表数据
            const labels = [];
            const data = [];
            
            // 添加常见指标
            if (metrics.structure_score !== undefined) {
                labels.push('结构完整性');
                data.push(parseFloat(metrics.structure_score) || 0);
            }
            
            if (metrics.plot_coherence !== undefined) {
                labels.push('情节连贯性');
                data.push(parseFloat(metrics.plot_coherence) || 0);
            }
            
            if (metrics.character_development !== undefined) {
                labels.push('人物塑造');
                data.push(parseFloat(metrics.character_development) || 0);
            }
            
            if (metrics.theme_depth !== undefined) {
                labels.push('主题深度');
                data.push(parseFloat(metrics.theme_depth) || 0);
            }
            
            if (metrics.world_building !== undefined) {
                labels.push('世界构建');
                data.push(parseFloat(metrics.world_building) || 0);
            }
            
            if (metrics.narrative_rhythm !== undefined) {
                labels.push('叙事节奏');
                data.push(parseFloat(metrics.narrative_rhythm) || 0);
            }
            
            // 如果没有足够的数据，使用默认数据
            if (labels.length < 3) {
                return defaultData;
            }
            
            return { labels, data };
        } catch (error) {
            console.error('提取图表数据时出错:', error);
            return defaultData;
        }
    }

    // 创建大纲分析图表
    function createOutlineAnalysisChart(canvas, chartData) {
        try {
            // 如果已经有图表实例，先销毁
            if (outlineAnalysisChart) {
                outlineAnalysisChart.destroy();
                outlineAnalysisChart = null;
            }

            // 准备数据
            const { labels, data } = chartData;

            // 创建雷达图配置
            const radarConfig = {
                type: 'radar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '大纲分析',
                        data: data,
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(255, 99, 132, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 10
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.raw}/10`;
                                }
                            }
                        }
                    }
                }
            };

            // 创建图表
            outlineAnalysisChart = new Chart(canvas, radarConfig);
        } catch (error) {
            console.error('创建大纲分析图表时出错:', error);
            
            // 显示错误信息
            if (canvas && canvas.parentElement) {
                canvas.parentElement.innerHTML = '<div class="alert alert-danger">图表加载失败</div>';
            }
        }
    }
})();
