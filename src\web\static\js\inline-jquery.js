/**
 * 九猫小说分析写作系统 - 内联jQuery
 * 
 * 此脚本用于在页面加载时直接嵌入一个极简版的jQuery，确保jQuery在任何情况下都可用
 * 版本: 1.0.0
 */

// 立即执行的内联jQuery
(function() {
    console.log('[内联jQuery] 初始化...');
    
    // 检查jQuery是否已加载
    if (typeof jQuery !== 'undefined') {
        console.log('[内联jQuery] jQuery已加载，版本:', jQuery.fn.jquery);
        
        // 确保$ 变量可用
        if (typeof $ === 'undefined') {
            console.log('[内联jQuery] $ 变量未定义，设置 $ = jQuery');
            window.$ = jQuery;
        }
        
        return;
    }
    
    console.log('[内联jQuery] jQuery未加载，创建内联jQuery');
    
    // 创建极简jQuery
    window.jQuery = function(selector) {
        if (typeof selector === 'function') {
            if (document.readyState !== 'loading') {
                selector();
            } else {
                document.addEventListener('DOMContentLoaded', selector);
            }
            return;
        }
        
        const elements = typeof selector === 'string' 
            ? document.querySelectorAll(selector)
            : [selector];
        
        return {
            elements: elements,
            length: elements.length,
            each: function(callback) {
                for (let i = 0; i < this.elements.length; i++) {
                    callback.call(this.elements[i], i, this.elements[i]);
                }
                return this;
            },
            html: function(content) {
                if (content === undefined) {
                    return this.elements[0] ? this.elements[0].innerHTML : '';
                }
                this.each(function() {
                    this.innerHTML = content;
                });
                return this;
            },
            text: function(content) {
                if (content === undefined) {
                    return this.elements[0] ? this.elements[0].textContent : '';
                }
                this.each(function() {
                    this.textContent = content;
                });
                return this;
            },
            addClass: function(className) {
                this.each(function() {
                    this.classList.add(className);
                });
                return this;
            },
            removeClass: function(className) {
                this.each(function() {
                    this.classList.remove(className);
                });
                return this;
            },
            append: function(content) {
                this.each(function() {
                    if (typeof content === 'string') {
                        this.innerHTML += content;
                    } else if (content.nodeType) {
                        this.appendChild(content);
                    }
                });
                return this;
            },
            click: function(callback) {
                this.each(function() {
                    this.addEventListener('click', callback);
                });
                return this;
            },
            on: function(event, callback) {
                this.each(function() {
                    this.addEventListener(event, callback);
                });
                return this;
            }
        };
    };
    
    // 设置版本和fn
    jQuery.fn = {};
    jQuery.fn.jquery = '1.0.0-minimal';
    
    // 添加ajax方法
    jQuery.ajax = function(options) {
        const xhr = new XMLHttpRequest();
        xhr.open(options.type || 'GET', options.url, true);
        
        if (options.contentType) {
            xhr.setRequestHeader('Content-Type', options.contentType);
        }
        
        xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                if (options.success) {
                    let response;
                    try {
                        response = JSON.parse(xhr.responseText);
                    } catch (e) {
                        response = xhr.responseText;
                    }
                    options.success(response);
                }
            } else {
                if (options.error) {
                    options.error(xhr);
                }
            }
        };
        
        xhr.onerror = function() {
            if (options.error) {
                options.error(xhr);
            }
        };
        
        xhr.send(options.data);
        
        return xhr;
    };
    
    // 设置$别名
    window.$ = jQuery;
    
    console.log('[内联jQuery] 内联jQuery创建完成');
    
    // 尝试加载真正的jQuery
    const script = document.createElement('script');
    script.src = '/static/js/lib/jquery.min.js';
    
    script.onload = function() {
        console.log('[内联jQuery] 真正的jQuery加载成功，版本:', jQuery.fn.jquery);
    };
    
    script.onerror = function() {
        console.error('[内联jQuery] 加载真正的jQuery失败，继续使用内联jQuery');
    };
    
    document.head.appendChild(script);
})();
