{% extends "v3.5/base.html" %}

{% block title %}控制台 - 九猫小说分析写作系统v3.5{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/knowledge-base-compact.css') }}">
<style>
    /* 控制台基本样式 */
    .console-container {
        height: calc(100vh - 250px);
        min-height: 600px;
    }

    /* 控制台导航栏 */
    .console-nav {
        background-color: var(--sidebar-bg);
        border-bottom: 1px solid var(--border-color);
    }

    .console-nav .nav-link {
        color: var(--text-color);
        padding: 1rem;
        border-radius: 0;
        border-bottom: 3px solid transparent;
    }

    .console-nav .nav-link.active {
        color: var(--primary-color);
        background-color: transparent;
        border-bottom: 3px solid var(--primary-color);
    }

    /* 控制台内容区域 */
    .console-content {
        padding: 1.5rem;
        height: 100%;
        overflow-y: auto;
    }

    /* 日志区域 */
    .log-container {
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        height: 300px;
        overflow-y: auto;
        padding: 1rem;
        font-family: 'Consolas', 'Monaco', monospace;
    }

    .log-line {
        margin-bottom: 0.25rem;
        white-space: pre-wrap;
        word-break: break-word;
    }

    .log-timestamp {
        color: var(--text-muted);
        margin-right: 0.5rem;
    }

    .log-info {
        color: var(--info-color);
    }

    .log-success {
        color: var(--success-color);
    }

    .log-warn {
        color: var(--warning-color);
    }

    .log-error {
        color: var(--danger-color);
    }

    /* 预设模板列表 */
    .template-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .template-item {
        cursor: pointer;
        transition: var(--transition);
        border-left: 3px solid transparent;
    }

    .template-item:hover {
        background-color: rgba(106, 140, 175, 0.1);
    }

    .template-item.active {
        background-color: rgba(106, 140, 175, 0.15);
        border-left: 3px solid var(--primary-color);
    }

    /* 写作结果区域 */
    .writing-result {
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        min-height: 300px;
        padding: 1rem;
        overflow-y: auto;
    }

    /* 系统监控 */
    .monitor-card {
        transition: var(--transition);
    }

    .monitor-card:hover {
        transform: translateY(-3px);
    }

    .progress {
        height: 0.75rem;
        border-radius: 1rem;
    }

    /* 知识库 */
    .knowledge-item {
        cursor: pointer;
        transition: var(--transition);
    }

    .knowledge-item:hover {
        background-color: rgba(106, 140, 175, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="mb-3">控制台</h1>
        <p class="lead">九猫小说分析写作系统v3.5控制台，提供运行日志、分析展示、知识库和自动写作功能。</p>
    </div>
</div>

<div class="card">
    <div class="card-header p-0">
        <ul class="nav nav-tabs console-nav" id="consoleTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="true">
                    <i class="fas fa-terminal me-2"></i>运行日志
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab" aria-controls="analysis" aria-selected="false">
                    <i class="fas fa-chart-bar me-2"></i>分析展示
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="knowledge-tab" data-bs-toggle="tab" data-bs-target="#knowledge" type="button" role="tab" aria-controls="knowledge" aria-selected="false">
                    <i class="fas fa-brain me-2"></i>知识库
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="writing-tab" data-bs-toggle="tab" data-bs-target="#writing" type="button" role="tab" aria-controls="writing" aria-selected="false">
                    <i class="fas fa-pen-fancy me-2"></i>自动写作
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body p-0">
        <div class="tab-content" id="consoleTabContent">
            <!-- 运行日志标签页 -->
            <div class="tab-pane fade show active" id="logs" role="tabpanel" aria-labelledby="logs-tab">
                <div class="console-content">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="logSearch" placeholder="搜索日志...">
                                <button class="btn btn-outline-secondary" type="button" id="clearLogSearchBtn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-outline-primary" id="clearLogsBtn">
                                <i class="fas fa-eraser me-1"></i>清空日志
                            </button>
                            <button class="btn btn-outline-secondary" id="pauseLogsBtn">
                                <i class="fas fa-pause me-1"></i>暂停更新
                            </button>
                        </div>
                    </div>
                    <div class="log-container" id="logContainer">
                        <div class="log-line">
                            <span class="log-timestamp">{{ now.strftime('%H:%M:%S') }}</span>
                            <span class="log-info">九猫小说分析写作系统v3.5已启动</span>
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">{{ now.strftime('%H:%M:%S') }}</span>
                            <span class="log-success">控制台初始化完成</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析展示标签页 -->
            <div class="tab-pane fade" id="analysis" role="tabpanel" aria-labelledby="analysis-tab">
                <div class="console-content">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="fas fa-book me-2"></i>参考蓝本
                                </div>
                                <div class="card-body template-list p-0">
                                    <div class="list-group list-group-flush">
                                        {% for template in templates %}
                                        <div class="list-group-item template-item" data-template-id="{{ template.id }}">
                                            <h6 class="mb-1">{{ template.title }}</h6>
                                            <small class="text-muted">{{ template.created_at.strftime('%Y-%m-%d') }}</small>
                                        </div>
                                        {% else %}
                                        <div class="list-group-item">
                                            <p class="mb-0 text-muted">暂无参考蓝本</p>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <i class="fas fa-chart-line me-2"></i>分析结果
                                </div>
                                <div class="card-body">
                                    <div id="analysisResult">
                                        <div class="text-center py-5">
                                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                            <p>请从左侧选择一个参考蓝本查看分析结果</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 知识库标签页 -->
            <div class="tab-pane fade" id="knowledge" role="tabpanel" aria-labelledby="knowledge-tab">
                <div class="console-content">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="knowledgeSearch" placeholder="搜索知识库...">
                                <button class="btn btn-outline-secondary" type="button" id="clearKnowledgeSearchBtn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-outline-primary" id="readTemplateBtn">
                                <i class="fas fa-sync-alt me-1"></i>读取蓝本
                            </button>
                            <button class="btn btn-outline-secondary" id="exportKnowledgeBtn">
                                <i class="fas fa-file-export me-1"></i>导出知识
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="fas fa-book me-2"></i>参考蓝本
                                </div>
                                <div class="card-body template-list p-0">
                                    <div class="list-group list-group-flush">
                                        {% for template in templates %}
                                        <div class="list-group-item knowledge-item" data-template-id="{{ template.id }}">
                                            <h6 class="mb-1">{{ template.title }}</h6>
                                            <small class="text-muted">{{ template.created_at.strftime('%Y-%m-%d') }}</small>
                                        </div>
                                        {% else %}
                                        <div class="list-group-item">
                                            <p class="mb-0 text-muted">暂无参考蓝本</p>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-brain me-2"></i>预设模板
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary" id="expandViewBtn">
                                            <i class="fas fa-expand-alt me-1"></i>展开查看
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="knowledgeContent">
                                        <div class="text-center py-5">
                                            <i class="fas fa-brain fa-3x text-muted mb-3"></i>
                                            <p>请从左侧选择一个参考蓝本查看预设模板</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 自动写作标签页 -->
            <div class="tab-pane fade" id="writing" role="tabpanel" aria-labelledby="writing-tab">
                <div class="console-content">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="templateSelect" class="form-label">选择参考蓝本</label>
                                <select class="form-select" id="templateSelect">
                                    <option value="">-- 请选择参考蓝本 --</option>
                                    {% for template in templates %}
                                    <option value="{{ template.id }}">{{ template.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="writingPrompt" class="form-label">写作提示</label>
                                <textarea class="form-control" id="writingPrompt" rows="5" placeholder="请输入写作提示，例如：写一个类似于参考蓝本风格的章节，主题是..."></textarea>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" id="startWritingBtn">
                                    <i class="fas fa-pen-fancy me-2"></i>开始写作
                                </button>
                                <button class="btn btn-outline-secondary" id="compareWithOriginalBtn">
                                    <i class="fas fa-exchange-alt me-2"></i>与原文对比
                                </button>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="card h-100">
                                <div class="card-header">
                                    <i class="fas fa-file-alt me-2"></i>写作结果
                                </div>
                                <div class="card-body writing-result" id="writingResult">
                                    <div class="text-center py-5">
                                        <i class="fas fa-pen-fancy fa-3x text-muted mb-3"></i>
                                        <p>请选择参考蓝本并输入写作提示，然后点击"开始写作"按钮</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/v3.5/console.js') }}"></script>
{% endblock %}
