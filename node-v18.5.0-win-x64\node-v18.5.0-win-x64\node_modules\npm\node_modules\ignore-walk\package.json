{"name": "ignore-walk", "version": "5.0.1", "description": "Nested/recursive `.gitignore`/`.npmignore` parsing and filtering.", "main": "lib/index.js", "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "mkdirp": "^1.0.4", "mutate-fs": "^2.1.1", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "eslint \"**/*.js\"", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "npmclilint": "npmcli-lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "prepublishOnly": "git push origin --follow-tags", "snap": "tap"}, "keywords": ["ignorefile", "ignore", "file", ".giti<PERSON>re", ".n<PERSON><PERSON><PERSON>", "glob"], "author": "GitHub Inc.", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/npm/ignore-walk.git"}, "files": ["bin/", "lib/"], "dependencies": {"minimatch": "^5.0.1"}, "tap": {"test-env": "LC_ALL=sk", "before": "test/00-setup.js", "after": "test/zz-cleanup.js", "jobs": 1}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2", "windowsCI": false}}