<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-pkg</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="npm-pkg">npm-pkg</h1>
<span class="description">Manages your package.json</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<pre lang="bash"><code>npm pkg set &lt;key&gt;=&lt;value&gt; [&lt;key&gt;=&lt;value&gt; ...]
npm pkg get [&lt;key&gt; [&lt;key&gt; ...]]
npm pkg delete &lt;key&gt; [&lt;key&gt; ...]
npm pkg set [&lt;array&gt;[&lt;index&gt;].&lt;key&gt;=&lt;value&gt; ...]
npm pkg set [&lt;array&gt;[].&lt;key&gt;=&lt;value&gt; ...]
</code></pre>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h3 id="description">Description</h3>
<p>A command that automates the management of <code>package.json</code> files.
<code>npm pkg</code> provide 3 different sub commands that allow you to modify or retrieve
values for given object keys in your <code>package.json</code>.</p>
<p>The syntax to retrieve and set fields is a dot separated representation of
the nested object properties to be found within your <code>package.json</code>, it's the
same notation used in <a href="../commands/npm-view.html"><code>npm view</code></a> to retrieve information
from the registry manifest, below you can find more examples on how to use it.</p>
<p>Returned values are always in <strong>json</strong> format.</p>
<ul>
<li>
<p><code>npm pkg get &lt;field&gt;</code></p>
<p>Retrieves a value <code>key</code>, defined in your <code>package.json</code> file.</p>
<p>For example, in order to retrieve the name of the current package, you
can run:</p>
<pre lang="bash"><code>npm pkg get name
</code></pre>
<p>It's also possible to retrieve multiple values at once:</p>
<pre lang="bash"><code>npm pkg get name version
</code></pre>
<p>You can view child fields by separating them with a period. To retrieve
the value of a test <code>script</code> value, you would run the following command:</p>
<pre lang="bash"><code>npm pkg get scripts.test
</code></pre>
<p>For fields that are arrays, requesting a non-numeric field will return
all of the values from the objects in the list. For example, to get all
the contributor emails for a package, you would run:</p>
<pre lang="bash"><code>npm pkg get contributors.email
</code></pre>
<p>You may also use numeric indices in square braces to specifically select
an item in an array field. To just get the email address of the first
contributor in the list, you can run:</p>
<pre lang="bash"><code>npm pkg get contributors[0].email
</code></pre>
<p>For complex fields you can also name a property in square brackets
to specifically select a child field. This is especially helpful
with the exports object:</p>
<pre lang="bash"><code>npm pkg get "exports[.].require"
</code></pre>
</li>
<li>
<p><code>npm pkg set &lt;field&gt;=&lt;value&gt;</code></p>
<p>Sets a <code>value</code> in your <code>package.json</code> based on the <code>field</code> value. When
saving to your <code>package.json</code> file the same set of rules used during
<code>npm install</code> and other cli commands that touches the <code>package.json</code> file
are used, making sure to respect the existing indentation and possibly
applying some validation prior to saving values to the file.</p>
<p>The same syntax used to retrieve values from your package can also be used
to define new properties or overriding existing ones, below are some
examples of how the dot separated syntax can be used to edit your
<code>package.json</code> file.</p>
<p>Defining a new bin named <code>mynewcommand</code> in your <code>package.json</code> that points
to a file <code>cli.js</code>:</p>
<pre lang="bash"><code>npm pkg set bin.mynewcommand=cli.js
</code></pre>
<p>Setting multiple fields at once is also possible:</p>
<pre lang="bash"><code>npm pkg set description='Awesome package' engines.node='&gt;=10'
</code></pre>
<p>It's also possible to add to array values, for example to add a new
contributor entry:</p>
<pre lang="bash"><code>npm pkg set contributors[0].name='Foo' contributors[0].email='<EMAIL>'
</code></pre>
<p>You may also append items to the end of an array using the special
empty bracket notation:</p>
<pre lang="bash"><code>npm pkg set contributors[].name='Foo' contributors[].name='Bar'
</code></pre>
<p>It's also possible to parse values as json prior to saving them to your
<code>package.json</code> file, for example in order to set a <code>"private": true</code>
property:</p>
<pre lang="bash"><code>npm pkg set private=true --json
</code></pre>
<p>It also enables saving values as numbers:</p>
<pre lang="bash"><code>npm pkg set tap.timeout=60 --json
</code></pre>
</li>
<li>
<p><code>npm pkg delete &lt;key&gt;</code></p>
<p>Deletes a <code>key</code> from your <code>package.json</code></p>
<p>The same syntax used to set values from your package can also be used
to remove existing ones. For example, in order to remove a script named
build:</p>
<pre lang="bash"><code>npm pkg delete scripts.build
</code></pre>
</li>
</ul>
<h3 id="workspaces-support">Workspaces support</h3>
<p>You can set/get/delete items across your configured workspaces by using the
<code>workspace</code> or <code>workspaces</code> config options.</p>
<p>For example, setting a <code>funding</code> value across all configured workspaces
of a project:</p>
<pre lang="bash"><code>npm pkg set funding=https://example.com --ws
</code></pre>
<p>When using <code>npm pkg get</code> to retrieve info from your configured workspaces, the
returned result will be in a json format in which top level keys are the
names of each workspace, the values of these keys will be the result values
returned from each of the configured workspaces, e.g:</p>
<pre><code>npm pkg get name version --ws
{
  "a": {
    "name": "a",
    "version": "1.0.0"
  },
  "b": {
    "name": "b",
    "version": "1.0.0"
  }
}
</code></pre>
<h3 id="configuration">Configuration</h3>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="force"><code>force</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Removes various protections against unfortunate side effects, common
mistakes, unnecessary performance degradation, and malicious input.</p>
<ul>
<li>Allow clobbering non-npm files in global installs.</li>
<li>Allow the <code>npm version</code> command to work on an unclean git repository.</li>
<li>Allow deleting the cache folder with <code>npm cache clean</code>.</li>
<li>Allow installing packages that have an <code>engines</code> declaration requiring a
different version of npm.</li>
<li>Allow installing packages that have an <code>engines</code> declaration requiring a
different version of <code>node</code>, even if <code>--engine-strict</code> is enabled.</li>
<li>Allow <code>npm audit fix</code> to install modules outside your stated dependency
range (including SemVer-major changes).</li>
<li>Allow unpublishing all versions of a published package.</li>
<li>Allow conflicting peerDependencies to be installed in the root project.</li>
<li>Implicitly set <code>--yes</code> during <code>npm init</code>.</li>
<li>Allow clobbering existing values in <code>npm pkg</code></li>
<li>Allow unpublishing of entire packages (not just a single version).</li>
</ul>
<p>If you don't have a clear idea of what you want to do, it is strongly
recommended that you do not use this option!</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="json"><code>json</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to output JSON data, rather than the normal output.</p>
<ul>
<li>In <code>npm pkg set</code> it enables parsing set values with JSON.parse() before
saving them to your <code>package.json</code>.</li>
</ul>
<p>Not supported by all npm commands.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="workspace"><code>workspace</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option.</p>
<p>Valid values for the <code>workspace</code> config are either:</p>
<ul>
<li>Workspace names</li>
<li>Path to a workspace directory</li>
<li>Path to a parent workspace directory (will result in selecting all
workspaces within that folder)</li>
</ul>
<p>When set for the <code>npm init</code> command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project.</p>
<p>This value is not exported to the environment for child processes.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h4 id="workspaces"><code>workspaces</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or Boolean</li>
</ul>
<p>Set to true to run the command in the context of <strong>all</strong> configured
workspaces.</p>
<p>Explicitly setting this to false will cause commands like <code>install</code> to
ignore workspaces altogether. When not set explicitly:</p>
<ul>
<li>Commands that operate on the <code>node_modules</code> tree (install, update, etc.)
will link workspaces into the <code>node_modules</code> folder. - Commands that do
other things (test, exec, publish, etc.) will operate on the root project,
<em>unless</em> one or more workspaces are specified in the <code>workspace</code> config.</li>
</ul>
<p>This value is not exported to the environment for child processes.</p>
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<!-- raw HTML omitted -->
<h2 id="see-also">See Also</h2>
<ul>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../commands/npm-init.html">npm init</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../commands/npm-set-script.html">npm set-script</a></li>
<li><a href="../using-npm/workspaces.html">workspaces</a></li>
</ul>
</div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-pkg.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>