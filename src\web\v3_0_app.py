"""
九猫小说分析写作系统v3.0应用独立入口
"""
import os
import sys
import logging
import time
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_cors import CORS
from sqlalchemy.orm import Session
from sqlalchemy import func

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入配置
import config

# 导入数据库模型
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.api_log import ApiLog
from src.db.connection import Session, engine
from src.models.base import Base

# 导入API客户端
try:
    from src.api.deepseek_client import DeepSeekClient
    from src.api.analysis import NovelAnalyzer
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("无法导入DeepSeekClient或NovelAnalyzer，某些功能可能不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(config.LOG_DIR if hasattr(config, 'LOG_DIR') else 'logs', 'v3_0_app.log'))
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_CONTENT_LENGTH

# 启用CORS
CORS(app)

# 添加自定义模板过滤器
@app.template_filter('tojson_safe')
def tojson_safe(obj):
    import json
    from markupsafe import Markup
    if hasattr(obj, '__class__') and obj.__class__.__name__ == 'MetaData':
        try:
            if hasattr(obj, 'items') and callable(obj.items):
                obj = {k: v for k, v in obj.items()}
            else:
                obj = {}
        except Exception as e:
            logger.error(f"转换MetaData对象为字典时出错: {str(e)}")
            obj = {}
    def json_default(o):
        if hasattr(o, '__dict__'):
            return o.__dict__
        elif hasattr(o, 'items') and callable(o.items):
            return {k: v for k, v in o.items()}
        else:
            return str(o)
    try:
        json_str = json.dumps(obj, default=json_default, ensure_ascii=False)
        return Markup(json_str)
    except Exception as e:
        logger.error(f"JSON序列化对象时出错: {str(e)}")
        return Markup("{}")

@app.template_filter('format_number')
def format_number(value):
    try:
        return "{:,}".format(int(value))
    except (ValueError, TypeError):
        return value

# 确保所有数据库表已创建
Base.metadata.create_all(engine)
logger.info("确认所有数据库表已创建")

# 运行数据库迁移
try:
    from src.db.migrations import run_migrations
    logger.info("开始执行数据库迁移...")
    run_migrations()
    logger.info("数据库迁移执行完成")
except Exception as e:
    logger.error(f"执行数据库迁移时出错: {str(e)}", exc_info=True)

# 只导入v3.0版本的路由
try:
    from src.web.routes.v3_routes import v3_bp
    from src.web.routes.v3_api import v3_api_bp
    logger.info("成功导入v3.0版本的路由蓝图")
except ImportError as e:
    logger.error(f"无法导入v3.0版本的路由蓝图: {str(e)}")
    raise

# 导入共享API路由模块
try:
    from src.web.routes.shared_api_routes import shared_api_bp
    logger.info("成功导入共享API路由模块")
except ImportError as e:
    logger.warning(f"无法导入共享API路由模块: {str(e)}")
    shared_api_bp = None

# 导入模板兼容性路由蓝图
try:
    from src.web.routes.template_compat_routes import template_compat_bp
    logger.info("成功导入模板兼容性路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入模板兼容性路由蓝图: {str(e)}")
    template_compat_bp = None

# 导入API兼容性路由蓝图
try:
    from src.web.routes.api_compat_routes import api_compat_bp
    logger.info("成功导入API兼容性路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入API兼容性路由蓝图: {str(e)}")
    api_compat_bp = None

# 导入预设内容API路由蓝图（如v3.0需要）
try:
    from src.web.routes.preset_api import preset_api_bp
    logger.info("成功导入预设内容API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入预设内容API路由蓝图: {str(e)}")
    preset_api_bp = None

# 导入章节API路由蓝图（如v3.0需要）
try:
    from src.web.routes.v3_chapters_api import v3_chapters_api_bp
    logger.info("成功导入章节API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入章节API路由蓝图: {str(e)}")
    v3_chapters_api_bp = None

# 导入系统API路由蓝图
try:
    from src.web.routes.system_api import system_api_bp
    logger.info("成功导入系统API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入系统API路由蓝图: {str(e)}")
    system_api_bp = None

# 导入测试功能API路由蓝图
try:
    from src.web.routes.v3_test_api import v3_test_api_bp
    logger.info("成功导入测试功能API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入测试功能API路由蓝图: {str(e)}")
    v3_test_api_bp = None

# 注册蓝图
app.register_blueprint(v3_bp, url_prefix='/v3')
app.register_blueprint(v3_api_bp, url_prefix='/api')
if preset_api_bp:
    app.register_blueprint(preset_api_bp)
    logger.info("已注册预设内容API路由蓝图")
if v3_chapters_api_bp:
    app.register_blueprint(v3_chapters_api_bp)
    logger.info("已注册章节API路由蓝图")
if shared_api_bp:
    app.register_blueprint(shared_api_bp)
    logger.info("已注册共享API路由模块")
if system_api_bp:
    app.register_blueprint(system_api_bp)
    logger.info("已注册系统API路由蓝图")
if template_compat_bp:
    app.register_blueprint(template_compat_bp)
    logger.info("已注册模板兼容性路由蓝图")
if api_compat_bp:
    app.register_blueprint(api_compat_bp)
    logger.info("已注册API兼容性路由蓝图")
if v3_test_api_bp:
    app.register_blueprint(v3_test_api_bp)
    logger.info("已注册测试功能API路由蓝图")

# 检查是否启用API路由修复
if os.environ.get('API_ROUTE_FIX') == 'True':
    logger.info("API路由修复已启用，注册所有必要的API路由")
    try:
        # 导入API函数
        from src.web.routes.v3_api import api_delete_chapter_analysis, api_get_novel_all_analysis

        # 注册章节删除API路由
        # 注册主路由
        if not any(rule.rule == '/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete' for rule in app.url_map.iter_rules()):
            app.add_url_rule('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete',
                           view_func=api_delete_chapter_analysis, methods=['POST'])
            logger.info("成功注册删除章节分析结果API主路由 (全局)")
        else:
            logger.info("删除章节分析结果API主路由已存在，无需重复注册")

        # 注册别名路由
        if not any(rule.rule == '/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete' for rule in app.url_map.iter_rules()):
            app.add_url_rule('/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete',
                           view_func=api_delete_chapter_analysis, methods=['POST'])
            logger.info("成功注册删除章节分析结果API别名路由 (全局)")
        else:
            logger.info("删除章节分析结果API别名路由已存在，无需重复注册")

        # 检查是否启用小说分析API修复
        if os.environ.get('ENABLE_NOVEL_ANALYSIS_API_FIX') == 'True':
            logger.info("小说分析API修复已启用，注册小说分析API路由")

            # 注册小说分析API路由
            if not any(rule.rule == '/api/novel/<int:novel_id>/analysis' for rule in app.url_map.iter_rules()):
                app.add_url_rule('/api/novel/<int:novel_id>/analysis',
                               view_func=api_get_novel_all_analysis)
                logger.info("成功注册小说分析API主路由 (全局)")
            else:
                logger.info("小说分析API主路由已存在，无需重复注册")

            # 注册别名路由
            if not any(rule.rule == '/api/novels/<int:novel_id>/analysis' for rule in app.url_map.iter_rules()):
                app.add_url_rule('/api/novels/<int:novel_id>/analysis',
                               view_func=api_get_novel_all_analysis)
                logger.info("成功注册小说分析API别名路由 (全局)")
            else:
                logger.info("小说分析API别名路由已存在，无需重复注册")
    except ImportError as e:
        logger.error(f"导入API函数失败: {str(e)}")
    except Exception as e:
        logger.error(f"注册API路由时出错: {str(e)}")
# 检查是否启用章节删除功能
elif os.environ.get('ENABLE_CHAPTER_DELETE') == 'True':
    logger.info("章节删除功能已启用，注册删除章节分析结果API路由")
    # 添加删除章节分析结果API路由
    try:
        from src.web.routes.v3_api import api_delete_chapter_analysis

        # 注册主路由
        if not any(rule.rule == '/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete' for rule in app.url_map.iter_rules()):
            v3_api_bp.add_url_rule('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete',
                                  view_func=api_delete_chapter_analysis, methods=['POST'])
            logger.info("成功注册删除章节分析结果API主路由")
        else:
            logger.info("删除章节分析结果API主路由已存在，无需重复注册")

        # 注册别名路由
        if not any(rule.rule == '/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete' for rule in app.url_map.iter_rules()):
            v3_api_bp.add_url_rule('/api/novels/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/delete',
                                  view_func=api_delete_chapter_analysis, methods=['POST'])
            logger.info("成功注册删除章节分析结果API别名路由")
        else:
            logger.info("删除章节分析结果API别名路由已存在，无需重复注册")
    except ImportError as e:
        logger.error(f"导入api_delete_chapter_analysis函数失败: {str(e)}")
    except Exception as e:
        logger.error(f"注册删除章节分析结果API路由时出错: {str(e)}")

# 添加全局缓存控制
@app.after_request
def add_cache_control(response):
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

# 处理Chrome DevTools请求
@app.route('/.well-known/appspecific/com.chrome.devtools.json')
def chrome_devtools():
    """处理Chrome DevTools请求，避免404日志"""
    return jsonify({})

# 主页路由
@app.route('/')
def index():
    return redirect(url_for('v3.index'))

# 添加v3.0路径重定向到v3路径
@app.route('/v3.0/')
def v3_0_index_redirect():
    """将v3.0首页路径重定向到v3首页路径"""
    logger.info("将v3.0首页路径重定向到v3首页路径")
    return redirect('/v3/')

@app.route('/v3.0/<path:subpath>')
def v3_0_subpath_redirect(subpath):
    """将v3.0子路径重定向到v3子路径"""
    logger.info(f"将v3.0子路径重定向到v3子路径: {subpath}")
    return redirect(f'/v3/{subpath}')

@app.route('/novels')
def novels():
    return redirect(url_for('v3.novels'))

@app.route('/upload')
def upload_novel():
    return redirect(url_for('v3.upload_novel'))

@app.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    return redirect(url_for('v3.view_novel', novel_id=novel_id))

@app.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    return redirect(url_for('v3.analysis', novel_id=novel_id, dimension=dimension))

@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>')
def view_chapter(novel_id, chapter_id):
    return redirect(url_for('v3.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def chapter_analysis(novel_id, chapter_id, dimension):
    return redirect(url_for('v3.chapter_analysis', novel_id=novel_id, chapter_id=chapter_id, dimension=dimension))

@app.route('/novel/<int:novel_id>/chapters/summary')
def chapters_summary(novel_id):
    return redirect(url_for('v3.chapters_summary', novel_id=novel_id))

@app.route('/console')
def console_redirect():
    return redirect(url_for('v3.console'))

@app.route('/v3/console')
def v3_console():
    return redirect(url_for('v3.console'))

@app.route('/v3/v3.1/console')
def v3_1_console_redirect():
    """处理错误的v3.1版本的console请求路径"""
    logger.warning("检测到错误的URL路径: /v3/v3.1/console，这是一个无效的路径")
    # 返回错误页面，说明这是一个无效的路径
    return render_template('v3/error.html',
                          error_code=404,
                          error_message='无效的URL路径: /v3/v3.1/console。请使用正确的路径: /v3/console')

@app.errorhandler(404)
def not_found(error):
    if request.path.startswith('/api'):
        return jsonify({'success': False, 'error': '接口不存在'}), 404

    # 检查是否是错误的v3.1路径
    if '/v3.1/' in request.path or request.path.endswith('/v3.1'):
        logger.warning(f"检测到错误的v3.1路径: {request.path}")
        return render_template('v3/error.html',
                             error_code=404,
                             error_message='无效的URL路径',
                             error_details=f"您正在尝试访问v3.1版本的路径 ({request.path})，但当前运行的是v3.0版本。请使用正确的v3.0路径。")

    return render_template('v3/error.html', error_code=404, error_message='页面未找到'), 404

@app.errorhandler(500)
def internal_server_error(e):
    logger.error(f"500错误: {str(e)}", exc_info=True)
    return render_template('v3/error.html', error_code=500, error_message='服务器内部错误'), 500

if __name__ == '__main__':
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=False,
        threaded=True,
        use_reloader=False
    )