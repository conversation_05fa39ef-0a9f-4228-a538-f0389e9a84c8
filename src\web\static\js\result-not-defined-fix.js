/**
 * 九猫 - result变量未定义修复脚本
 * 用于修复"result is not defined"错误
 * 版本: 1.0.1
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('result变量未定义修复脚本已加载');
    
    // 在所有内联脚本执行前定义全局result变量
    if (typeof window.result === 'undefined') {
        // 创建一个默认的result对象
        window.result = { 
            status: 'ok',
            message: '默认result对象',
            data: {},
            success: true
        };
        console.log('全局result变量已预防性定义');
    }
    
    // 安全获取对象属性
    function safeGetProperty(obj, prop) {
        try {
            return obj[prop];
        } catch (e) {
            return undefined;
        }
    }
    
    // 安全设置对象属性
    function safeSetProperty(obj, prop, value) {
        try {
            obj[prop] = value;
            return true;
        } catch (e) {
            console.warn(`设置 ${prop} 属性时出错:`, e);
            return false;
        }
    }
    
    // 修复document.createElement方法，避免textContent访问错误
    function fixCreateElement() {
        if (typeof document.createElement !== 'function') return;
        
        // 保存原始方法
        const originalCreateElement = document.createElement;
        
        // 重写方法
        document.createElement = function(tagName) {
            // 使用原始方法创建元素
            const element = originalCreateElement.call(document, tagName);
            
            // 特别处理脚本元素
            if (typeof tagName === 'string' && tagName.toLowerCase() === 'script') {
                try {
                    // 安全处理textContent属性
                    let textContentValue = '';
                    
                    // 定义安全的textContent属性
                    Object.defineProperty(element, 'textContent', {
                        get: function() {
                            return textContentValue;
                        },
                        set: function(value) {
                            textContentValue = value;
                            try {
                                // 尝试使用原始方法设置文本内容
                                if (typeof originalSetText === 'function') {
                                    originalSetText.call(element, value);
                                } else {
                                    // 后备方法
                                    element.innerHTML = value;
                                }
                            } catch (e) {
                                console.warn('设置脚本文本内容时出错，使用后备方法');
                                // 通过innerHTML设置
                                try {
                                    element.innerHTML = value;
                                } catch (innerError) {
                                    console.warn('设置脚本innerHTML时出错');
                                }
                            }
                        },
                        enumerable: true,
                        configurable: true
                    });
                } catch (e) {
                    console.warn('定义脚本textContent属性时出错:', e);
                }
            }
            
            return element;
        };
    }
    
    // 在文档上添加事件处理，用于捕获result未定义错误
    document.addEventListener('error', function(event) {
        if (event.error && event.error.message && event.error.message.includes('result is not defined')) {
            console.log('捕获到result未定义错误，提供默认值');
            if (typeof window.result === 'undefined') {
                window.result = { 
                    status: 'ok',
                    message: '捕获错误后定义的result对象',
                    data: {},
                    success: true
                };
            }
        }
    }, true);
    
    // 拦截内联脚本，修复其中的result引用
    function fixInlineScripts() {
        try {
            const scripts = document.querySelectorAll('script:not([src])');
            scripts.forEach(function(script) {
                const content = script.textContent || script.innerText || '';
                if (content.includes('result') && !content.includes('window.result') && !content.includes('var result')) {
                    // 提取脚本内容
                    let newContent = content;
                    
                    // 替换以下模式：
                    // result.xxx 替换为 window.result.xxx
                    // if (result) 替换为 if (window.result)
                    // result = 替换为 window.result =
                    newContent = newContent.replace(/\bresult\b(?!\s*=\s*window\.result)/g, 'window.result');
                    
                    if (newContent !== content) {
                        // 创建新脚本元素
                        const newScript = document.createElement('script');
                        
                        // 安全设置新脚本的内容
                        safeSetProperty(newScript, 'textContent', newContent);
                        
                        // 如果原脚本有ID，复制ID
                        if (script.id) {
                            safeSetProperty(newScript, 'id', script.id);
                        }
                        
                        // 替换原脚本
                        if (script.parentNode) {
                            script.parentNode.replaceChild(newScript, script);
                            console.log('已修复内联脚本中的result引用');
                        }
                    }
                }
            });
        } catch (e) {
            console.error('修复内联脚本时出错:', e);
        }
    }
    
    // 在DOM加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            fixCreateElement();
            fixInlineScripts();
            console.log('result变量未定义修复已完成');
        });
    } else {
        fixCreateElement();
        fixInlineScripts();
        console.log('result变量未定义修复已完成');
    }
    
    // 导出修复方法
    window.ResultFixer = {
        fixInlineScripts: fixInlineScripts,
        fixCreateElement: fixCreateElement
    };
})(); 