<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{% block title %}九猫小说分析写作系统v3.0{% endblock %}</title>

    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="{{ url_for('static', filename='css/bootstrap-icons.css') }}" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js" as="script">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" as="script">

    <!-- 样式表 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" onerror="this.onerror=null;this.href='{{ url_for('static', filename='css/bootstrap.min.css') }}';">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap-icons.css') }}" onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css';">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" onerror="this.onerror=null;this.href='{{ url_for('static', filename='css/fontawesome.min.css') }}';">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/v3_style.css') }}?v=1.0.0">

    <!-- 添加v3.1阻止器脚本（必须放在最前面） -->
    <script src="{{ url_for('static', filename='js/v3.1-blocker.js') }}"></script>

    <!-- Bootstrap Icons 应急加载器 -->
    <script src="{{ url_for('static', filename='js/bootstrap-icons-emergency-loader.js') }}"></script>

    <!-- 内联jQuery - 确保jQuery在页面最早加载 -->
    <script>
    // 立即执行的内联jQuery
    (function() {
        console.log('[内联jQuery] 初始化...');

        // 全局变量，标记jQuery是否已加载
        window.jQueryLoaded = false;

        // 检查jQuery是否已加载
        if (typeof jQuery !== 'undefined') {
            console.log('[内联jQuery] jQuery已加载，版本:', jQuery.fn.jquery);

            // 确保$ 变量可用
            if (typeof $ === 'undefined') {
                console.log('[内联jQuery] $ 变量未定义，设置 $ = jQuery');
                window.$ = jQuery;
            }

            // 标记jQuery已加载
            window.jQueryLoaded = true;
            return;
        }

        console.log('[内联jQuery] jQuery未加载，创建内联jQuery');

        // 创建极简jQuery
        window.jQuery = function(selector) {
            if (typeof selector === 'function') {
                if (document.readyState !== 'loading') {
                    selector();
                } else {
                    document.addEventListener('DOMContentLoaded', selector);
                }
                return;
            }

            const elements = typeof selector === 'string'
                ? document.querySelectorAll(selector)
                : [selector];

            return {
                elements: elements,
                length: elements.length,
                each: function(callback) {
                    for (let i = 0; i < this.elements.length; i++) {
                        callback.call(this.elements[i], i, this.elements[i]);
                    }
                    return this;
                },
                html: function(content) {
                    if (content === undefined) {
                        return this.elements[0] ? this.elements[0].innerHTML : '';
                    }
                    this.each(function() {
                        this.innerHTML = content;
                    });
                    return this;
                }
            };
        };

        // 设置版本和fn
        jQuery.fn = {};
        jQuery.fn.jquery = '1.0.0-minimal';

        // 设置$别名
        window.$ = jQuery;

        // 标记jQuery已加载
        window.jQueryLoaded = true;

        console.log('[内联jQuery] 内联jQuery创建完成');
    })();
    </script>

    <!-- 全局错误处理脚本 -->
    <script src="{{ url_for('static', filename='js/global-error-handler.js') }}"></script>

    <!-- 添加jQuery本地加载器 -->
    <script src="{{ url_for('static', filename='js/jquery-local-loader.js') }}"></script>

    <!-- 添加jQuery修复脚本 -->
    <script src="{{ url_for('static', filename='js/jquery-fix.js') }}"></script>

    <!-- 添加jQuery .on() 方法直接修复脚本 -->
    <script src="{{ url_for('static', filename='js/jquery-on-direct-fix.js') }}"></script>

    <!-- 添加jQuery .on() 方法修复脚本 -->
    <script src="{{ url_for('static', filename='js/jquery-on-method-fix.js') }}"></script>

    <!-- 添加资源加载修复脚本 -->
    <script src="{{ url_for('static', filename='js/resource-loader-fix.js') }}"></script>

    <!-- 添加Bootstrap Bundle修复脚本 -->
    <script src="{{ url_for('static', filename='js/bootstrap-bundle-fix-loader.js') }}"></script>

    <!-- 添加Bootstrap自动检测器脚本 -->
    <script src="{{ url_for('static', filename='js/bootstrap-auto-detector.js') }}"></script>

    <!-- 添加内联jQuery脚本 -->
    <script src="{{ url_for('static', filename='js/inline-jquery.js') }}"></script>

    <!-- 添加全局标签页修复加载器 -->
    <script src="{{ url_for('static', filename='js/global-tab-fix-loader.js') }}"></script>

    {% block extra_css %}{% endblock %}
    {% block head %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-cat me-2"></i>九猫小说分析写作系统v3.0
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3.novels') }}">
                            <i class="fas fa-book me-1"></i>小说列表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3.upload_novel') }}">
                            <i class="fas fa-upload me-1"></i>上传小说
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3.reference_templates') }}">
                            <i class="fas fa-bookmark me-1"></i>参考蓝本
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3.console') }}">
                            <i class="fas fa-terminal me-1"></i>控制台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3.showcase_page') }}">
                            <i class="fas fa-chart-bar me-1"></i>展示台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3.content_repository_page') }}">
                            <i class="fas fa-archive me-1"></i>内容仓库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('v3.test_page') }}">
                            <i class="fas fa-flask me-1"></i>测试功能
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="{{ url_for('v3.help_page') }}" class="btn btn-outline-light me-2">
                        <i class="fas fa-question-circle me-1"></i>帮助
                    </a>
                    <a href="{{ url_for('v3.system_monitor') }}" class="btn btn-outline-light">
                        <i class="fas fa-tachometer-alt me-1"></i>系统监控
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container my-4">
        <!-- 闪现消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <footer class="footer mt-auto py-3">
        <div class="container text-center">
            <span>© 2025 九猫小说分析写作系统v3.0 | <a href="{{ url_for('v3.help_page') }}" class="text-decoration-none" style="color: var(--primary-color);">帮助中心</a></span>
        </div>
    </footer>

    <!-- 基础JS库 -->
    <script>
        // 确保jQuery已加载
        window.ensureJQuery(function() {
            console.log('jQuery已成功加载并准备就绪');
        });
    </script>

    <!-- 本地加载基础JS库 -->
    <script src="{{ url_for('static', filename='js/lib/jquery.min.js') }}" onerror="console.error('本地jQuery加载失败');"></script>
    <script src="{{ url_for('static', filename='js/lib/bootstrap.bundle.min.js') }}" onerror="window.fixBootstrapBundle && window.fixBootstrapBundle()"></script>
    <script src="{{ url_for('static', filename='js/lib/marked.min.js') }}" onerror="document.write('<script src=\'https://cdn.jsdelivr.net/npm/marked/marked.min.js\'><\/script>')"></script>

    <!-- 备用Bootstrap加载 -->
    <script>
        // 检查Bootstrap是否已加载
        setTimeout(function() {
            if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
                console.error('Bootstrap未能正确加载，尝试修复');
                if (window.fixBootstrapBundle) {
                    window.fixBootstrapBundle();
                } else {
                    // 直接加载CDN版本
                    var script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
                    script.integrity = 'sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz';
                    script.crossOrigin = 'anonymous';
                    document.head.appendChild(script);
                }
            }
        }, 1000);
    </script>

    <!-- jQuery加载状态检查 -->
    <script>
        // 检查jQuery是否已加载
        if (typeof jQuery === 'undefined') {
            console.error('jQuery未能加载，尝试最后修复');
            window.ensureJQuery();
        } else {
            console.log('jQuery已成功加载，版本:', jQuery.fn.jquery);
            // 确保$ 变量可用
            window.$ = jQuery;
        }
    </script>

    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/v3_main.js') }}?v=1.0.0"></script>

    <!-- 页面特定JS -->
    {% block scripts %}{% endblock %}
</body>
</html>
