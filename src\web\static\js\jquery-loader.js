/**
 * 九猫 - jQuery加载器
 * 确保jQuery正确加载并处理依赖
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('jQuery加载器已初始化');
    
    // jQuery CDN地址列表
    const jqueryCDNs = [
        '/static/js/jquery.min.js',  // 首先尝试本地版本
        'https://code.jquery.com/jquery-3.6.4.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js',
        'https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js',
        'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js'
    ];
    
    // 加载jQuery的函数
    function loadjQuery(callback) {
        // 如果jQuery已经加载，直接调用回调
        if (window.jQuery) {
            console.log('jQuery已加载，版本:', window.jQuery.fn.jquery);
            if (typeof callback === 'function') {
                callback(window.jQuery);
            }
            return;
        }
        
        console.log('jQuery未加载，开始尝试加载');
        
        // 记录加载尝试
        let loadAttempts = 0;
        let jQueryLoaded = false;
        
        // 从CDN列表中加载
        function tryNextCDN(index) {
            if (index >= jqueryCDNs.length) {
                console.error('所有jQuery CDN都加载失败，尝试使用应急jQuery替代品');
                useEmergencyJQuery(callback);
                return;
            }
            
            loadAttempts++;
            const url = jqueryCDNs[index];
            
            console.log(`尝试从 ${url} 加载jQuery (尝试 ${loadAttempts}/${jqueryCDNs.length})`);
            
            // 创建脚本标签
            const script = document.createElement('script');
            script.src = url;
            script.async = false;
            
            // 成功加载
            script.onload = function() {
                if (!jQueryLoaded && window.jQuery) {
                    jQueryLoaded = true;
                    console.log(`jQuery成功加载，版本: ${window.jQuery.fn.jquery}`);
                    
                    // 添加一个便捷的noConflict版本
                    window.$j = window.jQuery.noConflict(true);
                    
                    // 执行成功回调
                    if (typeof callback === 'function') {
                        callback(window.jQuery);
                    }
                }
            };
            
            // 加载失败，尝试下一个CDN
            script.onerror = function() {
                console.warn(`从 ${url} 加载jQuery失败，尝试下一个CDN`);
                
                // 延迟尝试下一个，避免连续请求
                setTimeout(function() {
                    if (!jQueryLoaded) {
                        tryNextCDN(index + 1);
                    }
                }, 200);
            };
            
            // 添加到文档
            document.head.appendChild(script);
        }
        
        // 开始尝试第一个CDN
        tryNextCDN(0);
    }
    
    // 使用应急内联jQuery替代品
    function useEmergencyJQuery(callback) {
        console.warn('使用应急jQuery替代品');
        
        // 如果jQuery已加载，直接返回
        if (window.jQuery) {
            if (typeof callback === 'function') {
                callback(window.jQuery);
            }
            return;
        }
        
        // 简单的jQuery替代品
        window.jQuery = window.$ = function(selector) {
            // 基本选择器处理
            const elements = typeof selector === 'string' 
                ? document.querySelectorAll(selector)
                : [selector];
            
            // 返回一个类似jQuery的对象
            const jQueryObject = {
                elements: elements,
                length: elements.length,
                
                ready: function(fn) {
                    if (document.readyState !== 'loading') {
                        fn();
                    } else {
                        document.addEventListener('DOMContentLoaded', fn);
                    }
                    return this;
                },
                
                on: function(event, handler) {
                    this.each(function() {
                        this.addEventListener(event, handler);
                    });
                    return this;
                },
                
                each: function(callback) {
                    for (let i = 0; i < this.elements.length; i++) {
                        callback.call(this.elements[i], i, this.elements[i]);
                    }
                    return this;
                },
                
                addClass: function(className) {
                    this.each(function() {
                        this.classList.add(className);
                    });
                    return this;
                },
                
                removeClass: function(className) {
                    this.each(function() {
                        this.classList.remove(className);
                    });
                    return this;
                },
                
                toggleClass: function(className) {
                    this.each(function() {
                        this.classList.toggle(className);
                    });
                    return this;
                },
                
                attr: function(attr, value) {
                    if (value === undefined) {
                        return this.elements[0] ? this.elements[0].getAttribute(attr) : null;
                    }
                    this.each(function() {
                        this.setAttribute(attr, value);
                    });
                    return this;
                },
                
                html: function(content) {
                    if (content === undefined) {
                        return this.elements[0] ? this.elements[0].innerHTML : "";
                    }
                    this.each(function() {
                        this.innerHTML = content;
                    });
                    return this;
                },
                
                text: function(content) {
                    if (content === undefined) {
                        return this.elements[0] ? this.elements[0].textContent : "";
                    }
                    this.each(function() {
                        this.textContent = content;
                    });
                    return this;
                },
                
                show: function() {
                    this.each(function() {
                        this.style.display = '';
                    });
                    return this;
                },
                
                hide: function() {
                    this.each(function() {
                        this.style.display = 'none';
                    });
                    return this;
                },
                
                find: function(selector) {
                    const results = [];
                    this.each(function() {
                        const found = this.querySelectorAll(selector);
                        for (let i = 0; i < found.length; i++) {
                            results.push(found[i]);
                        }
                    });
                    
                    // 创建新的jQuery对象包含结果
                    const newObj = jQuery(document.createDocumentFragment());
                    newObj.elements = results;
                    newObj.length = results.length;
                    
                    // 复制元素到索引
                    for (let i = 0; i < results.length; i++) {
                        newObj[i] = results[i];
                    }
                    
                    return newObj;
                },
                
                append: function(content) {
                    this.each(function() {
                        if (typeof content === 'string') {
                            this.insertAdjacentHTML('beforeend', content);
                        } else if (content.nodeType) {
                            this.appendChild(content);
                        } else if (content.elements) {
                            // 处理jQuery对象
                            for (let i = 0; i < content.elements.length; i++) {
                                this.appendChild(content.elements[i].cloneNode(true));
                            }
                        }
                    });
                    return this;
                },
                
                ajax: function(options) {
                    console.warn('jQuery.ajax()的简化版本已调用');
                    const xhr = new XMLHttpRequest();
                    xhr.open(options.type || 'GET', options.url, true);
                    
                    if (options.contentType) {
                        xhr.setRequestHeader('Content-Type', options.contentType);
                    }
                    
                    xhr.onload = function() {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            if (options.success) {
                                let response = xhr.responseText;
                                if (options.dataType === 'json') {
                                    try {
                                        response = JSON.parse(response);
                                    } catch (e) {
                                        console.error('JSON解析错误', e);
                                    }
                                }
                                options.success(response, xhr.statusText, xhr);
                            }
                        } else if (options.error) {
                            options.error(xhr, xhr.statusText);
                        }
                    };
                    
                    xhr.onerror = function() {
                        if (options.error) {
                            options.error(xhr, xhr.statusText);
                        }
                    };
                    
                    xhr.send(options.data);
                    
                    return {
                        done: function(callback) {
                            xhr.addEventListener('load', function() {
                                if (xhr.status >= 200 && xhr.status < 300) {
                                    callback(xhr.responseText, xhr.statusText, xhr);
                                }
                            });
                            return this;
                        },
                        fail: function(callback) {
                            xhr.addEventListener('error', function() {
                                callback(xhr, xhr.statusText);
                            });
                            return this;
                        }
                    };
                }
            };
            
            // 使基本属性访问可用
            for (let i = 0; i < elements.length; i++) {
                jQueryObject[i] = elements[i];
            }
            
            return jQueryObject;
        };
        
        // 添加静态方法
        window.jQuery.fn = { jquery: '3.6.4-emergency' };
        
        window.jQuery.each = function(obj, callback) {
            if (Array.isArray(obj)) {
                for (let i = 0; i < obj.length; i++) {
                    callback.call(obj[i], i, obj[i]);
                }
            } else {
                for (let key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        callback.call(obj[key], key, obj[key]);
                    }
                }
            }
            return obj;
        };
        
        window.jQuery.ajax = window.jQuery().ajax;
        
        // 执行回调
        if (typeof callback === 'function') {
            callback(window.jQuery);
        }
        
        console.log('应急jQuery替代品已加载，版本:', window.jQuery.fn.jquery);
    }
    
    // 在DOMContentLoaded后加载jQuery
    function initJQuery() {
        loadjQuery(function(jQuery) {
            console.log('jQuery加载完成，准备加载Bootstrap');
            
            // 触发自定义事件，通知其他脚本jQuery已加载
            document.dispatchEvent(new CustomEvent('jQueryReady', { detail: jQuery }));
            
            // 加载Bootstrap
            if (!window.bootstrap) {
                // 先查找Bootstrap加载器
                if (typeof window.loadBootstrap === 'function') {
                    window.loadBootstrap();
                } else {
                    console.log('未找到Bootstrap加载器，尝试直接加载Bootstrap');
                    
                    // 加载Bootstrap
                    const bootstrapScript = document.createElement('script');
                    bootstrapScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
                    document.head.appendChild(bootstrapScript);
                    
                    bootstrapScript.onload = function() {
                        console.log('Bootstrap已加载');
                        document.dispatchEvent(new CustomEvent('bootstrapReady'));
                    };
                }
            }
        });
    }
    
    // 在页面加载完成后初始化jQuery（如果尚未加载）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initJQuery);
    } else {
        initJQuery();
    }
    
    // 导出到全局命名空间
    window.jQueryLoader = {
        loadjQuery: loadjQuery,
        useEmergencyJQuery: useEmergencyJQuery
    };
})(); 