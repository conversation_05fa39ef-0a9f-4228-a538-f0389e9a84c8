<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第1章 她爬到了修仙界 - 九猫</title>

    <!-- 尝试多种路径加载Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/lib/bootstrap.min.css" onerror="this.onerror=null;this.href='/direct-static/css/lib/bootstrap.min.css'">

    <!-- 如果本地加载失败，尝试CDN -->
    <script>
        // 检查Bootstrap CSS是否已加载
        setTimeout(function() {
            var bootstrapLoaded = false;
            var styles = document.styleSheets;
            for (var i = 0; i < styles.length; i++) {
                if (styles[i].href && styles[i].href.indexOf('bootstrap') > -1) {
                    bootstrapLoaded = true;
                    break;
                }
            }

            if (!bootstrapLoaded) {
                console.warn('本地Bootstrap CSS加载失败，尝试CDN');
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css';
                document.head.appendChild(link);
            }
        }, 1000);
    </script>

    <link rel="stylesheet" href="/static/css/style.css" onerror="this.onerror=null;this.href='/direct-static/css/style.css';console.error('样式文件加载失败')">
    
<style>
    .novel-excerpt {
        max-height: 300px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        font-size: 0.9rem;
        white-space: pre-wrap;
    }

    .analysis-card {
        height: 100%;
    }

    .analysis-content {
        max-height: 200px;
        overflow-y: auto;
    }

    .analysis-progress {
        margin-top: 10px;
        margin-bottom: 15px;
    }

    .analysis-status {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        color: #666;
        margin-top: 5px;
    }

    .analysis-visualization {
        margin-top: 15px;
        height: 150px;
        background-color: #f8f9fa;
        border-radius: 5px;
        overflow: hidden;
    }

    .analysis-chart {
        width: 100%;
        height: 100%;
    }

    /* 控制台样式 */
    .console-output {
        background-color: #1e1e1e;
        color: #f0f0f0;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.85rem;
        height: 300px;
        overflow-y: auto;
        padding: 10px;
        border-radius: 0 0 5px 5px;
    }

    .console-inner {
        padding: 5px;
    }

    .console-welcome {
        color: #888;
        margin-bottom: 10px;
    }

    .console-line {
        margin: 2px 0;
        line-height: 1.4;
        white-space: pre-wrap;
        word-break: break-word;
    }

    .log-debug {
        color: #888;
    }

    .log-info {
        color: #4CAF50;
    }

    .log-warning {
        color: #FFC107;
    }

    .log-error {
        color: #F44336;
    }

    .log-timestamp {
        color: #888;
        margin-right: 5px;
    }

    .log-dimension {
        color: #2196F3;
        font-weight: bold;
        margin-right: 5px;
    }

    .log-progress {
        color: #9C27B0;
        margin-right: 5px;
    }
</style>


    <!-- 预加载核心JS资源，确保快速可用 -->
    <link rel="preload" href="/static/js/lib/jquery-3.6.0.min.js" as="script">
    <link rel="preload" href="/static/js/lib/bootstrap.bundle.min.js" as="script">
    <link rel="prefetch" href="/static/js/lib/chart.min.js" as="script">

    <!-- 备用预加载 -->
    <link rel="preload" href="/direct-static/js/lib/jquery-3.6.0.min.js" as="script">
    <link rel="preload" href="/direct-static/js/lib/bootstrap.bundle.min.js" as="script">
    <link rel="prefetch" href="/direct-static/js/lib/chart.min.js" as="script">
<script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';console.error('错误处理脚本加载失败，尝试备用路径')"></script>
    </head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <span class="fs-4">九猫</span>
                <small class="text-muted">小说文本分析系统</small>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/upload">上传小说</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        
            
        

        
<div class="row"
     data-novel-id="4"
     data-novel-title="第1章 她爬到了修仙界"
     id="novel-container">
    <div class="col-md-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">首页</a></li>
                <li class="breadcrumb-item active">第1章 她爬到了修仙界</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <h1>第1章 她爬到了修仙界</h1>
            <div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                    分析小说
                </button>
                <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                    删除
                </button>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">小说信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>作者：</strong> 未知</p>
                        <p><strong>字数：</strong> 2643</p>
                        <p><strong>上传时间：</strong> 2025-04-27 16:50</p>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">小说摘要</h5>
                    </div>
                    <div class="card-body">
                        <div class="novel-excerpt">
                            第1章 她爬到了修仙界

注意看，眼前这个满脸呆滞的少女名叫桑念，她因为论文查重百分之零当场阴暗爬行，却不慎爬到了修仙界。


现在，她正面临有史以来最大的危机。


“你今日不杀我，来日我必定将你剜心剖骨，挫骨扬灰。”


桑念被这句话里的寒意激得打了个冷颤。


她下意识循着声音望去。


烛光暧昧摇晃。


千金难寻的鲛绡帐中，少年乌发散乱，双手被红绳金玲缚在床头，眼尾洇开薄薄的胭脂色，瞳若点漆，仿佛即将勾人魂魄的妖。


桑念：“……？”


她看看自己脱了一半的衣衫，又看看已经被扒得差不多的他，宕机的大脑艰难启动。


两秒后，问号变成了感叹号。


她火速从他身上爬下来，手忙脚乱的拢了件衣裳，无头苍蝇般跌跌撞撞往外跑。


“小姐？”听到动静，门口的丫鬟春儿不明所以，“您怎么出来了？”


桑念还没说话，她似乎明白了什么，又伸手轻轻一推，将她给推了回去，笑道：


“小姐别害羞，我们不守在这里就是了，你当心误了吉时，圆房要紧。”


“吱嘎——”


门关上，顺便锁死。


春儿得意的声音隐隐飘来，“这次看他还怎么跑。”


屋子里，桑念拎着自己松垮的衣襟，望向双手被红绳缚住的少年。


四目相对，死一样的寂静。


桑念绝望地闭上眼。


就在三分钟前，她还在宿舍尖叫扭曲蠕动外加阴暗爬行。


然而，不知是哪个环节出了错——


她爬到了修仙界。


准确的说，是一本以修仙为背景的小说里。


面前这位，正是本书女主的白月光，男配谢沉舟。


书中写到，他从小父母双亡，自己还会时不时精神失常。


因为长得好看，他一路走来，被女配强抢，被男配强抢，被女主强抢，被想强抢女主的男主强抢，怎一个惨字了得。


总而言之，这是一个心理健康和精神状态都很堪忧的倒霉蛋。


桑念没能穿成这本书的女主。


她是女配。


那个强抢谢沉舟并等新鲜劲儿过去了便对他动辄打骂百般羞辱，全方位无死角给予他身心重创活生生把他作成了疯批的恶毒女配，桑蕴灵。


当然，这样做的后果是——


他宁愿同归于尽也要一剑将她捅个对穿，切成整整齐齐的九九八十一片。


想到这里，桑念默默在心中为自己点了一首大悲羊，试图提前超度自己。


“剜心剖骨，挫骨扬灰，谢沉舟确实做到了。”

一道稚嫩的声音在桑念脑海中响起。


桑念警惕，“你是谁？”


“自我介绍一下，我，世界上最伟大的六六，主神的嫡系统，小米与瓜子杀手，艺术家，歌唱家，小鸡教教主，榴门高级信徒，还是……”


桑念倒吸一口凉气：“这么多人都在我脑袋里吗？”


六六：“。”


【叮咚~您的系统已下线】


一道提示音后，系统彻底息声，任凭她怎么呼叫也没反应，她只能暂时作罢。


现在这场面比论文查重百分之零还要刺激，桑念很担心自己会不小心厥过去。


以原主目前的身体素质来看，很有可能。


——原主桑蕴灵，青州城城主唯一的妹妹，身患心疾，素质极差。


注：此处的素质不单指身体素质。


因为生病，她从小被家里人娇纵得嚣张跋扈，看谁不爽就抽谁。

主打一个抽死他人绝不委屈自己。


桑念揉了揉一阵阵发晕的脑袋，扶着桌子坐下，给自己倒了杯热茶。


没喝两口，她想起床上的人，小心翼翼的问他：


“你喝吗？”


谢沉舟没说话，继续用让人毛骨悚然眼神看她。


桑念便小口小口喝干净杯子里的水，等头没那么晕了，目不斜视的起身走向他。


她抬起手。


少年死死盯着她，眸中盛满阴戾。


柔软的织物轻飘飘落下。


融融暖意逐渐弥漫全身。


他眼里多了一丝错愕。


桑念弯着腰给他解手上的红绳，指尖努力避开绳子上挂着的金铃，语速飞快：


“我放你走，以后咱们就是陌生人，谁也不认识谁。”


谢沉舟低眉凝着她鼻尖，半晌，轻嗤：

“你又想玩什么花样？”


桑念道：“你就当我脑子坏掉了吧。”


绳子不知道系的什么结，她怎么都解不开，期间不小心碰到他的手，烫的吓人。


她干脆放弃，在屋子里转了两圈，终于从抽屉里翻出一把剪刀。


可直到虎口都磨红了，那根绳子依旧毫发无伤。

桑念仔细翻找原主的记忆，终于想起来，这是特制的绳子。

上面下了禁制，剪不断解不开，需要口诀。


原主也不知道口诀是什么。

桑念拍拍手上不存在的灰，重新站起来，对谢沉舟道：

“要不然你再等等，我去叫人来帮忙。”


谢沉舟没应声，身体抖得厉害。


她吓了一跳，“你怎么了？”


他瞥她一眼，嘴角弧度讥讽，“你不知道我怎么了？”


桑念语塞。


——怕谢沉舟不从，进洞房前她哥结结实实灌了他三瓶迷情丹。


怪不得身上那么烫。


她心累得无以复加，不敢看他欲色浓重的眼，更不敢看他发生变化的身体：


“你忍忍，我去找人。”


谢沉舟：“……过来。”


桑念委婉道：“还是不了吧。”


“这不是你想...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="mt-5">分析结果</h2>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                        分析小说
                    </button>
                </div>

                <!-- 添加控制台日志显示区域 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center bg-dark text-white">
                        <h5 class="mb-0">分析控制台</h5>
                        <div>
                            <select class="form-select form-select-sm" id="console-log-level">
                                <option value="all">所有级别</option>
                                <option value="info">信息</option>
                                <option value="warning">警告</option>
                                <option value="error">错误</option>
                                <option value="debug">调试</option>
                            </select>
                            <button class="btn btn-sm btn-outline-light ms-2" id="clear-console">清空</button>
                            <button class="btn btn-sm btn-outline-light ms-2" id="copy-console">复制</button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="analysis-console" class="console-output">
                            <div class="console-inner">
                                <div class="console-welcome">
                                    <span class="text-muted">// 分析控制台已准备就绪，等待分析开始...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analysis-section mt-3">
                    <!-- 这里将显示所有维度的当前状态表格 -->
                    <!-- displayAllDimensions() 函数将在这里添加一个表格 -->
                </div>

                
                    <div class="row mt-4">
                        
                            <div class="col-md-4 mb-4">
                                <div class="card analysis-card" data-dimension="novel_characteristics">
                                    <div class="card-header">
                                        <h5 class="card-title">
                                            
                                                小说特点
                                            
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="badge bg-success">分析完成</span>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                                            data-dimension="novel_characteristics"
                                                            data-novel-id="4">
                                                        <i class="fas fa-sync"></i> 重新分析
                                                    </button>
                                                    <a href="/novel/4/analysis/novel_characteristics" class="btn btn-sm btn-outline-primary">查看详情</a>
                                                </div>
                                            </div>
                                            <div class="analysis-visualization">
                                                <canvas class="analysis-chart" data-dimension="novel_characteristics" width="400" height="250"></canvas>
                                            </div>
                                            <div class="analysis-excerpt mt-2">
                                                分析过程中出错，无法获取有效结果
                                            </div>
                                        
                                    </div>
                                </div>
                            </div>
                        
                            <div class="col-md-4 mb-4">
                                <div class="card analysis-card" data-dimension="chapter_outline">
                                    <div class="card-header">
                                        <h5 class="card-title">
                                            
                                                章节大纲
                                            
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="badge bg-success">分析完成</span>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                                            data-dimension="chapter_outline"
                                                            data-novel-id="4">
                                                        <i class="fas fa-sync"></i> 重新分析
                                                    </button>
                                                    <a href="/novel/4/analysis/chapter_outline" class="btn btn-sm btn-outline-primary">查看详情</a>
                                                </div>
                                            </div>
                                            <div class="analysis-visualization">
                                                <canvas class="analysis-chart" data-dimension="chapter_outline" width="400" height="250"></canvas>
                                            </div>
                                            <div class="analysis-excerpt mt-2">
                                                分析过程中出错，无法获取有效结果
                                            </div>
                                        
                                    </div>
                                </div>
                            </div>
                        
                            <div class="col-md-4 mb-4">
                                <div class="card analysis-card" data-dimension="paragraph_length">
                                    <div class="card-header">
                                        <h5 class="card-title">
                                            
                                                段落长度
                                            
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="badge bg-success">分析完成</span>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                                            data-dimension="paragraph_length"
                                                            data-novel-id="4">
                                                        <i class="fas fa-sync"></i> 重新分析
                                                    </button>
                                                    <a href="/novel/4/analysis/paragraph_length" class="btn btn-sm btn-outline-primary">查看详情</a>
                                                </div>
                                            </div>
                                            <div class="analysis-visualization">
                                                <canvas class="analysis-chart" data-dimension="paragraph_length" width="400" height="250"></canvas>
                                            </div>
                                            <div class="analysis-excerpt mt-2">
                                                分析过程中出错，无法获取有效结果。请检查API连接和响应。
                                            </div>
                                        
                                    </div>
                                </div>
                            </div>
                        
                            <div class="col-md-4 mb-4">
                                <div class="card analysis-card" data-dimension="world_building">
                                    <div class="card-header">
                                        <h5 class="card-title">
                                            
                                                世界构建
                                            
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="badge bg-success">分析完成</span>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                                            data-dimension="world_building"
                                                            data-novel-id="4">
                                                        <i class="fas fa-sync"></i> 重新分析
                                                    </button>
                                                    <a href="/novel/4/analysis/world_building" class="btn btn-sm btn-outline-primary">查看详情</a>
                                                </div>
                                            </div>
                                            <div class="analysis-visualization">
                                                <canvas class="analysis-chart" data-dimension="world_building" width="400" height="250"></canvas>
                                            </div>
                                            <div class="analysis-excerpt mt-2">
                                                这是一个融合现代幽默与古典修仙元素的穿越故事，作者通过多重手法构建了一个充满戏剧张力的奇幻世界。以下从五个维度进行深入分析：

### 一、空间结构的嵌套性构建
作者通过三层空间架构形成世界观冲击：
1. **现实锚点**：以"论文查重百分之零"的现代学术焦虑场景作为穿越触发点，形成强烈的现实代入感。通过"18°空调房""古拉拉黑暗之神"等当代符号，建立与读者的共情桥梁。
2. **异界空间**：
   - 物理环境：烛光摇曳的洞房（鲛绡帐/红绳金铃）暗示修真界的奢靡
   - 权力结构：青州城主妹妹的身份揭示等级森严的修真社会
3. **系统空间**：六六系统的存在形成超现实维度，"虐文女主...
                                            </div>
                                        
                                    </div>
                                </div>
                            </div>
                        
                            <div class="col-md-4 mb-4">
                                <div class="card analysis-card" data-dimension="language_style">
                                    <div class="card-header">
                                        <h5 class="card-title">
                                            
                                                语言风格
                                            
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="badge bg-success">分析完成</span>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                                            data-dimension="language_style"
                                                            data-novel-id="4">
                                                        <i class="fas fa-sync"></i> 重新分析
                                                    </button>
                                                    <a href="/novel/4/analysis/language_style" class="btn btn-sm btn-outline-primary">查看详情</a>
                                                </div>
                                            </div>
                                            <div class="analysis-visualization">
                                                <canvas class="analysis-chart" data-dimension="language_style" width="400" height="250"></canvas>
                                            </div>
                                            <div class="analysis-excerpt mt-2">
                                                以下是对该小说文本的详细语言风格分析：

### 一、现代网络语境与古典修仙元素的混搭
1. **次元碰撞式用词**：
- "论文查重百分之零当场阴暗爬行"：将学术焦虑（论文查重）与网络流行梗（阴暗爬行）直接嫁接于修仙场景，形成荒诞的时空错位感。"大悲羊"谐音梗更是强化了这种互联网语言狂欢特质。
- "古拉拉黑暗之神大战青青草原黑皮体育生"：通过无厘头的跨作品角色拼贴（巴啦啦小魔仙+喜羊羊与灰太狼），凸显主角的沙雕宅女属性。

2. **解构式古典描写**：
- "鲛绡帐""红绳金玲"等传统意象与"play的一环""发布会"等现代概念形成互文，如"圆房要紧"与系统提示音【叮咚~】形成仪式感与机...
                                            </div>
                                        
                                    </div>
                                </div>
                            </div>
                        
                            <div class="col-md-4 mb-4">
                                <div class="card analysis-card" data-dimension="rhythm_pacing">
                                    <div class="card-header">
                                        <h5 class="card-title">
                                            
                                                节奏与节奏
                                            
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="badge bg-success">分析完成</span>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                                            data-dimension="rhythm_pacing"
                                                            data-novel-id="4">
                                                        <i class="fas fa-sync"></i> 重新分析
                                                    </button>
                                                    <a href="/novel/4/analysis/rhythm_pacing" class="btn btn-sm btn-outline-primary">查看详情</a>
                                                </div>
                                            </div>
                                            <div class="analysis-visualization">
                                                <canvas class="analysis-chart" data-dimension="rhythm_pacing" width="400" height="250"></canvas>
                                            </div>
                                            <div class="analysis-excerpt mt-2">
                                                ### 节奏分析（逐字逐句解析）

---

#### **1. 开篇：快速引入冲突与悬念**
- **段落长度**：首段仅一句话，短促有力，通过“注意看”的引导式语言直接拉近读者距离，迅速交代主角“桑念”穿越的荒诞原因（论文查重0%→阴暗爬行→穿越修仙界）。  
- **句子节奏**：短句为主（如“现在，她正面临有史以来最大的危机”），配合情节的荒诞性（穿越逻辑），形成跳跃式推进，营造紧迫感。  
- **情节速度**：极速展开核心矛盾——桑念穿越至危险情境（与谢沉舟的洞房现场），省略背景铺垫，直接切入高潮场景，符合网文快节奏开篇模式。

---

#### **2. 场景描写：张弛交替的氛...
                                            </div>
                                        
                                    </div>
                                </div>
                            </div>
                        
                            <div class="col-md-4 mb-4">
                                <div class="card analysis-card" data-dimension="character_relationships">
                                    <div class="card-header">
                                        <h5 class="card-title">
                                            
                                                人物关系
                                            
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="badge bg-success">分析完成</span>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-secondary me-1 reanalyze-btn"
                                                            data-dimension="character_relationships"
                                                            data-novel-id="4">
                                                        <i class="fas fa-sync"></i> 重新分析
                                                    </button>
                                                    <a href="/novel/4/analysis/character_relationships" class="btn btn-sm btn-outline-primary">查看详情</a>
                                                </div>
                                            </div>
                                            <div class="analysis-visualization">
                                                <canvas class="analysis-chart" data-dimension="character_relationships" width="400" height="250"></canvas>
                                            </div>
                                            <div class="analysis-excerpt mt-2">
                                                # 分析过程中出错

## 错误详情
分析维度 **character_relationships** 时遇到了问题。

## 错误信息
```
name 'stats_start' is not defined
```

## 建议操作
请尝试以下解决方法：
1. 刷新页面并重新尝试分析
2. 检查小说文本是否过长或包含特殊字符
3. 确认API连接正常

如果问题持续存在，请联系系统管理员。

                                            </div>
                                        
                                    </div>
                                </div>
                            </div>
                        
                    </div>
                
            </div>
        </div>
    </div>
</div>

<!-- 分析模态框 -->
<div class="modal fade" id="analyzeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择分析维度</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="/novel/4/analyze" id="analyze-form" data-novel-id="4">
                <div class="modal-body">
                    <p>请选择要分析的维度：</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="select-all-dimensions">
                        <label class="form-check-label" for="select-all-dimensions">
                            <strong>全选</strong>
                        </label>
                    </div>
                    <hr>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="language_style" id="dimension-1">
                            <label class="form-check-label" for="dimension-1">
                                language_style
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="rhythm_pacing" id="dimension-2">
                            <label class="form-check-label" for="dimension-2">
                                rhythm_pacing
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="structure" id="dimension-3">
                            <label class="form-check-label" for="dimension-3">
                                structure
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="sentence_variation" id="dimension-4">
                            <label class="form-check-label" for="dimension-4">
                                sentence_variation
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="paragraph_length" id="dimension-5">
                            <label class="form-check-label" for="dimension-5">
                                paragraph_length
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="perspective_shifts" id="dimension-6">
                            <label class="form-check-label" for="dimension-6">
                                perspective_shifts
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="paragraph_flow" id="dimension-7">
                            <label class="form-check-label" for="dimension-7">
                                paragraph_flow
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="novel_characteristics" id="dimension-8">
                            <label class="form-check-label" for="dimension-8">
                                novel_characteristics
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="world_building" id="dimension-9">
                            <label class="form-check-label" for="dimension-9">
                                world_building
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="chapter_outline" id="dimension-10">
                            <label class="form-check-label" for="dimension-10">
                                chapter_outline
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="character_relationships" id="dimension-11">
                            <label class="form-check-label" for="dimension-11">
                                character_relationships
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="opening_effectiveness" id="dimension-12">
                            <label class="form-check-label" for="dimension-12">
                                opening_effectiveness
                            </label>
                        </div>
                    
                        <div class="form-check">
                            <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="climax_pacing" id="dimension-13">
                            <label class="form-check-label" for="dimension-13">
                                climax_pacing
                            </label>
                        </div>
                    
                    <div class="alert alert-warning mt-3">
                        <small>注意：分析过程可能需要较长时间，尤其是对于长篇小说。</small>
                    </div>

                    <div class="form-check form-switch mt-3">
                        <input class="form-check-input" type="checkbox" id="parallel-analysis" name="parallel_analysis" checked>
                        <label class="form-check-label" for="parallel-analysis">
                            <strong>启用并行分析</strong> (同时分析多个维度，提高效率)
                        </label>
                    </div>

                    <div class="form-check form-switch mt-2">
                        <input class="form-check-input" type="checkbox" id="use-cache" name="use_cache" checked>
                        <label class="form-check-label" for="use-cache">
                            <strong>使用缓存结果</strong> (如果有缓存，直接使用，避免重复分析)
                        </label>
                    </div>

                    <div class="form-check form-switch mt-2">
                        <input class="form-check-input" type="checkbox" id="force-refresh" name="force_refresh">
                        <label class="form-check-label" for="force-refresh">
                            <strong>强制刷新缓存</strong> (忽略现有缓存，重新分析)
                        </label>
                    </div>

                    <div class="mt-4">
                        <label for="model-select" class="form-label"><strong>选择分析模型</strong></label>
                        <select class="form-select" id="model-select" name="model">
                            <option value="deepseek-r1" selected>阿里云 DeepSeek R1 (默认)</option>
                            <option value="qwen-plus-latest">阿里云 通义千问-Plus-Latest</option>
                        </select>
                        <div class="form-text">选择不同的模型可能会产生不同的分析结果</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">开始分析</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除小说"第1章 她爬到了修仙界"吗？此操作不可撤销，所有相关的分析结果也将被删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form method="POST" action="/novel/4/delete">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">九猫小说文本分析系统 &copy; 2025</span>
            <p class="small text-muted">基于阿里云 DeepSeek R1 的小说深度分析工具</p>
        </div>
    </footer>

    <!-- 直接加载核心JS库 -->
    <script src="/static/js/lib/jquery-3.6.0.min.js" onerror="this.onerror=null;this.src='/direct-static/js/lib/jquery-3.6.0.min.js';console.error('jQuery加载失败，尝试备用路径')"></script>
    <script src="/static/js/lib/bootstrap.bundle.min.js" onerror="this.onerror=null;this.src='/direct-static/js/lib/bootstrap.bundle.min.js';console.error('Bootstrap JS加载失败，尝试备用路径')"></script>

    <!-- 按需加载Chart.js -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 只在需要时加载Chart.js
            if (document.querySelector('.chart-container') || document.querySelector('.analysis-chart')) {
                var chartScript = document.createElement('script');
                chartScript.src = "/static/js/lib/chart.min.js";
                chartScript.onerror = function() {
                    console.error('Chart.js加载失败，尝试备用路径');
                    this.onerror = null;
                    this.src = '/direct-static/js/lib/chart.min.js';
                };
                document.head.appendChild(chartScript);
                console.log('已加载Chart.js');

                // 如果页面上有初始化图表的函数，调用它
                chartScript.onload = function() {
                    if (typeof window.initializeCharts === 'function') {
                        setTimeout(function() {
                            window.initializeCharts();
                        }, 100);
                    }
                };
            }
        });
    </script>

    <!-- 项目主JS文件 -->
    <script src="/static/js/main.js" onerror="this.onerror=null;this.src='/direct-static/js/main.js';console.error('主JS文件加载失败，尝试备用路径')"></script>

    <!-- 初始化Bootstrap组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查Bootstrap是否已加载
            if (typeof bootstrap !== 'undefined') {
                console.log('初始化Bootstrap组件');
                try {
                    // 初始化工具提示
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    if (bootstrap.Tooltip) {
                    tooltipTriggerList.map(function(tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                        console.log('Tooltip 组件初始化成功');
                    } else {
                        console.warn('Bootstrap Tooltip 未定义，跳过初始化');
                    }

                    // 初始化弹出框
                    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                    if (bootstrap.Popover) {
                    popoverTriggerList.map(function(popoverTriggerEl) {
                        return new bootstrap.Popover(popoverTriggerEl);
                    });
                        console.log('Popover 组件初始化成功');
                    } else {
                        console.warn('Bootstrap Popover 未定义，跳过初始化');
                    }
                } catch (e) {
                    console.error('初始化Bootstrap组件时出错: ' + e);
                }
            } else {
                console.warn('Bootstrap未加载，跳过组件初始化');
            }
        });
    </script>

    <!-- 全局错误处理 -->
    <script>
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('JS错误:', message, '来源:', source, '行:', lineno, '列:', colno);
            return true; // 阻止默认错误处理
        };
    </script>

    
<script>
    // 从HTML元素获取模板变量
    const container = document.getElementById('novel-container');
    const novelIdFromTemplate = container ? container.getAttribute('data-novel-id') : null;
    const novelTitleFromTemplate = container ? container.getAttribute('data-novel-title') : null;

    // 脚本加载函数
    function loadScript(url, fallbacks, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;

        // 成功加载
        script.onload = function() {
            console.log('成功加载脚本: ' + url);
            if (callback) callback(null, url);
        };

        // 加载失败，尝试备用URL
        script.onerror = function() {
            console.warn('脚本加载失败: ' + url);
            if (fallbacks && fallbacks.length > 0) {
                var nextUrl = fallbacks.shift();
                console.log('尝试备用URL: ' + nextUrl);
                loadScript(nextUrl, fallbacks, callback);
            } else {
                console.error('所有URL都加载失败');
                if (callback) callback(new Error('所有URL都加载失败'), null);
            }
        };

        document.head.appendChild(script);
    }

    // 加载分析页面所需的JS文件
    function loadAnalysisScripts() {
        // 创建一个函数来加载脚本
        function loadScript(url, fallbackUrls, callback) {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = url;

            // 成功加载
            script.onload = function() {
                console.log('成功加载脚本: ' + url);
                if (callback) callback(null, url);
            };

            // 加载失败，尝试备用URL
            script.onerror = function() {
                console.warn('脚本加载失败: ' + url);
                if (fallbackUrls && fallbackUrls.length > 0) {
                    var nextUrl = fallbackUrls.shift();
                    console.log('尝试备用URL: ' + nextUrl);
                    loadScript(nextUrl, fallbackUrls, callback);
                } else {
                    console.error('所有URL都加载失败');
                    if (callback) callback(new Error('所有URL都加载失败'), null);
                }
            };

            document.head.appendChild(script);
        }

        // 加载Chart.js加载器
        loadScript(
            "/static/js/chart-loader.js",
            ['/direct-static/js/chart-loader.js'],
            function(error, url) {
                if (error) {
                    console.error('Chart.js加载器加载失败: ' + error);
                } else {
                    console.log('Chart.js加载器加载成功');
                }
            }
        );

        // 加载控制台日志JS
        loadScript(
            "/static/js/console-logger.js",
            ['/direct-static/js/console-logger.js'],
            function(error, url) {
                if (error) {
                    console.error('控制台日志JS加载失败: ' + error);
                    return;
                }

                console.log('控制台日志JS加载成功');

                // 如果jQuery已加载，初始化控制台日志
                if (typeof jQuery !== 'undefined' && typeof initConsoleLogger === 'function') {
                    try {
                        initConsoleLogger(novelIdFromTemplate, novelTitleFromTemplate);
                        console.log('控制台日志初始化成功');
                    } catch (e) {
                        console.error('初始化控制台日志时出错: ' + e);
                    }
                } else {
                    console.warn('jQuery或控制台日志函数未加载，无法初始化控制台');
                }
            }
        );

        // 加载小说分析JS
        loadScript(
            "/static/js/novel-analysis.js",
            ['/direct-static/js/novel-analysis.js'],
            function(error, url) {
                if (error) {
                    console.error('小说分析JS加载失败: ' + error);
                    return;
                }

                console.log('小说分析JS加载成功');

                // 如果页面上有初始化图表的函数，调用它
                if (typeof window.initializeCharts === 'function') {
                    setTimeout(function() {
                        try {
                            window.initializeCharts();
                            console.log('图表初始化成功');
                        } catch (e) {
                            console.error('初始化图表时出错: ' + e);
                        }
                    }, 500);
                }
            }
        );
    }

    // 当DOM加载完成或jQuery加载完成后，加载分析脚本
    if (typeof jQuery !== 'undefined') {
        $(document).ready(function() {
            loadAnalysisScripts();
        });
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            loadAnalysisScripts();
        });
    }

    // 错误处理函数，防止JS错误导致整个页面失效
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('捕获到JS错误:', message, '在行:', lineno, '列:', colno);
        // 防止错误中断其他功能
        return true;
    };

    // 初始化所有进度条宽度
    function initProgressBars() {
        console.log('初始化所有进度条宽度');
        const progressBars = document.querySelectorAll('.progress-bar[data-progress]');
        console.log('找到进度条元素数量:', progressBars.length);
        progressBars.forEach(bar => {
            const progress = parseInt(bar.getAttribute('data-progress') || '0');
            const minDisplay = parseInt(bar.getAttribute('data-min-display') || '10');
            if (progress !== null) {
                // 确保进度至少为最小显示值，以便用户能看到进度条
                const displayProgress = Math.max(progress, minDisplay);
                console.log(`设置进度条宽度: ${progress}% -> ${displayProgress}%`);
                bar.style.width = displayProgress + '%';
                // 确保文本内容也更新
                bar.textContent = `${progress}%`;
            }
        });
    }

    // 检查是否需要自动刷新页面
    function checkAutoRefresh() {
        // 获取所有进度条
        const progressBars = document.querySelectorAll('.progress-bar[role="progressbar"]');
        let allCompleted = true;

        // 检查是否所有进度条都已完成
        progressBars.forEach(bar => {
            const progress = parseInt(bar.getAttribute('aria-valuenow') || '0');
            if (progress < 100) {
                allCompleted = false;
            }
        });

        // 如果所有进度条都已完成，且页面上有进度条，则刷新页面
        if (allCompleted && progressBars.length > 0) {
            console.log('所有分析已完成，准备刷新页面以显示结果');
            setTimeout(() => {
                location.reload();
            }, 2000);
        }
    }

    // 全局变量，用于存储分析结果数据
    // 注意：analysisResultsData在下面已经定义，这里不需要重复定义

    $(document).ready(function() {
        // 确保novelId变量正确
        let currentNovelId;
        try {
            currentNovelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
        } catch (e) {
            console.error('解析currentNovelId出错:', e);
            currentNovelId = novelIdFromTemplate;
        }

        // 全选按钮逻辑
        $('#select-all-dimensions').change(function() {
            const isChecked = $(this).prop('checked');
            $('.dimension-checkbox').prop('checked', isChecked);
        });

        // 表单提交事件
        $('#analyze-form').submit(function(e) {
            e.preventDefault();

            const dimensions = [];
            $('.dimension-checkbox:checked').each(function() {
                dimensions.push($(this).val());
            });

            if (dimensions.length === 0) {
                alert('请至少选择一个分析维度');
                return;
            }

            // 关闭模态框
            $('#analyzeModal').modal('hide');

            // 开始分析
            startAnalysis(currentNovelId, dimensions);
        });

        // 开始分析函数
        function startAnalysis(novelId, dimensions) {
            console.log(`开始分析小说 ${novelId}，维度:`, dimensions);

            // 显示加载状态
            dimensions.forEach(dimension => {
                // 查找或创建维度卡片
                let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                if (!card) {
                    card = createDimensionCard(dimension);
                }

                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    if (cardBody) {
                        // 显示进度条
                        cardBody.innerHTML = `
                            <div class="analysis-progress">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        id="progress-${dimension}"
                                        aria-valuenow="10"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        data-progress="10"
                                        data-min-display="10"
                                        style="width: 10%">
                                        10%
                                    </div>
                                </div>
                                <div class="analysis-status">
                                    <span>分析中...</span>
                                    <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                                </div>
                                <div class="analysis-visualization mt-3">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">正在分析中，请稍候...</p>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }
            });

            // 确保URL不包含任何非ASCII字符，使用干净的URL
            const cleanUrl = `/api/novels/${novelId}/analyze`;

            console.log(`发送分析请求到: ${cleanUrl}，维度: ${dimensions.join(', ')}`);

            // 发送API请求
            fetch(cleanUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache'
                },
                body: JSON.stringify({
                    dimensions: dimensions,
                    parallel_analysis: true,
                    use_cache: document.getElementById('use-cache').checked,
                    force_refresh: document.getElementById('force-refresh').checked,
                    model: document.getElementById('model-select').value
                }),
            })
            .then(response => {
                if (!response.ok) {
                    console.error(`服务器响应错误: ${response.status} ${response.statusText}`);
                    return response.text().then(text => {
                        console.error(`错误响应内容: ${text}`);
                        throw new Error(`服务器响应错误: ${response.status} ${response.statusText}, 详情: ${text}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('分析请求响应:', data);

                if (data.success) {
                    // 启动进度更新
                    updateAnalysisProgress();

                    // 设置一个定时器，定期检查分析结果是否已保存到数据库
                    dimensions.forEach(dimension => {
                        // 每5秒检查一次分析结果
                        const checkResultInterval = setInterval(() => {
                            console.log(`检查维度 ${dimension} 的分析结果...`);
                            // 添加时间戳防止缓存
                            const timestamp = new Date().getTime();

                            console.log(`获取分析结果: /api/novels/${novelId}/analysis/${dimension}?_=${timestamp}`);

                            fetch(`/api/novels/${novelId}/analysis/${dimension}?_=${timestamp}`, {
                                headers: {
                                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                                    'Pragma': 'no-cache'
                                }
                            })
                                .then(response => {
                                    console.log(`维度 ${dimension} 的分析结果请求状态: ${response.status}`);
                                    if (response.ok) {
                                        // 如果成功获取到结果，清除定时器
                                        console.log(`维度 ${dimension} 的分析结果已保存到数据库`);
                                        clearInterval(checkResultInterval);
                                        return response.json();
                                    } else if (response.status === 404 || response.status === 202) {
                                        // 如果是404错误或202状态码，继续等待
                                        console.log(`维度 ${dimension} 的分析结果尚未保存到数据库，继续等待`);
                                        // 尝试解析响应内容，可能包含进度信息
                                        return response.json().catch(e => {
                                            console.log(`无法解析响应内容为JSON: ${e.message}`);
                                            return null;
                                        });
                                    } else {
                                        console.error(`获取维度 ${dimension} 的分析结果失败: HTTP ${response.status}`);
                                        return response.text().then(text => {
                                            console.error(`错误响应内容: ${text}`);
                                            throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                                        });
                                    }
                                })
                                .then(resultData => {
                                    if (resultData && resultData.success) {
                                        // 更新卡片显示结果
                                        updateDimensionCard(dimension, resultData);
                                    }
                                })
                                .catch(error => {
                                    console.error(`检查维度 ${dimension} 的分析结果时出错:`, error);
                                });
                        }, 5000); // 每5秒检查一次

                        // 60秒后自动清除定时器，避免无限期运行
                        setTimeout(() => {
                            clearInterval(checkResultInterval);
                        }, 60000);
                    });
                } else {
                    alert('分析启动失败: ' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('分析请求错误:', error);
                alert('请求错误: ' + error.message);
            });
        }

        // 开始单个维度分析函数
        function startSingleDimensionAnalysis(novelId, dimension) {
            console.log(`开始分析小说 ${novelId} 的维度 ${dimension}`);
            // 直接调用包含完整错误处理的 startAnalysis
            startAnalysis(novelId, [dimension]);
            /* 删除旧的 fetch 调用，因为它在 startAnalysis 中重复了
            fetch(`/api/novels/${novelId}/analyze`, {
                // ... fetch options ...
            })
            .then(response => response.json())
            .then(data => {
                // ... success handling ...
            })
            .catch(error => {
                console.error('分析请求错误:', error);
                 // 记录更详细的错误信息
                console.error('错误详情:', { message: error.message, stack: error.stack });
                alert('请求错误: ' + error.message);
            });
            */
        }

        // 单个维度分析按钮点击事件
        $('.analyze-single-dimension').click(function() {
            const dimension = $(this).data('dimension');
            console.log(`点击了维度 ${dimension} 的分析按钮`);
            startSingleDimensionAnalysis(currentNovelId, dimension);
        });

        // 检查是否有分析进度数据，更新进度条
        // 添加时间戳防止缓存
        const progressTimestamp = new Date().getTime();

        fetch(`/api/analysis/progress?novel_id=${novelId}&_=${progressTimestamp}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('加载页面时检查分析进度:', data);

                // 如果有进度数据且成功获取
                if (data.success && data.progress && Object.keys(data.progress).length > 0) {
                    console.log('有分析进度数据，开始初始化进度条');

                    // 过滤出未完成的分析
                    const inProgressDimensions = {};
                    let hasInProgressAnalysis = false;

                    for (const dimension in data.progress) {
                        if (!data.progress[dimension].completed && data.progress[dimension].progress < 100) {
                            inProgressDimensions[dimension] = data.progress[dimension];
                            hasInProgressAnalysis = true;
                        } else {
                            console.log(`维度 ${dimension} 已完成，不再显示进度条`);
                        }
                    }

                    // 如果有正在进行的分析，只显示这些分析的进度条
                    if (hasInProgressAnalysis) {
                        console.log('有正在进行的分析，只显示这些分析的进度条');
                        data.progress = inProgressDimensions;
                    }

                    // 显示所有进行中的分析
                    for (const dimension in data.progress) {
                        const progress = data.progress[dimension].progress;
                        console.log(`维度 ${dimension} 当前进度: ${progress}%`);

                        // 查找或创建维度卡片
                        let card = document.querySelector('.analysis-card[data-dimension="' + dimension + '"]');
                        if (!card) {
                            card = createDimensionCard(dimension);
                        }

                        if (card) {
                            const cardBody = card.querySelector('.card-body');

                            // 创建进度条
                            if (progress < 100 && progress >= 0) {
                                // 确保进度至少为10%，以便用户能看到进度条
                                const displayProgress = Math.max(progress, 10);
                                console.log(`为维度 ${dimension} 创建进度条，进度: ${progress}%, 显示进度: ${displayProgress}%`);

                                cardBody.innerHTML = `
                                    <div class="analysis-progress">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                role="progressbar"
                                                id="progress-${dimension}"
                                                aria-valuenow="${progress}"
                                                aria-valuemin="0"
                                                aria-valuemax="100"
                                                data-progress="${progress}"
                                                data-min-display="10"
                                                style='width: ${displayProgress}%'>
                                                ${progress}%
                                            </div>
                                        </div>
                                        <div class="analysis-status">
                                            <span>分析中...</span>
                                            <span id="time-${dimension}">预计剩余时间: ${data.progress[dimension].estimated_time}</span>
                                        </div>
                                        <div class="analysis-visualization mt-3">
                                            <div class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <p class="mt-2">正在分析中，请稍候...</p>
                                            </div>
                                        </div>
                                    </div>
                                `;

                                // 确认进度条已创建并设置了正确的宽度
                                setTimeout(() => {
                                    const progressBar = document.getElementById(`progress-${dimension}`);
                                    if (progressBar) {
                                        console.log(`确认维度 ${dimension} 的进度条宽度: ${progressBar.style.width}`);
                                    }
                                }, 100);
                            }
                        }
                    }

                    // 启动进度更新
                    updateAnalysisProgress();
                } else {
                    console.log('没有正在进行的分析');
                }
            })
            .catch(error => {
                console.error('获取分析进度时出错:', error);
                // 记录更详细的错误信息
                console.error('错误详情:', { message: error.message, stack: error.stack });
            });

        // 获取已完成的分析结果并显示
        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();

        fetch(`/api/novels/${novelId}/analysis?_=${timestamp}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(results => {
                console.log('获取分析结果数据:', results);

                // 如果获取到了新的分析结果，更新全局变量
                if (results && Array.isArray(results) && results.length > 0) {
                    console.log('API返回的分析结果数据:', results);

                    // 将结果转换为以维度为键的对象
                    const newResultsData = {};
                    results.forEach(result => {
                        if (result && result.dimension) {
                            newResultsData[result.dimension] = result;
                            console.log(`处理维度 ${result.dimension} 的结果:`, {
                                contentType: typeof result.content,
                                contentLength: result.content ? result.content.length : 0,
                                metadataType: typeof result.metadata,
                                metadataKeys: result.metadata ? Object.keys(result.metadata) : []
                            });
                        }
                    });

                    // 更新全局变量
                    if (Object.keys(newResultsData).length > 0) {
                        console.log('更新全局分析结果数据:', newResultsData);
                        window.analysisResultsData = newResultsData;
                    }
                }

                // 更新每个维度的卡片
                results.forEach(result => {
                    if (result && result.dimension) {
                        const dimension = result.dimension;
                        console.log(`尝试更新维度 ${dimension} 的卡片`);

                        // 如果找不到卡片，可能还没有创建
                        let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                        if (!card) {
                            console.log(`为完成的分析结果创建维度 ${dimension} 的卡片`);
                            card = createDimensionCard(dimension);
                        }

                        // 使用更新卡片函数
                        updateDimensionCard(dimension, result);
                    }
                });

                // 初始化所有图表
                setTimeout(initializeCharts, 500);
            })
            .catch(error => {
                console.error('获取分析结果时出错:', error);
                // 记录更详细的错误信息
                console.error('错误详情:', { message: error.message, stack: error.stack });
            });

        // 初始化进度条
        initProgressBars();

        // 检查是否需要自动刷新页面
        setTimeout(checkAutoRefresh, 5000);
    });

    // 创建分析维度卡片的函数
    function createDimensionCard(dimension) {
        console.log(`为维度 ${dimension} 创建新卡片 - 开始`);

        // 首先检查是否已存在该维度的卡片（使用多种方式查找）
        let existingCard = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);

        // 如果找不到，尝试使用ID查找
        if (!existingCard) {
            existingCard = document.getElementById(`card-${dimension}`);
        }

        // 如果找到了现有卡片，直接返回
        if (existingCard) {
            console.log(`维度 ${dimension} 的卡片已存在，直接返回现有卡片`);
            return existingCard;
        }

        console.log(`维度 ${dimension} 的卡片不存在，创建新卡片`);

        // 创建卡片容器
        const cardCol = document.createElement('div');
        cardCol.className = 'col-md-4 mb-4';
        cardCol.id = `card-container-${dimension}`;

        // 格式化维度名称为更友好的显示格式
        let displayName = formatDimensionName(dimension);

        // 卡片HTML
        cardCol.innerHTML = `
            <div class="card analysis-card" data-dimension="${dimension}" id="card-${dimension}">
                <div class="card-header">
                    <h5 class="card-title">${displayName}</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">准备分析中...</p>
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        console.log(`维度 ${dimension} 的卡片HTML已创建`);

        // 将卡片添加到分析结果容器中
        let resultsContainer = null;

        // 首先尝试查找已有的结果行容器
        resultsContainer = document.getElementById('analysis-results-row');
        if (resultsContainer) {
            console.log(`找到已有的分析结果行容器 #analysis-results-row`);
        } else {
            // 尝试查找任何包含卡片的行容器
            const existingCards = document.querySelectorAll('.analysis-card');
            if (existingCards.length > 0) {
                // 找到第一个卡片的父元素的父元素（行容器）
                const firstCardParent = existingCards[0].closest('.row');
                if (firstCardParent) {
                    console.log(`找到现有卡片的行容器`);
                    resultsContainer = firstCardParent;
                    // 给容器添加ID以便后续查找
                    if (!firstCardParent.id) {
                        firstCardParent.id = 'analysis-results-row';
                        console.log(`为现有行容器添加ID: analysis-results-row`);
                    }
                }
            }
        }

        // 如果仍然找不到容器，创建一个新的分析结果行容器
        if (!resultsContainer) {
            console.log(`未找到现有的结果容器，创建新容器`);

            // 创建一个新的行容器
            const newRow = document.createElement('div');
            newRow.className = 'row mt-4';
            newRow.id = 'analysis-results-row';

            // 查找分析部分
            const analysisSection = document.querySelector('.analysis-section');
            if (analysisSection) {
                console.log(`找到分析部分，在其后添加结果行容器`);
                analysisSection.insertAdjacentElement('afterend', newRow);
                resultsContainer = newRow;
            } else {
                // 如果找不到分析部分，查找分析结果标题
                const analysisHeading = document.querySelector('h2.mt-5');
                if (analysisHeading) {
                    console.log(`找到分析结果标题，在其后添加结果行容器`);
                    analysisHeading.insertAdjacentElement('afterend', newRow);
                    resultsContainer = newRow;
                } else {
                    // 如果还是找不到，尝试找其他可能的父元素
                    const contentSection = document.querySelector('.col-md-12');
                    if (contentSection) {
                        console.log(`找到内容部分，添加结果行容器`);
                        contentSection.appendChild(newRow);
                        resultsContainer = newRow;
                    } else {
                        console.error(`无法找到合适的位置添加分析结果行容器`);
                        // 作为最后手段，添加到body
                        document.body.appendChild(newRow);
                        resultsContainer = newRow;
                    }
                }
            }

            console.log(`创建了新的分析结果行容器: #${newRow.id}`);
        }

        // 添加卡片到容器
        if (resultsContainer) {
            console.log(`将卡片添加到容器中: ${resultsContainer.id || '未命名容器'}`);
            resultsContainer.appendChild(cardCol);

            // 确认卡片已添加到DOM
            setTimeout(() => {
                const addedCard = document.getElementById(`card-${dimension}`);
                if (addedCard) {
                    console.log(`确认维度 ${dimension} 的卡片已成功添加到DOM`);
                } else {
                    console.error(`维度 ${dimension} 的卡片未能成功添加到DOM`);
                }
            }, 50);

            // 获取并返回卡片元素
            const card = cardCol.querySelector('.analysis-card');
            if (card) {
                console.log(`维度 ${dimension} 的卡片创建成功`);
                return card;
            }
        }

        console.error(`维度 ${dimension} 的卡片创建失败`);
        return null;
    }

    // 启动多维度分析
    function startAnalysis(novelId, dimensions) {
        // 添加时间戳防止缓存
        const analyzeTimestamp = new Date().getTime();

        fetch(`/api/novels/${novelId}/analyze?_=${analyzeTimestamp}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: JSON.stringify({
                dimensions: dimensions,
                parallel_analysis: document.getElementById('parallel-analysis')
                    ? document.getElementById('parallel-analysis').checked
                    : true,
                use_cache: document.getElementById('use-cache')
                    ? document.getElementById('use-cache').checked
                    : true,
                force_refresh: document.getElementById('force-refresh')
                    ? document.getElementById('force-refresh').checked
                    : false
            }),
        })
        .then(response => {
            if (!response.ok) {
                console.error(`服务器响应错误: ${response.status} ${response.statusText}`);
                return response.text().then(text => {
                    console.error(`错误响应内容: ${text}`);
                    throw new Error(`服务器响应错误: ${response.status} ${response.statusText}, 详情: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('分析成功启动，创建进度条');

                // 为每个维度创建进度条
                dimensions.forEach(dimension => {
                    // 使用data-dimension属性查找对应的卡片
                    let card = document.querySelector('.analysis-card[data-dimension="' + dimension + '"]');
                    console.log(`查找维度 ${dimension} 的卡片元素，使用data-dimension属性`);

                    // 如果卡片不存在，创建它
                    if (!card) {
                        card = createDimensionCard(dimension);
                    }

                    if (card) {
                        const cardBody = card.querySelector('.card-body');
                        console.log(`找到维度 ${dimension} 的卡片元素`);

                        if (cardBody) {
                            console.log(`为维度 ${dimension} 创建进度条`);

                            // 创建进度条HTML
                            cardBody.innerHTML = `
                                <div class="analysis-progress">
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                            role="progressbar"
                                            id="progress-${dimension}"
                                            aria-valuenow="0"
                                            aria-valuemin="0"
                                            aria-valuemax="100"
                                            data-progress="0"
                                            data-min-display="10"
                                            style="width: 10%">
                                            10%
                                        </div>
                                    </div>
                                    <div class="analysis-status">
                                        <span>分析中...</span>
                                        <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                                    </div>
                                    <div class="analysis-visualization mt-3">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">正在分析中，请稍候...</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                    } else {
                        console.error(`无法找到维度 ${dimension} 的卡片元素`);
                    }
                });

                // 启动进度更新
                updateAnalysisProgress();
            } else {
                alert('分析启动失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('分析请求错误:', error);
            alert('请求错误: ' + error.message);
        });
    }

    // 分析结果数据已在novel-analysis.js中定义为全局变量
    // 确保全局变量存在
    if (typeof window.analysisResultsData === 'undefined') {
        window.analysisResultsData = {};
    }

    try {
        // 尝试从服务器端获取初始数据
        const serverData = JSON.parse('{"novel_characteristics": {"dimension": "novel_characteristics", "content": "\u5206\u6790\u8fc7\u7a0b\u4e2d\u51fa\u9519\uff0c\u65e0\u6cd5\u83b7\u53d6\u6709\u6548\u7ed3\u679c", "metadata": {"processing_time": 73.14056611061096, "chunk_count": 1, "start_time": "2025-04-30 23:16:20", "end_time": "2025-04-30 23:17:33", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "chapter_outline": {"dimension": "chapter_outline", "content": "\u5206\u6790\u8fc7\u7a0b\u4e2d\u51fa\u9519\uff0c\u65e0\u6cd5\u83b7\u53d6\u6709\u6548\u7ed3\u679c", "metadata": {"processing_time": 60.11929512023926, "chunk_count": 1, "start_time": "2025-05-01 00:07:51", "end_time": "2025-05-01 00:08:51", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "paragraph_length": {"dimension": "paragraph_length", "content": "\u5206\u6790\u8fc7\u7a0b\u4e2d\u51fa\u9519\uff0c\u65e0\u6cd5\u83b7\u53d6\u6709\u6548\u7ed3\u679c\u3002\u8bf7\u68c0\u67e5API\u8fde\u63a5\u548c\u54cd\u5e94\u3002", "metadata": {"processing_time": 52.85702848434448, "chunk_count": 1, "start_time": "2025-05-01 01:23:05", "end_time": "2025-05-01 01:23:58", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "world_building": {"dimension": "world_building", "content": "\u8fd9\u662f\u4e00\u4e2a\u878d\u5408\u73b0\u4ee3\u5e7d\u9ed8\u4e0e\u53e4\u5178\u4fee\u4ed9\u5143\u7d20\u7684\u7a7f\u8d8a\u6545\u4e8b\uff0c\u4f5c\u8005\u901a\u8fc7\u591a\u91cd\u624b\u6cd5\u6784\u5efa\u4e86\u4e00\u4e2a\u5145\u6ee1\u620f\u5267\u5f20\u529b\u7684\u5947\u5e7b\u4e16\u754c\u3002\u4ee5\u4e0b\u4ece\u4e94\u4e2a\u7ef4\u5ea6\u8fdb\u884c\u6df1\u5165\u5206\u6790\uff1a\n\n### \u4e00\u3001\u7a7a\u95f4\u7ed3\u6784\u7684\u5d4c\u5957\u6027\u6784\u5efa\n\u4f5c\u8005\u901a\u8fc7\u4e09\u5c42\u7a7a\u95f4\u67b6\u6784\u5f62\u6210\u4e16\u754c\u89c2\u51b2\u51fb\uff1a\n1. **\u73b0\u5b9e\u951a\u70b9**\uff1a\u4ee5\"\u8bba\u6587\u67e5\u91cd\u767e\u5206\u4e4b\u96f6\"\u7684\u73b0\u4ee3\u5b66\u672f\u7126\u8651\u573a\u666f\u4f5c\u4e3a\u7a7f\u8d8a\u89e6\u53d1\u70b9\uff0c\u5f62\u6210\u5f3a\u70c8\u7684\u73b0\u5b9e\u4ee3\u5165\u611f\u3002\u901a\u8fc7\"18\u00b0\u7a7a\u8c03\u623f\"\"\u53e4\u62c9\u62c9\u9ed1\u6697\u4e4b\u795e\"\u7b49\u5f53\u4ee3\u7b26\u53f7\uff0c\u5efa\u7acb\u4e0e\u8bfb\u8005\u7684\u5171\u60c5\u6865\u6881\u3002\n2. **\u5f02\u754c\u7a7a\u95f4**\uff1a\n   - \u7269\u7406\u73af\u5883\uff1a\u70db\u5149\u6447\u66f3\u7684\u6d1e\u623f\uff08\u9c9b\u7ee1\u5e10/\u7ea2\u7ef3\u91d1\u94c3\uff09\u6697\u793a\u4fee\u771f\u754c\u7684\u5962\u9761\n   - \u6743\u529b\u7ed3\u6784\uff1a\u9752\u5dde\u57ce\u4e3b\u59b9\u59b9\u7684\u8eab\u4efd\u63ed\u793a\u7b49\u7ea7\u68ee\u4e25\u7684\u4fee\u771f\u793e\u4f1a\n3. **\u7cfb\u7edf\u7a7a\u95f4**\uff1a\u516d\u516d\u7cfb\u7edf\u7684\u5b58\u5728\u5f62\u6210\u8d85\u73b0\u5b9e\u7ef4\u5ea6\uff0c\"\u8650\u6587\u5973\u4e3b\u6316\u7164\"\u7684\u5a01\u80c1\u6697\u793a\u591a\u5143\u5b87\u5b99\u6846\u67b6\n\n### \u4e8c\u3001\u6587\u5316\u7b26\u53f7\u7684\u62fc\u8d34\u827a\u672f\n\u4f5c\u8005\u521b\u9020\u6027\u6df7\u642d\u591a\u91cd\u6587\u5316\u7b26\u53f7\uff1a\n- **\u4fee\u771f\u4f20\u7edf\u5143\u7d20**\uff1a\u8ff7\u60c5\u4e39\u3001\u9c9b\u7ee1\u5e10\u3001\u7ea2\u7ef3\u7981\u5236\u7b49\u7ecf\u5178\u8bbe\u5b9a\n- **\u7f51\u7edc\u4e9a\u6587\u5316**\uff1a\"\u9634\u6697\u722c\u884c\"\"\u5927\u60b2\u7f8a\"\"\u69b4\u95e8\u4fe1\u5f92\"\u7b49\u7f51\u7edc\u6897\u7684\u620f\u8c11\u690d\u5165\n- **\u540e\u73b0\u4ee3\u89e3\u6784**\uff1a\u5c06\"\u5f3a\u62a2\u6c11\u7537\"\u8f6c\u5316\u4e3a\u91cd\u590d\u6027\u559c\u5267\u6865\u6bb5\uff0c\u6d88\u89e3\u4f20\u7edf\u4fee\u771f\u6587\u7684\u8083\u6740\u611f\n- **\u7cfb\u7edf\u6587\u5b66\u7279\u5f81**\uff1a\u4efb\u52a1\u673a\u5236\u4e0e\"\u8650\u6587\u5973\u4e3b\"\u7684\u4e92\u6587\u6027\uff0c\u5f62\u6210\u7c7b\u578b\u5c0f\u8bf4\u95f4\u7684\u5bf9\u8bdd\n\n...", "metadata": {"processing_time": 50.3065550327301, "chunk_count": 1, "start_time": "2025-05-01 14:53:49", "end_time": "2025-05-01 14:54:39", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "language_style": {"dimension": "language_style", "content": "\u4ee5\u4e0b\u662f\u5bf9\u8be5\u5c0f\u8bf4\u6587\u672c\u7684\u8be6\u7ec6\u8bed\u8a00\u98ce\u683c\u5206\u6790\uff1a\n\n### \u4e00\u3001\u73b0\u4ee3\u7f51\u7edc\u8bed\u5883\u4e0e\u53e4\u5178\u4fee\u4ed9\u5143\u7d20\u7684\u6df7\u642d\n1. **\u6b21\u5143\u78b0\u649e\u5f0f\u7528\u8bcd**\uff1a\n- \"\u8bba\u6587\u67e5\u91cd\u767e\u5206\u4e4b\u96f6\u5f53\u573a\u9634\u6697\u722c\u884c\"\uff1a\u5c06\u5b66\u672f\u7126\u8651\uff08\u8bba\u6587\u67e5\u91cd\uff09\u4e0e\u7f51\u7edc\u6d41\u884c\u6897\uff08\u9634\u6697\u722c\u884c\uff09\u76f4\u63a5\u5ac1\u63a5\u4e8e\u4fee\u4ed9\u573a\u666f\uff0c\u5f62\u6210\u8352\u8bde\u7684\u65f6\u7a7a\u9519\u4f4d\u611f\u3002\"\u5927\u60b2\u7f8a\"\u8c10\u97f3\u6897\u66f4\u662f\u5f3a\u5316\u4e86\u8fd9\u79cd\u4e92\u8054\u7f51\u8bed\u8a00\u72c2\u6b22\u7279\u8d28\u3002\n- \"\u53e4\u62c9\u62c9\u9ed1\u6697\u4e4b\u795e\u5927\u6218\u9752\u9752\u8349\u539f\u9ed1\u76ae\u4f53\u80b2\u751f\"\uff1a\u901a\u8fc7\u65e0\u5398\u5934\u7684\u8de8\u4f5c\u54c1\u89d2\u8272\u62fc\u8d34\uff08\u5df4\u5566\u5566\u5c0f\u9b54\u4ed9+\u559c\u7f8a\u7f8a\u4e0e\u7070\u592a\u72fc\uff09\uff0c\u51f8\u663e\u4e3b\u89d2\u7684\u6c99\u96d5\u5b85\u5973\u5c5e\u6027\u3002\n\n2. **\u89e3\u6784\u5f0f\u53e4\u5178\u63cf\u5199**\uff1a\n- \"\u9c9b\u7ee1\u5e10\"\"\u7ea2\u7ef3\u91d1\u73b2\"\u7b49\u4f20\u7edf\u610f\u8c61\u4e0e\"play\u7684\u4e00\u73af\"\"\u53d1\u5e03\u4f1a\"\u7b49\u73b0\u4ee3\u6982\u5ff5\u5f62\u6210\u4e92\u6587\uff0c\u5982\"\u5706\u623f\u8981\u7d27\"\u4e0e\u7cfb\u7edf\u63d0\u793a\u97f3\u3010\u53ee\u549a~\u3011\u5f62\u6210\u4eea\u5f0f\u611f\u4e0e\u673a\u68b0\u611f\u7684\u5bf9\u51b2\u3002\n- \u5bf9\u8c22\u6c89\u821f\u5916\u8c8c\u7684\u5de5\u7b14\u63cf\u7ed8\uff08\"\u773c\u5c3e\u6d07\u5f00\u8584\u8584\u7684\u80ed\u8102\u8272\"\uff09\u88ab\u89e3\u6784\u6210\"\u5b95\u673a\u7684\u5927\u8111\u8270\u96be\u542f\u52a8\"\u7684\u73b0\u4ee3\u601d\u7ef4\u5b95\u673a\u53cd\u5e94\u3002\n\n### \u4e8c\u3001\u591a\u91cd\u4fee\u8f9e\u624b\u6cd5\u7684\u52a8\u6001\u5d4c\u5957\n1. **\u611f\u5b98\u901a\u611f\u4fee\u8f9e**\uff1a\n- \"\u88ab\u5bd2\u610f\u6fc0\u5f97\u6253\u4e86\u4e2a\u51b7\u98a4\"\u4e0e\u89c6\u89c9\u63cf\u5199\"\u70db\u5149\u66a7\u6627\u6447\u6643\"\u5f62\u6210\u6e29\u5ea6-\u5149\u611f\u7684\u901a\u611f\u8054\u52a8\u3002\n- \"\u78b0\u5230\u4ed6\u7684\u624b\uff0c\u70eb\u7684\u5413\u4eba\"\u5c06\u89e6\u89c9\u6e29\u5ea6\u4e0e\u5fc3\u7406\u51b2\u51fb\u8fdb\u884c\u53cc\u91cd\u7f16\u7801\u3002\n\n2. **\u52a8\u4f5c\u62df\u6001\u4fee\u8f9e**\uff1a\n- \"\u5c16\u53eb\u626d\u66f2\u8815\u52a8\u5916\u52a0\u9634\u6697\u722c\u884c\"\u901a...", "metadata": {"processing_time": 88.85188937187195, "chunk_count": 1, "start_time": "2025-05-01 16:10:01", "end_time": "2025-05-01 16:11:30", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "rhythm_pacing": {"dimension": "rhythm_pacing", "content": "### \u8282\u594f\u5206\u6790\uff08\u9010\u5b57\u9010\u53e5\u89e3\u6790\uff09\n\n---\n\n#### **1. \u5f00\u7bc7\uff1a\u5feb\u901f\u5f15\u5165\u51b2\u7a81\u4e0e\u60ac\u5ff5**\n- **\u6bb5\u843d\u957f\u5ea6**\uff1a\u9996\u6bb5\u4ec5\u4e00\u53e5\u8bdd\uff0c\u77ed\u4fc3\u6709\u529b\uff0c\u901a\u8fc7\u201c\u6ce8\u610f\u770b\u201d\u7684\u5f15\u5bfc\u5f0f\u8bed\u8a00\u76f4\u63a5\u62c9\u8fd1\u8bfb\u8005\u8ddd\u79bb\uff0c\u8fc5\u901f\u4ea4\u4ee3\u4e3b\u89d2\u201c\u6851\u5ff5\u201d\u7a7f\u8d8a\u7684\u8352\u8bde\u539f\u56e0\uff08\u8bba\u6587\u67e5\u91cd0%\u2192\u9634\u6697\u722c\u884c\u2192\u7a7f\u8d8a\u4fee\u4ed9\u754c\uff09\u3002  \n- **\u53e5\u5b50\u8282\u594f**\uff1a\u77ed\u53e5\u4e3a\u4e3b\uff08\u5982\u201c\u73b0\u5728\uff0c\u5979\u6b63\u9762\u4e34\u6709\u53f2\u4ee5\u6765\u6700\u5927\u7684\u5371\u673a\u201d\uff09\uff0c\u914d\u5408\u60c5\u8282\u7684\u8352\u8bde\u6027\uff08\u7a7f\u8d8a\u903b\u8f91\uff09\uff0c\u5f62\u6210\u8df3\u8dc3\u5f0f\u63a8\u8fdb\uff0c\u8425\u9020\u7d27\u8feb\u611f\u3002  \n- **\u60c5\u8282\u901f\u5ea6**\uff1a\u6781\u901f\u5c55\u5f00\u6838\u5fc3\u77db\u76fe\u2014\u2014\u6851\u5ff5\u7a7f\u8d8a\u81f3\u5371\u9669\u60c5\u5883\uff08\u4e0e\u8c22\u6c89\u821f\u7684\u6d1e\u623f\u73b0\u573a\uff09\uff0c\u7701\u7565\u80cc\u666f\u94fa\u57ab\uff0c\u76f4\u63a5\u5207\u5165\u9ad8\u6f6e\u573a\u666f\uff0c\u7b26\u5408\u7f51\u6587\u5feb\u8282\u594f\u5f00\u7bc7\u6a21\u5f0f\u3002\n\n---\n\n#### **2. \u573a\u666f\u63cf\u5199\uff1a\u5f20\u5f1b\u4ea4\u66ff\u7684\u6c1b\u56f4\u6e32\u67d3**\n- **\u6bb5\u843d\u957f\u5ea6**\uff1a\u4e2d\u957f\u6bb5\u843d\u96c6\u4e2d\u63cf\u5199\u73af\u5883\uff08\u5982\u201c\u70db\u5149\u66a7\u6627\u6447\u6643\u2026\u2026\u5996\u201d\uff09\uff0c\u901a\u8fc7\u7ec6\u817b\u7684\u89c6\u89c9\u610f\u8c61\uff08\u9c9b\u7ee1\u5e10\u3001\u7ea2\u7ef3\u91d1\u94c3\u3001\u80ed\u8102\u8272\u773c\u5c3e\uff09\u8425\u9020\u9999\u8273\u4e0e\u5371\u9669\u4ea4\u7ec7\u7684\u6c1b\u56f4\uff0c\u8282\u594f\u6682\u65f6\u653e\u7f13\u3002  \n- **\u53e5\u5b50\u8282\u594f**\uff1a\u957f\u53e5\u94fa\u9648\u7ec6\u8282\uff08\u5982\u201c\u5c11\u5e74\u4e4c\u53d1\u6563\u4e71\u2026\u2026\u52fe\u4eba\u9b42\u9b44\u7684\u5996\u201d\uff09\uff0c\u7528\u6bd4\u55bb\uff08\u201c\u77b3\u82e5\u70b9\u6f06\u201d\uff09\u548c\u611f\u5b98\u63cf\u5199\uff08\u89e6\u89c9\u201c\u7ea2\u7ef3\u91d1\u73b2\u7f1a\u201d\u3001\u89c6\u89c9\u201c\u80ed\u8102\u8272\u201d\uff09\u589e\u5f3a\u753b\u9762\u611f\uff0c\u4e3a\u540e\u7eed\u51b2\u7a81\u84c4\u52bf\u3002  \n- **\u60c5\u8282\u901f\u5ea6**\uff1a\u901a\u8fc7\u6851\u5ff5\u89c6\u89d2\u7684\u5b95\u673a\u4e0e...", "metadata": {"processing_time": 88.56627416610718, "chunk_count": 1, "start_time": "2025-05-01 16:37:10", "end_time": "2025-05-01 16:38:39", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "character_relationships": {"dimension": "character_relationships", "content": "# \u5206\u6790\u8fc7\u7a0b\u4e2d\u51fa\u9519\n\n## \u9519\u8bef\u8be6\u60c5\n\u5206\u6790\u7ef4\u5ea6 **character_relationships** \u65f6\u9047\u5230\u4e86\u95ee\u9898\u3002\n\n## \u9519\u8bef\u4fe1\u606f\n```\nname 'stats_start' is not defined\n```\n\n## \u5efa\u8bae\u64cd\u4f5c\n\u8bf7\u5c1d\u8bd5\u4ee5\u4e0b\u89e3\u51b3\u65b9\u6cd5\uff1a\n1. \u5237\u65b0\u9875\u9762\u5e76\u91cd\u65b0\u5c1d\u8bd5\u5206\u6790\n2. \u68c0\u67e5\u5c0f\u8bf4\u6587\u672c\u662f\u5426\u8fc7\u957f\u6216\u5305\u542b\u7279\u6b8a\u5b57\u7b26\n3. \u786e\u8ba4API\u8fde\u63a5\u6b63\u5e38\n\n\u5982\u679c\u95ee\u9898\u6301\u7eed\u5b58\u5728\uff0c\u8bf7\u8054\u7cfb\u7cfb\u7edf\u7ba1\u7406\u5458\u3002\n", "metadata": {"error": "name 'stats_start' is not defined", "formatted_error": true}}}');
        console.log('成功解析服务器端分析结果数据:', serverData);

        // 合并到全局变量
        if (serverData && Object.keys(serverData).length > 0) {
            window.analysisResultsData = serverData;
            console.log('已将服务器端数据合并到全局变量');
        }

        // 显示更详细的数据结构信息
        for (const dimension in window.analysisResultsData) {
            console.log(`维度 ${dimension} 的数据:`, {
                dimension: dimension,
                content: window.analysisResultsData[dimension].content ? (window.analysisResultsData[dimension].content.substring(0, 50) + '...') : '无内容',
                metadata: window.analysisResultsData[dimension].metadata,
                hasVisualization: window.analysisResultsData[dimension].metadata && window.analysisResultsData[dimension].metadata.visualization_data ? '有可视化数据' : '无可视化数据'
            });
        }
    } catch (e) {
        console.error('解析分析结果数据时出错:', e);
        console.error('原始数据:', '{"novel_characteristics": {"dimension": "novel_characteristics", "content": "\u5206\u6790\u8fc7\u7a0b\u4e2d\u51fa\u9519\uff0c\u65e0\u6cd5\u83b7\u53d6\u6709\u6548\u7ed3\u679c", "metadata": {"processing_time": 73.14056611061096, "chunk_count": 1, "start_time": "2025-04-30 23:16:20", "end_time": "2025-04-30 23:17:33", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "chapter_outline": {"dimension": "chapter_outline", "content": "\u5206\u6790\u8fc7\u7a0b\u4e2d\u51fa\u9519\uff0c\u65e0\u6cd5\u83b7\u53d6\u6709\u6548\u7ed3\u679c", "metadata": {"processing_time": 60.11929512023926, "chunk_count": 1, "start_time": "2025-05-01 00:07:51", "end_time": "2025-05-01 00:08:51", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "paragraph_length": {"dimension": "paragraph_length", "content": "\u5206\u6790\u8fc7\u7a0b\u4e2d\u51fa\u9519\uff0c\u65e0\u6cd5\u83b7\u53d6\u6709\u6548\u7ed3\u679c\u3002\u8bf7\u68c0\u67e5API\u8fde\u63a5\u548c\u54cd\u5e94\u3002", "metadata": {"processing_time": 52.85702848434448, "chunk_count": 1, "start_time": "2025-05-01 01:23:05", "end_time": "2025-05-01 01:23:58", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "world_building": {"dimension": "world_building", "content": "\u8fd9\u662f\u4e00\u4e2a\u878d\u5408\u73b0\u4ee3\u5e7d\u9ed8\u4e0e\u53e4\u5178\u4fee\u4ed9\u5143\u7d20\u7684\u7a7f\u8d8a\u6545\u4e8b\uff0c\u4f5c\u8005\u901a\u8fc7\u591a\u91cd\u624b\u6cd5\u6784\u5efa\u4e86\u4e00\u4e2a\u5145\u6ee1\u620f\u5267\u5f20\u529b\u7684\u5947\u5e7b\u4e16\u754c\u3002\u4ee5\u4e0b\u4ece\u4e94\u4e2a\u7ef4\u5ea6\u8fdb\u884c\u6df1\u5165\u5206\u6790\uff1a\n\n### \u4e00\u3001\u7a7a\u95f4\u7ed3\u6784\u7684\u5d4c\u5957\u6027\u6784\u5efa\n\u4f5c\u8005\u901a\u8fc7\u4e09\u5c42\u7a7a\u95f4\u67b6\u6784\u5f62\u6210\u4e16\u754c\u89c2\u51b2\u51fb\uff1a\n1. **\u73b0\u5b9e\u951a\u70b9**\uff1a\u4ee5\"\u8bba\u6587\u67e5\u91cd\u767e\u5206\u4e4b\u96f6\"\u7684\u73b0\u4ee3\u5b66\u672f\u7126\u8651\u573a\u666f\u4f5c\u4e3a\u7a7f\u8d8a\u89e6\u53d1\u70b9\uff0c\u5f62\u6210\u5f3a\u70c8\u7684\u73b0\u5b9e\u4ee3\u5165\u611f\u3002\u901a\u8fc7\"18\u00b0\u7a7a\u8c03\u623f\"\"\u53e4\u62c9\u62c9\u9ed1\u6697\u4e4b\u795e\"\u7b49\u5f53\u4ee3\u7b26\u53f7\uff0c\u5efa\u7acb\u4e0e\u8bfb\u8005\u7684\u5171\u60c5\u6865\u6881\u3002\n2. **\u5f02\u754c\u7a7a\u95f4**\uff1a\n   - \u7269\u7406\u73af\u5883\uff1a\u70db\u5149\u6447\u66f3\u7684\u6d1e\u623f\uff08\u9c9b\u7ee1\u5e10/\u7ea2\u7ef3\u91d1\u94c3\uff09\u6697\u793a\u4fee\u771f\u754c\u7684\u5962\u9761\n   - \u6743\u529b\u7ed3\u6784\uff1a\u9752\u5dde\u57ce\u4e3b\u59b9\u59b9\u7684\u8eab\u4efd\u63ed\u793a\u7b49\u7ea7\u68ee\u4e25\u7684\u4fee\u771f\u793e\u4f1a\n3. **\u7cfb\u7edf\u7a7a\u95f4**\uff1a\u516d\u516d\u7cfb\u7edf\u7684\u5b58\u5728\u5f62\u6210\u8d85\u73b0\u5b9e\u7ef4\u5ea6\uff0c\"\u8650\u6587\u5973\u4e3b\u6316\u7164\"\u7684\u5a01\u80c1\u6697\u793a\u591a\u5143\u5b87\u5b99\u6846\u67b6\n\n### \u4e8c\u3001\u6587\u5316\u7b26\u53f7\u7684\u62fc\u8d34\u827a\u672f\n\u4f5c\u8005\u521b\u9020\u6027\u6df7\u642d\u591a\u91cd\u6587\u5316\u7b26\u53f7\uff1a\n- **\u4fee\u771f\u4f20\u7edf\u5143\u7d20**\uff1a\u8ff7\u60c5\u4e39\u3001\u9c9b\u7ee1\u5e10\u3001\u7ea2\u7ef3\u7981\u5236\u7b49\u7ecf\u5178\u8bbe\u5b9a\n- **\u7f51\u7edc\u4e9a\u6587\u5316**\uff1a\"\u9634\u6697\u722c\u884c\"\"\u5927\u60b2\u7f8a\"\"\u69b4\u95e8\u4fe1\u5f92\"\u7b49\u7f51\u7edc\u6897\u7684\u620f\u8c11\u690d\u5165\n- **\u540e\u73b0\u4ee3\u89e3\u6784**\uff1a\u5c06\"\u5f3a\u62a2\u6c11\u7537\"\u8f6c\u5316\u4e3a\u91cd\u590d\u6027\u559c\u5267\u6865\u6bb5\uff0c\u6d88\u89e3\u4f20\u7edf\u4fee\u771f\u6587\u7684\u8083\u6740\u611f\n- **\u7cfb\u7edf\u6587\u5b66\u7279\u5f81**\uff1a\u4efb\u52a1\u673a\u5236\u4e0e\"\u8650\u6587\u5973\u4e3b\"\u7684\u4e92\u6587\u6027\uff0c\u5f62\u6210\u7c7b\u578b\u5c0f\u8bf4\u95f4\u7684\u5bf9\u8bdd\n\n...", "metadata": {"processing_time": 50.3065550327301, "chunk_count": 1, "start_time": "2025-05-01 14:53:49", "end_time": "2025-05-01 14:54:39", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "language_style": {"dimension": "language_style", "content": "\u4ee5\u4e0b\u662f\u5bf9\u8be5\u5c0f\u8bf4\u6587\u672c\u7684\u8be6\u7ec6\u8bed\u8a00\u98ce\u683c\u5206\u6790\uff1a\n\n### \u4e00\u3001\u73b0\u4ee3\u7f51\u7edc\u8bed\u5883\u4e0e\u53e4\u5178\u4fee\u4ed9\u5143\u7d20\u7684\u6df7\u642d\n1. **\u6b21\u5143\u78b0\u649e\u5f0f\u7528\u8bcd**\uff1a\n- \"\u8bba\u6587\u67e5\u91cd\u767e\u5206\u4e4b\u96f6\u5f53\u573a\u9634\u6697\u722c\u884c\"\uff1a\u5c06\u5b66\u672f\u7126\u8651\uff08\u8bba\u6587\u67e5\u91cd\uff09\u4e0e\u7f51\u7edc\u6d41\u884c\u6897\uff08\u9634\u6697\u722c\u884c\uff09\u76f4\u63a5\u5ac1\u63a5\u4e8e\u4fee\u4ed9\u573a\u666f\uff0c\u5f62\u6210\u8352\u8bde\u7684\u65f6\u7a7a\u9519\u4f4d\u611f\u3002\"\u5927\u60b2\u7f8a\"\u8c10\u97f3\u6897\u66f4\u662f\u5f3a\u5316\u4e86\u8fd9\u79cd\u4e92\u8054\u7f51\u8bed\u8a00\u72c2\u6b22\u7279\u8d28\u3002\n- \"\u53e4\u62c9\u62c9\u9ed1\u6697\u4e4b\u795e\u5927\u6218\u9752\u9752\u8349\u539f\u9ed1\u76ae\u4f53\u80b2\u751f\"\uff1a\u901a\u8fc7\u65e0\u5398\u5934\u7684\u8de8\u4f5c\u54c1\u89d2\u8272\u62fc\u8d34\uff08\u5df4\u5566\u5566\u5c0f\u9b54\u4ed9+\u559c\u7f8a\u7f8a\u4e0e\u7070\u592a\u72fc\uff09\uff0c\u51f8\u663e\u4e3b\u89d2\u7684\u6c99\u96d5\u5b85\u5973\u5c5e\u6027\u3002\n\n2. **\u89e3\u6784\u5f0f\u53e4\u5178\u63cf\u5199**\uff1a\n- \"\u9c9b\u7ee1\u5e10\"\"\u7ea2\u7ef3\u91d1\u73b2\"\u7b49\u4f20\u7edf\u610f\u8c61\u4e0e\"play\u7684\u4e00\u73af\"\"\u53d1\u5e03\u4f1a\"\u7b49\u73b0\u4ee3\u6982\u5ff5\u5f62\u6210\u4e92\u6587\uff0c\u5982\"\u5706\u623f\u8981\u7d27\"\u4e0e\u7cfb\u7edf\u63d0\u793a\u97f3\u3010\u53ee\u549a~\u3011\u5f62\u6210\u4eea\u5f0f\u611f\u4e0e\u673a\u68b0\u611f\u7684\u5bf9\u51b2\u3002\n- \u5bf9\u8c22\u6c89\u821f\u5916\u8c8c\u7684\u5de5\u7b14\u63cf\u7ed8\uff08\"\u773c\u5c3e\u6d07\u5f00\u8584\u8584\u7684\u80ed\u8102\u8272\"\uff09\u88ab\u89e3\u6784\u6210\"\u5b95\u673a\u7684\u5927\u8111\u8270\u96be\u542f\u52a8\"\u7684\u73b0\u4ee3\u601d\u7ef4\u5b95\u673a\u53cd\u5e94\u3002\n\n### \u4e8c\u3001\u591a\u91cd\u4fee\u8f9e\u624b\u6cd5\u7684\u52a8\u6001\u5d4c\u5957\n1. **\u611f\u5b98\u901a\u611f\u4fee\u8f9e**\uff1a\n- \"\u88ab\u5bd2\u610f\u6fc0\u5f97\u6253\u4e86\u4e2a\u51b7\u98a4\"\u4e0e\u89c6\u89c9\u63cf\u5199\"\u70db\u5149\u66a7\u6627\u6447\u6643\"\u5f62\u6210\u6e29\u5ea6-\u5149\u611f\u7684\u901a\u611f\u8054\u52a8\u3002\n- \"\u78b0\u5230\u4ed6\u7684\u624b\uff0c\u70eb\u7684\u5413\u4eba\"\u5c06\u89e6\u89c9\u6e29\u5ea6\u4e0e\u5fc3\u7406\u51b2\u51fb\u8fdb\u884c\u53cc\u91cd\u7f16\u7801\u3002\n\n2. **\u52a8\u4f5c\u62df\u6001\u4fee\u8f9e**\uff1a\n- \"\u5c16\u53eb\u626d\u66f2\u8815\u52a8\u5916\u52a0\u9634\u6697\u722c\u884c\"\u901a...", "metadata": {"processing_time": 88.85188937187195, "chunk_count": 1, "start_time": "2025-05-01 16:10:01", "end_time": "2025-05-01 16:11:30", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "rhythm_pacing": {"dimension": "rhythm_pacing", "content": "### \u8282\u594f\u5206\u6790\uff08\u9010\u5b57\u9010\u53e5\u89e3\u6790\uff09\n\n---\n\n#### **1. \u5f00\u7bc7\uff1a\u5feb\u901f\u5f15\u5165\u51b2\u7a81\u4e0e\u60ac\u5ff5**\n- **\u6bb5\u843d\u957f\u5ea6**\uff1a\u9996\u6bb5\u4ec5\u4e00\u53e5\u8bdd\uff0c\u77ed\u4fc3\u6709\u529b\uff0c\u901a\u8fc7\u201c\u6ce8\u610f\u770b\u201d\u7684\u5f15\u5bfc\u5f0f\u8bed\u8a00\u76f4\u63a5\u62c9\u8fd1\u8bfb\u8005\u8ddd\u79bb\uff0c\u8fc5\u901f\u4ea4\u4ee3\u4e3b\u89d2\u201c\u6851\u5ff5\u201d\u7a7f\u8d8a\u7684\u8352\u8bde\u539f\u56e0\uff08\u8bba\u6587\u67e5\u91cd0%\u2192\u9634\u6697\u722c\u884c\u2192\u7a7f\u8d8a\u4fee\u4ed9\u754c\uff09\u3002  \n- **\u53e5\u5b50\u8282\u594f**\uff1a\u77ed\u53e5\u4e3a\u4e3b\uff08\u5982\u201c\u73b0\u5728\uff0c\u5979\u6b63\u9762\u4e34\u6709\u53f2\u4ee5\u6765\u6700\u5927\u7684\u5371\u673a\u201d\uff09\uff0c\u914d\u5408\u60c5\u8282\u7684\u8352\u8bde\u6027\uff08\u7a7f\u8d8a\u903b\u8f91\uff09\uff0c\u5f62\u6210\u8df3\u8dc3\u5f0f\u63a8\u8fdb\uff0c\u8425\u9020\u7d27\u8feb\u611f\u3002  \n- **\u60c5\u8282\u901f\u5ea6**\uff1a\u6781\u901f\u5c55\u5f00\u6838\u5fc3\u77db\u76fe\u2014\u2014\u6851\u5ff5\u7a7f\u8d8a\u81f3\u5371\u9669\u60c5\u5883\uff08\u4e0e\u8c22\u6c89\u821f\u7684\u6d1e\u623f\u73b0\u573a\uff09\uff0c\u7701\u7565\u80cc\u666f\u94fa\u57ab\uff0c\u76f4\u63a5\u5207\u5165\u9ad8\u6f6e\u573a\u666f\uff0c\u7b26\u5408\u7f51\u6587\u5feb\u8282\u594f\u5f00\u7bc7\u6a21\u5f0f\u3002\n\n---\n\n#### **2. \u573a\u666f\u63cf\u5199\uff1a\u5f20\u5f1b\u4ea4\u66ff\u7684\u6c1b\u56f4\u6e32\u67d3**\n- **\u6bb5\u843d\u957f\u5ea6**\uff1a\u4e2d\u957f\u6bb5\u843d\u96c6\u4e2d\u63cf\u5199\u73af\u5883\uff08\u5982\u201c\u70db\u5149\u66a7\u6627\u6447\u6643\u2026\u2026\u5996\u201d\uff09\uff0c\u901a\u8fc7\u7ec6\u817b\u7684\u89c6\u89c9\u610f\u8c61\uff08\u9c9b\u7ee1\u5e10\u3001\u7ea2\u7ef3\u91d1\u94c3\u3001\u80ed\u8102\u8272\u773c\u5c3e\uff09\u8425\u9020\u9999\u8273\u4e0e\u5371\u9669\u4ea4\u7ec7\u7684\u6c1b\u56f4\uff0c\u8282\u594f\u6682\u65f6\u653e\u7f13\u3002  \n- **\u53e5\u5b50\u8282\u594f**\uff1a\u957f\u53e5\u94fa\u9648\u7ec6\u8282\uff08\u5982\u201c\u5c11\u5e74\u4e4c\u53d1\u6563\u4e71\u2026\u2026\u52fe\u4eba\u9b42\u9b44\u7684\u5996\u201d\uff09\uff0c\u7528\u6bd4\u55bb\uff08\u201c\u77b3\u82e5\u70b9\u6f06\u201d\uff09\u548c\u611f\u5b98\u63cf\u5199\uff08\u89e6\u89c9\u201c\u7ea2\u7ef3\u91d1\u73b2\u7f1a\u201d\u3001\u89c6\u89c9\u201c\u80ed\u8102\u8272\u201d\uff09\u589e\u5f3a\u753b\u9762\u611f\uff0c\u4e3a\u540e\u7eed\u51b2\u7a81\u84c4\u52bf\u3002  \n- **\u60c5\u8282\u901f\u5ea6**\uff1a\u901a\u8fc7\u6851\u5ff5\u89c6\u89d2\u7684\u5b95\u673a\u4e0e...", "metadata": {"processing_time": 88.56627416610718, "chunk_count": 1, "start_time": "2025-05-01 16:37:10", "end_time": "2025-05-01 16:38:39", "visualization_data": {"radar": {"labels": ["\u98ce\u683c\u4e00\u81f4\u6027", "\u8282\u594f\u63a7\u5236", "\u7ed3\u6784\u5b8c\u6574\u6027", "\u4eba\u7269\u5851\u9020", "\u60c5\u8282\u53d1\u5c55", "\u4e3b\u9898\u6df1\u5ea6"], "data": [85, 72, 90, 65, 78, 82]}, "bar": {"labels": ["\u79ef\u6781\u60c5\u611f", "\u6d88\u6781\u60c5\u611f", "\u590d\u6742\u5ea6", "\u521b\u65b0\u6027", "\u8fde\u8d2f\u6027", "\u5438\u5f15\u529b"], "data": [75, 25, 82, 68, 90, 78]}}}}, "character_relationships": {"dimension": "character_relationships", "content": "# \u5206\u6790\u8fc7\u7a0b\u4e2d\u51fa\u9519\n\n## \u9519\u8bef\u8be6\u60c5\n\u5206\u6790\u7ef4\u5ea6 **character_relationships** \u65f6\u9047\u5230\u4e86\u95ee\u9898\u3002\n\n## \u9519\u8bef\u4fe1\u606f\n```\nname 'stats_start' is not defined\n```\n\n## \u5efa\u8bae\u64cd\u4f5c\n\u8bf7\u5c1d\u8bd5\u4ee5\u4e0b\u89e3\u51b3\u65b9\u6cd5\uff1a\n1. \u5237\u65b0\u9875\u9762\u5e76\u91cd\u65b0\u5c1d\u8bd5\u5206\u6790\n2. \u68c0\u67e5\u5c0f\u8bf4\u6587\u672c\u662f\u5426\u8fc7\u957f\u6216\u5305\u542b\u7279\u6b8a\u5b57\u7b26\n3. \u786e\u8ba4API\u8fde\u63a5\u6b63\u5e38\n\n\u5982\u679c\u95ee\u9898\u6301\u7eed\u5b58\u5728\uff0c\u8bf7\u8054\u7cfb\u7cfb\u7edf\u7ba1\u7406\u5458\u3002\n", "metadata": {"error": "name 'stats_start' is not defined", "formatted_error": true}}}');
        window.analysisResultsData = {};
    }

    // 初始化所有图表
    function initializeCharts() {
        const chartElements = document.querySelectorAll('.analysis-chart');
        console.log('找到图表元素数量:', chartElements.length);

        // 打印当前的分析结果数据
        console.log('初始化图表时的分析结果数据:', window.analysisResultsData);

        // 检查DOM中的卡片元素
        const cards = document.querySelectorAll('.analysis-card');
        console.log('当前DOM中的分析卡片数量:', cards.length);
        cards.forEach(card => {
            const dimension = card.getAttribute('data-dimension');
            console.log(`卡片维度: ${dimension}, 内容:`, card.innerHTML.substring(0, 100) + '...');
        });

        chartElements.forEach(canvas => {
            const dimension = canvas.getAttribute('data-dimension');
            if (!dimension) {
                console.error('图表元素没有dimension属性:', canvas);
                return;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error(`无法获取维度 ${dimension} 的图表上下文`);
                return;
            }

            // 尝试从分析结果中提取可视化数据
            let labels = ['风格', '节奏', '结构', '人物', '情节', '主题'];
            let data = [85, 72, 90, 65, 78, 82];

            console.log(`尝试为维度 ${dimension} 获取可视化数据`);
            console.log('当前analysisResultsData:', window.analysisResultsData);

            // 检查是否有可视化数据
            try {
                if (window.analysisResultsData &&
                    window.analysisResultsData[dimension] &&
                    window.analysisResultsData[dimension].metadata &&
                    window.analysisResultsData[dimension].metadata.visualization_data) {

                    const visualData = window.analysisResultsData[dimension].metadata.visualization_data.radar;
                    if (visualData && visualData.labels && visualData.data) {
                        labels = visualData.labels;
                        data = visualData.data;
                        console.log(`使用维度 ${dimension} 的自定义可视化数据:`, labels, data);
                    } else {
                        console.log(`维度 ${dimension} 没有雷达图数据，使用默认数据`);
                    }
                } else {
                    // 尝试从API获取最新数据
                    console.log(`analysisResultsData中没有维度 ${dimension} 的数据，尝试从API获取`);
                    fetch(`/api/novels/${novelIdFromTemplate}/analysis/${dimension}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('获取分析结果失败');
                            }
                            return response.json();
                        })
                        .then(resultData => {
                            if (resultData.metadata && resultData.metadata.visualization_data && resultData.metadata.visualization_data.radar) {
                                const visualData = resultData.metadata.visualization_data.radar;
                                if (visualData && visualData.labels && visualData.data) {
                                    labels = visualData.labels;
                                    data = visualData.data;
                                    console.log(`从API获取维度 ${dimension} 的可视化数据:`, labels, data);

                                    // 重新创建图表
                                    createChart(ctx, dimension, labels, data);
                                    return;
                                }
                            }
                            console.log(`从API获取的维度 ${dimension} 数据中没有可视化数据，使用默认数据`);
                            createChart(ctx, dimension, labels, data);
                        })
                        .catch(error => {
                            console.error(`获取维度 ${dimension} 的可视化数据失败:`, error);
                            createChart(ctx, dimension, labels, data);
                        });
                    return; // 异步获取数据，先返回
                }
            } catch (e) {
                console.error(`处理维度 ${dimension} 的可视化数据时出错:`, e);
                console.log(`为维度 ${dimension} 使用默认可视化数据`);
            }

            // 使用通用函数创建图表
            createChart(ctx, dimension, labels, data);
        });
    }

    // 创建雷达图的通用函数
    function createChart(ctx, dimension, labels, data) {
        if (!ctx) {
            console.error(`无法为维度 ${dimension} 创建图表：上下文无效`);
            return;
        }

        // 确保数据有效
        if (!labels || !Array.isArray(labels) || !data || !Array.isArray(data)) {
            console.error(`维度 ${dimension} 的图表数据无效:`, { labels, data });
            labels = ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'];
            data = [85, 72, 90, 65, 78, 82];
        }

        // 确保数据长度匹配
        if (labels.length !== data.length) {
            console.warn(`维度 ${dimension} 的标签和数据长度不匹配，将使用默认数据`);
            labels = ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'];
            data = [85, 72, 90, 65, 78, 82];
        }

        console.log(`为维度 ${dimension} 创建图表，使用数据:`, { labels, data });

        // 使用 chartLoader 确保 Chart.js 已加载
        window.chartLoader.ensureLoaded(function() {
            try {
                // 检查是否已存在图表，如果存在则销毁
                if (window.Chart && ctx.canvas) {
                    const chartInstance = Chart.getChart(ctx.canvas);
                    if (chartInstance) {
                        console.log(`销毁维度 ${dimension} 的现有图表`);
                        chartInstance.destroy();
                    }
                }

                // 创建新图表
                new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: formatDimensionName(dimension) + '分析评分',
                            data: data,
                            fill: true,
                            backgroundColor: 'rgba(74, 107, 223, 0.2)',
                            borderColor: 'rgb(74, 107, 223)',
                            pointBackgroundColor: 'rgb(74, 107, 223)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgb(74, 107, 223)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        elements: {
                            line: {
                                borderWidth: 3
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                suggestedMin: 0,
                                suggestedMax: 100,
                                ticks: {
                                    stepSize: 20
                                }
                            }
                        }
                    }
                });
                console.log(`成功为维度 ${dimension} 创建图表`);
            } catch (e) {
                console.error(`为维度 ${dimension} 创建图表时出错:`, e);
            }
        });
    }

    // 开始单个维度的分析
    function startSingleDimensionAnalysis(novelId, dimension) {
        console.log(`开始分析小说 ${novelId} 的 ${dimension} 维度`);

        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        const url = `/api/novels/${novelId}/analyze?_=${timestamp}`;

        console.log(`发送分析请求到: ${url}`);

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache'
            },
            body: JSON.stringify({
                dimensions: [dimension],
                parallel_analysis: document.getElementById('parallel-analysis') ? document.getElementById('parallel-analysis').checked : true,
                use_cache: document.getElementById('use-cache') ? document.getElementById('use-cache').checked : true,
                force_refresh: document.getElementById('force-refresh') ? document.getElementById('force-refresh').checked : false
            }),
        })
        .then(response => {
            console.log(`分析请求响应状态: ${response.status}`);
            if (!response.ok) {
                return response.text().then(text => {
                    console.error(`错误响应内容: ${text}`);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log(`分析请求响应数据:`, data);
            if (data.success) {
                // 使用data-dimension属性查找对应的卡片
                let card = document.querySelector('.analysis-card[data-dimension="' + dimension + '"]');
                console.log(`查找维度 ${dimension} 的卡片元素，使用data-dimension属性`);

                // 如果卡片不存在，创建它
                if (!card) {
                    card = createDimensionCard(dimension);
                }

                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    console.log(`为维度 ${dimension} 创建进度条`);

                    // 创建进度条HTML
                    cardBody.innerHTML = `
                        <div class="analysis-progress">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar"
                                     id="progress-${dimension}"
                                     aria-valuenow="10"
                                     aria-valuemin="0"
                                     aria-valuemax="100"
                                     data-progress="10"
                                     data-min-display="10"
                                     style="width: 10%">
                                    10%
                                </div>
                            </div>
                            <div class="analysis-status">
                                <span>分析中...</span>
                                <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                            </div>
                            <div class="analysis-visualization mt-3">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">正在分析中，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    `;

                    // 确认进度条已创建并设置了正确的宽度
                    setTimeout(() => {
                        const progressBar = document.getElementById(`progress-${dimension}`);
                        if (progressBar) {
                            console.log(`确认维度 ${dimension} 的进度条宽度: ${progressBar.style.width}`);
                        }
                    }, 100);

                    // 确认进度条已创建
                    const progressBar = document.getElementById(`progress-${dimension}`);
                    if (progressBar) {
                        console.log(`进度条元素已创建: progress-${dimension}`);
                    } else {
                        console.error(`无法找到进度条元素: progress-${dimension}`);
                    }
                } else {
                    console.error(`无法找到维度 ${dimension} 的卡片元素`);
                }

                // 启动进度更新
                console.log('开始更新分析进度');
                updateAnalysisProgress();
            } else {
                alert('分析启动失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('分析请求错误:', error);
            alert('请求错误: ' + error.message);
        });
    }

    // 重置数据库连接池
    function resetDatabaseConnections() {
        console.log('尝试重置数据库连接池');

        fetch('/api/reset_connections', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            }
        })
        .then(response => {
            if (!response.ok) {
                console.error(`重置连接池失败: HTTP ${response.status}`);
                return response.text().then(text => {
                    console.error(`错误响应内容: ${text}`);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('重置连接池响应:', data);
            if (data.success) {
                console.log('数据库连接池已重置');
                // 延迟1秒后重新尝试更新进度
                setTimeout(updateAnalysisProgress, 1000);
            } else {
                console.error('重置连接池失败:', data.error);
            }
        })
        .catch(error => {
            console.error('重置连接池请求错误:', error);
        });
    }

    // 增加防止重复并发轮询的标志
    let isUpdatingProgress = false;

    // 更新分析进度
    function updateAnalysisProgress() {
        if (isUpdatingProgress) {
            console.log('上一次进度请求尚未完成，跳过本次轮询');
            return;
        }
        isUpdatingProgress = true;
        console.log('开始更新分析进度');

        // 确保novelId是有效的
        let novelId;
        try {
            novelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
        } catch (e) {
            console.error('解析novelId出错:', e);
            novelId = novelIdFromTemplate;
        }

        // 添加随机参数防止缓存
        const timestamp = new Date().getTime();
        const url = `/api/analysis/progress?novel_id=${novelId}&_=${timestamp}`;

        console.log(`获取分析进度: ${url}`);

        // 检查是否有分析进度数据
        fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache'
            }
        })
            .then(response => {
                if (!response.ok) {
                    console.error(`获取进度信息失败: HTTP ${response.status}`);

                    // 如果是500错误，可能是连接池问题，尝试重置连接池
                    if (response.status === 500) {
                        console.log('检测到500错误，尝试重置数据库连接池');
                        // 重置并发标志，允许新的轮询
                        isUpdatingProgress = false;
                        resetDatabaseConnections();
                        return null;
                    }

                    return response.text().then(text => {
                        console.error(`错误响应内容: ${text}`);
                        throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('获取到进度数据:', data);

                if (!data.success) {
                    console.error('获取进度信息失败:', data.error);
                    return;
                }

                // 如果没有进度数据，退出
                if (!data.progress || Object.keys(data.progress).length === 0) {
                    console.log('没有正在进行的分析任务');
                    return;
                }

                // 处理每个维度的进度数据
                let allCompleted = true;
                let hasAnyProgress = false;

                // 更新控制台日志
                if (data.recent_logs && data.recent_logs.length > 0) {
                    console.log(`收到 ${data.recent_logs.length} 条最新日志`);
                    data.recent_logs.forEach(log => {
                        // 使用控制台记录器添加日志
                        if (window.consoleLogger) {
                            window.consoleLogger.addLog(log.message, log.level, log.timestamp, log.dimension);
                        }
                    });
                }

                // 更新小说标题
                if (data.novel_title) {
                    const titleElement = document.querySelector('h1.mt-4');
                    if (titleElement) {
                        // 检查标题是否包含小说ID
                        if (titleElement.textContent.includes('小说ID:')) {
                            titleElement.textContent = `《${data.novel_title}》`;
                        }
                    }
                }

                for (const dimension in data.progress) {
                    console.log(`处理维度 ${dimension} 的进度数据`);

                    // 跳过已完成的分析
                    if (data.progress[dimension].completed || data.progress[dimension].progress >= 100) {
                        console.log(`维度 ${dimension} 已完成，跳过进度更新`);
                        continue;
                    }

                    hasAnyProgress = true;

                    // 先确保该维度的卡片存在
                    let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);

                    if (!card) {
                        console.log(`未找到维度 ${dimension} 的卡片，创建新卡片`);
                        card = createDimensionCard(dimension);

                        if (!card) {
                            console.error(`无法为维度 ${dimension} 创建卡片，跳过此维度`);
                            continue;
                        }
                    }

                    // 找到卡片的主体部分
                    const cardBody = card.querySelector('.card-body');
                    if (!cardBody) {
                        console.error(`维度 ${dimension} 的卡片没有卡片主体元素`);
                        continue;
                    }

                    // 获取进度数据
                    const progress = data.progress[dimension].progress;
                    const estimatedTime = data.progress[dimension].estimated_time;

                    // 获取更详细的进度信息
                    const remainingTime = data.progress[dimension].remaining_time || "计算中...";
                    const eta = data.progress[dimension].eta || "";
                    const blocksProgress = data.progress[dimension].blocks_progress || "";
                    const blocksPercentage = data.progress[dimension].blocks_percentage || 0;

                    // 获取最新日志
                    const latestLog = data.progress[dimension].latest_log || "";

                    // 显示总体进度信息
                    if (data.overall_progress) {
                        const overallProgressElement = document.getElementById('overall-progress');
                        if (overallProgressElement) {
                            const overallProgress = data.overall_progress.progress;
                            const completedDimensions = data.overall_progress.completed_dimensions;
                            const totalDimensions = data.overall_progress.total_dimensions;
                            const overallRemainingTime = data.overall_progress.remaining_time;
                            const overallEta = data.overall_progress.eta;

                            overallProgressElement.innerHTML = `
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">总体分析进度</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="progress mb-3">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                style="width: ${overallProgress}%"
                                                aria-valuenow="${overallProgress}"
                                                aria-valuemin="0"
                                                aria-valuemax="100">
                                                ${overallProgress}%
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>已完成维度: ${completedDimensions}/${totalDimensions}</span>
                                            <span>预计剩余时间: ${overallRemainingTime}</span>
                                        </div>
                                        <div class="text-end mt-2">
                                            <small class="text-muted">预计完成时间: ${overallEta}</small>
                                        </div>
                                    </div>
                                </div>
                            `;
                        } else {
                            // 如果总体进度元素不存在，创建一个
                            const analysisSection = document.querySelector('.analysis-section');
                            if (analysisSection) {
                                const overallProgressDiv = document.createElement('div');
                                overallProgressDiv.id = 'overall-progress';
                                overallProgressDiv.className = 'mt-4';
                                analysisSection.insertAdjacentElement('afterend', overallProgressDiv);

                                // 递归调用自身，以便更新新创建的元素
                                updateAnalysisProgress();
                                return;
                            }
                        }
                    }

                    // 检查是否出错或完成
                    if (progress < 0) {
                        // 分析失败
                        console.log(`维度 ${dimension} 分析失败，错误信息: ${estimatedTime}`);
                        cardBody.innerHTML = `
                            <div class="alert alert-danger">
                                <strong>分析失败</strong>
                                <p>${estimatedTime || '未知错误'}</p>
                                <button class="btn btn-sm btn-danger analyze-single-dimension mt-2"
                                        data-dimension="${dimension}"
                                        data-novel-id="${novelId}">
                                    重试分析
                                </button>
                            </div>
                        `;
                    } else if (progress >= 100) {
                        // 分析完成，显示结果
                        console.log(`维度 ${dimension} 分析完成，加载结果`);
                        allCompleted = allCompleted && true;

                        // 添加时间戳防止缓存
                        const timestamp = new Date().getTime();

                        // 尝试从API获取结果
                        const resultUrl = `/api/novels/${novelId}/analysis/${dimension}?_=${timestamp}`;
                        console.log(`获取分析结果: ${resultUrl}`);

                        fetch(resultUrl, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'Cache-Control': 'no-cache, no-store, must-revalidate',
                                'Pragma': 'no-cache'
                            }
                        })
                            .then(response => {
                                console.log(`维度 ${dimension} 的分析结果请求状态: ${response.status}`);
                                if (!response.ok) {
                                    if (response.status === 404 || response.status === 202) {
                                        // 如果是404错误或202状态码，说明分析结果尚未保存到数据库
                                        console.log(`维度 ${dimension} 的分析结果尚未保存到数据库，继续等待`);

                                        // 尝试解析响应内容，可能包含进度信息
                                        return response.json().catch(e => {
                                            console.log(`无法解析响应内容为JSON: ${e.message}`);

                                            // 检查分析进度，如果进度已经是100%但结果不存在，可能是保存结果时出错
                                            if (progress >= 100) {
                                                // 尝试重新启动该维度的分析
                                                console.log(`维度 ${dimension} 进度为100%但结果不存在，尝试重新分析`);
                                                setTimeout(() => {
                                                    startSingleDimensionAnalysis(novelId, dimension);
                                                }, 3000); // 延迟3秒后重新启动分析
                                            }

                                            // 不抛出错误，继续等待进度更新
                                            return null;
                                        });
                                    } else {
                                        console.error(`获取维度 ${dimension} 的分析结果失败: HTTP ${response.status}`);
                                        return response.text().then(text => {
                                            console.error(`错误响应内容: ${text}`);
                                            throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                                        });
                                    }
                                }
                                return response.json();
                            })
                            .then(resultData => {
                                if (resultData) {
                                    console.log(`获取到维度 ${dimension} 的分析结果:`, resultData);
                                    if (resultData.success) {
                                        updateDimensionCard(dimension, resultData);
                                    } else {
                                        console.warn(`维度 ${dimension} 的分析结果不成功:`, resultData.error);
                                        // 如果分析进度是100%但结果不成功，可能是保存结果时出错
                                        if (progress >= 100) {
                                            console.log(`维度 ${dimension} 进度为100%但结果不成功，尝试重新分析`);
                                            setTimeout(() => {
                                                startSingleDimensionAnalysis(novelId, dimension);
                                            }, 3000); // 延迟3秒后重新启动分析
                                        }
                                    }
                                }
                            })
                            .catch(error => {
                                console.error(`获取维度 ${dimension} 的结果出错:`, error);
                                // 显示错误信息，但不中断进度更新
                                const card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                                if (card) {
                                    const cardBody = card.querySelector('.card-body');
                                    if (cardBody) {
                                        cardBody.innerHTML = `
                                            <div class="alert alert-warning">
                                                <strong>获取结果时出错</strong>
                                                <p>${error.message || '未知错误'}</p>
                                                <button class="btn btn-sm btn-primary mt-2 analyze-single-dimension"
                                                        data-dimension="${dimension}"
                                                        data-novel-id="${novelId}">
                                                    重试分析
                                                </button>
                                            </div>
                                        `;
                                    }
                                }
                            });
                    } else {
                        // 分析进行中，更新进度条
                        allCompleted = false;
                        const displayProgress = Math.max(progress, 10);

                        // 检查是否已有进度条
                        let progressBar = document.getElementById(`progress-${dimension}`);
                        let timeSpan = document.getElementById(`time-${dimension}`);

                        if (progressBar && timeSpan) {
                            // 更新现有进度条
                            console.log(`更新维度 ${dimension} 的进度条: ${progress}% -> ${displayProgress}%`);
                            progressBar.style.width = `${displayProgress}%`;
                            progressBar.setAttribute('aria-valuenow', progress);
                            progressBar.setAttribute('data-progress', progress);
                            progressBar.textContent = `${progress}%`;
                            timeSpan.textContent = `预计剩余时间: ${remainingTime || estimatedTime}`;

                            // 更新块级进度信息
                            const blocksElement = document.getElementById(`blocks-${dimension}`);
                            if (blocksElement && blocksProgress) {
                                blocksElement.textContent = `块进度: ${blocksProgress}`;

                                // 更新块级进度条
                                const blocksProgressBar = document.getElementById(`blocks-progress-${dimension}`);
                                if (blocksProgressBar) {
                                    blocksProgressBar.style.width = `${blocksPercentage}%`;
                                    blocksProgressBar.setAttribute('aria-valuenow', blocksPercentage);
                                }
                            }

                            // 更新预计完成时间
                            const etaElement = document.getElementById(`eta-${dimension}`);
                            if (etaElement && eta) {
                                etaElement.textContent = `预计完成时间: ${eta}`;
                            }

                            // 更新最新日志
                            const latestLogElement = document.getElementById(`latest-log-${dimension}`);
                            if (latestLogElement && latestLog) {
                                latestLogElement.textContent = `最新进展: ${latestLog}`;
                            } else if (latestLog && !latestLogElement) {
                                // 如果有最新日志但没有显示元素，添加一个
                                const statusDiv = progressBar.closest('.analysis-status');
                                if (statusDiv) {
                                    const logDiv = document.createElement('div');
                                    logDiv.className = 'mt-2 p-2 bg-light rounded';
                                    logDiv.innerHTML = `<small id="latest-log-${dimension}" class="text-muted">最新进展: ${latestLog}</small>`;

                                    // 找到终止分析按钮的容器，在它之前插入日志
                                    const buttonContainer = statusDiv.querySelector('.mt-2');
                                    if (buttonContainer) {
                                        statusDiv.insertBefore(logDiv, buttonContainer);
                                    } else {
                                        statusDiv.appendChild(logDiv);
                                    }
                                }
                            }
                        } else {
                            // 创建新的进度条
                            console.log(`为维度 ${dimension} 创建新进度条: ${progress}%`);
                            cardBody.innerHTML = `
                                <div class="analysis-progress">
                                    <div class="progress mb-2">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                            role="progressbar"
                                            id="progress-${dimension}"
                                            aria-valuenow="${progress}"
                                            aria-valuemin="0"
                                            aria-valuemax="100"
                                            data-progress="${progress}"
                                            data-min-display="10"
                                            style="width: ${displayProgress}%">
                                            ${progress}%
                                        </div>
                                    </div>
                                    <div class="analysis-status mb-2">
                                        <div class="d-flex justify-content-between">
                                            <span>分析中...</span>
                                            <span id="time-${dimension}">预计剩余时间: ${remainingTime || estimatedTime}</span>
                                        </div>
                                        ${blocksProgress ? `
                                            <div class="mt-2">
                                                <small id="blocks-${dimension}" class="text-muted">块进度: ${blocksProgress}</small>
                                                <div class="progress mt-1" style="height: 5px;">
                                                    <div class="progress-bar bg-info"
                                                        role="progressbar"
                                                        id="blocks-progress-${dimension}"
                                                        aria-valuenow="${blocksPercentage}"
                                                        aria-valuemin="0"
                                                        aria-valuemax="100"
                                                        style="width: ${blocksPercentage}%">
                                                    </div>
                                                </div>
                                            </div>
                                        ` : ''}
                                        ${eta ? `<div class="text-end mt-1"><small id="eta-${dimension}" class="text-muted">预计完成时间: ${eta}</small></div>` : ''}
                                        ${latestLog ? `
                                            <div class="mt-2 p-2 bg-light rounded">
                                                <small id="latest-log-${dimension}" class="text-muted">最新进展: ${latestLog}</small>
                                            </div>
                                        ` : `
                                            <div class="mt-2 p-2 bg-light rounded">
                                                <small id="latest-log-${dimension}" class="text-muted">最新进展: 分析进度 ${progress}%</small>
                                            </div>
                                        `}
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-danger stop-analysis-btn"
                                                    data-dimension="${dimension}"
                                                    data-novel-id="${novelId}"
                                                    onclick="stopAnalysis('${novelId}', '${dimension}')">
                                                终止分析
                                            </button>
                                        </div>
                                    </div>
                                    <div class="analysis-visualization mt-3">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">正在分析中，请稍候...</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                    }
                }

                // 如果所有分析都完成了或没有正在进行的分析，不再继续检查进度
                if ((allCompleted && Object.keys(data.progress).length > 0) || !hasAnyProgress) {
                    console.log('所有分析已完成或没有正在进行的分析，不再继续检查进度');
                    // 不再刷新页面，而是依靠前面的API调用来获取结果

                    // 确保获取所有完成的分析结果
                    fetch(`/api/novels/${novelId}/analysis?_=${new Date().getTime()}`)
                        .then(response => response.json())
                        .then(results => {
                            console.log('分析完成后获取所有结果:', results);
                            if (results && results.length > 0) {
                                console.log('开始处理分析结果，找到 ' + results.length + ' 个结果');
                                results.forEach(result => {
                                    console.log(`处理维度 ${result.dimension} 的结果:`, {
                                        contentType: typeof result.content,
                                        contentLength: result.content ? result.content.length : 0,
                                        metadataType: typeof result.metadata,
                                        metadataKeys: result.metadata ? Object.keys(result.metadata) : []
                                    });
                                    updateDimensionCard(result.dimension, result);
                                });
                            } else {
                                console.warn('没有找到任何分析结果');
                            }
                        })
                        .catch(error => console.error('获取完成结果时出错:', error));

                    // 重置标志
                    isUpdatingProgress = false;
                } else {
                    // 重置标志后继续下一轮
                    isUpdatingProgress = false;
                    setTimeout(updateAnalysisProgress, 1500);
                }
            })
            .catch(error => {
                console.error('获取进度信息时出错:', error);
                console.error('错误详情:', { message: error.message, stack: error.stack });
                // 重置标志后继续下一轮
                isUpdatingProgress = false;
                setTimeout(updateAnalysisProgress, 5000);
            });
    }

    // 终止分析函数
    function stopAnalysis(novelId, dimension) {
        console.log(`尝试终止小说 ${novelId} 的 ${dimension} 分析`);

        fetch(`/api/novels/${novelId}/stop_analysis`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                dimension: dimension
            }),
        })
        .then(response => {
            if (!response.ok) {
                console.error(`终止分析请求失败: HTTP ${response.status}`);
                return response.text().then(text => {
                    console.error(`错误响应内容: ${text}`);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log(`成功终止 ${dimension} 分析`);
                alert(`已终止 ${formatDimensionName(dimension)} 分析`);
            } else {
                console.error(`终止分析失败:`, data.error);
                alert(`终止分析失败: ${data.error || '未知错误'}`);
            }
        })
        .catch(error => {
            console.error('终止分析请求错误:', error);
            alert(`请求错误: ${error.message}`);
        });
    }

    // 更新维度卡片的函数
    function updateDimensionCard(dimension, resultData) {
        console.log(`尝试更新维度 ${dimension} 的卡片，数据:`, resultData);

        // 检查结果数据是否有效
        if (!resultData) {
            console.error(`维度 ${dimension} 的结果数据为空`);
            return;
        }

        // 如果结果数据中没有success字段，但有content字段，也认为是有效的
        if (!resultData.success && !resultData.content) {
            console.warn(`维度 ${dimension} 的结果数据可能无效，但仍尝试处理:`, resultData);
            // 继续处理，不要直接返回
        }

        // 查找卡片
        let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
        if (!card) {
            console.log(`未找到维度 ${dimension} 的卡片，尝试创建`);
            card = createDimensionCard(dimension);
            if (!card) {
                console.error(`无法为维度 ${dimension} 创建卡片`);
                return;
            }
        }

        // 获取卡片主体
        const cardBody = card.querySelector('.card-body');
        if (!cardBody) {
            console.error(`维度 ${dimension} 的卡片没有主体元素`);
            return;
        }

        // 保存结果数据到全局变量，供图表使用
        if (typeof analysisResultsData === 'undefined') {
            window.analysisResultsData = {};
        }
        analysisResultsData[dimension] = resultData;

        // 创建可视化HTML
        let visualizationHtml = '';
        if (resultData.metadata && resultData.metadata.visualization_data) {
            visualizationHtml = `
                <div class="analysis-visualization mt-3">
                    <canvas class="analysis-chart" data-dimension="${dimension}" width="400" height="250"></canvas>
                </div>
            `;
        }

        // 提取内容摘要
        let excerpt = '';
        try {
            // 确保内容是字符串
            let content = '';
            if (resultData.content) {
                if (typeof resultData.content === 'string') {
                    content = resultData.content;
                } else {
                    console.warn(`维度 ${dimension} 的内容不是字符串:`, resultData.content);
                    try {
                        content = JSON.stringify(resultData.content);
                    } catch (e) {
                        console.error(`转换维度 ${dimension} 的内容为字符串时出错:`, e);
                        content = '内容格式错误';
                    }
                }
            } else {
                content = '无内容';
            }

            // 去除HTML标签，只保留文本内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';
            excerpt = textContent.substring(0, 300) + '...';
            console.log(`成功提取维度 ${dimension} 的摘要，长度: ${excerpt.length}`);
        } catch (e) {
            console.error(`提取维度 ${dimension} 的摘要时出错:`, e);
            excerpt = '提取摘要时出错';
        }

        // 更新卡片内容
        try {
            cardBody.innerHTML = `
                <div class="d-flex justify-content-between mb-2">
                    <span class="badge bg-success">分析完成</span>
                    <a href="${window.location.pathname}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                </div>
                ${visualizationHtml}
                <div class="analysis-excerpt mt-2">
                    ${excerpt}
                </div>
            `;
            console.log(`成功更新维度 ${dimension} 的卡片内容`);
        } catch (e) {
            console.error(`更新维度 ${dimension} 的卡片内容时出错:`, e);
            // 尝试使用更简单的内容
            try {
                cardBody.innerHTML = `
                    <div class="d-flex justify-content-between mb-2">
                        <span class="badge bg-success">分析完成</span>
                        <a href="${window.location.pathname}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                    </div>
                    <div class="analysis-excerpt mt-2">
                        分析已完成，点击"查看详情"查看完整结果。
                    </div>
                `;
            } catch (innerError) {
                console.error(`尝试使用简化内容更新卡片时也出错:`, innerError);
            }
        }

        // 初始化图表
        setTimeout(() => {
            try {
                const canvas = card.querySelector('.analysis-chart');
                if (canvas) {
                    console.log(`找到维度 ${dimension} 的图表画布元素`);
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                        console.log(`获取到维度 ${dimension} 的图表上下文`);
                        try {
                            let labels = ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'];
                            let data = [85, 72, 90, 65, 78, 82];

                            // 确保元数据是有效的对象
                            let metadata = {};
                            if (resultData.metadata) {
                                if (typeof resultData.metadata === 'object') {
                                    metadata = resultData.metadata;
                                } else {
                                    console.warn(`维度 ${dimension} 的元数据不是对象:`, resultData.metadata);
                                    try {
                                        metadata = JSON.parse(resultData.metadata);
                                    } catch (e) {
                                        console.error(`解析维度 ${dimension} 的元数据时出错:`, e);
                                        metadata = {};
                                    }
                                }
                            }

                            if (metadata &&
                                metadata.visualization_data &&
                                metadata.visualization_data.radar) {

                                const visualData = metadata.visualization_data.radar;
                                if (visualData && visualData.labels && visualData.data) {
                                    labels = visualData.labels;
                                    data = visualData.data;
                                    console.log(`使用维度 ${dimension} 的自定义可视化数据:`, labels, data);
                                }
                            }

                            createChart(ctx, dimension, labels, data);
                            console.log(`成功为维度 ${dimension} 创建图表`);
                        } catch (e) {
                            console.error(`为维度 ${dimension} 创建图表时出错:`, e);
                        }
                    } else {
                        console.error(`无法获取维度 ${dimension} 的图表上下文`);
                    }
                } else {
                    console.error(`找不到维度 ${dimension} 的图表画布元素`);
                }
            } catch (e) {
                console.error(`初始化维度 ${dimension} 的图表时出错:`, e);
            }
        }, 200);

        console.log(`维度 ${dimension} 的卡片已更新`);
    }

    // 创建图表的函数
    function createChart(ctx, dimension, labels, data) {
        console.log(`创建维度 ${dimension} 的图表，标签:`, labels, '数据:', data);

        try {
            // 检查是否已存在图表实例，如果存在则销毁
            if (window.chartInstances && window.chartInstances[dimension]) {
                console.log(`销毁维度 ${dimension} 的现有图表实例`);
                window.chartInstances[dimension].destroy();
            }

            // 确保标签和数据是有效的数组
            if (!Array.isArray(labels)) {
                console.warn(`维度 ${dimension} 的标签不是数组，使用默认标签`);
                labels = ['风格一致性', '节奏控制', '结构完整性', '人物塑造', '情节发展', '主题深度'];
            }

            if (!Array.isArray(data)) {
                console.warn(`维度 ${dimension} 的数据不是数组，使用默认数据`);
                data = [85, 72, 90, 65, 78, 82];
            }

            // 创建雷达图
            const chartInstance = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: formatDimensionName(dimension),
                        data: data,
                        backgroundColor: 'rgba(74, 107, 223, 0.2)',
                        borderColor: 'rgba(74, 107, 223, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 保存图表实例以便后续管理
            if (!window.chartInstances) {
                window.chartInstances = {};
            }
            window.chartInstances[dimension] = chartInstance;

            console.log(`维度 ${dimension} 的图表创建成功`);
        } catch (e) {
            console.error(`创建维度 ${dimension} 的图表时出错:`, e);
        }
    }

    // 格式化维度名称的函数
    function formatDimensionName(dimension) {
        const dimensionMap = {
            'language_style': '语言风格',
            'rhythm_pacing': '节奏与节奏',
            'structure': '结构分析',
            'sentence_variation': '句式变化',
            'paragraph_length': '段落长度',
            'perspective_shifts': '视角转换',
            'paragraph_flow': '段落流畅度',
            'novel_characteristics': '小说特点',
            'world_building': '世界构建',
            'chapter_outline': '章节大纲',
            'character_relationships': '人物关系',
            'opening_effectiveness': '开篇效果',
            'climax_pacing': '高潮节奏'
        };

        // 如果找不到映射，尝试将下划线替换为空格并首字母大写
        if (!dimensionMap[dimension]) {
            console.log(`未找到维度 ${dimension} 的中文名称，使用默认格式化`);
            return dimension
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
        }

        return dimensionMap[dimension];
    }

    // 创建维度卡片的函数
    function createDimensionCard(dimension) {
        console.log(`尝试创建维度 ${dimension} 的卡片`);

        // 检查是否已存在该维度的卡片
        let existingCard = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
        if (existingCard) {
            console.log(`维度 ${dimension} 的卡片已存在，返回现有卡片`);
            return existingCard;
        }

        // 获取分析结果容器
        const container = document.querySelector('.row.mt-4');
        if (!container) {
            console.error('找不到分析结果容器');
            return null;
        }

        // 创建卡片列
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-4';

        // 创建卡片
        const card = document.createElement('div');
        card.className = 'card analysis-card';
        card.setAttribute('data-dimension', dimension);

        // 创建卡片头部
        const cardHeader = document.createElement('div');
        cardHeader.className = 'card-header';

        // 创建卡片标题
        const cardTitle = document.createElement('h5');
        cardTitle.className = 'card-title';
        cardTitle.textContent = formatDimensionName(dimension);

        // 创建卡片主体
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';
        cardBody.innerHTML = `
            <p class="text-muted">尚未分析</p>
            <button class="btn btn-sm btn-outline-secondary analyze-single-dimension"
                    data-dimension="${dimension}"
                    data-novel-id="${document.querySelector('#analyze-form').getAttribute('data-novel-id')}">
                开始分析
            </button>
        `;

        // 组装卡片
        cardHeader.appendChild(cardTitle);
        card.appendChild(cardHeader);
        card.appendChild(cardBody);
        col.appendChild(card);

        // 添加到容器
        container.appendChild(col);

        // 绑定分析按钮事件
        const analyzeButton = card.querySelector('.analyze-single-dimension');
        if (analyzeButton) {
            analyzeButton.addEventListener('click', function() {
                const dimension = this.getAttribute('data-dimension');
                const novelId = this.getAttribute('data-novel-id');
                console.log(`点击了维度 ${dimension} 的分析按钮`);
                startSingleDimensionAnalysis(novelId, dimension);
            });
        }

        console.log(`成功创建维度 ${dimension} 的卡片`);
        return card;
    }

    // 显示所有维度的当前状态
    function displayAllDimensions() {
        // 确保novelId是有效的
        let novelId;
        try {
            novelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
        } catch (e) {
            console.error('解析novelId出错:', e);
            novelId = novelIdFromTemplate;
        }

        // 获取所有可用维度
        const availableDimensions = [
            'language_style', 'rhythm_pacing', 'structure', 'sentence_variation',
            'paragraph_length', 'perspective_shifts', 'paragraph_flow',
            'novel_characteristics', 'world_building', 'chapter_outline',
            'character_relationships', 'opening_effectiveness', 'climax_pacing'
        ];

        // 创建容器
        const container = document.createElement('div');
        container.className = 'all-dimensions-status mt-4';

        // 标题
        const title = document.createElement('h3');
        title.className = 'mb-3';
        title.textContent = '当前分析状态';
        container.appendChild(title);

        // 创建表格
        const table = document.createElement('table');
        table.className = 'table table-striped';

        // 表头
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th>维度</th>
                <th>状态</th>
                <th>操作</th>
            </tr>
        `;
        table.appendChild(thead);

        // 表格内容
        const tbody = document.createElement('tbody');

        // 为每个维度创建一行
        availableDimensions.forEach(dimension => {
            const tr = document.createElement('tr');
            tr.setAttribute('data-dimension', dimension);

            // 维度名称
            const tdName = document.createElement('td');
            tdName.textContent = formatDimensionName(dimension);
            tr.appendChild(tdName);

            // 状态
            const tdStatus = document.createElement('td');
            tdStatus.className = 'dimension-status';
            tdStatus.innerHTML = `<span class="badge bg-secondary">未分析</span>`;
            tr.appendChild(tdStatus);

            // 操作
            const tdAction = document.createElement('td');
            tdAction.innerHTML = `
                <button class="btn btn-sm btn-outline-primary analyze-single-dimension"
                        data-dimension="${dimension}"
                        data-novel-id="${novelId}">
                    开始分析
                </button>
            `;
            tr.appendChild(tdAction);

            tbody.appendChild(tr);
        });

        table.appendChild(tbody);
        container.appendChild(table);

        // 添加到页面
        const analysisSection = document.querySelector('.analysis-section');
        if (analysisSection) {
            // 检查是否已有状态表格
            const existingTable = document.querySelector('.all-dimensions-status');
            if (existingTable) {
                existingTable.replaceWith(container);
            } else {
                analysisSection.appendChild(container);
            }

            // 重新绑定按钮事件
            container.querySelectorAll('.analyze-single-dimension').forEach(button => {
                button.addEventListener('click', function() {
                    const dimension = this.getAttribute('data-dimension');
                    console.log(`点击了维度 ${dimension} 的分析按钮`);
                    startSingleDimensionAnalysis(novelId, dimension);
                });
            });
        }

        // 更新状态
        updateDimensionStatus();
    }

    // 更新所有维度的状态显示
    function updateDimensionStatus() {
        // 确保novelId是有效的
        let novelId;
        try {
            novelId = parseInt(novelIdFromTemplate) || novelIdFromTemplate;
        } catch (e) {
            console.error('解析novelId出错:', e);
            novelId = novelIdFromTemplate;
        }

        // 获取所有分析结果和进度
        Promise.all([
            fetch(`/api/novels/${novelId}/analysis`).then(res => res.json()),
            fetch(`/api/analysis/progress?novel_id=${novelId}`).then(res => res.json())
        ])
        .then(([results, progress]) => {
            console.log('更新维度状态:', results, progress);

            // 如果获取到了新的分析结果，更新全局变量
            if (results && Array.isArray(results) && results.length > 0) {
                console.log('API返回的分析结果数据:', results);

                // 将结果转换为以维度为键的对象
                const newResultsData = {};
                results.forEach(result => {
                    if (result && result.dimension) {
                        console.log(`处理维度 ${result.dimension} 的结果:`, {
                            contentType: typeof result.content,
                            contentLength: result.content ? result.content.length : 0,
                            metadataType: typeof result.metadata,
                            metadataKeys: result.metadata ? Object.keys(result.metadata) : []
                        });

                        // 确保内容是字符串
                        let content = '';
                        if (result.content) {
                            if (typeof result.content === 'string') {
                                content = result.content;
                            } else {
                                console.warn(`维度 ${result.dimension} 的内容不是字符串:`, result.content);
                                try {
                                    content = JSON.stringify(result.content);
                                } catch (e) {
                                    console.error(`转换维度 ${result.dimension} 的内容为字符串时出错:`, e);
                                    content = '内容格式错误';
                                }
                            }
                        }

                        // 确保元数据是对象
                        let metadata = {};
                        if (result.metadata) {
                            if (typeof result.metadata === 'object') {
                                metadata = result.metadata;
                            } else {
                                console.warn(`维度 ${result.dimension} 的元数据不是对象:`, result.metadata);
                                try {
                                    metadata = JSON.parse(result.metadata);
                                } catch (e) {
                                    console.error(`解析维度 ${result.dimension} 的元数据时出错:`, e);
                                    metadata = {};
                                }
                            }
                        }

                        newResultsData[result.dimension] = {
                            dimension: result.dimension,
                            content: content,
                            metadata: metadata
                        };
                    }
                });

                // 更新全局变量
                if (Object.keys(newResultsData).length > 0) {
                    console.log('更新全局分析结果数据:', newResultsData);
                    analysisResultsData = newResultsData;

                    // 初始化图表
                    setTimeout(initializeCharts, 500);
                }
            }

            // 获取所有维度的状态表格行
            const rows = document.querySelectorAll('tr[data-dimension]');

            rows.forEach(row => {
                const dimension = row.getAttribute('data-dimension');
                const statusCell = row.querySelector('.dimension-status');
                const actionCell = row.querySelector('td:last-child');

                // 检查是否有已完成的结果
                const result = results.find(r => r.dimension === dimension);

                // 检查是否有进行中的分析
                const inProgress = progress.success &&
                                  progress.progress &&
                                  progress.progress[dimension] &&
                                  progress.progress[dimension].progress < 100 &&
                                  progress.progress[dimension].progress >= 0;

                // 检查是否有错误
                const hasError = progress.success &&
                                progress.progress &&
                                progress.progress[dimension] &&
                                progress.progress[dimension].progress < 0;

                if (result) {
                    // 分析已完成
                    statusCell.innerHTML = `<span class="badge bg-success">已完成分析</span>`;
                    actionCell.innerHTML = `
                        <a href="${window.location.pathname}/analysis/${dimension}"
                           class="btn btn-sm btn-outline-primary">
                            查看详情
                        </a>
                        <button class="btn btn-sm btn-outline-secondary analyze-single-dimension ms-2"
                                data-dimension="${dimension}"
                                data-novel-id="${novelId}">
                            重新分析
                        </button>
                    `;

                    // 更新卡片显示
                    let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                    if (card) {
                        const cardBody = card.querySelector('.card-body');
                        if (cardBody) {
                            // 创建可视化和结果内容
                            let visualizationHtml = '';
                            if (result.metadata && result.metadata.visualization_data) {
                                visualizationHtml = `
                                    <div class="analysis-visualization mt-3">
                                        <canvas class="analysis-chart" data-dimension="${dimension}" width="400" height="250"></canvas>
                                    </div>
                                `;
                            }

                            // 提取内容的前300个字符作为摘要
                            let excerpt = '';
                            try {
                                // 去除HTML标签，只保留文本内容
                                const tempDiv = document.createElement('div');
                                tempDiv.innerHTML = result.content || '';
                                const textContent = tempDiv.textContent || tempDiv.innerText || '';
                                excerpt = textContent.substring(0, 300) + '...';
                            } catch (e) {
                                console.error(`提取摘要时出错:`, e);
                                excerpt = (result.content || '').substring(0, 300) + '...';
                            }

                            // 更新卡片内容
                            cardBody.innerHTML = `
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="badge bg-success">分析完成</span>
                                    <a href="${window.location.pathname}/analysis/${dimension}" class="btn btn-sm btn-outline-primary">查看详情</a>
                                </div>
                                ${visualizationHtml}
                                <div class="analysis-excerpt mt-2">
                                    ${excerpt}
                                </div>
                            `;

                            // 初始化图表
                            setTimeout(() => {
                                const canvas = card.querySelector('.analysis-chart');
                                if (canvas) {
                                    initializeCharts();
                                }
                            }, 100);
                        }
                    }
                } else if (inProgress) {
                    // 分析进行中
                    const currentProgress = progress.progress[dimension].progress;
                    const estimatedTime = progress.progress[dimension].estimated_time;

                    statusCell.innerHTML = `
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                 aria-valuenow="${currentProgress}"
                                 aria-valuemin="0"
                                 aria-valuemax="100"
                                 style="width: ${currentProgress}%">
                                ${currentProgress}%
                            </div>
                        </div>
                        <small class="text-muted">预计剩余: ${estimatedTime}</small>
                    `;

                    actionCell.innerHTML = `
                        <span class="text-muted">分析中...</span>
                        <button class="btn btn-sm btn-outline-danger stop-analysis-btn ms-2"
                                data-dimension="${dimension}"
                                data-novel-id="${novelIdFromTemplate}">
                            终止
                        </button>
                    `;

                    // 更新卡片显示
                    let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                    if (!card) {
                        card = createDimensionCard(dimension);
                    }

                    if (card) {
                        const cardBody = card.querySelector('.card-body');
                        if (cardBody) {
                            const displayProgress = Math.max(currentProgress, 10);

                            cardBody.innerHTML = `
                                <div class="analysis-progress">
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                            role="progressbar"
                                            id="progress-${dimension}"
                                            aria-valuenow="${currentProgress}"
                                            aria-valuemin="0"
                                            aria-valuemax="100"
                                            data-progress="${currentProgress}"
                                            data-min-display="10"
                                            style="width: ${displayProgress}%">
                                            ${currentProgress}%
                                        </div>
                                    </div>
                                    <div class="analysis-status">
                                        <span>分析中...</span>
                                        <span id="time-${dimension}">预计剩余时间: ${estimatedTime}</span>
                                        <button class="btn btn-sm btn-danger stop-analysis-btn mt-2"
                                                data-dimension="${dimension}"
                                                data-novel-id="${novelIdFromTemplate}">
                                            终止分析
                                        </button>
                                    </div>
                                    <div class="analysis-visualization mt-3">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">正在分析中，请稍候...</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                    }
                } else if (hasError) {
                    // 分析出错或被终止
                    const errorMsg = progress.progress[dimension].estimated_time;
                    const isTerminated = progress.progress[dimension].progress === -2;

                    if (isTerminated) {
                        // 分析被终止，显示断点继续按钮
                        statusCell.innerHTML = `<span class="badge bg-warning">已终止</span>`;
                        actionCell.innerHTML = `
                            <button class="btn btn-sm btn-outline-primary resume-analysis-btn"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                从断点继续
                            </button>
                            <button class="btn btn-sm btn-outline-danger analyze-single-dimension ms-2"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                重新开始
                            </button>
                        `;

                        // 更新卡片显示
                        let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                        if (card) {
                            const cardBody = card.querySelector('.card-body');
                            if (cardBody) {
                                cardBody.innerHTML = `
                                    <div class="alert alert-warning">
                                        <strong>分析已终止</strong>
                                        <p>您可以从断点继续分析，或重新开始分析。</p>
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-primary resume-analysis-btn"
                                                    data-dimension="${dimension}"
                                                    data-novel-id="${novelIdFromTemplate}">
                                                从断点继续
                                            </button>
                                            <button class="btn btn-sm btn-danger analyze-single-dimension ms-2"
                                                    data-dimension="${dimension}"
                                                    data-novel-id="${novelIdFromTemplate}">
                                                重新开始
                                            </button>
                                        </div>
                                    </div>
                                `;
                            }
                        }
                    } else {
                        // 分析出错
                        statusCell.innerHTML = `<span class="badge bg-danger">分析失败</span>`;
                        actionCell.innerHTML = `
                            <button class="btn btn-sm btn-outline-danger analyze-single-dimension"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                重试
                            </button>
                        `;

                        // 更新卡片显示
                        let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                        if (card) {
                            const cardBody = card.querySelector('.card-body');
                            if (cardBody) {
                                cardBody.innerHTML = `
                                    <div class="alert alert-danger">
                                        <strong>分析失败</strong>
                                        <p>${errorMsg || '未知错误'}</p>
                                        <button class="btn btn-sm btn-danger analyze-single-dimension mt-2"
                                                data-dimension="${dimension}"
                                                data-novel-id="${novelIdFromTemplate}">
                                            重试分析
                                        </button>
                                    </div>
                                `;
                            }
                        }
                    }
                } else {
                    // 未分析
                    statusCell.innerHTML = `<span class="badge bg-secondary">未分析</span>`;
                    actionCell.innerHTML = `
                        <button class="btn btn-sm btn-outline-primary analyze-single-dimension"
                                data-dimension="${dimension}"
                                data-novel-id="${novelIdFromTemplate}">
                            开始分析
                        </button>
                    `;

                    // 更新卡片显示
                    let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                    if (card) {
                        const cardBody = card.querySelector('.card-body');
                        if (cardBody) {
                            cardBody.innerHTML = `
                                <div class="text-center py-4">
                                    <p class="text-muted">尚未分析此维度</p>
                                    <button class="btn btn-primary analyze-single-dimension mt-2"
                                            data-dimension="${dimension}"
                                            data-novel-id="${novelIdFromTemplate}">
                                        开始分析
                                    </button>
                                </div>
                            `;
                        }
                    }
                }
            });

            // 不再需要重新绑定按钮事件，我们将使用事件委托
        })
        .catch(error => {
            console.error('更新维度状态时出错:', error);
            // 记录更详细的错误信息
            console.error('错误详情:', { message: error.message, stack: error.stack });
        });
    }

    // 初始显示所有维度
    displayAllDimensions();

    // 定时更新维度状态
    setInterval(updateDimensionStatus, 3000); // 每3秒更新一次维度状态

    // 单个维度分析函数
    function startSingleDimensionAnalysis(novelId, dimension) {
        console.log(`开始分析小说 ${novelId} 的 ${dimension} 维度`);

        // 更新UI显示分析进行中
        const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
        if (row) {
            const statusCell = row.querySelector('.dimension-status');
            const actionCell = row.querySelector('td:last-child');

            statusCell.innerHTML = `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar"
                         aria-valuenow="0"
                         aria-valuemin="0"
                         aria-valuemax="100"
                         style="width: 0%">
                        0%
                    </div>
                </div>
                <small class="text-muted">预计剩余: 计算中...</small>
            `;

            actionCell.innerHTML = `
                <span class="text-muted">分析中...</span>
                <button class="btn btn-sm btn-outline-danger stop-analysis-btn ms-2"
                        data-dimension="${dimension}"
                        data-novel-id="${novelIdFromTemplate}">
                    终止
                </button>
            `;
        }

        // 更新卡片显示
        let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
        if (!card) {
            card = createDimensionCard(dimension);
        }

        if (card) {
            const cardBody = card.querySelector('.card-body');
            if (cardBody) {
                cardBody.innerHTML = `
                    <div class="analysis-progress">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                role="progressbar"
                                id="progress-${dimension}"
                                aria-valuenow="0"
                                aria-valuemin="0"
                                aria-valuemax="100"
                                data-progress="0"
                                data-min-display="10"
                                style="width: 10%">
                                0%
                            </div>
                        </div>
                        <div class="analysis-status">
                            <span>分析中...</span>
                            <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                            <button class="btn btn-sm btn-danger stop-analysis-btn mt-2"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                终止分析
                            </button>
                        </div>
                        <div class="analysis-visualization mt-3">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">正在分析中，请稍候...</p>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // 发送API请求开始分析
        fetch(`/api/novels/${novelId}/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                dimensions: [dimension]
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`成功启动 ${dimension} 分析:`, data);
                // 启动进度更新
                updateAnalysisProgress();
            } else {
                console.error(`启动 ${dimension} 分析失败:`, data);
                alert('启动分析失败: ' + (data.error || '未知错误'));

                // 恢复UI状态
                if (row) {
                    const statusCell = row.querySelector('.dimension-status');
                    const actionCell = row.querySelector('td:last-child');

                    statusCell.innerHTML = `<span class="badge bg-danger">启动失败</span>`;
                    actionCell.innerHTML = `
                        <button class="btn btn-sm btn-outline-primary analyze-single-dimension"
                                data-dimension="${dimension}"
                                data-novel-id="${novelId}">
                            重试分析
                        </button>
                    `;
                }

                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    if (cardBody) {
                        cardBody.innerHTML = `
                            <div class="alert alert-danger">
                                <strong>启动分析失败</strong>
                                <p>${data.error || '未知错误'}</p>
                                <button class="btn btn-sm btn-primary analyze-single-dimension mt-2"
                                        data-dimension="${dimension}"
                                        data-novel-id="${novelIdFromTemplate}">
                                    重试分析
                                </button>
                            </div>
                        `;
                    }
                }
            }
        })
        .catch(error => {
            console.error('分析请求错误:', error);
            alert('请求错误: ' + error.message);

            // 恢复UI状态
            if (row) {
                const statusCell = row.querySelector('.dimension-status');
                const actionCell = row.querySelector('td:last-child');

                statusCell.innerHTML = `<span class="badge bg-danger">请求错误</span>`;
                actionCell.innerHTML = `
                    <button class="btn btn-sm btn-outline-primary analyze-single-dimension"
                            data-dimension="${dimension}"
                            data-novel-id="${novelIdFromTemplate}">
                        重试分析
                    </button>
                `;
            }

            if (card) {
                const cardBody = card.querySelector('.card-body');
                if (cardBody) {
                    cardBody.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>请求错误</strong>
                            <p>${error.message}</p>
                            <button class="btn btn-sm btn-primary analyze-single-dimension mt-2"
                                    data-dimension="${dimension}"
                                    data-novel-id="${novelIdFromTemplate}">
                                重试分析
                            </button>
                        </div>
                    `;
                }
            }
        });
    }

    // 使用事件委托处理分析按钮点击
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('analyze-single-dimension')) {
            const dimension = e.target.getAttribute('data-dimension');
            const novelId = e.target.getAttribute('data-novel-id');
            console.log(`点击了维度 ${dimension} 的分析按钮，小说ID: ${novelId}`);

            if (!dimension || !novelId) {
                console.error('缺少必要的数据属性: dimension或novel-id');
                alert('无法启动分析：缺少必要的参数');
                return;
            }

            startSingleDimensionAnalysis(novelId, dimension);
        }

        // 处理重新分析按钮点击
        if (e.target && (e.target.classList.contains('reanalyze-btn') ||
                        (e.target.parentElement && e.target.parentElement.classList.contains('reanalyze-btn')))) {
            // 获取按钮元素（可能是图标的父元素）
            const button = e.target.classList.contains('reanalyze-btn') ? e.target : e.target.parentElement;
            const dimension = button.getAttribute('data-dimension');
            const novelId = button.getAttribute('data-novel-id');
            console.log(`点击了维度 ${dimension} 的重新分析按钮，小说ID: ${novelId}`);

            if (!dimension || !novelId) {
                console.error('缺少必要的数据属性: dimension或novel-id');
                alert('无法启动重新分析：缺少必要的参数');
                return;
            }

            if (confirm(`确定要重新分析"${dimension}"维度吗？现有结果将被覆盖。`)) {
                reanalyzeExistingDimension(novelId, dimension);
            }
        }
    });

    // 终止分析按钮事件
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('stop-analysis-btn')) {
            const dimension = e.target.getAttribute('data-dimension');
            const novelId = e.target.getAttribute('data-novel-id');

            if (confirm('确定要终止分析吗？您可以在稍后从断点继续分析。')) {
                stopAnalysis(novelId, dimension);
            }
        }
    });

    // 从断点继续按钮事件
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('resume-analysis-btn')) {
            const dimension = e.target.getAttribute('data-dimension');
            const novelId = e.target.getAttribute('data-novel-id');

            resumeAnalysis(novelId, dimension);
        }
    });

    // 终止分析函数
    function stopAnalysis(novelId, dimension) {
        fetch(`/api/novels/${novelId}/stop_analysis`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                dimension: dimension
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('分析已终止');
                // 刷新页面或更新UI
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                alert('终止分析失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('终止分析请求错误:', error);
            alert('请求错误: ' + error.message);
        });
    }

    // 重新分析已有维度
    function reanalyzeExistingDimension(novelId, dimension) {
        console.log(`开始重新分析维度 ${dimension}，小说ID: ${novelId}`);

        // 先删除现有结果
        fetch(`/api/novels/${novelId}/analysis/${dimension}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`成功删除维度 ${dimension} 的现有结果，开始新的分析`);

                // 删除成功后，开始新的分析
                return fetch(`/api/novels/${novelId}/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dimensions: [dimension],
                        force_refresh: true
                    })
                });
            } else {
                throw new Error(`删除维度 ${dimension} 的现有结果失败: ${data.error || '未知错误'}`);
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`成功启动维度 ${dimension} 的重新分析`);

                // 更新UI，显示分析进度
                const card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    if (cardBody) {
                        // 显示进度条
                        cardBody.innerHTML = `
                            <div class="analysis-progress">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        id="progress-${dimension}"
                                        aria-valuenow="10"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        data-progress="10"
                                        data-min-display="10"
                                        style="width: 10%">
                                        10%
                                    </div>
                                </div>
                                <div class="analysis-status">
                                    <span>分析中...</span>
                                    <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                                </div>
                                <div class="analysis-visualization mt-3">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">正在分析中，请稍候...</p>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }

                // 启动进度更新
                updateAnalysisProgress();
            } else {
                alert(`启动分析失败: ${data.error || '未知错误'}`);
            }
        })
        .catch(error => {
            console.error('重新分析请求出错:', error);
            alert(`请求错误: ${error.message}`);
        });
    }

    // 从断点继续分析函数
    function resumeAnalysis(novelId, dimension) {
        fetch(`/api/novels/${novelId}/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                dimensions: [dimension],
                resume: true
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('从断点继续分析');
                // 更新UI显示分析进行中
                const row = document.querySelector(`tr[data-dimension="${dimension}"]`);
                if (row) {
                    const statusCell = row.querySelector('.dimension-status');
                    const actionCell = row.querySelector('td:last-child');

                    statusCell.innerHTML = `
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                 aria-valuenow="0"
                                 aria-valuemin="0"
                                 aria-valuemax="100"
                                 style="width: 0%">
                                0%
                            </div>
                        </div>
                        <small class="text-muted">预计剩余: 计算中...</small>
                    `;

                    actionCell.innerHTML = `
                        <span class="text-muted">分析中...</span>
                        <button class="btn btn-sm btn-outline-danger stop-analysis-btn ms-2"
                                data-dimension="${dimension}"
                                data-novel-id="${novelIdFromTemplate}">
                            终止
                        </button>
                    `;
                }

                // 更新卡片显示
                let card = document.querySelector(`.analysis-card[data-dimension="${dimension}"]`);
                if (!card) {
                    card = createDimensionCard(dimension);
                }

                if (card) {
                    const cardBody = card.querySelector('.card-body');
                    if (cardBody) {
                        cardBody.innerHTML = `
                            <div class="analysis-progress">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        id="progress-${dimension}"
                                        aria-valuenow="0"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        data-progress="0"
                                        data-min-display="10"
                                        style="width: 10%">
                                        0%
                                    </div>
                                </div>
                                <div class="analysis-status">
                                    <span>分析中...</span>
                                    <span id="time-${dimension}">预计剩余时间: 计算中...</span>
                                    <button class="btn btn-sm btn-danger stop-analysis-btn mt-2"
                                            data-dimension="${dimension}"
                                            data-novel-id="${novelIdFromTemplate}">
                                        终止分析
                                    </button>
                                </div>
                                <div class="analysis-visualization mt-3">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">正在分析中，请稍候...</p>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }

                // 启动进度更新
                updateAnalysisProgress();
            } else {
                alert('从断点继续分析失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('从断点继续分析请求错误:', error);
            alert('请求错误: ' + error.message);
        });
    }

    // 在分析开始时启动控制台日志轮询
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化控制台日志，传递小说标题
        initConsoleLogger(novelIdFromTemplate, novelTitleFromTemplate);

        // 监听分析按钮点击事件
        document.querySelectorAll('.analyze-btn, .analyze-all-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 清空控制台
                clearConsole();
                // 不添加欢迎消息，直接开始轮询
                startLogPolling();
            });
        });
    });
</script>
<script src="/static/js/console-logger.js"></script>

</body>
</html>