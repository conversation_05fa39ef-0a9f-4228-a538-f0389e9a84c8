{% extends "base.html" %}

{% block title %}{{ novel.title }} - 九猫{% endblock %}

{% block head %}
{{ super() }}
<!-- 小说详情页面不停刷新修复脚本 - 最高优先级 -->
<script>
// 内联脚本，确保最先执行
(function() {
    console.log('[小说页面修复-内联] 初始化临时刷新阻止器');

    // 保存原始的reload方法
    var originalReload = window.location.reload;
    var originalAssign = window.location.assign;
    var originalReplace = window.location.replace;

    // 临时禁用reload方法，直到完整的修复脚本加载
    window.location.reload = function() {
        console.warn('[小说页面修复-内联] 拦截到页面刷新尝试');
        return false;
    };

    // 临时拦截跳转到首页的尝试
    window.location.assign = function(url) {
        if (url === '/' || url === '/index' || url === '/home') {
            console.warn('[小说页面修复-内联] 拦截到跳转到首页的尝试');
            return false;
        }
        return originalAssign.apply(this, arguments);
    };

    window.location.replace = function(url) {
        if (url === '/' || url === '/index' || url === '/home') {
            console.warn('[小说页面修复-内联] 拦截到跳转到首页的尝试');
            return false;
        }
        return originalReplace.apply(this, arguments);
    };

    // 清除所有可能的定时器
    for (var i = 1; i < 1000; i++) {
        try {
            clearTimeout(i);
            clearInterval(i);
        } catch (e) {}
    }

    // 获取当前小说ID并存储为全局变量，方便其他脚本使用
    try {
        var match = window.location.pathname.match(/\/novel\/(\d+)/);
        if (match && match[1]) {
            window.novelIdFromTemplate = match[1];
            console.log('[小说页面修复-内联] 当前小说ID: ' + window.novelIdFromTemplate);
        }
    } catch (e) {}

    console.log('[小说页面修复-内联] 临时刷新阻止器和跳转阻止器已启用');
})();
</script>

<!-- 小说页面专用修复脚本 -->
<script src="{{ url_for('static', filename='js/novel-page-refresh-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/novel-page-refresh-fix.js';"></script>
<script src="{{ url_for('static', filename='js/novel-page-redirect-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/novel-page-redirect-fix.js';"></script>
<script src="{{ url_for('static', filename='js/novel-link-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/novel-link-fix.js';"></script>
<script src="{{ url_for('static', filename='js/chapter-navigation-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/chapter-navigation-fix.js';"></script>
<script src="{{ url_for('static', filename='js/novel-navigation-fix.js') }}" crossorigin="anonymous" onerror="this.onerror=null;this.src='/direct-static/js/novel-navigation-fix.js';"></script>

<!-- 新的分析结果显示样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/analysis-display.css') }}" onerror="this.onerror=null;this.href='/direct-static/css/analysis-display.css';">
{% endblock %}

{% block extra_css %}
<style>
    .novel-excerpt {
        max-height: 300px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        font-size: 0.9rem;
        white-space: pre-wrap;
    }

    .analysis-card {
        height: 100%;
    }

    .analysis-content {
        max-height: 200px;
        overflow-y: auto;
    }

    .analysis-progress {
        margin-top: 10px;
        margin-bottom: 15px;
    }

    .analysis-status {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        color: #666;
        margin-top: 5px;
    }

    .analysis-visualization {
        margin-top: 15px;
        height: 150px;
        background-color: #f8f9fa;
        border-radius: 5px;
        overflow: hidden;
    }

    .analysis-chart {
        width: 100%;
        height: 100%;
    }

    /* 控制台样式 */
    .console-output {
        background-color: #1e1e1e;
        color: #f0f0f0;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.85rem;
        height: 300px;
        overflow-y: auto;
        padding: 10px;
        border-radius: 0 0 5px 5px;
    }

    .console-inner {
        padding: 5px;
    }

    .console-welcome {
        color: #888;
        margin-bottom: 10px;
    }

    .console-line {
        margin: 2px 0;
        line-height: 1.4;
        white-space: pre-wrap;
        word-break: break-word;
    }

    .log-debug {
        color: #888;
    }

    .log-info {
        color: #4CAF50;
    }

    .log-warning {
        color: #FFC107;
    }

    .log-error {
        color: #F44336;
    }

    .log-timestamp {
        color: #888;
        margin-right: 5px;
    }

    .log-dimension {
        color: #2196F3;
        font-weight: bold;
        margin-right: 5px;
    }

    .log-progress {
        color: #9C27B0;
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 针对小说页面的应急修复 -->
<div id="extension-warning-container"></div>
<script>
(function() {
    // 立即运行，处理章节分析按钮
    function fixChapterAnalysisButtons() {
        try {
            // 获取当前小说ID - 直接从环境变量中获取
            const novelId = "{{ novel.id }}";
            console.log("[小说页面修复] 当前小说ID: " + novelId);

            // 存储到全局变量
            window.novelId = novelId;

            // 找到所有章节分析按钮
            const buttons = document.querySelectorAll('a.btn, button.btn');
            buttons.forEach(function(btn) {
                if (btn.textContent.includes('章节分析') ||
                    (btn.textContent.includes('章节') && btn.textContent.includes('分析'))) {
                    console.log("[小说页面修复] 找到章节分析按钮: " + btn.textContent.trim());

                    // 设置正确的href和onclick
                    if (btn.tagName === 'A') {
                        btn.href = "/novel/" + novelId + "/chapters";
                    }

                    // 保存原始onclick
                    var originalOnclick = btn.onclick;

                    // 覆盖onclick
                    btn.onclick = function(e) {
                        console.log("[小说页面修复] 章节分析按钮被点击");
                        e.preventDefault();
                        e.stopPropagation();

                        // 延迟执行跳转，避免被其他脚本拦截
                        setTimeout(function() {
                            window.location.href = "/novel/" + novelId + "/chapters";
                        }, 10);

                        return false;
                    };

                    console.log("[小说页面修复] 章节分析按钮已修复");
                }
            });
        } catch (e) {
            console.error("[小说页面修复] 修复章节分析按钮时出错:", e);
        }
    }

    // 页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixChapterAnalysisButtons);
    } else {
        fixChapterAnalysisButtons();
    }

    // 加载浏览器扩展警告组件
    function loadExtensionWarning() {
        try {
            fetch("{{ url_for('static', filename='js/extension-warning.html') }}")
                .then(response => response.text())
                .then(html => {
                    document.getElementById('extension-warning-container').innerHTML = html;
                })
                .catch(err => {
                    console.error("[小说页面修复] 加载扩展警告组件失败:", err);
                });
        } catch (e) {
            console.error("[小说页面修复] 加载扩展警告组件时出错:", e);
        }
    }

    // 加载扩展警告组件
    setTimeout(loadExtensionWarning, 1000);
})();
</script>

{% if novel.id == 41 %}
<!-- 针对小说41的特殊修复脚本 -->
<script src="{{ url_for('static', filename='js/novel-41-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-41-fix.js';"></script>
{% endif %}

<!-- replaceChild 修复脚本 - 最高优先级 -->
<script src="{{ url_for('static', filename='js/replace-child-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/replace-child-fix.js';"></script>

<!-- 加载统一错误处理脚本，处理所有错误 -->
<script src="{{ url_for('static', filename='js/unified-error-handler.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/unified-error-handler.js';"></script>

<!-- 维度容器修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-container-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-container-fix.js';"></script>

<!-- 维度状态修复脚本 -->
<script src="{{ url_for('static', filename='js/dimension-status-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/dimension-status-fix.js';"></script>

<!-- 分析完成修复脚本 - 解决13个维度全部完成时分析结果不可见的问题 -->
<script src="{{ url_for('static', filename='js/analysis-complete-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/analysis-complete-fix.js';"></script>

<!-- 小说与章节分析显示修复脚本 -->
<script src="{{ url_for('static', filename='js/novel-chapter-display-fix.js') }}" onerror="this.onerror=null;this.src='/direct-static/js/novel-chapter-display-fix.js';"></script>

<div class="row"
     data-novel-id="{{ novel.id }}"
     data-novel-title="{{ novel.title }}"
     id="novel-container">
    <div class="col-md-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item active">{{ novel.title }}</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <h1>{{ novel.title }}</h1>
            <div>
                <a href="{{ url_for('chapter_fix.direct_chapters', novel_id=novel.id) }}" class="btn btn-success me-2 chapter-analysis-btn" data-novel-id="{{ novel.id }}" onclick="console.log('[小说详情页面] 点击章节分析按钮');">
                    <i class="fas fa-book-open"></i> 章节分析
                </a>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                    分析小说
                </button>
                <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                    删除
                </button>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">小说信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>作者：</strong> {{ novel.author or '未知' }}</p>
                        <p><strong>字数：</strong> {{ novel.word_count }}</p>
                        <p><strong>上传时间：</strong> {{ novel.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">小说摘要</h5>
                    </div>
                    <div class="card-body">
                        <div class="novel-excerpt">
                            {{ novel.get_excerpt(2000) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="mt-5">分析结果</h2>
                    <div>
                        <a href="{{ url_for('chapter_fix.direct_summary', novel_id=novel.id) }}" class="btn btn-success me-2 chapter-summary-btn" data-novel-id="{{ novel.id }}" onclick="console.log('[小说详情页面] 点击章节分析汇总按钮');">
                            <i class="fas fa-list-alt"></i> 章节分析汇总
                        </a>
                        <button type="button" class="btn btn-info me-2" id="syncToChaptersBtn">
                            <i class="fas fa-sync-alt"></i> 同步到章节
                        </button>
                        <button type="button" class="btn btn-warning me-2" id="aggregateChaptersBtn">
                            <i class="fas fa-compress-alt"></i> 汇总章节分析
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                            分析小说
                        </button>
                    </div>
                </div>

                <!-- 添加控制台日志显示区域 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center bg-dark text-white">
                        <h5 class="mb-0">分析控制台</h5>
                        <div>
                            <select class="form-select form-select-sm" id="console-log-level">
                                <option value="all">所有级别</option>
                                <option value="info">信息</option>
                                <option value="warning">警告</option>
                                <option value="error">错误</option>
                                <option value="debug">调试</option>
                            </select>
                            <button class="btn btn-sm btn-outline-light ms-2" id="clear-console">清空</button>
                            <button class="btn btn-sm btn-outline-light ms-2" id="copy-console">复制</button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="analysis-console" class="console-output">
                            <div class="console-inner">
                                <div class="console-welcome">
                                    <span class="text-muted">// 分析控制台已准备就绪，等待分析开始...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载指示器 -->
                <div id="loadingIndicator"></div>

                <!-- 分析状态卡片 -->
                <div class="card analysis-status-card mt-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="mb-0">分析状态</h3>
                            <div>
                                {% if completed_dimensions_count == total_dimensions_count %}
                                    <span class="badge bg-success p-2 analysis-status-badge">{{ completed_dimensions_count }}/{{ total_dimensions_count }} 分析已完成</span>
                                {% else %}
                                    <span class="badge bg-warning p-2 analysis-status-badge">{{ completed_dimensions_count }}/{{ total_dimensions_count }} 分析未完成</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="dimensions-status-table">
                                <thead class="table-light">
                                    <tr>
                                        <th>维度</th>
                                        <th>状态</th>
                                        <th>进度</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 13个维度的行 -->
                                    {% for dimension in [
                                        {'key': 'language_style', 'name': '语言风格'},
                                        {'key': 'rhythm_pacing', 'name': '节奏与节奏'},
                                        {'key': 'structure', 'name': '结构分析'},
                                        {'key': 'sentence_variation', 'name': '句式变化'},
                                        {'key': 'paragraph_length', 'name': '段落长度'},
                                        {'key': 'perspective_shifts', 'name': '视角转换'},
                                        {'key': 'paragraph_flow', 'name': '段落流畅度'},
                                        {'key': 'novel_characteristics', 'name': '小说特点'},
                                        {'key': 'world_building', 'name': '世界构建'},
                                        {'key': 'chapter_outline', 'name': '章纲分析'},
                                        {'key': 'character_relationships', 'name': '人物关系'},
                                        {'key': 'opening_effectiveness', 'name': '开篇效果'},
                                        {'key': 'climax_pacing', 'name': '高潮节奏'}
                                    ] %}
                                    <tr data-dimension="{{ dimension.key }}">
                                        <td>{{ dimension.name }}</td>
                                        <td class="dimension-status"><span class="badge bg-secondary">未知</span></td>
                                        <td class="dimension-progress">
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" style="width: 0%;"
                                                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                            </div>
                                        </td>
                                        <td class="dimension-actions">
                                            <button class="btn btn-sm btn-outline-primary analyze-btn"
                                                    data-dimension="{{ dimension.key }}"
                                                    data-novel-id="{{ novel.id }}">
                                                分析
                                            </button>
                                            <a href="/novel/{{ novel.id }}/analysis/{{ dimension.key }}"
                                               class="btn btn-sm btn-outline-secondary ms-1 view-btn"
                                               data-dimension="{{ dimension.key }}"
                                               data-novel-id="{{ novel.id }}">
                                                查看
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 分析结果部分 -->
                <div class="analysis-section mt-4" id="analysis-section-container">
                    <h3 class="mb-3">分析结果</h3>

                    {% if analysis_results %}
                        <div class="row">
                            {% for dimension, result in analysis_results.items() %}
                                <div class="col-md-4 mb-4">
                                    <div class="card analysis-card" data-dimension="{{ dimension }}">
                                        <div class="card-header">
                                            <h5 class="card-title">
                                                {% if dimension == 'language_style' %}
                                                    语言风格
                                                {% elif dimension == 'rhythm_pacing' %}
                                                    节奏与节奏
                                                {% elif dimension == 'structure' %}
                                                    结构分析
                                                {% elif dimension == 'sentence_variation' %}
                                                    句式变化
                                                {% elif dimension == 'paragraph_length' %}
                                                    段落长度
                                                {% elif dimension == 'perspective_shifts' %}
                                                    视角转换
                                                {% elif dimension == 'paragraph_flow' %}
                                                    段落流畅度
                                                {% elif dimension == 'novel_characteristics' %}
                                                    小说特点
                                                {% elif dimension == 'world_building' %}
                                                    世界构建
                                                {% elif dimension == 'chapter_outline' %}
                                                    章纲分析
                                                {% elif dimension == 'character_relationships' %}
                                                    人物关系
                                                {% elif dimension == 'opening_effectiveness' %}
                                                    开篇效果
                                                {% elif dimension == 'climax_pacing' %}
                                                    高潮节奏
                                                {% else %}
                                                    {{ dimension }}
                                                {% endif %}
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            {% if result %}
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span class="badge bg-success">分析完成</span>
                                                    <a href="/novel/{{ novel.id }}/analysis/{{ dimension }}"
                                                       class="btn btn-sm btn-outline-primary view-analysis-btn"
                                                       data-novel-id="{{ novel.id }}"
                                                       data-dimension="{{ dimension }}">
                                                        查看详情
                                                    </a>
                                                </div>
                                                <div class="analysis-excerpt mt-2">
                                                    {% if result is mapping %}
                                                        {% if result.content|length > 300 %}
                                                            {{ (result.content + "...")|safe }}
                                                        {% else %}
                                                            {{ result.content|safe }}
                                                        {% endif %}
                                                    {% else %}
                                                        {% if result.get_excerpt is defined %}
                                                            {{ result.get_excerpt(300)|safe }}
                                                        {% else %}
                                                            {% if result.content|length > 300 %}
                                                                {{ (result.content + "...")|safe }}
                                                            {% else %}
                                                                {{ result.content|safe }}
                                                            {% endif %}
                                                        {% endif %}
                                                    {% endif %}
                                                </div>
                                            {% elif dimension in analysis_in_progress %}
                                                <div class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">加载中...</span>
                                                    </div>
                                                    <p class="mt-2">正在分析中，请稍候...</p>
                                                    <div class="progress mt-3">
                                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                             role="progressbar"
                                                             id="progress-{{ dimension }}"
                                                             aria-valuenow="{{ analysis_in_progress[dimension].get('progress', 0) }}"
                                                             aria-valuemin="0"
                                                             aria-valuemax="100"
                                                             data-progress="{{ max(10, analysis_in_progress[dimension].get('progress', 0)) }}">
                                                            {{ analysis_in_progress[dimension].get('progress', 0) }}%
                                                        </div>
                                                    </div>
                                                </div>
                                            {% else %}
                                                <div class="text-center py-3">
                                                    <span class="badge bg-secondary mb-3">未分析</span>
                                                    <p class="text-muted">尚未对该维度进行分析</p>
                                                    <button class="btn btn-sm btn-primary analyze-single-dimension"
                                                            data-dimension="{{ dimension }}"
                                                            data-novel-id="{{ novel.id }}">
                                                        开始分析
                                                    </button>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            尚未进行任何分析。点击"分析小说"按钮开始分析，或者使用上方的维度表格选择特定维度进行分析。
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析模态框 -->
<div class="modal fade" id="analyzeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择分析维度</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('analyze_novel', novel_id=novel.id) }}" id="analyze-form" data-novel-id="{{ novel.id }}" onsubmit="return validateAnalyzeForm(this);">
                <div class="modal-body">
                    <p>请选择要分析的维度：</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="select-all-dimensions">
                        <label class="form-check-label" for="select-all-dimensions">
                            <strong>全选</strong>
                        </label>
                    </div>
                    <hr>
                    <!-- 维度列表将由JavaScript动态生成 -->
                    <!-- 这里是一个空的占位符，JavaScript会在这里添加维度列表 -->
                    <div class="alert alert-warning mt-3">
                        <small>注意：分析过程可能需要较长时间，尤其是对于长篇小说。</small>
                    </div>

                    <div class="form-check form-switch mt-3">
                        <input class="form-check-input" type="checkbox" id="parallel-analysis" name="parallel_analysis" checked>
                        <label class="form-check-label" for="parallel-analysis">
                            <strong>启用并行分析</strong> (同时分析多个维度，提高效率)
                        </label>
                    </div>

                    <div class="form-check form-switch mt-2">
                        <input class="form-check-input" type="checkbox" id="use-cache" name="use_cache" checked>
                        <label class="form-check-label" for="use-cache">
                            <strong>使用缓存结果</strong> (如果有缓存，直接使用，避免重复分析)
                        </label>
                    </div>

                    <div class="form-check form-switch mt-2">
                        <input class="form-check-input" type="checkbox" id="force-refresh" name="force_refresh">
                        <label class="form-check-label" for="force-refresh">
                            <strong>强制刷新缓存</strong> (忽略现有缓存，重新分析)
                        </label>
                    </div>

                    <div class="mt-4">
                        <label for="model-select" class="form-label"><strong>选择分析模型</strong></label>
                        <select class="form-select" id="model-select" name="model">
                            <option value="deepseek-r1" selected>阿里云 DeepSeek R1 (默认)</option>
                            <option value="qwen-plus-latest">阿里云 通义千问-Plus-Latest</option>
                        </select>
                        <div class="form-text">选择不同的模型可能会产生不同的分析结果</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">开始分析</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除小说"{{ novel.title }}"吗？此操作不可撤销，所有相关的分析结果也将被删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form method="POST" action="{{ url_for('delete_novel', novel_id=novel.id) }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 同步到章节模态框 -->
<div class="modal fade" id="syncToChaptersModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">同步到章节</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>此操作将把整本书的分析结果同步到各个章节，使章节分析页面显示分析结果。</p>
                <p>请选择要同步的维度：</p>

                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="sync-all-dimensions" checked>
                    <label class="form-check-label" for="sync-all-dimensions">
                        <strong>所有维度</strong>
                    </label>
                </div>

                <div id="sync-dimensions-list" class="ms-3">
                    <!-- 维度列表将由JavaScript动态生成 -->
                </div>

                <div class="alert alert-info mt-3">
                    <small>注意：同步操作会为每个章节创建分析结果记录，可能需要一些时间。</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmSyncBtn">开始同步</button>
            </div>
        </div>
    </div>
</div>

<!-- 汇总章节分析模态框 -->
<div class="modal fade" id="aggregateChaptersModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">汇总章节分析</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>此操作将把所有章节的分析结果汇总，创建一个整本书的汇总分析结果。</p>
                <p>请选择要汇总的维度：</p>

                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="aggregate-all-dimensions" checked>
                    <label class="form-check-label" for="aggregate-all-dimensions">
                        <strong>所有维度</strong>
                    </label>
                </div>

                <div id="aggregate-dimensions-list" class="ms-3">
                    <!-- 维度列表将由JavaScript动态生成 -->
                </div>

                <div class="alert alert-info mt-3">
                    <small>注意：汇总操作只会汇总章节特定的分析结果（即对章节单独进行分析的结果），不会汇总从整本书同步到章节的分析结果。</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAggregateBtn">开始汇总</button>
            </div>
        </div>
    </div>
</div>

{% if novel.id == 41 %}
<!-- 小说41特殊处理，确保章节分析按钮能够正常跳转 -->
<script>
(function() {
    // 当页面加载完成时执行
    function fixNovel41Links() {
        console.log('[小说41修复] 修复章节分析按钮');
        const buttons = document.querySelectorAll('a, button');

        buttons.forEach(function(btn) {
            // 检查是否是章节分析按钮
            if (btn.textContent &&
                btn.textContent.toLowerCase().includes('章节') &&
                btn.textContent.toLowerCase().includes('分析')) {

                // 修复链接
                if (btn.tagName === 'A') {
                    btn.href = '/novel/41/chapters';
                }

                // 绑定点击事件
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('[小说41修复] 点击章节分析按钮，跳转到: /novel/41/chapters');
                    window.location.href = '/novel/41/chapters';
                    return false;
                }, true);
            }
        });
    }

    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixNovel41Links);
    } else {
        // 如果页面已经加载完成，立即执行修复
        fixNovel41Links();
    }

    // 使用setTimeout代替setInterval，减少性能消耗
    setTimeout(fixNovel41Links, 1000);
    setTimeout(fixNovel41Links, 3000);
    setTimeout(fixNovel41Links, 5000);
})();
</script>
{% endif %}
{% endblock %}

{% block extra_js %}
<!-- 小说详情页历史分析修复脚本 -->
<script src="{{ url_for('static', filename='js/novel-detail-historical-fix.js') }}" data-critical="true"></script>

<!-- 大纲分析维度恢复脚本 -->
<script src="/src/web/static/js/outline-analysis-restore.js"></script>

<!-- 设置进度条宽度的脚本 -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 设置所有进度条的宽度
        document.querySelectorAll('.progress-bar[data-progress]').forEach(function(progressBar) {
            const progress = progressBar.getAttribute('data-progress');
            if (progress) {
                progressBar.style.width = progress + '%';
            }
        });
        
        // 监听DOM变化，设置新添加的进度条的宽度
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // 元素节点
                        const progressBars = node.querySelectorAll ? node.querySelectorAll('.progress-bar[data-progress]') : [];
                        progressBars.forEach(function(progressBar) {
                            const progress = progressBar.getAttribute('data-progress');
                            if (progress) {
                                progressBar.style.width = progress + '%';
                            }
                        });
                    }
                });
            });
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    });
</script>
{% endblock %}