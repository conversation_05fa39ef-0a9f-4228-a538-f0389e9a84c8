/**
 * CDN资源回退脚本
 * 用于检测CDN资源加载失败并提供本地备用资源
 */

(function() {
    console.log('CDN资源回退脚本已初始化');
    
    // CDN资源映射到本地资源
    const cdnToLocalMap = {
        // jQuery
        'jquery': {
            cdn: [
                'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js'
            ],
            local: '/static/js/lib/jquery.min.js'
        },
        // Bootstrap
        'bootstrap': {
            cdn: [
                'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js'
            ],
            local: '/static/js/lib/bootstrap.bundle.min.js'
        },
        // Bootstrap CSS
        'bootstrap-css': {
            cdn: [
                'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
                'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css'
            ],
            local: '/static/css/bootstrap.min.css'
        },
        // Font Awesome
        'fontawesome': {
            cdn: [
                'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
                'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css'
            ],
            local: '/static/css/fontawesome.min.css'
        }
    };
    
    // 检测资源加载失败并提供备用资源
    function setupResourceFallback() {
        // 监听资源加载错误事件
        window.addEventListener('error', function(event) {
            const target = event.target;
            
            // 只处理脚本和样式表加载失败
            if ((target.tagName === 'SCRIPT' || target.tagName === 'LINK') && target.src || target.href) {
                const url = target.src || target.href;
                console.warn(`资源加载失败: ${url}`);
                
                // 查找匹配的资源类型
                let resourceType = null;
                let isCdn = false;
                
                for (const [type, resources] of Object.entries(cdnToLocalMap)) {
                    if (resources.cdn.some(cdnUrl => url.includes(cdnUrl.split('/').pop().split('.')[0]))) {
                        resourceType = type;
                        isCdn = true;
                        break;
                    }
                }
                
                // 如果是CDN资源，尝试加载本地备用资源
                if (resourceType && isCdn) {
                    console.log(`尝试加载本地备用资源: ${cdnToLocalMap[resourceType].local}`);
                    
                    if (target.tagName === 'SCRIPT') {
                        const script = document.createElement('script');
                        script.src = cdnToLocalMap[resourceType].local;
                        document.head.appendChild(script);
                    } else if (target.tagName === 'LINK' && target.rel === 'stylesheet') {
                        const link = document.createElement('link');
                        link.rel = 'stylesheet';
                        link.href = cdnToLocalMap[resourceType].local;
                        document.head.appendChild(link);
                    }
                }
            }
        }, true);
    }
    
    // 检查关键资源是否已加载
    function checkCriticalResources() {
        // 检查jQuery
        if (typeof jQuery === 'undefined') {
            console.warn('jQuery未加载，尝试加载本地版本');
            const script = document.createElement('script');
            script.src = cdnToLocalMap.jquery.local;
            document.head.appendChild(script);
        }
        
        // 检查Bootstrap
        if (typeof bootstrap === 'undefined') {
            console.warn('Bootstrap未加载，尝试加载本地版本');
            const script = document.createElement('script');
            script.src = cdnToLocalMap.bootstrap.local;
            document.head.appendChild(script);
        }
        
        // 检查Bootstrap CSS
        let hasBootstrapCSS = false;
        document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            if (link.href && link.href.includes('bootstrap')) {
                hasBootstrapCSS = true;
            }
        });
        
        if (!hasBootstrapCSS) {
            console.warn('Bootstrap CSS未加载，尝试加载本地版本');
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = cdnToLocalMap['bootstrap-css'].local;
            document.head.appendChild(link);
        }
    }
    
    // 设置资源回退
    setupResourceFallback();
    
    // 页面加载完成后检查关键资源
    window.addEventListener('load', checkCriticalResources);
    
    // 也在DOMContentLoaded时检查
    document.addEventListener('DOMContentLoaded', checkCriticalResources);
    
    // 3秒后再次检查
    setTimeout(checkCriticalResources, 3000);
})();
