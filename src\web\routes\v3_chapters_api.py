"""
九猫小说分析写作系统v3.0 章节API路由
"""
import logging
import traceback
from flask import Blueprint, request, jsonify
from sqlalchemy import func
from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.analysis_result import AnalysisResult
import config

v3_chapters_api_bp = Blueprint('v3_chapters_api', __name__)
logger = logging.getLogger(__name__)

@v3_chapters_api_bp.route('/api/novel/<int:novel_id>/chapters')
def api_get_chapters(novel_id):
    """获取小说的章节列表"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 转换为字典列表
            chapters_list = []
            for chapter in chapters:
                chapters_list.append({
                    'id': chapter.id,
                    'title': chapter.title,
                    'chapter_number': chapter.chapter_number,
                    'word_count': chapter.word_count,
                    'is_analyzed': chapter.is_analyzed
                })

            return jsonify({
                'success': True,
                'chapters': chapters_list
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_chapters_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>')
def api_get_chapter(novel_id, chapter_id):
    """获取单个章节详情"""
    try:
        session = Session()
        try:
            # 获取章节
            chapter = session.query(Chapter).filter_by(
                novel_id=novel_id,
                id=chapter_id
            ).first()

            if not chapter:
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 转换为字典
            chapter_dict = {
                'id': chapter.id,
                'title': chapter.title,
                'chapter_number': chapter.chapter_number,
                'content': chapter.content,
                'word_count': chapter.word_count,
                'is_analyzed': chapter.is_analyzed,
                'created_at': chapter.created_at.isoformat() if chapter.created_at else None,
                'updated_at': chapter.updated_at.isoformat() if chapter.updated_at else None
            }

            return jsonify({
                'success': True,
                'chapter': chapter_dict
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节详情时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_chapters_api_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/dimensions')
def api_get_chapter_dimensions(novel_id, chapter_id):
    """获取章节的分析维度列表"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取章节
            chapter = session.query(Chapter).filter_by(
                novel_id=novel_id,
                id=chapter_id
            ).first()

            if not chapter:
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节'
                }), 404

            # 获取章节分析结果
            chapter_analysis_results = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id
            ).all()

            # 已分析的维度列表
            analyzed_dimensions = [result.dimension for result in chapter_analysis_results]

            # 构建维度列表
            dimensions = []
            for dimension in config.ANALYSIS_DIMENSIONS:
                dimensions.append({
                    'key': dimension['key'],
                    'name': dimension['name'],
                    'icon': dimension['icon'],
                    'is_analyzed': dimension['key'] in analyzed_dimensions
                })

            return jsonify({
                'success': True,
                'dimensions': dimensions
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析维度时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
