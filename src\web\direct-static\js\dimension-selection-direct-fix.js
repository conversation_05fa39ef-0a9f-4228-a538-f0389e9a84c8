/**
 * 九猫 - 分析维度选择直接修复脚本
 * 这个脚本专门用于直接修复分析维度选择对话框
 * 版本: 1.0.0
 */

(function() {
    console.log('[维度选择直接修复] 脚本加载中...');

    // 维度列表
    const DIMENSIONS = [
        { key: "language_style", name: "语言风格" },
        { key: "rhythm_pacing", name: "节奏与节奏" },
        { key: "structure", name: "结构分析" },
        { key: "sentence_variation", name: "句式变化" },
        { key: "paragraph_length", name: "段落长度" },
        { key: "perspective_shifts", name: "视角转换" },
        { key: "paragraph_flow", name: "段落流畅度" },
        { key: "novel_characteristics", name: "小说特点" },
        { key: "world_building", name: "世界构建" },
        { key: "chapter_outline", name: "章节大纲" },
        { key: "character_relationships", name: "人物关系" },
        { key: "opening_effectiveness", name: "开篇效果" },
        { key: "climax_pacing", name: "高潮节奏" },
        { key: "outline_analysis", name: "大纲分析" },
        { key: "popular_tropes", name: "热梗统计" }
    ];

    // 直接修复维度选择对话框
    function directFixDimensionSelection() {
        console.log('[维度选择直接修复] 开始直接修复维度选择对话框');

        // 查找模态框
        const modal = document.getElementById('analyzeModal');
        if (!modal) {
            console.error('[维度选择直接修复] 找不到分析模态框');
            return;
        }

        // 查找模态框内容区域
        const modalBody = modal.querySelector('.modal-body');
        if (!modalBody) {
            console.error('[维度选择直接修复] 找不到模态框内容区域');
            return;
        }

        // 查找全选选项
        const selectAllCheckbox = modalBody.querySelector('#select-all-dimensions');
        if (!selectAllCheckbox) {
            console.error('[维度选择直接修复] 找不到全选选项');
            return;
        }

        // 查找分隔线
        const hr = modalBody.querySelector('hr');
        if (!hr) {
            console.error('[维度选择直接修复] 找不到分隔线');
            return;
        }

        // 检查是否已经有维度选项
        const existingCheckboxes = modalBody.querySelectorAll('.dimension-checkbox');
        if (existingCheckboxes.length > 0) {
            console.log(`[维度选择直接修复] 已存在 ${existingCheckboxes.length} 个维度选项，不需要修复`);
            return;
        }

        // 创建维度选项容器
        const dimensionsContainer = document.createElement('div');
        dimensionsContainer.className = 'dimensions-container mt-3';
        dimensionsContainer.style.maxHeight = '300px';
        dimensionsContainer.style.overflowY = 'auto';
        dimensionsContainer.style.padding = '10px';

        // 添加所有维度选项
        DIMENSIONS.forEach((dimension, index) => {
            // 创建新的选择项
            const checkboxContainer = document.createElement('div');
            checkboxContainer.className = 'form-check mb-2';

            const checkbox = document.createElement('input');
            checkbox.className = 'form-check-input dimension-checkbox';
            checkbox.type = 'checkbox';
            checkbox.name = 'dimensions';
            checkbox.value = dimension.key;
            checkbox.id = `dimension-${index}`;
            checkbox.checked = true; // 默认选中

            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.setAttribute('for', `dimension-${index}`);
            label.textContent = dimension.name;

            // 添加到容器中
            checkboxContainer.appendChild(checkbox);
            checkboxContainer.appendChild(label);
            dimensionsContainer.appendChild(checkboxContainer);
        });

        // 将维度选项容器插入到分隔线后面
        hr.parentNode.insertBefore(dimensionsContainer, hr.nextSibling);

        console.log(`[维度选择直接修复] 成功添加了 ${DIMENSIONS.length} 个维度选项`);

        // 设置全选选项的事件处理
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = dimensionsContainer.querySelectorAll('.dimension-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // 监听模态框打开事件
    function setupModalListener() {
        // 使用jQuery监听模态框显示事件
        if (typeof $ !== 'undefined') {
            $(document).on('shown.bs.modal', '#analyzeModal', function() {
                console.log('[维度选择直接修复] 检测到模态框打开 (jQuery)');
                setTimeout(directFixDimensionSelection, 100);
            });
            console.log('[维度选择直接修复] 已设置jQuery模态框监听器');
        }

        // 使用原生JavaScript监听模态框显示
        document.addEventListener('click', function(event) {
            if (event.target.matches('[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]') ||
                event.target.closest('[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]')) {
                console.log('[维度选择直接修复] 检测到分析按钮点击');
                setTimeout(function() {
                    const modal = document.getElementById('analyzeModal');
                    if (modal && modal.classList.contains('show')) {
                        console.log('[维度选择直接修复] 检测到模态框打开 (原生JS)');
                        setTimeout(directFixDimensionSelection, 100);
                    }
                }, 300);
            }
        });
        console.log('[维度选择直接修复] 已设置原生JS模态框监听器');

        // 使用MutationObserver监听模态框显示
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' &&
                    mutation.attributeName === 'class' &&
                    mutation.target.id === 'analyzeModal' &&
                    mutation.target.classList.contains('show')) {
                    console.log('[维度选择直接修复] 检测到模态框打开 (MutationObserver)');
                    setTimeout(directFixDimensionSelection, 100);
                }
            });
        });

        const analyzeModal = document.getElementById('analyzeModal');
        if (analyzeModal) {
            observer.observe(analyzeModal, { attributes: true });
            console.log('[维度选择直接修复] 已设置MutationObserver模态框监听器');
        }
    }

    // 初始化
    function init() {
        console.log('[维度选择直接修复] 初始化中...');

        // 检查当前是否有打开的模态框
        const openModal = document.getElementById('analyzeModal');
        if (openModal && openModal.classList.contains('show')) {
            console.log('[维度选择直接修复] 检测到已打开的分析模态框');
            directFixDimensionSelection();
        }

        // 设置模态框监听器
        setupModalListener();

        // 导出全局函数
        window.directFixDimensionSelection = directFixDimensionSelection;

        console.log('[维度选择直接修复] 初始化完成');
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    console.log('[维度选择直接修复] 脚本加载完成');
})();
