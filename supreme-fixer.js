/**
 * 九猫系统 - 最高级修复器 (Supreme Fixer)
 * 版本: 1.0.0
 * 
 * 此脚本负责加载和协调所有其他修复脚本，提供统一的错误处理接口。
 */

(function() {
    // 全局命名空间
    window.__nineCatsFixes = window.__nineCatsFixes || {
        loaded: {},
        errors: {},
        startTime: Date.now()
    };
    
    // 配置
    const config = {
        // 修复脚本基础路径
        basePath: '/path/to/fixes/',
        
        // 脚本加载顺序和配置
        scripts: [
            { 
                name: 'emergency-resource-loader',
                path: 'emergency-resource-loader.js',
                priority: 10 // 最高优先级
            },
            { 
                name: 'create-element-fix',
                path: 'create-element-fix.js',
                priority: 9
            },
            { 
                name: 'result-not-defined-fix',
                path: 'result-not-defined-fix.js',
                priority: 8
            },
            { 
                name: 'dom-operation-fix',
                path: 'dom-operation-fix.js',
                priority: 7
            },
            { 
                name: 'jquery-loader',
                path: 'jquery-loader.js',
                priority: 6
            },
            { 
                name: 'chart-loader',
                path: 'chart-loader.js',
                priority: 5
            },
            { 
                name: 'main-fix',
                path: 'main-fix.js',
                priority: 1 // 最低优先级
            }
        ],
        
        // 调试设置
        debug: true,
        
        // 应急处理
        emergency: {
            enabled: true,
            maxRetries: 3,
            retryDelay: 1000 // 毫秒
        }
    };
    
    // 日志函数
    function log(message, error = false) {
        const prefix = '[九猫修复]';
        if (config.debug) {
            if (error) {
                console.error(prefix, message);
            } else {
                console.log(prefix, message);
            }
        }
    }
    
    // 创建脚本加载器
    function loadScript(scriptConfig, onComplete) {
        const { name, path, priority } = scriptConfig;
        const fullPath = config.basePath + path;
        
        log(`尝试加载修复脚本: ${name} (优先级: ${priority})`);
        
        // 创建脚本元素
        const script = document.createElement('script');
        script.src = fullPath;
        script.setAttribute('data-fixer', name);
        script.setAttribute('data-priority', priority);
        
        // 成功回调
        script.onload = function() {
            window.__nineCatsFixes.loaded[name] = true;
            log(`修复脚本加载成功: ${name}`);
            onComplete(null, name);
        };
        
        // 错误回调
        script.onerror = function() {
            window.__nineCatsFixes.errors[name] = true;
            log(`修复脚本加载失败: ${name}`, true);
            
            // 尝试应急加载
            if (config.emergency.enabled) {
                tryEmergencyLoad(scriptConfig, onComplete);
            } else {
                onComplete(new Error(`Failed to load script: ${name}`), name);
            }
        };
        
        // 添加到文档
        document.head.appendChild(script);
    }
    
    // 应急加载尝试
    function tryEmergencyLoad(scriptConfig, onComplete, attempt = 1) {
        const { name, path, priority } = scriptConfig;
        
        if (attempt > config.emergency.maxRetries) {
            log(`应急加载失败，已达到最大尝试次数: ${name}`, true);
            onComplete(new Error(`Emergency loading failed after ${attempt} attempts: ${name}`), name);
            return;
        }
        
        log(`应急加载尝试 ${attempt}/${config.emergency.maxRetries}: ${name}`);
        
        // 尝试不同的路径
        const emergencyPaths = [
            './fixes/' + path,
            '../fixes/' + path,
            '/fixes/' + path,
            '/static/fixes/' + path,
            '/static/js/fixes/' + path,
            'https://cdn.jsdelivr.net/gh/user/repo/fixes/' + path
        ];
        
        const currentPath = emergencyPaths[attempt - 1] || emergencyPaths[0];
        
        // 创建脚本元素
        const script = document.createElement('script');
        script.src = currentPath;
        script.setAttribute('data-fixer', name);
        script.setAttribute('data-priority', priority);
        script.setAttribute('data-emergency', 'true');
        script.setAttribute('data-attempt', attempt);
        
        // 成功回调
        script.onload = function() {
            window.__nineCatsFixes.loaded[name] = true;
            log(`应急加载成功: ${name} (路径: ${currentPath})`);
            onComplete(null, name);
        };
        
        // 错误回调
        script.onerror = function() {
            log(`应急加载失败: ${name} (路径: ${currentPath})`, true);
            
            // 延迟后重试下一个路径
            setTimeout(function() {
                tryEmergencyLoad(scriptConfig, onComplete, attempt + 1);
            }, config.emergency.retryDelay);
        };
        
        // 添加到文档
        document.head.appendChild(script);
    }
    
    // 应用内联修复
    function applyInlineFixes() {
        log('应用内联基础修复');
        
        // 1. 最基本的createElement修复
        if (!window.__nineCatsFixes.loaded['create-element-fix']) {
            (function() {
                const originalCreateElement = document.createElement;
                document.createElement = function(tagName) {
                    const element = originalCreateElement.call(document, tagName);
                    if (element && typeof element.textContent === 'undefined') {
                        Object.defineProperty(element, 'textContent', {
                            get: function() { return this.innerText || ''; },
                            set: function(value) { this.innerText = value; },
                            configurable: true
                        });
                    }
                    return element;
                };
                log('已应用内联createElement修复');
            })();
        }
        
        // 2. 最基本的result变量修复
        if (!window.__nineCatsFixes.loaded['result-not-defined-fix']) {
            (function() {
                if (typeof window.result === 'undefined') {
                    window.result = {
                        set: function() {},
                        get: function() { return null; }
                    };
                }
                log('已应用内联result变量修复');
            })();
        }
        
        log('内联基础修复完成');
    }
    
    // 按优先级加载所有脚本
    function loadAllScripts() {
        // 优先级排序
        const sortedScripts = [...config.scripts].sort((a, b) => b.priority - a.priority);
        
        // 应用基础内联修复
        applyInlineFixes();
        
        // 递归加载
        function loadNext(index) {
            if (index >= sortedScripts.length) {
                log('所有修复脚本加载完成');
                finalize();
                return;
            }
            
            const scriptConfig = sortedScripts[index];
            loadScript(scriptConfig, function(error, name) {
                if (error) {
                    log(`脚本加载错误: ${name}`, true);
                }
                loadNext(index + 1);
            });
        }
        
        loadNext(0);
    }
    
    // 完成所有加载后的收尾工作
    function finalize() {
        const loadedCount = Object.keys(window.__nineCatsFixes.loaded).length;
        const errorCount = Object.keys(window.__nineCatsFixes.errors).length;
        const totalTime = (Date.now() - window.__nineCatsFixes.startTime) / 1000;
        
        log(`修复脚本加载状态: 成功=${loadedCount}, 失败=${errorCount}, 总耗时=${totalTime}秒`);
        
        // 触发加载完成事件
        const event = new CustomEvent('nineCatsFixesLoaded', {
            detail: {
                loaded: window.__nineCatsFixes.loaded,
                errors: window.__nineCatsFixes.errors,
                totalTime: totalTime
            }
        });
        window.dispatchEvent(event);
        
        // 设置全局完成标志
        window.__nineCatsFixes.completed = true;
    }
    
    // 开始执行
    function init() {
        log('最高级修复器初始化');
        
        // 标记自身已加载
        window.__nineCatsFixes.loaded['supreme-fixer'] = true;
        
        // 注册全局错误处理器
        window.addEventListener('error', function(event) {
            if (config.debug) {
                log(`捕获到全局错误: ${event.message}`, true);
            }
        });
        
        // 加载所有修复脚本
        loadAllScripts();
    }
    
    // 如果页面已经加载完成，立即执行，否则等待加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})(); 