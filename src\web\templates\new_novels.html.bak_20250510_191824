{% extends "new_base.html" %}

{% block title %}小说列表 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-0"><i class="fas fa-book me-2"></i>小说列表</h1>
        <p class="text-muted">管理您上传的所有小说</p>
    </div>
    <div class="col-md-4 text-md-end">
        <a href="{{ url_for('upload_novel') }}" class="btn btn-primary">
            <i class="fas fa-upload me-2"></i>上传新小说
        </a>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form id="searchForm" class="row g-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索小说标题...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="sortSelect">
                    <option value="newest">最新上传</option>
                    <option value="oldest">最早上传</option>
                    <option value="title_asc">标题 (A-Z)</option>
                    <option value="title_desc">标题 (Z-A)</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="filterSelect">
                    <option value="all">所有小说</option>
                    <option value="analyzed">已分析</option>
                    <option value="not_analyzed">未分析</option>
                </select>
            </div>
        </form>
    </div>
</div>

<!-- 小说列表 -->
<div class="row" id="novelsList">
    {% if novels %}
        {% for novel in novels %}
        <div class="col-md-6 col-lg-4 mb-4 novel-item">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 text-truncate" title="{{ novel.title }}">
                        {{ novel.title }}
                    </h5>
                    <span class="badge {% if novel.is_analyzed %}bg-success{% else %}bg-secondary{% endif %}">
                        {% if novel.is_analyzed %}已分析{% else %}未分析{% endif %}
                    </span>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i> {{ novel.created_at.strftime('%Y-%m-%d') }}
                        </small>
                        <small class="text-muted ms-3">
                            <i class="fas fa-file-alt me-1"></i> {{ novel.word_count or '未知' }} 字
                        </small>
                    </div>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i> 查看详情
                        </a>
                        <button type="button" class="btn btn-outline-danger btn-sm delete-novel" data-novel-id="{{ novel.id }}" data-novel-title="{{ novel.title }}">
                            <i class="fas fa-trash-alt me-1"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i> 暂无小说，请上传小说开始分析
                <div class="mt-3">
                    <a href="{{ url_for('upload_novel') }}" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>上传新小说
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- 分页控件 -->
{% if novels and novels|length > 12 %}
<nav aria-label="小说列表分页">
    <ul class="pagination justify-content-center">
        <li class="page-item disabled">
            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
        </li>
        <li class="page-item active"><a class="page-link" href="#">1</a></li>
        <li class="page-item"><a class="page-link" href="#">2</a></li>
        <li class="page-item"><a class="page-link" href="#">3</a></li>
        <li class="page-item">
            <a class="page-link" href="#">下一页</a>
        </li>
    </ul>
</nav>
{% endif %}

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除小说 "<span id="deleteNovelTitle"></span>" 吗？</p>
                <p class="text-danger">此操作不可撤销，小说及其所有分析结果将被永久删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 删除小说确认
        const deleteButtons = document.querySelectorAll('.delete-novel');
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        const deleteNovelTitle = document.getElementById('deleteNovelTitle');
        const deleteForm = document.getElementById('deleteForm');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const novelId = this.getAttribute('data-novel-id');
                const novelTitle = this.getAttribute('data-novel-title');
                
                deleteNovelTitle.textContent = novelTitle;
                deleteForm.action = `/novel/${novelId}/delete`;
                
                deleteModal.show();
            });
        });
        
        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        const novelItems = document.querySelectorAll('.novel-item');
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            novelItems.forEach(item => {
                const title = item.querySelector('.card-title').textContent.toLowerCase();
                
                if (title.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
        
        // 排序功能
        const sortSelect = document.getElementById('sortSelect');
        const novelsList = document.getElementById('novelsList');
        
        sortSelect.addEventListener('change', function() {
            const sortValue = this.value;
            const novels = Array.from(novelItems);
            
            novels.sort((a, b) => {
                const titleA = a.querySelector('.card-title').textContent;
                const titleB = b.querySelector('.card-title').textContent;
                const dateA = a.querySelector('.text-muted').textContent;
                const dateB = b.querySelector('.text-muted').textContent;
                
                if (sortValue === 'newest') {
                    return dateB.localeCompare(dateA);
                } else if (sortValue === 'oldest') {
                    return dateA.localeCompare(dateB);
                } else if (sortValue === 'title_asc') {
                    return titleA.localeCompare(titleB);
                } else if (sortValue === 'title_desc') {
                    return titleB.localeCompare(titleA);
                }
            });
            
            // 重新排列DOM
            novels.forEach(novel => {
                novelsList.appendChild(novel);
            });
        });
        
        // 筛选功能
        const filterSelect = document.getElementById('filterSelect');
        
        filterSelect.addEventListener('change', function() {
            const filterValue = this.value;
            
            novelItems.forEach(item => {
                const badge = item.querySelector('.badge');
                const isAnalyzed = badge.classList.contains('bg-success');
                
                if (filterValue === 'all' || 
                    (filterValue === 'analyzed' && isAnalyzed) || 
                    (filterValue === 'not_analyzed' && !isAnalyzed)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
</script>
{% endblock %}
