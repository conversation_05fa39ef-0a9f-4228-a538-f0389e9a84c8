"""
短篇小说配置文件
定义知乎体短篇小说的特征、分析维度和写作配置
"""

# 知乎体（盐选故事）短篇小说特征
ZHIHU_STYLE_FEATURES = {
    "narrative_style": {
        "perspective": "第一人称主导，增强代入感",
        "tone": "口语化、纪实性语言，模拟知乎问答真实感",
        "structure": "快穿式单线结构，省略复杂支线"
    },
    "content_features": {
        "themes": ["扶弟魔", "真假千金", "死人文学", "原生家庭", "婚姻矛盾", "职场逆袭", "复仇打脸"],
        "setting": "现代都市背景，紧跟社会热点",
        "characters": "标签化人设（如凤凰男、扶弟魔），角色精简至3人内",
        "conflicts": "极致化矛盾冲突，情绪驱动型"
    },
    "writing_style": {
        "opening": "开篇即高潮，45字内抛出核心冲突",
        "pacing": "快节奏强冲突，每1000字包含关键剧情",
        "emotion": "精准触发情绪（虐文/爽文），以极致人设激发读者愤怒或爽感",
        "language": "口语化现代汉语，强代入感"
    },
    "format_features": {
        "length": "8000-12000字（约1万字标准）",
        "structure": "付费点前置（2500-5000字处），情节密度高",
        "paragraphs": "段落简洁，避免冗长描述",
        "rhythm": "单线推进，围绕核心冲突"
    },
    "commercial_features": {
        "emotion_driven": "情绪经济产物，平衡商业性与艺术性",
        "trend_following": "题材紧跟风口（如高考季写逆袭学霸）",
        "reader_feedback": "数据反馈敏感，稿费与点赞数挂钩",
        "innovation": "在套路中寻求反套路，类型融合创新"
    }
}

# 知乎体（盐选故事）分析维度
SHORT_STORY_ANALYSIS_DIMENSIONS = [
    {
        "key": "opening_hook",
        "name": "开篇钩子",
        "description": "分析开篇45字内的冲突设置和悬念营造",
        "weight": 1.4,  # 知乎体开篇极其重要
        "short_story_focus": "开篇即高潮，核心冲突前置，导语四要素（异常、意外、动作、冲突）"
    },
    {
        "key": "emotion_driving",
        "name": "情绪驱动",
        "description": "分析作品的情绪触发机制和类型化程度",
        "weight": 1.5,  # 情绪驱动是知乎体核心
        "short_story_focus": "精准触发虐文/爽文情绪，极致人设激发愤怒或爽感"
    },
    {
        "key": "conflict_intensity",
        "name": "冲突强度",
        "description": "分析矛盾冲突的极致化程度和情节密度",
        "weight": 1.3,
        "short_story_focus": "快节奏强冲突，每1000字包含关键剧情，避免背景铺陈过长"
    },
    {
        "key": "character_labeling",
        "name": "人物标签化",
        "description": "分析人物的标签化程度和功能性设计",
        "weight": 1.2,
        "short_story_focus": "标签化人设（扶弟魔、凤凰男等），角色精简至3人内"
    },
    {
        "key": "narrative_authenticity",
        "name": "叙述真实感",
        "description": "分析第一人称叙述的真实感和代入感",
        "weight": 1.3,
        "short_story_focus": "口语化纪实性语言，模拟知乎问答真实感，强代入感"
    },
    {
        "key": "plot_density",
        "name": "情节密度",
        "description": "分析情节推进的紧凑性和单线结构",
        "weight": 1.2,
        "short_story_focus": "快穿式单线结构，省略复杂支线，围绕核心冲突推进"
    },
    {
        "key": "theme_extremization",
        "name": "主题极致化",
        "description": "分析主题的极致化处理和社会痛点切入",
        "weight": 1.1,
        "short_story_focus": "从热门社会议题切入，制造极致冲突，抓痛点要狠"
    },
    {
        "key": "commercial_balance",
        "name": "商业平衡",
        "description": "分析商业性与艺术性的平衡程度",
        "weight": 1.0,
        "short_story_focus": "情绪经济产物，平衡商业导向与文学价值"
    },
    {
        "key": "innovation_within_formula",
        "name": "套路内创新",
        "description": "分析在类型框架中的创新和反套路设计",
        "weight": 1.1,
        "short_story_focus": "在套路中寻求反套路，类型融合创新，避免同质化"
    },
    {
        "key": "pacing_control",
        "name": "节奏控制",
        "description": "分析付费点设置和情绪层次递进",
        "weight": 1.2,
        "short_story_focus": "付费点前置（2500-5000字），情绪层次递进，逐步点燃读者情绪"
    },
    {
        "key": "ending_impact",
        "name": "结尾冲击",
        "description": "分析结尾的意外感和情绪冲击力",
        "weight": 1.3,
        "short_story_focus": "打破传统套路，制造意外感，收尾利落，用行为代替总结"
    },
    {
        "key": "social_relevance",
        "name": "社会相关性",
        "description": "分析对当前社会热点和痛点的切入程度",
        "weight": 1.1,
        "short_story_focus": "紧跟社会风口，响应热点议题，具有时代敏感性"
    }
]

# 知乎体（盐选故事）写作配置
SHORT_STORY_WRITING_CONFIG = {
    "target_length": {
        "min_words": 8000,
        "max_words": 12000,
        "target_words": 10000  # 约1万字标准
    },
    "structure": {
        "opening_words": 45,  # 开篇45字内抛出核心冲突
        "plot_points_per_1000": 1,  # 每1000字包含一个关键剧情
        "paywall_position": [2500, 5000],  # 付费点设置范围
        "single_thread": True  # 快穿式单线结构
    },
    "style_requirements": {
        "perspective": "第一人称主导",
        "tone": "口语化纪实性语言",
        "emotion_type": ["虐文", "爽文"],  # 主要情绪类型
        "conflict_level": "极致化",  # 冲突程度
        "pacing": "快节奏强冲突"
    },
    "content_guidelines": {
        "theme_categories": ["扶弟魔", "真假千金", "死人文学", "原生家庭", "婚姻矛盾", "职场逆袭", "复仇打脸"],
        "character_limit": 3,  # 主要人物不超过3个
        "character_style": "标签化人设",
        "setting_scope": "现代都市，紧跟社会热点",
        "conflict_escalation": "4-5层仇恨事件递进"
    },
    "commercial_requirements": {
        "emotion_economy": True,  # 情绪经济导向
        "trend_sensitivity": True,  # 热点敏感性
        "anti_routine": True,  # 反套路创新
        "reader_engagement": "高代入感"
    }
}

# 知乎体（盐选故事）写作提示词模板
ZHIHU_STYLE_WRITING_PROMPTS = {
    "core_requirements": """
请以知乎体（盐选故事）风格创作一篇短篇小说，严格遵循以下核心要求：

【核心特征】
1. 开篇即高潮：必须在开头45字内抛出核心冲突或悬念
2. 情绪驱动：精准触发虐文/爽文情绪，以极致人设激发读者愤怒或爽感
3. 快节奏强冲突：每1000字包含一个关键剧情，避免背景铺陈过长
4. 第一人称叙述：口语化纪实性语言，模拟知乎问答真实感
5. 字数控制：8000-12000字（约1万字标准）

【情绪经济要求】
- 抓痛点要狠：从热门社会议题（原生家庭、婚姻矛盾、扶弟魔等）切入
- 制造极致冲突：将普通设定升级为极致矛盾
- 情绪层次递进：设计4-5层仇恨事件，从隐忍到爆发
""",

    "opening_formula": """
🔥 知乎体导语创作指南（经典四要素+爆款案例）：

【经典四要素】（必须在45字内完成）：
1. 异常：打破常规的异常情况
2. 意外：出人意料的转折
3. 动作：正在进行的关键行为
4. 冲突：核心矛盾的直接展现

【三秒定律】必须在3秒内让读者产生"为什么？"或"后来呢？"的疑问
【五步引爆结构】：异常事件→感官细节→冲突核心→极端反应→行动悬念

【高转化导语案例】：

1. 身份反转+死亡冲击（悬疑复仇类）：
"凌晨三点，法医报告显示我死于溺水。可此刻，我正站在解剖台旁，看着自己的尸体。"

2. 社会痛点+极致羞辱（现实爽文类）：
"婆婆当众把离婚协议甩我脸上：'生不出儿子，就滚出我家！'我擦掉口红印，打开孕检单：'怀了，双胞胎。'"

3. 时空错位+悬疑游戏（脑洞无限流）：
"我被困在《红楼梦》副本里，成了死在大观园的林黛玉。系统提示：'杀死宝钗可复活。'"

【导语质检清单】：
✓ 首句是否建立反常识场景？
✓ 是否含可视觉化的关键道具？
✓ 结尾是否留下行动性悬念？
✓ 是否删除所有解释性形容词？

【核心原则】：用45字拍一部微电影——有画面、有悬念、有刀光剑影的情绪
""",

    "character_labeling": """
💡 人物标签化要求（经典原则+高转化策略）：

【基础配置原则】：
- 角色精简至3人内：主角、对手、关键配角
- 使用标签化人设：扶弟魔、凤凰男、真假千金、死人文学等
- 人物功能明确：每个角色都有明确的情绪触发功能
- 避免复杂人设：删除无关人物和场景，聚焦核心冲突

【关系词替代策略】：
- 用"丈夫""养子""婆婆"等关系词替代人名，减少记忆成本
- 绝不出现具体人名，避免复杂背景铺陈

【高转化标签化人设库】：
- 扶弟魔系列：偷儿子手术费、卖女儿房子、啃老不还钱
- 凤凰男系列：嫌弃妻子出身、妈宝男、双标渣男
- 真假千金系列：身世互换、养父母偏心、血缘复仇
- 死人文学系列：车祸遗言、癌症告白、意外真相
- 职场逆袭系列：被PUA后反击、小三上位被打脸

【情绪触发功能设计】：
- 虐文角色：用冷感动词（"轻笑""攥紧"）强化无力感
- 爽文角色：用挑衅式短句引爆爽感，结尾用句号代替感叹号
- 悬疑角色：用专业细节（法医、保险、医生）增强真实感

【核心要求】：每个角色必须有明确的情绪价值，服务于核心冲突
""",

    "plot_density": """
⚙️ 情节密度控制（基于付费转化数据）：

【多线信息压缩术】：
- 用道具串联伏笔：录音笔→法律复仇；孕检单→身世秘密
- 避免信息过载：每个道具承载2-3条关键信息线
- 物理证据增强纪实感：结扎报告、法医签名、钢钉等具体道具

【节奏控制黄金比例】：
- 0-1000字：导语+冲突建立（必须含反常识场景）
- 1000-2500字：情绪递进第一层（从震惊到愤怒）
- 2500-5000字：付费点高潮（三重打脸设计）
- 5000-8000字：复仇/反击执行（4-5层仇恨事件递进）
- 8000-10000字：终极诛心+意外结局

【删减铁律】：
- 删除所有解释性形容词（用行为代替"痛苦的""绝望的"）
- 合并功能重复角色（一人多职能）
- 砍掉无关场景转换（单一或少数场景）
- 每句话必须推动剧情或情绪，否则删除

【专业加持技巧】：
- 借用冷门职业合理化上帝视角（法医、保险、副本玩家）
- 用精确时间增强紧迫感（"凌晨三点""高考前10分钟"）
- 非常规动词制造记忆点（"撕答题卡""伪造报告""吊销执照"）
""",

    "anti_routine_innovation": """
反套路创新要求：
- 在套路中寻求反套路：如"真假千金"叠加"死人文学"
- 类型融合创新：结合不同热门元素创造新的冲突点
- 结局意外感：打破"破镜重圆"等传统套路，制造意外感
- 避免同质化：在热门框架中加入差异化元素
""",

    "ending_impact": """
结尾冲击力要求：
- 收尾利落：用行为代替总结，如主角转身离开而非解释动机
- 意外感：意料之外情理之中的结局设计
- 情绪冲击：最后的情绪爆发点，制造强烈冲击
- 回味价值：留下思考空间，但不拖泥带水
""",

    "format_structure": """
📱 知乎体格式与分段规范（移动端阅读优化）：

【核心逻辑】："视觉呼吸感+情绪节奏器"，专为移动端设计

【整体结构框架】（万字小说分段示例）：
[导语]（黄金45字，独立成段）
▼▼▼ （空行分隔，暗示正文开始）

[开篇冲突]（300-500字，2-4段）
- 段1：快速交代导语后续
- 段2：倒叙关键背景（1句话回溯）
- 段3：深化矛盾

[发展推进]（每800字一个情绪爆点）
▌ 段落群1（铺垫仇恨）
▌ 段落群2（反转准备）

[付费点]（2500字处设置）
⚠️ 在冲突顶点突然断章，下一段首句即结果

[高潮决战]（多采用"对话搏击式"分段）
[结局]（利落收尾≤3段）

【分段核心法则】：
1. "三行切割法"：单段≤3行（手机屏幕显示上限）
2. 情绪单元独立成段：关键动作、致命台词、感官冲击
3. 空行使用逻辑：
   - 导语→正文过渡：3行（制造阅读仪式感）
   - 时间/场景转换：2行（替代传统过渡语）
   - 关键反转前后：1行（制造心跳暂停效果）

【格式质检清单】：
✓ 手机预览时每屏有≥1个空行分隔块？
✓ 关键台词/动作是否独占段落？
✓ 每300字是否有情绪爆点段？
✓ 是否删除"他想""她感到"等心理提示词？

【终极心法】：知乎体的段落是情绪子弹，每个回车键都是扳机
"""
}

# 知乎体（盐选故事）验证标准
SHORT_STORY_VALIDATION = {
    "word_count": {
        "min_threshold": 7500,  # 最低字数阈值（放宽）
        "max_threshold": 13000,  # 最高字数阈值
        "ideal_range": (8000, 12000),  # 理想范围
        "target_standard": 10000  # 1万字标准
    },
    "opening_check": {
        "hook_within_45_chars": True,  # 45字内必须有钩子
        "conflict_immediate": True,  # 立即进入冲突
        "emotion_trigger": True,  # 情绪触发点
        "three_second_rule": True,  # 3秒内产生疑问
        "visual_props": True,  # 可视觉化关键道具
        "action_suspense": True,  # 行动性悬念
        "no_explanatory_adjectives": True  # 无解释性形容词
    },
    "structure_check": {
        "single_thread": True,  # 单线结构
        "plot_density": {"min_events_per_1000": 1},  # 每1000字至少1个关键事件
        "paywall_position": {"min": 2000, "max": 6000},  # 付费点位置
        "character_limit": {"max": 3}  # 最多3个主要角色
    },
    "emotion_check": {
        "emotion_type": ["虐文", "爽文"],  # 情绪类型
        "intensity_level": "极致化",  # 强度要求
        "escalation_layers": {"min": 3, "max": 6}  # 情绪递进层次
    },
    "zhihu_style_features": {
        "first_person_narrative": True,  # 第一人称叙述
        "colloquial_language": True,  # 口语化语言
        "social_relevance": True,  # 社会相关性
        "anti_routine_elements": True  # 反套路元素
    },
    "commercial_validation": {
        "emotion_economy_compliance": True,  # 情绪经济符合度
        "trend_sensitivity": True,  # 热点敏感性
        "reader_engagement": True  # 读者参与度
    },
    "format_validation": {
        "mobile_optimization": True,  # 移动端优化
        "three_line_rule": True,  # 三行切割法
        "emotion_unit_separation": True,  # 情绪单元独立成段
        "blank_line_logic": True,  # 空行使用逻辑
        "visual_breathing": True,  # 视觉呼吸感
        "paragraph_rhythm": True  # 段落节奏器效果
    }
}

# 短篇小说分析提示词优化
SHORT_STORY_ANALYSIS_PROMPTS = {
    "base_prompt": """
请分析这篇知乎体短篇小说的{dimension}。

分析要求：
1. 重点关注知乎体特征：第一人称叙述、现实题材、情感真挚
2. 考虑短篇小说的篇幅限制和表达特点
3. 分析语言的口语化程度和现代感
4. 评估情感共鸣和现实意义

小说内容：
{content}

请从以下角度进行分析：
""",
    
    "dimension_specific": {
        "opening_hook": "重点分析开篇45字内的冲突设置、三秒定律执行和五步引爆结构",
        "emotion_driving": "重点分析情绪触发机制、虐文/爽文类型和极致人设效果",
        "conflict_intensity": "重点分析矛盾冲突极致化程度和每1000字情节密度",
        "character_labeling": "重点分析人物标签化程度、关系词使用和情绪价值",
        "narrative_authenticity": "重点分析第一人称真实感、口语化程度和代入感",
        "plot_density": "重点分析快穿式单线结构、道具串联和信息压缩",
        "social_relevance": "重点分析社会热点切入、痛点抓取和时代敏感性"
    }
}

# 导语质检工具（基于头部制作人经验）
OPENING_QUALITY_CHECKER = {
    "three_second_test": {
        "question_triggers": ["为什么", "怎么", "后来呢", "什么意思"],
        "emotional_hooks": ["震惊", "愤怒", "好奇", "恐惧", "期待"],
        "time_limit": 3  # 秒
    },
    "visual_elements": {
        "required_props": ["具体道具", "物理证据", "关键物品"],
        "forbidden_abstracts": ["痛苦的", "绝望的", "愤怒的", "开心的"],
        "action_verbs": ["撕", "攥", "甩", "录", "伪造", "吊销"]
    },
    "structure_check": {
        "max_chars": 45,
        "required_elements": ["异常事件", "感官细节", "冲突核心", "极端反应", "行动悬念"],
        "forbidden_names": True,  # 禁止具体人名
        "relationship_words": ["丈夫", "婆婆", "养子", "初恋", "小三"]
    },
    "emotion_anchors": {
        "abuse_story": ["冷感动词", "句号结尾", "决绝语气"],
        "revenge_story": ["挑衅短句", "反击动作", "证据展示"],
        "suspense_story": ["专业细节", "时间精确", "身份反转"]
    }
}

# 爆款导语模板库（基于真实转化数据）
VIRAL_OPENING_TEMPLATES = {
    "identity_reversal_death": {
        "pattern": "时间+死亡报告+视角反转+专业细节+复仇宣言",
        "example": "凌晨三点，法医报告显示我死于溺水。可此刻，我正站在解剖台旁，看着自己的尸体。",
        "conversion_rate": "高",
        "emotion_type": "悬疑复仇"
    },
    "social_pain_extreme_humiliation": {
        "pattern": "公开羞辱+反转打脸+终极诛心+伪造证据",
        "example": "婆婆当众把离婚协议甩我脸上：'生不出儿子，就滚出我家！'我擦掉口红印，打开孕检单。",
        "conversion_rate": "极高",
        "emotion_type": "现实爽文"
    },
    "space_time_dislocation_suspense": {
        "pattern": "穿越设定+系统任务+案中案反转+经典IP",
        "example": "我被困在《红楼梦》副本里，成了死在大观园的林黛玉。系统提示：'杀死宝钗可复活。'",
        "conversion_rate": "中高",
        "emotion_type": "脑洞无限流"
    }
}
