@echo off
echo 九猫小说分析系统 - 永久修改系统环境变量
echo 此脚本需要管理员权限运行

:: 检查是否为管理员
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 请以管理员身份运行此脚本
    echo 右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 获取用户输入的目标目录
echo.
echo 请选择临时文件存储位置:
echo 1. D盘 (D:\九猫临时文件)
echo 2. E盘 (E:\九猫临时文件)
echo 3. 自定义路径
echo.

set /p DRIVE_CHOICE="请输入选择 (1/2/3): "

if "%DRIVE_CHOICE%"=="1" (
    set "TARGET_DIR=D:\九猫临时文件"
) else if "%DRIVE_CHOICE%"=="2" (
    set "TARGET_DIR=E:\九猫临时文件"
) else if "%DRIVE_CHOICE%"=="3" (
    set /p TARGET_DIR="请输入完整路径 (如 F:\Temp): "
) else (
    echo 无效的选择，使用默认值 D:\九猫临时文件
    set "TARGET_DIR=D:\九猫临时文件"
)

:: 确保目标目录存在
if not exist "%TARGET_DIR%" (
    echo 创建临时文件目录: %TARGET_DIR%
    mkdir "%TARGET_DIR%" 2>nul
    
    if %ERRORLEVEL% NEQ 0 (
        echo 创建目录失败，请检查路径或权限!
        pause
        exit /b 1
    )
)

echo.
echo 即将修改以下环境变量:
echo   TEMP: %TARGET_DIR%
echo   TMP: %TARGET_DIR%
echo.
echo 此操作将影响系统所有用户，并且需要重启计算机才能完全生效。
echo.

set /p CONFIRM="确认修改系统环境变量? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo 操作已取消
    pause
    exit /b 0
)

:: 创建子目录
if not exist "%TARGET_DIR%\九猫系统" mkdir "%TARGET_DIR%\九猫系统"
if not exist "%TARGET_DIR%\分析缓存" mkdir "%TARGET_DIR%\分析缓存"
if not exist "%TARGET_DIR%\日志" mkdir "%TARGET_DIR%\日志"

:: 修改系统环境变量（针对所有用户）
echo.
echo 正在修改系统环境变量...
setx TEMP "%TARGET_DIR%" /M
setx TMP "%TARGET_DIR%" /M

:: 修改当前用户环境变量
echo 正在修改当前用户环境变量...
setx TEMP "%TARGET_DIR%"
setx TMP "%TARGET_DIR%"

:: 立即为当前会话设置环境变量
set "TEMP=%TARGET_DIR%"
set "TMP=%TARGET_DIR%"

:: 创建符号链接（可选）
echo.
echo 是否创建符号链接，使常用C盘临时目录指向新位置?
echo 这将使不遵循环境变量的程序也能使用新的临时目录
set /p CREATE_LINKS="创建符号链接? (Y/N): "

if /i "%CREATE_LINKS%"=="Y" (
    echo.
    echo 创建符号链接...
    
    :: 定义要链接的目录
    set "LINK_DIRS=C:\Windows\Temp C:\Users\<USER>\AppData\Local\Temp"
    
    for %%D in (%LINK_DIRS%) do (
        echo 处理: %%D
        
        :: 备份原始目录（如果存在）
        if exist "%%D" (
            echo 备份原始目录...
            set "BACKUP_DIR=%%D_backup_%date:~0,4%%date:~5,2%%date:~8,2%"
            if exist "%%D\*" (
                :: 如果目录不为空，移动内容而不是删除
                echo 移动现有文件到新位置...
                robocopy "%%D" "%TARGET_DIR%" /E /MOVE /R:1 /W:1 /NFL /NDL /NJH /NJS > nul
            )
            attrib -r -s -h "%%D" /S /D
            rd "%%D" /S /Q 2>nul
        )
        
        :: 创建符号链接
        echo 创建符号链接: %%D -> %TARGET_DIR%
        mklink /J "%%D" "%TARGET_DIR%"
    )
)

echo.
echo ========================================
echo 系统环境变量修改完成!
echo ========================================
echo.
echo 新的临时文件路径: %TARGET_DIR%
echo.
echo 请重启计算机以使更改完全生效。
echo 重启后，所有应用程序（包括九猫系统）将使用新的临时目录。
echo.

pause 