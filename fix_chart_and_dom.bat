@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 九猫系统全面修复工具
echo 修复图表加载和DOM操作问题
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 正在以管理员身份运行...
) else (
    echo 警告: 未以管理员身份运行，某些功能可能受限
    echo 建议右键点击此脚本，选择"以管理员身份运行"
    echo.
    timeout /t 3 >nul
)

echo 开始修复流程...
echo.

REM 创建备份目录
if not exist "backup" mkdir backup
echo 已创建备份目录: backup

REM 记录当前时间作为备份后缀
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "BACKUP_SUFFIX=!dt:~0,8!_!dt:~8,6!"

REM 备份原始文件
echo 正在备份原始文件...

REM 检查和创建静态文件目录
if not exist "src\web\static\js\lib" (
    echo 创建lib目录...
    mkdir "src\web\static\js\lib"
)

REM 检查Chart.js是否存在，不存在则复制
if not exist "src\web\static\js\lib\chart.min.js" (
    if exist "src\web\static\js\chart.min.js" (
        echo 移动chart.min.js到lib目录...
        copy "src\web\static\js\chart.min.js" "src\web\static\js\lib\chart.min.js"
    ) else (
        echo 未找到chart.min.js，尝试从emergency-resource-loader.js中提取...
        copy "emergency-resource-loader.js" "src\web\static\js\emergency-resource-loader.js"
    )
)

REM 检查jQuery是否存在，不存在则复制
if not exist "src\web\static\js\lib\jquery.min.js" (
    if exist "src\web\static\js\jquery.min.js" (
        echo 移动jquery.min.js到lib目录...
        copy "src\web\static\js\jquery.min.js" "src\web\static\js\lib\jquery.min.js"
    ) else (
        echo 未找到jquery.min.js，尝试从emergency-resource-loader.js中提取...
    )
)

REM 检查Bootstrap是否存在，不存在则复制
if not exist "src\web\static\js\lib\bootstrap.bundle.min.js" (
    if exist "src\web\static\js\bootstrap.bundle.min.js" (
        echo 移动bootstrap.bundle.min.js到lib目录...
        copy "src\web\static\js\bootstrap.bundle.min.js" "src\web\static\js\lib\bootstrap.bundle.min.js"
    ) else (
        echo 未找到bootstrap.bundle.min.js，尝试从emergency-resource-loader.js中提取...
    )
)

REM 复制修复脚本
echo 正在复制修复脚本...

REM 确保静态目录存在
if not exist "src\web\static\js" mkdir "src\web\static\js"

REM 复制核心修复脚本
copy "src\web\static\js\universal-chart-fix-enhanced.js" "backup\universal-chart-fix-enhanced.js.!BACKUP_SUFFIX!" >nul 2>&1
copy "src\web\static\js\dimension-detail-fix-enhanced.js" "backup\dimension-detail-fix-enhanced.js.!BACKUP_SUFFIX!" >nul 2>&1
copy "src\web\static\js\global-error-handler.js" "backup\global-error-handler.js.!BACKUP_SUFFIX!" >nul 2>&1

echo 部署修复脚本...
copy "universal-chart-fix-enhanced.js" "src\web\static\js\universal-chart-fix-enhanced.js" >nul 2>&1 || echo 警告: 未能复制universal-chart-fix-enhanced.js
copy "dimension-detail-fix-enhanced.js" "src\web\static\js\dimension-detail-fix-enhanced.js" >nul 2>&1 || echo 警告: 未能复制dimension-detail-fix-enhanced.js
copy "global-error-handler.js" "src\web\static\js\global-error-handler.js" >nul 2>&1 || echo 警告: 未能复制global-error-handler.js

REM 检查模板文件
echo 检查模板文件...
copy "src\web\templates\base.html" "backup\base.html.!BACKUP_SUFFIX!" >nul 2>&1

REM 修复模板文件中的脚本引用
echo 修改模板文件中的脚本引用...
powershell -Command "(Get-Content src\web\templates\base.html) -replace '<!-- 脚本加载 -->\s+<script src=\"/static/js/jquery.min.js\"></script>\s+<script src=\"/static/js/bootstrap.bundle.min.js\"></script>', '<!-- 基础JS库 -->\r\n    <script src=\"{{ url_for(''static'', filename=''js/lib/jquery.min.js'') }}\"></script>\r\n    <script src=\"{{ url_for(''static'', filename=''js/lib/bootstrap.bundle.min.js'') }}\"></script>\r\n    <script src=\"{{ url_for(''static'', filename=''js/lib/chart.min.js'') }}\"></script>\r\n    \r\n    <!-- 九猫系统核心修复脚本 -->\r\n    <script src=\"{{ url_for(''static'', filename=''js/emergency-resource-loader.js'') }}\"></script>\r\n    <script src=\"{{ url_for(''static'', filename=''js/universal-chart-fix-enhanced.js'') }}\"></script>\r\n    <script src=\"{{ url_for(''static'', filename=''js/dimension-detail-fix-enhanced.js'') }}\"></script>\r\n    <script src=\"{{ url_for(''static'', filename=''js/global-error-handler.js'') }}\"></script>'" | Set-Content src\web\templates\base.html

echo 清理浏览器缓存...
echo 请在使用前清理浏览器缓存，确保加载最新的脚本文件。

echo.
echo ========================================
echo 修复完成！
echo.
echo 修复内容:
echo 1. 增强维度详情页面显示
echo 2. 修复图表加载问题
echo 3. 优化内存使用
echo 4. 添加全局错误处理
echo.
echo 如有问题请查看日志或联系技术支持
echo ========================================

pause
