"""
系统告警模型
用于存储系统告警信息
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text

from src.models.base import Base

class SystemAlert(Base):
    """系统告警模型，用于存储系统告警信息"""

    __tablename__ = 'system_alerts'

    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, default=datetime.now, index=True)
    level = Column(String(20), nullable=False)  # 告警级别：info, warning, critical
    title = Column(String(100), nullable=False)  # 告警标题
    message = Column(Text, nullable=False)  # 告警详细信息

    def __repr__(self):
        return f"<SystemAlert(id={self.id}, timestamp={self.timestamp}, level={self.level}, title={self.title})>"
