import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入必要的模块
from sqlalchemy import create_engine, Column, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import text

# 导入数据库连接和模型
from src.db.connection import engine, Session
from src.models.analysis_result import AnalysisResult
from src.models.base import Base

def add_reasoning_content_column():
    """添加reasoning_content列到analysis_results表"""
    try:
        # 检查列是否已存在
        conn = engine.connect()
        result = conn.execute(text("PRAGMA table_info(analysis_results)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'reasoning_content' not in columns:
            print("添加reasoning_content列...")
            conn.execute(text("ALTER TABLE analysis_results ADD COLUMN reasoning_content TEXT"))
            print("成功添加reasoning_content列")
        else:
            print("reasoning_content列已存在")
        
        conn.close()
        return True
    except Exception as e:
        print(f"添加列时出错: {str(e)}")
        return False
        
def migrate_reasoning_content_data():
    """从元数据中提取推理过程数据并迁移到新列"""
    session = Session()
    try:
        # 获取所有分析结果
        results = session.query(AnalysisResult).all()
        print(f"找到 {len(results)} 个分析结果")
        
        # 计数器
        updated = 0
        skipped = 0
        
        # 遍历并迁移数据
        for result in results:
            # 如果已有推理过程，跳过
            if result.reasoning_content:
                skipped += 1
                continue
                
            # 从元数据中提取
            if hasattr(result, 'analysis_metadata') and result.analysis_metadata:
                metadata = result.analysis_metadata
                
                # 如果是字符串，尝试解析JSON
                if isinstance(metadata, str):
                    try:
                        import json
                        metadata = json.loads(metadata)
                    except:
                        continue
                
                # 检查元数据中是否有推理过程
                if isinstance(metadata, dict) and 'reasoning_content' in metadata:
                    # 更新推理过程字段
                    result.reasoning_content = metadata['reasoning_content']
                    updated += 1
                    
        # 提交更改
        session.commit()
        print(f"已更新 {updated} 个记录，跳过 {skipped} 个已有记录")
        return True
    except Exception as e:
        print(f"迁移数据时出错: {str(e)}")
        session.rollback()
        return False
    finally:
        session.close()

if __name__ == "__main__":
    print("开始更新数据库结构...")
    
    # 添加新列
    if add_reasoning_content_column():
        # 迁移数据
        if migrate_reasoning_content_data():
            print("数据库更新完成")
        else:
            print("数据迁移失败")
    else:
        print("添加列失败") 