{% extends "base.html" %}

{% block title %}人物关系分析 - 优化版{% endblock %}

{% block head %}
{{ super() }}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1>人物关系分析</h1>
        <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-secondary">
            返回小说页面
        </a>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">人物关系分析结果</h5>
                </div>
                <div class="card-body">
                    <div id="analysis-content" class="analysis-content">
                        {% if analysis_result and analysis_result.content %}
                            {{ analysis_result.content|safe }}
                        {% else %}
                            <p class="text-muted">暂无分析结果</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">分析元数据</h5>
                </div>
                <div class="card-body">
                    <div id="metadata-display">
                        {% if analysis_result and analysis_result.metadata %}
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    处理时间
                                    <span class="badge bg-primary rounded-pill">{{ analysis_result.metadata.processing_time|default(0) }} 秒</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    分块数量
                                    <span class="badge bg-primary rounded-pill">{{ analysis_result.metadata.chunk_count|default(0) }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    API调用次数
                                    <span class="badge bg-primary rounded-pill">{{ analysis_result.metadata.api_calls|default(0) }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    令牌使用量
                                    <span class="badge bg-primary rounded-pill">{{ analysis_result.metadata.tokens_used|default(0) }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    费用
                                    <span class="badge bg-primary rounded-pill">{{ analysis_result.metadata.cost|default(0) }} 元</span>
                                </li>
                            </ul>
                        {% else %}
                            <p class="text-muted">暂无元数据</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">性能指标</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:250px;">
                        <canvas id="radarChart" data-dimension="character_relationships" data-chart-type="radar"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">资源使用</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:250px;">
                        <canvas id="barChart" data-dimension="character_relationships" data-chart-type="bar"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 初始化图表
function initializeCharts() {
    console.log('初始化人物关系图表');
    
    try {
        // 使用我们的安全图表创建函数
        if (window.chartFix) {
            // 获取图表容器
            const radarCanvas = document.getElementById('radarChart');
            const barCanvas = document.getElementById('barChart');
            
            if (radarCanvas && barCanvas) {
                // 从元数据构建图表数据
                const metadata = {{ analysis_result.metadata|tojson if analysis_result and analysis_result.metadata else '{}' }};
                
                // 雷达图数据
                const radarLabels = ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'];
                const radarData = [
                    metadata.processing_time || 0,
                    metadata.chunk_count || 0,
                    metadata.api_calls || 0,
                    metadata.tokens_used || 0,
                    metadata.cost || 0
                ];
                
                console.log('使用从元数据构建的雷达图数据:', radarLabels, radarData);
                
                // 创建雷达图
                window.chartFix.createRadarChart(radarCanvas, {
                    labels: radarLabels,
                    datasets: [{
                        label: '分析性能指标',
                        data: radarData,
                        backgroundColor: 'rgba(74, 107, 223, 0.2)',
                        borderColor: 'rgba(74, 107, 223, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                    }]
                });
                
                // 柱状图数据
                const barLabels = ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'];
                const barData = [
                    metadata.processing_time || 0,
                    metadata.chunk_count || 0,
                    metadata.api_calls || 0,
                    metadata.tokens_used || 0,
                    metadata.cost || 0
                ];
                
                console.log('使用从元数据构建的柱状图数据:', barLabels, barData);
                
                // 创建柱状图
                window.chartFix.createBarChart(barCanvas, {
                    labels: barLabels,
                    datasets: [{
                        label: '分析性能指标',
                        data: barData,
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(255, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(255, 206, 86, 0.2)',
                            'rgba(153, 102, 255, 0.2)'
                        ],
                        borderColor: [
                            'rgba(75, 192, 192, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                });
            } else {
                console.warn('找不到图表容器，跳过图表初始化');
            }
        } else {
            console.warn('图表修复工具未加载，使用备用方法初始化图表');
            
            // 备用初始化方法
            if (typeof Chart !== 'undefined') {
                // 获取图表容器
                const radarChart = document.getElementById('radarChart');
                const barChart = document.getElementById('barChart');
                
                if (radarChart && barChart) {
                    // 从元数据构建图表数据
                    const metadata = {{ analysis_result.metadata|tojson if analysis_result and analysis_result.metadata else '{}' }};
                    
                    // 雷达图
                    new Chart(radarChart.getContext('2d'), {
                        type: 'radar',
                        data: {
                            labels: ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'],
                            datasets: [{
                                label: '分析性能指标',
                                data: [
                                    metadata.processing_time || 0,
                                    metadata.chunk_count || 0,
                                    metadata.api_calls || 0,
                                    metadata.tokens_used || 0,
                                    metadata.cost || 0
                                ],
                                backgroundColor: 'rgba(74, 107, 223, 0.2)',
                                borderColor: 'rgba(74, 107, 223, 1)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgba(74, 107, 223, 1)',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: 'rgba(74, 107, 223, 1)'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                r: {
                                    angleLines: {
                                        display: true
                                    },
                                    suggestedMin: 0,
                                    suggestedMax: 100
                                }
                            }
                        }
                    });
                    
                    // 柱状图
                    new Chart(barChart.getContext('2d'), {
                        type: 'bar',
                        data: {
                            labels: ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'],
                            datasets: [{
                                label: '分析性能指标',
                                data: [
                                    metadata.processing_time || 0,
                                    metadata.chunk_count || 0,
                                    metadata.api_calls || 0,
                                    metadata.tokens_used || 0,
                                    metadata.cost || 0
                                ],
                                backgroundColor: [
                                    'rgba(75, 192, 192, 0.2)',
                                    'rgba(255, 99, 132, 0.2)',
                                    'rgba(54, 162, 235, 0.2)',
                                    'rgba(255, 206, 86, 0.2)',
                                    'rgba(153, 102, 255, 0.2)'
                                ],
                                borderColor: [
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(153, 102, 255, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            }
        }
        
        console.log('图表初始化完成');
    } catch (error) {
        console.error('初始化图表时出错:', error);
    }
}

// 在页面加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
    console.log('人物关系分析页面已加载');
    
    // 检查Chart.js是否已加载
    if (typeof Chart !== 'undefined') {
        console.log('Chart.js已加载');
        initializeCharts();
    } else {
        console.log('Chart.js未加载，等待加载完成');
        
        // 等待Chart.js加载完成
        const checkInterval = setInterval(function() {
            if (typeof Chart !== 'undefined') {
                console.log('检测到Chart.js已加载');
                clearInterval(checkInterval);
                initializeCharts();
            }
        }, 200);
        
        // 设置超时，避免无限等待
        setTimeout(function() {
            clearInterval(checkInterval);
            console.warn('等待Chart.js加载超时');
        }, 5000);
    }
});
</script>
{% endblock %}
