"""
九猫系统缓存预热工具
用于提前分析常用维度，优化用户体验
"""
import os
import sys
import time
import logging
import argparse
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

import config
from src.models.novel import Novel
from src.models.analysis import AnalysisResult
from src.api.analysis import NovelAnalyzer
from src.database import Session, engine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cache_prewarming.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("cache_prewarming")

def get_novels_for_prewarming(days=30, min_views=1):
    """获取需要预热的小说列表
    
    Args:
        days: 获取最近多少天内访问过的小说
        min_views: 最小访问次数
        
    Returns:
        符合条件的小说列表
    """
    logger.info(f"获取最近{days}天内访问次数大于{min_views}的小说")
    
    session = Session()
    try:
        # 计算时间阈值
        time_threshold = datetime.now() - timedelta(days=days)
        
        # 查询符合条件的小说
        # 注意：这里假设Novel模型有last_viewed和view_count字段
        # 如果没有这些字段，需要先修改数据库模型
        novels = session.query(Novel).filter(
            Novel.last_viewed >= time_threshold,
            Novel.view_count >= min_views
        ).all()
        
        logger.info(f"找到{len(novels)}本符合条件的小说")
        return novels
    except Exception as e:
        logger.error(f"获取小说列表时出错: {str(e)}")
        return []
    finally:
        session.close()

def get_popular_dimensions(days=30):
    """获取最常用的分析维度
    
    Args:
        days: 获取最近多少天内的分析数据
        
    Returns:
        按使用频率排序的维度列表
    """
    logger.info(f"获取最近{days}天内最常用的分析维度")
    
    session = Session()
    try:
        # 计算时间阈值
        time_threshold = datetime.now() - timedelta(days=days)
        
        # 查询分析结果，按维度分组并计数
        dimension_counts = session.query(
            AnalysisResult.dimension, 
            engine.func.count(AnalysisResult.id).label('count')
        ).filter(
            AnalysisResult.created_at >= time_threshold
        ).group_by(
            AnalysisResult.dimension
        ).order_by(
            engine.func.count(AnalysisResult.id).desc()
        ).all()
        
        # 提取维度名称
        popular_dimensions = [d[0] for d in dimension_counts]
        
        # 如果没有足够的数据，使用默认维度
        if len(popular_dimensions) < 3:
            logger.info("没有足够的历史数据，使用默认维度")
            popular_dimensions = ["language_style", "structure", "paragraph_length"]
        
        logger.info(f"最常用的维度: {', '.join(popular_dimensions[:5])}")
        return popular_dimensions
    except Exception as e:
        logger.error(f"获取常用维度时出错: {str(e)}")
        return ["language_style", "structure", "paragraph_length"]  # 默认维度
    finally:
        session.close()

def check_cache_status(novel_id, dimensions):
    """检查小说的缓存状态
    
    Args:
        novel_id: 小说ID
        dimensions: 要检查的维度列表
        
    Returns:
        需要更新缓存的维度列表
    """
    logger.info(f"检查小说ID:{novel_id}的缓存状态")
    
    session = Session()
    try:
        dimensions_to_update = []
        
        for dimension in dimensions:
            # 查询缓存结果
            cached_result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id, dimension=dimension
            ).first()
            
            # 检查是否需要更新缓存
            if not cached_result:
                logger.info(f"小说ID:{novel_id}, 维度:{dimension} - 没有缓存")
                dimensions_to_update.append(dimension)
            else:
                # 检查缓存是否过期
                cache_time = cached_result.updated_at
                current_time = datetime.now()
                cache_valid_seconds = config.CACHE_VALID_DAYS * 24 * 60 * 60
                
                if (current_time - cache_time).total_seconds() >= cache_valid_seconds:
                    logger.info(f"小说ID:{novel_id}, 维度:{dimension} - 缓存已过期")
                    dimensions_to_update.append(dimension)
                else:
                    logger.info(f"小说ID:{novel_id}, 维度:{dimension} - 缓存有效")
        
        return dimensions_to_update
    except Exception as e:
        logger.error(f"检查缓存状态时出错: {str(e)}")
        return dimensions
    finally:
        session.close()

def prewarm_novel_cache(novel, dimensions, force=False):
    """预热单本小说的缓存
    
    Args:
        novel: 小说对象
        dimensions: 要预热的维度列表
        force: 是否强制更新缓存
        
    Returns:
        成功预热的维度数量
    """
    logger.info(f"开始预热小说 '{novel.title}' (ID:{novel.id}) 的缓存")
    
    # 如果不强制更新，检查哪些维度需要更新
    if not force:
        dimensions = check_cache_status(novel.id, dimensions)
        
    if not dimensions:
        logger.info(f"小说 '{novel.title}' (ID:{novel.id}) 所有维度的缓存都是有效的，无需预热")
        return 0
    
    logger.info(f"将为小说 '{novel.title}' (ID:{novel.id}) 预热以下维度: {', '.join(dimensions)}")
    
    # 创建分析器
    analyzer = NovelAnalyzer()
    
    # 设置并行分析配置
    config.PARALLEL_ANALYSIS_ENABLED = True
    config.MAX_PARALLEL_ANALYSES = min(len(dimensions), 3)  # 限制并行数，避免资源占用过多
    config.MAX_CHUNK_WORKERS = 2
    config.FORCE_REFRESH_CACHE = force
    
    try:
        # 执行分析
        start_time = time.time()
        results = analyzer.analyze_novel_parallel(novel, dimensions)
        elapsed_time = time.time() - start_time
        
        logger.info(f"小说 '{novel.title}' (ID:{novel.id}) 缓存预热完成，耗时: {elapsed_time:.2f}秒")
        logger.info(f"成功预热的维度数量: {len(results)}")
        
        return len(results)
    except Exception as e:
        logger.error(f"预热小说 '{novel.title}' (ID:{novel.id}) 的缓存时出错: {str(e)}")
        return 0

def run_prewarming(days=30, min_views=1, max_novels=10, dimensions=None, force=False, max_workers=2):
    """运行缓存预热
    
    Args:
        days: 获取最近多少天内访问过的小说
        min_views: 最小访问次数
        max_novels: 最多处理多少本小说
        dimensions: 要预热的维度列表，如果为None则使用最常用维度
        force: 是否强制更新缓存
        max_workers: 最大并行工作线程数
        
    Returns:
        成功预热的小说数量
    """
    logger.info("=== 开始缓存预热 ===")
    logger.info(f"参数: days={days}, min_views={min_views}, max_novels={max_novels}, force={force}")
    
    # 获取需要预热的小说
    novels = get_novels_for_prewarming(days, min_views)
    
    # 限制小说数量
    if max_novels > 0 and len(novels) > max_novels:
        logger.info(f"限制预热小说数量为{max_novels}本")
        novels = novels[:max_novels]
    
    # 如果没有指定维度，获取最常用的维度
    if dimensions is None:
        dimensions = get_popular_dimensions(days)
        # 限制维度数量，避免资源占用过多
        dimensions = dimensions[:5]
    
    logger.info(f"将为{len(novels)}本小说预热以下维度: {', '.join(dimensions)}")
    
    # 使用线程池并行处理多本小说
    successful_novels = 0
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        
        for novel in novels:
            futures.append(executor.submit(
                prewarm_novel_cache, novel, dimensions, force
            ))
        
        # 收集结果
        for future in futures:
            try:
                result = future.result()
                if result > 0:
                    successful_novels += 1
            except Exception as e:
                logger.error(f"预热过程中出错: {str(e)}")
    
    logger.info(f"=== 缓存预热完成，成功处理{successful_novels}/{len(novels)}本小说 ===")
    return successful_novels

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="九猫系统缓存预热工具")
    parser.add_argument("--days", type=int, default=30, help="获取最近多少天内访问过的小说")
    parser.add_argument("--min-views", type=int, default=1, help="最小访问次数")
    parser.add_argument("--max-novels", type=int, default=10, help="最多处理多少本小说")
    parser.add_argument("--dimensions", type=str, help="要预热的维度，用逗号分隔")
    parser.add_argument("--force", action="store_true", help="强制更新缓存")
    parser.add_argument("--max-workers", type=int, default=2, help="最大并行工作线程数")
    
    args = parser.parse_args()
    
    # 处理维度参数
    dimensions = None
    if args.dimensions:
        dimensions = [d.strip() for d in args.dimensions.split(",")]
    
    # 运行预热
    run_prewarming(
        days=args.days,
        min_views=args.min_views,
        max_novels=args.max_novels,
        dimensions=dimensions,
        force=args.force,
        max_workers=args.max_workers
    )
    
    print("\n缓存预热完成！详细日志请查看 cache_prewarming.log")
