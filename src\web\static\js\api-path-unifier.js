/**
 * API路径统一修复脚本
 *
 * 这个脚本用于修复前端API调用路径不一致的问题，确保所有API请求都使用正确的路径前缀。
 * 它会拦截所有fetch和jQuery AJAX请求，并根据规则修改URL。
 */

(function() {
    console.log('[API路径统一] 初始化API路径统一修复脚本');

    // 保存原始的fetch函数
    const originalFetch = window.fetch;

    // 保存原始的jQuery AJAX函数
    let originalJQueryAjax = null;
    if (window.jQuery && window.jQuery.ajax) {
        originalJQueryAjax = window.jQuery.ajax;
    }

    // API路径映射规则
    const API_PATH_RULES = [
        // 分析状态API
        { pattern: /^\/api\/analysis\/(.+)\/status$/, replacement: '/v3.1/api/analysis/$1/status' },
        { pattern: /^\/api\/novel\/(\d+)\/analysis\/(.+)\/status$/, replacement: '/v3.1/api/analysis/$1_$2/status' },
        { pattern: /^\/api\/novel\/(\d+)\/chapter\/(\d+)\/analysis\/(.+)\/status$/, replacement: '/v3.1/api/analysis/$1_$2_$3/status' },

        // 分析结果API
        { pattern: /^\/api\/novel\/(\d+)\/analysis\/(.+)$/, replacement: '/v3.1/api/novel/$1/analysis/$2' },
        { pattern: /^\/api\/novels\/(\d+)\/analysis\/(.+)$/, replacement: '/v3.1/api/novel/$1/analysis/$2' },
        { pattern: /^\/api\/novel\/(\d+)\/chapter\/(\d+)\/analysis\/(.+)$/, replacement: '/v3.1/api/novel/$1/chapter/$2/analysis/$3' },
        { pattern: /^\/api\/novels\/(\d+)\/chapter\/(\d+)\/analysis\/(.+)$/, replacement: '/v3.1/api/novel/$1/chapter/$2/analysis/$3' },

        // 推理过程API
        { pattern: /^\/api\/novel\/(\d+)\/analysis\/(.+)\/reasoning_content$/, replacement: '/v3.1/api/novel/$1/analysis/$2/reasoning_content' },
        { pattern: /^\/api\/novels\/(\d+)\/analysis\/(.+)\/reasoning_content$/, replacement: '/v3.1/api/novel/$1/analysis/$2/reasoning_content' },
        { pattern: /^\/api\/novel\/(\d+)\/chapter\/(\d+)\/analysis\/(.+)\/reasoning_content$/, replacement: '/v3.1/api/novel/$1/chapter/$2/analysis/$3/reasoning_content' },
        { pattern: /^\/api\/novels\/(\d+)\/chapter\/(\d+)\/analysis\/(.+)\/reasoning_content$/, replacement: '/v3.1/api/novel/$1/chapter/$2/analysis/$3/reasoning_content' },

        // 分析维度API
        { pattern: /^\/api\/novel\/(\d+)\/analyze_dimension$/, replacement: '/v3.1/api/novel/$1/analyze_dimension' },
        { pattern: /^\/api\/novel\/(\d+)\/chapter\/(\d+)\/analyze_dimension$/, replacement: '/v3.1/api/novel/$1/chapter/$2/analyze_dimension' },

        // 取消分析API
        { pattern: /^\/api\/analysis\/(.+)\/cancel$/, replacement: '/v3.1/api/analysis/$1/cancel' },

        // 小说状态API
        { pattern: /^\/api\/novel\/(\d+)\/analysis_status$/, replacement: '/v3.1/api/novel/$1/analysis_status' },

        // v3.1 API路径修复（反向修复）
        { pattern: /^\/v3\.1\/api\/analysis\/(.+)\/status$/, replacement: '/api/analysis/$1/status' },
        { pattern: /^\/v3\.1\/api\/novel\/(\d+)\/analysis\/(.+)$/, replacement: '/api/novel/$1/analysis/$2' },
        { pattern: /^\/v3\.1\/api\/novel\/(\d+)\/chapter\/(\d+)\/analysis\/(.+)$/, replacement: '/api/novel/$1/chapter/$2/analysis/$3' }
    ];

    /**
     * 修复API路径
     * @param {string} url - 原始URL
     * @returns {string} - 修复后的URL
     */
    function fixApiUrl(url) {
        // 如果URL不是字符串，直接返回
        if (typeof url !== 'string') {
            return url;
        }

        // 处理direct_db路径
        if (url.includes('/direct_db/analysis')) {
            try {
                // 提取参数
                const urlObj = new URL(url, window.location.origin);
                const novelId = urlObj.searchParams.get('novel_id');
                const dimension = urlObj.searchParams.get('dimension');

                if (novelId && dimension) {
                    const fixedUrl = `/api/novel/${novelId}/analysis/${dimension}?_=${Date.now()}`;
                    console.log(`[API路径统一] 修复direct_db路径: ${url} -> ${fixedUrl}`);
                    return fixedUrl;
                }
            } catch (e) {
                console.error(`[API路径统一] 解析direct_db URL出错: ${e.message}`);
            }
        }

        // 处理direct_db章节路径
        if (url.includes('/direct_db/chapter_analysis')) {
            try {
                // 提取参数
                const urlObj = new URL(url, window.location.origin);
                const novelId = urlObj.searchParams.get('novel_id');
                const chapterId = urlObj.searchParams.get('chapter_id');
                const dimension = urlObj.searchParams.get('dimension');

                if (novelId && chapterId && dimension) {
                    const fixedUrl = `/api/novel/${novelId}/chapter/${chapterId}/analysis/${dimension}?_=${Date.now()}`;
                    console.log(`[API路径统一] 修复direct_db章节路径: ${url} -> ${fixedUrl}`);
                    return fixedUrl;
                }
            } catch (e) {
                console.error(`[API路径统一] 解析direct_db章节URL出错: ${e.message}`);
            }
        }

        // 应用路径映射规则
        for (const rule of API_PATH_RULES) {
            if (rule.pattern.test(url)) {
                const fixedUrl = url.replace(rule.pattern, rule.replacement);
                console.log(`[API路径统一] 修复API路径: ${url} -> ${fixedUrl}`);
                return fixedUrl;
            }
        }

        // 如果没有匹配的规则，返回原始URL
        return url;
    }

    // 重写fetch函数
    window.fetch = function(resource, options) {
        // 修复URL
        if (typeof resource === 'string') {
            resource = fixApiUrl(resource);
        } else if (resource && resource.url) {
            resource.url = fixApiUrl(resource.url);
        }

        // 调用原始fetch函数
        return originalFetch.apply(this, arguments)
            .then(response => {
                // 如果请求成功，返回响应
                if (response.ok) {
                    return response;
                }

                // 处理404错误
                if (response.status === 404 && typeof resource === 'string') {
                    // 如果是v3.1路径，尝试使用常规API路径
                    if (resource.includes('/v3.1/api/')) {
                        const backupUrl = resource.replace('/v3.1/api/', '/api/');
                        console.log(`[API路径统一] API请求404: ${resource}，尝试备用路径: ${backupUrl}`);

                        // 重新发送请求
                        return originalFetch.call(this, backupUrl, options);
                    }

                    // 如果是常规API路径，尝试使用v3.1路径
                    if (resource.startsWith('/api/')) {
                        const backupUrl = `/v3.1${resource}`;
                        console.log(`[API路径统一] API请求404: ${resource}，尝试备用路径: ${backupUrl}`);

                        // 重新发送请求
                        return originalFetch.call(this, backupUrl, options);
                    }
                }

                // 其他错误，返回原始响应
                return response;
            });
    };

    // 如果jQuery可用，重写jQuery AJAX函数
    if (originalJQueryAjax) {
        window.jQuery.ajax = function(url, options) {
            // 处理参数重载
            if (typeof url === 'object') {
                options = url;
                url = undefined;
            }

            // 创建新的选项对象
            const newOptions = options || {};

            // 如果URL是字符串，修复它
            if (typeof url === 'string') {
                url = fixApiUrl(url);
            }

            // 如果选项中包含URL，修复它
            if (newOptions.url) {
                newOptions.url = fixApiUrl(newOptions.url);
            }

            // 调用原始AJAX函数
            if (typeof url === 'string') {
                return originalJQueryAjax.call(this, url, newOptions);
            } else {
                return originalJQueryAjax.call(this, newOptions);
            }
        };
    }

    console.log('[API路径统一] API路径统一修复脚本已加载');
})();
