"""
复制jQuery文件到正确的位置
"""
import os
import shutil
import urllib.request

def ensure_dir(directory):
    """确保目录存在"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def download_file(url, destination):
    """下载文件"""
    try:
        print(f"正在下载: {url} 到 {destination}")
        urllib.request.urlretrieve(url, destination)
        print(f"下载成功: {destination}")
        return True
    except Exception as e:
        print(f"下载失败: {str(e)}")
        return False

def main():
    """主函数"""
    # 确保目录存在
    js_lib_dir = os.path.join("src", "web", "static", "js", "lib")
    ensure_dir(js_lib_dir)
    
    # jQuery文件
    jquery_file = os.path.join(js_lib_dir, "jquery-3.6.0.min.js")
    if not os.path.exists(jquery_file):
        # 尝试从CDN下载
        jquery_url = "https://code.jquery.com/jquery-3.6.0.min.js"
        download_file(jquery_url, jquery_file)
    
    # Bootstrap文件
    bootstrap_file = os.path.join(js_lib_dir, "bootstrap.bundle.min.js")
    if not os.path.exists(bootstrap_file):
        # 尝试从CDN下载
        bootstrap_url = "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        download_file(bootstrap_url, bootstrap_file)
    
    # Chart.js文件
    chart_file = os.path.join(js_lib_dir, "chart.min.js")
    if not os.path.exists(chart_file):
        # 尝试从CDN下载
        chart_url = "https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"
        download_file(chart_url, chart_file)
    
    print("文件复制完成")

if __name__ == "__main__":
    main()
