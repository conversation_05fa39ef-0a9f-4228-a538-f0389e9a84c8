@echo off
echo 九猫系统 - 静态资源修复工具
echo ===========================
echo.

:: 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python，请确保Python已安装并添加到PATH环境变量中。
    goto :end
)

echo [信息] 正在应用静态资源修复...
echo.

:: 运行修复脚本
python 应用静态资源修复.py
if %errorlevel% neq 0 (
    echo.
    echo [错误] 修复脚本运行失败，请查看日志文件了解详情。
    goto :end
)

echo.
echo [成功] 静态资源修复已应用！
echo.
echo 修复内容：
echo  - 创建了静态资源修复脚本
echo  - 将修复脚本复制到静态目录
echo  - 修改了模板文件以加载修复脚本
echo  - 添加了资源修复中间件
echo.
echo 请重启应用程序以使修复生效。

:end
echo.
pause 