@echo off
chcp 65001 >nul

echo ===================================
echo   九猫小说分析系统 - 增强版启动脚本
echo ===================================
echo.
echo 正在启动九猫小说分析系统...
echo.

:: 设置工作目录为脚本所在目录
cd /d %~dp0

:: 检查Python是否安装
echo 检查Python安装...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python。请安装Python并添加到PATH。
    pause
    exit /b 1
)

:: 检查是否存在src/web/app.py
echo 验证src/web/app.py是否存在...
if not exist src\web\app.py (
    echo [错误] 在当前目录中未找到src\web\app.py。请将此脚本移动到项目根目录。
    pause
    exit /b 1
)

:: 检查是否存在main.py
echo 检查main.py是否存在...
set USE_MAIN=0
if exist main.py (
    echo [信息] 找到main.py，将使用它启动系统。
    set USE_MAIN=1
) else (
    echo [信息] 未找到main.py，将使用src.web.app启动系统。
)

:: 下载前端依赖库
echo 检查前端依赖...
if not exist "src\web\static\js\lib\jquery.min.js" (
    echo [信息] 正在下载前端依赖...
    if exist download_libs.py (
        python download_libs.py
    ) else (
        echo [警告] 未找到download_libs.py，无法下载前端依赖。
        echo [警告] 系统可能无法正常运行。
    )
)

:: 设置环境变量，禁用DEBUG模式，使用真实API调用
echo 设置环境变量...
set DEBUG=False
set USE_REAL_API=True

:: 检查端口5001是否被占用
echo 检查端口5001是否被占用...
netstat -ano | findstr :5001 | findstr LISTENING >nul
if %errorlevel% equ 0 (
    echo [警告] 端口5001已被占用，尝试终止占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5001 ^| findstr LISTENING') do (
        echo 尝试终止进程ID: %%a
        taskkill /f /pid %%a >nul 2>&1
        if %errorlevel% equ 0 (
            echo [信息] 成功终止占用端口5001的进程。
        ) else (
            echo [警告] 无法终止占用端口5001的进程。可能需要手动终止。
        )
    )
)

:: 启动九猫系统
echo 在新窗口中启动九猫小说分析系统...

if %USE_MAIN% equ 1 (
    echo [信息] 使用main.py启动系统...
    start "九猫分析系统" cmd /k "cd /d %~dp0 && python main.py"
) else (
    echo [信息] 使用src.web.app启动系统...
    start "九猫分析系统" cmd /k "cd /d %~dp0 && python -m src.web.app"
)

:: 等待系统启动
echo 等待系统启动...
timeout /t 5 /nobreak >nul

:: 检查系统是否成功启动
echo 检查系统是否成功启动...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5001/connection-test' -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '[成功] 系统已成功启动！' } else { Write-Host '[警告] 系统可能未正常启动。状态码:' $response.StatusCode } } catch { Write-Host '[警告] 无法连接到系统。可能未成功启动。' }"

:: 打开浏览器
echo 在浏览器中打开 http://localhost:5001...
start "" http://localhost:5001

echo [成功] 启动脚本执行完毕。
echo 如果浏览器未自动打开，请手动访问: http://localhost:5001
echo.
echo 按任意键退出此窗口...
pause >nul
exit /b 0
