/**
 * 九猫小说分析系统 - API版本修复脚本
 * 
 * 此脚本用于检测和修复API版本混用问题，确保使用正确的API版本
 * 版本: 1.0.0
 */

(function() {
    console.log('[API版本修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        // 检测API版本的URL
        apiVersionCheckUrl: '/api/system/version',
        // 正确的API版本前缀
        correctApiPrefix: '/api/novel/',
        // 错误的API版本前缀
        wrongApiPrefix: '/api/novels/',
        // 需要修复的API路径
        apiPathsToFix: [
            // 章节分析相关
            '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}',
            '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content',
            '/api/novels/{novel_id}/chapter/{chapter_id}/dimensions',
            // 整本书分析相关
            '/api/novels/{novel_id}/analysis/{dimension}',
            '/api/novels/{novel_id}/analysis/{dimension}/reasoning_content',
            '/api/novels/{novel_id}/dimensions',
            // 其他API
            '/api/novels/{novel_id}/analyze',
            '/api/novels/{novel_id}/analyze_dimension',
            '/api/novels/{novel_id}/aggregate_chapters'
        ]
    };

    // 状态
    const STATE = {
        initialized: false,
        originalFetch: window.fetch,
        originalXhrOpen: XMLHttpRequest.prototype.open,
        originalJQueryAjax: $.ajax,
        apiVersion: null,
        fixedApiCalls: 0
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[API版本修复] ${message}`);
        }
    }

    // 初始化
    function init() {
        debugLog('初始化API版本修复脚本');

        // 检测API版本
        checkApiVersion();

        // 重写fetch函数
        window.fetch = function(url, options) {
            // 修复API路径
            const fixedUrl = fixApiPath(url);
            if (fixedUrl !== url) {
                debugLog(`修复fetch API路径: ${url} -> ${fixedUrl}`);
                STATE.fixedApiCalls++;
            }
            return STATE.originalFetch.call(this, fixedUrl, options);
        };

        // 重写XMLHttpRequest.open函数
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            // 修复API路径
            const fixedUrl = fixApiPath(url);
            if (fixedUrl !== url) {
                debugLog(`修复XMLHttpRequest API路径: ${url} -> ${fixedUrl}`);
                STATE.fixedApiCalls++;
            }
            return STATE.originalXhrOpen.call(this, method, fixedUrl, async, user, password);
        };

        // 重写jQuery.ajax函数
        $.ajax = function(options) {
            if (typeof options === 'string') {
                // 如果options是字符串，则它是URL
                const fixedUrl = fixApiPath(options);
                if (fixedUrl !== options) {
                    debugLog(`修复jQuery.ajax API路径(字符串): ${options} -> ${fixedUrl}`);
                    STATE.fixedApiCalls++;
                    options = fixedUrl;
                }
            } else if (options && options.url) {
                // 如果options是对象，则修复url属性
                const fixedUrl = fixApiPath(options.url);
                if (fixedUrl !== options.url) {
                    debugLog(`修复jQuery.ajax API路径(对象): ${options.url} -> ${fixedUrl}`);
                    STATE.fixedApiCalls++;
                    options.url = fixedUrl;
                }
            }
            return STATE.originalJQueryAjax.call($, options);
        };

        STATE.initialized = true;
        debugLog('API版本修复脚本初始化完成');
    }

    // 检测API版本
    function checkApiVersion() {
        debugLog('检测API版本');

        // 发送请求获取API版本
        $.ajax({
            url: CONFIG.apiVersionCheckUrl,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    STATE.apiVersion = response.version;
                    debugLog(`检测到API版本: ${STATE.apiVersion}`);
                } else {
                    debugLog(`检测API版本失败: ${response.error || '未知错误'}`, 'warn');
                }
            },
            error: function() {
                debugLog('检测API版本失败，使用默认修复策略', 'warn');
            }
        });
    }

    // 修复API路径
    function fixApiPath(url) {
        // 如果不是字符串，直接返回
        if (typeof url !== 'string') {
            return url;
        }

        // 检查是否是需要修复的API路径
        if (url.includes(CONFIG.wrongApiPrefix)) {
            // 替换错误的API前缀为正确的前缀
            return url.replace(CONFIG.wrongApiPrefix, CONFIG.correctApiPrefix);
        }

        // 检查是否是v2 API路径
        if (url.includes('/v2/api/')) {
            // 替换v2 API路径为v3 API路径
            return url.replace('/v2/api/', '/api/');
        }

        return url;
    }

    // 初始化脚本
    $(document).ready(function() {
        init();
    });
})();
