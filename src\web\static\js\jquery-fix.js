/**
 * 九猫 - jQuery修复脚本
 * 解决jQuery未定义的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('jQuery修复脚本已加载');

    // 检查jQuery是否已加载
    function checkJQuery() {
        if (typeof jQuery !== 'undefined') {
            console.log('jQuery已加载，版本:', jQuery.fn.jquery);
            
            // 确保$ 变量可用
            if (typeof $ === 'undefined') {
                console.log('$ 变量未定义，设置 $ = jQuery');
                window.$ = jQuery;
            }
            
            // 检查jQuery是否正常工作
            try {
                if ($('body').length > 0) {
                    console.log('jQuery选择器正常工作');
                } else {
                    console.warn('jQuery选择器可能有问题，找不到body元素');
                }
            } catch (e) {
                console.error('jQuery选择器测试失败:', e.message);
                
                // 尝试修复jQuery
                fixJQuery();
            }
            
            return true;
        } else {
            console.error('jQuery未加载，尝试加载jQuery');
            loadJQuery();
            return false;
        }
    }

    // 加载jQuery
    function loadJQuery() {
        console.log('尝试加载jQuery...');
        
        // 创建script元素加载jQuery
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';
        script.onload = function() {
            console.log('jQuery加载成功!');
            
            // 确保$ 变量可用
            window.$ = jQuery;
            
            // 触发jQuery加载完成事件
            const event = new Event('jQueryLoaded');
            document.dispatchEvent(event);
        };
        script.onerror = function() {
            console.error('加载jQuery失败，尝试使用备用CDN');
            
            // 尝试备用CDN
            const backupScript = document.createElement('script');
            backupScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js';
            backupScript.onload = function() {
                console.log('jQuery通过备用CDN加载成功!');
                
                // 确保$ 变量可用
                window.$ = jQuery;
                
                // 触发jQuery加载完成事件
                const event = new Event('jQueryLoaded');
                document.dispatchEvent(event);
            };
            backupScript.onerror = function() {
                console.error('所有jQuery加载尝试均失败');
            };
            document.head.appendChild(backupScript);
        };
        document.head.appendChild(script);
    }

    // 修复jQuery
    function fixJQuery() {
        console.log('尝试修复jQuery...');
        
        // 保存原始的jQuery
        const originalJQuery = jQuery;
        
        // 重新加载jQuery
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';
        script.onload = function() {
            console.log('jQuery重新加载成功!');
            
            // 如果原始的jQuery有问题，使用新加载的jQuery
            if (typeof jQuery !== 'undefined' && typeof jQuery.fn !== 'undefined') {
                console.log('使用新加载的jQuery');
                window.$ = jQuery;
            } else {
                console.log('新加载的jQuery也有问题，尝试修复原始jQuery');
                
                // 尝试修复原始jQuery
                window.jQuery = originalJQuery;
                window.$ = originalJQuery;
                
                // 如果jQuery.fn不存在，尝试创建它
                if (typeof jQuery.fn === 'undefined') {
                    jQuery.fn = {};
                    jQuery.fn.jquery = '3.6.4';
                }
            }
            
            // 触发jQuery修复完成事件
            const event = new Event('jQueryFixed');
            document.dispatchEvent(event);
        };
        document.head.appendChild(script);
    }

    // 在页面加载时检查jQuery
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkJQuery, 100);
        });
    } else {
        setTimeout(checkJQuery, 100);
    }

    // 在页面完全加载后再次检查
    window.addEventListener('load', function() {
        setTimeout(checkJQuery, 500);
    });

    // 定期检查jQuery
    let attempts = 0;
    const maxAttempts = 5;
    const interval = setInterval(function() {
        attempts++;
        
        if (checkJQuery()) {
            clearInterval(interval);
            console.log('jQuery检查完成，jQuery正常工作');
        }
        
        // 达到最大尝试次数后停止
        if (attempts >= maxAttempts) {
            clearInterval(interval);
            console.log('jQuery检查尝试完成');
        }
    }, 1000);

    // 导出全局函数，以便其他脚本可以使用
    window.ensureJQuery = function(callback) {
        if (typeof jQuery !== 'undefined') {
            if (callback && typeof callback === 'function') {
                callback(jQuery);
            }
            return true;
        } else {
            // 添加事件监听器，等待jQuery加载完成
            document.addEventListener('jQueryLoaded', function() {
                if (callback && typeof callback === 'function') {
                    callback(jQuery);
                }
            });
            
            // 加载jQuery
            loadJQuery();
            return false;
        }
    };

    console.log('jQuery修复脚本已加载完成');
})();
