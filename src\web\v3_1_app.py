"""
九猫小说分析写作系统v3.1应用独立入口
"""
import os
import sys
import logging
import time
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_cors import CORS
from sqlalchemy.orm import Session
from sqlalchemy import func

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入配置
import config

# 导入数据库模型
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.api_log import ApiLog
from src.db.connection import Session, engine
from src.models.base import Base

# 导入API客户端
try:
    from src.api.deepseek_client import DeepSeekClient
    from src.api.analysis import NovelAnalyzer
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("无法导入DeepSeekClient或NovelAnalyzer，某些功能可能不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(config.LOG_DIR if hasattr(config, 'LOG_DIR') else 'logs', 'v3_1_app.log'))
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_CONTENT_LENGTH

# 启用CORS
CORS(app)

# 添加自定义模板过滤器
@app.template_filter('tojson_safe')
def tojson_safe(obj):
    import json
    from markupsafe import Markup
    if hasattr(obj, '__class__') and obj.__class__.__name__ == 'MetaData':
        try:
            if hasattr(obj, 'items') and callable(obj.items):
                obj = {k: v for k, v in obj.items()}
            else:
                obj = {}
        except Exception as e:
            logger.error(f"转换MetaData对象为字典时出错: {str(e)}")
            obj = {}
    def json_default(o):
        if hasattr(o, '__dict__'):
            return o.__dict__
        elif hasattr(o, 'items') and callable(o.items):
            return {k: v for k, v in o.items()}
        else:
            return str(o)
    try:
        json_str = json.dumps(obj, default=json_default, ensure_ascii=False)
        return Markup(json_str)
    except Exception as e:
        logger.error(f"JSON序列化对象时出错: {str(e)}")
        return Markup("{}")

@app.template_filter('format_number')
def format_number(value):
    try:
        return "{:,}".format(int(value))
    except (ValueError, TypeError):
        return value

# 确保所有数据库表已创建
Base.metadata.create_all(engine)
logger.info("确认所有数据库表已创建")

# 运行数据库迁移
try:
    from src.db.migrations import run_migrations
    logger.info("开始执行数据库迁移...")
    run_migrations()
    logger.info("数据库迁移执行完成")
except Exception as e:
    logger.error(f"执行数据库迁移时出错: {str(e)}", exc_info=True)

# 只导入v3.1版本的路由
try:
    from src.web.routes.v3_1_routes import v3_1_bp
    from src.web.routes.v3_1_api_new import v3_1_api_bp
    logger.info("成功导入v3.1版本的路由蓝图")
except ImportError as e:
    logger.error(f"无法导入v3.1版本的路由蓝图: {str(e)}")
    raise

# 导入预设内容API路由蓝图（如v3.1需要）
try:
    from src.web.routes.preset_api import preset_api_bp
    logger.info("成功导入预设内容API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入预设内容API路由蓝图: {str(e)}")
    preset_api_bp = None

# 注册蓝图
app.register_blueprint(v3_1_bp, url_prefix='/v3.1')
app.register_blueprint(v3_1_api_bp, url_prefix='/api')
if preset_api_bp:
    app.register_blueprint(preset_api_bp)
    logger.info("已注册预设内容API路由蓝图")

# 添加全局缓存控制
@app.after_request
def add_cache_control(response):
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

# 主页路由
@app.route('/')
def index():
    return redirect(url_for('v3_1.index'))

@app.route('/novels')
def novels():
    return redirect(url_for('v3_1.novels'))

@app.route('/upload')
def upload_novel():
    return redirect(url_for('v3_1.upload_novel'))

@app.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    return redirect(url_for('v3_1.view_novel', novel_id=novel_id))

@app.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    return redirect(url_for('v3_1.analysis', novel_id=novel_id, dimension=dimension))

@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>')
def view_chapter(novel_id, chapter_id):
    return redirect(url_for('v3_1.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def chapter_analysis(novel_id, chapter_id, dimension):
    return redirect(url_for('v3_1.chapter_analysis', novel_id=novel_id, chapter_id=chapter_id, dimension=dimension))

@app.route('/novel/<int:novel_id>/chapters/summary')
def chapters_summary(novel_id):
    return redirect(url_for('v3_1.chapters_summary', novel_id=novel_id))

@app.route('/console')
def console_redirect():
    return redirect(url_for('v3_1.console'))

@app.route('/v3.1/console')
def v3_1_console():
    return redirect(url_for('v3_1.console'))

@app.errorhandler(404)
def not_found(error):
    if request.path.startswith('/v3.1/api'):
        return jsonify({'success': False, 'error': '接口不存在'}), 404
    return render_template('v3.1/error.html', error_code=404, error_message='页面未找到'), 404

@app.errorhandler(500)
def internal_server_error(e):
    logger.error(f"500错误: {str(e)}", exc_info=True)
    return render_template('v3.1/error.html', error_code=500, error_message='服务器内部错误'), 500

from src.web.routes.direct_db_routes import direct_db_bp
app.register_blueprint(direct_db_bp)

# 直接路由处理函数，用于修复 reasoning_content API 端点
@app.route('/api/novel/<int:novel_id>/analysis/<dimension>/reasoning_content')
def direct_reasoning_content(novel_id, dimension):
    """
    直接获取分析结果的推理过程内容。
    """
    try:
        # 记录请求信息
        logger.info(f"收到推理过程请求: novel_id={novel_id}, dimension={dimension}")

        # 获取会话
        session = Session()

        try:
            # 查询分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id, dimension=dimension
            ).first()

            # 如果没有找到分析结果
            if not result:
                logger.warning(f"找不到小说ID={novel_id}，维度={dimension}的分析结果")
                return jsonify({
                    "success": False,
                    "error": f"找不到小说ID={novel_id}，维度={dimension}的分析结果",
                    "message": "请确保已完成该维度的分析"
                }), 404

            # 初始化变量
            reasoning_content = None

            # 尝试方法1: 直接从 reasoning_content 字段获取
            if hasattr(result, 'reasoning_content') and result.reasoning_content:
                reasoning_content = result.reasoning_content
                logger.info(f"从 reasoning_content 字段找到推理过程，长度: {len(reasoning_content)}")

                return jsonify({
                    "success": True,
                    "reasoning_content": reasoning_content,
                    "source": "direct_field",
                    "dimension": dimension,
                    "novel_id": novel_id
                })

            # 尝试方法2: 从元数据中获取
            if hasattr(result, 'analysis_metadata') and result.analysis_metadata:
                metadata = result.analysis_metadata

                # 如果是字符串，尝试解析为JSON
                if isinstance(metadata, str):
                    try:
                        import json
                        metadata = json.loads(metadata)
                        logger.info("成功将元数据从字符串解析为JSON")
                    except Exception as e:
                        logger.error(f"解析元数据时出错: {str(e)}")
                        metadata = {}

                # 记录元数据结构
                if isinstance(metadata, dict):
                    logger.info(f"元数据键: {list(metadata.keys())}")

                # 方法2.1: 直接从metadata的顶层字段查找
                if isinstance(metadata, dict) and 'reasoning_content' in metadata:
                    reasoning_content = metadata['reasoning_content']
                    logger.info(f"从元数据的reasoning_content字段找到推理过程，长度: {len(reasoning_content)}")

                    return jsonify({
                        "success": True,
                        "reasoning_content": reasoning_content,
                        "source": "metadata_top_level",
                        "dimension": dimension,
                        "novel_id": novel_id
                    })

                # 方法2.2: 从response字段查找
                if isinstance(metadata, dict) and 'response' in metadata:
                    response = metadata['response']

                    if isinstance(response, dict) and 'reasoning_content' in response:
                        reasoning_content = response['reasoning_content']
                        logger.info(f"从元数据的response.reasoning_content找到推理过程，长度: {len(reasoning_content)}")

                        return jsonify({
                            "success": True,
                            "reasoning_content": reasoning_content,
                            "source": "metadata_response",
                            "dimension": dimension,
                            "novel_id": novel_id
                        })

            # 如果没有找到推理过程，返回空内容
            logger.warning(f"未找到小说ID={novel_id}，维度={dimension}的推理过程内容")
            return jsonify({
                "success": True,
                "reasoning_content": "未找到推理过程内容。",
                "source": "default",
                "dimension": dimension,
                "novel_id": novel_id
            })
        finally:
            session.close()
    except Exception as e:
        import traceback
        logger.error(f"获取推理过程内容时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# 为Flask日志添加过滤器，忽略/static/和/.well-known/路径的访问日志，让日志更干净。
log = logging.getLogger('werkzeug')
class IgnoreStaticFilter(logging.Filter):
    def filter(self, record):
        return not (
            record.args and (
                str(record.args[0]).startswith('/static/') or
                str(record.args[0]).startswith('/.well-known/')
            )
        )
log.addFilter(IgnoreStaticFilter())

if __name__ == '__main__':
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=False,
        threaded=True,
        use_reloader=False
    )