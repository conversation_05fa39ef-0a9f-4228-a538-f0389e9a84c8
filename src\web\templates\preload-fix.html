<!-- 预加载资源修复 -->
<script>
    // 预加载资源修复脚本
    document.addEventListener('DOMContentLoaded', function() {
        // 检查预加载资源是否被使用
        const preloads = document.querySelectorAll('link[rel="preload"]');
        
        preloads.forEach(function(preload) {
            // 获取预加载资源的URL和类型
            const href = preload.getAttribute('href');
            const as = preload.getAttribute('as');
            
            if (href && as) {
                // 根据资源类型创建相应的元素
                if (as === 'style') {
                    // 检查是否已经有相同href的样式表
                    const existingStyles = document.querySelectorAll('link[rel="stylesheet"]');
                    let exists = false;
                    
                    existingStyles.forEach(function(style) {
                        if (style.getAttribute('href') === href) {
                            exists = true;
                        }
                    });
                    
                    // 如果不存在，创建一个
                    if (!exists) {
                        console.log('预加载修复: 创建样式表 ' + href);
                        const link = document.createElement('link');
                        link.rel = 'stylesheet';
                        link.href = href;
                        document.head.appendChild(link);
                    }
                } else if (as === 'script') {
                    // 检查是否已经有相同src的脚本
                    const existingScripts = document.querySelectorAll('script');
                    let exists = false;
                    
                    existingScripts.forEach(function(script) {
                        if (script.getAttribute('src') === href) {
                            exists = true;
                        }
                    });
                    
                    // 如果不存在，创建一个
                    if (!exists) {
                        console.log('预加载修复: 创建脚本 ' + href);
                        const script = document.createElement('script');
                        script.src = href;
                        document.body.appendChild(script);
                    }
                }
            }
        });
    });
</script>
