/**
 * 标签页切换修复脚本
 * 用于修复Bootstrap标签页无法正常切换的问题
 */

(function() {
    console.log('[标签页修复] 初始化...');

    // 在DOM加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[标签页修复] DOM已加载，开始修复标签页');
        fixTabs();
    });

    // 确保jQuery加载后执行
    if (window.ensureJQuery) {
        window.ensureJQuery(function() {
            console.log('[标签页修复] jQuery已加载，开始修复标签页');
            fixTabs();
        });
    }

    // 修复标签页函数
    function fixTabs() {
        // 如果jQuery不可用，使用原生JS
        if (typeof jQuery === 'undefined' || !jQuery.fn.tab) {
            console.log('[标签页修复] 使用原生JS修复标签页');
            fixTabsWithVanillaJS();
        } else {
            console.log('[标签页修复] 使用jQuery修复标签页');
            fixTabsWithjQuery();
        }
    }

    // 使用原生JS修复标签页
    function fixTabsWithVanillaJS() {
        // 获取所有标签页链接
        var tabLinks = document.querySelectorAll('[data-toggle="tab"], [data-bs-toggle="tab"]');
        
        // 为每个标签页链接添加点击事件
        tabLinks.forEach(function(tabLink) {
            tabLink.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 获取目标面板
                var targetSelector = this.getAttribute('href') || this.getAttribute('data-bs-target');
                if (!targetSelector) return;
                
                var targetPanel = document.querySelector(targetSelector);
                if (!targetPanel) return;
                
                // 获取所有相关的标签页和面板
                var tabContainer = this.closest('.nav-tabs, [role="tablist"]');
                if (!tabContainer) return;
                
                var allTabs = tabContainer.querySelectorAll('[data-toggle="tab"], [data-bs-toggle="tab"]');
                var allPanels = [];
                
                // 收集所有面板
                allTabs.forEach(function(tab) {
                    var panelSelector = tab.getAttribute('href') || tab.getAttribute('data-bs-target');
                    if (panelSelector) {
                        var panel = document.querySelector(panelSelector);
                        if (panel) allPanels.push(panel);
                    }
                });
                
                // 移除所有活动状态
                allTabs.forEach(function(tab) {
                    tab.classList.remove('active');
                    tab.setAttribute('aria-selected', 'false');
                });
                
                allPanels.forEach(function(panel) {
                    panel.classList.remove('show', 'active');
                });
                
                // 设置当前标签页和面板为活动状态
                this.classList.add('active');
                this.setAttribute('aria-selected', 'true');
                
                targetPanel.classList.add('show', 'active');
                
                // 触发自定义事件
                var event = new CustomEvent('shown.bs.tab', {
                    bubbles: true,
                    detail: { relatedTarget: this }
                });
                this.dispatchEvent(event);
                
                console.log('[标签页修复] 已切换到标签页:', targetSelector);
            });
        });
        
        console.log('[标签页修复] 已为', tabLinks.length, '个标签页添加事件监听器');
    }

    // 使用jQuery修复标签页
    function fixTabsWithjQuery() {
        // 如果jQuery的tab方法不存在，添加一个简单的实现
        if (!jQuery.fn.tab) {
            console.log('[标签页修复] 添加jQuery.fn.tab方法');
            
            jQuery.fn.tab = function(action) {
                return this.each(function() {
                    var $this = $(this);
                    
                    if (action === 'show' || !action) {
                        // 获取目标面板
                        var targetSelector = $this.attr('href') || $this.data('bs-target') || $this.data('target');
                        if (!targetSelector) return;
                        
                        var $targetPanel = $(targetSelector);
                        if (!$targetPanel.length) return;
                        
                        // 获取所有相关的标签页和面板
                        var $tabContainer = $this.closest('.nav-tabs, [role="tablist"]');
                        if (!$tabContainer.length) return;
                        
                        var $allTabs = $tabContainer.find('[data-toggle="tab"], [data-bs-toggle="tab"]');
                        var $allPanels = $();
                        
                        // 收集所有面板
                        $allTabs.each(function() {
                            var panelSelector = $(this).attr('href') || $(this).data('bs-target') || $(this).data('target');
                            if (panelSelector) {
                                var $panel = $(panelSelector);
                                if ($panel.length) $allPanels = $allPanels.add($panel);
                            }
                        });
                        
                        // 移除所有活动状态
                        $allTabs.removeClass('active').attr('aria-selected', 'false');
                        $allPanels.removeClass('show active');
                        
                        // 设置当前标签页和面板为活动状态
                        $this.addClass('active').attr('aria-selected', 'true');
                        $targetPanel.addClass('show active');
                        
                        // 触发事件
                        $this.trigger('shown.bs.tab');
                        
                        console.log('[标签页修复] 已切换到标签页:', targetSelector);
                    }
                });
            };
        }
        
        // 为所有标签页链接添加点击事件
        $(document).on('click', '[data-toggle="tab"], [data-bs-toggle="tab"]', function(e) {
            e.preventDefault();
            $(this).tab('show');
        });
        
        console.log('[标签页修复] 已添加全局标签页点击事件处理');
    }

    console.log('[标签页修复] 脚本加载完成');
})();
