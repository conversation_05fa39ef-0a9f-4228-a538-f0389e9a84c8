/**
 * 九猫系统 Bootstrap 自动修复脚本
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于自动检测并修复页面中的 Bootstrap 相关问题
 */

(function() {
    console.log('[Bootstrap 自动修复] 初始化...');
    
    // 全局变量
    let fixAttempts = 0;
    const MAX_FIX_ATTEMPTS = 3;
    let checkInterval = null;
    
    // 检查是否有 Bootstrap 错误
    function checkForBootstrapErrors() {
        // 检查控制台错误
        const consoleErrors = getConsoleErrors();
        
        for (const error of consoleErrors) {
            if (isBootstrapError(error)) {
                console.log('[Bootstrap 自动修复] 检测到 Bootstrap 错误:', error);
                fixBootstrapError();
                return true;
            }
        }
        
        // 检查 DOM 中的错误指示器
        const errorElements = document.querySelectorAll('.bootstrap-error, [data-bootstrap-error]');
        if (errorElements.length > 0) {
            console.log('[Bootstrap 自动修复] 检测到 DOM 中的 Bootstrap 错误指示器');
            fixBootstrapError();
            return true;
        }
        
        return false;
    }
    
    // 获取控制台错误
    function getConsoleErrors() {
        // 如果有全局错误捕获器，使用它
        if (window.capturedErrors && Array.isArray(window.capturedErrors)) {
            return window.capturedErrors;
        }
        
        return [];
    }
    
    // 判断是否是 Bootstrap 错误
    function isBootstrapError(error) {
        if (!error) return false;
        
        // 错误消息字符串
        if (typeof error === 'string') {
            return error.includes('bootstrap') || 
                   error.includes('modal') || 
                   error.includes('tab') || 
                   error.includes('collapse') ||
                   error.includes('标记匹配') ||
                   error.includes('bootstrap.bundle.min.js');
        }
        
        // 错误对象
        if (error.message) {
            return error.message.includes('bootstrap') || 
                   error.message.includes('modal') || 
                   error.message.includes('tab') || 
                   error.message.includes('collapse') ||
                   error.message.includes('标记匹配') ||
                   error.message.includes('bootstrap.bundle.min.js');
        }
        
        // 错误堆栈
        if (error.stack) {
            return error.stack.includes('bootstrap') || 
                   error.stack.includes('bootstrap.bundle.min.js');
        }
        
        return false;
    }
    
    // 修复 Bootstrap 错误
    function fixBootstrapError() {
        fixAttempts++;
        console.log('[Bootstrap 自动修复] 尝试修复 Bootstrap 错误，第', fixAttempts, '次尝试');
        
        if (fixAttempts > MAX_FIX_ATTEMPTS) {
            console.log('[Bootstrap 自动修复] 已达到最大尝试次数，停止修复');
            stopPeriodicCheck();
            return;
        }
        
        // 移除现有的 Bootstrap 脚本
        removeExistingBootstrapScripts();
        
        // 加载修复脚本
        loadBootstrapFixScript();
        
        // 加载新的 Bootstrap 脚本
        loadNewBootstrapScript();
    }
    
    // 移除现有的 Bootstrap 脚本
    function removeExistingBootstrapScripts() {
        const scripts = document.querySelectorAll('script[src*="bootstrap"]');
        
        for (const script of scripts) {
            if (script.src.includes('bootstrap.bundle.min.js') || 
                script.src.includes('bootstrap.min.js')) {
                console.log('[Bootstrap 自动修复] 移除现有的 Bootstrap 脚本:', script.src);
                script.parentNode.removeChild(script);
            }
        }
    }
    
    // 加载 Bootstrap 修复脚本
    function loadBootstrapFixScript() {
        console.log('[Bootstrap 自动修复] 加载 Bootstrap 修复脚本');
        
        // 检查是否已加载
        if (document.querySelector('script[src*="bootstrap-bundle-fix.js"]')) {
            console.log('[Bootstrap 自动修复] Bootstrap 修复脚本已加载');
            return;
        }
        
        // 创建脚本元素
        const script = document.createElement('script');
        script.src = '/static/js/bootstrap-bundle-fix.js';
        
        script.onload = function() {
            console.log('[Bootstrap 自动修复] Bootstrap 修复脚本加载成功');
        };
        
        script.onerror = function() {
            console.error('[Bootstrap 自动修复] Bootstrap 修复脚本加载失败，尝试内联加载');
            
            // 创建内联脚本
            const inlineScript = document.createElement('script');
            inlineScript.textContent = `
                // 内联 Bootstrap 修复
                (function() {
                    console.log('[内联 Bootstrap 修复] 初始化...');
                    
                    // 创建最小的 bootstrap 对象
                    window.bootstrap = window.bootstrap || {};
                    
                    // 创建最小的 Modal 实现
                    bootstrap.Modal = function(element) {
                        this.element = element;
                        
                        this.show = function() {
                            if (this.element) {
                                this.element.style.display = 'block';
                                document.body.classList.add('modal-open');
                            }
                        };
                        
                        this.hide = function() {
                            if (this.element) {
                                this.element.style.display = 'none';
                                document.body.classList.remove('modal-open');
                            }
                        };
                        
                        this.toggle = function() {
                            if (this.element && this.element.style.display === 'block') {
                                this.hide();
                            } else {
                                this.show();
                            }
                        };
                    };
                    
                    // 创建最小的 Tab 实现
                    bootstrap.Tab = function(element) {
                        this.element = element;
                        
                        this.show = function() {
                            if (!this.element) return;
                            
                            // 获取目标面板
                            const target = document.querySelector(this.element.getAttribute('data-bs-target') || this.element.getAttribute('href'));
                            if (!target) return;
                            
                            // 获取所有相关的标签页和面板
                            const parent = this.element.closest('.nav');
                            if (!parent) return;
                            
                            const tabs = parent.querySelectorAll('[data-bs-toggle="tab"], [data-bs-toggle="pill"]');
                            const panes = document.querySelectorAll('.tab-pane');
                            
                            // 隐藏所有面板
                            for (let i = 0; i < panes.length; i++) {
                                panes[i].classList.remove('show', 'active');
                            }
                            
                            // 取消激活所有标签
                            for (let i = 0; i < tabs.length; i++) {
                                tabs[i].classList.remove('active');
                            }
                            
                            // 激活当前标签和面板
                            this.element.classList.add('active');
                            target.classList.add('show', 'active');
                        };
                    };
                    
                    console.log('[内联 Bootstrap 修复] 完成');
                })();
            `;
            
            document.head.appendChild(inlineScript);
        };
        
        document.head.appendChild(script);
    }
    
    // 加载新的 Bootstrap 脚本
    function loadNewBootstrapScript() {
        console.log('[Bootstrap 自动修复] 加载新的 Bootstrap 脚本');
        
        // 使用 ensureBootstrapBundle 函数（如果可用）
        if (typeof window.ensureBootstrapBundle === 'function') {
            window.ensureBootstrapBundle(function() {
                console.log('[Bootstrap 自动修复] Bootstrap 通过 ensureBootstrapBundle 加载成功');
            });
            return;
        }
        
        // 否则，直接加载
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
        script.integrity = 'sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz';
        script.crossOrigin = 'anonymous';
        
        script.onload = function() {
            console.log('[Bootstrap 自动修复] 新的 Bootstrap 脚本加载成功');
        };
        
        script.onerror = function() {
            console.error('[Bootstrap 自动修复] 新的 Bootstrap 脚本加载失败，尝试使用备用 CDN');
            
            const backupScript = document.createElement('script');
            backupScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js';
            
            document.head.appendChild(backupScript);
        };
        
        document.head.appendChild(script);
    }
    
    // 开始定期检查
    function startPeriodicCheck() {
        if (checkInterval) {
            clearInterval(checkInterval);
        }
        
        checkInterval = setInterval(function() {
            checkForBootstrapErrors();
        }, 2000); // 每2秒检查一次
    }
    
    // 停止定期检查
    function stopPeriodicCheck() {
        if (checkInterval) {
            clearInterval(checkInterval);
            checkInterval = null;
        }
    }
    
    // 初始检查
    function initialCheck() {
        // 检查页面是否已加载
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('[Bootstrap 自动修复] 页面已加载，执行初始检查');
            setTimeout(function() {
                if (checkForBootstrapErrors()) {
                    console.log('[Bootstrap 自动修复] 初始检查发现错误，开始定期检查');
                    startPeriodicCheck();
                } else {
                    console.log('[Bootstrap 自动修复] 初始检查未发现错误');
                }
            }, 1000); // 等待1秒，确保页面完全加载
        } else {
            console.log('[Bootstrap 自动修复] 页面尚未加载，等待 DOMContentLoaded 事件');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('[Bootstrap 自动修复] DOMContentLoaded 事件触发，执行初始检查');
                setTimeout(function() {
                    if (checkForBootstrapErrors()) {
                        console.log('[Bootstrap 自动修复] 初始检查发现错误，开始定期检查');
                        startPeriodicCheck();
                    } else {
                        console.log('[Bootstrap 自动修复] 初始检查未发现错误');
                    }
                }, 1000); // 等待1秒，确保页面完全加载
            });
        }
    }
    
    // 导出全局函数
    window.fixBootstrap = function() {
        console.log('[Bootstrap 自动修复] 手动触发 Bootstrap 修复');
        fixAttempts = 0; // 重置尝试次数
        fixBootstrapError();
        return true;
    };
    
    // 执行初始检查
    initialCheck();
    
    console.log('[Bootstrap 自动修复] 初始化完成');
})();
