' 九猫小说分析系统启动脚本 (VBS版本)
' 此脚本会在后台启动九猫系统，并自动打开浏览器

' 设置工作目录为脚本所在目录
Set fso = CreateObject("Scripting.FileSystemObject")
currentPath = fso.GetParentFolderName(WScript.ScriptFullName)
Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = currentPath

' 设置环境变量
WshShell.Environment("PROCESS")("DEBUG") = "False"
WshShell.Environment("PROCESS")("USE_REAL_API") = "True"
WshShell.Environment("PROCESS")("HOST") = "127.0.0.1"

' 显示启动消息
WScript.Echo "正在启动九猫小说分析系统..."
WScript.Echo "系统将在后台运行，此窗口将自动关闭"
WScript.Echo "浏览器将在几秒钟后自动打开"

' 创建临时批处理文件来设置环境变量并运行Python
Set tempFile = fso.CreateTextFile("temp_run.bat", True)
tempFile.WriteLine("@echo off")
tempFile.WriteLine("set DEBUG=False")
tempFile.WriteLine("set USE_REAL_API=True")
tempFile.WriteLine("set HOST=127.0.0.1")
tempFile.WriteLine("python run.py")
tempFile.Close

' 在后台运行批处理文件
WshShell.Run "temp_run.bat", 0, False

' 等待几秒钟确保服务启动（10秒）
WScript.Sleep 10000

' 打开浏览器 - 使用cmd命令强制使用默认浏览器
WshShell.Run "cmd /c start http://localhost:5001", 0, False

' 显示成功消息
WScript.Echo "九猫系统已成功启动！"
WScript.Echo "如果浏览器没有自动打开，请手动访问: http://localhost:5001"
WScript.Sleep 3000

' 删除临时批处理文件
' 注意：由于批处理文件正在运行，可能无法立即删除
On Error Resume Next
If fso.FileExists("temp_run.bat") Then
    fso.DeleteFile "temp_run.bat", True
End If
