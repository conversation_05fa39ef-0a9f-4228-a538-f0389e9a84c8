"""
直接处理同步API请求的脚本
"""
import os
import sys
import json
import logging
import traceback
from datetime import datetime
import sqlite3

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'direct_sync_api_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def sync_novel_analysis_to_chapters(novel_id, dimension=None):
    """
    将整本书的分析结果同步到各个章节。

    Args:
        novel_id: 小说ID
        dimension: 分析维度，如果为None则同步所有维度

    Returns:
        result: 同步结果，包含成功和失败信息
    """
    logger.info(f"开始同步小说 {novel_id} 的分析结果到章节，维度: {dimension}")
    
    try:
        # 连接数据库
        db_path = "novels.db"
        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            return {
                "success": False,
                "error": f"数据库文件不存在: {db_path}",
                "novel_id": novel_id
            }
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 获取小说的所有章节
        cursor.execute("SELECT * FROM chapters WHERE novel_id = ?", (novel_id,))
        chapters = cursor.fetchall()
        if not chapters:
            logger.warning(f"小说 {novel_id} 没有章节，无法同步分析结果")
            return {
                "success": False,
                "error": "小说没有章节，请先创建章节",
                "novel_id": novel_id
            }
        
        # 获取整本书的分析结果
        if dimension:
            cursor.execute("SELECT * FROM analysis_results WHERE novel_id = ? AND dimension = ?", (novel_id, dimension))
        else:
            cursor.execute("SELECT * FROM analysis_results WHERE novel_id = ?", (novel_id,))
        
        novel_results = cursor.fetchall()
        if not novel_results:
            logger.warning(f"小说 {novel_id} 没有分析结果，无法同步")
            return {
                "success": False,
                "error": "小说没有分析结果，请先进行整本书分析",
                "novel_id": novel_id
            }
        
        # 同步结果统计
        sync_stats = {
            "total_dimensions": len(novel_results),
            "total_chapters": len(chapters),
            "created_results": 0,
            "updated_results": 0,
            "skipped_results": 0,
            "dimensions": []
        }
        
        # 为每个维度同步结果
        for novel_result in novel_results:
            dimension = novel_result['dimension']
            sync_stats["dimensions"].append(dimension)
            
            # 为每个章节创建或更新分析结果
            for chapter in chapters:
                # 检查章节是否已有该维度的分析结果
                cursor.execute(
                    "SELECT * FROM chapter_analysis_results WHERE chapter_id = ? AND novel_id = ? AND dimension = ?",
                    (chapter['id'], novel_id, dimension)
                )
                existing_result = cursor.fetchone()
                
                # 准备分析内容
                content = f"""# {dimension.replace('_', ' ').title()} 分析

## 注意
此分析结果是基于整本书的分析自动生成的，可能不完全适用于本章节。
建议对本章节单独进行分析以获取更准确的结果。

## 章节信息
- 章节标题: {chapter['title'] or f'第{chapter["chapter_number"]}章'}
- 章节编号: {chapter['chapter_number']}
- 字数: {chapter['word_count'] if 'word_count' in chapter.keys() else '未知'}

## 整本书分析结果
{novel_result['content']}

"""
                
                # 准备元数据
                try:
                    metadata = json.loads(novel_result['analysis_metadata']) if novel_result['analysis_metadata'] else {}
                except:
                    metadata = {}
                
                metadata["synced_from_novel_analysis"] = True
                metadata["sync_time"] = datetime.now().isoformat()
                metadata["novel_result_id"] = novel_result['id']
                metadata["is_chapter_specific"] = False  # 标记为非章节特定分析
                
                # 创建或更新分析结果
                if existing_result:
                    # 更新现有结果
                    cursor.execute(
                        """
                        UPDATE chapter_analysis_results
                        SET content = ?, analysis_metadata = ?, updated_at = ?
                        WHERE id = ?
                        """,
                        (content, json.dumps(metadata), datetime.now().isoformat(), existing_result['id'])
                    )
                    sync_stats["updated_results"] += 1
                else:
                    # 创建新结果
                    cursor.execute(
                        """
                        INSERT INTO chapter_analysis_results
                        (chapter_id, novel_id, dimension, content, analysis_metadata, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                        """,
                        (
                            chapter['id'], novel_id, dimension, content, json.dumps(metadata),
                            datetime.now().isoformat(), datetime.now().isoformat()
                        )
                    )
                    sync_stats["created_results"] += 1
        
        # 提交事务
        conn.commit()
        
        logger.info(f"成功同步小说 {novel_id} 的分析结果到 {len(chapters)} 个章节，共 {len(novel_results)} 个维度")
        return {
            "success": True,
            "message": f"成功同步分析结果到 {len(chapters)} 个章节",
            "stats": sync_stats
        }
    
    except Exception as e:
        logger.error(f"同步分析结果时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "error": f"同步分析结果时出错: {str(e)}",
            "novel_id": novel_id
        }
    finally:
        if 'conn' in locals():
            conn.close()

def handle_sync_request(novel_id, request_data=None):
    """
    处理同步请求
    
    Args:
        novel_id: 小说ID
        request_data: 请求数据，包含dimension参数
        
    Returns:
        result: 同步结果
    """
    try:
        # 解析请求数据
        dimension = None
        if request_data:
            try:
                data = json.loads(request_data)
                dimension = data.get('dimension')
            except:
                pass
        
        # 调用同步函数
        result = sync_novel_analysis_to_chapters(novel_id, dimension)
        
        # 返回JSON结果
        return json.dumps(result)
    except Exception as e:
        logger.error(f"处理同步请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return json.dumps({
            "success": False,
            "error": f"处理同步请求时出错: {str(e)}",
            "novel_id": novel_id
        })

if __name__ == "__main__":
    # 从命令行参数获取小说ID和请求数据
    if len(sys.argv) < 2:
        print("用法: python direct_sync_api.py <novel_id> [request_data]")
        sys.exit(1)
    
    novel_id = int(sys.argv[1])
    request_data = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 处理请求
    result = handle_sync_request(novel_id, request_data)
    print(result)
