@echo off
chcp 65001 >nul

echo ===================================
echo   九猫小说分析系统启动脚本 (简化版)
echo ===================================
echo.
echo 正在启动九猫小说分析系统...
echo.

:: 设置工作目录为脚本所在目录
cd /d %~dp0

:: 检查Python是否安装
echo 检查Python安装...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python。请安装Python并添加到PATH。
    pause
    exit /b 1
)

:: 检查是否存在main.py
echo 验证main.py存在...
if not exist main.py (
    echo [错误] 在当前目录中未找到main.py。请将此脚本移动到项目根目录。
    pause
    exit /b 1
)

:: 设置环境变量，禁用DEBUG模式，使用真实API调用
echo 设置环境变量...
set DEBUG=False
set USE_REAL_API=True

:: 启动九猫系统
echo 启动九猫小说分析系统...
start "九猫分析系统" cmd /k "python main.py"

:: 等待系统启动
echo 等待系统启动...
timeout /t 5 /nobreak >nul

:: 打开浏览器
echo 在浏览器中打开 http://localhost:5001...
start "" http://localhost:5001

echo [成功] 系统已成功启动。
echo 请在浏览器中使用系统，完成后关闭命令窗口即可停止系统。
echo.
pause
