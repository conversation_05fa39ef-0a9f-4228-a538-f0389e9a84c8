@echo off
echo 九猫系统 - 修复脚本安装工具
echo ============================
echo.

:: 检查目录结构
if not exist "src\web\static\js" (
    echo 创建目录: src\web\static\js
    mkdir "src\web\static\js"
)

if not exist "src\web\templates" (
    echo 创建目录: src\web\templates
    mkdir "src\web\templates"
)

:: 复制修复脚本
echo 正在复制修复脚本...
copy "src\web\static\js\create-element-fix.js" "src\web\static\js\create-element-fix.js.bak" >nul 2>&1
copy "src\web\static\js\emergency-resource-loader.js" "src\web\static\js\emergency-resource-loader.js.bak" >nul 2>&1
copy "src\web\static\js\result-not-defined-fix.js" "src\web\static\js\result-not-defined-fix.js.bak" >nul 2>&1
copy "src\web\static\js\dom-operation-fix.js" "src\web\static\js\dom-operation-fix.js.bak" >nul 2>&1
copy "src\web\static\js\jquery-loader.js" "src\web\static\js\jquery-loader.js.bak" >nul 2>&1
copy "src\web\static\js\chart-loader.js" "src\web\static\js\chart-loader.js.bak" >nul 2>&1
copy "src\web\static\js\supreme-fixer.js" "src\web\static\js\supreme-fixer.js.bak" >nul 2>&1
copy "src\web\static\js\main-fix.js" "src\web\static\js\main-fix.js.bak" >nul 2>&1
copy "src\web\templates\fix-loader.html" "src\web\templates\fix-loader.html.bak" >nul 2>&1

:: 检查是否存在修复脚本文件
if not exist "src\web\static\js\create-element-fix.js" (
    echo 未找到修复脚本文件，请确保这些文件存在于当前目录。
    goto end
)

:: 复制文件到正确位置
copy "src\web\static\js\create-element-fix.js" "src\web\static\js\" >nul 2>&1
copy "src\web\static\js\emergency-resource-loader.js" "src\web\static\js\" >nul 2>&1
copy "src\web\static\js\result-not-defined-fix.js" "src\web\static\js\" >nul 2>&1
copy "src\web\static\js\dom-operation-fix.js" "src\web\static\js\" >nul 2>&1
copy "src\web\static\js\jquery-loader.js" "src\web\static\js\" >nul 2>&1
copy "src\web\static\js\chart-loader.js" "src\web\static\js\" >nul 2>&1
copy "src\web\static\js\supreme-fixer.js" "src\web\static\js\" >nul 2>&1
copy "src\web\static\js\main-fix.js" "src\web\static\js\" >nul 2>&1
copy "src\web\templates\fix-loader.html" "src\web\templates\" >nul 2>&1

echo 修复脚本已复制完成。

:: 检查基础模板文件
echo 正在检查基础模板文件...
set "base_template=src\web\templates\base.html"
if not exist "%base_template%" (
    echo 警告: 未找到基础模板文件 %base_template%
    echo 请手动将修复代码添加到您的基础模板中。
    goto skip_template_mod
)

:: 备份基础模板
copy "%base_template%" "%base_template%.bak" >nul 2>&1
echo 已创建基础模板备份: %base_template%.bak

:: 检查是否已添加修复代码
findstr /C:"fix-loader.html" "%base_template%" >nul
if %errorlevel% == 0 (
    echo 基础模板中已包含修复加载器引用，跳过修改。
) else (
    echo 正在修改基础模板...
    
    :: 创建临时文件
    echo ^<!-- 九猫系统修复加载器 - 必须位于head开始位置 --^> > temp.txt
    echo {% include 'fix-loader.html' %} >> temp.txt
    echo ^<script src="/static/js/supreme-fixer.js"^>^</script^> >> temp.txt
    echo. >> temp.txt
    
    :: 插入到<head>后面
    findstr /B /C:"<head" "%base_template%" > head.txt
    if %errorlevel% == 0 (
        for /f "tokens=*" %%a in (head.txt) do (
            echo %%a > new_base.html
        )
        type temp.txt >> new_base.html
        findstr /V /B /C:"<head" "%base_template%" >> new_base.html
        move /Y new_base.html "%base_template%" >nul
        echo 修复代码已添加到基础模板中。
    ) else (
        echo 警告: 无法在基础模板中找到<head>标签。
        echo 请手动将修复代码添加到您的基础模板中。
    )
    
    :: 清理临时文件
    del temp.txt >nul 2>&1
    del head.txt >nul 2>&1
)

:skip_template_mod
echo.
echo 修复脚本安装完成!
echo 请查看"九猫系统修复指南.md"了解更多信息。

:end
pause 