/**
 * 九猫系统 - 性能监控脚本
 * 用于监控页面加载和API响应时间
 */

(function() {
    // 配置
    const CONFIG = {
        enabled: true,                  // 是否启用性能监控
        logToConsole: true,             // 是否记录到控制台
        logToServer: true,              // 是否记录到服务器
        sampleRate: 0.2,                // 采样率 (0-1)
        slowThreshold: 1000,            // 慢响应阈值(ms)
        verySlowThreshold: 3000,        // 非常慢响应阈值(ms)
        logEndpoint: '/api/log/performance' // 日志记录端点
    };
    
    // 避免在部分访问中运行，按采样率决定
    if (!CONFIG.enabled || Math.random() > CONFIG.sampleRate) {
        console.log('[性能监控] 未启用或未被采样');
        return;
    }
    
    // 性能数据存储
    const performanceData = {
        pageLoad: {},
        apiCalls: [],
        resourceLoads: [],
        interactions: [],
        errors: []
    };
    
    // 初始化页面加载时间记录
    function initPageLoadMonitoring() {
        // 确保Performance API可用
        if (!window.performance || !window.performance.timing) {
            console.warn('[性能监控] Performance API不可用');
            return;
        }
        
        // 在页面完全加载后记录数据
        window.addEventListener('load', function() {
            // 给浏览器一点时间完成最终处理
            setTimeout(function() {
                recordPageLoadMetrics();
            }, 500);
        });
    }
    
    // 记录页面加载指标
    function recordPageLoadMetrics() {
        const timing = performance.timing;
        
        // 基本加载时间
        const loadTime = timing.loadEventEnd - timing.navigationStart;
        const domReadyTime = timing.domComplete - timing.domLoading;
        const networkLatency = timing.responseEnd - timing.requestStart;
        const processingTime = timing.domComplete - timing.responseEnd;
        const backendTime = timing.responseStart - timing.navigationStart;
        
        // 记录数据
        performanceData.pageLoad = {
            url: window.location.pathname,
            timestamp: new Date().toISOString(),
            totalLoadTime: loadTime,
            domReadyTime: domReadyTime,
            networkLatency: networkLatency,
            serverProcessingTime: backendTime,
            frontendProcessingTime: processingTime,
            userAgent: navigator.userAgent,
            screenResolution: `${window.screen.width}x${window.screen.height}`,
            connection: getConnectionInfo()
        };
        
        // 识别性能问题
        if (loadTime > CONFIG.verySlowThreshold) {
            console.warn(`[性能警告] 页面加载非常慢: ${loadTime}ms`);
            performanceData.pageLoad.severity = 'high';
        } else if (loadTime > CONFIG.slowThreshold) {
            console.warn(`[性能警告] 页面加载较慢: ${loadTime}ms`);
            performanceData.pageLoad.severity = 'medium';
        } else {
            performanceData.pageLoad.severity = 'low';
        }
        
        // 记录到控制台
        if (CONFIG.logToConsole) {
            console.log('[性能监控] 页面加载性能:', performanceData.pageLoad);
        }
        
        // 发送到服务器
        if (CONFIG.logToServer) {
            sendPerformanceDataToServer('pageLoad', performanceData.pageLoad);
        }
    }
    
    // 监控API调用
    function monitorApiCalls() {
        // 拦截fetch请求
        const originalFetch = window.fetch;
        
        window.fetch = function(resource, init) {
            const url = (resource instanceof Request) ? resource.url : resource;
            const startTime = performance.now();
            const isApiCall = typeof url === 'string' && url.includes('/api/');
            
            const fetchPromise = originalFetch.apply(this, arguments);
            
            if (isApiCall) {
                return fetchPromise.then(response => {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    const apiData = {
                        url: url,
                        method: (init && init.method) || 'GET',
                        duration: duration,
                        status: response.status,
                        timestamp: new Date().toISOString()
                    };
                    
                    // 识别慢响应
                    if (duration > CONFIG.verySlowThreshold) {
                        console.warn(`[性能警告] API调用非常慢: ${duration}ms - ${url}`);
                        apiData.severity = 'high';
                    } else if (duration > CONFIG.slowThreshold) {
                        console.warn(`[性能警告] API调用较慢: ${duration}ms - ${url}`);
                        apiData.severity = 'medium';
                    } else {
                        apiData.severity = 'low';
                    }
                    
                    // 记录数据
                    performanceData.apiCalls.push(apiData);
                    
                    // 记录到控制台
                    if (CONFIG.logToConsole) {
                        console.log('[性能监控] API调用:', apiData);
                    }
                    
                    // 发送到服务器
                    if (CONFIG.logToServer) {
                        sendPerformanceDataToServer('apiCall', apiData);
                    }
                    
                    return response;
                }).catch(error => {
                    const endTime = performance.now();
                    
                    // 记录错误
                    const errorData = {
                        url: url,
                        method: (init && init.method) || 'GET',
                        duration: endTime - startTime,
                        errorType: error.name,
                        errorMessage: error.message,
                        timestamp: new Date().toISOString(),
                        severity: 'high'
                    };
                    
                    performanceData.errors.push(errorData);
                    
                    // 记录到控制台
                    if (CONFIG.logToConsole) {
                        console.error('[性能监控] API错误:', errorData);
                    }
                    
                    // 发送到服务器
                    if (CONFIG.logToServer) {
                        sendPerformanceDataToServer('error', errorData);
                    }
                    
                    throw error;
                });
            }
            
            return fetchPromise;
        };
        
        // 拦截XMLHttpRequest
        const originalXhrOpen = XMLHttpRequest.prototype.open;
        const originalXhrSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url) {
            this._perfMonUrl = url;
            this._perfMonMethod = method;
            return originalXhrOpen.apply(this, arguments);
        };
        
        XMLHttpRequest.prototype.send = function() {
            const xhr = this;
            const url = xhr._perfMonUrl;
            const method = xhr._perfMonMethod;
            const isApiCall = typeof url === 'string' && url.includes('/api/');
            
            if (isApiCall) {
                const startTime = performance.now();
                
                // 监听加载完成事件
                xhr.addEventListener('load', function() {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    const apiData = {
                        url: url,
                        method: method,
                        duration: duration,
                        status: xhr.status,
                        timestamp: new Date().toISOString()
                    };
                    
                    // 识别慢响应
                    if (duration > CONFIG.verySlowThreshold) {
                        console.warn(`[性能警告] XHR调用非常慢: ${duration}ms - ${url}`);
                        apiData.severity = 'high';
                    } else if (duration > CONFIG.slowThreshold) {
                        console.warn(`[性能警告] XHR调用较慢: ${duration}ms - ${url}`);
                        apiData.severity = 'medium';
                    } else {
                        apiData.severity = 'low';
                    }
                    
                    // 记录数据
                    performanceData.apiCalls.push(apiData);
                    
                    // 记录到控制台
                    if (CONFIG.logToConsole) {
                        console.log('[性能监控] XHR调用:', apiData);
                    }
                    
                    // 发送到服务器
                    if (CONFIG.logToServer) {
                        sendPerformanceDataToServer('apiCall', apiData);
                    }
                });
                
                // 监听错误
                xhr.addEventListener('error', function() {
                    const endTime = performance.now();
                    
                    // 记录错误
                    const errorData = {
                        url: url,
                        method: method,
                        duration: endTime - startTime,
                        errorType: 'XHR Error',
                        errorMessage: `Status: ${xhr.status}`,
                        timestamp: new Date().toISOString(),
                        severity: 'high'
                    };
                    
                    performanceData.errors.push(errorData);
                    
                    // 记录到控制台
                    if (CONFIG.logToConsole) {
                        console.error('[性能监控] XHR错误:', errorData);
                    }
                    
                    // 发送到服务器
                    if (CONFIG.logToServer) {
                        sendPerformanceDataToServer('error', errorData);
                    }
                });
            }
            
            return originalXhrSend.apply(this, arguments);
        };
    }
    
    // 监控资源加载
    function monitorResourceLoading() {
        // 观察资源加载性能
        if (window.PerformanceObserver) {
            try {
                const observer = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    
                    for (const entry of entries) {
                        // 只关注关键资源类型
                        if (['script', 'css', 'fetch', 'xmlhttprequest'].includes(entry.initiatorType)) {
                            const resourceData = {
                                url: entry.name,
                                type: entry.initiatorType,
                                duration: entry.duration,
                                size: entry.transferSize,
                                timestamp: new Date().toISOString()
                            };
                            
                            // 识别慢资源
                            if (entry.duration > CONFIG.verySlowThreshold) {
                                console.warn(`[性能警告] 资源加载非常慢: ${entry.duration}ms - ${entry.name}`);
                                resourceData.severity = 'high';
                            } else if (entry.duration > CONFIG.slowThreshold) {
                                console.warn(`[性能警告] 资源加载较慢: ${entry.duration}ms - ${entry.name}`);
                                resourceData.severity = 'medium';
                            } else {
                                resourceData.severity = 'low';
                            }
                            
                            // 记录数据
                            performanceData.resourceLoads.push(resourceData);
                            
                            // 只记录慢资源到控制台
                            if (CONFIG.logToConsole && entry.duration > CONFIG.slowThreshold) {
                                console.log('[性能监控] 资源加载:', resourceData);
                            }
                            
                            // 只发送慢资源到服务器
                            if (CONFIG.logToServer && entry.duration > CONFIG.slowThreshold) {
                                sendPerformanceDataToServer('resource', resourceData);
                            }
                        }
                    }
                });
                
                observer.observe({ entryTypes: ['resource'] });
            } catch (e) {
                console.warn('[性能监控] PerformanceObserver不可用:', e);
            }
        }
    }
    
    // 获取网络连接信息
    function getConnectionInfo() {
        if (navigator.connection) {
            return {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt,
                saveData: navigator.connection.saveData
            };
        }
        return 'unknown';
    }
    
    // 将性能数据发送到服务器
    function sendPerformanceDataToServer(type, data) {
        try {
            const payload = {
                type: type,
                data: data,
                page: window.location.pathname,
                timestamp: new Date().toISOString()
            };
            
            // 使用Beacon API进行非阻塞发送
            if (navigator.sendBeacon) {
                const success = navigator.sendBeacon(CONFIG.logEndpoint, JSON.stringify(payload));
                if (!success) {
                    fallbackSend(payload);
                }
            } else {
                fallbackSend(payload);
            }
        } catch (e) {
            console.error('[性能监控] 发送性能数据失败:', e);
        }
    }
    
    // 备用发送方法
    function fallbackSend(payload) {
        // 使用fetch发送，忽略响应
        fetch(CONFIG.logEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload),
            keepalive: true
        }).catch(e => {
            console.error('[性能监控] 发送性能数据失败(备用方法):', e);
        });
    }
    
    // 初始化函数
    function init() {
        console.log('[性能监控] 初始化性能监控...');
        
        // 初始化各个监控模块
        initPageLoadMonitoring();
        monitorApiCalls();
        monitorResourceLoading();
        
        console.log('[性能监控] 性能监控已启用');
    }
    
    // 启动监控
    init();
})(); 