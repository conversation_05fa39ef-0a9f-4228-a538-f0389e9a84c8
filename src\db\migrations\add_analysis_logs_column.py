"""
数据库迁移脚本：为analysis_results表添加analysis_logs列
"""
import os
import sys
import logging
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from src.db.connection import engine, Session
from sqlalchemy import text

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migration():
    """执行迁移：为analysis_results表添加analysis_logs列"""
    logger.info("开始迁移：为analysis_results表添加analysis_logs列")
    
    # 检查列是否已存在
    check_sql = text("PRAGMA table_info(analysis_results)")
    
    with engine.connect() as conn:
        result = conn.execute(check_sql)
        columns = [row[1] for row in result]
        
        if 'analysis_logs' in columns:
            logger.info("analysis_logs列已存在，无需迁移")
            return
        
        # 添加列
        try:
            # SQLite不支持ALTER TABLE ADD COLUMN ... JSON，所以我们使用TEXT类型
            alter_sql = text("ALTER TABLE analysis_results ADD COLUMN analysis_logs TEXT")
            conn.execute(alter_sql)
            conn.commit()
            logger.info("成功添加analysis_logs列")
        except Exception as e:
            logger.error(f"添加列时出错: {str(e)}")
            raise

if __name__ == "__main__":
    try:
        run_migration()
        logger.info("迁移完成")
    except Exception as e:
        logger.error(f"迁移失败: {str(e)}")
        sys.exit(1)
