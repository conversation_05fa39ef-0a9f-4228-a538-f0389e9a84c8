{% extends "new_base.html" %}

{% block title %}帮助中心 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-0"><i class="fas fa-question-circle me-2"></i>帮助中心</h1>
        <p class="text-muted">九猫小说分析系统使用指南</p>
    </div>
    <div class="col-md-4 text-md-end">
        <a href="{{ url_for('new.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回首页
        </a>
    </div>
</div>

<div class="row">
    <!-- 左侧导航 -->
    <div class="col-lg-3 mb-4">
        <div class="card sticky-top" style="top: 20px;">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-list me-2"></i>目录</h3>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <a href="#getting-started" class="list-group-item list-group-item-action">开始使用</a>
                    <a href="#upload-novel" class="list-group-item list-group-item-action">上传小说</a>
                    <a href="#analysis-dimensions" class="list-group-item list-group-item-action">分析维度</a>
                    <a href="#chapter-analysis" class="list-group-item list-group-item-action">章节分析</a>
                    <a href="#export-results" class="list-group-item list-group-item-action">导出结果</a>
                    <a href="#system-monitor" class="list-group-item list-group-item-action">系统监控</a>
                    <a href="#faq" class="list-group-item list-group-item-action">常见问题</a>
                    <a href="#contact" class="list-group-item list-group-item-action">联系我们</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 右侧内容 -->
    <div class="col-lg-9">
        <!-- 开始使用 -->
        <div class="card mb-4" id="getting-started">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-rocket me-2"></i>开始使用</h2>
            </div>
            <div class="card-body">
                <h3>欢迎使用九猫小说分析系统</h3>
                <p>九猫小说分析系统是一款专为作家和编辑设计的智能小说分析工具，基于先进的AI技术，提供全方位的文本分析服务。</p>
                
                <h4>系统特点</h4>
                <ul>
                    <li><strong>多维度分析</strong>：提供13个专业维度的深度分析，全方位评估小说质量。</li>
                    <li><strong>AI驱动</strong>：采用先进的AI模型，提供专业水准的文学分析。</li>
                    <li><strong>高效处理</strong>：优化的处理流程，快速分析长篇小说文本。</li>
                    <li><strong>安全可靠</strong>：本地部署，确保您的创作内容安全保密。</li>
                </ul>
                
                <h4>快速入门</h4>
                <ol>
                    <li>上传小说：上传TXT文件或直接粘贴文本内容</li>
                    <li>选择分析维度：从13个专业维度中选择需要分析的方面</li>
                    <li>查看分析结果：获取详细的分析报告和改进建议</li>
                </ol>
            </div>
        </div>
        
        <!-- 上传小说 -->
        <div class="card mb-4" id="upload-novel">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-upload me-2"></i>上传小说</h2>
            </div>
            <div class="card-body">
                <h3>如何上传小说</h3>
                <p>九猫小说分析系统支持两种上传方式：文件上传和文本粘贴。</p>
                
                <h4>文件上传</h4>
                <ol>
                    <li>点击导航栏中的"上传分析"按钮</li>
                    <li>填写小说标题和作者（可选）</li>
                    <li>选择"上传文件"选项</li>
                    <li>点击"选择文件"按钮，选择要上传的TXT文件</li>
                    <li>选择是否自动开始分析</li>
                    <li>如果选择自动分析，请选择需要分析的维度</li>
                    <li>点击"上传并分析"按钮</li>
                </ol>
                
                <h4>文本粘贴</h4>
                <ol>
                    <li>点击导航栏中的"上传分析"按钮</li>
                    <li>填写小说标题和作者（可选）</li>
                    <li>选择"粘贴文本"选项</li>
                    <li>在文本框中粘贴小说内容</li>
                    <li>选择是否自动开始分析</li>
                    <li>如果选择自动分析，请选择需要分析的维度</li>
                    <li>点击"上传并分析"按钮</li>
                </ol>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>支持的文件格式：TXT, DOC, DOCX, PDF。最大文件大小：10MB。
                </div>
            </div>
        </div>
        
        <!-- 分析维度 -->
        <div class="card mb-4" id="analysis-dimensions">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-cubes me-2"></i>分析维度</h2>
            </div>
            <div class="card-body">
                <h3>13个专业分析维度</h3>
                <p>九猫小说分析系统提供13个专业的分析维度，全方位评估小说质量。</p>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h4><i class="fas fa-language me-2"></i>语言风格</h4>
                                <p>分析作者的用词、修辞手法和表达特点，评估语言的独特性和风格一致性。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h4><i class="fas fa-wave-square me-2"></i>节奏与节奏</h4>
                                <p>评估故事节奏的快慢变化和情感起伏，分析叙事流畅度和读者体验。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('new.dimensions') }}" class="btn btn-primary">
                        <i class="fas fa-cubes me-2"></i>查看全部13个分析维度
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 章节分析 -->
        <div class="card mb-4" id="chapter-analysis">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-list-ol me-2"></i>章节分析</h2>
            </div>
            <div class="card-body">
                <h3>如何进行章节分析</h3>
                <p>九猫小说分析系统支持对小说的各个章节进行单独分析，获取更详细的章节级评估。</p>
                
                <h4>查看章节列表</h4>
                <ol>
                    <li>在小说详情页面，点击右侧"章节分析"卡片中的"查看章节列表"按钮</li>
                    <li>系统会自动识别小说中的章节，并显示章节列表</li>
                    <li>点击章节标题可以展开查看章节内容</li>
                </ol>
                
                <h4>分析单个章节</h4>
                <ol>
                    <li>在章节列表页面，展开要分析的章节</li>
                    <li>点击"分析此章节"按钮</li>
                    <li>等待分析完成</li>
                    <li>查看分析结果</li>
                </ol>
                
                <h4>分析所有章节</h4>
                <ol>
                    <li>在章节列表页面，点击右上角"操作"下拉菜单</li>
                    <li>选择"分析所有章节"</li>
                    <li>等待分析完成</li>
                    <li>查看分析结果</li>
                </ol>
            </div>
        </div>
        
        <!-- 导出结果 -->
        <div class="card mb-4" id="export-results">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-file-export me-2"></i>导出结果</h2>
            </div>
            <div class="card-body">
                <h3>如何导出分析结果</h3>
                <p>九猫小说分析系统支持将分析结果导出为不同格式，方便保存和分享。</p>
                
                <h4>导出单个维度的分析结果</h4>
                <ol>
                    <li>在分析结果页面，点击右上角"导出"按钮</li>
                    <li>选择导出格式（PDF、Word文档或Markdown）</li>
                    <li>等待导出完成</li>
                    <li>保存导出的文件</li>
                </ol>
                
                <h4>导出所有维度的分析结果</h4>
                <ol>
                    <li>在小说详情页面，点击右侧"导出选项"卡片中的导出链接</li>
                    <li>选择导出格式（PDF、Word文档或Markdown）</li>
                    <li>等待导出完成</li>
                    <li>保存导出的文件</li>
                </ol>
            </div>
        </div>
        
        <!-- 系统监控 -->
        <div class="card mb-4" id="system-monitor">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-server me-2"></i>系统监控</h2>
            </div>
            <div class="card-body">
                <h3>如何使用系统监控</h3>
                <p>九猫小说分析系统提供系统监控功能，帮助您了解系统资源使用情况和性能指标。</p>
                
                <h4>查看系统资源</h4>
                <ol>
                    <li>点击导航栏右侧的"系统监控"按钮</li>
                    <li>查看CPU使用率、内存使用率、磁盘使用率和网络流量</li>
                </ol>
                
                <h4>查看API调用统计</h4>
                <ol>
                    <li>在系统监控页面，查看API调用统计卡片</li>
                    <li>了解今日API调用次数、本小时API调用次数和按维度统计的API调用情况</li>
                </ol>
                
                <h4>查看系统日志</h4>
                <ol>
                    <li>在系统监控页面，查看系统日志卡片</li>
                    <li>使用日志级别过滤器筛选日志</li>
                    <li>点击"加载更多"按钮查看更多日志</li>
                </ol>
            </div>
        </div>
        
        <!-- 常见问题 -->
        <div class="card mb-4" id="faq">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-question-circle me-2"></i>常见问题</h2>
            </div>
            <div class="card-body">
                <h3>常见问题解答</h3>
                
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading1">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse1" aria-expanded="false" aria-controls="faqCollapse1">
                                系统支持哪些文件格式？
                            </button>
                        </h2>
                        <div id="faqCollapse1" class="accordion-collapse collapse" aria-labelledby="faqHeading1" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                九猫小说分析系统支持TXT、DOC、DOCX和PDF格式的文件。为了获得最佳分析效果，建议使用UTF-8编码的TXT文件。
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse2" aria-expanded="false" aria-controls="faqCollapse2">
                                分析过程需要多长时间？
                            </button>
                        </h2>
                        <div id="faqCollapse2" class="accordion-collapse collapse" aria-labelledby="faqHeading2" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                分析时间取决于小说的长度和选择的分析维度。一般来说，一本10万字的小说，分析一个维度需要3-5分钟。您可以在分析过程中关闭分析进度窗口，系统会在后台继续分析。
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse3" aria-expanded="false" aria-controls="faqCollapse3">
                                如何删除已上传的小说？
                            </button>
                        </h2>
                        <div id="faqCollapse3" class="accordion-collapse collapse" aria-labelledby="faqHeading3" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                在小说详情页面，点击右上角的"删除"按钮，然后在确认对话框中点击"确认删除"。请注意，删除小说将同时删除所有相关的分析结果，此操作不可撤销。
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse4" aria-expanded="false" aria-controls="faqCollapse4">
                                系统是否支持多语言？
                            </button>
                        </h2>
                        <div id="faqCollapse4" class="accordion-collapse collapse" aria-labelledby="faqHeading4" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                目前，九猫小说分析系统主要支持中文小说的分析。我们计划在未来版本中添加对其他语言的支持。
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading5">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse5" aria-expanded="false" aria-controls="faqCollapse5">
                                如何提高分析准确性？
                            </button>
                        </h2>
                        <div id="faqCollapse5" class="accordion-collapse collapse" aria-labelledby="faqHeading5" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                为了获得最准确的分析结果，建议上传完整的小说文本，并确保文本格式规范，章节划分清晰。如果小说中包含大量特殊符号或格式问题，可能会影响分析准确性。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 联系我们 -->
        <div class="card" id="contact">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-envelope me-2"></i>联系我们</h2>
            </div>
            <div class="card-body">
                <h3>需要帮助？</h3>
                <p>如果您在使用九猫小说分析系统过程中遇到任何问题，或者有任何建议和反馈，请随时联系我们。</p>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h4><i class="fas fa-envelope me-2"></i>电子邮件</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h4><i class="fas fa-comments me-2"></i>在线客服</h4>
                                <p>工作时间：周一至周五 9:00-18:00</p>
                                <button class="btn btn-primary">
                                    <i class="fas fa-comments me-2"></i>开始聊天
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
