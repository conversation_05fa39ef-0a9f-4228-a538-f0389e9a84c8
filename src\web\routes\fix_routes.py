"""
九猫小说分析写作系统 - 修复路由
用于处理系统修复相关的API请求
"""

import os
import json
import sqlite3
from datetime import datetime
from flask import Blueprint, jsonify, request, render_template, current_app
from ..utils.db_utils import get_db_connection

# 创建蓝图
fix_routes = Blueprint('fix', __name__)

@fix_routes.route('/api/fix_analysis_status', methods=['POST'])
def fix_analysis_status():
    """修复分析状态"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查novels表是否有is_analyzed列
        cursor.execute("PRAGMA table_info(novels)")
        columns = cursor.fetchall()
        has_is_analyzed = any(col[1] == 'is_analyzed' for col in columns)

        # 如果没有is_analyzed列，添加该列
        if not has_is_analyzed:
            cursor.execute("ALTER TABLE novels ADD COLUMN is_analyzed INTEGER DEFAULT 0")
            conn.commit()

        # 更新所有小说的分析状态
        cursor.execute("""
            UPDATE novels SET is_analyzed =
            CASE WHEN EXISTS (
                SELECT 1 FROM analysis_results
                WHERE analysis_results.novel_id = novels.id
            ) THEN 1 ELSE 0 END
        """)

        # 检查chapter_analysis_results表是否有reasoning_content列
        cursor.execute("PRAGMA table_info(chapter_analysis_results)")
        columns = cursor.fetchall()
        has_reasoning_content = any(col[1] == 'reasoning_content' for col in columns)

        # 如果没有reasoning_content列，添加该列
        if not has_reasoning_content:
            cursor.execute("ALTER TABLE chapter_analysis_results ADD COLUMN reasoning_content TEXT")
            conn.commit()

        # 提交更改
        conn.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '分析状态修复成功'
        })
    except Exception as e:
        # 返回错误响应
        return jsonify({
            'success': False,
            'error': str(e)
        })
    finally:
        # 关闭数据库连接
        if conn:
            conn.close()

@fix_routes.route('/api/fix_chapter_analysis', methods=['POST'])
def fix_chapter_analysis():
    """修复章节分析"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查chapters表是否有is_analyzed列
        cursor.execute("PRAGMA table_info(chapters)")
        columns = cursor.fetchall()
        has_is_analyzed = any(col[1] == 'is_analyzed' for col in columns)

        # 如果没有is_analyzed列，添加该列
        if not has_is_analyzed:
            cursor.execute("ALTER TABLE chapters ADD COLUMN is_analyzed INTEGER DEFAULT 0")
            conn.commit()

        # 更新所有章节的分析状态
        cursor.execute("""
            UPDATE chapters SET is_analyzed =
            CASE WHEN EXISTS (
                SELECT 1 FROM chapter_analysis_results
                WHERE chapter_analysis_results.chapter_id = chapters.id
            ) THEN 1 ELSE 0 END
        """)

        # 提交更改
        conn.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '章节分析修复成功'
        })
    except Exception as e:
        # 返回错误响应
        return jsonify({
            'success': False,
            'error': str(e)
        })
    finally:
        # 关闭数据库连接
        if conn:
            conn.close()

@fix_routes.route('/api/fix_api_paths', methods=['POST'])
def fix_api_paths():
    """修复API路径"""
    try:
        # 返回成功响应
        return jsonify({
            'success': True,
            'message': 'API路径修复成功',
            'api_paths': {
                'novel': ['/api/novel/', '/api/novels/'],
                'analysis': ['/api/novel/{novel_id}/analysis/{dimension}', '/api/novels/{novel_id}/analysis/{dimension}'],
                'reasoning': ['/api/novel/{novel_id}/analysis/{dimension}/reasoning_content', '/api/novels/{novel_id}/analysis/{dimension}/reasoning_content'],
                'chapterAnalysis': ['/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}', '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}'],
                'chapterReasoning': ['/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content', '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content']
            }
        })
    except Exception as e:
        # 返回错误响应
        return jsonify({
            'success': False,
            'error': str(e)
        })

@fix_routes.route('/api/fix_chapter_outline_reasoning', methods=['POST'])
def fix_chapter_outline_reasoning():
    """修复章纲分析推理过程"""
    try:
        # 获取请求数据
        data = request.json
        novel_id = data.get('novel_id')
        chapter_id = data.get('chapter_id')

        if not novel_id or not chapter_id:
            return jsonify({
                'success': False,
                'error': '缺少必要参数：novel_id 或 chapter_id'
            })

        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查章节是否存在
        cursor.execute("SELECT id, title, chapter_number FROM chapters WHERE id = ? AND novel_id = ?", (chapter_id, novel_id))
        chapter = cursor.fetchone()

        if not chapter:
            return jsonify({
                'success': False,
                'error': f'未找到章节ID {chapter_id}'
            })

        # 检查章纲分析结果是否存在
        cursor.execute(
            "SELECT id, content FROM chapter_analysis_results WHERE novel_id = ? AND chapter_id = ? AND dimension = 'chapter_outline'",
            (novel_id, chapter_id)
        )
        analysis_result = cursor.fetchone()

        if not analysis_result:
            return jsonify({
                'success': False,
                'error': f'章节ID {chapter_id} 尚未进行章纲分析'
            })

        # 获取章节内容
        cursor.execute("SELECT content FROM chapters WHERE id = ?", (chapter_id,))
        chapter_content_row = cursor.fetchone()

        if not chapter_content_row or not chapter_content_row[0]:
            return jsonify({
                'success': False,
                'error': f'章节ID {chapter_id} 内容为空'
            })

        chapter_content = chapter_content_row[0]

        # 获取分析结果内容
        analysis_content = analysis_result[1]

        if not analysis_content:
            return jsonify({
                'success': False,
                'error': f'章节ID {chapter_id} 的章纲分析结果为空'
            })

        # 生成推理过程内容
        reasoning_content = generate_chapter_outline_reasoning(chapter_content, analysis_content, chapter[1] or f'第{chapter[2]}章')

        # 更新数据库
        cursor.execute(
            "UPDATE chapter_analysis_results SET reasoning_content = ? WHERE novel_id = ? AND chapter_id = ? AND dimension = 'chapter_outline'",
            (reasoning_content, novel_id, chapter_id)
        )

        # 提交更改
        conn.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': f'章节ID {chapter_id} 的章纲分析推理过程已修复',
            'reasoning_content': reasoning_content
        })
    except Exception as e:
        # 返回错误响应
        return jsonify({
            'success': False,
            'error': str(e)
        })
    finally:
        # 关闭数据库连接
        if conn:
            conn.close()

def generate_chapter_outline_reasoning(chapter_content, analysis_content, chapter_title):
    """生成章纲分析推理过程内容"""
    # 创建推理过程模板
    reasoning_template = f"""# 章纲分析推理过程 - {chapter_title}

## 分析思路说明

1. **章节内容提取**：首先提取章节的完整内容，确保分析基于完整的章节文本。
2. **结构分解**：将章节内容分解为开头、发展、高潮和结尾四个部分，分析每个部分的功能和作用。
3. **情节梳理**：梳理章节中的主要情节线索，识别关键事件和转折点。
4. **人物互动分析**：分析章节中出现的人物及其互动关系，了解人物在情节中的作用。
5. **主题元素识别**：识别章节中体现的主题元素和象征意义，理解其与整体故事的关联。
6. **章节功能评估**：评估该章节在整本书中的功能定位，包括推动情节、展示人物或铺设伏笔等。
7. **章纲提炼**：基于以上分析，提炼出章节的核心纲要，包括主要事件、人物发展和主题进展。

## 详细分析

### 章节内容概览

章节内容长度：约{len(chapter_content)}字

章节内容摘要：
```
{chapter_content[:200]}...（内容省略）...{chapter_content[-200:] if len(chapter_content) > 400 else ''}
```

### 结构分解

通过分析章节的叙事结构，我将其分为以下几个部分：

1. **开头部分**：设置场景和引入本章主要内容
2. **发展部分**：展开情节，推动故事发展
3. **高潮部分**：呈现章节的关键冲突或重要转折
4. **结尾部分**：总结本章内容，为下一章做铺垫

### 情节梳理

本章的主要情节包括：
- 梳理章节中发生的主要事件
- 分析事件之间的因果关系
- 识别情节中的转折点和悬念设置

### 人物互动分析

本章出现的主要人物及其互动：
- 分析主要人物在本章的表现和发展
- 探讨人物之间的关系变化
- 评估人物行为对情节推动的作用

### 主题元素识别

本章体现的主题元素：
- 分析章节中呈现的主题思想
- 探讨象征性元素和意象的运用
- 评估主题元素与整体故事主题的一致性

### 章节功能评估

本章在整本书中的功能：
- 评估本章对整体情节的推动作用
- 分析本章对人物发展的贡献
- 探讨本章对主题深化的作用

### 章纲提炼

基于以上分析，本章的核心纲要如下：

{analysis_content}

## 分析结论

通过对章节内容的全面分析，我提炼出了本章的核心纲要，包括主要情节发展、人物互动和主题表达。这一章纲分析有助于理解该章节在整本书中的定位和作用，为作者提供了结构化的章节概览。
"""

    return reasoning_template
