/**
 * 九猫系统 模板详情布局修复脚本
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于修复模板详情的布局，使其在右侧显示而不是挤在左侧
 */

(function() {
    console.log('[模板详情布局修复] 初始化...');
    
    // 全局变量
    let isFixed = false;
    let observer = null;
    let templateCache = {};
    
    // CSS样式
    const styles = `
        .template-container {
            margin-top: 20px;
        }
        
        .template-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            background-color: #fff;
        }
        
        .template-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            padding: 12px 15px;
        }
        
        .template-card .card-body {
            padding: 15px;
        }
        
        .template-content {
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .template-meta {
            margin-bottom: 10px;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .template-actions {
            margin-top: 15px;
            text-align: right;
        }
        
        .template-actions .btn {
            margin-left: 5px;
        }
        
        .template-detail-container {
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .template-detail-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .template-detail-content {
            margin-bottom: 20px;
        }
        
        .template-detail-footer {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #e0e0e0;
            text-align: right;
        }
        
        /* 响应式布局 */
        @media (max-width: 768px) {
            .template-detail-container {
                margin-top: 20px;
            }
        }
    `;
    
    // 添加样式
    function addStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = styles;
        document.head.appendChild(styleElement);
    }
    
    // 修复模板详情布局
    function fixTemplateDetailLayout() {
        console.log('[模板详情布局修复] 开始修复模板详情布局...');
        
        // 检查是否已经修复
        if (isFixed) {
            console.log('[模板详情布局修复] 模板详情布局已修复，无需再次修复');
            return;
        }
        
        try {
            // 查找章节列表容器
            const chapterListContainer = document.querySelector('.chapter-list');
            if (!chapterListContainer) {
                console.log('[模板详情布局修复] 未找到章节列表容器，可能不在章节列表页面');
                return;
            }
            
            // 查找模板容器
            const templateContainers = document.querySelectorAll('.template-container, .template-detail');
            if (templateContainers.length === 0) {
                console.log('[模板详情布局修复] 未找到模板容器，创建新的模板容器');
                
                // 创建新的布局
                createNewLayout(chapterListContainer);
            } else {
                // 修改现有布局
                modifyExistingLayout(chapterListContainer, templateContainers);
            }
            
            // 添加点击事件处理
            addClickHandlers();
            
            // 标记为已修复
            isFixed = true;
            
            console.log('[模板详情布局修复] 模板详情布局修复完成');
        } catch (error) {
            console.error('[模板详情布局修复] 修复模板详情布局时出错:', error);
        }
    }
    
    // 创建新的布局
    function createNewLayout(chapterListContainer) {
        console.log('[模板详情布局修复] 创建新的布局...');
        
        // 获取父容器
        const parentContainer = chapterListContainer.closest('.container') || chapterListContainer.parentElement;
        
        // 创建行容器
        const rowContainer = document.createElement('div');
        rowContainer.className = 'row';
        
        // 创建左侧列
        const leftColumn = document.createElement('div');
        leftColumn.className = 'col-md-4';
        
        // 将章节列表容器移动到左侧列
        if (chapterListContainer.parentElement) {
            chapterListContainer.parentElement.removeChild(chapterListContainer);
        }
        leftColumn.appendChild(chapterListContainer);
        
        // 创建右侧列
        const rightColumn = document.createElement('div');
        rightColumn.className = 'col-md-8';
        
        // 创建模板详情容器
        const templateDetailContainer = document.createElement('div');
        templateDetailContainer.className = 'template-detail-container';
        templateDetailContainer.id = 'template-detail-container';
        
        // 创建模板详情头部
        const templateDetailHeader = document.createElement('div');
        templateDetailHeader.className = 'template-detail-header';
        templateDetailHeader.innerHTML = '<h4>模板详情</h4>';
        
        // 创建模板详情内容
        const templateDetailContent = document.createElement('div');
        templateDetailContent.className = 'template-detail-content';
        templateDetailContent.id = 'template-detail-content';
        templateDetailContent.innerHTML = '<div class="alert alert-info">请从左侧选择要查看的维度或章节，右侧将显示对应的设定模板内容。</div>';
        
        // 创建模板详情底部
        const templateDetailFooter = document.createElement('div');
        templateDetailFooter.className = 'template-detail-footer';
        templateDetailFooter.innerHTML = '<button class="btn btn-sm btn-outline-secondary" id="copy-template-btn">复制内容</button>';
        
        // 组装模板详情容器
        templateDetailContainer.appendChild(templateDetailHeader);
        templateDetailContainer.appendChild(templateDetailContent);
        templateDetailContainer.appendChild(templateDetailFooter);
        
        // 将模板详情容器添加到右侧列
        rightColumn.appendChild(templateDetailContainer);
        
        // 将左右列添加到行容器
        rowContainer.appendChild(leftColumn);
        rowContainer.appendChild(rightColumn);
        
        // 将行容器添加到父容器
        parentContainer.appendChild(rowContainer);
        
        console.log('[模板详情布局修复] 新布局创建完成');
    }
    
    // 修改现有布局
    function modifyExistingLayout(chapterListContainer, templateContainers) {
        console.log('[模板详情布局修复] 修改现有布局...');
        
        // 获取父容器
        const parentContainer = chapterListContainer.closest('.container') || chapterListContainer.parentElement;
        
        // 检查是否已经有行容器
        let rowContainer = chapterListContainer.closest('.row');
        if (!rowContainer) {
            console.log('[模板详情布局修复] 创建新的行容器');
            
            // 创建行容器
            rowContainer = document.createElement('div');
            rowContainer.className = 'row';
            
            // 将行容器添加到父容器
            parentContainer.appendChild(rowContainer);
        }
        
        // 创建左侧列
        let leftColumn = rowContainer.querySelector('.col-md-4');
        if (!leftColumn) {
            console.log('[模板详情布局修复] 创建左侧列');
            
            leftColumn = document.createElement('div');
            leftColumn.className = 'col-md-4';
            
            // 将左侧列添加到行容器
            rowContainer.appendChild(leftColumn);
        }
        
        // 将章节列表容器移动到左侧列
        if (chapterListContainer.parentElement !== leftColumn) {
            if (chapterListContainer.parentElement) {
                chapterListContainer.parentElement.removeChild(chapterListContainer);
            }
            leftColumn.appendChild(chapterListContainer);
        }
        
        // 创建右侧列
        let rightColumn = rowContainer.querySelector('.col-md-8');
        if (!rightColumn) {
            console.log('[模板详情布局修复] 创建右侧列');
            
            rightColumn = document.createElement('div');
            rightColumn.className = 'col-md-8';
            
            // 将右侧列添加到行容器
            rowContainer.appendChild(rightColumn);
        }
        
        // 检查是否已经有模板详情容器
        let templateDetailContainer = rightColumn.querySelector('.template-detail-container');
        if (!templateDetailContainer) {
            console.log('[模板详情布局修复] 创建模板详情容器');
            
            // 创建模板详情容器
            templateDetailContainer = document.createElement('div');
            templateDetailContainer.className = 'template-detail-container';
            templateDetailContainer.id = 'template-detail-container';
            
            // 创建模板详情头部
            const templateDetailHeader = document.createElement('div');
            templateDetailHeader.className = 'template-detail-header';
            templateDetailHeader.innerHTML = '<h4>模板详情</h4>';
            
            // 创建模板详情内容
            const templateDetailContent = document.createElement('div');
            templateDetailContent.className = 'template-detail-content';
            templateDetailContent.id = 'template-detail-content';
            templateDetailContent.innerHTML = '<div class="alert alert-info">请从左侧选择要查看的维度或章节，右侧将显示对应的设定模板内容。</div>';
            
            // 创建模板详情底部
            const templateDetailFooter = document.createElement('div');
            templateDetailFooter.className = 'template-detail-footer';
            templateDetailFooter.innerHTML = '<button class="btn btn-sm btn-outline-secondary" id="copy-template-btn">复制内容</button>';
            
            // 组装模板详情容器
            templateDetailContainer.appendChild(templateDetailHeader);
            templateDetailContainer.appendChild(templateDetailContent);
            templateDetailContainer.appendChild(templateDetailFooter);
            
            // 将模板详情容器添加到右侧列
            rightColumn.appendChild(templateDetailContainer);
        }
        
        // 移动现有模板容器到模板详情内容
        const templateDetailContent = document.getElementById('template-detail-content');
        if (templateDetailContent && templateContainers.length > 0) {
            console.log('[模板详情布局修复] 移动现有模板容器到模板详情内容');
            
            // 清空模板详情内容
            templateDetailContent.innerHTML = '';
            
            // 移动模板容器
            templateContainers.forEach(templateContainer => {
                if (templateContainer.parentElement) {
                    templateContainer.parentElement.removeChild(templateContainer);
                }
                templateDetailContent.appendChild(templateContainer);
            });
        }
        
        console.log('[模板详情布局修复] 现有布局修改完成');
    }
    
    // 添加点击事件处理
    function addClickHandlers() {
        console.log('[模板详情布局修复] 添加点击事件处理...');
        
        // 添加复制按钮事件
        const copyButton = document.getElementById('copy-template-btn');
        if (copyButton) {
            copyButton.addEventListener('click', function() {
                const templateContent = document.querySelector('.template-content');
                if (templateContent) {
                    // 创建临时文本区域
                    const textarea = document.createElement('textarea');
                    textarea.value = templateContent.textContent;
                    document.body.appendChild(textarea);
                    
                    // 选择文本
                    textarea.select();
                    
                    // 复制文本
                    document.execCommand('copy');
                    
                    // 移除临时文本区域
                    document.body.removeChild(textarea);
                    
                    // 显示提示
                    alert('模板内容已复制到剪贴板');
                }
            });
        }
        
        // 添加章节和维度点击事件
        document.addEventListener('click', function(event) {
            // 检查是否点击了章节或维度链接
            const target = event.target.closest('.chapter-item, .dimension-item, [data-template-id]');
            if (!target) return;
            
            // 获取模板ID
            const templateId = target.getAttribute('data-template-id');
            if (!templateId) {
                console.log('[模板详情布局修复] 未找到模板ID，尝试从其他属性获取');
                
                // 尝试从其他属性获取模板ID
                const href = target.getAttribute('href');
                if (href && href.includes('template')) {
                    const match = href.match(/template\/(\d+)/);
                    if (match && match[1]) {
                        loadTemplate(match[1]);
                    }
                }
                
                return;
            }
            
            // 阻止默认行为
            event.preventDefault();
            
            // 加载模板
            loadTemplate(templateId);
        });
        
        console.log('[模板详情布局修复] 点击事件处理添加完成');
    }
    
    // 加载模板
    function loadTemplate(templateId) {
        console.log('[模板详情布局修复] 加载模板:', templateId);
        
        // 查找模板详情内容
        const templateDetailContent = document.getElementById('template-detail-content');
        if (!templateDetailContent) {
            console.log('[模板详情布局修复] 未找到模板详情内容，无法加载模板');
            return;
        }
        
        // 检查缓存
        if (templateCache[templateId]) {
            console.log('[模板详情布局修复] 使用缓存的模板');
            templateDetailContent.innerHTML = templateCache[templateId];
            return;
        }
        
        // 显示加载中
        templateDetailContent.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div><p class="mt-2">加载模板中...</p></div>';
        
        // 发送请求获取模板内容
        fetch(`/api/template/${templateId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 显示模板内容
                if (data.success) {
                    const templateHtml = `
                        <div class="template-card">
                            <div class="card-header">
                                <h5 class="mb-0">${data.data.title || '未命名模板'}</h5>
                                <div class="template-meta">
                                    <small>ID: ${templateId}</small>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="template-content">${data.data.content || '无内容'}</div>
                                <div class="template-actions">
                                    <button class="btn btn-sm btn-outline-secondary copy-btn" data-template-id="${templateId}">复制</button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 更新模板详情内容
                    templateDetailContent.innerHTML = templateHtml;
                    
                    // 缓存模板
                    templateCache[templateId] = templateHtml;
                    
                    // 添加复制按钮事件
                    const copyBtn = templateDetailContent.querySelector('.copy-btn');
                    if (copyBtn) {
                        copyBtn.addEventListener('click', function() {
                            const templateContent = templateDetailContent.querySelector('.template-content');
                            if (templateContent) {
                                // 创建临时文本区域
                                const textarea = document.createElement('textarea');
                                textarea.value = templateContent.textContent;
                                document.body.appendChild(textarea);
                                
                                // 选择文本
                                textarea.select();
                                
                                // 复制文本
                                document.execCommand('copy');
                                
                                // 移除临时文本区域
                                document.body.removeChild(textarea);
                                
                                // 显示提示
                                alert('模板内容已复制到剪贴板');
                            }
                        });
                    }
                } else {
                    templateDetailContent.innerHTML = `<div class="alert alert-danger">加载模板失败: ${data.message || '未知错误'}</div>`;
                }
            })
            .catch(error => {
                console.error('[模板详情布局修复] 加载模板时出错:', error);
                templateDetailContent.innerHTML = `<div class="alert alert-danger">加载模板失败: ${error.message}</div>`;
            });
    }
    
    // 观察DOM变化
    function observeDOMChanges() {
        console.log('[模板详情布局修复] 开始观察DOM变化...');
        
        // 创建观察器
        observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新的节点添加
                if (mutation.addedNodes.length > 0) {
                    // 检查是否需要修复模板详情布局
                    setTimeout(fixTemplateDetailLayout, 500);
                }
            });
        });
        
        // 开始观察
        observer.observe(document.body, { childList: true, subtree: true });
        
        console.log('[模板详情布局修复] DOM变化观察器已启动');
    }
    
    // 初始化
    function initialize() {
        console.log('[模板详情布局修复] 初始化...');
        
        // 添加样式
        addStyles();
        
        // 检查页面是否已加载
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('[模板详情布局修复] 页面已加载，开始修复');
            setTimeout(function() {
                fixTemplateDetailLayout();
                observeDOMChanges();
            }, 1000);
        } else {
            console.log('[模板详情布局修复] 页面尚未加载，等待DOMContentLoaded事件');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('[模板详情布局修复] DOMContentLoaded事件触发，开始修复');
                setTimeout(function() {
                    fixTemplateDetailLayout();
                    observeDOMChanges();
                }, 1000);
            });
        }
    }
    
    // 导出全局函数
    window.fixTemplateDetailLayout = fixTemplateDetailLayout;
    
    // 执行初始化
    initialize();
    
    console.log('[模板详情布局修复] 初始化完成');
})();
