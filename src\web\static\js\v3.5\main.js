/**
 * 九猫小说分析写作系统v3.5 主JavaScript文件
 */

// 在DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('九猫小说分析写作系统v3.5 已加载');

    // 初始化工具提示
    initTooltips();

    // 初始化弹出框
    initPopovers();

    // 初始化Markdown渲染
    initMarkdownRendering();

    // 初始化分析结果折叠面板
    initCollapsiblePanels();

    // 初始化进度条动画
    initProgressBars();

    // 初始化滚动动画
    initScrollAnimations();

    // 初始化错误处理
    initErrorHandling();
});

/**
 * 初始化Bootstrap工具提示
 */
function initTooltips() {
    try {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } catch (error) {
        console.error('初始化工具提示时出错:', error);
    }
}

/**
 * 初始化Bootstrap弹出框
 */
function initPopovers() {
    try {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function(popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    } catch (error) {
        console.error('初始化弹出框时出错:', error);
    }
}

/**
 * 初始化Markdown渲染
 */
function initMarkdownRendering() {
    try {
        // 配置Marked选项
        marked.setOptions({
            renderer: new marked.Renderer(),
            highlight: function(code, lang) {
                if (hljs && lang && hljs.getLanguage(lang)) {
                    try {
                        return hljs.highlight(lang, code).value;
                    } catch (e) {
                        console.error('代码高亮出错:', e);
                    }
                }
                return code;
            },
            pedantic: false,
            gfm: true,
            breaks: true,
            sanitize: false,
            smartLists: true,
            smartypants: false,
            xhtml: false
        });

        // 渲染所有Markdown内容
        document.querySelectorAll('.markdown-content').forEach(function(element) {
            element.innerHTML = marked.parse(element.textContent);
        });
    } catch (error) {
        console.error('初始化Markdown渲染时出错:', error);
    }
}

/**
 * 初始化分析结果折叠面板
 */
function initCollapsiblePanels() {
    try {
        // 为所有折叠面板添加点击事件
        document.querySelectorAll('.collapsible-header').forEach(function(header) {
            header.addEventListener('click', function() {
                // 切换图标
                const icon = this.querySelector('.collapse-icon');
                if (icon) {
                    icon.classList.toggle('fa-chevron-down');
                    icon.classList.toggle('fa-chevron-up');
                }
            });
        });
    } catch (error) {
        console.error('初始化折叠面板时出错:', error);
    }
}

/**
 * 初始化进度条动画
 */
function initProgressBars() {
    try {
        // 为所有进度条添加动画
        document.querySelectorAll('.progress-bar').forEach(function(progressBar) {
            const value = progressBar.getAttribute('aria-valuenow');
            progressBar.style.width = '0%';
            
            // 使用setTimeout延迟动画，确保DOM已完全加载
            setTimeout(function() {
                progressBar.style.width = value + '%';
            }, 100);
        });
    } catch (error) {
        console.error('初始化进度条动画时出错:', error);
    }
}

/**
 * 初始化滚动动画
 */
function initScrollAnimations() {
    try {
        // 为所有带有data-animate属性的元素添加滚动动画
        const animatedElements = document.querySelectorAll('[data-animate]');
        
        if (animatedElements.length > 0) {
            // 检查元素是否在视口中
            function isElementInViewport(el) {
                const rect = el.getBoundingClientRect();
                return (
                    rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
                    rect.bottom >= 0
                );
            }
            
            // 处理滚动事件
            function handleScroll() {
                animatedElements.forEach(function(element) {
                    if (isElementInViewport(element) && !element.classList.contains('animated')) {
                        const animation = element.getAttribute('data-animate');
                        element.classList.add('animated', animation);
                    }
                });
            }
            
            // 添加滚动事件监听器
            window.addEventListener('scroll', handleScroll);
            
            // 初始检查
            handleScroll();
        }
    } catch (error) {
        console.error('初始化滚动动画时出错:', error);
    }
}

/**
 * 初始化错误处理
 */
function initErrorHandling() {
    try {
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.message, 'at', event.filename, ':', event.lineno);
            
            // 如果存在错误日志容器，则添加错误信息
            const errorLogContainer = document.getElementById('errorLog');
            if (errorLogContainer) {
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger';
                errorMessage.innerHTML = `<strong>错误:</strong> ${event.message} <br><small>${event.filename}:${event.lineno}</small>`;
                errorLogContainer.appendChild(errorMessage);
            }
        });
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
            
            // 如果存在错误日志容器，则添加错误信息
            const errorLogContainer = document.getElementById('errorLog');
            if (errorLogContainer) {
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-warning';
                errorMessage.innerHTML = `<strong>Promise错误:</strong> ${event.reason}`;
                errorLogContainer.appendChild(errorMessage);
            }
        });
    } catch (error) {
        console.error('初始化错误处理时出错:', error);
    }
}

/**
 * 格式化日期时间
 * @param {Date|string} date 日期对象或日期字符串
 * @param {string} format 格式化模式，默认为'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    try {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }
        
        if (isNaN(date.getTime())) {
            return 'Invalid Date';
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    } catch (error) {
        console.error('格式化日期时间时出错:', error);
        return 'Error';
    }
}

/**
 * 显示通知
 * @param {string} message 通知消息
 * @param {string} type 通知类型，可选值：'success', 'info', 'warning', 'danger'
 * @param {number} duration 显示时长（毫秒），默认为3000
 */
function showNotification(message, type = 'info', duration = 3000) {
    try {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification-toast`;
        notification.innerHTML = message;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '250px';
        notification.style.padding = '15px';
        notification.style.borderRadius = '5px';
        notification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px)';
        notification.style.transition = 'opacity 0.3s, transform 0.3s';
        
        // 添加到文档
        document.body.appendChild(notification);
        
        // 显示通知
        setTimeout(function() {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 10);
        
        // 自动关闭
        setTimeout(function() {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            
            // 移除元素
            setTimeout(function() {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    } catch (error) {
        console.error('显示通知时出错:', error);
    }
}
