/**
 * 九猫 (Nine Cats) - 小说文本分析系统
 * 主要 JavaScript 文件
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，初始化JS功能');

    // 初始化工具提示
    try {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            console.log('工具提示初始化成功');
        } else {
            console.warn('Bootstrap Tooltip 未定义，跳过初始化');
        }
    } catch (e) {
        console.error('初始化工具提示时出错:', e);
    }

    // 初始化弹出框
    try {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
            popoverTriggerList.map(function(popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            console.log('弹出框初始化成功');
        } else {
            console.warn('Bootstrap Popover 未定义，跳过初始化');
        }
    } catch (e) {
        console.error('初始化弹出框时出错:', e);
    }

    // 自动关闭警告框
    try {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined' && bootstrap.Alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                } else {
                    alert.style.display = 'none';
                }
            }, 5000);
        });
    } catch (e) {
        console.error('设置自动关闭警告框时出错:', e);
    }

    // 分析进度处理
    const analysisProgressBar = document.getElementById('analysis-progress-bar');
    if (analysisProgressBar) {
        console.log('找到分析进度条，初始化进度更新');
        let progress = 0;
        const interval = setInterval(function() {
            progress += 5;
            if (progress > 100) {
                clearInterval(interval);
            } else {
                analysisProgressBar.style.width = progress + '%';
                analysisProgressBar.setAttribute('aria-valuenow', progress);
            }
        }, 1000);
    }

    // 文件上传预览
    const fileInput = document.getElementById('file');
    if (fileInput) {
        console.log('找到文件上传输入框，添加事件监听器');
        fileInput.addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                const fileNameDisplay = document.getElementById('file-name-display');
                if (fileNameDisplay) {
                    fileNameDisplay.textContent = fileName;
                }

                // 如果标题为空，使用文件名作为标题
                const titleInput = document.getElementById('title');
                if (titleInput && !titleInput.value) {
                    // 移除文件扩展名
                    const titleFromFile = fileName.split('.').slice(0, -1).join('.');
                    titleInput.value = titleFromFile;
                }
            }
        });
    }

    // 自定义分析表单处理
    const customAnalysisForm = document.getElementById('custom-analysis-form');
    if (customAnalysisForm) {
        console.log('找到自定义分析表单，添加提交事件监听器');
        customAnalysisForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const novelId = this.getAttribute('data-novel-id');
            const promptInput = document.getElementById('custom-prompt');
            const resultContainer = document.getElementById('custom-analysis-result');
            const submitButton = this.querySelector('button[type="submit"]');

            if (!promptInput.value.trim()) {
                alert('请输入分析提示');
                return;
            }

            // 显示加载状态
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 分析中...';
            resultContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">正在分析，请稍候...</p></div>';

            // 发送API请求
            fetch('/api/custom-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    novel_id: novelId,
                    prompt: promptInput.value
                }),
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('服务器响应异常：' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 安全处理结果
                    const safeResult = data.result ? data.result.replace(/\n/g, '<br>') : '';
                    resultContainer.innerHTML =
                        '<div class="card mt-3">' +
                        '<div class="card-header">分析结果</div>' +
                        '<div class="card-body">' +
                        '<div class="analysis-content">' + safeResult + '</div>' +
                        '</div>' +
                        '</div>';
                } else {
                    // 安全处理错误
                    const safeError = data.error || '未知错误';
                    resultContainer.innerHTML =
                        '<div class="alert alert-danger mt-3">' +
                        '分析失败: ' + safeError +
                        '</div>';
                }
            })
            .catch(error => {
                // 安全处理错误消息
                const safeErrorMessage = error && error.message ? error.message : '未知错误';
                resultContainer.innerHTML =
                    '<div class="alert alert-danger mt-3">' +
                    '请求错误: ' + safeErrorMessage +
                    '</div>';
                console.error('API请求失败:', error);
            })
            .finally(() => {
                submitButton.disabled = false;
                submitButton.innerHTML = '开始分析';
            });
        });
    }

    console.log('JS初始化完成');
});
