/**
 * 九猫 - 章节分析模态框直接修复脚本
 * 专门用于修复章节分析页面的模态框问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[章节模态框直接修复] 脚本已加载');

    // 直接修复章节分析模态框
    function fixChapterAnalysisModal() {
        console.log('[章节模态框直接修复] 开始修复章节分析模态框');

        // 查找分析按钮
        const analyzeButton = document.querySelector('button[data-bs-toggle="modal"][data-bs-target="#analyzeModal"]');
        if (!analyzeButton) {
            console.error('[章节模态框直接修复] 找不到分析按钮');
            return;
        }

        console.log('[章节模态框直接修复] 找到分析按钮，添加直接事件处理');

        // 移除原有的事件监听器
        const newButton = analyzeButton.cloneNode(true);
        analyzeButton.parentNode.replaceChild(newButton, analyzeButton);

        // 添加新的事件监听器
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('[章节模态框直接修复] 点击分析按钮');
            
            // 查找模态框
            const modal = document.getElementById('analyzeModal');
            if (!modal) {
                console.error('[章节模态框直接修复] 找不到分析模态框');
                return;
            }
            
            // 直接显示模态框
            modal.style.display = 'block';
            modal.classList.add('show');
            document.body.classList.add('modal-open');
            
            // 创建背景遮罩
            let backdrop = document.querySelector('.modal-backdrop');
            if (!backdrop) {
                backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                document.body.appendChild(backdrop);
            }
            
            console.log('[章节模态框直接修复] 成功显示模态框');
            
            // 确保维度选择正确显示
            setTimeout(function() {
                if (typeof window.fixChapterAnalysisDimensions === 'function') {
                    window.fixChapterAnalysisDimensions();
                    console.log('[章节模态框直接修复] 调用维度修复函数');
                }
            }, 100);
        });
        
        // 修复关闭按钮
        const closeButtons = modal.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            // 移除原有的事件监听器
            const newCloseButton = button.cloneNode(true);
            button.parentNode.replaceChild(newCloseButton, button);
            
            // 添加新的事件监听器
            newCloseButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('[章节模态框直接修复] 点击关闭按钮');
                
                // 隐藏模态框
                modal.style.display = 'none';
                modal.classList.remove('show');
                document.body.classList.remove('modal-open');
                
                // 移除背景遮罩
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.parentNode.removeChild(backdrop);
                }
                
                console.log('[章节模态框直接修复] 成功隐藏模态框');
            });
        });
        
        console.log('[章节模态框直接修复] 章节分析模态框修复完成');
    }

    // 初始化
    function init() {
        console.log('[章节模态框直接修复] 初始化中...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(fixChapterAnalysisModal, 500);
            });
        } else {
            setTimeout(fixChapterAnalysisModal, 500);
        }
        
        // 导出全局函数
        window.fixChapterAnalysisModal = fixChapterAnalysisModal;
        
        console.log('[章节模态框直接修复] 初始化完成');
    }

    // 初始化
    init();

    // 在页面完全加载后再次修复
    window.addEventListener('load', function() {
        console.log('[章节模态框直接修复] 页面完全加载，再次修复');
        setTimeout(fixChapterAnalysisModal, 1000);
    });

    console.log('[章节模态框直接修复] 脚本加载完成');
})();
