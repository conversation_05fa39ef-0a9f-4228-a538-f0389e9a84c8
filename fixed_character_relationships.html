<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第1章 她爬到了修仙界 - character_relationships 分析 - 九猫</title>

    <!-- 尝试多种路径加载Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/lib/bootstrap.min.css" onerror="this.onerror=null;this.href='/direct-static/css/lib/bootstrap.min.css'">

    <!-- 如果本地加载失败，尝试CDN -->
    <script>
        // 检查Bootstrap CSS是否已加载
        setTimeout(function() {
            var bootstrapLoaded = false;
            var styles = document.styleSheets;
            for (var i = 0; i < styles.length; i++) {
                if (styles[i].href && styles[i].href.indexOf('bootstrap') > -1) {
                    bootstrapLoaded = true;
                    break;
                }
            }

            if (!bootstrapLoaded) {
                console.warn('本地Bootstrap CSS加载失败，尝试CDN');
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css';
                document.head.appendChild(link);
            }
        }, 1000);
    </script>

    <link rel="stylesheet" href="/static/css/style.css" onerror="this.onerror=null;this.href='/direct-static/css/style.css';console.error('样式文件加载失败')">

<style>
    .analysis-content {
        white-space: pre-wrap;
        line-height: 1.6;
    }

    .metadata-item {
        margin-bottom: 0.5rem;
    }

    .visualization-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }
</style>


    <!-- 预加载核心JS资源，确保快速可用 -->
    <link rel="preload" href="/static/js/lib/jquery-3.6.0.min.js" as="script">
    <link rel="preload" href="/static/js/lib/bootstrap.bundle.min.js" as="script">
    <link rel="prefetch" href="/static/js/lib/chart.min.js" as="script">

    <!-- 备用预加载 -->
    <link rel="preload" href="/direct-static/js/lib/jquery-3.6.0.min.js" as="script">
    <link rel="preload" href="/direct-static/js/lib/bootstrap.bundle.min.js" as="script">
    <link rel="prefetch" href="/direct-static/js/lib/chart.min.js" as="script">
<script src="/static/js/error-handler.js" onerror="this.onerror=null;this.src='/direct-static/js/error-handler.js';console.error('错误处理脚本加载失败，尝试备用路径')"></script>
    </head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <span class="fs-4">九猫</span>
                <small class="text-muted">小说文本分析系统</small>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/upload">上传小说</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">首页</a></li>
                <li class="breadcrumb-item"><a href="/novel/4">第1章 她爬到了修仙界</a></li>
                <li class="breadcrumb-item active">character_relationships 分析</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <h1>character_relationships 分析</h1>
            <a href="/novel/4" class="btn btn-outline-secondary">
                返回小说页面
            </a>
        </div>

        <div class="row mt-4">
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析结果</h5>
                    </div>
                    <div class="card-body">
                        <div class="visualization-container">
                            <h6>分析可视化</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <canvas id="radarChart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <canvas id="barChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="analysis-content markdown-content">
                            <h1>分析过程中出错</h1>

<h2>错误详情</h2>
<p>分析维度 <strong>character_relationships</strong> 时遇到了问题。</p>

<h2>错误信息</h2>
<pre><code>name 'stats_start' is not defined
</code></pre>

<h2>建议操作</h2>
<p>请尝试以下解决方法：</p>
<ol>
<li>刷新页面并重新尝试分析</li>
<li>检查小说文本是否过长或包含特殊字符</li>
<li>确认API连接正常</li>
</ol>

<p>如果问题持续存在，请联系系统管理员。</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">小说信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>标题：</strong> 第1章 她爬到了修仙界</p>
                        <p><strong>作者：</strong> 未知</p>
                        <p><strong>字数：</strong> 2643</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">分析元数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="metadata-item">
                            <strong>分析时间：</strong>
                            <span>2025-05-02 18:30</span>
                        </div>
                        <div class="metadata-item">
                            <strong>最后更新：</strong>
                            <span>2025-05-02 18:30</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">九猫小说文本分析系统 &copy; 2025</span>
            <p class="small text-muted">基于阿里云 DeepSeek R1 的小说深度分析工具</p>
        </div>
    </footer>

    <!-- 直接加载核心JS库 -->
    <script src="/static/js/lib/jquery-3.6.0.min.js" onerror="this.onerror=null;this.src='/direct-static/js/lib/jquery-3.6.0.min.js';console.error('jQuery加载失败，尝试备用路径')"></script>
    <script src="/static/js/lib/bootstrap.bundle.min.js" onerror="this.onerror=null;this.src='/direct-static/js/lib/bootstrap.bundle.min.js';console.error('Bootstrap JS加载失败，尝试备用路径')"></script>

    <!-- 按需加载Chart.js -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 只在需要时加载Chart.js
            if (document.querySelector('.chart-container') || document.querySelector('.analysis-chart')) {
                var chartScript = document.createElement('script');
                chartScript.src = "/static/js/lib/chart.min.js";
                chartScript.onerror = function() {
                    console.error('Chart.js加载失败，尝试备用路径');
                    this.onerror = null;
                    this.src = '/direct-static/js/lib/chart.min.js';
                };
                document.head.appendChild(chartScript);
                console.log('已加载Chart.js');

                // 如果页面上有初始化图表的函数，调用它
                chartScript.onload = function() {
                    if (typeof window.initializeCharts === 'function') {
                        setTimeout(function() {
                            window.initializeCharts();
                        }, 100);
                    }
                };
            }
        });
    </script>

    <!-- 项目主JS文件 -->
    <script src="/static/js/main.js" onerror="this.onerror=null;this.src='/direct-static/js/main.js';console.error('主JS文件加载失败，尝试备用路径')"></script>

    <!-- 初始化Bootstrap组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查Bootstrap是否已加载
            if (typeof bootstrap !== 'undefined') {
                console.log('初始化Bootstrap组件');
                try {
                    // 初始化工具提示
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    if (bootstrap.Tooltip) {
                    tooltipTriggerList.map(function(tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                        console.log('Tooltip 组件初始化成功');
                    } else {
                        console.warn('Bootstrap Tooltip 未定义，跳过初始化');
                    }

                    // 初始化弹出框
                    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                    if (bootstrap.Popover) {
                    popoverTriggerList.map(function(popoverTriggerEl) {
                        return new bootstrap.Popover(popoverTriggerEl);
                    });
                        console.log('Popover 组件初始化成功');
                    } else {
                        console.warn('Bootstrap Popover 未定义，跳过初始化');
                    }
                } catch (e) {
                    console.error('初始化Bootstrap组件时出错: ' + e);
                }
            } else {
                console.warn('Bootstrap未加载，跳过组件初始化');
            }
        });
    </script>

    <!-- 简单的图表初始化脚本 -->
    <script>
        // 在页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 加载Chart.js库
            var chartScript = document.createElement('script');
            chartScript.src = "/static/js/lib/chart.min.js";
            chartScript.onerror = function() {
                console.error('Chart.js加载失败，尝试备用路径');
                this.onerror = null;
                this.src = '/direct-static/js/lib/chart.min.js';
            };
            
            chartScript.onload = function() {
                console.log('Chart.js加载成功，初始化图表');
                
                // 初始化雷达图
                var radarCtx = document.getElementById('radarChart').getContext('2d');
                new Chart(radarCtx, {
                    type: 'radar',
                    data: {
                        labels: ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'],
                        datasets: [{
                            label: 'character_relationships分析评分',
                            data: [0, 0, 0, 0, 0],
                            fill: true,
                            backgroundColor: 'rgba(74, 107, 223, 0.2)',
                            borderColor: 'rgb(74, 107, 223)',
                            pointBackgroundColor: 'rgb(74, 107, 223)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgb(74, 107, 223)'
                        }]
                    },
                    options: {
                        elements: {
                            line: {
                                borderWidth: 3
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                suggestedMin: 0,
                                suggestedMax: 100
                            }
                        }
                    }
                });

                // 初始化柱状图
                var barCtx = document.getElementById('barChart').getContext('2d');
                new Chart(barCtx, {
                    type: 'bar',
                    data: {
                        labels: ['人物数量', '关系复杂度', '关系变化', '关系描述', '关系冲突'],
                        datasets: [{
                            label: 'character_relationships分析指标',
                            data: [0, 0, 0, 0, 0],
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.2)',
                                'rgba(255, 99, 132, 0.2)',
                                'rgba(54, 162, 235, 0.2)',
                                'rgba(255, 206, 86, 0.2)',
                                'rgba(153, 102, 255, 0.2)'
                            ],
                            borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(153, 102, 255, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            };
            
            document.head.appendChild(chartScript);
        });
    </script>
</body>
</html>
