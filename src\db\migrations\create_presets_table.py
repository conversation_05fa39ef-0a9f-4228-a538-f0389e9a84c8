"""
创建预设内容表的迁移脚本
"""
import logging
import sqlite3
from datetime import datetime

logger = logging.getLogger(__name__)

def migrate():
    """创建预设内容表"""
    try:
        # 连接数据库
        conn = sqlite3.connect('novels.db')
        cursor = conn.cursor()

        # 检查表是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='presets'")
        if cursor.fetchone():
            logger.info("预设内容表已存在，跳过创建")
            conn.close()
            return True

        # 创建预设内容表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS presets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            category TEXT NOT NULL DEFAULT 'other',
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            meta_info TEXT
        )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX idx_presets_category ON presets(category)')
        cursor.execute('CREATE INDEX idx_presets_created_at ON presets(created_at)')

        # 提交事务
        conn.commit()

        logger.info("预设内容表创建成功")

        # 添加一些示例预设内容
        sample_presets = [
            {
                'title': '人物设定模板',
                'content': '''# 人物设定模板

## 基本信息
- 姓名：
- 年龄：
- 性别：
- 外貌特征：
- 职业/身份：

## 性格特点
- 主要性格：
- 优点：
- 缺点：
- 习惯/怪癖：

## 背景故事
- 成长经历：
- 重要事件：
- 家庭关系：

## 目标与动机
- 短期目标：
- 长期目标：
- 内在动机：
- 外在动机：

## 关系网络
- 盟友/朋友：
- 敌人/对手：
- 恋爱关系：

## 能力与技能
- 特殊能力：
- 专业技能：
- 弱点：

## 其他细节
- 喜好：
- 厌恶：
- 座右铭/口头禅：''',
                'category': 'character',
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            },
            {
                'title': '情节构思框架',
                'content': '''# 情节构思框架

## 核心冲突
- 主要冲突：
- 次要冲突：
- 内在冲突：

## 三幕结构
### 第一幕（开端）
- 引入主角和世界观
- 引发事件
- 第一个转折点

### 第二幕（发展）
- 尝试解决问题
- 障碍和挫折
- 中点事件
- 危机升级
- 第二个转折点

### 第三幕（结局）
- 最终对决
- 高潮
- 结局

## 情节节奏
- 高潮点：
- 低谷点：
- 悬念设置：

## 情感曲线
- 希望与绝望的交替
- 情感高点：
- 情感低点：

## 主题探索
- 核心主题：
- 如何通过情节体现主题：''',
                'category': 'plot',
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }
        ]

        for preset in sample_presets:
            cursor.execute(
                'INSERT INTO presets (title, content, category, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
                (preset['title'], preset['content'], preset['category'], preset['created_at'], preset['updated_at'])
            )

        # 提交事务
        conn.commit()

        logger.info(f"添加了 {len(sample_presets)} 个示例预设内容")

        # 关闭连接
        conn.close()

        return True
    except Exception as e:
        logger.error(f"创建预设内容表时出错: {str(e)}", exc_info=True)
        return False
