<!-- 修复链接和功能问题 -->
<div class="card mb-4">
    <div class="card-header bg-warning">
        <h5 class="mb-0">系统修复工具</h5>
    </div>
    <div class="card-body">
        <p><strong>问题描述：</strong>系统存在一些链接和功能问题，可以使用以下工具进行修复。</p>
        <div class="d-flex flex-wrap gap-2">
            <a href="{{ url_for('v3.fix_chapter_outline_page') }}" class="btn btn-warning">
                <i class="fas fa-tools me-2"></i>修复章纲分析推理过程
            </a>
            <button id="fixAnalysisStatusBtn" class="btn btn-warning">
                <i class="fas fa-sync me-2"></i>修复分析状态
            </button>
            <button id="fixChapterAnalysisBtn" class="btn btn-warning">
                <i class="fas fa-book me-2"></i>修复章节分析
            </button>
            <button id="fixAnalysisDisplayBtn" class="btn btn-warning">
                <i class="fas fa-wrench me-2"></i>修复分析显示
            </button>
            <button id="fixApiPathsBtn" class="btn btn-info">
                <i class="fas fa-link me-2"></i>修复API路径
            </button>
        </div>
    </div>
</div>

<script>
// 修复分析状态按钮点击事件
$(document).on('click', '#fixAnalysisStatusBtn', function() {
    if (!confirm('确定要修复分析状态吗？这将重新检查所有分析维度的完成状态。')) {
        return;
    }

    addLogEntry('info', '开始修复分析状态...');

    $.ajax({
        url: '/api/fix_analysis_status',
        type: 'POST',
        success: function(response) {
            if (response.success) {
                addLogEntry('info', '分析状态修复成功: ' + response.message);
                alert('分析状态修复成功！');
            } else {
                addLogEntry('error', '分析状态修复失败: ' + response.error);
                alert('分析状态修复失败: ' + response.error);
            }
        },
        error: function(xhr) {
            addLogEntry('error', '分析状态修复请求失败: ' + xhr.status + ' ' + xhr.statusText);
            alert('修复请求失败，请查看控制台日志');
        }
    });
});

// 修复章节分析按钮点击事件
$(document).on('click', '#fixChapterAnalysisBtn', function() {
    if (!confirm('确定要修复章节分析吗？这将检查并修复章节分析中的问题。')) {
        return;
    }

    addLogEntry('info', '开始修复章节分析...');

    $.ajax({
        url: '/api/fix_chapter_analysis',
        type: 'POST',
        success: function(response) {
            if (response.success) {
                addLogEntry('info', '章节分析修复成功: ' + response.message);
                alert('章节分析修复成功！');
            } else {
                addLogEntry('error', '章节分析修复失败: ' + response.error);
                alert('章节分析修复失败: ' + response.error);
            }
        },
        error: function(xhr) {
            addLogEntry('error', '章节分析修复请求失败: ' + xhr.status + ' ' + xhr.statusText);
            alert('修复请求失败，请查看控制台日志');
        }
    });
});

// 修复分析显示按钮点击事件
$(document).on('click', '#fixAnalysisDisplayBtn', function() {
    // 获取当前选中的模板ID、维度和章节ID
    const templateId = getSelectedTemplateId();
    const dimension = getSelectedDimension();
    const chapterId = getSelectedChapterId();

    if (!templateId) {
        alert('请先选择一个参考蓝本');
        return;
    }

    // 显示修复中提示
    addLogEntry('info', '开始修复分析显示问题...');

    // 如果当前在章节分析页面
    if ($('#chapters-tab').hasClass('active') && chapterId && dimension) {
        addLogEntry('info', `修复章节分析: 模板ID=${templateId}, 章节ID=${chapterId}, 维度=${dimension}`);

        // 修复章节分析结果
        if (typeof fixChapterAnalysisResult === 'function') {
            fixChapterAnalysisResult(templateId, chapterId, dimension);
        } else {
            // 手动修复
            $('#chapterAnalysisContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">修复中，正在重新加载章节分析结果...</p></div>');

            $.ajax({
                url: `/api/novel/${templateId}/chapter/${chapterId}/analysis/${dimension}`,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        let content = '';
                        if (response.result && response.result.content) {
                            content = response.result.content;
                        } else if (response.content) {
                            content = response.content;
                        }

                        if (content) {
                            $('#chapterAnalysisContent').html(marked.parse(content));
                            addLogEntry('info', '成功修复章节分析结果内容');
                        } else {
                            $('#chapterAnalysisContent').html('<div class="alert alert-warning">无法获取章节分析结果内容</div>');
                        }
                    } else {
                        $('#chapterAnalysisContent').html(`<div class="alert alert-warning">${response.error || '加载分析结果失败'}</div>`);
                    }
                },
                error: function() {
                    // 尝试备用API路径
                    $.ajax({
                        url: `/api/novels/${templateId}/chapter/${chapterId}/analysis/${dimension}`,
                        type: 'GET',
                        success: function(backupResponse) {
                            if (backupResponse.success) {
                                let content = '';
                                if (backupResponse.result && backupResponse.result.content) {
                                    content = backupResponse.result.content;
                                } else if (backupResponse.content) {
                                    content = backupResponse.content;
                                }

                                if (content) {
                                    $('#chapterAnalysisContent').html(marked.parse(content));
                                    addLogEntry('info', '通过备用API路径成功修复章节分析结果内容');
                                }
                            }
                        }
                    });
                }
            });
        }

        // 修复章节推理过程
        if (typeof fixChapterReasoningContent === 'function') {
            fixChapterReasoningContent(templateId, chapterId, dimension);
        } else {
            // 手动修复
            $('#chapterReasoningContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">修复中，正在重新加载章节推理过程...</p></div>');

            $.ajax({
                url: `/api/novel/${templateId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        let content = '';
                        if (response.reasoning_content) {
                            content = response.reasoning_content;
                        } else if (response.content) {
                            content = response.content;
                        }

                        if (content) {
                            $('#chapterReasoningContent').html(marked.parse(content));
                            addLogEntry('info', '成功修复章节推理过程内容');
                        } else {
                            $('#chapterReasoningContent').html('<div class="alert alert-warning">无法获取章节推理过程内容</div>');
                        }
                    } else {
                        $('#chapterReasoningContent').html(`<div class="alert alert-warning">${response.error || '加载推理过程失败'}</div>`);
                    }
                },
                error: function() {
                    // 尝试备用API路径
                    $.ajax({
                        url: `/api/novels/${templateId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`,
                        type: 'GET',
                        success: function(backupResponse) {
                            if (backupResponse.success) {
                                let content = '';
                                if (backupResponse.reasoning_content) {
                                    content = backupResponse.reasoning_content;
                                } else if (backupResponse.content) {
                                    content = backupResponse.content;
                                }

                                if (content) {
                                    $('#chapterReasoningContent').html(marked.parse(content));
                                    addLogEntry('info', '通过备用API路径成功修复章节推理过程内容');
                                }
                            }
                        }
                    });
                }
            });
        }
    }
    // 如果当前在整本书分析页面
    else if ($('#book-tab').hasClass('active') && dimension) {
        addLogEntry('info', `修复整本书分析: 模板ID=${templateId}, 维度=${dimension}`);

        // 修复分析结果
        if (typeof fixAnalysisResult === 'function') {
            fixAnalysisResult(templateId, dimension);
        } else {
            // 手动修复
            $('#analysisContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">修复中，正在重新加载分析结果...</p></div>');

            $.ajax({
                url: `/api/novel/${templateId}/analysis/${dimension}`,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        let content = '';
                        if (response.result && response.result.content) {
                            content = response.result.content;
                        } else if (response.content) {
                            content = response.content;
                        }

                        if (content) {
                            $('#analysisContent').html(marked.parse(content));
                            addLogEntry('info', '成功修复分析结果内容');
                        } else {
                            $('#analysisContent').html('<div class="alert alert-warning">无法获取分析结果内容</div>');
                        }
                    } else {
                        $('#analysisContent').html(`<div class="alert alert-warning">${response.error || '加载分析结果失败'}</div>`);
                    }
                },
                error: function() {
                    // 尝试备用API路径
                    $.ajax({
                        url: `/api/novels/${templateId}/analysis/${dimension}`,
                        type: 'GET',
                        success: function(backupResponse) {
                            if (backupResponse.success) {
                                let content = '';
                                if (backupResponse.result && backupResponse.result.content) {
                                    content = backupResponse.result.content;
                                } else if (backupResponse.content) {
                                    content = backupResponse.content;
                                }

                                if (content) {
                                    $('#analysisContent').html(marked.parse(content));
                                    addLogEntry('info', '通过备用API路径成功修复分析结果内容');
                                }
                            }
                        }
                    });
                }
            });
        }

        // 修复推理过程
        if (typeof fixReasoningContent === 'function') {
            fixReasoningContent(templateId, dimension);
        } else {
            // 手动修复
            $('#reasoningContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">修复中，正在重新加载推理过程...</p></div>');

            $.ajax({
                url: `/api/novel/${templateId}/analysis/${dimension}/reasoning_content`,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        let content = '';
                        if (response.reasoning_content) {
                            content = response.reasoning_content;
                        } else if (response.content) {
                            content = response.content;
                        }

                        if (content) {
                            $('#reasoningContent').html(marked.parse(content));
                            addLogEntry('info', '成功修复推理过程内容');
                        } else {
                            $('#reasoningContent').html('<div class="alert alert-warning">无法获取推理过程内容</div>');
                        }
                    } else {
                        $('#reasoningContent').html(`<div class="alert alert-warning">${response.error || '加载推理过程失败'}</div>`);
                    }
                },
                error: function() {
                    // 尝试备用API路径
                    $.ajax({
                        url: `/api/novels/${templateId}/analysis/${dimension}/reasoning_content`,
                        type: 'GET',
                        success: function(backupResponse) {
                            if (backupResponse.success) {
                                let content = '';
                                if (backupResponse.reasoning_content) {
                                    content = backupResponse.reasoning_content;
                                } else if (backupResponse.content) {
                                    content = backupResponse.content;
                                }

                                if (content) {
                                    $('#reasoningContent').html(marked.parse(content));
                                    addLogEntry('info', '通过备用API路径成功修复推理过程内容');
                                }
                            }
                        }
                    });
                }
            });
        }
    } else {
        alert('请先选择一个分析维度');
    }
});

// 修复API路径按钮点击事件
$(document).on('click', '#fixApiPathsBtn', function() {
    addLogEntry('info', '开始修复API路径问题...');

    // 检查是否有API路径修复脚本
    if (typeof window.fixApiPaths === 'function') {
        window.fixApiPaths();
        addLogEntry('info', 'API路径已修复');
    } else {
        // 修改全局API路径变量
        if (typeof CONFIG !== 'undefined' && CONFIG.apiPaths) {
            CONFIG.apiPaths = {
                novel: ['/api/novel/', '/api/novels/'],
                analysis: ['/api/novel/{novel_id}/analysis/{dimension}', '/api/novels/{novel_id}/analysis/{dimension}'],
                reasoning: ['/api/novel/{novel_id}/analysis/{dimension}/reasoning_content', '/api/novels/{novel_id}/analysis/{dimension}/reasoning_content'],
                chapterAnalysis: ['/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}', '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}'],
                chapterReasoning: ['/api/novel/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content', '/api/novels/{novel_id}/chapter/{chapter_id}/analysis/{dimension}/reasoning_content']
            };

            addLogEntry('info', 'API路径已更新');
        } else {
            addLogEntry('warn', '无法访问CONFIG.apiPaths变量');
        }

        // 尝试修复jQuery.ajax方法
        if (typeof jQuery !== 'undefined' && typeof jQuery.ajax === 'function') {
            const originalAjax = jQuery.ajax;
            jQuery.ajax = function(url, options) {
                // 处理不同的参数形式
                if (typeof url === 'object') {
                    options = url;
                    url = options.url;
                } else if (typeof options === 'object') {
                    url = options.url || url;
                }

                // 修复URL
                if (url && typeof url === 'string') {
                    // 替换API路径
                    if (url.includes('/api/novels/')) {
                        url = url.replace('/api/novels/', '/api/novel/');
                        addLogEntry('info', `修复API路径: ${url}`);
                    }

                    // 替换特定端点
                    if (url.includes('/template/structure') || url.includes('/template/paragraph_length')) {
                        url = url.replace('/novel/', '/template/');
                        addLogEntry('info', `修复API路径: ${url}`);
                    }

                    // 更新URL
                    if (typeof options === 'object') {
                        options.url = url;
                    }
                }

                // 调用原始方法
                return originalAjax.apply(this, typeof options === 'object' ? [options] : [url, options]);
            };

            addLogEntry('info', 'jQuery.ajax方法已修改，将自动修复API路径');
        }
    }

    // 刷新页面
    if (confirm('API路径已修复，是否刷新页面以应用更改？')) {
        location.reload();
    }
});

// 辅助函数：获取当前选中的模板ID
function getSelectedTemplateId() {
    // 尝试从全局变量获取
    if (typeof selectedTemplateId !== 'undefined' && selectedTemplateId) {
        return selectedTemplateId;
    }

    // 尝试从DOM元素获取
    const selectedTemplate = $('.template-card.border-primary');
    if (selectedTemplate.length > 0) {
        return selectedTemplate.data('template-id');
    }

    return null;
}

// 辅助函数：获取当前选中的维度
function getSelectedDimension() {
    // 尝试从全局变量获取
    if (typeof window.selectedDimension !== 'undefined' && window.selectedDimension) {
        return window.selectedDimension;
    }

    // 尝试从DOM元素获取
    const activeDimension = $('.dimension-item.active');
    if (activeDimension.length > 0) {
        return activeDimension.data('dimension');
    }

    return null;
}

// 辅助函数：获取当前选中的章节ID
function getSelectedChapterId() {
    // 尝试从全局变量获取
    if (typeof selectedChapterId !== 'undefined' && selectedChapterId) {
        return selectedChapterId;
    }

    // 尝试从DOM元素获取
    const selectedChapter = $('.chapter-item.active');
    if (selectedChapter.length > 0) {
        return selectedChapter.data('chapter-id');
    }

    return null;
}
</script>
