"""
九猫 - 数据库修复脚本
专门修复novel/4页面的character_relationships分析结果
"""

import os
import sys
import json
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入数据库模型
from src.db.connection import Session
from src.models.analysis_result import AnalysisResult

def fix_character_relationships():
    """修复novel/4页面的character_relationships分析结果"""
    logger.info("开始修复novel/4页面的character_relationships分析结果")
    
    # 创建数据库会话
    session = Session()
    
    try:
        # 查找novel/4页面的character_relationships分析结果
        result = session.query(AnalysisResult).filter_by(
            novel_id=4,
            dimension='character_relationships'
        ).first()
        
        if not result:
            logger.warning("未找到novel/4页面的character_relationships分析结果")
            return False
        
        logger.info(f"找到分析结果ID: {result.id}")
        
        # 获取当前内容
        current_content = result.content
        logger.info(f"当前内容长度: {len(current_content) if current_content else 0}")
        
        # 创建有效的内容
        valid_content = """# 分析过程中出错

## 错误详情
分析维度 **character_relationships** 时遇到了问题。

## 错误信息
```
name 'stats_start' is not defined
```

## 建议操作
请尝试以下解决方法：
1. 刷新页面并重新尝试分析
2. 检查小说文本是否过长或包含特殊字符
3. 确认API连接正常

如果问题持续存在，请联系系统管理员。
"""
        
        # 更新内容
        result.content = valid_content
        logger.info(f"更新后的内容长度: {len(valid_content)}")
        
        # 确保元数据是有效的
        if not result.analysis_metadata:
            result.analysis_metadata = {}
        
        # 更新元数据
        result.analysis_metadata.update({
            "processing_time": 0,
            "chunk_count": 0,
            "api_calls": 0,
            "tokens_used": 0,
            "cost": 0,
            "fixed_by_script": True,
            "fix_timestamp": "2025-05-02T18:30:00"
        })
        
        # 提交更改
        session.commit()
        logger.info("成功修复分析结果")
        
        return True
    except Exception as e:
        logger.error(f"修复分析结果时出错: {str(e)}")
        session.rollback()
        return False
    finally:
        session.close()

def main():
    """主函数"""
    logger.info("开始运行数据库修复脚本")
    
    # 修复character_relationships分析结果
    success = fix_character_relationships()
    
    if success:
        logger.info("修复脚本运行成功")
    else:
        logger.error("修复脚本运行失败")

if __name__ == "__main__":
    main()
