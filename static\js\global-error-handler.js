/**
 * 九猫系统 全局错误处理脚本
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于捕获和修复全局错误
 */

(function() {
    console.log('[全局错误处理] 初始化...');
    
    // 全局变量
    let errorCount = 0;
    const MAX_ERROR_COUNT = 10;
    const errorTypes = {
        BOOTSTRAP: 'bootstrap',
        JQUERY: 'jquery',
        SYNTAX: 'syntax',
        OTHER: 'other'
    };
    
    // 已捕获的错误
    const capturedErrors = [];
    
    // 错误处理函数
    function handleError(message, source, lineno, colno, error) {
        // 增加错误计数
        errorCount++;
        
        // 记录错误
        console.error(`[全局错误处理] 捕获到错误 #${errorCount}:`, message);
        console.error(`[全局错误处理] 来源: ${source}, 行: ${lineno}, 列: ${colno}`);
        
        // 分析错误类型
        const errorType = analyzeErrorType(message, source);
        
        // 记录错误
        capturedErrors.push({
            message,
            source,
            lineno,
            colno,
            error,
            type: errorType,
            timestamp: new Date().getTime()
        });
        
        // 根据错误类型处理
        switch (errorType) {
            case errorTypes.BOOTSTRAP:
                handleBootstrapError(message, source);
                break;
            case errorTypes.JQUERY:
                handleJQueryError(message, source);
                break;
            case errorTypes.SYNTAX:
                handleSyntaxError(message, source);
                break;
            default:
                // 其他错误不处理
                break;
        }
        
        // 如果错误太多，停止处理
        if (errorCount > MAX_ERROR_COUNT) {
            console.error(`[全局错误处理] 错误太多 (${errorCount}/${MAX_ERROR_COUNT})，停止处理`);
            return false;
        }
        
        // 返回true表示错误已处理，不会在控制台显示
        return true;
    }
    
    // 分析错误类型
    function analyzeErrorType(message, source) {
        // 检查是否是Bootstrap错误
        if (
            (message && (
                message.includes('bootstrap') ||
                message.includes('modal') ||
                message.includes('tab') ||
                message.includes('collapse') ||
                message.includes('标记匹配') ||
                message.includes('bootstrap.bundle.min.js')
            )) ||
            (source && source.includes('bootstrap'))
        ) {
            return errorTypes.BOOTSTRAP;
        }
        
        // 检查是否是jQuery错误
        if (
            (message && (
                message.includes('$ is not defined') ||
                message.includes('jQuery') ||
                message.includes('jquery') ||
                message.includes('$(...).on is not a function') ||
                message.includes('$(...).tab is not a function')
            )) ||
            (source && source.includes('jquery'))
        ) {
            return errorTypes.JQUERY;
        }
        
        // 检查是否是语法错误
        if (
            message && (
                message.includes('SyntaxError') ||
                message.includes('Unexpected token') ||
                message.includes('Unexpected identifier') ||
                message.includes('标记匹配')
            )
        ) {
            return errorTypes.SYNTAX;
        }
        
        // 其他错误
        return errorTypes.OTHER;
    }
    
    // 处理Bootstrap错误
    function handleBootstrapError(message, source) {
        console.log('[全局错误处理] 处理Bootstrap错误:', message);
        
        // 检查是否有修复函数
        if (typeof window.fixBootstrapBundle === 'function') {
            console.log('[全局错误处理] 调用Bootstrap修复函数');
            window.fixBootstrapBundle();
        } else {
            console.log('[全局错误处理] 没有找到Bootstrap修复函数，尝试加载修复脚本');
            
            // 加载修复脚本
            const script = document.createElement('script');
            script.src = '/static/js/bootstrap-bundle-fix-loader.js';
            
            script.onload = function() {
                console.log('[全局错误处理] Bootstrap修复脚本加载成功');
                
                // 检查是否有修复函数
                if (typeof window.fixBootstrapBundle === 'function') {
                    console.log('[全局错误处理] 调用Bootstrap修复函数');
                    window.fixBootstrapBundle();
                }
            };
            
            script.onerror = function() {
                console.error('[全局错误处理] Bootstrap修复脚本加载失败');
                
                // 直接加载CDN版本
                const cdnScript = document.createElement('script');
                cdnScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
                cdnScript.integrity = 'sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz';
                cdnScript.crossOrigin = 'anonymous';
                document.head.appendChild(cdnScript);
            };
            
            document.head.appendChild(script);
        }
    }
    
    // 处理jQuery错误
    function handleJQueryError(message, source) {
        console.log('[全局错误处理] 处理jQuery错误:', message);
        
        // 检查是否有修复函数
        if (typeof window.ensureJQuery === 'function') {
            console.log('[全局错误处理] 调用jQuery修复函数');
            window.ensureJQuery();
        } else {
            console.log('[全局错误处理] 没有找到jQuery修复函数，尝试加载jQuery');
            
            // 加载jQuery
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js';
            
            script.onload = function() {
                console.log('[全局错误处理] jQuery加载成功');
                
                // 确保$ 变量可用
                window.$ = jQuery;
            };
            
            document.head.appendChild(script);
        }
    }
    
    // 处理语法错误
    function handleSyntaxError(message, source) {
        console.log('[全局错误处理] 处理语法错误:', message);
        
        // 检查是否是Bootstrap语法错误
        if (source && source.includes('bootstrap')) {
            handleBootstrapError(message, source);
        }
    }
    
    // 设置全局错误处理
    window.onerror = handleError;
    
    // 导出已捕获的错误
    window.capturedErrors = capturedErrors;
    
    // 导出错误类型
    window.errorTypes = errorTypes;
    
    // 导出错误处理函数
    window.handleGlobalError = handleError;
    
    console.log('[全局错误处理] 初始化完成');
})();
