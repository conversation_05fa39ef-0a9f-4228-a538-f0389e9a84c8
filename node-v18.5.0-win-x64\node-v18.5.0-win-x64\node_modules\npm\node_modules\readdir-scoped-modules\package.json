{"name": "readdir-scoped-modules", "version": "1.1.0", "description": "Like `fs.readdir` but handling `@org/module` dirs as if they were a single entry.", "main": "readdir.js", "directories": {"test": "test"}, "dependencies": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "graceful-fs": "^4.1.2", "once": "^1.3.0"}, "devDependencies": {"tap": "^1.2.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/npm/readdir-scoped-modules"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "bugs": {"url": "https://github.com/npm/readdir-scoped-modules/issues"}, "homepage": "https://github.com/npm/readdir-scoped-modules", "files": ["readdir.js"]}