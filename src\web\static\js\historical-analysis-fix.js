/**
 * 历史分析修复脚本
 * 专门处理历史上已完成分析但没有显示结果的情况
 */
(function() {
    console.log('[历史分析修复] 脚本已加载');
    
    // 安全的日志函数
    function safeLog(message, level = 'log') {
        try {
            if (level === 'error') {
                console.error(`[历史分析修复] ${message}`);
            } else if (level === 'warn') {
                console.warn(`[历史分析修复] ${message}`);
            } else {
                console.log(`[历史分析修复] ${message}`);
            }
        } catch (e) {
            // 忽略日志错误
        }
    }
    
    // 获取当前小说ID
    function getNovelId() {
        // 尝试从URL中提取
        const urlMatch = window.location.pathname.match(/\/novel\/(\d+)/);
        if (urlMatch && urlMatch[1]) {
            return urlMatch[1];
        }
        
        // 尝试从面包屑导航中获取
        const breadcrumbLinks = document.querySelectorAll('.breadcrumb-item a');
        for (const link of breadcrumbLinks) {
            const href = link.getAttribute('href');
            if (href) {
                const novelMatch = href.match(/\/novel\/(\d+)/);
                if (novelMatch && novelMatch[1]) {
                    return novelMatch[1];
                }
            }
        }
        
        return null;
    }
    
    // 获取当前维度
    function getDimension() {
        // 尝试从URL中提取
        const urlMatch = window.location.pathname.match(/\/analysis\/([^\/]+)$/);
        if (urlMatch && urlMatch[1]) {
            return urlMatch[1];
        }
        
        // 尝试从当前维度元素获取
        const currentDimension = document.getElementById('currentDimension');
        if (currentDimension && currentDimension.textContent.trim()) {
            return currentDimension.textContent.trim();
        }
        
        return null;
    }
    
    // 处理小说详情页
    function handleNovelDetailPage() {
        try {
            // 检查是否在小说详情页
            if (window.location.pathname.match(/\/novel\/\d+\/?$/)) {
                safeLog('检测到小说详情页');
                
                // 检查是否有13维度已完成标记
                const completionStatus = document.querySelector('.card-body');
                if (completionStatus && completionStatus.textContent.includes('13/13')) {
                    safeLog('检测到13个维度全部完成');
                    
                    // 查找维度链接
                    const dimensionLinks = document.querySelectorAll('a.badge-success');
                    if (dimensionLinks.length > 0) {
                        safeLog(`找到${dimensionLinks.length}个已完成的维度链接`);
                        setTimeout(() => {
                            safeLog('自动点击第一个维度链接');
                            dimensionLinks[0].click();
                        }, 500);
                    } else {
                        safeLog('未找到已完成的维度链接');
                        
                        // 尝试找其他类型的链接
                        const allLinks = document.querySelectorAll('a[href*="/analysis/"]');
                        if (allLinks.length > 0) {
                            safeLog(`找到${allLinks.length}个分析链接`);
                            setTimeout(() => {
                                safeLog('自动点击第一个分析链接');
                                allLinks[0].click();
                            }, 500);
                        }
                    }
                }
            }
        } catch (e) {
            safeLog(`处理小说详情页时出错: ${e.message}`, 'error');
        }
    }
    
    // 处理分析页面
    function handleAnalysisPage() {
        try {
            // 检查是否在分析页面
            if (window.location.pathname.match(/\/analysis\//)) {
                safeLog('检测到分析页面');
                
                const novelId = getNovelId();
                const dimension = getDimension();
                
                if (!novelId || !dimension) {
                    safeLog(`无法获取小说ID或维度: novelId=${novelId}, dimension=${dimension}`, 'warn');
                    return;
                }
                
                // 检查分析内容是否为空
                const contentContainer = document.querySelector('.analysis-content');
                if (contentContainer && contentContainer.innerHTML.trim() === '') {
                    safeLog('分析内容为空，尝试加载');
                    
                    // 尝试从API加载内容
                    if (window.fetchAnalysisResult && window.updateAnalysisResult) {
                        safeLog('使用全局API函数加载内容');
                        window.fetchAnalysisResult(novelId, null, dimension)
                            .then(result => {
                                if (window.updateAnalysisResult(result)) {
                                    safeLog('成功加载分析结果');
                                } else {
                                    safeLog('更新分析结果失败', 'warn');
                                }
                            })
                            .catch(error => safeLog(`加载分析结果失败: ${error.message}`, 'error'));
                    } else {
                        safeLog('全局API函数未定义，直接使用fetch', 'warn');
                        
                        // 直接使用fetch加载内容
                        fetch(`/api/novel/${novelId}/analysis/${dimension}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success && data.result && data.result.content) {
                                    safeLog('获取到分析结果内容');
                                    contentContainer.innerHTML = data.result.content;
                                    safeLog('已更新分析内容');
                                } else {
                                    safeLog('分析结果为空或无效', 'warn');
                                }
                            })
                            .catch(error => safeLog(`直接获取分析结果失败: ${error.message}`, 'error'));
                    }
                }
                
                // 检查分析状态是否正确
                const statusElement = document.getElementById('analysisStatus');
                if (statusElement && statusElement.textContent.trim() !== '已完成') {
                    safeLog('修正分析状态为已完成');
                    statusElement.className = 'badge badge-success px-3 py-2';
                    statusElement.textContent = '已完成';
                    
                    const progressBar = document.getElementById('progressBar');
                    if (progressBar) {
                        progressBar.className = 'progress-bar bg-success';
                        progressBar.style.width = '100%';
                        progressBar.setAttribute('aria-valuenow', 100);
                        progressBar.textContent = '100%';
                    }
                }
            }
        } catch (e) {
            safeLog(`处理分析页面时出错: ${e.message}`, 'error');
        }
    }
    
    // 主函数
    function main() {
        try {
            safeLog('开始处理历史分析');
            
            // 根据页面类型选择处理方法
            if (window.location.pathname.match(/\/novel\/\d+\/?$/)) {
                handleNovelDetailPage();
            } else if (window.location.pathname.match(/\/analysis\//)) {
                handleAnalysisPage();
                
                // 对于分析页面，定期检查内容
                setInterval(handleAnalysisPage, 2000);
            }
        } catch (e) {
            safeLog(`主函数执行出错: ${e.message}`, 'error');
        }
    }
    
    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', main);
    } else {
        main();
    }
    
    safeLog('脚本加载完成');
})(); 