/**
 * 九猫小说分析写作系统 - 真实推理过程修复脚本
 *
 * 此脚本用于修复章节分析中显示模拟数据而非真实推理过程的问题
 * 版本: 1.0.0
 */

(function() {
    console.log('[真实推理过程修复] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        // 可能的API路径前缀
        apiPathPrefixes: [
            '/api/novel/',
            '/api/novels/',
            '/v3/api/novel/',
            '/v3/api/novels/',
            '/v3.1/api/novel/',
            '/v3.1/api/novels/',
            '/direct_db/analysis/'
        ],
        // 可能的推理过程字段名
        reasoningFields: [
            'reasoning_content',
            'reasoning_process',
            'analysis_process',
            'analysis_reasoning',
            'reasoning_details',
            'reasoning'
        ]
    };

    // 状态
    const STATE = {
        initialized: false,
        originalLoadChapterAnalysisResult: null,
        originalLoadAnalysisResult: null,
        cachedReasoningContent: {}  // 缓存已获取的推理过程内容
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[真实推理过程修复] ${message}`);
        }
    }

    // 初始化
    function init() {
        debugLog('初始化真实推理过程修复脚本');

        // 重写章节分析结果加载函数
        if (typeof loadChapterAnalysisResult === 'function') {
            STATE.originalLoadChapterAnalysisResult = loadChapterAnalysisResult;
            window.loadChapterAnalysisResult = fixedLoadChapterAnalysisResult;
            debugLog('已重写loadChapterAnalysisResult函数');
        }

        // 重写整本书分析结果加载函数
        if (typeof loadAnalysisResult === 'function') {
            STATE.originalLoadAnalysisResult = loadAnalysisResult;
            window.loadAnalysisResult = fixedLoadAnalysisResult;
            debugLog('已重写loadAnalysisResult函数');
        }

        // 监听推理过程标签页激活事件
        $(document).on('shown.bs.tab', '#chapter-reasoning-tab', function() {
            debugLog('章节推理过程标签页被激活');

            // 获取当前选中的模板ID、章节ID和维度
            const templateId = getSelectedTemplateId();
            const chapterId = getSelectedChapterId();
            const dimension = getSelectedDimension();

            if (templateId && chapterId && dimension) {
                // 检查推理过程内容是否为模板数据
                const reasoningContent = $('#chapterReasoningContent').text();
                if (isTemplateReasoning(reasoningContent)) {
                    debugLog('检测到模板推理过程，尝试获取真实推理过程');
                    loadRealChapterReasoning(templateId, chapterId, dimension);
                }
            }
        });

        // 监听整本书推理过程标签页激活事件
        $(document).on('shown.bs.tab', '#reasoning-tab', function() {
            debugLog('整本书推理过程标签页被激活');

            // 获取当前选中的模板ID和维度
            const templateId = getSelectedTemplateId();
            const dimension = getSelectedDimension();

            if (templateId && dimension) {
                // 检查推理过程内容是否为模板数据
                const reasoningContent = $('#reasoningContent').text();
                if (isTemplateReasoning(reasoningContent)) {
                    debugLog('检测到模板推理过程，尝试获取真实推理过程');
                    loadRealBookReasoning(templateId, dimension);
                }
            }
        });

        STATE.initialized = true;
        debugLog('真实推理过程修复脚本初始化完成');
    }

    // 修复后的章节分析结果加载函数
    function fixedLoadChapterAnalysisResult(templateId, chapterId, dimension) {
        debugLog(`修复的loadChapterAnalysisResult被调用: 模板ID=${templateId}, 章节ID=${chapterId}, 维度=${dimension}`);

        // 调用原始函数
        if (STATE.originalLoadChapterAnalysisResult) {
            STATE.originalLoadChapterAnalysisResult(templateId, chapterId, dimension);
        }

        // 延迟执行，确保原始函数先执行完毕
        setTimeout(() => {
            // 检查推理过程内容是否为模板数据
            const reasoningContent = $('#chapterReasoningContent').text();
            if (isTemplateReasoning(reasoningContent)) {
                debugLog('检测到模板推理过程，尝试获取真实推理过程');
                loadRealChapterReasoning(templateId, chapterId, dimension);
            }
        }, 1000);
    }

    // 修复后的整本书分析结果加载函数
    function fixedLoadAnalysisResult(templateId, dimension) {
        debugLog(`修复的loadAnalysisResult被调用: 模板ID=${templateId}, 维度=${dimension}`);

        // 调用原始函数
        if (STATE.originalLoadAnalysisResult) {
            STATE.originalLoadAnalysisResult(templateId, dimension);
        }

        // 延迟执行，确保原始函数先执行完毕
        setTimeout(() => {
            // 检查推理过程内容是否为模板数据
            const reasoningContent = $('#reasoningContent').text();
            if (isTemplateReasoning(reasoningContent)) {
                debugLog('检测到模板推理过程，尝试获取真实推理过程');
                loadRealBookReasoning(templateId, dimension);
            }
        }, 1000);
    }

    // 判断推理过程内容是否为模板数据
    function isTemplateReasoning(content) {
        if (!content) return true;

        // 检查是否包含模板特征
        const templateFeatures = [
            '分析思路说明',
            '1. 文本特征提取',
            '2. 结构化分析',
            '3. 角色关系图谱',
            '4. 冲突点识别',
            '5. 主题深度探索',
            '6. 读者情感预测',
            '7. 写作技巧评估',
            '8. 市场定位分析',
            '详细分析',
            '本文通过多层次分析'
        ];

        // 如果包含多个模板特征，则认为是模板数据
        let matchCount = 0;
        for (const feature of templateFeatures) {
            if (content.includes(feature)) {
                matchCount++;
            }
        }

        return matchCount >= 3;  // 如果匹配3个或更多特征，则认为是模板数据
    }

    // 加载真实的章节推理过程
    function loadRealChapterReasoning(templateId, chapterId, dimension) {
        debugLog(`尝试加载真实章节推理过程: 模板ID=${templateId}, 章节ID=${chapterId}, 维度=${dimension}`);

        // 检查缓存
        const cacheKey = `chapter_${templateId}_${chapterId}_${dimension}`;
        if (STATE.cachedReasoningContent[cacheKey]) {
            debugLog('使用缓存的推理过程内容');
            $('#chapterReasoningContent').html(marked.parse(STATE.cachedReasoningContent[cacheKey]));
            return;
        }

        // 显示加载中状态
        $('#chapterReasoningContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">正在获取真实推理过程，请稍候...</p></div>');

        // 尝试所有可能的API路径
        tryAllApiPaths(templateId, chapterId, dimension, true, function(content) {
            if (content) {
                // 缓存推理过程内容
                STATE.cachedReasoningContent[cacheKey] = content;

                // 更新显示
                $('#chapterReasoningContent').html(marked.parse(content));
                debugLog('成功加载真实章节推理过程');
            } else {
                // 尝试从数据库直接获取
                tryDirectDbAccess(templateId, chapterId, dimension, true, function(dbContent) {
                    if (dbContent) {
                        // 缓存推理过程内容
                        STATE.cachedReasoningContent[cacheKey] = dbContent;

                        // 更新显示
                        $('#chapterReasoningContent').html(marked.parse(dbContent));
                        debugLog('通过数据库直接访问成功加载真实章节推理过程');
                    } else {
                        $('#chapterReasoningContent').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>无法获取真实推理过程，请尝试重新分析</div>');
                        debugLog('无法获取真实章节推理过程', 'warn');
                    }
                });
            }
        });
    }

    // 加载真实的整本书推理过程
    function loadRealBookReasoning(templateId, dimension) {
        debugLog(`尝试加载真实整本书推理过程: 模板ID=${templateId}, 维度=${dimension}`);

        // 检查缓存
        const cacheKey = `book_${templateId}_${dimension}`;
        if (STATE.cachedReasoningContent[cacheKey]) {
            debugLog('使用缓存的推理过程内容');
            $('#reasoningContent').html(marked.parse(STATE.cachedReasoningContent[cacheKey]));
            return;
        }

        // 显示加载中状态
        $('#reasoningContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">正在获取真实推理过程，请稍候...</p></div>');

        // 尝试所有可能的API路径
        tryAllApiPaths(templateId, null, dimension, false, function(content) {
            if (content) {
                // 缓存推理过程内容
                STATE.cachedReasoningContent[cacheKey] = content;

                // 更新显示
                $('#reasoningContent').html(marked.parse(content));
                debugLog('成功加载真实整本书推理过程');
            } else {
                // 尝试从数据库直接获取
                tryDirectDbAccess(templateId, null, dimension, false, function(dbContent) {
                    if (dbContent) {
                        // 缓存推理过程内容
                        STATE.cachedReasoningContent[cacheKey] = dbContent;

                        // 更新显示
                        $('#reasoningContent').html(marked.parse(dbContent));
                        debugLog('通过数据库直接访问成功加载真实整本书推理过程');
                    } else {
                        $('#reasoningContent').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>无法获取真实推理过程，请尝试重新分析</div>');
                        debugLog('无法获取真实整本书推理过程', 'warn');
                    }
                });
            }
        });
    }

    // 尝试所有可能的API路径
    function tryAllApiPaths(templateId, chapterId, dimension, isChapter, callback) {
        let apiPaths = [];

        // 构建所有可能的API路径
        for (const prefix of CONFIG.apiPathPrefixes) {
            if (isChapter && chapterId) {
                apiPaths.push(`${prefix}${templateId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`);
            } else {
                apiPaths.push(`${prefix}${templateId}/analysis/${dimension}/reasoning_content`);
            }
        }

        debugLog(`尝试 ${apiPaths.length} 个可能的API路径`);

        // 递归尝试所有API路径
        function tryNextPath(index) {
            if (index >= apiPaths.length) {
                debugLog('所有API路径均失败', 'warn');
                callback(null);
                return;
            }

            const apiUrl = apiPaths[index];
            debugLog(`尝试API路径 ${index + 1}/${apiPaths.length}: ${apiUrl}`);

            $.ajax({
                url: apiUrl,
                type: 'GET',
                success: function(response) {
                    // 检查响应中是否包含推理过程内容
                    let content = null;

                    // 尝试所有可能的字段名
                    for (const field of CONFIG.reasoningFields) {
                        if (response[field]) {
                            content = response[field];
                            break;
                        } else if (response.result && response.result[field]) {
                            content = response.result[field];
                            break;
                        }
                    }

                    if (content && !isTemplateReasoning(content)) {
                        debugLog(`API路径 ${apiUrl} 成功获取真实推理过程`);
                        callback(content);
                    } else {
                        debugLog(`API路径 ${apiUrl} 返回的是模板数据或无效数据，尝试下一个路径`, 'warn');
                        tryNextPath(index + 1);
                    }
                },
                error: function() {
                    debugLog(`API路径 ${apiUrl} 请求失败，尝试下一个路径`, 'warn');
                    tryNextPath(index + 1);
                }
            });
        }

        // 开始尝试第一个路径
        tryNextPath(0);
    }

    // 尝试从数据库直接获取
    function tryDirectDbAccess(templateId, chapterId, dimension, isChapter, callback) {
        debugLog('尝试从数据库直接获取推理过程');

        let apiUrl = '/direct_db/analysis/get_reasoning_content';

        $.ajax({
            url: apiUrl,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                novel_id: templateId,
                chapter_id: chapterId,
                dimension: dimension,
                is_chapter: isChapter
            }),
            success: function(response) {
                if (response.success && response.reasoning_content && !isTemplateReasoning(response.reasoning_content)) {
                    debugLog('成功从数据库直接获取真实推理过程');
                    callback(response.reasoning_content);
                } else {
                    debugLog('从数据库直接获取的是模板数据或无效数据', 'warn');
                    callback(null);
                }
            },
            error: function() {
                debugLog('从数据库直接获取推理过程失败', 'error');
                callback(null);
            }
        });
    }

    // 获取当前选中的模板ID
    function getSelectedTemplateId() {
        // 尝试从全局变量获取
        if (typeof selectedTemplateId !== 'undefined' && selectedTemplateId) {
            return selectedTemplateId;
        }

        // 尝试从DOM元素获取
        const selectedTemplate = $('.template-card.border-primary');
        if (selectedTemplate.length > 0) {
            return selectedTemplate.data('template-id');
        }

        return null;
    }

    // 获取当前选中的章节ID
    function getSelectedChapterId() {
        // 尝试从全局变量获取
        if (typeof selectedChapterId !== 'undefined' && selectedChapterId) {
            return selectedChapterId;
        }

        // 尝试从DOM元素获取
        const selectedChapter = $('.chapter-item.active');
        if (selectedChapter.length > 0) {
            return selectedChapter.data('chapter-id');
        }

        return null;
    }

    // 获取当前选中的维度
    function getSelectedDimension() {
        // 尝试从全局变量获取
        if (typeof window.selectedDimension !== 'undefined' && window.selectedDimension) {
            return window.selectedDimension;
        }

        // 尝试从DOM元素获取
        const activeDimension = $('.dimension-item.active');
        if (activeDimension.length > 0) {
            return activeDimension.data('dimension');
        }

        return null;
    }

    // 初始化脚本
    $(document).ready(function() {
        init();
    });
})();
