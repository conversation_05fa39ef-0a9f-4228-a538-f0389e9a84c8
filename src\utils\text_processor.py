"""
Text processing utilities for the 九猫 (Nine Cats) novel analysis system.
"""
import re
import os
import logging
from typing import List, Dict, Any, Tuple, Optional
import jieba
import nltk
from collections import Counter

logger = logging.getLogger(__name__)

# Skip NLTK download for now
# try:
#     nltk.data.find('tokenizers/punkt')
# except LookupError:
#     nltk.download('punkt')

class TextProcessor:
    """Text processing utilities for novel analysis."""

    @staticmethod
    def load_text_file(file_path: str, encoding: str = 'utf-8') -> str:
        """
        Load text from a file.

        Args:
            file_path: Path to the text file.
            encoding: File encoding.

        Returns:
            File content as a string.
        """
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except UnicodeDecodeError:
            # Try different encodings if utf-8 fails
            encodings = ['gbk', 'gb2312', 'big5', 'utf-16']
            for enc in encodings:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        logger.info(f"Successfully read file with encoding: {enc}")
                        return f.read()
                except UnicodeDecodeError:
                    continue

            # If all encodings fail, raise an exception
            raise ValueError(f"Could not decode file {file_path} with any of the attempted encodings")

    @staticmethod
    def split_into_chapters(text: str, min_chapter_length: int = 100,
                           max_chapter_length: int = 50000) -> List[str]:
        """
        增强的章节分割方法，支持多种章节标记格式，并进行智能分割。

        Args:
            text: Novel text.
            min_chapter_length: 最小章节长度，小于此长度的章节将被忽略。
            max_chapter_length: 最大章节长度，超过此长度的章节将被尝试再次分割。

        Returns:
            List of chapters.
        """
        logger.info(f"开始分割章节，文本总长度: {len(text)} 字符")

        # 扩展章节标记模式 - 用于匹配整个章节
        chapter_patterns = [
            # 中文数字章节
            r'第[一二三四五六七八九十百千万零〇]+章[\s\S]*?(?=第[一二三四五六七八九十百千万零〇]+章|$)',
            # 阿拉伯数字章节
            r'第\s*\d+\s*章[\s\S]*?(?=第\s*\d+\s*章|$)',
            # 英文章节
            r'Chapter\s+\d+[\s\S]*?(?=Chapter\s+\d+|$)',
            # 中文数字回
            r'第[一二三四五六七八九十百千万零〇]+回[\s\S]*?(?=第[一二三四五六七八九十百千万零〇]+回|$)',
            # 阿拉伯数字回
            r'第\s*\d+\s*回[\s\S]*?(?=第\s*\d+\s*回|$)',
            # 中文数字节
            r'第[一二三四五六七八九十百千万零〇]+节[\s\S]*?(?=第[一二三四五六七八九十百千万零〇]+节|$)',
            # 阿拉伯数字节
            r'第\s*\d+\s*节[\s\S]*?(?=第\s*\d+\s*节|$)',
            # 特殊章节
            r'序章[\s\S]*?(?=第[一二三四五六七八九十百千万零〇\d]+章|Chapter\s+\d+|$)',
            r'前言[\s\S]*?(?=第[一二三四五六七八九十百千万零〇\d]+章|Chapter\s+\d+|$)',
            r'引言[\s\S]*?(?=第[一二三四五六七八九十百千万零〇\d]+章|Chapter\s+\d+|$)',
            r'楔子[\s\S]*?(?=第[一二三四五六七八九十百千万零〇\d]+章|Chapter\s+\d+|$)',
            r'尾声[\s\S]*?(?=$)',
            r'后记[\s\S]*?(?=$)',
            r'番外[\s\S]*?(?=番外\d+|第[一二三四五六七八九十百千万零〇\d]+章|$)',
            # 自定义标题章节 - 匹配可能的章节标题行
            r'^\s*[\u4e00-\u9fff]{2,15}\s*$[\s\S]*?(?=^\s*[\u4e00-\u9fff]{2,15}\s*$|$)',
        ]

        # 章节标记模式 - 用于提取章节标题和分割点
        chapter_marker_patterns = [
            # 中文数字章节
            r'第[一二三四五六七八九十百千万零〇]+章.*?(?=\n)',
            # 阿拉伯数字章节
            r'第\s*\d+\s*章.*?(?=\n)',
            # 英文章节
            r'Chapter\s+\d+.*?(?=\n)',
            # 中文数字回
            r'第[一二三四五六七八九十百千万零〇]+回.*?(?=\n)',
            # 阿拉伯数字回
            r'第\s*\d+\s*回.*?(?=\n)',
            # 中文数字节
            r'第[一二三四五六七八九十百千万零〇]+节.*?(?=\n)',
            # 阿拉伯数字节
            r'第\s*\d+\s*节.*?(?=\n)',
            # 特殊章节
            r'序章.*?(?=\n)',
            r'前言.*?(?=\n)',
            r'引言.*?(?=\n)',
            r'楔子.*?(?=\n)',
            r'尾声.*?(?=\n)',
            r'后记.*?(?=\n)',
            r'番外\d*.*?(?=\n)',
            # 自定义标题章节 - 匹配可能的章节标题行
            r'^\s*[\u4e00-\u9fff]{2,15}\s*$',
        ]

        # 尝试使用多种方法分割章节
        chapters = []

        # 方法1: 使用完整章节模式匹配
        logger.info("方法1: 使用完整章节模式匹配")
        remaining_text = text
        method1_chapters = []

        for pattern in chapter_patterns:
            matches = list(re.finditer(pattern, remaining_text, re.DOTALL | re.MULTILINE))
            if matches:
                for match in matches:
                    chapter_content = match.group(0)
                    if len(chapter_content.strip()) >= min_chapter_length:
                        method1_chapters.append(chapter_content)

                # 从剩余文本中移除这些章节
                for match in reversed(matches):  # 从后往前移除，避免位置变化
                    remaining_text = remaining_text[:match.start()] + remaining_text[match.end():]

        logger.info(f"方法1找到 {len(method1_chapters)} 个章节")

        # 方法2: 使用章节标记分割
        logger.info("方法2: 使用章节标记分割")
        method2_chapters = []

        # 提取所有可能的章节标记
        all_markers = []
        for pattern in chapter_marker_patterns:
            markers = list(re.finditer(pattern, text, re.DOTALL | re.MULTILINE))
            all_markers.extend(markers)

        # 按位置排序
        all_markers.sort(key=lambda m: m.start())

        if all_markers:
            # 提取章节标题，用于日志和调试
            chapter_titles = [text[m.start():m.start()+min(30, len(text)-m.start())].split('\n')[0] for m in all_markers]
            logger.info(f"找到 {len(chapter_titles)} 个章节标记: {chapter_titles[:5]}...")

            # 分割文本
            positions = [m.start() for m in all_markers]

            # 分割章节
            for i in range(len(positions) - 1):
                chapter_text = text[positions[i]:positions[i+1]]
                if len(chapter_text.strip()) >= min_chapter_length:
                    method2_chapters.append(chapter_text)

            # 添加最后一章
            last_chapter = text[positions[-1]:]
            if len(last_chapter.strip()) >= min_chapter_length:
                method2_chapters.append(last_chapter)

        logger.info(f"方法2找到 {len(method2_chapters)} 个章节")

        # 方法3: 使用段落分割（当其他方法失败时）
        logger.info("方法3: 使用段落分割")
        method3_chapters = []

        if not method1_chapters and not method2_chapters:
            # 按段落分割
            paragraphs = text.split('\n\n')

            # 合并段落成章节，每个章节约5000-10000字符
            current_chapter = ""
            for paragraph in paragraphs:
                if not paragraph.strip():
                    continue

                current_chapter += paragraph + "\n\n"

                # 如果当前章节长度超过5000字符，且遇到可能的章节分隔点，则创建新章节
                if len(current_chapter) > 5000:
                    # 检查是否是章节分隔点（如空行后跟可能的标题）
                    if paragraph.strip() and len(paragraph.strip()) < 30:
                        method3_chapters.append(current_chapter)
                        current_chapter = ""

            # 添加最后一个章节
            if current_chapter.strip():
                method3_chapters.append(current_chapter)

        logger.info(f"方法3找到 {len(method3_chapters)} 个章节")

        # 选择最佳分割结果
        if len(method1_chapters) >= len(method2_chapters) and len(method1_chapters) > 0:
            chapters = method1_chapters
            logger.info("使用方法1的分割结果")
        elif len(method2_chapters) > 0:
            chapters = method2_chapters
            logger.info("使用方法2的分割结果")
        else:
            chapters = method3_chapters
            logger.info("使用方法3的分割结果")

        # 处理剩余文本（如果有）
        if remaining_text.strip() and len(remaining_text.strip()) > min_chapter_length:
            # 检查是否有章节标记
            has_chapter_marker = False
            for pattern in chapter_marker_patterns:
                if re.search(pattern, remaining_text[:100], re.DOTALL | re.MULTILINE):
                    has_chapter_marker = True
                    break

            if has_chapter_marker:
                chapters.append(remaining_text)
                logger.info(f"添加未匹配的剩余文本作为单独章节，长度: {len(remaining_text)} 字符")

        # 对章节进行后处理
        processed_chapters = []
        for chapter in chapters:
            # 忽略非常短的章节
            if len(chapter.strip()) < min_chapter_length:
                continue

            # 分割过长的章节
            if len(chapter) > max_chapter_length:
                logger.info(f"发现过长章节 ({len(chapter)} 字符)，尝试再次分割")
                # 尝试按段落分割
                paragraphs = chapter.split('\n\n')

                # 每5000-10000字符创建一个子章节
                sub_chapter = ""
                sub_chapters = []

                for paragraph in paragraphs:
                    if not paragraph.strip():
                        continue

                    sub_chapter += paragraph + "\n\n"

                    if len(sub_chapter) > 10000:
                        sub_chapters.append(sub_chapter)
                        sub_chapter = ""

                # 添加最后一个子章节
                if sub_chapter.strip():
                    sub_chapters.append(sub_chapter)

                if sub_chapters:
                    processed_chapters.extend(sub_chapters)
                    logger.info(f"将过长章节分割为 {len(sub_chapters)} 个子章节")
                else:
                    processed_chapters.append(chapter)
            else:
                processed_chapters.append(chapter)

        # 尝试提取章节标题
        chapters_with_titles = []
        for chapter in processed_chapters:
            # 提取章节的前100个字符
            chapter_start = chapter[:min(100, len(chapter))]

            # 尝试匹配章节标题
            title = None
            for pattern in chapter_marker_patterns:
                match = re.search(pattern, chapter_start, re.DOTALL | re.MULTILINE)
                if match:
                    title = match.group(0).strip()
                    break

            # 如果没有找到标题，使用前20个字符作为标题
            if not title:
                title = chapter_start[:20].strip().replace('\n', ' ')
                if len(title) == 0:
                    title = f"未命名章节 {len(chapters_with_titles) + 1}"

            chapters_with_titles.append((title, chapter))

        # 尝试按章节编号排序
        try:
            def extract_chapter_number(chapter_info):
                title, _ = chapter_info

                # 尝试提取章节编号
                for pattern in [r'第(\d+)章', r'Chapter\s+(\d+)', r'第(\d+)回', r'第(\d+)节']:
                    match = re.search(pattern, title)
                    if match:
                        return int(match.group(1))

                # 尝试从中文数字中提取
                cn_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
                             '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
                             '百': 100, '千': 1000, '万': 10000, '零': 0, '〇': 0}

                for pattern in [r'第([一二三四五六七八九十百千万零〇]+)章', r'第([一二三四五六七八九十百千万零〇]+)回', r'第([一二三四五六七八九十百千万零〇]+)节']:
                    match = re.search(pattern, title)
                    if match:
                        cn_num = match.group(1)
                        # 简单处理：只处理个位数
                        if len(cn_num) == 1 and cn_num in cn_num_map:
                            return cn_num_map[cn_num]

                return 999999  # 默认值，用于无法提取编号的章节

            # 按章节编号排序
            chapters_with_titles.sort(key=extract_chapter_number)
            logger.info("已按章节编号排序")
        except Exception as e:
            logger.error(f"章节排序出错: {str(e)}")

        # 提取排序后的章节内容
        final_chapters = [chapter for _, chapter in chapters_with_titles]

        logger.info(f"章节分割完成，总共识别出 {len(final_chapters)} 个章节")

        return final_chapters

    @staticmethod
    def preview_chapter_splitting(text: str, max_preview_length: int = 100) -> List[Dict[str, Any]]:
        """
        预览章节分割结果，返回章节标题和内容预览。

        Args:
            text: Novel text.
            max_preview_length: 内容预览的最大长度。

        Returns:
            包含章节信息的字典列表，每个字典包含标题、内容预览和长度。
        """
        # 分割章节
        chapters = TextProcessor.split_into_chapters(text)

        # 准备预览信息
        preview_info = []

        for i, chapter in enumerate(chapters):
            # 提取章节的前100个字符
            chapter_start = chapter[:min(100, len(chapter))]

            # 尝试匹配章节标题
            title = None
            for pattern in [
                r'第[一二三四五六七八九十百千万零〇\d]+章.*?(?=\n)',
                r'Chapter\s+\d+.*?(?=\n)',
                r'第[一二三四五六七八九十百千万零〇\d]+回.*?(?=\n)',
                r'第[一二三四五六七八九十百千万零〇\d]+节.*?(?=\n)',
                r'序章.*?(?=\n)',
                r'前言.*?(?=\n)',
                r'引言.*?(?=\n)',
                r'楔子.*?(?=\n)',
                r'尾声.*?(?=\n)',
                r'后记.*?(?=\n)',
                r'番外\d*.*?(?=\n)',
            ]:
                match = re.search(pattern, chapter_start, re.DOTALL)
                if match:
                    title = match.group(0).strip()
                    break

            # 如果没有找到标题，使用章节编号
            if not title:
                title = f"第{i+1}章"

            # 准备内容预览
            content_preview = chapter[:max_preview_length].replace('\n', ' ')
            if len(chapter) > max_preview_length:
                content_preview += "..."

            # 添加到预览信息
            preview_info.append({
                "chapter_number": i + 1,
                "title": title,
                "preview": content_preview,
                "length": len(chapter),
                "word_count": len(re.findall(r'[\u4e00-\u9fff]|[a-zA-Z]+', chapter))
            })

        return preview_info

    @staticmethod
    def extract_sentences(text: str) -> List[str]:
        """
        Extract sentences from text.

        Args:
            text: Text to extract sentences from.

        Returns:
            List of sentences.
        """
        # For Chinese text, we need to handle both Chinese and English punctuation
        text = re.sub(r'([。！？!?])([^"''])', r'\1\n\2', text)  # Split on sentence endings
        text = re.sub(r'(\.{6})([^"''])', r'\1\n\2', text)  # Split on ellipses
        text = re.sub(r'(\…{2})([^"''])', r'\1\n\2', text)  # Split on Chinese ellipses

        # Handle quotations
        text = re.sub(r'([。！？!?]["''])([^，。！？!?])', r'\1\n\2', text)

        # Split the text
        sentences = text.split("\n")

        # Remove empty sentences and strip whitespace
        sentences = [s.strip() for s in sentences if s.strip()]

        return sentences

    @staticmethod
    def analyze_sentence_length(sentences: List[str]) -> Dict[str, Any]:
        """
        Analyze sentence length distribution.

        Args:
            sentences: List of sentences.

        Returns:
            Dictionary with sentence length statistics.
        """
        if not sentences:
            return {
                "average_length": 0,
                "min_length": 0,
                "max_length": 0,
                "length_distribution": {}
            }

        # Calculate sentence lengths
        lengths = [len(s) for s in sentences]

        # Calculate statistics
        avg_length = sum(lengths) / len(lengths)
        min_length = min(lengths)
        max_length = max(lengths)

        # Create length distribution
        length_ranges = [(0, 10), (11, 20), (21, 30), (31, 50), (51, 100), (101, float('inf'))]
        distribution = {}

        for start, end in length_ranges:
            range_name = f"{start}-{end if end != float('inf') else '∞'}"
            count = sum(1 for length in lengths if start <= length <= end)
            distribution[range_name] = count

        return {
            "average_length": avg_length,
            "min_length": min_length,
            "max_length": max_length,
            "length_distribution": distribution
        }

    @staticmethod
    def analyze_paragraph_length(text: str) -> Dict[str, Any]:
        """
        Analyze paragraph length distribution.

        Args:
            text: Text to analyze.

        Returns:
            Dictionary with paragraph length statistics.
        """
        # Split text into paragraphs
        paragraphs = [p.strip() for p in text.split('\n') if p.strip()]

        if not paragraphs:
            return {
                "average_length": 0,
                "min_length": 0,
                "max_length": 0,
                "length_distribution": {}
            }

        # Calculate paragraph lengths
        lengths = [len(p) for p in paragraphs]

        # Calculate statistics
        avg_length = sum(lengths) / len(lengths)
        min_length = min(lengths)
        max_length = max(lengths)

        # Create length distribution
        length_ranges = [(0, 50), (51, 100), (101, 200), (201, 500), (501, 1000), (1001, float('inf'))]
        distribution = {}

        for start, end in length_ranges:
            range_name = f"{start}-{end if end != float('inf') else '∞'}"
            count = sum(1 for length in lengths if start <= length <= end)
            distribution[range_name] = count

        return {
            "average_length": avg_length,
            "min_length": min_length,
            "max_length": max_length,
            "length_distribution": distribution
        }

    @staticmethod
    def extract_keywords(text: str, top_n: int = 20) -> List[Tuple[str, int]]:
        """
        Extract keywords from text.

        Args:
            text: Text to extract keywords from.
            top_n: Number of top keywords to return.

        Returns:
            List of (keyword, frequency) tuples.
        """
        # Use jieba for Chinese word segmentation
        words = jieba.cut(text)

        # Filter out stopwords and single characters
        stopwords = TextProcessor._load_stopwords()
        filtered_words = [word for word in words if word not in stopwords and len(word) > 1]

        # Count word frequencies
        word_counts = Counter(filtered_words)

        # Return top N keywords
        return word_counts.most_common(top_n)

    @staticmethod
    def _load_stopwords() -> List[str]:
        """
        Load Chinese stopwords.

        Returns:
            List of stopwords.
        """
        # Common Chinese stopwords
        common_stopwords = [
            '的', '了', '和', '是', '就', '都', '而', '及', '与', '这', '那', '有', '在',
            '中', '大', '为', '上', '个', '国', '我', '以', '要', '他', '时', '来', '用',
            '们', '生', '到', '作', '地', '于', '出', '就', '分', '对', '成', '会', '可',
            '主', '发', '年', '动', '同', '工', '也', '能', '下', '过', '子', '说', '产',
            '种', '面', '而', '方', '后', '多', '定', '行', '学', '法', '所', '民', '得',
            '经', '十', '三', '之', '进', '着', '等', '部', '度', '家', '电', '力', '里',
            '如', '水', '化', '高', '自', '二', '理', '起', '小', '物', '现', '实', '加',
            '量', '都', '两', '体', '制', '机', '当', '使', '点', '从', '业', '本', '去',
            '把', '性', '好', '应', '开', '它', '合', '还', '因', '由', '其', '些', '然',
            '前', '外', '天', '政', '四', '日', '那', '社', '义', '事', '平', '形', '相',
            '全', '表', '间', '样', '与', '关', '各', '重', '新', '线', '内', '数', '正',
            '心', '反', '你', '明', '看', '原', '又', '么', '利', '比', '或', '但', '质',
            '气', '第', '向', '道', '命', '此', '变', '条', '只', '没', '结', '解', '问',
            '意', '建', '月', '公', '无', '系', '军', '很', '情', '者', '最', '立', '代',
            '想', '已', '通', '并', '提', '直', '题', '党', '程', '展', '五', '果', '料',
            '象', '员', '革', '位', '入', '常', '文', '总', '次', '品', '式', '活', '设',
            '及', '管', '特', '件', '长', '求', '老', '头', '基', '资', '边', '流', '路',
            '级', '少', '图', '山', '统', '接', '知', '较', '将', '组', '见', '计', '别',
            '她', '手', '角', '期', '根', '论', '运', '农', '指', '几', '九', '区', '强',
            '放', '决', '西', '被', '干', '做', '必', '战', '先', '回', '则', '任', '取',
            '据', '处', '队', '南', '给', '色', '光', '门', '即', '保', '治', '北', '造',
            '百', '规', '热', '领', '七', '海', '口', '东', '导', '器', '压', '志', '世',
            '金', '增', '争', '济', '阶', '油', '思', '术', '极', '交', '受', '联', '什',
            '认', '六', '共', '权', '收', '证', '改', '清', '美', '再', '采', '转', '更',
            '单', '风', '切', '打', '白', '教', '速', '花', '带', '安', '场', '身', '车',
            '例', '真', '务', '具', '万', '每', '目', '至', '达', '走', '积', '示', '议',
            '声', '报', '斗', '完', '类', '八', '离', '华', '名', '确', '才', '科', '张',
            '信', '马', '节', '话', '米', '整', '空', '元', '况', '今', '集', '温', '传',
            '土', '许', '步', '群', '广', '石', '记', '需', '段', '研', '界', '拉', '林',
            '律', '叫', '且', '究', '观', '越', '织', '装', '影', '算', '低', '持', '音',
            '众', '书', '布', '复', '容', '儿', '须', '际', '商', '非', '验', '连', '断',
            '深', '难', '近', '矿', '千', '周', '委', '素', '技', '备', '半', '办', '青',
            '省', '列', '习', '响', '约', '支', '般', '史', '感', '劳', '便', '团', '往',
            '酸', '历', '市', '克', '何', '除', '消', '构', '府', '称', '太', '准', '精',
            '值', '号', '率', '族', '维', '划', '选', '标', '写', '存', '候', '毛', '亲',
            '快', '效', '斯', '院', '查', '江', '型', '眼', '王', '按', '格', '养', '易',
            '置', '派', '层', '片', '始', '却', '专', '状', '育', '厂', '京', '识', '适',
            '属', '圆', '包', '火', '住', '调', '满', '县', '局', '照', '参', '红', '细',
            '引', '听', '该', '铁', '价', '严', '首', '底', '液', '官', '德', '随', '病',
            '苏', '失', '尔', '死', '讲', '配', '女', '黄', '推', '显', '谈', '罪', '神',
            '艺', '呢', '席', '含', '企', '望', '密', '批', '营', '项', '防', '举', '球',
            '英', '氧', '势', '告', '李', '台', '落', '木', '帮', '轮', '破', '亚', '师',
            '围', '注', '远', '字', '材', '排', '供', '河', '态', '封', '另', '施', '减',
            '树', '溶', '怎', '止', '案', '言', '士', '均', '武', '固', '叶', '鱼', '波',
            '视', '仅', '费', '紧', '爱', '左', '章', '早', '朝', '害', '续', '轻', '服',
            '试', '食', '充', '兵', '源', '判', '护', '司', '足', '某', '练', '差', '致',
            '板', '田', '降', '黑', '犯', '负', '击', '范', '继', '兴', '似', '余', '坚',
            '曲', '输', '修', '故', '城', '夫', '够', '送', '笔', '船', '占', '右', '财',
            '吃', '富', '春', '职', '觉', '汉', '画', '功', '巴', '跟', '虽', '杂', '飞',
            '检', '吸', '助', '升', '阳', '互', '初', '创', '抗', '考', '投', '坏', '策',
            '古', '径', '换', '未', '跑', '留', '钢', '曾', '端', '责', '站', '简', '述',
            '钱', '副', '尽', '帝', '射', '草', '冲', '承', '独', '令', '限', '阿', '宣',
            '环', '双', '请', '超', '微', '让', '控', '州', '良', '轴', '找', '否', '纪',
            '益', '依', '优', '顶', '础', '载', '倒', '房', '突', '坐', '粉', '敌', '略',
            '客', '袁', '冷', '胜', '绝', '析', '块', '剂', '测', '丝', '协', '诉', '念',
            '陈', '仍', '罗', '盐', '友', '洋', '错', '苦', '夜', '刑', '移', '频', '逐',
            '靠', '混', '母', '短', '皮', '终', '聚', '汽', '村', '云', '哪', '既', '距',
            '卫', '停', '烈', '央', '察', '烧', '迅', '境', '若', '印', '洲', '刻', '括',
            '激', '孔', '搞', '甚', '室', '待', '核', '校', '散', '侵', '吧', '甲', '游',
            '久', '菜', '味', '旧', '模', '湖', '货', '损', '预', '阻', '毫', '普', '稳',
            '乙', '妈', '植', '息', '扩', '银', '语', '挥', '酒', '守', '拿', '序', '纸',
            '医', '缺', '雨', '吗', '针', '刘', '啊', '急', '唱', '误', '训', '愿', '审',
            '附', '获', '茶', '鲁', '预', '辑', '类', '卸', '导', '晚', '志', '演', '故',
            '县', '衣', '血', '峰', '席', '限', '妻', '章', '俱', '样', '姻', '卢', '岁',
            '缓', '凡', '谅', '拨', '沙', '仗', '类', '迁', '凭', '丰'
        ]

        return common_stopwords

    @staticmethod
    def analyze_dialogue_ratio(text: str) -> float:
        """
        Analyze the ratio of dialogue to narrative text.

        Args:
            text: Text to analyze.

        Returns:
            Ratio of dialogue to total text.
        """
        # Look for dialogue markers (quotes)
        dialogue_pattern = r'["'"](.*?)["'"]'
        dialogues = re.findall(dialogue_pattern, text)

        # Calculate total dialogue length
        dialogue_length = sum(len(d) for d in dialogues)

        # Calculate ratio
        if len(text) > 0:
            return dialogue_length / len(text)
        return 0.0

    @staticmethod
    def detect_language(text: str) -> str:
        """
        Detect the primary language of the text.

        Args:
            text: Text to analyze.

        Returns:
            Detected language code.
        """
        # Simple language detection based on character sets
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))

        if chinese_chars > english_chars:
            return "zh"
        else:
            return "en"

    @staticmethod
    def extract_named_entities(text: str, top_n: int = 20) -> List[Tuple[str, int]]:
        """
        Extract named entities from text.

        Args:
            text: Text to extract named entities from.
            top_n: Number of top entities to return.

        Returns:
            List of (entity, frequency) tuples.
        """
        # This is a simplified implementation
        # For Chinese text, we'll use a pattern-based approach

        # Look for common name patterns
        name_patterns = [
            r'[\u4e00-\u9fff]{2,3}(?:先生|小姐|女士|老师|教授|医生|同志|师傅)',  # Names with titles
            r'[\u4e00-\u9fff]{2,3}说',  # Names followed by "said"
            r'[\u4e00-\u9fff]{2,3}问',  # Names followed by "asked"
        ]

        entities = []
        for pattern in name_patterns:
            matches = re.findall(pattern, text)
            entities.extend([m.rstrip('说问') for m in matches])

        # Count entity frequencies
        entity_counts = Counter(entities)

        # Return top N entities
        return entity_counts.most_common(top_n)
