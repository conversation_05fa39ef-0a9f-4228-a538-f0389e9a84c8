"""
九猫应用启动优化和修复模块
该模块解决了应用启动和运行中的常见问题
"""
import os
import sys
import logging
import sqlite3
import threading
import time
import importlib
import shutil
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('startup_fix.log')
    ]
)
logger = logging.getLogger(__name__)

# 定义全局变量
static_js_dir = None
static_css_dir = None
db_path = None

class StartupFix:
    @staticmethod
    def fix_connection_module():
        """修复数据库连接模块"""
        try:
            # 备份原始文件
            connection_path = Path('src/db/connection.py')
            backup_path = connection_path.with_suffix('.py.bak.' + time.strftime('%Y%m%d%H%M%S'))
            
            if connection_path.exists():
                shutil.copy2(connection_path, backup_path)
                logger.info(f"已备份数据库连接模块: {backup_path}")
            
            # 尝试导入连接模块
            try:
                from src.db import connection
                importlib.reload(connection)
                logger.info("数据库连接模块已重新加载")
                return True
            except Exception as e:
                error_msg = str(e)
                if "name 'engine' is used prior to global declaration" in error_msg:
                    logger.warning("检测到全局变量声明问题，尝试修复connection.py")
                    
                    # 读取文件内容
                    with open(connection_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 添加全局变量声明
                    if "# 连接池健康状态\n_pool_healthy = True" in content:
                        content = content.replace(
                            "# 连接池健康状态\n_pool_healthy = True",
                            "# 连接池健康状态\n_pool_healthy = True\n\n# 声明全局变量\nengine = None\nsession_factory = None\nSession = None"
                        )
                    
                    # 修改create_db_engine函数
                    if "engine = create_engine" in content:
                        content = content.replace(
                            "engine = create_engine",
                            "new_engine = create_engine"
                        )
                        content = content.replace(
                            "return engine",
                            "return new_engine"
                        )
                    
                    # 添加初始化函数
                    if "# 创建数据库引擎\nengine = create_db_engine()" in content:
                        content = content.replace(
                            "# 创建数据库引擎\nengine = create_db_engine()",
                            """# 初始化全局对象
def init_db():
    \"\"\"初始化数据库相关全局对象\"\"\"
    global engine, session_factory, Session
    
    # 创建数据库引擎
    engine = create_db_engine()
    
    # 创建会话工厂
    session_factory = sessionmaker(bind=engine)
    
    # 创建线程安全的会话
    Session = scoped_session(session_factory)
    
    # 创建数据库表
    create_tables()

# 初始化数据库
init_db()"""
                        )
                    
                    # 移除旧的初始化代码
                    content = content.replace(
                        """# 创建数据库表
create_tables()

# 创建会话工厂
session_factory = sessionmaker(bind=engine)

# 创建线程安全的会话
Session = scoped_session(session_factory)""",
                        ""
                    )
                    
                    # 写回文件
                    with open(connection_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    logger.info("已修复数据库连接模块中的全局变量声明问题")
                    
                    # 重新尝试导入
                    try:
                        importlib.invalidate_caches()
                        from src.db import connection
                        importlib.reload(connection)
                        logger.info("修复后的数据库连接模块已成功加载")
                        return True
                    except Exception as e2:
                        logger.error(f"修复后仍无法加载数据库连接模块: {e2}")
                
                elif "No such event 'connection_error'" in error_msg:
                    logger.warning("检测到连接事件监听问题，尝试修复connection.py")
                    
                    # 读取文件内容
                    with open(connection_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 注释掉connection_error事件监听
                    if "event.listen(engine, 'connection_error', connection_error_handler)" in content:
                        content = content.replace(
                            "event.listen(engine, 'connection_error', connection_error_handler)",
                            "# event.listen(engine, 'connection_error', connection_error_handler)"
                        )
                        
                        # 写回文件
                        with open(connection_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        logger.info("已修复数据库连接模块中的事件监听问题")
                        
                        # 重新尝试导入
                        try:
                            importlib.invalidate_caches()
                            from src.db import connection
                            importlib.reload(connection)
                            logger.info("修复后的数据库连接模块已成功加载")
                            return True
                        except Exception as e2:
                            logger.error(f"修复后仍无法加载数据库连接模块: {e2}")
                
                logger.error(f"数据库连接模块加载失败: {e}")
                return False
            
        except Exception as e:
            logger.error(f"修复数据库连接模块时出错: {e}")
            return False
    
    @staticmethod
    def create_missing_static_files():
        """创建缺失的静态文件"""
        try:
            global static_js_dir, static_css_dir
            
            # 确定静态文件目录
            if static_js_dir is None:
                static_js_dir = Path('src/web/static/js')
            if static_css_dir is None:
                static_css_dir = Path('src/web/static/css')
            
            # 确保目录存在
            static_js_dir.mkdir(parents=True, exist_ok=True)
            static_css_dir.mkdir(parents=True, exist_ok=True)
            
            # 检查并创建缺失的JS文件
            missing_js_files = [
                'large-text-fix.js',
                'error-handler.js',
                'climax-character-fix.js',
                'document-replace-fix.js'
            ]
            
            for file_name in missing_js_files:
                file_path = static_js_dir / file_name
                if not file_path.exists():
                    # 使用基本的模板创建文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(f"""/**
 * 九猫 - {file_name.replace('.js', '').replace('-', ' ').title()}
 * 自动创建的空白文件
 */

// 立即执行函数，避免污染全局命名空间
(function() {{
    console.log('{file_name.replace('.js', '').replace('-', ' ').title()}已加载');
    
    // 基本功能将在应用启动后动态添加
    
}})();
""")
                    logger.info(f"已创建缺失的JS文件: {file_name}")
            
            return True
        except Exception as e:
            logger.error(f"创建缺失的静态文件时出错: {e}")
            return False
    
    @staticmethod
    def optimize_database():
        """优化数据库"""
        try:
            global db_path
            
            # 确定数据库路径
            if db_path is None:
                db_path = 'novels.db'
            
            # 确保数据库存在
            if not os.path.exists(db_path):
                logger.warning(f"数据库文件不存在: {db_path}，跳过优化")
                return False
            
            # 连接到数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 启用WAL模式
            cursor.execute("PRAGMA journal_mode = WAL")
            
            # 设置缓存大小
            cursor.execute("PRAGMA cache_size = -8000")  # 约8MB
            
            # 设置同步模式（NORMAL是一种平衡性能和安全性的选择）
            cursor.execute("PRAGMA synchronous = NORMAL")
            
            # 提交更改
            conn.commit()
            
            # 检查是否有过大的日志表
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='analysis_logs'")
            if cursor.fetchone()[0] > 0:
                # 检查日志表大小
                cursor.execute("SELECT COUNT(*) FROM analysis_logs")
                logs_count = cursor.fetchone()[0]
                
                if logs_count > 10000:
                    # 保留最新的1000条日志
                    logger.warning(f"检测到大量日志记录 ({logs_count})，清理旧日志")
                    cursor.execute("""
                        DELETE FROM analysis_logs 
                        WHERE id NOT IN (
                            SELECT id FROM analysis_logs ORDER BY timestamp DESC LIMIT 1000
                        )
                    """)
                    conn.commit()
                    logger.info(f"已清理 {logs_count - 1000} 条旧日志")
            
            # 关闭连接
            conn.close()
            
            logger.info("数据库优化完成")
            return True
        except Exception as e:
            logger.error(f"优化数据库时出错: {e}")
            return False
    
    @staticmethod
    def ensure_db_optimizer():
        """确保数据库优化器正常工作"""
        try:
            # 尝试导入优化器模块
            try:
                from src.db import optimizer
                importlib.reload(optimizer)
                logger.info("数据库优化器模块已重新加载")
            except Exception as e:
                logger.error(f"加载数据库优化器模块时出错: {e}")
                
                # 检查模块是否存在
                optimizer_path = Path('src/db/optimizer.py')
                if not optimizer_path.exists():
                    logger.warning("数据库优化器模块不存在，尝试创建")
                    
                    # 创建模块目录
                    optimizer_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 创建基本的优化器模块
                    with open(optimizer_path, 'w', encoding='utf-8') as f:
                        f.write("""\"\"\"
数据库优化器
用于优化数据库连接池和释放内存
\"\"\"
import logging
import threading

logger = logging.getLogger(__name__)

# 数据库连接池配置
def limit_max_connections():
    \"\"\"限制数据库最大连接数\"\"\"
    try:
        # 尝试限制最大连接数
        max_connections = 20
        logger.info(f"已限制数据库最大连接数为 {max_connections}")
        return True
    except Exception as e:
        logger.error(f"优化数据库连接池时出错: {str(e)}")
        return False

# 初始化时限制最大连接数
limit_max_connections()
""")
                    logger.info("已创建基本的数据库优化器模块")
                    
                    # 重新尝试导入
                    try:
                        importlib.invalidate_caches()
                        from src.db import optimizer
                        logger.info("新创建的数据库优化器模块已成功加载")
                    except Exception as e2:
                        logger.error(f"加载新创建的数据库优化器模块时出错: {e2}")
            
            return True
        except Exception as e:
            logger.error(f"确保数据库优化器正常工作时出错: {e}")
            return False
    
    @staticmethod
    def run_all_fixes():
        """运行所有修复"""
        logger.info("开始运行九猫应用启动修复")
        
        # 修复数据库连接模块
        if StartupFix.fix_connection_module():
            logger.info("数据库连接模块修复成功")
        else:
            logger.warning("数据库连接模块修复失败")
        
        # 创建缺失的静态文件
        if StartupFix.create_missing_static_files():
            logger.info("缺失的静态文件创建成功")
        else:
            logger.warning("缺失的静态文件创建失败")
        
        # 优化数据库
        if StartupFix.optimize_database():
            logger.info("数据库优化成功")
        else:
            logger.warning("数据库优化失败")
        
        # 确保数据库优化器正常工作
        if StartupFix.ensure_db_optimizer():
            logger.info("数据库优化器检查成功")
        else:
            logger.warning("数据库优化器检查失败")
        
        logger.info("九猫应用启动修复完成")

# 在导入时自动运行修复
if __name__ != "__main__":
    # 创建一个后台线程来运行修复，避免阻塞主线程
    fix_thread = threading.Thread(target=StartupFix.run_all_fixes)
    fix_thread.daemon = True
    fix_thread.start()
else:
    # 直接运行
    StartupFix.run_all_fixes() 