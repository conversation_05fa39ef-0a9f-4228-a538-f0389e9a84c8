{"name": "libnpmversion", "version": "3.0.4", "main": "lib/index.js", "files": ["bin/", "lib/"], "description": "library to do the things that 'npm version' does", "repository": {"type": "git", "url": "https://github.com/npm/cli.git", "directory": "workspaces/libnpmversion"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"lint": "eslint \"**/*.js\"", "test": "tap", "posttest": "npm run lint", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "tap": {"coverage-map": "map.js"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "dependencies": {"@npmcli/git": "^3.0.0", "@npmcli/run-script": "^3.0.0", "json-parse-even-better-errors": "^2.3.1", "proc-log": "^2.0.0", "semver": "^7.3.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}}