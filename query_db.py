import sqlite3
import os

# 获取当前目录
current_dir = os.getcwd()
print(f"当前目录: {current_dir}")

# 数据库路径
db_path = 'novels.db'
print(f"尝试连接数据库: {db_path}")

try:
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查询章纲分析数据
    print("\n查询章纲分析数据:")
    cursor.execute('SELECT id, chapter_id, dimension, length(reasoning_content) FROM chapter_analysis_results WHERE dimension="chapter_outline"')
    rows = cursor.fetchall()
    
    if rows:
        print(f"找到 {len(rows)} 条章纲分析数据:")
        for row in rows:
            print(f'ID: {row[0]}, 章节ID: {row[1]}, 维度: {row[2]}, 推理内容长度: {row[3] or 0}字符')
    else:
        print("未找到章纲分析数据")
    
    # 查询有推理内容的章纲分析数据
    print("\n查询有推理内容的章纲分析数据:")
    cursor.execute('SELECT id, chapter_id, dimension, length(reasoning_content) FROM chapter_analysis_results WHERE dimension="chapter_outline" AND reasoning_content IS NOT NULL AND length(reasoning_content) > 0')
    rows = cursor.fetchall()
    
    if rows:
        print(f"找到 {len(rows)} 条有推理内容的章纲分析数据:")
        for row in rows:
            print(f'ID: {row[0]}, 章节ID: {row[1]}, 维度: {row[2]}, 推理内容长度: {row[3]}字符')
    else:
        print("未找到有推理内容的章纲分析数据")
    
    # 查询章节信息
    print("\n查询章节信息:")
    cursor.execute('SELECT id, novel_id, chapter_number, title FROM chapters WHERE id IN (SELECT chapter_id FROM chapter_analysis_results WHERE dimension="chapter_outline")')
    chapters = cursor.fetchall()
    
    if chapters:
        print(f"找到 {len(chapters)} 条章节信息:")
        for chapter in chapters:
            print(f'章节ID: {chapter[0]}, 小说ID: {chapter[1]}, 章节编号: {chapter[2]}, 标题: {chapter[3]}')
    else:
        print("未找到相关章节信息")
    
    # 关闭连接
    conn.close()
    
except Exception as e:
    print(f"查询数据库时出错: {str(e)}")
