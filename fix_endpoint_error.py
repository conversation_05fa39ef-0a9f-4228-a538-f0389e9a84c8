"""
九猫小说分析系统 - 端点错误修复工具

此脚本用于修复"Could not build url for endpoint 'upload'. Did you mean 'upload_novel' instead?"错误。
通过在Flask应用中添加一个别名路由，将'upload'映射到'upload_novel'函数。
"""
import os
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('endpoint_fix.log')
    ]
)
logger = logging.getLogger(__name__)

def fix_endpoint_error():
    """修复端点错误"""
    logger.info("开始修复端点错误...")
    
    # 获取当前工作目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义app.py文件路径
    app_file_path = os.path.join(base_dir, 'src', 'web', 'app.py')
    
    if not os.path.exists(app_file_path):
        logger.error(f"找不到应用文件: {app_file_path}")
        return False
    
    # 读取app.py文件内容
    with open(app_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经有upload别名路由
    if "app.route('/upload', endpoint='upload')" in content:
        logger.info("端点别名已存在，无需修复")
        return True
    
    # 查找upload_novel函数定义
    upload_function_def = "@app.route('/upload', methods=['GET', 'POST'])\ndef upload_novel():"
    
    if upload_function_def not in content:
        logger.error("找不到upload_novel函数定义，无法自动修复")
        return False
    
    # 创建备份
    backup_path = app_file_path + '.bak'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    logger.info(f"已创建备份文件: {backup_path}")
    
    # 添加别名路由
    modified_content = content.replace(
        upload_function_def,
        f"@app.route('/upload', methods=['GET', 'POST'], endpoint='upload_novel')\<EMAIL>('/upload', endpoint='upload')\ndef upload_novel():"
    )
    
    # 写入修改后的内容
    with open(app_file_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    logger.info("已成功添加端点别名")
    return True

def fix_base_template():
    """修复base.html模板中的端点引用"""
    logger.info("检查base.html模板...")
    
    # 获取当前工作目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义base.html文件路径
    base_template_path = os.path.join(base_dir, 'src', 'web', 'templates', 'base.html')
    
    if not os.path.exists(base_template_path):
        logger.warning(f"找不到base.html模板: {base_template_path}")
        return False
    
    # 读取base.html文件内容
    with open(base_template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否有对'upload'端点的引用
    if "url_for('upload')" in content:
        # 创建备份
        backup_path = base_template_path + '.bak'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"已创建模板备份文件: {backup_path}")
        
        # 修改端点引用
        modified_content = content.replace("url_for('upload')", "url_for('upload_novel')")
        
        # 写入修改后的内容
        with open(base_template_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        logger.info("已修复base.html模板中的端点引用")
        return True
    else:
        logger.info("base.html模板中没有发现对'upload'端点的引用，无需修复")
        return True

def fix_index_template():
    """修复index.html模板中的端点引用"""
    logger.info("检查index.html模板...")
    
    # 获取当前工作目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义index.html文件路径
    index_template_path = os.path.join(base_dir, 'src', 'web', 'templates', 'index.html')
    
    if not os.path.exists(index_template_path):
        logger.warning(f"找不到index.html模板: {index_template_path}")
        return False
    
    # 读取index.html文件内容
    with open(index_template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否有对'upload'端点的引用
    if "url_for('upload')" in content:
        # 创建备份
        backup_path = index_template_path + '.bak'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"已创建模板备份文件: {backup_path}")
        
        # 修改端点引用
        modified_content = content.replace("url_for('upload')", "url_for('upload_novel')")
        
        # 写入修改后的内容
        with open(index_template_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        logger.info("已修复index.html模板中的端点引用")
        return True
    else:
        logger.info("index.html模板中没有发现对'upload'端点的引用，无需修复")
        return True

def check_novels_endpoint():
    """检查novels端点是否存在"""
    logger.info("检查novels端点...")
    
    # 获取当前工作目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义app.py文件路径
    app_file_path = os.path.join(base_dir, 'src', 'web', 'app.py')
    
    if not os.path.exists(app_file_path):
        logger.error(f"找不到应用文件: {app_file_path}")
        return False
    
    # 读取app.py文件内容
    with open(app_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否有novels端点定义
    if "@app.route('/novels')" in content or "def novels():" in content:
        logger.info("novels端点已存在")
        return True
    else:
        logger.warning("未找到novels端点定义，需要添加")
        
        # 创建备份
        backup_path = app_file_path + '.novels.bak'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"已创建备份文件: {backup_path}")
        
        # 在文件末尾添加novels端点定义
        novels_endpoint = """
@app.route('/novels')
def novels():
    """小说列表页面"""
    return redirect(url_for('index'))
"""
        
        # 写入修改后的内容
        with open(app_file_path, 'a', encoding='utf-8') as f:
            f.write(novels_endpoint)
        
        logger.info("已添加novels端点定义")
        return True

def check_analysis_endpoint():
    """检查analysis端点是否存在"""
    logger.info("检查analysis端点...")
    
    # 获取当前工作目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义app.py文件路径
    app_file_path = os.path.join(base_dir, 'src', 'web', 'app.py')
    
    if not os.path.exists(app_file_path):
        logger.error(f"找不到应用文件: {app_file_path}")
        return False
    
    # 读取app.py文件内容
    with open(app_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否有analysis端点定义
    if "@app.route('/analysis')" in content or "def analysis():" in content:
        logger.info("analysis端点已存在")
        return True
    else:
        logger.warning("未找到analysis端点定义，需要添加")
        
        # 创建备份
        backup_path = app_file_path + '.analysis.bak'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"已创建备份文件: {backup_path}")
        
        # 在文件末尾添加analysis端点定义
        analysis_endpoint = """
@app.route('/analysis')
def analysis():
    """分析结果页面"""
    return redirect(url_for('index'))
"""
        
        # 写入修改后的内容
        with open(app_file_path, 'a', encoding='utf-8') as f:
            f.write(analysis_endpoint)
        
        logger.info("已添加analysis端点定义")
        return True

if __name__ == "__main__":
    print("九猫小说分析系统 - 端点错误修复工具")
    print("=" * 50)
    print("此工具将修复'Could not build url for endpoint'错误")
    print()
    
    try:
        # 修复端点错误
        endpoint_fixed = fix_endpoint_error()
        
        # 修复模板中的端点引用
        base_fixed = fix_base_template()
        index_fixed = fix_index_template()
        
        # 检查并添加缺失的端点
        novels_fixed = check_novels_endpoint()
        analysis_fixed = check_analysis_endpoint()
        
        if endpoint_fixed and base_fixed and index_fixed and novels_fixed and analysis_fixed:
            print("所有端点问题已成功修复！")
            print("请重新启动九猫系统以应用更改。")
        else:
            print("部分端点问题已修复，但可能仍有一些问题。")
            print("请查看日志文件了解详情。")
    except Exception as e:
        logger.error(f"修复过程中出错: {str(e)}", exc_info=True)
        print(f"修复过程中出错: {str(e)}")
        print("详细错误信息已记录到日志文件")
    
    print()
    input("按Enter键退出...")
