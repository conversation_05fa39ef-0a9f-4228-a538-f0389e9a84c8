"""
九猫小说分析写作系统v3.5应用独立入口
"""
import os
import sys
import logging
import time
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_cors import CORS
from sqlalchemy.orm import Session
from sqlalchemy import func

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入配置
import config

# 导入数据库模型
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.api_log import ApiLog
from src.db.connection import Session, engine
from src.models.base import Base

# 导入API客户端
try:
    from src.api.deepseek_client import DeepSeekClient
    from src.api.analysis import NovelAnalyzer
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("无法导入DeepSeekClient或NovelAnalyzer，某些功能可能不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(config.LOG_DIR if hasattr(config, 'LOG_DIR') else 'logs', 'v3_5_app.log'))
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_CONTENT_LENGTH

# 启用CORS
CORS(app)

# 添加自定义模板过滤器
@app.template_filter('tojson_safe')
def tojson_safe(obj):
    import json
    from markupsafe import Markup
    if hasattr(obj, '__class__') and obj.__class__.__name__ == 'MetaData':
        try:
            if hasattr(obj, 'items') and callable(obj.items):
                obj = {k: v for k, v in obj.items()}
            else:
                obj = {}
        except Exception as e:
            logger.error(f"转换MetaData对象为字典时出错: {str(e)}")
            obj = {}
    def json_default(o):
        if hasattr(o, '__dict__'):
            return o.__dict__
        elif hasattr(o, 'items') and callable(o.items):
            return {k: v for k, v in o.items()}
        else:
            return str(o)
    try:
        json_str = json.dumps(obj, default=json_default, ensure_ascii=False)
        return Markup(json_str)
    except Exception as e:
        logger.error(f"JSON序列化对象时出错: {str(e)}")
        return Markup("{}")

@app.template_filter('format_number')
def format_number(value):
    try:
        return "{:,}".format(int(value))
    except (ValueError, TypeError):
        return value

# 确保所有数据库表已创建
Base.metadata.create_all(engine)
logger.info("确认所有数据库表已创建")

# 运行数据库迁移
try:
    from src.db.migrations import run_migrations
    logger.info("开始执行数据库迁移...")
    run_migrations()
    logger.info("数据库迁移执行完成")
except Exception as e:
    logger.error(f"执行数据库迁移时出错: {str(e)}", exc_info=True)

# 只导入v3.5版本的路由
try:
    from src.web.routes.v3_5_routes import v3_5_bp
    from src.web.routes.v3_5_api import v3_5_api_bp
    logger.info("成功导入v3.5版本的路由蓝图")
except ImportError as e:
    logger.error(f"无法导入v3.5版本的路由蓝图: {str(e)}")
    raise

# 导入共享API路由模块
try:
    from src.web.routes.shared_api_routes import shared_api_bp
    logger.info("成功导入共享API路由模块")
except ImportError as e:
    logger.warning(f"无法导入共享API路由模块: {str(e)}")
    shared_api_bp = None

# 导入预设内容API路由蓝图（如v3.5需要）
try:
    from src.web.routes.preset_api import preset_api_bp
    logger.info("成功导入预设内容API路由蓝图")
except ImportError as e:
    logger.warning(f"无法导入预设内容API路由蓝图: {str(e)}")
    preset_api_bp = None

# 注册蓝图
app.register_blueprint(v3_5_bp, url_prefix='/v3.5')
app.register_blueprint(v3_5_api_bp, url_prefix='/api')
if preset_api_bp:
    app.register_blueprint(preset_api_bp)
    logger.info("已注册预设内容API路由蓝图")
if shared_api_bp:
    app.register_blueprint(shared_api_bp)
    logger.info("已注册共享API路由模块")

# 添加全局缓存控制
@app.after_request
def add_cache_control(response):
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

# 主页路由
@app.route('/')
def index():
    return redirect(url_for('v3_5.index'))

@app.route('/novels')
def novels():
    return redirect(url_for('v3_5.novels'))

@app.route('/upload')
def upload_novel():
    return redirect(url_for('v3_5.upload_novel'))

@app.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    return redirect(url_for('v3_5.view_novel', novel_id=novel_id))

@app.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    return redirect(url_for('v3_5.analysis', novel_id=novel_id, dimension=dimension))

@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>')
def view_chapter(novel_id, chapter_id):
    return redirect(url_for('v3_5.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

@app.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def chapter_analysis(novel_id, chapter_id, dimension):
    return redirect(url_for('v3_5.chapter_analysis', novel_id=novel_id, chapter_id=chapter_id, dimension=dimension))

@app.route('/novel/<int:novel_id>/chapters/summary')
def chapters_summary(novel_id):
    return redirect(url_for('v3_5.chapters_summary', novel_id=novel_id))

@app.route('/console')
def console_redirect():
    return redirect(url_for('v3_5.console'))

@app.route('/v3.5/console')
def v3_5_console():
    return redirect(url_for('v3_5.console'))

@app.errorhandler(404)
def not_found(error):
    if request.path.startswith('/v3.5/api'):
        return jsonify({'success': False, 'error': '接口不存在'}), 404
    return render_template('v3.5/error.html', error_code=404, error_message='页面未找到'), 404

@app.errorhandler(500)
def internal_server_error(e):
    logger.error(f"500错误: {str(e)}", exc_info=True)
    return render_template('v3.5/error.html', error_code=500, error_message='服务器内部错误'), 500

if __name__ == '__main__':
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=False,
        threaded=True,
        use_reloader=False
    )