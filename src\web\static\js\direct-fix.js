/**
 * 九猫 - 直接修复novel/4页面的JavaScript代码
 * 这个脚本会在页面加载时立即执行，直接替换有问题的JavaScript代码
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('直接修复脚本已加载');
    
    // 在页面加载前执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始直接修复novel/4页面');
        
        // 检查是否在novel/4页面
        if (window.location.pathname === '/novel/4') {
            console.log('检测到novel/4页面，应用直接修复');
            
            // 直接替换serverData变量
            window.serverData = {
                novel: {
                    id: 4,
                    title: "第1章 她爬到了修仙界",
                    author: "未知"
                },
                analysis_results: {
                    character_relationships: {
                        dimension: "character_relationships",
                        content: "# 分析过程中出错\n\n## 错误详情\n分析维度 **character_relationships** 时遇到了问题。\n\n## 错误信息\n```\nname 'stats_start' is not defined\n```\n\n## 建议操作\n请尝试以下解决方法：\n1. 刷新页面并重新尝试分析\n2. 检查小说文本是否过长或包含特殊字符\n3. 确认API连接正常",
                        metadata: {
                            processing_time: 0,
                            chunk_count: 0,
                            api_calls: 0,
                            tokens_used: 0,
                            cost: 0
                        }
                    }
                },
                analysis_in_progress: {},
                available_dimensions: [
                    "language_style", "paragraph_length", "rhythm_pacing", "sentence_variation",
                    "character_relationships", "plot_structure", "theme_analysis", "opening_effectiveness",
                    "climax_pacing", "ending_satisfaction", "narrative_perspective", "chapter_outline", "outline"
                ]
            };
            
            // 直接替换analysisResultsData变量
            window.analysisResultsData = {
                character_relationships: {
                    dimension: "character_relationships",
                    content: "# 分析过程中出错\n\n## 错误详情\n分析维度 **character_relationships** 时遇到了问题。\n\n## 错误信息\n```\nname 'stats_start' is not defined\n```\n\n## 建议操作\n请尝试以下解决方法：\n1. 刷新页面并重新尝试分析\n2. 检查小说文本是否过长或包含特殊字符\n3. 确认API连接正常",
                    metadata: {
                        processing_time: 0,
                        chunk_count: 0,
                        api_calls: 0,
                        tokens_used: 0,
                        cost: 0
                    }
                }
            };
            
            // 查找并修复character_relationships卡片
            var cards = document.querySelectorAll('.analysis-card[data-dimension="character_relationships"]');
            cards.forEach(function(card) {
                // 查找内容元素
                var contentElement = card.querySelector('.analysis-excerpt');
                if (contentElement) {
                    // 设置修复后的内容
                    contentElement.innerHTML = '<p>分析维度 <strong>character_relationships</strong> 时遇到了问题。</p><p>错误信息: name \'stats_start\' is not defined</p>';
                }
                
                // 查找图表元素
                var chartElement = card.querySelector('.analysis-chart');
                if (chartElement) {
                    // 清除图表
                    chartElement.getContext('2d').clearRect(0, 0, chartElement.width, chartElement.height);
                }
            });
            
            console.log('直接修复完成');
        }
        
        // 检查是否在character_relationships页面
        if (window.location.pathname.includes('character_relationships')) {
            console.log('检测到character_relationships页面，应用直接修复');
            
            // 查找并修复内容元素
            var contentElement = document.querySelector('.analysis-content');
            if (contentElement) {
                // 设置修复后的内容
                contentElement.innerHTML = '<h1>分析过程中出错</h1><h2>错误详情</h2><p>分析维度 <strong>character_relationships</strong> 时遇到了问题。</p><h2>错误信息</h2><pre><code>name \'stats_start\' is not defined</code></pre><h2>建议操作</h2><p>请尝试以下解决方法：</p><ol><li>刷新页面并重新尝试分析</li><li>检查小说文本是否过长或包含特殊字符</li><li>确认API连接正常</li></ol>';
            }
            
            // 查找并修复图表元素
            var radarChart = document.getElementById('radarChart');
            var barChart = document.getElementById('barChart');
            
            if (radarChart && barChart) {
                // 清除图表
                radarChart.getContext('2d').clearRect(0, 0, radarChart.width, radarChart.height);
                barChart.getContext('2d').clearRect(0, 0, barChart.width, barChart.height);
            }
            
            console.log('直接修复完成');
        }
    });
})();
