{% extends "v3/base.html" %}

{% block title %}预设内容详情 - 九猫{% endblock %}

{% block styles %}
<style>
    .preset-content {
        white-space: pre-wrap;
        font-family: 'Courier New', Courier, monospace;
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        border: 1px solid #dee2e6;
        max-height: 70vh;
        overflow-y: auto;
    }

    .markdown-content h1 {
        font-size: 1.8rem;
        margin-top: 1.5rem;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .markdown-content h2 {
        font-size: 1.5rem;
        margin-top: 1.2rem;
        margin-bottom: 0.8rem;
        padding-bottom: 0.3rem;
        border-bottom: 1px solid #e9ecef;
    }

    .markdown-content h3 {
        font-size: 1.3rem;
        margin-top: 1rem;
        margin-bottom: 0.6rem;
        color: #0056b3;
    }

    .markdown-content h4, .markdown-content h5, .markdown-content h6 {
        font-size: 1.1rem;
        margin-top: 0.8rem;
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .markdown-content p {
        margin-bottom: 0.8rem;
    }

    .markdown-content ul, .markdown-content ol {
        margin-bottom: 1rem;
        padding-left: 2rem;
    }

    .markdown-content blockquote {
        border-left: 4px solid #ced4da;
        padding-left: 1rem;
        margin-left: 0;
        color: #6c757d;
    }

    .markdown-content code {
        background-color: #f1f3f5;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Courier New', Courier, monospace;
    }

    .markdown-content pre {
        background-color: #f1f3f5;
        padding: 1rem;
        border-radius: 5px;
        overflow-x: auto;
        margin-bottom: 1rem;
    }

    .markdown-content pre code {
        background-color: transparent;
        padding: 0;
    }

    .markdown-content table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
    }

    .markdown-content th, .markdown-content td {
        border: 1px solid #dee2e6;
        padding: 0.5rem;
    }

    .markdown-content th {
        background-color: #f8f9fa;
    }

    /* 预设模板特定样式 */
    .template-section {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .template-section h3 {
        color: #0056b3;
        border-bottom: 1px dashed #adb5bd;
        padding-bottom: 5px;
        margin-bottom: 15px;
    }

    .template-section h4 {
        color: #495057;
        margin-top: 15px;
        font-weight: 600;
    }

    .template-item {
        margin-bottom: 5px;
    }

    .application-guide {
        background-color: #e8f4f8;
        border-left: 4px solid #17a2b8;
        padding: 15px;
        margin-top: 20px;
        border-radius: 0 5px 5px 0;
    }

    .application-guide h2 {
        color: #17a2b8;
        border-bottom: none;
    }

    .application-guide ol {
        margin-bottom: 0;
    }

    /* 添加打印友好样式 */
    @media print {
        .preset-content {
            max-height: none;
            overflow: visible;
            border: none;
        }

        .card {
            border: none;
            box-shadow: none;
        }

        .card-header, .btn {
            display: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="mb-0">{{ preset.title }}</h1>
                    <div>
                        <a href="{{ url_for('v3.console') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回
                        </a>
                        <a href="#" class="btn btn-outline-primary" id="editPresetBtn" data-preset-id="{{ preset.id }}">
                            <i class="fas fa-edit me-1"></i>编辑
                        </a>
                        <button class="btn btn-outline-success" id="printPresetBtn">
                            <i class="fas fa-print me-1"></i>打印
                        </button>
                        <button class="btn btn-outline-info" id="downloadPresetBtn">
                            <i class="fas fa-download me-1"></i>导出
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <p><strong>分类：</strong><span class="badge bg-primary">{{ preset.category }}</span></p>
                            </div>
                            <div class="col-md-3">
                                <p><strong>创建时间：</strong>{{ preset.created_at }}</p>
                            </div>
                            <div class="col-md-3">
                                <p><strong>更新时间：</strong>{{ preset.updated_at }}</p>
                            </div>
                            <div class="col-md-3">
                                {% if preset.show_template_button %}
                                <a href="{{ url_for('v3.view_preset_templates', preset_id=preset.id) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-expand me-1"></i>展开查看
                                </a>
                                <a href="{{ url_for('v3.view_preset_templates', preset_id=preset.id) }}?force_refresh=true&timestamp={{ now }}" class="btn btn-outline-danger ms-2">
                                    <i class="fas fa-sync-alt me-1"></i>强制刷新
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info" id="knowledgeBaseAlert" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>知识库内容已转换为预设模板格式</strong>
                        <p class="mb-0 mt-1">系统已将知识库内容转换为预设模板格式，您可以直接使用或根据需要进行修改。</p>
                    </div>

                    <h2>内容</h2>
                    <div class="preset-content markdown-content" id="presetContent">
                        {{ preset.content|safe }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 获取预设类别
        const presetCategory = "{{ preset.category }}";
        const presetTitle = "{{ preset.title }}";

        // 将Markdown内容转换为HTML
        const presetContent = document.getElementById('presetContent');
        let processedContent = '';

        if (presetContent) {
            const markdownContent = presetContent.textContent;

            // 如果是知识库类型，转换为预设模板格式
            if (presetCategory === 'knowledge_base') {
                // 显示知识库转换提示
                document.getElementById('knowledgeBaseAlert').style.display = 'block';

                // 转换知识库内容为预设模板
                processedContent = convertKnowledgeBaseToTemplate(markdownContent);
                presetContent.innerHTML = marked.parse(processedContent);

                // 添加样式类
                applyTemplateStyles();
            } else {
                // 其他类型直接渲染
                processedContent = markdownContent;
                presetContent.innerHTML = marked.parse(markdownContent);
            }
        }

        // 编辑按钮点击事件
        document.getElementById('editPresetBtn').addEventListener('click', function(e) {
            e.preventDefault();
            const presetId = this.getAttribute('data-preset-id');

            // 跳转到控制台页面并选中该预设
            window.location.href = `/v3/console?preset=${presetId}`;
        });

        // 打印按钮点击事件
        document.getElementById('printPresetBtn').addEventListener('click', function(e) {
            e.preventDefault();
            window.print();
        });

        // 导出按钮点击事件
        document.getElementById('downloadPresetBtn').addEventListener('click', function(e) {
            e.preventDefault();

            // 创建Blob对象
            const blob = new Blob([processedContent], { type: 'text/markdown' });

            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${presetTitle}.md`;

            // 触发下载
            document.body.appendChild(a);
            a.click();

            // 清理
            setTimeout(function() {
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            }, 0);
        });

        // 应用模板样式
        function applyTemplateStyles() {
            // 为预设模板部分添加样式
            const templateSection = document.querySelector('#presetContent h2:nth-of-type(3)');
            if (templateSection) {
                const templateContent = templateSection.nextElementSibling;
                const allElements = [];

                // 收集所有需要处理的元素
                let currentElement = templateSection.nextElementSibling;
                while (currentElement && currentElement.tagName !== 'H2') {
                    allElements.push(currentElement);
                    currentElement = currentElement.nextElementSibling;
                }

                // 创建容器并添加样式
                const container = document.createElement('div');
                container.className = 'template-section';

                // 将元素移动到容器中
                templateSection.after(container);
                allElements.forEach(el => container.appendChild(el));
            }

            // 为应用指南部分添加样式
            const applicationGuide = document.querySelector('#presetContent h2:last-of-type');
            if (applicationGuide && applicationGuide.textContent.includes('应用指南')) {
                const guideContainer = document.createElement('div');
                guideContainer.className = 'application-guide';

                // 收集所有需要处理的元素
                const guideElements = [];
                let currentElement = applicationGuide;
                while (currentElement) {
                    guideElements.push(currentElement);
                    currentElement = currentElement.nextElementSibling;
                }

                // 创建容器并添加样式
                applicationGuide.before(guideContainer);
                guideElements.forEach(el => guideContainer.appendChild(el));
            }
        }

        /**
         * 将知识库内容转换为预设模板格式
         * @param {string} content 知识库内容
         * @returns {string} 格式化后的预设模板内容
         */
        function convertKnowledgeBaseToTemplate(content) {
            // 提取知识库标题
            const titleMatch = content.match(/^#\s+(.+?)(?:\r?\n|$)/);
            const title = titleMatch ? titleMatch[1] : '知识库内容';

            // 构建预设模板
            let template = `# ${title} - 预设模板\n\n`;

            // 添加基本信息部分
            template += `## 基本信息\n`;
            template += `- 类型：知识库\n`;
            template += `- 标题：${title}\n`;
            template += `- 创建时间：{{ preset.created_at }}\n\n`;

            // 添加内容概述部分
            template += `## 内容概述\n`;

            // 提取内容中的主要部分（跳过标题）
            const contentWithoutTitle = content.replace(/^#\s+.+?(?:\r?\n|$)/, '').trim();

            // 提取二级标题作为主要部分
            const sections = contentWithoutTitle.split(/^##\s+(.+?)(?:\r?\n|$)/m);

            // 如果找到了二级标题，按部分组织内容
            if (sections.length > 1) {
                // 第一个元素是标题前的内容，可能为空
                if (sections[0].trim()) {
                    template += sections[0].trim() + '\n\n';
                }

                // 处理每个部分
                for (let i = 1; i < sections.length; i += 2) {
                    if (i < sections.length - 1) {
                        const sectionTitle = sections[i];
                        const sectionContent = sections[i + 1].trim();

                        template += `### ${sectionTitle}\n${sectionContent}\n\n`;
                    }
                }
            } else {
                // 如果没有二级标题，直接添加内容
                template += contentWithoutTitle + '\n\n';
            }

            // 添加预设模板部分
            template += `## 预设模板\n\n`;

            // 根据内容类型添加不同的预设模板
            if (title.includes('人物') || content.includes('人物')) {
                template += `### 人物设定模板\n\n`;
                template += `#### 基本信息\n`;
                template += `- 姓名：\n`;
                template += `- 年龄：\n`;
                template += `- 性别：\n`;
                template += `- 外貌特征：\n`;
                template += `- 职业/身份：\n\n`;

                template += `#### 性格特点\n`;
                template += `- 主要性格：\n`;
                template += `- 优点：\n`;
                template += `- 缺点：\n`;
                template += `- 习惯/怪癖：\n\n`;

                template += `#### 背景故事\n`;
                template += `- 成长经历：\n`;
                template += `- 重要事件：\n`;
                template += `- 家庭关系：\n\n`;

                template += `#### 目标与动机\n`;
                template += `- 短期目标：\n`;
                template += `- 长期目标：\n`;
                template += `- 内在动机：\n`;
                template += `- 外在动机：\n\n`;

                template += `#### 关系网络\n`;
                template += `- 盟友/朋友：\n`;
                template += `- 敌人/对手：\n`;
                template += `- 恋爱关系：\n\n`;

                template += `#### 能力与技能\n`;
                template += `- 特殊能力：\n`;
                template += `- 专业技能：\n`;
                template += `- 弱点：\n\n`;

                template += `#### 其他细节\n`;
                template += `- 喜好：\n`;
                template += `- 厌恶：\n`;
                template += `- 座右铭/口头禅：\n`;
            } else if (title.includes('世界观') || content.includes('世界观')) {
                template += `### 世界观设定模板\n\n`;
                template += `#### 基本信息\n`;
                template += `- 世界名称：\n`;
                template += `- 时代背景：\n`;
                template += `- 地理环境：\n`;
                template += `- 主要种族/民族：\n\n`;

                template += `#### 社会结构\n`;
                template += `- 政治体系：\n`;
                template += `- 经济体系：\n`;
                template += `- 社会阶层：\n`;
                template += `- 主要组织/势力：\n\n`;

                template += `#### 文化与信仰\n`;
                template += `- 主要宗教：\n`;
                template += `- 文化特色：\n`;
                template += `- 重要节日/习俗：\n`;
                template += `- 艺术与文学：\n\n`;

                template += `#### 魔法/科技系统\n`;
                template += `- 系统规则：\n`;
                template += `- 能力来源：\n`;
                template += `- 限制条件：\n`;
                template += `- 常见应用：\n\n`;

                template += `#### 历史背景\n`;
                template += `- 重要历史事件：\n`;
                template += `- 历史人物：\n`;
                template += `- 当前冲突：\n\n`;

                template += `#### 地理区域\n`;
                template += `- 主要国家/地区：\n`;
                template += `- 重要城市：\n`;
                template += `- 特殊地点：\n\n`;

                template += `#### 其他元素\n`;
                template += `- 独特生物：\n`;
                template += `- 特殊物品：\n`;
                template += `- 世界法则：\n`;
            } else if (title.includes('情节') || content.includes('情节') || content.includes('剧情')) {
                template += `### 情节设计模板\n\n`;
                template += `#### 基本框架\n`;
                template += `- 故事类型：\n`;
                template += `- 主要情节线：\n`;
                template += `- 次要情节线：\n`;
                template += `- 时间跨度：\n\n`;

                template += `#### 开端设计\n`;
                template += `- 引入方式：\n`;
                template += `- 主要角色介绍：\n`;
                template += `- 初始冲突：\n`;
                template += `- 读者吸引点：\n\n`;

                template += `#### 发展阶段\n`;
                template += `- 主要事件：\n`;
                template += `- 角色成长：\n`;
                template += `- 冲突升级：\n`;
                template += `- 转折点：\n\n`;

                template += `#### 高潮设计\n`;
                template += `- 关键场景：\n`;
                template += `- 冲突解决：\n`;
                template += `- 情感高点：\n`;
                template += `- 节奏控制：\n\n`;

                template += `#### 结局设计\n`;
                template += `- 结局类型：\n`;
                template += `- 主要问题解决：\n`;
                template += `- 角色归宿：\n`;
                template += `- 留下的悬念：\n\n`;

                template += `#### 情节技巧\n`;
                template += `- 伏笔设置：\n`;
                template += `- 悬念创造：\n`;
                template += `- 情感调动：\n`;
                template += `- 节奏变化：\n\n`;

                template += `#### 主题表达\n`;
                template += `- 核心主题：\n`;
                template += `- 表达方式：\n`;
                template += `- 象征元素：\n`;
                template += `- 深层含义：\n`;
            } else {
                // 通用模板
                template += `### 通用预设模板\n\n`;
                template += `#### 基本要素\n`;
                template += `- 主题：\n`;
                template += `- 风格：\n`;
                template += `- 目标读者：\n`;
                template += `- 预期效果：\n\n`;

                template += `#### 内容结构\n`;
                template += `- 开头部分：\n`;
                template += `- 中间部分：\n`;
                template += `- 结尾部分：\n`;
                template += `- 重点内容：\n\n`;

                template += `#### 表达方式\n`;
                template += `- 语言风格：\n`;
                template += `- 叙述视角：\n`;
                template += `- 修辞手法：\n`;
                template += `- 情感基调：\n\n`;

                template += `#### 特色元素\n`;
                template += `- 独特卖点：\n`;
                template += `- 创新点：\n`;
                template += `- 传统元素：\n`;
                template += `- 融合方式：\n\n`;

                template += `#### 应用建议\n`;
                template += `- 适用场景：\n`;
                template += `- 注意事项：\n`;
                template += `- 拓展可能：\n`;
                template += `- 评估标准：\n`;
            }

            // 添加应用指南
            template += `## 应用指南\n\n`;
            template += `1. 本预设模板基于知识库内容生成，可根据实际需求调整\n`;
            template += `2. 使用时可选择性填充相关部分，不必拘泥于模板格式\n`;
            template += `3. 建议结合参考蓝本的分析结果，进一步丰富预设内容\n`;
            template += `4. 预设模板可导出为Markdown文件，方便离线使用和编辑\n`;

            return template;
        }
    });
</script>
{% endblock %}
