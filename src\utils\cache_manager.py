"""
九猫系统缓存管理模块
将所有缓存数据保存到外部存储，减轻系统内存压力
"""
import os
import time
import json
import pickle
import logging
import threading
import hashlib
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Tuple

logger = logging.getLogger(__name__)

# 外部存储根目录
EXTERNAL_STORAGE_ROOT = os.environ.get('EXTERNAL_STORAGE_ROOT', 'E:\\艹，又来一次\\九猫')

# 缓存目录
CACHE_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "cache")
JSON_CACHE_DIR = os.path.join(CACHE_DIR, "json")
PICKLE_CACHE_DIR = os.path.join(CACHE_DIR, "pickle")
TEXT_CACHE_DIR = os.path.join(CACHE_DIR, "text")

# 确保目录存在
for directory in [CACHE_DIR, JSON_CACHE_DIR, PICKLE_CACHE_DIR, TEXT_CACHE_DIR]:
    os.makedirs(directory, exist_ok=True)

class CacheManager:
    """缓存管理器，将缓存数据保存到外部存储"""
    
    def __init__(self, default_ttl=86400):
        """
        初始化缓存管理器
        
        Args:
            default_ttl: 默认缓存过期时间（秒）
        """
        self.default_ttl = default_ttl
        self.memory_cache = {}  # 内存缓存（仅用于频繁访问的小型数据）
        self.memory_cache_size = 0  # 内存缓存大小（字节）
        self.max_memory_cache_size = 10 * 1024 * 1024  # 最大内存缓存大小（10MB）
        self.lock = threading.RLock()  # 线程锁
    
    def _get_cache_key(self, key: str) -> str:
        """
        获取缓存键
        
        Args:
            key: 原始键
        
        Returns:
            处理后的缓存键
        """
        # 如果键已经是MD5格式，直接返回
        if len(key) == 32 and all(c in '0123456789abcdef' for c in key.lower()):
            return key.lower()
        
        # 否则计算MD5哈希
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    
    def _get_cache_path(self, key: str, cache_type: str) -> str:
        """
        获取缓存文件路径
        
        Args:
            key: 缓存键
            cache_type: 缓存类型（json, pickle, text）
        
        Returns:
            缓存文件路径
        """
        cache_key = self._get_cache_key(key)
        
        # 根据缓存类型选择目录
        if cache_type == 'json':
            cache_dir = JSON_CACHE_DIR
            extension = '.json'
        elif cache_type == 'pickle':
            cache_dir = PICKLE_CACHE_DIR
            extension = '.pkl'
        elif cache_type == 'text':
            cache_dir = TEXT_CACHE_DIR
            extension = '.txt'
        else:
            raise ValueError(f"不支持的缓存类型: {cache_type}")
        
        # 创建子目录（使用键的前两个字符）
        subdir = os.path.join(cache_dir, cache_key[:2])
        os.makedirs(subdir, exist_ok=True)
        
        # 返回完整路径
        return os.path.join(subdir, f"{cache_key}{extension}")
    
    def _is_cache_valid(self, cache_path: str, ttl: int) -> bool:
        """
        检查缓存是否有效
        
        Args:
            cache_path: 缓存文件路径
            ttl: 过期时间（秒）
        
        Returns:
            缓存是否有效
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(cache_path):
                return False
            
            # 检查文件是否过期
            file_time = os.path.getmtime(cache_path)
            current_time = time.time()
            
            return current_time - file_time <= ttl
        
        except Exception as e:
            logger.error(f"检查缓存有效性时出错: {str(e)}")
            return False
    
    def _estimate_size(self, data: Any) -> int:
        """
        估计数据大小
        
        Args:
            data: 数据
        
        Returns:
            估计的数据大小（字节）
        """
        try:
            if isinstance(data, (str, bytes)):
                return len(data)
            elif isinstance(data, (int, float, bool, type(None))):
                return 8
            elif isinstance(data, (list, tuple)):
                return sum(self._estimate_size(item) for item in data)
            elif isinstance(data, dict):
                return sum(self._estimate_size(k) + self._estimate_size(v) for k, v in data.items())
            else:
                # 对于复杂对象，使用pickle估计大小
                return len(pickle.dumps(data))
        
        except Exception:
            # 如果无法估计大小，返回一个较大的值
            return 1024 * 1024  # 1MB
    
    def _cleanup_memory_cache(self) -> None:
        """清理内存缓存"""
        with self.lock:
            # 如果内存缓存大小未超过限制，不需要清理
            if self.memory_cache_size <= self.max_memory_cache_size:
                return
            
            # 按最后访问时间排序
            items = [(k, v['data'], v['last_access']) for k, v in self.memory_cache.items()]
            items.sort(key=lambda x: x[2])
            
            # 删除最旧的项，直到缓存大小低于限制
            for key, data, _ in items:
                if self.memory_cache_size <= self.max_memory_cache_size * 0.8:  # 清理到80%
                    break
                
                if key in self.memory_cache:
                    data_size = self._estimate_size(data)
                    del self.memory_cache[key]
                    self.memory_cache_size -= data_size
    
    def set(self, key: str, data: Any, ttl: Optional[int] = None, cache_type: str = 'json') -> bool:
        """
        设置缓存
        
        Args:
            key: 缓存键
            data: 缓存数据
            ttl: 过期时间（秒），如果为None则使用默认值
            cache_type: 缓存类型（json, pickle, text）
        
        Returns:
            是否设置成功
        """
        if ttl is None:
            ttl = self.default_ttl
        
        try:
            # 获取缓存文件路径
            cache_path = self._get_cache_path(key, cache_type)
            
            # 保存到文件
            if cache_type == 'json':
                with open(cache_path, 'w', encoding='utf-8') as f:
                    json.dump({
                        'data': data,
                        'timestamp': time.time(),
                        'ttl': ttl
                    }, f, ensure_ascii=False, indent=2)
            
            elif cache_type == 'pickle':
                with open(cache_path, 'wb') as f:
                    pickle.dump({
                        'data': data,
                        'timestamp': time.time(),
                        'ttl': ttl
                    }, f)
            
            elif cache_type == 'text':
                with open(cache_path, 'w', encoding='utf-8') as f:
                    f.write(str(data))
            
            else:
                raise ValueError(f"不支持的缓存类型: {cache_type}")
            
            # 同时保存到内存缓存（如果数据较小）
            data_size = self._estimate_size(data)
            if data_size <= 1024 * 1024:  # 只缓存小于1MB的数据
                with self.lock:
                    self.memory_cache[key] = {
                        'data': data,
                        'timestamp': time.time(),
                        'ttl': ttl,
                        'last_access': time.time()
                    }
                    self.memory_cache_size += data_size
                    
                    # 如果内存缓存过大，清理一部分
                    self._cleanup_memory_cache()
            
            return True
        
        except Exception as e:
            logger.error(f"设置缓存时出错: {str(e)}")
            return False
    
    def get(self, key: str, default: Any = None, cache_type: str = 'json') -> Any:
        """
        获取缓存
        
        Args:
            key: 缓存键
            default: 默认值（如果缓存不存在或已过期）
            cache_type: 缓存类型（json, pickle, text）
        
        Returns:
            缓存数据或默认值
        """
        try:
            # 先从内存缓存获取
            with self.lock:
                if key in self.memory_cache:
                    cache_item = self.memory_cache[key]
                    
                    # 检查是否过期
                    if time.time() - cache_item['timestamp'] <= cache_item['ttl']:
                        # 更新最后访问时间
                        cache_item['last_access'] = time.time()
                        return cache_item['data']
                    else:
                        # 已过期，从内存缓存中删除
                        data_size = self._estimate_size(cache_item['data'])
                        del self.memory_cache[key]
                        self.memory_cache_size -= data_size
            
            # 从文件缓存获取
            cache_path = self._get_cache_path(key, cache_type)
            
            # 检查文件是否存在
            if not os.path.exists(cache_path):
                return default
            
            # 读取缓存文件
            if cache_type == 'json':
                with open(cache_path, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                # 检查是否过期
                if time.time() - cache_data['timestamp'] <= cache_data['ttl']:
                    # 更新内存缓存
                    data = cache_data['data']
                    data_size = self._estimate_size(data)
                    
                    if data_size <= 1024 * 1024:  # 只缓存小于1MB的数据
                        with self.lock:
                            self.memory_cache[key] = {
                                'data': data,
                                'timestamp': cache_data['timestamp'],
                                'ttl': cache_data['ttl'],
                                'last_access': time.time()
                            }
                            self.memory_cache_size += data_size
                            
                            # 如果内存缓存过大，清理一部分
                            self._cleanup_memory_cache()
                    
                    return data
                else:
                    # 已过期，删除缓存文件
                    os.remove(cache_path)
                    return default
            
            elif cache_type == 'pickle':
                with open(cache_path, 'rb') as f:
                    cache_data = pickle.load(f)
                
                # 检查是否过期
                if time.time() - cache_data['timestamp'] <= cache_data['ttl']:
                    # 更新内存缓存
                    data = cache_data['data']
                    data_size = self._estimate_size(data)
                    
                    if data_size <= 1024 * 1024:  # 只缓存小于1MB的数据
                        with self.lock:
                            self.memory_cache[key] = {
                                'data': data,
                                'timestamp': cache_data['timestamp'],
                                'ttl': cache_data['ttl'],
                                'last_access': time.time()
                            }
                            self.memory_cache_size += data_size
                            
                            # 如果内存缓存过大，清理一部分
                            self._cleanup_memory_cache()
                    
                    return data
                else:
                    # 已过期，删除缓存文件
                    os.remove(cache_path)
                    return default
            
            elif cache_type == 'text':
                # 检查是否过期
                if self._is_cache_valid(cache_path, self.default_ttl):
                    with open(cache_path, 'r', encoding='utf-8') as f:
                        data = f.read()
                    
                    # 更新内存缓存
                    data_size = self._estimate_size(data)
                    
                    if data_size <= 1024 * 1024:  # 只缓存小于1MB的数据
                        with self.lock:
                            self.memory_cache[key] = {
                                'data': data,
                                'timestamp': os.path.getmtime(cache_path),
                                'ttl': self.default_ttl,
                                'last_access': time.time()
                            }
                            self.memory_cache_size += data_size
                            
                            # 如果内存缓存过大，清理一部分
                            self._cleanup_memory_cache()
                    
                    return data
                else:
                    # 已过期，删除缓存文件
                    os.remove(cache_path)
                    return default
            
            else:
                raise ValueError(f"不支持的缓存类型: {cache_type}")
        
        except Exception as e:
            logger.error(f"获取缓存时出错: {str(e)}")
            return default
    
    def delete(self, key: str, cache_type: str = 'json') -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            cache_type: 缓存类型（json, pickle, text）
        
        Returns:
            是否删除成功
        """
        try:
            # 从内存缓存中删除
            with self.lock:
                if key in self.memory_cache:
                    data_size = self._estimate_size(self.memory_cache[key]['data'])
                    del self.memory_cache[key]
                    self.memory_cache_size -= data_size
            
            # 从文件缓存中删除
            cache_path = self._get_cache_path(key, cache_type)
            
            if os.path.exists(cache_path):
                os.remove(cache_path)
            
            return True
        
        except Exception as e:
            logger.error(f"删除缓存时出错: {str(e)}")
            return False
    
    def clear(self) -> bool:
        """
        清除所有缓存
        
        Returns:
            是否清除成功
        """
        try:
            # 清除内存缓存
            with self.lock:
                self.memory_cache.clear()
                self.memory_cache_size = 0
            
            # 清除文件缓存
            for cache_dir in [JSON_CACHE_DIR, PICKLE_CACHE_DIR, TEXT_CACHE_DIR]:
                for root, dirs, files in os.walk(cache_dir):
                    for file in files:
                        try:
                            os.remove(os.path.join(root, file))
                        except (PermissionError, OSError):
                            pass
            
            return True
        
        except Exception as e:
            logger.error(f"清除缓存时出错: {str(e)}")
            return False
    
    def cleanup(self, max_age: int = 86400) -> int:
        """
        清理过期缓存
        
        Args:
            max_age: 最大缓存时间（秒）
        
        Returns:
            清理的缓存数量
        """
        try:
            # 清理内存缓存
            with self.lock:
                current_time = time.time()
                keys_to_delete = []
                
                for key, cache_item in self.memory_cache.items():
                    if current_time - cache_item['timestamp'] > cache_item['ttl'] or \
                       current_time - cache_item['last_access'] > max_age:
                        keys_to_delete.append(key)
                
                for key in keys_to_delete:
                    data_size = self._estimate_size(self.memory_cache[key]['data'])
                    del self.memory_cache[key]
                    self.memory_cache_size -= data_size
            
            # 清理文件缓存
            cleaned_count = 0
            current_time = time.time()
            
            for cache_dir in [JSON_CACHE_DIR, PICKLE_CACHE_DIR, TEXT_CACHE_DIR]:
                for root, dirs, files in os.walk(cache_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        
                        try:
                            # 获取文件修改时间
                            file_time = os.path.getmtime(file_path)
                            
                            # 如果文件超过最大缓存时间，删除它
                            if current_time - file_time > max_age:
                                os.remove(file_path)
                                cleaned_count += 1
                        
                        except (PermissionError, OSError):
                            pass
            
            return cleaned_count
        
        except Exception as e:
            logger.error(f"清理缓存时出错: {str(e)}")
            return 0

# 创建全局缓存管理器实例
cache_manager = CacheManager()

# 导出便捷函数
def set_cache(key: str, data: Any, ttl: Optional[int] = None, cache_type: str = 'json') -> bool:
    """设置缓存"""
    return cache_manager.set(key, data, ttl, cache_type)

def get_cache(key: str, default: Any = None, cache_type: str = 'json') -> Any:
    """获取缓存"""
    return cache_manager.get(key, default, cache_type)

def delete_cache(key: str, cache_type: str = 'json') -> bool:
    """删除缓存"""
    return cache_manager.delete(key, cache_type)

def clear_cache() -> bool:
    """清除所有缓存"""
    return cache_manager.clear()

def cleanup_cache(max_age: int = 86400) -> int:
    """清理过期缓存"""
    return cache_manager.cleanup(max_age)
