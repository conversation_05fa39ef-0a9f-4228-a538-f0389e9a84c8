/**
 * 九猫 - Bootstrap Data属性解析修复脚本
 * 专门处理Bootstrap的data属性解析错误
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._bootstrapDataFixLoaded) {
        console.log('Bootstrap Data属性修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._bootstrapDataFixLoaded = true;
    
    console.log('Bootstrap Data属性修复脚本已加载 - 版本1.0.0');
    
    // 等待Bootstrap加载完成
    function waitForBootstrap() {
        // 检查Bootstrap是否已加载
        if (typeof bootstrap !== 'undefined') {
            fixBootstrapDataParser();
        } else {
            // 如果还没加载，等待100ms后再检查
            setTimeout(waitForBootstrap, 100);
        }
    }
    
    // 修复Bootstrap的数据解析函数
    function fixBootstrapDataParser() {
        try {
            // 找到Bootstrap的操作器模块
            const manipulatorModule = findManipulatorModule();
            
            if (manipulatorModule) {
                console.log('找到Bootstrap操作器模块，准备修复数据解析函数');
                
                // 保存原始的getDataAttribute函数
                const originalGetDataAttribute = manipulatorModule.getDataAttribute;
                
                // 重写getDataAttribute函数
                manipulatorModule.getDataAttribute = function(element, key) {
                    try {
                        // 调用原始函数
                        return originalGetDataAttribute(element, key);
                    } catch (error) {
                        // 如果发生错误，尝试手动解析
                        console.log('Bootstrap数据解析错误，尝试手动解析:', error.message);
                        
                        // 获取原始属性值
                        const attributeValue = element.getAttribute(`data-bs-${key}`);
                        
                        // 如果属性不存在，返回null
                        if (attributeValue === null || attributeValue === undefined) {
                            return null;
                        }
                        
                        // 手动解析属性值
                        return safeParseDataValue(attributeValue);
                    }
                };
                
                console.log('Bootstrap数据解析函数修复完成');
            } else {
                console.warn('未找到Bootstrap操作器模块，无法修复数据解析函数');
            }
        } catch (error) {
            console.error('修复Bootstrap数据解析函数时出错:', error);
        }
    }
    
    // 查找Bootstrap的操作器模块
    function findManipulatorModule() {
        // 如果Bootstrap已经加载
        if (typeof bootstrap !== 'undefined') {
            // 尝试查找操作器模块
            for (const key in bootstrap) {
                const module = bootstrap[key];
                
                // 检查是否是操作器模块
                if (module && 
                    typeof module === 'object' && 
                    typeof module.getDataAttribute === 'function' && 
                    typeof module.getDataAttributes === 'function') {
                    return module;
                }
            }
            
            // 如果在bootstrap对象中没找到，尝试在window对象中查找
            for (const key in window) {
                // 跳过一些常见的全局对象
                if (['document', 'window', 'location', 'history', 'localStorage', 'sessionStorage'].includes(key)) {
                    continue;
                }
                
                const obj = window[key];
                
                // 检查是否是操作器模块
                if (obj && 
                    typeof obj === 'object' && 
                    typeof obj.getDataAttribute === 'function' && 
                    typeof obj.getDataAttributes === 'function') {
                    return obj;
                }
            }
        }
        
        return null;
    }
    
    // 安全地解析数据属性值
    function safeParseDataValue(value) {
        // 如果值为null或undefined，返回null
        if (value === null || value === undefined) {
            return null;
        }
        
        // 转换布尔值
        if (value === 'true') return true;
        if (value === 'false') return false;
        
        // 转换数字
        if (value === Number(value).toString()) return Number(value);
        
        // 处理空字符串和null字符串
        if (value === '' || value === 'null') return null;
        
        // 如果不是字符串，直接返回
        if (typeof value !== 'string') return value;
        
        // 尝试解析JSON
        try {
            // 尝试解码URI组件并解析JSON
            return JSON.parse(decodeURIComponent(value));
        } catch (e) {
            try {
                // 如果解码失败，尝试直接解析
                return JSON.parse(value);
            } catch (e2) {
                // 如果解析失败，返回原始值
                return value;
            }
        }
    }
    
    // 启动修复过程
    waitForBootstrap();
    
    // 在页面完全加载后再次尝试修复
    window.addEventListener('load', function() {
        setTimeout(fixBootstrapDataParser, 1000);
    });
    
    console.log('Bootstrap Data属性修复脚本初始化完成');
})(); 