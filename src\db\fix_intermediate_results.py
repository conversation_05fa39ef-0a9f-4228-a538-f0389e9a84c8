"""
修复intermediate_results表，添加progress和metadata列
"""
import logging
from sqlalchemy import inspect

from src.db.connection import engine

logger = logging.getLogger(__name__)

def fix_intermediate_results_table():
    """
    检查并修复intermediate_results表，添加缺少的列
    """
    try:
        # 获取表的当前结构
        inspector = inspect(engine)

        # 检查表是否存在
        if 'intermediate_results' not in inspector.get_table_names():
            logger.warning("intermediate_results表不存在，无需修复")
            return

        # 获取表的列
        columns = [column['name'] for column in inspector.get_columns('intermediate_results')]

        # 检查并添加progress列
        if 'progress' not in columns:
            logger.info("添加progress列到intermediate_results表")
            engine.execute('ALTER TABLE intermediate_results ADD COLUMN progress INTEGER')

        # 检查并添加meta_data列
        if 'meta_data' not in columns:
            logger.info("添加meta_data列到intermediate_results表")
            engine.execute('ALTER TABLE intermediate_results ADD COLUMN meta_data JSON')

        logger.info("intermediate_results表修复完成")
    except Exception as e:
        logger.error(f"修复intermediate_results表时出错: {str(e)}", exc_info=True)
