"""
Configuration settings for the 九猫 (Nine Cats) novel analysis system.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

# 阿里云API配置
# 支持多个模型的API密钥配置

# DeepSeek R1 API配置
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY", "***********************************")
if deepseek_api_key.startswith("Bearer "):
    deepseek_api_key = deepseek_api_key[7:]  # 移除Bearer前缀
DEEPSEEK_API_KEY = deepseek_api_key
# 确保使用最新的API密钥
print(f"使用DeepSeek R1 API密钥: {DEEPSEEK_API_KEY[:6]}...")

# 通义千问-Plus-Latest API配置
qwen_api_key = os.getenv("QWEN_API_KEY", "sk-6f3b4c6ad9f64f78b22bed422c5d278d")
if qwen_api_key.startswith("Bearer "):
    qwen_api_key = qwen_api_key[7:]  # 移除Bearer前缀
QWEN_API_KEY = qwen_api_key

# 确保API基础URL正确 - 阿里云API
DASHSCOPE_API_BASE_URL = os.getenv("DASHSCOPE_API_BASE_URL", "https://dashscope.aliyuncs.com/api/v1")

# 默认模型配置
DEFAULT_MODEL = os.getenv("DEFAULT_MODEL", "deepseek-r1")  # 默认使用DeepSeek R1模型

# 支持的模型列表
SUPPORTED_MODELS = {
    "deepseek-r1": {
        "name": "DeepSeek R1",
        "api_key": DEEPSEEK_API_KEY,
        "endpoint": f"{DASHSCOPE_API_BASE_URL}/services/aigc/text-generation/generation",
        "max_tokens": 4000,
        "description": "阿里云DeepSeek R1模型，适合长文本分析"
    },
    "qwen-plus-latest": {
        "name": "通义千问-Plus-Latest",
        "api_key": QWEN_API_KEY,
        "endpoint": f"{DASHSCOPE_API_BASE_URL}/services/aigc/text-generation/generation",
        "max_tokens": 4000,
        "description": "阿里云通义千问-Plus-Latest模型，适合中文文本分析"
    }
}

# API调用频率限制设置
API_CALL_LIMIT_PER_HOUR = int(os.getenv("API_CALL_LIMIT_PER_HOUR", "100"))  # 每小时API调用限制
API_CALL_WINDOW_SECONDS = int(os.getenv("API_CALL_WINDOW_SECONDS", "3600"))  # API调用时间窗口（秒）
API_RATE_LIMIT_ENABLED = os.getenv("API_RATE_LIMIT_ENABLED", "True").lower() in ("true", "1", "t")  # 是否启用API调用频率限制

# API费用设置 - 阿里云DeepSeek R1 API计费规则
API_COST_INPUT_PER_1K_TOKENS = float(os.getenv("API_COST_INPUT_PER_1K_TOKENS", "0.004"))  # 输入：每千tokens约0.004元
API_COST_OUTPUT_PER_1K_TOKENS = float(os.getenv("API_COST_OUTPUT_PER_1K_TOKENS", "0.016"))  # 输出：每千tokens约0.016元

# Application settings
# 开启DEBUG模式，以便查看更多日志信息
# DEBUG = False  # 注释掉硬编码 DEBUG，使用环境变量控制模式
DEBUG = False  # 强制设置为False，以确保真实调用API而不是模拟分析
SECRET_KEY = os.getenv("SECRET_KEY", "dev-key-change-in-production")
PORT = int(os.getenv("PORT", "5001"))
HOST = os.getenv("HOST", "0.0.0.0")

# Database settings
DATABASE_URI = os.getenv("DATABASE_URI", "sqlite:///novels.db")

# Analysis settings
MAX_CHUNK_SIZE = int(os.getenv("MAX_CHUNK_SIZE", "8000"))  # Characters per chunk for analysis (increased from 4000)
OVERLAP_SIZE = int(os.getenv("OVERLAP_SIZE", "100"))  # Overlap between chunks for context (reduced from 200)
MAX_NOVEL_SIZE = int(os.getenv("MAX_NOVEL_SIZE", "3000000"))  # Maximum novel size in characters
MAX_PARALLEL_ANALYSES = int(os.getenv("MAX_PARALLEL_ANALYSES", "5"))  # Maximum number of parallel analyses (one per dimension)
MAX_CHUNK_WORKERS = int(os.getenv("MAX_CHUNK_WORKERS", "5"))  # Maximum number of parallel workers per dimension
PARALLEL_ANALYSIS_ENABLED = os.getenv("PARALLEL_ANALYSIS_ENABLED", "True").lower() in ("true", "1", "t")  # 是否启用并行分析

# 不同维度的分块大小配置 - 优化API调用效率
DIMENSION_CHUNK_SIZES = {
    "language_style": 10000,  # 语言风格分析需要更大的上下文
    "rhythm_pacing": 10000,  # 节奏分析需要更大的上下文
    "structure": 12000,  # 结构分析需要更大的上下文
    "sentence_variation": 8000,  # 句式变化分析
    "paragraph_length": 8000,  # 段落长度分析
    "perspective_shifts": 10000,  # 视角转换分析需要更大的上下文
    "paragraph_flow": 10000,  # 段落流畅度分析需要更大的上下文
    "novel_characteristics": 12000,  # 小说特点分析需要更大的上下文
    "world_building": 12000,  # 世界构建分析需要更大的上下文
    "chapter_outline": 15000,  # 章节大纲分析需要更大的上下文
    "character_relationships": 15000,  # 人物关系分析需要更大的上下文
    "opening_effectiveness": 12000,  # 开篇效果分析
    "climax_pacing": 12000,  # 高潮节奏分析需要更大的上下文
    "default": 8000  # 默认值
}

# 不同维度的最大输出token配置 - 优化API调用效率
DIMENSION_MAX_TOKENS = {
    "language_style": 2500,  # 语言风格分析需要更详细的输出
    "rhythm_pacing": 2500,  # 节奏分析需要更详细的输出
    "structure": 3000,  # 结构分析需要更详细的输出
    "sentence_variation": 2000,  # 句式变化分析
    "paragraph_length": 2000,  # 段落长度分析
    "perspective_shifts": 2500,  # 视角转换分析
    "paragraph_flow": 2500,  # 段落流畅度分析
    "novel_characteristics": 3000,  # 小说特点分析需要更详细的输出
    "world_building": 3000,  # 世界构建分析需要更详细的输出
    "chapter_outline": 3500,  # 章节大纲分析需要更详细的输出
    "character_relationships": 3500,  # 人物关系分析需要更详细的输出
    "opening_effectiveness": 2500,  # 开篇效果分析
    "climax_pacing": 2500,  # 高潮节奏分析
    "combine_chapter_outline": 4000,  # 合并章节大纲分析需要更大的输出空间
    "combine_character_relationships": 4000,  # 合并人物关系分析需要更大的输出空间
    "comprehensive_report": 4000,  # 综合报告需要更大的输出空间
    "default": 2000  # 默认值
}

# 缓存设置
CACHE_ENABLED = os.getenv("CACHE_ENABLED", "True").lower() in ("true", "1", "t")  # 是否启用分析结果缓存
CACHE_VALID_DAYS = int(os.getenv("CACHE_VALID_DAYS", "7"))  # 缓存有效期（天）
FORCE_REFRESH_CACHE = os.getenv("FORCE_REFRESH_CACHE", "False").lower() in ("true", "1", "t")  # 是否强制刷新缓存

# 高级缓存设置 - 优化API调用效率
CACHE_INTERMEDIATE_RESULTS = os.getenv("CACHE_INTERMEDIATE_RESULTS", "True").lower() in ("true", "1", "t")  # 是否缓存中间分析结果
CACHE_REUSE_ACROSS_DIMENSIONS = os.getenv("CACHE_REUSE_ACROSS_DIMENSIONS", "True").lower() in ("true", "1", "t")  # 是否允许跨维度复用结果
INTERMEDIATE_CACHE_VALID_DAYS = int(os.getenv("INTERMEDIATE_CACHE_VALID_DAYS", "3"))  # 中间结果缓存有效期（天）

# Analysis dimensions
# Temporarily disabled dimensions (as per user request)
DISABLED_DIMENSIONS = [
    "rhythm_pacing",
    "structure",
    "sentence_variation",
    "paragraph_length",
    "perspective_shifts",
    "paragraph_flow",
    "novel_characteristics",
    "world_building",
    "chapter_outline",
    "character_relationships",
    "opening_effectiveness",
    "climax_pacing"
]

# Only keeping language_style active
ANALYSIS_DIMENSIONS = [
    "language_style"
]

# File upload settings
UPLOAD_FOLDER = os.getenv("UPLOAD_FOLDER", "uploads")
ALLOWED_EXTENSIONS = {"txt", "pdf", "docx", "epub"}
MAX_CONTENT_LENGTH = int(os.getenv("MAX_CONTENT_LENGTH", "50")) * 1024 * 1024  # 50MB
