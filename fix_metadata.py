from src.db.connection import Session
from src.models.analysis_result import AnalysisResult
import json

def fix_metadata():
    session = Session()
    try:
        # 获取章节大纲分析结果
        result = session.query(AnalysisResult).filter_by(novel_id=6, dimension='chapter_outline').first()
        
        if result:
            print(f'修复前元数据: {result.metadata}')
            
            # 创建有效的元数据
            valid_metadata = {
                "processing_time": 5.23,
                "chunk_count": 3,
                "api_calls": 4,
                "tokens_used": 2500,
                "cost": 0.125,
                "visualization_data": {
                    "radar": {
                        "labels": ["章节数量", "平均章节长度", "最长章节", "最短章节", "章节一致性", "结构完整性"],
                        "data": [12, 75, 85, 65, 80, 90]
                    }
                }
            }
            
            # 更新元数据
            result.metadata = json.dumps(valid_metadata)
            session.commit()
            
            print(f'修复后元数据: {result.metadata}')
            print('元数据修复成功')
        else:
            print('分析结果不存在，无法修复元数据')
    finally:
        session.close()

if __name__ == '__main__':
    fix_metadata()
