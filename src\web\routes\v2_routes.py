"""
九猫小说分析系统v2.0路由
"""
import os
import json
import logging
import time
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from sqlalchemy.orm import Session as SQLAlchemySession
from sqlalchemy import func, desc

from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.api_log import ApiLog
from src.db.connection import Session
from src.api.deepseek_client import DeepSeekClient
from src.api.analysis import NovelAnalyzer
from src.services.chapter_analysis_service import ChapterAnalysisService
from src.services.analysis_service import aggregate_chapter_analyses
import config

# 创建蓝图
v2_bp = Blueprint('v2', __name__)
logger = logging.getLogger(__name__)

# 从config.py导入分析维度配置
ANALYSIS_DIMENSIONS = config.ANALYSIS_DIMENSIONS

@v2_bp.route('/')
def index():
    """首页"""
    try:
        session = Session()
        try:
            # 获取最近的小说
            novels = session.query(Novel).order_by(Novel.created_at.desc()).limit(5).all()
            return render_template('v2/index.html', novels=novels)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载首页时出错: {str(e)}", exc_info=True)
        return render_template('v2/index.html', novels=[])

@v2_bp.route('/novels')
def novels():
    """小说列表页面"""
    try:
        session = Session()
        try:
            # 获取所有小说
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()
            return render_template('v2/novels.html', novels=novels)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"加载小说列表页面时出错: {str(e)}", exc_info=True)
        return render_template('v2/novels.html', novels=[])

@v2_bp.route('/upload', methods=['GET', 'POST'])
def upload_novel():
    """上传小说页面"""
    if request.method == 'GET':
        return render_template('v2/upload.html', dimensions=ANALYSIS_DIMENSIONS)

    try:
        title = request.form.get('title', '未命名小说')
        author = request.form.get('author', '')
        content = ''

        # 处理文件上传
        if 'file' in request.files and request.form.get('uploadType') == 'file':
            file = request.files['file']
            if file.filename:
                content = file.read().decode('utf-8', errors='ignore')

        # 处理文本粘贴
        elif 'content' in request.form and request.form.get('uploadType') == 'text':
            content = request.form['content']

        if not content:
            return render_template('v2/upload.html', error="请上传文件或粘贴文本内容", dimensions=ANALYSIS_DIMENSIONS)

        # 保存到数据库
        session = Session()
        try:
            novel = Novel(
                title=title,
                author=author,
                content=content
            )

            # 字数会在Novel的__init__方法中自动计算
            session.add(novel)
            session.commit()

            # 检查是否需要自动分析
            auto_analyze = request.form.get('autoAnalyze') == 'on'
            if auto_analyze:
                # 获取选中的分析维度
                dimensions = request.form.getlist('dimensions')
                if dimensions:
                    # 启动分析
                    # 导入单维度分析模块
                    from src.api.analyze_dimension import analyze_dimension

                    # 逐个维度分析
                    for dimension in dimensions:
                        analyze_dimension(novel, dimension)

                    flash(f"小说《{title}》已上传并开始分析", "success")
                else:
                    flash(f"小说《{title}》已上传，但未选择分析维度", "warning")
            else:
                flash(f"小说《{title}》已上传", "success")

            # 重定向到小说详情页
            return redirect(url_for('v2.view_novel', novel_id=novel.id))
        finally:
            session.close()
    except Exception as e:
        logger.error(f"上传小说时出错: {str(e)}", exc_info=True)
        return render_template('v2/upload.html', error=f"上传失败: {str(e)}", dimensions=ANALYSIS_DIMENSIONS)

@v2_bp.route('/novel/<int:novel_id>')
def view_novel(novel_id):
    """查看小说详情页面"""
    try:
        session = Session()
        try:
            # 获取小说 - 使用get()方法，确保对象绑定到会话
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v2.novels'))

            # 创建一个字典来存储小说信息，避免在模板中直接使用数据库对象
            novel_data = {
                'id': novel.id,
                'title': novel.title,
                'author': novel.author,
                'word_count': novel.word_count,
                'content': novel.content,
                'is_analyzed': novel.is_analyzed,
                'created_at': novel.created_at
            }

            # 获取分析结果
            analysis_results = {}
            available_dimensions = []

            results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            for result in results:
                analysis_results[result.dimension] = result
                available_dimensions.append(result.dimension)

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            # 如果没有章节，尝试自动分割
            if not chapters:
                try:
                    # 使用章节分析服务分割章节
                    new_chapters = ChapterAnalysisService.split_novel_into_chapters(novel_id)

                    # 重新从数据库加载章节，确保它们绑定到当前会话
                    if new_chapters:
                        chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()
                except Exception as e:
                    logger.error(f"自动分割章节时出错: {str(e)}", exc_info=True)
                    chapters = []

            # 获取章节分析结果
            chapter_analysis_results = {}
            if chapters:
                chapter_ids = [chapter.id for chapter in chapters]
                try:
                    # 使用ORM方式查询，避免直接查询可能不存在的列
                    chapter_results = session.query(ChapterAnalysisResult).filter(
                        ChapterAnalysisResult.chapter_id.in_(chapter_ids)
                    ).all()

                    for result in chapter_results:
                        if result.chapter_id not in chapter_analysis_results:
                            chapter_analysis_results[result.chapter_id] = {}
                        chapter_analysis_results[result.chapter_id][result.dimension] = result
                except Exception as e:
                    logger.error(f"获取章节分析结果时出错: {str(e)}", exc_info=True)
                    # 如果出错，返回空结果
                    chapter_analysis_results = {}

            return render_template(
                'v2/novel_detail.html',
                novel=novel_data,  # 使用字典而不是数据库对象
                analysis_results=analysis_results,
                available_dimensions=available_dimensions,
                dimensions=ANALYSIS_DIMENSIONS,
                chapters=chapters,
                chapter_analysis_results=chapter_analysis_results
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看小说详情页面时出错: {str(e)}", exc_info=True)
        flash(f"加载小说详情失败: {str(e)}", "danger")
        return redirect(url_for('v2.novels'))

@v2_bp.route('/novel/<int:novel_id>/analysis/<dimension>')
def analysis(novel_id, dimension):
    """查看分析结果页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v2.novels'))

            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                flash(f"未找到{dimension}维度的分析结果", "warning")
                return redirect(url_for('v2.view_novel', novel_id=novel_id))

            # 获取维度信息
            dimension_info = next((d for d in ANALYSIS_DIMENSIONS if d["key"] == dimension), None)

            # 特殊处理章纲分析和大纲分析
            if dimension == 'chapter_outline':
                return render_template(
                    'v2/chapter_outline_fix.html',
                    novel=novel,
                    analysis_result=result
                )
            elif dimension == 'outline_analysis':
                return render_template(
                    'v2/outline_analysis_fix.html',
                    novel=novel,
                    analysis_result=result
                )
            else:
                return render_template(
                    'v2/analysis_detail.html',
                    novel=novel,
                    result=result,
                    dimension=dimension,
                    dimension_info=dimension_info
                )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看分析结果页面时出错: {str(e)}", exc_info=True)
        flash(f"加载分析结果失败: {str(e)}", "danger")
        return redirect(url_for('v2.view_novel', novel_id=novel_id))

@v2_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>')
def view_chapter(novel_id, chapter_id):
    """查看章节详情页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v2.novels'))

            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter or chapter.novel_id != novel_id:
                flash("未找到指定章节", "danger")
                return redirect(url_for('v2.view_novel', novel_id=novel_id))

            # 获取章节分析结果
            analysis_results = {}
            available_dimensions = []

            try:
                # 使用更安全的查询方式，只查询确定存在的列
                results = session.query(
                    ChapterAnalysisResult.id,
                    ChapterAnalysisResult.chapter_id,
                    ChapterAnalysisResult.novel_id,
                    ChapterAnalysisResult.dimension,
                    ChapterAnalysisResult.content,
                    ChapterAnalysisResult.analysis_metadata,
                    ChapterAnalysisResult.analysis_logs,
                    ChapterAnalysisResult.created_at,
                    ChapterAnalysisResult.updated_at
                ).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter_id
                ).all()

                for result_tuple in results:
                    # 创建一个新的ChapterAnalysisResult对象
                    result = ChapterAnalysisResult(
                        chapter_id=result_tuple[1],
                        novel_id=result_tuple[2],
                        dimension=result_tuple[3],
                        content=result_tuple[4],
                        metadata=result_tuple[5],
                        logs=result_tuple[6]
                    )
                    # 手动设置ID和时间戳
                    result.id = result_tuple[0]
                    result.created_at = result_tuple[7]
                    result.updated_at = result_tuple[8]

                    # 尝试从analysis_metadata中获取reasoning_content
                    if result.analysis_metadata and 'reasoning_content' in result.analysis_metadata:
                        result.reasoning_content = result.analysis_metadata['reasoning_content']

                    analysis_results[result.dimension] = result
                    available_dimensions.append(result.dimension)
            except Exception as e:
                logger.error(f"获取章节分析结果时出错: {str(e)}", exc_info=True)
                # 如果出错，返回空结果
                analysis_results = {}
                available_dimensions = []

            return render_template(
                'v2/chapter_detail.html',
                novel=novel,
                chapter=chapter,
                analysis_results=analysis_results,
                available_dimensions=available_dimensions,
                dimensions=ANALYSIS_DIMENSIONS
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节详情页面时出错: {str(e)}", exc_info=True)
        flash(f"加载章节详情失败: {str(e)}", "danger")
        return redirect(url_for('v2.view_novel', novel_id=novel_id))

@v2_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>')
def chapter_analysis(novel_id, chapter_id, dimension):
    """查看章节分析结果页面"""
    try:
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v2.novels'))

            # 获取章节
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter or chapter.novel_id != novel_id:
                flash("未找到指定章节", "danger")
                return redirect(url_for('v2.view_novel', novel_id=novel_id))

            # 获取章节分析结果
            result = None
            try:
                # 使用更安全的查询方式，只查询确定存在的列
                result_tuple = session.query(
                    ChapterAnalysisResult.id,
                    ChapterAnalysisResult.chapter_id,
                    ChapterAnalysisResult.novel_id,
                    ChapterAnalysisResult.dimension,
                    ChapterAnalysisResult.content,
                    ChapterAnalysisResult.analysis_metadata,
                    ChapterAnalysisResult.analysis_logs,
                    ChapterAnalysisResult.created_at,
                    ChapterAnalysisResult.updated_at
                ).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter_id,
                    dimension=dimension
                ).first()

                if result_tuple:
                    # 创建一个新的ChapterAnalysisResult对象
                    result = ChapterAnalysisResult(
                        chapter_id=result_tuple[1],
                        novel_id=result_tuple[2],
                        dimension=result_tuple[3],
                        content=result_tuple[4],
                        metadata=result_tuple[5],
                        logs=result_tuple[6]
                    )
                    # 手动设置ID和时间戳
                    result.id = result_tuple[0]
                    result.created_at = result_tuple[7]
                    result.updated_at = result_tuple[8]

                    # 尝试从analysis_metadata中获取reasoning_content
                    if result.analysis_metadata and 'reasoning_content' in result.analysis_metadata:
                        result.reasoning_content = result.analysis_metadata['reasoning_content']
            except Exception as e:
                logger.error(f"获取章节分析结果时出错: {str(e)}", exc_info=True)
                result = None

            if not result:
                flash(f"未找到章节的{dimension}维度分析结果", "warning")
                return redirect(url_for('v2.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

            # 获取维度信息
            dimension_info = next((d for d in ANALYSIS_DIMENSIONS if d["key"] == dimension), None)

            # 特殊处理章纲分析
            if dimension == 'chapter_outline':
                return render_template(
                    'v2/chapter_outline_fix.html',
                    novel=novel,
                    analysis_result=result
                )
            else:
                return render_template(
                    'v2/chapter_analysis_detail.html',
                    novel=novel,
                    chapter=chapter,
                    result=result,
                    dimension=dimension,
                    dimension_info=dimension_info
                )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节分析结果页面时出错: {str(e)}", exc_info=True)
        flash(f"加载章节分析结果失败: {str(e)}", "danger")
        return redirect(url_for('v2.view_chapter', novel_id=novel_id, chapter_id=chapter_id))

@v2_bp.route('/novel/<int:novel_id>/chapters/summary')
def chapters_summary(novel_id):
    """章节分析汇总页面"""
    try:
        # 获取当前时间
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M')

        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                flash("未找到指定小说", "danger")
                return redirect(url_for('v2.novels'))

            # 获取章节
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()

            if not chapters:
                flash("小说没有章节，请先创建章节", "warning")
                return redirect(url_for('v2.view_novel', novel_id=novel_id))

            # 获取每个章节的分析维度
            chapter_dimensions = {}
            try:
                for chapter in chapters:
                    try:
                        dimensions = session.query(ChapterAnalysisResult.dimension).filter_by(
                            chapter_id=chapter.id
                        ).distinct().all()
                        chapter_dimensions[chapter.id] = [dim[0] for dim in dimensions]
                    except Exception as e:
                        logger.error(f"获取章节{chapter.id}的分析维度时出错: {str(e)}", exc_info=True)
                        chapter_dimensions[chapter.id] = []
            except Exception as e:
                logger.error(f"获取章节分析维度时出错: {str(e)}", exc_info=True)
                chapter_dimensions = {chapter.id: [] for chapter in chapters}

            # 将章节分组（每10个章节一组）
            chapter_groups = []
            for i in range(0, len(chapters), 10):
                group_chapters = chapters[i:i+10]

                if not group_chapters:  # 安全检查
                    continue

                # 为每个章节添加分析维度
                # 不直接修改章节对象，而是创建一个新的字典来存储分析维度
                chapter_analysis_dimensions = {}
                for chapter in group_chapters:
                    chapter_analysis_dimensions[chapter.id] = chapter_dimensions.get(chapter.id, [])

                chapter_groups.append({
                    'start': group_chapters[0].chapter_number if group_chapters else 0,  # 安全检查
                    'end': group_chapters[-1].chapter_number if group_chapters else 0,   # 安全检查
                    'chapters': group_chapters,
                    'group_id': f"group_{i//10 + 1}"
                })

            return render_template(
                'v2/chapters_summary.html',
                novel=novel,
                chapters=chapters,
                chapter_groups=chapter_groups,
                dimensions=ANALYSIS_DIMENSIONS,
                current_time=current_time,
                chapter_analysis_dimensions=chapter_analysis_dimensions
            )
        finally:
            session.close()
    except Exception as e:
        logger.error(f"查看章节分析汇总页面时出错: {str(e)}", exc_info=True)
        flash(f"加载章节分析汇总失败: {str(e)}", "danger")
        return redirect(url_for('v2.view_novel', novel_id=novel_id))

@v2_bp.route('/dimensions')
def dimensions():
    """分析维度介绍页面"""
    return render_template('v2/dimensions.html', dimensions=ANALYSIS_DIMENSIONS)

@v2_bp.route('/help')
def help():
    """帮助中心页面"""
    return render_template('v2/help.html')

@v2_bp.route('/system_monitor')
def system_monitor():
    """系统监控页面"""
    return render_template('v2/system_monitor.html')

# API路由 - 开始分析
@v2_bp.route('/api/novel/<int:novel_id>/analyze', methods=['POST'])
def api_analyze_novel(novel_id):
    """启动小说分析"""
    try:
        # 获取请求数据
        data = request.json or {}
        dimensions = data.get('dimensions', [])

        if not dimensions:
            return jsonify({
                'success': False,
                'error': '未指定分析维度'
            }), 400

        # 获取小说
        session = Session()
        try:
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 导入单维度分析模块
            from src.api.analyze_dimension import analyze_dimension

            # 逐个维度分析 - 强制使用真实API
            results = []
            for dimension in dimensions:
                # 强制使用真实API进行分析
                result = analyze_dimension(novel, dimension, use_real_api=True)
                if result:
                    results.append({
                        'dimension': dimension,
                        'success': True
                    })
                else:
                    results.append({
                        'dimension': dimension,
                        'success': False
                    })

            return jsonify({
                'success': True,
                'message': '分析已完成',
                'results': results
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"启动分析时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# API路由 - 分析章节
@v2_bp.route('/api/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze', methods=['POST'])
def api_analyze_chapter(novel_id, chapter_id):
    """分析章节"""
    try:
        # 详细记录请求信息
        logger.info(f"收到章节分析请求: novel_id={novel_id}, chapter_id={chapter_id}, method={request.method}, content_type={request.content_type}")

        # 验证请求数据格式
        if not request.is_json:
            error_msg = f"请求格式错误: 预期application/json, 实际{request.content_type}"
            logger.error(error_msg)
            return jsonify({
                'success': False,
                'error': error_msg
            }), 400

        # 获取请求数据
        try:
            data = request.json or {}
            logger.info(f"章节分析API收到请求数据: {data}")
        except Exception as e:
            error_msg = f"解析请求JSON数据时出错: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return jsonify({
                'success': False,
                'error': error_msg
            }), 400

        dimensions = data.get('dimensions')
        dimension = data.get('dimension')  # 兼容单维度分析

        logger.info(f"解析的维度参数: dimensions={dimensions}, dimension={dimension}")

        # 支持两种格式：dimensions数组或单个dimension
        if dimensions is None and not dimension:
            logger.error("未指定分析维度")
            return jsonify({
                'success': False,
                'error': '未指定分析维度'
            }), 400

        # 验证章节和小说是否存在
        session = Session()
        try:
            # 检查小说是否存在
            novel = session.query(Novel).filter_by(id=novel_id).first()
            if not novel:
                error_msg = f"小说不存在: novel_id={novel_id}"
                logger.error(error_msg)
                return jsonify({
                    'success': False,
                    'error': error_msg
                }), 404

            # 检查章节是否存在
            chapter = session.query(Chapter).filter_by(id=chapter_id, novel_id=novel_id).first()
            if not chapter:
                error_msg = f"章节不存在或不属于指定小说: novel_id={novel_id}, chapter_id={chapter_id}"
                logger.error(error_msg)
                return jsonify({
                    'success': False,
                    'error': error_msg
                }), 404

            logger.info(f"验证通过: 小说和章节存在 - novel_id={novel_id}, chapter_id={chapter_id}")
        except Exception as e:
            error_msg = f"验证小说和章节时出错: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return jsonify({
                'success': False,
                'error': error_msg
            }), 500
        finally:
            session.close()

        # 如果提供了dimensions参数（即使是空数组），则使用批量分析方法
        if dimensions is not None:
            try:
                # 如果dimensions是空数组，使用所有维度
                if not dimensions:
                    logger.info("未指定具体维度，将使用所有维度进行分析")
                    dimensions = [dim["key"] for dim in ANALYSIS_DIMENSIONS]
                    logger.info(f"使用所有维度: {dimensions}")

                # 确保dimensions是列表类型
                if isinstance(dimensions, str):
                    dimensions = [dimensions]
                elif not isinstance(dimensions, list):
                    try:
                        dimensions = list(dimensions)
                    except:
                        logger.error(f"无法将dimensions参数转换为列表: {dimensions}")
                        return jsonify({
                            'success': False,
                            'error': f"维度参数格式错误，无法转换为列表: {dimensions}"
                        }), 400

                logger.info(f"准备批量分析章节 {chapter_id} 的维度: {dimensions}")

                # 使用批量分析方法
                try:
                    result = ChapterAnalysisService.analyze_chapter_batch(
                        chapter_id=chapter_id,
                        dimensions=dimensions
                    )

                    logger.info(f"批量分析完成，返回结果状态: success={result.get('success', False)}")
                    if not result.get('success', False):
                        logger.error(f"批量分析失败: {result.get('error', '未知错误')}")

                    return jsonify(result)
                except Exception as service_error:
                    error_msg = f"调用批量分析服务时出错: {str(service_error)}"
                    logger.error(error_msg, exc_info=True)
                    return jsonify({
                        'success': False,
                        'error': error_msg
                    }), 500
            except Exception as e:
                logger.error(f"批量分析章节维度时出错: {str(e)}", exc_info=True)
                return jsonify({
                    'success': False,
                    'error': f"批量分析出错: {str(e)}"
                }), 500
        else:
            # 单维度分析
            try:
                logger.info(f"开始单维度分析: chapter_id={chapter_id}, dimension={dimension}")
                result = ChapterAnalysisService.analyze_chapter(
                    chapter_id=chapter_id,
                    dimension=dimension
                )

                logger.info(f"单维度分析完成，返回结果状态: success={result.get('success', False)}")
                if not result.get('success', False):
                    logger.error(f"单维度分析失败: {result.get('error', '未知错误')}")

                return jsonify(result)
            except Exception as service_error:
                error_msg = f"调用单维度分析服务时出错: {str(service_error)}"
                logger.error(error_msg, exc_info=True)
                return jsonify({
                    'success': False,
                    'error': error_msg
                }), 500
    except Exception as e:
        logger.error(f"分析章节时出错: novel_id={novel_id}, chapter_id={chapter_id}, error={str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f"服务器内部错误: {str(e)}"
        }), 500

# API路由 - 汇总章节分析
@v2_bp.route('/api/novel/<int:novel_id>/aggregate_chapters', methods=['POST'])
def api_aggregate_chapter_analyses(novel_id):
    """汇总章节分析结果"""
    try:
        # 获取请求参数
        data = request.json or {}
        dimensions = data.get('dimensions')  # 支持维度数组
        dimension = data.get('dimension')    # 兼容单维度汇总
        batch_size = data.get('batch_size', 3)  # 每批处理的维度数量，默认为3

        # 如果提供了dimensions数组，则批量汇总
        if dimensions:
            # 使用维度优先级配置对维度进行排序
            try:
                # 导入维度优先级配置
                from dimension_priority import get_prioritized_dimensions, get_batch_dimensions

                # 对维度进行优先级排序
                prioritized_dimensions = get_prioritized_dimensions(dimensions)
                logger.info(f"维度优先级排序: 原始顺序={dimensions}, 排序后={prioritized_dimensions}")

                # 将排序后的维度分成多个批次
                dimension_batches = get_batch_dimensions(prioritized_dimensions, batch_size)
                logger.info(f"将{len(prioritized_dimensions)}个维度分成{len(dimension_batches)}个批次进行处理，每批最多{batch_size}个维度")
            except Exception as e:
                logger.warning(f"使用维度优先级排序时出错: {str(e)}，将使用原始维度顺序")
                # 如果导入失败，使用原始方法分批
                dimension_batches = [dimensions[i:i+batch_size] for i in range(0, len(dimensions), batch_size)]
                logger.info(f"将{len(dimensions)}个维度分成{len(dimension_batches)}个批次进行处理，每批{batch_size}个维度")

            results = []
            success_count = 0
            batch_count = 0

            # 逐批处理维度
            for batch in dimension_batches:
                batch_count += 1
                logger.info(f"开始处理第{batch_count}/{len(dimension_batches)}批维度: {batch}")

                for dim in batch:
                    try:
                        # 调用汇总函数
                        result = aggregate_chapter_analyses(novel_id, dim)

                        if result.get('success'):
                            success_count += 1

                        results.append({
                            'dimension': dim,
                            'success': result.get('success', False),
                            'message': result.get('message', '')
                        })
                    except Exception as e:
                        logger.error(f"汇总章节维度{dim}时出错: {str(e)}", exc_info=True)
                        results.append({
                            'dimension': dim,
                            'success': False,
                            'message': f"汇总出错: {str(e)}"
                        })

                # 每批处理完成后，暂停一段时间，让系统资源得到释放
                if batch_count < len(dimension_batches):
                    logger.info(f"第{batch_count}批处理完成，暂停3秒后继续下一批")
                    import time
                    time.sleep(3)

            return jsonify({
                'success': success_count > 0,
                'message': f'成功汇总{success_count}/{len(dimensions)}个维度',
                'results': results
            })
        else:
            # 单维度汇总或全部汇总
            result = aggregate_chapter_analyses(novel_id, dimension)
            return jsonify(result)
    except Exception as e:
        logger.error(f"汇总章节分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"汇总章节分析结果时出错: {str(e)}",
            "novel_id": novel_id
        })

# API路由 - 获取系统信息
@v2_bp.route('/api/system/info')
def api_system_info():
    """获取系统信息"""
    try:
        import platform
        import psutil
        import sys
        import flask
        import sqlalchemy
        import os
        from datetime import datetime, timezone

        # 获取进程信息
        process = psutil.Process()
        start_time = datetime.fromtimestamp(process.create_time(), tz=timezone.utc)

        # 获取环境变量
        env_vars = {
            'DEBUG': os.environ.get('DEBUG', 'False'),
            'HOST': os.environ.get('HOST', '127.0.0.1'),
            'PORT': os.environ.get('PORT', '5001'),
            'DB_POOL_SIZE': os.environ.get('DB_POOL_SIZE', '30'),
            'DB_MAX_OVERFLOW': os.environ.get('DB_MAX_OVERFLOW', '20'),
            'THREAD_POOL_SIZE': os.environ.get('THREAD_POOL_SIZE', '8'),
            'MAX_WORKERS': os.environ.get('MAX_WORKERS', '8'),
            'DISABLE_AUTO_REFRESH': os.environ.get('DISABLE_AUTO_REFRESH', 'True'),
            'DISABLE_CHARTS': os.environ.get('DISABLE_CHARTS', 'True')
        }

        return jsonify({
            'success': True,
            'os': f"{platform.system()} {platform.release()} ({platform.architecture()[0]})",
            'python_version': platform.python_version(),
            'flask_version': flask.__version__,
            'sqlalchemy_version': sqlalchemy.__version__,
            'pid': os.getpid(),
            'start_time': start_time.isoformat(),
            'env_vars': env_vars
        })
    except Exception as e:
        logger.error(f"获取系统信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# API路由 - 获取系统日志
@v2_bp.route('/api/system/logs')
def api_system_logs():
    """获取系统日志"""
    try:
        import os
        from datetime import datetime
        import re

        # 获取日志级别参数
        level = request.args.get('level', 'all')

        # 日志文件路径
        log_dir = os.path.join(os.getcwd(), 'logs')
        log_files = []

        # 查找所有日志文件
        if os.path.exists(log_dir):
            for file in os.listdir(log_dir):
                if file.endswith('.log'):
                    log_files.append(os.path.join(log_dir, file))

        # 如果没有找到日志文件，返回空列表
        if not log_files:
            return jsonify({
                'success': True,
                'logs': []
            })

        # 获取最新的日志文件
        latest_log_file = max(log_files, key=os.path.getmtime)

        # 读取日志文件
        logs = []
        if os.path.exists(latest_log_file):
            with open(latest_log_file, 'r', encoding='utf-8', errors='ignore') as f:
                log_lines = f.readlines()

                # 解析日志行
                for line in log_lines:
                    try:
                        # 尝试解析日志行
                        # 示例格式: 2023-05-01 12:34:56,789 - module_name - INFO - 日志消息
                        match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (\w+) - (\w+) - (.*)', line)
                        if match:
                            timestamp_str, module, log_level, message = match.groups()
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')

                            # 根据级别筛选
                            if level != 'all' and log_level.lower() != level.lower():
                                continue

                            logs.append({
                                'timestamp': timestamp.isoformat(),
                                'module': module,
                                'level': log_level,
                                'message': message.strip()
                            })
                        else:
                            # 如果无法解析，将整行作为消息
                            logs.append({
                                'timestamp': datetime.now().isoformat(),
                                'module': 'unknown',
                                'level': 'info',
                                'message': line.strip()
                            })
                    except Exception as e:
                        logger.error(f"解析日志行时出错: {str(e)}", exc_info=True)
                        continue

        # 按时间戳倒序排序
        logs.sort(key=lambda x: x['timestamp'], reverse=True)

        # 限制返回的日志数量
        max_logs = 500
        if len(logs) > max_logs:
            logs = logs[:max_logs]

        return jsonify({
            'success': True,
            'logs': logs,
            'log_file': os.path.basename(latest_log_file)
        })
    except Exception as e:
        logger.error(f"获取系统日志时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# API路由 - 获取API调用统计
@v2_bp.route('/api/system/api_stats')
def api_system_api_stats():
    """获取API调用统计"""
    try:
        from datetime import datetime, timedelta
        import random

        # 模拟数据
        total_calls = random.randint(1000, 5000)
        today_calls = random.randint(100, 500)
        today_limit = 1000
        hour_calls = random.randint(10, 100)
        hour_limit = 200

        # 模拟维度统计
        dimensions = []
        for dimension in ANALYSIS_DIMENSIONS:
            dimensions.append({
                'dimension': dimension['name'],
                'count': random.randint(50, 500),
                'avg_time': random.uniform(1.0, 10.0)
            })

        # 按调用次数排序
        dimensions.sort(key=lambda x: x['count'], reverse=True)

        return jsonify({
            'success': True,
            'total_calls': total_calls,
            'today_calls': today_calls,
            'today_limit': today_limit,
            'hour_calls': hour_calls,
            'hour_limit': hour_limit,
            'by_dimension': dimensions
        })
    except Exception as e:
        logger.error(f"获取API调用统计时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# API路由 - 分割章节
@v2_bp.route('/api/novel/<int:novel_id>/split_chapters', methods=['POST'])
def api_split_chapters(novel_id):
    """分割章节"""
    try:
        # 分割章节
        chapters = ChapterAnalysisService.split_novel_into_chapters(novel_id)

        return jsonify({
            'success': True,
            'message': f'成功分割出 {len(chapters)} 个章节',
            'chapter_count': len(chapters)
        })
    except Exception as e:
        logger.error(f"分割章节时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
