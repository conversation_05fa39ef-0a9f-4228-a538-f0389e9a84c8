<!-- button-fix-inline.html -->
<!-- 按钮文字紧急修复内联脚本 -->
<style>
    /* 按钮文字修复样式 */
    .btn {
        color: inherit !important;
        opacity: 1 !important;
        visibility: visible !important;
        overflow: visible !important;
        text-indent: 0 !important;
        white-space: normal !important;
    }

    /* 按钮颜色修复 */
    .btn-primary { color: #fff !important; background-color: #007bff; border-color: #007bff; }
    .btn-secondary { color: #fff !important; background-color: #6c757d; border-color: #6c757d; }
    .btn-success { color: #fff !important; background-color: #28a745; border-color: #28a745; }
    .btn-danger { color: #fff !important; background-color: #dc3545; border-color: #dc3545; }
    .btn-warning { color: #212529 !important; background-color: #ffc107; border-color: #ffc107; }
    .btn-info { color: #fff !important; background-color: #17a2b8; border-color: #17a2b8; }
</style>
<script>
    // 按钮文字修复脚本
    document.addEventListener('DOMContentLoaded', function() {
        // 修复按钮文字
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(function(button) {
            // 确保按钮文字可见
            button.style.color = 'inherit';
            button.style.opacity = '1';
            button.style.visibility = 'visible';
            button.style.overflow = 'visible';
            button.style.textIndent = '0';
            button.style.whiteSpace = 'normal';
        });

        console.log('按钮文字修复已应用');
    });
</script>
