/**
 * 九猫系统 - 全局标签页修复加载器
 * 确保标签页修复脚本在所有页面上都能正确加载
 * 版本: 1.0.0
 */

(function() {
    'use strict';
    
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[全局标签页修复加载器] 已加载');
        
        // 检查是否已加载标签页修复脚本
        if (typeof window.initTabFix === 'function') {
            console.log('[全局标签页修复加载器] 标签页修复脚本已加载');
            return;
        }
        
        // 检查页面上是否有标签页
        if (!hasTabsInPage()) {
            console.log('[全局标签页修复加载器] 页面上没有标签页，跳过加载');
            return;
        }
        
        // 加载标签页修复脚本
        loadTabFixScript();
    });
    
    /**
     * 检查页面上是否有标签页
     */
    function hasTabsInPage() {
        // 检查是否有标签页相关的元素
        return document.querySelector('.nav-tabs') !== null || 
               document.querySelector('[data-toggle="tab"]') !== null || 
               document.querySelector('[data-bs-toggle="tab"]') !== null || 
               document.querySelector('.tab-pane') !== null;
    }
    
    /**
     * 加载标签页修复脚本
     */
    function loadTabFixScript() {
        console.log('[全局标签页修复加载器] 开始加载标签页修复脚本');
        
        // 创建脚本元素
        const script = document.createElement('script');
        script.src = '/static/js/test-tab-fix.js';
        script.async = true;
        
        // 添加加载事件处理
        script.onload = function() {
            console.log('[全局标签页修复加载器] 标签页修复脚本加载成功');
        };
        
        script.onerror = function() {
            console.error('[全局标签页修复加载器] 标签页修复脚本加载失败，尝试备用路径');
            
            // 尝试从备用路径加载
            const backupScript = document.createElement('script');
            backupScript.src = '/direct-static/js/test-tab-fix.js';
            backupScript.async = true;
            
            backupScript.onload = function() {
                console.log('[全局标签页修复加载器] 从备用路径加载标签页修复脚本成功');
            };
            
            backupScript.onerror = function() {
                console.error('[全局标签页修复加载器] 从备用路径加载标签页修复脚本失败');
            };
            
            document.head.appendChild(backupScript);
        };
        
        // 添加到页面
        document.head.appendChild(script);
    }
})();
