Option Explicit

' 九猫小说分析系统 v2.0 启动脚本（简化版）
' 自动启动九猫系统，无需手动操作

' 设置工作目录为脚本所在目录
Dim fso, shell, pythonCmd, workingDir, runCmd
Set fso = CreateObject("Scripting.FileSystemObject")
Set shell = CreateObject("WScript.Shell")

' 获取当前脚本所在目录
workingDir = fso.GetParentFolderName(WScript.ScriptFullName)
shell.CurrentDirectory = workingDir

' 检查Python环境
pythonCmd = "python"
If Not CheckPythonInstalled(pythonCmd) Then
    pythonCmd = "py"
    If Not CheckPythonInstalled(pythonCmd) Then
        MsgBox "未检测到Python环境，请安装Python 3.8或更高版本。", vbExclamation, "九猫小说分析系统"
        WScript.Quit
    End If
End If

' 启动九猫系统
runCmd = pythonCmd & " -u -m src.web.v2_app"
shell.Run runCmd, 1, False

' 打开浏览器访问系统
WScript.Sleep 2000 ' 等待2秒，确保服务器已启动
shell.Run "http://localhost:5001", 1, False

' 显示启动成功消息
MsgBox "九猫小说分析系统 v2.0 已启动！" & vbCrLf & _
       "系统已在浏览器中打开，地址：http://localhost:5001" & vbCrLf & _
       "如果浏览器未自动打开，请手动访问上述地址。", vbInformation, "九猫小说分析系统"

' 检查Python是否已安装
Function CheckPythonInstalled(cmd)
    On Error Resume Next
    shell.Run cmd & " --version", 0, True
    CheckPythonInstalled = (Err.Number = 0)
    On Error GoTo 0
End Function
