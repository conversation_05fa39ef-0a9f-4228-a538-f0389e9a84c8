#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
九猫系统 - 推理过程修复工具
用于修复推理过程显示"API未返回推理过程"的问题

这个脚本会检查数据库中的分析结果，提取推理过程内容，并将其保存到数据库中。
"""

import os
import sys
import json
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('fix_reasoning_content')

# 导入数据库模型和连接
from src.db.connection import Session
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.api_log import ApiLog
from src.models.analysis_process import AnalysisProcess

def extract_reasoning_content(result):
    """
    从分析结果中提取推理过程内容

    Args:
        result: AnalysisResult对象

    Returns:
        推理过程内容，如果找不到则返回None
    """
    # 检查是否有分析元数据
    if not hasattr(result, 'analysis_metadata') or not result.analysis_metadata:
        logger.warning(f"分析结果 {result.id} 没有元数据")
        return None

    # 解析元数据
    try:
        if isinstance(result.analysis_metadata, str):
            metadata = json.loads(result.analysis_metadata)
        elif isinstance(result.analysis_metadata, dict):
            metadata = result.analysis_metadata
        else:
            logger.warning(f"分析结果 {result.id} 的元数据类型不支持: {type(result.analysis_metadata)}")
            return None
    except Exception as e:
        logger.error(f"解析元数据时出错: {str(e)}")
        return None

    # 检查元数据中是否包含reasoning_content字段
    if 'reasoning_content' in metadata:
        logger.info(f"在元数据中找到reasoning_content字段，长度: {len(metadata['reasoning_content'])}")
        return metadata['reasoning_content']

    # 检查元数据中是否包含response字段，并且response中有reasoning_content字段
    if 'response' in metadata and isinstance(metadata['response'], dict) and 'reasoning_content' in metadata['response']:
        logger.info(f"在元数据的response中找到reasoning_content字段，长度: {len(metadata['response']['reasoning_content'])}")
        return metadata['response']['reasoning_content']

    # 检查元数据中是否包含choices字段，并且choices中有message字段，message中有content字段
    if 'choices' in metadata and isinstance(metadata['choices'], list) and len(metadata['choices']) > 0:
        choice = metadata['choices'][0]
        if isinstance(choice, dict) and 'message' in choice:
            message = choice['message']
            if isinstance(message, dict) and 'content' in message:
                content = message['content']
                # 检查内容是否包含推理过程特征
                process_markers = [
                    '好的，我现在要',
                    '首先，我需要',
                    '我将分析',
                    '我需要分析',
                    '嗯，用户让我',
                    '下面我来分析',
                    '让我来分析',
                    '我会按照以下步骤',
                    '我将按照以下步骤',
                    '我将从以下几个方面',
                    '我需要从以下几个方面',
                    '我将逐步分析',
                    '好的，我现在需要',
                    '我需要通读一遍',
                    '我需要先了解',
                    '我会先阅读',
                    '我先阅读一遍',
                    '我需要分析用户提供的',
                    '开始分析这段小说',
                    '我需要全面考虑',
                    '我得仔细阅读',
                    '作为文学分析专家'
                ]

                for marker in process_markers:
                    if marker in content:
                        logger.info(f"在choices中找到推理过程，使用标记: {marker}")
                        return content

    # 检查content字段是否包含推理过程特征
    if hasattr(result, 'content') and result.content:
        content = result.content
        process_markers = [
            '好的，我现在要',
            '首先，我需要',
            '我将分析',
            '我需要分析',
            '嗯，用户让我',
            '下面我来分析',
            '让我来分析',
            '我会按照以下步骤',
            '我将按照以下步骤',
            '我将从以下几个方面',
            '我需要从以下几个方面',
            '我将逐步分析',
            '好的，我现在需要',
            '我需要通读一遍',
            '我需要先了解',
            '我会先阅读',
            '我先阅读一遍',
            '我需要分析用户提供的',
            '开始分析这段小说',
            '我需要全面考虑',
            '我得仔细阅读',
            '作为文学分析专家'
        ]

        for marker in process_markers:
            if marker in content:
                # 尝试提取分析过程部分
                start_idx = content.find(marker)
                end_markers = [
                    '# 分析结果',
                    '## 分析结果',
                    '\n\n分析结果',
                    '\n\n# ',
                    '\n\n## ',
                    '\n\n1. ',
                    '\n\n总结',
                    '\n\n综上所述',
                    '\n\n综合以上分析',
                    '\n\n通过以上分析'
                ]
                end_idx = -1

                for end_marker in end_markers:
                    temp_idx = content.find(end_marker, start_idx)
                    if temp_idx != -1 and (end_idx == -1 or temp_idx < end_idx):
                        end_idx = temp_idx

                if end_idx != -1:
                    reasoning_content = content[start_idx:end_idx]
                else:
                    reasoning_content = content[start_idx:]

                logger.info(f"从content中提取到推理过程，使用标记: {marker}")
                return reasoning_content

    # 如果找不到推理过程，返回None
    logger.warning(f"无法从分析结果 {result.id} 中提取推理过程")
    return None

def fix_reasoning_content():
    """
    修复推理过程显示"API未返回推理过程"的问题
    """
    session = Session()
    try:
        # 获取所有分析结果
        results = session.query(AnalysisResult).all()
        logger.info(f"找到 {len(results)} 个分析结果")

        # 统计信息
        fixed_count = 0
        already_fixed_count = 0
        failed_count = 0

        # 遍历所有分析结果
        for result in results:
            logger.info(f"处理分析结果: ID={result.id}, 小说ID={result.novel_id}, 维度={result.dimension}")

            # 检查是否已经有推理过程
            process_records = session.query(AnalysisProcess).filter_by(
                novel_id=result.novel_id, dimension=result.dimension, processing_stage='reasoning'
            ).first()

            if process_records:
                logger.info(f"分析结果 {result.id} 已经有推理过程记录")
                already_fixed_count += 1
                continue

            # 提取推理过程
            reasoning_content = extract_reasoning_content(result)

            if reasoning_content:
                # 创建分析过程记录
                try:
                    process = AnalysisProcess(
                        novel_id=result.novel_id,
                        dimension=result.dimension,
                        block_index=0,
                        total_blocks=1,
                        processing_stage='reasoning',
                        input_text=reasoning_content,
                        output_text='',
                        prompt_used='',
                        result_id=result.id
                    )
                    session.add(process)
                    session.commit()
                    logger.info(f"成功为分析结果 {result.id} 创建推理过程记录")
                    fixed_count += 1
                except Exception as e:
                    logger.error(f"创建推理过程记录时出错: {str(e)}")
                    failed_count += 1
            else:
                logger.warning(f"无法为分析结果 {result.id} 提取推理过程")
                failed_count += 1

        # 打印统计信息
        logger.info(f"修复完成: 成功修复 {fixed_count} 个，已修复 {already_fixed_count} 个，失败 {failed_count} 个")

    except Exception as e:
        logger.error(f"修复推理过程时出错: {str(e)}")
    finally:
        session.close()

if __name__ == "__main__":
    logger.info("开始修复推理过程...")
    fix_reasoning_content()
    logger.info("修复完成")
