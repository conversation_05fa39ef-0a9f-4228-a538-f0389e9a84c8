{"name": "npm-audit-report", "version": "3.0.0", "description": "Given a response from the npm security api, render it into a variety of security reports", "main": "lib/index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "keywords": ["npm", "security", "report", "audit"], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"chalk": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.1.2", "require-inject": "^1.4.4", "tap": "^16.0.0"}, "directories": {"lib": "lib", "test": "test"}, "repository": {"type": "git", "url": "https://github.com/npm/npm-audit-report.git"}, "bugs": {"url": "https://github.com/npm/npm-audit-report/issues"}, "homepage": "https://github.com/npm/npm-audit-report#readme", "files": ["bin/", "lib/", "reporters"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.1.2"}}