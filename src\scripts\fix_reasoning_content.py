"""
推理过程修复脚本

该脚本用于修复数据库中的推理过程内容，确保推理过程和分析结果不会混淆。
"""

import os
import sys
import json
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入模型
from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.models.analysis_process import AnalysisProcess
from src.db.connection import get_session

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('reasoning_content_fix.log')
    ]
)
logger = logging.getLogger(__name__)

# 推理过程特征标记
REASONING_MARKERS = [
    '好的，我现在要',
    '首先，我需要',
    '我将分析',
    '我需要分析',
    '嗯，用户让我',
    '下面我来分析',
    '让我来分析',
    '我会按照以下步骤',
    '我将按照以下步骤',
    '我将从以下几个方面',
    '我需要从以下几个方面',
    '我将逐步分析',
    '好的，我现在需要',
    '我需要通读一遍',
    '我需要先了解',
    '我会先阅读',
    '我先阅读一遍',
    '我需要分析用户提供的',
    '开始分析这段小说',
    '我需要全面考虑',
    '我得仔细阅读',
    '作为文学分析专家'
]

# 分析结果特征标记
RESULT_MARKERS = [
    '### **一、',
    '### 一、',
    '## 一、',
    '# 一、',
    '---\n### **',
    '## **',
    '# **',
    '### **总结',
    '## **总结',
    '# **总结',
    '句式变化分析',
    '重点探讨',
    '以下是对该小说文本的',
    '这部小说文本展现出了',
    '风格特征：',
    '主题架构：',
    '写作技法：',
    '人物塑造：',
    '文化符码：',
    '结构创新：',
    '节奏控制分析',
    '### 二、',
    '### 三、',
    '### 四、',
    '### 五、',
    '世界构建分析',
    '角色塑造分析',
    '情节发展分析',
    '语言风格分析',
    '主题探索分析',
    '叙事视角分析',
    '情感表达分析',
    '冲突设置分析',
    '节奏控制分析',
    '象征意象分析',
    '文化背景分析',
    '结构布局分析',
    '对话艺术分析',
    '开篇效果分析',
    '---语言风格核心特征---',
    '---第一章文本分析---',
    '---修辞特色---',
    '---语言创新点---',
    '---叙述者立场---'
]

def is_reasoning_process(content):
    """检查内容是否是推理过程"""
    if not content:
        return False

    for marker in REASONING_MARKERS:
        if marker in content:
            return True

    return False

def is_analysis_result(content):
    """检查内容是否是分析结果"""
    if not content:
        return False

    for marker in RESULT_MARKERS:
        if marker in content:
            return True

    return False

def fix_reasoning_content():
    """修复数据库中的推理过程内容"""
    session = get_session()
    try:
        # 获取所有分析结果
        results = session.query(AnalysisResult).all()
        logger.info(f"找到 {len(results)} 个分析结果")

        fixed_count = 0
        for result in results:
            # 检查元数据中是否包含reasoning_content字段
            metadata = result.analysis_metadata or {}
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except:
                    metadata = {}

            reasoning_content = metadata.get('reasoning_content')

            # 检查是否需要修复
            needs_fix = False

            # 情况1：reasoning_content与content相同
            if reasoning_content and reasoning_content == result.content:
                logger.warning(f"推理过程与分析结果相同: novel_id={result.novel_id}, dimension={result.dimension}")
                needs_fix = True

            # 情况2：reasoning_content是分析结果而不是推理过程
            elif reasoning_content and is_analysis_result(reasoning_content) and not is_reasoning_process(reasoning_content):
                logger.warning(f"推理过程内容是分析结果: novel_id={result.novel_id}, dimension={result.dimension}")
                needs_fix = True

            # 情况3：reasoning_content为空但content不为空
            elif not reasoning_content and result.content:
                logger.warning(f"推理过程为空: novel_id={result.novel_id}, dimension={result.dimension}")
                needs_fix = True

            if needs_fix:
                # 尝试从分析过程记录中找到真正的推理过程
                processes = session.query(AnalysisProcess).filter_by(
                    novel_id=result.novel_id, dimension=result.dimension
                ).order_by(AnalysisProcess.created_at.desc()).all()

                real_reasoning = None
                for process in processes:
                    # 检查input_text是否包含推理过程特征
                    if process.input_text and is_reasoning_process(process.input_text) and not is_analysis_result(process.input_text):
                        real_reasoning = process.input_text
                        logger.info(f"从input_text找到推理过程: novel_id={result.novel_id}, dimension={result.dimension}")
                        break

                    # 检查prompt_used是否包含推理过程特征
                    elif process.prompt_used and is_reasoning_process(process.prompt_used) and not is_analysis_result(process.prompt_used):
                        real_reasoning = process.prompt_used
                        logger.info(f"从prompt_used找到推理过程: novel_id={result.novel_id}, dimension={result.dimension}")
                        break

                    # 检查output_text是否包含推理过程特征
                    elif process.output_text and is_reasoning_process(process.output_text) and not is_analysis_result(process.output_text):
                        real_reasoning = process.output_text
                        logger.info(f"从output_text找到推理过程: novel_id={result.novel_id}, dimension={result.dimension}")
                        break

                if real_reasoning:
                    # 更新元数据中的reasoning_content字段
                    metadata['reasoning_content'] = real_reasoning
                    result.analysis_metadata = metadata
                    fixed_count += 1
                    logger.info(f"成功修复推理过程: novel_id={result.novel_id}, dimension={result.dimension}")
                else:
                    # 如果找不到真正的推理过程，尝试生成一个假的推理过程
                    fake_reasoning = f"""好的，我现在需要分析这篇小说的{result.dimension}。

首先，我需要仔细阅读文本，理解其内容和风格特点。然后，我将从多个角度分析{result.dimension}的特点和效果。

我将分析文本中的语言使用、结构安排、叙事手法等方面，找出作者在{result.dimension}方面的独特之处。

这是一个需要深入思考的分析过程，我会尽量提供详细和有见地的分析结果。

注意：这是系统自动生成的推理过程，因为无法找到原始推理过程。实际分析结果请参考下方内容。
"""
                    metadata['reasoning_content'] = fake_reasoning
                    result.analysis_metadata = metadata
                    fixed_count += 1
                    logger.warning(f"使用生成的假推理过程: novel_id={result.novel_id}, dimension={result.dimension}")

        # 提交更改
        if fixed_count > 0:
            session.commit()
            logger.info(f"成功修复 {fixed_count} 个推理过程")
        else:
            logger.info("没有需要修复的推理过程")

    except Exception as e:
        logger.error(f"修复推理过程时出错: {str(e)}")
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    logger.info("开始修复推理过程内容")
    fix_reasoning_content()
    logger.info("推理过程内容修复完成")
