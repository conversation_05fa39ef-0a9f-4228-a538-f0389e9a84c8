# 修复分析模板中推理过程显示问题
import os

# 模板文件路径
template_file = "src/web/templates/new_analysis.html"

try:
    # 读取文件内容
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复推理过程显示代码
    modified_content = content.replace(
        '{% if result.reasoning_content %}\n                        <pre class="reasoning-text">{{ result.reasoning_content }}</pre>\n                    {% elif result.metadata and result.metadata.reasoning_content %}',
        '{% if result.reasoning_content %}\n                        <pre class="reasoning-text">{{ result.reasoning_content }}</pre>\n                    {% elif result.analysis_metadata and result.analysis_metadata.reasoning_content %}\n                        <pre class="reasoning-text">{{ result.analysis_metadata.reasoning_content }}</pre>\n                    {% elif result.metadata and result.metadata.reasoning_content %}'
    )
    
    # 写回文件
    with open(template_file, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f"成功修复了推理过程显示问题: {template_file}")
    
except Exception as e:
    print(f"修复过程中出现错误: {str(e)}") 