#!/usr/bin/env python
"""
检查预设模板是否已添加到数据库中
"""
import os
import sys
from pprint import pprint

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的类和函数
from src.models.preset import Preset
from src.db.connection import Session

def check_presets():
    """检查预设模板是否已添加到数据库中"""
    session = Session()
    try:
        # 获取所有预设内容
        presets = session.query(Preset).filter_by(category="preset_template").all()
        
        if not presets:
            print("未找到预设模板")
            return
            
        print(f"找到 {len(presets)} 个预设模板:")
        
        for preset in presets:
            print(f"\nID: {preset.id}")
            print(f"标题: {preset.title}")
            print(f"分类: {preset.category}")
            print(f"元数据: {preset.meta_info}")
            print(f"创建时间: {preset.created_at}")
            
            # 只显示内容的前200个字符，避免输出过长
            content_preview = preset.content[:200] + "..." if len(preset.content) > 200 else preset.content
            print(f"内容预览: {content_preview}")
            print("-" * 50)
        
    finally:
        session.close()

if __name__ == "__main__":
    print("开始检查预设模板")
    check_presets()
    print("检查完成") 