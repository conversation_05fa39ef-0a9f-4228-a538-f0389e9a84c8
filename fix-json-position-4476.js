/**
 * 九猫 - JSON文件修复工具
 * 专门修复位置4476的JSON解析错误
 * 
 * 使用方法:
 * node fix-json-position-4476.js <文件路径>
 */

const fs = require('fs');
const path = require('path');

/**
 * 修复JSON字符串中的常见错误
 * @param {string} text - 要修复的JSON字符串
 * @return {string} - 修复后的JSON字符串
 */
function fixJsonString(text) {
    if (typeof text !== 'string') return text;
    
    // 修复位置4476附近的错误
    if (text.length > 4400 && text.length < 4600) {
        // 检查是否包含特定内容
        if (text.includes('关系张力与趣味性') && text.includes('总结')) {
            console.log('检测到位置4476附近的特定内容，应用精确修复');
            
            // 查找"关系张力与趣味性"部分的结束位置
            var sectionEndPos = text.indexOf('## 总结');
            if (sectionEndPos !== -1) {
                // 在"关系张力与趣味性"部分的结束和"总结"部分的开始之间添加逗号
                var beforeSection = text.substring(0, sectionEndPos);
                var afterSection = text.substring(sectionEndPos);
                
                // 确保在"metadata"前添加逗号
                if (afterSection.includes('"metadata"')) {
                    var metadataPos = afterSection.indexOf('"metadata"');
                    var fixedText = beforeSection + 
                                   afterSection.substring(0, metadataPos) + 
                                   ',' + 
                                   afterSection.substring(metadataPos);
                    return fixedText;
                }
            }
        }
    }
    
    // 修复character_relationships错误
    if (text.includes('character_relationships** 时遇到了问题')) {
        console.log('检测到character_relationships错误，应用特定修复');
        
        // 修复特定错误模式
        return text.replace(
            /(character_relationships\*\* 时遇到了问题[^"]*\\n\\n## 错误信息\\n```\\nname )(.*?)(")/g,
            '$1"$3'
        );
    }
    
    // 通用JSON修复
    return text
        // 修复未终止的字符串
        .replace(/([^\\])"([^"]*$)/g, '$1"$2"')
        // 修复缺少逗号的属性
        .replace(/([^,{])\s*"([^"]+)":/g, '$1,"$2":')
        // 修复缺少右大括号
        .replace(/([^}])\s*$/g, '$1}')
        // 修复未转义的换行符
        .replace(/\n/g, '\\n')
        // 修复未转义的制表符
        .replace(/\t/g, '\\t')
        // 修复未转义的回车符
        .replace(/\r/g, '\\r');
}

/**
 * 尝试修复JSON文件
 * @param {string} filePath - 文件路径
 */
function fixJsonFile(filePath) {
    console.log(`开始修复文件: ${filePath}`);
    
    try {
        // 读取文件内容
        const content = fs.readFileSync(filePath, 'utf8');
        console.log(`文件大小: ${content.length} 字节`);
        
        try {
            // 尝试解析原始内容
            JSON.parse(content);
            console.log('文件已经是有效的JSON，无需修复');
            return;
        } catch (e) {
            console.error('JSON解析错误:', e.message);
            
            // 检查是否是位置4476附近的错误
            if (e.message.includes('position 447') && e.message.includes("Expected ',' or '}'")) {
                console.log('检测到位置4476特定错误，应用专用修复');
                
                // 提取错误位置
                const errorPos = parseInt(e.message.match(/position (\d+)/)[1]);
                console.log('错误位置:', errorPos);
                
                // 显示错误位置前后的内容
                const beforeError = content.substring(Math.max(0, errorPos - 50), errorPos);
                const afterError = content.substring(errorPos, Math.min(content.length, errorPos + 50));
                console.log('错误位置前内容:', beforeError);
                console.log('错误位置后内容:', afterError);
                
                // 应用修复
                const fixedContent = fixJsonString(content);
                
                try {
                    // 验证修复后的内容
                    JSON.parse(fixedContent);
                    console.log('修复成功！');
                    
                    // 创建备份
                    const backupPath = `${filePath}.bak`;
                    fs.writeFileSync(backupPath, content);
                    console.log(`原始文件已备份为: ${backupPath}`);
                    
                    // 保存修复后的内容
                    fs.writeFileSync(filePath, fixedContent);
                    console.log(`修复后的内容已保存到: ${filePath}`);
                } catch (e2) {
                    console.error('修复验证失败:', e2.message);
                    
                    // 尝试多种修复策略
                    const strategies = [
                        // 策略1: 在错误位置添加逗号
                        () => content.substring(0, errorPos) + ',' + content.substring(errorPos),
                        // 策略2: 在错误位置添加右大括号
                        () => content.substring(0, errorPos) + '}' + content.substring(errorPos),
                        // 策略3: 在错误位置添加逗号和右大括号
                        () => content.substring(0, errorPos) + ',}' + content.substring(errorPos),
                        // 策略4: 在错误位置添加引号和逗号
                        () => content.substring(0, errorPos) + '\",' + content.substring(errorPos)
                    ];
                    
                    // 尝试每种修复策略
                    for (let i = 0; i < strategies.length; i++) {
                        try {
                            const strategyText = strategies[i]();
                            console.log(`尝试修复策略 ${i + 1}`);
                            
                            // 验证修复后的内容
                            JSON.parse(strategyText);
                            console.log(`修复策略 ${i + 1} 成功！`);
                            
                            // 创建备份
                            const backupPath = `${filePath}.bak`;
                            fs.writeFileSync(backupPath, content);
                            console.log(`原始文件已备份为: ${backupPath}`);
                            
                            // 保存修复后的内容
                            fs.writeFileSync(filePath, strategyText);
                            console.log(`修复后的内容已保存到: ${filePath}`);
                            
                            return;
                        } catch (e3) {
                            console.error(`修复策略 ${i + 1} 失败:`, e3.message);
                        }
                    }
                    
                    console.error('所有修复策略都失败，无法修复文件');
                }
            } else {
                // 对于其他类型的错误，尝试通用修复
                console.log('尝试通用JSON修复');
                
                const fixedContent = fixJsonString(content);
                
                try {
                    // 验证修复后的内容
                    JSON.parse(fixedContent);
                    console.log('通用修复成功！');
                    
                    // 创建备份
                    const backupPath = `${filePath}.bak`;
                    fs.writeFileSync(backupPath, content);
                    console.log(`原始文件已备份为: ${backupPath}`);
                    
                    // 保存修复后的内容
                    fs.writeFileSync(filePath, fixedContent);
                    console.log(`修复后的内容已保存到: ${filePath}`);
                } catch (e2) {
                    console.error('通用修复失败:', e2.message);
                    console.error('无法修复文件');
                }
            }
        }
    } catch (e) {
        console.error('读取文件失败:', e.message);
    }
}

// 主函数
function main() {
    // 获取命令行参数
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('用法: node fix-json-position-4476.js <文件路径>');
        return;
    }
    
    const filePath = args[0];
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
        console.error(`错误: 文件 "${filePath}" 不存在`);
        return;
    }
    
    // 检查是否是文件
    const stats = fs.statSync(filePath);
    if (!stats.isFile()) {
        console.error(`错误: "${filePath}" 不是一个文件`);
        return;
    }
    
    // 修复文件
    fixJsonFile(filePath);
}

// 执行主函数
main();
