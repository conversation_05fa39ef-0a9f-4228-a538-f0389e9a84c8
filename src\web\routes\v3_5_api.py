"""
九猫小说分析写作系统v3.5 - API路由
"""
import logging
import os
import json
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
import difflib
import re

from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.preset import Preset
from src.api.deepseek_client import DeepSeekClient
from src.api.analysis import NovelAnalyzer

logger = logging.getLogger(__name__)

v3_5_api_bp = Blueprint('v3_5_api', __name__, url_prefix='/v3.5/api')

# 创建API客户端和分析器
api_client = DeepSeekClient()
analyzer = NovelAnalyzer(api_client)

@v3_5_api_bp.route('/novels', methods=['GET'])
def get_novels():
    """
    获取小说列表

    响应:
    {
        "success": 是否成功,
        "novels": 小说列表
    }
    """
    try:
        session = Session()
        try:
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()

            # 转换为JSON格式
            novels_json = []
            for novel in novels:
                novels_json.append({
                    'id': novel.id,
                    'title': novel.title,
                    'author': novel.author,
                    'word_count': novel.word_count,
                    'created_at': novel.created_at.isoformat() if novel.created_at else None,
                    'updated_at': novel.updated_at.isoformat() if novel.updated_at else None
                })

            return jsonify({
                'success': True,
                'novels': novels_json
            })
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_5_api_bp.route('/auto_write', methods=['POST'])
def auto_write():
    """
    自动写作

    请求体:
    {
        "template_id": 预设模板ID,
        "prompt": 写作提示
    }

    响应:
    {
        "success": 是否成功,
        "content": 生成的内容
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        template_id = data.get('template_id')
        if not template_id:
            return jsonify({
                'success': False,
                'error': '预设模板ID不能为空'
            }), 400

        prompt = data.get('prompt', '')

        logger.info(f"开始自动写作: 模板ID={template_id}, 提示={prompt}")

        # 模拟自动写作过程
        session = Session()
        try:
            # 获取预设模板
            preset = session.query(Preset).get(template_id)
            if not preset:
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设模板'
                }), 404

            # 获取参考蓝本
            template = None
            if preset.meta_info and preset.meta_info.get('template_id'):
                template_id = preset.meta_info.get('template_id')
                template = session.query(Novel).get(template_id)

            # 生成内容
            content = f"# 基于《{preset.title}》的自动写作\n\n"
            content += f"## 写作提示\n{prompt}\n\n"
            content += f"## 生成内容\n"
            content += f"这是基于预设模板《{preset.title}》"

            if template:
                content += f"和参考蓝本《{template.title}》"

            content += "生成的内容。\n\n"
            content += "自动写作功能正在开发中，敬请期待！\n\n"
            content += "您可以在此处查看完整的预设模板内容，并根据需要进行修改。"

            # 构建响应数据
            response_data = {
                'success': True,
                'content': {
                    'title': f"基于《{preset.title}》的自动写作",
                    'content': content,
                    'original_text': template.content if template else ""
                }
            }

            logger.info(f"自动写作完成: 模板ID={template_id}")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"自动写作时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_5_api_bp.route('/compare_texts', methods=['POST'])
def compare_texts():
    """
    比较两段文本，找出相似部分

    请求体:
    {
        "text1": 第一段文本,
        "text2": 第二段文本,
        "similarity_threshold": 相似度阈值(可选，默认0.7)
    }

    响应:
    {
        "success": 是否成功,
        "comparison_result": {
            "similarity_score": 整体相似度分数,
            "similar_segments": [
                {
                    "text1_start": 文本1中的起始位置,
                    "text1_end": 文本1中的结束位置,
                    "text1_segment": 文本1中的相似片段,
                    "text2_start": 文本2中的起始位置,
                    "text2_end": 文本2中的结束位置,
                    "text2_segment": 文本2中的相似片段,
                    "similarity": 相似度分数
                },
                ...
            ]
        }
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        text1 = data.get('text1', '')
        text2 = data.get('text2', '')
        similarity_threshold = float(data.get('similarity_threshold', 0.7))

        if not text1 or not text2:
            return jsonify({
                'success': False,
                'error': '两段文本都不能为空'
            }), 400

        logger.info(f"开始比较文本: 文本1长度={len(text1)}, 文本2长度={len(text2)}")

        # 将文本分割成段落
        paragraphs1 = re.split(r'\n+', text1)
        paragraphs2 = re.split(r'\n+', text2)

        # 计算整体相似度
        similarity_score = difflib.SequenceMatcher(None, text1, text2).ratio()

        # 查找相似段落
        similar_segments = []
        for i, p1 in enumerate(paragraphs1):
            if len(p1.strip()) < 10:  # 忽略太短的段落
                continue

            for j, p2 in enumerate(paragraphs2):
                if len(p2.strip()) < 10:  # 忽略太短的段落
                    continue

                # 计算段落相似度
                similarity = difflib.SequenceMatcher(None, p1, p2).ratio()

                if similarity >= similarity_threshold:
                    # 计算在原文中的位置
                    text1_start = text1.find(p1)
                    text1_end = text1_start + len(p1)
                    text2_start = text2.find(p2)
                    text2_end = text2_start + len(p2)

                    similar_segments.append({
                        'text1_start': text1_start,
                        'text1_end': text1_end,
                        'text1_segment': p1,
                        'text2_start': text2_start,
                        'text2_end': text2_end,
                        'text2_segment': p2,
                        'similarity': similarity
                    })

        # 按相似度排序
        similar_segments.sort(key=lambda x: x['similarity'], reverse=True)

        # 构建响应数据
        response_data = {
            'success': True,
            'comparison_result': {
                'similarity_score': similarity_score,
                'similar_segments': similar_segments
            }
        }

        logger.info(f"文本比较完成: 相似度={similarity_score:.2f}, 找到{len(similar_segments)}个相似片段")
        return jsonify(response_data), 200
    except Exception as e:
        logger.error(f"比较文本时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_5_api_bp.route('/novel/<int:novel_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_novel_reasoning_content(novel_id, dimension):
    """
    获取小说分析推理过程
    """
    try:
        logger.info(f"获取小说分析推理过程 [小说ID: {novel_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到小说分析结果 [小说ID: {novel_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'dimension': dimension,
                'content': result.reasoning_content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }

            logger.info(f"成功获取小说分析推理过程 [小说ID: {novel_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说分析推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

@v3_5_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_chapter_reasoning_content(novel_id, chapter_id, dimension):
    """
    获取章节分析推理过程
    """
    try:
        session = Session()
        try:
            from src.models.chapter_analysis_result import ChapterAnalysisResult
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()
            if not result:
                return jsonify({'success': False, 'error': '未找到章节分析结果'}), 404
            return jsonify({
                'success': True,
                'dimension': dimension,
                'content': result.reasoning_content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500
