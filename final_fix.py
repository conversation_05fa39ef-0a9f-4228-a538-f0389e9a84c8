# 最终修复v3_routes.py文件
file_path = 'src/web/routes/v3_routes.py'

# 读取文件内容
with open(file_path, 'r', encoding='utf-8') as f:
    content = f.read()

# 创建备份
with open(file_path + '.final_backup', 'w', encoding='utf-8') as f:
    f.write(content)
print(f"已创建备份文件: {file_path}.final_backup")

# 找到并替换指定部分的代码
problem_code = """                                    break
            except Exception as e:
                    logger.error(f"从内容中解析参考蓝本ID或名称失败: {str(e)}")"""

fixed_code = """                                    break
                except Exception as e:
                    logger.error(f"从内容中解析参考蓝本ID或名称失败: {str(e)}")"""

if problem_code in content:
    # 替换并写回文件
    fixed_content = content.replace(problem_code, fixed_code)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    print("成功修复文件")
else:
    print("未能找到确切的问题代码，尝试按行修复")
    
    # 如果无法找到完整代码块，尝试按行修复
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if i > 520 and i < 523 and "except Exception as e:" in line:
            old_line = line
            # 确保缩进级别为16个空格
            new_line = " " * 16 + "except Exception as e:"
            lines[i] = new_line
            print(f"修复第{i+1}行: 从'{old_line}'到'{new_line}'")
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            print("按行修复完成")
            break
    else:
        print("未能找到except行进行修复") 