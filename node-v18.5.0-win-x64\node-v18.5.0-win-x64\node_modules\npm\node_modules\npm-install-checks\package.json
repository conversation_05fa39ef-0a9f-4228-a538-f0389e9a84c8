{"name": "npm-install-checks", "version": "5.0.0", "description": "Check the engines and platform fields in package.json", "main": "lib/index.js", "dependencies": {"semver": "^7.1.1"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "tap": "^16.0.1"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "https://github.com/npm/npm-install-checks.git"}, "keywords": ["npm,", "install"], "license": "BSD-2-<PERSON><PERSON>", "files": ["bin/", "lib/"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "author": "GitHub Inc.", "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}}