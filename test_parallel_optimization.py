#!/usr/bin/env python3
"""
九猫系统并行优化测试脚本
用于验证串行化瓶颈破除的效果
"""

import sys
import os
import time
import logging
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_values():
    """测试配置值是否正确更新"""
    try:
        import config
        
        logger.info("=== 配置值测试 ===")
        logger.info(f"MAX_PARALLEL_ANALYSES: {config.MAX_PARALLEL_ANALYSES}")
        logger.info(f"MAX_CHUNK_WORKERS: {config.MAX_CHUNK_WORKERS}")
        logger.info(f"PARALLEL_ANALYSIS_ENABLED: {config.PARALLEL_ANALYSIS_ENABLED}")
        
        # 验证配置值
        assert config.MAX_PARALLEL_ANALYSES >= 8, f"MAX_PARALLEL_ANALYSES应该>=8，实际值: {config.MAX_PARALLEL_ANALYSES}"
        assert config.MAX_CHUNK_WORKERS >= 12, f"MAX_CHUNK_WORKERS应该>=12，实际值: {config.MAX_CHUNK_WORKERS}"
        assert config.PARALLEL_ANALYSIS_ENABLED == True, "PARALLEL_ANALYSIS_ENABLED应该为True"
        
        logger.info("✅ 配置值测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置值测试失败: {str(e)}")
        return False

def test_parallel_optimization_config():
    """测试并行优化配置"""
    try:
        from src.config.parallel_optimization_config import ParallelOptimizationConfig
        
        logger.info("=== 并行优化配置测试 ===")
        
        # 测试精简版配置
        simplified_config = ParallelOptimizationConfig.get_optimization_config("simplified")
        logger.info(f"精简版 max_chunk_workers: {simplified_config['max_chunk_workers']}")
        logger.info(f"精简版 max_dimension_workers: {simplified_config['max_dimension_workers']}")
        
        # 测试默认版配置
        default_config = ParallelOptimizationConfig.get_optimization_config("default")
        logger.info(f"默认版 max_chunk_workers: {default_config['max_chunk_workers']}")
        logger.info(f"默认版 max_dimension_workers: {default_config['max_dimension_workers']}")
        
        # 验证配置值
        assert simplified_config['max_chunk_workers'] >= 8, "精简版块级并行度应该>=8"
        assert default_config['max_chunk_workers'] >= 12, "默认版块级并行度应该>=12"
        assert simplified_config['max_dimension_workers'] >= 8, "精简版维度级并行度应该>=8"
        assert default_config['max_dimension_workers'] >= 12, "默认版维度级并行度应该>=12"
        
        logger.info("✅ 并行优化配置测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 并行优化配置测试失败: {str(e)}")
        return False

def test_serialization_bottleneck_optimizer():
    """测试串行化瓶颈优化器"""
    try:
        from src.config.serialization_bottleneck_optimizer import SerializationBottleneckOptimizer
        
        logger.info("=== 串行化瓶颈优化器测试 ===")
        
        # 测试精简版优化器
        simplified_optimizer = SerializationBottleneckOptimizer("simplified")
        simplified_executor = simplified_optimizer.create_optimized_executor("test_task", 8)
        logger.info(f"精简版优化器创建成功，线程池: {simplified_executor}")
        
        # 测试默认版优化器
        default_optimizer = SerializationBottleneckOptimizer("default")
        default_executor = default_optimizer.create_optimized_executor("test_task", 12)
        logger.info(f"默认版优化器创建成功，线程池: {default_executor}")
        
        # 清理资源
        simplified_executor.shutdown(wait=False)
        default_executor.shutdown(wait=False)
        
        logger.info("✅ 串行化瓶颈优化器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 串行化瓶颈优化器测试失败: {str(e)}")
        return False

def test_thread_pool_performance():
    """测试线程池性能"""
    try:
        logger.info("=== 线程池性能测试 ===")
        
        def dummy_task(task_id):
            """模拟任务"""
            time.sleep(0.1)  # 模拟100ms的工作
            return f"Task {task_id} completed"
        
        # 测试不同并行度的性能
        test_cases = [
            ("旧版本（5线程）", 5),
            ("新版本精简版（8线程）", 8),
            ("新版本默认版（12线程）", 12)
        ]
        
        task_count = 20  # 20个任务
        
        for name, max_workers in test_cases:
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = [executor.submit(dummy_task, i) for i in range(task_count)]
                results = [future.result() for future in futures]
            
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info(f"{name}: {duration:.2f}秒 完成{task_count}个任务")
        
        logger.info("✅ 线程池性能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 线程池性能测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("开始九猫系统并行优化测试")
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("配置值测试", test_config_values()))
    test_results.append(("并行优化配置测试", test_parallel_optimization_config()))
    test_results.append(("串行化瓶颈优化器测试", test_serialization_bottleneck_optimizer()))
    test_results.append(("线程池性能测试", test_thread_pool_performance()))
    
    # 汇总结果
    logger.info("\n=== 测试结果汇总 ===")
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info(f"\n总计: {passed}个测试通过, {failed}个测试失败")
    
    if failed == 0:
        logger.info("🎉 所有测试通过！串行化瓶颈破除优化成功！")
        return True
    else:
        logger.error("⚠️ 部分测试失败，请检查优化实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
