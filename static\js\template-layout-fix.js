/**
 * 九猫系统模板详情页面布局修复脚本
 * 用于修复模板详情页面的布局问题，将单列布局改为两列布局
 */

(function() {
    console.log('[模板布局修复] 初始化...');

    // 等待DOM加载完成
    function onDOMReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    // 修复模板详情页面布局
    function fixTemplateLayout() {
        console.log('[模板布局修复] 开始修复模板详情页面布局...');

        // 检测是否是模板详情页面
        if (isTemplateDetailPage()) {
            console.log('[模板布局修复] 检测到模板详情页面');
            
            // 添加CSS样式
            addTemplateLayoutStyles();
            
            // 重构页面布局
            restructureTemplateLayout();
        } else {
            console.log('[模板布局修复] 当前页面不是模板详情页面');
        }
    }

    // 检测是否是模板详情页面
    function isTemplateDetailPage() {
        // 检查URL是否包含template或popular_tropes关键词
        const isTemplateURL = window.location.href.includes('template') || 
                             window.location.href.includes('popular_tropes');
        
        // 检查页面内容是否包含模板相关元素
        const hasTemplateElements = document.querySelector('.popular_tropes-container') !== null ||
                                   document.querySelector('[data-dimension="popular_tropes"]') !== null ||
                                   document.querySelector('.template-container') !== null ||
                                   document.querySelector('.template-settings') !== null;
        
        // 检查是否有特定的模板内容标记
        const hasTemplateContent = document.body.textContent.includes('popular_tropes') ||
                                  document.body.textContent.includes('热门桥段') ||
                                  document.body.textContent.includes('设定内容') ||
                                  document.body.textContent.includes('章节内容');
        
        return (isTemplateURL || hasTemplateElements || hasTemplateContent);
    }

    // 添加模板布局CSS样式
    function addTemplateLayoutStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            /* 模板详情页面两列布局 */
            .template-page-container {
                display: flex;
                width: 100%;
                min-height: 600px;
                background-color: #fffdf5;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
            
            /* 左侧导航/设置列 */
            .template-sidebar {
                width: 280px;
                background-color: #f8f9fa;
                padding: 20px;
                border-right: 1px solid #e9ecef;
                overflow-y: auto;
            }
            
            /* 右侧内容列 */
            .template-content {
                flex: 1;
                padding: 30px;
                overflow-y: auto;
                background-color: #fff;
            }
            
            /* 导航标题 */
            .template-sidebar h3 {
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                margin-top: 0;
                margin-bottom: 20px;
                font-size: 1.2rem;
            }
            
            /* 导航列表 */
            .template-nav {
                list-style-type: none;
                padding: 0;
                margin: 0 0 20px 0;
            }
            
            .template-nav-item {
                margin-bottom: 8px;
            }
            
            .template-nav-link {
                display: block;
                padding: 8px 12px;
                background-color: #fff;
                border-radius: 4px;
                color: #333;
                text-decoration: none;
                transition: all 0.2s ease;
            }
            
            .template-nav-link:hover {
                background-color: #e9ecef;
            }
            
            .template-nav-link.active {
                background-color: #3498db;
                color: #fff;
            }
            
            /* 设置区域 */
            .template-settings-section {
                margin-bottom: 20px;
                padding: 15px;
                background-color: #fff;
                border-radius: 8px;
            }
            
            .template-settings-section h4 {
                margin-top: 0;
                color: #2c3e50;
                font-size: 1rem;
            }
            
            /* 内容区域标题 */
            .template-content h2 {
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                margin-top: 0;
                margin-bottom: 20px;
            }
            
            /* 内容区域段落 */
            .template-content p {
                line-height: 1.6;
                margin-bottom: 15px;
            }
            
            /* 内容区域列表 */
            .template-content ul {
                padding-left: 20px;
                margin-bottom: 15px;
            }
            
            .template-content li {
                margin-bottom: 8px;
                line-height: 1.5;
            }
            
            /* 内容区域表单 */
            .template-content form {
                margin-bottom: 20px;
            }
            
            .template-content .form-group {
                margin-bottom: 15px;
            }
            
            .template-content label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            
            .template-content input,
            .template-content textarea,
            .template-content select {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            
            .template-content button {
                padding: 8px 16px;
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            
            .template-content button:hover {
                background-color: #2980b9;
            }
            
            /* 响应式布局 */
            @media (max-width: 768px) {
                .template-page-container {
                    flex-direction: column;
                }
                
                .template-sidebar {
                    width: 100%;
                    border-right: none;
                    border-bottom: 1px solid #e9ecef;
                }
            }
        `;
        
        document.head.appendChild(styleElement);
        console.log('[模板布局修复] 添加模板布局样式');
    }

    // 重构模板详情页面布局
    function restructureTemplateLayout() {
        console.log('[模板布局修复] 开始重构模板详情页面布局...');
        
        // 查找主要内容容器
        const mainContainer = findMainContainer();
        
        if (!mainContainer) {
            console.warn('[模板布局修复] 未找到主要内容容器，无法重构布局');
            return;
        }
        
        // 保存原始内容
        const originalContent = mainContainer.innerHTML;
        
        // 清空容器
        mainContainer.innerHTML = '';
        
        // 创建新的两列布局容器
        const templatePageContainer = document.createElement('div');
        templatePageContainer.className = 'template-page-container';
        
        // 创建左侧导航/设置列
        const templateSidebar = document.createElement('div');
        templateSidebar.className = 'template-sidebar';
        
        // 创建右侧内容列
        const templateContent = document.createElement('div');
        templateContent.className = 'template-content';
        
        // 解析原始内容，提取导航/设置和内容
        const { navItems, settingsItems, contentItems } = parseOriginalContent(originalContent);
        
        // 填充左侧导航/设置列
        if (navItems.length > 0) {
            const navTitle = document.createElement('h3');
            navTitle.textContent = '导航';
            templateSidebar.appendChild(navTitle);
            
            const navList = document.createElement('ul');
            navList.className = 'template-nav';
            
            navItems.forEach(item => {
                navList.appendChild(item);
            });
            
            templateSidebar.appendChild(navList);
        }
        
        // 填充左侧设置区域
        if (settingsItems.length > 0) {
            const settingsTitle = document.createElement('h3');
            settingsTitle.textContent = '设置';
            templateSidebar.appendChild(settingsTitle);
            
            settingsItems.forEach(item => {
                const settingsSection = document.createElement('div');
                settingsSection.className = 'template-settings-section';
                settingsSection.appendChild(item);
                templateSidebar.appendChild(settingsSection);
            });
        }
        
        // 填充右侧内容列
        if (contentItems.length > 0) {
            contentItems.forEach(item => {
                templateContent.appendChild(item);
            });
        } else {
            // 如果没有提取到内容，将原始内容放入右侧
            const contentWrapper = document.createElement('div');
            contentWrapper.innerHTML = originalContent;
            templateContent.appendChild(contentWrapper);
        }
        
        // 将两列添加到容器中
        templatePageContainer.appendChild(templateSidebar);
        templatePageContainer.appendChild(templateContent);
        
        // 将新布局添加到主容器中
        mainContainer.appendChild(templatePageContainer);
        
        console.log('[模板布局修复] 模板详情页面布局重构完成');
    }

    // 查找主要内容容器
    function findMainContainer() {
        // 尝试查找常见的主容器选择器
        const containerSelectors = [
            '.container', 
            '.container-fluid', 
            '.content-container', 
            '.main-content',
            '.template-container',
            '.popular_tropes-container',
            '[data-dimension="popular_tropes"]',
            '#content',
            'main'
        ];
        
        for (const selector of containerSelectors) {
            const container = document.querySelector(selector);
            if (container) {
                return container;
            }
        }
        
        // 如果找不到常见容器，尝试查找包含模板内容的容器
        const allDivs = document.querySelectorAll('div');
        for (const div of allDivs) {
            if (div.textContent.includes('popular_tropes') || 
                div.textContent.includes('热门桥段') ||
                div.textContent.includes('设定内容') ||
                div.textContent.includes('章节内容')) {
                
                // 确保找到的是父容器，而不是内容本身
                if (div.children.length > 2 && div.offsetWidth > 300) {
                    return div;
                }
            }
        }
        
        // 如果还是找不到，返回body作为最后的选择
        return document.body;
    }

    // 解析原始内容，提取导航/设置和内容
    function parseOriginalContent(originalContent) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = originalContent;
        
        const navItems = [];
        const settingsItems = [];
        const contentItems = [];
        
        // 提取导航项
        const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headings.forEach(heading => {
            const navItem = document.createElement('li');
            navItem.className = 'template-nav-item';
            
            const navLink = document.createElement('a');
            navLink.className = 'template-nav-link';
            navLink.href = '#' + (heading.id || 'section-' + Math.random().toString(36).substr(2, 9));
            navLink.textContent = heading.textContent;
            
            navItem.appendChild(navLink);
            navItems.push(navItem);
        });
        
        // 提取设置项
        const settingsElements = tempDiv.querySelectorAll('.popular_tropes-settings, [data-settings="popular_tropes"], .template-settings');
        settingsElements.forEach(element => {
            settingsItems.push(element.cloneNode(true));
        });
        
        // 如果没有找到设置元素，尝试查找包含"设置"或"设定"的元素
        if (settingsItems.length === 0) {
            const allElements = tempDiv.querySelectorAll('*');
            for (const element of allElements) {
                if (element.textContent.includes('设置') || 
                    element.textContent.includes('设定') ||
                    element.textContent.includes('适用于') ||
                    element.textContent.includes('请在此处添加')) {
                    
                    // 确保找到的是设置容器，而不是内容本身
                    if (element.children.length > 0 && element.tagName !== 'BODY') {
                        settingsItems.push(element.cloneNode(true));
                    }
                }
            }
        }
        
        // 提取内容项
        const contentElements = tempDiv.querySelectorAll('.popular_tropes-content, .template-content, .content');
        contentElements.forEach(element => {
            contentItems.push(element.cloneNode(true));
        });
        
        // 如果没有找到内容元素，尝试查找包含"内容"或"建议"的元素
        if (contentItems.length === 0) {
            const allElements = tempDiv.querySelectorAll('*');
            for (const element of allElements) {
                if (element.textContent.includes('内容') || 
                    element.textContent.includes('建议') ||
                    element.textContent.includes('结构') ||
                    element.textContent.includes('开篇部分')) {
                    
                    // 确保找到的是内容容器，而不是导航或设置
                    if (element.children.length > 0 && 
                        !element.textContent.includes('设置') && 
                        !element.textContent.includes('设定') &&
                        element.tagName !== 'BODY') {
                        contentItems.push(element.cloneNode(true));
                    }
                }
            }
        }
        
        return { navItems, settingsItems, contentItems };
    }

    // 监听DOM变化，动态修复模板布局
    function observeDOM() {
        const observer = new MutationObserver(mutations => {
            let shouldFixLayout = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        
                        if (node.nodeType === 1) { // 元素节点
                            if (node.classList && (
                                node.classList.contains('popular_tropes-container') || 
                                node.getAttribute('data-dimension') === 'popular_tropes' ||
                                node.classList.contains('template-container') ||
                                node.classList.contains('template-settings')
                            )) {
                                shouldFixLayout = true;
                                break;
                            }
                        }
                    }
                }
            });
            
            if (shouldFixLayout) {
                fixTemplateLayout();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('[模板布局修复] DOM观察器已启动');
    }

    // 初始化
    onDOMReady(() => {
        fixTemplateLayout();
        observeDOM();
    });
})();
