/**
 * 九猫 - 脚本错误修复
 * 专门处理Script error和跨域脚本错误问题
 * 版本: 1.0.0
 */

(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._scriptErrorFixLoaded) {
        console.log('[脚本错误修复] 脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._scriptErrorFixLoaded = true;
    
    console.log('[脚本错误修复] 脚本已加载 - 版本1.0.0');
    
    // 保存原始方法，用于安全操作
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    const originalOnError = window.onerror;
    const originalOnUnhandledRejection = window.onunhandledrejection;

    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[脚本错误修复] ' + message);
            } catch (e) {
                // 忽略日志错误
        }
    }

    function safeError(message) {
        try {
            originalConsoleError.call(console, '[脚本错误修复] ' + message);
        } catch (e) {
            // 忽略日志错误
        }
    }

    function safeWarn(message) {
        try {
            originalConsoleWarn.call(console, '[脚本错误修复] ' + message);
        } catch (e) {
            // 忽略日志错误
            }
    }
    
    // 处理跨域脚本错误
    function handleCrossOriginErrors() {
        safeLog('设置跨域脚本错误处理');
        
        // 设置全局错误处理程序
        window.onerror = function(message, source, lineno, colno, error) {
            // 检查跨域脚本错误
            if (message === 'Script error.' || message === 'Script error' || 
                (message && typeof message === 'string' && message.includes('Script error'))) {
                
                safeWarn(`捕获到跨域脚本错误: "${message}" 位置: ${source || '未知'} ${lineno || ''}:${colno || ''}`);

                // 检查特定位置的错误
                if (source === '41:30' || (lineno === 41 && colno === 30) || 
                    source === '41:33' || (lineno === 41 && colno === 33)) {
                    safeWarn('捕获到41:30或41:33位置的特定错误，可能是浏览器扩展引起');
                    
                    // 特定位置的错误通常是浏览器扩展引起的，直接阻止传播
                    return true;
                }
                
                // 对于其他跨域错误，添加crossorigin属性
                fixScriptCrossOrigin();
                
                // 阻止错误传播
                return true;
            }

            // 检查浏览器扩展错误
            if (error && isExtensionError(error)) {
                safeWarn(`捕获到浏览器扩展错误: "${message}"`);
                return true;
            }

            // 其他错误交给原始处理程序
            if (typeof originalOnError === 'function') {
                return originalOnError.call(window, message, source, lineno, colno, error);
            }
            
            return false;
        };
        
        // 设置未处理的Promise错误处理程序
        window.onunhandledrejection = function(event) {
            const error = event.reason;
            
            // 检查是否是浏览器扩展引起的错误
            if (error && isExtensionError(error)) {
                safeWarn(`捕获到浏览器扩展引起的Promise错误: "${error.message || String(error)}"`);
                event.preventDefault();
                return true;
            }

            // 检查是否是跨域错误
            if (error && (error.message === 'Script error.' || error.message === 'Script error' || 
                (error.message && typeof error.message === 'string' && error.message.includes('Script error')))) {
                
                safeWarn(`捕获到跨域脚本Promise错误: "${error.message}"`);
                
                // 添加crossorigin属性
                fixScriptCrossOrigin();
                
                event.preventDefault();
                return true;
            }

            // 其他错误交给原始处理程序
            if (typeof originalOnUnhandledRejection === 'function') {
                return originalOnUnhandledRejection.call(window, event);
            }

            return false;
        };

        safeLog('已设置全局错误处理程序');
    }

    // 修复脚本的crossorigin属性
    function fixScriptCrossOrigin() {
        try {
            safeLog('修复脚本的crossorigin属性');
            
            // 处理现有脚本
            const scripts = document.querySelectorAll('script[src]');
            let fixed = 0;
            
            scripts.forEach(script => {
                if (!script.hasAttribute('crossorigin')) {
                    script.setAttribute('crossorigin', 'anonymous');
                    fixed++;
        }
            });

            safeLog(`已为${fixed}个现有脚本添加crossorigin属性`);
            
            // 处理现有样式表
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            let fixedLinks = 0;
            
            links.forEach(link => {
                if (!link.hasAttribute('crossorigin')) {
                    link.setAttribute('crossorigin', 'anonymous');
                    fixedLinks++;
                }
            });
            
            safeLog(`已为${fixedLinks}个现有样式表添加crossorigin属性`);

            // 修改资源加载方法，确保新添加的脚本也有crossorigin属性
            if (!window.__scriptLoadersFixed) {
                // 保存原始方法
                const originalCreateElement = document.createElement;
                
                // 重写createElement方法
                document.createElement = function(tagName) {
                    const element = originalCreateElement.call(document, tagName);
                    
                    if (tagName.toLowerCase() === 'script' || 
                        (tagName.toLowerCase() === 'link' && element.rel === 'stylesheet')) {
                        // 覆盖setAttribute方法
                        const originalSetAttribute = element.setAttribute;
                        element.setAttribute = function(name, value) {
                            const result = originalSetAttribute.call(this, name, value);
                            
                            // 如果设置了src或href属性，自动添加crossorigin
                            if ((name === 'src' || name === 'href') && !this.hasAttribute('crossorigin')) {
                                originalSetAttribute.call(this, 'crossorigin', 'anonymous');
                                safeLog(`为动态创建的${tagName}添加crossorigin属性`);
                            }
                            
                            return result;
                        };
                    }
                    
                    return element;
                };
                
                window.__scriptLoadersFixed = true;
                safeLog('已修改资源加载方法，确保新脚本添加crossorigin属性');
            }
        } catch (e) {
            safeError(`修复脚本crossorigin属性时出错: ${e.message}`);
        }
        }

    // 检查是否是浏览器扩展引起的错误
    function isExtensionError(error) {
        try {
            if (!error) return false;
            
            const errorString = String(error);
            const errorMessage = error.message || '';
            
            // 检查常见浏览器扩展错误模式
            return (
                // Chrome扩展消息通道错误
                errorMessage.includes('message channel closed') ||
                errorMessage.includes('asynchronous response') ||
                errorMessage.includes('A listener indicated an asynchronous response') ||
                
                // 扩展内容脚本错误
                errorString.includes('extension') ||
                errorMessage.includes('extension') ||
                
                // 跨域访问错误
                (errorMessage.includes('cross-origin') && errorMessage.includes('extension')) ||
                
                // 扩展被禁用错误
                errorMessage.includes('Extension context invalidated') ||
                
                // Firefox扩展错误
                errorMessage.includes('Message manager disconnected') ||
                
                // 常见的扩展引发的错误格式
                errorMessage === 'Script error.' && !error.stack
            );
        } catch (e) {
            safeError(`检查扩展错误时出错: ${e.message}`);
            return false;
        }
    }
    
    // 显示浏览器扩展警告
    function showExtensionWarning() {
        // 只在页面加载完成后显示警告
        window.addEventListener('load', function() {
            try {
                // 创建警告元素
                const warningDiv = document.createElement('div');
                warningDiv.style.cssText = 'position:fixed; bottom:10px; right:10px; max-width:300px; background:#fff8e1; ' +
                    'border:1px solid #ffe082; border-radius:4px; padding:10px; z-index:10000; font-size:12px; ' +
                    'box-shadow:0 2px 4px rgba(0,0,0,0.2); display:none;';
                
                warningDiv.innerHTML = `
                    <div style="font-weight:bold; margin-bottom:5px;">浏览器扩展可能导致页面问题</div>
                    <p style="margin:0 0 10px 0;">检测到页面上有跨域脚本错误，这通常是由浏览器扩展引起的。</p>
                    <p style="margin:0 0 10px 0;">如果页面功能异常，请尝试禁用浏览器扩展或使用无痕模式访问。</p>
                    <button id="extension-warning-close" style="background:#ff9800; border:none; color:white; padding:5px 10px; border-radius:3px; cursor:pointer;">
                        我知道了
                    </button>
                `;
                
                document.body.appendChild(warningDiv);
                
                // 添加关闭按钮事件
                document.getElementById('extension-warning-close').addEventListener('click', function() {
                    warningDiv.style.display = 'none';
                    
                    // 存储用户已关闭提示的状态
                    try {
                        localStorage.setItem('extension_warning_dismissed', 'true');
                    } catch (e) {
                        // 忽略localStorage错误
                    }
                });
                
                // 检查是否之前已关闭提示
                let shouldShow = true;
                try {
                    shouldShow = localStorage.getItem('extension_warning_dismissed') !== 'true';
                } catch (e) {
                    // 忽略localStorage错误
                }
                
                // 只有在捕获到指定错误时才显示警告
                if (shouldShow && window.__capturedCrossOriginErrors) {
                    warningDiv.style.display = 'block';
                }
            } catch (e) {
                safeError(`显示扩展警告时出错: ${e.message}`);
            }
        });
    }
    
    // 监听错误事件
    function setupErrorListener() {
        // 已有全局处理程序，此处仅记录
        window.addEventListener('error', function(event) {
            // 检查是否是跨域脚本错误
            if (event.message === 'Script error.' || event.message === 'Script error' || 
                (event.message && typeof event.message === 'string' && event.message.includes('Script error'))) {
                
                // 标记已捕获跨域错误
                window.__capturedCrossOriginErrors = true;
                
                // 给用户一个明确的提示
                if (event.filename === '41:30' || (event.lineno === 41 && event.colno === 30) || 
                    event.filename === '41:33' || (event.lineno === 41 && event.colno === 33)) {
                    safeWarn('确认捕获到41:30或41:33位置的特定错误，很可能是浏览器扩展引起');
                }
            }
        }, true);
        
        safeLog('已设置错误事件监听器');
    }
    
    // 初始化函数
    function initialize() {
        safeLog('开始初始化');
        
        // 处理跨域脚本错误
        handleCrossOriginErrors();
        
        // 初始修复脚本crossorigin属性
        fixScriptCrossOrigin();
        
        // 设置错误监听
        setupErrorListener();
        
        // 显示浏览器扩展警告
        showExtensionWarning();
        
        safeLog('初始化完成');
    }
    
    // 立即执行初始化
    initialize();
    
    // DOM加载完成后再次检查
    document.addEventListener('DOMContentLoaded', function() {
        safeLog('DOM已加载，再次修复脚本');
        fixScriptCrossOrigin();
    });
    
    // 导出公共API，方便调试和手动调用
    window.scriptErrorFix = {
        fix: fixScriptCrossOrigin,
        isExtensionError: isExtensionError
    };
})();
