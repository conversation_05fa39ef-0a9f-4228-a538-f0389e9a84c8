#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
九猫系统图表修复工具
修复维度详情页面图表显示问题
"""

import os
import sys
import shutil
import time
from datetime import datetime
import json

def main():
    print("="*50)
    print("九猫系统图表修复工具")
    print("修复维度详情页面图表显示问题")
    print("="*50)
    print()

    # 检查路径
    base_dir = os.path.dirname(os.path.abspath(__file__))
    src_dir = os.path.join(base_dir, "src")
    web_dir = os.path.join(src_dir, "web")
    static_dir = os.path.join(web_dir, "static")
    js_dir = os.path.join(static_dir, "js")
    lib_dir = os.path.join(js_dir, "lib")
    templates_dir = os.path.join(web_dir, "templates")
    backup_dir = os.path.join(base_dir, "backup")

    # 创建必要的目录
    for directory in [backup_dir, lib_dir]:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")

    # 创建备份文件名后缀
    backup_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 备份文件
    files_to_backup = [
        os.path.join(templates_dir, "base.html"),
        os.path.join(js_dir, "universal-chart-fix-enhanced.js") if os.path.exists(os.path.join(js_dir, "universal-chart-fix-enhanced.js")) else None,
        os.path.join(js_dir, "dimension-detail-fix-enhanced.js") if os.path.exists(os.path.join(js_dir, "dimension-detail-fix-enhanced.js")) else None,
        os.path.join(js_dir, "global-error-handler.js") if os.path.exists(os.path.join(js_dir, "global-error-handler.js")) else None,
    ]

    # 执行备份
    print("备份文件中...")
    for file_path in files_to_backup:
        if file_path and os.path.exists(file_path):
            filename = os.path.basename(file_path)
            backup_path = os.path.join(backup_dir, f"{filename}.{backup_suffix}")
            try:
                shutil.copy2(file_path, backup_path)
                print(f"备份文件: {file_path} -> {backup_path}")
            except Exception as e:
                print(f"备份文件失败: {file_path} - {str(e)}")

    # 确保库文件存在
    print("\n检查库文件...")
    lib_files = {
        "chart.min.js": os.path.join(lib_dir, "chart.min.js"),
        "jquery.min.js": os.path.join(lib_dir, "jquery.min.js"),
        "bootstrap.bundle.min.js": os.path.join(lib_dir, "bootstrap.bundle.min.js")
    }

    for filename, filepath in lib_files.items():
        # 检查lib目录中是否有文件
        if not os.path.exists(filepath):
            # 检查根js目录是否有文件
            js_filepath = os.path.join(js_dir, filename)
            if os.path.exists(js_filepath):
                try:
                    shutil.copy2(js_filepath, filepath)
                    print(f"移动文件: {js_filepath} -> {filepath}")
                except Exception as e:
                    print(f"移动文件失败: {js_filepath} - {str(e)}")
            else:
                print(f"警告: 未找到库文件 {filename}，可能需要手动解决")

    # 复制修复脚本
    print("\n部署修复脚本...")
    fix_scripts = {
        "universal-chart-fix-enhanced.js": os.path.join(base_dir, "universal-chart-fix-enhanced.js"),
        "dimension-detail-fix-enhanced.js": os.path.join(base_dir, "dimension-detail-fix-enhanced.js"),
        "global-error-handler.js": os.path.join(base_dir, "global-error-handler.js"),
    }

    for filename, source_path in fix_scripts.items():
        if os.path.exists(source_path):
            try:
                target_path = os.path.join(js_dir, filename)
                shutil.copy2(source_path, target_path)
                print(f"复制脚本: {source_path} -> {target_path}")
            except Exception as e:
                print(f"复制脚本失败: {source_path} - {str(e)}")
        else:
            print(f"警告: 修复脚本 {filename} 不存在")
    
    # 修改base.html文件
    base_html_path = os.path.join(templates_dir, "base.html")
    if os.path.exists(base_html_path):
        print("\n修改模板文件...")
        try:
            with open(base_html_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 查找并替换脚本引用
            script_pattern = '<!-- 脚本加载 -->[\\s\\S]*?<script src="/static/js/jquery.min.js"></script>[\\s\\S]*?<script src="/static/js/bootstrap.bundle.min.js"></script>'
            script_replacement = '''<!-- 基础JS库 -->
    <script src="{{ url_for('static', filename='js/lib/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/lib/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/lib/chart.min.js') }}"></script>
    
    <!-- 九猫系统核心修复脚本 -->
    <script src="{{ url_for('static', filename='js/emergency-resource-loader.js') }}"></script>
    <script src="{{ url_for('static', filename='js/universal-chart-fix-enhanced.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dimension-detail-fix-enhanced.js') }}"></script>
    <script src="{{ url_for('static', filename='js/global-error-handler.js') }}"></script>'''

            if script_pattern in content:
                new_content = content.replace(script_pattern, script_replacement)
                with open(base_html_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                print(f"更新模板文件: {base_html_path}")
            else:
                print(f"警告: 无法在模板文件中找到脚本引用，可能需要手动修改")
                # 尝试简单替换
                if '<!-- 脚本加载 -->' in content and '<script src="/static/js/jquery.min.js"></script>' in content:
                    new_content = content.replace('<!-- 脚本加载 -->', '<!-- 基础JS库 -->')
                    new_content = new_content.replace('<script src="/static/js/jquery.min.js"></script>', '<script src="{{ url_for(\'static\', filename=\'js/lib/jquery.min.js\') }}"></script>')
                    new_content = new_content.replace('<script src="/static/js/bootstrap.bundle.min.js"></script>', 
                        '''<script src="{{ url_for('static', filename='js/lib/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/lib/chart.min.js') }}"></script>
    
    <!-- 九猫系统核心修复脚本 -->
    <script src="{{ url_for('static', filename='js/emergency-resource-loader.js') }}"></script>
    <script src="{{ url_for('static', filename='js/universal-chart-fix-enhanced.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dimension-detail-fix-enhanced.js') }}"></script>
    <script src="{{ url_for('static', filename='js/global-error-handler.js') }}"></script>''')
                    
                    with open(base_html_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    print(f"使用替代方法更新模板文件: {base_html_path}")
        except Exception as e:
            print(f"修改模板文件失败: {str(e)}")
    else:
        print(f"警告: 模板文件 {base_html_path} 不存在")

    print("\n"+"="*50)
    print("修复完成！")
    print("\n修复内容:")
    print("1. 增强维度详情页面显示")
    print("2. 修复图表加载问题")
    print("3. 优化内存使用")
    print("4. 添加全局错误处理")
    print("\n如有问题请查看日志或联系技术支持")
    print("="*50)
    
    print("\n提示: 请在使用前清理浏览器缓存，确保加载最新的脚本文件。")
    input("\n按回车键退出...")

if __name__ == "__main__":
    main() 