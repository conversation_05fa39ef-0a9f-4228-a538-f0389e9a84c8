"""
修复 /api/novel/{novel_id}/sync_to_chapters API 端点
"""
import os
import sys
import logging
import traceback
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'fix_sync_to_chapters_api_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def fix_api_endpoint():
    """修复 sync_to_chapters API 端点"""
    logger.info("开始修复 sync_to_chapters API 端点")
    
    # 检查 novel_routes.py 文件
    novel_routes_path = "src/web/routes/novel_routes.py"
    if not os.path.exists(novel_routes_path):
        logger.error(f"找不到文件: {novel_routes_path}")
        return False
    
    # 读取文件内容
    with open(novel_routes_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经存在 sync_to_chapters API 端点
    if '@novel_bp.route(\'/api/novel/<int:novel_id>/sync_to_chapters\', methods=[\'POST\'])' in content:
        logger.info("API 端点已存在于 novel_routes.py 中")
    else:
        logger.error("API 端点不存在于 novel_routes.py 中，需要添加")
        return False
    
    # 检查 app.py 文件
    app_path = "src/web/app.py"
    if not os.path.exists(app_path):
        logger.error(f"找不到文件: {app_path}")
        return False
    
    # 读取 app.py 文件内容
    with open(app_path, 'r', encoding='utf-8') as f:
        app_content = f.read()
    
    # 检查是否已经注册了 novel_bp 蓝图
    if "from src.web.routes.novel_routes import novel_bp" in app_content and "app.register_blueprint(novel_bp)" in app_content:
        logger.info("novel_bp 蓝图已在 app.py 中注册")
    else:
        logger.error("novel_bp 蓝图未在 app.py 中注册，需要添加")
        return False
    
    # 创建直接路由处理函数
    logger.info("创建直接路由处理函数")
    
    # 备份 app.py 文件
    backup_path = f"{app_path}.sync_api.bak"
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(app_content)
    logger.info(f"已创建备份文件: {backup_path}")
    
    # 添加直接路由处理函数
    direct_route = """
# 直接路由处理函数，用于修复 sync_to_chapters API 端点
@app.route('/api/novel/<int:novel_id>/sync_to_chapters', methods=['POST'])
def direct_sync_to_chapters(novel_id):
    \"\"\"
    直接路由处理函数，将请求转发到 novel_bp 蓝图中的处理函数
    \"\"\"
    from src.web.routes.novel_routes import api_sync_novel_analysis_to_chapters
    return api_sync_novel_analysis_to_chapters(novel_id)
"""
    
    # 在 app.py 文件中添加直接路由处理函数
    insert_position = app_content.find("if __name__ == '__main__':")
    if insert_position == -1:
        insert_position = len(app_content)
    
    new_app_content = app_content[:insert_position] + direct_route + app_content[insert_position:]
    
    # 写入修改后的 app.py 文件
    with open(app_path, 'w', encoding='utf-8') as f:
        f.write(new_app_content)
    
    logger.info("已添加直接路由处理函数")
    
    # 创建测试脚本
    test_script_path = "test_sync_api.py"
    test_script = """
\"\"\"
测试 sync_to_chapters API 端点
\"\"\"
import requests
import json

def test_sync_api():
    \"\"\"测试 sync_to_chapters API 端点\"\"\"
    url = "http://localhost:5001/api/novel/40/sync_to_chapters"
    headers = {
        "Content-Type": "application/json"
    }
    data = {
        "dimension": None  # 同步所有维度
    }
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("API 端点测试成功")
            return True
        else:
            print("API 端点测试失败")
            return False
    except Exception as e:
        print(f"测试时出错: {str(e)}")
        return False

if __name__ == "__main__":
    test_sync_api()
"""
    
    with open(test_script_path, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    logger.info(f"已创建测试脚本: {test_script_path}")
    
    logger.info("修复完成，请重启应用以应用更改")
    return True

if __name__ == "__main__":
    try:
        success = fix_api_endpoint()
        if success:
            logger.info("修复成功")
            sys.exit(0)
        else:
            logger.error("修复失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"修复过程中出错: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)
