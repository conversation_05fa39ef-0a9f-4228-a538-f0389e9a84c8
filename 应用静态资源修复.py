#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
九猫系统 - 静态资源修复应用脚本
该脚本用于将静态资源修复方案应用到系统中
"""
import os
import sys
import shutil
import logging
import re
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('static_resources_fix.log')
    ]
)
logger = logging.getLogger(__name__)

def ensure_directory_exists(directory):
    """确保目录存在"""
    try:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"确保目录存在: {directory}")
        return True
    except Exception as e:
        logger.error(f"创建目录时出错: {directory}, 错误: {str(e)}")
        return False

def copy_file(source, destination):
    """复制文件"""
    try:
        # 确保目标目录存在
        os.makedirs(os.path.dirname(destination), exist_ok=True)
        
        # 复制文件
        shutil.copy2(source, destination)
        logger.info(f"已复制文件: {source} -> {destination}")
        return True
    except Exception as e:
        logger.error(f"复制文件时出错: {source} -> {destination}, 错误: {str(e)}")
        return False

def inject_script_to_html_file(file_path, script_tag):
    """向HTML文件注入脚本标签"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含修复脚本
        if 'fix-static-resources.js' in content:
            logger.info(f"文件已包含修复脚本，跳过: {file_path}")
            return True
        
        # 在<head>标签后注入脚本
        if '<head>' in content:
            modified_content = content.replace('<head>', f'<head>\n    {script_tag}', 1)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            logger.info(f"已向文件注入修复脚本: {file_path}")
            return True
        else:
            logger.warning(f"文件中未找到<head>标签: {file_path}")
            return False
    except Exception as e:
        logger.error(f"向文件注入脚本时出错: {file_path}, 错误: {str(e)}")
        return False

def modify_app_py(file_path):
    """修改app.py文件，添加资源修复中间件"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含资源修复中间件
        if 'add_resource_fix_script' in content:
            logger.info(f"文件已包含资源修复中间件，跳过: {file_path}")
            return True
        
        # 查找合适的插入位置（在其他after_request装饰器之后）
        after_request_pattern = re.compile(r'(@app\.after_request\s+def\s+\w+\([^)]*\):.*?\n\s*return\s+response\s*\n)', re.DOTALL)
        match = after_request_pattern.search(content)
        
        if match:
            # 在最后一个after_request之后插入
            end_pos = match.end()
            middleware_code = """
@app.after_request
def add_resource_fix_script(response):
    \"\"\"在HTML响应中注入资源修复脚本\"\"\"
    if response.content_type and 'text/html' in response.content_type:
        # 只处理HTML响应
        html = response.get_data(as_text=True)
        
        # 在<head>标签后插入修复脚本
        inject_script = '<script src="/static/js/fix-static-resources.js" onerror="var s=document.createElement(\'script\');s.src=\'/direct-static/js/fix-static-resources.js\';document.head.appendChild(s);"></script>'
        if '<head>' in html:
            html = html.replace('<head>', '<head>' + inject_script, 1)
            response.set_data(html)
    return response
"""
            modified_content = content[:end_pos] + middleware_code + content[end_pos:]
        else:
            # 如果没有找到after_request，则在文件末尾添加
            modified_content = content + """
@app.after_request
def add_resource_fix_script(response):
    \"\"\"在HTML响应中注入资源修复脚本\"\"\"
    if response.content_type and 'text/html' in response.content_type:
        # 只处理HTML响应
        html = response.get_data(as_text=True)
        
        # 在<head>标签后插入修复脚本
        inject_script = '<script src="/static/js/fix-static-resources.js" onerror="var s=document.createElement(\'script\');s.src=\'/direct-static/js/fix-static-resources.js\';document.head.appendChild(s);"></script>'
        if '<head>' in html:
            html = html.replace('<head>', '<head>' + inject_script, 1)
            response.set_data(html)
    return response
"""
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        logger.info(f"已修改app.py文件，添加资源修复中间件: {file_path}")
        return True
    except Exception as e:
        logger.error(f"修改app.py文件时出错: {file_path}, 错误: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始应用静态资源修复...")
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 修复脚本路径
    fix_script_path = os.path.join(current_dir, 'fix-static-resources.js')
    
    # 检查修复脚本是否存在
    if not os.path.exists(fix_script_path):
        logger.error(f"修复脚本不存在: {fix_script_path}")
        return False
    
    # 静态文件目录
    static_dir = os.path.join(current_dir, 'src', 'web', 'static', 'js')
    direct_static_dir = os.path.join(current_dir, 'src', 'web', 'direct-static', 'js')
    
    # 确保目录存在
    ensure_directory_exists(static_dir)
    ensure_directory_exists(direct_static_dir)
    
    # 复制修复脚本到静态目录
    copy_file(fix_script_path, os.path.join(static_dir, 'fix-static-resources.js'))
    copy_file(fix_script_path, os.path.join(direct_static_dir, 'fix-static-resources.js'))
    
    # 查找所有HTML模板文件
    template_dir = os.path.join(current_dir, 'src', 'web', 'templates')
    if os.path.exists(template_dir):
        # 注入脚本标签
        script_tag = '<script src="{{ url_for(\'static\', filename=\'js/fix-static-resources.js\') }}" onerror="var s=document.createElement(\'script\');s.src=\'/direct-static/js/fix-static-resources.js\';document.head.appendChild(s);"></script>'
        
        # 处理base.html文件（如果存在）
        base_html_path = os.path.join(template_dir, 'base.html')
        if os.path.exists(base_html_path):
            inject_script_to_html_file(base_html_path, script_tag)
        
        # 处理其他可能的基础模板
        for base_template in ['new_base.html', 'full_base.html', 'v2/base.html']:
            template_path = os.path.join(template_dir, base_template)
            if os.path.exists(template_path):
                inject_script_to_html_file(template_path, script_tag)
    
    # 修改app.py文件
    app_py_path = os.path.join(current_dir, 'src', 'web', 'app.py')
    if os.path.exists(app_py_path):
        modify_app_py(app_py_path)
    
    logger.info("静态资源修复应用完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 