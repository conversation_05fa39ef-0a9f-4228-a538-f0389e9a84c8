"""
九猫系统结果缓存优化器
实现分析结果的智能缓存机制，减少重复分析
"""

import logging
import hashlib
import time
import pickle
import json
from typing import Dict, Any, Optional, List
from pathlib import Path
import re

logger = logging.getLogger(__name__)

class ResultCacheOptimizer:
    """结果缓存优化器，实现分析结果的智能缓存机制"""
    
    def __init__(self, prompt_template: str = "default"):
        """
        初始化优化器
        
        Args:
            prompt_template: 提示词模板
        """
        self.prompt_template = prompt_template
        self.config = self._load_optimization_config()
        
        # 初始化缓存目录
        self.cache_dir = Path("src/cache/result_cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 性能统计
        self.stats = {
            "cache_hits": 0,
            "cache_misses": 0,
            "cache_saves": 0,
            "tokens_saved": 0,
            "processing_time_saved": 0.0
        }
        
        logger.info(f"[结果缓存优化器] 初始化完成，使用模板: {prompt_template}")
    
    def _load_optimization_config(self) -> Dict[str, Any]:
        """加载优化配置"""
        # 默认配置
        config = {
            "cache_enabled": True,
            "similarity_threshold": 0.92 if self.prompt_template == "default" else 0.88,
            "max_cache_entries": 1000 if self.prompt_template == "default" else 500,
            "cache_expiration": 72 if self.prompt_template == "default" else 48,  # 小时
            "fingerprint_mode": "combined" if self.prompt_template == "default" else "hash",
            "enable_partial_match": self.prompt_template == "default"
        }
        
        # 可以从配置文件加载，这里简化实现，直接返回默认配置
        return config
    
    def optimize(self, analysis_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        优化分析请求
        
        Args:
            analysis_request: 分析请求
            
        Returns:
            优化后的分析请求
        """
        if not self.config.get("cache_enabled", True):
            return analysis_request
            
        # 仅对包含文本的请求进行优化
        if "text" not in analysis_request:
            return analysis_request
            
        text = analysis_request.get("text", "")
        dimension = analysis_request.get("dimension", "")
        
        if not text or not dimension:
            return analysis_request
            
        # 查找缓存
        cached_result = self._get_cached_result(text, dimension)
        
        # 如果找到缓存，添加到请求中
        if cached_result:
            logger.info(f"[结果缓存] 为维度{dimension}找到缓存结果，相似度: {cached_result.get('similarity', 0):.2f}")
            analysis_request["cached_result"] = cached_result
            analysis_request["use_cache"] = True
            
            # 更新统计信息
            self.stats["cache_hits"] += 1
            self.stats["tokens_saved"] += cached_result.get("tokens", 0)
            self.stats["processing_time_saved"] += cached_result.get("processing_time", 0)
        else:
            self.stats["cache_misses"] += 1
            
        return analysis_request
    
    def _get_cached_result(self, text: str, dimension: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的分析结果
        
        Args:
            text: 文本
            dimension: 分析维度
            
        Returns:
            缓存的结果或None
        """
        try:
            # 计算文本哈希
            text_hash = self._compute_text_hash(text)
            
            # 优先尝试精确匹配
            exact_cache_file = self.cache_dir / f"{dimension}_{text_hash}.pkl"
            
            if exact_cache_file.exists():
                # 加载缓存
                with open(exact_cache_file, "rb") as f:
                    cached_data = pickle.load(f)
                
                # 检查过期时间
                expiration = self.config.get("cache_expiration", 72) * 3600  # 转换为秒
                if time.time() - cached_data.get("timestamp", 0) > expiration:
                    return None
                    
                # 更新相似度
                cached_data["similarity"] = 1.0
                    
                return cached_data
            
            # 如果启用了部分匹配，尝试找到相似文本
            if self.config.get("enable_partial_match", True):
                # 获取所有缓存文件
                cache_files = list(self.cache_dir.glob(f"{dimension}_*.pkl"))
                
                # 寻找相似文本
                for cache_file in cache_files:
                    try:
                        with open(cache_file, "rb") as f:
                            cached_data = pickle.load(f)
                            
                        cached_text = cached_data.get("text", "")
                        
                        # 计算相似度
                        similarity = self._calculate_similarity(text, cached_text)
                        
                        # 如果相似度超过阈值，返回该缓存
                        if similarity >= self.config.get("similarity_threshold", 0.92):
                            cached_data["similarity"] = similarity
                            return cached_data
                    except Exception as e:
                        logger.warning(f"[结果缓存] 读取缓存文件失败: {e}")
                        continue
            
            return None
        except Exception as e:
            logger.warning(f"[结果缓存] 获取缓存失败: {e}")
            return None
    
    def save_to_cache(self, text: str, dimension: str, result: Dict[str, Any]) -> None:
        """
        将分析结果保存到缓存
        
        Args:
            text: 文本
            dimension: 分析维度
            result: 分析结果
        """
        try:
            # 清理过期缓存
            self._clean_expired_cache()
            
            # 执行缓存限制
            self._enforce_cache_limits()
            
            # 计算文本哈希
            text_hash = self._compute_text_hash(text)
            
            # 准备缓存数据
            cache_data = {
                "text": text,
                "dimension": dimension,
                "result": result,
                "timestamp": time.time(),
                "tokens": result.get("tokens_used", 0),
                "processing_time": result.get("processing_time", 0)
            }
            
            # 缓存文件路径
            cache_file = self.cache_dir / f"{dimension}_{text_hash}.pkl"
            
            # 保存缓存
            with open(cache_file, "wb") as f:
                pickle.dump(cache_data, f)
                
            # 更新统计信息
            self.stats["cache_saves"] += 1
            
            logger.info(f"[结果缓存] 已缓存维度{dimension}的分析结果")
        except Exception as e:
            logger.warning(f"[结果缓存] 保存缓存失败: {e}")
    
    def _compute_text_hash(self, text: str) -> str:
        """计算文本哈希"""
        return hashlib.md5(text.encode()).hexdigest()
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """
        计算文本相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度（0-1之间的值）
        """
        # 根据指纹模式选择不同的相似度计算方法
        if self.config.get("fingerprint_mode") == "combined":
            # 提取关键字和句子结构进行比较
            # 实际实现中应该使用更复杂的算法
            
            # 简化实现：基于关键词重叠率计算相似度
            words1 = set(re.findall(r'\w+', text1.lower()))
            words2 = set(re.findall(r'\w+', text2.lower()))
            
            if not words1 or not words2:
                return 0.0
                
            intersection = words1.intersection(words2)
            return len(intersection) / max(len(words1), len(words2))
        else:
            # 简单的长度比较
            ratio = min(len(text1), len(text2)) / max(len(text1), len(text2))
            return ratio * 0.8  # 长度相似度权重为0.8
    
    def _clean_expired_cache(self) -> None:
        """清理过期的缓存"""
        try:
            # 获取所有缓存文件
            cache_files = list(self.cache_dir.glob("*.pkl"))
            
            # 当前时间
            current_time = time.time()
            expiration = self.config.get("cache_expiration", 72) * 3600  # 转换为秒
            
            # 检查每个缓存文件
            for cache_file in cache_files:
                try:
                    # 获取文件修改时间
                    mtime = cache_file.stat().st_mtime
                    
                    # 如果文件过期，删除它
                    if current_time - mtime > expiration:
                        cache_file.unlink(missing_ok=True)
                        logger.debug(f"[结果缓存] 删除过期缓存: {cache_file.name}")
                except Exception as e:
                    logger.warning(f"[结果缓存] 清理缓存文件失败: {e}")
                    continue
        except Exception as e:
            logger.warning(f"[结果缓存] 清理过期缓存失败: {e}")
    
    def _enforce_cache_limits(self) -> None:
        """执行缓存限制"""
        try:
            # 获取所有缓存文件
            cache_files = list(self.cache_dir.glob("*.pkl"))
            
            # 检查是否超出限制
            max_entries = self.config.get("max_cache_entries", 1000)
            
            if len(cache_files) <= max_entries:
                return
                
            # 按修改时间排序
            cache_files.sort(key=lambda f: f.stat().st_mtime)
            
            # 删除最旧的文件
            for file in cache_files[:(len(cache_files) - max_entries)]:
                try:
                    file.unlink(missing_ok=True)
                    logger.debug(f"[结果缓存] 删除旧缓存以控制数量: {file.name}")
                except Exception as e:
                    logger.warning(f"[结果缓存] 删除缓存文件失败: {e}")
                    continue
                    
            logger.info(f"[结果缓存] 已删除{len(cache_files) - max_entries}个旧缓存文件")
        except Exception as e:
            logger.warning(f"[结果缓存] 执行缓存限制失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.stats 