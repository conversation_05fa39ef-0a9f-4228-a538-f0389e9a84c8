"""
九猫 - 单维度分析模块
提供单维度分析功能
"""

import logging
import time
import random
import os
from typing import Dict, Any, Optional

from src.models.novel import Novel
from src.models.analysis_result import AnalysisResult
from src.db.connection import Session
from dimension_analysis_templates import generate_dimension_analysis_template

logger = logging.getLogger(__name__)

def generate_analysis_content(novel: Novel, dimension: str) -> str:
    """
    生成分析结果内容

    Args:
        novel: 小说对象
        dimension: 分析维度

    Returns:
        分析结果内容
    """
    # 使用新的分析结果模板
    return generate_dimension_analysis_template(dimension, novel.title)

def generate_reasoning_content(novel: Novel, dimension: str) -> str:
    """
    生成推理过程内容

    Args:
        novel: 小说对象
        dimension: 分析维度

    Returns:
        推理过程内容
    """
    # 提取小说内容的一小部分用于示例
    excerpt = novel.get_excerpt(500)

    # 根据不同维度生成不同的推理过程
    reasoning_templates = {
        'language_style': f"""# 语言风格分析推理过程

## 1. 分析目标
分析小说《{novel.title}》的语言风格特点，包括词汇选择、句式结构、修辞手法等方面。

## 2. 文本样本分析
从小说中提取的样本：
```
{excerpt}
```

### 2.1 词汇分析
- 名词使用频率：中等
- 动词活跃度：较高
- 形容词丰富度：高
- 专业术语使用：适中

### 2.2 句式分析
- 平均句长：中等（15-20字）
- 长短句比例：3:2
- 复杂句使用频率：中等
- 特殊句式（如倒装句）：少量使用

### 2.3 修辞手法分析
- 比喻：频繁使用
- 拟人：适度使用
- 排比：少量使用
- 对比：适度使用

## 3. 风格特征归纳
根据以上分析，可以归纳出以下风格特征：
1. 语言简洁明了，表达直接
2. 善于使用生动的比喻和形象的描写
3. 对话自然流畅，符合人物性格
4. 词汇选择丰富多样，避免重复

## 4. 风格效果评估
该风格的效果：
- 增强了文本的可读性
- 提高了描写的生动性
- 强化了人物形象的塑造
- 增强了情感表达的力度

## 5. 结论
《{novel.title}》的语言风格独特，表现力强，为读者提供了良好的阅读体验。""",

        'rhythm_pacing': f"""# 节奏与节奏分析推理过程

## 1. 分析目标
分析小说《{novel.title}》的节奏特点，包括章节长度、情节推进速度、节奏变化技巧等方面。

## 2. 文本结构分析
### 2.1 章节长度统计
- 总章节数：估计20-30章
- 平均章节长度：约5000字
- 最长章节：约8000字（关键情节章节）
- 最短章节：约3000字（过渡性章节）

### 2.2 段落长度分析
- 平均段落长度：约100字
- 描写性段落：较长（150-200字）
- 对话性段落：较短（50-80字）
- 动作性段落：中等（80-120字）

## 3. 节奏变化分析
### 3.1 开篇节奏
从样本文本看：
```
{excerpt}
```
开篇节奏平缓，以环境和人物描写为主，为读者提供了进入故事的缓冲。

### 3.2 中段节奏
中段节奏起伏变化，通过以下方式实现：
- 对话与描写的交替使用
- 长短句的合理搭配
- 情节紧张度的有意识控制

### 3.3 结尾节奏
结尾节奏紧凑，通过以下方式实现：
- 短句增多，增强紧张感
- 动作描写增多，加快节奏
- 悬念设置，增强戏剧性

## 4. 节奏控制技巧分析
作者使用的主要节奏控制技巧：
1. 通过对话和动作描写加快节奏
2. 通过环境和心理描写放慢节奏
3. 通过悬念和伏笔控制节奏
4. 通过章节长度变化调整节奏

## 5. 结论
《{novel.title}》的节奏把握得当，既有紧张刺激的高潮，也有舒缓平和的过渡，为读者提供了良好的阅读体验。"""
    }

    # 如果有对应的模板，使用模板，否则使用默认模板
    if dimension in reasoning_templates:
        return reasoning_templates[dimension]
    else:
        return f"""# {dimension}分析推理过程

## 1. 分析目标
分析小说《{novel.title}》的{dimension}特点。

## 2. 文本样本分析
从小说中提取的样本：
```
{excerpt}
```

### 2.1 初步观察
- 观察点1：文本结构清晰
- 观察点2：表达方式多样
- 观察点3：内容丰富有深度

### 2.2 详细分析
- 分析点1：段落结构合理，逻辑性强
- 分析点2：语言表达准确，用词恰当
- 分析点3：情感表达真实，有感染力

## 3. 特征归纳
根据以上分析，可以归纳出以下特征：
1. 特征一：内容丰富，表现形式多样
2. 特征二：结构合理，逻辑性强
3. 特征三：风格独特，有个人特色

## 4. 效果评估
这些特征的效果：
- 增强了文本的可读性
- 提高了表达的准确性
- 强化了内容的感染力

## 5. 结论
《{novel.title}》在{dimension}方面表现良好，为读者提供了良好的阅读体验，但仍有提升空间。"""

def analyze_dimension(novel: Novel, dimension: str, analyzer=None, use_real_api=False) -> Optional[AnalysisResult]:
    """
    分析小说的单个维度

    Args:
        novel: 要分析的小说
        dimension: 分析维度
        analyzer: 可选的分析器对象，如果提供则使用此分析器
        use_real_api: 是否强制使用真实API

    Returns:
        分析结果对象，如果分析失败则返回None
    """
    logger.info(f"开始分析小说 ID:{novel.id} 的维度: {dimension}")

    start_time = time.time()

    # 创建新的会话，确保数据库操作的一致性
    session = Session()

    try:
        # 获取小说ID和其他必要信息，避免使用可能已分离的对象
        novel_id = novel.id

        # 记录传入对象的信息，以防后续需要使用
        original_title = getattr(novel, 'title', '未知标题')
        original_content = getattr(novel, 'content', '')
        original_author = getattr(novel, 'author', '未知作者')
        original_word_count = getattr(novel, 'word_count', len(original_content))

        logger.info(f"使用小说ID: {novel_id} 进行分析，标题: {original_title}")

        # 从数据库获取小说对象，确保使用当前会话中的对象
        db_novel = session.query(Novel).filter_by(id=novel_id).first()

        if not db_novel:
            logger.warning(f"数据库中无法找到小说 ID: {novel_id}，将使用传入的小说信息创建新对象")
            # 创建新的小说对象，使用从原始对象获取的信息
            db_novel = Novel(
                id=novel_id,
                title=original_title,
                content=original_content,
                author=original_author,
                word_count=original_word_count
            )

            # 尝试将临时小说对象添加到数据库
            try:
                session.add(db_novel)
                session.commit()
                logger.info(f"已将临时小说对象添加到数据库 ID: {novel_id}")
            except Exception as e:
                logger.error(f"将临时小说对象添加到数据库时出错: {str(e)}")
                # 回滚并继续使用临时对象
                session.rollback()

        # 获取小说标题和内容，避免后续使用分离对象时出错
        novel_title = db_novel.title
        novel_content = db_novel.content
        novel_word_count = db_novel.word_count if hasattr(db_novel, 'word_count') else len(db_novel.content)

        logger.info(f"开始分析小说 '{novel_title}' (ID:{novel_id}) 的维度: {dimension}")
        logger.info(f"小说内容长度: {len(novel_content)} 字符")

        # 检查是否已存在分析结果
        existing_result = session.query(AnalysisResult).filter_by(
            novel_id=novel_id,
            dimension=dimension
        ).first()

        # 对于chapter_outline和outline_analysis维度，强制重新分析，不使用缓存的结果
        if dimension in ["chapter_outline", "outline_analysis"]:
            if existing_result:
                logger.info(f"小说 '{novel_title}' (ID:{novel_id}) 的维度 {dimension} 已存在分析结果，但将强制重新分析")
                # 删除现有结果
                session.delete(existing_result)
                session.commit()
                existing_result = None
        elif existing_result:
            logger.info(f"小说 '{novel_title}' (ID:{novel_id}) 的维度 {dimension} 已存在分析结果，跳过分析")
            return existing_result

        # 强制使用真实API，忽略环境变量和参数
        use_real_api = True

        # 设置环境变量确保使用真实API
        os.environ['USE_REAL_API'] = 'true'
        os.environ['USE_MOCK_API'] = 'false'
        os.environ['ENABLE_MOCK_ANALYSIS'] = 'false'

        # 使用真实API进行分析
        logger.info(f"强制使用真实API分析小说 '{novel_title}' (ID:{novel_id}) 的维度: {dimension}")

        # 获取小说文本，确保有内容可分析
        novel_text = novel_content
        if not novel_text or len(novel_text.strip()) < 10:
            logger.error(f"小说 '{novel_title}' (ID:{novel_id}) 内容太短或为空")
            # 如果内容太少，也使用模拟数据
            template_content = generate_dimension_analysis_template(dimension, novel_title)
            content = f"""# 注意：这是模板内容，不是实际分析结果
# 小说内容太短，使用模板作为后备

{template_content}"""

            reasoning_content = generate_reasoning_content(db_novel, dimension)

            metadata = {
                "processing_time": time.time() - start_time,
                "word_count": novel_word_count,
                "timestamp": time.time(),
                "reasoning_content": reasoning_content,
                "using_real_api": False,
                "note": "内容太短，使用模拟数据",
                "is_template": True  # 明确标记这是模板内容
            }

            result = AnalysisResult(
                novel_id=novel_id,
                dimension=dimension,
                content=content,
                metadata=metadata,
                reasoning_content=reasoning_content
            )

            session.add(result)
            session.commit()

            logger.info(f"小说内容太短，使用模拟数据完成分析")

            return result

        # 使用提供的分析器或创建新的
        if analyzer is None:
            from src.api.deepseek_client import DeepSeekClient
            from src.api.analysis import NovelAnalyzer
            api_client = DeepSeekClient()
            analyzer = NovelAnalyzer(api_client=api_client)

        # 使用分析器分析文本
        try:
            logger.info(f"开始调用API分析小说 '{novel_title}' (ID:{novel_id}) 维度: {dimension}")
            result_data = analyzer.analyze_dimension(
                text=novel_text,
                dimension=dimension,
                title=novel_title
            )

            # 解析分析结果
            if not result_data or 'content' not in result_data:
                logger.error(f"API返回的数据格式不正确: {result_data}")
                raise ValueError(f"API返回的数据格式不正确: {result_data}")

            content = result_data['content']
            reasoning_content = result_data.get('reasoning', result_data.get('reasoning_content', "API未返回推理过程"))

            # 创建分析结果
            metadata = {
                "processing_time": time.time() - start_time,
                "word_count": novel_word_count,
                "timestamp": time.time(),
                "reasoning_content": reasoning_content,  # 将推理过程存储在元数据中
                "using_real_api": True  # 标记为使用真实API
            }

            # 使用API返回的实际内容，而不是模板
            # content = generate_dimension_analysis_template(dimension, novel_title)  # 注释掉这行，不再使用模板覆盖API结果

            # 创建分析结果
            result = AnalysisResult(
                novel_id=novel_id,
                dimension=dimension,
                content=content,  # 使用API返回的实际内容
                metadata=metadata,
                reasoning_content=reasoning_content
            )

            # 保存分析结果
            session.add(result)
            session.commit()

            logger.info(f"小说 '{novel_title}' (ID:{novel_id}) 的维度 {dimension} 真实API分析完成，耗时: {time.time() - start_time:.2f}秒")

            return result
        except Exception as api_error:
            logger.error(f"调用API分析小说 '{novel_title}' (ID:{novel_id}) 的维度 {dimension} 时出错: {str(api_error)}", exc_info=True)

            # API调用失败时，尝试使用模拟数据作为后备
            if not use_real_api:  # 如果不是强制使用真实API，则回退到模拟数据
                logger.info(f"API调用失败，使用模拟数据作为后备")

                # 在这种情况下，我们确实需要使用模板，因为API调用失败了
                # 但我们应该明确标记这是模板内容，而不是实际分析结果
                template_content = generate_dimension_analysis_template(dimension, novel_title)
                content = f"""# 注意：这是模板内容，不是实际分析结果
# API调用失败，使用模板作为后备

{template_content}"""

                reasoning_content = generate_reasoning_content(db_novel, dimension)

                metadata = {
                    "processing_time": time.time() - start_time,
                    "word_count": novel_word_count,
                    "timestamp": time.time(),
                    "reasoning_content": reasoning_content,
                    "using_real_api": False,
                    "api_error": str(api_error),
                    "is_template": True  # 明确标记这是模板内容
                }

                result = AnalysisResult(
                    novel_id=novel_id,
                    dimension=dimension,
                    content=content,
                    metadata=metadata,
                    reasoning_content=reasoning_content
                )

                session.add(result)
                session.commit()

                logger.info(f"使用模拟数据作为后备分析完成")

                return result
            else:
                # 如果强制使用真实API，则直接返回失败
                raise api_error

    except Exception as e:
        logger.error(f"分析小说 ID:{novel_id} 的维度 {dimension} 时出错: {str(e)}", exc_info=True)
        # 发生错误时回滚事务
        session.rollback()
        return None
    finally:
        # 确保会话始终关闭
        session.close()
