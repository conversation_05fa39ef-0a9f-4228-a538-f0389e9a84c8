#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建预设模板的脚本
"""

import os
import sys
import json
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模型
from src.models.preset import Preset
from src.models.novel import Novel
from src.models.chapter import Chapter
from config import DATABASE_URI, ANALYSIS_DIMENSIONS

# 创建数据库连接
engine = create_engine(DATABASE_URI)
Session = sessionmaker(bind=engine)
session = Session()

def create_preset_template():
    """创建预设模板"""
    try:
        # 检查是否已存在预设模板
        existing_preset = session.query(Preset).filter_by(category="preset_template").first()
        if existing_preset:
            print(f"已存在预设模板: ID={existing_preset.id}, 标题={existing_preset.title}")
            return existing_preset

        # 获取一个小说作为参考蓝本
        template = session.query(Novel).first()
        if not template:
            print("未找到小说，创建一个测试小说")
            # 创建一个测试小说
            template = Novel(
                title="测试小说",
                author="测试作者",
                content="这是一本测试小说的内容。",
                word_count=1000,
                novel_metadata={"is_template": True}
            )
            session.add(template)
            session.commit()

            # 创建一些测试章节
            for i in range(1, 6):
                chapter = Chapter(
                    novel_id=template.id,
                    chapter_number=i,
                    title=f"第{i}章",
                    content=f"这是第{i}章的内容。",
                    word_count=200
                )
                session.add(chapter)
            session.commit()
            print(f"已创建测试小说: ID={template.id}, 标题={template.title}")

        # 创建预设模板
        preset = Preset(
            title=f"预设模板-{template.title}",
            content=f"# {template.title} 预设模板\n\n## 基本信息\n- 参考蓝本ID: {template.id}\n- 参考蓝本: {template.title}\n- 作者: {template.author or '未知'}\n- 字数: {template.word_count or 0}\n- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n## 预设模板内容\n这是基于参考蓝本《{template.title}》生成的预设模板。",
            category="preset_template",
            meta_info={
                'template_id': template.id,
                'template_title': template.title,
                'created_at': datetime.now().isoformat()
            }
        )

        session.add(preset)
        session.commit()

        print(f"成功创建预设模板: ID={preset.id}, 标题={preset.title}")
        print(f"参考蓝本: ID={template.id}, 标题={template.title}")
        print(f"维度数量: {len(ANALYSIS_DIMENSIONS)}")

        # 获取章节数量
        chapters = session.query(Chapter).filter_by(novel_id=template.id).all()
        print(f"章节数量: {len(chapters)}")

        return preset
    except Exception as e:
        session.rollback()
        print(f"创建预设模板时出错: {str(e)}")
        return None
    finally:
        session.close()

if __name__ == "__main__":
    preset = create_preset_template()
    if preset:
        print(f"\n预设模板详情页面URL: http://localhost:5001/v3.1/preset/{preset.id}/templates")
