/**
 * API路径修复脚本
 *
 * 这个脚本用于修复所有API请求中的路径错误，将 /api/novels/ 替换为 /api/novel/
 *
 * 使用方法：
 * 1. 在页面加载时自动运行
 * 2. 拦截所有fetch请求，修复API路径
 * 3. 修复XHR请求的API路径
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('[API路径修复] 初始化API路径修复脚本');

    // 需要修复的API路径模式
    const pathPatterns = [
        '/api/novels/',
    ];

    // 对应的正确API路径
    const correctedPaths = [
        '/api/novel/',
    ];

    // 保存原始的fetch函数
    const originalFetch = window.fetch;

    // 重写fetch函数，拦截所有请求
    window.fetch = function(resource, options) {
        // 如果resource是字符串（URL），检查并修复
        if (typeof resource === 'string') {
            // 检查是否匹配需要修复的路径模式
            for (let i = 0; i < pathPatterns.length; i++) {
                if (resource.includes(pathPatterns[i])) {
                    // 替换为正确的路径
                    const correctedUrl = resource.replace(pathPatterns[i], correctedPaths[i]);
                    console.log(`[API路径修复] 修复API路径: ${resource} -> ${correctedUrl}`);
                    resource = correctedUrl;
                    break;
                }
            }
        }
        // 调用原始的fetch函数
        return originalFetch.call(this, resource, options);
    };

    // 保存原始的XMLHttpRequest.open方法
    const originalXhrOpen = XMLHttpRequest.prototype.open;

    // 重写XMLHttpRequest.open方法，拦截所有XHR请求
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        // 检查是否匹配需要修复的路径模式
        let modifiedUrl = url;
        for (let i = 0; i < pathPatterns.length; i++) {
            if (url.includes(pathPatterns[i])) {
                // 替换为正确的路径
                modifiedUrl = url.replace(pathPatterns[i], correctedPaths[i]);
                console.log(`[API路径修复] 修复XHR API路径: ${url} -> ${modifiedUrl}`);
                break;
            }
        }
        // 调用原始的open方法
        return originalXhrOpen.call(this, method, modifiedUrl, async, user, password);
    };

    // 修复jQuery的ajax请求（如果jQuery存在）
    if (typeof jQuery !== 'undefined') {
        console.log('[API路径修复] 检测到jQuery，修复jQuery的ajax请求');

        // 保存原始的jQuery.ajax方法
        const originalAjax = jQuery.ajax;

        // 重写jQuery.ajax方法
        jQuery.ajax = function(url, options) {
            // 处理不同的调用方式
            if (typeof url === 'object') {
                options = url;
                url = options.url;
            } else {
                options = options || {};
            }

            // 检查并修复URL
            if (url) {
                for (let i = 0; i < pathPatterns.length; i++) {
                    if (url.includes(pathPatterns[i])) {
                        // 替换为正确的路径
                        const correctedUrl = url.replace(pathPatterns[i], correctedPaths[i]);
                        console.log(`[API路径修复] 修复jQuery ajax URL: ${url} -> ${correctedUrl}`);
                        url = correctedUrl;
                        break;
                    }
                }

                // 如果options是对象，更新其中的url
                if (typeof options === 'object') {
                    options.url = url;
                }
            } else if (options.url) {
                // 检查options.url
                for (let i = 0; i < pathPatterns.length; i++) {
                    if (options.url.includes(pathPatterns[i])) {
                        // 替换为正确的路径
                        const correctedUrl = options.url.replace(pathPatterns[i], correctedPaths[i]);
                        console.log(`[API路径修复] 修复jQuery ajax options.url: ${options.url} -> ${correctedUrl}`);
                        options.url = correctedUrl;
                        break;
                    }
                }
            }

            // 调用原始的ajax方法
            return originalAjax.call(jQuery, options);
        };
    }

    console.log('[API路径修复] API路径修复脚本初始化完成');
})();
