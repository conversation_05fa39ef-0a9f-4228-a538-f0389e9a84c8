# 九猫3.0系统写作功能详细流程图

## 🎯 总体架构概览

```mermaid
graph TB
    A[用户触发写作请求] --> B[TestService.start_analysis_task]
    B --> C[创建后台线程]
    C --> D[_run_analysis_and_writing]
    D --> E[15维度分析阶段]
    E --> F[写作阶段]
    F --> G[3轮迭代优化]
    G --> H[结果存储与返回]
```

## 📋 详细工作流程

### 1. 用户请求阶段
```mermaid
graph LR
    A[用户选择小说] --> B[选择模型]
    B --> C[选择提示词模板]
    C --> D[点击一键分析写作]
    D --> E[API调用 start_analysis_task]
```

**关键参数：**
- `novel_id`: 小说ID
- `model`: 模型选择（deepseek-r1等）
- `prompt_template`: 提示词模板（default/simplified）

### 2. 任务初始化阶段
```mermaid
graph TB
    A[start_analysis_task] --> B[生成任务ID]
    B --> C[创建任务状态记录]
    C --> D[启动后台线程]
    D --> E[_run_analysis_and_writing]
```

**任务状态管理：**
```python
test_tasks[task_id] = {
    "status": "正在初始化...",
    "progress": 0,
    "completed": False,
    "error": None,
    "results": None,
    "model": model,
    "prompt_template": prompt_template
}
```

### 3. 15维度分析阶段
```mermaid
graph TB
    A[获取小说和章节数据] --> B[15维度分析循环]
    B --> C[语言风格分析]
    B --> D[节奏节拍分析]
    B --> E[结构分析]
    B --> F[人物关系分析]
    B --> G[世界构建分析]
    B --> H[开篇效果分析]
    B --> I[高潮节奏分析]
    B --> J[章纲分析]
    B --> K[大纲分析]
    B --> L[热梗统计分析]
    B --> M[其他5个维度...]
    C --> N[分析结果汇总]
    D --> N
    E --> N
    F --> N
    G --> N
    H --> N
    I --> N
    J --> N
    K --> N
    L --> N
    M --> N
```

**分析维度列表：**
1. language_style (语言风格)
2. rhythm_pacing (节奏节拍)
3. structure (结构分析)
4. sentence_variation (句式变化)
5. paragraph_length (段落长度)
6. perspective_changes (视角变化)
7. paragraph_flow (段落流畅度)
8. novel_features (小说特点)
9. world_building (世界构建)
10. character_relationships (人物关系)
11. opening_effect (开篇效果)
12. climax_pacing (高潮节奏)
13. chapter_outline (章纲分析)
14. outline_analysis (大纲分析)
15. hot_meme_statistics (热梗统计)

### 4. 写作阶段核心流程
```mermaid
graph TB
    A[开始写作阶段] --> B[章节循环处理]
    B --> C[连续性写作优化]
    C --> D[提取连贯性数据]
    D --> E[3轮迭代优化生成]
    E --> F[第1轮：基础内容生成]
    F --> G[第2轮：语言表达优化]
    G --> H[第3轮：最终打磨]
    H --> I[章节内容存储]
    I --> J[下一章节处理]
    J --> B
```

### 5. 提示词构建流程
```mermaid
graph TB
    A[_build_writing_prompt] --> B[检查参数有效性]
    B --> C[选择提示词模板类型]
    C --> D{模板类型判断}
    D -->|simplified| E[精简版提示词]
    D -->|default| F[默认版提示词]
    E --> G[添加分析摘要]
    F --> G
    G --> H[添加原文样本学习指导]
    H --> I[添加创新性写作任务]
    I --> J[添加迭代优化指导]
    J --> K[返回完整提示词]
```

**提示词模板对比：**

| 特性 | 默认版 (default) | 精简版 (simplified) |
|------|------------------|---------------------|
| 分析详细度 | 极其详细，不限字数 | 适度详细，控制长度 |
| 维度覆盖 | 全部15个维度 | 核心5个维度 |
| 推理过程 | 完整包含 | 简化处理 |
| 成本控制 | 无限制 | 降本增效 |
| 输出质量 | 最高质量 | 平衡质量与成本 |

### 6. 3轮迭代优化详细流程
```mermaid
graph TB
    A[_generate_chapter_with_iterative_optimization] --> B[第1轮：基础内容生成]
    B --> C[构建基础提示词]
    C --> D[添加连贯性增强]
    D --> E[调用API生成基础内容]
    E --> F{是否需要第2轮?}
    F -->|是| G[第2轮：语言表达优化]
    F -->|否| P[返回基础内容]
    G --> H[分析逻辑问题]
    H --> I[构建优化提示词]
    I --> J[调用优化API]
    J --> K{是否需要第3轮?}
    K -->|是| L[第3轮：最终打磨]
    K -->|否| Q[返回优化内容]
    L --> M[再次分析逻辑问题]
    M --> N[最终打磨API调用]
    N --> O[返回最终内容]
```

**迭代优化重点：**

| 轮次 | 主要任务 | 优化重点 |
|------|----------|----------|
| 第1轮 | 基础内容生成 | 创意和风格，完整故事框架 |
| 第2轮 | 语言表达优化 | 逻辑修复，语言生活化，维度融入 |
| 第3轮 | 最终打磨 | 连贯性检查，质量验证，细节完善 |

### 7. 逻辑问题检测机制
```mermaid
graph TB
    A[_analyze_logic_issues] --> B[检测常见逻辑问题]
    B --> C[编造前文未提及情节]
    B --> D[突发事件缺乏原因]
    B --> E[不符合剧情的词汇]
    B --> F[破折号句式]
    B --> G[过度比喻修辞]
    B --> H[系统出现缺乏反应]
    C --> I[生成问题反馈]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    I --> J[格式化反馈信息]
```

### 8. 连贯性管理流程
```mermaid
graph TB
    A[_extract_chapter_continuity_data] --> B[提取人物名称]
    B --> C[提取句式模式]
    C --> D[提取对话风格]
    D --> E[提取情感基调]
    E --> F[构建连贯性数据]
    F --> G[_build_continuity_prompt_enhancement]
    G --> H[生成连贯性指导]
```

**连贯性数据结构：**
```python
continuity_data = {
    "chapter_number": int,
    "characters": List[str],        # 人物名称
    "plot_points": List[str],       # 情节要点
    "style_features": Dict,         # 风格特征
    "hot_memes": List[str],         # 热梗元素
    "sentence_patterns": List[int], # 句式模式
    "dialogue_style": Dict,         # 对话风格
    "scene_setting": str,           # 场景设定
    "emotional_tone": str,          # 情感基调
    "narrative_perspective": str    # 叙述视角
}
```

### 9. API调用与错误处理流程
```mermaid
graph TB
    A[_generate_chapter_content] --> B[参数验证]
    B --> C[创建DeepSeekClient]
    C --> D[设置max_tokens]
    D --> E[构建完整提示词]
    E --> F[API调用]
    F --> G{调用成功?}
    G -->|是| H[内容验证]
    G -->|否| I[错误处理]
    I --> J[重试机制]
    J --> K{重试次数<3?}
    K -->|是| F
    K -->|否| L[返回错误信息]
    H --> M[返回生成内容]
```

**错误处理策略：**
- 服务器错误：延迟15-45秒重试
- 超时错误：延迟10-30秒重试
- Token错误：调整参数重试
- 其他错误：延迟5-15秒重试

### 10. 提示词模板管理流程
```mermaid
graph TB
    A[PromptTemplateManager] --> B{模板类型}
    B -->|default| C[_get_default_analysis_prompt]
    B -->|simplified| D[_get_simplified_analysis_prompt]
    C --> E[完整详细模式]
    D --> F[降本增效模式]
    E --> G[enhance_prompt_with_template]
    F --> G
    G --> H[增强后的提示词]
```

**模板特性对比：**

| 维度 | 默认版要求 | 精简版要求 |
|------|------------|------------|
| 字数限制 | 不限字数，至少8000字 | 控制在合理范围内 |
| 分析深度 | 极其详尽描述 | 重点突出，适度详细 |
| 原文使用 | 100%基于原文内容 | 基于原文，避免泛泛而谈 |
| 语言要求 | 通俗易懂，避免专业术语 | 简洁明了表达 |
| 成本控制 | 无成本限制 | 优化API成本 |

### 11. 写作保护配置流程
```mermaid
graph TB
    A[_configure_writing_protection] --> B[设置写作功能保护]
    B --> C[function_type: writing]
    C --> D[priority: highest]
    D --> E[token_limit: unlimited]
    E --> F[model_performance: maximum]
    F --> G[instruction_understanding: enhanced]
    G --> H[quality_assurance: strict]
    H --> I[cost_optimization: disabled_for_writing]
```

**写作保护配置：**
```python
protection_config = {
    "function_type": "writing",
    "priority": "highest",
    "token_limit": "unlimited",
    "model_performance": "maximum",
    "instruction_understanding": "enhanced",
    "quality_assurance": "strict",
    "cost_optimization": "disabled_for_writing"
}
```

### 12. 字数控制与追踪流程
```mermaid
graph TB
    A[WordCountTracker初始化] --> B[设置目标字数]
    B --> C[实时字数统计]
    C --> D[进度更新回调]
    D --> E[字数范围验证]
    E --> F{字数是否合适?}
    F -->|是| G[继续生成]
    F -->|否| H[调整生成策略]
    H --> I[重新生成部分内容]
    I --> C
    G --> J[完成生成]
```

**字数控制策略：**
- 目标字数：2000字（已调整，优先剧情质量）
- 容忍范围：80%-120%（1600-2400字）
- 实时追踪：每100字记录一次
- 动态调整：根据进度调整详略程度

### 13. 数据存储与结果处理流程
```mermaid
graph TB
    A[章节内容生成完成] --> B[内容验证]
    B --> C[存储到数据库]
    C --> D[更新任务状态]
    D --> E[生成结果摘要]
    E --> F[返回给前端]
    F --> G[用户查看结果]
```

**存储结构：**
```python
generated_chapters = [
    {
        "chapter_id": int,
        "chapter_number": int,
        "title": str,
        "content": str,
        "word_count": int,
        "generation_time": datetime,
        "model_used": str,
        "template_used": str,
        "iterations": int
    }
]
```
