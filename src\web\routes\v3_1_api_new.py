"""
九猫小说分析写作系统v3.1 - API路由
"""
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify

from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.preset import Preset

logger = logging.getLogger(__name__)

v3_1_api_bp = Blueprint('v3_1_api', __name__, url_prefix='/v3.1/api')

@v3_1_api_bp.route('/novels', methods=['GET'])
def get_novels():
    """
    获取小说列表

    响应:
    {
        "success": 是否成功,
        "novels": 小说列表
    }
    """
    try:
        logger.info("获取小说列表")
        session = Session()
        try:
            # 获取所有小说
            novels = session.query(Novel).order_by(Novel.created_at.desc()).all()
            novels_data = []

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS

            for novel in novels:
                # 获取章节数量
                chapter_count = session.query(Chapter).filter_by(novel_id=novel.id).count()

                # 获取分析结果数量
                analysis_count = session.query(AnalysisResult).filter_by(novel_id=novel.id).count()

                # 判断是否为参考蓝本
                is_template = novel.novel_metadata and novel.novel_metadata.get('is_template', False) if novel.novel_metadata else False

                novels_data.append({
                    'id': novel.id,
                    'title': novel.title,
                    'author': novel.author,
                    'word_count': novel.word_count,
                    'chapter_count': chapter_count,
                    'analysis_count': analysis_count,
                    'is_template': is_template,
                    'created_at': novel.created_at.isoformat() if novel.created_at else None,
                    'updated_at': novel.updated_at.isoformat() if novel.updated_at else None
                })

            # 构建响应数据
            response_data = {
                'success': True,
                'novels': novels_data
            }

            logger.info(f"成功获取小说列表，共 {len(novels_data)} 本小说")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/convert_to_template', methods=['POST'])
def convert_to_template():
    """
    将参考蓝本的分析结果转换为预设模板

    请求体:
    {
        "template_id": 参考蓝本ID,
        "knowledge_base_data": 知识库数据（可选）
    }

    响应:
    {
        "success": 是否成功,
        "message": 成功消息,
        "preset_id": 预设ID,
        "book_templates": 整本书预设模板,
        "chapter_templates": 章节预设模板
    }
    """
    try:
        data = request.get_json()
        if not data:
            logger.error("转换预设模板请求体为空")
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        template_id = data.get('template_id')
        if not template_id:
            logger.error("转换预设模板缺少参考蓝本ID")
            return jsonify({
                'success': False,
                'error': '参考蓝本ID不能为空'
            }), 400

        # 获取知识库数据（可选）
        _ = data.get('knowledge_base_data')  # 不使用，但保留参数
        logger.info(f"开始转换参考蓝本 ID {template_id} 为预设模板")

        # 创建一个简单的预设模板
        session = Session()
        try:
            # 获取参考蓝本信息
            template = session.query(Novel).get(template_id)
            if not template:
                logger.error(f"未找到参考蓝本 ID {template_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 创建预设模板
            preset = Preset(
                title=f"预设模板-{template.title}",
                content=f"# {template.title} 预设模板\n\n## 基本信息\n- 参考蓝本ID: {template_id}\n- 参考蓝本: {template.title}\n- 作者: {template.author or '未知'}\n- 字数: {template.word_count or 0}\n- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n## 预设模板内容\n这是基于参考蓝本《{template.title}》生成的预设模板。",
                category="preset_template",
                meta_info={
                    'template_id': template_id,
                    'template_title': template.title,
                    'created_at': datetime.now().isoformat()
                }
            )

            session.add(preset)
            session.commit()

            # 构建响应数据
            result = {
                'success': True,
                'message': '成功转换为预设模板',
                'preset_id': preset.id,
                'template': {
                    'id': template.id,
                    'title': template.title
                },
                'book_templates': {},
                'chapter_templates': {}
            }

            logger.info(f"成功创建预设模板 ID {preset.id}")
            return jsonify(result), 200
        except Exception as e:
            session.rollback()
            logger.error(f"创建预设模板时出错: {str(e)}", exc_info=True)
            raise
        finally:
            session.close()
    except Exception as e:
        logger.error(f"转换参考蓝本为预设模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>', methods=['GET'])
def get_novel_info(novel_id):
    """
    获取小说信息、章节列表和分析维度

    响应:
    {
        "success": 是否成功,
        "novel": 小说信息,
        "chapters": 章节列表,
        "dimensions": 分析维度列表
    }
    """
    try:
        logger.info(f"获取小说信息 [小说ID: {novel_id}]")
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).get(novel_id)
            if not novel:
                logger.error(f"未找到小说 ID {novel_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404

            # 获取章节列表
            chapters = session.query(Chapter).filter_by(novel_id=novel_id).order_by(Chapter.chapter_number).all()
            chapters_data = []

            for chapter in chapters:
                # 获取章节分析结果数量
                analysis_count = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id,
                    chapter_id=chapter.id
                ).count()

                chapters_data.append({
                    'id': chapter.id,
                    'novel_id': chapter.novel_id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'word_count': chapter.word_count,
                    'analysis_count': analysis_count
                })

            # 获取分析维度
            from config import ANALYSIS_DIMENSIONS
            dimensions_data = []

            # 获取已分析的维度
            analyzed_dimensions = []
            results = session.query(AnalysisResult).filter_by(novel_id=novel_id).all()
            for result in results:
                analyzed_dimensions.append(result.dimension)

            # 构建维度列表
            for dim in ANALYSIS_DIMENSIONS:
                dim_copy = dim.copy()
                dim_copy['is_analyzed'] = dim['key'] in analyzed_dimensions
                dimensions_data.append(dim_copy)

            # 构建响应数据
            response_data = {
                'success': True,
                'novel': {
                    'id': novel.id,
                    'title': novel.title,
                    'author': novel.author,
                    'word_count': novel.word_count,
                    'created_at': novel.created_at.isoformat() if novel.created_at else None,
                    'updated_at': novel.updated_at.isoformat() if novel.updated_at else None,
                    'is_template': novel.novel_metadata and novel.novel_metadata.get('is_template', False) if novel.novel_metadata else False
                },
                'chapters': chapters_data,
                'dimensions': dimensions_data
            }

            logger.info(f"成功获取小说信息 [小说ID: {novel_id}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_chapter_reasoning_content(novel_id, chapter_id, dimension):
    """
    获取章节分析推理过程
    """
    try:
        session = Session()
        try:
            from src.models.chapter_analysis_result import ChapterAnalysisResult
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=novel_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()
            if not result:
                return jsonify({'success': False, 'error': '未找到章节分析结果'}), 404
            return jsonify({
                'success': True,
                'dimension': dimension,
                'content': result.reasoning_content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

@v3_1_api_bp.route('/template/<int:template_id>/analysis/<dimension>', methods=['GET'])
def get_template_dimension_analysis(template_id, dimension):
    """
    获取参考蓝本特定维度的分析结果
    """
    try:
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=template_id,
                dimension=dimension
            ).first()

            if not result:
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的分析结果',
                    'result': None
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'result': {
                    'content': result.content,
                    'created_at': result.created_at.isoformat() if result.created_at else '',
                    'updated_at': result.updated_at.isoformat() if result.updated_at else '',
                    'metadata': result.metadata,
                    'analysis_logs': getattr(result, 'analysis_logs', [])
                }
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'result': None
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_template_dimension_reasoning(template_id, dimension):
    """
    获取参考蓝本特定维度的推理过程

    响应:
    {
        "success": 是否成功,
        "content": 推理内容
    }
    """
    try:
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=template_id,
                dimension=dimension
            ).first()

            if not result:
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'dimension': dimension,
                'content': result.reasoning_content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取参考蓝本推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>', methods=['GET'])
def get_chapter_analysis(template_id, chapter_id, dimension):
    """
    获取章节分析结果
    """
    try:
        logger.info(f"获取章节分析结果 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到章节分析结果 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节分析结果',
                    'result': None
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'result': {
                    'content': result.content,
                    'created_at': result.created_at.isoformat() if result.created_at else '',
                    'updated_at': result.updated_at.isoformat() if result.updated_at else '',
                    'metadata': result.metadata,
                    'analysis_logs': getattr(result, 'analysis_logs', [])
                }
            }

            logger.info(f"成功获取章节分析结果 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e),
            'result': None
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/chapter/<int:chapter_id>/analysis/<dimension>/reasoning_content', methods=['GET'])
def get_chapter_reasoning(template_id, chapter_id, dimension):
    """
    获取章节推理过程

    响应:
    {
        "success": 是否成功,
        "content": 推理过程内容
    }
    """
    try:
        logger.info(f"获取章节推理过程 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取章节分析结果
            result = session.query(ChapterAnalysisResult).filter_by(
                novel_id=template_id,
                chapter_id=chapter_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到章节分析结果 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定章节分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'content': result.reasoning_content or "暂无推理过程"
            }

            logger.info(f"成功获取章节推理过程 [模板ID: {template_id}, 章节ID: {chapter_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取章节推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/template/<int:template_id>/analysis', methods=['GET'])
def get_template_analysis(template_id):
    """
    获取参考蓝本的分析结果

    响应:
    {
        "success": 是否成功,
        "template": 参考蓝本信息,
        "book_analyses": 整本书分析结果,
        "chapters": 章节列表及分析结果
    }
    """
    try:
        session = Session()
        try:
            # 获取参考蓝本
            template = session.query(Novel).get(template_id)
            if not template:
                return jsonify({
                    'success': False,
                    'error': '未找到指定参考蓝本'
                }), 404

            # 检查是否为参考蓝本
            if not (template.novel_metadata and template.novel_metadata.get('is_template')):
                return jsonify({
                    'success': False,
                    'error': '指定的小说不是参考蓝本'
                }), 400

            # 获取整本书分析结果
            book_analyses = []
            results = session.query(AnalysisResult).filter_by(novel_id=template_id).all()
            for result in results:
                book_analyses.append({
                    'dimension': result.dimension,
                    'result': result.content,
                    'reasoning_content': result.reasoning_content,
                    'created_at': result.created_at.isoformat() if result.created_at else None,
                    'updated_at': result.updated_at.isoformat() if result.updated_at else None
                })

            # 获取章节
            chapters_data = []
            chapters = session.query(Chapter).filter_by(novel_id=template_id).order_by(Chapter.chapter_number).all()

            # 获取章节分析结果
            for chapter in chapters:
                chapter_results = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=template_id,
                    chapter_id=chapter.id
                ).all()

                analysis_results = []
                for result in chapter_results:
                    analysis_results.append({
                        'dimension': result.dimension,
                        'result': result.content,
                        'reasoning_content': result.reasoning_content,
                        'created_at': result.created_at.isoformat() if result.created_at else None,
                        'updated_at': result.updated_at.isoformat() if result.updated_at else None
                    })

                chapters_data.append({
                    'id': chapter.id,
                    'novel_id': chapter.novel_id,
                    'chapter_number': chapter.chapter_number,
                    'title': chapter.title or f'第{chapter.chapter_number}章',
                    'word_count': chapter.word_count,
                    'analysis_results': analysis_results
                })

            # 构建响应数据
            response_data = {
                'success': True,
                'template': {
                    'id': template.id,
                    'title': template.title,
                    'author': template.author,
                    'word_count': template.word_count,
                    'created_at': template.created_at.isoformat() if template.created_at else None,
                    'updated_at': template.updated_at.isoformat() if template.updated_at else None,
                    'template_created_at': template.novel_metadata.get('template_created_at') if template.novel_metadata else None
                },
                'book_analyses': book_analyses,
                'chapters': chapters_data
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取参考蓝本分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/preset/<int:preset_id>', methods=['GET'])
def get_preset(preset_id):
    """
    获取预设模板

    响应:
    {
        "success": 是否成功,
        "preset": 预设模板信息,
        "book_templates": 整本书预设模板,
        "chapter_templates": 章节预设模板
    }
    """
    try:
        session = Session()
        try:
            # 获取预设模板
            preset = session.query(Preset).get(preset_id)
            if not preset:
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设模板'
                }), 404

            # 检查是否为预设模板
            if preset.category != 'preset_template':
                return jsonify({
                    'success': False,
                    'error': '指定的预设不是预设模板'
                }), 400

            # 获取预设模板内容
            meta_info = preset.meta_info or {}
            template_id = meta_info.get('template_id')

            # 获取参考蓝本
            template = None
            if template_id:
                template = session.query(Novel).get(template_id)

            # 构建响应数据
            response_data = {
                'success': True,
                'preset': {
                    'id': preset.id,
                    'title': preset.title,
                    'content': preset.content,
                    'category': preset.category,
                    'created_at': preset.created_at.isoformat() if preset.created_at else None,
                    'updated_at': preset.updated_at.isoformat() if preset.updated_at else None,
                    'meta_info': meta_info
                },
                'template': {
                    'id': template.id,
                    'title': template.title,
                    'author': template.author,
                    'word_count': template.word_count
                } if template else None
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设模板时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/preset/<int:preset_id>/templates_data', methods=['GET'])
def get_preset_templates(preset_id):
    """
    获取预设模板详情

    响应:
    {
        "success": 是否成功,
        "preset": 预设模板信息,
        "novel": 参考蓝本信息,
        "book_templates": 整本书预设模板,
        "chapter_templates": 章节预设模板
    }
    """
    try:
        logger.info(f"获取预设模板详情 [预设ID: {preset_id}]")
        session = Session()
        try:
            # 获取预设模板
            preset = session.query(Preset).get(preset_id)
            if not preset:
                logger.error(f"未找到预设模板 ID {preset_id}")
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设模板'
                }), 404

            # 获取预设模板内容
            meta_info = preset.meta_info or {}
            template_id = meta_info.get('template_id')

            # 获取参考蓝本
            novel = None
            if template_id:
                novel = session.query(Novel).get(template_id)

            # 获取章节列表
            chapters = []
            if novel:
                chapters = session.query(Chapter).filter_by(novel_id=novel.id).order_by(Chapter.chapter_number).all()
                logger.info(f"获取到 {len(chapters)} 个章节")

            # 构建响应数据
            response_data = {
                'success': True,
                'preset': {
                    'id': preset.id,
                    'title': preset.title,
                    'content': preset.content,
                    'category': preset.category,
                    'created_at': preset.created_at.isoformat() if preset.created_at else None,
                    'updated_at': preset.updated_at.isoformat() if preset.updated_at else None,
                    'meta_info': meta_info
                },
                'novel': {
                    'id': novel.id,
                    'title': novel.title,
                    'author': novel.author,
                    'word_count': novel.word_count,
                    'created_at': novel.created_at.isoformat() if novel.created_at else None,
                    'updated_at': novel.updated_at.isoformat() if novel.updated_at else None
                } if novel else None,
                'chapters': [
                    {
                        'id': chapter.id,
                        'novel_id': chapter.novel_id,
                        'chapter_number': chapter.chapter_number,
                        'title': chapter.title or f'第{chapter.chapter_number}章',
                        'word_count': chapter.word_count
                    } for chapter in chapters
                ]
            }

            logger.info(f"成功获取预设模板详情 [预设ID: {preset_id}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设模板详情时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/presets', methods=['GET'])
def get_presets():
    """
    获取预设模板列表

    响应:
    {
        "success": 是否成功,
        "presets": 预设模板列表
    }
    """
    try:
        session = Session()
        try:
            # 获取预设模板列表
            presets = session.query(Preset).filter_by(category='preset_template').all()

            # 构建响应数据
            presets_data = []
            for preset in presets:
                meta_info = preset.meta_info or {}
                template_id = meta_info.get('template_id')

                # 获取参考蓝本
                template = None
                if template_id:
                    template = session.query(Novel).get(template_id)

                presets_data.append({
                    'id': preset.id,
                    'title': preset.title,
                    'created_at': preset.created_at.isoformat() if preset.created_at else None,
                    'updated_at': preset.updated_at.isoformat() if preset.updated_at else None,
                    'meta_info': meta_info,
                    'template': {
                        'id': template.id,
                        'title': template.title,
                        'author': template.author,
                        'word_count': template.word_count
                    } if template else None
                })

            response_data = {
                'success': True,
                'presets': presets_data
            }

            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设模板列表时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/analyze_dimension', methods=['POST'])
def analyze_novel_dimension(novel_id):
    """
    创建小说维度分析任务
    """
    try:
        data = request.json or {}
        dimension = data.get('dimension')
        if not dimension:
            return jsonify({
                'success': False,
                'error': '维度名称不能为空'
            }), 400
        force = data.get('force', 0)
        session = Session()
        try:
            novel = session.query(Novel).get(novel_id)
            if not novel:
                return jsonify({
                    'success': False,
                    'error': '未找到指定小说'
                }), 404
            from src.api.analysis import NovelAnalyzer
            from src.api.deepseek_client import DeepSeekClient
            from src.models.analysis_result import AnalysisResult
            # 创建分析器
            api_client = DeepSeekClient()
            analyzer = NovelAnalyzer(api_client=api_client)
            # 执行分析
            result_data = analyzer.analyze_dimension(
                text=novel.content,
                dimension=dimension,
                title=novel.title
            )
            if not result_data or 'content' not in result_data:
                return jsonify({'success': False, 'error': 'API返回数据格式不正确'}), 500
            # 保存分析结果（如已存在则更新，否则插入）
            content = result_data['content']
            reasoning_content = result_data.get('reasoning', result_data.get('reasoning_content', 'API未返回推理过程'))
            exist_result = session.query(AnalysisResult).filter_by(novel_id=novel_id, dimension=dimension).first()
            if exist_result:
                exist_result.content = content
                exist_result.reasoning_content = reasoning_content
                exist_result.analysis_metadata = {'created_at': datetime.now().isoformat(), 'using_real_api': True}
                exist_result.updated_at = datetime.now()
            else:
                result = AnalysisResult(
                    novel_id=novel_id,
                    dimension=dimension,
                    content=content,
                    reasoning_content=reasoning_content,
                    metadata={'created_at': datetime.now().isoformat(), 'using_real_api': True}
                )
                session.add(result)
            session.commit()
            analysis_id = f"{novel_id}_{dimension}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            return jsonify({
                'success': True,
                'analysis_id': analysis_id,
                'message': f'成功创建分析任务，正在分析维度 {dimension}'
            }), 200
        except Exception as e:
            session.rollback()
            return jsonify({'success': False, 'error': f'保存分析结果失败: {str(e)}'}), 500
        finally:
            session.close()
    except Exception as e:
        logger.error(f"创建分析任务时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/preset_info/<int:preset_id>', methods=['GET'])
def get_preset_info(preset_id):
    """
    获取预设模板信息

    响应:
    {
        "success": 是否成功,
        "preset": 预设模板信息
    }
    """
    try:
        session = Session()
        try:
            # 获取预设模板
            preset = session.query(Preset).get(preset_id)
            if not preset:
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设模板'
                }), 404

            # 转换为字典
            preset_dict = {
                'id': preset.id,
                'title': preset.title,
                'content': preset.content,
                'meta_info': preset.meta_info,
                'created_at': preset.created_at.isoformat() if preset.created_at else None,
                'updated_at': preset.updated_at.isoformat() if preset.updated_at else None
            }

            return jsonify({
                'success': True,
                'preset': preset_dict
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取预设模板信息时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/analysis/<analysis_id>/status', methods=['GET'])
def get_analysis_status(analysis_id):
    """
    获取分析任务状态

    响应:
    {
        "success": 是否成功,
        "status": 分析状态,
        "progress": 分析进度,
        "log_entries": 日志条目,
        "result_url": 结果URL（如果完成）,
        "error": 错误信息（如果失败）
    }
    """
    try:
        # 解析分析ID
        parts = analysis_id.split('_')
        if len(parts) < 3:
            return jsonify({
                'success': False,
                'error': '无效的分析ID'
            }), 400

        novel_id = parts[0]
        dimension = parts[1]

        # 尝试将novel_id转换为整数
        try:
            novel_id_int = int(novel_id)
        except ValueError:
            novel_id_int = novel_id

        # 获取小说和分析结果
        session = Session()
        try:
            # 解析分析ID格式，判断是整本书分析还是章节分析
            is_chapter_analysis = False
            chapter_id = None

            # 检查分析ID格式是否包含章节ID
            if len(parts) >= 3 and parts[1].isdigit():
                try:
                    chapter_id = int(parts[1])
                    dimension = parts[2]
                    is_chapter_analysis = True
                except (ValueError, IndexError):
                    # 如果转换失败，则不是章节分析
                    pass

            # 根据分析类型查询不同的表
            if is_chapter_analysis:
                # 导入章节分析结果模型
                from src.models.chapter_analysis_result import ChapterAnalysisResult

                # 查询章节分析结果
                result = session.query(ChapterAnalysisResult).filter_by(
                    novel_id=novel_id_int,
                    chapter_id=chapter_id,
                    dimension=dimension
                ).first()
            else:
                # 查询整本书分析结果
                result = session.query(AnalysisResult).filter_by(
                    novel_id=novel_id_int,
                    dimension=dimension
                ).first()

            # 检查分析是否完成
            if result:
                status = 'completed'
                progress = 100

                # 根据分析类型构建不同的结果URL
                if is_chapter_analysis:
                    result_url = f"/v3.1/novel/{novel_id}/chapter/{chapter_id}/dimension/{dimension}"
                else:
                    result_url = f"/v3.1/analysis/{novel_id}/{dimension}"

                log_entries = [
                    {'level': 'info', 'message': '分析已完成'},
                    {'level': 'success', 'message': '分析结果已保存'}
                ]

                # 记录日志
                if is_chapter_analysis:
                    logger.info(f"章节分析已完成: 小说ID={novel_id}, 章节ID={chapter_id}, 维度={dimension}")
                else:
                    logger.info(f"分析已完成: 小说ID={novel_id}, 维度={dimension}")
            else:
                # 检查是否正在分析中
                # 这里可以添加更复杂的逻辑来检查分析是否正在进行
                # 例如检查分析进度表或临时文件

                # 检查日志中是否有分析完成的记录
                # 从全局变量中获取分析日志
                from src.web.app import analysis_logs

                # 获取分析日志
                current_logs = []
                if novel_id_int in analysis_logs:
                    # 筛选出当前维度的日志
                    current_logs = [log for log in analysis_logs[novel_id_int]
                                   if (log.get('dimension') == dimension or
                                      (log.get('dimension') is None and dimension in log.get('message', '')))]

                # 检查日志中是否有分析完成的记录
                if current_logs and any("分析已完成" in log.get("message", "") for log in current_logs):
                    status = 'completed'
                    progress = 100

                    # 根据分析类型构建不同的结果URL
                    if is_chapter_analysis:
                        result_url = f"/v3.1/novel/{novel_id}/chapter/{chapter_id}/dimension/{dimension}"
                    else:
                        result_url = f"/v3.1/analysis/{novel_id}/{dimension}"

                    log_entries = [
                        {'level': 'info', 'message': '分析已完成'},
                        {'level': 'success', 'message': '分析结果已保存'}
                    ]

                    # 记录日志
                    if is_chapter_analysis:
                        logger.info(f"章节分析已完成(从日志检测): 小说ID={novel_id}, 章节ID={chapter_id}, 维度={dimension}")
                    else:
                        logger.info(f"分析已完成(从日志检测): 小说ID={novel_id}, 维度={dimension}")
                else:
                    status = 'analyzing'
                    progress = 75  # 提高进度显示，避免一直显示50%
                    result_url = None
                    log_entries = [
                        {'level': 'info', 'message': '分析正在进行中...'}
                    ]

                    # 记录日志
                    if is_chapter_analysis:
                        logger.info(f"章节分析进行中: 小说ID={novel_id}, 章节ID={chapter_id}, 维度={dimension}, 进度=75%")
                    else:
                        logger.info(f"分析进行中: 小说ID={novel_id}, 维度={dimension}, 进度=75%")

            return jsonify({
                'success': True,
                'status': status,
                'progress': progress,
                'log_entries': log_entries,
                'result_url': result_url
            }), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取分析状态时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/analysis/<analysis_id>/cancel', methods=['POST'])
def cancel_analysis(analysis_id):
    """
    取消分析任务

    响应:
    {
        "success": 是否成功,
        "message": 成功消息
    }
    """
    try:
        # 解析分析ID
        parts = analysis_id.split('_')
        if len(parts) < 3:
            return jsonify({
                'success': False,
                'error': '无效的分析ID'
            }), 400

        # 模拟取消分析
        return jsonify({
            'success': True,
            'message': '分析已取消'
        }), 200
    except Exception as e:
        logger.error(f"取消分析时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/auto_write', methods=['POST'])
def auto_write():
    """
    自动写作

    请求体:
    {
        "template_id": 预设模板ID,
        "prompt": 写作提示
    }

    响应:
    {
        "success": 是否成功,
        "content": 生成的内容
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        template_id = data.get('template_id')
        if not template_id:
            return jsonify({
                'success': False,
                'error': '预设模板ID不能为空'
            }), 400

        prompt = data.get('prompt', '')

        logger.info(f"开始自动写作: 模板ID={template_id}, 提示={prompt}")

        # 模拟自动写作过程
        session = Session()
        try:
            # 获取预设模板
            preset = session.query(Preset).get(template_id)
            if not preset:
                return jsonify({
                    'success': False,
                    'error': '未找到指定预设模板'
                }), 404

            # 获取参考蓝本
            template = None
            if preset.meta_info and preset.meta_info.get('template_id'):
                template_id = preset.meta_info.get('template_id')
                template = session.query(Novel).get(template_id)

            # 生成内容
            content = f"# 基于《{preset.title}》的自动写作\n\n"
            content += f"## 写作提示\n{prompt}\n\n"
            content += f"## 生成内容\n"
            content += f"这是基于预设模板《{preset.title}》"

            if template:
                content += f"和参考蓝本《{template.title}》"

            content += "生成的内容。\n\n"
            content += "自动写作功能正在开发中，敬请期待！\n\n"
            content += "您可以在此处查看完整的预设模板内容，并根据需要进行修改。"

            # 构建响应数据
            response_data = {
                'success': True,
                'content': {
                    'title': f"基于《{preset.title}》的自动写作",
                    'content': content
                }
            }

            logger.info(f"自动写作完成: 模板ID={template_id}")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"自动写作时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/chapter/<int:chapter_id>/analyze_dimension', methods=['POST'])
def analyze_chapter_dimension(novel_id, chapter_id):
    """
    分析章节特定维度
    请求体: {"dimension": 维度名称, "force": 是否强制重新分析（可选）}
    响应: {"success": 是否成功, "analysis_id": 分析任务ID, "message": 成功消息}
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '请求体不能为空'}), 400
        dimension = data.get('dimension')
        if not dimension:
            return jsonify({'success': False, 'error': '维度名称不能为空'}), 400
        force = data.get('force', 0)
        session = Session()
        try:
            chapter = session.query(Chapter).get(chapter_id)
            if not chapter or chapter.novel_id != int(novel_id):
                return jsonify({'success': False, 'error': '未找到指定章节'}), 404
            from src.api.analysis import NovelAnalyzer
            from src.api.deepseek_client import DeepSeekClient
            from src.models.chapter_analysis_result import ChapterAnalysisResult
            # 获取章节内容
            chapter_text = chapter.content
            chapter_title = chapter.title or f'第{chapter.chapter_number}章'
            # 创建分析器
            api_client = DeepSeekClient()
            analyzer = NovelAnalyzer(api_client=api_client)
            # 执行分析
            result_data = analyzer.analyze_dimension(
                text=chapter_text,
                dimension=dimension,
                title=chapter_title
            )
            if not result_data or 'content' not in result_data:
                return jsonify({'success': False, 'error': 'API返回数据格式不正确'}), 500
            # 保存分析结果（如已存在则更新，否则插入）
            content = result_data['content']
            reasoning_content = result_data.get('reasoning', result_data.get('reasoning_content', 'API未返回推理过程'))
            exist_result = session.query(ChapterAnalysisResult).filter_by(novel_id=novel_id, chapter_id=chapter_id, dimension=dimension).first()
            if exist_result:
                exist_result.content = content
                exist_result.reasoning_content = reasoning_content
                exist_result.analysis_metadata = {'created_at': datetime.now().isoformat(), 'using_real_api': True}
                exist_result.updated_at = datetime.now()
            else:
                result = ChapterAnalysisResult(
                    novel_id=novel_id,
                    chapter_id=chapter_id,
                    dimension=dimension,
                    content=content,
                    reasoning_content=reasoning_content,
                    metadata={'created_at': datetime.now().isoformat(), 'using_real_api': True}
                )
                session.add(result)
            session.commit()
            analysis_id = f"{novel_id}_{chapter_id}_{dimension}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            return jsonify({'success': True, 'analysis_id': analysis_id, 'message': f'成功创建分析任务，正在分析章节维度 {dimension}'}), 200
        except Exception as e:
            session.rollback()
            return jsonify({'success': False, 'error': f'保存章节分析结果失败: {str(e)}'}), 500
        finally:
            session.close()
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/analysis/<string:dimension>', methods=['GET'])
def get_novel_analysis(novel_id, dimension):
    """
    获取小说分析结果

    响应:
    {
        "success": 是否成功,
        "result": {
            "content": 分析结果内容,
            "created_at": 创建时间,
            "updated_at": 更新时间,
            "metadata": 元数据
        }
    }
    """
    try:
        logger.info(f"获取小说分析结果 [小说ID: {novel_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到小说分析结果 [小说ID: {novel_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的分析结果',
                    'result': None
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'result': {
                    'content': result.content,
                    'reasoning_content': result.reasoning_content,
                    'created_at': result.created_at.isoformat() if result.created_at else '',
                    'updated_at': result.updated_at.isoformat() if result.updated_at else '',
                    'metadata': result.metadata
                }
            }

            logger.info(f"成功获取小说分析结果 [小说ID: {novel_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说分析结果时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e),
            'result': None
        }), 500

@v3_1_api_bp.route('/novel/<int:novel_id>/analysis/<string:dimension>/reasoning_content', methods=['GET'])
def get_novel_reasoning(novel_id, dimension):
    """
    获取小说分析推理过程

    响应:
    {
        "success": 是否成功,
        "content": 推理过程内容
    }
    """
    try:
        logger.info(f"获取小说分析推理过程 [小说ID: {novel_id}, 维度: {dimension}]")
        session = Session()
        try:
            # 获取分析结果
            result = session.query(AnalysisResult).filter_by(
                novel_id=novel_id,
                dimension=dimension
            ).first()

            if not result:
                logger.error(f"未找到小说分析结果 [小说ID: {novel_id}, 维度: {dimension}]")
                return jsonify({
                    'success': False,
                    'error': '未找到指定维度的分析结果'
                }), 404

            # 构建响应数据
            response_data = {
                'success': True,
                'dimension': dimension,
                'content': result.reasoning_content,
                'created_at': result.created_at.isoformat() if result.created_at else None,
                'updated_at': result.updated_at.isoformat() if result.updated_at else None
            }

            logger.info(f"成功获取小说分析推理过程 [小说ID: {novel_id}, 维度: {dimension}]")
            return jsonify(response_data), 200
        finally:
            session.close()
    except Exception as e:
        logger.error(f"获取小说分析推理过程时出错: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
