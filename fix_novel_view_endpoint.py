"""
九猫小说分析系统 - novel.view_novel 端点错误修复工具

此脚本用于修复 "Could not build url for endpoint 'novel.view_novel' with values ['novel_id']. Did you mean 'view_novel' instead?" 错误。
通过检查和修改模板文件中的URL引用，将 'novel.view_novel' 更正为 'view_novel'。
"""
import os
import logging
import sys
import re
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_novel_endpoint.log')
    ]
)
logger = logging.getLogger(__name__)

def scan_templates_directory(templates_dir="src/web/templates"):
    """扫描模板目录中的所有HTML文件"""
    logger.info(f"扫描模板目录: {templates_dir}")
    
    templates_path = Path(templates_dir)
    if not templates_path.exists():
        logger.error(f"模板目录不存在: {templates_dir}")
        return []
    
    # 收集所有HTML文件
    html_files = []
    for file_path in templates_path.rglob("*.html"):
        html_files.append(str(file_path))
    
    logger.info(f"找到 {len(html_files)} 个HTML文件")
    return html_files

def check_and_fix_template(template_path):
    """检查并修复模板文件中的URL引用"""
    logger.info(f"检查模板文件: {template_path}")
    
    # 读取模板文件内容
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否有对'novel.view_novel'端点的引用
    has_error = False
    
    # 使用正则表达式查找各种形式的引用
    patterns = [
        r"url_for\s*\(\s*['\"]novel\.view_novel['\"]\s*,\s*novel_id\s*=",  # url_for('novel.view_novel', novel_id=...)
        r"url_for\s*\(\s*['\"]novel\.view_novel['\"]\s*\)"                  # url_for('novel.view_novel')
    ]
    
    for pattern in patterns:
        if re.search(pattern, content):
            has_error = True
            break
    
    if not has_error:
        logger.info(f"文件 {template_path} 没有发现错误引用")
        return False
    
    # 创建备份
    backup_path = f"{template_path}.bak"
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    logger.info(f"已创建备份文件: {backup_path}")
    
    # 修复URL引用
    modified_content = re.sub(
        r"url_for\s*\(\s*['\"]novel\.view_novel['\"]\s*,", 
        "url_for('view_novel',", 
        content
    )
    
    # 修复无参数的URL引用
    modified_content = re.sub(
        r"url_for\s*\(\s*['\"]novel\.view_novel['\"]\s*\)", 
        "url_for('view_novel')", 
        modified_content
    )
    
    # 写入修改后的内容
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    logger.info(f"已修复文件 {template_path} 中的URL引用")
    return True

def check_blueprint_registration():
    """检查蓝图注册，确保路由没有冲突"""
    app_file_path = "src/web/app.py"
    logger.info(f"检查蓝图注册: {app_file_path}")
    
    if not os.path.exists(app_file_path):
        logger.error(f"找不到应用文件: {app_file_path}")
        return False
    
    # 读取app.py文件内容
    with open(app_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否有冲突的路由
    has_conflict = "@app.route('/novel/<int:novel_id>')" in content and "novel_bp = Blueprint('novel', __name__)" in content
    
    if not has_conflict:
        logger.info("没有发现路由冲突")
        return False
    
    # 创建备份
    backup_path = f"{app_file_path}.route.bak"
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    logger.info(f"已创建备份文件: {backup_path}")
    
    # 注释掉app.py中的冲突路由
    modified_content = content.replace(
        "@app.route('/novel/<int:novel_id>')", 
        "# 已移至novel_routes.py蓝图\n# @app.route('/novel/<int:novel_id>')"
    )
    
    # 写入修改后的内容
    with open(app_file_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    logger.info("已注释掉冲突的路由定义")
    return True

def main():
    """主函数，执行修复流程"""
    print("九猫小说分析系统 - novel.view_novel 端点错误修复工具")
    print("=" * 50)
    print("此工具将修复'Could not build url for endpoint novel.view_novel'错误")
    print()
    
    try:
        # 检查并修复蓝图注册
        blueprint_fixed = check_blueprint_registration()
        
        # 扫描模板目录
        template_files = scan_templates_directory()
        
        # 逐个检查并修复模板文件
        fixed_count = 0
        for template_path in template_files:
            if check_and_fix_template(template_path):
                fixed_count += 1
        
        if fixed_count > 0 or blueprint_fixed:
            print(f"修复完成！已修复 {fixed_count} 个模板文件。")
            if blueprint_fixed:
                print("已修复路由冲突。")
        else:
            print("未发现需要修复的问题。")
        
        print("请重启应用以应用修复。")
        return 0
    except Exception as e:
        logger.error(f"执行修复过程时出错: {str(e)}", exc_info=True)
        print(f"错误: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 