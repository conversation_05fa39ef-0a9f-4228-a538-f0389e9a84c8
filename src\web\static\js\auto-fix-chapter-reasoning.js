/**
 * 九猫小说分析写作系统 - 自动修复章节推理过程脚本
 *
 * 此脚本用于在页面加载时自动添加推理过程标签页
 * 版本: 1.0.0
 */

(function() {
    console.log('[自动修复章节推理过程] 脚本加载中...');

    // 配置
    const CONFIG = {
        debug: true,
        checkInterval: 1000, // 检查间隔（毫秒）
        maxChecks: 10 // 最大检查次数
    };

    // 状态
    const STATE = {
        initialized: false,
        checkCount: 0
    };

    // 调试日志
    function debugLog(message, level = 'log') {
        if (CONFIG.debug) {
            console[level](`[自动修复章节推理过程] ${message}`);
        }
    }

    // 初始化
    function init() {
        debugLog('初始化自动修复章节推理过程脚本');

        // 检查是否在控制台页面
        if (window.location.pathname.includes('/v3/console') || window.location.pathname.includes('/console')) {
            debugLog('检测到控制台页面，开始自动修复');

            // 延迟执行，确保页面已加载
            setTimeout(autoFix, 1000);
        }

        STATE.initialized = true;
        debugLog('自动修复章节推理过程脚本初始化完成');
    }

    // 自动修复
    function autoFix() {
        debugLog('开始自动修复');

        // 检查是否有章节分析结果
        if ($('#chapterAnalysisContent').length > 0) {
            debugLog('找到章节分析内容容器');

            // 检查是否已存在推理过程标签页
            if ($('#chapterAnalysisResultTab').find('#chapter-reasoning-tab').length === 0) {
                debugLog('推理过程标签页不存在，添加标签页');

                // 检查是否需要创建整个标签页结构
                if ($('#chapterAnalysisResultTab').length === 0) {
                    debugLog('章节分析标签页结构不存在，创建完整结构');

                    // 创建完整的标签页结构
                    const tabsStructure = `
                        <div class="card">
                            <div class="card-header bg-light">
                                <ul class="nav nav-tabs card-header-tabs" id="chapterAnalysisResultTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="chapter-result-tab" data-bs-toggle="tab" data-bs-target="#chapter-result" type="button" role="tab" aria-controls="chapter-result" aria-selected="true">分析结果</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="chapter-reasoning-tab" data-bs-toggle="tab" data-bs-target="#chapter-reasoning" type="button" role="tab" aria-controls="chapter-reasoning" aria-selected="false">推理过程</button>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content" id="chapterAnalysisResultTabContent">
                                    <div class="tab-pane fade show active" id="chapter-result" role="tabpanel" aria-labelledby="chapter-result-tab">
                                        <div id="chapterAnalysisContent">
                                            <div class="text-center py-3">
                                                <div class="spinner-border text-primary" role="status"></div>
                                                <p class="mt-3">加载中，请稍候...</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="chapter-reasoning" role="tabpanel" aria-labelledby="chapter-reasoning-tab">
                                        <div id="chapterReasoningContent">
                                            <div class="text-center py-3">
                                                <div class="spinner-border text-primary" role="status"></div>
                                                <p class="mt-3">加载中，请稍候...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 替换现有内容
                    const cardContainer = $('#chapterAnalysisContent').closest('.card');
                    if (cardContainer.length > 0) {
                        cardContainer.replaceWith(tabsStructure);
                        debugLog('已创建完整的章节分析标签页结构');
                    } else {
                        // 如果找不到现有容器，添加到章节分析区域
                        $('.col-md-9').eq(1).html(tabsStructure);
                        debugLog('已在章节分析区域创建标签页结构');
                    }
                } else {
                    // 只添加推理过程标签页
                    $('#chapterAnalysisResultTab').append(`
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="chapter-reasoning-tab" data-bs-toggle="tab" data-bs-target="#chapter-reasoning" type="button" role="tab" aria-controls="chapter-reasoning" aria-selected="false">推理过程</button>
                        </li>
                    `);

                    // 添加推理过程内容面板
                    $('#chapterAnalysisResultTabContent').append(`
                        <div class="tab-pane fade" id="chapter-reasoning" role="tabpanel" aria-labelledby="chapter-reasoning-tab">
                            <div id="chapterReasoningContent">
                                <div class="text-center py-3">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-3">加载中，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    `);

                    debugLog('已添加推理过程标签页和内容面板');
                }

                // 加载推理过程内容
                loadChapterReasoningContent();
            } else {
                debugLog('推理过程标签页已存在');
            }
        } else {
            // 如果没有找到章节分析内容容器，继续检查
            STATE.checkCount++;
            if (STATE.checkCount < CONFIG.maxChecks) {
                debugLog(`未找到章节分析内容容器，将在${CONFIG.checkInterval}毫秒后重试（${STATE.checkCount}/${CONFIG.maxChecks}）`);
                setTimeout(autoFix, CONFIG.checkInterval);
            } else {
                debugLog('达到最大检查次数，停止自动修复', 'warn');
            }
        }
    }

    // 加载章节推理过程内容
    function loadChapterReasoningContent() {
        debugLog('尝试加载章节推理过程内容');

        // 获取当前选中的模板ID、章节ID和维度
        const templateId = getSelectedTemplateId();
        const chapterId = getSelectedChapterId();
        const dimension = getSelectedDimension();

        if (templateId && chapterId && dimension) {
            debugLog(`找到当前选中的参数：模板ID=${templateId}, 章节ID=${chapterId}, 维度=${dimension}`);

            // 调用API获取章节推理过程
            $.ajax({
                url: `/api/novel/${templateId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        // 渲染推理过程
                        if (response.reasoning_content) {
                            $('#chapterReasoningContent').html(marked.parse(response.reasoning_content));
                            debugLog('成功加载章节推理过程');
                        } else if (response.content) {
                            $('#chapterReasoningContent').html(marked.parse(response.content));
                            debugLog('成功加载章节推理过程');
                        } else {
                            $('#chapterReasoningContent').html(`<div class="alert alert-warning">章节推理过程内容为空</div>`);
                            debugLog('章节推理过程内容为空', 'warn');
                        }
                    } else {
                        $('#chapterReasoningContent').html(`<div class="alert alert-warning">${response.error || '加载推理过程失败'}</div>`);
                        debugLog(`加载章节推理过程失败：${response.error || '未知错误'}`, 'warn');

                        // 尝试备用API路径
                        tryBackupApiPath(templateId, chapterId, dimension);
                    }
                },
                error: function(xhr) {
                    $('#chapterReasoningContent').html('<div class="alert alert-danger">加载推理过程时出错</div>');
                    debugLog(`加载章节推理过程失败：${xhr.status} ${xhr.statusText}`, 'error');

                    // 尝试备用API路径
                    tryBackupApiPath(templateId, chapterId, dimension);
                }
            });
        } else {
            debugLog('未找到当前选中的参数，无法加载章节推理过程', 'warn');
        }
    }

    // 尝试备用API路径
    function tryBackupApiPath(templateId, chapterId, dimension) {
        debugLog('尝试备用API路径');

        $.ajax({
            url: `/api/novels/${templateId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`,
            type: 'GET',
            success: function(backupResponse) {
                if (backupResponse.success) {
                    if (backupResponse.reasoning_content) {
                        $('#chapterReasoningContent').html(marked.parse(backupResponse.reasoning_content));
                        debugLog('通过备用API路径成功加载章节推理过程');
                    } else if (backupResponse.content) {
                        $('#chapterReasoningContent').html(marked.parse(backupResponse.content));
                        debugLog('通过备用API路径成功加载章节推理过程');
                    }
                }
            }
        });
    }

    // 获取当前选中的模板ID
    function getSelectedTemplateId() {
        // 尝试从全局变量获取
        if (typeof selectedTemplateId !== 'undefined' && selectedTemplateId) {
            return selectedTemplateId;
        }

        // 尝试从DOM元素获取
        const selectedTemplate = $('.template-card.border-primary');
        if (selectedTemplate.length > 0) {
            return selectedTemplate.data('template-id');
        }

        return null;
    }

    // 获取当前选中的章节ID
    function getSelectedChapterId() {
        // 尝试从全局变量获取
        if (typeof selectedChapterId !== 'undefined' && selectedChapterId) {
            return selectedChapterId;
        }

        // 尝试从DOM元素获取
        const selectedChapter = $('.chapter-item.active');
        if (selectedChapter.length > 0) {
            return selectedChapter.data('chapter-id');
        }

        return null;
    }

    // 获取当前选中的维度
    function getSelectedDimension() {
        // 尝试从全局变量获取
        if (typeof window.selectedDimension !== 'undefined' && window.selectedDimension) {
            return window.selectedDimension;
        }

        // 尝试从DOM元素获取
        const activeDimension = $('.dimension-item.active');
        if (activeDimension.length > 0) {
            return activeDimension.data('dimension');
        }

        return null;
    }

    // 初始化脚本
    $(document).ready(function() {
        init();
    });
})();
