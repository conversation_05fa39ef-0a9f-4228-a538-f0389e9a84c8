"""
九猫 - 查询数据库脚本
查询数据库中的表和数据
"""

import os
import sqlite3
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_database_file():
    """查找数据库文件"""
    # 可能的数据库文件路径
    possible_paths = [
        'data/novels.db',
        'src/data/novels.db',
        'novels.db',
        'instance/novels.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"找到数据库文件: {path}")
            return path
    
    # 如果没有找到，搜索当前目录及其子目录
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                path = os.path.join(root, file)
                logger.info(f"找到数据库文件: {path}")
                return path
    
    logger.error("未找到数据库文件")
    return None

def query_database():
    """查询数据库"""
    # 查找数据库文件
    db_path = find_database_file()
    if not db_path:
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        logger.info(f"数据库表: {tables}")
        
        # 查询每个表的结构
        for table in tables:
            table_name = table[0]
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            logger.info(f"表 {table_name} 的结构: {columns}")
        
        # 查询novels表
        cursor.execute("SELECT id, title, author FROM novels;")
        novels = cursor.fetchall()
        logger.info(f"小说: {novels}")
        
        # 查询analysis_results表
        cursor.execute("SELECT id, novel_id, dimension FROM analysis_results;")
        results = cursor.fetchall()
        logger.info(f"分析结果: {results}")
        
        # 关闭连接
        conn.close()
        
        return True
    except Exception as e:
        logger.error(f"查询数据库时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始查询数据库")
    
    # 查询数据库
    success = query_database()
    
    if success:
        logger.info("数据库查询成功")
    else:
        logger.error("数据库查询失败")

if __name__ == "__main__":
    main()
