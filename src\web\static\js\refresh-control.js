/**
 * 页面刷新控制模块
 * 提供手动刷新功能，禁用所有自动刷新
 */

// 刷新控制配置
const RefreshConfig = {
    // 默认配置
    defaults: {
        showRefreshNotice: true, // 是否显示刷新通知
        enableAutoRefresh: false, // 自动刷新已禁用
        refreshStatusPosition: 'bottom-right', // 刷新状态显示位置
        checkAnalysisStatus: false, // 不检查分析状态
        novelId: null, // 小说ID
        dimension: null, // 分析维度
        debugMode: true // 调试模式，输出更多日志
    },

    // 当前配置
    current: {},

    // 初始化配置
    init: function(options) {
        // 合并默认配置和用户配置，但强制禁用自动刷新
        this.current = Object.assign({}, this.defaults, options);

        // 强制禁用自动刷新
        this.current.enableAutoRefresh = false;

        // 检查页面是否已经有刷新控制器实例
        if (window._refreshControllerInitialized) {
            console.warn('[刷新控制] 检测到页面已经初始化了刷新控制器，避免重复初始化');
            // 返回当前配置但不执行初始化
            return this.current;
        }

        // 标记刷新控制器已初始化
        window._refreshControllerInitialized = true;

        console.log('[刷新控制] 已禁用所有自动刷新功能');

        return this.current;
    }
};

// 刷新控制器
const RefreshController = {
    // 状态变量
    state: {
        lastRefreshTime: Date.now(),
        refreshCount: 0,
        pageRefreshing: false
    },

    // 初始化刷新控制器
    init: function(options) {
        // 初始化配置
        const config = RefreshConfig.init(options);

        // 如果已经初始化过，直接返回
        if (window._refreshControllerInitialized && window._refreshControllerInstance) {
            this.log('刷新控制器已经初始化，返回现有实例');
            return window._refreshControllerInstance;
        }

        // 保存实例引用
        window._refreshControllerInstance = this;

        // 设置刷新UI
        if (config.showRefreshNotice) {
            this.setupRefreshUI();
        }

        // 清除所有可能的自动刷新
        this.clearAllAutoRefresh();

        // 添加页面卸载事件，记录刷新状态
        window.addEventListener('beforeunload', () => {
            if (this.state.pageRefreshing) {
                // 设置标记，表示这是由刷新控制器触发的刷新
                try {
                    sessionStorage.setItem('refreshController_refreshed', 'true');
                    sessionStorage.setItem('refreshController_refreshTime', Date.now().toString());
                } catch (e) {
                    // 忽略存储错误
                }
            }
        });

        this.log('刷新控制器初始化完成，已禁用所有自动刷新');
        return this;
    },

    // 清除所有可能的自动刷新
    clearAllAutoRefresh: function() {
        // 清除所有可能的定时器
        const clearAllTimers = () => {
            // 查找并清除所有可能的定时器
            for (let i = 1; i < 10000; i++) {
                try {
                    clearTimeout(i);
                    clearInterval(i);
                } catch (e) {
                    // 忽略错误
                }
            }
        };

        // 尝试清除所有定时器
        try {
            clearAllTimers();
            this.log('已清除所有可能的定时器');
        } catch (e) {
            this.log('清除定时器时出错: ' + e.message);
        }

        // 禁用页面的自动刷新元标记
        const metaTags = document.getElementsByTagName('meta');
        for (let i = 0; i < metaTags.length; i++) {
            if (metaTags[i].httpEquiv === 'refresh') {
                metaTags[i].parentNode.removeChild(metaTags[i]);
                this.log('已移除页面自动刷新元标记');
            }
        }
    },

    // 安全日志函数
    log: function(message) {
        try {
            console.log(`[刷新控制] ${message}`);
        } catch (e) {
            // 忽略日志错误
        }
    },

    // 执行页面刷新（仅手动刷新）
    performPageRefresh: function() {
        // 检查是否已经在刷新中
        if (this.state.pageRefreshing) {
            this.log('页面已经在刷新中，忽略重复刷新请求');
            return;
        }

        this.log('执行手动页面刷新');

        // 标记页面正在刷新
        this.state.pageRefreshing = true;

        // 更新最后刷新时间
        this.state.lastRefreshTime = Date.now();
        this.state.refreshCount++;

        // 添加刷新参数，防止缓存
        const url = new URL(window.location.href);
        url.searchParams.set('_refresh', Date.now());
        url.searchParams.set('norefresh', 'true'); // 添加禁用自动刷新的参数

        // 执行刷新
        window.location.href = url.toString();
    },

    // 设置刷新UI
    setupRefreshUI: function() {
        // 创建刷新状态显示元素
        const refreshStatusContainer = document.createElement('div');
        refreshStatusContainer.id = 'refreshStatusContainer';
        refreshStatusContainer.className = 'refresh-status-container';

        // 设置位置样式
        let positionStyle = 'position: fixed; ';
        switch (RefreshConfig.current.refreshStatusPosition) {
            case 'top-right':
                positionStyle += 'top: 10px; right: 10px;';
                break;
            case 'top-left':
                positionStyle += 'top: 10px; left: 10px;';
                break;
            case 'bottom-left':
                positionStyle += 'bottom: 10px; left: 10px;';
                break;
            case 'bottom-right':
            default:
                positionStyle += 'bottom: 10px; right: 10px;';
                break;
        }

        refreshStatusContainer.style.cssText = positionStyle + ' background: rgba(0,0,0,0.7); color: white; padding: 8px 12px; border-radius: 4px; font-size: 12px; z-index: 9999; display: flex; align-items: center;';

        // 添加刷新状态信息
        refreshStatusContainer.innerHTML = `
            <div style="margin-right: 10px;">
                <div>上次刷新: <span id="lastRefreshTime">刚刚</span></div>
                <div><strong>自动刷新已禁用</strong></div>
            </div>
            <div>
                <button id="refreshNowBtn" class="btn btn-sm btn-primary">手动刷新</button>
            </div>
        `;

        document.body.appendChild(refreshStatusContainer);

        // 更新刷新时间显示
        const updateRefreshTimeDisplay = () => {
            const lastRefreshElem = document.getElementById('lastRefreshTime');

            if (lastRefreshElem) {
                const now = Date.now();
                const timeSinceLastRefresh = now - this.state.lastRefreshTime;

                // 格式化上次刷新时间
                if (timeSinceLastRefresh < 60000) {
                    lastRefreshElem.textContent = `${Math.floor(timeSinceLastRefresh / 1000)}秒前`;
                } else if (timeSinceLastRefresh < 3600000) {
                    lastRefreshElem.textContent = `${Math.floor(timeSinceLastRefresh / 60000)}分钟前`;
                } else {
                    const date = new Date(this.state.lastRefreshTime);
                    lastRefreshElem.textContent = `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
                }
            }
        };

        // 立即刷新按钮事件
        document.getElementById('refreshNowBtn').addEventListener('click', () => {
            this.log('用户点击手动刷新按钮');
            this.performPageRefresh();
        });

        // 定期更新显示
        setInterval(updateRefreshTimeDisplay, 1000);

        // 初始更新显示
        updateRefreshTimeDisplay();
    },

    // 检查分析状态（不设置自动刷新）
    checkAnalysisStatusAndSetupRefresh: function(novelId, dimension) {
        // 添加随机参数防止缓存
        const url = `/api/analysis/progress?novel_id=${novelId}&_=${Date.now()}`;

        // 获取分析进度
        fetch(url)
            .then(res => res.json())
            .then(data => {
                if (data.success && data.progress && data.progress[dimension]) {
                    const progressData = data.progress[dimension];
                    const isRunning = data.is_running;
                    const progress = progressData.progress || 0;

                    if (RefreshConfig.current.debugMode) {
                        this.log(`分析状态: 进度=${progress}%, 运行中=${isRunning}`);
                    }

                    // 仅记录状态，不执行任何自动刷新
                    if (progress === 100) {
                        this.log('分析已完成');
                    } else if (isRunning) {
                        this.log('分析正在进行中');
                    } else {
                        this.log('分析暂停中');
                    }
                } else {
                    this.log('未找到分析进度数据');
                }
            })
            .catch(err => {
                console.error('获取分析状态失败:', err);
                this.log('获取分析状态失败');
            });
    }
};

// 导出模块
window.RefreshController = RefreshController;
