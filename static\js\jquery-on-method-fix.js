/**
 * jQuery .on() 方法修复脚本
 * 作为备份方案，确保jQuery.fn.on方法可用
 */
(function() {
    console.log('[jQuery方法修复] 初始化...');

    // 检查并等待jQuery加载
    function ensureJQuery(maxAttempts = 20) {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            
            function checkJQuery() {
                if (typeof jQuery !== 'undefined') {
                    console.log('[jQuery方法修复] jQuery已加载');
                    resolve(jQuery);
                } else {
                    attempts++;
                    if (attempts >= maxAttempts) {
                        console.error('[jQuery方法修复] jQuery加载超时');
                        reject(new Error('jQuery加载超时'));
                        return;
                    }
                    console.log('[jQuery方法修复] 等待jQuery加载，尝试次数:', attempts);
                    setTimeout(checkJQuery, 100);
                }
            }
            
            checkJQuery();
        });
    }

    // 修复jQuery方法
    function fixJQueryMethods($) {
        console.log('[jQuery方法修复] 开始修复jQuery方法');

        // 修复on方法
        if (typeof $.fn.on !== 'function') {
            console.log('[jQuery方法修复] 修复.on()方法');
            $.fn.on = function(events, selector, data, handler) {
                // 处理参数
                if (typeof selector === 'function') {
                    handler = selector;
                    selector = undefined;
                    data = undefined;
                } else if (typeof data === 'function') {
                    handler = data;
                    data = undefined;
                }

                if (!handler) return this;

                // 处理多个事件
                if (events.indexOf(' ') > -1) {
                    const eventArray = events.split(' ');
                    for (let i = 0; i < eventArray.length; i++) {
                        this.on(eventArray[i], selector, data, handler);
                    }
                    return this;
                }

                return this.each(function() {
                    const element = this;

                    // 处理事件委托
                    if (selector) {
                        const originalHandler = handler;
                        handler = function(e) {
                            const target = e.target;
                            const matches = element.querySelectorAll(selector);
                            let current = target;

                            while (current && current !== element) {
                                for (let i = 0; i < matches.length; i++) {
                                    if (current === matches[i]) {
                                        e.delegateTarget = element;
                                        e.currentTarget = current;
                                        originalHandler.call(current, e);
                                        return;
                                    }
                                }
                                current = current.parentNode;
                            }
                        };
                    }

                    // 处理Bootstrap事件
                    if (events.includes('.bs.')) {
                        const baseEvent = events.split('.')[0];
                        element.addEventListener(baseEvent, handler);
                    } else {
                        element.addEventListener(events, handler);
                    }
                });
            };
        }

        // 修复off方法
        if (typeof $.fn.off !== 'function') {
            console.log('[jQuery方法修复] 修复.off()方法');
            $.fn.off = function(events, selector, handler) {
                if (typeof selector === 'function') {
                    handler = selector;
                    selector = undefined;
                }

                return this.each(function() {
                    if (events) {
                        events.split(' ').forEach((event) => {
                            const baseEvent = event.split('.')[0];
                            this.removeEventListener(baseEvent, handler);
                        });
                    }
                });
            };
        }

        // 修复trigger方法
        if (typeof $.fn.trigger !== 'function') {
            console.log('[jQuery方法修复] 修复.trigger()方法');
            $.fn.trigger = function(eventType) {
                return this.each(function() {
                    try {
                        // 尝试创建自定义事件
                        const event = new Event(eventType);
                        this.dispatchEvent(event);
                    } catch (e) {
                        // 降级方案：使用旧的事件API
                        try {
                            const event = document.createEvent('Event');
                            event.initEvent(eventType, true, true);
                            this.dispatchEvent(event);
                        } catch (ie_e) {
                            console.error('[jQuery方法修复] 触发事件失败:', ie_e);
                        }
                    }
                });
            };
        }

        // 修复tab方法（Bootstrap需要）
        if (typeof $.fn.tab !== 'function') {
            console.log('[jQuery方法修复] 修复.tab()方法');
            $.fn.tab = function(action) {
                if (action === 'show') {
                    return this.each(function() {
                        // 触发click事件来激活标签页
                        this.click();
                    });
                }
                return this;
            };
        }

        console.log('[jQuery方法修复] jQuery方法修复完成');
    }

    // 导出全局修复函数
    window.fixJQueryOnMethod = function() {
        ensureJQuery()
            .then(fixJQueryMethods)
            .catch(error => {
                console.error('[jQuery方法修复] 修复失败:', error);
            });
    };

    // 立即执行修复
    ensureJQuery()
        .then(fixJQueryMethods)
        .catch(error => {
            console.error('[jQuery方法修复] 初始化失败:', error);
        });

    console.log('[jQuery方法修复] 初始化完成');
})();