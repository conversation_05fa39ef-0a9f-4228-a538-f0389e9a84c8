/**
 * 静态文件404错误修复脚本
 * 解决 favicon.ico 和 bootstrap.bundle.min.js.map 等静态文件404错误
 */

(function() {
    'use strict';

    // 404错误处理配置
    const STATIC_FILE_CONFIG = {
        // 需要处理的静态文件映射
        fallbackMap: {
            'favicon.ico': '/static/favicon.ico',
            'bootstrap.bundle.min.js.map': '/static/js/bootstrap.bundle.min.js.map',
            'bootstrap.min.js.map': '/static/js/bootstrap.min.js.map',
            'jquery.min.js.map': '/static/js/jquery.min.js.map',
            'chart.min.js.map': '/static/js/chart.min.js.map'
        },
        // 忽略的404错误（不显示在控制台）
        ignoredErrors: [
            'favicon.ico',
            '.map',
            'sourcemap'
        ]
    };

    /**
     * 拦截网络请求错误
     */
    function interceptNetworkErrors() {
        // 拦截 fetch 请求
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            return originalFetch.apply(this, args)
                .then(response => {
                    if (!response.ok && response.status === 404) {
                        handleStaticFile404(args[0], response);
                    }
                    return response;
                })
                .catch(error => {
                    console.warn('[静态文件修复] Fetch请求失败:', error);
                    throw error;
                });
        };

        // 拦截 XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this.addEventListener('error', function() {
                handleStaticFile404(url, this);
            });
            
            this.addEventListener('load', function() {
                if (this.status === 404) {
                    handleStaticFile404(url, this);
                }
            });
            
            return originalXHROpen.call(this, method, url, ...args);
        };
    }

    /**
     * 处理静态文件404错误
     */
    function handleStaticFile404(url, response) {
        try {
            const urlObj = new URL(url, window.location.origin);
            const pathname = urlObj.pathname;
            const filename = pathname.split('/').pop();
            
            // 检查是否是需要处理的静态文件
            const fallbackUrl = STATIC_FILE_CONFIG.fallbackMap[filename];
            
            if (fallbackUrl) {
                console.log(`[静态文件修复] 检测到404错误: ${pathname}, 尝试使用备用路径: ${fallbackUrl}`);
                
                // 尝试加载备用路径
                loadFallbackResource(fallbackUrl, filename);
            } else {
                // 检查是否应该忽略此错误
                const shouldIgnore = STATIC_FILE_CONFIG.ignoredErrors.some(pattern => 
                    filename.includes(pattern)
                );
                
                if (!shouldIgnore) {
                    console.warn(`[静态文件修复] 未处理的404错误: ${pathname}`);
                }
            }
        } catch (error) {
            console.error('[静态文件修复] 处理404错误时出错:', error);
        }
    }

    /**
     * 加载备用资源
     */
    function loadFallbackResource(fallbackUrl, filename) {
        // 根据文件类型选择加载方式
        if (filename.endsWith('.css')) {
            loadFallbackCSS(fallbackUrl);
        } else if (filename.endsWith('.js')) {
            loadFallbackJS(fallbackUrl);
        } else if (filename.endsWith('.ico')) {
            loadFallbackIcon(fallbackUrl);
        } else if (filename.endsWith('.map')) {
            // Source map文件通常不需要实际加载，只需要确保文件存在
            checkSourceMapExists(fallbackUrl);
        }
    }

    /**
     * 加载备用CSS文件
     */
    function loadFallbackCSS(url) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        link.onload = function() {
            console.log(`[静态文件修复] 成功加载备用CSS: ${url}`);
        };
        link.onerror = function() {
            console.warn(`[静态文件修复] 备用CSS加载失败: ${url}`);
        };
        document.head.appendChild(link);
    }

    /**
     * 加载备用JS文件
     */
    function loadFallbackJS(url) {
        const script = document.createElement('script');
        script.src = url;
        script.onload = function() {
            console.log(`[静态文件修复] 成功加载备用JS: ${url}`);
        };
        script.onerror = function() {
            console.warn(`[静态文件修复] 备用JS加载失败: ${url}`);
        };
        document.head.appendChild(script);
    }

    /**
     * 加载备用图标文件
     */
    function loadFallbackIcon(url) {
        // 检查图标是否存在
        const img = new Image();
        img.onload = function() {
            console.log(`[静态文件修复] 备用图标存在: ${url}`);
            // 更新页面图标
            updateFavicon(url);
        };
        img.onerror = function() {
            console.warn(`[静态文件修复] 备用图标不存在: ${url}`);
            // 创建默认图标
            createDefaultFavicon();
        };
        img.src = url;
    }

    /**
     * 检查Source Map文件是否存在
     */
    function checkSourceMapExists(url) {
        fetch(url, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    console.log(`[静态文件修复] Source map文件存在: ${url}`);
                } else {
                    console.log(`[静态文件修复] Source map文件不存在: ${url} (这是正常的)`);
                }
            })
            .catch(error => {
                console.log(`[静态文件修复] 无法检查source map文件: ${url} (这是正常的)`);
            });
    }

    /**
     * 更新页面图标
     */
    function updateFavicon(url) {
        // 移除现有的favicon链接
        const existingLinks = document.querySelectorAll('link[rel="icon"], link[rel="shortcut icon"]');
        existingLinks.forEach(link => link.remove());
        
        // 添加新的favicon链接
        const link = document.createElement('link');
        link.rel = 'icon';
        link.type = 'image/x-icon';
        link.href = url;
        document.head.appendChild(link);
    }

    /**
     * 创建默认图标
     */
    function createDefaultFavicon() {
        // 创建一个简单的SVG图标
        const svg = `
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                <rect width="32" height="32" fill="#007bff"/>
                <text x="16" y="20" font-family="Arial" font-size="16" fill="white" text-anchor="middle">九</text>
            </svg>
        `;
        
        const blob = new Blob([svg], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        
        updateFavicon(url);
        console.log('[静态文件修复] 已创建默认图标');
    }

    /**
     * 修复控制台错误显示
     */
    function fixConsoleErrors() {
        // 拦截console.error以过滤静态文件404错误
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            
            // 检查是否是应该忽略的静态文件错误
            const shouldIgnore = STATIC_FILE_CONFIG.ignoredErrors.some(pattern => 
                message.includes(pattern) && (message.includes('404') || message.includes('Failed to load'))
            );
            
            if (!shouldIgnore) {
                originalConsoleError.apply(console, args);
            } else {
                // 将忽略的错误降级为警告
                console.warn('[静态文件] 忽略的404错误:', ...args);
            }
        };
    }

    /**
     * 预检查静态文件
     */
    function precheckStaticFiles() {
        console.log('[静态文件修复] 开始预检查静态文件...');
        
        Object.entries(STATIC_FILE_CONFIG.fallbackMap).forEach(([filename, fallbackUrl]) => {
            // 检查备用文件是否存在
            fetch(fallbackUrl, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        console.log(`[静态文件修复] 备用文件存在: ${fallbackUrl}`);
                    } else {
                        console.warn(`[静态文件修复] 备用文件不存在: ${fallbackUrl}`);
                    }
                })
                .catch(error => {
                    console.warn(`[静态文件修复] 无法检查备用文件: ${fallbackUrl}`, error);
                });
        });
    }

    /**
     * 创建静态文件状态监控面板
     */
    function createStatusPanel() {
        const panel = $(`
            <div id="staticFileStatus" style="
                position: fixed;
                bottom: 10px;
                left: 10px;
                z-index: 9998;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
                max-width: 300px;
                display: none;
            ">
                <div style="font-weight: bold; margin-bottom: 5px;">静态文件状态</div>
                <div id="staticFileList"></div>
                <button id="toggleStaticStatus" style="
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 2px 6px;
                    border-radius: 2px;
                    font-size: 10px;
                    margin-top: 5px;
                ">隐藏</button>
            </div>
        `);
        
        $('body').append(panel);
        
        // 切换显示/隐藏
        $('#toggleStaticStatus').click(function() {
            const $panel = $('#staticFileStatus');
            const $list = $('#staticFileList');
            
            if ($list.is(':visible')) {
                $list.hide();
                $(this).text('显示');
            } else {
                $list.show();
                $(this).text('隐藏');
            }
        });
        
        // 双击标题显示/隐藏整个面板
        $('#staticFileStatus').dblclick(function() {
            $(this).toggle();
        });
        
        // 更新状态列表
        function updateStatusList() {
            const $list = $('#staticFileList');
            $list.empty();
            
            Object.entries(STATIC_FILE_CONFIG.fallbackMap).forEach(([filename, fallbackUrl]) => {
                const status = $(`<div style="margin: 2px 0;">${filename}: <span style="color: #ffc107;">检查中...</span></div>`);
                $list.append(status);
                
                fetch(fallbackUrl, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            status.find('span').css('color', '#28a745').text('正常');
                        } else {
                            status.find('span').css('color', '#dc3545').text('404');
                        }
                    })
                    .catch(error => {
                        status.find('span').css('color', '#dc3545').text('错误');
                    });
            });
        }
        
        // 初始更新
        updateStatusList();
        
        // 定期更新（每30秒）
        setInterval(updateStatusList, 30000);
        
        // 如果有错误，显示面板
        setTimeout(function() {
            if ($('#staticFileList span[style*="dc3545"]').length > 0) {
                $('#staticFileStatus').show();
            }
        }, 2000);
    }

    // 初始化
    $(document).ready(function() {
        console.log('[静态文件修复] 初始化静态文件404错误修复脚本');
        
        interceptNetworkErrors();
        fixConsoleErrors();
        precheckStaticFiles();
        createStatusPanel();
        
        // 检查并修复favicon
        if (!document.querySelector('link[rel="icon"]') && !document.querySelector('link[rel="shortcut icon"]')) {
            console.log('[静态文件修复] 未找到favicon，尝试加载默认favicon');
            loadFallbackIcon('/static/favicon.ico');
        }
        
        console.log('[静态文件修复] 静态文件404错误修复脚本初始化完成');
    });

})();
