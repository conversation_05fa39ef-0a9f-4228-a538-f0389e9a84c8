"""
全面数据库修复脚本：重建数据库映射并确保所有列都被正确识别
"""
import os
import sys
import logging
import traceback
from datetime import datetime
import json
import sqlite3

# 配置日志
log_filename = f"fix_database_comprehensive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def backup_database():
    """备份当前数据库"""
    try:
        backup_filename = f"novels_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        
        # 检查数据库文件是否存在
        if not os.path.exists("novels.db"):
            logger.warning("数据库文件不存在，跳过备份")
            return True
            
        # 复制数据库文件
        import shutil
        shutil.copy2("novels.db", backup_filename)
        logger.info(f"数据库已备份为 {backup_filename}")
        return True
    except Exception as e:
        logger.error(f"备份数据库失败: {str(e)}")
        logger.error(traceback.format_exc())
        return False
    
def check_database_structure():
    """检查数据库结构"""
    try:
        if not os.path.exists("novels.db"):
            logger.warning("数据库文件不存在，无法检查结构")
            return {}
            
        # 连接数据库
        conn = sqlite3.connect("novels.db")
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall() if not row[0].startswith('sqlite_')]
        
        structure = {}
        for table in tables:
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [{"name": row[1], "type": row[2], "notnull": row[3], "default": row[4], "pk": row[5]} 
                      for row in cursor.fetchall()]
            structure[table] = columns
            
        conn.close()
        return structure
            except Exception as e:
        logger.error(f"检查数据库结构失败: {str(e)}")
        logger.error(traceback.format_exc())
        return {}

def recreate_models_mapping():
    """重新创建模型映射"""
    try:
        # 将当前工作目录添加到Python路径
        sys.path.insert(0, os.getcwd())
        
        # 尝试导入所需模块
        try:
            from sqlalchemy import inspect
            from src.db.connection import engine, Base, init_db
            from src.models.novel import Novel
            from src.models.analysis_result import AnalysisResult
            from src.models.analysis_process import AnalysisProcess
            from src.models.intermediate_result import IntermediateResult
            
            # 检查当前映射
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            logger.info(f"当前数据库表: {tables}")
        
            # 记录每个表的列
            for table in tables:
                columns = [column['name'] for column in inspector.get_columns(table)]
                logger.info(f"表 {table} 的列: {columns}")
            
            # 重新初始化数据库
            logger.info("重新初始化数据库...")
            init_db()
            
            # 验证映射是否正确
            inspector = inspect(engine)
            for table in tables:
                columns = [column['name'] for column in inspector.get_columns(table)]
                logger.info(f"修复后表 {table} 的列: {columns}")
                
            logger.info("模型映射已重新创建")
            return True
        except ImportError as e:
            logger.error(f"导入模块失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
            except Exception as e:
        logger.error(f"重新创建模型映射失败: {str(e)}")
        logger.error(traceback.format_exc())
                return False
        
def verify_database():
    """验证数据库是否可用"""
    try:
        # 尝试进行简单查询
        conn = sqlite3.connect("novels.db")
        cursor = conn.cursor()
        
        # 检查novels表
        cursor.execute("SELECT COUNT(*) FROM novels")
        novel_count = cursor.fetchone()[0]
        logger.info(f"数据库中有 {novel_count} 个小说记录")
        
        # 获取novels表的一行数据进行结构验证
        if novel_count > 0:
            cursor.execute("SELECT * FROM novels LIMIT 1")
            column_names = [description[0] for description in cursor.description]
            logger.info(f"novels表列名: {column_names}")
            
            # 检查是否有file_name列
            if "file_name" in column_names:
                logger.info("novels表中file_name列已存在且可访问")
            else:
                logger.warning("novels表中file_name列不存在!")
        
        conn.close()
        logger.info("数据库验证完成")
        return True
    except Exception as e:
        logger.error(f"验证数据库失败: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def fix_file_name_column():
    """修复file_name列问题"""
    try:
        conn = sqlite3.connect("novels.db")
        cursor = conn.cursor()
        
        # 检查novels表是否有file_name列
        cursor.execute("PRAGMA table_info(novels)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if "file_name" not in columns:
            logger.info("添加file_name列...")
            cursor.execute("ALTER TABLE novels ADD COLUMN file_name TEXT")
            conn.commit()
            logger.info("成功添加file_name列")
        else:
            logger.info("file_name列已存在")
            
        # 从file_path更新file_name
        cursor.execute("""
            UPDATE novels 
            SET file_name = SUBSTR(file_path, INSTR(file_path, '\\')+1) 
            WHERE file_path IS NOT NULL AND (file_name IS NULL OR file_name = '') AND INSTR(file_path, '\\') > 0
        """)
        
        # 处理使用正斜杠的路径
        cursor.execute("""
            UPDATE novels 
            SET file_name = SUBSTR(file_path, INSTR(file_path, '/')+1) 
            WHERE file_path IS NOT NULL AND (file_name IS NULL OR file_name = '') AND INSTR(file_path, '/') > 0
        """)
        
        # 如果file_path本身就是文件名（没有路径分隔符）
        cursor.execute("""
            UPDATE novels 
            SET file_name = file_path 
            WHERE file_path IS NOT NULL AND (file_name IS NULL OR file_name = '')
            AND INSTR(file_path, '\\') = 0 AND INSTR(file_path, '/') = 0
        """)
        
        rows_updated = conn.total_changes
        conn.commit()
        logger.info(f"更新了 {rows_updated} 行数据的file_name")
        
        conn.close()
        return True
    except Exception as e:
        logger.error(f"修复file_name列失败: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def fix_database_tables():
    """修复数据库表"""
    try:
        # 确保所有必要的表都存在，列都正确
        structure = check_database_structure()
        if not structure:
            logger.warning("无法获取数据库结构信息")
            return False
            
        logger.info("数据库表结构:")
        for table, columns in structure.items():
            column_names = [c["name"] for c in columns]
            logger.info(f"  表 {table}: {column_names}")
            
        # 重新创建模型映射
        recreate_models_mapping()
        
        # 修复file_name列
        fix_file_name_column()
        
        # 验证修复后的数据库
        verify_database()
        
        return True
    except Exception as e:
        logger.error(f"修复数据库表失败: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("========== 开始全面数据库修复 ==========")
    
    # 备份数据库
    if not backup_database():
        logger.error("备份数据库失败，修复终止")
        return
        
    # 修复数据库表
    if fix_database_tables():
        logger.info("数据库表已修复")
        print("\n✓ 数据库修复成功完成！数据库结构已更新。")
        print("✓ 现在您可以正常使用九猫小说分析系统了。")
    else:
        logger.error("数据库表修复失败")
        print("\n✗ 数据库修复失败，请查看日志获取详细信息。")
        print(f"✗ 日志文件: {log_filename}")
    
    logger.info("========== 数据库修复结束 ==========")

if __name__ == "__main__":
    main()
