"""
九猫系统 - 章节分析提示词增强器 v2
用于增强章节分析提示词，确保分析更加详细和连贯
"""

import logging
import re
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

def enhance_chapter_analysis_prompt_v2(original_prompt: str, dimension: str, prompt_template: str = "default") -> str:
    """
    增强章节分析提示词，添加用户要求的补充内容

    Args:
        original_prompt: 原始提示词
        dimension: 分析维度
        prompt_template: 提示词模板，默认为 default，可选 simplified

    Returns:
        增强后的提示词
    """
    # 根据提示词模板选择不同的增强内容
    if prompt_template == "simplified":
        # 精简版增强内容 - 温和优化模式
        general_enhancement = """
重要补充要求（精简版温和优化模式）：
1. 基于100%原文分析：分析必须基于章节的实际内容，避免泛泛而谈
2. 重点叙述原文内容：重点描述章节中的关键场景、事件、对话和人物心理活动
3. 精炼详细：分析结果要精炼但保持必要详细度，重点突出核心内容
4. 承接上一章节：将前序章节的核心分析作为本章分析的基础，展示章节间的主要连贯性
5. 简洁明了：使用清晰、简洁的语言表达，避免冗余和过多专业术语

分析内容应包含：
- 关键场景描述：重点描述最重要场景的环境、氛围和背景
- 核心事件过程：按顺序描述最重要事件的起因、经过和结果
- 主要对话互动：记录主要人物的核心对话内容
- 重要心理活动：描述人物的关键心理活动和重要情感变化
- 核心情节脉络：展示情节的主要发展过程

以上要求针对精简版温和优化模式，在保证分析质量的同时进一步控制成本。
"""
    else:
        # 默认版增强内容 - 完整模式
        general_enhancement = """
重要补充要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 必须详细叙述原文内容：极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理活动
3. 不限字数，越详细越好：分析结果必须极其详细，不设字数限制，至少5000字以上
4. 必须承接上一章节同维度的分析结果和推理过程：将前序章节的分析作为本章分析的基础，展示章节间的连贯性和发展
5. 语言必须通俗易懂：使用清晰、生动的语言，避免过多专业术语

分析内容必须包含：
- 详细的场景描述：环境、氛围、背景、光线、声音、气味等感官细节
- 完整的事件过程：按照发生顺序详细描述每个重要事件的起因、经过和结果
- 人物对话与互动：完整记录主要人物的对话内容，尽可能使用原文中的重要对话
- 人物心理活动：深入描述人物的心理活动、情感变化和内心冲突
- 情节发展脉络：清晰展示情节如何从开始发展到结束，包括转折点和高潮

以上要求是首要执行的，必须执行的！在进行分析时，请首先考虑这些要求。
"""

    # 特定维度的增强内容
    if prompt_template == "simplified":
        # 精简版特定维度增强内容
        dimension_specific_enhancements = {
            "chapter_outline": """
章纲分析特别要求（精简版温和优化模式）：
1. 基于原文分析：分析基于章节的实际内容，避免泛泛而谈
2. 重点叙述原文内容：重点描述章节中发生的关键事件，包括主要场景、事件、对话和人物心理活动
3. 精炼详细：内容重现要精炼但保持必要详细度，重点突出核心内容
4. 承接上一章节：将前序章节的核心分析作为本章分析的基础，展示章节间的主要连贯性
5. 简洁明了：使用清晰、简洁的语言表达，避免冗余

内容重现应包含：
- 关键场景描述：重点描述最重要场景的环境、氛围和背景
- 核心事件过程：按顺序描述最重要事件的起因、经过和结果
- 主要对话互动：记录主要人物的核心对话内容
- 重要心理活动：描述人物的关键心理活动和重要情感变化
- 核心情节脉络：展示情节的主要发展过程

以上要求针对精简版温和优化模式，在保证分析质量的同时进一步控制成本。
""",
        }
    else:
        # 默认版特定维度增强内容
        dimension_specific_enhancements = {
            "chapter_outline": """
章纲分析特别要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 必须极其详细叙述原文内容：极其详尽地描述章节中发生的一切，包括场景、事件、对话和人物心理活动
3. 不限字数，必须极其详细：内容重现部分必须极其详细，至少8000字以上
4. 必须承接上一章节的章纲分析：将前序章节的分析作为本章分析的基础，展示章节间的连贯性和发展
5. 语言必须通俗易懂：使用清晰、生动的语言，模仿原文的风格和语气

内容重现部分必须包含：
- 详细的场景描述（至少1000字）：环境、氛围、背景、光线、声音、气味等感官细节
- 完整的事件过程（至少2000字）：按照发生顺序详细描述每个重要事件的起因、经过和结果
- 人物对话与互动（至少2000字）：完整记录主要人物的对话内容，尽可能使用原文中的重要对话
- 人物心理活动（至少1000字）：深入描述人物的心理活动、情感变化和内心冲突
- 情节发展脉络（至少1000字）：清晰展示情节如何从开始发展到结束，包括转折点和高潮

以上要求是首要执行的，必须执行的！在进行章纲分析时，请首先考虑这些要求。
""",
            "structure": """
结构分析特别要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 必须详细分析章节结构：极其详尽地分析章节的开头、发展、高潮、结尾等结构元素
3. 不限字数，必须极其详细：分析结果必须极其详细，至少5000字以上
4. 必须承接上一章节的结构分析：将前序章节的分析作为本章分析的基础，展示章节间的结构连贯性
5. 必须分析章节在整体结构中的作用：分析该章节如何推动整体故事发展

结构分析必须包含：
- 章节内部结构分析（至少1000字）：详细分析章节的开头、发展、高潮、结尾等结构元素
- 章节功能定位分析（至少1000字）：分析章节的功能类型（推进型/铺垫型/转折型/高潮型/过渡型）
- 目标-阻碍-行动单元分析（至少1000字）：分析章节中的G-O-A结构完整性
- 章节间关系分析（至少1000字）：分析本章节与前后章节的关系和连接方式
- 读者体验设计分析（至少1000字）：分析章节如何管理读者期待和维持读者兴趣

以上要求是首要执行的，必须执行的！在进行结构分析时，请首先考虑这些要求。
""",
            "character_relationships": """
人物关系分析特别要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 必须详细分析人物关系：极其详尽地分析章节中出现的所有人物及其关系
3. 不限字数，必须极其详细：分析结果必须极其详细，至少5000字以上
4. 必须承接上一章节的人物关系分析：将前序章节的分析作为本章分析的基础，展示人物关系的发展变化
5. 必须使用通俗易懂的语言：避免晦涩难懂的学术化表达

人物关系分析必须包含：
- 主要人物分析（每个人物至少500字）：详细分析每个主要人物的性格、动机、行为和心理
- 人物关系网络分析（至少1000字）：分析所有人物之间的关系网络和互动模式
- 人物冲突分析（至少1000字）：分析章节中的人物冲突及其原因和影响
- 人物关系变化分析（至少1000字）：分析本章节中人物关系的变化和发展
- 人物关系对情节的推动作用（至少1000字）：分析人物关系如何推动情节发展

以上要求是首要执行的，必须执行的！在进行人物关系分析时，请首先考虑这些要求。
""",
            "opening_effectiveness": """
开篇效果分析特别要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 必须详细分析开篇效果：极其详尽地分析章节开篇的效果和技巧
3. 不限字数，必须极其详细：分析结果必须极其详细，至少5000字以上
4. 必须包含五个具体提示词分析：三章关键元素计算、首章危机浓度检测、悬念钩子强度评估、金手指显性度扫描和共情指数图谱生成
5. 必须承接上一章节的开篇效果分析：将前序章节的分析作为本章分析的基础

开篇效果分析必须包含：
- 三章关键元素计算（至少1000字）：分析章节中的关键元素及其在前三章中的分布和作用
- 首章危机浓度检测（至少1000字）：分析章节中的危机元素及其强度和影响
- 悬念钩子强度评估（至少1000字）：分析章节中的悬念设置和钩子技巧及其效果
- 金手指显性度扫描（至少1000字）：分析章节中的金手指元素及其显性程度和合理性
- 共情指数图谱生成（至少1000字）：分析章节中的共情元素及其对读者情感的影响

以上要求是首要执行的，必须执行的！在进行开篇效果分析时，请首先考虑这些要求。
"""
        }

    # 获取特定维度的增强内容，如果没有则使用通用增强内容
    enhancement = dimension_specific_enhancements.get(dimension, general_enhancement)

    # 检查原始提示词是否已经包含增强内容
    if prompt_template == "simplified":
        # 精简版检查
        if "精简版温和优化模式" in original_prompt:
            logger.info(f"精简版提示词已包含增强内容，无需再次添加")
            return original_prompt
    else:
        # 默认版检查
        if "必须使用原文" in original_prompt and "必须详细叙述原文内容" in original_prompt:
            logger.info(f"默认版提示词已包含增强内容，无需再次添加")
            return original_prompt

    # 在提示词的开头添加增强内容
    # 查找合适的插入位置：在第一个"你是"或"请"之后
    match = re.search(r'(你是.*?专家.*?[。，,.]|请.*?分析.*?[。，,.])', original_prompt)
    if match:
        insert_position = match.end()
        enhanced_prompt = original_prompt[:insert_position] + enhancement + original_prompt[insert_position:]
    else:
        # 如果找不到合适的位置，就在开头添加
        enhanced_prompt = enhancement + original_prompt

    logger.info(f"成功增强{dimension}维度的章节分析提示词（{prompt_template}版）")
    return enhanced_prompt

# 测试函数
if __name__ == "__main__":
    test_prompt = "你是一位经验丰富的文学分析专家，请对以下章节文本进行详细的结构分析。"
    enhanced = enhance_chapter_analysis_prompt_v2(test_prompt, "structure")
    print(enhanced)
