{% extends "v3/base.html" %}

{% block title %}控制台 - 九猫小说分析写作系统v3.0{% endblock %}

{% block head %}
{{ super() }}
<!-- 添加Markdown渲染库 -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<!-- 添加GitHub风格的Markdown样式 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css@5.1.0/github-markdown.min.css">
<style>
    .markdown-body {
        box-sizing: border-box;
        min-width: 200px;
        max-width: 100%;
        padding: 15px;
        background-color: #fff;
        border-radius: 5px;
    }
    .console-output {
        max-height: 70vh;
        overflow-y: auto;
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    /* 自定义Markdown样式 */
    .markdown-body h1, .markdown-body h2, .markdown-body h3 {
        border-bottom: 1px solid #eaecef;
        padding-bottom: 0.3em;
    }
    .markdown-body blockquote {
        padding: 0 1em;
        color: #6a737d;
        border-left: 0.25em solid #dfe2e5;
    }
    .markdown-body pre {
        background-color: #f6f8fa;
        border-radius: 3px;
        padding: 16px;
        overflow: auto;
    }
    .markdown-body code {
        background-color: rgba(27,31,35,0.05);
        border-radius: 3px;
        padding: 0.2em 0.4em;
    }
</style>
{% endblock %}

{% block extra_css %}
<style>
    .console-container {
        height: calc(100vh - 250px);
        min-height: 500px;
    }
    .console-sidebar {
        border-right: 1px solid #e9ecef;
        height: 100%;
        overflow-y: auto;
    }
    .console-main {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .console-output {
        flex-grow: 1;
        overflow-y: auto;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 1rem;
        font-family: 'Courier New', Courier, monospace;
        white-space: pre-wrap;
    }
    .console-input {
        margin-top: 1rem;
    }
    .template-card {
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .template-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .template-card.active {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }
    .dimension-item {
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }
    .dimension-item:hover {
        background-color: #f8f9fa;
    }
    .dimension-item.active {
        background-color: #e9ecef;
        font-weight: bold;
    }
    .chapter-item {
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }
    .chapter-item:hover {
        background-color: #f8f9fa;
    }
    .chapter-item.active {
        background-color: #e9ecef;
        font-weight: bold;
    }
    .nav-tabs .nav-link {
        border: none;
        color: #495057;
        font-weight: 500;
        padding: 0.75rem 1rem;
    }
    .nav-tabs .nav-link.active {
        color: #4a6bff;
        border-bottom: 3px solid #4a6bff;
        background-color: transparent;
    }

    /* 预设内容样式 */
    .preset-item {
        cursor: pointer;
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
    }
    .preset-item:hover {
        background-color: #f8f9fa;
    }
    .preset-item.active {
        border-left-color: #4a6bff;
        background-color: #e9ecef;
    }

    /* 运行日志样式 */
    .log-line {
        padding: 2px 0;
        line-height: 1.4;
        white-space: pre-wrap;
        word-break: break-word;
    }
    .log-timestamp {
        color: #569cd6;
        margin-right: 0.5rem;
    }
    .log-welcome {
        color: #8a8a8a;
        font-style: italic;
    }
    .log-info {
        color: #f0f0f0;
    }
    .log-warn {
        color: #dcdcaa;
    }
    .log-error {
        color: #f14c4c;
    }
    .log-filter {
        cursor: pointer;
    }
    .log-filter.active {
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<script>
// 获取分类名称
window.getCategoryName = function(category) {
    const categoryNames = {
        'writing_prompt': '写作提示',
        'character': '人物设定',
        'plot': '情节构思',
        'scene': '场景描写',
        'knowledge_base': '知识库',
        'chapter_template': '章节预设模板',
        'other': '其他'
    };
    return categoryNames[category] || category;
}

// 定义全局变量
let presets = []; // 存储预设内容
let currentPresetId = null; // 当前选中的预设ID

// 格式化日期
window.formatDate = function(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
};

// 定义全局函数
window.loadPresets = function() {
    $('#presetList').html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><p class="small mt-2">加载中...</p></div>');

    // 调用API获取预设列表
    $.ajax({
        url: '/api/presets',
        type: 'GET',
        success: function(response) {
            if (response.success) {
                presets = response.presets || [];

                if (presets.length > 0) {
                    let presetsHtml = '';
                    presets.forEach(preset => {
                        const isActive = preset.id === currentPresetId ? 'active' : '';
                        // 根据预设类型设置不同的"展开查看"按钮链接
                        const viewUrl = preset.category === 'knowledge_base'
                            ? `/v3/preset/${preset.id}/templates` // 知识库类型直接链接到预设模板页面
                            : `/v3/preset/${preset.id}`; // 其他类型链接到预设详情页面

                        presetsHtml += `
                            <a href="javascript:void(0)" class="list-group-item list-group-item-action preset-item ${isActive}" data-preset-id="${preset.id}">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${preset.title}</h6>
                                    <small>${window.getCategoryName(preset.category)}</small>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <small class="text-muted">${window.formatDate(preset.created_at)}</small>
                                    <a href="${viewUrl}" class="btn btn-sm btn-outline-info" target="_blank" onclick="event.stopPropagation();">
                                        <i class="fas fa-expand-alt"></i> 展开查看
                                    </a>
                                </div>
                            </a>
                        `;
                    });
                    $('#presetList').html(presetsHtml);
                } else {
                    $('#presetList').html('<div class="text-center py-3"><small class="text-muted">暂无预设内容</small><p class="small mt-2"><button id="createFirstPresetBtn" class="btn btn-sm btn-outline-primary">创建第一个预设</button></p></div>');

                    // 绑定创建第一个预设按钮事件
                    $('#createFirstPresetBtn').click(function() {
                        $('#newPresetBtn').click();
                    });
                }

                // 绑定预设点击事件
                $('.preset-item').click(function() {
                    const presetId = $(this).data('preset-id');
                    window.loadPreset(presetId);
                });

                // 添加日志
                addLogEntry('info', `已加载 ${presets.length} 个预设内容`);
            } else {
                $('#presetList').html(`<div class="alert alert-warning">${response.error || '无法加载预设列表'}</div>`);
                addLogEntry('warn', `加载预设列表失败: ${response.error || '未知错误'}`);
            }
        },
        error: function(xhr) {
            $('#presetList').html('<div class="alert alert-danger">加载预设列表时出错，请刷新页面重试</div>');
            addLogEntry('error', `加载预设列表出错: ${xhr.status} ${xhr.statusText}`);

            // 自动重试
            setTimeout(function() {
                addLogEntry('info', '正在尝试重新加载预设列表...');
                window.loadPresets();
            }, 5000);
        }
    });
};
</script>

<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h1 class="mb-3"><i class="fas fa-terminal text-info me-2"></i>控制台</h1>
                <p class="lead">控制台是九猫系统的核心功能，可以调用参考蓝本中的分析结果，进行自动写作。</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong> 请先选择一个参考蓝本，然后查看分析结果或开始自动写作。
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <!-- 参考蓝本选择 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-info text-white">
                <h3 class="card-title mb-0"><i class="fas fa-bookmark me-2"></i>选择参考蓝本</h3>
            </div>
            <div class="card-body">
                {% if templates %}
                    <div class="row">
                        {% for template in templates %}
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 template-card" data-template-id="{{ template.id }}">
                                    <div class="card-body">
                                        <h4 class="card-title">{{ template.title }}</h4>
                                        <p class="card-text text-muted">
                                            {% if template.author %}作者: {{ template.author }}<br>{% endif %}
                                            字数: {{ template.word_count }}<br>
                                            章节数: {{ template.chapter_count }}
                                        </p>
                                        <div class="text-center mt-3">
                                            <button class="btn btn-primary btn-sm select-template-btn" data-template-id="{{ template.id }}">
                                                <i class="fas fa-check me-1"></i>选择此蓝本
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-bookmark fa-3x text-muted mb-3"></i>
                        <h4>暂无参考蓝本</h4>
                        <p class="text-muted">您需要先创建参考蓝本才能使用控制台功能。</p>
                        <a href="{{ url_for('v3.reference_templates') }}" class="btn btn-primary mt-2">
                            <i class="fas fa-plus me-1"></i>创建参考蓝本
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 控制台主界面 -->
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <ul class="nav nav-tabs card-header-tabs" id="consoleTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="true">
                            <i class="fas fa-terminal me-1"></i>运行日志
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab" aria-controls="analysis" aria-selected="false">
                            <i class="fas fa-search me-1"></i>分析结果
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="preset-tab" data-bs-toggle="tab" data-bs-target="#preset" type="button" role="tab" aria-controls="preset" aria-selected="false">
                            <i class="fas fa-file-alt me-1"></i>预设内容
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="writing-tab" data-bs-toggle="tab" data-bs-target="#writing" type="button" role="tab" aria-controls="writing" aria-selected="false">
                            <i class="fas fa-pencil-alt me-1"></i>自动写作
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body p-0">
                <div class="tab-content" id="consoleTabContent">
                    <!-- 运行日志标签页 -->
                    <div class="tab-pane fade show active" id="logs" role="tabpanel" aria-labelledby="logs-tab">
                        <div class="p-3">
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <h4 class="mb-3">系统运行日志</h4>
                                    <p class="text-muted">显示系统运行过程中的日志信息，包括API调用、分析过程和错误信息。</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group">
                                        <button id="clearLogsBtn" class="btn btn-outline-secondary">
                                            <i class="fas fa-eraser me-1"></i>清空日志
                                        </button>
                                        <button id="exportLogsBtn" class="btn btn-outline-primary">
                                            <i class="fas fa-download me-1"></i>导出日志
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">日志过滤</h5>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="autoScrollSwitch" checked>
                                            <label class="form-check-label" for="autoScrollSwitch">自动滚动</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="input-group">
                                                <input type="text" id="logSearch" class="form-control" placeholder="搜索日志内容...">
                                                <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="btn-group w-100">
                                                <button type="button" class="btn btn-outline-secondary btn-sm log-filter active" data-type="all">全部</button>
                                                <button type="button" class="btn btn-outline-info btn-sm log-filter" data-type="info">信息</button>
                                                <button type="button" class="btn btn-outline-warning btn-sm log-filter" data-type="warn">警告</button>
                                                <button type="button" class="btn btn-outline-danger btn-sm log-filter" data-type="error">错误</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">日志内容</h5>
                                </div>
                                <div class="card-body p-0">
                                    <div id="logContent" class="console-output" style="height: 400px; background-color: #1e1e1e; color: #f0f0f0; font-family: 'Consolas', 'Courier New', monospace;">
                                        <!-- 日志内容将通过JavaScript动态加载 -->
                                        <div class="p-3">
                                            <div class="log-line log-welcome">
                                                <span class="log-timestamp">[{{ now.strftime('%H:%M:%S') }}]</span> 欢迎使用九猫系统运行日志。此日志显示系统运行过程中的重要信息。
                                            </div>
                                            <div class="log-line log-info">
                                                <span class="log-timestamp">[{{ now.strftime('%H:%M:%S') }}]</span> 系统已启动，准备就绪。
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析结果标签页 -->
                    <div class="tab-pane fade" id="analysis" role="tabpanel" aria-labelledby="analysis-tab">
                        <div class="row g-0 console-container">
                            <div class="col-md-3 console-sidebar p-3">
                                <h5 class="mb-3">整本书的分析维度</h5>
                                <div id="dimensionList" class="mb-4">
                                    <!-- 分析维度列表将通过JavaScript动态加载 -->
                                    <div class="text-center py-3">
                                        <small class="text-muted">请先选择参考蓝本</small>
                                    </div>
                                </div>

                                <h5 class="mb-3">章节列表</h5>
                                <div id="chapterList">
                                    <!-- 章节列表将通过JavaScript动态加载 -->
                                    <div class="text-center py-3">
                                        <small class="text-muted">请先选择参考蓝本</small>
                                    </div>
                                </div>

                                <!-- 调试信息已移除 -->
                            </div>
                            <div class="col-md-9 console-main p-3">
                                <!-- 分析内容标签页 -->
                                <ul class="nav nav-tabs mb-3" id="analysisTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="result-tab" data-bs-toggle="tab" data-bs-target="#result" type="button" role="tab" aria-controls="result" aria-selected="true">
                                            <i class="fas fa-chart-bar me-1"></i>分析结果
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="reasoning-tab" data-bs-toggle="tab" data-bs-target="#reasoning" type="button" role="tab" aria-controls="reasoning" aria-selected="false">
                                            <i class="fas fa-brain me-1"></i>推理过程
                                        </button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="analysisTabContent">
                                    <div class="tab-pane fade show active" id="result" role="tabpanel" aria-labelledby="result-tab">
                                        <div id="analysisContent" class="console-output markdown-body">
                                            欢迎使用九猫控制台！

                                            请先从左侧选择一个参考蓝本，然后选择要查看的分析维度或章节。

                                            控制台可以帮助您：
                                            1. 查看参考蓝本的分析结果
                                            2. 查看分析推理过程
                                            3. 进行自动写作

                                            开始使用吧！
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="reasoning" role="tabpanel" aria-labelledby="reasoning-tab">
                                        <div id="reasoningContent" class="console-output markdown-body">
                                            选择分析维度后，这里将显示详细的推理过程。
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 预设内容标签页 -->
                    <div class="tab-pane fade" id="preset" role="tabpanel" aria-labelledby="preset-tab">
                        <div class="p-3">
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <h4 class="mb-3">预设内容管理</h4>
                                    <p class="text-muted">预设内容可以保存常用的写作提示、文本片段或草稿，方便后续使用。</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button id="newPresetBtn" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>新建预设
                                    </button>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <!-- 预设列表 -->
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">预设列表</h5>
                                                <div class="input-group input-group-sm" style="width: 150px;">
                                                    <input type="text" id="presetSearch" class="form-control" placeholder="搜索预设...">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="list-group list-group-flush" id="presetList" style="max-height: 400px; overflow-y: auto;">
                                            <!-- 预设列表将通过JavaScript动态加载 -->
                                            <div class="text-center py-3">
                                                <small class="text-muted">暂无预设内容</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <!-- 预设编辑区 -->
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0" id="presetEditorTitle">编辑预设</h5>
                                                <div>
                                                    <button id="savePresetBtn" class="btn btn-sm btn-success">
                                                        <i class="fas fa-save me-1"></i>保存
                                                    </button>
                                                    <button id="deletePresetBtn" class="btn btn-sm btn-danger ms-1">
                                                        <i class="fas fa-trash me-1"></i>删除
                                                    </button>
                                                    <button id="readPresetBtn" class="btn btn-sm btn-info ms-1">
                                                        <i class="fas fa-book-reader me-1"></i>读取
                                                    </button>
                                                    <button id="expandPresetBtn" class="btn btn-sm btn-outline-info ms-1">
                                                        <i class="fas fa-expand-alt me-1"></i>展开查看
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="presetTitle" class="form-label">标题</label>
                                                <input type="text" class="form-control" id="presetTitle" placeholder="输入预设标题">
                                            </div>
                                            <div class="mb-3">
                                                <label for="presetContent" class="form-label">内容</label>
                                                <textarea class="form-control" id="presetContent" rows="10" placeholder="输入预设内容..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label for="presetCategory" class="form-label">分类</label>
                                                <select class="form-select" id="presetCategory">
                                                    <option value="writing_prompt">写作提示</option>
                                                    <option value="character">人物设定</option>
                                                    <option value="plot">情节构思</option>
                                                    <option value="scene">场景描写</option>
                                                    <option value="knowledge_base">知识库</option>
                                                    <option value="chapter_template">章节预设模板</option>
                                                    <option value="other">其他</option>
                                                </select>
                                            </div>
                                            <input type="hidden" id="presetId" value="">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 自动写作标签页 -->
                    <div class="tab-pane fade" id="writing" role="tabpanel" aria-labelledby="writing-tab">
                        <div class="p-3">
                            <div class="mb-3">
                                <label for="writingPrompt" class="form-label">写作提示</label>
                                <textarea class="form-control" id="writingPrompt" rows="3" placeholder="请输入写作提示，例如：写一个类似于参考蓝本风格的章节，主题是..."></textarea>
                            </div>
                            <div class="mb-3">
                                <button id="startWritingBtn" class="btn btn-primary">
                                    <i class="fas fa-play me-1"></i>开始写作
                                </button>
                                <button id="clearOutputBtn" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-eraser me-1"></i>清空输出
                                </button>
                            </div>
                            <div class="console-output" id="writingOutput">
                                自动写作功能将基于选定的参考蓝本生成内容。

                                请先选择一个参考蓝本，然后在上方输入写作提示，点击"开始写作"按钮。

                                系统将分析参考蓝本的风格、结构、人物等特点，生成符合要求的内容。

                                生成的内容将显示在这里。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入浏览器控制台捕获工具 -->
<script src="{{ url_for('static', filename='js/browser-console-capture.js') }}"></script>
<!-- 引入知识库创建脚本 -->
<script src="{{ url_for('static', filename='js/create-knowledge-base.js') }}"></script>
<!-- 引入分析理解生成器 -->
<script src="{{ url_for('static', filename='js/analysis-understanding-generator.js') }}"></script>
<!-- 添加参考蓝本选择器脚本 -->
<script src="{{ url_for('static', filename='js/v3/template_selector.js') }}"></script>
<script>
    // 强制刷新页面内容，解决缓存问题
    function forceRefresh() {
        location.reload(true);
    }

    // 格式化时间戳
    function formatTimestamp() {
        const now = new Date();
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        return `[${hours}:${minutes}:${seconds}]`;
    }

    // 添加日志条目
    function addLogEntry(type, message) {
        const logContent = document.getElementById('logContent');
        if (!logContent) return;

        const logLine = document.createElement('div');
        logLine.className = `log-line log-${type}`;
        logLine.innerHTML = `<span class="log-timestamp">${formatTimestamp()}</span> ${message}`;
        logLine.dataset.type = type;

        logContent.appendChild(logLine);

        // 自动滚动到底部
        if (document.getElementById('autoScrollSwitch') && document.getElementById('autoScrollSwitch').checked) {
            logContent.scrollTop = logContent.scrollHeight;
        }

        // 应用过滤器
        applyLogFilters();
    }

    // 应用日志过滤器
    function applyLogFilters() {
        const logContent = document.getElementById('logContent');
        if (!logContent) return;

        const activeFilter = document.querySelector('.log-filter.active');
        if (!activeFilter) return;

        const filterType = activeFilter.dataset.type;
        const searchText = document.getElementById('logSearch') ? document.getElementById('logSearch').value.toLowerCase() : '';

        const logLines = logContent.querySelectorAll('.log-line');

        logLines.forEach(line => {
            const type = line.dataset.type;
            const content = line.textContent.toLowerCase();

            let visible = true;

            // 类型过滤
            if (filterType !== 'all' && type !== filterType) {
                visible = false;
            }

            // 搜索过滤
            if (searchText && !content.includes(searchText)) {
                visible = false;
            }

            line.style.display = visible ? 'block' : 'none';
        });
    }

    // 初始化运行日志
    window.initLogs = function() {
        // 添加初始日志
        addLogEntry('info', '控制台页面已加载');

        // 拦截控制台日志
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error,
            info: console.info
        };

        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addLogEntry('info', args.join(' '));
        };

        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addLogEntry('warn', args.join(' '));
        };

        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addLogEntry('error', args.join(' '));
        };

        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addLogEntry('info', args.join(' '));
        };
    }

    // 添加刷新按钮
    $(document).ready(function() {
        // 添加刷新按钮到页面
        $('body').append(`
            <div style="position: fixed; bottom: 20px; right: 20px; z-index: 9999;">
                <button class="btn btn-warning" onclick="forceRefresh()">
                    <i class="fas fa-sync-alt"></i> 强制刷新
                </button>
            </div>
        `);

        // 直接修改DOM中的文本，确保显示正确
        $('.console-sidebar h5').each(function() {
            if ($(this).text() === '分析维度') {
                $(this).text('整本书的分析维度');
            }
        });

        // 初始化运行日志
        // 确保initLogs函数已定义
        if (typeof window.initLogs !== 'function') {
            console.error('window.initLogs is not defined, using fallback initialization');
            // 添加初始日志
            addLogEntry('info', '控制台页面已加载');
        } else {
            window.initLogs();
        }

        // 初始化预设内容
        window.loadPresets();

        // 添加日志
        addLogEntry('info', '控制台初始化完成');

        // 监听标签页切换事件
        $('#consoleTab button').on('shown.bs.tab', function (e) {
            const targetId = $(e.target).attr('id');
            if (targetId === 'logs-tab') {
                addLogEntry('info', '切换到运行日志标签页');
            } else if (targetId === 'preset-tab') {
                addLogEntry('info', '切换到预设内容标签页');
                // 重新加载预设列表
                window.loadPresets();
            } else if (targetId === 'writing-tab') {
                addLogEntry('info', '切换到自动写作标签页');
            } else if (targetId === 'analysis-tab') {
                addLogEntry('info', '切换到分析结果标签页');
            }
        });

        // 全局变量，用于存储选中的参考蓝本、维度和章节
        let selectedTemplateId = null;
        let selectedDimension = null;
        let selectedChapter = null;
        let selectedTemplateName = null;

        // 获取URL参数中的template_id
        const urlParams = new URLSearchParams(window.location.search);
        const templateIdFromUrl = urlParams.get('template_id');
        if (templateIdFromUrl) {
            selectedTemplateId = templateIdFromUrl;
            $(`.template-card[data-template-id="${selectedTemplateId}"]`).addClass('active');
            window.loadChapters(selectedTemplateId);

            // 获取蓝本名称
            const templateCard = $(`.template-card[data-template-id="${selectedTemplateId}"]`);
            if (templateCard.length > 0) {
                selectedTemplateName = templateCard.find('.card-title').text();
                addLogEntry('info', `已选择参考蓝本: ${selectedTemplateName}`);
            }
        }

        // 选择参考蓝本 - 使用事件委托以支持动态添加的元素
        $(document).on('click', '.template-card', function() {
            console.log('参考蓝本被点击:', $(this).data('template-id'));
            $('.template-card').removeClass('active');
            $(this).addClass('active');
            selectedTemplateId = $(this).data('template-id');
            selectedTemplateName = $(this).find('.card-title').text();

            // 显示选择提示
            const $alertHtml = $(`
                <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>已选择参考蓝本:</strong> ${selectedTemplateName}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `);

            // 移除之前的提示
            $('.template-selected-alert').remove();

            // 添加新提示
            $alertHtml.addClass('template-selected-alert');
            $(this).closest('.card-body').append($alertHtml);

            // 添加日志
            addLogEntry('info', `已选择参考蓝本: ${selectedTemplateName}`);

            // 自动切换到分析结果标签页
            setTimeout(function() {
                $('#analysis-tab').tab('show');
            }, 1000);

            // 加载章节和维度
            window.loadChapters(selectedTemplateId);
        });

        // 选择分析维度
        $(document).on('click', '.dimension-item', function() {
            $('.dimension-item').removeClass('active');
            $(this).addClass('active');
            selectedDimension = $(this).data('dimension');
            const dimensionName = $(this).text().trim();

            addLogEntry('info', `已选择分析维度: ${dimensionName}`);

            selectedChapter = null;
            $('.chapter-item').removeClass('active');

            if (selectedTemplateId) {
                window.loadAnalysisContent(selectedTemplateId, selectedDimension);
            }
        });

        // 选择章节
        $(document).on('click', '.chapter-item', function() {
            $('.chapter-item').removeClass('active');
            $(this).addClass('active');
            selectedChapter = $(this).data('chapter-id');
            const chapterTitle = $(this).text().trim();

            addLogEntry('info', `已选择章节: ${chapterTitle}`);

            if (selectedTemplateId && selectedDimension && selectedChapter) {
                window.loadChapterAnalysisContent(selectedTemplateId, selectedChapter, selectedDimension);
            }
        });

        // 开始写作
        $('#startWritingBtn').click(function() {
            if (!selectedTemplateId) {
                alert('请先选择一个参考蓝本');
                return;
            }

            const prompt = $('#writingPrompt').val().trim();
            if (!prompt) {
                alert('请输入写作提示');
                return;
            }

            $('#writingOutput').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在生成内容，请稍候...</p></div>');

            // 调用自动写作API
            $.ajax({
                url: '/api/auto_write',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    template_id: selectedTemplateId,
                    prompt: prompt
                }),
                success: function(response) {
                    if (response.success) {
                        $('#writingOutput').html(response.content.content);
                    } else {
                        $('#writingOutput').html(`<div class="alert alert-danger">${response.error}</div>`);
                    }
                },
                error: function(xhr) {
                    $('#writingOutput').html('<div class="alert alert-danger">生成内容时出错，请重试</div>');
                }
            });
        });

        // 清空输出
        $('#clearOutputBtn').click(function() {
            $('#writingOutput').html('自动写作功能将基于选定的参考蓝本生成内容。\n\n请先选择一个参考蓝本，然后在上方输入写作提示，点击"开始写作"按钮。\n\n系统将分析参考蓝本的风格、结构、人物等特点，生成符合要求的内容。\n\n生成的内容将显示在这里。');
        });

        // ===== 预设内容功能 =====

        // 新建预设
        $('#newPresetBtn').click(function() {
            // 清空表单
            $('#presetId').val('');
            $('#presetTitle').val('');
            $('#presetContent').val('');
            $('#presetCategory').val('writing_prompt');
            $('#presetEditorTitle').text('新建预设');
            currentPresetId = null;

            // 取消选中状态
            $('.preset-item').removeClass('active');
        });

        // 保存预设
        $('#savePresetBtn').click(function() {
            const title = $('#presetTitle').val().trim();
            const content = $('#presetContent').val().trim();
            const category = $('#presetCategory').val();
            const presetId = $('#presetId').val();

            if (!title) {
                alert('请输入预设标题');
                return;
            }

            if (!content) {
                alert('请输入预设内容');
                return;
            }

            // 显示保存中状态
            const originalText = $(this).html();
            $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');
            $(this).prop('disabled', true);

            // 调用API保存预设
            $.ajax({
                url: '/api/presets',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    id: presetId || null,
                    title: title,
                    content: content,
                    category: category
                }),
                success: function(response) {
                    if (response.success) {
                        // 添加日志
                        addLogEntry('info', `预设内容 "${title}" 已保存`);

                        // 重新加载预设列表
                        window.loadPresets();

                        // 更新当前预设ID
                        $('#presetId').val(response.preset.id);
                        currentPresetId = response.preset.id;
                        $('#presetEditorTitle').text('编辑预设');
                    } else {
                        alert('保存失败: ' + response.error);
                        addLogEntry('error', `预设内容保存失败: ${response.error}`);
                    }
                },
                error: function(xhr) {
                    alert('保存失败，请重试');
                    addLogEntry('error', `预设内容保存失败: ${xhr.status} ${xhr.statusText}`);
                },
                complete: function() {
                    // 恢复按钮状态
                    $('#savePresetBtn').html(originalText);
                    $('#savePresetBtn').prop('disabled', false);
                }
            });
        });

        // 删除预设
        $('#deletePresetBtn').click(function() {
            const presetId = $('#presetId').val();
            const title = $('#presetTitle').val();

            if (!presetId) {
                alert('请先选择要删除的预设');
                return;
            }

            if (!confirm(`确定要删除预设 "${title}" 吗？此操作不可恢复。`)) {
                return;
            }

            // 显示删除中状态
            const originalText = $(this).html();
            $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>删除中...');
            $(this).prop('disabled', true);

            // 调用API删除预设
            $.ajax({
                url: `/api/presets/${presetId}`,
                type: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        // 添加日志
                        addLogEntry('info', `预设内容 "${title}" 已删除`);

                        // 清空表单
                        $('#presetId').val('');
                        $('#presetTitle').val('');
                        $('#presetContent').val('');
                        $('#presetCategory').val('writing_prompt');
                        $('#presetEditorTitle').text('新建预设');
                        currentPresetId = null;

                        // 重新加载预设列表
                        window.loadPresets();
                    } else {
                        alert('删除失败: ' + response.error);
                        addLogEntry('error', `预设内容删除失败: ${response.error}`);
                    }
                },
                error: function(xhr) {
                    alert('删除失败，请重试');
                    addLogEntry('error', `预设内容删除失败: ${xhr.status} ${xhr.statusText}`);
                },
                complete: function() {
                    // 恢复按钮状态
                    $('#deletePresetBtn').html(originalText);
                    $('#deletePresetBtn').prop('disabled', false);
                }
            });
        });

        // 展开查看按钮点击事件
        $('#expandPresetBtn').click(function() {
            const presetId = $('#presetId').val();
            const category = $('#presetCategory').val();

            if (!presetId) {
                alert('请先选择要查看的预设内容');
                return;
            }

            // 根据预设类型设置不同的链接
            const viewUrl = category === 'knowledge_base'
                ? `/v3/preset/${presetId}/templates` // 知识库类型直接链接到预设模板页面
                : `/v3/preset/${presetId}`; // 其他类型链接到预设详情页面

            // 打开新窗口查看预设内容
            window.open(viewUrl, '_blank');
        });

        // 读取预设内容
        $('#readPresetBtn').click(function() {
            const presetId = $('#presetId').val();
            const title = $('#presetTitle').val();
            const category = $('#presetCategory').val();
            const content = $('#presetContent').val();

            if (!presetId) {
                alert('请先选择要读取的预设内容');
                return;
            }

            // 显示读取中状态
            const originalText = $(this).html();
            $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>读取中...');
            $(this).prop('disabled', true);

            // 切换到运行日志标签页
            $('#logs-tab').tab('show');

            // 添加日志
            addLogEntry('info', `开始读取预设内容: "${title}"`);

            // 如果是知识库类型，执行特殊处理
            if (category === 'knowledge_base') {
                readKnowledgeBase(content, title);
            } else {
                // 普通预设内容读取
                addLogEntry('info', `预设内容 "${title}" 已读取`);

                // 如果有选中的参考蓝本，可以将预设内容应用到写作提示
                if (selectedTemplateId) {
                    $('#writingPrompt').val(content);
                    addLogEntry('info', `已将预设内容应用到写作提示`);

                    // 切换到自动写作标签页
                    setTimeout(function() {
                        $('#writing-tab').tab('show');
                    }, 1000);
                }
            }

            // 恢复按钮状态
            setTimeout(function() {
                $('#readPresetBtn').html(originalText);
                $('#readPresetBtn').prop('disabled', false);
            }, 500);
        });

        // 加载单个预设
        window.loadPreset = function(presetId) {
            // 更新选中状态
            $('.preset-item').removeClass('active');
            $(`.preset-item[data-preset-id="${presetId}"]`).addClass('active');

            // 查找预设
            const preset = presets.find(p => p.id == presetId);
            if (preset) {
                // 填充表单
                $('#presetId').val(preset.id);
                $('#presetTitle').val(preset.title);
                $('#presetContent').val(preset.content);
                $('#presetCategory').val(preset.category);
                $('#presetEditorTitle').text('编辑预设');
                currentPresetId = preset.id;
            } else {
                // 调用API获取预设详情
                $.ajax({
                    url: `/api/presets/${presetId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.success && response.preset) {
                            // 填充表单
                            $('#presetId').val(response.preset.id);
                            $('#presetTitle').val(response.preset.title);
                            $('#presetContent').val(response.preset.content);
                            $('#presetCategory').val(response.preset.category);
                            $('#presetEditorTitle').text('编辑预设');
                            currentPresetId = response.preset.id;
                        } else {
                            alert('加载预设失败: ' + response.error);
                        }
                    },
                    error: function(xhr) {
                        alert('加载预设失败，请重试');
                    }
                });
            }
        }

        // 搜索预设
        $('#presetSearch').on('input', function() {
            const searchText = $(this).val().toLowerCase();

            $('.preset-item').each(function() {
                const title = $(this).find('h6').text().toLowerCase();
                const category = $(this).find('small').text().toLowerCase();

                if (title.includes(searchText) || category.includes(searchText)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // 读取知识库内容
        function readKnowledgeBase(content, title) {
            addLogEntry('info', `开始读取知识库: "${title}"`);

            // 检查是否选择了参考蓝本
            if (!selectedTemplateId) {
                addLogEntry('error', '未选择参考蓝本，无法读取知识库');
                alert('请先选择一个参考蓝本，然后再读取知识库');
                return;
            }

            try {
                // 显示读取状态
                $('#analysisContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在读取参考蓝本数据，请稍候...</p></div>');

                // 切换到分析结果标签页
                $('#analysis-tab').tab('show');

                // 添加日志
                addLogEntry('info', `正在读取参考蓝本ID: ${selectedTemplateId}, 名称: ${selectedTemplateName || '未知'}`);

                // 使用分析理解生成器
                addLogEntry('info', '正在使用分析理解生成器读取蓝本分析结果...');

                // 引入分析理解生成器脚本
                if (typeof generateUnderstandingFromTemplate === 'function') {
                    // 调用生成函数
                    generateUnderstandingFromTemplate(selectedTemplateId);
                    addLogEntry('info', '已启动分析理解生成器，正在读取分析结果...');

                    // 显示提示信息
                    $('#analysisContent').html(`
                        <div class="alert alert-info">
                            <h4><i class="fas fa-info-circle me-2"></i>正在读取参考蓝本</h4>
                            <p>系统正在读取参考蓝本 <strong>${selectedTemplateName || selectedTemplateId}</strong> 的分析结果和推理过程。</p>
                            <p>这个过程可能需要一些时间，请查看运行日志了解进度。</p>
                            <p>读取完成后，系统将自动生成知识库内容并保存。</p>
                        </div>
                    `);
                } else {
                    // 如果脚本未加载，使用传统方式读取
                    addLogEntry('warn', '分析理解生成器未加载，使用传统方式读取');

                    // 显示错误信息
                    $('#analysisContent').html(`
                        <div class="alert alert-warning">
                            <h4><i class="fas fa-exclamation-triangle me-2"></i>无法加载分析理解生成器</h4>
                            <p>系统将尝试使用传统方式读取参考蓝本数据。</p>
                        </div>
                    `);

                    // 读取蓝本原文
                    addLogEntry('info', '正在读取蓝本原文...');

                    $.ajax({
                        url: `/api/novel/${selectedTemplateId}`,
                        type: 'GET',
                        success: function(response) {
                            if (response.success) {
                                addLogEntry('info', `蓝本原文读取成功: ${response.novel.title}`);

                                // 读取整本书的分析维度
                                readTemplateBookDimensions(selectedTemplateId);
                            } else {
                                addLogEntry('error', `蓝本原文读取失败: ${response.error || '未知错误'}`);

                                // 显示错误信息
                                $('#analysisContent').html(`
                                    <div class="alert alert-danger">
                                        <h4><i class="fas fa-times-circle me-2"></i>读取失败</h4>
                                        <p>无法读取参考蓝本原文: ${response.error || '未知错误'}</p>
                                        <p>请检查参考蓝本是否存在，或者刷新页面重试。</p>
                                    </div>
                                `);
                            }
                        },
                        error: function(xhr) {
                            addLogEntry('error', `蓝本原文读取失败: ${xhr.status} ${xhr.statusText}`);

                            // 显示错误信息
                            $('#analysisContent').html(`
                                <div class="alert alert-danger">
                                    <h4><i class="fas fa-times-circle me-2"></i>读取失败</h4>
                                    <p>无法读取参考蓝本原文: ${xhr.status} ${xhr.statusText}</p>
                                    <p>请检查网络连接，或者刷新页面重试。</p>
                                </div>
                            `);
                        }
                    });
                }
            } catch (e) {
                addLogEntry('error', `读取知识库时出错: ${e.message}`);

                // 显示错误信息
                $('#analysisContent').html(`
                    <div class="alert alert-danger">
                        <h4><i class="fas fa-times-circle me-2"></i>读取失败</h4>
                        <p>读取知识库时出错: ${e.message}</p>
                        <p>请刷新页面重试。</p>
                    </div>
                `);
            }
        }

        // 读取蓝本整本书的分析维度
        function readTemplateBookDimensions(templateId) {
            addLogEntry('info', '正在读取蓝本整本书的分析维度...');

            // 获取所有维度
            const dimensions = [
                { key: 'language_style', name: '语言风格' },
                { key: 'rhythm_pacing', name: '节奏节拍' },
                { key: 'structure', name: '结构分析' },
                { key: 'sentence_variation', name: '句式变化' },
                { key: 'paragraph_length', name: '段落长度' },
                { key: 'perspective_shifts', name: '视角转换' },
                { key: 'paragraph_flow', name: '段落流畅度' },
                { key: 'novel_characteristics', name: '小说特点' },
                { key: 'world_building', name: '世界构建' },
                { key: 'character_relationships', name: '人物关系' },
                { key: 'opening_effectiveness', name: '开篇效果' },
                { key: 'climax_pacing', name: '高潮节奏' },
                { key: 'theme_exploration', name: '主题探索' },
                { key: 'outline_analysis', name: '大纲分析' },
                { key: 'chapter_outline', name: '章纲分析' }
            ];

            // 读取每个维度的分析结果和推理过程
            let completedDimensions = 0;

            dimensions.forEach(dimension => {
                addLogEntry('info', `正在读取蓝本整本书的分析维度: ${dimension.name}`);

                // 读取分析结果
                $.ajax({
                    url: `/api/novel/${templateId}/analysis/${dimension.key}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.success && response.result) {
                            addLogEntry('info', `成功读取 ${dimension.name} 的分析结果`);

                            // 读取推理过程
                            $.ajax({
                                url: `/api/novel/${templateId}/analysis/${dimension.key}/reasoning_content`,
                                type: 'GET',
                                success: function(reasoningResponse) {
                                    if (reasoningResponse.success && reasoningResponse.reasoning_content) {
                                        addLogEntry('info', `成功读取 ${dimension.name} 的推理过程`);
                                    } else {
                                        addLogEntry('warn', `无法读取 ${dimension.name} 的推理过程: ${reasoningResponse.error || '未知错误'}`);
                                    }

                                    // 检查是否所有维度都已读取完成
                                    completedDimensions++;
                                    if (completedDimensions === dimensions.length) {
                                        // 开始读取章节列表
                                        readTemplateChapters(templateId);
                                    }
                                },
                                error: function(xhr) {
                                    addLogEntry('error', `读取 ${dimension.name} 的推理过程失败: ${xhr.status} ${xhr.statusText}`);

                                    // 即使失败也继续处理
                                    completedDimensions++;
                                    if (completedDimensions === dimensions.length) {
                                        // 开始读取章节列表
                                        readTemplateChapters(templateId);
                                    }
                                }
                            });
                        } else {
                            addLogEntry('warn', `无法读取 ${dimension.name} 的分析结果: ${response.error || '未知错误'}`);

                            // 即使失败也继续处理
                            completedDimensions++;
                            if (completedDimensions === dimensions.length) {
                                // 开始读取章节列表
                                readTemplateChapters(templateId);
                            }
                        }
                    },
                    error: function(xhr) {
                        addLogEntry('error', `读取 ${dimension.name} 的分析结果失败: ${xhr.status} ${xhr.statusText}`);

                        // 即使失败也继续处理
                        completedDimensions++;
                        if (completedDimensions === dimensions.length) {
                            // 开始读取章节列表
                            readTemplateChapters(templateId);
                        }
                    }
                });
            });
        }

        // 读取蓝本章节列表
        function readTemplateChapters(templateId) {
            addLogEntry('info', '正在读取蓝本章节列表...');

            $.ajax({
                url: `/api/novel/${templateId}/chapters`,
                type: 'GET',
                success: function(response) {
                    if (response.success && response.chapters && response.chapters.length > 0) {
                        const chapters = response.chapters;
                        addLogEntry('info', `成功读取 ${chapters.length} 个章节`);

                        // 读取每个章节的分析维度
                        readTemplateChapterDimensions(templateId, chapters);
                    } else {
                        addLogEntry('warn', `无法读取章节列表: ${response.error || '未知错误'}`);

                        // 完成所有读取
                        addLogEntry('info', '蓝本读取完成');
                    }
                },
                error: function(xhr) {
                    addLogEntry('error', `读取章节列表失败: ${xhr.status} ${xhr.statusText}`);

                    // 完成所有读取
                    addLogEntry('info', '蓝本读取完成');
                }
            });
        }

        // 读取蓝本章节的分析维度
        function readTemplateChapterDimensions(templateId, chapters) {
            addLogEntry('info', '正在读取蓝本章节的分析维度...');

            // 获取所有维度
            const dimensions = [
                { key: 'language_style', name: '语言风格' },
                { key: 'rhythm_pacing', name: '节奏节拍' },
                { key: 'structure', name: '结构分析' },
                { key: 'sentence_variation', name: '句式变化' },
                { key: 'paragraph_length', name: '段落长度' },
                { key: 'perspective_shifts', name: '视角转换' },
                { key: 'paragraph_flow', name: '段落流畅度' },
                { key: 'novel_characteristics', name: '小说特点' },
                { key: 'world_building', name: '世界构建' },
                { key: 'character_relationships', name: '人物关系' },
                { key: 'opening_effectiveness', name: '开篇效果' },
                { key: 'climax_pacing', name: '高潮节奏' },
                { key: 'theme_exploration', name: '主题探索' },
                { key: 'outline_analysis', name: '大纲分析' },
                { key: 'chapter_outline', name: '章纲分析' }
            ];

            // 为了避免发送太多请求，只读取第一章的分析维度
            const firstChapter = chapters[0];
            addLogEntry('info', `正在读取第一章 "${firstChapter.title}" 的分析维度...`);

            let completedDimensions = 0;

            dimensions.forEach(dimension => {
                addLogEntry('info', `正在读取章节 "${firstChapter.title}" 的 ${dimension.name}`);

                // 读取分析结果
                $.ajax({
                    url: `/api/novel/${templateId}/chapter/${firstChapter.id}/analysis/${dimension.key}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.success && response.result) {
                            addLogEntry('info', `成功读取章节 "${firstChapter.title}" 的 ${dimension.name} 分析结果`);

                            // 读取推理过程
                            $.ajax({
                                url: `/api/novel/${templateId}/chapter/${firstChapter.id}/analysis/${dimension.key}/reasoning_content`,
                                type: 'GET',
                                success: function(reasoningResponse) {
                                    if (reasoningResponse.success && reasoningResponse.reasoning_content) {
                                        addLogEntry('info', `成功读取章节 "${firstChapter.title}" 的 ${dimension.name} 推理过程`);
                                    } else {
                                        addLogEntry('warn', `无法读取章节 "${firstChapter.title}" 的 ${dimension.name} 推理过程: ${reasoningResponse.error || '未知错误'}`);
                                    }

                                    // 检查是否所有维度都已读取完成
                                    completedDimensions++;
                                    if (completedDimensions === dimensions.length) {
                                        // 完成所有读取
                                        addLogEntry('info', '蓝本读取完成');

                                        // 切换到分析结果标签页
                                        setTimeout(function() {
                                            $('#analysis-tab').tab('show');
                                        }, 1000);
                                    }
                                },
                                error: function(xhr) {
                                    addLogEntry('error', `读取章节 "${firstChapter.title}" 的 ${dimension.name} 推理过程失败: ${xhr.status} ${xhr.statusText}`);

                                    // 即使失败也继续处理
                                    completedDimensions++;
                                    if (completedDimensions === dimensions.length) {
                                        // 完成所有读取
                                        addLogEntry('info', '蓝本读取完成');

                                        // 切换到分析结果标签页
                                        setTimeout(function() {
                                            $('#analysis-tab').tab('show');
                                        }, 1000);
                                    }
                                }
                            });
                        } else {
                            addLogEntry('warn', `无法读取章节 "${firstChapter.title}" 的 ${dimension.name} 分析结果: ${response.error || '未知错误'}`);

                            // 即使失败也继续处理
                            completedDimensions++;
                            if (completedDimensions === dimensions.length) {
                                // 完成所有读取
                                addLogEntry('info', '蓝本读取完成');

                                // 切换到分析结果标签页
                                setTimeout(function() {
                                    $('#analysis-tab').tab('show');
                                }, 1000);
                            }
                        }
                    },
                    error: function(xhr) {
                        addLogEntry('error', `读取章节 "${firstChapter.title}" 的 ${dimension.name} 分析结果失败: ${xhr.status} ${xhr.statusText}`);

                        // 即使失败也继续处理
                        completedDimensions++;
                        if (completedDimensions === dimensions.length) {
                            // 完成所有读取
                            addLogEntry('info', '蓝本读取完成');

                            // 切换到分析结果标签页
                            setTimeout(function() {
                                $('#analysis-tab').tab('show');
                            }, 1000);
                        }
                    }
                });
            });
        }

        // ===== 运行日志功能 =====

        // 清空日志
        $('#clearLogsBtn').click(function() {
            if (confirm('确定要清空所有日志吗？')) {
                $('#logContent').html('');
                addLogEntry('info', '日志已清空');
            }
        });

        // 导出日志
        $('#exportLogsBtn').click(function() {
            const logContent = document.getElementById('logContent');
            if (!logContent) return;

            const logLines = Array.from(logContent.querySelectorAll('.log-line'))
                .map(line => line.textContent)
                .join('\n');

            const blob = new Blob([logLines], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);

            const now = new Date();
            const dateStr = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);

            const a = document.createElement('a');
            a.href = url;
            a.download = `九猫系统日志-${dateStr}.txt`;
            a.click();

            URL.revokeObjectURL(url);

            addLogEntry('info', '日志已导出');
        });

        // 日志过滤器点击事件
        $('.log-filter').click(function() {
            $('.log-filter').removeClass('active');
            $(this).addClass('active');
            applyLogFilters();
        });

        // 日志搜索
        $('#logSearch').on('input', function() {
            applyLogFilters();
        });

        // 清除搜索
        $('#clearSearchBtn').click(function() {
            $('#logSearch').val('');
            applyLogFilters();
        });

        // 初始化运行日志函数已移至全局作用域

        // 加载章节列表和分析维度
        window.loadChapters = function(templateId) {
            $('#chapterList').html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><p class="small mt-2">加载中...</p></div>');
            $('#dimensionList').html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><p class="small mt-2">加载中...</p></div>');

            // 调用API获取小说信息、章节列表和分析维度
            $.ajax({
                url: `/api/novel/${templateId}`,
                type: 'GET',
                success: function(response) {
                    // 移除API响应日志

                    // 不再显示调试信息

                    if (response.success) {
                        // 加载分析维度
                        if (response.dimensions && response.dimensions.length > 0) {
                            let dimensionsHtml = '';

                            // 使用API返回的维度列表
                            response.dimensions.forEach(dimension => {
                                // 只显示已分析的维度
                                if (dimension.is_analyzed) {
                                    dimensionsHtml += `<div class="dimension-item" data-dimension="${dimension.key}">
                                        <i class="fas fa-${dimension.icon} me-2"></i>${dimension.name}
                                    </div>`;
                                }
                            });

                            if (dimensionsHtml) {
                                $('#dimensionList').html(dimensionsHtml);
                            } else {
                                $('#dimensionList').html('<div class="alert alert-warning">该小说没有已分析的维度</div>');
                            }
                        } else if (response.novel && response.novel.available_dimensions) {
                            // 兼容旧版API响应格式
                            let dimensionsHtml = '';
                            // 定义维度图标映射
                            const dimensionIcons = {
                                'outline_analysis': 'project-diagram',
                                'chapter_outline': 'list-ol',
                                'language_style': 'language',
                                'rhythm_pacing': 'drum',
                                'structure': 'sitemap',
                                'sentence_variation': 'text-width',
                                'paragraph_length': 'paragraph',
                                'perspective_shifts': 'exchange-alt',
                                'paragraph_flow': 'stream',
                                'novel_characteristics': 'fingerprint',
                                'world_building': 'globe',
                                'character_relationships': 'users',
                                'opening_effectiveness': 'door-open',
                                'climax_pacing': 'mountain',
                                'theme_exploration': 'lightbulb'
                            };

                            // 定义维度名称映射
                            const dimensionNames = {
                                'outline_analysis': '大纲分析',
                                'chapter_outline': '章纲分析',
                                'language_style': '语言风格',
                                'rhythm_pacing': '节奏节拍',
                                'structure': '结构分析',
                                'sentence_variation': '句式变化',
                                'paragraph_length': '段落长度',
                                'perspective_shifts': '视角转换',
                                'paragraph_flow': '段落流畅度',
                                'novel_characteristics': '小说特征',
                                'world_building': '世界构建',
                                'character_relationships': '人物关系',
                                'opening_effectiveness': '开篇效果',
                                'climax_pacing': '高潮节奏',
                                'theme_exploration': '主题探索'
                            };

                            response.novel.available_dimensions.forEach(dimension => {
                                const icon = dimensionIcons[dimension] || 'star';
                                const name = dimensionNames[dimension] || dimension;
                                dimensionsHtml += `<div class="dimension-item" data-dimension="${dimension}">
                                    <i class="fas fa-${icon} me-2"></i>${name}
                                </div>`;
                            });

                            if (dimensionsHtml) {
                                $('#dimensionList').html(dimensionsHtml);
                            } else {
                                $('#dimensionList').html('<div class="alert alert-warning">该小说没有可用的分析维度</div>');
                            }
                        } else {
                            $('#dimensionList').html('<div class="alert alert-warning">无法加载分析维度</div>');
                        }

                        // 加载章节列表
                        if (response.chapters && response.chapters.length > 0) {
                            let chaptersHtml = '';
                            response.chapters.forEach(chapter => {
                                chaptersHtml += `<div class="chapter-item" data-chapter-id="${chapter.id}">
                                    <i class="fas fa-book me-2"></i>${chapter.title}
                                </div>`;
                            });
                            $('#chapterList').html(chaptersHtml);
                        } else {
                            $('#chapterList').html('<div class="alert alert-warning">该小说没有章节</div>');
                        }
                    } else {
                        $('#dimensionList').html('<div class="alert alert-warning">无法加载分析维度</div>');
                        $('#chapterList').html('<div class="alert alert-warning">无法加载章节列表</div>');
                    }
                },
                error: function(xhr) {
                    // 移除API错误日志
                    $('#dimensionList').html('<div class="alert alert-danger">加载分析维度时出错</div>');
                    $('#chapterList').html('<div class="alert alert-danger">加载章节列表时出错</div>');

                    // 不再显示错误信息到调试区域
                    console.error(`API错误: ${xhr.status} ${xhr.statusText}`, xhr.responseText || '无响应内容');
                }
            });
        }

        // 加载分析内容
        window.loadAnalysisContent = function(templateId, dimension) {
            $('#analysisContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">加载中...</p></div>');
            $('#reasoningContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">加载中...</p></div>');

            // 添加日志
            addLogEntry('info', `正在加载整本书的分析维度: ${dimension}`);

            // 调用API获取分析内容
            $.ajax({
                url: `/api/novel/${templateId}/analysis/${dimension}`,
                type: 'GET',
                success: function(response) {
                    if (response.success && response.result) {
                        // 渲染Markdown内容
                        const content = marked.parse(response.result.content);
                        $('#analysisContent').html(content);
                        addLogEntry('info', `成功加载整本书的分析结果: ${dimension}`);
                    } else {
                        $('#analysisContent').html(`<div class="alert alert-warning">${response.error || '无法加载分析内容'}</div>`);
                        addLogEntry('warn', `无法加载整本书的分析结果: ${response.error || '未知错误'}`);
                    }
                },
                error: function(xhr) {
                    $('#analysisContent').html('<div class="alert alert-danger">加载分析内容时出错</div>');
                    addLogEntry('error', `加载整本书的分析结果失败: ${xhr.status} ${xhr.statusText}`);
                }
            });

            // 调用API获取推理过程
            $.ajax({
                url: `/api/novel/${templateId}/analysis/${dimension}/reasoning_content`,
                type: 'GET',
                success: function(response) {
                    if (response.success && response.reasoning_content) {
                        // 渲染Markdown内容
                        const content = marked.parse(response.reasoning_content);
                        $('#reasoningContent').html(content);
                        addLogEntry('info', `成功加载整本书的推理过程: ${dimension}`);
                    } else {
                        $('#reasoningContent').html(`<div class="alert alert-warning">${response.error || '无法加载推理过程'}</div>`);
                        addLogEntry('warn', `无法加载整本书的推理过程: ${response.error || '未知错误'}`);
                    }
                },
                error: function(xhr) {
                    $('#reasoningContent').html('<div class="alert alert-danger">加载推理过程时出错</div>');
                    addLogEntry('error', `加载整本书的推理过程失败: ${xhr.status} ${xhr.statusText}`);
                }
            });
        }

        // 加载章节分析内容
        window.loadChapterAnalysisContent = function(templateId, chapterId, dimension) {
            $('#analysisContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">加载中...</p></div>');
            $('#reasoningContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">加载中...</p></div>');

            // 获取章节标题
            const chapterTitle = $(`.chapter-item[data-chapter-id="${chapterId}"]`).text().trim();

            // 添加日志
            addLogEntry('info', `正在加载章节 "${chapterTitle}" 的分析维度: ${dimension}`);

            // 调用API获取章节分析内容
            $.ajax({
                url: `/api/novel/${templateId}/chapter/${chapterId}/analysis/${dimension}`,
                type: 'GET',
                success: function(response) {
                    if (response.success && response.result) {
                        // 渲染Markdown内容
                        const content = marked.parse(response.result.content);
                        $('#analysisContent').html(content);
                        addLogEntry('info', `成功加载章节 "${chapterTitle}" 的分析结果: ${dimension}`);
                    } else {
                        $('#analysisContent').html(`<div class="alert alert-warning">${response.error || '无法加载章节分析内容'}</div>`);
                        addLogEntry('warn', `无法加载章节 "${chapterTitle}" 的分析结果: ${response.error || '未知错误'}`);
                    }
                },
                error: function(xhr) {
                    $('#analysisContent').html('<div class="alert alert-danger">加载章节分析内容时出错</div>');
                    addLogEntry('error', `加载章节 "${chapterTitle}" 的分析结果失败: ${xhr.status} ${xhr.statusText}`);
                }
            });

            // 调用API获取章节推理过程
            $.ajax({
                url: `/api/novel/${templateId}/chapter/${chapterId}/analysis/${dimension}/reasoning_content`,
                type: 'GET',
                success: function(response) {
                    if (response.success && response.reasoning_content) {
                        // 渲染Markdown内容
                        const content = marked.parse(response.reasoning_content);
                        $('#reasoningContent').html(content);
                        addLogEntry('info', `成功加载章节 "${chapterTitle}" 的推理过程: ${dimension}`);
                    } else {
                        $('#reasoningContent').html(`<div class="alert alert-warning">${response.error || '无法加载推理过程'}</div>`);
                        addLogEntry('warn', `无法加载章节 "${chapterTitle}" 的推理过程: ${response.error || '未知错误'}`);
                    }
                },
                error: function(xhr) {
                    $('#reasoningContent').html('<div class="alert alert-danger">加载推理过程时出错</div>');
                    addLogEntry('error', `加载章节 "${chapterTitle}" 的推理过程失败: ${xhr.status} ${xhr.statusText}`);
                }
            });
        }

        // 加载预设内容列表
        window.loadPresets = function() {
            // 添加日志
            addLogEntry('info', '正在加载预设内容列表...');

            // 显示加载中
            $('#presetList').html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><p class="small mt-2">加载中...</p></div>');

            // 调用API获取预设内容列表
            $.ajax({
                url: '/api/presets',
                type: 'GET',
                success: function(response) {
                    if (response.success && response.presets && response.presets.length > 0) {
                        // 保存预设列表到全局变量
                        window.presets = response.presets;

                        // 构建预设列表HTML
                        let presetsHtml = '';
                        response.presets.forEach(preset => {
                            // 获取分类名称
                            let categoryName = '其他';
                            switch(preset.category) {
                                case 'writing_prompt': categoryName = '写作提示'; break;
                                case 'character': categoryName = '人物设定'; break;
                                case 'plot': categoryName = '情节构思'; break;
                                case 'scene': categoryName = '场景描写'; break;
                                case 'knowledge_base': categoryName = '知识库'; break;
                                case 'chapter_template': categoryName = '章节预设模板'; break;
                            }

                            presetsHtml += `
                                <a href="javascript:void(0)" class="list-group-item list-group-item-action preset-item" data-preset-id="${preset.id}">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${preset.title}</h6>
                                        <small>${categoryName}</small>
                                    </div>
                                    <small class="text-muted">${new Date(preset.created_at).toLocaleString()}</small>
                                </a>
                            `;
                        });

                        $('#presetList').html(presetsHtml);

                        // 绑定预设点击事件
                        $('.preset-item').click(function() {
                            const presetId = $(this).data('preset-id');
                            window.loadPreset(presetId);
                        });

                        addLogEntry('info', `成功加载 ${response.presets.length} 个预设内容`);
                    } else {
                        $('#presetList').html('<div class="text-center py-3"><small class="text-muted">暂无预设内容</small></div>');
                        addLogEntry('info', '没有找到预设内容');
                    }
                },
                error: function(xhr) {
                    $('#presetList').html('<div class="alert alert-danger">加载预设内容失败</div>');
                    addLogEntry('error', `加载预设内容失败: ${xhr.status} ${xhr.statusText}`);
                }
            });
        }
    });
</script>
{% endblock %}
