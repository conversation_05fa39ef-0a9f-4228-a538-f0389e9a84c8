/* 九猫小说分析系统 - 主样式表 */

body {
    font-family: "Microsoft YaHei", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
}

.navbar-dark .navbar-nav .active > .nav-link {
    color: #fff;
    font-weight: 500;
}

/* 卡片样式 */
.card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: bold;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header.bg-primary {
    background-color: #007bff !important;
}

.card-header.bg-success {
    background-color: #28a745 !important;
}

.card-header.bg-info {
    background-color: #17a2b8 !important;
}

.card-header.bg-dark {
    background-color: #343a40 !important;
}

.card-header.bg-secondary {
    background-color: #6c757d !important;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

.btn-outline-info {
    color: #17a2b8;
    border-color: #17a2b8;
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* 列表组样式 */
.list-group-item {
    border-left: none;
    border-right: none;
    padding: 0.75rem 1.25rem;
}

.list-group-item:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.list-group-item:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.4em 0.6em;
}

/* 文本样式 */
.text-muted {
    color: #6c757d !important;
}

.lead {
    font-size: 1.1rem;
    font-weight: 300;
}

/* 分析页面样式 */
.analysis-card {
    height: 100%;
}

.analysis-card .card-body {
    display: flex;
    flex-direction: column;
}

.analysis-card .card-title {
    margin-bottom: 1rem;
}

.analysis-card .card-text {
    flex-grow: 1;
}

.analysis-card .btn {
    margin-top: auto;
}

/* 进度条样式 */
.progress {
    height: 1.2rem;
    margin-bottom: 1rem;
    border-radius: 0.25rem;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* 分析结果页面样式 */
.result-section {
    margin-bottom: 2rem;
}

.result-section h3 {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1.5rem;
}

/* 页脚样式 */
footer {
    margin-top: 3rem;
    padding: 2rem 0;
    background-color: #343a40;
    color: #fff;
}

footer h5 {
    font-weight: 600;
    margin-bottom: 1rem;
}

footer .small {
    opacity: 0.8;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-header h4, .card-header h5 {
        font-size: 1.1rem;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 分析维度卡片样式 */
.dimension-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.dimension-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dimension-card .card-body {
    padding: 1.5rem;
}

.dimension-card .dimension-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #007bff;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
