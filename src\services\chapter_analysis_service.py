"""
Chapter analysis service for the 九猫 (Nine Cats) novel analysis system.
"""
import logging
import threading
import time
import traceback
import concurrent.futures
import re
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

from src.db.connection import Session
from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.models.chapter_analysis_process import ChapterAnalysisProcess
from src.utils.text_processor import TextProcessor
from src.api.deepseek_client import DeepSeekClient
import config
from src.config.serialization_bottleneck_optimizer import SerializationBottleneckOptimizer

logger = logging.getLogger(__name__)

# 分析任务状态
analysis_tasks = {}
analysis_logs = {}

class ChapterAnalysisService:
    """Chapter analysis service."""

    @staticmethod
    def split_novel_into_chapters(novel_id: int) -> List[Chapter]:
        """
        将小说分割为章节。

        Args:
            novel_id: 小说ID

        Returns:
            章节列表
        """
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).filter(Novel.id == novel_id).first()
            if not novel:
                logger.error(f"小说不存在: {novel_id}")
                return []

            # 检查是否已经有章节
            existing_chapters = session.query(Chapter).filter(Chapter.novel_id == novel_id).all()
            if existing_chapters:
                logger.info(f"小说 {novel_id} 已有 {len(existing_chapters)} 个章节")
                # 创建一个新的章节列表，避免返回会话绑定的对象
                chapters_copy = []
                for chapter in existing_chapters:
                    chapters_copy.append({
                        'id': chapter.id,
                        'novel_id': chapter.novel_id,
                        'chapter_number': chapter.chapter_number,
                        'title': chapter.title,
                        'content_length': len(chapter.content) if chapter.content else 0
                    })
                return chapters_copy

            # 获取小说内容
            novel_content = novel.content

            # 关闭当前会话，避免长时间持有会话
            session.close()

            # 使用TextProcessor分割章节
            chapter_texts = TextProcessor.split_into_chapters(novel_content)
            logger.info(f"小说 {novel_id} 分割为 {len(chapter_texts)} 个章节")

            # 创建新的会话用于保存章节
            new_session = Session()
            try:
                # 创建章节对象
                chapters = []
                for i, chapter_text in enumerate(chapter_texts):
                    # 尝试提取章节标题
                    title = None
                    first_line = chapter_text.strip().split('\n')[0] if chapter_text.strip() else ""
                    if first_line and len(first_line) < 100:  # 标题通常不会太长
                        title = first_line

                    # 创建章节
                    chapter = Chapter(
                        novel_id=novel_id,
                        chapter_number=i + 1,
                        content=chapter_text,
                        title=title
                    )
                    new_session.add(chapter)

                    # 添加章节信息到返回列表
                    chapters.append({
                        'chapter_number': i + 1,
                        'title': title,
                        'content_length': len(chapter_text) if chapter_text else 0
                    })

                # 提交事务
                new_session.commit()
                logger.info(f"成功创建 {len(chapters)} 个章节")
                return chapters
            except Exception as e:
                logger.error(f"保存章节时出错: {str(e)}")
                logger.error(traceback.format_exc())
                new_session.rollback()
                return []
            finally:
                new_session.close()
        except Exception as e:
            logger.error(f"分割章节时出错: {str(e)}")
            logger.error(traceback.format_exc())
            session.rollback()
            return []
        finally:
            # 确保会话被关闭
            if session.is_active:
                session.close()

    @staticmethod
    def get_previous_chapter_analysis(novel_id: int, current_chapter_id: int, dimension: str, limit: int = 3) -> list:
        """
        获取前面章节的分析结果，用于章节间信息联通

        Args:
            novel_id: 小说ID
            current_chapter_id: 当前章节ID
            dimension: 分析维度
            limit: 获取的前序章节数量

        Returns:
            前序章节的分析结果列表
        """
        logger.info(f"获取前序章节分析结果: novel_id={novel_id}, current_chapter_id={current_chapter_id}, dimension={dimension}, limit={limit}")

        # 记录当前章节的章节号，用于后续查找前序章节
        current_chapter_number = None

        with Session() as session:
            try:
                # 获取当前章节的信息
                current_chapter = session.query(Chapter).filter(Chapter.id == current_chapter_id, Chapter.novel_id == novel_id).first()
                if not current_chapter:
                    logger.error(f"获取前序章节分析结果失败：当前章节不存在 chapter_id={current_chapter_id}")
                    return []

                # 记录当前章节号，用于后续查找前序章节
                current_chapter_number = current_chapter.chapter_number
                logger.info(f"当前章节信息: id={current_chapter.id}, chapter_number={current_chapter_number}, title={current_chapter.title or '无标题'}")

                # 获取前面的章节
                previous_chapters = session.query(Chapter).filter(
                    Chapter.novel_id == novel_id,
                    Chapter.chapter_number < current_chapter.chapter_number
                ).order_by(Chapter.chapter_number.desc()).limit(limit).all()

                if not previous_chapters:
                    logger.info(f"当前章节 {current_chapter_id} 是第一章或前面没有章节")
                    return []

                # 记录找到的前序章节
                logger.info(f"找到 {len(previous_chapters)} 个前序章节:")
                for idx, prev in enumerate(previous_chapters):
                    logger.info(f"前序章节 {idx+1}: id={prev.id}, chapter_number={prev.chapter_number}, title={prev.title or '无标题'}")

                # 获取前面章节的分析结果
                previous_analyses = []
                for prev_chapter in previous_chapters:
                    logger.info(f"查询章节 {prev_chapter.id} (第{prev_chapter.chapter_number}章) 的 {dimension} 维度分析结果")
                    analysis_result = session.query(ChapterAnalysisResult).filter(
                        ChapterAnalysisResult.novel_id == novel_id,
                        ChapterAnalysisResult.chapter_id == prev_chapter.id,
                        ChapterAnalysisResult.dimension == dimension
                    ).first()

                    # 无论是否有result字段，都尝试获取分析结果
                    if analysis_result:
                        try:
                            logger.info(f"找到章节 {prev_chapter.id} 的 {dimension} 维度分析结果")

                            # 检查分析结果的属性
                            has_result = hasattr(analysis_result, 'result') and analysis_result.result
                            has_content = hasattr(analysis_result, 'content') and analysis_result.content
                            has_reasoning = hasattr(analysis_result, 'reasoning_content') and analysis_result.reasoning_content

                            logger.info(f"分析结果属性: has_result={has_result}, has_content={has_content}, has_reasoning={has_reasoning}")

                            # 尝试从result字段获取数据
                            if has_result:
                                try:
                                    result_data = json.loads(analysis_result.result) if isinstance(analysis_result.result, str) else analysis_result.result
                                    logger.info(f"成功解析result字段: 类型={type(result_data)}")
                                except Exception as json_err:
                                    # 如果result不是JSON字符串，直接使用
                                    logger.warning(f"解析result字段失败: {str(json_err)}")
                                    result_data = {
                                        "content": analysis_result.content,
                                        "reasoning_content": analysis_result.reasoning_content
                                    }
                            else:
                                # 如果没有result字段，直接使用content和reasoning_content字段
                                logger.info(f"使用content和reasoning_content字段")
                                result_data = {
                                    "content": analysis_result.content,
                                    "reasoning_content": analysis_result.reasoning_content
                                }

                            # 确保content和reasoning_content不为空
                            content = result_data.get("content", "") or analysis_result.content or ""
                            reasoning_content = result_data.get("reasoning_content", "") or analysis_result.reasoning_content or ""

                            logger.info(f"提取的内容: content长度={len(content)}, reasoning_content长度={len(reasoning_content)}")

                            # 只有当content不为空时才添加到前序分析结果中
                            if content.strip():
                                previous_analyses.append({
                                    "chapter_id": prev_chapter.id,
                                    "chapter_number": prev_chapter.chapter_number,
                                    "chapter_title": prev_chapter.title or f"第{prev_chapter.chapter_number}章",
                                    "content": content,
                                    "reasoning_content": reasoning_content
                                })
                                logger.info(f"成功添加章节 {prev_chapter.id} 的 {dimension} 维度分析结果到前序分析列表")
                            else:
                                logger.warning(f"章节 {prev_chapter.id} 的 {dimension} 维度分析结果内容为空，跳过")
                        except Exception as e:
                            logger.error(f"处理章节 {prev_chapter.id} 的分析结果时出错: {str(e)}")
                            logger.error(traceback.format_exc())
                    else:
                        logger.warning(f"章节 {prev_chapter.id} 没有 {dimension} 维度的分析结果")

                logger.info(f"成功获取 {len(previous_analyses)} 个前序章节的分析结果")
                return previous_analyses
            except Exception as e:
                logger.error(f"获取前序章节分析结果时出错: {str(e)}")
                logger.error(traceback.format_exc())
                return []

    @staticmethod
    def analyze_chapter_batch(chapter_id: int, dimensions: List[str], model: str = None,
                           use_cache: bool = True, save_process: bool = True) -> Dict[str, Any]:
        """
        批量分析单个章节的多个维度，优化版本支持缓存和进度保存。
        使用维度优先级配置，优先分析章纲分析和大纲分析。

        Args:
            chapter_id: 章节ID
            dimensions: 分析维度列表
            model: 使用的模型
            use_cache: 是否使用缓存的分析结果
            save_process: 是否保存分析过程

        Returns:
            分析结果
        """
        logger.info(f"开始批量分析章节 {chapter_id} 的维度: {dimensions}")

        # 验证输入参数
        if not dimensions:
            logger.error(f"未指定分析维度: dimensions={dimensions}")
            return {"success": False, "error": "未指定分析维度"}

        # 确保dimensions是列表类型
        if not isinstance(dimensions, list):
            try:
                # 尝试转换为列表
                if isinstance(dimensions, str):
                    dimensions = [dimensions]
                else:
                    dimensions = list(dimensions)
                logger.warning(f"dimensions参数不是列表类型，已转换为: {dimensions}")
            except Exception as e:
                logger.error(f"无法将dimensions参数转换为列表: {str(e)}")
                return {"success": False, "error": f"维度参数格式错误: {str(e)}"}

        # 使用维度优先级配置对维度进行排序
        try:
            # 导入维度优先级配置
            from dimension_priority import get_prioritized_dimensions

            # 对维度进行优先级排序
            prioritized_dimensions = get_prioritized_dimensions(dimensions)
            logger.info(f"维度优先级排序: 原始顺序={dimensions}, 排序后={prioritized_dimensions}")

            # 使用排序后的维度列表
            dimensions = prioritized_dimensions
        except Exception as e:
            logger.warning(f"使用维度优先级排序时出错: {str(e)}，将使用原始维度顺序")
            # 如果导入失败，继续使用原始维度列表

        logger.info(f"准备分析 {len(dimensions)} 个维度: {dimensions}")
        results = []
        success_count = 0

        for dimension in dimensions:
            try:
                logger.info(f"开始分析维度: {dimension}")
                # 分析单个维度
                result = ChapterAnalysisService.analyze_chapter(
                    chapter_id=chapter_id,
                    dimension=dimension,
                    model=model,
                    use_cache=use_cache,
                    save_process=save_process,
                    prompt_template="default"  # 批量分析使用默认模板
                )

                logger.info(f"维度 {dimension} 分析结果: success={result.get('success', False)}")

                if result.get("success", False):
                    success_count += 1

                results.append({
                    "dimension": dimension,
                    "success": result.get("success", False),
                    "message": result.get("message", ""),
                    "cached": result.get("cached", False)
                })
            except Exception as e:
                logger.error(f"分析章节维度 {dimension} 时出错: {str(e)}")
                logger.error(traceback.format_exc())
                results.append({
                    "dimension": dimension,
                    "success": False,
                    "message": f"分析出错: {str(e)}"
                })

        logger.info(f"批量分析完成，成功: {success_count}/{len(dimensions)}")
        return {
            "success": success_count > 0,
            "message": f"成功分析 {success_count}/{len(dimensions)} 个维度",
            "results": results
        }

    @staticmethod
    def analyze_chapter(chapter_id: int, dimension: str, model: str = None,
                       use_cache: bool = True, save_process: bool = True, prompt_template: str = "default") -> Dict[str, Any]:
        """
        分析单个章节的指定维度，优化版本支持缓存和进度保存。

        Args:
            chapter_id: 章节ID
            dimension: 分析维度
            model: 使用的模型
            use_cache: 是否使用缓存的分析结果
            save_process: 是否保存分析过程
            prompt_template: 提示词模板（default或simplified）

        Returns:
            分析结果
        """
        # 记录详细的入口日志
        logger.info(f"开始分析章节方法: chapter_id={chapter_id}, dimension={dimension}, model={model}, use_cache={use_cache}, save_process={save_process}")

        # 创建会话
        session = Session()

        # 用于存储章节和小说信息的变量
        chapter_content = None
        chapter_title = None
        chapter_number = None
        novel_id = None
        novel_title = None
        existing_result = None
        result_id = None

        try:
            try:
                # 获取章节
                chapter = session.query(Chapter).filter(Chapter.id == chapter_id).first()
                if not chapter:
                    error_msg = f"章节不存在: chapter_id={chapter_id}"
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg}

                # 保存章节信息，避免会话关闭后使用chapter对象
                chapter_content = chapter.content
                chapter_title = chapter.title
                chapter_number = chapter.chapter_number
                novel_id = chapter.novel_id

                # 获取小说
                novel = session.query(Novel).filter(Novel.id == novel_id).first()
                if not novel:
                    error_msg = f"小说不存在: novel_id={novel_id}"
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg}

                # 保存小说信息，避免会话关闭后使用novel对象
                novel_title = novel.title

                logger.info(f"成功获取章节和小说数据: chapter_id={chapter_id}, novel_id={novel_id}, 章节内容长度={len(chapter_content or '')}")

                # 检查是否已经有分析结果
                existing_result = None
                if use_cache:
                    existing_result = session.query(ChapterAnalysisResult).filter(
                        ChapterAnalysisResult.chapter_id == chapter_id,
                        ChapterAnalysisResult.dimension == dimension
                    ).first()

                # 对于chapter_outline和outline_analysis维度，强制重新分析，不使用缓存的结果
                if dimension in ["chapter_outline", "outline_analysis"]:
                    if existing_result:
                        logger.info(f"章节 {chapter_id} 的 {dimension} 维度已有分析结果，但将强制重新分析")
                        # 删除现有结果
                        session.delete(existing_result)
                        session.commit()
                        existing_result = None
                elif existing_result and not config.FORCE_REANALYSIS:
                    logger.info(f"章节 {chapter_id} 的 {dimension} 维度已有分析结果")
                    # 创建一个包含所有必要信息的结果字典，而不是直接引用数据库对象
                    result_dict = {
                        "id": existing_result.id,
                        "chapter_id": existing_result.chapter_id,
                        "novel_id": existing_result.novel_id,
                        "dimension": existing_result.dimension,
                        "content": existing_result.content,
                        "reasoning_content": existing_result.reasoning_content,
                        "metadata": existing_result.analysis_metadata,
                        "logs": existing_result.analysis_logs,
                        "created_at": existing_result.created_at.isoformat() if existing_result.created_at else None,
                        "updated_at": existing_result.updated_at.isoformat() if existing_result.updated_at else None
                    }
                    return {
                        "success": True,
                        "result": result_dict,
                        "message": "使用现有分析结果",
                        "cached": True
                    }

                # 使用DeepSeekClient进行分析 - 强制使用真实API
                model_to_use = model or config.DEFAULT_MODEL
                logger.info(f"使用模型 {model_to_use} 进行分析")
                try:
                    # 确保环境变量设置为使用真实API
                    import os
                    os.environ['USE_REAL_API'] = 'True'
                    os.environ['USE_MOCK_API'] = 'False'
                    os.environ['ENABLE_MOCK_ANALYSIS'] = 'False'

                    client = DeepSeekClient(model=model_to_use)
                    logger.info(f"成功创建DeepSeekClient实例: model={model_to_use}")
                except Exception as e:
                    logger.error(f"创建DeepSeekClient实例失败: {str(e)}")
                    return {"success": False, "error": f"创建分析客户端失败: {str(e)}"}

                # 创建分析日志
                chapter_logs = []

                # 记录分析开始
                start_time = time.time()
                logger.info(f"开始分析章节 {chapter_id} 的 {dimension} 维度，章节内容长度: {len(chapter.content)} 字符")

                # 添加分析开始日志
                chapter_logs.append({
                    "timestamp": datetime.now().isoformat(),
                    "level": "info",
                    "message": f"开始分析章节 {chapter_id} 的 {dimension} 维度",
                    "dimension": dimension,
                    "chapter_id": chapter_id
                })

                # 创建临时分析结果，用于保存进度
                if save_process and not existing_result:
                    temp_result = ChapterAnalysisResult(
                        chapter_id=chapter_id,
                        novel_id=novel.id,
                        dimension=dimension,
                        content="分析中...",
                        metadata={"status": "processing"},
                        logs=chapter_logs
                    )
                    session.add(temp_result)
                    session.commit()
                    result_id = temp_result.id
                elif existing_result:
                    result_id = existing_result.id
                else:
                    result_id = None

                # 进行分析，支持进度回调
                def progress_callback(stage, progress, message):
                    # 记录进度
                    if save_process and result_id:
                        try:
                            # 创建分析过程记录
                            process = ChapterAnalysisProcess(
                                chapter_id=chapter_id,
                                novel_id=novel.id,
                                result_id=result_id,
                                dimension=dimension,
                                processing_stage=stage,
                                output_text=message,
                                is_successful=True
                            )
                            session.add(process)
                            session.commit()

                            # 添加到日志
                            chapter_logs.append({
                                "timestamp": datetime.now().isoformat(),
                                "level": "info",
                                "message": f"分析进度: {stage} - {progress}%: {message}",
                                "dimension": dimension,
                                "chapter_id": chapter_id,
                                "stage": stage,
                                "progress": progress
                            })

                            # 更新分析结果的日志
                            result = session.query(ChapterAnalysisResult).get(result_id)
                            if result:
                                result.analysis_logs = chapter_logs
                                session.commit()
                        except Exception as e:
                            logger.error(f"保存分析进度时出错: {str(e)}")

                # 进行分析，传入进度回调
                try:
                    logger.info(f"调用API进行文本分析: chapter_id={chapter_id}, dimension={dimension}, model={model_to_use}")

                    # 设置客户端的当前小说ID和章节ID
                    client.current_novel_id = novel.id
                    client.current_chapter_id = chapter_id

                    # 确保使用真实API调用，不使用DEBUG模式
                    logger.info(f"确保使用真实API调用，不使用DEBUG模式")

                    # 使用与整本书分析相同的推理过程模板
                    from reasoning_content_template import generate_reasoning_content_template

                    # 生成章节专用的推理内容模板，使用之前保存的变量而不是数据库对象
                    reasoning_template = generate_reasoning_content_template(
                        dimension=dimension,
                        title=novel_title,
                        is_chapter=True,
                        chapter_title=chapter_title or f"第{chapter_number}章"
                    )

                    # 获取前序章节的分析结果，用于章节间信息联通
                    previous_analyses = ChapterAnalysisService.get_previous_chapter_analysis(
                        novel_id=novel_id,  # 使用之前保存的变量而不是数据库对象
                        current_chapter_id=chapter_id,
                        dimension=dimension,
                        limit=3  # 获取前3个章节的分析结果
                    )

                    # 使用成本优化器构建前序章节分析结果的摘要
                    previous_analyses_summary = ""
                    if previous_analyses:
                        try:
                            from src.config.chapter_cost_optimization import ChapterCostOptimization

                            # 计算原始长度
                            original_length = sum(len(analysis.get("content", "")) + len(analysis.get("reasoning_content", ""))
                                                for analysis in previous_analyses)

                            # 使用成本优化器生成摘要
                            previous_analyses_summary = ChapterCostOptimization.optimize_previous_analyses(
                                previous_analyses=previous_analyses,
                                current_chapter_number=chapter_number,
                                dimension=dimension,
                                prompt_template=prompt_template
                            )

                            # 计算成本降低效果
                            cost_stats = ChapterCostOptimization.calculate_cost_reduction(
                                original_length=original_length,
                                optimized_length=len(previous_analyses_summary)
                            )

                            logger.info(f"[成本优化] 前序章节摘要优化完成")
                            logger.info(f"[成本优化] 原始长度: {cost_stats['original_length']} → 优化后: {cost_stats['optimized_length']}")
                            logger.info(f"[成本优化] 压缩率: {cost_stats['reduction_rate']:.1%}，节省tokens: {cost_stats['saved_tokens']}")
                            logger.info(f"[成本优化] 预计节省成本: {cost_stats['estimated_cost_saving']:.4f}元")

                        except ImportError:
                            # 降级到原有逻辑
                            logger.warning("成本优化器不可用，使用原有摘要逻辑")
                            previous_analyses_summary = "## 前序章节分析摘要：\n\n"
                            for i, prev_analysis in enumerate(previous_analyses):
                                chapter_title = prev_analysis.get("chapter_title") or f"第{prev_analysis.get('chapter_number')}章"
                                content = prev_analysis.get("content", "")

                                # 根据提示词模板调整摘要长度
                                max_length = 200 if prompt_template == "simplified" else 400
                                content_summary = content[:max_length] + "..." if len(content) > max_length else content

                                previous_analyses_summary += f"### {chapter_title} 分析摘要：\n{content_summary}\n\n"

                            logger.info(f"成功构建前序章节分析摘要，包含 {len(previous_analyses)} 个章节")
                    else:
                        logger.info(f"没有前序章节分析结果可用")

                    # 导入增强的提示词生成器
                    try:
                        from src.api.chapter_analysis_prompt_enhancer_v2 import enhance_chapter_analysis_prompt_v2

                        # 构建基础提示词
                        base_prompt = f"""你是一位经验丰富的文学分析专家，请对以下章节文本进行详细的{dimension}分析。

请按照以下结构提供详细的推理过程和分析结果。分析内容不限字数，越详细越好：

1. 首先提供分析思路说明，列出5-7个分析方法，使用加粗和编号列表。
   - 每个分析方法必须直接引用章节中的1-2个具体例子（包括引号内的原文）
   - 简要分析这些例子如何体现该分析方法
   - 不要泛泛而谈，必须紧密结合文本内容

2. 然后提供详细分析，使用以下结构：
   - 第一部分：[从文本中提取的关键特点1]（使用三级标题）
     • 必须引用章节中的3-4个具体原文（使用引号标注）
     • 详细分析这些引文如何体现该特点
     • 分析这个特点在整个章节中的作用和意义
     • 每个部分的分析至少应包含300字以上的详细描述

   - 第二部分：[从文本中提取的关键特点2]
     • 同样必须引用具体原文并详细分析
     • 每个部分的分析至少应包含300字以上的详细描述

   - 以此类推，分析5-7个从文本中直接提取的关键特点

3. 最后，如果有前序章节的分析结果，请添加一个"章节间联系分析"部分，分析本章节与前序章节的联系、变化和发展。

{reasoning_template}

文本标题：《{novel_title}》
章节标题：{chapter_title or f"第{chapter_number}章"}
章节编号：第{chapter_number}章

{previous_analyses_summary if previous_analyses else ""}

请在分析本章节时，考虑前序章节的分析结果，注意章节之间的联系和发展脉络。分析应该不仅关注本章节的独立特点，还要关注其与前序章节的关联和变化。

文本内容：
{chapter_content}

请确保分析思路部分使用加粗和编号列出分析方法，详细分析部分使用三级标题分小节进行具体分析，每个小节都应包含多个具体文本例子和非常深入的分析。分析内容不限字数，越详细越好，可以尽可能展开分析。不要担心分析内容过长，系统已配置足够的处理能力。
"""

                        # 使用增强器增强提示词，传递提示词模板参数
                        enhanced_prompt = enhance_chapter_analysis_prompt_v2(base_prompt, dimension, prompt_template)
                        logger.info(f"成功使用增强器v2增强提示词，维度: {dimension}，模板: {prompt_template}")
                    except Exception as e:
                        logger.error(f"使用增强器v2增强提示词时出错: {str(e)}")

                        # 使用原始增强提示词作为备选
                        enhanced_prompt = f"""你是一位经验丰富的文学分析专家，请对以下章节文本进行详细的{dimension}分析。

重要补充要求（必须首先执行）：
1. 必须使用原文：分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 必须叙述原文内容：详细描述章节中的场景、事件、对话和人物心理活动
3. 不限字数不做限制：分析结果越详细越好，不要因为字数限制而简化分析，至少5000字以上
4. 必须承接上一章节同维度的分析结果和推理过程：将前序章节的分析作为本章分析的基础，展示章节间的连贯性和发展
5. 语言必须通俗易懂：使用清晰、简洁的语言表达，避免过多专业术语

请按照以下结构提供详细的推理过程和分析结果。分析内容不限字数，越详细越好：

1. 首先提供分析思路说明，列出5-7个分析方法，使用加粗和编号列表。
   - 每个分析方法必须直接引用章节中的1-2个具体例子（包括引号内的原文）
   - 简要分析这些例子如何体现该分析方法
   - 不要泛泛而谈，必须紧密结合文本内容

2. 然后提供详细分析，使用以下结构：
   - 第一部分：[从文本中提取的关键特点1]（使用三级标题）
     • 必须引用章节中的3-4个具体原文（使用引号标注）
     • 详细分析这些引文如何体现该特点
     • 分析这个特点在整个章节中的作用和意义
     • 每个部分的分析至少应包含500字以上的详细描述

   - 第二部分：[从文本中提取的关键特点2]
     • 同样必须引用具体原文并详细分析
     • 每个部分的分析至少应包含500字以上的详细描述

   - 以此类推，分析5-7个从文本中直接提取的关键特点

3. 最后，如果有前序章节的分析结果，请添加一个"章节间联系分析"部分，分析本章节与前序章节的联系、变化和发展。

{reasoning_template}

文本标题：《{novel_title}》
章节标题：{chapter_title or f"第{chapter_number}章"}
章节编号：第{chapter_number}章

{previous_analyses_summary if previous_analyses else ""}

请在分析本章节时，考虑前序章节的分析结果，注意章节之间的联系和发展脉络。分析应该不仅关注本章节的独立特点，还要关注其与前序章节的关联和变化。

文本内容：
{chapter_content}

请确保分析思路部分使用加粗和编号列出分析方法，详细分析部分使用三级标题分小节进行具体分析，每个小节都应包含多个具体文本例子和非常深入的分析。分析内容不限字数，越详细越好，可以尽可能展开分析。不要担心分析内容过长，系统已配置足够的处理能力。

重要提示：
1. 你的分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 每个分析点都必须有多个直接引用的原文（使用引号标注）
3. 分析思路部分也必须引用具体原文，而不仅是列出方法
4. 不要讨论文本中没有明确出现的内容
5. 聚焦于文本中最明显、最具体的例子，避免过度解读
6. 在分析中要体现章节间的联系和发展脉络，不要将本章节孤立对待
7. 分析内容必须非常详细，每个部分至少包含500字以上的描述
"""

                    # 调用API进行分析，使用增强的提示词
                    analysis_result = client.analyze_text(
                        enhanced_prompt,
                        analysis_type=dimension,
                        novel_id=novel_id,  # 使用之前保存的变量而不是数据库对象
                        chapter_id=chapter_id,
                        progress_callback=progress_callback,
                        prompt_template=prompt_template  # 传递提示词模板参数
                    )

                    # 检查推理过程内容
                    original_reasoning_content = None
                    if "reasoning_content" in analysis_result and analysis_result["reasoning_content"]:
                        logger.info(f"成功获取推理过程内容，长度: {len(analysis_result['reasoning_content'])} 字符")
                        original_reasoning_content = analysis_result["reasoning_content"]
                    else:
                        # 如果API没有返回推理过程，使用内容作为推理过程
                        logger.warning(f"未能获取推理过程内容，将使用分析内容作为推理过程")
                        original_reasoning_content = analysis_result.get("content", "")

                    # 生成整本书风格的结构化推理内容
                    try:
                        # 导入推理内容模板生成器
                        from reasoning_content_template import generate_reasoning_content_template, format_reasoning_content, get_dimension_name, get_dimension_analysis_approach

                        # 保存原始推理内容，确保不丢失
                        original_analysis = original_reasoning_content

                        # 获取维度名称
                        dimension_name = get_dimension_name(dimension)

                        # 尝试生成更丰富的推理内容
                        try:
                            # 获取前序章节的分析结果，用于章节间信息联通
                            previous_analyses = ChapterAnalysisService.get_previous_chapter_analysis(
                                novel_id=novel_id,  # 使用之前保存的变量而不是数据库对象
                                current_chapter_id=chapter_id,
                                dimension=dimension,
                                limit=2  # 获取前2个章节的分析结果
                            )

                            # 构建前序章节分析结果的摘要
                            previous_analyses_summary = ""
                            if previous_analyses:
                                previous_analyses_summary = "## 前序章节分析摘要：\n\n"
                                for i, prev_analysis in enumerate(previous_analyses):
                                    chapter_title = prev_analysis.get("chapter_title") or f"第{prev_analysis.get('chapter_number')}章"
                                    content = prev_analysis.get("content", "")

                                    # 提取分析内容的关键点（最多300字）
                                    content_summary = content[:300] + "..." if len(content) > 300 else content

                                    previous_analyses_summary += f"### {chapter_title} 分析摘要：\n{content_summary}\n\n"

                                logger.info(f"成功构建前序章节分析摘要，包含 {len(previous_analyses)} 个章节")
                            else:
                                logger.info(f"没有前序章节分析结果可用于增强分析")

                            # 构建增强的分析提示词，生成与整本书分析类似的结构化内容
                            enhanced_prompt = f"""你是一位经验丰富的文学分析专家，请对以下章节文本进行详细的{dimension_name}分析。
请按照以下结构提供分析：

1. 首先提供分析思路说明，列出5-7个分析方法，使用加粗和编号列表。
   - 每个分析方法必须直接引用章节中的1-2个具体例子（包括引号内的原文）
   - 简要分析这些例子如何体现该分析方法
   - 不要泛泛而谈，必须紧密结合文本内容

2. 然后提供详细分析，使用以下结构：
   - 第一部分：[从文本中提取的关键特点1]（使用三级标题）
     • 必须引用章节中的3-4个具体原文（使用引号标注）
     • 详细分析这些引文如何体现该特点
     • 分析这个特点在整个章节中的作用和意义
     • 每个部分的分析至少应包含300字以上的详细描述

   - 第二部分：[从文本中提取的关键特点2]
     • 同样必须引用具体原文并详细分析
     • 每个部分的分析至少应包含300字以上的详细描述

   - 以此类推，分析5-7个从文本中直接提取的关键特点

3. 最后，如果有前序章节的分析结果，请添加一个"章节间联系分析"部分，分析本章节与前序章节在{dimension_name}方面的联系、变化和发展。

{previous_analyses_summary if previous_analyses else ""}

分析要专业、深入，但必须严格基于文本内容，不要添加文本中不存在的内容或过度推测。每个特点的分析必须包含至少3个直接引用的原文，并对这些原文进行详细分析。分析内容不限字数，越详细越好，可以尽可能展开分析。

章节标题：{chapter_title or f"第{chapter_number}章"}
章节编号：第{chapter_number}章
章节内容：
{chapter_content[:5000]}...（章节内容较长，此处仅显示部分）

重要提示：
1. 你的分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 每个分析点都必须有多个直接引用的原文（使用引号标注）
3. 分析思路部分也必须引用具体原文，而不仅是列出方法
4. 不要讨论文本中没有明确出现的内容
5. 聚焦于文本中最明显、最具体的例子，避免过度解读
6. 在分析中要体现章节间的联系和发展脉络，不要将本章节孤立对待
7. 分析内容必须非常详细，每个部分至少包含500字以上的描述
"""

                            logger.info(f"尝试生成更丰富的推理内容，使用增强提示词")

                            # 调用API生成更丰富的推理内容
                            rich_analysis_result = client.analyze_text(
                                enhanced_prompt,
                                analysis_type=f"{dimension}_enhanced",
                                novel_id=novel_id,  # 使用之前保存的变量
                                chapter_id=chapter_id,
                                prompt_template=prompt_template  # 传递提示词模板参数
                            )

                            # 检查是否成功获取丰富的推理内容
                            if "content" in rich_analysis_result and rich_analysis_result["content"]:
                                rich_content = rich_analysis_result["content"]
                                logger.info(f"成功生成丰富的推理内容，长度: {len(rich_content)} 字符")

                                # 提取分析思路部分
                                approach_match = re.search(r'(?:分析思路|分析方法)[:：]?\s*([\s\S]*?)(?=\n##|\n\d+\.|\Z)', rich_content)
                                approach_content = approach_match.group(1).strip() if approach_match else get_dimension_analysis_approach(dimension)

                                # 提取详细分析部分
                                analysis_match = re.search(r'(?:详细分析|分析结果)[:：]?\s*([\s\S]*)', rich_content)
                                analysis_content = analysis_match.group(1).strip() if analysis_match else rich_content

                                # 提取章节间联系分析部分
                                chapter_connection_match = re.search(r'(?:##\s*章节间联系分析|##\s*与前序章节的联系与发展)[:：]?\s*([\s\S]*?)(?=\n##|\Z)', rich_content)
                                chapter_connection_content = ""
                                if chapter_connection_match and chapter_connection_match.group(1):
                                    chapter_connection_content = chapter_connection_match.group(1).strip()
                                    logger.info(f"成功提取章节间联系分析内容，长度: {len(chapter_connection_content)} 字符")

                                # 构建结构化的推理内容，包含丰富的分析、章节间联系和原始内容
                                structured_reasoning = f"""## 分析思路说明：
{approach_content}

## 详细{dimension_name}分析：
{analysis_content}

"""

                                # 如果有章节间联系分析，添加到结构化内容中
                                if chapter_connection_content:
                                    structured_reasoning += f"""## 章节间联系分析：
{chapter_connection_content}

"""

                                # 添加原始分析内容
                                structured_reasoning += f"""## 原始分析内容：
{original_analysis}
"""

                                # 记录生成的丰富内容，用于调试
                                logger.info(f"生成的结构化推理内容包含分析思路和详细分析，总长度: {len(structured_reasoning)} 字符")

                                # 检查内容是否足够丰富
                                if len(analysis_content) < 1000:
                                    logger.warning(f"生成的详细分析内容可能不够丰富，长度仅为 {len(analysis_content)} 字符")

                                    # 尝试再次生成更丰富的内容
                                    try:
                                        # 获取前序章节的分析结果，用于章节间信息联通
                                        previous_analyses = ChapterAnalysisService.get_previous_chapter_analysis(
                                            novel_id=novel_id,  # 使用之前保存的变量而不是数据库对象
                                            current_chapter_id=chapter_id,
                                            dimension=dimension,
                                            limit=2  # 获取前2个章节的分析结果
                                        )

                                        # 构建前序章节分析结果的摘要
                                        previous_analyses_summary = ""
                                        if previous_analyses:
                                            previous_analyses_summary = "## 前序章节分析摘要：\n\n"
                                            for i, prev_analysis in enumerate(previous_analyses):
                                                chapter_title = prev_analysis.get("chapter_title") or f"第{prev_analysis.get('chapter_number')}章"
                                                content = prev_analysis.get("content", "")

                                                # 提取分析内容的关键点（最多300字）
                                                content_summary = content[:300] + "..." if len(content) > 300 else content

                                                previous_analyses_summary += f"### {chapter_title} 分析摘要：\n{content_summary}\n\n"

                                            logger.info(f"成功构建前序章节分析摘要，包含 {len(previous_analyses)} 个章节")
                                        else:
                                            logger.info(f"没有前序章节分析结果可用于深入分析")

                                        # 构建更强调详细内容的提示词
                                        detailed_prompt = f"""你是一位经验丰富的文学分析专家，请对以下章节文本进行非常详细的{dimension_name}分析。

我需要极其详尽的分析，每个分析点必须：
1. 直接引用至少5个章节中的原文（使用引号标注）
2. 对每个引用进行详细分析，说明它如何体现特定的{dimension_name}特点
3. 分析必须完全基于文本内容，不要添加不存在的内容或过度推测
4. 考虑本章节与前序章节的联系和发展脉络
5. 每个分析部分至少包含500字以上的详细描述

{previous_analyses_summary if previous_analyses else ""}

请按照以下结构提供分析：

1. 首先提供分析思路说明，列出7个分析方法，使用加粗和编号列表。
   - 每个分析方法必须直接引用章节中的2-3个具体例子（包括引号内的原文）
   - 详细分析这些例子如何体现该分析方法
   - 不要泛泛而谈，必须紧密结合文本内容

2. 然后提供详细分析，使用以下结构：
   - 第一部分：[从文本中提取的关键特点1]（使用三级标题）
     • 必须引用章节中的5-6个具体原文（使用引号标注）
     • 详细分析这些引文如何体现该特点
     • 分析这个特点在整个章节中的作用和意义
     • 每个部分的分析至少应包含500字以上的详细描述

   - 第二部分：[从文本中提取的关键特点2]
     • 同样必须引用具体原文并详细分析
     • 每个部分的分析至少应包含500字以上的详细描述

   - 以此类推，分析7个从文本中直接提取的关键特点

3. 最后，添加一个"章节间联系分析"部分，分析本章节与前序章节在{dimension_name}方面的联系、变化和发展。

请分析以下章节的{dimension_name}特点，重点关注：
1. 文本中实际出现的具体表达方式和修辞手法（必须引用原文）
2. 文本中的句式结构和段落组织（必须引用原文）
3. 文本中体现的语气语调和情感表达（必须引用原文）
4. 文本中显示的风格特点（必须引用原文）
5. 这些特点在文本中的具体作用（必须基于引用的原文分析）
6. 本章节与前序章节在{dimension_name}方面的联系、变化和发展（如果有前序章节分析）

章节标题：{chapter_title or f"第{chapter_number}章"}
章节编号：第{chapter_number}章
章节内容：
{chapter_content[:5000]}...（章节内容较长，此处仅显示部分）

重要提示：
1. 你的分析必须100%基于章节的实际内容，不允许泛泛而谈或猜测
2. 每个分析点都必须有多个直接引用的原文（使用引号标注）
3. 不要讨论文本中没有明确出现的内容
4. 聚焦于文本中最明显、最具体的例子，避免过度解读
5. 分析必须具体到每个引用的原文，而不是笼统概括
6. 在分析中要体现章节间的联系和发展脉络，不要将本章节孤立对待
7. 分析内容必须非常详细，每个部分至少包含500字以上的描述
"""

                                        # 调用API生成更详细的内容
                                        detailed_result = client.analyze_text(
                                            detailed_prompt,
                                            analysis_type=f"{dimension}_detailed",
                                            novel_id=novel_id,  # 使用之前保存的变量而不是数据库对象
                                            chapter_id=chapter_id,
                                            prompt_template=prompt_template  # 传递提示词模板参数
                                        )

                                        # 如果成功获取更详细的内容，合并到结构化内容中
                                        if "content" in detailed_result and detailed_result["content"]:
                                            detailed_content = detailed_result["content"]
                                            logger.info(f"成功生成更详细的分析内容，长度: {len(detailed_content)} 字符")

                                            # 提取章节间联系分析部分
                                            chapter_connection_match = re.search(r'(?:##\s*章节间联系分析|##\s*与前序章节的联系与发展|本章节与前序章节在.*?方面的联系)[:：]?\s*([\s\S]*?)(?=\n##|\Z)', detailed_content)
                                            chapter_connection_content = ""
                                            if chapter_connection_match and chapter_connection_match.group(1):
                                                chapter_connection_content = chapter_connection_match.group(1).strip()
                                                logger.info(f"成功从深入分析中提取章节间联系分析内容，长度: {len(chapter_connection_content)} 字符")

                                            # 构建更丰富的结构化内容
                                            structured_reasoning = f"""## 分析思路说明：
{approach_content}

## 详细{dimension_name}分析：
{analysis_content}

## 深入分析：
{detailed_content}

"""

                                            # 如果有章节间联系分析，添加到结构化内容中
                                            if chapter_connection_content and not "章节间联系分析" in structured_reasoning:
                                                structured_reasoning += f"""## 章节间联系分析：
{chapter_connection_content}

"""

                                            # 添加原始分析内容
                                            structured_reasoning += f"""## 原始分析内容：
{original_analysis}
"""
                                    except Exception as e:
                                        logger.error(f"生成更详细分析内容时出错: {str(e)}")
                                        # 保持原有内容不变
                            else:
                                # 如果无法获取丰富内容，使用基本结构
                                logger.warning(f"无法获取丰富的推理内容，使用基本结构")
                                structured_reasoning = f"""## 分析思路说明：
{get_dimension_analysis_approach(dimension)}

## 详细{dimension_name}分析：
### 1. 主要{dimension_name}特点
{original_analysis}
"""
                        except Exception as e:
                            # 如果生成丰富内容失败，使用基本结构
                            logger.error(f"生成丰富推理内容时出错: {str(e)}")
                            logger.error(traceback.format_exc())
                            structured_reasoning = f"""## 分析思路说明：
{get_dimension_analysis_approach(dimension)}

## 详细{dimension_name}分析：
{original_analysis}
"""

                        # 合并原始推理内容和结构化内容
                        analysis_result["reasoning_content"] = structured_reasoning
                        logger.info(f"已生成结构化推理内容，长度: {len(structured_reasoning)} 字符")
                    except Exception as e:
                        # 如果生成结构化内容失败，使用原始内容并进行简单格式化
                        logger.error(f"生成结构化推理内容时出错: {str(e)}")
                        logger.error(traceback.format_exc())
                        analysis_result["reasoning_content"] = format_reasoning_content(original_reasoning_content)
                        logger.info(f"使用格式化的原始推理内容，长度: {len(analysis_result['reasoning_content'])} 字符")

                    logger.info(f"API分析完成: 结果长度={len(str(analysis_result))}")

                    # 添加到控制台日志
                    try:
                        from src.web.app import add_analysis_log
                        add_analysis_log(novel_id, f"[章节分析] 章节 {chapter_id} 的 {dimension} 维度分析完成", "info", dimension)
                    except Exception as log_error:
                        logger.error(f"添加分析完成日志时出错: {str(log_error)}")
                except Exception as e:
                    logger.error(f"调用API分析文本时出错: {str(e)}")
                    logger.error(traceback.format_exc())

                    # 添加到控制台日志
                    try:
                        from src.web.app import add_analysis_log
                        add_analysis_log(novel_id, f"[章节分析] 章节 {chapter_id} 的 {dimension} 维度分析出错: {str(e)}", "error", dimension)
                    except Exception as log_error:
                        logger.error(f"添加分析错误日志时出错: {str(log_error)}")

                    return {"success": False, "error": f"分析文本时出错: {str(e)}"}

                # 检查分析结果是否有效
                if not analysis_result:
                    logger.error("API返回空的分析结果")
                    return {"success": False, "error": "API返回空的分析结果"}

                if "content" not in analysis_result:
                    logger.error("API返回的分析结果缺少content字段")
                    return {"success": False, "error": "API返回的分析结果缺少必要字段"}

                # 记录分析结束
                end_time = time.time()
                analysis_time = end_time - start_time
                logger.info(f"完成分析章节 {chapter_id} 的 {dimension} 维度，耗时 {analysis_time:.2f} 秒")

                # 添加分析结束日志
                chapter_logs.append({
                    "timestamp": datetime.now().isoformat(),
                    "level": "info",
                    "message": f"完成分析章节 {chapter_id} 的 {dimension} 维度，耗时 {analysis_time:.2f} 秒",
                    "dimension": dimension,
                    "chapter_id": chapter_id,
                    "analysis_time": analysis_time
                })

                # 创建或更新分析结果
                try:
                    logger.info(f"准备保存分析结果: chapter_id={chapter_id}, dimension={dimension}, content_length={len(analysis_result.get('content', ''))}")

                    if existing_result:
                        logger.info(f"更新现有分析结果: result_id={existing_result.id}")
                        try:
                            existing_result.content = analysis_result.get("content", "")
                            existing_result.reasoning_content = analysis_result.get("reasoning_content", "")
                            existing_result.analysis_metadata = analysis_result.get("metadata", {})
                            existing_result.analysis_logs = chapter_logs
                            result = existing_result
                        except Exception as e:
                            logger.error(f"更新现有分析结果时出错: {str(e)}", exc_info=True)
                            raise
                    elif save_process and result_id:
                        # 更新临时分析结果
                        temp_result = session.query(ChapterAnalysisResult).get(result_id)
                        if temp_result:
                            logger.info(f"更新临时分析结果: result_id={temp_result.id}")
                            # 准备元数据
                            metadata = analysis_result.get("metadata", {})
                            metadata["is_chapter_specific"] = True  # 标记为章节特定分析

                            temp_result.content = analysis_result.get("content", "")
                            # 确保推理过程内容被正确保存
                            reasoning_content = analysis_result.get("reasoning_content", "")
                            if reasoning_content:
                                logger.info(f"找到推理过程内容，长度: {len(reasoning_content)} 字符")
                            else:
                                logger.warning(f"未找到推理过程内容，将使用空字符串")
                            temp_result.reasoning_content = reasoning_content
                            temp_result.analysis_metadata = metadata
                            temp_result.analysis_logs = chapter_logs
                            result = temp_result
                        else:
                            # 如果临时结果不存在，创建新结果
                            logger.info(f"临时分析结果不存在，创建新结果: result_id={result_id}")
                            # 准备元数据
                            metadata = analysis_result.get("metadata", {})
                            metadata["is_chapter_specific"] = True  # 标记为章节特定分析

                            result = ChapterAnalysisResult(
                                chapter_id=chapter_id,
                                novel_id=novel_id,  # 使用之前保存的变量而不是数据库对象
                                dimension=dimension,
                                content=analysis_result.get("content", ""),
                                reasoning_content=analysis_result.get("reasoning_content", ""),
                                metadata=metadata,
                                logs=chapter_logs
                            )
                            session.add(result)
                    else:
                        # 创建新结果
                        logger.info(f"创建新分析结果: chapter_id={chapter_id}, dimension={dimension}")
                        # 准备元数据
                        metadata = analysis_result.get("metadata", {})
                        metadata["is_chapter_specific"] = True  # 标记为章节特定分析

                        result = ChapterAnalysisResult(
                            chapter_id=chapter_id,
                            novel_id=novel_id,  # 使用之前保存的变量而不是数据库对象
                            dimension=dimension,
                            content=analysis_result.get("content", ""),
                            reasoning_content=analysis_result.get("reasoning_content", ""),
                            metadata=metadata,
                            logs=chapter_logs
                        )
                        session.add(result)

                    logger.info(f"分析结果准备就绪，即将提交到数据库")
                except Exception as e:
                    logger.error(f"准备分析结果时出错: {str(e)}")
                    logger.error(traceback.format_exc())
                    raise

                # 提交事务
                try:
                    logger.info(f"提交分析结果到数据库")
                    session.commit()
                    logger.info(f"提交分析结果成功: chapter_id={chapter_id}, dimension={dimension}")

                    # 更新章节的is_analyzed标志
                    try:
                        logger.info(f"更新章节分析状态: chapter_id={chapter_id}")
                        chapter.is_analyzed = True
                        session.commit()
                        logger.info(f"章节分析状态已更新")
                    except Exception as e:
                        logger.error(f"更新章节分析状态时出错: {str(e)}")
                        logger.error(traceback.format_exc())
                        # 不抛出异常，因为主要分析结果已经保存成功
                except Exception as e:
                    logger.error(f"提交分析结果到数据库时出错: {str(e)}", exc_info=True)
                    session.rollback()
                    logger.info(f"已回滚事务")
                    return {"success": False, "error": f"保存分析结果时出错: {str(e)}"}

                # 保存分析过程
                if save_process and "reasoning_content" in analysis_result:
                    try:
                        reasoning = analysis_result.get("reasoning_content", "")
                        if reasoning:
                            logger.info(f"保存分析推理过程: chapter_id={chapter_id}, dimension={dimension}, reasoning_length={len(reasoning)}")
                            process = ChapterAnalysisProcess(
                                chapter_id=chapter_id,
                                novel_id=novel_id,  # 使用之前保存的变量而不是数据库对象
                                result_id=result.id,
                                dimension=dimension,
                                processing_stage="reasoning",
                                output_text=reasoning,
                                is_successful=True
                            )
                            session.add(process)
                            session.commit()
                            logger.info(f"分析推理过程已成功保存")
                        else:
                            logger.warning(f"分析推理过程为空，跳过保存: chapter_id={chapter_id}, dimension={dimension}")
                    except Exception as e:
                        logger.error(f"保存分析推理过程时出错: {str(e)}")
                        logger.error(traceback.format_exc())
                        # 不抛出异常，因为主要分析结果已经保存成功

                # 创建一个包含所有必要信息的结果字典，而不是直接引用数据库对象
                result_dict = {
                    "id": result.id,
                    "chapter_id": result.chapter_id,
                    "novel_id": result.novel_id,
                    "dimension": result.dimension,
                    "content": result.content,
                    "reasoning_content": result.reasoning_content,
                    "metadata": result.analysis_metadata,
                    "logs": result.analysis_logs,
                    "created_at": result.created_at.isoformat() if result.created_at else None,
                    "updated_at": result.updated_at.isoformat() if result.updated_at else None
                }

                return {
                    "success": True,
                    "result": result_dict,
                    "message": "分析完成",
                    "cached": False,
                    "analysis_time": analysis_time
                }
            except Exception as e:
                logger.error(f"分析章节时发生未处理异常: chapter_id={chapter_id}, dimension={dimension}, error={str(e)}", exc_info=True)
                try:
                    session.rollback()
                    logger.info("已回滚事务")
                except:
                    pass
                return {"success": False, "error": f"分析章节时发生内部错误: {str(e)}"}
        except Exception as e:
            logger.error(f"分析章节时发生未处理异常: chapter_id={chapter_id}, dimension={dimension}, error={str(e)}", exc_info=True)
            try:
                session.rollback()
                logger.info("已回滚事务")
            except:
                pass
            return {"success": False, "error": f"分析章节时发生内部错误: {str(e)}"}
        finally:
            try:
                session.close()
                logger.info(f"已关闭数据库会话: chapter_id={chapter_id}, dimension={dimension}")
            except:
                pass

    @staticmethod
    def start_chapter_analysis_task(novel_id: int, dimension: str, model: str = None,
                                   parallel: bool = True, max_workers: int = 4, prompt_template: str = "default") -> Dict[str, Any]:
        """
        启动章节分析任务，支持并行处理。

        Args:
            novel_id: 小说ID
            dimension: 分析维度
            model: 使用的模型
            parallel: 是否并行处理
            max_workers: 最大并行工作线程数

        Returns:
            任务状态
        """
        # 生成任务ID
        task_id = f"chapter_analysis_{novel_id}_{dimension}_{int(time.time())}"

        # 检查是否已有正在运行的任务
        if novel_id in analysis_tasks and analysis_tasks[novel_id].get("is_running", False):
            return {
                "success": False,
                "error": f"小说 {novel_id} 已有正在运行的分析任务",
                "task_id": analysis_tasks[novel_id].get("task_id")
            }

        # 初始化任务状态
        analysis_tasks[novel_id] = {
            "task_id": task_id,
            "novel_id": novel_id,
            "dimension": dimension,
            "model": model or config.DEFAULT_MODEL,
            "parallel": parallel,
            "max_workers": max_workers,
            "is_running": True,
            "start_time": time.time(),
            "progress": 0,
            "total_chapters": 0,
            "completed_chapters": 0,
            "current_chapter": None,
            "status": "starting"
        }

        # 初始化日志
        analysis_logs[novel_id] = []

        # 添加任务启动日志，区分精简版和默认版
        if prompt_template == "simplified":
            message = f"🚀 启动章节分析任务（精简版降本增效模式）: {task_id}，并行处理: {parallel}，最大工作线程数: {max_workers}"
            analysis_logs[novel_id].append({
                "timestamp": datetime.now().isoformat(),
                "level": "info",
                "message": message,
                "dimension": f"{dimension}（精简版）",
                "novel_id": novel_id,
                "parallel": parallel,
                "max_workers": max_workers,
                "mode": "simplified",
                "cost_optimization": "已启用5项降本增效策略"
            })
        else:
            analysis_logs[novel_id].append({
                "timestamp": datetime.now().isoformat(),
                "level": "info",
                "message": f"启动章节分析任务（默认版）: {task_id}，并行处理: {parallel}，最大工作线程数: {max_workers}",
                "dimension": f"{dimension}（默认版）",
                "novel_id": novel_id,
                "parallel": parallel,
                "max_workers": max_workers,
                "mode": "default"
            })

        # 启动分析线程
        thread = threading.Thread(
            target=ChapterAnalysisService._run_chapter_analysis_task,
            args=(task_id, novel_id, dimension, model, parallel, max_workers, prompt_template)
        )
        thread.daemon = True
        thread.start()

        return {
            "success": True,
            "task_id": task_id,
            "parallel": parallel,
            "max_workers": max_workers,
            "message": f"开始分析小说 {novel_id} 的 {dimension} 维度"
        }

    @staticmethod
    def _run_chapter_analysis_task(task_id: str, novel_id: int, dimension: str, model: str = None,
                                  parallel: bool = True, max_workers: int = 4, prompt_template: str = "default") -> None:
        """
        运行章节分析任务，支持并行处理。

        Args:
            task_id: 任务ID
            novel_id: 小说ID
            dimension: 分析维度
            model: 使用的模型
            parallel: 是否并行处理
            max_workers: 最大并行工作线程数
        """
        logger.info(f"开始运行章节分析任务: {task_id}")

        # 更新任务状态
        analysis_tasks[novel_id]["status"] = "splitting_chapters"

        try:
            # 分割章节
            chapters = ChapterAnalysisService.split_novel_into_chapters(novel_id)
            if not chapters:
                logger.error(f"分割章节失败: {novel_id}")
                analysis_tasks[novel_id]["status"] = "failed"
                analysis_tasks[novel_id]["error"] = "分割章节失败"
                analysis_tasks[novel_id]["is_running"] = False
                return

            # 更新任务状态
            analysis_tasks[novel_id]["total_chapters"] = len(chapters)
            analysis_tasks[novel_id]["status"] = "analyzing"

            # 创建分析结果字典，用于存储分析结果
            results = {}
            completed_count = 0

            # 定义章节分析函数
            def analyze_chapter_worker(chapter, index):
                nonlocal completed_count

                # 检查任务是否被取消
                if not analysis_tasks.get(novel_id, {}).get("is_running", False):
                    logger.info(f"任务已取消: {task_id}")
                    return None

                # 更新当前章节（处理章节对象和字典两种情况）
                chapter_id = chapter.id if hasattr(chapter, 'id') else chapter['id']
                analysis_tasks[novel_id]["current_chapter"] = chapter_id

                # 分析章节
                logger.info(f"分析章节 {chapter_id} ({index+1}/{len(chapters)})")
                result = ChapterAnalysisService.analyze_chapter(chapter_id, dimension, model, prompt_template=prompt_template)

                # 检查分析结果
                if not result.get("success", False):
                    logger.error(f"分析章节 {chapter_id} 失败: {result.get('error')}")
                    # 添加到日志，但继续分析其他章节
                    analysis_logs[novel_id].append({
                        "timestamp": datetime.now().isoformat(),
                        "level": "error",
                        "message": f"分析章节 {chapter_id} 失败: {result.get('error')}",
                        "dimension": dimension,
                        "chapter_id": chapter_id
                    })

                # 更新进度
                with threading.Lock():
                    completed_count += 1
                    analysis_tasks[novel_id]["completed_chapters"] = completed_count
                    analysis_tasks[novel_id]["progress"] = int(completed_count / len(chapters) * 100)

                return result

            # 根据是否并行处理选择不同的分析方式
            if parallel and len(chapters) > 1:
                # 应用效率与成本平衡优化策略 + 智能维度分组
                if prompt_template == "simplified":
                    # 精简版：智能维度分组 + 分段处理策略
                    dimension_groups = ChapterAnalysisService._create_dimension_groups(dimension)

                    optimized_config = {
                        "worker_threads": min(max_workers, 5),  # 最多5个工作线程
                        "instance_concurrency": 8,  # 单实例并发度8
                        "connection_pool_size": 15,  # 连接池大小15
                        "segment_size": 3,  # 分段大小3
                        "use_segmented_analysis": len(chapters) > 6,  # 超过6章使用分段分析
                        "use_dimension_grouping": True,  # 启用维度分组
                        "dimension_groups": dimension_groups,  # 维度分组配置
                        "api_delay": 1.5,  # API延迟
                        "resource_cleanup": True,  # 启用资源清理
                        "reserved_instances": True,  # 启用预留实例
                        "async_processing": True,  # 启用异步处理
                        "trigger_optimization": True,  # 启用触发器优化
                        "output_control": {  # 输出控制策略
                            "core_dimensions_max_tokens": 800,    # 核心维度最大token数
                            "auxiliary_dimensions_max_tokens": 500,  # 辅助维度最大token数
                            "detail_dimensions_max_tokens": 300,    # 细节维度最大token数
                            "enable_structured_output": True,      # 启用结构化输出
                            "enable_smart_summarization": True     # 启用智能汇总
                        }
                    }
                    logger.info(f"[章节分析-智能优化] 精简版模式：维度分组{len(dimension_groups)}组，输出控制，智能汇总")
                    logger.info(f"[章节分析-效率成本平衡] {optimized_config['worker_threads']}个线程，单实例并发{optimized_config['instance_concurrency']}，连接池{optimized_config['connection_pool_size']}")
                else:
                    # 默认版：性能优先配置
                    optimized_config = {
                        "worker_threads": min(max_workers, 12),  # 最多12个工作线程
                        "instance_concurrency": 10,  # 单实例并发度10
                        "connection_pool_size": 30,  # 连接池大小30
                        "segment_size": 5,  # 分段大小5
                        "use_segmented_analysis": False,  # 默认版不使用分段分析
                        "use_dimension_grouping": False,  # 默认版不使用维度分组
                        "api_delay": 0.5,  # 较短API延迟
                        "resource_cleanup": True,  # 启用资源清理
                        "reserved_instances": True,  # 启用预留实例
                        "async_processing": True,  # 启用异步处理
                        "trigger_optimization": False,  # 默认版不需要触发器优化
                        "output_control": {  # 默认版不限制输出
                            "enable_structured_output": False,
                            "enable_smart_summarization": False
                        }
                    }
                    logger.info(f"[章节分析-性能优先] 默认版模式：{optimized_config['worker_threads']}个线程，单实例并发{optimized_config['instance_concurrency']}，连接池{optimized_config['connection_pool_size']}")

                # 应用优化策略
                ChapterAnalysisService._apply_chapter_analysis_optimizations(task_id, optimized_config, prompt_template)

                # 选择分析策略
                if optimized_config["use_segmented_analysis"]:
                    # 使用分段分析策略
                    results = ChapterAnalysisService._analyze_chapters_in_segments(
                        task_id, novel_id, chapters, dimension, model, optimized_config, analyze_chapter_worker
                    )
                else:
                    # 使用优化的并行分析策略
                    results = ChapterAnalysisService._analyze_chapters_parallel_optimized(
                        task_id, novel_id, chapters, dimension, model, optimized_config, analyze_chapter_worker
                    )
            else:
                # 串行处理
                logger.info("使用串行处理")
                results = {}

                # 分析每个章节
                for i, chapter in enumerate(chapters):
                    # 检查任务是否被取消
                    if not analysis_tasks.get(novel_id, {}).get("is_running", False):
                        logger.info(f"任务已取消: {task_id}")
                        break

                    # 分析章节
                    result = analyze_chapter_worker(chapter, i)
                    if result:
                        chapter_id = chapter.id if hasattr(chapter, 'id') else chapter['id']
                        results[chapter_id] = result

            # 分析完成后的资源清理
            try:
                from src.services.test_service import TestService
                TestService._cleanup_analysis_resources(None, task_id, prompt_template)
                logger.info(f"[章节分析] 任务 {task_id} 资源清理完成")
            except Exception as e:
                logger.warning(f"[章节分析] 资源清理时出错: {str(e)}")

            # 检查是否所有章节都已分析
            if completed_count == len(chapters):
                # 更新任务状态
                analysis_tasks[novel_id]["status"] = "completed"
                analysis_tasks[novel_id]["progress"] = 100

                # 添加到日志
                analysis_logs[novel_id].append({
                    "timestamp": datetime.now().isoformat(),
                    "level": "info",
                    "message": f"完成所有章节的分析，共 {len(chapters)} 个章节",
                    "dimension": dimension,
                    "total_chapters": len(chapters),
                    "completed_chapters": completed_count
                })
            else:
                # 更新任务状态
                analysis_tasks[novel_id]["status"] = "partially_completed"
                analysis_tasks[novel_id]["progress"] = int(completed_count / len(chapters) * 100)

                # 添加到日志
                analysis_logs[novel_id].append({
                    "timestamp": datetime.now().isoformat(),
                    "level": "warning",
                    "message": f"部分章节分析完成，共 {len(chapters)} 个章节，完成 {completed_count} 个",
                    "dimension": dimension,
                    "total_chapters": len(chapters),
                    "completed_chapters": completed_count
                })

            # 更新任务结束时间
            analysis_tasks[novel_id]["end_time"] = time.time()
            analysis_tasks[novel_id]["is_running"] = False

            logger.info(f"完成章节分析任务: {task_id}")
        except Exception as e:
            logger.error(f"运行章节分析任务时出错: {str(e)}")
            logger.error(traceback.format_exc())

            # 更新任务状态
            analysis_tasks[novel_id]["status"] = "failed"
            analysis_tasks[novel_id]["error"] = str(e)
            analysis_tasks[novel_id]["is_running"] = False

            # 添加到日志
            analysis_logs[novel_id].append({
                "timestamp": datetime.now().isoformat(),
                "level": "error",
                "message": f"运行章节分析任务时出错: {str(e)}",
                "dimension": dimension,
                "traceback": traceback.format_exc()
            })

    @staticmethod
    def get_chapter_analysis_task_progress(novel_id: int) -> Dict[str, Any]:
        """
        获取章节分析任务进度。

        Args:
            novel_id: 小说ID

        Returns:
            任务进度
        """
        if novel_id not in analysis_tasks:
            return {
                "success": False,
                "error": f"小说 {novel_id} 没有正在运行的分析任务"
            }

        task = analysis_tasks[novel_id]
        logs = analysis_logs.get(novel_id, [])

        return {
            "success": True,
            "task_id": task.get("task_id"),
            "novel_id": novel_id,
            "dimension": task.get("dimension"),
            "is_running": task.get("is_running", False),
            "status": task.get("status"),
            "progress": task.get("progress", 0),
            "total_chapters": task.get("total_chapters", 0),
            "completed_chapters": task.get("completed_chapters", 0),
            "current_chapter": task.get("current_chapter"),
            "start_time": task.get("start_time"),
            "end_time": task.get("end_time") if "end_time" in task else None,
            "logs": logs[-10:] if logs else []  # 只返回最近的10条日志
        }

    @staticmethod
    def cancel_chapter_analysis_task(novel_id: int) -> Dict[str, Any]:
        """
        取消章节分析任务。

        Args:
            novel_id: 小说ID

        Returns:
            取消结果
        """
        if novel_id not in analysis_tasks:
            return {
                "success": False,
                "error": f"小说 {novel_id} 没有正在运行的分析任务"
            }

        # 更新任务状态
        analysis_tasks[novel_id]["is_running"] = False
        analysis_tasks[novel_id]["status"] = "cancelled"

        return {
            "success": True,
            "message": f"已取消小说 {novel_id} 的分析任务"
        }

    @staticmethod
    def get_chapter_analysis_results(novel_id: int, dimension: str = None) -> Dict[str, Any]:
        """
        获取章节分析结果。

        Args:
            novel_id: 小说ID
            dimension: 分析维度（可选）

        Returns:
            分析结果
        """
        session = Session()
        try:
            # 获取小说
            novel = session.query(Novel).filter(Novel.id == novel_id).first()
            if not novel:
                logger.error(f"小说不存在: {novel_id}")
                return {"success": False, "error": "小说不存在"}

            # 获取章节
            chapters = session.query(Chapter).filter(Chapter.novel_id == novel_id).order_by(Chapter.chapter_number).all()
            if not chapters:
                logger.error(f"小说 {novel_id} 没有章节")
                return {"success": False, "error": "小说没有章节"}

            # 构建查询
            query = session.query(ChapterAnalysisResult).filter(ChapterAnalysisResult.novel_id == novel_id)
            if dimension:
                query = query.filter(ChapterAnalysisResult.dimension == dimension)

            # 获取分析结果
            results = query.all()

            # 按章节和维度组织结果
            organized_results = {}
            for chapter in chapters:
                chapter_dict = chapter.to_dict()
                chapter_dict.pop("content", None)  # 移除内容，减少数据量

                chapter_results = {}
                for result in results:
                    if result.chapter_id == chapter.id:
                        result_dict = result.to_dict()
                        chapter_results[result.dimension] = result_dict

                organized_results[chapter.id] = {
                    "chapter": chapter_dict,
                    "results": chapter_results
                }

            return {
                "success": True,
                "novel": novel.get_summary(),
                "chapters": organized_results
            }
        except Exception as e:
            logger.error(f"获取章节分析结果时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {"success": False, "error": str(e)}
        finally:
            session.close()

    @staticmethod
    def get_chapter_analysis_result(chapter_id: int, dimension: str) -> Dict[str, Any]:
        """
        获取单个章节的分析结果。

        Args:
            chapter_id: 章节ID
            dimension: 分析维度

        Returns:
            分析结果
        """
        session = Session()
        try:
            # 获取章节
            chapter = session.query(Chapter).filter(Chapter.id == chapter_id).first()
            if not chapter:
                logger.error(f"章节不存在: {chapter_id}")
                return {"success": False, "error": "章节不存在"}

            # 获取分析结果
            result = session.query(ChapterAnalysisResult).filter(
                ChapterAnalysisResult.chapter_id == chapter_id,
                ChapterAnalysisResult.dimension == dimension
            ).first()

            if not result:
                logger.error(f"章节 {chapter_id} 的 {dimension} 维度没有分析结果")
                return {"success": False, "error": "没有分析结果"}

            # 记录推理过程内容长度，帮助调试
            reasoning_content_length = len(result.reasoning_content) if result.reasoning_content else 0
            logger.info(f"章节 {chapter_id} 的 {dimension} 推理过程内容长度: {reasoning_content_length}字符")

            # 如果没有推理过程内容，尝试从元数据中获取
            if reasoning_content_length == 0 and result.analysis_metadata:
                try:
                    metadata = result.analysis_metadata
                    if isinstance(metadata, dict) and 'reasoning_content' in metadata:
                        result.reasoning_content = metadata['reasoning_content']
                        reasoning_content_length = len(result.reasoning_content)
                        logger.info(f"从元数据中获取到推理过程内容，长度: {reasoning_content_length}字符")
                except Exception as e:
                    logger.error(f"从元数据中获取推理过程内容时出错: {str(e)}")

            return {
                "success": True,
                "chapter": chapter.get_summary(),
                "result": result.to_dict()
            }
        except Exception as e:
            logger.error(f"获取章节分析结果时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {"success": False, "error": str(e)}
        finally:
            session.close()

    @staticmethod
    def get_chapter_analysis_result_with_reasoning(novel_id: int, chapter_id: int, dimension: str) -> Tuple[Dict[str, Any], str]:
        """
        获取单个章节的分析结果和推理过程。

        Args:
            novel_id: 小说ID
            chapter_id: 章节ID
            dimension: 分析维度

        Returns:
            分析结果和推理过程的元组 (result, reasoning_content)
        """
        session = Session()
        try:
            # 获取章节
            chapter = session.query(Chapter).filter(
                Chapter.id == chapter_id,
                Chapter.novel_id == novel_id
            ).first()

            if not chapter:
                logger.error(f"章节不存在: novel_id={novel_id}, chapter_id={chapter_id}")
                return None, None

            # 获取分析结果
            result = session.query(ChapterAnalysisResult).filter(
                ChapterAnalysisResult.novel_id == novel_id,
                ChapterAnalysisResult.chapter_id == chapter_id,
                ChapterAnalysisResult.dimension == dimension
            ).first()

            if not result:
                logger.error(f"章节 {chapter_id} 的 {dimension} 维度没有分析结果")
                return None, None

            # 获取推理过程内容
            reasoning_content = result.reasoning_content or ""

            # 记录推理过程内容长度，帮助调试
            reasoning_content_length = len(reasoning_content)
            logger.info(f"章节 {chapter_id} 的 {dimension} 推理过程内容长度: {reasoning_content_length}字符")

            # 如果没有推理过程内容，尝试从元数据中获取
            if reasoning_content_length == 0 and result.analysis_metadata:
                try:
                    metadata = result.analysis_metadata
                    if isinstance(metadata, dict) and 'reasoning_content' in metadata:
                        reasoning_content = metadata['reasoning_content']
                        reasoning_content_length = len(reasoning_content)
                        logger.info(f"从元数据中获取到推理过程内容，长度: {reasoning_content_length}字符")
                except Exception as e:
                    logger.error(f"从元数据中获取推理过程内容时出错: {str(e)}")

            # 如果仍然没有推理过程内容，尝试从分析过程中获取
            if reasoning_content_length == 0:
                try:
                    # 获取分析过程
                    processes = session.query(ChapterAnalysisProcess).filter(
                        ChapterAnalysisProcess.novel_id == novel_id,
                        ChapterAnalysisProcess.chapter_id == chapter_id,
                        ChapterAnalysisProcess.dimension == dimension,
                        ChapterAnalysisProcess.processing_stage == "reasoning"
                    ).all()

                    if processes:
                        # 使用最新的推理过程
                        latest_process = max(processes, key=lambda p: p.created_at if p.created_at else datetime.min)
                        reasoning_content = latest_process.output_text or ""
                        reasoning_content_length = len(reasoning_content)
                        logger.info(f"从分析过程中获取到推理过程内容，长度: {reasoning_content_length}字符")
                except Exception as e:
                    logger.error(f"从分析过程中获取推理过程内容时出错: {str(e)}")

            return result, reasoning_content
        except Exception as e:
            logger.error(f"获取章节分析结果和推理过程时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return None, None
        finally:
            session.close()

    @staticmethod
    def _apply_chapter_analysis_optimizations(task_id: str, optimized_config: Dict, prompt_template: str):
        """
        应用章节分析优化策略

        Args:
            task_id: 任务ID
            optimized_config: 优化配置
            prompt_template: 提示词模板
        """
        try:
            logger.info(f"[章节分析优化] 开始应用优化策略，任务ID: {task_id}")

            # 导入TestService的优化函数
            from src.services.test_service import TestService

            # 1. 配置预留实例
            if optimized_config.get("reserved_instances", False):
                TestService._configure_reserved_instances(task_id, optimized_config["worker_threads"])

            # 2. 设置异步处理
            if optimized_config.get("async_processing", False):
                async_config = {
                    "worker_threads": optimized_config["worker_threads"],
                    "instance_concurrency": optimized_config["instance_concurrency"],
                    "connection_pool_size": optimized_config["connection_pool_size"],
                    "connection_reuse": True,
                    "warmup_enabled": True
                }
                TestService._setup_async_processing(task_id, prompt_template)
                TestService._initialize_connection_pool(task_id, async_config)
                TestService._configure_instance_concurrency(task_id, async_config)

            # 3. 优化触发器规则（仅精简版）
            if optimized_config.get("trigger_optimization", False):
                TestService._optimize_trigger_rules(task_id, prompt_template)

            logger.info(f"[章节分析优化] 优化策略应用完成")

        except Exception as e:
            logger.error(f"[章节分析优化] 应用优化策略时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _analyze_chapters_in_segments(task_id: str, novel_id: int, chapters: List, dimension: str,
                                     model: str, optimized_config: Dict, analyze_chapter_worker) -> Dict:
        """
        分段分析章节（精简版专用降本增效策略）

        Args:
            task_id: 任务ID
            novel_id: 小说ID
            chapters: 章节列表
            dimension: 分析维度
            model: 模型
            optimized_config: 优化配置
            analyze_chapter_worker: 章节分析工作函数

        Returns:
            分析结果字典
        """
        try:
            import time
            import concurrent.futures

            results = {}
            segment_size = optimized_config["segment_size"]
            api_delay = optimized_config["api_delay"]

            # 将章节分成多个段落
            chapter_segments = []
            for i in range(0, len(chapters), segment_size):
                segment = chapters[i:i + segment_size]
                chapter_segments.append(segment)

            logger.info(f"[章节分段分析] 将{len(chapters)}个章节分成{len(chapter_segments)}段，每段最多{segment_size}个章节")

            # 逐段分析
            for segment_index, chapter_segment in enumerate(chapter_segments):
                segment_start_time = time.time()

                # 更新任务状态
                analysis_tasks[novel_id]["status"] = f"🔄 正在分析第{segment_index + 1}/{len(chapter_segments)}段章节（精简版分段策略）"

                logger.info(f"[章节分段分析] 开始分析第{segment_index + 1}段，包含章节: {[c.id for c in chapter_segment]}")

                # 段间延迟
                if segment_index > 0:
                    time.sleep(api_delay * 2)
                    logger.info(f"[章节分段分析] 段间延迟{api_delay * 2}秒，避免触发扩缩容")

                # 在每段内部使用小规模并行
                max_workers = min(segment_size, optimized_config["worker_threads"])
                with concurrent.futures.ThreadPoolExecutor(
                    max_workers=max_workers,
                    thread_name_prefix=f"chapter_segment_{segment_index + 1}"
                ) as executor:

                    # 创建当前段的分析任务
                    future_to_chapter = {}

                    for ch_index, chapter in enumerate(chapter_segment):
                        # 章节间延迟
                        if ch_index > 0:
                            time.sleep(api_delay)
                            logger.info(f"[章节分段分析] 章节间延迟{api_delay}秒后分析章节: {chapter.id}")

                        future = executor.submit(analyze_chapter_worker, chapter, ch_index)
                        future_to_chapter[future] = chapter

                    # 处理当前段的完成任务
                    for future in concurrent.futures.as_completed(future_to_chapter):
                        chapter = future_to_chapter[future]
                        try:
                            result = future.result()
                            if result:
                                results[chapter.id] = result
                            logger.info(f"[章节分段分析] 成功分析章节 {chapter.id}")
                        except Exception as e:
                            logger.error(f"[章节分段分析] 分析章节 {chapter.id} 时出错: {str(e)}")
                            # 添加到日志
                            analysis_logs[novel_id].append({
                                "timestamp": datetime.now().isoformat(),
                                "level": "error",
                                "message": f"分析章节 {chapter.id} 时出错: {str(e)}",
                                "dimension": dimension,
                                "chapter_id": chapter.id
                            })

                segment_end_time = time.time()
                segment_duration = segment_end_time - segment_start_time
                logger.info(f"[章节分段分析] 第{segment_index + 1}段分析完成，耗时{segment_duration:.1f}秒")

                # 段后资源清理
                ChapterAnalysisService._cleanup_chapter_segment_resources(novel_id, segment_index)

            logger.info(f"[章节分段分析] 所有{len(chapter_segments)}段分析完成，共分析{len(results)}个章节")
            return results

        except Exception as e:
            logger.error(f"[章节分段分析] 分段分析章节时出错: {str(e)}", exc_info=True)
            return {}

    @staticmethod
    def _analyze_chapters_parallel_optimized(task_id: str, novel_id: int, chapters: List, dimension: str,
                                           model: str, optimized_config: Dict, analyze_chapter_worker) -> Dict:
        """
        优化的并行分析章节（默认版使用）

        Args:
            task_id: 任务ID
            novel_id: 小说ID
            chapters: 章节列表
            dimension: 分析维度
            model: 模型
            optimized_config: 优化配置
            analyze_chapter_worker: 章节分析工作函数

        Returns:
            分析结果字典
        """
        try:
            import time
            import concurrent.futures

            results = {}
            max_workers = optimized_config["worker_threads"]
            api_delay = optimized_config["api_delay"]

            logger.info(f"[章节优化并行] 使用优化的并行处理，最大工作线程数: {max_workers}")

            # 使用优化的线程池
            with concurrent.futures.ThreadPoolExecutor(
                max_workers=max_workers,
                thread_name_prefix="chapter_analysis_optimized"
            ) as executor:

                # 提交任务（带延迟）
                future_to_chapter = {}

                for i, chapter in enumerate(chapters):
                    # 安全获取章节ID
                    chapter_id = getattr(chapter, 'id', None) if hasattr(chapter, 'id') else chapter.get('id', f'chapter_{i}') if isinstance(chapter, dict) else f'chapter_{i}'

                    # API调用延迟
                    if i > 0:
                        time.sleep(api_delay)
                        logger.info(f"[章节优化并行] API调用延迟{api_delay}秒后分析章节: {chapter_id}")

                    future = executor.submit(analyze_chapter_worker, chapter, i)
                    future_to_chapter[future] = chapter

                # 获取结果
                for future in concurrent.futures.as_completed(future_to_chapter):
                    chapter = future_to_chapter[future]
                    # 安全获取章节ID
                    chapter_id = getattr(chapter, 'id', None) if hasattr(chapter, 'id') else chapter.get('id', f'unknown_chapter') if isinstance(chapter, dict) else 'unknown_chapter'

                    try:
                        result = future.result()
                        if result:
                            results[chapter_id] = result
                        logger.info(f"[章节优化并行] 成功分析章节 {chapter_id}")
                    except Exception as e:
                        logger.error(f"[章节优化并行] 分析章节 {chapter_id} 时出错: {str(e)}")
                        # 添加到日志
                        analysis_logs[novel_id].append({
                            "timestamp": datetime.now().isoformat(),
                            "level": "error",
                            "message": f"分析章节 {chapter_id} 时出错: {str(e)}",
                            "dimension": dimension,
                            "chapter_id": chapter_id,
                            "traceback": traceback.format_exc()
                        })

            logger.info(f"[章节优化并行] 并行分析完成，共分析{len(results)}个章节")
            return results

        except Exception as e:
            logger.error(f"[章节优化并行] 优化并行分析时出错: {str(e)}", exc_info=True)
            return {}

    @staticmethod
    def _cleanup_chapter_segment_resources(novel_id: int, segment_index: int):
        """
        章节分段分析后的资源清理

        Args:
            novel_id: 小说ID
            segment_index: 段索引
        """
        try:
            import gc

            # 轻量级清理，避免影响性能
            if segment_index % 2 == 0:  # 每两段清理一次
                collected = gc.collect()
                logger.info(f"[章节段资源清理] 小说{novel_id}第{segment_index + 1}段后清理，回收{collected}个对象")

        except Exception as e:
            logger.warning(f"[章节段资源清理] 小说{novel_id}第{segment_index + 1}段资源清理时出错: {str(e)}")

    @staticmethod
    def _create_dimension_groups(dimension: str) -> Dict[str, Dict]:
        """
        创建智能维度分组，解决AI汇总处理不完全的问题

        通过将15个维度分成3组，每组采用不同的分析深度和输出控制：
        - 核心维度组：重点分析，详细输出
        - 辅助维度组：标准分析，适中输出
        - 细节维度组：精简分析，要点输出

        Args:
            dimension: 当前分析的维度

        Returns:
            维度分组配置字典
        """
        try:
            # 定义维度分组策略
            dimension_groups = {
                "core_group": {
                    "name": "核心维度组",
                    "dimensions": ["情节发展", "人物关系", "主题思想", "角色塑造", "冲突设置"],
                    "priority": "high",
                    "analysis_depth": "detailed",
                    "max_tokens": 800,
                    "description": "影响写作质量的核心维度，需要详细分析"
                },
                "auxiliary_group": {
                    "name": "辅助维度组",
                    "dimensions": ["语言风格", "写作技巧", "节奏控制", "情感表达", "伏笔铺垫"],
                    "priority": "medium",
                    "analysis_depth": "standard",
                    "max_tokens": 500,
                    "description": "提升写作技巧的辅助维度，标准分析"
                },
                "detail_group": {
                    "name": "细节维度组",
                    "dimensions": ["环境描写", "对话设计", "章纲分析", "大纲分析", "热梗统计"],
                    "priority": "low",
                    "analysis_depth": "concise",
                    "max_tokens": 300,
                    "description": "细节优化维度，精简分析要点"
                }
            }

            # 确定当前维度属于哪个组
            current_group = None
            for group_name, group_config in dimension_groups.items():
                if dimension in group_config["dimensions"]:
                    current_group = group_name
                    break

            if not current_group:
                # 如果维度不在预定义组中，归类到辅助组
                current_group = "auxiliary_group"
                dimension_groups["auxiliary_group"]["dimensions"].append(dimension)

            # 添加当前维度的具体配置
            for group_name, group_config in dimension_groups.items():
                group_config["is_current_group"] = (group_name == current_group)
                group_config["current_dimension"] = dimension if group_name == current_group else None

            logger.info(f"[维度分组] 维度'{dimension}'归类到{dimension_groups[current_group]['name']}，分析深度：{dimension_groups[current_group]['analysis_depth']}")

            return dimension_groups

        except Exception as e:
            logger.error(f"[维度分组] 创建维度分组时出错: {str(e)}", exc_info=True)
            # 返回默认配置
            return {
                "default_group": {
                    "name": "默认组",
                    "dimensions": [dimension],
                    "priority": "medium",
                    "analysis_depth": "standard",
                    "max_tokens": 500,
                    "is_current_group": True,
                    "current_dimension": dimension
                }
            }

    @staticmethod
    def _apply_output_control(analysis_content: str, dimension: str, output_control: Dict) -> str:
        """
        应用输出控制策略，温和限制输出长度但保证分析质量

        Args:
            analysis_content: 原始分析内容
            dimension: 分析维度
            output_control: 输出控制配置

        Returns:
            控制后的分析内容
        """
        try:
            if not output_control.get("enable_structured_output", False):
                return analysis_content

            # 获取维度分组
            dimension_groups = ChapterAnalysisService._create_dimension_groups(dimension)

            # 确定当前维度的token限制
            max_tokens = 500  # 默认值
            analysis_depth = "standard"

            for group_config in dimension_groups.values():
                if group_config.get("is_current_group", False):
                    max_tokens = group_config.get("max_tokens", 500)
                    analysis_depth = group_config.get("analysis_depth", "standard")
                    break

            # 估算当前内容的token数（简单估算：1个中文字符≈1.5个token）
            estimated_tokens = len(analysis_content) * 1.5

            if estimated_tokens <= max_tokens:
                # 内容长度合适，不需要压缩
                logger.info(f"[输出控制] 维度'{dimension}'内容长度合适({estimated_tokens:.0f}≤{max_tokens}tokens)，无需压缩")
                return analysis_content

            # 需要智能压缩
            compression_ratio = max_tokens / estimated_tokens
            logger.info(f"[输出控制] 维度'{dimension}'需要压缩，目标比例：{compression_ratio:.2f}")

            # 根据分析深度采用不同的压缩策略
            if analysis_depth == "detailed":
                # 详细分析：保留核心内容，适度压缩
                compressed_content = ChapterAnalysisService._compress_content_detailed(analysis_content, compression_ratio)
            elif analysis_depth == "standard":
                # 标准分析：平衡压缩
                compressed_content = ChapterAnalysisService._compress_content_standard(analysis_content, compression_ratio)
            else:  # concise
                # 精简分析：提取要点
                compressed_content = ChapterAnalysisService._compress_content_concise(analysis_content, compression_ratio)

            # 验证压缩后的长度
            final_tokens = len(compressed_content) * 1.5
            logger.info(f"[输出控制] 维度'{dimension}'压缩完成：{estimated_tokens:.0f}→{final_tokens:.0f}tokens，压缩率{(1-final_tokens/estimated_tokens)*100:.1f}%")

            return compressed_content

        except Exception as e:
            logger.error(f"[输出控制] 应用输出控制时出错: {str(e)}", exc_info=True)
            return analysis_content

    @staticmethod
    def _compress_content_detailed(content: str, ratio: float) -> str:
        """
        详细分析的内容压缩（保留核心内容）

        Args:
            content: 原始内容
            ratio: 压缩比例

        Returns:
            压缩后的内容
        """
        try:
            # 分段处理
            paragraphs = content.split('\n')

            # 保留重要段落（包含关键词的段落）
            important_keywords = ['分析', '特点', '技巧', '效果', '建议', '总结', '关键', '核心', '重要']
            important_paragraphs = []
            other_paragraphs = []

            for para in paragraphs:
                if any(keyword in para for keyword in important_keywords) or len(para) > 50:
                    important_paragraphs.append(para)
                else:
                    other_paragraphs.append(para)

            # 保留所有重要段落，适度压缩其他段落
            target_other_count = max(1, int(len(other_paragraphs) * ratio))
            selected_other = other_paragraphs[:target_other_count]

            result = important_paragraphs + selected_other
            return '\n'.join(result)

        except Exception as e:
            logger.error(f"[内容压缩] 详细压缩时出错: {str(e)}")
            return content[:int(len(content) * ratio)]

    @staticmethod
    def _compress_content_standard(content: str, ratio: float) -> str:
        """
        标准分析的内容压缩（平衡压缩）

        Args:
            content: 原始内容
            ratio: 压缩比例

        Returns:
            压缩后的内容
        """
        try:
            # 按句子分割
            sentences = content.replace('。', '。\n').replace('！', '！\n').replace('？', '？\n').split('\n')
            sentences = [s.strip() for s in sentences if s.strip()]

            # 计算保留的句子数量
            target_count = max(3, int(len(sentences) * ratio))

            # 优先保留包含关键信息的句子
            scored_sentences = []
            for i, sentence in enumerate(sentences):
                score = 0
                # 位置权重（开头和结尾的句子更重要）
                if i < len(sentences) * 0.3 or i > len(sentences) * 0.7:
                    score += 2
                # 长度权重（适中长度的句子更重要）
                if 20 <= len(sentence) <= 100:
                    score += 1
                # 关键词权重
                keywords = ['分析', '特点', '效果', '建议', '重要', '关键']
                score += sum(1 for keyword in keywords if keyword in sentence)

                scored_sentences.append((score, i, sentence))

            # 按分数排序，选择top句子
            scored_sentences.sort(key=lambda x: x[0], reverse=True)
            selected_sentences = sorted(scored_sentences[:target_count], key=lambda x: x[1])

            result = [sentence[2] for sentence in selected_sentences]
            return '。'.join(result) + '。'

        except Exception as e:
            logger.error(f"[内容压缩] 标准压缩时出错: {str(e)}")
            return content[:int(len(content) * ratio)]

    @staticmethod
    def _compress_content_concise(content: str, ratio: float) -> str:
        """
        精简分析的内容压缩（提取要点）

        Args:
            content: 原始内容
            ratio: 压缩比例

        Returns:
            压缩后的内容
        """
        try:
            # 提取关键要点
            sentences = content.replace('。', '。\n').split('\n')
            sentences = [s.strip() for s in sentences if s.strip()]

            # 寻找要点句（通常包含数字、关键词等）
            key_points = []
            for sentence in sentences:
                # 包含要点标识的句子
                if any(marker in sentence for marker in ['1.', '2.', '3.', '首先', '其次', '最后', '总之', '综上']):
                    key_points.append(sentence)
                # 包含重要关键词的短句
                elif any(keyword in sentence for keyword in ['特点', '效果', '建议', '关键']) and len(sentence) <= 50:
                    key_points.append(sentence)

            # 如果要点不够，补充重要句子
            if len(key_points) < 3:
                important_sentences = [s for s in sentences if 20 <= len(s) <= 80]
                key_points.extend(important_sentences[:3-len(key_points)])

            # 限制要点数量
            max_points = max(2, int(len(sentences) * ratio))
            result = key_points[:max_points]

            return '。'.join(result) + '。'

        except Exception as e:
            logger.error(f"[内容压缩] 精简压缩时出错: {str(e)}")
            return content[:int(len(content) * ratio)]