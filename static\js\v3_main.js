/**
 * 九猫小说分析写作系统v3.0 - 全局JavaScript函数
 * 
 * 这个文件包含了系统中所有页面共用的JavaScript函数
 */

// 在DOM加载完成后执行
$(document).ready(function() {
    console.log('九猫小说分析写作系统v3.0 - 全局JavaScript已加载');

    // 修复所有按钮的点击事件
    fixAllButtons();

    // 初始化工具提示
    initTooltips();

    // 初始化Markdown渲染
    initMarkdownRendering();

    // 初始化导航栏高亮
    highlightCurrentNavItem();
});

/**
 * 修复所有按钮的点击事件
 */
function fixAllButtons() {
    // 修复所有带有href属性的按钮
    $('a.btn').each(function() {
        const $btn = $(this);
        const href = $btn.attr('href');
        
        // 如果按钮有href属性但没有点击事件，添加默认点击事件
        if (href && href !== '#' && !$btn.data('has-click-handler')) {
            $btn.data('has-click-handler', true);
            
            // 记录按钮点击
            $btn.on('click', function() {
                console.log('按钮点击: ' + href);
            });
        }
    });

    // 修复所有返回按钮
    $('.btn:contains("返回")').each(function() {
        const $btn = $(this);
        
        // 如果按钮没有href属性或href为#，添加返回上一页的点击事件
        if (!$btn.attr('href') || $btn.attr('href') === '#') {
            $btn.on('click', function(e) {
                e.preventDefault();
                console.log('返回按钮点击，返回上一页');
                window.history.back();
            });
        }
    });

    // 修复所有模态框按钮
    $('[data-bs-toggle="modal"]').each(function() {
        const $btn = $(this);
        const targetModal = $btn.data('bs-target');
        
        // 确保模态框存在
        if ($(targetModal).length > 0) {
            // 记录模态框打开
            $btn.on('click', function() {
                console.log('打开模态框: ' + targetModal);
            });
        }
    });
}

/**
 * 初始化工具提示
 */
function initTooltips() {
    // 初始化所有带有data-bs-toggle="tooltip"属性的元素
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化Markdown渲染
 */
function initMarkdownRendering() {
    // 渲染所有带有data-markdown属性的元素
    $('[data-markdown]').each(function() {
        const $element = $(this);
        const markdown = $element.text();
        
        // 使用marked库渲染Markdown
        if (typeof marked !== 'undefined') {
            $element.html(marked.parse(markdown));
        }
    });
}

/**
 * 高亮当前导航项
 */
function highlightCurrentNavItem() {
    // 获取当前页面的URL路径
    const currentPath = window.location.pathname;
    
    // 遍历导航栏中的所有链接
    $('.navbar-nav .nav-link').each(function() {
        const $link = $(this);
        const href = $link.attr('href');
        
        // 如果链接的href与当前路径匹配，添加active类
        if (href === currentPath || (href !== '/' && currentPath.startsWith(href))) {
            $link.addClass('active');
        }
    });
}

/**
 * 显示加载提示
 * @param {string} message - 显示的消息
 * @returns {void}
 */
function showLoading(message = '正在处理，请稍候...') {
    // 如果已经存在加载提示，先移除
    $('#loadingOverlay').remove();
    
    // 创建加载提示HTML
    const loadingHtml = `
        <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
            <div class="card p-4 text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>${message}</h5>
            </div>
        </div>
    `;
    
    // 添加到body
    $('body').append(loadingHtml);
}

/**
 * 隐藏加载提示
 * @returns {void}
 */
function hideLoading() {
    $('#loadingOverlay').remove();
}

/**
 * 显示提示消息
 * @param {string} message - 显示的消息
 * @param {string} type - 消息类型 (success, info, warning, danger)
 * @param {number} duration - 显示时间 (毫秒)
 * @returns {void}
 */
function showAlert(message, type = 'info', duration = 3000) {
    // 创建提示消息HTML
    const alertId = 'alert-' + Date.now();
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;" id="${alertId}">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // 添加到body
    $('body').append(alertHtml);
    
    // 设置定时器，自动关闭提示消息
    setTimeout(function() {
        $('#' + alertId).alert('close');
    }, duration);
}
