/**
 * 九猫 - 系统监控页面修复脚本
 * 解决系统监控页面加载错误和资源超时问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('系统监控页面修复脚本已加载 v1.0.0');
    
    // 资源加载超时时间（毫秒）
    const RESOURCE_TIMEOUT = 10000; // 增加到10秒
    
    // 重试次数
    const MAX_RETRIES = 3;
    
    // 已加载的资源
    const loadedResources = new Set();
    
    // 资源加载重试计数
    const resourceRetries = {};
    
    // 修复资源加载超时问题
    function fixResourceTimeouts() {
        // 监听所有资源加载错误
        window.addEventListener('error', function(event) {
            const target = event.target;
            
            // 检查是否是资源加载错误
            if (target && (target.tagName === 'SCRIPT' || target.tagName === 'LINK' || target.tagName === 'IMG')) {
                const url = target.src || target.href;
                
                if (!url) return;
                
                console.warn(`资源加载失败: ${url}`);
                
                // 检查是否已经重试过
                if (!resourceRetries[url]) {
                    resourceRetries[url] = 0;
                }
                
                // 如果重试次数未超过最大值，尝试重新加载
                if (resourceRetries[url] < MAX_RETRIES) {
                    resourceRetries[url]++;
                    console.log(`尝试重新加载资源 (${resourceRetries[url]}/${MAX_RETRIES}): ${url}`);
                    
                    // 创建新的元素替换旧的
                    const newElement = document.createElement(target.tagName);
                    
                    // 复制所有属性
                    for (let i = 0; i < target.attributes.length; i++) {
                        const attr = target.attributes[i];
                        newElement.setAttribute(attr.name, attr.value);
                    }
                    
                    // 设置更长的超时时间
                    if (target.tagName === 'SCRIPT') {
                        newElement.async = false; // 确保按顺序加载
                    }
                    
                    // 替换元素
                    if (target.parentNode) {
                        target.parentNode.replaceChild(newElement, target);
                    }
                    
                    // 阻止错误传播
                    event.preventDefault();
                    return false;
                }
            }
        }, true);
        
        // 修复 TDX resource 警告
        const originalConsoleWarn = console.warn;
        console.warn = function() {
            // 检查是否是 TDX resource 警告
            if (arguments[0] && typeof arguments[0] === 'string' && 
                arguments[0].includes('TDX resource')) {
                // 忽略这些警告
                return;
            }
            
            // 对于其他警告，使用原始方法
            return originalConsoleWarn.apply(console, arguments);
        };
    }
    
    // 修复系统监控页面加载
    function fixSystemMonitorPage() {
        // 检查是否在系统监控页面
        if (window.location.pathname.includes('/system-monitor')) {
            console.log('检测到系统监控页面，应用修复');
            
            // 添加页面加载完成处理
            window.addEventListener('load', function() {
                console.log('系统监控页面加载完成，检查错误');
                
                // 检查是否显示错误消息
                const errorElements = document.querySelectorAll('h1, h2, h3, h4, h5');
                let hasError = false;
                
                for (const el of errorElements) {
                    if (el.textContent.includes('发生错误') || el.textContent.includes('遇到了问题')) {
                        hasError = true;
                        break;
                    }
                }
                
                if (hasError) {
                    console.log('检测到错误消息，尝试重新加载数据');
                    
                    // 尝试重新加载系统监控数据
                    fetch('/api/system/status')
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('成功获取系统状态数据:', data);
                            
                            // 尝试更新页面内容
                            updateSystemMonitorUI(data);
                        })
                        .catch(error => {
                            console.error('获取系统状态数据失败:', error);
                            
                            // 显示友好的错误消息
                            showFriendlyErrorMessage();
                        });
                }
            });
        }
    }
    
    // 更新系统监控UI
    function updateSystemMonitorUI(data) {
        // 创建一个新的内容容器
        const container = document.createElement('div');
        container.className = 'container mt-4';
        
        // 创建标题
        const title = document.createElement('h1');
        title.textContent = '系统监控';
        container.appendChild(title);
        
        // 创建系统状态卡片
        const statusCard = document.createElement('div');
        statusCard.className = 'card mb-4';
        
        const statusCardHeader = document.createElement('div');
        statusCardHeader.className = 'card-header';
        statusCardHeader.innerHTML = '<h5 class="card-title">系统状态</h5>';
        statusCard.appendChild(statusCardHeader);
        
        const statusCardBody = document.createElement('div');
        statusCardBody.className = 'card-body';
        
        // 添加系统状态信息
        if (data.system) {
            const systemInfo = document.createElement('div');
            systemInfo.innerHTML = `
                <p><strong>CPU使用率:</strong> ${data.system.cpu_percent}%</p>
                <p><strong>内存使用率:</strong> ${data.system.memory_percent}%</p>
                <p><strong>已用内存:</strong> ${data.system.memory_used} MB</p>
                <p><strong>总内存:</strong> ${data.system.memory_total} MB</p>
                <p><strong>系统启动时间:</strong> ${data.system.boot_time}</p>
            `;
            statusCardBody.appendChild(systemInfo);
        } else {
            statusCardBody.innerHTML = '<p>无法获取系统状态信息</p>';
        }
        
        statusCard.appendChild(statusCardBody);
        container.appendChild(statusCard);
        
        // 创建进程信息卡片
        if (data.processes && data.processes.length > 0) {
            const processCard = document.createElement('div');
            processCard.className = 'card mb-4';
            
            const processCardHeader = document.createElement('div');
            processCardHeader.className = 'card-header';
            processCardHeader.innerHTML = '<h5 class="card-title">进程信息</h5>';
            processCard.appendChild(processCardHeader);
            
            const processCardBody = document.createElement('div');
            processCardBody.className = 'card-body';
            
            // 创建进程表格
            const table = document.createElement('table');
            table.className = 'table table-striped';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>PID</th>
                        <th>名称</th>
                        <th>CPU使用率</th>
                        <th>内存使用率</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody id="process-table-body">
                </tbody>
            `;
            
            processCardBody.appendChild(table);
            
            // 添加进程数据
            const tableBody = table.querySelector('#process-table-body');
            data.processes.forEach(process => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${process.pid}</td>
                    <td>${process.name}</td>
                    <td>${process.cpu_percent}%</td>
                    <td>${process.memory_percent}%</td>
                    <td>${process.status}</td>
                `;
                tableBody.appendChild(row);
            });
            
            processCard.appendChild(processCardBody);
            container.appendChild(processCard);
        }
        
        // 创建刷新按钮
        const refreshButton = document.createElement('button');
        refreshButton.className = 'btn btn-primary';
        refreshButton.textContent = '刷新数据';
        refreshButton.onclick = function() {
            location.reload();
        };
        container.appendChild(refreshButton);
        
        // 替换页面内容
        const mainContent = document.querySelector('main') || document.querySelector('.container') || document.body;
        mainContent.innerHTML = '';
        mainContent.appendChild(container);
    }
    
    // 显示友好的错误消息
    function showFriendlyErrorMessage() {
        // 创建一个新的内容容器
        const container = document.createElement('div');
        container.className = 'container mt-4';
        
        // 创建错误消息
        container.innerHTML = `
            <div class="alert alert-warning">
                <h4>系统监控暂时不可用</h4>
                <p>系统监控服务可能正在维护或暂时无法访问。请稍后再试。</p>
                <p>您可以尝试以下操作：</p>
                <ul>
                    <li>刷新页面</li>
                    <li>检查系统服务是否正常运行</li>
                    <li>确认数据库连接是否正常</li>
                </ul>
                <button class="btn btn-primary mt-3" onclick="location.reload()">刷新页面</button>
                <a href="/" class="btn btn-secondary mt-3 ml-2">返回首页</a>
            </div>
        `;
        
        // 替换页面内容
        const mainContent = document.querySelector('main') || document.querySelector('.container') || document.body;
        mainContent.innerHTML = '';
        mainContent.appendChild(container);
    }
    
    // 应用所有修复
    function applyAllFixes() {
        // 修复资源加载超时问题
        fixResourceTimeouts();
        
        // 修复系统监控页面
        fixSystemMonitorPage();
    }
    
    // 在页面加载完成后应用修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyAllFixes);
    } else {
        applyAllFixes();
    }
})();
