"""
测试脚本，用于验证九猫小说分析系统的基本功能。
"""
import os
import logging
from datetime import datetime

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.models.base import Base
from src.models.novel import Novel
from src.api.deepseek_client import DeepSeekClient
from src.api.analysis import NovelAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database():
    """测试数据库连接和模型"""
    logger.info("测试数据库连接和模型...")
    
    # 创建内存数据库
    engine = create_engine('sqlite:///:memory:')
    Base.metadata.create_all(engine)
    
    # 创建会话
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # 创建测试小说
    novel = Novel(
        title="测试小说",
        content="这是一个测试小说的内容。这只是一个简短的示例。",
        author="测试作者"
    )
    
    # 添加到数据库
    session.add(novel)
    session.commit()
    
    # 验证是否成功添加
    retrieved_novel = session.query(Novel).filter_by(title="测试小说").first()
    
    if retrieved_novel and retrieved_novel.title == "测试小说":
        logger.info("数据库测试成功！")
        return True
    else:
        logger.error("数据库测试失败！")
        return False

def test_deepseek_api():
    """测试DeepSeek API连接"""
    logger.info("测试DeepSeek API连接...")
    
    client = DeepSeekClient()
    
    try:
        # 简单的API测试
        result = client.analyze_text("这是一个测试文本。", "language_style", max_tokens=100)
        
        if "error" in result:
            logger.error(f"API测试失败: {result['error']}")
            return False
        
        logger.info("DeepSeek API测试成功！")
        logger.info(f"API返回内容: {result['content'][:100]}...")
        return True
    except Exception as e:
        logger.error(f"API测试出错: {str(e)}")
        return False

def run_tests():
    """运行所有测试"""
    logger.info("开始测试九猫小说分析系统...")
    
    # 测试数据库
    db_result = test_database()
    
    # 测试API
    api_result = test_deepseek_api()
    
    # 输出总结果
    if db_result and api_result:
        logger.info("所有测试通过！系统可以正常运行。")
    else:
        logger.warning("部分测试失败。请检查日志了解详情。")

if __name__ == "__main__":
    run_tests()
