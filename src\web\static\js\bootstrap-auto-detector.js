/**
 * 九猫系统 Bootstrap 自动检测器
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于在页面加载时自动检测和修复 bootstrap.bundle.min.js 的问题
 */

(function() {
    console.log('[Bootstrap 自动检测器] 初始化...');
    
    // 全局变量
    let checkInterval = null;
    let checkCount = 0;
    const MAX_CHECK_COUNT = 5;
    
    // 检查 Bootstrap 是否正常工作
    function checkBootstrap() {
        console.log('[Bootstrap 自动检测器] 检查 Bootstrap...');
        
        // 增加检查计数
        checkCount++;
        
        // 检查是否已经达到最大检查次数
        if (checkCount > MAX_CHECK_COUNT) {
            console.log('[Bootstrap 自动检测器] 已达到最大检查次数，停止检查');
            stopChecking();
            return;
        }
        
        // 检查 bootstrap 对象是否存在
        if (typeof bootstrap === 'undefined') {
            console.error('[Bootstrap 自动检测器] bootstrap 对象不存在，尝试修复');
            fixBootstrap();
            return;
        }
        
        // 检查 bootstrap.Modal 是否存在
        if (typeof bootstrap.Modal === 'undefined') {
            console.error('[Bootstrap 自动检测器] bootstrap.Modal 不存在，尝试修复');
            fixBootstrap();
            return;
        }
        
        // 尝试创建一个 Modal 实例
        try {
            const modalElement = document.createElement('div');
            modalElement.className = 'modal';
            document.body.appendChild(modalElement);
            
            const modal = new bootstrap.Modal(modalElement);
            modal.dispose();
            document.body.removeChild(modalElement);
            
            console.log('[Bootstrap 自动检测器] Bootstrap 正常工作');
            stopChecking();
        } catch (e) {
            console.error('[Bootstrap 自动检测器] 创建 Modal 实例失败:', e);
            fixBootstrap();
        }
    }
    
    // 修复 Bootstrap
    function fixBootstrap() {
        console.log('[Bootstrap 自动检测器] 尝试修复 Bootstrap...');
        
        // 检查是否有修复函数
        if (typeof window.fixBootstrapBundle === 'function') {
            console.log('[Bootstrap 自动检测器] 调用 Bootstrap 修复函数');
            window.fixBootstrapBundle();
        } else {
            console.log('[Bootstrap 自动检测器] 没有找到 Bootstrap 修复函数，尝试加载修复脚本');
            
            // 加载修复脚本
            const script = document.createElement('script');
            script.src = '/static/js/bootstrap-bundle-fix-loader.js';
            
            script.onload = function() {
                console.log('[Bootstrap 自动检测器] Bootstrap 修复脚本加载成功');
                
                // 检查是否有修复函数
                if (typeof window.fixBootstrapBundle === 'function') {
                    console.log('[Bootstrap 自动检测器] 调用 Bootstrap 修复函数');
                    window.fixBootstrapBundle();
                }
            };
            
            script.onerror = function() {
                console.error('[Bootstrap 自动检测器] Bootstrap 修复脚本加载失败');
                
                // 直接加载CDN版本
                const cdnScript = document.createElement('script');
                cdnScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
                cdnScript.integrity = 'sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz';
                cdnScript.crossOrigin = 'anonymous';
                document.head.appendChild(cdnScript);
            };
            
            document.head.appendChild(script);
        }
    }
    
    // 开始检查
    function startChecking() {
        console.log('[Bootstrap 自动检测器] 开始检查...');
        
        // 如果已经在检查，先停止
        stopChecking();
        
        // 重置检查计数
        checkCount = 0;
        
        // 立即检查一次
        checkBootstrap();
        
        // 设置定时检查
        checkInterval = setInterval(checkBootstrap, 2000);
    }
    
    // 停止检查
    function stopChecking() {
        if (checkInterval) {
            clearInterval(checkInterval);
            checkInterval = null;
        }
    }
    
    // 初始化
    function initialize() {
        console.log('[Bootstrap 自动检测器] 初始化...');
        
        // 检查页面是否已加载
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('[Bootstrap 自动检测器] 页面已加载，开始检查');
            setTimeout(startChecking, 1000);
        } else {
            console.log('[Bootstrap 自动检测器] 页面尚未加载，等待 DOMContentLoaded 事件');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('[Bootstrap 自动检测器] DOMContentLoaded 事件触发，开始检查');
                setTimeout(startChecking, 1000);
            });
        }
    }
    
    // 导出全局函数
    window.checkBootstrap = checkBootstrap;
    window.fixBootstrap = fixBootstrap;
    window.startBootstrapChecking = startChecking;
    window.stopBootstrapChecking = stopChecking;
    
    // 执行初始化
    initialize();
    
    console.log('[Bootstrap 自动检测器] 初始化完成');
})();
