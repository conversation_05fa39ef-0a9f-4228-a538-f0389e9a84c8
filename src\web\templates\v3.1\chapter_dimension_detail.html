{% extends "v3.1/base.html" %}

{% block title %}{{ dimension_info.name }} - {{ chapter.title }} - {{ novel.title }} - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .dimension-header {
        background: linear-gradient(135deg, #FFF8E1, #FFFCF5);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
        border: 1px solid #E6D7B9;
    }

    .dimension-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;
    }

    .dimension-header h1 {
        position: relative;
        z-index: 1;
        font-weight: 700;
        color: #5D4037;
    }

    .dimension-header p {
        position: relative;
        z-index: 1;
        color: #5D4037;
    }

    .dimension-icon-large {
        font-size: 3rem;
        color: #FF8F00;
        margin-bottom: 1rem;
    }

    .analysis-card {
        border: 1px solid #E6D7B9;
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .analysis-card .card-header {
        background-color: #FFF8E1;
        border-bottom: 1px solid #E6D7B9;
        padding: 1rem;
    }

    .analysis-content {
        padding: 1.5rem;
    }

    .reasoning-content {
        padding: 1.5rem;
        background-color: #FFFCF5;
        border-top: 1px solid #E6D7B9;
        max-height: 500px;
        overflow-y: auto;
    }

    .btn-primary {
        background-color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-primary:hover {
        background-color: #F57C00;
        border-color: #F57C00;
    }

    .btn-outline-primary {
        color: #FF8F00;
        border-color: #FF8F00;
    }

    .btn-outline-primary:hover {
        background-color: #FF8F00;
        border-color: #FF8F00;
        color: white;
    }

    .badge.bg-primary {
        background-color: #FF8F00 !important;
    }

    .badge.bg-secondary {
        background-color: #757575 !important;
    }

    .badge.bg-success {
        background-color: #43A047 !important;
    }

    .badge.bg-warning {
        background-color: #FFB300 !important;
    }

    .badge.bg-info {
        background-color: #29B6F6 !important;
    }

    .analysis-meta {
        background-color: #FFFFFF;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #E6D7B9;
    }

    .analysis-meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .analysis-meta-item i {
        width: 24px;
        margin-right: 0.5rem;
        color: #FF8F00;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 1.5rem;
    }

    .toggle-reasoning-btn {
        cursor: pointer;
        padding: 0.5rem 1rem;
        background-color: #FFF8E1;
        border: 1px solid #E6D7B9;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .toggle-reasoning-btn:hover {
        background-color: #FFE0B2;
    }

    .reasoning-section {
        display: none;
    }

    .reasoning-section.show {
        display: block;
    }

    .reasoning-content {
        white-space: pre-wrap;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        line-height: 1.8;
    }

    .analysis-result {
        white-space: pre-wrap;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        line-height: 1.8;
    }

    .chapter-navigation {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 维度头部信息 -->
<div class="dimension-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 mb-3">{{ dimension_info.name }}</h1>
            <p class="lead mb-2">
                <i class="fas fa-book me-2"></i>{{ novel.title }} - {{ chapter.title }}
                {% if novel.author %}
                <span class="ms-2"><i class="fas fa-user me-1"></i>{{ novel.author }}</span>
                {% endif %}
            </p>
            <p class="mb-0">
                <span class="badge bg-primary me-2">第{{ chapter.chapter_number }}章</span>
                <span class="badge bg-info me-2">{{ chapter.word_count }} 字</span>
                <span class="badge bg-success me-2">{{ chapter.analyzed_dimensions|default([])|length }}/15 维度已分析</span>
            </p>
        </div>
        <div class="col-md-4 text-end d-none d-md-block">
            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('v3_1.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回章节
                </a>
                <button id="reanalyzeBtn" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-1"></i>重新分析
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 章节导航 -->
<div class="chapter-navigation mb-4">
    {% if prev_chapter %}
    <a href="{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=prev_chapter.id, dimension=dimension) }}" class="btn btn-outline-secondary">
        <i class="fas fa-chevron-left me-1"></i>上一章：{{ prev_chapter.title }}
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        <i class="fas fa-chevron-left me-1"></i>已是第一章
    </button>
    {% endif %}

    {% if next_chapter %}
    <a href="{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=next_chapter.id, dimension=dimension) }}" class="btn btn-outline-secondary">
        下一章：{{ next_chapter.title }}<i class="fas fa-chevron-right ms-1"></i>
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        已是最后一章<i class="fas fa-chevron-right ms-1"></i>
    </button>
    {% endif %}
</div>

<!-- 主要内容 -->
<div class="row">
    <div class="col-md-4 mb-4">
        <!-- 维度信息 -->
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>维度信息</h3>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <i class="{{ dimension_info.icon }} dimension-icon-large"></i>
                    <h3>{{ dimension_info.name }}</h3>
                </div>

                <div class="analysis-meta">
                    <div class="analysis-meta-item">
                        <i class="fas fa-book"></i>
                        <span>小说: {{ novel.title }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-bookmark"></i>
                        <span>章节: {{ chapter.title }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-chart-pie"></i>
                        <span>维度: {{ dimension_info.name }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-font"></i>
                        <span>字数: {{ chapter.word_count }}</span>
                    </div>
                    <div class="analysis-meta-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>分析时间: {{ analysis.created_at.strftime('%Y-%m-%d %H:%M') if analysis.created_at is not string else analysis.created_at }}</span>
                    </div>
                </div>

                <p class="card-text">{{ dimension_info.description }}</p>

                <div class="d-grid gap-2 mt-3">
                    <a href="{{ url_for('v3_1.view_templates', novel_id=novel.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-cog me-1"></i>查看设定模板
                    </a>
                </div>
            </div>
        </div>

        <!-- 其他维度 -->
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>其他维度</h3>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for dim in dimensions %}
                        {% if dim.key != dimension %}
                            <a href="{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=chapter.id, dimension=dim.key) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="{{ dim.icon }} me-2"></i>{{ dim.name }}</span>
                                {% if dim.key in chapter.analyzed_dimensions|default([]) %}
                                <span class="badge bg-success rounded-pill">已分析</span>
                                {% else %}
                                <span class="badge bg-secondary rounded-pill">未分析</span>
                                {% endif %}
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- 分析结果 -->
        <div class="analysis-card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i>分析结果</h3>
                <button class="btn btn-sm btn-outline-primary" id="copyResultBtn">
                    <i class="fas fa-copy me-1"></i>复制结果
                </button>
            </div>
            <div class="analysis-content">
                {% if analysis and analysis.result %}
                    <div class="analysis-result">{{ analysis.result }}</div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h4>暂无分析结果</h4>
                        <p class="text-muted">该维度尚未分析或分析结果不可用。</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 分析过程 -->
        <div class="analysis-card shadow-sm">
            <div class="toggle-reasoning-btn" id="toggleReasoningBtn">
                <h3 class="card-title mb-0"><i class="fas fa-brain me-2"></i>分析过程</h3>
                <i class="fas fa-chevron-down" id="reasoningIcon"></i>
            </div>
            <div class="reasoning-section" id="reasoningSection">
                <div class="reasoning-content">
                    {% if analysis and analysis.reasoning_content %}
                        {{ analysis.reasoning_content }}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-brain fa-3x text-muted mb-3"></i>
                            <h4>暂无分析过程</h4>
                            <p class="text-muted">该维度的分析过程不可用。</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 可视化图表 -->
        {% if dimension == 'rhythm_pacing' or dimension == 'climax_pacing' %}
        <div class="analysis-card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-chart-area me-2"></i>节奏曲线</h3>
            </div>
            <div class="analysis-content">
                <div class="chart-container">
                    <canvas id="pacingChart"></canvas>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 章节导航（底部） -->
<div class="chapter-navigation mt-4">
    {% if prev_chapter %}
    <a href="{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=prev_chapter.id, dimension=dimension) }}" class="btn btn-outline-secondary">
        <i class="fas fa-chevron-left me-1"></i>上一章：{{ prev_chapter.title }}
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        <i class="fas fa-chevron-left me-1"></i>已是第一章
    </button>
    {% endif %}

    {% if next_chapter %}
    <a href="{{ url_for('v3_1.chapter_dimension_detail', novel_id=novel.id, chapter_id=next_chapter.id, dimension=dimension) }}" class="btn btn-outline-secondary">
        下一章：{{ next_chapter.title }}<i class="fas fa-chevron-right ms-1"></i>
    </a>
    {% else %}
    <button class="btn btn-outline-secondary" disabled>
        已是最后一章<i class="fas fa-chevron-right ms-1"></i>
    </button>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // 切换分析过程显示/隐藏
        $('#toggleReasoningBtn').click(function() {
            $('#reasoningSection').toggleClass('show');
            const icon = $('#reasoningIcon');
            if (icon.hasClass('fa-chevron-down')) {
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            } else {
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            }
        });

        // 复制分析结果
        $('#copyResultBtn').click(function() {
            const resultText = $('.analysis-result').text();
            copyToClipboard(resultText);

            // 显示复制成功提示
            const originalText = $(this).html();
            $(this).html('<i class="fas fa-check me-1"></i>已复制');

            // 2秒后恢复原始文本
            setTimeout(() => {
                $(this).html(originalText);
            }, 2000);
        });

        // 重新分析按钮点击事件
        $('#reanalyzeBtn').click(function() {
            if (confirm('确定要重新分析"{{ dimension_info.name }}"维度吗？\n\n重新分析将覆盖现有结果，分析过程可能需要几分钟时间，请耐心等待。')) {
                // 显示加载状态
                const originalText = $(this).html();
                $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...');
                $(this).prop('disabled', true);

                // 跳转到分析页面
                window.location.href = "{{ url_for('v3_1.analyze_chapter_dimension', novel_id=novel.id, chapter_id=chapter.id, dimension=dimension, force=1) }}";
            }
        });

        // 复制到剪贴板函数
        function copyToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }

        {% if dimension == 'rhythm_pacing' or dimension == 'climax_pacing' %}
        // 节奏曲线图表
        const pacingCtx = document.getElementById('pacingChart').getContext('2d');
        const pacingChart = new Chart(pacingCtx, {
            type: 'line',
            data: {
                labels: ['开始', '25%', '50%', '75%', '结束'],
                datasets: [{
                    label: '节奏强度',
                    data: [30, 45, 60, 85, 70],
                    borderColor: '#FF8F00',
                    backgroundColor: 'rgba(255, 143, 0, 0.1)',
                    borderWidth: 3,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: '强度'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '章节进程'
                        }
                    }
                }
            }
        });
        {% endif %}

        // 键盘导航
        $(document).keydown(function(e) {
            // 左箭头键 - 上一章
            if (e.keyCode === 37) {
                const prevChapterLink = $('a:contains("上一章")').first();
                if (prevChapterLink.length && !prevChapterLink.hasClass('disabled')) {
                    window.location.href = prevChapterLink.attr('href');
                }
            }
            // 右箭头键 - 下一章
            else if (e.keyCode === 39) {
                const nextChapterLink = $('a:contains("下一章")').first();
                if (nextChapterLink.length && !nextChapterLink.hasClass('disabled')) {
                    window.location.href = nextChapterLink.attr('href');
                }
            }
        });
    });
</script>
{% endblock %}