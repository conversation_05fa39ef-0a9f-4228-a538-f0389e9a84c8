{% extends "v3.1/base.html" %}

{% block title %}系统监控 - 九猫小说分析写作系统v3.1{% endblock %}

{% block extra_css %}
<style>
    .monitor-card {
        transition: all 0.3s ease;
    }
    .monitor-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .progress {
        height: 1rem;
    }
    .chart-container {
        height: 300px;
    }
    .log-container {
        height: 400px;
        overflow-y: auto;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 1rem;
        font-family: 'Courier New', Courier, monospace;
    }
    .log-entry {
        margin-bottom: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e9ecef;
    }
    .log-info {
        color: #0d6efd;
    }
    .log-warning {
        color: #ffc107;
    }
    .log-error {
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h1 class="mb-3"><i class="fas fa-tachometer-alt text-primary me-2"></i>系统监控</h1>
                <p class="lead">系统监控页面提供实时的系统状态和性能数据，帮助您了解系统的运行情况。</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong> 系统监控数据每60秒自动刷新一次。您也可以点击右上角的刷新按钮手动刷新。
                </div>
                <div class="text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class="me-3 text-muted">
                            <small>最后更新: {{ monitor_data.last_updated|default('未知') }}</small>
                        </div>
                        <button id="refreshBtn" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i>刷新数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统状态卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-4">
        <div class="card h-100 monitor-card shadow-sm">
            <div class="card-body">
                <h5 class="card-title text-primary">
                    <i class="fas fa-clock me-2"></i>系统运行时间
                </h5>
                <h2 class="display-5 mb-0">{{ generated_content_stats.total_hours|default('0') }} 小时</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100 monitor-card shadow-sm">
            <div class="card-body">
                <h5 class="card-title text-success">
                    <i class="fas fa-server me-2"></i>API调用次数
                </h5>
                <h2 class="display-5 mb-0">{{ monitor_data.api_calls|default('0') }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100 monitor-card shadow-sm">
            <div class="card-body">
                <h5 class="card-title text-info">
                    <i class="fas fa-book me-2"></i>小说总数
                </h5>
                <h2 class="display-5 mb-0">{{ monitor_data.novel_count|default('0') }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100 monitor-card shadow-sm">
            <div class="card-body">
                <h5 class="card-title text-warning">
                    <i class="fas fa-file-alt me-2"></i>章节总数
                </h5>
                <h2 class="display-5 mb-0">{{ generated_content_stats.total_chapters|default('0') }}</h2>
            </div>
        </div>
    </div>
</div>

<!-- 系统资源使用情况 -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header">
                <h3 class="card-title mb-0">系统资源使用趋势</h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="resourceChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header">
                <h3 class="card-title mb-0">API成本统计</h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="apiCostChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // 资源使用趋势图表
        const resourceCtx = document.getElementById('resourceChart').getContext('2d');
        const resourceChart = new Chart(resourceCtx, {
            type: 'line',
            data: {
                labels: ['10:00', '10:10', '10:20', '10:30', '10:40', '10:50', '11:00'],
                datasets: [{
                    label: 'CPU使用率',
                    data: [20, 25, 30, 35, 30, 25, 25],
                    borderColor: 'rgba(13, 110, 253, 1)',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '内存使用率',
                    data: [30, 35, 40, 45, 40, 35, 40],
                    borderColor: 'rgba(25, 135, 84, 1)',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: '使用率 (%)'
                        }
                    }
                }
            }
        });

        // API成本统计图表
        const apiCostCtx = document.getElementById('apiCostChart').getContext('2d');
        const apiCostChart = new Chart(apiCostCtx, {
            type: 'pie',
            data: {
                labels: ['DeepSeek R1', '通义千问'],
                datasets: [{
                    data: [70, 30],
                    backgroundColor: [
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(25, 135, 84, 0.7)'
                    ],
                    borderColor: [
                        'rgba(13, 110, 253, 1)',
                        'rgba(25, 135, 84, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // 刷新按钮点击事件
        $('#refreshBtn').click(function() {
            // 显示加载中提示
            $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>加载中...');
            $(this).prop('disabled', true);

            // 刷新页面
            location.reload();
        });

        // 自动刷新
        setInterval(function() {
            // 自动刷新页面
            location.reload();
        }, 60000);
    });
</script>
{% endblock %}
