/**
 * 九猫 - 专门修复分析结果页面的脚本
 * 这个脚本会在分析结果页面加载时执行，修复特定的JSON问题
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('分析结果页面修复脚本已加载');

    // 在页面加载完成后执行修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复分析结果页面');

        // 修复元数据显示
        fixMetadataDisplay();

        // 修复图表初始化
        fixChartInitialization();

        // 修复分析日志显示
        fixAnalysisLogs();

        // 修复推理过程显示
        fixReasoningContent();

        // 渲染Markdown内容
        renderMarkdownContent();
    });

    // 修复元数据显示
    function fixMetadataDisplay() {
        try {
            // 查找元数据元素
            var metadataElement = document.getElementById('analysis-metadata');
            if (!metadataElement) {
                console.log('未找到元数据元素，跳过修复');
                return;
            }

            console.log('找到元数据元素，尝试修复');

            // 获取元数据
            var metadataStr = metadataElement.getAttribute('data-metadata');
            if (!metadataStr) {
                console.log('元数据为空，跳过修复');
                return;
            }

            // 尝试解析JSON
            try {
                var metadata = JSON.parse(metadataStr);
                console.log('元数据解析成功');
            } catch (e) {
                console.error('元数据解析失败:', e.message);

                // 尝试修复JSON
                try {
                    // 检查是否是未终止的字符串错误
                    if (e.message.includes('Unterminated string')) {
                        console.log('检测到未终止的字符串错误，尝试修复');

                        // 尝试修复未终止的字符串
                        var errorPos = -1;
                        var match = e.message.match(/position (\d+)/);
                        if (match) {
                            errorPos = parseInt(match[1]);
                        }

                        if (errorPos >= 0) {
                            console.log('错误位置:', errorPos);

                            // 在错误位置添加引号
                            var fixedMetadata = metadataStr.substring(0, errorPos) + '"' + metadataStr.substring(errorPos);

                            try {
                                // 尝试解析修复后的JSON
                                metadata = JSON.parse(fixedMetadata);
                                console.log('成功修复元数据JSON');

                                // 更新元素的属性
                                metadataElement.setAttribute('data-metadata', fixedMetadata);
                            } catch (e2) {
                                console.error('修复元数据JSON失败:', e2.message);

                                // 使用空对象作为备用
                                metadata = {};
                                metadataElement.setAttribute('data-metadata', '{}');
                            }
                        }
                    } else {
                        // 使用空对象作为备用
                        metadata = {};
                        metadataElement.setAttribute('data-metadata', '{}');
                    }
                } catch (fixError) {
                    console.error('修复过程中出错:', fixError.message);
                    metadata = {};
                }
            }

            // 确保元数据是有效的对象
            if (!metadata || typeof metadata !== 'object') {
                console.log('元数据不是有效的对象，使用空对象替代');
                metadata = {};
                metadataElement.setAttribute('data-metadata', '{}');
            }

            // 更新页面上的元数据显示
            updateMetadataDisplay(metadata);
        } catch (e) {
            console.error('修复元数据显示时出错:', e.message);
        }
    }

    // 更新元数据显示
    function updateMetadataDisplay(metadata) {
        try {
            // 查找元数据显示区域
            var metadataItems = document.querySelectorAll('.metadata-item');
            if (metadataItems.length === 0) {
                console.log('未找到元数据显示区域，跳过更新');
                return;
            }

            console.log('找到元数据显示区域，更新显示');

            // 更新处理时间
            var processingTimeItem = Array.from(metadataItems).find(function(item) {
                return item.textContent.includes('处理时间');
            });
            if (processingTimeItem && metadata.processing_time) {
                var timeSpan = processingTimeItem.querySelector('span');
                if (timeSpan) {
                    timeSpan.textContent = (Math.round(metadata.processing_time * 100) / 100) + ' 秒';
                }
            }

            // 更新分析块数
            var chunkCountItem = Array.from(metadataItems).find(function(item) {
                return item.textContent.includes('分析块数');
            });
            if (chunkCountItem && metadata.chunk_count) {
                var countSpan = chunkCountItem.querySelector('span');
                if (countSpan) {
                    countSpan.textContent = metadata.chunk_count;
                }
            }
        } catch (e) {
            console.error('更新元数据显示时出错:', e.message);
        }
    }

    // 修复图表初始化
    function fixChartInitialization() {
        try {
            // 检查是否有图表容器
            var chartContainers = document.querySelectorAll('.chart-container');
            if (chartContainers.length === 0) {
                console.log('未找到图表容器，跳过修复');
                return;
            }

            console.log('找到图表容器，尝试修复图表初始化');

            // 检查Chart.js是否已加载
            if (typeof Chart === 'undefined') {
                console.log('Chart.js未加载，尝试加载');

                // 尝试先从本地加载
                var localChartScriptPath = '/static/js/lib/chart.min.js';
                var localFallbackPath = '/direct-static/js/lib/chart.min.js';
                var cdnPath = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js';

                loadScript(localChartScriptPath, function() {
                    // 本地加载成功
                    console.log('从本地成功加载Chart.js');
                    initializeCharts();
                }, function() {
                    // 尝试备用本地路径
                    loadScript(localFallbackPath, function() {
                        console.log('从备用本地路径成功加载Chart.js');
                        initializeCharts();
                    }, function() {
                        // 尝试从CDN加载
                        loadScript(cdnPath, function() {
                            console.log('从CDN成功加载Chart.js');
                            initializeCharts();
                        }, function() {
                            console.error('无法加载Chart.js，图表初始化失败');
                        });
                    });
                });
            } else {
                // Chart.js已加载，直接初始化图表
                console.log('Chart.js已加载，初始化图表');
                initializeCharts();
            }
        } catch (e) {
            console.error('修复图表初始化时出错:', e.message);
        }
    }

    // 安全地加载脚本
    function loadScript(src, onSuccess, onError) {
        try {
            var script = document.createElement('script');
            script.src = src;
            script.async = false;

            script.onload = function() {
                if (typeof onSuccess === 'function') {
                    onSuccess();
                }
            };

            script.onerror = function() {
                if (typeof onError === 'function') {
                    onError();
                }
            };

            // 安全地添加脚本
            try {
                document.head.appendChild(script);
            } catch (appendError) {
                console.error('添加脚本时出错:', appendError);
                if (typeof onError === 'function') {
                    onError();
                }
            }
        } catch (e) {
            console.error('加载脚本时出错:', e);
            if (typeof onError === 'function') {
                onError();
            }
        }
    }

    // 初始化图表
    function initializeCharts() {
        try {
            // 获取元数据
            var metadataElement = document.getElementById('analysis-metadata');
            var metadata = {};

            if (metadataElement) {
                try {
                    metadata = JSON.parse(metadataElement.getAttribute('data-metadata') || '{}');
                } catch (e) {
                    console.error('解析元数据JSON失败:', e.message);
                    metadata = {};
                }
            }

            // 初始化雷达图
            var radarCanvas = document.getElementById('radarChart');
            if (radarCanvas) {
                console.log('找到雷达图画布，初始化雷达图');

                // 准备雷达图数据
                var radarLabels = ['处理时间(秒)', '分块数量', 'API调用次数', '令牌使用量', '费用(元)'];
                var radarData = [
                    metadata.processing_time || 0,
                    metadata.chunk_count || 0,
                    metadata.api_calls || 0,
                    metadata.tokens_used || 0,
                    metadata.cost || 0
                ];

                // 如果有可视化数据，使用它
                if (metadata.visualization_data && metadata.visualization_data.radar) {
                    if (metadata.visualization_data.radar.labels) {
                        radarLabels = metadata.visualization_data.radar.labels;
                    }
                    if (metadata.visualization_data.radar.data) {
                        radarData = metadata.visualization_data.radar.data;
                    }
                }

                // 创建雷达图
                try {
                    new Chart(radarCanvas.getContext('2d'), {
                        type: 'radar',
                        data: {
                            labels: radarLabels,
                            datasets: [{
                                label: '分析评分',
                                data: radarData,
                                fill: true,
                                backgroundColor: 'rgba(74, 107, 223, 0.2)',
                                borderColor: 'rgb(74, 107, 223)',
                                pointBackgroundColor: 'rgb(74, 107, 223)',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: 'rgb(74, 107, 223)'
                            }]
                        },
                        options: {
                            elements: {
                                line: {
                                    borderWidth: 3
                                }
                            },
                            scales: {
                                r: {
                                    angleLines: {
                                        display: true
                                    },
                                    suggestedMin: 0,
                                    suggestedMax: 100
                                }
                            }
                        }
                    });

                    console.log('雷达图初始化成功');
                } catch (e) {
                    console.error('雷达图初始化失败:', e.message);
                }
            }

            // 初始化柱状图
            var barCanvas = document.getElementById('barChart');
            if (barCanvas) {
                console.log('找到柱状图画布，初始化柱状图');

                // 准备柱状图数据
                var barLabels = radarLabels;
                var barData = radarData;

                // 如果有可视化数据，使用它
                if (metadata.visualization_data && metadata.visualization_data.bar) {
                    if (metadata.visualization_data.bar.labels) {
                        barLabels = metadata.visualization_data.bar.labels;
                    }
                    if (metadata.visualization_data.bar.data) {
                        barData = metadata.visualization_data.bar.data;
                    }
                }

                // 创建柱状图
                try {
                    new Chart(barCanvas.getContext('2d'), {
                        type: 'bar',
                        data: {
                            labels: barLabels,
                            datasets: [{
                                label: '分析指标',
                                data: barData,
                                backgroundColor: [
                                    'rgba(75, 192, 192, 0.2)',
                                    'rgba(255, 99, 132, 0.2)',
                                    'rgba(54, 162, 235, 0.2)',
                                    'rgba(255, 206, 86, 0.2)',
                                    'rgba(153, 102, 255, 0.2)',
                                    'rgba(255, 159, 64, 0.2)'
                                ],
                                borderColor: [
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(153, 102, 255, 1)',
                                    'rgba(255, 159, 64, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100
                                }
                            }
                        }
                    });

                    console.log('柱状图初始化成功');
                } catch (e) {
                    console.error('柱状图初始化失败:', e.message);
                }
            }
        } catch (e) {
            console.error('初始化图表时出错:', e.message);
        }
    }

    // 修复分析日志显示
    function fixAnalysisLogs() {
        try {
            // 查找分析日志折叠区域
            var logsCollapse = document.getElementById('analysisLogsCollapse');
            if (!logsCollapse) {
                console.log('未找到分析日志折叠区域，跳过修复');
                return;
            }

            console.log('找到分析日志折叠区域，尝试修复');

            // 查找分析日志容器
            var logsContainer = logsCollapse.querySelector('.analysis-logs');
            if (!logsContainer) {
                console.log('未找到分析日志容器，跳过修复');
                return;
            }

            // 检查是否有日志条目
            var logEntries = logsContainer.querySelectorAll('.log-entry');
            if (logEntries.length === 0) {
                console.log('未找到日志条目，尝试从结果对象中获取');

                // 尝试从结果对象中获取日志
                var resultElement = document.querySelector('.analysis-content');
                if (!resultElement) {
                    console.log('未找到分析结果元素，跳过修复');
                    return;
                }

                // 尝试解析结果内容
                var resultContent = resultElement.textContent;
                if (!resultContent) {
                    console.log('分析结果内容为空，跳过修复');
                    return;
                }

                try {
                    // 尝试解析JSON
                    var resultData = JSON.parse(resultContent);
                    console.log('成功解析分析结果JSON');

                    // 检查是否有日志
                    if (resultData.logs && Array.isArray(resultData.logs) && resultData.logs.length > 0) {
                        console.log('从分析结果中找到日志，添加到日志容器');

                        // 清空日志容器
                        logsContainer.innerHTML = '';

                        // 添加日志条目
                        resultData.logs.forEach(function(log) {
                            var logEntry = document.createElement('div');
                            logEntry.className = 'log-entry';

                            // 添加日志级别样式
                            if (log.level === 'error') {
                                logEntry.className += ' text-danger';
                            } else if (log.level === 'warning') {
                                logEntry.className += ' text-warning';
                            }

                            // 添加重要日志样式
                            if (log.important) {
                                logEntry.className += ' font-weight-bold text-primary';
                            }

                            // 设置日志内容
                            logEntry.textContent = '[' + log.timestamp + '] ' + (log.level ? log.level.toUpperCase() + ' - ' : '') + log.message;

                            // 添加到容器
                            logsContainer.appendChild(logEntry);
                        });

                        console.log('成功添加日志条目');
                    }
                } catch (e) {
                    console.error('解析分析结果JSON失败:', e.message);
                }
            }
        } catch (e) {
            console.error('修复分析日志显示时出错:', e.message);
        }
    }

    // 修复推理过程显示
    function fixReasoningContent() {
        try {
            // 查找推理过程容器
            var reasoningContainers = document.querySelectorAll('[data-reasoning-container]');
            if (reasoningContainers.length === 0) {
                console.log('未找到推理过程容器，跳过修复');
                return;
            }

            console.log(`找到 ${reasoningContainers.length} 个推理过程容器，尝试修复`);

            // 处理每个容器
            reasoningContainers.forEach(function(container) {
                // 检查容器内容
                var reasoningText = container.querySelector('.reasoning-text');
                if (!reasoningText) {
                    console.log('未找到推理文本元素，跳过修复');
                    return;
                }

                // 获取推理内容
                var content = reasoningText.textContent || '';
                if (!content || content.trim() === '') {
                    console.log('推理内容为空，跳过修复');
                    return;
                }

                // 检查内容是否已经包含标准格式
                if (!content.includes('## 分析思路说明') && !content.includes('## 详细分析')) {
                    console.log('推理内容不包含标准格式，尝试格式化');

                    // 尝试格式化内容
                    var formattedContent = formatReasoningContent(content);

                    // 更新容器内容
                    reasoningText.innerHTML = formattedContent;
                    reasoningText.classList.add('markdown-content');

                    console.log('成功格式化推理内容');
                } else {
                    console.log('推理内容已包含标准格式，无需修复');
                }
            });
        } catch (e) {
            console.error('修复推理过程显示时出错:', e.message);
        }
    }

    // 格式化推理内容
    function formatReasoningContent(content) {
        if (!content) return '';

        // 尝试识别分析思路部分
        var formattedContent = content;

        // 检查是否包含编号列表（可能是分析思路）
        var numberedListMatch = content.match(/(?:^|\n)(?:1\.\s+[^\n]+(?:\n\d+\.\s+[^\n]+)+)/);
        if (numberedListMatch) {
            var approachContent = numberedListMatch[0];
            var restContent = content.substring(numberedListMatch.index + approachContent.length).trim();

            formattedContent = `<h2>分析思路说明：</h2>\n${approachContent}\n\n<h2>详细分析：</h2>\n${restContent}`;
        } else {
            // 如果没有明显的编号列表，尝试按比例分割
            var splitPoint = Math.floor(content.length * 0.3); // 前30%作为分析思路
            var splitIndex = content.substring(0, splitPoint).lastIndexOf('\n\n');

            if (splitIndex > 0) {
                var approachContent = content.substring(0, splitIndex).trim();
                var analysisContent = content.substring(splitIndex).trim();

                formattedContent = `<h2>分析思路说明：</h2>\n${approachContent}\n\n<h2>详细分析：</h2>\n${analysisContent}`;
            } else {
                // 如果无法分割，添加标准标题
                formattedContent = `<h2>分析思路说明：</h2>\n[自动提取的分析思路]\n\n<h2>详细分析：</h2>\n${content}`;
            }
        }

        // 将Markdown格式转换为HTML
        formattedContent = formattedContent
            // 处理标题
            .replace(/### (.*?)$/gm, '<h3>$1</h3>')
            // 处理粗体
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            // 处理编号列表
            .replace(/^\d+\.\s+(.*?)$/gm, '<li>$1</li>')
            // 处理段落
            .replace(/\n\n/g, '</p><p>')
            // 包装在段落中
            .replace(/^(.+)$/gm, '<p>$1</p>');

        return formattedContent;
    }

    // 渲染Markdown内容
    function renderMarkdownContent() {
        try {
            // 查找所有Markdown内容容器
            var markdownContainers = document.querySelectorAll('.markdown-content');
            if (markdownContainers.length === 0) {
                console.log('未找到Markdown内容容器，跳过渲染');
                return;
            }

            console.log(`找到 ${markdownContainers.length} 个Markdown内容容器，尝试渲染`);

            // 处理每个容器
            markdownContainers.forEach(function(container) {
                // 检查容器内容是否已经包含HTML标签
                if (container.innerHTML.includes('<h2>') || container.innerHTML.includes('<h3>')) {
                    console.log('容器内容已包含HTML标签，跳过渲染');
                    return;
                }

                // 获取内容
                var content = container.textContent || '';
                if (!content || content.trim() === '') {
                    console.log('容器内容为空，跳过渲染');
                    return;
                }

                // 将Markdown格式转换为HTML
                var htmlContent = content
                    // 处理标题
                    .replace(/## (.*?)$/gm, '<h2>$1</h2>')
                    .replace(/### (.*?)$/gm, '<h3>$1</h3>')
                    // 处理粗体
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    // 处理编号列表
                    .replace(/^\d+\.\s+(.*?)$/gm, '<li>$1</li>')
                    // 处理段落
                    .replace(/\n\n/g, '</p><p>')
                    // 包装在段落中
                    .replace(/^(.+)$/gm, '<p>$1</p>');

                // 更新容器内容
                container.innerHTML = htmlContent;

                console.log('成功渲染Markdown内容');
            });
        } catch (e) {
            console.error('渲染Markdown内容时出错:', e.message);
        }
    }
})();
