"""
九猫系统图表数据外部存储模块
将图表数据存储到外部存储，减轻系统内存压力
"""
import os
import json
import logging
import time
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# 外部存储根目录
EXTERNAL_STORAGE_ROOT = os.environ.get('EXTERNAL_STORAGE_PATH', "E:\\艹，又来一次\\九猫\\external_storage")

# 图表数据目录
CHARTS_DIR = os.path.join(EXTERNAL_STORAGE_ROOT, "charts")

# 确保目录存在
def ensure_directories():
    """确保所有必要的目录存在"""
    os.makedirs(CHARTS_DIR, exist_ok=True)
    logger.info(f"图表数据外部存储目录: {CHARTS_DIR}")

# 初始化外部存储
ensure_directories()

def save_chart_data(novel_id: int, dimension: str, chart_data: Dict[str, Any]) -> bool:
    """
    将图表数据保存到外部存储
    
    Args:
        novel_id: 小说ID
        dimension: 分析维度
        chart_data: 图表数据
        
    Returns:
        是否保存成功
    """
    try:
        # 创建小说图表目录
        novel_chart_dir = os.path.join(CHARTS_DIR, f"novel_{novel_id}")
        os.makedirs(novel_chart_dir, exist_ok=True)
        
        # 创建图表数据文件名
        chart_file = os.path.join(novel_chart_dir, f"{dimension}_chart.json")
        
        # 保存图表数据
        with open(chart_file, 'w', encoding='utf-8') as f:
            json.dump(chart_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"已将小说 {novel_id} 的 {dimension} 维度图表数据保存到外部存储")
        return True
    except Exception as e:
        logger.error(f"保存图表数据到外部存储时出错: {str(e)}")
        return False

def load_chart_data(novel_id: int, dimension: str) -> Optional[Dict[str, Any]]:
    """
    从外部存储加载图表数据
    
    Args:
        novel_id: 小说ID
        dimension: 分析维度
        
    Returns:
        图表数据，如果不存在则返回None
    """
    try:
        # 获取图表数据文件名
        novel_chart_dir = os.path.join(CHARTS_DIR, f"novel_{novel_id}")
        chart_file = os.path.join(novel_chart_dir, f"{dimension}_chart.json")
        
        # 检查文件是否存在
        if not os.path.exists(chart_file):
            logger.debug(f"小说 {novel_id} 的 {dimension} 维度图表数据不存在")
            return None
            
        # 加载图表数据
        with open(chart_file, 'r', encoding='utf-8') as f:
            chart_data = json.load(f)
            
        logger.info(f"已从外部存储加载小说 {novel_id} 的 {dimension} 维度图表数据")
        return chart_data
    except Exception as e:
        logger.error(f"从外部存储加载图表数据时出错: {str(e)}")
        return None

def delete_chart_data(novel_id: int, dimension: str = None) -> bool:
    """
    从外部存储删除图表数据
    
    Args:
        novel_id: 小说ID
        dimension: 分析维度，如果为None则删除所有维度的图表数据
        
    Returns:
        是否删除成功
    """
    try:
        # 获取小说图表目录
        novel_chart_dir = os.path.join(CHARTS_DIR, f"novel_{novel_id}")
        
        # 检查目录是否存在
        if not os.path.exists(novel_chart_dir):
            logger.debug(f"小说 {novel_id} 的图表数据目录不存在")
            return True
            
        # 如果指定了维度，只删除该维度的图表数据
        if dimension:
            chart_file = os.path.join(novel_chart_dir, f"{dimension}_chart.json")
            if os.path.exists(chart_file):
                os.remove(chart_file)
                logger.info(f"已删除小说 {novel_id} 的 {dimension} 维度图表数据")
            return True
            
        # 否则删除所有图表数据
        for file_name in os.listdir(novel_chart_dir):
            if file_name.endswith('_chart.json'):
                os.remove(os.path.join(novel_chart_dir, file_name))
                
        # 如果目录为空，删除目录
        if not os.listdir(novel_chart_dir):
            os.rmdir(novel_chart_dir)
            
        logger.info(f"已删除小说 {novel_id} 的所有图表数据")
        return True
    except Exception as e:
        logger.error(f"删除图表数据时出错: {str(e)}")
        return False

def list_chart_dimensions(novel_id: int) -> list:
    """
    列出小说的所有图表维度
    
    Args:
        novel_id: 小说ID
        
    Returns:
        维度列表
    """
    try:
        # 获取小说图表目录
        novel_chart_dir = os.path.join(CHARTS_DIR, f"novel_{novel_id}")
        
        # 检查目录是否存在
        if not os.path.exists(novel_chart_dir):
            return []
            
        # 获取所有图表文件
        dimensions = []
        for file_name in os.listdir(novel_chart_dir):
            if file_name.endswith('_chart.json'):
                dimension = file_name.replace('_chart.json', '')
                dimensions.append(dimension)
                
        return dimensions
    except Exception as e:
        logger.error(f"列出图表维度时出错: {str(e)}")
        return []

def extract_chart_data_from_metadata(metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    从分析结果元数据中提取图表数据
    
    Args:
        metadata: 分析结果元数据
        
    Returns:
        图表数据，如果不存在则返回None
    """
    try:
        # 检查元数据是否包含可视化数据
        if not metadata or 'visualization_data' not in metadata:
            return None
            
        visualization_data = metadata['visualization_data']
        
        # 构建图表数据
        chart_data = {
            'charts': []
        }
        
        # 处理雷达图
        if 'radar' in visualization_data:
            radar_data = visualization_data['radar']
            chart_data['charts'].append({
                'type': 'radar',
                'id': 'radarChart',
                'data': {
                    'labels': radar_data.get('labels', []),
                    'datasets': radar_data.get('datasets', [])
                },
                'options': radar_data.get('options', {})
            })
            
        # 处理柱状图
        if 'bar' in visualization_data:
            bar_data = visualization_data['bar']
            chart_data['charts'].append({
                'type': 'bar',
                'id': 'barChart',
                'data': {
                    'labels': bar_data.get('labels', []),
                    'datasets': bar_data.get('datasets', [])
                },
                'options': bar_data.get('options', {})
            })
            
        # 处理折线图
        if 'line' in visualization_data:
            line_data = visualization_data['line']
            chart_data['charts'].append({
                'type': 'line',
                'id': 'lineChart',
                'data': {
                    'labels': line_data.get('labels', []),
                    'datasets': line_data.get('datasets', [])
                },
                'options': line_data.get('options', {})
            })
            
        # 处理饼图
        if 'pie' in visualization_data:
            pie_data = visualization_data['pie']
            chart_data['charts'].append({
                'type': 'pie',
                'id': 'pieChart',
                'data': {
                    'labels': pie_data.get('labels', []),
                    'datasets': pie_data.get('datasets', [])
                },
                'options': pie_data.get('options', {})
            })
            
        # 处理散点图
        if 'scatter' in visualization_data:
            scatter_data = visualization_data['scatter']
            chart_data['charts'].append({
                'type': 'scatter',
                'id': 'scatterChart',
                'data': {
                    'labels': scatter_data.get('labels', []),
                    'datasets': scatter_data.get('datasets', [])
                },
                'options': scatter_data.get('options', {})
            })
            
        # 如果没有图表数据，返回None
        if not chart_data['charts']:
            return None
            
        return chart_data
    except Exception as e:
        logger.error(f"从元数据提取图表数据时出错: {str(e)}")
        return None
