/**
 * 九猫 - 小说详情页面不停刷新修复脚本
 * 专门处理小说详情页面不停刷新和加载缓慢的问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    // 检查是否已经加载过此脚本，避免重复加载
    if (window._novelPageRefreshFixLoaded) {
        console.log('小说详情页面不停刷新修复脚本已加载，避免重复执行');
        return;
    }
    
    // 标记脚本已加载
    window._novelPageRefreshFixLoaded = true;
    
    console.log('小说详情页面不停刷新修复脚本已加载 - 版本1.0.0');
    
    // 保存原始的方法引用
    const originalConsoleLog = console.log;
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;
    const originalClearTimeout = window.clearTimeout;
    const originalClearInterval = window.clearInterval;
    const originalReload = window.location.reload;
    const originalAssign = window.location.assign;
    const originalReplace = window.location.replace;
    
    // 记录定时器ID
    const timerIds = new Set();
    
    // 安全日志函数
    function safeLog(message) {
        try {
            originalConsoleLog.call(console, '[小说页面修复] ' + message);
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 初始化函数
    function initialize() {
        safeLog('初始化小说详情页面不停刷新修复');
        
        // 检查是否在小说详情页面
        if (!isNovelDetailPage()) {
            safeLog('当前不是小说详情页面，不执行修复');
            return;
        }
        
        safeLog('检测到小说详情页面，开始执行修复');
        
        // 禁用页面刷新
        disablePageRefresh();
        
        // 清除所有可能的定时器
        clearAllTimers();
        
        // 拦截定时器创建
        interceptTimers();
        
        // 修复可能导致刷新的脚本
        fixRefreshScripts();
        
        // 优化页面加载
        optimizePageLoading();
        
        // 添加页面可见性变化事件处理
        handleVisibilityChange();
        
        safeLog('小说详情页面不停刷新修复完成');
    }
    
    // 检查是否在小说详情页面
    function isNovelDetailPage() {
        const path = window.location.pathname;
        // 匹配 /novel/{id} 格式的URL
        return /^\/novel\/\d+\/?$/.test(path);
    }
    
    // 禁用页面刷新
    function disablePageRefresh() {
        safeLog('禁用页面刷新功能');
        
        // 重写location.reload
        window.location.reload = function() {
            safeLog('拦截到页面刷新尝试');
            showRefreshBlockedNotification();
            return false;
        };
        
        // 拦截location.assign和location.replace
        window.location.assign = function(url) {
            // 检查是否是当前页面的刷新尝试
            const currentUrl = window.location.href.split('?')[0];
            const targetUrl = (url || '').split('?')[0];
            
            if (currentUrl === targetUrl || url === window.location.href) {
                safeLog('拦截到通过location.assign的刷新尝试');
                showRefreshBlockedNotification();
                return false;
            }
            
            // 如果不是刷新，允许正常导航
            return originalAssign.apply(this, arguments);
        };
        
        window.location.replace = function(url) {
            // 检查是否是当前页面的刷新尝试
            const currentUrl = window.location.href.split('?')[0];
            const targetUrl = (url || '').split('?')[0];
            
            if (currentUrl === targetUrl || url === window.location.href) {
                safeLog('拦截到通过location.replace的刷新尝试');
                showRefreshBlockedNotification();
                return false;
            }
            
            // 如果不是刷新，允许正常导航
            return originalReplace.apply(this, arguments);
        };
        
        // 禁用页面的自动刷新元标记
        try {
            const metaTags = document.getElementsByTagName('meta');
            for (let i = 0; i < metaTags.length; i++) {
                if (metaTags[i].httpEquiv === 'refresh') {
                    metaTags[i].parentNode.removeChild(metaTags[i]);
                    safeLog('已移除页面自动刷新元标记');
                }
            }
        } catch (e) {
            safeLog('移除自动刷新元标记时出错: ' + e.message);
        }
    }
    
    // 清除所有可能的定时器
    function clearAllTimers() {
        safeLog('清除所有可能的定时器');
        
        try {
            // 清除所有可能的定时器
            for (let i = 1; i < 10000; i++) {
                try {
                    originalClearTimeout(i);
                    originalClearInterval(i);
                } catch (e) {
                    // 忽略错误
                }
            }
            safeLog('已清除所有可能的定时器');
        } catch (e) {
            safeLog('清除定时器时出错: ' + e.message);
        }
    }
    
    // 拦截定时器创建
    function interceptTimers() {
        safeLog('拦截定时器创建');
        
        // 重写setTimeout
        window.setTimeout = function(callback, delay, ...args) {
            // 检查回调函数是否可能导致刷新
            const callbackString = callback.toString();
            const isRefreshAttempt = 
                callbackString.includes('location.reload') || 
                callbackString.includes('window.location.reload') ||
                callbackString.includes('location.href') ||
                callbackString.includes('window.location.href =');
            
            if (isRefreshAttempt) {
                safeLog('拦截到setTimeout中的刷新尝试');
                showRefreshBlockedNotification();
                
                // 返回一个假的定时器ID
                return -1;
            }
            
            // 如果不是刷新尝试，但间隔时间太短，延长间隔时间
            if (delay < 1000) {
                delay = Math.max(delay, 1000);
            }
            
            // 创建定时器并记录ID
            const timerId = originalSetTimeout.apply(this, [callback, delay, ...args]);
            timerIds.add(timerId);
            
            return timerId;
        };
        
        // 重写setInterval
        window.setInterval = function(callback, delay, ...args) {
            // 检查回调函数是否可能导致刷新
            const callbackString = callback.toString();
            const isRefreshAttempt = 
                callbackString.includes('location.reload') || 
                callbackString.includes('window.location.reload') ||
                callbackString.includes('location.href') ||
                callbackString.includes('window.location.href =');
            
            if (isRefreshAttempt) {
                safeLog('拦截到setInterval中的刷新尝试');
                showRefreshBlockedNotification();
                
                // 返回一个假的定时器ID
                return -1;
            }
            
            // 如果不是刷新尝试，但间隔时间太短，延长间隔时间
            if (delay < 1000) {
                delay = Math.max(delay, 1000);
            }
            
            // 创建定时器并记录ID
            const timerId = originalSetInterval.apply(this, [callback, delay, ...args]);
            timerIds.add(timerId);
            
            return timerId;
        };
        
        // 重写clearTimeout和clearInterval，从记录中移除ID
        window.clearTimeout = function(id) {
            timerIds.delete(id);
            return originalClearTimeout.apply(this, arguments);
        };
        
        window.clearInterval = function(id) {
            timerIds.delete(id);
            return originalClearInterval.apply(this, arguments);
        };
    }
    
    // 修复可能导致刷新的脚本
    function fixRefreshScripts() {
        safeLog('修复可能导致刷新的脚本');
        
        // 查找并禁用包含自动刷新代码的脚本
        const scripts = document.querySelectorAll('script:not([src])');
        for (const script of scripts) {
            const content = script.textContent || '';
            
            // 检查是否包含自动刷新代码
            if (content.includes('location.reload') || 
                content.includes('window.location.reload') || 
                (content.includes('setTimeout') && content.includes('reload'))) {
                
                safeLog('发现可能导致自动刷新的脚本');
                
                // 尝试替换脚本内容
                try {
                    const newScript = document.createElement('script');
                    newScript.textContent = content
                        .replace(/location\.reload/g, '/* location.reload */')
                        .replace(/window\.location\.reload/g, '/* window.location.reload */')
                        .replace(/setTimeout\s*\(\s*function\s*\(\s*\)\s*\{\s*(?:window\.)?location\.reload/g, 
                                'setTimeout(function() { /* location.reload disabled */');
                    
                    // 替换原始脚本
                    if (script.parentNode) {
                        script.parentNode.replaceChild(newScript, script);
                        safeLog('已禁用自动刷新脚本');
                    }
                } catch (e) {
                    safeLog('禁用自动刷新脚本失败: ' + e.message);
                }
            }
        }
    }
    
    // 优化页面加载
    function optimizePageLoading() {
        safeLog('优化页面加载');
        
        // 延迟加载非关键资源
        setTimeout(() => {
            // 查找所有图表相关的脚本和样式
            const chartScripts = document.querySelectorAll('script[src*="chart"]');
            for (const script of chartScripts) {
                script.setAttribute('defer', 'defer');
                safeLog('延迟加载图表脚本: ' + script.src);
            }
            
            // 禁用不必要的动画和过渡效果
            const style = document.createElement('style');
            style.textContent = `
                .progress-bar-animated, .progress-bar-striped {
                    animation: none !important;
                }
                .fade {
                    transition: none !important;
                }
            `;
            document.head.appendChild(style);
            safeLog('已禁用不必要的动画和过渡效果');
        }, 100);
    }
    
    // 处理页面可见性变化
    function handleVisibilityChange() {
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，再次执行修复
                safeLog('页面变为可见，再次执行修复');
                setTimeout(initialize, 100);
            }
        });
    }
    
    // 显示刷新被阻止的通知
    function showRefreshBlockedNotification() {
        const notificationId = 'refresh-blocked-notification';
        
        // 如果已有通知，不再显示
        if (document.getElementById(notificationId)) {
            return;
        }
        
        // 创建通知元素
        const notification = document.createElement('div');
        notification.id = notificationId;
        notification.style.cssText = 'position: fixed; bottom: 20px; right: 20px; background-color: #fff; border: 1px solid #ccc; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 15px; border-radius: 5px; z-index: 10000; max-width: 350px;';
        
        notification.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px; color: #d9534f;">⚠️ 检测到页面刷新循环</div>
            <p style="margin-bottom: 10px;">已自动阻止页面刷新，以防止频繁刷新导致的性能问题。</p>
            <div>
                <button id="refresh-blocked-close" style="padding: 5px 10px; background: #eee; border: none; margin-right: 10px; cursor: pointer;">关闭</button>
                <button id="refresh-blocked-refresh" style="padding: 5px 10px; background: #5bc0de; color: white; border: none; cursor: pointer;">手动刷新一次</button>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 添加关闭按钮事件
        document.getElementById('refresh-blocked-close').addEventListener('click', function() {
            notification.remove();
        });
        
        // 添加手动刷新按钮事件
        document.getElementById('refresh-blocked-refresh').addEventListener('click', function() {
            notification.remove();
            safeLog('用户请求手动刷新一次');
            originalReload.call(window.location);
        });
        
        // 5秒后自动关闭
        setTimeout(() => {
            if (document.getElementById(notificationId)) {
                notification.remove();
            }
        }, 5000);
    }
    
    // 在页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果页面已经加载完成，立即执行修复
        initialize();
    }
    
    // 导出API，方便调试和手动调用
    window.novelPageRefreshFix = {
        initialize: initialize,
        clearAllTimers: clearAllTimers,
        showRefreshBlockedNotification: showRefreshBlockedNotification
    };
})();
