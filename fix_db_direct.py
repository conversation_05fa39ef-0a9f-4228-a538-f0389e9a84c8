"""
直接修复数据库结构，添加缺失的analysis_logs列
"""
import os
import sys
import sqlite3
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_database():
    """直接修复数据库结构"""
    db_path = "novels.db"
    
    if not os.path.exists(db_path):
        logger.error(f"数据库文件 {db_path} 不存在")
        return False
    
    logger.info(f"正在修复数据库 {db_path}")
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查列是否已存在
        cursor.execute("PRAGMA table_info(analysis_results)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'analysis_logs' in columns:
            logger.info("analysis_logs列已存在，无需修复")
            conn.close()
            return True
        
        # 添加列
        logger.info("正在添加analysis_logs列...")
        cursor.execute("ALTER TABLE analysis_results ADD COLUMN analysis_logs TEXT")
        
        # 提交更改
        conn.commit()
        logger.info("成功添加analysis_logs列")
        
        # 关闭连接
        conn.close()
        return True
    except Exception as e:
        logger.error(f"修复数据库时出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_database()
    if success:
        print("数据库修复成功！")
        sys.exit(0)
    else:
        print("数据库修复失败，请查看日志")
        sys.exit(1)
