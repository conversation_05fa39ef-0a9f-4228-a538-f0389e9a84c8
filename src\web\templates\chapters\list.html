{% extends "base.html" %}

{% block title %}{{ novel.title }} - 章节列表{% endblock %}

{% block head %}
{{ super() }}
<!-- 章节列表页面专用修复脚本 -->
<script src="/static/js/chapter-list-fix-loader.js"></script>
<script>
// 立即执行函数，避免污染全局命名空间
(function() {
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[章节列表页面修复] 初始化');

        // 修复所有章节链接
        const chapterLinks = document.querySelectorAll('a.btn-primary');
        chapterLinks.forEach(function(link) {
            // 获取原始href
            const originalHref = link.getAttribute('href');

            // 确保链接正确
            link.addEventListener('click', function(e) {
                console.log('[章节列表页面修复] 点击章节链接: ' + originalHref);
            });
        });

        // 修复分析按钮
        const analyzeButton = document.getElementById('startAnalysisBtn');
        if (analyzeButton) {
            analyzeButton.addEventListener('click', function(e) {
                console.log('[章节列表页面修复] 点击分析按钮');
            });
        }
    });
})();
</script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">小说列表</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('view_novel', novel_id=novel.id) }}">{{ novel.title }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">章节列表</li>
        </ol>
    </nav>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2>{{ novel.title }} - 章节列表</h2>
            <div>
                <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#analyzeModal">
                    分析所有章节
                </button>
                <button type="button" class="btn btn-success" id="analyzeAllChapterDimensionsBtn">
                    一键分析所有章节维度
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <p><strong>作者：</strong>{{ novel.author or '未知' }}</p>
                <p><strong>总字数：</strong>{{ novel.word_count }}</p>
                <p><strong>章节数：</strong>{{ chapters|length }}</p>
            </div>

            <!-- 分析进度条 -->
            <div id="analysisProgressContainer" class="mb-4" style="display: none;">
                <h4>分析进度</h4>
                <div class="progress mb-2">
                    <div id="analysisProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div id="analysisStatus" class="text-muted">准备中...</div>
                <div class="mt-2">
                    <button id="cancelAnalysisBtn" class="btn btn-sm btn-danger">取消分析</button>
                </div>
            </div>

            <!-- 章节列表 -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>章节</th>
                            <th>标题</th>
                            <th>字数</th>
                            <th>分析状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for chapter in chapters %}
                        <tr>
                            <td>{{ chapter.chapter_number }}</td>
                            <td>{{ chapter.title or '无标题' }}</td>
                            <td>{{ chapter.word_count }}</td>
                            <td>
                                {% if analysis_counts[chapter.id] > 0 %}
                                <span class="badge bg-success">已分析 ({{ analysis_counts[chapter.id] }})</span>
                                {% else %}
                                <span class="badge bg-secondary">未分析</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('chapter.view_chapter', novel_id=novel.id, chapter_id=chapter.id) }}" class="btn btn-sm btn-primary">查看</a>
                                {% if analysis_counts[chapter.id] > 0 %}
                                <button class="btn btn-sm btn-outline-primary ms-1 reanalyze-chapter-btn"
                                        data-novel-id="{{ novel.id }}"
                                        data-chapter-id="{{ chapter.id }}">
                                    <i class="fas fa-sync-alt"></i> 重新分析
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 分析模态框 -->
<div class="modal fade" id="analyzeModal" tabindex="-1" aria-labelledby="analyzeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analyzeModalLabel">分析所有章节</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="analyzeForm" action="/novel/{{ novel.id }}/chapters/analyze" method="POST">
                    <div class="mb-3">
                        <p>请选择要分析的维度：</p>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="select-all-dimensions">
                            <label class="form-check-label" for="select-all-dimensions">
                                <strong>全选</strong>
                            </label>
                        </div>
                        <hr>
                        <!-- 维度列表将由JavaScript动态生成 -->
                        <div id="dimensions-container">
                            {% for dimension in dimensions %}
                            <div class="form-check mb-2">
                                <input class="form-check-input dimension-checkbox" type="checkbox" name="dimensions" value="{{ dimension.key }}" id="dimension-{{ loop.index }}" checked>
                                <label class="form-check-label" for="dimension-{{ loop.index }}">
                                    {{ dimension.name }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="alert alert-warning mt-3">
                            <small>注意：分析过程可能需要较长时间，尤其是对于长篇小说。</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="modelSelect" class="form-label">选择分析模型</label>
                        <select class="form-select" id="modelSelect">
                            <option value="deepseek-r1">DeepSeek R1</option>
                            <option value="qwen-plus-latest">通义千问-Plus-Latest</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="parallelSwitch" checked>
                            <label class="form-check-label" for="parallelSwitch">启用并行处理</label>
                        </div>
                        <div class="form-text">并行处理可以加快分析速度，但可能增加系统负载</div>
                    </div>
                    <div class="mb-3" id="maxWorkersContainer">
                        <label for="maxWorkersRange" class="form-label">最大工作线程数: <span id="maxWorkersValue">4</span></label>
                        <input type="range" class="form-range" id="maxWorkersRange" min="1" max="8" value="4">
                        <div class="form-text">增加工作线程数可以加快分析速度，但会增加系统负载</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startAnalysisBtn">开始分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const novelId = {{ novel.id }};
        let analysisInterval = null;

        // 一键分析所有章节维度按钮
        document.getElementById('analyzeAllChapterDimensionsBtn').addEventListener('click', function() {
            if (confirm('确定要一键分析该小说的所有章节的所有维度吗？\n\n这将会分析所有章节的全部15个维度，可能需要较长时间。')) {
                // 显示进度条
                document.getElementById('analysisProgressContainer').style.display = 'block';
                document.getElementById('analysisStatus').textContent = '正在启动所有章节维度分析...';

                // 发送请求
                fetch(`/api/novel/${novelId}/analyze_all_chapters`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        // 开始轮询进度
                        startProgressPolling();
                    } else {
                        alert('启动分析失败: ' + data.message);
                        document.getElementById('analysisProgressContainer').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('启动分析时出错:', error);
                    alert('启动分析时出错，请查看控制台');
                    document.getElementById('analysisProgressContainer').style.display = 'none';
                });
            }
        });

        // 最大工作线程数滑块
        document.getElementById('maxWorkersRange').addEventListener('input', function() {
            document.getElementById('maxWorkersValue').textContent = this.value;
        });

        // 并行处理开关
        document.getElementById('parallelSwitch').addEventListener('change', function() {
            document.getElementById('maxWorkersContainer').style.display = this.checked ? 'block' : 'none';
        });

        // 全选按钮逻辑
        document.getElementById('select-all-dimensions').addEventListener('change', function() {
            const isChecked = this.checked;
            document.querySelectorAll('.dimension-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });

        // 开始分析按钮
        document.getElementById('startAnalysisBtn').addEventListener('click', function() {
            // 获取选中的维度
            const dimensions = [];
            document.querySelectorAll('.dimension-checkbox:checked').forEach(checkbox => {
                dimensions.push(checkbox.value);
            });

            const model = document.getElementById('modelSelect').value;
            const parallel = document.getElementById('parallelSwitch').checked;
            const maxWorkers = parseInt(document.getElementById('maxWorkersRange').value);

            if (dimensions.length === 0) {
                alert('请至少选择一个分析维度');
                return;
            }

            // 使用第一个维度作为主维度（兼容旧代码）
            const dimension = dimensions[0];

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('analyzeModal'));
            modal.hide();

            // 显示进度条
            document.getElementById('analysisProgressContainer').style.display = 'block';

            // 创建隐藏的表单字段
            const form = document.getElementById('analyzeForm');

            // 清除之前的隐藏字段
            const oldInputs = form.querySelectorAll('input[type="hidden"]');
            oldInputs.forEach(input => input.remove());

            // 添加维度字段
            const dimensionInput = document.createElement('input');
            dimensionInput.type = 'hidden';
            dimensionInput.name = 'dimension';
            dimensionInput.value = dimension;
            form.appendChild(dimensionInput);

            // 添加模型字段
            const modelInput = document.createElement('input');
            modelInput.type = 'hidden';
            modelInput.name = 'model';
            modelInput.value = model;
            form.appendChild(modelInput);

            // 添加并行处理字段
            const parallelInput = document.createElement('input');
            parallelInput.type = 'hidden';
            parallelInput.name = 'parallel';
            parallelInput.value = parallel;
            form.appendChild(parallelInput);

            // 添加最大工作线程数字段
            const maxWorkersInput = document.createElement('input');
            maxWorkersInput.type = 'hidden';
            maxWorkersInput.name = 'max_workers';
            maxWorkersInput.value = maxWorkers;
            form.appendChild(maxWorkersInput);

            // 提交表单
            form.submit();
        });

        // 取消分析按钮
        document.getElementById('cancelAnalysisBtn').addEventListener('click', function() {
            if (confirm('确定要取消分析任务吗？')) {
                fetch(`/novel/${novelId}/chapters/cancel`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('已取消分析任务');
                        stopProgressPolling();
                        document.getElementById('analysisProgressContainer').style.display = 'none';
                    } else {
                        alert('取消分析任务失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('取消分析任务时出错:', error);
                    alert('取消分析任务时出错');
                });
            }
        });

        // 开始轮询进度
        function startProgressPolling() {
            // 清除之前的轮询
            stopProgressPolling();

            // 开始新的轮询
            analysisInterval = setInterval(function() {
                fetch(`/novel/${novelId}/chapters/progress`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateProgressUI(data);

                        // 如果分析完成或失败，停止轮询
                        if (!data.is_running) {
                            stopProgressPolling();

                            // 如果分析完成，刷新页面
                            if (data.status === 'completed') {
                                setTimeout(function() {
                                    window.location.reload();
                                }, 2000);
                            }
                        }
                    } else {
                        console.error('获取进度失败:', data.error);
                    }
                })
                .catch(error => {
                    console.error('获取进度时出错:', error);
                });
            }, 2000);
        }

        // 停止轮询进度
        function stopProgressPolling() {
            if (analysisInterval) {
                clearInterval(analysisInterval);
                analysisInterval = null;
            }
        }

        // 更新进度UI
        function updateProgressUI(data) {
            const progressBar = document.getElementById('analysisProgressBar');
            const statusText = document.getElementById('analysisStatus');

            // 更新进度条
            progressBar.style.width = `${data.progress}%`;
            progressBar.setAttribute('aria-valuenow', data.progress);
            progressBar.textContent = `${data.progress}%`;

            // 更新状态文本
            let statusMessage = '';
            switch (data.status) {
                case 'starting':
                    statusMessage = '正在启动分析任务...';
                    break;
                case 'splitting_chapters':
                    statusMessage = '正在分割章节...';
                    break;
                case 'analyzing':
                    statusMessage = `正在分析章节 (${data.completed_chapters}/${data.total_chapters})`;
                    break;
                case 'completed':
                    statusMessage = '分析完成';
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.remove('progress-bar-striped');
                    progressBar.classList.add('bg-success');
                    break;
                case 'failed':
                    statusMessage = `分析失败: ${data.error || '未知错误'}`;
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.remove('progress-bar-striped');
                    progressBar.classList.add('bg-danger');
                    break;
                case 'cancelled':
                    statusMessage = '分析已取消';
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.remove('progress-bar-striped');
                    progressBar.classList.add('bg-warning');
                    break;
                default:
                    statusMessage = `状态: ${data.status}`;
            }

            statusText.textContent = statusMessage;
        }

        // 检查是否有正在进行的分析任务
        fetch(`/novel/${novelId}/chapters/progress`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.is_running) {
                // 显示进度条
                document.getElementById('analysisProgressContainer').style.display = 'block';

                // 更新进度UI
                updateProgressUI(data);

                // 开始轮询进度
                startProgressPolling();
            }
        })
        .catch(error => {
            console.error('检查分析任务时出错:', error);
        });

        // 章节重新分析按钮点击事件
        document.querySelectorAll('.reanalyze-chapter-btn').forEach(button => {
            button.addEventListener('click', function() {
                const novelId = this.getAttribute('data-novel-id');
                const chapterId = this.getAttribute('data-chapter-id');

                if (!novelId || !chapterId) {
                    console.error('缺少必要的数据属性');
                    return;
                }

                if (confirm('确定要重新分析该章节的所有维度吗？这将覆盖现有的分析结果。')) {
                    // 禁用按钮防止重复点击
                    this.disabled = true;
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中...';

                    // 获取所有维度
                    const allDimensions = [];
                    document.querySelectorAll('.dimension-checkbox').forEach(checkbox => {
                        allDimensions.push(checkbox.value);
                    });

                    // 创建一个计数器来跟踪完成的分析请求
                    let completedRequests = 0;
                    let successfulRequests = 0;
                    const totalRequests = allDimensions.length;

                    // 显示进度信息
                    const progressInfo = document.createElement('div');
                    progressInfo.className = 'progress-info mt-2';
                    progressInfo.innerHTML = `<small>正在分析维度: 0/${totalRequests}</small>`;
                    this.parentNode.appendChild(progressInfo);

                    // 对每个维度发送单独的分析请求
                    allDimensions.forEach(dimension => {
                        fetch(`/api/novel/${novelId}/chapter/${chapterId}/analyze`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                dimension: dimension, // 单个维度
                                model: 'deepseek-r1' // 使用默认模型
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            completedRequests++;
                            if (data.success) {
                                successfulRequests++;
                            }

                            // 更新进度信息
                            progressInfo.innerHTML = `<small>正在分析维度: ${completedRequests}/${totalRequests} (成功: ${successfulRequests})</small>`;

                            // 如果所有请求都已完成，刷新页面
                            if (completedRequests === totalRequests) {
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            }
                        })
                        .catch(error => {
                            completedRequests++;
                            console.error(`分析维度 ${dimension} 时出错:`, error);

                            // 更新进度信息
                            progressInfo.innerHTML = `<small>正在分析维度: ${completedRequests}/${totalRequests} (成功: ${successfulRequests})</small>`;

                            // 如果所有请求都已完成，刷新页面
                            if (completedRequests === totalRequests) {
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            }
                        });
                    });

                    // 所有的处理都在上面的forEach循环中完成
                }
            });
        });
    });
</script>
{% endblock %}
