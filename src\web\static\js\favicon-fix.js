/**
 * 九猫 - Favicon修复脚本
 * 用于修复favicon相关问题
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('Favicon修复脚本已加载 v1.0.0');
    
    // 配置
    const CONFIG = {
        enableDebug: false,           // 启用调试模式
        defaultFavicon: 'data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>📚</text></svg>'
    };
    
    // 初始化
    function initialize() {
        console.log('初始化Favicon修复');
        
        // 检查是否已有favicon
        if (!hasFavicon()) {
            // 添加默认favicon
            addDefaultFavicon();
        }
        
        // 防止浏览器自动请求favicon.ico
        preventFaviconRequest();
    }
    
    // 检查是否已有favicon
    function hasFavicon() {
        const links = document.querySelectorAll('link[rel="icon"], link[rel="shortcut icon"]');
        return links.length > 0;
    }
    
    // 添加默认favicon
    function addDefaultFavicon() {
        console.log('添加默认favicon');
        
        const link = document.createElement('link');
        link.rel = 'icon';
        link.href = CONFIG.defaultFavicon;
        
        // 添加到head
        document.head.appendChild(link);
    }
    
    // 防止浏览器自动请求favicon.ico
    function preventFaviconRequest() {
        // 创建一个空的favicon.ico请求处理器
        const originalFetch = window.fetch;
        
        window.fetch = function(url, options) {
            // 如果是favicon.ico请求，返回一个空响应
            if (typeof url === 'string' && url.includes('favicon.ico')) {
                console.log('拦截favicon.ico请求');
                return Promise.resolve(new Response(new Blob(), { status: 200 }));
            }
            
            // 否则使用原始fetch
            return originalFetch.apply(this, arguments);
        };
        
        // 拦截XMLHttpRequest
        const originalOpen = XMLHttpRequest.prototype.open;
        
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            // 如果是favicon.ico请求，修改为空请求
            if (typeof url === 'string' && url.includes('favicon.ico')) {
                console.log('拦截XMLHttpRequest favicon.ico请求');
                url = 'data:,';
            }
            
            // 调用原始方法
            return originalOpen.call(this, method, url, async, user, password);
        };
    }
    
    // 初始化
    initialize();
})();
