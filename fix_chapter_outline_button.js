/**
 * 九猫 - 章纲分析推理过程修复按钮
 * 在控制台页面添加修复按钮
 */
(function() {
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('添加章纲分析推理过程修复按钮');
        
        // 创建修复按钮
        const fixButton = document.createElement('div');
        fixButton.className = 'card mb-4';
        fixButton.innerHTML = `
            <div class="card-header bg-warning">
                <h5 class="mb-0">系统修复工具</h5>
            </div>
            <div class="card-body">
                <p><strong>问题描述：</strong>整本书的章纲分析（chapter_outline）推理过程显示的是一个简短的摘要，而不是详细的分析内容。</p>
                <a href="/fix-chapter-outline" class="btn btn-warning">
                    <i class="fas fa-tools me-2"></i>修复章纲分析推理过程
                </a>
            </div>
        `;
        
        // 查找运行日志区域
        const logContent = document.getElementById('logContent');
        if (logContent) {
            // 找到运行日志的父元素
            const logParent = logContent.parentElement.parentElement.parentElement;
            if (logParent) {
                // 在运行日志前插入修复按钮
                logParent.parentElement.insertBefore(fixButton, logParent);
                console.log('成功添加章纲分析推理过程修复按钮');
            }
        } else {
            console.error('未找到运行日志区域，无法添加修复按钮');
        }
    });
})();
