"""
系统监控路由
提供系统监控仪表板和相关API
"""
import os
import logging
from datetime import datetime
from flask import Blueprint, render_template, jsonify, request, send_file, redirect, url_for

from src.web.system_monitor import (
    get_system_metrics, get_api_stats, get_db_stats, 
    get_alerts, get_alert_settings, update_alert_settings,
    get_logs, get_dimension_stats
)

# 创建蓝图
system_monitor_bp = Blueprint('system_monitor', __name__)

logger = logging.getLogger(__name__)

@system_monitor_bp.route('/system-monitor')
def system_monitor():
    """系统监控仪表板页面"""
    try:
        # 获取系统指标
        system_metrics = get_system_metrics()
        
        # 获取API调用统计
        api_stats = get_api_stats()
        
        # 获取数据库连接池状态
        db_stats = get_db_stats()
        
        # 获取系统告警
        alerts = get_alerts(limit=10)
        
        # 获取告警设置
        alert_settings = get_alert_settings()
        
        # 获取系统日志
        logs = get_logs(limit=100)
        
        # 获取维度统计
        dimension_stats = get_dimension_stats()
        
        return render_template(
            'system_monitor.html',
            system_metrics=system_metrics,
            api_stats=api_stats,
            db_stats=db_stats,
            alerts=alerts,
            alert_settings=alert_settings,
            logs=logs,
            dimension_stats=dimension_stats,
            last_updated=datetime.now()
        )
    except Exception as e:
        logger.error(f"加载系统监控页面时出错: {str(e)}")
        return render_template('error.html', error=f"加载系统监控页面时出错: {str(e)}")

@system_monitor_bp.route('/api/system-metrics')
def api_system_metrics():
    """获取系统指标API"""
    try:
        metrics = get_system_metrics()
        return jsonify({
            'success': True,
            'metrics': metrics,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取系统指标API出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_monitor_bp.route('/api/api-stats')
def api_stats_endpoint():
    """获取API调用统计API"""
    try:
        stats = get_api_stats()
        return jsonify({
            'success': True,
            'stats': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取API调用统计出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_monitor_bp.route('/api/db-stats')
def db_stats_endpoint():
    """获取数据库连接池状态API"""
    try:
        stats = get_db_stats()
        return jsonify({
            'success': True,
            'stats': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取数据库连接池状态出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_monitor_bp.route('/api/alerts')
def alerts_endpoint():
    """获取系统告警API"""
    try:
        limit = request.args.get('limit', 10, type=int)
        alerts = get_alerts(limit=limit)
        
        # 转换为JSON可序列化格式
        alerts_json = []
        for alert in alerts:
            alerts_json.append({
                'id': alert.id,
                'timestamp': alert.timestamp.isoformat(),
                'level': alert.level,
                'title': alert.title,
                'message': alert.message
            })
        
        return jsonify({
            'success': True,
            'alerts': alerts_json,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取系统告警出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_monitor_bp.route('/api/alert-settings', methods=['GET', 'POST'])
def alert_settings_endpoint():
    """获取或更新告警设置API"""
    try:
        if request.method == 'GET':
            settings = get_alert_settings()
            return jsonify({
                'success': True,
                'settings': settings
            })
        else:
            # 更新设置
            data = request.json
            success = update_alert_settings(data)
            return jsonify({
                'success': success,
                'message': '告警设置已更新' if success else '更新告警设置失败'
            })
    except Exception as e:
        logger.error(f"处理告警设置时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_monitor_bp.route('/api/logs')
def logs_endpoint():
    """获取系统日志API"""
    try:
        limit = request.args.get('limit', 100, type=int)
        level = request.args.get('level')
        logs = get_logs(limit=limit, level=level)
        
        # 转换为JSON可序列化格式
        logs_json = []
        for log in logs:
            logs_json.append({
                'timestamp': log['timestamp'].isoformat(),
                'name': log['name'],
                'level': log['level'],
                'message': log['message']
            })
        
        return jsonify({
            'success': True,
            'logs': logs_json,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取系统日志出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_monitor_bp.route('/download-logs')
def download_logs():
    """下载完整日志文件"""
    try:
        log_file = 'app.log'
        if os.path.exists(log_file):
            return send_file(
                log_file,
                as_attachment=True,
                download_name=f"九猫系统日志_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            )
        else:
            return jsonify({
                'success': False,
                'error': '日志文件不存在'
            }), 404
    except Exception as e:
        logger.error(f"下载日志文件时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_monitor_bp.route('/update-alert-settings', methods=['POST'])
def update_alert_settings_endpoint():
    """更新告警设置"""
    try:
        # 从表单获取设置
        enabled = 'enable_alerts' in request.form
        email_enabled = 'email_alerts' in request.form
        
        # 更新设置
        settings = {
            'enabled': enabled,
            'email_enabled': email_enabled
        }
        
        success = update_alert_settings(settings)
        
        if success:
            return redirect(url_for('system_monitor.system_monitor'))
        else:
            return render_template('error.html', error='更新告警设置失败')
    except Exception as e:
        logger.error(f"更新告警设置时出错: {str(e)}")
        return render_template('error.html', error=f"更新告警设置时出错: {str(e)}")
