"""
九猫系统内存监控模块
监控系统内存使用情况，防止内存溢出导致系统崩溃
"""
import os
import sys
import logging
import threading
import time
import psutil
import gc
from typing import Callable, Optional, Dict, List, Any

logger = logging.getLogger(__name__)

# 全局变量，用于跟踪内存状态
memory_warning = False
memory_critical = False

class MemoryMonitor:
    """内存监控器，定期检查系统内存使用情况"""

    def __init__(
        self,
        warning_threshold: float = 80.0,  # 内存使用率警告阈值（百分比）
        critical_threshold: float = 90.0,  # 内存使用率危险阈值（百分比）
        check_interval: float = 5.0,  # 检查间隔（秒）
        on_warning: Optional[Callable] = None,  # 警告回调函数
        on_critical: Optional[Callable] = None,  # 危险回调函数
        enabled: bool = True  # 是否启用监控
    ):
        """
        初始化内存监控器

        Args:
            warning_threshold: 内存使用率警告阈值（百分比）
            critical_threshold: 内存使用率危险阈值（百分比）
            check_interval: 检查间隔（秒）
            on_warning: 警告回调函数
            on_critical: 危险回调函数
            enabled: 是否启用监控
        """
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.check_interval = check_interval
        self.on_warning = on_warning or self._default_warning_callback
        self.on_critical = on_critical or self._default_critical_callback
        self.enabled = enabled
        self.running = False
        self.monitor_thread = None

    def start(self) -> None:
        """启动内存监控"""
        if not self.enabled:
            logger.info("内存监控已禁用")
            return

        if self.running:
            logger.warning("内存监控已在运行")
            return

        self.running = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            daemon=True,
            name="MemoryMonitorThread"
        )
        self.monitor_thread.start()
        logger.info(f"内存监控已启动（警告阈值: {self.warning_threshold}%，危险阈值: {self.critical_threshold}%）")

    def stop(self) -> None:
        """停止内存监控"""
        self.running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1.0)
        logger.info("内存监控已停止")

    def _monitor_loop(self) -> None:
        """内存监控循环"""
        global memory_warning, memory_critical

        while self.running:
            try:
                # 获取内存使用情况
                memory_info = psutil.virtual_memory()
                memory_percent = memory_info.percent

                # 记录内存使用情况（仅在DEBUG级别）
                logger.debug(f"内存使用率: {memory_percent:.1f}%")

                # 更新全局内存状态
                old_warning = memory_warning
                old_critical = memory_critical

                # 检查是否超过阈值
                if memory_percent >= self.critical_threshold:
                    memory_critical = True
                    memory_warning = True

                    if not old_critical:  # 只在首次达到危险阈值时记录
                        logger.critical(f"内存使用率达到危险水平: {memory_percent:.1f}%")

                    self.on_critical(memory_percent)
                elif memory_percent >= self.warning_threshold:
                    memory_warning = True
                    memory_critical = False

                    if not old_warning:  # 只在首次达到警告阈值时记录
                        logger.warning(f"内存使用率较高: {memory_percent:.1f}%")

                    self.on_warning(memory_percent)
                else:
                    # 内存恢复正常
                    if memory_warning or memory_critical:
                        logger.info(f"内存使用率恢复正常: {memory_percent:.1f}%")

                    memory_warning = False
                    memory_critical = False

            except Exception as e:
                logger.error(f"内存监控出错: {str(e)}")

            # 等待下一次检查
            time.sleep(self.check_interval)

    def _default_warning_callback(self, memory_percent: float) -> None:
        """默认警告回调函数"""
        # 在警告级别，我们可以尝试释放一些内存
        logger.warning(f"内存使用率较高 ({memory_percent:.1f}%)，尝试释放内存")

        # 尝试清理数据库连接
        try:
            from src.utils.db_optimizer import check_and_cleanup_db_connections

            # 检查并清理数据库连接
            db_cleaned = check_and_cleanup_db_connections(force=False)
            if db_cleaned:
                logger.info("已清理数据库连接")
        except ImportError:
            pass

        # 执行常规内存清理
        self._attempt_memory_cleanup()

    def _default_critical_callback(self, memory_percent: float) -> None:
        """默认危险回调函数"""
        # 在危险级别，我们需要采取更激进的措施
        logger.critical(f"系统内存不足 ({memory_percent:.1f}%)，尝试紧急释放内存")

        # 首先尝试清理数据库连接
        try:
            from src.utils.db_optimizer import check_and_cleanup_db_connections, optimize_db_connection_pool

            # 强制清理数据库连接
            db_cleaned = check_and_cleanup_db_connections(force=True)
            if db_cleaned:
                logger.warning("已强制清理数据库连接")

            # 优化数据库连接池
            db_optimized = optimize_db_connection_pool()
            if db_optimized:
                logger.warning("已优化数据库连接池配置")
        except ImportError:
            logger.warning("无法导入数据库优化模块")

        # 执行常规内存清理
        self._attempt_memory_cleanup(aggressive=True)

        # 如果内存仍然不足，尝试更激进的措施
        if psutil.virtual_memory().percent >= self.critical_threshold:
            logger.critical("内存仍然不足，尝试终止非关键进程")
            self._terminate_non_critical_processes()

            # 再次检查内存
            if psutil.virtual_memory().percent >= self.critical_threshold:
                logger.critical("内存仍然不足，建议手动终止一些进程")

    def _terminate_non_critical_processes(self) -> None:
        """终止非关键进程，释放内存"""
        try:
            # 获取当前进程
            current_process = psutil.Process()

            # 获取子进程
            children = current_process.children(recursive=True)

            if not children:
                logger.info("没有找到可终止的子进程")
                return

            logger.warning(f"找到 {len(children)} 个子进程，尝试终止非关键进程")

            # 按内存使用量排序
            children.sort(key=lambda p: p.memory_info().rss, reverse=True)

            # 终止前3个内存占用最高的子进程
            for i, process in enumerate(children[:3]):
                try:
                    # 获取进程信息
                    process_name = process.name()
                    process_memory = process.memory_info().rss / (1024 * 1024)  # MB

                    # 跳过关键进程
                    if process_name.lower() in ['python.exe', 'pythonw.exe']:
                        continue

                    logger.warning(f"终止进程: {process_name} (PID: {process.pid}, 内存: {process_memory:.1f}MB)")

                    # 终止进程
                    process.terminate()

                    # 等待进程终止
                    try:
                        process.wait(timeout=3)
                    except psutil.TimeoutExpired:
                        # 如果进程没有及时终止，强制终止
                        logger.warning(f"进程 {process_name} 没有响应，强制终止")
                        process.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                    logger.error(f"终止进程时出错: {str(e)}")
                    continue
        except Exception as e:
            logger.error(f"终止非关键进程时出错: {str(e)}")

    def _attempt_memory_cleanup(self, aggressive: bool = False) -> None:
        """
        尝试清理内存

        Args:
            aggressive: 是否使用激进的清理方式
        """
        # 检查是否禁用并行分析
        disable_parallel = os.environ.get('DISABLE_PARALLEL_ANALYSIS', 'False').lower() == 'true'

        # 强制进行垃圾回收
        gc.collect()

        # 清理日志（这是最安全的清理方式）
        self._cleanup_logs(aggressive)

        # 清理临时文件
        self._cleanup_temp_files()

        # 如果并行分析被禁用，或者内存非常紧张，才清理SQLAlchemy缓存
        if disable_parallel or aggressive:
            # 清理SQLAlchemy缓存
            self._cleanup_sqlalchemy_cache()

        if aggressive:
            # 在激进模式下，我们可以尝试清除更多缓存
            # 注意：这可能会影响性能，但可以防止崩溃
            logger.warning("执行激进的内存清理")

            # 清理大型对象
            self._cleanup_large_objects()

            # 如果并行分析被禁用，才清理Python模块缓存
            if disable_parallel:
                # 清理Python模块缓存
                self._cleanup_module_cache()
            else:
                logger.info("保留Python模块缓存以支持并行分析")

            # 如果使用了Numpy，可以清除其缓存
            try:
                import numpy as np
                np.clear_cache()
            except (ImportError, AttributeError):
                pass

            # 如果使用了其他库，可以在这里添加相应的清理代码

    def _cleanup_temp_files(self) -> None:
        """清理临时文件"""
        try:
            import tempfile
            import os
            import glob

            # 获取临时目录
            temp_dir = tempfile.gettempdir()

            # 查找临时目录中的Python相关临时文件
            patterns = [
                os.path.join(temp_dir, "*.pyc"),
                os.path.join(temp_dir, "py*_*.tmp"),
                os.path.join(temp_dir, "tmp*.tmp")
            ]

            # 删除匹配的文件
            deleted_count = 0
            for pattern in patterns:
                for file_path in glob.glob(pattern):
                    try:
                        # 只删除小于1小时的文件
                        if time.time() - os.path.getmtime(file_path) < 3600:
                            os.remove(file_path)
                            deleted_count += 1
                    except (PermissionError, OSError):
                        # 忽略权限错误
                        pass

            if deleted_count > 0:
                logger.info(f"已清理 {deleted_count} 个临时文件")
        except Exception as e:
            logger.error(f"清理临时文件时出错: {str(e)}")

    def _cleanup_sqlalchemy_cache(self) -> None:
        """清理SQLAlchemy缓存"""
        try:
            # 尝试导入SQLAlchemy
            import sqlalchemy

            # 尝试清理会话
            try:
                from src.db.connection import Session
                Session.remove()
                logger.info("已清理SQLAlchemy会话")
            except (ImportError, AttributeError):
                pass

            # 尝试清理引擎缓存
            try:
                from sqlalchemy import pool
                pool._refs.clear()
                logger.info("已清理SQLAlchemy连接池引用")
            except (ImportError, AttributeError):
                pass
        except ImportError:
            # SQLAlchemy未安装，忽略
            pass

    def _cleanup_module_cache(self) -> None:
        """清理Python模块缓存"""
        try:
            # 获取所有已加载模块
            import sys

            # 保留的关键模块
            essential_modules = {
                'sys', 'os', 'time', 'logging', 'threading', 'gc', 'psutil',
                'flask', 'sqlalchemy', 'src', 'config', 'builtins', 'types',
                'importlib', 'site', 'encodings', 'codecs', 'io', 'abc',
                '_weakrefset', 'posixpath', 'genericpath', 'stat', '_collections_abc',
                'enum', 'collections', 'operator', 'keyword', 'functools',
                'copyreg', 're', 'sre_compile', 'sre_parse', 'sre_constants',
                '_sre', 'warnings', 'weakref', 'datetime', 'math', 'typing',
                'concurrent', 'multiprocessing', 'queue', 'json', 'requests',
                'urllib', 'http', 'socket', 'sqlite3', 'numpy', 'pandas',
                'werkzeug', 'jinja2', 'markupsafe', 'itsdangerous', 'click',
                'deepseek', 'analysis', 'novel', 'character', 'plot', 'theme',
                'setting', 'style', 'tone', 'pov', 'narrative', 'symbolism',
                'imagery', 'motif', 'conflict', 'resolution', 'development',
                'relationship', 'worldbuilding', 'magic', 'technology'
            }

            # 要清理的模块
            modules_to_clear = []

            # 查找可以清理的模块（只清理非关键且不是src包的模块）
            for module_name in list(sys.modules.keys()):
                # 检查模块名称是否以关键模块名称开头
                if any(module_name == name or module_name.startswith(name + '.') for name in essential_modules):
                    continue

                # 保留src包中的模块（这些是我们自己的代码）
                if module_name.startswith('src.'):
                    continue

                # 保留分析相关的模块
                if 'analysis' in module_name or 'novel' in module_name:
                    continue

                # 保留并行处理相关的模块
                if 'concurrent' in module_name or 'multiprocessing' in module_name or 'thread' in module_name:
                    continue

                # 添加到清理列表
                modules_to_clear.append(module_name)

            # 清理模块（只在内存非常紧张时才执行）
            if psutil.virtual_memory().percent > 90:
                for module_name in modules_to_clear:
                    try:
                        del sys.modules[module_name]
                    except KeyError:
                        # 模块可能已被其他线程删除
                        pass

                logger.info(f"已清理 {len(modules_to_clear)} 个非关键Python模块")
            else:
                logger.info(f"跳过模块清理，保留 {len(modules_to_clear)} 个非关键模块以支持并行分析")
        except Exception as e:
            logger.error(f"清理Python模块缓存时出错: {str(e)}")

    def _cleanup_logs(self, aggressive: bool = False) -> None:
        """
        清理日志，减少内存占用

        Args:
            aggressive: 是否使用激进的清理方式
        """
        try:
            # 导入日志过滤器
            from src.utils.log_filter import filter_logs, clear_duplicate_count

            # 获取全局日志变量
            # 注意：这里假设日志存储在app.py中的analysis_logs字典中
            # 如果实际存储位置不同，需要相应调整
            try:
                from src.web.app import analysis_logs

                # 记录清理前的日志数量
                total_logs_before = sum(len(logs) for logs in analysis_logs.values())

                # 对每个小说的日志进行过滤
                for novel_id, logs in list(analysis_logs.items()):
                    # 过滤日志
                    filtered_logs = filter_logs(
                        logs,
                        novel_id,
                        max_logs=500 if not aggressive else 100,  # 在激进模式下保留更少的日志
                        memory_critical=aggressive
                    )

                    # 更新日志
                    analysis_logs[novel_id] = filtered_logs

                # 清除重复日志计数
                clear_duplicate_count()

                # 记录清理后的日志数量
                total_logs_after = sum(len(logs) for logs in analysis_logs.values())

                # 记录清理结果
                if total_logs_before > total_logs_after:
                    logger.info(f"日志清理完成: 从 {total_logs_before} 条减少到 {total_logs_after} 条")

            except (ImportError, AttributeError) as e:
                logger.warning(f"无法访问分析日志: {str(e)}")

        except Exception as e:
            logger.error(f"清理日志时出错: {str(e)}")

    def _cleanup_large_objects(self) -> None:
        """清理大型对象，释放内存"""
        try:
            # 获取当前所有对象
            all_objects = gc.get_objects()

            # 找出大型对象（大于10MB）
            large_objects = []
            for obj in all_objects:
                try:
                    size = sys.getsizeof(obj)
                    if size > 10 * 1024 * 1024:  # 大于10MB
                        large_objects.append((obj, size))
                except Exception:
                    pass

            # 记录大型对象信息
            if large_objects:
                logger.warning(f"发现 {len(large_objects)} 个大型对象")
                for i, (obj, size) in enumerate(large_objects[:5]):  # 只显示前5个
                    logger.warning(f"大型对象 #{i+1}: 类型={type(obj).__name__}, 大小={self.format_bytes(size)}")

                # 尝试释放一些大型对象
                # 注意：这里只是示例，实际应用中需要更谨慎地处理
                # 例如，可以清除缓存、临时数据等

                # 强制垃圾回收
                gc.collect()

        except Exception as e:
            logger.error(f"清理大型对象时出错: {str(e)}")

    @staticmethod
    def get_memory_usage() -> dict:
        """
        获取当前内存使用情况

        Returns:
            包含内存使用信息的字典
        """
        memory = psutil.virtual_memory()
        process = psutil.Process(os.getpid())
        process_memory = process.memory_info()

        return {
            "system_total": memory.total,
            "system_available": memory.available,
            "system_used": memory.used,
            "system_percent": memory.percent,
            "process_rss": process_memory.rss,  # 物理内存使用
            "process_vms": process_memory.vms,  # 虚拟内存使用
        }

    @staticmethod
    def format_bytes(bytes_value: int) -> str:
        """
        将字节数格式化为人类可读的形式

        Args:
            bytes_value: 字节数

        Returns:
            格式化后的字符串
        """
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} PB"

    @staticmethod
    def print_memory_status() -> None:
        """打印当前内存状态"""
        memory_info = MemoryMonitor.get_memory_usage()

        print("\n===== 内存使用状况 =====")
        print(f"系统总内存: {MemoryMonitor.format_bytes(memory_info['system_total'])}")
        print(f"系统可用内存: {MemoryMonitor.format_bytes(memory_info['system_available'])}")
        print(f"系统内存使用率: {memory_info['system_percent']:.1f}%")
        print(f"当前进程物理内存: {MemoryMonitor.format_bytes(memory_info['process_rss'])}")
        print(f"当前进程虚拟内存: {MemoryMonitor.format_bytes(memory_info['process_vms'])}")
        print("========================\n")

        # 保存内存状态到外部存储
        try:
            from src.utils.external_storage import save_memory_stats
            # 添加时间戳
            memory_info['timestamp'] = time.time()
            save_memory_stats(memory_info)
        except ImportError:
            # 外部存储模块不可用
            pass

# 创建全局内存监控器实例
monitor = MemoryMonitor()

def start_memory_monitoring(
    warning_threshold: float = 80.0,
    critical_threshold: float = 90.0,
    check_interval: float = 5.0
) -> None:
    """
    启动内存监控

    Args:
        warning_threshold: 内存使用率警告阈值（百分比）
        critical_threshold: 内存使用率危险阈值（百分比）
        check_interval: 检查间隔（秒）
    """
    global monitor
    monitor = MemoryMonitor(
        warning_threshold=warning_threshold,
        critical_threshold=critical_threshold,
        check_interval=check_interval
    )
    monitor.start()

def stop_memory_monitoring() -> None:
    """停止内存监控"""
    global monitor
    if monitor:
        monitor.stop()

def get_memory_usage() -> str:
    """
    获取当前内存使用情况的格式化字符串

    Returns:
        格式化的内存使用情况字符串
    """
    memory_info = MemoryMonitor.get_memory_usage()

    # 格式化内存信息
    system_total_gb = memory_info['system_total'] / (1024 * 1024 * 1024)
    system_used_gb = memory_info['system_used'] / (1024 * 1024 * 1024)
    process_mb = memory_info['process_rss'] / (1024 * 1024)

    return f"系统内存: {system_total_gb:.2f} GB, 使用率: {memory_info['system_percent']}%, 进程内存: {process_mb:.2f} MB"
