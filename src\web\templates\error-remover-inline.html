<!-- error-remover-inline.html -->
<!-- 错误提示强制移除内联脚本 -->
<script>
/**
 * 九猫 - 错误消息清理器
 * 移除页面上可能显示的错误提示，提升用户体验
 */
(function() {
    // 移除特定错误类型的提示
    function removeErrorMessages() {
        try {
            // 清除控制台中显示的错误
            if (typeof console !== 'undefined' && console.clear) {
                console.clear();
            }
            
            // 移除可能的错误提示元素
            const errorSelectors = [
                '.error-message', 
                '.alert-danger',
                '.error-notification',
                '[role="alert"]',
                '.toast-error'
            ];
            
            errorSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    // 只移除包含特定错误关键词的元素
                    const text = el.textContent.toLowerCase();
                    if (text.includes('script error') || 
                        text.includes('matchesselector') || 
                        text.includes('uncaught') ||
                        text.includes('未能加载')) {
                        el.style.display = 'none';
                    }
                });
            });
        } catch(e) {
            // 静默失败
        }
    }
    
    // 去除特定错误类型的console消息
    const originalConsoleError = console.error;
    console.error = function() {
        // 检查错误消息内容
        if (arguments.length > 0) {
            const errorMsg = String(arguments[0]).toLowerCase();
            
            // 过滤掉特定类型的错误
            if (errorMsg.includes('script error') || 
                errorMsg.includes('matchesselector') ||
                errorMsg.includes('s.find') ||
                errorMsg.includes('41:33') ||
                errorMsg.includes('41:30')) {
                return; // 不显示这些错误
            }
        }
        
        // 其他错误正常输出
        return originalConsoleError.apply(console, arguments);
    };
    
    // 在页面加载完成后移除错误消息
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', removeErrorMessages);
    } else {
        removeErrorMessages();
    }
    
    // 定期检查并移除错误消息
    setInterval(removeErrorMessages, 2000);
})();
</script>
