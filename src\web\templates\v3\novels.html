{% extends "v3/base.html" %}

{% block title %}小说列表 - 九猫小说分析写作系统v3.0{% endblock %}

{% block extra_css %}
<style>
    .novel-card {
        transition: all 0.3s ease;
        height: 100%;
    }

    .novel-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px var(--shadow-color);
    }

    .novel-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1.25rem;
    }

    .novel-meta {
        color: var(--text-color);
        opacity: 0.8;
        font-size: 0.9rem;
    }

    .novel-stats {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid var(--border-color);
    }

    .novel-stat {
        text-align: center;
    }

    .novel-stat-value {
        font-weight: 600;
        font-size: 1.1rem;
        color: var(--primary-color);
    }

    .novel-stat-label {
        font-size: 0.8rem;
        opacity: 0.7;
    }

    .filter-card {
        margin-bottom: 1.5rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        background-color: var(--card-bg);
        border-radius: 0.75rem;
        border: 1px dashed var(--border-color);
    }

    .empty-icon {
        font-size: 3rem;
        color: var(--border-color);
        margin-bottom: 1rem;
    }

    /* 批量操作样式 */
    .batch-toolbar {
        position: sticky;
        top: 0;
        z-index: 100;
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .batch-mode .novel-card {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .batch-mode .novel-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }

    .batch-mode .novel-card.selected {
        border: 2px solid var(--primary-color);
        background-color: rgba(var(--primary-color-rgb), 0.05);
    }

    .batch-checkbox {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 10;
        background: white;
        border-radius: 50%;
        padding: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2"><i class="fas fa-book text-primary me-2"></i>小说列表</h1>
                        <p class="lead mb-0">管理您上传的所有小说，查看分析结果和章节内容。</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" id="toggleBatchModeBtn">
                            <i class="fas fa-check-square me-1"></i>批量管理
                        </button>
                        <a href="{{ url_for('v3.upload_novel') }}" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>上传新小说
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 筛选器 -->
    <div class="col-lg-3 mb-4">
        <div class="card shadow-sm filter-card">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-filter me-2"></i>筛选</h3>
            </div>
            <div class="card-body">
                <form id="filterForm">
                    <div class="mb-3">
                        <label for="searchTitle" class="form-label">标题搜索</label>
                        <input type="text" class="form-control" id="searchTitle" placeholder="输入小说标题...">
                    </div>
                    <div class="mb-3">
                        <label for="sortBy" class="form-label">排序方式</label>
                        <select class="form-select" id="sortBy">
                            <option value="newest">最新上传</option>
                            <option value="oldest">最早上传</option>
                            <option value="wordCount">字数最多</option>
                            <option value="title">标题排序</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label d-block">分析状态</label>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="statusAll" checked>
                            <label class="form-check-label" for="statusAll">全部</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="statusFullyAnalyzed">
                            <label class="form-check-label" for="statusFullyAnalyzed">全部维度已分析</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="statusPartiallyAnalyzed">
                            <label class="form-check-label" for="statusPartiallyAnalyzed">部分维度已分析</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="statusTemplate">
                            <label class="form-check-label" for="statusTemplate">参考蓝本</label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i>应用筛选
                    </button>
                </form>
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>统计</h3>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-3">
                    <span>小说总数</span>
                    <span class="badge bg-primary">{{ novels|length }}</span>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <span>总字数</span>
                    <span class="badge bg-success">{{ novels|sum(attribute='word_count')|default(0)|format_number }}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>平均字数</span>
                    <span class="badge bg-info">
                        {% if novels|length > 0 %}
                            {{ (novels|sum(attribute='word_count') / novels|length)|int|format_number }}
                        {% else %}
                            0
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 小说列表 -->
    <div class="col-lg-9">
        {% if novels %}
            <div class="row">
                {% for novel in novels %}
                <div class="col-md-6 col-lg-4 mb-4">
                    {% set analysis_count = novel.analysis_results|length if novel.analysis_results else 0 %}
                    {% set total_dimensions = dimensions|length %}
                    {% set is_fully_analyzed = novel.is_fully_analyzed %}
                    {% set is_partially_analyzed = novel.is_partially_analyzed %}
                    {% set is_template = novel.novel_metadata and novel.novel_metadata.get('is_template', False) %}

                    <div class="card novel-card shadow-sm" data-novel-id="{{ novel.id }}">
                        <div class="card-body"
                             data-is-template="{{ is_template }}"
                             data-is-fully-analyzed="{{ is_fully_analyzed }}"
                             data-is-partially-analyzed="{{ is_partially_analyzed }}"
                             data-analysis-count="{{ analysis_count }}"
                             data-total-dimensions="{{ total_dimensions }}">

                            <div class="position-absolute top-0 end-0 mt-2 me-2">
                                {% if is_template %}
                                <span class="badge bg-warning me-1" style="background-color: #ff9800 !important;">
                                    <i class="fas fa-bookmark me-1"></i>参考蓝本
                                </span>
                                {% endif %}

                                {% if is_fully_analyzed %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle me-1"></i>全部维度已分析
                                </span>
                                {% elif is_partially_analyzed %}
                                <span class="badge bg-info">
                                    <i class="fas fa-spinner me-1"></i>部分维度已分析
                                </span>
                                {% endif %}
                            </div>
                            <h3 class="novel-title">{{ novel.title }}</h3>
                            <div class="novel-meta">
                                <div><i class="fas fa-user me-1"></i>{{ novel.author or '未知作者' }}</div>
                                <div><i class="fas fa-calendar-alt me-1"></i>{{ novel.created_at.strftime('%Y-%m-%d') }}</div>
                            </div>
                            <div class="novel-stats">
                                <div class="novel-stat">
                                    <div class="novel-stat-value">{{ novel.word_count|format_number }}</div>
                                    <div class="novel-stat-label">字数</div>
                                </div>
                                <div class="novel-stat">
                                    <div class="novel-stat-value">{{ novel.chapters|length }}</div>
                                    <div class="novel-stat-label">章节</div>
                                </div>
                                <div class="novel-stat">
                                    <div class="novel-stat-value">
                                        {% set analysis_count = novel.analysis_results|length if novel.analysis_results else 0 %}
                                        {{ analysis_count }}
                                    </div>
                                    <div class="novel-stat-label">分析维度</div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex gap-2">
                                <a href="{{ url_for('v3.view_novel', novel_id=novel.id) }}" class="btn btn-primary flex-grow-1">
                                    <i class="fas fa-eye me-1"></i>查看详情
                                </a>
                                {% if novel.can_set_as_template and not (novel.novel_metadata and novel.novel_metadata.get('is_template', False)) %}
                                <button class="btn btn-success set-template-btn" data-novel-id="{{ novel.id }}" data-novel-title="{{ novel.title }}">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                                {% endif %}
                                <button class="btn btn-danger delete-novel-btn" data-novel-id="{{ novel.id }}" data-novel-title="{{ novel.title }}">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-book-open"></i>
                </div>
                <h3>暂无小说</h3>
                <p class="text-muted">您还没有上传任何小说，点击下方按钮上传您的第一本小说。</p>
                <a href="{{ url_for('v3.upload_novel') }}" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i>上传小说
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/v3/batch-operations.js') }}"></script>
<script>
    $(document).ready(function() {
        // 批量管理模式切换
        let batchMode = false;

        $('#toggleBatchModeBtn').click(function() {
            batchMode = !batchMode;

            if (batchMode) {
                // 进入批量模式
                $(this).html('<i class="fas fa-times me-1"></i>退出批量管理');
                $(this).removeClass('btn-outline-primary').addClass('btn-warning');
                $('.col-lg-9').addClass('batch-mode');

                // 为每个小说卡片添加选择框
                $('.novel-card').each(function() {
                    const novelId = $(this).data('novel-id');
                    if (!$(this).find('.batch-checkbox').length) {
                        $(this).prepend(`
                            <div class="batch-checkbox">
                                <input type="checkbox" class="form-check-input item-checkbox" value="${novelId}" id="novel_${novelId}">
                            </div>
                        `);
                    }
                });

                // 初始化批量操作
                if (window.batchOperations) {
                    window.batchOperations.addCheckboxesToItems('novels');
                }

                // 添加卡片点击事件
                $('.novel-card').off('click.batch').on('click.batch', function(e) {
                    if ($(e.target).is('a, button, input') || $(e.target).closest('a, button').length) {
                        return; // 不处理链接和按钮的点击
                    }

                    const checkbox = $(this).find('.item-checkbox');
                    checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
                });

            } else {
                // 退出批量模式
                $(this).html('<i class="fas fa-check-square me-1"></i>批量管理');
                $(this).removeClass('btn-warning').addClass('btn-outline-primary');
                $('.col-lg-9').removeClass('batch-mode');

                // 移除选择框
                $('.batch-checkbox').remove();
                $('.novel-card').removeClass('selected');

                // 隐藏批量工具栏
                $('#batchToolbar').hide();

                // 移除卡片点击事件
                $('.novel-card').off('click.batch');

                // 清理批量操作状态
                if (window.batchOperations) {
                    window.batchOperations.cancelSelection();
                }
            }
        });

        // 监听选择框变化，更新卡片样式
        $(document).on('change', '.item-checkbox', function() {
            const card = $(this).closest('.novel-card');
            if ($(this).is(':checked')) {
                card.addClass('selected');
            } else {
                card.removeClass('selected');
            }
        });
        // 格式化数字的辅助函数
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        // 删除小说按钮点击事件
        $(".delete-novel-btn").on("click", function() {
            const novelId = $(this).data("novel-id");
            const novelTitle = $(this).data("novel-title");

            if (confirm(`确定要删除小说《${novelTitle}》吗？此操作不可恢复！`)) {
                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在删除小说，请稍候...</h5>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);

                // 发送AJAX请求
                $.ajax({
                    url: `/api/novel/${novelId}/delete`,
                    type: 'POST',
                    contentType: 'application/json',
                    success: function(response) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        if (response.success) {
                            alert(`小说《${novelTitle}》已成功删除`);
                            // 刷新页面
                            location.reload();
                        } else {
                            alert(`删除小说失败: ${response.message}`);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        alert(`删除小说失败: ${error}`);
                    }
                });
            }
        });

        // 设为参考蓝本按钮点击事件
        $(".set-template-btn").on("click", function() {
            const novelId = $(this).data("novel-id");
            const novelTitle = $(this).data("novel-title");
            const $btn = $(this);
            const originalHtml = $btn.html();

            if (confirm(`确定要将小说《${novelTitle}》设为参考蓝本吗？\n\n设置后可在控制台中使用该参考蓝本。`)) {
                // 显示加载状态
                $btn.html('<i class="fas fa-spinner fa-spin"></i>');
                $btn.prop('disabled', true);

                // 显示加载提示
                const loadingHtml = `
                    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="loadingOverlay">
                        <div class="card p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在设置参考蓝本，请稍候...</h5>
                            <p class="text-muted small">设置完成后可在控制台中使用该参考蓝本</p>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);

                // 发送AJAX请求
                $.ajax({
                    url: `/api/novel/${novelId}/set_as_template`,
                    type: 'POST',
                    data: JSON.stringify({
                        novel_id: novelId
                    }),
                    contentType: 'application/json',
                    success: function(response) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        if (response.success) {
                            // 显示成功提示
                            const successHtml = `
                                <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999;" id="successOverlay">
                                    <div class="card p-4 text-center">
                                        <div class="text-success mb-3">
                                            <i class="fas fa-check-circle fa-3x"></i>
                                        </div>
                                        <h5>小说《${novelTitle}》已成功设为参考蓝本</h5>
                                        <p class="text-muted">您现在可以在控制台中使用该参考蓝本</p>
                                        <div class="mt-3">
                                            <button class="btn btn-primary me-2" id="goToConsoleBtn">
                                                <i class="fas fa-terminal me-1"></i>前往控制台
                                            </button>
                                            <button class="btn btn-secondary" id="stayHereBtn">
                                                <i class="fas fa-times me-1"></i>关闭
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `;
                            $('body').append(successHtml);

                            // 绑定按钮事件
                            $('#goToConsoleBtn').click(function() {
                                window.location.href = `/v3/console?template_id=${novelId}`;
                            });

                            $('#stayHereBtn').click(function() {
                                $('#successOverlay').remove();
                                // 添加时间戳参数，防止浏览器缓存
                                const timestamp = new Date().getTime();
                                const url = new URL(window.location.href);
                                url.searchParams.set('_', timestamp);
                                // 刷新页面
                                window.location.href = url.toString();
                            });
                        } else {
                            alert(`设置参考蓝本失败: ${response.message || response.error}`);
                            // 恢复按钮状态
                            $btn.html(originalHtml);
                            $btn.prop('disabled', false);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 移除加载提示
                        $('#loadingOverlay').remove();

                        // 恢复按钮状态
                        $btn.html(originalHtml);
                        $btn.prop('disabled', false);

                        alert(`设置参考蓝本失败: ${error}`);
                    }
                });
            }
        });

        // 筛选功能
        $("#filterForm").on("submit", function(e) {
            e.preventDefault();

            const searchTitle = $("#searchTitle").val().toLowerCase();
            const sortBy = $("#sortBy").val();
            const showAll = $("#statusAll").is(":checked");
            const showFullyAnalyzed = $("#statusFullyAnalyzed").is(":checked");
            const showPartiallyAnalyzed = $("#statusPartiallyAnalyzed").is(":checked");
            const showTemplate = $("#statusTemplate").is(":checked");

            // 获取所有小说卡片
            const $novelCards = $(".novel-card").parent();

            // 先隐藏所有小说卡片
            $novelCards.hide();

            // 筛选小说卡片
            $novelCards.each(function() {
                const $card = $(this);
                const title = $card.find(".novel-title").text().toLowerCase();
                const $cardBody = $card.find(".card-body");
                // 获取数据属性并确保它们是布尔值
                const isFullyAnalyzed = String($cardBody.data("is-fully-analyzed")).toLowerCase() === "true";
                const isPartiallyAnalyzed = String($cardBody.data("is-partially-analyzed")).toLowerCase() === "true";
                const isTemplate = String($cardBody.data("is-template")).toLowerCase() === "true";

                // 调试信息
                console.log(`小说: ${title}, 全部维度已分析: ${isFullyAnalyzed}, 部分维度已分析: ${isPartiallyAnalyzed}, 是参考蓝本: ${isTemplate}`);

                // 标题筛选
                let showByTitle = true;
                if (searchTitle) {
                    showByTitle = title.includes(searchTitle);
                }

                // 状态筛选
                let showByStatus = true;
                if (!showAll) {
                    if (showFullyAnalyzed && !showPartiallyAnalyzed && !showTemplate) {
                        // 只显示全部维度已分析的
                        showByStatus = isFullyAnalyzed;
                    } else if (!showFullyAnalyzed && showPartiallyAnalyzed && !showTemplate) {
                        // 只显示部分维度已分析的
                        showByStatus = isPartiallyAnalyzed;
                    } else if (!showFullyAnalyzed && !showPartiallyAnalyzed && showTemplate) {
                        // 只显示参考蓝本
                        showByStatus = isTemplate;
                    } else if (showFullyAnalyzed && showPartiallyAnalyzed && !showTemplate) {
                        // 显示任何已分析的（全部或部分）
                        showByStatus = isFullyAnalyzed || isPartiallyAnalyzed;
                    } else if (showFullyAnalyzed && !showPartiallyAnalyzed && showTemplate) {
                        // 显示全部维度已分析的或参考蓝本
                        showByStatus = isFullyAnalyzed || isTemplate;
                    } else if (!showFullyAnalyzed && showPartiallyAnalyzed && showTemplate) {
                        // 显示部分维度已分析的或参考蓝本
                        showByStatus = isPartiallyAnalyzed || isTemplate;
                    } else if (showFullyAnalyzed && showPartiallyAnalyzed && showTemplate) {
                        // 显示任何已分析的或参考蓝本
                        showByStatus = isFullyAnalyzed || isPartiallyAnalyzed || isTemplate;
                    } else {
                        // 没有选择任何状态
                        showByStatus = false;
                    }
                }

                // 如果符合筛选条件，显示卡片
                if (showByTitle && showByStatus) {
                    $card.show();
                }
            });

            // 排序小说卡片
            const $novelList = $(".col-lg-9 .row");
            const $novels = $novelList.children().detach();

            $novels.sort(function(a, b) {
                const $a = $(a);
                const $b = $(b);

                if (sortBy === "newest") {
                    const dateA = new Date($a.find(".novel-meta div:eq(1)").text().replace("​", ""));
                    const dateB = new Date($b.find(".novel-meta div:eq(1)").text().replace("​", ""));
                    return dateB - dateA;
                } else if (sortBy === "oldest") {
                    const dateA = new Date($a.find(".novel-meta div:eq(1)").text().replace("​", ""));
                    const dateB = new Date($b.find(".novel-meta div:eq(1)").text().replace("​", ""));
                    return dateA - dateB;
                } else if (sortBy === "wordCount") {
                    const wordCountA = parseInt($a.find(".novel-stat-value").eq(0).text().replace(/,/g, ""));
                    const wordCountB = parseInt($b.find(".novel-stat-value").eq(0).text().replace(/,/g, ""));
                    return wordCountB - wordCountA;
                } else if (sortBy === "title") {
                    const titleA = $a.find(".novel-title").text().toLowerCase();
                    const titleB = $b.find(".novel-title").text().toLowerCase();
                    return titleA.localeCompare(titleB);
                }

                return 0;
            });

            $novelList.append($novels);

            // 显示筛选结果统计
            const visibleCount = $novels.filter(":visible").length;
            const totalCount = $novels.length;

            // 添加筛选结果提示
            if ($("#filterResultInfo").length === 0) {
                $(".col-lg-9").prepend(
                    '<div id="filterResultInfo" class="alert alert-info mb-4">' +
                    '<i class="fas fa-filter me-2"></i>' +
                    '<span></span>' +
                    '</div>'
                );
            }

            $("#filterResultInfo span").text(`显示 ${visibleCount}/${totalCount} 本小说`);

            // 如果没有符合条件的小说，显示提示
            if (visibleCount === 0) {
                if ($("#noResultsMessage").length === 0) {
                    $(".col-lg-9 .row").after(
                        '<div id="noResultsMessage" class="empty-state">' +
                        '<div class="empty-icon"><i class="fas fa-search"></i></div>' +
                        '<h3>没有符合条件的小说</h3>' +
                        '<p class="text-muted">请尝试调整筛选条件</p>' +
                        '<button class="btn btn-outline-primary" id="resetFilterBtn">' +
                        '<i class="fas fa-undo me-1"></i>重置筛选' +
                        '</button>' +
                        '</div>'
                    );
                } else {
                    $("#noResultsMessage").show();
                }
            } else {
                $("#noResultsMessage").hide();
            }
        });

        // 重置筛选按钮点击事件
        $(document).on("click", "#resetFilterBtn", function() {
            $("#searchTitle").val("");
            $("#sortBy").val("newest");
            $("#statusAll").prop("checked", true);
            $("#statusFullyAnalyzed, #statusPartiallyAnalyzed, #statusTemplate").prop("checked", false);

            // 显示所有小说卡片
            $(".novel-card").parent().show();

            // 移除筛选结果提示
            $("#filterResultInfo").remove();
            $("#noResultsMessage").hide();
        });

        // 状态复选框联动
        $("#statusAll").on("change", function() {
            if ($(this).is(":checked")) {
                $("#statusFullyAnalyzed, #statusPartiallyAnalyzed, #statusTemplate").prop("checked", false);
            }
        });

        $("#statusFullyAnalyzed, #statusPartiallyAnalyzed, #statusTemplate").on("change", function() {
            if ($(this).is(":checked")) {
                $("#statusAll").prop("checked", false);
            }

            if (!$("#statusFullyAnalyzed").is(":checked") && !$("#statusPartiallyAnalyzed").is(":checked") && !$("#statusTemplate").is(":checked")) {
                $("#statusAll").prop("checked", true);
            }
        });
    });
</script>
{% endblock %}
