# 临时文件目录 - 由optimize_nine_cats.py添加
TEMP_DIR = "E:\\艹，又来一次\\九猫\\nine_cats_temp"
CACHE_DIR = "E:\\艹，又来一次\\九猫\\nine_cats_temp\\cache"
LOG_DIR = "E:\\艹，又来一次\\九猫\\nine_cats_temp\\logs"
ANALYSIS_TEMP_DIR = "E:\\艹，又来一次\\九猫\\nine_cats_temp\\analysis"

# 内存优化设置 - 由optimize_nine_cats.py添加
CHUNK_SIZE = 1024 * 1024  # 1MB
MAX_CACHE_SIZE = 100 * 1024 * 1024  # 100MB
ENABLE_MEMORY_OPTIMIZATION = True

"""
Configuration settings for the 九猫 (Nine Cats) novel analysis system.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

# 分块大小设置
MAX_CHUNK_SIZE = int(os.getenv("MAX_CHUNK_SIZE", "10000"))  # 最大分块大小

# 阿里云API配置
# 支持多个模型的API密钥配置

# DeepSeek R1 API配置
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY", "***********************************")
if deepseek_api_key.startswith("Bearer "):
    deepseek_api_key = deepseek_api_key[7:]  # 移除Bearer前缀
DEEPSEEK_API_KEY = deepseek_api_key
# 确保使用最新的API密钥
print(f"使用DeepSeek R1 API密钥: {DEEPSEEK_API_KEY[:6]}...")

# 通义千问-Plus-Latest API配置
qwen_api_key = os.getenv("QWEN_API_KEY", "sk-6f3b4c6ad9f64f78b22bed422c5d278d")
if qwen_api_key.startswith("Bearer "):
    qwen_api_key = qwen_api_key[7:]  # 移除Bearer前缀
QWEN_API_KEY = qwen_api_key

# 通义千问-QVQ-Max API配置
qwen_qvq_api_key = os.getenv("QWEN_QVQ_API_KEY", "sk-461690d08ac64555a16e195aff80edd4")
if qwen_qvq_api_key.startswith("Bearer "):
    qwen_qvq_api_key = qwen_qvq_api_key[7:]  # 移除Bearer前缀
QWEN_QVQ_API_KEY = qwen_qvq_api_key

# 确保API基础URL正确 - 阿里云API
DASHSCOPE_API_BASE_URL = os.getenv("DASHSCOPE_API_BASE_URL", "https://dashscope.aliyuncs.com/api/v1")

# 默认模型配置
DEFAULT_MODEL = os.getenv("DEFAULT_MODEL", "deepseek-r1")  # 默认使用DeepSeek R1模型

# 支持的模型列表
SUPPORTED_MODELS = {
    "deepseek-r1": {
        "name": "DeepSeek R1",
        "api_key": DEEPSEEK_API_KEY,
        "endpoint": f"{DASHSCOPE_API_BASE_URL}/services/aigc/text-generation/generation",
        "max_tokens": 12000,
        "description": "阿里云DeepSeek R1模型，适合长文本分析"
    },
    "qwen-plus-latest": {
        "name": "通义千问-Plus-Latest",
        "api_key": QWEN_API_KEY,
        "endpoint": f"{DASHSCOPE_API_BASE_URL}/services/aigc/text-generation/generation",
        "max_tokens": 12000,
        "description": "阿里云通义千问-Plus-Latest模型，适合中文文本分析"
    },
    "qwen-qvq-max": {
        "name": "通义千问-QVQ-Max",
        "api_key": QWEN_QVQ_API_KEY,
        "endpoint": f"{DASHSCOPE_API_BASE_URL}/services/aigc/text-generation/generation",
        "max_tokens": 8192,  # 根据API错误信息，max_tokens不能超过8192
        "description": "阿里云通义千问-QVQ-Max模型，适合高质量中文文本生成"
    }
}

# API调用频率限制设置
API_CALL_LIMIT_PER_HOUR = int(os.getenv("API_CALL_LIMIT_PER_HOUR", "100"))  # 每小时API调用限制
API_CALL_WINDOW_SECONDS = int(os.getenv("API_CALL_WINDOW_SECONDS", "3600"))  # API调用时间窗口（秒）
API_RATE_LIMIT_ENABLED = os.getenv("API_RATE_LIMIT_ENABLED", "True").lower() in ("true", "1", "t")  # 是否启用API调用频率限制

# API费用设置 - 阿里云DeepSeek R1 API计费规则
API_COST_INPUT_PER_1K_TOKENS = float(os.getenv("API_COST_INPUT_PER_1K_TOKENS", "0.004"))  # 输入：每千tokens约0.004元
API_COST_OUTPUT_PER_1K_TOKENS = float(os.getenv("API_COST_OUTPUT_PER_1K_TOKENS", "0.016"))  # 输出：每千tokens约0.016元

# Application settings
# 开启DEBUG模式，以便查看更多日志信息
# DEBUG = False  # 注释掉硬编码 DEBUG，使用环境变量控制模式
DEBUG = False  # 强制设置为False，以确保真实调用API而不是模拟分析
FORCE_REANALYSIS = True  # 强制重新分析，不使用缓存
FORCE_REAL_API = True  # 强制使用真实API调用，即使在DEBUG模式下
SECRET_KEY = os.getenv("SECRET_KEY", "dev-key-change-in-production")
PORT = int(os.getenv("PORT", "5001"))
HOST = os.getenv("HOST", "0.0.0.0")

# Database settings
DATABASE_URI = os.getenv("DATABASE_URI", "sqlite:///novels.db")

# Analysis settings
OVERLAP_SIZE = int(os.getenv("OVERLAP_SIZE", "100"))  # Overlap between chunks for context (reduced from 200)
MAX_NOVEL_SIZE = int(os.getenv("MAX_NOVEL_SIZE", "3000000"))  # Maximum novel size in characters
MAX_PARALLEL_ANALYSES = int(os.getenv("MAX_PARALLEL_ANALYSES", "6"))  # Maximum number of parallel analyses (one per dimension) - 温和提升并行度
MAX_CHUNK_WORKERS = int(os.getenv("MAX_CHUNK_WORKERS", "8"))  # Maximum number of parallel workers per dimension - 温和破除串行化瓶颈
PARALLEL_ANALYSIS_ENABLED = os.getenv("PARALLEL_ANALYSIS_ENABLED", "True").lower() in ("true", "1", "t")  # 是否启用并行分析

# 不同维度的分块大小配置 - 优化API调用效率
DIMENSION_CHUNK_SIZES = {
    "language_style": 10000,  # 语言风格分析需要更大的上下文
    "rhythm_pacing": 10000,  # 节奏分析需要更大的上下文
    "structure": 12000,  # 结构分析需要更大的上下文
    "sentence_variation": 8000,  # 句式变化分析
    "paragraph_length": 8000,  # 段落长度分析
    "perspective_shifts": 8000,  # 视角转换分析
    "paragraph_flow": 8000,  # 段落流畅度分析
    "novel_characteristics": 10000,  # 小说特点分析需要非常详细的输出
    "world_building": 10000,  # 世界构建分析需要非常详细的输出
    "chapter_outline": 10000,  # 章节大纲分析需要非常详细的输出
    "character_relationships": 10000,  # 人物关系分析需要非常详细的输出
    "opening_effectiveness": 8000,  # 开篇效果分析
    "climax_pacing": 8000,  # 高潮节奏分析
    "outline_analysis": 12000,  # 大纲分析需要更大的上下文
    "popular_tropes": 10000,  # 热梗统计需要更大的上下文
    "combine_chapter_outline": 12000,  # 合并章节大纲分析需要更大的输出空间
    "combine_character_relationships": 12000,  # 合并人物关系分析需要更大的输出空间
    "comprehensive_report": 12000,  # 综合报告需要更大的输出空间
    "default": 8000  # 默认值大幅增加
}
# 不同维度的最大输出token配置 - 超大幅增加以获取极其详细的分析结果
DIMENSION_MAX_TOKENS = {
    "language_style": 15000,  # 语言风格分析需要极其详细的输出
    "rhythm_pacing": 15000,  # 节奏分析需要极其详细的输出
    "structure": 18000,  # 结构分析需要极其详细的输出
    "sentence_variation": 15000,  # 句式变化分析需要极其详细的输出
    "paragraph_length": 15000,  # 段落长度分析需要极其详细的输出
    "perspective_shifts": 15000,  # 视角转换分析需要极其详细的输出
    "paragraph_flow": 15000,  # 段落流畅度分析需要极其详细的输出
    "novel_characteristics": 18000,  # 小说特点分析需要极其详细的输出
    "world_building": 18000,  # 世界构建分析需要极其详细的输出
    "chapter_outline": 25000,  # 章节大纲分析需要极其详细的输出，不受字数限制
    "character_relationships": 18000,  # 人物关系分析需要极其详细的输出
    "opening_effectiveness": 15000,  # 开篇效果分析需要极其详细的输出
    "climax_pacing": 15000,  # 高潮节奏分析需要极其详细的输出
    "outline_analysis": 20000,  # 大纲分析需要极其详细的输出
    "popular_tropes": 18000,  # 热梗统计需要极其详细的输出
    "combine_chapter_outline": 20000,  # 合并章节大纲分析需要极其详细的输出
    "combine_character_relationships": 20000,  # 合并人物关系分析需要极其详细的输出
    "comprehensive_report": 20000,  # 综合报告需要极其详细的输出
    "default": 15000  # 默认值超大幅增加
}

# 缓存设置
CACHE_ENABLED = False  # 完全禁用缓存，确保使用最新的提示词模板
CACHE_VALID_DAYS = int(os.getenv("CACHE_VALID_DAYS", "7"))  # 缓存有效期（天）
FORCE_REFRESH_CACHE = True  # 强制刷新缓存，确保使用最新的提示词模板
FORCE_REANALYSIS = True  # 强制重新分析，不使用缓存的结果

# 高级缓存设置 - 优化API调用效率
CACHE_INTERMEDIATE_RESULTS = False  # 禁用中间分析结果缓存
CACHE_REUSE_ACROSS_DIMENSIONS = False  # 禁止跨维度复用结果，确保每个维度使用专门的提示词
INTERMEDIATE_CACHE_VALID_DAYS = int(os.getenv("INTERMEDIATE_CACHE_VALID_DAYS", "3"))  # 中间结果缓存有效期（天）

# Analysis dimensions
# 所有维度都已启用
DISABLED_DIMENSIONS = []

# 启用所有分析维度
ANALYSIS_DIMENSIONS = [
    {"key": "language_style", "name": "语言风格", "icon": "fas fa-language"},
    {"key": "rhythm_pacing", "name": "节奏节拍", "icon": "fas fa-drum"},
    {"key": "structure", "name": "结构分析", "icon": "fas fa-sitemap"},
    {"key": "sentence_variation", "name": "句式变化", "icon": "fas fa-text-width"},
    {"key": "paragraph_length", "name": "段落长度", "icon": "fas fa-indent"},
    {"key": "perspective_shifts", "name": "视角转换", "icon": "fas fa-eye"},
    {"key": "paragraph_flow", "name": "段落流畅度", "icon": "fas fa-stream"},
    {"key": "novel_characteristics", "name": "小说特点", "icon": "fas fa-fingerprint"},
    {"key": "world_building", "name": "世界构建", "icon": "fas fa-globe"},
    {"key": "character_relationships", "name": "人物关系", "icon": "fas fa-users"},
    {"key": "opening_effectiveness", "name": "开篇效果", "icon": "fas fa-door-open"},
    {"key": "climax_pacing", "name": "高潮节奏", "icon": "fas fa-mountain"},
    {"key": "chapter_outline", "name": "章纲分析", "icon": "fas fa-list-ol"},
    {"key": "outline_analysis", "name": "大纲分析", "icon": "fas fa-project-diagram"},
    {"key": "popular_tropes", "name": "热梗统计", "icon": "fas fa-fire-alt"}
]

# File upload settings
UPLOAD_FOLDER = os.getenv("UPLOAD_FOLDER", "uploads")
ALLOWED_EXTENSIONS = {"txt", "pdf", "docx", "epub"}
MAX_CONTENT_LENGTH = int(os.getenv("MAX_CONTENT_LENGTH", "50")) * 1024 * 1024  # 50MB
