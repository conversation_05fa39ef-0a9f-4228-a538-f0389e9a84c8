#!/usr/bin/env python
"""
修复模板转换服务中的维度名称字典，添加用户要求的15个维度
"""
import os
import sys
import logging
import fileinput
import re
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'fix_template_dimension_names_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

def fix_dimension_names():
    """修复模板转换服务中的维度名称字典，添加用户要求的15个维度"""
    # 源文件路径
    src_file = 'src/services/template_conversion_service.py'
    
    # 检查文件是否存在
    if not os.path.exists(src_file):
        logger.error(f"文件不存在: {src_file}")
        return False
    
    # 维度名称正则表达式模式
    dimension_dict_pattern = r"dimension_names\s*=\s*\{([^}]*)\}"
    
    # 备份原文件
    backup_file = f"{src_file}.bak_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        with open(src_file, 'r', encoding='utf-8') as source:
            content = source.read()
            with open(backup_file, 'w', encoding='utf-8') as backup:
                backup.write(content)
        logger.info(f"已备份原文件到: {backup_file}")
    except Exception as e:
        logger.error(f"备份文件失败: {str(e)}")
        return False
    
    # 新的15个维度名称字典
    new_dimension_names = """            'language_style': '语言风格',
            'rhythm_pacing': '节奏节拍',
            'structure': '结构分析',
            'sentence_variation': '句式变化',
            'paragraph_length': '段落长度',
            'perspective_shifts': '视角变化',
            'paragraph_flow': '段落流畅度',
            'novel_characteristics': '小说特点',
            'world_building': '世界构建',
            'character_relationships': '人物关系',
            'opening_effectiveness': '开篇效果',
            'climax_pacing': '高潮节奏',
            'chapter_outline': '章纲分析',
            'outline_analysis': '大纲分析',
            'popular_tropes': '热梗统计'"""
    
    try:
        # 使用正则表达式替换维度名称字典
        updated_content = re.sub(dimension_dict_pattern, 
                               f"dimension_names = {{\n{new_dimension_names}\n        }}", 
                               content, 
                               flags=re.DOTALL)
        
        # 将更新后的内容写回文件
        with open(src_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"成功更新文件: {src_file}")
        logger.info("已添加用户要求的15个维度到维度名称字典")
        return True
    except Exception as e:
        logger.error(f"更新文件失败: {str(e)}")
        # 还原备份
        try:
            with open(backup_file, 'r', encoding='utf-8') as backup:
                with open(src_file, 'w', encoding='utf-8') as source:
                    source.write(backup.read())
            logger.info("已还原原文件")
        except Exception as restore_err:
            logger.error(f"还原文件失败: {str(restore_err)}")
        return False

def enhance_template_generation():
    """增强模板生成函数，添加用户要求的15个维度的具体模板内容"""
    # 源文件路径
    src_file = 'src/services/template_conversion_service.py'
    
    # 检查文件是否存在
    if not os.path.exists(src_file):
        logger.error(f"文件不存在: {src_file}")
        return False
    
    # 备份原文件
    backup_file = f"{src_file}.enhance_bak_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        with open(src_file, 'r', encoding='utf-8') as source:
            content = source.read()
            with open(backup_file, 'w', encoding='utf-8') as backup:
                backup.write(content)
        logger.info(f"已备份原文件到: {backup_file}")
    except Exception as e:
        logger.error(f"备份文件失败: {str(e)}")
        return False
    
    # 查找模板生成函数中的条件分支
    if_pattern = r"(\s+# 根据不同维度添加不同的模板内容\n\s+if dimension == 'language_style':[^\n]*\n)([^elif]*)(elif dimension == 'rhythm_pacing':[^\n]*\n)([^else]*)(else:[^\n]*\n)([^\n]*)"
    
    # 针对不同维度设计的模板内容
    language_style_template = """            template_content += "#### 词汇选择\\n- 常用词汇：\\n- 特色词汇：\\n- 专业术语：\\n- 方言俚语：\\n\\n"
            template_content += "#### 句式结构\\n- 常用句式：\\n- 特色句式：\\n- 句长变化：\\n- 语气特点：\\n\\n"
            template_content += "#### 修辞手法\\n- 常用修辞：\\n- 特色修辞：\\n- 修辞效果：\\n- 修辞频率：\\n\\n"
            template_content += "#### 语气语调\\n- 叙述语气：\\n- 对话语气：\\n- 情感表达：\\n- 语调变化：\\n\\n"
            template_content += "#### 段落风格\\n- 抒情性段落占比：\\n- 叙事性段落占比：\\n- 五感描写分布：\\n- 特色表达方式：\\n\\n"""
    
    rhythm_pacing_template = """            template_content += "#### 情节波动曲线\\n- 高潮分布：\\n- 起伏频率：\\n- 情绪变化：\\n- 节奏控制：\\n\\n"
            template_content += "#### 场景节奏\\n- 场景转换：\\n- 场景长度：\\n- 场景密度：\\n- 场景变化：\\n\\n"
            template_content += "#### 冲突密度\\n- 外部冲突：\\n- 内部冲突：\\n- 冲突强度：\\n- 冲突解决方式：\\n\\n"
            template_content += "#### 时间流速\\n- 时间压缩：\\n- 时间延展：\\n- 时间跳跃：\\n- 时间停滞：\\n\\n"
            template_content += "#### 章节结构\\n- 章节钩子设计：\\n- 章节长度：\\n- 多线叙事切换：\\n- 章节过渡技巧：\\n\\n"""
    
    structure_template = """            template_content += "#### 整体结构框架\\n- 结构类型：\\n- 开端-发展-结局占比：\\n- 主线与支线比例：\\n- 主要转折点设置：\\n\\n"
            template_content += "#### 叙事结构\\n- 叙事视角：\\n- 时间线处理：\\n- 情节单元安排：\\n- 伏笔系统：\\n\\n"
            template_content += "#### 章节结构\\n- 章节功能定位：\\n- 内部结构模式：\\n- 章节间连接方式：\\n- 特殊章节设计：\\n\\n"
            template_content += "#### 多线叙事\\n- 线索数量：\\n- 交织频率：\\n- 线索平衡性：\\n- 汇聚点设计：\\n\\n"
            template_content += "#### 高潮分布\\n- 小高潮位置：\\n- 中高潮位置：\\n- 大高潮位置：\\n- 递进关系：\\n\\n"""
    
    sentence_variation_template = """            template_content += "#### 长短句分布\\n- 短句占比：\\n- 中句占比：\\n- 长句占比：\\n- 变化规律：\\n\\n"
            template_content += "#### 句式类型比例\\n- 陈述句占比：\\n- 疑问句占比：\\n- 感叹句占比：\\n- 祈使句占比：\\n\\n"
            template_content += "#### 特色句式\\n- 排比句使用：\\n- 设问句使用：\\n- 对偶句使用：\\n- 叠词句使用：\\n\\n"
            template_content += "#### 段落结构\\n- 段落首句特点：\\n- 段落尾句特点：\\n- 过渡句使用：\\n- 承接技巧：\\n\\n"
            template_content += "#### 对话与叙述\\n- 对话句比例：\\n- 叙述句比例：\\n- 对话特点：\\n- 叙述风格：\\n\\n"""
    
    paragraph_length_template = """            template_content += "#### 段落长度分布\\n- 短段落(1-3行)占比：\\n- 中段落(4-6行)占比：\\n- 长段落(7行以上)占比：\\n- 分布规律：\\n\\n"
            template_content += "#### 内容与长度关系\\n- 对话段落长度：\\n- 描写段落长度：\\n- 动作段落长度：\\n- 内心段落长度：\\n\\n"
            template_content += "#### 段落变化规律\\n- 高潮前变化：\\n- 高潮中变化：\\n- 高潮后变化：\\n- 过渡段设计：\\n\\n"
            template_content += "#### 人物与段落\\n- 主角段落特点：\\n- 次要角色段落：\\n- 群体描写段落：\\n- 场景与段落：\\n\\n"
            template_content += "#### 特殊场景处理\\n- 战斗场景段落：\\n- 情感场景段落：\\n- 叙述场景段落：\\n- 回忆场景段落：\\n\\n"""
    
    perspective_shifts_template = """            template_content += "#### 主要叙事视角\\n- 视角类型：\\n- 视角深度：\\n- 视角限制：\\n- 视角特点：\\n\\n"
            template_content += "#### 视角切换技巧\\n- 切换频率：\\n- 切换方式：\\n- 过渡处理：\\n- 衔接技巧：\\n\\n"
            template_content += "#### 多视角处理\\n- 同一场景多视角：\\n- 多角色视角分配：\\n- 信息控制策略：\\n- 视角交叉效果：\\n\\n"
            template_content += "#### 视角与情感\\n- 感情强度映射：\\n- 心理深度控制：\\n- 认知差异处理：\\n- 情感共鸣技巧：\\n\\n"
            template_content += "#### 特殊视角应用\\n- 隐藏信息视角：\\n- 误导性视角：\\n- 客观旁观视角：\\n- 视角转换节点：\\n\\n"""
    
    paragraph_flow_template = """            template_content += "#### 段落连接方式\\n- 因果连接比例：\\n- 时间推进比例：\\n- 空间转换比例：\\n- 心理关联比例：\\n\\n"
            template_content += "#### 过渡设计\\n- 过渡词使用频率：\\n- 隐性过渡技巧：\\n- 场景切换过渡：\\n- 时间跳跃过渡：\\n\\n"
            template_content += "#### 意象连接\\n- 关键意象选择：\\n- 意象重复频率：\\n- 意象变化发展：\\n- 意象象征作用：\\n\\n"
            template_content += "#### 情感递进\\n- 情感递进模式：\\n- 情感波动设计：\\n- 共鸣点布置：\\n- 节奏控制手法：\\n\\n"
            template_content += "#### 信息组织\\n- 主题聚焦度：\\n- 回顾与铺垫：\\n- 衔接与断裂：\\n- 多线索管理：\\n\\n"""
    
    novel_characteristics_template = """            template_content += "#### 核心题材特点\\n- 题材类型：\\n- 类型文学特征：\\n- 题材创新点：\\n- 类型融合度：\\n\\n"
            template_content += "#### 世界观设计\\n- 世界观开放度：\\n- 设定独特性：\\n- 规则系统：\\n- 环境特点：\\n\\n"
            template_content += "#### 情感基调\\n- 主要情感色彩：\\n- 情感变化规律：\\n- 情感强度控制：\\n- 情感层次性：\\n\\n"
            template_content += "#### 风格特点\\n- 写作风格倾向：\\n- 风格统一性：\\n- 标志性表达：\\n- 风格变化：\\n\\n"
            template_content += "#### 创新亮点\\n- 情节创新：\\n- 人物创新：\\n- 世界创新：\\n- 表达创新：\\n\\n"""
    
    world_building_template = """            template_content += "#### 世界类型与物理规则\\n- 世界类型：\\n- 自然规律：\\n- 超自然规则：\\n- 规则限制与平衡：\\n\\n"
            template_content += "#### 社会与文化\\n- 社会结构：\\n- 政治体系：\\n- 文化传统：\\n- 信仰系统：\\n\\n"
            template_content += "#### 地理与环境\\n- 地理结构：\\n- 气候特点：\\n- 特殊地点：\\n- 环境影响：\\n\\n"
            template_content += "#### 历史与背景\\n- 关键历史事件：\\n- 时代背景：\\n- 民族历史：\\n- 历史影响：\\n\\n"
            template_content += "#### 世界展示策略\\n- 展示节奏：\\n- 信息量控制：\\n- 设定融入方式：\\n- 读者理解考量：\\n\\n"""
    
    character_relationships_template = """            template_content += "#### 核心关系网络\\n- 关系网类型：\\n- 关系节点数量：\\n- 关系复杂度：\\n- 关系图谱：\\n\\n"
            template_content += "#### 关系类型分布\\n- 亲情关系比例：\\n- 友情关系比例：\\n- 爱情关系比例：\\n- 敌对关系比例：\\n\\n"
            template_content += "#### 关系发展模式\\n- 主要发展模式：\\n- 关系转折点：\\n- 冲突与和解：\\n- 关系深化方式：\\n\\n"
            template_content += "#### x关系对人物的影响\\n- 性格塑造影响：\\n- 能力发展影响：\\n- 决策影响因素：\\n- 成长推动作用：\\n\\n"
            template_content += "#### 群体关系动态\\n- 阵营形成过程：\\n- 派系变动规律：\\n- 团队凝聚机制：\\n- 群体冲突处理：\\n\\n"""
    
    opening_effectiveness_template = """            template_content += "#### 开篇类型与设计\\n- 开篇类型：\\n- 首章结构：\\n- 开篇强度：\\n- 设计理念：\\n\\n"
            template_content += "#### 核心元素引入\\n- 主角引入方式：\\n- 冲突暗示手法：\\n- 世界观展示：\\n- 主题暗示：\\n\\n"
            template_content += "#### 读者引导\\n- 共鸣点设置：\\n- 好奇心激发：\\n- 期待构建：\\n- 阅读节奏设定：\\n\\n"
            template_content += "#### 信息量控制\\n- 核心信息数量：\\n- 设定信息分布：\\n- 悬念布置：\\n- 信息节奏：\\n\\n"
            template_content += "#### 情感与基调\\n- 情感基调确立：\\n- 语言风格表现：\\n- 氛围营造：\\n- 基调与整体一致性：\\n\\n"""
    
    climax_pacing_template = """            template_content += "#### 高潮点分布\\n- 小高潮分布：\\n- 中高潮分布：\\n- 大高潮分布：\\n- 分布规律：\\n\\n"
            template_content += "#### 高潮类型与比例\\n- 战斗高潮占比：\\n- 情感高潮占比：\\n- 悬念解答占比：\\n- 能力突破占比：\\n\\n"
            template_content += "#### 高潮构建技巧\\n- 铺垫长度：\\n- 高潮持续：\\n- 高潮后缓冲：\\n- 突破与悬念：\\n\\n"
            template_content += "#### 多线交汇设计\\n- 交汇线索数量：\\n- 交汇方式：\\n- 交汇节点：\\n- 交汇效果：\\n\\n"
            template_content += "#### 递进式结构\\n- 强度递进关系：\\n- 情感积累方式：\\n- 悬念层层深入：\\n- 满足感设计：\\n\\n"""
    
    chapter_outline_template = """            template_content += "#### 章节基本信息\\n- 章节功能：\\n- 字数规划：\\n- 阅读时长：\\n- 整体定位：\\n\\n"
            template_content += "#### 内部结构设计\\n- 结构模式：\\n- 起承转合比例：\\n- 冲突设置：\\n- 解决方式：\\n\\n"
            template_content += "#### 章节衔接\\n- 与上章关系：\\n- 与下章关系：\\n- 连接方式：\\n- 过渡设计：\\n\\n"
            template_content += "#### 焦点与节奏\\n- 焦点类型：\\n- 节奏规划：\\n- 信息点分布：\\n- 情感曲线：\\n\\n"
            template_content += "#### 特殊设计\\n- 钩子设计：\\n- 悬念布置：\\n- 伏笔设置：\\n- 创新亮点：\\n\\n"""
    
    outline_analysis_template = """            template_content += "#### 主线故事设计\\n- 主线起因：\\n- 主线经过：\\n- 主线结果：\\n- 主线转折点：\\n\\n"
            template_content += "#### 次要情节线设计\\n- 支线数量：\\n- 支线功能：\\n- 支线与主线关系：\\n- 支线交织点：\\n\\n"
            template_content += "#### 角色成长规划\\n- 主角成长路径：\\n- 关键成长节点：\\n- 能力进阶设计：\\n- 性格发展轨迹：\\n\\n"
            template_content += "#### 世界观展开计划\\n- 世界元素揭示顺序：\\n- 设定深化节点：\\n- 秘密与真相安排：\\n- 历史背景展现：\\n\\n"
            template_content += "#### 整体进度规划\\n- 篇幅分配：\\n- 高潮安排：\\n- 情感发展：\\n- 节奏控制：\\n\\n"""
    
    popular_tropes_template = """            template_content += "#### 热梗使用规划\\n- 使用密度：\\n- 分布规律：\\n- 融入自然度：\\n- 创新变化：\\n\\n"
            template_content += "#### 热梗类型比例\\n- 网络流行梗比例：\\n- 影视梗比例：\\n- 游戏梗比例：\\n- 文学梗比例：\\n\\n"
            template_content += "#### 热梗时效性\\n- 经典长寿梗比例：\\n- 阶段性流行梗比例：\\n- 新兴热梗比例：\\n- 原创梗潜力：\\n\\n"
            template_content += "#### 热梗使用场景\\n- 对话中使用比例：\\n- 叙述中使用比例：\\n- 人物思想中比例：\\n- 场景匹配度：\\n\\n"
            template_content += "#### 热梗与角色\\n- 角色专属梗：\\n- 梗与人设匹配：\\n- 角色语言特色：\\n- 角色互动梗：\\n\\n"""
    
    # 构建新的条件分支
    new_if_conditions = f"""        # 根据不同维度添加不同的模板内容
        if dimension == 'language_style':
{language_style_template}
        elif dimension == 'rhythm_pacing':
{rhythm_pacing_template}
        elif dimension == 'structure':
{structure_template}
        elif dimension == 'sentence_variation':
{sentence_variation_template}
        elif dimension == 'paragraph_length':
{paragraph_length_template}
        elif dimension == 'perspective_shifts':
{perspective_shifts_template}
        elif dimension == 'paragraph_flow':
{paragraph_flow_template}
        elif dimension == 'novel_characteristics':
{novel_characteristics_template}
        elif dimension == 'world_building':
{world_building_template}
        elif dimension == 'character_relationships':
{character_relationships_template}
        elif dimension == 'opening_effectiveness':
{opening_effectiveness_template}
        elif dimension == 'climax_pacing':
{climax_pacing_template}
        elif dimension == 'chapter_outline':
{chapter_outline_template}
        elif dimension == 'outline_analysis':
{outline_analysis_template}
        elif dimension == 'popular_tropes':
{popular_tropes_template}
        else:
            template_content += "#### 基本要素\\n- 主要特点：\\n- 变化规律：\\n- 效果评估：\\n- 创新之处：\\n\\n"
            template_content += "#### 应用建议\\n- 适用场景：\\n- 注意事项：\\n- 常见问题：\\n- 优化方向：\\n\\n"
"""
    
    # 尝试查找并替换条件分支
    try:
        with open(src_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找第一个模板生成函数中的条件分支
        pattern1 = r"(\s+# 根据不同维度添加不同的模板内容\n\s+if dimension.*?else:.*?\n\s+template_content \+= \"#### 基本要素.*?#### 应用建议.*?\n\n)"
        match1 = re.search(pattern1, content, re.DOTALL)
        
        if match1:
            # 替换第一个函数中的条件分支
            updated_content = content.replace(match1.group(0), new_if_conditions)
            
            # 查找第二个模板生成函数中的条件分支
            pattern2 = r"(\s+# 根据不同维度添加不同的模板内容\n\s+if dimension.*?else:.*?\n\s+template_content \+= \"#### 基本要素.*?#### 应用建议.*?\n\n)"
            match2 = re.search(pattern2, updated_content[match1.end():], re.DOTALL)
            
            if match2:
                # 替换第二个函数中的条件分支
                start_idx = match1.end() + match2.start()
                end_idx = match1.end() + match2.end()
                final_content = updated_content[:start_idx] + new_if_conditions + updated_content[end_idx:]
                
                # 将更新后的内容写回文件
                with open(src_file, 'w', encoding='utf-8') as f:
                    f.write(final_content)
                
                logger.info(f"成功更新两个模板生成函数中的条件分支")
                return True
            else:
                # 只能找到一个函数的条件分支，只更新第一个
                with open(src_file, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                
                logger.info(f"只找到并更新了一个模板生成函数中的条件分支")
                return True
        else:
            logger.error("未能找到需要替换的条件分支")
            return False
    
    except Exception as e:
        logger.error(f"更新条件分支失败: {str(e)}")
        # 还原备份
        try:
            with open(backup_file, 'r', encoding='utf-8') as backup:
                with open(src_file, 'w', encoding='utf-8') as source:
                    source.write(backup.read())
            logger.info("已还原原文件")
        except Exception as restore_err:
            logger.error(f"还原文件失败: {str(restore_err)}")
        return False

if __name__ == "__main__":
    logger.info("开始修复模板转换服务中的维度名称字典")
    fix_result = fix_dimension_names()
    
    if fix_result:
        logger.info("维度名称字典修复成功")
        
        logger.info("开始增强模板生成函数")
        enhance_result = enhance_template_generation()
        
        if enhance_result:
            logger.info("模板生成函数增强成功")
            logger.info("修复完成，已添加用户要求的15个维度的详细模板内容")
        else:
            logger.error("模板生成函数增强失败")
    else:
        logger.error("维度名称字典修复失败") 