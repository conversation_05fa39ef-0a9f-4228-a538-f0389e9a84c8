{"name": "json-stringify-nice", "version": "1.1.4", "description": "Stringify an object sorting scalars before objects, and defaulting to 2-space indent", "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "posttest": "npm run lint", "snap": "tap", "postsnap": "npm run lintfix", "eslint": "eslint", "lint": "npm run eslint -- index.js test/**/*.js", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"test-env": ["LC_ALL=sk"], "check-coverage": true}, "devDependencies": {"eslint": "^7.25.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0", "tap": "^15.0.6"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": "https://github.com/isaacs/json-stringify-nice", "files": ["index.js"]}