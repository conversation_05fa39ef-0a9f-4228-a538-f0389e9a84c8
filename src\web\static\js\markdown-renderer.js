/**
 * 九猫系统 - Markdown渲染工具
 * 用于增强分析结果和推理过程的显示效果
 */

document.addEventListener('DOMContentLoaded', function() {
    // 查找所有Markdown内容容器
    const markdownContainers = document.querySelectorAll('.markdown-content');
    
    // 如果找到容器，进行格式化处理
    if (markdownContainers.length > 0) {
        markdownContainers.forEach(enhanceMarkdownContent);
    }
    
    // 监听标签页点击事件，处理动态加载的内容
    const contentTabs = document.querySelectorAll('.nav-link');
    if (contentTabs.length > 0) {
        contentTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 延迟执行，等待内容加载
                setTimeout(function() {
                    const dynamicContainers = document.querySelectorAll('.markdown-content:not(.enhanced)');
                    dynamicContainers.forEach(enhanceMarkdownContent);
                }, 500);
            });
        });
    }
});

/**
 * 增强Markdown内容显示
 * @param {HTMLElement} container - Markdown内容容器
 */
function enhanceMarkdownContent(container) {
    // 标记为已处理，避免重复处理
    if (container.classList.contains('enhanced')) {
        return;
    }
    
    // 获取原始内容
    let content = container.innerHTML;
    
    // 处理标题
    content = enhanceHeadings(content);
    
    // 处理列表
    content = enhanceLists(content);
    
    // 处理引用
    content = enhanceBlockquotes(content);
    
    // 处理代码块
    content = enhanceCodeBlocks(content);
    
    // 处理表格
    content = enhanceTables(content);
    
    // 处理强调和加粗
    content = enhanceEmphasis(content);
    
    // 处理分隔线
    content = enhanceHorizontalRules(content);
    
    // 更新内容
    container.innerHTML = content;
    
    // 标记为已处理
    container.classList.add('enhanced');
}

/**
 * 增强标题显示
 * @param {string} content - 原始内容
 * @returns {string} 处理后的内容
 */
function enhanceHeadings(content) {
    // 处理Markdown标题 (# 标题)
    content = content.replace(/(?:^|\n)(#{1,6})\s+([^\n]+)/g, function(match, hashes, title) {
        const level = hashes.length;
        return `<h${level} class="enhanced-heading">${title}</h${level}>`;
    });
    
    // 处理数字编号标题 (1. 标题)
    content = content.replace(/(?:^|\n)(\d+)\.\s+([^\n]+)(?=\n|$)/g, function(match, number, title) {
        return `<div class="numbered-heading"><span class="number">${number}.</span> ${title}</div>`;
    });
    
    return content;
}

/**
 * 增强列表显示
 * @param {string} content - 原始内容
 * @returns {string} 处理后的内容
 */
function enhanceLists(content) {
    // 处理无序列表
    content = content.replace(/(?:^|\n)[\*\-•]\s+([^\n]+)(?=\n|$)/g, '<li>$1</li>');
    content = content.replace(/(?:<li>.*?<\/li>)+/g, '<ul class="enhanced-list">$&</ul>');
    
    // 处理有序列表
    content = content.replace(/(?:^|\n)\d+\.\s+([^\n]+)(?=\n|$)/g, '<li>$1</li>');
    content = content.replace(/(?:<li>.*?<\/li>)+/g, '<ol class="enhanced-list">$&</ol>');
    
    return content;
}

/**
 * 增强引用显示
 * @param {string} content - 原始内容
 * @returns {string} 处理后的内容
 */
function enhanceBlockquotes(content) {
    // 处理引用块
    content = content.replace(/(?:^|\n)>\s+([^\n]+)(?=\n|$)/g, '<blockquote class="enhanced-quote">$1</blockquote>');
    
    return content;
}

/**
 * 增强代码块显示
 * @param {string} content - 原始内容
 * @returns {string} 处理后的内容
 */
function enhanceCodeBlocks(content) {
    // 处理行内代码
    content = content.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');
    
    // 处理代码块
    content = content.replace(/```([^`]+)```/g, '<pre class="enhanced-code-block"><code>$1</code></pre>');
    
    return content;
}

/**
 * 增强表格显示
 * @param {string} content - 原始内容
 * @returns {string} 处理后的内容
 */
function enhanceTables(content) {
    // 简单表格处理
    if (content.includes('|') && content.includes('\n')) {
        const lines = content.split('\n');
        let inTable = false;
        let tableHTML = '<table class="enhanced-table">';
        let headerProcessed = false;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            if (line.startsWith('|') && line.endsWith('|')) {
                if (!inTable) {
                    inTable = true;
                }
                
                const cells = line.split('|').filter(cell => cell.trim() !== '');
                
                if (!headerProcessed && i < lines.length - 1 && lines[i+1].trim().startsWith('|') && lines[i+1].trim().includes('-')) {
                    // 这是表头
                    tableHTML += '<thead><tr>';
                    cells.forEach(cell => {
                        tableHTML += `<th>${cell.trim()}</th>`;
                    });
                    tableHTML += '</tr></thead><tbody>';
                    headerProcessed = true;
                    i++; // 跳过分隔行
                } else {
                    // 这是表格内容
                    tableHTML += '<tr>';
                    cells.forEach(cell => {
                        tableHTML += `<td>${cell.trim()}</td>`;
                    });
                    tableHTML += '</tr>';
                }
            } else if (inTable) {
                // 表格结束
                tableHTML += '</tbody></table>';
                inTable = false;
                headerProcessed = false;
                
                // 替换原始内容中的表格部分
                const tableStart = content.indexOf('|');
                const tableEnd = content.lastIndexOf('|') + 1;
                content = content.substring(0, tableStart) + tableHTML + content.substring(tableEnd);
            }
        }
        
        if (inTable) {
            // 确保最后一个表格正确关闭
            tableHTML += '</tbody></table>';
            
            // 替换原始内容中的表格部分
            const tableStart = content.indexOf('|');
            const tableEnd = content.lastIndexOf('|') + 1;
            content = content.substring(0, tableStart) + tableHTML + content.substring(tableEnd);
        }
    }
    
    return content;
}

/**
 * 增强强调和加粗显示
 * @param {string} content - 原始内容
 * @returns {string} 处理后的内容
 */
function enhanceEmphasis(content) {
    // 处理加粗
    content = content.replace(/\*\*([^*]+)\*\*/g, '<strong class="enhanced-bold">$1</strong>');
    
    // 处理斜体
    content = content.replace(/\*([^*]+)\*/g, '<em class="enhanced-italic">$1</em>');
    
    return content;
}

/**
 * 增强分隔线显示
 * @param {string} content - 原始内容
 * @returns {string} 处理后的内容
 */
function enhanceHorizontalRules(content) {
    // 处理分隔线
    content = content.replace(/(?:^|\n)[-*_]{3,}(?=\n|$)/g, '<hr class="enhanced-hr">');
    
    return content;
}
