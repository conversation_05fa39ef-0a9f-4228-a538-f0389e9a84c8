"""
测试API调用是否正常工作
"""
import os
import sys
import logging
import json
import requests
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入配置
import config

def test_deepseek_api():
    """测试DeepSeek API调用"""
    logger.info("开始测试DeepSeek API调用")
    
    # 获取API密钥
    api_key = config.DEEPSEEK_API_KEY
    logger.info(f"使用API密钥: {api_key[:6]}...")
    
    # 设置API端点
    endpoint = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    logger.info(f"使用API端点: {endpoint}")
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # 设置请求体
    payload = {
        "model": "deepseek-r1",
        "input": {
            "prompt": "请分析以下文本的语言风格：\n\n这是一段测试文本，用于验证API调用是否正常工作。",
            "parameters": {
                "max_tokens": 500,
                "temperature": 0.1,
                "top_p": 0.8,
                "top_k": 50,
                "repetition_penalty": 1.1,
                "stop": []
            }
        }
    }
    
    # 发送请求
    logger.info("发送API请求...")
    start_time = time.time()
    try:
        response = requests.post(endpoint, headers=headers, json=payload, timeout=60)
        end_time = time.time()
        logger.info(f"请求耗时: {end_time - start_time:.2f}秒")
        
        # 检查响应状态码
        logger.info(f"响应状态码: {response.status_code}")
        
        # 尝试解析响应内容
        if response.status_code == 200:
            try:
                response_json = response.json()
                logger.info(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)[:500]}...")
                
                # 提取生成的文本
                output = response_json.get("output", {})
                text = output.get("text", "")
                logger.info(f"生成的文本: {text[:200]}...")
                
                # 提取令牌使用信息
                usage = output.get("usage", {})
                prompt_tokens = usage.get("prompt_tokens", 0)
                completion_tokens = usage.get("completion_tokens", 0)
                total_tokens = prompt_tokens + completion_tokens
                logger.info(f"令牌使用: 输入={prompt_tokens}, 输出={completion_tokens}, 总计={total_tokens}")
                
                return True
            except Exception as e:
                logger.error(f"解析响应内容时出错: {str(e)}")
                logger.error(f"响应内容: {response.text[:500]}...")
                return False
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text[:500]}...")
            return False
    except Exception as e:
        logger.error(f"发送API请求时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始测试API调用")
    
    # 测试DeepSeek API调用
    success = test_deepseek_api()
    
    if success:
        logger.info("API调用测试成功")
    else:
        logger.error("API调用测试失败")
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
