"""
简单检查数据库结构
"""

import sqlite3
import os

# 数据库路径
db_path = os.path.join(os.path.dirname(__file__), 'novel_analysis.db')

def simple_check_db():
    """简单检查数据库结构"""
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"数据库中的表：")
        for table in tables:
            print(f"- {table[0]}")
    except Exception as e:
        print(f"检查数据库结构时出错: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    simple_check_db()
