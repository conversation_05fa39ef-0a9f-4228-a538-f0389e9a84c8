<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>jQuery测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 20px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 3px;
            min-height: 20px;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .failure {
            color: #e74c3c;
            font-weight: bold;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        #log {
            margin-top: 20px;
            padding: 10px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
        }
        .log-entry {
            margin: 5px 0;
            padding: 3px;
            border-bottom: 1px solid #eee;
        }
        .info { color: #2980b9; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
    </style>
    
    <!-- 脚本加载器 -->
    <script src="/static/js/script-loader.js"></script>
</head>
<body>
    <div class="container">
        <h1>jQuery功能测试页面</h1>
        <p>此页面用于测试jQuery及其修复脚本的功能。</p>
        
        <div class="test-section">
            <h2>1. jQuery加载测试</h2>
            <button id="checkJQuery">检查jQuery</button>
            <div id="jqueryResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>2. .on()方法测试</h2>
            <button id="testButton">点击测试</button>
            <button id="testDelegation">测试事件委托</button>
            <div id="delegationContainer">
                <p class="delegated-item">委托项目1</p>
                <p class="delegated-item">委托项目2</p>
                <p class="delegated-item">委托项目3</p>
            </div>
            <div id="onMethodResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>3. 其他jQuery方法测试</h2>
            <button id="testHtml">测试.html()</button>
            <button id="testCss">测试.css()</button>
            <button id="testAjax">测试$.ajax()</button>
            <div id="otherMethodsResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>4. 修复脚本测试</h2>
            <button id="testFix">测试修复脚本</button>
            <button id="forceError">强制jQuery错误</button>
            <div id="fixResult" class="test-result"></div>
        </div>
        
        <h2>控制台日志</h2>
        <div id="log"></div>
        
        <div style="margin-top: 20px;">
            <button id="clearLog">清除日志</button>
            <button id="reloadPage">重新加载页面</button>
        </div>
    </div>
    
    <script>
        // 自定义日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 同时输出到浏览器控制台
            console[type === 'error' ? 'error' : type === 'warning' ? 'warn' : 'log'](message);
        }
        
        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            
            // 检查jQuery按钮
            document.getElementById('checkJQuery').addEventListener('click', function() {
                const result = document.getElementById('jqueryResult');
                
                if (typeof jQuery !== 'undefined') {
                    result.innerHTML = `<span class="success">jQuery已加载，版本: ${jQuery.fn.jquery || '自定义版本'}</span>`;
                    log(`jQuery已加载，版本: ${jQuery.fn.jquery || '自定义版本'}`);
                } else {
                    result.innerHTML = `<span class="failure">jQuery未加载</span>`;
                    log('jQuery未加载', 'error');
                }
            });
            
            // 测试.on()方法
            document.getElementById('testButton').addEventListener('click', function() {
                const result = document.getElementById('onMethodResult');
                
                try {
                    if (typeof jQuery !== 'undefined') {
                        if (typeof jQuery.fn.on === 'function') {
                            $('#testButton').on('testEvent', function() {
                                log('.on()方法测试成功');
                            });
                            $('#testButton').trigger('testEvent');
                            result.innerHTML = `<span class="success">.on()方法测试成功</span>`;
                        } else {
                            result.innerHTML = `<span class="failure">.on()方法不存在</span>`;
                            log('.on()方法不存在', 'error');
                        }
                    } else {
                        result.innerHTML = `<span class="failure">jQuery未加载，无法测试.on()方法</span>`;
                        log('jQuery未加载，无法测试.on()方法', 'error');
                    }
                } catch (e) {
                    result.innerHTML = `<span class="failure">测试.on()方法时出错: ${e.message}</span>`;
                    log(`测试.on()方法时出错: ${e.message}`, 'error');
                }
            });
            
            // 测试事件委托
            document.getElementById('testDelegation').addEventListener('click', function() {
                const result = document.getElementById('onMethodResult');
                
                try {
                    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.on === 'function') {
                        $('#delegationContainer').on('click', '.delegated-item', function() {
                            log(`点击了: ${this.textContent}`);
                            result.innerHTML = `<span class="success">事件委托测试成功: ${this.textContent}</span>`;
                        });
                        log('已设置事件委托，请点击委托项目');
                    } else {
                        result.innerHTML = `<span class="failure">jQuery或.on()方法不可用，无法测试事件委托</span>`;
                        log('jQuery或.on()方法不可用，无法测试事件委托', 'error');
                    }
                } catch (e) {
                    result.innerHTML = `<span class="failure">测试事件委托时出错: ${e.message}</span>`;
                    log(`测试事件委托时出错: ${e.message}`, 'error');
                }
            });
            
            // 测试其他jQuery方法
            document.getElementById('testHtml').addEventListener('click', function() {
                const result = document.getElementById('otherMethodsResult');
                
                try {
                    if (typeof jQuery !== 'undefined') {
                        $('#otherMethodsResult').html('<span class="success">.html()方法测试成功</span>');
                        log('.html()方法测试成功');
                    } else {
                        result.innerHTML = `<span class="failure">jQuery未加载，无法测试.html()方法</span>`;
                        log('jQuery未加载，无法测试.html()方法', 'error');
                    }
                } catch (e) {
                    result.innerHTML = `<span class="failure">测试.html()方法时出错: ${e.message}</span>`;
                    log(`测试.html()方法时出错: ${e.message}`, 'error');
                }
            });
            
            document.getElementById('testCss').addEventListener('click', function() {
                const result = document.getElementById('otherMethodsResult');
                
                try {
                    if (typeof jQuery !== 'undefined') {
                        $('#otherMethodsResult').css('color', 'green');
                        result.innerHTML += '<br><span class="success">.css()方法测试成功</span>';
                        log('.css()方法测试成功');
                    } else {
                        result.innerHTML = `<span class="failure">jQuery未加载，无法测试.css()方法</span>`;
                        log('jQuery未加载，无法测试.css()方法', 'error');
                    }
                } catch (e) {
                    result.innerHTML = `<span class="failure">测试.css()方法时出错: ${e.message}</span>`;
                    log(`测试.css()方法时出错: ${e.message}`, 'error');
                }
            });
            
            document.getElementById('testAjax').addEventListener('click', function() {
                const result = document.getElementById('otherMethodsResult');
                
                try {
                    if (typeof jQuery !== 'undefined' && typeof jQuery.ajax === 'function') {
                        result.innerHTML += '<br>正在测试$.ajax()...';
                        log('正在测试$.ajax()...');
                        
                        jQuery.ajax({
                            url: '/api/ping',
                            type: 'GET',
                            success: function(data) {
                                result.innerHTML += '<br><span class="success">$.ajax()方法测试成功</span>';
                                log('$.ajax()方法测试成功');
                            },
                            error: function() {
                                result.innerHTML += '<br><span class="warning">$.ajax()请求失败，但方法可用</span>';
                                log('$.ajax()请求失败，但方法可用', 'warning');
                            }
                        });
                    } else {
                        result.innerHTML += '<br><span class="failure">jQuery未加载或$.ajax()方法不可用</span>';
                        log('jQuery未加载或$.ajax()方法不可用', 'error');
                    }
                } catch (e) {
                    result.innerHTML += `<br><span class="failure">测试$.ajax()方法时出错: ${e.message}</span>`;
                    log(`测试$.ajax()方法时出错: ${e.message}`, 'error');
                }
            });
            
            // 测试修复脚本
            document.getElementById('testFix').addEventListener('click', function() {
                const result = document.getElementById('fixResult');
                
                try {
                    if (typeof window.fixConsole === 'function') {
                        window.fixConsole();
                        result.innerHTML = '<span class="success">修复脚本可用并已执行</span>';
                        log('修复脚本可用并已执行');
                    } else {
                        result.innerHTML = '<span class="failure">修复脚本不可用</span>';
                        log('修复脚本不可用', 'error');
                    }
                } catch (e) {
                    result.innerHTML = `<span class="failure">测试修复脚本时出错: ${e.message}</span>`;
                    log(`测试修复脚本时出错: ${e.message}`, 'error');
                }
            });
            
            // 强制jQuery错误
            document.getElementById('forceError').addEventListener('click', function() {
                const result = document.getElementById('fixResult');
                
                try {
                    log('强制触发jQuery错误...');
                    result.innerHTML = '<span class="warning">正在强制触发jQuery错误...</span>';
                    
                    // 临时删除jQuery.fn.on方法
                    if (typeof jQuery !== 'undefined') {
                        const originalOn = jQuery.fn.on;
                        jQuery.fn.on = undefined;
                        
                        // 尝试使用.on()方法触发错误
                        try {
                            $('#testButton').on('click', function() {});
                        } catch (e) {
                            log(`成功触发错误: ${e.message}`, 'warning');
                            result.innerHTML += '<br><span class="warning">成功触发错误，应该会自动修复</span>';
                        }
                        
                        // 恢复原始方法
                        setTimeout(function() {
                            jQuery.fn.on = originalOn;
                            log('已恢复jQuery.fn.on方法');
                            result.innerHTML += '<br><span class="success">已恢复jQuery.fn.on方法</span>';
                        }, 2000);
                    } else {
                        result.innerHTML = '<span class="failure">jQuery未加载，无法强制错误</span>';
                        log('jQuery未加载，无法强制错误', 'error');
                    }
                } catch (e) {
                    result.innerHTML = `<span class="failure">强制错误时出错: ${e.message}</span>`;
                    log(`强制错误时出错: ${e.message}`, 'error');
                }
            });
            
            // 清除日志
            document.getElementById('clearLog').addEventListener('click', function() {
                document.getElementById('log').innerHTML = '';
                log('日志已清除');
            });
            
            // 重新加载页面
            document.getElementById('reloadPage').addEventListener('click', function() {
                location.reload();
            });
        });
    </script>
</body>
</html>
