/**
 * 九猫系统 Bootstrap Bundle 修复加载器
 * 版本: 1.0.0
 * 最后更新: 2025-05-21
 * 
 * 此脚本用于在页面加载时自动替换损坏的 bootstrap.bundle.min.js 文件
 */

(function() {
    console.log('[Bootstrap Bundle 修复加载器] 初始化...');
    
    // 检查是否已加载 bootstrap.bundle.min.js
    function checkBootstrapBundle() {
        // 检查是否存在 bootstrap 对象
        if (typeof bootstrap !== 'undefined') {
            console.log('[Bootstrap Bundle 修复加载器] Bootstrap Bundle 已加载');
            return true;
        }
        
        // 检查是否存在 Modal 类
        if (typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined') {
            console.log('[Bootstrap Bundle 修复加载器] Bootstrap Modal 已加载');
            return true;
        }
        
        console.log('[Bootstrap Bundle 修复加载器] Bootstrap Bundle 未加载');
        return false;
    }
    
    // 检查是否有错误
    function checkForErrors() {
        // 检查是否有语法错误
        try {
            // 尝试访问 bootstrap 对象
            if (typeof bootstrap !== 'undefined') {
                // 尝试创建一个 Modal 实例
                const modalElement = document.createElement('div');
                modalElement.className = 'modal';
                document.body.appendChild(modalElement);
                
                try {
                    const modal = new bootstrap.Modal(modalElement);
                    modal.dispose();
                    document.body.removeChild(modalElement);
                    console.log('[Bootstrap Bundle 修复加载器] Bootstrap Modal 测试成功');
                    return false;
                } catch (e) {
                    console.error('[Bootstrap Bundle 修复加载器] Bootstrap Modal 测试失败:', e);
                    document.body.removeChild(modalElement);
                    return true;
                }
            }
        } catch (e) {
            console.error('[Bootstrap Bundle 修复加载器] 检查错误时出错:', e);
            return true;
        }
        
        return !checkBootstrapBundle();
    }
    
    // 加载 Bootstrap Bundle
    function loadBootstrapBundle() {
        console.log('[Bootstrap Bundle 修复加载器] 尝试加载 Bootstrap Bundle...');
        
        // 创建 script 元素
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
        script.integrity = 'sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz';
        script.crossOrigin = 'anonymous';
        
        // 加载成功回调
        script.onload = function() {
            console.log('[Bootstrap Bundle 修复加载器] Bootstrap Bundle 加载成功');
            
            // 创建自定义事件通知其他脚本
            try {
                const event = new Event('bootstrapBundleLoaded');
                document.dispatchEvent(event);
            } catch (e) {
                console.error('[Bootstrap Bundle 修复加载器] 创建事件失败:', e);
                
                // IE 兼容性处理
                try {
                    const ieEvent = document.createEvent('Event');
                    ieEvent.initEvent('bootstrapBundleLoaded', true, true);
                    document.dispatchEvent(ieEvent);
                } catch (ie_e) {
                    console.error('[Bootstrap Bundle 修复加载器] IE 事件创建失败:', ie_e);
                }
            }
        };
        
        // 加载失败回调
        script.onerror = function() {
            console.error('[Bootstrap Bundle 修复加载器] Bootstrap Bundle 加载失败，尝试使用备用 CDN');
            
            // 尝试使用备用 CDN
            const backupScript = document.createElement('script');
            backupScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js';
            
            backupScript.onload = function() {
                console.log('[Bootstrap Bundle 修复加载器] 从备用 CDN 加载 Bootstrap Bundle 成功');
                
                // 创建自定义事件通知其他脚本
                try {
                    const event = new Event('bootstrapBundleLoaded');
                    document.dispatchEvent(event);
                } catch (e) {
                    console.error('[Bootstrap Bundle 修复加载器] 创建事件失败:', e);
                }
            };
            
            backupScript.onerror = function() {
                console.error('[Bootstrap Bundle 修复加载器] 从备用 CDN 加载 Bootstrap Bundle 也失败，创建最小实现');
                createMinimalImplementation();
            };
            
            document.head.appendChild(backupScript);
        };
        
        // 添加到文档
        document.head.appendChild(script);
    }
    
    // 创建最小实现
    function createMinimalImplementation() {
        console.log('[Bootstrap Bundle 修复加载器] 创建 Bootstrap 最小实现');
        
        // 创建最小的 bootstrap 对象
        window.bootstrap = window.bootstrap || {};
        
        // 创建最小的 Modal 实现
        bootstrap.Modal = function(element, options) {
            this.element = element;
            this.options = options || {};
            
            this.show = function() {
                if (this.element) {
                    this.element.style.display = 'block';
                    document.body.classList.add('modal-open');
                    
                    // 创建背景遮罩
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    document.body.appendChild(backdrop);
                }
            };
            
            this.hide = function() {
                if (this.element) {
                    this.element.style.display = 'none';
                    document.body.classList.remove('modal-open');
                    
                    // 移除背景遮罩
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.parentNode.removeChild(backdrop);
                    }
                }
            };
            
            this.toggle = function() {
                if (this.element && this.element.style.display === 'block') {
                    this.hide();
                } else {
                    this.show();
                }
            };
            
            this.dispose = function() {
                // 清理资源
            };
        };
        
        // 创建最小的 Tab 实现
        bootstrap.Tab = function(element) {
            this.element = element;
            
            this.show = function() {
                if (!this.element) return;
                
                // 获取目标面板
                const target = document.querySelector(this.element.getAttribute('data-bs-target') || this.element.getAttribute('href'));
                if (!target) return;
                
                // 获取所有相关的标签页和面板
                const parent = this.element.closest('.nav');
                if (!parent) return;
                
                const tabs = parent.querySelectorAll('[data-bs-toggle="tab"], [data-bs-toggle="pill"]');
                const panes = document.querySelectorAll('.tab-pane');
                
                // 隐藏所有面板
                for (let i = 0; i < panes.length; i++) {
                    panes[i].classList.remove('show', 'active');
                }
                
                // 取消激活所有标签
                for (let i = 0; i < tabs.length; i++) {
                    tabs[i].classList.remove('active');
                    tabs[i].setAttribute('aria-selected', 'false');
                }
                
                // 激活当前标签和面板
                this.element.classList.add('active');
                this.element.setAttribute('aria-selected', 'true');
                target.classList.add('show', 'active');
            };
            
            this.dispose = function() {
                // 清理资源
            };
        };
        
        // 创建最小的 Collapse 实现
        bootstrap.Collapse = function(element, options) {
            this.element = element;
            this.options = options || {};
            
            this.show = function() {
                if (this.element) {
                    this.element.style.height = 'auto';
                    this.element.classList.add('show');
                }
            };
            
            this.hide = function() {
                if (this.element) {
                    this.element.style.height = '0';
                    this.element.classList.remove('show');
                }
            };
            
            this.toggle = function() {
                if (this.element.classList.contains('show')) {
                    this.hide();
                } else {
                    this.show();
                }
            };
            
            this.dispose = function() {
                // 清理资源
            };
        };
        
        console.log('[Bootstrap Bundle 修复加载器] Bootstrap 最小实现创建完成');
        
        // 创建自定义事件通知其他脚本
        try {
            const event = new Event('bootstrapBundleLoaded');
            document.dispatchEvent(event);
        } catch (e) {
            console.error('[Bootstrap Bundle 修复加载器] 创建事件失败:', e);
        }
    }
    
    // 初始化
    function initialize() {
        // 检查页面是否已加载
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('[Bootstrap Bundle 修复加载器] 页面已加载，执行初始检查');
            setTimeout(function() {
                if (checkForErrors()) {
                    console.log('[Bootstrap Bundle 修复加载器] 检测到错误，开始加载 Bootstrap Bundle');
                    loadBootstrapBundle();
                } else {
                    console.log('[Bootstrap Bundle 修复加载器] 未检测到错误，无需加载 Bootstrap Bundle');
                }
            }, 1000); // 等待1秒，确保页面完全加载
        } else {
            console.log('[Bootstrap Bundle 修复加载器] 页面尚未加载，等待 DOMContentLoaded 事件');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('[Bootstrap Bundle 修复加载器] DOMContentLoaded 事件触发，执行初始检查');
                setTimeout(function() {
                    if (checkForErrors()) {
                        console.log('[Bootstrap Bundle 修复加载器] 检测到错误，开始加载 Bootstrap Bundle');
                        loadBootstrapBundle();
                    } else {
                        console.log('[Bootstrap Bundle 修复加载器] 未检测到错误，无需加载 Bootstrap Bundle');
                    }
                }, 1000); // 等待1秒，确保页面完全加载
            });
        }
    }
    
    // 导出全局函数
    window.fixBootstrapBundle = function() {
        console.log('[Bootstrap Bundle 修复加载器] 手动触发 Bootstrap Bundle 修复');
        loadBootstrapBundle();
        return true;
    };
    
    // 执行初始化
    initialize();
    
    console.log('[Bootstrap Bundle 修复加载器] 初始化完成');
})();
