{% extends "new_base.html" %}

{% block title %}系统监控 - 九猫小说分析系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-0"><i class="fas fa-server me-2"></i>系统监控</h1>
        <p class="text-muted">监控系统资源使用情况和性能指标</p>
    </div>
    <div class="col-md-4 text-md-end">
        <button class="btn btn-primary" id="refreshBtn">
            <i class="fas fa-sync-alt me-2"></i>刷新数据
        </button>
    </div>
</div>

<div class="row">
    <!-- 系统资源卡片 -->
    <div class="col-md-6 col-xl-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-microchip me-2"></i>CPU使用率</h3>
            </div>
            <div class="card-body text-center">
                <div class="resource-gauge" id="cpuGauge">
                    <div class="gauge-value">
                        <span id="cpuValue">0</span><span>%</span>
                    </div>
                    <div class="progress mt-2">
                        <div id="cpuBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-xl-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-memory me-2"></i>内存使用率</h3>
            </div>
            <div class="card-body text-center">
                <div class="resource-gauge" id="memoryGauge">
                    <div class="gauge-value">
                        <span id="memoryValue">0</span><span>%</span>
                    </div>
                    <div class="progress mt-2">
                        <div id="memoryBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="mt-2 text-muted" id="memoryDetails">
                    已用: 0 MB / 总计: 0 MB
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-xl-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-hdd me-2"></i>磁盘使用率</h3>
            </div>
            <div class="card-body text-center">
                <div class="resource-gauge" id="diskGauge">
                    <div class="gauge-value">
                        <span id="diskValue">0</span><span>%</span>
                    </div>
                    <div class="progress mt-2">
                        <div id="diskBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="mt-2 text-muted" id="diskDetails">
                    已用: 0 GB / 总计: 0 GB
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-xl-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-network-wired me-2"></i>网络流量</h3>
            </div>
            <div class="card-body text-center">
                <div class="resource-gauge" id="networkGauge">
                    <div class="gauge-value">
                        <span id="networkValue">0</span><span> KB/s</span>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="d-flex justify-content-between">
                        <span><i class="fas fa-arrow-down text-success"></i> 下载</span>
                        <span id="downloadSpeed">0 KB/s</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span><i class="fas fa-arrow-up text-danger"></i> 上传</span>
                        <span id="uploadSpeed">0 KB/s</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- API调用统计卡片 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title"><i class="fas fa-exchange-alt me-2"></i>API调用统计</h3>
                <span class="badge bg-primary" id="apiCallsTotal">总计: 0</span>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span>今日API调用</span>
                        <span id="apiCallsToday">0 / 100</span>
                    </div>
                    <div class="progress">
                        <div id="apiCallsTodayBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span>本小时API调用</span>
                        <span id="apiCallsHour">0 / 20</span>
                    </div>
                    <div class="progress">
                        <div id="apiCallsHourBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>维度</th>
                                <th>调用次数</th>
                                <th>平均处理时间</th>
                            </tr>
                        </thead>
                        <tbody id="apiCallsByDimension">
                            <tr>
                                <td colspan="3" class="text-center">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统日志卡片 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title"><i class="fas fa-list-alt me-2"></i>系统日志</h3>
                <div>
                    <select class="form-select form-select-sm" id="logLevelFilter">
                        <option value="all">所有级别</option>
                        <option value="error">错误</option>
                        <option value="warning">警告</option>
                        <option value="info">信息</option>
                        <option value="debug">调试</option>
                    </select>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="log-container" id="logContainer">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">加载日志...</p>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-primary" id="loadMoreLogsBtn">
                    <i class="fas fa-plus me-1"></i>加载更多
                </button>
                <button class="btn btn-sm btn-outline-secondary" id="clearLogsBtn">
                    <i class="fas fa-eraser me-1"></i>清空日志
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 数据库状态卡片 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-database me-2"></i>数据库状态</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h5>连接池</h5>
                            <div class="d-flex justify-content-between mb-2">
                                <span>活跃连接</span>
                                <span id="dbActiveConnections">0 / 0</span>
                            </div>
                            <div class="progress mb-3">
                                <div id="dbConnectionsBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h5>数据库大小</h5>
                            <div class="d-flex justify-content-between mb-2">
                                <span>当前大小</span>
                                <span id="dbSize">0 MB</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5>表统计</h5>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>表名</th>
                                <th>记录数</th>
                                <th>大小</th>
                            </tr>
                        </thead>
                        <tbody id="dbTableStats">
                            <tr>
                                <td colspan="3" class="text-center">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息卡片 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-info-circle me-2"></i>系统信息</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-group mb-3">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                操作系统
                                <span id="osInfo">加载中...</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Python版本
                                <span id="pythonVersion">加载中...</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Flask版本
                                <span id="flaskVersion">加载中...</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                SQLAlchemy版本
                                <span id="sqlalchemyVersion">加载中...</span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-group mb-3">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                系统启动时间
                                <span id="systemStartTime">加载中...</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                运行时长
                                <span id="systemUptime">加载中...</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                线程数
                                <span id="threadCount">加载中...</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                进程ID
                                <span id="processId">加载中...</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <h5 class="mt-3">环境变量</h5>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>变量名</th>
                                <th>值</th>
                            </tr>
                        </thead>
                        <tbody id="environmentVars">
                            <tr>
                                <td colspan="2" class="text-center">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始加载数据
        loadSystemData();
        loadApiCallStats();
        loadSystemLogs();
        loadDatabaseStats();
        loadSystemInfo();
        
        // 刷新按钮点击事件
        document.getElementById('refreshBtn').addEventListener('click', function() {
            loadSystemData();
            loadApiCallStats();
            loadSystemLogs();
            loadDatabaseStats();
            loadSystemInfo();
        });
        
        // 日志级别过滤器
        document.getElementById('logLevelFilter').addEventListener('change', function() {
            loadSystemLogs();
        });
        
        // 加载更多日志按钮
        document.getElementById('loadMoreLogsBtn').addEventListener('click', function() {
            loadMoreLogs();
        });
        
        // 清空日志按钮
        document.getElementById('clearLogsBtn').addEventListener('click', function() {
            if (confirm('确定要清空日志显示吗？这不会删除实际的日志文件。')) {
                document.getElementById('logContainer').innerHTML = '<div class="text-center py-3">日志已清空</div>';
            }
        });
        
        // 设置定时刷新
        setInterval(function() {
            loadSystemData();
        }, 10000); // 每10秒刷新一次系统数据
        
        setInterval(function() {
            loadApiCallStats();
        }, 30000); // 每30秒刷新一次API调用统计
    });
    
    // 加载系统数据
    function loadSystemData() {
        fetch('/api/system/resources')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新CPU使用率
                    document.getElementById('cpuValue').textContent = data.cpu.percent;
                    document.getElementById('cpuBar').style.width = `${data.cpu.percent}%`;
                    
                    // 更新内存使用率
                    document.getElementById('memoryValue').textContent = data.memory.percent;
                    document.getElementById('memoryBar').style.width = `${data.memory.percent}%`;
                    document.getElementById('memoryDetails').textContent = 
                        `已用: ${formatBytes(data.memory.used)} / 总计: ${formatBytes(data.memory.total)}`;
                    
                    // 更新磁盘使用率
                    document.getElementById('diskValue').textContent = data.disk.percent;
                    document.getElementById('diskBar').style.width = `${data.disk.percent}%`;
                    document.getElementById('diskDetails').textContent = 
                        `已用: ${formatBytes(data.disk.used)} / 总计: ${formatBytes(data.disk.total)}`;
                    
                    // 更新网络流量
                    document.getElementById('networkValue').textContent = formatBytes(data.network.total_per_second, 0, true);
                    document.getElementById('downloadSpeed').textContent = formatBytes(data.network.bytes_recv_per_second, 0, true) + '/s';
                    document.getElementById('uploadSpeed').textContent = formatBytes(data.network.bytes_sent_per_second, 0, true) + '/s';
                }
            })
            .catch(error => {
                console.error('获取系统资源数据失败:', error);
            });
    }
    
    // 加载API调用统计
    function loadApiCallStats() {
        fetch('/api/system/api_stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新API调用总数
                    document.getElementById('apiCallsTotal').textContent = `总计: ${data.total_calls}`;
                    
                    // 更新今日API调用
                    const todayPercent = (data.today_calls / data.today_limit) * 100;
                    document.getElementById('apiCallsToday').textContent = `${data.today_calls} / ${data.today_limit}`;
                    document.getElementById('apiCallsTodayBar').style.width = `${todayPercent}%`;
                    
                    // 更新本小时API调用
                    const hourPercent = (data.hour_calls / data.hour_limit) * 100;
                    document.getElementById('apiCallsHour').textContent = `${data.hour_calls} / ${data.hour_limit}`;
                    document.getElementById('apiCallsHourBar').style.width = `${hourPercent}%`;
                    
                    // 更新按维度统计
                    const tbody = document.getElementById('apiCallsByDimension');
                    tbody.innerHTML = '';
                    
                    if (data.by_dimension.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="3" class="text-center">暂无数据</td></tr>';
                    } else {
                        data.by_dimension.forEach(item => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${getDimensionName(item.dimension)}</td>
                                <td>${item.count}</td>
                                <td>${item.avg_time.toFixed(2)}秒</td>
                            `;
                            tbody.appendChild(row);
                        });
                    }
                }
            })
            .catch(error => {
                console.error('获取API调用统计失败:', error);
            });
    }
    
    // 加载系统日志
    function loadSystemLogs() {
        const logLevel = document.getElementById('logLevelFilter').value;
        
        fetch(`/api/system/logs?level=${logLevel}&limit=50`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const logContainer = document.getElementById('logContainer');
                    logContainer.innerHTML = '';
                    
                    if (data.logs.length === 0) {
                        logContainer.innerHTML = '<div class="text-center py-3">暂无日志</div>';
                    } else {
                        const logList = document.createElement('div');
                        logList.className = 'log-list';
                        
                        data.logs.forEach(log => {
                            const logItem = document.createElement('div');
                            logItem.className = `log-item log-${log.level.toLowerCase()}`;
                            
                            logItem.innerHTML = `
                                <div class="log-time">${formatDateTime(log.timestamp)}</div>
                                <div class="log-level">${log.level}</div>
                                <div class="log-message">${log.message}</div>
                            `;
                            
                            logList.appendChild(logItem);
                        });
                        
                        logContainer.appendChild(logList);
                    }
                }
            })
            .catch(error => {
                console.error('获取系统日志失败:', error);
            });
    }
    
    // 加载更多日志
    function loadMoreLogs() {
        const logLevel = document.getElementById('logLevelFilter').value;
        const logItems = document.querySelectorAll('.log-item');
        const offset = logItems.length;
        
        fetch(`/api/system/logs?level=${logLevel}&limit=30&offset=${offset}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const logList = document.querySelector('.log-list');
                    
                    if (data.logs.length === 0) {
                        alert('没有更多日志了');
                    } else {
                        data.logs.forEach(log => {
                            const logItem = document.createElement('div');
                            logItem.className = `log-item log-${log.level.toLowerCase()}`;
                            
                            logItem.innerHTML = `
                                <div class="log-time">${formatDateTime(log.timestamp)}</div>
                                <div class="log-level">${log.level}</div>
                                <div class="log-message">${log.message}</div>
                            `;
                            
                            logList.appendChild(logItem);
                        });
                    }
                }
            })
            .catch(error => {
                console.error('获取更多系统日志失败:', error);
            });
    }
    
    // 加载数据库统计
    function loadDatabaseStats() {
        fetch('/api/system/database')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新连接池信息
                    const connectionPercent = (data.connections.active / data.connections.max) * 100;
                    document.getElementById('dbActiveConnections').textContent = 
                        `${data.connections.active} / ${data.connections.max}`;
                    document.getElementById('dbConnectionsBar').style.width = `${connectionPercent}%`;
                    
                    // 更新数据库大小
                    document.getElementById('dbSize').textContent = formatBytes(data.size);
                    
                    // 更新表统计
                    const tbody = document.getElementById('dbTableStats');
                    tbody.innerHTML = '';
                    
                    if (data.tables.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="3" class="text-center">暂无数据</td></tr>';
                    } else {
                        data.tables.forEach(table => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${table.name}</td>
                                <td>${table.rows}</td>
                                <td>${formatBytes(table.size)}</td>
                            `;
                            tbody.appendChild(row);
                        });
                    }
                }
            })
            .catch(error => {
                console.error('获取数据库统计失败:', error);
            });
    }
    
    // 加载系统信息
    function loadSystemInfo() {
        fetch('/api/system/info')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新系统信息
                    document.getElementById('osInfo').textContent = data.os;
                    document.getElementById('pythonVersion').textContent = data.python_version;
                    document.getElementById('flaskVersion').textContent = data.flask_version;
                    document.getElementById('sqlalchemyVersion').textContent = data.sqlalchemy_version;
                    
                    document.getElementById('systemStartTime').textContent = formatDateTime(data.start_time);
                    document.getElementById('systemUptime').textContent = formatUptime(data.uptime);
                    document.getElementById('threadCount').textContent = data.threads;
                    document.getElementById('processId').textContent = data.pid;
                    
                    // 更新环境变量
                    const tbody = document.getElementById('environmentVars');
                    tbody.innerHTML = '';
                    
                    if (Object.keys(data.env_vars).length === 0) {
                        tbody.innerHTML = '<tr><td colspan="2" class="text-center">暂无数据</td></tr>';
                    } else {
                        for (const [key, value] of Object.entries(data.env_vars)) {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${key}</td>
                                <td>${value}</td>
                            `;
                            tbody.appendChild(row);
                        }
                    }
                }
            })
            .catch(error => {
                console.error('获取系统信息失败:', error);
            });
    }
    
    // 格式化字节数
    function formatBytes(bytes, decimals = 2, useKB = false) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = useKB ? ['Bytes', 'KB', 'MB', 'GB', 'TB'] : ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        
        const i = useKB ? 1 : Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }
    
    // 格式化日期时间
    function formatDateTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    
    // 格式化运行时长
    function formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        let result = '';
        if (days > 0) result += `${days}天 `;
        if (hours > 0 || days > 0) result += `${hours}小时 `;
        if (minutes > 0 || hours > 0 || days > 0) result += `${minutes}分钟 `;
        result += `${secs}秒`;
        
        return result;
    }
    
    // 获取维度名称
    function getDimensionName(dimension) {
        const dimensionNames = {
            'language_style': '语言风格',
            'rhythm_pacing': '节奏与节奏',
            'structure': '结构分析',
            'sentence_variation': '句式变化',
            'paragraph_length': '段落长度',
            'perspective_shifts': '视角转换',
            'paragraph_flow': '段落流畅度',
            'novel_characteristics': '小说特点',
            'world_building': '世界构建',
            'chapter_outline': '章节大纲',
            'character_relationships': '人物关系',
            'opening_effectiveness': '开篇效果',
            'climax_pacing': '高潮节奏'
        };
        
        return dimensionNames[dimension] || dimension;
    }
</script>
{% endblock %}

{% block extra_css %}
<style>
    /* 系统监控页面特定样式 */
    .resource-gauge {
        padding: 1rem 0;
    }
    
    .gauge-value {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .log-container {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid var(--border-color);
        border-radius: 0.25rem;
    }
    
    .log-list {
        font-family: monospace;
        font-size: 0.9rem;
    }
    
    .log-item {
        padding: 0.5rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
    }
    
    .log-item:last-child {
        border-bottom: none;
    }
    
    .log-time {
        flex: 0 0 150px;
        color: var(--secondary-color);
    }
    
    .log-level {
        flex: 0 0 80px;
        font-weight: 600;
    }
    
    .log-message {
        flex: 1;
        word-break: break-word;
    }
    
    .log-error .log-level {
        color: var(--danger-color);
    }
    
    .log-warning .log-level {
        color: var(--warning-color);
    }
    
    .log-info .log-level {
        color: var(--info-color);
    }
    
    .log-debug .log-level {
        color: var(--secondary-color);
    }
</style>
{% endblock %}
