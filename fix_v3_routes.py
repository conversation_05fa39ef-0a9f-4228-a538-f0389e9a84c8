# 修复v3_routes.py文件中的语法错误
file_path = 'src/web/routes/v3_routes.py'

# 读取文件内容
with open(file_path, 'r', encoding='utf-8') as f:
    lines = f.readlines()

# 创建备份
with open(file_path + '.backup', 'w', encoding='utf-8') as f:
    f.writelines(lines)
print(f"已创建备份文件: {file_path}.backup")

# 检查第522行是否包含'except Exception as e:'
if len(lines) >= 522 and 'except Exception as e:' in lines[521]:
    print(f"发现问题行 (522): {lines[521].strip()}")
    
    # 修改缩进
    lines[521] = lines[521].replace('    except', 'except')
    print(f"修改后: {lines[521].strip()}")
else:
    print("未找到第522行的except语句")
    
    # 手动搜索
    for i, line in enumerate(lines):
        if 520 <= i <= 523 and 'except' in line:
            print(f"在行 {i+1} 找到except: {line.strip()}")
            if 'except Exception as e:' in line:
                old_line = line
                new_line = line.replace('                    except', '                except')
                lines[i] = new_line
                print(f"修改行 {i+1}:\n旧: {old_line.strip()}\n新: {new_line.strip()}")

# 写回文件
with open(file_path, 'w', encoding='utf-8') as f:
    f.writelines(lines)
print(f"已写回修改后的文件: {file_path}")

# 验证修复
print("\n尝试导入模块验证修复:")
try:
    import importlib.util
    spec = importlib.util.spec_from_file_location("v3_routes", file_path)
    if spec and spec.loader:
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print("成功导入模块，修复有效！")
    else:
        print("无法创建模块规范")
except SyntaxError as e:
    print(f"语法错误仍然存在: {e}")
except Exception as e:
    print(f"导入时出现其他错误: {e}") 