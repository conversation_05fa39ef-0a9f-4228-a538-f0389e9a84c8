"""
打包九猫小说分析系统为可执行文件
"""
import sys
import os
from cx_Freeze import setup, Executable

# 确保当前目录是项目根目录
os.chdir(os.path.dirname(os.path.abspath(__file__)))

# 依赖项
build_exe_options = {
    "packages": [
        "os", "sys", "flask", "sqlalchemy", "werkzeug", "jinja2",
        "requests", "logging", "datetime", "uuid", "json", "time",
        "concurrent.futures", "dotenv", "pandas", "nltk", "jieba",
        "matplotlib", "networkx", "tqdm", "pydantic"
    ],
    "excludes": ["tkinter", "unittest", "email", "html", "http", "xml"],
    "include_files": [
        "config.py",
        "main.py",
        ("src/web/templates", "src/web/templates"),
        ("src/web/static", "src/web/static"),
        ("uploads", "uploads"),
        ("README.md", "README.md"),
        (".env", ".env")
    ],
    "include_msvcr": True,
    "build_exe": "build/exe.win-amd64-3.x/NineCats",
}

# 可执行文件基本信息
base = None
if sys.platform == "win32":
    base = None  # 使用控制台模式，方便查看日志

setup(
    name="九猫小说分析系统",
    version="1.0.0",
    description="基于DeepSeek R1的小说文本分析系统",
    options={"build_exe": build_exe_options},
    executables=[
        Executable(
            "main.py",
            base=base,
            target_name="NineCats.exe",
            icon="src/web/static/favicon.ico"  # 如果有图标的话
        )
    ]
)
