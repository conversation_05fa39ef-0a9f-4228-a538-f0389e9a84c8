"""
测试分析过程记录功能
"""
import os
import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

# 导入必要的模块
try:
    from src.db.connection import Session
    from src.models.analysis_process import AnalysisProcess
    from src.models.novel import Novel
    from src.services.process_recording_patch import ProcessRecorder
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    sys.exit(1)

def check_environment_variables():
    """检查环境变量是否正确设置"""
    env_vars = {
        'ENABLE_DETAILED_PROCESS_RECORDING': os.environ.get('ENABLE_DETAILED_PROCESS_RECORDING', 'False'),
        'SAVE_FULL_API_INTERACTIONS': os.environ.get('SAVE_FULL_API_INTERACTIONS', 'False'),
        'SAVE_PROMPTS': os.environ.get('SAVE_PROMPTS', 'False'),
        'ENHANCED_LOG_CATEGORIES': os.environ.get('ENHANCED_LOG_CATEGORIES', 'False'),
        'RECORD_INTERMEDIATE_RESULTS': os.environ.get('RECORD_INTERMEDIATE_RESULTS', 'False'),
        'PROCESS_RECORDING_DEBUG': os.environ.get('PROCESS_RECORDING_DEBUG', 'False'),
        'PROCESS_RECORDING_VERBOSE': os.environ.get('PROCESS_RECORDING_VERBOSE', 'False'),
        'FORCE_PROCESS_RECORDING': os.environ.get('FORCE_PROCESS_RECORDING', 'False')
    }
    
    logger.info("当前环境变量设置:")
    for var, value in env_vars.items():
        logger.info(f"  {var} = {value}")
    
    # 检查是否启用了分析过程记录
    is_enabled = ProcessRecorder.is_process_recording_enabled()
    logger.info(f"分析过程记录功能是否启用: {is_enabled}")
    
    return is_enabled

def check_database_connection():
    """检查数据库连接是否正常"""
    try:
        session = Session()
        # 尝试查询一条记录
        novel = session.query(Novel).first()
        if novel:
            logger.info(f"数据库连接正常，找到小说: ID={novel.id}, 标题={novel.title}")
        else:
            logger.warning("数据库连接正常，但没有找到任何小说")
        session.close()
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

def check_existing_processes():
    """检查数据库中是否存在分析过程记录"""
    try:
        session = Session()
        # 查询分析过程记录总数
        total_count = session.query(AnalysisProcess).count()
        logger.info(f"数据库中共有 {total_count} 条分析过程记录")
        
        # 查询最近的10条记录
        recent_processes = session.query(AnalysisProcess).order_by(AnalysisProcess.id.desc()).limit(10).all()
        logger.info(f"最近的 {len(recent_processes)} 条分析过程记录:")
        for process in recent_processes:
            logger.info(f"  ID={process.id}, 小说ID={process.novel_id}, 维度={process.dimension}, 阶段={process.processing_stage}, 时间={process.created_at}")
        
        session.close()
        return total_count > 0
    except Exception as e:
        logger.error(f"查询分析过程记录失败: {e}")
        return False

def create_test_process_record():
    """创建一条测试分析过程记录"""
    try:
        session = Session()
        # 查询一个小说ID
        novel = session.query(Novel).first()
        if not novel:
            logger.error("没有找到任何小说，无法创建测试记录")
            session.close()
            return False
        
        novel_id = novel.id
        session.close()
        
        # 创建测试记录
        logger.info(f"尝试为小说ID={novel_id}创建测试分析过程记录")
        process_id = ProcessRecorder.record_process(
            novel_id=novel_id,
            dimension="test_dimension",
            block_index=0,
            total_blocks=1,
            stage="test",
            input_text="测试输入文本",
            output_text="测试输出文本",
            prompt="测试提示词",
            processing_time=100,
            tokens=50,
            metadata={"test": True, "timestamp": datetime.now().isoformat()}
        )
        
        if process_id:
            logger.info(f"成功创建测试记录，ID={process_id}")
            return True
        else:
            logger.error("创建测试记录失败")
            return False
    except Exception as e:
        logger.error(f"创建测试记录时出错: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始测试分析过程记录功能")
    
    # 检查环境变量
    env_ok = check_environment_variables()
    if not env_ok:
        logger.error("环境变量未正确设置，分析过程记录功能未启用")
        logger.info("请确保在启动脚本中设置了 ENABLE_DETAILED_PROCESS_RECORDING=True")
        return
    
    # 检查数据库连接
    db_ok = check_database_connection()
    if not db_ok:
        logger.error("数据库连接失败，无法继续测试")
        return
    
    # 检查现有记录
    has_records = check_existing_processes()
    if has_records:
        logger.info("数据库中已存在分析过程记录")
    else:
        logger.warning("数据库中没有找到任何分析过程记录")
    
    # 创建测试记录
    test_ok = create_test_process_record()
    if test_ok:
        logger.info("测试成功，分析过程记录功能正常工作")
    else:
        logger.error("测试失败，分析过程记录功能不正常")
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
