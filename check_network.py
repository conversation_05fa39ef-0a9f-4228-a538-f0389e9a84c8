"""
检查网络配置
"""
import socket
import os
import subprocess
import sys

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"检查端口时出错: {str(e)}")
        return False

def check_localhost():
    """检查localhost是否正常解析"""
    try:
        ip = socket.gethostbyname('localhost')
        print(f"localhost解析为: {ip}")
        return ip == '127.0.0.1'
    except Exception as e:
        print(f"解析localhost时出错: {str(e)}")
        return False

def ping_localhost():
    """Ping localhost"""
    try:
        if sys.platform == 'win32':
            result = subprocess.run(['ping', '-n', '1', 'localhost'], 
                                   stdout=subprocess.PIPE, 
                                   stderr=subprocess.PIPE,
                                   text=True)
        else:
            result = subprocess.run(['ping', '-c', '1', 'localhost'], 
                                   stdout=subprocess.PIPE, 
                                   stderr=subprocess.PIPE,
                                   text=True)
        
        print("Ping结果:")
        print(result.stdout)
        return "TTL=" in result.stdout or "ttl=" in result.stdout
    except Exception as e:
        print(f"Ping localhost时出错: {str(e)}")
        return False

def check_hosts_file():
    """检查hosts文件"""
    try:
        if sys.platform == 'win32':
            hosts_path = r'C:\Windows\System32\drivers\etc\hosts'
        else:
            hosts_path = '/etc/hosts'
        
        if os.path.exists(hosts_path):
            with open(hosts_path, 'r') as f:
                hosts_content = f.read()
            
            print("hosts文件内容:")
            print(hosts_content)
            
            return '127.0.0.1 localhost' in hosts_content
        else:
            print(f"找不到hosts文件: {hosts_path}")
            return False
    except Exception as e:
        print(f"检查hosts文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("=== 网络配置检查 ===")
    
    # 检查端口5001是否被占用
    port_in_use = check_port(5001)
    print(f"端口5001是否被占用: {port_in_use}")
    
    # 检查localhost是否正常解析
    localhost_ok = check_localhost()
    print(f"localhost解析是否正常: {localhost_ok}")
    
    # Ping localhost
    ping_ok = ping_localhost()
    print(f"Ping localhost是否成功: {ping_ok}")
    
    # 检查hosts文件
    hosts_ok = check_hosts_file()
    print(f"hosts文件是否正常: {hosts_ok}")
    
    print("\n=== 检查结果 ===")
    if port_in_use:
        print("端口5001已被占用，这可能是九猫系统正在运行")
    else:
        print("端口5001未被占用，九猫系统可能未启动")
    
    if localhost_ok and ping_ok and hosts_ok:
        print("网络配置正常")
    else:
        print("网络配置可能有问题，请检查hosts文件和网络设置")

if __name__ == "__main__":
    main()
    input("按回车键继续...")
