/**
 * 九猫 - Chart.js加载器
 * 确保Chart.js正确加载并处理依赖
 * 版本: 1.0.0
 */

// 立即执行函数，避免污染全局命名空间
(function() {
    console.log('Chart.js加载器已初始化');
    
    // Chart.js CDN地址列表
    const chartJsCDNs = [
        '/static/js/lib/chart.min.js',  // 首先尝试本地lib文件夹中的版本
        '/static/js/chart.min.js',  // 然后尝试js根目录的版本
        'https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.3.0/chart.umd.min.js',
        'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js'
    ];
    
    // 加载Chart.js的函数
    function loadChartJs(callback) {
        // 如果Chart.js已经加载，直接调用回调
        if (window.Chart) {
            console.log('Chart.js已加载');
            
            // 确保Chart实例有destroy方法
            ensureChartHasDestroyMethod();
            
            if (typeof callback === 'function') {
                callback(window.Chart);
            }
            return;
        }
        
        console.log('Chart.js未加载，开始尝试加载');
        
        // 记录加载尝试
        let loadAttempts = 0;
        let chartJsLoaded = false;
        
        // 从CDN列表中加载
        function tryNextCDN(index) {
            if (index >= chartJsCDNs.length) {
                console.error('所有Chart.js CDN都加载失败，尝试使用应急Chart.js替代品');
                useEmergencyChartJs(callback);
                return;
            }
            
            loadAttempts++;
            const url = chartJsCDNs[index];
            
            console.log(`尝试从 ${url} 加载Chart.js (尝试 ${loadAttempts}/${chartJsCDNs.length})`);
            
            // 创建脚本标签
            const script = document.createElement('script');
            script.src = url;
            script.async = false;
            
            // 成功加载
            script.onload = function() {
                if (!chartJsLoaded && window.Chart) {
                    chartJsLoaded = true;
                    console.log('Chart.js成功加载');
                    
                    // 确保Chart实例有destroy方法
                    ensureChartHasDestroyMethod();
                    
                    // 执行成功回调
                    if (typeof callback === 'function') {
                        callback(window.Chart);
                    }
                }
            };
            
            // 加载失败，尝试下一个CDN
            script.onerror = function() {
                console.warn(`从 ${url} 加载Chart.js失败，尝试下一个CDN`);
                
                // 延迟尝试下一个，避免连续请求
                setTimeout(function() {
                    if (!chartJsLoaded) {
                        tryNextCDN(index + 1);
                    }
                }, 200);
            };
            
            // 添加到文档
            document.head.appendChild(script);
        }
        
        // 开始尝试第一个CDN
        tryNextCDN(0);
    }
    
    // 使用应急内联Chart.js替代品
    function useEmergencyChartJs(callback) {
        console.warn('使用应急Chart.js替代品');
        
        // 如果Chart.js已加载，直接返回
        if (window.Chart) {
            if (typeof callback === 'function') {
                callback(window.Chart);
            }
            return;
        }
        
        // 简单的Chart.js替代品
        window.Chart = function(ctx, config) {
            this.ctx = ctx;
            this.config = config || {};
            this.data = config?.data || {};
            this.options = config?.options || {};
            this.type = config?.type || 'line';
            
            // 实现一些基本方法
            this.update = function() {
                console.log('Chart.update() 被调用');
                this.render();
            };
            
            this.destroy = function() {
                console.log('Chart.destroy() 被调用');
                // 清空画布
                const canvas = ctx.canvas;
                if (canvas) {
                    const context = canvas.getContext('2d');
                    if (context) {
                        context.clearRect(0, 0, canvas.width, canvas.height);
                    }
                }
            };
            
            this.render = function() {
                // 绘制简单的占位图
                const canvas = ctx.canvas;
                if (canvas) {
                    const context = canvas.getContext('2d');
                    if (context) {
                        // 清空画布
                        context.clearRect(0, 0, canvas.width, canvas.height);
                        
                        // 绘制边框
                        context.strokeStyle = '#cccccc';
                        context.lineWidth = 2;
                        context.strokeRect(2, 2, canvas.width - 4, canvas.height - 4);
                        
                        // 绘制类型和标题
                        context.fillStyle = '#666666';
                        context.font = 'bold 14px Arial';
                        context.textAlign = 'center';
                        context.textBaseline = 'middle';
                        context.fillText(`Chart.js (${this.type})`, canvas.width / 2, 20);
                        
                        if (this.config.options && this.config.options.title && this.config.options.title.text) {
                            context.fillText(this.config.options.title.text, canvas.width / 2, 40);
                        }
                        
                        // 如果有数据，绘制简单的数据可视化
                        if (this.data && this.data.datasets && this.data.datasets.length > 0) {
                            const dataset = this.data.datasets[0];
                            const data = dataset.data || [];
                            
                            if (data.length > 0) {
                                // 绘制简化的图表
                                if (this.type === 'line' || this.type === 'bar') {
                                    // 找出数据范围
                                    const min = Math.min(...data);
                                    const max = Math.max(...data);
                                    const range = max - min || 1;
                                    
                                    // 计算绘图区域
                                    const padding = 50;
                                    const graphWidth = canvas.width - padding * 2;
                                    const graphHeight = canvas.height - padding * 2;
                                    const barWidth = graphWidth / data.length * 0.8;
                                    
                                    // 绘制数据
                                    context.beginPath();
                                    context.strokeStyle = dataset.borderColor || '#3e95cd';
                                    context.fillStyle = dataset.backgroundColor || 'rgba(62, 149, 205, 0.2)';
                                    
                                    if (this.type === 'line') {
                                        // 绘制线图
                                        for (let i = 0; i < data.length; i++) {
                                            const x = padding + (graphWidth * i / (data.length - 1 || 1));
                                            const y = padding + graphHeight - (graphHeight * (data[i] - min) / range);
                                            
                                            if (i === 0) {
                                                context.moveTo(x, y);
                                            } else {
                                                context.lineTo(x, y);
                                            }
                                        }
                                        context.stroke();
                                    } else if (this.type === 'bar') {
                                        // 绘制柱状图
                                        for (let i = 0; i < data.length; i++) {
                                            const x = padding + (graphWidth * i / data.length) + (graphWidth / data.length - barWidth) / 2;
                                            const valueHeight = graphHeight * (data[i] - min) / range;
                                            const y = padding + graphHeight - valueHeight;
                                            
                                            context.fillRect(x, y, barWidth, valueHeight);
                                            context.strokeRect(x, y, barWidth, valueHeight);
                                        }
                                    }
                                } else if (this.type === 'pie' || this.type === 'doughnut') {
                                    // 绘制饼图/环形图
                                    const centerX = canvas.width / 2;
                                    const centerY = canvas.height / 2;
                                    const radius = Math.min(graphWidth, graphHeight) / 2;
                                    
                                    // 计算总和
                                    const sum = data.reduce((acc, val) => acc + val, 0);
                                    
                                    // 绘制各个扇区
                                    let startAngle = 0;
                                    const colors = ['#3e95cd', '#8e5ea2', '#3cba9f', '#e8c3b9', '#c45850'];
                                    
                                    for (let i = 0; i < data.length; i++) {
                                        const endAngle = startAngle + (data[i] / sum) * Math.PI * 2;
                                        
                                        context.beginPath();
                                        context.moveTo(centerX, centerY);
                                        context.arc(centerX, centerY, radius, startAngle, endAngle);
                                        context.closePath();
                                        
                                        context.fillStyle = colors[i % colors.length];
                                        context.fill();
                                        context.stroke();
                                        
                                        startAngle = endAngle;
                                    }
                                    
                                    // 如果是环形图，挖出中心
                                    if (this.type === 'doughnut') {
                                        context.beginPath();
                                        context.arc(centerX, centerY, radius * 0.5, 0, Math.PI * 2);
                                        context.fillStyle = 'white';
                                        context.fill();
                                    }
                                }
                            } else {
                                // 无数据
                                context.fillText('无数据', canvas.width / 2, canvas.height / 2);
                            }
                        } else {
                            // 无数据
                            context.fillText('无数据', canvas.width / 2, canvas.height / 2);
                        }
                    }
                }
            };
            
            // 初始渲染
            this.render();
        };
        
        // 添加静态属性和方法
        window.Chart.defaults = {
            global: {
                responsive: true,
                maintainAspectRatio: true
            },
            font: {
                family: 'Arial, sans-serif',
                size: 12
            }
        };
        
        window.Chart.register = function() {
            console.log('Chart.register() 被调用（应急模式）');
        };
        
        // 执行回调
        if (typeof callback === 'function') {
            callback(window.Chart);
        }
        
        console.log('应急Chart.js替代品已加载');
    }
    
    // 修复现有的Chart实例
    function fixExistingCharts() {
        // 查找所有canvas元素
        const canvases = document.querySelectorAll('canvas');
        console.log(`找到 ${canvases.length} 个canvas元素，尝试修复`);
        
        canvases.forEach(function(canvas, index) {
            // 检查是否有关联的图表数据
            const chartData = canvas.dataset.chart;
            if (chartData) {
                try {
                    // 尝试解析图表数据
                    const config = JSON.parse(chartData);
                    console.log(`重新创建图表 #${index}`);
                    
                    // 创建新图表
                    new Chart(canvas.getContext('2d'), config);
                } catch (e) {
                    console.error(`解析图表 #${index} 数据时出错:`, e);
                }
            }
        });
    }
    
    // 在页面加载完成后加载Chart.js
    function initChartJs() {
        // 检查是否有jQuery，如果有等待jQuery加载完成
        if (window.jQueryLoader && !window.jQuery) {
            console.log('等待jQuery加载完成后加载Chart.js');
            document.addEventListener('jQueryReady', function() {
                loadChartJs(function() {
                    fixExistingCharts();
                });
            });
            return;
        }
        
        // 直接加载Chart.js
        loadChartJs(function() {
            fixExistingCharts();
        });
    }
    
    // 在页面加载完成后初始化Chart.js（如果尚未加载）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initChartJs);
    } else {
        initChartJs();
    }
    
    // 确保Chart.js实例有destroy方法
    function ensureChartHasDestroyMethod() {
        if (window.Chart && window.Chart.prototype) {
            // 检查原型上是否存在destroy方法
            if (typeof window.Chart.prototype.destroy !== 'function') {
                console.log('为Chart.js实例添加缺失的destroy方法');
                window.Chart.prototype.destroy = function() {
                    console.log('使用兼容的destroy方法清除图表');
                    try {
                        // 尝试使用现有的Chart.js方法
                        if (typeof Chart.getChart === 'function' && this.ctx && this.ctx.canvas) {
                            const existingChart = Chart.getChart(this.ctx.canvas);
                            if (existingChart && typeof existingChart.destroy === 'function') {
                                return existingChart.destroy();
                            }
                        }
                        
                        // 如果上述方法不可用，则手动清除画布
                        if (this.ctx && this.ctx.canvas) {
                            this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
                        }
                        return true;
                    } catch (e) {
                        console.warn('使用兼容的destroy方法时出错:', e);
                        return false;
                    }
                };
            }
        }
    }
    
    // 导出到全局命名空间
    window.ChartLoader = {
        loadChartJs: loadChartJs,
        useEmergencyChartJs: useEmergencyChartJs,
        fixExistingCharts: fixExistingCharts,
        ensureLoaded: loadChartJs
    };
    
    // 同时提供小写版本的全局变量，以确保兼容性
    window.chartLoader = window.ChartLoader;
})();
